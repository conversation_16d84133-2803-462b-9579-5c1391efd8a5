package models

// OCPXParameterStu ...
type OCPXParameterStu struct {
	// ocpx uid, 暂时无用
	OCPXUID string `json:"ocpx_uid"`

	// kuaishou adid, 快手ocpx必传
	KuaiShouAdID string `json:"kuaishou_adid"`

	// os: android或ios
	Os           string `json:"os"`
	OsVersion    string `json:"os_version,omitempty"`
	Model        string `json:"model,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	Ua           string `json:"ua,omitempty"`
	IP           string `json:"ip,omitempty"`

	// android设备参数
	Imei    string `json:"imei,omitempty"`
	ImeiMd5 string `json:"imei_md5,omitempty"`
	Oaid    string `json:"oaid,omitempty"`

	// ios设备参数
	Idfa      string                   `json:"idfa,omitempty"`
	IdfaMd5   string                   `json:"idfa_md5,omitempty"`
	CAIDMulti []OCPXParameterCAIDMulti `json:"caid_multi,omitempty"`

	// callback url
	CallbackURL string `json:"callback_url,omitempty"`
}

type OCPXParameterCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}
