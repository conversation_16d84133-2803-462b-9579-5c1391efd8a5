package dpi

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"

	"github.com/redis/go-redis/v9"
)

func GetDeviceDpi(ctx context.Context, redisClient *redis.Client, manufacturer string, model string, os string, osv string) float64 {
	manufacturer = strings.Replace(manufacturer, " ", "", -1)
	manufacturer = strings.ToLower(manufacturer)

	model = strings.Replace(model, " ", "", -1)
	model = strings.ToLower(model)

	os = strings.Replace(os, " ", "", -1)
	os = strings.ToLower(os)

	osv = strings.Replace(osv, " ", "", -1)
	osv = strings.ToLower(osv)

	redisKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DPI_KEY, manufacturer, model, os, osv)
	redisValue, _ := redisClient.Get(ctx, redisKey).Result()
	if len(redisValue) > 0 {
		dpiFloat, _ := strconv.ParseFloat(redisValue, 64)
		if dpiFloat > 0 {
			return dpiFloat
		}
	}

	redisKey = fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DPI_ABNORMAL_KEY, os, osv)
	redisValue, _ = redisClient.Get(ctx, redisKey).Result()
	if len(redisValue) > 0 {
		osAndOsvDpiFloat, _ := strconv.ParseFloat(redisValue, 64)
		if osAndOsvDpiFloat > 0 {
			return osAndOsvDpiFloat
		}
	}

	redisKey = fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DPI_MINIMUM_GUARANTEE_KEY, os)
	redisValue, _ = redisClient.Get(ctx, redisKey).Result()
	if len(redisValue) > 0 {
		osDpiFloat, _ := strconv.ParseFloat(redisValue, 64)
		if osDpiFloat > 0 {
			return osDpiFloat
		}
	}

	return 480
}
