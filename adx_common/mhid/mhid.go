// Package mhid 提供了 MapleHaze ID (MHID) 的管理功能，包括存储、转换和上下文操作
// MHID 是一个用于标识设备的唯一标识符，使用 uint32 类型存储
package mhid

import (
	"context"
	"fmt"
	"strconv"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"github.com/redis/go-redis/v9"
)

// MhidData 存储设备标识信息
// DidMd5 是设备 ID 的 MD5 哈希值
// Mhid 是设备的唯一标识符，使用 uint32 类型存储
type MhidData struct {
	DidMd5 string `json:"did_md5"`
	Mhid   uint32 `json:"mhid"`
}

func (d MhidData) GetKey() string {
	return d.DidMd5
}

// mhidKey 用于在 context 中存储 MHID 数据的键
type mhidKey struct{}

// MhidKey 是用于在 context 中存储和获取 MHID 数据的键值
var MhidKey = &mhidKey{}

// MhidStore 定义了 MHID 存储接口
type MhidStore interface {
	// GetMhid 根据设备 ID 的 MD5 值获取对应的 MHID 数据
	GetMhid(ctx context.Context, didMd5 string) (mhid *MhidData, err error)
}

// MhidRedisStore 实现了基于 Redis 的 MHID 存储
type MhidRedisStore struct {
	client *redis.Client
}

// GetMhid 从 Redis 中获取指定设备 ID 对应的 MHID 数据
func (s *MhidRedisStore) GetMhid(ctx context.Context, didMd5 string) (mhid *MhidData, err error) {
	if result, err := s.client.Get(ctx, fmt.Sprintf(rediskeys.ADX_MHID_KEY, didMd5)).Result(); err == nil {
		if resultInt, err := strconv.ParseUint(result, 10, 32); err == nil {
			return &MhidData{
				DidMd5: didMd5,
				Mhid:   uint32(resultInt),
			}, nil
		} else {
			return nil, err
		}
	} else {
		return nil, err
	}
}

// NewMhidRedisStore 创建一个新的 Redis MHID 存储实例
func NewMhidRedisStore(client *redis.Client) *MhidRedisStore {
	return &MhidRedisStore{client: client}
}

// ContextWithMhid 将 MHID 数据添加到 context 中
// 如果 context 中已存在 MHID 数据，则返回错误
func ContextWithMhid(
	ctx context.Context,
	mhidStroe MhidStore,
	didMd5 string,
) (context.Context, error) {
	if existingMhid, ok := GetMhidFromContext(ctx); ok {
		return ctx, fmt.Errorf("mhid already exists: %d, no need to fetch again", existingMhid.Mhid)
	}
	if mhid, err := mhidStroe.GetMhid(ctx, didMd5); err == nil {
		return context.WithValue(ctx, MhidKey, *mhid), nil
	} else {
		return nil, err
	}
}

// GetMhidFromContext 从 context 中获取 MHID 数据
// 如果数据不存在或类型不匹配，ok 将为 false
func GetMhidFromContext(ctx context.Context) (mhid MhidData, ok bool) {
	mhid, ok = ctx.Value(MhidKey).(MhidData)
	return
}
