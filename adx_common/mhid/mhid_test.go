package mhid

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRedisClient 是 redis.Client 的模拟实现
type MockRedisClient struct {
	mock.Mock
}

// MhidRedisStore 实现了基于 Redis 的 MHID 存储
type MhidMockStore struct {
	client *MockRedisClient
}

// GetMhid 从 Redis 中获取指定设备 ID 对应的 MHID 数据
func (s *MhidMockStore) GetMhid(ctx context.Context, didMd5 string) (mhid *MhidData, err error) {
	if result, err := s.client.Get(ctx, fmt.Sprintf(rediskeys.ADX_MHID_KEY, didMd5)).Result(); err == nil {
		if resultInt, err := strconv.ParseUint(result, 10, 32); err == nil {
			return &MhidData{
				DidMd5: didMd5,
				Mhid:   uint32(resultInt),
			}, nil
		} else {
			return nil, err
		}
	} else {
		return nil, err
	}
}

// Get 模拟 redis.Client 的 Get 方法
func (m *MockRedisClient) Get(ctx context.Context, key string) *redis.StringCmd {
	args := m.Called(ctx, key)
	return args.Get(0).(*redis.StringCmd)
}

// TestMhidRedisStore_GetMhid 测试 MhidRedisStore 的 GetMhid 方法
func TestMhidRedisStore_GetMhid(t *testing.T) {
	tests := []struct {
		name         string
		didMd5       string
		redisValue   string
		redisErr     error
		expectedErr  bool
		expectedMhid uint32
	}{
		{"正常情况", "test123", "3000000000", nil, false, 3000000000},
		{"Redis错误", "test456", "", redis.ErrClosed, true, 0},
		{"无效MHID值", "test789", "invalid", nil, true, 0},
		{"MHID值为0", "test000", "0", nil, false, 0},
		{"MHID最大值", "testmax", "4294967295", nil, false, 4294967295},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 mock Redis 客户端
			mockRedis := new(MockRedisClient)
			store := &MhidMockStore{client: mockRedis}
			ctx := context.Background()

			// 设置预期行为
			mockRedis.On("Get", ctx, mock.Anything).Return(redis.NewStringResult(tt.redisValue, tt.redisErr))

			// 执行测试
			mhid, err := store.GetMhid(ctx, tt.didMd5)

			// 验证结果
			if tt.expectedErr {
				assert.Error(t, err)
				assert.Nil(t, mhid)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, mhid)
				assert.Equal(t, tt.didMd5, mhid.DidMd5)
				assert.Equal(t, tt.expectedMhid, mhid.Mhid)
			}

			// 验证 mock 是否按预期被调用
			mockRedis.AssertExpectations(t)
		})
	}
}

// TestContextWithMhid 测试 ContextWithMhid 函数
func TestContextWithMhid(t *testing.T) {
	tests := []struct {
		name        string
		didMd5      string
		redisValue  string
		redisErr    error
		expectedErr bool
		hasExisting bool
	}{
		{"正常情况", "test123", "1000", nil, false, false},
		{"Redis错误", "test456", "", redis.ErrClosed, true, false},
		{"已存在MHID", "test789", "2000", nil, true, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 mock Redis 客户端
			mockRedis := new(MockRedisClient)
			store := &MhidMockStore{client: mockRedis}
			ctx := context.Background()

			// 如果需要测试已存在MHID的情况
			if tt.hasExisting {
				ctx = context.WithValue(ctx, MhidKey, MhidData{DidMd5: tt.didMd5, Mhid: 1000})
			}

			// 设置预期行为
			if !tt.hasExisting {
				mockRedis.On("Get", ctx, mock.Anything).Return(redis.NewStringResult(tt.redisValue, tt.redisErr))
			}

			// 执行测试
			newCtx, err := ContextWithMhid(ctx, store, tt.didMd5)
			// 验证结果
			if tt.expectedErr {
				assert.Error(t, err)
				if tt.hasExisting {
					assert.Contains(t, err.Error(), "mhid already exists")
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, newCtx)

				// 验证context中的MHID数据
				mhid, ok := GetMhidFromContext(newCtx)
				assert.True(t, ok)
				assert.Equal(t, tt.didMd5, mhid.DidMd5)
				expectedMhid, _ := strconv.ParseUint(tt.redisValue, 10, 32)
				assert.Equal(t, uint32(expectedMhid), mhid.Mhid)
			}

			// 验证 mock 是否按预期被调用
			mockRedis.AssertExpectations(t)
		})
	}
}

// TestGetMhidFromContext 测试 GetMhidFromContext 函数
func TestGetMhidFromContext(t *testing.T) {
	tests := []struct {
		name         string
		setupCtx     func() context.Context
		expectedOk   bool
		expectedMhid MhidData
	}{
		{
			name: "正常情况",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), MhidKey,
					MhidData{DidMd5: "test123", Mhid: 3000000000})
			},
			expectedOk:   true,
			expectedMhid: MhidData{DidMd5: "test123", Mhid: 3000000000},
		},
		{
			name:         "空Context",
			setupCtx:     context.Background,
			expectedOk:   false,
			expectedMhid: MhidData{},
		},
		{
			name: "无效类型",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), MhidKey, "invalid")
			},
			expectedOk:   false,
			expectedMhid: MhidData{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupCtx()
			mhid, ok := GetMhidFromContext(ctx)

			assert.Equal(t, tt.expectedOk, ok)
			assert.Equal(t, tt.expectedMhid, mhid)
		})
	}
}

func TestContextWithMhidAndGetMhidFromContext(t *testing.T) {
	ctx := context.Background()

	ctx = context.WithValue(ctx, MhidKey, MhidData{DidMd5: "test123", Mhid: 3000000000})

	mhid, ok := GetMhidFromContext(ctx)
	log.Println(mhid, ok)
}
