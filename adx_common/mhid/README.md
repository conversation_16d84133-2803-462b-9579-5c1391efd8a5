# MHID 模块

## 功能介绍

MHID（MapleHaze ID）是一个用于广告系统中设备标识的模块，它提供了一种统一的方式来管理和追踪设备标识。主要功能包括：

-   基于设备 ID（Device ID）的 MD5 值生成唯一的 MHID
-   提供设备标识的存储和检索功能
-   支持上下文（Context）中的 MHID 管理

## 核心接口

### MhidData 结构体

```go
type MhidData struct {
    DidMd5 string  // 设备 ID 的 MD5 值
    Mhid   uint32  // 设备对应的 MHID 值
}
```

### 主要方法

#### ContextWithMhid

```go
func ContextWithMhid(ctx context.Context, store MhidStore, didMd5 string) (context.Context, error)
```

将 MHID 信息添加到上下文中。如果上下文中已存在 MHID，则返回错误。

#### GetMhidFromContext

```go
func GetMhidFromContext(ctx context.Context) (MhidData, bool)
```

从上下文中获取 MHID 信息。如果上下文中不存在 MHID 或类型不正确，则返回 false。

### MhidStore 接口

```go
type MhidStore interface {
    GetMhid(ctx context.Context, didMd5 string) (mhid *MhidData, err error)
}
```

## 使用示例

### 基本用法

```go
package main

import (
    "context"
    "log"

    "your/path/to/mhid"
)

func main() {
    // 创建 Redis 存储实例
    store := mhid.NewMhidRedisStore(redisClient)

    // 创建上下文
    ctx := context.Background()

    // 设备 ID 的 MD5 值
    didMd5 := "abc123def456"

    // 将 MHID 添加到上下文
    newCtx, err := mhid.ContextWithMhid(ctx, store, didMd5)
    if err != nil {
        log.Fatal(err)
    }

    // 从上下文中获取 MHID
    if mhidData, ok := mhid.GetMhidFromContext(newCtx); ok {
        log.Printf("Device MHID: %d", mhidData.Mhid)
    }
}
```

## 业务流程图

### MHID 生成和使用流程

```mermaid
sequenceDiagram
    participant Client
    participant MhidModule
    participant Redis

    Client->>MhidModule: 请求 MHID (didMd5)
    MhidModule->>Redis: 查询缓存
    alt 缓存命中
        Redis-->>MhidModule: 返回 MHID
    else 缓存未命中
        MhidModule->>Redis: 生成并存储新的 MHID
        Redis-->>MhidModule: 确认存储
    end
    MhidModule->>Client: 返回 MHID 数据
```

## 最佳实践

1. **错误处理**

    - 始终检查从上下文获取 MHID 的返回值
    - 处理 Redis 存储可能出现的错误

2. **性能优化**

    - 使用 Redis 缓存减少 MHID 查询开销

3. **并发处理**
    - 使用 context.Context 确保请求级别的数据隔离
    - 注意 Redis 操作的原子性

## 注意事项

1. MHID 是 uint32 类型，取值范围为 0 ~ 4294967295
2. 上下文中已存在 MHID 时，ContextWithMhid 会返回错误
3. Redis 存储实现应确保高可用性和数据一致性
