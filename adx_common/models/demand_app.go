package models

type DemandAppModel struct {
	// 上游媒体id
	DemandAppID string `json:"demand_app_id"`
	// 上游媒体name
	DemandAppName string `json:"demand_app_name"`
	// 上游平台id
	PlatformID string `json:"platform_id"`
	// 上游媒体接入类型, 目前 0(api), 1(sdk)
	IntegrationType int `json:"integration_type"`
	// 上游主体id
	CorpID string `json:"corp_id"`
	// 原始上游媒体id
	RawDemandAppID string `json:"raw_demand_app_id"`
	// os, 0: android; 1: ios
	Os int `json:"os"`
	// 是否enable
	IsEnable int `json:"is_enable"`
}
