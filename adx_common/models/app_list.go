package models

import (
	"log"
	"sort"
)

type AppListStu struct {
	AppName            string
	AndroidPackageName string
	IosPackageName     string
}

type BaiduAppListStu struct {
	AndroidAppId int
	IosAppId     int
}

// 获得AppList对象映射
func GetAppListMapGroup1() map[int]AppListStu {
	return appListMapGroup1
}

func GetAppIdByPackageName(packageName string) (int, bool) {
	val, ok := mhAppListMap[packageName]
	return val, ok
}

func GetMHAppListMap() map[string]int {
	return mhAppListMap
}

func GetBaiduAppIdByAppIdByOs(appId []int, os string) []uint32 {
	var baiduAppIds []uint32
	for _, id := range appId {
		if val, ok := baiduAppListMap[id]; ok {
			if os == "android" {
				if val.AndroidAppId > 0 {
					baiduAppIds = append(baiduAppIds, uint32(val.AndroidAppId))
				}
			} else {
				if val.IosAppId > 0 {
					baiduAppIds = append(baiduAppIds, uint32(val.IosAppId))
				}
			}
		}
	}

	return baiduAppIds
}

func GetTanxPackageNameByAppIdByOs(appId []int, os string) []string {
	var tanxPackageName []string
	for _, id := range appId {
		if val, ok := appListMapGroup1[id]; ok {
			if os == "android" {
				tanxPackageName = append(tanxPackageName, val.AndroidPackageName)
			} else {
				tanxPackageName = append(tanxPackageName, val.IosPackageName)
			}
		}
	}

	return tanxPackageName
}

// 获得AppId列表
func GetAppIdListArrayGroup1() []int {
	keys := make([]int, 0, len(appListMapGroup1))
	for k := range appListMapGroup1 {
		keys = append(keys, k)
	}
	sort.Ints(keys)

	return keys
}

// 通过AppId数组返回映射值
func GetAppListMappingValueGroup1(keys []int) (value int) {
	appIdKeys := GetAppIdListArrayGroup1()
	for _, key := range keys {
		index := 0
		for _, indexKey := range appIdKeys {
			if key == indexKey {
				log.Printf("indexKey:%v, value:%v, index:%v, 1<<index:%v\n", indexKey, value, index, 1<<index)
				value = value ^ (1 << index)
			}
			index = index + 1
		}
	}
	return
}

func IsKeysInMappingValue(keys int, mappingValue int) bool {
	return mappingValue&keys == keys
}

// !弃用
var appListMapGroup1 = map[int]AppListStu{
	1001: {AppName: "淘宝", AndroidPackageName: "com.taobao.taobao", IosPackageName: "com.taobao.taobao4iphone"},
	1002: {AppName: "京东", AndroidPackageName: "com.jingdong.app.mall", IosPackageName: "com.360buy.jdmobile"},
	1003: {AppName: "抖音", AndroidPackageName: "com.ss.android.ugc.aweme", IosPackageName: "com.ss.iphone.ugc.Aweme"},
	1004: {AppName: "快手", AndroidPackageName: "com.smile.gifmaker", IosPackageName: "com.jiangjia.gif"},
	1005: {AppName: "拼多多", AndroidPackageName: "com.xunmeng.pinduoduo", IosPackageName: "com.xunmeng.pinduoduo"},
	1006: {AppName: "美团", AndroidPackageName: "com.sankuai.meituan", IosPackageName: "com.meituan.imeituan"},
	1007: {AppName: "12306", AndroidPackageName: "com.MobileTicket", IosPackageName: "cn.12306.rails12306"},
	1008: {AppName: "支付宝", AndroidPackageName: "com.eg.android.AlipayGphone", IosPackageName: "com.alipay.iphoneclient"},
	1009: {AppName: "快手极速版", AndroidPackageName: "com.kuaishou.nebula", IosPackageName: "com.kuaishou.nebula"},
	1010: {AppName: "抖音极速版", AndroidPackageName: "com.ss.android.ugc.aweme.lite", IosPackageName: "com.ss.iphone.ugc.aweme.lite"},
	1011: {AppName: "携程", AndroidPackageName: "ctrip.android.view", IosPackageName: "ctrip.com"},
	1012: {AppName: "去哪儿", AndroidPackageName: "com.Qunar", IosPackageName: "com.qunar.iphoneclient8"},
	1013: {AppName: "高德", AndroidPackageName: "com.autonavi.amap", IosPackageName: "com.autonavi.minimap"},
	1014: {AppName: "滴滴", AndroidPackageName: "com.sdu.didi.psnger", IosPackageName: "com.xiaojukeji.didi"},
	1015: {AppName: "天猫", AndroidPackageName: "com.tmall.wireless", IosPackageName: "com.taobao.tmall"},
	1016: {AppName: "UC浏览器", AndroidPackageName: "com.UCMobile", IosPackageName: "com.ucweb.iphone.lowversion"},
	1017: {AppName: "唯品会", AndroidPackageName: "com.achievo.vipshop", IosPackageName: "com.vipshop.iphone"},
	1018: {AppName: "小红书", AndroidPackageName: "com.xingin.xhs", IosPackageName: "com.xingin.discover"},
	1019: {AppName: "饿了么", AndroidPackageName: "me.ele", IosPackageName: "me.ele.ios.eleme"},
	1020: {AppName: "美团外卖", AndroidPackageName: "com.sankuai.meituan.takeoutnew", IosPackageName: "com.meituan.itakeaway"},
	1021: {AppName: "微信", AndroidPackageName: "com.tencent.mm", IosPackageName: "com.tencent.xin"},
	1022: {AppName: "QQ", AndroidPackageName: "com.tencent.mobileqq", IosPackageName: "com.tencent.mqq"},
	1023: {AppName: "微博", AndroidPackageName: "com.sina.weibo", IosPackageName: "com.sina.weibo"},
	1024: {AppName: "钉钉", AndroidPackageName: "com.alibaba.android.rimet", IosPackageName: "com.laiwang.DingTalk"},
	1025: {AppName: "百度", AndroidPackageName: "com.baidu.searchbox", IosPackageName: "com.baidu.BaiduMobile"},
	1026: {AppName: "汽车之家", AndroidPackageName: "com.cubic.autohome", IosPackageName: "com.autohome"},
	1027: {AppName: "懂车帝", AndroidPackageName: "com.ss.android.auto", IosPackageName: "com.ss.ios.auto"},
	1028: {AppName: "企业微信", AndroidPackageName: "com.tencent.wework", IosPackageName: "com.tencent.ww"},
	1029: {AppName: "斗鱼", AndroidPackageName: "air.tv.douyu.android", IosPackageName: "tv.douyu.live"},
	1030: {AppName: "虎牙", AndroidPackageName: "com.duowan.kiwi", IosPackageName: "com.yy.kiwi"},
	1031: {AppName: "闲鱼", AndroidPackageName: "com.taobao.idlefish", IosPackageName: "com.taobao.fleamarket"},
	1032: {AppName: "口碑", AndroidPackageName: "com.taobao.mobile.dipei", IosPackageName: "com.taobao.kbmeishi"},
	1033: {AppName: "飞猪", AndroidPackageName: "com.taobao.trip", IosPackageName: "com.taobao.travel"},
	1034: {AppName: "零售通", AndroidPackageName: "com.alibaba.wireless.lstretailer", IosPackageName: "com.alibaba.retailTrader"},
	1035: {AppName: "优酷", AndroidPackageName: "com.youku.phone", IosPackageName: "com.youku.YouKu"},
	1036: {AppName: "网易考拉", AndroidPackageName: "com.kaola", IosPackageName: "com.netease.kaola"},
	1037: {AppName: "淘特", AndroidPackageName: "com.taobao.litetao", IosPackageName: "com.taobao.special"},
	1038: {AppName: "点淘", AndroidPackageName: "com.taobao.live", IosPackageName: "com.taobao.live"},
	1039: {AppName: "1688", AndroidPackageName: "com.alibaba.wireless", IosPackageName: "com.alibaba.wireless"},
	1040: {AppName: "夸克浏览器", AndroidPackageName: "com.quark.browser", IosPackageName: "com.quark.browser"},
	1041: {AppName: "盒马", AndroidPackageName: "com.wudaokou.hippo", IosPackageName: "com.wdk.hmxs"},
	1042: {AppName: "菜鸟裹裹", AndroidPackageName: "com.cainiao.wireless", IosPackageName: "com.cainiao.cnwireless"},
	1043: {AppName: "爱奇艺", AndroidPackageName: "com.qiyi.video", IosPackageName: "com.qiyi.iphone"},
	1044: {AppName: "天眼查", AndroidPackageName: "com.tianyancha.skyeye", IosPackageName: "com.jindidata.SkyEyes"},
	1045: {AppName: "贝壳", AndroidPackageName: "com.lianjia.beike", IosPackageName: "com.lianjia.beike"},
	1046: {AppName: "知乎", AndroidPackageName: "com.zhihu.android", IosPackageName: "com.zhihu.ios"},
	1047: {AppName: "苏宁易购", AndroidPackageName: "com.suning.mobile.ebuy", IosPackageName: "SuningEMall"},
	1048: {AppName: "DNF助手", AndroidPackageName: "com.tencent.gamehelper.dnf", IosPackageName: ""},
	1049: {AppName: "中国移动", AndroidPackageName: "com.greenpoint.android.mc10086.activity", IosPackageName: "cn.10086.app"},
	1050: {AppName: "得物", AndroidPackageName: "com.shizhuang.duapp", IosPackageName: ""},
	1051: {AppName: "酷狗", AndroidPackageName: "com.kugou.android", IosPackageName: "com.kugou.kugou1002"},
	1052: {AppName: "兼职猫", AndroidPackageName: "com.huazhu.xiaojianzhi", IosPackageName: ""},
	1053: {AppName: "青团社兼职", AndroidPackageName: "com.qts.customer", IosPackageName: ""},
	1054: {AppName: "兼职兼客", AndroidPackageName: "com.wodan.xianshijian", IosPackageName: ""},
	1055: {AppName: "人人兼职", AndroidPackageName: "com.rrhired.customer", IosPackageName: ""},
	1056: {AppName: "淘宝联盟", AndroidPackageName: "com.alimama.moon", IosPackageName: ""},
	1057: {AppName: "鱼泡网", AndroidPackageName: "io.dcloud.H576E6CC7", IosPackageName: ""},
	1058: {AppName: "58同城", AndroidPackageName: "com.wuba", IosPackageName: ""},
	1059: {AppName: "返利网", AndroidPackageName: "com.fanli.android.apps", IosPackageName: ""},
	1060: {AppName: "省钱快报", AndroidPackageName: "com.jzyd.coupon", IosPackageName: ""},
	1063: {AppName: "七猫", AndroidPackageName: "com.kmxs.reader", IosPackageName: "com.yueyou.cyreader"},
	1064: {AppName: "YY语音", AndroidPackageName: "com.yy.yyvoicetool", IosPackageName: "com.yy.yyvoicetool"},
	1065: {AppName: "喜马拉雅", AndroidPackageName: "com.ximalaya.ting.android", IosPackageName: "com.gemd.iting"},
	1066: {AppName: "哔哩哔哩", AndroidPackageName: "tv.danmaku.bili", IosPackageName: "tv.danmaku.bilianime"},
	1067: {AppName: "陌陌", AndroidPackageName: "com.immomo.momo", IosPackageName: "com.wemomo.momoappdemo1"},
	//! 最多64个 也就是1064 超过请另建一个Group
}

var baiduAppListMap = map[int]BaiduAppListStu{
	1001: {AndroidAppId: 100003, IosAppId: 200003},
	1002: {AndroidAppId: 100001, IosAppId: 200001},
	1003: {AndroidAppId: 100048, IosAppId: 200048},
	1004: {AndroidAppId: 100018, IosAppId: 200018},
	1005: {AndroidAppId: 100010, IosAppId: 200010},
	1006: {AndroidAppId: 100007, IosAppId: 200007},
	1008: {AndroidAppId: 100059, IosAppId: 200059},
	1009: {AndroidAppId: 100058, IosAppId: 200058},
	1010: {AndroidAppId: 100065, IosAppId: 200065},
	1011: {AndroidAppId: 100005, IosAppId: 200005},
	1012: {AndroidAppId: 100006, IosAppId: 200006},
	1017: {AndroidAppId: 100002, IosAppId: 200002},
	1046: {AndroidAppId: 100035, IosAppId: 200017},
	1050: {AndroidAppId: 100135, IosAppId: 200065},
	1051: {AndroidAppId: 100123},
}

var mhAppListMap = map[string]int{
	"com.taobao.taobao":                       1001,
	"com.jingdong.app.mall":                   1002,
	"com.ss.android.ugc.aweme":                1003,
	"com.smile.gifmaker":                      1004,
	"com.xunmeng.pinduoduo":                   1005,
	"com.sankuai.meituan":                     1006,
	"com.MobileTicket":                        1007,
	"com.eg.android.AlipayGphone":             1008,
	"com.ss.android.ugc.aweme.lite":           1010,
	"ctrip.android.view":                      1011,
	"com.Qunar":                               1012,
	"com.autonavi.amap":                       1013,
	"com.sdu.didi.psnger":                     1014,
	"com.tmall.wireless":                      1015,
	"com.UCMobile":                            1016,
	"com.achievo.vipshop":                     1017,
	"com.xingin.xhs":                          1018,
	"me.ele":                                  1019,
	"com.sankuai.meituan.takeoutnew":          1020,
	"com.tencent.mm":                          1021,
	"com.tencent.mobileqq":                    1022,
	"com.alibaba.android.rimet":               1024,
	"com.baidu.searchbox":                     1025,
	"com.cubic.autohome":                      1026,
	"com.ss.android.auto":                     1027,
	"com.tencent.wework":                      1028,
	"air.tv.douyu.android":                    1029,
	"com.duowan.kiwi":                         1030,
	"com.taobao.idlefish":                     1031,
	"com.taobao.mobile.dipei":                 1032,
	"com.taobao.trip":                         1033,
	"com.alibaba.wireless.lstretailer":        1034,
	"com.youku.phone":                         1035,
	"com.kaola":                               1036,
	"com.taobao.litetao":                      1037,
	"com.wudaokou.hippo":                      1041,
	"com.cainiao.wireless":                    1042,
	"com.qiyi.video":                          1043,
	"com.tianyancha.skyeye":                   1044,
	"com.zhihu.android":                       1046,
	"com.suning.mobile.ebuy":                  1047,
	"com.tencent.gamehelper.dnf":              1048,
	"com.greenpoint.android.mc10086.activity": 1049,
	"com.kugou.android":                       1051,
	"com.taobao.taobao4iphone":                1001,
	"com.360buy.jdmobile":                     1002,
	"com.ss.iphone.ugc.Aweme":                 1003,
	"com.jiangjia.gif":                        1004,
	"com.meituan.imeituan":                    1006,
	"cn.12306.rails12306":                     1007,
	"com.alipay.iphoneclient":                 1008,
	"com.kuaishou.nebula":                     1009,
	"com.ss.iphone.ugc.aweme.lite":            1010,
	"ctrip.com":                               1011,
	"com.qunar.iphoneclient8":                 1012,
	"com.autonavi.minimap":                    1013,
	"com.xiaojukeji.didi":                     1014,
	"com.taobao.tmall":                        1015,
	"com.ucweb.iphone.lowversion":             1016,
	"com.vipshop.iphone":                      1017,
	"com.xingin.discover":                     1018,
	"me.ele.ios.eleme":                        1019,
	"com.meituan.itakeaway":                   1020,
	"com.tencent.xin":                         1021,
	"com.tencent.mqq":                         1022,
	"com.sina.weibo":                          1023,
	"com.laiwang.DingTalk":                    1024,
	"com.baidu.BaiduMobile":                   1025,
	"com.autohome":                            1026,
	"com.ss.ios.auto":                         1027,
	"com.tencent.ww":                          1028,
	"tv.douyu.live":                           1029,
	"com.yy.kiwi":                             1030,
	"com.taobao.fleamarket":                   1031,
	"com.taobao.kbmeishi":                     1032,
	"com.taobao.travel":                       1033,
	"com.alibaba.retailTrader":                1034,
	"com.youku.YouKu":                         1035,
	"com.netease.kaola":                       1036,
	"com.taobao.special":                      1037,
	"com.taobao.live":                         1038,
	"com.alibaba.wireless":                    1039,
	"com.quark.browser":                       1040,
	"com.wdk.hmxs":                            1041,
	"com.cainiao.cnwireless":                  1042,
	"com.qiyi.iphone":                         1043,
	"com.jindidata.SkyEyes":                   1044,
	"com.lianjia.beike":                       1045,
	"com.zhihu.ios":                           1046,
	"SuningEMall":                             1047,
	"cn.10086.app":                            1049,
	"com.shizhuang.duapp":                     1050,
	"com.kugou.kugou1002":                     1051,
	"com.huazhu.xiaojianzhi":                  1052,
	"com.qts.customer":                        1053,
	"com.wodan.xianshijian":                   1054,
	"com.rrhired.customer":                    1055,
	"com.alimama.moon":                        1056,
	"io.dcloud.H576E6CC7":                     1057,
	"com.wuba":                                1058,
	"com.fanli.android.apps":                  1059,
	"com.jzyd.coupon":                         1060,
	"taobao://":                               1001,
	"openapp.jdmobile://":                     1002,
	"snsdk1128://":                            1003,
	"kwai://":                                 1004,
	"pinduoduo://":                            1005,
	"imeituan://":                             1006,
	"trainassist://":                          1007,
	"alipay://":                               1008,
	"kuaishoumini://":                         1009,
	"ttminiapp://":                            1010,
	"CtripWireless://":                        1011,
	"qunar://":                                1012,
	"iosamap://":                              1013,
	"diditaxi://":                             1014,
	"tmall://":                                1015,
	"ucbrowser://":                            1016,
	"vipshop://":                              1017,
	"xhs://":                                  1018,
	"eleme://":                                1019,
	"mms://":                                  1020,
	"weixin://":                               1021,
	"mqq://":                                  1022,
	"sinaweibo://":                            1023,
	"dingtalk://":                             1024,
	"baidu://":                                1025,
	"chehao://":                               1026,
	"dcx://":                                  1027,
	"wxwork://":                               1028,
	"douyu://":                                1029,
	"huya://":                                 1030,
	"xianyu://":                               1031,
	"koubei://":                               1032,
	"feizhu://":                               1033,
	"rtmall://":                               1034,
	"youku://":                                1035,
	"tb://":                                   1037,
	"dtapp://":                                1038,
	"zhihu://":                                1046,
	"kugouURL://":                             1051,
	"com.kmxs.reader":                         1063,
	"com.yueyou.cyreader":                     1063,
	"com.yy.yyvoicetool":                      1064,
	"com.ximalaya.ting.android":               1065,
	"com.gemd.iting":                          1065,
	"tv.danmaku.bili":                         1066,
	"tv.danmaku.bilianime":                    1066,
	"com.immomo.momo":                         1067,
	"com.wemomo.momoappdemo1":                 1067,
}

var appListMapGroup2 = map[int]AppListStu{
	//! 1065开始
	//! 最多64个 也就是1128 超过请另建一个Group
}

var appListMapGroup3 = map[int]AppListStu{
	//! 1129开始
	//! 最多64个 也就是1256 超过请另建一个Group
}
