package models

type DemandPlatformModel struct {
	// 上游平台id
	PlatformID string `json:"platform_id"`
	// 上游平台name
	PlatformName string `json:"platform_name"`
	// 是否enable
	IsEnable int `json:"is_enable"`
	// 是否冻结
	IsFreeze int `json:"is_freeze"`
	// 上游是否支持请求传竞价底价
	IsSupportCPMBidFloor int `json:"is_support_cpm_bid_floor"`
	// 是否允许自定义竞价底价
	IsCustomCPMBidFloor int `json:"is_custom_cpm_bid_floor"`
}

type DemandPlatformAndCorpModel struct {
	CorpId       int    `json:"corp_id"`
	PlatformID   string `json:"platform_id"`
	PlatformName string `json:"platform_name"`
	Corp         string `json:"corp"`
	AppId        string `json:"app_id"`
	AppName      string `json:"app_name"`
}
