package models

type DeviceAppListDataColumn struct {
	Name        string `json:"name"`        // 列的英文名
	Description string `json:"description"` // 列的中文注释
}

type AppListMappingInfo struct {
	AppId              int    `json:"app_id"`
	AppKey             string `json:"app_key"`
	AppName            string `json:"app_name"`
	AndroidPackageName string `json:"android_package_name"`
	IosPackageName     string `json:"ios_package_name"`
}

var AppListMappingInfoList = []AppListMappingInfo{
	{AppId: 1001, AppName: "淘宝", AndroidPackageName: "com.taobao.taobao", IosPackageName: "com.taobao.taobao4iphone"},
	{AppId: 1002, AppName: "京东", AndroidPackageName: "com.jingdong.app.mall", IosPackageName: "com.360buy.jdmobile"},
	{AppId: 1003, AppName: "抖音", AndroidPackageName: "com.ss.android.ugc.aweme", IosPackageName: "com.ss.iphone.ugc.Aweme"},
	{AppId: 1004, AppName: "快手", AndroidPackageName: "com.smile.gifmaker", IosPackageName: "com.jiangjia.gif"},
	{AppId: 1005, AppName: "拼多多", AndroidPackageName: "com.xunmeng.pinduoduo", IosPackageName: "com.xunmeng.pinduoduo"},
	{AppId: 1006, AppName: "美团", AndroidPackageName: "com.sankuai.meituan", IosPackageName: "com.meituan.imeituan"},
	{AppId: 1007, AppName: "12306", AndroidPackageName: "com.MobileTicket", IosPackageName: "cn.12306.rails12306"},
	{AppId: 1008, AppName: "支付宝", AndroidPackageName: "com.eg.android.AlipayGphone", IosPackageName: "com.alipay.iphoneclient"},
	{AppId: 1009, AppName: "快手极速版", AndroidPackageName: "com.kuaishou.nebula", IosPackageName: "com.kuaishou.nebula"},
	{AppId: 1010, AppName: "抖音极速版", AndroidPackageName: "com.ss.android.ugc.aweme.lite", IosPackageName: "com.ss.iphone.ugc.aweme.lite"},
	{AppId: 1011, AppName: "携程", AndroidPackageName: "ctrip.android.view", IosPackageName: "ctrip.com"},
	{AppId: 1012, AppName: "去哪儿", AndroidPackageName: "com.Qunar", IosPackageName: "com.qunar.iphoneclient8"},
	{AppId: 1013, AppName: "高德", AndroidPackageName: "com.autonavi.amap", IosPackageName: "com.autonavi.minimap"},
	{AppId: 1014, AppName: "滴滴", AndroidPackageName: "com.sdu.didi.psnger", IosPackageName: "com.xiaojukeji.didi"},
	{AppId: 1015, AppName: "天猫", AndroidPackageName: "com.tmall.wireless", IosPackageName: "com.taobao.tmall"},
	{AppId: 1016, AppName: "UC浏览器", AndroidPackageName: "com.UCMobile", IosPackageName: "com.ucweb.iphone.lowversion"},
	{AppId: 1017, AppName: "唯品会", AndroidPackageName: "com.achievo.vipshop", IosPackageName: "com.vipshop.iphone"},
	{AppId: 1018, AppName: "小红书", AndroidPackageName: "com.xingin.xhs", IosPackageName: "com.xingin.discover"},
	{AppId: 1019, AppName: "饿了么", AndroidPackageName: "me.ele", IosPackageName: "me.ele.ios.eleme"},
	{AppId: 1020, AppName: "美团外卖", AndroidPackageName: "com.sankuai.meituan.takeoutnew", IosPackageName: "com.meituan.itakeaway"},
	{AppId: 1021, AppName: "微信", AndroidPackageName: "com.tencent.mm", IosPackageName: "com.tencent.xin"},
	{AppId: 1022, AppName: "QQ", AndroidPackageName: "com.tencent.mobileqq", IosPackageName: "com.tencent.mqq"},
	{AppId: 1023, AppName: "微博", AndroidPackageName: "com.sina.weibo", IosPackageName: "com.sina.weibo"},
	{AppId: 1024, AppName: "钉钉", AndroidPackageName: "com.alibaba.android.rimet", IosPackageName: "com.laiwang.DingTalk"},
	{AppId: 1025, AppName: "百度", AndroidPackageName: "com.baidu.searchbox", IosPackageName: "com.baidu.BaiduMobile"},
	{AppId: 1026, AppName: "汽车之家", AndroidPackageName: "com.cubic.autohome", IosPackageName: "com.autohome"},
	{AppId: 1027, AppName: "懂车帝", AndroidPackageName: "com.ss.android.auto", IosPackageName: "com.ss.ios.auto"},
	{AppId: 1028, AppName: "企业微信", AndroidPackageName: "com.tencent.wework", IosPackageName: "com.tencent.ww"},
	{AppId: 1029, AppName: "斗鱼", AndroidPackageName: "air.tv.douyu.android", IosPackageName: "tv.douyu.live"},
	{AppId: 1030, AppName: "虎牙", AndroidPackageName: "com.duowan.kiwi", IosPackageName: "com.yy.kiwi"},
	{AppId: 1031, AppName: "闲鱼", AndroidPackageName: "com.taobao.idlefish", IosPackageName: "com.taobao.fleamarket"},
	{AppId: 1032, AppName: "口碑", AndroidPackageName: "com.taobao.mobile.dipei", IosPackageName: "com.taobao.kbmeishi"},
	{AppId: 1033, AppName: "飞猪", AndroidPackageName: "com.taobao.trip", IosPackageName: "com.taobao.travel"},
	{AppId: 1034, AppName: "零售通", AndroidPackageName: "com.alibaba.wireless.lstretailer", IosPackageName: "com.alibaba.retailTrader"},
	{AppId: 1035, AppName: "优酷", AndroidPackageName: "com.youku.phone", IosPackageName: "com.youku.YouKu"},
	{AppId: 1036, AppName: "网易考拉", AndroidPackageName: "com.kaola", IosPackageName: "com.netease.kaola"},
	{AppId: 1037, AppName: "淘特", AndroidPackageName: "com.taobao.litetao", IosPackageName: "com.taobao.special"},
	{AppId: 1038, AppName: "点淘", AndroidPackageName: "com.taobao.live", IosPackageName: "com.taobao.live"},
	{AppId: 1039, AppName: "1688", AndroidPackageName: "com.alibaba.wireless", IosPackageName: "com.alibaba.wireless"},
	{AppId: 1040, AppName: "夸克浏览器", AndroidPackageName: "com.quark.browser", IosPackageName: "com.quark.browser"},
	{AppId: 1041, AppName: "盒马", AndroidPackageName: "com.wudaokou.hippo", IosPackageName: "com.wdk.hmxs"},
	{AppId: 1042, AppName: "菜鸟裹裹", AndroidPackageName: "com.cainiao.wireless", IosPackageName: "com.cainiao.cnwireless"},
	{AppId: 1043, AppName: "爱奇艺", AndroidPackageName: "com.qiyi.video", IosPackageName: "com.qiyi.iphone"},
	{AppId: 1044, AppName: "天眼查", AndroidPackageName: "com.tianyancha.skyeye", IosPackageName: "com.jindidata.SkyEyes"},
	{AppId: 1045, AppName: "贝壳", AndroidPackageName: "com.lianjia.beike", IosPackageName: "com.lianjia.beike"},
	{AppId: 1046, AppName: "知乎", AndroidPackageName: "com.zhihu.android", IosPackageName: "com.zhihu.ios"},
	{AppId: 1047, AppName: "苏宁易购", AndroidPackageName: "com.suning.mobile.ebuy", IosPackageName: "SuningEMall"},
	{AppId: 1048, AppName: "DNF助手", AndroidPackageName: "com.tencent.gamehelper.dnf", IosPackageName: ""},
	{AppId: 1049, AppName: "中国移动", AndroidPackageName: "com.greenpoint.android.mc10086.activity", IosPackageName: "cn.10086.app"},
	{AppId: 1050, AppName: "得物", AndroidPackageName: "com.shizhuang.duapp", IosPackageName: ""},
	{AppId: 1051, AppName: "酷狗", AndroidPackageName: "com.kugou.android", IosPackageName: "com.kugou.kugou1002"},
	{AppId: 1052, AppName: "兼职猫", AndroidPackageName: "com.huazhu.xiaojianzhi", IosPackageName: ""},
	{AppId: 1053, AppName: "青团社兼职", AndroidPackageName: "com.qts.customer", IosPackageName: ""},
	{AppId: 1054, AppName: "兼职兼客", AndroidPackageName: "com.wodan.xianshijian", IosPackageName: ""},
	{AppId: 1055, AppName: "人人兼职", AndroidPackageName: "com.rrhired.customer", IosPackageName: ""},
	{AppId: 1056, AppName: "淘宝联盟", AndroidPackageName: "com.alimama.moon", IosPackageName: ""},
	{AppId: 1057, AppName: "鱼泡网", AndroidPackageName: "io.dcloud.H576E6CC7", IosPackageName: ""},
	{AppId: 1058, AppName: "58同城", AndroidPackageName: "com.wuba", IosPackageName: ""},
	{AppId: 1059, AppName: "返利网", AndroidPackageName: "com.fanli.android.apps", IosPackageName: ""},
	{AppId: 1060, AppName: "省钱快报", AndroidPackageName: "com.jzyd.coupon", IosPackageName: ""},
	{AppId: 1065, AppName: "喜马拉雅", AndroidPackageName: "com.ximalaya.ting.android", IosPackageName: "com.gemd.iting"},
	{AppId: 1066, AppName: "哔哩哔哩", AndroidPackageName: "tv.danmaku.bili", IosPackageName: "tv.danmaku.bilianime"},
	{AppId: 1067, AppName: "陌陌", AndroidPackageName: "com.immomo.momo", IosPackageName: "com.wemomo.momoappdemo1"},
}

var AppListMapping = map[int]string{
	1001: "taobao",
	1002: "jingdong",
	1003: "douyin",
	1004: "kuaishou",
	1005: "pdd",
	1006: "meituan",
	1007: "mobile_ticket",
	1008: "alipay",
	1009: "kuaishou_lite",
	1010: "douyin_lite",
	1013: "gaode",
	1014: "didi",
	1015: "tmall",
	1019: "ele",
	1020: "meituan_takeout",
	1021: "wx",
	1022: "qq",
	1023: "weibo",
	1025: "baidu",
	1048: "dnf",
	1049: "cm",
	1052: "jzm",
	1053: "qts",
	1054: "jzjk",
	1055: "rrhired",
	1056: "alimama",
	1057: "dcloud",
	1058: "wuba",
	1059: "fanli",
	1060: "jzyd",
}

var AppListAppIdDidMapping = map[int]string{
	1001: "taobao",
	1002: "jingdong",
	1004: "kuaishou",
	1005: "pdd",
	1006: "meituan",
	1008: "alipay",
	1023: "weibo",
}

func GetAppListMappingInfoList() (items []AppListMappingInfo) {
	for appId, appKey := range AppListMapping {
		var item AppListMappingInfo
		item.AppId = appId
		item.AppKey = appKey

		for _, appInfo := range AppListMappingInfoList {
			if item.AppId == appInfo.AppId {
				item.AppName = appInfo.AppName
				item.AndroidPackageName = appInfo.AndroidPackageName
				item.IosPackageName = appInfo.IosPackageName
			}
		}
		items = append(items, item)
	}
	return
}

var DeviceAppListDataColumns = []DeviceAppListDataColumn{
	{
		Name:        "imei",
		Description: "IMEI",
	},
	{
		Name:        "imei_md5",
		Description: "IMEI_MD5",
	},
	{
		Name:        "android_id",
		Description: "ANDROID_ID",
	},
	{
		Name:        "android_id_md5",
		Description: "ANDROID_ID_MD5",
	},
	{
		Name:        "oaid",
		Description: "OAID",
	},
	{
		Name:        "oaid_md5",
		Description: "OAID_MD5",
	},
	{
		Name:        "caid",
		Description: "CAID",
	},
	{
		Name:        "caid_version",
		Description: "CAID_VERSION",
	},
	{
		Name:        "os",
		Description: "系统",
	},
	{
		Name:        "osv",
		Description: "系统版本",
	},
	{
		Name:        "model",
		Description: "机型",
	},
	{
		Name:        "manufacturer",
		Description: "厂商",
	},
	{
		Name:        "ip",
		Description: "IP",
	},
	{
		Name:        "ip_country",
		Description: "IP_国家",
	},
	{
		Name:        "ip_province",
		Description: "IP_省",
	},
	{
		Name:        "ip_city",
		Description: "IP_市",
	},
	{
		Name:        "lat",
		Description: "经度",
	},
	{
		Name:        "lng",
		Description: "纬度",
	},
	{
		Name:        "gps_country",
		Description: "GPS_国家",
	},
	{
		Name:        "gps_province",
		Description: "GPS_省",
	},
	{
		Name:        "gps_city",
		Description: "GPS_市",
	},
	{
		Name:        "taobao",
		Description: "淘宝",
	},
	{
		Name:        "jingdong",
		Description: "京东",
	},
	{
		Name:        "douyin",
		Description: "抖音",
	},
	{
		Name:        "kuaishou",
		Description: "快手",
	},
	{
		Name:        "pdd",
		Description: "拼多多",
	},
	{
		Name:        "meituan",
		Description: "美团",
	},
	{
		Name:        "mobile_ticket",
		Description: "12306",
	},
	{
		Name:        "alipay",
		Description: "支付宝",
	},
	{
		Name:        "kuaishou_lite",
		Description: "快手极速版",
	},
	{
		Name:        "douyin_lite",
		Description: "抖音极速版",
	},
	{
		Name:        "gaode",
		Description: "高德",
	},
	{
		Name:        "didi",
		Description: "滴滴",
	},
	{
		Name:        "tmall",
		Description: "天猫",
	},
	{
		Name:        "ele",
		Description: "饿了么",
	},
	{
		Name:        "meituan_takeout",
		Description: "美团外卖",
	},
	{
		Name:        "wx",
		Description: "微信",
	},
	{
		Name:        "qq",
		Description: "QQ",
	},
	{
		Name:        "weibo",
		Description: "微博",
	},
	{
		Name:        "baidu",
		Description: "百度",
	},
	{
		Name:        "dnf",
		Description: "DNF助手",
	},
	{
		Name:        "cm",
		Description: "中国移动",
	},
	{
		Name:        "jzm",
		Description: "兼职猫",
	},
	{
		Name:        "qts",
		Description: "青团社兼职",
	},
	{
		Name:        "jzjk",
		Description: "兼职兼客",
	},
	{
		Name:        "rrhired",
		Description: "人人兼职",
	},
	{
		Name:        "alimama",
		Description: "淘宝联盟",
	},
	{
		Name:        "dcloud",
		Description: "鱼泡网",
	},
	{
		Name:        "wuba",
		Description: "58同城",
	},
	{
		Name:        "fanli",
		Description: "返利网",
	},
	{
		Name:        "jzyd",
		Description: "省钱快报",
	},
}
