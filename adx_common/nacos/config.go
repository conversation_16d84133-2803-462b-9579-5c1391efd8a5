package nacos

import (
	"errors"
	"fmt"
	"time"
)

const (
	defaultIPAddr      = "mse-e53a8aa2-p.nacos-ans.mse.aliyuncs.com"
	defaultPort        = 8848
	defaultContextPath = "./nacos"
	defaultFormat      = FormatYAML
	defaultAccessKey   = "LTAI5tJ3pUzf96rXWgzPPwre"
	defaultSecretKey   = "******************************"
)

// NacosConfig nacos连接配置
type NacosConfig struct {
	// 只保留业务相关字段
	NamespaceID string `json:"namespace_id" yaml:"namespaceID"`
	Group       string `json:"group" yaml:"group"`
	DataID      string `json:"data_id" yaml:"dataID"`
	// 缓存键，用于快速查找和索引
	CacheKey string `json:"cache_key" yaml:"cacheKey"`
	// 可选配置
	TimeoutMs      uint64 `json:"timeout_ms" yaml:"timeoutMs"`
	ListenInterval uint64 `json:"listen_interval" yaml:"listenInterval"`
	LogDir         string `json:"log_dir" yaml:"logDir"`
	CacheDir       string `json:"cache_dir" yaml:"cacheDir"`
	LogLevel       string `json:"log_level" yaml:"logLevel"`
}

// NewNacosConfig 工厂方法，自动填充固定字段
func NewNacosConfig(namespaceID, group, dataID, cacheKey string) NacosConfig {
	return NacosConfig{
		NamespaceID:    namespaceID,
		Group:          group,
		DataID:         dataID,
		CacheKey:       cacheKey,
		TimeoutMs:      5000,
		ListenInterval: 30000,
		LogDir:         "./nacos/log",
		CacheDir:       "./nacos/cache",
		LogLevel:       "debug",
	}
}

// ConfigItem 配置项
type ConfigItem struct {
	// Nacos连接配置
	NacosConfig NacosConfig `json:"nacos_config" yaml:"nacosConfig"`
}

// MultiNacosConfig 多nacos配置管理
type MultiNacosConfig struct {
	// 配置项列表
	Configs []ConfigItem `json:"configs" yaml:"configs"`
	// 全局设置
	Global GlobalConfig `json:"global" yaml:"global"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	// 默认超时时间（毫秒）
	DefaultTimeoutMs uint64 `json:"default_timeout_ms" yaml:"defaultTimeoutMs"`
	// 默认监听间隔（毫秒）
	DefaultListenInterval uint64 `json:"default_listen_interval" yaml:"defaultListenInterval"`
	// 默认日志级别
	DefaultLogLevel string `json:"default_log_level" yaml:"defaultLogLevel"`
	// 重连间隔（秒）
	ReconnectInterval time.Duration `json:"reconnect_interval" yaml:"reconnectInterval"`
	// 最大重连次数
	MaxRetryTimes int `json:"max_retry_times" yaml:"maxRetryTimes"`
	// 健康检查间隔（秒）
	HealthCheckInterval time.Duration `json:"health_check_interval" yaml:"healthCheckInterval"`
}

// Validate 验证配置
func (c *NacosConfig) Validate() error {
	if c.NamespaceID == "" {
		return errors.New("namespace_id不能为空")
	}
	if c.Group == "" {
		return errors.New("group不能为空")
	}
	if c.DataID == "" {
		return errors.New("data_id不能为空")
	}
	return nil
}

// GetServerAddr 获取服务器地址
func (c *NacosConfig) GetServerAddr() string {
	return fmt.Sprintf("%s://%s:%d%s", "http", defaultIPAddr, defaultPort, defaultContextPath)
}

// Validate 验证配置项
func (c *ConfigItem) Validate() error {
	return c.NacosConfig.Validate()
}

// SetDefaults 设置默认值
func (c *MultiNacosConfig) SetDefaults() {
	if c.Global.DefaultTimeoutMs == 0 {
		c.Global.DefaultTimeoutMs = 5000 // 5秒
	}
	if c.Global.DefaultListenInterval == 0 {
		c.Global.DefaultListenInterval = 30000 // 30秒
	}
	if c.Global.DefaultLogLevel == "" {
		c.Global.DefaultLogLevel = "info"
	}
	if c.Global.ReconnectInterval == 0 {
		c.Global.ReconnectInterval = 30 * time.Second
	}
	if c.Global.MaxRetryTimes == 0 {
		c.Global.MaxRetryTimes = 3
	}
	if c.Global.HealthCheckInterval == 0 {
		c.Global.HealthCheckInterval = 60 * time.Second
	}

	// 为每个配置项设置默认值
	for i := range c.Configs {
		config := &c.Configs[i]
		if config.NacosConfig.TimeoutMs == 0 {
			config.NacosConfig.TimeoutMs = c.Global.DefaultTimeoutMs
		}
		if config.NacosConfig.ListenInterval == 0 {
			config.NacosConfig.ListenInterval = c.Global.DefaultListenInterval
		}
		if config.NacosConfig.LogLevel == "" {
			config.NacosConfig.LogLevel = c.Global.DefaultLogLevel
		}
	}
}

// Validate 验证多配置
func (c *MultiNacosConfig) Validate() error {
	if len(c.Configs) == 0 {
		return errors.New("至少需要一个配置项")
	}

	for _, config := range c.Configs {
		if err := config.Validate(); err != nil {
			return fmt.Errorf("配置项 验证失败: %v", err)
		}
	}

	return nil
}

// NewMultiNacosConfig 批量创建多配置，支持直接传入参数组
func NewMultiNacosConfig(configs []NacosConfig, global ...GlobalConfig) MultiNacosConfig {
	items := make([]ConfigItem, len(configs))
	for i, c := range configs {
		items[i] = ConfigItem{NacosConfig: c}
	}
	var g GlobalConfig
	if len(global) > 0 {
		g = global[0]
	} else {
		g = GlobalConfig{
			DefaultTimeoutMs:      5000,
			DefaultListenInterval: 30000,
			DefaultLogLevel:       "debug",
			ReconnectInterval:     30 * time.Second,
			MaxRetryTimes:         3,
			HealthCheckInterval:   60 * time.Second,
		}
	}
	return MultiNacosConfig{
		Configs: items,
		Global:  g,
	}
}
