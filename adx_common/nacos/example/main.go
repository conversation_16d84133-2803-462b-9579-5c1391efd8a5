package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/nacos"
)

func main() {
	// 创建配置（使用您提供的配置信息）
	config := nacos.NewMultiNacosConfig([]nacos.NacosConfig{
		nacos.NewNacosConfig(
			"1b9cd8f0-f90b-4291-80b8-66a9019cd2ba",
			"prod",
			"server.yml",
			"server",
		),
		nacos.NewNacosConfig(
			"1b9cd8f0-f90b-4291-80b8-66a9019cd2ba",
			"prod",
			"database.yml",
			"database",
		),
	})

	// 添加配置变更监听器
	log.Println("添加配置监听器...")
	listener := nacos.ConfigListenerFunc(func(event nacos.ConfigChangeEvent) error {
		log.Printf("📢 配置变更事件:")
		log.Printf("  - 配置键: %s", event.ConfigKey)
		log.Printf("  - 数据ID: %s", event.DataID)
		log.Printf("  - 分组: %s", event.Group)
		log.Printf("  - 变更类型: %s", event.ChangeType)
		log.Printf("  - 时间戳: %s", event.Timestamp.Format(time.RFC3339))
		log.Printf("  - 内容: %s", event.Content)

		// 这里可以添加你的业务逻辑
		// 例如：重新加载配置、更新内存缓存、通知其他组件等
		fmt.Println("--------------------")
		fmt.Printf("配置内容:\n%s\n", event.Content)
		fmt.Println("--------------------")

		return nil
	})

	if err := nacos.InitWithConfig(config); err != nil {
		log.Fatalf("初始化失败: %v", err)
	}

	if err := nacos.AddGlobalListener(listener); err != nil {
		log.Fatalf("添加监听器失败: %v", err)
	}

	// 演示CacheKey功能
	log.Println("📝 演示CacheKey功能:")
	for _, cacheKey := range config.Configs {
		content, err := nacos.GetConfigByCacheKey(cacheKey.NacosConfig.CacheKey)
		if err != nil {
			log.Printf("  ❌ 获取配置 %v 失败: %v", cacheKey, err)
		} else {
			log.Printf("  ✅ 配置 %v 内容: %v", cacheKey, content)
		}
	}

	log.Println("🚀 Nacos配置监听服务已启动！")
	log.Println("📝 监听配置变更中... (按 Ctrl+C 退出)")

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	log.Println("📡 收到退出信号，正在优雅关闭...")

	// 创建一个带超时的关闭流程
	done := make(chan bool, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("关闭过程中发生panic: %v", r)
			}
			done <- true
		}()

		log.Println("关闭Nacos SDK...")
		if err := nacos.Close(); err != nil {
			log.Printf("关闭SDK时出错: %v", err)
		} else {
			log.Println("✅ 优雅关闭完成")
		}

	}()

	// 等待关闭完成，最多等待5秒
	select {
	case <-done:
		log.Println("✅ 优雅关闭完成")
	case <-time.After(10 * time.Second):
		log.Println("⚠️ 关闭超时，强制退出")
	}
}
