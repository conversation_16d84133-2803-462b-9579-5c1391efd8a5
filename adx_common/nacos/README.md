# nacos 组件说明

本目录实现了基于 [nacos-sdk-go](https://github.com/nacos-group/nacos-sdk-go) 的配置中心客户端，支持多配置管理、配置监听、健康检查等功能，适用于微服务场景下的动态配置管理。

## 主要功能
- 支持多 Nacos 配置的统一管理
- 支持配置变更监听与回调
- 健康检查与自动重连机制
- 配置项的结构化管理与校验
- 支持多种配置格式（YAML、JSON、Properties、TOML、XML）

## 主要类型说明

- `Client`：单个 Nacos 配置客户端，负责连接、获取配置、监听变更等。
- `Manager`：多客户端管理器，统一管理多个 Nacos 配置，支持全局监听、批量操作等。
- `ConfigItem`/`NacosConfig`：配置项及其参数结构体。
- `ConfigListener`：配置变更监听器接口。
- `ConfigChangeEvent`：配置变更事件结构体。
- `HealthCheckResult`：健康检查结果。

## 快速开始

### 1. 定义配置项
```go
import "your_module_path/nacos"

config := nacos.NewNacosConfig(
    "namespace-id",
    "group",
    "data-id",
    "cache-key", // 用于唯一标识该配置
)
item := nacos.ConfigItem{NacosConfig: config}
```

### 2. 创建客户端
```go
client, err := nacos.NewClient(item, 3) // 3为最大重试次数
if err != nil {
    panic(err)
}
```

### 3. 获取配置内容
```go
content, err := client.GetConfig()
if err != nil {
    // 处理错误
}
fmt.Println(content)
```

### 4. 监听配置变更
```go
type MyListener struct{}
func (l *MyListener) OnConfigChange(event nacos.ConfigChangeEvent) error {
    fmt.Printf("配置变更: %+v\n", event)
    return nil
}
func (l *MyListener) OnError(configKey string, err error) {
    fmt.Printf("监听器错误: %s, %v\n", configKey, err)
}

client.AddListener(&MyListener{})
client.StartListening()
```

### 5. 多配置统一管理
```go
multiConfig := nacos.NewMultiNacosConfig([]nacos.NacosConfig{config1, config2})
manager, err := nacos.NewManager(multiConfig)
if err != nil {
    panic(err)
}
manager.StartListening()
```

## 配置结构说明

```go
type NacosConfig struct {
    NamespaceID    string // 命名空间ID
    Group          string // 分组
    DataID         string // 数据ID
    CacheKey       string // 缓存键（唯一标识）
    TimeoutMs      uint64 // 超时时间（毫秒）
    ListenInterval uint64 // 监听间隔（毫秒）
    LogDir         string // 日志目录
    CacheDir       string // 缓存目录
    LogLevel       string // 日志级别
}
```

## 健康检查与自动重连
- `client.HealthCheck()` 可主动检查当前配置的健康状态。
- `manager` 支持定时自动健康检查与重连，无需手动干预。

## 配置解析辅助
可使用 `ParseConfigYamlData` 等方法将配置内容解析为结构体：
```go
var obj YourStruct
err := nacos.ParseConfigYamlData([]byte(content), &obj)
```

## 依赖
- [nacos-sdk-go v2](https://github.com/nacos-group/nacos-sdk-go)
- [spf13/viper](https://github.com/spf13/viper)（用于配置解析）

## 目录结构
- `client.go`：单客户端实现
- `manager.go`：多客户端管理
- `config.go`：配置结构体与校验
- `types.go`：类型定义与事件
- `parse.go`：配置解析辅助

## 注意事项
- 请确保 Nacos 服务端可用，相关参数（如 namespace、group、dataId）需与服务端一致。
- 建议为每个配置项设置唯一的 `CacheKey`。
- 日志与缓存目录可根据实际情况调整。

---

如需更多示例或问题反馈，请联系维护者。
