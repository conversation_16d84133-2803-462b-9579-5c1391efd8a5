package nacos

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// Manager 多nacos客户端管理器
type Manager struct {
	config          MultiNacosConfig
	clients         map[string]*Client
	globalListeners []ConfigListener
	mu              sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
	isRunning       bool
}

// NewManager 创建新的管理器
func NewManager(config MultiNacosConfig) (*Manager, error) {
	// 设置默认值并验证配置
	config.SetDefaults()
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 检查cacheKey是否重复
	cacheKeys := make(map[string]bool)
	for _, config := range config.Configs {
		if _, ok := cacheKeys[config.NacosConfig.CacheKey]; ok {
			return nil, fmt.Errorf("cacheKey重复: %s", config.NacosConfig.CacheKey)
		}
		cacheKeys[config.NacosConfig.CacheKey] = true
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &Manager{
		config:          config,
		clients:         make(map[string]*Client),
		globalListeners: make([]ConfigListener, 0),
		ctx:             ctx,
		cancel:          cancel,
	}

	// 初始化客户端
	if err := manager.initClients(); err != nil {
		cancel()
		return nil, fmt.Errorf("初始化客户端失败: %v", err)
	}

	return manager, nil
}

// initClients 初始化所有客户端
func (m *Manager) initClients() error {
	for _, configItem := range m.config.Configs {
		client, err := NewClient(configItem, m.config.Global.MaxRetryTimes)
		if err != nil {
			fmt.Printf("创建客户端失败: %v", err)
			return err
		}

		// 为客户端添加全局监听器
		for _, listener := range m.globalListeners {
			client.AddListener(listener)
		}

		key := configItem.NacosConfig.CacheKey
		m.clients[key] = client
		fmt.Printf("客户端初始化成功: %s", key)
	}

	return nil
}

// AddGlobalListener 添加全局配置监听器（所有客户端都会收到通知）
func (m *Manager) AddGlobalListener(listener ConfigListener) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.globalListeners = append(m.globalListeners, listener)

	// 为已存在的客户端添加监听器
	for _, client := range m.clients {
		client.AddListener(listener)
	}
}

// GetConfig 获取指定配置的内容
func (m *Manager) GetConfig(configKey string) (string, error) {
	m.mu.RLock()
	client, exists := m.clients[configKey]
	m.mu.RUnlock()

	if !exists {
		return "", fmt.Errorf("配置键 %s 不存在", configKey)
	}

	return client.GetConfig()
}

// GetAllConfigs 获取所有配置的内容
func (m *Manager) GetAllConfigs() (map[string]string, error) {
	m.mu.RLock()
	clients := make(map[string]*Client, len(m.clients))
	for k, v := range m.clients {
		clients[k] = v
	}
	m.mu.RUnlock()

	configs := make(map[string]string)
	for key, client := range clients {
		content, err := client.GetConfig()
		if err != nil {
			fmt.Printf("获取配置 %s 失败: %v", key, err)
			return nil, fmt.Errorf("获取配置 %s 失败: %v", key, err)
		}
		configs[key] = content
	}

	return configs, nil
}

// StartListening 开始监听所有配置的变化
func (m *Manager) StartListening() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.isRunning {
		return fmt.Errorf("管理器已经在运行中")
	}

	// 启动所有客户端的监听
	for key, client := range m.clients {
		go func(configKey string, c *Client) {
			select {
			case <-m.ctx.Done():
				return
			default:
				if err := c.StartListening(); err != nil {
					fmt.Printf("启动配置 %s 监听失败: %v", configKey, err)
				} else {
					fmt.Printf("配置 %s 开始监听", configKey)
				}
			}
		}(key, client)
	}

	// 启动自动重连
	go m.autoReconnect()

	m.isRunning = true
	return nil
}

// autoReconnect 自动重连
func (m *Manager) autoReconnect() {
	if m.config.Global.ReconnectInterval <= 0 {
		return
	}

	ticker := time.NewTicker(m.config.Global.ReconnectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkAndReconnect()
		}
	}
}

// checkAndReconnect 检查并重连不健康的客户端
func (m *Manager) checkAndReconnect() {
	m.mu.RLock()
	clients := make(map[string]*Client, len(m.clients))
	for k, v := range m.clients {
		clients[k] = v
	}
	m.mu.RUnlock()

	for key, client := range clients {
		result := client.HealthCheck()
		if !result.Healthy {
			fmt.Printf("配置 %s 不健康，尝试重连", key)
			if err := client.Reconnect(); err != nil {
				fmt.Printf("配置 %s 重连失败: %v", key, err)
			} else {
				fmt.Printf("配置 %s 重连成功", key)
			}
		}
	}
}

// GetHealthStatus 获取所有客户端的健康状态
func (m *Manager) GetHealthStatus() map[string]HealthCheckResult {
	m.mu.RLock()
	clients := make(map[string]*Client, len(m.clients))
	for k, v := range m.clients {
		clients[k] = v
	}
	m.mu.RUnlock()

	results := make(map[string]HealthCheckResult)
	for key, client := range clients {
		results[key] = client.HealthCheck()
	}

	return results
}

// GetClient 获取指定的客户端
func (m *Manager) GetClient(configKey string) (*Client, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, exists := m.clients[configKey]
	if !exists {
		return nil, fmt.Errorf("配置键 %s 不存在", configKey)
	}

	return client, nil
}

// GetClientByCacheKey 通过CacheKey获取客户端（别名方法，与GetClient功能相同）
func (m *Manager) GetClientByCacheKey(cacheKey string) (*Client, error) {
	return m.GetClient(cacheKey)
}

// GetConfigByCacheKey 通过CacheKey获取配置内容（别名方法）
func (m *Manager) GetConfigByCacheKey(cacheKey string) (string, error) {
	return m.GetConfig(cacheKey)
}

// GetClientKeys 获取所有客户端的配置键
func (m *Manager) GetClientKeys() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	keys := make([]string, 0, len(m.clients))
	for key := range m.clients {
		keys = append(keys, key)
	}

	return keys
}

// IsRunning 检查管理器是否正在运行
func (m *Manager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isRunning
}

// Close 关闭管理器和所有客户端
func (m *Manager) Close() error {
	// 首先取消context，通知所有goroutine停止
	m.cancel()

	// 关闭所有客户端
	for key, client := range m.clients {
		if err := client.Close(); err != nil {
			fmt.Printf("关闭客户端 %s 失败: %v", key, err)
		} else {
			fmt.Printf("客户端 %s 已关闭", key)
		}
	}

	m.clients = nil
	m.globalListeners = nil

	fmt.Printf("管理器已关闭")
	return nil
}
