package nacos

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
)

// Client nacos客户端
type Client struct {
	config      ConfigItem
	nacosClient config_client.IConfigClient
	listeners   []ConfigListener
	mu          sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
	isListening bool
	lastContent string
	retryCount  int
	maxRetries  int
}

// NewClient 创建新的nacos客户端
func NewClient(config ConfigItem, maxRetries int) (*Client, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	client := &Client{
		config:     config,
		listeners:  make([]ConfigListener, 0),
		ctx:        ctx,
		cancel:     cancel,
		maxRetries: maxRetries,
	}

	if err := client.connect(); err != nil {
		cancel()
		return nil, fmt.Errorf("连接nacos失败: %v", err)
	}

	return client, nil
}

// connect 连接到nacos
func (c *Client) connect() error {
	serverConfigs := []constant.ServerConfig{
		{
			IpAddr:      defaultIPAddr,
			Port:        defaultPort,
			ContextPath: defaultContextPath,
			Scheme:      "http",
		},
	}

	clientConfig := constant.ClientConfig{
		NamespaceId:         c.config.NacosConfig.NamespaceID,
		TimeoutMs:           c.config.NacosConfig.TimeoutMs,
		NotLoadCacheAtStart: true,
		LogDir:              c.config.NacosConfig.LogDir,
		CacheDir:            c.config.NacosConfig.CacheDir,
		LogLevel:            c.config.NacosConfig.LogLevel,
		AccessKey:           defaultAccessKey,
		SecretKey:           defaultSecretKey,
	}

	nacosClient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		return err
	}

	c.nacosClient = nacosClient
	return nil
}

// AddListener 添加配置监听器
func (c *Client) AddListener(listener ConfigListener) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.listeners = append(c.listeners, listener)
}

// GetConfig 获取配置内容
func (c *Client) GetConfig() (string, error) {
	content, err := c.nacosClient.GetConfig(vo.ConfigParam{
		DataId: c.config.NacosConfig.DataID,
		Group:  c.config.NacosConfig.Group,
	})
	if err != nil {
		return "", err
	}
	return content, nil
}

// GetConfigKey 获取配置键
func (c *Client) GetConfigKey() string {
	return buildConfigMapKeyFromConfigItem(&c.config)
}

// HealthCheck 健康检查
func (c *Client) HealthCheck() HealthCheckResult {
	result := HealthCheckResult{
		ConfigKey: buildConfigMapKeyFromConfigItem(&c.config),
		CheckTime: time.Now(),
	}

	// 尝试获取配置来检查连接状态
	_, err := c.GetConfig()
	if err != nil {
		result.Healthy = false
		result.Message = fmt.Sprintf("健康检查失败: %v", err)
	} else {
		result.Healthy = true
		result.Message = "健康检查正常"
	}

	return result
}

// Reconnect 重新连接
func (c *Client) Reconnect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	wasListening := c.isListening

	// 如果正在监听，先停止
	if c.isListening {
		_ = c.nacosClient.CancelListenConfig(vo.ConfigParam{
			DataId: c.config.NacosConfig.DataID,
			Group:  c.config.NacosConfig.Group,
		})
		c.isListening = false
	}

	// 重新连接
	if err := c.connect(); err != nil {
		return err
	}

	// 如果之前在监听，重新开始监听
	if wasListening {
		return c.StartListening()
	}

	c.retryCount = 0 // 重连成功后重置重试计数
	return nil
}

// startListeningUnsafe 不加锁的开始监听（内部使用）
func (c *Client) StartListening() error {
	if c.isListening {
		return fmt.Errorf("客户端 %s 已经在监听中", buildConfigMapKeyFromConfigItem(&c.config))
	}

	// 先获取当前配置
	content, err := c.GetConfig()
	if err != nil {
		return fmt.Errorf("获取初始配置失败: %v", err)
	}
	c.lastContent = content
	// 注册监听器
	err = c.nacosClient.ListenConfig(vo.ConfigParam{
		DataId: c.config.NacosConfig.DataID,
		Group:  c.config.NacosConfig.Group,
		OnChange: func(namespace, group, dataId, data string) {
			fmt.Println("OnChange", namespace, group, dataId, data)
			changeType := ChangeTypeUpdate
			if data == "" {
				changeType = ChangeTypeDelete
			} else if c.lastContent == "" {
				changeType = ChangeTypeAdd
			}

			event := ConfigChangeEvent{
				ConfigKey:   buildConfigMapKeyFromConfigItem(&c.config),
				DataID:      dataId,
				Group:       group,
				NamespaceID: namespace,
				Content:     data,
				Format:      defaultFormat,
				Timestamp:   time.Now(),
				ChangeType:  changeType,
			}

			c.lastContent = data
			c.notifyListeners(event)
		},
	})

	if err != nil {
		return fmt.Errorf("注册配置监听器失败: %v", err)
	}

	c.isListening = true
	return nil
}

// notifyListeners 通知所有监听器
func (c *Client) notifyListeners(event ConfigChangeEvent) {
	c.mu.RLock()
	listeners := make([]ConfigListener, len(c.listeners))
	copy(listeners, c.listeners)
	c.mu.RUnlock()

	for _, listener := range listeners {
		go func(l ConfigListener) {
			defer func() {
				if r := recover(); r != nil {
					l.OnError(buildConfigMapKeyFromConfigItem(&c.config), fmt.Errorf("监听器panic: %v", r))
				}
			}()

			// 创建一个带超时的context
			ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
			defer cancel()

			// 使用channel来处理监听器调用
			done := make(chan error, 1)
			go func() {
				defer func() {
					if r := recover(); r != nil {
						done <- fmt.Errorf("监听器panic: %v", r)
					}
				}()
				done <- l.OnConfigChange(event)
			}()

			select {
			case <-ctx.Done():
				// context取消或超时
				l.OnError(buildConfigMapKeyFromConfigItem(&c.config), fmt.Errorf("监听器处理超时或被取消"))
			case err := <-done:
				if err != nil {
					l.OnError(buildConfigMapKeyFromConfigItem(&c.config), err)
				}
			}
		}(listener)
	}
}

// Close 关闭客户端
func (c *Client) Close() error {
	if c.isListening {
		_ = c.nacosClient.CancelListenConfig(vo.ConfigParam{
			DataId: c.config.NacosConfig.DataID,
			Group:  c.config.NacosConfig.Group,
		})
		c.isListening = false
	}

	c.listeners = nil
	return nil
}

// buildConfigMapKeyFromConfigItem 用于 client 层
func buildConfigMapKeyFromConfigItem(item *ConfigItem) string {
	return item.NacosConfig.CacheKey
}
