package nacos

import (
	"time"
)

// ConfigFormat 配置文件格式枚举
type ConfigFormat string

const (
	FormatYAML       ConfigFormat = "yaml"
	FormatJSON       ConfigFormat = "json"
	FormatProperties ConfigFormat = "properties"
	FormatTOML       ConfigFormat = "toml"
	FormatXML        ConfigFormat = "xml"
)

// ConfigChangeEvent 配置变更事件
type ConfigChangeEvent struct {
	// 配置唯一标识
	ConfigKey string `json:"config_key"`
	// 数据ID
	DataID string `json:"data_id"`
	// 分组
	Group string `json:"group"`
	// 命名空间ID
	NamespaceID string `json:"namespace_id"`
	// 配置内容
	Content string `json:"content"`
	// 配置格式
	Format ConfigFormat `json:"format"`
	// 变更时间
	Timestamp time.Time `json:"timestamp"`
	// 变更类型
	ChangeType ChangeType `json:"change_type"`
}

// ChangeType 变更类型
type ChangeType string

const (
	ChangeTypeAdd    ChangeType = "ADD"
	ChangeTypeUpdate ChangeType = "UPDATE"
	ChangeTypeDelete ChangeType = "DELETE"
)

// ConfigListener 配置监听器接口
type ConfigListener interface {
	// OnConfigChange 配置变更回调
	OnConfigChange(event ConfigChangeEvent) error
	// OnError 错误回调
	OnError(configKey string, err error)
}

// ConfigListenerFunc 函数式监听器
type ConfigListenerFunc func(event ConfigChangeEvent) error

func (f ConfigListenerFunc) OnConfigChange(event ConfigChangeEvent) error {
	return f(event)
}

func (f ConfigListenerFunc) OnError(configKey string, err error) {
	// 默认不处理错误，用户可以自己实现
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	ConfigKey string    `json:"config_key"`
	Healthy   bool      `json:"healthy"`
	Message   string    `json:"message"`
	CheckTime time.Time `json:"check_time"`
}
