package nacos

import (
	"fmt"
	"sync"
)

var (
	// globalManager 全局管理器实例
	globalManager *Manager
	globalMutex   sync.RWMutex
)

// InitWithConfig 使用配置初始化全局nacos管理器，并自动启动监听
func InitWithConfig(config MultiNacosConfig) error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalManager != nil {
		return fmt.Errorf("全局管理器已初始化，请先调用Close()")
	}

	manager, err := NewManager(config)
	if err != nil {
		return err
	}

	globalManager = manager
	return nil
}

// GetGlobalManager 获取全局管理器实例
func GetGlobalManager() (*Manager, error) {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	if globalManager == nil {
		return nil, fmt.Errorf("全局管理器未初始化，请先调用Init方法")
	}

	return globalManager, nil
}

// AddGlobalListener 添加全局配置监听器（全局API）
func AddGlobalListener(listener ConfigListener) error {
	manager, err := GetGlobalManager()
	if err != nil {
		return err
	}

	manager.AddGlobalListener(listener)

	if err := manager.StartListening(); err != nil {
		return fmt.Errorf("启动监听失败: %v", err)
	}
	return nil
}

// GetConfig 获取指定配置的内容（全局API）
func GetConfig(configKey string) (string, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return "", err
	}

	return manager.GetConfig(configKey)
}

// GetAllConfigs 获取所有配置的内容（全局API）
func GetAllConfigs() (map[string]string, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return nil, err
	}

	return manager.GetAllConfigs()
}

// GetConfigByCacheKey 通过CacheKey获取配置内容（全局API，别名方法）
func GetConfigByCacheKey(cacheKey string) (string, error) {
	return GetConfig(cacheKey)
}

// GetHealthStatus 获取所有客户端的健康状态（全局API）
func GetHealthStatus() (map[string]HealthCheckResult, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return nil, err
	}

	return manager.GetHealthStatus(), nil
}

// GetClientKeys 获取所有客户端的配置键（全局API）
func GetClientKeys() ([]string, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return nil, err
	}

	return manager.GetClientKeys(), nil
}

// IsRunning 检查管理器是否正在运行（全局API）
func IsRunning() (bool, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return false, err
	}

	return manager.IsRunning(), nil
}

// Close 关闭全局管理器
func Close() error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalManager == nil {
		return nil
	}

	err := globalManager.Close()
	globalManager = nil
	return err
}
