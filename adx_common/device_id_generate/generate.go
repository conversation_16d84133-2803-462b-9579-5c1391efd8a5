package deviceidgenerate

import (
	"crypto/md5"
	"encoding/hex"
	"log"
	"sort"
	"strconv"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"
)

// GenerateDeviceID ...
// 返回: 16位md5 key
func GenerateDeviceID(devicemodel models.DeviceModel) string {
	tmpDeviceModel := devicemodel

	paramRepair(&tmpDeviceModel)

	tmpDIDKey := ""
	if tmpDeviceModel.Os == "android" {
		if len(tmpDeviceModel.OsVersion) > 0 {
			osvMajorStr := strings.Split(tmpDeviceModel.OsVersion, ".")[0]
			osvMajor := convertStringToInt(osvMajorStr)
			if osvMajor < 10 {
				if len(tmpDeviceModel.Imei) > 0 {
					tmpDIDKey = getMd5(tmpDeviceModel.Imei)
				} else if len(tmpDeviceModel.ImeiMd5) > 0 && tmpDeviceModel.ImeiMd5 != getMd5("") {
					tmpDIDKey = strings.ToLower(tmpDeviceModel.ImeiMd5)
				}
			} else {
				if len(tmpDeviceModel.Oaid) > 0 {
					tmpDIDKey = getMd5(tmpDeviceModel.Oaid)
				} else if len(tmpDeviceModel.OaidMd5) > 0 {
					tmpDIDKey = strings.ToLower(tmpDeviceModel.OaidMd5)
				}
			}
		} else {
			if len(tmpDeviceModel.Oaid) > 0 {
				tmpDIDKey = getMd5(tmpDeviceModel.Oaid)
			} else if len(tmpDeviceModel.OaidMd5) > 0 {
				tmpDIDKey = strings.ToLower(tmpDeviceModel.OaidMd5)
			} else if len(tmpDeviceModel.Imei) > 0 {
				tmpDIDKey = getMd5(tmpDeviceModel.Imei)
			} else if len(tmpDeviceModel.ImeiMd5) > 0 && tmpDeviceModel.ImeiMd5 != getMd5("") {
				tmpDIDKey = strings.ToLower(tmpDeviceModel.ImeiMd5)
			}
		}
	} else if tmpDeviceModel.Os == "ios" {
		if len(tmpDeviceModel.Idfa) > 0 {
			tmpDIDKey = getMd5(tmpDeviceModel.Idfa)
		} else if len(tmpDeviceModel.IdfaMd5) > 0 && tmpDeviceModel.IdfaMd5 != getMd5("") {
			tmpDIDKey = strings.ToLower(tmpDeviceModel.IdfaMd5)
		} else if len(tmpDeviceModel.CAIDMulti) > 0 {
			// caid multi
			tmpCAIDMulti := tmpDeviceModel.CAIDMulti
			sort.Sort(CAIDMultiSort(tmpCAIDMulti))
			tmpDIDKey = getMd5(tmpCAIDMulti[0].CAID + tmpCAIDMulti[0].CAIDVersion)
		} else if len(tmpDeviceModel.SystemUpdateSec) > 0 &&
			len(tmpDeviceModel.Country) > 0 && len(tmpDeviceModel.Language) > 0 && len(tmpDeviceModel.DeviceNameMd5) > 0 &&
			len(tmpDeviceModel.HardwareMachine) > 0 && len(tmpDeviceModel.PhysicalMemoryByte) > 0 && len(tmpDeviceModel.HarddiskSizeByte) > 0 &&
			len(tmpDeviceModel.TimeZone) > 0 && len(tmpDeviceModel.HardwareModel) > 0 {
			// yinzi
			tmpDIDKey = getMd5(tmpDeviceModel.SystemUpdateSec +
				tmpDeviceModel.Country + tmpDeviceModel.Language + tmpDeviceModel.DeviceNameMd5 +
				tmpDeviceModel.HardwareMachine + tmpDeviceModel.PhysicalMemoryByte + tmpDeviceModel.HarddiskSizeByte +
				tmpDeviceModel.TimeZone + tmpDeviceModel.HardwareModel)
		}
	}

	return get16Md5(tmpDIDKey)
}

type CAIDMultiSort []models.DeviceCAIDMultiModel

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return convertStringToInt(s[i].CAIDVersion) > convertStringToInt(s[j].CAIDVersion)
}

// getMd5 ...
func getMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// get16Md5 ...
func get16Md5(str string) string {
	return getMd5(str)[8:24]
}

// convertStringToInt ...
func convertStringToInt(str string) int {
	value, _ := strconv.Atoi(str)
	return value
}

func paramRepair(deviceInfo *models.DeviceModel) {
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = strings.Replace(deviceInfo.Imei, " ", "", -1)
		deviceInfo.ImeiMd5 = strings.ToLower(deviceInfo.ImeiMd5)
		deviceInfo.Oaid = strings.Replace(deviceInfo.Oaid, " ", "", -1)
		deviceInfo.OaidMd5 = strings.ToLower(deviceInfo.OaidMd5)

		if deviceInfo.ImeiMd5 == getMd5("") {
			deviceInfo.ImeiMd5 = ""
		}

		if deviceInfo.OaidMd5 == getMd5("") {
			deviceInfo.OaidMd5 = ""
		}

		osvMajor := 0
		if len(deviceInfo.OsVersion) > 0 {
			osvMajorStr := strings.Split(deviceInfo.OsVersion, ".")[0]
			osvMajor = convertStringToInt(osvMajorStr)
			// log.Println(osvMajor)
		}

		if strings.ToLower(deviceInfo.Imei) == "unknown" ||
			strings.ToLower(deviceInfo.Imei) == "null" ||
			len(deviceInfo.Imei) < 3 ||
			osvMajor >= 10 {
			deviceInfo.Imei = ""
		}

		if strings.Contains(strings.ToLower(deviceInfo.Oaid), "unknown") ||
			strings.Contains(strings.ToLower(deviceInfo.Oaid), "null") {
			deviceInfo.Oaid = ""
		}

		if len(deviceInfo.ImeiMd5) != 32 ||
			osvMajor >= 10 {
			deviceInfo.ImeiMd5 = ""
		}

		if verifyDID(deviceInfo.Imei, "imei") {
		} else {
			deviceInfo.Imei = ""
		}
		if verifyDID(deviceInfo.ImeiMd5, "imei_md5") {
		} else {
			deviceInfo.ImeiMd5 = ""
		}
		if verifyDID(deviceInfo.Oaid, "oaid") {
		} else {
			deviceInfo.Oaid = ""
		}
		if verifyDID(deviceInfo.OaidMd5, "oaid_md5") {
		} else {
			deviceInfo.OaidMd5 = ""
		}
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = strings.Replace(deviceInfo.Idfa, " ", "", -1)

		if len(deviceInfo.Idfa) > 0 {
			idfaRn := []rune(deviceInfo.Idfa)
			if len(deviceInfo.Idfa) != 36 || string(idfaRn[8]) != "-" || string(idfaRn[13]) != "-" || string(idfaRn[18]) != "-" || string(idfaRn[23]) != "-" {
				deviceInfo.Idfa = ""
			}
		}

		if deviceInfo.IdfaMd5 == getMd5("") {
			deviceInfo.IdfaMd5 = ""
		}

		if verifyDID(deviceInfo.Idfa, "idfa") {
		} else {
			deviceInfo.Idfa = ""
		}

		if verifyDID(deviceInfo.IdfaMd5, "idfa_md5") {
		} else {
			deviceInfo.IdfaMd5 = ""
		}
	}
}

func verifyDID(did string, didType string) bool {

	maxLength := (len(did) + 1) / 2
	// log.Println(maxLength)
	for i := 0; i < len(did); i++ {
		if strings.Count(did, string(did[i])) >= int(maxLength) {
			log.Printf("wrong did value: %v, wrong char: %v, did type: %v\n", did, string(did[i]), didType)
			return false
		}
	}

	return true
}
