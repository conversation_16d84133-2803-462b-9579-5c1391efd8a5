package monitor

// 监控所有 API 的 QPS 和耗时
import (
	"errors"
	"fmt"
	"math/rand/v2"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
	APIQPS = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "api_qps_total",
			Help: "QPS of all API endpoints.",
		},
		[]string{"service", "method", "path", "status_code"},
	)

	APIDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "api_duration_seconds",
			Help:    "Duration monitoring for all API endpoints.",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"service", "method", "path", "status_code"},
	)
)

var serviceName string
var monitorRatio float64

// Init 初始化 prometheus 监控  ，输入监控比例
func Init(service string, needMonitorRatio float64) error {
	if needMonitorRatio <= 0 || needMonitorRatio > 1 {
		return errors.New("needMonitorRatio must be between 0 and 1")
	}
	serviceName = service
	monitorRatio = needMonitorRatio
	prometheus.MustRegister(APIQPS)
	prometheus.MustRegister(APIDuration)

	// 注册函数监控指标
	RegisterFunctionMetrics()

	return nil
}

// Gin中间件，监控所有 API
func PrometheusAPIMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		method := c.Request.Method
		path := c.FullPath()
		statusCode := c.Writer.Status()
		if path != "" && rand.Float64() <= monitorRatio {
			APIQPS.WithLabelValues(serviceName, method, path, intToString(statusCode)).Inc()
			APIDuration.WithLabelValues(serviceName, method, path, intToString(statusCode)).Observe(time.Since(start).Seconds())
		}
	}
}

func intToString(i int) string {
	return fmt.Sprintf("%d", i)
}

// 暴露 metrics 路由
func PrometheusHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		promhttp.Handler().ServeHTTP(c.Writer, c.Request)
	}
}
