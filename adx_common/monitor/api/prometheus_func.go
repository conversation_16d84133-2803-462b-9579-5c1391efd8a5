package monitor

// 监控特定函数的 QPS 和耗时分布
import (
	"time"

	"math/rand/v2"

	"github.com/prometheus/client_golang/prometheus"
)

// FunctionMetrics 用于监控特定函数的 QPS 和耗时分布
var functionQPS = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "function_qps_total",
		Help: "QPS of monitored functions.",
	},
	[]string{"service", "func"},
)

var functionDuration = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "function_duration_seconds",
		Help:    "Duration of monitored functions.",
		Buckets: prometheus.DefBuckets, // 默认的 5 个桶，0.005, 0.01, 0.025, 0.05, 0.1
	},
	[]string{"service", "func"},
)

// RegisterFunctionMetrics 注册函数监控指标
func RegisterFunctionMetrics() {
	prometheus.MustRegister(functionQPS)
	prometheus.MustRegister(functionDuration)
}

// MonitorFunc 用于包裹需要监控的函数
func MonitorFunc(service, funcName string, f func()) func() {
	return func() {
		start := time.Now()
		f()
		elapsed := time.Since(start).Seconds()
		functionQPS.WithLabelValues(service, funcName).Inc()
		functionDuration.WithLabelValues(service, funcName).Observe(elapsed)
	}
}

// ReportFunctionMetrics 业务层调用：上报函数 QPS 和耗时，受全局采样率 monitorRatio 控制
func ReportFunctionMetrics(service, funcName string, durationSeconds float64) {
	if rand.Float64() < monitorRatio {
		functionQPS.WithLabelValues(service, funcName).Inc()
		functionDuration.WithLabelValues(service, funcName).Observe(durationSeconds)
	}
}
