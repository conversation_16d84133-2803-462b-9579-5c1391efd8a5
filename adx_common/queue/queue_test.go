package queue

import (
	"context"
	"log"
	"sync"
	"testing"
	"time"

	uuid "github.com/satori/go.uuid"
)

func TestQueue(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	queue := NewQueue[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 20*time.Second)
	defer cancel()

	go func() {
		time.Sleep(40 * time.Second)
		cancel()
	}()

	bufferLimit := 100000
	fetchOnceLimit := 10000
	handleLimt := 10000
	fetchSqlOffset := 0
	var mux sync.Mutex

	batchFinished := false

	batchFunc := BatchFunc[TestData](
		func(
			batchContext context.Context,
			dataPtr *[]TestData,
			closeCallbackFunc CloseCallbackFunc,
		) (
			finish bool,
			err error,
		) {
			if fetchSqlOffset >= 200000 {
				finish = true
				batchFinished = true
				log.Println("fetchSqlOffset finish")
				//closeCallbackFunc(batchContext)
				return
			}

			mux.Lock()
			if len(*dataPtr) <= bufferLimit-fetchOnceLimit {

				fetchSqlLimit := fetchOnceLimit
				for i := 0; i < fetchOnceLimit; i++ {
					*dataPtr = append(*dataPtr, TestData{Id: uuid.NewV4().String(), Value: i})
				}
				//time.Sleep(1 * time.Second)

				fetchSqlOffset = fetchSqlOffset + fetchSqlLimit
				log.Println(len(*dataPtr), fetchSqlOffset)
			}
			mux.Unlock()

			return
		})
	queue.SetBatchFunc(&batchFunc)

	handleFunc := HandleFunc[TestData](
		func(
			handleContext context.Context,
			dataPtr *[]TestData,
			closeCallbackFunc CloseCallbackFunc,
		) (
			finish bool,
			err error,
		) {

			//log.Println(len(*dataPtr), "HandleFunc")
			if len(*dataPtr) > 0 {
				mux.Lock()

				if len(*dataPtr) > handleLimt {
					dataToHandle := (*dataPtr)[:handleLimt]
					*dataPtr = (*dataPtr)[handleLimt:]
					log.Println("len(dataToHandle):", len(dataToHandle))
				} else {
					dataToHandle := (*dataPtr)[:len(*dataPtr)]
					*dataPtr = []TestData{}
					log.Println("len(dataToHandle):", len(dataToHandle), " finish")
					//closeCallbackFunc(handleContext)
					if batchFinished {
						finish = true
					}
				}
				mux.Unlock()
			}
			return
		})
	queue.SetHandleFunc(&handleFunc)

	err := queue.Dispatch(ctxTimeout)
	log.Println(err, ctxTimeout.Err())

}
