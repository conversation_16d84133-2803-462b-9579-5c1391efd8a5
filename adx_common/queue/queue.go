package queue

import (
	"context"
	"errors"
)

type BatchFunc[T any] func(
	batchContext context.Context,
	dataPtr *[]T,
	closeCallbackFunc CloseCallbackFunc,
) (
	finish bool,
	err error,
)

type HandleFunc[T any] func(
	handleContext context.Context,
	dataPtr *[]T,
	closeCallbackFunc CloseCallbackFunc,
) (
	finish bool,
	err error,
)

type CloseCallbackFunc func(ctx context.Context)

type Queue[T any] struct {
	batchFunc  *BatchFunc[T]
	handleFunc *HandleFunc[T]
	data       *[]T
}

func NewQueue[T any]() *Queue[T] {
	return &Queue[T]{
		batchFunc:  nil,
		handleFunc: nil,
		data:       nil,
	}
}

func (q *Queue[T]) SetBatchFunc(batchFunc *BatchFunc[T]) {
	q.batchFunc = batchFunc
}

func (q *Queue[T]) SetHandleFunc(handleFunc *HandleFunc[T]) {
	q.handleFunc = handleFunc
}

func (q *Queue[T]) Dispatch(
	ctx context.Context,
) (
	err error,
) {
	if q.batchFunc == nil {
		err = errors.New("batchFunc is nil")
		return
	}

	if q.handleFunc == nil {
		err = errors.New("handleFunc is nil")
		return
	}

	data := []T{}
	batchChan := make(chan bool, 1)
	handleChan := make(chan bool, 1)
	cancelChan := make(chan bool, 10)

	defer func() {
		data = []T{}
		close(batchChan)
		close(handleChan)
		close(cancelChan)
	}()

	closeCallbackFunc := CloseCallbackFunc(func(ctx context.Context) {
		cancelChan <- true
	})

	batchChan <- true
	handleChan <- true

	for {
		select {
		case <-ctx.Done():
			return
		case batch := <-batchChan:
			if !batch {
				continue
			}
			finish, err := (*q.batchFunc)(ctx, &data, closeCallbackFunc)
			if err != nil || finish {
				if batch {
					batchChan <- false
				}
				continue
			} else {
				batchChan <- true
			}
		case handle := <-handleChan:
			if !handle {
				continue
			}
			finish, err := (*q.handleFunc)(ctx, &data, closeCallbackFunc)
			if err != nil || finish {
				if handle {
					handleChan <- false
				}
				continue
			} else {
				handleChan <- true
			}
		case <-cancelChan:
			return
		default:
			return
		}
	}
}
