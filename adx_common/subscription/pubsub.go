package subscription

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/google/uuid"
)

type User struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}
type Data struct {
	Id        string `json:"id"`
	Title     string `json:"title"`
	Type      string `json:"type"`
	Receivers []User `json:"receivers"`
	Content   string `json:"content"`
}
type AdxNotificationStruct struct {
	Data    Data   `json:"data"`
	Error   int    `json:"error"`
	Message string `json:"message"`
}

func CallWebhook(path string, data interface{}) (err error) {
	gatewayEndpoint := utilities.GetEnv(
		"ADX_MANAGE_GATEWAY_ENDPOINT",
		"http://adx-manage-gateway-svc:4000",
	)

	jsonData, err := json.Marshal(data)
	if err != nil {
		return
	}

	payload := strings.NewReader(string(jsonData))
	urlString := fmt.Sprintf("%s/%s", gatewayEndpoint, path)

	httpRequest, err := http.NewRequest("POST", urlString, payload)
	httpRequest.Header.Add("Content-Type", "application/json")
	if err != nil {
		return err
	}

	client := &http.Client{Timeout: time.Minute}

	response, err := client.Do(httpRequest)
	if err != nil {
		return err
	}

	defer response.Body.Close()

	if response.StatusCode != http.StatusNoContent {
		return fmt.Errorf("bad request, error code %d", response.StatusCode)
	}

	return
}

func SendSystemMessage(title string, content string) (err error) {
	data := AdxNotificationStruct{
		Data: Data{
			Id:      uuid.NewString(),
			Title:   title,
			Type:    "message",
			Content: content,
			Receivers: []User{
				{Id: "", Name: "ALL"},
			},
		},
		Error:   0,
		Message: "success",
	}
	err = CallWebhook("subscriptions/adx/notification", data)

	return
}
