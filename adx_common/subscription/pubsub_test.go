package subscription

import (
	"log"
	"os"
	"testing"
)

func TestCallWebhook(t *testing.T) {
	type Data struct {
		Data int `json:"data"`
	}
	type MemoryDataStruct struct {
		Data    Data   `json:"data"`
		Error   int    `json:"error"`
		Message string `json:"message"`
	}

	os.Setenv("ADX_MANAGE_GATEWAY_ENDPOINT", "http://localhost:4000")

	data := MemoryDataStruct{
		Data: Data{
			Data: 88888,
		},
		Error:   0,
		Message: "success",
	}

	/*
	   Gateway 配置
	   - type: Subscription
	     field: memoryData
	     pubsubTopic: webhook:post:/webhooks/memory-data
	     responseSample: ./apps/ssp_heatmap_core/samples/heatmap/memoryData.response.json
	*/

	err := CallWebhook("webhooks/memory-data", data)

	log.Println(err)
}
