# Adx_common

## Adx 命名统一

| 中         | En         |
| ---------- | ---------- |
| 上游       | demand     |
| 下游       | supply     |
| 平台       | platform   |
| 主体       | corp       |
| 上游媒体   | demand_app |
| 上游广告位 | demand_pos |
| 下游媒体   | supply_app |
| 下游广告位 | supply_pos |

## 关于 demand_app/demand_pos 的 raw_id

    demand_app和demand_pos 自动生成唯一ID
    demand_app和demand_pos 由上游提供的id存在raw_id字段里

## SSP ID API

```shell

#上游全部平台列表
/ssp/demand/platform/list

platform_table
{
    platform_id
    platform_name
    ...
}

#上游全部主体列表
/ssp/demand/corp/list

corp_table
{
    corp_id
    corp_name
    ...
}

#上游全部媒体列表
/ssp/demand/demand_app/list

demand_app_table
{
    demand_app_id
    demand_app_name
    platform_id
    corp_id
    raw_demand_app_id
    ...
}

#上游全部广告位列表
/ssp/demand/demand_pos/list

demand_pos_table
{
    demand_pos_id
    demand_pos_name
    demand_app_id
    raw_demand_pos_id
    ...
}


# 下游全部媒体列表
/ssp/supply/supply_app/list
supply_app_table
{
    supply_app_id
    supply_app_name
    ...
}

# 下游全部广告位列表
/ssp/supply/supply_pos/list
supply_pos_table
{
    supply_pos_id
    supply_pos_name
    supply_app_id
    ...
}
```
