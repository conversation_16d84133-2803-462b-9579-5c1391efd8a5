package collector

import (
	"context"
	"errors"
	"log"
	"sync"
	"time"
)

// 读取/处理异步采集器会话
type CollectorSession[T any] struct {
	fetchDataFunc               *FetchDataFunc[T]
	handleDataFunc              *HandleDataFunc[T]
	bufferLimitCallbackFunc     *BufferLimitCallbackFunc
	handleDataEmptyCallbackFunc *HandleDataEmptyCallbackFunc
	finishCallbackFunc          *FinishCallbackFunc

	wg sync.WaitGroup
}

type CollectorSessionBuffer[T any] struct {
	Data                 *[]T
	IsFetchDataFinished  bool
	IsHandleDataFinished bool
	FetchSqlOffset       int
}

// 读取操作方法
// 自定义读取数据的方法
// fetchContext 传递的context
// fetchOnceLimit 每次读数据的limit
// fetchSqlOffset 每次取数据库Sql的offset
// closeCallbackFunc 手动终止采集
//
// items 返回的数据数组，一旦items为空，fetchData终止
// fetchSqlLimit 该值为每次循环fetchSqlOffset的增加量, 例如: 从n(n>=1)条sql拼一个数组返回, fetchSqlLimit = fetchOnceLimit / n
// err 返回错误信息
type FetchDataFunc[T any] func(
	fetchContext context.Context,
	fetchOnceLimit int,
	fetchSqlOffset int,
	closeCallbackFunc CloseCallbackFunc,
) (
	items []T,
	fetchSqlLimit int,
	err error,
)

// 处理操作方法
// 自定义处理数据的方法
// handleContext 传递的context
// items 需要处理的数据
// closeCallbackFunc 手动终止采集
//
// err 返回错误信息
type HandleDataFunc[T any] func(
	handleContext context.Context,
	items []T,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 缓存区数据饱和后回调方法
// bufferLimitContext 传递的context
// closeCallbackFunc 手动终止采集
type BufferLimitCallbackFunc func(
	bufferLimitContext context.Context,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 待处理数据区为空回调方法
// handleDataEmptyContext 传递的context
// closeCallbackFunc 手动终止采集
type HandleDataEmptyCallbackFunc func(
	handleDataEmptyContext context.Context,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 会话处理结束回调方法
type FinishCallbackFunc func(finishContext context.Context) (err error)

// 关闭会话的回调方法
type CloseCallbackFunc func(ctx context.Context)

// T: 泛型,数据结构体的类名
// bufferLimit: 总数据条数
// fetchOnceLimit: 每次取数据条数
// handleLimt: 每次处理数据条数
func NewCollectorSession[T any]() *CollectorSession[T] {

	return &CollectorSession[T]{
		fetchDataFunc:               nil,
		handleDataFunc:              nil,
		finishCallbackFunc:          nil,
		bufferLimitCallbackFunc:     nil,
		handleDataEmptyCallbackFunc: nil,
	}
}

func (c *CollectorSession[T]) SetFetchDataFunc(fetchDataFunc *FetchDataFunc[T]) {
	c.fetchDataFunc = fetchDataFunc
}

func (c *CollectorSession[T]) SetHandleDataFunc(handleDataFunc *HandleDataFunc[T]) {
	c.handleDataFunc = handleDataFunc
}

func (c *CollectorSession[T]) SetBufferLimitCallbackFunc(bufferLimitCallbackFunc *BufferLimitCallbackFunc) {
	c.bufferLimitCallbackFunc = bufferLimitCallbackFunc
}

func (c *CollectorSession[T]) SetHandleDataEmptyCallbackFunc(handleDataEmptyCallbackFunc *HandleDataEmptyCallbackFunc) {
	c.handleDataEmptyCallbackFunc = handleDataEmptyCallbackFunc
}

func (c *CollectorSession[T]) SetFinishCallbackFunc(finishCallbackFunc *FinishCallbackFunc) {
	c.finishCallbackFunc = finishCallbackFunc
}

func (c *CollectorSession[T]) Run(
	ctx context.Context,
	bufferLimit int,
	fetchOnceLimit int,
	handleLimt int,
) (
	err error,
) {

	if fetchOnceLimit > bufferLimit {
		fetchOnceLimit = bufferLimit
	}

	// init
	data := []T{}
	bufferData := CollectorSessionBuffer[T]{
		Data:                 &data,
		IsFetchDataFinished:  false,
		IsHandleDataFinished: false,
		FetchSqlOffset:       0,
	}

	bufferDataChan := make(chan *CollectorSessionBuffer[T], 4)
	isFetchDataClosedChan := make(chan bool, 2)
	isHandleDataClosedChan := make(chan bool, 2)

	defer func() {
		if c.finishCallbackFunc != nil {
			(*c.finishCallbackFunc)(ctx)
		}
		bufferData.Data = nil
		close(bufferDataChan)
		close(isFetchDataClosedChan)
		close(isHandleDataClosedChan)
	}()

	bufferDataChan <- &bufferData

	closeCallbackFunc := CloseCallbackFunc(func(ctx context.Context) {
		select {
		case _, ok := <-isFetchDataClosedChan:
			if ok {
				isFetchDataClosedChan <- true
			}
		default:
			isFetchDataClosedChan <- true
		}

		select {
		case _, ok := <-isHandleDataClosedChan:
			if ok {
				isHandleDataClosedChan <- true
			}
		default:
			isHandleDataClosedChan <- true
		}
	})

	// fetch & handle
	c.wg.Add(2)
	go func() {
		if innerErr := c.FetchData(ctx, bufferDataChan, isFetchDataClosedChan, bufferLimit, fetchOnceLimit, closeCallbackFunc); innerErr != nil {
			log.Println("FetchData err:", err)
			err = errors.Join(err, innerErr)
		}
	}()

	go func() {
		time.Sleep(time.Second * 2)
		if innerErr := c.HandleData(ctx, bufferDataChan, isHandleDataClosedChan, handleLimt, closeCallbackFunc); innerErr != nil {
			log.Println("HandleData err:", err)
			err = errors.Join(err, innerErr)
		}
	}()
	c.wg.Wait()

	return
}

func (c *CollectorSession[T]) FetchData(
	ctx context.Context,
	bufferDataChan chan *CollectorSessionBuffer[T],
	isFetchDataClosedChan chan bool,
	bufferLimit int,
	fetchOnceLimit int,
	closeCallbackFunc CloseCallbackFunc,
) (err error) {
	if c.fetchDataFunc == nil {
		return errors.New("fetchDataFunc is nil")
	}
	for {
		select {
		case <-isFetchDataClosedChan:
			c.wg.Done()
			return
		case <-ctx.Done():
			c.wg.Done()
			return
		case bufferData, ok := <-bufferDataChan:
			select {
			case <-isFetchDataClosedChan:
				c.wg.Done()
				return
			case <-ctx.Done():
				c.wg.Done()
				return
			default:
				if ok {
					if bufferData.IsFetchDataFinished {
						bufferDataChan <- bufferData

						c.wg.Done()
						return
					}

					if len(*bufferData.Data) <= bufferLimit-fetchOnceLimit {
						if items, fetchSqlLimit, innerErr := (*c.fetchDataFunc)(ctx, fetchOnceLimit, bufferData.FetchSqlOffset, closeCallbackFunc); innerErr == nil {
							if len(items) == 0 {
								bufferData.IsFetchDataFinished = true
							} else {
								if bufferData.Data != nil {
									*bufferData.Data = append(*bufferData.Data, items...)
									bufferData.IsHandleDataFinished = false
									bufferData.FetchSqlOffset = bufferData.FetchSqlOffset + fetchSqlLimit
								}
							}
						} else {
							bufferData.IsFetchDataFinished = true
							err = innerErr
						}
					} else if c.bufferLimitCallbackFunc != nil {
						(*c.bufferLimitCallbackFunc)(ctx, closeCallbackFunc)
					}

					bufferDataChan <- bufferData

					if bufferData.IsFetchDataFinished && bufferData.IsHandleDataFinished {
						c.wg.Done()
						return
					}
				} else {
					c.wg.Done()
					return
				}
			}
		}
	}
}

func (c *CollectorSession[T]) HandleData(
	ctx context.Context,
	bufferDataChan chan *CollectorSessionBuffer[T],
	isHandleDataClosedChan chan bool,
	handleLimt int,
	closeCallbackFunc CloseCallbackFunc,
) (err error) {
	if c.handleDataFunc == nil {
		return errors.New("handleDataFunc is nil")
	}

	for {
		select {
		case <-isHandleDataClosedChan:
			c.wg.Done()
			return
		case <-ctx.Done():
			c.wg.Done()
			return
		case bufferData, ok := <-bufferDataChan:
			select {
			case <-isHandleDataClosedChan:
				c.wg.Done()
				return
			case <-ctx.Done():
				c.wg.Done()
				return
			default:
				if ok {
					if bufferData.IsHandleDataFinished {
						bufferDataChan <- bufferData
						c.wg.Done()

						return
					}

					if len(*bufferData.Data) > 0 {
						if len(*bufferData.Data) > handleLimt {
							dataToHandle := (*bufferData.Data)[:handleLimt]
							*bufferData.Data = (*bufferData.Data)[handleLimt:]
							bufferDataChan <- bufferData

							if err = (*c.handleDataFunc)(ctx, dataToHandle, closeCallbackFunc); err != nil {
								log.Println("handleDataFunc error: ", err)
							}
						} else {
							dataToHandle := (*bufferData.Data)[:len(*bufferData.Data)]
							*bufferData.Data = []T{}
							bufferData.IsHandleDataFinished = true
							bufferDataChan <- bufferData

							if err = (*c.handleDataFunc)(ctx, dataToHandle, closeCallbackFunc); err != nil {
								log.Println("handleDataFunc error: ", err)
							}

						}
					} else {
						if c.handleDataEmptyCallbackFunc != nil {
							(*c.handleDataEmptyCallbackFunc)(ctx, closeCallbackFunc)
						}

						if bufferData.IsFetchDataFinished {
							bufferData.IsHandleDataFinished = true
							bufferDataChan <- bufferData
						}
					}

					if bufferData.IsFetchDataFinished && bufferData.IsHandleDataFinished {
						c.wg.Done()
						return
					}
				} else {
					c.wg.Done()
					return
				}
			}
		}
	}
}
