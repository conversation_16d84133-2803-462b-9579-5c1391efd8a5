package collector

import (
	"fmt"
	"time"
)

// CollectorError 定义采集器错误接口
type CollectorError interface {
	error
	Code() ErrorCode
	Retryable() bool
}

// ErrorCode 错误码类型
type ErrorCode int

const (
	// ErrCodeUnknown 未知错误
	ErrCodeUnknown ErrorCode = iota
	// ErrCodeFetchFuncNil 数据获取函数未设置
	ErrCodeFetchFuncNil
	// ErrCodeHandleFuncNil 数据处理函数未设置
	ErrCodeHandleFuncNil
	// ErrCodeFetchData 数据获取错误
	ErrCodeFetchData
	// ErrCodeHandleData 数据处理错误
	ErrCodeHandleData
	// ErrCodeBufferFull 缓冲区已满
	ErrCodeBufferFull
	// ErrCodeContextCanceled 上下文取消
	ErrCodeContextCanceled
)

// baseError 基础错误类型
type baseError struct {
	code      ErrorCode
	msg       string
	retryable bool
	time      time.Time
}

func (e *baseError) Error() string {
	return fmt.Sprintf("[%s] Code: %d, Message: %s", e.time.Format(time.RFC3339), e.code, e.msg)
}

func (e *baseError) Code() ErrorCode {
	return e.code
}

func (e *baseError) Retryable() bool {
	return e.retryable
}

// NewError 创建新的错误
func NewError(code ErrorCode, msg string, retryable bool) CollectorError {
	return &baseError{
		code:      code,
		msg:       msg,
		retryable: retryable,
		time:      time.Now(),
	}
}

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error) bool {
	if ce, ok := err.(CollectorError); ok {
		return ce.Retryable()
	}
	return false
}

// RetryWithBackoff 实现退避重试机制
func RetryWithBackoff(maxRetries int, initialDelay time.Duration, fn func() error) error {
	var err error
	delay := initialDelay

	for i := 0; i < maxRetries; i++ {
		err = fn()
		if err == nil {
			return nil
		}

		if !IsRetryableError(err) {
			return err
		}

		if i < maxRetries-1 {
			time.Sleep(delay)
			delay *= 2 // 指数退避
		}
	}

	return fmt.Errorf("达到最大重试次数 %d: %w", maxRetries, err)
}
