package collector

import (
	"context"
	"errors"
	"log"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

func TestCollectorSessionRunWithCancel(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(3 * time.Second)
		cancel()
	}()

	total := 100000

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= total {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})
	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()

		log.Println("handleData:", len(items))
		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000, 3)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionRunWithTimeout(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	go func() {
		time.Sleep(30 * time.Second)
		cancel()
	}()

	total := 100000

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= total {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})
	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()

		log.Println(len(items))
		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000, 3)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionWithClose(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(40 * time.Second)
		cancel()
	}()

	handledTotal := 0

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()
		var sesstionMu sync.Mutex
		for i := 0; i < len(items); i++ {
			sesstionMu.Lock()
			if handledTotal >= 100000 {
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)

				log.Println("handledTotal is", handledTotal)
				return
			}

			handledTotal = handledTotal + 1
			sesstionMu.Unlock()
		}

		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000, 3)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionRun(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(40 * time.Second)
		cancel()
	}()

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= 1000000 {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()
		log.Println("单次处理条数: ", len(items))

		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 1000000, 50000, 20000, 3)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionWithEmptyData(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 测试空数据场景
	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			return nil, 0, nil
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		t.Error("不应该调用 handleDataFunc")
		return nil
	})

	emptyDataCalled := false
	handleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		emptyDataCalled = true
		return nil
	})

	finishCalled := false
	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		finishCalled = true
		return nil
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	collector.Run(ctxTimeout, 1000, 100, 50, 3)

	if !emptyDataCalled {
		t.Error("应该调用 HandleDataEmptyCallbackFunc")
	}
	if !finishCalled {
		t.Error("应该调用 FinishCallbackFunc")
	}
}

func TestCollectorSessionWithError(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 测试 fetchDataFunc 返回错误
	fetchError := errors.New("fetch error")
	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			return nil, 0, fetchError
		})

	// 测试 handleDataFunc 返回错误
	handleError := errors.New("handle error")
	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		return handleError
	})

	finishCalled := false
	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		finishCalled = true
		return nil
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	err := collector.Run(ctxTimeout, 1000, 100, 50, 3)
	if err == nil {
		t.Error("应该返回错误")
	}
	if !finishCalled {
		t.Error("即使发生错误也应该调用 FinishCallbackFunc")
	}
}

func TestCollectorSessionConcurrent(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 使用原子操作记录处理的数据量
	var processedCount atomic.Int64
	// 记录缓冲区满的次数
	var bufferFullCount atomic.Int64

	// 设置期望处理的数据总量
	totalData := 1000000

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			if fetchSqlOffset >= totalData {
				return nil, 0, nil
			}

			// 优化批量查询逻辑
			remaining := totalData - fetchSqlOffset
			batchSize := fetchOnceLimit
			if remaining < batchSize {
				batchSize = remaining
			}

			for i := 0; i < batchSize; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: fetchSqlOffset + i})
			}
			fetchSqlLimit = batchSize
			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		processedCount.Add(int64(len(items)))
		log.Printf("处理数据批次大小: %d", len(items))
		return nil
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		bufferFullCount.Add(1)
		return nil
	})

	finishCalled := false
	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		finishCalled = true
		return nil
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	// 记录开始时间
	start := time.Now()

	// 运行采集器
	err := collector.Run(ctxTimeout, 100000, 5000, 2000, 3)

	// 计算执行时间
	duration := time.Since(start)

	// 验证基本功能
	if !finishCalled {
		t.Error("FinishCallbackFunc未被调用")
	}

	// 验证处理数据量是否符合预期
	processedTotal := processedCount.Load()
	if processedTotal != int64(totalData) {
		t.Errorf("处理数据量不符合预期: 期望 %d, 实际 %d", totalData, processedTotal)
	}

	// 输出性能指标
	t.Logf("总处理数据量: %d", processedTotal)
	t.Logf("执行时间: %v", duration)
	t.Logf("平均处理速率: %.2f 条/秒", float64(processedTotal)/duration.Seconds())
	t.Logf("缓冲区满次数: %d", bufferFullCount.Load())

	// 获取采集器性能指标
	metrics := collector.metrics.GetMetrics()
	t.Logf("平均获取延迟: %.2f ms", metrics["avg_fetch_latency_ms"])
	t.Logf("平均处理延迟: %.2f ms", metrics["avg_handle_latency_ms"])
	t.Logf("最大缓冲区大小: %d", metrics["max_buffer_size"])
	t.Logf("错误次数: %d", metrics["total_errors"])

	// 验证错误处理
	if err != nil {
		t.Logf("采集器执行出错: %v", err)
	}
}

func TestCollectorSessionPerformance(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 调整数据量和处理参数
	totalData := 100000
	var processedCount atomic.Int64

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			if fetchSqlOffset >= totalData {
				return nil, 0, nil
			}

			// 优化批量查询逻辑
			remaining := totalData - fetchSqlOffset
			batchSize := fetchOnceLimit
			if remaining < batchSize {
				batchSize = remaining
			}

			for i := 0; i < batchSize; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: fetchSqlOffset + i})
			}
			fetchSqlLimit = batchSize
			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		// 减少处理延迟
		time.Sleep(time.Microsecond)
		processedCount.Add(int64(len(items)))
		return nil
	})

	bufferFullCount := 0
	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		bufferFullCount++
		return nil
	})

	start := time.Now()

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)

	// 调整缓冲区和批处理参数
	err := collector.Run(ctxTimeout, 10000, 1000, 500, 3)
	if err != nil {
		t.Errorf("性能测试执行出错: %v", err)
	}

	duration := time.Since(start)
	processedTotal := processedCount.Load()

	// 验证处理数据量是否符合预期
	if processedTotal != int64(totalData) {
		t.Errorf("处理数据量不符合预期: 期望 %d, 实际 %d", totalData, processedTotal)
	}

	// 输出性能指标
	t.Logf("总处理数据量: %d", processedTotal)
	t.Logf("处理耗时: %v", duration)
	t.Logf("平均处理速率: %.2f 条/秒", float64(processedTotal)/duration.Seconds())
	t.Logf("缓冲区满次数: %d", bufferFullCount)

	// 获取采集器性能指标
	metrics := collector.metrics.GetMetrics()
	t.Logf("平均获取延迟: %.2f ms", metrics["avg_fetch_latency_ms"])
	t.Logf("平均处理延迟: %.2f ms", metrics["avg_handle_latency_ms"])
	t.Logf("最大缓冲区大小: %d", metrics["max_buffer_size"])
	t.Logf("错误次数: %d", metrics["total_errors"])
	t.Logf("重试次数: %d", metrics["total_retries"])
}

func myFunc(a chan int) {
	go func() {
		//time.Sleep(10 * time.Second)
		a <- 1
	}()
}
func TestRoutine(t *testing.T) {

	a := make(chan int)

	// go func() {
	// 	time.Sleep(10 * time.Second)
	// 	a <- 1
	// }()
	go myFunc(a)
	for {
		select {
		case <-a:
		default:
		}
		utilities.PrintRuntimeInfoWithTag("init")
		time.Sleep(time.Second)
	}
}

func TestPrintRuntimeInfoWithTag(t *testing.T) {

	utilities.PrintRuntimeInfoWithTag("init")

}

func TestChan(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var wg sync.WaitGroup
	c := make(chan int, 3)
	wg.Add(3)

	c <- 0
	utilities.PrintRuntimeInfoWithTag("init")

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 1")
					c <- v + 1
				}
			}
		}
	}()

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 2")
					c <- v + 1
				}
			}
		}
	}()

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 3")
					c <- v + 1
				}
			}
		}
	}()

	wg.Wait()
	utilities.PrintRuntimeInfoWithTag("init")

}

func ErrReturnFuncDeepLevel() (value int, err error) {
	return 1, errors.New("deep level error")
}

func ErrReturnFunc1() (err error) {
	err = errors.New("error1")
	if v, err := ErrReturnFuncDeepLevel(); err == nil {
		log.Println(v)
	} else {
		log.Println(err)
	}

	return
}

func ErrReturnFunc2() (err error) {
	err = errors.New("error2")
	if v, innerErr := ErrReturnFuncDeepLevel(); innerErr == nil {
		log.Println(v)
	} else {
		log.Println(innerErr)

		err = innerErr
	}
	return
}

func TestErrorReturn(t *testing.T) {
	if err := ErrReturnFunc1(); err != nil {
		log.Println("ErrReturnFunc1 return:", err)
	}

	if err := ErrReturnFunc2(); err != nil {
		log.Println("ErrReturnFunc2 return:", err)
	}
}

func TestErrorJoin(t *testing.T) {
	err1 := errors.New("error1")

	err2 := errors.New("error2")

	err := errors.Join(err1, err2)

	log.Println(err)
}
