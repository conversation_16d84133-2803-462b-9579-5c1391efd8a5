package collector

import (
	"testing"
	"time"
)

func TestBufferManager(t *testing.T) {
	metrics := NewCollectorMetrics()

	// 测试基本初始化
	t.Run("Basic Initialization", func(t *testing.T) {
		bm := NewBufferManager(1000, 100, 10000, metrics)
		if bm.GetCurrentSize() != 1000 {
			t.Errorf("Expected initial size 1000, got %d", bm.GetCurrentSize())
		}
	})

	// 测试边界条件
	t.Run("Boundary Conditions", func(t *testing.T) {
		// 测试最小值边界
		bm := NewBufferManager(50, 100, 1000, metrics)
		if bm.GetCurrentSize() != 50 {
			t.Errorf("Expected size 50, got %d", bm.GetCurrentSize())
		}

		// 测试最大值边界
		bm = NewBufferManager(2000, 100, 1000, metrics)
		if bm.GetCurrentSize() != 2000 {
			t.<PERSON><PERSON><PERSON>("Expected size 2000, got %d", bm.GetCurrentSize())
		}
	})

	// 测试自动调整功能
	t.Run("Auto Adjustment", func(t *testing.T) {
		bm := NewBufferManager(1000, 100, 10000, metrics)

		// 模拟高内存使用率场景
		for i := 0; i < 1000000; i++ {
			_ = make([]byte, 1024) // 分配内存
		}

		bm.AdjustBufferSize()
		if bm.GetCurrentSize() > 1000 {
			t.Errorf("Expected size to decrease, got %d", bm.GetCurrentSize())
		}
	})

	// 测试并发安全性
	t.Run("Concurrency Safety", func(t *testing.T) {
		bm := NewBufferManager(1000, 100, 10000, metrics)
		go func() {
			for i := 0; i < 100; i++ {
				bm.AdjustBufferSize()
				time.Sleep(time.Millisecond)
			}
		}()

		for i := 0; i < 100; i++ {
			_ = bm.GetCurrentSize()
			time.Sleep(time.Millisecond)
		}
	})

	// 测试调整间隔
	t.Run("Adjustment Interval", func(t *testing.T) {
		bm := NewBufferManager(1000, 100, 10000, metrics)
		initialSize := bm.GetCurrentSize()

		// 连续调用应该因为间隔限制而不会改变大小
		bm.AdjustBufferSize()
		bm.AdjustBufferSize()

		if bm.GetCurrentSize() != initialSize {
			t.Errorf("Buffer size should not change within interval, got %d, expected %d",
				bm.GetCurrentSize(), initialSize)
		}
	})

	// 测试扩容和缩容检查函数
	t.Run("Growth and Shrink Check", func(t *testing.T) {
		bm := NewBufferManager(1000, 100, 10000, metrics)

		// 初始状态应该可以扩容
		if !bm.CanGrow() {
			t.Error("Should be able to grow initially")
		}

		// 分配大量内存后应该需要缩容
		data := make([][]byte, 1000)
		for i := 0; i < 1000; i++ {
			data[i] = make([]byte, 1024*1024) // 分配1MB
		}

		if !bm.ShouldShrink() {
			t.Error("Should need to shrink under high memory usage")
		}

		// 清理内存
		data = nil
	})
}
