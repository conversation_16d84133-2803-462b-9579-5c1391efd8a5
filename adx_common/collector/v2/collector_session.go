package collector

import (
	"context"
	"errors"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

// 读取/处理异步采集器会话
type CollectorSession[T any] struct {
	fetchDataFunc               *FetchDataFunc[T]
	handleDataFunc              *HandleDataFunc[T]
	bufferLimitCallbackFunc     *BufferLimitCallbackFunc
	handleDataEmptyCallbackFunc *HandleDataEmptyCallbackFunc
	finishCallbackFunc          *FinishCallbackFunc

	metrics       *CollectorMetrics
	bufferManager *BufferManager
	wg            sync.WaitGroup
	isClosed      atomic.Bool
}

type CollectorSessionBuffer[T any] struct {
	Data                 *[]T
	IsFetchDataFinished  bool
	IsHandleDataFinished bool
	FetchSqlOffset       int
}

// 读取操作方法
// 自定义读取数据的方法
// fetchContext 传递的context
// fetchOnceLimit 每次读数据的limit
// fetchSqlOffset 每次取数据库Sql的offset
// closeCallbackFunc 手动终止采集
//
// items 返回的数据数组，一旦items为空，fetchData终止
// fetchSqlLimit 该值为每次循环fetchSqlOffset的增加量, 例如: 从n(n>=1)条sql拼一个数组返回, fetchSqlLimit = fetchOnceLimit / n
// err 返回错误信息
type FetchDataFunc[T any] func(
	fetchContext context.Context,
	fetchOnceLimit int,
	fetchSqlOffset int,
	closeCallbackFunc CloseCallbackFunc,
) (
	items []T,
	fetchSqlLimit int,
	err error,
)

// 处理操作方法
// 自定义处理数据的方法
// handleContext 传递的context
// items 需要处理的数据
// closeCallbackFunc 手动终止采集
//
// err 返回错误信息
type HandleDataFunc[T any] func(
	handleContext context.Context,
	items []T,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 缓存区数据饱和后回调方法
// bufferLimitContext 传递的context
// closeCallbackFunc 手动终止采集
type BufferLimitCallbackFunc func(
	bufferLimitContext context.Context,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 待处理数据区为空回调方法
// handleDataEmptyContext 传递的context
// closeCallbackFunc 手动终止采集
type HandleDataEmptyCallbackFunc func(
	handleDataEmptyContext context.Context,
	closeCallbackFunc CloseCallbackFunc,
) (err error)

// 会话处理结束回调方法
type FinishCallbackFunc func(finishContext context.Context) (err error)

// 关闭会话的回调方法
type CloseCallbackFunc func(ctx context.Context)

// T: 泛型,数据结构体的类名
// bufferLimit: 总数据条数
// fetchOnceLimit: 每次取数据条数
// handleLimt: 每次处理数据条数
func NewCollectorSession[T any]() *CollectorSession[T] {
	metrics := NewCollectorMetrics()
	return &CollectorSession[T]{
		fetchDataFunc:               nil,
		handleDataFunc:              nil,
		finishCallbackFunc:          nil,
		bufferLimitCallbackFunc:     nil,
		handleDataEmptyCallbackFunc: nil,
		metrics:                     metrics,
		bufferManager:               nil,
	}
}

func (c *CollectorSession[T]) SetFetchDataFunc(fetchDataFunc *FetchDataFunc[T]) {
	c.fetchDataFunc = fetchDataFunc
}

func (c *CollectorSession[T]) SetHandleDataFunc(handleDataFunc *HandleDataFunc[T]) {
	c.handleDataFunc = handleDataFunc
}

func (c *CollectorSession[T]) SetBufferLimitCallbackFunc(bufferLimitCallbackFunc *BufferLimitCallbackFunc) {
	c.bufferLimitCallbackFunc = bufferLimitCallbackFunc
}

func (c *CollectorSession[T]) SetHandleDataEmptyCallbackFunc(handleDataEmptyCallbackFunc *HandleDataEmptyCallbackFunc) {
	c.handleDataEmptyCallbackFunc = handleDataEmptyCallbackFunc
}

func (c *CollectorSession[T]) SetFinishCallbackFunc(finishCallbackFunc *FinishCallbackFunc) {
	c.finishCallbackFunc = finishCallbackFunc
}

func (c *CollectorSession[T]) Run(
	ctx context.Context,
	bufferLimit int,
	fetchOnceLimit int,
	handleLimt int,
	maxRetries int,
) (
	err error,
) {
	// 重置关闭状态
	c.isClosed.Store(false)

	// 初始化缓冲区管理器
	if c.bufferManager == nil {
		c.bufferManager = NewBufferManager(int64(bufferLimit), int64(bufferLimit/10), int64(bufferLimit), c.metrics)
	}

	if fetchOnceLimit > bufferLimit {
		fetchOnceLimit = bufferLimit
	}

	// init
	data := []T{}
	bufferData := CollectorSessionBuffer[T]{
		Data:                 &data,
		IsFetchDataFinished:  false,
		IsHandleDataFinished: false,
		FetchSqlOffset:       0,
	}

	bufferDataChan := make(chan *CollectorSessionBuffer[T], 4)
	isFetchDataClosedChan := make(chan bool, 2)
	isHandleDataClosedChan := make(chan bool, 2)

	defer func() {
		if c.finishCallbackFunc != nil {
			(*c.finishCallbackFunc)(ctx)
		}
		bufferData.Data = nil
		close(bufferDataChan)
		close(isFetchDataClosedChan)
		close(isHandleDataClosedChan)

		c.isClosed.Store(true)

	}()

	bufferDataChan <- &bufferData

	closeCallbackFunc := CloseCallbackFunc(func(ctx context.Context) {
		select {
		case _, ok := <-isFetchDataClosedChan:
			if ok {
				isFetchDataClosedChan <- true
			}
		default:
			isFetchDataClosedChan <- true
		}

		select {
		case _, ok := <-isHandleDataClosedChan:
			if ok {
				isHandleDataClosedChan <- true
			}
		default:
			isHandleDataClosedChan <- true
		}
	})

	// fetch & handle
	c.wg.Add(2)
	go func() {
		if innerErr := RetryWithBackoff(maxRetries, time.Second, func() error {
			return c.FetchData(ctx, bufferDataChan, isFetchDataClosedChan, bufferLimit, fetchOnceLimit, closeCallbackFunc)
		}); innerErr != nil {
			log.Println("FetchData err:", innerErr)
			err = errors.Join(err, innerErr)
		}
	}()

	go func() {
		time.Sleep(time.Second * 2)
		if innerErr := RetryWithBackoff(maxRetries, time.Second, func() error {
			return c.HandleData(ctx, bufferDataChan, isHandleDataClosedChan, handleLimt, closeCallbackFunc)
		}); innerErr != nil {
			log.Println("HandleData err:", innerErr)
			err = errors.Join(err, innerErr)
		}
	}()
	c.wg.Wait()

	return
}

func (c *CollectorSession[T]) FetchData(
	ctx context.Context,
	bufferDataChan chan *CollectorSessionBuffer[T],
	isFetchDataClosedChan chan bool,
	bufferLimit int,
	fetchOnceLimit int,
	closeCallbackFunc CloseCallbackFunc,
) (err error) {
	// 定期调整缓冲区大小
	go func() {
		ticker := time.NewTicker(time.Second * 5)
		defer ticker.Stop()

		for !c.isClosed.Load() {
			select {
			case <-ticker.C:
				c.bufferManager.AdjustBufferSize()
			case <-ctx.Done():
				return
			}
		}
	}()
	if c.fetchDataFunc == nil {
		return NewError(ErrCodeFetchFuncNil, "fetchDataFunc is nil", false)
	}
	for {
		select {
		case <-isFetchDataClosedChan:
			c.wg.Done()
			return
		case <-ctx.Done():
			c.wg.Done()
			return
		case bufferData, ok := <-bufferDataChan:
			select {
			case <-isFetchDataClosedChan:
				c.wg.Done()
				return
			case <-ctx.Done():
				c.wg.Done()
				return
			default:
				if ok {
					if bufferData.IsFetchDataFinished {
						bufferDataChan <- bufferData

						c.wg.Done()
						return
					}

					// 更新当前缓冲区大小
					c.metrics.UpdateBufferSize(int64(len(*bufferData.Data)))

					// 检查是否需要调整缓冲区大小
					minFetchLimit := fetchOnceLimit / 4 // 最小不低于初始值的1/4
					maxFetchLimit := bufferLimit        // 最大不超过初始值的2倍

					if c.bufferManager.ShouldShrink() {
						newLimit := int(float64(fetchOnceLimit) * 0.8)
						if newLimit >= minFetchLimit {
							fetchOnceLimit = newLimit

							log.Printf("缓冲区已满，收缩至 %d（最小阈值：%d）\n", fetchOnceLimit, minFetchLimit)
						}
					} else if c.bufferManager.CanGrow() {
						// 根据当前缓冲区使用率计算增长系数
						growthFactor := 1.2
						bufferUsage := c.metrics.GetBufferUsage()
						if bufferUsage < 0.5 { // 提高使用率阈值，使扩容更加谨慎
							// 根据使用率动态计算增长系数，使其更平滑
							growthFactor = 1.2 + (0.5-bufferUsage)*0.6 // 最大增长系数为1.5
						}

						newLimit := int(float64(fetchOnceLimit) * growthFactor)
						if newLimit <= maxFetchLimit {
							fetchOnceLimit = newLimit

							log.Printf("缓冲区空闲，增长至 %d（最大阈值：%d）\n", fetchOnceLimit, maxFetchLimit)
						}
					}

					if len(*bufferData.Data) <= bufferLimit-fetchOnceLimit {
						start := time.Now()
						if items, fetchSqlLimit, innerErr := (*c.fetchDataFunc)(ctx, fetchOnceLimit, bufferData.FetchSqlOffset, closeCallbackFunc); innerErr == nil {
							// 记录获取数据的性能指标
							c.metrics.RecordFetch(len(items), time.Since(start))

							if len(items) == 0 {
								bufferData.IsFetchDataFinished = true
							} else {
								if bufferData.Data != nil {
									*bufferData.Data = append(*bufferData.Data, items...)
									bufferData.IsHandleDataFinished = false
									bufferData.FetchSqlOffset = bufferData.FetchSqlOffset + fetchSqlLimit
								}
							}
						} else {
							c.metrics.RecordError()
							bufferData.IsFetchDataFinished = true
							err = innerErr
						}
					} else if c.bufferLimitCallbackFunc != nil {
						c.metrics.RecordBufferFull()
						(*c.bufferLimitCallbackFunc)(ctx, closeCallbackFunc)
					}

					bufferDataChan <- bufferData

					if bufferData.IsFetchDataFinished && bufferData.IsHandleDataFinished {
						c.wg.Done()
						return
					}
				} else {
					c.wg.Done()
					return
				}
			}
		}
	}
}

func (c *CollectorSession[T]) HandleData(
	ctx context.Context,
	bufferDataChan chan *CollectorSessionBuffer[T],
	isHandleDataClosedChan chan bool,
	handleLimt int,
	closeCallbackFunc CloseCallbackFunc,
) (err error) {
	if c.handleDataFunc == nil {
		return NewError(ErrCodeHandleFuncNil, "handleDataFunc is nil", false)
	}

	for {
		select {
		case <-isHandleDataClosedChan:
			c.wg.Done()
			return
		case <-ctx.Done():
			c.wg.Done()
			return
		case bufferData, ok := <-bufferDataChan:
			select {
			case <-isHandleDataClosedChan:
				c.wg.Done()
				return
			case <-ctx.Done():
				c.wg.Done()
				return
			default:
				if ok {
					if bufferData.IsHandleDataFinished {
						bufferDataChan <- bufferData
						c.wg.Done()

						return
					}

					// 根据缓冲区状态调整处理批次大小
					if c.bufferManager.ShouldShrink() {
						handleLimt = int(float64(handleLimt) * 0.8)
					} else if c.bufferManager.CanGrow() {
						handleLimt = int(float64(handleLimt) * 1.2)
					}

					if len(*bufferData.Data) > 0 {
						if len(*bufferData.Data) > handleLimt {
							dataToHandle := (*bufferData.Data)[:handleLimt]
							*bufferData.Data = (*bufferData.Data)[handleLimt:]
							bufferDataChan <- bufferData

							start := time.Now()
							if err = (*c.handleDataFunc)(ctx, dataToHandle, closeCallbackFunc); err != nil {
								c.metrics.RecordError()
								if ce, ok := err.(CollectorError); ok {
									log.Printf("handleDataFunc error: Code=%d, Message=%s, Retryable=%v\n", ce.Code(), err.Error(), ce.Retryable())
								} else {
									log.Println("handleDataFunc error: ", err)
								}
							}
							c.metrics.RecordHandle(len(dataToHandle), time.Since(start))
						} else {
							dataToHandle := (*bufferData.Data)[:len(*bufferData.Data)]
							*bufferData.Data = []T{}
							bufferData.IsHandleDataFinished = true
							bufferDataChan <- bufferData

							start := time.Now()
							if err = (*c.handleDataFunc)(ctx, dataToHandle, closeCallbackFunc); err != nil {
								c.metrics.RecordError()
								if ce, ok := err.(CollectorError); ok {
									log.Printf("handleDataFunc error: Code=%d, Message=%s, Retryable=%v\n", ce.Code(), err.Error(), ce.Retryable())
								} else {
									log.Println("handleDataFunc error: ", err)
								}
							}
							c.metrics.RecordHandle(len(dataToHandle), time.Since(start))
						}
					} else {
						if c.handleDataEmptyCallbackFunc != nil {
							(*c.handleDataEmptyCallbackFunc)(ctx, closeCallbackFunc)
						}

						if bufferData.IsFetchDataFinished {
							bufferData.IsHandleDataFinished = true
							bufferDataChan <- bufferData
						}
					}

					if bufferData.IsFetchDataFinished && bufferData.IsHandleDataFinished {
						c.wg.Done()
						return
					}
				} else {
					c.wg.Done()
					return
				}
			}
		}
	}
}
