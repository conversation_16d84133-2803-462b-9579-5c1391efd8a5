package core

import (
	"bytes"
	"crypto/aes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"
)

// AES加密
func AESEncrypt(content, key string) (string, error) {
	keyBytes, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return "", fmt.Errorf("key decode error: %v", err)
	}

	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("cipher creation error: %v", err)
	}

	paddedContent := PKCS7Padding([]byte(content), block.BlockSize())
	ciphertext := make([]byte, len(paddedContent))
	for bs, be := 0, block.BlockSize(); bs < len(paddedContent); bs, be = bs+block.BlockSize(), be+block.BlockSize() {
		block.Encrypt(ciphertext[bs:be], paddedContent[bs:be])
	}

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// AES解密
func AESDecrypt(content, key string) (string, error) {
	keyBytes, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return "", fmt.Errorf("key decode error: %v", err)
	}

	ciphertext, err := base64.StdEncoding.DecodeString(content)
	if err != nil {
		return "", fmt.Errorf("content decode error: %v", err)
	}

	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("cipher creation error: %v", err)
	}

	plaintext := make([]byte, len(ciphertext))
	for bs, be := 0, block.BlockSize(); bs < len(ciphertext); bs, be = bs+block.BlockSize(), be+block.BlockSize() {
		block.Decrypt(plaintext[bs:be], ciphertext[bs:be])
	}

	plaintext = PKCS7UnPadding(plaintext)
	return string(plaintext), nil
}

// HMAC-SHA256签名
func HmacSHA256(data, key string) string {
	h := hmac.New(sha256.New, []byte(key))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// 参数排序
func SortKeyValue(params map[string]interface{}) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var builder strings.Builder
	first := true
	for _, key := range keys {
		if !first {
			builder.WriteString("&")
		}
		first = false

		builder.WriteString(key)
		builder.WriteString("=")

		value := params[key]
		if value != nil {
			builder.WriteString(fmt.Sprintf("%v", value))
		}
	}
	return builder.String()
}

// SHA256签名
func Sha256Sign(params map[string]interface{}, secret string) string {
	presignStr := SortKeyValue(params)
	return HmacSHA256(presignStr, secret)
}

// PKCS7填充
func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// PKCS7去除填充
func PKCS7UnPadding(data []byte) []byte {
	length := len(data)
	unPadding := int(data[length-1])
	return data[:(length - unPadding)]
}

// SignUtil Golang版本
type JdSignUtil struct{}

// Sha256Sign 生成签名
func (su *JdSignUtil) Sha256Sign(params map[string]interface{}, secret string) string {
	presignStr := su.SortKeyValue(params)
	return su.HmacSHA256(presignStr, secret)
}

// HmacSHA256 HMAC-SHA256计算
func (su *JdSignUtil) HmacSHA256(data, key string) string {
	h := hmac.New(sha256.New, []byte(key))
	h.Write([]byte(data))
	return su.BytesToHex(h.Sum(nil))
}

// BytesToHex 字节数组转十六进制字符串
func (su *JdSignUtil) BytesToHex(bytes []byte) string {
	hexString := make([]byte, hex.EncodedLen(len(bytes)))
	hex.Encode(hexString, bytes)
	return string(hexString)
}

// SortKeyValue 参数排序（保持与Java完全一致，使用%作为分隔符）
func (su *JdSignUtil) SortKeyValue(params map[string]interface{}) string {
	// 获取并排序keys
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var builder strings.Builder
	first := true
	for _, key := range keys {
		if !first {
			builder.WriteString("%") // 使用%作为分隔符，与Java一致
		}
		first = false

		builder.WriteString(key)
		builder.WriteString("=")

		value := params[key]
		valueString := ""
		if value != nil {
			valueString = fmt.Sprintf("%v", value) // 使用String.valueOf等效方式
		}
		builder.WriteString(valueString)
	}
	return builder.String()
}

// func main() {
// 	signUtil := JdSignUtil{}

// 	// 示例参数（与Java代码行为一致）
// 	params := map[string]interface{}{
// 		"name":  "John",
// 		"age":   30,
// 		"city":  "New York",
// 		"empty": nil,
// 	}
// 	secret := "mysecretkey"

// 	// 1. 测试参数排序
// 	sortedParams := signUtil.SortKeyValue(params)
// 	fmt.Println("排序后的参数字符串:", sortedParams)
// 	// 输出示例: age=30%city=New York%empty=%name=John

// 	// 2. 测试HMAC-SHA256
// 	hmacResult := signUtil.HmacSHA256("test data", secret)
// 	fmt.Println("HMAC-SHA256结果:", hmacResult)

// 	// 3. 测试完整签名流程
// 	signature := signUtil.Sha256Sign(params, secret)
// 	fmt.Println("最终签名结果:", signature)
// }
