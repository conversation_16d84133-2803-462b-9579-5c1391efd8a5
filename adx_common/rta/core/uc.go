package core

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
)

// IsUCRtaOK ...
// https://{{host}}/rta/{{channel}}/v1?channel={{channel}}
func IsUCRtaOK(c context.Context, bigdataUID string, deviceInfo *models.MHDeviceStu, ucRtaInfo *models.UCRtaStu) bool {

	defer func() {
		if err := recover(); err != nil {
			log.Printf("IsUCRtaOK recover error, bigdataUID=%v, err=%v\n", bigdataUID, err)
		}
	}()

	client := &http.Client{Timeout: time.Duration(200) * time.Millisecond}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	ucRtaReqStu := UCRtaReqStu{}

	// 参数验证
	paramRepair(deviceInfo)

	ucRtaReqStu.Channel = ucRtaInfo.UCRtaChannel

	isDeviceOK := false
	if deviceInfo.Os == "android" {
		ucRtaReqStu.Platform = "ANDROID"
		if len(deviceInfo.Oaid) > 0 {
			ucRtaReqStu.DIDType = "OAID"
			ucRtaReqStu.DID = deviceInfo.Oaid

			isDeviceOK = true
		} else if len(deviceInfo.OaidMd5) > 0 && deviceInfo.OaidMd5 != utils.GetMd5("") {
			ucRtaReqStu.DIDType = "OAID_MD5"
			ucRtaReqStu.DID = strings.ToLower(deviceInfo.OaidMd5)

			isDeviceOK = true
		} else if len(deviceInfo.Imei) > 0 {
			ucRtaReqStu.DIDType = "IMEI_MD5"
			ucRtaReqStu.DID = utils.GetMd5(deviceInfo.Imei)

			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			ucRtaReqStu.DIDType = "IMEI_MD5"
			ucRtaReqStu.DID = strings.ToLower(deviceInfo.ImeiMd5)

			isDeviceOK = true
		}

	} else if deviceInfo.Os == "ios" {
		ucRtaReqStu.Platform = "IOS"

		if len(deviceInfo.Idfa) > 0 {
			ucRtaReqStu.DIDType = "IDFA"
			ucRtaReqStu.DID = deviceInfo.Idfa

			isDeviceOK = true
		} else if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			ucRtaReqStu.DIDType = "IDFA_MD5"
			ucRtaReqStu.DID = strings.ToLower(deviceInfo.IdfaMd5)

			isDeviceOK = true
		}
	} else {
		return false
	}

	if isDeviceOK {
	} else {
		return false
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	ucRtaReqByte, _ := json.Marshal(ucRtaReqStu)
	// send req
	requestGet, err := http.NewRequest("POST", ucRtaInfo.UCRtaUrl, bytes.NewReader(ucRtaReqByte))
	if err != nil {
		log.Printf("uc_rta NewRequest failed, bigdataUID=%v, err=%v\n", bigdataUID, err.Error())
		return false
	}

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestGet)
	if err != nil {
		log.Printf("uc_rta req failed, bigdataUID=%v, err=%v\n", bigdataUID, err.Error())
		return false
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("uc_rta resp io.ReadAll, bigdataUID=%v, err=%v\n", bigdataUID, err.Error())
		return false
	}
	if resp.StatusCode != 200 {
		return false
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	ucRtaResp := UCRtaRespStu{}
	err = json.Unmarshal([]byte(bodyContent), &ucRtaResp)
	if err != nil {
		return false
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	//tmpRespByte, _ := json.Marshal(ucRtaResp)
	//log.Println("uc_rta resp:", bigdataUID, string(tmpRespByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if ucRtaResp.StatusCode == 0 {
		log.Printf("uc_rta resp status code, bigdataUID=%v, code=%v\n", bigdataUID, ucRtaResp.StatusCode)
		return true
	}

	return false
}

// UCRtaReqStu ...
type UCRtaReqStu struct {
	// 终端唯一标识
	DID string `json:"did,omitempty"`
	// ANDROID, IOS, UNKNOWN
	Platform string `json:"platform,omitempty"`
	// did类型, IMEI, IMEI_MD5, OAID, OAID_MD5, IDFA, IDFA_MD5
	DIDType string `json:"didType,omitempty"`
	// 广告位id
	SlotId string `json:"Slot_Id,omitempty"`
	// channel
	Channel string `json:"channel,omitempty"`
}

// UCRtaRespStu ...
type UCRtaRespStu struct {
	// 状态码, 0: 需要投放, 1 不需要投放, >1 其他错误id
	StatusCode int `json:"status_code"`
	// 投放的广告账号id列表/策略列表
	AcIds []float32 `json:"acIds"`
	// 投放的广告计划id列表
	AdIds []float32 `json:"adIds"`
}
