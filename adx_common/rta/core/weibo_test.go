package core

import (
	"context"
	"fmt"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestIsWeiboRtaOKWithTimeout(t *testing.T) {
	// 创建上下文
	ctx := context.Background()

	// 创建请求ID
	reqId := uuid.New().String()

	// 创建 MHDeviceStu 对象，使用用户提供的参数
	deviceInfo := &models.MHDeviceStu{
		DIDMd5:       "74105669f11de861",
		Os:           "android",
		Osv:          "15",
		IP:           "2408:8462:4e00:5ef7:184a:4682:c934:4a32",
		IPCountry:    "中国",
		IPProvince:   "黑龙江",
		IPCity:       "哈尔滨",
		Ua:           "Xiaomi-Redmi K60 Ultra__weibo__15.6.2__android__android15",
		Oaid:         "0ac8d1c2539d13de",
		Model:        "2304FPN6DC",
		Manufacturer: "Xiaomi",
	}

	// 创建 maplehazeRTAID
	maplehazeRTAID := "mh100015"

	// 构建预期的 Redis 键
	result, err := IsWeiboRtaOKWithTimeout(ctx, nil, reqId, deviceInfo, maplehazeRTAID)
	if err != nil {
		fmt.Printf("IsWeiboRtaOKWithTimeout failed: %v", err)
		return
	}
	assert.Equal(t, false, result)

}
