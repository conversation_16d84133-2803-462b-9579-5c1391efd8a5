package core

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/redis/go-redis/v9"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/pb/kuaishou_rta"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"

	"google.golang.org/protobuf/proto"
)

// IsKuaiShouRtaOK ...
// kuaishou debug url
// url := "https://material-api.test.gifshow.com/rest/n/rta/common/kfy"
// kuaishou url
// url := "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy"
func IsKuaiShouRtaOK(ctx context.Context, redisClient *redis.Client, bigdataUID string, deviceInfo *models.MHDeviceStu, kuaishouRtaInfo *models.KuaiShouRtaStu) (bool, error) {

	defer func() {
		if err := recover(); err != nil {
			log.Println("IsKuaiShouRtaOK recover error:", err)
		}
	}()

	// log.Println("ks_rta config:", bigdataUID, kuaishouRtaInfo.KuaiShouChannel, kuaishouRtaInfo.KuaiShouToken, kuaishouRtaInfo.KuaiShouPromotionTypes)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 参数验证
	paramRepair(deviceInfo)

	kuaishouReqDevice := &kuaishou_rta.RtaRequest_Device{}
	isDeviceOK := false

	// device
	if deviceInfo.Os == "android" {
		if len(deviceInfo.Imei) > 0 {
			kuaishouReqDevice.Imei = deviceInfo.Imei

			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			kuaishouReqDevice.ImeiMd5 = strings.ToLower(deviceInfo.ImeiMd5)

			isDeviceOK = true
		}
		if len(deviceInfo.Oaid) > 0 {
			kuaishouReqDevice.Oaid = deviceInfo.Oaid

			isDeviceOK = true
		}
		if len(deviceInfo.OaidMd5) > 0 {
			kuaishouReqDevice.OaidMd5 = deviceInfo.OaidMd5

			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		if len(deviceInfo.Idfa) > 0 {
			kuaishouReqDevice.Idfa = deviceInfo.Idfa

			isDeviceOK = true
		} else if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			kuaishouReqDevice.IdfaMd5 = strings.ToLower(deviceInfo.IdfaMd5)

			isDeviceOK = true
		}
	} else {
		return false, errors.New("ks_rta, wrong os")
	}
	if isDeviceOK {
	} else {
		return false, errors.New("ks_rta, wrong did_md5")
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		return false, errors.New("ks_rta, wrong did_md5")
	}
	// redis cache
	redisKey := fmt.Sprintf(rediskeys.RTA_CACHE_DIDMD5_KEY, kuaishouRtaInfo.MaplehazeRTAID, time.Now().Format("2006-01-02"), deviceInfo.DIDMd5)
	randNum := rand.Intn(100)
	if randNum <= 50 {
		redisValue, redisErr := redisClient.Get(ctx, redisKey).Result()
		if redisErr != nil {
			// log.Println("redis error:", redisErr)
		} else {
			// log.Printf("ks_rta redis_key:%v, redis_value:%v\n", redisKey, redisValue)
			return convertStringToInt(redisValue) == 1, nil
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	currentTime := utils.GetCurrentMilliSecond()
	kuaishouReqPb := &kuaishou_rta.RtaRequest{
		RequestId:   bigdataUID,
		RequestTime: currentTime,
		Channel:     kuaishouRtaInfo.KuaiShouChannel,
		Sign:        utils.GetMd5(bigdataUID + utils.ConvertInt64ToString(currentTime) + kuaishouRtaInfo.KuaiShouToken),
	}

	kuaishouReqPb.Device = kuaishouReqDevice

	// PromotionType
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_laxin")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lite_laxin")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lahui")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lite_lahui")
	if len(kuaishouRtaInfo.KuaiShouPromotionTypes) > 0 {
		kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, kuaishouRtaInfo.KuaiShouPromotionTypes...)
	} else {
		return false, errors.New("ks_rta, wrong params")
	}

	kuaishouReqPbByte, _ := proto.Marshal(kuaishouReqPb)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// tmpReqByte, _ := json.Marshal(kuaishouReqPb)
	// log.Println("ks_rta req:", bigdataUID, string(tmpReqByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// startTime := time.Now()
	bodyContent, retCode, err := GetFastHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		kuaishouRtaInfo.KuaiShouRtaUrl,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
		utilities.WithProtobufBody(kuaishouReqPbByte),
	)
	// log.Printf("ks_rta req failed, bigdataUID=%v, retCode=%v, err=%v, elapsed=%v\n", bigdataUID, retCode, err, time.Since(startTime))
	if err != nil {
		return false, err
	}
	if retCode != 200 {
		return false, errors.New("ks_rta, wrong status code")
	}

	// client := &http.Client{Timeout: time.Duration(200) * time.Millisecond}

	// // send req
	// requestGet, err := http.NewRequest("POST", kuaishouRtaInfo.KuaiShouRtaUrl, bytes.NewReader(kuaishouReqPbByte))
	// requestGet = requestGet.WithContext(ctx)
	// if err != nil {
	// 	log.Printf("ks_rta NewRequest failed, bigdataUID=%v, err=%v\n", bigdataUID, err.Error())
	// 	return false, errors.New("wrong request")
	// }

	// requestGet.Header.Add("Content-Type", "application/x-protobuf; charset=utf-8")
	// requestGet.Header.Add("Connection", "keep-alive")

	// resp, err := client.Do(requestGet)
	// retCode := 0
	// if resp != nil {
	// 	retCode = resp.StatusCode
	// }

	// if err != nil {
	// 	log.Printf("ks_rta req failed, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
	// 	return false, err
	// }
	// defer resp.Body.Close()

	// bodyContent, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	log.Printf("ks_rta resp io.ReadAll, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
	// 	return false, err
	// }
	// if resp.StatusCode != 200 {
	// 	return false, errors.New("ks_rta, wrong status code")
	// }
	////////////////////////////////////////////////////////////////////////////////////////////////////
	kuaishouResp := &kuaishou_rta.RtaResponse{}
	err = proto.Unmarshal(bodyContent, kuaishouResp)
	if err != nil {
		log.Printf("ks_rta proto.Unmarshal, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
		return false, errors.New("ks_rta, wrong proto unmarshas")
	}
	// log.Printf("ks_rta result bigdataUID=%v, retCode=%v, elapsed=%v, req=%v, resp=%v, kuaishouResp=%v\n", bigdataUID, retCode, time.Since(startTime), kuaishouReqPb, kuaishouResp, kuaishouResp)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// tmpRespByte, _ := json.Marshal(kuaishouResp)
	// log.Println("ks_rta resp:", bigdataUID, string(tmpRespByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if kuaishouResp.StatusCode != 0 {
		log.Printf("ks_rta resp status code, bigdataUID=%v, retCode=%v, resp=%v\n", bigdataUID, retCode, kuaishouResp)
		return false, errors.New("ks_rta, wrong ks status code")
	}

	for _, item := range kuaishouResp.PromotionResult {
		// log.Println("ks_rta resp result:", bigdataUID, item.PromotionType, item.Accept)
		if item.Accept {
			if randNum <= 50 {
				err = redisClient.Set(ctx, redisKey, 1, 20*time.Minute).Err()
				// log.Println("ks_rta set redis_key ok:", redisKey)
				log.Printf("ks_rta set redis_key ok, redisKey=%v, err=%v\n", redisKey, err)
			}

			return true, nil
		}
	}

	if randNum <= 50 {
		// rta redis过期时间随机 00:00 - 01:00
		randTTLMinHour := 0
		randTTLMaxHour := 1

		randTTLKey, _ := utilities.RandomTTL(int64(randTTLMinHour), int64(randTTLMaxHour))
		err = redisClient.Set(ctx, redisKey, 0, randTTLKey).Err()
		log.Printf("ks_rta set redis_key failed retCode=%v, redisKey=%v, err=%v\n", retCode, redisKey, err)
	}

	return false, nil
}

func IsKuaiShouRtaOKWithTimeout(ctx context.Context, redisClient databases.Redis, bigdataUID string, deviceInfo *models.MHDeviceStu, kuaishouRtaInfo *models.KuaiShouRtaStu) (bool, error) {

	defer func() {
		if err := recover(); err != nil {
			log.Println("IsKuaiShouRtaOK recover error:", err)
		}
	}()

	// log.Println("ks_rta config:", bigdataUID, kuaishouRtaInfo.KuaiShouChannel, kuaishouRtaInfo.KuaiShouToken, kuaishouRtaInfo.KuaiShouPromotionTypes)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 参数验证
	paramRepair(deviceInfo)

	kuaishouReqDevice := &kuaishou_rta.RtaRequest_Device{}
	isDeviceOK := false

	// device
	if deviceInfo.Os == "android" {
		if len(deviceInfo.Imei) > 0 {
			kuaishouReqDevice.Imei = deviceInfo.Imei

			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			kuaishouReqDevice.ImeiMd5 = strings.ToLower(deviceInfo.ImeiMd5)

			isDeviceOK = true
		}
		if len(deviceInfo.Oaid) > 0 {
			kuaishouReqDevice.Oaid = deviceInfo.Oaid

			isDeviceOK = true
		}
		if len(deviceInfo.OaidMd5) > 0 {
			kuaishouReqDevice.OaidMd5 = deviceInfo.OaidMd5

			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		if len(deviceInfo.Idfa) > 0 {
			kuaishouReqDevice.Idfa = deviceInfo.Idfa

			isDeviceOK = true
		} else if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			kuaishouReqDevice.IdfaMd5 = strings.ToLower(deviceInfo.IdfaMd5)

			isDeviceOK = true
		}
	} else {
		return false, errors.New("ks_rta, wrong os")
	}
	if isDeviceOK {
	} else {
		return false, errors.New("ks_rta, wrong did_md5")
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		return false, errors.New("ks_rta, wrong did_md5")
	}
	// redis cache
	redisKey := fmt.Sprintf(rediskeys.RTA_CACHE_DIDMD5_KEY, kuaishouRtaInfo.MaplehazeRTAID, time.Now().Format("2006-01-02"), deviceInfo.DIDMd5)
	randNum := rand.Intn(100)
	if randNum <= 50 {
		redisValue, redisErr := redisClient.Get(ctx, utils.Timeout50mill, redisKey)
		if redisErr != nil {
			// log.Println("redis error:", redisErr)
		} else {
			// log.Printf("ks_rta redis_key:%v, redis_value:%v\n", redisKey, redisValue)
			return convertStringToInt(redisValue) == 1, nil
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	currentTime := utils.GetCurrentMilliSecond()
	kuaishouReqPb := &kuaishou_rta.RtaRequest{
		RequestId:   bigdataUID,
		RequestTime: currentTime,
		Channel:     kuaishouRtaInfo.KuaiShouChannel,
		Sign:        utils.GetMd5(bigdataUID + utils.ConvertInt64ToString(currentTime) + kuaishouRtaInfo.KuaiShouToken),
	}

	kuaishouReqPb.Device = kuaishouReqDevice

	// PromotionType
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_laxin")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lite_laxin")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lahui")
	// kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, "kwai_lite_lahui")
	if len(kuaishouRtaInfo.KuaiShouPromotionTypes) > 0 {
		kuaishouReqPb.PromotionType = append(kuaishouReqPb.PromotionType, kuaishouRtaInfo.KuaiShouPromotionTypes...)
	} else {
		return false, errors.New("ks_rta, wrong params")
	}

	kuaishouReqPbByte, _ := proto.Marshal(kuaishouReqPb)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// tmpReqByte, _ := json.Marshal(kuaishouReqPb)
	// log.Println("ks_rta req:", bigdataUID, string(tmpReqByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// startTime := time.Now()
	bodyContent, retCode, err := GetFastHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		kuaishouRtaInfo.KuaiShouRtaUrl,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
		utilities.WithProtobufBody(kuaishouReqPbByte),
	)
	// log.Printf("ks_rta req failed, bigdataUID=%v, retCode=%v, err=%v, elapsed=%v\n", bigdataUID, retCode, err, time.Since(startTime))
	if err != nil {
		return false, err
	}
	if retCode != 200 {
		return false, errors.New("ks_rta, wrong status code")
	}

	// client := &http.Client{Timeout: time.Duration(200) * time.Millisecond}

	// // send req
	// requestGet, err := http.NewRequest("POST", kuaishouRtaInfo.KuaiShouRtaUrl, bytes.NewReader(kuaishouReqPbByte))
	// requestGet = requestGet.WithContext(ctx)
	// if err != nil {
	// 	log.Printf("ks_rta NewRequest failed, bigdataUID=%v, err=%v\n", bigdataUID, err.Error())
	// 	return false, errors.New("wrong request")
	// }

	// requestGet.Header.Add("Content-Type", "application/x-protobuf; charset=utf-8")
	// requestGet.Header.Add("Connection", "keep-alive")

	// resp, err := client.Do(requestGet)
	// retCode := 0
	// if resp != nil {
	// 	retCode = resp.StatusCode
	// }

	// if err != nil {
	// 	log.Printf("ks_rta req failed, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
	// 	return false, err
	// }
	// defer resp.Body.Close()

	// bodyContent, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	log.Printf("ks_rta resp io.ReadAll, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
	// 	return false, err
	// }
	// if resp.StatusCode != 200 {
	// 	return false, errors.New("ks_rta, wrong status code")
	// }
	////////////////////////////////////////////////////////////////////////////////////////////////////
	kuaishouResp := &kuaishou_rta.RtaResponse{}
	err = proto.Unmarshal(bodyContent, kuaishouResp)
	if err != nil {
		log.Printf("ks_rta proto.Unmarshal, bigdataUID=%v, retCode=%v, err=%v\n", bigdataUID, retCode, err.Error())
		return false, errors.New("ks_rta, wrong proto unmarshas")
	}
	// log.Printf("ks_rta result bigdataUID=%v, retCode=%v, elapsed=%v, req=%v, resp=%v, kuaishouResp=%v\n", bigdataUID, retCode, time.Since(startTime), kuaishouReqPb, kuaishouResp, kuaishouResp)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// tmpRespByte, _ := json.Marshal(kuaishouResp)
	// log.Println("ks_rta resp:", bigdataUID, string(tmpRespByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if kuaishouResp.StatusCode != 0 {
		log.Printf("ks_rta resp status code, bigdataUID=%v, retCode=%v, resp=%v\n", bigdataUID, retCode, kuaishouResp)
		return false, errors.New("ks_rta, wrong ks status code")
	}

	for _, item := range kuaishouResp.PromotionResult {
		// log.Println("ks_rta resp result:", bigdataUID, item.PromotionType, item.Accept)
		if item.Accept {
			if randNum <= 50 {
				err = redisClient.Set(ctx, utils.Timeout50mill, redisKey, 1, 20*time.Minute)
				// log.Println("ks_rta set redis_key ok:", redisKey)
				log.Printf("ks_rta set redis_key ok, redisKey=%v, err=%v\n", redisKey, err)
			}

			return true, nil
		}
	}

	if randNum <= 50 {
		// rta redis过期时间随机 00:00 - 01:00
		randTTLMinHour := 0
		randTTLMaxHour := 1

		randTTLKey, _ := utilities.RandomTTL(int64(randTTLMinHour), int64(randTTLMaxHour))
		err = redisClient.Set(ctx, utils.Timeout50mill, redisKey, 0, randTTLKey)
		log.Printf("ks_rta set redis_key failed retCode=%v, redisKey=%v, err=%v\n", retCode, redisKey, err)
	}

	return false, nil
}

func paramRepair(deviceInfo *models.MHDeviceStu) {
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = strings.Replace(deviceInfo.Imei, " ", "", -1)
		deviceInfo.Oaid = strings.Replace(deviceInfo.Oaid, " ", "", -1)

		if deviceInfo.ImeiMd5 == utils.GetMd5("") {
			deviceInfo.ImeiMd5 = ""
		}

		if deviceInfo.OaidMd5 == utils.GetMd5("") {
			deviceInfo.OaidMd5 = ""
		}

		osvMajor := 0
		if len(deviceInfo.Osv) > 0 {
			osvMajorStr := strings.Split(deviceInfo.Osv, ".")[0]
			osvMajor = convertStringToInt(osvMajorStr)
			// log.Println(osvMajor)
		}

		if strings.ToLower(deviceInfo.Imei) == "unknown" ||
			strings.ToLower(deviceInfo.Imei) == "null" ||
			len(deviceInfo.Imei) < 3 ||
			osvMajor >= 10 {
			deviceInfo.Imei = ""
		}

		if len(deviceInfo.ImeiMd5) != 32 ||
			osvMajor >= 10 {
			deviceInfo.ImeiMd5 = ""
		}

		if verifyDID(deviceInfo.Imei, "imei") {
		} else {
			deviceInfo.Imei = ""
		}
		if verifyDID(deviceInfo.ImeiMd5, "imei_md5") {
		} else {
			deviceInfo.ImeiMd5 = ""
		}
		if verifyDID(deviceInfo.Oaid, "oaid") {
		} else {
			deviceInfo.Oaid = ""
		}
		if verifyDID(deviceInfo.OaidMd5, "oaid_md5") {
		} else {
			deviceInfo.OaidMd5 = ""
		}

		if len(deviceInfo.Imei) > 0 {
			if isImei(deviceInfo.Imei) {
			} else {
				log.Printf("wrong imei: %v\n", deviceInfo.Imei)
				deviceInfo.Imei = ""
			}
		}
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = strings.Replace(deviceInfo.Idfa, " ", "", -1)

		if len(deviceInfo.Idfa) > 0 {
			idfaRn := []rune(deviceInfo.Idfa)
			if len(deviceInfo.Idfa) != 36 || string(idfaRn[8]) != "-" || string(idfaRn[13]) != "-" || string(idfaRn[18]) != "-" || string(idfaRn[23]) != "-" {
				deviceInfo.Idfa = ""
			}
		}

		if deviceInfo.IdfaMd5 == utils.GetMd5("") {
			deviceInfo.IdfaMd5 = ""
		}

		if verifyDID(deviceInfo.Idfa, "idfa") {
		} else {
			deviceInfo.Idfa = ""
		}

		if verifyDID(deviceInfo.IdfaMd5, "idfa_md5") {
		} else {
			deviceInfo.IdfaMd5 = ""
		}
	}
}

func verifyDID(did string, didType string) bool {

	maxLength := (len(did) + 1) / 2
	// log.Println(maxLength)

	for i := 0; i < len(did); i++ {
		if strings.Count(did, string(did[i])) >= int(maxLength) {
			log.Printf("wrong did value: %v, wrong char: %v, did type: %v\n", did, string(did[i]), didType)
			return false
		}
	}

	return true
}

// isImei ...
func isImei(imei string) bool {
	// meid
	if len(imei) == 14 {
		return true
	}
	// imei
	if len(imei) != 15 {
		return false
	}

	prefix := imei[0 : len(imei)-1]
	// log.Println(prefix)
	lastDig := imei[len(imei)-1:]
	// log.Println(lastDig)

	var total, sum1, sum2 int
	n := len(prefix)
	for i := 0; i < n; i++ {
		num, _ := strconv.Atoi(string(prefix[i]))
		// 奇数
		if i%2 == 0 {
			sum1 += num
		} else { // 偶数
			tmp := num * 2
			if tmp < 10 {
				sum2 += tmp
			} else {
				sum2 = sum2 + tmp + 1 - 10
			}
		}
	}
	total = sum1 + sum2
	if total%10 == 0 {
		if convertStringToInt(lastDig) == 0 {
			return true
		}
	} else {
		if convertStringToInt(lastDig) == 10-(total%10) {
			return true
		}
	}
	return false
}

// convertStringToInt ...
func convertStringToInt(str string) int {
	value, _ := strconv.Atoi(str)
	return value
}
