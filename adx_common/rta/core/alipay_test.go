package core

import (
	"context"
	"fmt"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"github.com/google/uuid"
)

// 	curl --location --request POST 'https://ugapi.alipay.com/rta/json/shuyin' \
// --header 'Content-Type: application/json' \
// --header 'Cookie: spanner=4MdTVSAKqYkl17N+JF0nodR5okH7H5d2Xt2T4qEYgj0=' \
// --data-raw '{
//     "devices": {
//         "0671ee7fad161e3ad8193040004ee3e6": {
//             "OAID": {
//                 "encrypt_type": "MD5",
//                 "device_id": "0671ee7fad161e3ad8193040004ee3e6"
//             }
//         }
//     },
//     "request_id": "1696843702017-1"
// }'

func TestAlipay(t *testing.T) {
	deviceId := "FE16F406-6D74-4C2C-B288-0675E9CF21DD"
	var ctx = context.Background()

	deviceInfo := &models.MHDeviceStu{
		Os:           "ios",
		Osv:          "10.0",
		Model:        "MI 10",
		Manufacturer: "Apple",
		DIDMd5:       deviceId, // 16位的DID MD5值
		Oaid:         deviceId,
		OaidMd5:      utils.GetMd5(deviceId),
		Idfa:         deviceId,
		IdfaMd5:      utils.GetMd5(deviceId),
		IP:           "***********",
	}
	ret, err := IsAlipayRtaOKWithTimeout(ctx, nil, uuid.NewString(), deviceInfo, "mh100016")
	fmt.Printf("ret=%v, err=%v\n", ret, err)
}
