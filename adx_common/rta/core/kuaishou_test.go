package core

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/pb/kuaishou_rta"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
)

const (
	kuaishouUrl     = "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy"
	kuaishouToken   = "w2y17"
	kuaishouChannel = "GERUI"
)

// TestKuaishouHttpRequest_AndroidDevice 测试快手RTA HTTP请求 - Android设备场景
func TestKuaishouGoRestyRequest(t *testing.T) {

	// 构造完整的RTA请求
	currentTime := utils.GetCurrentMilliSecond()
	bigdataUID := uuid.NewV4().String()
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   bigdataUID,
		Channel:     kuaishouChannel,
		RequestTime: currentTime,
		Sign:        utils.GetMd5(bigdataUID + utils.ConvertInt64ToString(currentTime) + kuaishouToken),
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei:    "123456789012345",
			ImeiMd5: "5d41402abc4b2a76b9719d911017c592",
			Oaid:    "test-oaid-12345",
			OaidMd5: "098f6bcd4621d373cade4e832627b4f6",
		},
		PromotionType: []string{"kwai_laxin"},
	}

	requestBytes, err := proto.Marshal(testRequest)
	assert.NoError(t, err)
	assert.NotEmpty(t, requestBytes)

	// 执行HTTP请求
	ctx := context.Background()
	bodyContent, retCode, err := GetHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		kuaishouUrl,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
		utilities.WithProtobufBody(requestBytes),
	)

	// 验证结果
	if err != nil {
		fmt.Printf("HTTP请求失败: %v\n", err)
		return
	}

	var kuaishouResp kuaishou_rta.RtaResponse
	err = proto.Unmarshal(bodyContent, &kuaishouResp)
	if err != nil {
		fmt.Printf("ks_rta resp: %v\n", kuaishouResp)
		return
	}

	if kuaishouResp.StatusCode != 0 {
		fmt.Printf("ks_rta resp status code, bigdataUID=%v, retCode=%v, resp=%v\n", bigdataUID, retCode, kuaishouResp)
		return
	}

	fmt.Printf("ks_rta resp: %v\n", kuaishouResp)
}

func TestKuaishouFastHttpRequest(t *testing.T) {

	// 构造完整的RTA请求
	currentTime := utils.GetCurrentMilliSecond()
	bigdataUID := uuid.NewV4().String()
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   bigdataUID,
		Channel:     kuaishouChannel,
		RequestTime: currentTime,
		Sign:        utils.GetMd5(bigdataUID + utils.ConvertInt64ToString(currentTime) + kuaishouToken),
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei:    "123456789012345",
			ImeiMd5: "5d41402abc4b2a76b9719d911017c592",
			Oaid:    "test-oaid-12345",
			OaidMd5: "098f6bcd4621d373cade4e832627b4f6",
		},
		PromotionType: []string{"kwai_laxin"},
	}

	requestBytes, err := proto.Marshal(testRequest)
	assert.NoError(t, err)
	assert.NotEmpty(t, requestBytes)

	// 执行HTTP请求
	ctx := context.Background()
	bodyContent, retCode, err := GetFastHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		kuaishouUrl,
		utilities.WithHeaders(map[string]string{
			"Content-Type": "application/x-protobuf; charset=utf-8",
		}),
		utilities.WithProtobufBody(requestBytes),
	)

	// 验证结果
	if err != nil {
		fmt.Printf("HTTP请求失败: %v\n", err)
		return
	}

	var kuaishouResp kuaishou_rta.RtaResponse
	err = proto.Unmarshal(bodyContent, &kuaishouResp)
	if err != nil {
		fmt.Printf("ks_rta resp: %v\n", kuaishouResp)
		return
	}

	if kuaishouResp.StatusCode != 0 {
		fmt.Printf("ks_rta resp status code, bigdataUID=%v, retCode=%v, resp=%v\n", bigdataUID, retCode, kuaishouResp)
		return
	}

	fmt.Printf("ks_rta resp: %v\n", kuaishouResp)
}
