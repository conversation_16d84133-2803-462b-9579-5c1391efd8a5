package core

import (
	"context"
	"fmt"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"github.com/google/uuid"
)

func TestIsJDRtaOK(t *testing.T) {
	// 创建测试设备信息
	deviceInfo := &models.MHDeviceStu{
		Os:   "android",
		Osv:  "10.0",
		Oaid: "FE16F406-6D74-4C2C-B288-0675E9CF21DD",
	}

	maplehazeRTAID := "mh100019"
	// 测试RTA调用
	result, err := IsJDRtaOKWithTimeout(context.Background(), nil, uuid.NewString(), deviceInfo, maplehazeRTAID)

	// 由于这是测试环境，我们主要验证函数不会panic和基本的参数验证
	if err != nil {
		fmt.Printf("JD RTA call returned error (expected in test): %v\n", err)
		return
	}

	fmt.Printf("JD RTA result: %v\n", result)
}
