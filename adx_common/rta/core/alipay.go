package core

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"net/http"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/redis/go-redis/v9"
)

var (
	// 支付宝-dsp当日未播放人群 rtaID: tab3p2
	// 支付宝-快手P0&P1人群 rtaID: 38704
	// 支付宝-p1&p0人群 rtaID: tab3p0p1
	rtaIdMap = map[string]string{
		"mh100016": "tab3p2",
		"mh100017": "38704",
		"mh100018": "tab3p0p1",
	}
)

// IsAlipayRtaOK 检查支付宝RTA是否通过
func IsAlipayRtaOK(ctx context.Context, redisClient *redis.Client, requestID string, deviceInfo *models.MHDeviceStu, maplehazeRTAID string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("IsAlipayRtaOK recover error:", err)
		}
	}()

	if maplehazeRTAID == "" {
		log.Printf("alipay_rta, wrong rta info, rtaInfo=%+v", maplehazeRTAID)
		return false, errors.New("alipay_rta, wrong rta info")
	}

	rtaId, ok := rtaIdMap[maplehazeRTAID]
	if !ok {
		log.Printf("alipay_rta, wrong rta id, rtaId=%v", maplehazeRTAID)
		return false, errors.New("alipay_rta, wrong rta id")
	}

	// 参数验证
	paramRepair(deviceInfo)

	// 检查设备信息是否有效
	isDeviceOK := false
	var deviceType, deviceID, deviceIDMd5 string

	if deviceInfo.Os == "android" {
		// 优先使用OAID
		if len(deviceInfo.OaidMd5) > 0 && deviceInfo.OaidMd5 != utils.GetMd5("") {
			deviceType = "OAID"
			deviceIDMd5 = strings.ToLower(deviceInfo.OaidMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Oaid) > 0 {
			deviceType = "OAID"
			deviceID = deviceInfo.Oaid
			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			deviceType = "IMEI"
			deviceIDMd5 = strings.ToLower(deviceInfo.ImeiMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Imei) > 0 {
			deviceType = "IMEI"
			deviceID = deviceInfo.Imei
			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			deviceType = "IDFA"
			deviceIDMd5 = strings.ToLower(deviceInfo.IdfaMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Idfa) > 0 {
			deviceType = "IDFA"
			deviceID = deviceInfo.Idfa
			isDeviceOK = true
		}
	} else {
		return false, errors.New("alipay_rta, wrong os")
	}

	if !isDeviceOK {
		return false, errors.New("alipay_rta, wrong device info")
	}

	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		return false, errors.New("alipay_rta, wrong did_md5")
	}

	// 使用Redis缓存结果
	// redisKey := fmt.Sprintf(rediskeys.RTA_CACHE_DIDMD5_KEY, maplehazeRTAID, time.Now().Format("2006-01-02"), deviceInfo.DIDMd5)
	// redisValue, redisErr := redisClient.Get(ctx, redisKey).Result()
	// if redisErr != nil {
	// 	// Redis缓存未命中，继续处理
	// } else {
	// 	log.Printf("alipay_rta redis_key:%v, redis_value:%v\n", redisKey, redisValue)
	// 	return utils.ConvertStringToInt(redisValue) == 1, nil
	// }

	// 构建请求体
	alipayReq := &models.AlipayRtaRequest{
		Devices:   make(map[string]map[string]models.AlipayRtaDevice),
		RequestID: requestID,
	}

	// 使用DIDMd5作为唯一键
	alipayReq.Devices[deviceInfo.DIDMd5] = make(map[string]models.AlipayRtaDevice)

	// 根据设备类型设置请求参数
	if len(deviceID) > 0 {
		// 未加密设备ID
		alipayReq.Devices[deviceInfo.DIDMd5][deviceType] = models.AlipayRtaDevice{
			DeviceID:    deviceID,
			EncryptType: "",
		}
	} else if len(deviceIDMd5) > 0 {
		// MD5加密的设备ID
		alipayReq.Devices[deviceInfo.DIDMd5][deviceType] = models.AlipayRtaDevice{
			DeviceID:    deviceIDMd5,
			EncryptType: "MD5",
		}
	}

	if rtaId != "" {
		alipayReq.RtaIdList = []string{rtaId}
	}

	// 将请求体转换为JSON
	reqBody, err := json.Marshal(alipayReq)
	if err != nil {
		log.Printf("alipay_rta json.Marshal error: %v\n", err)
		return false, err
	}

	// 构建请求URL
	rtaURL := "https://ugapi.alipay.com/rta/json/gerui"

	// 发送HTTP请求
	startTime := time.Now()
	bodyContent, retCode, err := GetFastHTTPClientDebug(true).DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		rtaURL,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/json"}),
		utilities.WithJSONBody(string(reqBody)),
	)

	log.Printf("alipay_rta req, rtaId=%v, retCode=%v, err=%v, elapsed=%v\n", rtaId, retCode, err, time.Since(startTime))

	if err != nil {
		return false, err
	}

	if retCode != 200 {
		return false, errors.New("alipay_rta, wrong status code")
	}

	// 解析响应
	alipayResp := &models.AlipayRtaResponse{}
	err = json.Unmarshal([]byte(bodyContent), alipayResp)
	if err != nil {
		log.Printf("alipay_rta json.Unmarshal error: %v\n", err)
		return false, err
	}

	log.Printf("alipay_rta result rtaId=%v, retCode=%v, elapsed=%v, resp=%+v\n", rtaId, retCode, time.Since(startTime), alipayResp)

	// 检查响应是否成功
	if !alipayResp.Success {
		log.Printf("alipay_rta response not success, requestID=%v, resultCode=%v, resultDesc=%v\n", requestID, alipayResp.ResultCode, alipayResp.ResultDesc)
		return false, errors.New("alipay_rta, response not success")
	}

	// 检查是否需要参与竞价
	if alipayResp.Response != nil && alipayResp.Response.RequiredFlow {
		// 缓存结果到Redis
		// err = redisClient.Set(ctx, redisKey, 1, 10*time.Minute).Err()
		log.Printf("alipay_rta set redis_key ok, rtaId=%v\n", rtaId)
		return true, nil
	}

	// 如果不需要参与竞价，缓存结果到Redis
	// rta redis过期时间设置为今晚0点
	// now := time.Now()
	// tomorrow := now.AddDate(0, 0, 1)
	// midnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	// randTTLKey := midnight.Sub(now)
	// err = redisClient.Set(ctx, redisKey, 0, randTTLKey).Err()
	log.Printf("alipay_rta set redis_key failed retCode=%v, rtaId=%v, err=%v\n", retCode, rtaId, err)

	return false, nil
}

// IsAlipayRtaOKWithTimeout 带超时的支付宝RTA检查
func IsAlipayRtaOKWithTimeout(ctx context.Context, redisClient databases.Redis, requestID string, deviceInfo *models.MHDeviceStu, maplehazeRTAID string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("IsAlipayRtaOKWithTimeout recover error:", err)
		}
	}()

	if maplehazeRTAID == "" {
		log.Printf("alipay_rta, wrong rta info, rtaInfo=%+v", maplehazeRTAID)
		return false, errors.New("alipay_rta, wrong rta info")
	}

	rtaId, ok := rtaIdMap[maplehazeRTAID]
	if !ok {
		log.Printf("alipay_rta, wrong rta id, rtaId=%v", maplehazeRTAID)
		return false, errors.New("alipay_rta, wrong rta id")
	}

	// 参数验证
	paramRepair(deviceInfo)

	// 检查设备信息是否有效
	isDeviceOK := false
	var deviceType, deviceID, deviceIDMd5 string

	if deviceInfo.Os == "android" {
		// 优先使用OAID
		if len(deviceInfo.OaidMd5) > 0 && deviceInfo.OaidMd5 != utils.GetMd5("") {
			deviceType = "OAID"
			deviceIDMd5 = strings.ToLower(deviceInfo.OaidMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Oaid) > 0 {
			deviceType = "OAID"
			deviceID = deviceInfo.Oaid
			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			deviceType = "IMEI"
			deviceIDMd5 = strings.ToLower(deviceInfo.ImeiMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Imei) > 0 {
			deviceType = "IMEI"
			deviceID = deviceInfo.Imei
			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			deviceType = "IDFA"
			deviceIDMd5 = strings.ToLower(deviceInfo.IdfaMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Idfa) > 0 {
			deviceType = "IDFA"
			deviceID = deviceInfo.Idfa
			isDeviceOK = true
		}
	} else {
		return false, errors.New("alipay_rta, wrong os")
	}

	if !isDeviceOK {
		log.Printf("alipay_rta, wrong device info, deviceInfo=%+v", deviceInfo)
		return false, errors.New("alipay_rta, wrong device info")
	}

	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		log.Printf("alipay_rta, wrong did_md5, did_md5=%v", deviceInfo.DIDMd5)
		return false, errors.New("alipay_rta, wrong did_md5")
	}

	// 使用Redis缓存结果
	// redisKey := fmt.Sprintf(rediskeys.RTA_CACHE_DIDMD5_KEY, maplehazeRTAID, time.Now().Format("2006-01-02"), deviceInfo.DIDMd5)
	// redisValue, redisErr := redisClient.Get(ctx, utils.Timeout50mill, redisKey)
	// if redisErr != nil {
	// 	// Redis缓存未命中，继续处理
	// } else {
	// 	log.Printf("alipay_rta redis_key:%v, redis_value:%v\n", redisKey, redisValue)
	// 	return utils.ConvertStringToInt(redisValue) == 1, nil
	// }

	// 构建请求体
	alipayReq := &models.AlipayRtaRequest{
		Devices:   make(map[string]map[string]models.AlipayRtaDevice),
		RequestID: requestID,
	}

	// 使用DIDMd5作为唯一键
	alipayReq.Devices[deviceInfo.DIDMd5] = make(map[string]models.AlipayRtaDevice)

	// 根据设备类型设置请求参数
	if len(deviceID) > 0 {
		// 未加密设备ID
		alipayReq.Devices[deviceInfo.DIDMd5][deviceType] = models.AlipayRtaDevice{
			DeviceID:    deviceID,
			EncryptType: "",
		}
	} else if len(deviceIDMd5) > 0 {
		// MD5加密的设备ID
		alipayReq.Devices[deviceInfo.DIDMd5][deviceType] = models.AlipayRtaDevice{
			DeviceID:    deviceIDMd5,
			EncryptType: "MD5",
		}
	}

	if rtaId != "" {
		alipayReq.RtaIdList = []string{rtaId}
	}

	// 将请求体转换为JSON
	reqBody, err := json.Marshal(alipayReq)
	if err != nil {
		log.Printf("alipay_rta json.Marshal error: %v\n", err)
		return false, err
	}

	// 构建请求URL
	rtaURL := "https://ugapi.alipay.com/rta/json/gerui"

	// 发送HTTP请求
	startTime := time.Now()
	bodyContent, retCode, err := GetFastHTTPClientDebug(true).DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		rtaURL,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/json"}),
		utilities.WithJSONBody(string(reqBody)),
	)

	log.Printf("alipay_rta req, rtaId=%v, retCode=%v, bodyContent=%v, elapsed=%v\n", rtaId, retCode, string(reqBody), time.Since(startTime))

	if err != nil {
		return false, err
	}

	if retCode != 200 {
		return false, errors.New("alipay_rta, wrong status code")
	}

	// 解析响应
	alipayResp := &models.AlipayRtaResponse{}
	err = json.Unmarshal([]byte(bodyContent), alipayResp)
	if err != nil {
		log.Printf("alipay_rta json.Unmarshal error: %v\n", err)
		return false, err
	}

	log.Printf("alipay_rta result rtaId=%v, retCode=%v, elapsed=%v, resp=%+v\n", rtaId, retCode, time.Since(startTime), alipayResp)

	// 检查响应是否成功
	if !alipayResp.Success {
		log.Printf("alipay_rta response not success, requestID=%v, resultCode=%v, resultDesc=%v\n", requestID, alipayResp.ResultCode, alipayResp.ResultDesc)
		return false, errors.New("alipay_rta, response not success")
	}

	// 检查是否需要参与竞价
	if alipayResp.Response != nil && alipayResp.Response.RequiredFlow {
		// err = redisClient.Set(ctx, utils.Timeout50mill, redisKey, 1, 10*time.Minute)
		log.Printf("alipay_rta set redis_key ok, rtaId=%v\n", rtaId)
		return true, err
	}

	// rta redis过期时间设置为今晚0点
	// now := time.Now()
	// tomorrow := now.AddDate(0, 0, 1)
	// midnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	// randTTLKey := midnight.Sub(now)
	// err = redisClient.Set(ctx, utils.Timeout50mill, redisKey, 0, randTTLKey)
	log.Printf("alipay_rta set redis_key failed retCode=%v, rtaId=%v, err=%v\n", retCode, rtaId, err)

	return false, nil
}
