package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var (
//	rtaIdJdMap = map[string]string{
//		"mh100019": "",
//	}
)

const (
	secret = "rKaxalFCCJTu2kHlAJ8c1Q=="
	aeskey = "cfcf5c98fa1a4dca8f739011142c45cd"
)

// IsJDRtaOKWithTimeout 带超时的京东科技RTA检查
func IsJDRtaOKWithTimeout(ctx context.Context, redisClient databases.Redis, requestID string, deviceInfo *models.MHDeviceStu, maplehazeRTAID string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("IsJDRtaOK recover error:", err)
		}
	}()

	if maplehazeRTAID == "" {
		log.Printf("jd_rta, wrong rta info, rtaInfo=%+v", maplehazeRTAID)
		return false, errors.New("alipay_rta, wrong rta info")
	}

	rtaId := maplehazeRTAID
	// rtaId, ok := rtaIdJdMap[maplehazeRTAID]
	// if !ok {
	// 	log.Printf("jd_rta, wrong rta id, rtaId=%v", maplehazeRTAID)
	// 	return false, errors.New("alipay_rta, wrong rta id")
	// }

	// 参数验证
	paramRepair(deviceInfo)

	// 检查设备信息是否有效
	isDeviceOK := false
	var idType, deviceID, deviceIDMd5 string

	switch deviceInfo.Os {
	case "android":
		// 优先使用OAID
		if len(deviceInfo.OaidMd5) > 0 && deviceInfo.OaidMd5 != utils.GetMd5("") {
			idType = "oaid_orgnl"
			deviceIDMd5 = strings.ToLower(deviceInfo.OaidMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Oaid) > 0 {
			idType = "oaid_orgnl"
			deviceID = deviceInfo.Oaid
			deviceIDMd5 = utils.GetMd5(deviceID)
			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			idType = "imei"
			deviceIDMd5 = strings.ToLower(deviceInfo.ImeiMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Imei) > 0 {
			idType = "imei"
			deviceID = deviceInfo.Imei
			deviceIDMd5 = utils.GetMd5(deviceID)
			isDeviceOK = true
		}

	case "ios":
		if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			idType = "idfa"
			deviceIDMd5 = strings.ToLower(deviceInfo.IdfaMd5)
			isDeviceOK = true
		} else if len(deviceInfo.Idfa) > 0 {
			idType = "idfa"
			deviceID = deviceInfo.Idfa
			deviceIDMd5 = utils.GetMd5(deviceID)
			isDeviceOK = true
		}

	default:
		log.Printf("jd_rta, wrong os, os=%v", deviceInfo.Os)
		return false, errors.New("jd_rta, wrong os")
	}

	if !isDeviceOK {
		log.Printf("jd_rta, wrong device info, deviceInfo=%+v", deviceInfo)
		return false, errors.New("jd_rta, wrong device info")
	}

	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		log.Printf("jd_rta, wrong did_md5, did_md5=%v", deviceInfo.DIDMd5)
		return false, errors.New("jd_rta, wrong did_md5")
	}

	mediaCode := "geruichuangxin"

	// 构建requestData内容
	requestData := map[string]interface{}{
		"idType":     idType,
		"idMd5Value": deviceIDMd5,
		"rtaIds":     []string{rtaId},
		"ext":        "{}",
	}

	// 将requestData转换为JSON字符串
	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
		log.Printf("jd_rta requestData json.Marshal error: %v\n", err)
		return false, err
	}
	requestDataString := string(requestDataJSON)

	// 构建签名参数
	bodyMap := map[string]interface{}{
		"requestId": uuid.New().String(),
		"mediaCode": mediaCode,
		"timestamp": time.Now().UnixMilli(),
	}

	// 生成签名
	signUtil := JdSignUtil{}
	sign := signUtil.Sha256Sign(bodyMap, secret)

	// AES加密requestData
	encryptedRequestData, err := AESEncrypt(requestDataString, aeskey)
	if err != nil {
		log.Printf("jd_rta AES encrypt error: %v\n", err)
		return false, err
	}

	// 构建最终请求体
	bodyMap["requestData"] = encryptedRequestData
	bodyMap["sign"] = sign

	// 将请求体转换为JSON
	reqBody, err := json.Marshal(bodyMap)
	if err != nil {
		log.Printf("jd_rta json.Marshal error: %v\n", err)
		return false, err
	}

	// 发送HTTP请求
	startTime := time.Now()
	bodyContent, retCode, err := GetFastHTTPClientDebug(true).DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		fmt.Sprintf("https://mk.jr.jd.com/rta/genericaes/%v", mediaCode),
		utilities.WithHeaders(map[string]string{
			"Content-Type": "application/json",
		}),
		utilities.WithJSONBody(string(reqBody)),
	)

	if err != nil {
		log.Printf("jd_rta req error, rtaId=%v, retCode=%v, err=%v, elapsed=%v\n", rtaId, retCode, err, time.Since(startTime))
		return false, err
	}

	if retCode != 200 {
		return false, errors.New("jd_rta, wrong status code")
	}

	// 解析响应
	jdResp := &models.JDRtaResponse{}
	err = json.Unmarshal([]byte(bodyContent), jdResp)
	if err != nil {
		log.Printf("jd_rta json.Unmarshal error: %v\n", err)
		return false, err
	}

	log.Printf("jd_rta result rtaId=%v, retCode=%v, elapsed=%v, resp=%+v\n", rtaId, retCode, time.Since(startTime), jdResp)

	// 检查响应是否成功
	if jdResp.Code != "0" {
		log.Printf("jd_rta response not success, requestID=%v, code=%v, msg=%v\n", requestID, jdResp.Code, jdResp.Msg)
		return false, errors.New("jd_rta, response not success")
	}

	// 检查是否需要参与竞价
	if jdResp.Status == 1 {
		log.Printf("jd_rta bid allowed, rtaId=%v\n", rtaId)
		return true, nil
	}

	// // 检查策略结果
	// for _, result := range jdResp.RtaldResults {
	// 	if result.Status == 1 {
	// 		log.Printf("jd_rta strategy allowed, rtaId=%v, rtald=%v\n", rtaId, result.Rtald)
	// 		return true, nil
	// 	}
	// }

	log.Printf("jd_rta bid not allowed, rtaId=%v\n", rtaId)
	return false, nil
}
