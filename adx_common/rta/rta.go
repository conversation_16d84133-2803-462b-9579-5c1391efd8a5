package rta

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"strings"

	deviceidgenerate "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate"
	deviceidgeneratemodel "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"github.com/allegro/bigcache/v3"
	"github.com/redis/go-redis/v9"
	uuid "github.com/satori/go.uuid"
)

const (
	MaplehazeRtaPlatform         = "maplehaze"          // 枫岚互联RTA平台
	KuaiShouRtaPlatform          = "kuaishou"           // 快手RTA平台
	MaplehazeKuaiShouRtaPlatform = "maplehaze_kuaishou" // 枫岚互联快手RTA平台
	AMapRtaPlatform              = "amap"               // 高德RTA平台
	UCRtaPlatform                = "uc"                 // 优酷RTA平台
	WeiboRtaPlatform             = "weibo"              // 微博RTA平台
	AlipayRtaPlatform            = "alipay"             // 支付宝RTA平台
	JDRtaPlatform                = "jd"                 // 京东科技RTA平台
)

func IsRTAOK(ctx context.Context,
	redisClient *redis.Client,
	mysqlDB *sql.DB,
	bigCache *bigcache.BigCache,
	maplehazeRTAID string,
	deviceInfo *models.MHDeviceStu) (bool, error) {

	if redisClient == nil || mysqlDB == nil || bigCache == nil || len(maplehazeRTAID) == 0 {
		return false, errors.New("wrong params")
	}

	// 先读bigcache, 不存在读mysql
	config, err := getRTAConfigInfoFromBigCacheMySQL(ctx, mysqlDB, bigCache, maplehazeRTAID)
	if err != nil {
		return false, err
	}
	//log.Println("config:", config)
	deviceInfo.DIDMd5 = GetDeviceIDMd5Key(deviceInfo)
	// log.Printf("IsRTAOK rta maplehazeRTAID=%v, didmd5=%v\n", maplehazeRTAID, deviceInfo.DIDMd5)

	isRTAOK := false
	switch config.RtaPlatform {
	case MaplehazeRtaPlatform:
		isRTAOK, err = core.IsMaplehazeRtaOK(ctx, redisClient, config.MaplehazeRedisKey, deviceInfo)
		if err != nil {
			return false, err
		}
	case KuaiShouRtaPlatform:
		isRTAOK, err = core.IsKuaiShouRtaOK(ctx, redisClient, uuid.NewV4().String(), deviceInfo, &models.KuaiShouRtaStu{
			MaplehazeRTAID:         maplehazeRTAID,
			KuaiShouRtaUrl:         "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy",
			KuaiShouChannel:        config.KuaiShouChannel,
			KuaiShouToken:          config.KuaiShouToken,
			KuaiShouPromotionTypes: config.KuaiShouPromotionTypes,
		})
		if err != nil {
			return false, err
		}
	case AMapRtaPlatform:
	case MaplehazeKuaiShouRtaPlatform:
		isRTAOK, _ = core.IsMaplehazeRtaOK(ctx, redisClient, config.MaplehazeRedisKey, deviceInfo)
		if isRTAOK {
		} else {
			isRTAOK, err = core.IsKuaiShouRtaOK(ctx, redisClient, uuid.NewV4().String(), deviceInfo, &models.KuaiShouRtaStu{
				MaplehazeRTAID:         maplehazeRTAID,
				KuaiShouRtaUrl:         "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy",
				KuaiShouChannel:        config.KuaiShouChannel,
				KuaiShouToken:          config.KuaiShouToken,
				KuaiShouPromotionTypes: config.KuaiShouPromotionTypes,
			})
			if err != nil {
				return false, err
			}
		}
	case UCRtaPlatform:
		// TODO: host等待uc rta文档正式上线 https://{{host}}/rta/{{channel}}/v1?channel={{channel}}
		ucRtaUrl := "https://{{host}}/rta/" + config.UCChannel + "/v1?channel=" + config.UCChannel
		isRTAOK = core.IsUCRtaOK(ctx, uuid.NewV4().String(), deviceInfo, &models.UCRtaStu{
			UCRtaUrl:     ucRtaUrl,
			UCRtaChannel: config.UCChannel,
		})
	case WeiboRtaPlatform:
		if isRTAOK, err = core.IsWeiboRtaOK(ctx, redisClient, uuid.NewV4().String(), deviceInfo, maplehazeRTAID); err != nil {
			return false, err
		}
	case AlipayRtaPlatform:
		isRTAOK, err = core.IsAlipayRtaOK(ctx, redisClient, uuid.NewV4().String(), deviceInfo, maplehazeRTAID)
		if err != nil {
			return false, err
		}
	default:
		log.Printf("empty rta platform:%v\n", config.RtaPlatform)
	}

	if isRTAOK {
		return true, nil
	}
	return false, errors.New("rta is failed")
}

func IsRTAOKWithTimeout(ctx context.Context,
	redisClient databases.Redis,
	mysqlDB *sql.DB,
	bigCache *bigcache.BigCache,
	maplehazeRTAID string,
	deviceInfo *models.MHDeviceStu) (bool, error) {

	if redisClient == nil || mysqlDB == nil || bigCache == nil || len(maplehazeRTAID) == 0 {
		return false, errors.New("wrong params")
	}

	// 先读bigcache, 不存在读mysql
	config, err := getRTAConfigInfoFromBigCacheMySQL(ctx, mysqlDB, bigCache, maplehazeRTAID)
	if err != nil {
		return false, err
	}
	//log.Println("config:", config)
	deviceInfo.DIDMd5 = GetDeviceIDMd5Key(deviceInfo)
	// log.Printf("IsRTAOK rta maplehazeRTAID=%v, didmd5=%v\n", maplehazeRTAID, deviceInfo.DIDMd5)

	isRTAOK := false
	switch config.RtaPlatform {
	case MaplehazeRtaPlatform:
		isRTAOK, err = core.IsMaplehazeRtaOKWithTimeout(ctx, redisClient, config.MaplehazeRedisKey, deviceInfo)
		if err != nil {
			return false, err
		}
	case KuaiShouRtaPlatform:
		isRTAOK, err = core.IsKuaiShouRtaOKWithTimeout(ctx, redisClient, uuid.NewV4().String(), deviceInfo, &models.KuaiShouRtaStu{
			MaplehazeRTAID:         maplehazeRTAID,
			KuaiShouRtaUrl:         "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy",
			KuaiShouChannel:        config.KuaiShouChannel,
			KuaiShouToken:          config.KuaiShouToken,
			KuaiShouPromotionTypes: config.KuaiShouPromotionTypes,
		})
		if err != nil {
			return false, err
		}
	case AMapRtaPlatform:
	case MaplehazeKuaiShouRtaPlatform:
		isRTAOK, _ = core.IsMaplehazeRtaOKWithTimeout(ctx, redisClient, config.MaplehazeRedisKey, deviceInfo)
		if isRTAOK {
		} else {
			isRTAOK, err = core.IsKuaiShouRtaOKWithTimeout(ctx, redisClient, uuid.NewV4().String(), deviceInfo, &models.KuaiShouRtaStu{
				MaplehazeRTAID:         maplehazeRTAID,
				KuaiShouRtaUrl:         "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy",
				KuaiShouChannel:        config.KuaiShouChannel,
				KuaiShouToken:          config.KuaiShouToken,
				KuaiShouPromotionTypes: config.KuaiShouPromotionTypes,
			})
			if err != nil {
				return false, err
			}
		}
	case UCRtaPlatform:
		// TODO: host等待uc rta文档正式上线 https://{{host}}/rta/{{channel}}/v1?channel={{channel}}
		ucRtaUrl := "https://{{host}}/rta/" + config.UCChannel + "/v1?channel=" + config.UCChannel
		isRTAOK = core.IsUCRtaOK(ctx, uuid.NewV4().String(), deviceInfo, &models.UCRtaStu{
			UCRtaUrl:     ucRtaUrl,
			UCRtaChannel: config.UCChannel,
		})
	case WeiboRtaPlatform:
		if isRTAOK, err = core.IsWeiboRtaOKWithTimeout(ctx, redisClient, uuid.NewV4().String(), deviceInfo, maplehazeRTAID); err != nil {
			return false, err
		}
	case AlipayRtaPlatform:
		isRTAOK, err = core.IsAlipayRtaOKWithTimeout(ctx, redisClient, uuid.NewV4().String(), deviceInfo, maplehazeRTAID)
		if err != nil {
			return false, err
		}
	case JDRtaPlatform:
		isRTAOK, err = core.IsJDRtaOKWithTimeout(ctx, redisClient, uuid.NewV4().String(), deviceInfo, maplehazeRTAID)
		if err != nil {
			return false, err
		}
	default:
		log.Printf("empty rta platform:%v\n", config.RtaPlatform)
	}

	if isRTAOK {
		return true, nil
	}
	return false, errors.New("rta is failed")
}

func getRTAConfigInfoFromBigCacheMySQL(ctx context.Context, mysqlDB *sql.DB,
	bigCache *bigcache.BigCache,
	maplehazeRTAID string) (*models.RtaConfigStu, error) {

	var tmpItem models.RtaConfigStu

	// bigcache
	cacheKey := "bigcache_maplehaze_rta_id_" + maplehazeRTAID
	cacheValue, cacheError := bigCache.Get(cacheKey)
	if cacheError != nil {
		// log.Println("cache error:", cacheKey, cacheError)
	} else {
		// log.Println("cache value:", string(cacheValue))
		if len(cacheValue) == 0 {
			return nil, errors.New("rta no config in cache")
		}
		json.Unmarshal([]byte(cacheValue), &tmpItem)

		return &tmpItem, nil
	}

	// mysql
	var tmpKSPromotionTypes, tmpAMapRtaIds string
	sqlMediaStr := "select IFNULL(a.maplehaze_rta_id, ''), " +
		"IFNULL(a.rta_platform, ''), " +
		"IFNULL(a.kuaishou_channel, ''), " +
		"IFNULL(a.kuaishou_token, ''), " +
		"IFNULL(a.kuaishou_promotion_types, ''), " +
		"IFNULL(a.uc_channel, ''), " +
		"IFNULL(a.amap_source, ''), " +
		"IFNULL(a.amap_channel, ''), " +
		"IFNULL(a.amap_rta_ids, ''), " +
		"IFNULL(a.maplehaze_redis_key, '') " +
		"from rta.rta_configs a where a.maplehaze_rta_id = ?"

	row := mysqlDB.QueryRowContext(ctx, sqlMediaStr, maplehazeRTAID)
	err := row.Scan(&tmpItem.MaplehazeRtaID,
		&tmpItem.RtaPlatform,
		&tmpItem.KuaiShouChannel,
		&tmpItem.KuaiShouToken,
		&tmpKSPromotionTypes,
		&tmpItem.UCChannel,
		&tmpItem.AMapSource,
		&tmpItem.AMapChannel,
		&tmpAMapRtaIds,
		&tmpItem.MaplehazeRedisKey)

	if err != nil {
		log.Printf("scan failed, err::%v\n", err)
		// bigCache.Set(cacheKey, []byte(""))

		return nil, err
	}

	tmpItem.KuaiShouPromotionTypes = strings.Split(tmpKSPromotionTypes, ",")
	tmpItem.AMapRtaIDs = strings.Split(tmpAMapRtaIds, ",")

	tmpJson, _ := json.Marshal(tmpItem)
	bigCache.Set(cacheKey, tmpJson)

	return &tmpItem, nil
}

func GetDeviceIDMd5Key(rtaDeviceInfo *models.MHDeviceStu) string {
	var deviceInfo deviceidgeneratemodel.DeviceModel
	deviceInfo.Os = rtaDeviceInfo.Os
	deviceInfo.OsVersion = rtaDeviceInfo.Osv
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = rtaDeviceInfo.Imei
		deviceInfo.ImeiMd5 = rtaDeviceInfo.ImeiMd5
		deviceInfo.Oaid = rtaDeviceInfo.Oaid
		deviceInfo.OaidMd5 = rtaDeviceInfo.OaidMd5
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = rtaDeviceInfo.Idfa
		deviceInfo.IdfaMd5 = rtaDeviceInfo.IdfaMd5

		if len(rtaDeviceInfo.CAIDMulti) > 0 {
			var tmpCAIDMulti []deviceidgeneratemodel.DeviceCAIDMultiModel
			for _, tmpItem := range rtaDeviceInfo.CAIDMulti {
				if len(tmpItem.CAID) > 0 && len(tmpItem.CAIDVersion) > 0 {
					var tmpCAIDItem deviceidgeneratemodel.DeviceCAIDMultiModel

					tmpCAIDItem.CAID = tmpItem.CAID
					tmpCAIDItem.CAIDVersion = tmpItem.CAIDVersion
					tmpCAIDMulti = append(tmpCAIDMulti, tmpCAIDItem)
				}
			}

			deviceInfo.CAIDMulti = tmpCAIDMulti
		}
	}

	return deviceidgenerate.GenerateDeviceID(deviceInfo)
}
