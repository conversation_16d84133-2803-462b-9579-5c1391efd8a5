package models

// MHDeviceStu ...
type MHDeviceStu struct {
	Os           string `json:"os"`
	Osv          string `json:"os_version"`
	Model        string `json:"model"`
	Manufacturer string `json:"manufacturer"`
	DIDMd5       string `json:"did_md5"`
	// android imei, android_id, oaid
	Imei         string `json:"imei"`
	ImeiMd5      string `json:"imei_md5"`
	AndroidID    string `json:"android_id"`
	AndroidIDMd5 string `json:"android_id_md5"`
	Oaid         string `json:"oaid"`
	OaidMd5      string `json:"oaid_md5"`
	// ua
	Ua string `json:"ua"`
	// ip
	IP string `json:"ip"`
	// ip location(old)
	IPCountry  string `json:"ip_country"`
	IPProvince string `json:"ip_province"`
	IPCity     string `json:"ip_city"`
	// ip location
	IPLocationCountry  string `json:"ip_location_country"`
	IPLocationProvince string `json:"ip_location_province"`
	IPLocationCity     string `json:"ip_location_city"`
	// idfa
	Idfa    string `json:"idfa"`
	IdfaMd5 string `json:"idfa_md5"`
	// caid_multi
	CAIDMulti []MHDeviceCAIDMulti `json:"caid_multi"`
}

type MHDeviceCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}
