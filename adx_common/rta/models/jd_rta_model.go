package models

// JDRtaRequest 京东科技RTA请求结构体
type JDRtaRequest struct {
	RequestID   string   `json:"requestId"`         // 必传，请求ID
	MediaCode   string   `json:"mediaCode"`         // 必传，媒体编码
	IDType      string   `json:"idType"`            // 必传，设备ID类型
	IDMd5Value  string   `json:"idMd5Value"`        // 必传，设备ID的MD5值
	IDValue     string   `json:"idValue,omitempty"` // 非必传，设备ID明文（与MD5值二选一）
	RequestData string   `json:"requestData"`
	Sign        string   `json:"sign"`
	Timestamp   int64    `json:"timestamp"`
	Rtalds      []string `json:"rtalds,omitempty"` // 非必传，RTA策略列表
}

// JDRtaResponse 京东科技RTA响应结构体
type JDRtaResponse struct {
	RequestID    string        `json:"requestId"`              // 入参的请求ID
	Code         string        `json:"code"`                   // `0` 代表请求正常，其他为异常
	Msg          string        `json:"msg"`                    // 异常信息
	Status       int           `json:"status"`                 // 是否参数：`0` 不参数，`1` 参数
	RtaldResults []JDRtaResult `json:"rtaldResults,omitempty"` // RTA策略参数结果
}

// JDRtaResult 京东科技RTA策略结果结构体
type JDRtaResult struct {
	Rtald  string `json:"rtald"`  // RTA策略ID
	Status int    `json:"status"` // 是否参数：`0` 不参数，`1` 参数
}
