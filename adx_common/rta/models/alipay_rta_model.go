package models

// AlipayRtaRequest 支付宝RTA请求结构体
type AlipayRtaRequest struct {
	Devices   map[string]map[string]AlipayRtaDevice `json:"devices"`
	RtaIdList []string                              `json:"rta_id_list"`
	RequestID string                                `json:"request_id"`
}

// AlipayRtaDevice 支付宝RTA设备信息结构体
type AlipayRtaDevice struct {
	DeviceID    string `json:"device_id"`
	EncryptType string `json:"encrypt_type"`
}

// AlipayRtaResponse 支付宝RTA响应结构体
type AlipayRtaResponse struct {
	ExtMap     map[string]interface{} `json:"extMap"`
	Retriable  bool                   `json:"retriable"`
	Response   *AlipayRtaResult       `json:"response"`
	Success    bool                   `json:"success"`
	ResultCode string                 `json:"resultCode"`
	ResultDesc string                 `json:"resultDesc"`
}

// AlipayRtaResult 支付宝RTA结果结构体
type AlipayRtaResult struct {
	RequiredFlow bool                `json:"requiredFlow"`
	RtaInfoList  []AlipayRtaInfoItem `json:"rtaInfoList"`
}

// AlipayRtaInfoItem 支付宝RTA信息项结构体
type AlipayRtaInfoItem struct {
	AccountID string `json:"accountId"`
}
