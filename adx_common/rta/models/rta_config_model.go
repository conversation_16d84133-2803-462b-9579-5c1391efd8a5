package models

// RtaConfigStu ...
type RtaConfigStu struct {
	MaplehazeRtaID string `json:"maplehaze_rta_id,omitempty"`
	// rta平台
	RtaPlatform string `json:"rta_platform,omitempty"`
	// 快手rta参数
	KuaiShouChannel        string   `json:"kuaishou_channel,omitempty"`
	KuaiShouToken          string   `json:"kuaishou_token,omitempty"`
	KuaiShouPromotionTypes []string `json:"kuaishou_promotion_types,omitempty"`
	// 高德rta参数
	AMapSource  string   `json:"amap_source,omitempty"`
	AMapChannel string   `json:"amap_channel,omitempty"`
	AMapRtaIDs  []string `json:"amap_rta_ids,omitempty"`
	// 枫岚redis key
	MaplehazeRedisKey string `json:"maplehaze_redis_key,omitempty"`
	// UC rta参数
	UCChannel string `json:"uc_channel,omitempty"`
}
