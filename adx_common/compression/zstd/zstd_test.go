package zstd

import (
	"fmt"
	"testing"
)

func TestCompress(t *testing.T) {
	compressed, err := Compress([]byte("hello"))
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	fmt.Println(string(compressed))
}

func TestDecompress(t *testing.T) {
	compressed, err := Compress([]byte("hello"))
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	decompressed, err := Decompress(compressed)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	fmt.Println(string(decompressed))
}
