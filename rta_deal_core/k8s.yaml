apiVersion: apps/v1
kind: Deployment
metadata:
    name: rta-core
    labels:
        app: rta-core
spec:
    replicas: 1
    selector:
        matchLabels:
            app: rta-core
    template:
        metadata:
            name: rta-core
            creationTimestamp: null
            labels:
                app: rta-core
        spec:
            containers:
                - name: http-api-endpoint
                  image: >-
                      mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/adx/rta-core:master
                  envFrom:
                      - configMapRef:
                            name: rta-core
                  resources:
                      requests:
                          cpu: 250m
                          memory: 512Mi
                  imagePullPolicy: Always
            restartPolicy: Always
---
apiVersion: v1
kind: ConfigMap
metadata:
    name: rta-core
    namespace: default
data:
    APP_ID: ''
---
apiVersion: v1
kind: Secret
metadata:
    name: rta-core
    namespace: default
type: Opaque
data:
    APP_KEY: cGFzc3dvcmQ=
---
apiVersion: v1
kind: Service
metadata:
    name: rta-core-svc
    namespace: default
status:
    loadBalancer: {}
spec:
    ports:
        - name: http
          protocol: TCP
          port: 80
          targetPort: 8080
    selector:
        app: rta-core
    type: ClusterIP
