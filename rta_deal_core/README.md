# RTA Core

## 环境变量配置

RTA Core 支持通过环境变量进行配置，允许在不同环境中灵活部署。如果未设置环境变量，将使用默认配置值。

### 数据库配置

#### MySQL 配置
- `MYSQL_HOST`: MySQL 主机地址
- `MYSQL_DBNAME`: MySQL 数据库名称
- `MYSQL_SSP_DBNAME`: MySQL SSP 数据库名称
- `MYSQL_USERNAME`: MySQL 用户名
- `MYSQL_PASSWORD`: MySQL 密码
- `MYSQL_PORT`: MySQL 端口

#### Redis 配置
- `REDIS_HOST`: Redis 主机地址
- `REDIS_PORT`: Redis 端口

#### Hologres 配置
- `HOLOGRES_DSN`: Hologres 数据源名称 (DSN)

#### Postgres 配置
- `POSTGRES_DSN`: Postgres 数据源名称 (DSN)

### 其他配置

- `IP_DB_PATH`: IP 数据库路径
- `ENCRYPT_KEY`: 加密密钥

### Hologres 性能配置

- `HOLOGRES_WRITEMODE`: 写入模式
- `HOLOGRES_BATCHSIZE`: 批处理大小
- `HOLOGRES_WRITE_BATCH_BYTE_SIZE`: 写入批处理字节大小
- `HOLOGRES_WRITE_MAX_INTERVAL_MS`: 写入最大间隔（毫秒）
- `HOLOGRES_THREADSIZE`: 线程大小

### 本地开发

对于本地开发，可以创建一个 `.env` 文件，然后使用以下命令启动应用：

```bash
source .env && go run main.go
```
