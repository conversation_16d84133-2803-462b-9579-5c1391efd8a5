package utils

import (
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var RedisBatchSize int = u.GetEnvInt(
	"REDIS_BATCH_SIZE",
	10000,
)

var HologresWriteMode int = u.GetEnvInt(
	"HOLOGRES_WRITEMODE",
	0,
)

var HologresBatchSize int = u.GetEnvInt(
	"HOLOGRES_BATCHSIZE",
	8192,
)

var HologresWriteBatchByteSize int64 = u.GetEnvInt64(
	"HOLOGRES_WRITE_BATCH_BYTE_SIZE",
	33554432,
)

var HologresWriteMaxIntervalMs int64 = u.GetEnvInt64(
	"HOLOGRES_WRITE_MAX_INTERVAL_MS",
	60000,
)

var HologresThreadSize int = u.GetEnvInt(
	"HOLOGRES_THREADSIZE",
	1,
)

var DauRedisFilterBit string = u.GetEnv(
	"DAU_REDIS_FILTER_BIT",
	"3",
)

var DauRedisFilterDiv string = u.GetEnv(
	"DAU_REDIS_FILTER_DIV",
	"4",
)

var DauSaveHolo bool = u.GetEnvBool(
	"DAU_SAVE_HOLO",
	false,
)

var DauSaveHoloRatio int64 = u.GetEnvInt64(
	"DAU_SAVE_HOLO_RATIO",
	0,
)

var MauSaveHoloRatio int64 = u.GetEnvInt64(
	"MAU_SAVE_HOLO_RATIO",
	0,
)

var DidSaveHoloRatio int64 = u.GetEnvInt64(
	"DID_SAVE_HOLO_RATIO",
	0,
)

var DmpRecordWhiteList []string = u.GetEnvStringArray(
	"DMP_RECORD_WHITELIST",
	[]string{
		"10581",
	},
)

// 0 - 100
var WriteHoloReqDataRatio int = u.GetEnvInt(
	"WRITE_HOLO_REQ_DATA_RATIO",
	0,
)

var RandTTLMinHour int64 = u.GetEnvInt64(
	"RAND_TTL_MIN_HOUR",
	0,
)

var RandTTLMaxHour int64 = u.GetEnvInt64(
	"RAND_TTL_MAX_HOUR",
	3,
)

// DEBUG

var WriteHoloDebugReqDataRatio int = u.GetEnvInt(
	"WRITE_HOLO_DEBUG_REQ_DATA_RATIO",
	0,
)

var DebugPAppId string = u.GetEnv(
	"DEBUG_P_APP_ID",
	"",
)

var DebugTmpAppId string = u.GetEnv(
	"DEBUG_TMP_APP_ID",
	"",
)

var DebugAppId string = u.GetEnv(
	"DEBUG_APP_ID",
	"",
)

var DebugKsAppId string = u.GetEnv(
	"DEBUG_KS_APP_ID",
	"",
)

var ReplaceIPDAUPAppList []string = u.GetEnvStringArray(
	"REPLACE_IP_DAU_PAPP_LIST",
	[]string{},
)

var DebugKSUGMaxEcpm int = u.GetEnvInt(
	"DEBUG_KS_UG_MAX_ECPM",
	0,
)

var DebugBaiqingtengJSON string = u.GetEnv(
	"DEBUG_BAIQINGTENG_JSON",
	"",
)

var DebugBaiqingtengURL string = u.GetEnv(
	"DEBUG_BAIQINGTENG_URL",
	"",
)

var DebugBaiqingtengXUBDate string = u.GetEnv(
	"DEBUG_BAIQINGTENG_X_UB_DATE",
	"",
)

var DebugBaiqingtengXUBAuthorization string = u.GetEnv(
	"DEBUG_BAIQINGTENG_X_UB_AUTHORIZATION",
	"",
)
