package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"hash"
	"io"
	"net"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
)

// GetMd5 ...
func GetMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// Get16Md5 ...
func Get16Md5(str string) string {
	return GetMd5(str)[8:24]
}

// GetSha1 ...
func GetSha1(str string) string {
	sha1 := sha1.New()
	sha1.Write([]byte(str))
	return hex.EncodeToString(sha1.Sum([]byte("")))
}

// ConvertStringToInt ...
func ConvertStringToInt(str string) int {
	value, _ := strconv.Atoi(str)
	return value
}

// ConvertStringToInt64 ...
func ConvertStringToInt64(str string) int64 {
	value, _ := strconv.ParseInt(str, 10, 64)
	return value
}

// ConvertIntToString ...
func ConvertIntToString(value int) string {
	return strconv.Itoa(value)
}

// ConvertInt64ToString ...
func ConvertInt64ToString(value int64) string {
	return strconv.FormatInt(value, 10)
}

// ConvertStringToFloat ...
func ConvertStringToFloat(str string) float64 {
	value, _ := strconv.ParseFloat(str, 32)
	return value
}

// ConvertFloat32ToString 保留小数点后2位
func ConvertFloat32ToString(value float32) string {
	return strconv.FormatFloat(float64(value), 'f', 2, 32)
}

func IsNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

func checkIP(ipStr string) bool {
	log := logger.GetSugaredLogger()
	address := net.ParseIP(ipStr)
	if address == nil {
		log.Errorf("wrong ip=%v", ipStr)
		return false
	}
	// log.Debugf("right ip: %v", address.String())
	return true
}

// ip to int64
func inetAton(ipStr string) int64 {
	bits := strings.Split(ipStr, ".")

	b0, _ := strconv.Atoi(bits[0])
	b1, _ := strconv.Atoi(bits[1])
	b2, _ := strconv.Atoi(bits[2])
	b3, _ := strconv.Atoi(bits[3])

	var sum int64

	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

// IsInnerIP ...
func IsInnerIP(ipStr string) bool {
	if !checkIP(ipStr) {
		return false
	}
	inputIPNum := inetAton(ipStr)
	innerIPA := inetAton("**************")
	innerIPB := inetAton("**************")
	innerIPC := inetAton("***************")
	innerIPD := inetAton("**************")
	innerIPF := inetAton("***************")

	return inputIPNum>>24 == innerIPA>>24 || inputIPNum>>20 == innerIPB>>20 ||
		inputIPNum>>16 == innerIPC>>16 || inputIPNum>>22 == innerIPD>>22 ||
		inputIPNum>>24 == innerIPF>>24
}

// IsImei ...
func IsImei(imei string) bool {
	// meid
	if len(imei) == 14 {
		return true
	}
	// imei
	if len(imei) != 15 {
		return false
	}

	prefix := imei[0 : len(imei)-1]
	// log.Println(prefix)
	lastDig := imei[len(imei)-1:]
	// log.Println(lastDig)

	var total, sum1, sum2 int
	n := len(prefix)
	for i := 0; i < n; i++ {
		num, _ := strconv.Atoi(string(prefix[i]))
		// 奇数
		if i%2 == 0 {
			sum1 += num
		} else { // 偶数
			tmp := num * 2
			if tmp < 10 {
				sum2 += tmp
			} else {
				sum2 = sum2 + tmp + 1 - 10
			}
		}
	}
	total = sum1 + sum2
	if total%10 == 0 {
		if ConvertStringToInt(lastDig) == 0 {
			return true
		}
	} else {
		if ConvertStringToInt(lastDig) == 10-(total%10) {
			return true
		}
	}
	return false
}

func GetIOSModel(originModel string) string {
	if originModel == "iPhone 1G" {
		return "iPhone1,1"
	} else if originModel == "iPhone 3G" {
		return "iPhone1,2"
	} else if originModel == "iPhone 3GS" {
		return "iPhone2,1"
	} else if originModel == "iPhone 4 (GSM)" {
		return "iPhone3,1"
	} else if originModel == "iPhone 4" {
		return "iPhone3,2"
	} else if originModel == "iPhone 4 (CDMA)" {
		return "iPhone3,3"
	} else if originModel == "iPhone 4S" {
		return "iPhone4,1"
	} else if originModel == "iPhone 5" {
		return "iPhone5,1"
	} else if originModel == "iPhone 5" {
		return "iPhone5,2"
	} else if originModel == "iPhone 5c" {
		return "iPhone5,3"
	} else if originModel == "iPhone 5c" {
		return "iPhone5,4"
	} else if originModel == "iPhone 5s" {
		return "iPhone6,1"
	} else if originModel == "iPhone 5s" {
		return "iPhone6,2"
	} else if originModel == "iPhone 6 Plus" {
		return "iPhone7,1"
	} else if originModel == "iPhone 6" {
		return "iPhone7,2"
	} else if originModel == "iPhone 6s" {
		return "iPhone8,1"
	} else if originModel == "iPhone 6s Plus" {
		return "iPhone8,2"
	} else if originModel == "iPhone SE (1st generation)" {
		return "iPhone8,4"
	} else if originModel == "iPhone 7" {
		return "iPhone9,1"
	} else if originModel == "iPhone 7 Plus" {
		return "iPhone9,2"
	} else if originModel == "iPhone 7" {
		return "iPhone9,3"
	} else if originModel == "iPhone 7 Plus" {
		return "iPhone9,4"
	} else if originModel == "iPhone 8" {
		return "iPhone10,1"
	} else if originModel == "iPhone 8 Plus" {
		return "iPhone10,2"
	} else if originModel == "iPhone X" {
		return "iPhone10,3"
	} else if originModel == "iPhone 8" {
		return "iPhone10,4"
	} else if originModel == "iPhone 8 Plus" {
		return "iPhone10,5"
	} else if originModel == "iPhone X" {
		return "iPhone10,6"
	} else if originModel == "iPhone XS" {
		return "iPhone11,2"
	} else if originModel == "iPhone XS Max" {
		return "iPhone11,6"
	} else if originModel == "iPhone XR" {
		return "iPhone11,8"
	} else if originModel == "iPhone 11" {
		return "iPhone12,1"
	} else if originModel == "iPhone 11 Pro" {
		return "iPhone12,3"
	} else if originModel == "iPhone 11 Pro Max" {
		return "iPhone12,5"
	} else if originModel == "iPhone SE (2nd generation)" {
		return "iPhone12,8"
	} else if originModel == "iPhone 12 mini" {
		return "iPhone13,1"
	} else if originModel == "iPhone 12" {
		return "iPhone13,2"
	} else if originModel == "iPhone 12 Pro" {
		return "iPhone13,3"
	} else if originModel == "iPhone 12 Pro Max" {
		return "iPhone13,4"
	} else if originModel == "iPhone 13 Pro" {
		return "iPhone14,2"
	} else if originModel == "iPhone 13 Pro Max" {
		return "iPhone14,3"
	} else if originModel == "iPhone 13 mini" {
		return "iPhone14,4"
	} else if originModel == "iPhone 13" {
		return "iPhone14,5"
	} else if originModel == "iPad 1" {
		return "iPad1,1"
	} else if originModel == "iPad 2 (WiFi)" {
		return "iPad2,1"
	} else if originModel == "iPad 2 (GSM)" {
		return "iPad2,2"
	} else if originModel == "iPad 2 (CDMA)" {
		return "iPad2,3"
	} else if originModel == "iPad 2" {
		return "iPad2,4"
	} else if originModel == "iPad mini 1" {
		return "iPad2,5"
	} else if originModel == "iPad mini 1" {
		return "iPad2,6"
	} else if originModel == "iPad mini 1" {
		return "iPad2,7"
	} else if originModel == "iPad 3 (WiFi)" {
		return "iPad3,1"
	} else if originModel == "iPad 3 (4G)" {
		return "iPad3,2"
	} else if originModel == "iPad 3 (4G)" {
		return "iPad3,3"
	} else if originModel == "iPad 4" {
		return "iPad3,4"
	} else if originModel == "iPad 4" {
		return "iPad3,5"
	} else if originModel == "iPad 4" {
		return "iPad3,6"
	} else if originModel == "iPad Air" {
		return "iPad4,1"
	} else if originModel == "iPad Air" {
		return "iPad4,2"
	} else if originModel == "iPad Air" {
		return "iPad4,3"
	} else if originModel == "iPad mini 2" {
		return "iPad4,4"
	} else if originModel == "iPad mini 2" {
		return "iPad4,5"
	} else if originModel == "iPad mini 2" {
		return "iPad4,6"
	} else if originModel == "iPad mini 3" {
		return "iPad4,7"
	} else if originModel == "iPad mini 3" {
		return "iPad4,8"
	} else if originModel == "iPad mini 3" {
		return "iPad4,9"
	} else if originModel == "iPad mini 4" {
		return "iPad5,1"
	} else if originModel == "iPad mini 4" {
		return "iPad5,2"
	} else if originModel == "iPad Air 2" {
		return "iPad5,3"
	} else if originModel == "iPad Air 2" {
		return "iPad5,4"
	} else if originModel == "iPad Pro (9.7-inch)" {
		return "iPad6,3"
	} else if originModel == "iPad Pro (9.7-inch)" {
		return "iPad6,4"
	} else if originModel == "iPad Pro (12.9-inch)" {
		return "iPad6,7"
	} else if originModel == "iPad Pro (12.9-inch)" {
		return "iPad6,8"
	} else if originModel == "iPad (5th generation)" {
		return "iPad6,11"
	} else if originModel == "iPad (5th generation)" {
		return "iPad6,12"
	} else if originModel == "iPad Pro (12.9-inch)(2nd generation)" {
		return "iPad7,1"
	} else if originModel == "iPad Pro (12.9-inch)(2nd generation)" {
		return "iPad7,2"
	} else if originModel == "iPad Pro (10.5-inch)" {
		return "iPad7,3"
	} else if originModel == "iPad Pro (10.5-inch)" {
		return "iPad7,4"
	} else if originModel == "iPad (6th generation)" {
		return "iPad7,5"
	} else if originModel == "iPad (6th generation)" {
		return "iPad7,6"
	} else if originModel == "iPad (7th generation)" {
		return "iPad7,11"
	} else if originModel == "iPad (7th generation)" {
		return "iPad7,12"
	} else if originModel == "iPad Pro (11-inch)" {
		return "iPad8,1"
	} else if originModel == "iPad Pro (11-inch)" {
		return "iPad8,2"
	} else if originModel == "iPad Pro (11-inch)" {
		return "iPad8,3"
	} else if originModel == "iPad Pro (11-inch)" {
		return "iPad8,4"
	} else if originModel == "iPad Pro (12.9-inch) (3rd generation)" {
		return "iPad8,5"
	} else if originModel == "iPad Pro (12.9-inch) (3rd generation)" {
		return "iPad8,6"
	} else if originModel == "iPad Pro (12.9-inch) (3rd generation)" {
		return "iPad8,7"
	} else if originModel == "iPad Pro (12.9-inch) (3rd generation)" {
		return "iPad8,8"
	} else if originModel == "iPad Pro (11-inch) (2nd generation)" {
		return "iPad8,9"
	} else if originModel == "iPad Pro (11-inch) (2nd generation)" {
		return "iPad8,10"
	} else if originModel == "iPad Pro (12.9-inch) (4th generation)" {
		return "iPad8,11"
	} else if originModel == "iPad Pro (12.9-inch) (4th generation)" {
		return "iPad8,12"
	} else if originModel == "iPad mini (5th generation)" {
		return "iPad11,1"
	} else if originModel == "iPad mini (5th generation)" {
		return "iPad11,2"
	} else if originModel == "iPad Air (3rd generation)" {
		return "iPad11,3"
	} else if originModel == "iPad Air (3rd generation)" {
		return "iPad11,4"
	} else if originModel == "iPad (8th generation)" {
		return "iPad11,6"
	} else if originModel == "iPad (8th generation)" {
		return "iPad11,7"
	} else if originModel == "iPad (9th generation)" {
		return "iPad12,1"
	} else if originModel == "iPad (9th generation)" {
		return "iPad12,2"
	} else if originModel == "iPad Air (4th generation)" {
		return "iPad13,1"
	} else if originModel == "iPad Air (4th generation)" {
		return "iPad13,2"
	} else if originModel == "iPad Pro (11-inch) (3rd generation)" {
		return "iPad13,4"
	} else if originModel == "iPad Pro (11-inch) (3rd generation)" {
		return "iPad13,5"
	} else if originModel == "iPad Pro (11-inch) (3rd generation)" {
		return "iPad13,6"
	} else if originModel == "iPad Pro (11-inch) (3rd generation)" {
		return "iPad13,7"
	} else if originModel == "iPad Pro (12.9-inch) (5th generation)" {
		return "iPad13,8"
	} else if originModel == "iPad Pro (12.9-inch) (5th generation)" {
		return "iPad13,9"
	} else if originModel == "iPad Pro (12.9-inch) (5th generation)" {
		return "iPad13,10"
	} else if originModel == "iPad Pro (12.9-inch) (5th generation)" {
		return "iPad13,11"
	} else if originModel == "iPad mini (6th generation)" {
		return "iPad14,1"
	} else if originModel == "iPad mini (6th generation)" {
		return "iPad14,2"
	}

	return originModel
}

func VerifyDID(did string, didType string) bool {
	log := logger.GetSugaredLogger()
	// log.Debugf("%s", prefix)

	// log.Debugf("%s", lastDig)

	// log.Debugf("%d", maxLength)
	for i := 0; i < len(did); i++ {
		if (did[i] < '0' || did[i] > '9') && (did[i] < 'A' || did[i] > 'F') && (did[i] < 'a' || did[i] > 'f') {
			log.Errorf("wrong did value: %v, wrong char: %v, did type: %v", did, string(did[i]), didType)
			return false
		}
	}
	return true
}

func GetCurrentNanoSecond() int64 {
	return time.Now().UnixNano()
}

// GetCurrentMilliSecond ...
func GetCurrentMilliSecond() int64 {
	return time.Now().UnixNano() / 1e6
}

// GetCurrentSecond ...
func GetCurrentSecond() int64 {
	return time.Now().Unix()
}

// GetCurrentHour ...
func GetCurrentHour() int {
	return time.Now().Hour()
}

// const ...
const (
	PayloadSize    = 8
	InitVectorSize = 16
	SignatureSize  = 4
	// EKey           = "dc31f9c99fd64b724ad58628f1cbcdc17571ddcd"
	// IKey           = "48fdef1455de9f986dd280ce1fd910b9e4f7b13c"
	UTF8 = "utf-8"
)

// AddBase64Padding ...
func AddBase64Padding(paramBase64 string) string {
	if i := len(paramBase64) % 4; i != 0 {
		paramBase64 += strings.Repeat("=", 4-i)
	}
	return paramBase64
}

// CreateHmac ...
func CreateHmac(key, mode string, isBase64 bool) (hash.Hash, error) {
	var b64DecodedKey, k []byte
	var err error
	if isBase64 {
		b64DecodedKey, err = base64.URLEncoding.DecodeString(AddBase64Padding(key))
		if err == nil {
			key = string(b64DecodedKey[:])
		}
	}
	if mode == UTF8 {
		k = []byte(key)
	} else {
		k, err = hex.DecodeString(key)
	}
	if err != nil {
		return nil, err
	}
	return hmac.New(sha1.New, k), nil
}

// HmacSum ...
func HmacSum(hmacParam hash.Hash, buf []byte) []byte {
	hmacParam.Reset()
	hmacParam.Write(buf)
	return hmacParam.Sum(nil)
}

// GenerateHmac ...
func GenerateHmac(key string, buf []byte) ([]byte, error) {
	hmacParam, err := CreateHmac(key, UTF8, false)
	if err != nil {
		err = fmt.Errorf("jzt/encrypt: create hmac error, %s", err.Error())
		return nil, err
	}
	return HmacSum(hmacParam, buf), nil
}

// EncryptPrice ...
func EncryptPrice(price float64, eKey string, iKey string) (string, error) {
	var (
		iv         = make([]byte, InitVectorSize)
		encoded    = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	sum := md5.Sum([]byte(""))
	copy(iv[:], sum[:])

	// pad = hmac(e_key, iv)  // first 8 bytes
	pad, err := GenerateHmac(eKey, iv[:])
	if err != nil {
		return "", err
	}
	pad = pad[:PayloadSize]

	bits := uint64(price)
	binary.BigEndian.PutUint64(priceBytes, bits)
	// enc_price = pad <xor> price
	for i := range priceBytes {
		encoded[i] = pad[i] ^ priceBytes[i]
	}

	// signature = hmac(i_key, data || iv), first 4 bytes
	sig, err := GenerateHmac(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return "", err
	}
	signature = sig[:SignatureSize]

	// final_message = WebSafeBase64Encode( iv || enc_price || signature )
	finalMessage := strings.TrimRight(
		base64.URLEncoding.EncodeToString(append(append(iv[:], encoded[:]...), signature[:]...)),
		"=")
	return finalMessage, nil
}

// DecryptPrice ...
func DecryptPrice(finalMessage string, eKey string, iKey string) (float64, error) {
	var err error
	var errPrice float64
	encryptedPrice := AddBase64Padding(finalMessage)
	decoded, err := base64.URLEncoding.DecodeString(encryptedPrice)
	if err != nil {
		return errPrice, err
	}
	var (
		iv         = make([]byte, InitVectorSize)
		p          = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], decoded[0:16])
	copy(p[:], decoded[16:24])
	copy(signature[:], decoded[24:28])

	pad, err := GenerateHmac(eKey, iv[:])
	if err != nil {
		return errPrice, err
	}
	pad = pad[:PayloadSize]
	for i := range p {
		priceBytes[i] = pad[i] ^ p[i]
	}

	sig, err := GenerateHmac(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return errPrice, err
	}
	sig = sig[:SignatureSize]
	for i := range sig {
		if sig[i] != signature[i] {
			return errPrice, fmt.Errorf("jzt/decrypt: Failed to decrypt, got:%s ,expect:%s", string(sig), string(signature))
		}
	}
	// price := math.Float64frombits(binary.BigEndian.Uint64(priceBytes))
	price := float64(binary.BigEndian.Uint64(priceBytes[:]))

	return price, nil
}

func Generate2345Hmac(key string, buf []byte) []byte {

	h := hmac.New(sha1.New, []byte(key))
	h.Write(buf)
	signature := hex.EncodeToString(h.Sum(nil))
	return []byte(signature)

}

func HmacSha256(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// EncryptPrice ...
func Encrypt2345Price(price float64, eKey string, iKey string) (string, error) {
	var (
		iv         = make([]byte, InitVectorSize)
		encoded    = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], []byte("33fef3252c60a105"))
	pad := Generate2345Hmac(eKey, iv[:])
	pad = pad[:PayloadSize]

	priceString := strconv.FormatFloat(price, 'f', 0, 64)
	priceString = fmt.Sprintf("%-8s", priceString)
	copy(priceBytes[:], []byte(priceString))
	for i := range priceBytes {
		encoded[i] = pad[i] ^ priceBytes[i]
	}

	sig := Generate2345Hmac(iKey, append(priceBytes[:], iv[:]...))
	signature = sig[:SignatureSize]

	finalMessage := base64.URLEncoding.EncodeToString(append(append(iv[:], encoded[:]...), signature[:]...))
	return finalMessage, nil
}

// DecryptPrice ...
func Decrypt2345Price(finalMessage string, eKey string, iKey string) (float64, error) {
	var err error
	var errPrice float64
	encryptedPrice := AddBase64Padding(finalMessage)
	decoded, err := base64.URLEncoding.DecodeString(encryptedPrice)
	if err != nil {
		return errPrice, err
	}

	var (
		iv         = make([]byte, InitVectorSize)
		p          = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], decoded[0:16])
	copy(p[:], decoded[16:24])
	copy(signature[:], decoded[24:28])

	pad := Generate2345Hmac(eKey, iv[:])
	pad = pad[:PayloadSize]
	for i := range p {
		priceBytes[i] = p[i] ^ pad[i]
	}

	sig := Generate2345Hmac(iKey, append(priceBytes[:], iv[:]...))

	sig = sig[:SignatureSize]

	for i := range sig {
		if sig[i] != signature[i] {
			return errPrice, fmt.Errorf("jzt/decrypt: Failed to decrypt, got:%s ,expect:%s", string(sig), string(signature))
		}
	}
	price := ConvertStringToFloat(strings.Trim(string(priceBytes[:]), " "))
	return price, nil
}

// PKCS7Padding 填充模式
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	//Repeat()函数的功能是把切片[]byte{byte(padding)}复制padding个，然后合并成新的字节切片返回
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS7UnPadding 填充的反向操作，删除填充字符串
func PKCS7UnPadding(origData []byte) ([]byte, error) {
	//获取数据长度
	length := len(origData)
	if length == 0 {
		return nil, errors.New("加密字符串错误！")
	} else {
		//获取填充字符串长度
		unpadding := int(origData[length-1])
		//截取切片，删除填充字节，并且返回明文
		return origData[:(length - unpadding)], nil
	}
}

func AesCBCPKCS5Decrypt(crypted, key []byte) []byte {
	log := logger.GetSugaredLogger()
	block, err := aes.NewCipher(key)
	if err != nil {
		log.Errorf("err is: %v", err)
	}
	blockMode := NewECBDecrypter(block)
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS5UnPadding(origData)
	// log.Infof("source is : %v", string(origData))
	return origData
}

func AesCBCPKCS5Encrypt(src, key string) []byte {
	log := logger.GetSugaredLogger()
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		log.Errorf("key error1, err is %v", err)
	}
	// if src == "" {
	// 	log.Info("plain content empty")
	// }
	ecb := NewECBEncrypter(block)
	content := []byte(src)
	content = PKCS5Padding(content, block.BlockSize())
	crypted := make([]byte, len(content))
	ecb.CryptBlocks(crypted, content)
	// 普通base64编码加密 区别于urlsafe base64
	// log.Infof("base64 result: %v", base64.StdEncoding.EncodeToString(crypted))
	// log.Debugf("base64UrlSafe result: %v", Base64URLEncode(crypted))
	return crypted
}

// HexDecode
func HexDecode(s string) []byte {
	dst := make([]byte, hex.DecodedLen(len(s))) //申请一个切片, 指明大小. 必须使用hex.DecodedLen
	n, err := hex.Decode(dst, []byte(s))        //进制转换, src->dst
	if err != nil {
		return nil
	}
	return dst[:n] //返回0:n的数据.
}

// HexEncode
func HexEncode(s string) []byte {
	dst := make([]byte, hex.EncodedLen(len(s))) //申请一个切片, 指明大小. 必须使用hex.EncodedLen
	n := hex.Encode(dst, []byte(s))             //字节流转化成16进制
	return dst[:n]
}

func Base64URLDecode(data string) ([]byte, error) {
	// log := logger.GetSugaredLogger()
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing)
	// res, err := base64.URLEncoding.DecodeString(data)
	// log.Infof("decodebase64urlsafe is, res=%v, err=%v", string(res), err)
	return base64.URLEncoding.DecodeString(data)
}

func Base64URLEncode(source []byte) string {
	// Base64 Url Safe is the same as Base64 but does not contain '/' and '+' (replaced by '_' and '-') and trailing '=' are removed.
	bytearr := base64.StdEncoding.EncodeToString(source)
	// safeurl := strings.Replace(string(bytearr), "/", "_", -1)
	// safeurl = strings.Replace(safeurl, "+", "-", -1)
	// safeurl = strings.Replace(safeurl, "=", "", -1)

	safeurl := url.QueryEscape(bytearr)
	return safeurl
}

type ecb struct {
	b         cipher.Block
	blockSize int
}

func newECB(b cipher.Block) *ecb {
	return &ecb{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbEncrypter ecb

// NewECBEncrypter returns a BlockMode which encrypts in electronic code book
// mode, using the given Block.
func NewECBEncrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbEncrypter)(newECB(b))
}
func (x *ecbEncrypter) BlockSize() int { return x.blockSize }
func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

type ecbDecrypter ecb

// NewECBDecrypter returns a BlockMode which decrypts in electronic code book
// mode, using the given Block.
func NewECBDecrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbDecrypter)(newECB(b))
}
func (x *ecbDecrypter) BlockSize() int { return x.blockSize }
func (x *ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Decrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	// 去掉最后一个字节 unpadding 次
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// AesCBCEncrypt 实现加密
func AesCBCEncrypt(origData []byte, key []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//获取块的大小
	blockSize := block.BlockSize()
	//对数据进行填充，让数据长度满足需求
	origData = PKCS7Padding(origData, blockSize)
	//采用AES加密方法中CBC加密模式
	blocMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypted := make([]byte, len(origData))
	//执行加密
	blocMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

// AesCBCDecrypt 实现解密
func AesCBCDecrypt(cypted []byte, key []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//获取块大小
	blockSize := block.BlockSize()
	//创建加密客户端实例
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	origData := make([]byte, len(cypted))
	//这个函数也可以用来解密
	blockMode.CryptBlocks(origData, cypted)
	//去除填充字符串
	origData, err = PKCS7UnPadding(origData)
	if err != nil {
		return nil, err
	}
	return origData, err
}

// EncodeString 加密base64
func EncodeString(pwd []byte) (string, error) {
	result, err := AesCBCEncrypt(pwd, []byte("1234567887654321"))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(result), err
}

// DecodeString 解密
func DecodeString(pwd string) ([]byte, error) {
	//解密base64字符串
	pwdByte, err := base64.StdEncoding.DecodeString(pwd)
	if err != nil {
		return nil, err
	}
	//执行AES解密
	return AesCBCDecrypt(pwdByte, []byte("1234567887654321"))
}

// AesECBDecrypt ...
func AesECBDecrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	origData, err := PKCS7UnPadding(decrypted)
	if err != nil {
		return nil
	}
	return origData
}

// AesECBEncrypt ...
func AesECBEncrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	data = PKCS7Padding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return decrypted
}

// AesECBNoPaddingDecrypt ...
func AesECBNoPaddingDecrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	origData, err := UnNoPadding(decrypted)
	if err != nil {
		return nil
	}
	return origData
}

// AesECBNoPaddingEncrypt ...
func AesECBNoPaddingEncrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	data = NoPadding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return decrypted
}

func NoPadding(src []byte, blockSize int) []byte {
	count := blockSize - len(src)%blockSize
	if len(src)%blockSize == 0 {
		return src
	} else {
		return append(src, bytes.Repeat([]byte{byte(0)}, count)...)
	}
}
func UnNoPadding(src []byte) ([]byte, error) {
	for i := len(src) - 1; ; i-- {
		if src[i] != 0 {
			return src[:i+1], nil
		}
	}
	// return nil, errors.New("加密字符串错误！")
}

// AesCBCEncryptPKCS5PaddingWithIV 实现加密WithIV
func AESCBCPKCS5PaddingEncryptWithIV(origData []byte, key []byte, iv []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//对数据进行填充，让数据长度满足需求
	origData = PKCS5Padding(origData, block.BlockSize())
	//采用AES加密方法中CBC加密模式
	blocMode := cipher.NewCBCEncrypter(block, iv)
	crypted := make([]byte, len(origData))
	//执行加密
	blocMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

// AesCBCDecryptWithIV 实现解密WithIV
func AesCBCDecryptWithIV(cypted []byte, key []byte, iv []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//创建加密客户端实例
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(cypted))
	//这个函数也可以用来解密
	blockMode.CryptBlocks(origData, cypted)
	//去除填充字符串
	origData, err = PKCS7UnPadding(origData)
	if err != nil {
		return nil, err
	}
	return origData, err
}

func AESCFBEncrypt(key, data []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	data = PKCS7Padding(data, aes.BlockSize)
	cipherText := make([]byte, aes.BlockSize+len(data))
	iv := cipherText[:aes.BlockSize]
	if _, err = io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(cipherText[aes.BlockSize:], data)
	return cipherText, nil
}

func AESCFBDecrypt(key, ciphertext []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	if len(ciphertext) < aes.BlockSize {
		return nil, errors.New("Ciphertext block size is too short!")
	}
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)
	return PKCS7UnPadding(ciphertext)
}

func InterceptStringWith3Dots(resStr string, strLen int) string {
	result := resStr
	r := []rune(resStr)
	if len(r) >= strLen {
		result = string(r[:strLen]) + "..."
	}
	return result
}

func ConvertBoolToInt(value bool) int {
	if value {
		return 1
	} else {
		return 0
	}
}

func GetNowDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now()
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastMinuteDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Minute)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastTenMinuteDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Minute * 10)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetYesterdayDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour * 24)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}
