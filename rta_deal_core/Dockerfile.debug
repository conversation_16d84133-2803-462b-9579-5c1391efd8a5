FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401agolang1.22-holoclientc20250102 as go

WORKDIR /src
COPY ./ /src

ARG GIT_USER
ARG GIT_TOKEN

RUN yum install -y tzdata libpq jemalloc vim supervisor unzip git procps-ng

RUN echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc && \
    go env -w GO111MODULE=on && \
    go env -w GOPROXY=https://goproxy.cn,direct && \
    go env -w GOPRIVATE=codeup.aliyun.com && \
    cp assets/ip2region.db ./

RUN cd /src && \
    CGO_ENABLED=1 GOOS=linux go build -o main && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p /src/logs/
RUN mkdir -p /src/runtime/nginx/logs

# 设置日志环境变量
ENV LOG_LEVEL="info" \
    LOG_DIR="/src/logs" \
    LOG_FILENAME="rta_core.log" \
    LOG_MAX_SIZE="1000" \
    LOG_MAX_AGE="1" \
    LOG_MAX_BACKUPS="1" \
    LOG_STDOUT="true" \
    LOG_REPORT_CALLER="true" \
    LOG_JSON_FORMAT="false"

CMD /src/run.sh

# CMD [ "tail", "-f", "/dev/null" ]
