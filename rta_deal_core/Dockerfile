FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401agolang1.22-holoclientc20250102 as go

WORKDIR /src
COPY ./ /src

ARG GIT_USER
ARG GIT_TOKEN

RUN yum install -y tzdata libpq jemalloc unzip git

RUN echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc && \
    go env -w GO111MODULE=on && \
    go env -w GOPROXY=https://goproxy.cn,direct && \
    go env -w GOPRIVATE=codeup.aliyun.com && \
    CGO_ENABLED=1 GOOS=linux go build -o main

###

FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401golang1.22-base20250102

RUN yum install -y tzdata libpq jemalloc procps-ng

RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

# 创建日志目录
RUN mkdir -p /src/logs && chmod 755 /src/logs

COPY --from=go /src/main /app
COPY --from=go /src/assets/ip2region.db /app
COPY --from=go /usr/local/lib64/libholo-client.so /usr/local/lib64/libholo-client.so
COPY --from=go /usr/lib/liblog4c.* /usr/lib/
COPY --from=go /src/assets/log4crc /app

RUN echo "/usr/local/lib64" >> /etc/ld.so.conf.d/holo.conf && \
    echo "/usr/lib" >> /etc/ld.so.conf.d/holo.conf && \
    ldconfig

# 设置日志环境变量
ENV LOG_LEVEL="info" \
    LOG_DIR="/src/logs" \
    LOG_FILENAME="rta_core.log" \
    LOG_MAX_SIZE="1000" \
    LOG_MAX_AGE="1" \
    LOG_MAX_BACKUPS="1" \
    LOG_STDOUT="true" \
    LOG_REPORT_CALLER="true" \
    LOG_JSON_FORMAT="false"

CMD [ "/app/main" ]