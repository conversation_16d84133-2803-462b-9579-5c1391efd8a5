package api

import (
	"context"
	"rta_core/db"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/segmentio/kafka-go"
)

func TestKafka(t *testing.T) {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

	key := "key_p"
	parentCtx := context.Background()

	ctx, cancel := context.WithTimeout(parentCtx, 10*time.Second)
	defer cancel()

	product, err := db.GetProducer(ctx)
	if err != nil {
		logger.GetSugaredLogger().Errorf("kafka test GetProducer err: %v", err)
		return
	}

	go func() {
		msgs := []*databases.Message{
			{
				Key:   []byte(key),
				Value: []byte(time.Now().Format("2006-01-02 15:04:05")),
			},
		}

		err = product.Produce(ctx, msgs)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test Produce err: %v", err)
			return
		}
	}()

	consumer, err := db.GetConsumer(ctx)
	if err != nil {
		logger.GetSugaredLogger().Errorf("kafka test GetConsumer err: %v", err)
		return
	}

	go func() {
		err = consumer.Consume(ctx, ConsumeCallback)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test Consume err: %v", err)
			return
		}
	}()

	select {
	case <-ctx.Done():
		logger.GetSugaredLogger().Info("kafka test ctx timeout done")
		return
	}
}

func TestOld(t *testing.T) {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

	reqKey := "org_key_p"
	parentCtx := context.Background()

	ctx, cancel := context.WithTimeout(parentCtx, 15*time.Second)
	defer cancel()

	breaker := []string{"alikafka-serverless-cn-fhh3zns0s04-1000.alikafka.aliyuncs.com:9093", "alikafka-serverless-cn-fhh3zns0s04-2000.alikafka.aliyuncs.com:9093", "alikafka-serverless-cn-fhh3zns0s04-3000.alikafka.aliyuncs.com:9093"}
	producer := kafka.Writer{
		Addr:     kafka.TCP(breaker...),
		Balancer: &kafka.Hash{},
		Topic:    db.TestTopic,
		// Async:    true,
	}

	go func() {
		key := []byte(reqKey)
		content := []byte("123")
		err := producer.WriteMessages(ctx, kafka.Message{
			Key:   key,
			Value: content,
		})

		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test orgWriteMessages err: %v", err)
		}
	}()
	defer producer.Close()

	go func() {
		deviceDauSupplyConfig := kafka.ReaderConfig{
			Brokers:           breaker,
			GroupID:           db.ConsumerGroup,
			Topic:             db.TestTopic,
			QueueCapacity:     2000,    // Kafka队列容量
			MaxBytes:          1000000, // Kafka最大消息大小
			ReadBatchTimeout:  time.Duration(40) * time.Second,
			CommitInterval:    time.Duration(10) * time.Second,
			HeartbeatInterval: time.Duration(3) * time.Second,
			SessionTimeout:    time.Duration(120) * time.Second,
			MaxWait:           time.Duration(10) * time.Second,
		}

		kafkaReader := kafka.NewReader(deviceDauSupplyConfig)

		for {
			m, err := kafkaReader.ReadMessage(ctx)
			if err != nil {
				logger.GetSugaredLogger().Errorf("kafka test read message error:%v\n", err)
				break
			}
			logger.GetSugaredLogger().Infof("kafka test read message key:%v, value:%v, error:%v\n", m.Key, m.Value, err)

			select {
			case <-ctx.Done():
				break
			}
		}
	}()

	select {
	case <-ctx.Done():
		logger.GetSugaredLogger().Info("kafka test ctx timeout2 done")
		return

	}
}
