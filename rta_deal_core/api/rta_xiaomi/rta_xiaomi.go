package rta_xiaomi

import (
	"encoding/json"
	"rta_core/db"
	"rta_core/utils"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	rtacore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"

	"github.com/gin-gonic/gin"
)

// HandleByXiaoMi ...
func HandleByXiaoMi(c *gin.Context, channel string) (*XiaoMiResp, int) {
	// logger.Debug("rta_xiaomi")
	log := logger.GetSugaredLogger()

	bodyContent, err := c.GetRawData()
	if err != nil {
		log.Errorf("rta_xiaomi GetRawData error=%v", err)
		return nil, 204
	}

	// 判断是否开启gzip
	// acceptEncoding := c.GetHeader("Accept-Encoding")
	// contentEncoding := c.GetHeader("Content-Encoding")

	// log.Debugf("rta_xiaomi header all: %v", c.Request.Header)
	// log.Debugf("rta_xiaomi header accept-encodeing: %v", acceptEncoding)
	// log.Debugf("rta_xiaomi header content-encoding: %v", contentEncoding)

	// if strings.Contains(acceptEncoding, "gzip") && strings.Contains(contentEncoding, "gzip") {
	// 	buf := bytes.NewBuffer(bodyContent)
	// 	reader, _ := gzip.NewReader(buf)
	// 	defer reader.Close()
	// 	bodyContent, _ = io.ReadAll(reader)
	// }

	// log.Infof("rta_xiaomi postbody =%v", string(bodyContent))
	var xiaomiReq XiaoMiReq
	err = json.Unmarshal(bodyContent, &xiaomiReq)
	if err != nil {
		log.Errorf("rta_xiaomi parser error=%v", err)
		return nil, 204
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	osv := "10"
	imeiMd5 := ""
	oaid := ""
	if xiaomiReq.DIDType == 0 {
		osv = "9"
		imeiMd5 = xiaomiReq.Imei2
	} else if xiaomiReq.DIDType == 1 {
		osv = "10"
		oaid = xiaomiReq.DID
	}
	deviceInfo := rtacoremodels.MHDeviceStu{
		Os:      "android",
		Osv:     osv,
		Ua:      "",
		IP:      "",
		Idfa:    "",
		Oaid:    oaid,
		ImeiMd5: imeiMd5,
	}

	// if true {
	// 	tmpJsonData, _ := json.Marshal(deviceInfo)
	// 	log.Debugf("rta_xiaomi rta device: %s", string(tmpJsonData))
	// }
	////////////////////////////////////////////////////////////////////////////////////////////////////
	resp := XiaoMiResp{}
	resp.Errno = 0
	resp.Message = "success"
	resp.BidResp = XiaoMiRespBidResp{
		ID:      xiaomiReq.ID,
		BidID:   xiaomiReq.ID,
		ResType: 0,
	}
	var bidRespAdRespArray []XiaoMiRespBidRespAdResp
	for _, adReq := range xiaomiReq.AdReqs {
		var tmpBidRespAdResp XiaoMiRespBidRespAdResp
		tmpBidRespAdResp.Token = adReq.Token
		// rta ok or not
		isOK, _ := rtacore.IsRTAOKWithTimeout(c, db.GetRedis(), db.GlbMySQLDb, db.GlbBigCacheMinute, strings.Replace(adReq.Token, "maplehaze_", "", -1), &deviceInfo)
		if isOK {
			tmpBidRespAdResp.Ac = "y"
		} else {
			tmpBidRespAdResp.Ac = "n"
		}

		tmpBidRespAdResp.CacheTime = -1

		bidRespAdRespArray = append(bidRespAdRespArray, tmpBidRespAdResp)
	}

	resp.BidResp.AdResps = bidRespAdRespArray

	// for debug
	// if true {
	// 	tmpJsonData, _ := json.Marshal(resp)
	// 	log.Infof("rta_xiaomi rta resp: %v", string(tmpJsonData))
	// }

	return &resp, 200
}

// XiaoMiReq ...
type XiaoMiReq struct {
	ID      string              `json:"id"`
	Version string              `json:"v"`
	DID     string              `json:"did"`
	DIDType int                 `json:"didType"`
	Imei2   string              `json:"imei2"`
	Test    int                 `json:"test"`
	AdReqs  []XiaoMiReqAdMiReqs `json:"adReqs"`
}

// XiaoMiReqAdMiReqs ...
type XiaoMiReqAdMiReqs struct {
	Token string `json:"token"`
}

// XiaoMiResp ...
type XiaoMiResp struct {
	Errno   int               `json:"errno"`
	Message string            `json:"msg"`
	BidResp XiaoMiRespBidResp `json:"bidResp"`
}

// XiaoMiRespBidResp ...
type XiaoMiRespBidResp struct {
	ID      string                    `json:"id"`
	BidID   string                    `json:"bidId"`
	ResType int                       `json:"resType"`
	AdResps []XiaoMiRespBidRespAdResp `json:"adResps"`
}

// XiaoMiRespBidRespAdResp ...
type XiaoMiRespBidRespAdResp struct {
	Token     string `json:"token"`
	Ac        string `json:"ac"`
	CacheTime int    `json:"cacheTime"`
}

type CAIDMultiSort []rtacoremodels.MHDeviceCAIDMulti

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].CAIDVersion) > utils.ConvertStringToInt(s[j].CAIDVersion)
}
