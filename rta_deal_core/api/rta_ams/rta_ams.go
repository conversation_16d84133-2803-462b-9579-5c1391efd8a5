package rta_ams

import (
	"rta_core/db"
	"rta_core/pb/ams_rta"
	"rta_core/utils"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	rtacore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"google.golang.org/protobuf/proto"

	"github.com/gin-gonic/gin"
)

// HandleByAMS ...
func HandleByAMS(c *gin.Context, channel string) *ams_rta.RtaResponse {
	// logger.Debug("rta_ams")
	log := logger.GetSugaredLogger()

	// TODO 待定
	rtaID := "???"

	bodyContent, err := c.GetRawData()
	if err != nil {
		log.Errorf("rta_ams HandleByAMS GetRawData err: %v", err)
		return amsNoBidReturn("error", "parser raw_data error")
	}

	amsReq := &ams_rta.RtaRequest{}
	err = proto.Unmarshal(bodyContent, amsReq)
	if err != nil {
		log.Errorf("rta_ams HandleByAMS proto.Unmarshal err: %v", err)
		return amsNoBidReturn("error", "parser pb error")
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	os := ""
	if amsReq.Device.Os == ams_rta.RtaRequest_OS_ANDROID.Enum() {
		os = "android"
	} else if amsReq.Device.Os == ams_rta.RtaRequest_OS_IOS.Enum() {
		os = "ios"
	}
	if len(os) == 0 {
		return amsNoBidReturn(amsReq.GetId(), "wrong os")
	}

	// TODO
	osv := "10"
	////////////////////////////////////////////////////////////////////////////////////////////////////
	deviceInfo := rtacoremodels.MHDeviceStu{
		Os:      strings.ToLower(os),
		Osv:     osv,
		Ua:      "",
		IP:      amsReq.Device.GetIp(),
		ImeiMd5: amsReq.Device.GetImeiMd5Sum(),
		Idfa:    "",
		IdfaMd5: amsReq.Device.GetIdfaMd5Sum(),
		Oaid:    amsReq.Device.GetOaid(),
		OaidMd5: amsReq.Device.GetOaidMd5Sum(),
	}
	if os == "ios" {
		for _, item := range amsReq.Device.GetQaidInfos() {
			var tmpCaidInfo rtacoremodels.MHDeviceCAIDMulti
			tmpCaidInfo.CAID = item.GetQaid()
			tmpCaidInfo.CAIDVersion = item.GetOriginVersion()
			deviceInfo.CAIDMulti = append(deviceInfo.CAIDMulti, tmpCaidInfo)
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	isOK, _ := rtacore.IsRTAOKWithTimeout(c, db.GetRedis(), db.GlbMySQLDb, db.GlbBigCacheMinute, rtaID, &deviceInfo)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if isOK {
	} else {
		return amsNoBidReturn(amsReq.GetId(), "rta is not ok")
	}

	amsResp := &ams_rta.RtaResponse{
		RequestId: amsReq.Id,
		Code:      proto.Uint32(0),
	}

	// TODO 待定
	// amsResp.OutTargetId = append(amsResp.OutTargetId, "")

	return amsResp
}

func amsNoBidReturn(reqID string, reason string) *ams_rta.RtaResponse {
	// log := logger.GetSugaredLogger()
	// log.Infof("amsNoBidReturn reqId=%v, reason=%v", reqID, reason)
	amsResp := &ams_rta.RtaResponse{
		RequestId: proto.String(reqID),
		Code:      proto.Uint32(1),
	}
	return amsResp
}

type CAIDMultiSort []rtacoremodels.MHDeviceCAIDMulti

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].CAIDVersion) > utils.ConvertStringToInt(s[j].CAIDVersion)
}
