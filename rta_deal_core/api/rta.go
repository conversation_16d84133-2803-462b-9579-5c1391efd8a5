package api

import (
	"rta_core/api/rta_ams"
	"rta_core/api/rta_honor"
	"rta_core/api/rta_qtt"
	"rta_core/api/rta_xiaomi"
	"rta_core/consts"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	"github.com/gin-gonic/gin"
)

// RTAPostRequest http://localhost:8080/request?channel=1
func RTAPostRequest(c *gin.Context) {
	log := logger.GetSugaredLogger()
	channel := c.Query("channel")

	switch channel {
	case consts.RTAChannelHonor:
		// honor
		resp, code := rta_honor.HandleByHonor(c, channel)
		c.PureJ<PERSON>N(code, resp)
	case consts.RTAChannelAMS:
		// ams
		resp := rta_ams.HandleByAMS(c, channel)
		c.ProtoBuf(200, resp)
	case consts.RTAChannelBaidu:
		// baidu
		// resp := rta_baidu.HandleByBaiDu(c, channel)
		// c.ProtoBuf(200, resp)
	case consts.RTAChannelXiaomi:
		// xiaomi
		resp, code := rta_xiaomi.HandleByXiaoMi(c, channel)
		c.PureJSON(code, resp)
	case consts.RTAChannelQTT:
		// qtt
		resp := rta_qtt.HandleByQTT(c, channel)
		c.ProtoBuf(200, resp)
	default:
		log.Infof("empty channel post: %s", channel)
	}
}
