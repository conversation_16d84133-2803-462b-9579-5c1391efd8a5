package rta_honor

import (
	"context"
	"encoding/json"
	"rta_core/db"
	"rta_core/utils"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	rtacore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"

	"github.com/gin-gonic/gin"
)

// HandleByHonor ...
func HandleByHonor(c *gin.Context, channel string) (*HonorResp, int) {
	// logger.Debug("rta_honor")
	log := logger.GetSugaredLogger()
	// startTime := time.Now()
	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	// acceptEncoding := c.<PERSON>Header("Accept-Encoding")
	// contentEncoding := c.Get<PERSON>eader("Content-Encoding")

	// log.Debugf("rta_honor header all: %v", c.Request.Header)
	// log.Debugf("rta_honor header accept-encodeing: %v", acceptEncoding)
	// log.Debugf("rta_honor header content-encoding: %v", contentEncoding)

	// log.Infof("rta_honor postbody: %v", string(bodyContent))
	var honorReq HonorReq
	err := json.Unmarshal(bodyContent, &honorReq)
	if err != nil {
		log.Errorf("rta_honor parser error: %v", err)
		return nil, 204
	}
	// log.Debugf("honor req: %v", honorReq)

	ctx, cancel := context.WithTimeout(c, 100*time.Millisecond)
	defer cancel() // 确保在函数返回时取消 context
	ch := make(chan HonorResp, 1)

	go func() {
		resp := HandleByHonorGoroutine(ctx, honorReq)
		ch <- resp
		close(ch)
	}()

	// 使用 select 语句监听 context 的 Done() 通道
	select {
	case <-ctx.Done():
		// 如果 Done() 通道被关闭，说明 context 被取消或超时
		resp := HonorResp{}
		resp.Code = -1
		resp.Message = "failed"
		resp.Interested = "N"

		// log.Infof("rta_honor rsp ctx cancel or timeout, requestId: %v, elapsed: %v", honorReq.RequestId, time.Since(startTime))
		return &resp, 200
	case res := <-ch:
		// jsonData, _ := json.Marshal(res)
		// log.Infof("rta_honor rsp requestId:%v, elapsed: %v, resp=%v", honorReq.RequestId, time.Since(startTime), string(jsonData))
		return &res, 200
	}
}

func HandleByHonorGoroutine(c context.Context, honorReq HonorReq) HonorResp {

	// 参竟oaidMd5: 2089450e7559089a2dae00e3c0c4d9a3
	// 不参竟oaidMd5: 3a3f844d63bfd2a8b4d3b114889b2d03
	// if honorReq.Device.OaidMd5 == "2089450e7559089a2dae00e3c0c4d9a3" {
	// 	var rtaInfoArray []HonorRespUserInfoRtaInfo
	// 	for _, rtaId := range honorReq.RtaIdList {
	// 		var tmpRtaInfo HonorRespUserInfoRtaInfo
	// 		tmpRtaInfo.RtaId = rtaId
	// 		rtaInfoArray = append(rtaInfoArray, tmpRtaInfo)
	// 	}

	// 	resp := HonorResp{}
	// 	resp.Code = 0
	// 	resp.Message = "success"
	// 	resp.Interested = "Y"
	// 	resp.UserInfo = HonorRespUserInfo{
	// 		RtaInfoList: rtaInfoArray,
	// 	}
	// 	return &resp, 200
	// } else if honorReq.Device.OaidMd5 == "3a3f844d63bfd2a8b4d3b114889b2d03" {
	// 	resp := HonorResp{}
	// 	resp.Code = -1
	// 	resp.Message = "failed"
	// 	resp.Interested = "N"
	// 	return &resp, 200
	// }

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 目前只支持1个rta_id_list
	// if len(honorReq.RtaIdList) != 1 {
	// 	return nil, 204
	// }
	////////////////////////////////////////////////////////////////////////////////////////////////////
	deviceInfo := rtacoremodels.MHDeviceStu{
		Os:      strings.ToLower(honorReq.Device.Os),
		Osv:     honorReq.Device.Osv,
		Ua:      honorReq.Device.Ua,
		IP:      honorReq.Device.IP,
		Idfa:    honorReq.Device.Ifa,
		Oaid:    honorReq.Device.Oaid,
		OaidMd5: strings.ToLower(honorReq.Device.OaidMd5),
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	var rtaOKIdList []string
	for _, rtaId := range honorReq.RtaIdList {
		// logger.GetSugaredLogger().Infof("honor rta rtaId:%v", rtaId)
		// go func() {
		// 	marshalStr, _ := json.Marshal(deviceInfo)
		// 	go models.BigDataHoloDebugRTA(fmt.Sprintf("honor_%v", rtaId), string(marshalStr), 1)
		// }()

		isOK, err := rtacore.IsRTAOKWithTimeout(c, db.GetRedis(), db.GlbMySQLDb, db.GlbBigCacheMinute, rtaId, &deviceInfo)
		if err != nil {
			// if err.Error() != redis.Nil.Error() {
			// 	logger.GetSugaredLogger().Errorf("debug honor rta err:%v", err)
			// }
			continue
		}

		if isOK {
			rtaOKIdList = append(rtaOKIdList, rtaId)
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if len(rtaOKIdList) == 0 {
		// log.Info("rta_honor rta failed.")
		resp := HonorResp{}
		resp.Code = -1
		resp.Message = "failed"
		resp.Interested = "N"
		return resp
	}

	// log.Info("rta_honor rta success.")
	var rtaInfoArray []HonorRespUserInfoRtaInfo
	for _, rtaId := range rtaOKIdList {
		var tmpRtaInfo HonorRespUserInfoRtaInfo
		tmpRtaInfo.RtaId = rtaId
		rtaInfoArray = append(rtaInfoArray, tmpRtaInfo)
	}

	resp := HonorResp{}
	resp.Code = 0
	resp.Message = "success"
	resp.Interested = "Y"
	resp.UserInfo = HonorRespUserInfo{
		RtaInfoList: rtaInfoArray,
	}

	// jsonData, _ := json.Marshal(resp)
	// log.Infof("honor debug resp: %v", string(jsonData))
	return resp
}

// HonorReq ...
type HonorReq struct {
	// HONOR Ads 生成，全局唯一的请求 ID。最长支持 128 个字符
	RequestId string `json:"requestId"`
	// 请求时间，单位 ms
	RequestTime int64 `json:"requestTime"`
	// RTA ID 列表
	RtaIdList []string `json:"rtaIdList"`
	// 设备信息
	Device HonorReqDevice `json:"device"`
}

// HonorReqDevice ...
type HonorReqDevice struct {
	// 浏览器 User Agent，明文
	Ua string `json:"ua"`
	// 用户 IPv4 地址，例如:***************
	IP string `json:"ip"`
	// 设备传播名称
	PropagationName string `json:"propagationName"`
	// 设备操作系统，e.g. android
	Os string `json:"os"`
	// 设备操作系统版本号
	Osv string `json:"osv"`
	// 广告标识，IOS 为 IDFA，Android 为 OAID 或 GAID
	Ifa string `json:"ifa"`
	// 设备 OAID
	Oaid string `json:"oaid"`
	// 设备 OAID MD5
	OaidMd5 string `json:"oaidMd5"`
}

// HonorResp ...
type HonorResp struct {
	// 响应码，广告主侧定义。0 表示成功，其它表示失败
	Code int `json:"code"`
	// 响应描述信息
	Message string `json:"message,omitempty"`
	// 是否对请求中的用户感兴趣:Y / N
	Interested string `json:"interested,omitempty"`
	// 用户信息
	UserInfo HonorRespUserInfo `json:"userInfo,omitempty"`
}

// HonorRespUserInfo ...
type HonorRespUserInfo struct {
	// RTA信息列表
	RtaInfoList []HonorRespUserInfoRtaInfo `json:"rtaInfoList,omitempty"`
	// 如有返回值则只投放指定的创意，最多只支持 100 个，超出部分弃 用
	CreativeIdList []string `json:"creativeIdList,omitempty"`
}

// HonorRespUserInfoRtaInfo ...
type HonorRespUserInfoRtaInfo struct {
	// 参竞时返回对应参竞的rtaid
	RtaId string `json:"rtaId,omitempty"`

	// 调价比率, 为空, 不传或默认都为1
	// PriceRate float32 `json:"priceRate,omitempty"`
}

type CAIDMultiSort []rtacoremodels.MHDeviceCAIDMulti

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].CAIDVersion) > utils.ConvertStringToInt(s[j].CAIDVersion)
}
