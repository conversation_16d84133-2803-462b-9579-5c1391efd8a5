package api

import (
	"context"
	"rta_core/db"
	"rta_core/utils"
	"runtime"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	"github.com/gin-gonic/gin"
	"github.com/segmentio/kafka-go"
)

var (
	breaker = []string{"alikafka-serverless-cn-fhh3zns0s04-1000-vpc.alikafka.aliyuncs.com:9092", "alikafka-serverless-cn-fhh3zns0s04-2000-vpc.alikafka.aliyuncs.com:9092", "alikafka-serverless-cn-fhh3zns0s04-3000-vpc.alikafka.aliyuncs.com:9092"}
)

// Test http://localhost:8081/api/test
func Test(c *gin.Context) {

	// log.Println("runtime num cpu:", runtime.NumCPU())
	// log.Println("runtime go max procs:", runtime.GOMAXPROCS(-1))

	cpuNum := utils.ConvertIntToString(runtime.NumCPU())
	goMaxProcsNum := utils.ConvertIntToString(runtime.GOMAXPROCS(-1))
	logger.GetSugaredLogger().Infof("runtime go max procs, cpu=%v, goMaxProcsNum=%v\n", cpuNum, goMaxProcsNum)
	c.JSON(200, gin.H{
		"message": "rta-core2 server OK" + ", runtime num cpu: " + cpuNum + ", runtime GOMAXPROCS: " + goMaxProcsNum,
	})
}

func OldKafka(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			logger.GetSugaredLogger().Errorf("kafka test panic2: %v", err)
		}
	}()

	reqKey := c.DefaultQuery("key", "org_key_p")
	parentCtx := c.Request.Context()

	ctx, cancel := context.WithTimeout(parentCtx, 15*time.Second)
	defer cancel()

	producer := kafka.Writer{
		Addr:     kafka.TCP(breaker...),
		Balancer: &kafka.Hash{},
		Topic:    db.TestTopic,
		Async:    true,
	}

	go func() {
		key := []byte(reqKey)
		content := []byte("123")
		err := producer.WriteMessages(ctx, kafka.Message{
			Key:   key,
			Value: content,
		})

		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test orgWriteMessages err: %v", err)
		}
	}()
	defer producer.Close()

	go func() {
		deviceDauSupplyConfig := kafka.ReaderConfig{
			Brokers:           breaker,
			GroupID:           db.ConsumerGroup,
			Topic:             db.TestTopic,
			QueueCapacity:     2000,    // Kafka队列容量
			MaxBytes:          1000000, // Kafka最大消息大小
			ReadBatchTimeout:  time.Duration(40) * time.Second,
			CommitInterval:    time.Duration(10) * time.Second,
			HeartbeatInterval: time.Duration(3) * time.Second,
			SessionTimeout:    time.Duration(120) * time.Second,
			MaxWait:           time.Duration(10) * time.Second,
		}

		kafkaReader := kafka.NewReader(deviceDauSupplyConfig)

		for {
			m, err := kafkaReader.ReadMessage(ctx)
			if err != nil {
				logger.GetSugaredLogger().Errorf("kafka test read message error:%v\n", err)
				break
			}
			logger.GetSugaredLogger().Infof("kafka test read message key:%v, value:%v, error:%v\n", m.Key, m.Value, err)

			select {
			case <-ctx.Done():
				break
			}
		}
	}()

	select {
	case <-ctx.Done():
		logger.GetSugaredLogger().Info("kafka test ctx timeout2 done")
		c.JSON(200, gin.H{"message": "ctx timeout"})
		return

	}
}

// CheckData http://localhost:8081/api/testData?key=key_p
func NewKafka(c *gin.Context) {

	defer func() {
		if err := recover(); err != nil {
			logger.GetSugaredLogger().Errorf("kafka test panic: %v", err)
		}
	}()

	key := c.DefaultQuery("key", "key_p")
	parentCtx := c.Request.Context()

	ctx, cancel := context.WithTimeout(parentCtx, 20*time.Second)
	defer cancel()

	go func() {
		product, err := db.GetProducer(ctx)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test GetProducer err: %v", err)
			return
		}
		msgs := []*databases.Message{
			{
				Key:   []byte(key),
				Value: []byte(time.Now().Format("2006-01-02 15:04:05")),
			},
		}

		logger.GetSugaredLogger().Info("kafka test start Producer")
		err = product.Produce(ctx, msgs)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test Produce err: %v", err)
			return
		}
	}()

	go func() {
		consumer, err := db.GetConsumer(ctx)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test GetConsumer err: %v", err)
			return
		}

		logger.GetSugaredLogger().Info("kafka test start Consumer")
		err = consumer.Consume(ctx, ConsumeCallback)
		if err != nil {
			logger.GetSugaredLogger().Errorf("kafka test Consume err: %v", err)
			return
		}
	}()

	select {
	case <-ctx.Done():
		logger.GetSugaredLogger().Info("kafka test ctx timeout done")
		c.JSON(200, gin.H{"message": "ctx timeout"})
		return

	}
	c.JSON(200, gin.H{"message": "complate"})
}

func ConsumeCallback(msg *kafka.Message) error {
	logger.GetSugaredLogger().Infof("kafka test ConsumeCallback msg key: %v, value: %v", string(msg.Key), string(msg.Value))
	return nil
}
