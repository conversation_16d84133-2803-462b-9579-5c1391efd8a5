package api

import (
	"rta_core/db"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	"github.com/gin-gonic/gin"
)

func DebugSaveToOCPXTest2(c *gin.Context) {
	log := logger.GetSugaredLogger()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.Errorf("debug ocpx panic: %v", err)
			}
		}()

		sql := "INSERT INTO deal.ocpx_test2 (" +
			"id, reqid, plan_id, os, osv, did_md5, imei, oaid, ip, ua, model, manufacturer, transform_type, dd, hh, mm, report_time" +
			") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, TO_TIMESTAMP($17))"
		_, err := db.GlbPostgresDb.Exec(sql,
			c.<PERSON>("id"),
			c.<PERSON>("reqid"),
			c.<PERSON>("planid"),
			c.<PERSON>("os"),
			c.Query("osv"),
			c.Query("didmd5"),
			c.Query("imei"),
			c.Query("oaid"),
			c.ClientIP(),
			c.GetHeader("User-Agent"),
			c.Query("model"),
			c.Query("manufacturer"),
			c.Query("actionType"),
			time.Now().Format("2006-01-02"),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now().UnixMilli()/1000,
		)
		if err != nil {
			log.Errorf("exec failed, err: %v", err)
			return
		}
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = ""

	respData := map[string]interface{}{}
	respData["callback"] = "ok"
	resp["data"] = respData

	c.PureJSON(200, resp)
}
