package rta_baidu

import (
	"rta_core/db"
	"rta_core/pb/baidu_rta"
	"rta_core/utils"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	rtacore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"google.golang.org/protobuf/proto"

	"github.com/gin-gonic/gin"
)

// HandleByBaiDu ...
func HandleByBaiDu(c *gin.Context, channel string) *baidu_rta.RtaApiResponse {
	// logger.Debug("rta_baidu")

	log := logger.GetSugaredLogger()

	bodyContent, err := c.GetRawData()
	if err != nil {
		log.Errorf("HandleByBaiDu GetRawData error=%v", err)
		return baiduNoBidReturn(0, "parser raw_data error")
	}

	baiduReq := &baidu_rta.RtaApiRequest{}
	err = proto.Unmarshal(bodyContent, baiduReq)
	if err != nil {
		log.Errorf("HandleByBaiDu proto.Unmarshal error=%v", err)
		return baiduNoBidReturn(0, "parser pb error")
	}

	////////////////////////////////////////////////////////////////////////////////////////
	// tmpReqByte, _ := json.Marshal(baiduReq)
	// log.Infof("baidu req=%v", string(tmpReqByte))
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if len(baiduReq.GetPrefetchDate()) > 0 {
		return baiduNoBidReturn(baiduReq.GetQid(), "wrong prefetch_date")
	}

	os := ""
	if baiduReq.GetOsType() == baidu_rta.OsType_ANDROID {
		os = "android"
	} else if baiduReq.GetOsType() == baidu_rta.OsType_IOS {
		os = "ios"
	}
	if len(os) == 0 {
		return baiduNoBidReturn(baiduReq.GetQid(), "wrong os")
	}

	rtaID := c.Query("rtaid")
	if len(rtaID) == 0 {
		return baiduNoBidReturn(baiduReq.GetQid(), "no rtaid config")
	}

	osv := "10"
	if baiduReq.GetOsMajorVersion() > 0 {
		osv = utils.ConvertIntToString(int(baiduReq.GetOsMajorVersion()))
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// for debug
	// log.Debugf("baidu debug: %s", string(baiduReq.DeviceInfo.GetImeiMd5()))
	if string(baiduReq.DeviceInfo.GetImeiMd5()) == "cd9e459ea708a948d5c2f5a6ca8838cf" {
		baiduStrategyResults := &baidu_rta.RtaStrategyAdResult{
			RtaId: proto.Uint64(uint64(utils.ConvertStringToInt(strings.Replace(rtaID, "mh", "", -1)))),
		}
		var baiduStrategyResultArray []*baidu_rta.RtaStrategyAdResult
		baiduStrategyResultArray = append(baiduStrategyResultArray, baiduStrategyResults)

		baiduResp := &baidu_rta.RtaApiResponse{
			Qid:             baiduReq.Qid,
			StrategyResults: baiduStrategyResultArray,
			Res:             baidu_rta.ResType_ALL.Enum(),
			RtaBidType:      baidu_rta.RtaBidType_RTA_NORMAL.Enum(),
		}

		////////////////////////////////////////////////////////////////////////////////////////
		// tmpRespByte, _ := json.Marshal(baiduResp)
		// log.Infof("baidu debug resp=%v", string(tmpRespByte))
		////////////////////////////////////////////////////////////////////////////////////////

		return baiduResp
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	deviceInfo := rtacoremodels.MHDeviceStu{
		Os:      strings.ToLower(os),
		Osv:     osv,
		Ua:      "",
		IP:      "",
		ImeiMd5: strings.ToLower(string(baiduReq.DeviceInfo.GetImeiMd5())),
		Idfa:    strings.ToLower(string(baiduReq.DeviceInfo.GetIdfa())),
		IdfaMd5: strings.ToLower(string(baiduReq.DeviceInfo.GetIdfaMd5())),
		Oaid:    strings.ToLower(string(baiduReq.DeviceInfo.GetOaid())),
		OaidMd5: strings.ToLower(string(baiduReq.DeviceInfo.GetOaidMd5())),
	}
	if os == "ios" {
		for _, item := range baiduReq.DeviceInfo.GetCaidInfo() {
			var tmpCaidInfo rtacoremodels.MHDeviceCAIDMulti
			tmpCaidInfo.CAID = string(item.GetCaid())
			tmpCaidInfo.CAIDVersion = string(item.GetCaidVersion())
			deviceInfo.CAIDMulti = append(deviceInfo.CAIDMulti, tmpCaidInfo)
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	isOK, _ := rtacore.IsRTAOKWithTimeout(c, db.GetRedis(), db.GlbMySQLDb, db.GlbBigCacheMinute, rtaID, &deviceInfo)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	if isOK {
	} else {
		return baiduNoBidReturn(baiduReq.GetQid(), "rta is not ok")
	}

	baiduStrategyResults := &baidu_rta.RtaStrategyAdResult{
		RtaId: proto.Uint64(uint64(utils.ConvertStringToInt(strings.Replace(rtaID, "mh", "", -1)))),
	}
	var baiduStrategyResultArray []*baidu_rta.RtaStrategyAdResult
	baiduStrategyResultArray = append(baiduStrategyResultArray, baiduStrategyResults)

	baiduResp := &baidu_rta.RtaApiResponse{
		Qid:             baiduReq.Qid,
		StrategyResults: baiduStrategyResultArray,
		Res:             baidu_rta.ResType_ALL.Enum(),
		RtaBidType:      baidu_rta.RtaBidType_RTA_NORMAL.Enum(),
	}

	////////////////////////////////////////////////////////////////////////////////////////
	// tmpRespByte, _ := json.Marshal(baiduResp)
	// log.Infof("baidu resp=%v", string(tmpRespByte))
	////////////////////////////////////////////////////////////////////////////////////////

	return baiduResp
}

func baiduNoBidReturn(reqID uint64, reason string) *baidu_rta.RtaApiResponse {
	// log := logger.GetSugaredLogger()
	// log.Infof("baiduNoBidReturn reason=%v", reason)
	baiduResp := &baidu_rta.RtaApiResponse{
		Qid: proto.Uint64(reqID),
		Res: baidu_rta.ResType_NONE.Enum(),
	}
	return baiduResp
}

type CAIDMultiSort []rtacoremodels.MHDeviceCAIDMulti

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].CAIDVersion) > utils.ConvertStringToInt(s[j].CAIDVersion)
}
