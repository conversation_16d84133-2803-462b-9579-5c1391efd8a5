package rta_qtt

import (
	"encoding/json"
	"fmt"
	"rta_core/db"
	"rta_core/models"
	"rta_core/pb/qtt_rta"
	"strconv"
	"strings"
	"time"

	rtacore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"google.golang.org/protobuf/proto"

	"github.com/gin-gonic/gin"
)

var (
	// 文档：https://docs.qq.com/sheet/DSnlMZmRUc2ZMZ3dv?tab=BB08J2
	// 快手拉新rtaid:2879
	// 快极拉新rtaid:2880
	// 快手拉活rtaid:2881
	// 快极拉活rtaid: 2882
	qttRtaIdMap = map[string]string{
		"2879": "mh100001",
		"2880": "mh100002",
		"2881": "mh100003",
		"2882": "mh100004",
	} // 趣头条RTAID-maplehazeRtaID 映射
)

// HandleByQTT 处理趣头条RTA请求
func HandleByQTT(c *gin.Context, channel string) *qtt_rta.RTAResponse {
	log := logger.GetSugaredLogger()
	startTime := time.Now()

	// 1. 解析请求体
	bodyContent, err := c.GetRawData()
	if err != nil {
		log.Errorf("HandleByQTT GetRawData error=%v", err)
		return qttNoBidReturn("", "parser raw_data error")
	}

	qttReq := &qtt_rta.RTARequest{}
	err = proto.Unmarshal(bodyContent, qttReq)
	if err != nil {
		log.Errorf("HandleByQTT proto.Unmarshal error=%v", err)
		return qttNoBidReturn("", "parser pb error")
	}

	// 上报数据到holo
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("HandleByQTT report holo error=%v", r)
			}
		}()

		marshalStr, _ := json.Marshal(qttReq)
		models.BigDataHoloDebugRTA(fmt.Sprintf("qtt_%v", qttReq.RtaId), string(marshalStr), 1)
	}()

	// 2. 基本参数校验
	if qttReq.Device == nil {
		return qttNoBidReturn(qttReq.ReqId, "no device info")
	}

	// 3. 获取设备操作系统信息
	os := ""
	switch qttReq.Device.Os {
	case qtt_rta.OSType_ANDROID:
		os = "android"
	case qtt_rta.OSType_IOS:
		os = "ios"
	default:
		return qttNoBidReturn(qttReq.ReqId, "wrong os")
	}

	// 4. 构建设备信息
	deviceInfo := rtacoremodels.MHDeviceStu{
		Os: strings.ToLower(os),
		IP: qttReq.Device.Ip,
	}

	// 5. 处理设备ID
	for _, devID := range qttReq.Device.DevId {
		switch devID.Type {
		case qtt_rta.DeviceIdType_IMEI:
			if devID.IsMd5 {
				deviceInfo.ImeiMd5 = strings.ToLower(devID.Id)
			} else {
				deviceInfo.Imei = strings.ToLower(devID.Id)
			}
		case qtt_rta.DeviceIdType_IDFA:
			if devID.IsMd5 {
				deviceInfo.IdfaMd5 = strings.ToLower(devID.Id)
			} else {
				deviceInfo.Idfa = strings.ToLower(devID.Id)
			}
		case qtt_rta.DeviceIdType_OAID:
			if devID.IsMd5 {
				deviceInfo.OaidMd5 = strings.ToLower(devID.Id)
			} else {
				deviceInfo.Oaid = strings.ToLower(devID.Id)
			}
		case qtt_rta.DeviceIdType_CAID:
			var caidInfo rtacoremodels.MHDeviceCAIDMulti
			caidInfo.CAID = devID.Id
			caidInfo.CAIDVersion = devID.Ver
			deviceInfo.CAIDMulti = append(deviceInfo.CAIDMulti, caidInfo)
		}
	}

	// // todo... 接口联调
	// if deviceInfo.ImeiMd5 == "c35d02f274959c9df422c801647c2209" {
	// 	log.Infof("HandleByQTT imei is match, bid")
	// 	// 全部接受
	// 	return &qtt_rta.RTAResponse{
	// 		StatusCode: qtt_rta.StatusCode_BID_ALL,
	// 		Success:    true,
	// 		ReqId:      qttReq.ReqId,
	// 		CostTime:   time.Since(startTime).Milliseconds(),
	// 	}
	// } else if deviceInfo.ImeiMd5 == "f31c0c2827fc24540a78e3e8d4c6493b" {
	// 	log.Infof("HandleByQTT imei is match, but no bid")
	// 	// 全部放弃
	// 	return &qtt_rta.RTAResponse{
	// 		StatusCode: qtt_rta.StatusCode_BID_ABANDON,
	// 		Success:    true,
	// 		ReqId:      qttReq.ReqId,
	// 	}
	// } else {
	// 	log.Infof("HandleByQTT imei is not match, but no bid")
	// 	return qttNoBidReturn(qttReq.ReqId, "test case, imei is not match")
	// }

	// 6. 检查RTA配置
	rtaID := strconv.FormatInt(qttReq.RtaId, 10)
	if len(rtaID) == 0 {
		return qttNoBidReturn(qttReq.ReqId, "no rtaid config")
	}

	maplehazeRtaID, ok := qttRtaIdMap[rtaID]
	if !ok {
		return qttNoBidReturn(qttReq.ReqId, "no maplehazeRtaID config")
	}

	// 7. RTA 校验
	isOK, _ := rtacore.IsRTAOKWithTimeout(c, db.GetRedis(), db.GlbMySQLDb, db.GlbBigCacheMinute, maplehazeRtaID, &deviceInfo)
	log.Infof("HandleByQTT IsRTAOK isOK=%v, maplehazeRtaID/rtaId=%v/%v, qttReq=%v ", isOK, maplehazeRtaID, rtaID, qttReq.String())
	if !isOK {
		return &qtt_rta.RTAResponse{
			StatusCode: qtt_rta.StatusCode_BID_ABANDON,
			Success:    true,
			ReqId:      qttReq.ReqId,
		}
	}

	// 8. 构建响应
	resp := &qtt_rta.RTAResponse{
		StatusCode: qtt_rta.StatusCode_BID_ALL,
		Success:    true,
		ReqId:      qttReq.ReqId,
		CostTime:   time.Since(startTime).Milliseconds(),
	}

	return resp
}

// qttNoBidReturn 返回不参与竞价的响应
func qttNoBidReturn(reqID string, reason string) *qtt_rta.RTAResponse {
	log := logger.GetSugaredLogger()
	log.Infof("qttNoBidReturn reqID=%v, reason=%v", reqID, reason)

	return &qtt_rta.RTAResponse{
		StatusCode: qtt_rta.StatusCode_BID_ABANDON,
		Success:    false,
		ReqId:      reqID,
	}
}
