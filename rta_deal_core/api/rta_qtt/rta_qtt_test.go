package rta_qtt

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"rta_core/pb/qtt_rta"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
)

func setupTestContext() (*gin.Context, *httptest.ResponseRecorder) {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = &http.Request{
		Header: make(http.Header),
		URL:    &url.URL{},
	}
	return c, w
}

func TestHandleByQTT_BasicRequest(t *testing.T) {
	c, _ := setupTestContext()
	c.Request.URL.RawQuery = "rtaid=test_rta_id"

	// 构建基本请求
	req := &qtt_rta.RTARequest{
		ReqId: "test_req_id",
		Device: &qtt_rta.Device{
			Os: qtt_rta.OSType_ANDROID,
			DevId: []*qtt_rta.DeviceId{
				{
					Type:  qtt_rta.DeviceIdType_IMEI,
					Id:    "test_imei",
					IsMd5: true,
				},
			},
			Ip: "127.0.0.1",
		},
	}

	// 序列化请求
	data, err := proto.Marshal(req)
	assert.NoError(t, err)
	c.Request.Body = io.NopCloser(bytes.NewReader(data))

	resp := HandleByQTT(c, "test_channel")
	assert.NotNil(t, resp)
	assert.Equal(t, qtt_rta.StatusCode_BID_ALL, resp.StatusCode)
	assert.Equal(t, true, resp.Success)
	assert.Equal(t, req.ReqId, resp.ReqId)
}

func TestHandleByQTT_DeviceIDs(t *testing.T) {
	testCases := []struct {
		name     string
		deviceId *qtt_rta.DeviceId
	}{
		{
			name: "IMEI",
			deviceId: &qtt_rta.DeviceId{
				Type:  qtt_rta.DeviceIdType_IMEI,
				Id:    "test_imei",
				IsMd5: true,
			},
		},
		{
			name: "IDFA",
			deviceId: &qtt_rta.DeviceId{
				Type:  qtt_rta.DeviceIdType_IDFA,
				Id:    "test_idfa",
				IsMd5: false,
			},
		},
		{
			name: "OAID",
			deviceId: &qtt_rta.DeviceId{
				Type:  qtt_rta.DeviceIdType_OAID,
				Id:    "test_oaid",
				IsMd5: true,
			},
		},
		{
			name: "CAID",
			deviceId: &qtt_rta.DeviceId{
				Type:  qtt_rta.DeviceIdType_CAID,
				Id:    "test_caid",
				Ver:   "2.0",
				IsMd5: false,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			c, _ := setupTestContext()
			c.Request.URL.RawQuery = "rtaid=test_rta_id"

			req := &qtt_rta.RTARequest{
				ReqId: "test_req_id",
				Device: &qtt_rta.Device{
					Os:    qtt_rta.OSType_ANDROID,
					DevId: []*qtt_rta.DeviceId{tc.deviceId},
					Ip:    "127.0.0.1",
				},
			}

			data, err := proto.Marshal(req)
			assert.NoError(t, err)
			c.Request.Body = io.NopCloser(bytes.NewReader(data))

			resp := HandleByQTT(c, "test_channel")
			assert.NotNil(t, resp)
			assert.Equal(t, qtt_rta.StatusCode_BID_ALL, resp.StatusCode)
			assert.Equal(t, true, resp.Success)
		})
	}
}

func TestHandleByQTT_SecondaryBidding(t *testing.T) {
	c, _ := setupTestContext()
	c.Request.URL.RawQuery = "rtaid=test_rta_id"

	req := &qtt_rta.RTARequest{
		ReqId:   "test_req_id",
		ReqType: "ADLIST_REQUEST",
		Device: &qtt_rta.Device{
			Os: qtt_rta.OSType_ANDROID,
			DevId: []*qtt_rta.DeviceId{
				{
					Type:  qtt_rta.DeviceIdType_IMEI,
					Id:    "test_imei",
					IsMd5: true,
				},
			},
			Ip: "127.0.0.1",
		},
		AdInfos: []*qtt_rta.AdInfo{
			{
				AdId:      123,
				AdgroupId: 456,
				AccountId: 789,
			},
		},
	}

	data, err := proto.Marshal(req)
	assert.NoError(t, err)
	c.Request.Body = io.NopCloser(bytes.NewReader(data))

	resp := HandleByQTT(c, "test_channel")
	assert.NotNil(t, resp)
	assert.Equal(t, qtt_rta.StatusCode_BID_ALL, resp.StatusCode)
	assert.Equal(t, true, resp.Success)
	assert.NotEmpty(t, resp.AdResults)
	assert.Equal(t, int32(123), resp.AdResults[0].AdId)
	assert.Equal(t, float32(1.0), resp.AdResults[0].Boost)
}

func TestHandleByQTT_RtaId2879(t *testing.T) {
	c, _ := setupTestContext()
	c.Request.URL.RawQuery = "rtaid=test_rta_id"

	// 构建请求 - 快手拉新 rtaId: 2879
	req := &qtt_rta.RTARequest{
		ReqId: "test_req_id_2879",
		RtaId: 2879,
		Device: &qtt_rta.Device{
			Os: qtt_rta.OSType_ANDROID,
			DevId: []*qtt_rta.DeviceId{
				{
					Type:  qtt_rta.DeviceIdType_IMEI,
					Id:    "test_imei_2879",
					IsMd5: true,
				},
				{
					Type:  qtt_rta.DeviceIdType_OAID,
					Id:    "test_oaid_2879",
					IsMd5: true,
				},
			},
			Ip: "127.0.0.1",
		},
	}

	// 序列化请求
	data, err := proto.Marshal(req)
	assert.NoError(t, err)
	c.Request.Body = io.NopCloser(bytes.NewReader(data))

	// 调用HandleByQTT
	resp := HandleByQTT(c, "5")

	// 验证响应
	assert.NotNil(t, resp)
	assert.Equal(t, req.ReqId, resp.ReqId)

	// 检查映射是否正确 (2879 -> mh100001)
	// 注意：这个测试可能会失败，因为实际结果取决于rtacore.IsRTAOK的返回值
	// 如果需要模拟IsRTAOK的行为，可能需要使用mock
	fmt.Printf("Response for rtaId 2879: StatusCode=%v, Success=%v\n", resp.StatusCode, resp.Success)
}

func TestQttGoRestyRequest(t *testing.T) {

	qttReq := &qtt_rta.RTARequest{
		ReqId:   uuid.NewV4().String(),
		RtaId:   2879,
		ReqType: "ADLIST_REQUEST",
		AdInfos: []*qtt_rta.AdInfo{
			{
				AdId:      123,
				AdgroupId: 456,
				AccountId: 789,
			},
		},
		Device: &qtt_rta.Device{
			Os: qtt_rta.OSType_ANDROID,
			DevId: []*qtt_rta.DeviceId{
				{
					Type:  qtt_rta.DeviceIdType_IMEI,
					Id:    "***************",
					IsMd5: true,
				},
			},
		},
	}

	requestBytes, err := proto.Marshal(qttReq)
	if err != nil {
		fmt.Printf("marshal qttReq failed: %v\n", err)
		return
	}

	// 执行HTTP请求
	ctx := context.Background()
	bodyContent, retCode, err := core.GetHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		"https://rta.maplehaze.cn/request?channel=5",
		utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
		utilities.WithProtobufBody(requestBytes),
	)

	// 验证结果
	if err != nil {
		fmt.Printf("HTTP请求失败: %v\n", err)
		return
	}

	var qttResp qtt_rta.RTAResponse
	err = proto.Unmarshal(bodyContent, &qttResp)
	if err != nil {
		fmt.Printf("ks_rta resp: %v\n", qttResp)
		return
	}

	fmt.Printf("qtt resp: %v, retCode=%v\n", qttResp, retCode)
}
