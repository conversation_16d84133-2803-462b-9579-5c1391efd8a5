package models

import (
	"rta_core/db"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	uuid "github.com/satori/go.uuid"
)

// BigDataHoloDebugRTA ...
func BigDataHoloDebugRTA(rtaId string, test string, isPass int) {
	// log := logger.GetSugaredLogger()
	// log.Infof("holo debug rta, rtaId=%v", rtaId)

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "debug_rta"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("rtaid", rtaId, len(rtaId))
	put.SetTextValByColName("test", test, len(test))
	put.SetInt32ValByColName("is_pass", int32(isPass))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}
