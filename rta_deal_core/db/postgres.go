package db

import (
	"database/sql"
	"rta_core/config"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
)

var GlbPostgresDb *sql.DB

func InitPostgres() error {
	log := logger.GetSugaredLogger()

	////////////////////////////////////////////////////////////////////////////////////////////////
	db, err := sql.Open("postgres", config.POSTGRESSDSN)
	if err != nil {
		log.Errorf("postgres init error: %v", err)
		return err
	}
	err = db.Ping()
	if err != nil {
		log.Errorf("postgres ping error: %v", err)
		return err
	}
	////////////////////////////////////////////////////////////////////////////////////////////////

	GlbPostgresDb = db

	return nil
}

func ClosePostgres() {
	if GlbPostgresDb != nil {
		GlbPostgresDb.Close()
	}
}
