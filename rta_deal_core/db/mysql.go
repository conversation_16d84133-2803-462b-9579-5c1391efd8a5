package db

import (
	"database/sql"
	"rta_core/config"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// GlbMySQLDb ...
var GlbMySQLDb *sql.DB

// InitMysql ...
func InitMysql() (err error) {
	// db, err := sql.Open("mysql", "user:password@/dbname")
	// db, err := sql.Open("mysql", "root:12345678@tcp(127.0.0.1:3306)/adx")
	mysqlDB, err := sql.Open("mysql", config.MySQLUserName+":"+config.MySQLPassword+"@tcp("+config.MySQLHost+":"+config.MySQLPort+")/"+config.MySQLDbname)
	mysqlDB.SetMaxOpenConns(30)
	mysqlDB.SetMaxIdleConns(15)
	mysqlDB.SetConnMaxLifetime(10 * time.Minute)
	// log.Println(mysqlDB)
	// log.Println(err)

	GlbMySQLDb = mysqlDB

	if err != nil {
		return err
	}

	err = mysqlDB.Ping()
	if err != nil {
		return err
	}
	return nil
}

func CloseMysql() {
	if GlbMySQLDb != nil {
		GlbMySQLDb.Close()
	}
}
