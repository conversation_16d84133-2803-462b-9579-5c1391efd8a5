package db

import (
	"context"
	"log"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
)

const (
	TestTopic     = "100001"
	ConsumerGroup = "test"

	KafkaUserName = "alikafka_serverless-cn-fhh3zns0s04"
	KafkaPassword = "pB4bjjIuVxNFZU5B5ZeoJHy7YPo7kCX9"
)

var KafkaTestProducerClient databases.KafkaProducer

func GetProducer(ctx context.Context) (ret databases.KafkaProducer, err error) {
	if KafkaTestProducerClient != nil {
		return KafkaTestProducerClient, nil
	}

	KafkaTestProducerClient, err = databases.NewProducer(ctx, &databases.ProducerConfig{
		Basic: databases.KafkaBasicConfig{
			Topic:            TestTopic,
			BootstrapServers: "alikafka-serverless-cn-fhh3zns0s04-1000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-fhh3zns0s04-2000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-fhh3zns0s04-3000-vpc.alikafka.aliyuncs.com:9092",
		},
		// Security: &databases.KafkaSecurityConfig{
		// 	SecurityProtocol: "sasl_ssl",
		// 	SaslUsername:     KafkaUserName,
		// 	SaslPassword:     KafkaPassword,
		// 	SslCaLocation:    "/Users/<USER>/Documents/tools/only-4096-ca-cert",
		// },
		// Advanced: &databases.KafkaAdvancedConfig{
		// 	RequiredAcks: 0,
		// 	ErrorHandler: func(batch []*databases.Message, err error) {
		// 		// 直接打印每条消息的内容
		// 		log.Printf("生产者错误: %v\n消息内容:\n", err)
		// 		for i, msg := range batch {
		// 			log.Printf("[%d] %s\n", i, msg.String())
		// 		}
		// 	},
		// },
	})

	if err != nil {
		log.Printf("failed to init kafka test product client: %v", err)
	}

	return KafkaTestProducerClient, err
}

var KafkaTestConsumerClient databases.KafkaConsumer

func GetConsumer(ctx context.Context) (ret databases.KafkaConsumer, err error) {
	if KafkaTestConsumerClient != nil {
		return KafkaTestConsumerClient, nil
	}

	KafkaTestConsumerClient, err = databases.NewConsumer(ctx, &databases.ConsumerConfig{
		Basic: databases.KafkaBasicConfig{
			Topic:            TestTopic,
			BootstrapServers: "alikafka-serverless-cn-fhh3zns0s04-1000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-fhh3zns0s04-2000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-fhh3zns0s04-3000-vpc.alikafka.aliyuncs.com:9092",
		},
		GroupId: ConsumerGroup,
		// Security: &databases.KafkaSecurityConfig{
		// 	SecurityProtocol: "sasl_ssl",
		// 	SaslUsername:     KafkaUserName,
		// 	SaslPassword:     KafkaPassword,
		// 	SslCaLocation:    "/Users/<USER>/Documents/tools/only-4096-ca-cert",
		// },
	})
	if err != nil {
		log.Printf("failed to init kafka test consumer client: %v", err)
	}
	return KafkaTestConsumerClient, err
}
