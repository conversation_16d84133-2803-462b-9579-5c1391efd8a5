package db

import (
	"fmt"
	"log"
	"rta_core/config"
	"sync"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	_ "github.com/go-sql-driver/mysql"
)

var RedisClient databases.Redis
var onceRedisClient sync.Once

func InitRedis() (err error) {
	onceRedisClient.Do(func() {
		RedisClient, err = databases.NewRedisClient(databases.WithAddr(fmt.Sprintf("%v:%v", config.RedisHost, config.RedisPort)))
		if err != nil {
			log.Printf("failed to init redis client: %v", err)
		}
	})
	return err
}

func GetRedis() databases.Redis {
	return RedisClient
}

func CloseRedis() {
	if RedisClient == nil {
		return
	}
	RedisClient.Close()
}

// // GlbRedis ...
// var GlbRedis *redis.Client

// // GlbRedisTmp ...
// // var GlbRedisTmp *redis.Client

// // InitRedis ...
// func InitRedis() {
// 	// mysqlDB, err := sql.Open("mysql", config.MYSQL_USERNAME+":"+config.MYSQL_PASSWORD+"@tcp("+config.MYSQL_HOST+":"+config.MYSQL_PORT+")/"+config.MYSQL_DBNAME)

// 	redisClient := redis.NewClient(&redis.Options{
// 		// Addr:     "localhost:6379",
// 		Addr:     config.RedisHost + ":" + config.RedisPort,
// 		Password: "", // no password set
// 		DB:       0,  // use default DB
// 	})

// 	GlbRedis = redisClient

// 	// redisClientTmp := redis.NewClient(&redis.Options{
// 	// 	// Addr:     "localhost:6379",
// 	// 	Addr:     "r-2zej4pjq343qg6i0pu.redis.rds.aliyuncs.com" + ":" + config.RedisPort,
// 	// 	Password: "", // no password set
// 	// 	DB:       0,  // use default DB
// 	// })

// 	// GlbRedisTmp = redisClientTmp
// }

// func CloseRedis() {
// 	if GlbRedis != nil {
// 		GlbRedis.Close()
// 	}
// }

// // func InitRedis(c *gin.Context) (err error) {
// // 	redisClient := redis.NewClient(&redis.Options{
// // 		Addr:     "localhost:6379",
// // 		Password: "", // no password set
// // 		DB:       0,  // use default DB
// // 	})

// // 	pong, err := redisClient.Ping(c).Result()
// // 	log.Println(pong, err)
// // 	if err != nil {
// // 		return err
// // 	}

// // 	return nil
// // }
