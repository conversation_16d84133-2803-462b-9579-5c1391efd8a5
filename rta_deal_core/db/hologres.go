package db

import (
	"rta_core/config"
	"rta_core/utils"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

// writeMode -> update
var GlbHologresAdxSSPUpdateDataDb *holoclient.HoloClient
var GlbHologresAdxSSPUpdateDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

var GlbHologresAdxSSPDataDb *holoclient.HoloClient
var GlbHologresAdxSSPDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

func InitHologres() error {

	writeMode := utils.HologresWriteMode
	batchSize := utils.HologresBatchSize

	threadSize := utils.HologresThreadSize

	writeBatchByteSize := utils.HologresWriteBatchByteSize

	writeMaxIntervalMs := utils.HologresWriteMaxIntervalMs

	// adx_ssp writeMode -> update
	hologresUpdateClient := databases.NewHologres(
		config.HOLOGRESDSN,
		1,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxSSPUpdateDataDb = hologresUpdateClient

	// adx_ssp
	hologresAdxSSPDataClient := databases.NewHologres(
		config.HOLOGRESDSN,
		writeMode,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxSSPDataDb = hologresAdxSSPDataClient
	NewHologresAdxSSPDataTableSchema("debug", "debug_rta")

	// writeMode -> update
	NewHologresAdxSSPDataUpdateTableSchema("debug", "debug_rtb")

	return nil
}

func CloseHologres() {
	if GlbHologresAdxSSPUpdateDataDb != nil {
		GlbHologresAdxSSPUpdateDataDb.Close()
	}
	if GlbHologresAdxSSPDataDb != nil {
		GlbHologresAdxSSPDataDb.Close()
	}
}

func NewHologresAdxSSPDataUpdateTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxSSPUpdateDataTableSchemas[tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxSSPUpdateDataDb.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxSSPUpdateDataTableSchemas[tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}

func NewHologresAdxSSPDataTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxSSPDataTableSchemas[schemaName+tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxSSPDataDb.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxSSPDataTableSchemas[schemaName+tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}
