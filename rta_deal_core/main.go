package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"rta_core/db"
	"rta_core/router"
	"sync"
	"syscall"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	adCommonCore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"

	"net/http"
	_ "net/http/pprof"

	_ "go.uber.org/automaxprocs"
)

func main() {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 获取zap日志实例
	log := logger.GetSugaredLogger()

	// 确保程序退出前同步日志
	defer logger.Sync()

	// 初始化资源
	if err := initResources(); err != nil {
		log.Errorf("Failed to initialize resources: %v\n", err)
		return
	}

	// // Disable Console Color, you don't need console color when writing the logs to file.
	// gin.DisableConsoleColor()
	// // Force log's color
	// // gin.ForceConsoleColor()

	// // Use the following code if you need to write the logs to file and console at the same time.
	// gin.DefaultWriter = io.MultiWriter(f, os.Stdout)

	// gin.SetMode(gin.ReleaseMode)

	// pprof 服务
	pprofServer := &http.Server{
		Addr:    ":6060",
		Handler: nil,
	}
	go func() {
		if err := pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("pprof listen: %s", err)
		}
	}()

	// 初始化路由
	r := router.Init()

	// prometheus
	// p := ginprometheus.NewPrometheus("gin")
	// p.Use(router)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 在goroutine中启动服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("listen: %s", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	// kill (无参数) 默认发送 syscall.SIGTERM
	// kill -2 发送 syscall.SIGINT
	// kill -9 发送 syscall.SIGKILL，但无法被捕获，所以不需要添加
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("shutting down the server...")

	// 创建一个5秒超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 尝试优雅关闭主服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Errorf("shutdown server err=%v", err.Error())
	}

	// 尝试优雅关闭pprof服务器
	if err := pprofServer.Shutdown(ctx); err != nil {
		log.Errorf("shutdown pprof server err=%v", err.Error())
	}

	// 关闭数据库连接
	db.CloseMysql()
	db.CloseRedis()
	db.CloseBigCache()
	db.CloseHologres()
	db.ClosePostgres()

	// 释放HTTP客户端资源
	adCommonCore.CloseFastHTTPClient()

	log.Info("the server has gracefully exited")
}

// 初始化资源
func initResources() error {
	var wg sync.WaitGroup
	errChan := make(chan error, 5) // 用于接收初始化错误

	// 初始化redis
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitRedis(); err != nil {
			errChan <- fmt.Errorf("failed to init redis: %w", err)
		}
	}()

	// 初始化mysql
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitMysql(); err != nil {
			errChan <- fmt.Errorf("failed to init mysql: %w", err)
		}
	}()

	// 初始化clickhouse
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitPostgres(); err != nil {
			errChan <- fmt.Errorf("failed to init clickhouse: %w", err)
		}
	}()

	// 初始化bigcache
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitBigCache(); err != nil {
			errChan <- fmt.Errorf("failed to init bigcache: %w", err)
		}
	}()

	// 初始化hologres
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitHologres(); err != nil {
			errChan <- fmt.Errorf("failed to init hologres: %w", err)
		}
	}()

	// 等待所有初始化完成
	wg.Wait()
	close(errChan)

	// 检查是否有初始化错误
	var initErr error
	for err := range errChan {
		if err != nil {
			initErr = err
			break
		}
	}

	// 如果有错误，清理已初始化的资源
	if initErr != nil {
		// 清理资源
		db.CloseMysql()
		db.CloseRedis()
		db.ClosePostgres()
		db.CloseBigCache()
		db.CloseHologres()
		return initErr
	}

	return nil
}
