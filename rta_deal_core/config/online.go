package config

import (
	"log"
	"os"
	"strings"
)

// 环境变量相关常量
const (
	// MySQL 相关环境变量
	ENV_MYSQL_HOST       = "MYSQL_HOST"
	ENV_MYSQL_DBNAME     = "MYSQL_DBNAME"
	ENV_MYSQL_SSP_DBNAME = "MYSQL_SSP_DBNAME"
	ENV_MYSQL_USERNAME   = "MYSQL_USERNAME"
	ENV_MYSQL_PASSWORD   = "MYSQL_PASSWORD"
	ENV_MYSQL_PORT       = "MYSQL_PORT"

	// Redis 相关环境变量
	ENV_REDIS_HOST = "REDIS_HOST"
	ENV_REDIS_PORT = "REDIS_PORT"

	// Hologres 相关环境变量
	ENV_HOLOGRES_DSN = "HOLOGRES_DSN"

	// Postgres 相关环境变量
	ENV_POSTGRES_DSN = "POSTGRES_DSN"

	// 其他配置项
	ENV_IP_DB_PATH  = "IP_DB_PATH"
	ENV_ENCRYPT_KEY = "ENCRYPT_KEY"
)

// 默认配置值
var (
	// MySQL 默认配置
	MySQLHost      = getEnv(ENV_MYSQL_HOST, "")
	MySQLDbname    = getEnv(ENV_MYSQL_DBNAME, "")
	MySQLSSPDbname = getEnv(ENV_MYSQL_SSP_DBNAME, "")
	MySQLUserName  = getEnv(ENV_MYSQL_USERNAME, "")
	MySQLPassword  = getEnv(ENV_MYSQL_PASSWORD, "")
	MySQLPort      = getEnv(ENV_MYSQL_PORT, "")

	// Redis 默认配置
	RedisHost = getEnv(ENV_REDIS_HOST, "")
	RedisPort = getEnv(ENV_REDIS_PORT, "")

	// Hologres 默认配置
	HOLOGRESDSN = getEnv(ENV_HOLOGRES_DSN, "")

	// Postgres 默认配置
	POSTGRESSDSN = getEnv(ENV_POSTGRES_DSN, "")

	// 其他配置
	IPDBPath   = getEnv(ENV_IP_DB_PATH, "")
	EncryptKEY = getEnv(ENV_ENCRYPT_KEY, "")
)

// getEnv 从环境变量获取值，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		log.Printf("getEnv warning: key=%v, defaultValue=%v", key, defaultValue)
		return defaultValue
	}
	return strings.TrimSpace(value)
}
