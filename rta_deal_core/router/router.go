package router

import (
	"rta_core/api"

	"github.com/gin-gonic/gin"
)

// Init ...
func Init() *gin.Engine {
	router := gin.Default()

	// 路由组: api
	v1 := router.Group("/api")
	{
		v1.GET("/test", api.Test)
		v1.GET("/testNewKafka", api.NewKafka)
		v1.GET("/testOldKafka", api.OldKafka)
	}

	// rta pos 请求
	router.POST("/request", api.RTAPostRequest)

	// debug ocpx
	router.GET("/debug/ocpx", api.DebugSaveToOCPXTest2)

	// router.Run(":8081")
	return router
}
