syntax = "proto3";

package qtt_rta;
option go_package = "rta_core/pb/qtt_rta;qtt_rta";

enum OSType {
    OS_UNKNOWN = 0;
    ANDROID = 1;
    IOS = 2;
};

enum DeviceIdType {
    DEVID_UNKNOWN = 0;
    IMEI = 1;
    IDFA = 2;
    ANDROIDID = 3;
    MEID = 4;
    OAID = 5;
    TUID = 6;
    CAID = 7;
};

enum StatusCode {
    BID_ALL = 0; //全部参与竞价
    BID_ABANDON = 1; //全部放弃竞价
    BID_EXCLUDE_PART = 2; //部分id 不参与竞价
}

enum ExcludeType {
    DEFAULT = 0; //自动检测校验方式
    USERID = 1; //检验账号id
    STRATEGYID = 2; //校验策略id
}

message DeviceId {
    DeviceIdType type = 1; //设备ID 的类型
    string id = 2; //设备ID
    bool is_md5 = 3; //设备ID 是否md5 加密
    string ver = 4; // CAID 版本号
};

message Device {
    OSType os = 1; //操作系统
    repeated DeviceId dev_id = 2; //设备ID
    string ip = 3; //设备IP
}

message AdInfo {
    int32 ad_id = 1; //广告创意id
    int64 adgroup_id = 2; //广告组id
    int64 account_id = 3; //账户id
}

message RTARequest {
    string req_id = 1; //请求ID， 请求唯一标识
    string slot_id = 2; //当前流量的广告位
    Device device = 3; //设备相关信息
    int64 rta_id = 4;//rta_id
    string req_type = 5; //值为空或NORMAL_REQUEST时，为普通请求；值为ADLIST_REQUEST时，为二次请求。
    repeated AdInfo ad_infos = 6; //粗排检索出的广告列表
    string install_type = 7; //从广告主投放的广告中返回已安装app包名，多个包名使用英文","分割。
    string app_bundle_id = 8; //流量来源app包名加密值
}

message RTAResponse {
    StatusCode status_code = 1; //状态码
    repeated int64 exclude_user_id = 2; //不参与竞价的user id
    repeated int64 exclude_unit_id = 3; //不参与竞价的unit id
    int64 cost_time = 4; //广告主内部耗时，单位秒
    repeated string strategy_id = 5; //参与竞价的strategy id
    repeated int64 pid = 6; //dpa 商品id
    ExcludeType exclude_type = 7;// 不参与竞价的标识
    string dsp_tag_str = 8; //自定义归因串联字段，请求级（字符型）
    float boost = 9; //cpa_given提权系数
    bool success = 10; //服务端是否正确响应
    string req_id = 11; //回传原请求id，请求唯一标识
    repeated UserBid user_bid = 12; //策略id或账号维度的双出价
    bool given_cache_time = 13; //是否回复实时缓存
    int64 cache_time = 14; //广告主实时返回的缓存时间,单位(秒),范围[0,7200]
    repeated AdResult ad_results = 15; //针对广告的实时出价，req_type值为ADLIST_REQUEST时返回
}

message UserBid {
    repeated int64 user_id = 1; //参与竞价的user id
    repeated string strategy_id = 2; //参与竞价的strategy_id,优先级>user_id
    float boost = 3; //cpa出价提权系数
    int64 cpa_given = 4; //cpa广告出价,优先级>boost,只填cpa_given只影响单出价单元
    float deep_boost = 5; //深度转化出价提权系数
    int64 deep_cpa_given = 6; //深度转化出价,优先级>deep_boost,双出价单元如果修改cpa_given要一起填deep_cpa_given
    int64 exp_id = 7; // 实验分桶ID
}

message AdResult {
    int32 ad_id = 1; //广告创意id
    bool ignore = 2; //值为true时，则忽略该广告的二次出价
    float boost = 3; //针对该广告的CPA出价提权系数
    int64 cpa_given = 4; //针对该广告的CPA广告出价，优先级>boost
    float deep_boost = 5; //针对该广告的深度转化出价提权系数。只返回浅层系数，深层系数会同比例自动调整
    int64 deep_cpa_given = 6; //针对该广告的深度转化出价。优先级>deep_boost，只返回浅层出价，深层出价会同比例自动调整
    int64 exp_id = 7; // 实验分桶ID
}


//RTA 账号配置
//RTAStrategy
message RTAStrategy {
    bool overtime_bidding = 1; //超时竞价，1打开 0 关闭
    uint32 persent = 2; //流量切分的百分比
    uint32 qps = 3; //qps， 整体值——暂时不用
    uint32 admixture_percent=4; //掺量比例
}
// redis_scan
message RTAAccount {
    int64 rta_id = 1; //每个rta 生成唯一的rta_id, 这里最好用agentid
    repeated int64 white_user_id_list = 2; //白名单队列，如果RTAResponse 返回的userID 不在队列中，认为响应无效
    repeated int64 white_unit_id_list = 3; //白名单队列，如果RTAResponse unitID 不在队列中，认为响应无效——暂时不用
    string url = 4; //请求url
    int32 timeout = 5; //超时时间，单位毫秒
    int64 cache_ex_time = 6; //缓存过期时间
    RTAStrategy rta_strategy = 7;//相关策略
    int32 status = 8; //账号状态
    int64 refuse_cache_time = 9; //拒绝请求缓存过期时间
    string strategy_id_str = 10;//json格式策略id -> userid
    map<string, UserIds> strategy_id_list = 11;//将strategy_id_str反序列化
    int32 exec_stage = 12;//执行阶段，默认0
    int64 rely_rta_id = 13;//依赖rta id
}

message UserIds {
    repeated int64 user_id_list = 1;
}

message RTAAccounts {
  repeated RTAAccount rows = 1;
}
