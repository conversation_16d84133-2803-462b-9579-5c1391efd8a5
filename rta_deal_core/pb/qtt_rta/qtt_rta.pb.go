// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pb/qtt_rta/qtt_rta.proto

package qtt_rta

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OSType int32

const (
	OSType_OS_UNKNOWN OSType = 0
	OSType_ANDROID    OSType = 1
	OSType_IOS        OSType = 2
)

// Enum value maps for OSType.
var (
	OSType_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "ANDROID",
		2: "IOS",
	}
	OSType_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"ANDROID":    1,
		"IOS":        2,
	}
)

func (x OSType) Enum() *OSType {
	p := new(OSType)
	*p = x
	return p
}

func (x OSType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OSType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_qtt_rta_qtt_rta_proto_enumTypes[0].Descriptor()
}

func (OSType) Type() protoreflect.EnumType {
	return &file_pb_qtt_rta_qtt_rta_proto_enumTypes[0]
}

func (x OSType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OSType.Descriptor instead.
func (OSType) EnumDescriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{0}
}

type DeviceIdType int32

const (
	DeviceIdType_DEVID_UNKNOWN DeviceIdType = 0
	DeviceIdType_IMEI          DeviceIdType = 1
	DeviceIdType_IDFA          DeviceIdType = 2
	DeviceIdType_ANDROIDID     DeviceIdType = 3
	DeviceIdType_MEID          DeviceIdType = 4
	DeviceIdType_OAID          DeviceIdType = 5
	DeviceIdType_TUID          DeviceIdType = 6
	DeviceIdType_CAID          DeviceIdType = 7
)

// Enum value maps for DeviceIdType.
var (
	DeviceIdType_name = map[int32]string{
		0: "DEVID_UNKNOWN",
		1: "IMEI",
		2: "IDFA",
		3: "ANDROIDID",
		4: "MEID",
		5: "OAID",
		6: "TUID",
		7: "CAID",
	}
	DeviceIdType_value = map[string]int32{
		"DEVID_UNKNOWN": 0,
		"IMEI":          1,
		"IDFA":          2,
		"ANDROIDID":     3,
		"MEID":          4,
		"OAID":          5,
		"TUID":          6,
		"CAID":          7,
	}
)

func (x DeviceIdType) Enum() *DeviceIdType {
	p := new(DeviceIdType)
	*p = x
	return p
}

func (x DeviceIdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceIdType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_qtt_rta_qtt_rta_proto_enumTypes[1].Descriptor()
}

func (DeviceIdType) Type() protoreflect.EnumType {
	return &file_pb_qtt_rta_qtt_rta_proto_enumTypes[1]
}

func (x DeviceIdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceIdType.Descriptor instead.
func (DeviceIdType) EnumDescriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{1}
}

type StatusCode int32

const (
	StatusCode_BID_ALL          StatusCode = 0 //全部参与竞价
	StatusCode_BID_ABANDON      StatusCode = 1 //全部放弃竞价
	StatusCode_BID_EXCLUDE_PART StatusCode = 2 //部分id 不参与竞价
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0: "BID_ALL",
		1: "BID_ABANDON",
		2: "BID_EXCLUDE_PART",
	}
	StatusCode_value = map[string]int32{
		"BID_ALL":          0,
		"BID_ABANDON":      1,
		"BID_EXCLUDE_PART": 2,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_qtt_rta_qtt_rta_proto_enumTypes[2].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_pb_qtt_rta_qtt_rta_proto_enumTypes[2]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{2}
}

type ExcludeType int32

const (
	ExcludeType_DEFAULT    ExcludeType = 0 //自动检测校验方式
	ExcludeType_USERID     ExcludeType = 1 //检验账号id
	ExcludeType_STRATEGYID ExcludeType = 2 //校验策略id
)

// Enum value maps for ExcludeType.
var (
	ExcludeType_name = map[int32]string{
		0: "DEFAULT",
		1: "USERID",
		2: "STRATEGYID",
	}
	ExcludeType_value = map[string]int32{
		"DEFAULT":    0,
		"USERID":     1,
		"STRATEGYID": 2,
	}
)

func (x ExcludeType) Enum() *ExcludeType {
	p := new(ExcludeType)
	*p = x
	return p
}

func (x ExcludeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExcludeType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_qtt_rta_qtt_rta_proto_enumTypes[3].Descriptor()
}

func (ExcludeType) Type() protoreflect.EnumType {
	return &file_pb_qtt_rta_qtt_rta_proto_enumTypes[3]
}

func (x ExcludeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExcludeType.Descriptor instead.
func (ExcludeType) EnumDescriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{3}
}

type DeviceId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          DeviceIdType           `protobuf:"varint,1,opt,name=type,proto3,enum=qtt_rta.DeviceIdType" json:"type,omitempty"` //设备ID 的类型
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                                //设备ID
	IsMd5         bool                   `protobuf:"varint,3,opt,name=is_md5,json=isMd5,proto3" json:"is_md5,omitempty"`            //设备ID 是否md5 加密
	Ver           string                 `protobuf:"bytes,4,opt,name=ver,proto3" json:"ver,omitempty"`                              // CAID 版本号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceId) Reset() {
	*x = DeviceId{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceId) ProtoMessage() {}

func (x *DeviceId) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceId.ProtoReflect.Descriptor instead.
func (*DeviceId) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceId) GetType() DeviceIdType {
	if x != nil {
		return x.Type
	}
	return DeviceIdType_DEVID_UNKNOWN
}

func (x *DeviceId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceId) GetIsMd5() bool {
	if x != nil {
		return x.IsMd5
	}
	return false
}

func (x *DeviceId) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

type Device struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Os            OSType                 `protobuf:"varint,1,opt,name=os,proto3,enum=qtt_rta.OSType" json:"os,omitempty"` //操作系统
	DevId         []*DeviceId            `protobuf:"bytes,2,rep,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`   //设备ID
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`                      //设备IP
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{1}
}

func (x *Device) GetOs() OSType {
	if x != nil {
		return x.Os
	}
	return OSType_OS_UNKNOWN
}

func (x *Device) GetDevId() []*DeviceId {
	if x != nil {
		return x.DevId
	}
	return nil
}

func (x *Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type AdInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AdId          int32                  `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                //广告创意id
	AdgroupId     int64                  `protobuf:"varint,2,opt,name=adgroup_id,json=adgroupId,proto3" json:"adgroup_id,omitempty"` //广告组id
	AccountId     int64                  `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` //账户id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdInfo) Reset() {
	*x = AdInfo{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdInfo) ProtoMessage() {}

func (x *AdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdInfo.ProtoReflect.Descriptor instead.
func (*AdInfo) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{2}
}

func (x *AdInfo) GetAdId() int32 {
	if x != nil {
		return x.AdId
	}
	return 0
}

func (x *AdInfo) GetAdgroupId() int64 {
	if x != nil {
		return x.AdgroupId
	}
	return 0
}

func (x *AdInfo) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

type RTARequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReqId         string                 `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`                     //请求ID， 请求唯一标识
	SlotId        string                 `protobuf:"bytes,2,opt,name=slot_id,json=slotId,proto3" json:"slot_id,omitempty"`                  //当前流量的广告位
	Device        *Device                `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`                                //设备相关信息
	RtaId         int64                  `protobuf:"varint,4,opt,name=rta_id,json=rtaId,proto3" json:"rta_id,omitempty"`                    //rta_id
	ReqType       string                 `protobuf:"bytes,5,opt,name=req_type,json=reqType,proto3" json:"req_type,omitempty"`               //值为空或NORMAL_REQUEST时，为普通请求；值为ADLIST_REQUEST时，为二次请求。
	AdInfos       []*AdInfo              `protobuf:"bytes,6,rep,name=ad_infos,json=adInfos,proto3" json:"ad_infos,omitempty"`               //粗排检索出的广告列表
	InstallType   string                 `protobuf:"bytes,7,opt,name=install_type,json=installType,proto3" json:"install_type,omitempty"`   //从广告主投放的广告中返回已安装app包名，多个包名使用英文","分割。
	AppBundleId   string                 `protobuf:"bytes,8,opt,name=app_bundle_id,json=appBundleId,proto3" json:"app_bundle_id,omitempty"` //流量来源app包名加密值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTARequest) Reset() {
	*x = RTARequest{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTARequest) ProtoMessage() {}

func (x *RTARequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTARequest.ProtoReflect.Descriptor instead.
func (*RTARequest) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{3}
}

func (x *RTARequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *RTARequest) GetSlotId() string {
	if x != nil {
		return x.SlotId
	}
	return ""
}

func (x *RTARequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RTARequest) GetRtaId() int64 {
	if x != nil {
		return x.RtaId
	}
	return 0
}

func (x *RTARequest) GetReqType() string {
	if x != nil {
		return x.ReqType
	}
	return ""
}

func (x *RTARequest) GetAdInfos() []*AdInfo {
	if x != nil {
		return x.AdInfos
	}
	return nil
}

func (x *RTARequest) GetInstallType() string {
	if x != nil {
		return x.InstallType
	}
	return ""
}

func (x *RTARequest) GetAppBundleId() string {
	if x != nil {
		return x.AppBundleId
	}
	return ""
}

type RTAResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StatusCode     StatusCode             `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3,enum=qtt_rta.StatusCode" json:"status_code,omitempty"`     //状态码
	ExcludeUserId  []int64                `protobuf:"varint,2,rep,packed,name=exclude_user_id,json=excludeUserId,proto3" json:"exclude_user_id,omitempty"`           //不参与竞价的user id
	ExcludeUnitId  []int64                `protobuf:"varint,3,rep,packed,name=exclude_unit_id,json=excludeUnitId,proto3" json:"exclude_unit_id,omitempty"`           //不参与竞价的unit id
	CostTime       int64                  `protobuf:"varint,4,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                   //广告主内部耗时，单位秒
	StrategyId     []string               `protobuf:"bytes,5,rep,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`                              //参与竞价的strategy id
	Pid            []int64                `protobuf:"varint,6,rep,packed,name=pid,proto3" json:"pid,omitempty"`                                                      //dpa 商品id
	ExcludeType    ExcludeType            `protobuf:"varint,7,opt,name=exclude_type,json=excludeType,proto3,enum=qtt_rta.ExcludeType" json:"exclude_type,omitempty"` // 不参与竞价的标识
	DspTagStr      string                 `protobuf:"bytes,8,opt,name=dsp_tag_str,json=dspTagStr,proto3" json:"dsp_tag_str,omitempty"`                               //自定义归因串联字段，请求级（字符型）
	Boost          float32                `protobuf:"fixed32,9,opt,name=boost,proto3" json:"boost,omitempty"`                                                        //cpa_given提权系数
	Success        bool                   `protobuf:"varint,10,opt,name=success,proto3" json:"success,omitempty"`                                                    //服务端是否正确响应
	ReqId          string                 `protobuf:"bytes,11,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`                                            //回传原请求id，请求唯一标识
	UserBid        []*UserBid             `protobuf:"bytes,12,rep,name=user_bid,json=userBid,proto3" json:"user_bid,omitempty"`                                      //策略id或账号维度的双出价
	GivenCacheTime bool                   `protobuf:"varint,13,opt,name=given_cache_time,json=givenCacheTime,proto3" json:"given_cache_time,omitempty"`              //是否回复实时缓存
	CacheTime      int64                  `protobuf:"varint,14,opt,name=cache_time,json=cacheTime,proto3" json:"cache_time,omitempty"`                               //广告主实时返回的缓存时间,单位(秒),范围[0,7200]
	AdResults      []*AdResult            `protobuf:"bytes,15,rep,name=ad_results,json=adResults,proto3" json:"ad_results,omitempty"`                                //针对广告的实时出价，req_type值为ADLIST_REQUEST时返回
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RTAResponse) Reset() {
	*x = RTAResponse{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTAResponse) ProtoMessage() {}

func (x *RTAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTAResponse.ProtoReflect.Descriptor instead.
func (*RTAResponse) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{4}
}

func (x *RTAResponse) GetStatusCode() StatusCode {
	if x != nil {
		return x.StatusCode
	}
	return StatusCode_BID_ALL
}

func (x *RTAResponse) GetExcludeUserId() []int64 {
	if x != nil {
		return x.ExcludeUserId
	}
	return nil
}

func (x *RTAResponse) GetExcludeUnitId() []int64 {
	if x != nil {
		return x.ExcludeUnitId
	}
	return nil
}

func (x *RTAResponse) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *RTAResponse) GetStrategyId() []string {
	if x != nil {
		return x.StrategyId
	}
	return nil
}

func (x *RTAResponse) GetPid() []int64 {
	if x != nil {
		return x.Pid
	}
	return nil
}

func (x *RTAResponse) GetExcludeType() ExcludeType {
	if x != nil {
		return x.ExcludeType
	}
	return ExcludeType_DEFAULT
}

func (x *RTAResponse) GetDspTagStr() string {
	if x != nil {
		return x.DspTagStr
	}
	return ""
}

func (x *RTAResponse) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

func (x *RTAResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RTAResponse) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *RTAResponse) GetUserBid() []*UserBid {
	if x != nil {
		return x.UserBid
	}
	return nil
}

func (x *RTAResponse) GetGivenCacheTime() bool {
	if x != nil {
		return x.GivenCacheTime
	}
	return false
}

func (x *RTAResponse) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

func (x *RTAResponse) GetAdResults() []*AdResult {
	if x != nil {
		return x.AdResults
	}
	return nil
}

type UserBid struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        []int64                `protobuf:"varint,1,rep,packed,name=user_id,json=userId,proto3" json:"user_id,omitempty"`              //参与竞价的user id
	StrategyId    []string               `protobuf:"bytes,2,rep,name=strategy_id,json=strategyId,proto3" json:"strategy_id,omitempty"`          //参与竞价的strategy_id,优先级>user_id
	Boost         float32                `protobuf:"fixed32,3,opt,name=boost,proto3" json:"boost,omitempty"`                                    //cpa出价提权系数
	CpaGiven      int64                  `protobuf:"varint,4,opt,name=cpa_given,json=cpaGiven,proto3" json:"cpa_given,omitempty"`               //cpa广告出价,优先级>boost,只填cpa_given只影响单出价单元
	DeepBoost     float32                `protobuf:"fixed32,5,opt,name=deep_boost,json=deepBoost,proto3" json:"deep_boost,omitempty"`           //深度转化出价提权系数
	DeepCpaGiven  int64                  `protobuf:"varint,6,opt,name=deep_cpa_given,json=deepCpaGiven,proto3" json:"deep_cpa_given,omitempty"` //深度转化出价,优先级>deep_boost,双出价单元如果修改cpa_given要一起填deep_cpa_given
	ExpId         int64                  `protobuf:"varint,7,opt,name=exp_id,json=expId,proto3" json:"exp_id,omitempty"`                        // 实验分桶ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBid) Reset() {
	*x = UserBid{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBid) ProtoMessage() {}

func (x *UserBid) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBid.ProtoReflect.Descriptor instead.
func (*UserBid) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{5}
}

func (x *UserBid) GetUserId() []int64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *UserBid) GetStrategyId() []string {
	if x != nil {
		return x.StrategyId
	}
	return nil
}

func (x *UserBid) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

func (x *UserBid) GetCpaGiven() int64 {
	if x != nil {
		return x.CpaGiven
	}
	return 0
}

func (x *UserBid) GetDeepBoost() float32 {
	if x != nil {
		return x.DeepBoost
	}
	return 0
}

func (x *UserBid) GetDeepCpaGiven() int64 {
	if x != nil {
		return x.DeepCpaGiven
	}
	return 0
}

func (x *UserBid) GetExpId() int64 {
	if x != nil {
		return x.ExpId
	}
	return 0
}

type AdResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AdId          int32                  `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                           //广告创意id
	Ignore        bool                   `protobuf:"varint,2,opt,name=ignore,proto3" json:"ignore,omitempty"`                                   //值为true时，则忽略该广告的二次出价
	Boost         float32                `protobuf:"fixed32,3,opt,name=boost,proto3" json:"boost,omitempty"`                                    //针对该广告的CPA出价提权系数
	CpaGiven      int64                  `protobuf:"varint,4,opt,name=cpa_given,json=cpaGiven,proto3" json:"cpa_given,omitempty"`               //针对该广告的CPA广告出价，优先级>boost
	DeepBoost     float32                `protobuf:"fixed32,5,opt,name=deep_boost,json=deepBoost,proto3" json:"deep_boost,omitempty"`           //针对该广告的深度转化出价提权系数。只返回浅层系数，深层系数会同比例自动调整
	DeepCpaGiven  int64                  `protobuf:"varint,6,opt,name=deep_cpa_given,json=deepCpaGiven,proto3" json:"deep_cpa_given,omitempty"` //针对该广告的深度转化出价。优先级>deep_boost，只返回浅层出价，深层出价会同比例自动调整
	ExpId         int64                  `protobuf:"varint,7,opt,name=exp_id,json=expId,proto3" json:"exp_id,omitempty"`                        // 实验分桶ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdResult) Reset() {
	*x = AdResult{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResult) ProtoMessage() {}

func (x *AdResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResult.ProtoReflect.Descriptor instead.
func (*AdResult) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{6}
}

func (x *AdResult) GetAdId() int32 {
	if x != nil {
		return x.AdId
	}
	return 0
}

func (x *AdResult) GetIgnore() bool {
	if x != nil {
		return x.Ignore
	}
	return false
}

func (x *AdResult) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

func (x *AdResult) GetCpaGiven() int64 {
	if x != nil {
		return x.CpaGiven
	}
	return 0
}

func (x *AdResult) GetDeepBoost() float32 {
	if x != nil {
		return x.DeepBoost
	}
	return 0
}

func (x *AdResult) GetDeepCpaGiven() int64 {
	if x != nil {
		return x.DeepCpaGiven
	}
	return 0
}

func (x *AdResult) GetExpId() int64 {
	if x != nil {
		return x.ExpId
	}
	return 0
}

// RTA 账号配置
// RTAStrategy
type RTAStrategy struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	OvertimeBidding  bool                   `protobuf:"varint,1,opt,name=overtime_bidding,json=overtimeBidding,proto3" json:"overtime_bidding,omitempty"`    //超时竞价，1打开 0 关闭
	Persent          uint32                 `protobuf:"varint,2,opt,name=persent,proto3" json:"persent,omitempty"`                                           //流量切分的百分比
	Qps              uint32                 `protobuf:"varint,3,opt,name=qps,proto3" json:"qps,omitempty"`                                                   //qps， 整体值——暂时不用
	AdmixturePercent uint32                 `protobuf:"varint,4,opt,name=admixture_percent,json=admixturePercent,proto3" json:"admixture_percent,omitempty"` //掺量比例
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RTAStrategy) Reset() {
	*x = RTAStrategy{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTAStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTAStrategy) ProtoMessage() {}

func (x *RTAStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTAStrategy.ProtoReflect.Descriptor instead.
func (*RTAStrategy) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{7}
}

func (x *RTAStrategy) GetOvertimeBidding() bool {
	if x != nil {
		return x.OvertimeBidding
	}
	return false
}

func (x *RTAStrategy) GetPersent() uint32 {
	if x != nil {
		return x.Persent
	}
	return 0
}

func (x *RTAStrategy) GetQps() uint32 {
	if x != nil {
		return x.Qps
	}
	return 0
}

func (x *RTAStrategy) GetAdmixturePercent() uint32 {
	if x != nil {
		return x.AdmixturePercent
	}
	return 0
}

// redis_scan
type RTAAccount struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RtaId           int64                  `protobuf:"varint,1,opt,name=rta_id,json=rtaId,proto3" json:"rta_id,omitempty"`                                                                                                        //每个rta 生成唯一的rta_id, 这里最好用agentid
	WhiteUserIdList []int64                `protobuf:"varint,2,rep,packed,name=white_user_id_list,json=whiteUserIdList,proto3" json:"white_user_id_list,omitempty"`                                                               //白名单队列，如果RTAResponse 返回的userID 不在队列中，认为响应无效
	WhiteUnitIdList []int64                `protobuf:"varint,3,rep,packed,name=white_unit_id_list,json=whiteUnitIdList,proto3" json:"white_unit_id_list,omitempty"`                                                               //白名单队列，如果RTAResponse unitID 不在队列中，认为响应无效——暂时不用
	Url             string                 `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`                                                                                                                          //请求url
	Timeout         int32                  `protobuf:"varint,5,opt,name=timeout,proto3" json:"timeout,omitempty"`                                                                                                                 //超时时间，单位毫秒
	CacheExTime     int64                  `protobuf:"varint,6,opt,name=cache_ex_time,json=cacheExTime,proto3" json:"cache_ex_time,omitempty"`                                                                                    //缓存过期时间
	RtaStrategy     *RTAStrategy           `protobuf:"bytes,7,opt,name=rta_strategy,json=rtaStrategy,proto3" json:"rta_strategy,omitempty"`                                                                                       //相关策略
	Status          int32                  `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`                                                                                                                   //账号状态
	RefuseCacheTime int64                  `protobuf:"varint,9,opt,name=refuse_cache_time,json=refuseCacheTime,proto3" json:"refuse_cache_time,omitempty"`                                                                        //拒绝请求缓存过期时间
	StrategyIdStr   string                 `protobuf:"bytes,10,opt,name=strategy_id_str,json=strategyIdStr,proto3" json:"strategy_id_str,omitempty"`                                                                              //json格式策略id -> userid
	StrategyIdList  map[string]*UserIds    `protobuf:"bytes,11,rep,name=strategy_id_list,json=strategyIdList,proto3" json:"strategy_id_list,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //将strategy_id_str反序列化
	ExecStage       int32                  `protobuf:"varint,12,opt,name=exec_stage,json=execStage,proto3" json:"exec_stage,omitempty"`                                                                                           //执行阶段，默认0
	RelyRtaId       int64                  `protobuf:"varint,13,opt,name=rely_rta_id,json=relyRtaId,proto3" json:"rely_rta_id,omitempty"`                                                                                         //依赖rta id
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RTAAccount) Reset() {
	*x = RTAAccount{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTAAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTAAccount) ProtoMessage() {}

func (x *RTAAccount) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTAAccount.ProtoReflect.Descriptor instead.
func (*RTAAccount) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{8}
}

func (x *RTAAccount) GetRtaId() int64 {
	if x != nil {
		return x.RtaId
	}
	return 0
}

func (x *RTAAccount) GetWhiteUserIdList() []int64 {
	if x != nil {
		return x.WhiteUserIdList
	}
	return nil
}

func (x *RTAAccount) GetWhiteUnitIdList() []int64 {
	if x != nil {
		return x.WhiteUnitIdList
	}
	return nil
}

func (x *RTAAccount) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RTAAccount) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *RTAAccount) GetCacheExTime() int64 {
	if x != nil {
		return x.CacheExTime
	}
	return 0
}

func (x *RTAAccount) GetRtaStrategy() *RTAStrategy {
	if x != nil {
		return x.RtaStrategy
	}
	return nil
}

func (x *RTAAccount) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RTAAccount) GetRefuseCacheTime() int64 {
	if x != nil {
		return x.RefuseCacheTime
	}
	return 0
}

func (x *RTAAccount) GetStrategyIdStr() string {
	if x != nil {
		return x.StrategyIdStr
	}
	return ""
}

func (x *RTAAccount) GetStrategyIdList() map[string]*UserIds {
	if x != nil {
		return x.StrategyIdList
	}
	return nil
}

func (x *RTAAccount) GetExecStage() int32 {
	if x != nil {
		return x.ExecStage
	}
	return 0
}

func (x *RTAAccount) GetRelyRtaId() int64 {
	if x != nil {
		return x.RelyRtaId
	}
	return 0
}

type UserIds struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserIdList    []int64                `protobuf:"varint,1,rep,packed,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIds) Reset() {
	*x = UserIds{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIds) ProtoMessage() {}

func (x *UserIds) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIds.ProtoReflect.Descriptor instead.
func (*UserIds) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{9}
}

func (x *UserIds) GetUserIdList() []int64 {
	if x != nil {
		return x.UserIdList
	}
	return nil
}

type RTAAccounts struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rows          []*RTAAccount          `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTAAccounts) Reset() {
	*x = RTAAccounts{}
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTAAccounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTAAccounts) ProtoMessage() {}

func (x *RTAAccounts) ProtoReflect() protoreflect.Message {
	mi := &file_pb_qtt_rta_qtt_rta_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTAAccounts.ProtoReflect.Descriptor instead.
func (*RTAAccounts) Descriptor() ([]byte, []int) {
	return file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP(), []int{10}
}

func (x *RTAAccounts) GetRows() []*RTAAccount {
	if x != nil {
		return x.Rows
	}
	return nil
}

var File_pb_qtt_rta_qtt_rta_proto protoreflect.FileDescriptor

const file_pb_qtt_rta_qtt_rta_proto_rawDesc = "" +
	"\n" +
	"\x18pb/qtt_rta/qtt_rta.proto\x12\aqtt_rta\"n\n" +
	"\bDeviceId\x12)\n" +
	"\x04type\x18\x01 \x01(\x0e2\x15.qtt_rta.DeviceIdTypeR\x04type\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x15\n" +
	"\x06is_md5\x18\x03 \x01(\bR\x05isMd5\x12\x10\n" +
	"\x03ver\x18\x04 \x01(\tR\x03ver\"c\n" +
	"\x06Device\x12\x1f\n" +
	"\x02os\x18\x01 \x01(\x0e2\x0f.qtt_rta.OSTypeR\x02os\x12(\n" +
	"\x06dev_id\x18\x02 \x03(\v2\x11.qtt_rta.DeviceIdR\x05devId\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\"[\n" +
	"\x06AdInfo\x12\x13\n" +
	"\x05ad_id\x18\x01 \x01(\x05R\x04adId\x12\x1d\n" +
	"\n" +
	"adgroup_id\x18\x02 \x01(\x03R\tadgroupId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x03 \x01(\x03R\taccountId\"\x8a\x02\n" +
	"\n" +
	"RTARequest\x12\x15\n" +
	"\x06req_id\x18\x01 \x01(\tR\x05reqId\x12\x17\n" +
	"\aslot_id\x18\x02 \x01(\tR\x06slotId\x12'\n" +
	"\x06device\x18\x03 \x01(\v2\x0f.qtt_rta.DeviceR\x06device\x12\x15\n" +
	"\x06rta_id\x18\x04 \x01(\x03R\x05rtaId\x12\x19\n" +
	"\breq_type\x18\x05 \x01(\tR\areqType\x12*\n" +
	"\bad_infos\x18\x06 \x03(\v2\x0f.qtt_rta.AdInfoR\aadInfos\x12!\n" +
	"\finstall_type\x18\a \x01(\tR\vinstallType\x12\"\n" +
	"\rapp_bundle_id\x18\b \x01(\tR\vappBundleId\"\xab\x04\n" +
	"\vRTAResponse\x124\n" +
	"\vstatus_code\x18\x01 \x01(\x0e2\x13.qtt_rta.StatusCodeR\n" +
	"statusCode\x12&\n" +
	"\x0fexclude_user_id\x18\x02 \x03(\x03R\rexcludeUserId\x12&\n" +
	"\x0fexclude_unit_id\x18\x03 \x03(\x03R\rexcludeUnitId\x12\x1b\n" +
	"\tcost_time\x18\x04 \x01(\x03R\bcostTime\x12\x1f\n" +
	"\vstrategy_id\x18\x05 \x03(\tR\n" +
	"strategyId\x12\x10\n" +
	"\x03pid\x18\x06 \x03(\x03R\x03pid\x127\n" +
	"\fexclude_type\x18\a \x01(\x0e2\x14.qtt_rta.ExcludeTypeR\vexcludeType\x12\x1e\n" +
	"\vdsp_tag_str\x18\b \x01(\tR\tdspTagStr\x12\x14\n" +
	"\x05boost\x18\t \x01(\x02R\x05boost\x12\x18\n" +
	"\asuccess\x18\n" +
	" \x01(\bR\asuccess\x12\x15\n" +
	"\x06req_id\x18\v \x01(\tR\x05reqId\x12+\n" +
	"\buser_bid\x18\f \x03(\v2\x10.qtt_rta.UserBidR\auserBid\x12(\n" +
	"\x10given_cache_time\x18\r \x01(\bR\x0egivenCacheTime\x12\x1d\n" +
	"\n" +
	"cache_time\x18\x0e \x01(\x03R\tcacheTime\x120\n" +
	"\n" +
	"ad_results\x18\x0f \x03(\v2\x11.qtt_rta.AdResultR\tadResults\"\xd2\x01\n" +
	"\aUserBid\x12\x17\n" +
	"\auser_id\x18\x01 \x03(\x03R\x06userId\x12\x1f\n" +
	"\vstrategy_id\x18\x02 \x03(\tR\n" +
	"strategyId\x12\x14\n" +
	"\x05boost\x18\x03 \x01(\x02R\x05boost\x12\x1b\n" +
	"\tcpa_given\x18\x04 \x01(\x03R\bcpaGiven\x12\x1d\n" +
	"\n" +
	"deep_boost\x18\x05 \x01(\x02R\tdeepBoost\x12$\n" +
	"\x0edeep_cpa_given\x18\x06 \x01(\x03R\fdeepCpaGiven\x12\x15\n" +
	"\x06exp_id\x18\a \x01(\x03R\x05expId\"\xc6\x01\n" +
	"\bAdResult\x12\x13\n" +
	"\x05ad_id\x18\x01 \x01(\x05R\x04adId\x12\x16\n" +
	"\x06ignore\x18\x02 \x01(\bR\x06ignore\x12\x14\n" +
	"\x05boost\x18\x03 \x01(\x02R\x05boost\x12\x1b\n" +
	"\tcpa_given\x18\x04 \x01(\x03R\bcpaGiven\x12\x1d\n" +
	"\n" +
	"deep_boost\x18\x05 \x01(\x02R\tdeepBoost\x12$\n" +
	"\x0edeep_cpa_given\x18\x06 \x01(\x03R\fdeepCpaGiven\x12\x15\n" +
	"\x06exp_id\x18\a \x01(\x03R\x05expId\"\x91\x01\n" +
	"\vRTAStrategy\x12)\n" +
	"\x10overtime_bidding\x18\x01 \x01(\bR\x0fovertimeBidding\x12\x18\n" +
	"\apersent\x18\x02 \x01(\rR\apersent\x12\x10\n" +
	"\x03qps\x18\x03 \x01(\rR\x03qps\x12+\n" +
	"\x11admixture_percent\x18\x04 \x01(\rR\x10admixturePercent\"\xd9\x04\n" +
	"\n" +
	"RTAAccount\x12\x15\n" +
	"\x06rta_id\x18\x01 \x01(\x03R\x05rtaId\x12+\n" +
	"\x12white_user_id_list\x18\x02 \x03(\x03R\x0fwhiteUserIdList\x12+\n" +
	"\x12white_unit_id_list\x18\x03 \x03(\x03R\x0fwhiteUnitIdList\x12\x10\n" +
	"\x03url\x18\x04 \x01(\tR\x03url\x12\x18\n" +
	"\atimeout\x18\x05 \x01(\x05R\atimeout\x12\"\n" +
	"\rcache_ex_time\x18\x06 \x01(\x03R\vcacheExTime\x127\n" +
	"\frta_strategy\x18\a \x01(\v2\x14.qtt_rta.RTAStrategyR\vrtaStrategy\x12\x16\n" +
	"\x06status\x18\b \x01(\x05R\x06status\x12*\n" +
	"\x11refuse_cache_time\x18\t \x01(\x03R\x0frefuseCacheTime\x12&\n" +
	"\x0fstrategy_id_str\x18\n" +
	" \x01(\tR\rstrategyIdStr\x12Q\n" +
	"\x10strategy_id_list\x18\v \x03(\v2'.qtt_rta.RTAAccount.StrategyIdListEntryR\x0estrategyIdList\x12\x1d\n" +
	"\n" +
	"exec_stage\x18\f \x01(\x05R\texecStage\x12\x1e\n" +
	"\vrely_rta_id\x18\r \x01(\x03R\trelyRtaId\x1aS\n" +
	"\x13StrategyIdListEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12&\n" +
	"\x05value\x18\x02 \x01(\v2\x10.qtt_rta.UserIdsR\x05value:\x028\x01\"+\n" +
	"\aUserIds\x12 \n" +
	"\fuser_id_list\x18\x01 \x03(\x03R\n" +
	"userIdList\"6\n" +
	"\vRTAAccounts\x12'\n" +
	"\x04rows\x18\x01 \x03(\v2\x13.qtt_rta.RTAAccountR\x04rows*.\n" +
	"\x06OSType\x12\x0e\n" +
	"\n" +
	"OS_UNKNOWN\x10\x00\x12\v\n" +
	"\aANDROID\x10\x01\x12\a\n" +
	"\x03IOS\x10\x02*l\n" +
	"\fDeviceIdType\x12\x11\n" +
	"\rDEVID_UNKNOWN\x10\x00\x12\b\n" +
	"\x04IMEI\x10\x01\x12\b\n" +
	"\x04IDFA\x10\x02\x12\r\n" +
	"\tANDROIDID\x10\x03\x12\b\n" +
	"\x04MEID\x10\x04\x12\b\n" +
	"\x04OAID\x10\x05\x12\b\n" +
	"\x04TUID\x10\x06\x12\b\n" +
	"\x04CAID\x10\a*@\n" +
	"\n" +
	"StatusCode\x12\v\n" +
	"\aBID_ALL\x10\x00\x12\x0f\n" +
	"\vBID_ABANDON\x10\x01\x12\x14\n" +
	"\x10BID_EXCLUDE_PART\x10\x02*6\n" +
	"\vExcludeType\x12\v\n" +
	"\aDEFAULT\x10\x00\x12\n" +
	"\n" +
	"\x06USERID\x10\x01\x12\x0e\n" +
	"\n" +
	"STRATEGYID\x10\x02B\x1dZ\x1brta_core/pb/qtt_rta;qtt_rtab\x06proto3"

var (
	file_pb_qtt_rta_qtt_rta_proto_rawDescOnce sync.Once
	file_pb_qtt_rta_qtt_rta_proto_rawDescData []byte
)

func file_pb_qtt_rta_qtt_rta_proto_rawDescGZIP() []byte {
	file_pb_qtt_rta_qtt_rta_proto_rawDescOnce.Do(func() {
		file_pb_qtt_rta_qtt_rta_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_qtt_rta_qtt_rta_proto_rawDesc), len(file_pb_qtt_rta_qtt_rta_proto_rawDesc)))
	})
	return file_pb_qtt_rta_qtt_rta_proto_rawDescData
}

var file_pb_qtt_rta_qtt_rta_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_qtt_rta_qtt_rta_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_pb_qtt_rta_qtt_rta_proto_goTypes = []any{
	(OSType)(0),         // 0: qtt_rta.OSType
	(DeviceIdType)(0),   // 1: qtt_rta.DeviceIdType
	(StatusCode)(0),     // 2: qtt_rta.StatusCode
	(ExcludeType)(0),    // 3: qtt_rta.ExcludeType
	(*DeviceId)(nil),    // 4: qtt_rta.DeviceId
	(*Device)(nil),      // 5: qtt_rta.Device
	(*AdInfo)(nil),      // 6: qtt_rta.AdInfo
	(*RTARequest)(nil),  // 7: qtt_rta.RTARequest
	(*RTAResponse)(nil), // 8: qtt_rta.RTAResponse
	(*UserBid)(nil),     // 9: qtt_rta.UserBid
	(*AdResult)(nil),    // 10: qtt_rta.AdResult
	(*RTAStrategy)(nil), // 11: qtt_rta.RTAStrategy
	(*RTAAccount)(nil),  // 12: qtt_rta.RTAAccount
	(*UserIds)(nil),     // 13: qtt_rta.UserIds
	(*RTAAccounts)(nil), // 14: qtt_rta.RTAAccounts
	nil,                 // 15: qtt_rta.RTAAccount.StrategyIdListEntry
}
var file_pb_qtt_rta_qtt_rta_proto_depIdxs = []int32{
	1,  // 0: qtt_rta.DeviceId.type:type_name -> qtt_rta.DeviceIdType
	0,  // 1: qtt_rta.Device.os:type_name -> qtt_rta.OSType
	4,  // 2: qtt_rta.Device.dev_id:type_name -> qtt_rta.DeviceId
	5,  // 3: qtt_rta.RTARequest.device:type_name -> qtt_rta.Device
	6,  // 4: qtt_rta.RTARequest.ad_infos:type_name -> qtt_rta.AdInfo
	2,  // 5: qtt_rta.RTAResponse.status_code:type_name -> qtt_rta.StatusCode
	3,  // 6: qtt_rta.RTAResponse.exclude_type:type_name -> qtt_rta.ExcludeType
	9,  // 7: qtt_rta.RTAResponse.user_bid:type_name -> qtt_rta.UserBid
	10, // 8: qtt_rta.RTAResponse.ad_results:type_name -> qtt_rta.AdResult
	11, // 9: qtt_rta.RTAAccount.rta_strategy:type_name -> qtt_rta.RTAStrategy
	15, // 10: qtt_rta.RTAAccount.strategy_id_list:type_name -> qtt_rta.RTAAccount.StrategyIdListEntry
	12, // 11: qtt_rta.RTAAccounts.rows:type_name -> qtt_rta.RTAAccount
	13, // 12: qtt_rta.RTAAccount.StrategyIdListEntry.value:type_name -> qtt_rta.UserIds
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_pb_qtt_rta_qtt_rta_proto_init() }
func file_pb_qtt_rta_qtt_rta_proto_init() {
	if File_pb_qtt_rta_qtt_rta_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_qtt_rta_qtt_rta_proto_rawDesc), len(file_pb_qtt_rta_qtt_rta_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_qtt_rta_qtt_rta_proto_goTypes,
		DependencyIndexes: file_pb_qtt_rta_qtt_rta_proto_depIdxs,
		EnumInfos:         file_pb_qtt_rta_qtt_rta_proto_enumTypes,
		MessageInfos:      file_pb_qtt_rta_qtt_rta_proto_msgTypes,
	}.Build()
	File_pb_qtt_rta_qtt_rta_proto = out.File
	file_pb_qtt_rta_qtt_rta_proto_goTypes = nil
	file_pb_qtt_rta_qtt_rta_proto_depIdxs = nil
}
