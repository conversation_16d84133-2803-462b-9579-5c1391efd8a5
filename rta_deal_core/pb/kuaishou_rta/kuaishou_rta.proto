// Copyright (c) 2020 Kuaishou.Inc.
syntax = "proto3";

option go_package = "rta_core/pb/kuaishou_rta";

package kuaishou_rta;

message RtaRequest {
  string request_id = 1; 	//本次请求的唯一标识id。点击检测链接中需要带上该字段，以知道某一次的点击是由哪一次rta请求带来的
  string channel = 2; 		//渠道标识，需向快手技术申请
  int64 request_time = 3; //发起调用时的unix时间戳，毫秒。快手侧会与服务器当前时间进行比对，两者差值不能大于10分钟
  string sign = 4; 		//验签值，sign = md5(request_id + request_time + 渠道独有的标识令牌)，三个字段直接拼接，中间没有"+"号

  Device device = 5; //设备信息
  message Device {
    string imei = 1; //imei原值
    string imeiMd5 = 2; //imeiMd5 = toLowerCase(md5(imei原值))
    string oaid = 3; //oaid原值
    string oaidMd5 = 4; //oaidMd5 = toLowerCase(md5(oaid原值))
    string idfa = 5; //idfa原值
    string idfaMd5 = 6; //idfaMd5 = toLowerCase(md5(idfa原值))
  }
  
  repeated string promotion_type = 7; //该设备需要问询的推广类型，如快手拉新、快手极速版拉活。具体值在线下约定
}

message RtaResponse {
  int32 status_code = 1; // 服务状态码，正常:0，异常:非0
  repeated PromotionResult promotion_result = 2;

  message PromotionResult {
    string promotion_type = 1; //对应RtaRequest.promotion_type
    bool accept = 2; //true:选择该流量，可以参与竞价
  }
}
