
syntax = "proto2";

option go_package = "rta_core/pb/baidu_rta";

package baidu_rta;

// rta 请求
message RtaApiRequest {
    required uint64 qid = 1;         // 请求唯一标识
    required OsType os_type = 2;    // 操作系统类型:
    required string device_id_md5 = 3;     // 该字段已停止下发，后续直接对接device_info字段。 
    required uint64 sign_time = 4;    // 调用的时间戳, 1970-01-01后的毫秒数
    required string token = 5;         // 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
    optional AndroidDeviceIdType android_device_type = 6;     // 该字段已停止使用，后续直接对接device_info字段。 
    optional DeviceInfo device_info = 8; // 用户设备信息
    optional FlowType flow_type = 9; // 流量类型
    optional uint64 media_id = 11; //媒体id
    optional uint32 os_major_version = 14; //系统主版本号，仅用于品牌广告百度系开屏流量使用
    optional bool is_dpa_request = 15; //标记请求是否为DPA请求(需要商品)
    optional string prefetch_date = 16; //表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
    optional uint64 timestamp = 17; //请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
    optional uint32 bes_media_group = 18; // 媒体行业
    optional uint32 exp_id = 20; // 客户实验号，该字段需申请流量分桶能力后再下发

}
enum FlowType {
    SHOUBAI = 1;  // 站内流量
    UNION =  2;    // 百青藤流量
    KAIPING = 4; // 开屏（仅指品牌广告百度系开屏流量）

}
enum OsType {
    UNKNOWN = 0;    // 类型未知
    ANDROID = 1;     // android
    IOS = 2;         // ios
}
enum AndroidDeviceIdType {
    IMEI = 1;    
    OAID = 2;     
}
message CaidInfo {
    optional bytes caid = 1;
    optional bytes caid_version = 2; //caid版本
}
message DeviceInfo {
    optional bytes idfa_md5 = 1; 
    optional bytes imei_md5 = 2;
    optional bytes android_id_md5 = 3;
    optional bytes oaid_md5 = 4;
    optional bytes oaid = 7;
    optional bytes idfa = 8;
    optional bytes imei2_md5 = 12;
    optional bytes bt = 16;
    optional bytes ut = 17;
    repeated CaidInfo caid_info = 18; // 多版本的caid
}

// rta 返回
message RtaApiResponse {
    required uint64 qid = 1;
    required ResType res = 2;
    optional uint32 user_score = 3;        // 客户打分，可选，DPA暂不可用
    repeated AdResult ad_results = 4;     // 指定要出的广告组，可选
    repeated RtaStrategyAdResult strategy_results = 5;
    optional DpaResult dpa_results = 6;  // DPA相关数据
    repeated BidRise rta_bid_rise = 8; //分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
    optional string prefetch_date = 9; //表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
    optional RtaBidType rta_bid_type = 10; // 标志是否使用rta溢价
    repeated RtaBid rta_bid = 11; // rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
    repeated RtaBid rta_cpc_bid = 12; // rta直接出价,必须配合策略id使用,用于cpc广告
}
enum RtaBidType {
    RTA_NORMAL = 0; // 不溢价
    RTA_BID = 1; // 动态出价
    RTA_RISE = 2; // rta溢价
}
message AdResult {
    required uint64 account_id = 1;    // 账户id，百度方分配的账户id
    repeated uint64 unit_id = 2;        // 广告单元id
}
message RtaStrategyAdResult {
    required uint64 rta_id = 1;
}
message BidRise {
    optional uint64 rta_id = 1;
    optional float bid_rise = 2; // 溢价系数
}
message RtaBid {
    required uint64 rta_id = 1;
    required uint32 rta_bid_value = 2;// rta 直接出价
}
enum ResType {
    ALL = 0;         // 全部投放
    NONE = 1;         // 全部不投
    PART = 2;         // 只投放指定部分
}
message DpaResult {
    // 描述一组候选商品
    message ProductList {
        // 描述候选商品队列优先级
        enum Priority {
            Level_0 = 0;  // 高优先级
            Level_1 = 1;  // 中优先级
            Level_2 = 2;  // 低优先级
        }
        // 描述单个商品
        message Product { 
            required string id = 1;  // id,通常是商品outer_id
            optional double bid_ratio = 2;  // 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
            optional double score = 3;  // 商品打分，分数越大表示优先级越高（可选，非必须）
        }
        required Priority priority = 1;  // 候选商品队列优先级
        repeated Product pid_list = 2;   // 候选商品队列
        optional uint64 catalog_id = 3;   // 商品目录id（可选，非必须）
    }
    repeated ProductList pid_lists = 1; // 多组候选商品队列参竞
}

// 批量接口
// 在DPA-RTA场景下，一方面高QPS对服务器压力过大，另一方面商品推荐数据和算法耗时无法满足实时要求，因此百度和广告主并不严格要求实时商品参竞，而是通过缓存一段时间的设备号，批量发送批量召回，百度对商品推荐结果进行缓存，在一定时限内供下次直接取用。
message BatchRtaApiRequest{
    repeated RtaApiRequest rta_req_list = 1;
}
message BatchRtaApiResponse {
    repeated RtaApiResponse rta_res_list = 1;
}