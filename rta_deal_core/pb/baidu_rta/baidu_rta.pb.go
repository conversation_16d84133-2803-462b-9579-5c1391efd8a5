// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: baidu_rta.proto

package baidu_rta

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FlowType int32

const (
	FlowType_SHOUBAI FlowType = 1 // 站内流量
	FlowType_UNION   FlowType = 2 // 百青藤流量
	FlowType_KAIPING FlowType = 4 // 开屏（仅指品牌广告百度系开屏流量）
)

// Enum value maps for FlowType.
var (
	FlowType_name = map[int32]string{
		1: "SHOUBAI",
		2: "UNION",
		4: "KAIPING",
	}
	FlowType_value = map[string]int32{
		"SHOUBAI": 1,
		"UNION":   2,
		"KAIPING": 4,
	}
)

func (x FlowType) Enum() *FlowType {
	p := new(FlowType)
	*p = x
	return p
}

func (x FlowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[0].Descriptor()
}

func (FlowType) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[0]
}

func (x FlowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *FlowType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = FlowType(num)
	return nil
}

// Deprecated: Use FlowType.Descriptor instead.
func (FlowType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{0}
}

type OsType int32

const (
	OsType_UNKNOWN OsType = 0 // 类型未知
	OsType_ANDROID OsType = 1 // android
	OsType_IOS     OsType = 2 // ios
)

// Enum value maps for OsType.
var (
	OsType_name = map[int32]string{
		0: "UNKNOWN",
		1: "ANDROID",
		2: "IOS",
	}
	OsType_value = map[string]int32{
		"UNKNOWN": 0,
		"ANDROID": 1,
		"IOS":     2,
	}
)

func (x OsType) Enum() *OsType {
	p := new(OsType)
	*p = x
	return p
}

func (x OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[1].Descriptor()
}

func (OsType) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[1]
}

func (x OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *OsType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = OsType(num)
	return nil
}

// Deprecated: Use OsType.Descriptor instead.
func (OsType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{1}
}

type AndroidDeviceIdType int32

const (
	AndroidDeviceIdType_IMEI AndroidDeviceIdType = 1
	AndroidDeviceIdType_OAID AndroidDeviceIdType = 2
)

// Enum value maps for AndroidDeviceIdType.
var (
	AndroidDeviceIdType_name = map[int32]string{
		1: "IMEI",
		2: "OAID",
	}
	AndroidDeviceIdType_value = map[string]int32{
		"IMEI": 1,
		"OAID": 2,
	}
)

func (x AndroidDeviceIdType) Enum() *AndroidDeviceIdType {
	p := new(AndroidDeviceIdType)
	*p = x
	return p
}

func (x AndroidDeviceIdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AndroidDeviceIdType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[2].Descriptor()
}

func (AndroidDeviceIdType) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[2]
}

func (x AndroidDeviceIdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AndroidDeviceIdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AndroidDeviceIdType(num)
	return nil
}

// Deprecated: Use AndroidDeviceIdType.Descriptor instead.
func (AndroidDeviceIdType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{2}
}

type RtaBidType int32

const (
	RtaBidType_RTA_NORMAL RtaBidType = 0 // 不溢价
	RtaBidType_RTA_BID    RtaBidType = 1 // 动态出价
	RtaBidType_RTA_RISE   RtaBidType = 2 // rta溢价
)

// Enum value maps for RtaBidType.
var (
	RtaBidType_name = map[int32]string{
		0: "RTA_NORMAL",
		1: "RTA_BID",
		2: "RTA_RISE",
	}
	RtaBidType_value = map[string]int32{
		"RTA_NORMAL": 0,
		"RTA_BID":    1,
		"RTA_RISE":   2,
	}
)

func (x RtaBidType) Enum() *RtaBidType {
	p := new(RtaBidType)
	*p = x
	return p
}

func (x RtaBidType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaBidType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[3].Descriptor()
}

func (RtaBidType) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[3]
}

func (x RtaBidType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaBidType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaBidType(num)
	return nil
}

// Deprecated: Use RtaBidType.Descriptor instead.
func (RtaBidType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{3}
}

type ResType int32

const (
	ResType_ALL  ResType = 0 // 全部投放
	ResType_NONE ResType = 1 // 全部不投
	ResType_PART ResType = 2 // 只投放指定部分
)

// Enum value maps for ResType.
var (
	ResType_name = map[int32]string{
		0: "ALL",
		1: "NONE",
		2: "PART",
	}
	ResType_value = map[string]int32{
		"ALL":  0,
		"NONE": 1,
		"PART": 2,
	}
)

func (x ResType) Enum() *ResType {
	p := new(ResType)
	*p = x
	return p
}

func (x ResType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[4].Descriptor()
}

func (ResType) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[4]
}

func (x ResType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ResType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ResType(num)
	return nil
}

// Deprecated: Use ResType.Descriptor instead.
func (ResType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{4}
}

// 描述候选商品队列优先级
type DpaResult_ProductList_Priority int32

const (
	DpaResult_ProductList_Level_0 DpaResult_ProductList_Priority = 0 // 高优先级
	DpaResult_ProductList_Level_1 DpaResult_ProductList_Priority = 1 // 中优先级
	DpaResult_ProductList_Level_2 DpaResult_ProductList_Priority = 2 // 低优先级
)

// Enum value maps for DpaResult_ProductList_Priority.
var (
	DpaResult_ProductList_Priority_name = map[int32]string{
		0: "Level_0",
		1: "Level_1",
		2: "Level_2",
	}
	DpaResult_ProductList_Priority_value = map[string]int32{
		"Level_0": 0,
		"Level_1": 1,
		"Level_2": 2,
	}
)

func (x DpaResult_ProductList_Priority) Enum() *DpaResult_ProductList_Priority {
	p := new(DpaResult_ProductList_Priority)
	*p = x
	return p
}

func (x DpaResult_ProductList_Priority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DpaResult_ProductList_Priority) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_rta_proto_enumTypes[5].Descriptor()
}

func (DpaResult_ProductList_Priority) Type() protoreflect.EnumType {
	return &file_baidu_rta_proto_enumTypes[5]
}

func (x DpaResult_ProductList_Priority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DpaResult_ProductList_Priority) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DpaResult_ProductList_Priority(num)
	return nil
}

// Deprecated: Use DpaResult_ProductList_Priority.Descriptor instead.
func (DpaResult_ProductList_Priority) EnumDescriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{8, 0, 0}
}

// rta 请求
type RtaApiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Qid               *uint64              `protobuf:"varint,1,req,name=qid" json:"qid,omitempty"`                                                                                           // 请求唯一标识
	OsType            *OsType              `protobuf:"varint,2,req,name=os_type,json=osType,enum=baidu_rta.OsType" json:"os_type,omitempty"`                                                 // 操作系统类型:
	DeviceIdMd5       *string              `protobuf:"bytes,3,req,name=device_id_md5,json=deviceIdMd5" json:"device_id_md5,omitempty"`                                                       // 该字段已停止下发，后续直接对接device_info字段。
	SignTime          *uint64              `protobuf:"varint,4,req,name=sign_time,json=signTime" json:"sign_time,omitempty"`                                                                 // 调用的时间戳, 1970-01-01后的毫秒数
	Token             *string              `protobuf:"bytes,5,req,name=token" json:"token,omitempty"`                                                                                        // 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
	AndroidDeviceType *AndroidDeviceIdType `protobuf:"varint,6,opt,name=android_device_type,json=androidDeviceType,enum=baidu_rta.AndroidDeviceIdType" json:"android_device_type,omitempty"` // 该字段已停止使用，后续直接对接device_info字段。
	DeviceInfo        *DeviceInfo          `protobuf:"bytes,8,opt,name=device_info,json=deviceInfo" json:"device_info,omitempty"`                                                            // 用户设备信息
	FlowType          *FlowType            `protobuf:"varint,9,opt,name=flow_type,json=flowType,enum=baidu_rta.FlowType" json:"flow_type,omitempty"`                                         // 流量类型
	MediaId           *uint64              `protobuf:"varint,11,opt,name=media_id,json=mediaId" json:"media_id,omitempty"`                                                                   //媒体id
	OsMajorVersion    *uint32              `protobuf:"varint,14,opt,name=os_major_version,json=osMajorVersion" json:"os_major_version,omitempty"`                                            //系统主版本号，仅用于品牌广告百度系开屏流量使用
	IsDpaRequest      *bool                `protobuf:"varint,15,opt,name=is_dpa_request,json=isDpaRequest" json:"is_dpa_request,omitempty"`                                                  //标记请求是否为DPA请求(需要商品)
	PrefetchDate      *string              `protobuf:"bytes,16,opt,name=prefetch_date,json=prefetchDate" json:"prefetch_date,omitempty"`                                                     //表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
	Timestamp         *uint64              `protobuf:"varint,17,opt,name=timestamp" json:"timestamp,omitempty"`                                                                              //请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
	BesMediaGroup     *uint32              `protobuf:"varint,18,opt,name=bes_media_group,json=besMediaGroup" json:"bes_media_group,omitempty"`                                               // 媒体行业
	ExpId             *uint32              `protobuf:"varint,20,opt,name=exp_id,json=expId" json:"exp_id,omitempty"`                                                                         // 客户实验号，该字段需申请流量分桶能力后再下发
}

func (x *RtaApiRequest) Reset() {
	*x = RtaApiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaApiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaApiRequest) ProtoMessage() {}

func (x *RtaApiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaApiRequest.ProtoReflect.Descriptor instead.
func (*RtaApiRequest) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{0}
}

func (x *RtaApiRequest) GetQid() uint64 {
	if x != nil && x.Qid != nil {
		return *x.Qid
	}
	return 0
}

func (x *RtaApiRequest) GetOsType() OsType {
	if x != nil && x.OsType != nil {
		return *x.OsType
	}
	return OsType_UNKNOWN
}

func (x *RtaApiRequest) GetDeviceIdMd5() string {
	if x != nil && x.DeviceIdMd5 != nil {
		return *x.DeviceIdMd5
	}
	return ""
}

func (x *RtaApiRequest) GetSignTime() uint64 {
	if x != nil && x.SignTime != nil {
		return *x.SignTime
	}
	return 0
}

func (x *RtaApiRequest) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

func (x *RtaApiRequest) GetAndroidDeviceType() AndroidDeviceIdType {
	if x != nil && x.AndroidDeviceType != nil {
		return *x.AndroidDeviceType
	}
	return AndroidDeviceIdType_IMEI
}

func (x *RtaApiRequest) GetDeviceInfo() *DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *RtaApiRequest) GetFlowType() FlowType {
	if x != nil && x.FlowType != nil {
		return *x.FlowType
	}
	return FlowType_SHOUBAI
}

func (x *RtaApiRequest) GetMediaId() uint64 {
	if x != nil && x.MediaId != nil {
		return *x.MediaId
	}
	return 0
}

func (x *RtaApiRequest) GetOsMajorVersion() uint32 {
	if x != nil && x.OsMajorVersion != nil {
		return *x.OsMajorVersion
	}
	return 0
}

func (x *RtaApiRequest) GetIsDpaRequest() bool {
	if x != nil && x.IsDpaRequest != nil {
		return *x.IsDpaRequest
	}
	return false
}

func (x *RtaApiRequest) GetPrefetchDate() string {
	if x != nil && x.PrefetchDate != nil {
		return *x.PrefetchDate
	}
	return ""
}

func (x *RtaApiRequest) GetTimestamp() uint64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *RtaApiRequest) GetBesMediaGroup() uint32 {
	if x != nil && x.BesMediaGroup != nil {
		return *x.BesMediaGroup
	}
	return 0
}

func (x *RtaApiRequest) GetExpId() uint32 {
	if x != nil && x.ExpId != nil {
		return *x.ExpId
	}
	return 0
}

type CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid        []byte `protobuf:"bytes,1,opt,name=caid" json:"caid,omitempty"`
	CaidVersion []byte `protobuf:"bytes,2,opt,name=caid_version,json=caidVersion" json:"caid_version,omitempty"` //caid版本
}

func (x *CaidInfo) Reset() {
	*x = CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaidInfo) ProtoMessage() {}

func (x *CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaidInfo.ProtoReflect.Descriptor instead.
func (*CaidInfo) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{1}
}

func (x *CaidInfo) GetCaid() []byte {
	if x != nil {
		return x.Caid
	}
	return nil
}

func (x *CaidInfo) GetCaidVersion() []byte {
	if x != nil {
		return x.CaidVersion
	}
	return nil
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IdfaMd5      []byte      `protobuf:"bytes,1,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`
	ImeiMd5      []byte      `protobuf:"bytes,2,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`
	AndroidIdMd5 []byte      `protobuf:"bytes,3,opt,name=android_id_md5,json=androidIdMd5" json:"android_id_md5,omitempty"`
	OaidMd5      []byte      `protobuf:"bytes,4,opt,name=oaid_md5,json=oaidMd5" json:"oaid_md5,omitempty"`
	Oaid         []byte      `protobuf:"bytes,7,opt,name=oaid" json:"oaid,omitempty"`
	Idfa         []byte      `protobuf:"bytes,8,opt,name=idfa" json:"idfa,omitempty"`
	Imei2Md5     []byte      `protobuf:"bytes,12,opt,name=imei2_md5,json=imei2Md5" json:"imei2_md5,omitempty"`
	Bt           []byte      `protobuf:"bytes,16,opt,name=bt" json:"bt,omitempty"`
	Ut           []byte      `protobuf:"bytes,17,opt,name=ut" json:"ut,omitempty"`
	CaidInfo     []*CaidInfo `protobuf:"bytes,18,rep,name=caid_info,json=caidInfo" json:"caid_info,omitempty"` // 多版本的caid
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceInfo) GetIdfaMd5() []byte {
	if x != nil {
		return x.IdfaMd5
	}
	return nil
}

func (x *DeviceInfo) GetImeiMd5() []byte {
	if x != nil {
		return x.ImeiMd5
	}
	return nil
}

func (x *DeviceInfo) GetAndroidIdMd5() []byte {
	if x != nil {
		return x.AndroidIdMd5
	}
	return nil
}

func (x *DeviceInfo) GetOaidMd5() []byte {
	if x != nil {
		return x.OaidMd5
	}
	return nil
}

func (x *DeviceInfo) GetOaid() []byte {
	if x != nil {
		return x.Oaid
	}
	return nil
}

func (x *DeviceInfo) GetIdfa() []byte {
	if x != nil {
		return x.Idfa
	}
	return nil
}

func (x *DeviceInfo) GetImei2Md5() []byte {
	if x != nil {
		return x.Imei2Md5
	}
	return nil
}

func (x *DeviceInfo) GetBt() []byte {
	if x != nil {
		return x.Bt
	}
	return nil
}

func (x *DeviceInfo) GetUt() []byte {
	if x != nil {
		return x.Ut
	}
	return nil
}

func (x *DeviceInfo) GetCaidInfo() []*CaidInfo {
	if x != nil {
		return x.CaidInfo
	}
	return nil
}

// rta 返回
type RtaApiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Qid             *uint64                `protobuf:"varint,1,req,name=qid" json:"qid,omitempty"`
	Res             *ResType               `protobuf:"varint,2,req,name=res,enum=baidu_rta.ResType" json:"res,omitempty"`
	UserScore       *uint32                `protobuf:"varint,3,opt,name=user_score,json=userScore" json:"user_score,omitempty"` // 客户打分，可选，DPA暂不可用
	AdResults       []*AdResult            `protobuf:"bytes,4,rep,name=ad_results,json=adResults" json:"ad_results,omitempty"`  // 指定要出的广告组，可选
	StrategyResults []*RtaStrategyAdResult `protobuf:"bytes,5,rep,name=strategy_results,json=strategyResults" json:"strategy_results,omitempty"`
	DpaResults      *DpaResult             `protobuf:"bytes,6,opt,name=dpa_results,json=dpaResults" json:"dpa_results,omitempty"`                               // DPA相关数据
	RtaBidRise      []*BidRise             `protobuf:"bytes,8,rep,name=rta_bid_rise,json=rtaBidRise" json:"rta_bid_rise,omitempty"`                             //分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
	PrefetchDate    *string                `protobuf:"bytes,9,opt,name=prefetch_date,json=prefetchDate" json:"prefetch_date,omitempty"`                         //表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
	RtaBidType      *RtaBidType            `protobuf:"varint,10,opt,name=rta_bid_type,json=rtaBidType,enum=baidu_rta.RtaBidType" json:"rta_bid_type,omitempty"` // 标志是否使用rta溢价
	RtaBid          []*RtaBid              `protobuf:"bytes,11,rep,name=rta_bid,json=rtaBid" json:"rta_bid,omitempty"`                                          // rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
	RtaCpcBid       []*RtaBid              `protobuf:"bytes,12,rep,name=rta_cpc_bid,json=rtaCpcBid" json:"rta_cpc_bid,omitempty"`                               // rta直接出价,必须配合策略id使用,用于cpc广告
}

func (x *RtaApiResponse) Reset() {
	*x = RtaApiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaApiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaApiResponse) ProtoMessage() {}

func (x *RtaApiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaApiResponse.ProtoReflect.Descriptor instead.
func (*RtaApiResponse) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{3}
}

func (x *RtaApiResponse) GetQid() uint64 {
	if x != nil && x.Qid != nil {
		return *x.Qid
	}
	return 0
}

func (x *RtaApiResponse) GetRes() ResType {
	if x != nil && x.Res != nil {
		return *x.Res
	}
	return ResType_ALL
}

func (x *RtaApiResponse) GetUserScore() uint32 {
	if x != nil && x.UserScore != nil {
		return *x.UserScore
	}
	return 0
}

func (x *RtaApiResponse) GetAdResults() []*AdResult {
	if x != nil {
		return x.AdResults
	}
	return nil
}

func (x *RtaApiResponse) GetStrategyResults() []*RtaStrategyAdResult {
	if x != nil {
		return x.StrategyResults
	}
	return nil
}

func (x *RtaApiResponse) GetDpaResults() *DpaResult {
	if x != nil {
		return x.DpaResults
	}
	return nil
}

func (x *RtaApiResponse) GetRtaBidRise() []*BidRise {
	if x != nil {
		return x.RtaBidRise
	}
	return nil
}

func (x *RtaApiResponse) GetPrefetchDate() string {
	if x != nil && x.PrefetchDate != nil {
		return *x.PrefetchDate
	}
	return ""
}

func (x *RtaApiResponse) GetRtaBidType() RtaBidType {
	if x != nil && x.RtaBidType != nil {
		return *x.RtaBidType
	}
	return RtaBidType_RTA_NORMAL
}

func (x *RtaApiResponse) GetRtaBid() []*RtaBid {
	if x != nil {
		return x.RtaBid
	}
	return nil
}

func (x *RtaApiResponse) GetRtaCpcBid() []*RtaBid {
	if x != nil {
		return x.RtaCpcBid
	}
	return nil
}

type AdResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId *uint64  `protobuf:"varint,1,req,name=account_id,json=accountId" json:"account_id,omitempty"` // 账户id，百度方分配的账户id
	UnitId    []uint64 `protobuf:"varint,2,rep,name=unit_id,json=unitId" json:"unit_id,omitempty"`          // 广告单元id
}

func (x *AdResult) Reset() {
	*x = AdResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResult) ProtoMessage() {}

func (x *AdResult) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResult.ProtoReflect.Descriptor instead.
func (*AdResult) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{4}
}

func (x *AdResult) GetAccountId() uint64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *AdResult) GetUnitId() []uint64 {
	if x != nil {
		return x.UnitId
	}
	return nil
}

type RtaStrategyAdResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RtaId *uint64 `protobuf:"varint,1,req,name=rta_id,json=rtaId" json:"rta_id,omitempty"`
}

func (x *RtaStrategyAdResult) Reset() {
	*x = RtaStrategyAdResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaStrategyAdResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaStrategyAdResult) ProtoMessage() {}

func (x *RtaStrategyAdResult) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaStrategyAdResult.ProtoReflect.Descriptor instead.
func (*RtaStrategyAdResult) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{5}
}

func (x *RtaStrategyAdResult) GetRtaId() uint64 {
	if x != nil && x.RtaId != nil {
		return *x.RtaId
	}
	return 0
}

type BidRise struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RtaId   *uint64  `protobuf:"varint,1,opt,name=rta_id,json=rtaId" json:"rta_id,omitempty"`
	BidRise *float32 `protobuf:"fixed32,2,opt,name=bid_rise,json=bidRise" json:"bid_rise,omitempty"` // 溢价系数
}

func (x *BidRise) Reset() {
	*x = BidRise{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRise) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRise) ProtoMessage() {}

func (x *BidRise) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRise.ProtoReflect.Descriptor instead.
func (*BidRise) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{6}
}

func (x *BidRise) GetRtaId() uint64 {
	if x != nil && x.RtaId != nil {
		return *x.RtaId
	}
	return 0
}

func (x *BidRise) GetBidRise() float32 {
	if x != nil && x.BidRise != nil {
		return *x.BidRise
	}
	return 0
}

type RtaBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RtaId       *uint64 `protobuf:"varint,1,req,name=rta_id,json=rtaId" json:"rta_id,omitempty"`
	RtaBidValue *uint32 `protobuf:"varint,2,req,name=rta_bid_value,json=rtaBidValue" json:"rta_bid_value,omitempty"` // rta 直接出价
}

func (x *RtaBid) Reset() {
	*x = RtaBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaBid) ProtoMessage() {}

func (x *RtaBid) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaBid.ProtoReflect.Descriptor instead.
func (*RtaBid) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{7}
}

func (x *RtaBid) GetRtaId() uint64 {
	if x != nil && x.RtaId != nil {
		return *x.RtaId
	}
	return 0
}

func (x *RtaBid) GetRtaBidValue() uint32 {
	if x != nil && x.RtaBidValue != nil {
		return *x.RtaBidValue
	}
	return 0
}

type DpaResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PidLists []*DpaResult_ProductList `protobuf:"bytes,1,rep,name=pid_lists,json=pidLists" json:"pid_lists,omitempty"` // 多组候选商品队列参竞
}

func (x *DpaResult) Reset() {
	*x = DpaResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DpaResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DpaResult) ProtoMessage() {}

func (x *DpaResult) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DpaResult.ProtoReflect.Descriptor instead.
func (*DpaResult) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{8}
}

func (x *DpaResult) GetPidLists() []*DpaResult_ProductList {
	if x != nil {
		return x.PidLists
	}
	return nil
}

// 批量接口
// 在DPA-RTA场景下，一方面高QPS对服务器压力过大，另一方面商品推荐数据和算法耗时无法满足实时要求，因此百度和广告主并不严格要求实时商品参竞，而是通过缓存一段时间的设备号，批量发送批量召回，百度对商品推荐结果进行缓存，在一定时限内供下次直接取用。
type BatchRtaApiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RtaReqList []*RtaApiRequest `protobuf:"bytes,1,rep,name=rta_req_list,json=rtaReqList" json:"rta_req_list,omitempty"`
}

func (x *BatchRtaApiRequest) Reset() {
	*x = BatchRtaApiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRtaApiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRtaApiRequest) ProtoMessage() {}

func (x *BatchRtaApiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRtaApiRequest.ProtoReflect.Descriptor instead.
func (*BatchRtaApiRequest) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{9}
}

func (x *BatchRtaApiRequest) GetRtaReqList() []*RtaApiRequest {
	if x != nil {
		return x.RtaReqList
	}
	return nil
}

type BatchRtaApiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RtaResList []*RtaApiResponse `protobuf:"bytes,1,rep,name=rta_res_list,json=rtaResList" json:"rta_res_list,omitempty"`
}

func (x *BatchRtaApiResponse) Reset() {
	*x = BatchRtaApiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRtaApiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRtaApiResponse) ProtoMessage() {}

func (x *BatchRtaApiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRtaApiResponse.ProtoReflect.Descriptor instead.
func (*BatchRtaApiResponse) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{10}
}

func (x *BatchRtaApiResponse) GetRtaResList() []*RtaApiResponse {
	if x != nil {
		return x.RtaResList
	}
	return nil
}

// 描述一组候选商品
type DpaResult_ProductList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Priority  *DpaResult_ProductList_Priority  `protobuf:"varint,1,req,name=priority,enum=baidu_rta.DpaResult_ProductList_Priority" json:"priority,omitempty"` // 候选商品队列优先级
	PidList   []*DpaResult_ProductList_Product `protobuf:"bytes,2,rep,name=pid_list,json=pidList" json:"pid_list,omitempty"`                                   // 候选商品队列
	CatalogId *uint64                          `protobuf:"varint,3,opt,name=catalog_id,json=catalogId" json:"catalog_id,omitempty"`                            // 商品目录id（可选，非必须）
}

func (x *DpaResult_ProductList) Reset() {
	*x = DpaResult_ProductList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DpaResult_ProductList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DpaResult_ProductList) ProtoMessage() {}

func (x *DpaResult_ProductList) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DpaResult_ProductList.ProtoReflect.Descriptor instead.
func (*DpaResult_ProductList) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{8, 0}
}

func (x *DpaResult_ProductList) GetPriority() DpaResult_ProductList_Priority {
	if x != nil && x.Priority != nil {
		return *x.Priority
	}
	return DpaResult_ProductList_Level_0
}

func (x *DpaResult_ProductList) GetPidList() []*DpaResult_ProductList_Product {
	if x != nil {
		return x.PidList
	}
	return nil
}

func (x *DpaResult_ProductList) GetCatalogId() uint64 {
	if x != nil && x.CatalogId != nil {
		return *x.CatalogId
	}
	return 0
}

// 描述单个商品
type DpaResult_ProductList_Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       *string  `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                               // id,通常是商品outer_id
	BidRatio *float64 `protobuf:"fixed64,2,opt,name=bid_ratio,json=bidRatio" json:"bid_ratio,omitempty"` // 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
	Score    *float64 `protobuf:"fixed64,3,opt,name=score" json:"score,omitempty"`                       // 商品打分，分数越大表示优先级越高（可选，非必须）
}

func (x *DpaResult_ProductList_Product) Reset() {
	*x = DpaResult_ProductList_Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_rta_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DpaResult_ProductList_Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DpaResult_ProductList_Product) ProtoMessage() {}

func (x *DpaResult_ProductList_Product) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_rta_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DpaResult_ProductList_Product.ProtoReflect.Descriptor instead.
func (*DpaResult_ProductList_Product) Descriptor() ([]byte, []int) {
	return file_baidu_rta_proto_rawDescGZIP(), []int{8, 0, 0}
}

func (x *DpaResult_ProductList_Product) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *DpaResult_ProductList_Product) GetBidRatio() float64 {
	if x != nil && x.BidRatio != nil {
		return *x.BidRatio
	}
	return 0
}

func (x *DpaResult_ProductList_Product) GetScore() float64 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

var File_baidu_rta_proto protoreflect.FileDescriptor

var file_baidu_rta_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x22, 0xcb, 0x04, 0x0a,
	0x0d, 0x52, 0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x71, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x03, 0x71, 0x69, 0x64,
	0x12, 0x2a, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x4d, 0x64, 0x35,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x02, 0x28, 0x04, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x4e, 0x0a, 0x13, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x41, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x11, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x5f, 0x72, 0x74, 0x61, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x09, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x73, 0x5f, 0x6d,
	0x61, 0x6a, 0x6f, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x73, 0x4d, 0x61, 0x6a, 0x6f, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x64, 0x70, 0x61, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x44, 0x70,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x62,
	0x65, 0x73, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x65, 0x73, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x65, 0x78, 0x70, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x08, 0x43, 0x61,
	0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61,
	0x69, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x9a, 0x02,
	0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d,
	0x64, 0x35, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x49, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64,
	0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x6d, 0x65, 0x69, 0x32, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x69, 0x6d, 0x65, 0x69, 0x32, 0x4d, 0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x74, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x62, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x61,
	0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x90, 0x04, 0x0a, 0x0e, 0x52,
	0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x71, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x03, 0x71, 0x69, 0x64, 0x12,
	0x24, 0x0a, 0x03, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x62,
	0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x03, 0x72, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x5f, 0x72, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x61,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52,
	0x74, 0x61, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x41, 0x64, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x0b, 0x64, 0x70, 0x61, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x5f, 0x72, 0x74, 0x61, 0x2e, 0x44, 0x70, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a,
	0x64, 0x70, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x0c, 0x72, 0x74,
	0x61, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x72, 0x69, 0x73, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x69, 0x73, 0x65, 0x52, 0x0a, 0x72, 0x74, 0x61, 0x42, 0x69, 0x64, 0x52, 0x69, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0c, 0x72, 0x74, 0x61, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x62, 0x61,
	0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x42, 0x69, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x72, 0x74, 0x61, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x07, 0x72, 0x74, 0x61, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x42,
	0x69, 0x64, 0x52, 0x06, 0x72, 0x74, 0x61, 0x42, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x0b, 0x72, 0x74,
	0x61, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x42,
	0x69, 0x64, 0x52, 0x09, 0x72, 0x74, 0x61, 0x43, 0x70, 0x63, 0x42, 0x69, 0x64, 0x22, 0x42, 0x0a,
	0x08, 0x41, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x6e, 0x69, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x74, 0x49,
	0x64, 0x22, 0x2c, 0x0a, 0x13, 0x52, 0x74, 0x61, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x41, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x74, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x72, 0x74, 0x61, 0x49, 0x64, 0x22,
	0x3b, 0x0a, 0x07, 0x42, 0x69, 0x64, 0x52, 0x69, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x74,
	0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x72, 0x74, 0x61, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x72, 0x69, 0x73, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x07, 0x62, 0x69, 0x64, 0x52, 0x69, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x06,
	0x52, 0x74, 0x61, 0x42, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x74, 0x61, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x72, 0x74, 0x61, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x72, 0x74, 0x61, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x74, 0x61, 0x42, 0x69, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x86, 0x03, 0x0a, 0x09, 0x44, 0x70, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3d, 0x0a, 0x09, 0x70, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x44,
	0x70, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x70, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x1a, 0xb9,
	0x02, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x45,
	0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x44, 0x70, 0x61,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x43, 0x0a, 0x08, 0x70, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f,
	0x72, 0x74, 0x61, 0x2e, 0x44, 0x70, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x52, 0x07, 0x70, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x1a, 0x4c, 0x0a, 0x07, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x31, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x30, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x32, 0x10, 0x02, 0x22, 0x50, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x74, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72,
	0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x72, 0x74, 0x61, 0x52, 0x65, 0x71, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x13,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x72, 0x74, 0x61, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x62, 0x61, 0x69, 0x64,
	0x75, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x41, 0x70, 0x69, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x72, 0x74, 0x61, 0x52, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x2a, 0x2f, 0x0a, 0x08, 0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x48, 0x4f, 0x55, 0x42, 0x41, 0x49, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x4e, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4b, 0x41, 0x49, 0x50, 0x49, 0x4e, 0x47, 0x10,
	0x04, 0x2a, 0x2b, 0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52,
	0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x2a, 0x29,
	0x0a, 0x13, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4d, 0x45, 0x49, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x4f, 0x41, 0x49, 0x44, 0x10, 0x02, 0x2a, 0x37, 0x0a, 0x0a, 0x52, 0x74, 0x61,
	0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x54, 0x41, 0x5f, 0x4e,
	0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x54, 0x41, 0x5f, 0x42,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x54, 0x41, 0x5f, 0x52, 0x49, 0x53, 0x45,
	0x10, 0x02, 0x2a, 0x26, 0x0a, 0x07, 0x52, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a,
	0x03, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x52, 0x54, 0x10, 0x02, 0x42, 0x17, 0x5a, 0x15, 0x72, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x62, 0x2f, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f,
	0x72, 0x74, 0x61,
}

var (
	file_baidu_rta_proto_rawDescOnce sync.Once
	file_baidu_rta_proto_rawDescData = file_baidu_rta_proto_rawDesc
)

func file_baidu_rta_proto_rawDescGZIP() []byte {
	file_baidu_rta_proto_rawDescOnce.Do(func() {
		file_baidu_rta_proto_rawDescData = protoimpl.X.CompressGZIP(file_baidu_rta_proto_rawDescData)
	})
	return file_baidu_rta_proto_rawDescData
}

var file_baidu_rta_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_baidu_rta_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_baidu_rta_proto_goTypes = []interface{}{
	(FlowType)(0),                         // 0: baidu_rta.FlowType
	(OsType)(0),                           // 1: baidu_rta.OsType
	(AndroidDeviceIdType)(0),              // 2: baidu_rta.AndroidDeviceIdType
	(RtaBidType)(0),                       // 3: baidu_rta.RtaBidType
	(ResType)(0),                          // 4: baidu_rta.ResType
	(DpaResult_ProductList_Priority)(0),   // 5: baidu_rta.DpaResult.ProductList.Priority
	(*RtaApiRequest)(nil),                 // 6: baidu_rta.RtaApiRequest
	(*CaidInfo)(nil),                      // 7: baidu_rta.CaidInfo
	(*DeviceInfo)(nil),                    // 8: baidu_rta.DeviceInfo
	(*RtaApiResponse)(nil),                // 9: baidu_rta.RtaApiResponse
	(*AdResult)(nil),                      // 10: baidu_rta.AdResult
	(*RtaStrategyAdResult)(nil),           // 11: baidu_rta.RtaStrategyAdResult
	(*BidRise)(nil),                       // 12: baidu_rta.BidRise
	(*RtaBid)(nil),                        // 13: baidu_rta.RtaBid
	(*DpaResult)(nil),                     // 14: baidu_rta.DpaResult
	(*BatchRtaApiRequest)(nil),            // 15: baidu_rta.BatchRtaApiRequest
	(*BatchRtaApiResponse)(nil),           // 16: baidu_rta.BatchRtaApiResponse
	(*DpaResult_ProductList)(nil),         // 17: baidu_rta.DpaResult.ProductList
	(*DpaResult_ProductList_Product)(nil), // 18: baidu_rta.DpaResult.ProductList.Product
}
var file_baidu_rta_proto_depIdxs = []int32{
	1,  // 0: baidu_rta.RtaApiRequest.os_type:type_name -> baidu_rta.OsType
	2,  // 1: baidu_rta.RtaApiRequest.android_device_type:type_name -> baidu_rta.AndroidDeviceIdType
	8,  // 2: baidu_rta.RtaApiRequest.device_info:type_name -> baidu_rta.DeviceInfo
	0,  // 3: baidu_rta.RtaApiRequest.flow_type:type_name -> baidu_rta.FlowType
	7,  // 4: baidu_rta.DeviceInfo.caid_info:type_name -> baidu_rta.CaidInfo
	4,  // 5: baidu_rta.RtaApiResponse.res:type_name -> baidu_rta.ResType
	10, // 6: baidu_rta.RtaApiResponse.ad_results:type_name -> baidu_rta.AdResult
	11, // 7: baidu_rta.RtaApiResponse.strategy_results:type_name -> baidu_rta.RtaStrategyAdResult
	14, // 8: baidu_rta.RtaApiResponse.dpa_results:type_name -> baidu_rta.DpaResult
	12, // 9: baidu_rta.RtaApiResponse.rta_bid_rise:type_name -> baidu_rta.BidRise
	3,  // 10: baidu_rta.RtaApiResponse.rta_bid_type:type_name -> baidu_rta.RtaBidType
	13, // 11: baidu_rta.RtaApiResponse.rta_bid:type_name -> baidu_rta.RtaBid
	13, // 12: baidu_rta.RtaApiResponse.rta_cpc_bid:type_name -> baidu_rta.RtaBid
	17, // 13: baidu_rta.DpaResult.pid_lists:type_name -> baidu_rta.DpaResult.ProductList
	6,  // 14: baidu_rta.BatchRtaApiRequest.rta_req_list:type_name -> baidu_rta.RtaApiRequest
	9,  // 15: baidu_rta.BatchRtaApiResponse.rta_res_list:type_name -> baidu_rta.RtaApiResponse
	5,  // 16: baidu_rta.DpaResult.ProductList.priority:type_name -> baidu_rta.DpaResult.ProductList.Priority
	18, // 17: baidu_rta.DpaResult.ProductList.pid_list:type_name -> baidu_rta.DpaResult.ProductList.Product
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_baidu_rta_proto_init() }
func file_baidu_rta_proto_init() {
	if File_baidu_rta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_baidu_rta_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaApiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaApiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaStrategyAdResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRise); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DpaResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRtaApiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRtaApiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DpaResult_ProductList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_rta_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DpaResult_ProductList_Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_baidu_rta_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_baidu_rta_proto_goTypes,
		DependencyIndexes: file_baidu_rta_proto_depIdxs,
		EnumInfos:         file_baidu_rta_proto_enumTypes,
		MessageInfos:      file_baidu_rta_proto_msgTypes,
	}.Build()
	File_baidu_rta_proto = out.File
	file_baidu_rta_proto_rawDesc = nil
	file_baidu_rta_proto_goTypes = nil
	file_baidu_rta_proto_depIdxs = nil
}
