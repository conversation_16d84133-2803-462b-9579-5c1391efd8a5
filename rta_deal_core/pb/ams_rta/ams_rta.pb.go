// Copyright (c) 2019 Tencent Inc.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: ams_rta.proto

package ams_rta

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RtaRequest_OperatingSystem int32

const (
	RtaRequest_OS_UNKNOWN RtaRequest_OperatingSystem = 0
	RtaRequest_OS_IOS     RtaRequest_OperatingSystem = 1
	RtaRequest_OS_ANDROID RtaRequest_OperatingSystem = 2
	RtaRequest_OS_WINDOWS RtaRequest_OperatingSystem = 3
	RtaRequest_OS_SYMBIAN RtaRequest_OperatingSystem = 4
	RtaRequest_OS_JAVA    RtaRequest_OperatingSystem = 5
)

// Enum value maps for RtaRequest_OperatingSystem.
var (
	RtaRequest_OperatingSystem_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "OS_IOS",
		2: "OS_ANDROID",
		3: "OS_WINDOWS",
		4: "OS_SYMBIAN",
		5: "OS_JAVA",
	}
	RtaRequest_OperatingSystem_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"OS_IOS":     1,
		"OS_ANDROID": 2,
		"OS_WINDOWS": 3,
		"OS_SYMBIAN": 4,
		"OS_JAVA":    5,
	}
)

func (x RtaRequest_OperatingSystem) Enum() *RtaRequest_OperatingSystem {
	p := new(RtaRequest_OperatingSystem)
	*p = x
	return p
}

func (x RtaRequest_OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaRequest_OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_ams_rta_proto_enumTypes[0].Descriptor()
}

func (RtaRequest_OperatingSystem) Type() protoreflect.EnumType {
	return &file_ams_rta_proto_enumTypes[0]
}

func (x RtaRequest_OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaRequest_OperatingSystem) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaRequest_OperatingSystem(num)
	return nil
}

// Deprecated: Use RtaRequest_OperatingSystem.Descriptor instead.
func (RtaRequest_OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 0}
}

type RtaRequest_Device_DeviceIdTag int32

const (
	RtaRequest_Device_IDFA_MD5_DOUBTFUL      RtaRequest_Device_DeviceIdTag = 0
	RtaRequest_Device_IMEI_MD5_DOUBTFUL      RtaRequest_Device_DeviceIdTag = 1
	RtaRequest_Device_ANDROIDID_MD5_DOUBTFUL RtaRequest_Device_DeviceIdTag = 2
	RtaRequest_Device_MAC_MD5_DOUBTFUL       RtaRequest_Device_DeviceIdTag = 3
	RtaRequest_Device_OAID_MD5_DOUBTFUL      RtaRequest_Device_DeviceIdTag = 4
	RtaRequest_Device_OAID_DOUBTFUL          RtaRequest_Device_DeviceIdTag = 5
	RtaRequest_Device_QAID_DOUBTFUL          RtaRequest_Device_DeviceIdTag = 6
)

// Enum value maps for RtaRequest_Device_DeviceIdTag.
var (
	RtaRequest_Device_DeviceIdTag_name = map[int32]string{
		0: "IDFA_MD5_DOUBTFUL",
		1: "IMEI_MD5_DOUBTFUL",
		2: "ANDROIDID_MD5_DOUBTFUL",
		3: "MAC_MD5_DOUBTFUL",
		4: "OAID_MD5_DOUBTFUL",
		5: "OAID_DOUBTFUL",
		6: "QAID_DOUBTFUL",
	}
	RtaRequest_Device_DeviceIdTag_value = map[string]int32{
		"IDFA_MD5_DOUBTFUL":      0,
		"IMEI_MD5_DOUBTFUL":      1,
		"ANDROIDID_MD5_DOUBTFUL": 2,
		"MAC_MD5_DOUBTFUL":       3,
		"OAID_MD5_DOUBTFUL":      4,
		"OAID_DOUBTFUL":          5,
		"QAID_DOUBTFUL":          6,
	}
)

func (x RtaRequest_Device_DeviceIdTag) Enum() *RtaRequest_Device_DeviceIdTag {
	p := new(RtaRequest_Device_DeviceIdTag)
	*p = x
	return p
}

func (x RtaRequest_Device_DeviceIdTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaRequest_Device_DeviceIdTag) Descriptor() protoreflect.EnumDescriptor {
	return file_ams_rta_proto_enumTypes[1].Descriptor()
}

func (RtaRequest_Device_DeviceIdTag) Type() protoreflect.EnumType {
	return &file_ams_rta_proto_enumTypes[1]
}

func (x RtaRequest_Device_DeviceIdTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaRequest_Device_DeviceIdTag) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaRequest_Device_DeviceIdTag(num)
	return nil
}

// Deprecated: Use RtaRequest_Device_DeviceIdTag.Descriptor instead.
func (RtaRequest_Device_DeviceIdTag) EnumDescriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 1, 0}
}

type RtaRequest_Device_CacheDeviceIdType int32

const (
	RtaRequest_Device_IDFA_MD5      RtaRequest_Device_CacheDeviceIdType = 0
	RtaRequest_Device_IMEI_MD5      RtaRequest_Device_CacheDeviceIdType = 1
	RtaRequest_Device_OAID          RtaRequest_Device_CacheDeviceIdType = 2
	RtaRequest_Device_OAID_MD5      RtaRequest_Device_CacheDeviceIdType = 3
	RtaRequest_Device_ANDROIDID_MD5 RtaRequest_Device_CacheDeviceIdType = 4
	RtaRequest_Device_MAC_MD5       RtaRequest_Device_CacheDeviceIdType = 5
	RtaRequest_Device_NIL           RtaRequest_Device_CacheDeviceIdType = 6 // 所有设备号可疑或为空
	RtaRequest_Device_QAID          RtaRequest_Device_CacheDeviceIdType = 7
)

// Enum value maps for RtaRequest_Device_CacheDeviceIdType.
var (
	RtaRequest_Device_CacheDeviceIdType_name = map[int32]string{
		0: "IDFA_MD5",
		1: "IMEI_MD5",
		2: "OAID",
		3: "OAID_MD5",
		4: "ANDROIDID_MD5",
		5: "MAC_MD5",
		6: "NIL",
		7: "QAID",
	}
	RtaRequest_Device_CacheDeviceIdType_value = map[string]int32{
		"IDFA_MD5":      0,
		"IMEI_MD5":      1,
		"OAID":          2,
		"OAID_MD5":      3,
		"ANDROIDID_MD5": 4,
		"MAC_MD5":       5,
		"NIL":           6,
		"QAID":          7,
	}
)

func (x RtaRequest_Device_CacheDeviceIdType) Enum() *RtaRequest_Device_CacheDeviceIdType {
	p := new(RtaRequest_Device_CacheDeviceIdType)
	*p = x
	return p
}

func (x RtaRequest_Device_CacheDeviceIdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaRequest_Device_CacheDeviceIdType) Descriptor() protoreflect.EnumDescriptor {
	return file_ams_rta_proto_enumTypes[2].Descriptor()
}

func (RtaRequest_Device_CacheDeviceIdType) Type() protoreflect.EnumType {
	return &file_ams_rta_proto_enumTypes[2]
}

func (x RtaRequest_Device_CacheDeviceIdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaRequest_Device_CacheDeviceIdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaRequest_Device_CacheDeviceIdType(num)
	return nil
}

// Deprecated: Use RtaRequest_Device_CacheDeviceIdType.Descriptor instead.
func (RtaRequest_Device_CacheDeviceIdType) EnumDescriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 1, 1}
}

type RtaRequest_FLModel_ModelType int32

const (
	RtaRequest_FLModel_DEFAULT RtaRequest_FLModel_ModelType = 0
)

// Enum value maps for RtaRequest_FLModel_ModelType.
var (
	RtaRequest_FLModel_ModelType_name = map[int32]string{
		0: "DEFAULT",
	}
	RtaRequest_FLModel_ModelType_value = map[string]int32{
		"DEFAULT": 0,
	}
)

func (x RtaRequest_FLModel_ModelType) Enum() *RtaRequest_FLModel_ModelType {
	p := new(RtaRequest_FLModel_ModelType)
	*p = x
	return p
}

func (x RtaRequest_FLModel_ModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaRequest_FLModel_ModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_ams_rta_proto_enumTypes[3].Descriptor()
}

func (RtaRequest_FLModel_ModelType) Type() protoreflect.EnumType {
	return &file_ams_rta_proto_enumTypes[3]
}

func (x RtaRequest_FLModel_ModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaRequest_FLModel_ModelType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaRequest_FLModel_ModelType(num)
	return nil
}

// Deprecated: Use RtaRequest_FLModel_ModelType.Descriptor instead.
func (RtaRequest_FLModel_ModelType) EnumDescriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 4, 0}
}

type RtaResponse_FLModelResInfo_Status int32

const (
	RtaResponse_FLModelResInfo_UNKNOWN RtaResponse_FLModelResInfo_Status = 0
	RtaResponse_FLModelResInfo_SUCCESS RtaResponse_FLModelResInfo_Status = 1
	RtaResponse_FLModelResInfo_FAILED  RtaResponse_FLModelResInfo_Status = 2
)

// Enum value maps for RtaResponse_FLModelResInfo_Status.
var (
	RtaResponse_FLModelResInfo_Status_name = map[int32]string{
		0: "UNKNOWN",
		1: "SUCCESS",
		2: "FAILED",
	}
	RtaResponse_FLModelResInfo_Status_value = map[string]int32{
		"UNKNOWN": 0,
		"SUCCESS": 1,
		"FAILED":  2,
	}
)

func (x RtaResponse_FLModelResInfo_Status) Enum() *RtaResponse_FLModelResInfo_Status {
	p := new(RtaResponse_FLModelResInfo_Status)
	*p = x
	return p
}

func (x RtaResponse_FLModelResInfo_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtaResponse_FLModelResInfo_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_ams_rta_proto_enumTypes[4].Descriptor()
}

func (RtaResponse_FLModelResInfo_Status) Type() protoreflect.EnumType {
	return &file_ams_rta_proto_enumTypes[4]
}

func (x RtaResponse_FLModelResInfo_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RtaResponse_FLModelResInfo_Status) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RtaResponse_FLModelResInfo_Status(num)
	return nil
}

// Deprecated: Use RtaResponse_FLModelResInfo_Status.Descriptor instead.
func (RtaResponse_FLModelResInfo_Status) EnumDescriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{2, 2, 0}
}

type RtaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求唯一标识，对应归因中的Trace ID，非腾讯广告系统中的请求ID
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// true表示探测网络延迟
	IsPing *bool `protobuf:"varint,2,opt,name=is_ping,json=isPing" json:"is_ping,omitempty"`
	// true表示测试请求，广告不会被展示和计费
	IsTest      *bool                    `protobuf:"varint,3,opt,name=is_test,json=isTest" json:"is_test,omitempty"`
	Impressions []*RtaRequest_Impression `protobuf:"bytes,4,rep,name=impressions" json:"impressions,omitempty"`
	Device      *RtaRequest_Device       `protobuf:"bytes,5,opt,name=device" json:"device,omitempty"`
	// 已安装的app包的hash值，不对外开放
	TargetInstalledAppHashs []uint32 `protobuf:"varint,84,rep,packed,name=target_installed_app_hashs,json=targetInstalledAppHashs" json:"target_installed_app_hashs,omitempty"`
	// 流量分类，默认为0，不对外开放
	// btw: 不再支持，无需关注
	SitesetId *uint64                  `protobuf:"varint,6,opt,name=siteset_id,json=sitesetId" json:"siteset_id,omitempty"`
	Exps      []*RtaRequest_Experiment `protobuf:"bytes,9,rep,name=exps" json:"exps,omitempty"`
	// 请求中包含的所有规格尺寸id, 字段含义： 宽*10000 + 高
	SpecSizeIds      []uint32                   `protobuf:"varint,10,rep,packed,name=spec_size_ids,json=specSizeIds" json:"spec_size_ids,omitempty"`
	AllFeatureGroups []*RtaRequest_FeatureGroup `protobuf:"bytes,17,rep,name=all_feature_groups,json=allFeatureGroups" json:"all_feature_groups,omitempty"`
	FlModels         []*RtaRequest_FLModel      `protobuf:"bytes,14,rep,name=fl_models,json=flModels" json:"fl_models,omitempty"`
	// btw: 不再支持，无需关注
	MediaInfo *RtaRequest_MediaInfo `protobuf:"bytes,99,opt,name=media_info,json=mediaInfo" json:"media_info,omitempty"`
}

func (x *RtaRequest) Reset() {
	*x = RtaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest) ProtoMessage() {}

func (x *RtaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest.ProtoReflect.Descriptor instead.
func (*RtaRequest) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0}
}

func (x *RtaRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RtaRequest) GetIsPing() bool {
	if x != nil && x.IsPing != nil {
		return *x.IsPing
	}
	return false
}

func (x *RtaRequest) GetIsTest() bool {
	if x != nil && x.IsTest != nil {
		return *x.IsTest
	}
	return false
}

func (x *RtaRequest) GetImpressions() []*RtaRequest_Impression {
	if x != nil {
		return x.Impressions
	}
	return nil
}

func (x *RtaRequest) GetDevice() *RtaRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RtaRequest) GetTargetInstalledAppHashs() []uint32 {
	if x != nil {
		return x.TargetInstalledAppHashs
	}
	return nil
}

func (x *RtaRequest) GetSitesetId() uint64 {
	if x != nil && x.SitesetId != nil {
		return *x.SitesetId
	}
	return 0
}

func (x *RtaRequest) GetExps() []*RtaRequest_Experiment {
	if x != nil {
		return x.Exps
	}
	return nil
}

func (x *RtaRequest) GetSpecSizeIds() []uint32 {
	if x != nil {
		return x.SpecSizeIds
	}
	return nil
}

func (x *RtaRequest) GetAllFeatureGroups() []*RtaRequest_FeatureGroup {
	if x != nil {
		return x.AllFeatureGroups
	}
	return nil
}

func (x *RtaRequest) GetFlModels() []*RtaRequest_FLModel {
	if x != nil {
		return x.FlModels
	}
	return nil
}

func (x *RtaRequest) GetMediaInfo() *RtaRequest_MediaInfo {
	if x != nil {
		return x.MediaInfo
	}
	return nil
}

type DynamicProductInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 商品库id
	ProductLib *uint64 `protobuf:"varint,1,opt,name=product_lib,json=productLib" json:"product_lib,omitempty"`
	// 商品信息
	Products []*DynamicProductInfo_Product `protobuf:"bytes,2,rep,name=products" json:"products,omitempty"`
}

func (x *DynamicProductInfo) Reset() {
	*x = DynamicProductInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicProductInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicProductInfo) ProtoMessage() {}

func (x *DynamicProductInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicProductInfo.ProtoReflect.Descriptor instead.
func (*DynamicProductInfo) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{1}
}

func (x *DynamicProductInfo) GetProductLib() uint64 {
	if x != nil && x.ProductLib != nil {
		return *x.ProductLib
	}
	return 0
}

func (x *DynamicProductInfo) GetProducts() []*DynamicProductInfo_Product {
	if x != nil {
		return x.Products
	}
	return nil
}

type RtaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 来自RtaRequest.id，对应归因中的Rta Trace ID，非腾讯广告系统中的请求ID
	RequestId *string `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	// 返回的状态码，0为选择该流量，非0为过滤，该状态码落地日志，方便定位问题
	// 如果有impression，则优先看impression的回复
	Code        *uint32                   `protobuf:"varint,2,opt,name=code" json:"code,omitempty"`
	Impressions []*RtaResponse_Impression `protobuf:"bytes,3,rep,name=impressions" json:"impressions,omitempty"`
	// 收到RtaRequest至发送完RtaResponse的用时，单位:毫秒
	ProcessingTimeMs *int32 `protobuf:"varint,4,opt,name=processing_time_ms,json=processingTimeMs" json:"processing_time_ms,omitempty"`
	// 指定禁止的广告主账号，如果有impression，则优先看impression的回复
	ExcludeAdvertiserId []uint64 `protobuf:"varint,5,rep,packed,name=exclude_advertiser_id,json=excludeAdvertiserId" json:"exclude_advertiser_id,omitempty"`
	// 实时指定对该设备的缓存时间，单位秒。
	// 需提前开启缓存功能，该值需高于配置的缓存时间，且不得超过7天
	ResponseCacheTime *uint32 `protobuf:"varint,7,opt,name=response_cache_time,json=responseCacheTime" json:"response_cache_time,omitempty"`
	//  自定义策略，当code为0选择该流量时，只选择包含该策略的所有广告主，如果有impression，则优先看impression的回复
	OutTargetId []string                  `protobuf:"bytes,10,rep,name=out_target_id,json=outTargetId" json:"out_target_id,omitempty"`
	TargetInfos []*RtaResponse_TargetInfo `protobuf:"bytes,12,rep,name=target_infos,json=targetInfos" json:"target_infos,omitempty"`
	// 广告id白名单
	AidWhitelist    []uint64                      `protobuf:"varint,13,rep,packed,name=aid_whitelist,json=aidWhitelist" json:"aid_whitelist,omitempty"`
	FlModelResInfos []*RtaResponse_FLModelResInfo `protobuf:"bytes,16,rep,name=fl_model_res_infos,json=flModelResInfos" json:"fl_model_res_infos,omitempty"`
}

func (x *RtaResponse) Reset() {
	*x = RtaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaResponse) ProtoMessage() {}

func (x *RtaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaResponse.ProtoReflect.Descriptor instead.
func (*RtaResponse) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{2}
}

func (x *RtaResponse) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *RtaResponse) GetCode() uint32 {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return 0
}

func (x *RtaResponse) GetImpressions() []*RtaResponse_Impression {
	if x != nil {
		return x.Impressions
	}
	return nil
}

func (x *RtaResponse) GetProcessingTimeMs() int32 {
	if x != nil && x.ProcessingTimeMs != nil {
		return *x.ProcessingTimeMs
	}
	return 0
}

func (x *RtaResponse) GetExcludeAdvertiserId() []uint64 {
	if x != nil {
		return x.ExcludeAdvertiserId
	}
	return nil
}

func (x *RtaResponse) GetResponseCacheTime() uint32 {
	if x != nil && x.ResponseCacheTime != nil {
		return *x.ResponseCacheTime
	}
	return 0
}

func (x *RtaResponse) GetOutTargetId() []string {
	if x != nil {
		return x.OutTargetId
	}
	return nil
}

func (x *RtaResponse) GetTargetInfos() []*RtaResponse_TargetInfo {
	if x != nil {
		return x.TargetInfos
	}
	return nil
}

func (x *RtaResponse) GetAidWhitelist() []uint64 {
	if x != nil {
		return x.AidWhitelist
	}
	return nil
}

func (x *RtaResponse) GetFlModelResInfos() []*RtaResponse_FLModelResInfo {
	if x != nil {
		return x.FlModelResInfos
	}
	return nil
}

// Impression为高级功能，详见Q&A8
// btw: 不再支持，无需关注(已编辑)
type RtaRequest_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 该请求范围内，Impression唯一标识
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// 加密的广告位id
	PlacementId *string `protobuf:"bytes,2,opt,name=placement_id,json=placementId" json:"placement_id,omitempty"`
	// 创意形式ID
	CrtTemplateId []uint32 `protobuf:"varint,3,rep,packed,name=crt_template_id,json=crtTemplateId" json:"crt_template_id,omitempty"`
	// 广告位一级场景Id
	OnePositionScene *uint32 `protobuf:"varint,97,opt,name=one_position_scene,json=onePositionScene" json:"one_position_scene,omitempty"`
	// 广告位类型
	PlacementType *uint32 `protobuf:"varint,99,opt,name=placement_type,json=placementType" json:"placement_type,omitempty"`
}

func (x *RtaRequest_Impression) Reset() {
	*x = RtaRequest_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_Impression) ProtoMessage() {}

func (x *RtaRequest_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_Impression.ProtoReflect.Descriptor instead.
func (*RtaRequest_Impression) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RtaRequest_Impression) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RtaRequest_Impression) GetPlacementId() string {
	if x != nil && x.PlacementId != nil {
		return *x.PlacementId
	}
	return ""
}

func (x *RtaRequest_Impression) GetCrtTemplateId() []uint32 {
	if x != nil {
		return x.CrtTemplateId
	}
	return nil
}

func (x *RtaRequest_Impression) GetOnePositionScene() uint32 {
	if x != nil && x.OnePositionScene != nil {
		return *x.OnePositionScene
	}
	return 0
}

func (x *RtaRequest_Impression) GetPlacementType() uint32 {
	if x != nil && x.PlacementType != nil {
		return *x.PlacementType
	}
	return 0
}

type RtaRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os *RtaRequest_OperatingSystem `protobuf:"varint,1,opt,name=os,enum=ams_rta.RtaRequest_OperatingSystem" json:"os,omitempty"`
	// iOS设备的IDFA md5sum
	IdfaMd5Sum *string `protobuf:"bytes,2,opt,name=idfa_md5sum,json=idfaMd5sum" json:"idfa_md5sum,omitempty"`
	// Android设备的IMEI md5sum
	ImeiMd5Sum *string `protobuf:"bytes,3,opt,name=imei_md5sum,json=imeiMd5sum" json:"imei_md5sum,omitempty"`
	// Android设备的Android ID
	AndroidIdMd5Sum *string `protobuf:"bytes,4,opt,name=android_id_md5sum,json=androidIdMd5sum" json:"android_id_md5sum,omitempty"`
	// 用户设备的mac地址
	MacMd5Sum *string `protobuf:"bytes,5,opt,name=mac_md5sum,json=macMd5sum" json:"mac_md5sum,omitempty"`
	// Android设备的oaid  md5sum
	OaidMd5Sum *string `protobuf:"bytes,6,opt,name=oaid_md5sum,json=oaidMd5sum" json:"oaid_md5sum,omitempty"`
	// 用户IP
	Ip *string `protobuf:"bytes,7,opt,name=ip" json:"ip,omitempty"`
	// Android设备的oaid 原值
	// btw: 23年6月30日下线
	Oaid *string `protobuf:"bytes,8,opt,name=oaid" json:"oaid,omitempty"`
	// 上述设备id是否可疑标志
	DoubtfulIdsList []RtaRequest_Device_DeviceIdTag `protobuf:"varint,9,rep,name=doubtful_ids_list,json=doubtfulIdsList,enum=ams_rta.RtaRequest_Device_DeviceIdTag" json:"doubtful_ids_list,omitempty"`
	// 本次请求作为缓存key的设备号类型
	CachedDeviceidType *RtaRequest_Device_CacheDeviceIdType `protobuf:"varint,10,opt,name=cached_deviceid_type,json=cachedDeviceidType,enum=ams_rta.RtaRequest_Device_CacheDeviceIdType" json:"cached_deviceid_type,omitempty"`
	QaidInfos          []*RtaRequest_Device_QaidInfo        `protobuf:"bytes,13,rep,name=qaid_infos,json=qaidInfos" json:"qaid_infos,omitempty"`
}

func (x *RtaRequest_Device) Reset() {
	*x = RtaRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_Device) ProtoMessage() {}

func (x *RtaRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_Device.ProtoReflect.Descriptor instead.
func (*RtaRequest_Device) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 1}
}

func (x *RtaRequest_Device) GetOs() RtaRequest_OperatingSystem {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return RtaRequest_OS_UNKNOWN
}

func (x *RtaRequest_Device) GetIdfaMd5Sum() string {
	if x != nil && x.IdfaMd5Sum != nil {
		return *x.IdfaMd5Sum
	}
	return ""
}

func (x *RtaRequest_Device) GetImeiMd5Sum() string {
	if x != nil && x.ImeiMd5Sum != nil {
		return *x.ImeiMd5Sum
	}
	return ""
}

func (x *RtaRequest_Device) GetAndroidIdMd5Sum() string {
	if x != nil && x.AndroidIdMd5Sum != nil {
		return *x.AndroidIdMd5Sum
	}
	return ""
}

func (x *RtaRequest_Device) GetMacMd5Sum() string {
	if x != nil && x.MacMd5Sum != nil {
		return *x.MacMd5Sum
	}
	return ""
}

func (x *RtaRequest_Device) GetOaidMd5Sum() string {
	if x != nil && x.OaidMd5Sum != nil {
		return *x.OaidMd5Sum
	}
	return ""
}

func (x *RtaRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *RtaRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *RtaRequest_Device) GetDoubtfulIdsList() []RtaRequest_Device_DeviceIdTag {
	if x != nil {
		return x.DoubtfulIdsList
	}
	return nil
}

func (x *RtaRequest_Device) GetCachedDeviceidType() RtaRequest_Device_CacheDeviceIdType {
	if x != nil && x.CachedDeviceidType != nil {
		return *x.CachedDeviceidType
	}
	return RtaRequest_Device_IDFA_MD5
}

func (x *RtaRequest_Device) GetQaidInfos() []*RtaRequest_Device_QaidInfo {
	if x != nil {
		return x.QaidInfos
	}
	return nil
}

// 实验信息
type RtaRequest_Experiment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpId *uint32 `protobuf:"varint,1,opt,name=exp_id,json=expId" json:"exp_id,omitempty"`
}

func (x *RtaRequest_Experiment) Reset() {
	*x = RtaRequest_Experiment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_Experiment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_Experiment) ProtoMessage() {}

func (x *RtaRequest_Experiment) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_Experiment.ProtoReflect.Descriptor instead.
func (*RtaRequest_Experiment) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 2}
}

func (x *RtaRequest_Experiment) GetExpId() uint32 {
	if x != nil && x.ExpId != nil {
		return *x.ExpId
	}
	return 0
}

type RtaRequest_FeatureGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeaturesType *uint64 `protobuf:"varint,2,opt,name=features_type,json=featuresType" json:"features_type,omitempty"` // 取值=1001代表是换机用户，否则为非换机用户
}

func (x *RtaRequest_FeatureGroup) Reset() {
	*x = RtaRequest_FeatureGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_FeatureGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_FeatureGroup) ProtoMessage() {}

func (x *RtaRequest_FeatureGroup) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_FeatureGroup.ProtoReflect.Descriptor instead.
func (*RtaRequest_FeatureGroup) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 3}
}

func (x *RtaRequest_FeatureGroup) GetFeaturesType() uint64 {
	if x != nil && x.FeaturesType != nil {
		return *x.FeaturesType
	}
	return 0
}

// 联邦学习模型 Federated Learning Model
type RtaRequest_FLModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId      *uint64                       `protobuf:"varint,1,opt,name=model_id,json=modelId" json:"model_id,omitempty"`
	ModelName    *string                       `protobuf:"bytes,2,opt,name=model_name,json=modelName" json:"model_name,omitempty"`
	ModelVersion *string                       `protobuf:"bytes,3,opt,name=model_version,json=modelVersion" json:"model_version,omitempty"`
	ModelType    *RtaRequest_FLModel_ModelType `protobuf:"varint,4,opt,name=model_type,json=modelType,enum=ams_rta.RtaRequest_FLModel_ModelType" json:"model_type,omitempty"`
	Embedding    []float32                     `protobuf:"fixed32,5,rep,packed,name=embedding" json:"embedding,omitempty"`
}

func (x *RtaRequest_FLModel) Reset() {
	*x = RtaRequest_FLModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_FLModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_FLModel) ProtoMessage() {}

func (x *RtaRequest_FLModel) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_FLModel.ProtoReflect.Descriptor instead.
func (*RtaRequest_FLModel) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 4}
}

func (x *RtaRequest_FLModel) GetModelId() uint64 {
	if x != nil && x.ModelId != nil {
		return *x.ModelId
	}
	return 0
}

func (x *RtaRequest_FLModel) GetModelName() string {
	if x != nil && x.ModelName != nil {
		return *x.ModelName
	}
	return ""
}

func (x *RtaRequest_FLModel) GetModelVersion() string {
	if x != nil && x.ModelVersion != nil {
		return *x.ModelVersion
	}
	return ""
}

func (x *RtaRequest_FLModel) GetModelType() RtaRequest_FLModel_ModelType {
	if x != nil && x.ModelType != nil {
		return *x.ModelType
	}
	return RtaRequest_FLModel_DEFAULT
}

func (x *RtaRequest_FLModel) GetEmbedding() []float32 {
	if x != nil {
		return x.Embedding
	}
	return nil
}

type RtaRequest_MediaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体id
	MediumId *uint64 `protobuf:"varint,2,opt,name=medium_id,json=mediumId" json:"medium_id,omitempty"`
}

func (x *RtaRequest_MediaInfo) Reset() {
	*x = RtaRequest_MediaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_MediaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_MediaInfo) ProtoMessage() {}

func (x *RtaRequest_MediaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_MediaInfo.ProtoReflect.Descriptor instead.
func (*RtaRequest_MediaInfo) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 5}
}

func (x *RtaRequest_MediaInfo) GetMediumId() uint64 {
	if x != nil && x.MediumId != nil {
		return *x.MediumId
	}
	return 0
}

// 多版本QAID信息
type RtaRequest_Device_QaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// QAID版本，同时区分QAID含义
	Version *uint32 `protobuf:"varint,1,opt,name=version" json:"version,omitempty"`
	// QAID值
	Qaid *string `protobuf:"bytes,2,opt,name=qaid" json:"qaid,omitempty"`
	// 广协原始版本
	OriginVersion *string `protobuf:"bytes,3,opt,name=origin_version,json=originVersion" json:"origin_version,omitempty"`
	// QAID md5值，qaid原值进行md5后小写输出
	QaidMd5Sum *string `protobuf:"bytes,4,opt,name=qaid_md5sum,json=qaidMd5sum" json:"qaid_md5sum,omitempty"`
}

func (x *RtaRequest_Device_QaidInfo) Reset() {
	*x = RtaRequest_Device_QaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaRequest_Device_QaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaRequest_Device_QaidInfo) ProtoMessage() {}

func (x *RtaRequest_Device_QaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaRequest_Device_QaidInfo.ProtoReflect.Descriptor instead.
func (*RtaRequest_Device_QaidInfo) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *RtaRequest_Device_QaidInfo) GetVersion() uint32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *RtaRequest_Device_QaidInfo) GetQaid() string {
	if x != nil && x.Qaid != nil {
		return *x.Qaid
	}
	return ""
}

func (x *RtaRequest_Device_QaidInfo) GetOriginVersion() string {
	if x != nil && x.OriginVersion != nil {
		return *x.OriginVersion
	}
	return ""
}

func (x *RtaRequest_Device_QaidInfo) GetQaidMd5Sum() string {
	if x != nil && x.QaidMd5Sum != nil {
		return *x.QaidMd5Sum
	}
	return ""
}

type DynamicProductInfo_Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 商品id
	Id *uint64 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	// 商品推荐的权重，取值范围[1, 100]，取值越高越重要，如果不传或者为0，按商品回复的顺序排序
	Priority *uint32 `protobuf:"varint,2,opt,name=priority" json:"priority,omitempty"`
	// 用户和商品产生互动的时间，unix时间戳，如果不传，则以回复RTA时间为准
	Timestamp *uint64 `protobuf:"varint,3,opt,name=timestamp" json:"timestamp,omitempty"`
}

func (x *DynamicProductInfo_Product) Reset() {
	*x = DynamicProductInfo_Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicProductInfo_Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicProductInfo_Product) ProtoMessage() {}

func (x *DynamicProductInfo_Product) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicProductInfo_Product.ProtoReflect.Descriptor instead.
func (*DynamicProductInfo_Product) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{1, 0}
}

func (x *DynamicProductInfo_Product) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *DynamicProductInfo_Product) GetPriority() uint32 {
	if x != nil && x.Priority != nil {
		return *x.Priority
	}
	return 0
}

func (x *DynamicProductInfo_Product) GetTimestamp() uint64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

// Impression为高级功能，详见QA
type RtaResponse_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// RtaRequest.Impression.id
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// impression级状态码，0为选择该流量，非0为过滤，该状态码落地日志，方便定位问题
	Code *uint32 `protobuf:"varint,2,opt,name=code" json:"code,omitempty"`
	// 指定禁止的广告主账号
	ExcludeAdvertiserId []uint64 `protobuf:"varint,3,rep,packed,name=exclude_advertiser_id,json=excludeAdvertiserId" json:"exclude_advertiser_id,omitempty"`
	// 自定义的策略，当code为0选择该流量时，只选择包含该策略的所有广告主
	OutTargetId []string `protobuf:"bytes,5,rep,name=out_target_id,json=outTargetId" json:"out_target_id,omitempty"`
}

func (x *RtaResponse_Impression) Reset() {
	*x = RtaResponse_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaResponse_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaResponse_Impression) ProtoMessage() {}

func (x *RtaResponse_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaResponse_Impression.ProtoReflect.Descriptor instead.
func (*RtaResponse_Impression) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RtaResponse_Impression) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RtaResponse_Impression) GetCode() uint32 {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return 0
}

func (x *RtaResponse_Impression) GetExcludeAdvertiserId() []uint64 {
	if x != nil {
		return x.ExcludeAdvertiserId
	}
	return nil
}

func (x *RtaResponse_Impression) GetOutTargetId() []string {
	if x != nil {
		return x.OutTargetId
	}
	return nil
}

// 策略绑定信息
type RtaResponse_TargetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 自定义策略，和请求级out_target_id一致
	// btw: 这里不代表回复参竞，只代表给策略id指定下面的出价等额外信息，是否参竞需要在外层回复out_target_id（repeated string out_target_id = 10）...(已编辑)
	OutTargetId *string `protobuf:"bytes,1,opt,name=out_target_id,json=outTargetId" json:"out_target_id,omitempty"`
	// 用户加权值，取值范围[1~10] 10个等级，超出该范围按空处理
	// btw: 不对外开放，请使用user_weight_factor
	UserWeight *int32 `protobuf:"varint,2,opt,name=user_weight,json=userWeight" json:"user_weight,omitempty"`
	// 广告主对该策略下的广告实时回复cpc价格（高于订单价格才有效）, 单位：分/次
	CpcPrice *uint32 `protobuf:"varint,3,opt,name=cpc_price,json=cpcPrice" json:"cpc_price,omitempty"`
	// 针对同一用户，为指定的out_target_id分别推荐商品信息，优先级高于请求级推荐商品
	DynamicProductInfos []*DynamicProductInfo `protobuf:"bytes,4,rep,name=dynamic_product_infos,json=dynamicProductInfos" json:"dynamic_product_infos,omitempty"`
	// 广告主对该策略下的广告实时回复cpa目标价格, 单位：分/次
	// btw: 作用于ocpm/ocpc广告
	CpaPrice *uint32 `protobuf:"varint,5,opt,name=cpa_price,json=cpaPrice" json:"cpa_price,omitempty"`
	// 用户加权系数， 有上下限，默认0.2~5
	// btw: 作用于ocpm/ocpc广告
	UserWeightFactor *float32 `protobuf:"fixed32,6,opt,name=user_weight_factor,json=userWeightFactor" json:"user_weight_factor,omitempty"`
	// 广告主回复的cpc系数， 有上下限，默认0.2~5
	// btw: 作用于cpc广告
	CpcFactor *float32 `protobuf:"fixed32,7,opt,name=cpc_factor,json=cpcFactor" json:"cpc_factor,omitempty"`
	// 广告ID，支持广告级改价能力，该字段和out_target_id、advertiser_id三选一
	Aid *uint64 `protobuf:"varint,8,opt,name=aid" json:"aid,omitempty"`
	// 策略级自定义tag，int型，优先建议使用int型
	DspTag *uint64 `protobuf:"varint,9,opt,name=dsp_tag,json=dspTag" json:"dsp_tag,omitempty"`
	// 策略级自定义tag字符串，最大长度40字节,仅限字母数字下划线
	DspTagStr *string `protobuf:"bytes,10,opt,name=dsp_tag_str,json=dspTagStr" json:"dsp_tag_str,omitempty"`
	// pCVR
	Pcvr *float32 `protobuf:"fixed32,11,opt,name=pcvr" json:"pcvr,omitempty"`
	// 广告主ID，支持账号级别改价能力，该字段和out_target_id，aid三选一
	AdvertiserId *uint64 `protobuf:"varint,12,opt,name=advertiser_id,json=advertiserId" json:"advertiser_id,omitempty"`
}

func (x *RtaResponse_TargetInfo) Reset() {
	*x = RtaResponse_TargetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaResponse_TargetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaResponse_TargetInfo) ProtoMessage() {}

func (x *RtaResponse_TargetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaResponse_TargetInfo.ProtoReflect.Descriptor instead.
func (*RtaResponse_TargetInfo) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{2, 1}
}

func (x *RtaResponse_TargetInfo) GetOutTargetId() string {
	if x != nil && x.OutTargetId != nil {
		return *x.OutTargetId
	}
	return ""
}

func (x *RtaResponse_TargetInfo) GetUserWeight() int32 {
	if x != nil && x.UserWeight != nil {
		return *x.UserWeight
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetCpcPrice() uint32 {
	if x != nil && x.CpcPrice != nil {
		return *x.CpcPrice
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetDynamicProductInfos() []*DynamicProductInfo {
	if x != nil {
		return x.DynamicProductInfos
	}
	return nil
}

func (x *RtaResponse_TargetInfo) GetCpaPrice() uint32 {
	if x != nil && x.CpaPrice != nil {
		return *x.CpaPrice
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetUserWeightFactor() float32 {
	if x != nil && x.UserWeightFactor != nil {
		return *x.UserWeightFactor
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetCpcFactor() float32 {
	if x != nil && x.CpcFactor != nil {
		return *x.CpcFactor
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetAid() uint64 {
	if x != nil && x.Aid != nil {
		return *x.Aid
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetDspTag() uint64 {
	if x != nil && x.DspTag != nil {
		return *x.DspTag
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetDspTagStr() string {
	if x != nil && x.DspTagStr != nil {
		return *x.DspTagStr
	}
	return ""
}

func (x *RtaResponse_TargetInfo) GetPcvr() float32 {
	if x != nil && x.Pcvr != nil {
		return *x.Pcvr
	}
	return 0
}

func (x *RtaResponse_TargetInfo) GetAdvertiserId() uint64 {
	if x != nil && x.AdvertiserId != nil {
		return *x.AdvertiserId
	}
	return 0
}

// 联邦学习模型使用情况
type RtaResponse_FLModelResInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId *uint64                            `protobuf:"varint,1,opt,name=model_id,json=modelId" json:"model_id,omitempty"`
	Status  *RtaResponse_FLModelResInfo_Status `protobuf:"varint,2,opt,name=status,enum=ams_rta.RtaResponse_FLModelResInfo_Status" json:"status,omitempty"`
}

func (x *RtaResponse_FLModelResInfo) Reset() {
	*x = RtaResponse_FLModelResInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ams_rta_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaResponse_FLModelResInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaResponse_FLModelResInfo) ProtoMessage() {}

func (x *RtaResponse_FLModelResInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ams_rta_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaResponse_FLModelResInfo.ProtoReflect.Descriptor instead.
func (*RtaResponse_FLModelResInfo) Descriptor() ([]byte, []int) {
	return file_ams_rta_proto_rawDescGZIP(), []int{2, 2}
}

func (x *RtaResponse_FLModelResInfo) GetModelId() uint64 {
	if x != nil && x.ModelId != nil {
		return *x.ModelId
	}
	return 0
}

func (x *RtaResponse_FLModelResInfo) GetStatus() RtaResponse_FLModelResInfo_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return RtaResponse_FLModelResInfo_UNKNOWN
}

var File_ams_rta_proto protoreflect.FileDescriptor

var file_ams_rta_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x22, 0x9e, 0x11, 0x0a, 0x0a, 0x52, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x67,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x73, 0x54, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x69, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b,
	0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x6d,
	0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x3f, 0x0a, 0x1a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x73, 0x18, 0x54, 0x20,
	0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x01, 0x52, 0x17, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x48, 0x61, 0x73, 0x68, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x74, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x69, 0x74, 0x65, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x65,
	0x78, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0b,
	0x73, 0x70, 0x65, 0x63, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x4e, 0x0a, 0x12, 0x61,
	0x6c, 0x6c, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74,
	0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x66,
	0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x46, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x66, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x6d, 0x73, 0x5f,
	0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0xc0, 0x01, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x63, 0x72, 0x74, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x63, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x6e, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x61, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x6f,
	0x6e, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xb3, 0x07, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x33, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d,
	0x64, 0x35, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x66,
	0x61, 0x4d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x65, 0x69, 0x5f,
	0x6d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d,
	0x65, 0x69, 0x4d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x4d, 0x64,
	0x35, 0x73, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x73,
	0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35,
	0x73, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x73,
	0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64,
	0x35, 0x73, 0x75, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x11, 0x64, 0x6f, 0x75, 0x62,
	0x74, 0x66, 0x75, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x54, 0x61, 0x67, 0x52, 0x0f, 0x64, 0x6f, 0x75,
	0x62, 0x74, 0x66, 0x75, 0x6c, 0x49, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5e, 0x0a, 0x14,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x69, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x6d, 0x73,
	0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x63, 0x61, 0x63, 0x68, 0x65, 0x64,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0a,
	0x71, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x51, 0x61, 0x69,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x71, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x1a, 0x80, 0x01, 0x0a, 0x08, 0x51, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x71, 0x61, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x71, 0x61, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x73, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x71, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35,
	0x73, 0x75, 0x6d, 0x22, 0xaa, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x54, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x44, 0x46, 0x41, 0x5f, 0x4d, 0x44, 0x35, 0x5f,
	0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x4d,
	0x45, 0x49, 0x5f, 0x4d, 0x44, 0x35, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10,
	0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x49, 0x44, 0x5f, 0x4d,
	0x44, 0x35, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x41, 0x43, 0x5f, 0x4d, 0x44, 0x35, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55,
	0x4c, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x41, 0x49, 0x44, 0x5f, 0x4d, 0x44, 0x35, 0x5f,
	0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x41,
	0x49, 0x44, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10, 0x05, 0x12, 0x11, 0x0a,
	0x0d, 0x51, 0x41, 0x49, 0x44, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x54, 0x46, 0x55, 0x4c, 0x10, 0x06,
	0x22, 0x7a, 0x0a, 0x11, 0x43, 0x61, 0x63, 0x68, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x44, 0x46, 0x41, 0x5f, 0x4d, 0x44,
	0x35, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4d, 0x45, 0x49, 0x5f, 0x4d, 0x44, 0x35, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x41, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4f,
	0x41, 0x49, 0x44, 0x5f, 0x4d, 0x44, 0x35, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x4e, 0x44,
	0x52, 0x4f, 0x49, 0x44, 0x49, 0x44, 0x5f, 0x4d, 0x44, 0x35, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x4d, 0x41, 0x43, 0x5f, 0x4d, 0x44, 0x35, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x49, 0x4c,
	0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x51, 0x41, 0x49, 0x44, 0x10, 0x07, 0x1a, 0x23, 0x0a, 0x0a,
	0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x78,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x65, 0x78, 0x70, 0x49,
	0x64, 0x1a, 0x33, 0x0a, 0x0c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xea, 0x01, 0x0a, 0x07, 0x46, 0x4c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x44, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e,
	0x52, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x4c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x02, 0x42, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x18, 0x0a, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c,
	0x54, 0x10, 0x00, 0x1a, 0x28, 0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x49, 0x64, 0x22, 0x6a, 0x0a,
	0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x53, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x4f, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a,
	0x4f, 0x53, 0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a,
	0x4f, 0x53, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x49, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x4f, 0x53, 0x5f, 0x4a, 0x41, 0x56, 0x41, 0x10, 0x05, 0x22, 0xcb, 0x01, 0x0a, 0x12, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6c, 0x69, 0x62, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69,
	0x62, 0x12, 0x3f, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x73, 0x1a, 0x53, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xdd, 0x09, 0x0a, 0x0b, 0x52, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a,
	0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x36, 0x0a, 0x15, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10, 0x01, 0x52, 0x13,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x27, 0x0a, 0x0d, 0x61,
	0x69, 0x64, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x04, 0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x61, 0x69, 0x64, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x12, 0x66, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x66, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x8c, 0x01, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x15, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x42, 0x02, 0x10, 0x01, 0x52, 0x13, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x75, 0x74, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x1a, 0xad, 0x03, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x75, 0x74,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x63,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x70,
	0x63, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x15, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x61, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x70, 0x61, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x70, 0x63, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x70, 0x63, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x61, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x73, 0x70, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x73, 0x70, 0x54, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0b,
	0x64, 0x73, 0x70, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x64, 0x73, 0x70, 0x54, 0x61, 0x67, 0x53, 0x74, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x63, 0x76, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x63, 0x76, 0x72,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x9f, 0x01, 0x0a, 0x0e, 0x46, 0x4c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61, 0x2e, 0x52, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x4c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x42, 0x15, 0x5a, 0x13, 0x72, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x6d, 0x73, 0x5f, 0x72, 0x74, 0x61,
}

var (
	file_ams_rta_proto_rawDescOnce sync.Once
	file_ams_rta_proto_rawDescData = file_ams_rta_proto_rawDesc
)

func file_ams_rta_proto_rawDescGZIP() []byte {
	file_ams_rta_proto_rawDescOnce.Do(func() {
		file_ams_rta_proto_rawDescData = protoimpl.X.CompressGZIP(file_ams_rta_proto_rawDescData)
	})
	return file_ams_rta_proto_rawDescData
}

var file_ams_rta_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_ams_rta_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_ams_rta_proto_goTypes = []interface{}{
	(RtaRequest_OperatingSystem)(0),          // 0: ams_rta.RtaRequest.OperatingSystem
	(RtaRequest_Device_DeviceIdTag)(0),       // 1: ams_rta.RtaRequest.Device.DeviceIdTag
	(RtaRequest_Device_CacheDeviceIdType)(0), // 2: ams_rta.RtaRequest.Device.CacheDeviceIdType
	(RtaRequest_FLModel_ModelType)(0),        // 3: ams_rta.RtaRequest.FLModel.ModelType
	(RtaResponse_FLModelResInfo_Status)(0),   // 4: ams_rta.RtaResponse.FLModelResInfo.Status
	(*RtaRequest)(nil),                       // 5: ams_rta.RtaRequest
	(*DynamicProductInfo)(nil),               // 6: ams_rta.DynamicProductInfo
	(*RtaResponse)(nil),                      // 7: ams_rta.RtaResponse
	(*RtaRequest_Impression)(nil),            // 8: ams_rta.RtaRequest.Impression
	(*RtaRequest_Device)(nil),                // 9: ams_rta.RtaRequest.Device
	(*RtaRequest_Experiment)(nil),            // 10: ams_rta.RtaRequest.Experiment
	(*RtaRequest_FeatureGroup)(nil),          // 11: ams_rta.RtaRequest.FeatureGroup
	(*RtaRequest_FLModel)(nil),               // 12: ams_rta.RtaRequest.FLModel
	(*RtaRequest_MediaInfo)(nil),             // 13: ams_rta.RtaRequest.MediaInfo
	(*RtaRequest_Device_QaidInfo)(nil),       // 14: ams_rta.RtaRequest.Device.QaidInfo
	(*DynamicProductInfo_Product)(nil),       // 15: ams_rta.DynamicProductInfo.Product
	(*RtaResponse_Impression)(nil),           // 16: ams_rta.RtaResponse.Impression
	(*RtaResponse_TargetInfo)(nil),           // 17: ams_rta.RtaResponse.TargetInfo
	(*RtaResponse_FLModelResInfo)(nil),       // 18: ams_rta.RtaResponse.FLModelResInfo
}
var file_ams_rta_proto_depIdxs = []int32{
	8,  // 0: ams_rta.RtaRequest.impressions:type_name -> ams_rta.RtaRequest.Impression
	9,  // 1: ams_rta.RtaRequest.device:type_name -> ams_rta.RtaRequest.Device
	10, // 2: ams_rta.RtaRequest.exps:type_name -> ams_rta.RtaRequest.Experiment
	11, // 3: ams_rta.RtaRequest.all_feature_groups:type_name -> ams_rta.RtaRequest.FeatureGroup
	12, // 4: ams_rta.RtaRequest.fl_models:type_name -> ams_rta.RtaRequest.FLModel
	13, // 5: ams_rta.RtaRequest.media_info:type_name -> ams_rta.RtaRequest.MediaInfo
	15, // 6: ams_rta.DynamicProductInfo.products:type_name -> ams_rta.DynamicProductInfo.Product
	16, // 7: ams_rta.RtaResponse.impressions:type_name -> ams_rta.RtaResponse.Impression
	17, // 8: ams_rta.RtaResponse.target_infos:type_name -> ams_rta.RtaResponse.TargetInfo
	18, // 9: ams_rta.RtaResponse.fl_model_res_infos:type_name -> ams_rta.RtaResponse.FLModelResInfo
	0,  // 10: ams_rta.RtaRequest.Device.os:type_name -> ams_rta.RtaRequest.OperatingSystem
	1,  // 11: ams_rta.RtaRequest.Device.doubtful_ids_list:type_name -> ams_rta.RtaRequest.Device.DeviceIdTag
	2,  // 12: ams_rta.RtaRequest.Device.cached_deviceid_type:type_name -> ams_rta.RtaRequest.Device.CacheDeviceIdType
	14, // 13: ams_rta.RtaRequest.Device.qaid_infos:type_name -> ams_rta.RtaRequest.Device.QaidInfo
	3,  // 14: ams_rta.RtaRequest.FLModel.model_type:type_name -> ams_rta.RtaRequest.FLModel.ModelType
	6,  // 15: ams_rta.RtaResponse.TargetInfo.dynamic_product_infos:type_name -> ams_rta.DynamicProductInfo
	4,  // 16: ams_rta.RtaResponse.FLModelResInfo.status:type_name -> ams_rta.RtaResponse.FLModelResInfo.Status
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_ams_rta_proto_init() }
func file_ams_rta_proto_init() {
	if File_ams_rta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ams_rta_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicProductInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_Experiment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_FeatureGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_FLModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_MediaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaRequest_Device_QaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicProductInfo_Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaResponse_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaResponse_TargetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ams_rta_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaResponse_FLModelResInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ams_rta_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ams_rta_proto_goTypes,
		DependencyIndexes: file_ams_rta_proto_depIdxs,
		EnumInfos:         file_ams_rta_proto_enumTypes,
		MessageInfos:      file_ams_rta_proto_msgTypes,
	}.Build()
	File_ams_rta_proto = out.File
	file_ams_rta_proto_rawDesc = nil
	file_ams_rta_proto_goTypes = nil
	file_ams_rta_proto_depIdxs = nil
}
