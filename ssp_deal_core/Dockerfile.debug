FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250402golang1.23-alinux20250102 as go

WORKDIR /src
COPY ./ /src

ARG GIT_USER
ARG GIT_TOKEN

RUN yum install -y tzdata libpq jemalloc vim supervisor unzip git procps-ng lsof

RUN echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc && \
    source /root/.gvm/scripts/gvm && \
    go env -w GO111MODULE=on && \
    go env -w GOPROXY=https://mirrors.aliyun.com/goproxy && \
    go env -w GOPRIVATE=codeup.aliyun.com && \
    cp config/gdt_public_key.pem ./src/ && \
    cp config/ip2region.db ./src/ && \
    cd /src/src && \
    unzip -d /src/data /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    rm -rf /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    CGO_ENABLED=1 GOOS=linux go build -o main && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# ip2location
RUN mkdir -p /app/
RUN cp -r /src/data /app/

RUN mkdir -p /src/logs/
RUN mkdir -p /src/runtime/nginx/logs
CMD /src/run_debug.sh

# CMD [ "tail", "-f", "/dev/null" ]
