package config

import (
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/svcclusterip"
)

// mysql
const (
	MySQLHost      string = "pc-2zec97555354c4g16.rwlb.rds.aliyuncs.com"
	MySQLDbname    string = "adx"
	MySQLSSPDbname string = "ssp"
	MySQLUserName  string = "maplehaze"
	MySQLPassword  string = "Mhint@123"
	MySQLPort      string = "3306"
)

// redis
const (
	RedisHost string = "r-2zejfdoaq1comnt5pv.redis.rds.aliyuncs.com"
	RedisPort string = "6379"
)

// ClickHouse
const (
	ClickHouseHostPort              string = "tcp://cc-2ze0ogu94yp20oa12-clickhouse.clickhouseserver.rds.aliyuncs.com:9000?username=maplehaze&password=Mhint@123"
	ClickHouseHostPort2             string = "tcp://vw-2zeff0el2o1208927.clickhouse.ads.aliyuncs.com:9000?username=maplehaze&password=Mhint@123&debug=false"
	ClickHouseReqMaxNum             int    = 300000 // 并发请求
	ClickHouseMaterialMaxNum        int    = 3000   // 送审素材
	ClickHouseExpMaxNum             int    = 3000   // 曝光
	ClickHouseClkMaxNum             int    = 300    // 点击
	ClickHouseRtbReqPriceMaxNum     int    = 300000 // RTB请求价格
	ClickHouseDeepLinkMaxNum        int    = 3000   // DP上报链接
	ClickHouseDIDReqMaxNum          int    = 30000  // DID请求
	ClickHouseDIDUpReqMaxNum        int    = 30000  // DID上游请求
	ClickHouseDIDAppListMaxNum      int    = 20000  // AppList
	ClickHouseAuditMaxNum           int    = 3000   // 审核
	ClickHouseReplaceRedisKeyMaxNum int    = 10000  // 替换包key
	ClickHouseClickXYMaxNum         int    = 300    // 点击库
	ClickHouseSensorMaxNum          int    = 300    // sensor
	ClickHouseInterval              int64  = 60
	// ClickHouseMismatchBundleMaxNum  int    = 3000
	// ClickHouseWinPriceNum           int   = 300000
	// ClickHouseTimeOutNum            int   = 90000
)

// holo
const (
	// vpc
	HoloHostPort string = "host=hgpostcn-cn-zxu34m2qh002-cn-beijing-vpc-st.hologres.aliyuncs.com port=80 dbname=ssp_rawdata user=BASIC$maplehaze password=Mhint@123 sslmode=disable"
	// 外网
	// HoloHostPort string = "host=hgpostcn-cn-zxu34m2qh002-cn-beijing.hologres.aliyuncs.com port=80 dbname=ssp_rawdata user=BASIC$maplehaze password=Mhint@123 sslmode=disable"
)

// log path
const (
	IPDBPath            string = "./ip2region.db"
	GDTPublicKeyPemPath string = "./gdt_public_key.pem"
)

// 上游url
const (
	// UpGdtURL       string = "http://mi.gdt.qq.com/api/v3"
	// UpGdtDebugURL  string = "http://test.mi.gdt.qq.com/api/v3"
	UpKuaiShouURL  string = "https://open.e.kuaishou.com/rest/e/v2/open/univ"
	UpInmobiURL    string = "http://union.api.w.inmobi.cn/showad/v3.3"
	UpYinliURL     string = "http://uapi.yinliwlkj.com/v1/tvxqa/ads"
	UpJDDebugURL   string = "https://dsp-test-x.jd.com/adx/maplehaze"
	UpJDURL        string = "https://dsp-x.jd.com/adx/maplehaze"
	UpOPPODebugURL string = "http://uapi-ads-test.wanyol.com/union/ads/v1/api/adReq"
	UpOPPOURL      string = "https://uapi.ads.heytapmobi.com/union/ads/v1/api/adReq"
	UpVIVOURL      string = "https://uapi-ads.vivo.com.cn/u/api/v1/reqAd"
	UpZhiHuURL     string = "https://adx.zhihu.com/get_ads"
	UpHuaWeiURL    string = "https://acd.op.hicloud.com/ppsadx/getResult"
	UpXiaoMiURL    string = "http://api.ad.xiaomi.com/u/api/bidding/v3"
	UpYouDaoURL    string = "http://gorgon.youdao.com/gorgon/request.s"
	UpYiDianURL    string = "http://dsp2.yidianzixun.com/bid?adxType=huanqiu_1037&rtbVersion=3"
	UpBaiDuURL     string = "http://mobads.baidu.com/api_5"
	UpDspURL       string = svcclusterip.DSP_DEAL_CORE
)

// 接口回调
const (
	ExternalExpURL             string = "https://ssp.maplehaze.cn/report/expose"
	ExternalClkURL             string = "https://ssp.maplehaze.cn/report/click"
	ExternalDPURL              string = "https://ssp.maplehaze.cn/report/dp"
	ExternalRtbPriceURL        string = "https://ssp.maplehaze.cn/rtb/price"
	ExternalRtbPriceHttpURL    string = "http://ssp.maplehaze.cn/rtb/price"
	ExternalVideoStartTrackURL string = "https://ssp.maplehaze.cn/report/videostart"
	ExternalVideoEndTrackURL   string = "https://ssp.maplehaze.cn/report/videoend"
	ExternalWinNoticeURL       string = "https://ssp.maplehaze.cn/price/win"
	ExternalLossNoticeURL      string = "https://ssp.maplehaze.cn/price/loss"
)

// 加密key
const (
	EncryptKEY    string = "1234567887654321"
	EncryptNewKEY string = "6ac03ee1bc51bc8c"
)

// ...
const (
	RedisKeyConfigTTL      time.Duration = 30 * time.Second
	CacheServerURL         string        = "http://cache-config-core-svc"
	CacheServerHttpTimeOut time.Duration = 100 * time.Millisecond
	CacheTTL               time.Duration = 24 * time.Hour
)

// ...
const (
	AnticheatConfigAPI string = "anticheat_config_api_%s" // anticheat_config_api_[platform_id]
	AnticheatConfigSDK string = "anticheat_config_sdk_%s" // anticheat_config_sdk_[platform_id]
)

const (
	ADX_SSP_CACHE_DPI_KEY = "dpi_dict_%s" // dpi_dict_[key]
	ADX_SSP_CACHE_UA_KEY  = "ua_dict_%s"  // ua_dict_[key]
)
