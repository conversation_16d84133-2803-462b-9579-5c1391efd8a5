package config

import "github.com/segmentio/kafka-go"

var KafkaWriterConfig = kafka.WriterConfig{
	Brokers: []string{
		"alikafka-serverless-cn-fhh3zns0s04-1000-vpc.alikafka.aliyuncs.com:9092",
		"alikafka-serverless-cn-fhh3zns0s04-2000-vpc.alikafka.aliyuncs.com:9092",
		"alikafka-serverless-cn-fhh3zns0s04-3000-vpc.alikafka.aliyuncs.com:9092",
	},
	Balancer: &kafka.Hash{},
}

var KafkaReaderConfig = kafka.ReaderConfig{
	Brokers: []string{
		"alikafka-serverless-cn-fhh3zns0s04-1000-vpc.alikafka.aliyuncs.com:9092",
		"alikafka-serverless-cn-fhh3zns0s04-2000-vpc.alikafka.aliyuncs.com:9092",
		"alikafka-serverless-cn-fhh3zns0s04-3000-vpc.alikafka.aliyuncs.com:9092",
	},
}
