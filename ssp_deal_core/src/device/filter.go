package device

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"mh_proxy/db"
	"regexp"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
)

func IsTrustedAppId(c context.Context, appId string) bool {
	isTrusted := false

	var trustedAppIds []string

	cacheKey := "ssp_trusted_app_ids"
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisCacheErr == nil {
			json.Unmarshal([]byte(redisCacheValue), &trustedAppIds)
		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &trustedAppIds)
	}

	for _, id := range trustedAppIds {
		if id == appId {
			return true
		}
	}

	return isTrusted
}

func IsTrustedManufacturer(c context.Context, manufacturer string, os string) bool {
	isTrusted := false
	var trustedManufacturers []string

	cacheKey := "ssp_trusted_manufacturers"
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisCacheErr == nil {
			json.Unmarshal([]byte(redisCacheValue), &trustedManufacturers)
		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &trustedManufacturers)
	}

	for _, trustedManufacturer := range trustedManufacturers {
		// 忽略大小写比较
		if len(manufacturer) > 0 && strings.EqualFold(fmt.Sprintf("%s|%s", manufacturer, os), trustedManufacturer) {
			return true
		}
	}

	return isTrusted
}

func IsBannedModel(c context.Context, model string) bool {
	isBanned := false

	var modelBlacklistRules []string

	cacheKey := "ssp_model_blacklist_rules"
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisCacheErr == nil {
			json.Unmarshal([]byte(redisCacheValue), &modelBlacklistRules)
		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &modelBlacklistRules)
	}

	for _, modelBlacklistRule := range modelBlacklistRules {
		matched, _ := regexp.MatchString(modelBlacklistRule, model)
		if matched {
			return true
		}
	}

	return isBanned
}

func IsNeedReplaceModel(c context.Context, model string) bool {
	isBanned := false

	var modelBlacklistRules []string

	cacheKey := "ssp_model_replacelist_rules"
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisCacheErr == nil {
			json.Unmarshal([]byte(redisCacheValue), &modelBlacklistRules)
		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))

	} else {
		json.Unmarshal(cacheValue, &modelBlacklistRules)
	}

	for _, modelBlacklistRule := range modelBlacklistRules {
		matched, _ := regexp.MatchString(modelBlacklistRule, model)
		if matched {
			return true
		}
	}

	return isBanned
}

func IsDauDemandBlacklist(c context.Context, pAppId string) bool {

	var blackListIds []string

	cacheKey := rediskeys.ADX_DEVICE_STATISTICS_DAU_DEMAND_BLACKLIST_KEY
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()

		if redisCacheErr != nil {
			log.Println("IsDauDemandBlacklist error:", redisCacheErr)

			return false
		} else {
			json.Unmarshal([]byte(redisCacheValue), &blackListIds)
			log.Println("IsDauDemandBlacklist from redis:", blackListIds)

		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &blackListIds)
		// log.Println("IsDauDemandBlacklist from bigcache:", blackListIds)
	}

	for _, id := range blackListIds {
		if id == pAppId {
			return true
		}
	}

	return false
}

func IsDauSupplyBlacklist(c context.Context, pAppId string) bool {

	var blackListIds []string

	cacheKey := rediskeys.ADX_DEVICE_STATISTICS_DAU_SUPPLY_BLACKLIST_KEY
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()

		if redisCacheErr != nil {
			log.Println("IsDauSupplyBlacklist error:", redisCacheErr)

			return false
		} else {
			json.Unmarshal([]byte(redisCacheValue), &blackListIds)
			log.Println("IsDauSupplyBlacklist from redis:", blackListIds)

		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &blackListIds)
		// log.Println("IsDauSupplyBlacklist from bigcache:", blackListIds)
	}

	for _, id := range blackListIds {
		if id == pAppId {
			return true
		}
	}

	return false
}

func IsDauDemandWhitelist(c context.Context, pAppId string) bool {

	var whiteListIds []string

	cacheKey := rediskeys.ADX_DEVICE_STATISTICS_DAU_DEMAND_WHITELIST_KEY
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()

		if redisCacheErr != nil {
			log.Println("IsDauDemandWhitelist error:", redisCacheErr)

			return false
		} else {
			json.Unmarshal([]byte(redisCacheValue), &whiteListIds)
			log.Println("IsDauDemandWhitelist from redis:", whiteListIds)

		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &whiteListIds)
		// log.Println("IsDauDemandWhitelist from bigcache:", whiteListIds)
	}

	for _, id := range whiteListIds {
		if id == pAppId {
			return true
		}
	}

	return false
}

func IsDauSupplyWhitelist(c context.Context, pAppId string) bool {

	var whiteListIds []string

	cacheKey := rediskeys.ADX_DEVICE_STATISTICS_DAU_SUPPLY_WHITELIST_KEY
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()

		if redisCacheErr != nil {
			log.Println("IsDauSupplyWhitelist error:", redisCacheErr)

			return false
		} else {
			json.Unmarshal([]byte(redisCacheValue), &whiteListIds)
			log.Println("IsDauSupplyWhitelist from redis:", whiteListIds)

		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &whiteListIds)
		// log.Println("IsDauSupplyWhitelist from bigcache:", whiteListIds)
	}

	for _, id := range whiteListIds {
		if id == pAppId {
			return true
		}
	}

	return false
}

// ip/did 的请求、间隔、曝光、点击统计分别使用各自的白名单进行管理
// 白名单算法由 @王春生 提供
// 2024-12-23

// 是否在did的请求白名单
func IsDidReqWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDReq == 1 {
		return true
	}

	return false
}

// 是否在did请求间隔的白名单
func IsDidReqLastTimeWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDReqInterval == 1 {
		return true
	}

	return false
}

// 是否在did的曝光白名单
func IsDidExpWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDExp == 1 {
		return true
	}

	return false
}

// 是否在did曝光间隔的白名单
func IsDidExpLastTimeWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDExpInterval == 1 {
		return true
	}

	return false
}

// 是否在did的点击白名单
func IsDidClkWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDClk == 1 {
		return true
	}

	return false
}

// 是否在did点击间隔的白名单
func IsDidClkLastTimeWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitDIDClkInterval == 1 {
		return true
	}

	return false
}

// 是否在ip的请求白名单
func IsIpReqWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitIPReq == 1 {
		return true
	}

	return false
}

// 是否在ip的曝光白名单
func IsIpExpWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitIPExp == 1 {
		return true
	}

	return false
}

// 是否在ip的点击白名单
func IsIpClkWhitelist(c context.Context, pAppId string) bool {
	platformAppInfo := getPlatformAppInfo(pAppId)
	if platformAppInfo == nil {
		return false
	}

	if platformAppInfo.PlatformAppIsLimitIPClk == 1 {
		return true
	}

	return false
}

// getPlatformAppInfo ...
func getPlatformAppInfo(platformAppID string) *PlatformPosStu {

	cacheKey := "go_platform_app_" + platformAppID

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var platformPos PlatformPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &platformPos)

		return &platformPos
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}
