package exp

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandDidLastTimeExpDataArray []DidDemandExpData
var batchSaveDidDemandDidLastTimeExpDataMutex sync.Mutex
var batchSaveDidDemandDidLastTimeExpDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidLastTimeExp(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsDidExpLastTimeWhitelist(c, pAppId) {
		return
	}

	expData := DidDemandExpData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandDidLastTimeExpDataMutex.Lock()

	batchSaveDidDemandDidLastTimeExpDataArray = append(batchSaveDidDemandDidLastTimeExpDataArray, expData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandDidLastTimeExpDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandDidLastTimeExpDataTime < 60 {
		batchSaveDidDemandDidLastTimeExpDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandDidLastTimeExpDataArray[0:]

	batchSaveDidDemandDidLastTimeExpDataArray = batchSaveDidDemandDidLastTimeExpDataArray[0:0]
	batchSaveDidDemandDidLastTimeExpDataMutex.Unlock()
	batchSaveDidDemandDidLastTimeExpDataTime = utils.GetCurrentSecond()

	err := ExpDidLastTimeStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidLastTimeExp error:", err)
	}

}

func ExpDidLastTimeStatistics(
	c context.Context,
	list []DidDemandExpData,
) (err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ExpDidLastTimeStatistics, error:", err)
		}
	}()

	if utilities.SkipHologress {
		return nil
	}

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {
		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}

	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId := range pAppCounter {

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			// ! 给春生提供的记录DID最后一次曝光时间
			lastTimeFieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_EXP_LAST_TIME_FIELDKEY, pAppId, dd)
			_, err = db.GlbRedis.Do(c, "EXHSET", cacheKey, lastTimeFieldKey, utils.GetCurrentMilliSecond(), "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}

		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()
	}

	return
}
