package exp

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandExpDataArray []DidDemandExpData
var batchSaveDidDemandExpDataMutex sync.Mutex
var batchSaveDidDemandExpDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidExp(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsDidExpWhitelist(c, pAppId) {
		return
	}

	expData := DidDemandExpData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandExpDataMutex.Lock()

	batchSaveDidDemandExpDataArray = append(batchSaveDidDemandExpDataArray, expData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandExpDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandExpDataTime < 60 {
		batchSaveDidDemandExpDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandExpDataArray[0:]

	batchSaveDidDemandExpDataArray = batchSaveDidDemandExpDataArray[0:0]
	batchSaveDidDemandExpDataMutex.Unlock()
	batchSaveDidDemandExpDataTime = utils.GetCurrentSecond()

	err := ExpDidStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidExp error:", err)
	}

}

func ExpDidStatistics(
	c context.Context,
	list []DidDemandExpData,
) (err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ExpDidStatistics, error:", err)
		}
	}()

	if utilities.SkipHologress {
		return nil
	}

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {
		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}

	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId, number := range pAppCounter {
			fieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_EXP_NUMBER_FIELDKEY, pAppId, dd)

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			_, err := db.GlbRedis.Do(c, "EXHINCRBY", cacheKey, fieldKey, number, "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}

		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()
	}

	return
}
