package clk

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandClkDataArray []DidDemandClkData
var batchSaveDidDemandClkDataMutex sync.Mutex
var batchSaveDidDemandClkDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidClk(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {
	if !device.IsDidClkWhitelist(c, pAppId) {
		return
	}

	clkData := DidDemandClkData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandClkDataMutex.Lock()

	batchSaveDidDemandClkDataArray = append(batchSaveDidDemandClkDataArray, clkData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandClkDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandClkDataTime < 60 {
		batchSaveDidDemandClkDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandClkDataArray[0:]

	batchSaveDidDemandClkDataArray = batchSaveDidDemandClkDataArray[0:0]
	batchSaveDidDemandClkDataMutex.Unlock()
	batchSaveDidDemandClkDataTime = utils.GetCurrentSecond()

	err := ClkDidStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidClk error:", err)
	}

}

func ClkDidStatistics(
	c context.Context,
	list []DidDemandClkData,
) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ClkDidStatistics, error:", err)
		}
	}()

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {
		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}
	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId, number := range pAppCounter {
			fieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_CLK_NUMBER_FIELDKEY, pAppId, dd)

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			_, err := db.GlbRedis.Do(c, "EXHINCRBY", cacheKey, fieldKey, number, "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}

		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()

	}

	return
}
