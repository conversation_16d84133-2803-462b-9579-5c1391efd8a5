package clk

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandDidLastTimeClkDataArray []DidDemandClkData
var batchSaveDidDemandDidLastTimeDataMutex sync.Mutex
var batchSaveDidDemandDidLastTimeDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidLastTimeClk(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsDidClkLastTimeWhitelist(c, pAppId) {
		return
	}

	clkData := DidDemandClkData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandDidLastTimeDataMutex.Lock()

	batchSaveDidDemandDidLastTimeClkDataArray = append(batchSaveDidDemandDidLastTimeClkDataArray, clkData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandDidLastTimeClkDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandDidLastTimeDataTime < 60 {
		batchSaveDidDemandDidLastTimeDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandDidLastTimeClkDataArray[0:]

	batchSaveDidDemandDidLastTimeClkDataArray = batchSaveDidDemandDidLastTimeClkDataArray[0:0]
	batchSaveDidDemandDidLastTimeDataMutex.Unlock()
	batchSaveDidDemandDidLastTimeDataTime = utils.GetCurrentSecond()

	err := ClkDidLastTimeStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidLastTimeClk error:", err)
	}

}

func ClkDidLastTimeStatistics(
	c context.Context,
	list []DidDemandClkData,
) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ClkDidLastTimeStatistics, error:", err)
		}
	}()

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {
		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}
	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId := range pAppCounter {

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			// ! 给春生提供的记录DID最后一次点击时间
			lastTimeFieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_CLK_LAST_TIME_FIELDKEY, pAppId, dd)
			_, err = db.GlbRedis.Do(c, "EXHSET", cacheKey, lastTimeFieldKey, utils.GetCurrentMilliSecond(), "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}
		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()

	}

	return
}
