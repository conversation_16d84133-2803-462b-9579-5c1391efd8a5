package clk

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandIpClkDataArray []DidDemandClkData
var batchSaveDidDemandIpClkDataMutex sync.Mutex
var batchSaveDidDemandIpClkDataTime int64 = utils.GetCurrentSecond()

func DidDemandIpClk(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsIpClkWhitelist(c, pAppId) {
		return
	}

	clkData := DidDemandClkData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandIpClkDataMutex.Lock()

	batchSaveDidDemandIpClkDataArray = append(batchSaveDidDemandIpClkDataArray, clkData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandIpClkDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandIpClkDataTime < 60 {
		batchSaveDidDemandIpClkDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandIpClkDataArray[0:]

	batchSaveDidDemandIpClkDataArray = batchSaveDidDemandIpClkDataArray[0:0]
	batchSaveDidDemandIpClkDataMutex.Unlock()
	batchSaveDidDemandIpClkDataTime = utils.GetCurrentSecond()

	err := ClkIpStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandIpClk error:", err)
	}

}

func ClkIpStatistics(
	c context.Context,
	list []DidDemandClkData,
) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ClkIpStatistics, error:", err)
		}
	}()

	dd := time.Now().Format("2006-01-02")

	// ip_pappid 的计数
	ipPAppCounter := map[string]map[string]int{}
	for _, item := range list {
		if _, ok := ipPAppCounter[item.Ip]; ok {
			if number, ok2 := ipPAppCounter[item.Ip][item.PlatformAppID]; ok2 {
				ipPAppCounter[item.Ip][item.PlatformAppID] = number + 1
			} else {
				ipPAppCounter[item.Ip][item.PlatformAppID] = 1
			}
		} else {
			ipPAppCounter[item.Ip] = map[string]int{}
			ipPAppCounter[item.Ip][item.PlatformAppID] = 1
		}
	}

	schemaIp := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_ip_demand")

	for ip, pAppCounter := range ipPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_PREFIX, ip)
		for pAppId, number := range pAppCounter {
			fieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_CLK_NUMBER_FIELDKEY, pAppId, dd)

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			numberCache, err := db.GlbRedis.Do(c, "EXHINCRBY", cacheKey, fieldKey, number, "EX", int32(randTTL.Seconds())).Result()

			if err != nil {
				continue
			}

			item := holoclient.NewMutationRequest(schemaIp)
			item.SetValWithTextByColName("ip", ip, len(ip))
			item.SetValWithTextByColName("p_app_id", pAppId, len(pAppId))
			item.SetValWithTextByColName("dd", dd, len(dd))
			if numberInt, ok := numberCache.(int64); ok {
				item.SetInt32ValByColName("clk", int32(numberInt))
			}

			db.GlbHologresAdxSSPUpdateDataDb.Submit(item)
		}

		randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

		db.GlbRedis.Expire(c, cacheKey, randTTLKey).Result()
	}

	return
}
