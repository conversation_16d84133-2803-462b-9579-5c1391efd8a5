package device

import (
	"math/rand"
	"mh_proxy/utilities"
	"time"
)

func DauReqHoloRatioSwitch() bool {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	dauReqSaveHoloRatio := utilities.DauSaveHoloRatio
	if dauReqSaveHoloRatio == 0 {
		return false
	}

	if r.Int63n(dauReqSaveHoloRatio) != 0 {
		return false
	}

	return true
}

func MauReqHoloRatioSwitch() bool {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	mauReqSaveHoloRatio := utilities.MauSaveHoloRatio
	if mauReqSaveHoloRatio == 0 {
		return false
	}

	if r.Int63n(mauReqSaveHoloRatio) != 0 {
		return false
	}

	return true
}

func DidReqHoloRatioSwitch() bool {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	didReqSaveHoloRatio := utilities.DidSaveHoloRatio
	if didReqSaveHoloRatio == 0 {
		return false
	}

	if r.Int63n(didReqSaveHoloRatio) != 0 {
		return false
	}

	return true
}

func DauReqKafkaRatioSwitch() bool {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	dauSaveKafkaRatio := utilities.DauSaveKafkaRatio
	if dauSaveKafkaRatio == 0 {
		return false
	}

	if r.Int63n(dauSaveKafkaRatio) != 0 {
		return false
	}

	return true
}

func DeviceInfoSaveRatioSwitch() bool {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	dauSaveKafkaRatio := utilities.DeviceInfoSaveRatio
	if dauSaveKafkaRatio == 0 {
		return false
	}

	if r.Int63n(dauSaveKafkaRatio) != 0 {
		return false
	}

	return true
}
