package dau

import (
	"log"
	"mh_proxy/utilities"
	"testing"
	"time"
)

func TestSaveDidTrustedManufacturerAndModelToRedisKeys(t *testing.T) {
	keys := map[string]int{}

	keys["123"] = 1
	keys["abc"] = 1

	for key, _ := range keys {
		log.Println(key)
	}
}

func TestDauReqKafkaRedisFilterTTLMinutes(t *testing.T) {
	log.Println(time.Minute, time.Minute*time.Duration(utilities.DauReqKafkaRedisFilterTTLMinutes))
}
