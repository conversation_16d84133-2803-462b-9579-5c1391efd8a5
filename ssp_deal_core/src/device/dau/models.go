package dau

import "mh_proxy/device"

type DauReqRawData struct {
	DidMd5         string                  `json:"did_md5" db:"did_md5"`
	AppId          string                  `json:"app_id" db:"app_id"`
	Model          string                  `json:"model" db:"model"`
	Manufacturer   string                  `json:"manufacturer" db:"manufacturer"`
	Os             string                  `json:"os" db:"os"`
	SdkVersion     string                  `json:"sdk_version"`
	AppType        int                     `json:"app_type,omitempty"`
	Date           string                  `json:"dd" db:"dd"`
	Osv            string                  `json:"osv"`
	Ua             string                  `json:"ua,omitempty"`
	Imei           string                  `json:"imei,omitempty"`
	ImeiMd5        string                  `json:"imei_md5,omitempty"`
	AndroidID      string                  `json:"android_id,omitempty"`
	AndroidIDMd5   string                  `json:"android_id_md5,omitempty"`
	Oaid           string                  `json:"oaid,omitempty"`
	OaidMd5        string                  `json:"oaid_md5,omitempty"`
	Idfa           string                  `json:"idfa,omitempty"`
	IdfaMd5        string                  `json:"idfa_md5,omitempty"`
	LBSIPCountry   string                  `json:"lbs_ip_country,omitempty"`
	LBSIPProvince  string                  `json:"lbs_ip_province,omitempty"`
	LBSIPCity      string                  `json:"lbs_ip_city,omitempty"`
	LBSGPSCountry  string                  `json:"lbs_gps_country,omitempty"`
	LBSGPSProvince string                  `json:"lbs_gps_province,omitempty"`
	LBSGPSCity     string                  `json:"lbs_gps_city,omitempty"`
	CAID           string                  `json:"caid,omitempty"`
	CAIDVersion    string                  `json:"caid_version,omitempty"`
	IP             string                  `json:"ip,omitempty"`
	ConnectType    int                     `json:"connect_type"`
	Carrier        int                     `json:"carrier"`
	ScreenWidth    int                     `json:"screen_width,omitempty"`
	ScreenHeight   int                     `json:"screen_height,omitempty"`
	AppList        []int                   `json:"applist,omitempty"`
	DPI            float32                 `json:"dpi,omitempty"`
	CAIDMulti      []device.MHReqCAIDMulti `json:"caid_multi,omitempty"`
}

type DauUpstreamReqRawData struct {
	DidMd5       string `json:"did_md5" db:"did_md5"`
	PAppId       string `json:"p_app_id" db:"p_app_id"`
	PMediaId     string `json:"p_media_id" db:"p_media_id"`
	PCorpId      string `json:"p_corp_id" db:"p_corp_id"`
	Model        string `json:"model" db:"model"`
	Manufacturer string `json:"manufacturer" db:"manufacturer"`
	Os           string `json:"os" db:"os"`
	Date         string `json:"dd" db:"dd"`
	Osv          string `json:"osv"`
	ConnectType  int    `json:"connect_type"`
	Carrier      int    `json:"carrier"`
}

type DidDataObject struct {
	Did          string `json:"did"`
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
	Osv          string `json:"osv"`
}
type DidModelAndManufacturer struct {
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
}
