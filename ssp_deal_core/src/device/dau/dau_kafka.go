package dau

import (
	"context"
	"encoding/json"
	"log"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"strconv"
	"sync"
	"time"

	commonKafka "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/kafka"
	"github.com/segmentio/kafka-go"
)

var dauSupplyKafkaProducer = kafka.Writer{
	Addr:     kafka.TCP(config.KafkaWriterConfig.Brokers...),
	Balancer: &kafka.Hash{},
	Topic:    commonKafka.TOPIC_SSP_DEVICE_DAU_SUPPLY,
	Async:    true, // make the writer asynchronous
}

var dauDemandKafkaProducer = kafka.Writer{
	Addr:     kafka.TCP(config.KafkaWriterConfig.Brokers...),
	Balancer: &kafka.Hash{},
	Topic:    commonKafka.TOPIC_SSP_DEVICE_DAU_DEMAND,
	Async:    true, // make the writer asynchronous
}

var batchSaveDauSupplyReqDataKafkaArray []DauReqRawData
var batchSaveDauSupplyReqDataKafkaMutex sync.Mutex
var batchSaveDauSupplyReqDataKafkaTime = utils.GetCurrentSecond()

var batchSaveDauDemandReqDataKafkaArray []DauUpstreamReqRawData
var batchSaveDauDemandReqDataKafkaMutex sync.Mutex
var batchSaveDauDemandReqDataKafkaTime = utils.GetCurrentSecond()

func dauSupplyKafkaSend(
	ctx context.Context,
	key []byte,
	content []byte,
) (err error) {
	return dauSupplyKafkaProducer.WriteMessages(ctx, kafka.Message{
		Key:   key,
		Value: content,
	})
}

func dauDemandKafkaSend(
	ctx context.Context,
	key []byte,
	content []byte,
) (err error) {
	return dauDemandKafkaProducer.WriteMessages(ctx, kafka.Message{
		Key:   key,
		Value: content,
	})
}

func DauSupplyReqKafka(
	c context.Context,
	didMd5 string,
	didMd5Type int,
	mhReq *device.MHReq,
	localPos *device.LocalPosStu,
	sdkVersion string,
) {

	if !device.DauReqKafkaRatioSwitch() {
		return
	}
	if utilities.DauUsingSupplyBlacklist && device.IsDauSupplyBlacklist(c, localPos.LocalAppID) {
		return
	}

	timestamp := time.Now()
	dd := timestamp.Format("2006-01-02")

	appTypeInt, err := strconv.Atoi(localPos.LocalAppType)
	if err != nil {
		appTypeInt = 0
	}

	var reqData DauReqRawData
	reqData.DidMd5 = didMd5
	reqData.AppId = localPos.LocalAppID
	reqData.Model = mhReq.Device.Model
	reqData.Manufacturer = mhReq.Device.Manufacturer
	reqData.Os = mhReq.Device.Os
	reqData.SdkVersion = sdkVersion
	reqData.AppType = appTypeInt
	reqData.Date = dd
	reqData.Osv = mhReq.Device.OsVersion
	reqData.ConnectType = mhReq.Network.ConnectType
	reqData.Carrier = mhReq.Network.Carrier

	// ua dpi
	if len(mhReq.Device.Manufacturer) > 0 && len(mhReq.Device.Model) > 0 && mhReq.Device.DPI > 0.5 {
		dictModelProbability := rand.Intn(utilities.DeviceDictModelProbability)
		if dictModelProbability == 0 {
			reqData.DPI = mhReq.Device.DPI
			reqData.Ua = mhReq.Device.Ua
		}
	}

	// applist
	if len(mhReq.Device.AppList) > 0 {
		randNum := rand.Intn(100)
		if randNum <= utilities.DeviceAppListProbability {
			reqData.Imei = mhReq.Device.Imei
			reqData.ImeiMd5 = mhReq.Device.ImeiMd5
			reqData.AndroidID = mhReq.Device.AndroidID
			reqData.AndroidIDMd5 = mhReq.Device.AndroidIDMd5
			reqData.Oaid = mhReq.Device.Oaid
			reqData.OaidMd5 = mhReq.Device.OaidMd5
			reqData.CAID = mhReq.Device.CAID
			reqData.CAIDVersion = mhReq.Device.CAIDVersion
			reqData.IP = mhReq.Device.IP
			reqData.LBSIPCountry = mhReq.Device.LBSIPCountry
			reqData.LBSIPProvince = mhReq.Device.LBSIPProvince
			reqData.LBSIPCity = mhReq.Device.LBSIPCity
			reqData.Idfa = mhReq.Device.Idfa
			reqData.ScreenWidth = mhReq.Device.ScreenWidth
			reqData.ScreenHeight = mhReq.Device.ScreenHeight
			reqData.IdfaMd5 = mhReq.Device.IdfaMd5
			reqData.CAIDMulti = mhReq.Device.CAIDMulti
			reqData.AppList = mhReq.Device.AppList
		}
	}

	batchSaveDauSupplyReqKafka(c, reqData)

}

func batchSaveDauSupplyReqKafka(c context.Context, reqData DauReqRawData) {
	batchSaveDauSupplyReqDataKafkaMutex.Lock()

	batchSaveDauSupplyReqDataKafkaArray = append(batchSaveDauSupplyReqDataKafkaArray, reqData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDauSupplyReqDataKafkaArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDauSupplyReqDataKafkaTime < 60 {
		batchSaveDauSupplyReqDataKafkaMutex.Unlock()
		return
	}

	newDataList := batchSaveDauSupplyReqDataKafkaArray[0:]

	batchSaveDauSupplyReqDataKafkaArray = batchSaveDauSupplyReqDataKafkaArray[0:0]
	batchSaveDauSupplyReqDataKafkaMutex.Unlock()

	batchSaveDauSupplyReqDataKafkaTime = utils.GetCurrentSecond()

	// 写DAU原始数据
	err := sendDauSupplyDataToKafka(c, newDataList)
	if err != nil {
		log.Println("[DAU]sendDauSupplyDataToKafka Error:", err)
	}

}

func sendDauSupplyDataToKafka(
	ctx context.Context,
	dataList []DauReqRawData,
) (
	err error,
) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DAU]sendDauSupplyDataToKafka error:", err)
		}
	}()

	for _, reqData := range dataList {
		if dataString, err := json.Marshal(reqData); err == nil {
			dauSupplyKafkaSend(
				ctx,
				[]byte(reqData.DidMd5),
				dataString,
			)
		}
	}

	return
}

func DauDemandReqKafka(
	c context.Context,
	mhReq *device.MHReq,
	localPos *device.LocalPosStu,
	platformPos *device.PlatformPosStu,
	didMd5Key string,
) {

	if !device.DauReqKafkaRatioSwitch() {
		return
	}
	if utilities.DauUsingDemandBlacklist && device.IsDauDemandBlacklist(c, platformPos.PlatformAppID) {
		return
	}

	timestamp := time.Now()
	dd := timestamp.Format("2006-01-02")

	var reqData DauUpstreamReqRawData
	reqData.DidMd5 = didMd5Key
	reqData.PAppId = platformPos.PlatformAppID
	reqData.PMediaId = platformPos.PlatformMediaID
	reqData.PCorpId = strconv.Itoa(platformPos.PlatformAppCorpID)
	reqData.Model = mhReq.Device.Model
	reqData.Manufacturer = mhReq.Device.Manufacturer
	reqData.Os = mhReq.Device.Os
	reqData.Date = dd
	reqData.Osv = mhReq.Device.OsVersion
	reqData.ConnectType = mhReq.Network.ConnectType
	reqData.Carrier = mhReq.Network.Carrier

	batchSaveDauDemandReqKafka(c, reqData)

}

func batchSaveDauDemandReqKafka(c context.Context, reqData DauUpstreamReqRawData) {
	batchSaveDauDemandReqDataKafkaMutex.Lock()

	batchSaveDauDemandReqDataKafkaArray = append(batchSaveDauDemandReqDataKafkaArray, reqData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDauSupplyReqDataKafkaArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDauDemandReqDataKafkaTime < 60 {
		batchSaveDauDemandReqDataKafkaMutex.Unlock()
		return
	}

	newDataList := batchSaveDauDemandReqDataKafkaArray[0:]

	batchSaveDauDemandReqDataKafkaArray = batchSaveDauDemandReqDataKafkaArray[0:0]
	batchSaveDauDemandReqDataKafkaMutex.Unlock()

	batchSaveDauDemandReqDataKafkaTime = utils.GetCurrentSecond()

	// 写DAU原始数据
	err := sendDauDemandDataToKafka(c, newDataList)
	if err != nil {
		log.Println("[DAU]sendDauSupplyDataToKafka Error:", err)
	}

}

func sendDauDemandDataToKafka(
	ctx context.Context,
	dataList []DauUpstreamReqRawData,
) (
	err error,
) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DAU]sendDauDemandDataToKafka error:", err)
		}
	}()

	for _, reqData := range dataList {
		if dataString, err := json.Marshal(reqData); err == nil {
			dauDemandKafkaSend(
				ctx,
				[]byte(reqData.DidMd5),
				dataString,
			)
		}
	}

	return
}
