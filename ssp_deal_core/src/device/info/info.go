package info

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"strings"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

var batchSaveDeviceInfoReqDataArray []DeviceInfoReqRawData
var batchSaveDeviceInfoReqDataMutex sync.Mutex
var batchSaveDeviceInfoReqDataTime int64 = utils.GetCurrentSecond()

func DeviceInfoReq(
	c context.Context,
	didMd5 string,
	didMd5Type int,
	mhReq *device.MHReq,
	localPos *device.LocalPosStu,
	sdkVersion string,
) {

	if !device.DeviceInfoSaveRatioSwitch() {
		return
	}

	timestamp := time.Now()
	dd := timestamp.Format("2006-01-02")

	var reqData DeviceInfoReqRawData
	reqData.DidMd5 = didMd5
	reqData.AppId = localPos.LocalAppID
	reqData.Model = mhReq.Device.Model
	reqData.Manufacturer = mhReq.Device.Manufacturer
	reqData.Os = mhReq.Device.Os
	reqData.SdkVersion = sdkVersion
	reqData.Date = dd
	reqData.Osv = mhReq.Device.OsVersion
	reqData.ConnectType = mhReq.Network.ConnectType
	reqData.Carrier = mhReq.Network.Carrier

	//log.Println("[[DAULOG]]", "localPos.LocalAppID:[", localPos.LocalAppID, "] mhReq.Network.ConnectType:[", mhReq.Network.ConnectType, "]")
	batchSaveDeviceInfoReq(c, reqData)

}

func batchSaveDeviceInfoReq(c context.Context, reqData DeviceInfoReqRawData) {
	batchSaveDeviceInfoReqDataMutex.Lock()

	batchSaveDeviceInfoReqDataArray = append(batchSaveDeviceInfoReqDataArray, reqData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDeviceInfoReqDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDeviceInfoReqDataTime < 60 {
		batchSaveDeviceInfoReqDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDeviceInfoReqDataArray[0:]

	batchSaveDeviceInfoReqDataArray = batchSaveDeviceInfoReqDataArray[0:0]
	batchSaveDeviceInfoReqDataMutex.Unlock()

	batchSaveDeviceInfoReqDataTime = utils.GetCurrentSecond()

	// 写ssp_did_data_with_didmd5
	// 过滤: 可信媒体,黑名单
	err := saveDeviceInfoDataToRedis(c, newDataList)
	if err != nil {
		log.Println("[DID]saveDeviceInfoDataToRedis Error:", err)
	}

	// 厂商和机型统计
	// 过滤: 可信媒体,黑名单
	err = saveDidTrustedDailyManufacturerToHolo(c, newDataList)
	if err != nil {
		log.Println("[DID]saveDidTrustedDailyManufacturer Error:", err)
	}

	// 写adx_ssp_device_did_manufacturer_with_modelmd5
	// 过滤: 可信媒体,可信厂商,黑名单
	err = saveDidTrustedDailyManufacturerToRedis(c, newDataList)
	if err != nil {
		log.Println("[DID]saveDidTrustedDailyManufacturerToRedis Error:", err)
	}

	// 写adx_ssp_device_did_trusted_manufacturer_and_model_%s_%s
	// 过滤: 可信媒体,可信厂商,黑名单
	err = saveDidTrustedManufacturerAndModelToHoloAndRedis(c, newDataList)
	if err != nil {
		log.Println("[DID]saveDidTrustedManufacturerAndModelToRedis Error:", err)
	}

	err = saveDeviceInfoDebug(c, newDataList)
	if err != nil {
		log.Println("[DID]saveDeviceInfoDebug Error:", err)

	}

}

// 过滤: 可信媒体,黑名单
func saveDeviceInfoDataToRedis(c context.Context, dataList []DeviceInfoReqRawData) (err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DAU]saveDeviceInfoDataToRedis error:", err)
		}
	}()

	for _, item := range dataList {

		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_DATA_WITH_DIDMD5_PREFIX, item.DidMd5)

		bigCacheKey := fmt.Sprintf("%s:%s", "bc_", cacheKey)

		_, cacheErr := db.GlbBigCacheMinute.Get(bigCacheKey)
		if cacheErr != nil {
			db.GlbBigCacheMinute.Set(bigCacheKey, []byte("1"))
		} else {
			return
		}

		// 2023年08月11日 加回可信厂商
		if !device.IsTrustedAppId(c, item.AppId) || !device.IsTrustedManufacturer(c, item.Manufacturer, item.Os) || device.IsBannedModel(c, item.Model) {
			continue
		}

		fixedModel := strings.ToLower(strings.Replace(item.Model, " ", "", -1))

		data := DeviceInfoDataObject{
			Did:          item.DidMd5,
			Model:        fixedModel,
			RawModel:     item.Model,
			Manufacturer: item.Manufacturer,
			Osv:          item.Osv,
		}
		tmpJSON, _ := json.Marshal(data)
		err = db.GlbRedis.Set(c, cacheKey, string(tmpJSON), 3*24*60*60*time.Second).Err()

	}

	return
}

// 过滤: 可信媒体,黑名单
func saveDidTrustedDailyManufacturerToHolo(c context.Context, dataList []DeviceInfoReqRawData) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID]saveDidTrustedDailyManufacturerToHolo error:", err)
		}
	}()

	schema := db.NewHologresAdxSSPDataTableSchema("device", "did_manufacturer_daily_trusted")

	for _, reqData := range dataList {

		if !device.IsTrustedAppId(c, reqData.AppId) || device.IsBannedModel(c, reqData.Model) {
			continue
		}

		item := holoclient.NewMutationRequest(schema)

		manufacturerLower := strings.ToLower(reqData.Manufacturer)
		modelLower := strings.ToLower(reqData.Model)

		key := fmt.Sprintf("%s:%s:%s", reqData.Date, manufacturerLower, modelLower)

		md5 := utils.GetMd5(key)

		item.SetValWithTextByColName("md5", md5, len(md5))
		item.SetValWithTextByColName("dd", reqData.Date, len(reqData.Date))
		item.SetValWithTextByColName("manufacturer", manufacturerLower, len(manufacturerLower))
		item.SetValWithTextByColName("model", modelLower, len(modelLower))

		db.GlbHologresAdxSSPDataDb.Submit(item)

	}

	return
}

// 过滤: 可信媒体,可信厂商,黑名单
func saveDidTrustedDailyManufacturerToRedis(c context.Context, dataList []DeviceInfoReqRawData) (err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("[DAU]saveDidTrustedDailyManufacturerToRedis error:", err)
		}
	}()

	for _, item := range dataList {

		fixedManufacturer := strings.ToLower(strings.Replace(item.Manufacturer, " ", "", -1))
		fixedModel := strings.ToLower(strings.Replace(item.Model, " ", "", -1))
		if len(fixedModel) == 0 || len(fixedManufacturer) == 0 {
			continue
		}

		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_MANUFACTURER_WITH_MODELMD5_PREFIX, utils.GetMd5(fixedModel))

		bigCacheKey := fmt.Sprintf("%s:%s", "bc_", cacheKey)

		_, cacheErr := db.GlbBigCacheMinute.Get(bigCacheKey)
		if cacheErr != nil {
			db.GlbBigCacheMinute.Set(bigCacheKey, []byte("1"))
		} else {
			return
		}

		if !device.IsTrustedAppId(c, item.AppId) || !device.IsTrustedManufacturer(c, item.Manufacturer, item.Os) || device.IsBannedModel(c, item.Model) {
			continue
		}

		data := DeviceInfoModelAndManufacturer{
			Model:        item.Model,
			RawModel:     item.Model,
			Manufacturer: item.Manufacturer,
		}
		tmpJSON, _ := json.Marshal(data)
		err = db.GlbRedis.Set(c, cacheKey, string(tmpJSON), 3*24*60*60*time.Second).Err()

	}
	return
}

// 过滤: 可信媒体,可信厂商,黑名单
func saveDidTrustedManufacturerAndModelToHoloAndRedis(c context.Context, dataList []DeviceInfoReqRawData) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DAU]saveDidTrustedManufacturerAndModelToRedis error:", err)
		}
	}()

	schema := db.NewHologresAdxSSPDataTableSchema("device", "did_manufacturer_and_model_trusted")

	for _, item := range dataList {

		if !device.IsTrustedAppId(c, item.AppId) || !device.IsTrustedManufacturer(c, item.Manufacturer, item.Os) || device.IsBannedModel(c, item.Model) {
			continue
		}

		// 写redis
		fixedManufacturer := strings.ToLower(strings.Replace(item.Manufacturer, " ", "", -1))
		fixedModel := strings.ToLower(strings.Replace(item.Model, " ", "", -1))
		if len(fixedModel) == 0 || len(fixedManufacturer) == 0 {
			continue
		}

		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_TRUSTED_MANUFACTURER_AND_MODEL_PREFIX, fixedManufacturer, fixedModel)

		bigCacheKey := fmt.Sprintf("%s:%s", "bc_", cacheKey)
		_, cacheErr := db.GlbBigCacheMinute.Get(bigCacheKey)
		if cacheErr != nil {
			db.GlbBigCacheMinute.Set(bigCacheKey, []byte("1"))
		} else {
			return
		}

		err = db.GlbRedis.Set(c, cacheKey, 1, 3*24*60*60*time.Second).Err()

		// 写holo
		holoItem := holoclient.NewMutationRequest(schema)
		holoItem.SetValWithTextByColName("manufacturer", fixedManufacturer, len(fixedManufacturer))
		holoItem.SetValWithTextByColName("model", fixedModel, len(fixedModel))
		db.GlbHologresAdxSSPDataDb.Submit(holoItem)

	}

	return
}

func saveDeviceInfoDebug(
	ctx context.Context,
	dataList []DeviceInfoReqRawData,
) (
	err error,
) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID]saveDeviceInfoDebug error:", err)
		}
	}()

	schema := db.NewHologresAdxSSPDataTableSchema("device", "device_info_debug")

	for _, reqData := range dataList {

		if !device.IsTrustedAppId(ctx, reqData.AppId) ||
			!device.IsTrustedManufacturer(ctx, reqData.Manufacturer, reqData.Os) ||
			device.IsBannedModel(ctx, reqData.Model) {
			continue
		}

		item := holoclient.NewMutationRequest(schema)

		manufacturerLower := strings.ToLower(reqData.Manufacturer)
		modelLower := strings.ToLower(reqData.Model)

		item.SetValWithTextByColName("did_md5", reqData.DidMd5, len(reqData.DidMd5))
		item.SetValWithTextByColName("manufacturer", manufacturerLower, len(manufacturerLower))
		item.SetValWithTextByColName("model", modelLower, len(modelLower))
		item.SetValWithTextByColName("dd", reqData.Date, len(reqData.Date))
		item.SetValWithTextByColName("app_id", reqData.AppId, len(reqData.AppId))
		item.SetValWithTextByColName("os", reqData.Os, len(reqData.Os))

		content := ""
		if contentData, err := json.Marshal(reqData); err == nil {
			content = string(contentData)
		}
		item.SetValWithTextByColName("content", content, len(content))

		db.GlbHologresAdxSSPDataDb.Submit(item)

	}

	return
}
