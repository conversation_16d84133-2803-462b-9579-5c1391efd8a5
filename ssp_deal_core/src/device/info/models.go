package info

type DeviceInfoReqRawData struct {
	DidMd5       string `json:"did_md5" db:"did_md5"`
	AppId        string `json:"app_id" db:"app_id"`
	Model        string `json:"model" db:"model"`
	Manufacturer string `json:"manufacturer" db:"manufacturer"`
	Os           string `json:"os" db:"os"`
	SdkVersion   string `json:"sdk_version"`
	Date         string `json:"dd" db:"dd"`
	Osv          string `json:"osv"`
	ConnectType  int    `json:"connect_type"`
	Carrier      int    `json:"carrier"`
}

type DeviceInfoDataObject struct {
	Did          string `json:"did"`
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
	Osv          string `json:"osv"`
}
type DeviceInfoModelAndManufacturer struct {
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
}
