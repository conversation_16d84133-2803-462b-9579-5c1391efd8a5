package device

import (
	"encoding/json"
)

func ModelsMHReqToDeviceReq(reqData []byte) (result *MHReq) {
	json.Unmarshal(reqData, &result)
	return
}

func ModelsLocalPosStuToDeviceLocalPosStu(reqData []byte) (result *LocalPosStu) {
	json.Unmarshal(reqData, &result)
	return
}

func ModelsPlatformPosStuToDevicePlatformPosStu(reqData []byte) (result *PlatformPosStu) {
	json.Unmarshal(reqData, &result)
	return
}

// MHReqApp ...
type MHReqApp struct {
	AppID       string `json:"app_id"`
	AppBundleID string `json:"app_bundle_id"`
	AppName     string `json:"app_bundle_name"`
	AppType     int    `json:"app_ssp_type"`
}

// MHReqPos ...
type MHReqPos struct {
	ID      int `json:"id"`
	Width   int `json:"width"`
	Height  int `json:"height"`
	AdCount int `json:"ad_count"`
	// RTB tagid
	TagID string `json:"tagid"`
}

// MHReqDevice ...
type MHReqDevice struct {
	Os            string  `json:"os"`
	OsVersion     string  `json:"os_version"`
	Model         string  `json:"model"`
	Manufacturer  string  `json:"manufacturer"`
	DeviceType    int     `json:"device_type"`
	ScreenWidth   int     `json:"screen_width"`
	ScreenHeight  int     `json:"screen_height"`
	ScreenDensity float32 `json:"screen_density"`
	DPI           float32 `json:"dpi"`
	Orientation   int     `json:"orientation"`
	Imei          string  `json:"imei"`
	ImeiMd5       string  `json:"imei_md5"`
	AndroidID     string  `json:"android_id"`
	AndroidIDMd5  string  `json:"android_id_md5"`
	Oaid          string  `json:"oaid"`
	OaidMd5       string  `json:"oaid_md5,omitempty"`
	Idfa          string  `json:"idfa"`
	IdfaMd5       string  `json:"idfa_md5"`
	Ua            string  `json:"ua"`
	IP            string  `json:"ip"`
	IPCountry     string  `json:"ip_country"`
	IPProvince    string  `json:"ip_province"`
	IPCity        string  `json:"ip_city"`
	Mac           string  `json:"mac"`
	// appstore version, hms version
	AppStoreVersion string `json:"appstore_version"`
	HMSCoreVersion  string `json:"hms_version"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec"`
	Country            string `json:"country"`
	Language           string `json:"language"`
	DeviceNameMd5      string `json:"device_name_md5"`
	HardwareMachine    string `json:"hardware_machine"` // iPhone10,3
	HardwareModel      string `json:"hardware_model"`   // D22AP
	PhysicalMemoryByte string `json:"physical_memory_byte"`
	HarddiskSizeByte   string `json:"harddisk_size_byte"`
	SystemUpdateSec    string `json:"system_update_sec"`
	TimeZone           string `json:"time_zone"`
	CPUNum             int    `json:"cpu_num"`
	CAID               string `json:"caid"`
	CAIDVersion        string `json:"caid_version"`
	// boot_mark update_mark
	BootMark   string `json:"boot_mark"`
	UpdateMark string `json:"update_mark"`
	// miui version
	MiuiVersion string `json:"miui_version"`
	OAIDSource  int    `json:"oaid_source"`
	SDFreeSpace string `json:"sd_free_space"`

	LBSIPCountry  string `json:"lbs_ip_country,omitempty"`
	LBSIPProvince string `json:"lbs_ip_province,omitempty"`
	LBSIPCity     string `json:"lbs_ip_city,omitempty"`

	AppList []int `json:"applist,omitempty"`

	CAIDMulti []MHReqCAIDMulti `json:"caid_multi,omitempty"`
}

type MHReqCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}

// MHReqNetwork ...
type MHReqNetwork struct {
	ConnectType int `json:"connect_type"`
	Carrier     int `json:"carrier"`
}

// MHReqGeo ...
type MHReqGeo struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// MHReq ...
type MHReq struct {
	App     MHReqApp     `json:"media"`
	Pos     MHReqPos     `json:"pos"`
	Device  MHReqDevice  `json:"device"`
	Network MHReqNetwork `json:"network"`
	Geo     MHReqGeo     `json:"geo"`
}

type LocalAppStu struct {
	LocalAppID           string `json:"app_id,omitempty"`
	LocalAppIsGetOaid    int    `json:"is_get_oaid"`
	LocalAppIsGetGPS     int    `json:"is_get_gps"`
	LocalAppIsGetAppList int    `json:"is_get_applist"`
}

// LocalPosStu ...
type LocalPosStu struct {
	LocalPosID                       string
	LocalAppID                       string
	LocalPosWidth                    int
	LocalPosHeight                   int
	LocalPosPinDuoDuoStyleIDs        string `json:"LocalPosPinDuoDuoStyleIDs,omitempty"` // localpos拼多多样式id, 优先使用下游拼多多样式id
	LocalPosType                     int
	LocalOs                          string
	LocalAppName                     string
	LocalAppBundleID                 string
	LocalAppType                     string // 1. SDK; 2. API; 3. SDK+API; 4. RTB; 5. ADX; 6. 商店
	LocalAppSupportAntiCheatH5       int
	LocalAppSplashClickRegion        int `json:"LocalAppSplashClickRegion,omitempty"`        // 支持开屏点击区域
	LocalAppNativeDownloadCompliance int `json:"LocalAppNativeDownloadCompliance,omitempty"` // 支持原生下载合规
	// LocalAppRewardCompliance         int `json:"LocalAppRewardCompliance,omitempty"`         // 支持激励视频合规
	// LocalAppRewardSkipTime           int `json:"LocalAppRewardSkipTime,omitempty"`           // 激励视频跳过时间
	LocalPosIsActive                 int
	LocalPosMHLandPage               int    // 是否用maplehaze落地页
	LocalPosTimeOut                  int    // 超时时间
	LocalPosIsConcurrent             int    // 是否并发: 0 不并发, 1 并发
	LocalPosMaterialType             int    // 素材类型: 0 图片, 1 视频, 2图片+视频
	LocalAppSupportExtraClipboard    int    // 是否支持剪贴板
	LocalAppSupportExtraNotification int    // 是否支持通知
	LocalAppSupportExtraWakeUp       int    // 是否支持唤醒
	LocalAppIsFilterDownloadBudget   int    // 是否过滤下载预算(优量汇,穿山甲,快手,PDD,祝融,知盟)
	LocalAppIsDirect                 int    // 是否支持定向包
	LocalAppDirectTTL                string // 定向包ttl
	LocalAppDirectTTLUnit            string // 定向包ttl单位
	LocalAppIsReplaceDID             int    // 是否支持替换包
	LocalAppReplaceTTL               string // 替换包ttl
	LocalAppReplaceTTLUnit           string // 替换包ttl单位
	LocalAppReplaceParams            string `json:"LocalAppReplaceParams,omitempty"`    // 替换包参数
	LocalAppIsLimitFrequency         int    `json:"LocalAppIsLimitFrequency,omitempty"` // 是否限频
	LocalAppLimitFrequencyHour0      int    `json:"LocalAppLimitFrequencyHour0,omitempty"`
	LocalAppLimitFrequencyHour1      int    `json:"LocalAppLimitFrequencyHour1,omitempty"`
	LocalAppLimitFrequencyHour2      int    `json:"LocalAppLimitFrequencyHour2,omitempty"`
	LocalAppLimitFrequencyHour3      int    `json:"LocalAppLimitFrequencyHour3,omitempty"`
	LocalAppLimitFrequencyHour4      int    `json:"LocalAppLimitFrequencyHour4,omitempty"`
	LocalAppLimitFrequencyHour5      int    `json:"LocalAppLimitFrequencyHour5,omitempty"`
	LocalAppLimitFrequencyHour6      int    `json:"LocalAppLimitFrequencyHour6,omitempty"`
	LocalAppLimitFrequencyHour7      int    `json:"LocalAppLimitFrequencyHour7,omitempty"`
	LocalAppLimitFrequencyHour8      int    `json:"LocalAppLimitFrequencyHour8,omitempty"`
	LocalAppLimitFrequencyHour9      int    `json:"LocalAppLimitFrequencyHour9,omitempty"`
	LocalAppLimitFrequencyHour10     int    `json:"LocalAppLimitFrequencyHour10,omitempty"`
	LocalAppLimitFrequencyHour11     int    `json:"LocalAppLimitFrequencyHour11,omitempty"`
	LocalAppLimitFrequencyHour12     int    `json:"LocalAppLimitFrequencyHour12,omitempty"`
	LocalAppLimitFrequencyHour13     int    `json:"LocalAppLimitFrequencyHour13,omitempty"`
	LocalAppLimitFrequencyHour14     int    `json:"LocalAppLimitFrequencyHour14,omitempty"`
	LocalAppLimitFrequencyHour15     int    `json:"LocalAppLimitFrequencyHour15,omitempty"`
	LocalAppLimitFrequencyHour16     int    `json:"LocalAppLimitFrequencyHour16,omitempty"`
	LocalAppLimitFrequencyHour17     int    `json:"LocalAppLimitFrequencyHour17,omitempty"`
	LocalAppLimitFrequencyHour18     int    `json:"LocalAppLimitFrequencyHour18,omitempty"`
	LocalAppLimitFrequencyHour19     int    `json:"LocalAppLimitFrequencyHour19,omitempty"`
	LocalAppLimitFrequencyHour20     int    `json:"LocalAppLimitFrequencyHour20,omitempty"`
	LocalAppLimitFrequencyHour21     int    `json:"LocalAppLimitFrequencyHour21,omitempty"`
	LocalAppLimitFrequencyHour22     int    `json:"LocalAppLimitFrequencyHour22,omitempty"`
	LocalAppLimitFrequencyHour23     int    `json:"LocalAppLimitFrequencyHour23,omitempty"`
	LocalAppIsExtraLimitFrequency    int    `json:"LocalAppIsExtraLimitFrequency,omitempty"`
	LocalAppExtraLimitFrequency      string `json:"LocalAppExtraLimitFrequency,omitempty"`
	LocalAppIsReportPriceLoss        int    `json:"LocalAppIsReportPriceLoss,omitempty"`
	LocalAppIsNeedAudit              int    `json:"LocalAppIsNeedAudit,omitempty"`
	LocalPosIsNeedAudit              int    `json:"LocalPosIsNeedAudit,omitempty"`
	LocalAppIsVerifyPackage          int    `json:"LocalAppIsVerifyPackage,omitempty"`
	LocalAppIsLogicPixel             int    `json:"LocalAppIsLogicPixel,omitempty"`
	LocalAppLogicPixelList           string `json:"LocalAppLogicPixelList,omitempty"`
	// LocalAppIsClickLimit             int    `json:"LocalAppIsClickLimit,omitempty"`   // 点击上报去重
	// LocalAppClickLimitList           string `json:"LocalAppClickLimitList,omitempty"` // 点击上报去重广告源
	// 素材过滤
	LocalPosIsMaterialFilterOrientation   int    `json:"LocalPosIsMaterialFilterOrientation,omitempty"`   // 是否素材过滤方向, 0 不过滤, 1 仅横向, 2 仅竖向
	LocalPosIsMaterialFilterDuration      int    `json:"LocalPosIsMaterialFilterDuration,omitempty"`      // 是否素材过滤时长
	LocalPosMaterialFilterMinDuration     string `json:"LocalPosMaterialFilterMinDuration,omitempty"`     // 素材过滤最小时长
	LocalPosMaterialFilterMaxDuration     string `json:"LocalPosMaterialFilterMaxDuration,omitempty"`     // 素材过滤最大时长
	LocalPosIsMaterialFilterFixedDuration int    `json:"LocalPosIsMaterialFilterFixedDuration,omitempty"` // 是否素材过滤固定时长
	LocalPosMaterialFilterFixedDurations  string `json:"LocalPosMaterialFilterFixedDurations,omitempty"`  // 素材过滤固定时长值
	LocalPosMaterialFilterSizes           string `json:"LocalPosMaterialFilterSizes,omitempty"`           // 素材过滤固定素材尺寸
	// local app shield rule
	LocalAppShieldRuleID      string `json:"LocalAppShieldRuleID,omitempty"`
	LocalAppShieldKey         string `json:"LocalAppShieldKey,omitempty"`
	LocalAppShieldPackageName string `json:"LocalAppShieldPackageName,omitempty"`
	LocalAppShieldAdURL       string `json:"LocalAppShieldAdURL,omitempty"`
	LocalAppShieldMaterialURL string `json:"LocalAppShieldMaterialURL,omitempty"`
	LocalAppShieldRuleType    int    `json:"LocalAppShieldRuleType,omitempty"`
	LocalAppShieldTimeList    string `json:"LocalAppShieldTimeList,omitempty"`
	LocalAppShieldRegionList  string `json:"LocalAppShieldRegionList,omitempty"`
	// local pos shield rule
	LocalPosShieldRuleID      string `json:"LocalPosShieldRuleID,omitempty"`
	LocalPosShieldKey         string `json:"LocalPosShieldKey,omitempty"`
	LocalPosShieldPackageName string `json:"LocalPosShieldPackageName,omitempty"`
	LocalPosShieldAdURL       string `json:"LocalPosShieldAdURL,omitempty"`
	LocalPosShieldMaterialURL string `json:"LocalPosShieldMaterialURL,omitempty"`
	LocalPosShieldRuleType    int    `json:"LocalPosShieldRuleType,omitempty"`
	LocalPosShieldTimeList    string `json:"LocalPosShieldTimeList,omitempty"`
	LocalPosShieldRegionList  string `json:"LocalPosShieldRegionList,omitempty"`
	// local app shield white rule
	LocalAppShieldWhiteRuleID      string `json:"LocalAppShieldWhiteRuleID,omitempty"`
	LocalAppShieldWhiteKey         string `json:"LocalAppShieldWhiteKey,omitempty"`
	LocalAppShieldWhitePackageName string `json:"LocalAppShieldWhitePackageName,omitempty"`
	LocalAppShieldWhiteAdURL       string `json:"LocalAppShieldWhiteAdURL,omitempty"`
	LocalAppShieldWhiteMaterialURL string `json:"LocalAppShieldWhiteMaterialURL,omitempty"`
	LocalAppShieldWhiteRuleType    int    `json:"LocalAppShieldWhiteRuleType,omitempty"`
	LocalAppShieldWhiteTimeList    string `json:"LocalAppShieldWhiteTimeList,omitempty"`
	LocalAppShieldWhiteRegionList  string `json:"LocalAppShieldWhiteRegionList,omitempty"`
	// local pos shield white rule
	LocalPosShieldWhiteRuleID      string `json:"LocalPosShieldWhiteRuleID,omitempty"`
	LocalPosShieldWhiteKey         string `json:"LocalPosShieldWhiteKey,omitempty"`
	LocalPosShieldWhitePackageName string `json:"LocalPosShieldWhitePackageName,omitempty"`
	LocalPosShieldWhiteAdURL       string `json:"LocalPosShieldWhiteAdURL,omitempty"`
	LocalPosShieldWhiteMaterialURL string `json:"LocalPosShieldWhiteMaterialURL,omitempty"`
	LocalPosShieldWhiteRuleType    int    `json:"LocalPosShieldWhiteRuleType,omitempty"`
	LocalPosShieldWhiteTimeList    string `json:"LocalPosShieldWhiteTimeList,omitempty"`
	LocalPosShieldWhiteRegionList  string `json:"LocalPosShieldWhiteRegionList,omitempty"`
	// local pos 云控选项
	LocalPosInteractionConfigID     string `json:"LocalPosInteractionConfigID,omitempty"`
	LocalPosInteractionTypeListJson string `json:"LocalPosInteractionTypeListJson,omitempty"`
	LocalPosYaoSpeed                string `json:"LocalPosYaoSpeed,omitempty"`
	LocalPosYaoTriggerTime          string `json:"LocalPosYaoTriggerTime,omitempty"`
	LocalPosIsSaHuaConfig           int    `json:"LocalPosIsSaHuaConfig,omitempty"`
	LocalPosSahuaConfigListJson     string `json:"LocalPosSahuaConfigListJson,omitempty"`
	LocalPosSahuaImageTriggerTime   string `json:"LocalPosSahuaImageTriggerTime,omitempty"`
	LocalPosSahuaVideoTriggerTime   string `json:"LocalPosSahuaVideoTriggerTime,omitempty"`
	LocalPosSahuaImageDurationTime  string `json:"LocalPosSahuaImageDurationTime,omitempty"`
	LocalPosSahuaVideoDurationTime  string `json:"LocalPosSahuaVideoDurationTime,omitempty"`
	LocalPosIsDownloadDialog        int    `json:"LocalPosIsDownloadDialog,omitempty"`
	LocalPosIsAutoPlayMobileNetwork int    `json:"LocalPosIsAutoPlayMobileNetwork,omitempty"`
	// local app是否存储为替换包
	LocalAppIsDIDStorage int `json:"LocalAppIsDIDStorage,omitempty"`
}

type PlatformPosStu struct {
	PlatformPosID                   string
	PlatformAppID                   string
	PlatformAppCorpID               int
	PlatformMediaID                 string
	PlatformAppType                 string `json:"PlatformAppType,omitempty"` // 0 api; 1 sdk
	PlatformAppBundle               string `json:"PlatformAppBundle,omitempty"`
	PlatformPosStyleIDs             string `json:"PlatformPosStyleIDs,omitempty"` // pos styleids
	PlatformOs                      string // 0 -> Android; 1 -> iOS; 2 -> 通用
	PlatformPosWidth                int
	PlatformPosHeight               int
	PlatformPosType                 int    // 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	PlatformPosMaterialType         int    // 0 -> 图片; 1 -> 视频; 2 -> 图片+视频; 3 -> 模版
	PlatformPosIsDebug              int    `json:"PlatformPosIsDebug,omitempty"`
	PlatformPosIsReplaceXY          int    `json:"PlatformPosIsReplaceXY,omitempty"`          // 是否替换xy
	PlatformPosReplaceXYType        int    `json:"PlatformPosReplaceXYType,omitempty"`        // 替换xy类型, 0为替换坐标库, 1为替换-999
	PlatformPosIsReplaceXYLibOffset int    `json:"PlatformPosIsReplaceXYLibOffset,omitempty"` // 替换xy 新库 key offset
	PlatformPosDirection            int    `json:"PlatformPosDirection,omitempty"`            // 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
	PlatformAppName                 string `json:"PlatformAppName,omitempty"`
	PlatformAppVersion              string `json:"PlatformAppVersion,omitempty"`
	PlatformAppKey                  string `json:"PlatformAppKey,omitempty"`
	PlatformAppSecret               string `json:"PlatformAppSecret,omitempty"`
	PlatformAppUpURL                string `json:"PlatformAppUpURL,omitempty"`
	PlatformAppUpDebugURL           string `json:"PlatformAppUpDebugURL,omitempty"`
	PlatformAppFilterUa             int    `json:"PlatformAppFilterUa,omitempty"`
	PlatformAppFilterIllegalUa      int    `json:"PlatformAppFilterIllegalUa,omitempty"`
	PlatformAppIsActive             int    `json:"PlatformAppIsActive,omitempty"`
	PlatformPosIsActive             int    `json:"PlatformPosIsActive,omitempty"`
	// 过滤model
	PlatformPosModelType   int    `json:"PlatformPosModelType,omitempty"`   // 单选: 0 全部; 1 OPPO; 2 VIVO; 3 HUAWEI; 4 小米
	PlatformPosModelFilter string `json:"PlatformPosModelFilter,omitempty"` // 多选: 1 OPPO; 2 VIVO; 3 HUAWEI; 4 小米
	// 价格是否加密
	PlatformAppIsPriceEncrypt int    `json:"PlatformAppIsPriceEncrypt,omitempty"`
	PlatformAppPriceEncrypt   string `json:"platform_app_price_encrypt,omitempty"`
	PlatformAppPriceEncrypt2  string `json:"platform_app_price_encrypt_2,omitempty"`
	// 成交价倍数
	PlatformAppFinalPriceTimes float32 `json:"PlatformAppFinalPriceTimes,omitempty"`
	// 是否替换did
	PlatformAppIsReplaceDID   int `json:"PlatformAppIsReplaceDID,omitempty"`
	PlatformAppIsReplaceDIDUa int `json:"PlatformAppIsReplaceDIDUa,omitempty"`
	// 替换did md5_key, 5 ratio
	PlatformAppReplaceDIDMd5Key       string `json:"PlatformAppReplaceDIDMd5Key,omitempty"`
	PlatformAppReplaceNoRatio         int    `json:"PlatformAppReplaceNoRatio,omitempty"`
	PlatformAppReplaceIPRatio         int    `json:"PlatformAppReplaceIPRatio,omitempty"`
	PlatformAppReplaceIPModelRatio    int    `json:"PlatformAppReplaceIPModelRatio,omitempty"`
	PlatformAppReplaceIPOsvRatio      int    `json:"PlatformAppReplaceIPOsvRatio,omitempty"`
	PlatformAppReplaceIPModelOsvRatio int    `json:"PlatformAppReplaceIPModelOsvRatio,omitempty"`
	PlatformAppReplaceWhiteMedias     string `json:"PlatformAppReplaceWhiteMedias,omitempty"`
	// 前提是替换包白名单媒体, 如果是替换包白名单媒体并且走到上游配置的广告位, 就忽略替换包白名单媒体功能
	PlatformAppReplaceWhiteMediasPlatformPos string `json:"PlatformAppReplaceWhiteMediasPlatformPos,omitempty"`
	// magic 替换包库md5key
	PlatformAppMagicReplaceDIDMd5Key string `json:"PlatformAppMagicReplaceDIDMd5Key,omitempty"`
	// dau
	PlatformAppMaxDAUType   int `json:"PlatformAppMaxDAUType,omitempty"` // 1 无限制, 2 限量数(单位万)
	PlatformAppMaxDAU       int `json:"PlatformAppMaxDAU,omitempty"`
	PlatformAppMaxDAURandom int `json:"PlatformAppMaxDAURandom,omitempty"`
	// direcet media
	PlatformAppIsDirectMedia int    `json:"PlatformAppIsDirectMedia,omitempty"`
	PlatformAppDirectMedias  string `json:"PlatformAppDirectMedias,omitempty"`
	// 是否dp上报失败
	PlatformAppIsDeepLinkFailed int `json:"PlatformAppIsDeepLinkFailed,omitempty"`
	// 是否普通广告 0 ,还是附加广告 1
	PlatformPosProperty int `json:"PlatformPosProperty,omitempty"`
	// 只要osv>=10
	PlatformPosIsOnlyMore10 int `json:"PlatformPosIsOnlyMore10,omitempty"`
	// 只要hms_core
	PlatformPosIsHmsCore int `json:"PlatformPosIsHmsCore,omitempty"`
	// pos key, 360
	PlatformPosKey string `json:"PlatformPosKey,omitempty"`
	// 拼多多channel
	PlatformAppPinDuoDuoChannel string `json:"platform_app_pdd_channel,omitempty"`
	// 是否上报ua
	PlatformAppIsReportUa int `json:"PlatformAppIsReportUa,omitempty"`
	// iOS上报主参数
	PlatformAppIOSReportMainParam string `json:"PlatformAppIOSReportMainParam,omitempty"`
	// caid附属参数
	PlatformAppIOSReportSubParam string `json:"PlatformAppIOSReportSubParam,omitempty"`
	// 是否上报android_id
	PlatformAppIsReportAndroidID int `json:"PlatformAppIsReportAndroidID,omitempty"`
	// 是否上报sld, 目前只是gdt
	PlatformPosIsReportSLD int `json:"PlatformPosIsReportSLD,omitempty"`
	// sld类型, 目前只是gdt
	PlatformPosSLDType int `json:"PlatformPosSLDType,omitempty"`
	// 竞胜转竞败上报
	PlatformPosIsPriceWinToLost int `json:"PlatformPosIsPriceWinToLost,omitempty"`
	// 竞胜转竞败上报比例值
	PlatformPosPriceWinToLostWeight int `json:"PlatformPosPriceWinToLostWeight,omitempty"`
	// idfa明文转MD5
	PlatformAppIsReportIDFAMd5 int `json:"PlatformAppIsReportIDFAMd5,omitempty"`
	// 是否支持厂商应用商店
	PlatformAppIsSupportAppStore int `json:"PlatformAppIsSupportAppStore,omitempty"`
	// 是否请求快应用广告
	PlatformAppIsSupportQuickApp int `json:"PlatformAppIsSupportQuickApp,omitempty"`
	// 是否屏蔽拼多多广告
	PlatformAppIsStopPinDuoDuo int `json:"PlatformAppIsStopPinDuoDuo,omitempty"`
	// 是否屏蔽快手广告
	PlatformAppIsStopKuaiShou int `json:"PlatformAppIsStopKuaiShou,omitempty"`
	// 是否屏蔽jd广告
	PlatformAppIsStopJingDong int `json:"PlatformAppIsStopJingDong,omitempty"`
	// 是否jd广告百分比
	PlatformAppStopJingDongWeight int `json:"PlatformAppStopJingDongWeight,omitempty"`
	// 是否单设备频控限制
	PlatformAppIsDIDFrequencyControl int `json:"PlatformAppIsDIDFrequencyControl,omitempty"`
	// 单设备频控限制时间(毫秒)
	// PlatformAppDIDFrequencyControlTime string `json:"PlatformAppDIDFrequencyControlTime,omitempty"`
	// 单设备频控时段控制(毫秒)
	PlatformAppLimitFrequencyHour0  int `json:"PlatformAppLimitFrequencyHour0,omitempty"`
	PlatformAppLimitFrequencyHour1  int `json:"PlatformAppLimitFrequencyHour1,omitempty"`
	PlatformAppLimitFrequencyHour2  int `json:"PlatformAppLimitFrequencyHour2,omitempty"`
	PlatformAppLimitFrequencyHour3  int `json:"PlatformAppLimitFrequencyHour3,omitempty"`
	PlatformAppLimitFrequencyHour4  int `json:"PlatformAppLimitFrequencyHour4,omitempty"`
	PlatformAppLimitFrequencyHour5  int `json:"PlatformAppLimitFrequencyHour5,omitempty"`
	PlatformAppLimitFrequencyHour6  int `json:"PlatformAppLimitFrequencyHour6,omitempty"`
	PlatformAppLimitFrequencyHour7  int `json:"PlatformAppLimitFrequencyHour7,omitempty"`
	PlatformAppLimitFrequencyHour8  int `json:"PlatformAppLimitFrequencyHour8,omitempty"`
	PlatformAppLimitFrequencyHour9  int `json:"PlatformAppLimitFrequencyHour9,omitempty"`
	PlatformAppLimitFrequencyHour10 int `json:"PlatformAppLimitFrequencyHour10,omitempty"`
	PlatformAppLimitFrequencyHour11 int `json:"PlatformAppLimitFrequencyHour11,omitempty"`
	PlatformAppLimitFrequencyHour12 int `json:"PlatformAppLimitFrequencyHour12,omitempty"`
	PlatformAppLimitFrequencyHour13 int `json:"PlatformAppLimitFrequencyHour13,omitempty"`
	PlatformAppLimitFrequencyHour14 int `json:"PlatformAppLimitFrequencyHour14,omitempty"`
	PlatformAppLimitFrequencyHour15 int `json:"PlatformAppLimitFrequencyHour15,omitempty"`
	PlatformAppLimitFrequencyHour16 int `json:"PlatformAppLimitFrequencyHour16,omitempty"`
	PlatformAppLimitFrequencyHour17 int `json:"PlatformAppLimitFrequencyHour17,omitempty"`
	PlatformAppLimitFrequencyHour18 int `json:"PlatformAppLimitFrequencyHour18,omitempty"`
	PlatformAppLimitFrequencyHour19 int `json:"PlatformAppLimitFrequencyHour19,omitempty"`
	PlatformAppLimitFrequencyHour20 int `json:"PlatformAppLimitFrequencyHour20,omitempty"`
	PlatformAppLimitFrequencyHour21 int `json:"PlatformAppLimitFrequencyHour21,omitempty"`
	PlatformAppLimitFrequencyHour22 int `json:"PlatformAppLimitFrequencyHour22,omitempty"`
	PlatformAppLimitFrequencyHour23 int `json:"PlatformAppLimitFrequencyHour23,omitempty"`
	// 上游原生广告位请求个数
	PlatformPosNativeReqNum int `json:"PlatformPosNativeReqNum,omitempty"`
	// csj配置
	PlatformAppSHA1        string `json:"PlatformAppSHA1,omitempty"`
	PlatformAppItunesID    string `json:"PlatformAppItunesID,omitempty"`
	PlatformAppAdxName     string `json:"PlatformAppAdxName,omitempty"`
	PlatformAppAdxPassword string `json:"PlatformAppAdxPassword,omitempty"`
	// gdt api字段support_pkg, query配置
	PlatformPosIsGdtDownloadPosType int    `json:"PlatformPosIsGdtDownloadPosType,omitempty"` // 0 关, 1 包名, 2 关键词
	PlatformPosGdtPackageNames      string `json:"PlatformPosGdtPackageNames,omitempty"`
	PlatformPosGdtSearchKeys        string `json:"PlatformPosGdtSearchKeys,omitempty"`

	// 是否ip请求限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPReq int `json:"PlatformAppIsLimitIPReq,omitempty"`
	// 是否ip曝光限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPExp int `json:"PlatformAppIsLimitIPExp,omitempty"`
	// 是否ip点击限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPClk int `json:"PlatformAppIsLimitIPClk,omitempty"`
	// 是否did请求限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDReq int `json:"PlatformAppIsLimitDIDReq,omitempty"`
	// 是否did曝光限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDExp int `json:"PlatformAppIsLimitDIDExp,omitempty"`
	// 是否did点击限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDClk int `json:"PlatformAppIsLimitDIDClk,omitempty"`
	// 是否did req间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDReqInterval int `json:"PlatformAppIsLimitDIDReqInterval,omitempty"`
	// 是否did exp间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDExpInterval int `json:"PlatformAppIsLimitDIDExpInterval,omitempty"`
	// 是否did clk间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDClkInterval int `json:"PlatformAppIsLimitDIDClkInterval,omitempty"`
}

// PlatformPolicyStu ...
type PlatformPolicyStu struct {
	ID                        string
	PlatformMediaID           string
	PlatformIsActive          int
	PlatformMaxCostType1      int
	PlatformMaxCostType2      int
	PlatformMaxCostType3      int
	PlatformMaxCostType4      int
	PlatformMaxCostType5      int
	PlatformMaxCostType6      int
	PlatformMaxCostType0      int
	PlatformMaxCost1          int
	PlatformMaxCost2          int
	PlatformMaxCost3          int
	PlatformMaxCost4          int
	PlatformMaxCost5          int
	PlatformMaxCost6          int
	PlatformMaxCost0          int
	PlatformMaxReqType1       int
	PlatformMaxReqType2       int
	PlatformMaxReqType3       int
	PlatformMaxReqType4       int
	PlatformMaxReqType5       int
	PlatformMaxReqType6       int
	PlatformMaxReqType0       int
	PlatformMaxReq1           int64
	PlatformMaxReq2           int64
	PlatformMaxReq3           int64
	PlatformMaxReq4           int64
	PlatformMaxReq5           int64
	PlatformMaxReq6           int64
	PlatformMaxReq0           int64
	PlatformReqRateAndroid1   int
	PlatformReqRateAndroid2   int
	PlatformReqRateAndroid3   int
	PlatformReqRateAndroid4   int
	PlatformReqRateAndroid5   int
	PlatformReqRateAndroid6   int
	PlatformReqRateAndroid0   int
	PlatformReqRateIos1       int
	PlatformReqRateIos2       int
	PlatformReqRateIos3       int
	PlatformReqRateIos4       int
	PlatformReqRateIos5       int
	PlatformReqRateIos6       int
	PlatformReqRateIos0       int
	PlatformBigDataAndroidReq int64
	PlatformBigDataIosReq     int64
	PlatformBigDataCost       int // 单位: 元
}
