package req

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandDidLastTimeReqDataArray []DidDemandReqData
var batchSaveDidDemandDidLastTimeDataMutex sync.Mutex
var batchSaveDidDemandDidLastTimeDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidLastTimeReq(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsDidReqLastTimeWhitelist(c, pAppId) {
		return
	}

	reqData := DidDemandReqData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandDidLastTimeDataMutex.Lock()

	batchSaveDidDemandDidLastTimeReqDataArray = append(batchSaveDidDemandDidLastTimeReqDataArray, reqData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandDidLastTimeReqDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandDidLastTimeDataTime < 60 {
		batchSaveDidDemandDidLastTimeDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandDidLastTimeReqDataArray[0:]

	batchSaveDidDemandDidLastTimeReqDataArray = batchSaveDidDemandDidLastTimeReqDataArray[0:0]
	batchSaveDidDemandDidLastTimeDataMutex.Unlock()
	batchSaveDidDemandDidLastTimeDataTime = utils.GetCurrentSecond()

	err := ReqDidLastTimeStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidLastTimeReq error:", err)
	}
}

func ReqDidLastTimeStatistics(
	c context.Context,
	list []DidDemandReqData,
) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ReqDidLastTimeStatistics, error:", err)
		}
	}()

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {

		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}

	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId := range pAppCounter {

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			// ! 给春生提供的记录DID最后一次请求时间
			lastTimeFieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_REQ_LAST_TIME_FIELDKEY, pAppId, dd)
			_, err = db.GlbRedis.Do(c, "EXHSET", cacheKey, lastTimeFieldKey, utils.GetCurrentMilliSecond(), "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}

		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()
	}

	return
}
