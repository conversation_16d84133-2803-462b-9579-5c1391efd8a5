package req

import (
	"context"
	"fmt"
	"log"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var batchSaveDidDemandReqDataArray []DidDemandReqData
var batchSaveDidDemandReqDataMutex sync.Mutex
var batchSaveDidDemandReqDataTime int64 = utils.GetCurrentSecond()

func DidDemandDidReq(
	c context.Context,
	didMd5 string,
	ip string,
	pAppId string,
) {

	if !device.IsDidReqWhitelist(c, pAppId) {
		return
	}

	reqData := DidDemandReqData{
		DidMd5:        didMd5,
		Ip:            ip,
		PlatformAppID: pAppId,
	}
	batchSaveDidDemandReqDataMutex.Lock()

	batchSaveDidDemandReqDataArray = append(batchSaveDidDemandReqDataArray, reqData)

	batchSize := utilities.RedisBatchSize

	if len(batchSaveDidDemandReqDataArray) < batchSize &&
		utils.GetCurrentSecond()-batchSaveDidDemandReqDataTime < 60 {
		batchSaveDidDemandReqDataMutex.Unlock()
		return
	}

	newDataList := batchSaveDidDemandReqDataArray[0:]

	batchSaveDidDemandReqDataArray = batchSaveDidDemandReqDataArray[0:0]
	batchSaveDidDemandReqDataMutex.Unlock()
	batchSaveDidDemandReqDataTime = utils.GetCurrentSecond()

	err := ReqDidStatistics(c, newDataList)

	if err != nil {
		log.Println("DidDemandDidReq error:", err)
	}
}

func ReqDidStatistics(
	c context.Context,
	list []DidDemandReqData,
) (err error) {
	if utilities.SkipHologress {
		return nil
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("[DID_REQ]ReqDidStatistics, error:", err)
		}
	}()

	dd := time.Now().Format("2006-01-02")

	// did_pappid 的计数
	didPAppCounter := map[string]map[string]int{}
	for _, item := range list {

		if _, ok := didPAppCounter[item.DidMd5]; ok {
			if number, ok2 := didPAppCounter[item.DidMd5][item.PlatformAppID]; ok2 {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = number + 1
			} else {
				didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
			}
		} else {
			didPAppCounter[item.DidMd5] = map[string]int{}
			didPAppCounter[item.DidMd5][item.PlatformAppID] = 1
		}
	}

	//schemaDid := db.NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")

	for didMd5, pAppCounter := range didPAppCounter {
		cacheKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5)
		for pAppId, number := range pAppCounter {
			fieldKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_REQ_NUMBER_FIELDKEY, pAppId, dd)

			randTTL, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)

			_, err := db.GlbRedis.Do(c, "EXHINCRBY", cacheKey, fieldKey, number, "EX", int32(randTTL.Seconds())).Result()
			if err != nil {
				continue
			}
		}

		db.GlbRedis.Expire(c, cacheKey, 24*time.Hour).Result()
	}

	return
}
