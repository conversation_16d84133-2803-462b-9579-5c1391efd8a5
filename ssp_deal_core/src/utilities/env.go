package utilities

import (
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var RedisBatchSize = u.GetEnvInt(
	"REDIS_BATCH_SIZE",
	10000,
)

var PostgresDSN = u.GetEnv(
	"POSTGRES_DSN",
	"host=hgpostcn-cn-9lb3ai0fe002-cn-beijing-vpc-st.hologres.aliyuncs.com port=80 dbname=ssp_device user=BASIC$maplehaze password=Mhint@123 sslmode=disable",
)

var PostgresAdxSSPDataDSN = u.GetEnv(
	"POSTGRES_ADX_SSP_DATA_DSN",
	"host=hgpostcn-cn-9lb3ai0fe002-cn-beijing-vpc-st.hologres.aliyuncs.com port=80 dbname=adx_ssp user=BASIC$maplehaze password=Mhint@123 sslmode=disable",
)

var HologresWriteMode = u.GetEnvInt(
	"HOLOGRES_WRITEMODE",
	0,
)

var HologresBatchSize = u.GetEnvInt(
	"HOLOGRES_BATCHSIZE",
	8192,
)

var HologresWriteBatchByteSize = u.GetEnvInt64(
	"HOLOGRES_WRITE_BATCH_BYTE_SIZE",
	33554432,
)

var HologresWriteMaxIntervalMs = u.GetEnvInt64(
	"HOLOGRES_WRITE_MAX_INTERVAL_MS",
	60000,
)

var HologresThreadSize = u.GetEnvInt(
	"HOLOGRES_THREADSIZE",
	1,
)

// DauRedis去重分片参数，分片数量 = (16 ^ Bit) / Div
// Wenzhi
var DauRedisFilterBit = u.GetEnv(
	"DAU_REDIS_FILTER_BIT",
	"3",
)

// DauRedis去重分片参数，分片数量 = (16 ^ Bit) / Div
// Wenzhi
var DauRedisFilterDiv = u.GetEnv(
	"DAU_REDIS_FILTER_DIV",
	"4",
)

var DauSaveHolo = u.GetEnvBool(
	"DAU_SAVE_HOLO",
	false,
)

var DauSaveHoloRatio = u.GetEnvInt64(
	"DAU_SAVE_HOLO_RATIO",
	0,
)

var MauSaveHoloRatio = u.GetEnvInt64(
	"MAU_SAVE_HOLO_RATIO",
	0,
)

var DidSaveHoloRatio = u.GetEnvInt64(
	"DID_SAVE_HOLO_RATIO",
	0,
)

var RandTTLMinHour = u.GetEnvInt64(
	"RAND_TTL_MIN_HOUR",
	0,
)

var RandTTLMaxHour = u.GetEnvInt64(
	"RAND_TTL_MAX_HOUR",
	3,
)

// DEBUG

var WriteHoloDebugReqDataRatio = u.GetEnvInt(
	"WRITE_HOLO_DEBUG_REQ_DATA_RATIO",
	0,
)

var DebugPAppId = u.GetEnv(
	"DEBUG_P_APP_ID",
	"",
)

var DebugTmpAppId = u.GetEnv(
	"DEBUG_TMP_APP_ID",
	"",
)

var DebugAppId = u.GetEnv(
	"DEBUG_APP_ID",
	"",
)

var DebugKSUGMaxEcpm = u.GetEnvInt(
	"DEBUG_KS_UG_MAX_ECPM",
	0,
)

var SkipHologress = u.GetEnvBool(
	"SKIP_HOLOGRESS",
	false,
)

var KsCridReplaceSwitch = u.GetEnvBool(
	"KS_CRID_REPLACE_SWITCH",
	true,
)

// 京准通开屏自渲染广告位id
var JDSDKSplashSelfRenderPlatformPosIdList = u.GetEnvStringArray(
	"JD_SDK_SPLASH_SELF_RENDER_PPOS_ID_LIST",
	[]string{},
)

var DebugReplacePlatformAppIDList = u.GetEnvStringArray(
	"DEBUG_REPLACE_P_APP_ID_LIST",
	[]string{},
)

// var BlackDIDList []string = u.GetEnvStringArray(
// 	"BLACK_DID_LIST",
// 	[]string{},
// )

var KafkaUserName = u.GetEnv(
	"KAFKA_USERNAME",
	"alikafka_serverless-cn-fhh3zns0s04",
)

var KafkaPassword = u.GetEnv(
	"KAFKA_PASSWORD",
	"pB4bjjIuVxNFZU5B5ZeoJHy7YPo7kCX9",
)

var MHCaPath = u.GetEnv(
	"MH_CA_PATH",
	"/only-4096-ca-cert",
)

var DauSaveKafkaRatio = u.GetEnvInt64(
	"DAU_SAVE_KAFKA_RATIO",
	0,
)

// Dau写入kafka之前redis去重的ttl
// Wenzhi.
var DauReqKafkaRedisFilterTTLMinutes = u.GetEnvInt(
	"DAU_KAFKA_REDISFILTER_TTL_MINUTES",
	1,
)

// 设备信息写入比率
var DeviceInfoSaveRatio = u.GetEnvInt64(
	"DEVICE_INFO_SAVE_RATIO",
	10000,
)

// var DauUsingDemandWhitelist bool = u.GetEnvBool(
// 	"DAU_USING_DEMAND_WHITELIST",
// 	false,
// )

// var DauUsingSupplyWhitelist bool = u.GetEnvBool(
// 	"DAU_USING_SUPPLY_WHITELIST",
// 	false,
// )

var DauUsingDemandBlacklist = u.GetEnvBool(
	"DAU_USING_DEMAND_BLACKLIST",
	true,
)

var DauUsingSupplyBlacklist = u.GetEnvBool(
	"DAU_USING_SUPPLY_BLACKLIST",
	true,
)

// DeviceDictModelProbability ua/dpi 写入kafka 写入概率
var DeviceDictModelProbability = u.GetEnvInt(
	"DEVICE_DICT_MODEL_PROBABILITY",
	100000,
)

// DeviceAppListProbability applist 写入kafka 写入概率
var DeviceAppListProbability = u.GetEnvInt(
	"DEVICE_APP_LIST_PROBABILITY",
	80,
)

var DevicePriceMediaID = u.GetEnv(
	"DEVICE_PRICE_MEDIAID",
	"",
)
