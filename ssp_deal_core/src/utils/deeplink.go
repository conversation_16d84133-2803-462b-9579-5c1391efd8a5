package utils

import "strings"

// GetIconURLByDeeplink ...
func GetIconURLByDeeplink(deeplink string) string {
	if len(deeplink) == 0 {
		return "https://static.maplehaze.cn/static/logo_256.png"
	} else if strings.Contains(deeplink, "pinduoduo") {
		return "https://static.maplehaze.cn/adimg/pdd/pdd-icon.png"
	} else if strings.Contains(deeplink, "taobao") {
		return "https://static.maplehaze.cn/adimg/taobao/taobao-icon-512x512.png"
	}

	return "https://static.maplehaze.cn/static/logo_256.png"
}
