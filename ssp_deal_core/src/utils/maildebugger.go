package utils

import (
	"fmt"
	"net/http"
	"strings"
)

type MailDebugger struct {
	DebuggerUrl string
}

func MailDebuggerSend(debug string) {
	mailDebugger := MailDebugger{DebuggerUrl: "http://ssp-statistics-core-svc/api/debug"}
	//mailDebugger := MailDebugger{DebuggerUrl: "https://ssp-statistics.maplehaze.cn/api/debug"}
	mailDebugger.Send(debug)
}

func (md *MailDebugger) Send(debug string) {
	var r http.Request
	r.ParseForm()
	r.Form.Add("debug", debug)
	bodystr := strings.TrimSpace(r.Form.Encode())
	request, err := http.NewRequest("POST", md.DebuggerUrl, strings.NewReader(bodystr))
	if err != nil {
		fmt.Println(err)
	}
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	request.Header.Set("Connection", "Keep-Alive")

	var resp *http.Response
	resp, err = http.DefaultClient.Do(request)
	if err != nil {
		fmt.Println(err)

	}
	fmt.Println(resp)
}
