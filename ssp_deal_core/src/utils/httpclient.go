package utils

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

var client *u.HttpClient
var clientOnce sync.Once

// 创建resty全局实例
func GetHttpClient() *u.HttpClient {
	clientOnce.Do(func() {
		client = u.NewClient()
	})
	return client
}

// 退出时释放连接池资源
func CloseHttpClient() {
	if client != nil {
		client.Close()
	}
}

// 竞价成功失败请求上报, 默认1000ms timeout
func CurlWinLossNoticeURL(url string) {
	bodyContent, statusCode, err := GetHttpClient().DoWithTimeout(context.Background(), 1000*time.Millisecond, http.MethodGet, url, u.WithHeaders(map[string]string{"Content-Type": "application/json; charset=utf-8"}))
	fmt.Println("kbg_debug_win_loss:", statusCode, err, url, string(bodyContent))
}
