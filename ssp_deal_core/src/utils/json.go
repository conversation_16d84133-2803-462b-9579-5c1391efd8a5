package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
)

func JsonPrintln(object interface{}) {
	return

	fmt.Println("<<<DEBUG")
	jsonBytes, err := JSONNoEscapeMarshal(object)
	if err != nil {
		fmt.Printf("%+v\n", object)
	} else {
		fmt.Println(string(jsonBytes))
	}
	fmt.Println("DEBUG>>>")
}

func JSONNoEscapeMarshal(data interface{}) ([]byte, error) {
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	if err := jsonEncoder.Encode(data); err != nil {
		return nil, err
	}

	return bf.Bytes(), nil
}
