package up_common

import (
	"context"
	"fmt"
	"mh_proxy/models"
	"net/url"
)

type MHUpPiplineStatusEnum int

const (
	MH_UP_PIPLINE_STATUS_EMPTY MHUpPiplineStatusEnum = iota
	MH_UP_PIPLINE_STATUS_RUNNING
	MH_UP_PIPLINE_STATUS_BREAK
)

const (
	MH_UP_ERROR_CODE_102006 int = 102006
	MH_UP_ERROR_CODE_104013 int = 104013
	MH_UP_ERROR_CODE_104022 int = 104022
	MH_UP_ERROR_CODE_900000 int = 900000
	MH_UP_ERROR_CODE_900002 int = 900002
	MH_UP_ERROR_CODE_900003 int = 900003
	MH_UP_ERROR_CODE_900004 int = 900004
	MH_UP_ERROR_CODE_900101 int = 900101
	MH_UP_ERROR_CODE_900102 int = 900102
	MH_UP_ERROR_CODE_900103 int = 900103
	MH_UP_ERROR_CODE_900106 int = 900106
	MH_UP_ERROR_CODE_900107 int = 900107
	MH_UP_ERROR_CODE_900108 int = 900108
	MH_UP_ERROR_CODE_900201 int = 900201
)

type UpCommonReplacedValues struct {
	Os              UpCommonOSEnum
	OsVersion       string
	Model           string
	DeviceType      UpCommonDeviceTypeEnum
	Manufacturer    string
	Imei            string
	ImeiMd5         string
	Oaid            string
	AndroidId       string
	AndroidIdMd5    string
	Idfa            string
	IdfaMd5         string
	ScreenDirection UpCommonScreenDirection
	Ua              string
}

func (v *UpCommonReplacedValues) String() string {
	return fmt.Sprintf("%+v", *v)
}

type UpCommonScreenDirection int

const (
	MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE UpCommonScreenDirection = 0
	MH_UP_COMMON_SCREENDIRECTION_PORTRAIT  UpCommonScreenDirection = 1
)

type UpCommonOSEnum int

const (
	MH_UP_COMMON_OS_ANDROID UpCommonOSEnum = iota
	MH_UP_COMMON_OS_IOS
	MH_UP_COMMON_OS_UNKNOWN
)

func (os UpCommonOSEnum) String() string {
	switch os {
	case MH_UP_COMMON_OS_ANDROID:
		return "android"
	case MH_UP_COMMON_OS_IOS:
		return "ios"
	}
	return "unknow"
}

func NewCommonOS(os string) UpCommonOSEnum {
	switch os {
	case "android":
		return MH_UP_COMMON_OS_ANDROID
	case "ios":
		return MH_UP_COMMON_OS_IOS
	}

	return MH_UP_COMMON_OS_ANDROID
}

type UpCommonDeviceTypeEnum int

const (
	MH_UP_COMMON_DEVICE_TYPE_UNKNOWN UpCommonDeviceTypeEnum = 0
	MH_UP_COMMON_DEVICE_TYPE_PHONE   UpCommonDeviceTypeEnum = 1
	MH_UP_COMMON_DEVICE_TYPE_PAD     UpCommonDeviceTypeEnum = 2
)

type UpCommonConnectTypeEnum int

const (
	MH_UP_COMMON_CONNECTTYPE_UNKNOWN UpCommonConnectTypeEnum = iota
	MH_UP_COMMON_CONNECTTYPE_WIFI
	MH_UP_COMMON_CONNECTTYPE_2G
	MH_UP_COMMON_CONNECTTYPE_3G
	MH_UP_COMMON_CONNECTTYPE_4G
	MH_UP_COMMON_CONNECTTYPE_5G
)

func (connectType UpCommonConnectTypeEnum) String() string {
	switch connectType {
	case 1:
		return "wifi"
	case 2:
		return "2G"
	case 3:
		return "3G"
	case 4:
		return "4G"
	case 5:
		return "5G"
	}
	return "unknown"
}

type UpCommonCarrierEnum int

const (
	MH_UP_COMMON_CARRIER_UNKNOWN UpCommonCarrierEnum = iota
	MH_UP_COMMON_CARRIER_CM
	MH_UP_COMMON_CARRIER_CU
	MH_UP_COMMON_CARRIER_CT
)

func (carrier UpCommonCarrierEnum) String() string {
	switch carrier {
	case MH_UP_COMMON_CARRIER_CM:
		return "cm"
	case MH_UP_COMMON_CARRIER_CU:
		return "cu"
	case MH_UP_COMMON_CARRIER_CT:
		return "ct"
	}
	return "unknown"
}

type UpCommonConvType int

const (
	MH_UP_COMMON_CONVTYPE_DOWNLOAD_START    UpCommonConvType = 1
	MH_UP_COMMON_CONVTYPE_DOWNLOAD_END      UpCommonConvType = 2
	MH_UP_COMMON_CONVTYPE_INSTALL_END       UpCommonConvType = 3
	MH_UP_COMMON_CONVTYPE_DOWNLOAD_PAUSE    UpCommonConvType = 3
	MH_UP_COMMON_CONVTYPE_DOWNLOAD_CONTINUE UpCommonConvType = 5
	MH_UP_COMMON_CONVTYPE_DOWNLOAD_REMOVE   UpCommonConvType = 6
	MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS  UpCommonConvType = 10
	MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL     UpCommonConvType = 11
	MH_UP_COMMON_CONVTYPE_USER_CLOSE_AD     UpCommonConvType = 100
	MH_UP_COMMON_CONVTYPE_USER_REDUCE_AD    UpCommonConvType = 101
)

type UpCommonCrtType int

const (
	MH_UP_COMMON_CRTTYPE_IMAGE UpCommonCrtType = 11
	MH_UP_COMMON_CRTTYPE_VIDEO UpCommonCrtType = 20

	// Deprecated
	MH_UP_COMMON_CRTTYPE_IMAGE1 UpCommonCrtType = 2
	// Deprecated
	MH_UP_COMMON_CRTTYPE_IMAGE1_TEXT2 UpCommonCrtType = 7
	// Deprecated
	MH_UP_COMMON_CRTTYPE_IMAGE_TEXT_SMALL UpCommonCrtType = 24
)

type UpCommonInteractType int

const (
	MH_UP_COMMON_INTERACTTYPE_LANDPAGE UpCommonInteractType = 0
	MH_UP_COMMON_INTERACTTYPE_DOWNLOAD UpCommonInteractType = 1
)

const MH_UP_COMMON_DEFAULT_PERMISSION = "允许程序访问电话状态\n允许程序通过GPS芯片接收卫星的定位信息\n允许程序可以读取设备外部存储空间\n允许程序更改系统设置\n允许程序通过WiFi或移动基站的方式获取用户粗略位置信息\n允许程序写入外部存储,如SD卡上写文件\n"

type UpCommonVidoeEventType int

const (
	MH_UP_COMMON_VIDEO_EVENTTYPE_START         UpCommonVidoeEventType = 100
	MH_UP_COMMON_VIDEO_EVENTTYPE_FIRSTQUARTILE UpCommonVidoeEventType = 2
	MH_UP_COMMON_VIDEO_EVENTTYPE_MIDPOINT      UpCommonVidoeEventType = 3
	MH_UP_COMMON_VIDEO_EVENTTYPE_THIRDUARTILE  UpCommonVidoeEventType = 4
	MH_UP_COMMON_VIDEO_EVENTTYPE_END           UpCommonVidoeEventType = 103
	MH_UP_COMMON_VIDEO_EVENTTYPE_MUTE          UpCommonVidoeEventType = 6
	MH_UP_COMMON_VIDEO_EVENTTYPE_SKIP          UpCommonVidoeEventType = 7
	MH_UP_COMMON_VIDEO_EVENTTYPE_CLOSE         UpCommonVidoeEventType = 104
)

type UpCommonPosType int

const (
	MH_UP_COMMON_POSTYPE_BANNER           UpCommonPosType = 1
	MH_UP_COMMON_POSTYPE_SPLASH           UpCommonPosType = 2
	MH_UP_COMMON_POSTYPE_INTERSTITIAL     UpCommonPosType = 3
	MH_UP_COMMON_POSTYPE_NATIVE           UpCommonPosType = 4
	MH_UP_COMMON_POSTYPE_PATCH            UpCommonPosType = 13
	MH_UP_COMMON_POSTYPE_FULLSCREEN_VIDEO UpCommonPosType = 9
	MH_UP_COMMON_POSTYPE_REWARD_VIDEO     UpCommonPosType = 11
)

type UpCommonResponseObject struct {
	RespData *UpCommonResponseDataObject
	Extra    *UpCommonResponseExtraObject
}

func (r *UpCommonResponseObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

// TODO: 将来替换掉
type UpCommonResponseExtraObject models.MHUpRespExtra

func (r *UpCommonResponseExtraObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonResponseDataDataMapObject map[string]struct {
	List []*UpCommonAPIResponseObject `json:"list"`
}

func (r *UpCommonResponseDataDataMapObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonResponseDataObject struct {
	Ret          int                                `json:"ret"`
	Msg          string                             `json:"msg"`
	Data         *UpCommonResponseDataDataMapObject `json:"data"`
	IsLogicPixel int                                `json:"is_logic_pixel"`
	IsClickLimit int                                `json:"is_click_limit"`
}

func (r *UpCommonResponseDataObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonAPIResponseObject struct {
	AdId              string                                `json:"ad_id"`
	Title             string                                `json:"title"`
	Description       string                                `json:"description"`
	PackageName       string                                `json:"package_name"`
	Crid              string                                `json:"crid"`
	ReqWidth          int                                   `json:"req_width"`
	ReqHeight         int                                   `json:"req_height"`
	DeepLink          string                                `json:"deep_link,omitempty"`
	ConvTracks        []*UpCommonAPIResponseConvTrackObject `json:"conv_tracks,omitempty"`
	IconUrl           string                                `json:"icon_url,omitempty"`
	CrtType           UpCommonCrtType                       `json:"crt_type,omitempty"`
	Video             *UpCommonAPIResponseVideoObject       `json:"video,omitempty"`
	Images            []*UpCommonAPIResponseImageObject     `json:"imgs,omitempty"`
	InteractType      UpCommonInteractType                  `json:"interact_type"`
	AdUrl             string                                `json:"ad_url,omitempty"`
	LandPageUrl       string                                `json:"landpage_url,omitempty"`
	DownloadUrl       string                                `json:"download_url,omitempty"`
	Publisher         string                                `json:"publisher,omitempty"`
	AppName           string                                `json:"app_name,omitempty"`
	AppVersion        string                                `json:"app_version,omitempty"`
	PrivacyUrl        string                                `json:"privacy_url,omitempty"`
	PermissionUrl     string                                `json:"permission_url,omitempty"`
	Permission        string                                `json:"permission,omitempty"`
	PackageSize       int                                   `json:"package_size,omitempty"`
	ImpressionLink    []string                              `json:"impression_link"`
	ClickLink         []string                              `json:"click_link"`
	MaterialDirection int                                   `json:"material_direction,omitempty"`
	Log               string                                `json:"log,omitempty"`
}

func (r *UpCommonAPIResponseObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonAPIResponseConvTrackObject struct {
	ConvType UpCommonConvType `json:"conv_type"`
	ConvUrls []string         `json:"conv_urls"`
}

func (r *UpCommonAPIResponseConvTrackObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonAPIResponseVideoObject struct {
	Duration     int                                         `json:"duration,omitempty"`
	Width        int                                         `json:"width,omitempty"`
	Height       int                                         `json:"height,omitempty"`
	VideoUrl     string                                      `json:"video_url"`
	CoverUrl     string                                      `json:"cover_url,omitempty"`
	EndcardType  int                                         `json:"endcard_type"`
	EndcardRange int                                         `json:"endcard_range"`
	EventTracks  []*UpCommonAPIResponseVideoEventTrackObject `json:"event_tracks,omitempty"`
}

func (r *UpCommonAPIResponseVideoObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonAPIResponseVideoEventTrackObject struct {
	EventType      UpCommonVidoeEventType `json:"event_type,omitempty"`
	EventTrackUrls []string               `json:"event_track_urls,omitempty"`
}

func (r *UpCommonAPIResponseVideoEventTrackObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonAPIResponseImageObject struct {
	Url    string `json:"url"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
}

func (r *UpCommonAPIResponseImageObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type UpCommonLinkWithParams struct {
	url url.Values
}

func (p *UpCommonLinkWithParams) Add(key string, value string) {
	p.url.Add(key, value)
}

func (p *UpCommonLinkWithParams) AddBigDataParams(paramsString string) {
	p.url.Add("log", paramsString)
}

func (p *UpCommonLinkWithParams) StringWithUrl(url string) string {
	return url + "?" + p.url.Encode()
}

func (p *UpCommonLinkWithParams) String() string {
	return p.url.Encode()
}

type UpCommonPipline struct {
	Context context.Context `json:"-"`

	Status MHUpPiplineStatusEnum
	Error  error `json:"-"`

	ReplacedValues *UpCommonReplacedValues

	UUID                string
	MhReq               *models.MHReq
	LocalPos            *models.LocalPosStu
	PlatformPos         *models.PlatformPosStu
	CategoryInfo        *models.CategoryStu
	ResponseExtra       *UpCommonResponseExtraObject
	Response            *UpCommonResponseObject
	DidInfo             *models.DIDRedisDataStu
	RespTmpInternalCode int
	IsLogicPixel        bool
	LogicPixelWidth     int
	LogicPixelHeight    int
	IsClickLimit        bool
	IsReportToWin       bool
	IsPlatformPolicyOK  bool
	MhClickLink         *UpCommonLinkWithParams
	MhImpressionLink    *UpCommonLinkWithParams
}

func (p *UpCommonPipline) String() string {
	return fmt.Sprintf("%+v", *p)
}

// type UpProjectPipline[T any] interface {
// 	Init() *T
// }
