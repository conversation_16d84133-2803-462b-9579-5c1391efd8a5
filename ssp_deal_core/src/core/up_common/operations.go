package up_common

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/
func NewCommonPipline(c context.Context,
	mhReq *models.MHReq,
	localPos *models.LocalPosStu,
	platformPos *models.PlatformPosStu,
	categoryInfo *models.CategoryStu,
	bigdataUID string) *UpCommonPipline {

	p := &UpCommonPipline{}

	p.Status = MH_UP_PIPLINE_STATUS_RUNNING

	p.Context = c
	p.MhReq = mhReq
	p.LocalPos = localPos
	p.PlatformPos = platformPos
	p.CategoryInfo = categoryInfo
	p.UUID = bigdataUID

	p.RespTmpInternalCode = MH_UP_ERROR_CODE_900000

	// p.DidInfo = models.RedisPlatformDID(p.Context,
	// 	p.MhReq,
	// 	p.LocalPos,
	// 	p.PlatformPos)

	return p.setupResponseExtra().
		setupIsPlatformPolicyOK().
		setupIsLogicPixel().
		setupIsClickLimit().
		checkPlatformAppIsActive().
		checkPlatformPosIsActive().
		checkDAULimit().
		checkMaxReqNum().
		checkMaxExpNum().
		checkMaxClkNum().
		checkArea().
		checkUA().
		initMhImpressionLink().
		initMhClickLink().
		filterDid()
}

/**
* Private Methods
**/

func (p *UpCommonPipline) setupResponseExtra() *UpCommonPipline {

	var responseExtra UpCommonResponseExtraObject
	responseExtra.InternalCode = 900000
	responseExtra.ExternalCode = 0
	responseExtra.UpReqTime = 0
	responseExtra.UpReqNum = 0
	responseExtra.UpRespTime = 0
	responseExtra.UpRespNum = 0
	responseExtra.UpRespOKNum = 0

	p.ResponseExtra = &responseExtra

	return p
}

// 检查App
func (p *UpCommonPipline) checkPlatformAppIsActive() *UpCommonPipline {

	if p.PlatformPos.PlatformAppIsActive == 1 {
		return p
	}

	// 错误信息
	p.SetPanicStringAndCodes("app closed",
		MH_UP_ERROR_CODE_900103,
		MH_UP_ERROR_CODE_102006)
	return p
}

// 检查Pos
func (p *UpCommonPipline) checkPlatformPosIsActive() *UpCommonPipline {

	if p.PlatformPos.PlatformPosIsActive == 1 {
		return p
	}

	// 错误信息
	p.SetPanicStringAndCodes("pos closed",
		MH_UP_ERROR_CODE_900103,
		MH_UP_ERROR_CODE_102006)
	return p
}

func (p *UpCommonPipline) checkDAULimit() *UpCommonPipline {

	// TODO: 加限制DAU的代码

	return p
}

func (p *UpCommonPipline) checkMaxReqNum() *UpCommonPipline {

	if p.CategoryInfo.MaxReqNum > 0 {
		maxReqRedisKey := "max_req_" +
			time.Now().Format("2006-01-02") + "_" +
			p.LocalPos.LocalAppID + "_" +
			p.LocalPos.LocalPosID + "_" +
			p.PlatformPos.PlatformAppID + "_" +
			p.PlatformPos.PlatformPosID
		maxReqRedisValue, cacheErr := models.CacheFromRedis(p.Context, maxReqRedisKey)

		if cacheErr == nil && utils.ConvertStringToInt(maxReqRedisValue) == 0 {
			p.SetPanicStringAndCodes(fmt.Sprintf("max req redis key: %s, max req redis value: %s", maxReqRedisKey, maxReqRedisValue),
				MH_UP_ERROR_CODE_900002,
				MH_UP_ERROR_CODE_102006)
		}
	}

	return p
}

func (p *UpCommonPipline) checkMaxExpNum() *UpCommonPipline {

	if p.CategoryInfo.MaxReqNum > 0 {
		maxExpRedisKey := "max_exp_" +
			time.Now().Format("2006-01-02") + "_" +
			p.LocalPos.LocalAppID + "_" +
			p.LocalPos.LocalPosID + "_" +
			p.PlatformPos.PlatformAppID + "_" +
			p.PlatformPos.PlatformPosID
		maxExpRedisValue, cacheErr := models.CacheFromRedis(p.Context, maxExpRedisKey)

		if cacheErr == nil && utils.ConvertStringToInt(maxExpRedisValue) == 0 {
			p.SetPanicStringAndCodes(fmt.Sprintf("max exp redis key: %s, max exp redis value: %s", maxExpRedisKey, maxExpRedisValue),
				MH_UP_ERROR_CODE_900003,
				MH_UP_ERROR_CODE_102006)
		}
	}

	return p
}

func (p *UpCommonPipline) checkMaxClkNum() *UpCommonPipline {

	if p.CategoryInfo.MaxReqNum > 0 {
		maxClkRedisKey := "max_clk_" +
			time.Now().Format("2006-01-02") + "_" +
			p.LocalPos.LocalAppID + "_" +
			p.LocalPos.LocalPosID + "_" +
			p.PlatformPos.PlatformAppID + "_" +
			p.PlatformPos.PlatformPosID
		maxClkRedisValue, cacheErr := models.CacheFromRedis(p.Context, maxClkRedisKey)

		if cacheErr == nil && utils.ConvertStringToInt(maxClkRedisValue) == 0 {
			p.SetPanicStringAndCodes(fmt.Sprintf("max clk redis key: %s, max clk redis value: %s", maxClkRedisKey, maxClkRedisValue),
				MH_UP_ERROR_CODE_900004,
				MH_UP_ERROR_CODE_102006)
		}
	}

	return p
}

func (p *UpCommonPipline) checkArea() *UpCommonPipline {

	if !strings.Contains(p.MhReq.Device.IP, ":") {
		region, err := utils.New(config.IPDBPath)

		if err == nil {
			ip, _ := region.BtreeSearch(p.MhReq.Device.IP)

			// 地域白名单
			if len(p.CategoryInfo.WhiteList) > 0 {
				if !strings.Contains(p.CategoryInfo.WhiteList, ip.Province) && !strings.Contains(p.CategoryInfo.WhiteList, ip.Country) {
					p.SetPanicStringAndCodes("ip is not in white list",
						MH_UP_ERROR_CODE_900108,
						MH_UP_ERROR_CODE_102006)
				}
			}
			// 地域黑名单
			if len(p.CategoryInfo.BlackList) > 0 {
				if strings.Contains(p.CategoryInfo.BlackList, ip.Province) || strings.Contains(p.CategoryInfo.BlackList, ip.Country) {
					p.SetPanicStringAndCodes("ip is in black list",
						MH_UP_ERROR_CODE_900108,
						MH_UP_ERROR_CODE_102006)
				}
			}
		}

		defer region.Close()
	}

	return p
}

func (p *UpCommonPipline) checkUA() *UpCommonPipline {

	if p.PlatformPos.PlatformAppFilterUa == 1 {
		destUA := p.MhReq.Device.Ua

		if !strings.HasPrefix(destUA, "Mozilla") && !strings.HasPrefix(destUA, "Dalvik") {

			p.SetPanicStringAndCodes("invalid ua",
				MH_UP_ERROR_CODE_900101,
				MH_UP_ERROR_CODE_104022)
		}
	}

	return p
}

func (p *UpCommonPipline) setupIsLogicPixel() *UpCommonPipline {

	p.IsLogicPixel = false

	if p.LocalPos.LocalAppType == "1" {
		p.IsLogicPixel = true
		if len(p.LocalPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(p.LocalPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == p.PlatformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				p.IsLogicPixel = false
			}
		}
	} else if p.LocalPos.LocalAppClickPointType == 2 {
		p.IsLogicPixel = true
	}

	if p.IsLogicPixel {
		if p.MhReq.Device.XDPI > 0 {
			p.LogicPixelWidth = int(float32(p.LocalPos.LocalPosWidth)/p.MhReq.Device.XDPI + 0.5)
			p.LogicPixelHeight = int(float32(p.LocalPos.LocalPosHeight)/p.MhReq.Device.XDPI + 0.5)
		} else {
			p.LogicPixelWidth = p.LocalPos.LocalPosWidth
			p.LogicPixelHeight = p.LocalPos.LocalPosHeight
		}
	}

	return p
}

func (p *UpCommonPipline) setupIsClickLimit() *UpCommonPipline {

	p.IsClickLimit = false

	// if p.LocalPos.LocalAppType == "1" {
	// 	if p.LocalPos.LocalAppIsClickLimit == 1 {
	// 		isOK := false
	// 		clickLimitArray := strings.Split(p.LocalPos.LocalAppClickLimitList, ",")
	// 		for _, tmpItem := range clickLimitArray {
	// 			if tmpItem == p.PlatformPos.PlatformMediaID {
	// 				isOK = true
	// 				break
	// 			}
	// 		}
	// 		if isOK {
	// 			p.IsClickLimit = true
	// 		}
	// 	}
	// }

	return p
}

func (p *UpCommonPipline) setupIsPlatformPolicyOK() *UpCommonPipline {

	p.IsPlatformPolicyOK = true

	// if !p.IsPlatformPolicyOK {
	// 	p.SetPanicStringAndCodes("platform policy not ok", MH_UP_ERROR_CODE_900103, MH_UP_ERROR_CODE_102006)
	// }

	return p
}

func (p *UpCommonPipline) filterDid() *UpCommonPipline {

	// 老筛选包删除
	// if p.DidInfo != nil {
	// 	if p.PlatformPos.PlatformAppIsDirectMedia == 1 {
	// 		if strings.Contains(p.PlatformPos.PlatformAppDirectMedias, p.LocalPos.LocalAppID) {
	// 		} else {
	// 			isOK := false
	// 			directMediaArray := strings.Split(p.PlatformPos.PlatformAppDirectMedias, ",")
	// 			for _, tmpDirectMediaItem := range directMediaArray {
	// 				if strings.Contains(p.DidInfo.AppIDs, tmpDirectMediaItem) {
	// 					isOK = true
	// 					break
	// 				}
	// 			}
	// 			if isOK {
	// 			} else {
	// 				p.SetPanicStringAndCodes("", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
	// 			}
	// 		}
	// 	}
	// }

	return p
}

func (p *UpCommonPipline) initMhClickLink() *UpCommonPipline {

	if p.MhClickLink == nil {
		p.MhClickLink = &UpCommonLinkWithParams{url: url.Values{}}
	}

	p.MhClickLink.Add("req_width", utils.ConvertIntToString(p.PlatformPos.PlatformPosWidth))
	p.MhClickLink.Add("req_height", utils.ConvertIntToString(p.PlatformPos.PlatformPosHeight))

	if p.IsLogicPixel {
		p.MhClickLink.Add("width", utils.ConvertIntToString(p.LogicPixelWidth))
		p.MhClickLink.Add("height", utils.ConvertIntToString(p.LogicPixelHeight))
	} else {
		p.MhClickLink.Add("width", utils.ConvertIntToString(p.LocalPos.LocalPosWidth))
		p.MhClickLink.Add("height", utils.ConvertIntToString(p.LocalPos.LocalPosHeight))
	}

	if p.PlatformPos.PlatformPosIsReplaceXY == 1 {
		p.MhClickLink.Add("down_x", "-999")
		p.MhClickLink.Add("down_y", "-999")
		p.MhClickLink.Add("up_x", "-999")
		p.MhClickLink.Add("up_y", "-999")
	} else {
		p.MhClickLink.Add("down_x", "__DOWN_X__")
		p.MhClickLink.Add("down_y", "__DOWN_Y__")
		p.MhClickLink.Add("up_x", "__UP_X__")
		p.MhClickLink.Add("up_y", "__UP_Y__")
	}

	return p
}

func (p *UpCommonPipline) initMhImpressionLink() *UpCommonPipline {

	if p.MhImpressionLink == nil {
		p.MhImpressionLink = &UpCommonLinkWithParams{url: url.Values{}}
	}

	return p
}

/**
 * * 函数式方法模板
 */
func (p *UpCommonPipline) actionTemplate() *UpCommonPipline {

	return p
}

/**
* Public Methods
**/

func (p *UpCommonPipline) ReplaceDid() *UpCommonPipline {

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	if p.PlatformPos.PlatformAppIsReplaceDID == 1 {
		// if len(p.PlatformPos.PlatformAppReplaceDIDMedias) > 0 {
		// 	if strings.Contains(p.PlatformPos.PlatformAppReplaceDIDMedias, p.LocalPos.LocalAppID) {
		// 	} else {
		// 		isDirectOK := false
		// 		if p.DidInfo != nil {
		// 			if strings.Contains(p.DidInfo.AppIDs, p.PlatformPos.PlatformAppReplaceDIDMedias) {
		// 				isDirectOK = true
		// 			}
		// 		}
		// 		if isDirectOK {
		// 		} else {
		// 			if p.IsAndroid() {
		// 				country, city := utils.GetCityByIP(p.MhReq.Device.IP)

		// 				if country == "中国" && len(city) > 2 {
		// 					osvStr := ""
		// 					if p.IsAndroidMajorLessThanTen() {
		// 						osvStr = "less10"
		// 					} else {
		// 						osvStr = "more10"
		// 					}
		// 					// osv model key
		// 					osvKey := strings.Replace(p.MhReq.Device.OsVersion, " ", "", -1)
		// 					modelKey := strings.ToLower(strings.Replace(p.MhReq.Device.Model, " ", "", -1))
		// 					redisReplaceKey := ""
		// 					if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "osv") && strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "model") {
		// 						redisReplaceKey = "rr1_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + osvKey + "_" + modelKey + "_" + city
		// 					} else if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "osv") {
		// 						redisReplaceKey = "rr2_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + osvKey + "_" + city
		// 					} else if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "model") {
		// 						redisReplaceKey = "rr3_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + modelKey + "_" + city
		// 					} else {
		// 						redisReplaceKey = "rr4_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + osvStr + "_" + city
		// 					}

		// 					redisDIDValue, redisErr := did.GetReplaceDIDFromRedis(p.Context, redisReplaceKey)

		// 					if redisErr != nil {
		// 						p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 					} else {
		// 						var didRedisData models.ReplaceDIDStu
		// 						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
		// 						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

		// 						switch strings.ToLower(p.MhReq.Device.Os) {
		// 						case MH_UP_COMMON_OS_ANDROID.String():
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_ANDROID
		// 						case MH_UP_COMMON_OS_IOS.String():
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_IOS
		// 						default:
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_UNKNOWN
		// 						}
		// 						p.ReplacedValues.OsVersion = didRedisData.OsVersion
		// 						p.ReplacedValues.Model = didRedisData.Model
		// 						p.ReplacedValues.DeviceType = UpCommonDeviceTypeEnum(p.MhReq.Device.DeviceType)
		// 						p.ReplacedValues.Manufacturer = didRedisData.Manufacturer
		// 						if p.IsAndroidMajorLessThanTen() {
		// 							if len(didRedisData.Imei) > 0 {
		// 								p.ReplacedValues.Imei = didRedisData.Imei
		// 								p.ReplacedValues.ImeiMd5 = utils.GetMd5(didRedisData.Imei)
		// 							} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
		// 								p.ReplacedValues.ImeiMd5 = strings.ToLower(didRedisData.ImeiMd5)
		// 							} else {
		// 								p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 							}
		// 						} else {
		// 							if len(didRedisData.Oaid) > 0 {
		// 								p.ReplacedValues.Oaid = didRedisData.Oaid
		// 							} else if len(didRedisData.AndroidID) > 0 {
		// 								p.ReplacedValues.AndroidId = didRedisData.AndroidID
		// 								p.ReplacedValues.AndroidIdMd5 = utils.GetMd5(didRedisData.AndroidID)
		// 							} else if len(didRedisData.AndroidIDMd5) > 0 && didRedisData.AndroidIDMd5 != utils.GetMd5("") {
		// 								p.ReplacedValues.AndroidIdMd5 = strings.ToLower(didRedisData.AndroidIDMd5)
		// 							} else {
		// 								p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 							}
		// 						}
		// 						if p.PlatformPos.PlatformPosType == 11 {
		// 							// 0 横屏, 1 竖屏
		// 							p.ReplacedValues.ScreenDirection = UpCommonScreenDirection(p.PlatformPos.PlatformPosDirection)
		// 						}

		// 						if p.PlatformPos.PlatformAppIsReportUa == 1 && p.PlatformPos.PlatformAppIsReplaceDIDUa == 1 {
		// 							p.ReplacedValues.Ua = didRedisData.Ua
		// 						}

		// 						isHaveReplace = true
		// 					}
		// 				} else {
		// 					p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 				}
		// 			} else if p.IsIos() {
		// 				country, city := utils.GetCityByIP(p.MhReq.Device.IP)

		// 				if country == "中国" && len(city) > 2 {
		// 					// osv model key
		// 					osvKey := strings.Replace(p.MhReq.Device.OsVersion, " ", "", -1)
		// 					modelKey := strings.ToLower(strings.Replace(p.MhReq.Device.Model, " ", "", -1))
		// 					redisReplaceKey := ""
		// 					if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "osv") && strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "model") {
		// 						redisReplaceKey = "rr1_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + osvKey + "_" + modelKey + "_" + city
		// 					} else if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "osv") {
		// 						redisReplaceKey = "rr2_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + osvKey + "_" + city
		// 					} else if strings.Contains(p.PlatformPos.PlatformAppReplaceParams, "model") {
		// 						redisReplaceKey = "rr3_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + modelKey + "_" + city
		// 					} else {
		// 						redisReplaceKey = "rr4_" + p.PlatformPos.PlatformAppReplaceDIDMedias + "_" + city
		// 					}

		// 					redisDIDValue, redisErr := did.GetReplaceDIDFromRedis(p.Context, redisReplaceKey)

		// 					if redisErr != nil {
		// 						p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 					} else {
		// 						var didRedisData models.ReplaceDIDStu
		// 						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
		// 						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

		// 						switch strings.ToLower(p.MhReq.Device.Os) {
		// 						case MH_UP_COMMON_OS_ANDROID.String():
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_ANDROID
		// 						case MH_UP_COMMON_OS_IOS.String():
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_IOS
		// 						default:
		// 							p.ReplacedValues.Os = MH_UP_COMMON_OS_UNKNOWN
		// 						}
		// 						p.ReplacedValues.OsVersion = didRedisData.OsVersion
		// 						p.ReplacedValues.Model = didRedisData.Model
		// 						p.ReplacedValues.DeviceType = UpCommonDeviceTypeEnum(p.MhReq.Device.DeviceType)
		// 						p.ReplacedValues.Manufacturer = didRedisData.Manufacturer

		// 						if len(didRedisData.Idfa) > 0 {
		// 							p.ReplacedValues.Idfa = didRedisData.Idfa
		// 							p.ReplacedValues.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)
		// 						} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
		// 							p.ReplacedValues.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

		// 						} else {
		// 							p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 						}

		// 						if p.PlatformPos.PlatformPosType == 11 {
		// 							// 0 横屏, 1 竖屏
		// 							p.ReplacedValues.ScreenDirection = UpCommonScreenDirection(p.PlatformPos.PlatformPosDirection)
		// 						}

		// 						if p.PlatformPos.PlatformAppIsReportUa == 1 && p.PlatformPos.PlatformAppIsReplaceDIDUa == 1 {
		// 							p.ReplacedValues.Ua = didRedisData.Ua
		// 						}

		// 						isHaveReplace = true
		// 					}
		// 				} else {
		// 					p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 				}
		// 			} else {
		// 				p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// 			}
		// 		}
		// 	}
		// } else {
		// 	p.SetPanicStringAndCodes("gdt, no replace did", MH_UP_ERROR_CODE_900101, MH_UP_ERROR_CODE_102006)
		// }
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()

		models.BigDataDIDUPReq(p.Context, p.MhReq, p.LocalPos, p.PlatformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	return p
}

func (p *UpCommonPipline) RandPriceRatio() int {
	return 95 + rand.Intn(4)
}

func (p *UpCommonPipline) RandFailedPriceRatio() int {
	return 150 + rand.Intn(51)
}

func (p *UpCommonPipline) IsAndroid() bool {
	return p.MhReq.Device.Os == "android"
}

func (p *UpCommonPipline) EncodeRtbParams() string {
	encodeRtbParams := ""
	rtbParams := url.Values{}
	rtbParams.Add("app_id", p.LocalPos.LocalAppID)
	rtbParams.Add("pos_id", p.LocalPos.LocalPosID)
	rtbParams.Add("p_app_id", p.PlatformPos.PlatformAppID)
	rtbParams.Add("p_pos_id", p.PlatformPos.PlatformPosID)

	encodeStr, err := utils.EncodeString([]byte(rtbParams.Encode()))

	if err == nil {
		encodeRtbParams = encodeStr
	}

	return encodeRtbParams
}

func (p *UpCommonPipline) IsAndroidMajorLessThanTen() bool {
	osvMajor := 0
	if len(p.MhReq.Device.OsVersion) > 0 {
		osvMajorStr := strings.Split(p.MhReq.Device.OsVersion, ".")[0]
		osvMajor = utils.ConvertStringToInt(osvMajorStr)
	}

	return osvMajor < 10
}

func (p *UpCommonPipline) IsIos() bool {
	return p.MhReq.Device.Os == "ios"
}

func (p *UpCommonPipline) RequestUrl(url string) string {
	httpRequest, _ := http.NewRequest("GET", url, nil)
	httpRequest.Header.Add("Content-Type", "application/json; charset=utf-8")

	client := &http.Client{Timeout: 1000 * time.Millisecond}
	response, err := client.Do(httpRequest)
	if err != nil {
		return ""
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if err != nil {
		return ""
	}

	return string(bodyContent)
}

func (p *UpCommonPipline) ResponseResult() *models.MHUpResp {

	var resp models.MHUpResp

	// 如果因为抛出异常没有执行到 SetupCommonResponse
	if p.Response == nil {
		commonResponse := UpCommonResponseObject{}
		commonResponse.Extra = p.ResponseExtra

		errStr := ""
		if p.Error != nil {
			errStr = p.Error.Error()
		}
		commonResponse.RespData = &UpCommonResponseDataObject{
			Ret: p.ResponseExtra.ExternalCode,
			Msg: errStr,
		}
		p.Response = &commonResponse
	}

	respJson, err := utils.JSONNoEscapeMarshal(p.Response)

	if err == nil {
		json.Unmarshal(respJson, &resp)
	}

	return &resp
}

// 设置字符串类型的错误信息
func (p *UpCommonPipline) SetPanicString(err string) {
	p.SetPanic(fmt.Errorf(err))
}

// 设置字符串类型和Code的错误信息
func (p *UpCommonPipline) SetPanicStringAndCodes(err string, internalCode int, externalCode int) {
	p.ResponseExtra.InternalCode = internalCode
	p.ResponseExtra.ExternalCode = externalCode
	p.SetPanic(fmt.Errorf(err))
}

// 设置错误信息
func (p *UpCommonPipline) SetPanic(err error) {
	p.Error = err
	p.Status = MH_UP_PIPLINE_STATUS_BREAK
	panic(p.Error)
}
