package up_common

import (
	"encoding/json"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"time"
)

func EncodeParams(mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, bigdataUID string,
	maplehazeAdID string,
	localFloorPrice int, localFinalPrice int, ecpm int, supplyEcpm int) string {

	didMd5Key := mhReq.Device.DIDMd5

	bigdataParams := url.Values{}
	bigdataParams.Add("uid", bigdataUID)
	bigdataParams.Add("adid", maplehazeAdID)
	bigdataParams.Add("app_id", localPos.LocalAppID)
	bigdataParams.Add("pos_id", localPos.LocalPosID)
	bigdataParams.Add("app_type", localPos.LocalAppType)
	if len(platformPos.PlatformAppID) > 0 {
		bigdataParams.Add("p_app_id", platformPos.PlatformAppID)
	}
	if len(platformPos.PlatformPosID) > 0 {
		bigdataParams.Add("p_pos_id", platformPos.PlatformPosID)
	}
	bigdataParams.Add("p_app_type", platformPos.PlatformAppType)
	bigdataParams.Add("channel", platformPos.PlatformMediaID)
	bigdataParams.Add("app_name", localPos.LocalAppName)
	bigdataParams.Add("app_bundle", localPos.LocalAppBundleID)
	bigdataParams.Add("os", mhReq.Device.Os)
	bigdataParams.Add("osv", mhReq.Device.OsVersion)
	if len(mhReq.Device.Imei) > 0 {
		bigdataParams.Add("imei", mhReq.Device.Imei)
	}
	if len(mhReq.Device.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", mhReq.Device.ImeiMd5)
	}
	if len(mhReq.Device.AndroidID) > 0 {
		bigdataParams.Add("android_id", mhReq.Device.AndroidID)
	}
	if len(mhReq.Device.AndroidIDMd5) > 0 {
		bigdataParams.Add("android_id_md5", mhReq.Device.AndroidIDMd5)
	}
	if len(mhReq.Device.Oaid) > 0 {
		bigdataParams.Add("oaid", mhReq.Device.Oaid)
	}
	if len(mhReq.Device.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", mhReq.Device.OaidMd5)
	}
	if len(mhReq.Device.Idfa) > 0 {
		bigdataParams.Add("idfa", mhReq.Device.Idfa)
	}
	if len(mhReq.Device.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", mhReq.Device.IdfaMd5)
	}
	if len(mhReq.Device.CAIDMulti) > 0 {
		tmpCaidMultiJson, _ := json.Marshal(mhReq.Device.CAIDMulti)
		bigdataParams.Add("caid_multi", string(tmpCaidMultiJson))
	}
	bigdataParams.Add("model", mhReq.Device.Model)
	bigdataParams.Add("manufacturer", mhReq.Device.Manufacturer)
	bigdataParams.Add("did_md5", didMd5Key)
	bigdataParams.Add("floor_price", utils.ConvertIntToString(localFloorPrice))
	bigdataParams.Add("final_price", utils.ConvertIntToString(localFinalPrice))
	bigdataParams.Add("ecpm", utils.ConvertIntToString(ecpm))
	if mhReq.Device.DPI > 0 {
		bigdataParams.Add("dpi", utils.ConvertFloat32ToString(mhReq.Device.DPI))
	}
	if mhReq.Device.XDPI > 0 {
		bigdataParams.Add("xdpi", utils.ConvertFloat32ToString(mhReq.Device.XDPI))
	}
	bigdataParams.Add("supply_ecpm", utils.ConvertIntToString(supplyEcpm))

	// 点击坐标是否服务器生成、sld样式是否服务器生成、是逻辑像素还是物理像素
	if platformPos.PlatformPosIsReplaceXY == 1 {
		bigdataParams.Add("is_server_replace_xy", utils.ConvertIntToString(platformPos.PlatformPosIsReplaceXY))
	} else {
		bigdataParams.Add("is_server_replace_xy", "0")
	}
	if platformPos.PlatformPosIsReportSLD == 1 {
		bigdataParams.Add("is_server_sld", utils.ConvertIntToString(platformPos.PlatformPosIsReportSLD))
	} else {
		bigdataParams.Add("is_server_sld", "0")
	}
	if localPos.LocalAppClickPointType == 2 {
		bigdataParams.Add("is_logic_pixel", "1")
	} else {
		bigdataParams.Add("is_logic_pixel", "0")
	}

	if len(mhReq.SDKVersion) > 0 {
		bigdataParams.Add("sdk_version", mhReq.SDKVersion)
	}
	bigdataParams.Add("connect_type", utils.ConvertIntToString(mhReq.Network.ConnectType))
	bigdataParams.Add("carrier", utils.ConvertIntToString(mhReq.Network.Carrier))

	bigdataParams.Add("clickpoint_type", utils.ConvertIntToString(localPos.LocalAppClickPointType))
	bigdataParams.Add("clickpoint_replace_type", utils.ConvertIntToString(platformPos.PlatformPosIsReplaceXY))
	bigdataParams.Add("deal_time", utils.ConvertInt64ToString(time.Now().UnixMicro()))

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

// isServerReplaceXY 是否真实在服务器端替换xy, 与配置无关
// serverReplaceXYType 服务器端替换xy类型, 0物理像素, 1逻辑像素
func EncodeParamsWithReplaceData(mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, bigdataUID string,
	maplehazeAdID string,
	localFloorPrice int, localFinalPrice int, ecpm int, isReplaced bool, replaceDIDStu models.ReplaceDIDStu,
	isServerReplaceXY bool, serverReplaceXYType int, supplyEcpm int) string {

	originDidMd5Key := mhReq.Device.DIDMd5

	tmpMHReq := *mhReq
	if isReplaced {
		tmpMHReq.Device.OsVersion = replaceDIDStu.OsVersion
		tmpMHReq.Device.Imei = replaceDIDStu.Imei
		tmpMHReq.Device.ImeiMd5 = replaceDIDStu.ImeiMd5
		tmpMHReq.Device.AndroidID = replaceDIDStu.AndroidID
		tmpMHReq.Device.AndroidIDMd5 = replaceDIDStu.AndroidIDMd5
		tmpMHReq.Device.Oaid = replaceDIDStu.Oaid
		tmpMHReq.Device.Idfa = replaceDIDStu.Idfa
		tmpMHReq.Device.IdfaMd5 = replaceDIDStu.IdfaMd5
		tmpMHReq.Device.Model = replaceDIDStu.Model
		tmpMHReq.Device.Manufacturer = replaceDIDStu.Manufacturer
		tmpMHReq.Device.DeviceStartSec = replaceDIDStu.DeviceStartSec
		tmpMHReq.Device.Country = replaceDIDStu.Country
		tmpMHReq.Device.Language = replaceDIDStu.Language
		tmpMHReq.Device.DeviceNameMd5 = replaceDIDStu.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = replaceDIDStu.HardwareMachine
		tmpMHReq.Device.HardwareModel = replaceDIDStu.HardwareModel
		tmpMHReq.Device.PhysicalMemoryByte = replaceDIDStu.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = replaceDIDStu.HarddiskSizeByte
		tmpMHReq.Device.SystemUpdateSec = replaceDIDStu.SystemUpdateSec
		tmpMHReq.Device.TimeZone = replaceDIDStu.TimeZone
		tmpMHReq.Network.ConnectType = replaceDIDStu.ConnectType
		tmpMHReq.Network.Carrier = replaceDIDStu.Carrier
		// tmpMHReq.Device.CAID = replaceDIDStu.CAID
		// tmpMHReq.Device.CAIDVersion = replaceDIDStu.CAIDVersion
		if len(replaceDIDStu.CAIDMultiJson) > 0 {
			var tmpCAIDMulti []models.MHReqCAIDMulti
			json.Unmarshal([]byte(replaceDIDStu.CAIDMultiJson), &tmpCAIDMulti)

			tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		}
		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	didMd5Key := tmpMHReq.Device.DIDMd5

	bigdataParams := url.Values{}
	bigdataParams.Add("uid", bigdataUID)
	bigdataParams.Add("adid", maplehazeAdID)
	bigdataParams.Add("app_id", localPos.LocalAppID)
	bigdataParams.Add("pos_id", localPos.LocalPosID)
	bigdataParams.Add("app_type", localPos.LocalAppType)
	bigdataParams.Add("p_app_id", platformPos.PlatformAppID)
	bigdataParams.Add("p_pos_id", platformPos.PlatformPosID)
	bigdataParams.Add("p_app_type", platformPos.PlatformAppType)
	bigdataParams.Add("channel", platformPos.PlatformMediaID)
	bigdataParams.Add("app_name", localPos.LocalAppName)
	bigdataParams.Add("app_bundle", localPos.LocalAppBundleID)
	bigdataParams.Add("os", mhReq.Device.Os)
	bigdataParams.Add("osv", tmpMHReq.Device.OsVersion)
	if len(tmpMHReq.Device.Imei) > 0 {
		bigdataParams.Add("imei", tmpMHReq.Device.Imei)
	}
	if len(tmpMHReq.Device.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", tmpMHReq.Device.ImeiMd5)
	}
	if len(tmpMHReq.Device.AndroidID) > 0 {
		bigdataParams.Add("android_id", tmpMHReq.Device.AndroidID)
	}
	if len(tmpMHReq.Device.AndroidIDMd5) > 0 {
		bigdataParams.Add("android_id_md5", tmpMHReq.Device.AndroidIDMd5)
	}
	if len(tmpMHReq.Device.Oaid) > 0 {
		bigdataParams.Add("oaid", tmpMHReq.Device.Oaid)
	}
	if len(tmpMHReq.Device.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", tmpMHReq.Device.OaidMd5)
	}
	if len(tmpMHReq.Device.Idfa) > 0 {
		bigdataParams.Add("idfa", tmpMHReq.Device.Idfa)
	}
	if len(mhReq.Device.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", mhReq.Device.IdfaMd5)
	}
	if len(tmpMHReq.Device.CAIDMulti) > 0 {
		tmpCaidMultiJson, _ := json.Marshal(tmpMHReq.Device.CAIDMulti)
		bigdataParams.Add("caid_multi", string(tmpCaidMultiJson))
	}
	bigdataParams.Add("model", tmpMHReq.Device.Model)
	bigdataParams.Add("manufacturer", tmpMHReq.Device.Manufacturer)
	bigdataParams.Add("did_md5", didMd5Key)
	bigdataParams.Add("origin_did_md5", originDidMd5Key)
	bigdataParams.Add("floor_price", utils.ConvertIntToString(localFloorPrice))
	bigdataParams.Add("final_price", utils.ConvertIntToString(localFinalPrice))
	bigdataParams.Add("ecpm", utils.ConvertIntToString(ecpm))
	if mhReq.Device.DPI > 0 {
		bigdataParams.Add("dpi", utils.ConvertFloat32ToString(mhReq.Device.DPI))
	}
	if mhReq.Device.XDPI > 0 {
		bigdataParams.Add("xdpi", utils.ConvertFloat32ToString(mhReq.Device.XDPI))
	}
	bigdataParams.Add("supply_ecpm", utils.ConvertIntToString(supplyEcpm))

	// 点击坐标是否服务器生成、sld样式是否服务器生成、是逻辑像素还是物理像素
	if platformPos.PlatformPosIsReplaceXY == 1 {
		bigdataParams.Add("is_server_replace_xy", utils.ConvertIntToString(platformPos.PlatformPosIsReplaceXY))
	} else {
		bigdataParams.Add("is_server_replace_xy", "0")
	}
	if platformPos.PlatformPosIsReportSLD == 1 {
		bigdataParams.Add("is_server_sld", utils.ConvertIntToString(platformPos.PlatformPosIsReportSLD))
	} else {
		bigdataParams.Add("is_server_sld", "0")
	}
	if localPos.LocalAppClickPointType == 2 {
		bigdataParams.Add("is_logic_pixel", "1")
	} else {
		bigdataParams.Add("is_logic_pixel", "0")
	}

	if isServerReplaceXY {
		bigdataParams.Add("is_server_real_replace_xy", "1")
		bigdataParams.Add("server_real_replace_xy_type", utils.ConvertIntToString(serverReplaceXYType))
	} else {
		bigdataParams.Add("is_server_real_replace_xy", "0")
		bigdataParams.Add("server_real_replace_xy_type", "0")
	}

	if len(mhReq.SDKVersion) > 0 {
		bigdataParams.Add("sdk_version", mhReq.SDKVersion)
	}
	bigdataParams.Add("connect_type", utils.ConvertIntToString(tmpMHReq.Network.ConnectType))
	bigdataParams.Add("carrier", utils.ConvertIntToString(tmpMHReq.Network.Carrier))

	bigdataParams.Add("clickpoint_type", utils.ConvertIntToString(localPos.LocalAppClickPointType))
	bigdataParams.Add("clickpoint_replace_type", utils.ConvertIntToString(platformPos.PlatformPosIsReplaceXY))
	bigdataParams.Add("deal_time", utils.ConvertInt64ToString(time.Now().UnixMicro()))

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}
