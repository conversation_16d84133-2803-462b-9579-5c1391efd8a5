package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromGdt ...
func GetFromGdt(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from gdt")
	// platformPos.PlatformAppID = "1109866913"
	// platformPos.PlatformPosID = "1051046795750063"
	// platformPos.PlatformAppBundle = "com.zhihu.android"
	// fmt.Println("get from gdt, local app id: ", localPos.LocalAppID)
	// fmt.Println("get from gdt, local pos id: ", localPos.LocalPosID)
	// fmt.Println("get from gdt, local app type: ", localPos.LocalAppType)
	// fmt.Println("get from gdt, platform app id: ", platformPos.PlatformAppID)
	// fmt.Println("get from gdt, platform pos id: ", platformPos.PlatformPosID)
	// fmt.Println("get from gdt, platform pos type: ", platformPos.PlatformPosType)
	// fmt.Println("get from gdt, platform pos direction: ", platformPos.PlatformPosDirection)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 3.3.5以下的版本，对优量汇rtb和api都直接屏蔽
	if localPos.LocalAppType == "1" {
		if IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 5) {
		} else {
			bigdataExtra.InternalCode = 900001
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////
	// 判定逻辑
	// if strings.Contains(iosReportMainParameter, "idfa") {
	// }
	// if strings.Contains(iosReportMainParameter, "caid") {
	// }
	// if strings.Contains(iosReportMainParameter, "yinzi") {
	// }

	// if xxxxxxOK {
	// } else if len(iosReportSubParameter1) > 0 {
	// 	if strings.Contains(iosReportSubParameter1, "idfa") {
	// 	} else if strings.Contains(iosReportSubParameter1, "caid") {
	// 	} else if strings.Contains(iosReportSubParameter1, "yinzi") {
	// 	}
	// }

	// if xxxxxxOK {
	// } else if len(iosReportSubParameter2) > 0 {
	// 	if strings.Contains(iosReportSubParameter2, "idfa") {
	// 	} else if strings.Contains(iosReportSubParameter2, "caid") {
	// 	} else if strings.Contains(iosReportSubParameter2, "yinzi") {
	// 	}
	// }
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}
	// requestGet, _ := http.NewRequest("GET", "http://localhost:8080/test/gethw", nil)
	// gdtURL := config.UpGdtURL
	// if platformPos.PlatformPosIsDebug == 1 {
	// 	gdtURL = config.UpGdtDebugURL
	// }
	requestGet, _ := http.NewRequestWithContext(c, "GET", platformPos.PlatformAppUpURL, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destConfigUA)
	if platformPos.PlatformAppIsReportUa == 1 {
		requestGet.Header.Add("User-Agent", destConfigUA)
	}

	// 转int64
	plaformPosID, err := strconv.ParseInt(platformPos.PlatformPosID, 10, 64)
	// plaformPosID, err := strconv.Atoi(platformPos.PlatformPosID)
	gdtReqPosMap := map[string]interface{}{}
	gdtReqPosMap["id"] = plaformPosID
	gdtReqPosMap["width"] = platformPos.PlatformPosWidth
	gdtReqPosMap["height"] = platformPos.PlatformPosHeight
	gdtReqPosMap["ad_count"] = mhReq.Pos.AdCount
	gdtReqPosMap["deep_link_version"] = 1
	if platformPos.PlatformAppIsSupportQuickApp == 1 {
		gdtReqPosMap["support_quick_app"] = true
	}
	// gdtReqPosMap["need_rendered_ad"] = false

	// 1 support_pkg; 2 query
	// if platformPos.PlatformPosIsSupportPkgs == 1 {
	// 	if platformPos.PlatformPosSupportPkgsType == 0 {
	// 		if len(mhReq.Pos.PkgWhitelist) > 0 {
	// 			gdtReqPosMap["support_pkg"] = mhReq.Pos.PkgWhitelist
	// 		}
	// 	} else if platformPos.PlatformPosSupportPkgsType == 1 {
	// 		if len(platformPos.PlatformPosSupportPkgs) > 0 {
	// 			gdtReqPosMap["support_pkg"] = strings.Split(platformPos.PlatformPosSupportPkgs, ",")
	// 		}
	// 	}
	// }
	// if platformPos.PlatformPosIsSupportQuerys == 1 {
	// 	if platformPos.PlatformPosSupportQuerysType == 0 {
	// 		if len(mhReq.Pos.Query) > 0 {
	// 			gdtReqPosMap["query"] = mhReq.Pos.Query
	// 		}
	// 	} else if platformPos.PlatformPosSupportQuerysType == 1 {
	// 		if len(platformPos.PlatformPosSupportQuerys) > 0 {
	// 			tmpArray := strings.Split(platformPos.PlatformPosSupportQuerys, ",")
	// 			gdtReqPosMap["query"] = tmpArray[rand.Intn(len(tmpArray))]
	// 		}
	// 	}
	// }

	gdtReqPosjson, err := json.Marshal(gdtReqPosMap)
	if err != nil {
		fmt.Println("wrong pos json")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	gdtReqMediaMap := map[string]interface{}{}
	gdtReqMediaMap["app_id"] = platformPos.PlatformAppID
	gdtReqMediaMap["app_bundle_id"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	gdtReqMediajson, err := json.Marshal(gdtReqMediaMap)
	if err != nil {
		fmt.Println("wrong media json")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	gdtReqNetworkMap := map[string]interface{}{}
	gdtReqNetworkMap["connect_type"] = mhReq.Network.ConnectType
	if mhReq.Network.ConnectType == 7 {
		gdtReqNetworkMap["connect_type"] = 7
	}
	gdtReqNetworkMap["carrier"] = mhReq.Network.Carrier
	if platformPos.PlatformAppIsReportUa == 1 {
		gdtReqNetworkMap["ua"] = destConfigUA
	}

	gdtReqNetworkjson, err := json.Marshal(gdtReqNetworkMap)
	if err != nil {
		fmt.Println("wrong network json")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	gdtReqDeviceMap := map[string]interface{}{}
	gdtReqDeviceMap["os"] = strings.ToLower(mhReq.Device.Os)
	gdtReqDeviceMap["os_version"] = mhReq.Device.OsVersion
	gdtReqDeviceMap["model"] = mhReq.Device.Model
	gdtReqDeviceMap["device_type"] = mhReq.Device.DeviceType

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		gdtReqDeviceMap["manufacturer"] = mhReq.Device.Manufacturer

		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				gdtReqDeviceMap["imei_md5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				gdtReqDeviceMap["imei_md5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				gdtReqDeviceMap["oaid"] = mhReq.Device.Oaid
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					isAndroidDeviceOK = true
				}
				gdtReqDeviceMap["android_id_md5"] = utils.GetMd5(mhReq.Device.AndroidID)
			} else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					isAndroidDeviceOK = true
				}
				gdtReqDeviceMap["android_id_md5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from gdt error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				gdtReqDeviceMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				gdtReqDeviceMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						gdtReqDeviceMap["qaid"] = item.CAID
						gdtReqDeviceMap["qaid_version"] = item.CAIDVersion
						break
					}
				}
			} else {
				var reqDeviceInfoCaidMapArray []map[string]interface{}

				for _, item := range mhReq.Device.CAIDMulti {
					reqDeviceQaidMap := map[string]interface{}{}
					reqDeviceQaidMap["qaid"] = item.CAID
					reqDeviceQaidMap["version"] = item.CAIDVersion

					reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
				}

				if len(reqDeviceInfoCaidMapArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					gdtReqDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
				if tmp1DeviceStartSec > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					gdtReqDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
					gdtReqDeviceMap["country"] = tmpCountry
					gdtReqDeviceMap["language"] = tmpLanguage
					gdtReqDeviceMap["device_name_md5"] = tmpDeviceNameMd5
					gdtReqDeviceMap["hardware_machine"] = tmpHardwareMachine
					gdtReqDeviceMap["hardware_model"] = tmpHardwareModel
					gdtReqDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
					gdtReqDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
					gdtReqDeviceMap["system_update_sec"] = tmpSystemUpdateSec
					gdtReqDeviceMap["time_zone"] = tmpTimeZone
				}
			}

		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					gdtReqDeviceMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					gdtReqDeviceMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							gdtReqDeviceMap["qaid"] = item.CAID
							gdtReqDeviceMap["qaid_version"] = item.CAIDVersion
							break
						}
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceQaidMap := map[string]interface{}{}
						reqDeviceQaidMap["qaid"] = item.CAID
						reqDeviceQaidMap["version"] = item.CAIDVersion

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						gdtReqDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					if tmp1DeviceStartSec > 0 {
						isIosDeviceOK = true
						isIOSToUpReportYinZi = true

						gdtReqDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
						gdtReqDeviceMap["country"] = tmpCountry
						gdtReqDeviceMap["language"] = tmpLanguage
						gdtReqDeviceMap["device_name_md5"] = tmpDeviceNameMd5
						gdtReqDeviceMap["hardware_machine"] = tmpHardwareMachine
						gdtReqDeviceMap["hardware_model"] = tmpHardwareModel
						gdtReqDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
						gdtReqDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
						gdtReqDeviceMap["system_update_sec"] = tmpSystemUpdateSec
						gdtReqDeviceMap["time_zone"] = tmpTimeZone
					}
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					gdtReqDeviceMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					gdtReqDeviceMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							gdtReqDeviceMap["qaid"] = item.CAID
							gdtReqDeviceMap["qaid_version"] = item.CAIDVersion
							break
						}
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceQaidMap := map[string]interface{}{}
						reqDeviceQaidMap["qaid"] = item.CAID
						reqDeviceQaidMap["version"] = item.CAIDVersion

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						gdtReqDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					if tmp1DeviceStartSec > 0 {
						isIosDeviceOK = true
						isIOSToUpReportYinZi = true

						gdtReqDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
						gdtReqDeviceMap["country"] = tmpCountry
						gdtReqDeviceMap["language"] = tmpLanguage
						gdtReqDeviceMap["device_name_md5"] = tmpDeviceNameMd5
						gdtReqDeviceMap["hardware_machine"] = tmpHardwareMachine
						gdtReqDeviceMap["hardware_model"] = tmpHardwareModel
						gdtReqDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
						gdtReqDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
						gdtReqDeviceMap["system_update_sec"] = tmpSystemUpdateSec
						gdtReqDeviceMap["time_zone"] = tmpTimeZone
					}
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from gdt error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// 视频
	if platformPos.PlatformPosType == 11 {
		// 0 横屏, 1 竖屏
		if platformPos.PlatformPosDirection == 0 {
			gdtReqDeviceMap["orientation"] = 90
		} else if platformPos.PlatformPosDirection == 1 {
			gdtReqDeviceMap["orientation"] = 0
		}
	}

	gdtReqDevicejson, err := json.Marshal(gdtReqDeviceMap)
	if err != nil {
		// fmt.Println("wrong device json")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug gdt android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					gdtReplaceDeviceMap := map[string]interface{}{}
					gdtReplaceDeviceMap["os"] = strings.ToLower(mhReq.Device.Os)
					gdtReplaceDeviceMap["os_version"] = didRedisData.OsVersion
					gdtReplaceDeviceMap["model"] = didRedisData.Model
					gdtReplaceDeviceMap["device_type"] = mhReq.Device.DeviceType
					gdtReplaceDeviceMap["manufacturer"] = didRedisData.Manufacturer

					if didRedisData.Version == 1 {
						gdtReqNetworkMap["connect_type"] = didRedisData.ConnectType
						gdtReqNetworkMap["carrier"] = didRedisData.Carrier

						gdtReqNetworkjson, _ = json.Marshal(gdtReqNetworkMap)
					}
					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							gdtReplaceDeviceMap["imei_md5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							gdtReplaceDeviceMap["imei_md5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							gdtReplaceDeviceMap["oaid"] = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								gdtReplaceDeviceMap["android_id_md5"] = utils.GetMd5(didRedisData.AndroidID)
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else if len(didRedisData.AndroidIDMd5) > 0 && didRedisData.AndroidIDMd5 != utils.GetMd5("") {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								gdtReplaceDeviceMap["android_id_md5"] = strings.ToLower(didRedisData.AndroidIDMd5)
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					if platformPos.PlatformPosType == 11 {
						// 0 横屏, 1 竖屏
						if platformPos.PlatformPosDirection == 0 {
							gdtReplaceDeviceMap["orientation"] = 90
						} else if platformPos.PlatformPosDirection == 1 {
							gdtReplaceDeviceMap["orientation"] = 0
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						gdtReqNetworkMap["ua"] = didRedisData.Ua

						requestGet.Header.Del("User-Agent")
						requestGet.Header.Add("User-Agent", didRedisData.Ua)

						replaceUA = didRedisData.Ua

						gdtReqNetworkjson, _ = json.Marshal(gdtReqNetworkMap)

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
					gdtReqDevicejson, _ = json.Marshal(gdtReplaceDeviceMap)
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug gdt ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						gdtReplaceDeviceMap := map[string]interface{}{}
						gdtReplaceDeviceMap["os"] = strings.ToLower(mhReq.Device.Os)
						gdtReplaceDeviceMap["os_version"] = didRedisData.OsVersion
						gdtReplaceDeviceMap["model"] = didRedisData.Model
						gdtReplaceDeviceMap["device_type"] = mhReq.Device.DeviceType
						// gdtReplaceDeviceMap["manufacturer"] = didRedisData.Manufacturer

						if didRedisData.Version == 1 {
							gdtReqNetworkMap["connect_type"] = didRedisData.ConnectType
							gdtReqNetworkMap["carrier"] = didRedisData.Carrier

							gdtReqNetworkjson, _ = json.Marshal(gdtReqNetworkMap)
						}

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								// gdtReplaceDeviceMap["idfa"] = didRedisData.Idfa
								gdtReplaceDeviceMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								gdtReplaceDeviceMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										gdtReplaceDeviceMap["qaid"] = item.CAID
										gdtReplaceDeviceMap["qaid_version"] = item.CAIDVersion
										break
									}
								}
							} else {
								var reqDeviceInfoCaidMapArray []map[string]interface{}

								for _, item := range tmpCAIDMulti {
									reqDeviceQaidMap := map[string]interface{}{}
									reqDeviceQaidMap["qaid"] = item.CAID
									reqDeviceQaidMap["version"] = item.CAIDVersion
									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
								}
								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									gdtReplaceDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
								if tmp1DeviceStartSec > 0 {
									gdtReplaceDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
									gdtReplaceDeviceMap["country"] = tmpCountry
									gdtReplaceDeviceMap["language"] = tmpLanguage
									gdtReplaceDeviceMap["device_name_md5"] = tmpDeviceNameMd5
									gdtReplaceDeviceMap["hardware_machine"] = tmpHardwareMachine
									gdtReplaceDeviceMap["hardware_model"] = tmpHardwareModel
									gdtReplaceDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
									gdtReplaceDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
									gdtReplaceDeviceMap["system_update_sec"] = tmpSystemUpdateSec
									gdtReplaceDeviceMap["time_zone"] = tmpTimeZone

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									// gdtReplaceDeviceMap["idfa"] = didRedisData.Idfa
									gdtReplaceDeviceMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									gdtReplaceDeviceMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											gdtReplaceDeviceMap["qaid"] = item.CAID
											gdtReplaceDeviceMap["qaid_version"] = item.CAIDVersion
											break
										}
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceQaidMap := map[string]interface{}{}
										reqDeviceQaidMap["qaid"] = item.CAID
										reqDeviceQaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
									}
									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										gdtReplaceDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									if tmp1DeviceStartSec > 0 {
										gdtReplaceDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
										gdtReplaceDeviceMap["country"] = tmpCountry
										gdtReplaceDeviceMap["language"] = tmpLanguage
										gdtReplaceDeviceMap["device_name_md5"] = tmpDeviceNameMd5
										gdtReplaceDeviceMap["hardware_machine"] = tmpHardwareMachine
										gdtReplaceDeviceMap["hardware_model"] = tmpHardwareModel
										gdtReplaceDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
										gdtReplaceDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
										gdtReplaceDeviceMap["system_update_sec"] = tmpSystemUpdateSec
										gdtReplaceDeviceMap["time_zone"] = tmpTimeZone

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									// gdtReplaceDeviceMap["idfa"] = didRedisData.Idfa
									gdtReplaceDeviceMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									gdtReplaceDeviceMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											gdtReplaceDeviceMap["qaid"] = item.CAID
											gdtReplaceDeviceMap["qaid_version"] = item.CAIDVersion
											break
										}
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceQaidMap := map[string]interface{}{}
										reqDeviceQaidMap["qaid"] = item.CAID
										reqDeviceQaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceQaidMap)
									}
									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										gdtReplaceDeviceMap["multi_qaid"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									if tmp1DeviceStartSec > 0 {
										gdtReplaceDeviceMap["device_start_sec"] = utils.ConvertIntToString(tmp1DeviceStartSec)
										gdtReplaceDeviceMap["country"] = tmpCountry
										gdtReplaceDeviceMap["language"] = tmpLanguage
										gdtReplaceDeviceMap["device_name_md5"] = tmpDeviceNameMd5
										gdtReplaceDeviceMap["hardware_machine"] = tmpHardwareMachine
										gdtReplaceDeviceMap["hardware_model"] = tmpHardwareModel
										gdtReplaceDeviceMap["physical_memory_byte"] = tmpPhysicalMemoryByte
										gdtReplaceDeviceMap["harddisk_size_byte"] = tmpHarddiskSizeByte
										gdtReplaceDeviceMap["system_update_sec"] = tmpSystemUpdateSec
										gdtReplaceDeviceMap["time_zone"] = tmpTimeZone

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
						if platformPos.PlatformPosType == 11 {
							// 0 横屏, 1 竖屏
							if platformPos.PlatformPosDirection == 0 {
								gdtReplaceDeviceMap["orientation"] = 90
							} else if platformPos.PlatformPosDirection == 1 {
								gdtReplaceDeviceMap["orientation"] = 0
							}
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							gdtReqNetworkMap["ua"] = didRedisData.Ua

							requestGet.Header.Del("User-Agent")
							requestGet.Header.Add("User-Agent", didRedisData.Ua)

							replaceUA = didRedisData.Ua

							gdtReqNetworkjson, _ = json.Marshal(gdtReqNetworkMap)

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
						gdtReqDevicejson, _ = json.Marshal(gdtReplaceDeviceMap)
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from gdt error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from gdt error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)

		// debug
		// if platformPos.PlatformAppID == "1109920434" {
		// 	models.BigDataDebugReq(c, bigdataUID, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
		// }
	}()

	q := requestGet.URL.Query()
	q.Add("api_version", "3.17")
	q.Add("support_https", "1")
	if platformPos.PlatformAppIsSupportAppStore == 1 {
		q.Add("support_app_store", "1")
	}
	q.Add("pos", string(gdtReqPosjson))
	q.Add("media", string(gdtReqMediajson))
	q.Add("device", string(gdtReqDevicejson))
	q.Add("network", string(gdtReqNetworkjson))

	if len(platformPos.PlatformAppKey) > 0 {
		tmpTimeStamp := utils.GetCurrentMilliSecond()
		signData := "device=" + string(gdtReqDevicejson) +
			"&" + "media=" + string(gdtReqMediajson) +
			"&" + "network=" + string(gdtReqNetworkjson) +
			"&" + "pos=" + string(gdtReqPosjson) +
			"&" + "time_stamp=" + utils.ConvertInt64ToString(tmpTimeStamp)

		// fmt.Println("kbg_debug sign_data:", signData)
		signResult := utils.HmacSha256(signData, platformPos.PlatformAppKey)
		// fmt.Println("kbg_debug sign_result:", signResult)

		q.Add("time_stamp", utils.ConvertInt64ToString(tmpTimeStamp))
		q.Add("sign", signResult)
	}
	if platformPos.PlatformPosIsTMax == 1 {
		q.Add("tmax", utils.ConvertIntToString(utils.ConvertStringToInt(platformPos.PlatformPosTMaxValue)))
	} else {
		q.Add("tmax", utils.ConvertIntToString(0))
	}

	requestGet.URL.RawQuery = q.Encode()

	// fmt.Println("gdt req: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, requestGet.URL.String())
	// if mhReq.Device.Os == "ios" {
	// 	fmt.Println("kbg_debug_gdt 1: ", platformPos.PlatformAppID, iosReportMainParameter, iosReportSubParameter1, iosReportSubParameter2)
	// 	fmt.Println("kbg_debug_gdt 2: ", isIOSToUpReportIDFA, isIOSToUpReportCAID, isIOSToUpReportYinZi)
	// 	fmt.Println("kbg_debug_gdt req: ", requestGet.URL.String())
	// }

	if true {
		if isHaveReplace {
			go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 1, string(requestGet.URL.String()), bigdataReplaceDIDData,
				mhReq)
		} else {
			go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 0, string(requestGet.URL.String()), bigdataReplaceDIDData,
				mhReq)
		}
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)

	// if platformPos.PlatformPosID == "5151562643646953" || platformPos.PlatformPosID == "5181067633540900" {
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&gdt_resp", string(bodyContent), "10807", "60180", "fl_ios_xs", "1870100034")
	// }

	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println("gdt resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	gdtRespStu := GdtRespStu{}
	json.Unmarshal([]byte(bodyContent), &gdtRespStu)
	// fmt.Println(gdtRespStu.Ret)
	// fmt.Println(gdtRespStu.Msg)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// if gdtRespStu.Ret == 107066 || gdtRespStu.Ret == 107067 {
	// if platformPos.PlatformAppID == "1109840738" ||
	// 	platformPos.PlatformAppID == "1200680949" ||
	// 	platformPos.PlatformAppID == "1200677854" {
	// 	if gdtRespStu.Ret != 0 {
	// 		go func() {
	// 			defer func() {
	// 				if err := recover(); err != nil {
	// 					fmt.Println("bigdata debug up panic:", err)
	// 				}
	// 			}()
	// 			models.DebugUpToBigData2(c, bigdataUID, mhReq, localPos, platformPos, tmpCAID, tmpCAIDVersion, gdtRespStu.Ret, requestGet.URL.String(), string(bodyContent))
	// 		}()
	// 	}
	// }

	if gdtRespStu.Ret != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = gdtRespStu.Ret

		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = gdtRespStu.Ret
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	gdtRespDataListItemStu := gdtRespStu.Data[platformPos.PlatformPosID].List

	if len(gdtRespDataListItemStu) > 1 {
		sort.Sort(GdtEcpmSort(gdtRespDataListItemStu))
	}

	for _, item := range gdtRespDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		gdtEcpm := item.Ecpm

		respTmpPrice = respTmpPrice + gdtEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if gdtEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			gdtEcpm = platformPos.PlatformPosEcpm
		}

		gdtLossNoticeURL := getGDTPriceFailedURL(item.LossNoticeURL, platformPos, gdtEcpm)
		// fmt.Println("gdt_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(gdtEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > gdtEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
			continue
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(gdtEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(item.Title) > 0 {
			respListItemMap["title"] = item.Title
		}

		// description
		if len(item.Description) > 0 {
			respListItemMap["description"] = item.Description
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				if IsClikPointLibInWhite(c, localPos, platformPos) {
				} else {
					redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
					// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900101
						bigdataExtra.ExternalCode = 102006

						curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
						return MhUpErrorRespMap("", bigdataExtra)
					} else {

						tmpDownX = utils.ConvertIntToString(redisDownX)
						tmpDownY = utils.ConvertIntToString(redisDownY)
						tmpUpX = utils.ConvertIntToString(redisUpX)
						tmpUpY = utils.ConvertIntToString(redisUpY)

						if redisClickXYType == "0" { // 物理像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 0
						} else if redisClickXYType == "1" { // 逻辑像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 1
						}
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// click_link gdt
		gdtClickLink := item.ClickLink
		gdtClickLink = strings.Replace(gdtClickLink, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
		gdtClickLink = strings.Replace(gdtClickLink, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				gdtClickLink = strings.Replace(gdtClickLink, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
				gdtClickLink = strings.Replace(gdtClickLink, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
			} else {
				gdtClickLink = strings.Replace(gdtClickLink, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
				gdtClickLink = strings.Replace(gdtClickLink, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
			}
		} else {
			gdtClickLink = strings.Replace(gdtClickLink, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			gdtClickLink = strings.Replace(gdtClickLink, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
		}
		if platformPos.PlatformPosIsReportSLD == 1 {
			gdtClickLink = strings.Replace(gdtClickLink, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
		}

		gdtClickLink = strings.Replace(gdtClickLink, "__DOWN_X__", tmpDownX, -1)
		gdtClickLink = strings.Replace(gdtClickLink, "__DOWN_Y__", tmpDownY, -1)
		gdtClickLink = strings.Replace(gdtClickLink, "__UP_X__", tmpUpX, -1)
		gdtClickLink = strings.Replace(gdtClickLink, "__UP_Y__", tmpUpY, -1)

		if mhReq.Device.Os == "android" {
			if len(item.PackageURL) > 0 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.PackageURL
				respListItemMap["download_url"] = item.PackageURL
				if len(item.LandingPageURL) > 0 {
					respListItemMap["landpage_url"] = item.LandingPageURL
				}
			} else if len(item.LandingPageURL) > 0 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.LandingPageURL
				respListItemMap["landpage_url"] = item.LandingPageURL
			} else {

				curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
				continue
			}

			// gdt五要素
			// if len(item.AdvertiserName) > 0 && len(item.AppVersion) > 0 {
			// 	respListItemMap["publisher"] = item.AdvertiserName
			// 	respListItemMap["app_version"] = item.AppVersion
			// 	if len(item.AppInfo) > 0 {
			// 		respListItemMap["privacy_url"] = item.AppInfo
			// 	} else {
			// 		respListItemMap["privacy_url"] = "https://static.maplehaze.cn/static/privacy_ad.html"
			// 	}
			// 	respListItemMap["permission"] = "允许程序访问电话状态\n允许程序通过GPS芯片接收卫星的定位信息\n允许程序可以读取设备外部存储空间\n允许程序更改系统设置\n允许程序通过WiFi或移动基站的方式获取用户粗略位置信息\n允许程序写入外部存储,如SD卡上写文件\n"
			// 	respListItemMap["package_size"] = (200 + rand.Intn(800)) * 1024 * 1024
			// }
			if len(item.AppName) > 0 {
				respListItemMap["app_name"] = item.AppName
			} else {
			}
			if len(item.AdvertiserName) > 0 {
				respListItemMap["publisher"] = item.AdvertiserName
			}
			if len(item.AppVersion) > 0 {
				respListItemMap["app_version"] = item.AppVersion
			}
			if len(item.PrivacyURL) > 0 {
				respListItemMap["privacy_url"] = item.PrivacyURL
			}
			if len(item.PermissionsURL) > 0 {
				respListItemMap["permission_url"] = item.PermissionsURL
			}
		} else if mhReq.Device.Os == "ios" {
			if item.AppleID > 0 {
				respListItemMap["ad_url"] = "https://apps.apple.com/cn/app/id" + utils.ConvertIntToString(item.AppleID)
				respListItemMap["interact_type"] = 1
				respListItemMap["download_url"] = "https://apps.apple.com/cn/app/id" + utils.ConvertIntToString(item.AppleID)
			} else {
				if len(item.LandingPageURL) > 0 {
					if strings.Contains(item.LandingPageURL, "apple.com") {
						respListItemMap["ad_url"] = item.LandingPageURL
						respListItemMap["interact_type"] = 1
						respListItemMap["download_url"] = item.LandingPageURL
					} else {
						respListItemMap["ad_url"] = item.LandingPageURL
						respListItemMap["interact_type"] = 0
						respListItemMap["landpage_url"] = item.LandingPageURL
					}
				} else {
					curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
					continue
				}
			}
		}

		if len(item.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, item.VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
					continue
				}

				respListVideoItemMap["duration"] = item.VideoDuration * 1000
			}
			respListVideoItemMap["width"] = item.VideoWidth
			respListVideoItemMap["height"] = item.VideoHeight
			respListVideoItemMap["video_url"] = item.VideoURL

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, item.VideoWidth, item.VideoHeight)
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
				continue
			}

			// cover_url
			if len(item.ImageURL) > 0 {
				respListVideoItemMap["cover_url"] = item.ImageURL
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track
			var respListEventTrackURLMap []map[string]interface{}

			videoConvURL := item.VideoConvLink
			videoConvURL = strings.Replace(videoConvURL, "__VIDEO_TIME__", utils.ConvertIntToString(item.VideoDuration), -1)
			videoConvURL = strings.Replace(videoConvURL, "__BEGIN_TIME__", "0", -1)
			videoConvURL = strings.Replace(videoConvURL, "__END_TIME__", utils.ConvertIntToString(item.VideoDuration), -1) // ???
			videoConvURL = strings.Replace(videoConvURL, "__PLAY_FIRST_FRAME__", "1", -1)
			videoConvURL = strings.Replace(videoConvURL, "__PLAY_LAST_FRAME__", "1", -1) // ???
			// 0 横屏, 1 竖屏
			if platformPos.PlatformPosDirection == 0 {
				videoConvURL = strings.Replace(videoConvURL, "__SCENE__", "4", -1)
			} else if platformPos.PlatformPosDirection == 1 {
				videoConvURL = strings.Replace(videoConvURL, "__SCENE__", "2", -1)
			}
			videoConvURL = strings.Replace(videoConvURL, "__TYPE__", "1", -1)
			videoConvURL = strings.Replace(videoConvURL, "__BEHAVIOR__", "1", -1)
			videoConvURL = strings.Replace(videoConvURL, "__STATUS__", "0", -1)

			// respListVideoBeginEventTrackMap := map[string]interface{}{}
			// respListVideoBeginEventTrackMap["event_type"] = 100
			// var respListVideoBeginEventTrackURLMap []string
			// respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoConvURL)
			// respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			// respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoConvURL)
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)

			respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(item.ImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = item.ImageURL
				respListImageItemMap["width"] = item.ImageWidth
				respListImageItemMap["height"] = item.ImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, item.ImageWidth, item.ImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		if len(item.CrtID) > 0 {
			respListItemMap["crid"] = item.CrtID
		}

		// deeplink
		destDeepLink := ""
		if len(item.DeepLink) > 0 || len(item.MarketURL) > 0 || len(item.UniversalLink) > 0 {
			if len(item.UniversalLink) > 0 {
				destDeepLink = item.UniversalLink

				respListItemMap["deep_link"] = item.UniversalLink
			} else if len(item.DeepLink) > 0 {
				destDeepLink = item.DeepLink

				respListItemMap["deep_link"] = item.DeepLink
			} else if len(item.QuickAppLink) > 0 {
				destDeepLink = item.QuickAppLink

				respListItemMap["deep_link"] = item.QuickAppLink
			} else if len(item.MarketURL) > 0 {
				destDeepLink = item.MarketURL

				respListItemMap["deep_link"] = item.MarketURL
			}

			if len(item.MarketURL) > 0 {
				respListItemMap["market_url"] = item.MarketURL
			}
			if len(item.QuickAppLink) > 0 {
				respListItemMap["quick_app_link"] = item.QuickAppLink
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			if len(item.ConversionEvent.DeepLinkSuccess) > 0 {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, item.ConversionEvent.DeepLinkSuccess)
			}
			// if len(item.ConversionLink) > 0 {
			// 	tmpConversionLink := strings.Replace(item.ConversionLink, "__ACTION_ID__", "246", -1)
			// 	respListItemDeepLinkArray = append(respListItemDeepLinkArray, tmpConversionLink)
			// }

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			// if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			// 	var respListItemDeepLinkFailedArray []string

			// 	if len(item.ConversionLink) > 0 {
			// 		tmpConversionLink := strings.Replace(item.ConversionLink, "__ACTION_ID__", "249", -1)
			// 		respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, tmpConversionLink)
			// 	}

			// 	mhDPFailedParams := url.Values{}
			// 	mhDPFailedParams.Add("result", "1")
			// 	mhDPFailedParams.Add("reason", "3")
			// 	mhDPFailedParams.Add("log", bigdataParams)

			// 	respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			// 	respListItemConv11Map := map[string]interface{}{}
			// 	respListItemConv11Map["conv_type"] = 11
			// 	respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

			// 	respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			// }

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(item.PackageName) > 0 {
			respListItemMap["package_name"] = item.PackageName

			destPackageName = item.PackageName
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		if strings.Contains(destDeepLink, "weixin://") {
			respListItemMap["package_name"] = "com.tencent.mm"
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, item.AppName, destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// gdt返回appinfo_url, 传给媒体
		if len(item.AppInfoURL) > 0 {
			respListItemMap["appinfo_url"] = item.AppInfoURL
		}

		// icon_url
		if len(item.IconURL) > 0 {
			respListItemMap["icon_url"] = item.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, gdtEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		gdtWinNoticeURL := getGDTPriceOKURL(item.WinNoticeURL, platformPos, gdtEcpm)
		if len(gdtWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, gdtWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link gdt
		respListItemImpArray = append(respListItemImpArray, item.ImpressionLink)

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string

		if IsAdIDReplaceXYByOS(c, mhReq) {
			if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
				gdtClickLink = gdtClickLink + "&is_adid_replace_xy=0"
			}
			if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
				gdtClickLink = gdtClickLink + "&replace_adid_sim_xy_default=1"
				respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
			}
		}
		respListItemClkArray = append(respListItemClkArray, gdtClickLink)

		// deeplink invoke 加到点击上报中
		if len(item.ConversionEvent.DeepLinkInvoke) > 0 {
			tmpDeepLinkInvoke := item.ConversionEvent.DeepLinkInvoke
			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					tmpDeepLinkInvoke = tmpDeepLinkInvoke + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					tmpDeepLinkInvoke = tmpDeepLinkInvoke + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, tmpDeepLinkInvoke)
		}

		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(gdtLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, gdtLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlGDTPriceFailedURL(gdtLossNoticeURL, &bigdataExtra)
			continue
		}

		// 上报竞价失败
		if len(gdtLossNoticeURL) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, gdtLossNoticeURL)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = gdtEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("gdt resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// gdt resp
	respGdt := models.MHUpResp{}
	respGdt.RespData = &mhResp
	respGdt.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respGdt
}

// curl price ok
func getGDTPriceOKURL(winNoticeURL string, platformPos *models.PlatformPosStu, gdtEcpm int) string {
	if len(winNoticeURL) > 0 && gdtEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && platformPos.PlatformAppIsReportWin == 1 {
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}

		macroPrice := utils.ConvertIntToString(int(gdtEcpm * randPRValue / 100))

		cipherText := utils.RSAEncrypt([]byte(macroPrice), config.GDTPublicKeyPemPath)
		macroPrice = utils.Base64URLEncode(cipherText)

		tmpWinNoticeURL := winNoticeURL
		tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "__AUCTION_PRICE__", macroPrice, -1)

		return tmpWinNoticeURL
	}

	return ""
}

// curl price failed
func curlGDTPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("gdt loss url panic:", err)
				}
			}()

			curlGDTNurl(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

// curl price failed
func getGDTPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, gdtEcpm int) string {
	if gdtEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && platformPos.PlatformAppIsReportLoss == 1 {
		randPRValue := 100
		tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
		tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
		if tmp1 <= tmp2 {
			randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
		} else {
			return ""
		}

		macroPrice := utils.ConvertIntToString(int(gdtEcpm * randPRValue / 100))

		cipherText := utils.RSAEncrypt([]byte(macroPrice), config.GDTPublicKeyPemPath)
		macroPrice = utils.Base64URLEncode(cipherText)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_PRICE__", macroPrice, -1)
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_LOSS__", "1", -1)

		return tmpLossNoticeURL
	}

	return ""
}

func curlGDTNurl(nurl string) string {

	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println(requestGet.URL.String())
	// fmt.Println("gdt nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return ""
	}

	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)
	// fmt.Println("gdt nurl  resp: " + string(bodyContent))
	return string(bodyContent)
}

type GdtEcpmSort []GdtRespDataListItemStu

func (s GdtEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s GdtEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s GdtEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}

// GdtRespStu ...
type GdtRespStu struct {
	Ret  int                    `json:"ret"`
	Msg  string                 `json:"msg"`
	Data map[string]GdtDataItem `json:"data"`
}

// GdtDataItem ...
type GdtDataItem struct {
	List []GdtRespDataListItemStu `json:"list"`
}

// GdtRespDataListItemStu ...
type GdtRespDataListItemStu struct {
	AdID           string `json:"ad_id"`
	ImpressionLink string `json:"impression_link"`
	ClickLink      string `json:"click_link"`
	// ConversionLink string `json:"conversion_link"`
	InteractType   int    `json:"interact_type"`
	CrtType        int    `json:"crt_type"`
	Title          string `json:"title"`
	Description    string `json:"description"`
	ImageURL       string `json:"img_url"`
	IconURL        string `json:"img2_url"`
	PackageName    string `json:"package_name"` // android
	PackageURL     string `json:"package_url"`  // android
	RelationTarget string `json:"relation_target"`
	ImageHeight    int    `json:"img_height"`
	ImageWidth     int    `json:"img_width"`
	CrtID          string `json:"crt_id"`
	TraceID        string `json:"trace_id"`
	AppleID        int    `json:"app_id"` // ios
	VideoURL       string `json:"video_url"`
	VideoDuration  int    `json:"video_duration"`
	VideoFileSize  int    `json:"video_file_size"`
	VideoWidth     int    `json:"video_width"`
	VideoHeight    int    `json:"video_height"`
	VideoConvLink  string `json:"video_view_link"`
	DeepLink       string `json:"customized_invoke_url"`
	MarketURL      string `json:"market_url"`       // 厂商应用商店
	UniversalLink  string `json:"universal_link"`   // iOS Universal Link
	QuickAppLink   string `json:"quick_app_link"`   // 快应用url
	Ecpm           int    `json:"ecpm"`             // ecpm
	EcpmLevel      string `json:"ecpm_level"`       // 表示本条广告实时的eCMP价格层级标签，每个层级标签对应线下约定的eCPM价格范围，成功返回值为String类型数字或字母，类型为String，具体含义线下约定对齐
	LandingPageURL string `json:"landing_page_url"` // landingpage
	AdvertiserName string `json:"advertiser_name"`  // advertiser name
	AppInfoURL     string `json:"app_info_url"`     // app info
	AppName        string `json:"app_name"`         // app name
	AppVersion     string `json:"app_version"`      // app version
	WinNoticeURL   string `json:"win_notice_url"`   // win notice url
	LossNoticeURL  string `json:"loss_notice_url"`  // loss notice url
	// conversion_event
	ConversionEvent GdtRespDataListItemConversionEventStu `json:"conversion_event"`

	// 权限, 隐私
	PermissionsURL string `json:"permissions_url"`   // permission url
	PrivacyURL     string `json:"privacy_agreement"` // privacy url
}

// GdtRespDataListItemConversionEventStu ...
type GdtRespDataListItemConversionEventStu struct {
	DeepLinkInvoke  string `json:"deeplink_invoke"`  // 尝试调起deeplink
	DeepLinkSuccess string `json:"deeplink_success"` // deeplink调起成功
}
