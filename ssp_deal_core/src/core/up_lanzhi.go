package core

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromupLanzhi 蓝智
func GetFromupLanzhi(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var access string
	switch mhReq.Network.ConnectType {
	case 1:
		access = "Wi-Fi"
	case 2:
		access = "2G"
	case 3:
		access = "3G"
	case 4, 5, 6:
		access = "4G"
	case 7:
		access = "5G"
	default:
		access = "Unknown"
	}

	var carrier string
	switch mhReq.Network.Carrier {
	case 1:
		carrier = "46000"
	case 2:
		access = "46003"
	case 3:
		access = "46001"
	default:
		carrier = "Unknown"
	}

	var slotScene string
	switch platformPos.PlatformPosType {
	case 1:
		slotScene = "1002"
	case 2:
		slotScene = "1000"
	case 3:
		slotScene = "1001"
	case 4:
		slotScene = "1004"
	case 11:
		slotScene = "1003"
	default:
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(platformPos.PlatformPosStyleIDs) == 0 {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	var slotStyleArray []*LanzhiRequestAdSlotInfoSlotStyleObject
	posStyleIdArray := strings.Split(platformPos.PlatformPosStyleIDs, ",")
	for _, styleId := range posStyleIdArray {
		slotStyleItem := LanzhiRequestAdSlotInfoSlotStyleObject{
			Style:  styleId,
			Width:  platformPos.PlatformPosWidth,
			Height: platformPos.PlatformPosHeight,
		}
		slotStyleArray = append(slotStyleArray, &slotStyleItem)
	}

	request := LanzhiRequestObject{
		RequestID: bigdataUID,
		Account: &LanzhiRequestAccountObject{
			UserID:   "1081",
			UserName: "枫岚",
			UserKey:  "1072a37de33eb8b402aa99991fa8cf53",
		},
		AdAppInfo: &LanzhiRequestAdAppInfoObject{
			AppName:         platformPos.PlatformAppName,
			PkgName:         GetAppBundleByConfig(c, mhReq, localPos, platformPos),
			PkgVer:          platformPos.PlatformAppVersion,
			InteractionType: []int32{1, 2, 3},
		},
		AdDeviceInfo: &LanzhiRequestAdDeviceInfoObject{
			Width:    mhReq.Device.ScreenWidth,
			Height:   mhReq.Device.ScreenHeight,
			ClientIP: mhReq.Device.IP,
			Os:       strings.ToLower(mhReq.Device.Os),
			Access:   access,
			Osv:      mhReq.Device.OsVersion,
			Carrier:  carrier,
			Brand:    mhReq.Device.Manufacturer,
			Device:   mhReq.Device.Model,
		},
		AdSlotInfo: &LanzhiRequestAdSlotInfoObject{
			AdCount:    mhReq.Pos.AdCount,
			Attribute:  0,
			SlotID:     platformPos.PlatformPosID,
			SlotScene:  slotScene,
			FloorPrice: strconv.Itoa(categoryInfo.FloorPrice),
			SlotStyle:  slotStyleArray,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.AdDeviceInfo.Ua = destConfigUA
	}

	var deviceIdArray []*LanzhiRequestDeviceIdsObject

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				var imeiDeviceId LanzhiRequestDeviceIdsObject
				imeiDeviceId.DeviceID = mhReq.Device.Imei
				imeiDeviceId.DeviceIDType = "IMEI"

				deviceIdArray = append(deviceIdArray, &imeiDeviceId)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				var oaidDeviceId LanzhiRequestDeviceIdsObject
				oaidDeviceId.DeviceID = mhReq.Device.Oaid
				oaidDeviceId.DeviceIDType = "OAID"

				deviceIdArray = append(deviceIdArray, &oaidDeviceId)

				if len(mhReq.Device.AndroidID) > 0 {
					var AndroidIdDeviceId LanzhiRequestDeviceIdsObject
					AndroidIdDeviceId.DeviceID = mhReq.Device.AndroidID
					AndroidIdDeviceId.DeviceIDType = "ANDROID_ID"

					deviceIdArray = append(deviceIdArray, &AndroidIdDeviceId)
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				var idfaDeviceId LanzhiRequestDeviceIdsObject
				idfaDeviceId.DeviceID = mhReq.Device.Idfa
				idfaDeviceId.DeviceIDType = "IDFA"

				deviceIdArray = append(deviceIdArray, &idfaDeviceId)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						var caidDeviceId LanzhiRequestDeviceIdsObject
						caidDeviceId.DeviceID = item.CAID
						caidDeviceId.DeviceIDType = "CAID"

						deviceIdArray = append(deviceIdArray, &caidDeviceId)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			} else {
				for _, item := range mhReq.Device.CAIDMulti {
					var caidDeviceId LanzhiRequestDeviceIdsObject
					caidDeviceId.DeviceID = item.CAID
					caidDeviceId.DeviceIDType = "CAID"

					deviceIdArray = append(deviceIdArray, &caidDeviceId)

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					break
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					var idfaDeviceId LanzhiRequestDeviceIdsObject
					idfaDeviceId.DeviceID = mhReq.Device.Idfa
					idfaDeviceId.DeviceIDType = "IDFA"

					deviceIdArray = append(deviceIdArray, &idfaDeviceId)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidDeviceId LanzhiRequestDeviceIdsObject
							caidDeviceId.DeviceID = item.CAID
							caidDeviceId.DeviceIDType = "CAID"

							deviceIdArray = append(deviceIdArray, &caidDeviceId)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						var caidDeviceId LanzhiRequestDeviceIdsObject
						caidDeviceId.DeviceID = item.CAID
						caidDeviceId.DeviceIDType = "CAID"

						deviceIdArray = append(deviceIdArray, &caidDeviceId)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					var idfaDeviceId LanzhiRequestDeviceIdsObject
					idfaDeviceId.DeviceID = mhReq.Device.Idfa
					idfaDeviceId.DeviceIDType = "IDFA"
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidDeviceId LanzhiRequestDeviceIdsObject
							caidDeviceId.DeviceID = item.CAID
							caidDeviceId.DeviceIDType = "CAID"

							deviceIdArray = append(deviceIdArray, &caidDeviceId)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						var caidDeviceId LanzhiRequestDeviceIdsObject
						caidDeviceId.DeviceID = item.CAID
						caidDeviceId.DeviceIDType = "CAID"

						deviceIdArray = append(deviceIdArray, &caidDeviceId)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	request.AdDeviceInfo.DeviceIds = deviceIdArray

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		var didDeviceIdArray []*LanzhiRequestDeviceIdsObject
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					request.AdDeviceInfo.Os = strings.ToLower(mhReq.Device.Os)
					request.AdDeviceInfo.Osv = didRedisData.OsVersion
					request.AdDeviceInfo.Device = didRedisData.Model
					request.AdDeviceInfo.Brand = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							var imeiDeviceId LanzhiRequestDeviceIdsObject
							imeiDeviceId.DeviceID = didRedisData.Imei
							imeiDeviceId.DeviceIDType = "IMEI"

							didDeviceIdArray = append(didDeviceIdArray, &imeiDeviceId)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							var oaidDeviceId LanzhiRequestDeviceIdsObject
							oaidDeviceId.DeviceID = didRedisData.Oaid
							oaidDeviceId.DeviceIDType = "OAID"

							didDeviceIdArray = append(didDeviceIdArray, &oaidDeviceId)

							if len(didRedisData.AndroidID) > 0 {
								var AndroidIdDeviceId LanzhiRequestDeviceIdsObject
								AndroidIdDeviceId.DeviceID = didRedisData.AndroidID
								AndroidIdDeviceId.DeviceIDType = "ANDROID_ID"

								didDeviceIdArray = append(didDeviceIdArray, &AndroidIdDeviceId)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.AdDeviceInfo.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						request.AdDeviceInfo.Os = strings.ToLower(mhReq.Device.Os)
						request.AdDeviceInfo.Osv = didRedisData.OsVersion
						request.AdDeviceInfo.Device = didRedisData.Model
						request.AdDeviceInfo.Brand = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								var idfaDeviceId LanzhiRequestDeviceIdsObject
								idfaDeviceId.DeviceID = didRedisData.Idfa
								idfaDeviceId.DeviceIDType = "IDFA"

								didDeviceIdArray = append(didDeviceIdArray, &idfaDeviceId)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidDeviceId LanzhiRequestDeviceIdsObject
										caidDeviceId.DeviceID = item.CAID
										caidDeviceId.DeviceIDType = "CAID"

										didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									var caidDeviceId LanzhiRequestDeviceIdsObject
									caidDeviceId.DeviceID = item.CAID
									caidDeviceId.DeviceIDType = "CAID"

									didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									break
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									var idfaDeviceId LanzhiRequestDeviceIdsObject
									idfaDeviceId.DeviceID = didRedisData.Idfa
									idfaDeviceId.DeviceIDType = "IDFA"

									didDeviceIdArray = append(didDeviceIdArray, &idfaDeviceId)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidDeviceId LanzhiRequestDeviceIdsObject
											caidDeviceId.DeviceID = item.CAID
											caidDeviceId.DeviceIDType = "CAID"

											didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										var caidDeviceId LanzhiRequestDeviceIdsObject
										caidDeviceId.DeviceID = item.CAID
										caidDeviceId.DeviceIDType = "CAID"

										didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									var idfaDeviceId LanzhiRequestDeviceIdsObject
									idfaDeviceId.DeviceID = didRedisData.Idfa
									idfaDeviceId.DeviceIDType = "IDFA"

									didDeviceIdArray = append(didDeviceIdArray, &idfaDeviceId)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidDeviceId LanzhiRequestDeviceIdsObject
											caidDeviceId.DeviceID = item.CAID
											caidDeviceId.DeviceIDType = "CAID"

											didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										var caidDeviceId LanzhiRequestDeviceIdsObject
										caidDeviceId.DeviceID = item.CAID
										caidDeviceId.DeviceIDType = "CAID"

										didDeviceIdArray = append(didDeviceIdArray, &caidDeviceId)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.AdDeviceInfo.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5

		request.AdDeviceInfo.DeviceIds = didDeviceIdArray
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}
	requestMarshal, _ := json.Marshal(request)

	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(requestMarshal)
	if err != nil {
		_ = gzipWriter.Close()
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	if err = gzipWriter.Close(); err != nil {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	write := buf.Bytes()
	requestPost, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(write))
	requestPost.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestPost.Header.Add("Connection", "keep-alive")
	requestPost.Header.Add("Accept-Encoding", "gzip,deflate,br")
	requestPost.Header.Add("Content-Encoding", "gzip")
	if platformPos.PlatformAppIsReportUa == 1 {
		requestPost.Header.Add("User-Agent", request.AdDeviceInfo.Ua)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}

	resp, err := client.Do(requestPost)
	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	bufer := bytes.NewBuffer(bodyContent)
	reader, err := gzip.NewReader(bufer)
	if err != nil {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	defer func(reader *gzip.Reader) {
		err = reader.Close()
		if err != nil {
			fmt.Println(err.Error())
		}
	}(reader)
	bodyContent, _ = io.ReadAll(reader)

	respStu := LanzhiResponseObject{}
	_ = json.Unmarshal(bodyContent, &respStu)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if respStu.Code != "0" {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006

		codeNum, _ := strconv.Atoi(respStu.Code)

		bigdataExtra.UpRespCode = codeNum

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if respStu.AdSlots == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	for _, bidItem := range respStu.AdSlots {
		respTmpRespAllNum = respTmpRespAllNum + 1

		if bidItem.Content == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
				go curlLanzhiURL(bidItem.LoseURL, 0, bigdataUID)
			}

			continue
		}

		// ecpm
		price := bidItem.Content.BidPrice

		respTmpPrice = respTmpPrice + price

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
			price = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > price {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
					go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
				}

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
				go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
			}

			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			item.Ecpm = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			item.Ecpm = localPos.LocalPosEcpm
		}

		maplehazeAdId := uuid.NewV4().String()

		randPRValue := 95 + rand.Intn(4)
		macroPrice := utils.ConvertIntToString(price * randPRValue / 100)

		switch bidItem.Action {
		case 0:
			item.InteractType = 0
			item.LandpageURL = bidItem.PageURL

		case 1:
			item.InteractType = 1
			item.DownloadURL = bidItem.DownloadUrl
		default:
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
				go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
			}

			continue
		}

		if len(bidItem.Content.Scheme) > 0 {
			item.DeepLink = bidItem.Content.Scheme
		}
		if mhReq.Device.Os == "ios" && len(bidItem.Content.IosUlk) > 0 {
			item.DeepLink = bidItem.Content.IosUlk
		}

		// adid
		item.AdID = maplehazeAdId
		item.ReqWidth = platformPos.PlatformPosWidth
		item.ReqHeight = platformPos.PlatformPosHeight
		item.Crid = bidItem.Content.CreativeID
		item.MarketURL = bidItem.AppStoreUrl

		if len(bidItem.Content.AppName) > 0 {
			item.AppName = bidItem.Content.AppName
		}
		if len(bidItem.Content.PackageName) > 0 {
			item.PackageName = bidItem.Content.PackageName
		}
		if len(bidItem.Content.Version) > 0 {
			item.AppVersion = bidItem.Content.Version
		}
		if len(bidItem.Content.Developer) > 0 {
			item.Publisher = bidItem.Content.Developer
		}
		if len(bidItem.Content.Privacy) > 0 {
			item.PrivacyLink = bidItem.Content.Privacy
		}
		if len(bidItem.Content.Permission) > 0 {
			item.PermissionURL = bidItem.Content.Permission
		}
		if len(bidItem.Content.AppDesc) > 0 {
			item.AppInfoURL = bidItem.Content.AppDesc
		}
		if len(bidItem.Content.FileSize) > 0 {
			packageSize, _ := strconv.ParseInt(bidItem.Content.FileSize, 10, 64)
			item.PackageSize = packageSize
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
						go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
					}

					continue
				} else {
					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		// impression_link
		var respListItemImpArray []string
		mhImpParams := url.Values{}
		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		// click_link
		var respListItemClickArray []string
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)
		respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		if len(bidItem.ClickURL) > 0 && bidItem.ClickURL != nil {
			for _, clkUrl := range bidItem.ClickURL {
				clkUrl = strings.Replace(clkUrl, "%%price%%", macroPrice, -1)
				clkUrl = strings.Replace(clkUrl, "%%request_id%%", bigdataUID, -1)
				clkUrl = strings.Replace(clkUrl, "%%DOWN_X%%", tmpDownX, -1)
				clkUrl = strings.Replace(clkUrl, "%%DOWN_Y%%", tmpDownY, -1)
				clkUrl = strings.Replace(clkUrl, "%%UP_X%%", tmpUpX, -1)
				clkUrl = strings.Replace(clkUrl, "%%UP_Y%%", tmpUpY, -1)

				respListItemClickArray = append(respListItemClickArray, clkUrl)
			}
		}

		if len(bidItem.ShowURL) > 0 && bidItem.ShowURL != nil {
			for _, impUrl := range bidItem.ShowURL {
				impUrl = strings.Replace(impUrl, "%%price%%", macroPrice, -1)
				impUrl = strings.Replace(impUrl, "%%request_id%%", bigdataUID, -1)

				respListItemImpArray = append(respListItemImpArray, impUrl)
			}
		}

		if len(item.DeepLink) > 0 {
			var convTrackArray []models.MHRespConvTracks
			var convTracks models.MHRespConvTracks
			// deeplink track
			var respListItemSuccDeepLinkArray []string
			var respListItemDeepLinkFailedArray []string
			if len(bidItem.EventTracks) > 0 {
				for _, trackItem := range bidItem.EventTracks {
					if trackItem.TrackType == 1 {
						respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, trackItem.TrackUrls...)
					}
					if trackItem.TrackType == 2 {
						respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, trackItem.TrackUrls...)
					}
				}
			}
			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			mhDPParams.Add("log", bigdataParams)
			respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
			convTracks.ConvType = 10
			convTracks.ConvURLS = respListItemSuccDeepLinkArray
			convTrackArray = append(convTrackArray, convTracks)

			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				var deeplinkFailedConvTrack models.MHRespConvTracks
				deeplinkFailedConvTrack.ConvType = 11
				deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
				convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
			}

			item.ConvTracks = convTrackArray
		}

		item.ImpressionLink = respListItemImpArray
		item.ClickLink = respListItemClickArray

		if bidItem.Content.LogoImg != nil {
			item.IconURL = bidItem.Content.LogoImg.ImgURL
		}

		if len(bidItem.Content.Title) > 0 {
			item.Title = bidItem.Content.Title
		}

		if len(bidItem.Content.Content) > 0 {
			item.Description = bidItem.Content.Content
		}

		if len(bidItem.Content.Images) == 0 && bidItem.Content.Video == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
				go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
			}

			continue
		}

		if bidItem.Content.Video != nil && (len(bidItem.Content.Video.VideoUrl.Fd) > 0 || len(bidItem.Content.Video.VideoUrl.Ld) > 0) {
			var video models.MHRespVideo
			if bidItem.Content.Images != nil {
				video.CoverURL = bidItem.Content.Images[0].ImgURL
			}
			var videoURL string
			if len(bidItem.Content.Video.VideoUrl.Ld) > 0 {
				videoURL = bidItem.Content.Video.VideoUrl.Ld
			}
			if len(bidItem.Content.Video.VideoUrl.Fd) > 0 {
				videoURL = bidItem.Content.Video.VideoUrl.Fd
			}
			duration, _ := strconv.Atoi(bidItem.Content.Video.VideoDuration)
			video.VideoURL = videoURL
			video.Duration = duration * 1000
			video.Width = int(bidItem.Content.Video.VideoWidth)
			video.Height = int(bidItem.Content.Video.VideoHeight)
			item.Video = &video
			item.CrtType = 20
			var videoEventArray []models.MHEventTrackItem
			if len(bidItem.EventTracks) > 0 {
				for _, trackItem := range bidItem.EventTracks {
					if trackItem.TrackType == 3 {
						videoStart := models.MHEventTrackItem{
							EventType: 100,
							EventURLS: trackItem.TrackUrls,
						}
						videoEventArray = append(videoEventArray, videoStart)
					}
					if trackItem.TrackType == 4 {
						videoEnd := models.MHEventTrackItem{
							EventType: 103,
							EventURLS: trackItem.TrackUrls,
						}
						videoEventArray = append(videoEventArray, videoEnd)
					}
				}
				item.Video.EventTracks = videoEventArray
			}
		} else {
			if bidItem.Content.Images != nil {
				var imgList []models.MHRespImage
				for _, imgItem := range bidItem.Content.Images {
					var img models.MHRespImage
					img.URL = imgItem.ImgURL
					img.Width = imgItem.ImgW
					img.Height = imgItem.ImgH
					imgList = append(imgList, img)
				}
				item.Image = imgList
				item.CrtType = 11
			}
		}

		if item.Video == nil && item.Image == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.LoseURL != nil && len(bidItem.LoseURL) > 0 {
				go curlLanzhiURL(bidItem.LoseURL, price, bigdataUID)
			}

			continue
		}

		// win notice url
		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}
		item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		if bidItem.WinURL != nil && len(bidItem.WinURL) > 0 {
			go curlLanzhiURL(bidItem.WinURL, price, bigdataUID)
		}

		item.MaterialDirection = platformPos.PlatformPosDirection
		item.PEcpm = price
		list = append(list, item)
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	lanzhiResp := models.MHUpResp{}
	lanzhiResp.RespData = &mhResp
	lanzhiResp.Extra = bigdataExtra

	return &lanzhiResp

}

type LanzhiRequestObject struct {
	RequestID    string                           `json:"request_id"`
	Account      *LanzhiRequestAccountObject      `json:"account"`
	AdAppInfo    *LanzhiRequestAdAppInfoObject    `json:"ad_app_info"`
	AdDeviceInfo *LanzhiRequestAdDeviceInfoObject `json:"ad_device_info"`
	AdSlotInfo   *LanzhiRequestAdSlotInfoObject   `json:"ad_slot_info"`
}

type LanzhiRequestAdDeviceInfoObject struct {
	Width     int                             `json:"width"`
	Height    int                             `json:"height"`
	ClientIP  string                          `json:"client_ip"`
	Ua        string                          `json:"ua"`
	Os        string                          `json:"os"`
	Access    string                          `json:"access"`
	Osv       string                          `json:"osv"`
	Carrier   string                          `json:"carrier"`
	Brand     string                          `json:"brand"`
	Device    string                          `json:"device"`
	DeviceIds []*LanzhiRequestDeviceIdsObject `json:"device_ids"`
}

type LanzhiRequestAdSlotInfoObject struct {
	AdCount    int                                       `json:"ad_count"`
	Attribute  int                                       `json:"attribute"`
	SlotID     string                                    `json:"slot_id"`
	SlotScene  string                                    `json:"slot_scene"`
	FloorPrice string                                    `json:"floor_price"`
	SlotStyle  []*LanzhiRequestAdSlotInfoSlotStyleObject `json:"slot_style"`
}

type LanzhiRequestAdSlotInfoSlotStyleObject struct {
	Style       string `json:"style"`
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Ratio       string `json:"ratio"`
	MinBitrate  string `json:"min_bitrate"`
	MaxBitrate  string `json:"max_bitrate"`
	MinDuration string `json:"min_duration"`
}

type LanzhiRequestDeviceIdsObject struct {
	DeviceID     string `json:"device_id"`
	DeviceIDType string `json:"device_id_type"`
}

type LanzhiRequestAccountObject struct {
	UserID   string `json:"user_id"`
	UserName string `json:"user_name"`
	UserKey  string `json:"user_key"`
}

type LanzhiRequestAdAppInfoObject struct {
	AppName         string  `json:"app_name"`
	PkgName         string  `json:"pkg_name"`
	PkgVer          string  `json:"pkg_ver"`
	InteractionType []int32 `json:"interaction_type"`
}

type LanzhiResponseObject struct {
	Code      string                         `json:"code"`
	Reason    string                         `json:"reason"`
	SlotID    string                         `json:"slot_id"`
	RequestID string                         `json:"request_id"`
	AdSlots   []*LanzhiResponseAdSlotsObject `json:"ad_slots,omitempty"`
}

type LanzhiResponseAdSlotsObject struct {
	Action       int32                               `json:"action,omitempty"`
	AdvertiserId string                              `json:"advertiser_id,omitempty"`
	Category     []string                            `json:"category,omitempty"`
	WinURL       []string                            `json:"win_url,omitempty"`
	LoseURL      []string                            `json:"lose_url,omitempty"`
	ShowURL      []string                            `json:"show_url,omitempty"`
	ClickURL     []string                            `json:"click_url,omitempty"`
	PageURL      string                              `json:"page_url,omitempty"`
	AppStoreUrl  string                              `json:"app_store_url,omitempty"`
	DownloadUrl  string                              `json:"download_url,omitempty"`
	BackupUrl    string                              `json:"backup_url,omitempty"`
	Content      *LanzhiResponseAdSlotsContentObject `json:"content,omitempty"`
	EventTracks  []*LanzhiResponseEventTracksObject  `json:"event_tracks,omitempty"`
}

type LanzhiResponseEventTracksObject struct {
	TrackType int      `json:"track_type,omitempty"`
	TrackUrls []string `json:"track_urls,omitempty"`
}

type LanzhiResponseAdSlotsContentObject struct {
	Width       int                                  `json:"width,omitempty"`
	Height      int                                  `json:"height,omitempty"`
	BidPrice    int                                  `json:"bid_price,omitempty"`
	CreativeID  string                               `json:"creative_id,omitempty"`
	BudgetType  string                               `json:"budget_type,omitempty"`
	LogoName    string                               `json:"logo_name,omitempty"`
	Source      string                               `json:"source,omitempty"`
	SlotScene   string                               `json:"slot_scene,omitempty"`
	SlotStyle   string                               `json:"slot_style,omitempty"`
	Title       string                               `json:"title,omitempty"`
	Content     string                               `json:"content,omitempty"`
	AppLogo     string                               `json:"app_logo,omitempty"`
	AppName     string                               `json:"app_name,omitempty"`
	Developer   string                               `json:"developer,omitempty"`
	AppDesc     string                               `json:"app_desc,omitempty"`
	PackageName string                               `json:"package,omitempty"`
	AppStore    string                               `json:"app_store,omitempty"`
	FileName    string                               `json:"file_name,omitempty"`
	FileSize    string                               `json:"file_size,omitempty"`
	Permission  string                               `json:"permission,omitempty"`
	Privacy     string                               `json:"privacy,omitempty"`
	Version     string                               `json:"version,omitempty"`
	Scheme      string                               `json:"scheme,omitempty"`
	IosUlk      string                               `json:"ios_ulk,omitempty"`
	LogoImg     *LanzhiResponseAdSlotsImagesObject   `json:"logo_img,omitempty"`
	Video       *LanzhiResponseAdSlotsVideoObject    `json:"video,omitempty"`
	Images      []*LanzhiResponseAdSlotsImagesObject `json:"images,omitempty"`
}

type LanzhiResponseAdSlotsVideoObject struct {
	VideoUrl      *LanzhiResponseAdSlotsVideoUrlObject `json:"video_url,omitempty"`
	VideoDuration string                               `json:"video_duration,omitempty"`
	VideoSize     string                               `json:"video_size,omitempty"`
	VideoWidth    int32                                `json:"video_width,omitempty"`
	VideoHeight   int32                                `json:"video_height,omitempty"`
}

type LanzhiResponseAdSlotsVideoUrlObject struct {
	Ld string `json:"ld,omitempty"`
	Fd string `json:"fd,omitempty"`
}

type LanzhiResponseAdSlotsImagesObject struct {
	ImgURL string `json:"img_url,omitempty"`
	ImgH   int    `json:"img_h,omitempty"`
	ImgW   int    `json:"img_w,omitempty"`
}

func curlLanzhiURL(urls []string, price int, requestId string) {
	for _, urlItem := range urls {
		client := &http.Client{Timeout: 1000 * time.Millisecond}
		urlItem = strings.Replace(urlItem, "%%price%%", strconv.Itoa(price), -1)
		urlItem = strings.Replace(urlItem, "%%request_id%%", requestId, -1)
		requestGet, _ := http.NewRequest("GET", urlItem, nil)
		requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
		resp, err := client.Do(requestGet)
		if err != nil {
			return
		}

		defer resp.Body.Close()
	}
}
