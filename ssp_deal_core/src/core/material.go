package core

import (
	"bytes"
	"context"
	"fmt"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/crid_generate/utilities"
)

// HandleMaterial 先补空, 再替换, 再屏蔽
// 参数: 上游返回
// 返回: 是否ok.
// 如果为true, 需要把传入指针的素材的补空和替换功能都完成
// 如果为false, 素材被屏蔽, 需要把external_code改为102006, 把internal_code也修改
func HandleMaterial(c context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu, mhUpResp *models.MHUpResp, dspExpLinks []string, dspClkLinks []string) bool {
	mhResp := mhUpResp.RespData

	var tmpDataItemArray []models.MHRespDataItem
	var tmpShieldCode int

	traffic := 1
	cacheKey := "go_ssp_material_statistics_traffic_config_app_" + localPos.LocalAppID
	cacheValue, _ := db.GlbBigCacheMinute.Get(cacheKey)
	if len(cacheValue) > 0 {
		num, err := strconv.Atoi(string(cacheValue))
		if err != nil {
			traffic = 1
		} else {
			traffic = num
		}
	}

	if mhResp.Data[localPos.LocalPosID].List != nil {
		for idx, item := range mhResp.Data[localPos.LocalPosID].List {
			//if localPos.LocalPosID == "60128" && mhUpResp.Extra.PlatformPos.PlatformPosID == "cocloudsplash05817" {
			//	go models.BigDataHolMaterialAllData(bigdataUID, mhUpResp.Extra.PlatformPos, localPos, mhReq, &item)
			//}

			item.DemandCrid = item.Crid

			// 下游广告位下载转 H5
			if localPos.LocalPosMHLandPage == 1 {
				item.InteractType = 0
			}

			// 2025.06.06 hack 11180 10808对于返回channel=24的下载类转h5
			if localPos.LocalAppID == "11180" || localPos.LocalAppID == "10808" {
				if mhUpResp.Extra.PlatformPos.PlatformMediaID == "24" {
					item.InteractType = 0
				}
			}

			// 是否忽略点击上报, 1. deeplink=配置deeplink, 2. 忽略上游返回点击监测链接
			isSkipClickLink, skipClickDeeplink := IsSkipClickLink(c, mhReq, localPos, mhUpResp.Extra.PlatformPos, item)
			if isSkipClickLink {
				item.DeepLink = skipClickDeeplink
				item.IsSkipClick = 1

				var tmpClickLinkArray []string
				for _, tmpClickLinkItem := range item.ClickLink {
					if strings.Contains(tmpClickLinkItem, "maplehaze.cn") {
						tmpClickLinkArray = append(tmpClickLinkArray, tmpClickLinkItem+"&is_skip=1")
					}
				}
				item.ClickLink = tmpClickLinkArray
			}

			// 是否丢弃deeplink, 如果不丢弃, 按照之前逻辑, 如果丢弃, 替换配置的deeplink
			isDiscardDeepLink := IsDiscardDeepLink(c, mhReq, localPos, mhUpResp.Extra.PlatformPos, item)
			if isDiscardDeepLink {
				item.DeepLink = ""
			}

			// 上游屏蔽
			isAdShieldOK, adShieldCode := IsNewMaterialAdShieldOK(c, bigdataUID, mhReq, localPos, mhUpResp.Extra.PlatformPos, &item)
			if !isAdShieldOK {
				tmpShieldCode = adShieldCode
				mhUpResp.Extra.UpRespFailedNum = mhUpResp.Extra.UpRespFailedNum + 1

				// demand竞价失败上报
				if len(item.DemandLossNoticeURLs) > 0 {
					curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
					// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
					// }
					mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
					item.DemandLossNoticeURLs = nil
				}
				continue
			}

			// 媒体广告类型屏蔽
			isTypeShieldOK, typeShieldCode := IsMaterialTypeShieldOK(localPos, mhReq, &item)
			if !isTypeShieldOK {
				tmpShieldCode = typeShieldCode
				mhUpResp.Extra.UpRespFailedNum = mhUpResp.Extra.UpRespFailedNum + 1

				// demand竞价失败上报
				if len(item.DemandLossNoticeURLs) > 0 {
					curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
					// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
					// }
					mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
					item.DemandLossNoticeURLs = nil
				}
				continue
			}

			// 扩展广告
			isExtraOK, extraCode := IsSSPExtraOK(c, mhUpResp.Extra.PlatformPos, localPos, mhReq, &item)
			if !isExtraOK {
				tmpShieldCode = extraCode
				mhUpResp.Extra.UpRespFailedNum = mhUpResp.Extra.UpRespFailedNum + 1

				// demand竞价失败上报
				if len(item.DemandLossNoticeURLs) > 0 {
					curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
					// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
					// }
					mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
					item.DemandLossNoticeURLs = nil
				}
				continue
			}

			var replaceData models.MHRespDataItem
			var (
				imgUrl   string
				videoUrl string
				coverUrl string
				iconUrl  string
				title    string
				desc     string
			)
			// 原始数据
			replaceData = item

			NewMaterialReplaceType(c, bigdataUID, mhReq, localPos, mhUpResp.Extra.PlatformPos, &item)
			MaterialReplaceAdContent(localPos, mhUpResp.Extra.PlatformPos, &item)
			NewMaterialReplace(c, bigdataUID, mhReq, localPos, mhUpResp.Extra.PlatformPos, &item, &replaceData)

			////////////////////////////////////////////////////////////////////////////////////////
			// 媒体屏蔽
			isShieldOK, shieldCode := IsNewMaterialShieldOK(c, bigdataUID, mhReq, localPos, &item, mhUpResp.Extra.PlatformPos)
			if isShieldOK {
				// 补空
				MaterialFull(mhUpResp.Extra.PlatformPos, mhReq.Device.Os, &item)
				MaterialPackageFull(mhReq.Device.Os, &item)

				title = item.Title
				desc = item.Description
				if item.Image != nil {
					imgUrl = item.Image[0].URL
				}
				if len(item.IconURL) > 0 {
					iconUrl = item.IconURL
				}
				if item.Video != nil {
					if len(item.Video.VideoURL) > 0 {
						videoUrl = item.Video.VideoURL
					}
					if len(item.Video.CoverURL) > 0 {
						coverUrl = item.Video.CoverURL
					}
				}
				crid := utilities.GetCrid(title, desc, iconUrl, imgUrl, coverUrl, videoUrl)
				item.SupplyCrid = crid
				item.Crid = crid
				replaceData.SupplyCrid = crid
				replaceData.Crid = crid

				if len(replaceData.IsReplace) > 0 && strings.Contains(replaceData.IsReplace, "1") {
					go models.BigDataHolMaterialReplaceData(bigdataUID, mhUpResp.Extra.PlatformPos, localPos, mhReq, &replaceData)
				}

				// sdk, market_url长度 > 0, 下发appstore_package_name给sdk
				if localPos.LocalAppType == "1" {
					if len(item.MarketURL) > 0 {
						// huawei, oppo, vivo, xiaomi
						tmpManufacturer := GetManufactureType(c, mhReq)
						if tmpManufacturer == "huawei" {
							item.AppstorePackageName = "com.huawei.appmarket"
						} else if tmpManufacturer == "xiaomi" {
							item.AppstorePackageName = "com.xiaomi.market"
						} else if tmpManufacturer == "oppo" {
							osvMajor := 0
							if len(mhReq.Device.OsVersion) > 0 {
								osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
								osvMajor = utils.ConvertStringToInt(osvMajorStr)
							}
							if osvMajor < 9 {
								item.AppstorePackageName = "com.oppo.market"
							} else {
								item.AppstorePackageName = "com.heytap.market"
							}
						} else if tmpManufacturer == "vivo" {
							item.AppstorePackageName = "com.bbk.appstore"
						}
					}
				}

				// sdk, quick_app_link长度 > 0, 下发quickapp_store_pkg给sdk
				if localPos.LocalAppType == "1" {
					if len(item.QuickAppLink) > 0 {
						// honor, huawei, oppo, vivo, xiaomi
						tmpManufacturer := GetManufactureType(c, mhReq)
						if tmpManufacturer == "honor" {
							item.QuickAppStorePkg = "com.hihonor.servicecenter"
						} else if tmpManufacturer == "huawei" {
							item.QuickAppStorePkg = "com.huawei.fastapp"
						} else if tmpManufacturer == "xiaomi" {
							item.QuickAppStorePkg = "com.miui.hybrid"
						} else if tmpManufacturer == "oppo" {
							item.QuickAppStorePkg = "com.nearme.instant.platform"
						} else if tmpManufacturer == "vivo" {
							item.QuickAppStorePkg = "com.vivo.hybrid"
						}
					}
				}

				// sdk 3.3.3之前的流量，当上游是ovhm时，强制返回market_dp=1，同时不下发appstore_package_name
				if localPos.LocalAppType == "1" {
					if len(item.DeepLink) > 0 && item.InteractType == 1 {
						if IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 3) {
						} else {
							if mhUpResp.Extra.PlatformPos.PlatformMediaID == "15" ||
								mhUpResp.Extra.PlatformPos.PlatformMediaID == "16" ||
								mhUpResp.Extra.PlatformPos.PlatformMediaID == "21" ||
								mhUpResp.Extra.PlatformPos.PlatformMediaID == "24" {
								item.MarketDP = 1
								item.AppstorePackageName = ""
							}
						}
					}
				}

				// 是否http强转https
				if localPos.LocalAppIsForceHttps == 1 {
					// image url
					if len(item.Image) > 0 {
						if len(item.Image[0].URL) > 0 && strings.HasPrefix(item.Image[0].URL, "http://") {
							item.Image[0].URL = strings.Replace(item.Image[0].URL, "http://", "https://", -1)
						}
					}
					// video url, cover url
					if item.Video != nil {
						if len(item.Video.VideoURL) > 0 && strings.HasPrefix(item.Video.VideoURL, "http://") {
							item.Video.VideoURL = strings.Replace(item.Video.VideoURL, "http://", "https://", -1)
						}
						if len(item.Video.CoverURL) > 0 && strings.HasPrefix(item.Video.CoverURL, "http://") {
							item.Video.CoverURL = strings.Replace(item.Video.CoverURL, "http://", "https://", -1)
						}
					}
					// icon url
					if len(item.IconURL) > 0 && strings.HasPrefix(item.IconURL, "http://") {
						item.IconURL = strings.Replace(item.IconURL, "http://", "https://", -1)
					}
					// adurl
					if len(item.AdURL) > 0 && strings.HasPrefix(item.AdURL, "http://") {
						item.AdURL = strings.Replace(item.AdURL, "http://", "https://", -1)
					}
					// landingpage url
					if len(item.LandpageURL) > 0 && strings.HasPrefix(item.LandpageURL, "http://") {
						item.LandpageURL = strings.Replace(item.LandpageURL, "http://", "https://", -1)
					}
					// download url
					if len(item.DownloadURL) > 0 && strings.HasPrefix(item.DownloadURL, "http://") {
						item.DownloadURL = strings.Replace(item.DownloadURL, "http://", "https://", -1)
					}
				}

				// 是否三方监测
				if mhUpResp.Extra.PlatformPos.PlatformPosIsThirdMonitor == 1 {
					isWhiteFlag := 1
					if len(mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorWhiteMediasConfig) == 0 {
						isWhiteFlag = 2
					} else {
						for _, whiteMediasConfigItem := range mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorWhiteMediasConfig {
							if whiteMediasConfigItem.LocalAppID == localPos.LocalAppID {
								isWhiteFlag = 2
								break
							}
						}
					}

					if isWhiteFlag == 2 {
						if len(mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorExpLink) > 0 {
							item.ImpressionLink = append(item.ImpressionLink, mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorExpLink...)
						}
						if len(mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorClkLink) > 0 {
							item.ClickLink = append(item.ClickLink, mhUpResp.Extra.PlatformPos.PlatformPosThirdMonitorClkLink...)
						}
					}
				}

				// dsp补曝光, 点击
				if idx == 0 {
					if len(dspExpLinks) > 0 {
						item.ImpressionLink = append(item.ImpressionLink, dspExpLinks...)
					}
					if len(dspClkLinks) > 0 {
						item.ClickLink = append(item.ClickLink, dspClkLinks...)
					}
				}

				// 包名白名单
				if len(mhReq.Pos.PkgWhitelist) > 0 {
					if item.InteractType == 0 || len(item.PackageName) == 0 {
						// demand竞价失败上报
						if len(item.DemandLossNoticeURLs) > 0 {
							curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
							// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
							// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
							// }
							mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
							item.DemandLossNoticeURLs = nil
						}
						continue
					}
					isInWhite := false
					for _, pkgWhiteItem := range mhReq.Pos.PkgWhitelist {
						if pkgWhiteItem == item.PackageName {
							isInWhite = true
							break
						}
					}
					if isInWhite {
					} else {
						// demand竞价失败上报
						if len(item.DemandLossNoticeURLs) > 0 {
							curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
							// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
							// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
							// }
							mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
							item.DemandLossNoticeURLs = nil
						}
						continue
					}
				}

				// category 快手出价类型
				if mhUpResp.Extra.Category != nil {
					if len(mhUpResp.Extra.Category.KsPriceType) > 0 {
						if mhUpResp.Extra.Category.KsPriceType == "cpm" {
							item.KsPriceType = "cpm"
						} else if mhUpResp.Extra.Category.KsPriceType == "cpc" {
							exHGETDIDIPKey := fmt.Sprintf(rediskeys.STATISTICS_REALTIME_BY_SUPPLY_DEMAND_POS_KEY,
								localPos.LocalAppID,
								localPos.LocalPosID,
								mhUpResp.Extra.PlatformPos.PlatformAppID,
								mhUpResp.Extra.PlatformPos.PlatformPosID)
							result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETDIDIPKey, "clk_rate")
							if redisErr != nil {
								item.KsPriceType = "cpm"
							} else {
								tmpClickRate := utils.ConvertStringToFloat(result.(string))
								if tmpClickRate <= 0 {
									item.KsPriceType = "cpm"
								} else {
									item.KsPriceType = "cpc"
									item.KsClickRate = tmpClickRate
								}
							}
						}
					}
				}

				// 上游媒体 H5为空使用枫岚落地页
				if mhUpResp.Extra.PlatformPos.PlatformAppIsMaplehazeLandingPage == 1 {
					if mhReq.Device.Os == "android" && len(item.DownloadURL) > 0 {
						landingPageParams := url.Values{}

						landingPageParams.Add("apkurl", item.DownloadURL)
						landingPageParams.Add("title", item.Title)
						landingPageParams.Add("description", item.Description)
						landingPageParams.Add("icon", item.IconURL)

						if item.Image != nil {
							landingPageParams.Add("image", item.Image[0].URL)
						} else {
							if item.Video != nil {
								landingPageParams.Add("image", item.Video.CoverURL)
							} else {
								landingPageParams.Add("image", "")
							}
						}
						if len(item.AppVersion) > 0 {
							landingPageParams.Add("version", item.AppVersion)
						} else {
							landingPageParams.Add("version", "1.0.0")
						}
						if len(item.Publisher) > 0 {
							landingPageParams.Add("author", item.Publisher)
						} else {
							landingPageParams.Add("author", "ADS")
						}
						landingPageParams.Add("privacy", item.PrivacyLink)

						maplehazeLandingPageURL := "https://www.maplehaze.cn/static/landingpage" + "?" + landingPageParams.Encode()
						item.AdURL = maplehazeLandingPageURL
						item.LandpageURL = maplehazeLandingPageURL
					}
				}

				if mhUpResp.Extra.PlatformPos.PlatformMediaID == "98" && localPos.LocalAppID == "10913" {
					dictQueryNum := rand.Intn(10)
					if dictQueryNum == 0 {
						go models.BigDataHoloMaterialDictQuery(bigdataUID, mhUpResp.Extra.PlatformPos, item)
					}
				}

				// todo 处理item前需要再win_data前面
				if traffic == 100 {
					go models.BigDataHoloMaterialWinData(bigdataUID, mhUpResp.Extra.PlatformPos, localPos, mhReq, item)
				} else {
					if traffic > 0 {
						randNum := rand.Intn(100)
						if randNum <= traffic {
							go models.BigDataHoloMaterialWinData(bigdataUID, mhUpResp.Extra.PlatformPos, localPos, mhReq, item)
						}
					}
				}

				// 1、10913写死versioncode=999999999
				// if localPos.LocalAppID == "10913" {
				// 	if item.InteractType == 1 {
				// 		// item.DeepLink = "leapp://ptn/appinfo.do?packagename=" + item.PackageName + "&versioncode=-1&autodw=1&source=fl"
				// 		item.AppVersionCode = "999999999"
				// 	}
				// }

				// sdk dp_h5_cfg_value 功能
				if localPos.LocalAppType == "1" {
					if item.InteractType == 0 {
						if localPos.LocalAppIsCoDPH5 == 0 {
							item.DPH5Config = 0
						} else if localPos.LocalAppIsCoDPH5 == 1 {
							if localPos.LocalAppDPH5Type == 0 {
								item.DPH5Config = 1
							} else if localPos.LocalAppDPH5Type == 1 {
								item.DPH5Config = 0
								if len(localPos.LocalAppDPH5PlatformIDS) > 0 {
									dph5PlatformArray := strings.Split(localPos.LocalAppDPH5PlatformIDS, ",")
									for _, tmpItem := range dph5PlatformArray {
										if tmpItem == mhUpResp.Extra.PlatformPos.PlatformMediaID {
											item.DPH5Config = 1
											break
										}
									}
								}
							}
						}
					}
				}

				// sdk -> api 点击延迟上报时间
				if localPos.LocalAppType == "1" {
					if len(localPos.LocalAppClickDelayReportMinTime) == 0 && len(localPos.LocalAppClickDelayReportMaxTime) == 0 {
					} else {
						clickDelayReportMinTime := utils.ConvertStringToInt(localPos.LocalAppClickDelayReportMinTime)
						clickDelayReportMaxTime := utils.ConvertStringToInt(localPos.LocalAppClickDelayReportMaxTime)

						if clickDelayReportMinTime >= clickDelayReportMaxTime {
							item.IntervalTime = clickDelayReportMinTime
						} else {
							item.IntervalTime = clickDelayReportMinTime + rand.Intn(clickDelayReportMaxTime+1-clickDelayReportMinTime)
						}
					}
				}

				// 下游媒体配置rtb媒体类型, 下发上游平台, 字段ad_channel
				if localPos.LocalAppType == "4" {
					item.AdChannel = mhUpResp.Extra.PlatformPos.PlatformMediaID
				}

				if localPos.LocalAppType == "1" {
					if mhReq.Device.Os == "android" {
						if localPos.LocalPosIsMagic == 1 {
							if rand.Intn(100) < localPos.LocalPosMagicCloseReplaceClickWeight {
								item.IsMagicCloseReplaceClick = 2
							} else {
								item.IsMagicCloseReplaceClick = 1
							}
						}
					} else if mhReq.Device.Os == "ios" {
						if localPos.LocalPosNewIsMagic == 1 {
							if rand.Intn(100) < localPos.LocalPosNewMagicCloseReplaceClickWeight {
								item.IsMagicCloseReplaceClick = 2
							} else {
								item.IsMagicCloseReplaceClick = 1
							}
						}
					}
				}

				if localPos.LocalAppType == "1" {
					if mhReq.Device.Os == "android" {
						if len(localPos.LocalAppSDKNotificationPosTypeList) > 0 {
							tmpSDKNotificationPosTypeList := strings.Split(localPos.LocalAppSDKNotificationPosTypeList, ",")
							for _, tmpSDKNotificationPosTypeItem := range tmpSDKNotificationPosTypeList {
								if utils.ConvertStringToInt(tmpSDKNotificationPosTypeItem) == localPos.LocalPosType {
									item.IsDownloadShowNotification = 1
									break
								}
							}
						}
					}
				}

				// hack 2025.05.19
				// android api预算:
				// app_id: 11180
				// version＜3.3.9.4
				// 下游原生广告位
				// 过滤视频素材
				if localPos.LocalAppID == "11180" && localPos.LocalPosType == 4 {
					if IsSDKVersionMoreThanOrEqualFourVersionCode(c, mhReq.SDKVersion, 3, 3, 9, 4) {
					} else {
						if item.Video != nil && len(item.Video.VideoURL) > 0 {
							continue
						}
					}
				}

				// 2024.12.16
				// 0，网页
				// 1，下载
				// 2，小程序
				// 3、小游戏
				// 4、快应用
				// 5、游戏下载
				// 6、应用下载
				// if localPos.LocalPosID == "61143" {
				// 	item.InteractType = 4
				// } else if localPos.LocalPosID == "61142" {
				// 	item.InteractType = 3
				// } else if localPos.LocalPosID == "61141" {
				// 	item.InteractType = 2
				// } else if localPos.LocalPosID == "61140" {
				// 	item.InteractType = 5
				// } else if localPos.LocalPosID == "61139" {
				// 	item.InteractType = 6
				// }
				// 2025.02.20
				// https://fenglanhulian.yuque.com/nr6907/uet79s/ptt0rrhepcimpgsq?singleDoc#
				// https://kea58ff4ba.docs.qq.com/sheet/DYm5GWlpnUmlVZURNamdqZklZ?tab=BB08J2
				// 2025.03.28 删除 841101001 hack
				// if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101001" {
				// 	item.InteractType = 1
				// } else if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101056" {
				// 	item.InteractType = 3
				// } else if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101055" {
				// 	item.InteractType = 2
				// } else if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101058" {
				// 	item.InteractType = 6
				// } else if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101059" {
				// 	item.InteractType = 4
				// } else if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101057" {
				// 	item.InteractType = 5
				// }

				// 2025.04.29 61732 = 5  61733 = 4
				if localPos.LocalPosID == "61732" {
					item.InteractType = 5
				} else if localPos.LocalPosID == "61733" {
					item.InteractType = 4
				}

				cridParams := url.Values{}
				cridParams.Set("supply_crid", item.Crid)
				cridParams.Set("demand_crid", item.DemandCrid)

				if len(item.ImpressionLink) > 0 {
					var impressionLink []string
					for _, impItem := range item.ImpressionLink {
						if strings.Contains(impItem, "maplehaze.cn") {
							impItem = impItem + "&" + cridParams.Encode()
						}
						impressionLink = append(impressionLink, impItem)
					}

					item.ImpressionLink = impressionLink
				}

				if len(item.ClickLink) > 0 {
					var clickLink []string
					for _, clkItem := range item.ClickLink {
						if strings.Contains(clkItem, "maplehaze.cn") {
							clkItem = clkItem + "&" + cridParams.Encode()
						}

						clickLink = append(clickLink, clkItem)
					}

					item.ClickLink = clickLink
				}

				// todo p_ecpm 属于内部上游字段 使用完后销毁
				item.PEcpm = 0
				tmpDataItemArray = append(tmpDataItemArray, item)
			} else {
				tmpShieldCode = shieldCode
				mhUpResp.Extra.UpRespFailedNum = mhUpResp.Extra.UpRespFailedNum + 1

				// demand竞价失败上报
				if len(item.DemandLossNoticeURLs) > 0 {
					curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 2)
					// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
					// }
					mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
					item.DemandLossNoticeURLs = nil
				}
			}
		}
	}

	// 如果屏蔽都不OK, 直接返回102006
	if len(tmpDataItemArray) == 0 {
		mhUpResp.Extra.ExternalCode = 102006
		mhUpResp.Extra.InternalCode = tmpShieldCode
		return false
	}

	// 2025.05.20 SDK强制单次请求下曝光次数为1次
	if localPos.LocalAppType == "1" {
		mhResp.ExpMaxLimit = 1
		mhResp.ClickMaxLimit = 1
		if mhUpResp != nil && mhUpResp.Extra.PlatformPos != nil && mhUpResp.Extra.PlatformPos.PlatformPosMaxClickLimit > 0 {
			mhResp.ClickMaxLimit = mhUpResp.Extra.PlatformPos.PlatformPosMaxClickLimit
		}
	}

	// 更新up resp list
	mhResp.Data[localPos.LocalPosID].List = tmpDataItemArray
	// 更新up resp
	mhUpResp.RespData = mhResp

	return true
}

func HandleWinLossNoticeURLsWithAdID(c context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu,
	mhUpResp *models.MHUpResp, winAdidArray []string) bool {
	mhResp := mhUpResp.RespData

	var tmpDataItemArray []models.MHRespDataItem
	if mhResp.Data[localPos.LocalPosID].List != nil {
		for _, item := range mhResp.Data[localPos.LocalPosID].List {
			////////////////////////////////////////////////////////////////////////////////////////
			isInWinAdid := false
			for _, winAdidItem := range winAdidArray {
				if winAdidItem == item.AdID {
					isInWinAdid = true
					break
				}
			}
			if isInWinAdid {
				if len(item.DemandWinNoticeURLs) > 0 {
					curlURLArray(item.DemandWinNoticeURLs)
					// mhUpResp.Extra.UpPriceReportWinNum = mhUpResp.Extra.UpPriceReportWinNum + 1
				}
				// if mhUpResp.Extra.PlatformPos.PlatformAppID == "841101004" {
				// 	go models.BigDataHoloDebugJson2(bigdataUID+"&841101004&win", strings.Join(item.DemandWinNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
				// }
			} else {
				if len(item.DemandLossNoticeURLs) > 0 {
					curlLossURLArrayWithReason(item.DemandLossNoticeURLs, mhUpResp.Extra.PlatformPos, 1)
					// if mhUpResp.Extra.PlatformPos.PlatformAppID == "516400015" || mhUpResp.Extra.PlatformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", strings.Join(item.DemandLossNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, mhUpResp.Extra.PlatformPos.PlatformAppID, mhUpResp.Extra.PlatformPos.PlatformAppID)
					// }
					mhUpResp.Extra.UpPriceReportFailedNum = mhUpResp.Extra.UpPriceReportFailedNum + 1
				}
			}

			item.DemandWinNoticeURLs = nil
			item.DemandLossNoticeURLs = nil
			////////////////////////////////////////////////////////////////////////////////////////
			tmpDataItemArray = append(tmpDataItemArray, item)
		}
	}

	// 如果屏蔽都不OK, 直接返回102006
	if len(tmpDataItemArray) == 0 {
		mhUpResp.Extra.ExternalCode = 102006
		mhUpResp.Extra.InternalCode = 900009
		return false
	}

	// 更新up resp list
	mhResp.Data[localPos.LocalPosID].List = tmpDataItemArray
	// 更新up resp
	mhUpResp.RespData = mhResp

	return true
}

// curl notice urls
// reason: 1 竞价失败，2 素材问题，3 其他
// 目前拼多多 宏 __LOSE_CODE__, 竞败原因：1-出价低于竞争对手，2-创意素材问题，3-其他
func curlLossURLArrayWithReason(noticeURLs []string, platformPos *models.PlatformPosStu, reason int) {
	if platformPos != nil && platformPos.PlatformMediaID == "33" {
		var tmpLossURLArray []string
		for _, tmpItem := range noticeURLs {
			tmpItem = strings.Replace(tmpItem, "__LOSE_CODE__", utils.ConvertIntToString(reason), -1)
			tmpLossURLArray = append(tmpLossURLArray, tmpItem)
		}
		curlURLArray(tmpLossURLArray)
	} else {
		curlURLArray(noticeURLs)
	}
}

// curl notice urls
func curlURLArray(noticeURLs []string) {
	if len(noticeURLs) > 0 {
		go func(tmpNoticeURLs []string) {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("loss failed url panic:", err)
				}
			}()
			for _, item := range tmpNoticeURLs {
				if strings.HasPrefix(item, "http") {
					client := &http.Client{Timeout: 1000 * time.Millisecond}
					requestGet, _ := http.NewRequest("GET", item, nil)

					requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
					fmt.Println("curl nurl url: ", requestGet.URL.String())

					resp, err := client.Do(requestGet)
					if err != nil {
						// fmt.Printf("get request failed, err:[%s]", err.Error())
						return
					}
					defer resp.Body.Close()
				} else {
					client := &http.Client{Timeout: 1000 * time.Millisecond}

					// send req
					requestGet, _ := http.NewRequest("POST", "https://www.pangolin-dsp-toutiao.com/api/common/ads/failreason", bytes.NewReader([]byte(item)))

					requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

					resp, err := client.Do(requestGet)
					if err != nil {
						// fmt.Printf("get request failed, err:[%s]", err.Error())
						return
					}
					defer resp.Body.Close()
				}

				// bodyContent, _ := io.ReadAll(resp.Body)
				// fmt.Println("curl nurl resp: ", string(bodyContent))
			}
		}(noticeURLs)
	}
}
