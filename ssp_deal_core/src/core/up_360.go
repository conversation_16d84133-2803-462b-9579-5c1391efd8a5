package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFrom360 ...
func GetFrom360(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from 360")

	// fmt.Println("get from 360, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from 360, p_pos_id:", platformPos.PlatformPosID)

	// test
	if platformPos.PlatformPosIsDebug == 1 {
		// platformPos.PlatformAppUpURL = "http://10.216.192.76:8080/adGet"
		// platformPos.PlatformAppID = "300"
		// platformPos.PlatformPosID = "1314"
		// platformPos.PlatformAppKey = "AC95173AC758F1F4DD8E39B4982424D4"
	}

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	/////////////////////////////////////////////////////////////////////////////////////
	// device
	reqDeviceMap := map[string]interface{}{}
	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				reqDeviceMap["imei"] = mhReq.Device.Imei
				reqDeviceMap["deviceId"] = strings.ToUpper(utils.GetMd5(mhReq.Device.Imei))
				reqDeviceMap["imeiMd5"] = strings.ToUpper(utils.GetMd5(mhReq.Device.Imei))
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				reqDeviceMap["deviceId"] = strings.ToUpper(mhReq.Device.ImeiMd5)
				reqDeviceMap["imeiMd5"] = strings.ToUpper(mhReq.Device.ImeiMd5)
			} else {
				fmt.Println("get from 360 error req < 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceMap["deviceId"] = strings.ToLower(utils.GetMd5(mhReq.Device.Oaid))
				reqDeviceMap["oaid"] = mhReq.Device.Oaid
				reqDeviceMap["oaidMd5"] = utils.GetMd5(mhReq.Device.Oaid)
			}

			if len(mhReq.Device.Oaid) == 0 || strings.Contains(mhReq.Device.Oaid, "00000000") {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		if len(mhReq.Device.Idfa) > 0 {
			reqDeviceMap["deviceId"] = mhReq.Device.Idfa
			reqDeviceMap["idfa"] = mhReq.Device.Idfa
			reqDeviceMap["idfaMd5"] = utils.GetMd5(mhReq.Device.Idfa)
		} else {
			fmt.Println("get from 360 error req idfa")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if mhReq.Device.Os == "android" {
		reqDeviceMap["model"] = mhReq.Device.Model
		reqDeviceMap["brand"] = mhReq.Device.Manufacturer
		reqDeviceMap["os"] = "android"
	} else if mhReq.Device.Os == "ios" {
		reqDeviceMap["model"] = mhReq.Device.Model
		reqDeviceMap["brand"] = "Apple"
		reqDeviceMap["os"] = "ios"
	}

	reqDeviceMap["osVersion"] = mhReq.Device.OsVersion
	reqDeviceMap["deviceType"] = 1
	reqDeviceMap["ip"] = mhReq.Device.IP
	reqDeviceMap["ipMd5"] = utils.GetMd5(mhReq.Device.IP)
	reqDeviceMap["userAgent"] = destConfigUA

	if mhReq.Network.Carrier == 0 {
		reqDeviceMap["carrier"] = 9
	} else if mhReq.Network.Carrier == 1 {
		reqDeviceMap["carrier"] = 1
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceMap["carrier"] = 2
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceMap["carrier"] = 3
	} else {
		reqDeviceMap["carrier"] = 9
	}

	if mhReq.Network.ConnectType == 0 {
		// 未知
		reqDeviceMap["network"] = 1
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		reqDeviceMap["network"] = 6
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		reqDeviceMap["network"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		reqDeviceMap["network"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		reqDeviceMap["network"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		reqDeviceMap["network"] = 5
	} else {
		reqDeviceMap["network"] = 6
	}
	if len(mhReq.Device.BootMark) > 0 {
		reqDeviceMap["boot_mark"] = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		reqDeviceMap["update_mark"] = mhReq.Device.UpdateMark
	}
	if len(mhReq.Device.AndroidID) > 0 {
		reqDeviceMap["androidId"] = strings.ToLower(mhReq.Device.AndroidID)
		reqDeviceMap["androidIdMd5"] = strings.ToLower(utils.GetMd5(mhReq.Device.AndroidID))
	}
	if len(mhReq.Device.AndroidIDMd5) > 0 {
		reqDeviceMap["androidIdMd5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)
	}
	if mhReq.Device.ScreenWidth > 0 {
		reqDeviceMap["screenWidth"] = mhReq.Device.ScreenWidth
	}
	if mhReq.Device.ScreenHeight > 0 {
		reqDeviceMap["screenHeight"] = mhReq.Device.ScreenHeight
	}

	// imp
	reqImpInfoArray := []map[string]interface{}{}
	reqImpInfoItemMap := map[string]interface{}{}
	reqImpInfoItemMap["id"] = 1
	reqImpInfoItemMap["width"] = platformPos.PlatformPosWidth
	reqImpInfoItemMap["height"] = platformPos.PlatformPosHeight
	reqImpInfoItemMap["bidfloor"] = localPosFloorPrice

	if platformPos.PlatformPosType == 5 || platformPos.PlatformPosType == 11 {
		if platformPos.PlatformPosType == 5 {
			reqImpInfoItemVideoMap := map[string]interface{}{}
			reqImpInfoItemVideoMap["videoType"] = 1

			reqImpInfoItemMap["video"] = reqImpInfoItemVideoMap

		} else if platformPos.PlatformPosType == 11 {
			reqImpInfoItemVideoMap := map[string]interface{}{}
			reqImpInfoItemVideoMap["videoType"] = 2

			reqImpInfoItemMap["video"] = reqImpInfoItemVideoMap
		}
	}
	reqImpInfoArray = append(reqImpInfoArray, reqImpInfoItemMap)

	// app
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["appName"] = platformPos.PlatformAppName
	reqAppInfoMap["pkgName"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	reqAppInfoMap["version"] = platformPos.PlatformAppVersion

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					gdtReplaceDeviceMap := map[string]interface{}{}
					gdtReplaceDeviceMap["os"] = strings.ToLower(mhReq.Device.Os)
					gdtReplaceDeviceMap["os_version"] = didRedisData.OsVersion
					gdtReplaceDeviceMap["model"] = didRedisData.Model
					gdtReplaceDeviceMap["device_type"] = mhReq.Device.DeviceType
					gdtReplaceDeviceMap["manufacturer"] = didRedisData.Manufacturer
					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							gdtReplaceDeviceMap["imei_md5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							gdtReplaceDeviceMap["imei_md5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							gdtReplaceDeviceMap["oaid"] = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								gdtReplaceDeviceMap["android_id_md5"] = utils.GetMd5(didRedisData.AndroidID)
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else if len(didRedisData.AndroidIDMd5) > 0 && didRedisData.AndroidIDMd5 != utils.GetMd5("") {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								gdtReplaceDeviceMap["android_id_md5"] = strings.ToLower(didRedisData.AndroidIDMd5)
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					if platformPos.PlatformPosType == 11 {
						// 0 横屏, 1 竖屏
						if platformPos.PlatformPosDirection == 0 {
							gdtReplaceDeviceMap["orientation"] = 90
						} else if platformPos.PlatformPosDirection == 1 {
							gdtReplaceDeviceMap["orientation"] = 0
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					_, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		var bigdataReplaceDIDStu models.ReplaceDIDStu
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, false, bigdataReplaceDIDStu)
	}()

	// req id
	requestID := ""
	platformAppIDLength := len(platformPos.PlatformAppID)
	for i := 0; i < 5-platformAppIDLength; i++ {
		requestID = requestID + "0"
	}
	requestID = requestID + platformPos.PlatformAppID
	platformPosIDLength := len(platformPos.PlatformPosID)
	for i := 0; i < 7-platformPosIDLength; i++ {
		requestID = requestID + "0"
	}
	requestID = requestID + platformPos.PlatformPosID
	requestID = requestID + utils.ConvertInt64ToString(utils.GetCurrentMilliSecond())

	// req auth
	reqAuth := strings.ToUpper(utils.GetMd5(requestID + platformPos.PlatformPosKey))

	postData := map[string]interface{}{
		"requestId": requestID,
		"auth":      reqAuth,
		"device":    reqDeviceMap,
		"imps":      reqImpInfoArray,
		"app":       reqAppInfoMap,
		"timeout":   timeout,
		"version":   "3.0",
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("360 req: " + string(jsonData))

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("360 resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	three60RespStu := Three60RespStu{}
	json.Unmarshal([]byte(bodyContent), &three60RespStu)

	if three60RespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(three60RespStu.Data.Groups) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(three60RespStu.Data.Groups[0].Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, three60InfoItem := range three60RespStu.Data.Groups[0].Ads {
		adInfoItem := three60InfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		three60Ecpm := int(adInfoItem.Price)

		respTmpPrice = respTmpPrice + three60Ecpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if three60Ecpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			three60Ecpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > three60Ecpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(three60Ecpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceValue := "__win_price__"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("360 price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			randPrice := three60Ecpm * randPRValue / 100

			tmpPriceValue, _ := utils.AesCBCEncrypt([]byte(utils.ConvertIntToString(randPrice)), []byte(platformPos.PlatformAppPriceEncrypt))
			encodePriceValue = string(utils.HexEncode(string(tmpPriceValue)))
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		// crid
		respListItemMap["crid"] = utils.ConvertIntToString(adInfoItem.CreativeID)

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.AppInfo.DeepLink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.AppInfo.DeepLink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, adTrackInfoItem := range adInfoItem.AppInfo.EventTracks {
				if adTrackInfoItem.EventType == 14 {
					for _, adTrackInfoTrackURLItem := range adTrackInfoItem.EventTrackUrls {
						trackItem := adTrackInfoTrackURLItem
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, trackItem)
					}
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, adTrackInfoItem := range adInfoItem.AppInfo.EventTracks {
					if adTrackInfoItem.EventType == 13 {
						for _, adTrackInfoTrackURLItem := range adTrackInfoItem.EventTrackUrls {
							trackItem := adTrackInfoTrackURLItem
							respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, trackItem)
						}
					}
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.IconURL
		} else if len(adInfoItem.AdIcon) > 0 {
			respListItemMap["icon_url"] = adInfoItem.AdIcon
		} else if len(adInfoItem.Video.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Video.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.AppInfo.DeepLink)
		}

		isVideo := false

		// 视频 adType 5.原生 视频广告 6. 激励视频广告
		if adInfoItem.PosType == 5 || adInfoItem.PosType == 6 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Video.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Video.VideoDuration/1000)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
				respListVideoItemMap["duration"] = adInfoItem.Video.VideoDuration
			}
			if adInfoItem.Video.VideoWidth > 0 {
				respListVideoItemMap["width"] = adInfoItem.Video.VideoWidth
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Video.VideoHeight > 0 {
				respListVideoItemMap["height"] = adInfoItem.Video.VideoHeight
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}
			if adInfoItem.Video.VideoWidth > 0 && adInfoItem.Video.VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Video.VideoWidth, adInfoItem.Video.VideoHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}
			respListVideoItemMap["video_url"] = adInfoItem.Video.VideoURL

			// cover_url
			if len(adInfoItem.Video.CoverURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Video.CoverURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, videoStartTrackInfoItem := range adInfoItem.Video.EventTracks {
				if videoStartTrackInfoItem.EventType == 1 {
					for _, videoStartURLItem := range videoStartTrackInfoItem.EventTrackUrls {
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoStartURLItem)
					}
				}
			}
			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, videoEndTrackInfoItem := range adInfoItem.Video.EventTracks {
				if videoEndTrackInfoItem.EventType == 5 {
					for _, videoEndURLItem := range videoEndTrackInfoItem.EventTrackUrls {
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoEndURLItem)
					}
				}
			}
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.ImageURLs) > 0 {
				respListImageItemMap["url"] = adInfoItem.ImageURLs[0].ImageURL
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if adInfoItem.ImageURLs[0].ImageWidth > 0 {
				respListImageItemMap["width"] = adInfoItem.ImageURLs[0].ImageWidth
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.ImageURLs[0].ImageHeight > 0 {
				respListImageItemMap["height"] = adInfoItem.ImageURLs[0].ImageHeight
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.ImageURLs[0].ImageWidth > 0 && adInfoItem.ImageURLs[0].ImageHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.ImageURLs[0].ImageWidth, adInfoItem.ImageURLs[0].ImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoItem.IsDownloadAd == 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.Link
			respListItemMap["landpage_url"] = adInfoItem.Link
		} else if adInfoItem.IsDownloadAd == 1 {
			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.Link
			respListItemMap["download_url"] = adInfoItem.Link

			if len(adInfoItem.AppInfo.DownUrl) > 0 {
				respListItemMap["download_url"] = adInfoItem.AppInfo.DownUrl
			}
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.AppInfo.PkgName) > 0 {
			respListItemMap["package_name"] = adInfoItem.AppInfo.PkgName
		}
		if len(adInfoItem.AppInfo.Name) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppInfo.Name
		}
		if len(adInfoItem.AppInfo.SoftCorpName) > 0 {
			respListItemMap["publisher"] = adInfoItem.AppInfo.SoftCorpName
		}
		if len(adInfoItem.AppInfo.SensitiveUrl) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.AppInfo.SensitiveUrl
		}
		if len(adInfoItem.AppInfo.UsesPermission) > 0 {
			respListItemMap["permisson"] = adInfoItem.AppInfo.UsesPermission
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, three60Ecpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.ImpTrackUrls {
			impItem := trackInfoItem
			impItem = strings.Replace(impItem, "__win_price__", encodePriceValue, -1)

			respListItemImpArray = append(respListItemImpArray, impItem)
		}

		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.ClickTrackUrls {
			clkItem := trackInfoItem
			clkItem = strings.Replace(clkItem, "__re_down_x__", "-999", -1)
			clkItem = strings.Replace(clkItem, "__re_down_y__", "-999", -1)
			clkItem = strings.Replace(clkItem, "__re_up_x__", "-999", -1)
			clkItem = strings.Replace(clkItem, "__re_up_y__", "-999", -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = three60Ecpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// 360 resp
	resp360 := models.MHUpResp{}
	resp360.RespData = &mhResp
	resp360.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &resp360
}

// Three60RespStu ...
type Three60RespStu struct {
	Code     int                `json:"code"`
	Message  string             `json:"msg"`
	ErrorMsg string             `json:"errorMsg"`
	Data     Three60RespDataStu `json:"data"`
}

// Three60RespDataStu ...
type Three60RespDataStu struct {
	RequestID string                    `json:"requestId"`
	Ts        int64                     `json:"ts"`
	Groups    []Three60RespDataGroupStu `json:"groups"`
}

// Three60RespDataGroupStu ...
type Three60RespDataGroupStu struct {
	ImpID int                         `json:"impId"`
	Ads   []Three60RespDataGroupAdStu `json:"ads"`
}

// Three60RespDataGroupAdStu ...
type Three60RespDataGroupAdStu struct {
	ID             int                              `json:"id"`
	CreativeID     int                              `json:"creativeId"`
	Title          string                           `json:"title"`
	Description    string                           `json:"desc"`
	Link           string                           `json:"link"`
	ClickTrackUrls []string                         `json:"clickTrackUrls"`
	ImpTrackUrls   []string                         `json:"impTrackUrls"`
	Width          int                              `json:"adWidth"`
	Height         int                              `json:"adHeight"`
	PosType        int                              `json:"adType"`
	IsDownloadAd   int                              `json:"downloadAd"`
	Source         string                           `json:"source"`
	NativeAdType   string                           `json:"nativeAdType"`
	ImageURLs      []Three60RespDataGroupAdImageStu `json:"imgs"`
	AppInfo        Three60RespDataGroupAdAppInfoStu `json:"appInfo"`
	Video          Three60RespDataGroupAdVideoStu   `json:"video"`
	AdIcon         string                           `json:"adIcon"`
	IconURL        string                           `json:"matterIcon"`
	Price          float32                          `json:"price"`
}

// Three60RespDataGroupAdImageStu ...
type Three60RespDataGroupAdImageStu struct {
	ImageURL    string `json:"url"`
	ImageWidth  int    `json:"width"`
	ImageHeight int    `json:"height"`
}

// Three60RespDataGroupAdAppInfoStu ...
type Three60RespDataGroupAdAppInfoStu struct {
	PkgName        string                                `json:"pkgName"`
	Size           int64                                 `json:"size"`
	Version        string                                `json:"version"`
	DeepLink       string                                `json:"deeplink"`
	Name           string                                `json:"name"`
	DownUrl        string                                `json:"downUrl"`
	SoftCorpName   string                                `json:"softCorpName"`
	SensitiveUrl   string                                `json:"sensitiveUrl"`
	UsesPermission string                                `json:"usesPermission"`
	EventTracks    []Three60RespDataGroupAdEventTrackStu `json:"eventTracks"`
}

// Three60RespDataGroupAdVideoStu ...
type Three60RespDataGroupAdVideoStu struct {
	VideoDuration int                                   `json:"duration"`
	VideoWidth    int                                   `json:"width"`
	VideoHeight   int                                   `json:"height"`
	VideoURL      string                                `json:"videoUrl"`
	CoverURL      string                                `json:"coverUrl"`
	VideoLength   int                                   `json:"length"`
	EventTracks   []Three60RespDataGroupAdEventTrackStu `json:"eventTracks"`
	VideoType     int                                   `json:"videoType"`
	Skip          int                                   `json:"skip"`
	SkipMinTime   int                                   `json:"skipMinTime"`
	PreloadTTL    int                                   `json:"preloadTtl"`
	EndcardURL    string                                `json:"endcardUrl"`
	BackgroundURL string                                `json:"backgroundUrl"`
	VideoDesc     string                                `json:"videoDesc"`
	VideoCurl     string                                `json:"c_url"`
	IconURL       string                                `json:"iconUrl"`
}

// Three60RespDataGroupAdEventTrackStu ...
type Three60RespDataGroupAdEventTrackStu struct {
	EventType      int      `json:"eventType"`
	EventTrackUrls []string `json:"eventTrackUrls"`
}
