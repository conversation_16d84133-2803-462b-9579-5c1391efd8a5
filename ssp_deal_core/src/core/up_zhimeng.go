package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromZhiMeng ...
func GetFromZhiMeng(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from zhimeng")

	// test
	// platformPos.PlatformAppID = "1138"
	// platformPos.PlatformAppName = "WiFi万能钥匙"
	// platformPos.PlatformAppBundle = "com.snda.wifilocating"
	// platformPos.PlatformPosID = "113802"
	// bigdataUID = "31ef37a4-886f-40bc-8dd8-b3b45e179fa1"

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	////////////////////////////////////////////////////////////////////////////////////////
	reqImpInfoArray := []map[string]interface{}{}
	reqImpInfoItemMap := map[string]interface{}{}
	reqImpInfoItemMap["pos_id"] = platformPos.PlatformPosID
	reqImpInfoItemMap["width"] = platformPos.PlatformPosWidth
	reqImpInfoItemMap["height"] = platformPos.PlatformPosHeight
	reqImpInfoItemMap["app_name"] = platformPos.PlatformAppName
	reqImpInfoItemMap["package_name"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	if len(platformPos.PlatformAppVersion) > 0 {
		// reqImpInfoItemMap["app_version"] = platformPos.PlatformAppVersion
	}
	if localPosFloorPrice > 0 {
		reqImpInfoItemMap["cpm_bid_floor"] = localPosFloorPrice
	}
	reqImpInfoArray = append(reqImpInfoArray, reqImpInfoItemMap)

	reqDeviceInfoMap := map[string]interface{}{}
	reqDeviceInfoMap["type"] = 2
	reqDeviceInfoMap["ip"] = mhReq.Device.IP
	reqDeviceInfoMap["ua"] = destConfigUA
	reqDeviceInfoMap["network"] = mhReq.Network.ConnectType
	if mhReq.Network.ConnectType == 7 {
		// 5G
		reqDeviceInfoMap["network"] = 5
	}
	if mhReq.Network.Carrier == 0 {
		reqDeviceInfoMap["operator"] = 0
	} else if mhReq.Network.Carrier == 1 {
		reqDeviceInfoMap["operator"] = 1
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceInfoMap["operator"] = 3
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceInfoMap["operator"] = 2
	}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				// reqDeviceInfoMap["imei"] = mhReq.Device.Imei
				reqDeviceInfoMap["imei_md5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				reqDeviceInfoMap["imei_md5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	reqDeviceInfoMap["android_id"] = mhReq.Device.AndroidID

			// 	extraReportParams = extraReportParams + ",android_id_md5"
			// }
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}

	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
					reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
				} else {
					reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa
				}
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caids"] = []map[string]interface{}{
							{
								"caid":         item.CAID,
								"caid_version": item.CAIDVersion,
							},
						}

						break
					}
				}
			} else {
				for _, item := range mhReq.Device.CAIDMulti {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					reqDeviceInfoMap["caids"] = []map[string]interface{}{
						{
							"caid":         item.CAID,
							"caid_version": item.CAIDVersion,
						},
					}
					break
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
				reqDeviceInfoMap["locale_country"] = tmpCountry
				reqDeviceInfoMap["language"] = tmpLanguage
				reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
				// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
				reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
				reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
				reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
				reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
				reqDeviceInfoMap["time_zone"] = tmpTimeZone
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
						reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
					} else {
						reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa
					}
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							reqDeviceInfoMap["caids"] = []map[string]interface{}{
								{
									"caid":         item.CAID,
									"caid_version": item.CAIDVersion,
								},
							}
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caids"] = []map[string]interface{}{
							{
								"caid":         item.CAID,
								"caid_version": item.CAIDVersion,
							},
						}
						break
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
					reqDeviceInfoMap["locale_country"] = tmpCountry
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
					reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
					reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
					reqDeviceInfoMap["time_zone"] = tmpTimeZone
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
						reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
					} else {
						reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa
					}
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							reqDeviceInfoMap["caids"] = []map[string]interface{}{
								{
									"caid":         item.CAID,
									"caid_version": item.CAIDVersion,
								},
							}
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caids"] = []map[string]interface{}{
							{
								"caid":         item.CAID,
								"caid_version": item.CAIDVersion,
							},
						}
						break
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
					reqDeviceInfoMap["locale_country"] = tmpCountry
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
					reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
					reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
					reqDeviceInfoMap["time_zone"] = tmpTimeZone
				}
			}
		}

		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			fmt.Println("get from zhimeng error req idfa")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if mhReq.Device.Os == "android" {
		reqDeviceInfoMap["os"] = 2
		if len(mhReq.Device.Manufacturer) > 0 {
			reqDeviceInfoMap["brand"] = mhReq.Device.Manufacturer
		}
	} else if mhReq.Device.Os == "ios" {
		reqDeviceInfoMap["os"] = 1
		reqDeviceInfoMap["brand"] = "Apple"
	}

	if len(mhReq.Device.Model) > 0 {
		reqDeviceInfoMap["model"] = mhReq.Device.Model
	}
	if len(mhReq.Device.OsVersion) > 0 {
		reqDeviceInfoMap["os_version"] = mhReq.Device.OsVersion
	}
	// if len(mhReq.Device.HMSCoreVersion) > 0 {
	// 	reqDeviceInfoMap["vercode_of_hms"] = mhReq.Device.HMSCoreVersion
	// }
	// if len(mhReq.Device.AppStoreVersion) > 0 {
	// 	reqDeviceInfoMap["vercode_of_ag"] = mhReq.Device.AppStoreVersion
	// }
	// if mhReq.Device.ScreenWidth > 0 {
	// 	reqDeviceInfoMap["screen_width"] = mhReq.Device.ScreenWidth
	// }
	// if mhReq.Device.ScreenHeight > 0 {
	// 	reqDeviceInfoMap["screen_height"] = mhReq.Device.ScreenHeight
	// }

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// delete(reqDeviceInfoMap, "imei")
					delete(reqDeviceInfoMap, "imei_md5")
					delete(reqDeviceInfoMap, "oaid")

					reqDeviceInfoMap["os_version"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["brand"] = didRedisData.Manufacturer
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imei_md5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imei_md5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					// osv model key
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						delete(reqDeviceInfoMap, "device_start_sec")
						delete(reqDeviceInfoMap, "device_name_md5")
						delete(reqDeviceInfoMap, "sys_memory")
						delete(reqDeviceInfoMap, "sys_hard_size")
						delete(reqDeviceInfoMap, "sys_update_sec")
						delete(reqDeviceInfoMap, "hardware_model")
						delete(reqDeviceInfoMap, "locale_country")
						delete(reqDeviceInfoMap, "language")
						delete(reqDeviceInfoMap, "time_zone")
						delete(reqDeviceInfoMap, "idfa")
						delete(reqDeviceInfoMap, "idfa_md5")
						delete(reqDeviceInfoMap, "caid")
						delete(reqDeviceInfoMap, "caid_version")

						reqDeviceInfoMap["os_version"] = didRedisData.OsVersion
						reqDeviceInfoMap["model"] = didRedisData.Model
						if platformPos.PlatformAppIsReplaceDIDUa == 1 {
							reqDeviceInfoMap["ua"] = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
									reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)
								} else {
									reqDeviceInfoMap["idfa"] = didRedisData.Idfa
								}

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true

							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caids"] = []map[string]interface{}{
											{
												"caid":         item.CAID,
												"caid_version": item.CAIDVersion,
											},
										}
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caids"] = []map[string]interface{}{
										{
											"caid":         item.CAID,
											"caid_version": item.CAIDVersion,
										},
									}
									break
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
								reqDeviceInfoMap["locale_country"] = tmpCountry
								reqDeviceInfoMap["language"] = tmpLanguage
								reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
								// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
								reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
								reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
								reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
								reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
								reqDeviceInfoMap["time_zone"] = tmpTimeZone

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
										reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)
									} else {
										reqDeviceInfoMap["idfa"] = didRedisData.Idfa
									}

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true

								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											reqDeviceInfoMap["caids"] = []map[string]interface{}{
												{
													"caid":         item.CAID,
													"caid_version": item.CAIDVersion,
												},
											}
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caids"] = []map[string]interface{}{
											{
												"caid":         item.CAID,
												"caid_version": item.CAIDVersion,
											},
										}
										break
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
									reqDeviceInfoMap["locale_country"] = tmpCountry
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
									reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
									reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
									reqDeviceInfoMap["time_zone"] = tmpTimeZone

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									if platformPos.PlatformAppIsReportIDFAMd5 == 1 {
										reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)
									} else {
										reqDeviceInfoMap["idfa"] = didRedisData.Idfa
									}

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true

								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											reqDeviceInfoMap["caids"] = []map[string]interface{}{
												{
													"caid":         item.CAID,
													"caid_version": item.CAIDVersion,
												},
											}
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caids"] = []map[string]interface{}{
											{
												"caid":         item.CAID,
												"caid_version": item.CAIDVersion,
											},
										}
										break
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									reqDeviceInfoMap["device_start_sec"] = tmpDeviceStartSec
									reqDeviceInfoMap["locale_country"] = tmpCountry
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									reqDeviceInfoMap["sys_memory"] = tmpPhysicalMemoryByte
									reqDeviceInfoMap["sys_hard_size"] = tmpHarddiskSizeByte
									reqDeviceInfoMap["sys_update_sec"] = tmpSystemUpdateSec
									reqDeviceInfoMap["time_zone"] = tmpTimeZone

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					isHaveReplace = true
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from zhimeng error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from zhimeng error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	zhihuChannel := platformPos.PlatformAppID

	zhihuDomainArray := strings.Split(platformPos.PlatformAppUpURL, "/")
	zhihuDomain := zhihuDomainArray[len(zhihuDomainArray)-1]

	zhihuKey := platformPos.PlatformAppKey

	postData := map[string]interface{}{
		"protocol_version": "1.0.0",
		"channel":          zhihuChannel,
		"request_id":       bigdataUID,
		"imp_info":         reqImpInfoArray,
		"device_info":      reqDeviceInfoMap,
	}

	postData["ciphertext"] = getZhiMengCipher(c, zhihuChannel, zhihuDomain, zhihuKey)
	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("zhimeng req: " + string(jsonData))
	// fmt.Println("zhimeng req url: " + platformPos.PlatformAppUpURL)
	// fmt.Println("zhimeng req key: " + zhihuKey)

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)

	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("zhimeng resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	zhimengRespStu := ZhiMengRespStu{}
	json.Unmarshal([]byte(bodyContent), &zhimengRespStu)

	if zhimengRespStu.Code != 1 {
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(zhimengRespStu.ContentInfos) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}

	zhimengRespItemList := zhimengRespStu.ContentInfos

	if len(zhimengRespItemList) > 1 {
		sort.Sort(ZhiMengEcpmSort(zhimengRespItemList))
	}

	for _, zhimengContentInfoItem := range zhimengRespItemList {
		contentInfo := zhimengContentInfoItem

		zhimengEcpm := contentInfo.Ecpm

		respTmpPrice = respTmpPrice + zhimengEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if zhimengEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			zhimengEcpm = platformPos.PlatformPosEcpm
		}

		zhimengLossNoticeURL := getZhiMengPriceFailedURL(contentInfo.Tracks.LossNoticeURL, platformPos, zhimengEcpm)

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > zhimengEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(zhimengEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(contentInfo.Title) > 0 {
			respListItemMap["title"] = contentInfo.Title
		}

		// description
		if len(contentInfo.Description) > 0 {
			respListItemMap["description"] = contentInfo.Description
		}

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		isVideo := false

		// show_type  1-文字链 3-单图 4-多图 5-视频
		if contentInfo.ShowType == 5 {
			if len(contentInfo.VideoInfo) == 0 {

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if contentInfo.VideoInfo[0].VideoDuration > 0 {
				respListVideoItemMap["duration"] = contentInfo.VideoInfo[0].VideoDuration * 1000
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, contentInfo.VideoInfo[0].VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
					continue
				}
			}
			respListVideoItemMap["width"] = contentInfo.VideoInfo[0].VideoWidth
			respListVideoItemMap["height"] = contentInfo.VideoInfo[0].VideoHeight
			respListVideoItemMap["video_url"] = contentInfo.VideoInfo[0].VideoURL

			if contentInfo.VideoInfo[0].VideoWidth > 0 && contentInfo.VideoInfo[0].VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, contentInfo.VideoInfo[0].VideoWidth, contentInfo.VideoInfo[0].VideoHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
					continue
				}
			}
			if len(contentInfo.VideoInfo[0].VideoURL) == 0 {
				fmt.Println("wrong video url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}

			// cover_url
			respListVideoItemMap["cover_url"] = contentInfo.VideoInfo[0].CoverURL
			if len(contentInfo.VideoInfo[0].CoverURL) == 0 {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track
			// 有返回，仅激励视频上报__ACTION__中399（开始播放）和400（播放完成），其他情况不上报
			if platformPos.PlatformPosType == 11 {
				var respListEventTrackURLMap []map[string]interface{}

				// 随机95-98%替换__PR__
				randPRValue := 95 + rand.Intn(4)
				macroPrice := utils.ConvertIntToString(int(zhimengEcpm * randPRValue / 100))

				// track params
				mhTrackParams := url.Values{}
				mhTrackParams.Add("log", up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, zhimengEcpm, tmpEcpm))

				// 399
				respListVideoBeginEventTrackMap := map[string]interface{}{}
				respListVideoBeginEventTrackMap["event_type"] = 100
				var respListVideoBeginEventTrackURLMap []string
				for _, convItem := range contentInfo.Tracks.Conversion {
					if convItem.EventType == 399 {
						tmpURLArray := convItem.URL
						for _, tmpItem := range tmpURLArray {
							tmpItem = strings.Replace(tmpItem, "__PR__", macroPrice, -1)
							tmpItem = strings.Replace(tmpItem, "__ACTION__", "399", -1)
							respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, tmpItem)
						}
					}
				}

				// respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, config.ExternalVideoStartTrackURL+"?"+mhTrackParams.Encode())

				if len(respListVideoBeginEventTrackURLMap) > 0 {
					respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
				}

				// 400
				respListVideoEndEventTrackMap := map[string]interface{}{}
				respListVideoEndEventTrackMap["event_type"] = 103
				var respListVideoEndEventTrackURLMap []string
				for _, convItem := range contentInfo.Tracks.Conversion {
					if convItem.EventType == 400 {
						tmpURLArray := convItem.URL
						for _, tmpItem := range tmpURLArray {
							tmpItem = strings.Replace(tmpItem, "__PR__", macroPrice, -1)
							tmpItem = strings.Replace(tmpItem, "__ACTION__", "400", -1)
							tmpItem = strings.Replace(tmpItem, "__VIDEO_TIME__", utils.ConvertIntToString(contentInfo.VideoInfo[0].VideoDuration), -1)
							tmpItem = strings.Replace(tmpItem, "__BEGIN_TIME__", "0", -1)
							tmpItem = strings.Replace(tmpItem, "__END_TIME__", utils.ConvertIntToString(contentInfo.VideoInfo[0].VideoDuration), -1) // ???
							tmpItem = strings.Replace(tmpItem, "__PLAY_FIRST_FRAME__", "1", -1)
							tmpItem = strings.Replace(tmpItem, "__PLAY_LAST_FRAME__", "1", -1) // ???
							// 0 横屏, 1 竖屏
							if platformPos.PlatformPosDirection == 0 {
								tmpItem = strings.Replace(tmpItem, "__SCENE__", "4", -1)
							} else if platformPos.PlatformPosDirection == 1 {
								tmpItem = strings.Replace(tmpItem, "__SCENE__", "2", -1)
							}
							tmpItem = strings.Replace(tmpItem, "__TYPE__", "1", -1)
							tmpItem = strings.Replace(tmpItem, "__BEHAVIOR__", "1", -1)
							tmpItem = strings.Replace(tmpItem, "__STATUS__", "0", -1)
							respListVideoEndEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, tmpItem)
						}
					}
				}
				// respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, config.ExternalVideoEndTrackURL+"?"+mhTrackParams.Encode())

				if len(respListVideoEndEventTrackURLMap) > 0 {
					respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
				}

				if len(respListEventTrackURLMap) > 0 {
					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20

		} else if contentInfo.ShowType == 3 || contentInfo.ShowType == 4 {
			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(contentInfo.ImgInfo) == 0 {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
			respListImageItemMap["url"] = contentInfo.ImgInfo[0].URL
			respListImageItemMap["width"] = contentInfo.ImgInfo[0].Width
			respListImageItemMap["height"] = contentInfo.ImgInfo[0].Height

			if contentInfo.ImgInfo[0].Width > 0 && contentInfo.ImgInfo[0].Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, contentInfo.ImgInfo[0].Width, contentInfo.ImgInfo[0].Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		} else {

			curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
			continue
		}

		if contentInfo.OperationType == 2 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = contentInfo.LandingURL
			respListItemMap["landpage_url"] = contentInfo.LandingURL
		} else if contentInfo.OperationType == 1 {
			if len(contentInfo.AppDownloadURL) == 0 {

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = contentInfo.AppDownloadURL
			respListItemMap["download_url"] = contentInfo.AppDownloadURL

			if len(contentInfo.LandingURL) > 0 {
				respListItemMap["landpage_url"] = contentInfo.LandingURL
			}
		} else {
			curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
			continue
		}

		if len(contentInfo.AppInfo.Name) > 0 {
			respListItemMap["app_name"] = contentInfo.AppInfo.Name
		}
		if len(contentInfo.AppInfo.CorporationName) > 0 {
			respListItemMap["publisher"] = contentInfo.AppInfo.CorporationName
		}
		if len(contentInfo.AppInfo.Version) > 0 {
			respListItemMap["app_version"] = contentInfo.AppInfo.Version
		}
		if len(contentInfo.AppInfo.PrivacyURL) > 0 {
			respListItemMap["privacy_url"] = contentInfo.AppInfo.PrivacyURL
		}
		if len(contentInfo.AppInfo.PermissionInfo) > 0 {
			respListItemMap["permission"] = contentInfo.AppInfo.PermissionInfo
		}
		if contentInfo.AppInfo.PackageSize > 0 {
			respListItemMap["package_size"] = contentInfo.AppInfo.PackageSize
		}

		// crid
		if contentInfo.CreativeID > 0 {
			respListItemMap["crid"] = utils.ConvertInt64ToString(contentInfo.CreativeID)
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, zhimengEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		zhimengWinNoticeURL := getZhiMengPriceWinURL(contentInfo.Tracks.WinNoticeURL, platformPos, zhimengEcpm)
		if len(zhimengWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, zhimengWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link zhimeng
		for _, zhimengImpURLItem := range contentInfo.Tracks.Imps {
			tmpImpURL := zhimengImpURLItem
			tmpImpURL = strings.Replace(tmpImpURL, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)

			respListItemImpArray = append(respListItemImpArray, tmpImpURL)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link zhimeng
		for _, zhimengClkURLItem := range contentInfo.Tracks.Clicks {
			tmpClkURL := zhimengClkURLItem
			tmpClkURL = strings.Replace(tmpClkURL, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)

			tmpClkURL = strings.Replace(tmpClkURL, "__DOWN_X__", tmpDownX, -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__DOWN_Y__", tmpDownY, -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__UP_X__", tmpUpX, -1)
			tmpClkURL = strings.Replace(tmpClkURL, "__UP_Y__", tmpUpY, -1)

			if platformPos.PlatformPosIsReportSLD == 1 {
				tmpClkURL = strings.Replace(tmpClkURL, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
			}
			tmpClkURL = strings.Replace(tmpClkURL, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					tmpClkURL = tmpClkURL + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					tmpClkURL = tmpClkURL + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, tmpClkURL)
		}

		for _, convItem := range contentInfo.Tracks.Conversion {
			if convItem.EventType == 39 {
				tmpURLArray := convItem.URL
				for _, tmpURLItem := range tmpURLArray {
					tmpItem := tmpURLItem
					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							tmpItem = tmpItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}
					respListItemClkArray = append(respListItemClkArray, tmpItem)
				}
			}
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(zhimengLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, zhimengLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// deep link
		destDeepLink := ""
		if len(contentInfo.DeepLink) > 0 || len(contentInfo.MarketURL) > 0 {
			if len(contentInfo.DeepLink) > 0 {
				destDeepLink = contentInfo.DeepLink

				respListItemMap["deep_link"] = contentInfo.DeepLink
			} else if len(contentInfo.MarketURL) > 0 {
				destDeepLink = contentInfo.MarketURL

				respListItemMap["deep_link"] = contentInfo.MarketURL
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, convItem := range contentInfo.Tracks.Conversion {
				if convItem.EventType == 36 {
					tmpURLArray := convItem.URL
					for _, tmpItem := range tmpURLArray {
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, tmpItem)
					}
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(contentInfo.AppInfo.PackageName) > 0 {
			respListItemMap["package_name"] = contentInfo.AppInfo.PackageName

			destPackageName = contentInfo.AppInfo.PackageName

		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, contentInfo.AppInfo.Name, destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(contentInfo.AppInfo.IconURL) > 0 {
			respListItemMap["icon_url"] = contentInfo.AppInfo.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {

				curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {

			curlZhiMengPriceFailedURL(zhimengLossNoticeURL, &bigdataExtra)
			continue
		}

		respListItemMap["p_ecpm"] = zhimengEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// zhimeng resp
	respZhimeng := models.MHUpResp{}
	respZhimeng.RespData = &mhResp
	respZhimeng.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respZhimeng
}

// getZhiMengCipher ...
func getZhiMengCipher(c context.Context, zhihuChannel string, zhihuDomain string, zhihuKey string) string {
	key := []byte(zhihuKey)
	now := time.Now()
	values := url.Values{}
	values.Add("channel", zhihuChannel)
	values.Add("domain", zhihuDomain)
	values.Add("timestamp", strconv.FormatInt(now.Unix(), 10))
	plaintext := values.Encode()
	// fmt.Println("plaintext:", plaintext)
	ciphertext, err := utils.AESCFBEncrypt(key, []byte(plaintext))
	if err != nil {
		fmt.Println("error:", err)
	}
	// fmt.Println("ciphertext:", base64.StdEncoding.EncodeToString(ciphertext))
	// originData, err := utils.AESCFBDecrypt(key, ciphertext)
	// if err != nil {
	// 	fmt.Println("error:", err)
	// }
	// fmt.Println("plaintext:", string(originData))

	respCipher := base64.StdEncoding.EncodeToString(ciphertext)
	return respCipher
}

type ZhiMengEcpmSort []ZhiMengRespContentStu

func (s ZhiMengEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s ZhiMengEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s ZhiMengEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}

// ZhiMengRespStu ...
type ZhiMengRespStu struct {
	RequestID    string                  `json:"request_id"`
	Code         int                     `json:"code"`
	ContentInfos []ZhiMengRespContentStu `json:"content_info"`
}

type ZhiMengRespContentStu struct {
	PosID          string                       `json:"pos_id"`
	Title          string                       `json:"title"`
	Description    string                       `json:"description"`
	LandingURL     string                       `json:"landing_url"`
	DeepLink       string                       `json:"deep_link"`
	Tracks         ZhiMengRespContentTracksStu  `json:"tracker"`
	AuthorInfo     ZhiMengRespContentAuthorStu  `json:"author_info"`
	ImgInfo        []ZhiMengRespContentImageStu `json:"img"`
	DataType       int                          `json:"data_type"`
	ShowType       int                          `json:"show_type"`
	OperationType  int                          `json:"operation_type"`
	AppInfo        ZhiMengRespContentAppStu     `json:"app_info"`
	AppDownloadURL string                       `json:"app_download_url"`
	LogoURL        string                       `json:"ad_logo_url"`
	CreativeID     int64                        `json:"creative_id"`
	Ecpm           int                          `json:"ecpm"`
	ProductName    string                       `json:"product_name"`
	VideoInfo      []ZhiMengRespContentVideoStu `json:"video_info"`
	MarketURL      string                       `json:"market_url"`
}
type ZhiMengRespContentAuthorStu struct {
	Name string `json:"name"`
	Logo string `json:"logo"`
}

type ZhiMengRespContentTracksStu struct {
	Imps          []string                                `json:"imps"`
	Clicks        []string                                `json:"clicks"`
	Conversion    []ZhiMengRespContentTracksConversionStu `json:"conversion"`
	WinNoticeURL  string                                  `json:"win_notice_url"`
	LossNoticeURL string                                  `json:"loss_notice_url"`
}

type ZhiMengRespContentImageStu struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type ZhiMengRespContentAppStu struct {
	Name            string `json:"name"`
	IconURL         string `json:"icon_url"`
	PackageName     string `json:"package_name"`
	PackageSize     int    `json:"package_size"`
	Version         string `json:"version"`
	PermissionInfo  string `json:"permission_info"`
	PrivacyURL      string `json:"privacy_url"`
	CorporationName string `json:"corporation_name"`
}

type ZhiMengRespContentVideoStu struct {
	VideoURL      string `json:"url"`
	CoverURL      string `json:"cover_url"`
	FirstFrame    string `json:"first_frame"`
	VideoDuration int    `json:"duration"`
	VideoWidth    int    `json:"width"`
	VideoHeight   int    `json:"height"`
}

type ZhiMengRespContentTracksConversionStu struct {
	EventType int      `json:"event_type"`
	URL       []string `json:"url"`
}

func getZhiMengPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, zhimengEcpm int) string {
	if zhimengEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	randPRValue := 100
	if platformPos.PlatformAppReportWinType == 0 {
		randPRValue = 100
	} else if platformPos.PlatformAppReportWinType == 1 {
		tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
		tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
		if tmp1 <= tmp2 {
			randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
		}
	}
	macroPrice := utils.ConvertIntToString(int(zhimengEcpm * randPRValue / 100))

	cipherText := utils.RSAEncrypt([]byte(macroPrice), config.GDTPublicKeyPemPath)
	macroPrice = utils.Base64URLEncode(cipherText)

	tmpWinNoticeURL := winNoticeURL
	tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "__AUCTION_PRICE__", macroPrice, -1)
	return tmpWinNoticeURL
}

func getZhiMengPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, zhimengEcpm int) string {
	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	if zhimengEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 {
		macroPrice := utils.ConvertIntToString(int(zhimengEcpm * tmpRandValue / 100))

		cipherText := utils.RSAEncrypt([]byte(macroPrice), config.GDTPublicKeyPemPath)
		macroPrice = utils.Base64URLEncode(cipherText)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_PRICE__", macroPrice, -1)
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_LOSS__", "1", -1)

		return tmpLossNoticeURL
	}

	return ""
}

// curl price failed
func curlZhiMengPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("ks loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}
