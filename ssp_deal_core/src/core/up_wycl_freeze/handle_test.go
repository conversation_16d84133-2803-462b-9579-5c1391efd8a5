package up_wycl_freeze

import (
	"encoding/hex"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"testing"

	"github.com/gin-gonic/gin"
)

func Test_GetFromFancy(ct *testing.T) {
	db.InitRedis()
	db.InitBigCache()
	db.InitMysql()

	c := gin.Context{}

	mhReqApp := models.MHReqApp{}
	mhReqDevice := models.MHReqDevice{
		DeviceType: 1,
		IP:         "***********",
		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
		OsVersion:  "12.14.0",
		Imei:       "865166020069038",
		Oaid:       "2c06c2722e300206e4c6c79f07c7d2efd7ec74b82daa67e1019b1efd92a2b488",
		Os:         "android",
		Idfa:       "idfs_string",
	}

	mhReq := models.MHReq{
		App:    mhReqApp,
		Device: mhReqDevice,
	}

	localPosInfo := models.LocalPosStu{
		LocalPosID:                       "1010012",
		LocalAppID:                       "10387",
		LocalPosWidth:                    1280,
		LocalPosHeight:                   720,
		LocalPosType:                     4,
		LocalOs:                          "0",
		LocalAppName:                     "七读免费小说API-Android",
		LocalAppBundleID:                 "com.dj.sevenRead",
		LocalAppType:                     "4",
		LocalAppSplashClickRegion:        0,
		LocalAppNativeDownloadCompliance: 0,
		LocalPosIsActive:                 1,
		LocalPosMHLandPage:               0,
		LocalPosTimeOut:                  260,
		LocalPosMaterialType:             0,
		LocalAppSupportExtraClipboard:    0,
		LocalAppSupportExtraNotification: 0,
		LocalAppSupportExtraWakeUp:       0,
		LocalAppIsExtraLimitFrequency:    1,
		LocalAppExtraLimitFrequency:      "60",
	}

	platformPosInfo := models.PlatformPosStu{
		// PlatformAppID:             "1001379",
		// PlatformPosID:             "03cf29746ab1",
		// PlatformAppVersion:        "1.0",
		// PlatformAppIsActive:       1,
		// PlatformPosIsActive:       1,
		// PlatformAppUpURL:          "http://ad.tianmu.mobi/ad",
		// PlatformAppName:           "maplehaze",
		// PlatformAppBundle:         "com.maplehaze",
		// PlatformAppIsPriceEncrypt: 1,
		// PlatformAppPriceEncrypt:   "0276af914fee82a4",
		// PlatformPosType:           2,
		// PlatformPosWidth:          1080,
		// PlatformPosHeight:         1920,

		PlatformPosID: "17038",
		//PlatformAppID:               "1001379",
		PlatformAppCorpID:       7,
		PlatformMediaID:         "14",
		PlatformAppBundle:       "com.maplehaze",
		PlatformPosStyleIDs:     "",
		PlatformOs:              "0",
		PlatformPosWidth:        1080,
		PlatformPosHeight:       1920,
		PlatformPosType:         4,
		PlatformPosMaterialType: 2,
		PlatformPosIsDebug:      0,
		PlatformPosIsReplaceXY:  0,
		PlatformPosDirection:    0,
		PlatformAppVersion:      "1.0",
		PlatformAppUpURL:        "http://nextest.ws.netease.com/ex/adx/wy15",
		PlatformAppFilterUa:     0,
		PlatformAppIsActive:     1,
		PlatformPosIsActive:     1,
		// PlatformAppIsPriceEncrypt:   1,
		// PlatformAppPriceEncrypt:     "0276af914fee82a4",
		PlatformAppIsReplaceDID:     0,
		PlatformAppIsReplaceDIDUa:   1,
		PlatformAppMaxDAUType:       0,
		PlatformAppMaxDAU:           0,
		PlatformAppIsDeepLinkFailed: 0,
	}

	categoryInfo := models.CategoryStu{
		LocalPosID:              "57650",
		LocalAppID:              "10387",
		PlatformPosID:           "1da7e2b8045c",
		PlatformAppID:           "1001379",
		PlatformMediaID:         "37",
		PlatformPosType:         4,
		PlatformPosMaterialType: 2,
		// Label:                   1,
		// Priority:                1,
		// SubPriority:             7,
		// Status:                  1,
		// SubStatus:               1,
		// Sub1Status:              1,
		// APIWeight:               65,
		// APISubWeight:            100,
		// MaxReqNum:               -1,
		// MaxExpNum:               -1,
		// MaxClkNum:               -1,
		FloorPrice:   20,
		FinalPrice:   50,
		WhiteList:    "",
		BlackList:    "",
		WhiteVersion: "",
		BlackVersion: "",
	}

	bigdataUID := "123456"

	respData := GetFromWycl(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

	fmt.Printf("\t\t%#v,\n", platformPosInfo)
	fmt.Printf("\t\t%#v,\n", respData)
}

//

func Test_(ct *testing.T) {
	price, _ := utils.Base64URLDecode("gPvIKSe/7NurzzYlJUnNZg==")
	keyByte := []byte("YzI1MDg4NmZhMDVj")
	keyStr := hex.EncodeToString(keyByte)
	ivByte := []byte("NGEzYWU3MTY3M2Yx")
	ivStr := hex.EncodeToString(ivByte)
	decrypt, err := AesDecrypt(price, keyStr, ivStr)
	if err != nil {
		return
	}

	fmt.Println(decrypt)
}
