package up_wycl_freeze

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"mh_proxy/utils"
)

func AesEncrypt(origData []byte, keyStr string, ivStr string) ([]byte, error) {
	key, _ := hex.DecodeString(keyStr)
	iv, _ := hex.DecodeString(ivStr)
	block, err := aes.NewCipher(key)
	if err != nil {
		fmt.Println("WyclDEBUG: AesEncrypt err:", err)
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = utils.PKCS7Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv)
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

func AesDecrypt(crypted []byte, keyStr string, ivStr string) ([]byte, error) {
	key, _ := hex.DecodeString(keyStr)
	iv, _ := hex.DecodeString(ivStr)
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData, _ = utils.PKCS7UnPadding(origData)
	return origData, nil
}

func Base64URLEncode(source []byte) string {
	return base64.URLEncoding.EncodeToString(source)
}
