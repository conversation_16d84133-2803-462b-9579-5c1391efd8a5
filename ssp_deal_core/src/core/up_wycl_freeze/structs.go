package up_wycl_freeze

import (
	"mh_proxy/core/up_common"
)

type WyclRequestOsEnum string

const (
	Wycl_OS_IOS     WyclRequestOsEnum = "ios"
	Wycl_OS_ANDROID WyclRequestOsEnum = "android"
)

type WyclRequestCarrierEnum string

const (
	//Wycl_CARRIER_UNKNOWN WyclRequestCarrierEnum = iota
	Wycl_CARRIER_CM      WyclRequestCarrierEnum = "cm"
	Wycl_CARRIER_CU      WyclRequestCarrierEnum = "cu"
	Wycl_CARRIER_CT      WyclRequestCarrierEnum = "ct"
	WYCL_CARRIER_UNKNOWN WyclRequestCarrierEnum = "unknown"
)

type WyclRequestConnectiontypeEnum string

const (
	Wycl_CONNECTIONTYPE_UNKNOWN WyclRequestConnectiontypeEnum = "unknown"
	Wycl_CONNECTIONTYPE_WIFI    WyclRequestConnectiontypeEnum = "wifi"
	Wycl_CONNECTIONTYPE_2G      WyclRequestConnectiontypeEnum = "2G"
	Wycl_CONNECTIONTYPE_3G      WyclRequestConnectiontypeEnum = "3G"
	Wycl_CONNECTIONTYPE_4G      WyclRequestConnectiontypeEnum = "4G"
	Wycl_CONNECTIONTYPE_5G      WyclRequestConnectiontypeEnum = "5G"
)

type WyclDeviceTypeEnum int

const (
	Wycl_DEVICETYPE_UNKNOWN WyclDeviceTypeEnum = iota
	Wycl_DEVICETYPE_MOBILE
	Wycl_DEVICETYPE_PAD
)

func NewCarrier(carrier int) WyclRequestCarrierEnum {
	switch carrier {
	case 1:
		return Wycl_CARRIER_CM
	case 2:
		return Wycl_CARRIER_CU
	case 3:
		return Wycl_CARRIER_CT
	}
	return WYCL_CARRIER_UNKNOWN
}

func NewNetwork(network int) WyclRequestConnectiontypeEnum {
	switch network {
	case 0:
		return Wycl_CONNECTIONTYPE_UNKNOWN
	case 1:
		return Wycl_CONNECTIONTYPE_WIFI
	case 2:
		return Wycl_CONNECTIONTYPE_2G
	case 3:
		return Wycl_CONNECTIONTYPE_3G
	case 4:
		return Wycl_CONNECTIONTYPE_4G
	case 5:
		return Wycl_CONNECTIONTYPE_5G
	}
	return Wycl_CONNECTIONTYPE_UNKNOWN
}

func NewPlatform(platform int) WyclDeviceTypeEnum {
	switch platform {
	case 0:
		return Wycl_DEVICETYPE_UNKNOWN
	case 1:
		return Wycl_DEVICETYPE_MOBILE
	case 2:
		return Wycl_DEVICETYPE_PAD
	default:
		return Wycl_DEVICETYPE_UNKNOWN
	}
}

// WyclRequestObject WyclRequest Objects
type WyclRequestObject struct {
	ReqId   string                   `json:"req_id"`
	Version string                   `json:"version"`
	IsTest  bool                     `json:"is_test"`
	Adunit  *WyclRequestAdunitObject `json:"adunit"`
	Device  *WyclRequestDeviceObject `json:"device"`
}

type WyclRequestAdunitObject struct {
	Tagid       string   `json:"tagid"`
	FloorPrice  int      `json:"floor_price"`
	AllowStyles []string `json:"allow_styles"`
	AppVersion  string   `json:"app_version"`
	AppName     string   `json:"app_name"`
	AppBundle   string   `json:"app_bundle"`
}

type WyclRequestDeviceObject struct {
	Os           WyclRequestOsEnum             `json:"os"`
	Imei         string                        `json:"imei"`
	ImeiMd5      string                        `json:"imeimd5"`
	Mac          string                        `json:"mac"`
	Idfa         string                        `json:"idfa"`
	Dq           string                        `json:"dq"`
	IPS          WyclRequestCarrierEnum        `json:"isp"`
	Network      WyclRequestConnectiontypeEnum `json:"network_status"`
	Ip           string                        `json:"ip"`
	Longitude    string                        `json:"longitude"`
	Latitude     string                        `json:"latitude"`
	LocationType int                           `json:"location_type"`
	CityCode     string                        `json:"city_code"`
	Osv          string                        `json:"osv"`
	Oaid         string                        `json:"oaid"`
	AndroidId    string                        `json:"android_id"`
	CarrierName  string                        `json:"carrier_name"`
	Idfv         string                        `json:"idfv"`
	CountryCode  string                        `json:"country_code"`
	Ua           string                        `json:"ua"`
	Model        string                        `json:"model"`
	Manufacturer string                        `json:"manufacturer"`
	LocalTzName  string                        `json:"local_tz_name"`
	StartupTime  string                        `json:"startup_time"`
	MbTime       string                        `json:"mb_time"`
	CpuNum       int                           `json:"cpu_num"`
	DiskTotal    int64                         `json:"disk_total"`
	MemTotal     int64                         `json:"mem_total"`
	AuthStatus   int                           `json:"auth_status"`
	DeviceType   string                        `json:"device_type"`
	Language     string                        `json:"language"`
	PhoneName    string                        `json:"phone_name"`
	Platform     WyclDeviceTypeEnum            `json:"platform"`
	Caid         string                        `json:"caid"`
	CaidVersion  string                        `json:"caid_version"`
}

// WyclResponse Objects

type WyclResponseObject struct {
	Result int                      `json:"result"`
	Ads    []*WyclResponseAdsObject `json:"ads"`
}

type WyclResponseAdsObject struct {
	Tagid              string                                  `json:"tagid"`
	Style              string                                  `json:"style"`
	Adid               string                                  `json:"adid"`
	Title              string                                  `json:"title"`
	SubTitle           string                                  `json:"sub_title"`
	Content            string                                  `json:"content"`
	StartupContent     string                                  `json:"startup_content"`
	Price              int                                     `json:"price"`
	Nurl               string                                  `json:"nurl"`
	RelatedActionLinks []*WyclResponseRelatedActionLinksObject `json:"relatedActionLinks"`
	Monitor            []*WyclResponseMonitorObject            `json:"monitor"`
	Resources          []*WyclResponseResourcesObject          `json:"resources"`
}

type WyclResponseRelatedActionLinksObject struct {
	Type         string `json:"type"`
	Title        string `json:"title"`
	Url          string `json:"url"`
	LinkExtParam string `json:"link_ext_param"`
}

type WyclResponseLinkExtParamObject struct {
	PackageSize string                       `json:"package_size"`
	PackageName string                       `json:"package_name"`
	AppName     string                       `json:"app_name"`
	StoreLink   string                       `json:"store_link"`
	Clauses     []*WyclResponseClausesObject `json:"clauses"`
}

type WyclResponseClausesObject struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}

type WyclResponseMonitorObject struct {
	Action int    `json:"action"`
	Url    string `json:"url"`
}

type WyclResponseResourcesObject struct {
	Urls []string `json:"urls"`
	Size []string `json:"size"`
	Type int      `json:"type"`
}

type WyclPipline struct {
	Common *up_common.UpCommonPipline

	Request  *WyclRequestObject
	Response *WyclResponseObject
}
