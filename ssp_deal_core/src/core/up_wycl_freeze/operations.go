package up_wycl_freeze

import (
	"bytes"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
)

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &WyclPipline{Common: common}
}

/**
* Private Methods
**/

func (p *WyclPipline) replaceLinkString(link string, price int) string {
	linkString := link
	linkString = strings.Replace(linkString, "${AUCTION_DX}", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_DY}", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UX}", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UY}", "__UP_Y__", -1)

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "${AUCTION_DX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_DY}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UY}", "-999", -1)
	}

	fmt.Println("WyclDEBUG: PlatformAppIsPriceEncrypt:", p.Common.PlatformPos.PlatformAppIsPriceEncrypt)

	if p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		fmt.Println("WyclDEBUG: price:", price)
		fmt.Println("WyclDEBUG: encryptKey:", p.encryptKey())
		fmt.Println("WyclDEBUG: encryptIv:", p.encryptIv())
		encryptPrice, _ := AesEncrypt([]byte(utils.ConvertIntToString(price)), p.encryptKey(), p.encryptIv())
		fmt.Println("WyclDEBUG: encryptPrice:", encryptPrice)
		if len(encryptPrice) > 0 {
			linkString = strings.Replace(linkString, "${AUCTION_PRICE}", Base64URLEncode(encryptPrice), -1)
		}
	}

	return linkString
}

func (p *WyclPipline) encryptKey() string {
	keyByte := []byte(p.Common.PlatformPos.PlatformAppPriceEncrypt)
	return hex.EncodeToString(keyByte)
}
func (p *WyclPipline) encryptIv() string {
	ivByte := []byte(p.Common.PlatformPos.PlatformAppPriceEncrypt2)
	return hex.EncodeToString(ivByte)
}

/**
 * * 函数式方法模板
 */
func (p *WyclPipline) actionTemplate() *WyclPipline {

	return p
}

/**
* Public Methods
**/

func (p *WyclPipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("WyclDEBUG: GetFromWycl error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *WyclPipline) SetupRequest() up_common.PiplineInterface {

	// Setup WyclRequestObject
	requestObject := &WyclRequestObject{
		ReqId:   p.Common.UUID,
		Version: "1.0.0",
		IsTest:  false,
		Adunit: &WyclRequestAdunitObject{
			Tagid:       p.Common.PlatformPos.PlatformPosID,
			FloorPrice:  p.Common.CategoryInfo.FloorPrice,
			AllowStyles: []string{p.Common.PlatformPos.PlatformPosStyleIDs},
			AppVersion:  p.Common.PlatformPos.PlatformAppVersion,
			AppName:     p.Common.PlatformPos.PlatformAppName,
			AppBundle:   p.Common.PlatformPos.PlatformAppBundle,
		},
		Device: &WyclRequestDeviceObject{
			ImeiMd5:     p.Common.MhReq.Device.ImeiMd5,
			Mac:         p.Common.MhReq.Device.Mac,
			Dq:          strconv.Itoa(p.Common.MhReq.Device.ScreenWidth) + ":" + strconv.Itoa(p.Common.MhReq.Device.ScreenHeight),
			IPS:         NewCarrier(p.Common.MhReq.Network.Carrier),
			Network:     NewNetwork(p.Common.MhReq.Network.ConnectType),
			Ip:          p.Common.MhReq.Device.IP,
			Osv:         p.Common.MhReq.Device.OsVersion,
			AndroidId:   p.Common.MhReq.Device.AndroidID,
			CountryCode: p.Common.MhReq.Device.Country,
			Ua:          p.Common.MhReq.Device.Ua,
			Platform:    NewPlatform(p.Common.MhReq.Device.DeviceType),
		},
	}

	if len(p.Common.MhReq.Device.Manufacturer) > 0 {
		requestObject.Device.Manufacturer = p.Common.MhReq.Device.Manufacturer
	}

	if len(p.Common.MhReq.Device.Model) > 0 {
		requestObject.Device.Model = p.Common.MhReq.Device.Model
	}

	if p.Common.IsAndroid() {
		requestObject.Device.Os = Wycl_OS_ANDROID

		if p.Common.IsAndroidMajorLessThanTen() {
			if len(p.Common.MhReq.Device.Imei) > 0 {
				requestObject.Device.Imei = p.Common.MhReq.Device.Imei
			} else {
				p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
					up_common.MH_UP_ERROR_CODE_900101,
					up_common.MH_UP_ERROR_CODE_104013)
			}
		} else {
			if len(p.Common.MhReq.Device.Oaid) > 0 {
				requestObject.Device.Oaid = p.Common.MhReq.Device.Oaid
			} else {
				p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
					up_common.MH_UP_ERROR_CODE_900101,
					up_common.MH_UP_ERROR_CODE_104013)
			}
		}
	} else {
		requestObject.Device.Os = Wycl_OS_IOS
		if len(p.Common.MhReq.Device.Idfa) > 0 {
			requestObject.Device.Idfa = p.Common.MhReq.Device.Idfa
		}
		// TODO 需要改为CAIDMulti
		// if len(p.Common.MhReq.Device.CAID) > 0 {
		// 	requestObject.Device.Caid = p.Common.MhReq.Device.CAID
		// }
		// if len(p.Common.MhReq.Device.CAIDVersion) > 0 {
		// 	requestObject.Device.CaidVersion = p.Common.MhReq.Device.CAIDVersion
		// }
		if len(p.Common.MhReq.Device.HardwareModel) > 0 {
			requestObject.Device.DeviceType = p.Common.MhReq.Device.HardwareModel
		}
		if len(p.Common.MhReq.Device.TimeZone) > 0 {
			requestObject.Device.LocalTzName = p.Common.MhReq.Device.TimeZone
		}
		if len(p.Common.MhReq.Device.DeviceStartSec) > 0 {
			requestObject.Device.StartupTime = p.Common.MhReq.Device.DeviceStartSec
		}
		if len(p.Common.MhReq.Device.SystemUpdateSec) > 0 {
			requestObject.Device.MbTime = p.Common.MhReq.Device.SystemUpdateSec
		}

		if utils.ConvertStringToInt(p.Common.MhReq.Device.CPUNum) > 0 {
			requestObject.Device.CpuNum = utils.ConvertStringToInt(p.Common.MhReq.Device.CPUNum)
		}
		if len(p.Common.MhReq.Device.HarddiskSizeByte) > 0 {
			requestObject.Device.DiskTotal, _ = strconv.ParseInt(p.Common.MhReq.Device.HarddiskSizeByte, 10, 64)
		}
		if len(p.Common.MhReq.Device.PhysicalMemoryByte) > 0 {
			requestObject.Device.MemTotal, _ = strconv.ParseInt(p.Common.MhReq.Device.PhysicalMemoryByte, 10, 64)
		}
		if len(p.Common.MhReq.Device.Language) > 0 {
			requestObject.Device.Language = p.Common.MhReq.Device.Language
		}
		if len(p.Common.MhReq.Device.DeviceNameMd5) > 0 {
			requestObject.Device.PhoneName = p.Common.MhReq.Device.DeviceNameMd5
		}
	}
	p.Request = requestObject
	return p
}

func (p *WyclPipline) ReplaceRequest() up_common.PiplineInterface {
	return p
}

func (p *WyclPipline) RequestAd() up_common.PiplineInterface {
	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)
	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(jsonData))
	httpRequest.Header.Add("Content-Type", "application/json")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode == 204 {
		p.Common.SetPanicStringAndCodes("no fill",
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject WyclResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	fmt.Println("WyclDEBUG: bodyContent", string(bodyContent))

	p.Response = &responseObject

	if p.Response.Ads == nil || len(p.Response.Ads) == 0 {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *WyclPipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	for _, responseItem := range p.Response.Ads {
		fmt.Println("WyclDEBUG: responseItem", responseItem)
		bidPrice := responseItem.Price
		fmt.Println("WyclDEBUG: bidPrice", bidPrice)
		p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
		p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

		// 成功时的价格比例
		randPriceRatio := p.Common.RandPriceRatio()
		winPrice := bidPrice * randPriceRatio / 100

		//失败时的价格比例
		//randFailedPriceRatio := p.Common.RandFailedPriceRatio()

		if p.Common.CategoryInfo.FinalPrice > bidPrice {
			fmt.Println("WyclDEBUG: finalPrice", p.Common.CategoryInfo.FinalPrice)
			p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

			// 如果有竞败链接，需要回调处理
			continue
		}

		var commonAPIResponseObject up_common.UpCommonAPIResponseObject

		if len(responseItem.Title) > 0 {
			commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Title)
			commonAPIResponseObject.Title = responseItem.Title
		}

		for _, relatedActionLinksItem := range responseItem.RelatedActionLinks {
			var linkExtParam WyclResponseLinkExtParamObject
			if relatedActionLinksItem.Type == "download" || relatedActionLinksItem.Type == "toApp" {
				commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD

				json.Unmarshal([]byte(relatedActionLinksItem.LinkExtParam), &linkExtParam)
				if len(linkExtParam.AppName) > 0 {
					commonAPIResponseObject.AppName = linkExtParam.AppName
				}
				if len(linkExtParam.PackageSize) > 0 {
					packageSize, _ := strconv.Atoi(linkExtParam.PackageSize)
					commonAPIResponseObject.PackageSize = packageSize
				}
				if len(linkExtParam.Clauses) > 0 {
					for _, clausesItem := range linkExtParam.Clauses {
						switch clausesItem.Title {
						case "权限":
							commonAPIResponseObject.Permission = clausesItem.Url
						case "隐私":
							commonAPIResponseObject.PrivacyUrl = clausesItem.Url
						}
					}
				}
			} else {
				commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
			}
			if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_IOS.String() {
				commonAPIResponseObject.LandPageUrl = relatedActionLinksItem.Url
				commonAPIResponseObject.AdUrl = relatedActionLinksItem.Url
			} else {
				switch relatedActionLinksItem.Type {
				case "landing_page":
					commonAPIResponseObject.LandPageUrl = relatedActionLinksItem.Url
					commonAPIResponseObject.AdUrl = relatedActionLinksItem.Url
				case "download":
					commonAPIResponseObject.DownloadUrl = relatedActionLinksItem.Url
				case "toApp":
					commonAPIResponseObject.DeepLink = relatedActionLinksItem.Url
				}
			}
		}
		commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

		commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

		var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

		var respListItemDeepLinkArray []string
		var respListItemShowArray []string
		var respListItemClickArray []string

		mhDPParams := url.Values{}
		mhDPParams.Add("result", "0")
		mhDPParams.Add("reason", "")
		mhDPParams.Add("deeptype", "__DEEP_TYPE__")
		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			p.Common.CategoryInfo.FloorPrice,
			p.Common.CategoryInfo.FinalPrice,
			bidPrice,
			0)
		mhDPParams.Add("log", bigdataParams)

		respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

		if responseItem.Monitor != nil && len(responseItem.Monitor) > 0 {
			var downloadUrls []string
			var downloadType up_common.UpCommonConvType
			var commonAPIResponseDownloadConvTrackObject up_common.UpCommonAPIResponseConvTrackObject

			var downloadEndUrls []string
			var downloadEndType up_common.UpCommonConvType
			var commonAPIResponseDownloadEndConvTrackObject up_common.UpCommonAPIResponseConvTrackObject

			var installEndUrls []string
			var installEndType up_common.UpCommonConvType
			var commonAPIResponseInstallEndConvTrackObject up_common.UpCommonAPIResponseConvTrackObject

			for _, monitorItem := range responseItem.Monitor {
				switch monitorItem.Action {
				case 0:
					respListItemShowArray = append(respListItemShowArray, p.replaceLinkString(monitorItem.Url, winPrice))
				case 1:
					respListItemClickArray = append(respListItemClickArray, monitorItem.Url)
				case 5:
					downloadType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
					downloadUrls = append(downloadUrls, monitorItem.Url)
				case 18:
					downloadEndType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
					downloadEndUrls = append(downloadEndUrls, monitorItem.Url)
				case 23:
					installEndType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
					installEndUrls = append(installEndUrls, monitorItem.Url)
				}
			}
			if downloadType == up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START {
				commonAPIResponseDownloadConvTrackObject.ConvType = downloadType
				commonAPIResponseDownloadConvTrackObject.ConvUrls = downloadUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseDownloadConvTrackObject)
			}
			if downloadEndType == up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END {
				commonAPIResponseDownloadEndConvTrackObject.ConvType = downloadEndType
				commonAPIResponseDownloadEndConvTrackObject.ConvUrls = downloadEndUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseDownloadEndConvTrackObject)
			}
			if installEndType == up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END {
				commonAPIResponseInstallEndConvTrackObject.ConvType = installEndType
				commonAPIResponseInstallEndConvTrackObject.ConvUrls = installEndUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseInstallEndConvTrackObject)
			}
		}

		if len(respListItemClickArray) > 0 {
			p.Common.MhClickLink.AddBigDataParams(bigdataParams)
			respListItemClickArray = append(respListItemClickArray, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))
			commonAPIResponseObject.ClickLink = respListItemClickArray

		}
		if len(respListItemShowArray) > 0 {
			p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)
			respListItemShowArray = append(respListItemShowArray, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))
			commonAPIResponseObject.ImpressionLink = respListItemShowArray
		}

		commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(commonAPIResponseObject.DeepLink)

		if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {
			var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
			convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

			var respListItemDeepLinkFailedArray []string

			mhDPFailedParams := url.Values{}
			mhDPFailedParams.Add("result", "1")
			mhDPFailedParams.Add("reason", "3")
			mhDPFailedParams.Add("log", bigdataParams)

			respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
			convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
		}
		if len(responseItem.Resources) > 0 {
			for _, resourcesItem := range responseItem.Resources {
				switch resourcesItem.Type {
				case 0, 2:
					var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject
					if len(resourcesItem.Urls) > 0 {
						for _, image := range resourcesItem.Urls {
							var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
							commonAPIResponseImageObject.Url = image
							commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
							commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
							commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
						}
					}
					commonAPIResponseObject.Images = commonAPIResponseImageObjects
					commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

					if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
						p.Common.SetPanicStringAndCodes("images is empty",
							up_common.MH_UP_ERROR_CODE_900201,
							up_common.MH_UP_ERROR_CODE_102006)
					}
				case 1:
					var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
					commonAPIResponseVideoObject.EndcardType = 1
					commonAPIResponseVideoObject.EndcardRange = 1
					commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
					commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight
					commonAPIResponseVideoObject.VideoUrl = resourcesItem.Urls[0]
					if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
						if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
							commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
						}
					} else {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
						if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
							commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
						}
					}
					commonAPIResponseObject.Video = &commonAPIResponseVideoObject
					commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
				}
			}
		}
		if len(commonAPIResponseConvTrackObjects) > 0 {
			fmt.Println("WyclDEBUG: commonAPIResponseConvTrackObjects:", commonAPIResponseConvTrackObjects)
			commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
		}

		if p.Common.IsReportToWin {
			if len(responseItem.Nurl) > 0 && bidPrice > 0 && p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							fmt.Println("gdt win url panic:", err)
						}
					}()

					replacedLinkString := p.replaceLinkString(responseItem.Nurl, winPrice)
					p.Common.RequestUrl(replacedLinkString)
				}()
			}
		}
		commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
		commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

		fmt.Println("WyclDEBUG: commonAPIResponseObject", commonAPIResponseObject)

		list = append(list, &commonAPIResponseObject)
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("FANCYDEBUG: responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *WyclPipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
