package core

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/csj_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromCSJ ...
func GetFromCSJ(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from csj")
	// platformPos.PlatformAppUpURL = "http://tserver.kejet.net/r/m/b"
	// platformPos.PlatformPosID = "1000100001"
	// fmt.Println("get from toutiao, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from toutiao, p_pos_id:", platformPos.PlatformPosID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := utils.ConvertStringToInt(mhReq.Device.CPUNum)

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	csjReq := &csj_up.BidRequest{
		RequestId:   proto.String(utils.GetMd5(bigdataUID)),
		ApiVersion:  proto.String("1.5.5"),
		Uid:         proto.String(""),
		SourceType:  proto.String("app"),
		Ua:          proto.String(destConfigUA),
		Ip:          proto.String(mhReq.Device.IP),
		AdxName:     proto.String(platformPos.PlatformAppAdxName),
		AdxPassword: proto.String(platformPos.PlatformAppAdxPassword),
		Timeout:     proto.Uint32(uint32(timeout)),
	}

	// 1 support_pkg; 2 query
	// query字段
	// if platformPos.PlatformPosIsSupportQuerys == 1 {
	// 	if platformPos.PlatformPosSupportQuerysType == 0 {
	// 		if len(mhReq.Pos.Query) > 0 {
	// 			csjReq.Query = proto.String(mhReq.Pos.Query)
	// 		}
	// 	} else if platformPos.PlatformPosSupportQuerysType == 1 {
	// 		if len(platformPos.PlatformPosSupportQuerys) > 0 {
	// 			tmpArray := strings.Split(platformPos.PlatformPosSupportQuerys, ",")
	// 			csjReq.Query = proto.String(tmpArray[rand.Intn(len(tmpArray))])
	// 		}
	// 	}
	// }

	// app
	csjReqApp := &csj_up.BidRequest_App{
		Appid: proto.String(platformPos.PlatformAppID),
	}

	csjReqApp.PackageName = proto.String(GetAppBundleByConfig(c, mhReq, localPos, platformPos))
	if mhReq.Device.Os == "android" {
		csjReqApp.ApkSign = proto.String(platformPos.PlatformAppSHA1)
		if len(platformPos.PlatformAppSHA1) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		csjReqApp.ItunesId = proto.String(platformPos.PlatformAppItunesID)
	} else {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// city, province
	// reqCity := ""
	// reqProvince := ""
	// csjReqAppGeo := &csj_up.BidRequest_Geo{
	// 	City:     proto.String(reqCity),
	// 	Province: proto.String(reqProvince),
	// }

	// csjReqApp.Geo = csjReqAppGeo

	csjReq.App = csjReqApp

	// device
	csjReqDevice := &csj_up.BidRequest_Device{
		Type: csj_up.BidRequest_Device_PHONE.Enum(),

		Did:              proto.String(""),
		Imei:             proto.String(""),
		Ssid:             proto.String(""),
		WifiMac:          proto.String(""),
		RomVersion:       proto.String(""),
		SysCompilingTime: proto.String(""),
		// Language:         proto.String("中文"),
		CountryCode: proto.String("cn"),
	}

	// 原始请求ios参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		csjReqDevice.Did = proto.String("")

		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if platformPos.PlatformPosIsOnlyMore10 == 1 {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
		if osvMajor < 10 {

			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				csjReqDevice.ImeiMd5 = proto.String(utils.GetMd5(mhReq.Device.Imei))
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				csjReqDevice.ImeiMd5 = proto.String(strings.ToLower(mhReq.Device.ImeiMd5))
			}

			// if len(mhReq.Device.AndroidID) > 0 {
			// 	csjReqDevice.AndroidId = proto.String(strings.ToLower(mhReq.Device.AndroidID))

			// 	extraReportParams = extraReportParams + ",android_id"
			// }

		} else {

			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				csjReqDevice.Oaid = proto.String(mhReq.Device.Oaid)
			}

			// if len(mhReq.Device.AndroidID) > 0 {
			// 	csjReqDevice.AndroidId = proto.String(strings.ToLower(mhReq.Device.AndroidID))

			// 	extraReportParams = extraReportParams + ",android_id"
			// }
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// if localPos.LocalAppID == "10143" {
		// 	fmt.Println("kbg_debug_csj, app_id idfa:", localPos.LocalAppID, mhReq.Device.OsVersion, mhReq.Device.Idfa, mhReq.Device.IdfaMd5)
		// 	fmt.Println("kbg_debug_csj, app_id caid:", localPos.LocalAppID, tmpCAID, tmpCAIDVersion)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", DeviceStartSec:", tmpDeviceStartSec)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", Country:", tmpCountry)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", Language:", tmpLanguage)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", DeviceNameMd5:", tmpDeviceNameMd5)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", HardwareModel:", tmpHardwareModel)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", PhysicalMemoryByte:", tmpPhysicalMemoryByte)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", HarddiskSizeByte:", tmpHarddiskSizeByte)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", SystemUpdateSec:", tmpSystemUpdateSec)
		// 	fmt.Println("kbg_debug_csj, app_id yinzi", localPos.LocalAppID, ", TimeZone:", tmpTimeZone)
		// }

		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				csjReqDevice.Did = proto.String(mhReq.Device.Idfa)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							csjReqDevice.CAID1 = proto.String(item.CAID)
							csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

							csjReqDevice.CAID2 = proto.String("")
							csjReqDevice.CAID2Version = proto.String("")

							break
						}
					}
				} else {
					tmpCAIDMulti := mhReq.Device.CAIDMulti
					sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

					for i, item := range tmpCAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						if i == 0 {
							csjReqDevice.CAID1 = proto.String(item.CAID)
							csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
						} else if i == 1 {
							csjReqDevice.CAID2 = proto.String(item.CAID)
							csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
						}
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
				len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

				memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
				hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

				if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
				} else {
					csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
					csjReqDevice.CountryCode = proto.String(tmpCountry)
					csjReqDevice.Language = proto.String(tmpLanguage)
					csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
					// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
					csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
					memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
					csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
					hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
					csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
					csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
					csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

					if tmpCPUNum > 0 {
						csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
					}

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					csjReqDevice.Did = proto.String(mhReq.Device.Idfa)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								csjReqDevice.CAID1 = proto.String(item.CAID)
								csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

								csjReqDevice.CAID2 = proto.String("")
								csjReqDevice.CAID2Version = proto.String("")

								break
							}
						}
					} else {
						tmpCAIDMulti := mhReq.Device.CAIDMulti
						sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

						for i, item := range tmpCAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							if i == 0 {
								csjReqDevice.CAID1 = proto.String(item.CAID)
								csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
							} else if i == 1 {
								csjReqDevice.CAID2 = proto.String(item.CAID)
								csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
							}
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
					len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

					memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
					hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

					if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
					} else {
						csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
						csjReqDevice.CountryCode = proto.String(tmpCountry)
						csjReqDevice.Language = proto.String(tmpLanguage)
						csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
						// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
						csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
						memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
						csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
						hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
						csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
						csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
						csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

						if tmpCPUNum > 0 {
							csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
						}

						isIosDeviceOK = true
						isIOSToUpReportYinZi = true
					}
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					csjReqDevice.Did = proto.String(mhReq.Device.Idfa)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								csjReqDevice.CAID1 = proto.String(item.CAID)
								csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

								csjReqDevice.CAID2 = proto.String("")
								csjReqDevice.CAID2Version = proto.String("")

								break
							}
						}
					} else {
						tmpCAIDMulti := mhReq.Device.CAIDMulti
						sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

						for i, item := range tmpCAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							if i == 0 {
								csjReqDevice.CAID1 = proto.String(item.CAID)
								csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
							} else if i == 1 {
								csjReqDevice.CAID2 = proto.String(item.CAID)
								csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
							}
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
					len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

					memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
					hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

					if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
					} else {
						csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
						csjReqDevice.CountryCode = proto.String(tmpCountry)
						csjReqDevice.Language = proto.String(tmpLanguage)
						csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
						// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
						csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
						memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
						csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
						hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
						csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
						csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
						csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

						if tmpCPUNum > 0 {
							csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
						}

						isIosDeviceOK = true
						isIOSToUpReportYinZi = true
					}
				}
			}
		}

		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// os, os_version
	if mhReq.Device.Os == "android" {
		csjReqDevice.Os = csj_up.BidRequest_Device_ANDROID.Enum()
	} else if mhReq.Device.Os == "ios" {
		csjReqDevice.Os = csj_up.BidRequest_Device_IOS.Enum()
	}
	csjReqDevice.OsVersion = proto.String(mhReq.Device.OsVersion)
	csjReqDevice.Model = proto.String(mhReq.Device.Model)
	csjReqDevice.Vendor = proto.String(mhReq.Device.Manufacturer)
	if mhReq.Device.Os == "ios" {
		csjReqDevice.Vendor = proto.String("Apple")
	}

	// csjReqDevice.Mac = proto.String(mhReq.Device.Mac)
	csjReqDevice.ScreenWidth = proto.Uint32(uint32(mhReq.Device.ScreenWidth))
	csjReqDevice.ScreenHeight = proto.Uint32(uint32(mhReq.Device.ScreenHeight))

	if platformPos.PlatformPosDirection == 0 {
		csjReqDevice.Orientation = csj_up.BidRequest_Device_HORIZONTAL.Enum()
	} else if platformPos.PlatformPosDirection == 1 {
		csjReqDevice.Orientation = csj_up.BidRequest_Device_VERTICAL.Enum()
	}

	// device -> network
	if mhReq.Network.ConnectType == 0 {
		// 未知
		csjReqDevice.ConnType = csj_up.BidRequest_Device_CONN_UNKNOWN.Enum()
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		csjReqDevice.ConnType = csj_up.BidRequest_Device_WIFI.Enum()
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		csjReqDevice.ConnType = csj_up.BidRequest_Device_MOBILE_2G.Enum()
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		csjReqDevice.ConnType = csj_up.BidRequest_Device_MOBILE_3G.Enum()
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		csjReqDevice.ConnType = csj_up.BidRequest_Device_MOBILE_4G.Enum()
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		csjReqDevice.ConnType = csj_up.BidRequest_Device_MOBILE_5G.Enum()
	}

	// device -> carrier
	if mhReq.Network.Carrier == 1 {
		csjReqDevice.Carrier = csj_up.BidRequest_Device_MOBILE.Enum()
		if mhReq.Device.Os == "ios" {
			csjReqDevice.CarrierName = proto.String("中国移动")
		}
	} else if mhReq.Network.Carrier == 2 {
		csjReqDevice.Carrier = csj_up.BidRequest_Device_UNICOM.Enum()
		if mhReq.Device.Os == "ios" {
			csjReqDevice.CarrierName = proto.String("中国联通")
		}
	} else if mhReq.Network.Carrier == 3 {
		csjReqDevice.Carrier = csj_up.BidRequest_Device_TELECOM.Enum()
		if mhReq.Device.Os == "ios" {
			csjReqDevice.CarrierName = proto.String("中国电信")
		}
	} else {
		csjReqDevice.Carrier = csj_up.BidRequest_Device_CARRIER_UNKNOWN.Enum()
		if mhReq.Device.Os == "ios" {
			csjReqDevice.CarrierName = proto.String("")
		}
	}

	csjReq.Device = csjReqDevice

	// adslots
	csjReqAdSlot := &csj_up.BidRequest_AdSlot{
		Id:                      proto.String(platformPos.PlatformPosID),
		IsSupportDpl:            proto.Bool(true),
		IsSupportUlink:          proto.Bool(true),
		IsFilterUnlistedPackage: proto.Bool(false),
	}
	// adslots -> adtype
	if platformPos.PlatformPosType == 1 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_BANNER.Enum()
	} else if platformPos.PlatformPosType == 2 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_SPLASH.Enum()
	} else if platformPos.PlatformPosType == 3 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_INTERSTITIAL.Enum()
	} else if platformPos.PlatformPosType == 4 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_STREAM.Enum()
	} else if platformPos.PlatformPosType == 13 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_PATCH.Enum()
	} else if platformPos.PlatformPosType == 9 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_FULLSCREEN_VIDEO.Enum()
	} else if platformPos.PlatformPosType == 11 {
		csjReqAdSlot.Adtype = csj_up.BidRequest_AdSlot_REWARD_VIDEO.Enum()
	}

	// count
	csjReqAdSlot.AdCount = proto.Uint32(uint32(mhReq.Pos.AdCount))
	// floor price
	csjReqAdSlot.MinimumCpm = proto.Uint64(uint64(localPosFloorPrice))
	// adslots -> accepted_size
	if platformPos.PlatformPosMaterialType == 0 {
		csjReqAdSlotAcceptedSize := &csj_up.BidRequest_AdSlot_Size{
			Width:        proto.Uint32(uint32(platformPos.PlatformPosWidth)),
			Height:       proto.Uint32(uint32(platformPos.PlatformPosHeight)),
			CreativeType: proto.Uint32(uint32(1)),
		}
		csjReqAdSlot.AcceptedSize = append(csjReqAdSlot.AcceptedSize, csjReqAdSlotAcceptedSize)
	} else if platformPos.PlatformPosMaterialType == 1 {
		csjReqAdSlotAcceptedSize := &csj_up.BidRequest_AdSlot_Size{
			Width:        proto.Uint32(uint32(platformPos.PlatformPosWidth)),
			Height:       proto.Uint32(uint32(platformPos.PlatformPosHeight)),
			CreativeType: proto.Uint32(uint32(4)),
		}
		csjReqAdSlot.AcceptedSize = append(csjReqAdSlot.AcceptedSize, csjReqAdSlotAcceptedSize)
	} else {
		csjReqAdSlotAcceptedSize := &csj_up.BidRequest_AdSlot_Size{
			Width:        proto.Uint32(uint32(platformPos.PlatformPosWidth)),
			Height:       proto.Uint32(uint32(platformPos.PlatformPosHeight)),
			CreativeType: proto.Uint32(uint32(1)),
		}
		csjReqAdSlotAcceptedSize1 := &csj_up.BidRequest_AdSlot_Size{
			Width:        proto.Uint32(uint32(platformPos.PlatformPosWidth)),
			Height:       proto.Uint32(uint32(platformPos.PlatformPosHeight)),
			CreativeType: proto.Uint32(uint32(4)),
		}
		csjReqAdSlot.AcceptedSize = append(csjReqAdSlot.AcceptedSize, csjReqAdSlotAcceptedSize)
		csjReqAdSlot.AcceptedSize = append(csjReqAdSlot.AcceptedSize, csjReqAdSlotAcceptedSize1)
	}

	csjReqAdSlot.AcceptedInteractionType = append(csjReqAdSlot.AcceptedInteractionType, csj_up.BidRequest_AdSlot_SURFING)
	csjReqAdSlot.AcceptedInteractionType = append(csjReqAdSlot.AcceptedInteractionType, csj_up.BidRequest_AdSlot_IN_APP)
	csjReqAdSlot.AcceptedInteractionType = append(csjReqAdSlot.AcceptedInteractionType, csj_up.BidRequest_AdSlot_DOWLOAD)

	// adslots -> pos
	// 顶部 1, 底部 2, 信息流内 3, 中部 4, 全屏 5
	if platformPos.PlatformPosType == 2 || platformPos.PlatformPosType == 9 || platformPos.PlatformPosType == 11 {
		csjReqAdSlot.Pos = csj_up.BidRequest_AdSlot_FULLSCREEN.Enum()
	} else if platformPos.PlatformPosType == 4 {
		csjReqAdSlot.Pos = csj_up.BidRequest_AdSlot_FLOW.Enum()
	} else if platformPos.PlatformPosType == 3 {
		csjReqAdSlot.Pos = csj_up.BidRequest_AdSlot_MIDDLE.Enum()
	}

	// 1 support_pkg; 2 query
	// if platformPos.PlatformPosIsSupportPkgs == 1 {
	// 	if platformPos.PlatformPosSupportPkgsType == 0 {
	// 		if len(mhReq.Pos.PkgWhitelist) > 0 {
	// 			csjReqAdSlot.SupportPkg = mhReq.Pos.PkgWhitelist
	// 		}
	// 	} else if platformPos.PlatformPosSupportPkgsType == 1 {
	// 		if len(platformPos.PlatformPosSupportPkgs) > 0 {
	// 			csjReqAdSlot.SupportPkg = strings.Split(platformPos.PlatformPosSupportPkgs, ",")
	// 		}
	// 	}
	// }

	csjReq.Adslots = append(csjReq.Adslots, csjReqAdSlot)

	////////////////////////////////////////////////////////////////////////////////////////
	if platformPos.PlatformAppIsReportUa == 1 {
		csjReq.Ua = proto.String(destConfigUA)
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					csjReqDevice.OsVersion = proto.String(didRedisData.OsVersion)
					csjReqDevice.Model = proto.String(didRedisData.Model)
					csjReqDevice.Vendor = proto.String(didRedisData.Manufacturer)

					csjReqDevice.ImeiMd5 = proto.String("")
					csjReqDevice.Oaid = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							csjReqDevice.ImeiMd5 = proto.String(utils.GetMd5(didRedisData.Imei))
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							csjReqDevice.ImeiMd5 = proto.String(strings.ToLower(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							csjReqDevice.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						csjReq.Ua = proto.String(didRedisData.Ua)

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						replaceUA = didRedisData.Ua
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					// osv model key
					// fmt.Println("kbg_debug ios replace_key: ", localPos.LocalPosID, redisReplaceKey)
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						csjReqDevice.Did = proto.String("")
						csjReqDevice.CAID1 = proto.String("")
						csjReqDevice.CAID1Version = proto.String("")
						csjReqDevice.CAID2 = proto.String("")
						csjReqDevice.CAID2Version = proto.String("")
						csjReqDevice.StartupTime = proto.String("")
						csjReqDevice.CountryCode = proto.String("")
						csjReqDevice.Language = proto.String("")
						csjReqDevice.PhoneName = proto.String("")
						csjReqDevice.DeviceType = proto.String("")
						csjReqDevice.MemTotal = proto.Int64(0)
						csjReqDevice.DiskTotal = proto.Int64(0)
						csjReqDevice.MbTime = proto.String("")
						csjReqDevice.LocalTzTime = proto.String("")
						csjReqDevice.CpuNum = proto.Int32(0)

						csjReqDevice.OsVersion = proto.String(didRedisData.OsVersion)
						csjReqDevice.Model = proto.String(didRedisData.Model)
						// csjReqDevice.Vendor = proto.String(didRedisData.Manufacturer)

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(mhReq.Device.Idfa) > 0 {
								csjReqDevice.Did = proto.String(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range mhReq.Device.CAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										csjReqDevice.CAID1 = proto.String(item.CAID)
										csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

										csjReqDevice.CAID2 = proto.String("")
										csjReqDevice.CAID2Version = proto.String("")

										break
									}
								}
							} else {
								for i, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									if i == 0 {
										csjReqDevice.CAID1 = proto.String(item.CAID)
										csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
									} else if i == 1 {
										csjReqDevice.CAID2 = proto.String(item.CAID)
										csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
									}
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
								len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

								memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
								hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

								if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
								} else {
									csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
									csjReqDevice.CountryCode = proto.String(tmpCountry)
									csjReqDevice.Language = proto.String(tmpLanguage)
									csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
									// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
									csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
									memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
									csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
									hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
									csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
									csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
									csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

									if tmpCPUNum > 0 {
										csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
									}

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(mhReq.Device.Idfa) > 0 {
									csjReqDevice.Did = proto.String(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range mhReq.Device.CAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											csjReqDevice.CAID1 = proto.String(item.CAID)
											csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

											csjReqDevice.CAID2 = proto.String("")
											csjReqDevice.CAID2Version = proto.String("")

											break
										}
									}
								} else {
									for i, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										if i == 0 {
											csjReqDevice.CAID1 = proto.String(item.CAID)
											csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
										} else if i == 1 {
											csjReqDevice.CAID2 = proto.String(item.CAID)
											csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
										}
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
									len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

									memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
									hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

									if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
									} else {
										csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
										csjReqDevice.CountryCode = proto.String(tmpCountry)
										csjReqDevice.Language = proto.String(tmpLanguage)
										csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
										// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
										csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
										memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
										csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
										hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
										csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
										csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
										csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

										if tmpCPUNum > 0 {
											csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
										}

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(mhReq.Device.Idfa) > 0 {
									csjReqDevice.Did = proto.String(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range mhReq.Device.CAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											csjReqDevice.CAID1 = proto.String(item.CAID)
											csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)

											csjReqDevice.CAID2 = proto.String("")
											csjReqDevice.CAID2Version = proto.String("")

											break
										}
									}
								} else {
									for i, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										if i == 0 {
											csjReqDevice.CAID1 = proto.String(item.CAID)
											csjReqDevice.CAID1Version = proto.String(item.CAIDVersion)
										} else if i == 1 {
											csjReqDevice.CAID2 = proto.String(item.CAID)
											csjReqDevice.CAID2Version = proto.String(item.CAIDVersion)
										}
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 &&
									len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 {

									memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
									hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)

									if memTotalInt64 == 0 || hardDiskSizeInt64 == 0 {
									} else {
										csjReqDevice.StartupTime = proto.String(tmpDeviceStartSec)
										csjReqDevice.CountryCode = proto.String(tmpCountry)
										csjReqDevice.Language = proto.String(tmpLanguage)
										csjReqDevice.PhoneName = proto.String(tmpDeviceNameMd5)
										// csjReqDevice.XXX = proto.String(tmpHardwareMachine)
										csjReqDevice.DeviceType = proto.String(tmpHardwareModel)
										memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
										csjReqDevice.MemTotal = proto.Int64(memTotalInt64)
										hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
										csjReqDevice.DiskTotal = proto.Int64(hardDiskSizeInt64)
										csjReqDevice.MbTime = proto.String(tmpSystemUpdateSec)
										csjReqDevice.LocalTzTime = proto.String(tmpTimeZone)

										if tmpCPUNum > 0 {
											csjReqDevice.CpuNum = proto.Int32(int32(tmpCPUNum))
										}

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							csjReq.Ua = proto.String(didRedisData.Ua)

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}

							replaceUA = didRedisData.Ua
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from csj error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from csj error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	////////////////////////////////////////////////////////////////////////////////////////
	// log
	// csjReqTmpByte, _ := json.Marshal(csjReq)
	// fmt.Println("csj req: ", string(csjReqTmpByte))
	// go models.BigDataHoloDebugJson2(bigdataUID+"&csj_req", string(csjReqTmpByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	// fmt.Println("csj req url: ", platformPos.PlatformAppUpURL)
	////////////////////////////////////////////////////////////////////////////////////////
	csjReqPbByte, _ := proto.Marshal(csjReq)

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(csjReqPbByte))

	requestGet.Header.Add("Content-Type", "application/octet-stream;charset=UTF-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("csj resp body: " + string(bodyContent))
	if resp.StatusCode == 204 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	csjResp := &csj_up.BidResponse{}
	err = proto.Unmarshal(bodyContent, csjResp)
	if err != nil {
		fmt.Println(err)
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	// if localPos.LocalAppID == "10883" {
	// 	csjReqTmpByte, _ := json.Marshal(csjReq)
	// 	csjRespTmpByte, _ := json.Marshal(csjResp)
	// 	csjDeviceTmpByte, _ := json.Marshal(csjReqDevice)

	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&csj_resp_10883", string(csjDeviceTmpByte), string(csjReqTmpByte), localPos.LocalPosID, platformPos.PlatformAppID, string(csjRespTmpByte))
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	// log
	// csjRespTmpByte, _ := json.Marshal(csjResp)
	// fmt.Println("csj resp: ", string(csjRespTmpByte))
	// go models.BigDataHoloDebugJson2(bigdataUID+"&csj_resp", string(csjRespTmpByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	////////////////////////////////////////////////////////////////////////////////////////

	if csjResp.GetStatusCode() != 0 {
		bigdataExtra.UpRespCode = int(csjResp.GetStatusCode())

		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(csjResp.GetAds()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, csjInfoItem := range csjResp.GetAds() {

		adInfoItem := csjInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// ecpm
		csjEcpm := int(adInfoItem.GetPrice())

		respTmpPrice = respTmpPrice + csjEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if csjEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			csjEcpm = platformPos.PlatformPosEcpm
		}
		csjLossNoticeData := getCSJFailedNurl(platformPos.PlatformAppAdxName, csjResp.GetRequestId(), platformPos, csjEcpm)

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > csjEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(csjEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// 随机95-98%替换__PR__
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}
		tmpWinPrice := int64(csjEcpm * randPRValue / 100)
		macroPrice := ""
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {
			macroPrice = CalcAuctionPrice(tmpWinPrice, utils.Get16Md5(csjResp.GetRequestId()), []byte(platformPos.PlatformAppPriceEncrypt), []byte(platformPos.PlatformAppPriceEncrypt2))
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.GetCreative().GetTitle()) > 0 {
			respListItemMap["title"] = adInfoItem.GetCreative().GetTitle()
		}

		// description
		if len(adInfoItem.Creative.GetDescription()) > 0 {
			respListItemMap["description"] = adInfoItem.Creative.GetDescription()
		}

		// crid
		respListItemMap["crid"] = utils.ConvertInt64ToString(adInfoItem.Creative.GetCreativeId())

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// market url
		if len(adInfoItem.Creative.GetMarketUrl()) > 0 {
			respListItemMap["market_url"] = adInfoItem.Creative.GetMarketUrl()
		}

		// deep link
		if len(adInfoItem.Creative.GetDeeplinkUrl()) > 0 ||
			len(adInfoItem.Creative.GetUlinkUrl()) > 0 ||
			len(adInfoItem.Creative.GetMarketUrl()) > 0 {

			if len(adInfoItem.Creative.GetDeeplinkUrl()) > 0 {
				respListItemMap["deep_link"] = adInfoItem.Creative.GetDeeplinkUrl()
			} else if len(adInfoItem.Creative.GetUlinkUrl()) > 0 {
				respListItemMap["deep_link"] = adInfoItem.Creative.GetUlinkUrl()
			} else if len(adInfoItem.Creative.GetMarketUrl()) > 0 {
				respListItemMap["deep_link"] = adInfoItem.Creative.GetMarketUrl()
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, deeplinkTrackItem := range adInfoItem.Creative.GetTrackingEvent() {
				if deeplinkTrackItem.GetEvent() == "dpl_success" {
					respListItemDeepLinkArray = append(respListItemDeepLinkArray, deeplinkTrackItem.GetUrl())
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, csjEcpm, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, deeplinkTrackItem := range adInfoItem.Creative.GetTrackingEvent() {
					if deeplinkTrackItem.GetEvent() == "dpl_failed" {
						respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, deeplinkTrackItem.GetUrl())
					}
				}

				// mhDPFailedParams := url.Values{}
				// mhDPFailedParams.Add("result", "1")
				// mhDPFailedParams.Add("reason", "3")
				// mhDPFailedParams.Add("log", bigdataParams)

				// respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		if len(adInfoItem.Creative.GetPackageName()) > 0 {
			respListItemMap["package_name"] = adInfoItem.Creative.GetPackageName()
		} else if len(adInfoItem.Creative.AppInfo.GetPackageName()) > 0 {
			respListItemMap["package_name"] = adInfoItem.Creative.GetAppInfo().GetPackageName()
		} else {
			hackPackageName := GetPackageNameByDeeplink(adInfoItem.Creative.GetDeeplinkUrl())
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName
			}
		}

		// icon_url
		if len(adInfoItem.GetCreative().GetIcon()) > 0 {
			respListItemMap["icon_url"] = adInfoItem.GetCreative().GetIcon()
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Creative.GetDeeplinkUrl())
		}

		isVideo := false

		// 视频
		if adInfoItem.Creative.GetCreativeType() == csj_up.BidResponse_Ad_MaterialMeta_VIDEO {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.GetCreative().Video.GetVideoDuration() > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(adInfoItem.GetCreative().Video.GetVideoDuration()))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
					continue
				}
				respListVideoItemMap["duration"] = int(adInfoItem.GetCreative().Video.GetVideoDuration() * 1000)
			}

			// cover_url
			if len(adInfoItem.GetCreative().Video.GetCoverUrl()) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.GetCreative().Video.GetCoverUrl()
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["video_url"] = adInfoItem.GetCreative().Video.GetVideoUrl()
			if adInfoItem.GetCreative().Video.GetCoverWidth() > 0 {
				respListVideoItemMap["width"] = int(adInfoItem.GetCreative().Video.GetCoverWidth())
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}

			if adInfoItem.GetCreative().Video.GetCoverHeight() > 0 {
				respListVideoItemMap["height"] = int(adInfoItem.GetCreative().Video.GetCoverHeight())
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.GetCreative().Video.GetCoverWidth() > 0 && adInfoItem.GetCreative().Video.GetCoverHeight() > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.GetCreative().Video.GetCoverWidth()), int(adInfoItem.GetCreative().Video.GetCoverHeight()))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
					continue
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}
			// event track 开始播放和播放完成
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range adInfoItem.Creative.GetTrackingEvent() {
				if monitorItem.GetEvent() == "feed_auto_play" {
					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorItem.GetUrl())
				}
			}
			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range adInfoItem.Creative.GetTrackingEvent() {
				if monitorItem.GetEvent() == "feed_over" {
					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorItem.GetUrl())
				}
			}
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			isHasImage := false
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.GetCreative().Image.GetUrl()) > 0 {
				isHasImage = true
				respListImageItemMap["url"] = adInfoItem.GetCreative().Image.GetUrl()
				if adInfoItem.GetCreative().Image.GetWidth() > 0 {
					respListImageItemMap["width"] = adInfoItem.GetCreative().Image.GetWidth()
				} else {
					respListImageItemMap["width"] = platformPos.PlatformPosWidth
				}
				if adInfoItem.GetCreative().Image.GetHeight() > 0 {
					respListImageItemMap["height"] = adInfoItem.GetCreative().Image.GetHeight()

				} else {
					respListImageItemMap["height"] = platformPos.PlatformPosHeight
				}

				if adInfoItem.GetCreative().Image.GetWidth() > 0 && adInfoItem.GetCreative().Image.GetHeight() > 0 {
					// 过滤素材方向, 大小
					isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.GetCreative().Image.GetWidth()), int(adInfoItem.GetCreative().Image.GetHeight()))
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
						continue
					}
				}
			}
			if isHasImage {
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
				continue
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoItem.GetCreative().GetInteractionType() == csj_up.BidResponse_Ad_MaterialMeta_DOWLOAD {

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.GetCreative().GetDownloadUrl()
			respListItemMap["download_url"] = adInfoItem.GetCreative().GetDownloadUrl()

			if len(adInfoItem.Creative.GetTargetUrl()) > 0 {
				respListItemMap["landpage_url"] = adInfoItem.Creative.GetTargetUrl()
			}

			if len(adInfoItem.Creative.GetAppName()) > 0 {
				respListItemMap["app_name"] = adInfoItem.Creative.GetAppName()
			} else if len(adInfoItem.Creative.AppInfo.GetAppName()) > 0 {
				respListItemMap["app_name"] = adInfoItem.Creative.AppInfo.GetAppName()
			}
		} else if adInfoItem.GetCreative().GetInteractionType() == csj_up.BidResponse_Ad_MaterialMeta_SURFING || adInfoItem.GetCreative().GetInteractionType() == csj_up.BidResponse_Ad_MaterialMeta_IN_APP {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.Creative.GetTargetUrl()
			respListItemMap["landpage_url"] = adInfoItem.Creative.GetTargetUrl()
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
			continue
		}

		if len(adInfoItem.Creative.AppInfo.GetDeveloperName()) > 0 {
			respListItemMap["publisher"] = adInfoItem.GetCreative().AppInfo.GetDeveloperName()
		}
		if len(adInfoItem.Creative.AppInfo.GetAppVersion()) > 0 {
			respListItemMap["app_version"] = adInfoItem.GetCreative().AppInfo.GetAppVersion()
		}
		if len(adInfoItem.Creative.AppInfo.GetPrivacyPolicyUrl()) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.GetCreative().AppInfo.GetPrivacyPolicyUrl()
		}
		if len(adInfoItem.Creative.AppInfo.GetPermissions()) > 0 {
			permissionStr := ""
			for _, permissionItem := range adInfoItem.GetCreative().AppInfo.GetPermissions() {
				permissionStr = permissionStr + permissionItem.GetPermissionName() + "\n"
			}
			respListItemMap["permission"] = permissionStr
		}
		if len(adInfoItem.Creative.AppInfo.GetPermissionsUrl()) > 0 {
			respListItemMap["permission_url"] = adInfoItem.Creative.AppInfo.GetPermissionsUrl()
		}
		if adInfoItem.Creative.GetProductSize() > 0 {
			respListItemMap["package_size"] = int(adInfoItem.Creative.GetProductSize())
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, csjEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		if len(adInfoItem.Creative.GetWinNoticeUrl()) > 0 && len(macroPrice) > 0 {
			var tmpWinNoticeURLs []string
			for _, winNoticeURLItem := range adInfoItem.Creative.GetWinNoticeUrl() {
				tmpWinNoticeURL := winNoticeURLItem
				tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "{AUCTION_PRICE}", macroPrice, -1)

				tmpWinNoticeURLs = append(tmpWinNoticeURLs, tmpWinNoticeURL)
			}
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// impression_link track url
		for _, monitorItem := range adInfoItem.Creative.GetShowUrl() {
			impItem := monitorItem
			if len(macroPrice) > 0 {
				impItem = strings.Replace(impItem, "{AUCTION_PRICE}", macroPrice, -1)
			}
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, monitorItem := range adInfoItem.Creative.GetClickUrl() {
			clkItem := monitorItem
			// clkItem = strings.Replace(clkItem, "${__WX_REQ_WIDTH__}", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			// clkItem = strings.Replace(clkItem, "${__WX_REQ_HEIGHT__}", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			// clkItem = strings.Replace(clkItem, "${__WX_WIDTH__}", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			// clkItem = strings.Replace(clkItem, "${__WX_HEIGHT__}", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
			// clkItem = strings.Replace(clkItem, "${__WX_DOWN_X__}", "__DOWN_X__", -1)
			// clkItem = strings.Replace(clkItem, "${__WX_DOWN_Y__}", "__DOWN_Y__", -1)
			// clkItem = strings.Replace(clkItem, "${__WX_UP_X__}", "__UP_X__", -1)
			// clkItem = strings.Replace(clkItem, "${__WX_UP_Y__}", "__UP_Y__", -1)
			// clkItem = strings.Replace(clkItem, "${__WX_TS__}", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(csjLossNoticeData) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, csjLossNoticeData)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlCSJPriceFailedURL(csjLossNoticeData, &bigdataExtra)
			continue
		}

		// 上报竞价失败
		if len(csjLossNoticeData) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, csjLossNoticeData)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = csjEcpm

		respListArray = append(respListArray, respListItemMap)
	}

	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// csj resp
	respCsj := models.MHUpResp{}
	respCsj.RespData = &mhResp
	respCsj.Extra = bigdataExtra
	// respCsj.Extra.RespCount = len(respListArray)
	// respCsj.Extra.ExternalCode = 0
	// respCsj.Extra.InternalCode = 900000

	return &respCsj
}

func curlCSJNurl(nurl string) string {

	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println(requestGet.URL.String())
	// fmt.Println("csj nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return ""
	}

	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Println("csj nurl  resp: " + string(bodyContent))
	return string(bodyContent)
}

// func curlCSJWinFailedNurl(adxName string, requestID string, platformPosID string, csjEcpm int) string {
// 	lossURL := "https://www.pangolin-dsp-toutiao.com/api/common/ads/failreason"

// 	// ecpm * (150-200%)
// 	tmpRandValue := 150 + rand.Intn(51)
// 	failedPrice := csjEcpm * tmpRandValue / 100

// 	postData := map[string]interface{}{
// 		"name":        adxName,
// 		"request_id":  requestID,
// 		"rit":         platformPosID,
// 		"code":        4001,
// 		"reason":      "公开竞价时，输给了更高的价格",
// 		"first_price": failedPrice,
// 	}

// 	// fmt.Println(postData)
// 	////////////////////////////////////////////////////////////////////////////////////////
// 	jsonData, _ := json.Marshal(postData)

// 	// client
// 	client := &http.Client{Timeout: 1000 * time.Millisecond}

// 	// send req
// 	requestGet, _ := http.NewRequest("POST", lossURL, bytes.NewReader(jsonData))

// 	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
// 	requestGet.Header.Add("Connection", "keep-alive")
// 	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
// 	// requestGet.Header.Add("User-Agent", destUA)

// 	fmt.Println(requestGet.URL.String())

// 	resp, err := client.Do(requestGet)
// 	if err != nil {
// 		// fmt.Printf("get request failed, err:[%s]", err.Error())
// 		return ""
// 	}
// 	defer resp.Body.Close()

// 	bodyContent, err := io.ReadAll(resp.Body)

// 	// fmt.Println("csj nurl  resp: " + string(bodyContent))
// 	return string(bodyContent)
// }

// curl price failed
func curlCSJPriceFailedURL(jsonData string, bigdataExtra *models.MHUpRespExtra) {
	if len(jsonData) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("ks loss url panic:", err)
				}
			}()

			// client
			client := &http.Client{Timeout: 1000 * time.Millisecond}

			// send req
			requestGet, _ := http.NewRequest("POST", "https://www.pangolin-dsp-toutiao.com/api/common/ads/failreason", bytes.NewReader([]byte(jsonData)))

			requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
			requestGet.Header.Add("Connection", "keep-alive")
			// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
			// requestGet.Header.Add("User-Agent", destUA)

			fmt.Println(requestGet.URL.String())

			resp, err := client.Do(requestGet)
			if err != nil {
				// fmt.Printf("get request failed, err:[%s]", err.Error())
				return
			}
			defer resp.Body.Close()
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

func getCSJFailedNurl(adxName string, requestID string, platformPos *models.PlatformPosStu, csjEcpm int) string {
	// lossURL := "https://www.pangolin-dsp-toutiao.com/api/common/ads/failreason"

	// ecpm * (150-200%)
	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}
	failedPrice := csjEcpm * tmpRandValue / 100

	postData := map[string]interface{}{
		"name":        adxName,
		"request_id":  requestID,
		"rit":         platformPos.PlatformPosID,
		"code":        4001,
		"reason":      "公开竞价时，输给了更高的价格",
		"first_price": failedPrice,
	}

	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	return string(jsonData)
}

func Int64ToBytes(value int64) []byte {
	b := make([]byte, 8)
	binary.BigEndian.PutUint64(b, uint64(value))
	return b
}

func CalcAuctionPrice(price int64, requestId string, ekey []byte, ikey []byte) string {
	ekeyHmac := hmac.New(sha1.New, ekey)
	ikeyHmac := hmac.New(sha1.New, ikey)
	iv := []byte(requestId)
	ekeyHmac.Write(iv)
	pad := ekeyHmac.Sum(nil)
	padValue := binary.BigEndian.Uint64(pad[:8])
	buf := int64(padValue) ^ price
	comb := append(Int64ToBytes(price), iv...)
	ikeyHmac.Write(comb)
	sig := ikeyHmac.Sum(nil)[:4]
	temp := append(iv, Int64ToBytes(buf)...)
	finalMsg := append(temp, sig...)
	result := base64.URLEncoding.EncodeToString(finalMsg)
	return result
}
