package up_adnine_freeze

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &AdninePipline{Common: common}
}

/**
* Private Methods
**/

func (p *AdninePipline) replaceLinkString(link string, price int) string {
	linkString := link
	linkString = strings.Replace(linkString, "${AUCTION_DX}", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_DY}", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UX}", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UY}", "__UP_Y__", -1)

	// if p.Common.IsLogicPixel {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	// } else {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	// }

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "${AUCTION_DX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_DY}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UY}", "-999", -1)
	}

	if p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		encryptPrice := p.encryptPrice(utils.ConvertIntToString(price))
		if len(encryptPrice) > 0 {

			if strings.Contains(linkString, "${AUCTION_PRICE}") {
				fmt.Println("ADNINEDEBUG: linkString:", linkString, "${AUCTION_PRICE}:", encryptPrice, price)
			}

			linkString = strings.Replace(linkString, "${AUCTION_PRICE}", encryptPrice, -1)
		} else {
			fmt.Println("encryptPrice is empty")
		}
	} else {
		linkString = strings.Replace(linkString, "${AUCTION_PRICE}", utils.ConvertIntToString(price), -1)
	}

	return linkString
}

func (p *AdninePipline) encryptKey() string {
	return p.Common.PlatformPos.PlatformAppPriceEncrypt
}

func (p *AdninePipline) encryptPrice(price string) string {
	encryptKey := p.encryptKey()

	if len(encryptKey) == 0 {
		fmt.Println("price: ", price, "PlatformAppPriceEncrypt: ", p.Common.PlatformPos.PlatformAppPriceEncrypt)
		fmt.Printf("PlatformPos: %+v", p.Common.PlatformPos)
		return ""
	}
	result := base64.StdEncoding.EncodeToString(utils.AesCBCPKCS5Encrypt(price, p.encryptKey()))

	return url.QueryEscape(result)
}

// func (p *AdninePipline) decryptPrice(encryptedPrice string) (string, error) {
// 	var priceString string
// 	decodeString, err := base64.StdEncoding.DecodeString(encryptedPrice)
// 	if err != nil {
// 		return priceString, err
// 	}
// 	priceString = string(utils.AesCBCPKCS5Decrypt(decodeString, []byte(p.encryptKey())))
// 	return priceString, nil
// }

/**
 * * 函数式方法模板
 */
func (p *AdninePipline) actionTemplate() *AdninePipline {

	return p
}

/**
* Public Methods
**/

func (p *AdninePipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("ADNINEDEBUG: GetFromAdnine error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *AdninePipline) SetupRequest() up_common.PiplineInterface {

	// Setup AdnineRequestObject
	requestObject := AdnineRequestObject{}
	{
		requestObject.Bid = utils.GetMd5(p.Common.UUID)
		requestObject.ApiVersion = "1.0"
		requestObject.Ua = p.Common.MhReq.Device.Ua
		requestAdspaceObject := AdnineRequestAdspaceObject{}
		{
			requestAdspaceObject.AdspaceId = p.Common.PlatformPos.PlatformPosID
			requestAdspaceObject.AdspaceType = NewAdnineRequestAdspaceType(up_common.UpCommonPosType(p.Common.CategoryInfo.PlatformPosType))
			requestAdspaceObject.AdspacePosition = ADNINE_ADSPACE_POSITION_NONE
			requestAdspaceObject.AllowedHtml = false
			requestAdspaceObject.Width = p.Common.PlatformPos.PlatformPosWidth
			requestAdspaceObject.Height = p.Common.PlatformPos.PlatformPosHeight
			requestAdspaceObject.ImpressionNum = 1
			requestAdspaceObject.PageId = p.Common.UUID
			requestAdspaceObject.InteractionType = []int{0}
		}

		requestAdspaceObjectArray := []*AdnineRequestAdspaceObject{}
		requestAdspaceObjectArray = append(requestAdspaceObjectArray, &requestAdspaceObject)
		requestObject.Adspaces = requestAdspaceObjectArray

		// Setup AdnineRequestAppObject
		requestAppObject := AdnineRequestAppObject{}
		{
			requestAppObject.AppId = p.Common.PlatformPos.PlatformAppID
			requestAppObject.ChannelId = "351" //TODO: 写死
			requestAppObject.AppName = p.Common.PlatformPos.PlatformAppName
			requestAppObject.PackageName = p.Common.PlatformPos.PlatformAppBundle
			requestAppObject.AppVersion = p.Common.PlatformPos.PlatformAppVersion
		}
		requestObject.App = &requestAppObject

		requestNetworkObject := AdnineRequestNetworkObject{}
		{
			requestNetworkObject.Ip = p.Common.MhReq.Device.IP
			requestNetworkObject.NetworkType = NewAdnineRequestNetworkType(up_common.UpCommonConnectTypeEnum(p.Common.MhReq.Network.ConnectType))
			requestNetworkObject.CarrierId = NewAdnineRequestCarrierId(up_common.UpCommonCarrierEnum(p.Common.MhReq.Network.Carrier))

		}
		requestObject.Network = &requestNetworkObject

		// Setup AdnineRequestDeviceObject
		requestAdDiveceObject := AdnineRequestDeviceObject{}
		{
			deviceIdArray := []*AdnineRequestDeviceIdObject{}

			requestAdDiveceObject.OsVersion = p.Common.MhReq.Device.OsVersion
			if len(p.Common.MhReq.Device.Manufacturer) > 0 {
				requestAdDiveceObject.Brand = p.Common.MhReq.Device.Manufacturer
			}
			if len(p.Common.MhReq.Device.Model) > 0 {
				requestAdDiveceObject.Model = p.Common.MhReq.Device.Model
			}

			requestAdDiveceObject.DeviceType = NewAdnineDeviceType(up_common.UpCommonDeviceTypeEnum(p.Common.MhReq.Device.DeviceType))
			requestAdDiveceObject.ScreenWidth = p.Common.MhReq.Device.ScreenWidth
			requestAdDiveceObject.ScreenHeight = p.Common.MhReq.Device.ScreenHeight
			if p.Common.MhReq.Device.DPI > 0 {
				requestAdDiveceObject.ScreenDensity = float64(p.Common.MhReq.Device.DPI) / 160.0
			}

			if p.Common.IsAndroid() {
				requestAdDiveceObject.OsType = ADNINE_OS_ANDROID

				if p.Common.IsAndroidMajorLessThanTen() {
					if len(p.Common.MhReq.Device.Imei) > 0 {
						deviceIdArray = append(deviceIdArray, &AdnineRequestDeviceIdObject{
							DeviceIdType: ADNINE_DEVICE_ID_TYPE_IMEI,
							DeviceId:     p.Common.MhReq.Device.Imei,
							HashType:     ADNINE_DEVICE_HASH_TYPE_NONE,
						})
					} else if len(p.Common.MhReq.Device.ImeiMd5) > 0 {
						deviceIdArray = append(deviceIdArray, &AdnineRequestDeviceIdObject{
							DeviceIdType: ADNINE_DEVICE_ID_TYPE_IMEI,
							DeviceId:     p.Common.MhReq.Device.ImeiMd5,
							HashType:     ADNINE_DEVICE_HASH_TYPE_MD5,
						})
					} else {
						p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				} else {
					if len(p.Common.MhReq.Device.Oaid) > 0 {
						deviceIdArray = append(deviceIdArray, &AdnineRequestDeviceIdObject{
							DeviceIdType: ADNINE_DEVICE_ID_TYPE_OAID,
							DeviceId:     p.Common.MhReq.Device.Oaid,
							HashType:     ADNINE_DEVICE_HASH_TYPE_NONE,
						})
					} else {
						p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				}

			} else {
				requestAdDiveceObject.OsType = ADNINE_OS_IOS
				if len(p.Common.MhReq.Device.Idfa) > 0 {
					deviceIdArray = append(deviceIdArray, &AdnineRequestDeviceIdObject{
						DeviceIdType: ADNINE_DEVICE_ID_TYPE_IDFA,
						DeviceId:     p.Common.MhReq.Device.Idfa,
						HashType:     ADNINE_DEVICE_HASH_TYPE_NONE,
					})
				} else if len(p.Common.MhReq.Device.IdfaMd5) > 0 {
					deviceIdArray = append(deviceIdArray, &AdnineRequestDeviceIdObject{
						DeviceIdType: ADNINE_DEVICE_ID_TYPE_IDFA,
						DeviceId:     p.Common.MhReq.Device.IdfaMd5,
						HashType:     ADNINE_DEVICE_HASH_TYPE_MD5,
					})
				}
			}

			requestAdDiveceObject.DeviceId = deviceIdArray
		}
		requestObject.Device = &requestAdDiveceObject
	}
	p.Request = &requestObject

	return p
}

func (p *AdninePipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.Device.OsType = NewAdnineOsType(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Device.OsVersion = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Device.Model = p.Common.ReplacedValues.Model
	}

	p.Request.Device.DeviceType = NewAdnineDeviceType(p.Common.ReplacedValues.DeviceType)

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Device.Brand = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_IMEI && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_NONE {
				deviceId.DeviceId = p.Common.ReplacedValues.Imei
			}
		}
	}

	if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_IMEI && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_MD5 {
				deviceId.DeviceId = p.Common.ReplacedValues.ImeiMd5
			}
		}
	}

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_OAID && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_NONE {
				deviceId.DeviceId = p.Common.ReplacedValues.Oaid
			}
		}
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_AAID && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_NONE {
				deviceId.DeviceId = p.Common.ReplacedValues.AndroidId
			}
		}
	}

	if len(p.Common.ReplacedValues.AndroidIdMd5) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_AAID && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_MD5 {
				deviceId.DeviceId = p.Common.ReplacedValues.AndroidIdMd5
			}
		}
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		for _, deviceId := range p.Request.Device.DeviceId {
			if deviceId.DeviceIdType == ADNINE_DEVICE_ID_TYPE_IDFA && deviceId.HashType == ADNINE_DEVICE_HASH_TYPE_NONE {
				deviceId.DeviceId = p.Common.ReplacedValues.Idfa
			}
		}
	}

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Ua = p.Common.ReplacedValues.Ua
	}

	p.Request.Device.ScreenOrientation = NewAdnineOrientation(p.Common.ReplacedValues.ScreenDirection)

	return p
}

func (p *AdninePipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(jsonData))
	httpRequest.Header.Add("Content-Type", "application/json")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	log.Println(">>>>>>>>>", response, p.Common.PlatformPos.PlatformAppUpURL)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode == 204 {
		p.Common.SetPanicStringAndCodes("no fill",
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject AdnineResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	fmt.Println("ADNINEDEBUG: bodyContent", string(bodyContent))

	p.Response = &responseObject

	if len(p.Response.Bid) == 0 {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *AdninePipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	for _, responseItem := range p.Response.Ads[0].Creative {
		bidPrice := 0

		p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
		p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

		// 成功时的价格比例
		randPriceRatio := p.Common.RandPriceRatio()
		winPrice := int(bidPrice * randPriceRatio / 100)

		// 失败时的价格比例
		// randFailedPriceRatio := p.Common.RandFailedPriceRatio()

		// if p.Common.CategoryInfo.FinalPrice > bidPrice {
		// 	p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

		// 	// 如果有竞败链接，需要回调处理

		// 	continue
		// }

		if responseItem.Adm == nil {
			continue
		}

		var commonAPIResponseObject up_common.UpCommonAPIResponseObject

		if responseItem.Adm.Native != nil && responseItem.Adm.Native.Title != nil && len(responseItem.Adm.Native.Title.Text) > 0 {
			commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Adm.Native.Title.Text)
			commonAPIResponseObject.Title = responseItem.Adm.Native.Title.Text
		}

		if responseItem.Adm.Native != nil && len(responseItem.Adm.Native.Desc) > 0 {
			commonAPIResponseObject.Description = responseItem.Adm.Native.Desc
		}

		if len(responseItem.PackageName) > 0 {
			commonAPIResponseObject.PackageName = responseItem.PackageName
		}

		if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_ANDROID.String() {
			commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE

			if responseItem.InteractionObject != nil && len(responseItem.InteractionObject.Url) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.InteractionObject.Url
				commonAPIResponseObject.AdUrl = responseItem.InteractionObject.Url
			} else if len(responseItem.InteractionObject.IntroUrl) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.InteractionObject.IntroUrl
				commonAPIResponseObject.AdUrl = responseItem.InteractionObject.IntroUrl
			}

			// if responseItem.Adm.Ext != nil && len(responseItem.Adm.Ext.AdvertiserName) > 0 {
			// 	commonAPIResponseObject.Publisher = responseItem.Adm.Ext.AdvertiserName
			// }

			// if responseItem.Adm.App != nil && len(responseItem.Adm.App.Name) > 0 {
			// 	commonAPIResponseObject.AppName = responseItem.Adm.App.Name
			// }

			if responseItem.InteractionObject != nil && len(responseItem.InteractionObject.ApkVer) > 0 {
				commonAPIResponseObject.AppVersion = responseItem.InteractionObject.ApkVer
			}

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyUrl) > 0 {
			// 	commonAPIResponseObject.PrivacyUrl = responseItem.AppPromotion.PrivacyPolicyUrl
			// }

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyInfo) > 0 {
			// 	commonAPIResponseObject.Permission = responseItem.AppPromotion.PrivacyPolicyInfo
			// } else {
			// 	commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			// }

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyAuthUrl) > 0 {
			// 	commonAPIResponseObject.PermissionUrl = responseItem.AppPromotion.PrivacyAuthUrl
			// }

			commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024

		} else if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_IOS.String() {
			if responseItem.InteractionObject != nil {
				if len(responseItem.InteractionObject.Url) > 0 {
					if strings.Contains(responseItem.InteractionObject.Url, "apple.com") {
						commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
					} else {
						commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
					}

					commonAPIResponseObject.LandPageUrl = responseItem.InteractionObject.Url
					commonAPIResponseObject.AdUrl = responseItem.InteractionObject.Url
				} else if len(responseItem.InteractionObject.IntroUrl) > 0 {
					if strings.Contains(responseItem.InteractionObject.IntroUrl, "apple.com") {
						commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
					} else {
						commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
					}

					commonAPIResponseObject.LandPageUrl = responseItem.InteractionObject.IntroUrl
					commonAPIResponseObject.AdUrl = responseItem.InteractionObject.IntroUrl
				}
			}
		}

		commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

		commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

		var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

		if responseItem.InteractionObject != nil && len(responseItem.InteractionObject.DeepLink) > 0 {
			commonAPIResponseObject.DeepLink = responseItem.InteractionObject.DeepLink

			var convTrack up_common.UpCommonAPIResponseConvTrackObject
			convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(
				p.Common.MhReq,
				p.Common.LocalPos,
				p.Common.PlatformPos,
				p.Common.UUID,
				p.Common.UUID,
				0,
				0,
				0,
				0)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			if responseItem.EventTrack != nil && len(responseItem.EventTrack) > 0 {
				for _, eventTrank := range responseItem.EventTrack {
					if eventTrank.EventType == ADNINE_EVENTTYPE_ACTIVE {
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, eventTrank.NotifyUrl)

					}
				}
			}

			convTrack.ConvUrls = respListItemDeepLinkArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

			if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

				var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
				convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
				convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
			}
		}

		if responseItem.EventTrack != nil && len(responseItem.EventTrack) > 0 {
			convUrlsDownloadStart := []string{}
			convUrlsDownloadEnd := []string{}
			convUrlsInstallEnd := []string{}

			for _, eventTrank := range responseItem.EventTrack {
				if eventTrank.EventType == ADNINE_EVENTTYPE_DOWNLOAD {
					convUrlsDownloadEnd = append(convUrlsDownloadEnd, eventTrank.NotifyUrl)
				} else if eventTrank.EventType == ADNINE_EVENTTYPE_START_DOWN {
					convUrlsDownloadStart = append(convUrlsDownloadStart, eventTrank.NotifyUrl)
				} else if eventTrank.EventType == ADNINE_EVENTTYPE_INSTALL {
					convUrlsInstallEnd = append(convUrlsInstallEnd, eventTrank.NotifyUrl)
				}
			}
			if len(convUrlsDownloadStart) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
				commonAPIResponseConvTrackObject.ConvUrls = convUrlsDownloadStart
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			} else if len(convUrlsDownloadEnd) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
				commonAPIResponseConvTrackObject.ConvUrls = convUrlsDownloadEnd
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			} else if len(convUrlsInstallEnd) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
				commonAPIResponseConvTrackObject.ConvUrls = convUrlsInstallEnd
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			}
		}

		if len(commonAPIResponseConvTrackObjects) > 0 {
			commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
		}

		if responseItem.Adm.Native != nil && len(responseItem.Adm.Native.SmallImg) > 0 {
			commonAPIResponseObject.IconUrl = responseItem.Adm.Native.SmallImg
		} else if responseItem.InteractionObject != nil && len(responseItem.InteractionObject.DeepLink) > 0 {
			commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(responseItem.InteractionObject.DeepLink)
		}

		if responseItem.Adm.Native != nil && responseItem.Adm.Native.Video != nil && len(responseItem.Adm.Native.Video.VideoUrl) > 0 {
			// 视频
			var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
			if responseItem.Adm.Native.Video.Duration > 0 {
				commonAPIResponseVideoObject.Duration = responseItem.Adm.Native.Video.Duration * 1000
			}
			commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight

			commonAPIResponseVideoObject.VideoUrl = responseItem.Adm.Native.Video.VideoUrl

			if len(responseItem.Adm.Native.Video.CoveImgUrl) > 0 {
				commonAPIResponseVideoObject.CoverUrl = responseItem.Adm.Native.Video.CoveImgUrl
			} else if responseItem.Adm.Native.Img != nil && len(responseItem.Adm.Native.Img.Url) > 0 {
				commonAPIResponseVideoObject.CoverUrl = responseItem.Adm.Native.Img.Url
			} else {
				if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			commonAPIResponseVideoObject.EndcardType = 1
			commonAPIResponseVideoObject.EndcardRange = 1

			var commonAPIResponseVideoEventTrackObjects []*up_common.UpCommonAPIResponseVideoEventTrackObject

			videoEventTrackStart := []string{}
			videoEventTrackEnd := []string{}
			videoEventTrackClose := []string{}
			videoEventTrackSkip := []string{}

			for _, eventTrank := range responseItem.EventTrack {
				if eventTrank.EventType == ADNINE_EVENTTYPE_VIDEO_START {
					videoEventTrackStart = append(videoEventTrackStart, eventTrank.NotifyUrl)
				} else if eventTrank.EventType == ADNINE_EVENTTYPE_VIDEO_END {
					videoEventTrackEnd = append(videoEventTrackEnd, eventTrank.NotifyUrl)
				} else if eventTrank.EventType == ADNINE_EVENTTYPE_VIDEO_CLOSE {
					videoEventTrackClose = append(videoEventTrackClose, eventTrank.NotifyUrl)
				} else if eventTrank.EventType == ADNINE_EVENTTYPE_VIDEO_SKIP {
					videoEventTrackSkip = append(videoEventTrackSkip, eventTrank.NotifyUrl)
				}
			}

			if len(videoEventTrackStart) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_START
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = videoEventTrackStart
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			} else if len(videoEventTrackEnd) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_END
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = videoEventTrackEnd
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			} else if len(videoEventTrackClose) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_CLOSE
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = videoEventTrackClose
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			} else if len(videoEventTrackSkip) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_SKIP
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = videoEventTrackSkip
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}
			if len(commonAPIResponseVideoEventTrackObjects) > 0 {
				commonAPIResponseVideoObject.EventTracks = commonAPIResponseVideoEventTrackObjects
			}

			commonAPIResponseObject.Video = &commonAPIResponseVideoObject
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
		} else {
			// 图片
			var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

			if responseItem.Adm.Native != nil && responseItem.Adm.Native.Img != nil && len(responseItem.Adm.Native.Img.Url) > 0 {
				var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
				commonAPIResponseImageObject.Url = responseItem.Adm.Native.Img.Url
				commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
				commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
				commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
			}
			commonAPIResponseObject.Images = commonAPIResponseImageObjects
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

			if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
				p.Common.SetPanicStringAndCodes("images is empty",
					up_common.MH_UP_ERROR_CODE_900201,
					up_common.MH_UP_ERROR_CODE_102006)
			}
		}

		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			p.Common.CategoryInfo.FloorPrice,
			p.Common.CategoryInfo.FinalPrice,
			bidPrice,
			0)

		if responseItem.EventTrack != nil && len(responseItem.EventTrack) > 0 {
			var impressionLinks []string

			p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

			impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

			for _, eventTrank := range responseItem.EventTrack {

				if eventTrank.EventType == ADNINE_EVENTTYPE_SHOW {

					replacedLinkString := p.replaceLinkString(eventTrank.NotifyUrl, winPrice)

					impressionLinks = append(impressionLinks, replacedLinkString)
				}
			}

			commonAPIResponseObject.ImpressionLink = impressionLinks

		}

		if responseItem.EventTrack != nil && len(responseItem.EventTrack) > 0 {
			var clickLinks []string

			p.Common.MhClickLink.AddBigDataParams(bigdataParams)

			clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

			for _, eventTrank := range responseItem.EventTrack {

				if eventTrank.EventType == ADNINE_EVENTTYPE_CLICK || eventTrank.EventType == ADNINE_EVENTTYPE_ACTIVE {
					replacedLinkString := p.replaceLinkString(eventTrank.NotifyUrl, winPrice)

					clickLinks = append(clickLinks, replacedLinkString)
				}
			}

			commonAPIResponseObject.ClickLink = clickLinks
		}

		commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
		commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

		list = append(list, &commonAPIResponseObject)
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("ADNINEDEBUG: responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *AdninePipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
