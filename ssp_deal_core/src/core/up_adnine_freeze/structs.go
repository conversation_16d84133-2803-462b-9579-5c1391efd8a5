package up_adnine_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
)

// AdnineRequest Objects
type AdnineRequestObject struct {
	Bid        string                        `json:"bid"`
	ApiVersion string                        `json:"api_version"`
	Ua         string                        `json:"ua"`
	App        *AdnineRequestAppObject       `json:"app"`
	Device     *AdnineRequestDeviceObject    `json:"device"`
	Network    *AdnineRequestNetworkObject   `json:"network"`
	Gps        *AdnineRequestGeoObject       `json:"gps,omitempty"`
	User       *AdnineRequestUserObject      `json:"user,omitempty"`
	Adspaces   []*AdnineRequestAdspaceObject `json:"adspaces"`
	IsDebug    bool                          `json:"is_debug,omitempty"`
	WebvUa     string                        `json:"webv_ua,omitempty"`
}

type AdnineRequestAppObject struct {
	AppId       string `json:"app_id"`
	ChannelId   string `json:"channel_id,omitempty"`
	AppName     string `json:"app_name,omitempty"`
	PackageName string `json:"package_name,omitempty"`
	Category    string `json:"category,omitempty"`
	AppKeywords string `json:"app_keywords,omitempty"`
	AppVersion  string `json:"app_version"`
}

type AdnineRequestDeviceIdObject struct {
	DeviceId     string                          `json:"device_id"`
	DeviceIdType AdnineRequestDeviceIdTypeEnum   `json:"device_id_type"`
	HashType     AdnineRequestDeviceHashTypeEnum `json:"hash_type"`
}

type AdnineRequestDeviceIdTypeEnum int

const (
	ADNINE_DEVICE_ID_TYPE_UNKNOWN AdnineRequestDeviceIdTypeEnum = iota
	ADNINE_DEVICE_ID_TYPE_IMEI
	ADNINE_DEVICE_ID_TYPE_IDFA
	ADNINE_DEVICE_ID_TYPE_AAID
	ADNINE_DEVICE_ID_TYPE_MAC
	ADNINE_DEVICE_ID_TYPE_IDFV
	ADNINE_DEVICE_ID_TYPE_M2ID
	ADNINE_DEVICE_ID_TYPE_SERIALID
	ADNINE_DEVICE_ID_TYPE_IMSI
	ADNINE_DEVICE_ID_TYPE_OAID
)

type AdnineRequestDeviceHashTypeEnum int

const (
	ADNINE_DEVICE_HASH_TYPE_NONE AdnineRequestDeviceHashTypeEnum = iota
	ADNINE_DEVICE_HASH_TYPE_MD5
	ADNINE_DEVICE_HASH_TYPE_SH1
	ADNINE_DEVICE_HASH_TYPE_OTHER
)

type AdnineOsTypeEnum int

const (
	ADNINE_OS_UNKNOWN AdnineOsTypeEnum = iota
	ADNINE_OS_IOS
	ADNINE_OS_ANDROID
	ADNINE_OS_WP
)

func NewAdnineOsType(os up_common.UpCommonOSEnum) AdnineOsTypeEnum {
	switch os {
	case up_common.MH_UP_COMMON_OS_ANDROID:
		return ADNINE_OS_ANDROID
	case up_common.MH_UP_COMMON_OS_IOS:
		return ADNINE_OS_IOS
	}

	return ADNINE_OS_UNKNOWN
}

type AdnineDeviceTypeEnum int

const (
	ADNINE_DEVICETYPE_UNKNOWN AdnineDeviceTypeEnum = iota
	ADNINE_DEVICETYPE_TABLET
	ADNINE_DEVICETYPE_PHONE
	ADNINE_DEVICETYPE_PC
)

func NewAdnineDeviceType(deviceType up_common.UpCommonDeviceTypeEnum) AdnineDeviceTypeEnum {
	switch deviceType {
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PHONE:
		return ADNINE_DEVICETYPE_PHONE
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PAD:
		return ADNINE_DEVICETYPE_TABLET
	}
	return ADNINE_DEVICETYPE_UNKNOWN
}

type AdnineOrientationEnum int

const (
	FNACY_ORIENTATION_UNKNOWN AdnineOrientationEnum = iota
	FNACY_ORIENTATION_PORTRAIT
	FNACY_ORIENTATION_LANDSCAPE
)

func NewAdnineOrientation(screenDirection up_common.UpCommonScreenDirection) AdnineOrientationEnum {
	switch screenDirection {
	case up_common.MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE:
		return FNACY_ORIENTATION_LANDSCAPE
	case up_common.MH_UP_COMMON_SCREENDIRECTION_PORTRAIT:
		return FNACY_ORIENTATION_PORTRAIT
	}
	return FNACY_ORIENTATION_UNKNOWN
}

type AdnineRequestDeviceObject struct {
	DeviceId          []*AdnineRequestDeviceIdObject `json:"device_id"`
	OsType            AdnineOsTypeEnum               `json:"os_type"`
	OsVersion         string                         `json:"os_version"`
	OsApiLevel        string                         `json:"os_api_level"`
	Brand             string                         `json:"brand"`
	Model             string                         `json:"model"`
	DeviceType        AdnineDeviceTypeEnum           `json:"device_type"`
	Language          string                         `json:"language"`
	ScreenWidth       int                            `json:"screen_width"`
	ScreenHeight      int                            `json:"screen_height"`
	ScreenDensity     float64                        `json:"screen_density"`
	ScreenOrientation AdnineOrientationEnum          `json:"screen_orientation,omitempty"`
	Sensors           []int                          `json:"sensors,omitempty"`
	Jailbreaked       bool                           `json:"jailbreaked,omitempty"`
	HmsVersionCode    string                         `json:"hms_version_code,omitempty"`
	HWAppStoreVersion string                         `json:"app_store_version,omitempty"`
	AppStoreVersion   string                         `json:"appStoreVersion,omitempty"`
}

type AdnineRequestNetworkTypeEnum int

const (
	ADNINE_NETWORK_TYPE_UNKNOWN AdnineRequestNetworkTypeEnum = iota
	ADNINE_NETWORK_TYPE_WIFI
	ADNINE_NETWORK_TYPE_2G
	ADNINE_NETWORK_TYPE_3G
	ADNINE_NETWORK_TYPE_4G
)

func NewAdnineRequestNetworkType(connectType up_common.UpCommonConnectTypeEnum) AdnineRequestNetworkTypeEnum {
	switch connectType {
	case up_common.MH_UP_COMMON_CONNECTTYPE_WIFI:
		return ADNINE_NETWORK_TYPE_WIFI
	case up_common.MH_UP_COMMON_CONNECTTYPE_2G:
		return ADNINE_NETWORK_TYPE_2G
	case up_common.MH_UP_COMMON_CONNECTTYPE_3G:
		return ADNINE_NETWORK_TYPE_3G
	case up_common.MH_UP_COMMON_CONNECTTYPE_4G:
		return ADNINE_NETWORK_TYPE_4G
	}
	return ADNINE_NETWORK_TYPE_UNKNOWN
}

type AdnineRequestCarrierIdEnum int

const (
	ADNINE_CARRIER_UNKNOWN AdnineRequestCarrierIdEnum = 0
	ADNINE_CARRIER_CM                                 = 70120
	ADNINE_CARRIER_CU                                 = 70123
	ADNINE_CARRIER_CT                                 = 70121
)

func NewAdnineRequestCarrierId(carrier up_common.UpCommonCarrierEnum) AdnineRequestCarrierIdEnum {
	switch carrier {
	case up_common.MH_UP_COMMON_CARRIER_CM:
		return ADNINE_CARRIER_CM
	case up_common.MH_UP_COMMON_CARRIER_CT:
		return ADNINE_CARRIER_CT
	case up_common.MH_UP_COMMON_CARRIER_CU:
		return ADNINE_CARRIER_CU
	}
	return ADNINE_CARRIER_UNKNOWN
}

type AdnineRequestNetworkObject struct {
	Ip          string                       `json:"ip"`
	NetworkType AdnineRequestNetworkTypeEnum `json:"network_type"`
	CarrierId   AdnineRequestCarrierIdEnum   `json:"carrier_id"`
}

type AdnineRequestGeoObject struct {
}

type AdnineRequestUserObject struct {
}

type AdnineRequestAdspaceTypeEnum int

const (
	ADNINE_ADSPACE_TYPE_BANNER          AdnineRequestAdspaceTypeEnum = 1
	ADNINE_ADSPACE_TYPE_OPENING         AdnineRequestAdspaceTypeEnum = 2
	ADNINE_ADSPACE_TYPE_INTERSTITIAL    AdnineRequestAdspaceTypeEnum = 3
	ADNINE_ADSPACE_TYPE_NATIVE          AdnineRequestAdspaceTypeEnum = 4
	ADNINE_ADSPACE_TYPE_TEXT            AdnineRequestAdspaceTypeEnum = 5
	ADNINE_ADSPACE_TYPE_INCENTIVE_VIDEO AdnineRequestAdspaceTypeEnum = 12
	ADNINE_ADSPACE_TYPE_FULL_VIDEO      AdnineRequestAdspaceTypeEnum = 13
)

func NewAdnineRequestAdspaceType(posType up_common.UpCommonPosType) AdnineRequestAdspaceTypeEnum {
	switch posType {
	case up_common.MH_UP_COMMON_POSTYPE_BANNER:
		return ADNINE_ADSPACE_TYPE_BANNER
	case up_common.MH_UP_COMMON_POSTYPE_SPLASH:
		return ADNINE_ADSPACE_TYPE_OPENING
	case up_common.MH_UP_COMMON_POSTYPE_INTERSTITIAL:
		return ADNINE_ADSPACE_TYPE_INTERSTITIAL
	case up_common.MH_UP_COMMON_POSTYPE_REWARD_VIDEO:
		return ADNINE_ADSPACE_TYPE_INCENTIVE_VIDEO
	case up_common.MH_UP_COMMON_POSTYPE_FULLSCREEN_VIDEO:
		return ADNINE_ADSPACE_TYPE_FULL_VIDEO
	}
	return ADNINE_ADSPACE_TYPE_NATIVE
}

type AdnineRequestAdspacePositionEnum int

const (
	ADNINE_ADSPACE_POSITION_NONE AdnineRequestAdspacePositionEnum = iota
	ADNINE_ADSPACE_POSITION_FIRST_POS
	ADNINE_ADSPACE_POSITION_OTHERS
)

type AdnineRequestAdspaceObject struct {
	AdspaceId       string                           `json:"adspace_id"`
	AdspaceType     AdnineRequestAdspaceTypeEnum     `json:"adspace_type"`
	AdspacePosition AdnineRequestAdspacePositionEnum `json:"adspace_position"`
	AllowedHtml     bool                             `json:"allowed_html"`
	Width           int                              `json:"width"`
	Height          int                              `json:"height"`
	ImpressionNum   int                              `json:"impression_num"`
	Keywords        []string                         `json:"keywords,omitempty"`
	Channel         string                           `json:"channel,omitempty"`
	OpenType        int                              `json:"open_type"`
	InteractionType []int                            `json:"interaction_type"`
	ImpressionTime  int                              `json:"impression_time,omitempty"`
	PageId          string                           `json:"page_id"`
}

// AdnineResponse Objects

type AdnineResponseObject struct {
	Bid       string                     `json:"bid"`
	Ads       []*AdmineResponseAdsObject `json:"ads"`
	ErrorCode int64                      `json:"error_code"`
	ErrorMsg  string                     `json:"error_msg"`
}

type AdmineResponseAdsObject struct {
	AdspaceId string                          `json:"adspace_id"`
	Creative  []*AdmineResponseCreativeObject `json:"creative"`
}

type AdmineResponseCreativeObject struct {
	BannerId          string                            `json:"banner_id"`
	AdspaceSlotSeq    int                               `json:"adspace_slot_seq"`
	OpenType          int                               `json:"open_type"`
	InteractionType   int                               `json:"interaction_type"`
	InteractionObject *AdmineResponseInteractionObject  `json:"interaction_object"`
	PackageName       string                            `json:"package_name"`
	AdmType           int                               `json:"adm_type"`
	Adm               *AdmineResponseAdmObject          `json:"adm"`
	EventTrack        []*AdmineResponseEventTrackObject `json:"event_track"`
	UaForEvent        string                            `json:"ua_for_event"`
	Headers           map[string]string                 `json:"headers"`
	ValidTime         int64                             `json:"valid_time"`
}

type AdmineResponseInteractionObject struct {
	Url      string `json:"url"`
	UrlType  int    `json:"url_type"`
	IntroUrl string `json:"intro_url"`
	DeepLink string `json:"active_uri"`
	DownType string `json:"down_type"`
	ApkVer   string `json:"apk_ver"`
}

type AdmineResponseAdmObject struct {
	Source string                      `json:"source"`
	Native *AdmineResponseNativeObject `json:"nativ"` // 就是 nativ 不是拼写错误
}

type AdmineResponseNativeObject struct {
	Img            *AdmineResponseImgObject   `json:"img"`
	Title          *AdmineResponseTitleObject `json:"title"`
	SmallImg       string                     `json:"smallImg"`
	Desc           string                     `json:"desc"`
	Video          *AdmineResponseVidoeObject `json:"video"`
	DisableAdMark  int                        `json:"disable_ad_mark"`
	AdvertiserLogo *AdmineResponseImgObject   `json:"advertiser_logo"`
}

type AdmineResponseImgObject struct {
	Url    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type AdmineResponseTitleObject struct {
	Text string `json:"text"`
}

type AdmineResponseVidoeObject struct {
	CoveImgUrl string                        `json:"coveImgUrl"`
	VideoUrl   string                        `json:"videoUrl"`
	Width      int                           `json:"width"`
	Height     int                           `json:"height"`
	Formmat    string                        `json:"format"`
	Duration   int                           `json:"duration"`
	EndFrame   *AdmineResponseEndFrameObject `json:"end_frame"`
}

type AdmineResponseEndFrameObject struct {
}

type AdmineResponseEventTypeEnum string

const (
	ADNINE_EVENTTYPE_SHOW               AdmineResponseEventTypeEnum = "1"
	ADNINE_EVENTTYPE_CLICK              AdmineResponseEventTypeEnum = "2"
	ADNINE_EVENTTYPE_OPEN               AdmineResponseEventTypeEnum = "3"
	ADNINE_EVENTTYPE_DOWNLOAD           AdmineResponseEventTypeEnum = "4"
	ADNINE_EVENTTYPE_INSTALL            AdmineResponseEventTypeEnum = "5"
	ADNINE_EVENTTYPE_ACTIVE             AdmineResponseEventTypeEnum = "6"
	ADNINE_EVENTTYPE_DYNAMIC_SHOW       AdmineResponseEventTypeEnum = "7"
	ADNINE_EVENTTYPE_START_DOWN         AdmineResponseEventTypeEnum = "8"
	ADNINE_EVENTTYPE_START_INSTALL      AdmineResponseEventTypeEnum = "9"
	ADNINE_EVENTTYPE_VIDEO_START        AdmineResponseEventTypeEnum = "10"
	ADNINE_EVENTTYPE_VIDEO_FULL_SCREEN  AdmineResponseEventTypeEnum = "11"
	ADNINE_EVENTTYPE_VIDEO_END          AdmineResponseEventTypeEnum = "12"
	ADNINE_EVENTTYPE_VIDEO_CARD_CLICK   AdmineResponseEventTypeEnum = "13"
	ADNINE_EVENTTYPE_VIDEO_SKIP         AdmineResponseEventTypeEnum = "14"
	ADNINE_EVENTTYPE_VIDEO_LOAD_SUCCEED AdmineResponseEventTypeEnum = "15"
	ADNINE_EVENTTYPE_VIDEO_LOAD_FAILED  AdmineResponseEventTypeEnum = "16"
	ADNINE_EVENTTYPE_VIDEO_MUTE         AdmineResponseEventTypeEnum = "17"
	ADNINE_EVENTTYPE_VIDEO_NON_MUTE     AdmineResponseEventTypeEnum = "18"
	ADNINE_EVENTTYPE_VIDEO_PROGRESS     AdmineResponseEventTypeEnum = "19"
	ADNINE_EVENTTYPE_VIDEO_CLOSE        AdmineResponseEventTypeEnum = "20"
	ADNINE_EVENTTYPE_VIDEO_PAUSE        AdmineResponseEventTypeEnum = "21"
	ADNINE_EVENTTYPE_VIDEO_RESUME       AdmineResponseEventTypeEnum = "22"
	ADNINE_EVENTTYPE_VIDEO_FRAME_COLSE  AdmineResponseEventTypeEnum = "23"
	ADNINE_EVENTTYPE_VIDEO_FRAME_SHOW   AdmineResponseEventTypeEnum = "24"
	ADNINE_EVENTTYPE_VIDEO_FRAME_CLICK  AdmineResponseEventTypeEnum = "25"
	ADNINE_EVENTTYPE_OPEN_SUCCEED       AdmineResponseEventTypeEnum = "26"
	ADNINE_EVENTTYPE_OPEN_FAILURE       AdmineResponseEventTypeEnum = "27"
	ADNINE_EVENTTYPE_AD_CLOSE           AdmineResponseEventTypeEnum = "28"
	ADNINE_EVENTTYPE_APP_EXIST          AdmineResponseEventTypeEnum = "29"
	ADNINE_EVENTTYPE_APP_NOT_EXIST      AdmineResponseEventTypeEnum = "30"
	ADNINE_EVENTTYPE_APP_UNKNOWN_EXIST  AdmineResponseEventTypeEnum = "31"
	ADNINE_EVENTTYPE_VIDEO_PLAY_FAILED  AdmineResponseEventTypeEnum = "32"
	ADNINE_EVENTTYPE_DOWNLOAD_PAUSE     AdmineResponseEventTypeEnum = "33"
	ADNINE_EVENTTYPE_DOWNLOAD_CONTINUE  AdmineResponseEventTypeEnum = "34"
	ADNINE_EVENTTYPE_DOWNLOAD_DELETE    AdmineResponseEventTypeEnum = "35"
)

type AdmineResponseEventTrackObject struct {
	EventType AdmineResponseEventTypeEnum `json:"event_type"`
	NotifyUrl string                      `json:"notify_url"`
}

type AdninePipline struct {
	Common *up_common.UpCommonPipline

	Request  *AdnineRequestObject
	Response *AdnineResponseObject
}

func (r *AdninePipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
