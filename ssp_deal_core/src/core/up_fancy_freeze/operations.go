package up_fancy_freeze

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &FancyPipline{Common: common}
}

/**
* Private Methods
**/

func (p *FancyPipline) replaceLinkString(link string, price int) string {
	linkString := link
	linkString = strings.Replace(linkString, "${AUCTION_DX}", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_DY}", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UX}", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "${AUCTION_UY}", "__UP_Y__", -1)

	// if p.Common.IsLogicPixel {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	// } else {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	// }

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "${AUCTION_DX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_DY}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UX}", "-999", -1)
		linkString = strings.Replace(linkString, "${AUCTION_UY}", "-999", -1)
	}

	if p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		encryptPrice := p.encryptPrice(utils.ConvertIntToString(price))
		if len(encryptPrice) > 0 {

			if strings.Contains(linkString, "${AUCTION_PRICE}") {
				fmt.Println("FANCYDEBUG: linkString:", linkString, "${AUCTION_PRICE}:", encryptPrice, price)
			}

			linkString = strings.Replace(linkString, "${AUCTION_PRICE}", encryptPrice, -1)
		} else {
			fmt.Println("encryptPrice is empty")
		}
	} else {
		linkString = strings.Replace(linkString, "${AUCTION_PRICE}", utils.ConvertIntToString(price), -1)
	}

	return linkString
}

func (p *FancyPipline) encryptKey() string {
	return p.Common.PlatformPos.PlatformAppPriceEncrypt
}

func (p *FancyPipline) encryptPrice(price string) string {
	encryptKey := p.encryptKey()

	if len(encryptKey) == 0 {
		fmt.Println("price: ", price, "PlatformAppPriceEncrypt: ", p.Common.PlatformPos.PlatformAppPriceEncrypt)
		fmt.Printf("PlatformPos: %+v", p.Common.PlatformPos)
		return ""
	}
	result := base64.StdEncoding.EncodeToString(utils.AesCBCPKCS5Encrypt(price, p.encryptKey()))

	return url.QueryEscape(result)
}

// func (p *FancyPipline) decryptPrice(encryptedPrice string) (string, error) {
// 	var priceString string
// 	decodeString, err := base64.StdEncoding.DecodeString(encryptedPrice)
// 	if err != nil {
// 		return priceString, err
// 	}
// 	priceString = string(utils.AesCBCPKCS5Decrypt(decodeString, []byte(p.encryptKey())))
// 	return priceString, nil
// }

/**
 * * 函数式方法模板
 */
func (p *FancyPipline) actionTemplate() *FancyPipline {

	return p
}

/**
* Public Methods
**/

func (p *FancyPipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("FANCYDEBUG: GetFromFancy error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *FancyPipline) SetupRequest() up_common.PiplineInterface {

	// Setup FancyRequestObject
	requestObject := FancyRequestObject{}
	{
		requestObject.Id = p.Common.UUID
		requestObjectImpObject := FancyRequestImpObject{}
		{
			requestObjectImpObject.Id = p.Common.UUID
			requestObjectImpObject.Slotid = p.Common.PlatformPos.PlatformPosID // TODO: 确认一下
			requestObjectImpObject.Tagid = p.Common.PlatformPos.PlatformPosID
			requestObjectImpObject.Width = p.Common.PlatformPos.PlatformPosWidth
			requestObjectImpObject.Height = p.Common.PlatformPos.PlatformPosHeight
			requestObjectImpObject.Bidfloor = float64(p.Common.CategoryInfo.FloorPrice)
			requestObjectImpObject.Secure = 0
		}

		requestObjectImpArray := []*FancyRequestImpObject{}
		requestObjectImpArray = append(requestObjectImpArray, &requestObjectImpObject)
		requestObject.Imp = requestObjectImpArray

		// Setup FancyRequestAppObject
		requestAppObject := FancyRequestAppObject{}
		{
			requestAppObject.Id = p.Common.PlatformPos.PlatformAppID
			requestAppObject.Name = p.Common.PlatformPos.PlatformAppName
			requestAppObject.Bundle = p.Common.PlatformPos.PlatformAppBundle
			requestAppObject.Version = p.Common.PlatformPos.PlatformAppVersion
		}
		requestObject.App = &requestAppObject

		// Setup FancyRequestDeviceObject
		requestAdDiveceObject := FancyRequestDeviceObject{}
		{
			requestAdDiveceObject.Ip = p.Common.MhReq.Device.IP
			requestAdDiveceObject.Ua = p.Common.MhReq.Device.Ua
			requestAdDiveceObject.Osv = p.Common.MhReq.Device.OsVersion

			if len(p.Common.MhReq.Device.Manufacturer) > 0 {
				requestAdDiveceObject.Make = p.Common.MhReq.Device.Manufacturer
			}

			if len(p.Common.MhReq.Device.Model) > 0 {
				requestAdDiveceObject.Model = p.Common.MhReq.Device.Model
			}

			if p.Common.IsAndroid() {
				requestAdDiveceObject.Os = FANCY_OS_ANDROID

				if p.Common.IsAndroidMajorLessThanTen() {
					if len(p.Common.MhReq.Device.Imei) > 0 {
						requestAdDiveceObject.Imei = p.Common.MhReq.Device.Imei
					} else {
						p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				} else {
					if len(p.Common.MhReq.Device.Oaid) > 0 {
						requestAdDiveceObject.Oaid = p.Common.MhReq.Device.Oaid
					} else {
						p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				}

			} else {
				requestAdDiveceObject.Os = FANCY_OS_IOS
				if len(p.Common.MhReq.Device.Idfa) > 0 {
					requestAdDiveceObject.Idfa = p.Common.MhReq.Device.Idfa
				}
			}
		}
		requestObject.Device = &requestAdDiveceObject
	}
	p.Request = &requestObject

	return p
}

func (p *FancyPipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.Device.Os = NewFancyOS(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Device.Osv = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Device.Model = p.Common.ReplacedValues.Model
	}

	p.Request.Device.DeviceType = NewFancyDeviceType(p.Common.ReplacedValues.DeviceType)

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Device.Make = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		p.Request.Device.Imei = p.Common.ReplacedValues.Imei
	}

	// if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
	// 	p.Request.Device.ImeiMd5 = p.Common.ReplacedValues.ImeiMd5
	// }

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		p.Request.Device.Oaid = p.Common.ReplacedValues.Oaid
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		p.Request.Device.AndroidId = p.Common.ReplacedValues.AndroidId
	}

	// if len(p.Common.ReplacedValues.AndroidIdMd5) > 0 {
	// 	p.Request.Device.AndroidIdMd5 = p.Common.ReplacedValues.AndroidIdMd5
	// }

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Device.Ua = p.Common.ReplacedValues.Ua
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		p.Request.Device.Idfa = p.Common.ReplacedValues.Idfa
	}

	p.Request.Device.Orientation = NewFancyOrientation(p.Common.ReplacedValues.ScreenDirection)

	return p
}

func (p *FancyPipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(jsonData))
	httpRequest.Header.Add("Content-Type", "application/json")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode == 204 {
		p.Common.SetPanicStringAndCodes("no fill",
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject FancyResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	fmt.Println("FANCYDEBUG: bodyContent", string(bodyContent))

	p.Response = &responseObject

	if p.Response.Seatbid == nil || len(p.Response.Seatbid) == 0 {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *FancyPipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	for _, responseItem := range p.Response.Seatbid[0].Bid {
		bidPrice := int(responseItem.Price)

		p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
		p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

		// 成功时的价格比例
		randPriceRatio := p.Common.RandPriceRatio()
		winPrice := int(bidPrice * randPriceRatio / 100)

		// 失败时的价格比例
		// randFailedPriceRatio := p.Common.RandFailedPriceRatio()

		if p.Common.CategoryInfo.FinalPrice > bidPrice {
			p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

			// 如果有竞败链接，需要回调处理
			continue
		}

		if responseItem.Adm == nil {
			continue
		}

		var commonAPIResponseObject up_common.UpCommonAPIResponseObject

		if len(responseItem.Adm.Title) > 0 {
			commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Adm.Title)
			commonAPIResponseObject.Title = responseItem.Adm.Title
		}

		if len(responseItem.Adm.Description) > 0 {
			commonAPIResponseObject.Description = responseItem.Adm.Description
		}
		if responseItem.Adm.App != nil && len(responseItem.Adm.App.PackageName) > 0 {
			commonAPIResponseObject.PackageName = responseItem.Adm.App.PackageName
		}

		if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_ANDROID.String() {
			commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE

			if len(responseItem.Adm.LandingPage) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.Adm.LandingPage
				commonAPIResponseObject.AdUrl = responseItem.Adm.LandingPage
			}

			if responseItem.Adm.Ext != nil && len(responseItem.Adm.Ext.AdvertiserName) > 0 {
				commonAPIResponseObject.Publisher = responseItem.Adm.Ext.AdvertiserName
			}

			if responseItem.Adm.App != nil && len(responseItem.Adm.App.Name) > 0 {
				commonAPIResponseObject.AppName = responseItem.Adm.App.Name
			}

			// if responseItem.Adm.App != nil && len(responseItem.Adm.App.AppVersion) > 0 {
			// 	commonAPIResponseObject.AppVersion = responseItem.Adm.App.AppVersion
			// }

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyUrl) > 0 {
			// 	commonAPIResponseObject.PrivacyUrl = responseItem.AppPromotion.PrivacyPolicyUrl
			// }

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyInfo) > 0 {
			// 	commonAPIResponseObject.Permission = responseItem.AppPromotion.PrivacyPolicyInfo
			// } else {
			// 	commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			// }

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyAuthUrl) > 0 {
			// 	commonAPIResponseObject.PermissionUrl = responseItem.AppPromotion.PrivacyAuthUrl
			// }

			commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024

		} else if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_IOS.String() {
			if len(responseItem.Adm.LandingPage) > 0 {
				if strings.Contains(responseItem.Adm.LandingPage, "apple.com") {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
				} else {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
				}

				commonAPIResponseObject.LandPageUrl = responseItem.Adm.LandingPage
				commonAPIResponseObject.AdUrl = responseItem.Adm.LandingPage
			}
		}

		commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

		commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

		var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

		if len(responseItem.Adm.DeepLink) > 0 {
			commonAPIResponseObject.DeepLink = responseItem.Adm.DeepLink

			var convTrack up_common.UpCommonAPIResponseConvTrackObject
			convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(
				p.Common.MhReq,
				p.Common.LocalPos,
				p.Common.PlatformPos,
				p.Common.UUID,
				p.Common.UUID,
				0,
				0,
				0,
				0)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			if responseItem.DpTrackers != nil && len(responseItem.DpTrackers) > 0 {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, responseItem.DpTrackers...)
			}

			convTrack.ConvUrls = respListItemDeepLinkArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

			if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

				var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
				convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
				convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
			}
		}

		if responseItem.DownStartTrackers != nil && len(responseItem.DownStartTrackers) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.DownStartTrackers
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if responseItem.DownCompTrackers != nil && len(responseItem.DownCompTrackers) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.DownCompTrackers
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if responseItem.InstallCompTrackers != nil && len(responseItem.InstallCompTrackers) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.InstallCompTrackers
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if len(commonAPIResponseConvTrackObjects) > 0 {
			commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
		}

		if responseItem.Adm.App != nil && len(responseItem.Adm.App.Icon) > 0 {
			commonAPIResponseObject.IconUrl = responseItem.Adm.App.Icon
		} else {
			commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(responseItem.Adm.DeepLink)
		}

		if responseItem.Adm.Video != nil && len(responseItem.Adm.Video.Url) > 0 {
			// 视频
			var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
			if responseItem.Adm.Video.Duration > 0 {
				commonAPIResponseVideoObject.Duration = responseItem.Adm.Video.Duration * 1000
			}
			commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight

			commonAPIResponseVideoObject.VideoUrl = responseItem.Adm.Video.Url

			if len(responseItem.Adm.Images) > 0 {
				commonAPIResponseVideoObject.CoverUrl = responseItem.Adm.Images[0].Url
			} else {
				if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			commonAPIResponseVideoObject.EndcardType = 1
			commonAPIResponseVideoObject.EndcardRange = 1

			var commonAPIResponseVideoEventTrackObjects []*up_common.UpCommonAPIResponseVideoEventTrackObject

			if responseItem.VideoTrackers != nil && len(responseItem.VideoTrackers) > 0 {
				videoTrackerUrls := []string{}
				for _, tracker := range responseItem.VideoTrackers {
					videoTrackerUrls = append(videoTrackerUrls, tracker.Url)
				}

				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_END
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = videoTrackerUrls
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			// if responseItem.VideoTrackers != nil && len(responseItem.VideoTrackers) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_START
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoStart
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoQuarter) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_FIRSTQUARTILE
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoQuarter
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoMiddle) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_MIDPOINT
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoMiddle
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoThirdQuarter) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_THIRDUARTILE
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoThirdQuarter
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoEnd) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_END
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoEnd
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoMute) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_MUTE
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoMute
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoSkip) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_SKIP
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoSkip
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			// if responseItem.Tracker != nil && len(responseItem.Tracker.VideoClose) > 0 {
			// 	var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			// 	commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_CLOSE
			// 	commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoClose
			// 	commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			// }

			if len(commonAPIResponseVideoEventTrackObjects) > 0 {
				commonAPIResponseVideoObject.EventTracks = commonAPIResponseVideoEventTrackObjects
			}

			commonAPIResponseObject.Video = &commonAPIResponseVideoObject
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
		} else {
			// 图片
			var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

			if len(responseItem.Adm.Images) > 0 {
				for _, image := range responseItem.Adm.Images {
					var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
					commonAPIResponseImageObject.Url = image.Url
					commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
					commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
					commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)

				}
			}
			commonAPIResponseObject.Images = commonAPIResponseImageObjects
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

			if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
				p.Common.SetPanicStringAndCodes("images is empty",
					up_common.MH_UP_ERROR_CODE_900201,
					up_common.MH_UP_ERROR_CODE_102006)
			}
		}

		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			p.Common.CategoryInfo.FloorPrice,
			p.Common.CategoryInfo.FinalPrice,
			bidPrice,
			0)

		if responseItem.ImpTrackers != nil && len(responseItem.ImpTrackers) > 0 {
			var impressionLinks []string

			p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

			impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

			for _, linkString := range responseItem.ImpTrackers {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				impressionLinks = append(impressionLinks, replacedLinkString)
			}

			commonAPIResponseObject.ImpressionLink = impressionLinks

		}

		if responseItem.ClickTrackers != nil && len(responseItem.ClickTrackers) > 0 {
			var clickLinks []string

			p.Common.MhClickLink.AddBigDataParams(bigdataParams)

			clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

			for _, linkString := range responseItem.ClickTrackers {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				clickLinks = append(clickLinks, replacedLinkString)
			}

			if len(responseItem.DpTrackers) > 0 {
				clickLinks = append(clickLinks, responseItem.DpTrackers...)
			}

			commonAPIResponseObject.ClickLink = clickLinks
		}

		if p.Common.IsReportToWin {
			if len(responseItem.Nurl) > 0 && bidPrice > 0 && p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							fmt.Println("gdt win url panic:", err)
						}
					}()

					replacedLinkString := p.replaceLinkString(responseItem.Nurl, winPrice)

					p.Common.RequestUrl(replacedLinkString)
				}()
			}
		}

		commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
		commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

		list = append(list, &commonAPIResponseObject)
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("FANCYDEBUG: responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *FancyPipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
