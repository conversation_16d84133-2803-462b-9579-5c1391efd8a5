package up_fancy_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
)

// FancyRequest Objects
type FancyRequestObject struct {
	Id     string                    `json:"id"`
	Imp    []*FancyRequestImpObject  `json:"imp"`
	App    *FancyRequestAppObject    `json:"app"`
	Device *FancyRequestDeviceObject `json:"device"`
	Site   *FancyRequestSiteObject   `json:"site"`
	User   *FancyRequestUserObject   `json:"user"`
	Tmax   int                       `json:"tmax"`
	Test   bool                      `json:"test"`
	Ext    string                    `json:"ext"`
}

type FancyRequestImpObject struct {
	Id              string                 `json:"id"`
	Slotid          string                 `json:"slotid"`
	Tagid           string                 `json:"tagid"`
	Width           int                    `json:"w"`
	Height          int                    `json:"h"`
	Minduration     int                    `json:"minduration"`
	Maxduration     int                    `json:"maxduration"`
	FormIds         []FancyFormIdEnum      `json:"formids"`
	AdType          FancyAdTypeEnum        `json:"adtype"`
	CType           []FancyCTypeEnum       `json:"ctype"`
	Bidfloor        float64                `json:"bidfloor"`
	Bidtype         int                    `json:"bidtype"`
	Pmp             *FancyRequestPmpObject `json:"pmp"`
	Secure          int                    `json:"secure"`
	SupportInteract []FancyInteractEnum    `json:"support_interact"`
	Impdate         string                 `json:"impdate"`
}

type FancyFormIdEnum int

const (
	FANCY_FORMID_UNKNOWN     FancyFormIdEnum = iota
	FANCY_FORMID_SINGLE      FancyFormIdEnum = 1
	FANCY_FORMID_THREE       FancyFormIdEnum = 5
	FANCY_FORMID_IMAGE_VIDEO FancyFormIdEnum = 6
	FANCY_FORMID_IMAGE_ICON  FancyFormIdEnum = 7
	FANCY_FORMID_THREE_ICON  FancyFormIdEnum = 9
	FANCY_FORMID_IMAGE_TEXT  FancyFormIdEnum = 11
)

type FancyAdTypeEnum int

const (
	FANCY_ADTYPE_UNKNOWN FancyAdTypeEnum = iota
	FANCY_ADTYPE_VIDEOIMAGE
	FANCY_ADTYPE_VIDEOPAUSE
	FANCY_ADTYPE_VIDEOPCORNERMARK
	FANCY_ADTYPE_VIDEOSUSPEND
	FANCY_ADTYPE_BANNER
	FANCY_ADTYPE_SPLASH
	FANCY_ADTYPE_SCREEN
	FANCY_ADTYPE_NATIVE
	FANCY_ADTYPE_VPAID
	FANCY_ADTYPE_CREATIVE
	FANCY_ADTYPE_TEXT
	FANCY_ADTYPE_REWARDEDVIDEO
)

type FancyCTypeEnum int

const (
	FANCY_CTYPE_UNKNOWN FancyCTypeEnum = iota
	FANCY_CTYPE_JPG
	FANCY_CTYPE_PNG
	FANCY_CTYPE_GIF
	FANCY_CTYPE_SWF
	FANCY_CTYPE_MP4
	FANCY_CTYPE_FLV
	FANCY_CTYPE_TEXT
	FANCY_CTYPE_IMAGETEXT
	FANCY_CTYPE_CRITIVE
	FANCY_CTYPE_VAPID
	FANCY_CTYPE_REWARDEDVIDEO
)

type FancyInteractEnum int

const (
	FANCY_INTERACT_H5 FancyInteractEnum = iota
	FANCY_INTERACT_DOWNLOAD
	FANCY_INTERACT_WECHAT
	FANCY_INTERACT_DEEPLINK
)

type FancyRequestPmpObject struct {
	Deal  string `json:"deal"`
	Price int    `json:"price"`
}

type FancyRequestAppObject struct {
	Id         string                     `json:"id"`
	Name       string                     `json:"name"`
	Version    string                     `json:"ver"`
	SdkVersion string                     `json:"sdk_version"`
	Bundle     string                     `json:"bundle"`
	Cat        []string                   `json:"cat"`
	Content    *FancyRequestContentObject `json:"content"`
}

type FancyRequestContentObject struct {
	Title    string   `json:"title"`
	Channel  []string `json:"channel"`
	Cat      []string `json:"cat"`
	Keywords string   `json:"keywords"`
}

type FancyRequestDeviceObject struct {
	Imei           string                  `json:"imei"`
	ImeiMd5        string                  `json:"imeimd5"`
	Oaid           string                  `json:"oaid"`
	OaidMd5        string                  `json:"oaidmd5"`
	AndroidId      string                  `json:"aid"`
	AndroidIdMd5   string                  `json:"aidmd5"`
	Idfa           string                  `json:"idfa"`
	IdfaMd5        string                  `json:"idfamd5"`
	Caid           string                  `json:"caid"`
	CaidVersion    string                  `json:"caid_version"`
	AliAaid        string                  `json:"ali_aaid"`
	Os             FancyRequestOsEnum      `json:"os"`
	Osv            string                  `json:"osv"`
	Make           string                  `json:"make"`
	Model          string                  `json:"model"`
	DeviceType     FancyDeviceTypeEnum     `json:"devicetype"`
	Ip             string                  `json:"ip"`
	Mac            string                  `json:"mac"`
	MacMd5         string                  `json:"macmd5"`
	Ua             string                  `json:"ua"`
	Carrier        FancyRequestCarrierEnum `json:"carrier"`
	Connectiontype int                     `json:"connection_type"`
	ScreenWidth    int                     `json:"sw"`
	ScreenHeight   int                     `json:"sh"`
	ScreenDensity  int                     `json:"den"`
	Orientation    FancyOrientationEnum    `json:"ori"`
	Geo            *FancyRequestGeoObject  `json:"geo"`
	BootMark       string                  `json:"boot_mark"`
	UpdateMark     string                  `json:"update_mark"`
	VerCodeOfHms   string                  `json:"verCodeOfHms"`
	VerCodeOfAG    string                  `json:"verCodeOfAG"`
}

type FancyRequestOsEnum int

const (
	FANCY_OS_UNKNOWN FancyRequestOsEnum = iota
	FANCY_OS_IOS
	FANCY_OS_ANDROID
	FANCY_OS_WINDOWSPHONE
	FANCY_OS_SYMBIAN
	FANCY_OS_WINDOWS
	FANCY_OS_MACOS
)

func NewFancyOS(os up_common.UpCommonOSEnum) FancyRequestOsEnum {
	switch os {
	case up_common.MH_UP_COMMON_OS_ANDROID:
		return FANCY_OS_ANDROID
	case up_common.MH_UP_COMMON_OS_IOS:
		return FANCY_OS_IOS
	}

	return FANCY_OS_ANDROID
}

type FancyDeviceTypeEnum int

const (
	FANCY_DEVICETYPE_UNKNOWN FancyDeviceTypeEnum = iota
	FANCY_DEVICETYPE_MOBILE
	FANCY_DEVICETYPE_PAD
	FANCY_DEVICETYPE_MOBILEDEVICE
	FANCY_DEVICETYPE_PC
	FANCY_DEVICETYPE_TV
)

func NewFancyDeviceType(deviceType up_common.UpCommonDeviceTypeEnum) FancyDeviceTypeEnum {
	switch deviceType {
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PAD:
		return FANCY_DEVICETYPE_PAD
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PHONE:
		return FANCY_DEVICETYPE_MOBILE
	}

	return FANCY_DEVICETYPE_UNKNOWN
}

type FancyRequestCarrierEnum int

const (
	FANCY_CARRIER_UNKNOWN FancyRequestCarrierEnum = iota
	FANCY_CARRIER_CM
	FANCY_CARRIER_CU
	FANCY_CARRIER_CT
)

type FancyRequestConnectiontypeEnum int

const (
	FANCY_CONNECTIONTYPE_UNKNOWN FancyRequestConnectiontypeEnum = iota
	FANCY_CONNECTIONTYPE_WIFI
	FANCY_CONNECTIONTYPE_2G
	FANCY_CONNECTIONTYPE_3G
	FANCY_CONNECTIONTYPE_4G
	FANCY_CONNECTIONTYPE_5G
)

type FancyOrientationEnum int

const (
	FNACY_ORIENTATION_UNKNOWN FancyOrientationEnum = iota
	FNACY_ORIENTATION_PORTRAIT
	FNACY_ORIENTATION_LANDSCAPE
)

func NewFancyOrientation(screenDirection up_common.UpCommonScreenDirection) FancyOrientationEnum {
	switch screenDirection {
	case up_common.MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE:
		return FNACY_ORIENTATION_LANDSCAPE
	case up_common.MH_UP_COMMON_SCREENDIRECTION_PORTRAIT:
		return FNACY_ORIENTATION_PORTRAIT
	}

	return FNACY_ORIENTATION_PORTRAIT
}

type FancyRequestGeoObject struct {
	Lat     float64 `json:"lat"`
	Lon     float64 `json:"lon"`
	GeoType int     `json:"geotype"`
	City    string  `json:"city"`
}

type FancyRequestSiteObject struct {
	Name    string                     `json:"name"`
	Domain  string                     `json:"domain"`
	Page    string                     `json:"page"`
	Ref     string                     `json:"ref"`
	Content *FancyRequestContentObject `json:"content"`
}

type FancyRequestUserObject struct {
	Id          string   `json:"id"`
	Gender      string   `json:"gender"`
	Age         int      `json:"age"`
	AudienceTag []string `json:"audience_tag"`
}

// FancyResponse Objects

type FancyResponseObject struct {
	Id      string                        `json:"id"`
	Seatbid []*FancyResponseSeatBidObject `json:"seatbid"`
	Bidid   string                        `json:"bidid"`
}

type FancyResponseSeatBidObject struct {
	Seat string                    `json:"seat"`
	Bid  []*FancyResponseBidObject `json:"bid"`
}

type FancyResponseBidObject struct {
	Id                   string                             `json:"id"`
	Impid                string                             `json:"impid"`
	Price                float64                            `json:"price"`
	Dealid               string                             `json:"dealid"`
	Crid                 string                             `json:"crid"`
	Nurl                 string                             `json:"nurl"`
	Adm                  *FancyResponseAdmObject            `json:"adm"`
	ImpTrackers          []string                           `json:"imp_trackers"`
	ClickTrackers        []string                           `json:"click_trackers"`
	DownStartTrackers    []string                           `json:"down_start_trackers"`
	DownCompTrackers     []string                           `json:"down_comp_trackers"`
	InstallStartTrackers []string                           `json:"install_start_trackers"`
	InstallCompTrackers  []string                           `json:"install_comp_trackers"`
	VideoTrackers        []*FancyResponseVideoTrackerObject `json:"video_trackers"`
	DpTrackers           []string                           `json:"dp_trackers"`
	SdkSwitch            string                             `json:"sdk_switch"`
}

type FancyResponseVideoTrackerObject struct {
	Event int    `json:"event"`
	Url   string `json:"url"`
}

type FancyResponseAdmObject struct {
	Title       string                       `json:"title"`
	Description string                       `json:"desc"`
	Images      []*FancyResponseImageObject  `json:"img"`
	Video       *FancyResponseVideoObject    `json:"video"`
	LandingPage string                       `json:"land"`
	Interact    FancyInteractEnum            `json:"interact"`
	CType       FancyCTypeEnum               `json:"ctype"`
	FormId      FancyFormIdEnum              `json:"formid"`
	DeepLink    string                       `json:"dplink"`
	App         *FancyResponseAppObject      `json:"app"`
	Ext         *FancyResponseExtObject      `json:"ext"`
	LandDesc    *FancyResponseLandDescObject `json:"land_desc"`
}

type FancyResponseImageObject struct {
	Url    string `json:"url"`
	Width  int    `json:"w"`
	Height int    `jason:"h"`
	Md5    string `json:"md5"`
}

type FancyResponseVideoObject struct {
	Url      string `json:"url"`
	Width    int    `json:"w"`
	Height   int    `jason:"h"`
	Duration int    `json:"duration"`
	Md5      string `json:"md5"`
}

type FancyResponseAppObject struct {
	Name        string                        `json:"name"`
	Icon        string                        `json:"icon"`
	PackageName string                        `json:"package_name"`
	WechatExt   *FancyResponseWechatExtObject `json:"wechat_ext"`
	AppId       string                        `json:"app_id"`
}

type FancyResponseExtObject struct {
	Icon           string `json:"icon"`
	Logo           string `json:"logo"`
	AdvertiserName string `json:"advertiser_name"`
	SubTitle       string `json:"sub_title"`
	ButtonText     string `json:"btn"`
	Style          string `json:"style"`
}

type FancyResponseWechatExtObject struct {
}

type FancyResponseLandDescObject struct {
}

type FancyResponseShardDataObject struct {
}

type FancyPipline struct {
	Common *up_common.UpCommonPipline

	Request  *FancyRequestObject
	Response *FancyResponseObject
}

func (r *FancyPipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
