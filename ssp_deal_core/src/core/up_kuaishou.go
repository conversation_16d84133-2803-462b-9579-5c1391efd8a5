package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromKuaishou ...
func GetFromKuaishou(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from kuaishou")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////
	// 判定逻辑
	// if strings.Contains(iosReportMainParameter, "idfa") {
	// }
	// if strings.Contains(iosReportMainParameter, "caid") {
	// }
	// if strings.Contains(iosReportMainParameter, "yinzi") {
	// }

	// if xxxxxxOK {
	// } else if len(iosReportSubParameter1) > 0 {
	// 	if strings.Contains(iosReportSubParameter1, "idfa") {
	// 	} else if strings.Contains(iosReportSubParameter1, "caid") {
	// 	} else if strings.Contains(iosReportSubParameter1, "yinzi") {
	// 	}
	// }

	// if xxxxxxOK {
	// } else if len(iosReportSubParameter2) > 0 {
	// 	if strings.Contains(iosReportSubParameter2, "idfa") {
	// 	} else if strings.Contains(iosReportSubParameter2, "caid") {
	// 	} else if strings.Contains(iosReportSubParameter2, "yinzi") {
	// 	}
	// }

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	////////////////////////////////////////////////////////////////////////////////////////
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["appId"] = platformPos.PlatformAppID
	reqAppInfoMap["packageName"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	reqNetworkMap := map[string]interface{}{}
	reqNetworkMap["ip"] = mhReq.Device.IP
	reqNetworkMap["connectionType"] = 1
	if mhReq.Network.ConnectType == 0 {
		// 未知
		reqNetworkMap["connectionType"] = 1
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		reqNetworkMap["connectionType"] = 100
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		reqNetworkMap["connectionType"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		reqNetworkMap["connectionType"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		reqNetworkMap["connectionType"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		reqNetworkMap["connectionType"] = 5
	} else if mhReq.Network.ConnectType == 8 {
		// 以太网
		reqNetworkMap["connectionType"] = 101
	}

	reqNetworkMap["operatorType"] = mhReq.Network.Carrier
	if mhReq.Network.Carrier == 3 {
		reqNetworkMap["operatorType"] = 2
	} else if mhReq.Network.Carrier == 2 {
		reqNetworkMap["operatorType"] = 3
	}

	// if len(mhReq.Device.Mac) > 0 {
	// reqNetworkMap["mac"] = mhReq.Device.Mac
	// }

	reqImpInfoArray := []map[string]interface{}{}
	reqImpInfoItemMap := map[string]interface{}{}
	reqImpInfoItemMap["posId"] = utils.ConvertStringToInt(platformPos.PlatformPosID)
	reqImpInfoItemMap["adNum"] = mhReq.Pos.AdCount
	reqImpInfoItemMap["width"] = platformPos.PlatformPosWidth
	reqImpInfoItemMap["height"] = platformPos.PlatformPosHeight
	// reqImpInfoItemMap["entryScene"] = platformPos.PlatformPosHeight
	// reqImpInfoItemMap["pageId"] = platformPos.PlatformPosHeight
	// reqImpInfoItemMap["subPageId"] = platformPos.PlatformPosHeight
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="8">原生模版</el-radio>
	// <el-radio label="7">原生2.0</el-radio>
	// <el-radio label="5">原生视频</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="6">信息流</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">视频内容联盟</el-radio>
	// <el-radio label="12">视频内容联盟组件</el-radio>
	if platformPos.PlatformPosType == 4 {
		reqImpInfoItemMap["adStyle"] = 1
	} else if platformPos.PlatformPosType == 11 {
		reqImpInfoItemMap["adStyle"] = 2
	} else if platformPos.PlatformPosType == 9 {
		reqImpInfoItemMap["adStyle"] = 3
	} else if platformPos.PlatformPosType == 2 {
		reqImpInfoItemMap["adStyle"] = 4
	}
	reqImpInfoItemMap["cpmBidFloor"] = localPosFloorPrice
	// if platformPos.PlatformPosIsSupportQuerys == 1 {
	// 	if platformPos.PlatformPosSupportQuerysType == 0 {
	// 		if len(mhReq.Pos.Query) > 0 {
	// 			reqImpInfoItemMap["query"] = mhReq.Pos.Query
	// 		}
	// 	} else if platformPos.PlatformPosSupportQuerysType == 1 {
	// 		if len(platformPos.PlatformPosSupportQuerys) > 0 {
	// 			tmpArray := strings.Split(platformPos.PlatformPosSupportQuerys, ",")
	// 			reqImpInfoItemMap["query"] = tmpArray[rand.Intn(len(tmpArray))]
	// 		}
	// 	}
	// }
	reqImpInfoArray = append(reqImpInfoArray, reqImpInfoItemMap)

	ksImeiMd5 := ""
	ksAndroidIDMd5 := ""
	ksOaid := ""
	ksIdfa := ""

	reqDeviceInfoMap := map[string]interface{}{}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				// reqDeviceInfoMap["imei"] = mhReq.Device.Imei
				reqDeviceInfoMap["imeiMd5"] = strings.ToLower(utils.GetMd5(mhReq.Device.Imei))
				ksImeiMd5 = strings.ToLower(utils.GetMd5(mhReq.Device.Imei))
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				reqDeviceInfoMap["imeiMd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
				ksImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
				ksOaid = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {

		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa

				ksIdfa = mhReq.Device.Idfa
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var reqDeviceInfoCaidMapArray []map[string]interface{}

				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["kenyId"] = item.CAID
						reqDeviceInfoCaidMap["version"] = item.CAIDVersion

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
						break
					}
				}

				if len(reqDeviceInfoCaidMapArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
				}
			} else {
				var reqDeviceInfoCaidMapArray []map[string]interface{}

				for _, item := range mhReq.Device.CAIDMulti {
					reqDeviceInfoCaidMap := map[string]interface{}{}
					reqDeviceInfoCaidMap["kenyId"] = item.CAID
					reqDeviceInfoCaidMap["version"] = item.CAIDVersion
					reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
				}

				if len(reqDeviceInfoCaidMapArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
				reqDeviceInfoMap["country"] = "CHN"
				reqDeviceInfoMap["language"] = tmpLanguage
				reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
				// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
				// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
				memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
				reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
				hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
				reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
				reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
				reqDeviceInfoMap["timeZone"] = tmpTimeZone
				reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa

					ksIdfa = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["kenyId"] = item.CAID
							reqDeviceInfoCaidMap["version"] = item.CAIDVersion

							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
							break
						}
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["kenyId"] = item.CAID
						reqDeviceInfoCaidMap["version"] = item.CAIDVersion
						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
					reqDeviceInfoMap["country"] = "CHN"
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
					reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
					hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
					reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
					reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
					reqDeviceInfoMap["timeZone"] = tmpTimeZone
					reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa

					ksIdfa = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["kenyId"] = item.CAID
							reqDeviceInfoCaidMap["version"] = item.CAIDVersion

							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
							break
						}
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["kenyId"] = item.CAID
						reqDeviceInfoCaidMap["version"] = item.CAIDVersion
						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
					reqDeviceInfoMap["country"] = "CHN"
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
					reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
					hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
					reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
					reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
					reqDeviceInfoMap["timeZone"] = tmpTimeZone
					reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
				}
			}
		}

		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from ks error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if mhReq.Device.Os == "android" {
		reqDeviceInfoMap["osType"] = 1
		reqDeviceInfoMap["platform"] = 3
		if len(mhReq.Device.Manufacturer) > 0 {
			if mhReq.Device.Manufacturer != "unknown" {
				reqDeviceInfoMap["deviceBrand"] = mhReq.Device.Manufacturer
			} else if strings.Contains(mhReq.Device.Manufacturer, "MI") || strings.Contains(mhReq.Device.Manufacturer, "Redmi") {
				reqDeviceInfoMap["deviceBrand"] = "Xiaomi"
			} else if strings.Contains(mhReq.Device.Manufacturer, "OPPO") || strings.Contains(mhReq.Device.Manufacturer, "oppo") {
				reqDeviceInfoMap["deviceBrand"] = "OPPO"
			} else if strings.Contains(mhReq.Device.Manufacturer, "vivo") {
				reqDeviceInfoMap["deviceBrand"] = "vivo"
			} else if strings.Contains(mhReq.Device.Manufacturer, "HONOR") || strings.Contains(mhReq.Device.Manufacturer, "HUAWEI") {
				reqDeviceInfoMap["deviceBrand"] = "HUAWEI"
			}
		} else {
			reqDeviceInfoMap["deviceBrand"] = "unknown"
		}
	} else if mhReq.Device.Os == "ios" {
		reqDeviceInfoMap["osType"] = 2
		reqDeviceInfoMap["platform"] = 1
		reqDeviceInfoMap["deviceBrand"] = "Apple"
	}

	if len(mhReq.Device.Model) > 0 {
		reqDeviceInfoMap["deviceModel"] = mhReq.Device.Model
	}
	if len(mhReq.Device.OsVersion) > 0 {
		reqDeviceInfoMap["osVersion"] = mhReq.Device.OsVersion
	}
	if mhReq.Device.ScreenWidth > 0 {
		reqDeviceInfoMap["screenWidth"] = mhReq.Device.ScreenWidth
	}
	if mhReq.Device.ScreenHeight > 0 {
		reqDeviceInfoMap["screenHeight"] = mhReq.Device.ScreenHeight
	}

	reqDeviceInfoMap["language"] = "zh"

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// if platformPos.PlatformAppID == "543800007" || platformPos.PlatformAppID == "543800008" ||
	// 	platformPos.PlatformAppID == "543800009" || platformPos.PlatformAppID == "543800010" ||
	// 	platformPos.PlatformAppID == "543800011" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("bigdata req did panic:", err)
	// 			}
	// 		}()
	// 		models.BigDataAdxZuiYouReqDID(c, bigdataUID, mhReq)
	// 	}()
	// } else if platformPos.PlatformAppID == "555600005" || platformPos.PlatformAppID == "555600006" ||
	// 	platformPos.PlatformAppID == "555600001" || platformPos.PlatformAppID == "555600002" ||
	// 	platformPos.PlatformAppID == "555600007" || platformPos.PlatformAppID == "555600008" ||
	// 	platformPos.PlatformAppID == "555600003" || platformPos.PlatformAppID == "555600004" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("bigdata req did panic:", err)
	// 			}
	// 		}()
	// 		models.BigDataAdxRMWReqDID(c, bigdataUID, mhReq)
	// 	}()
	// }
	// go func() {
	// 	defer func() {
	// 		if err := recover(); err != nil {
	// 			fmt.Println("redis did panic:", err)
	// 		}
	// 	}()
	// 	models.SetDIDRedis(c, mhReq, localPos, platformPos)
	// }()

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				// fmt.Println("kbg_debug ks android replace_key did value: ", localPos.LocalPosID, redisDIDValue)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imeiMd5")
					delete(reqDeviceInfoMap, "oaid")

					ksImeiMd5 = ""
					ksAndroidIDMd5 = ""
					ksOaid = ""

					reqDeviceInfoMap["osVersion"] = didRedisData.OsVersion
					reqDeviceInfoMap["deviceModel"] = didRedisData.Model
					reqDeviceInfoMap["deviceBrand"] = didRedisData.Manufacturer
					if didRedisData.Version == 1 {
						reqNetworkMap["connectionType"] = 1
						if didRedisData.ConnectType == 0 {
							// 未知
							reqNetworkMap["connectionType"] = 1
						} else if didRedisData.ConnectType == 1 {
							// wifi
							reqNetworkMap["connectionType"] = 100
						} else if didRedisData.ConnectType == 2 {
							// 2G
							reqNetworkMap["connectionType"] = 2
						} else if didRedisData.ConnectType == 3 {
							// 3G
							reqNetworkMap["connectionType"] = 3
						} else if didRedisData.ConnectType == 4 {
							// 4G
							reqNetworkMap["connectionType"] = 4
						} else if didRedisData.ConnectType == 7 {
							// 5G
							reqNetworkMap["connectionType"] = 5
						} else if didRedisData.ConnectType == 8 {
							// 以太网
							reqNetworkMap["connectionType"] = 101
						}

						reqNetworkMap["operatorType"] = didRedisData.Carrier
						if didRedisData.Carrier == 3 {
							reqNetworkMap["operatorType"] = 2
						} else if didRedisData.Carrier == 2 {
							reqNetworkMap["operatorType"] = 3
						}
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imeiMd5"] = utils.GetMd5(didRedisData.Imei)

							ksImeiMd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imeiMd5"] = strings.ToLower(didRedisData.ImeiMd5)

							ksImeiMd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid

							ksOaid = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// headers -> ua
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua
						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					// osv model key
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					// fmt.Println("kbg_debug ks ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						delete(reqDeviceInfoMap, "idfa")
						delete(reqDeviceInfoMap, "systemBootTimeMilliSec")
						delete(reqDeviceInfoMap, "country")
						delete(reqDeviceInfoMap, "language")
						delete(reqDeviceInfoMap, "deviceNameMd5")
						delete(reqDeviceInfoMap, "physicalMemoryKBytes")
						delete(reqDeviceInfoMap, "hardDiskSizeKBytes")
						delete(reqDeviceInfoMap, "systemUpdateTimeNanoSec")
						delete(reqDeviceInfoMap, "timeZone")
						delete(reqDeviceInfoMap, "kenyIdList")
						delete(reqDeviceInfoMap, "deviceFileTime")

						reqDeviceInfoMap["osVersion"] = didRedisData.OsVersion
						reqDeviceInfoMap["deviceModel"] = didRedisData.Model

						if didRedisData.Version == 1 {
							reqNetworkMap["connectionType"] = 1
							if didRedisData.ConnectType == 0 {
								// 未知
								reqNetworkMap["connectionType"] = 1
							} else if didRedisData.ConnectType == 1 {
								// wifi
								reqNetworkMap["connectionType"] = 100
							} else if didRedisData.ConnectType == 2 {
								// 2G
								reqNetworkMap["connectionType"] = 2
							} else if didRedisData.ConnectType == 3 {
								// 3G
								reqNetworkMap["connectionType"] = 3
							} else if didRedisData.ConnectType == 4 {
								// 4G
								reqNetworkMap["connectionType"] = 4
							} else if didRedisData.ConnectType == 7 {
								// 5G
								reqNetworkMap["connectionType"] = 5
							} else if didRedisData.ConnectType == 8 {
								// 以太网
								reqNetworkMap["connectionType"] = 101
							}

							reqNetworkMap["operatorType"] = didRedisData.Carrier
							if didRedisData.Carrier == 3 {
								reqNetworkMap["operatorType"] = 2
							} else if didRedisData.Carrier == 2 {
								reqNetworkMap["operatorType"] = 3
							}
						}

						ksIdfa = ""
						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								reqDeviceInfoMap["idfa"] = didRedisData.Idfa

								ksIdfa = didRedisData.Idfa

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var reqDeviceInfoCaidMapArray []map[string]interface{}
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["kenyId"] = item.CAID
										reqDeviceInfoCaidMap["version"] = item.CAIDVersion

										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
										break
									}
								}
								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
								}
							} else {
								var reqDeviceInfoCaidMapArray []map[string]interface{}

								for _, item := range tmpCAIDMulti {
									reqDeviceInfoCaidMap := map[string]interface{}{}
									reqDeviceInfoCaidMap["kenyId"] = item.CAID
									reqDeviceInfoCaidMap["version"] = item.CAIDVersion
									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
								reqDeviceInfoMap["country"] = "CHN"
								reqDeviceInfoMap["language"] = tmpLanguage
								reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
								// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
								// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
								memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
								reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
								hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
								reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
								reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
								reqDeviceInfoMap["timeZone"] = tmpTimeZone
								reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["idfa"] = didRedisData.Idfa

									ksIdfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var reqDeviceInfoCaidMapArray []map[string]interface{}
									for _, item := range tmpCAIDMulti {

										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											reqDeviceInfoCaidMap := map[string]interface{}{}
											reqDeviceInfoCaidMap["kenyId"] = item.CAID
											reqDeviceInfoCaidMap["version"] = item.CAIDVersion

											reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
											break
										}
									}
									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["kenyId"] = item.CAID
										reqDeviceInfoCaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
									}

									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
									reqDeviceInfoMap["country"] = "CHN"
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
									reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
									hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
									reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
									reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
									reqDeviceInfoMap["timeZone"] = tmpTimeZone
									reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["idfa"] = didRedisData.Idfa

									ksIdfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var reqDeviceInfoCaidMapArray []map[string]interface{}
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											reqDeviceInfoCaidMap := map[string]interface{}{}
											reqDeviceInfoCaidMap["kenyId"] = item.CAID
											reqDeviceInfoCaidMap["version"] = item.CAIDVersion

											reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
											break
										}
									}
									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["kenyId"] = item.CAID
										reqDeviceInfoCaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
									}

									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										reqDeviceInfoMap["kenyIdList"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									reqDeviceInfoMap["systemBootTimeMilliSec"] = tmpDeviceStartSec
									reqDeviceInfoMap["country"] = "CHN"
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									memTotalInt64 := utils.ConvertStringToInt64(tmpPhysicalMemoryByte)
									reqDeviceInfoMap["physicalMemoryKBytes"] = memTotalInt64
									hardDiskSizeInt64 := utils.ConvertStringToInt64(tmpHarddiskSizeByte)
									reqDeviceInfoMap["hardDiskSizeKBytes"] = hardDiskSizeInt64
									reqDeviceInfoMap["systemUpdateTimeNanoSec"] = tmpSystemUpdateSec
									reqDeviceInfoMap["timeZone"] = tmpTimeZone
									reqDeviceInfoMap["deviceFileTime"] = tmpDeviceBirthSec
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// headers -> ua
						if platformPos.PlatformAppIsReplaceDIDUa == 1 {
							destConfigUA = didRedisData.Ua
							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from ks error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from ks error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"protocolVersion": "1.1.17",
		"appInfo":         reqAppInfoMap,
		"deviceInfo":      reqDeviceInfoMap,
		"networkInfo":     reqNetworkMap,
		"impInfo":         reqImpInfoArray,
	}
	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)

	if platformPos.PlatformAppID == "1088700007" || platformPos.PlatformAppID == "1088700011" || platformPos.PlatformAppID == "1088700013" {
		tmpMHReq := *mhReq

		if isHaveReplace {
			tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
			tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
			tmpMHReq.Device.AndroidID = bigdataReplaceDIDData.AndroidID
			tmpMHReq.Device.AndroidIDMd5 = bigdataReplaceDIDData.AndroidIDMd5
			tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
			tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
			tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
			tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
			tmpMHReq.Device.Model = bigdataReplaceDIDData.Model
			tmpMHReq.Device.Manufacturer = bigdataReplaceDIDData.Manufacturer
			tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
			tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
			tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
			tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
			tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
			tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel
			tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
			tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
			tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
			tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
			// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
			// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion

			if len(bigdataReplaceDIDData.CAIDMultiJson) > 0 {
				var tmpCAIDMulti []models.MHReqCAIDMulti
				json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)

				tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
			}

			tmpMHReq.Network.ConnectType = bigdataReplaceDIDData.ConnectType
			tmpMHReq.Network.Carrier = bigdataReplaceDIDData.Carrier

			tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
		}

		go models.BigDataHoloDebugJson3(uuid.NewV4().String(), tmpMHReq.Device.DIDMd5,
			localPos.LocalAppID, localPos.LocalPosID,
			platformPos.PlatformAppID, platformPos.PlatformPosID,
			tmpMHReq.Device.OsVersion,
			tmpMHReq.Device.Model,
			tmpMHReq.Device.Manufacturer,
			tmpMHReq.Device.IP,
			tmpMHReq.Device.LBSIPCountry,
			tmpMHReq.Device.LBSIPProvince,
			tmpMHReq.Device.LBSIPCity,
			string(jsonData),
		)
	}

	// if platformPos.PlatformAppID == "516400018" || platformPos.PlatformAppID == "516400019" {
	// 	deviceBrand, _ := reqDeviceInfoMap["deviceBrand"].(string)
	// 	deviceModel, _ := reqDeviceInfoMap["deviceModel"].(string)
	// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"&up_ks_req_"+platformPos.PlatformAppID, string(jsonData), deviceBrand, deviceModel, mhReq.Device.IP, mhReq.Device.DIDMd5)
	// }

	// fmt.Println("ks req: " + string(jsonData))
	// if mhReq.Device.Os == "ios" {
	// 	fmt.Println("kbg_debug_ks 1: ", platformPos.PlatformAppID, iosReportMainParameter, iosReportSubParameter1, iosReportSubParameter2)
	// 	fmt.Println("kbg_debug_ks 2: ", isIOSToUpReportIDFA, isIOSToUpReportCAID, isIOSToUpReportYinZi)
	// 	fmt.Println("kbg_debug_ks req: ", string(jsonData))
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"
	httpHeader["User-Agent"] = destConfigUA

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, config.UpKuaiShouURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&ks", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	ksRespStu := KsRespStu{}
	json.Unmarshal([]byte(bodyContent), &ksRespStu)
	// fmt.Println(ksRespStu.LLSID)

	if ksRespStu.Result != 1 {
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006

		// ks返回码, 跟gdt统一, 返回错误码
		bigdataExtra.UpRespCode = ksRespStu.Result

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(ksRespStu.ImpAdInfo) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006

		// ks返回码, 跟gdt统一, 无填充
		bigdataExtra.UpRespCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ks返回码, 跟gdt统一, 有填充
	bigdataExtra.UpRespCode = 0

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_resp", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, impAdInfoItem := range ksRespStu.ImpAdInfo {
		AdInfo := impAdInfoItem.AdInfo
		if len(AdInfo) == 0 {
			respTmpInternalCode = 900103
			respTmpRespFailedNum = mhReq.Pos.AdCount
			continue
		}

		if len(AdInfo) > 1 {
			sort.Sort(KsEcpmSort(AdInfo))
		}

		for _, adInfoItem := range AdInfo {
			respTmpRespAllNum = respTmpRespAllNum + 1

			respListItemMap := map[string]interface{}{}

			// 随机95-98%替换__PR__
			randPRValue := 100
			if platformPos.PlatformAppReportWinType == 0 {
				randPRValue = 100
			} else if platformPos.PlatformAppReportWinType == 1 {
				tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
				tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
				if tmp1 <= tmp2 {
					randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
				}
			}

			ksEcpm := adInfoItem.AdBaseInfo.Ecpm

			respTmpPrice = respTmpPrice + ksEcpm

			// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
			if ksEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
				ksEcpm = platformPos.PlatformPosEcpm
			}

			ksLossNoticeURL := getKSPriceFailedURL(adInfoItem.AdBaseInfo.ConvURL, platformPos, ksEcpm, ksRespStu.LLSID)
			// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
			if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
				if localPosFinalPrice > ksEcpm {
					respTmpInternalCode = 900104
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
					// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					// }
					continue
				}
			}

			// 填充后限制
			isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
			if isAllInOneLimitAfterUpRespOK {
			} else {
				respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
				// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
				// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
				// }
				continue
			}

			// 下发ecpm
			// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
			tmpEcpm := 0
			if localPos.LocalPosEcpmType == 0 {
				// 不下发
				tmpEcpm = localPos.LocalPosEcpm
			} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
				tmpEcpm = int(float32(ksEcpm) * (float32(100) - localPosProfitRate) / 100)
				if tmpEcpm < 1 {
					tmpEcpm = 0
				}

				respListItemMap["ecpm"] = tmpEcpm
			} else if localPos.LocalPosEcpmType == 2 {
				tmpEcpm = localPos.LocalPosEcpm
				respListItemMap["ecpm"] = localPos.LocalPosEcpm
			}

			// adid
			maplehazeAdId := uuid.NewV4().String()
			respListItemMap["ad_id"] = maplehazeAdId

			// title
			if len(adInfoItem.AdBaseInfo.AppName) > 0 {
				respListItemMap["title"] = adInfoItem.AdBaseInfo.AppName
			}

			// description
			if len(adInfoItem.AdBaseInfo.AdDescription) > 0 {
				respListItemMap["description"] = adInfoItem.AdBaseInfo.AdDescription
			}

			// crid
			respListItemMap["crid"] = utils.ConvertInt64ToString(adInfoItem.AdBaseInfo.CreativeID)

			// req_width, req_height
			respListItemMap["req_width"] = platformPos.PlatformPosWidth
			respListItemMap["req_height"] = platformPos.PlatformPosHeight

			// 是否有deeplink
			tmpIsHasDeepLink := false
			// deep link
			if len(adInfoItem.AdConversionInfo.DeeplinkURL) > 0 {
				respListItemMap["deep_link"] = adInfoItem.AdConversionInfo.DeeplinkURL

				tmpIsHasDeepLink = true
			}

			if len(adInfoItem.AdConversionInfo.MarketURL) > 0 {
				respListItemMap["market_url"] = adInfoItem.AdConversionInfo.MarketURL
			}

			// package name
			destPackageName := ""
			if len(adInfoItem.AdBaseInfo.AppPackageName) > 0 {
				respListItemMap["package_name"] = adInfoItem.AdBaseInfo.AppPackageName

				destPackageName = adInfoItem.AdBaseInfo.AppPackageName
			} else {
				hackPackageName := GetPackageNameByDeeplink(adInfoItem.AdConversionInfo.DeeplinkURL)
				if len(hackPackageName) > 0 {
					respListItemMap["package_name"] = hackPackageName
					destPackageName = hackPackageName
				}
			}

			// icon_url
			if len(adInfoItem.AdBaseInfo.AppIconURL) > 0 {
				respListItemMap["icon_url"] = adInfoItem.AdBaseInfo.AppIconURL
			} else {
				respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.AdConversionInfo.DeeplinkURL)
			}

			isVideo := false

			// 广告素材总体类型 1:视 频 2:单图 3:多图
			if adInfoItem.AdMaterialInfo.MaterialType == 1 {
				isVideo = true

				respListVideoItemMap := map[string]interface{}{}
				if adInfoItem.AdMaterialInfo.MaterialFeature[0].VideoDuration > 0 {
					respListVideoItemMap["duration"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].VideoDuration * 1000

					// 过滤video_duration
					isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.AdMaterialInfo.MaterialFeature[0].VideoDuration)
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
						// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
						// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
						// }
						continue
					}
				}
				respListVideoItemMap["width"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width
				respListVideoItemMap["height"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height

				if adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width > 0 && adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height > 0 {
					// 过滤素材方向, 大小
					isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width, adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height)
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
						// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
						// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
						// }
						continue
					}
				}

				respListVideoItemMap["video_url"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialURL
				if len(adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialURL) == 0 {
					fmt.Println("wrong video url")
					respTmpInternalCode = 900105
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
					// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					// }
					continue
				}
				respListVideoItemMap["cover_url"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].CoverURL

				if adInfoItem.AdBaseInfo.AdShowDuration > 0 {
					respListVideoItemMap["skip_min_time"] = adInfoItem.AdBaseInfo.AdShowDuration * 1000
				}
				respListVideoItemMap["endcard_type"] = 1
				if localPos.LocalPosType == 9 {
					respListVideoItemMap["skip_min_time"] = 0
					respListVideoItemMap["endcard_range"] = 0
				}

				// event track
				// 有返回，仅激励视频上报__ACTION__中399（开始播放）和400（播放完成），其他情况不上报
				if platformPos.PlatformPosType == 11 {
					var respListEventTrackURLMap []map[string]interface{}

					ksConvURL := adInfoItem.AdBaseInfo.ConvURL
					if ksEcpm > 0 {
						// priceKey := "123456789abcdefghijklmnopqrstuvw"
						macroPrice := utils.ConvertIntToString(int(ksEcpm * randPRValue / 100))
						if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
							priceKey := platformPos.PlatformAppPriceEncrypt
							decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
							macroPrice = url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))

							ksConvURL = strings.Replace(ksConvURL, "__PR__", macroPrice, -1)
							ksConvURL = strings.Replace(ksConvURL, "__PRTYPE__", "encrypt", -1)

						} else {
							ksConvURL = strings.Replace(ksConvURL, "__PR__", macroPrice, -1)
						}
						// decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
						// escapeMacroPrice := url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))
						// ksConvURL = strings.Replace(ksConvURL, "__PRTYPE__", escapeMacroPrice, -1)
					}

					// track params
					mhTrackParams := url.Values{}
					mhTrackParams.Add("log", up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, ksEcpm, tmpEcpm))

					// 399
					respListVideoBeginEventTrackMap := map[string]interface{}{}
					respListVideoBeginEventTrackMap["event_type"] = 100
					var respListVideoBeginEventTrackURLMap []string
					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, strings.Replace(ksConvURL, "__ACTION__", "399", -1))
					// respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, config.ExternalVideoStartTrackURL+"?"+mhTrackParams.Encode())

					respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)

					// 400
					respListVideoEndEventTrackMap := map[string]interface{}{}
					respListVideoEndEventTrackMap["event_type"] = 103
					var respListVideoEndEventTrackURLMap []string
					respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, strings.Replace(ksConvURL, "__ACTION__", "400", -1))
					// respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, config.ExternalVideoEndTrackURL+"?"+mhTrackParams.Encode())

					respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)

					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}

				respListItemMap["video"] = respListVideoItemMap

				// crt_type
				respListItemMap["crt_type"] = 20
			} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
				isVideo = false

				// imgs
				respListImageItemMap := map[string]interface{}{}
				if len(adInfoItem.AdMaterialInfo.MaterialFeature) == 0 {
					fmt.Println("wrong image url")
					respTmpInternalCode = 900105
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
					// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					// }
					continue
				}
				respListImageItemMap["url"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialURL
				respListImageItemMap["width"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width
				respListImageItemMap["height"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height

				if adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width > 0 && adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height > 0 {
					// 过滤素材方向, 大小
					isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width, adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height)
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
						// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
						// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
						// }
						continue
					}
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray

				// crt_type
				respListItemMap["crt_type"] = 11
			}

			// interact_type ad_url
			if adInfoItem.AdBaseInfo.AdOperationType == 2 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = adInfoItem.AdConversionInfo.H5Url
				respListItemMap["landpage_url"] = adInfoItem.AdConversionInfo.H5Url
				if strings.Contains(adInfoItem.AdConversionInfo.H5Url, ".apk") {
					respListItemMap["interact_type"] = 1
					respListItemMap["ad_url"] = adInfoItem.AdConversionInfo.H5Url
					respListItemMap["download_url"] = adInfoItem.AdConversionInfo.H5Url
				}
			} else if adInfoItem.AdBaseInfo.AdOperationType == 1 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = adInfoItem.AdConversionInfo.AppDownloadURL
				respListItemMap["download_url"] = adInfoItem.AdConversionInfo.AppDownloadURL

				if len(adInfoItem.AdConversionInfo.KwaiLandingPageUrl) > 0 {
					respListItemMap["landpage_url"] = adInfoItem.AdConversionInfo.KwaiLandingPageUrl
				}
			}
			if len(adInfoItem.AdBaseInfo.AppName) > 0 {
				respListItemMap["app_name"] = adInfoItem.AdBaseInfo.AppName
			}
			if len(adInfoItem.AdBaseInfo.CorporationName) > 0 {
				respListItemMap["publisher"] = adInfoItem.AdBaseInfo.CorporationName
			}
			if len(adInfoItem.AdBaseInfo.AppVersion) > 0 {
				respListItemMap["app_version"] = adInfoItem.AdBaseInfo.AppVersion
			}
			if len(adInfoItem.DownloadSafeInfo.AppPrivacyUrl) > 0 {
				respListItemMap["privacy_url"] = adInfoItem.DownloadSafeInfo.AppPrivacyUrl
			}
			if len(adInfoItem.DownloadSafeInfo.PermissionInfo) > 0 {
				respListItemMap["permission"] = adInfoItem.DownloadSafeInfo.PermissionInfo
			}
			if adInfoItem.AdBaseInfo.PackageSize > 0 {
				respListItemMap["package_size"] = adInfoItem.AdBaseInfo.PackageSize
			}
			if mhReq.Device.Os == "android" {
				appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, adInfoItem.AdBaseInfo.AppName, destPackageName)
				if appInfoFromRedisErr == nil {
					respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
					respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
				}
			}

			// ks返回appinfo_url, 传给媒体
			if len(adInfoItem.DownloadSafeInfo.IntroductionInfo) > 0 {
				respListItemMap["appinfo"] = adInfoItem.DownloadSafeInfo.IntroductionInfo
			}
			if len(adInfoItem.DownloadSafeInfo.IntroductionInfoUrl) > 0 {
				respListItemMap["appinfo_url"] = adInfoItem.DownloadSafeInfo.IntroductionInfoUrl
			}

			// impression_link array
			var respListItemImpArray []string
			// impression_link maplehaze
			mhImpParams := url.Values{}
			bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, ksEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
			mhImpParams.Add("log", bigdataParams)

			ksWinNoticeURL := getKSPriceWinURL(adInfoItem.AdBaseInfo.ConvURL, platformPos, ksEcpm, randPRValue)
			if len(ksWinNoticeURL) > 0 {
				var tmpWinNoticeURLs []string
				tmpWinNoticeURLs = append(tmpWinNoticeURLs, ksWinNoticeURL)
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}

			// respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
			// impression_link kuaishou
			ksImpURL := adInfoItem.AdBaseInfo.ShowURL
			if ksEcpm > 0 {
				// priceKey := "123456789abcdefghijklmnopqrstuvw"
				macroPrice := utils.ConvertIntToString(int(ksEcpm * randPRValue / 100))
				if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
					priceKey := platformPos.PlatformAppPriceEncrypt
					decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
					macroPrice = url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))
					ksImpURL = strings.Replace(ksImpURL, "__PR__", macroPrice, -1)
					ksImpURL = strings.Replace(ksImpURL, "__PRTYPE__", "encrypt", -1)
				} else {
					ksImpURL = strings.Replace(ksImpURL, "__PR__", macroPrice, -1)
				}
				// decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
				// escapeMacroPrice := url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))
				// ksImpURL = strings.Replace(ksImpURL, "__PR__", macroPrice, -1)
				// ksImpURL = strings.Replace(ksImpURL, "__PRTYPE__", escapeMacroPrice, -1)
			}
			respListItemImpArray = append(respListItemImpArray, ksImpURL)
			// impression_link kuaishou track url
			for _, adTrackInfoItem := range adInfoItem.AdTrackInfo {
				if adTrackInfoItem.Type == 1 {
					for _, adTrackInfoImpURLItem := range adTrackInfoItem.URL {
						impItem := adTrackInfoImpURLItem
						impItem = strings.Replace(impItem, "__MAC__", "", -1)
						impItem = strings.Replace(impItem, "__MAC2__", "", -1)
						impItem = strings.Replace(impItem, "__MAC3__", "", -1)
						if mhReq.Device.Os == "android" {
							impItem = strings.Replace(impItem, "__IMEI__", "", -1)
							impItem = strings.Replace(impItem, "__IMEI2__", ksImeiMd5, -1)
							impItem = strings.Replace(impItem, "__IMEI3__", "", -1)
							impItem = strings.Replace(impItem, "__ANDROIDID__", "", -1)
							impItem = strings.Replace(impItem, "__ANDROIDID2__", ksAndroidIDMd5, -1)
							impItem = strings.Replace(impItem, "__ANDROIDID3__", "", -1)
							impItem = strings.Replace(impItem, "__OAID__", ksOaid, -1)
						} else if mhReq.Device.Os == "ios" {
							impItem = strings.Replace(impItem, "__IDFA__", ksIdfa, -1)
							impItem = strings.Replace(impItem, "__IDFA2__", utils.GetMd5(ksIdfa), -1)
							impItem = strings.Replace(impItem, "__IDFA3__", "", -1)
						}
						impItem = strings.Replace(impItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
						respListItemImpArray = append(respListItemImpArray, impItem)
					}
				}
			}
			// impression_link maplehaze
			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

			respListItemMap["impression_link"] = respListItemImpArray

			// click_link array
			var respListItemClkArray []string
			// click_link maplehaze
			mhClkParams := url.Values{}
			mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
			mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			mhClkParams.Add("down_x", "__DOWN_X__")
			mhClkParams.Add("down_y", "__DOWN_Y__")
			mhClkParams.Add("up_x", "__UP_X__")
			mhClkParams.Add("up_y", "__UP_Y__")
			mhClkParams.Add("mh_down_x", "__DOWN_X__")
			mhClkParams.Add("mh_down_y", "__DOWN_Y__")
			mhClkParams.Add("mh_up_x", "__UP_X__")
			mhClkParams.Add("mh_up_y", "__UP_Y__")
			mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
			mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
			mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
			mhClkParams.Add("turnx", "__TURN_X__")
			mhClkParams.Add("turny", "__TURN_Y__")
			mhClkParams.Add("turnz", "__TURN_Z__")
			mhClkParams.Add("turntime", "__TURN_TIME__")

			if platformPos.PlatformPosIsReportSLD == 1 {
				mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
			} else {
				mhClkParams.Add("sld", "__SLD__")
			}
			mhClkParams.Add("log", bigdataParams)

			// click_link kuaishou
			ksClickURL := adInfoItem.AdBaseInfo.ClickURL
			ksClickURL = strings.Replace(ksClickURL, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			ksClickURL = strings.Replace(ksClickURL, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			ksClickURL = strings.Replace(ksClickURL, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			ksClickURL = strings.Replace(ksClickURL, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)

			macroPrice := utils.ConvertIntToString(int(ksEcpm * randPRValue / 100))
			if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
				priceKey := platformPos.PlatformAppPriceEncrypt
				decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
				macroPrice = url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))
				ksClickURL = strings.Replace(ksClickURL, "__PR__", macroPrice, -1)
				ksClickURL = strings.Replace(ksClickURL, "__PRTYPE__", "encrypt", -1)
			} else {
				ksClickURL = strings.Replace(ksClickURL, "__PR__", macroPrice, -1)
			}

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					ksClickURL = ksClickURL + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					ksClickURL = ksClickURL + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, ksClickURL)
			// click_link kuaishou track url
			for _, adTrackInfoItem := range adInfoItem.AdTrackInfo {
				if adTrackInfoItem.Type == 2 {
					for _, adTrackInfoImpURLItem := range adTrackInfoItem.URL {
						clkItem := adTrackInfoImpURLItem
						clkItem = strings.Replace(clkItem, "__MAC__", "", -1)
						clkItem = strings.Replace(clkItem, "__MAC2__", "", -1)
						clkItem = strings.Replace(clkItem, "__MAC3__", "", -1)
						if mhReq.Device.Os == "android" {
							clkItem = strings.Replace(clkItem, "__IMEI__", "", -1)
							clkItem = strings.Replace(clkItem, "__IMEI2__", ksImeiMd5, -1)
							clkItem = strings.Replace(clkItem, "__IMEI3__", "", -1)
							clkItem = strings.Replace(clkItem, "__ANDROIDID__", "", -1)
							clkItem = strings.Replace(clkItem, "__ANDROIDID2__", ksAndroidIDMd5, -1)
							clkItem = strings.Replace(clkItem, "__ANDROIDID3__", "", -1)
							clkItem = strings.Replace(clkItem, "__OAID__", ksOaid, -1)
						} else if mhReq.Device.Os == "ios" {
							clkItem = strings.Replace(clkItem, "__IDFA__", ksIdfa, -1)
							clkItem = strings.Replace(clkItem, "__IDFA2__", utils.GetMd5(ksIdfa), -1)
							clkItem = strings.Replace(clkItem, "__IDFA3__", "", -1)
						}
						clkItem = strings.Replace(clkItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)

						if IsAdIDReplaceXYByOS(c, mhReq) {
							if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
								clkItem = clkItem + "&is_adid_replace_xy=0"
							}
							if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
								clkItem = clkItem + "&replace_adid_sim_xy_default=1"
								respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
							}
						}

						respListItemClkArray = append(respListItemClkArray, clkItem)
					}
				}
			}
			// click_link maplehaze
			respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

			respListItemMap["click_link"] = respListItemClkArray

			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				// win notice url
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
				if len(ksLossNoticeURL) > 0 {
					var tmpLossNoticeURLs []string
					tmpLossNoticeURLs = append(tmpLossNoticeURLs, ksLossNoticeURL)
					tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
					tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
					mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
				}
				respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}

			// direction
			respListItemMap["material_direction"] = platformPos.PlatformPosDirection

			// log
			respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

			if tmpIsHasDeepLink {
				// deeplink track
				var respListItemDeepLinkArray []string

				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
				mhDPParams.Add("log", bigdataParams)

				respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

				var respListItemConvArray []map[string]interface{}

				respListItemConvMap := map[string]interface{}{}
				respListItemConvMap["conv_type"] = 10
				respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

				respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

				// deeplink failed track
				if platformPos.PlatformAppIsDeepLinkFailed == 1 {
					var respListItemDeepLinkFailedArray []string

					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

					respListItemConv11Map := map[string]interface{}{}
					respListItemConv11Map["conv_type"] = 11
					respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

					respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
				}

				respListItemMap["conv_tracks"] = respListItemConvArray
			}

			// 下发sdk曝光点击ua
			if localPos.LocalAppType == "1" {
				if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
					respListItemMap["ua"] = replaceUA
				}
			}

			// 如果上游配置图片 or 下游配置图片, 过滤视频
			// 如果上游配置视频 or 下游配置视频, 过滤图片
			// 如果上游配置图片+视频, 不过滤
			// 最后返回请求的个数
			if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
				if isVideo {
					curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
					// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					// }
					continue
				}
			}
			if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
				if isVideo {
				} else {
					curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
					// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
					// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					// }
					continue
				}
			}
			if len(respListArray) >= mhReq.Pos.AdCount {

				curlKSPriceFailedURL(ksLossNoticeURL, &bigdataExtra)
				// if platformPos.PlatformAppID == "516400015" || platformPos.PlatformAppID == "516400016" {
				// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_ks_loss", ksLossNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
				// }
				continue
			}

			// 上报竞价失败
			if len(ksLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, ksLossNoticeURL)
				respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
			}

			respListItemMap["p_ecpm"] = ksEcpm

			respListArray = append(respListArray, respListItemMap)
		}
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// kuaishou resp
	respKuaiShou := models.MHUpResp{}
	respKuaiShou.RespData = &mhResp
	respKuaiShou.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respKuaiShou
}

// get price win url
func getKSPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, ksEcpm int, randValue int) string {
	if ksEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	ksWinNoticeURL := strings.Replace(winNoticeURL, "__ACTION__", "600", -1)

	macroPrice := utils.ConvertIntToString(int(ksEcpm * randValue / 100))
	if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		priceKey := platformPos.PlatformAppPriceEncrypt
		decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(priceKey))
		macroPrice = url.QueryEscape(base64.StdEncoding.EncodeToString(decodePriceX))

		ksWinNoticeURL = strings.Replace(ksWinNoticeURL, "__PR__", macroPrice, -1)
		ksWinNoticeURL = strings.Replace(ksWinNoticeURL, "__PRTYPE__", "encrypt", -1)

	} else {
		ksWinNoticeURL = strings.Replace(ksWinNoticeURL, "__PR__", macroPrice, -1)
	}

	return ksWinNoticeURL
}

// get price failed url
func getKSPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, ksEcpm int, llsid int64) string {
	if ksEcpm <= 0 || len(lossNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportLoss == 0 {
		return ""
	}

	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	macroPrice := utils.ConvertIntToString(int(ksEcpm * tmpRandValue / 100))

	ksLossNoticeURL := strings.Replace(lossNoticeURL, "__ACTION__", "809", -1)
	if platformPos.PlatformAppIsReportLossPrice == 0 {
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__IFR__", "2", -1)
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_TYPE__", "2", -1)
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__AD_ECPM__", macroPrice, -1)
	} else if platformPos.PlatformAppIsReportLossPrice == 1 {
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_TYPE__", "2", -1)
		if platformPos.PlatformAppIsReportLossPricePlatformName == 1 {
			tmpTotalWeight := utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceCsjWeight) +
				utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceGdtWeight) +
				utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceBaiduWeight) +
				utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceOtherWeight)
			if tmpTotalWeight == 0 {
				ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "other", -1)
			} else {
				tmpRandWeight := rand.Intn(tmpTotalWeight)
				if tmpRandWeight < utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceCsjWeight) {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "chuanshanjia", -1)
				} else if tmpRandWeight < utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceCsjWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceGdtWeight) {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "guangdiantong", -1)
				} else if tmpRandWeight < utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceCsjWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceGdtWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceBaiduWeight) {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "baidu", -1)
				} else if tmpRandWeight < utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceCsjWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceGdtWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceBaiduWeight)+
					utils.ConvertStringToInt(platformPos.PlatformAppReportLossPriceOtherWeight) {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "other", -1)
				} else {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__ADN_NAME__", "other", -1)
				}
			}
		}
		cacheValue, cacheError := db.GlbBigCache.Get("go_loss_data")
		if cacheError == nil {
			var tmpLossDataArray []models.LossDataStu

			json.Unmarshal(cacheValue, &tmpLossDataArray)
			if len(tmpLossDataArray) > 0 {
				tmpLossData := tmpLossDataArray[rand.Intn(len(tmpLossDataArray))]

				if platformPos.PlatformAppIsReportLossPricePublisher == 1 {
					tmpPublisher := url.QueryEscape(tmpLossData.Publisher)
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__AD_N__", tmpPublisher, -1)
				}
				if platformPos.PlatformAppIsReportLossPriceTitle == 1 {
					tmpTitle := url.QueryEscape(tmpLossData.Title)
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__AD_TI__", tmpTitle, -1)
				}
			}
		}

		tmpIsSMarco := 0
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__AD_REQID__", utils.ConvertInt64ToString(llsid), -1)
		if platformPos.PlatformAppIsReportLossPriceExp == 1 {
			if utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceExpMinWeight) <= utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceExpMaxWeight) {
				tmp1 := int(utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceExpMinWeight) * 100)
				tmp2 := int(utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceExpMaxWeight) * 100)

				if rand.Intn(10000) < (tmp1 + rand.Intn(tmp2-tmp1+1)) {
					tmpIsSMarco = 1
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__IS_S__", "1", -1)
				} else {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__IS_S__", "0", -1)
				}
			}
		}
		if platformPos.PlatformAppIsReportLossPriceClk == 1 {
			if utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceClkMinWeight) <= utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceClkMaxWeight) {
				tmp1 := int(utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceClkMinWeight) * 100)
				tmp2 := int(utils.ConvertStringToFloat(platformPos.PlatformAppReportLossPriceClkMaxWeight) * 100)

				if tmpIsSMarco == 1 && rand.Intn(10000) < (tmp1+rand.Intn(tmp2-tmp1+1)) {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__IS_C__", "1", -1)
				} else {
					ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__IS_C__", "0", -1)
				}
			}
		}
		ksLossNoticeURL = strings.Replace(ksLossNoticeURL, "__AD_ECPM__", macroPrice, -1)
	}

	return ksLossNoticeURL
}

// curl price failed
func curlKSPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("ks loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

type KsEcpmSort []KsAdInfoStu

func (s KsEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s KsEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s KsEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].AdBaseInfo.Ecpm > s[j].AdBaseInfo.Ecpm
}

// KsRespStu ...
type KsRespStu struct {
	LLSID     int64          `json:"llsid"`
	Result    int            `json:"result"`
	ErrorMsg  string         `json:"errorMsg"`
	ImpAdInfo []KsRespImpStu `json:"impAdInfo"`
}

// KsRespImpStu ...
type KsRespImpStu struct {
	Type        int           `json:"type"`
	AdStyle     int           `json:"adStyle"`
	PosID       int64         `json:"posId"`
	ContentType int           `json:"contentType"`
	AdInfo      []KsAdInfoStu `json:"adInfo"`
}

// KsAdInfoStu ...
type KsAdInfoStu struct {
	AdTrackInfo      []KsAdTrackInfo    `json:"adTrackInfo"`
	AdConversionInfo KsAdConversionInfo `json:"adConversionInfo"`
	AdBaseInfo       KsAdBaseInfo       `json:"adBaseInfo"`
	AdMaterialInfo   KsAdMaterialInfo   `json:"adMaterialInfo"`
	// AdvertiserInfo   KsAdvertiserInfo   `json:"advertiserInfo"`
	// AdPreloadInfo    KsAdPreloadInfo    `json:"adPreloadInfo"`
	// AdSplashInfo     KsAdSplashInfo     `json:"adSplashInfo"`
	DownloadSafeInfo KsDownloadSafeInfo `json:"downloadSafeInfo"`
}

// KsAdTrackInfo ...
type KsAdTrackInfo struct {
	Type int      `json:"type"`
	URL  []string `json:"url"`
}

// KsAdConversionInfo ...
type KsAdConversionInfo struct {
	H5Url              string `json:"h5Url"`
	DeeplinkURL        string `json:"deeplinkUrl"`
	AppDownloadURL     string `json:"appDownloadUrl"`
	MarketURL          string `json:"marketUrl"`
	KwaiLandingPageUrl string `json:"kwaiLandingPageUrl"`
}

// KsAdBaseInfo ...
type KsAdBaseInfo struct {
	CreativeID      int64  `json:"creativeId"`
	AccountID       int64  `json:"accountId"`
	AdSourceType    int    `json:"adSourceType"`
	AdDescription   string `json:"adDescription"`
	AdOperationType int    `json:"adOperationType"`
	AdShowDuration  int    `json:"adShowDuration"`
	AppIconURL      string `json:"appIconUrl"`
	AppName         string `json:"appName"`
	AppPackageName  string `json:"appPackageName"`
	AppScore        int    `json:"appScore"`
	ShowURL         string `json:"showUrl"`
	ClickURL        string `json:"clickUrl"`
	ConvURL         string `json:"convUrl"`
	Ecpm            int    `json:"ecpm"`
	AppVersion      string `json:"appVersion"`
	CorporationName string `json:"corporationName"`
	PackageSize     int    `json:"packageSize"`
}

// KsAdMaterialInfo ...
type KsAdMaterialInfo struct {
	MaterialType    int                 `json:"materialType"`
	MaterialFeature []KsMaterialFeature `json:"materialFeature"`
}

// KsMaterialFeature ...
type KsMaterialFeature struct {
	MaterialURL   string         `json:"materialUrl"`
	CoverURL      string         `json:"coverUrl"`
	MaterialSize  KsMaterialSize `json:"materialSize"`
	FeatureType   int            `json:"featureType"`
	VideoDuration int            `json:"videoDuration"`
	VideoWidth    int            `json:"videoWidth"`
	VideoHeight   int            `json:"videoHeight"`
	// FirstFrame    string         `json:"firstFrame"`
}

// KsMaterialSize ...
type KsMaterialSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// KsDownloadSafeInfo ...
type KsDownloadSafeInfo struct {
	AppPrivacyUrl  string `json:"appPrivacyUrl"`
	PermissionInfo string `json:"permissionInfo"`
	// 应用功能介绍信息
	IntroductionInfo string `json:"introductionInfo"`
	// 应用功能介绍信息的 url
	IntroductionInfoUrl string `json:"introductionInfoUrl"`
}
