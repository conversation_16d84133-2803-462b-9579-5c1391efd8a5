package core

import (
	"encoding/json"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func MaterialReplace(localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, item *models.MHRespDataItem, replaceData *models.MHRespDataItem) {
	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}
	appKey := "go_ssp_material_replace_config_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_material_replace_config_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(posValue) == 0 {
		return
	}

	var materialReplace []models.NewMaterialReplaceBigCache
	if len(appValue) > 0 {
		_ = json.Unmarshal(appValue, &materialReplace)
	}

	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &materialReplace)
	}

	randNum := rand.Intn(100)
	for _, materialItem := range materialReplace {
		platformIds := materialItem.PlatformIds

		if len(platformIds) > 0 {
			if !strings.Contains(platformIds, tmpId) {
				continue
			}
		}

		if randNum > materialItem.Chance {
			continue
		}
		var (
			adUrlArray       []string
			replaceKeyArray  []string
			materialUrlArray []string
		)

		if len(materialItem.ReplaceKey) > 0 {
			replaceKeyArray = strings.Split(materialItem.ReplaceKey, ",")
		}

		if len(materialItem.AdUrl) > 0 {
			adUrlArray = strings.Split(materialItem.AdUrl, ",")
		}

		if len(materialItem.MaterialUrl) > 0 {
			materialUrlArray = strings.Split(materialItem.MaterialUrl, ",")
		}

		params := url.Values{}
		params.Set("date", time.Now().Format("20060102"))

		s := utils.Base64URLEncode([]byte(params.Encode()))

		// 配置定义
		var (
			titleArray []string
			descArray  []string
			//proportionallyImgArray []string
			//imgWidthArray          []string
			//imgHeightArray         []string
			//iconArray              []string
			//videoWidthArray        []string
			//videoHeightArray       []string
			//coverWidthArray        []string
			//coverHeightArray       []string
		)
		titleArray = materialItem.Title
		descArray = materialItem.Desc
		//proportionallyImgArray = materialItem.ProportionallyImg
		//imgWidthArray = append(materialItem.ImgW, proportionallyImgArray...)
		//imgHeightArray = append(materialItem.ImgH, proportionallyImgArray...)
		//iconArray = materialItem.Icon
		//videoWidthArray = materialItem.VideoW
		//videoHeightArray = materialItem.VideoH
		//coverWidthArray = materialItem.CoverW
		//coverHeightArray = materialItem.CoverH
		group := random(materialItem.Group, int64(len(materialItem.Group)))

		flag := 1
		titleFlag := "0" // 标题
		descFlag := "0"  // 描述
		imageFlag := "0" // 图片
		videoFlag := "0" // 视频链接
		iconFlag := "0"  // icon
		coverFlag := "0" // 视频封面
		FillVacanciesFlag := "0000"

		isReplace := titleFlag + descFlag + iconFlag + imageFlag + coverFlag + videoFlag + FillVacanciesFlag
		item.IsReplace = isReplace

		if len(replaceKeyArray) > 0 {
			for _, replaceKeyItem := range replaceKeyArray {
				if len(item.Title) > 0 {
					if strings.Contains(item.Title, replaceKeyItem) {
						flag = 2
						break
					}
				}
				if len(item.Description) > 0 {
					if strings.Contains(item.Description, replaceKeyItem) {
						flag = 2
						break
					}
				}
			}
		}

		if len(adUrlArray) > 0 && flag == 1 {
			for _, adUrlItem := range adUrlArray {
				if len(item.LandpageURL) > 0 {
					if strings.Contains(item.LandpageURL, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.DownloadURL) > 0 {
					if strings.Contains(item.DownloadURL, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.DeepLink) > 0 {
					if strings.Contains(item.DeepLink, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.AdURL) > 0 {
					if strings.Contains(item.AdURL, adUrlItem) {
						flag = 2
						break
					}
				}
			}
		}

		if len(materialUrlArray) > 0 && flag == 1 {
			for _, materialUrlItem := range materialUrlArray {
				if len(item.Image) > 0 {
					if strings.Contains(item.Image[0].URL, materialUrlItem) {
						flag = 2
						break
					}
				}
				if item.Video != nil && len(item.Video.VideoURL) > 0 {
					if strings.Contains(item.Video.VideoURL, materialUrlItem) {
						flag = 2
						break
					}
					if strings.Contains(item.Video.CoverURL, materialUrlItem) {
						flag = 2
						break
					}
				}

				if len(item.IconURL) > 0 {
					if strings.Contains(item.IconURL, materialUrlItem) {
						flag = 2
						break
					}
				}
			}
		}

		if materialItem.UnconditionalSwitch == 2 {
			flag = 2
		}

		if flag == 2 {
			if len(titleArray) > 0 {
				titleFlag = "1"
				item.Title = random(titleArray, 0)[0]
			}
			if len(descArray) > 0 {
				descFlag = "1"
				item.Description = random(descArray, 0)[0]
			}
			//if len(iconArray) > 0 {
			//item.IconURL = random(iconArray, 0)[0]
			//if materialItem.DynamicMaterials == 2 {
			//	if strings.Contains(item.IconURL, "?") {
			//		item.IconURL = item.IconURL + "&" + s
			//	} else {
			//		item.IconURL = item.IconURL + "?" + s
			//	}
			//}
			//}

			for _, groupItem := range group {
				switch groupItem.ReplaceType {
				case "3", "4":
					iconFlag = "1"
					item.IconURL = groupItem.ReplaceValue
					if groupItem.DynamicMaterials == 2 {
						if strings.Contains(item.IconURL, "?") {
							item.IconURL = item.IconURL + "&" + s
						} else {
							item.IconURL = item.IconURL + "?" + s
						}
					}
					break
				}
			}
			switch item.CrtType {
			case 11:
				if item.Image != nil {
					if materialItem.Type == 2 {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "5", "6":
								if len(groupItem.ReplaceValue) > 0 {
									var mhRespImageArray []models.MHRespImage
									var mhRespImage models.MHRespImage
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(groupItem.ReplaceValue, "?") {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "&" + s
										} else {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "?" + s
										}
									}
									mhRespImage.URL = groupItem.ReplaceValue
									mhRespImage.Width = groupItem.Width
									mhRespImage.Height = groupItem.Height
									mhRespImageArray = append(mhRespImageArray, mhRespImage)
									imageFlag = "1"
									item.Image = mhRespImageArray
									break
								}
							}
						}
					} else {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "5", "6":
								if len(groupItem.ReplaceValue) > 0 {
									var mhRespImageArray []models.MHRespImage

									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(groupItem.ReplaceValue, "?") {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "&" + s
										} else {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "?" + s
										}
									}
									for _, img := range item.Image {
										var mhRespImage models.MHRespImage
										mhRespImage.URL = img.URL
										if img.Width == img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width == groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
										if img.Width > img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
										if img.Width < img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
									}

									if len(mhRespImageArray) > 0 {
										imageFlag = "1"
										item.Image = mhRespImageArray
										break
									}
								}
							}
						}
						//if len(imgWidthArray) > 0 || len(imgHeightArray) > 0 || len(proportionallyImgArray) > 0 {
						//	var mhRespImageArray []models.MHRespImage
						//	for _, img := range item.Image {
						//		var mhRespImage models.MHRespImage
						//		mhRespImage.URL = img.URL
						//		if img.Width == img.Height && len(proportionallyImgArray) > 0 {
						//			mhRespImage.URL = random(proportionallyImgArray, 0)[0]
						//		}
						//		if img.Width > img.Height && len(imgWidthArray) > 0 {
						//			mhRespImage.URL = random(imgWidthArray, 0)[0]
						//		}
						//		if img.Height > img.Width && len(imgHeightArray) > 0 {
						//			mhRespImage.URL = random(imgHeightArray, 0)[0]
						//		}
						//		if materialItem.DynamicMaterials == 2 {
						//			if strings.Contains(mhRespImage.URL, "?") {
						//				mhRespImage.URL = mhRespImage.URL + "&" + s
						//			} else {
						//				mhRespImage.URL = mhRespImage.URL + "?" + s
						//			}
						//		}
						//		mhRespImage.Width = img.Width
						//		mhRespImage.Height = img.Height
						//		mhRespImageArray = append(mhRespImageArray, mhRespImage)
						//	}
						//	imageFlag = "1"
						//	item.Image = mhRespImageArray
						//}
					}
				}
			case 20:
				if item.Video != nil {
					if materialItem.Type == 2 {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "7", "8":
								if len(groupItem.ReplaceValue) > 0 {
									item.Video.VideoURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									videoFlag = "1"
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.VideoURL, "?") {
											item.Video.VideoURL = item.Video.VideoURL + "&" + s
										} else {
											item.Video.VideoURL = item.Video.VideoURL + "?" + s
										}
									}
									break
								}
							}
						}

						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "9", "10":
								if len(groupItem.ReplaceValue) > 0 {
									item.Video.CoverURL = groupItem.ReplaceValue
									videoFlag = "1"
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
							}
						}

						//var coverNewArray []string
						//coverNewArray = append(coverNewArray, coverWidthArray...)
						//coverNewArray = append(coverNewArray, coverHeightArray...)
						//if len(coverNewArray) > 0 {
						//	item.Video.CoverURL = random(coverNewArray, 0)[0]
						//	if materialItem.DynamicMaterials == 2 {
						//		if strings.Contains(item.Video.CoverURL, "?") {
						//			item.Video.CoverURL = item.Video.CoverURL + "&" + s
						//		} else {
						//			item.Video.CoverURL = item.Video.CoverURL + "?" + s
						//		}
						//	}
						//	coverFlag = "1"
						//}
					} else {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "7", "8":
								if item.Video.Width > item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
									videoFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
								if item.Video.Width < item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
									videoFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
							case "9", "10":
								if item.Video.Width > item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
									coverFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
								if item.Video.Width < item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
									coverFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
							}
						}

						//if len(videoWidthArray) > 0 || len(videoHeightArray) > 0 {
						//	if item.Video.Width > item.Video.Height && len(videoWidthArray) > 0 {
						//		item.Video.VideoURL = random(videoWidthArray, 0)[0]
						//	}
						//	if item.Video.Height > item.Video.Width && len(videoHeightArray) > 0 {
						//		item.Video.VideoURL = random(videoHeightArray, 0)[0]
						//	}
						//	videoFlag = "1"
						//}
						//if len(coverWidthArray) > 0 || len(coverHeightArray) > 0 {
						//	if item.Video.Width > item.Video.Height && len(coverWidthArray) > 0 {
						//		item.Video.CoverURL = random(coverWidthArray, 0)[0]
						//	}
						//	if item.Video.Height > item.Video.Width && len(coverHeightArray) > 0 {
						//		item.Video.CoverURL = random(coverHeightArray, 0)[0]
						//	}
						//	coverFlag = "1"
						//}

						//if materialItem.DynamicMaterials == 2 {
						//	if strings.Contains(item.Video.VideoURL, "?") {
						//		item.Video.VideoURL = item.Video.VideoURL + "&" + s
						//	} else {
						//		item.Video.VideoURL = item.Video.VideoURL + "?" + s
						//	}
						//}
					}
				}
			}

			isReplace = titleFlag + descFlag + iconFlag + imageFlag + coverFlag + videoFlag + FillVacanciesFlag
			item.IsReplace = isReplace

			replaceData.IsReplace = item.IsReplace
			replaceData.ReplaceTitle = item.Title
			replaceData.ReplaceDescription = item.Description
			replaceData.ReplaceIconURL = item.IconURL
			replaceData.ReplaceImage = item.Image
			replaceData.ReplaceVideo = item.Video
			break
		}
	}
}

func random[T any](input []T, i int64) []T {
	rand.New(rand.NewSource(time.Now().Unix() + i))
	for i := len(input) - 1; i >= 0; i-- {
		index := rand.Intn(i + 1)
		input[index], input[i] = input[i], input[index]
	}
	return input
}
