package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/uunion_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromUUnion ...
func GetFromUUnion(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	fmt.Println("get from uunion")

	// platformPos.PlatformAppID = "62b949c105844627b5c98494"
	// platformPos.PlatformPosID = "100001781"
	// platformPos.PlatformAppBundle = "com.test"
	// platformPos.PlatformAppUpURL = "http://dsp.ads.umeng.com/uunion/api"
	// platformPos.PlatformAppUpURL = "http://test-ssp.ads.umeng.com/uunion/api"
	// platformPos.PlatformAppVersion = "1.0"
	// categoryInfo.FloorPrice = 15
	fmt.Println("get from uunion, local app id: ", localPos.LocalAppID)
	fmt.Println("get from uunion, local pos id: ", localPos.LocalPosID)
	// fmt.Println("get from uunion, local app type: ", localPos.LocalAppType)
	fmt.Println("get from uunion, platform app id: ", platformPos.PlatformAppID)
	fmt.Println("get from uunion, platform pos id: ", platformPos.PlatformPosID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	////////////////////////////////////////////////////////////////////////////////////////

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	// device
	uunionReqDevice := &uunion_up.BidRequest_Device{}

	uunionReqDevice.W = int32(mhReq.Device.ScreenWidth)
	uunionReqDevice.H = int32(mhReq.Device.ScreenHeight)

	// device ip
	uunionReqDevice.Ip = mhReq.Device.IP

	// device aliaaid
	// uunionReqDevice.AliAaid = ?
	// uunionReqDevice.AliAaid = "1DE07783-64EB-AA71-95EF-261061931F2F"

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		uunionReqDevice.Ua = destConfigUA
	}

	// device carrier
	if mhReq.Network.Carrier == 1 {
		uunionReqDevice.Carrier = uunion_up.BidRequest_Device_CHINA_MOBILE
	} else if mhReq.Network.Carrier == 2 {
		uunionReqDevice.Carrier = uunion_up.BidRequest_Device_CHINA_UNICOM
	} else if mhReq.Network.Carrier == 3 {
		uunionReqDevice.Carrier = uunion_up.BidRequest_Device_CHINA_TELECOM
	} else {
		uunionReqDevice.Carrier = uunion_up.BidRequest_Device_CARRIER_UNKNOWN
	}

	// device network
	if mhReq.Network.ConnectType == 0 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_CON_UNKNOWN
	} else if mhReq.Network.ConnectType == 1 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_WIFI
	} else if mhReq.Network.ConnectType == 2 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_CELL_2G
	} else if mhReq.Network.ConnectType == 3 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_CELL_3G
	} else if mhReq.Network.ConnectType == 4 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_CELL_4G
	} else if mhReq.Network.ConnectType == 7 {
		uunionReqDevice.ConnectionType = uunion_up.BidRequest_Device_CELL_5G
	}

	// device type
	uunionReqDevice.DeviceType = uunion_up.BidRequest_Device_PHONE

	// device os
	if mhReq.Device.Os == "android" {
		uunionReqDevice.Os = uunion_up.BidRequest_Device_ANDROID
	} else if mhReq.Device.Os == "ios" {
		uunionReqDevice.Os = uunion_up.BidRequest_Device_IOS
	}

	// device osv
	uunionReqDevice.Osv = mhReq.Device.OsVersion

	// device model
	uunionReqDevice.Model = mhReq.Device.Model

	// device make
	uunionReqDevice.Brand = mhReq.Device.Manufacturer

	// device boot_mark, update_mark
	uunionReqDevice.BootMark = mhReq.Device.BootMark
	uunionReqDevice.UpdateMark = mhReq.Device.UpdateMark

	// device uid
	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	// 替换宏imei, idfa, oaid
	// uunionReqImei := ""
	uunionReqIdfa := ""
	uunionReqOaid := ""

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				uunionReqDevice.ImeiMd5 = utils.GetMd5(mhReq.Device.Imei)

			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				uunionReqDevice.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				uunionReqDevice.Oaid = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from uunion error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				uunionReqDevice.Idfa = mhReq.Device.Idfa

				uunionReqIdfa = mhReq.Device.Idfa
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

							uunionReqOaid = item.CAID
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

						uunionReqOaid = item.CAID
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					uunionReqDevice.Idfa = mhReq.Device.Idfa

					uunionReqIdfa = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

								uunionReqOaid = item.CAID
								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

							uunionReqOaid = item.CAID
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					uunionReqDevice.Idfa = mhReq.Device.Idfa

					uunionReqIdfa = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

								uunionReqOaid = item.CAID
								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

							uunionReqOaid = item.CAID
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from uunion error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug uunion android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					uunionReqDevice.Osv = didRedisData.OsVersion

					// device model
					uunionReqDevice.Model = didRedisData.Model

					// device make
					uunionReqDevice.Brand = didRedisData.Manufacturer

					// device 清除
					uunionReqDevice.ImeiMd5 = ""
					uunionReqDevice.Oaid = ""

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							uunionReqDevice.ImeiMd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							uunionReqDevice.ImeiMd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							uunionReqDevice.Oaid = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						uunionReqDevice.Ua = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug uunion ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						uunionReqDevice.Osv = didRedisData.OsVersion

						// device model
						uunionReqDevice.Model = didRedisData.Model

						// device make
						uunionReqDevice.Brand = didRedisData.Manufacturer

						// device 清除
						uunionReqDevice.Idfa = ""
						uunionReqDevice.Caid = ""
						uunionReqIdfa = ""
						uunionReqOaid = ""

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {

								uunionReqDevice.Idfa = didRedisData.Idfa

								uunionReqIdfa = didRedisData.Idfa

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

										uunionReqOaid = item.CAID
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

									uunionReqOaid = item.CAID
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {

									uunionReqDevice.Idfa = didRedisData.Idfa

									uunionReqIdfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

											uunionReqOaid = item.CAID
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

										uunionReqOaid = item.CAID
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {

									uunionReqDevice.Idfa = didRedisData.Idfa

									uunionReqIdfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

											uunionReqOaid = item.CAID
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										uunionReqDevice.Caid = item.CAIDVersion + "_" + item.CAID

										uunionReqOaid = item.CAID
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							uunionReqDevice.Ua = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from uunion error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from uunion error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	////////////////////////////////////////////////////////////////////////////////////////
	// req imp
	uunionReqImp := &uunion_up.BidRequest_Impression{
		Id:       0,
		SlotId:   platformPos.PlatformPosID,
		BidFloor: int32(localPosFloorPrice),
	}
	// 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	if platformPos.PlatformPosType == 1 {
		uunionReqImp.SlotType = uunion_up.BidRequest_Impression_BANNER
	} else if platformPos.PlatformPosType == 2 {
		uunionReqImp.SlotType = uunion_up.BidRequest_Impression_SPLASH
	} else if platformPos.PlatformPosType == 3 {
		uunionReqImp.SlotType = uunion_up.BidRequest_Impression_INTERSTITIAL
	} else if platformPos.PlatformPosType == 4 {
		uunionReqImp.SlotType = uunion_up.BidRequest_Impression_NATIVE
	} else {
		uunionReqImp.SlotType = uunion_up.BidRequest_Impression_SLOT_TYPE_UNKNOWN
	}

	// req app
	uunionReqApp := &uunion_up.BidRequest_App{
		Appkey:  platformPos.PlatformAppID,
		Name:    platformPos.PlatformAppName,
		Version: platformPos.PlatformAppVersion,
	}
	uunionReqApp.PackageName = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	uunionReqPb := &uunion_up.BidRequest{
		Version:   1,
		RequestId: bigdataUID,
		App:       uunionReqApp,
		Device:    uunionReqDevice,
	}
	uunionReqPb.Imp = append(uunionReqPb.Imp, uunionReqImp)

	uunionReqPbByte, xxxxxx := proto.Marshal(uunionReqPb)
	if xxxxxx != nil {
		panic(xxxxxx)
	}

	tmpReqByte, _ := json.Marshal(uunionReqPb)
	fmt.Println("========================================================================================================================")
	fmt.Println("uunion url:", platformPos.PlatformAppUpURL)
	fmt.Println("app id:", platformPos.PlatformAppID)
	fmt.Println("pos id:", platformPos.PlatformPosID)
	fmt.Println("========================================================================================================================")
	fmt.Println("uunion req:", string(tmpReqByte))
	fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(uunionReqPbByte))

	requestGet.Header.Add("Content-Type", "application/octet-stream; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println("uunion resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	fmt.Println("========================================================================================================================")
	uunionResp := &uunion_up.BidResponse{}
	err = proto.Unmarshal(bodyContent, uunionResp)
	if err != nil {
		fmt.Println(err)
	}

	tmpRespByte, _ := json.Marshal(uunionResp)
	fmt.Println("uunion resp code:", resp.StatusCode)
	fmt.Println("uunion resp:", string(tmpRespByte))
	fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if uunionResp.GetStatus() != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(uunionResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(uunionResp.GetSeats()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(uunionResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(uunionResp.GetSeats()[0].GetAds()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(uunionResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := uunionResp.GetSeats()[0].GetAds()

	// if len(gdtRespDataListItemStu) > 1 {
	// 	sort.Sort(uunionEcpmSort(gdtRespDataListItemStu))
	// }

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		uunionEcpm := int(item.GetBidPrice())

		respTmpPrice = respTmpPrice + uunionEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if uunionEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			uunionEcpm = platformPos.PlatformPosEcpm
		}

		// fmt.Println("uunion_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(uunionEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > uunionEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		respListItemMap := map[string]interface{}{}

		// 随机95-98%替换价格宏
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}

		// 获取上游竞价成功上报链接
		tmpWinNoticeURLs := getUUnionPriceWinURL(item.TrackUrls.GetWinUrls(), platformPos, uunionEcpm, mhReq, uunionReqIdfa, uunionReqOaid, randPRValue)

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(uunionEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := item.GetTitle()
		respDescription := item.GetDesc()
		respIconURL := item.GetIcon().GetUrl()
		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		if len(item.GetImage()) > 0 {
			respImageURL = string(item.GetImage()[0].GetUrl())
			respImageWidth = int(item.GetImage()[0].GetW())
			respImageHeight = int(item.GetImage()[0].GetH())
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		if mhReq.Device.Os == "android" {
			if len(item.AppInfo.GetDownloadUrl()) > 0 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.AppInfo.GetDownloadUrl()
				respListItemMap["download_url"] = item.AppInfo.GetDownloadUrl()
				if len(item.GetLandingPageUrl()) > 0 {
					respListItemMap["landpage_url"] = item.GetLandingPageUrl()
				}
			} else if len(item.GetLandingPageUrl()) > 0 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.GetLandingPageUrl()
				respListItemMap["landpage_url"] = item.GetLandingPageUrl()
			} else {

				continue
			}

			if len(item.AppInfo.GetAppName()) > 0 {
				respListItemMap["app_name"] = item.AppInfo.GetAppName()
			} else {
			}
			if len(item.AppInfo.GetDeveloper()) > 0 {
				respListItemMap["publisher"] = item.AppInfo.GetDeveloper()
			}
			if len(item.AppInfo.GetAppVersion()) > 0 {
				respListItemMap["app_version"] = string(item.AppInfo.GetAppVersion())
			}
			if len(item.AppInfo.GetAppPrivacy()) > 0 {
				respListItemMap["privacy_url"] = item.AppInfo.GetAppPrivacy()
			}
			if len(item.AppInfo.GetAppPermission()) > 0 {
				respListItemMap["permission_url"] = item.AppInfo.GetAppPermission()
			}
		} else if mhReq.Device.Os == "ios" {
			if len(item.GetLandingPageUrl()) > 0 {
				respListItemMap["ad_url"] = item.GetLandingPageUrl()
				respListItemMap["interact_type"] = 0
				respListItemMap["landpage_url"] = item.GetLandingPageUrl()
			} else if len(item.AppInfo.GetDownloadUrl()) > 0 {
				respListItemMap["ad_url"] = item.AppInfo.GetDownloadUrl()
				respListItemMap["interact_type"] = 1
				respListItemMap["download_url"] = item.AppInfo.GetDownloadUrl()
			} else {
				continue
			}
		}

		if len(string(item.GetVideo().GetUrl())) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.GetVideo().GetDuration() > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(item.GetVideo().GetDuration()))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					continue
				}

				respListVideoItemMap["duration"] = int(item.GetVideo().GetDuration()) * 1000
			}
			respListVideoItemMap["width"] = int(item.GetVideo().GetW())
			respListVideoItemMap["height"] = int(item.GetVideo().GetH())
			respListVideoItemMap["video_url"] = string(item.GetVideo().GetUrl())

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(item.GetVideo().GetW()), int(item.GetVideo().GetH()))
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// cover_url
			if len(respImageURL) > 0 {
				respListVideoItemMap["cover_url"] = respImageURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			isVideoTrack := 0
			// event track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range item.TrackUrls.GetVideoPlay0() {
				respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorItem)
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				isVideoTrack = 1
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range item.TrackUrls.GetVideoPlay4() {
				respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, monitorItem)
			}
			if len(respListVideoEndEventTrackURLMap) > 0 {
				isVideoTrack = 1

				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if isVideoTrack == 1 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL
				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		// respListItemMap["crid"] = GetCRIDByMd5(&respListItemMap)

		// deeplink
		destDeepLink := ""
		if len(string(item.GetDeeplink())) > 0 {
			destDeepLink = string(item.GetDeeplink())

			respListItemMap["deep_link"] = destDeepLink

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, monitorItem := range item.TrackUrls.GetDeeplinkSuccess() {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, monitorItem)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, monitorItem := range item.TrackUrls.GetDeeplinkFail() {
					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, monitorItem)
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(item.AppInfo.GetPackageName()) > 0 {

			respListItemMap["package_name"] = string(item.AppInfo.GetPackageName())

			destPackageName = item.AppInfo.GetPackageName()
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, item.AppInfo.GetAppName(), destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, uunionEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		if len(tmpWinNoticeURLs) > 0 {
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link
		for _, showItem := range item.TrackUrls.GetExposeUrls() {
			tmpItem := showItem
			tmpItem = strings.Replace(tmpItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
			tmpItem = strings.Replace(tmpItem, "__TS_MS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
			tmpItem = strings.Replace(tmpItem, "__IP__", mhReq.Device.IP, -1)
			if mhReq.Device.Os == "android" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "2", -1)
				// tmpItem = strings.Replace(tmpItem, "__IMEI__", uunionReqImei, -1)
			} else if mhReq.Device.Os == "ios" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
				tmpItem = strings.Replace(tmpItem, "__IDFA__", uunionReqIdfa, -1)
				tmpItem = strings.Replace(tmpItem, "__OAID__", uunionReqOaid, -1)
			}
			respListItemImpArray = append(respListItemImpArray, string(tmpItem))
		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, clickItem := range item.TrackUrls.GetClickUrls() {
			tmpItem := clickItem
			tmpItem = strings.Replace(tmpItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
			tmpItem = strings.Replace(tmpItem, "__TS_MS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
			tmpItem = strings.Replace(tmpItem, "__IP__", mhReq.Device.IP, -1)
			if mhReq.Device.Os == "android" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "2", -1)
				// tmpItem = strings.Replace(tmpItem, "__IMEI__", uunionReqImei, -1)
			} else if mhReq.Device.Os == "ios" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
				tmpItem = strings.Replace(tmpItem, "__IDFA__", uunionReqIdfa, -1)
				tmpItem = strings.Replace(tmpItem, "__OAID__", uunionReqOaid, -1)
			}

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					tmpItem = tmpItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, string(tmpItem))
		}

		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = uunionEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("uunion resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// uunion resp
	respUUnion := models.MHUpResp{}
	respUUnion.RespData = &mhResp
	respUUnion.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respUUnion
}

// get price win url
func getUUnionPriceWinURL(winNoticeURLArray []string, platformPos *models.PlatformPosStu, uunionEcpm int,
	mhReq *models.MHReq, uunionReqIdfa string, uunionReqOaid string, randPRValue int) []string {
	if uunionEcpm <= 0 || len(winNoticeURLArray) == 0 {
		return []string{}
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return []string{}
	}

	macroPrice := utils.ConvertIntToString(int(uunionEcpm * randPRValue / 100))

	var tmpWinNoticeURLs []string
	for _, winItem := range winNoticeURLArray {
		tmpWinItem := winItem
		tmpWinItem = strings.Replace(tmpWinItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
		tmpWinItem = strings.Replace(tmpWinItem, "__TS_MS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
		tmpWinItem = strings.Replace(tmpWinItem, "__IP__", mhReq.Device.IP, -1)
		if mhReq.Device.Os == "android" {
			tmpWinItem = strings.Replace(tmpWinItem, "__OS__", "2", -1)
			// tmpWinItem = strings.Replace(tmpWinItem, "__IMEI__", uunionReqImei, -1)
		} else if mhReq.Device.Os == "ios" {
			tmpWinItem = strings.Replace(tmpWinItem, "__OS__", "1", -1)
			tmpWinItem = strings.Replace(tmpWinItem, "__IDFA__", uunionReqIdfa, -1)
			tmpWinItem = strings.Replace(tmpWinItem, "__OAID__", uunionReqOaid, -1)
		}
		tmpWinItem = strings.Replace(tmpWinItem, "__WPRICE__", macroPrice, -1)

		tmpWinNoticeURLs = append(tmpWinNoticeURLs, tmpWinItem)
	}

	return tmpWinNoticeURLs
}
