package core

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromXiaoMi ...
func GetFromXiaoMi(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	fmt.Println("get from xiaomi")

	// test image
	// platformPos.PlatformPosID = "3dc4ab45b27222da105e60ec899e6f2a"
	// test video
	// platformPos.PlatformPosID = "77295ab0558fa54fc5cc9ed6a28b6da7"

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// client := &http.Client{}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["packageName"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	reqUserInfoMap := map[string]interface{}{}
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				reqUserInfoMap["imei"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				reqUserInfoMap["imei"] = strings.ToLower(mhReq.Device.ImeiMd5)
			} else {
				fmt.Println("get from xiaomi null imei")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqUserInfoMap["oaid"] = mhReq.Device.Oaid
			} else {
				fmt.Println("get from xiaomi null oaid")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		fmt.Println("xiaomi wrong manufacturer")
		bigdataExtra.InternalCode = 900110
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if mhReq.Network.ConnectType == 0 {
		reqUserInfoMap["connectionType"] = "unknown"
	} else if mhReq.Network.ConnectType == 1 {
		reqUserInfoMap["connectionType"] = "wifi"
	} else if mhReq.Network.ConnectType == 2 {
		reqUserInfoMap["connectionType"] = "2g"
	} else if mhReq.Network.ConnectType == 3 {
		reqUserInfoMap["connectionType"] = "3g"
	} else if mhReq.Network.ConnectType == 4 {
		reqUserInfoMap["connectionType"] = "4g"
	} else if mhReq.Network.ConnectType == 7 {
		reqUserInfoMap["connectionType"] = "5g"
	}

	reqUserInfoMap["ip"] = mhReq.Device.IP

	// if len(mhReq.Device.Mac) > 0 {
	// 	reqUserInfoMap["mac"] = mhReq.Device.Mac
	// }
	if len(destConfigUA) > 0 {
		reqUserInfoMap["ua"] = destConfigUA
	}

	// impRequests
	reqImpRequestArray := []map[string]interface{}{}
	reqImpRequestItemMap := map[string]interface{}{}
	reqImpRequestItemMap["adsCount"] = 1
	reqImpRequestItemMap["upId"] = platformPos.PlatformPosID
	reqImpRequestItemMap["bidfloor"] = localPosFloorPrice

	reqImpRequestArray = append(reqImpRequestArray, reqImpRequestItemMap)

	reqDeviceInfoMap := map[string]interface{}{}
	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		// reqDeviceInfoMap["tokenId"] = ""
		reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = "android"
		reqDeviceInfoMap["model"] = mhReq.Device.Model
		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["androidVersion"] = mhReq.Device.OsVersion
		}
	} else if mhReq.Device.Os == "ios" {
		fmt.Println("get from xiaomi ios")
		bigdataExtra.InternalCode = 900110
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqUserInfoMap, "imei")
					delete(reqUserInfoMap, "oaid")

					reqDeviceInfoMap["androidVersion"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer

					// isOKReplaceManufacturer := false
					// for _, manufactureConfigItem := range manufactureConfigArray {
					// 	if strings.ToLower(didRedisData.Manufacturer) == manufactureConfigItem {
					// 		isOKReplaceManufacturer = true
					// 	}
					// }
					// if isOKReplaceManufacturer {
					// } else {
					// 	bigdataExtra.InternalCode = 900011
					// 	bigdataExtra.ExternalCode = 102006

					// 	return MhUpErrorRespMap("", bigdataExtra)
					// }

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqUserInfoMap["ua"] = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}
					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqUserInfoMap["imei"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqUserInfoMap["imei"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqUserInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"deviceInfo":  reqDeviceInfoMap,
		"userInfo":    reqUserInfoMap,
		"appInfo":     reqAppInfoMap,
		"impRequests": reqImpRequestArray,
	}

	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	fmt.Println("xiaomi req: " + string(jsonData))

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", config.UpXiaoMiURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)

	fmt.Println("xiaomi req url: " + requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("xiaomi resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	xiaomiRespStu := XiaoMiRespStu{}
	json.Unmarshal([]byte(bodyContent), &xiaomiRespStu)

	if xiaomiRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006

		if xiaomiRespStu.Code == 1 {
			bigdataExtra.UpRespCode = 102006
		} else {
			bigdataExtra.UpRespCode = xiaomiRespStu.Code
		}

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(xiaomiRespStu.AdInfos) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006

		// xiaomi返回码, 跟gdt统一, 无填充
		bigdataExtra.UpRespCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// xiaomi返回码, 跟gdt统一, 有填充
	bigdataExtra.UpRespCode = 0

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, xiaomiAdInfoItem := range xiaomiRespStu.AdInfos {
		adInfoItem := xiaomiAdInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		xiaomiEcpm := adInfoItem.Ecpm

		respTmpPrice = respTmpPrice + xiaomiEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if xiaomiEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			xiaomiEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > xiaomiEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(xiaomiEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// 获取encodePriceValue, 竞胜url数组
		encodePriceValue, xiaomiWinNoticeNurls := getXiaoMiPriceOKURL(adInfoItem.Nurls, platformPos, xiaomiEcpm)

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		} else if len(adInfoItem.Brand) > 0 {
			respListItemMap["title"] = adInfoItem.Brand
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// crid
		respListItemMap["crid"] = utils.ConvertIntToString(adInfoItem.ID)

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		isDeepLinkOK := false
		if len(adInfoItem.Deeplink) > 0 {
			isDeepLinkOK = true
			respListItemMap["deep_link"] = strings.Replace(adInfoItem.Deeplink, "mimarket://launchordetail?", "mimarket://details?", -1)
		} else if len(adInfoItem.LandingPageURL) > 0 {
			if strings.Contains(adInfoItem.LandingPageURL, "market://") || strings.Contains(adInfoItem.LandingPageURL, "mimarket://") {
				isDeepLinkOK = true
				respListItemMap["deep_link"] = strings.Replace(adInfoItem.LandingPageURL, "mimarket://launchordetail?", "mimarket://details?", -1)
			}
		}
		if isDeepLinkOK == true {
			// 字段 appstore_package_name
			if strings.HasPrefix(adInfoItem.Deeplink, "mimarket://") {
				respListItemMap["appstore_package_name"] = "com.xiaomi.market"
			}

			// deeplink ok track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Deeplink)
		}

		isVideo := false
		for _, assetItem := range adInfoItem.Assets {
			if assetItem.MaterialType == 3 {
				isVideo = true
			}
		}

		// 视频
		if isVideo {
			respListVideoItemMap := map[string]interface{}{}
			// if adInfoContentItem.MetaData.VideoInfo.VideoDuration > 0 {
			// 	respListVideoItemMap["duration"] = adInfoContentItem.MetaData.VideoInfo.VideoDuration
			// }
			// respListVideoItemMap["width"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialWidth
			// respListVideoItemMap["height"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialHeight
			// respListVideoItemMap["video_url"] = adInfoContentItem.MetaData.VideoInfo.VideoURL

			// if len(adInfoContentItem.MetaData.ImageInfo) > 0 {
			// 	respListVideoItemMap["cover_url"] = adInfoContentItem.MetaData.ImageInfo[0].URL
			// }

			// cover_url
			for _, assetItem := range adInfoItem.Assets {
				if assetItem.MaterialType == 1 {
					respListVideoItemMap["cover_url"] = assetItem.URL
				} else if assetItem.MaterialType == 3 {
					respListVideoItemMap["video_url"] = assetItem.URL
					respListVideoItemMap["width"] = assetItem.Width
					respListVideoItemMap["height"] = assetItem.Height
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			if platformPos.PlatformPosType == 11 {
				var respListEventTrackURLMap []map[string]interface{}

				respListVideoBeginEventTrackMap := map[string]interface{}{}
				respListVideoBeginEventTrackMap["event_type"] = 100
				var respListVideoBeginEventTrackURLMap []string
				for _, monitorItem := range adInfoItem.VideoStartMonitorURLs {
					tmpItem := monitorItem
					tmpItem = strings.Replace(tmpItem, "{WIN_PRICE}", encodePriceValue, -1)

					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, tmpItem)
				}
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				if len(respListVideoBeginEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
				}

				respListVideoEndEventTrackMap := map[string]interface{}{}
				respListVideoEndEventTrackMap["event_type"] = 103
				var respListVideoEndEventTrackURLMap []string
				for _, monitorItem := range adInfoItem.VideoFinishMonitorURLs {
					tmpItem := monitorItem
					tmpItem = strings.Replace(tmpItem, "{WIN_PRICE}", encodePriceValue, -1)
					respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, tmpItem)
				}
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				if len(respListVideoEndEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
				}

				if len(respListEventTrackURLMap) > 0 {
					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// imgs
			isHasImage := false
			respListImageItemMap := map[string]interface{}{}
			for _, assetItem := range adInfoItem.Assets {
				if assetItem.MaterialType == 1 {
					isHasImage = true
					respListImageItemMap["url"] = assetItem.URL
					respListImageItemMap["width"] = assetItem.Width
					respListImageItemMap["height"] = assetItem.Height
				}
			}
			if isHasImage {
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoItem.TargetType == 1 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = strings.Replace(adInfoItem.LandingPageURL, "{WIN_PRICE}", encodePriceValue, -1)
			respListItemMap["landpage_url"] = strings.Replace(adInfoItem.LandingPageURL, "{WIN_PRICE}", encodePriceValue, -1)
		} else if adInfoItem.TargetType == 2 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.DownloadURL
			respListItemMap["landpage_url"] = adInfoItem.DownloadURL
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoItem.PackageName

			// android
			if mhReq.Device.Os == "android" {
				appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, adInfoItem.AppName, adInfoItem.PackageName)
				if appInfoFromRedisErr == nil {
					respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
					respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
				}
			}
		}
		if len(adInfoItem.AppIntroductionURL) > 0 {
			respListItemMap["appinfo_url"] = adInfoItem.AppIntroductionURL
		}
		if len(adInfoItem.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppName
		}
		if len(adInfoItem.AppDeveloper) > 0 {
			respListItemMap["publisher"] = adInfoItem.AppDeveloper
		}
		if len(adInfoItem.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.AppVersion
		}
		if len(adInfoItem.AppPrivacyURL) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.AppPrivacyURL
		}
		if len(adInfoItem.AppPermissionURL) > 0 {
			respListItemMap["permission_url"] = adInfoItem.AppPermissionURL
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, xiaomiEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		if len(xiaomiWinNoticeNurls) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, xiaomiWinNoticeNurls...)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// impression_link track url
		for _, monitorItem := range adInfoItem.ImpressionMonitorURLs {
			impItem := monitorItem
			impItem = strings.Replace(impItem, "{WIN_PRICE}", encodePriceValue, -1)
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, monitorItem := range adInfoItem.ClickMonitorURLs {
			clkItem := monitorItem
			clkItem = strings.Replace(clkItem, "{REQWIDTH}", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			clkItem = strings.Replace(clkItem, "{REQHEIGHT}", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			clkItem = strings.Replace(clkItem, "{WIDTH}", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			clkItem = strings.Replace(clkItem, "{HEIGHT}", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
			clkItem = strings.Replace(clkItem, "{DOWNX}", tmpDownX, -1)
			clkItem = strings.Replace(clkItem, "{DOWNY}", tmpDownY, -1)
			clkItem = strings.Replace(clkItem, "{UPX}", tmpUpX, -1)
			clkItem = strings.Replace(clkItem, "{UPY}", tmpUpY, -1)
			clkItem = strings.Replace(clkItem, "{WIN_PRICE}", encodePriceValue, -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, clkItem)
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = xiaomiEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// kuaishou resp
	respKuaiShou := models.MHUpResp{}
	respKuaiShou.RespData = &mhResp
	respKuaiShou.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respKuaiShou
}

func EncodeXiaoMiPrice(price int64, encryptionKey string, integrityKey string) (string, error) {
	// var (
	// 	INITIALIZATION_VECTOR_SIZE = 16
	// 	ENCODE_FORMAT              = "US-ASCII"
	// 	SECRET_FORMAT              = "HmacSHA1"
	// )
	INITIALIZATION_VECTOR_SIZE := 16

	iv := []byte("1234567891234567")[:INITIALIZATION_VECTOR_SIZE]

	var demoEncryptionKey, demoIntegrityKey []byte
	if encryptionKey != "" && integrityKey != "" {
		demoEncryptionKey = []byte(encryptionKey)
		demoIntegrityKey = []byte(integrityKey)
	}

	hmacPad := hmac.New(sha1.New, demoEncryptionKey)
	hmacPad.Write(iv)
	pad := hmacPad.Sum(nil)[:8]

	str := strconv.FormatInt(price, 10)
	priceBytes := make([]byte, 8)
	copy(priceBytes, str)
	encPrice := make([]byte, 8)
	for i := 0; i < 8; i++ {
		encPrice[i] = pad[i] ^ priceBytes[i]
	}

	hmacSig := hmac.New(sha1.New, demoIntegrityKey)
	hmacSig.Write(append(priceBytes, iv...))
	sig := hmacSig.Sum(nil)[:4]

	finalMessage := make([]byte, INITIALIZATION_VECTOR_SIZE+len(encPrice)+len(sig))
	copy(finalMessage[:INITIALIZATION_VECTOR_SIZE], iv)
	copy(finalMessage[INITIALIZATION_VECTOR_SIZE:], encPrice)
	copy(finalMessage[INITIALIZATION_VECTOR_SIZE+len(encPrice):], sig)

	r := base64.RawURLEncoding.EncodeToString(finalMessage)

	return r, nil
}

// get price win url
func getXiaoMiPriceOKURL(nurls []string, platformPos *models.PlatformPosStu, xiaomiEcpm int) (string, []string) {
	if xiaomiEcpm <= 0 || len(nurls) == 0 {
		return "{WIN_PRICE}", []string{}
	}

	if platformPos.PlatformAppIsPriceEncrypt == 0 ||
		len(platformPos.PlatformAppPriceEncrypt) == 0 ||
		len(platformPos.PlatformAppPriceEncrypt2) == 0 {
		return "{WIN_PRICE}", []string{}
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return "{WIN_PRICE}", []string{}
	}

	var tmpWinNoticeURLArray []string

	randPRValue := 100
	if platformPos.PlatformAppReportWinType == 0 {
		randPRValue = 100
	} else if platformPos.PlatformAppReportWinType == 1 {
		tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
		tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
		if tmp1 <= tmp2 {
			randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
		}
	}

	eKey := platformPos.PlatformAppPriceEncrypt
	iKey := platformPos.PlatformAppPriceEncrypt2

	randPrice := xiaomiEcpm * randPRValue / 100
	encodePriceValue, _ := EncodeXiaoMiPrice(int64(randPrice), eKey, iKey)

	for _, nurlItem := range nurls {
		if len(nurlItem) > 0 {
			tmpNurlItem := strings.Replace(nurlItem, "{WIN_PRICE}", encodePriceValue, -1)

			tmpWinNoticeURLArray = append(tmpWinNoticeURLArray, tmpNurlItem)
		}
	}

	return encodePriceValue, tmpWinNoticeURLArray
}

// XiaoMiRespStu ...
type XiaoMiRespStu struct {
	Code    int               `json:"code"`
	AdInfos []XiaoMiRespAdStu `json:"adInfos"`
}

type XiaoMiRespAdStu struct {
	ID                     int                    `json:"id"`
	Title                  string                 `json:"title"`
	Description            string                 `json:"summary"`
	Brand                  string                 `json:"brand"`
	Assets                 []XiaoMiRespAdAssetStu `json:"assets"`
	CreativeType           int                    `json:"creativeType"`
	TargetType             int                    `json:"targetType"`
	Deeplink               string                 `json:"deeplink"`
	LandingPageURL         string                 `json:"landingPageUrl"`
	DownloadURL            string                 `json:"actionUrl"`
	IconURL                string                 `json:"iconUrl"`
	PackageName            string                 `json:"packageName"`
	ImpressionMonitorURLs  []string               `json:"viewMonitorUrls"`
	ClickMonitorURLs       []string               `json:"clickMonitorUrls"`
	VideoStartMonitorURLs  []string               `json:"videoStartMonitorUrls"`
	VideoFinishMonitorURLs []string               `json:"videoFinishMonitorUrls"`
	Ecpm                   int                    `json:"price"`
	AppName                string                 `json:"appName"`
	AppDeveloper           string                 `json:"appDeveloper"`
	AppVersion             string                 `json:"appVersion"`
	AppPermissionURL       string                 `json:"appPermission"`
	AppPrivacyURL          string                 `json:"appPrivacy"`
	AppIntroductionURL     string                 `json:"appIntroduction"`
	Nurls                  []string               `json:"nurl"`
}

type XiaoMiRespAdAssetStu struct {
	URL          string `json:"url"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	MaterialType int    `json:"materialType"`
}
