package core

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/tanx_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromTanx ...
func GetFromTanx(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from tanx")

	// debug
	// platformPos.PlatformPosID = "mm_116532257_14926483_58180202"

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		fmt.Println("tanx ua failed:", destConfigUA)
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	// tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	// tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	// tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	// tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// device
	tanxReqDevice := &tanx_up.Request_Device{}

	// device ip
	tanxReqDevice.Ip = proto.String(mhReq.Device.IP)

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		tanxReqDevice.UserAgent = proto.String(destConfigUA)
	}

	// device carrier
	if mhReq.Network.Carrier == 1 {
		tanxReqDevice.Operator = proto.Int32(1)
	} else if mhReq.Network.Carrier == 2 {
		tanxReqDevice.Operator = proto.Int32(2)
	} else if mhReq.Network.Carrier == 3 {
		tanxReqDevice.Operator = proto.Int32(3)
	} else {
		tanxReqDevice.Operator = proto.Int32(0)
	}

	// device network, 设备所处网络环境 0-未识别,1-wifi,2-2g,3-3g,4-4g,5-5g
	if mhReq.Network.ConnectType == 0 {
		tanxReqDevice.Network = proto.Int32(0)
	} else if mhReq.Network.ConnectType == 1 {
		tanxReqDevice.Network = proto.Int32(1)
	} else if mhReq.Network.ConnectType == 2 {
		tanxReqDevice.Network = proto.Int32(2)
	} else if mhReq.Network.ConnectType == 3 {
		tanxReqDevice.Network = proto.Int32(3)
	} else if mhReq.Network.ConnectType == 4 {
		tanxReqDevice.Network = proto.Int32(4)
	} else if mhReq.Network.ConnectType == 7 {
		tanxReqDevice.Network = proto.Int32(5)
	}

	// device type
	tanxReqDevice.DeviceType = proto.Int32(0)

	// device os
	if mhReq.Device.Os == "android" {
		tanxReqDevice.Os = proto.String("Android")
	} else if mhReq.Device.Os == "ios" {
		tanxReqDevice.Os = proto.String("iOS")
	}

	// device osv
	tanxReqDevice.Osv = proto.String(mhReq.Device.OsVersion)

	// device model
	tanxReqDevice.Model = proto.String(mhReq.Device.Model)

	// device make
	tanxReqDevice.Brand = proto.String(mhReq.Device.Manufacturer)

	// device screen width, height
	tanxReqDevice.Width = proto.Int32(int32(mhReq.Device.ScreenWidth))
	tanxReqDevice.Height = proto.Int32(int32(mhReq.Device.ScreenHeight))

	if len(mhReq.Device.BootMark) > 0 {
		tanxReqDevice.BootMark = proto.String(mhReq.Device.BootMark)
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		tanxReqDevice.UpdateMark = proto.String(mhReq.Device.UpdateMark)
	}

	// geo
	if mhReq.Geo.Lat > 0 && mhReq.Geo.Lng > 0 {
		tanxReqDevice.Geo = &tanx_up.Request_Device_Geo{
			Lat: proto.Float64(mhReq.Geo.Lat),
			Lon: proto.Float64(mhReq.Geo.Lng),
		}
	}

	// device app list
	if platformPos.PlatformAppIsReportAppList == 1 && len(mhReq.Device.AppList) > 0 {
		tanxReqDevice.InstalledApp = models2.GetTanxPackageNameByAppIdByOs(mhReq.Device.AppList, mhReq.Device.Os)
	}

	// device uid
	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				tanxReqDevice.ImeiMd5 = proto.String(strings.ToUpper(utils.GetMd5(mhReq.Device.Imei)))
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				tanxReqDevice.ImeiMd5 = proto.String(strings.ToUpper(mhReq.Device.ImeiMd5))
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				tanxReqDevice.Oaid = proto.String(mhReq.Device.Oaid)
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true

				tanxReqDevice.OaidMd5 = proto.String(strings.ToUpper(mhReq.Device.OaidMd5))
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from tanx error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(mhReq.Device.Idfa)))

			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
							tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

							tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)

							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
						tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
						tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

						tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
		}
		if strings.Contains(iosReportMainParameter, "paid") {
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(mhReq.Device.Idfa)))

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
								tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
								tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

								tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
							tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

							tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(mhReq.Device.Idfa)))

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
								tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
								tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

								tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
							tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

							tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from tanx error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug tanx android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					tanxReqDevice.Osv = proto.String(didRedisData.OsVersion)

					// device model
					tanxReqDevice.Model = proto.String(didRedisData.Model)

					// device make
					tanxReqDevice.Brand = proto.String(didRedisData.Manufacturer)

					// 初始化为空
					tanxReqDevice.ImeiMd5 = proto.String("")
					tanxReqDevice.Oaid = proto.String("")
					tanxReqDevice.OaidMd5 = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							tanxReqDevice.ImeiMd5 = proto.String(strings.ToUpper(utils.GetMd5(didRedisData.Imei)))
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							tanxReqDevice.ImeiMd5 = proto.String(strings.ToUpper(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							tanxReqDevice.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						tanxReqDevice.UserAgent = proto.String(didRedisData.Ua)

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug tanx ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						tanxReqDevice.Osv = proto.String(didRedisData.OsVersion)

						// device model
						tanxReqDevice.Model = proto.String(didRedisData.Model)

						// device make
						tanxReqDevice.Brand = proto.String(didRedisData.Manufacturer)

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						// 初始化为空
						tanxReqDevice.IdfaMd5 = proto.String("")
						tanxReqDevice.Caids = tanxReqDevice.Caids[:0]

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(didRedisData.Idfa)))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
										tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

										tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
									tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
									tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

									tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
						}

						if strings.Contains(iosReportMainParameter, "paid") {
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(didRedisData.Idfa)))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
											tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
											tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

											tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)

											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
										tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

										tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(utils.GetMd5(didRedisData.Idfa)))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									tanxReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
											tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
											tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

											tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)

											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpTanxReqDeviceCaid := &tanx_up.Request_Device_CAID{}
										tmpTanxReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpTanxReqDeviceCaid.Ver = proto.String(item.CAIDVersion)

										tanxReqDevice.Caids = append(tanxReqDevice.Caids, tmpTanxReqDeviceCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							tanxReqDevice.UserAgent = proto.String(didRedisData.Ua)

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from tanx error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from tanx error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// req imp
	tanxReqImp := &tanx_up.Request_Impression{
		Id:       proto.Int32(0),
		Pid:      proto.String(platformPos.PlatformPosID),
		Width:    proto.Int32(int32(platformPos.PlatformPosWidth)),
		Height:   proto.Int32(int32(platformPos.PlatformPosHeight)),
		Pos:      proto.Int32(0),
		SlotNum:  proto.Int32(int32(mhReq.Pos.AdCount)),
		BidFloor: proto.Int32(int32(localPosFloorPrice)),
	}
	if len(categoryInfo.DealID) > 0 {
		tanxReqImpDeal := &tanx_up.Request_Impression_Deal{
			DealId:   proto.String(categoryInfo.DealID),
			MinPrice: proto.Int32(int32(localPosFloorPrice)),
		}
		tanxReqImp.Deal = append(tanxReqImp.Deal, tanxReqImpDeal)
	}

	// debug
	// tanxReqImp.NativeTemplateId = append(tanxReqImp.NativeTemplateId, "1000349")

	// req app
	tanxReqApp := &tanx_up.Request_App{
		AppName:     proto.String(platformPos.PlatformAppName),
		PackageName: proto.String(GetAppBundleByConfig(c, mhReq, localPos, platformPos)),
		Category:    []string{"105001"},
	}

	tanxReqPb := &tanx_up.Request{
		Version:       proto.Int32(2),
		Id:            proto.String(utils.GetMd5(bigdataUID)),
		App:           tanxReqApp,
		Device:        tanxReqDevice,
		HttpsRequired: proto.Bool(true),
		// 本次请求是否预览广告。若为 true，则返回 预览广告。默认值 false。
		// IsPreview:     proto.Bool(true),
	}
	tanxReqPb.Imp = append(tanxReqPb.Imp, tanxReqImp)

	////////////////////////////////////////////////////////////////////////////////////////
	// debugStr := `
	// {
	// 	"version": 2,
	// 	"id": "0a674362000057b6be176e7600d4c089",
	// 	"imp": {
	// 		"id": 0,
	// 		"pid": "mm_116532257_14926483_58180224",
	// 		"deal": {
	// 			"deal_id": "feed_dealid_test",
	// 			"min_price": "0"
	// 		}
	// 	},
	// 	"device": {
	// 		"ip": "*************",
	// 		"user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-CN; vivo Y13 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 UCBrowser/10.10.8.822 U3/0.8.0 Mobile Safari/534.30",
	// 		"imei": "867348022656925",
	// 		"device_type": 0,
	// 		"brand": "Samsung",
	// 		"model": "galaxy",
	// 		"os": "Android",
	// 		"osv": "4.4.4",
	// 		"network": 1,
	// 		"operator": 1,
	// 		"width": 480,
	// 		"height": 640,
	// 		"pixel_ratio": 10000,
	// 		"timezone_offset": 480
	// 	},
	// 	"app": {
	// 		"package_name": "com.xxxx.news",
	// 		"app_name": "新闻",
	// 		"category": "101701"
	// 	}
	// }
	// `
	// debugXXX := json.Unmarshal([]byte(debugStr), tanxReqPb)
	// fmt.Println("json unmarshal error:", debugXXX)
	////////////////////////////////////////////////////////////////////////////////////////
	tanxReqPbByte, xxxxxx := proto.Marshal(tanxReqPb)
	if xxxxxx != nil {
		panic(xxxxxx)
	}

	// fmt.Println("========================================================================================================================")
	// tmpReqByte, _ := json.Marshal(&tanxReqPb)
	// fmt.Println("tanx req:", string(tmpReqByte))
	// fmt.Println("tanx req url:", platformPos.PlatformAppUpURL)
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/x-protobuf; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(tanxReqPbByte))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	tanxResp := &tanx_up.Response{}
	err = proto.Unmarshal(bodyContent, tanxResp)
	if err != nil {
		fmt.Println(err)
	}

	// debug
	if rand.Intn(5000000) == 0 {
		tmpRespByte, _ := json.Marshal(tanxResp)
		go models.BigDataHoloDebugJson2(bigdataUID+"&tanx", string(tmpRespByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}

	// fmt.Println("========================================================================================================================")
	// tmpRespByte, _ := json.Marshal(tanxResp)
	// fmt.Println("tanx resp code:", resp.StatusCode)
	// fmt.Println("tanx resp body:", string(tmpRespByte))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if tanxResp.GetStatus() != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(tanxResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(tanxResp.GetSeat()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(tanxResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(tanxResp.GetSeat()[0].GetAd()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(tanxResp.GetStatus())

		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// if platformPos.PlatformAppID == "dae57f48" || platformPos.PlatformAppID == "df53e75a" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("debug panic:", err)
	// 			}
	// 		}()

	// 		tmpRespByte, _ := json.Marshal(tanxResp)
	// 		models.SaveDemandRespDataToHolo(c, bigdataUID, platformPos.PlatformAppID, platformPos.PlatformPosID, string(tmpRespByte))
	// 	}()
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := tanxResp.Seat[0].Ad

	if len(respDataListItemStu) > 1 {
		sort.Sort(tanxPbEcpmSort(respDataListItemStu))
	}

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		tanxEcpm := int(item.GetBidPrice())

		respTmpPrice = respTmpPrice + tanxEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if tanxEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			tanxEcpm = platformPos.PlatformPosEcpm
		}

		tanxLossNoticeURL := getTanxPriceFailedURL(tanxResp.GetId(), item.GetLossNoticeUrl(), platformPos, tanxEcpm)

		// fmt.Println("gdt_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(tanxEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > tanxEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)

			continue
		}

		// 创意类型，表示素材是一个图片或者视频片段;1-文字;2-图片;3-Flash;4-视频
		if item.GetCreativeType() == 1 || item.GetCreativeType() == 3 {
			continue
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(tanxEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// 计算价格宏
		tanxWinPriceMacro := "__AUCTION_PRICE__"
		if tanxEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {

			randPRValue := 100
			if platformPos.PlatformAppReportWinType == 0 {
				randPRValue = 100
			} else if platformPos.PlatformAppReportWinType == 1 {
				tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
				tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
				if tmp1 <= tmp2 {
					randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
				}
			}
			tmpTanxEcpm := int(tanxEcpm * randPRValue / 100)
			token, _ := hex.DecodeString(platformPos.PlatformAppPriceEncrypt)

			decodeStr, _ := hex.DecodeString(tanxResp.GetId() + fmt.Sprintf("%08X", tmpTanxEcpm))
			encodePriceValue1 := utils.AesECBEncrypt([]byte(decodeStr), []byte(token))

			tanxWinPriceMacro = base64.RawURLEncoding.EncodeToString(encodePriceValue1)
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := ""
		respDescription := ""
		respIconURL := ""
		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		respVideoURL := ""
		respVideoWidth := 0
		respVideoHeight := 0
		respVideoDuration := 0

		for _, attrItem := range item.GetNativeAd().GetAttr() {
			if attrItem.GetName() == "title" {
				respTitle = attrItem.GetValue()
			} else if attrItem.GetName() == "description" {
				respDescription = attrItem.GetValue()
			} else if attrItem.GetName() == "img_sm" {
				respIconURL = attrItem.GetValue()
			} else if attrItem.GetName() == "img_url" {
				respImageURL = attrItem.GetValue()
			} else if attrItem.GetName() == "img_url_width" {
				respImageWidth = utils.ConvertStringToInt(attrItem.GetValue())
			} else if attrItem.GetName() == "img_url_height" {
				respImageHeight = utils.ConvertStringToInt(attrItem.GetValue())
			} else if attrItem.GetName() == "video" {
				respVideoURL = attrItem.GetValue()
			} else if attrItem.GetName() == "video_width" {
				respVideoWidth = utils.ConvertStringToInt(attrItem.GetValue())
			} else if attrItem.GetName() == "video_height" {
				respVideoHeight = utils.ConvertStringToInt(attrItem.GetValue())
			} else if attrItem.GetName() == "video_duration" {
				respVideoDuration = utils.ConvertStringToInt(attrItem.GetValue())
			}
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		if mhReq.Device.Os == "android" {
			if len(item.GetDownloadUrl()) > 0 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.GetDownloadUrl()
				respListItemMap["download_url"] = item.GetDownloadUrl()
				if len(item.GetClickThroughUrl()) > 0 {
					respListItemMap["landpage_url"] = item.GetClickThroughUrl()
				}
			} else {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.GetClickThroughUrl()
				respListItemMap["landpage_url"] = item.GetClickThroughUrl()
			}
		} else if mhReq.Device.Os == "ios" {
			if len(item.GetDownloadUrl()) > 0 {
				respListItemMap["ad_url"] = item.GetDownloadUrl()
				respListItemMap["interact_type"] = 1
				respListItemMap["download_url"] = item.GetDownloadUrl()
			} else {
				respListItemMap["ad_url"] = item.GetClickThroughUrl()
				respListItemMap["interact_type"] = 0
				respListItemMap["landpage_url"] = item.GetClickThroughUrl()
			}
		}

		if len(respVideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if respVideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, respVideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
					continue
				}

				respListVideoItemMap["duration"] = respVideoDuration
			}
			respListVideoItemMap["width"] = respVideoWidth
			respListVideoItemMap["height"] = respVideoHeight
			respListVideoItemMap["video_url"] = respVideoURL

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respVideoWidth, respVideoHeight)
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
				continue
			}

			// cover_url
			if len(respImageURL) > 0 {
				respListVideoItemMap["cover_url"] = respImageURL
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			isVideoTrack := 0
			// event track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range item.GetEventTrack() {
				if monitorItem.GetType() == 1 {
					for _, monitorUrlItem := range monitorItem.GetUrl() {
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, string(monitorUrlItem))
					}
				}
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				isVideoTrack = 1
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range item.GetEventTrack() {
				if monitorItem.GetType() == 3 {
					for _, monitorUrlItem := range monitorItem.GetUrl() {
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, string(monitorUrlItem))
					}
				}
			}
			if len(respListVideoEndEventTrackURLMap) > 0 {
				isVideoTrack = 1

				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if isVideoTrack == 1 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL
				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		if len(item.GetCreativeId()) > 0 {
			respListItemMap["crid"] = item.GetCreativeId()
		}

		// deeplink
		destDeepLink := ""
		if len(item.GetDeeplinkUrl()) > 0 {
			destDeepLink = item.GetDeeplinkUrl()

			respListItemMap["deep_link"] = destDeepLink
		}

		if item.GetOpenType() == 4 && len(item.GetClickThroughUrl()) > 0 {
			destDeepLink = item.GetClickThroughUrl()

			respListItemMap["deep_link"] = item.GetClickThroughUrl()
		}

		if len(destDeepLink) > 0 {

			// deeplink track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			// if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			// 	var respListItemDeepLinkFailedArray []string

			// 	if len(item.ConversionLink) > 0 {
			// 		tmpConversionLink := strings.Replace(item.ConversionLink, "__ACTION_ID__", "249", -1)
			// 		respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, tmpConversionLink)
			// 	}

			// 	mhDPFailedParams := url.Values{}
			// 	mhDPFailedParams.Add("result", "1")
			// 	mhDPFailedParams.Add("reason", "3")
			// 	mhDPFailedParams.Add("log", bigdataParams)

			// 	respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			// 	respListItemConv11Map := map[string]interface{}{}
			// 	respListItemConv11Map["conv_type"] = 11
			// 	respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

			// 	respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			// }

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// // package name
		// destPackageName := ""
		// if len(string(item.GetAdm().GetPackageName())) > 0 {
		// 	// 通过pacakge_name过滤
		// 	isMaterialFilter, filterErrCode := IsMaterialFilterPackageNameByConfig(c, mhReq, localPos, platformPos, string(item.GetAdm().GetPackageName()))
		// 	if isMaterialFilter {
		// 		respTmpInternalCode = filterErrCode
		// 		respTmpRespFailedNum = respTmpRespFailedNum + 1

		// 		curlTanxPriceFailedURL(item.GetLossNoticeUrl(), platformPos, tanxEcpm, &bigdataExtra)
		// 		continue
		// 	}

		// 	respListItemMap["package_name"] = string(item.GetAdm().GetPackageName())

		// 	destPackageName = string(item.GetAdm().GetPackageName())
		// } else {
		// 	hackPackageName := GetPackageNameByDeeplink(destDeepLink)
		// 	if len(hackPackageName) > 0 {
		// 		respListItemMap["package_name"] = hackPackageName

		// 		destPackageName = hackPackageName
		// 	}
		// }

		// // android
		// if mhReq.Device.Os == "android" {
		// 	appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, string(item.GetAdm().GetAppName()), destPackageName)
		// 	if appInfoFromRedisErr == nil {
		// 		respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
		// 		respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
		// 	}
		// }

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string

		// impression_link tanx
		for _, showItem := range item.GetImpressionTrackingUrl() {
			tmpItem := string(showItem)

			tmpItem = strings.Replace(tmpItem, "__IP__", tanxReqDevice.GetIp(), -1)
			tmpItem = strings.Replace(tmpItem, "__UA__", tanxReqDevice.GetUserAgent(), -1)
			tmpItem = strings.Replace(tmpItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
			if len(tanxReqDevice.GetBootMark()) > 0 {
				tmpItem = strings.Replace(tmpItem, "__BOOT_MARK__", tanxReqDevice.GetBootMark(), -1)
			}
			if len(tanxReqDevice.GetUpdateMark()) > 0 {
				tmpItem = strings.Replace(tmpItem, "__UPDATE_MARK__", tanxReqDevice.GetUpdateMark(), -1)
			}
			if mhReq.Device.Os == "android" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "2", -1)

				if len(tanxReqDevice.GetImeiMd5()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IMEI_MD5__", tanxReqDevice.GetImeiMd5(), -1)
				}
				if len(tanxReqDevice.GetOaid()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__OAID__", tanxReqDevice.GetOaid(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)

				if len(tanxReqDevice.GetIdfaMd5()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", tanxReqDevice.GetIdfaMd5(), -1)
				}
				if len(tanxReqDevice.GetCaids()) > 0 {
					var tmpCaidMacroArray []string
					for _, tmpCaidItem := range tanxReqDevice.GetCaids() {
						tmpCaidMacroArray = append(tmpCaidMacroArray, tmpCaidItem.GetVer()+"_"+tmpCaidItem.GetCaid())
					}
					tmpItem = strings.Replace(tmpItem, "__CAID__", url.QueryEscape(strings.Join(tmpCaidMacroArray, ",")), -1)
				}
			}
			// 曝光价格宏替换
			tmpItem = strings.Replace(tmpItem, "__AUCTION_PRICE__", tanxWinPriceMacro, -1)

			respListItemImpArray = append(respListItemImpArray, tmpItem)
		}

		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, tanxEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		tanxWinNoticeURL := getTanxPriceWinURL(item.GetWinnoticeUrl(), platformPos, tanxEcpm, tanxWinPriceMacro)
		if len(tanxWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, tanxWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, clickItem := range item.GetClickTrackingUrl() {
			tmpItem := string(clickItem)
			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					tmpItem = tmpItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			tmpItem = strings.Replace(tmpItem, "__IP__", tanxReqDevice.GetIp(), -1)
			tmpItem = strings.Replace(tmpItem, "__UA__", tanxReqDevice.GetUserAgent(), -1)
			tmpItem = strings.Replace(tmpItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
			if len(tanxReqDevice.GetBootMark()) > 0 {
				tmpItem = strings.Replace(tmpItem, "__BOOT_MARK__", tanxReqDevice.GetBootMark(), -1)
			}
			if len(tanxReqDevice.GetUpdateMark()) > 0 {
				tmpItem = strings.Replace(tmpItem, "__UPDATE_MARK__", tanxReqDevice.GetUpdateMark(), -1)
			}
			if mhReq.Device.Os == "android" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "2", -1)

				if len(tanxReqDevice.GetImeiMd5()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IMEI_MD5__", tanxReqDevice.GetImeiMd5(), -1)
				}
				if len(tanxReqDevice.GetOaid()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__OAID__", tanxReqDevice.GetOaid(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)

				if len(tanxReqDevice.GetIdfaMd5()) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", tanxReqDevice.GetIdfaMd5(), -1)
				}
				if len(tanxReqDevice.GetCaids()) > 0 {
					var tmpCaidMacroArray []string
					for _, tmpCaidItem := range tanxReqDevice.GetCaids() {
						tmpCaidMacroArray = append(tmpCaidMacroArray, tmpCaidItem.GetVer()+"_"+tmpCaidItem.GetCaid())
					}
					tmpItem = strings.Replace(tmpItem, "__CAID__", url.QueryEscape(strings.Join(tmpCaidMacroArray, ",")), -1)
				}
			}

			// 点击价格宏替换
			tmpItem = strings.Replace(tmpItem, "__AUCTION_PRICE__", tanxWinPriceMacro, -1)

			respListItemClkArray = append(respListItemClkArray, tmpItem)
		}

		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(tanxLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, tanxLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlTanxPriceFailedURL(tanxLossNoticeURL, &bigdataExtra)
			continue
		}

		// 上报竞价失败
		if len(tanxLossNoticeURL) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, tanxLossNoticeURL)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = tanxEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("tanx resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tanx resp
	respGdt := models.MHUpResp{}
	respGdt.RespData = &mhResp
	respGdt.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respGdt
}

type tanxPbEcpmSort []*tanx_up.Response_Seat_Ad

func (s tanxPbEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s tanxPbEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s tanxPbEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].GetBidPrice() > s[j].GetBidPrice()
}

func getTanxPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, tanxEcpm int, winPriceMacro string) string {
	if tanxEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	if tanxEcpm > 0 && len(winNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		tmpWinNoticeURL := winNoticeURL
		tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "__AUCTION_PRICE__", winPriceMacro, -1)

		return tmpWinNoticeURL
	}
	return ""
}

func getTanxPriceFailedURL(tanxRespId string, lossNoticeURL string, platformPos *models.PlatformPosStu, tanxEcpm int) string {
	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	if tanxEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		randPrice := int(tanxEcpm * tmpRandValue / 100)

		token, _ := hex.DecodeString(platformPos.PlatformAppPriceEncrypt)

		decodeStr, _ := hex.DecodeString(tanxRespId + fmt.Sprintf("%08X", randPrice))
		encodePriceValue1 := utils.AesECBEncrypt([]byte(decodeStr), []byte(token))

		encodePriceValue := base64.RawURLEncoding.EncodeToString(encodePriceValue1)
		// fmt.Println("debug tanx price:", randPrice, hex.EncodeToString(encodePriceValue1), encodePriceValue)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_PRICE__", encodePriceValue, -1)
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__LOSS_REASON__", "1040435", -1)

		return tmpLossNoticeURL
	}

	return ""
}

// curl price failed
func curlTanxPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("tanx loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}
