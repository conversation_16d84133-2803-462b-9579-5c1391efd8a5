package core

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/quna_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"

	uuid "github.com/satori/go.uuid"
)

// 去哪 adx
func GetFromupQuna(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := mhReq.Device.CPUNum

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType quna_up.Request_Device_ConnectionType
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = quna_up.Request_Device_WiFi

	case 3:
		connectionType = quna_up.Request_Device_G3
	case 4, 5, 6:
		connectionType = quna_up.Request_Device_G4
	case 7:
		connectionType = quna_up.Request_Device_G5
	}

	var carrier string
	switch mhReq.Network.Carrier {
	case 1:
		carrier = "mobile"
	case 2:
		carrier = "unicom"
	case 3:
		carrier = "telecom"
	default:
		carrier = "unknown"
	}

	var impList []*quna_up.Request_Imp
	imp := &quna_up.Request_Imp{
		Id:         uuid.NewV4().String(),
		Tagid:      platformPos.PlatformPosID,
		Bidfloor:   float64(categoryInfo.FloorPrice),
		Isdeeplink: true,
		Isul:       true,
		Isdownload: true,
		Secure:     1,
	}

	id := 0
	switch platformPos.PlatformPosType {
	case 2, 3:
		var nativeAssetArray []*quna_up.Request_Imp_NativeAsset
		var native quna_up.Request_Imp_Native
		if platformPos.PlatformPosMaterialType == 0 || platformPos.PlatformPosMaterialType == 2 {
			id = id + 1
			nativeAssetNaImg := quna_up.Request_Imp_NativeAsset_NaImg{
				Type: 3,
				Wmin: uint32(platformPos.PlatformPosWidth),
				Hmin: uint32(platformPos.PlatformPosHeight),
			}

			nativeImgAsset := &quna_up.Request_Imp_NativeAsset{
				Id:         uint32(id),
				Isrequired: 1,
				Img:        &nativeAssetNaImg,
			}
			nativeAssetArray = append(nativeAssetArray, nativeImgAsset)

			native.Assets = nativeAssetArray
			native.Layout = 501
		}

		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			id = id + 1
			nativeAssetNaVideo := quna_up.Request_Imp_NativeAsset_NaVideo{
				Wmin: uint32(platformPos.PlatformPosWidth),
				Hmin: uint32(platformPos.PlatformPosHeight),
			}

			nativeImgAsset := &quna_up.Request_Imp_NativeAsset{
				Id:         uint32(id),
				Isrequired: 1,
				Video:      &nativeAssetNaVideo,
			}
			nativeAssetArray = append(nativeAssetArray, nativeImgAsset)

			native.Assets = nativeAssetArray
			native.Layout = 501
		}

		imp.Native = &native
	case 4:
		var nativeAssetArray []*quna_up.Request_Imp_NativeAsset
		var native quna_up.Request_Imp_Native
		if platformPos.PlatformPosMaterialType == 0 || platformPos.PlatformPosMaterialType == 2 {
			id = id + 1
			nativeAssetNaImg := quna_up.Request_Imp_NativeAsset_NaImg{
				Type: 3,
				Wmin: uint32(platformPos.PlatformPosWidth),
				Hmin: uint32(platformPos.PlatformPosHeight),
			}

			nativeImgAsset := &quna_up.Request_Imp_NativeAsset{
				Id:         uint32(id),
				Isrequired: 1,
				Img:        &nativeAssetNaImg,
			}
			nativeAssetArray = append(nativeAssetArray, nativeImgAsset)

			native.Assets = nativeAssetArray
			native.Layout = 501
		}

		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			id = id + 1
			nativeAssetNaVideo := quna_up.Request_Imp_NativeAsset_NaVideo{
				Wmin: uint32(platformPos.PlatformPosWidth),
				Hmin: uint32(platformPos.PlatformPosHeight),
			}

			nativeImgAsset := &quna_up.Request_Imp_NativeAsset{
				Id:         uint32(id),
				Isrequired: 1,
				Video:      &nativeAssetNaVideo,
			}
			nativeAssetArray = append(nativeAssetArray, nativeImgAsset)

			native.Assets = nativeAssetArray
			native.Layout = 3
		}

		imp.Native = &native
	case 9:
		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			impVideo := quna_up.Request_Imp_Video{
				W:    uint32(platformPos.PlatformPosWidth),
				H:    uint32(platformPos.PlatformPosHeight),
				Type: 1,
			}

			imp.Video = &impVideo
		}
	case 11:
		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			impVideo := quna_up.Request_Imp_Video{
				W:    uint32(platformPos.PlatformPosWidth),
				H:    uint32(platformPos.PlatformPosHeight),
				Type: 2,
			}
			imp.Video = &impVideo
		}
	}

	impList = append(impList, imp)
	reqApp := &quna_up.Request_App{
		Bundle:  GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		Name:    platformPos.PlatformAppName,
		Version: platformPos.PlatformAppVersion,
	}

	request := quna_up.Request{
		Id:      bigdataUID,
		Version: "2.0",
		Imp:     impList,
		App:     reqApp,
		Device: &quna_up.Request_Device{
			Os:             mhReq.Device.Os,
			Osv:            mhReq.Device.OsVersion,
			Mac:            mhReq.Device.Mac,
			Ip:             mhReq.Device.IP,
			Ua:             mhReq.Device.Ua,
			Connectiontype: connectionType,
			Devicetype:     4,
			Make:           mhReq.Device.Manufacturer,
			Brand:          mhReq.Device.Manufacturer,
			Model:          mhReq.Device.Model,
			Carrier:        carrier,
			Screenheight:   uint32(mhReq.Device.ScreenHeight),
			Screenwidth:    uint32(mhReq.Device.ScreenWidth),
			BootMark:       mhReq.Device.BootMark,
			UpdateMark:     mhReq.Device.UpdateMark,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Device.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Device.Did = mhReq.Device.Imei
				request.Device.Didmd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.Device.Didmd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Device.Oid = mhReq.Device.Oaid
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true
				request.Device.Oidmd5 = strings.ToLower(mhReq.Device.OaidMd5)
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					request.Device.Androidid = mhReq.Device.AndroidID
					if len(mhReq.Device.AndroidIDMd5) > 0 {
						request.Device.Androididmd5 = mhReq.Device.AndroidIDMd5
					}
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Device.Ifa = mhReq.Device.Idfa
				request.Device.Ifamd5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				request.Device.Ifamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			} else {
				for _, item := range mhReq.Device.CAIDMulti {
					request.Device.Caid = item.CAID
					request.Device.CaidVersion = item.CAIDVersion

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				request.Device.Language = tmpLanguage
				request.Device.Country = tmpCountry
				request.Device.BirthTime = tmpDeviceBirthSec
				request.Device.StartTimeMsec = tmpDeviceStartSec
				request.Device.UpdateTimeNsec = tmpSystemUpdateSec
				request.Device.HardwareModel = tmpHardwareModel
				request.Device.LocalTzTime = tmpTimeZone
				request.Device.DeviceNameMd5 = tmpDeviceNameMd5
				cpu, _ := strconv.Atoi(tmpCPUNum)
				diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
				memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

				request.Device.CpuNum = int32(cpu)
				request.Device.DiskTotal = diskTotal
				request.Device.MemTotal = memTotal
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Ifa = mhReq.Device.Idfa
					request.Device.Ifamd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Ifamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.CaidVersion = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					request.Device.Language = tmpLanguage
					request.Device.Country = tmpCountry
					request.Device.BirthTime = tmpDeviceBirthSec
					request.Device.StartTimeMsec = tmpDeviceStartSec
					request.Device.UpdateTimeNsec = tmpSystemUpdateSec
					request.Device.HardwareModel = tmpHardwareModel
					request.Device.LocalTzTime = tmpTimeZone
					request.Device.DeviceNameMd5 = tmpDeviceNameMd5
					cpu, _ := strconv.Atoi(tmpCPUNum)
					diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
					memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

					request.Device.CpuNum = int32(cpu)
					request.Device.DiskTotal = diskTotal
					request.Device.MemTotal = memTotal

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Ifa = mhReq.Device.Idfa
					request.Device.Ifamd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Ifamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.CaidVersion = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					request.Device.Language = tmpLanguage
					request.Device.Country = tmpCountry
					request.Device.BirthTime = tmpDeviceBirthSec
					request.Device.StartTimeMsec = tmpDeviceStartSec
					request.Device.UpdateTimeNsec = tmpSystemUpdateSec
					request.Device.HardwareModel = tmpHardwareModel
					request.Device.LocalTzTime = tmpTimeZone
					request.Device.DeviceNameMd5 = tmpDeviceNameMd5
					cpu, _ := strconv.Atoi(tmpCPUNum)
					diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
					memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

					request.Device.CpuNum = int32(cpu)
					request.Device.DiskTotal = diskTotal
					request.Device.MemTotal = memTotal

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.Device.Os = "ios"
					case "android":
						request.Device.Os = "android"
					}
					request.Device.Osv = didRedisData.OsVersion
					request.Device.Model = didRedisData.Model
					request.Device.Make = didRedisData.Manufacturer
					request.Device.Brand = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Device.Did = didRedisData.Imei
							request.Device.Didmd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.Device.Didmd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Device.Oid = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								request.Device.Androidid = didRedisData.AndroidID
								if len(didRedisData.AndroidIDMd5) > 0 {
									request.Device.Androididmd5 = didRedisData.AndroidIDMd5
								}
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Device.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.Device.Os = "ios"
						case "android":
							request.Device.Os = "android"
						}
						request.Device.Osv = didRedisData.OsVersion
						request.Device.Model = didRedisData.Model
						request.Device.Make = didRedisData.Manufacturer
						request.Device.Brand = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Device.Ifa = didRedisData.Idfa
								request.Device.Ifamd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.Device.Ifamd5 = strings.ToLower(didRedisData.IdfaMd5)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									request.Device.Caid = item.CAID
									request.Device.CaidVersion = item.CAIDVersion

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
								}
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							cpu := didRedisData.CPUNum

							if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && cpu > 0 {
								request.Device.Language = tmpLanguage
								request.Device.Country = tmpCountry
								request.Device.BirthTime = tmpDeviceBirthSec
								request.Device.StartTimeMsec = tmpDeviceStartSec
								request.Device.UpdateTimeNsec = tmpSystemUpdateSec
								request.Device.HardwareModel = tmpHardwareModel
								request.Device.LocalTzTime = tmpTimeZone
								request.Device.DeviceNameMd5 = tmpDeviceNameMd5
								diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
								memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

								request.Device.CpuNum = int32(cpu)
								request.Device.DiskTotal = diskTotal
								request.Device.MemTotal = memTotal

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Ifa = didRedisData.Idfa
									request.Device.Ifamd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.Ifamd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.CaidVersion = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								cpu := didRedisData.CPUNum

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && cpu > 0 {
									request.Device.Language = tmpLanguage
									request.Device.Country = tmpCountry
									request.Device.BirthTime = tmpDeviceBirthSec
									request.Device.StartTimeMsec = tmpDeviceStartSec
									request.Device.UpdateTimeNsec = tmpSystemUpdateSec
									request.Device.HardwareModel = tmpHardwareModel
									request.Device.LocalTzTime = tmpTimeZone
									request.Device.DeviceNameMd5 = tmpDeviceNameMd5
									diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
									memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

									request.Device.CpuNum = int32(cpu)
									request.Device.DiskTotal = diskTotal
									request.Device.MemTotal = memTotal

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Ifa = didRedisData.Idfa
									request.Device.Ifamd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.Ifamd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.CaidVersion = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								cpu := didRedisData.CPUNum

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && cpu > 0 {
									request.Device.Language = tmpLanguage
									request.Device.Country = tmpCountry
									request.Device.BirthTime = tmpDeviceBirthSec
									request.Device.StartTimeMsec = tmpDeviceStartSec
									request.Device.UpdateTimeNsec = tmpSystemUpdateSec
									request.Device.HardwareModel = tmpHardwareModel
									request.Device.LocalTzTime = tmpTimeZone
									request.Device.DeviceNameMd5 = tmpDeviceNameMd5
									diskTotal, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
									memTotal, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)

									request.Device.CpuNum = int32(cpu)
									request.Device.DiskTotal = diskTotal
									request.Device.MemTotal = memTotal

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Device.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	requestMarshal, _ := proto.Marshal(&request)
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(requestMarshal))
	requestGet.Header.Add("Content-Type", "application/octet-stream;charset=utf-8")
	if platformPos.PlatformAppIsReportUa == 1 {
		requestGet.Header.Add("User-Agent", mhReq.Device.Ua)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)

	qunaRespStu := quna_up.Response{}
	_ = proto.Unmarshal(bodyContent, &qunaRespStu)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if qunaRespStu.Seatbid == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	for _, bidItem := range qunaRespStu.GetSeatbid().GetBids() {
		respTmpRespAllNum = respTmpRespAllNum + 1
		// ecpm
		price := int(bidItem.Price)

		respTmpPrice = respTmpPrice + price

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
			price = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > price {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			item.Ecpm = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			item.Ecpm = localPos.LocalPosEcpm
		}

		switch bidItem.GetAction() {
		case "1":
			item.InteractType = 0
			if len(bidItem.GetTarget()) > 0 {
				item.LandpageURL = bidItem.GetTarget()
			}
		case "2":
			item.InteractType = 1
			if len(bidItem.GetDownload()) > 0 {
				item.DownloadURL = bidItem.GetDownload()
			}
		default:
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		item.AdID = maplehazeAdId
		item.ReqWidth = platformPos.PlatformPosWidth
		item.ReqHeight = platformPos.PlatformPosHeight
		item.Crid = bidItem.GetCrid()
		item.DeepLink = bidItem.GetDeeplink()
		item.MarketURL = bidItem.GetMarketUrl()

		if bidItem.GetAdmobject() == nil {
			continue
		}
		respNative := bidItem.GetAdmobject().GetNative()
		// title

		for _, asseresItem := range respNative.GetAssets() {
			if len(asseresItem.GetTitle().GetText()) > 0 {
				item.Title = asseresItem.GetTitle().GetText()
			}
			if asseresItem.GetImg() == nil && asseresItem.GetVideo() == nil {
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
			if asseresItem.GetImg() != nil {
				var imgList []models.MHRespImage

				var img models.MHRespImage
				img.URL = asseresItem.GetImg().GetUrl()
				img.Width = int(asseresItem.GetImg().GetW())
				img.Height = int(asseresItem.GetImg().GetH())
				imgList = append(imgList, img)

				item.Image = imgList
				item.CrtType = 11
			} else {
				if asseresItem.GetVideo() != nil {
					var video models.MHRespVideo
					video.CoverURL = asseresItem.GetVideo().GetCover()
					video.VideoURL = asseresItem.GetVideo().GetUrl()
					video.Duration = int(asseresItem.GetVideo().GetDuration()) * 1000
					item.Video = &video
					item.CrtType = 20
				}
			}
		}

		if item.Video == nil && item.Image == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		if bidItem.GetApp() != nil {
			item.AppName = bidItem.GetApp().GetName()
			item.PackageName = bidItem.GetApp().GetPackage()
			item.AppVersion = bidItem.GetApp().GetVersion()
			item.Publisher = bidItem.GetApp().GetPublisher()
			item.PrivacyLink = bidItem.GetApp().GetPrivacyLink()
			item.PermissionURL = bidItem.GetApp().GetPermissionLink()
			item.AppInfoURL = bidItem.GetApp().GetAppDesc()
			item.PackageSize = int64(bidItem.GetApp().GetSize())
			item.IconURL = bidItem.GetApp().GetIcon()
		}

		tmpDownX := ""
		tmpDownY := ""
		tmpUpX := ""
		tmpUpY := ""

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				} else {
					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		if len(bidItem.GetDeeplink()) > 0 {
			var convTrackArray []models.MHRespConvTracks
			var convTracks models.MHRespConvTracks
			// deeplink track
			var respListItemSuccDeepLinkArray []string
			if len(bidItem.GetEvents().GetDeeplinkUrls()) > 0 {
				respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, bidItem.GetEvents().GetDeeplinkUrls()...)
			}
			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			mhDPParams.Add("log", bigdataParams)
			respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
			convTracks.ConvType = 10
			convTracks.ConvURLS = respListItemSuccDeepLinkArray
			convTrackArray = append(convTrackArray, convTracks)

			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string
				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
				if len(bidItem.GetEvents().GetDeeplinkFurls()) > 0 {
					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, bidItem.GetEvents().GetDeeplinkFurls()...)
				}

				var deeplinkFailedConvTrack models.MHRespConvTracks
				deeplinkFailedConvTrack.ConvType = 11
				deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
				convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
			}

			item.ConvTracks = convTrackArray
		}

		randPRValue := 95 + rand.Intn(4)
		macroPrice := uint64(price * randPRValue / 100)
		macroPriceStr, _ := EncryptPrice([]byte(platformPos.PlatformAppPriceEncrypt), []byte(platformPos.PlatformAppPriceEncrypt2), macroPrice)

		// win notice url
		qunaWinNoticeURL := bidItem.GetNurl()

		if len(qunaWinNoticeURL) > 0 {
			qunaWinNoticeURL = strings.Replace(qunaWinNoticeURL, "__WINPRICE__", string(macroPriceStr), -1)

			go func() {
				defer func() {
					if err := recover(); err != nil {
						fmt.Println("quna nurl panic:", err)
					}
				}()
				curlQunaNurl(qunaWinNoticeURL)
			}()
		}

		// impression_link
		var respListItemImpArray []string
		mhImpParams := url.Values{}
		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		if len(bidItem.GetEvents().GetImpUrls()) > 0 {
			for _, impUrl := range bidItem.GetEvents().GetImpUrls() {
				impUrl = strings.Replace(impUrl, "__WINPRICE__", string(macroPriceStr), -1)
				impUrl = strings.Replace(impUrl, "__PWIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
				impUrl = strings.Replace(impUrl, "__PHEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
				if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
					if mhReq.Device.XDPI > 0 {
						tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
						tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

						impUrl = strings.Replace(impUrl, "__PWIDTH__", utils.ConvertIntToString(tmpWidth), -1)
						impUrl = strings.Replace(impUrl, "__PHEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
					} else {
						impUrl = strings.Replace(impUrl, "__PWIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						impUrl = strings.Replace(impUrl, "__PHEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
				} else {
					impUrl = strings.Replace(impUrl, "__PWIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
					impUrl = strings.Replace(impUrl, "__PHEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
				}
				impUrl = strings.Replace(impUrl, "__DOWN_X__", tmpDownX, -1)
				impUrl = strings.Replace(impUrl, "__DOWN_Y__", tmpDownY, -1)
				impUrl = strings.Replace(impUrl, "__UP_X__", tmpUpX, -1)
				impUrl = strings.Replace(impUrl, "__UP_Y__", tmpUpY, -1)

				respListItemImpArray = append(respListItemImpArray, impUrl)
			}
		}
		item.ImpressionLink = respListItemImpArray

		// click_link
		var respListItemClickArray []string
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)
		respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
		if len(bidItem.GetEvents().GetClickUrls()) > 0 {
			for _, clkUrl := range bidItem.GetEvents().GetClickUrls() {
				clkUrl = strings.Replace(clkUrl, "__WINPRICE__", string(macroPriceStr), -1)
				clkUrl = strings.Replace(clkUrl, "__PWIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
				clkUrl = strings.Replace(clkUrl, "__PHEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
				if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
					if mhReq.Device.XDPI > 0 {
						tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
						tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

						clkUrl = strings.Replace(clkUrl, "__PWIDTH__", utils.ConvertIntToString(tmpWidth), -1)
						clkUrl = strings.Replace(clkUrl, "__PHEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
					} else {
						clkUrl = strings.Replace(clkUrl, "__PWIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkUrl = strings.Replace(clkUrl, "__PHEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
				} else {
					clkUrl = strings.Replace(clkUrl, "__PWIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
					clkUrl = strings.Replace(clkUrl, "__PHEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
				}
				if platformPos.PlatformPosIsReportSLD == 1 {
					clkUrl = strings.Replace(clkUrl, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
				}
				clkUrl = strings.Replace(clkUrl, "__DOWN_X__", tmpDownX, -1)
				clkUrl = strings.Replace(clkUrl, "__DOWN_Y__", tmpDownY, -1)
				clkUrl = strings.Replace(clkUrl, "__UP_X__", tmpUpX, -1)
				clkUrl = strings.Replace(clkUrl, "__UP_Y__", tmpUpY, -1)

				respListItemClickArray = append(respListItemClickArray, clkUrl)
			}
		}
		item.ClickLink = respListItemClickArray

		// win notice url
		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}
		item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		item.MaterialDirection = platformPos.PlatformPosDirection
		item.PEcpm = price
		list = append(list, item)
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	respQuna := models.MHUpResp{}
	respQuna.RespData = &mhResp
	respQuna.Extra = bigdataExtra

	return &respQuna

}

func curlQunaNurl(nurl string) {
	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	resp, err := client.Do(requestGet)
	if err != nil {
		return
	}

	defer resp.Body.Close()
}

func EncryptPrice(ecKey, icKey []byte, price uint64) ([]byte, error) {
	if len(icKey) == 0 || len(ecKey) == 0 {
		return nil, nil
	}

	iv := []byte(fmt.Sprintf("%d", time.Now().UnixNano()/1000))

	h := hmac.New(sha1.New, ecKey)
	h.Write(iv)
	pad := h.Sum(nil)[:8]

	p := make([]byte, 8)
	binary.BigEndian.PutUint64(p, price)
	encPrice := safeXORByte(pad, p)

	h = hmac.New(sha1.New, icKey)
	h.Write(p)
	h.Write(iv)
	sig := h.Sum(nil)[:4]

	b := make([]byte, 0, len(iv)+len(encPrice)+len(sig))
	buf := bytes.NewBuffer(b)
	buf.Write(iv)
	buf.Write(encPrice)
	buf.Write(sig)
	n := base64.RawURLEncoding.EncodedLen(len(buf.Bytes()))
	msg := make([]byte, n, n)
	base64.RawURLEncoding.Encode(msg, buf.Bytes())

	return msg, nil
}

func safeXORByte(a, b []byte) []byte {
	n := len(a)
	if len(b) < n {
		n = len(b)
	}

	if n == 0 {
		return nil
	}

	dst := make([]byte, n)

	for i := 0; i < n; i++ {
		dst[i] = a[i] ^ b[i]
	}

	return dst
}
