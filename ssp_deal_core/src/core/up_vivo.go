package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromVIVO ...
func GetFromVIVO(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from vivo")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// 开屏测试
	// platformPos.PlatformAppID = "03ad601d82b64f8e9b4939259b47c26e"
	// platformPos.PlatformPosID = "127465683bfc44bba5b0e875972bd9dd"
	// platformPos.PlatformPosType = 2
	// platformPos.PlatformAppBundle = "com.vivo.adnet.demo.app"
	// platformPos.PlatformPosIsDebug = 1
	// 激励视频测试
	// platformPos.PlatformAppID = "03ad601d82b64f8e9b4939259b47c26e"
	// platformPos.PlatformPosID = "2ac9eac8200b4388930ecc17ed9c8cba"
	// platformPos.PlatformPosType = 11
	// platformPos.PlatformAppBundle = "com.vivo.adnet.demo.app"
	// platformPos.PlatformPosIsDebug = 1

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["mediaId"] = platformPos.PlatformAppID
	reqAppInfoMap["appPackage"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	reqAppInfoMap["appVersion"] = 1 // ?????? platformPos.PlatformAppVersion

	reqPosInfoMap := map[string]interface{}{}
	reqPosInfoMap["positionId"] = platformPos.PlatformPosID
	// 1. Banner, 2. 插屏, 4. 开屏, 8. 原生, 64. 激励视频
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="8">原生模版</el-radio>
	// <el-radio label="7">原生2.0</el-radio>
	// <el-radio label="5">原生视频</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="6">信息流</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">视频内容联盟</el-radio>
	// <el-radio label="12">视频内容联盟组件</el-radio>
	if platformPos.PlatformPosType == 1 {
		reqPosInfoMap["displayType"] = 3
	} else if platformPos.PlatformPosType == 3 {
		reqPosInfoMap["displayType"] = 4
	} else if platformPos.PlatformPosType == 2 {
		reqPosInfoMap["displayType"] = 2
	} else if platformPos.PlatformPosType == 4 {
		reqPosInfoMap["displayType"] = 5
	} else if platformPos.PlatformPosType == 11 {
		reqPosInfoMap["displayType"] = 9
	}
	reqPosInfoMap["width"] = platformPos.PlatformPosWidth
	reqPosInfoMap["height"] = platformPos.PlatformPosHeight

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	if localPosFloorPrice > 0 {
		reqPosInfoMap["bidFloor"] = localPosFloorPrice
	}

	reqDeviceInfoMap := map[string]interface{}{}

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true

				reqDeviceInfoMap["imei"] = mhReq.Device.Imei
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true

				reqDeviceInfoMap["didMd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	reqDeviceInfoMap["androidId"] = mhReq.Device.AndroidID

			// 	extraReportParams = extraReportParams + ",android_id"
			// }
			// if len(mhReq.Device.Oaid) > 0 {
			// 	reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid

			// 	extraReportParams = extraReportParams + ",oaid"
			// }
			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
			}
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	reqDeviceInfoMap["androidId"] = mhReq.Device.AndroidID

			// 	extraReportParams = extraReportParams + ",android_id"
			// }
			if len(mhReq.Device.Oaid) > 0 {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		fmt.Println("get from vivo error")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(mhReq.Device.Mac) > 0 {
		reqDeviceInfoMap["mac"] = mhReq.Device.Mac
	}

	// is debug
	if platformPos.PlatformPosIsDebug == 1 {
		fmt.Println("get from vivo debug")
		mhReq.Device.Manufacturer = "VIVO"
	}

	// if strings.Contains(mhReq.Device.Manufacturer, "VIVO") || strings.Contains(mhReq.Device.Manufacturer, "vivo") {
	// } else {
	// 	bigdataExtra.InternalCode = 900110
	// 	bigdataExtra.ExternalCode = 102006

	// 	return MhUpErrorRespMap("", bigdataExtra)
	// }
	// reqDeviceInfoMap["make"] = "vivo"

	reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer

	if len(mhReq.Device.Model) > 0 {
		reqDeviceInfoMap["model"] = mhReq.Device.Model
	}
	if len(mhReq.Device.OsVersion) > 0 {
		reqDeviceInfoMap["an"] = mhReq.Device.OsVersion

		if mhReq.Device.OsVersion == "5" {
			reqDeviceInfoMap["av"] = 21
		} else if mhReq.Device.OsVersion == "5.1" {
			reqDeviceInfoMap["av"] = 22
		} else if mhReq.Device.OsVersion == "6" {
			reqDeviceInfoMap["av"] = 23
		} else if mhReq.Device.OsVersion == "7" {
			reqDeviceInfoMap["av"] = 24
		} else if mhReq.Device.OsVersion == "7.1" {
			reqDeviceInfoMap["av"] = 25
		} else if mhReq.Device.OsVersion == "8" {
			reqDeviceInfoMap["av"] = 26
		} else if mhReq.Device.OsVersion == "8.1" {
			reqDeviceInfoMap["av"] = 27
		} else if mhReq.Device.OsVersion == "9" {
			reqDeviceInfoMap["av"] = 28
		} else if mhReq.Device.OsVersion == "10" {
			reqDeviceInfoMap["av"] = 29
		} else if mhReq.Device.OsVersion == "11" {
			reqDeviceInfoMap["av"] = 30
		} else if mhReq.Device.OsVersion == "12" {
			reqDeviceInfoMap["av"] = 31
		} else {
			bigdataExtra.InternalCode = 900110
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else {
		bigdataExtra.InternalCode = 900110
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	if mhReq.Device.ScreenWidth > 0 {
		reqDeviceInfoMap["screenWidth"] = mhReq.Device.ScreenWidth
	} else {
		reqDeviceInfoMap["screenWidth"] = 1080
	}
	if mhReq.Device.ScreenHeight > 0 {
		reqDeviceInfoMap["screenHeight"] = mhReq.Device.ScreenHeight
	} else {
		reqDeviceInfoMap["screenHeight"] = 1920
	}

	// ip
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	// ua
	reqDeviceInfoMap["ua"] = destConfigUA

	// elapseTime
	// reqDeviceInfoMap["elapseTime"] = 100 // ??????

	// network info
	reqNetWorkInfoMap := map[string]interface{}{}

	if mhReq.Network.ConnectType == 0 {
		// 未知
		reqNetWorkInfoMap["connectType"] = 0
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		reqNetWorkInfoMap["connectType"] = 100
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		reqNetWorkInfoMap["connectType"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		reqNetWorkInfoMap["connectType"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		reqNetWorkInfoMap["connectType"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		reqNetWorkInfoMap["connectType"] = 5
	}

	reqNetWorkInfoMap["carrier"] = mhReq.Network.Carrier

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imei")
					delete(reqDeviceInfoMap, "didMd5")
					delete(reqDeviceInfoMap, "oaid")

					if len(didRedisData.OsVersion) > 0 {
						reqDeviceInfoMap["an"] = didRedisData.OsVersion
						if didRedisData.OsVersion == "5" {
							reqDeviceInfoMap["av"] = 21
						} else if didRedisData.OsVersion == "5.1" {
							reqDeviceInfoMap["av"] = 22
						} else if didRedisData.OsVersion == "6" {
							reqDeviceInfoMap["av"] = 23
						} else if didRedisData.OsVersion == "7" {
							reqDeviceInfoMap["av"] = 24
						} else if didRedisData.OsVersion == "7.1" {
							reqDeviceInfoMap["av"] = 25
						} else if didRedisData.OsVersion == "8" {
							reqDeviceInfoMap["av"] = 26
						} else if didRedisData.OsVersion == "8.1" {
							reqDeviceInfoMap["av"] = 27
						} else if didRedisData.OsVersion == "9" {
							reqDeviceInfoMap["av"] = 28
						} else if didRedisData.OsVersion == "10" {
							reqDeviceInfoMap["av"] = 29
						} else if didRedisData.OsVersion == "11" {
							reqDeviceInfoMap["av"] = 30
						} else if didRedisData.OsVersion == "12" {
							reqDeviceInfoMap["av"] = 31
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer

					// isOKReplaceManufacturer := false
					// for _, manufactureConfigItem := range manufactureConfigArray {
					// 	if strings.ToLower(didRedisData.Manufacturer) == manufactureConfigItem {
					// 		isOKReplaceManufacturer = true
					// 	}
					// }
					// if isOKReplaceManufacturer {
					// } else {
					// 	bigdataExtra.InternalCode = 900011
					// 	bigdataExtra.ExternalCode = 102006

					// 	return MhUpErrorRespMap("", bigdataExtra)
					// }
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imei"] = didRedisData.Imei
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["didMd5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// apiVc appStoreVc
	postData := map[string]interface{}{
		"apiVersion":      "1.0",
		"sysVersion":      "unknow",
		"appstoreVersion": 1221,
		"media":           reqAppInfoMap,
		"postion":         reqPosInfoMap,
		"device":          reqDeviceInfoMap,
		"network":         reqNetWorkInfoMap,
		// "timeout":         timeout,
	}
	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("vivo req: " + string(jsonData))

	// return MhUpErrorRespMap("", bigdataExtra)

	vivoUPURL := config.UpVIVOURL

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", vivoUPURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destConfigUA)

	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Println("vivo resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	vivoRespStu := VIVORespStu{}
	json.Unmarshal([]byte(bodyContent), &vivoRespStu)

	if vivoRespStu.Code != 1 {
		bigdataExtra.UpRespCode = int(vivoRespStu.Code)

		if vivoRespStu.Code == 4 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		} else {
			bigdataExtra.InternalCode = 900102
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if len(vivoRespStu.Data) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	tmpRespCount := 1
	if mhReq.Pos.AdCount > 0 {
		tmpRespCount = mhReq.Pos.AdCount
	}
	var respListArray []map[string]interface{}

	vivoRespDataItemList := vivoRespStu.Data

	if len(vivoRespDataItemList) > 1 {
		sort.Sort(VIVOEcpmSort(vivoRespDataItemList))
	}

	for _, adInfoItem := range vivoRespDataItemList {

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// ecpm
		vivoEcpm := adInfoItem.Ecpm

		respTmpPrice = respTmpPrice + vivoEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if vivoEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			vivoEcpm = platformPos.PlatformPosEcpm
		}

		vivoLossNoticeURL := getVIVOPriceFailedURL(adInfoItem.WinURL, platformPos, vivoEcpm)
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFloorPrice > 0 {
				if localPosFinalPrice > vivoEcpm {
					respTmpInternalCode = 900104
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
					continue
				}
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(vivoEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		} else if len(adInfoItem.AppName) > 0 {
			respListItemMap["title"] = adInfoItem.AppName
		}

		// description
		if len(adInfoItem.Desc) > 0 {
			respListItemMap["description"] = adInfoItem.Desc
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"
		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// crid
		respListItemMap["crid"] = adInfoItem.AdID

		// package name
		if len(adInfoItem.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoItem.PackageName

			// android
			if mhReq.Device.Os == "android" {
				if len(adInfoItem.AppDescription) > 0 {
					respListItemMap["appinfo"] = adInfoItem.AppDescription
				} else {
					appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, adInfoItem.AppName, adInfoItem.PackageName)
					if appInfoFromRedisErr == nil {
						respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
						respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
					}
				}
			}
		}

		// deep link
		if len(adInfoItem.DeepLink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.DeepLink

			// 字段 appstore_package_name
			if strings.HasPrefix(adInfoItem.DeepLink, "vivomarket://details?") {
				respListItemMap["appstore_package_name"] = "com.bbk.appstore"
			}
		}

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// icon_url
		if len(adInfoItem.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.DeepLink)
		}

		isVideo := false

		if adInfoItem.AdType == 9 || (adInfoItem.AdType == 5 && len(adInfoItem.Video.URL) > 0) {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Video.Duration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Video.Duration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
					continue
				}
				respListVideoItemMap["duration"] = adInfoItem.Video.Duration * 1000
			}
			respListVideoItemMap["width"] = adInfoItem.Video.Width
			respListVideoItemMap["height"] = adInfoItem.Video.Height

			if adInfoItem.Video.Width > 0 && adInfoItem.Video.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Video.Width, adInfoItem.Video.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
					continue
				}
			}

			respListVideoItemMap["video_url"] = adInfoItem.Video.URL

			// cover_url
			if len(adInfoItem.Video.CoverURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Video.CoverURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// 播放开始, 播放结束 track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, adTrackInfoItem := range adInfoItem.TrackingList {
				if adTrackInfoItem.TrackingEvent == 5 {
					for _, adTrackInfoVideoStartURLItem := range adTrackInfoItem.TrackUrls {
						videoItem := adTrackInfoVideoStartURLItem
						videoItem = strings.Replace(videoItem, "__IP__", mhReq.Device.IP, -1)
						videoItem = strings.Replace(videoItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoItem)
					}
				}
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, adTrackInfoItem := range adInfoItem.TrackingList {
				if adTrackInfoItem.TrackingEvent == 9 {
					for _, adTrackInfoVideoStartURLItem := range adTrackInfoItem.TrackUrls {
						videoItem := adTrackInfoVideoStartURLItem
						videoItem = strings.Replace(videoItem, "__IP__", mhReq.Device.IP, -1)
						videoItem = strings.Replace(videoItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoItem)
					}
				}
			}

			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if adInfoItem.AdType == 2 || adInfoItem.AdType == 3 || adInfoItem.AdType == 4 || adInfoItem.AdType == 5 {
			isVideo = false

			respListImageItemMap := map[string]interface{}{}
			respListImageItemMap["url"] = adInfoItem.Image.URL
			respListImageItemMap["width"] = platformPos.PlatformPosWidth
			respListImageItemMap["height"] = platformPos.PlatformPosHeight

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		} else {

			curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
			continue
		}

		// interact_type ad_url
		if len(adInfoItem.TargetURL) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.TargetURL
			respListItemMap["landpage_url"] = adInfoItem.TargetURL

		} else {

			if len(adInfoItem.DownloadURL) > 0 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = adInfoItem.DownloadURL
				respListItemMap["download_url"] = adInfoItem.DownloadURL

				if len(adInfoItem.TargetURL) > 0 {
					respListItemMap["landpage_url"] = adInfoItem.TargetURL
				}
			} else if len(adInfoItem.TargetURL) > 0 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = adInfoItem.TargetURL
				respListItemMap["landpage_url"] = adInfoItem.TargetURL
			} else {
				curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		if len(adInfoItem.AppDeveloper) > 0 {
			respListItemMap["publisher"] = adInfoItem.AppDeveloper
		}
		if len(adInfoItem.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.AppVersion
		}
		if len(adInfoItem.AppPrivacyURL) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.AppPrivacyURL
		}
		if len(adInfoItem.AppPermission) > 0 {
			permissionStr := ""
			for _, permissionItem := range adInfoItem.AppPermission {
				permissionStr = permissionStr + permissionItem.Title + "\n"
			}
			respListItemMap["permission"] = permissionStr
		}
		if adInfoItem.AppSize > 0 {
			respListItemMap["package_size"] = adInfoItem.AppSize * 1024
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, vivoEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		vivoWinNoticeURL := getVIVOPriceOKURL(adInfoItem.WinURL, platformPos, vivoEcpm)
		if len(vivoWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, vivoWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// impression_link track url
		for _, adTrackInfoItem := range adInfoItem.TrackingList {
			if adTrackInfoItem.TrackingEvent == 2 {
				for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
					impItem := adTrackInfoImpURLItem
					impItem = strings.Replace(impItem, "__IP__", mhReq.Device.IP, -1)
					impItem = strings.Replace(impItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
					respListItemImpArray = append(respListItemImpArray, impItem)
				}
			}
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link  track url
		for _, adTrackInfoItem := range adInfoItem.TrackingList {
			if adTrackInfoItem.TrackingEvent == 3 {
				for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
					clkItem := adTrackInfoImpURLItem
					clkItem = strings.Replace(clkItem, "__IP__", mhReq.Device.IP, -1)
					clkItem = strings.Replace(clkItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
					clkItem = strings.Replace(clkItem, "__CLICKAREA__", "1", -1) // ??????
					clkItem = strings.Replace(clkItem, "__X__", tmpDownX, -1)
					clkItem = strings.Replace(clkItem, "__Y__", tmpDownY, -1)

					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							clkItem = clkItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							clkItem = clkItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}

					respListItemClkArray = append(respListItemClkArray, clkItem)
				}
			}
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(vivoLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, vivoLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// conv_tracks deeplink
		var respListItemDeepLinkArray []string

		for _, adTrackInfoItem := range adInfoItem.TrackingList {
			if adTrackInfoItem.TrackingEvent == 21 {
				for _, adTrackInfoTrackURLItem := range adTrackInfoItem.TrackUrls {
					trackItem := adTrackInfoTrackURLItem
					trackItem = strings.Replace(trackItem, "__IP__", mhReq.Device.IP, -1)
					trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
					trackItem = strings.Replace(trackItem, "__CLICKAREA__", "1", -1) // ??????
					trackItem = strings.Replace(trackItem, "__X__", tmpDownX, -1)
					trackItem = strings.Replace(trackItem, "__Y__", tmpDownY, -1)
					trackItem = strings.Replace(trackItem, "__DP_RESULT__", "0", -1)

					respListItemDeepLinkArray = append(respListItemDeepLinkArray, trackItem)
				}
			}
		}
		if len(respListItemDeepLinkArray) > 0 {
			// maplehaze dp track
			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, adTrackInfoItem := range adInfoItem.TrackingList {
					if adTrackInfoItem.TrackingEvent == 21 {
						for _, adTrackInfoTrackURLItem := range adTrackInfoItem.TrackUrls {
							trackItem := adTrackInfoTrackURLItem
							trackItem = strings.Replace(trackItem, "__IP__", mhReq.Device.IP, -1)
							trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
							trackItem = strings.Replace(trackItem, "__CLICKAREA__", "1", -1) // ??????
							trackItem = strings.Replace(trackItem, "__X__", tmpDownX, -1)
							trackItem = strings.Replace(trackItem, "__Y__", tmpDownY, -1)
							trackItem = strings.Replace(trackItem, "__DP_RESULT__", "1", -1)
							trackItem = strings.Replace(trackItem, "__DP_REASON__", "3", -1)

							respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, trackItem)
						}
					}
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		if len(respListArray) >= tmpRespCount {
			curlVIVOPriceFailedURL(vivoLossNoticeURL, &bigdataExtra)
			continue
		}

		respListItemMap["p_ecpm"] = vivoEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// vivo resp
	respVivo := models.MHUpResp{}
	respVivo.RespData = &mhResp
	respVivo.Extra = bigdataExtra

	return &respVivo
}

func curlVIVOPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {

	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("vivo win url panic:", err)
				}
			}()
			client := &http.Client{Timeout: 1000 * time.Millisecond}
			requestGet, _ := http.NewRequest("GET", lossNoticeURL, nil)

			requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

			// fmt.Println(requestGet.URL.String())
			// fmt.Println("vivo nurl req: " + requestGet.URL.String())

			resp, err := client.Do(requestGet)
			if err != nil {
				// fmt.Printf("get request failed, err:[%s]", err.Error())
				return
			}

			defer resp.Body.Close()

			// bodyContent, err := io.ReadAll(resp.Body)
			// fmt.Println("vivo nurl  resp: " + string(bodyContent))
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

type VIVOEcpmSort []VIVORespDataStu

func (s VIVOEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s VIVOEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s VIVOEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}

// VIVORespStu ...
type VIVORespStu struct {
	Code int32             `json:"code"`
	Msg  string            `json:"msg"`
	Data []VIVORespDataStu `json:"data"`
}

// VIVORespDataStu ...
type VIVORespDataStu struct {
	AdID           string                     `json:"adId"`
	AdType         int                        `json:"adType"`
	MaterialType   int                        `json:"materialType"`
	TargetURL      string                     `json:"targetUrl"`
	DownloadURL    string                     `json:"downloadUrl"`
	DeepLink       string                     `json:"deeplink"`
	Title          string                     `json:"title"`
	Desc           string                     `json:"description"`
	SourceAvatar   string                     `json:"sourceAvatar"`
	Image          VIVORespImageStu           `json:"image"`
	ImageList      []VIVORespImageStu         `json:"imageList"`
	Video          VIVORespVideoStu           `json:"video"`
	PackageName    string                     `json:"appPackage"`
	IconURL        string                     `json:"appIconUrl"`
	AppName        string                     `json:"appName"`
	AppSize        int64                      `json:"appSize"`
	AppDeveloper   string                     `json:"appDeveloper"`
	AppPermission  []VIVORespAppPermissionStu `json:"appPermission"`
	AppPrivacyURL  string                     `json:"appPrivacyPolicyUrl"`
	AppVersion     string                     `json:"appVersionName"`
	AppDescription string                     `json:"appDescription"`
	TrackingList   []VIVORespTrackingListStu  `json:"trackingList"`
	BidMode        int                        `json:"bidMode"`
	Ecpm           int                        `json:"price"`
	WinURL         string                     `json:"noticeUrl"`
	PositionID     string                     `json:"positionId"`
	Token          string                     `json:"token"`
}

// VIVORespImageStu ...
type VIVORespImageStu struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

// VIVORespVideoStu ...
type VIVORespVideoStu struct {
	URL      string `json:"videoUrl"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Duration int    `json:"duration"`
	Size     int    `json:"size"`
	CoverURL string `json:"previewImgUrl"`
}

// VIVORespTrackingListStu ...
type VIVORespTrackingListStu struct {
	TrackingEvent int      `json:"trackingEvent"`
	TrackUrls     []string `json:"trackUrls"`
}

// VIVORespAppPermissionStu ...
type VIVORespAppPermissionStu struct {
	PermissionType string `json:"permissionType"`
	Description    string `json:"describe"`
	Title          string `json:"title"`
}

// curl price failed
func getVIVOPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, vivoEcpm int) string {
	if vivoEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsReportLoss == 1 {
		randPRValue := 100
		tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
		tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
		if tmp1 <= tmp2 {
			randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
		} else {
			return ""
		}

		macroPrice := utils.ConvertIntToString(int(vivoEcpm * randPRValue / 100))

		cipherText := utils.RSAEncrypt([]byte(macroPrice), config.GDTPublicKeyPemPath)
		macroPrice = utils.Base64URLEncode(cipherText)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__WIN_PRICE__", macroPrice, -1)
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_LOSS__", "1", -1)

		return tmpLossNoticeURL
	}

	return ""
}

// curl price ok
func getVIVOPriceOKURL(winNoticeURL string, platformPos *models.PlatformPosStu, vivoEcpm int) string {
	if len(winNoticeURL) > 0 && vivoEcpm > 0 && platformPos.PlatformAppIsReportWin == 1 {
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}

		macroPrice := utils.ConvertIntToString(int(vivoEcpm * randPRValue / 100))

		tmpWinNoticeURL := winNoticeURL
		tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "__WIN_PRICE__", macroPrice, -1)

		return tmpWinNoticeURL
	}

	return ""
}
