package core

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	uuid "github.com/satori/go.uuid"
)

// GetFromupYueDong 数字悦动 adx
func GetFromupYueDong(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := mhReq.Device.CPUNum

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType int
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = 2
	case 2:
		connectionType = 4
	case 3:
		connectionType = 5
	case 4, 5, 6:
		connectionType = 6
	case 7:
		connectionType = 7
	}

	var style int
	switch platformPos.PlatformPosType {
	case 2:
		style = 1
	case 3:
		style = 2
	case 4:
		style = 4
	case 9:
		style = 6
	case 11:
		style = 5
	}

	var os int
	switch mhReq.Device.Os {
	case "android":
		os = 1
	case "ios":
		os = 2
	}

	var impList []*YueDongRequestImpObject
	imp := &YueDongRequestImpObject{
		Id:       uuid.NewV4().String(),
		Tagid:    platformPos.PlatformPosID,
		Style:    style,
		Secure:   1,
		Bidfloor: categoryInfo.FloorPrice,
		Width:    platformPos.PlatformPosWidth,
		Height:   platformPos.PlatformPosHeight,
		DeepLink: 1,
		Ul:       1,
	}
	impList = append(impList, imp)

	request := YueDongRequestObject{
		Id:    bigdataUID,
		Token: "9357ff11-4ea8-5a69-9549-3c29a5be8c5c",
		App: &YueDongRequestAppObject{
			Id:     platformPos.PlatformAppID,
			Name:   platformPos.PlatformAppName,
			Ver:    platformPos.PlatformAppVersion,
			Bundle: GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		},
		Imp: impList,
		Device: &YueDongRequestDeviceObject{
			Os:             os,
			Connectiontype: connectionType,
			Devicetype:     1,
			Carrier:        mhReq.Network.Carrier,
			H:              mhReq.Device.ScreenWidth,
			W:              mhReq.Device.ScreenHeight,
			Osv:            mhReq.Device.OsVersion,
			Ip:             mhReq.Device.IP,
			Make:           mhReq.Device.Manufacturer,
			Model:          mhReq.Device.Model,
			Mac:            mhReq.Device.Mac,
			BootMark:       mhReq.Device.BootMark,
			UpdateMark:     mhReq.Device.UpdateMark,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Device.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Device.Did = mhReq.Device.Imei
				request.Device.Didmd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.Device.Didmd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Device.Oaid = mhReq.Device.Oaid
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true
				request.Device.OaidMd5 = strings.ToLower(mhReq.Device.OaidMd5)
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					request.Device.AndroidId = mhReq.Device.AndroidID
					if len(mhReq.Device.AndroidIDMd5) > 0 {
						request.Device.AndroidIdMd5 = mhReq.Device.AndroidIDMd5
					}
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Device.Idfa = mhReq.Device.Idfa
				request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			} else {
				for _, item := range mhReq.Device.CAIDMulti {
					request.Device.Caid = item.CAID
					request.Device.CaidVersion = item.CAIDVersion

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				request.Device.UpdateTime = tmpSystemUpdateSec
				request.Device.BirthTime = tmpDeviceStartSec
				request.Device.SysCompilingTime = tmpDeviceBirthSec
				request.Device.DeviceNameMd5 = tmpDeviceNameMd5
				request.Device.HardwareMachine = tmpHardwareMachine
				request.Device.PhysicalMemory = tmpPhysicalMemoryByte
				request.Device.HardDiskSize = tmpHarddiskSizeByte
				request.Device.Language = tmpLanguage
				request.Device.Country = tmpCountry
				request.Device.TimeZone = tmpTimeZone
				num, _ := strconv.Atoi(tmpCPUNum)
				request.Device.CpuNum = num
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.CaidVersion = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					request.Device.UpdateTime = tmpSystemUpdateSec
					request.Device.BirthTime = tmpDeviceStartSec
					request.Device.SysCompilingTime = tmpDeviceBirthSec
					request.Device.DeviceNameMd5 = tmpDeviceNameMd5
					request.Device.HardwareMachine = tmpHardwareMachine
					request.Device.PhysicalMemory = tmpPhysicalMemoryByte
					request.Device.HardDiskSize = tmpHarddiskSizeByte
					request.Device.Language = tmpLanguage
					request.Device.Country = tmpCountry
					request.Device.TimeZone = tmpTimeZone
					num, _ := strconv.Atoi(tmpCPUNum)
					request.Device.CpuNum = num
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.CaidVersion = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.CaidVersion = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					request.Device.UpdateTime = tmpSystemUpdateSec
					request.Device.BirthTime = tmpDeviceStartSec
					request.Device.SysCompilingTime = tmpDeviceBirthSec
					request.Device.DeviceNameMd5 = tmpDeviceNameMd5
					request.Device.HardwareMachine = tmpHardwareMachine
					request.Device.PhysicalMemory = tmpPhysicalMemoryByte
					request.Device.HardDiskSize = tmpHarddiskSizeByte
					request.Device.Language = tmpLanguage
					request.Device.Country = tmpCountry
					request.Device.TimeZone = tmpTimeZone
					num, _ := strconv.Atoi(tmpCPUNum)
					request.Device.CpuNum = num
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.Device.Os = 2
					case "android":
						request.Device.Os = 1
					}
					request.Device.Osv = didRedisData.OsVersion
					request.Device.Model = didRedisData.Model
					request.Device.Make = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Device.Did = didRedisData.Imei
							request.Device.Didmd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.Device.Didmd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Device.Oaid = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								request.Device.AndroidId = didRedisData.AndroidID
								if len(didRedisData.AndroidIDMd5) > 0 {
									request.Device.AndroidIdMd5 = didRedisData.AndroidIDMd5
								}
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Device.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.Device.Os = 2
						case "android":
							request.Device.Os = 1
						}
						request.Device.Osv = didRedisData.OsVersion
						request.Device.Model = didRedisData.Model
						request.Device.Make = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Device.Idfa = didRedisData.Idfa
								request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									request.Device.Caid = item.CAID
									request.Device.CaidVersion = item.CAIDVersion

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
								}
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

							if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								request.Device.UpdateTime = tmpSystemUpdateSec
								request.Device.BirthTime = tmpDeviceStartSec
								request.Device.SysCompilingTime = tmpDeviceBirthSec
								request.Device.DeviceNameMd5 = tmpDeviceNameMd5
								request.Device.HardwareMachine = tmpHardwareMachine
								request.Device.PhysicalMemory = tmpPhysicalMemoryByte
								request.Device.HardDiskSize = tmpHarddiskSizeByte
								request.Device.Language = tmpLanguage
								request.Device.Country = tmpCountry
								request.Device.TimeZone = tmpTimeZone
								num, _ := strconv.Atoi(tmpCPUNum)
								request.Device.CpuNum = num
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.CaidVersion = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									request.Device.UpdateTime = tmpSystemUpdateSec
									request.Device.BirthTime = tmpDeviceStartSec
									request.Device.SysCompilingTime = tmpDeviceBirthSec
									request.Device.DeviceNameMd5 = tmpDeviceNameMd5
									request.Device.HardwareMachine = tmpHardwareMachine
									request.Device.PhysicalMemory = tmpPhysicalMemoryByte
									request.Device.HardDiskSize = tmpHarddiskSizeByte
									request.Device.Language = tmpLanguage
									request.Device.Country = tmpCountry
									request.Device.TimeZone = tmpTimeZone
									num, _ := strconv.Atoi(tmpCPUNum)
									request.Device.CpuNum = num
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.CaidVersion = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.CaidVersion = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									request.Device.UpdateTime = tmpSystemUpdateSec
									request.Device.BirthTime = tmpDeviceStartSec
									request.Device.SysCompilingTime = tmpDeviceBirthSec
									request.Device.DeviceNameMd5 = tmpDeviceNameMd5
									request.Device.HardwareMachine = tmpHardwareMachine
									request.Device.PhysicalMemory = tmpPhysicalMemoryByte
									request.Device.HardDiskSize = tmpHarddiskSizeByte
									request.Device.Language = tmpLanguage
									request.Device.Country = tmpCountry
									request.Device.TimeZone = tmpTimeZone
									num, _ := strconv.Atoi(tmpCPUNum)
									request.Device.CpuNum = num
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Device.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	requestMarshal, _ := json.Marshal(request)
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(requestMarshal)
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
	}
	if err = gzipWriter.Close(); err != nil {
		fmt.Println(err)
	}
	write := buf.Bytes()

	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"
	httpHeader["Accept-Encoding"] = "gzip"
	httpHeader["Content-Encoding"] = "gzip"

	if platformPos.PlatformAppIsReportUa == 1 {
		httpHeader["User-Agent"] = mhReq.Device.Ua
	}
	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(bytes.NewReader(write)))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	ydRespStu := YueDongResponseObject{}
	_ = json.Unmarshal(bodyContent, &ydRespStu)

	// 返回数据
	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if ydRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = ydRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if ydRespStu.Seatbid == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	bid := ydRespStu.Seatbid.Bid
	for _, bidItem := range bid {
		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		price := bidItem.Price

		respTmpPrice = respTmpPrice + price

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
			price = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > price {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			item.Ecpm = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			item.Ecpm = localPos.LocalPosEcpm
		}

		maplehazeAdId := uuid.NewV4().String()

		randPRValue := 95 + rand.Intn(4)
		macroPrice := utils.ConvertIntToString(price * randPRValue / 100)
		macroPriceStr, _ := ydEncrypt([]byte(macroPrice), []byte(platformPos.PlatformAppPriceEncrypt))

		switch bidItem.Action {
		case 1, 2:
			item.InteractType = 0
			item.LandpageURL = bidItem.LandingUrl
			item.DeepLink = bidItem.TargetUrl
		case 3:
			item.InteractType = 1
			item.DownloadURL = bidItem.TargetUrl
		default:
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// adid
		item.AdID = maplehazeAdId
		item.ReqWidth = platformPos.PlatformPosWidth
		item.ReqHeight = platformPos.PlatformPosHeight
		item.Crid = bidItem.Adid
		item.AppName = bidItem.AppName
		item.PackageName = bidItem.PackageName
		item.AppVersion = bidItem.AppVersion
		item.Publisher = bidItem.Developer
		item.PrivacyLink = bidItem.PrivacyUrl
		item.PermissionURL = bidItem.PermissionUrl
		item.PackageSize = int64(bidItem.AppSize)

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				} else {
					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		// impression_link
		var respListItemImpArray []string
		mhImpParams := url.Values{}
		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		// click_link
		var respListItemClickArray []string
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)
		respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		if bidItem.Tracking != nil {
			if len(bidItem.Tracking.ClickTrackers) > 0 {
				for _, clkUrl := range bidItem.Tracking.ClickTrackers {
					clkUrl = strings.Replace(clkUrl, "_PRICE_", macroPriceStr, -1)
					clkUrl = strings.Replace(clkUrl, "_WIDTH_", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					clkUrl = strings.Replace(clkUrl, "_HEIGHT_", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							clkUrl = strings.Replace(clkUrl, "_WIDTH_", utils.ConvertIntToString(tmpWidth), -1)
							clkUrl = strings.Replace(clkUrl, "_HEIGHT_", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							clkUrl = strings.Replace(clkUrl, "_WIDTH_", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							clkUrl = strings.Replace(clkUrl, "_HEIGHT_", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						clkUrl = strings.Replace(clkUrl, "_WIDTH_", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkUrl = strings.Replace(clkUrl, "_HEIGHT_", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					if platformPos.PlatformPosIsReportSLD == 1 {
						clkUrl = strings.Replace(clkUrl, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					}
					clkUrl = strings.Replace(clkUrl, "_SBZMX_", tmpDownX, -1)
					clkUrl = strings.Replace(clkUrl, "_SBZMY_", tmpDownY, -1)
					clkUrl = strings.Replace(clkUrl, "_SBZCX_", tmpUpX, -1)
					clkUrl = strings.Replace(clkUrl, "_SBZCY_", tmpUpY, -1)

					respListItemClickArray = append(respListItemClickArray, clkUrl)
				}
			}

			if len(bidItem.Tracking.ImpTrackers) > 0 {
				for _, impUrl := range bidItem.Tracking.ImpTrackers {
					impUrl = strings.Replace(impUrl, "_PRICE_", macroPriceStr, -1)
					impUrl = strings.Replace(impUrl, "_WIDTH_", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					impUrl = strings.Replace(impUrl, "_HEIGHT_", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							impUrl = strings.Replace(impUrl, "_WIDTH_", utils.ConvertIntToString(tmpWidth), -1)
							impUrl = strings.Replace(impUrl, "_HEIGHT_", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							impUrl = strings.Replace(impUrl, "_WIDTH_", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							impUrl = strings.Replace(impUrl, "_HEIGHT_", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						impUrl = strings.Replace(impUrl, "_WIDTH_", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						impUrl = strings.Replace(impUrl, "_HEIGHT_", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					if platformPos.PlatformPosIsReportSLD == 1 {
						impUrl = strings.Replace(impUrl, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					}
					impUrl = strings.Replace(impUrl, "_SBZMX_", tmpDownX, -1)
					impUrl = strings.Replace(impUrl, "_SBZMY_", tmpDownY, -1)
					impUrl = strings.Replace(impUrl, "_SBZCX_", tmpUpX, -1)
					impUrl = strings.Replace(impUrl, "_SBZCY_", tmpUpY, -1)

					respListItemImpArray = append(respListItemImpArray, impUrl)
				}
			}

			if len(item.DeepLink) > 0 {
				var convTrackArray []models.MHRespConvTracks
				var convTracks models.MHRespConvTracks
				// deeplink track
				var respListItemSuccDeepLinkArray []string
				if len(bidItem.Tracking.DplkTrackers) > 0 {
					respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, bidItem.Tracking.DplkTrackers...)
				}
				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				mhDPParams.Add("log", bigdataParams)
				respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
				convTracks.ConvType = 10
				convTracks.ConvURLS = respListItemSuccDeepLinkArray
				convTrackArray = append(convTrackArray, convTracks)

				if platformPos.PlatformAppIsDeepLinkFailed == 1 {
					var respListItemDeepLinkFailedArray []string
					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

					if len(bidItem.Tracking.DplkFailTrackers) > 0 {
						respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, bidItem.Tracking.DplkFailTrackers...)
					}

					var deeplinkFailedConvTrack models.MHRespConvTracks
					deeplinkFailedConvTrack.ConvType = 11
					deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
					convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
				}

				item.ConvTracks = convTrackArray
			}
		}

		item.ImpressionLink = respListItemImpArray
		item.ClickLink = respListItemClickArray

		if bidItem.Adm == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		} else {
			item.IconURL = bidItem.Adm.Icon
			// title
			if len(bidItem.Adm.Title) > 0 {
				item.Title = bidItem.Adm.Title
			}
			// description
			if len(bidItem.Adm.Desc) > 0 {
				item.Description = bidItem.Adm.Desc
			}
			if bidItem.Adm.Img == nil && bidItem.Adm.Video == nil {
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
			if bidItem.Adm.Img != nil {
				var imgList []models.MHRespImage
				for _, imgItem := range bidItem.Adm.Img {
					var img models.MHRespImage
					img.URL = imgItem.Url
					img.Width = imgItem.Width
					img.Height = imgItem.Height
					imgList = append(imgList, img)
				}
				item.Image = imgList
				item.CrtType = 11
			} else {
				if bidItem.Adm.Video != nil {
					var video models.MHRespVideo
					video.VideoURL = bidItem.Adm.Video.Url
					video.CoverURL = bidItem.Adm.Video.Cover
					video.Duration = bidItem.Adm.Video.Duration * 1000
					video.Width = bidItem.Adm.Video.Width
					video.Height = bidItem.Adm.Video.Height
					item.Video = &video
					item.CrtType = 20
				}
			}

			if item.Video == nil && item.Image == nil {
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// win notice url
		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}
		item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		item.MaterialDirection = platformPos.PlatformPosDirection
		item.PEcpm = price
		list = append(list, item)
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	respTianGong := models.MHUpResp{}
	respTianGong.RespData = &mhResp
	respTianGong.Extra = bigdataExtra

	return &respTianGong

}

func ydEncrypt(plaintext []byte, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	iv := key[:aes.BlockSize]
	paddedPlaintext := pad(plaintext, aes.BlockSize)
	ciphertext := make([]byte, len(paddedPlaintext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, paddedPlaintext)
	encoded := base64.URLEncoding.EncodeToString(ciphertext)
	return encoded, nil
}

func pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

type YueDongRequestObject struct {
	Id     string                      `json:"id"`
	Token  string                      `json:"token"`
	App    *YueDongRequestAppObject    `json:"app"`
	Imp    []*YueDongRequestImpObject  `json:"imp"`
	Device *YueDongRequestDeviceObject `json:"device"`
}

type YueDongRequestAppObject struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
	Ver    string `json:"ver"`
}

type YueDongRequestImpObject struct {
	Id       string `json:"id"`
	Tagid    string `json:"tagid"`
	Style    int    `json:"style"`
	Secure   int    `json:"secure"`
	Bidfloor int    `json:"bidfloor"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	DeepLink int    `json:"deeplink"`
	Ul       int    `json:"ul"`
}

type YueDongRequestDeviceObject struct {
	Os               int      `json:"os"`
	Connectiontype   int      `json:"connectiontype"`
	Devicetype       int      `json:"devicetype"`
	Carrier          int      `json:"carrier"`
	H                int      `json:"h"`
	W                int      `json:"w"`
	CpuNum           int      `json:"cpu_num,omitempty"`
	Osv              string   `json:"osv"`
	Did              string   `json:"did"`
	Didmd5           string   `json:"didmd5"`
	Oaid             string   `json:"oid"`
	OaidMd5          string   `json:"oidmd5"`
	AndroidId        string   `json:"dpid"`
	AndroidIdMd5     string   `json:"dpidmd5"`
	Idfa             string   `json:"ifa"`
	IdfaMd5          string   `json:"ifamd5"`
	Ip               string   `json:"ip"`
	Ua               string   `json:"ua"`
	Make             string   `json:"make"`
	Model            string   `json:"model"`
	Hwv              string   `json:"hwv,omitempty"`
	Macmd5           string   `json:"macmd5,omitempty"`
	Mac              string   `json:"mac,omitempty"`
	RomVersion       string   `json:"rom_version,omitempty"`
	BootMark         string   `json:"boot_mark,omitempty"`
	UpdateMark       string   `json:"update_mark,omitempty"`
	UpdateTime       string   `json:"update_time,omitempty"`
	BootTime         string   `json:"boot_time,omitempty"`
	SysCompilingTime string   `json:"sys_compiling_time,omitempty"`
	BirthTime        string   `json:"birth_time,omitempty"`
	Paid             string   `json:"paid_1_4,omitempty"`
	HmsVer           string   `json:"hms_ver,omitempty"`
	HwagVer          string   `json:"hwag_ver,omitempty"`
	Caid             string   `json:"caid,omitempty"`
	CaidVersion      string   `json:"caid_version,omitempty"`
	DeviceNameMd5    string   `json:"device_name_md5,omitempty"`
	HardwareMachine  string   `json:"hardware_machine,omitempty"`
	PhysicalMemory   string   `json:"physical_memory,omitempty"`
	HardDiskSize     string   `json:"hard_disk_size,omitempty"`
	Country          string   `json:"country,omitempty"`
	Language         string   `json:"language,omitempty"`
	TimeZone         string   `json:"time_zone,omitempty"`
	InstalledApp     []string `json:"installed_app,omitempty"`
}

type YueDongResponseObject struct {
	Code    int                           `json:"code"`
	Id      string                        `json:"id"`
	Bidid   string                        `json:"bidid"`
	Seatbid *YueDongResponseSeatbidObject `json:"seatbid,omitempty"`
}
type YueDongResponseSeatbidObject struct {
	Bid []*YueDongResponseBidObject `json:"bid"`
}

type YueDongResponseBidObject struct {
	Price         int                               `json:"price"`
	Action        int                               `json:"action"`
	AppSize       int                               `json:"app_size"`
	ProtocolType  int                               `json:"protocol_type"`
	Id            string                            `json:"id"`
	Adid          string                            `json:"adid"`
	Impid         string                            `json:"impid"`
	TargetUrl     string                            `json:"target_url"`
	LandingUrl    string                            `json:"landing_url"`
	AppName       string                            `json:"app_name"`
	BrandName     string                            `json:"brand_name"`
	PackageName   string                            `json:"package_name"`
	AppVersion    string                            `json:"app_version"`
	Developer     string                            `json:"developer"`
	PrivacyUrl    string                            `json:"privacy_url"`
	PermissionUrl string                            `json:"permission_url"`
	Tracking      *YueDongResponseBidTrackingObject `json:"tracking"`
	Adm           *YueDongResponseBidAdmObject      `json:"adm"`
}

type YueDongResponseBidAdmObject struct {
	Id    string                           `json:"id"`
	Title string                           `json:"title"`
	Desc  string                           `json:"desc"`
	Icon  string                           `json:"icon"`
	Video *YueDongResponseBidVideoObject   `json:"video"`
	Img   []*YueDongResponseBidImageObject `json:"img"`
}

type YueDongResponseBidImageObject struct {
	Width  int    `json:"w"`
	Height int    `json:"h"`
	Url    string `json:"url"`
}

type YueDongResponseBidVideoObject struct {
	Duration int    `json:"duration"`
	Width    int    `json:"w"`
	Height   int    `json:"h"`
	Url      string `json:"url"`
	Cover    string `json:"cover"`
}

type YueDongResponseBidTrackingObject struct {
	Nurl             string   `json:"nurl"`
	ClickTrackers    []string `json:"click_trackers"`
	ImpTrackers      []string `json:"imp_trackers"`
	DplkTrackers     []string `json:"dplk_trackers"`
	DplkFailTrackers []string `json:"dplk_fail_trackers"`
}
