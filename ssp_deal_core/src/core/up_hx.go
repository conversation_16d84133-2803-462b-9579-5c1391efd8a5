package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	uuid "github.com/satori/go.uuid"
)

// GetFromHx 昊昕 adx
func GetFromHx(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := mhReq.Device.CPUNum
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType int
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = 1
	case 2:
		connectionType = 2
	case 3:
		connectionType = 4
	case 4, 5, 6:
		connectionType = 5
	case 7:
		connectionType = 5
	}

	var os int
	switch mhReq.Device.Os {
	case "ios":
		os = 1
	case "android":
		os = 2
	}

	request := RequestObject{
		AdSpaceID: platformPos.PlatformPosID,
		App: RequestAppObject{
			AppID:      platformPos.PlatformAppID,
			AppVersion: platformPos.PlatformAppVersion,
		},
		Device: RequestDeviceObject{
			IsDeepLink:     true,
			SupportVideo:   true,
			DeviceType:     mhReq.Device.DeviceType,
			ConnectionType: connectionType,
			Carrier:        mhReq.Network.Carrier,
			Orientation:    1,
			Os:             os,
			Height:         mhReq.Device.ScreenHeight,
			Width:          mhReq.Device.ScreenWidth,
			SlotWidth:      platformPos.PlatformPosWidth,
			SlotHeight:     platformPos.PlatformPosHeight,
			SlotBidFloor:   categoryInfo.FloorPrice,
			IP:             mhReq.Device.IP,
			Bundle:         GetAppBundleByConfig(c, mhReq, localPos, platformPos),
			Mac:            mhReq.Device.Mac,
			Make:           mhReq.Device.Manufacturer,
			Brand:          mhReq.Device.Manufacturer,
			Model:          mhReq.Device.Model,
			OsVersion:      mhReq.Device.OsVersion,
			UpdateMark:     mhReq.Device.UpdateMark,
			BootMark:       mhReq.Device.BootMark,
			VerCodeOfHms:   mhReq.Device.HMSCoreVersion,
			VerCodeOfAg:    mhReq.Device.AppStoreVersion,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Device.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Device.Imei = mhReq.Device.Imei
				request.Device.ImeiMd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.Device.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Device.Oaid = mhReq.Device.Oaid
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					isAndroidDeviceOK = true
				}
				request.Device.AndroidID = mhReq.Device.AndroidID
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Device.Idfa = mhReq.Device.Idfa
				request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}

		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {
				tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
				if tmp1DeviceStartSec > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
					request.Device.DiskTotal = diskSizeByte

					CPUNum, _ := strconv.Atoi(tmpCPUNum)
					request.Device.CPUNum = CPUNum
					request.Device.StartUpTime = int64(tmp1DeviceStartSec)

					systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
					request.Device.MbTime = systemUpdateSec
					request.Device.Timezone = tmpTimeZone
					request.Device.SysCpmTime = tmpDeviceBirthSec

					physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
					request.Device.MemTotal = physicalMemoryByte

				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {

					tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					if tmp1DeviceStartSec > 0 {
						isIosDeviceOK = true
						isIOSToUpReportYinZi = true

						diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
						request.Device.DiskTotal = diskSizeByte

						CPUNum, _ := strconv.Atoi(tmpCPUNum)
						request.Device.CPUNum = CPUNum
						request.Device.StartUpTime = int64(tmp1DeviceStartSec)

						systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
						request.Device.MbTime = systemUpdateSec
						request.Device.Timezone = tmpTimeZone
						request.Device.SysCpmTime = tmpDeviceBirthSec

						physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
						request.Device.MemTotal = physicalMemoryByte
					}
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {

					tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					if tmp1DeviceStartSec > 0 {
						isIosDeviceOK = true
						isIOSToUpReportYinZi = true

						diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
						request.Device.DiskTotal = diskSizeByte

						CPUNum, _ := strconv.Atoi(tmpCPUNum)
						request.Device.CPUNum = CPUNum
						request.Device.StartUpTime = int64(tmp1DeviceStartSec)

						systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
						request.Device.MbTime = systemUpdateSec
						request.Device.Timezone = tmpTimeZone
						request.Device.SysCpmTime = tmpDeviceBirthSec

						physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
						request.Device.MemTotal = physicalMemoryByte
					}
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.Device.Os = 1
					case "android":
						request.Device.Os = 2
					}
					request.Device.OsVersion = didRedisData.OsVersion
					request.Device.Model = didRedisData.Model
					request.Device.Make = didRedisData.Manufacturer
					request.Device.Brand = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Device.Imei = didRedisData.Imei
							request.Device.ImeiMd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.Device.ImeiMd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Device.Oaid = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								request.Device.AndroidID = didRedisData.AndroidID
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Device.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.Device.Os = 1
						case "android":
							request.Device.Os = 2
						}
						request.Device.OsVersion = didRedisData.OsVersion
						request.Device.Model = didRedisData.Model
						request.Device.Make = didRedisData.Manufacturer
						request.Device.Brand = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Device.Idfa = didRedisData.Idfa
								request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {

								tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
								if tmp1DeviceStartSec > 0 {

									diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
									request.Device.DiskTotal = diskSizeByte

									CPUNum, _ := strconv.Atoi(tmpCPUNum)
									request.Device.CPUNum = CPUNum
									request.Device.StartUpTime = int64(tmp1DeviceStartSec)

									systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
									request.Device.MbTime = systemUpdateSec
									request.Device.Timezone = tmpTimeZone
									request.Device.SysCpmTime = tmpDeviceBirthSec

									physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
									request.Device.MemTotal = physicalMemoryByte

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {
									tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									if tmp1DeviceStartSec > 0 {
										diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
										request.Device.DiskTotal = diskSizeByte

										CPUNum, _ := strconv.Atoi(tmpCPUNum)
										request.Device.CPUNum = CPUNum
										request.Device.StartUpTime = int64(tmp1DeviceStartSec)

										systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
										request.Device.MbTime = systemUpdateSec
										request.Device.Timezone = tmpTimeZone
										request.Device.SysCpmTime = tmpDeviceBirthSec

										physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
										request.Device.MemTotal = physicalMemoryByte

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpPhysicalMemoryByte) > 0 {

									tmp1DeviceStartSec := utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									if tmp1DeviceStartSec > 0 {
										diskSizeByte, _ := strconv.ParseInt(tmpHarddiskSizeByte, 10, 64)
										request.Device.DiskTotal = diskSizeByte

										CPUNum, _ := strconv.Atoi(tmpCPUNum)
										request.Device.CPUNum = CPUNum
										request.Device.StartUpTime = int64(tmp1DeviceStartSec)

										systemUpdateSec, _ := strconv.ParseFloat(tmpSystemUpdateSec, 64)
										request.Device.MbTime = systemUpdateSec
										request.Device.Timezone = tmpTimeZone
										request.Device.SysCpmTime = tmpDeviceBirthSec

										physicalMemoryByte, _ := strconv.ParseInt(tmpPhysicalMemoryByte, 10, 64)
										request.Device.MemTotal = physicalMemoryByte

										isIosReplaceDeviceOK = true
										isIOSToUpReportYinZi = true
									}
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Device.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	requestMarshal, _ := json.Marshal(request)
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	if platformPos.PlatformAppIsReportUa == 1 {
		httpHeader["User-Agent"] = mhReq.Device.Ua
	}
	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(requestMarshal))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	hxRespStu := ResponseObject{}
	_ = json.Unmarshal(bodyContent, &hxRespStu)

	// 返回数据
	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if hxRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = hxRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if hxRespStu.Data == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	respData := hxRespStu.Data

	respTmpRespAllNum = respTmpRespAllNum + 1

	// ecpm
	price := respData.Price

	respTmpPrice = respTmpPrice + price

	// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
	if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
		price = platformPos.PlatformPosEcpm
	}
	// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
	if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
		// 价格拦截, 跟之前一样
		if localPosFinalPrice > price {
			respTmpInternalCode = 900104
			respTmpRespFailedNum = respTmpRespFailedNum + 1
		}
	}

	// TODO return?
	// 填充后限制
	isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitAfterUpRespOK {
	} else {
		respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
		respTmpRespFailedNum = respTmpRespFailedNum + 1
	}

	// 下发ecpm
	// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
	tmpEcpm := 0
	if localPos.LocalPosEcpmType == 0 {
		// 不下发
		tmpEcpm = localPos.LocalPosEcpm
	} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
		tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
		if tmpEcpm < 1 {
			tmpEcpm = 0
		}

		item.Ecpm = tmpEcpm
	} else if localPos.LocalPosEcpmType == 2 {
		tmpEcpm = localPos.LocalPosEcpm
		item.Ecpm = localPos.LocalPosEcpm
	}

	switch respData.MediaStyle {
	case 1:
		item.InteractType = 0
		if len(respData.Creative.DURL) > 0 {
			item.LandpageURL = respData.Creative.DURL
		}
	case 2:
		item.InteractType = 1
		if len(respData.Creative.DURL) > 0 {
			item.DownloadURL = respData.Creative.DURL
		}
	default:
		bigdataExtra.InternalCode = 900105
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// adid
	maplehazeAdId := uuid.NewV4().String()
	item.AdID = maplehazeAdId
	item.ReqWidth = platformPos.PlatformPosWidth
	item.ReqHeight = platformPos.PlatformPosHeight

	// title
	if len(respData.Creative.Title) > 0 {
		item.Title = respData.Creative.Title
	}
	// description
	if len(item.Description) > 0 {
		item.Description = respData.Creative.Desc
	}

	if respData.Creative == nil {
		bigdataExtra.InternalCode = 900105
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}
	if respData.Creative.AdShowType == 0 {
		if len(respData.Creative.Images) > 0 {
			var imgList []models.MHRespImage
			for _, imgItem := range respData.Creative.Images {
				var img models.MHRespImage
				img.URL = imgItem
				img.Width = respData.Creative.Width
				img.Height = respData.Creative.Height
				imgList = append(imgList, img)
			}
			item.Image = imgList
			item.CrtType = 11
		}
	} else {
		if respData.Creative.VideoCreative != nil {
			var video models.MHRespVideo
			video.VideoURL = respData.Creative.VideoCreative.VideoUrl
			video.CoverURL = respData.Creative.VideoCreative.CoverUrl
			video.Duration = respData.Creative.VideoCreative.Duration
			video.Width = respData.Creative.Width
			video.Height = respData.Creative.Height
			if len(respData.Creative.VideoCreative.CoverUrl) == 0 && len(respData.Creative.Images) > 0 {
				video.CoverURL = respData.Creative.Images[0]
			}
			item.Video = &video
			item.CrtType = 20
		}
	}

	if item.Image == nil && item.Video == nil {
		bigdataExtra.InternalCode = 900105
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if respData.Creative.AppResponse != nil {
		item.AppName = respData.Creative.AppResponse.AppName
		item.PackageName = respData.Creative.AppResponse.Package
		item.AppVersion = respData.Creative.AppResponse.VersionName
		item.Publisher = respData.Creative.AppResponse.DevelopName
		item.PrivacyLink = respData.Creative.AppResponse.AppPrivacyPolicyUrl
		item.Permission = respData.Creative.AppResponse.PermissionDesc
		item.PermissionURL = respData.Creative.AppResponse.AppPermissionUrl
		item.IconURL = respData.Creative.AppResponse.AppIconUrl
	}

	bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)

	if len(respData.Creative.DeepLink) > 0 {
		var convTrackArray []models.MHRespConvTracks
		var convTracks models.MHRespConvTracks
		item.DeepLink = respData.Creative.DeepLink
		// deeplink track
		var respListItemSuccDeepLinkArray []string
		if len(respData.Creative.DpLinkUrls) > 0 {
			respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, respData.Creative.DpLinkUrls...)
		}
		mhDPParams := url.Values{}
		mhDPParams.Add("result", "0")
		mhDPParams.Add("reason", "")
		mhDPParams.Add("deeptype", "__DEEP_TYPE__")
		mhDPParams.Add("log", bigdataParams)
		respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
		convTracks.ConvType = 10
		convTracks.ConvURLS = respListItemSuccDeepLinkArray
		convTrackArray = append(convTrackArray, convTracks)

		if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			var respListItemDeepLinkFailedArray []string
			if len(respData.Creative.DpLinkErrUrls) > 0 {
				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, respData.Creative.DpLinkErrUrls...)
			}
			mhDPFailedParams := url.Values{}
			mhDPFailedParams.Add("result", "1")
			mhDPFailedParams.Add("reason", "3")
			mhDPFailedParams.Add("log", bigdataParams)

			respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			var deeplinkFailedConvTrack models.MHRespConvTracks
			deeplinkFailedConvTrack.ConvType = 11
			deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
			convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
		}

		item.ConvTracks = convTrackArray
	}

	// impression_link
	var respListItemImpArray []string
	mhImpParams := url.Values{}
	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
	if len(respData.Creative.ShowMonitorLinks) > 0 {
		respListItemImpArray = append(respListItemImpArray, respData.Creative.ShowMonitorLinks...)
	}
	item.ImpressionLink = respListItemImpArray

	// click_link
	var respListItemClickArray []string
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
	mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
	mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
	mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("mh_down_x", "__DOWN_X__")
	mhClkParams.Add("mh_down_y", "__DOWN_Y__")
	mhClkParams.Add("mh_up_x", "__UP_X__")
	mhClkParams.Add("mh_up_y", "__UP_Y__")
	mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
	mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
	mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
	mhClkParams.Add("turnx", "__TURN_X__")
	mhClkParams.Add("turny", "__TURN_Y__")
	mhClkParams.Add("turnz", "__TURN_Z__")
	mhClkParams.Add("turntime", "__TURN_TIME__")

	if platformPos.PlatformPosIsReportSLD == 1 {
		mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
	} else {
		mhClkParams.Add("sld", "__SLD__")
	}
	mhClkParams.Add("log", bigdataParams)
	respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
	if len(respData.Creative.ClickMonitorLinks) > 0 {
		respListItemClickArray = append(respListItemClickArray, respData.Creative.ClickMonitorLinks...)
	}
	item.ClickLink = respListItemClickArray

	// win notice url
	if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
		mhWinNoticeURLParams := url.Values{}
		mhWinNoticeURLParams.Add("log", bigdataParams)
		mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
		mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
		mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
		item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

		// loss notice url
		mhLossNoticeURLParams := url.Values{}
		mhLossNoticeURLParams.Add("log", bigdataParams)
		mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
		mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
		mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
		item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
	}
	item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

	item.MaterialDirection = platformPos.PlatformPosDirection
	item.PEcpm = price
	list = append(list, item)

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	respHx := models.MHUpResp{}
	respHx.RespData = &mhResp
	respHx.Extra = bigdataExtra

	return &respHx

}

type RequestObject struct {
	AdSpaceID string              `json:"ad_space_id"`
	App       RequestAppObject    `json:"app"`
	Device    RequestDeviceObject `json:"device"`
}

type RequestAppObject struct {
	AppID      string `json:"app_id"`
	AppVersion string `json:"app_version"`
	Channel    string `json:"channel"`
}

type RequestDeviceObject struct {
	IsDeepLink       bool     `json:"is_deep_link,omitempty"`
	SupportVideo     bool     `json:"support_video,omitempty"`
	DeviceType       int      `json:"device_type"`
	ConnectionType   int      `json:"connection_type"`
	Carrier          int      `json:"carrier"`
	Os               int      `json:"os"`
	Orientation      int      `json:"orientation,omitempty"`
	Height           int      `json:"height,omitempty"`
	Width            int      `json:"width,omitempty"`
	Ppi              int      `json:"ppi,omitempty"`
	CPUNum           int      `json:"cpu_num"`
	SlotWidth        int      `json:"slot_width,omitempty"`
	SlotHeight       int      `json:"slot_height,omitempty"`
	SlotBidFloor     int      `json:"slot_bid_floor"`
	DiskTotal        int64    `json:"disk_total"`
	MemTotal         int64    `json:"mem_total"`
	StartUpTime      int64    `json:"start_up_time"`
	MbTime           float64  `json:"mb_time"`
	Ua               string   `json:"ua"`
	IP               string   `json:"ip"`
	Bundle           string   `json:"bundle"`
	Oaid             string   `json:"o_aid,omitempty"`
	Imei             string   `json:"imei,omitempty"`
	ImeiMd5          string   `json:"imei_md5,omitempty"`
	Idfa             string   `json:"idfa,omitempty"`
	IdfaMd5          string   `json:"idfa_md5,omitempty"`
	AndroidID        string   `json:"android_id,omitempty"`
	Mac              string   `json:"mac,omitempty"`
	Imsi             string   `json:"i_msi,omitempty"`
	Make             string   `json:"make"`
	Brand            string   `json:"brand"`
	Model            string   `json:"model"`
	OsVersion        string   `json:"os_version"`
	RomVersion       string   `json:"rom_version,omitempty"`
	Lon              string   `json:"lon,omitempty"`
	Lat              string   `json:"lat,omitempty"`
	Timezone         string   `json:"os_timezone,omitempty"`
	SysCpmTime       string   `json:"sys_cpm_time,omitempty"`
	PhoneName        string   `json:"phone_name,omitempty"`
	Ssid             string   `json:"ssid,omitempty"`
	WifiMac          string   `json:"wifi_mac,omitempty"`
	UpdateMark       string   `json:"update_mark"`
	BootMark         string   `json:"boot_mark"`
	ViVoStoreVersion string   `json:"vi_vo_store_version,omitempty"`
	VerCodeOfHms     string   `json:"ver_code_of_hms,omitempty"`
	VerCodeOfAg      string   `json:"ver_code_of_ag,omitempty"`
	AppLists         []string `json:"app_lists,omitempty"`
}

type ResponseObject struct {
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Data *ResponseDataObject `json:"data"`
}

type ResponseDataCreativeObject struct {
	Action            int                              `json:"dAction"`
	AdShowType        int                              `json:"adShowType"`
	AdPlaceType       int                              `json:"adPlaceType"`
	Width             int                              `json:"width"`
	Height            int                              `json:"height"`
	Title             string                           `json:"title"`
	Desc              string                           `json:"desc"`
	DeepLink          string                           `json:"deepLink"`
	DURL              string                           `json:"dUrl"`
	Images            []string                         `json:"images"`
	ShowMonitorLinks  []string                         `json:"showMonitorLinks"`
	ClickMonitorLinks []string                         `json:"clickMonitorLinks"`
	DpLinkUrls        []string                         `json:"dpLinkUrls"`
	DsUrls            []string                         `json:"dsUrls"`
	DeUrls            []string                         `json:"deUrls"`
	InstBUrls         []string                         `json:"instBUrls"`
	InstEUrls         []string                         `json:"instEUrls"`
	DownSuccessUrls   []string                         `json:"downSuccessUrls"`
	DpLinkTryUrls     []string                         `json:"dpLinkTryUrls"`
	DpLinkErrUrls     []string                         `json:"dpLinkErrUrls"`
	VideoCreative     *ResponseDataCreativeVideoObject `json:"videoCreative"`
	AppResponse       *ResponseDataCreativeAppObject   `json:"appResponse"`
	WechatMiniProgram *ResponseDataCreativeAppObject   `json:"wechatMiniProgram"`
}

type ResponseDataCreativeVideoObject struct {
	Duration               int    `json:"duration"`
	VideoUrl               string `json:"videoUrl"`
	CoverUrl               string `json:"coverUrl"`
	VideoStartUrls         string `json:"videoStartUrls"`
	VideoFirstQuartileUrls string `json:"videoFirstQuartileUrls"`
	VideoMidpointUrls      string `json:"videoMidpointUrls"`
	VideoThirdQuartileUrls string `json:"videoThirdQuartileUrls"`
	VideoEndUrls           string `json:"videoEndUrls"`
	VideoFailUrls          string `json:"videoFailUrls"`
}

type ResponseDataCreativeAppObject struct {
	AppName             string `json:"appName"`
	Package             string `json:"package"`
	VersionName         string `json:"versionName"`
	DevelopName         string `json:"developName"`
	AppIconUrl          string `json:"appIconUrl"`
	AppPrivacyPolicyUrl string `json:"appPrivacyPolicyUrl"`
	AppPermissionUrl    string `json:"appPermissionUrl"`
	PermissionLabel     string `json:"permissionLabel"`
	PermissionDesc      string `json:"permissionDesc"`
}

type ResponseDataCreativeWechatObject struct {
	AppId    string `json:"appId"`
	OriginId string `json:"originId"`
	Path     string `json:"path"`
}

type ResponseDataObject struct {
	Type       int                         `json:"type"`
	MediaStyle int                         `json:"mediaStyle"`
	Price      int                         `json:"price"`
	Creative   *ResponseDataCreativeObject `json:"creative"`
}
