package core

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/tiangong_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func GetFromTianGong(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	// tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	requestObject := tiangong_up.Request{
		Source: &tiangong_up.Request_Source{
			SourceId:     proto.String(platformPos.PlatformPosID),
			ImpressionId: proto.String(utils.Get16Md5(platformPos.PlatformPosID)),
			Width:        proto.Int32(int32(platformPos.PlatformPosWidth)),
			Height:       proto.Int32(int32(platformPos.PlatformPosHeight)),
			IsMulti:      proto.Bool(false),
		},
		Device: &tiangong_up.Request_Device{
			Ip:         proto.String(mhReq.Device.IP),
			AndroidId:  proto.String(mhReq.Device.AndroidID),
			DeviceType: proto.Int32(0),
			Osv:        proto.String(mhReq.Device.OsVersion),
			Width:      proto.Int32(int32(mhReq.Device.ScreenWidth)),
			Height:     proto.Int32(int32(mhReq.Device.ScreenHeight)),
		},
	}

	switch mhReq.Network.ConnectType {
	case 0:
		requestObject.Device.Network = proto.Int32(0)
	case 1:
		requestObject.Device.Network = proto.Int32(1)
	case 2:
		requestObject.Device.Network = proto.Int32(2)
	case 3:
		requestObject.Device.Network = proto.Int32(3)
	case 4:
		requestObject.Device.Network = proto.Int32(4)
	case 7:
		requestObject.Device.Network = proto.Int32(5)
	default:
		requestObject.Device.Network = proto.Int32(0)
	}

	switch mhReq.Network.Carrier {
	case 1:
		requestObject.Device.Operator = proto.Int32(1)
	case 2:
		requestObject.Device.Operator = proto.Int32(2)
	case 3:
		requestObject.Device.Operator = proto.Int32(3)
	}

	if len(mhReq.Device.Manufacturer) > 0 {
		requestObject.Device.Brand = proto.String(mhReq.Device.Manufacturer)
	}

	if len(mhReq.Device.Model) > 0 {
		requestObject.Device.Model = proto.String(mhReq.Device.Model)
	}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false
	// defaultDeviceId := "acc2584006a5ae5ec9fd98ef579fc4ae"
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		requestObject.Device.Os = proto.String("Android")
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IMEI.Enum()
				requestObject.Device.DeiviceId = proto.String(utils.GetMd5(mhReq.Device.Imei))
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				requestObject.Device.IdentifierType = tiangong_up.IdentifierType_OAID.Enum()
				requestObject.Device.DeiviceId = proto.String(mhReq.Device.Oaid)
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true

				requestObject.Device.IdentifierType = tiangong_up.IdentifierType_OAID_MD5.Enum()
				requestObject.Device.DeiviceId = proto.String(mhReq.Device.OaidMd5)
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// mhReq.Device.Imei = defaultDeviceId
			// requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IMEI.Enum()
			// requestObject.Device.DeiviceId = proto.String(utils.GetMd5(defaultDeviceId))

			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else {
		requestObject.Device.Os = proto.String("iOS")
		if len(mhReq.Device.Idfa) == 0 {
			// mhReq.Device.Idfa = defaultDeviceId
			requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IDFA.Enum()
			requestObject.Device.DeiviceId = proto.String(utils.GetMd5(mhReq.Device.Idfa))
		}
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IDFA.Enum()
				requestObject.Device.DeiviceId = proto.String(utils.GetMd5(mhReq.Device.Idfa))

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				if len(mhReq.Device.CAIDMulti) > 0 {
					var caidArray []*tiangong_up.Request_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo tiangong_up.Request_Device_CaidInfo
							caidInfo.Caid = proto.String(item.CAID)
							caidInfo.Version = proto.String(item.CAIDVersion)
							caidArray = append(caidArray, &caidInfo)
							break
						}
					}
					requestObject.Device.CaidInfo = caidArray
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
			} else {
				if len(mhReq.Device.CAIDMulti) > 0 {
					var caidArray []*tiangong_up.Request_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo tiangong_up.Request_Device_CaidInfo
							caidInfo.Caid = proto.String(item.CAID)
							caidInfo.Version = proto.String(item.CAIDVersion)
							caidArray = append(caidArray, &caidInfo)
						}
					}
					requestObject.Device.CaidInfo = caidArray
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
			}
		}

		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					if len(mhReq.Device.CAIDMulti) > 0 {
						var caidArray []*tiangong_up.Request_Device_CaidInfo
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								var caidInfo tiangong_up.Request_Device_CaidInfo
								caidInfo.Caid = proto.String(item.CAID)
								caidInfo.Version = proto.String(item.CAIDVersion)
								caidArray = append(caidArray, &caidInfo)
								break
							}
						}
						requestObject.Device.CaidInfo = caidArray
					}
					if len(requestObject.Device.CaidInfo) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				} else {
					if len(mhReq.Device.CAIDMulti) > 0 {
						var caidArray []*tiangong_up.Request_Device_CaidInfo
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								var caidInfo tiangong_up.Request_Device_CaidInfo
								caidInfo.Caid = proto.String(item.CAID)
								caidInfo.Version = proto.String(item.CAIDVersion)
								caidArray = append(caidArray, &caidInfo)
							}
						}
						requestObject.Device.CaidInfo = caidArray
					}
					if len(requestObject.Device.CaidInfo) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}
		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					if len(mhReq.Device.CAIDMulti) > 0 {
						var caidArray []*tiangong_up.Request_Device_CaidInfo
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								var caidInfo tiangong_up.Request_Device_CaidInfo
								caidInfo.Caid = proto.String(item.CAID)
								caidInfo.Version = proto.String(item.CAIDVersion)
								caidArray = append(caidArray, &caidInfo)
								break
							}
						}
						requestObject.Device.CaidInfo = caidArray
					}
					if len(requestObject.Device.CaidInfo) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				} else {
					if len(mhReq.Device.CAIDMulti) > 0 {
						var caidArray []*tiangong_up.Request_Device_CaidInfo
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								var caidInfo tiangong_up.Request_Device_CaidInfo
								caidInfo.Caid = proto.String(item.CAID)
								caidInfo.Version = proto.String(item.CAIDVersion)
								caidArray = append(caidArray, &caidInfo)
							}
						}
						requestObject.Device.CaidInfo = caidArray
					}
					if len(requestObject.Device.CaidInfo) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}
		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			fmt.Println("get from jd error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false
	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)

	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					requestObject.Device.Osv = proto.String(didRedisData.OsVersion)
					requestObject.Device.Model = proto.String(didRedisData.Model)
					requestObject.Device.Brand = proto.String(didRedisData.Manufacturer)

					requestObject.Device.IdentifierType = tiangong_up.IdentifierType_OTHER.Enum()
					requestObject.Device.DeiviceId = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IMEI.Enum()
							requestObject.Device.DeiviceId = proto.String(utils.GetMd5(didRedisData.Imei))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							requestObject.Device.IdentifierType = tiangong_up.IdentifierType_OAID.Enum()
							requestObject.Device.DeiviceId = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}

					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						requestObject.Device.Osv = proto.String(didRedisData.OsVersion)
						requestObject.Device.Model = proto.String(didRedisData.Model)
						requestObject.Device.Brand = proto.String(didRedisData.Manufacturer)

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IDFA.Enum()
								requestObject.Device.DeiviceId = proto.String(utils.GetMd5(didRedisData.Idfa))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidArray []*tiangong_up.Request_Device_CaidInfo
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidInfo tiangong_up.Request_Device_CaidInfo
										caidInfo.Caid = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
										break
									}
								}
								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									requestObject.Device.CaidInfo = caidArray
								}
							} else {
								var caidArray []*tiangong_up.Request_Device_CaidInfo

								for _, item := range tmpCAIDMulti {
									var caidInfo tiangong_up.Request_Device_CaidInfo
									caidInfo.Caid = proto.String(item.CAID)
									caidInfo.Version = proto.String(item.CAIDVersion)
									caidArray = append(caidArray, &caidInfo)
								}

								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									requestObject.Device.CaidInfo = caidArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							// tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							// tmpTimeZone = didRedisData.TimeZone

							if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {
								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IDFA.Enum()
									requestObject.Device.DeiviceId = proto.String(utils.GetMd5(didRedisData.Idfa))
								}

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*tiangong_up.Request_Device_CaidInfo
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo tiangong_up.Request_Device_CaidInfo
											caidInfo.Caid = proto.String(item.CAID)
											caidInfo.Version = proto.String(item.CAIDVersion)
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										requestObject.Device.CaidInfo = caidArray
									}
								} else {
									var caidArray []*tiangong_up.Request_Device_CaidInfo

									for _, item := range tmpCAIDMulti {
										var caidInfo tiangong_up.Request_Device_CaidInfo
										caidInfo.Caid = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
									}

									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										requestObject.Device.CaidInfo = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								// tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									requestObject.Device.IdentifierType = tiangong_up.IdentifierType_IDFA.Enum()
									requestObject.Device.DeiviceId = proto.String(utils.GetMd5(didRedisData.Idfa))
								}

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*tiangong_up.Request_Device_CaidInfo

									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo tiangong_up.Request_Device_CaidInfo
											caidInfo.Caid = proto.String(item.CAID)
											caidInfo.Version = proto.String(item.CAIDVersion)
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										requestObject.Device.CaidInfo = caidArray
									}
								} else {
									var caidArray []*tiangong_up.Request_Device_CaidInfo

									for _, item := range tmpCAIDMulti {
										var caidInfo tiangong_up.Request_Device_CaidInfo
										caidInfo.Caid = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
									}

									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										requestObject.Device.CaidInfo = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								// tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}
	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from jd error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from jd error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006
				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	// tmpReqByte, _ := json.Marshal(&requestObject)
	// fmt.Println("tiangong req:", string(tmpReqByte))
	// fmt.Println("tiangong req url:", platformPos.PlatformAppUpURL)

	marshal, _ := proto.Marshal(&requestObject)
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(marshal)
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
	}
	if err = gzipWriter.Close(); err != nil {
		fmt.Println(err)
	}

	write := buf.Bytes()
	request, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(write))
	request.Header.Add("Content-Type", "application/octet-stream")
	request.Header.Add("Connection", "keep-alive")
	request.Header.Add("Accept-Encoding", "gzip,deflate,br")
	request.Header.Add("Content-Encoding", "gzip")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(request)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	defer resp.Body.Close()

	if resp.StatusCode == 204 {
		fmt.Println("no fill")
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if resp.StatusCode != 200 {
		if err != nil {
			fmt.Println("status is no 200: " + err.Error())
		}
		fmt.Println("status is no 200")
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bodyContent, err := io.ReadAll(resp.Body)

	bufer := bytes.NewBuffer(bodyContent)
	reader, _ := gzip.NewReader(bufer)
	defer func(reader *gzip.Reader) {
		err := reader.Close()
		if err != nil {

		}
	}(reader)
	bodyContent, _ = io.ReadAll(reader)

	var responseObject tiangong_up.Response
	_ = proto.Unmarshal(bodyContent, &responseObject)

	// tmpRespByte, _ := json.Marshal(&responseObject)
	// fmt.Println("tiangong resp code:", resp.StatusCode)
	// fmt.Println("tiangong resp body:", string(tmpRespByte))
	if true {
		tmpRespByte, _ := json.Marshal(&responseObject)
		go models.BigDataHoloDebugJson2(bigdataUID, string(tmpRespByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}

	if responseObject.GetStatus() != 0 {
		fmt.Println("upstream request error: code:" + string(responseObject.GetStatus()) + "msg:" + responseObject.GetMsg())
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	bigdataExtra.UpRespTime = 1
	responseItem := responseObject.GetAd()
	if responseItem == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 统计上游返回个数+1
	respTmpRespAllNum = respTmpRespAllNum + 1

	bidPrice := 0

	respTmpPrice = respTmpPrice + bidPrice

	var list []*models.MHRespDataItem
	var commonAPIResponseObject models.MHRespDataItem

	maplehazeAdId := uuid.NewV4().String()
	commonAPIResponseObject.AdID = maplehazeAdId

	if platformPos.PlatformPosEcpmType == 1 {
		bidPrice = platformPos.PlatformPosEcpm
	}

	if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
		if localPosFinalPrice > bidPrice {
			bigdataExtra.InternalCode = 900104
			bigdataExtra.ExternalCode = 102006
			bigdataExtra.UpPrice = respTmpPrice
			bigdataExtra.UpRespNum = 1
			bigdataExtra.UpRespFailedNum = 1
			bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// 填充后限制
	isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitAfterUpRespOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterUpRespOKErrCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 下发ecpm
	tmpEcpm := 0
	if localPos.LocalPosEcpmType == 0 {
		// 不下发
		tmpEcpm = localPos.LocalPosEcpm
	} else if localPos.LocalPosEcpmType == 1 {
		tmpEcpm = int(float32(bidPrice) * (float32(100) - localPosProfitRate) / 100)
		if tmpEcpm <= 0 {
			tmpEcpm = 1
		}

		commonAPIResponseObject.Ecpm = tmpEcpm
	} else if localPos.LocalPosEcpmType == 2 {
		tmpEcpm = localPos.LocalPosEcpm
		commonAPIResponseObject.Ecpm = localPos.LocalPosEcpm
	}

	if len(responseItem.GetTitle()) > 0 {
		commonAPIResponseObject.Title = responseItem.GetTitle()
	}

	if len(responseItem.GetSubTitle()) > 0 {
		commonAPIResponseObject.Description = responseItem.GetSubTitle()
	}

	// 天宫点击问题调查：
	// 1. 云端需要改为并行模式，并且尝试兼容串行模式
	// btw: 判断的字段建议使用 jdClickUrl (H5 落地页)，如果有且不为空串则是并行方案，否则是串行方案。
	// 2.
	// a 并行时jdImpUrl和thirdImpUrl需要加入曝光监测impression_link，
	// b 串行时impressionUrl需要加入曝光监测
	// 3.
	// a 不论串并模式，extendClickUrls均需要加入点击监测click_link，
	// b 然后并行时jdClickUrl和thirdClickUrl需要加入点击监测，
	// c 串行时clickUrl需要加入点击监测
	isBingXing := false
	if len(responseItem.GetJdClickUrl()) > 0 {
		isBingXing = true
	}

	// deeplink
	tmpDeepLink := ""
	if mhReq.Device.Os == "android" {
		if isBingXing {
			tmpDeepLink = responseItem.GetJdOpenUrl()
		} else {
			tmpDeepLink = responseItem.GetOpenUrl()
		}
	} else if mhReq.Device.Os == "ios" {
		if isBingXing {
			if len(responseItem.GetJdULinkUrl()) > 0 {
				tmpDeepLink = responseItem.GetJdULinkUrl()
			} else if len(responseItem.GetJdOpenUrl()) > 0 {
				tmpDeepLink = responseItem.GetJdOpenUrl()
			}
		} else {
			if len(responseItem.GetULinkUrl()) > 0 {
				tmpDeepLink = responseItem.GetULinkUrl()
			} else if len(responseItem.GetOpenUrl()) > 0 {
				tmpDeepLink = responseItem.GetOpenUrl()
			}
		}
	}

	// landingpage
	tmpLandingPage := ""
	if isBingXing {
		tmpLandingPage = responseItem.GetJdClickUrl()
	} else {
		tmpLandingPage = responseItem.GetClickUrl()
	}

	commonAPIResponseObject.InteractType = 0
	commonAPIResponseObject.LandpageURL = tmpLandingPage
	commonAPIResponseObject.DeepLink = tmpDeepLink
	commonAPIResponseObject.Crid = commonAPIResponseObject.AdID
	commonAPIResponseObject.ReqWidth = platformPos.PlatformPosWidth
	commonAPIResponseObject.ReqHeight = platformPos.PlatformPosHeight

	// 是否有deeplink
	if len(tmpDeepLink) > 0 {
		// deeplink track
		var respListItemDeepLinkArray []string

		mhDPParams := url.Values{}
		mhDPParams.Add("result", "0")
		mhDPParams.Add("reason", "")
		mhDPParams.Add("deeptype", "__DEEP_TYPE__")
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
		mhDPParams.Add("log", bigdataParams)

		respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

		var convTrackArray []models.MHRespConvTracks

		var deeplinkConvTrack models.MHRespConvTracks
		deeplinkConvTrack.ConvType = 10
		deeplinkConvTrack.ConvURLS = respListItemDeepLinkArray

		convTrackArray = append(convTrackArray, deeplinkConvTrack)

		// deeplink failed track
		if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			var respListItemDeepLinkFailedArray []string

			mhDPFailedParams := url.Values{}
			mhDPFailedParams.Add("result", "1")
			mhDPFailedParams.Add("reason", "3")
			mhDPFailedParams.Add("log", bigdataParams)

			respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			var deeplinkFailedConvTrack models.MHRespConvTracks
			deeplinkFailedConvTrack.ConvType = 11
			deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray

			convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
		}

		commonAPIResponseObject.ConvTracks = convTrackArray
	}

	var respListItemShowArray []string
	var respListItemClickArray []string

	bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, bidPrice, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)

	// 曝光监测链接
	if isBingXing {
		if len(responseItem.GetJdImpUrl()) > 0 {
			tmpItem := responseItem.GetJdImpUrl()
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemShowArray = append(respListItemShowArray, tmpItem)
		}
		if len(responseItem.GetThirdImpUrl()) > 0 {
			tmpItem := responseItem.GetThirdImpUrl()
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemShowArray = append(respListItemShowArray, tmpItem)
		}
	} else {
		if len(responseItem.GetImpressionUrl()) > 0 {
			tmpItem := responseItem.GetImpressionUrl()
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemShowArray = append(respListItemShowArray, tmpItem)
		}
	}

	// impression_link maplehaze
	mhImpParams := url.Values{}
	mhImpParams.Add("log", bigdataParams)
	respListItemShowArray = append(respListItemShowArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
	commonAPIResponseObject.ImpressionLink = respListItemShowArray

	// 点击监测链接
	if isBingXing {
		// if len(responseItem.GetJdClickUrl()) > 0 {
		// 	tmpItem := responseItem.GetJdClickUrl()
		// 	if mhReq.Device.Os == "android" {
		// 		if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
		// 			tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
		// 		} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
		// 			tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
		// 		} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
		// 			tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
		// 		}
		// 	} else if mhReq.Device.Os == "ios" {
		// 		if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
		// 			tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
		// 		}

		// 		if len(requestObject.Device.CaidInfo) > 0 {
		// 			var tmpReplace []string
		// 			for _, item := range requestObject.Device.CaidInfo {
		// 				tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
		// 			}

		// 			tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
		// 		}
		// 	}

		// 	respListItemClickArray = append(respListItemClickArray, tmpItem)
		// }

		if len(responseItem.GetThirdClickUrl()) > 0 {
			tmpItem := responseItem.GetThirdClickUrl()
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemClickArray = append(respListItemClickArray, tmpItem)
		}

		for _, clkItem := range responseItem.GetExtendClickUrls() {
			tmpItem := clkItem
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemClickArray = append(respListItemClickArray, tmpItem)
		}
	} else {
		if len(responseItem.GetClickUrl()) > 0 {
			tmpItem := responseItem.GetClickUrl()
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemClickArray = append(respListItemClickArray, tmpItem)
		}
		for _, clkItem := range responseItem.GetExtendClickUrls() {
			tmpItem := clkItem
			if mhReq.Device.Os == "android" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IMEI {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID {
					tmpItem = strings.Replace(tmpItem, "__OAID__", requestObject.Device.GetDeiviceId(), -1)
				} else if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_OAID_MD5 {
					tmpItem = strings.Replace(tmpItem, "__OAID1__", requestObject.Device.GetDeiviceId(), -1)
				}
			} else if mhReq.Device.Os == "ios" {
				if requestObject.Device.GetIdentifierType() == tiangong_up.IdentifierType_IDFA {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", requestObject.Device.GetDeiviceId(), -1)
				}

				if len(requestObject.Device.CaidInfo) > 0 {
					var tmpReplace []string
					for _, item := range requestObject.Device.CaidInfo {
						tmpReplace = append(tmpReplace, item.GetVersion()+"_"+item.GetCaid())
					}

					tmpItem = strings.Replace(tmpItem, "__CAID__", strings.Join(tmpReplace, ","), -1)
				}
			}

			respListItemClickArray = append(respListItemClickArray, tmpItem)
		}
	}

	// click_link maplehaze
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
	mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
	mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
	mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("mh_down_x", "__DOWN_X__")
	mhClkParams.Add("mh_down_y", "__DOWN_Y__")
	mhClkParams.Add("mh_up_x", "__UP_X__")
	mhClkParams.Add("mh_up_y", "__UP_Y__")
	mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
	mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
	mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
	mhClkParams.Add("turnx", "__TURN_X__")
	mhClkParams.Add("turny", "__TURN_Y__")
	mhClkParams.Add("turnz", "__TURN_Z__")
	mhClkParams.Add("turntime", "__TURN_TIME__")

	if platformPos.PlatformPosIsReportSLD == 1 {
		mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
	} else {
		mhClkParams.Add("sld", "__SLD__")
	}
	mhClkParams.Add("log", bigdataParams)

	respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
	commonAPIResponseObject.ClickLink = respListItemClickArray

	// win notice url
	if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
		mhWinNoticeURLParams := url.Values{}
		mhWinNoticeURLParams.Add("log", bigdataParams)
		mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
		mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
		mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
		commonAPIResponseObject.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

		// loss notice url
		mhLossNoticeURLParams := url.Values{}
		mhLossNoticeURLParams.Add("log", bigdataParams)
		mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
		mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
		mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
		commonAPIResponseObject.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
	}
	commonAPIResponseObject.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

	if len(responseItem.GetCreative()) > 0 {
		for _, resourcesItem := range responseItem.GetCreative() {
			var commonAPIResponseImageObjects []models.MHRespImage
			if len(resourcesItem.GetCreativeUrl()) > 0 {
				var commonAPIResponseImageObject models.MHRespImage
				commonAPIResponseImageObject.URL = responseItem.GetCreativeDomain() + resourcesItem.GetCreativeUrl()
				commonAPIResponseImageObject.Width = platformPos.PlatformPosWidth
				commonAPIResponseImageObject.Height = platformPos.PlatformPosHeight
				commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, commonAPIResponseImageObject)
			}
			commonAPIResponseObject.Image = commonAPIResponseImageObjects
			commonAPIResponseObject.CrtType = 11

			if commonAPIResponseObject.Image == nil || len(commonAPIResponseObject.Image) == 0 {
				continue
			}
		}
	} else {
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	commonAPIResponseObject.MaterialDirection = platformPos.PlatformPosDirection
	commonAPIResponseObject.PEcpm = bidPrice
	list = append(list, &commonAPIResponseObject)

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = 1
		bigdataExtra.UpRespFailedNum = 1
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	respTJ := models.MHUpResp{}
	respTJ.RespData = &mhResp
	respTJ.Extra = bigdataExtra

	respTJStr, _ := json.Marshal(respTJ)
	fmt.Println(string(respTJStr))
	return &respTJ

}
