package core

import (
	"encoding/json"
	"mh_proxy/db"
	"mh_proxy/models"
	"strings"
)

func MaterialPackageFull(os string, item *models.MHRespDataItem) {
	if len(item.PackageName) > 0 || len(item.DeepLink) == 0 {
		return
	}

	fullKey := "go_material_package_full"
	fullCacheValue, _ := db.GlbBigCacheMinute.Get(fullKey)
	if len(fullCacheValue) == 0 {
		return
	}

	var materialPackageFull MaterialPackageFullStu
	_ = json.Unmarshal(fullCacheValue, &materialPackageFull.Data)

	for deepLinkStr, packageName := range *materialPackageFull.Data {
		deepLinkArray := strings.Split(deepLinkStr, ",")
		for _, deepLink := range deepLinkArray {
			if strings.Contains(item.DeepLink, deepLink) {
				packageNameArray := strings.Split(packageName, "_")
				if os == "android" {
					item.PackageName = packageNameArray[0]
				} else {
					item.PackageName = packageNameArray[1]
				}
				return
			}
		}
	}
}

type MaterialPackageFullStu struct {
	Data *map[string]string
}
