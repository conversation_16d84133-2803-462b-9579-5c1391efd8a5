package core

import (
	"context"
	"encoding/json"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/crid_generate/utilities"
)

func NewMaterialReplaceType(ctx context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, item *models.MHRespDataItem) {
	var replaceResults []*models.ReplaceResult

	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}

	appKey := "go_ssp_material_replace_type_config_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_material_replace_type_config_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(posValue) == 0 {
		return
	}

	var materialReplace []models.NewMaterialReplaceType
	if len(appValue) > 0 {
		_ = json.Unmarshal(appValue, &materialReplace)
	}

	if len(appValue) == 0 && len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &materialReplace)
	}

	params := url.Values{}
	params.Set("date", time.Now().Format("20060102"))

	s := utils.Base64URLEncode([]byte(params.Encode()))

	for _, materialItem := range materialReplace {
		if len(materialItem.MaterialReplaceGroup) == 0 {
			continue
		}
		materialReplaceGroup := random(materialItem.MaterialReplaceGroup, int64(len(materialItem.MaterialReplaceGroup)))
		for _, materialReplaceGroupItem := range materialReplaceGroup {
			switch item.CrtType {
			case 11:
				if materialReplaceGroupItem.ReplaceType == "6" && len(materialReplaceGroupItem.VideoUrl) > 0 {
					var originalImage []models.Image
					if item.Image != nil && len(item.Image) > 0 {
						for _, img := range item.Image {
							var mhRespImage models.Image
							mhRespImage.URL = img.URL
							mhRespImage.Width = img.Width
							mhRespImage.Height = img.Height
							originalImage = append(originalImage, mhRespImage)
						}
						var video models.MHRespVideo
						video.VideoURL = materialReplaceGroupItem.VideoUrl
						video.CoverURL = materialReplaceGroupItem.CoverUrl
						video.Width = materialReplaceGroupItem.Width
						video.Height = materialReplaceGroupItem.Height
						video.Duration = materialReplaceGroupItem.Duration * 1000
						if len(video.VideoURL) > 0 {
							if materialReplaceGroupItem.DynamicMaterials == 2 {
								if strings.Contains(video.VideoURL, "?") {
									video.VideoURL = video.VideoURL + "&" + s
								} else {
									video.VideoURL = video.VideoURL + "?" + s
								}
							}
							item.Video = &video
							item.CrtType = 20
							item.Image = nil
							replaceResults = append(replaceResults, &models.ReplaceResult{
								Type:        models.ReplaceTypeImage,
								RuleID:      materialItem.RuleID,
								RuleName:    materialItem.RuleName,
								OriginalImg: originalImage,
							})
							break
						}
					}
				}
			case 20:
				if materialReplaceGroupItem.ReplaceType == "8" && len(materialReplaceGroupItem.ImgUrl) > 0 {
					if item.Video != nil {
						originalVideo := models.Video{
							Duration: item.Video.Duration,
							Width:    item.Video.Width,
							Height:   item.Video.Height,
							VideoURL: item.Video.VideoURL,
							CoverURL: item.Video.CoverURL,
						}

						var mhRespImageArray []models.MHRespImage
						var mhRespImage models.MHRespImage
						mhRespImage.URL = materialReplaceGroupItem.ImgUrl
						mhRespImage.Width = materialReplaceGroupItem.Width
						mhRespImage.Height = materialReplaceGroupItem.Height

						if materialReplaceGroupItem.DynamicMaterials == 2 {
							if strings.Contains(mhRespImage.URL, "?") {
								mhRespImage.URL = mhRespImage.URL + "&" + s
							} else {
								mhRespImage.URL = mhRespImage.URL + "?" + s
							}
						}

						mhRespImageArray = append(mhRespImageArray, mhRespImage)

						if len(mhRespImageArray) > 0 {
							item.CrtType = 11
							item.Image = mhRespImageArray
							item.Video = nil
							replaceResults = append(replaceResults, &models.ReplaceResult{
								Type:          models.ReplaceTypeVideo,
								RuleID:        materialItem.RuleID,
								RuleName:      materialItem.RuleName,
								OriginalVideo: originalVideo,
							})
							break
						}
					}
				}
			}
		}
	}

	if len(replaceResults) > 0 {
		jsonData, _ := json.Marshal(replaceResults)
		var (
			imgUrl   string
			videoUrl string
			coverUrl string
		)
		if item.Image != nil && len(item.Image) > 0 {
			imgUrl = item.Image[0].URL
		}
		if item.Video != nil {
			if len(item.Video.VideoURL) > 0 {
				videoUrl = item.Video.VideoURL
			}
			if len(item.Video.CoverURL) > 0 {
				coverUrl = item.Video.CoverURL
			}
		}
		crid := utilities.GetCrid(item.Title, item.Description, item.IconURL, imgUrl, coverUrl, videoUrl)
		item.SupplyCrid = crid

		// 确保所有参数都不为nil
		if mhReq != nil && len(jsonData) > 0 {
			// Create a deep copy of the item to avoid race conditions in the goroutine
			itemCopy := *item // Copy the struct

			// Create deep copies of nested fields
			if item.Video != nil {
				videoCopy := *item.Video
				itemCopy.Video = &videoCopy
			}

			if len(item.Image) > 0 {
				imageCopy := make([]models.MHRespImage, len(item.Image))
				copy(imageCopy, item.Image)
				itemCopy.Image = imageCopy
			}

			go models.MaterialReplaceStatisticsRawDataKafka(ctx, bigdataUID, mhReq, localPos, platformPos, &itemCopy, jsonData, 2)
		}
	}
}
