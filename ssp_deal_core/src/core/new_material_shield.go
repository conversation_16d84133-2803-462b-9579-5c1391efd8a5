package core

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/models"
	"strings"
	"time"
)

// BlockType 屏蔽类型枚举
type BlockType int

const (
	BlockTypeKeyword     BlockType = 1 // 关键词
	BlockTypePackageName BlockType = 2 // 包名
	BlockTypeAdURL       BlockType = 3 // 广告URL
	BlockTypeMaterialURL BlockType = 4 // 素材URL
	BlockTypeTime        BlockType = 5 // 时间规则
	BlockTypeRegion      BlockType = 6 // 地域规则
	BlockTypeTimeRegion  BlockType = 7 // 时间+地域规则
	BlockTypeAdType      BlockType = 8 // 广告类型
	BlockTypeExpUrl      BlockType = 9 // 监测URL
)

// ShieldResult 存储屏蔽结果信息
type ShieldResult struct {
	IsOK       bool
	Code       int
	BlockType  BlockType // 被拉黑的类型
	BlockValue string    // 具体被拉黑的值
}

// IsShieldOK 检查素材是否符合屏蔽规则
// 屏蔽功能 -----> 直接返回
// 关键词: title(错误码: 900111); description(错误码: 900112)
// 包名: package_name, ios apple_id (错误码: 900114)
// ad_url: 落地页, 下载页, deeplink (错误码: 900113)
// 素材url: image_url, video_url, cover_url, icon_url (错误码: 900115)
// shield_rule_type: 0 不设置, 1 仅时间, 2 仅地域, 3 时间+地域
func IsNewMaterialShieldOK(c context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespDataItem *models.MHRespDataItem, platformPos *models.PlatformPosStu) (bool, int) {
	// 创建结果数组，用于收集所有错误
	var shieldResults []*ShieldResult

	// 先检查白名单
	whiteResult := checkMaterialShieldWhite(c, mhReq, localPos, mhRespDataItem)
	if !whiteResult.IsOK {
		// 如果白名单检查不通过，添加到结果集
		shieldResults = append(shieldResults, whiteResult)
	} else {
		// 白名单通过，再检查黑名单
		blackResults := checkMaterialShieldBlack(c, mhReq, localPos, mhRespDataItem)
		// 将所有黑名单检查结果添加到结果集
		shieldResults = append(shieldResults, blackResults...)
	}

	// 如果没有错误，返回成功
	if len(shieldResults) == 0 {
		return true, 900000
	}

	marshal, _ := json.Marshal(shieldResults)

	// Create a deep copy of the item to avoid race conditions in the goroutine
	itemCopy := *mhRespDataItem // Copy the struct

	// Create deep copies of nested fields
	if mhRespDataItem.Video != nil {
		videoCopy := *mhRespDataItem.Video
		itemCopy.Video = &videoCopy
	}

	if len(mhRespDataItem.Image) > 0 {
		imageCopy := make([]models.MHRespImage, len(mhRespDataItem.Image))
		copy(imageCopy, mhRespDataItem.Image)
		itemCopy.Image = imageCopy
	}

	go models.MaterialShieldStatisticsRawDataKafka(c, bigdataUID, mhReq, localPos, platformPos, &itemCopy, marshal, 1)
	fmt.Println(string(marshal))

	firstError := shieldResults[0]
	return false, firstError.Code
}

// 检查时间是否匹配规则
func isTimeMatch(timeList string) bool {
	if len(timeList) > 0 {
		return strings.Contains(timeList, time.Now().Format("15"))
	}
	return false
}

// 检查地域是否匹配规则
func isRegionMatch(deviceRegion, regionList string) bool {
	return len(deviceRegion) > 0 && len(regionList) > 0 && strings.Contains(regionList, deviceRegion)
}

// 检查素材URL是否匹配规则
func isMaterialURLMatch(item string, mhRespItem *models.MHRespDataItem) bool {
	// 检查图片URL
	if len(mhRespItem.Image) > 0 && strings.Contains(mhRespItem.Image[0].URL, item) {
		return true
	}
	// 检查视频URL
	if mhRespItem.Video != nil {
		if len(mhRespItem.Video.VideoURL) > 0 && strings.Contains(mhRespItem.Video.VideoURL, item) {
			return true
		}
		if len(mhRespItem.Video.CoverURL) > 0 && strings.Contains(mhRespItem.Video.CoverURL, item) {
			return true
		}
	}
	// 检查图标URL
	if len(mhRespItem.IconURL) > 0 && strings.Contains(mhRespItem.IconURL, item) {
		return true
	}
	return false
}

// 检查监测URL是否匹配规则
func isExpURLMatch(item string, mhRespItem *models.MHRespDataItem) bool {
	if len(mhRespItem.ImpressionLink) > 0 {
		for _, impressionUrl := range mhRespItem.ImpressionLink {
			if strings.Contains(impressionUrl, item) {
				return true
			}
		}
	}

	if len(mhRespItem.ClickLink) > 0 {
		for _, impressionUrl := range mhRespItem.ClickLink {
			if strings.Contains(impressionUrl, item) {
				return true
			}
		}
	}

	return false
}

// 检查广告URL是否匹配规则
func isAdURLMatch(item string, mhRespItem *models.MHRespDataItem) bool {
	// 检查落地页URL
	if len(mhRespItem.LandpageURL) > 0 && strings.Contains(mhRespItem.LandpageURL, item) {
		return true
	}
	// 检查下载URL
	if len(mhRespItem.DownloadURL) > 0 && strings.Contains(mhRespItem.DownloadURL, item) {
		return true
	}
	// 检查广告URL
	if len(mhRespItem.AdURL) > 0 && strings.Contains(mhRespItem.AdURL, item) {
		return true
	}
	// 检查深链接
	if len(mhRespItem.DeepLink) > 0 && strings.Contains(mhRespItem.DeepLink, item) {
		return true
	}
	return false
}

// 检查包名是否匹配规则
func isPackageNameMatch(item string, mhRespItem *models.MHRespDataItem, deviceOS string) bool {
	// 检查包名
	if len(mhRespItem.PackageName) > 0 && strings.Contains(mhRespItem.PackageName, item) {
		return true
	}
	// 检查iOS特殊情况
	if deviceOS == "ios" {
		iosAdURL := mhRespItem.AdURL
		if strings.Contains(iosAdURL, "apple.com") && strings.Contains(iosAdURL, item) {
			return true
		}
	}
	return false
}

// 检查关键词是否匹配规则
func isKeywordMatch(item string, mhRespItem *models.MHRespDataItem) bool {
	// 检查标题
	if len(mhRespItem.Title) > 0 && strings.Contains(mhRespItem.Title, item) {
		return true
	}
	// 检查描述
	if len(mhRespItem.Description) > 0 && strings.Contains(mhRespItem.Description, item) {
		return true
	}
	return false
}

// 创建一个错误结果
func createErrorResult(code int, blockType BlockType, blockValue string) *ShieldResult {
	return &ShieldResult{
		IsOK:       false,
		Code:       code,
		BlockType:  blockType,
		BlockValue: blockValue,
	}
}

// 检查素材是否符合白名单规则
func checkMaterialShieldWhite(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespItem *models.MHRespDataItem) *ShieldResult {
	// 创建默认成功结果
	result := &ShieldResult{
		IsOK: true,
		Code: 900000,
	}

	// 检查是否有白名单规则
	if len(localPos.LocalAppShieldWhiteRuleID) == 0 && len(localPos.LocalPosShieldWhiteRuleID) == 0 {
		return result
	}

	// 检查白名单是否激活
	if localPos.LocalAppShieldWhiteIsActive == 0 && localPos.LocalPosShieldWhiteIsActive == 0 {
		return result
	}

	// 广告类型屏蔽 app
	if localPos.LocalAppAdShieldWhiteType > 0 {
		// 下载
		if mhRespItem.InteractType == 0 && localPos.LocalAppAdShieldWhiteType == 1 {
			return createErrorResult(900111, BlockTypeAdType, "download")
		}
		// h5
		if mhRespItem.InteractType == 1 && localPos.LocalAppAdShieldWhiteType == 2 {
			return createErrorResult(900111, BlockTypeAdType, "h5")
		}
	}

	// 广告类型屏蔽 pos
	if localPos.LocalPosAdShieldWhiteType > 0 {
		switch localPos.LocalPosAdShieldWhiteType {
		case 1: // 下载
			if mhRespItem.InteractType == 0 {
				return createErrorResult(900111, BlockTypeAdType, "download")
			}
		case 2: // h5
			if mhRespItem.InteractType == 1 {
				return createErrorResult(900111, BlockTypeAdType, "h5")
			}
		}
	}

	// 检查时间规则 - App
	if localPos.LocalAppShieldWhiteRuleType == 1 && isTimeMatch(localPos.LocalAppShieldWhiteTimeList) {
		return result
	}

	// 检查时间规则 - Pos
	if localPos.LocalPosShieldWhiteRuleType == 1 && isTimeMatch(localPos.LocalPosShieldWhiteTimeList) {
		return result
	}

	// 检查地域规则 - App
	if localPos.LocalAppShieldWhiteRuleType == 2 {
		if isRegionMatch(mhReq.Device.IPProvince, localPos.LocalAppShieldWhiteRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalAppShieldWhiteRegionList) {
			return result
		}
	}

	// 检查地域规则 - Pos
	if localPos.LocalPosShieldWhiteRuleType == 2 {
		if isRegionMatch(mhReq.Device.IPProvince, localPos.LocalPosShieldWhiteRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalPosShieldWhiteRegionList) {
			return result
		}
	}

	// 检查时间+地域规则 - App
	if localPos.LocalAppShieldWhiteRuleType == 3 {
		isTimeOK := isTimeMatch(localPos.LocalAppShieldWhiteTimeList)
		isRegionOK := isRegionMatch(mhReq.Device.IPProvince, localPos.LocalAppShieldWhiteRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalAppShieldWhiteRegionList)
		if isTimeOK && isRegionOK {
			return result
		}
	}

	// 检查时间+地域规则 - Pos
	if localPos.LocalPosShieldWhiteRuleType == 3 {
		isTimeOK := isTimeMatch(localPos.LocalPosShieldWhiteTimeList)
		isRegionOK := isRegionMatch(mhReq.Device.IPProvince, localPos.LocalPosShieldWhiteRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalPosShieldWhiteRegionList)
		if isTimeOK && isRegionOK {
			return result
		}
	}

	// 检查关键词 - App
	if len(localPos.LocalAppShieldWhiteKey) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteKey, ",") {
			if len(item) > 0 && isKeywordMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查关键词 - Pos
	if len(localPos.LocalPosShieldWhiteKey) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteKey, ",") {
			if len(item) > 0 && isKeywordMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查包名 - App
	if len(localPos.LocalAppShieldWhitePackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhitePackageName, ",") {
			if len(item) > 0 && isPackageNameMatch(item, mhRespItem, mhReq.Device.Os) {
				return result
			}
		}
	}

	// 检查包名 - Pos
	if len(localPos.LocalPosShieldWhitePackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhitePackageName, ",") {
			if len(item) > 0 && isPackageNameMatch(item, mhRespItem, mhReq.Device.Os) {
				return result
			}
		}
	}

	// 检查广告URL - App
	if len(localPos.LocalAppShieldWhiteAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteAdURL, ",") {
			if len(item) > 0 && isAdURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查广告URL - Pos
	if len(localPos.LocalPosShieldWhiteAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteAdURL, ",") {
			if len(item) > 0 && isAdURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查素材URL - App
	if len(localPos.LocalAppShieldWhiteMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteMaterialURL, ",") {
			if len(item) > 0 && isMaterialURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查素材URL - Pos
	if len(localPos.LocalPosShieldWhiteMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteMaterialURL, ",") {
			if len(item) > 0 && isMaterialURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查监测URL - App
	if len(localPos.LocalAppShieldWhiteExpUrl) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteExpUrl, ",") {
			if len(item) > 0 && isExpURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	// 检查监测URL - Pos
	if len(localPos.LocalPosShieldWhiteExpUrl) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteExpUrl, ",") {
			if len(item) > 0 && isExpURLMatch(item, mhRespItem) {
				return result
			}
		}
	}

	return createErrorResult(900111, BlockTypeTimeRegion, "no_match")
}

// 检查关键词是否匹配黑名单规则，返回错误结果
func checkKeywordBlacklist(item string, mhRespItem *models.MHRespDataItem) *ShieldResult {
	// 检查标题
	if len(mhRespItem.Title) > 0 && strings.Contains(mhRespItem.Title, item) {
		return createErrorResult(900111, BlockTypeKeyword, item)
	}
	// 检查描述
	if len(mhRespItem.Description) > 0 && strings.Contains(mhRespItem.Description, item) {
		return createErrorResult(900112, BlockTypeKeyword, item)
	}
	return nil
}

// 检查包名是否匹配黑名单规则，返回错误结果
func checkPackageNameBlacklist(item string, mhRespItem *models.MHRespDataItem, deviceOS string) *ShieldResult {
	// 检查包名
	if len(mhRespItem.PackageName) > 0 && strings.Contains(mhRespItem.PackageName, item) {
		return createErrorResult(900114, BlockTypePackageName, item)
	}
	// 检查iOS特殊情况
	if deviceOS == "ios" {
		iosAdURL := mhRespItem.AdURL
		if strings.Contains(iosAdURL, "apple.com") && strings.Contains(iosAdURL, item) {
			return createErrorResult(900114, BlockTypePackageName, item)
		}
	}
	return nil
}

// 检查广告URL是否匹配黑名单规则，返回错误结果
func checkAdURLBlacklist(item string, mhRespItem *models.MHRespDataItem) *ShieldResult {
	// 检查落地页URL
	if len(mhRespItem.LandpageURL) > 0 && strings.Contains(mhRespItem.LandpageURL, item) {
		return createErrorResult(900113, BlockTypeAdURL, item)
	}
	// 检查下载URL
	if len(mhRespItem.DownloadURL) > 0 && strings.Contains(mhRespItem.DownloadURL, item) {
		return createErrorResult(900113, BlockTypeAdURL, item)
	}
	// 检查广告URL
	if len(mhRespItem.AdURL) > 0 && strings.Contains(mhRespItem.AdURL, item) {
		return createErrorResult(900113, BlockTypeAdURL, item)
	}
	// 检查深链接
	if len(mhRespItem.DeepLink) > 0 && strings.Contains(mhRespItem.DeepLink, item) {
		return createErrorResult(900113, BlockTypeAdURL, item)
	}
	return nil
}

// 检查素材URL是否匹配黑名单规则，返回错误结果
func checkMaterialURLBlacklist(item string, mhRespItem *models.MHRespDataItem) *ShieldResult {
	// 检查图片URL
	if len(mhRespItem.Image) > 0 && strings.Contains(mhRespItem.Image[0].URL, item) {
		return createErrorResult(900115, BlockTypeMaterialURL, item)
	}
	// 检查视频URL
	if mhRespItem.Video != nil {
		if len(mhRespItem.Video.VideoURL) > 0 && strings.Contains(mhRespItem.Video.VideoURL, item) {
			return createErrorResult(900115, BlockTypeMaterialURL, item)
		}
		if len(mhRespItem.Video.CoverURL) > 0 && strings.Contains(mhRespItem.Video.CoverURL, item) {
			return createErrorResult(900115, BlockTypeMaterialURL, item)
		}
	}
	// 检查图标URL
	if len(mhRespItem.IconURL) > 0 && strings.Contains(mhRespItem.IconURL, item) {
		return createErrorResult(900115, BlockTypeMaterialURL, item)
	}
	return nil
}

// 检查监测URL是否匹配黑名单规则，返回错误结果
func checkExpURLBlacklist(item string, mhRespItem *models.MHRespDataItem) *ShieldResult {
	if len(mhRespItem.ImpressionLink) > 0 {
		for _, impressionUrl := range mhRespItem.ImpressionLink {
			if strings.Contains(impressionUrl, item) {
				return createErrorResult(900117, BlockTypeExpUrl, item)
			}
		}
	}

	if len(mhRespItem.ClickLink) > 0 {
		for _, impressionUrl := range mhRespItem.ClickLink {
			if strings.Contains(impressionUrl, item) {
				return createErrorResult(900117, BlockTypeExpUrl, item)
			}
		}
	}

	return nil
}

// 检查素材是否符合黑名单规则，返回所有错误结果
func checkMaterialShieldBlack(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespItem *models.MHRespDataItem) []*ShieldResult {
	var results []*ShieldResult

	// 检查是否有黑名单规则
	if len(localPos.LocalAppShieldRuleID) == 0 && len(localPos.LocalPosShieldRuleID) == 0 {
		return results
	}

	// 检查黑名单是否激活
	if localPos.LocalAppShieldIsActive == 0 && localPos.LocalPosShieldIsActive == 0 {
		return results
	}

	// 广告类型屏蔽 app
	if localPos.LocalAppAdShieldType > 0 {
		// 下载
		if mhRespItem.InteractType == 1 && localPos.LocalAppAdShieldType == 1 {
			results = append(results, createErrorResult(900115, BlockTypeAdType, "download"))
		}
		// h5
		if mhRespItem.InteractType == 0 && localPos.LocalAppAdShieldType == 2 {
			results = append(results, createErrorResult(900115, BlockTypeAdType, "h5"))
		}
	}

	// 广告类型屏蔽 pos
	if localPos.LocalPosAdShieldType > 0 {
		// 下载
		if mhRespItem.InteractType == 1 && localPos.LocalPosAdShieldType == 1 {
			results = append(results, createErrorResult(900115, BlockTypeAdType, "download"))
		}
		// h5
		if mhRespItem.InteractType == 0 && localPos.LocalPosAdShieldType == 2 {
			results = append(results, createErrorResult(900115, BlockTypeAdType, "h5"))
		}
	}

	// 检查时间规则 - App
	if localPos.LocalAppShieldRuleType == 1 && isTimeMatch(localPos.LocalAppShieldTimeList) {
		results = append(results, createErrorResult(900115, BlockTypeTime, time.Now().Format("15")))
	}

	// 检查时间规则 - Pos
	if localPos.LocalPosShieldRuleType == 1 && isTimeMatch(localPos.LocalPosShieldTimeList) {
		results = append(results, createErrorResult(900115, BlockTypeTime, time.Now().Format("15")))
	}

	// 检查地域规则 - App
	if localPos.LocalAppShieldRuleType == 2 {
		if isRegionMatch(mhReq.Device.IPProvince, localPos.LocalAppShieldRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalAppShieldRegionList) {
			results = append(results, createErrorResult(900115, BlockTypeRegion, mhReq.Device.IPProvince))
		}
	}

	// 检查地域规则 - Pos
	if localPos.LocalPosShieldRuleType == 2 {
		if isRegionMatch(mhReq.Device.IPProvince, localPos.LocalPosShieldRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalPosShieldRegionList) {
			results = append(results, createErrorResult(900115, BlockTypeRegion, mhReq.Device.IPProvince))
		}
	}

	// 检查时间+地域规则 - App
	if localPos.LocalAppShieldRuleType == 3 {
		isTimeOK := isTimeMatch(localPos.LocalAppShieldTimeList)
		isRegionOK := isRegionMatch(mhReq.Device.IPProvince, localPos.LocalAppShieldRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalAppShieldRegionList)
		if isTimeOK && isRegionOK {
			results = append(results, createErrorResult(900115, BlockTypeTimeRegion, mhReq.Device.IPProvince+"_"+time.Now().Format("15")))
		}
	}

	// 检查时间+地域规则 - Pos
	if localPos.LocalPosShieldRuleType == 3 {
		isTimeOK := isTimeMatch(localPos.LocalPosShieldTimeList)
		isRegionOK := isRegionMatch(mhReq.Device.IPProvince, localPos.LocalPosShieldRegionList) ||
			isRegionMatch(mhReq.Device.IPCity, localPos.LocalPosShieldRegionList)
		if isTimeOK && isRegionOK {
			results = append(results, createErrorResult(900115, BlockTypeTimeRegion, mhReq.Device.IPProvince+"_"+time.Now().Format("15")))
		}
	}

	// 检查关键词 - App
	if len(localPos.LocalAppShieldKey) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldKey, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkKeywordBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查关键词 - Pos
	if len(localPos.LocalPosShieldKey) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldKey, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkKeywordBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查包名 - App
	if len(localPos.LocalAppShieldPackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldPackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkPackageNameBlacklist(item, mhRespItem, mhReq.Device.Os); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查包名 - Pos
	if len(localPos.LocalPosShieldPackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldPackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkPackageNameBlacklist(item, mhRespItem, mhReq.Device.Os); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查广告URL - App
	if len(localPos.LocalAppShieldAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkAdURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查广告URL - Pos
	if len(localPos.LocalPosShieldAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkAdURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查素材URL - App
	if len(localPos.LocalAppShieldMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkMaterialURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查素材URL - Pos
	if len(localPos.LocalPosShieldMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkMaterialURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查监测URL - App
	if len(localPos.LocalAppShieldExpUrl) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldExpUrl, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkExpURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	// 检查监测URL - Pos
	if len(localPos.LocalPosShieldExpUrl) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldExpUrl, ",") {
			if len(item) == 0 {
				continue
			}
			if result := checkExpURLBlacklist(item, mhRespItem); result != nil {
				results = append(results, result)
			}
		}
	}

	return results
}
