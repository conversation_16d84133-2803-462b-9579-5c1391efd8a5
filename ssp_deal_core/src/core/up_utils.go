package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"sort"
	"strconv"
	"strings"
	"time"

	deviceidgenerate "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate"
	deviceidgeneratemodel "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

/**
* Deprecated
* ! 今后建议迁移到utils
 */

// GetIconURLByDeeplink ...
func GetIconURLByDeeplink(deeplink string) string {
	if len(deeplink) == 0 {
		return "https://static.maplehaze.cn/static/logo_256.png"
	} else if strings.Contains(deeplink, "pinduoduo") {
		return "https://static.maplehaze.cn/adimg/pdd/pdd-icon.png"
	} else if strings.Contains(deeplink, "taobao") {
		return "https://static.maplehaze.cn/adimg/taobao/taobao-icon-512x512.png"
	}

	return "https://static.maplehaze.cn/static/logo_256.png"
}

// GetPackageNameByDeeplink ...
// 1、dp链接中带有pinduoduo替换：com.xunmeng.pinduoduo
// 2、dp链接中带有taobao替换：com.taobao.taobao
// 3、dp链接中带有jdmobile、jd.com、openjd替换：com.jingdong.app.mall
// 4、dp链接中带有vip.com替换：com.achievo.vipshop
func GetPackageNameByDeeplink(deeplink string) string {
	if len(deeplink) == 0 {
		return ""
	} else if strings.Contains(deeplink, "pinduoduo") {
		return "com.xunmeng.pinduoduo"
	} else if strings.Contains(deeplink, "taobao") {
		return "com.taobao.taobao"
	} else if strings.Contains(deeplink, "jdmobile") || strings.Contains(deeplink, "jd.com") || strings.Contains(deeplink, "openjd") {
		return "com.jingdong.app.mall"
	} else if strings.Contains(deeplink, "vip.com") {
		return "com.achievo.vipshop"
	}

	return ""
}

// GetReplaceDIDFlag ...
// 0 -> no replace
// 1 -> replace
func GetReplaceDIDFlag(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) int {
	if platformPos.PlatformAppIsReplaceDID == 0 {
		return 0
	}

	if len(platformPos.PlatformAppReplaceWhiteMedias) > 0 {
		isReplaceWhiteOK := false
		replaceWhiteMediaArray := strings.Split(platformPos.PlatformAppReplaceWhiteMedias, ",")
		for _, tmpReplaceWhiteMediaItem := range replaceWhiteMediaArray {
			if tmpReplaceWhiteMediaItem == localPos.LocalAppID {
				isReplaceWhiteOK = true
				break
			}
		}
		if isReplaceWhiteOK {
			if len(platformPos.PlatformAppReplaceWhiteMediasPlatformPos) == 0 {
				return 0
			} else {
				isReplaceWhitePlatformPosOK := false
				replaceWhiteMediaPlatformPosArray := strings.Split(platformPos.PlatformAppReplaceWhiteMediasPlatformPos, ",")
				for _, tmpReplaceWhiteMediaPlatformItem := range replaceWhiteMediaPlatformPosArray {
					if tmpReplaceWhiteMediaPlatformItem == platformPos.PlatformPosID {
						isReplaceWhitePlatformPosOK = true
						break
					}
				}

				if isReplaceWhitePlatformPosOK {
				} else {
					return 0
				}
			}
		}
	}

	randalue := rand.Intn(100)

	if randalue < platformPos.PlatformAppReplaceNoRatio {
		return 0
	}
	return 1
}

// GetReplaceDIDAndroidFromRedis ...
func GetReplaceDIDAndroidFromRedis(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu,
	platformPos *models.PlatformPosStu, replaceDIDFlag int, noUsed string) (string, error) {

	// 如果设置了magic的替换包md5最新版, 优先使用
	if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
		// 以下都用的是ip location库
		if mhReq.Device.LBSIPCountry == "中国" && len(mhReq.Device.LBSIPCity) > 1 {
		} else {
			return "", errors.New("ip is wrong")
		}

		if platformPos.PlatformAppIsReplaceDIDIP == 1 {
			if len(mhReq.Device.DIDMd5) == 0 {
				// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
				// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceipfailed0&",
				// 	localPos, platformPos, mhReq,
				// 	mhReq.Device.DIDMd5,
				// 	"wrong did error",
				// 	string(tmpDeviceByte),
				// 	"wrong did error")

				return "", errors.New("wrong did error")
			}
			tmpReplaceKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_KEY,
				mhReq.Device.DIDMd5,
				platformPos.PlatformAppID,
				mhReq.Device.IP,
				strings.ToLower(strings.Replace(mhReq.Device.OsVersion, " ", "", -1)),
				strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
			tmpRedisValue, tmpRedisErr := db.GlbRedis.Get(c, tmpReplaceKey).Result()
			if tmpRedisErr != nil {
			} else {
				var didRedisData models.ReplaceDIDStu
				json.Unmarshal([]byte(tmpRedisValue), &didRedisData)
				didRedisData.Os = "android"

				// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
				// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceip0&"+tmpReplaceKey,
				// 	localPos, platformPos, mhReq,
				// 	mhReq.Device.DIDMd5,
				// 	GetReplaceDeviceIDMd5Key(didRedisData),
				// 	string(tmpDeviceByte),
				// 	tmpRedisValue)

				return tmpRedisValue, tmpRedisErr
			}
		}

		redisDIDValue, redisErr := GetMagicReplaceDIDLibAndroidFromRedis(c, mhReq, localPos, platformPos, mhReq.Device.LBSIPCity)
		if redisErr != nil {
		} else {
			var didRedisData models.ReplaceDIDStu
			json.Unmarshal([]byte(redisDIDValue), &didRedisData)

			didRedisData.Os = "android"
			///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
			if platformPos.PlatformAppIsReplaceDIDIP == 1 {
				replaceDIDMd5Key := GetReplaceDeviceIDMd5Key(didRedisData)

				exHGETDIDIPKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_IP_KEY,
					replaceDIDMd5Key,
					platformPos.PlatformAppID)

				exHGETFieldMaxIPNum := "max_ip_num"
				exHGETFieldMaxIPArray := "ip_array"

				// 记录单设备ip跳动
				result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETDIDIPKey, exHGETFieldMaxIPArray)
				if redisErr != nil {
					var ipArray []string
					ipArray = append(ipArray, mhReq.Device.IP)

					tmpJSON, _ := json.Marshal(ipArray)
					db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPArray, tmpJSON)
					db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPNum, len(ipArray))
				} else {
					var ipArray []string
					json.Unmarshal([]byte(result.(string)), &ipArray)

					isIPOK := false
					for _, tmpIPItem := range ipArray {
						if tmpIPItem == mhReq.Device.IP {
							isIPOK = true
							break
						}
					}

					if isIPOK {
					} else {
						if len(ipArray) >= platformPos.PlatformAppMaxNumReplaceDIDIP {
							// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
							// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceipfailed1&"+exHGETDIDIPKey,
							// 	localPos, platformPos, mhReq,
							// 	mhReq.Device.DIDMd5,
							// 	replaceDIDMd5Key,
							// 	string(tmpDeviceByte),
							// 	redisDIDValue+"_"+result.(string)+"_"+mhReq.Device.IP+"_"+"max ip num error")

							return "", errors.New("max ip num error")
						} else {
							ipArray = append(ipArray, mhReq.Device.IP)
							ipArray = removeDuplicates(ipArray)

							tmpJSON, _ := json.Marshal(ipArray)
							db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPArray, tmpJSON)
							db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPNum, len(ipArray))
						}
					}
				}

				// debug
				// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
				// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceip1&"+exHGETDIDIPKey,
				// 	localPos, platformPos, mhReq,
				// 	mhReq.Device.DIDMd5,
				// 	replaceDIDMd5Key,
				// 	string(tmpDeviceByte),
				// 	redisDIDValue)

				// 设置单设备保持精准替换
				tmpReplaceKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_KEY,
					mhReq.Device.DIDMd5,
					platformPos.PlatformAppID,
					mhReq.Device.IP,
					strings.ToLower(strings.Replace(mhReq.Device.OsVersion, " ", "", -1)),
					strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
				db.GlbRedis.Set(c, tmpReplaceKey, redisDIDValue, time.Duration(platformPos.PlatformAppReplaceDIDIPTTL)*time.Hour)

				db.GlbRedis.Expire(c, exHGETDIDIPKey, time.Duration(platformPos.PlatformAppReplaceDIDIPTTL)*time.Hour)
			} else {
				exHGETKey := "replace_" + platformPos.PlatformAppID + "_" + GetReplaceDeviceIDMd5Key(didRedisData) + "_" + time.Now().Format("20060102")

				// 判定单设备替换最大次数开关
				if platformPos.PlatformAppIsReplaceDIDMaxNum == 1 {
					exHGETFieldMaxNum := "max_num"

					redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETFieldMaxNum)
					if redisErr != nil {
					} else {
						redisMaxNum := utils.ConvertStringToInt(redisResult.(string))
						if redisMaxNum > platformPos.PlatformAppMaxNumReplaceDID {
							// go func() {
							// 	defer func() {
							// 		if err := recover(); err != nil {
							// 			fmt.Println("replace did panic:", err)
							// 		}
							// 	}()
							// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), redisMaxNum, 0, "", 0, "fail max num")
							// }()

							return "", errors.New("max num error")
						}
					}

					// 增加单设备替换最大次数
					db.GlbRedis.Do(c, "EXHINCRBY", exHGETKey, exHGETFieldMaxNum, 1)
					go func() {
						defer func() {
							if err := recover(); err != nil {
								fmt.Println("redis panic:", err)
							}
						}()
						randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
						db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
					}()
				}

				// 单设备替换地域跳动最大次数开关
				if platformPos.PlatformAppIsReplaceDIDDiffRegion == 1 {
					exHGETFieldMaxRegionNum := "max_region_num"
					exHGETFieldMaxRegionArray := "region_array"

					// 增加单设备替换地域跳动最大次数
					result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETFieldMaxRegionArray)
					if redisErr != nil {
						var regionArray []string
						regionArray = append(regionArray, mhReq.Device.LBSIPCity)

						tmpJSON, _ := json.Marshal(regionArray)
						db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionArray, tmpJSON)
						db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionNum, len(regionArray))
						go func() {
							defer func() {
								if err := recover(); err != nil {
									fmt.Println("redis panic:", err)
								}
							}()
							randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
							db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
						}()
					} else {
						var regionArray []string
						json.Unmarshal([]byte(result.(string)), &regionArray)

						isRegionOK := false
						for _, tmpRegionItem := range regionArray {
							if tmpRegionItem == mhReq.Device.LBSIPCity {
								isRegionOK = true
								break
							}
						}

						if isRegionOK {
						} else {
							if len(regionArray) >= platformPos.PlatformAppMaxNumReplaceDIDDiffRegion {
								// go func() {
								// 	defer func() {
								// 		if err := recover(); err != nil {
								// 			fmt.Println("max region num panic:", err)
								// 		}
								// 	}()

								// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), 0, len(regionArray), result.(string)+"_"+mhReq.Device.LBSIPCity, 0, "fail max diff region num")
								// }()

								return "", errors.New("max region num error")
							}

							regionArray = append(regionArray, mhReq.Device.LBSIPCity)
							regionArray = removeDuplicates(regionArray)

							tmpJSON, _ := json.Marshal(regionArray)
							db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionArray, tmpJSON)
							db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionNum, len(regionArray))
							go func() {
								defer func() {
									if err := recover(); err != nil {
										fmt.Println("redis panic:", err)
									}
								}()
								randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
								db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
							}()
						}
					}
				}
			}

			// go func() {
			// 	defer func() {
			// 		if err := recover(); err != nil {
			// 			fmt.Println("replace did panic:", err)
			// 		}
			// 	}()
			// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), 0, 0, "", 1, "")
			// }()

			// 万分之一概率保存请求数据，同时k8s配置100%保存的did
			// isDebug := false
			// for _, tmpDebugItem := range utilities.DebugReplaceOAIDList {
			// 	if tmpDebugItem == didRedisData.Oaid {
			// 		isDebug = true
			// 		break
			// 	}
			// }

			// for _, tmpDebugItem := range utilities.DebugReplacePlatformAppIDList {
			// 	if platformPos.PlatformAppID == tmpDebugItem {
			// 		isDebug = true
			// 		break
			// 	}
			// }

			// if isDebug {
			// } else {
			// 	if rand.Intn(10000) == 0 {
			// 		isDebug = true
			// 	}
			// }

			// if isDebug {
			// 	go func() {
			// 		defer func() {
			// 			if err := recover(); err != nil {
			// 				fmt.Println("debug replace did panic:", err)
			// 			}
			// 		}()
			// 		models.SaveDebugReplaceAndroidData(c, mhReq, localPos, platformPos, didRedisData)
			// 	}()
			// }
			///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
		}

		return redisDIDValue, redisErr
	}

	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	return "", errors.New("old replace did error")
}

func GetMagicReplaceDIDLibAndroidFromRedis(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu,
	platformPos *models.PlatformPosStu, city string) (string, error) {

	if mhReq.Device.Os == "android" {
	} else {
		return "", errors.New("no replace did key")
	}

	// osv model key
	osvKey := strings.Replace(mhReq.Device.OsVersion, " ", "", -1)
	modelKey := strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1))

	exhashBaseKey := "ssp_mrlib_" + platformPos.PlatformAppMagicReplaceDIDLibMd5Key + "_" + osvKey + "_" + modelKey + "_" + city
	if strings.Contains(platformPos.PlatformAppMagicReplaceDIDLibMd5Key, ",") {
		magicReplaceDIDLibMd5KeyArray := strings.Split(platformPos.PlatformAppMagicReplaceDIDLibMd5Key, ",")
		if len(magicReplaceDIDLibMd5KeyArray) != 2 {
			return "", errors.New("wrong replace did key")
		}
		if magicReplaceDIDLibMd5KeyArray[1] == "0" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + osvKey + "_" + modelKey + "_" + city
		} else if magicReplaceDIDLibMd5KeyArray[1] == "1" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + osvKey + "_" + city
		} else if magicReplaceDIDLibMd5KeyArray[1] == "2" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + city
		}
	}

	redisDIDValue, redisErr := models.GetMagicReplaceDIDLibFromRedis(c, mhReq.Device.DIDMd5, exhashBaseKey, mhReq, localPos, platformPos)
	return redisDIDValue, redisErr
}

// GetReplaceDIDIOSFromRedis ...
func GetReplaceDIDIOSFromRedis(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, replaceDIDFlag int) (string, error) {
	// 如果设置了magic的替换包md5最新版, 优先使用
	if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
		// 以下都用的是ip location库
		if mhReq.Device.LBSIPCountry == "中国" && len(mhReq.Device.LBSIPCity) > 1 {
		} else {
			return "", errors.New("ip is wrong")
		}

		if platformPos.PlatformAppIsReplaceDIDIP == 1 {
			if len(mhReq.Device.DIDMd5) == 0 {
				return "", errors.New("wrong did error")
			}
			tmpReplaceKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_KEY,
				mhReq.Device.DIDMd5,
				platformPos.PlatformAppID,
				mhReq.Device.IP,
				strings.ToLower(strings.Replace(mhReq.Device.OsVersion, " ", "", -1)),
				strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
			tmpRedisValue, tmpRedisErr := db.GlbRedis.Get(c, tmpReplaceKey).Result()
			if tmpRedisErr != nil {
			} else {
				var didRedisData models.ReplaceDIDStu
				json.Unmarshal([]byte(tmpRedisValue), &didRedisData)
				didRedisData.Os = "ios"

				// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
				// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceip0&"+tmpReplaceKey,
				// 	localPos, platformPos, mhReq,
				// 	mhReq.Device.DIDMd5,
				// 	GetReplaceDeviceIDMd5Key(didRedisData),
				// 	string(tmpDeviceByte),
				// 	tmpRedisValue)

				return tmpRedisValue, tmpRedisErr
			}
		}

		redisDIDValue, redisErr := GetMagicReplaceDIDLibIOSFromRedis(c, mhReq, localPos, platformPos, mhReq.Device.LBSIPCity)
		if redisErr != nil {
		} else {
			var didRedisData models.ReplaceDIDStu
			json.Unmarshal([]byte(redisDIDValue), &didRedisData)

			didRedisData.Os = "ios"
			///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
			if platformPos.PlatformAppIsReplaceDIDIP == 1 {
				replaceDIDMd5Key := GetReplaceDeviceIDMd5Key(didRedisData)

				exHGETDIDIPKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_IP_KEY,
					replaceDIDMd5Key,
					platformPos.PlatformAppID)

				exHGETFieldMaxIPNum := "max_ip_num"
				exHGETFieldMaxIPArray := "ip_array"

				// 记录单设备ip跳动
				result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETDIDIPKey, exHGETFieldMaxIPArray)
				if redisErr != nil {
					var ipArray []string
					ipArray = append(ipArray, mhReq.Device.IP)

					tmpJSON, _ := json.Marshal(ipArray)
					db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPArray, tmpJSON)
					db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPNum, len(ipArray))
				} else {
					var ipArray []string
					json.Unmarshal([]byte(result.(string)), &ipArray)

					isIPOK := false
					for _, tmpIPItem := range ipArray {
						if tmpIPItem == mhReq.Device.IP {
							isIPOK = true
							break
						}
					}

					if isIPOK {
					} else {
						if len(ipArray) >= platformPos.PlatformAppMaxNumReplaceDIDIP {
							return "", errors.New("max ip num error")
						} else {
							ipArray = append(ipArray, mhReq.Device.IP)
							ipArray = removeDuplicates(ipArray)

							tmpJSON, _ := json.Marshal(ipArray)
							db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPArray, tmpJSON)
							db.GlbRedis.Do(c, "EXHSET", exHGETDIDIPKey, exHGETFieldMaxIPNum, len(ipArray))
						}
					}
				}

				// debug
				// tmpDeviceByte, _ := json.Marshal(mhReq.Device)
				// go models.BigDataHoloDebugReplaceIP(uuid.NewV4().String()+"&replaceip1&"+exHGETDIDIPKey,
				// 	localPos, platformPos, mhReq,
				// 	mhReq.Device.DIDMd5,
				// 	replaceDIDMd5Key,
				// 	string(tmpDeviceByte),
				// 	redisDIDValue)

				// 设置单设备保持精准替换
				tmpReplaceKey := fmt.Sprintf(rediskeys.ADX_SSP_REPLACE_UNIQUE_DID_KEY,
					mhReq.Device.DIDMd5,
					platformPos.PlatformAppID,
					mhReq.Device.IP,
					strings.ToLower(strings.Replace(mhReq.Device.OsVersion, " ", "", -1)),
					strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
				db.GlbRedis.Set(c, tmpReplaceKey, redisDIDValue, time.Duration(platformPos.PlatformAppReplaceDIDIPTTL)*time.Hour)

				db.GlbRedis.Expire(c, exHGETDIDIPKey, time.Duration(platformPos.PlatformAppReplaceDIDIPTTL)*time.Hour)
			} else {
				exHGETKey := "replace_" + platformPos.PlatformAppID + "_" + GetReplaceDeviceIDMd5Key(didRedisData) + "_" + time.Now().Format("20060102")

				// 判定单设备替换最大次数开关
				if platformPos.PlatformAppIsReplaceDIDMaxNum == 1 {
					exHGETFieldMaxNum := "max_num"

					redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETFieldMaxNum)
					if redisErr != nil {
					} else {
						redisMaxNum := utils.ConvertStringToInt(redisResult.(string))
						if redisMaxNum > platformPos.PlatformAppMaxNumReplaceDID {
							// go func() {
							// 	defer func() {
							// 		if err := recover(); err != nil {
							// 			fmt.Println("replace did panic:", err)
							// 		}
							// 	}()
							// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), redisMaxNum, 0, "", 0, "fail max num")
							// }()

							return "", errors.New("max num error")
						}
					}

					// 增加单设备替换最大次数
					db.GlbRedis.Do(c, "EXHINCRBY", exHGETKey, exHGETFieldMaxNum, 1)

					go func() {
						defer func() {
							if err := recover(); err != nil {
								fmt.Println("redis panic:", err)
							}
						}()
						randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
						db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
					}()
				}

				// 单设备替换地域跳动最大次数开关
				if platformPos.PlatformAppIsReplaceDIDDiffRegion == 1 {
					exHGETFieldMaxRegionNum := "max_region_num"
					exHGETFieldMaxRegionArray := "region_array"

					// 增加单设备替换地域跳动最大次数
					result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETFieldMaxRegionArray)
					if redisErr != nil {
						var regionArray []string
						regionArray = append(regionArray, mhReq.Device.LBSIPCity)

						tmpJSON, _ := json.Marshal(regionArray)
						db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionArray, tmpJSON)
						db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionNum, len(regionArray))

						go func() {
							defer func() {
								if err := recover(); err != nil {
									fmt.Println("redis panic:", err)
								}
							}()
							randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
							db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
						}()
					} else {
						var regionArray []string
						json.Unmarshal([]byte(result.(string)), &regionArray)

						isRegionOK := false
						for _, tmpRegionItem := range regionArray {
							if tmpRegionItem == mhReq.Device.LBSIPCity {
								isRegionOK = true
								break
							}
						}

						if isRegionOK {
						} else {
							if len(regionArray) >= platformPos.PlatformAppMaxNumReplaceDIDDiffRegion {
								// go func() {
								// 	defer func() {
								// 		if err := recover(); err != nil {
								// 			fmt.Println("max region num panic:", err)
								// 		}
								// 	}()

								// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), 0, len(regionArray), result.(string)+"_"+mhReq.Device.LBSIPCity, 0, "fail max diff region num")
								// }()

								return "", errors.New("max region num error")
							}

							regionArray = append(regionArray, mhReq.Device.LBSIPCity)
							regionArray = removeDuplicates(regionArray)

							tmpJSON, _ := json.Marshal(regionArray)
							db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionArray, tmpJSON)
							db.GlbRedis.Do(c, "EXHSET", exHGETKey, exHGETFieldMaxRegionNum, len(regionArray))

							go func() {
								defer func() {
									if err := recover(); err != nil {
										fmt.Println("redis panic:", err)
									}
								}()
								randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
								db.GlbRedis.Expire(c, exHGETKey, randTTLKey).Result()
							}()
						}
					}
				}
			}
			// go func() {
			// 	defer func() {
			// 		if err := recover(); err != nil {
			// 			fmt.Println("replace did panic:", err)
			// 		}
			// 	}()
			// 	models.SaveDebugMagicReplaceData(c, mhReq, localPos, platformPos, exHGETKey, string(redisDIDValue), 0, 0, "", 1, "")
			// }()

			// 万分之一概率保存请求数据，同时k8s配置100%保存的did
			// isDebug := false
			// for _, tmpDebugItem := range utilities.DebugReplaceCAIDList {
			// 	if strings.Contains(didRedisData.CAIDMultiJson, tmpDebugItem) {
			// 		isDebug = true
			// 		break
			// 	}
			// }

			// for _, tmpDebugItem := range utilities.DebugReplacePlatformAppIDList {
			// 	if platformPos.PlatformAppID == tmpDebugItem {
			// 		isDebug = true
			// 		break
			// 	}
			// }

			// if isDebug {
			// } else {
			// 	if rand.Intn(10000) == 0 {
			// 		isDebug = true
			// 	}
			// }

			// if isDebug {
			// 	go func() {
			// 		defer func() {
			// 			if err := recover(); err != nil {
			// 				fmt.Println("debug replace did panic:", err)
			// 			}
			// 		}()
			// 		models.SaveDebugReplaceIOSData(c, mhReq, localPos, platformPos, didRedisData)
			// 	}()
			// }

			///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
		}

		return redisDIDValue, redisErr
	}

	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	return "", errors.New("old replace did error")
}

func GetMagicReplaceDIDLibIOSFromRedis(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu,
	platformPos *models.PlatformPosStu, city string) (string, error) {

	if mhReq.Device.Os == "ios" {
	} else {
		return "", errors.New("no replace did key")
	}

	// osv model key
	osvKey := strings.Replace(mhReq.Device.OsVersion, " ", "", -1)
	modelKey := strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1))

	exhashBaseKey := "ssp_mrlib_" + platformPos.PlatformAppMagicReplaceDIDLibMd5Key + "_" + osvKey + "_" + modelKey + "_" + city
	if strings.Contains(platformPos.PlatformAppMagicReplaceDIDLibMd5Key, ",") {
		magicReplaceDIDLibMd5KeyArray := strings.Split(platformPos.PlatformAppMagicReplaceDIDLibMd5Key, ",")
		if len(magicReplaceDIDLibMd5KeyArray) != 2 {
			return "", errors.New("wrong replace did key")
		}
		if magicReplaceDIDLibMd5KeyArray[1] == "0" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + osvKey + "_" + modelKey + "_" + city
		} else if magicReplaceDIDLibMd5KeyArray[1] == "1" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + osvKey + "_" + city
		} else if magicReplaceDIDLibMd5KeyArray[1] == "2" {
			exhashBaseKey = "ssp_mrlib_" + magicReplaceDIDLibMd5KeyArray[0] + "_" + city
		}
	}

	redisDIDValue, redisErr := models.GetMagicReplaceDIDLibFromRedis(c, mhReq.Device.DIDMd5, exhashBaseKey, mhReq, localPos, platformPos)
	return redisDIDValue, redisErr
}

// IsMaterialFilterByVideoDuration 是否根据视频时长配置过滤
func IsMaterialFilterByVideoDuration(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	videoDuration int) (bool, int) {
	if localPos.LocalPosIsMaterialFilterDuration == 1 {
		configMinDuration := utils.ConvertStringToInt(localPos.LocalPosMaterialFilterMinDuration)
		configMaxDuration := utils.ConvertStringToInt(localPos.LocalPosMaterialFilterMaxDuration)
		if configMinDuration > 0 {
			if videoDuration < configMinDuration {
				return true, 900203
			}
		}
		if configMaxDuration > 0 {
			if videoDuration > configMaxDuration {
				return true, 900203
			}
		}
	}
	if localPos.LocalPosIsMaterialFilterFixedDuration == 1 {
		if len(localPos.LocalPosMaterialFilterFixedDurations) > 0 {
			fixedDurationsArray := strings.Split(localPos.LocalPosMaterialFilterFixedDurations, ",")
			isExist := false
			for _, tmpItem := range fixedDurationsArray {
				if utils.ConvertStringToInt(tmpItem) == videoDuration {
					isExist = true
				}
			}
			if isExist {
			} else {
				return true, 900203
			}
		}
	}
	return false, 0
}

// IsMaterialFilterByOrientationAndSize ...
// 1. 只针对上游返回宽高做过滤
// 2. 素材方向过滤: 仅横向, 只要横向; 仅竖向, 只要竖向
func IsMaterialFilterByOrientationAndSize(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	materialWidth int, materialHeight int) (bool, int) {
	if localPos.LocalPosIsMaterialFilterOrientation == 1 {
		// 仅横向
		if materialWidth <= materialHeight {
			return true, 900201
		}

	} else if localPos.LocalPosIsMaterialFilterOrientation == 2 {
		// 仅竖向
		if materialWidth >= materialHeight {
			return true, 900201
		}
	}

	if len(localPos.LocalPosMaterialFilterSizes) > 0 {
		filterSizeArray := strings.Split(localPos.LocalPosMaterialFilterSizes, ",")
		isExist := false
		for _, tmpItem := range filterSizeArray {
			if len(tmpItem) > 0 {
				tmpSize := strings.Split(tmpItem, "*")
				tmpWidth := utils.ConvertStringToInt(tmpSize[0])
				tmpHeight := utils.ConvertStringToInt(tmpSize[1])
				if tmpWidth == materialWidth && tmpHeight == materialHeight {
					isExist = true
				}
			}
		}
		if isExist {
		} else {
			return true, 900202
		}
	}

	return false, 0
}

// GetReplaceManufactureConfig ...
// GET ssp_manufacturer_packages_huawei
// GET ssp_manufacturer_packages_xiaomi
// GET ssp_manufacturer_packages_oppo
// GET ssp_manufacturer_packages_vivo
// GET ssp_manufacturer_packages_lenovo
// GET ssp_manufacturer_packages_meizu
// GET ssp_manufacturer_packages_honor
func GetReplaceManufactureConfig(c context.Context, cacheKey string) []string {
	var manufactureArray []string

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)

		redisValue, redisErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisErr != nil {
			// fmt.Println("redis error:", redisErr)
			db.GlbBigCache.Set(cacheKey, []byte(""))
		} else {
			cacheValue = []byte(redisValue)
			db.GlbBigCache.Set(cacheKey, []byte(redisValue))
		}
	}

	json.Unmarshal(cacheValue, &manufactureArray)
	return manufactureArray
}

// GetManufactureType 获取供应商类型
func GetManufactureType(c context.Context, mhReq *models.MHReq) string {
	oppoManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"oppo")
	if len(oppoManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range oppoManufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return "oppo"
			}
		}
	}
	vivoManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"vivo")
	if len(vivoManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range vivoManufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return "vivo"
			}
		}
	}
	honorManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"honor")
	if len(honorManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range honorManufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return "honor"
			}
		}
	}
	hwManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"huawei")
	if len(hwManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range hwManufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return "huawei"
			}
		}
	}
	xiaomiManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"xiaomi")
	if len(xiaomiManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range xiaomiManufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return "xiaomi"
			}
		}
	}
	return ""
}

// IsAllInOneLimitOK
func IsAllInOneLimitOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataExtra *models.MHUpRespExtra) (bool, int) {
	// category是否关闭
	if categoryInfo.IsActive == 1 {
	} else {
		fmt.Println("category close:", platformPos.PlatformAppID)
		return false, 900103
	}

	// 上游p_app_id是否关闭
	if platformPos.PlatformAppIsActive == 1 {
	} else {
		fmt.Println("p_app_id close:", platformPos.PlatformAppID)
		return false, 900103
	}

	// 上游p_pos_id是否关闭
	if platformPos.PlatformPosIsActive == 1 {
	} else {
		fmt.Println("p_pos_id close", platformPos.PlatformPosID)
		return false, 900103
	}

	// 过滤请求比重
	if platformPos.PlatformAppReqFilterWeight > 0 {
		if rand.Intn(100) < platformPos.PlatformAppReqFilterWeight {
			return false, 900002
		}
	}

	isPlatformPolicyOK := models.IsPlatformPolicyAPIOK(c, mhReq, localPos, platformPos.PlatformMediaID, platformPos.PlatformAppCorpID)
	if isPlatformPolicyOK {
	} else {
		return false, 900010
	}

	// 判定max req
	if categoryInfo.MaxReqNum > 0 {
		maxReqRedisKey := "max_req_" + time.Now().Format("2006-01-02") + "_" + localPos.LocalAppID + "_" + localPos.LocalPosID + "_" + platformPos.PlatformAppID + "_" + platformPos.PlatformPosID
		maxReqRedisValue, cacheErr := models.CacheFromRedis(c, maxReqRedisKey)

		if cacheErr != nil {
			// fmt.Println("redis error:", redisErr)
		} else {
			if utils.ConvertStringToInt(maxReqRedisValue) == 0 {
				fmt.Println("category max req redis key value: ", localPos.LocalPosID, platformPos.PlatformPosID, maxReqRedisKey, maxReqRedisValue)
				return false, 900002
			}
		}
	}

	// 判定max did req
	if categoryInfo.IsMaxDIDReqNum == 1 && categoryInfo.MaxDIDReqNum > 0 {
		// 比较
		exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_STATISTICS_DID_PREFIX, mhReq.Device.DIDMd5)
		exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_STATISTICS_SUPPLY_DID_REQ_NUMBER_FIELDKEY, localPos.LocalPosID, platformPos.PlatformPosID, time.Now().Format("2006-01-02"))

		redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

		if redisErr != nil {
		} else {
			redisMaxNumStr := redisResult.(string)
			redisMaxNum := utils.ConvertStringToInt(redisMaxNumStr)

			if redisMaxNum >= categoryInfo.MaxDIDReqNum {
				// if platformPos.PlatformAppID == "1121853624" {
				// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_a", "a", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
				// }
				return false, 900010
			}
		}

		randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
		db.GlbRedis.Expire(c, exHGETKey, randTTLKey)
	}

	// 判定max exp
	if categoryInfo.MaxExpNum > 0 {
		maxExpRedisKey := "max_exp_" + time.Now().Format("2006-01-02") + "_" + localPos.LocalAppID + "_" + localPos.LocalPosID + "_" + platformPos.PlatformAppID + "_" + platformPos.PlatformPosID
		maxExpRedisValue, cacheErr := models.CacheFromRedis(c, maxExpRedisKey)

		if cacheErr != nil {
			// fmt.Println("redis error:", redisErr)
		} else {
			if utils.ConvertStringToInt(maxExpRedisValue) == 0 {
				fmt.Println("category max exp redis key value: ", localPos.LocalPosID, platformPos.PlatformPosID, maxExpRedisKey, maxExpRedisValue)
				return false, 900003
			}
		}
	}

	// 判定max clk
	if categoryInfo.MaxClkNum > 0 {
		maxClkRedisKey := "max_clk_" + time.Now().Format("2006-01-02") + "_" + localPos.LocalAppID + "_" + localPos.LocalPosID + "_" + platformPos.PlatformAppID + "_" + platformPos.PlatformPosID
		maxClkRedisValue, cacheErr := models.CacheFromRedis(c, maxClkRedisKey)

		if cacheErr != nil {
			// fmt.Println("redis error:", redisErr)
		} else {
			if utils.ConvertStringToInt(maxClkRedisValue) == 0 {
				fmt.Println("category max clk redis key value: ", localPos.LocalPosID, platformPos.PlatformPosID, maxClkRedisKey, maxClkRedisValue)
				return false, 900004
			}
		}
	}

	// 判定地域
	if len(categoryInfo.WhiteList) > 0 || len(categoryInfo.BlackList) > 0 {
		if strings.Contains(mhReq.Device.IP, ":") {
		} else {
			// 地域白名单
			if len(categoryInfo.WhiteList) > 0 {
				if strings.Contains(categoryInfo.WhiteList, mhReq.Device.IPProvince) || strings.Contains(categoryInfo.WhiteList, mhReq.Device.IPCity) {
				} else {
					fmt.Println("category white failed:", localPos.LocalPosID, platformPos.PlatformPosID)
					return false, 900108

				}
			}
			// 地域黑名单
			if len(categoryInfo.BlackList) > 0 {
				if strings.Contains(categoryInfo.BlackList, mhReq.Device.IPProvince) || strings.Contains(categoryInfo.BlackList, mhReq.Device.IPCity) {
					fmt.Println("category black failed:", localPos.LocalPosID, platformPos.PlatformPosID)
					return false, 900108
				}
			}
		}
	}

	// 判定厂商白名单
	if mhReq.Device.Os == "android" {
		isManufacturerOK, isManufacturerOKErrCode := IsManufacturerCategoryOK(c, mhReq, categoryInfo)
		if isManufacturerOK {
		} else {
			fmt.Println("category manufacturer failed:", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID, mhReq.Device.Manufacturer)
			return false, isManufacturerOKErrCode
		}
	}

	// sdk限制
	if len(mhReq.SDKVersion) > 0 {
		// fmt.Println(sdkVersion)
		if len(categoryInfo.WhiteVersion) > 0 {
			tmpSDKVersionArray := strings.Split(mhReq.SDKVersion, ".")
			tmpWhiteVersionArray := strings.Split(categoryInfo.WhiteVersion, ".")

			if len(tmpSDKVersionArray) > 0 && len(tmpWhiteVersionArray) > 0 {
				if utils.IsNum(tmpSDKVersionArray[0]) && utils.IsNum(tmpWhiteVersionArray[0]) {
					tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[0])
					tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[0])
					if tmp1 < tmp2 {
						return false, 900009
					}
				}
			}
			if len(tmpSDKVersionArray) > 1 && len(tmpWhiteVersionArray) > 1 {
				if utils.IsNum(tmpSDKVersionArray[1]) && utils.IsNum(tmpWhiteVersionArray[1]) {
					tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[1])
					tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[1])
					if tmp1 < tmp2 {
						return false, 900009
					}
				}
			}
			if len(tmpSDKVersionArray) > 2 && len(tmpWhiteVersionArray) > 2 {
				if utils.IsNum(tmpSDKVersionArray[2]) && utils.IsNum(tmpWhiteVersionArray[2]) {
					tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[2])
					tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[2])
					if tmp1 < tmp2 {
						return false, 900009
					}
				}
			}
			if len(tmpSDKVersionArray) > 3 && len(tmpWhiteVersionArray) > 3 {
				if utils.IsNum(tmpSDKVersionArray[3]) && utils.IsNum(tmpWhiteVersionArray[3]) {
					tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[3])
					tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[3])
					if tmp1 < tmp2 {
						return false, 900009
					}
				}
			}
		}
		if len(categoryInfo.BlackVersion) > 0 {
			if strings.Contains(categoryInfo.BlackVersion, mhReq.SDKVersion) {
				return false, 900009
			}
		}
	}

	// 反作弊
	_, anticheatAPICacheError := db.GlbBigCacheMinute.Get(fmt.Sprintf(config.AnticheatConfigAPI, platformPos.PlatformMediaID))
	if anticheatAPICacheError != nil {
	} else {
		// redis
		tmpRedisKey := fmt.Sprintf(rediskeys.ADX_SSP_ANTICHEAT_KEY, mhReq.Device.DIDMd5)
		_, tmpRedisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()
		if tmpRedisErr != nil {
		} else {
			// go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"&antichet_api", mhReq.Device.DIDMd5, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			return false, 900005
		}
	}

	// 是否请求限频
	if platformPos.PlatformPosIsReqLimitFrequency == 1 {
		isReqLimitFrequencyWhiteOK := false
		for _, item := range platformPos.PlatformPosReqLimitFrequencyWhiteMediasConfig {
			if item.LocalAppID == localPos.LocalAppID {
				randalue := rand.Intn(100)
				if randalue < item.Weight {
					isReqLimitFrequencyWhiteOK = true
				}
				break
			}
		}
		if isReqLimitFrequencyWhiteOK {
		} else {
			if len(platformPos.PlatformPosReqLimitFrequencyConfigJson) > 0 {
				var reqLimitFrequencyArray []models.ReqLimitFrequency1Stu
				json.Unmarshal([]byte(platformPos.PlatformPosReqLimitFrequencyConfigJson), &reqLimitFrequencyArray)
				currentHour := time.Now().Hour()
				currentMinute := time.Now().Minute()
				if len(reqLimitFrequencyArray) > 0 {
					for _, item := range reqLimitFrequencyArray {
						if (60*item.BeginHour+item.BeginMinute) <= (60*currentHour+currentMinute) &&
							(60*currentHour+currentMinute) <= (60*item.EndHour+item.EndMinute) {
							randValue := rand.Intn(10000)
							if randValue < item.Rate {
							} else {
								// if platformPos.PlatformAppID == "1121853624" {
								// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_b", "b", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
								// }
								return false, 900010
							}
						}
					}
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 是否通过人群包
	if IsCategoryCrowdPackageOK(c, mhReq, localPos, categoryInfo, bigdataExtra) {
	} else {
		return false, 900012
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 是否通过人群包
	if IsCrowdPackageOK(c, mhReq, localPos, platformPos, bigdataExtra) {
	} else {
		return false, 900012
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////

	// 判定上游p_app_id ip 最大请求数
	isLimitIPReqOK, isLimitIPReqErrCode := IsLimitIPReqOK(c, mhReq, localPos, platformPos)
	if isLimitIPReqOK {
	} else {
		return false, isLimitIPReqErrCode
	}

	// 判定上游p_app_id ip 最大曝光数
	isLimitIPExpOK, isLimitIPExpErrCode := IsLimitIPExpOK(c, mhReq, localPos, platformPos)
	if isLimitIPExpOK {
	} else {
		return false, isLimitIPExpErrCode
	}

	// 判定上游p_app_id ip 最大点击数
	isLimitIPClkOK, isLimitIPClkErrCode := IsLimitIPClkOK(c, mhReq, localPos, platformPos)
	if isLimitIPClkOK {
	} else {
		return false, isLimitIPClkErrCode
	}

	return true, 0
}

// IsAllInOneLimitOKAfterReplaceDID
func IsAllInOneLimitOKAfterReplaceDID(c context.Context, mhReq *models.MHReq,
	localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu,
	bigdataExtra *models.MHUpRespExtra,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {

	// 是否替换包之后厂商过滤请求
	isManufacturerOK, isManufacturerOKErrCode := IsManufacturerOKAfterReplaceDID(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isManufacturerOK {
	} else {
		return false, isManufacturerOKErrCode
	}

	// 请求上游计算设备did dau
	isUpMaxDAUOK, _, isUpMaxDAUInternalErrCode := GetUpMaxDAUStatus(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if platformPos.PlatformAppMaxDAUType == 2 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<3
	}
	if isUpMaxDAUOK {
	} else {
		return false, isUpMaxDAUInternalErrCode
	}

	// 判定上游p_app_id did 最大请求数
	isLimitDIDReqOK, isLimitDIDReqErrCode := IsLimitDIDReqOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDReqOK {
	} else {
		return false, isLimitDIDReqErrCode
	}

	// 判定上游p_app_id did 最大曝光数
	isLimitDIDExpOK, isLimitDIDExpErrCode := IsLimitDIDExpOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDExpOK {
	} else {
		return false, isLimitDIDExpErrCode
	}

	// 判定上游p_app_id did 最大点击数
	isLimitDIDClkOK, isLimitDIDClkErrCode := IsLimitDIDClkOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDClkOK {
	} else {
		return false, isLimitDIDClkErrCode
	}

	// 判定上游p_app_id did 最大req间隔数
	isLimitDIDReqIntervalOK, isLimitDIDReqIntervalErrCode := IsLimitDIDReqIntervalOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDReqIntervalOK {
	} else {
		return false, isLimitDIDReqIntervalErrCode
	}

	// 判定上游p_app_id did 最大exp间隔数
	isLimitDIDExpIntervalOK, isLimitDIDExpIntervalErrCode := IsLimitDIDExpIntervalOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDExpIntervalOK {
	} else {
		return false, isLimitDIDExpIntervalErrCode
	}

	// 判定上游p_app_id did 最大req间隔数
	isLimitDIDClkIntervalOK, isLimitDIDClkIntervalErrCode := IsLimitDIDClkIntervalOK(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	if isLimitDIDClkIntervalOK {
	} else {
		return false, isLimitDIDClkIntervalErrCode
	}

	return true, 0
}

// IsAllInOneLimitOKAfterUpResp
func IsAllInOneLimitOKAfterUpResp(c context.Context, mhReq *models.MHReq,
	localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu,
	bigdataExtra *models.MHUpRespExtra,
) (bool, int) {

	// 平台策略
	isPlatformPolicyOK := models.IsPlatformPolicyCostOK(c, mhReq, localPos, platformPos.PlatformMediaID, platformPos.PlatformAppCorpID)
	if isPlatformPolicyOK {
	} else {
		fmt.Println("platform policy not ok , app id: " + localPos.LocalAppID)
		return false, 900103
	}

	return true, 0
}

// IsCrowdPackageOK ...
func IsCrowdPackageOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, bigdataExtra *models.MHUpRespExtra) bool {
	// 是否通过人群包
	if platformPos.PlatformAppIsCrowdPackage == 0 {
		return true
	}

	// 人群包媒体白名单, 直接通过
	for _, item := range platformPos.PlatformAppCrowdPackageWhiteMediasConfig {
		if item.LocalAppID == localPos.LocalAppID {
			return true
		}
	}

	if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		return false
	}

	// 上游人群包统计
	if bigdataExtra != nil {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<1
	}

	redisKey := "dmp_crowd_" + utils.Get16Md5(platformPos.PlatformAppCrowdPackageID) + "_" + mhReq.Device.DIDMd5
	_, redisErr := db.GlbRedis.Get(c, redisKey).Result()
	if redisErr != nil {
		if platformPos.PlatformAppCrowdPackageType == 0 {
			// 人群包未命中时强制通过比例
			for _, item := range platformPos.PlatformAppCrowdPackageFailedMediasConfig {
				if item.LocalAppID == localPos.LocalAppID || item.LocalAppID == "all" {
					randalue := rand.Intn(10000)
					if randalue < item.Weight {
						if bigdataExtra != nil {
							bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<8
						}
						return true
					}
					break
				}
			}

			return false
		} else {
			if bigdataExtra != nil {
				bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<4
			}
			return true
		}
	} else {
		if platformPos.PlatformAppCrowdPackageType == 0 {
			if bigdataExtra != nil {
				bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<4
			}
			return true
		} else {
			// 人群包未命中时强制通过比例
			for _, item := range platformPos.PlatformAppCrowdPackageFailedMediasConfig {
				if item.LocalAppID == localPos.LocalAppID || item.LocalAppID == "all" {
					randalue := rand.Intn(10000)
					if randalue < item.Weight {
						if bigdataExtra != nil {
							bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<8
						}
						return true
					}
					break
				}
			}
			return false
		}
	}

	// return false
}

// IsCategoryCrowdPackageOK ...
func IsCategoryCrowdPackageOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, categoryInfo *models.CategoryStu, bigdataExtra *models.MHUpRespExtra) bool {
	// 是否通过人群包
	if categoryInfo.IsCrowdPackage == 0 {
		return true
	}

	if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		return false
	}

	// 下游人群包统计
	if bigdataExtra != nil {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<6
	}

	redisKey := "dmp_crowd_" + utils.Get16Md5(categoryInfo.CrowdPackageID) + "_" + mhReq.Device.DIDMd5
	_, redisErr := db.GlbRedis.Get(c, redisKey).Result()
	if redisErr != nil {
		if categoryInfo.CrowdPackageType == 0 {
			return false
		} else {
			if bigdataExtra != nil {
				bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<7
			}
			return true
		}
	} else {
		if categoryInfo.CrowdPackageType == 0 {
			if bigdataExtra != nil {
				bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<7
			}
			return true
		} else {
			return false
		}
	}

	// return false
}

// IsManufacturerCategoryOK 过滤厂商
func IsManufacturerCategoryOK(c context.Context, mhReq *models.MHReq, category *models.CategoryStu) (bool, int) {
	if len(category.ManufacturerList) == 0 {
		return true, 0
	}
	if strings.Contains(category.ManufacturerList, "all") {
		return true, 0
	}

	manufactureArray := strings.Split(category.ManufacturerList, ",")
	for _, manufactureItem := range manufactureArray {
		if manufactureItem == "others" {
			continue
		}
		manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+manufactureItem)
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return true, 0
			}
		}
	}

	// 选中其他
	if strings.Contains(category.ManufacturerList, "others") {
		// huawei
		manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_huawei")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// xiaomi
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_xiaomi")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// oppo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_oppo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// vivo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_vivo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// lenovo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_lenovo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// meizu
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_meizu")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// honor
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_honor")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		return true, 0
	}

	return false, 900110
}

// IsManufacturerOKAfterReplaceDID 过滤厂商
func IsManufacturerOKAfterReplaceDID(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {

	tmpManufacturer := mhReq.Device.Manufacturer
	if isHaveReplace {
		tmpManufacturer = bigdataReplaceDIDData.Manufacturer
	}

	if len(platformPos.PlatformPosManufacturerList) == 0 {
		return true, 0
	}
	if strings.Contains(platformPos.PlatformPosManufacturerList, "all") {
		return true, 0
	}

	manufactureArray := strings.Split(platformPos.PlatformPosManufacturerList, ",")
	for _, manufactureItem := range manufactureArray {
		if manufactureItem == "others" {
			continue
		}
		manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+manufactureItem)
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return true, 0
			}
		}
	}

	// 选中其他
	if strings.Contains(platformPos.PlatformPosManufacturerList, "others") {
		// huawei
		manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_huawei")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// xiaomi
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_xiaomi")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// oppo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_oppo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// vivo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_vivo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// lenovo
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_lenovo")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// meizu
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_meizu")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		// honor
		manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_honor")
		for _, manufactureConfigItem := range manufactureConfigArray {
			if strings.ToLower(tmpManufacturer) == manufactureConfigItem {
				return false, 900110
			}
		}
		return true, 0
	}

	return false, 900110
}

// GetUpMaxDAUStatus 请求上游计算设备did dau
func GetUpMaxDAUStatus(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int, int) {
	if platformPos.PlatformAppMaxDAUType == 2 {
	} else {
		return true, 0, 0
	}

	if platformPos.PlatformAppMaxDAURandom == 0 {
		return false, 102006, 900101
	}

	tmpMHReq := *mhReq
	// did_md5
	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		return false, 104016, 900101
	}

	// redis取最大dau
	tmpRedisMaxDAUKey := fmt.Sprintf(rediskeys.ADX_SSP_MAX_DAU_NUMBER_KEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"), utils.ConvertIntToString(rand.Intn(8)))
	tmpRedisMaxDAUValueInt := 0

	//////////////////////////////////////////////////////////////////////////////////////////////
	tmpCacheMaxeDAUValue, tmpCacheMaxeDAUError := db.GlbBigCacheMinute.Get(tmpRedisMaxDAUKey)
	if tmpCacheMaxeDAUError != nil {
		fmt.Println("dau cache error:", tmpRedisMaxDAUKey, tmpCacheMaxeDAUError)

		tmpRedisMaxeDAUValue, redisErr := db.GlbRedis.Get(c, tmpRedisMaxDAUKey).Result()
		if redisErr != nil {
		} else {
			db.GlbBigCacheMinute.Set(tmpRedisMaxDAUKey, []byte(tmpRedisMaxeDAUValue))
			tmpRedisMaxDAUValueInt = utils.ConvertStringToInt(tmpRedisMaxeDAUValue)
		}
	} else {
		tmpRedisMaxDAUValueInt = utils.ConvertStringToInt(string(tmpCacheMaxeDAUValue))
	}
	//////////////////////////////////////////////////////////////////////////////////////////////

	// 如果大于设定值*10000, 并且did_md5枚没记录, 直接return
	if tmpRedisMaxDAUValueInt >= platformPos.PlatformAppMaxDAURandom {
		// fmt.Println("kbg_debug_max_dau return:", platformPos.PlatformAppID, tmpRedisMaxDAUValueInt, platformPos.PlatformAppMaxDAURandom)

		return false, 102006, 900101
	}

	return true, 0, 0
}

// GetDestConfigUA 获取配置ua
func GetDestConfigUA(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) string {
	if localPos.LocalAppIsFixUA == 2 {
		isNoReplaceUA := false
		if len(localPos.LocalAppUANoReplaceDemandPlatformIDs) > 0 {
			for _, item := range strings.Split(localPos.LocalAppUANoReplaceDemandPlatformIDs, ",") {
				if item == platformPos.PlatformMediaID {
					isNoReplaceUA = true
					break
				}
			}
		}
		if isNoReplaceUA {
		} else {
			// 替换ua
			return GetDeviceUA(c, mhReq)
		}
	}

	return mhReq.Device.Ua
}

// IsFilterUA 过滤UA
func IsFilterUA(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, destConfigUA string) (bool, int) {
	if platformPos.PlatformAppFilterUa == 1 {
		// 包含Mozilla or Dalvik 全过
		if strings.HasPrefix(destConfigUA, "Mozilla") || strings.HasPrefix(destConfigUA, "Dalvik") {
		} else {
			return true, 900101
		}
	}

	if platformPos.PlatformAppFilterIllegalUa == 1 {
		if mhReq.Device.Os == "android" {
			// Mozilla/5.0 (Linux; Android 12; GIA-AN00 Build/HONORGIA-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.105 Mobile Safari/537.36
			// 12
			// GIA-AN00
			tmpOSV := mhReq.Device.OsVersion
			tmpModel := mhReq.Device.Model
			tmpUA := destConfigUA
			if strings.Contains(strings.ToLower(tmpUA), strings.ToLower("Android "+tmpOSV)) && strings.Contains(strings.ToLower(tmpUA), strings.ToLower(tmpModel)) {
			} else {
				return true, 900117
			}

		} else if mhReq.Device.Os == "ios" {
			// Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
			// 16.4.1
			// iPhone15,3
			tmpOSV := mhReq.Device.OsVersion
			if strings.Count(tmpOSV, ".") >= 2 && strings.HasSuffix(tmpOSV, ".0") {
				tmpOSV = tmpOSV[:len(tmpOSV)-2]
			}

			tmpUA := destConfigUA
			tmpUA = strings.Replace(tmpUA, "_", ".", -1)
			if strings.Contains(tmpUA, tmpOSV) {
			} else {
				return true, 900117
			}
		}
	}

	return false, 0
}

// IsLimitIPReqOK
func IsLimitIPReqOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) (bool, int) {

	if platformPos.PlatformAppIsLimitIPReq == 0 {
		return true, 0
	}

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_PREFIX, mhReq.Device.IP)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_REQ_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	// debug
	tmpDebugValue := int64(0)

	if redisErr != nil {
	} else {
		maxIPReqNumStr := redisResult.(string)
		maxIPReqNum := utils.ConvertStringToInt64(maxIPReqNumStr)

		if maxIPReqNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitIPReq) {
			models.SaveLimitIPErrorToHolo(c, mhReq.Device.IP, platformPos.PlatformAppID, maxIPReqNum, 1, false)
			return false, 900002
		}

		tmpDebugValue = maxIPReqNum
	}

	// debug
	limitIPReqRules := []models.PlatformAppLimitIPReqRuleStu{}
	json.Unmarshal([]byte(platformPos.PlatformAppLimitIPReqRuleJson), &limitIPReqRules)
	if len(limitIPReqRules) > 0 {
		sort.Sort(PlatformAppLimitIPReqRuleSort(limitIPReqRules))
		isOK := true
		for _, item := range limitIPReqRules {
			if utils.ConvertStringToInt(item.LimitIPReqMaxRatio)*utils.ConvertStringToInt(platformPos.PlatformAppMaxNumLimitIPReq)/100 < int(tmpDebugValue) {
				if rand.Intn(100) < utils.ConvertStringToInt(item.LimitIPReqFilterRatio) {
					isOK = false
				}
				break
			}
		}

		if isOK {
		} else {
			models.SaveLimitIPErrorToHolo(c, mhReq.Device.IP, platformPos.PlatformAppID, tmpDebugValue, 1, false)
			return false, 900002
		}
	}
	// if platformPos.PlatformAppID == "555600002" || platformPos.PlatformAppID == "555600006" {
	// 	models.SaveLimitIPErrorToHolo(c, mhReq.Device.IP, platformPos.PlatformAppID, tmpDebugValue, 1, true)
	// }

	return true, 0
}

type PlatformAppLimitIPReqRuleSort []models.PlatformAppLimitIPReqRuleStu

func (s PlatformAppLimitIPReqRuleSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s PlatformAppLimitIPReqRuleSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s PlatformAppLimitIPReqRuleSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].LimitIPReqMaxRatio > s[j].LimitIPReqMaxRatio
}

// IsLimitIPExpOK
func IsLimitIPExpOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) (bool, int) {
	if platformPos.PlatformAppIsLimitIPExp == 0 {
		return true, 0
	}

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_PREFIX, mhReq.Device.IP)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_EXP_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		if redisMaxNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitIPExp) {
			models.SaveLimitIPErrorToHolo(c, mhReq.Device.IP, platformPos.PlatformAppID, redisMaxNum, 2, false)
			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_c", "c", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitIPClkOK
func IsLimitIPClkOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) (bool, int) {
	if platformPos.PlatformAppIsLimitIPClk == 0 {
		return true, 0
	}

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_PREFIX, mhReq.Device.IP)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_CLK_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		if redisMaxNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitIPClk) {
			models.SaveLimitIPErrorToHolo(c, mhReq.Device.IP, platformPos.PlatformAppID, redisMaxNum, 3, false)
			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_d", "d", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDReqOK
func IsLimitDIDReqOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDReq == 0 {
		return true, 0
	}

	tmpMHReq := *mhReq
	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_e", "e", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_REQ_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		if redisMaxNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDReq) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, redisMaxNum, 0, false)
			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_f", "f", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDExpOK
func IsLimitDIDExpOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDExp == 0 {
		return true, 0
	}

	if localPos.LocalAppID == "10012" || localPos.LocalAppID == "10754" {
		return true, 0
	}

	tmpMHReq := *mhReq

	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_g", "g", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_EXP_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		if redisMaxNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDExp) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, redisMaxNum, 1, false)

			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_h", "h", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDClkOK
func IsLimitDIDClkOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDClk == 0 {
		return true, 0
	}

	tmpMHReq := *mhReq
	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_i", "i", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_CLK_NUMBER_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		if redisMaxNum >= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDClk) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, redisMaxNum, 2, false)
			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_j", "j", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDReqIntervalOK
func IsLimitDIDReqIntervalOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDReqInterval == 0 {
		return true, 0
	}

	tmpMHReq := *mhReq
	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_k", "k", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_REQ_LAST_TIME_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		tmpInterval := utils.GetCurrentMilliSecond() - redisMaxNum
		if tmpInterval <= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDReqInterval) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, tmpInterval, 3, false)

			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_l", "l", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDExpIntervalOK
func IsLimitDIDExpIntervalOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDExpInterval == 0 {
		return true, 0
	}

	tmpMHReq := *mhReq
	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_m", "m", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_EXP_LAST_TIME_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		tmpInterval := utils.GetCurrentMilliSecond() - redisMaxNum
		if tmpInterval <= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDExpInterval) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, tmpInterval, 3, false)

			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_n", "n", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// IsLimitDIDClkIntervalOK
func IsLimitDIDClkIntervalOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu,
	isHaveReplace bool, bigdataReplaceDIDData models.ReplaceDIDStu) (bool, int) {
	if platformPos.PlatformAppIsLimitDIDClkInterval == 0 {
		return true, 0
	}

	tmpMHReq := *mhReq

	if isHaveReplace {
		tmpMHReq.Device.OsVersion = bigdataReplaceDIDData.OsVersion
		tmpMHReq.Device.Imei = bigdataReplaceDIDData.Imei
		tmpMHReq.Device.ImeiMd5 = bigdataReplaceDIDData.ImeiMd5
		tmpMHReq.Device.Oaid = bigdataReplaceDIDData.Oaid
		tmpMHReq.Device.Idfa = bigdataReplaceDIDData.Idfa
		tmpMHReq.Device.IdfaMd5 = bigdataReplaceDIDData.IdfaMd5
		// tmpMHReq.Device.CAID = bigdataReplaceDIDData.CAID
		// tmpMHReq.Device.CAIDVersion = bigdataReplaceDIDData.CAIDVersion
		var tmpCAIDMulti []models.MHReqCAIDMulti
		json.Unmarshal([]byte(bigdataReplaceDIDData.CAIDMultiJson), &tmpCAIDMulti)
		tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		tmpMHReq.Device.DeviceStartSec = bigdataReplaceDIDData.DeviceStartSec
		tmpMHReq.Device.SystemUpdateSec = bigdataReplaceDIDData.SystemUpdateSec
		tmpMHReq.Device.Country = bigdataReplaceDIDData.Country
		tmpMHReq.Device.Language = bigdataReplaceDIDData.Language
		tmpMHReq.Device.DeviceNameMd5 = bigdataReplaceDIDData.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = bigdataReplaceDIDData.HardwareMachine
		tmpMHReq.Device.PhysicalMemoryByte = bigdataReplaceDIDData.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = bigdataReplaceDIDData.HarddiskSizeByte
		tmpMHReq.Device.TimeZone = bigdataReplaceDIDData.TimeZone
		tmpMHReq.Device.HardwareModel = bigdataReplaceDIDData.HardwareModel

		tmpMHReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(&tmpMHReq)
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		// if platformPos.PlatformAppID == "1121853624" {
		// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_o", "o", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		// }
		return false, 900010
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	exHGETKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_PREFIX, didMd5Key)
	exHGETField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_CLK_LAST_TIME_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

	redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETKey, exHGETField)

	if redisErr != nil {
	} else {
		redisMaxNumStr := redisResult.(string)
		redisMaxNum := utils.ConvertStringToInt64(redisMaxNumStr)

		tmpInterval := utils.GetCurrentMilliSecond() - redisMaxNum
		if tmpInterval <= utils.ConvertStringToInt64(platformPos.PlatformAppMaxNumLimitDIDClkInterval) {
			models.SaveLimitDIDErrorToHolo(c, didMd5Key, platformPos.PlatformAppID, tmpInterval, 3, false)

			// if platformPos.PlatformAppID == "1121853624" {
			// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"_p", "p", localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
			return false, 900010
		}
	}

	return true, 0
}

// GetAppBundleByConfig
// 替换包名开关：
//  1. 关 ->  上报上游配置包名
//  2. 开
//     A. 如果在替换名单 -> 传对应替换包名。
//     B. 如果不在替换名单
//     B1 兜底包名若选中媒体包名
//     B11. 请求中包名有传值 -> 传请求里包名
//     B12. 若请求中不含包名 -> 传下游媒体配置包名
//     B2 兜底包名选中自定义包名的 -> 传自定义包名
//
// json示例: [{"supply_app_id":"10013","bundle_ids":["com.a","com.b","com.c"],"tmp_bundle_id":"","weight":5},{"supply_app_id":"10012","bundle_ids":["com.1","com.2","com.3"],"tmp_bundle_id":"","weight":15}]
func GetAppBundleByConfig(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) string {

	if platformPos.PlatformAppIsReplaceAppBundleID == 0 {
		return platformPos.PlatformAppBundle
	} else if platformPos.PlatformAppIsReplaceAppBundleID == 1 {
		if len(platformPos.PlatformAppReplaceAppBundleIDJsonConfig) > 2 {
			var replaceAppBundleIDs []ReplaceAppBundleIDStu
			json.Unmarshal([]byte(platformPos.PlatformAppReplaceAppBundleIDJsonConfig), &replaceAppBundleIDs)
			for _, tmpReplaceBundleIDItem := range replaceAppBundleIDs {
				if tmpReplaceBundleIDItem.SupplyAppID == localPos.LocalAppID {
					randValue := rand.Intn(100)
					if randValue < tmpReplaceBundleIDItem.Weight {
						if len(tmpReplaceBundleIDItem.BundleIDs) > 0 {
							return tmpReplaceBundleIDItem.BundleIDs[rand.Intn(len(tmpReplaceBundleIDItem.BundleIDs))]
						}
					}
				}
			}
		}

		if platformPos.PlatformAppCustomReplaceAppBundleIDType == 0 {
			if len(mhReq.App.AppBundleID) > 0 {
				return mhReq.App.AppBundleID
			} else {
				return localPos.LocalAppBundleID
			}
		} else if platformPos.PlatformAppCustomReplaceAppBundleIDType == 1 {
			return platformPos.PlatformAppCustomReplaceAppBundleID
		}
	}
	return platformPos.PlatformAppBundle
}

type ReplaceAppBundleIDStu struct {
	SupplyAppID string   `json:"supply_app_id"`
	BundleIDs   []string `json:"bundle_ids"`
	Weight      int      `json:"weight"`
}

func GetPriceConfig(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, category *models.CategoryStu) (int, int, float32) {

	// 请求价
	localPosFloorPrice := mhReq.Pos.CPMBidFloor
	if localPos.LocalAppType == "4" {
		// rtb媒体, 如果选中自定义竞价底价选中的平台, 使用配置的竞价底价
		if len(localPos.LocalPosCustomCPMBidFloorPlatformList) > 0 {
			customCPMBidFloorPlatformListArray := strings.Split(localPos.LocalPosCustomCPMBidFloorPlatformList, ",")
			isCustomPrice := false
			for _, item := range customCPMBidFloorPlatformListArray {
				if item == platformPos.PlatformMediaID {
					isCustomPrice = true
					break
				}
			}

			if isCustomPrice {
				localPosFloorPrice = category.FloorPrice
			}
		}
	} else {
		localPosFloorPrice = category.FloorPrice
	}

	// 截断价
	localPosFinalPrice := category.FinalPrice

	// 利润率
	localPosProfitRate := float32(utils.ConvertStringToFloat(category.ProfitRate))
	if len(category.ProfitRate) == 0 {
		localPosProfitRate = localPos.LocalPosProfitRate
	}

	return localPosFloorPrice, localPosFinalPrice, localPosProfitRate
}

// android >= 3.3.0
// ios返回true
func IsAdIDReplaceXYByOS(c context.Context, mhReq *models.MHReq) bool {
	if len(mhReq.SDKVersion) <= 0 {
		return false
	}

	if mhReq.Device.Os == "ios" {
		return true
	}

	return IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 0)
}

// 是否sdk版本号 >= x.y.z
func IsSDKVersionMoreThanOrEqualVersionCode(c context.Context, sdkVersion string, majorCode int, minorCode int, patchCode int) bool {
	if len(sdkVersion) <= 0 {
		return false
	}
	// fmt.Println(sdkVersion)
	tmpSDKVersionArray := strings.Split(sdkVersion, ".")

	tmpSDKVersion0 := 0
	tmpSDKVersion1 := 0
	tmpSDKVersion2 := 0

	if len(tmpSDKVersionArray) > 0 {
		if utils.IsNum(tmpSDKVersionArray[0]) {
			tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
			if tmpSDKVersion0 < majorCode {
				return false
			}

			if tmpSDKVersion0 > majorCode {
				return true
			}

			if tmpSDKVersion0 == majorCode && len(tmpSDKVersionArray) == 1 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 1 {
		if utils.IsNum(tmpSDKVersionArray[1]) {
			tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
			if tmpSDKVersion0 == majorCode {
				if tmpSDKVersion1 < minorCode {
					return false
				}
				if tmpSDKVersion1 > minorCode {
					return true
				}
				if tmpSDKVersion1 == minorCode && len(tmpSDKVersionArray) == 2 {
					return false
				}
			}
		}
	}
	if len(tmpSDKVersionArray) > 2 {
		if utils.IsNum(tmpSDKVersionArray[2]) {
			tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
			if tmpSDKVersion1 == minorCode && tmpSDKVersion0 == majorCode {
				if tmpSDKVersion2 < patchCode {
					return false
				}
				if tmpSDKVersion2 > patchCode {
					return true
				}

				if tmpSDKVersion2 == patchCode && len(tmpSDKVersionArray) == 3 {
					return false
				}
			}
		}
	}

	return true
}

// 是否sdk版本号 >= x.y.z.c
func IsSDKVersionMoreThanOrEqualFourVersionCode(c context.Context, sdkVersion string, majorCode int, minorCode int, patchCode int, patchPatchCode int) bool {
	if len(sdkVersion) <= 0 {
		return false
	}
	// fmt.Println(sdkVersion)
	tmpSDKVersionArray := strings.Split(sdkVersion, ".")

	tmpSDKVersion0 := 0
	tmpSDKVersion1 := 0
	tmpSDKVersion2 := 0
	tmpSDKVersion3 := 0

	if len(tmpSDKVersionArray) > 0 {
		if utils.IsNum(tmpSDKVersionArray[0]) {
			tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
			if tmpSDKVersion0 < majorCode {
				return false
			}

			if tmpSDKVersion0 > majorCode {
				return true
			}

			if tmpSDKVersion0 == majorCode && len(tmpSDKVersionArray) == 1 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 1 {
		if utils.IsNum(tmpSDKVersionArray[1]) {
			tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
			if tmpSDKVersion0 == majorCode {
				if tmpSDKVersion1 < minorCode {
					return false
				}
				if tmpSDKVersion1 > minorCode {
					return true
				}
				if tmpSDKVersion1 == minorCode && len(tmpSDKVersionArray) == 2 {
					return false
				}
			}
		}
	}
	if len(tmpSDKVersionArray) > 2 {
		if utils.IsNum(tmpSDKVersionArray[2]) {
			tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
			if tmpSDKVersion1 == minorCode && tmpSDKVersion0 == majorCode {
				if tmpSDKVersion2 < patchCode {
					return false
				}
				if tmpSDKVersion2 > patchCode {
					return true
				}

				if tmpSDKVersion2 == patchCode && len(tmpSDKVersionArray) == 3 {
					return false
				}
			}
		}
	}
	if len(tmpSDKVersionArray) > 3 {
		if utils.IsNum(tmpSDKVersionArray[3]) {
			tmpSDKVersion3 = utils.ConvertStringToInt(tmpSDKVersionArray[3])
			if tmpSDKVersion3 < patchPatchCode && tmpSDKVersion2 == patchCode && tmpSDKVersion1 == minorCode && tmpSDKVersion0 == majorCode {
				return false
			}
		}
	}

	return true
}

func IsSkipClickLink(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, mhRespItem models.MHRespDataItem) (bool, string) {
	if platformPos.PlatformPosIsSkipReportClick == 0 {
		return false, ""
	}

	if platformPos.PlatformPosIsSkipReportClick == 1 {
		for _, item := range platformPos.PlatformPosSkipReportClickConfig {
			if item.LocalAppID == localPos.LocalAppID || item.LocalAppID == "all" {
				if item.FilterType == "jd" {
					// 判定京东返回规则：
					// 1、包名=com.jingdong.app.mall
					// 2、落地页或者下载链接包含jd.com
					// 3、dp链接包含openapp.jdmobile://
					// 如果符合jd, 按比例丢
					if mhRespItem.PackageName == "com.jingdong.app.mall" ||
						strings.Contains(mhRespItem.DeepLink, "openapp.jdmobile://") ||
						strings.Contains(mhRespItem.LandpageURL, "jd.com") ||
						strings.Contains(mhRespItem.DownloadURL, "jd.com") {
						randalue := rand.Intn(100)
						if randalue < item.Weight {
							// go func() {
							// 	defer func() {
							// 		if err := recover(); err != nil {
							// 			fmt.Println("debug panic:", err)
							// 		}
							// 	}()
							// 	tmpJsonData, _ := json.Marshal(mhRespItem)
							// 	models.BigDataHoloDebugJson2(uuid.NewV4().String()+"&skipclickjd", string(tmpJsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
							// }()
							return true, item.Deeplink
						}
					}
				} else {
					randalue := rand.Intn(100)
					if randalue < item.Weight {
						return true, item.Deeplink
					}
				}
				break
			}
		}
	} else if platformPos.PlatformPosIsSkipReportClick == 2 {
		exHGETDIDIPKey := fmt.Sprintf(rediskeys.STATISTICS_REALTIME_BY_DEMAND_POS_KEY,
			platformPos.PlatformAppID,
			platformPos.PlatformPosID)

		result, redisErr := utils.RedisSendCommand(c, "EXHGET", exHGETDIDIPKey, "clk_rate")
		if redisErr != nil {
		} else {
			realtimeClkRate := float32(utils.ConvertStringToFloat(result.(string)))
			expectedClkRate := platformPos.PlatformPosExpectedClickRate
			if realtimeClkRate > 0 && realtimeClkRate > expectedClkRate {
				randDiscardMaxNum := (1 - expectedClkRate/float32(realtimeClkRate)) * 2 * 100
				for _, item := range platformPos.PlatformPosExpectedSkipReportClickConfig {
					if item.LocalAppID == localPos.LocalAppID || item.LocalAppID == "all" {
						randalue := rand.Intn(100)
						if randalue < int(randDiscardMaxNum) {
							return true, item.Deeplink
						}
						break
					}
				}
			}
		}
	}

	return false, ""
}

func IsDiscardDeepLink(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, mhRespItem models.MHRespDataItem) bool {
	if platformPos.PlatformPosIsDiscardDeeplink == 0 {
		return false
	}

	if platformPos.PlatformPosIsDiscardDeeplink == 1 {
		for _, item := range platformPos.PlatformPosDiscardDeeplinkConfig {
			if item.LocalAppID == localPos.LocalAppID || item.LocalAppID == "all" {
				if item.FilterType == "jd" {
					// 判定京东返回规则：
					// 1、包名=com.jingdong.app.mall
					// 2、落地页或者下载链接包含jd.com
					// 3、dp链接包含openapp.jdmobile://
					// 如果符合jd, 按比例丢
					if mhRespItem.PackageName == "com.jingdong.app.mall" ||
						strings.Contains(mhRespItem.DeepLink, "openapp.jdmobile://") ||
						strings.Contains(mhRespItem.LandpageURL, "jd.com") ||
						strings.Contains(mhRespItem.DownloadURL, "jd.com") {
						randalue := rand.Intn(100)
						if randalue < item.Weight {
							return true
						}
					}
				} else {
					randalue := rand.Intn(100)
					if randalue < item.Weight {
						return true
					}
				}
				break
			}
		}
	}

	return false
}

func GetSupportQuerysPkgs(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) ([]string, []string) {
	if platformPos.PlatformPosSupportQuerysPkgsType == 0 {
		return []string{}, []string{}
	}
	var tmpSupportQuerys []string
	var tmpSupportPkgs []string

	if platformPos.PlatformPosIsSupportQuerys == 1 {
		if len(mhReq.Pos.Query) > 0 {
			tmpSupportQuerys = append(tmpSupportQuerys, mhReq.Pos.Query)
		} else {
			if platformPos.PlatformPosSupportQuerysType == 1 {
				cacheValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DICT_QUERY_KEY)
				if cacheError != nil {
				} else {
					var tmQueryPkgsDICTStu models.QueryPkgsDICTStu
					json.Unmarshal([]byte(cacheValue), &tmQueryPkgsDICTStu)
					tmpSupportQuerys = tmQueryPkgsDICTStu.Querys
				}
			}
		}
	}

	if platformPos.PlatformPosIsSupportPkgs == 1 {
		if len(mhReq.Pos.PkgWhitelist) > 0 {
			tmpSupportPkgs = mhReq.Pos.PkgWhitelist
		} else {
			if platformPos.PlatformPosSupportPkgsType == 1 {
				cacheValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DICT_QUERY_KEY)
				if cacheError != nil {
				} else {
					var tmQueryPkgsDICTStu models.QueryPkgsDICTStu
					json.Unmarshal([]byte(cacheValue), &tmQueryPkgsDICTStu)
					tmpSupportPkgs = tmQueryPkgsDICTStu.Pkgs
				}
			}
		}
	}

	if platformPos.PlatformPosSupportQuerysPkgsType == 1 {
		if len(tmpSupportQuerys) > 0 {
			return tmpSupportQuerys, []string{}
		} else {
			return []string{}, tmpSupportPkgs
		}
	} else if platformPos.PlatformPosSupportQuerysPkgsType == 2 {

		if len(tmpSupportPkgs) > 0 {
			return []string{}, tmpSupportPkgs
		} else {
			return tmpSupportQuerys, []string{}
		}
	} else if platformPos.PlatformPosSupportQuerysPkgsType == 3 {
		return tmpSupportQuerys, tmpSupportPkgs
	}

	return []string{}, []string{}
}

func IsClikPointLibInWhite(c context.Context, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu) bool {
	if len(platformPos.PlatformPosClickPointLibConfig) > 0 {
		for _, item := range platformPos.PlatformPosClickPointLibConfig {
			if item.LocalAppID == "all" {
				return true
			} else if item.LocalAppID == localPos.LocalAppID {
				return true
			}
		}
	}

	return false
}

func removeDuplicates(nums []string) []string {
	result := make([]string, 0) // 创建新的切片来保存结果
	seen := map[string]bool{}   // 创建一个空的map来记录已经遇到过的数字

	for _, num := range nums {
		if !seen[num] { // 如果该数字还没有被添加进result或者seen中
			result = append(result, num) // 将其添加到result中
			seen[num] = true             // 标记这个数字为已经处理过了
		}
	}

	return result
}

// GetReplaceDeviceIDMd5Key ...
func GetReplaceDeviceIDMd5Key(didRedisData models.ReplaceDIDStu) string {
	var deviceInfo deviceidgeneratemodel.DeviceModel
	deviceInfo.Os = didRedisData.Os
	deviceInfo.OsVersion = didRedisData.OsVersion
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = didRedisData.Imei
		deviceInfo.ImeiMd5 = didRedisData.ImeiMd5
		deviceInfo.Oaid = didRedisData.Oaid
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = didRedisData.Idfa
		deviceInfo.IdfaMd5 = didRedisData.IdfaMd5

		deviceInfo.DeviceStartSec = didRedisData.DeviceStartSec
		deviceInfo.Country = didRedisData.Country
		deviceInfo.Language = didRedisData.Language
		deviceInfo.DeviceNameMd5 = didRedisData.DeviceNameMd5
		deviceInfo.HardwareMachine = didRedisData.HardwareMachine
		deviceInfo.HardwareModel = didRedisData.HardwareModel
		deviceInfo.PhysicalMemoryByte = didRedisData.PhysicalMemoryByte
		deviceInfo.HarddiskSizeByte = didRedisData.HarddiskSizeByte
		deviceInfo.SystemUpdateSec = didRedisData.SystemUpdateSec
		deviceInfo.TimeZone = didRedisData.TimeZone

		if len(didRedisData.CAIDMultiJson) > 0 {
			var tmpReplaceCAIDMulti []models.MHReqCAIDMulti
			json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpReplaceCAIDMulti)

			var tmpCAIDMulti []deviceidgeneratemodel.DeviceCAIDMultiModel
			for _, tmpItem := range tmpReplaceCAIDMulti {
				if len(tmpItem.CAID) > 0 && len(tmpItem.CAIDVersion) > 0 {
					var tmpCAIDItem deviceidgeneratemodel.DeviceCAIDMultiModel

					tmpCAIDItem.CAID = tmpItem.CAID
					tmpCAIDItem.CAIDVersion = tmpItem.CAIDVersion
					tmpCAIDMulti = append(tmpCAIDMulti, tmpCAIDItem)
				}
			}

			deviceInfo.CAIDMulti = tmpCAIDMulti
		}
	}

	return deviceidgenerate.GenerateDeviceID(deviceInfo)
}

func GetLogoConfig(localAppID string, localPosID string, mhReq *models.MHReq) (string, string) {

	var sdkLogoConfig string
	var apiLogoConfig string

	// logo app id
	logoAppIDCacheKey := "go_logo_config_app_id:" + localAppID
	cacheLogoAppIDValue, cacheLogoAppIDError := db.GlbBigCacheMinute.Get(logoAppIDCacheKey)
	if cacheLogoAppIDError != nil {
	} else {
		var tmpLogoCacheConfig models.LogoDataCacheConfigStu
		json.Unmarshal([]byte(cacheLogoAppIDValue), &tmpLogoCacheConfig)

		// appid sdk logo config
		if len(tmpLogoCacheConfig.SdkWhiteList) == 0 && len(tmpLogoCacheConfig.SdkBlackList) == 0 {
			sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
		} else {
			if len(mhReq.Device.LBSIPProvince) == 0 || len(mhReq.Device.LBSIPCity) == 0 {
				sdkLogoConfig = ""
			} else {
				if len(tmpLogoCacheConfig.SdkWhiteList) > 0 {
					isInWhiteCity := false
					for _, item := range tmpLogoCacheConfig.SdkWhiteList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInWhiteCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInWhiteCity = true
								break
							}
						}
					}
					if isInWhiteCity {
						sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
					}
				} else if len(tmpLogoCacheConfig.SdkBlackList) > 0 {
					isInBlackCity := false
					for _, item := range tmpLogoCacheConfig.SdkBlackList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInBlackCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInBlackCity = true
								break
							}
						}
					}
					if isInBlackCity {
					} else {
						sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
					}
				}
			}
		}

		// appid api logo config
		if len(tmpLogoCacheConfig.ApiWhiteList) == 0 && len(tmpLogoCacheConfig.ApiBlackList) == 0 {
			apiLogoConfig = tmpLogoCacheConfig.ApiLogo
		} else {
			if len(mhReq.Device.LBSIPProvince) == 0 || len(mhReq.Device.LBSIPCity) == 0 {
				apiLogoConfig = ""
			} else {
				if len(tmpLogoCacheConfig.ApiWhiteList) > 0 {
					isInWhiteCity := false
					for _, item := range tmpLogoCacheConfig.ApiWhiteList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInWhiteCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInWhiteCity = true
								break
							}
						}
					}
					if isInWhiteCity {
						apiLogoConfig = tmpLogoCacheConfig.ApiLogo
					}

				} else if len(tmpLogoCacheConfig.ApiBlackList) > 0 {
					isInBlackCity := false
					for _, item := range tmpLogoCacheConfig.ApiBlackList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInBlackCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInBlackCity = true
								break
							}
						}
					}
					if isInBlackCity {
					} else {
						apiLogoConfig = tmpLogoCacheConfig.ApiLogo
					}
				}
			}
		}
	}

	// logo pos id
	logoPosIDCacheKey := "go_logo_config_pos_id:" + localPosID
	cacheLogoPosIDValue, cacheLogoPosIDError := db.GlbBigCacheMinute.Get(logoPosIDCacheKey)
	if cacheLogoPosIDError != nil {
	} else {
		var tmpLogoCacheConfig models.LogoDataCacheConfigStu
		json.Unmarshal([]byte(cacheLogoPosIDValue), &tmpLogoCacheConfig)

		// posid sdk logo config
		if len(tmpLogoCacheConfig.SdkWhiteList) == 0 && len(tmpLogoCacheConfig.SdkBlackList) == 0 {
			sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
		} else {
			if len(mhReq.Device.LBSIPProvince) == 0 || len(mhReq.Device.LBSIPCity) == 0 {
				sdkLogoConfig = ""
			} else {
				if len(tmpLogoCacheConfig.SdkWhiteList) > 0 {
					isInWhiteCity := false
					for _, item := range tmpLogoCacheConfig.SdkWhiteList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInWhiteCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInWhiteCity = true
								break
							}
						}
					}
					if isInWhiteCity {
						sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
					}
				} else if len(tmpLogoCacheConfig.SdkBlackList) > 0 {
					isInBlackCity := false
					for _, item := range tmpLogoCacheConfig.SdkBlackList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInBlackCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInBlackCity = true
								break
							}
						}
					}
					if isInBlackCity {
					} else {
						sdkLogoConfig = tmpLogoCacheConfig.SdkLogo
					}
				}
			}
		}

		// posid api logo config
		if len(tmpLogoCacheConfig.ApiWhiteList) == 0 && len(tmpLogoCacheConfig.ApiBlackList) == 0 {
			apiLogoConfig = tmpLogoCacheConfig.ApiLogo
		} else {
			if len(mhReq.Device.LBSIPProvince) == 0 || len(mhReq.Device.LBSIPCity) == 0 {
				apiLogoConfig = ""
			} else {
				if len(tmpLogoCacheConfig.ApiWhiteList) > 0 {
					isInWhiteCity := false
					for _, item := range tmpLogoCacheConfig.ApiWhiteList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInWhiteCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInWhiteCity = true
								break
							}
						}
					}
					if isInWhiteCity {
						apiLogoConfig = tmpLogoCacheConfig.ApiLogo
					}

				} else if len(tmpLogoCacheConfig.ApiBlackList) > 0 {
					isInBlackCity := false
					for _, item := range tmpLogoCacheConfig.ApiBlackList {
						tmpRegionArray := strings.Split(item, "-")
						if len(tmpRegionArray) == 1 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince {
								isInBlackCity = true
								break
							}
						} else if len(tmpRegionArray) == 2 {
							if tmpRegionArray[0] == mhReq.Device.LBSIPProvince && tmpRegionArray[1] == mhReq.Device.LBSIPCity {
								isInBlackCity = true
								break
							}
						}
					}
					if isInBlackCity {
					} else {
						apiLogoConfig = tmpLogoCacheConfig.ApiLogo
					}
				}
			}
		}
	}

	return sdkLogoConfig, apiLogoConfig
}

func GetDeviceDpi(ctx context.Context, manufacturer string, model string, os string, osv string) float64 {
	manufacturer = strings.Replace(manufacturer, " ", "", -1)
	manufacturer = strings.ToLower(manufacturer)

	model = strings.Replace(model, " ", "", -1)
	model = strings.ToLower(model)

	os = strings.Replace(os, " ", "", -1)
	os = strings.ToLower(os)

	osv = strings.Replace(osv, " ", "", -1)
	osv = strings.ToLower(osv)

	cacheValue, cacheError := db.GlbBigCache.Get(fmt.Sprintf(config.ADX_SSP_CACHE_DPI_KEY, manufacturer+"_"+model+"_"+os+"_"+osv))
	if cacheError != nil {
	} else {
		dpiFloat, _ := strconv.ParseFloat(string(cacheValue), 64)
		if dpiFloat > 0 {
			return dpiFloat
		}
	}

	cacheValue, cacheError = db.GlbBigCache.Get(fmt.Sprintf(config.ADX_SSP_CACHE_DPI_KEY, os+"_"+osv))
	if cacheError != nil {
	} else {
		dpiFloat, _ := strconv.ParseFloat(string(cacheValue), 64)
		if dpiFloat > 0 {
			return dpiFloat
		}
	}

	cacheValue, cacheError = db.GlbBigCache.Get(fmt.Sprintf(config.ADX_SSP_CACHE_DPI_KEY, os))
	if cacheError != nil {
	} else {
		dpiFloat, _ := strconv.ParseFloat(string(cacheValue), 64)
		if dpiFloat > 0 {
			return dpiFloat
		}
	}

	return 480
}

// 补空ua, android
func GetDeviceUA(ctx context.Context, mhReq *models.MHReq) string {
	if mhReq.Device.Os == "android" {
		uaCacheKey := fmt.Sprintf(config.ADX_SSP_CACHE_UA_KEY,
			strings.ToLower(strings.Replace(mhReq.Device.Manufacturer, " ", "", -1))+
				"_"+
				strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1))+
				"_"+
				strings.ToLower(strings.Replace(mhReq.Device.OsVersion, " ", "", -1)),
		)

		cacheValue, cacheError := db.GlbBigCache.Get(uaCacheKey)
		if cacheError != nil {
		} else {
			return string(cacheValue)
		}

		return "Mozilla/5.0 (Linux; Android " + mhReq.Device.OsVersion + "; " + mhReq.Device.Model + " Build/" +
			mhReq.Device.Manufacturer + mhReq.Device.Model + "; wv)"
	} else if mhReq.Device.Os == "ios" {
		return "Mozilla/5.0 (iPhone; CPU iPhone OS " + strings.Replace(mhReq.Device.OsVersion, ".", "_", -1) + " like Mac OS X)"
	}

	return ""
}
