package up_coolads_freeze

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &CooladsPipline{Common: common}
}

/**
* Private Methods
**/

func (p *CooladsPipline) replaceLinkString(link string, price int) string {
	linkString := link
	linkString = strings.Replace(linkString, "__DOWN_X__", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "__DOWN_Y__", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "__UP_X__", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "__UP_Y__", "__UP_Y__", -1)

	// if p.Common.IsLogicPixel {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	// } else {
	// 	linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
	// 	linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	// }

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "__DOWN_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__DOWN_Y__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_Y__", "-999", -1)
	}

	if p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		encryptPrice := p.encryptPrice(utils.ConvertIntToString(price))
		if len(encryptPrice) > 0 {

			if strings.Contains(linkString, "__PRICE__") {
				fmt.Println("COOLADSDEBUG: linkString:", linkString, "__PRICE__:", encryptPrice, price)
			}

			linkString = strings.Replace(linkString, "__PRICE__", encryptPrice, -1)
		} else {
			fmt.Println("encryptPrice is empty")
		}
	} else {
		linkString = strings.Replace(linkString, "__PRICE__", utils.ConvertIntToString(price), -1)
	}

	return linkString
}

func (p *CooladsPipline) encryptKey() string {
	return p.Common.PlatformPos.PlatformAppPriceEncrypt
}

func (p *CooladsPipline) encryptPrice(price string) string {
	encryptKey := p.encryptKey()

	if len(encryptKey) == 0 {
		fmt.Println("price: ", price, "PlatformAppPriceEncrypt: ", p.Common.PlatformPos.PlatformAppPriceEncrypt)
		fmt.Printf("PlatformPos: %+v", p.Common.PlatformPos)
		return ""
	}
	result := base64.StdEncoding.EncodeToString(utils.AesCBCPKCS5Encrypt(price, p.encryptKey()))

	return url.QueryEscape(result)
}

// func (p *CooladsPipline) decryptPrice(encryptedPrice string) (string, error) {
// 	var priceString string
// 	decodeString, err := base64.StdEncoding.DecodeString(encryptedPrice)
// 	if err != nil {
// 		return priceString, err
// 	}
// 	priceString = string(utils.AesCBCPKCS5Decrypt(decodeString, []byte(p.encryptKey())))
// 	return priceString, nil
// }

/**
 * * 函数式方法模板
 */
func (p *CooladsPipline) actionTemplate() *CooladsPipline {

	return p
}

/**
* Public Methods
**/

func (p *CooladsPipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("COOLADSDEBUG: GetFromCoolads error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *CooladsPipline) SetupRequest() up_common.PiplineInterface {

	// Setup CooladsRequestObject
	requestObject := CooladsRequestObject{}
	{
		requestObject.Traceid = p.Common.UUID
		requestObject.AppId = p.Common.PlatformPos.PlatformAppID
		requestObject.Pid = p.Common.PlatformPos.PlatformPosID
		requestObject.Appname = p.Common.PlatformPos.PlatformAppName
		requestObject.BundleId = p.Common.PlatformPos.PlatformAppBundle
		requestObject.Appversion = p.Common.PlatformPos.PlatformAppVersion
		requestObject.BidFloor = p.Common.CategoryInfo.FloorPrice
		//requestObject.Ppi = 0 // TODO:
		requestObject.Nw = NewCooladsDeviceType(up_common.UpCommonConnectTypeEnum(p.Common.MhReq.Network.ConnectType))

		if p.Common.IsAndroid() {
			requestObject.S = COOLADS_OS_ANDROID

			if len(p.Common.MhReq.Device.AndroidID) > 0 {
				requestObject.Androidid = p.Common.MhReq.Device.AndroidID
			}

			if len(p.Common.MhReq.Device.AndroidIDMd5) > 0 {
				requestObject.AndroididMd5 = p.Common.MhReq.Device.AndroidIDMd5
			}

			if p.Common.IsAndroidMajorLessThanTen() {
				if len(p.Common.MhReq.Device.Imei) > 0 {
					requestObject.Imei = p.Common.MhReq.Device.Imei
				} else {
					p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
						up_common.MH_UP_ERROR_CODE_900101,
						up_common.MH_UP_ERROR_CODE_104013)
				}
			} else {
				if len(p.Common.MhReq.Device.Oaid) > 0 {
					requestObject.Oaid = p.Common.MhReq.Device.Oaid
				} else {
					p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
						up_common.MH_UP_ERROR_CODE_900101,
						up_common.MH_UP_ERROR_CODE_104013)
				}
			}

		} else {
			requestObject.S = COOLADS_OS_IOS
			if len(p.Common.MhReq.Device.Idfa) > 0 {
				requestObject.Idfa = p.Common.MhReq.Device.Idfa
			}

			if len(p.Common.MhReq.Device.IdfaMd5) > 0 {
				requestObject.IdfaMd5 = p.Common.MhReq.Device.IdfaMd5
			}
		}

		requestObject.Ua = p.Common.MhReq.Device.Ua
		requestObject.Vendor = p.Common.MhReq.Device.Manufacturer
		requestObject.Devicetype = p.Common.MhReq.Device.Model // TODO:
		requestObject.Sv = p.Common.MhReq.Device.OsVersion
		requestObject.W = p.Common.MhReq.Device.ScreenWidth
		requestObject.H = p.Common.MhReq.Device.ScreenHeight
		requestObject.Adw = p.Common.PlatformPos.PlatformPosWidth
		requestObject.Adh = p.Common.PlatformPos.PlatformPosHeight
		requestObject.Ip = p.Common.MhReq.Device.IP
		//requestObject.Imsi = "" // TODO:
		requestObject.ApiVersion = 10
		requestObject.Orientation = NewCooladsOrientation(up_common.UpCommonScreenDirection(p.Common.MhReq.Device.Orientation))

	}
	p.Request = &requestObject

	return p
}

func (p *CooladsPipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.S = NewCooladsOS(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Sv = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Devicetype = p.Common.ReplacedValues.Model
	}

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Vendor = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		p.Request.Imei = p.Common.ReplacedValues.Imei
	}

	// if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
	// 	p.Request.Device.ImeiMd5 = p.Common.ReplacedValues.ImeiMd5
	// }

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		p.Request.Oaid = p.Common.ReplacedValues.Oaid
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		p.Request.Androidid = p.Common.ReplacedValues.AndroidId
	}

	// if len(p.Common.ReplacedValues.AndroidIdMd5) > 0 {
	// 	p.Request.Device.AndroidIdMd5 = p.Common.ReplacedValues.AndroidIdMd5
	// }

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Ua = p.Common.ReplacedValues.Ua
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		p.Request.Idfa = p.Common.ReplacedValues.Idfa
	}

	p.Request.Orientation = NewCooladsOrientation(p.Common.ReplacedValues.ScreenDirection)

	return p
}

func (p *CooladsPipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("GET", p.Common.PlatformPos.PlatformAppUpURL, nil)
	//httpRequest.Header.Add("Content-Type", "application/json")
	//httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	query := httpRequest.URL.Query()

	jsonMap := map[string]interface{}{}
	json.Unmarshal(jsonData, &jsonMap)

	for key, value := range jsonMap {
		stringValue, ok := value.(string)
		if ok {
			query.Add(key, stringValue)
		} else {
			intValue, ok := value.(int)
			if ok {
				query.Add(key, utils.ConvertIntToString(intValue))
			}
		}

	}

	httpRequest.URL.RawQuery = query.Encode()
	fmt.Println(httpRequest.URL.String())

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject CooladsResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	fmt.Println("COOLADSDEBUG: bodyContent:", string(bodyContent), ", response.StatusCode:", response.StatusCode)

	p.Response = &responseObject

	if len(p.Response.Title) == 0 && len(p.Response.Desc) == 0 {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *CooladsPipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	for _, responseItem := range []*CooladsResponseObject{p.Response} {
		bidPrice := int(responseItem.Price)

		p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
		p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

		// 成功时的价格比例
		randPriceRatio := p.Common.RandPriceRatio()
		winPrice := int(bidPrice * randPriceRatio / 100)

		// 失败时的价格比例
		// randFailedPriceRatio := p.Common.RandFailedPriceRatio()

		if p.Common.CategoryInfo.FinalPrice > bidPrice {
			p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

			// 如果有竞败链接，需要回调处理
			continue
		}

		if len(responseItem.Title) == 0 {
			continue
		}

		var commonAPIResponseObject up_common.UpCommonAPIResponseObject

		if len(responseItem.Title) > 0 {
			commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Title)
			commonAPIResponseObject.Title = responseItem.Title
		}

		if len(responseItem.Desc) > 0 {
			commonAPIResponseObject.Description = responseItem.Desc
		}

		if len(responseItem.Package) > 0 {
			commonAPIResponseObject.PackageName = responseItem.Package
		}

		if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_ANDROID.String() {
			commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE

			if len(responseItem.Link) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.Link
				commonAPIResponseObject.AdUrl = responseItem.Link
			}

			if len(responseItem.CompName) > 0 {
				commonAPIResponseObject.AppName = responseItem.CompName
			}

			if len(responseItem.VersionName) > 0 {
				commonAPIResponseObject.AppVersion = responseItem.VersionName
			}

			if len(responseItem.SecretUrl) > 0 {
				commonAPIResponseObject.PrivacyUrl = responseItem.SecretUrl
			}

			// if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyInfo) > 0 {
			// 	commonAPIResponseObject.Permission = responseItem.AppPromotion.PrivacyPolicyInfo
			// } else {
			// 	commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			// }

			if len(responseItem.PermissionUrl) > 0 {
				commonAPIResponseObject.PermissionUrl = responseItem.PermissionUrl
			}

			commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024

		} else if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_IOS.String() {
			if len(responseItem.Link) > 0 {
				if strings.Contains(responseItem.Link, "apple.com") {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
				} else {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
				}

				commonAPIResponseObject.LandPageUrl = responseItem.Link
				commonAPIResponseObject.AdUrl = responseItem.Link
			}
		}

		commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

		commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

		var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

		if len(responseItem.DeepLink) > 0 || len(responseItem.UniversalLink) > 0 {
			commonAPIResponseObject.DeepLink = responseItem.DeepLink

			if len(responseItem.UniversalLink) > 0 {
				commonAPIResponseObject.DeepLink = responseItem.UniversalLink
			}

			var convTrack up_common.UpCommonAPIResponseConvTrackObject
			convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(
				p.Common.MhReq,
				p.Common.LocalPos,
				p.Common.PlatformPos,
				p.Common.UUID,
				p.Common.UUID,
				0,
				0,
				0,
				0)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			if len(responseItem.DplinkUrls) > 0 {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, responseItem.DplinkUrls...)
			}

			convTrack.ConvUrls = respListItemDeepLinkArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

			var convTrackFail up_common.UpCommonAPIResponseConvTrackObject
			convTrackFail.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL
			convTrack.ConvUrls = responseItem.DplinkErrUrls
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

			if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

				var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
				convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
				convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
			}
		}

		if len(responseItem.DwsUrls) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.DwsUrls
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if len(responseItem.DweUrls) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.DweUrls
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if len(responseItem.InstUrls) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.InstUrls
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if len(commonAPIResponseConvTrackObjects) > 0 {
			commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
		}

		// 图片
		var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

		if len(responseItem.Pics) > 0 {
			for _, image := range responseItem.Pics {
				var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
				commonAPIResponseImageObject.Url = image
				commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
				commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
				commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
			}
		} else if len(responseItem.Pic) > 0 {
			var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
			commonAPIResponseImageObject.Url = responseItem.Pic
			commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
			commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
		}

		commonAPIResponseObject.Images = commonAPIResponseImageObjects
		commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

		if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
			p.Common.SetPanicStringAndCodes("images is empty",
				up_common.MH_UP_ERROR_CODE_900201,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			p.Common.CategoryInfo.FloorPrice,
			p.Common.CategoryInfo.FinalPrice,
			bidPrice,
			0)

		if responseItem.UnfoldMonitorLink != nil && len(responseItem.UnfoldMonitorLink) > 0 {
			var impressionLinks []string

			p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

			impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

			for _, linkString := range responseItem.UnfoldMonitorLink {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				impressionLinks = append(impressionLinks, replacedLinkString)
			}

			commonAPIResponseObject.ImpressionLink = impressionLinks

		}

		if responseItem.ClickMonitorLink != nil && len(responseItem.ClickMonitorLink) > 0 {
			var clickLinks []string

			p.Common.MhClickLink.AddBigDataParams(bigdataParams)

			clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

			for _, linkString := range responseItem.ClickMonitorLink {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				clickLinks = append(clickLinks, replacedLinkString)
			}

			if len(responseItem.DplinkUrls) > 0 {
				clickLinks = append(clickLinks, responseItem.DplinkUrls...)
			}

			commonAPIResponseObject.ClickLink = clickLinks
		}

		// if p.Common.IsReportToWin {
		// 	if len(responseItem.Nurl) > 0 && bidPrice > 0 && p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		// 		go func() {
		// 			defer func() {
		// 				if err := recover(); err != nil {
		// 					fmt.Println("gdt win url panic:", err)
		// 				}
		// 			}()

		// 			replacedLinkString := p.replaceLinkString(responseItem.Nurl, winPrice)

		// 			winResponse := p.Common.RequestUrl(replacedLinkString)

		// 			models.BigDataWinPrice(
		// 				p.Common.UUID,
		// 				p.Common.LocalPos,
		// 				p.Common.PlatformPos,
		// 				1,
		// 				replacedLinkString,
		// 				winResponse)
		// 		}()
		// 	}
		// }

		commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
		commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

		list = append(list, &commonAPIResponseObject)
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("COOLADSDEBUG: responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *CooladsPipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
