package up_coolads_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
)

// CooladsRequest Objects
type CooladsRequestObject struct {
	Traceid            string                    `json:"traceid"`
	AppId              string                    `json:"appId"`
	Pid                string                    `json:"pid"`
	Appname            string                    `json:"appname,omitempty"`
	BundleId           string                    `json:"bundleId,omitempty"`
	Appversion         string                    `json:"appversion,omitempty"`
	AppversionCode     int                       `json:"appversionCode,omitempty"`
	Appstoreversion    string                    `json:"appstoreversion,omitempty"`
	BidFloor           int                       `json:"bidFloor,omitempty"`
	Elapsetime         int                       `json:"elapsetime,omitempty"`
	VerCodeOfAG        string                    `json:"verCodeOfAG,omitempty"`
	VerCodeOfHms       string                    `json:"verCodeOfHms,omitempty"`
	Ppi                int                       `json:"ppi"`
	Screendensity      int                       `json:"screendensity"`
	Nw                 CooladsRequestNetworkEnum `json:"nw"`
	Androidid          string                    `json:"androidid"`
	AndroididMd5       string                    `json:"androididMd5"`
	Imei               string                    `json:"imei"`
	ImeiMd5            string                    `json:"imeiMd5"`
	Oaid               string                    `json:"oaid"`
	OaidMd5            string                    `json:"oaidMd5"`
	Idfa               string                    `json:"idfa"`
	Caid               string                    `json:"caid,omitempty"`
	IdfaMd5            string                    `json:"idfaMd5"`
	Idfv               string                    `json:"idfv"`
	Ua                 string                    `json:"ua"`
	Mac                string                    `json:"mac,omitempty"`
	Vendor             string                    `json:"vendor"`
	Devicetype         string                    `json:"devicetype"`
	Sv                 string                    `json:"sv"`
	S                  CooladsRequestOSEnum      `json:"s"`
	W                  int                       `json:"w"`
	H                  int                       `json:"h"`
	Adw                int                       `json:"adw,omitempty"`
	Adh                int                       `json:"adh,omitempty"`
	Ip                 string                    `json:"ip"`
	Lng                string                    `json:"lng,omitempty"`
	Lat                string                    `json:"lat,omitempty"`
	Carrier            string                    `json:"carrier"`
	Imsi               string                    `json:"imsi"`
	Isdeeplink         bool                      `json:"isdeeplink,omitempty"`
	Isul               bool                      `json:"isul,omitempty"`
	BootMark           string                    `json:"boot_mark,omitempty"`
	UpdateMark         string                    `json:"update_mark,omitempty"`
	Manufacturer       string                    `json:"manufacturer,omitempty"`
	UpdateTime         string                    `json:"updateTime,omitempty"`
	ApiVersion         int                       `json:"apiVersion"`
	RomVersion         string                    `json:"romVersion"`
	Orientation        CooladsOrientationEnum    `json:"orientation"`
	MemorySize         float64                   `json:"memorySize,omitempty"`
	TimeZone           string                    `json:"timeZone,omitempty"`
	ModelCode          string                    `json:"modelCode,omitempty"`
	DiskSize           float64                   `json:"diskSize,omitempty"`
	Lmt                int                       `json:"lmt,omitempty"`
	PhoneName          string                    `json:"phoneName,omitempty"`
	OsUpdateTimeSecond string                    `json:"osUpdateTimeSecond,omitempty"`
	BatteryStatus      int                       `json:"batteryStatus,omitempty"`
	BatteryPower       int                       `json:"batteryPower,omitempty"`
	CpuNumber          int                       `json:"cpuNumber,omitempty"`
	CpuFrequency       float64                   `json:"cpuFrequency,omitempty"`
	Al                 string                    `json:"al,omitempty"`
}

type CooladsRequestNetworkEnum int

const (
	COOLADS_NETWORK_UNKNOWN CooladsRequestNetworkEnum = 1
	COOLADS_NETWORK_2G      CooladsRequestNetworkEnum = 2
	COOLADS_NETWORK_3G      CooladsRequestNetworkEnum = 3
	COOLADS_NETWORK_4G      CooladsRequestNetworkEnum = 4
	COOLADS_NETWORK_5G      CooladsRequestNetworkEnum = 5
	COOLADS_NETWORK_WIFI    CooladsRequestNetworkEnum = 20
)

func NewCooladsDeviceType(connectType up_common.UpCommonConnectTypeEnum) CooladsRequestNetworkEnum {
	switch connectType {
	case up_common.MH_UP_COMMON_CONNECTTYPE_2G:
		return COOLADS_NETWORK_2G
	case up_common.MH_UP_COMMON_CONNECTTYPE_3G:
		return COOLADS_NETWORK_3G
	case up_common.MH_UP_COMMON_CONNECTTYPE_4G:
		return COOLADS_NETWORK_4G
	case up_common.MH_UP_COMMON_CONNECTTYPE_5G:
		return COOLADS_NETWORK_5G
	case up_common.MH_UP_COMMON_CONNECTTYPE_WIFI:
		return COOLADS_NETWORK_WIFI
	}

	return COOLADS_NETWORK_UNKNOWN
}

type CooladsRequestOSEnum string

const (
	COOLADS_OS_ANDROID CooladsRequestOSEnum = "android"
	COOLADS_OS_IOS     CooladsRequestOSEnum = "ios"
)

func NewCooladsOS(os up_common.UpCommonOSEnum) CooladsRequestOSEnum {
	switch os {
	case up_common.MH_UP_COMMON_OS_ANDROID:
		return COOLADS_OS_ANDROID
	case up_common.MH_UP_COMMON_OS_IOS:
		return COOLADS_OS_IOS
	}

	return COOLADS_OS_ANDROID
}

type CooladsOrientationEnum int

const (
	COOLADS_ORIENTATION_PORTRAIT  CooladsOrientationEnum = 0
	COOLADS_ORIENTATION_LANDSCAPE CooladsOrientationEnum = 1
)

func NewCooladsOrientation(screenDirection up_common.UpCommonScreenDirection) CooladsOrientationEnum {
	switch screenDirection {
	case up_common.MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE:
		return COOLADS_ORIENTATION_LANDSCAPE
	case up_common.MH_UP_COMMON_SCREENDIRECTION_PORTRAIT:
		return COOLADS_ORIENTATION_PORTRAIT
	}

	return COOLADS_ORIENTATION_PORTRAIT
}

// CooladsResponse Objects

type CooladsResponseObject struct {
	Title             string   `json:"title"`
	Desc              string   `json:"desc"`
	MediaStyle        int      `json:"mediaStyle"`
	CompName          string   `json:"compName"`
	VersionName       string   `json:"versionName"`
	SecretUrl         string   `json:"secretUrl"`
	PermissionUrl     string   `json:"permissionUrl"`
	Package           string   `json:"package"`
	Pic               string   `json:"pic"`
	Price             int      `json:"price"`
	Pics              []string `json:"pics"`
	Link              string   `json:"link"`
	DeepLink          string   `json:"deepLink"`
	WxMiniProId       string   `json:"wxMiniProId"`
	WxMiniProPath     string   `json:"wxMiniProPath"`
	AdMark            string   `json:"adMark"`
	UniversalLink     string   `json:"universalLink"`
	UnfoldMonitorLink []string `json:"unfoldMonitorLink"`
	ClickMonitorLink  []string `json:"clickMonitorLink"`
	DwsUrls           []string `json:"dwsUrls"`
	DweUrls           []string `json:"dweUrls"`
	InstBUrls         []string `json:"instBUrls"`
	InstUrls          []string `json:"instUrls"`
	DnUrls            []string `json:"dnUrls"`
	DplinkUrls        []string `json:"dplinkUrls"`
	DplinkTryUrls     []string `json:"dplinkTryUrls"`
	DplinkErrUrls     []string `json:"dplinkErrUrls"`
}

type CooladsPipline struct {
	Common *up_common.UpCommonPipline

	Request  *CooladsRequestObject
	Response *CooladsResponseObject
}

func (r *CooladsPipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
