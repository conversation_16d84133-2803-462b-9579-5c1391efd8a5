package core

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromHuaWei ...
func GetFromHuaWei(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from huawei")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// 测试
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">单Feed流</el-radio>
	// <el-radio label="12">双Feed流</el-radio>
	// <el-radio label="8">原生模版(待删除)</el-radio>
	// <el-radio label="7">原生2.0(待删除)</el-radio>
	// <el-radio label="5">原生视频(待删除)</el-radio>
	// platformPos.PlatformPosID = "testx9dtjwj8hp"
	// platformPos.PlatformPosType = 11

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["pkgname"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	if len(platformPos.PlatformAppVersion) > 0 {
		reqAppInfoMap["version"] = platformPos.PlatformAppVersion
	}

	reqNetworkMap := map[string]interface{}{}
	// reqNetworkMap["ip"] = mhReq.Device.IP
	// reqNetworkMap["connection_type"] = mhReq.Network.ConnectType
	// reqNetworkMap["operator_type"] = mhReq.Network.Carrier

	if mhReq.Network.ConnectType == 0 {
		reqNetworkMap["type"] = 0
	} else if mhReq.Network.ConnectType == 1 {
		reqNetworkMap["type"] = 2
	} else if mhReq.Network.ConnectType == 2 {
		reqNetworkMap["type"] = 4
	} else if mhReq.Network.ConnectType == 3 {
		reqNetworkMap["type"] = 5
	} else if mhReq.Network.ConnectType == 4 {
		reqNetworkMap["type"] = 6
	} else if mhReq.Network.ConnectType == 7 {
		reqNetworkMap["type"] = 7
	}

	if mhReq.Network.Carrier == 1 {
		reqNetworkMap["carrier"] = 2
	} else if mhReq.Network.Carrier == 2 {
		reqNetworkMap["carrier"] = 1
	} else if mhReq.Network.Carrier == 3 {
		reqNetworkMap["carrier"] = 3
	} else {
		reqNetworkMap["carrier"] = 0
	}

	// if len(mhReq.Device.Mac) > 0 {
	// 	reqNetworkMap["mac"] = mhReq.Device.Mac
	// }

	// multislot
	reqMultiSlotArray := []map[string]interface{}{}
	reqSlotItemMap := map[string]interface{}{}
	reqSlotItemMap["slotid"] = platformPos.PlatformPosID
	if platformPos.PlatformPosIsDebug == 1 {
		reqSlotItemMap["test"] = 1
	} else {
		reqSlotItemMap["test"] = 0
	}
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">单Feed流</el-radio>
	// <el-radio label="12">双Feed流</el-radio>
	// <el-radio label="8">原生模版(待删除)</el-radio>
	// <el-radio label="7">原生2.0(待删除)</el-radio>
	// <el-radio label="5">原生视频(待删除)</el-radio>

	// 请求的广告类型:  1:开屏广告
	// 	 3:原生广告
	// 	 7:激励视频
	// 	 8:Banner
	// 	 12:插屏
	// 	 60:视频贴片广告
	if platformPos.PlatformPosType == 2 {
		reqSlotItemMap["adtype"] = 1
	} else if platformPos.PlatformPosType == 4 {
		reqSlotItemMap["adtype"] = 3
	} else if platformPos.PlatformPosType == 11 {
		reqSlotItemMap["adtype"] = 7
	} else if platformPos.PlatformPosType == 1 {
		reqSlotItemMap["adtype"] = 8
	} else if platformPos.PlatformPosType == 3 {
		reqSlotItemMap["adtype"] = 12
	} else if platformPos.PlatformPosType == 13 {
		reqSlotItemMap["adtype"] = 60
	}
	reqMultiSlotArray = append(reqMultiSlotArray, reqSlotItemMap)

	reqDeviceInfoMap := map[string]interface{}{}
	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				reqDeviceInfoMap["imei"] = mhReq.Device.Imei
			} else {
				fmt.Println("get from huawei null imei")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				if strings.Contains(mhReq.Device.Oaid, "00000000") {
				} else {
					reqDeviceInfoMap["isTrackingEnabled"] = "1"
				}
				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
			} else {
				fmt.Println("get from huawei null oaid")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}

	} else if mhReq.Device.Os == "ios" {

		fmt.Println("get from huawei ios")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	reqDeviceInfoMap["type"] = 4
	if platformPos.PlatformAppIsReportUa == 1 {
		reqDeviceInfoMap["useragent"] = destConfigUA
	}
	reqDeviceInfoMap["os"] = "Android"
	if len(mhReq.Device.OsVersion) > 0 {
		reqDeviceInfoMap["version"] = mhReq.Device.OsVersion
	}
	reqDeviceInfoMap["maker"] = mhReq.Device.Manufacturer

	if len(mhReq.Device.Model) > 0 {
		reqDeviceInfoMap["model"] = mhReq.Device.Model
	}
	if mhReq.Device.ScreenWidth > 0 {
		reqDeviceInfoMap["width"] = mhReq.Device.ScreenWidth
	}
	if mhReq.Device.ScreenHeight > 0 {
		reqDeviceInfoMap["height"] = mhReq.Device.ScreenHeight
	}
	reqDeviceInfoMap["language"] = "zh"
	reqDeviceInfoMap["localeCountry"] = "CN"
	reqDeviceInfoMap["belongCountry"] = "CN"
	reqDeviceInfoMap["verCodeOfHms"] = mhReq.Device.HMSCoreVersion
	reqDeviceInfoMap["clientTime"] = time.Now().Format("2006-01-02 15:04:05.000-0700")
	reqDeviceInfoMap["verCodeOfAG"] = mhReq.Device.AppStoreVersion
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	// redis hms_core, appstore_version
	if len(mhReq.Device.HMSCoreVersion) > 0 && len(mhReq.Device.AppStoreVersion) > 0 {
		reqDeviceInfoMap["verCodeOfHms"] = mhReq.Device.HMSCoreVersion
		reqDeviceInfoMap["verCodeOfAG"] = mhReq.Device.AppStoreVersion
	} else {
		if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		} else {
			redisHuaWeiValue, redisErr := db.GlbRedis.Get(c, "huawei_"+mhReq.Device.DIDMd5).Result()
			if redisErr != nil {
				// fmt.Println("redis error:", redisErr)
			} else {
				// fmt.Println("redis value:", redisValue)
				var tmpDIDHuaWeiHmsData models.DIDHuaWeiHMSStu
				json.Unmarshal([]byte(redisHuaWeiValue), &tmpDIDHuaWeiHmsData)

				reqDeviceInfoMap["verCodeOfHms"] = tmpDIDHuaWeiHmsData.HMSCoreVersion
				reqDeviceInfoMap["verCodeOfAG"] = tmpDIDHuaWeiHmsData.AppStoreVersion
			}
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imei")
					delete(reqDeviceInfoMap, "oaid")

					reqDeviceInfoMap["version"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["maker"] = didRedisData.Manufacturer

					// isOKReplaceManufacturer := false
					// for _, manufactureConfigItem := range manufactureConfigArray {
					// 	if strings.ToLower(didRedisData.Manufacturer) == manufactureConfigItem {
					// 		isOKReplaceManufacturer = true
					// 	}
					// }
					// if isOKReplaceManufacturer {
					// } else {
					// 	bigdataExtra.InternalCode = 900011
					// 	bigdataExtra.ExternalCode = 102006

					// 	return MhUpErrorRespMap("", bigdataExtra)
					// }
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["useragent"] = didRedisData.Ua

						replaceUA = didRedisData.Ua
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imei"] = didRedisData.Imei
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"version":   "3.4",
		"app":       reqAppInfoMap,
		"device":    reqDeviceInfoMap,
		"multislot": reqMultiSlotArray,
		"network":   reqNetworkMap,
	}
	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("huawei req: " + string(jsonData))

	// Authorization:Digest username={appId},realm= /ppsadx/getResult,nonce={timestamp},response={digest},algorithm=HmacSHA256,usertype ={usertype},keyid={keyid}
	// Digest username=254869917055069824,realm=ppsadx/getResult,nonce=1606724886022,response=fd54f0dbb5dd9f74b001d4790a30bb3433069d2a311706c057fce3dc08d50479,algorithm=HmacSHA256,usertype=1,keyid=41
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"
	httpHeader["Authorization"] = getHuaWeiAuthorization(platformPos)

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, config.UpHuaWeiURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("huawei resp: " + string(bodyContent))

	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&huawei", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	huaweiRespStu := HuaWeiRespStu{}
	json.Unmarshal([]byte(bodyContent), &huaweiRespStu)

	if huaweiRespStu.RetCode != 200 {
		bigdataExtra.UpRespCode = huaweiRespStu.RetCode

		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(huaweiRespStu.Multiad) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, huaweiAdInfoItem := range huaweiRespStu.Multiad {
		adInfoItem := huaweiAdInfoItem
		if adInfoItem.RetCode30 != 200 {
			respTmpInternalCode = 900103
			respTmpRespFailedNum = mhReq.Pos.AdCount
			continue
		}

		if len(adInfoItem.Content) == 0 {
			respTmpInternalCode = 900103
			respTmpRespFailedNum = mhReq.Pos.AdCount
			continue
		}

		adInfoContentItem := adInfoItem.Content[0]

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		huaweiEcpm := int(adInfoContentItem.Ecpm * 100)

		respTmpPrice = respTmpPrice + huaweiEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if huaweiEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			huaweiEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {

			if localPosFinalPrice > huaweiEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(huaweiEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoContentItem.MetaData.ApkInfo.AppName) > 0 {
			tmpTitle, _ := url.QueryUnescape(adInfoContentItem.MetaData.ApkInfo.AppName)
			respListItemMap["title"] = tmpTitle
		}

		// description
		if len(adInfoContentItem.MetaData.Title) > 0 {
			tmpDescription, _ := url.QueryUnescape(adInfoContentItem.MetaData.Title)
			respListItemMap["description"] = tmpDescription
		} else if len(adInfoContentItem.MetaData.ApkInfo.Description) > 0 {
			tmpDescription, _ := url.QueryUnescape(adInfoContentItem.MetaData.ApkInfo.Description)
			respListItemMap["description"] = tmpDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// crid
		respListItemMap["crid"] = adInfoContentItem.ContentID

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// conv tracks
		var respListItemConvArray []map[string]interface{}

		// deep link
		if len(adInfoContentItem.MetaData.Intent) > 0 {
			// tmpIntent, _ := url.QueryUnescape(adInfoContentItem.MetaData.Intent)
			if strings.Contains(adInfoContentItem.MetaData.Intent, "intent%3A%2F%2F") {
				continue
			} else if strings.Contains(adInfoContentItem.MetaData.Intent, "hwpps://landingpage") {

				respListItemMap["deep_link"] = adInfoContentItem.MetaData.Intent
				// fmt.Println("huawei resp deeplink: " + adInfoContentItem.MetaData.Intent)
			} else {

				tmpIntent, _ := url.QueryUnescape(adInfoContentItem.MetaData.Intent)
				respListItemMap["deep_link"] = tmpIntent
				// fmt.Println("huawei resp deeplink: " + tmpIntent)
			}

			// 字段 appstore_package_name
			if strings.HasPrefix(adInfoContentItem.MetaData.Intent, "hwpps://landingpage") {
				respListItemMap["appstore_package_name"] = "com.huawei.appmarket"
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			// huawei deeplink track
			for _, monitorItem := range adInfoContentItem.Monitor {
				if monitorItem.EventType == "intentSuccess" {
					for _, monitorUrlItem := range monitorItem.MonitorURL {
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, monitorUrlItem)
					}
				}
			}

			// maplehaze deeplink track
			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
		}

		// 下载完成
		var respListItemConvType2Array []string
		for _, monitorItem := range adInfoContentItem.Monitor {
			if monitorItem.EventType == "download" {
				for _, monitorUrlItem := range monitorItem.MonitorURL {
					respListItemConvType2Array = append(respListItemConvType2Array, monitorUrlItem)
				}
			}
		}
		if len(respListItemConvType2Array) > 0 {
			respListItemConv2Map := map[string]interface{}{}
			respListItemConv2Map["conv_type"] = 2
			respListItemConv2Map["conv_urls"] = respListItemConvType2Array

			respListItemConvArray = append(respListItemConvArray, respListItemConv2Map)
		}

		// 安装完成
		var respListItemConvType3Array []string
		for _, monitorItem := range adInfoContentItem.Monitor {
			if monitorItem.EventType == "install" {
				for _, monitorUrlItem := range monitorItem.MonitorURL {
					respListItemConvType3Array = append(respListItemConvType3Array, monitorUrlItem)
				}
			}
		}
		if len(respListItemConvType3Array) > 0 {
			respListItemConv3Map := map[string]interface{}{}
			respListItemConv3Map["conv_type"] = 3
			respListItemConv3Map["conv_urls"] = respListItemConvType3Array

			respListItemConvArray = append(respListItemConvArray, respListItemConv3Map)
		}

		if len(respListItemConvArray) > 0 {
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoContentItem.MetaData.ApkInfo.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoContentItem.MetaData.ApkInfo.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoContentItem.MetaData.Intent)
		}

		isVideo := false

		// creativetype 6:视频文 9:视频 11:视频带图和文(激励视频专用) 106:视频文带下载按钮
		if adInfoContentItem.CreativeType == 6 || adInfoContentItem.CreativeType == 9 || adInfoContentItem.CreativeType == 11 || adInfoContentItem.CreativeType == 106 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoContentItem.MetaData.VideoInfo.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoContentItem.MetaData.VideoInfo.VideoDuration/1000)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
				respListVideoItemMap["duration"] = adInfoContentItem.MetaData.VideoInfo.VideoDuration
			}
			// respListVideoItemMap["width"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialWidth
			// respListVideoItemMap["height"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialHeight
			respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			respListVideoItemMap["video_url"] = adInfoContentItem.MetaData.VideoInfo.VideoURL
			if len(adInfoContentItem.MetaData.VideoInfo.VideoURL) == 0 {
				fmt.Println("wrong video url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
			// cover_url
			if len(adInfoContentItem.MetaData.ImageInfo) > 0 && len(adInfoContentItem.MetaData.ImageInfo[0].URL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoContentItem.MetaData.ImageInfo[0].URL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			if platformPos.PlatformPosType == 11 {
				var respListEventTrackURLMap []map[string]interface{}

				respListVideoBeginEventTrackMap := map[string]interface{}{}
				respListVideoBeginEventTrackMap["event_type"] = 100
				var respListVideoBeginEventTrackURLMap []string
				for _, monitorItem := range adInfoContentItem.Monitor {
					if monitorItem.EventType == "playStart" {
						for _, monitorUrlItem := range monitorItem.MonitorURL {
							respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorUrlItem)
						}
					}
				}
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				if len(respListVideoBeginEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
				}

				respListVideoEndEventTrackMap := map[string]interface{}{}
				respListVideoEndEventTrackMap["event_type"] = 103
				var respListVideoEndEventTrackURLMap []string
				for _, monitorItem := range adInfoContentItem.Monitor {
					if monitorItem.EventType == "playEnd" {
						for _, monitorUrlItem := range monitorItem.MonitorURL {
							respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, monitorUrlItem)
						}
					}
				}
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				if len(respListVideoEndEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
				}

				if len(respListEventTrackURLMap) > 0 {
					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoContentItem.MetaData.ImageInfo) == 0 {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
			respListImageItemMap["url"] = adInfoContentItem.MetaData.ImageInfo[0].URL

			if adInfoContentItem.MetaData.ImageInfo[0].Width > 0 {
				respListImageItemMap["width"] = adInfoContentItem.MetaData.ImageInfo[0].Width
			}
			if adInfoContentItem.MetaData.ImageInfo[0].Height > 0 {
				respListImageItemMap["height"] = adInfoContentItem.MetaData.ImageInfo[0].Height
			}

			if adInfoContentItem.MetaData.ImageInfo[0].Width > 0 && adInfoContentItem.MetaData.ImageInfo[0].Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoContentItem.MetaData.ImageInfo[0].Width, adInfoContentItem.MetaData.ImageInfo[0].Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			// 如果高度大于宽度,返回无广告
			// if platformPos.PlatformPosType == 4 && adInfoContentItem.MetaData.ImageInfo[0].Width < adInfoContentItem.MetaData.ImageInfo[0].Height*3/2 {
			// 	respTmpInternalCode = 900105
			// 	respTmpRespFailedNum = respTmpRespFailedNum + 1

			// 	continue
			// }
			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoContentItem.InteractionType == 1 || adInfoContentItem.InteractionType == 7 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoContentItem.MetaData.ClickURL
			respListItemMap["landpage_url"] = adInfoContentItem.MetaData.ClickURL
		} else if adInfoContentItem.InteractionType == 2 || adInfoContentItem.InteractionType == 3 || adInfoContentItem.InteractionType == 4 || adInfoContentItem.InteractionType == 5 {

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoContentItem.MetaData.ApkInfo.DownloadURL
			respListItemMap["download_url"] = adInfoContentItem.MetaData.ApkInfo.DownloadURL

		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoContentItem.MetaData.ApkInfo.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoContentItem.MetaData.ApkInfo.PackageName

			// android
			if mhReq.Device.Os == "android" {
				tmpAppName := ""
				if len(adInfoContentItem.MetaData.ApkInfo.AppName) > 0 {
					tmpAppName, _ = url.QueryUnescape(adInfoContentItem.MetaData.ApkInfo.AppName)
				}
				appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, tmpAppName, adInfoContentItem.MetaData.ApkInfo.PackageName)
				if appInfoFromRedisErr == nil {
					respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
					respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
				}
			}

			if len(adInfoContentItem.MetaData.ApkInfo.DetailUrl) > 0 {
				respListItemMap["appinfo_url"] = adInfoContentItem.MetaData.ApkInfo.DetailUrl
			}
		}

		if len(adInfoContentItem.MetaData.ApkInfo.AppName) > 0 {
			tmpAppName, _ := url.QueryUnescape(adInfoContentItem.MetaData.ApkInfo.AppName)
			respListItemMap["app_name"] = tmpAppName
		}
		if len(adInfoContentItem.MetaData.ApkInfo.DeveloperName) > 0 {
			respListItemMap["publisher"] = adInfoContentItem.MetaData.ApkInfo.DeveloperName
		}
		if len(adInfoContentItem.MetaData.ApkInfo.VersionName) > 0 {
			respListItemMap["app_version"] = adInfoContentItem.MetaData.ApkInfo.VersionName
		}
		if len(adInfoContentItem.MetaData.PrivacyUrl) > 0 {
			respListItemMap["privacy_url"] = adInfoContentItem.MetaData.PrivacyUrl
		}
		if len(adInfoContentItem.MetaData.ApkInfo.Permissions) > 0 {
			permissionStr := ""
			for _, huaweiPermissionItem := range adInfoContentItem.MetaData.ApkInfo.Permissions {
				tmpPermission, _ := url.QueryUnescape(huaweiPermissionItem.PermissionLabel)
				permissionStr = permissionStr + tmpPermission + "\n"
			}
			respListItemMap["permission"] = permissionStr
		}
		if len(adInfoContentItem.MetaData.ApkInfo.PermissionUrl) > 0 {
			respListItemMap["permission_url"] = adInfoContentItem.MetaData.ApkInfo.PermissionUrl
		}
		if adInfoContentItem.MetaData.ApkInfo.FileSize > 0 {
			respListItemMap["package_size"] = int(adInfoContentItem.MetaData.ApkInfo.FileSize)
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, huaweiEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, monitorItem := range adInfoContentItem.Monitor {
			if monitorItem.EventType == "imp" {
				for _, monitorUrlItem := range monitorItem.MonitorURL {
					impItem := monitorUrlItem
					respListItemImpArray = append(respListItemImpArray, impItem)
				}
			}
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}

		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, monitorItem := range adInfoContentItem.Monitor {
			if monitorItem.EventType == "click" {
				for _, monitorUrlItem := range monitorItem.MonitorURL {
					clkItem := monitorUrlItem
					clkItem = strings.Replace(clkItem, "__HW_W__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					clkItem = strings.Replace(clkItem, "__HW_H__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					clkItem = strings.Replace(clkItem, "__HW_DOWN_X__", tmpDownX, -1)
					clkItem = strings.Replace(clkItem, "__HW_DOWN_Y__", tmpDownY, -1)
					clkItem = strings.Replace(clkItem, "__HW_UP_X__", tmpUpX, -1)
					clkItem = strings.Replace(clkItem, "__HW_UP_Y__", tmpUpY, -1)

					if platformPos.PlatformPosIsReportSLD == 1 {
						clkItem = strings.Replace(clkItem, "__HW_SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					} else {
						clkItem = strings.Replace(clkItem, "__HW_SLD__", "__SLD__", -1)
					}

					if mhReq.Device.XDPI > 0 {
						clkItem = strings.Replace(clkItem, "__HW_DENSITY__", utils.ConvertFloat32ToString(mhReq.Device.XDPI), -1)
					}

					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							clkItem = clkItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							clkItem = clkItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}

					respListItemClkArray = append(respListItemClkArray, clkItem)
				}
			}
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = huaweiEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	////////////////////////////////////////////////////////////////////////////////////////
	// if platformPos.PlatformAppID == "100380473" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("debug panic:", err)
	// 			}
	// 		}()

	// 		tmpRespByte, _ := json.Marshal(respMap)
	// 		models.SaveDemandRespDataToHolo(c, bigdataUID, platformPos.PlatformAppID, platformPos.PlatformPosID, string(tmpRespByte))
	// 	}()
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// kuaishou resp
	respKuaiShou := models.MHUpResp{}
	respKuaiShou.RespData = &mhResp
	respKuaiShou.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respKuaiShou
}

func getHuaWeiAuthorization(platformPos *models.PlatformPosStu) string {
	// appID := "639129698365866368"
	appID := platformPos.PlatformAppPublisherID

	realm := "/ppsadx/getResult"
	nonce := utils.ConvertInt64ToString(utils.GetCurrentMilliSecond())
	algorithm := "HmacSHA256"
	userType := "1"
	keyID := platformPos.PlatformAppKeyID

	hmacData := nonce + ":" + "POST" + ":" + realm
	// hmacSecret := appID + ":" + realm[1:] + ":" + "f6c0423d17b922d8cdc6ffcd47b5d27cd81f6380169f711adace35aee700b55c"
	hmacSecret := appID + ":" + realm[1:] + ":" + platformPos.PlatformAppSignKey

	hmacValue := hmacSha256(hmacData, hmacSecret)
	// fmt.Println("huawei hmacData: " + hmacData)
	// fmt.Println("huawei hmacSecret: " + hmacSecret)
	// fmt.Println("huawei hmac: " + hmacValue)

	authorization := "Digest " + "username=" + appID + ",realm=" + realm[1:] + ",nonce=" + nonce + ",response=" + hmacValue + ",algorithm=" + algorithm + ",usertype=" + userType + ",keyid=" + keyID
	// fmt.Println("huawei authorization: " + authorization)

	return authorization
}

func hmacSha256(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// HuaWeiRespStu ...
type HuaWeiRespStu struct {
	RetCode int               `json:"retcode"`
	Multiad []HuaWeiRespAdStu `json:"multiad"`
}

// HuaWeiRespAdStu ...
type HuaWeiRespAdStu struct {
	RetCode30 int                      `json:"retcode30"`
	SlotID    string                   `json:"slotid"`
	Content   []HuaWeiRespAdContentStu `json:"content"`
}

// HuaWeiRespAdContentStu ...
type HuaWeiRespAdContentStu struct {
	ContentID       string                          `json:"contentid"`
	StartTime       float32                         `json:"starttime"`
	EndTime         float32                         `json:"endtime"`
	InteractionType int                             `json:"interactiontype"`
	CreativeType    int                             `json:"creativetype"`
	MetaData        HuaWeiRespAdContentMetaDataStu  `json:"metaData"`
	Monitor         []HuaWeiRespAdContentMonitorStu `json:"monitor"`
	FilterList      []int                           `json:"filterList"`
	Ecpm            float32                         `json:"price"` // 单位: 元/千次
	// Ext             []HuaWeiRespAdContentExtItemStu `json:"ext"`
}

// HuaWeiRespAdContentMetaDataStu ...
type HuaWeiRespAdContentMetaDataStu struct {
	Title             string                                    `json:"title"`
	ImageInfo         []HuaWeiRespAdContentMetaDataImageInfoStu `json:"imageInfo"`
	ClickURL          string                                    `json:"clickUrl"`
	VideoInfo         HuaWeiRespAdContentMetaDataVideoInfoStu   `json:"videoInfo"`
	ApkInfo           HuaWeiRespAdContentMetaDataApkInfoStu     `json:"apkInfo"`
	Duration          float32                                   `json:"duration"`
	ScreenOrientation string                                    `json:"screenOrientation"`
	PrivacyUrl        string                                    `json:"privacyUrl"`
	Intent            string                                    `json:"intent"`
}

// HuaWeiRespAdContentMetaDataImageInfoStu ...
type HuaWeiRespAdContentMetaDataImageInfoStu struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

// HuaWeiRespAdContentMetaDataVideoInfoStu ...
type HuaWeiRespAdContentMetaDataVideoInfoStu struct {
	VideoURL      string  `json:"videoDownloadUrl"`
	VideoDuration int     `json:"videoDuration"`
	VideoFileSize int     `json:"videoFileSize"`
	VideoRatio    float32 `json:"videoRatio"`
}

// HuaWeiRespAdContentMetaDataApkInfoStu ...
type HuaWeiRespAdContentMetaDataApkInfoStu struct {
	DownloadURL       string                                            `json:"url"`
	VersionCode       string                                            `json:"versionCode"`
	FileSize          float32                                           `json:"fileSize"`
	PackageName       string                                            `json:"packageName"`
	SecondDownloadURL string                                            `json:"secondUrl"`
	AppName           string                                            `json:"appName"`
	Permissions       []HuaWeiRespAdContentMetaDataApkInfoPermissionStu `json:"permissions"`
	VersionName       string                                            `json:"versionName"`
	Description       string                                            `json:"appDesc"`
	IconURL           string                                            `json:"appIcon"`
	DeveloperName     string                                            `json:"developerName"`
	PermissionUrl     string                                            `json:"permissionUrl"`
	DetailUrl         string                                            `json:"detailUrl"`
}

// HuaWeiRespAdContentMetaDataApkInfoPermissionStu ...
type HuaWeiRespAdContentMetaDataApkInfoPermissionStu struct {
	PermissionLabel  string `json:"permissionLabel"`
	GroupDescription string `json:"groupDesc"`
	TargetSDK        string `json:"targetSDK"`
}

// HuaWeiRespAdContentMonitorStu ...
type HuaWeiRespAdContentMonitorStu struct {
	EventType  string   `json:"eventType"`
	MonitorURL []string `json:"url"`
}

// HuaWeiRespAdContentExtItemStu ...
// type HuaWeiRespAdContentExtItemStu struct {
// 	Key   string `json:"key"`
// 	Value string `json:"value"`
// }
