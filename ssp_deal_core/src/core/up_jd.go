package core

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/jd_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

// GetFromJD ...
func GetFromJD(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from jd")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	// tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// req device
	jdReqDevice := &jd_up.Device{}

	jdReqDevice.Ua = proto.String(destConfigUA)
	jdReqDevice.Ip = proto.String(mhReq.Device.IP)

	switch mhReq.Network.ConnectType {
	case 0:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_UNKNOWN.Enum()
	case 1:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_WIFI.Enum()
	case 2:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_2G.Enum()
	case 3:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_3G.Enum()
	case 4:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_4G.Enum()
	case 7:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_5G.Enum()
	default:
		jdReqDevice.ConnectionType = jd_up.ConnectionType_CELLULAR_UNKNOWN.Enum()
	}

	switch mhReq.Network.Carrier {
	case 1:
		jdReqDevice.Carrier = jd_up.CarrierType_CHINA_MOBILE.Enum()
	case 2:
		jdReqDevice.Carrier = jd_up.CarrierType_CHINA_UNICOM.Enum()
	case 3:
		jdReqDevice.Carrier = jd_up.CarrierType_CHINA_TELECOM.Enum()
	default:
		jdReqDevice.Carrier = jd_up.CarrierType_UNKNOWN_CARRIER.Enum()
	}

	jdReqDevice.ScreenHeight = proto.Int32(int32(mhReq.Device.ScreenHeight))
	jdReqDevice.ScreenWidth = proto.Int32(int32(mhReq.Device.ScreenWidth))

	if mhReq.Device.Os == "android" {
		jdReqDevice.Os = jd_up.OperatingSystem_OS_ANDROID.Enum()
	} else {
		jdReqDevice.Os = jd_up.OperatingSystem_OS_IOS.Enum()
	}
	jdReqDevice.OsVersion = proto.String(mhReq.Device.OsVersion)

	jdReqDevice.Model = proto.String(mhReq.Device.Model)
	jdReqDevice.Make = proto.String(mhReq.Device.Manufacturer)

	// 原始请求ios参数是否ok
	isIosDeviceOK := false
	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				jdReqDevice.Imei = proto.String(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				jdReqDevice.ImeiMd5 = proto.String(strings.ToUpper(mhReq.Device.ImeiMd5))
			} else {
				fmt.Println("get from jd error req < 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				jdReqDevice.Oaid = proto.String(mhReq.Device.Oaid)
			} else if len(mhReq.Device.OaidMd5) > 0 {
				jdReqDevice.OaidMd5 = proto.String(mhReq.Device.OaidMd5)
			} else {
				fmt.Println("get from jd error req > 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}

	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				jdReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var caidArray []*jd_up.Device_Caid

				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						var caidInfo jd_up.Device_Caid
						caidInfo.Id = proto.String(item.CAID)
						caidInfo.Version = proto.String(item.CAIDVersion)
						caidArray = append(caidArray, &caidInfo)
						break
					}
				}

				if len(caidArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					jdReqDevice.Caids = caidArray
				}
			} else {
				var caidArray []*jd_up.Device_Caid

				for _, item := range mhReq.Device.CAIDMulti {
					var caidInfo jd_up.Device_Caid
					caidInfo.Id = proto.String(item.CAID)
					caidInfo.Version = proto.String(item.CAIDVersion)
					caidArray = append(caidArray, &caidInfo)
				}

				if len(caidArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					jdReqDevice.Caids = caidArray
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

				jdReqDevice.Language = proto.String(mhReq.Device.Language)
				jdReqDevice.HwName = proto.String(mhReq.Device.DeviceNameMd5)
				jdReqDevice.HwMachine = proto.String(mhReq.Device.HardwareMachine)
				jdReqDevice.CountryCode = proto.String(mhReq.Device.Country)
				jdReqDevice.SysMemory = proto.String(mhReq.Device.PhysicalMemoryByte)
				jdReqDevice.SysDiskSize = proto.String(mhReq.Device.HarddiskSizeByte)
				jdReqDevice.OsUpdateTime = proto.String(mhReq.Device.SystemUpdateSec)

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					jdReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []*jd_up.Device_Caid

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo jd_up.Device_Caid
							caidInfo.Id = proto.String(item.CAID)
							caidInfo.Version = proto.String(item.CAIDVersion)
							caidArray = append(caidArray, &caidInfo)
							break
						}
					}

					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						jdReqDevice.Caids = caidArray
					}
				} else {
					var caidArray []*jd_up.Device_Caid

					for _, item := range mhReq.Device.CAIDMulti {
						var caidInfo jd_up.Device_Caid
						caidInfo.Id = proto.String(item.CAID)
						caidInfo.Version = proto.String(item.CAIDVersion)
						caidArray = append(caidArray, &caidInfo)
					}

					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						jdReqDevice.Caids = caidArray
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

					jdReqDevice.Language = proto.String(mhReq.Device.Language)
					jdReqDevice.HwName = proto.String(mhReq.Device.DeviceNameMd5)
					jdReqDevice.HwMachine = proto.String(mhReq.Device.HardwareMachine)
					jdReqDevice.CountryCode = proto.String(mhReq.Device.Country)
					jdReqDevice.SysMemory = proto.String(mhReq.Device.PhysicalMemoryByte)
					jdReqDevice.SysDiskSize = proto.String(mhReq.Device.HarddiskSizeByte)
					jdReqDevice.OsUpdateTime = proto.String(mhReq.Device.SystemUpdateSec)

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					jdReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []*jd_up.Device_Caid

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo jd_up.Device_Caid
							caidInfo.Id = proto.String(item.CAID)
							caidInfo.Version = proto.String(item.CAIDVersion)
							caidArray = append(caidArray, &caidInfo)
							break
						}
					}

					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						jdReqDevice.Caids = caidArray
					}
				} else {
					var caidArray []*jd_up.Device_Caid

					for _, item := range mhReq.Device.CAIDMulti {
						var caidInfo jd_up.Device_Caid
						caidInfo.Id = proto.String(item.CAID)
						caidInfo.Version = proto.String(item.CAIDVersion)
						caidArray = append(caidArray, &caidInfo)
					}

					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						jdReqDevice.Caids = caidArray
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

					jdReqDevice.Language = proto.String(mhReq.Device.Language)
					jdReqDevice.HwName = proto.String(mhReq.Device.DeviceNameMd5)
					jdReqDevice.HwMachine = proto.String(mhReq.Device.HardwareMachine)
					jdReqDevice.CountryCode = proto.String(mhReq.Device.Country)
					jdReqDevice.SysMemory = proto.String(mhReq.Device.PhysicalMemoryByte)
					jdReqDevice.SysDiskSize = proto.String(mhReq.Device.HarddiskSizeByte)
					jdReqDevice.OsUpdateTime = proto.String(mhReq.Device.SystemUpdateSec)

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			fmt.Println("get from jd error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug jd android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					jdReqDevice.OsVersion = proto.String(didRedisData.OsVersion)
					jdReqDevice.Model = proto.String(didRedisData.Model)
					jdReqDevice.Make = proto.String(didRedisData.Manufacturer)

					jdReqDevice.Imei = proto.String("")
					jdReqDevice.ImeiMd5 = proto.String("")
					jdReqDevice.Oaid = proto.String("")
					jdReqDevice.OaidMd5 = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							jdReqDevice.Imei = proto.String(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							jdReqDevice.ImeiMd5 = proto.String(strings.ToUpper(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							jdReqDevice.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						jdReqDevice.Ua = proto.String(didRedisData.Ua)

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug jd ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						jdReqDevice.OsVersion = proto.String(didRedisData.OsVersion)
						jdReqDevice.Model = proto.String(didRedisData.Model)
						jdReqDevice.Make = proto.String(didRedisData.Manufacturer)

						jdReqDevice.Language = proto.String("")
						jdReqDevice.HwName = proto.String("")
						jdReqDevice.HwMachine = proto.String("")
						jdReqDevice.CountryCode = proto.String("")
						jdReqDevice.SysMemory = proto.String("")
						jdReqDevice.SysDiskSize = proto.String("")
						jdReqDevice.OsUpdateTime = proto.String("")

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								jdReqDevice.Idfa = proto.String(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidArray []*jd_up.Device_Caid

								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidInfo jd_up.Device_Caid
										caidInfo.Id = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
										break
									}
								}
								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									jdReqDevice.Caids = caidArray
								}
							} else {
								var caidArray []*jd_up.Device_Caid

								for _, item := range tmpCAIDMulti {
									var caidInfo jd_up.Device_Caid
									caidInfo.Id = proto.String(item.CAID)
									caidInfo.Version = proto.String(item.CAIDVersion)
									caidArray = append(caidArray, &caidInfo)
								}

								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									jdReqDevice.Caids = caidArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							// tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							// tmpTimeZone = didRedisData.TimeZone

							if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

								jdReqDevice.Language = proto.String(tmpLanguage)
								jdReqDevice.HwName = proto.String(tmpDeviceNameMd5)
								jdReqDevice.HwMachine = proto.String(tmpHardwareMachine)
								jdReqDevice.CountryCode = proto.String(tmpCountry)
								jdReqDevice.SysMemory = proto.String(tmpPhysicalMemoryByte)
								jdReqDevice.SysDiskSize = proto.String(tmpHarddiskSizeByte)
								jdReqDevice.OsUpdateTime = proto.String(tmpSystemUpdateSec)

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									jdReqDevice.Idfa = proto.String(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*jd_up.Device_Caid

									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo jd_up.Device_Caid
											caidInfo.Id = proto.String(item.CAID)
											caidInfo.Version = proto.String(item.CAIDVersion)
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										jdReqDevice.Caids = caidArray
									}
								} else {
									var caidArray []*jd_up.Device_Caid

									for _, item := range tmpCAIDMulti {
										var caidInfo jd_up.Device_Caid
										caidInfo.Id = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
									}

									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										jdReqDevice.Caids = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								// tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

									jdReqDevice.Language = proto.String(tmpLanguage)
									jdReqDevice.HwName = proto.String(tmpDeviceNameMd5)
									jdReqDevice.HwMachine = proto.String(tmpHardwareMachine)
									jdReqDevice.CountryCode = proto.String(tmpCountry)
									jdReqDevice.SysMemory = proto.String(tmpPhysicalMemoryByte)
									jdReqDevice.SysDiskSize = proto.String(tmpHarddiskSizeByte)
									jdReqDevice.OsUpdateTime = proto.String(tmpSystemUpdateSec)

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									jdReqDevice.Idfa = proto.String(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									jdReqDevice.IdfaMd5 = proto.String(strings.ToUpper(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*jd_up.Device_Caid

									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo jd_up.Device_Caid
											caidInfo.Id = proto.String(item.CAID)
											caidInfo.Version = proto.String(item.CAIDVersion)
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										jdReqDevice.Caids = caidArray
									}
								} else {
									var caidArray []*jd_up.Device_Caid

									for _, item := range tmpCAIDMulti {
										var caidInfo jd_up.Device_Caid
										caidInfo.Id = proto.String(item.CAID)
										caidInfo.Version = proto.String(item.CAIDVersion)
										caidArray = append(caidArray, &caidInfo)
									}

									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										jdReqDevice.Caids = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								// tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 {

									jdReqDevice.Language = proto.String(tmpLanguage)
									jdReqDevice.HwName = proto.String(tmpDeviceNameMd5)
									jdReqDevice.HwMachine = proto.String(tmpHardwareMachine)
									jdReqDevice.CountryCode = proto.String(tmpCountry)
									jdReqDevice.SysMemory = proto.String(tmpPhysicalMemoryByte)
									jdReqDevice.SysDiskSize = proto.String(tmpHarddiskSizeByte)
									jdReqDevice.OsUpdateTime = proto.String(tmpSystemUpdateSec)

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							jdReqDevice.Ua = proto.String(didRedisData.Ua)

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from jd error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	// fmt.Println(localPosFloorPrice)
	// fmt.Println(localPosFinalPrice)
	// fmt.Println(platformPos.PlatformPosIsDebug)

	// ad count
	adCount := mhReq.Pos.AdCount
	if adCount > 5 {
		adCount = 5
	}

	// req imp
	jdReqImp := &jd_up.Impression{
		Id:       proto.String(bigdataUID),
		TagId:    proto.String(platformPos.PlatformPosID),
		AdsCount: proto.Int32(int32(adCount)),
	}

	// req pb
	jdReqPb := &jd_up.BidRequest{
		Id:      proto.String(bigdataUID),
		Version: proto.String("4.0"),
		App: &jd_up.App{
			Bundle: proto.String(GetAppBundleByConfig(c, mhReq, localPos, platformPos)),
			AppId:  proto.String(platformPos.PlatformAppID),
		},
		Device: jdReqDevice,
		// User: &jd_up.User{
		// 	JdAppInstalled: proto.Bool(true),
		// },
	}

	// if len(mhReq.Device.AppList) > 0 {
	// 	for _, appId := range mhReq.Device.AppList {
	// 		if appId == 1002 {
	// 			jdReqPb.User.JdAppInstalled = proto.Bool(true)
	// 			break
	// 		}
	// 	}
	// }

	jdReqPb.Impressions = append(jdReqPb.Impressions, jdReqImp)

	// tmpReqByte, _ := json.Marshal(jdReqPb)
	// fmt.Println("kbg_debug_jd url:", platformPos.PlatformAppUpURL)
	// fmt.Println("kbg_debug_jd req:", string(tmpReqByte))
	// fmt.Println("========================================================================================================================")

	// req gzip
	jdReqPbByte, _ := proto.Marshal(jdReqPb)
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(jdReqPbByte)
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
	}
	if err = gzipWriter.Close(); err != nil {
		fmt.Println(err)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json"
	httpHeader["Connection"] = "keep-alive"
	httpHeader["Accept-Encoding"] = "gzip,deflate,br"
	httpHeader["Content-Encoding"] = "gzip"
	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(buf.Bytes()))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		if statusCode == 204 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jdResp := &jd_up.BidResponse{}
	err = proto.Unmarshal(bodyContent, jdResp)
	if err != nil {
		fmt.Println(err)
	}

	// tmpRespByte, _ := json.Marshal(jdResp)
	// fmt.Println("kbg_debug_jd resp code:", resp.StatusCode)
	// fmt.Println("kbg_debug_jd resp:", string(tmpRespByte))
	// fmt.Println("========================================================================================================================")

	// debug
	if rand.Intn(1000000) == 0 {
		tmpRespByte, _ := json.Marshal(jdResp)
		go models.BigDataHoloDebugJson2(bigdataUID+"&jd", string(tmpRespByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}
	if jdResp.GetStatusCode() != 0 || len(jdResp.GetSeatBids()) == 0 {
		bigdataExtra.UpRespCode = int(jdResp.GetStatusCode())

		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1
	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, jdSeatBidItem := range jdResp.GetSeatBids() {

		for _, jdBidItem := range jdSeatBidItem.GetBids() {
			if len(jdBidItem.GetAdm().GetItems()) == 0 {
				respTmpInternalCode = 900103
				respTmpRespFailedNum = mhReq.Pos.AdCount
				continue
			}

			for range jdBidItem.GetAdm().GetItems() {
				respTmpRespAllNum = respTmpRespAllNum + 1
			}

			// ecpm
			jdRespPrice := int(jdBidItem.GetPrice())

			for range jdBidItem.GetAdm().GetItems() {
				respTmpPrice = respTmpPrice + int(jdRespPrice)
			}

			// if platformPos.PlatformAppID == "2237202690" && localPos.LocalPosID == "60128" {
			// 	go models.BigDataHoloDebugJson(bigdataUID, localPos, platformPos, utils.ConvertIntToString(jdRespPrice))
			// }
			// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
			if jdRespPrice <= 0 && platformPos.PlatformPosEcpmType == 1 {
				jdRespPrice = platformPos.PlatformPosEcpm
			}

			if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
				if localPosFinalPrice > int(jdRespPrice) {
					respTmpInternalCode = 900104
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1

					continue
				}
			}

			// 填充后限制
			isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
			if isAllInOneLimitAfterUpRespOK {
			} else {
				respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
				continue
			}

			for _, adInfoItem := range jdBidItem.GetAdm().GetItems() {

				respListItemMap := map[string]interface{}{}

				// adid
				maplehazeAdId := uuid.NewV4().String()
				respListItemMap["ad_id"] = maplehazeAdId

				// 下发ecpm
				// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
				tmpEcpm := 0
				if localPos.LocalPosEcpmType == 0 {
					// 不下发
					tmpEcpm = localPos.LocalPosEcpm
				} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
					tmpEcpm = int(float32(jdRespPrice) * (float32(100) - localPosProfitRate) / 100)
					if tmpEcpm < 1 {
						tmpEcpm = 0
					}
					respListItemMap["ecpm"] = tmpEcpm
				} else if localPos.LocalPosEcpmType == 2 {
					tmpEcpm = localPos.LocalPosEcpm
					respListItemMap["ecpm"] = localPos.LocalPosEcpm
				}

				// title
				if len(adInfoItem.GetTitle()) > 0 {
					respListItemMap["title"] = adInfoItem.GetTitle()
				}
				if len(adInfoItem.GetDesc()) > 0 {
					respListItemMap["description"] = adInfoItem.GetDesc()
				}

				// crid
				respListItemMap["crid"] = jdBidItem.GetAdId()

				// req_width, req_height
				respListItemMap["req_width"] = platformPos.PlatformPosWidth
				respListItemMap["req_height"] = platformPos.PlatformPosHeight

				// deep link
				respListItemMap["deep_link"] = adInfoItem.GetDeeplinkUrl()
				if mhReq.Device.Os == "ios" {
					respListItemMap["deep_link"] = adInfoItem.GetUniversalLinkUrl()
				}

				// deeplink track
				if len(adInfoItem.GetDeeplinkUrl()) > 0 || len(adInfoItem.GetUniversalLinkUrl()) > 0 {
					var respListItemDeepLinkArray []string

					mhDPParams := url.Values{}
					mhDPParams.Add("result", "0")
					mhDPParams.Add("reason", "")
					mhDPParams.Add("deeptype", "__DEEP_TYPE__")
					bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
					mhDPParams.Add("log", bigdataParams)

					respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

					var respListItemConvArray []map[string]interface{}

					respListItemConvMap := map[string]interface{}{}
					respListItemConvMap["conv_type"] = 10
					respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

					respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

					// deeplink failed track
					if platformPos.PlatformAppIsDeepLinkFailed == 1 {
						var respListItemDeepLinkFailedArray []string

						mhDPFailedParams := url.Values{}
						mhDPFailedParams.Add("result", "1")
						mhDPFailedParams.Add("reason", "3")
						mhDPFailedParams.Add("log", bigdataParams)

						respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

						respListItemConv11Map := map[string]interface{}{}
						respListItemConv11Map["conv_type"] = 11
						respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

						respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
					}

					respListItemMap["conv_tracks"] = respListItemConvArray
				}

				isVideo := false
				// video
				if adInfoItem.GetVideo() != nil {
					isVideo = true

					respListVideoItemMap := map[string]interface{}{}
					if adInfoItem.GetVideo().GetDuration() > 0 {
						// 过滤video_duration
						isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(adInfoItem.GetVideo().GetDuration()))
						if isMaterialFilter {
							respTmpInternalCode = filterErrCode
							respTmpRespFailedNum = respTmpRespFailedNum + 1

							continue
						}

						respListVideoItemMap["duration"] = int(adInfoItem.GetVideo().GetDuration())
					}
					respListVideoItemMap["width"] = int(adInfoItem.GetVideo().GetWidth())
					respListVideoItemMap["height"] = int(adInfoItem.GetVideo().GetHeight())
					respListVideoItemMap["video_url"] = adInfoItem.GetVideo().GetUrl()

					// 过滤素材方向, 大小
					isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.GetVideo().GetWidth()), int(adInfoItem.GetVideo().GetHeight()))
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						continue
					}

					// cover_url
					if len(adInfoItem.GetImgs()) > 0 {
						respListVideoItemMap["cover_url"] = adInfoItem.GetImgs()[0].GetUrl()
					}

					respListVideoItemMap["endcard_type"] = 1
					// if localPos.LocalPosType == 9 {
					// 	respListVideoItemMap["skip_min_time"] = 0
					// 	respListVideoItemMap["endcard_range"] = 0
					// }

					respListItemMap["video"] = respListVideoItemMap

					// crt_type
					respListItemMap["crt_type"] = 20
				} else {

					// imgs
					var respListItemImageArray []map[string]interface{}
					if len(adInfoItem.GetImgs()) > 0 {
						for _, imgItem := range adInfoItem.Imgs {
							respListImageItemMap := map[string]interface{}{}
							respListImageItemMap["url"] = imgItem.GetUrl()
							respListImageItemMap["width"] = int(imgItem.GetWidth())
							respListImageItemMap["height"] = int(imgItem.GetHeight())
							respListItemImageArray = append(respListItemImageArray, respListImageItemMap)
						}
					} else {
						continue
					}
					respListItemMap["imgs"] = respListItemImageArray

					// icon_url
					respListItemMap["icon_url"] = adInfoItem.GetIconUrl()

					// crt_type
					respListItemMap["crt_type"] = 11
				}

				// interact_type ad_url
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = adInfoItem.GetClickUrl()
				respListItemMap["landpage_url"] = adInfoItem.GetClickUrl()
				// impression_link array
				var respListItemImpArray []string
				// impression_link jd
				respListItemImpArray = append(respListItemImpArray, adInfoItem.GetExposalUrls()...)

				// impression_link maplehaze
				mhImpParams := url.Values{}
				bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, int(jdRespPrice), tmpEcpm)
				mhImpParams.Add("log", bigdataParams)

				respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
				respListItemMap["impression_link"] = respListItemImpArray

				// click_link array
				var respListItemClkArray []string
				// click_link jd
				for _, clickItem := range adInfoItem.GetClickMonitorUrls() {
					tmpItem := clickItem
					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							tmpItem = tmpItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}
					respListItemClkArray = append(respListItemClkArray, tmpItem)
				}

				// click_link maplehaze
				mhClkParams := url.Values{}
				mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
				mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
				mhClkParams.Add("down_x", "__DOWN_X__")
				mhClkParams.Add("down_y", "__DOWN_Y__")
				mhClkParams.Add("up_x", "__UP_X__")
				mhClkParams.Add("up_y", "__UP_Y__")
				mhClkParams.Add("mh_down_x", "__DOWN_X__")
				mhClkParams.Add("mh_down_y", "__DOWN_Y__")
				mhClkParams.Add("mh_up_x", "__UP_X__")
				mhClkParams.Add("mh_up_y", "__UP_Y__")
				mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
				mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
				mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
				mhClkParams.Add("turnx", "__TURN_X__")
				mhClkParams.Add("turny", "__TURN_Y__")
				mhClkParams.Add("turnz", "__TURN_Z__")
				mhClkParams.Add("turntime", "__TURN_TIME__")

				if platformPos.PlatformPosIsReportSLD == 1 {
					mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
				} else {
					mhClkParams.Add("sld", "__SLD__")
				}
				mhClkParams.Add("log", bigdataParams)

				respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
				respListItemMap["click_link"] = respListItemClkArray

				if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
					// win notice url
					mhWinNoticeURLParams := url.Values{}
					mhWinNoticeURLParams.Add("log", bigdataParams)
					mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
					mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
					mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
					respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

					// loss notice url
					mhLossNoticeURLParams := url.Values{}
					mhLossNoticeURLParams.Add("log", bigdataParams)
					mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
					mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
					mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
					respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
				}

				// direction
				respListItemMap["material_direction"] = platformPos.PlatformPosDirection

				// log
				respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

				if len(respListArray) >= mhReq.Pos.AdCount {
					continue
				}

				// 如果上游配置图片 or 下游配置图片, 过滤视频
				// 如果上游配置视频 or 下游配置视频, 过滤图片
				// 如果上游配置图片+视频, 不过滤
				// 最后返回请求的个数
				if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
					if isVideo {
						continue
					}
				}
				if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
					if isVideo {
					} else {
						continue
					}
				}
				if len(respListArray) >= mhReq.Pos.AdCount {
					continue
				}

				respListItemMap["p_ecpm"] = int(jdRespPrice)

				respListArray = append(respListArray, respListItemMap)
			}
		}
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("kbg_debug_jd maplehaze resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// jd resp
	respJd := models.MHUpResp{}
	respJd.RespData = &mhResp
	respJd.Extra = bigdataExtra
	// respJd.Extra.RespCount = len(respListArray)
	// respJd.Extra.ExternalCode = 0
	// respJd.Extra.InternalCode = 900000

	return &respJd
}
