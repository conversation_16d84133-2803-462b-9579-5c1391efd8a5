package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromBeiJingShiDai ...
func GetFromBeiJingShiDai(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from beijingshidai")

	// test
	// 测试地址: http://39.107.84.239/ads/bid
	// appid: 21087
	// tagid:
	// 开屏: 2108705
	// 视频: 2108706
	// 原生: 2108707
	// 加密价格测试实例:
	// key: 4ew8wo1tjagk9k6k
	// 加密后密文: cc25b4f7b511bb6471d56091b40be169
	// 解密后价格（单位: 分）: 500
	// platformPos.PlatformAppID = "21087"
	// platformPos.PlatformPosID = "2108707"
	// platformPos.PlatformAppBundle = "com.yidian"
	// fmt.Println("get from beijingshidai, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from beijingshidai, p_pos_id:", platformPos.PlatformPosID)
	// fmt.Println("get from beijingshidai, app_bundle:", platformPos.PlatformAppBundle)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["id"] = platformPos.PlatformAppID
	reqAppInfoMap["name"] = platformPos.PlatformAppName
	reqAppInfoMap["bundle"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	reqAppInfoMap["ver"] = platformPos.PlatformAppVersion

	// imp
	reqImpArray := []map[string]interface{}{}
	reqImpItemMap := map[string]interface{}{}
	reqImpItemMap["id"] = bigdataUID
	reqImpItemMap["tagid"] = platformPos.PlatformPosID
	reqImpItemMap["bidfloor"] = localPosFloorPrice
	reqImpItemMap["dplink"] = 1
	// 广告位类型: 1 – banner 2 – 开屏 3 – 插屏 4 – 原生 5 – 激励 视频
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">单Feed流</el-radio>
	// <el-radio label="12">双Feed流</el-radio>
	// <el-radio label="8">原生模版(待删除)</el-radio>
	// <el-radio label="7">原生2.0(待删除)</el-radio>
	// <el-radio label="5">原生视频(待删除)</el-radio>
	if platformPos.PlatformPosType == 1 {
		reqImpItemMap["type"] = 1
	} else if platformPos.PlatformPosType == 2 {
		reqImpItemMap["type"] = 2
	} else if platformPos.PlatformPosType == 3 {
		reqImpItemMap["type"] = 3
	} else if platformPos.PlatformPosType == 4 {
		reqImpItemMap["type"] = 4
	} else if platformPos.PlatformPosType == 11 {
		reqImpItemMap["type"] = 5
	}

	// reqImpItemMap["dealIds"] = [...]string{"123", "456"}
	reqImpArray = append(reqImpArray, reqImpItemMap)

	// device
	reqDeviceInfoMap := map[string]interface{}{}
	reqDeviceInfoMap["ua"] = destConfigUA
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true
				reqDeviceInfoMap["didmd5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true
				reqDeviceInfoMap["didmd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}

			// 小栓需要开android_id
			if len(mhReq.Device.AndroidID) > 0 {
				reqDeviceInfoMap["dpidmd5"] = utils.GetMd5(mhReq.Device.AndroidID)
			} else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
				reqDeviceInfoMap["dpidmd5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)
			}

			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.AndroidID) > 0 {
				reqDeviceInfoMap["dpidmd5"] = utils.GetMd5(mhReq.Device.AndroidID)
			} else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
				reqDeviceInfoMap["dpidmd5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)
			}

			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
			}

			if len(mhReq.Device.Oaid) > 0 {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
		reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = "ANDROID"
		reqDeviceInfoMap["model"] = mhReq.Device.Model
		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion

			if mhReq.Device.OsVersion == "5" {
				reqDeviceInfoMap["osl"] = 21
			} else if mhReq.Device.OsVersion == "5.1" {
				reqDeviceInfoMap["osl"] = 22
			} else if mhReq.Device.OsVersion == "6" {
				reqDeviceInfoMap["osl"] = 23
			} else if mhReq.Device.OsVersion == "7" {
				reqDeviceInfoMap["osl"] = 24
			} else if mhReq.Device.OsVersion == "7.1" {
				reqDeviceInfoMap["osl"] = 25
			} else if mhReq.Device.OsVersion == "8" {
				reqDeviceInfoMap["osl"] = 26
			} else if mhReq.Device.OsVersion == "8.1" {
				reqDeviceInfoMap["osl"] = 27
			} else if mhReq.Device.OsVersion == "9" {
				reqDeviceInfoMap["osl"] = 28
			} else if mhReq.Device.OsVersion == "10" {
				reqDeviceInfoMap["osl"] = 29
			} else if mhReq.Device.OsVersion == "11" {
				reqDeviceInfoMap["osl"] = 30
			} else if mhReq.Device.OsVersion == "12" {
				reqDeviceInfoMap["osl"] = 31
			}
		}
	} else if mhReq.Device.Os == "ios" {
		// fmt.Println("get from beijingshidai ios")
		reqDeviceInfoMap["make"] = "Apple"
		reqDeviceInfoMap["os"] = "IOS"
		reqDeviceInfoMap["model"] = mhReq.Device.Model

		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
		}

		if len(mhReq.Device.Idfa) > 0 {
			reqDeviceInfoMap["idfamd5"] = utils.GetMd5(mhReq.Device.Idfa)
		} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
			reqDeviceInfoMap["idfamd5"] = strings.ToLower(mhReq.Device.IdfaMd5)
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}
	reqDeviceInfoMap["w"] = mhReq.Device.ScreenWidth
	reqDeviceInfoMap["h"] = mhReq.Device.ScreenHeight

	if mhReq.Network.ConnectType == 0 {
		reqDeviceInfoMap["connectiontype"] = 2
	} else if mhReq.Network.ConnectType == 1 {
		reqDeviceInfoMap["connectiontype"] = 2
	} else if mhReq.Network.ConnectType == 2 {
		reqDeviceInfoMap["connectiontype"] = 4
	} else if mhReq.Network.ConnectType == 3 {
		reqDeviceInfoMap["connectiontype"] = 5
	} else if mhReq.Network.ConnectType == 4 {
		reqDeviceInfoMap["connectiontype"] = 6
	} else if mhReq.Network.ConnectType == 7 {
		reqDeviceInfoMap["connectiontype"] = 7
	} else {
		reqDeviceInfoMap["connectiontype"] = 2
	}

	if mhReq.Network.Carrier == 0 {
		reqDeviceInfoMap["carrier"] = 46000
	} else if mhReq.Network.Carrier == 1 {
		reqDeviceInfoMap["carrier"] = 46000
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceInfoMap["carrier"] = 46001
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceInfoMap["carrier"] = 46003
	} else {
		reqDeviceInfoMap["carrier"] = 46000
	}

	reqDeviceInfoMap["devicetype"] = 1

	if len(mhReq.Device.BootMark) > 0 {
		reqDeviceInfoMap["startup_time"] = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		reqDeviceInfoMap["mb_time"] = mhReq.Device.UpdateMark
	}

	if len(mhReq.Device.AppStoreVersion) > 0 {
		reqDeviceInfoMap["appstore_ver"] = mhReq.Device.AppStoreVersion
	}

	// if len(mhReq.Device.Mac) > 0 {
	// 	reqUserInfoMap["mac"] = mhReq.Device.Mac
	// }

	// 替换包
	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		var bigdataReplaceDIDStu models.ReplaceDIDStu
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, false, bigdataReplaceDIDStu)
	}()

	postData := map[string]interface{}{
		"id":      bigdataUID,
		"imp":     reqImpArray,
		"app":     reqAppInfoMap,
		"device":  reqDeviceInfoMap,
		"version": "1.0.0",
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("beijingshidai req: " + string(jsonData))

	// return MhUpErrorRespMap("", bigdataExtra)
	beijingshidaiUPURL := platformPos.PlatformAppUpURL
	// if platformPos.PlatformPosIsDebug == 1 {
	// 	beijingshidaiUPURL = platformPos.PlatformAppUpDebugURL
	// }
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", beijingshidaiUPURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("beijingshidai resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	beijingshidaiRespStu := BeiJingShiDaiRespStu{}
	json.Unmarshal([]byte(bodyContent), &beijingshidaiRespStu)

	if len(beijingshidaiRespStu.SeatBids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(beijingshidaiRespStu.SeatBids[0].Bids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}

	beijingshidaiRespItemList := beijingshidaiRespStu.SeatBids[0].Bids

	if len(beijingshidaiRespItemList) > 1 {
		sort.Sort(BeiJingShiDaiEcpmSort(beijingshidaiRespItemList))
	}

	for _, beijingshidaiInfoItem := range beijingshidaiRespItemList {
		adInfoItem := beijingshidaiInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		beijingshidaiEcpm := adInfoItem.Price

		respTmpPrice = respTmpPrice + beijingshidaiEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if beijingshidaiEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			beijingshidaiEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > beijingshidaiEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(beijingshidaiEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceHexValue := "__AUCTION_PRICE__"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("beijingshidai price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			macroPrice := utils.ConvertIntToString(int(beijingshidaiEcpm * randPRValue / 100))
			beijingshidaikey := platformPos.PlatformAppPriceEncrypt
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, beijingshidaikey)
			encodePriceHexValue = string(utils.HexEncode(string(encodePrice)))
		}

		currentMillSecond := utils.GetCurrentMilliSecond()
		currentSecond := utils.GetCurrentSecond()

		// win notice url
		winNoticeURL := adInfoItem.NURL

		if len(winNoticeURL) > 0 {
			winNoticeURL = strings.Replace(winNoticeURL, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_ID__", bigdataUID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_PRICE__", encodePriceHexValue, -1)

			go func() {
				defer func() {
					if err := recover(); err != nil {
						fmt.Println("beijingshidai nurl panic:", err)
					}
				}()
				curlBeiJingShiDaiNurl(winNoticeURL)
			}()
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		// crid
		respListItemMap["crid"] = adInfoItem.CRID

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.Deeplink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.Deeplink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, dpTrackInfoItem := range adInfoItem.Tracking {
				if dpTrackInfoItem.TrackEvent == "DP_SUCC" {
					for _, dpTrackURLItem := range dpTrackInfoItem.TrackUrls {
						trackItem := dpTrackURLItem
						trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
						trackItem = strings.Replace(trackItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
						trackItem = strings.Replace(trackItem, "__AUCTION_ID__", bigdataUID, -1)
						trackItem = strings.Replace(trackItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
						trackItem = strings.Replace(trackItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
						trackItem = strings.Replace(trackItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, trackItem)
					}
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, dpTrackInfoItem := range adInfoItem.Tracking {
					if dpTrackInfoItem.TrackEvent == "DP_FAIL" {
						for _, dpTrackURLItem := range dpTrackInfoItem.TrackUrls {
							trackItem := dpTrackURLItem
							trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
							trackItem = strings.Replace(trackItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_ID__", bigdataUID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
							respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, trackItem)
						}
					}
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.Icon.ImageURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Icon.ImageURL
		} else if len(adInfoItem.App.AppIcon.ImageURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.App.AppIcon.ImageURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Deeplink)
		}

		isVideo := false

		// 视频
		if len(adInfoItem.Video.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Video.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Video.VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
				respListVideoItemMap["duration"] = adInfoItem.Video.VideoDuration * 1000
			}
			if adInfoItem.Video.VideoWidth > 0 {
				respListVideoItemMap["width"] = adInfoItem.Video.VideoWidth
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Video.VideoHeight > 0 {
				respListVideoItemMap["height"] = adInfoItem.Video.VideoHeight
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}
			if adInfoItem.Video.VideoWidth > 0 && adInfoItem.Video.VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Video.VideoWidth, adInfoItem.Video.VideoHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			respListVideoItemMap["video_url"] = adInfoItem.Video.VideoURL

			// cover_url
			if len(adInfoItem.ImageURLs) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.ImageURLs[0].ImageURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			if platformPos.PlatformPosType == 11 {
				var respListEventTrackURLMap []map[string]interface{}

				respListVideoBeginEventTrackMap := map[string]interface{}{}
				respListVideoBeginEventTrackMap["event_type"] = 100
				var respListVideoBeginEventTrackURLMap []string
				for _, videoStartTrackInfoItem := range adInfoItem.Tracking {
					if videoStartTrackInfoItem.TrackEvent == "VIDEO_START" {
						for _, videoStartTrackURLItem := range videoStartTrackInfoItem.TrackUrls {
							trackItem := videoStartTrackURLItem
							trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
							trackItem = strings.Replace(trackItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_ID__", bigdataUID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
							respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, trackItem)
						}
					}
				}
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				if len(respListVideoBeginEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
				}

				respListVideoEndEventTrackMap := map[string]interface{}{}
				respListVideoEndEventTrackMap["event_type"] = 103
				var respListVideoEndEventTrackURLMap []string
				for _, videoEndTrackInfoItem := range adInfoItem.Tracking {
					if videoEndTrackInfoItem.TrackEvent == "VIDEO_END" {
						for _, videoEndTrackURLItem := range videoEndTrackInfoItem.TrackUrls {
							trackItem := videoEndTrackURLItem
							trackItem = strings.Replace(trackItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
							trackItem = strings.Replace(trackItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_ID__", bigdataUID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
							trackItem = strings.Replace(trackItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
							respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, trackItem)
						}
					}
				}
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				if len(respListVideoEndEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
				}

				if len(respListEventTrackURLMap) > 0 {
					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.ImageURLs) > 0 {
				respListImageItemMap["url"] = adInfoItem.ImageURLs[0].ImageURL
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if adInfoItem.ImageURLs[0].ImageWidth > 0 {
				respListImageItemMap["width"] = adInfoItem.ImageURLs[0].ImageWidth
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.ImageURLs[0].ImageHeight > 0 {
				respListImageItemMap["height"] = adInfoItem.ImageURLs[0].ImageHeight
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.ImageURLs[0].ImageWidth > 0 && adInfoItem.ImageURLs[0].ImageHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.ImageURLs[0].ImageWidth, adInfoItem.ImageURLs[0].ImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoItem.IsDown == 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.ClickURL
			respListItemMap["landpage_url"] = adInfoItem.ClickURL
			if len(adInfoItem.ClickURL) == 0 {
				continue
			}
		} else if adInfoItem.IsDown == 1 {
			if len(adInfoItem.ClickURL) == 0 {
				continue
			}

			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.ClickURL
			respListItemMap["download_url"] = adInfoItem.ClickURL

		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.App.AppBundle) > 0 {
			respListItemMap["package_name"] = adInfoItem.App.AppBundle
		}

		if len(adInfoItem.App.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.App.AppName
		}
		// if len(adInfoContentItem.MetaData.ApkInfo.DeveloperName) > 0 {
		// 	respListItemMap["publisher"] = adInfoContentItem.MetaData.ApkInfo.DeveloperName
		// }
		// if len(adInfoContentItem.MetaData.ApkInfo.VersionName) > 0 {
		// 	respListItemMap["app_version"] = adInfoContentItem.MetaData.ApkInfo.VersionName
		// }
		// if len(adInfoContentItem.MetaData.PrivacyUrl) > 0 {
		// 	respListItemMap["privacy_url"] = adInfoContentItem.MetaData.PrivacyUrl
		// }
		// if len(adInfoContentItem.MetaData.ApkInfo.Permissions) > 0 {
		// 	permissionStr := ""
		// 	for _, huaweiPermissionItem := range adInfoContentItem.MetaData.ApkInfo.Permissions {
		// 		tmpPermission, _ := url.QueryUnescape(huaweiPermissionItem.PermissionLabel)
		// 		permissionStr = permissionStr + tmpPermission + "\n"
		// 	}
		// 	respListItemMap["permission"] = permissionStr
		// }
		// if adInfoItem.ApkSize > 0 {
		// 	respListItemMap["package_size"] = adInfoItem.ApkSize
		// }

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, beijingshidaiEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.Tracking {
			if trackInfoItem.TrackEvent == "IMP" {
				for _, trackURLItem := range trackInfoItem.TrackUrls {
					impItem := trackURLItem
					impItem = strings.Replace(impItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
					impItem = strings.Replace(impItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
					impItem = strings.Replace(impItem, "__AUCTION_ID__", bigdataUID, -1)
					impItem = strings.Replace(impItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
					impItem = strings.Replace(impItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
					impItem = strings.Replace(impItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
					respListItemImpArray = append(respListItemImpArray, impItem)
				}
			}
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.Tracking {
			if trackInfoItem.TrackEvent == "CLICK" {
				for _, trackURLItem := range trackInfoItem.TrackUrls {
					clkItem := trackURLItem
					clkItem = strings.Replace(clkItem, "__TS__", utils.ConvertInt64ToString(currentMillSecond), -1)
					clkItem = strings.Replace(clkItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
					clkItem = strings.Replace(clkItem, "__AUCTION_ID__", bigdataUID, -1)
					clkItem = strings.Replace(clkItem, "__AUCTION_BID_ID__", beijingshidaiRespStu.BIDID, -1)
					clkItem = strings.Replace(clkItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
					clkItem = strings.Replace(clkItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)

					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							clkItem = clkItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							clkItem = clkItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}

					respListItemClkArray = append(respListItemClkArray, clkItem)
				}
			}
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = beijingshidaiEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// beijingshidai resp
	respBeiJingShiDai := models.MHUpResp{}
	respBeiJingShiDai.RespData = &mhResp
	respBeiJingShiDai.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respBeiJingShiDai
}

func curlBeiJingShiDaiNurl(nurl string) {
	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println(requestGet.URL.String())
	// fmt.Println("gdt nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return
	}

	defer resp.Body.Close()
}

type BeiJingShiDaiEcpmSort []BeiJingShiDaiRespSeatBidItemStu

func (s BeiJingShiDaiEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s BeiJingShiDaiEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s BeiJingShiDaiEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Price > s[j].Price
}

// BeiJingShiDaiRespStu ...
type BeiJingShiDaiRespStu struct {
	ID       string                         `json:"id"`
	BIDID    string                         `json:"bidid"`
	SeatBids []BeiJingShiDaiRespSeatBidsStu `json:"seatbid"`
	NBR      int                            `json:"nbr"`
}

type BeiJingShiDaiRespSeatBidsStu struct {
	Bids []BeiJingShiDaiRespSeatBidItemStu `json:"bid"`
}

type BeiJingShiDaiRespSeatBidItemStu struct {
	ID          string                                 `json:"id"`
	ImpID       string                                 `json:"impid"`
	Price       int                                    `json:"price"`
	AdID        string                                 `json:"adid"`
	NURL        string                                 `json:"nurl"`
	AdoMains    []string                               `json:"adomain"`
	CRID        string                                 `json:"crid"`
	DealID      string                                 `json:"dealid"`
	Title       string                                 `json:"title"`
	Description string                                 `json:"desc"`
	Logo        BeiJingShiDaiRespSeatBidItemLogoStu    `json:"logo"`
	Icon        BeiJingShiDaiRespSeatBidItemImageStu   `json:"icon"`
	ImageURLs   []BeiJingShiDaiRespSeatBidItemImageStu `json:"images"`
	Video       BeiJingShiDaiRespSeatBidItemVideoStu   `json:"video"`
	App         BeiJingShiDaiRespSeatBidItemAppStu     `json:"app"`
	ClickURL    string                                 `json:"clickurl"`
	Deeplink    string                                 `json:"deeplink"`
	IsDown      int                                    `json:"isdown"`
	Macro       int                                    `json:"macro"`
	IsDeep      int                                    `json:"isdeep"`
	Tracking    []BeiJingShiDaiRespSeatBidItemTrackStu `json:"tracking"`
}

type BeiJingShiDaiRespSeatBidItemAppStu struct {
	AppName   string                               `json:"name"`
	AppBundle string                               `json:"bundle"`
	AppIcon   BeiJingShiDaiRespSeatBidItemImageStu `json:"icon"`
	AppSize   int                                  `json:"size"` // 单位字节
}

type BeiJingShiDaiRespSeatBidItemVideoStu struct {
	VideoWidth    int    `json:"w"`
	VideoHeight   int    `json:"h"`
	VideoURL      string `json:"url"`
	VideoDuration int    `json:"duration"`
	VideoSize     int    `json:"size"` // kb
	VideoType     string `json:"type"`
}

type BeiJingShiDaiRespSeatBidItemImageStu struct {
	ImageWidth  int    `json:"w"`
	ImageHeight int    `json:"h"`
	ImageURL    string `json:"url"`
	ImageType   string `json:"type"`
	ImageSize   int    `json:"size"` // kb
}

type BeiJingShiDaiRespSeatBidItemLogoStu struct {
	LogoURL string `json:"url"`
	Source  string `json:"source"`
}

type BeiJingShiDaiRespSeatBidItemTrackStu struct {
	TrackEvent string   `json:"event"`
	TrackUrls  []string `json:"urls"`
}
