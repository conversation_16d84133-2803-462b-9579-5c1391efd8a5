package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/zuiyou_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromZuiYou ...
func GetFromZuiYou(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from zuiyou")

	// test
	// platformPos.PlatformAppID = "test_app"
	// platformPos.PlatformPosID = "test_tag_id_back"
	// platformPos.PlatformAppName = "测试"
	// platformPos.PlatformAppBundle = "com.test"
	// platformPos.PlatformAppVersion = "0.1"
	// fmt.Println("get from zuiyou, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from zuiyou, p_pos_id:", platformPos.PlatformPosID)
	// fmt.Println("get from zuiyou, app_bundle:", platformPos.PlatformAppBundle)
	// fmt.Println("get from zuiyou, p_pos_property:", platformPos.PlatformPosProperty)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	zuiyouReq := &zuiyou_up.VulcanRequest{
		ApiVersion: "1.9.2",
		RequestId:  bigdataUID,
		Timeout:    uint32(timeout),
		App: &zuiyou_up.VulcanRequest_App{
			Appid:       platformPos.PlatformAppID,
			Name:        platformPos.PlatformAppName,
			PackageName: GetAppBundleByConfig(c, mhReq, localPos, platformPos),
			Version:     platformPos.PlatformAppVersion,
		},
	}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	zuiyouReqDevice := &zuiyou_up.VulcanRequest_Device{}
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				zuiyouReqDevice.ImeiMd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				zuiyouReqDevice.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}

		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				zuiyouReqDevice.Oaid = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				zuiyouReqDevice.Idfa = mhReq.Device.Idfa
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				zuiyouReqDevice.IdfaMd5 = mhReq.Device.IdfaMd5
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {

				var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
							KenyId:  item.CAID,
							Version: item.CAIDVersion,
						}
						caidList = append(caidList, &caidItem)

						break
					}
				}
				zuiyouReqDevice.CaidList = caidList
			} else {
				var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
				for _, item := range mhReq.Device.CAIDMulti {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
						KenyId:  item.CAID,
						Version: item.CAIDVersion,
					}
					caidList = append(caidList, &caidItem)
				}
				zuiyouReqDevice.CaidList = caidList
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				zuiyouReqDevice.StartupTime = tmpDeviceStartSec
				zuiyouReqDevice.CountryCode = tmpCountry
				zuiyouReqDevice.Language = tmpLanguage
				zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
				zuiyouReqDevice.OrgModel = tmpHardwareMachine
				zuiyouReqDevice.DeviceModel = tmpHardwareModel
				zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
				zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
				zuiyouReqDevice.MbTime = tmpSystemUpdateSec
				zuiyouReqDevice.LocalTzTime = tmpTimeZone
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					zuiyouReqDevice.Idfa = mhReq.Device.Idfa
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					zuiyouReqDevice.IdfaMd5 = mhReq.Device.IdfaMd5
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
								KenyId:  item.CAID,
								Version: item.CAIDVersion,
							}
							caidList = append(caidList, &caidItem)
							break
						}
					}
					zuiyouReqDevice.CaidList = caidList
				} else {
					var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
							KenyId:  item.CAID,
							Version: item.CAIDVersion,
						}
						caidList = append(caidList, &caidItem)
					}
					zuiyouReqDevice.CaidList = caidList
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					zuiyouReqDevice.StartupTime = tmpDeviceStartSec
					zuiyouReqDevice.CountryCode = tmpCountry
					zuiyouReqDevice.Language = tmpLanguage
					zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
					zuiyouReqDevice.OrgModel = tmpHardwareMachine
					zuiyouReqDevice.DeviceModel = tmpHardwareModel
					zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
					zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
					zuiyouReqDevice.MbTime = tmpSystemUpdateSec
					zuiyouReqDevice.LocalTzTime = tmpTimeZone
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					zuiyouReqDevice.Idfa = mhReq.Device.Idfa
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					zuiyouReqDevice.IdfaMd5 = mhReq.Device.IdfaMd5
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
								KenyId:  item.CAID,
								Version: item.CAIDVersion,
							}
							caidList = append(caidList, &caidItem)

							break
						}
					}
					zuiyouReqDevice.CaidList = caidList
				} else {
					var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
							KenyId:  item.CAID,
							Version: item.CAIDVersion,
						}
						caidList = append(caidList, &caidItem)
					}
					zuiyouReqDevice.CaidList = caidList
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					zuiyouReqDevice.StartupTime = tmpDeviceStartSec
					zuiyouReqDevice.CountryCode = tmpCountry
					zuiyouReqDevice.Language = tmpLanguage
					zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
					zuiyouReqDevice.OrgModel = tmpHardwareMachine
					zuiyouReqDevice.DeviceModel = tmpHardwareModel
					zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
					zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
					zuiyouReqDevice.MbTime = tmpSystemUpdateSec
					zuiyouReqDevice.LocalTzTime = tmpTimeZone
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if mhReq.Device.Os == "android" {
		zuiyouReqDevice.OsType = zuiyou_up.VulcanRequest_Device_ANDROID

		zuiyouReqDevice.Model = mhReq.Device.Model
		zuiyouReqDevice.Make = mhReq.Device.Manufacturer

		// 新增设备厂商信息
		if len(mhReq.Device.Manufacturer) > 0 {
			tmpManufacturer := GetManufactureType(c, mhReq)
			switch tmpManufacturer {
			case "vivo":
				if len(mhReq.Device.AppStoreVersion) > 0 {
					zuiyouReqDeviceInfo := &zuiyou_up.VulcanRequest_Device_VivoDevInfo{
						VivoAppstoreVersion: mhReq.Device.AppStoreVersion,
					}
					zuiyouReqDevice.VivoInfo = zuiyouReqDeviceInfo
				}
			case "oppo":
				if len(mhReq.Device.AppStoreVersion) > 0 {
					zuiyouReqDeviceInfo := &zuiyou_up.VulcanRequest_Device_OppoDevInfo{
						OppoAppstoreVersion: mhReq.Device.AppStoreVersion,
					}
					zuiyouReqDevice.OppoInfo = zuiyouReqDeviceInfo
				}
			case "huawei":
				zuiyouReqDeviceInfo := &zuiyou_up.VulcanRequest_Device_HWDevInfo{}
				if len(mhReq.Device.AppStoreVersion) > 0 {
					zuiyouReqDeviceInfo.AgVersion = mhReq.Device.AppStoreVersion
				}
				if len(mhReq.Device.HMSCoreVersion) > 0 {
					zuiyouReqDeviceInfo.HmsVersion = mhReq.Device.HMSCoreVersion
				}
				zuiyouReqDevice.HwInfo = zuiyouReqDeviceInfo
			}
		}
	} else if mhReq.Device.Os == "ios" {
		zuiyouReqDevice.OsType = zuiyou_up.VulcanRequest_Device_IOS

		zuiyouReqDevice.Model = mhReq.Device.Model
		zuiyouReqDevice.Make = "Apple"
	}
	zuiyouReqDevice.DeviceType = zuiyou_up.VulcanRequest_Device_PHONE
	zuiyouReqDevice.OsVersion = mhReq.Device.OsVersion
	zuiyouReqDevice.Ua = destConfigUA
	zuiyouReqDevice.Ip = mhReq.Device.IP
	zuiyouReqDevice.ScreenWidth = uint32(mhReq.Device.ScreenWidth)
	zuiyouReqDevice.ScreenHeight = uint32(mhReq.Device.ScreenHeight)
	// if len(mhReq.Device.Mac) > 0 {
	// 	zuiyouReqDevice.Mac = mhReq.Device.Mac
	// }

	if mhReq.Network.ConnectType == 0 {
		// 未知
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_CONN_UNKNOWN
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_WIFI
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_MOBILE_2G
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_MOBILE_3G
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_MOBILE_4G
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		zuiyouReqDevice.ConnType = zuiyou_up.VulcanRequest_Device_MOBILE_5G
	}
	if mhReq.Network.Carrier == 1 {
		zuiyouReqDevice.Carrier = zuiyou_up.VulcanRequest_Device_MOBILE
	} else if mhReq.Network.Carrier == 2 {
		zuiyouReqDevice.Carrier = zuiyou_up.VulcanRequest_Device_UNICOM
	} else if mhReq.Network.Carrier == 3 {
		zuiyouReqDevice.Carrier = zuiyou_up.VulcanRequest_Device_TELECOM
	} else {
		zuiyouReqDevice.Carrier = zuiyou_up.VulcanRequest_Device_CARRIER_UNKNOWN
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					zuiyouReqDevice.ImeiMd5 = ""
					zuiyouReqDevice.Oaid = ""

					zuiyouReqDevice.OsVersion = didRedisData.OsVersion
					zuiyouReqDevice.Model = didRedisData.Model
					zuiyouReqDevice.Make = didRedisData.Manufacturer
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						zuiyouReqDevice.Ua = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							zuiyouReqDevice.ImeiMd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							zuiyouReqDevice.ImeiMd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							zuiyouReqDevice.Oaid = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}

				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						zuiyouReqDevice.OsVersion = didRedisData.OsVersion
						zuiyouReqDevice.Model = didRedisData.Model
						zuiyouReqDevice.Idfa = ""
						zuiyouReqDevice.IdfaMd5 = ""
						zuiyouReqDevice.Caid = ""
						zuiyouReqDevice.CaidVersion = ""
						zuiyouReqDevice.StartupTime = ""
						zuiyouReqDevice.CountryCode = ""
						zuiyouReqDevice.Language = ""
						zuiyouReqDevice.PhoneName = ""
						zuiyouReqDevice.OrgModel = ""
						zuiyouReqDevice.DeviceModel = ""
						zuiyouReqDevice.MemTotal = 0
						zuiyouReqDevice.DiskTotal = 0
						zuiyouReqDevice.MbTime = ""
						zuiyouReqDevice.LocalTzTime = ""

						// reqDeviceInfoMap["make"] = "Apple"
						if platformPos.PlatformAppIsReplaceDIDUa == 1 {
							zuiyouReqDevice.Ua = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								zuiyouReqDevice.Idfa = didRedisData.Idfa

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								zuiyouReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
											KenyId:  item.CAID,
											Version: item.CAIDVersion,
										}
										caidList = append(caidList, &caidItem)
										break
									}
								}
								zuiyouReqDevice.CaidList = caidList
							} else {
								var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
										KenyId:  item.CAID,
										Version: item.CAIDVersion,
									}
									caidList = append(caidList, &caidItem)
								}
								zuiyouReqDevice.CaidList = caidList
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								zuiyouReqDevice.StartupTime = tmpDeviceStartSec
								zuiyouReqDevice.CountryCode = tmpCountry
								zuiyouReqDevice.Language = tmpLanguage
								zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
								zuiyouReqDevice.OrgModel = tmpHardwareMachine
								zuiyouReqDevice.DeviceModel = tmpHardwareModel
								zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
								zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
								zuiyouReqDevice.MbTime = tmpSystemUpdateSec
								zuiyouReqDevice.LocalTzTime = tmpTimeZone
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									zuiyouReqDevice.Idfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									zuiyouReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
												KenyId:  item.CAID,
												Version: item.CAIDVersion,
											}
											caidList = append(caidList, &caidItem)
											break
										}
									}
									zuiyouReqDevice.CaidList = caidList
								} else {
									var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
											KenyId:  item.CAID,
											Version: item.CAIDVersion,
										}
										caidList = append(caidList, &caidItem)
									}
									zuiyouReqDevice.CaidList = caidList
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									zuiyouReqDevice.StartupTime = tmpDeviceStartSec
									zuiyouReqDevice.CountryCode = tmpCountry
									zuiyouReqDevice.Language = tmpLanguage
									zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
									zuiyouReqDevice.OrgModel = tmpHardwareMachine
									zuiyouReqDevice.DeviceModel = tmpHardwareModel
									zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
									zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
									zuiyouReqDevice.MbTime = tmpSystemUpdateSec
									zuiyouReqDevice.LocalTzTime = tmpTimeZone
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									zuiyouReqDevice.Idfa = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									zuiyouReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
												KenyId:  item.CAID,
												Version: item.CAIDVersion,
											}
											caidList = append(caidList, &caidItem)
											break
										}
									}
									zuiyouReqDevice.CaidList = caidList
								} else {
									var caidList []*zuiyou_up.VulcanRequest_Device_CaidInfo
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										caidItem := zuiyou_up.VulcanRequest_Device_CaidInfo{
											KenyId:  item.CAID,
											Version: item.CAIDVersion,
										}
										caidList = append(caidList, &caidItem)
									}
									zuiyouReqDevice.CaidList = caidList
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									zuiyouReqDevice.StartupTime = tmpDeviceStartSec
									zuiyouReqDevice.CountryCode = tmpCountry
									zuiyouReqDevice.Language = tmpLanguage
									zuiyouReqDevice.PhoneName = tmpDeviceNameMd5
									zuiyouReqDevice.OrgModel = tmpHardwareMachine
									zuiyouReqDevice.DeviceModel = tmpHardwareModel
									zuiyouReqDevice.MemTotal = uint64(utils.ConvertStringToInt(tmpPhysicalMemoryByte))
									zuiyouReqDevice.DiskTotal = uint64(utils.ConvertStringToInt(tmpHarddiskSizeByte))
									zuiyouReqDevice.MbTime = tmpSystemUpdateSec
									zuiyouReqDevice.LocalTzTime = tmpTimeZone
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from zuiyou error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from zuiyou error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	zuiyouReq.Device = zuiyouReqDevice

	// ad slot
	zuiyouReqAdSlot := &zuiyou_up.VulcanRequest_Adslot{}
	if platformPos.PlatformPosType == 2 {
		zuiyouReqAdSlot.AdType = zuiyou_up.VulcanRequest_Adslot_SPLASH
	} else {
		zuiyouReqAdSlot.AdType = zuiyou_up.VulcanRequest_Adslot_NATIVE
	}
	zuiyouReqAdSlot.MaterialTypes = append(zuiyouReqAdSlot.MaterialTypes, zuiyou_up.VulcanRequest_Adslot_ONE_IMG)
	zuiyouReqAdSlot.MaterialTypes = append(zuiyouReqAdSlot.MaterialTypes, zuiyou_up.VulcanRequest_Adslot_VIDEO)
	zuiyouReqAdSlot.InteractionTypes = append(zuiyouReqAdSlot.InteractionTypes, zuiyou_up.VulcanRequest_Adslot_LANDING_PAGE)
	zuiyouReqAdSlot.InteractionTypes = append(zuiyouReqAdSlot.InteractionTypes, zuiyou_up.VulcanRequest_Adslot_INVOKE)
	zuiyouReqAdSlot.InteractionTypes = append(zuiyouReqAdSlot.InteractionTypes, zuiyou_up.VulcanRequest_Adslot_DOWNLOAD)
	zuiyouReqAdSlot.InteractionTypes = append(zuiyouReqAdSlot.InteractionTypes, zuiyou_up.VulcanRequest_Adslot_BROWSER)
	zuiyouReqAdSlot.Price = uint32(localPosFloorPrice)
	zuiyouReqAdSlot.TagId = platformPos.PlatformPosID
	zuiyouReqAdSlot.Cnt = uint32(mhReq.Pos.AdCount)

	zuiyouReq.Adslot = zuiyouReqAdSlot

	// log
	// zuiyouReqTmpByte, _ := json.Marshal(zuiyouReq)
	// zuiyouReqTmpString := string(zuiyouReqTmpByte)
	// fmt.Println("zuiyou req: ", zuiyouReqTmpString)

	////////////////////////////////////////////////////////////////////////////////////////
	zuiyouReqPbByte, _ := proto.Marshal(zuiyouReq)

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "GET", platformPos.PlatformAppUpURL, bytes.NewReader(zuiyouReqPbByte))

	requestGet.Header.Add("Content-Type", "application/octet-stream")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("zuiyou resp body: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	zuiyouResp := &zuiyou_up.VulcanResponse{}
	err = proto.Unmarshal(bodyContent, zuiyouResp)
	if err != nil {
		fmt.Println(err)
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// log
	// zuiyouRespTmpByte, _ := json.Marshal(zuiyouResp)
	// zuiyouRespTmpString := string(zuiyouRespTmpByte)
	// fmt.Println("zuiyou resp: ", zuiyouRespTmpString)

	if zuiyouResp.StatusCode != zuiyou_up.VulcanResponse_REQ_OK {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(zuiyouResp.Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}

	zuiyouRespItemList := zuiyouResp.Ads

	if len(zuiyouRespItemList) > 1 {
		sort.Sort(ZuiYouEcpmSort(zuiyouRespItemList))
	}

	for _, zuiyouInfoItem := range zuiyouRespItemList {
		adInfoItem := zuiyouInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		zuiyouEcpm := int(adInfoItem.Price)

		respTmpPrice = respTmpPrice + zuiyouEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if zuiyouEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			zuiyouEcpm = platformPos.PlatformPosEcpm
		}

		zuiyouLossNoticeURL := getZuiYouPriceFailedURL(adInfoItem.LossUrl, platformPos, zuiyouEcpm)

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > zuiyouEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(zuiyouEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceValue := "__BID_PRICE__"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {
			fmt.Println("zuiyou price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 100
			if platformPos.PlatformAppReportWinType == 0 {
				randPRValue = 100
			} else if platformPos.PlatformAppReportWinType == 1 {
				tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
				tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
				if tmp1 <= tmp2 {
					randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
				}
			}
			randPrice := zuiyouEcpm * randPRValue / 100
			eKey := platformPos.PlatformAppPriceEncrypt
			iKey := platformPos.PlatformAppPriceEncrypt2

			encodePriceValue, _ = utils.EncryptPrice(float64(randPrice), eKey, iKey)
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Material.Name) > 0 {
			respListItemMap["title"] = adInfoItem.Material.Name
		}

		// description
		if len(adInfoItem.Material.Desc) > 0 {
			respListItemMap["description"] = adInfoItem.Material.Desc
		}

		// crid
		respListItemMap["crid"] = adInfoItem.CreativeId

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.Interaction.InvokeUrl) > 0 {
			respListItemMap["deep_link"] = adInfoItem.Interaction.InvokeUrl

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, dpTrackInfoItem := range adInfoItem.Tracking.InvokeWaitSuccTracking {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, dpTrackInfoItem)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, dpTrackInfoItem := range adInfoItem.Tracking.InvokeFailedTracking {
					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, dpTrackInfoItem)
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.Material.IconUrl) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Material.IconUrl
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Interaction.InvokeUrl)
		}

		isVideo := false

		// 视频
		if adInfoItem.Material.Video != nil && len(adInfoItem.Material.Video.VideoUrl) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Material.Video.Dur > 0 {
				respListVideoItemMap["duration"] = adInfoItem.Material.Video.Dur
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(adInfoItem.Material.Video.Dur)/1000)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
					continue
				}
			}
			if adInfoItem.Material.Video.Width > 0 {
				respListVideoItemMap["width"] = adInfoItem.Material.Video.Width
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Material.Video.Height > 0 {
				respListVideoItemMap["height"] = adInfoItem.Material.Video.Height
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Material.Video.Width > 0 && adInfoItem.Material.Video.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.Material.Video.Width), int(adInfoItem.Material.Video.Height))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
					continue
				}
			}
			respListVideoItemMap["video_url"] = adInfoItem.Material.Video.VideoUrl

			// cover_url
			if len(adInfoItem.Material.Video.CoverUrl) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Material.Video.CoverUrl
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			// if platformPos.PlatformPosType == 11 {
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, videoStartTrackInfoItem := range adInfoItem.Tracking.VideoStartTracking {
				respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoStartTrackInfoItem)
			}
			// if platformPos.PlatformPosProperty == 1 && adInfoItem.Material.Video.Dur > 0 && adInfoItem.Tracking.VideoPlayX > 0 {
			// 	if adInfoItem.Tracking.VideoPlayX < 7000 {
			// 		for _, videoTrackInfoItem := range adInfoItem.Tracking.VideoPlayXTracking {
			// 			respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoTrackInfoItem)
			// 		}
			// 	}
			// }
			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, videoEndTrackInfoItem := range adInfoItem.Tracking.VideoFinishTracking {
				respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoEndTrackInfoItem)
			}
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			// }

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Material.Img.CoverUrl) > 0 {
				respListImageItemMap["url"] = adInfoItem.Material.Img.CoverUrl
			} else if len(adInfoItem.Material.Img.ImgUrls) > 0 {
				respListImageItemMap["url"] = adInfoItem.Material.Img.ImgUrls[0]
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
				continue
			}

			if adInfoItem.Material.Img.Width > 0 {
				respListImageItemMap["width"] = adInfoItem.Material.Img.Width
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Material.Img.Height > 0 {
				respListImageItemMap["height"] = adInfoItem.Material.Img.Height
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Material.Img.Width > 0 && adInfoItem.Material.Img.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.Material.Img.Width), int(adInfoItem.Material.Img.Height))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
					continue
				}
			}
			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if len(adInfoItem.Interaction.LandingUrl) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.Interaction.LandingUrl
			respListItemMap["landpage_url"] = adInfoItem.Interaction.LandingUrl
		} else if len(adInfoItem.Interaction.DownloadUrl) > 0 {

			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.Interaction.DownloadUrl
			respListItemMap["download_url"] = adInfoItem.Interaction.DownloadUrl

			// if len(adInfoItem.App.AppName) > 0 {
			// 	respListItemMap["app_name"] = adInfoItem.App.AppName
			// }

		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
			continue
		}

		if len(adInfoItem.Interaction.PkgName) > 0 {
			respListItemMap["package_name"] = adInfoItem.Interaction.PkgName
		} else {
			hackPackageName := GetPackageNameByDeeplink(adInfoItem.Interaction.InvokeUrl)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName
			}
		}

		if len(adInfoItem.Interaction.DeveloperName) > 0 {
			respListItemMap["publisher"] = adInfoItem.Interaction.DeveloperName
		}
		if len(adInfoItem.Interaction.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.Interaction.AppVersion
		}
		if len(adInfoItem.Interaction.PrivacyUrl) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.Interaction.PrivacyUrl
		}
		if len(adInfoItem.Interaction.AppIntroUrl) > 0 {
			respListItemMap["appinfo_url"] = adInfoItem.Interaction.AppIntroUrl
		}
		// if len(adInfoContentItem.MetaData.ApkInfo.Permissions) > 0 {
		// 	permissionStr := ""
		// 	for _, huaweiPermissionItem := range adInfoContentItem.MetaData.ApkInfo.Permissions {
		// 		tmpPermission, _ := url.QueryUnescape(huaweiPermissionItem.PermissionLabel)
		// 		permissionStr = permissionStr + tmpPermission + "\n"
		// 	}
		// 	respListItemMap["permission"] = permissionStr
		// }
		// if adInfoItem.ApkSize > 0 {
		// 	respListItemMap["package_size"] = adInfoItem.ApkSize
		// }

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, zuiyouEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		mhImpParams.Add("log", bigdataParams)
		zuiyouWinNoticeURL := getZuiYouPriceWinURL(adInfoItem.WinUrl, platformPos, zuiyouEcpm, encodePriceValue)
		if len(zuiyouWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, zuiyouWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.Tracking.ImpTracking {
			impItem := trackInfoItem
			impItem = strings.Replace(impItem, "__BID_PRICE__", encodePriceValue, -1)

			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.Tracking.ClickTracking {
			clkItem := trackInfoItem
			clkItem = strings.Replace(clkItem, "__BID_PRICE__", encodePriceValue, -1)

			clkItem = strings.Replace(clkItem, "__DOWN_X__", tmpDownX, -1)
			clkItem = strings.Replace(clkItem, "__DOWN_Y__", tmpDownY, -1)
			clkItem = strings.Replace(clkItem, "__UP_X__", tmpUpX, -1)
			clkItem = strings.Replace(clkItem, "__UP_Y__", tmpUpY, -1)

			if platformPos.PlatformPosIsReportSLD == 1 {
				clkItem = strings.Replace(clkItem, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
			}

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// invoke_try_tracking
		for _, trackInfoItem := range adInfoItem.Tracking.InvokeTryTracking {
			clkItem := trackInfoItem
			clkItem = strings.Replace(clkItem, "__BID_PRICE__", encodePriceValue, -1)
			if platformPos.PlatformPosIsReportSLD == 1 {
				clkItem = strings.Replace(clkItem, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
			}

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(zuiyouLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, zuiyouLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlZuiYouPriceFailedURL(zuiyouLossNoticeURL, &bigdataExtra)
			continue
		}

		respListItemMap["p_ecpm"] = zuiyouEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// zuiyou resp
	respZuiYou := models.MHUpResp{}
	respZuiYou.RespData = &mhResp
	respZuiYou.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respZuiYou
}

type ZuiYouEcpmSort []*zuiyou_up.VulcanResponse_Ad

func (s ZuiYouEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s ZuiYouEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s ZuiYouEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Price > s[j].Price
}

func getZuiYouPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, zuiyouEcpm int, encodePriceValue string) string {
	if zuiyouEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	tmpWinNoticeURL := strings.Replace(winNoticeURL, "__BID_PRICE__", encodePriceValue, -1)
	return tmpWinNoticeURL
}

func getZuiYouPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, zuiyouEcpm int) string {
	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	if zuiyouEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 &&
		len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {

		randPrice := int(zuiyouEcpm * tmpRandValue / 100)

		eKey := platformPos.PlatformAppPriceEncrypt
		iKey := platformPos.PlatformAppPriceEncrypt2

		encodePriceValue, _ := utils.EncryptPrice(float64(randPrice), eKey, iKey)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__BID_PRICE__", encodePriceValue, -1)
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__ACTION_LOSS__", "0", -1)

		return tmpLossNoticeURL
	}
	return ""
}

// curl price failed
func curlZuiYouPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("ks loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}
