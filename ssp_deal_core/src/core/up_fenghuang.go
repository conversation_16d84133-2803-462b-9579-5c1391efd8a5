package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromFengHuang ...
func GetFromFengHuang(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	fmt.Println("get from fenghuang")

	// test
	// platformPos.PlatformAppID = "21087"
	// platformPos.PlatformPosID = "2108707"
	// platformPos.PlatformAppBundle = "com.test"
	// fmt.Println("get from fenghuang, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from fenghuang, p_pos_id:", platformPos.PlatformPosID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := utils.ConvertStringToInt(mhReq.Device.CPUNum)
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	////////////////////////////////////////////////////////////////////////////////////////
	// imp
	reqImpArray := []map[string]interface{}{}

	reqImpItemDealMap := map[string]interface{}{}
	reqImpItemDealMap["type"] = 1
	reqImpItemDealMap["bidfloor"] = localPosFloorPrice

	reqImpItemMap := map[string]interface{}{}
	reqImpItemMap["adid"] = platformPos.PlatformPosID
	reqImpItemMap["deal"] = reqImpItemDealMap

	reqImpArray = append(reqImpArray, reqImpItemMap)

	// device
	reqDeviceInfoMap := map[string]interface{}{}
	reqDeviceInfoMap["ua"] = destConfigUA
	reqDeviceInfoMap["ip"] = mhReq.Device.IP
	reqDeviceInfoMap["bundle"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	if mhReq.Device.Os == "android" {
		if mhReq.Device.Os == "android" {
			// fmt.Println("os version: " + mhReq.Device.OsVersion)
			osvMajor := 0
			if len(mhReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// fmt.Println(osvMajor)
			}
			if osvMajor < 10 {
				isImeiOK := false

				if len(mhReq.Device.Imei) > 0 {
					isImeiOK = true
					reqDeviceInfoMap["imeimd5"] = utils.GetMd5(mhReq.Device.Imei)
				} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
					isImeiOK = true
					reqDeviceInfoMap["imeimd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
				}

				// if len(mhReq.Device.AndroidID) > 0 {
				// 	reqDeviceInfoMap["dpidmd5"] = utils.GetMd5(mhReq.Device.AndroidID)

				// 	extraReportParams = extraReportParams + ",android_id"
				// } else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
				// 	reqDeviceInfoMap["dpidmd5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)

				// 	extraReportParams = extraReportParams + ",android_id_md5"
				// }

				if isImeiOK {
				} else {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 104013

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				// if len(mhReq.Device.AndroidID) > 0 {
				// 	reqDeviceInfoMap["dpidmd5"] = utils.GetMd5(mhReq.Device.AndroidID)

				// 	extraReportParams = extraReportParams + ",android_id"
				// } else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
				// 	reqDeviceInfoMap["dpidmd5"] = strings.ToLower(mhReq.Device.AndroidIDMd5)

				// 	extraReportParams = extraReportParams + ",android_id_md5"
				// }

				if len(mhReq.Device.Oaid) > 0 {
					reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
				}

				if len(mhReq.Device.Oaid) > 0 {
				} else {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 104013

					return MhUpErrorRespMap("", bigdataExtra)
				}
			}
		}
		reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = "Android"
		reqDeviceInfoMap["model"] = mhReq.Device.Model
		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osr"] = mhReq.Device.OsVersion
		}
	} else if mhReq.Device.Os == "ios" {
		// fmt.Println("get from fenguang ios")
		reqDeviceInfoMap["make"] = "Apple"
		reqDeviceInfoMap["os"] = "iOS"
		reqDeviceInfoMap["model"] = mhReq.Device.Model

		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osr"] = mhReq.Device.OsVersion
		}

		isIosDeviceOK := false

		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				reqDeviceInfoMap["ifa"] = mhReq.Device.Idfa
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 &&
				// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
				// reqDeviceInfoMap["country"] = tmpCountry
				// reqDeviceInfoMap["language"] = tmpLanguage
				// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
				// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
				// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
				reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
				reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
				reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
				reqDeviceInfoMap["ostimezone"] = tmpTimeZone

				if tmpCPUNum > 0 {
					reqDeviceInfoMap["cpunum"] = tmpCPUNum
				}

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["ifa"] = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 &&
					// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					// reqDeviceInfoMap["country"] = tmpCountry
					// reqDeviceInfoMap["language"] = tmpLanguage
					// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
					reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
					reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
					reqDeviceInfoMap["ostimezone"] = tmpTimeZone

					if tmpCPUNum > 0 {
						reqDeviceInfoMap["cpunum"] = tmpCPUNum
					}

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["ifa"] = mhReq.Device.Idfa
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 &&
					// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
					// reqDeviceInfoMap["country"] = tmpCountry
					// reqDeviceInfoMap["language"] = tmpLanguage
					// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
					// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
					// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
					reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
					reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
					reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
					reqDeviceInfoMap["ostimezone"] = tmpTimeZone

					if tmpCPUNum > 0 {
						reqDeviceInfoMap["cpunum"] = tmpCPUNum
					}

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		// 如果替换包开走替换包判定
		if platformPos.PlatformAppIsReplaceDID == 1 {
			isIosDeviceOK = true
		}

		if isIosDeviceOK {
		} else {
			// fmt.Println("get from gdt error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}
	reqDeviceInfoMap["w"] = mhReq.Device.ScreenWidth
	reqDeviceInfoMap["h"] = mhReq.Device.ScreenHeight

	if mhReq.Network.ConnectType == 0 {
		reqDeviceInfoMap["connectiontype"] = 0
	} else if mhReq.Network.ConnectType == 1 {
		reqDeviceInfoMap["connectiontype"] = 1
	} else if mhReq.Network.ConnectType == 2 {
		reqDeviceInfoMap["connectiontype"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		reqDeviceInfoMap["connectiontype"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		reqDeviceInfoMap["connectiontype"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		reqDeviceInfoMap["connectiontype"] = 5
	} else {
		reqDeviceInfoMap["connectiontype"] = 0
	}

	if mhReq.Network.Carrier == 0 {
		reqDeviceInfoMap["carrier"] = 0
	} else if mhReq.Network.Carrier == 1 {
		reqDeviceInfoMap["carrier"] = 1
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceInfoMap["carrier"] = 2
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceInfoMap["carrier"] = 3
	} else {
		reqDeviceInfoMap["carrier"] = 0
	}

	reqDeviceInfoMap["devicetype"] = 2

	if len(mhReq.Device.BootMark) > 0 {
		reqDeviceInfoMap["boot_mark"] = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		reqDeviceInfoMap["update_mark"] = mhReq.Device.UpdateMark
	}
	lowerManufacturer := strings.ToLower(mhReq.Device.Manufacturer)
	if lowerManufacturer == "huawei" || lowerManufacturer == "honor" {
		if len(mhReq.Device.HMSCoreVersion) > 0 {
			reqDeviceInfoMap["verCodeOfHms"] = mhReq.Device.HMSCoreVersion
		}
		if len(mhReq.Device.AppStoreVersion) > 0 {
			reqDeviceInfoMap["verCodeOfAG"] = mhReq.Device.AppStoreVersion
		}
	} else if lowerManufacturer == "vivo" {
		if len(mhReq.Device.AppStoreVersion) > 0 {
			reqDeviceInfoMap["vivostorever"] = mhReq.Device.AppStoreVersion
		}
	}

	// if len(mhReq.Device.Mac) > 0 {
	// 	reqUserInfoMap["mac"] = mhReq.Device.Mac
	// }

	// 替换包
	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imeimd5")
					delete(reqDeviceInfoMap, "oaid")
					delete(reqDeviceInfoMap, "verCodeOfHms")
					delete(reqDeviceInfoMap, "verCodeOfAG")
					delete(reqDeviceInfoMap, "vivostorever")

					reqDeviceInfoMap["osr"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua
					}
					if osvMajor < 10 {

						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imeimd5"] = utils.GetMd5(didRedisData.Imei)

						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imeimd5"] = didRedisData.ImeiMd5
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true

			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						reqDeviceInfoMap["osr"] = didRedisData.OsVersion
						reqDeviceInfoMap["model"] = didRedisData.Model
						if platformPos.PlatformAppIsReplaceDIDUa == 1 {
							reqDeviceInfoMap["ua"] = didRedisData.Ua

							replaceUA = didRedisData.Ua
						}
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								reqDeviceInfoMap["ifa"] = didRedisData.Idfa

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							// tmpCountry = didRedisData.Country
							// tmpLanguage = didRedisData.Language
							// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 &&
								// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
								// reqDeviceInfoMap["country"] = tmpCountry
								// reqDeviceInfoMap["language"] = tmpLanguage
								// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
								// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
								// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
								reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
								reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
								reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
								reqDeviceInfoMap["ostimezone"] = tmpTimeZone

								if tmpCPUNum > 0 {
									reqDeviceInfoMap["cpunum"] = tmpCPUNum
								}

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["ifa"] = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								// tmpCountry = didRedisData.Country
								// tmpLanguage = didRedisData.Language
								// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 &&
									// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									// reqDeviceInfoMap["country"] = tmpCountry
									// reqDeviceInfoMap["language"] = tmpLanguage
									// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
									reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
									reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
									reqDeviceInfoMap["ostimezone"] = tmpTimeZone

									if tmpCPUNum > 0 {
										reqDeviceInfoMap["cpunum"] = tmpCPUNum
									}

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["ifa"] = didRedisData.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								// tmpCountry = didRedisData.Country
								// tmpLanguage = didRedisData.Language
								// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								// tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 &&
									// len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									// len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									reqDeviceInfoMap["startuptime"] = utils.ConvertStringToInt(strings.Split(tmpDeviceStartSec, ".")[0])
									// reqDeviceInfoMap["country"] = tmpCountry
									// reqDeviceInfoMap["language"] = tmpLanguage
									// reqDeviceInfoMap["device_name_md5"] = tmpDeviceNameMd5
									// reqDeviceInfoMap["hardware_machine"] = tmpHardwareMachine
									// reqDeviceInfoMap["hardware_model"] = tmpHardwareModel
									reqDeviceInfoMap["memtotal"] = utils.ConvertStringToInt(strings.Split(tmpPhysicalMemoryByte, ".")[0])
									reqDeviceInfoMap["disktotal"] = utils.ConvertStringToInt(strings.Split(tmpHarddiskSizeByte, ".")[0])
									reqDeviceInfoMap["mbtime"] = utils.ConvertStringToInt(strings.Split(tmpSystemUpdateSec, ".")[0])
									reqDeviceInfoMap["ostimezone"] = tmpTimeZone

									if tmpCPUNum > 0 {
										reqDeviceInfoMap["cpunum"] = tmpCPUNum
									}

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					isHaveReplace = true
				} else {
					// 暂未实现因子替换包
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"appid":  platformPos.PlatformAppID,
		"appver": platformPos.PlatformAppVersion,
		"imps":   reqImpArray,
		"device": reqDeviceInfoMap,
		"secure": 0,
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	fmt.Println("fenghuang req: " + string(jsonData))

	// debug
	// bigdataExtra.InternalCode = 900101
	// bigdataExtra.ExternalCode = 102006
	// return MhUpErrorRespMap("", bigdataExtra)

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	requestGet.Header.Add("api-version", "1.0.1")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("fenghuang resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	fenghuangRespStu := FengHuangRespStu{}
	json.Unmarshal([]byte(bodyContent), &fenghuangRespStu)

	// if fenghuangRespStu.Err.Code == 0 {
	// 	bigdataExtra.InternalCode = 900103
	// 	bigdataExtra.ExternalCode = 102006
	// 	return MhUpErrorRespMap("", bigdataExtra)
	// }

	if len(fenghuangRespStu.Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, fenghuangInfoItem := range fenghuangRespStu.Ads {
		adInfoItem := fenghuangInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		fenghuangEcpm := adInfoItem.BidDeal.Price

		respTmpPrice = respTmpPrice + fenghuangEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if fenghuangEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			fenghuangEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > fenghuangEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(fenghuangEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceHexValue := "{BID_PRICE_AES}"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("fenghuang price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			// randPRValue := 95 + rand.Intn(4)
			// macroPrice := utils.ConvertIntToString(int(fenghuangEcpm * randPRValue / 100))
			//  凤凰的成交价上报按照返回价格上报
			macroPrice := utils.ConvertIntToString(fenghuangEcpm)
			fenghuangkey := platformPos.PlatformAppPriceEncrypt
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, fenghuangkey)
			encodePriceHexValue = utils.Base64URLEncode(encodePrice)
		}

		currentMillSecond := utils.GetCurrentMilliSecond()
		currentSecond := utils.GetCurrentSecond()

		// win notice url
		winNoticeURL := adInfoItem.BidDeal.Nurl

		if len(winNoticeURL) > 0 {
			winNoticeURL = strings.Replace(winNoticeURL, "__TS_MS__", utils.ConvertInt64ToString(currentMillSecond), -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
			winNoticeURL = strings.Replace(winNoticeURL, "{BID_PRICE_AES}", encodePriceHexValue, -1)

			go func() {
				defer func() {
					if err := recover(); err != nil {
						fmt.Println("fenghuang nurl panic:", err)
					}
				}()
				curlFengHuangNurl(winNoticeURL)
			}()
		}

		// 创意类型:1:静态创意 2:动态创意;
		if adInfoItem.Type == 2 {
			continue
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Creative.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Creative.Title
		}

		// description
		if len(adInfoItem.Creative.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Creative.Description
		}

		// crid
		respListItemMap["crid"] = adInfoItem.Creative.CreativeID

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.Creative.Deeplink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.Creative.Deeplink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, dpTrackInfoItem := range adInfoItem.Creative.Deeplinksurls {
				trackItem := dpTrackInfoItem
				trackItem = strings.Replace(trackItem, "__TS_MS__", utils.ConvertInt64ToString(currentMillSecond), -1)
				trackItem = strings.Replace(trackItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
				trackItem = strings.Replace(trackItem, "{BID_PRICE_AES}", encodePriceHexValue, -1)
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, trackItem)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.Creative.AppIconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Creative.AppIconURL
		} else if len(adInfoItem.Icon.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Icon.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Creative.Deeplink)
		}

		// 视频vast不支持
		if adInfoItem.Type != 1 || len(adInfoItem.Vast) > 0 {
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		isVideo := false

		if len(adInfoItem.Creative.Video.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Creative.Video.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Creative.Video.VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}

				respListVideoItemMap["duration"] = adInfoItem.Creative.Video.VideoDuration
			}
			respListVideoItemMap["width"] = adInfoItem.Creative.Width
			respListVideoItemMap["height"] = adInfoItem.Creative.Height
			respListVideoItemMap["video_url"] = adInfoItem.Creative.Video.VideoURL

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Creative.Width, adInfoItem.Creative.Height)
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}

			// cover_url
			if len(adInfoItem.Creative.Video.CoverURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Creative.Video.CoverURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track
			var respListEventTrackURLMap []map[string]interface{}

			// event track video start
			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string

			for _, videoBeginMonitorItem := range adInfoItem.Creative.Video.VideoBeginMonitors {
				respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoBeginMonitorItem)
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			// event track video end
			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, videoEndMonitorItem := range adInfoItem.Creative.Video.VideoEndMonitors {
				respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoEndMonitorItem)
			}

			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Creative.ImageURLs) > 0 {
				respListImageItemMap["url"] = adInfoItem.Creative.ImageURLs[0]
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if adInfoItem.Creative.Width > 0 {
				respListImageItemMap["width"] = adInfoItem.Creative.Width
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Creative.Height > 0 {
				respListImageItemMap["height"] = adInfoItem.Creative.Height
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Creative.Width > 0 && adInfoItem.Creative.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Creative.Width, adInfoItem.Creative.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// 安卓机型如果 Creative.durl 不为空并且 Creative.daction 为 1，则为广点通的下载广告
		if adInfoItem.Creative.DAction == 1 && len(adInfoItem.Creative.DownloadURL) > 0 {
			continue
		}

		// interact_type ad_url
		if len(adInfoItem.Creative.LandingpageURL) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.Creative.LandingpageURL
			respListItemMap["landpage_url"] = adInfoItem.Creative.LandingpageURL
		} else if adInfoItem.Creative.DAction == 0 && len(adInfoItem.Creative.DownloadURL) > 0 {

			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.Creative.DownloadURL
			respListItemMap["download_url"] = adInfoItem.Creative.DownloadURL
			if len(adInfoItem.Creative.LandingpageURL) > 0 {
				respListItemMap["landpage_url"] = adInfoItem.Creative.LandingpageURL
			}
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.Creative.Bundle) > 0 {
			respListItemMap["package_name"] = adInfoItem.Creative.Bundle
		}

		if len(adInfoItem.Creative.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.Creative.AppName
		}
		if len(adInfoItem.Creative.DeveloperName) > 0 {
			respListItemMap["publisher"] = adInfoItem.Creative.DeveloperName
		}
		if len(adInfoItem.Creative.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.Creative.AppVersion
		}
		if len(adInfoItem.Creative.AppPrivacyPolicyUrl) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.Creative.AppPrivacyPolicyUrl
		}
		if len(adInfoItem.Creative.AppPermissions) > 0 {
			permissionStr := ""
			for _, permissionItem := range adInfoItem.Creative.AppPermissions {
				tmpPermission, _ := url.QueryUnescape(permissionItem.PermissionLabel)
				permissionStr = permissionStr + tmpPermission + "\n"
			}
			respListItemMap["permission"] = permissionStr
		}
		// if adInfoItem.ApkSize > 0 {
		// 	respListItemMap["package_size"] = adInfoItem.ApkSize
		// }

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, fenghuangEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, trackURLItem := range adInfoItem.ImpTracks {
			impItem := trackURLItem
			impItem = strings.Replace(impItem, "__TS_MS__", utils.ConvertInt64ToString(currentMillSecond), -1)
			impItem = strings.Replace(impItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
			impItem = strings.Replace(impItem, "{BID_PRICE_AES}", encodePriceHexValue, -1)
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackURLItem := range adInfoItem.ClkTracks {
			clkItem := trackURLItem
			clkItem = strings.Replace(clkItem, "__TS_MS__", utils.ConvertInt64ToString(currentMillSecond), -1)
			clkItem = strings.Replace(clkItem, "__TS_S__", utils.ConvertInt64ToString(currentSecond), -1)
			clkItem = strings.Replace(clkItem, "{BID_PRICE_AES}", encodePriceHexValue, -1)
			clkItem = strings.Replace(clkItem, "__CLICK_DOWN_X__", "__DOWN_X__", -1)
			clkItem = strings.Replace(clkItem, "__CLICK_DOWN_Y__", "__DOWN_Y__", -1)
			clkItem = strings.Replace(clkItem, "__CLICK_UP_X__", "__UP_X__", -1)
			clkItem = strings.Replace(clkItem, "__CLICK_UP_Y__", "__UP_Y__", -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = fenghuangEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// fenghuang resp
	respFenghuang := models.MHUpResp{}
	respFenghuang.RespData = &mhResp
	respFenghuang.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respFenghuang
}

func curlFengHuangNurl(nurl string) {
	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println(requestGet.URL.String())
	// fmt.Println("gdt nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return
	}

	defer resp.Body.Close()
}

// FengHuangRespStu ...
type FengHuangRespStu struct {
	AppID string                `json:"appid"`
	Ads   []FengHuangRespAdsStu `json:"ads"`
	Err   FengHuangRespErrStu   `json:"err"`
}

// FengHuangRespErrStu ...
type FengHuangRespErrStu struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// FengHuangRespAdsStu ...
type FengHuangRespAdsStu struct {
	AdID      string                      `json:"adid"`
	BidDeal   FengHuangRespAdsBidDealStu  `json:"biddeal"`
	Type      int                         `json:"type"`
	StyleType string                      `json:"styletype"`
	Style     string                      `json:"style"`
	Creative  FengHuangRespAdsCreativeStu `json:"creative"`
	Html      string                      `json:"html"`
	Vast      string                      `json:"vast"`
	ImpTracks []string                    `json:"murls"`
	ClkTracks []string                    `json:"acurls"`
	Icon      FengHuangRespAdsIconStu     `json:"icon"`
}

// FengHuangRespAdsBidDealStu ...
type FengHuangRespAdsBidDealStu struct {
	Type  int    `json:"type"`
	Price int    `json:"price"`
	Nurl  string `json:"nurl"`
}

// FengHuangRespAdsIconStu ...
type FengHuangRespAdsIconStu struct {
	Text    string `json:"text"`
	IconURL string `json:"url"`
}

// FengHuangRespAdsCreativeStu ...
type FengHuangRespAdsCreativeStu struct {
	CreativeID          string                                  `json:"id"`
	Width               int                                     `json:"w"`
	Height              int                                     `json:"h"`
	ImageURLs           []string                                `json:"imgs"`
	LandingpageURL      string                                  `json:"clickthrough"`
	Title               string                                  `json:"title"`
	Text                string                                  `json:"text"`
	Description         string                                  `json:"desc"`
	DAction             int                                     `json:"daction"`
	DownloadURL         string                                  `json:"durl"`
	Dcurls              []string                                `json:"dcurls"`
	Bundle              string                                  `json:"bundle"`
	AppName             string                                  `json:"appname"`
	Deeplink            string                                  `json:"dplurl"`
	Deeplinkturls       []string                                `json:"dpturls"`
	Deeplinksurls       []string                                `json:"dpsurls"`
	Installedurls       []string                                `json:"installedurls"`
	DeveloperName       string                                  `json:"developerName"`
	AppVersion          string                                  `json:"appVersion"`
	AppPrivacyPolicyUrl string                                  `json:"appPrivacyPolicyUrl"`
	AppPermissionUrl    string                                  `json:"appPermissionUrl"`
	AppPermissions      []FengHuangRespAdsCreativePermissionStu `json:"appPermissions"`
	AppIconURL          string                                  `json:"appIconUrl"`
	InteractionType     string                                  `json:"interactionType"`
	Video               FengHuangRespAdsCreativeVideoStu        `json:"video"`
}

// FengHuangRespAdsCreativePermissionStu ...
type FengHuangRespAdsCreativePermissionStu struct {
	PermissionLabel      string `json:"permissionLabel"`
	PermissionDesciption string `json:"permissionDesc"`
}

// FengHuangRespAdsCreativeVideoStu ...
type FengHuangRespAdsCreativeVideoStu struct {
	VideoURL                  string   `json:"video_url"`
	VideoDuration             int      `json:"video_time"`
	VideoSize                 string   `json:"video_size"`
	CoverURL                  string   `json:"cover_url"`
	VideoBeginMonitors        []string `json:"video_begin"`
	VideoOneQuarterMonitors   []string `json:"first_quartile_urls"`
	VideoHalfMonitors         []string `json:"mid_point_urls"`
	VideoThreeQuarterMonitors []string `json:"third_quartile_urls"`
	VideoEndMonitors          []string `json:"video_end"`
}
