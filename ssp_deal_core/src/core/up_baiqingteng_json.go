package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"

	uuid "github.com/satori/go.uuid"
)

// baiqingteng json协议
func GetFromBaiQingTengJson(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {

	// http://debug.mobads.baidu.com/pbssp
	// E_KEY ="bcd352eb405e9455"
	// I_KEY = "fc1415055eefbc89"
	// 安卓appid: b3c3ab06
	// 安卓广告位: android01
	// sspid: 1126

	// platformPos.PlatformAppUnionBaiduMediaID = "1126"
	// platformPos.PlatformAppID = "b3c3ab06"
	// platformPos.PlatformPosID = "android01"
	// platformPos.PlatformAppBundle = "com.test"
	// platformPos.PlatformAppUpURL = "http://debug.mobads.baidu.com/pbssp"
	// platformPos.PlatformAppVersion = "1.0"
	// categoryInfo.FloorPrice = 15
	// fmt.Println("get from baiqingteng json, local app id: ", localPos.LocalAppID)
	// fmt.Println("get from baiqingteng json, local pos id: ", localPos.LocalPosID)
	// fmt.Println("get from baiqingteng json, local app type: ", localPos.LocalAppType)
	// fmt.Println("get from baiqingteng json, platform app id: ", platformPos.PlatformAppID)
	// fmt.Println("get from baiqingteng json, platform pos id: ", platformPos.PlatformPosID)
	// fmt.Println("get from baiqingteng json, platform pos type: ", platformPos.PlatformPosType)
	// fmt.Println("get from baiqingteng json, platform pos direction: ", platformPos.PlatformPosDirection)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	// device
	baiqingtengReqDevice := BaiQingTengBidRequestDeviceStu{}

	baiqingtengReqDevice.W = mhReq.Device.ScreenWidth
	baiqingtengReqDevice.H = mhReq.Device.ScreenHeight

	// device ip
	if strings.Contains(mhReq.Device.IP, ":") {
		baiqingtengReqDevice.IPV6 = mhReq.Device.IP
	} else {
		baiqingtengReqDevice.IP = mhReq.Device.IP
	}

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		baiqingtengReqDevice.UA = destConfigUA
	}

	// device carrier
	carrierStr := ""
	if mhReq.Network.Carrier == 1 {
		baiqingtengReqDevice.Carrier = 1
		carrierStr = "中国移动"
	} else if mhReq.Network.Carrier == 2 {
		baiqingtengReqDevice.Carrier = 3
		carrierStr = "中国联通"
	} else if mhReq.Network.Carrier == 3 {
		baiqingtengReqDevice.Carrier = 2
		carrierStr = "中国电信"
	} else {
		baiqingtengReqDevice.Carrier = 0
		carrierStr = ""
	}

	// device network
	if mhReq.Network.ConnectType == 0 {
		baiqingtengReqDevice.NetworkType = 0
	} else if mhReq.Network.ConnectType == 1 {
		baiqingtengReqDevice.NetworkType = 1
	} else if mhReq.Network.ConnectType == 2 {
		baiqingtengReqDevice.NetworkType = 2
	} else if mhReq.Network.ConnectType == 3 {
		baiqingtengReqDevice.NetworkType = 3
	} else if mhReq.Network.ConnectType == 4 {
		baiqingtengReqDevice.NetworkType = 4
	} else if mhReq.Network.ConnectType == 7 {
		baiqingtengReqDevice.NetworkType = 5
	}

	// device type
	baiqingtengReqDevice.DeviceType = 1

	// device os
	if mhReq.Device.Os == "android" {
		baiqingtengReqDevice.Os = 2
	} else if mhReq.Device.Os == "ios" {
		baiqingtengReqDevice.Os = 1
	}

	// device osv
	baiqingtengReqDevice.Osv = mhReq.Device.OsVersion

	// device model
	baiqingtengReqDevice.Model = mhReq.Device.Model

	// device make
	baiqingtengReqDevice.Make = mhReq.Device.Manufacturer

	// device uid
	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
					DIDMd5: utils.GetMd5(mhReq.Device.Imei),
				}
				baiqingtengReqDevice.UID = baiqingtengReqDeviceUID

			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
					DIDMd5: strings.ToLower(mhReq.Device.ImeiMd5),
				}
				baiqingtengReqDevice.UID = baiqingtengReqDeviceUID
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
					Oaid: mhReq.Device.Oaid,
				}
				baiqingtengReqDevice.UID = baiqingtengReqDeviceUID
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from baiqingteng error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{}
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
							baiqingtengReqDeviceUIDCaid.ID = item.CAID
							baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

							baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)

							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
						baiqingtengReqDeviceUIDCaid.ID = item.CAID
						baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

						baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				// device -> factor -> caidFactor
				baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
					CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
						Disk:           tmpHarddiskSizeByte,
						Model:          tmpHardwareModel,
						PhysicalMemory: tmpPhysicalMemoryByte,
						SysFileTime:    tmpSystemUpdateSec,
						BootSecTime:    tmpDeviceStartSec,
						SystemVersion:  mhReq.Device.OsVersion,
						Machine:        tmpHardwareMachine,
						DeviceName:     tmpDeviceNameMd5,
						Language:       tmpLanguage,
						TimeZone:       tmpTimeZone,
						CarrierInfo:    carrierStr,
						CountryCode:    tmpCountry,
					},
				}
				baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

				// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
				baiqingtengReqDevice.Bstime = tmpDeviceStartSec
				baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
				baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
				baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec
			}
		}
		if strings.Contains(iosReportMainParameter, "paid") {
			if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpDeviceBirthSec) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				baiqingtengReqDevice.Paid = utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec)
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
								baiqingtengReqDeviceUIDCaid.ID = item.CAID
								baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

								baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
							baiqingtengReqDeviceUIDCaid.ID = item.CAID
							baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

							baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					// device -> factor -> caidFactor
					baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
						CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
							Disk:           tmpHarddiskSizeByte,
							Model:          tmpHardwareModel,
							PhysicalMemory: tmpPhysicalMemoryByte,
							SysFileTime:    tmpSystemUpdateSec,
							BootSecTime:    tmpDeviceStartSec,
							SystemVersion:  mhReq.Device.OsVersion,
							Machine:        tmpHardwareMachine,
							DeviceName:     tmpDeviceNameMd5,
							Language:       tmpLanguage,
							TimeZone:       tmpTimeZone,
							CarrierInfo:    carrierStr,
							CountryCode:    tmpCountry,
						},
					}
					baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

					// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
					baiqingtengReqDevice.Bstime = tmpDeviceStartSec
					baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
					baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
					baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
								baiqingtengReqDeviceUIDCaid.ID = item.CAID
								baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

								baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
							baiqingtengReqDeviceUIDCaid.ID = item.CAID
							baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

							baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					// device -> factor -> caidFactor
					baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
						CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
							Disk:           tmpHarddiskSizeByte,
							Model:          tmpHardwareModel,
							PhysicalMemory: tmpPhysicalMemoryByte,
							SysFileTime:    tmpSystemUpdateSec,
							BootSecTime:    tmpDeviceStartSec,
							SystemVersion:  mhReq.Device.OsVersion,
							Machine:        tmpHardwareMachine,
							DeviceName:     tmpDeviceNameMd5,
							Language:       tmpLanguage,
							TimeZone:       tmpTimeZone,
							CarrierInfo:    carrierStr,
							CountryCode:    tmpCountry,
						},
					}
					baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

					// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
					baiqingtengReqDevice.Bstime = tmpDeviceStartSec
					baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
					baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
					baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec
				}
			}
		}
		baiqingtengReqDevice.UID = baiqingtengReqDeviceUID

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from baiqingteng error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug baiqingteng android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					baiqingtengReqDevice.Osv = didRedisData.OsVersion

					// device model
					baiqingtengReqDevice.Model = didRedisData.Model

					// device make
					baiqingtengReqDevice.Make = didRedisData.Manufacturer

					if didRedisData.Version == 1 {
						// device carrier
						if didRedisData.Carrier == 1 {
							baiqingtengReqDevice.Carrier = 1
							carrierStr = "中国移动"
						} else if didRedisData.Carrier == 2 {
							baiqingtengReqDevice.Carrier = 3
							carrierStr = "中国联通"
						} else if didRedisData.Carrier == 3 {
							baiqingtengReqDevice.Carrier = 2
							carrierStr = "中国电信"
						} else {
							baiqingtengReqDevice.Carrier = 0
							carrierStr = ""
						}

						// device network
						if didRedisData.ConnectType == 0 {
							baiqingtengReqDevice.NetworkType = 0
						} else if didRedisData.ConnectType == 1 {
							baiqingtengReqDevice.NetworkType = 1
						} else if didRedisData.ConnectType == 2 {
							baiqingtengReqDevice.NetworkType = 2
						} else if didRedisData.ConnectType == 3 {
							baiqingtengReqDevice.NetworkType = 3
						} else if didRedisData.ConnectType == 4 {
							baiqingtengReqDevice.NetworkType = 4
						} else if didRedisData.ConnectType == 7 {
							baiqingtengReqDevice.NetworkType = 5
						}
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
								DIDMd5: utils.GetMd5(didRedisData.Imei),
							}
							baiqingtengReqDevice.UID = baiqingtengReqDeviceUID
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
								DIDMd5: strings.ToLower(didRedisData.ImeiMd5),
							}
							baiqingtengReqDevice.UID = baiqingtengReqDeviceUID
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{
								Oaid: didRedisData.Oaid,
							}
							baiqingtengReqDevice.UID = baiqingtengReqDeviceUID
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						baiqingtengReqDevice.UA = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug baiqingteng ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						baiqingtengReqDevice.Osv = didRedisData.OsVersion

						// device model
						baiqingtengReqDevice.Model = didRedisData.Model

						// device make
						baiqingtengReqDevice.Make = didRedisData.Manufacturer

						if didRedisData.Version == 1 {
							// device carrier
							if didRedisData.Carrier == 1 {
								baiqingtengReqDevice.Carrier = 1
								carrierStr = "中国移动"
							} else if didRedisData.Carrier == 2 {
								baiqingtengReqDevice.Carrier = 3
								carrierStr = "中国联通"
							} else if didRedisData.Carrier == 3 {
								baiqingtengReqDevice.Carrier = 2
								carrierStr = "中国电信"
							} else {
								baiqingtengReqDevice.Carrier = 0
								carrierStr = ""
							}

							// device network
							if didRedisData.ConnectType == 0 {
								baiqingtengReqDevice.NetworkType = 0
							} else if didRedisData.ConnectType == 1 {
								baiqingtengReqDevice.NetworkType = 1
							} else if didRedisData.ConnectType == 2 {
								baiqingtengReqDevice.NetworkType = 2
							} else if didRedisData.ConnectType == 3 {
								baiqingtengReqDevice.NetworkType = 3
							} else if didRedisData.ConnectType == 4 {
								baiqingtengReqDevice.NetworkType = 4
							} else if didRedisData.ConnectType == 7 {
								baiqingtengReqDevice.NetworkType = 5
							}
						}

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						baiqingtengReqDeviceUID := BaiQingTengBidRequestDeviceUIDStu{}

						// 初始化为空
						baiqingtengReqDevice.Factor = BaiQingTengBidRequestDeviceFactorStu{}
						baiqingtengReqDevice.Bstime = ""
						baiqingtengReqDevice.Aftime = ""
						baiqingtengReqDevice.Sftime = ""
						baiqingtengReqDevice.Fbtime = ""
						baiqingtengReqDevice.Paid = ""

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

								baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
										baiqingtengReqDeviceUIDCaid.ID = item.CAID
										baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

										baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
									baiqingtengReqDeviceUIDCaid.ID = item.CAID
									baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

									baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
									CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
										Disk:           tmpHarddiskSizeByte,
										Model:          tmpHardwareModel,
										PhysicalMemory: tmpPhysicalMemoryByte,
										SysFileTime:    tmpSystemUpdateSec,
										BootSecTime:    tmpDeviceStartSec,
										SystemVersion:  didRedisData.OsVersion,
										Machine:        tmpHardwareMachine,
										DeviceName:     tmpDeviceNameMd5,
										Language:       tmpLanguage,
										TimeZone:       tmpTimeZone,
										CarrierInfo:    carrierStr,
										CountryCode:    tmpCountry,
									},
								}

								baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

								// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
								baiqingtengReqDevice.Bstime = tmpDeviceStartSec
								baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
								baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
								baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if strings.Contains(iosReportMainParameter, "paid") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpDeviceBirthSec) > 0 {

								baiqingtengReqDevice.Paid = utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec)

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
											baiqingtengReqDeviceUIDCaid.ID = item.CAID
											baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

											baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
										baiqingtengReqDeviceUIDCaid.ID = item.CAID
										baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

										baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
										CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
											Disk:           tmpHarddiskSizeByte,
											Model:          tmpHardwareModel,
											PhysicalMemory: tmpPhysicalMemoryByte,
											SysFileTime:    tmpSystemUpdateSec,
											BootSecTime:    tmpDeviceStartSec,
											SystemVersion:  didRedisData.OsVersion,
											Machine:        tmpHardwareMachine,
											DeviceName:     tmpDeviceNameMd5,
											Language:       tmpLanguage,
											TimeZone:       tmpTimeZone,
											CarrierInfo:    carrierStr,
											CountryCode:    tmpCountry,
										},
									}

									baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

									// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
									baiqingtengReqDevice.Bstime = tmpDeviceStartSec
									baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
									baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
									baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									baiqingtengReqDeviceUID.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									baiqingtengReqDeviceUID.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
											baiqingtengReqDeviceUIDCaid.ID = item.CAID
											baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

											baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										baiqingtengReqDeviceUIDCaid := BaiQingTengBidRequestDeviceUIDCaidStu{}
										baiqingtengReqDeviceUIDCaid.ID = item.CAID
										baiqingtengReqDeviceUIDCaid.Version = item.CAIDVersion

										baiqingtengReqDeviceUID.Caid = append(baiqingtengReqDeviceUID.Caid, baiqingtengReqDeviceUIDCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									baiqingtengReqDeviceFactor := BaiQingTengBidRequestDeviceFactorStu{
										CaidFactor: BaiQingTengBidRequestDeviceFactorCaidFactorStu{
											Disk:           tmpHarddiskSizeByte,
											Model:          tmpHardwareModel,
											PhysicalMemory: tmpPhysicalMemoryByte,
											SysFileTime:    tmpSystemUpdateSec,
											BootSecTime:    tmpDeviceStartSec,
											SystemVersion:  didRedisData.OsVersion,
											Machine:        tmpHardwareMachine,
											DeviceName:     tmpDeviceNameMd5,
											Language:       tmpLanguage,
											TimeZone:       tmpTimeZone,
											CarrierInfo:    carrierStr,
											CountryCode:    tmpCountry,
										},
									}

									baiqingtengReqDevice.Factor = baiqingtengReqDeviceFactor

									// device bstime(开机时间), aftime(不填), sftime(系统版本更新时间), fbtime(device_birth_sec)
									baiqingtengReqDevice.Bstime = tmpDeviceStartSec
									baiqingtengReqDevice.Aftime = tmpSystemUpdateSec
									baiqingtengReqDevice.Sftime = tmpSystemUpdateSec
									baiqingtengReqDevice.Fbtime = tmpDeviceBirthSec

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						baiqingtengReqDevice.UID = baiqingtengReqDeviceUID

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							baiqingtengReqDevice.UA = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from baiqingteng error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from baiqingteng error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)

		// debug
		// if platformPos.PlatformAppID == "1109920434" {
		// 	models.BigDataDebugReq(c, bigdataUID, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
		// }
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// req imp
	baiqingtengReqImp := BaiQingTengBidRequestImpStu{
		ID:       "1",
		AppID:    platformPos.PlatformAppID,
		TagID:    platformPos.PlatformPosID,
		BidFloor: localPosFloorPrice,
		Secure:   1,
		MaxCount: 1,
	}
	// 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	if platformPos.PlatformPosType == 1 {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)

	} else if platformPos.PlatformPosType == 2 {
		baiqingtengReqImp.AdType = 3
		baiqingtengReqImp.Instl = 1

		baiqingtengReqImpAsset := BaiQingTengBidRequestImpAssetStu{
			TemplateId: 2,
		}
		// 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
		if platformPos.PlatformPosDirection == 1 {
			baiqingtengReqImpAsset.Ratio = 5
		} else {
			baiqingtengReqImpAsset.Ratio = 4
		}

		baiqingtengReqImp.Assets = append(baiqingtengReqImp.Assets, baiqingtengReqImpAsset)
	} else if platformPos.PlatformPosType == 3 {
		baiqingtengReqImp.AdType = 6

		baiqingtengReqImpAsset := BaiQingTengBidRequestImpAssetStu{
			TemplateId: 2,
		}
		// 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
		if platformPos.PlatformPosDirection == 1 {
			baiqingtengReqImpAsset.Ratio = 5
		} else {
			baiqingtengReqImpAsset.Ratio = 4
		}

		baiqingtengReqImp.Assets = append(baiqingtengReqImp.Assets, baiqingtengReqImpAsset)
	} else if platformPos.PlatformPosType == 4 {
		baiqingtengReqImp.AdType = 0

		baiqingtengReqImpAsset := BaiQingTengBidRequestImpAssetStu{
			TemplateId: 2,
		}
		// 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
		if platformPos.PlatformPosDirection == 1 {
			baiqingtengReqImpAsset.Ratio = 5
		} else {
			baiqingtengReqImpAsset.Ratio = 4
		}

		baiqingtengReqImp.Assets = append(baiqingtengReqImp.Assets, baiqingtengReqImpAsset)
	} else if platformPos.PlatformPosType == 9 || platformPos.PlatformPosType == 11 {
		baiqingtengReqImp.AdType = 4

		baiqingtengReqImpAsset := BaiQingTengBidRequestImpAssetStu{
			TemplateId: 2,
		}
		// 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
		if platformPos.PlatformPosDirection == 1 {
			baiqingtengReqImpAsset.Ratio = 5
		} else {
			baiqingtengReqImpAsset.Ratio = 4
		}

		baiqingtengReqImp.Assets = append(baiqingtengReqImp.Assets, baiqingtengReqImpAsset)
	}

	baiqingtengReqImp.ActionType = append(baiqingtengReqImp.ActionType, 0)
	baiqingtengReqImp.ActionType = append(baiqingtengReqImp.ActionType, 1)
	baiqingtengReqImp.ActionType = append(baiqingtengReqImp.ActionType, 2)
	if platformPos.PlatformAppIsReportAppList == 1 && len(mhReq.Device.AppList) > 0 {
		baiduAppId := models2.GetBaiduAppIdByAppIdByOs(mhReq.Device.AppList, mhReq.Device.Os)
		baiqingtengReqDevice.AppList = baiduAppId
	}

	// req app
	baiqingtengReqApp := BaiQingTengBidRequestAppStu{
		Bundle:  GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		Version: platformPos.PlatformAppVersion,
	}

	baiqingtengReq := BaiQingTengBidRequestStu{
		MediaID: utils.ConvertStringToInt(platformPos.PlatformAppUnionBaiduMediaID),
		ReqID:   utils.GetMd5(bigdataUID),
		App:     baiqingtengReqApp,
		Device:  baiqingtengReqDevice,
	}
	baiqingtengReq.Imp = append(baiqingtengReq.Imp, baiqingtengReqImp)

	jsonData, _ := json.Marshal(baiqingtengReq)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("baiqingteng url:", platformPos.PlatformAppUpURL)
	// fmt.Println("========================================================================================================================")

	// fmt.Println("baiqingteng req:", string(jsonData))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	fmt.Println("baiqingteng resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	baiqingtengResp := BaiQingTengBidResponseStu{}
	err = json.Unmarshal(bodyContent, &baiqingtengResp)
	if err != nil {
		fmt.Println(err)
	}

	// tmpRespByte, _ := json.Marshal(baiqingtengResp)
	// fmt.Println("baiqingteng resp code:", resp.StatusCode)
	// fmt.Println("baiqingteng resp:", string(tmpRespByte))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if baiqingtengResp.Nbr != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = baiqingtengResp.Nbr

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(baiqingtengResp.SeatBid) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = baiqingtengResp.Nbr

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(baiqingtengResp.SeatBid[0].BID) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = baiqingtengResp.Nbr

		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	// if platformPos.PlatformAppID == "dae57f48" || platformPos.PlatformAppID == "df53e75a" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("debug panic:", err)
	// 			}
	// 		}()

	// 		models.SaveDemandRespDataToHolo(c, bigdataUID, platformPos.PlatformAppID, platformPos.PlatformPosID, string(bodyContent))
	// 	}()
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := baiqingtengResp.SeatBid[0].BID

	if len(respDataListItemStu) > 1 {
		sort.Sort(baiqingtengJsonEcpmSort(respDataListItemStu))
	}

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		baiqingtengEcpm := item.Price

		respTmpPrice = respTmpPrice + baiqingtengEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if baiqingtengEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			baiqingtengEcpm = platformPos.PlatformPosEcpm
		}

		baiqingtengLossNoticeURLArray := getBaiQingTengJsonPriceFailedURL(item.Lurl, platformPos, baiqingtengEcpm, &bigdataExtra)

		// fmt.Println("gdt_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(baiqingtengEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > baiqingtengEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)

			continue
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(baiqingtengEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := item.Adm.Title
		respDescription := item.Adm.Desc
		respIconURL := string(item.Adm.Icon)
		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		if len(item.Adm.Images) > 0 {
			respImageURL = string(item.Adm.Images[0].Url)
			respImageWidth = int(item.Adm.Images[0].Width)
			respImageHeight = int(item.Adm.Images[0].Height)
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		if mhReq.Device.Os == "android" {
			if item.Adm.ActionType == 1 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = string(item.Adm.LanddingPage)
				respListItemMap["download_url"] = string(item.Adm.LanddingPage)
			} else if item.Adm.ActionType == 0 || item.Adm.ActionType == 2 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = string(item.Adm.LanddingPage)
				respListItemMap["landpage_url"] = string(item.Adm.LanddingPage)
			} else {

				curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
				continue
			}

			if len(string(item.Adm.AppName)) > 0 {
				respListItemMap["app_name"] = string(item.Adm.AppName)
			} else {
			}
			if len(string(item.Adm.Publisher)) > 0 {
				respListItemMap["publisher"] = string(item.Adm.Publisher)
			}
			if len(string(item.Adm.AppVersion)) > 0 {
				respListItemMap["app_version"] = string(item.Adm.AppVersion)
			}
			if len(string(item.Adm.Privacy)) > 0 {
				respListItemMap["privacy_url"] = string(item.Adm.Privacy)
			}
			if len(string(item.Adm.Permission)) > 0 {
				respListItemMap["permission_url"] = string(item.Adm.Permission)
			}
		} else if mhReq.Device.Os == "ios" {
			if len(string(item.Adm.BundleId)) > 0 {
				respListItemMap["ad_url"] = "https://apps.apple.com/cn/app/id" + string(item.Adm.BundleId)
				respListItemMap["interact_type"] = 1
				respListItemMap["download_url"] = "https://apps.apple.com/cn/app/id" + string(item.Adm.BundleId)
			} else {
				if len(string(item.Adm.LanddingPage)) > 0 {
					if strings.Contains(string(item.Adm.LanddingPage), "apple.com") {
						respListItemMap["ad_url"] = string(item.Adm.LanddingPage)
						respListItemMap["interact_type"] = 1
						respListItemMap["download_url"] = string(item.Adm.LanddingPage)
					} else {
						respListItemMap["ad_url"] = string(item.Adm.LanddingPage)
						respListItemMap["interact_type"] = 0
						respListItemMap["landpage_url"] = string(item.Adm.LanddingPage)
					}
				} else {
					curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
					continue
				}
			}
		}

		if len(string(item.Adm.Video.Url)) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.Adm.Video.Duration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(item.Adm.Video.Duration))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
					continue
				}

				respListVideoItemMap["duration"] = int(item.Adm.Video.Duration) * 1000
			}
			respListVideoItemMap["width"] = int(item.Adm.Video.Width)
			respListVideoItemMap["height"] = int(item.Adm.Video.Height)
			respListVideoItemMap["video_url"] = string(item.Adm.Video.Url)

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(item.Adm.Video.Width), int(item.Adm.Video.Height))
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
				continue
			}

			// cover_url
			if len(respImageURL) > 0 {
				respListVideoItemMap["cover_url"] = respImageURL
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			isVideoTrack := 0
			// event track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range item.TrackMonitors {
				if monitorItem.EventType == 100 {
					for _, monitorUrlItem := range monitorItem.Url {
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, string(monitorUrlItem))
					}
				}
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				isVideoTrack = 1
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range item.TrackMonitors {
				if monitorItem.EventType == 105 {
					for _, monitorUrlItem := range monitorItem.Url {
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, string(monitorUrlItem))
					}
				}
			}
			if len(respListVideoEndEventTrackURLMap) > 0 {
				isVideoTrack = 1

				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if isVideoTrack == 1 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL

				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		if len(string(item.Crid)) > 0 {
			respListItemMap["crid"] = item.Crid
		}

		// deeplink
		destDeepLink := ""
		if len(string(item.Adm.Deeplink)) > 0 || len(string(item.Adm.AppStoreLink)) > 0 {
			if len(string(item.Adm.AppStoreLink)) > 0 {
				destDeepLink = string(item.Adm.AppStoreLink)
			} else if len(string(item.Adm.Deeplink)) > 0 {
				destDeepLink = string(item.Adm.Deeplink)
			}

			respListItemMap["deep_link"] = destDeepLink

			if len(string(item.Adm.AppStoreLink)) > 0 {
				respListItemMap["market_url"] = string(item.Adm.AppStoreLink)
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			// if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			// 	var respListItemDeepLinkFailedArray []string

			// 	if len(item.ConversionLink) > 0 {
			// 		tmpConversionLink := strings.Replace(item.ConversionLink, "__ACTION_ID__", "249", -1)
			// 		respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, tmpConversionLink)
			// 	}

			// 	mhDPFailedParams := url.Values{}
			// 	mhDPFailedParams.Add("result", "1")
			// 	mhDPFailedParams.Add("reason", "3")
			// 	mhDPFailedParams.Add("log", bigdataParams)

			// 	respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			// 	respListItemConv11Map := map[string]interface{}{}
			// 	respListItemConv11Map["conv_type"] = 11
			// 	respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

			// 	respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			// }

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(string(item.Adm.PackageName)) > 0 {
			respListItemMap["package_name"] = string(item.Adm.PackageName)

			destPackageName = string(item.Adm.PackageName)
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, string(item.Adm.AppName), destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, baiqingtengEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link baiqingteng
		for _, showItem := range item.ShowUrls {
			tmpItem := string(showItem)

			priceMarcro := "${AUCTION_PRICE}"
			if baiqingtengEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {
				// 随机95-98%替换价格宏
				randPRValue := 95 + rand.Intn(4)
				randPrice := int(baiqingtengEcpm * randPRValue / 100)
				// 原ecpm上报
				randPrice = baiqingtengEcpm

				eKey := platformPos.PlatformAppPriceEncrypt
				iKey := platformPos.PlatformAppPriceEncrypt2

				randPriceString := utils.ConvertIntToString(randPrice)
				encodePriceByteValue, _ := utils.AESCBCPKCS5PaddingEncryptWithIV([]byte(randPriceString), []byte(eKey), []byte(iKey))
				priceMarcro = base64.StdEncoding.EncodeToString(encodePriceByteValue)
				priceMarcro = url.QueryEscape(priceMarcro)
			}
			tmpItem = strings.Replace(tmpItem, "${AUCTION_PRICE}", priceMarcro, -1)

			respListItemImpArray = append(respListItemImpArray, tmpItem)
		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, clickItem := range item.ClickUrls {
			clkItem := clickItem
			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, clkItem)
		}
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlBaiQingTengJsonPriceFailedURLArray(baiqingtengLossNoticeURLArray, &bigdataExtra)
			continue
		}

		// 上报竞价失败, 无需回传竞价成功
		if len(baiqingtengLossNoticeURLArray) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, baiqingtengLossNoticeURLArray...)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = baiqingtengEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("baiqingteng resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// baiqingteng resp
	respGdt := models.MHUpResp{}
	respGdt.RespData = &mhResp
	respGdt.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respGdt
}

type baiqingtengJsonEcpmSort []BaiQingTengBidResponseSeatBidBidStu

func (s baiqingtengJsonEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s baiqingtengJsonEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s baiqingtengJsonEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Price > s[j].Price
}

// BaiQingTengBidRequestStu ...
type BaiQingTengBidRequestStu struct {
	MediaID int                            `json:"mediaId,omitempty"`
	ReqID   string                         `json:"reqId,omitempty"`
	Imp     []BaiQingTengBidRequestImpStu  `json:"imp,omitempty"`
	App     BaiQingTengBidRequestAppStu    `json:"app,omitempty"`
	Device  BaiQingTengBidRequestDeviceStu `json:"device,omitempty"`
}

type BaiQingTengBidRequestImpStu struct {
	ID         string                             `json:"id,omitempty"`
	AppID      string                             `json:"appId,omitempty"`
	TagID      string                             `json:"tagId,omitempty"`
	BidFloor   int                                `json:"bidFloor,omitempty"`
	Secure     int                                `json:"secure,omitempty"`
	MaxCount   int                                `json:"maxCount,omitempty"`
	AdType     int                                `json:"adType"`
	Assets     []BaiQingTengBidRequestImpAssetStu `json:"assets,omitempty"`
	ActionType []int                              `json:"actionType,omitempty"`
	Instl      int                                `json:"instl,omitempty"`
}

type BaiQingTengBidRequestImpAssetStu struct {
	TemplateId int `json:"templateId,omitempty"`
	Ratio      int `json:"ratio,omitempty"`
}

type BaiQingTengBidRequestAppStu struct {
	Bundle  string `json:"bundle,omitempty"`
	Version string `json:"version,omitempty"`
}

type BaiQingTengBidRequestDeviceStu struct {
	IP          string                               `json:"ip,omitempty"`
	IPV6        string                               `json:"ipv6,omitempty"`
	UA          string                               `json:"ua,omitempty"`
	W           int                                  `json:"w,omitempty"`
	H           int                                  `json:"h,omitempty"`
	Carrier     int                                  `json:"carrier,omitempty"`
	NetworkType int                                  `json:"networkType,omitempty"`
	DeviceType  int                                  `json:"deviceType,omitempty"`
	Os          int                                  `json:"os,omitempty"`
	Osv         string                               `json:"osv,omitempty"`
	Model       string                               `json:"model,omitempty"`
	Make        string                               `json:"make,omitempty"`
	UID         BaiQingTengBidRequestDeviceUIDStu    `json:"uid,omitempty"`
	Factor      BaiQingTengBidRequestDeviceFactorStu `json:"factor,omitempty"`
	Bstime      string                               `json:"bstime,omitempty"`
	Aftime      string                               `json:"aftime,omitempty"`
	Sftime      string                               `json:"sftime,omitempty"`
	Fbtime      string                               `json:"fbtime,omitempty"`
	Paid        string                               `json:"paid,omitempty"`
	AppList     []uint32                             `json:"appList,omitempty"`
}

type BaiQingTengBidRequestDeviceUIDStu struct {
	DIDMd5  string                                  `json:"didMd5,omitempty"`
	Oaid    string                                  `json:"oaid,omitempty"`
	IdfaMd5 string                                  `json:"idfaMd5,omitempty"`
	Caid    []BaiQingTengBidRequestDeviceUIDCaidStu `json:"caid,omitempty"`
}

type BaiQingTengBidRequestDeviceUIDCaidStu struct {
	ID      string `json:"id,omitempty"`
	Version string `json:"version,omitempty"`
}

type BaiQingTengBidRequestDeviceFactorStu struct {
	CaidFactor BaiQingTengBidRequestDeviceFactorCaidFactorStu `json:"caidFactor,omitempty"`
}

type BaiQingTengBidRequestDeviceFactorCaidFactorStu struct {
	Disk           string `json:"disk,omitempty"`
	Model          string `json:"model,omitempty"`
	PhysicalMemory string `json:"physicalMemory,omitempty"`
	SysFileTime    string `json:"sysFileTime,omitempty"`
	BootSecTime    string `json:"bootSecTime,omitempty"`
	SystemVersion  string `json:"systemVersion,omitempty"`
	Machine        string `json:"machine,omitempty"`
	DeviceName     string `json:"deviceName,omitempty"`
	Language       string `json:"language,omitempty"`
	TimeZone       string `json:"timeZone,omitempty"`
	CarrierInfo    string `json:"carrierInfo,omitempty"`
	CountryCode    string `json:"countryCode,omitempty"`
}

// BaiQingTengBidResponseStu ...
type BaiQingTengBidResponseStu struct {
	Nbr     int                                `json:"nbr,omitempty"`
	SeatBid []BaiQingTengBidResponseSeatBidStu `json:"seatBid,omitempty"`
}

type BaiQingTengBidResponseSeatBidStu struct {
	BID []BaiQingTengBidResponseSeatBidBidStu `json:"bid,omitempty"`
}

type BaiQingTengBidResponseSeatBidBidStu struct {
	ID            string                                            `json:"id,omitempty"`
	Crid          string                                            `json:"crid,omitempty"`
	Adm           BaiQingTengBidResponseSeatBidBidAdmStu            `json:"adm,omitempty"`
	Price         int                                               `json:"price,omitempty"`
	Lurl          []string                                          `json:"lurl,omitempty"`
	ShowUrls      []string                                          `json:"showUrl,omitempty"`
	ClickUrls     []string                                          `json:"clickUrl,omitempty"`
	TrackMonitors []BaiQingTengBidResponseSeatBidBidTrackMonitorStu `json:"mons,omitempty"`
}

type BaiQingTengBidResponseSeatBidBidTrackMonitorStu struct {
	Url       string `json:"url,omitempty"`
	EventType int    `json:"event,omitempty"`
}

type BaiQingTengBidResponseSeatBidBidAdmStu struct {
	Title        string                                        `json:"title,omitempty"`
	Desc         string                                        `json:"desc,omitempty"`
	Icon         string                                        `json:"icon,omitempty"`
	Images       []BaiQingTengBidResponseSeatBidBidAdmImageStu `json:"img,omitempty"`
	AppStoreLink string                                        `json:"appStoreLink,omitempty"`
	LanddingPage string                                        `json:"landdingPage,omitempty"`
	Deeplink     string                                        `json:"deeplink,omitempty"`
	AppName      string                                        `json:"appName,omitempty"`
	PackageName  string                                        `json:"packageName,omitempty"`
	Publisher    string                                        `json:"publisher,omitempty"`
	AppVersion   string                                        `json:"appVersion,omitempty"`
	Privacy      string                                        `json:"privacy,omitempty"`
	BundleId     string                                        `json:"bundleId,omitempty"`
	Permission   string                                        `json:"permission,omitempty"`
	Video        BaiQingTengBidResponseSeatBidBidAdmVideoStu   `json:"video,omitempty"`
	ActionType   int                                           `json:"actionType,omitempty"`
}

type BaiQingTengBidResponseSeatBidBidAdmImageStu struct {
	Url    string `json:"url,omitempty"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
}

type BaiQingTengBidResponseSeatBidBidAdmVideoStu struct {
	Url      string `json:"url,omitempty"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	Duration int    `json:"duration,omitempty"`
}

// curl price failed
func getBaiQingTengJsonPriceFailedURL(lossNoticeURs []string, platformPos *models.PlatformPosStu, baiqingtengEcpm int, bigdataExtra *models.MHUpRespExtra) []string {
	var baiqingtengLossNoticeURLArray []string
	if baiqingtengEcpm > 0 && len(lossNoticeURs) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {
		// 随机150-200%替换价格宏
		tmpRandValue := 150 + rand.Intn(51)
		randPrice := int(baiqingtengEcpm * tmpRandValue / 100)

		randPriceString := utils.ConvertIntToString(randPrice)
		encodePriceValue := randPriceString + "%23" + "%23" + "%23"
		for _, item := range lossNoticeURs {
			tmpWinNoticeURL := strings.Replace(string(item), "${AUCTION_WININFO}", encodePriceValue, -1)
			baiqingtengLossNoticeURLArray = append(baiqingtengLossNoticeURLArray, tmpWinNoticeURL)
		}
	}

	return baiqingtengLossNoticeURLArray
}

// curl price failed
func curlBaiQingTengJsonPriceFailedURLArray(lossNoticeURs []string, bigdataExtra *models.MHUpRespExtra) {
	if len(lossNoticeURs) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("baiqingteng win failed url panic:", err)
				}
			}()
			for _, item := range lossNoticeURs {
				curlBaiQingTengNurl(item)
			}
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}
