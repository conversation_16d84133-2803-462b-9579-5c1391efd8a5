package core

import (
	"fmt"
	"mh_proxy/models"
	"testing"

	"github.com/gin-gonic/gin"
)

func Test_GetFromMiMan(ct *testing.T) {
	c := gin.Context{}

	mhReqApp := models.MHReqApp{}
	mhReqDevice := models.MHReqDevice{
		DeviceType: 1,
		IP:         "***********",
		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
		OsVersion:  "1.14.0",
		ImeiMd5:    "F1C7976BC455CB548BFC550EB7687F06",
		Oaid:       "oaid",
		Os:         "android",
		Idfa:       "idfs_string",
	}

	mhReq := models.MHReq{
		App:    mhReqApp,
		Device: mhReqDevice,
	}

	localPosInfo := models.LocalPosStu{}

	platformPosInfo := models.PlatformPosStu{
		PlatformPosID:       "193288",
		PlatformAppIsActive: 1,
		PlatformPosIsActive: 1,
		PlatformAppUpURL:    "http://v.ad.xelements.cn/cx",
		PlatformAppName:     "wantu",
		PlatformAppBundle:   "com.weitu.wantu",
	}

	categoryInfo := models.CategoryStu{
		FloorPrice: 1000,
	}

	bigdataUID := "123456"

	respData := GetFromMiMan(&c, &mhReq, &localPosInfo, &platformPosInfo, categoryInfo, bigdataUID)

	fmt.Printf("\t\t%#v,\n", platformPosInfo)

	fmt.Printf("\t\t%#v,\n", respData)
}
