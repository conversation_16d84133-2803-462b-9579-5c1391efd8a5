package core

import (
	"context"
	"encoding/json"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"

	"mh_proxy/db"
	"mh_proxy/models"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta"
	rtacoremodels "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
)

func IsSSPExtraOK(c context.Context, platformPos *models.PlatformPosStu, localPos *models.LocalPosStu, mhReq *models.MHReq, item *models.MHRespDataItem) (bool, int) {
	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}

	appKey := "go_ssp_extra_app_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_extra_pos_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)

	if len(appValue) == 0 && len(posValue) == 0 {
		return true, 900000
	}

	var sspExtra models.SSPExtraBigCache

	if len(appValue) > 0 && len(posValue) == 0 {
		_ = json.Unmarshal(appValue, &sspExtra)
	}
	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &sspExtra)
	}

	platformIdFlag := 0
	platformIds := sspExtra.PlatformIds
	if len(platformIds) == 0 {
		return true, 900000
	}

	platformIdArray := strings.Split(platformIds, ",")
	for _, platformIdItem := range platformIdArray {
		if platformIdItem == tmpId {
			platformIdFlag = 1
			break
		}
	}

	if platformIdFlag == 0 {
		return true, 900000
	}

	// 价格拦截
	price := 0
	priceFlag := 1
	if len(sspExtra.PriceIntercept) > 0 {
		price, _ = strconv.Atoi(sspExtra.PriceIntercept)
	}
	if item.PEcpm < price {
		priceFlag = 0
	}

	adUrl := sspExtra.AdUrl
	extraKey := sspExtra.ExtraKey
	materialUrl := sspExtra.MaterialUrl
	packageName := sspExtra.PackageName
	expUrl := sspExtra.ExpUrl
	conditionType := sspExtra.ConditionType
	rtaId := sspExtra.RtaId
	extraType := sspExtra.ExtraType
	dealId := sspExtra.MgDealId

	otherFlag := 0
	expFlag := 0

	// 监测链接
	if conditionType == 1 && len(expUrl) > 0 {
		expUrlArray := strings.Split(expUrl, ",")
		for _, impressionUrl := range item.ImpressionLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(impressionUrl, expUrlItem) {
					expFlag = 1
					break
				}
			}
		}

		for _, clickUrl := range item.ClickLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(clickUrl, expUrlItem) {
					expFlag = 1
					break
				}
			}
		}
	} else {
		expFlag = 1
	}

	if len(adUrl) == 0 && len(extraKey) == 0 && len(materialUrl) == 0 && len(packageName) == 0 {
		otherFlag = 1
	}

	if len(adUrl) > 0 && otherFlag == 0 {
		adUrlArray := strings.Split(adUrl, ",")
		for _, adUrlItem := range adUrlArray {
			if len(item.LandpageURL) > 0 {
				if strings.Contains(item.LandpageURL, adUrlItem) {
					otherFlag = 1
				}
			}
			if len(item.DownloadURL) > 0 {
				if strings.Contains(item.DownloadURL, adUrlItem) {
					otherFlag = 1
				}
			}
			if len(item.DeepLink) > 0 {
				if strings.Contains(item.DeepLink, adUrlItem) {
					otherFlag = 1
				}
			}
			if len(item.AdURL) > 0 {
				if strings.Contains(item.AdURL, adUrlItem) {
					otherFlag = 1
				}
			}
		}
	}

	if len(extraKey) > 0 && otherFlag == 0 {
		extraKeyArray := strings.Split(extraKey, ",")
		for _, extraItem := range extraKeyArray {
			if len(item.Title) > 0 {
				if strings.Contains(item.Title, extraItem) {
					otherFlag = 1
				}
			}
			if len(item.Description) > 0 {
				if strings.Contains(item.Description, extraItem) {
					otherFlag = 1
				}
			}
		}
	}

	if len(materialUrl) > 0 && otherFlag == 0 {
		materialUrlArray := strings.Split(materialUrl, ",")
		for _, materialUrlItem := range materialUrlArray {
			if len(item.Image) > 0 {
				if strings.Contains(item.Image[0].URL, materialUrlItem) {
					otherFlag = 1
				}
			}
			if item.Video != nil && len(item.Video.VideoURL) > 0 {
				if strings.Contains(item.Video.VideoURL, materialUrlItem) {
					otherFlag = 1
				}
				if strings.Contains(item.Video.CoverURL, materialUrlItem) {
					otherFlag = 1
				}
			}

			if len(item.IconURL) > 0 {
				if strings.Contains(item.IconURL, materialUrlItem) {
					otherFlag = 1
				}
			}
		}
	}

	if len(packageName) > 0 && otherFlag == 0 {
		packageNameArray := strings.Split(packageName, ",")
		for _, packageNameItem := range packageNameArray {
			if strings.Contains(item.PackageName, packageNameItem) {
				otherFlag = 1
			}
		}
	}

	mgEvent := 0

	if expFlag == 1 && otherFlag == 1 {
		if extraType == 2 && len(rtaId) > 0 {
			deviceInfo := rtacoremodels.MHDeviceStu{
				Os:   mhReq.Device.Os,
				Osv:  mhReq.Device.OsVersion,
				Ua:   mhReq.Device.Ua,
				IP:   mhReq.Device.IP,
				Idfa: mhReq.Device.Idfa,
				Oaid: mhReq.Device.Oaid,
			}
			rtaOk, _ := rta.IsRTAOK(c, db.GlbRedis, db.GlbMySQLDb, db.GlbBigCacheMinute, rtaId, &deviceInfo)
			if !rtaOk {
				return false, 900111
			}
		} else if extraType == 1 {
			hour := time.Now().Format("15")
			result := strings.TrimLeft(hour, "0")
			hourNum, _ := strconv.Atoi(result)

			expWeight := sspExtra.ExpWeight

			if rand.Intn(100) < expWeight {
				if len(sspExtra.GroupList) > 0 {
					for _, groupItem := range sspExtra.GroupList {
						if groupItem.TimePeriod == "0" {
							if groupItem.ExpCount > 0 {
								mgEvent = 1
								break
							}
						} else {
							timeArray := strings.Split(groupItem.TimePeriod, "-")
							timeSNum, _ := strconv.Atoi(timeArray[0])
							timeENum, _ := strconv.Atoi(timeArray[1])
							if timeSNum == hourNum && hourNum < timeENum {
								if groupItem.ExpCount > 0 {
									mgEvent = 1
									break
								}
							}
						}
					}
					if len(sspExtra.ClickRate) > 0 {
						clickRate, _ := strconv.ParseFloat(sspExtra.ClickRate, 64)
						randomNum := rand.Intn(100)
						if float64(randomNum) < clickRate {
							mgEvent = 2
						}
					}
				}
			}
		}
	}

	if len(dealId) > 0 && mgEvent > 0 {
		if mgEvent == 2 && priceFlag == 1 {
			var clickLink []string
			for _, clickUrl := range item.ClickLink {
				if strings.Contains(clickUrl, "maplehaze.cn") {
					params := url.Values{}
					params.Set("mg_deal_id", dealId)
					if strings.Contains(clickUrl, "?") {
						clickUrl = clickUrl + "&" + params.Encode()
					} else {
						clickUrl = clickUrl + "?" + params.Encode()
					}
				}
				clickLink = append(clickLink, clickUrl)
			}
			item.ClickLink = clickLink
			item.MGAdEvent = mgEvent
		}
		if mgEvent >= 1 && priceFlag == 1 {
			var impressionLink []string
			for _, impressionUrl := range item.ImpressionLink {
				if strings.Contains(impressionUrl, "maplehaze.cn") {
					params := url.Values{}
					params.Set("mg_deal_id", dealId)
					if strings.Contains(impressionUrl, "?") {
						impressionUrl = impressionUrl + "&" + params.Encode()
					} else {
						impressionUrl = impressionUrl + "?" + params.Encode()
					}
				}
				impressionLink = append(impressionLink, impressionUrl)
			}
			item.ImpressionLink = impressionLink
			item.MGAdEvent = mgEvent
		}
	}

	return true, 900000
}
