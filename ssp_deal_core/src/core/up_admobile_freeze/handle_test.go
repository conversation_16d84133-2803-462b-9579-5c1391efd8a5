package up_admobile_freeze

import (
	"encoding/base64"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
)

// func Test_GetFromADmobileAppClosed(ct *testing.T) {
// 	db.InitRedis()

// 	c := gin.Context{}

// 	mhReqApp := models.MHReqApp{}
// 	mhReqDevice := models.MHReqDevice{
// 		DeviceType: 1,
// 		IP:         "***********",
// 		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
// 		OsVersion:  "1.14.0",
// 		ImeiMd5:    "F1C7976BC455CB548BFC550EB7687F06",
// 		Oaid:       "oaid",
// 		Os:         "android",
// 		Idfa:       "idfs_string",
// 	}

// 	mhReq := models.MHReq{
// 		App:    mhReqApp,
// 		Device: mhReqDevice,
// 	}

// 	localPosInfo := models.LocalPosStu{}

// 	platformPosInfo := models.PlatformPosStu{
// 		PlatformPosID:       "1001372",
// 		PlatformAppIsActive: 0,
// 		PlatformPosIsActive: 1,
// 		PlatformAppUpURL:    "http://ad.tianmu.mobi/ad",
// 		PlatformAppName:     "wantu",
// 		PlatformAppBundle:   "com.weitu.wantu",
// 	}

// 	categoryInfo := models.CategoryStu{
// 		FloorPrice: 1000,
// 	}

// 	bigdataUID := "123456"

// 	respData := GetFromADmobile(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

// 	fmt.Printf("\t\t%#v,\n", platformPosInfo)

// 	fmt.Printf("\t\t%#v,\n", respData)
// }

// func Test_GetFromADmobilePosClosed(ct *testing.T) {
// 	db.InitRedis()

// 	c := gin.Context{}

// 	mhReqApp := models.MHReqApp{}
// 	mhReqDevice := models.MHReqDevice{
// 		DeviceType: 1,
// 		IP:         "***********",
// 		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
// 		OsVersion:  "1.14.0",
// 		ImeiMd5:    "F1C7976BC455CB548BFC550EB7687F06",
// 		Oaid:       "oaid",
// 		Os:         "android",
// 		Idfa:       "idfs_string",
// 	}

// 	mhReq := models.MHReq{
// 		App:    mhReqApp,
// 		Device: mhReqDevice,
// 	}

// 	localPosInfo := models.LocalPosStu{}

// 	platformPosInfo := models.PlatformPosStu{
// 		PlatformPosID:       "193288",
// 		PlatformAppIsActive: 1,
// 		PlatformPosIsActive: 0,
// 		PlatformAppUpURL:    "http://ad.tianmu.mobi/ad",
// 		PlatformAppName:     "wantu",
// 		PlatformAppBundle:   "com.weitu.wantu",
// 	}

// 	categoryInfo := models.CategoryStu{
// 		FloorPrice: 1000,
// 	}

// 	bigdataUID := "123456"

// 	respData := GetFromADmobile(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

// 	fmt.Printf("\t\t%#v,\n", platformPosInfo)

// 	fmt.Printf("\t\t%#v,\n", respData)
// }

func Test_GetFromADmobile(ct *testing.T) {
	db.InitRedis()
	db.InitBigCache()
	db.InitMysql()

	c := gin.Context{}

	mhReqApp := models.MHReqApp{}
	mhReqDevice := models.MHReqDevice{
		DeviceType: 1,
		IP:         "***********",
		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
		OsVersion:  "1.14.0",
		Imei:       "865166020069038",
		Oaid:       "2c06c2722e300206e4c6c79f07c7d2efd7ec74b82daa67e1019b1efd92a2b488",
		Os:         "android",
		Idfa:       "idfs_string",
	}

	mhReq := models.MHReq{
		App:    mhReqApp,
		Device: mhReqDevice,
	}

	localPosInfo := models.LocalPosStu{
		LocalPosID:                       "57650",
		LocalAppID:                       "10387",
		LocalPosWidth:                    1280,
		LocalPosHeight:                   720,
		LocalPosType:                     4,
		LocalOs:                          "0",
		LocalAppName:                     "七读免费小说API-Android",
		LocalAppBundleID:                 "com.dj.sevenRead",
		LocalAppType:                     "4",
		LocalAppSplashClickRegion:        0,
		LocalAppNativeDownloadCompliance: 0,
		LocalPosIsActive:                 1,
		LocalPosMHLandPage:               0,
		LocalPosTimeOut:                  260,
		LocalPosMaterialType:             0,
		LocalAppSupportExtraClipboard:    0,
		LocalAppSupportExtraNotification: 0,
		LocalAppSupportExtraWakeUp:       0,
		LocalAppIsExtraLimitFrequency:    1,
		LocalAppExtraLimitFrequency:      "60",
	}

	platformPosInfo := models.PlatformPosStu{
		// PlatformAppID:             "1001379",
		// PlatformPosID:             "03cf29746ab1",
		// PlatformAppVersion:        "1.0",
		// PlatformAppIsActive:       1,
		// PlatformPosIsActive:       1,
		// PlatformAppUpURL:          "http://ad.tianmu.mobi/ad",
		// PlatformAppName:           "maplehaze",
		// PlatformAppBundle:         "com.maplehaze",
		// PlatformAppIsPriceEncrypt: 1,
		// PlatformAppPriceEncrypt:   "0276af914fee82a4",
		// PlatformPosType:           2,
		// PlatformPosWidth:          1080,
		// PlatformPosHeight:         1920,

		PlatformPosID:               "1e8af79250cb",
		PlatformAppID:               "1001379",
		PlatformAppCorpID:           1,
		PlatformMediaID:             "37",
		PlatformAppBundle:           "com.maplehaze",
		PlatformPosStyleIDs:         "",
		PlatformOs:                  "0",
		PlatformPosWidth:            1280,
		PlatformPosHeight:           720,
		PlatformPosType:             4,
		PlatformPosMaterialType:     2,
		PlatformPosIsDebug:          0,
		PlatformPosIsReplaceXY:      0,
		PlatformPosDirection:        0,
		PlatformAppVersion:          "1.0",
		PlatformAppUpURL:            "http://ad.tianmu.mobi/ad",
		PlatformAppFilterUa:         0,
		PlatformAppIsActive:         1,
		PlatformPosIsActive:         1,
		PlatformAppIsPriceEncrypt:   1,
		PlatformAppPriceEncrypt:     "0276af914fee82a4",
		PlatformAppIsReplaceDID:     0,
		PlatformAppIsReplaceDIDUa:   1,
		PlatformAppMaxDAUType:       0,
		PlatformAppMaxDAU:           0,
		PlatformAppIsDeepLinkFailed: 0,
	}

	categoryInfo := models.CategoryStu{
		LocalPosID:              "57650",
		LocalAppID:              "10387",
		PlatformPosID:           "1da7e2b8045c",
		PlatformAppID:           "1001379",
		PlatformMediaID:         "37",
		PlatformPosType:         4,
		PlatformPosMaterialType: 2,
		// Label:                   1,
		// Priority:                1,
		// SubPriority:             7,
		// Status:                  1,
		// SubStatus:               1,
		// Sub1Status:              1,
		// APIWeight:               65,
		// APISubWeight:            100,
		// MaxReqNum:               -1,
		// MaxExpNum:               -1,
		// MaxClkNum:               -1,
		FloorPrice:   20,
		FinalPrice:   50,
		WhiteList:    "",
		BlackList:    "",
		WhiteVersion: "",
		BlackVersion: "",
	}

	bigdataUID := "123456"

	respData := GetFromADmobile(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

	fmt.Printf("\t\t%#v,\n", platformPosInfo)
	fmt.Printf("\t\t%#v,\n", respData)
}

func Test_EncryptPrice(ct *testing.T) {
	appKey := "0276af914fee82a4"
	priceString := "99"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := base64.StdEncoding.EncodeToString(finalPrice)
	urlEncode := url.QueryEscape(finalString)
	fmt.Println("Test_EncryptPrice finalPrice: ", priceString, ":", finalString, ":", urlEncode)

}

func Test_DecryptPrice(ct *testing.T) {
	appKey := "0276af914fee82a4"
	s := "SMbvQwvZoyzKu6nh//Rb2w=="
	s1, _ := base64.StdEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptPrice finalPrice: ", s, ":", string(finalPrice))
}
