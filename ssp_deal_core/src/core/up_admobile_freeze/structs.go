package up_admobile_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
)

// ADmobileRequestObject Objects

type ADmobileRequestObject struct {
	App        *ADmobileRequestAppObject        `json:"app"`
	AdPosition *ADmobileRequestAdPositionObject `json:"adPosition"`
	Device     *ADmobileRequestDeviceObject     `json:"device"`
	BidFloor   int                              `json:"bidFloor"`
}

func (r *ADmobileRequestObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileRequestAppObject struct {
	AppId       int64  `json:"appId"`
	PackageName string `json:"packageName"`
	AppVersion  string `json:"appVersion"`
	Category    string `json:"category,omitempty"`
}

func (r *ADmobileRequestAppObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileRequestAdPositionObject struct {
	AdPositionId string `json:"adPositionId"`
	AdWidth      int    `json:"adWidth"`
	AdHeight     int    `json:"adHeight"`
}

func (r *ADmobileRequestAdPositionObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileOSEnum int

const (
	MH_UP_ADMOBILE_OS_ANDROID ADmobileOSEnum = 1
	MH_UP_ADMOBILE_OS_IOS     ADmobileOSEnum = 2
)

func NewADmobileOS(os up_common.UpCommonOSEnum) ADmobileOSEnum {
	switch os {
	case up_common.MH_UP_COMMON_OS_ANDROID:
		return MH_UP_ADMOBILE_OS_ANDROID
	case up_common.MH_UP_COMMON_OS_IOS:
		return MH_UP_ADMOBILE_OS_IOS
	}

	return MH_UP_ADMOBILE_OS_ANDROID
}

type ADmobileDevictTypeEnum string

const (
	MH_UP_ADMOBILE_DEVICE_TYPE_PHONE   ADmobileDevictTypeEnum = "PHONE"
	MH_UP_ADMOBILE_DEVICE_TYPE_PAD     ADmobileDevictTypeEnum = "PAD"
	MH_UP_ADMOBILE_DEVICE_TYPE_UNKNOWN ADmobileDevictTypeEnum = "OTHER"
)

func NewADmobileDeviceType(deviceType up_common.UpCommonDeviceTypeEnum) ADmobileDevictTypeEnum {
	switch deviceType {
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PAD:
		return MH_UP_ADMOBILE_DEVICE_TYPE_PAD
	case up_common.MH_UP_COMMON_DEVICE_TYPE_PHONE:
		return MH_UP_ADMOBILE_DEVICE_TYPE_PHONE
	}

	return MH_UP_ADMOBILE_DEVICE_TYPE_UNKNOWN
}

type ADmobileRequestDeviceTypeObject struct {
	DeviceType ADmobileDevictTypeEnum `json:"deviceType"`
}

func (t *ADmobileRequestDeviceTypeObject) String() string {
	return string(t.DeviceType)
}

type ADmobileOrientationEnum string

const (
	MH_UP_ADMOBILE_ORIENTATION_PORTRAIT  ADmobileOrientationEnum = "PORTRAIT"
	MH_UP_ADMOBILE_ORIENTATION_LANDSCAPE ADmobileOrientationEnum = "LANDSCAPE"
)

func NewADmobileOrientation(screenDirection up_common.UpCommonScreenDirection) ADmobileOrientationEnum {
	switch screenDirection {
	case up_common.MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE:
		return MH_UP_ADMOBILE_ORIENTATION_LANDSCAPE
	case up_common.MH_UP_COMMON_SCREENDIRECTION_PORTRAIT:
		return MH_UP_ADMOBILE_ORIENTATION_PORTRAIT
	}

	return MH_UP_ADMOBILE_ORIENTATION_PORTRAIT
}

type ADmobileRequestOrientationObject struct {
	Orientation ADmobileOrientationEnum `json:"orientation"`
}

func (t *ADmobileRequestOrientationObject) String() string {
	return string(t.Orientation)
}

type ADmobileNetworkEnum string

const (
	MH_UP_ADMOBILE_NETWORK_UNKNOWN ADmobileNetworkEnum = "UNKNOWN"
	MH_UP_ADMOBILE_NETWORK_2G      ADmobileNetworkEnum = "NET_2G"
	MH_UP_ADMOBILE_NETWORK_3G      ADmobileNetworkEnum = "NET_3G"
	MH_UP_ADMOBILE_NETWORK_4G      ADmobileNetworkEnum = "NET_4G"
	MH_UP_ADMOBILE_NETWORK_5G      ADmobileNetworkEnum = "NET_5G"
	MH_UP_ADMOBILE_NETWORK_WIFI    ADmobileNetworkEnum = "WIFI"
)

type ADmobileRequestNetworkObject struct {
	Network ADmobileNetworkEnum `json:"network"`
}

func (t *ADmobileRequestNetworkObject) String() string {
	return string(t.Network)
}

type ADmobileBatteryStatusEnum string

const (
	MH_UP_ADMOBILE_BATTERYPOWER_UNKNOWN   ADmobileBatteryStatusEnum = "UNKNOWN"
	MH_UP_ADMOBILE_BATTERYPOWER_UNPLUGGED ADmobileBatteryStatusEnum = "UNPLUGGED"
	MH_UP_ADMOBILE_BATTERYPOWER_CHARGING  ADmobileBatteryStatusEnum = "CHARGING"
)

type ADmobileRequestBatteryStatusObject struct {
	BatteryStatus ADmobileBatteryStatusEnum `json:"batteryStatus"`
}

func (t *ADmobileRequestBatteryStatusObject) String() string {
	return string(t.BatteryStatus)
}

type ADmobileRequestDeviceObject struct {
	Os              ADmobileOSEnum                      `json:"os"`
	Ip              string                              `json:"ip"`
	IpV6            string                              `json:"ip_v6"`
	UserAgent       string                              `json:"userAgent"`
	OsVersion       string                              `json:"osVersion,omitempty"`
	Idfa            string                              `json:"idfa,omitempty"`
	Idfv            string                              `json:"idfv,omitempty"`
	AndroidId       string                              `json:"androidId,omitempty"`
	Oaid            string                              `json:"oaid,omitempty"`
	Imei            string                              `json:"imei,omitempty"`
	Mac             string                              `json:"mac,omitempty"`
	Network         *ADmobileRequestNetworkObject       `json:"network,omitempty"`
	Vendor          string                              `json:"vendor,omitempty"`
	ModelNo         string                              `json:"modelNo,omitempty"`
	Longitude       float32                             `json:"longitude,omitempty"`
	Latitude        float32                             `json:"Latitude,omitempty"`
	ScreenWidth     int                                 `json:"screenWidth,omitempty"`
	ScreenHeight    int                                 `json:"screenHeight,omitempty"`
	Ppi             int                                 `json:"ppi,omitempty"`
	Inch            float32                             `json:"inch,omitempty"`
	DeviceType      *ADmobileRequestDeviceTypeObject    `json:"deviceType,omitempty"`
	Orientation     *ADmobileRequestOrientationObject   `json:"orientation,omitempty"`
	Imsi            string                              `json:"imsi,omitempty"`
	PhoneName       string                              `json:"phoneName,omitempty"`
	BatteryStatus   *ADmobileRequestBatteryStatusObject `json:"batteryStatus,omitempty"`
	BatteryPower    int                                 `json:"batteryPower,omitempty"`
	DiskSize        int                                 `json:"diskSize,omitempty"`
	MemorySize      int                                 `json:"memorySize,omitempty"`
	CpuNumber       int                                 `json:"cpuNumber,omitempty"`
	CpuFrequency    float32                             `json:"cpuFrequency,omitempty"`
	OsBootMark      string                              `json:"osBootMark,omitempty"`
	OsUpdateMark    string                              `json:"osUpdateMark,omitempty"`
	AndroidApiLevel int                                 `json:"androidApiLevel,omitempty"`
	OsBootTime      int64                               `json:"osBootTime,omitempty"`
	OsUpdateTime    int64                               `json:"osUpdateTime,omitempty"`
	OsElapseTime    int64                               `json:"osElapseTime,omitempty"`
	Caid            string                              `json:"caid,omitempty"`
	Vaid            string                              `json:"vaid,omitempty"`
	Language        string                              `json:"language,omitempty"`
	TimeZone        string                              `json:"timeZone,omitempty"`
	StoreVersion    string                              `json:"storeVersion,omitempty"`
	HmsVersion      string                              `json:"hmsVersion,omitempty"`
	HarmonyOsVer    string                              `json:"harmonyOsVer,omitempty"`
	OsUiVersion     string                              `json:"osUiVersion,omitempty"`
	InstallApps     []string                            `json:"installApps,omitempty"`
}

func (r *ADmobileRequestDeviceObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileResponseObject struct {
	Code    int                       `json:"code"`
	Message string                    `json:"message"`
	Data    *ADmobileResponseAdObject `json:"data,omitempty"`
}

func (r *ADmobileResponseObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileResponseAdObject struct {
	AdType           string                              `json:"adType"`
	MaterialType     int                                 `json:"materialType"`
	Action           int                                 `json:"action"`
	Title            string                              `json:"title,omitempty"`
	Desc             string                              `json:"desc,omitempty"`
	ImageUrl         string                              `json:"imageUrl,omitempty"`
	ImageUrlList     []string                            `json:"imageUrlList,omitempty"`
	Video            *ADmobileResponseVideoObject        `json:"video,omitempty"`
	DeeplinkUrl      string                              `json:"deeplinkUrl,omitempty"`
	LandingPageUrl   string                              `json:"landingPageUrl,omitempty"`
	AppPromotion     *ADmobileResponseAppPromotionObject `json:"appPromotion,omitempty"`
	Tracker          *ADmobileResponseTrackerObject      `json:"tracker"`
	BidPrice         int                                 `json:"bidPrice,omitempty"`
	WinNoticeUrl     string                              `json:"winNoticeUrl,omitempty"`
	AdSource         string                              `json:"adSource,omitempty"`
	InteractStyle    int                                 `json:"interactStyle,omitempty"`
	InteractSubStyle int                                 `json:"interactSubStyle,omitempty"`
}

func (r *ADmobileResponseAdObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileResponseVideoObject struct {
	Url           string `json:"url"`
	Duration      int    `json:"duration,omitempty"`
	ForceDuration int    `json:"forceDuration,omitempty"`
}

func (r *ADmobileResponseVideoObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileResponseAppPromotionObject struct {
	AppName           string `json:"appName,omitempty"`
	AppVersion        string `json:"appVersion,omitempty"`
	AppBundle         string `json:"appBundle,omitempty"`
	AppIconUrl        string `json:"appIconUrl,omitempty"`
	AdvertiserName    string `json:"advertiserName,omitempty"`
	PrivacyPolicyUrl  string `json:"privacyPolicyUrl,omitempty"`
	PrivacyPolicyInfo string `json:"privacyPolicyInfo,omitempty"`
	PrivacyAuthUrl    string `json:"privacyAuthUrl,omitempty"`
}

func (r *ADmobileResponseAppPromotionObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobileResponseTrackerObject struct {
	Display             []string `json:"display"`
	Click               []string `json:"click"`
	Deeplink            []string `json:"deeplink,omitempty"`
	DownloadStart       []string `json:"downloadStart,omitempty"`
	DownloadEnd         []string `json:"downloadEnd,omitempty"`
	InstallStart        []string `json:"installStart,omitempty"`
	InstallEnd          []string `json:"installEnd,omitempty"`
	Open                []string `json:"open,omitempty"`
	VideoLoaded         []string `json:"videoLoaded,omitempty"`
	VideoError          []string `json:"videoError,omitempty"`
	VideoStart          []string `json:"videoStart,omitempty"`
	VideoQuarter        []string `json:"videoQuarter,omitempty"`
	VideoMiddle         []string `json:"videoMiddle,omitempty"`
	VideoThirdQuarter   []string `json:"videoThirdQuarter,omitempty"`
	VideoEnd            []string `json:"videoEnd,omitempty"`
	VideoPause          []string `json:"videoPause,omitempty"`
	VideoResume         []string `json:"videoResume,omitempty"`
	VideoSkip           []string `json:"videoSkip,omitempty"`
	VideoMute           []string `json:"videoMute,omitempty"`
	VideoUnmute         []string `json:"videoUnmute,omitempty"`
	VideoReplay         []string `json:"videoReplay,omitempty"`
	VideoClose          []string `json:"videoClose,omitempty"`
	VideoFullScreen     []string `json:"videoFullScreen,omitempty"`
	VideoExitFullScreen []string `json:"videoExitFullScreen,omitempty"`
	RewardSuccess       []string `json:"rewardSuccess,omitempty"`
}

func (r *ADmobileResponseTrackerObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type ADmobilePipline struct {
	Common *up_common.UpCommonPipline

	Request  *ADmobileRequestObject
	Response *ADmobileResponseObject
}

func (r *ADmobilePipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
