package up_admobile_freeze

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &ADmobilePipline{Common: common}
}

/**
* Private Methods
**/

func (p *ADmobilePipline) replaceLinkString(link string, price int) string {
	linkString := link
	linkString = strings.Replace(linkString, "__TM_DOWN_X__", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "__TM_DOWN_Y__", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "__TM_UP_X__", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "__TM_UP_Y__", "__UP_Y__", -1)

	if p.Common.IsLogicPixel {
		linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
		linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	} else {
		linkString = strings.Replace(linkString, "__TM_WIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
		linkString = strings.Replace(linkString, "__TM_HEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	}

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "__DOWN_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__DOWN_Y__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_Y__", "-999", -1)
	}

	if p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
		encryptPrice := p.encryptPrice(utils.ConvertIntToString(price))
		if len(encryptPrice) > 0 {

			if strings.Contains(linkString, "__TM_BID_PRICE__") {
				fmt.Println("ADMOBILEDEBUG: linkString:", linkString, "__TM_BID_PRICE__:", encryptPrice, price)
			}

			linkString = strings.Replace(linkString, "__TM_BID_PRICE__", encryptPrice, -1)
		} else {
			fmt.Println("encryptPrice is empty")
		}
	}

	return linkString
}

func (p *ADmobilePipline) encryptKey() string {
	return p.Common.PlatformPos.PlatformAppPriceEncrypt
}

func (p *ADmobilePipline) encryptPrice(price string) string {
	encryptKey := p.encryptKey()

	if len(encryptKey) == 0 {
		fmt.Println("price: ", price, "PlatformAppPriceEncrypt: ", p.Common.PlatformPos.PlatformAppPriceEncrypt)
		fmt.Printf("PlatformPos: %+v", p.Common.PlatformPos)
		return ""
	}
	result := base64.StdEncoding.EncodeToString(utils.AesCBCPKCS5Encrypt(price, p.encryptKey()))

	return url.QueryEscape(result)
}

// func (p *ADmobilePipline) decryptPrice(encryptedPrice string) (string, error) {
// 	var priceString string
// 	decodeString, err := base64.StdEncoding.DecodeString(encryptedPrice)
// 	if err != nil {
// 		return priceString, err
// 	}
// 	priceString = string(utils.AesCBCPKCS5Decrypt(decodeString, []byte(p.encryptKey())))
// 	return priceString, nil
// }

/**
 * * 函数式方法模板
 */
func (p *ADmobilePipline) actionTemplate() *ADmobilePipline {

	return p
}

/**
* Public Methods
**/

func (p *ADmobilePipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("ADMOBILEDEBUG: GetFromADmobile error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *ADmobilePipline) SetupRequest() up_common.PiplineInterface {

	// Setup ADmobileRequestObject
	requestObject := ADmobileRequestObject{}
	{
		// Setup ADmobileRequestAppObject
		requestAppObject := ADmobileRequestAppObject{}
		{
			requestAppObject.AppId = int64(utils.ConvertStringToInt(p.Common.PlatformPos.PlatformAppID))
			requestAppObject.PackageName = p.Common.PlatformPos.PlatformAppBundle
			requestAppObject.AppVersion = p.Common.PlatformPos.PlatformAppVersion
			// requestAppObject.Category = "" // 非必填字段，跟产品确认 不填

		}

		// Setup ADmobileRequestAdPositionObject
		requestAdPositionObject := ADmobileRequestAdPositionObject{}
		{
			requestAdPositionObject.AdPositionId = p.Common.PlatformPos.PlatformPosID
			requestAdPositionObject.AdWidth = p.Common.PlatformPos.PlatformPosWidth
			requestAdPositionObject.AdHeight = p.Common.PlatformPos.PlatformPosHeight
		}

		// Setup ADmobileRequestDeviceObject
		requestAdDiveceObject := ADmobileRequestDeviceObject{}
		{
			requestAdDiveceObject.Ip = p.Common.MhReq.Device.IP
			requestAdDiveceObject.IpV6 = "" // 与产品确认 填空
			requestAdDiveceObject.UserAgent = p.Common.MhReq.Device.Ua
			requestAdDiveceObject.OsVersion = p.Common.MhReq.Device.OsVersion

			if p.Common.IsAndroid() {
				requestAdDiveceObject.Os = MH_UP_ADMOBILE_OS_ANDROID

				if p.Common.IsAndroidMajorLessThanTen() {
					if len(p.Common.MhReq.Device.Imei) > 0 {
						requestAdDiveceObject.Imei = p.Common.MhReq.Device.Imei
					} else {
						p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				} else {
					if len(p.Common.MhReq.Device.Oaid) > 0 {
						requestAdDiveceObject.Oaid = p.Common.MhReq.Device.Oaid
					} else {
						p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				}

			} else {
				requestAdDiveceObject.Os = MH_UP_ADMOBILE_OS_IOS

				if len(p.Common.MhReq.Device.Idfa) > 0 {
					requestAdDiveceObject.Idfa = p.Common.MhReq.Device.Idfa
				}

			}
		}

		requestObject.App = &requestAppObject
		requestObject.AdPosition = &requestAdPositionObject
		requestObject.Device = &requestAdDiveceObject
		requestObject.BidFloor = p.Common.CategoryInfo.FloorPrice
	}
	p.Request = &requestObject

	return p
}

func (p *ADmobilePipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.Device.Os = NewADmobileOS(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Device.OsVersion = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Device.ModelNo = p.Common.ReplacedValues.Model
	}

	p.Request.Device.DeviceType = &ADmobileRequestDeviceTypeObject{DeviceType: NewADmobileDeviceType(p.Common.ReplacedValues.DeviceType)}

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Device.Vendor = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		p.Request.Device.Imei = p.Common.ReplacedValues.Imei
	}

	// if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
	// 	p.Request.Device.ImeiMd5 = p.Common.ReplacedValues.ImeiMd5
	// }

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		p.Request.Device.Oaid = p.Common.ReplacedValues.Oaid
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		p.Request.Device.AndroidId = p.Common.ReplacedValues.AndroidId
	}

	// if len(p.Common.ReplacedValues.AndroidIdMd5) > 0 {
	// 	p.Request.Device.AndroidIdMd5 = p.Common.ReplacedValues.AndroidIdMd5
	// }

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Device.UserAgent = p.Common.ReplacedValues.Ua
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		p.Request.Device.Idfa = p.Common.ReplacedValues.Idfa
	}

	p.Request.Device.Orientation = &ADmobileRequestOrientationObject{Orientation: NewADmobileOrientation(p.Common.ReplacedValues.ScreenDirection)}

	return p
}

func (p *ADmobilePipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(jsonData))
	httpRequest.Header.Add("Content-Type", "application/json")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject ADmobileResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	fmt.Println("ADMOBILEDEBUG: bodyContent", string(bodyContent))

	p.Response = &responseObject

	if p.Response.Data == nil {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900103,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *ADmobilePipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	for _, responseItem := range []ADmobileResponseAdObject{*p.Response.Data} {
		bidPrice := responseItem.BidPrice

		p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
		p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

		// 成功时的价格比例
		randPriceRatio := p.Common.RandPriceRatio()
		winPrice := int(bidPrice * randPriceRatio / 100)

		// 失败时的价格比例
		// randFailedPriceRatio := p.Common.RandFailedPriceRatio()

		if p.Common.CategoryInfo.FinalPrice > bidPrice {
			p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

			// 如果有竞败链接，需要回调处理
			continue
		}

		var commonAPIResponseObject up_common.UpCommonAPIResponseObject

		if len(responseItem.Title) > 0 {
			commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Title)
			commonAPIResponseObject.Title = responseItem.Title
		}

		if len(responseItem.Desc) > 0 {
			commonAPIResponseObject.Description = responseItem.Desc
		}

		if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.AppBundle) > 0 {
			commonAPIResponseObject.PackageName = responseItem.AppPromotion.AppBundle
		}

		if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_ANDROID.String() {
			commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE

			if len(responseItem.LandingPageUrl) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.LandingPageUrl
				commonAPIResponseObject.AdUrl = responseItem.LandingPageUrl
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.AdvertiserName) > 0 {
				commonAPIResponseObject.Publisher = responseItem.AppPromotion.AdvertiserName
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.AppName) > 0 {
				commonAPIResponseObject.AppName = responseItem.AppPromotion.AppName
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.AppVersion) > 0 {
				commonAPIResponseObject.AppVersion = responseItem.AppPromotion.AppVersion
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyUrl) > 0 {
				commonAPIResponseObject.PrivacyUrl = responseItem.AppPromotion.PrivacyPolicyUrl
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyPolicyInfo) > 0 {
				commonAPIResponseObject.Permission = responseItem.AppPromotion.PrivacyPolicyInfo
			} else {
				commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			}

			if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.PrivacyAuthUrl) > 0 {
				commonAPIResponseObject.PermissionUrl = responseItem.AppPromotion.PrivacyAuthUrl
			}

			commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024

		} else if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_IOS.String() {
			if len(responseItem.LandingPageUrl) > 0 {
				if strings.Contains(responseItem.LandingPageUrl, "apple.com") {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
				} else {
					commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
				}

				commonAPIResponseObject.LandPageUrl = responseItem.LandingPageUrl
				commonAPIResponseObject.AdUrl = responseItem.LandingPageUrl
			}
		}

		commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

		commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

		var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

		if len(responseItem.DeeplinkUrl) > 0 {
			commonAPIResponseObject.DeepLink = responseItem.DeeplinkUrl

			var convTrack up_common.UpCommonAPIResponseConvTrackObject
			convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(
				p.Common.MhReq,
				p.Common.LocalPos,
				p.Common.PlatformPos,
				p.Common.UUID,
				p.Common.UUID,
				0,
				0,
				0,
				0)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			// 与产品确认 成功报Open 2022年10月28日
			if responseItem.Tracker != nil && len(responseItem.Tracker.Open) > 0 {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, responseItem.Tracker.Open...)
			}

			convTrack.ConvUrls = respListItemDeepLinkArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

			if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

				var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
				convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
				convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
			}
		}

		if responseItem.Tracker != nil && len(responseItem.Tracker.DownloadStart) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.Tracker.DownloadStart
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if responseItem.Tracker != nil && len(responseItem.Tracker.DownloadEnd) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.Tracker.DownloadEnd
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if responseItem.Tracker != nil && len(responseItem.Tracker.InstallEnd) > 0 {
			var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
			commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
			commonAPIResponseConvTrackObject.ConvUrls = responseItem.Tracker.InstallEnd
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
		}

		if len(commonAPIResponseConvTrackObjects) > 0 {
			commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
		}

		if responseItem.AppPromotion != nil && len(responseItem.AppPromotion.AppIconUrl) > 0 {
			commonAPIResponseObject.IconUrl = responseItem.AppPromotion.AppIconUrl
		} else {
			commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(responseItem.DeeplinkUrl)
		}

		if responseItem.Video != nil && len(responseItem.Video.Url) > 0 {
			// 视频
			var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
			if responseItem.Video.Duration > 0 {
				commonAPIResponseVideoObject.Duration = responseItem.Video.Duration * 1000
			}
			commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight

			commonAPIResponseVideoObject.VideoUrl = responseItem.Video.Url

			if len(responseItem.ImageUrlList) > 0 {
				commonAPIResponseVideoObject.CoverUrl = responseItem.ImageUrlList[0]
			} else if len(responseItem.ImageUrl) > 0 {
				commonAPIResponseVideoObject.CoverUrl = responseItem.ImageUrl
			} else {
				if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
						commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			commonAPIResponseVideoObject.EndcardType = 1
			commonAPIResponseVideoObject.EndcardRange = 1

			var commonAPIResponseVideoEventTrackObjects []*up_common.UpCommonAPIResponseVideoEventTrackObject

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoStart) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_START
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoStart
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoQuarter) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_FIRSTQUARTILE
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoQuarter
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoMiddle) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_MIDPOINT
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoMiddle
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoThirdQuarter) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_THIRDUARTILE
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoThirdQuarter
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoEnd) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_END
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoEnd
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoMute) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_MUTE
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoMute
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoSkip) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_SKIP
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoSkip
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if responseItem.Tracker != nil && len(responseItem.Tracker.VideoClose) > 0 {
				var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
				commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_CLOSE
				commonAPIResponseVideoEventTrackObject.EventTrackUrls = responseItem.Tracker.VideoClose
				commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
			}

			if len(commonAPIResponseVideoEventTrackObjects) > 0 {
				commonAPIResponseVideoObject.EventTracks = commonAPIResponseVideoEventTrackObjects
			}

			commonAPIResponseObject.Video = &commonAPIResponseVideoObject
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
		} else {
			// 图片
			var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

			if len(responseItem.ImageUrlList) > 0 {
				for _, imageUrl := range responseItem.ImageUrlList {
					var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
					commonAPIResponseImageObject.Url = imageUrl
					commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
					commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
					commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
				}
			} else if len(responseItem.ImageUrl) > 0 {
				var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
				commonAPIResponseImageObject.Url = responseItem.ImageUrl
				commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
				commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
				commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)

			}

			commonAPIResponseObject.Images = commonAPIResponseImageObjects
			commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

			if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
				p.Common.SetPanicStringAndCodes("images is empty",
					up_common.MH_UP_ERROR_CODE_900201,
					up_common.MH_UP_ERROR_CODE_102006)
			}
		}

		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			p.Common.CategoryInfo.FloorPrice,
			p.Common.CategoryInfo.FinalPrice,
			bidPrice,
			0)

		if responseItem.Tracker != nil && len(responseItem.Tracker.Display) > 0 {
			var impressionLinks []string

			p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

			impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

			for _, linkString := range responseItem.Tracker.Display {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				impressionLinks = append(impressionLinks, replacedLinkString)
			}

			commonAPIResponseObject.ImpressionLink = impressionLinks

		}

		if responseItem.Tracker != nil && len(responseItem.Tracker.Click) > 0 {
			var clickLinks []string

			p.Common.MhClickLink.AddBigDataParams(bigdataParams)

			clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

			for _, linkString := range responseItem.Tracker.Click {

				replacedLinkString := p.replaceLinkString(linkString, winPrice)

				clickLinks = append(clickLinks, replacedLinkString)
			}

			if len(responseItem.Tracker.Deeplink) > 0 {
				clickLinks = append(clickLinks, responseItem.Tracker.Deeplink...)
			}

			commonAPIResponseObject.ClickLink = clickLinks
		}

		if p.Common.IsReportToWin {
			if len(responseItem.WinNoticeUrl) > 0 && bidPrice > 0 && p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
				go func() {
					defer func() {
						if err := recover(); err != nil {
							fmt.Println("gdt win url panic:", err)
						}
					}()

					replacedLinkString := p.replaceLinkString(responseItem.WinNoticeUrl, winPrice)

					p.Common.RequestUrl(replacedLinkString)
				}()
			}
		}

		commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
		commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

		list = append(list, &commonAPIResponseObject)
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("ADMOBILEDEBUG: responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *ADmobilePipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
