package core

import (
	"encoding/json"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/models"
	"strconv"
)

func MaterialReplaceAdContent(localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, item *models.MHRespDataItem) {
	if item.IsSkipClick != 1 {
		return
	}

	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}

	appKey := "go_ssp_material_replace_ad_content_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_material_replace_ad_content_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(posValue) == 0 {
		return
	}

	var materialReplaceAdContent []models.MaterialReplaceAdContentModel
	if len(appValue) > 0 {
		_ = json.Unmarshal(appValue, &materialReplaceAdContent)
	}

	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &materialReplaceAdContent)
	}

	randNum := rand.Intn(100)

	for _, materialItem := range materialReplaceAdContent {
		if randNum > materialItem.Chance {
			continue
		}

		for _, groupItem := range materialItem.Group {
			groupRandNum := rand.Intn(100)
			chance := float64(groupItem.Chance) / float64(materialItem.GroupChance) * 100
			if groupRandNum > int(chance) {
				continue
			}
			if len(groupItem.DpUrl) > 0 || len(groupItem.DownloadUrl) > 0 || len(groupItem.H5Url) > 0 {
				item.DeepLink = ""
				item.LandpageURL = ""
				item.DownloadURL = ""
				if len(groupItem.DpUrl) > 0 {
					item.DeepLink = groupItem.DpUrl
				}
				if len(groupItem.DownloadUrl) > 0 {
					item.DownloadURL = groupItem.DownloadUrl
				}
				if len(groupItem.H5Url) > 0 {
					item.AdURL = groupItem.H5Url
					item.LandpageURL = groupItem.H5Url
				}
			}
			if len(groupItem.Title) > 0 {
				item.Title = groupItem.Title
			}
			if len(groupItem.Description) > 0 {
				item.Description = groupItem.Description
			}

			if len(groupItem.ImageUrl) > 0 {
				var mhRespImageArray []models.MHRespImage
				var mhRespImage models.MHRespImage
				mhRespImage.URL = groupItem.ImageUrl
				mhRespImage.Width = groupItem.Width
				mhRespImage.Height = groupItem.Height
				mhRespImageArray = append(mhRespImageArray, mhRespImage)
				if len(mhRespImageArray) > 0 {
					item.CrtType = 11
					item.Image = mhRespImageArray
					item.Video = nil
				}
			}

			if len(groupItem.VideoUrl) > 0 {
				var video models.MHRespVideo
				video.VideoURL = groupItem.VideoUrl
				video.CoverURL = groupItem.CoverUrl
				video.Width = groupItem.Width
				video.Height = groupItem.Height
				video.Duration = groupItem.Duration * 1000
				if len(video.VideoURL) > 0 {
					item.Video = &video
					item.CrtType = 20
					item.Image = nil
				}
			}
		}
	}
}
