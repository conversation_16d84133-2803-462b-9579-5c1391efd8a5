package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromMiMan ...
func GetFromMiMan(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from miman")

	// test
	// fmt.Println("get from miman, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from miman, p_pos_id:", platformPos.PlatformPosID)
	// fmt.Println("get from miman, app_bundle:", platformPos.PlatformAppBundle)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	// reqAppInfoMap["id"] = platformPos.PlatformAppID
	reqAppInfoMap["m_app"] = platformPos.PlatformAppName
	reqAppInfoMap["m_app_pn"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	// nativeadObject
	nativeadObject := map[string]interface{}{}
	nativeadObject["required_fields"] = []string{}

	// device
	reqDeviceInfoMap := map[string]interface{}{}
	reqDeviceInfoMap["devicetype"] = 0
	if mhReq.Device.DeviceType == 2 {
		reqDeviceInfoMap["devicetype"] = 1
	}

	reqDeviceInfoMap["ua"] = destConfigUA
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	// if strings.Contains(mhReq.Device.IP, ":") {
	// 	reqDeviceInfoMap["ipv6"] = mhReq.Device.IP
	// } else {
	// 	reqDeviceInfoMap["ip"] = mhReq.Device.IP
	// }

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true
				reqDeviceInfoMap["imei_md5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true
				reqDeviceInfoMap["imei_md5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}

			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceInfoMap["oaid_md5"] = utils.GetMd5(mhReq.Device.Oaid)
			}

			if len(mhReq.Device.Oaid) > 0 {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
		// reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = 0
		// reqDeviceInfoMap["model"] = mhReq.Device.Model
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
		// }
	} else if mhReq.Device.Os == "ios" {
		// fmt.Println("get from miman ios")
		reqDeviceInfoMap["m_mfr"] = "apple"
		reqDeviceInfoMap["os"] = 1
		// reqDeviceInfoMap["model"] = mhReq.Device.Model

		if len(mhReq.Device.OsVersion) > 0 {
			// reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
		}

		if len(mhReq.Device.Idfa) > 0 {
			//reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)
			reqDeviceInfoMap["idfa"] = mhReq.Device.Idfa
		} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
			// reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)

			// extraReportParams = extraReportParams + ",idfa_md5"
		} else {
			// miman需要无设备请求
			// bigdataExtra.InternalCode = 900101
			// bigdataExtra.ExternalCode = 102006

			// return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion

	// reqDeviceInfoMap["w"] = mhReq.Device.ScreenWidth
	// reqDeviceInfoMap["h"] = mhReq.Device.ScreenHeight

	// if mhReq.Network.ConnectType == 0 {
	// 	reqDeviceInfoMap["connectiontype"] = 0
	// } else if mhReq.Network.ConnectType == 1 {
	// 	reqDeviceInfoMap["connectiontype"] = 2
	// } else if mhReq.Network.ConnectType == 2 {
	// 	reqDeviceInfoMap["connectiontype"] = 4
	// } else if mhReq.Network.ConnectType == 3 {
	// 	reqDeviceInfoMap["connectiontype"] = 5
	// } else if mhReq.Network.ConnectType == 4 {
	// 	reqDeviceInfoMap["connectiontype"] = 6
	// } else if mhReq.Network.ConnectType == 7 {
	// 	reqDeviceInfoMap["connectiontype"] = 7
	// } else {
	// 	reqDeviceInfoMap["connectiontype"] = 0
	// }

	// if mhReq.Network.Carrier == 0 {
	// 	reqDeviceInfoMap["carrier"] = "unknown"
	// } else if mhReq.Network.Carrier == 1 {
	// 	reqDeviceInfoMap["carrier"] = "mobile"
	// } else if mhReq.Network.Carrier == 2 {
	// 	reqDeviceInfoMap["carrier"] = "unicom"
	// } else if mhReq.Network.Carrier == 3 {
	// 	reqDeviceInfoMap["carrier"] = "telecom"
	// } else {
	// 	reqDeviceInfoMap["carrier"] = "unknown"
	// }

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				// fmt.Println("kbg_debug miman android replace_key did value: ", localPos.LocalPosID, redisDIDValue)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imei_md5")
					delete(reqDeviceInfoMap, "oaid_md5")

					reqDeviceInfoMap["os"] = 0
					// reqDeviceInfoMap["osv"] = didRedisData.OsVersion
					// reqDeviceInfoMap["model"] = didRedisData.Model
					// reqDeviceInfoMap["make"] = didRedisData.Manufacturer
					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imei_md5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imei_md5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid_md5"] = utils.GetMd5(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				// osv model key
				redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

				// fmt.Println("kbg_debug miman ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					reqDeviceInfoMap["os"] = 1
					// reqDeviceInfoMap["osv"] = didRedisData.OsVersion
					// reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["m_mfr"] = "apple"
					if len(didRedisData.Idfa) > 0 {
						reqDeviceInfoMap["idfa"] = didRedisData.Idfa
					} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
						//reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)
					} else {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					}

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua
					}

					isHaveReplace = true
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()

		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"version":     "1.0",
		"nativead":    nativeadObject,
		"pid":         platformPos.PlatformPosID,
		"bidfloor":    localPosFloorPrice,
		"action_type": 1,
		"device":      reqDeviceInfoMap,
		"app":         reqAppInfoMap,
		"reqid":       bigdataUID,
		"secure":      1, // 2022年10月12日 产品要求改成 1
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	fmt.Println("miman req: " + string(jsonData))

	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	resp, err := client.Do(requestGet)
	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("miman resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// // Debug
	// if err == nil {
	// 	utils.MailDebuggerSend(string(bodyContent))
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	mimanRespStu := MiManRespStu{}
	json.Unmarshal([]byte(bodyContent), &mimanRespStu)

	//fmt.Println(bodyContent)

	if len(mimanRespStu.SRC) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	srcDecode, err := url.QueryUnescape(mimanRespStu.SRC)
	if err != nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	mimanRespSRC := MiManRespSRC{}

	mimanRespTypeISRC := MiManRespTypeISRC{}

	if mimanRespStu.Type == "R" {
		json.Unmarshal([]byte(srcDecode), &mimanRespSRC)

		if len(mimanRespSRC.Images) == 0 && len(mimanRespSRC.Video.Url) == 0 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else {
		json.Unmarshal([]byte(srcDecode), &mimanRespTypeISRC)

		if len(mimanRespTypeISRC.Url) == 0 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, mimanInfoItem := range []MiManRespSRC{mimanRespSRC} {
		adInfoItem := mimanInfoItem

		if mimanRespStu.Type != "R" {
			adInfoItem.Images = append(adInfoItem.Images,
				MiManRespImg{Url: mimanRespTypeISRC.Url,
					Width:  mimanRespTypeISRC.Width,
					Height: mimanRespTypeISRC.Height})
		}

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		mimanEcpm := utils.ConvertStringToInt(adInfoItem.Price)

		respTmpPrice = respTmpPrice + mimanEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if mimanEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			mimanEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > mimanEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(mimanEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceValue := "%%PRICE%%"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("miman price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			macroPrice := utils.ConvertIntToString(int(mimanEcpm * randPRValue / 100))

			// only for test
			// macroPrice = utils.ConvertIntToString(mimanEcpm)

			mimankey := platformPos.PlatformAppPriceEncrypt
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, mimankey)
			encodePriceValue = base64.StdEncoding.EncodeToString(encodePrice)
		}

		// win notice url
		// winNoticeURL := adInfoItem.NURL

		// if len(winNoticeURL) > 0 {
		// 	winNoticeURL = strings.Replace(winNoticeURL, "%%PRICE%%", encodePriceValue, -1)

		// 	go func() {
		// 		defer func() {
		// 			if err := recover(); err != nil {
		// 				fmt.Println("miman nurl panic:", err)
		// 			}
		// 		}()
		// 		curlMiManNurl(winNoticeURL)
		// 	}()
		// }

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		// crid
		if len(mimanRespStu.Crid) > 0 {
			respListItemMap["crid"] = mimanRespStu.Crid
		} else {
			respListItemMap["crid"] = utils.GetMd5(adInfoItem.Title)

		}

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(mimanRespStu.DeepLink) > 0 {
			respListItemMap["deep_link"] = mimanRespStu.DeepLink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.Icon.Url) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Icon.Url
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(mimanRespStu.DeepLink)
		}

		isVideo := false

		// 视频
		if len(adInfoItem.Video.Url) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Video.Duration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Video.Duration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
				respListVideoItemMap["duration"] = adInfoItem.Video.Duration * 1000
			}
			if adInfoItem.Video.Width > 0 {
				respListVideoItemMap["width"] = adInfoItem.Video.Width
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Video.Height > 0 {
				respListVideoItemMap["height"] = adInfoItem.Video.Height
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}
			respListVideoItemMap["video_url"] = adInfoItem.Video.Url

			if adInfoItem.Video.Width > 0 && adInfoItem.Video.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Video.Width, adInfoItem.Video.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			// cover_url
			if len(adInfoItem.Video.CoverImgUrl) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Video.CoverImgUrl
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			// 2022-09-30: vidoe对象的icon取 vodeo.icon_url
			if len(adInfoItem.Video.LogoUrl) > 0 {
				respListItemMap["icon_url"] = adInfoItem.Video.LogoUrl
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Images) > 0 && len(adInfoItem.Images[0].Url) > 0 {
				respListImageItemMap["url"] = adInfoItem.Images[0].Url
			} else if len(adInfoItem.Images) > 0 && len(adInfoItem.Images[0].Url) > 0 {
				respListImageItemMap["url"] = adInfoItem.Images[0].Url
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if len(adInfoItem.Images) > 0 && adInfoItem.Images[0].Width > 0 {
				respListImageItemMap["width"] = adInfoItem.Images[0].Width
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if len(adInfoItem.Images) > 0 && adInfoItem.Images[0].Height > 0 {
				respListImageItemMap["height"] = adInfoItem.Images[0].Height
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if len(adInfoItem.Images) > 0 && adInfoItem.Images[0].Width > 0 && adInfoItem.Images[0].Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Images[0].Width, adInfoItem.Images[0].Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if mimanRespStu.Action == 2 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = mimanRespStu.Ldp
			respListItemMap["landpage_url"] = mimanRespStu.Ldp
			if len(mimanRespStu.Ldp) == 0 {
				continue
			}
		} else if mimanRespStu.Action == 1 {
			if len(mimanRespStu.Ldp) == 0 {
				continue
			}

			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = mimanRespStu.Ldp
			respListItemMap["download_url"] = mimanRespStu.Ldp

		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.AppBundleID) > 0 {
			respListItemMap["package_name"] = adInfoItem.AppBundleID
		}

		if len(adInfoItem.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppName
		}
		if len(adInfoItem.Source) > 0 {
			respListItemMap["publisher"] = adInfoItem.Source
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, mimanEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		if len(mimanRespStu.Pm.Start) > 0 {
			for _, impItem := range mimanRespStu.Pm.Start {
				tmpItem := impItem
				tmpItem = strings.Replace(tmpItem, "${AUCTION_PRICE}", encodePriceValue, -1)

				respListItemImpArray = append(respListItemImpArray, tmpItem)
			}
			// impression_link maplehaze
			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, clkItem := range mimanRespStu.Cm {

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = mimanEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// miman resp
	respPDD := models.MHUpResp{}
	respPDD.RespData = &mhResp
	respPDD.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respPDD
}

func curlMiManNurl(nurl string) {
	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println("miman nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())
		return
	}

	defer resp.Body.Close()
}

// MiManRespStu ...
type MiManRespStu struct {
	PID      string      `json:"pid"`
	Action   int         `json:"action"`
	Type     string      `json:"type"`
	SRC      string      `json:"src"`
	Pm       MiManRespPm `json:"pm"`
	Cm       []string    `json:"cm"`
	Ldp      string      `json:"ldp"`
	DeepLink string      `json:"deeplink_url"`
	ReqID    string      `json:"reqid"`
	DealID   string      `json:"dealid"`
	Crid     string      `json:"crid"`
	Price    float32     `json:"float"`
}

type MiManRespPm struct {
	Start         []string `json:"0"`
	FirstQuartile []string `json:"1"`
	Midpoint      []string `json:"2"`
	ThirdQuartile []string `json:"3"`
	Complete      []string `json:"4"`
}

type MiManRespSRC struct {
	Title         string         `json:"title"`
	Description   string         `json:"desc"`
	Source        string         `json:"source"`
	Images        []MiManRespImg `json:"image"`
	Logo          MiManRespLogo  `json:"logo"`
	Icon          MiManRespIcon  `json:"icon"`
	Video         MiManRespVideo `json:"video"`
	Rating        string         `json:"rating"`
	Like          string         `json:"like"`
	Downloads     string         `json:"downloads"`
	Price         string         `json:"price"`
	CallToAuction string         `json:"call_to_auction"`
	Store         string         `json:"store"`
	AppName       string         `json:"appname"`
	AppBundleID   string         `json:"bundle"`
}

type MiManRespTypeISRC struct {
	Url      string `json:"url"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Duration int    `json:"duration"`
}

type MiManRespImg struct {
	Url    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type MiManRespIcon struct {
	Url    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type MiManRespLogo struct {
	Url    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type MiManRespVideo struct {
	Url         string `json:"url"`
	CoverImgUrl string `json:"cover_img_url"`
	LogoUrl     string `json:"logo_url"`
	Width       int    `json:"w"`
	Height      int    `json:"h"`
	Duration    int    `joson:"duration"`
}
