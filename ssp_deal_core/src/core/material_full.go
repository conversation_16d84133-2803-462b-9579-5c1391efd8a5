package core

import (
	"encoding/json"
	"math"
	"mh_proxy/db"
	"mh_proxy/models"
	"strconv"
)

func MaterialFull(platformPos *models.PlatformPosStu, os string, item *models.MHRespDataItem) {
	fullKey := "go_material_full"
	adFullKey := "go_material_full_" + platformPos.PlatformMediaID
	adFullCacheValue, _ := db.GlbBigCacheMinute.Get(adFullKey)
	fullCacheValue, _ := db.GlbBigCacheMinute.Get(fullKey)
	if len(adFullCacheValue) == 0 && len(fullCacheValue) == 0 {
		return
	}

	var materialFull MaterialFullStu
	var adMaterialFull MaterialFullStu

	if len(adFullCacheValue) > 0 {
		_ = json.Unmarshal(adFullCacheValue, &adMaterialFull)
	}
	if len(fullCacheValue) > 0 {
		_ = json.Unmarshal(fullCacheValue, &materialFull)
	}

	var (
		titleArray   []string
		adTitleArray []string
		descArray    []string
		adDescArray  []string
		iconArray    []string
		adIconArray  []string

		nativeCoverUrlArray  []string
		nativeCoverUrlWArray []string
		nativeCoverUrlHArray []string

		adNativeCoverUrlArray  []string
		adNativeCoverUrlWArray []string
		adNativeCoverUrlHArray []string

		incentiveCoverUrlArray  []string
		incentiveCoverUrlWArray []string
		incentiveCoverUrlHArray []string

		adIncentiveCoverUrlArray  []string
		adIncentiveCoverUrlWArray []string
		adIncentiveCoverUrlHArray []string

		appNameArray       []string
		adAppNameArray     []string
		appVersionArray    []string
		adAppVersionArray  []string
		appInfoArray       []string
		adAppInfoArray     []string
		publisherArray     []string
		adPublisherArray   []string
		permissionArray    []string
		adPermissionArray  []string
		privacyLinkArray   []string
		adPrivacyLinkArray []string
		packageSizeArray   []string
		adPackageSizeArray []string

		//permissionUrlArray   []string
		//adPermissionUrlArray []string
	)

	if len(materialFull.Title) > 0 {
		titleArray = materialFull.Title
	}
	if len(adMaterialFull.Title) > 0 {
		adTitleArray = adMaterialFull.Title
	}
	if len(materialFull.Desc) > 0 {
		descArray = materialFull.Desc
	}
	if len(adMaterialFull.Desc) > 0 {
		adDescArray = adMaterialFull.Desc
	}
	if len(materialFull.Icon) > 0 {
		iconArray = materialFull.Icon
	}
	if len(adMaterialFull.Icon) > 0 {
		adIconArray = adMaterialFull.Icon
	}

	if len(materialFull.NativeCoverUrl) > 0 {
		nativeCoverUrlArray = materialFull.NativeCoverUrl
	}
	if len(materialFull.NativeCoverUrlW) > 0 {
		nativeCoverUrlWArray = materialFull.NativeCoverUrlW
	}
	if len(materialFull.NativeCoverUrlH) > 0 {
		nativeCoverUrlHArray = materialFull.NativeCoverUrlH
	}

	if len(adMaterialFull.NativeCoverUrl) > 0 {
		adNativeCoverUrlArray = adMaterialFull.NativeCoverUrl
	}
	if len(adMaterialFull.NativeCoverUrlW) > 0 {
		adNativeCoverUrlWArray = adMaterialFull.NativeCoverUrlW
	}
	if len(adMaterialFull.NativeCoverUrlH) > 0 {
		adNativeCoverUrlHArray = adMaterialFull.NativeCoverUrlH
	}

	if len(materialFull.IncentiveCoverUrl) > 0 {
		incentiveCoverUrlArray = materialFull.IncentiveCoverUrl
	}
	if len(materialFull.IncentiveCoverUrlW) > 0 {
		incentiveCoverUrlWArray = materialFull.IncentiveCoverUrlW
	}
	if len(materialFull.IncentiveCoverUrlH) > 0 {
		incentiveCoverUrlHArray = materialFull.IncentiveCoverUrlH
	}

	if len(adMaterialFull.IncentiveCoverUrl) > 0 {
		adIncentiveCoverUrlArray = adMaterialFull.IncentiveCoverUrl
	}
	if len(adMaterialFull.IncentiveCoverUrlW) > 0 {
		adIncentiveCoverUrlWArray = adMaterialFull.IncentiveCoverUrlW
	}
	if len(adMaterialFull.IncentiveCoverUrlH) > 0 {
		adIncentiveCoverUrlHArray = adMaterialFull.IncentiveCoverUrlH
	}

	if len(materialFull.AppName) > 0 {
		appNameArray = materialFull.AppName
	}
	if len(adMaterialFull.AppName) > 0 {
		adAppNameArray = adMaterialFull.AppName
	}
	if len(materialFull.AppVersion) > 0 {
		appVersionArray = materialFull.AppVersion
	}
	if len(adMaterialFull.AppVersion) > 0 {
		adAppVersionArray = adMaterialFull.AppVersion
	}
	if len(materialFull.AppInfo) > 0 {
		appInfoArray = materialFull.AppInfo
	}
	if len(adMaterialFull.AppInfo) > 0 {
		adAppInfoArray = adMaterialFull.AppInfo
	}
	if len(materialFull.Publisher) > 0 {
		publisherArray = materialFull.Publisher
	}
	if len(adMaterialFull.Publisher) > 0 {
		adPublisherArray = adMaterialFull.Publisher
	}
	if len(materialFull.Permission) > 0 {
		permissionArray = materialFull.Permission
	}
	if len(adMaterialFull.Permission) > 0 {
		adPermissionArray = adMaterialFull.Permission
	}
	//if len(materialFull.PermissionUrl) > 0 {
	//	permissionUrlArray = materialFull.PermissionUrl
	//}
	//if len(adMaterialFull.PermissionUrl) > 0 {
	//	adPermissionUrlArray = adMaterialFull.PermissionUrl
	//}
	if len(materialFull.PrivacyLink) > 0 {
		privacyLinkArray = materialFull.PrivacyLink
	}
	if len(adMaterialFull.PrivacyLink) > 0 {
		adPrivacyLinkArray = adMaterialFull.PrivacyLink
	}
	if len(materialFull.PackageSize) > 0 {
		packageSizeArray = materialFull.PackageSize
	}
	if len(adMaterialFull.PackageSize) > 0 {
		adPackageSizeArray = adMaterialFull.PackageSize
	}

	var isEmpty string

	if len(item.Title) == 0 {
		if len(titleArray) > 0 {
			isEmpty = "1"
			item.Title = random(titleArray, 0)[0]
		}
		if len(adTitleArray) > 0 {
			isEmpty = "1"
			item.Title = random(adTitleArray, 0)[0]
		}
	}
	if len(item.Description) == 0 {
		if len(descArray) > 0 {
			isEmpty = "1"
			item.Description = random(descArray, 0)[0]
		}
		if len(adDescArray) > 0 {
			isEmpty = "1"
			item.Description = random(adDescArray, 0)[0]
		}
	}
	if len(item.IconURL) == 0 {
		if len(iconArray) > 0 {
			isEmpty = "1"
			item.IconURL = random(iconArray, 0)[0]
		}
		if len(adIconArray) > 0 {
			isEmpty = "1"
			item.IconURL = random(adIconArray, 0)[0]
		}
	}
	isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0

	switch platformPos.PlatformPosType {
	case 4:
		if isVideoAd {
			if len(item.Video.CoverURL) == 0 {
				if len(nativeCoverUrlArray) > 0 {
					isEmpty = "1"
					item.Video.CoverURL = random(nativeCoverUrlArray, 0)[0]
				}
				if len(adNativeCoverUrlArray) > 0 {
					isEmpty = "1"
					item.Video.CoverURL = random(adNativeCoverUrlArray, 0)[0]
				}

				if item.Video.Width > item.Video.Height && (len(nativeCoverUrlWArray) > 0 || len(adNativeCoverUrlWArray) > 0) {
					if len(nativeCoverUrlWArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(nativeCoverUrlWArray, 0)[0]
					}
					if len(adNativeCoverUrlWArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(adNativeCoverUrlWArray, 0)[0]
					}
				}

				if item.Video.Height > item.Video.Width && (len(nativeCoverUrlHArray) > 0 || len(adNativeCoverUrlHArray) > 0) {
					if len(nativeCoverUrlHArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(nativeCoverUrlHArray, 0)[0]
					}
					if len(adNativeCoverUrlHArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(adNativeCoverUrlHArray, 0)[0]
					}
				}
			}
		}
	case 11:
		if isVideoAd {
			if len(item.Video.CoverURL) == 0 {
				if len(incentiveCoverUrlArray) > 0 {
					isEmpty = "1"
					item.Video.CoverURL = random(incentiveCoverUrlArray, 0)[0]
				}
				if len(adIncentiveCoverUrlArray) > 0 {
					isEmpty = "1"
					item.Video.CoverURL = random(adIncentiveCoverUrlArray, 0)[0]
				}

				if item.Video.Width > item.Video.Height && (len(incentiveCoverUrlWArray) > 0 || len(adIncentiveCoverUrlWArray) > 0) {
					if len(incentiveCoverUrlWArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(incentiveCoverUrlWArray, 0)[0]
					}
					if len(adIncentiveCoverUrlWArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(adIncentiveCoverUrlWArray, 0)[0]
					}
				}

				if item.Video.Height > item.Video.Width && (len(incentiveCoverUrlHArray) > 0 || len(adIncentiveCoverUrlHArray) > 0) {
					if len(incentiveCoverUrlHArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(incentiveCoverUrlHArray, 0)[0]
					}
					if len(adIncentiveCoverUrlHArray) > 0 {
						isEmpty = "1"
						item.Video.CoverURL = random(adIncentiveCoverUrlHArray, 0)[0]
					}
				}
			}
		}
	}

	if os == "android" {
		if len(item.AppName) == 0 {
			if len(appNameArray) > 0 {
				isEmpty = "1"
				item.AppName = random(appNameArray, 0)[0]
			}
			if len(adAppNameArray) > 0 {
				isEmpty = "1"
				item.AppName = random(adAppNameArray, 0)[0]
			}
		}
		if len(item.AppVersion) == 0 {
			if len(appVersionArray) > 0 {
				isEmpty = "1"
				item.AppVersion = random(appVersionArray, 0)[0]
			}
			if len(adAppVersionArray) > 0 {
				isEmpty = "1"
				item.AppVersion = random(adAppVersionArray, 0)[0]
			}
		}
		if len(item.AppInfo) == 0 {
			if len(appInfoArray) > 0 {
				isEmpty = "1"
				item.AppInfo = random(appInfoArray, 0)[0]
			}
			if len(adAppInfoArray) > 0 {
				isEmpty = "1"
				item.AppInfo = random(adAppInfoArray, 0)[0]
			}
		}
		if len(item.Publisher) == 0 {
			if len(publisherArray) > 0 {
				isEmpty = "1"
				item.Publisher = random(publisherArray, 0)[0]
			}
			if len(adPublisherArray) > 0 {
				isEmpty = "1"
				item.Publisher = random(adPublisherArray, 0)[0]
			}
		}
		if len(item.Permission) == 0 {
			if len(permissionArray) > 0 {
				isEmpty = "1"
				item.Permission = random(permissionArray, 0)[0]
			}
			if len(adPermissionArray) > 0 {
				isEmpty = "1"
				item.Permission = random(adPermissionArray, 0)[0]
			}
		}
		//if len(item.PermissionURL) == 0 {
		//	if len(permissionUrlArray) > 0 {
		//		isEmpty = "1"
		//		item.PermissionURL = random(permissionUrlArray, 0)[0]
		//	}
		//	if len(adPermissionUrlArray) > 0 {
		//		isEmpty = "1"
		//		item.PermissionURL = random(adPermissionUrlArray, 0)[0]
		//	}
		//}
		if len(item.PrivacyLink) == 0 {
			if len(privacyLinkArray) > 0 {
				isEmpty = "1"
				item.PrivacyLink = random(privacyLinkArray, 0)[0]
			}
			if len(adPrivacyLinkArray) > 0 {
				isEmpty = "1"
				item.PrivacyLink = random(adPrivacyLinkArray, 0)[0]
			}
		}

		if item.PackageSize == 0 {
			if len(packageSizeArray) > 0 {
				isEmpty = "1"
				packageSizeStr := random(packageSizeArray, 0)[0]
				if len(packageSizeStr) > 0 {
					packageSizeFloat, _ := strconv.ParseFloat(packageSizeStr, 64)
					packageSizeNum := int64(math.Round(packageSizeFloat * float64(1024) * float64(1024)))
					item.PackageSize = packageSizeNum
				}
			}
			if len(adPackageSizeArray) > 0 {
				isEmpty = "1"
				packageSizeStr := random(adPackageSizeArray, 0)[0]
				if len(packageSizeStr) > 0 {
					packageSizeFloat, _ := strconv.ParseFloat(packageSizeStr, 64)
					packageSizeNum := int64(math.Round(packageSizeFloat * float64(1024) * float64(1024)))
					item.PackageSize = packageSizeNum
				}
			}
		}
	}

	item.IsEmpty = isEmpty
}

type MaterialFullStu struct {
	Title []string `json:"title,omitempty"`
	Desc  []string `json:"desc,omitempty"`
	Icon  []string `json:"icon,omitempty"`

	NativeCoverUrl  []string `json:"native_cover_url,omitempty"`
	NativeCoverUrlW []string `json:"native_cover_url_w,omitempty"`
	NativeCoverUrlH []string `json:"native_cover_url_h,omitempty"`

	IncentiveCoverUrl  []string `json:"incentive_cover_url,omitempty"`
	IncentiveCoverUrlW []string `json:"incentive_cover_url_w,omitempty"`
	IncentiveCoverUrlH []string `json:"incentive_cover_url_h,omitempty"`

	AppName       []string `json:"app_ame,omitempty"`
	AppVersion    []string `json:"app_version,omitempty"`
	AppInfo       []string `json:"app_info,omitempty"`
	Publisher     []string `json:"publisher,omitempty"`
	Permission    []string `json:"permission,omitempty"`
	PermissionUrl []string `json:"permission_url,omitempty"`
	PrivacyLink   []string `json:"privacy_link,omitempty"`
	PackageSize   []string `json:"package_size,omitempty"`
}
