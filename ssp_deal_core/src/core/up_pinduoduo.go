package core

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromPinDuoDuo ...
func GetFromPinDuoDuo(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from pinduoduo")

	// test
	// fmt.Println("get from pinduoduo, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from pinduoduo, p_pos_id:", platformPos.PlatformPosID)
	// fmt.Println("get from pinduoduo, app_bundle:", platformPos.PlatformAppBundle)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	// tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	// tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	// reqAppInfoMap["id"] = platformPos.PlatformAppID
	// reqAppInfoMap["name"] = platformPos.PlatformAppName
	reqAppInfoMap["bundle"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	// reqAppInfoMap["version"] = platformPos.PlatformAppVersion

	// imp
	reqImpArray := []map[string]interface{}{}
	reqImpItemMap := map[string]interface{}{}
	reqImpItemMap["id"] = bigdataUID
	reqImpItemMap["tagid"] = platformPos.PlatformPosID
	reqImpItemMap["bidfloor"] = localPosFloorPrice
	// reqImpItemMap["support_deeplink"] = true
	// 厂商流量标识
	if localPos.LocalAppIsManufacturer == 0 {
		reqImpItemMap["vendor_flow"] = 0
	} else {
		reqImpItemMap["vendor_flow"] = 1
	}
	// 快应用流量标识
	if localPos.LocalAppIsQuickApp == 0 {
		reqImpItemMap["quick_app_flow"] = 0
	} else {
		reqImpItemMap["quick_app_flow"] = 1
	}

	// imp templates
	reqImpItemTemplatesMap := []map[string]interface{}{}

	// localpos拼多多样式id, 优先使用下游拼多多样式id
	if len(localPos.LocalPosPinDuoDuoStyleIDs) > 0 {
		localPosStyles := strings.Split(localPos.LocalPosPinDuoDuoStyleIDs, ",")
		for _, styleItem := range localPosStyles {
			reqImpItemTemplate := map[string]interface{}{}
			reqImpItemTemplate["template_id"] = styleItem

			reqImpItemTemplatesMap = append(reqImpItemTemplatesMap, reqImpItemTemplate)
		}
	} else if len(platformPos.PlatformPosStyleIDs) > 0 {
		platformPosStyles := strings.Split(platformPos.PlatformPosStyleIDs, ",")
		for _, styleItem := range platformPosStyles {
			reqImpItemTemplate := map[string]interface{}{}
			reqImpItemTemplate["template_id"] = styleItem

			reqImpItemTemplatesMap = append(reqImpItemTemplatesMap, reqImpItemTemplate)
		}
	}

	reqImpItemMap["templates"] = reqImpItemTemplatesMap

	// imp content_type
	var reqImpItemContentTypeMap []int
	reqImpItemContentTypeMap = append(reqImpItemContentTypeMap, 1)
	reqImpItemContentTypeMap = append(reqImpItemContentTypeMap, 2)

	reqImpItemMap["content_type"] = reqImpItemContentTypeMap

	reqImpArray = append(reqImpArray, reqImpItemMap)

	// device
	reqDeviceInfoMap := map[string]interface{}{}
	reqDeviceInfoMap["ua"] = destConfigUA
	reqDeviceInfoMap["ip"] = mhReq.Device.IP
	if strings.Contains(mhReq.Device.IP, ":") {
		reqDeviceInfoMap["ipv6"] = mhReq.Device.IP
	} else {
		reqDeviceInfoMap["ip"] = mhReq.Device.IP
	}

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true
				reqDeviceInfoMap["imei_md5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true
				reqDeviceInfoMap["imei_md5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}

			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceInfoMap["oaid_md5"] = utils.GetMd5(mhReq.Device.Oaid)
			} else if len(mhReq.Device.OaidMd5) > 0 {
				reqDeviceInfoMap["oaid_md5"] = strings.ToLower(mhReq.Device.OaidMd5)
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
		reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = "android"
		reqDeviceInfoMap["model"] = mhReq.Device.Model
		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
		}
	} else if mhReq.Device.Os == "ios" {
		// fmt.Println("get from pinduoduo ios")
		reqDeviceInfoMap["make"] = "apple"
		reqDeviceInfoMap["os"] = "ios"
		reqDeviceInfoMap["model"] = mhReq.Device.Model

		if len(mhReq.Device.OsVersion) > 0 {
			reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
		}

		isIosDeviceOK := false

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			// 只支持单版本
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var reqDeviceInfoCaidMapArray []map[string]interface{}

				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["caid"] = item.CAID
						reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
						reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
						break
					}
				}

				if len(reqDeviceInfoCaidMapArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true

					reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
				}
			} else {
				var reqDeviceInfoCaidMapArray []map[string]interface{}

				for _, item := range mhReq.Device.CAIDMulti {
					reqDeviceInfoCaidMap := map[string]interface{}{}
					reqDeviceInfoCaidMap["caid"] = item.CAID
					reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
					reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

					reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
				}

				if len(reqDeviceInfoCaidMapArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
				reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
				reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

				isIOSToUpReportYinZi = true
			}
			// birth_time
			if len(tmpDeviceBirthSec) > 0 {
				reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
			}
		}
		if strings.Contains(iosReportMainParameter, "paid") {
			if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpDeviceBirthSec) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				reqDeviceInfoMap["paid_1_4"] = utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec)
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				// 只支持单版本
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["caid"] = item.CAID
							reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
							reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
							break
						}
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["caid"] = item.CAID
						reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
						reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
					reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
					reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

					isIOSToUpReportYinZi = true
				}
				// birth_time
				if len(tmpDeviceBirthSec) > 0 {
					reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					reqDeviceInfoMap["idfa_md5"] = strings.ToLower(mhReq.Device.IdfaMd5)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				// 只支持单版本
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["caid"] = item.CAID
							reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
							reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
							break
						}
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["caid"] = item.CAID
						reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
						reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
					reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
					reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

					isIOSToUpReportYinZi = true
				}
				// birth_time
				if len(tmpDeviceBirthSec) > 0 {
					reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
				}
			}
		}

		// if isIosDeviceOK {
		// } else {
		// 	// pinduoduo需要无设备请求
		// 	// bigdataExtra.InternalCode = 900101
		// 	// bigdataExtra.ExternalCode = 102006

		// 	// return MhUpErrorRespMap("", bigdataExtra)
		// }
	}
	// reqDeviceInfoMap["w"] = mhReq.Device.ScreenWidth
	// reqDeviceInfoMap["h"] = mhReq.Device.ScreenHeight

	// if mhReq.Network.ConnectType == 0 {
	// 	reqDeviceInfoMap["connectiontype"] = 0
	// } else if mhReq.Network.ConnectType == 1 {
	// 	reqDeviceInfoMap["connectiontype"] = 2
	// } else if mhReq.Network.ConnectType == 2 {
	// 	reqDeviceInfoMap["connectiontype"] = 4
	// } else if mhReq.Network.ConnectType == 3 {
	// 	reqDeviceInfoMap["connectiontype"] = 5
	// } else if mhReq.Network.ConnectType == 4 {
	// 	reqDeviceInfoMap["connectiontype"] = 6
	// } else if mhReq.Network.ConnectType == 7 {
	// 	reqDeviceInfoMap["connectiontype"] = 7
	// } else {
	// 	reqDeviceInfoMap["connectiontype"] = 0
	// }

	// if mhReq.Network.Carrier == 0 {
	// 	reqDeviceInfoMap["carrier"] = "unknown"
	// } else if mhReq.Network.Carrier == 1 {
	// 	reqDeviceInfoMap["carrier"] = "mobile"
	// } else if mhReq.Network.Carrier == 2 {
	// 	reqDeviceInfoMap["carrier"] = "unicom"
	// } else if mhReq.Network.Carrier == 3 {
	// 	reqDeviceInfoMap["carrier"] = "telecom"
	// } else {
	// 	reqDeviceInfoMap["carrier"] = "unknown"
	// }

	reqDeviceInfoMap["devicetype"] = 1

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				// fmt.Println("kbg_debug pinduoduo android replace_key did value: ", localPos.LocalPosID, redisDIDValue)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "imei_md5")
					delete(reqDeviceInfoMap, "oaid_md5")

					reqDeviceInfoMap["os"] = "android"
					reqDeviceInfoMap["osv"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer
					reqDeviceInfoMap["ua"] = didRedisData.Ua

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imei_md5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imei_md5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid_md5"] = utils.GetMd5(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						replaceUA = didRedisData.Ua
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				// osv model key
				redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

				// fmt.Println("kbg_debug pinduoduo ios replace_key: ", localPos.LocalPosID, redisReplaceKey)
				// fmt.Println("kbg_debug pinduoduo ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "idfa_md5")
					delete(reqDeviceInfoMap, "caid_infos")
					delete(reqDeviceInfoMap, "boot_time")
					delete(reqDeviceInfoMap, "update_time")
					delete(reqDeviceInfoMap, "birth_time")
					delete(reqDeviceInfoMap, "paid_1_4")

					reqDeviceInfoMap["os"] = "ios"
					reqDeviceInfoMap["osv"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = "apple"
					reqDeviceInfoMap["ua"] = didRedisData.Ua

					// 替换请求ios参数是否ok
					isIosReplaceDeviceOK := false
					isIOSToUpReportIDFA = false
					isIOSToUpReportCAID = false
					isIOSToUpReportYinZi = false

					if strings.Contains(iosReportMainParameter, "idfa") {
						if len(didRedisData.Idfa) > 0 {
							reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

							isIosReplaceDeviceOK = true
							isIOSToUpReportIDFA = true
						} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

							isIosReplaceDeviceOK = true
							isIOSToUpReportIDFA = true
						}
					}
					if strings.Contains(iosReportMainParameter, "caid") {
						var tmpCAIDMulti []models.MHReqCAIDMulti
						json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
						sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

						if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
							var reqDeviceInfoCaidMapArray []map[string]interface{}
							for _, item := range tmpCAIDMulti {
								if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
									reqDeviceInfoCaidMap := map[string]interface{}{}
									reqDeviceInfoCaidMap["caid"] = item.CAID
									reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
									reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
									break
								}
							}

							if len(reqDeviceInfoCaidMapArray) > 0 {
								isIosReplaceDeviceOK = true
								isIOSToUpReportCAID = true

								reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
							}
						} else {
							var reqDeviceInfoCaidMapArray []map[string]interface{}
							for _, item := range tmpCAIDMulti {
								reqDeviceInfoCaidMap := map[string]interface{}{}
								reqDeviceInfoCaidMap["caid"] = item.CAID
								reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
								reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

								reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
							}

							if len(reqDeviceInfoCaidMapArray) > 0 {
								isIosReplaceDeviceOK = true
								isIOSToUpReportCAID = true

								reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
							}
						}
					}
					if strings.Contains(iosReportMainParameter, "yinzi") {
						tmpDeviceStartSec = didRedisData.DeviceStartSec
						// tmpCountry = didRedisData.Country
						// tmpLanguage = didRedisData.Language
						// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
						// tmpHardwareMachine = didRedisData.HardwareMachine
						// tmpHardwareModel = didRedisData.HardwareModel
						// tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
						// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
						tmpSystemUpdateSec = didRedisData.SystemUpdateSec
						// tmpTimeZone = didRedisData.TimeZone
						tmpDeviceBirthSec = didRedisData.DeviceBirthSec

						if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
							reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
							reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

							isIosReplaceDeviceOK = true
							isIOSToUpReportYinZi = true
						}

						if len(tmpDeviceBirthSec) > 0 {
							reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
						}
					}
					if strings.Contains(iosReportMainParameter, "paid") {
						tmpDeviceStartSec = didRedisData.DeviceStartSec
						tmpSystemUpdateSec = didRedisData.SystemUpdateSec
						tmpDeviceBirthSec = didRedisData.DeviceBirthSec

						if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
							len(tmpDeviceBirthSec) > 0 {

							reqDeviceInfoMap["paid_1_4"] = utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec)

							isIosReplaceDeviceOK = true
							isIOSToUpReportYinZi = true
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter1) > 0 {
						if strings.Contains(iosReportSubParameter1, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter1, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var reqDeviceInfoCaidMapArray []map[string]interface{}
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["caid"] = item.CAID
										reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
										reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
										break
									}
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
								}
							} else {
								var reqDeviceInfoCaidMapArray []map[string]interface{}
								for _, item := range tmpCAIDMulti {
									reqDeviceInfoCaidMap := map[string]interface{}{}
									reqDeviceInfoCaidMap["caid"] = item.CAID
									reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
									reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
								}
							}
						} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							// tmpCountry = didRedisData.Country
							// tmpLanguage = didRedisData.Language
							// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							// tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							// tmpTimeZone = didRedisData.TimeZone
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
								reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
								reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}

							if len(tmpDeviceBirthSec) > 0 {
								reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
							}
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter2) > 0 {
						if strings.Contains(iosReportSubParameter2, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								reqDeviceInfoMap["idfa_md5"] = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								reqDeviceInfoMap["idfa_md5"] = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter2, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var reqDeviceInfoCaidMapArray []map[string]interface{}
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["caid"] = item.CAID
										reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
										reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
										break
									}
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
								}
							} else {
								var reqDeviceInfoCaidMapArray []map[string]interface{}
								for _, item := range tmpCAIDMulti {
									reqDeviceInfoCaidMap := map[string]interface{}{}
									reqDeviceInfoCaidMap["caid"] = item.CAID
									reqDeviceInfoCaidMap["version_str"] = item.CAIDVersion
									reqDeviceInfoCaidMap["version"] = utils.ConvertStringToInt(item.CAIDVersion)

									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caid_infos"] = reqDeviceInfoCaidMapArray
								}
							}
						} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							// tmpCountry = didRedisData.Country
							// tmpLanguage = didRedisData.Language
							// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							// tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							// tmpTimeZone = didRedisData.TimeZone
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 {
								reqDeviceInfoMap["boot_time"] = tmpDeviceStartSec
								reqDeviceInfoMap["update_time"] = tmpSystemUpdateSec

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}

							if len(tmpDeviceBirthSec) > 0 {
								reqDeviceInfoMap["birth_time"] = tmpDeviceBirthSec
							}
						}
					}

					if isIosReplaceDeviceOK {
					} else {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					}

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						replaceUA = didRedisData.Ua
					}

					isHaveReplace = true
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()

		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// user -> tag_id (1:已经安装拼多多 APP， 0:未安装，-1：未知，未知类型必须有值)
	reqUserInfoMap := map[string]interface{}{}
	var reqUserTagIdInfoMap []string
	if len(mhReq.Device.AppList) > 0 {
		isExist := false
		for _, appId := range mhReq.Device.AppList {
			if appId == 1005 {
				isExist = true
				break
			}
		}
		if isExist {
			reqUserTagIdInfoMap = append(reqUserTagIdInfoMap, "1")
		} else {
			reqUserTagIdInfoMap = append(reqUserTagIdInfoMap, "0")
		}
		reqUserInfoMap["tag_id"] = reqUserTagIdInfoMap
	} else {
		reqUserTagIdInfoMap = append(reqUserTagIdInfoMap, "-1")
		reqUserInfoMap["tag_id"] = reqUserTagIdInfoMap
	}

	// 在user对象 ext字段里：
	// tbi是淘宝安装态，已安装传1，未安装传0，未知传-1
	// jdi是京东安装态，已安装传1，未安装传0，未知传-1
	// 请求示例： "ext": "{\"tbi\": 1,\"jdi\": 1}"
	reqUserExtInfoMap := map[string]interface{}{}
	if len(mhReq.Device.AppList) > 0 {
		reqUserExtInfoMap["tbi"] = 0
		for _, appId := range mhReq.Device.AppList {
			// 1001 -> taobao
			if appId == 1001 {
				reqUserExtInfoMap["tbi"] = 1
				break
			}
		}
	} else {
		reqUserExtInfoMap["tbi"] = -1
	}
	if len(mhReq.Device.AppList) > 0 {
		reqUserExtInfoMap["jdi"] = 0
		for _, appId := range mhReq.Device.AppList {
			// 1002 -> jd
			if appId == 1002 {
				reqUserExtInfoMap["jdi"] = 1
				break
			}
		}
	} else {
		reqUserExtInfoMap["jdi"] = -1
	}
	userExtJsonData, _ := json.Marshal(reqUserExtInfoMap)
	reqUserInfoMap["ext"] = string(userExtJsonData)

	postData := map[string]interface{}{
		"adx_id":     platformPos.PlatformAppPinDuoDuoChannel,
		"request_id": bigdataUID,
		"at":         1,
		"imp":        reqImpArray,
		"app":        reqAppInfoMap,
		"device":     reqDeviceInfoMap,
		"user":       reqUserInfoMap,
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("pinduoduo req: ", string(jsonData))

	// 请求gzip
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(jsonData)
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
	}
	if err = gzipWriter.Close(); err != nil {
		fmt.Println(err)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json"
	httpHeader["Connection"] = "keep-alive"
	httpHeader["Content-Encoding"] = "gzip"
	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(buf.Bytes()))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		if statusCode == 204 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&pinduoduo", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, string(jsonData), platformPos.PlatformPosID)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	pinduoduoRespStu := PinDuoDuoRespStu{}
	json.Unmarshal([]byte(bodyContent), &pinduoduoRespStu)

	if len(pinduoduoRespStu.SeatBids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(pinduoduoRespStu.SeatBids[0].Bids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// nbr -> up_resp_code
	bigdataExtra.UpRespCode = pinduoduoRespStu.NBR

	if pinduoduoRespStu.NBR != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}

	pinduoduoRespItemList := pinduoduoRespStu.SeatBids[0].Bids
	if len(pinduoduoRespItemList) > 1 {
		sort.Sort(PinDuoDuoEcpmSort(pinduoduoRespItemList))
	}

	for _, pinduoduoInfoItem := range pinduoduoRespItemList {
		adInfoItem := pinduoduoInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		pinduoduoEcpm := adInfoItem.Price

		respTmpPrice = respTmpPrice + pinduoduoEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if pinduoduoEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			pinduoduoEcpm = platformPos.PlatformPosEcpm
		}

		pinduoduoLossNoticeURL := getPinDuoDuoPriceFailedURL(adInfoItem.LURL, platformPos, pinduoduoEcpm)

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > pinduoduoEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 1)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 3)
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(pinduoduoEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceValue := "%%PRICE%%"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("pinduoduo price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			macroPrice := utils.ConvertIntToString(int(pinduoduoEcpm * randPRValue / 100))

			// only for test
			// macroPrice = utils.ConvertIntToString(pinduoduoEcpm)

			pinduoduokey := platformPos.PlatformAppPriceEncrypt
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, pinduoduokey)
			encodePriceValue = base64.StdEncoding.EncodeToString(encodePrice)
		}

		// win notice url
		winNoticeURL := getPinDuoDuoPriceWinURL(adInfoItem.NURL, platformPos, pinduoduoEcpm, encodePriceValue)

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Adm.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Adm.Title
		}

		// description
		if len(adInfoItem.Adm.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Adm.Description
		}

		// crid
		// respListItemMap["crid"] = utils.GetMd5(adInfoItem.Adm.Title)

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.Deeplink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.Deeplink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		respListItemMap["icon_url"] = adInfoItem.Adm.IconURL

		// 如果不返回 video_url 或者 image_url 返回错误
		if len(adInfoItem.Adm.VideoURL) == 0 && len(adInfoItem.Adm.ImageURLs) == 0 {
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)

			// debug pdd
			go models.BigDataHoloDebugJson2(bigdataUID+"&debug&pinduoduo", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, string(jsonData), platformPos.PlatformPosID)
			continue
		}

		isVideo := false

		// 视频
		if len(adInfoItem.Adm.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Adm.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.Adm.VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
					continue
				}
				respListVideoItemMap["duration"] = adInfoItem.Adm.VideoDuration * 1000
			}
			if adInfoItem.Adm.Width > 0 {
				respListVideoItemMap["width"] = adInfoItem.Adm.Width
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Adm.Height > 0 {
				respListVideoItemMap["height"] = adInfoItem.Adm.Height
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}
			if adInfoItem.Adm.Width > 0 && adInfoItem.Adm.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Adm.Width, adInfoItem.Adm.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
					continue
				}
			}
			respListVideoItemMap["video_url"] = adInfoItem.Adm.VideoURL

			// cover_url
			if len(adInfoItem.Adm.VideoBackgroundURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Adm.VideoBackgroundURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, videoStartTrackItem := range adInfoItem.VideoPlayStartTrackUrls {
				respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoStartTrackItem)
			}
			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, videoEndTrackItem := range adInfoItem.VideoPlayCompleteTrackUrls {
				respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoEndTrackItem)
			}
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Adm.ImageURLs) > 0 {
				respListImageItemMap["url"] = adInfoItem.Adm.ImageURLs[0]
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
				continue
			}

			if adInfoItem.Adm.Width > 0 {
				respListImageItemMap["width"] = adInfoItem.Adm.Width
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Adm.Height > 0 {
				respListImageItemMap["height"] = adInfoItem.Adm.Height
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Adm.Width > 0 && adInfoItem.Adm.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Adm.Width, adInfoItem.Adm.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		////////////////////////////////////////////////////////////////////////////////////////////////////
		// pdd需求:
		// 1. content_type = 1:
		// 		a: 如果download_url长度 == 0
		// 			maplehaze字段:
		// 				interact_type(maplehaze字段) = 0
		// 				landpage_url(maplehaze字段) = click_url(pdd字段)
		// 		b: 如果download_url长度 > 0
		// 			maplehaze字段:
		// 				interact_type(maplehaze字段) = 1
		// 				landpage_url(maplehaze字段) = click_url(pdd字段)
		// 				download_url(maplehaze字段) = download_url(pdd ext download_url字段)
		// 2. content_type = 2:
		// 		maplehaze字段:
		// 			interact_type(maplehaze字段) = 1
		// 			landpage_url(maplehaze字段) = maplehaze落地页
		// 			download_url(maplehaze字段) = click_url(pdd字段)
		////////////////////////////////////////////////////////////////////////////////////////////////////
		// interact_type ad_url
		// if adInfoItem.ContentType == 1 {
		// 	respListItemMap["interact_type"] = 0
		// 	respListItemMap["ad_url"] = adInfoItem.ClickURL
		// 	respListItemMap["landpage_url"] = adInfoItem.ClickURL
		// 	if len(adInfoItem.ClickURL) == 0 {
		// 		continue
		// 	}

		// 	if len(adInfoItem.Ext) > 0 {
		// 		pinduoduoRespExtStu := PinDuoDuoRespSeatBidItemAdmExtStu{}
		// 		json.Unmarshal([]byte(adInfoItem.Ext), &pinduoduoRespExtStu)
		// 		if len(pinduoduoRespExtStu.DownloadURL) > 0 {
		// 			respListItemMap["interact_type"] = 1
		// 			respListItemMap["download_url"] = pinduoduoRespExtStu.DownloadURL
		// 		}
		// 	}
		// } else if adInfoItem.ContentType == 2 {
		// 	if len(adInfoItem.ClickURL) == 0 {
		// 		continue
		// 	}

		// 	// respListItemMap["interact_type"] = 1
		// 	// respListItemMap["ad_url"] = adInfoItem.DownloadURL
		// 	// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
		// 	respListItemMap["interact_type"] = 1
		// 	respListItemMap["ad_url"] = adInfoItem.ClickURL
		// 	respListItemMap["download_url"] = adInfoItem.ClickURL

		// } else {
		// 	fmt.Println("wrong interact type")
		// 	respTmpInternalCode = 900105
		// 	respTmpRespFailedNum = respTmpRespFailedNum + 1

		// 	continue
		// }

		////////////////////////////////////////////////////////////////////////////////////////////////////
		// pdd需求:
		// 强制转h5类型, 2024-12-05
		////////////////////////////////////////////////////////////////////////////////////////////////////
		respListItemMap["interact_type"] = 0
		respListItemMap["ad_url"] = adInfoItem.ClickURL
		respListItemMap["landpage_url"] = adInfoItem.ClickURL

		// if len(adInfoItem.AppBundle) > 0 {
		// 	respListItemMap["package_name"] = adInfoItem.AppBundle
		// } else {
		// 	respListItemMap["package_name"] = "com.xunmeng.pinduoduo"
		// }
		respListItemMap["package_name"] = "com.xunmeng.pinduoduo"

		if len(adInfoItem.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppName
		} else {
			respListItemMap["app_name"] = "拼多多"
		}
		if len(adInfoItem.Ext) > 0 {
			pinduoduoRespExtStu := PinDuoDuoRespSeatBidItemAdmExtStu{}
			json.Unmarshal([]byte(adInfoItem.Ext), &pinduoduoRespExtStu)
			if len(pinduoduoRespExtStu.Developer) > 0 {
				respListItemMap["publisher"] = pinduoduoRespExtStu.Developer
			}

			if len(pinduoduoRespExtStu.AppVersion) > 0 {
				respListItemMap["app_version"] = pinduoduoRespExtStu.AppVersion
			}

			if len(pinduoduoRespExtStu.PrivacyLink) > 0 {
				respListItemMap["privacy_url"] = pinduoduoRespExtStu.PrivacyLink
			}

			if len(pinduoduoRespExtStu.PermissionLink) > 0 {
				respListItemMap["permission_url"] = pinduoduoRespExtStu.PermissionLink
			}
		}

		// crid
		// respListItemMap["crid"] = GetCRIDByMd5(&respListItemMap)

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, pinduoduoEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		if platformPos.PlatformAppIsReportWin == 1 && platformPos.PlatformAppReportWinConfig == 0 {
			if len(winNoticeURL) > 0 {
				var tmpWinNoticeURLs []string
				tmpWinNoticeURLs = append(tmpWinNoticeURLs, winNoticeURL)
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}
		}

		// impression_link track url
		for _, impItem := range adInfoItem.ImpTrackUrls {
			tmpItem := impItem
			tmpItem = strings.Replace(tmpItem, "%%PRICE%%", encodePriceValue, -1)

			respListItemImpArray = append(respListItemImpArray, tmpItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, clkItem := range adInfoItem.ClickTrackUrls {

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, "拼多多", "com.xunmeng.pinduoduo")
		if appInfoFromRedisErr == nil {
			respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
			respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(pinduoduoLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, strings.Replace(pinduoduoLossNoticeURL, "__LOSE_CODE__", "1", -1))
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()

			// if rand.Intn(100000) == 0 {
			// 	go models.BigDataHoloDebugJson2(bigdataUID+"&pinduoduoloss", adInfoItem.LURL, pinduoduoLossNoticeURL, strings.Replace(pinduoduoLossNoticeURL, "__LOSE_CODE__", "1", -1), platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlPinDuoDuoPriceFailedURL(pinduoduoLossNoticeURL, &bigdataExtra, 2)
			continue
		}

		// 上报竞价成功
		if platformPos.PlatformAppIsReportWin == 1 && platformPos.PlatformAppReportWinConfig == 1 {
			if len(winNoticeURL) > 0 {
				var tmpWinNoticeURLs []string
				tmpWinNoticeURLs = append(tmpWinNoticeURLs, winNoticeURL)
				respListItemMap["demand_win_notice_urls"] = tmpWinNoticeURLs

				// if platformPos.PlatformAppID == "841101027" {
				// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_demand_win", strings.Join(tmpWinNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
				// }
			}
		}

		// 上报竞价失败
		if len(pinduoduoLossNoticeURL) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, pinduoduoLossNoticeURL)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = pinduoduoEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// pinduoduo resp
	respPDD := models.MHUpResp{}
	respPDD.RespData = &mhResp
	respPDD.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respPDD
}

type PinDuoDuoEcpmSort []PinDuoDuoRespSeatBidItemStu

func (s PinDuoDuoEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s PinDuoDuoEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s PinDuoDuoEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Price > s[j].Price
}

func getPinDuoDuoPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, pinduoduoEcpm int, winPriceMacro string) string {
	if pinduoduoEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	if pinduoduoEcpm > 0 && len(winNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		tmpWinNoticeURL := winNoticeURL
		tmpWinNoticeURL = strings.Replace(winNoticeURL, "%%PRICE%%", winPriceMacro, -1)

		return tmpWinNoticeURL
	}
	return ""
}

func getPinDuoDuoPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, pinduoduoEcpm int) string {

	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	if pinduoduoEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsReportLoss == 1 &&
		platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		macroPrice := utils.ConvertIntToString(int(pinduoduoEcpm * tmpRandValue / 100))

		pinduoduokey := platformPos.PlatformAppPriceEncrypt
		encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, pinduoduokey)
		encodePriceValue := base64.StdEncoding.EncodeToString(encodePrice)

		tmpLossNoticeURL := lossNoticeURL
		tmpLossNoticeURL = strings.Replace(tmpLossNoticeURL, "__AUCTION_WIN_PRICE__", encodePriceValue, -1)
		return tmpLossNoticeURL
	}

	return ""
}

// curl price failed
// reason 宏 __LOSE_CODE__, 竞败原因：1-出价低于竞争对手，2-创意素材问题，3-其他
func curlPinDuoDuoPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra, reason int) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		lossNoticeURL = strings.Replace(lossNoticeURL, "__LOSE_CODE__", utils.ConvertIntToString(reason), -1)
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("tanx loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

// PinDuoDuoRespStu ...
type PinDuoDuoRespStu struct {
	ID        string                      `json:"adx_id"`
	RequestID string                      `json:"request_id"`
	BIDID     string                      `json:"bid_id"`
	SeatBids  []PingDuoDuoRespSeatBidsStu `json:"seatbid"`
	NBR       int                         `json:"nbr"`
}

type PingDuoDuoRespSeatBidsStu struct {
	Bids []PinDuoDuoRespSeatBidItemStu `json:"bid"`
}

type PinDuoDuoRespSeatBidItemStu struct {
	ID             string                         `json:"id"`
	ImpID          string                         `json:"impid"`
	TagID          string                         `json:"tagid"`
	TemplateID     string                         `json:"template_id"`
	Price          int                            `json:"price"`
	PDDAdID        string                         `json:"pdd_adid"`
	AdID           string                         `json:"adid"`
	NURL           string                         `json:"nurl"`
	LURL           string                         `json:"lurl"` // 竞价失败url
	ImpTrackUrls   []string                       `json:"iurl"`
	ClickTrackUrls []string                       `json:"curl"`
	ClickURL       string                         `json:"click_url"`
	ContentType    int                            `json:"content_type"` // 1 H5类型, 2 下载类型
	Deeplink       string                         `json:"deeplink_url"`
	AppName        string                         `json:"app_name"`
	AppBundle      string                         `json:"app_bundle"`
	Adm            PinDuoDuoRespSeatBidItemAdmStu `json:"adm"`
	Ext            string                         `json:"ext"`
	// 视频播放上报, video_play_complete_url 后续暂时不用了, 改为 video_play_stop_url
	VideoPlayStartTrackUrls    []string `json:"video_play_start_url"`
	VideoPlayCompleteTrackUrls []string `json:"video_play_stop_url"`
}

// pinduoduo title <-> desc
type PinDuoDuoRespSeatBidItemAdmStu struct {
	Title              string   `json:"title"`
	Description        string   `json:"desc"`
	Text               string   `json:"text"`
	ImageURLs          []string `json:"image_url"`
	VideoURL           string   `json:"video_url"`
	VideoBackgroundURL string   `json:"video_background_url"`
	IconURL            string   `json:"icon_url"`
	Button             string   `json:"button"`
	Brand              string   `json:"brand"`
	VideoDuration      int      `json:"duration"`
	Width              int      `json:"width"`
	Height             int      `json:"height"`
}

type PinDuoDuoRespSeatBidItemAdmExtStu struct {
	PrivacyLink    string `json:"privacy_policy_link"`
	PermissionLink string `json:"permission_link"`
	AppVersion     string `json:"app_version"`
	DownloadURL    string `json:"download_url"`
	Developer      string `json:"developer"`
}
