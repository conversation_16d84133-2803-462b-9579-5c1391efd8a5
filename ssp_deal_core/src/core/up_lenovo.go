package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromLenovo ...
func GetFromLenovo(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from lenovo")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// device
	lenovoReqDevice := LenovoReqDeviceStu{}

	// device os
	if mhReq.Device.Os == "android" {
		lenovoReqDevice.OsType = 1
	} else if mhReq.Device.Os == "ios" {
		lenovoReqDevice.OsType = 2
	}

	// device osv
	lenovoReqDevice.Osv = mhReq.Device.OsVersion

	// device model
	lenovoReqDevice.Model = mhReq.Device.Model

	// device make
	lenovoReqDevice.Vendor = mhReq.Device.Manufacturer

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		lenovoReqDevice.Ua = destConfigUA
	}

	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				lenovoReqDevice.ImeiMd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				lenovoReqDevice.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				lenovoReqDevice.Oaid = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from lenovo error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				lenovoReqDevice.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				lenovoReqDevice.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							lenovoReqDevice.Caid = item.CAID
							break
						}
					}
				} else {
					// for _, item := range mhReq.Device.CAIDMulti {
					// 	isIosDeviceOK = true
					// 	isIOSToUpReportCAID = true
					// }
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 &&
				len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				// 系统内存容量
				lenovoReqDevice.Memory = tmpPhysicalMemoryByte

				lenovoReqDevice.BootTime = tmpDeviceStartSec
				lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
				lenovoReqDevice.TimeZone = tmpTimeZone
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					lenovoReqDevice.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					lenovoReqDevice.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								lenovoReqDevice.Caid = item.CAID
								break
							}
						}
					} else {
						// for _, item := range mhReq.Device.CAIDMulti {
						// 	isIosDeviceOK = true
						// 	isIOSToUpReportCAID = true
						// }
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 &&
					len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					/// 系统内存容量
					lenovoReqDevice.Memory = tmpPhysicalMemoryByte

					lenovoReqDevice.BootTime = tmpDeviceStartSec
					lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
					lenovoReqDevice.TimeZone = tmpTimeZone
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					lenovoReqDevice.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					lenovoReqDevice.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								lenovoReqDevice.Caid = item.CAID
								break
							}
						}
					} else {
						// for _, item := range mhReq.Device.CAIDMulti {
						// 	isIosDeviceOK = true
						// 	isIOSToUpReportCAID = true
						// }
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 &&
					len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					// 系统内存容量
					lenovoReqDevice.Memory = tmpPhysicalMemoryByte

					lenovoReqDevice.BootTime = tmpDeviceStartSec
					lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
					lenovoReqDevice.TimeZone = tmpTimeZone
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from lenovo error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if len(mhReq.Device.BootMark) > 0 {
		lenovoReqDevice.BootMark = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		lenovoReqDevice.UpdateMark = mhReq.Device.UpdateMark
	}
	if len(mhReq.Device.HMSCoreVersion) > 0 {
		lenovoReqDevice.HMSCoreVersion = mhReq.Device.HMSCoreVersion
	}
	if len(mhReq.Device.AppStoreVersion) > 0 {
		lenovoReqDevice.AppStoreVersion = mhReq.Device.AppStoreVersion
	}

	// 替换包
	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, strings.ToLower(mhReq.Device.Manufacturer))

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					lenovoReqDevice.Imei = ""
					lenovoReqDevice.ImeiMd5 = ""
					lenovoReqDevice.Oaid = ""
					lenovoReqDevice.BootMark = ""
					lenovoReqDevice.UpdateMark = ""

					lenovoReqDevice.Osv = didRedisData.OsVersion
					lenovoReqDevice.Model = didRedisData.Model
					lenovoReqDevice.Vendor = didRedisData.Manufacturer

					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						// destUA = didRedisData.Ua
						lenovoReqDevice.Ua = didRedisData.Ua
						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					if osvMajor < 10 {

						if len(didRedisData.Imei) > 0 {
							lenovoReqDevice.ImeiMd5 = utils.GetMd5(didRedisData.Imei)

						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							lenovoReqDevice.ImeiMd5 = didRedisData.ImeiMd5
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							lenovoReqDevice.Oaid = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				// osv model key
				redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// 初始化为空
					lenovoReqDevice.IdfaMd5 = ""
					lenovoReqDevice.Caid = ""
					lenovoReqDevice.Memory = ""
					lenovoReqDevice.BootTime = ""
					lenovoReqDevice.UpdateTime = ""
					lenovoReqDevice.TimeZone = ""

					lenovoReqDevice.Osv = didRedisData.OsVersion
					lenovoReqDevice.Model = didRedisData.Model
					lenovoReqDevice.Vendor = didRedisData.Manufacturer

					// 替换请求ios参数是否ok
					isIosReplaceDeviceOK := false
					isIOSToUpReportIDFA = false
					isIOSToUpReportCAID = false
					isIOSToUpReportYinZi = false

					if strings.Contains(iosReportMainParameter, "idfa") {
						if len(didRedisData.Idfa) > 0 {
							lenovoReqDevice.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

							isIosReplaceDeviceOK = true
							isIOSToUpReportIDFA = true
						} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

							lenovoReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

							isIosReplaceDeviceOK = true
							isIOSToUpReportIDFA = true
						}
					}
					if strings.Contains(iosReportMainParameter, "caid") {
						var tmpCAIDMulti []models.MHReqCAIDMulti
						json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
						sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

						if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
							for _, item := range tmpCAIDMulti {
								if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									lenovoReqDevice.Caid = item.CAID
									break
								}
							}
						} else {
							// for _, item := range tmpCAIDMulti {
							// 	isIosReplaceDeviceOK = true
							// 	isIOSToUpReportCAID = true
							// }
						}
					}
					if strings.Contains(iosReportMainParameter, "yinzi") {
						tmpDeviceStartSec = didRedisData.DeviceStartSec
						// tmpCountry = didRedisData.Country
						// tmpLanguage = didRedisData.Language
						// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
						// tmpHardwareMachine = didRedisData.HardwareMachine
						// tmpHardwareModel = didRedisData.HardwareModel
						tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
						// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
						tmpSystemUpdateSec = didRedisData.SystemUpdateSec
						tmpTimeZone = didRedisData.TimeZone
						// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

						if len(tmpDeviceStartSec) > 0 &&
							len(tmpPhysicalMemoryByte) > 0 &&
							len(tmpSystemUpdateSec) > 0 &&
							len(tmpTimeZone) > 0 {

							// 系统内存容量
							lenovoReqDevice.Memory = tmpPhysicalMemoryByte

							lenovoReqDevice.BootTime = tmpDeviceStartSec
							lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
							lenovoReqDevice.TimeZone = tmpTimeZone

							isIosReplaceDeviceOK = true
							isIOSToUpReportYinZi = true
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter1) > 0 {
						if strings.Contains(iosReportSubParameter1, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								lenovoReqDevice.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

								lenovoReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter1, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										lenovoReqDevice.Caid = item.CAID
										break
									}
								}
							} else {
								// for _, item := range tmpCAIDMulti {
								// 	isIosReplaceDeviceOK = true
								// 	isIOSToUpReportCAID = true
								// }
							}
						} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							// tmpCountry = didRedisData.Country
							// tmpLanguage = didRedisData.Language
							// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 &&
								len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								// 系统内存容量
								lenovoReqDevice.Memory = tmpPhysicalMemoryByte

								lenovoReqDevice.BootTime = tmpDeviceStartSec
								lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
								lenovoReqDevice.TimeZone = tmpTimeZone

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter2) > 0 {
						if strings.Contains(iosReportSubParameter2, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								lenovoReqDevice.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

								lenovoReqDevice.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter2, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										lenovoReqDevice.Caid = item.CAID
										break
									}
								}
							} else {
								// for _, item := range tmpCAIDMulti {
								// 	isIosReplaceDeviceOK = true
								// 	isIOSToUpReportCAID = true
								// }
							}
						} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							// tmpCountry = didRedisData.Country
							// tmpLanguage = didRedisData.Language
							// tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							// tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							// tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 &&
								len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								// 系统内存容量
								lenovoReqDevice.Memory = tmpPhysicalMemoryByte

								lenovoReqDevice.BootTime = tmpDeviceStartSec
								lenovoReqDevice.UpdateTime = tmpSystemUpdateSec
								lenovoReqDevice.TimeZone = tmpTimeZone

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}
					}

					if isIosReplaceDeviceOK {
					} else {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					}

					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						lenovoReqDevice.Ua = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from lenovo error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from lenovo error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// req app
	lenovoReqApp := LenovoReqAppStu{
		AppID:      platformPos.PlatformAppID,
		AppBundle:  GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		AppVersion: platformPos.PlatformAppVersion,
	}

	// req pos
	lenovoReqPos := LenovoReqPosStu{
		PosID:    platformPos.PlatformPosID,
		Width:    platformPos.PlatformPosWidth,
		Height:   platformPos.PlatformPosHeight,
		AdNum:    1,
		BidFloor: localPosFloorPrice,
		BidType:  0,
	}

	// req network
	lenovoReqNetwork := LenovoReqNetWorkStu{
		IP:           mhReq.Device.IP,
		OperatorType: mhReq.Network.Carrier,
	}
	if mhReq.Network.ConnectType == 0 {
		lenovoReqNetwork.ConnectionType = 0
	} else if mhReq.Network.ConnectType == 1 {
		lenovoReqNetwork.ConnectionType = 1
	} else if mhReq.Network.ConnectType == 2 {
		lenovoReqNetwork.ConnectionType = 2
	} else if mhReq.Network.ConnectType == 3 {
		lenovoReqNetwork.ConnectionType = 3
	} else if mhReq.Network.ConnectType == 4 {
		lenovoReqNetwork.ConnectionType = 4
	} else if mhReq.Network.ConnectType == 7 {
		lenovoReqNetwork.ConnectionType = 5
	} else {
		lenovoReqNetwork.ConnectionType = 1
	}

	// req
	lenovoReq := LenovoReqStu{
		RequestID:  bigdataUID,
		APIVersion: "3.8",
		App:        lenovoReqApp,
		Pos:        lenovoReqPos,
		Device:     lenovoReqDevice,
		Network:    lenovoReqNetwork,
	}
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(lenovoReq)
	fmt.Println("lenovo req: " + string(jsonData))

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	fmt.Println("lenovo resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	lenovoRespStu := LenovoRespStu{}
	json.Unmarshal([]byte(bodyContent), &lenovoRespStu)

	if lenovoRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = lenovoRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(lenovoRespStu.Data.Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}

	if len(lenovoRespStu.Data.Ads) > 1 {
		sort.Sort(LenovoEcpmSort(lenovoRespStu.Data.Ads))
	}

	for _, lenovoInfoItem := range lenovoRespStu.Data.Ads {
		adInfoItem := lenovoInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		lenovoEcpm := int(adInfoItem.Ecpm)

		respTmpPrice = respTmpPrice + lenovoEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if lenovoEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			lenovoEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > lenovoEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(lenovoEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// down_type 下载方式 1-直接请求下载 2-需要二次请求获取下载地址，详见注意事项
		if adInfoItem.Material.DownType == 2 {
			continue
		}
		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Material.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Material.Title
		}

		// description
		if len(adInfoItem.Material.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Material.Description
		}

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.DeepLink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.DeepLink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, dpSuccessTrackItem := range adInfoItem.AwakenpLinks {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, dpSuccessTrackItem)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, trackItem := range adInfoItem.AwakenpFaidLinks {
					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, trackItem)
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, adInfoItem.Material.AppName, adInfoItem.Material.AppPackage)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// gdt返回appinfo_url, 传给媒体
		if len(adInfoItem.Material.AppInfoURL) > 0 {
			respListItemMap["appinfo_url"] = adInfoItem.Material.AppInfoURL
		}

		// icon_url
		if len(adInfoItem.Material.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Material.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.DeepLink)
		}

		isVideo := false

		// 视频
		if len(adInfoItem.Material.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Material.VideoDuration > 0 {
				respListVideoItemMap["duration"] = adInfoItem.Material.VideoDuration * 1000
			}
			respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			respListVideoItemMap["height"] = platformPos.PlatformPosHeight

			respListVideoItemMap["video_url"] = adInfoItem.Material.VideoURL

			// cover_url
			if localPos.LocalPosWidth > localPos.LocalPosHeight {
				respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
			} else {
				respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			if platformPos.PlatformPosType == 11 {
				var respListEventTrackURLMap []map[string]interface{}

				respListVideoBeginEventTrackMap := map[string]interface{}{}
				respListVideoBeginEventTrackMap["event_type"] = 100
				var respListVideoBeginEventTrackURLMap []string
				for _, trackItem := range adInfoItem.VideoStartTracks {
					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, trackItem)
				}
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				if len(respListVideoBeginEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
				}

				respListVideoEndEventTrackMap := map[string]interface{}{}
				respListVideoEndEventTrackMap["event_type"] = 103
				var respListVideoEndEventTrackURLMap []string
				for _, trackItem := range adInfoItem.VideoFinishTracks {
					respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, trackItem)
				}
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				if len(respListVideoEndEventTrackURLMap) > 0 {
					respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
				}

				if len(respListEventTrackURLMap) > 0 {
					respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
				}
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Material.ImageURL) > 0 {
				respListImageItemMap["url"] = adInfoItem.Material.ImageURL
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if adInfoItem.Material.ImageWidth > 0 {
				respListImageItemMap["width"] = adInfoItem.Material.ImageWidth
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Material.ImageHeight > 0 {
				respListImageItemMap["height"] = adInfoItem.Material.ImageHeight
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Material.ImageWidth > 0 && adInfoItem.Material.ImageHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Material.ImageWidth, adInfoItem.Material.ImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		// respListItemMap["crid"] = GetCRIDByMd5(&respListItemMap)

		// interact_type ad_url
		if adInfoItem.Material.InteractionType == 2 || len(adInfoItem.PackageURL) > 0 {
			tmpDownLoadURL := ""
			if len(adInfoItem.PackageURL) > 0 {
				tmpDownLoadURL = adInfoItem.PackageURL
			} else if len(adInfoItem.ClickURL) > 0 {
				tmpDownLoadURL = adInfoItem.ClickURL
			}

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = tmpDownLoadURL
			respListItemMap["download_url"] = tmpDownLoadURL

		} else {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.ClickURL
			respListItemMap["landpage_url"] = adInfoItem.ClickURL
			if len(adInfoItem.ClickURL) == 0 {
				continue
			}
		}

		if len(adInfoItem.Material.AppPackage) > 0 {
			respListItemMap["package_name"] = adInfoItem.Material.AppPackage
		}

		if len(adInfoItem.Material.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.Material.AppName
		}
		if len(adInfoItem.Material.DevName) > 0 {
			respListItemMap["publisher"] = adInfoItem.Material.DevName
		}
		if len(adInfoItem.Material.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.Material.AppVersion
		}
		if len(adInfoItem.Material.PrivacyURL) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.Material.PrivacyURL
		}
		if len(adInfoItem.Material.AppPermissions) > 0 {
			permissionStr := ""
			for _, permissionItem := range adInfoItem.Material.AppPermissions {
				permissionStr = permissionStr + permissionItem.Title + "\n"
			}
			respListItemMap["permission"] = permissionStr
		}
		if adInfoItem.Material.AppSize > 0 {
			respListItemMap["package_size"] = adInfoItem.Material.AppSize
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, lenovoEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		if len(adInfoItem.Nurl) > 0 && platformPos.PlatformAppIsReportWin == 1 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, adInfoItem.Nurl)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.ImpressionLinks {
			impItem := trackInfoItem
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.ClickLinks {
			clkItem := trackInfoItem

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = lenovoEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// lenovo resp
	respLenovo := models.MHUpResp{}
	respLenovo.RespData = &mhResp
	respLenovo.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respLenovo
}

type LenovoEcpmSort []LenovoRespAdsStu

func (s LenovoEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s LenovoEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s LenovoEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}

// LenovoReqStu ...
type LenovoReqStu struct {
	RequestID  string              `json:"request_id"`
	APIVersion string              `json:"api_version"`
	App        LenovoReqAppStu     `json:"app"`
	Pos        LenovoReqPosStu     `json:"pos"`
	Device     LenovoReqDeviceStu  `json:"device"`
	Network    LenovoReqNetWorkStu `json:"network"`
}

type LenovoReqAppStu struct {
	AppID      string `json:"app_id"`
	AppBundle  string `json:"app_bundle"`
	AppVersion string `json:"app_version"`
}

type LenovoReqPosStu struct {
	PosID    string `json:"id"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	AdNum    int    `json:"ad_num"`
	BidFloor int    `json:"bid_floor"`
	BidType  int    `json:"bid_type"`
}

type LenovoReqDeviceStu struct {
	Imei            string `json:"imei"`
	ImeiMd5         string `json:"imei_md5"`
	Oaid            string `json:"oaid"`
	AndroidID       string `json:"android_id"`
	Idfa            string `json:"idfa"`
	IdfaMd5         string `json:"idfa_md5"`
	Caid            string `json:"caid"`
	DeviceType      int    `json:"device_type"`
	Ua              string `json:"user_agent"`
	Model           string `json:"model"`
	Vendor          string `json:"vendor"`
	OsType          int    `json:"os_type"`
	Osv             string `json:"os_version"`
	ScreenWidth     int    `json:"screen_width"`
	ScreenHeight    int    `json:"screen_height"`
	BootMark        string `json:"boot_mark"`
	UpdateMark      string `json:"update_mark"`
	HMSCoreVersion  string `json:"hms_version"`
	AppStoreVersion string `json:"app_store_version"`
	// MIUIVersion     string `json:"miui_version"`
	CPUNum     int    `json:"cpu_num"`
	Memory     string `json:"memory"`
	BootTime   string `json:"boot_time"`
	UpdateTime string `json:"update_time"`
	TimeZone   string `json:"time_zone"`
}

type LenovoReqNetWorkStu struct {
	IP             string `json:"ip"`
	ConnectionType int    `json:"connection_type"`
	OperatorType   int    `json:"operator_type"`
}

// LenovoRespStu ...
type LenovoRespStu struct {
	RequestID string            `json:"request_id"`
	Code      int               `json:"code"`
	Msg       string            `json:"msg"`
	Data      LenovoRespDataStu `json:"data"`
}

// LenovoRespDataStu ...
type LenovoRespDataStu struct {
	PosID string             `json:"pos_id"`
	Ads   []LenovoRespAdsStu `json:"ads"`
}

// LenovoRespAdsStu ...
type LenovoRespAdsStu struct {
	// 是否需要宏替换标识:0-否, 1-是
	MacroType int `json:"macro_type"`
	// 素材
	Material LenovoRespAdsMaterialStu `json:"material"`
	Ecpm     int                      `json:"price"`
	// 竟胜通知地址, 非必填
	Nurl string `json:"nurl"`
	// 广告点击跳转地址(按需宏替换), 如果 deep_link_url 不为 空则优先使用 deep_link_url
	ClickURL string `json:"click_url"`
	// APP 直接下载地址, 优先判断 package_url, 无值则使用 click_url 完成下载操作 如果 down_type = 2 时, 请求 click_url 获得实际下载地址
	PackageURL string `json:"package_url"`
	// 唤醒广告地址, 如果 deep_link_url 无法调起 APP,  使用 click_url 打开推广链接
	DeepLink string `json:"deep_link_url"`
	// 点击事件上报地址
	ClickLinks []string `json:"click_links"`
	// 曝光事件上报地址
	ImpressionLinks []string `json:"impression_links"`
	// 唤醒完成事件上报地址
	AwakenpLinks []string `json:"awaken_links"`
	// 唤醒失败事件上报地址
	AwakenpFaidLinks []string `json:"awaken_fail_links"`
	// 视频播放事件上报地址
	VideoStartTracks []string `json:"video_play_links"`
	// 视频播放完成事件上报地址
	VideoFinishTracks []string `json:"video_finish_links"`
}

// LenovoRespAdsMaterialStu ...
type LenovoRespAdsMaterialStu struct {
	AdID         string   `json:"ad_id"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	BrandName    string   `json:"brand_name"`
	IconURL      string   `json:"icon_url"`
	ImageURL     string   `json:"img_url"`
	ImageList    []string `json:"img_list"`
	ImageWidth   int      `json:"img_width"`
	ImageHeight  int      `json:"img_height"`
	CreativeType int      `json:"creative_type"`
	// 交互类型，1-打开网页，2-点击下载，3-APP 唤醒
	InteractionType int                                  `json:"interaction_type"`
	DownType        int                                  `json:"down_type"`
	AppName         string                               `json:"app_name"`
	AppPackage      string                               `json:"app_package"`
	AppVersion      string                               `json:"app_version"`
	AppSize         int                                  `json:"app_size"`
	Rating          int                                  `json:"rating"`
	Comments        int                                  `json:"comments"`
	DevName         string                               `json:"dev_name"`
	AppInfoURL      string                               `json:"app_info_url"`
	AppPermissions  []LenovoRespAdsMaterialPermissionStu `json:"app_permissions"`
	PermissionsURL  string                               `json:"permissions_url"`
	PrivacyURL      string                               `json:"privacy_url"`
	VideoURL        string                               `json:"video_url"`
	VideoDuration   int                                  `json:"video_duration"`
	VideoSize       int                                  `json:"video_size"`
	VideoType       string                               `json:"video_type"`
}

// LenovoRespAdsMaterialPermissionStu ...
type LenovoRespAdsMaterialPermissionStu struct {
	Title       string `json:"title"`
	Description string `json:"description"`
}
