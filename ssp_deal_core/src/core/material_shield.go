package core

import (
	"context"
	"mh_proxy/models"
	"strings"
	"time"
)

// IsShieldOK ...
// 屏蔽功能 -----> 直接返回
// 关键词: title(错误码: 900111); description(错误码: 900112)
// 包名: package_name, ios apple_id (错误码: 900114)
// ad_url: 落地页, 下载页, deeplink (错误码: 900113)
// 素材url: image_url, video_url, cover_url, icon_url (错误码: 900115)
// shield_rule_type: 0 不设置, 1 仅时间, 2 仅地域, 3 时间+地域
func IsMaterialShieldOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespDataItem *models.MHRespDataItem) (bool, int, string) {
	// shield white
	isShieldWhiteOK, shieldWhiteCode, shieldWhiteMessage := isMaterialShieldWhiteOK(c, mhReq, localPos, mhRespDataItem)
	if isShieldWhiteOK {
	} else {
		return isShieldWhiteOK, shieldWhiteCode, shieldWhiteMessage
	}

	// shield black
	return isMaterialShieldBlackOK(c, mhReq, localPos, mhRespDataItem)
}

// 是否shield white OK
func isMaterialShieldWhiteOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespItem *models.MHRespDataItem) (bool, int, string) {

	if len(localPos.LocalAppShieldWhiteRuleID) == 0 && len(localPos.LocalPosShieldWhiteRuleID) == 0 {
		return true, 900000, "OK"
	}

	if localPos.LocalAppShieldWhiteIsActive == 0 && localPos.LocalPosShieldWhiteIsActive == 0 {
		return true, 900000, "OK"
	}

	// 广告类型屏蔽 app
	if localPos.LocalAppAdShieldWhiteType > 0 {
		// 下载
		if mhRespItem.InteractType == 0 && localPos.LocalAppAdShieldWhiteType == 1 {
			return false, 900111, "屏蔽白名单失败"
		}
		// h5
		if mhRespItem.InteractType == 1 && localPos.LocalAppAdShieldWhiteType == 2 {
			return false, 900111, "屏蔽白名单失败"
		}
	}

	// 广告类型屏蔽 pos
	if localPos.LocalPosAdShieldWhiteType > 0 {
		switch localPos.LocalPosAdShieldWhiteType {
		case 1: // 下载
			if mhRespItem.InteractType == 0 {
				return false, 900111, "屏蔽白名单失败"
			}
		case 2: // h5
			if mhRespItem.InteractType == 1 {
				return false, 900111, "屏蔽白名单失败"
			}
		}
	}

	// shield_rule_type 1 仅时间 local_app
	if localPos.LocalAppShieldWhiteRuleType == 1 {
		if len(localPos.LocalAppShieldWhiteTimeList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteTimeList, time.Now().Format("15")) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 1 仅时间 local_pos
	if localPos.LocalPosShieldWhiteRuleType == 1 {
		if len(localPos.LocalPosShieldWhiteTimeList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteTimeList, time.Now().Format("15")) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 2 仅地域 local_app
	if localPos.LocalAppShieldWhiteRuleType == 2 {
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalAppShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteRegionList, mhReq.Device.IPProvince) {
				return true, 900000, "OK"
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalAppShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteRegionList, mhReq.Device.IPCity) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 2 仅地域 local_pos
	if localPos.LocalPosShieldWhiteRuleType == 2 {
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalPosShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteRegionList, mhReq.Device.IPProvince) {
				return true, 900000, "OK"
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalPosShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteRegionList, mhReq.Device.IPCity) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 3 时间+地域 local_app
	if localPos.LocalAppShieldWhiteRuleType == 3 {
		isTimeOK := false
		isRegionOK := false
		if len(localPos.LocalAppShieldWhiteTimeList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteTimeList, time.Now().Format("15")) {
				isTimeOK = true
			}
		}
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalAppShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteRegionList, mhReq.Device.IPProvince) {
				isRegionOK = true
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalAppShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldWhiteRegionList, mhReq.Device.IPCity) {
				isRegionOK = true
			}
		}
		if isTimeOK && isRegionOK {
			return true, 900000, "OK"
		}
	}

	// shield_rule_type 3 时间+地域 local_pos
	if localPos.LocalPosShieldWhiteRuleType == 3 {
		isTimeOK := false
		isRegionOK := false
		if len(localPos.LocalPosShieldWhiteTimeList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteTimeList, time.Now().Format("15")) {
				isTimeOK = true
			}
		}
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalPosShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteRegionList, mhReq.Device.IPProvince) {
				isRegionOK = true
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalPosShieldWhiteRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldWhiteRegionList, mhReq.Device.IPCity) {
				isRegionOK = true
			}
		}
		if isTimeOK && isRegionOK {
			return true, 900000, "OK"
		}
	}

	// 解析
	// dataByte, _ := json.Marshal(respListItemMap)
	// mhRespItem := models.MHRespDataItem{}
	// json.Unmarshal(dataByte, &mhRespItem)

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核关键字 app shield_key
	if len(localPos.LocalAppShieldWhiteKey) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteKey, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Title) > 0 {
				if strings.Contains(mhRespItem.Title, item) {
					return true, 900000, "OK"
				}
			}

			if len(mhRespItem.Description) > 0 {
				if strings.Contains(mhRespItem.Description, item) {
					return true, 900000, "OK"
				}
			}
		}
	}

	// 审核关键字 pos shield_key
	if len(localPos.LocalPosShieldWhiteKey) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteKey, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Title) > 0 {
				if strings.Contains(mhRespItem.Title, item) {
					return true, 900000, "OK"
				}
			}

			if len(mhRespItem.Description) > 0 {
				if strings.Contains(mhRespItem.Description, item) {
					return true, 900000, "OK"
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核包名 app
	if len(localPos.LocalAppShieldWhitePackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhitePackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.PackageName) > 0 {
				if strings.Contains(mhRespItem.PackageName, item) {
					return true, 900000, "OK"
				}
			}
			if mhReq.Device.Os == "ios" {
				iosAdURL := mhRespItem.AdURL
				if strings.Contains(iosAdURL, "apple.com") {
					if strings.Contains(iosAdURL, item) {
						return true, 900000, "OK"
					}
				}
			}
		}
	}
	// 审核包名 pos
	if len(localPos.LocalPosShieldWhitePackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhitePackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.PackageName) > 0 {
				if strings.Contains(mhRespItem.PackageName, item) {
					return true, 900000, "OK"
				}
			}
			if mhReq.Device.Os == "ios" {
				iosAdURL := mhRespItem.AdURL
				if strings.Contains(iosAdURL, "apple.com") {
					if strings.Contains(iosAdURL, item) {
						return true, 900000, "OK"
					}
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核ad_url app
	if len(localPos.LocalAppShieldWhiteAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.LandpageURL) > 0 {
				if strings.Contains(mhRespItem.LandpageURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.DownloadURL) > 0 {
				if strings.Contains(mhRespItem.DownloadURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.AdURL) > 0 {
				if strings.Contains(mhRespItem.AdURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.DeepLink) > 0 {
				if strings.Contains(mhRespItem.DeepLink, item) {
					return true, 900000, "OK"
				}
			}
		}
	}

	// 审核ad_url pos
	if len(localPos.LocalPosShieldWhiteAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.LandpageURL) > 0 {
				if strings.Contains(mhRespItem.LandpageURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.DownloadURL) > 0 {
				if strings.Contains(mhRespItem.DownloadURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.AdURL) > 0 {
				if strings.Contains(mhRespItem.AdURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.DeepLink) > 0 {
				if strings.Contains(mhRespItem.DeepLink, item) {
					return true, 900000, "OK"
				}
			}
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核素材 app
	if len(localPos.LocalAppShieldWhiteMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldWhiteMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Image) > 0 {
				if strings.Contains(mhRespItem.Image[0].URL, item) {
					return true, 900000, "OK"
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.VideoURL) > 0 {
				if strings.Contains(mhRespItem.Video.VideoURL, item) {
					return true, 900000, "OK"
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.CoverURL) > 0 {
				if strings.Contains(mhRespItem.Video.CoverURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.IconURL) > 0 {
				if strings.Contains(mhRespItem.IconURL, item) {
					return true, 900000, "OK"
				}
			}
		}
	}

	// 审核素材 pos
	if len(localPos.LocalPosShieldWhiteMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldWhiteMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Image) > 0 {
				if strings.Contains(mhRespItem.Image[0].URL, item) {
					return true, 900000, "OK"
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.VideoURL) > 0 {
				if strings.Contains(mhRespItem.Video.VideoURL, item) {
					return true, 900000, "OK"
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.CoverURL) > 0 {
				if strings.Contains(mhRespItem.Video.CoverURL, item) {
					return true, 900000, "OK"
				}
			}
			if len(mhRespItem.IconURL) > 0 {
				if strings.Contains(mhRespItem.IconURL, item) {
					return true, 900000, "OK"
				}
			}
		}
	}

	return false, 900111, "屏蔽白名单失败"
}

// 是否shield black OK
func isMaterialShieldBlackOK(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhRespItem *models.MHRespDataItem) (bool, int, string) {
	if len(localPos.LocalAppShieldRuleID) == 0 && len(localPos.LocalPosShieldRuleID) == 0 {
		return true, 900000, "OK"
	}

	if localPos.LocalAppShieldIsActive == 0 && localPos.LocalPosShieldIsActive == 0 {
		return true, 900000, "OK"
	}

	// 广告类型屏蔽 app
	if localPos.LocalAppAdShieldType > 0 {
		// 下载
		if mhRespItem.InteractType == 1 && localPos.LocalAppAdShieldType == 1 {
			return false, 900115, "ad_shield_type download"
		}
		// h5
		if mhRespItem.InteractType == 0 && localPos.LocalAppAdShieldType == 2 {
			return false, 900115, "ad_shield_type h5"
		}
	}
	// 广告类型屏蔽 pos
	if localPos.LocalPosAdShieldType > 0 {
		// 下载
		if mhRespItem.InteractType == 1 && localPos.LocalPosAdShieldType == 1 {
			return false, 900115, "ad_shield_type download"
		}
		// h5
		if mhRespItem.InteractType == 0 && localPos.LocalPosAdShieldType == 2 {
			return false, 900115, "ad_shield_type h5"
		}
	}

	// shield_rule_type 1 仅时间 local_app
	if localPos.LocalAppShieldRuleType == 1 {
		if len(localPos.LocalAppShieldTimeList) > 0 {
			if strings.Contains(localPos.LocalAppShieldTimeList, time.Now().Format("15")) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 1 仅时间 local_pos
	if localPos.LocalPosShieldRuleType == 1 {
		if len(localPos.LocalPosShieldTimeList) > 0 {
			if strings.Contains(localPos.LocalPosShieldTimeList, time.Now().Format("15")) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 2 仅地域 local_app
	if localPos.LocalAppShieldRuleType == 2 {
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalAppShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldRegionList, mhReq.Device.IPProvince) {
				return true, 900000, "OK"
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalAppShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldRegionList, mhReq.Device.IPCity) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 2 仅地域 local_pos
	if localPos.LocalPosShieldRuleType == 2 {
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalPosShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldRegionList, mhReq.Device.IPProvince) {
				return true, 900000, "OK"
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalPosShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldRegionList, mhReq.Device.IPCity) {
				return true, 900000, "OK"
			}
		}
	}

	// shield_rule_type 3 时间+地域 local_app
	if localPos.LocalAppShieldRuleType == 3 {
		isTimeOK := false
		isRegionOK := false
		if len(localPos.LocalAppShieldTimeList) > 0 {
			if strings.Contains(localPos.LocalAppShieldTimeList, time.Now().Format("15")) {
				isTimeOK = true
			}
		}
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalAppShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldRegionList, mhReq.Device.IPProvince) {
				isRegionOK = true
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalAppShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalAppShieldRegionList, mhReq.Device.IPCity) {
				isRegionOK = true
			}
		}
		if isTimeOK && isRegionOK {
			return true, 900000, "OK"
		}
	}

	// shield_rule_type 3 时间+地域 local_pos
	if localPos.LocalPosShieldRuleType == 3 {
		isTimeOK := false
		isRegionOK := false
		if len(localPos.LocalPosShieldTimeList) > 0 {
			if strings.Contains(localPos.LocalPosShieldTimeList, time.Now().Format("15")) {
				isTimeOK = true
			}
		}
		if len(mhReq.Device.IPProvince) > 0 && len(localPos.LocalPosShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldRegionList, mhReq.Device.IPProvince) {
				isRegionOK = true
			}
		}
		if len(mhReq.Device.IPCity) > 0 && len(localPos.LocalPosShieldRegionList) > 0 {
			if strings.Contains(localPos.LocalPosShieldRegionList, mhReq.Device.IPCity) {
				isRegionOK = true
			}
		}
		if isTimeOK && isRegionOK {
			return true, 900000, "OK"
		}
	}

	// // 解析
	// dataByte, _ := json.Marshal(respListItemMap)
	// mhRespItem := models.MHRespDataItem{}
	// json.Unmarshal(dataByte, &mhRespItem)

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核关键字 app shield_key
	if len(localPos.LocalAppShieldKey) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldKey, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Title) > 0 {
				if strings.Contains(mhRespItem.Title, item) {
					return false, 900111, "标题关键字," + item
				}
			}

			if len(mhRespItem.Description) > 0 {
				if strings.Contains(mhRespItem.Description, item) {
					return false, 900112, "描述关键字," + item
				}
			}
		}
	}

	// 审核关键字 pos shield_key
	if len(localPos.LocalPosShieldKey) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldKey, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Title) > 0 {
				if strings.Contains(mhRespItem.Title, item) {
					return false, 900111, "标题关键字," + item
				}
			}

			if len(mhRespItem.Description) > 0 {
				if strings.Contains(mhRespItem.Description, item) {
					return false, 900112, "描述关键字," + item
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核包名 app
	if len(localPos.LocalAppShieldPackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldPackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.PackageName) > 0 {
				if strings.Contains(mhRespItem.PackageName, item) {
					return false, 900114, "包名," + item
				}
			}
			if mhReq.Device.Os == "ios" {
				iosAdURL := mhRespItem.AdURL
				if strings.Contains(iosAdURL, "apple.com") {
					if strings.Contains(iosAdURL, item) {
						return false, 900114, "ios包名," + item
					}
				}
			}
		}
	}
	// 审核包名 pos
	if len(localPos.LocalPosShieldPackageName) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldPackageName, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.PackageName) > 0 {
				if strings.Contains(mhRespItem.PackageName, item) {
					return false, 900114, "包名," + item
				}
			}
			if mhReq.Device.Os == "ios" {
				iosAdURL := mhRespItem.AdURL
				if strings.Contains(iosAdURL, "apple.com") {
					if strings.Contains(iosAdURL, item) {
						return false, 900114, "ios包名," + item
					}
				}
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核ad_url app
	if len(localPos.LocalAppShieldAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.LandpageURL) > 0 {
				if strings.Contains(mhRespItem.LandpageURL, item) {
					return false, 900113, "landing page url," + item
				}
			}
			if len(mhRespItem.DownloadURL) > 0 {
				if strings.Contains(mhRespItem.DownloadURL, item) {
					return false, 900113, "download url," + item
				}
			}
			if len(mhRespItem.AdURL) > 0 {
				if strings.Contains(mhRespItem.AdURL, item) {
					return false, 900113, "ad url," + item
				}
			}
			if len(mhRespItem.DeepLink) > 0 {
				if strings.Contains(mhRespItem.DeepLink, item) {
					return false, 900113, "deeplink," + item
				}
			}
		}
	}

	// 审核ad_url pos
	if len(localPos.LocalPosShieldAdURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldAdURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.LandpageURL) > 0 {
				if strings.Contains(mhRespItem.LandpageURL, item) {
					return false, 900113, "landing page url," + item
				}
			}
			if len(mhRespItem.DownloadURL) > 0 {
				if strings.Contains(mhRespItem.DownloadURL, item) {
					return false, 900113, "download url," + item
				}
			}
			if len(mhRespItem.AdURL) > 0 {
				if strings.Contains(mhRespItem.AdURL, item) {
					return false, 900113, "ad url," + item
				}
			}
			if len(mhRespItem.DeepLink) > 0 {
				if strings.Contains(mhRespItem.DeepLink, item) {
					return false, 900113, "deeplink," + item
				}
			}
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 审核素材 app
	if len(localPos.LocalAppShieldMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalAppShieldMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Image) > 0 {
				if strings.Contains(mhRespItem.Image[0].URL, item) {
					return false, 900115, "image url," + item
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.VideoURL) > 0 {
				if strings.Contains(mhRespItem.Video.VideoURL, item) {
					return false, 900115, "video url," + item
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.CoverURL) > 0 {
				if strings.Contains(mhRespItem.Video.CoverURL, item) {
					return false, 900115, "cover url," + item
				}
			}
			if len(mhRespItem.IconURL) > 0 {
				if strings.Contains(mhRespItem.IconURL, item) {
					return false, 900115, "icon url," + item
				}
			}
		}
	}

	// 审核素材 pos
	if len(localPos.LocalPosShieldMaterialURL) > 0 {
		for _, item := range strings.Split(localPos.LocalPosShieldMaterialURL, ",") {
			if len(item) == 0 {
				continue
			}
			if len(mhRespItem.Image) > 0 {
				if strings.Contains(mhRespItem.Image[0].URL, item) {
					return false, 900115, "image url," + item
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.VideoURL) > 0 {
				if strings.Contains(mhRespItem.Video.VideoURL, item) {
					return false, 900115, "video url," + item
				}
			}
			if mhRespItem.Video != nil && len(mhRespItem.Video.CoverURL) > 0 {
				if strings.Contains(mhRespItem.Video.CoverURL, item) {
					return false, 900115, "cover url," + item
				}
			}
			if len(mhRespItem.IconURL) > 0 {
				if strings.Contains(mhRespItem.IconURL, item) {
					return false, 900115, "icon url," + item
				}
			}
		}
	}

	return true, 900000, "OK"
}
