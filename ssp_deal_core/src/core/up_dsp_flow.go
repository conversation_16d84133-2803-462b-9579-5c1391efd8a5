package core

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

// GetFromDspByFlow ...
func GetFromDspByFlow(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, bigdataUID string) *DspRespStu {
	// fmt.Println("get from dsp, local info: ", localPos.LocalAppID, localPos.LocalPosID)

	cacheKey := "dsp_config_supply_app_id_" + localPos.LocalAppID
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	if cacheError != nil {
		return nil
	}

	var dspFlowStu models.DspFlowStu
	json.Unmarshal([]byte(cacheValue), &dspFlowStu)
	if len(dspFlowStu.PosTypes) == 0 {
		return nil
	}

	// 通过率
	if rand.Intn(10000) < dspFlowStu.Weight {
	} else {
		return nil
	}

	// fmt.Println("debug_cache ->:", cacheError, string(cacheValue))

	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="9">激励视频</el-radio>
	tmpPosTypeStr := ""
	switch localPos.LocalPosType {
	case 2:
		tmpPosTypeStr = "splash"
	case 3:
		tmpPosTypeStr = "interstitial"
	case 4:
		tmpPosTypeStr = "native"
	case 9:
		tmpPosTypeStr = "reward"
	default:
		return nil

	}
	isPosTypeOK := false
	for _, item := range dspFlowStu.PosTypes {
		if item == tmpPosTypeStr {
			isPosTypeOK = true
			break
		}
	}
	if isPosTypeOK {
	} else {
		return nil
	}

	// timeout
	timeout := localPos.LocalPosTimeOut

	reqStu := DspReqStu{
		SDKVersion: mhReq.SDKVersion,
		Media: DspReqMediaStu{
			SupplyAppID: localPos.LocalAppID,
		},
		Pos: DspReqPosStu{
			SupplyPosID:   localPos.LocalPosID,
			SupplyPosType: tmpPosTypeStr,
		},
		Device: DspReqDeviceStu{
			Os:           mhReq.Device.Os,
			OsVersion:    mhReq.Device.OsVersion,
			Model:        mhReq.Device.Model,
			Manufacturer: mhReq.Device.Manufacturer,
			Imei:         mhReq.Device.Imei,
			ImeiMd5:      mhReq.Device.ImeiMd5,
			AndroidID:    mhReq.Device.AndroidID,
			AndroidIDMd5: mhReq.Device.AndroidIDMd5,
			Oaid:         mhReq.Device.Oaid,
			OaidMd5:      mhReq.Device.OaidMd5,
			Idfa:         mhReq.Device.Idfa,
			IdfaMd5:      mhReq.Device.IdfaMd5,
			Ua:           mhReq.Device.Ua,
			DeviceType:   mhReq.Device.DeviceType,
			IP:           mhReq.Device.IP,
			Mac:          mhReq.Device.Mac,
			DIDMd5Key:    mhReq.Device.DIDMd5,
			CAIDMulti:    mhReq.Device.CAIDMulti,
		},
		Network: DspReqNetwork{
			ConnectType: mhReq.Network.ConnectType,
			Carrier:     mhReq.Network.Carrier,
		},
		Geo: DspReqGeo{
			Lat: mhReq.Geo.Lat,
			Lng: mhReq.Geo.Lng,
		},
	}

	jsonData, _ := json.Marshal(reqStu)
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	// httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, config.UpDspURL+"/flow/req",
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	if err != nil {
		return nil
	}

	if statusCode != 200 {
		return nil
	}
	////////////////////////////////////////////////////////////////////////////////////////
	fmt.Println("dsp flow resp:", string(bodyContent))

	dspRespStu := DspRespStu{}
	json.Unmarshal([]byte(bodyContent), &dspRespStu)
	if dspRespStu.Code != 10000 {
		return nil
	}

	return &dspRespStu
}

// DspReqMediaStu ...
type DspReqMediaStu struct {
	SupplyAppID string `json:"app_id,omitempty"`
}

// DspReqNetwork ...
type DspReqNetwork struct {
	ConnectType int `json:"connect_type,omitempty"`
	Carrier     int `json:"carrier,omitempty"`
}

// DspReqGeo ...
type DspReqGeo struct {
	Lat float64 `json:"lat,omitempty"`
	Lng float64 `json:"lng,omitempty"`
}
