package core

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// 360 os json协议
func GetFrom360OS(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from 360os")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	// network
	three60OSReqNetwork := Three60OSRequestNetworkStu{}
	three60OSReqNetwork.IP = mhReq.Device.IP
	if mhReq.Network.ConnectType == 0 {
		three60OSReqNetwork.NetworkType = 0
	} else if mhReq.Network.ConnectType == 1 {
		three60OSReqNetwork.NetworkType = 1
	} else if mhReq.Network.ConnectType == 2 {
		three60OSReqNetwork.NetworkType = 2
	} else if mhReq.Network.ConnectType == 3 {
		three60OSReqNetwork.NetworkType = 3
	} else if mhReq.Network.ConnectType == 4 {
		three60OSReqNetwork.NetworkType = 4
	} else if mhReq.Network.ConnectType == 7 {
		three60OSReqNetwork.NetworkType = 5
	}
	if mhReq.Network.Carrier == 1 {
		three60OSReqNetwork.CarrierID = 70120
	} else if mhReq.Network.Carrier == 2 {
		three60OSReqNetwork.CarrierID = 70123
	} else if mhReq.Network.Carrier == 3 {
		three60OSReqNetwork.CarrierID = 70121
	} else {
		three60OSReqNetwork.CarrierID = 0
	}

	// device
	three60OSReqDevice := Three60OSRequestDeviceStu{}
	three60OSReqDeviceID := Three60OSRequestDeviceIDStu{}

	// 媒体屏幕宽高0,0 传 1080*2340
	three60OSReqDevice.ScreenWidth = mhReq.Device.ScreenWidth
	if three60OSReqDevice.ScreenWidth == 0 {
		three60OSReqDevice.ScreenWidth = 1080
	}
	three60OSReqDevice.ScreenHeight = mhReq.Device.ScreenHeight
	if three60OSReqDevice.ScreenHeight == 0 {
		three60OSReqDevice.ScreenHeight = 2340
	}
	if mhReq.Device.XDPI > 0 {
		three60OSReqDevice.ScreenDensity = mhReq.Device.XDPI
	}

	// device type
	three60OSReqDevice.DeviceType = 2
	three60OSReqDevice.Language = mhReq.Device.Language
	three60OSReqDevice.Country = mhReq.Device.Country
	three60OSReqDevice.TimeZone = mhReq.Device.TimeZone

	// device os
	if mhReq.Device.Os == "android" {
		three60OSReqDevice.OsType = 2
	} else if mhReq.Device.Os == "ios" {
		three60OSReqDevice.OsType = 1
	}

	// device osv
	three60OSReqDevice.Osv = mhReq.Device.OsVersion

	// device model
	three60OSReqDevice.Model = mhReq.Device.Model

	// device make
	three60OSReqDevice.Brand = mhReq.Device.Manufacturer
	three60OSReqDevice.Manufacture = mhReq.Device.Manufacturer

	// device uid
	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				three60OSReqDeviceID.DeviceID = utils.GetMd5(mhReq.Device.Imei)
				three60OSReqDeviceID.DeviceIDType = 1
				three60OSReqDeviceID.HashType = 1

			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				three60OSReqDeviceID.DeviceID = strings.ToLower(mhReq.Device.ImeiMd5)
				three60OSReqDeviceID.DeviceIDType = 1
				three60OSReqDeviceID.HashType = 1
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				three60OSReqDeviceID.DeviceID = mhReq.Device.Oaid
				three60OSReqDeviceID.DeviceIDType = 10
				three60OSReqDeviceID.HashType = 0
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from 360 os error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				three60OSReqDeviceID.DeviceID = utils.GetMd5(mhReq.Device.Idfa)
				three60OSReqDeviceID.DeviceIDType = 2
				three60OSReqDeviceID.HashType = 1

			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				three60OSReqDeviceID.DeviceID = strings.ToLower(mhReq.Device.IdfaMd5)
				three60OSReqDeviceID.DeviceIDType = 2
				three60OSReqDeviceID.HashType = 1
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
							threeOSReqDeviceUIDCaid.Caid = item.CAID
							threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

							three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
						threeOSReqDeviceUIDCaid.Caid = item.CAID
						threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

						three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				three60OSReqDevice.StartUpTime = tmpDeviceStartSec
				three60OSReqDevice.MbTime = tmpSystemUpdateSec
				three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
				three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
				three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
				three60OSReqDevice.Machine = tmpHardwareMachine
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					three60OSReqDeviceID.DeviceID = utils.GetMd5(mhReq.Device.Idfa)
					three60OSReqDeviceID.DeviceIDType = 2
					three60OSReqDeviceID.HashType = 1
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					three60OSReqDeviceID.DeviceID = strings.ToLower(mhReq.Device.IdfaMd5)
					three60OSReqDeviceID.DeviceIDType = 2
					three60OSReqDeviceID.HashType = 1
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
								threeOSReqDeviceUIDCaid.Caid = item.CAID
								threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

								three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
							threeOSReqDeviceUIDCaid.Caid = item.CAID
							threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

							three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					three60OSReqDevice.StartUpTime = tmpDeviceStartSec
					three60OSReqDevice.MbTime = tmpSystemUpdateSec
					three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
					three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
					three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
					three60OSReqDevice.Machine = tmpHardwareMachine
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					three60OSReqDeviceID.DeviceID = utils.GetMd5(mhReq.Device.Idfa)
					three60OSReqDeviceID.DeviceIDType = 2
					three60OSReqDeviceID.HashType = 1

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					three60OSReqDeviceID.DeviceID = strings.ToLower(mhReq.Device.IdfaMd5)
					three60OSReqDeviceID.DeviceIDType = 2
					three60OSReqDeviceID.HashType = 1
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
								threeOSReqDeviceUIDCaid.Caid = item.CAID
								threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

								three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
							threeOSReqDeviceUIDCaid.Caid = item.CAID
							threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

							three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					three60OSReqDevice.StartUpTime = tmpDeviceStartSec
					three60OSReqDevice.MbTime = tmpSystemUpdateSec
					three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
					three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
					three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
					three60OSReqDevice.Machine = tmpHardwareMachine
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from 360os error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug 360os android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					three60OSReqDevice.Osv = didRedisData.OsVersion

					// device model
					three60OSReqDevice.Model = didRedisData.Model

					// device make
					three60OSReqDevice.Brand = mhReq.Device.Manufacturer
					three60OSReqDevice.Manufacture = didRedisData.Manufacturer

					if didRedisData.Version == 1 {
						// device carrier
						if didRedisData.Carrier == 1 {
							three60OSReqNetwork.CarrierID = 70120
						} else if didRedisData.Carrier == 2 {
							three60OSReqNetwork.CarrierID = 70123
						} else if didRedisData.Carrier == 3 {
							three60OSReqNetwork.CarrierID = 70121
						} else {
							three60OSReqNetwork.CarrierID = 0
						}

						// device network
						if didRedisData.ConnectType == 0 {
							three60OSReqNetwork.NetworkType = 0
						} else if didRedisData.ConnectType == 1 {
							three60OSReqNetwork.NetworkType = 1
						} else if didRedisData.ConnectType == 2 {
							three60OSReqNetwork.NetworkType = 2
						} else if didRedisData.ConnectType == 3 {
							three60OSReqNetwork.NetworkType = 3
						} else if didRedisData.ConnectType == 4 {
							three60OSReqNetwork.NetworkType = 4
						} else if didRedisData.ConnectType == 7 {
							three60OSReqNetwork.NetworkType = 5
						}
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							three60OSReqDeviceID.DeviceID = utils.GetMd5(didRedisData.Imei)
							three60OSReqDeviceID.DeviceIDType = 1
							three60OSReqDeviceID.HashType = 1
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							three60OSReqDeviceID.DeviceID = strings.ToLower(didRedisData.ImeiMd5)
							three60OSReqDeviceID.DeviceIDType = 1
							three60OSReqDeviceID.HashType = 1
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							three60OSReqDeviceID.DeviceID = didRedisData.Oaid
							three60OSReqDeviceID.DeviceIDType = 10
							three60OSReqDeviceID.HashType = 0
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug 360os ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						three60OSReqDevice.Osv = didRedisData.OsVersion

						// device model
						three60OSReqDevice.Model = didRedisData.Model

						// device make
						three60OSReqDevice.Brand = mhReq.Device.Manufacturer
						three60OSReqDevice.Manufacture = didRedisData.Manufacturer

						if didRedisData.Version == 1 {
							// device carrier
							if didRedisData.Carrier == 1 {
								three60OSReqNetwork.CarrierID = 70120
							} else if didRedisData.Carrier == 2 {
								three60OSReqNetwork.CarrierID = 70123
							} else if didRedisData.Carrier == 3 {
								three60OSReqNetwork.CarrierID = 70121
							} else {
								three60OSReqNetwork.CarrierID = 0
							}

							// device network
							if didRedisData.ConnectType == 0 {
								three60OSReqNetwork.NetworkType = 0
							} else if didRedisData.ConnectType == 1 {
								three60OSReqNetwork.NetworkType = 1
							} else if didRedisData.ConnectType == 2 {
								three60OSReqNetwork.NetworkType = 2
							} else if didRedisData.ConnectType == 3 {
								three60OSReqNetwork.NetworkType = 3
							} else if didRedisData.ConnectType == 4 {
								three60OSReqNetwork.NetworkType = 4
							} else if didRedisData.ConnectType == 7 {
								three60OSReqNetwork.NetworkType = 5
							}
						}

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						// 初始化为空
						three60OSReqDeviceID.DeviceID = ""
						three60OSReqDeviceID.DeviceIDType = 0
						three60OSReqDeviceID.HashType = 0

						three60OSReqDevice.StartUpTime = ""
						three60OSReqDevice.MbTime = ""
						three60OSReqDevice.DiskTotal = ""
						three60OSReqDevice.MemTotal = ""
						three60OSReqDevice.DeviceNameMd5 = ""
						three60OSReqDevice.Machine = ""

						// 清空caid
						three60OSReqDevice.CaidMulti = three60OSReqDevice.CaidMulti[:0]

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								three60OSReqDeviceID.DeviceID = utils.GetMd5(didRedisData.Idfa)
								three60OSReqDeviceID.DeviceIDType = 2
								three60OSReqDeviceID.HashType = 1

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								three60OSReqDeviceID.DeviceID = strings.ToLower(didRedisData.IdfaMd5)
								three60OSReqDeviceID.DeviceIDType = 2
								three60OSReqDeviceID.HashType = 1

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
										threeOSReqDeviceUIDCaid.Caid = item.CAID
										threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

										three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
									threeOSReqDeviceUIDCaid.Caid = item.CAID
									threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

									three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								three60OSReqDevice.StartUpTime = tmpDeviceStartSec
								three60OSReqDevice.MbTime = tmpSystemUpdateSec
								three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
								three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
								three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
								three60OSReqDevice.Machine = tmpHardwareMachine

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									three60OSReqDeviceID.DeviceID = utils.GetMd5(didRedisData.Idfa)
									three60OSReqDeviceID.DeviceIDType = 2
									three60OSReqDeviceID.HashType = 1

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									three60OSReqDeviceID.DeviceID = strings.ToLower(didRedisData.IdfaMd5)
									three60OSReqDeviceID.DeviceIDType = 2
									three60OSReqDeviceID.HashType = 1

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
											threeOSReqDeviceUIDCaid.Caid = item.CAID
											threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

											three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
										threeOSReqDeviceUIDCaid.Caid = item.CAID
										threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

										three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									three60OSReqDevice.StartUpTime = tmpDeviceStartSec
									three60OSReqDevice.MbTime = tmpSystemUpdateSec
									three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
									three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
									three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
									three60OSReqDevice.Machine = tmpHardwareMachine

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									three60OSReqDeviceID.DeviceID = utils.GetMd5(didRedisData.Idfa)
									three60OSReqDeviceID.DeviceIDType = 2
									three60OSReqDeviceID.HashType = 1

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									three60OSReqDeviceID.DeviceID = strings.ToLower(didRedisData.IdfaMd5)
									three60OSReqDeviceID.DeviceIDType = 2
									three60OSReqDeviceID.HashType = 1

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
											threeOSReqDeviceUIDCaid.Caid = item.CAID
											threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

											three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										threeOSReqDeviceUIDCaid := Three60OSRequestDeviceCaidStu{}
										threeOSReqDeviceUIDCaid.Caid = item.CAID
										threeOSReqDeviceUIDCaid.Version = item.CAIDVersion

										three60OSReqDevice.CaidMulti = append(three60OSReqDevice.CaidMulti, threeOSReqDeviceUIDCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								// tmpDeviceBirthSec = didRedisData.DeviceBirthSec

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									three60OSReqDevice.StartUpTime = tmpDeviceStartSec
									three60OSReqDevice.MbTime = tmpSystemUpdateSec
									three60OSReqDevice.DiskTotal = tmpHarddiskSizeByte
									three60OSReqDevice.MemTotal = tmpPhysicalMemoryByte
									three60OSReqDevice.DeviceNameMd5 = tmpDeviceNameMd5
									three60OSReqDevice.Machine = tmpHardwareMachine

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							destConfigUA = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	three60OSReqDevice.DeviceID = append(three60OSReqDevice.DeviceID, three60OSReqDeviceID)

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from 360os error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from 360os error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// req imp
	three60OSReqAdspace := Three60OSRequestAdSpacesStu{
		AdspaceID:       platformPos.PlatformPosID,
		Width:           platformPos.PlatformPosWidth,
		Height:          platformPos.PlatformPosHeight,
		ImpressionNum:   mhReq.Pos.AdCount,
		OpenType:        0,
		BidFloor:        localPosFloorPrice,
		InteractionType: []int{0},
	}
	// 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	if platformPos.PlatformPosType == 1 {
		three60OSReqAdspace.AdspaceType = 1
	} else if platformPos.PlatformPosType == 2 {
		three60OSReqAdspace.AdspaceType = 2
	} else if platformPos.PlatformPosType == 3 {
		three60OSReqAdspace.AdspaceType = 3
	} else if platformPos.PlatformPosType == 4 {
		three60OSReqAdspace.AdspaceType = 4
	} else if platformPos.PlatformPosType == 9 || platformPos.PlatformPosType == 11 {
		three60OSReqAdspace.AdspaceType = 8
	} else {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// req app
	three60OSReqApp := Three60OSRequestAppStu{
		AppID:       platformPos.PlatformAppID,
		AppName:     platformPos.PlatformAppName,
		PackageName: GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		AppVersion:  platformPos.PlatformAppVersion,
	}
	var three60OSReqGps Three60OSRequestGpsStu
	if mhReq.Geo.Lng > 0 && mhReq.Geo.Lat > 0 {
		three60OSReqGps = Three60OSRequestGpsStu{
			Type:      1,
			Longitude: mhReq.Geo.Lng,
			Latgitude: mhReq.Geo.Lat,
			Timestamp: time.Now().Unix(),
		}
	}

	three60OSReq := Three60OSRequestStu{
		BID:     utils.GetMd5(bigdataUID),
		App:     three60OSReqApp,
		Device:  three60OSReqDevice,
		Network: three60OSReqNetwork,
		Gps:     three60OSReqGps,
		Secure:  0,
	}
	three60OSReq.AdSpaces = append(three60OSReq.AdSpaces, three60OSReqAdspace)

	if platformPos.PlatformAppIsReportUa == 1 {
		three60OSReq.UA = destConfigUA
	}

	jsonData, _ := json.Marshal(three60OSReq)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("360os url:", platformPos.PlatformAppUpURL+"?ver=1&appId="+platformPos.PlatformAppID)
	// fmt.Println("360os key:", platformPos.PlatformAppKey, platformPos.PlatformAppSecret)
	// fmt.Println("========================================================================================================================")

	fmt.Println("360os req:", string(jsonData))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// go models.BigDataHoloDebugJson2(bigdataUID+"&360os_req", string(jsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)

	// 加密请求data
	encryptedJsonData, err := encrypt_content([]byte(jsonData), []byte(platformPos.PlatformAppKey), []byte(platformPos.PlatformAppSecret))
	if err != nil {
		fmt.Println("360os encrypted error:", err)
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL+"?ver=1&appId="+platformPos.PlatformAppID, bytes.NewReader(encryptedJsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Content-Encrypt", "true")
	requestGet.Header.Add("Connection", "keep-alive")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println("360os resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	decryptedJsonData, err := decrypt_content([]byte(bodyContent), []byte(platformPos.PlatformAppKey), []byte(platformPos.PlatformAppSecret))
	if err != nil {
		fmt.Println("360os decrypted error:", err)
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	fmt.Println("360os resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(decryptedJsonData))

	// go models.BigDataHoloDebugJson2(bigdataUID+"&360os_resp", string(decryptedJsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)

	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	three60OSResp := Three60OSResponseStu{}
	err = json.Unmarshal(decryptedJsonData, &three60OSResp)
	if err != nil {
		fmt.Println(err)
	}

	// tmpRespByte, _ := json.Marshal(three60OSResp)
	// fmt.Println("360os resp code:", resp.StatusCode)
	// fmt.Println("360os resp:", string(tmpRespByte))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if three60OSResp.ErrorCode != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = three60OSResp.ErrorCode

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(three60OSResp.Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = three60OSResp.ErrorCode

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(three60OSResp.Ads[0].Creative) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = three60OSResp.ErrorCode

		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := three60OSResp.Ads[0].Creative

	if len(respDataListItemStu) > 1 {
		sort.Sort(three60OSJsonEcpmSort(respDataListItemStu))
	}

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		three60OSEcpm := item.Price

		respTmpPrice = respTmpPrice + three60OSEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if three60OSEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			three60OSEcpm = platformPos.PlatformPosEcpm
		}

		three60OSLossNoticeURL := ""
		for _, monitorItem := range item.EventTrack {
			if monitorItem.EventType == "23" {
				three60OSLossNoticeURL = string(monitorItem.NotifyURL)
			}
		}

		// fmt.Println("gdt_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(three60OSEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > three60OSEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)

			continue
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(three60OSEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// 不支持小程序类型, mp_id, mp_path???
		if item.AdmType == "9" {
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
			continue
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := item.Native.Title.Text
		respDescription := item.Native.Desc
		respIconURL := ""
		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		for _, imageItem := range item.Native.ImageList {
			if imageItem.Type == 1 {
				respImageURL = imageItem.URL
				respImageWidth = imageItem.Width
				respImageHeight = imageItem.Height
			} else if imageItem.Type == 2 {
				respIconURL = imageItem.URL
			}
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		if mhReq.Device.Os == "android" {
			if item.InteractionType == "3" {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.InteractionObject.URL
				respListItemMap["download_url"] = item.DownloadObject.URL
			} else {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.InteractionObject.URL
				respListItemMap["landpage_url"] = item.InteractionObject.URL
			}

			if len(item.DownloadObject.AppName) > 0 {
				respListItemMap["app_name"] = item.DownloadObject.AppName
			} else {
			}
			if len(item.DownloadObject.Author) > 0 {
				respListItemMap["publisher"] = item.DownloadObject.Author
			}
			if len(item.DownloadObject.AppVersion) > 0 {
				respListItemMap["app_version"] = item.DownloadObject.AppVersion
			}
			if len(item.DownloadObject.PrivacyURL) > 0 {
				respListItemMap["privacy_url"] = item.DownloadObject.PrivacyURL
			}
			if len(item.DownloadObject.PermissionURL) > 0 {
				respListItemMap["permission_url"] = item.DownloadObject.PermissionURL
			}
		} else if mhReq.Device.Os == "ios" {
			if item.InteractionType == "3" {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.InteractionObject.URL
				respListItemMap["download_url"] = item.DownloadObject.URL
			} else {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.InteractionObject.URL
				respListItemMap["landpage_url"] = item.InteractionObject.URL
			}
		}

		if len(string(item.Native.Video.URL)) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.Native.Video.Duration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(item.Native.Video.Duration))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
					continue
				}

				respListVideoItemMap["duration"] = int(item.Native.Video.Duration) * 1000
			}
			respListVideoItemMap["width"] = int(item.Native.Video.Width)
			respListVideoItemMap["height"] = int(item.Native.Video.Height)
			respListVideoItemMap["video_url"] = string(item.Native.Video.URL)

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(item.Native.Video.Width), int(item.Native.Video.Height))
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
				continue
			}

			// cover_url
			if len(respImageURL) > 0 {
				respListVideoItemMap["cover_url"] = respImageURL
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			isVideoTrack := 0
			// event track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range item.EventTrack {
				if monitorItem.EventType == "14" {
					respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, string(monitorItem.NotifyURL))
				}
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				isVideoTrack = 1
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range item.EventTrack {
				if monitorItem.EventType == "16" {
					respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, string(monitorItem.NotifyURL))
				}
			}
			if len(respListVideoEndEventTrackURLMap) > 0 {
				isVideoTrack = 1

				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if isVideoTrack == 1 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL

				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		if len(string(item.CreativeID)) > 0 {
			respListItemMap["crid"] = item.CreativeID
		}

		// deeplink
		destDeepLink := ""
		if len(string(item.InteractionObject.Deeplink)) > 0 {
			destDeepLink = string(item.InteractionObject.Deeplink)

			respListItemMap["deep_link"] = destDeepLink

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, monitorItem := range item.EventTrack {
				if monitorItem.EventType == "9" {
					respListItemDeepLinkArray = append(respListItemDeepLinkArray, string(monitorItem.NotifyURL))
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			// if platformPos.PlatformAppIsDeepLinkFailed == 1 {
			// 	var respListItemDeepLinkFailedArray []string

			// 	if len(item.ConversionLink) > 0 {
			// 		tmpConversionLink := strings.Replace(item.ConversionLink, "__ACTION_ID__", "249", -1)
			// 		respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, tmpConversionLink)
			// 	}

			// 	mhDPFailedParams := url.Values{}
			// 	mhDPFailedParams.Add("result", "1")
			// 	mhDPFailedParams.Add("reason", "3")
			// 	mhDPFailedParams.Add("log", bigdataParams)

			// 	respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			// 	respListItemConv11Map := map[string]interface{}{}
			// 	respListItemConv11Map["conv_type"] = 11
			// 	respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

			// 	respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			// }

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(string(item.DownloadObject.PackageName)) > 0 {
			respListItemMap["package_name"] = string(item.DownloadObject.PackageName)

			destPackageName = string(item.DownloadObject.PackageName)
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, string(item.DownloadObject.AppName), destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, three60OSEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		three60OSWinNoticeURL := ""
		for _, monitorItem := range item.EventTrack {
			if monitorItem.EventType == "22" {
				three60OSWinNoticeURL = string(monitorItem.NotifyURL)
			}
		}

		if len(three60OSWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, three60OSWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link 360os
		for _, showItem := range item.EventTrack {
			if showItem.EventType == "1" {
				tmpItem := string(showItem.NotifyURL)
				respListItemImpArray = append(respListItemImpArray, tmpItem)
			}
		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, clickItem := range item.EventTrack {
			if clickItem.EventType == "2" {
				tmpItem := clickItem.NotifyURL

				tmpItem = strings.Replace(tmpItem, "__DOWN_X__", tmpDownX, -1)
				tmpItem = strings.Replace(tmpItem, "__DOWN_Y__", tmpDownY, -1)
				tmpItem = strings.Replace(tmpItem, "__UP_X__", tmpUpX, -1)
				tmpItem = strings.Replace(tmpItem, "__UP_Y__", tmpUpY, -1)

				if IsAdIDReplaceXYByOS(c, mhReq) {
					if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
						tmpItem = tmpItem + "&is_adid_replace_xy=0"
					}
					if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
						tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
						respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
					}
				}
				respListItemClkArray = append(respListItemClkArray, tmpItem)
			}
		}
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(three60OSLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, three60OSLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlThree60OSPriceFailedURL(three60OSLossNoticeURL, &bigdataExtra)
			continue
		}

		// 上报竞价失败, 无需回传竞价成功
		if len(three60OSLossNoticeURL) > 0 {
			var tmpLossNoticeURLs []string
			tmpLossNoticeURLs = append(tmpLossNoticeURLs, three60OSLossNoticeURL)
			respListItemMap["demand_loss_notice_urls"] = tmpLossNoticeURLs
		}

		respListItemMap["p_ecpm"] = three60OSEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("360os resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// 360os resp
	respGdt := models.MHUpResp{}
	respGdt.RespData = &mhResp
	respGdt.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respGdt
}

type Three60OSRequestStu struct {
	BID      string                        `json:"bid,omitempty"`
	UA       string                        `json:"ua,omitempty"`
	App      Three60OSRequestAppStu        `json:"app,omitempty"`
	Device   Three60OSRequestDeviceStu     `json:"device,omitempty"`
	Network  Three60OSRequestNetworkStu    `json:"network,omitempty"`
	AdSpaces []Three60OSRequestAdSpacesStu `json:"adspaces,omitempty"`
	Gps      Three60OSRequestGpsStu        `json:"gps,omitempty"`
	Secure   int                           `json:"secure,omitempty"`
}

type Three60OSRequestGpsStu struct {
	Type      int     `json:"type,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
	Latgitude float64 `json:"latgitude,omitempty"`
	Timestamp int64   `json:"timestamp,omitempty"`
}

type Three60OSRequestAppStu struct {
	AppID       string `json:"app_id,omitempty"`
	AppName     string `json:"app_name,omitempty"`
	PackageName string `json:"package_name,omitempty"`
	AppVersion  string `json:"app_version,omitempty"`
}

type Three60OSRequestNetworkStu struct {
	IP          string `json:"ip,omitempty"`
	NetworkType int    `json:"network_type"`
	CarrierID   int    `json:"carrier_id"`
}

type Three60OSRequestAdSpacesStu struct {
	AdspaceID       string   `json:"adspace_id,omitempty"`
	AdspaceType     int      `json:"adspace_type,omitempty"`
	Width           int      `json:"width,omitempty"`
	Height          int      `json:"height,omitempty"`
	ImpressionNum   int      `json:"impression_num,omitempty"`
	OpenType        int      `json:"open_type"`
	InteractionType []int    `json:"interaction_type,omitempty"`
	InstalledPkgs   []string `json:"installed_pkgs,omitempty"`
	BidFloor        int      `json:"bid_floor,omitempty"`
}

type Three60OSRequestDeviceStu struct {
	DeviceID      []Three60OSRequestDeviceIDStu   `json:"device_id,omitempty"`
	OsType        int                             `json:"os_type,omitempty"`
	Osv           string                          `json:"os_version,omitempty"`
	Brand         string                          `json:"brand,omitempty"`
	Model         string                          `json:"model,omitempty"`
	DeviceType    int                             `json:"device_type,omitempty"`
	Language      string                          `json:"language,omitempty"`
	Country       string                          `json:"country,omitempty"`
	ScreenWidth   int                             `json:"screen_width,omitempty"`
	ScreenHeight  int                             `json:"screen_height,omitempty"`
	ScreenDensity float32                         `json:"screen_density,omitempty"`
	CaidMulti     []Three60OSRequestDeviceCaidStu `json:"caid,omitempty"`
	Manufacture   string                          `json:"manufacture,omitempty"`
	TimeZone      string                          `json:"ostimezone,omitempty"`    // 系统开机时间戳
	StartUpTime   string                          `json:"start_up_time,omitempty"` // 系统开机时间戳
	MbTime        string                          `json:"mb_time,omitempty"`       // 系统编译时间戳
	DiskTotal     string                          `json:"disk_total,omitempty"`    // 磁盘总空间
	MemTotal      string                          `json:"mem_total,omitempty"`     // 内存总空间
	UpdateMark    string                          `json:"update_mark,omitempty"`
	BootMark      string                          `json:"boot_mark,omitempty"`
	DeviceNameMd5 string                          `json:"device_name_md5,omitempty"` // ios 设备名称 md5
	Machine       string                          `json:"machine,omitempty"`         // ios 设备 machine
}

type Three60OSRequestDeviceIDStu struct {
	DeviceID     string `json:"device_id,omitempty"`
	DeviceIDType int    `json:"device_id_type,omitempty"`
	HashType     int    `json:"hash_type"`
}

type Three60OSRequestDeviceCaidStu struct {
	Caid    string `json:"caid,omitempty"`
	Version string `json:"ver,omitempty"`
}

// Three60OSResponseStu ...
type Three60OSResponseStu struct {
	BID       string                   `json:"bid,omitempty"`
	Ads       []Three60OSResponseAdStu `json:"ads,omitempty"`
	ErrorCode int                      `json:"error_code,omitempty"`
	ErrorMsg  string                   `json:"error_msg,omitempty"`
}

type Three60OSResponseAdStu struct {
	AdspaceID string                           `json:"adspace_id,omitempty"`
	Creative  []Three60OSResponseAdCreativeStu `json:"creative,omitempty"`
}

type Three60OSResponseAdCreativeStu struct {
	CreativeID        string                                          `json:"banner_id,omitempty"`
	OpenType          int                                             `json:"open_type,omitempty"`
	InteractionType   string                                          `json:"interaction_type,omitempty"`
	InteractionObject Three60OSResponseAdCreativeInteractionObjectStu `json:"interaction_object,omitempty"`
	AdmType           string                                          `json:"adm_type,omitempty"`
	Native            Three60OSResponseAdCreativeNativeStu            `json:"native,omitempty"`
	EventTrack        []Three60OSResponseAdCreativeEventTrackStu      `json:"event_track,omitempty"`
	AdSpaceType       int                                             `json:"adSpaceType,omitempty"`
	Price             int                                             `json:"price,omitempty"`
	DownloadObject    Three60OSResponseAdCreativeDownloadStu          `json:"download_object,omitempty"`
	MPID              string                                          `json:"mp_id,omitempty"`
	MPPath            string                                          `json:"mp_path,omitempty"`
	CoordinateValue   []Three60OSResponseAdCreativeCoordinateStu      `json:"coordinateValue,omitempty"`
}

type Three60OSResponseAdCreativeInteractionObjectStu struct {
	URL      string `json:"url,omitempty"`
	IntroURL string `json:"intro_url,omitempty"`
	Deeplink string `json:"deeplink,omitempty"`
}

type Three60OSResponseAdCreativeNativeStu struct {
	Title     Three60OSResponseAdCreativeNativeTitleStu   `json:"title,omitempty"`
	Desc      string                                      `json:"desc,omitempty"`
	ButtonTxt string                                      `json:"buttonTxt,omitempty"`
	ImageList []Three60OSResponseAdCreativeNativeImageStu `json:"imgList,omitempty"`
	Video     Three60OSResponseAdCreativeNativeVideoStu   `json:"vid,omitempty"`
}

type Three60OSResponseAdCreativeNativeImageStu struct {
	URL    string `json:"url,omitempty"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
	MD5    string `json:"md5,omitempty"`
	Type   int    `json:"type,omitempty"`
}

type Three60OSResponseAdCreativeNativeVideoStu struct {
	URL      string `json:"url,omitempty"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	CoverURL string `json:"imgSrc,omitempty"`
	Duration int    `json:"duration,omitempty"`
}

type Three60OSResponseAdCreativeNativeTitleStu struct {
	Text string `json:"text,omitempty"`
}

type Three60OSResponseAdCreativeEventTrackStu struct {
	EventType string `json:"event_type,omitempty"`
	NotifyURL string `json:"notify_url,omitempty"`
	Plat      string `json:"plat,omitempty"`
}

type Three60OSResponseAdCreativeDownloadStu struct {
	URL             string `json:"url,omitempty"`
	PackageName     string `json:"package_name,omitempty"`
	Size            int    `json:"size,omitempty"`
	AppVersion      string `json:"app_version,omitempty"`
	InstallSilence  bool   `json:"install_silence,omitempty"`
	OpenSilence     bool   `json:"open_silence,omitempty"`
	DownType        int    `json:"down_type,omitempty"`
	AppName         string `json:"app_name,omitempty"`
	Author          string `json:"author,omitempty"`
	AppPermissions  string `json:"app_permissions,omitempty"`
	PermissionURL   string `json:"permission_url,omitempty"`
	PrivacyURL      string `json:"privacy_url,omitempty"`
	FunctionDescURL string `json:"function_desc_url,omitempty"`
}

type Three60OSResponseAdCreativeCoordinateStu struct {
	CoordinateType int    `json:"coordinateType,omitempty"`
	CoordinateX    string `json:"coordinateX,omitempty"`
	CoordinateY    string `json:"coordinateY,omitempty"`
	MaxAcc         int    `json:"maxAcc,omitempty"`
}

type three60OSJsonEcpmSort []Three60OSResponseAdCreativeStu

func (s three60OSJsonEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s three60OSJsonEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s three60OSJsonEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Price > s[j].Price
}

// curl price failed
func curlThree60OSPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("360os win failed url panic:", err)
				}
			}()
			curlBaiQingTengNurl(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}

// 360文档
// https://github.com/higxuan/aes-go-demo/blob/main/main.go
func encrypt_content(src, key, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return []byte{}, err
	}

	if 0 == len(src) {
		return []byte{}, errors.New("AES/CBC/PKCS5PADDING encrypt failed, src empty")
	}

	ecbEncoder := cipher.NewCBCEncrypter(block, iv)
	content := PKCS5_padding(src, block.BlockSize())
	if len(content)%aes.BlockSize != 0 {
		return []byte{}, errors.New("AES/CBC/PKCS5PADDING encrypt content not a multiple of the block size")
	}

	encrypted := make([]byte, len(content))
	ecbEncoder.CryptBlocks(encrypted, content)

	if err != nil {
		return []byte{}, err
	}
	base64Str := base64.StdEncoding.EncodeToString(encrypted)
	urlEncodeStr := url.QueryEscape(base64Str)
	return []byte(urlEncodeStr), nil
}

func decrypt_content(encrypted, key, iv []byte) (decryptContent []byte, decryptError error) {
	urlDecode, err := url.QueryUnescape(string(encrypted))
	if err != nil {
		return []byte{}, err
	}

	plainContent, err := base64.StdEncoding.DecodeString(urlDecode)

	if err != nil {
		return []byte{}, err
	}

	decryptContent = []byte{}

	block, err := aes.NewCipher(key)
	if err != nil {
		decryptError = err
		return
	}

	if 0 == len(plainContent) {
		decryptError = errors.New("AES/CBC/PKCS5PADDING decrypt failed, src empty")
		return
	}

	ecbDecoder := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(plainContent))
	ecbDecoder.CryptBlocks(decrypted, plainContent)

	decryptContent = PKCS5_trimming(decrypted)

	if err != nil {
		return []byte{}, err
	}
	return decryptContent, nil

}

func PKCS5_padding(cipherText []byte, blockSize int) []byte {
	padding := blockSize - len(cipherText)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(cipherText, padText...)
}

func PKCS5_trimming(encryptText []byte) []byte {
	padding := encryptText[len(encryptText)-1]
	return encryptText[:len(encryptText)-int(padding)]
}
