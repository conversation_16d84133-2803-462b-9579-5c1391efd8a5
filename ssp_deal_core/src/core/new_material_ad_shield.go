package core

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"strconv"
	"strings"
	"time"
)

func IsNewMaterialAdShieldOK(ctx context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, item *models.MHRespDataItem) (bool, int) {
	// 创建结果数组，用于收集所有错误
	var shieldResults []*ShieldResult

	// 白名单检查
	whiteResult := checkMaterialAdShieldWhite(ctx, mhReq, platformPos, item)
	if !whiteResult.IsOK {
		// 如果白名单检查不通过，添加到结果集
		shieldResults = append(shieldResults, whiteResult)
	} else {
		// 白名单通过，再检查黑名单
		blackResults := checkMaterialAdShieldBlack(ctx, mhReq, platformPos, item)
		// 将所有黑名单检查结果添加到结果集
		shieldResults = append(shieldResults, blackResults...)
	}

	// 如果没有错误，返回成功
	if len(shieldResults) == 0 {
		return true, 900000
	}

	marshal, _ := json.Marshal(shieldResults)

	// Create a deep copy of the item to avoid race conditions in the goroutine
	itemCopy := *item // Copy the struct

	// Create deep copies of nested fields
	if item.Video != nil {
		videoCopy := *item.Video
		itemCopy.Video = &videoCopy
	}

	if len(item.Image) > 0 {
		imageCopy := make([]models.MHRespImage, len(item.Image))
		copy(imageCopy, item.Image)
		itemCopy.Image = imageCopy
	}

	go models.MaterialShieldStatisticsRawDataKafka(ctx, bigdataUID, mhReq, localPos, platformPos, &itemCopy, marshal, 2)
	fmt.Println(string(marshal))

	firstError := shieldResults[0]
	return false, firstError.Code
}

// 白名单检查逻辑，返回单个结果（有问题时返回 *ShieldResult，否则返回 nil）
func checkMaterialAdShieldWhite(ctx context.Context, mhReq *models.MHReq, platformPos *models.PlatformPosStu, item *models.MHRespDataItem) *ShieldResult {
	// 创建默认成功结果
	result := &ShieldResult{
		IsOK: true,
		Code: 900000,
	}

	// 原 isNewWhile 函数的逻辑开始
	corpID := strconv.Itoa(platformPos.PlatformAppCorpID)
	if platformPos.PlatformMediaID == "99" {
		corpID = "1"
	}

	platformKey := "go_ad_fhield_white_platform_" + string(platformPos.PlatformMediaID) + "_" + corpID
	appKey := "go_ad_fhield_white_app_" + platformPos.PlatformAppID
	posKey := "go_ad_fhield_white_pos_" + platformPos.PlatformPosID
	platformValue, _ := db.GlbBigCacheMinute.Get(platformKey)
	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(platformValue) == 0 && len(posValue) == 0 {
		return result
	}

	var adShield models.AdShieldBigCache
	if len(platformValue) > 0 && len(appValue) == 0 && len(posValue) == 0 {
		_ = json.Unmarshal(platformValue, &adShield)
	}
	if len(appValue) > 0 && len(posValue) == 0 {
		_ = json.Unmarshal(appValue, &adShield)
	}
	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &adShield)
	}

	expUrl := adShield.ExpUrl
	adUrl := adShield.AdUrl
	shieldKey := adShield.ShieldKey
	materialUrl := adShield.MaterialUrl
	packageName := adShield.PackageName
	timeList := adShield.TimeList
	regionList := adShield.RegionList
	adShieldType := adShield.AdShieldType

	if adShieldType > 0 {
		switch adShieldType {
		case 1: // 下载
			if item.InteractType == 0 {
				return createErrorResult(900115, BlockTypeAdType, "download")
			}
		case 2: // h5
			if item.InteractType == 1 {
				return createErrorResult(900115, BlockTypeAdType, "h5")
			}
		}
	}

	if len(expUrl) > 0 {
		expUrlArray := strings.Split(expUrl, ",")
		for _, impressionUrl := range item.ImpressionLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(impressionUrl, expUrlItem) {
					return result
				}
			}
		}
		for _, clickUrl := range item.ClickLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(clickUrl, expUrlItem) {
					return result
				}
			}
		}
	}

	if len(adUrl) > 0 {
		adUrlArray := strings.Split(adUrl, ",")
		for _, adUrlItem := range adUrlArray {
			if (len(item.LandpageURL) > 0 && strings.Contains(item.LandpageURL, adUrlItem)) ||
				(len(item.DownloadURL) > 0 && strings.Contains(item.DownloadURL, adUrlItem)) ||
				(len(item.DeepLink) > 0 && strings.Contains(item.DeepLink, adUrlItem)) ||
				(len(item.AdURL) > 0 && strings.Contains(item.AdURL, adUrlItem)) {
				return result
			}
		}
	}

	if len(shieldKey) > 0 {
		shieldKeyArray := strings.Split(shieldKey, ",")
		for _, shieldKeyItem := range shieldKeyArray {
			if (len(item.Title) > 0 && strings.Contains(item.Title, shieldKeyItem)) ||
				(len(item.Description) > 0 && strings.Contains(item.Description, shieldKeyItem)) {
				return result
			}
		}
	}

	if len(materialUrl) > 0 {
		materialUrlArray := strings.Split(materialUrl, ",")
		for _, materialUrlItem := range materialUrlArray {
			if (len(item.Image) > 0 && strings.Contains(item.Image[0].URL, materialUrlItem)) ||
				(item.Video != nil && len(item.Video.VideoURL) > 0 && strings.Contains(item.Video.VideoURL, materialUrlItem)) ||
				(item.Video != nil && len(item.Video.CoverURL) > 0 && strings.Contains(item.Video.CoverURL, materialUrlItem)) ||
				(len(item.IconURL) > 0 && strings.Contains(item.IconURL, materialUrlItem)) {
				return result
			}
		}
	}

	if len(packageName) > 0 {
		packageNameArray := strings.Split(packageName, ",")
		for _, packageNameItem := range packageNameArray {
			if strings.Contains(item.PackageName, packageNameItem) {
				return result
			}
		}
	}

	if len(timeList) > 0 {
		hh := time.Now().Format("15")
		timeListArray := strings.Split(timeList, ",")
		for _, timeItem := range timeListArray {
			if strings.Contains(hh, timeItem) {
				return result
			}
		}
	}

	if len(regionList) > 0 {
		regionListArray := strings.Split(regionList, ",")
		for _, regionItem := range regionListArray {
			if strings.Contains(mhReq.Device.IPProvince, regionItem) ||
				strings.Contains(mhReq.Device.IPCity, regionItem) {
				return result
			}
		}
	}

	return createErrorResult(900111, BlockTypeTimeRegion, "no_match")
}

// 黑名单检查逻辑，返回所有错误（有问题时返回 []*ShieldResult，否则返回空 slice）
func checkMaterialAdShieldBlack(ctx context.Context, mhReq *models.MHReq, platformPos *models.PlatformPosStu, item *models.MHRespDataItem) []*ShieldResult {
	corpID := "1"
	if platformPos.PlatformMediaID != "99" {
		corpID = strconv.Itoa(platformPos.PlatformAppCorpID)
	}

	platformKey := "go_ad_fhield_platform_" + platformPos.PlatformMediaID + "_" + corpID
	appKey := "go_ad_fhield_app_" + platformPos.PlatformAppID
	posKey := "go_ad_fhield_pos_" + platformPos.PlatformPosID
	platformValue, _ := db.GlbBigCacheMinute.Get(platformKey)
	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(platformValue) == 0 && len(posValue) == 0 {
		return nil
	}

	var adShield models.AdShieldBigCache
	if len(platformValue) > 0 && len(appValue) == 0 && len(posValue) == 0 {
		_ = json.Unmarshal(platformValue, &adShield)
	}
	if len(appValue) > 0 && len(posValue) == 0 {
		_ = json.Unmarshal(appValue, &adShield)
	}
	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &adShield)
	}

	expUrl := adShield.ExpUrl
	adUrl := adShield.AdUrl
	shieldKey := adShield.ShieldKey
	materialUrl := adShield.MaterialUrl
	packageName := adShield.PackageName
	timeList := adShield.TimeList
	regionList := adShield.RegionList
	adShieldType := adShield.AdShieldType

	var results []*ShieldResult

	if adShieldType > 0 {
		switch adShieldType {
		case 1: // 下载
			if item.InteractType == 0 {
				results = append(results, createErrorResult(900111, BlockTypeAdType, "download"))
			}
		case 2: // h5
			if item.InteractType == 1 {
				results = append(results, createErrorResult(900111, BlockTypeAdType, "h5"))
			}
		}
	}

	if len(expUrl) > 0 {
		expUrlArray := strings.Split(expUrl, ",")
		for _, impressionUrl := range item.ImpressionLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(impressionUrl, expUrlItem) {
					results = append(results, createErrorResult(900111, BlockTypeExpUrl, expUrlItem))
				}
			}
		}
		for _, clickUrl := range item.ClickLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(clickUrl, expUrlItem) {
					results = append(results, createErrorResult(900111, BlockTypeExpUrl, expUrlItem))
				}
			}
		}
	}

	if len(adUrl) > 0 {
		adUrlArray := strings.Split(adUrl, ",")
		for _, adUrlItem := range adUrlArray {
			if len(item.LandpageURL) > 0 {
				if strings.Contains(item.LandpageURL, adUrlItem) {
					results = append(results, createErrorResult(900113, BlockTypeAdURL, adUrlItem))
				}
			}
			if len(item.DownloadURL) > 0 {
				if strings.Contains(item.DownloadURL, adUrlItem) {
					results = append(results, createErrorResult(900113, BlockTypeAdURL, adUrlItem))
				}
			}
			if len(item.DeepLink) > 0 {
				if strings.Contains(item.DeepLink, adUrlItem) {
					results = append(results, createErrorResult(900113, BlockTypeAdURL, adUrlItem))
				}
			}
			if len(item.AdURL) > 0 {
				if strings.Contains(item.AdURL, adUrlItem) {
					results = append(results, createErrorResult(900113, BlockTypeAdURL, adUrlItem))
				}
			}
		}
	}

	if len(shieldKey) > 0 {
		shieldKeyArray := strings.Split(shieldKey, ",")
		for _, shieldKeyItem := range shieldKeyArray {
			if len(item.Title) > 0 {
				if strings.Contains(item.Title, shieldKeyItem) {
					results = append(results, createErrorResult(900111, BlockTypeKeyword, shieldKeyItem))
				}
			}
			if len(item.Description) > 0 {
				if strings.Contains(item.Description, shieldKeyItem) {
					results = append(results, createErrorResult(900112, BlockTypeKeyword, shieldKeyItem))
				}
			}
		}
	}

	if len(materialUrl) > 0 {
		materialUrlArray := strings.Split(materialUrl, ",")
		for _, materialUrlItem := range materialUrlArray {
			if len(item.Image) > 0 {
				if strings.Contains(item.Image[0].URL, materialUrlItem) {
					results = append(results, createErrorResult(900115, BlockTypeMaterialURL, materialUrlItem))
				}
			}
			if item.Video != nil && len(item.Video.VideoURL) > 0 {
				if strings.Contains(item.Video.VideoURL, materialUrlItem) {
					results = append(results, createErrorResult(900115, BlockTypeMaterialURL, materialUrlItem))
				}
				if strings.Contains(item.Video.CoverURL, materialUrlItem) {
					results = append(results, createErrorResult(900115, BlockTypeMaterialURL, materialUrlItem))
				}
			}
			if len(item.IconURL) > 0 {
				if strings.Contains(item.IconURL, materialUrlItem) {
					results = append(results, createErrorResult(900115, BlockTypeMaterialURL, materialUrlItem))
				}
			}
		}
	}

	if len(packageName) > 0 {
		packageNameArray := strings.Split(packageName, ",")
		for _, packageNameItem := range packageNameArray {
			if strings.Contains(item.PackageName, packageNameItem) {
				results = append(results, createErrorResult(900114, BlockTypePackageName, packageNameItem))
			}
		}
	}

	if len(timeList) > 0 {
		hh := time.Now().Format("15")
		timeListArray := strings.Split(timeList, ",")
		for _, timeItem := range timeListArray {
			if strings.Contains(hh, timeItem) {
				results = append(results, createErrorResult(900115, BlockTypeTime, timeItem))
			}
		}
	}

	if len(regionList) > 0 {
		regionListArray := strings.Split(regionList, ",")
		for _, regionItem := range regionListArray {
			if strings.Contains(mhReq.Device.IPProvince, regionItem) {
				results = append(results, createErrorResult(900115, BlockTypeRegion, regionItem))
			}
			if strings.Contains(mhReq.Device.IPCity, regionItem) {
				results = append(results, createErrorResult(900115, BlockTypeRegion, regionItem))
			}
		}
	}

	return results
}
