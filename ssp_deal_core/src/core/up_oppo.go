package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromOPPO ...
func GetFromOPPO(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from oppo")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// 应用名称：喜马拉雅
	// 包名：com.ximalaya.ting.android
	// 需调试的广告位类型：开屏，原生，激励视频

	// appid：101001221
	// 开屏posid：24693
	// 原生大图posid：24694
	// 激励视频posid：24695
	// 开屏测试
	// platformPos.PlatformAppID = "101001221"
	// platformPos.PlatformPosID = "24693"
	// platformPos.PlatformPosType = 2
	// platformPos.PlatformAppBundle = "com.ximalaya.ting.android"
	// platformPos.PlatformPosIsDebug = 1
	// 激励视频测试
	// platformPos.PlatformAppID = "101001221"
	// platformPos.PlatformPosID = "24695"
	// platformPos.PlatformPosType = 11
	// platformPos.PlatformAppBundle = "com.ximalaya.ting.android"
	// platformPos.PlatformPosIsDebug = 1

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	var isLogicPixel = 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["appId"] = platformPos.PlatformAppID
	reqAppInfoMap["pkgname"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	reqAppInfoMap["verName"] = platformPos.PlatformAppVersion

	reqPosInfoMap := map[string]interface{}{}
	reqPosInfoMap["id"] = platformPos.PlatformPosID
	// 1. Banner, 2. 插屏, 4. 开屏, 8. 原生, 64. 激励视频
	// <el-radio label="1">Banner</el-radio>
	// <el-radio label="2">开屏</el-radio>
	// <el-radio label="3">插屏</el-radio>
	// <el-radio label="4">原生</el-radio>
	// <el-radio label="8">原生模版</el-radio>
	// <el-radio label="7">原生2.0</el-radio>
	// <el-radio label="5">原生视频</el-radio>
	// <el-radio label="13">贴片</el-radio>
	// <el-radio label="6">信息流</el-radio>
	// <el-radio label="9">全屏视频</el-radio>
	// <el-radio label="11">激励视频</el-radio>
	// <el-radio label="10">视频内容联盟</el-radio>
	// <el-radio label="12">视频内容联盟组件</el-radio>
	if platformPos.PlatformPosType == 1 {
		reqPosInfoMap["posType"] = 1
	} else if platformPos.PlatformPosType == 3 {
		reqPosInfoMap["posType"] = 2
	} else if platformPos.PlatformPosType == 2 {
		reqPosInfoMap["posType"] = 4
	} else if platformPos.PlatformPosType == 4 {
		reqPosInfoMap["posType"] = 8
	} else if platformPos.PlatformPosType == 11 {
		reqPosInfoMap["posType"] = 64
	}
	reqPosInfoMap["w"] = platformPos.PlatformPosWidth
	reqPosInfoMap["h"] = platformPos.PlatformPosHeight

	reqDeviceInfoMap := map[string]interface{}{}

	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false
			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true

				// reqDeviceInfoMap["imei"] = mhReq.Device.Imei
				reqDeviceInfoMap["imeiMd5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true

				reqDeviceInfoMap["imeiMd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	reqDeviceInfoMap["anId"] = mhReq.Device.AndroidID

			// 	extraReportParams = extraReportParams + ",android_id"
			// }
			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				reqDeviceInfoMap["oaId"] = mhReq.Device.Oaid
			}
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	reqDeviceInfoMap["anId"] = mhReq.Device.AndroidID

			// 	extraReportParams = extraReportParams + ",android_id"
			// }
			if len(mhReq.Device.Oaid) > 0 {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}

	} else if mhReq.Device.Os == "ios" {
		fmt.Println("get from oppo error")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// is debug
	if platformPos.PlatformPosIsDebug == 1 {
		mhReq.Device.Manufacturer = "OPPO"
	}

	reqDeviceInfoMap["brand"] = mhReq.Device.Manufacturer

	if len(mhReq.Device.Model) > 0 {
		reqDeviceInfoMap["model"] = mhReq.Device.Model
	}
	if len(mhReq.Device.OsVersion) > 0 {
		reqDeviceInfoMap["anVer"] = mhReq.Device.OsVersion
	}
	if mhReq.Device.ScreenWidth > 0 {
		reqDeviceInfoMap["w"] = mhReq.Device.ScreenWidth
	}
	if mhReq.Device.ScreenHeight > 0 {
		reqDeviceInfoMap["h"] = mhReq.Device.ScreenHeight
	}

	reqConnectType := "UNKNOWN"
	if mhReq.Network.ConnectType == 0 {
		// 未知
		reqConnectType = "UNKNOWN"
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		reqConnectType = "WIFI"
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		reqConnectType = "2G"
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		reqConnectType = "3G"
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		reqConnectType = "4G"
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		reqConnectType = "5G"
	}
	reqDeviceInfoMap["connectionType"] = reqConnectType

	reqDeviceInfoMap["carrier"] = mhReq.Network.Carrier

	// ip
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	// ua
	reqDeviceInfoMap["ua"] = destConfigUA

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// delete(reqDeviceInfoMap, "imei")
					delete(reqDeviceInfoMap, "imeiMd5")
					delete(reqDeviceInfoMap, "oaId")

					reqDeviceInfoMap["anVer"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["brand"] = didRedisData.Manufacturer
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}

					// isOKReplaceManufacturer := false
					// for _, manufactureConfigItem := range manufactureConfigArray {
					// 	if strings.ToLower(didRedisData.Manufacturer) == manufactureConfigItem {
					// 		isOKReplaceManufacturer = true
					// 	}
					// }
					// if isOKReplaceManufacturer {
					// } else {
					// 	bigdataExtra.InternalCode = 900011
					// 	bigdataExtra.ExternalCode = 102006

					// 	return MhUpErrorRespMap("", bigdataExtra)
					// }

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imeiMd5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imeiMd5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaId"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true
			} else if mhReq.Device.Os == "ios" {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// apiVc appStoreVc
	postData := map[string]interface{}{
		"apiVersion": 1,
		"apiVc":      108,
		"appStoreVc": 5000,
		"appInfo":    reqAppInfoMap,
		"posInfo":    reqPosInfoMap,
		"devInfo":    reqDeviceInfoMap,
	}
	if platformPos.PlatformAppID == "1100001313" {
		postData["apiVc"] = 111
	}
	// fmt.Println(postData)
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("oppo req: " + string(jsonData))

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, config.UpOPPOURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&oppo", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	oppoRespStu := OPPORespStu{}
	json.Unmarshal([]byte(bodyContent), &oppoRespStu)

	if oppoRespStu.Code != 0 {
		bigdataExtra.UpRespCode = int(oppoRespStu.Code)

		if oppoRespStu.Code == 1003 {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		} else {
			bigdataExtra.InternalCode = 900102
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if len(oppoRespStu.AdList) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	tmpRespCount := 1
	if mhReq.Pos.AdCount > 0 {
		tmpRespCount = mhReq.Pos.AdCount
	}
	var respListArray []map[string]interface{}

	oppoRespItemList := oppoRespStu.AdList

	if len(oppoRespItemList) > 1 {
		sort.Sort(OPPOEcpmSort(oppoRespItemList))
	}

	for _, adInfoItem := range oppoRespItemList {

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// 随机95-98%替换__PR__
		randPRValue := 95 + rand.Intn(4)

		oppoEcpm := adInfoItem.Ecpm

		respTmpPrice = respTmpPrice + oppoEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if oppoEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			oppoEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > oppoEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				if platformPos.PlatformAppIsPriceEncrypt == 1 {
					for _, adTrackInfoItem := range adInfoItem.TrackingList {
						if adTrackInfoItem.TrackingEvent == 5 {
							for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
								nurlItem := adTrackInfoImpURLItem
								nurlItem = strings.Replace(nurlItem, "$nt$", reqConnectType, -1)
								nurlItem = strings.Replace(nurlItem, "$br$", "1", -1)
								nurlItem = strings.Replace(nurlItem, "$bcl$", "2", -1)
								curlOPPONurl(nurlItem)
							}
						}
					}
				}

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			if platformPos.PlatformAppIsPriceEncrypt == 1 {
				for _, adTrackInfoItem := range adInfoItem.TrackingList {
					if adTrackInfoItem.TrackingEvent == 5 {
						for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
							nurlItem := adTrackInfoImpURLItem
							nurlItem = strings.Replace(nurlItem, "$nt$", reqConnectType, -1)
							nurlItem = strings.Replace(nurlItem, "$br$", "1", -1)
							nurlItem = strings.Replace(nurlItem, "$bcl$", "2", -1)
							curlOPPONurl(nurlItem)
						}
					}
				}
			}
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(oppoEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		}

		// description
		if len(adInfoItem.Desc) > 0 {
			respListItemMap["description"] = adInfoItem.Desc
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				// fmt.Println("kbg_debug_gdt:", redisDownX, redisDownY, redisUpX, redisUpY)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {

					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// crid
		respListItemMap["crid"] = adInfoItem.AdID

		// interact_type ad_url
		respListItemMap["interact_type"] = 0
		respListItemMap["ad_url"] = adInfoItem.TargetURL
		respListItemMap["landpage_url"] = adInfoItem.TargetURL

		// package name
		if len(adInfoItem.App.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoItem.App.PackageName

			// android
			if mhReq.Device.Os == "android" {
				appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, adInfoItem.App.AppName, adInfoItem.App.PackageName)
				if appInfoFromRedisErr == nil {
					respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
					respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
				}
			}
		}
		if len(adInfoItem.App.AppDescUrl) > 0 {
			respListItemMap["appinfo_url"] = adInfoItem.App.AppDescUrl
		}

		// deep link
		if len(adInfoItem.DeepLink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.DeepLink

			// 字段 appstore_package_name
			if strings.HasPrefix(adInfoItem.DeepLink, "market://details?") {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor < 9 {
					respListItemMap["appstore_package_name"] = "com.oppo.market"

				} else {
					respListItemMap["appstore_package_name"] = "com.heytap.market"
				}
			}

			// deeplink track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// icon_url
		respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.DeepLink)

		isVideo := false
		// creativeType
		// 2 纯图片广告
		// 3 图标广告
		// 6 640*320 图文混合广告(单图)
		// 7 320*210 图文混合广告(单图)
		// 8 320*210 图文混合广告(3 图)
		// 10 激励视频广告
		// 11 开屏视频广告
		// 15 插屏视频
		// 16 原生视频
		// 18 竖版大图
		// 19 原生竖版视频
		if adInfoItem.CreativeType == 10 || adInfoItem.CreativeType == 11 || adInfoItem.CreativeType == 15 || adInfoItem.CreativeType == 16 || adInfoItem.CreativeType == 19 {
			isVideo = true
		}

		if isVideo {
			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.VideoDuration > 0 {
				respListVideoItemMap["duration"] = adInfoItem.VideoDuration
			}
			// respListVideoItemMap["width"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Width
			// respListVideoItemMap["height"] = adInfoItem.AdMaterialInfo.MaterialFeature[0].MaterialSize.Height
			for _, fileListItem := range adInfoItem.FileList {
				if fileListItem.FileType == 1 {
					if adInfoItem.ContentType == 2 {
						respListItemMap["icon_url"] = fileListItem.URL
					}
					// respListVideoItemMap["cover_url"] = fileListItem.URL
				} else if fileListItem.FileType == 2 {
					respListVideoItemMap["video_url"] = fileListItem.URL
				}
			}

			if localPos.LocalPosWidth > localPos.LocalPosHeight {
				respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
				if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
				}
			} else {
				respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
				if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}
			// track params
			mhTrackParams := url.Values{}
			mhTrackParams.Add("log", up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, 0, tmpEcpm))

			// 播放开始, 播放结束 track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, adTrackInfoItem := range adInfoItem.TrackingList {
				if adTrackInfoItem.TrackingEvent == 10010 {
					for _, adTrackURLItem := range adTrackInfoItem.TrackUrls {
						tmpItem := adTrackURLItem
						tmpItem = strings.Replace(tmpItem, "$nt$", reqConnectType, -1)
						tmpItem = strings.Replace(tmpItem, "$progress$", "0", -1)

						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, tmpItem)
					}
				}
			}
			respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, config.ExternalVideoStartTrackURL+"?"+mhTrackParams.Encode())

			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, adTrackInfoItem := range adInfoItem.TrackingList {
				if adTrackInfoItem.TrackingEvent == 10011 {
					for _, adTrackURLItem := range adTrackInfoItem.TrackUrls {
						tmpItem := adTrackURLItem
						tmpItem = strings.Replace(tmpItem, "$nt$", reqConnectType, -1)
						tmpItem = strings.Replace(tmpItem, "$progress$", utils.ConvertIntToString(adInfoItem.VideoDuration), -1)

						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, tmpItem)
					}
				}
			}
			respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, config.ExternalVideoEndTrackURL+"?"+mhTrackParams.Encode())
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// imgs
			// creativeType
			// 2 纯图片广告
			// 3 图标广告
			// 6 640*320 图文混合广告(单图)
			// 7 320*210 图文混合广告(单图)
			// 8 320*210 图文混合广告(3 图)
			// 18 竖版大图
			if platformPos.PlatformPosType == 14 {
				if adInfoItem.CreativeType == 3 {
				} else {
					continue
				}
				continue
			} else {
				if adInfoItem.CreativeType == 2 || adInfoItem.CreativeType == 6 || adInfoItem.CreativeType == 18 {
				} else {
					continue
				}
			}

			respListImageItemMap := map[string]interface{}{}
			for _, fileListItem := range adInfoItem.FileList {
				if fileListItem.FileType == 1 {
					respListImageItemMap["url"] = fileListItem.URL
				}
			}
			respListImageItemMap["width"] = platformPos.PlatformPosWidth
			respListImageItemMap["height"] = platformPos.PlatformPosHeight

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			for _, adTrackInfoItem := range adInfoItem.TrackingList {
				if adTrackInfoItem.TrackingEvent == 5 {
					for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
						nurlItem := adTrackInfoImpURLItem
						nurlItem = strings.Replace(nurlItem, "$nt$", reqConnectType, -1)
						nurlItem = strings.Replace(nurlItem, "$br$", "0", -1)

						// encode price
						encodeKey, encodeErr := base64.URLEncoding.DecodeString(platformPos.PlatformAppPriceEncrypt)
						if encodeErr == nil {
							macroPrice := utils.ConvertIntToString(int(oppoEcpm * randPRValue / 100))
							if platformPos.PlatformPosID == "1686419" {
								go models.BigDataHoloOppoDebug(oppoRespStu.RespID, maplehazeAdId, bigdataUID, strconv.Itoa(oppoEcpm), macroPrice)
							}

							decodePriceX := utils.AesECBEncrypt([]byte(macroPrice), []byte(encodeKey))
							fmt.Println(base64.StdEncoding.EncodeToString(decodePriceX))
							nurlItem = strings.Replace(nurlItem, "$swp$", base64.StdEncoding.EncodeToString(decodePriceX), -1)
							curlOPPONurl(nurlItem)
						}
					}
				}
			}
		}

		if len(adInfoItem.App.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.App.AppName
		}
		if len(adInfoItem.App.DeveloperName) > 0 {
			respListItemMap["publisher"] = adInfoItem.App.DeveloperName
		}
		if len(adInfoItem.App.VersionName) > 0 {
			respListItemMap["app_version"] = adInfoItem.App.VersionName
		}
		if len(adInfoItem.App.PrivacyUrl) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.App.PrivacyUrl
		}
		if len(adInfoItem.App.PermissionUrl) > 0 {
			respListItemMap["permission"] = "允许程序访问电话状态\n允许程序通过GPS芯片接收卫星的定位信息\n允许程序可以读取设备外部存储空间\n允许程序更改系统设置\n允许程序通过WiFi或移动基站的方式获取用户粗略位置信息\n允许程序写入外部存储,如SD卡上写文件\n"
			respListItemMap["permission_url"] = adInfoItem.App.PermissionUrl
		}
		if adInfoItem.App.ApkSize > 0 {
			respListItemMap["package_size"] = adInfoItem.App.ApkSize
		}

		// v2.1.5新增返回字段
		if len(adInfoItem.App.AppIconUrl) > 0 {
			respListItemMap["icon_url"] = adInfoItem.App.AppIconUrl
		}
		if len(adInfoItem.App.DownloadUrl) > 0 {
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.App.DownloadUrl
			respListItemMap["download_url"] = adInfoItem.App.DownloadUrl
		}
		if adInfoItem.App.VersionCode > 0 {
			respListItemMap["app_version"] = utils.ConvertInt64ToString(adInfoItem.App.VersionCode)
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, oppoEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, adTrackInfoItem := range adInfoItem.TrackingList {
			if adTrackInfoItem.TrackingEvent == 2 {
				for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
					impItem := adTrackInfoImpURLItem
					impItem = strings.Replace(impItem, "$nt$", reqConnectType, -1)
					impItem = strings.Replace(impItem, "$ca$", utils.ConvertIntToString(mhReq.Network.Carrier), -1)
					respListItemImpArray = append(respListItemImpArray, impItem)
				}
			}
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link  track url
		for _, adTrackInfoItem := range adInfoItem.TrackingList {
			if adTrackInfoItem.TrackingEvent == 1 {
				for _, adTrackInfoImpURLItem := range adTrackInfoItem.TrackUrls {
					clkItem := adTrackInfoImpURLItem
					clkItem = strings.Replace(clkItem, "$nt$", reqConnectType, -1)
					clkItem = strings.Replace(clkItem, "$ca$", utils.ConvertIntToString(mhReq.Network.Carrier), -1)
					clkItem = strings.Replace(clkItem, "$dx$", tmpDownX, -1)
					clkItem = strings.Replace(clkItem, "$dy$", tmpDownY, -1)
					clkItem = strings.Replace(clkItem, "$ux$", tmpUpX, -1)
					clkItem = strings.Replace(clkItem, "$uy$", tmpUpY, -1)

					clkItem = strings.Replace(clkItem, "$acw$", "__WIDTH__", -1)
					clkItem = strings.Replace(clkItem, "$ach$", "__HEIGHT__", -1)
					clkItem = strings.Replace(clkItem, "$itm$", "__SLD__", -1)
					clkItem = strings.Replace(clkItem, "$xma$", "__X_MAX_ACC__", -1)
					clkItem = strings.Replace(clkItem, "$yma$", "__Y_MAX_ACC__", -1)
					clkItem = strings.Replace(clkItem, "$zma$", "__Z_MAX_ACC__", -1)

					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
							clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					if platformPos.PlatformPosIsReportSLD == 1 {
						clkItem = strings.Replace(clkItem, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					}

					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							clkItem = clkItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							clkItem = clkItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}

					respListItemClkArray = append(respListItemClkArray, clkItem)
				}
			}
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}

		if len(respListArray) >= tmpRespCount {
			continue
		}

		respListItemMap["p_ecpm"] = oppoEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// kuaishou resp
	respOppo := models.MHUpResp{}
	respOppo.RespData = &mhResp
	respOppo.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respOppo
	// for test
	// bigdataExtra.InternalCode = respTmpInternalCode
	// bigdataExtra.ExternalCode = 102006

	// return MhUpErrorRespMap("", bigdataExtra)
}

func curlOPPONurl(nurl string) {

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("oppo win url panic:", err)
			}
		}()

		client := &http.Client{Timeout: 1000 * time.Millisecond}
		requestGet, _ := http.NewRequest("GET", nurl, nil)

		requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

		// fmt.Println(requestGet.URL.String())
		// fmt.Println("oppo nurl req: " + requestGet.URL.String())

		resp, err := client.Do(requestGet)
		if err != nil {
			// fmt.Printf("get request failed, err:[%s]", err.Error())
			return
		}

		defer resp.Body.Close()

		// bodyContent, err := io.ReadAll(resp.Body)
		// fmt.Println("oppo nurl  resp: " + string(bodyContent))
	}()
}

type OPPOEcpmSort []OPPORespAdListStu

func (s OPPOEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s OPPOEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s OPPOEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}

// OPPORespStu ...
type OPPORespStu struct {
	Code        int32               `json:"code"`
	Msg         string              `json:"msg"`
	RespID      string              `json:"respId"`
	ReqInterval int                 `json:"reqInterval"`
	AdList      []OPPORespAdListStu `json:"adList"`
}

// OPPORespAdListStu ...
type OPPORespAdListStu struct {
	AdID          string                       `json:"adId"`
	LogoFile      OPPORespAdListLogoStu        `json:"logoFile"`
	CreativeType  int                          `json:"creativeType"`
	FileList      []OPPORespAdListFileListStu  `json:"fileList"`
	Title         string                       `json:"title"`
	Desc          string                       `json:"desc"`
	TargetURL     string                       `json:"targetUrl"`
	DeepLink      string                       `json:"deepLink"`
	TrackingList  []OPPORespAdListTrackListStu `json:"trackingList"`
	ContentType   int                          `json:"contentType"`
	VideoDuration int                          `json:"videoDuration"`
	Ecpm          int                          `json:"price"`
	App           OPPORespAdListAppStu         `json:"App"`
}

// OPPORespAdListLogoStu ...
type OPPORespAdListLogoStu struct {
	FileType int    `json:"fileType"`
	URL      string `json:"url"`
}

// OPPORespAdListFileListStu ...
type OPPORespAdListFileListStu struct {
	FileType int    `json:"fileType"`
	URL      string `json:"url"`
}

// OPPORespAdListTrackListStu ...
type OPPORespAdListTrackListStu struct {
	TrackingEvent int      `json:"trackingEvent"`
	TrackUrls     []string `json:"trackUrls"`
}

type OPPORespAdListAppStu struct {
	PackageName   string `json:"appPackage"`
	ApkSize       int    `json:"apkSize"`
	AppDescUrl    string `json:"appDescUrl"`
	PrivacyUrl    string `json:"privacyUrl"`
	PermissionUrl string `json:"permissionUrl"`
	DeveloperName string `json:"developerName"`
	VersionName   string `json:"versionName"`
	AppName       string `json:"appName"`
	AppIconUrl    string `json:"appIconUrl"`
	DownloadUrl   string `json:"downloadUrl"`
	ApkMd5        string `json:"apkMd5"`
	VersionCode   int64  `json:"versionCode"`
}
