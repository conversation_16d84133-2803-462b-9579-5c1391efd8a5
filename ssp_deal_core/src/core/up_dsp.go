package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromDsp ...
func GetFromDsp(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from dsp")
	// fmt.Println("get from dsp, local app id: " + localPos.LocalAppID)
	// fmt.Println("get from dsp, local pos id: " + localPos.LocalPosID)
	// fmt.Println("get from dsp, up app id: " + platformPos.PlatformAppID)
	// fmt.Println("get from dsp, up pos id: " + platformPos.PlatformPosID)
	// fmt.Println("get from dsp, up media id: " + platformPos.PlatformMediaID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	// fmt.Println("redis error:", redisErr)
	materailType := 0 // 0 图片, 1 视频, 2 图片+视频
	if localPos.LocalPosMaterialType == 1 {
		materailType = 1
	} else if localPos.LocalPosMaterialType == 2 {
		materailType = 2
	}

	reqStu := DspReqStu{
		SDKVersion: mhReq.SDKVersion,
		Pos: DspReqPosStu{
			PlanID:       platformPos.PlatformPosID,
			Width:        localPos.LocalPosWidth,
			Height:       localPos.LocalPosHeight,
			CreativeType: materailType,
			CPMBidFloor:  localPosFloorPrice,
		},
		Device: DspReqDeviceStu{
			Os:           mhReq.Device.Os,
			OsVersion:    mhReq.Device.OsVersion,
			Model:        mhReq.Device.Model,
			Manufacturer: mhReq.Device.Manufacturer,
			Imei:         mhReq.Device.Imei,
			ImeiMd5:      mhReq.Device.ImeiMd5,
			AndroidID:    mhReq.Device.AndroidID,
			AndroidIDMd5: mhReq.Device.AndroidIDMd5,
			Oaid:         mhReq.Device.Oaid,
			OaidMd5:      mhReq.Device.OaidMd5,
			Idfa:         mhReq.Device.Idfa,
			IdfaMd5:      mhReq.Device.IdfaMd5,
			Ua:           mhReq.Device.Ua,
			DeviceType:   mhReq.Device.DeviceType,
			IP:           mhReq.Device.IP,
			Mac:          mhReq.Device.Mac,
			DIDMd5Key:    mhReq.Device.DIDMd5,
			// CAID:         mhReq.Device.CAID,
			// CAIDVersion:  mhReq.Device.CAIDVersion,
			CAIDMulti: mhReq.Device.CAIDMulti,
		},
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	jsonData, _ := json.Marshal(reqStu)

	// if platformPos.PlatformPosID == "d035272db972e2e1" {
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&ssp_debug_d035272db972e2e1", string(jsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	// httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, config.UpDspURL+"/api/req",
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&dsp", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	dspRespStu := DspRespStu{}
	json.Unmarshal([]byte(bodyContent), &dspRespStu)

	if dspRespStu.Code != 10000 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = dspRespStu.Code
		return MhUpErrorRespMap("", bigdataExtra)
	}
	// fmt.Println(dspRespStu.Data.PID)

	if len(dspRespStu.Data) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}

	for _, item := range dspRespStu.Data {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		dspEcpm := item.Ecpm

		respTmpPrice = respTmpPrice + dspEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if dspEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			dspEcpm = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > dspEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
		}
		// <el-radio label="1">Banner</el-radio>
		// <el-radio label="2">开屏</el-radio>
		// <el-radio label="3">插屏</el-radio>
		// <el-radio label="4">原生</el-radio>
		// <el-radio label="11">贴片</el-radio>
		// <el-radio label="7">全屏视频</el-radio>
		// <el-radio label="9">激励视频</el-radio>
		// <el-radio label="8">单Feed流</el-radio>
		// <el-radio label="10">双Feed流</el-radio>

		// <el-radio label="0">图片</el-radio>
		// <el-radio label="1">视频</el-radio>
		// <el-radio label="2">图片+视频</el-radio>

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(dspEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		respListItemMap["title"] = item.Title
		// description
		respListItemMap["description"] = item.Description
		respListItemMap["crid"] = item.CRID

		if len(item.DeepLink) > 0 || len(item.MarketURL) > 0 || len(item.QuickAppLink) > 0 {
			respListItemMap["deep_link"] = item.DeepLink
			respListItemMap["market_url"] = item.MarketURL
			respListItemMap["quick_app_link"] = item.QuickAppLink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			var respListItemDeepLinkFailedArray []string

			mhDPFailedParams := url.Values{}
			mhDPFailedParams.Add("result", "1")
			mhDPFailedParams.Add("reason", "3")
			mhDPFailedParams.Add("log", bigdataParams)

			respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

			respListItemConv11Map := map[string]interface{}{}
			respListItemConv11Map["conv_type"] = 11
			respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

			respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		if localPos.LocalAppIsLenovoSecureDownload == 1 {
			if len(item.DownloadLink) > 0 {
				// 获取lenovo厂商
				manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"lenovo")

				isInManufactureConfig := false
				for _, manufactureConfigItem := range manufactureConfigArray {
					if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
						isInManufactureConfig = true
					}
				}
				if isInManufactureConfig {
					respListItemMap["deep_link"] = "leapp://ptn/url_dl.do?appdlinfo=" +
						url.QueryEscape(
							"downurl="+item.DownloadLink+
								"&pn="+item.PackageName+
								"&vc="+item.AppVersion+
								"&size="+item.PackageSize+
								"&name="+item.AppName+
								"&icon="+item.IconURL+
								"&replaced=1"+
								"&extendinfo="+item.IconURL)
				}
			}
		}

		if len(item.AppName) > 0 {
			respListItemMap["app_name"] = item.AppName
		}
		if len(item.PackageName) > 0 {
			respListItemMap["package_name"] = item.PackageName

			// android
			if mhReq.Device.Os == "android" {
				appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, item.AppName, item.PackageName)
				if appInfoFromRedisErr == nil {
					respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
					respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
				}
			}
		}
		// icon_url
		if len(item.IconURL) > 0 {
			respListItemMap["icon_url"] = item.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(item.DeepLink)
		}
		if len(item.AppVersion) > 0 {
			respListItemMap["app_version"] = item.AppVersion
		}
		if len(item.AppVersionCode) > 0 {
			respListItemMap["app_version_code"] = item.AppVersionCode
		}
		if len(item.Publisher) > 0 {
			respListItemMap["publisher"] = item.Publisher
		}
		if len(item.PrivacyLink) > 0 {
			respListItemMap["privacy_url"] = item.PrivacyLink
		}
		if len(item.Permissions) > 0 {
			respListItemMap["permission"] = item.Permissions
		}
		if len(item.PackageSize) > 0 {
			tmp1, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", utils.ConvertStringToFloat(item.PackageSize)), 64)
			respListItemMap["package_size"] = (int)(tmp1 * 1024 * 1024)
		}

		respListItemMap["appinfo"] = item.AppInfo

		// image or video
		if item.MaterialType == 0 {
			respListImageItemMap := map[string]interface{}{}

			respListImageItemMap["url"] = item.ImageURL
			respListImageItemMap["width"] = item.ImageWidth
			respListImageItemMap["height"] = item.ImageHeight

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		} else if item.MaterialType == 1 {
			respListVideoItemMap := map[string]interface{}{}

			respListVideoItemMap["width"] = item.VideoWidth
			respListVideoItemMap["height"] = item.VideoHeight
			respListVideoItemMap["video_url"] = item.VideoURL
			respListVideoItemMap["duration"] = item.VideoDuration * 1000

			// 过滤video_duration
			isMaterialFilter, _ := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, item.VideoDuration)
			if isMaterialFilter {
				continue
			}

			if item.VideoWidth > 0 && item.VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, _ := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, item.VideoWidth, item.VideoHeight)
				if isMaterialFilter {
					continue
				}
			}

			// cover_url
			if len(item.CoverURL) > 0 {
				respListVideoItemMap["cover_url"] = item.CoverURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			// 下游广告位类型
			// <el-radio label="1">Banner</el-radio>
			// <el-radio label="2">开屏</el-radio>
			// <el-radio label="3">插屏</el-radio>
			// <el-radio label="4">原生</el-radio>
			// <el-radio label="5">原生视频</el-radio>
			// <el-radio label="11">贴片</el-radio>
			// <el-radio label="6">信息流</el-radio>
			// <el-radio label="7">全屏视频</el-radio>
			// <el-radio label="9">激励视频</el-radio>
			// <el-radio label="8">视频内容联盟</el-radio>
			// <el-radio label="10">视频内容联盟组件</el-radio>
			// if localPos.LocalPosType == 7 {
			// 	respListVideoItemMap["skip_min_time"] = 5000
			// }
			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, "https://adx.maplehaze.cn/api/test")

			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, "https://adx.maplehaze.cn/api/test")

			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			bigdataExtra.InternalCode = 900103
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		////////////////////////////////////////////////////////////
		// test video
		// respListVideoItemMap := map[string]interface{}{}

		// respListVideoItemMap["width"] = 10
		// respListVideoItemMap["height"] = 10
		// respListVideoItemMap["video_url"] = "http://www.google.com"
		// respListVideoItemMap["duration"] = 1000
		// respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"

		// var respListEventTrackURLMap []map[string]interface{}

		// respListVideoBeginEventTrackMap := map[string]interface{}{}
		// respListVideoBeginEventTrackMap["event_type"] = 100
		// var respListVideoBeginEventTrackURLMap []string
		// respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, "http://begin1")
		// respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, "http://begin2")

		// respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
		// if len(respListVideoBeginEventTrackURLMap) > 0 {
		// 	respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
		// }

		// respListVideoEndEventTrackMap := map[string]interface{}{}
		// respListVideoEndEventTrackMap["event_type"] = 103
		// var respListVideoEndEventTrackURLMap []string
		// respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, "http://end1")
		// respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, "http://end2")

		// respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
		// if len(respListVideoEndEventTrackURLMap) > 0 {
		// 	respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
		// }

		// if len(respListEventTrackURLMap) > 0 {
		// 	respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
		// }

		// respListItemMap["video"] = respListVideoItemMap
		////////////////////////////////////////////////////////////

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// interact_type ad_url
		if len(item.DownloadLink) > 0 {
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = item.DownloadLink
			respListItemMap["download_url"] = item.DownloadLink
			if len(item.H5Link) > 0 {
				respListItemMap["landpage_url"] = item.H5Link
			}
		} else if len(item.H5Link) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = item.H5Link
			respListItemMap["landpage_url"] = item.H5Link
		} else {
			continue
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, dspEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		if len(item.ExpLinks) > 0 {
			for _, expItem := range item.ExpLinks {
				respListItemImpArray = append(respListItemImpArray, expItem)
			}
		}
		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")
		mhClkParams.Add("sld", "__SLD__")
		mhClkParams.Add("log", bigdataParams)

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
		if len(item.ClkLinks) > 0 {
			for _, clkItem := range item.ClkLinks {
				tmpItem := clkItem
				tmpItem = strings.Replace(tmpItem, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
				tmpItem = strings.Replace(tmpItem, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
				tmpItem = strings.Replace(tmpItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
				tmpItem = strings.Replace(tmpItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
				respListItemClkArray = append(respListItemClkArray, tmpItem)
			}
		}

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		if localPos.LocalPosWidth > localPos.LocalPosHeight {
			respListItemMap["material_direction"] = 0
		} else {
			respListItemMap["material_direction"] = 1
		}

		// 过滤已安装包名
		respListItemMap["filter_package_name"] = strings.Split(item.FilterPackageNames, ",")
		// 定向已安装包名
		respListItemMap["install_package_name"] = strings.Split(item.DirectInstalledPackageNames, ",")

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		respListItemMap["p_ecpm"] = dspEcpm

		respListArray = append(respListArray, respListItemMap)
	}

	bigdataExtra.UpRespTime = 1

	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[utils.ConvertIntToString(mhReq.Pos.ID)] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// self resp
	respSelf := models.MHUpResp{}
	respSelf.RespData = &mhResp
	respSelf.Extra = bigdataExtra
	// respSelf.Extra.RespCount = len(respListArray)
	// respSelf.Extra.ExternalCode = 0
	// respSelf.Extra.InternalCode = 900000

	return &respSelf
}

// DspReqStu ...
type DspReqStu struct {
	ApiVersion        string          `json:"api_version,omitempty"`
	SDKVersion        string          `json:"sdk_version,omitempty"`
	Device            DspReqDeviceStu `json:"device"`
	Pos               DspReqPosStu    `json:"pos"`
	ExtraClipboard    int             `json:"extra_clipboard,omitempty"`
	ExtraNotification int             `json:"extra_notification,omitempty"`
	ExtraWakeUp       int             `json:"extra_wakeup,omitempty"`

	// 流量包增加
	Media   DspReqMediaStu `json:"media"`
	Network DspReqNetwork  `json:"network"`
	Geo     DspReqGeo      `json:"geo"`
}

// DspReqPosStu ...
type DspReqPosStu struct {
	PlanID       string `json:"plan_id,omitempty"`
	Width        int    `json:"width,omitempty"`
	Height       int    `json:"height,omitempty"`
	CreativeType int    `json:"creative_type,omitempty"`
	// ssp supply_app_id
	SupplyAppID string `json:"supply_app_id,omitempty"`
	// cpm floor
	CPMBidFloor int `json:"cpm_bid_floor,omitempty"`

	// 流量包使用广告位ID, 广告位类型, splash, interstitial, native, reward
	SupplyPosID   string `json:"pos_id,omitempty"`
	SupplyPosType string `json:"pos_type,omitempty"`
}

// DspReqDeviceStu ...
type DspReqDeviceStu struct {
	Os           string `json:"os,omitempty"`
	OsVersion    string `json:"os_version,omitempty"`
	Model        string `json:"model,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	DeviceType   int    `json:"device_type,omitempty"`
	Imei         string `json:"imei,omitempty"`
	ImeiMd5      string `json:"imei_md5,omitempty"`
	AndroidID    string `json:"android_id,omitempty"`
	AndroidIDMd5 string `json:"android_id_md5,omitempty"`
	Oaid         string `json:"oaid,omitempty"`
	OaidMd5      string `json:"oaid_md5,omitempty"`
	Idfa         string `json:"idfa,omitempty"`
	IdfaMd5      string `json:"idfa_md5,omitempty"`
	Ua           string `json:"ua,omitempty"`
	IP           string `json:"ip,omitempty"`
	Mac          string `json:"mac,omitempty"`
	DIDMd5Key    string `json:"did_md5,omitempty"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec,omitempty"`
	Country            string `json:"country,omitempty"`
	Language           string `json:"language,omitempty"`
	DeviceNameMd5      string `json:"device_name_md5,omitempty"`
	HardwareMachine    string `json:"hardware_machine,omitempty"`
	HardwareModel      string `json:"hardware_model,omitempty"`
	PhysicalMemoryByte string `json:"physical_memory_byte,omitempty"`
	HarddiskSizeByte   string `json:"harddisk_size_byte,omitempty"`
	SystemUpdateSec    string `json:"system_update_sec,omitempty"`
	TimeZone           string `json:"time_zone,omitempty"`
	// CAID               string `json:"caid"`
	// CAIDVersion        string `json:"caid_version"`
	CAIDMulti []models.MHReqCAIDMulti `json:"caid_multi,omitempty"`
}

type DspReqCAIDMulti struct {
	CAID        string `json:"caid,omitempty"`
	CAIDVersion string `json:"caid_version,omitempty"`
}

// DspRespStu ...
type DspRespStu struct {
	Code int              `json:"code"`
	Data []DspRespDataStu `json:"data"`
}

type DspRespDataStu struct {
	CRID           string   `json:"crid"`
	Title          string   `json:"title"`
	Description    string   `json:"description"`
	IconURL        string   `json:"icon_url"`
	MaterialType   int      `json:"material_type"`
	ImageURL       string   `json:"image_url"`
	ImageWidth     int      `json:"image_width"`
	ImageHeight    int      `json:"image_height"`
	VideoURL       string   `json:"video_url"`
	CoverURL       string   `json:"cover_url"`
	VideoWidth     int      `json:"video_width"`
	VideoHeight    int      `json:"video_height"`
	VideoDuration  int      `json:"video_duration"`
	ExpLinks       []string `json:"exp_links"`
	ClkLinks       []string `json:"clk_links"`
	DownloadLink   string   `json:"download_link"`
	H5Link         string   `json:"h5_link"`
	DeepLink       string   `json:"deep_link"`
	MarketURL      string   `json:"market_url"`
	QuickAppLink   string   `json:"quick_app_link"`
	AppName        string   `json:"app_name"`
	PackageName    string   `json:"package_name"`
	PackageSize    string   `json:"package_size"`
	Publisher      string   `json:"publisher"`
	AppVersion     string   `json:"app_version"`
	AppVersionCode string   `json:"app_version_code"`
	AppInfo        string   `json:"app_info"`
	PrivacyLink    string   `json:"privacy_link"`
	Permissions    string   `json:"permissons"`
	Ecpm           int      `json:"ecpm"`

	// SDK过滤已安装
	FilterPackageNames string `json:"filter_package_names"`
	// SDK定向已安装
	DirectInstalledPackageNames string `json:"direct_install_package_names"`
}
