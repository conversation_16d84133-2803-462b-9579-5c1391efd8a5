package up_jiatou_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
)

// JiatouRequestObject Objects

type JiatouRequestObject struct {
	Pid              string  `json:"pid"`                         // 需要注册帐号，申请广告位后获得
	Apitype          string  `json:"apitype"`                     // banner/native 默认为 banner
	Size             string  `json:"size,omitempty"`              // 300x300 默认为系统设置尺寸。仅 可以从“4.广告尺寸”中选择相应尺 寸若列表中不包含请联系技术人员
	AdzLocation      int     `json:"adzlocation,omitempty"`       // 1首屏 2非首屏 默认1
	BidFloor         float64 `json:"bidfloor,omitempty"`          // 位置的 CPM 底价单位(元)如果不 填写以系统设置底价为准。
	Secure           int     `json:"secure,omitempty"`            // 0:http 1:https 如果不填写使用 系统设置
	DetectedLanguage string  `json:"detected_language,omitempty"` // 用户语言
	TimezoneOffset   string  `json:"timezoneoffset,omitempty"`    // 480(+8 时区为 480) 默认 480
	Rid              string  `json:"rid,omitempty"`               // 随机id
	MainUrl          string  `json:"mainurl,omitempty"`           // 自定义来源URL
	Content          string  `json:"content,omitempty"`           // 页面主要内容或者频道信息
	Ip               string  `json:"ip,omitempty"`                // 服务器端发送请求时必须发送
	Ua               string  `json:"ua,omitempty"`                // 服务器端发送请求时必须发送
	Uid              string  `json:"uid,omitempty"`               // 服务器端发送请求时必须发送
	Platform         string  `json:"platform,omitempty"`          // IOS/ANDROID/WP
	Model            string  `json:"model,omitempty"`             // 手机型号
	Version          string  `json:"ver,omitempty"`               // 手机系统版本
	Brand            string  `json:"brand,omitempty"`             // 手机品牌
	AppId            string  `json:"appid,omitempty"`             // 应用包名
	AppName          string  `json:"appname,omitempty"`           // 应用名称
	AppVersion       string  `json:"appver,omitempty"`            // 应用版本
	IsApp            string  `json:"isapp,omitempty"`             // Y/N
	Width            int     `json:"width,omitempty"`             // 手机屏幕宽度
	Height           int     `json:"height,omitempty"`            // 手机屏幕高度
	Mac              string  `json:"mac,omitempty"`               // 手机Mac地址
	MacMd5           string  `json:"macmd5,omitempty"`            // 手机Mac地址 MD5
	Gaid             string  `json:"gaid,omitempty"`              // Google Advertising ID
	Imei             string  `json:"imei,omitempty"`              // IMEI
	ImeiMd5          string  `json:"imeimd5,omitempty"`           // IMEI MD5
	AndroidId        string  `json:"androidid,omitempty"`         // AndroidId
	Oaid             string  `json:"oaid,omitempty"`              // OAID
	OaidMd5          string  `json:"oaidmd5,omitempty"`           // OAID MD5
	Idfa             string  `json:"idfa,omitempty"`              // IDFA
	IdfaMd5          string  `json:"idfamd5,omitempty"`           // IDFA MD5
	Caid             string  `json:"caid,omitempty"`              // CAID
	CaidVersion      string  `json:"caid_ver,omitempty"`          // CAID VERSION
	IsJailbreak      string  `json:"jailbreak,omitempty"`         // 是否越狱 Y/N
	MachineType      string  `json:"machinetype,omitempty"`       // 机器类型
	SystemDiskSize   string  `json:"sysdisksize,omitempty"`       // 硬盘容量
	SystemMemory     string  `json:"sysmemory,omitempty"`         // 系统内存
	CountryCode      string  `json:"countrycode,omitempty"`       // 国家代码
	Hwmachine        string  `json:"hwmachine,omitempty"`         // 系统型号
	Hwname           string  `json:"hwname,omitempty"`            // 设备名称
	HwnameMd5        string  `json:"hwname_md5,omitempty"`        // 设备名称MD5
	Hwv              string  `json:"hwv,omitempty"`               // 硬件型号版本
	OsUpdateTime     string  `json:"osupdatetime,omitempty"`      // 系统更新时间
	NetworkType      string  `json:"networktype,omitempty"`       // 网络类型 2G 3G 4G 5G WIFI UNKNOWN
	IsTabletDevice   string  `json:"istabletdevice,omitempty"`    // 是否为平板 Y/N
	Imsi             string  `json:"imsi,omitempty"`              // IMSI
	Dpi              float64 `json:"dpi,omitempty"`               // DPI
	Idfv             string  `json:"idfv,omitempty"`              // IDFV
	Carrier          string  `json:"carrier,omitempty"`           // 运营商名称 移动/电信/联通 等等
	BindCrid         string  `json:"bcrid,omitempty"`             // 屏蔽素材id 多个使用逗号分割
	BindSeat         string  `json:"bseat,omitempty"`             // 屏蔽广告主seat 多个使用逗号分割
	Lat              float64 `json:"lat,omitempty"`               // 纬度
	Lng              float64 `json:"lng,omitempty"`               // 经度
	BootMark         string  `json:"boot_mark,omitempty"`         // 系统启动标识
	UpdateMark       string  `json:"update_mark,omitempty"`       // 系统更新标识
	InstalledApp     string  `json:"installed_app,omitempty"`     // 安装包列表 多个使用逗号分割
}

func (r *JiatouRequestObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

func NewJiatouOS(os up_common.UpCommonOSEnum) string {
	if os == up_common.MH_UP_COMMON_OS_IOS {
		return "IOS"
	}
	return "ANDROID"
}

type JiatouResponseObject struct {
	Click                     string   `json:"click,omitempty"`                     // 点击跳转地址 非原生广告时必填。原生广告时使用 landingpage_url 或 app_download_url。 (支持点击坐标宏，详细信息参照文档 2.3)
	Pm                        []string `json:"pm"`                                  // 曝光计费地址 广告展示时需要请求地址，曝光地址是 数组，需要执行每个曝光监测。pm 为 数组。
	Cm                        []string `json:"cm,omitempty"`                        // 点击监测地址 点击监测地址。点击时对这些地址发送 请求。cm 为数组。(支持点击坐标 宏，详细信息参照文档 2.3)
	DpCm                      []string `json:"dpcm,omitempty"`                      // Deeplink监测地址
	StartDownloadMonitorUrls  []string `json:"startdownloadmonitorUrls,omitempty"`  // 开始下载监测地址
	FinishDownloadMonitorUrls []string `json:"finishdownloadmonitorUrls,omitempty"` // 下载完成监测地址
	StartInstallMonitorUrls   []string `json:"startInstallmonitorUrls,omitempty"`   // 安装开始监测地址
	FinishInstallMonitorUrls  []string `json:"finishInstallmonitorUrls,omitempty"`  // 安装完成监测地址
	Active                    []string `json:"active,omitempty"`                    // 上报激活监测
	Start                     []string `json:"start,omitempty"`                     // 视频开始播放上报
	FirstQuartile             []string `json:"firstQuartile,omitempty"`             // 视频播放至少 25%上报
	Midpoint                  []string `json:"midpoint,omitempty"`                  // 视频播放至少 50%上报
	ThirdQuartile             []string `json:"thirdQuartile,omitempty"`             // 视频播放至少 75%上报
	Complete                  []string `json:"complete,omitempty"`                  // 视频播放完毕上报地址
	Skip                      []string `json:"skip,omitempty"`                      // 视频广告跳过上报地址
	CloseLinear               []string `json:"closeLinear,omitempty"`               // 视频广告关闭上报地址
	AdUrl                     string   `json:"adurl"`                               // 主素材地址(图片/native 视频广告位此 字段返回视频素材)
	AdUrls                    []string `json:"adurls,omitempty"`                    // 多图广告位会同时返回 adurl 和 adurls，优先获取 adurls
	ImgUrl                    string   `json:"imgurl,omitempty"`                    // 图片地址(原生视频时有效)
	Duration                  int      `json:"duration,omitempty"`                  // 视频时长(原生视频时有效;单位秒)
	CreativeId                string   `json:"creative_id"`                         // 素材ID
	Seat                      string   `json:"seat"`                                // 广告主 id 或者名称
	LandingPageUrl            string   `json:"landingpage_url,omitempty"`           // 若 landingpage_url 和 app_download_url 同时存在，优先下载 (支持点击坐标宏，详细信息参照文档 2.3)
	AppDownloadUrl            string   `json:"app_download_url,omitempty"`          // (支持点击坐标宏，详细信息参照文档 2.3)
	GDTDownloadUrl            string   `json:"gdt_download_url,omitempty"`          // 如果有广点通下载地址优先使用广点通下载地址。需要按照广点通下载协议处理
	AppName                   string   `json:"app_name,omitempty"`                  // App名称
	AppPackageName            string   `json:"app_package_name,omitempty"`          // App包名
	AppVendor                 string   `json:"app_vendor,omitempty"`                // App厂商
	AppSize                   int      `json:"app_size,omitempty"`                  // App文件大小
	AppVersion                string   `json:"app_ver,omitempty"`                   // App版本号
	AppPrivacy                string   `json:"app_privacy,omitempty"`               // App隐私说明 Url
	AppPermissions            string   `json:"app_permissions,omitempty"`           // App权限
	Title                     string   `json:"title,omitempty"`                     // Native 原生广告标题 title
	Body                      string   `json:"body,omitempty"`                      // Native 原生广告介绍 body
	AppIcon                   string   `json:"app_icon,omitempty"`                  // Native 原生广告图标
	Star                      int      `json:"star,omitempty"`                      // Native 原生广告星级
	Price                     int      `json:"price,omitempty"`                     // Native 原生广告价格
	Adveristiser              string   `json:"adveristiser,omitempty"`              // Native 原生广告广告主
	Deeplink                  string   `json:"deeplink,omitempty"`                  // Native 原生 deeplink 地址 (支持点击坐标宏，详细信息参照文档 2.3)
	AuctionPrice              string   `json:"auction_price,omitempty"`             // VIP 媒体返回，默认不返回。单位 (元)如果使用此字段需要替换 pm 中宏${AUCTION_PRICE} 单位(元)。
	Myprice                   string   `json:"myprice,omitempty"`                   // VIP 媒体返回，默认不返回
	TemplateId                string   `json:"template_id,omitempty"`               // 素材模板 id 位置使用多模板时返回这个 id 默认不 返回
	SourceLog                 string   `json:"source_logo,omitempty"`               // 广告来源图标
	Error                     string   `json:"error,omitempty"`                     // 错误信息
	Width                     string   `json:"w,omitempty"`                         // 广告宽
	Height                    string   `json:"h,omitempty"`                         // 广告高
}

func (r *JiatouResponseObject) String() string {
	return fmt.Sprintf("%+v", *r)
}

type JiatouPipline struct {
	Common *up_common.UpCommonPipline

	Request  *JiatouRequestObject
	Response *JiatouResponseObject
}

func (r *JiatouPipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
