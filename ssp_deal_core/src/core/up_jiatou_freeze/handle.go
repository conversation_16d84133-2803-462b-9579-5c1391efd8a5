package up_jiatou_freeze

import (
	"context"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
)

func GetFromJiatou(c context.Context,
	mhReq *models.MHReq,
	localPos *models.LocalPosStu,
	platformPos *models.PlatformPosStu,
	categoryInfo *models.CategoryStu,
	bigdataUID string) *models.MHUpResp {

	upCommonPipline := up_common.NewCommonPipline(
		c,
		mhReq,
		localPos,
		platformPos,
		categoryInfo,
		bigdataUID)
	pipline := NewPipline(upCommonPipline)
	pipline.Run()
	responseResult := pipline.ResponseResult()
	utils.JsonPrintln(pipline)
	return responseResult
}
