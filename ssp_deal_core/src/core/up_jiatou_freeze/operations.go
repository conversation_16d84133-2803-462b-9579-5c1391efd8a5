package up_jiatou_freeze

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &JiatouPipline{Common: common}
}

/**
* Private Methods
**/

func (p *JiatouPipline) replaceLinkString(link string) string {
	linkString := link
	linkString = strings.Replace(linkString, "__JYCLICKTIME__", utils.ConvertInt64ToString(time.Now().UnixNano()/1e6), -1)
	linkString = strings.Replace(linkString, "__JYDOWNX__", "__DOWN_X__", -1)
	linkString = strings.Replace(linkString, "__JYDOWNY__", "__DOWN_Y__", -1)
	linkString = strings.Replace(linkString, "__JYUPX__", "__UP_X__", -1)
	linkString = strings.Replace(linkString, "__JYUPY__", "__UP_Y__", -1)

	if p.Common.IsLogicPixel {
		linkString = strings.Replace(linkString, "__JYWIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
		linkString = strings.Replace(linkString, "__JYHEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	} else {
		linkString = strings.Replace(linkString, "__JYWIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
		linkString = strings.Replace(linkString, "__JYHEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	}

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "__DOWN_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__DOWN_Y__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_Y__", "-999", -1)
	}

	return linkString
}

/**
 * * 函数式方法模板
 */
func (p *JiatouPipline) actionTemplate() *JiatouPipline {

	return p
}

/**
* Public Methods
**/

func (p *JiatouPipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("GetFromJiatou error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *JiatouPipline) SetupRequest() up_common.PiplineInterface {

	// Setup JiatouRequestObject
	requestObject := JiatouRequestObject{}
	{

		requestObject.Pid = p.Common.PlatformPos.PlatformPosID
		requestObject.Apitype = "native"
		requestObject.Size = fmt.Sprintf("%dx%d", p.Common.PlatformPos.PlatformPosWidth, p.Common.PlatformPos.PlatformPosHeight)
		requestObject.Ip = p.Common.MhReq.Device.IP
		requestObject.Ua = p.Common.MhReq.Device.Ua
		requestObject.Version = p.Common.MhReq.Device.OsVersion
		requestObject.AppId = p.Common.PlatformPos.PlatformAppBundle
		requestObject.AppVersion = p.Common.PlatformPos.PlatformAppVersion
		requestObject.BidFloor = float64(p.Common.CategoryInfo.FloorPrice) / 100
		requestObject.Secure = 1
		requestObject.Rid = p.Common.UUID

		if p.Common.IsAndroid() {
			requestObject.Platform = "ANDROID"

			if p.Common.IsAndroidMajorLessThanTen() {
				if len(p.Common.MhReq.Device.Imei) > 0 {
					requestObject.Imei = p.Common.MhReq.Device.Imei
				} else if len(p.Common.MhReq.Device.ImeiMd5) > 0 {
					requestObject.ImeiMd5 = p.Common.MhReq.Device.ImeiMd5
				} else {
					p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
						up_common.MH_UP_ERROR_CODE_900101,
						up_common.MH_UP_ERROR_CODE_104013)
				}
			} else {
				if len(p.Common.MhReq.Device.Oaid) > 0 {
					requestObject.Oaid = p.Common.MhReq.Device.Oaid
				} else {
					p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
						up_common.MH_UP_ERROR_CODE_900101,
						up_common.MH_UP_ERROR_CODE_104013)
				}
			}
		} else {
			requestObject.Platform = "IOS"

			if len(p.Common.MhReq.Device.Idfa) > 0 {
				requestObject.Idfa = p.Common.MhReq.Device.Idfa
			} else if len(p.Common.MhReq.Device.IdfaMd5) > 0 {
				requestObject.IdfaMd5 = p.Common.MhReq.Device.IdfaMd5
			}
		}
	}
	p.Request = &requestObject

	return p
}

func (p *JiatouPipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.Platform = NewJiatouOS(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Version = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Model = p.Common.ReplacedValues.Model
	}

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Brand = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		p.Request.Imei = p.Common.ReplacedValues.Imei
	}

	if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
		p.Request.ImeiMd5 = p.Common.ReplacedValues.ImeiMd5
	}

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		p.Request.Oaid = p.Common.ReplacedValues.Oaid
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		p.Request.Idfa = p.Common.ReplacedValues.Idfa
	}

	if len(p.Common.ReplacedValues.IdfaMd5) > 0 {
		p.Request.IdfaMd5 = p.Common.ReplacedValues.IdfaMd5
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		p.Request.AndroidId = p.Common.ReplacedValues.AndroidId
	}

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Ua = p.Common.ReplacedValues.Ua
	}

	return p
}

func (p *JiatouPipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(jsonData))
	httpRequest.Header.Add("Content-Type", "application/json")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode != 200 {
		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		p.Common.SetPanicStringAndCodes("status is no 200",
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	var responseObject JiatouResponseObject
	json.Unmarshal(bodyContent, &responseObject)

	p.Response = &responseObject

	if len(p.Response.Error) > 0 {
		p.Common.SetPanicStringAndCodes("upstream request error: "+p.Response.Error,
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *JiatouPipline) SetupCommonResponse() up_common.PiplineInterface {

	bidPrice := 0

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	var commonAPIResponseObject up_common.UpCommonAPIResponseObject

	commonAPIResponseObject.AdId = p.Response.CreativeId

	if len(p.Response.Title) > 0 {
		commonAPIResponseObject.Title = p.Response.Title
	}

	if len(p.Response.Body) > 0 {
		commonAPIResponseObject.Description = p.Response.Body
	}

	if len(p.Response.AppPackageName) > 0 {
		commonAPIResponseObject.PackageName = p.Response.AppPackageName
	}

	if len(p.Response.GDTDownloadUrl) > 0 || len(p.Response.AppDownloadUrl) > 0 {
		commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD

		if len(p.Response.GDTDownloadUrl) > 0 {
			commonAPIResponseObject.DownloadUrl = p.Response.GDTDownloadUrl
		} else if len(p.Response.AppDownloadUrl) > 0 {
			commonAPIResponseObject.DownloadUrl = p.replaceLinkString(p.Response.AppDownloadUrl)
		}

		commonAPIResponseObject.LandPageUrl = p.Response.LandingPageUrl
		commonAPIResponseObject.AdUrl = p.Response.Click
	}

	if p.Common.MhReq.Device.Os == up_common.MH_UP_COMMON_OS_ANDROID.String() {

		if len(p.Response.Adveristiser) > 0 {
			commonAPIResponseObject.Publisher = p.Response.Adveristiser
		}

		if len(p.Response.AppName) > 0 {
			commonAPIResponseObject.AppName = p.Response.AppName
		}

		if len(p.Response.AppVersion) > 0 {
			commonAPIResponseObject.AppVersion = p.Response.AppVersion
		}

		if len(p.Response.AppPrivacy) > 0 {
			commonAPIResponseObject.PrivacyUrl = p.Response.AppPrivacy
		}

		if len(p.Response.AppPermissions) > 0 {
			commonAPIResponseObject.Permission = p.Response.AppPermissions
		} else {
			commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
		}
		commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024
	} else {
		if len(p.Response.LandingPageUrl) > 0 {
			if strings.Contains(p.Response.LandingPageUrl, "apple.com") {
				commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
			} else {
				commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE
			}
		}
	}

	commonAPIResponseObject.Crid = p.Response.CreativeId
	commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
	commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

	var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

	if len(p.Response.Deeplink) > 0 {
		commonAPIResponseObject.DeepLink = p.replaceLinkString(p.Response.Deeplink)

		var convTrack up_common.UpCommonAPIResponseConvTrackObject
		convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

		var respListItemDeepLinkArray []string

		mhDPParams := url.Values{}
		mhDPParams.Add("result", "0")
		mhDPParams.Add("reason", "")
		mhDPParams.Add("deeptype", "__DEEP_TYPE__")
		bigdataParams := up_common.EncodeParams(
			p.Common.MhReq,
			p.Common.LocalPos,
			p.Common.PlatformPos,
			p.Common.UUID,
			p.Common.UUID,
			0,
			0,
			0,
			0)
		mhDPParams.Add("log", bigdataParams)

		respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

		// if len(p.Response.DpCm) > 0 {
		// 	respListItemDeepLinkArray = append(respListItemDeepLinkArray, p.Response.DpCm...)
		// }

		convTrack.ConvUrls = respListItemDeepLinkArray
		commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

		if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

			var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
			convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

			var respListItemDeepLinkFailedArray []string

			mhDPFailedParams := url.Values{}
			mhDPFailedParams.Add("result", "1")
			mhDPFailedParams.Add("reason", "3")
			mhDPFailedParams.Add("log", bigdataParams)

			respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
			convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
			commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
		}
	}

	if len(p.Response.StartDownloadMonitorUrls) > 0 {
		var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
		commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
		commonAPIResponseConvTrackObject.ConvUrls = p.Response.StartDownloadMonitorUrls
		commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
	}

	if len(p.Response.FinishDownloadMonitorUrls) > 0 {
		var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
		commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
		commonAPIResponseConvTrackObject.ConvUrls = p.Response.FinishDownloadMonitorUrls
		commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
	}

	if len(p.Response.FinishInstallMonitorUrls) > 0 {
		var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
		commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
		commonAPIResponseConvTrackObject.ConvUrls = p.Response.FinishInstallMonitorUrls
		commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
	}

	if len(commonAPIResponseConvTrackObjects) > 0 {
		commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
	}

	if len(p.Response.AppIcon) > 0 {
		commonAPIResponseObject.IconUrl = p.Response.AppIcon
	} else {
		commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(p.Response.Deeplink)
	}

	// 如果是视频类型
	if p.Common.PlatformPos.PlatformPosMaterialType == 1 {
		// 视频
		var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
		if p.Response.Duration > 0 {
			commonAPIResponseVideoObject.Duration = p.Response.Duration * 1000
		}
		commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
		commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight

		commonAPIResponseVideoObject.VideoUrl = p.Response.AdUrl

		if len(p.Response.ImgUrl) > 0 {
			commonAPIResponseVideoObject.CoverUrl = p.Response.ImgUrl
		} else if len(p.Response.AdUrls) > 0 {
			commonAPIResponseVideoObject.CoverUrl = p.Response.AdUrls[0]
		} else {
			if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
				commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
				if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
				}
			} else {
				commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
				if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
					commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
				}
			}
		}

		commonAPIResponseVideoObject.EndcardType = 1
		commonAPIResponseVideoObject.EndcardRange = 1

		var commonAPIResponseVideoEventTrackObjects []*up_common.UpCommonAPIResponseVideoEventTrackObject

		if len(p.Response.Start) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_START
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.Start
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.FirstQuartile) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_FIRSTQUARTILE
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.FirstQuartile
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.Midpoint) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_MIDPOINT
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.Midpoint
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.ThirdQuartile) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_THIRDUARTILE
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.ThirdQuartile
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.Complete) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_END
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.Complete
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.Skip) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_SKIP
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.Skip
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(p.Response.CloseLinear) > 0 {
			var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
			commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_CLOSE
			commonAPIResponseVideoEventTrackObject.EventTrackUrls = p.Response.CloseLinear
			commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
		}

		if len(commonAPIResponseVideoEventTrackObjects) > 0 {
			commonAPIResponseVideoObject.EventTracks = commonAPIResponseVideoEventTrackObjects
		}

		commonAPIResponseObject.Video = &commonAPIResponseVideoObject
		commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
	} else {
		// 图片
		var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

		if len(p.Response.AdUrls) > 0 {
			for _, imageUrl := range p.Response.AdUrls {
				var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
				commonAPIResponseImageObject.Url = imageUrl
				commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
				commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
				commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
			}
		} else if len(p.Response.AdUrl) > 0 {
			var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
			commonAPIResponseImageObject.Url = p.Response.AdUrl
			commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
			commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
		}

		commonAPIResponseObject.Images = commonAPIResponseImageObjects
		commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

		if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
			p.Common.SetPanicStringAndCodes("images is empty",
				up_common.MH_UP_ERROR_CODE_900201,
				up_common.MH_UP_ERROR_CODE_102006)
		}
	}

	list = append(list, &commonAPIResponseObject)

	bigdataParams := up_common.EncodeParams(
		p.Common.MhReq,
		p.Common.LocalPos,
		p.Common.PlatformPos,
		p.Common.UUID,
		p.Common.UUID,
		p.Common.CategoryInfo.FloorPrice,
		p.Common.CategoryInfo.FinalPrice,
		bidPrice,
		0)

	if len(p.Response.Pm) > 0 {
		var impressionLinks []string

		p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

		impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

		for _, linkString := range p.Response.Pm {

			replacedLinkString := p.replaceLinkString(linkString)

			impressionLinks = append(impressionLinks, replacedLinkString)
		}
		commonAPIResponseObject.ImpressionLink = impressionLinks

	}

	if len(p.Response.Cm) > 0 {
		var clickLinks []string

		p.Common.MhClickLink.AddBigDataParams(bigdataParams)

		clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

		for _, linkString := range p.Response.Cm {

			replacedLinkString := p.replaceLinkString(linkString)

			clickLinks = append(clickLinks, replacedLinkString)
		}

		if len(p.Response.DpCm) > 0 {
			clickLinks = append(clickLinks, p.Response.DpCm...)
		}

		commonAPIResponseObject.ClickLink = clickLinks
	}

	commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
	commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *JiatouPipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
