package core

import (
	"encoding/json"
	"mh_proxy/db"
	"mh_proxy/models"
	"strconv"
	"strings"
	"time"
)

func IsMaterialAdShieldOK(platformPos *models.PlatformPosStu, mhReq *models.MHReq, item *models.MHRespDataItem) (bool, int) {
	isOK, code, flag := isWhile(platformPos, mhReq, item)
	if !isOK {
		return isOK, code
	}
	if !flag {
		return isBlack(platformPos, mhReq, item)
	}
	return true, 900000
}

func isWhile(platformPos *models.PlatformPosStu, mhReq *models.MHReq, item *models.MHRespDataItem) (bool, int, bool) {
	corpID := strconv.Itoa(platformPos.PlatformAppCorpID)
	if platformPos.PlatformMediaID == "99" {
		corpID = "1"
	}

	platformKey := "go_ad_fhield_white_platform_" + string(platformPos.PlatformMediaID) + "_" + corpID
	appKey := "go_ad_fhield_white_app_" + platformPos.PlatformAppID
	posKey := "go_ad_fhield_white_pos_" + platformPos.PlatformPosID
	platformValue, _ := db.GlbBigCacheMinute.Get(platformKey)
	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(platformValue) == 0 && len(posValue) == 0 {
		return true, 900000, false
	}
	var adShield models.AdShieldBigCache
	if len(platformValue) > 0 && len(appValue) == 0 && len(posValue) == 0 {
		_ = json.Unmarshal(platformValue, &adShield)
	}
	if len(appValue) > 0 && len(posValue) == 0 {
		_ = json.Unmarshal(appValue, &adShield)
	}
	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &adShield)
	}

	expUrl := adShield.ExpUrl
	adUrl := adShield.AdUrl
	shieldKey := adShield.ShieldKey
	materialUrl := adShield.MaterialUrl
	packageName := adShield.PackageName
	timeList := adShield.TimeList
	regionList := adShield.RegionList
	adShieldType := adShield.AdShieldType

	if adShieldType > 0 {
		switch adShieldType {
		case 1: // 下载
			if item.InteractType == 0 {
				return false, 900111, true
			}
		case 2: // h5
			if item.InteractType == 1 {
				return false, 900111, true
			}
		}
	}

	if len(expUrl) > 0 {
		expUrlArray := strings.Split(expUrl, ",")
		for _, impressionUrl := range item.ImpressionLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(impressionUrl, expUrlItem) {
					return true, 900000, true
				}
			}
		}
		for _, clickUrl := range item.ClickLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(clickUrl, expUrlItem) {
					return true, 900000, true
				}
			}
		}
	}

	if len(adUrl) > 0 {
		adUrlArray := strings.Split(adUrl, ",")
		for _, adUrlItem := range adUrlArray {
			if len(item.LandpageURL) > 0 {
				if strings.Contains(item.LandpageURL, adUrlItem) {
					return true, 900000, true
				}
			}
			if len(item.DownloadURL) > 0 {
				if strings.Contains(item.DownloadURL, adUrlItem) {
					return true, 900000, true
				}
			}
			if len(item.DeepLink) > 0 {
				if strings.Contains(item.DeepLink, adUrlItem) {
					return true, 900000, true
				}
			}
			if len(item.AdURL) > 0 {
				if strings.Contains(item.AdURL, adUrlItem) {
					return true, 900000, true
				}
			}
		}
	}

	if len(shieldKey) > 0 {
		shieldKeyArray := strings.Split(shieldKey, ",")
		for _, shieldKeyItem := range shieldKeyArray {
			if len(item.Title) > 0 {
				if strings.Contains(item.Title, shieldKeyItem) {
					return true, 900000, true
				}
			}
			if len(item.Description) > 0 {
				if strings.Contains(item.Description, shieldKeyItem) {
					return true, 900000, true
				}
			}
		}
	}

	if len(materialUrl) > 0 {
		materialUrlArray := strings.Split(materialUrl, ",")
		for _, materialUrlItem := range materialUrlArray {
			if len(item.Image) > 0 {
				if strings.Contains(item.Image[0].URL, materialUrlItem) {
					return true, 900000, true
				}
			}
			if item.Video != nil && len(item.Video.VideoURL) > 0 {
				if strings.Contains(item.Video.VideoURL, materialUrlItem) {
					return true, 900000, true
				}
				if strings.Contains(item.Video.CoverURL, materialUrlItem) {
					return true, 900000, true
				}
			}

			if len(item.IconURL) > 0 {
				if strings.Contains(item.IconURL, materialUrlItem) {
					return true, 900000, true
				}
			}
		}
	}

	if len(packageName) > 0 {
		packageNameArray := strings.Split(packageName, ",")
		for _, packageNameItem := range packageNameArray {
			if strings.Contains(item.PackageName, packageNameItem) {
				return true, 900000, true
			}
		}
	}

	if len(timeList) > 0 {
		hh := time.Now().Format("15")
		timeListArray := strings.Split(timeList, ",")
		for _, timeItem := range timeListArray {
			if strings.Contains(hh, timeItem) {
				return true, 900000, true
			}
		}
	}

	if len(regionList) > 0 {
		regionListArray := strings.Split(regionList, ",")
		for _, regionItem := range regionListArray {
			if strings.Contains(mhReq.Device.IPProvince, regionItem) {
				return true, 900000, true
			}
			if strings.Contains(mhReq.Device.IPCity, regionItem) {
				return true, 900000, true
			}
		}
	}

	return false, 900111, true
}

func isBlack(platformPos *models.PlatformPosStu, mhReq *models.MHReq, item *models.MHRespDataItem) (bool, int) {
	corpID := strconv.Itoa(platformPos.PlatformAppCorpID)
	if platformPos.PlatformMediaID == "99" {
		corpID = "1"
	}

	platformKey := "go_ad_fhield_platform_" + string(platformPos.PlatformMediaID) + "_" + corpID
	appKey := "go_ad_fhield_app_" + platformPos.PlatformAppID
	posKey := "go_ad_fhield_pos_" + platformPos.PlatformPosID
	platformValue, _ := db.GlbBigCacheMinute.Get(platformKey)
	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(platformValue) == 0 && len(posValue) == 0 {
		return true, 900000
	}

	var adShield models.AdShieldBigCache
	if len(platformValue) > 0 && len(appValue) == 0 && len(posValue) == 0 {
		_ = json.Unmarshal(platformValue, &adShield)
	}
	if len(appValue) > 0 && len(posValue) == 0 {
		_ = json.Unmarshal(appValue, &adShield)
	}
	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &adShield)
	}

	expUrl := adShield.ExpUrl
	adUrl := adShield.AdUrl
	shieldKey := adShield.ShieldKey
	materialUrl := adShield.MaterialUrl
	packageName := adShield.PackageName
	timeList := adShield.TimeList
	regionList := adShield.RegionList
	adShieldType := adShield.AdShieldType

	if adShieldType > 0 {
		switch adShieldType {
		case 1: // 下载
			if item.InteractType == 0 {
				return false, 900111
			}
		case 2: // h5
			if item.InteractType == 1 {
				return false, 900111
			}
		}
	}

	if len(expUrl) > 0 {
		expUrlArray := strings.Split(expUrl, ",")
		for _, impressionUrl := range item.ImpressionLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(impressionUrl, expUrlItem) {
					return false, 900111
				}
			}
		}
		for _, clickUrl := range item.ClickLink {
			for _, expUrlItem := range expUrlArray {
				if strings.Contains(clickUrl, expUrlItem) {
					return false, 900111
				}
			}
		}
	}

	if len(adUrl) > 0 {
		adUrlArray := strings.Split(adUrl, ",")
		for _, adUrlItem := range adUrlArray {
			if len(item.LandpageURL) > 0 {
				if strings.Contains(item.LandpageURL, adUrlItem) {
					return false, 900111
				}
			}
			if len(item.DownloadURL) > 0 {
				if strings.Contains(item.DownloadURL, adUrlItem) {
					return false, 900111
				}
			}
			if len(item.DeepLink) > 0 {
				if strings.Contains(item.DeepLink, adUrlItem) {
					return false, 900111
				}
			}
			if len(item.AdURL) > 0 {
				if strings.Contains(item.AdURL, adUrlItem) {
					return false, 900111
				}
			}
		}
	}

	if len(shieldKey) > 0 {
		shieldKeyArray := strings.Split(shieldKey, ",")
		for _, shieldKeyItem := range shieldKeyArray {
			if len(item.Title) > 0 {
				if strings.Contains(item.Title, shieldKeyItem) {
					return false, 900111
				}
			}
			if len(item.Description) > 0 {
				if strings.Contains(item.Description, shieldKeyItem) {
					return false, 900111
				}
			}
		}
	}

	if len(materialUrl) > 0 {
		materialUrlArray := strings.Split(materialUrl, ",")
		for _, materialUrlItem := range materialUrlArray {
			if len(item.Image) > 0 {
				if strings.Contains(item.Image[0].URL, materialUrlItem) {
					return false, 900111
				}
			}
			if item.Video != nil && len(item.Video.VideoURL) > 0 {
				if strings.Contains(item.Video.VideoURL, materialUrlItem) {
					return false, 900111
				}
				if strings.Contains(item.Video.CoverURL, materialUrlItem) {
					return false, 900111
				}
			}

			if len(item.IconURL) > 0 {
				if strings.Contains(item.IconURL, materialUrlItem) {
					return false, 900111
				}
			}
		}
	}

	if len(packageName) > 0 {
		packageNameArray := strings.Split(packageName, ",")
		for _, packageNameItem := range packageNameArray {
			if strings.Contains(item.PackageName, packageNameItem) {
				return false, 900111
			}
		}
	}

	if len(timeList) > 0 {
		hh := time.Now().Format("15")
		timeListArray := strings.Split(timeList, ",")
		for _, timeItem := range timeListArray {
			if strings.Contains(hh, timeItem) {
				return false, 900111
			}
		}
	}

	if len(regionList) > 0 {
		regionListArray := strings.Split(regionList, ",")
		for _, regionItem := range regionListArray {
			if strings.Contains(mhReq.Device.IPProvince, regionItem) {
				return false, 900111
			}
			if strings.Contains(mhReq.Device.IPCity, regionItem) {
				return false, 900111
			}
		}
	}

	return true, 900000
}
