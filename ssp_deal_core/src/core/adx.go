package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_admobile_freeze"
	"mh_proxy/core/up_adnine_freeze"
	"mh_proxy/core/up_common"
	"mh_proxy/core/up_coolads_freeze"
	"mh_proxy/core/up_fancy_freeze"
	"mh_proxy/core/up_jiatou_freeze"
	"mh_proxy/core/up_wangmai_freeze"
	"mh_proxy/core/up_wycl_freeze"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"net/url"
	"regexp"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/ip_to_region/core/ip2location"
	uuid "github.com/satori/go.uuid"
)

// GetADFromAdxWithContext ...
func GetADFromAdxWithContext(c context.Context, mhReq *models.MHReq, bigdataUID string) *models.MHResp {
	start := time.Now()
	timeout := 800 // 默认800ms
	losPos := models.GetLocalPosInfo(c, strconv.Itoa(mhReq.Pos.ID), mhReq.App.AppID)
	if losPos != nil && losPos.LocalPosTimeOut > 0 {
		timeout = losPos.LocalPosTimeOut
	}

	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Millisecond)
	defer cancel()

	// context
	resultChan := make(chan struct {
		respStu *models.MHResp
	}, 1)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("api task goroutine appID=%v, posID=%v, timeout=%v, cost=%v, panic: %v\n", mhReq.App.AppID, mhReq.Pos.ID, timeout, time.Since(start), err)
				resultChan <- struct {
					respStu *models.MHResp
				}{
					respStu: &models.MHResp{Ret: 102006, Msg: "panic"},
				}
			}
		}()

		resultChan <- struct {
			respStu *models.MHResp
		}{
			respStu: GetADFromAdx(ctx, mhReq, bigdataUID),
		}
	}()

	// 等待结果或超时
	select {
	case data := <-resultChan:
		fmt.Printf("api task complete appID=%v, posID=%v, timeout=%v, cost=%v, respMap=%v\n", mhReq.App.AppID, mhReq.Pos.ID, timeout, time.Since(start), data.respStu)
		// c.PureJSON(200, data.respStu)
		return data.respStu
	case <-ctx.Done():
		// 请求超时，返回错误
		fmt.Printf("api task timeout, appID=%v, posID=%v, timeout=%v, cost=%v\n", mhReq.App.AppID, mhReq.Pos.ID, timeout, time.Since(start))
		return &models.MHResp{Ret: 102006, Msg: ""}
	}
}

// GetADFromAdx ...
func GetADFromAdx(c context.Context, mhReq *models.MHReq, bigdataUID string) *models.MHResp {
	// fmt.Println("adx begin")

	localAppID := mhReq.App.AppID
	localPosID := mhReq.Pos.ID

	downBeginTime := utils.GetCurrentMilliSecond()

	localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println("local 0")
	// fmt.Println(localPosInfo)
	// fmt.Println("local 1")
	if localPosInfo == nil {
		return MhApiErrorRespMap(100007, "")
	}

	if localPosInfo.LocalPosIsActive != 1 {
		return MhApiErrorRespMap(100135, "")
	}

	// 补参数
	paramRepair(c, mhReq, localPosInfo)

	if localPosInfo.LocalAppType == "1" {
	} else {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("redis did panic:", err)
				}
			}()
			models.RedisEntryDID(c, mhReq, localPosInfo, "")

			// models.DebugReqDataToBigData2(c, mhReq, localPosInfo, bigdataUID)
		}()
	}

	randomNumber := rand.Intn(100000)
	if randomNumber == 0 {
		models.GoBigDataHoloDebugeDpi(localAppID, mhReq.Device.DPI)
	}

	// 判定参数
	if localPosInfo.LocalAppIsVerifyParam == 1 {
		paramVerifyCode, internalErrorCode := paramVerify(c, bigdataUID, mhReq, localPosInfo)
		// fmt.Println("param verify code: " + strconv.Itoa(paramVerifyCode))
		if paramVerifyCode != 0 {
			// fmt.Println("param verify code: " + strconv.Itoa(paramVerifyCode))
			// fmt.Println("param internal error code: " + strconv.Itoa(internalErrorCode))
			// bigdata

			// 并发存大数据
			var respExtraData models.MHUpRespExtra
			respExtraData.BigdataUID = bigdataUID
			respExtraData.DownCostTime = utils.GetCurrentMilliSecond() - downBeginTime
			respExtraData.ExternalCode = paramVerifyCode
			respExtraData.InternalCode = internalErrorCode

			go func(mhReq *models.MHReq, localPosInfo *models.LocalPosStu, respExtraData *models.MHUpRespExtra) {
				// fmt.Println("Goroutine bigdata")
				defer func() {
					if err := recover(); err != nil {
						fmt.Println("bigdata req panic:", err)
					}
				}()

				models.BigDataAdxCoReq(c, mhReq, localPosInfo, respExtraData)
			}(mhReq, localPosInfo, &respExtraData)

			return MhApiErrorRespMap(paramVerifyCode, "")
		}
	}
	if len(mhReq.Device.IP) == 0 {
		return MhApiErrorRespMap(104021, "")
	}

	// 协议的app list, https://docs.qq.com/sheet/DQlZzS1pycEJWeVFZ?tab=BB08J2
	//if len(mhReq.Device.AppList) > 0 {
	//	go func(mhReq *models.MHReq, localPosInfo *models.LocalPosStu) {
	//		defer func() {
	//			if err := recover(); err != nil {
	//				fmt.Println("bigdata al panic:", err)
	//			}
	//		}()
	//		models.GoBigDataHoloAppListDidData(mhReq)
	//
	//		if localPosInfo.LocalAppID == "10012" || localPosInfo.LocalAppID == "10016" {
	//			models.GoBigDataHoloDebugAppListData(localPosInfo, mhReq)
	//		} else {
	//			applisRandomNumber := rand.Intn(10000)
	//			if applisRandomNumber == 0 {
	//				models.GoBigDataHoloDebugAppListData(localPosInfo, mhReq)
	//			}
	//		}
	//
	//	}(mhReq, localPosInfo)
	//}

	// var respStuTest models.MHResp
	// respStuTest.Ret = 9
	// return &respStuTest

	// 获取要跑的上游列表
	category := models.GetAPIConfigFromCategoryPrice(c, strconv.Itoa(localPosID), localAppID)
	if category == nil {
		// 并发存大数据
		var respExtraData models.MHUpRespExtra
		respExtraData.BigdataUID = bigdataUID
		respExtraData.DownCostTime = utils.GetCurrentMilliSecond() - downBeginTime
		respExtraData.ExternalCode = 102006
		respExtraData.InternalCode = 900001

		go func(mhReq *models.MHReq, localPosInfo *models.LocalPosStu, respExtraData *models.MHUpRespExtra) {
			// fmt.Println("Goroutine bigdata")
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("bigdata req panic:", err)
				}
			}()

			models.BigDataAdxCoReq(c, mhReq, localPosInfo, respExtraData)
		}(mhReq, localPosInfo, &respExtraData)

		return MhApiErrorRespMap(102006, "")
	}
	// fmt.Println(category)
	// fmt.Println(len(*category))
	if len(*category) == 0 {

		// 并发存大数据
		var respExtraData models.MHUpRespExtra
		respExtraData.BigdataUID = bigdataUID
		respExtraData.DownCostTime = utils.GetCurrentMilliSecond() - downBeginTime
		respExtraData.ExternalCode = 102006
		respExtraData.InternalCode = 900001

		go func(mhReq *models.MHReq, localPosInfo *models.LocalPosStu, respExtraData *models.MHUpRespExtra) {
			// fmt.Println("Goroutine bigdata")
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("bigdata req panic:", err)
				}
			}()

			models.BigDataAdxCoReq(c, mhReq, localPosInfo, respExtraData)
		}(mhReq, localPosInfo, &respExtraData)

		return MhApiErrorRespMap(102006, "")
	}

	// 流量包方式去dsp取数据, 曝光和点击监测链接
	var dspExpLinks []string
	var dspClkLinks []string
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("flow panic:", err)
			}
		}()
		dspResp := GetFromDspByFlow(c, mhReq, localPosInfo, bigdataUID)
		if dspResp != nil {
			for _, item := range dspResp.Data {
				dspExpLinks = append(dspExpLinks, item.ExpLinks...)
				dspClkLinks = append(dspClkLinks, item.ClkLinks...)
			}
		}
	}()

	// var respMap []map[string]interface{}
	var wg sync.WaitGroup
	wg.Add(len(*category))

	respMap := make([]*models.MHUpResp, len(*category))

	for i := 0; i < len(*category); i++ {
		go func(categoryIndex int, category models.CategoryStu) {
			defer func(tmpCategory models.CategoryStu) {
				if err := recover(); err != nil {
					fmt.Println("serious panic:", err, tmpCategory.PlatformPosID, tmpCategory.PlatformAppID, tmpCategory.PlatformMediaID)
					fmt.Println(string(debug.Stack()))

					wg.Done()
				}
			}(category)

			if category.PlatformMediaID == "99" {
				// dsp
				var platformPosInfo models.PlatformPosStu
				platformPosInfo.PlatformPosID = category.PlatformPosID
				platformPosInfo.PlatformAppID = category.PlatformAppID
				platformPosInfo.PlatformMediaID = "99"
				platformPosInfo.PlatformAppIsActive = 1
				platformPosInfo.PlatformPosIsActive = 1

				upBeginTime := utils.GetCurrentMilliSecond()

				respData := GetFromDsp(c, mhReq, localPosInfo, &platformPosInfo, category, bigdataUID)

				if respData != nil {
					respData.Extra.PlatformAppID = category.PlatformAppID
					respData.Extra.PlatformPosID = category.PlatformPosID
					respData.Extra.RespCostTime = utils.GetCurrentMilliSecond() - upBeginTime

					respData.Extra.PlatformPos = &platformPosInfo
					respData.Extra.Category = &category

					respMap[categoryIndex] = respData
				}
			} else {
				platformPosInfo := models.GetPlatformPosInfo(c, category.PlatformPosID, category.PlatformAppID, category.PlatformMediaID)
				if platformPosInfo != nil {
					upBeginTime := utils.GetCurrentMilliSecond()

					var respData *models.MHUpResp

					switch category.PlatformMediaID {
					case "1":
						respData = GetFromGdt(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "2", "36":
						respData = GetFromCSJ(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "3":
					case "5":
						respData = GetFrom360(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "7":
						respData = up_wangmai_freeze.GetFromWangmai(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "8":
						respData = GetFromBaiQingTeng(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "10":
						respData = GetFromLenovo(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "13":
						respData = GetFromYouDao(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "14":
						respData = GetFromKuaishou(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "15":
						respData = GetFromXiaoMi(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "16":
						respData = GetFromOPPO(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "17":
					case "18":
						respData = GetFromIQiYi(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "19":
						respData = GetFromJD(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "20":
					case "21":
						respData = GetFromVIVO(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "23":
					case "24":
						respData = GetFromHuaWei(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "25":
						respData = GetFromZhiMeng(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "26":
						respData = GetFromBeiJingShiDai(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "27":
						respData = GetFromZuiYou(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "28":
						respData = GetFromDomob(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "29":
						respData = GetFromBaiHe(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "30":
						respData = GetFromXinShu(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "31":
					case "32":
						respData = GetFromFengHuang(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "33":
						respData = GetFromPinDuoDuo(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "34":
					case "35":
						respData = GetFromMiMan(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "37":
						respData = up_admobile_freeze.GetFromADmobile(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "38":
						respData = up_jiatou_freeze.GetFromJiatou(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "40":
						respData = up_fancy_freeze.GetFromFancy(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "41":
						respData = up_coolads_freeze.GetFromCoolads(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "42":
						respData = up_adnine_freeze.GetFromAdnine(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "43", "57":
						respData = GetFromTanx(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "44":
						respData = up_wycl_freeze.GetFromWycl(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "45":
						respData = GetFromUUnion(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "46":
						respData = GetFromQingShan(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "47":
						respData = GetFromQiHang(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					case "48":
						respData = GetFromTianGong(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "49":
						respData = GetFromXimeng(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "50":
						respData = GetFromHx(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "51":
						respData = GetFromupXingFan(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "52":
						respData = GetFromupYueDong(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "53":
						respData = GetFromupQuna(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "54":
						respData = GetFromupLanwa(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "55":
						respData = GetFrom360OS(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "56":
						respData = GetFromupLanzhi(c, mhReq, localPosInfo, platformPosInfo, &category, bigdataUID)
					case "98":
						respData = GetFromMaplehazeRTB(c, mhReq, localPosInfo, platformPosInfo, category, bigdataUID)
					default:
						fmt.Println("no config p_media_id: ", category.PlatformMediaID)
					}

					if respData != nil {
						respData.Extra.PlatformAppID = category.PlatformAppID
						respData.Extra.PlatformPosID = category.PlatformPosID
						respData.Extra.RespCostTime = utils.GetCurrentMilliSecond() - upBeginTime

						respData.Extra.PlatformPos = platformPosInfo
						respData.Extra.Category = &category

						respMap[categoryIndex] = respData
					}
				}
			}

			wg.Done()
		}(i, (*category)[i])
	}
	wg.Wait()

	////////////////////////////////////////////////////////////////////////////////////////
	var normalRespMap []*models.MHUpResp
	for _, item := range respMap {
		if item == nil {
			continue
		}

		normalRespMap = append(normalRespMap, item)
	}
	if len(normalRespMap) == 0 {
		return MhApiErrorRespMap(102006, "")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// 处理素材
	for _, item := range normalRespMap {
		if item.Extra.ExternalCode == 0 {
			if len(utilities.DevicePriceMediaID) > 0 {
				MediaIDArray := strings.Split(utilities.DevicePriceMediaID, ",")
				for _, channel := range MediaIDArray {
					if item.Extra.PlatformPos.PlatformMediaID == channel {
						go func(ctx context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, mhUpResp *models.MHUpResp) {
							defer func() {
								if err := recover(); err != nil {
									fmt.Println("device price kafka panic:", err)
								}
							}()
							models.DevicePirceRawDataKafka(ctx, mhReq, localPos, mhUpResp)
						}(c, mhReq, localPosInfo, item)
					}
				}
			}
			HandleMaterial(c, bigdataUID, mhReq, localPosInfo, item, dspExpLinks, dspClkLinks)
		}
	}

	// 计算win adid
	var winAdidArray []string

	// 计算强制, 非强制竞胜个数ecpm排序
	var mhRespForceWinDataItem []models.MHRespDataItem
	var mhRespDataItem []models.MHRespDataItem

	for _, item := range normalRespMap {
		if item.Extra.ExternalCode == 0 {
			if item.Extra.Category.IsForceWin == 1 {
				mhRespForceWinDataItem = append(mhRespForceWinDataItem, item.RespData.Data[localPosInfo.LocalPosID].List...)
			} else {
				mhRespDataItem = append(mhRespDataItem, item.RespData.Data[localPosInfo.LocalPosID].List...)
			}
		}
	}

	// 按照ecpm排序
	if len(mhRespForceWinDataItem) > 1 {
		sort.Sort(MaplehazeEcpmSort(mhRespForceWinDataItem))
	}
	if len(mhRespDataItem) > 1 {
		sort.Sort(MaplehazeEcpmSort(mhRespDataItem))
	}

	// 根据adcount取个数
	var mhAllRespForceWinDataItem []models.MHRespDataItem
	mhAllRespForceWinDataItem = append(mhAllRespForceWinDataItem, mhRespForceWinDataItem...)
	mhAllRespForceWinDataItem = append(mhAllRespForceWinDataItem, mhRespDataItem...)

	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}
	var destMHRespDataItem []models.MHRespDataItem
	if len(mhAllRespForceWinDataItem) > mhReq.Pos.AdCount {
		destMHRespDataItem = mhAllRespForceWinDataItem[:mhReq.Pos.AdCount]

		// 记录竞价成功竞价失败adid
		for idx, item := range mhAllRespForceWinDataItem {
			if idx < mhReq.Pos.AdCount {
				winAdidArray = append(winAdidArray, item.AdID)
			}
		}
	} else {
		destMHRespDataItem = mhAllRespForceWinDataItem

		// 记录竞价成功adid
		for _, item := range mhAllRespForceWinDataItem {
			winAdidArray = append(winAdidArray, item.AdID)
		}
	}

	// fmt.Println("kbg_debug bidding:", len(mhRespForceWinDataItem), len(mhRespDataItem), len(destMHRespDataItem), winAdidArray)
	////////////////////////////////////////////////////////////////////////////////////////
	// 竞价成功后处理上报上游竞价失败
	for _, item := range normalRespMap {
		if item == nil {
			continue
		}

		if item.Extra.ExternalCode == 0 {
			HandleWinLossNoticeURLsWithAdID(c, bigdataUID, mhReq, localPosInfo, item, winAdidArray)
		}
	}

	// 统计
	go func(mhReq *models.MHReq, localPosInfo *models.LocalPosStu, normalRespMap []*models.MHUpResp, winAdidArray []string) {
		// fmt.Println("Goroutine bigdata")
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata req panic:", err)
			}
		}()

		// 并发存大数据
		for idx, item := range normalRespMap {
			if item == nil {
				continue
			}

			respExtraData := item.Extra
			respExtraData.BigdataUID = bigdataUID
			respExtraData.DownCostTime = utils.GetCurrentMilliSecond() - downBeginTime

			tmpFloorPrice := item.Extra.FloorPrice
			tmpFinalPrice := item.Extra.FinalPrice

			if len(winAdidArray) > 0 {
				isInWinAdid := false
				if item.Extra.ExternalCode == 0 && item.RespData.Ret == 0 {
					if item.RespData.Data[localPosInfo.LocalPosID].List != nil {
						for _, item := range item.RespData.Data[localPosInfo.LocalPosID].List {
							for _, winAdidItem := range winAdidArray {
								if winAdidItem == item.AdID {
									isInWinAdid = true

									respExtraData.UpRespOKNum = respExtraData.UpRespOKNum + 1
									respExtraData.FloorPrice = respExtraData.FloorPrice + tmpFloorPrice
									respExtraData.FinalPrice = respExtraData.FinalPrice + tmpFinalPrice
									break
								}
							}
						}
					}
				}
				if isInWinAdid {
					respExtraData.IsWin = 1
				} else {
					respExtraData.IsWin = 2
				}
			}

			if len(winAdidArray) == 0 {
				if idx == 0 {
					respExtraData.IsWin = 1
				} else {
					respExtraData.IsWin = 2
				}
			}

			models.BigDataAdxCoReq(c, mhReq, localPosInfo, &respExtraData)
		}
	}(mhReq, localPosInfo, normalRespMap, winAdidArray)
	// 返回
	if len(destMHRespDataItem) == 0 {
		return MhApiErrorRespMap(102006, "")
	}

	maplehazeResp := models.MHResp{}
	maplehazeResp.Ret = 0
	maplehazeResp.Msg = ""
	maplehazeRespMapData := make(map[string]*models.MHRespData)
	maplehazeRespMapData[localPosInfo.LocalPosID] = &models.MHRespData{List: destMHRespDataItem}
	maplehazeResp.Data = maplehazeRespMapData

	return &maplehazeResp

	////////////////////////////////////////////////////////////////////////////////////////
	// if localPosInfo.LocalPosIsNeedAudit == 1 {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("api save to audit panic:", err)
	// 			}
	// 		}()
	// 		models.BigDataApiAudit(c, bigdataUID, localPosInfo, mhReq, destResp.RespData)
	// 	}()
	// }

	// 验证最后返回是否有效
	// isRespVaild := isMHRespVaild(c, localPosInfo, destResp.RespData)
	// if isRespVaild {
	// } else {
	// 	fmt.Println("kbg_debug_resp is not valid 105001")
	// 	return MhErrorRespMap(105001, "")
	// }
	////////////////////////////////////////////////////////////////////////////////////////
}

// GetSDKConfigFromAdx ...
func GetSDKConfigFromAdx(c context.Context, mhReq *models.MHReq) *map[string]interface{} {
	// fmt.Println("adx sdk begin")

	localAppID := mhReq.App.AppID
	localPosID := mhReq.Pos.ID

	localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println("local 0")
	// fmt.Println(localPosInfo)
	// fmt.Println("local 1")
	if localPosInfo == nil {
		return MhErrorRespMap(100007, "")
	}

	if localPosInfo.LocalPosIsActive != 1 {
		return MhErrorRespMap(100135, "")
	}

	bigdataUID := uuid.NewV4().String()

	category := models.GetSdkConfigFromCategoryPrice(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println(category)
	// fmt.Println(len(*category))
	if category == nil {
		return MhErrorRespMap(102006, "")
	}
	if len(*category) == 0 {
		return MhErrorRespMap(102006, "")
	}

	var platformPosInfo models.PlatformPosStu
	platformPosInfo.PlatformAppID = (*category)[0].PlatformAppID
	platformPosInfo.PlatformPosID = (*category)[0].PlatformPosID

	// impression_link array
	var respListItemImpArray []string
	// impression_link maplehaze
	mhImpParams := url.Values{}

	// for sdk
	maplehazeAdId := uuid.NewV4().String()
	platformPos := models.PlatformPosStu{PlatformAppID: "", PlatformPosID: "", PlatformAppType: "1"}
	bigdataParams := up_common.EncodeParams(mhReq, localPosInfo, &platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, 0)

	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	// click_link array
	var respListItemClkArray []string
	// click_link maplehaze
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", "__REQ_WIDTH__")
	mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	mhClkParams.Add("width", "__WIDTH__")
	mhClkParams.Add("height", "__HEIGHT__")
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("log", bigdataParams)

	respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

	// resp
	resp := map[string]interface{}{}
	resp["ret"] = 1
	resp["msg"] = ""

	resp["impression_link"] = respListItemImpArray
	resp["click_link"] = respListItemClkArray

	// sdk ban keyword
	resp["ban_keyword"] = ""

	var respListArray []map[string]interface{}

	for _, item := range *category {
		if item.IsActive == 1 {
		} else {
			continue
		}
		respListItem := map[string]interface{}{}
		respListItem["platform_pos_id"] = item.PlatformPosID
		respListItem["platform_pos_type"] = item.PlatformPosType
		respListItem["platform_app_id"] = item.PlatformAppID
		respListItem["platform_media_id"] = item.PlatformMediaID
		if item.Label == 3 {
			respListItem["mode"] = "1"
		} else {
			respListItem["mode"] = "0"
		}

		// 反作弊
		if item.Label == 3 {
			_, anticheatSDKCacheError := db.GlbBigCacheMinute.Get(fmt.Sprintf(config.AnticheatConfigSDK, item.PlatformMediaID))
			if anticheatSDKCacheError != nil {
			} else {
				// redis
				tmpRedisKey := fmt.Sprintf(rediskeys.ADX_SSP_ANTICHEAT_KEY, mhReq.Device.DIDMd5)
				_, tmpRedisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()
				if tmpRedisErr != nil {
				} else {
					continue
				}
			}
		}

		// black did 过滤jd sdk配置
		// if item.Label == 3 && item.PlatformMediaID == "19" {
		// 	isInBlack := false
		// 	for _, tmpBlackItem := range utilities.BlackDIDList {
		// 		// fmt.Println("debug_black_did:", tmpBlackItem)
		// 		if len(mhReq.Device.Imei) > 0 && tmpBlackItem == mhReq.Device.Imei {
		// 			isInBlack = true
		// 			break
		// 		}
		// 		if len(mhReq.Device.Oaid) > 0 && tmpBlackItem == mhReq.Device.Oaid {
		// 			isInBlack = true
		// 			break
		// 		}
		// 	}
		// 	if isInBlack {
		// 		continue
		// 	}
		// }
		// if item.PlatformMediaID == "15" {
		// 	respListItem["platform_app_key"] = item.PlatformAppKey
		// 	respListItem["platform_app_secret"] = item.PlatformAppSecret
		// }
		respListArray = append(respListArray, respListItem)
	}
	resp["data"] = respListArray

	// fmt.Println(resp)
	// fmt.Println("adx sdk end")
	return &resp
}

// GetSDKConfigV2FromAdx ...
func GetSDKConfigV2FromAdx(c context.Context, mhReq *models.MHReq) *map[string]interface{} {
	// fmt.Println("adx sdk begin")

	localAppID := mhReq.App.AppID
	localPosID := mhReq.Pos.ID

	localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println("local 0")
	// fmt.Println(localPosInfo)
	// fmt.Println("local 1")
	if localPosInfo == nil {
		return MhErrorRespMap(100007, "")
	}

	if localPosInfo.LocalPosIsActive != 1 {
		return MhErrorRespMap(100135, "")
	}

	bigdataUID := uuid.NewV4().String()

	category := models.GetSdkConfigFromCategoryPrice(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println(category)
	// fmt.Println(len(*category))
	if category == nil {
		return MhErrorRespMap(102006, "")
	}
	if len(*category) == 0 {
		return MhErrorRespMap(102006, "")
	}

	// sdk补参数
	paramSDKRepair(c, mhReq, localPosInfo)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("redis sdk did panic:", err)
			}
		}()

		models.RedisEntryDID(c, mhReq, localPosInfo, mhReq.SDKVersion)
	}()

	// impression_link array
	var respListItemImpArray []string
	// impression_link maplehaze
	mhImpParams := url.Values{}

	// for sdk
	maplehazeAdId := uuid.NewV4().String()
	platformPos := models.PlatformPosStu{PlatformAppID: "", PlatformPosID: "", PlatformAppType: "1"}
	bigdataParams := up_common.EncodeParams(mhReq, localPosInfo, &platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, 0)

	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	// click_link array
	var respListItemClkArray []string
	// click_link maplehaze
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", "__REQ_WIDTH__")
	mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	mhClkParams.Add("width", "__WIDTH__")
	mhClkParams.Add("height", "__HEIGHT__")
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("log", bigdataParams)

	respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

	// resp
	resp := map[string]interface{}{}
	resp["ret"] = 1
	resp["msg"] = ""

	resp["impression_link"] = respListItemImpArray
	resp["click_link"] = respListItemClkArray

	// sdk ban keyword
	resp["ban_keyword"] = ""

	// 是否并发
	if IsSDKVersionMoreThanOrEqual3021(c, mhReq.SDKVersion) {
		resp["is_concurrent"] = 1
	} else {
		resp["is_concurrent"] = 0
	}
	// 超时
	resp["timeout"] = localPosInfo.LocalPosSDKTimeOut
	// 支持开屏点击区域
	resp["splash_click_region"] = localPosInfo.LocalAppSplashClickRegion
	// 支持原生下载合规
	resp["native_download_compliance"] = localPosInfo.LocalAppNativeDownloadCompliance
	// 限频
	if localPosInfo.LocalAppIsLimitFrequency == 0 {
		resp["limit_frequency"] = 0
	} else {
		resp["limit_frequency"] = 0
		if len(localPosInfo.LocalAppLimitFrequencyJson) > 0 {
			var limitFrequencyArray []models.SDKLimitFrequencyStu
			json.Unmarshal([]byte(localPosInfo.LocalAppLimitFrequencyJson), &limitFrequencyArray)
			currentHour := time.Now().Hour()
			if len(limitFrequencyArray) > 0 {
				for _, item := range limitFrequencyArray {
					if item.BeginHour <= currentHour &&
						currentHour <= item.EndHour {
						resp["limit_frequency"] = utils.ConvertStringToInt(item.Interval)
						break
					}
				}
			}
		}
	}

	// 支持clipboard
	if localPosInfo.LocalAppSupportExtraClipboard == 1 {
		resp["ec"] = localPosInfo.LocalAppSupportExtraClipboard
	}
	// 支持notification
	if localPosInfo.LocalAppSupportExtraNotification == 1 {
		resp["en"] = localPosInfo.LocalAppSupportExtraNotification
	}
	// 支持wakeup
	if localPosInfo.LocalAppSupportExtraWakeUp == 1 {
		resp["ew"] = localPosInfo.LocalAppSupportExtraWakeUp
	}

	// extra限频
	if localPosInfo.LocalAppIsExtraLimitFrequency == 0 {
		resp["extra_limit_frequency"] = 0
	} else {
		resp["extra_limit_frequency"] = utils.ConvertStringToInt(localPosInfo.LocalAppExtraLimitFrequency)
	}

	// 是否失败价格上报
	resp["is_report_price_failed"] = localPosInfo.LocalAppIsReportPriceLoss

	var respListArray []map[string]interface{}

	for _, item := range *category {
		if item.IsActive == 1 {
		} else {
			continue
		}
		respListItem := map[string]interface{}{}
		respListItem["platform_pos_id"] = item.PlatformPosID
		respListItem["platform_pos_type"] = item.PlatformPosType
		respListItem["platform_pos_sub_type"] = item.PlatformPosMaterialType
		respListItem["platform_app_id"] = item.PlatformAppID
		respListItem["platform_media_id"] = item.PlatformMediaID
		respListItem["floor_price"] = item.FloorPrice
		respListItem["final_price"] = item.FinalPrice
		if item.Label == 3 {
			respListItem["mode"] = "1"
		} else {
			respListItem["mode"] = "0"
		}
		if item.PlatformMediaID == "43" {
			tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
			if len(tmpSDKPlatformPos.PlatformAppKey) > 0 {
				respListItem["platform_app_key"] = tmpSDKPlatformPos.PlatformAppKey
			}
			if len(tmpSDKPlatformPos.PlatformAppSecret) > 0 {
				respListItem["platform_app_secret"] = tmpSDKPlatformPos.PlatformAppSecret
			}
		}

		// 反作弊
		if item.Label == 3 {
			_, anticheatSDKCacheError := db.GlbBigCacheMinute.Get(fmt.Sprintf(config.AnticheatConfigSDK, item.PlatformMediaID))
			if anticheatSDKCacheError != nil {
			} else {
				// redis
				tmpRedisKey := fmt.Sprintf(rediskeys.ADX_SSP_ANTICHEAT_KEY, mhReq.Device.DIDMd5)
				_, tmpRedisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()
				if tmpRedisErr != nil {
				} else {
					continue
				}
			}
		}

		// black did 过滤jd sdk配置
		// if item.Label == 3 && item.PlatformMediaID == "19" {
		// 	isInBlack := false
		// 	for _, tmpBlackItem := range utilities.BlackDIDList {
		// 		// fmt.Println("debug_black_did:", tmpBlackItem)
		// 		if len(mhReq.Device.Imei) > 0 && tmpBlackItem == mhReq.Device.Imei {
		// 			isInBlack = true
		// 			break
		// 		}
		// 		if len(mhReq.Device.Oaid) > 0 && tmpBlackItem == mhReq.Device.Oaid {
		// 			isInBlack = true
		// 			break
		// 		}
		// 	}
		// 	if isInBlack {
		// 		continue
		// 	}
		// }

		// fmt.Println("kbg_debug p_app_id:", item.PlatformAppID)
		// fmt.Println("kbg_debug p_pos_id:", item.PlatformPosID)
		// fmt.Println("kbg_debug floor price:", item.FloorPrice)
		// fmt.Println("kbg_debug final price:", item.FinalPrice)
		// fmt.Println("kbg_debug white:", item.WhiteList)
		// fmt.Println("kbg_debug black:", item.BlackList)
		// fmt.Println("kbg_debug ip:", mhReq.Device.IP)

		// if localPosInfo.LocalPosID == "57858" {
		// 	fmt.Println("kbg_debug 57858 debug label:", item.Label, item.PlatformAppID, item.PlatformPosID)
		// }
		if item.Label == 3 {
			// if localPosInfo.LocalPosID == "57858" {
			// 	fmt.Println("kbg_debug 57858 debug config:", item.WhiteList, item.BlackList, item.MaxReqNum, item.MaxExpNum, item.MaxClkNum)
			// }
			// label == 3 sdk配置, 判定空did_md5生成规则为空, 直接不返回sdk配置
			if mhReq.Device.Os == "android" {
				if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
					continue
				}
			}

			// 判定地域
			if len(item.WhiteList) > 0 || len(item.BlackList) > 0 {
				if strings.Contains(mhReq.Device.IP, ":") {
				} else {
					// 地域白名单
					if len(item.WhiteList) > 0 {
						if strings.Contains(item.WhiteList, mhReq.Device.IPProvince) || strings.Contains(item.WhiteList, mhReq.Device.IPCountry) {
						} else {
							fmt.Println("sdk white failed")
							continue
						}
					}
					// 地域黑名单
					if len(item.BlackList) > 0 {
						if strings.Contains(item.BlackList, mhReq.Device.IPProvince) || strings.Contains(item.BlackList, mhReq.Device.IPCountry) {
							fmt.Println("sdk black failed")
							continue
						}
					}
				}
			}

			// 判定厂商白名单
			isManufacturerOK, _ := IsManufacturerCategoryOK(c, mhReq, &item)
			if isManufacturerOK {
			} else {
				fmt.Println("sdk category manufacturer failed :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
				continue
			}

			// 判定max req
			if item.MaxReqNum > 0 {
				maxReqRedisKey := "max_req_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxReqRedisValue, cacheErr := models.CacheFromRedis(c, maxReqRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxReqRedisValue) == 0 {
						fmt.Println("sdk max req return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max exp
			if item.MaxExpNum > 0 {
				maxExpRedisKey := "max_exp_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxExpRedisValue, cacheErr := models.CacheFromRedis(c, maxExpRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxExpRedisValue) == 0 {
						fmt.Println("sdk max exp return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max clk
			if item.MaxClkNum > 0 {
				maxClkRedisKey := "max_clk_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxClkRedisValue, cacheErr := models.CacheFromRedis(c, maxClkRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxClkRedisValue) == 0 {
						fmt.Println("sdk max clk return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定流水, dau
			tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
			if tmpSDKPlatformPos != nil {
				isPlatformPolicyOK := models.IsPlatformPolicyOK(c, mhReq, localPosInfo, tmpSDKPlatformPos.PlatformMediaID, tmpSDKPlatformPos.PlatformAppCorpID)
				// fmt.Println("sdk is policy ok: ", isPlatformPolicyOK)
				if isPlatformPolicyOK {
				} else {
					fmt.Println("sdk platform policy not ok , app id: " + localPosInfo.LocalAppID)
					continue
				}
			}

			// sdk限制
			if len(mhReq.SDKVersion) > 0 {
				// fmt.Println(sdkVersion)
				if len(item.WhiteVersion) > 0 {
					tmpSDKVersionArray := strings.Split(mhReq.SDKVersion, ".")
					tmpWhiteVersionArray := strings.Split(item.WhiteVersion, ".")

					if len(tmpSDKVersionArray) > 0 && len(tmpWhiteVersionArray) > 0 {
						if utils.IsNum(tmpSDKVersionArray[0]) && utils.IsNum(tmpWhiteVersionArray[0]) {
							tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[0])
							tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[0])
							if tmp1 < tmp2 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 1 && len(tmpWhiteVersionArray) > 1 {
						if utils.IsNum(tmpSDKVersionArray[1]) && utils.IsNum(tmpWhiteVersionArray[1]) {
							tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[1])
							tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[1])
							if tmp1 < tmp2 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 2 && len(tmpWhiteVersionArray) > 2 {
						if utils.IsNum(tmpSDKVersionArray[2]) && utils.IsNum(tmpWhiteVersionArray[2]) {
							tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[2])
							tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[2])
							if tmp1 < tmp2 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 3 && len(tmpWhiteVersionArray) > 3 {
						if utils.IsNum(tmpSDKVersionArray[3]) && utils.IsNum(tmpWhiteVersionArray[3]) {
							tmp1 := utils.ConvertStringToInt(tmpSDKVersionArray[3])
							tmp2 := utils.ConvertStringToInt(tmpWhiteVersionArray[3])
							if tmp1 < tmp2 {
								continue
							}
						}
					}
				}
				if len(item.BlackVersion) > 0 {
					if strings.Contains(item.BlackVersion, mhReq.SDKVersion) {
						continue
					}
				}
			}
		}
		respListArray = append(respListArray, respListItem)
	}

	// if localPosInfo.LocalPosID == "57858" {
	// 	tmpByte, _ := json.Marshal(respListArray)
	// 	fmt.Println("kbg_debug 57858 config:", mhReq.Device.IP, string(tmpByte))
	// }

	if len(respListArray) == 0 {
		return MhErrorRespMap(102006, "")
	}
	resp["data"] = respListArray

	// fmt.Println(resp)
	// fmt.Println("adx sdk end")
	return &resp
}

// GetSDKConfigV30FromAdx ...
func GetSDKConfigV30FromAdx(c context.Context, mhReq *models.MHReq) *map[string]interface{} {
	// fmt.Println("adx sdk v3.0 begin")
	// sdkVersion := c.Query("sdk_version")

	localAppID := mhReq.App.AppID
	localPosID := mhReq.Pos.ID

	localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println("local 0")
	// fmt.Println(localPosInfo)
	// fmt.Println("local 1")
	if localPosInfo == nil {
		return MhErrorRespMap(100007, "")
	}

	if localPosInfo.LocalPosIsActive != 1 {
		return MhSDKErrorRespMap(100135, "", localPosInfo)
	}

	bigdataUID := uuid.NewV4().String()

	category := models.GetSdkConfigFromCategoryPrice(c, strconv.Itoa(localPosID), localAppID)

	// fmt.Println(category)
	// fmt.Println(len(*category))
	if category == nil {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}
	if len(*category) == 0 {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}

	// sdk补参数
	paramSDKRepair(c, mhReq, localPosInfo)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("redis sdk did panic:", err)
			}
		}()

		models.RedisEntryDID(c, mhReq, localPosInfo, mhReq.SDKVersion)
	}()

	var platformPosInfo models.PlatformPosStu
	platformPosInfo.PlatformAppID = (*category)[0].PlatformAppID
	platformPosInfo.PlatformPosID = (*category)[0].PlatformPosID

	// impression_link array
	var respListItemImpArray []string
	// impression_link maplehaze
	mhImpParams := url.Values{}

	// for sdk
	maplehazeAdId := uuid.NewV4().String()
	platformPos := models.PlatformPosStu{PlatformAppID: "", PlatformPosID: "", PlatformAppType: "1"}
	bigdataParams := up_common.EncodeParams(mhReq, localPosInfo, &platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, 0)

	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	// click_link array
	var respListItemClkArray []string
	// click_link maplehaze
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", "__REQ_WIDTH__")
	mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	mhClkParams.Add("width", "__WIDTH__")
	mhClkParams.Add("height", "__HEIGHT__")
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("pappid", "__P_APP_ID__")
	mhClkParams.Add("pposid", "__P_POS_ID__")
	mhClkParams.Add("log", bigdataParams)

	respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

	// resp
	resp := map[string]interface{}{}
	resp["ret"] = 1
	resp["msg"] = ""

	// ecpm type
	resp["ecpm_type"] = localPosInfo.LocalPosEcpmType

	resp["impression_link"] = respListItemImpArray
	resp["click_link"] = respListItemClkArray

	// sdk ban keyword
	resp["ban_keyword"] = ""

	// 是否并发
	if IsSDKVersionMoreThanOrEqual3021(c, mhReq.SDKVersion) {
		resp["is_concurrent"] = 1
	} else {
		resp["is_concurrent"] = 0
	}
	// 超时
	resp["timeout"] = localPosInfo.LocalPosSDKTimeOut
	// 支持开屏点击区域
	// resp["splash_click_region"] = localPosInfo.LocalAppSplashClickRegion
	// 支持原生下载合规
	// resp["native_download_compliance"] = localPosInfo.LocalAppNativeDownloadCompliance
	// 限频
	if localPosInfo.LocalAppIsLimitFrequency == 0 {
		resp["limit_frequency"] = 0
	} else {
		resp["limit_frequency"] = 0
		if len(localPosInfo.LocalAppLimitFrequencyJson) > 0 {
			var limitFrequencyArray []models.SDKLimitFrequencyStu
			json.Unmarshal([]byte(localPosInfo.LocalAppLimitFrequencyJson), &limitFrequencyArray)
			currentHour := time.Now().Hour()
			if len(limitFrequencyArray) > 0 {
				for _, item := range limitFrequencyArray {
					if item.BeginHour <= currentHour &&
						currentHour <= item.EndHour {
						resp["limit_frequency"] = utils.ConvertStringToInt(item.Interval)
						break
					}
				}
			}
		}
	}

	// 支持clipboard
	if localPosInfo.LocalAppSupportExtraClipboard == 1 {
		resp["ec"] = localPosInfo.LocalAppSupportExtraClipboard
	}
	// 支持notification
	if localPosInfo.LocalAppSupportExtraNotification == 1 {
		resp["en"] = localPosInfo.LocalAppSupportExtraNotification
	}
	// 支持wakeup
	if localPosInfo.LocalAppSupportExtraWakeUp == 1 {
		resp["ew"] = localPosInfo.LocalAppSupportExtraWakeUp
	}

	// extra限频
	if localPosInfo.LocalAppIsExtraLimitFrequency == 0 {
		resp["extra_limit_frequency"] = 0
	} else {
		resp["extra_limit_frequency"] = utils.ConvertStringToInt(localPosInfo.LocalAppExtraLimitFrequency)
	}

	// 是否失败价格上报
	resp["is_report_price_failed"] = localPosInfo.LocalAppIsReportPriceLoss

	// sdk云控
	// 0(默认), 1(摇一摇), 2(滑动), 3(点滑摇)
	tmpInteractionType := 0
	tmpInteractionYaoSpeed := 0
	tmpInteractionYaoTriggerTime := 0
	if len(localPosInfo.LocalPosInteractionTypeListJson) > 0 {
		tmpSDKInteractionConfigArray := []models.SDKInteractionConfigStu{}
		json.Unmarshal([]byte(localPosInfo.LocalPosInteractionTypeListJson), &tmpSDKInteractionConfigArray)
		for _, tmpConfigItem := range tmpSDKInteractionConfigArray {
			isTimeOK := false
			isWhiteOK := true
			isBlackOK := true
			isWeightOK := false

			hh := time.Now().Hour()
			if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
				isTimeOK = true
			}
			if len(tmpConfigItem.WhiteList) > 0 {
				tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
				if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
				} else {
					isWhiteOK = false
				}
			}

			if len(tmpConfigItem.BlackList) > 0 {
				tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
				if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
					isBlackOK = false
				}
			}

			if rand.Intn(100) < tmpConfigItem.Weight {
				isWeightOK = true
			}
			// fmt.Println("IP:", mhReq.Device.IP)
			// fmt.Println("IPCity:", mhReq.Device.IPCity)
			// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
			// fmt.Println("jiahu is_time:", isTimeOK)
			// fmt.Println("jiahu is_white:", isWhiteOK)
			// fmt.Println("jiahu is_black:", isBlackOK)
			// fmt.Println("jiahu is_weight:", isWeightOK)

			if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
				tmpInteractionType = utils.ConvertStringToInt(tmpConfigItem.InteractionType)
				tmpInteractionYaoSpeed = utils.ConvertStringToInt(tmpConfigItem.YaoSpeed)
				tmpInteractionYaoTriggerTime = utils.ConvertStringToInt(tmpConfigItem.YaoTriggerTime)
			}
		}
	}
	resp["interaction_type"] = tmpInteractionType
	// 摇一摇加速度(单位:m/s²)
	resp["yao_speed"] = tmpInteractionYaoSpeed
	// 摇一摇触发时间(单位:ms)
	resp["yao_trigger_time"] = tmpInteractionYaoTriggerTime

	resp["yao_direction"] = localPosInfo.LocalPosYaoDirection
	// 是否开启撒花, 0(关), 1(开)
	tmpSaHuaConfig := 0
	if localPosInfo.LocalPosInteractionStyle == 1 && len(localPosInfo.LocalPosSahuaConfigListJson) > 0 {
		tmpSDKSaHuaConfigArray := []models.SDKSaHuaConfigStu{}
		json.Unmarshal([]byte(localPosInfo.LocalPosSahuaConfigListJson), &tmpSDKSaHuaConfigArray)
		for _, tmpConfigItem := range tmpSDKSaHuaConfigArray {
			isTimeOK := false
			isWhiteOK := true
			isBlackOK := true
			isWeightOK := false

			hh := time.Now().Hour()
			if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
				isTimeOK = true
			}
			if len(tmpConfigItem.WhiteList) > 0 {
				tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
				if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
				} else {
					isWhiteOK = false
				}
			}

			if len(tmpConfigItem.BlackList) > 0 {
				tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
				if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
					isBlackOK = false
				}
			}

			if rand.Intn(100) < tmpConfigItem.Weight {
				isWeightOK = true
			}
			// fmt.Println("IP:", mhReq.Device.IP)
			// fmt.Println("IPCity:", mhReq.Device.IPCity)
			// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
			// fmt.Println("sahua is_time:", isTimeOK)
			// fmt.Println("sahua is_white:", isWhiteOK)
			// fmt.Println("sahua is_black:", isBlackOK)
			// fmt.Println("sahua is_weight:", isWeightOK)
			if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
				tmpSaHuaConfig = 1
			}
		}
	}
	// 是否开启撒花, 0(关), 1(开)
	resp["is_flower_config"] = tmpSaHuaConfig
	// 撒花图片触发时间(单位:ms)
	resp["flower_image_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaImageTriggerTime)
	// 撒花视频触发时间(单位:ms)
	resp["flower_video_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaVideoTriggerTime)
	// 撒花图片播放时间(单位:ms)
	resp["flower_image_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaImageDurationTime)
	// 撒花视频播放时间(单位:ms)
	resp["flower_video_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaVideoDurationTime)
	// SDK摇一摇组件 (关闭: 0; 枫岚组件:1; 枫岚动画+三方组件: 2)
	// 可支持SDK广告源(优量汇:1; 快手:2; 京东:3; Tanx:4; 穿山甲:5; 百度:6; 爱奇艺:7)
	// jd 2.4.0, ks 3.3.36
	resp["extsdk_interaction_type"] = localPosInfo.LocalPosIsSupportSDKInteraction
	//  SDK摇一摇组件, 在小于(<3.0.23)下发关闭选项
	if IsSDKVersionMoreThanOrEqual3023(c, mhReq.SDKVersion) {
	} else {
		localPosInfo.LocalPosIsSupportSDKInteraction = 0
	}

	if localPosInfo.LocalPosIsSupportSDKInteraction == 0 {
		resp["extsdk_interaction_support"] = []string{}
	} else {
		var itemMapArray []map[string]interface{}
		for _, item := range strings.Split(localPosInfo.LocalPosSDKInteractionSupportTypes, ",") {
			// 枫岚组件:1, extsdk_interaction_support, 不返回优量汇: 1
			if localPosInfo.LocalPosIsSupportSDKInteraction == 1 {
				if item == "1" {
					continue
				}
			}
			// 枫岚动画+三方组件: 2, extsdk_interaction_support, 只返回优量汇, 快手, 京东
			if localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				if item == "1" || item == "2" || item == "3" {
				} else {
					continue
				}
			}
			itemMap := map[string]interface{}{}
			itemMap["type"] = utils.ConvertStringToInt(item)
			if item == "3" && localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				itemMap["min_version"] = "2.4.0"
				itemMap["max_version"] = "10"
			} else if item == "2" && localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				itemMap["min_version"] = "3.3.36"
				itemMap["max_version"] = "10"
			} else {
				itemMap["min_version"] = "1"
				itemMap["max_version"] = "10"
			}

			itemMapArray = append(itemMapArray, itemMap)
		}
		resp["extsdk_interaction_support"] = itemMapArray
	}

	// 是否下载二次确认弹窗, 0(关), 1(开)
	resp["is_download_dialog"] = localPosInfo.LocalPosIsDownloadDialog
	// 是否移动网络自动播放, 0(关), 1(开)
	resp["is_auto_play_mobile_network"] = localPosInfo.LocalPosIsAutoPlayMobileNetwork
	// 是否点击区域下载合规弹窗
	resp["is_clickview_download_compliance"] = localPosInfo.LocalPosIsClickViewDownloadCompliance
	// 是否非点击区域下载合规弹窗
	resp["is_adview_download_compliance"] = localPosInfo.LocalPosIsAdViewDownloadCompliance
	// 是否激励视频退出弹窗
	if localPosInfo.LocalPosIsRewardVideoExitConfirm == 1 {
		resp["reward_video_exit_confirm"] = 0
	} else {
		resp["reward_video_exit_confirm"] = 1
	}
	// 是否摇一摇后台关闭传感器
	if localPosInfo.LocalPosIsYaoBgDisableSensor == 0 {
		resp["yao_disable_sensor"] = 0
	} else {
		resp["yao_disable_sensor"] = 1
	}
	// 替换SDK广告来源
	if localPosInfo.LocalPosIsRadst == 0 {
	} else {
		resp["radst"] = utils.ConvertStringToInt(localPosInfo.LocalPosRadst)
	}

	var respListArray []map[string]interface{}

	for _, item := range *category {
		if item.IsActive == 1 {
		} else {
			continue
		}
		respListItem := map[string]interface{}{}
		respListItem["platform_pos_id"] = item.PlatformPosID
		respListItem["platform_pos_type"] = item.PlatformPosType
		respListItem["platform_pos_sub_type"] = item.PlatformPosMaterialType
		respListItem["platform_app_id"] = item.PlatformAppID
		respListItem["platform_media_id"] = item.PlatformMediaID
		respListItem["floor_price"] = 0               // 此字段废弃, floor_price版本设置0
		respListItem["final_price"] = item.FinalPrice // 此字段废弃, final_price和新字段platform_pos_fnp保持一致

		// 反作弊
		if item.Label == 3 {
			_, anticheatSDKCacheError := db.GlbBigCacheMinute.Get(fmt.Sprintf(config.AnticheatConfigSDK, item.PlatformMediaID))
			if anticheatSDKCacheError != nil {
			} else {
				// redis
				tmpRedisKey := fmt.Sprintf(rediskeys.ADX_SSP_ANTICHEAT_KEY, mhReq.Device.DIDMd5)
				_, tmpRedisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()
				if tmpRedisErr != nil {
				} else {
					continue
				}
			}
		}

		tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
		if tmpSDKPlatformPos != nil {
			respListItem["is_replace_xy"] = tmpSDKPlatformPos.PlatformPosIsReplaceXYDefault
			respListItem["replace_xy_default"] = tmpSDKPlatformPos.PlatformPosReplaceXYDefault
		}

		if item.Label == 3 {
			if mhReq.SDKVersion == "3.1.5" || mhReq.SDKVersion == "3.2.9" || mhReq.SDKVersion == "3.2.10" || mhReq.SDKVersion == "3.2.11" || mhReq.SDKVersion == "3.2.12" {
				randNum := rand.Intn(100)
				if randNum < localPosInfo.LocalAppSDKFilterSDKWeight {
					continue
				}
			}
		}

		// black did 过滤jd sdk配置
		// if item.Label == 3 && item.PlatformMediaID == "19" {
		// 	isInBlack := false
		// 	for _, tmpBlackItem := range utilities.BlackDIDList {
		// 		// fmt.Println("debug_black_did:", tmpBlackItem)
		// 		if len(mhReq.Device.Imei) > 0 && tmpBlackItem == mhReq.Device.Imei {
		// 			isInBlack = true
		// 			break
		// 		}
		// 		if len(mhReq.Device.Oaid) > 0 && tmpBlackItem == mhReq.Device.Oaid {
		// 			isInBlack = true
		// 			break
		// 		}
		// 	}
		// 	if isInBlack {
		// 		continue
		// 	}
		// }

		if item.Label == 3 {
			respListItem["mode"] = "1"
		} else {
			respListItem["mode"] = "0"
		}
		if item.PlatformMediaID == "43" {
			if tmpSDKPlatformPos != nil {
				if len(tmpSDKPlatformPos.PlatformAppKey) > 0 {
					respListItem["platform_app_key"] = tmpSDKPlatformPos.PlatformAppKey
				}
				if len(tmpSDKPlatformPos.PlatformAppSecret) > 0 {
					respListItem["platform_app_secret"] = tmpSDKPlatformPos.PlatformAppSecret
				}
			}
		}

		// fmt.Println("kbg_debug p_app_id:", item.PlatformAppID)
		// fmt.Println("kbg_debug p_pos_id:", item.PlatformPosID)
		// fmt.Println("kbg_debug floor price:", item.FloorPrice)
		// fmt.Println("kbg_debug final price:", item.FinalPrice)
		// fmt.Println("kbg_debug white:", item.WhiteList)
		// fmt.Println("kbg_debug black:", item.BlackList)
		// fmt.Println("kbg_debug ip:", mhReq.Device.IP)

		// if localPosInfo.LocalPosID == "57858" {
		// 	fmt.Println("kbg_debug 57858 debug label:", item.Label, item.PlatformAppID, item.PlatformPosID)
		// }
		if item.Label == 3 {
			// if localPosInfo.LocalPosID == "57858" {
			// 	fmt.Println("kbg_debug 57858 debug config:", item.WhiteList, item.BlackList, item.MaxReqNum, item.MaxExpNum, item.MaxClkNum)
			// }
			// label == 3 sdk配置, 判定空did_md5生成规则为空, 直接不返回sdk配置
			if mhReq.Device.Os == "android" {
				if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
					// fmt.Println("kbg_debug_sdk_config sdk_verison:" + sdkVersion + ", os:" + mhReq.Device.Os + ",osv:" + mhReq.Device.OsVersion + ",imei:" + mhReq.Device.Imei + ",android_id:" + mhReq.Device.AndroidID + ",oaid:" + mhReq.Device.Oaid)
					continue
				}
			}

			// 判定地域
			if len(item.WhiteList) > 0 || len(item.BlackList) > 0 {
				if strings.Contains(mhReq.Device.IP, ":") {
				} else {
					// 地域白名单
					if len(item.WhiteList) > 0 {
						if strings.Contains(item.WhiteList, mhReq.Device.IPProvince) || strings.Contains(item.WhiteList, mhReq.Device.IPCountry) {
						} else {
							fmt.Println("sdk white failed")
							continue
						}
					}
					// 地域黑名单
					if len(item.BlackList) > 0 {
						if strings.Contains(item.BlackList, mhReq.Device.IPProvince) || strings.Contains(item.BlackList, mhReq.Device.IPCountry) {
							fmt.Println("sdk black failed")
							continue
						}
					}
				}
			}

			// 判定厂商白名单
			isManufacturerOK, _ := IsManufacturerCategoryOK(c, mhReq, &item)
			if isManufacturerOK {
			} else {
				fmt.Println("sdk category manufacturer failed :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
				continue
			}

			// 判定max req
			if item.MaxReqNum > 0 {
				maxReqRedisKey := "max_req_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxReqRedisValue, cacheErr := models.CacheFromRedis(c, maxReqRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxReqRedisValue) == 0 {
						fmt.Println("sdk max req return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max exp
			if item.MaxExpNum > 0 {
				maxExpRedisKey := "max_exp_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxExpRedisValue, cacheErr := models.CacheFromRedis(c, maxExpRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxExpRedisValue) == 0 {
						fmt.Println("sdk max exp return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max clk
			if item.MaxClkNum > 0 {
				maxClkRedisKey := "max_clk_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxClkRedisValue, cacheErr := models.CacheFromRedis(c, maxClkRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxClkRedisValue) == 0 {
						fmt.Println("sdk max clk return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// sdk人群包
			if IsCategoryCrowdPackageOK(c, mhReq, localPosInfo, &item, nil) {
			} else {
				continue
			}

			// 判定流水, dau
			tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
			if tmpSDKPlatformPos != nil {
				isPlatformPolicyOK := models.IsPlatformPolicyOK(c, mhReq, localPosInfo, tmpSDKPlatformPos.PlatformMediaID, tmpSDKPlatformPos.PlatformAppCorpID)
				// fmt.Println("sdk is policy ok: ", isPlatformPolicyOK)
				if isPlatformPolicyOK {
				} else {
					fmt.Println("sdk platform policy not ok , app id: " + localPosInfo.LocalAppID)
					continue
				}

				// sdk人群包
				if IsCrowdPackageOK(c, mhReq, localPosInfo, tmpSDKPlatformPos, nil) {
				} else {
					continue
				}
			}

			// sdk限制
			if len(mhReq.SDKVersion) > 0 {
				// fmt.Println(sdkVersion)
				if len(item.WhiteVersion) > 0 {
					tmpSDKVersionArray := strings.Split(mhReq.SDKVersion, ".")
					tmpWhiteVersionArray := strings.Split(item.WhiteVersion, ".")

					tmpSDKVersion0 := 0
					tmpWhiteVersion0 := 0

					tmpSDKVersion1 := 0
					tmpWhiteVersion1 := 0

					tmpSDKVersion2 := 0
					tmpWhiteVersion2 := 0

					tmpSDKVersion3 := 0
					tmpWhiteVersion3 := 0

					if len(tmpSDKVersionArray) > 0 && len(tmpWhiteVersionArray) > 0 {
						if utils.IsNum(tmpSDKVersionArray[0]) && utils.IsNum(tmpWhiteVersionArray[0]) {
							tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
							tmpWhiteVersion0 = utils.ConvertStringToInt(tmpWhiteVersionArray[0])
							if tmpSDKVersion0 < tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 1 && len(tmpWhiteVersionArray) > 1 {
						if utils.IsNum(tmpSDKVersionArray[1]) && utils.IsNum(tmpWhiteVersionArray[1]) {
							tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
							tmpWhiteVersion1 = utils.ConvertStringToInt(tmpWhiteVersionArray[1])
							if tmpSDKVersion1 < tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 2 && len(tmpWhiteVersionArray) > 2 {
						if utils.IsNum(tmpSDKVersionArray[2]) && utils.IsNum(tmpWhiteVersionArray[2]) {
							tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
							tmpWhiteVersion2 = utils.ConvertStringToInt(tmpWhiteVersionArray[2])
							if tmpSDKVersion2 < tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 3 && len(tmpWhiteVersionArray) > 3 {
						if utils.IsNum(tmpSDKVersionArray[3]) && utils.IsNum(tmpWhiteVersionArray[3]) {
							tmpSDKVersion3 = utils.ConvertStringToInt(tmpSDKVersionArray[3])
							tmpWhiteVersion3 = utils.ConvertStringToInt(tmpWhiteVersionArray[3])
							if tmpSDKVersion3 < tmpWhiteVersion3 && tmpSDKVersion2 == tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
				}
				if len(item.BlackVersion) > 0 {
					if strings.Contains(item.BlackVersion, mhReq.SDKVersion) {
						continue
					}
				}
			}
		}
		respListArray = append(respListArray, respListItem)
	}

	// if localPosInfo.LocalPosID == "57858" {
	// 	tmpByte, _ := json.Marshal(respListArray)
	// 	fmt.Println("kbg_debug 57858 config:", mhReq.Device.IP, string(tmpByte))
	// }

	if len(respListArray) == 0 {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}
	resp["data"] = respListArray

	// fmt.Println(resp)
	// fmt.Println("adx sdk end")
	return &resp
}

// GetSDKConfigV33FromAdx ...
func GetSDKConfigV33FromAdx(c context.Context, mhReq *models.MHReq) *map[string]interface{} {
	// fmt.Println("sdk config v3.3 begin")
	// sdkVersion := c.Query("sdk_version")

	if mhReq.Device.Os == "android" && mhReq.Device.Oaid == "137b9e66-e53f-47b2-b5da-09e2640b3b45" {
		return MhErrorRespMap(102006, "")
	}

	localAppID := mhReq.App.AppID
	localPosID := mhReq.Pos.ID

	localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
	// fmt.Println("local 0")
	// fmt.Println(localPosInfo)
	// fmt.Println("local 1")
	if localPosInfo == nil {
		return MhErrorRespMap(100007, "")
	}

	if localPosInfo.LocalPosIsActive != 1 {
		return MhSDKErrorRespMap(100135, "", localPosInfo)
	}

	bigdataUID := uuid.NewV4().String()

	category := models.GetSdkConfigFromCategoryPrice(c, strconv.Itoa(localPosID), localAppID)

	// fmt.Println(category)
	// fmt.Println(len(*category))
	if category == nil {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}
	if len(*category) == 0 {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}

	// sdk补参数
	paramSDKRepair(c, mhReq, localPosInfo)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("redis sdk did panic:", err)
			}
		}()

		models.RedisEntryDID(c, mhReq, localPosInfo, mhReq.SDKVersion)
		randomNumber := rand.Intn(10000)
		if randomNumber == 0 {
			models.GoBigDataHoloDeviceDpi(mhReq)
			models.GoBigDataHoloDeviceDictModel(mhReq)
		}
	}()

	// var platformPosInfo models.PlatformPosStu
	// platformPosInfo.PlatformAppID = (*category)[0].PlatformAppID
	// platformPosInfo.PlatformPosID = (*category)[0].PlatformPosID

	// for sdk 多条曝光,点击,win_notice_url,loss_notice_url
	var respReportArray []map[string]interface{}

	for i := 0; i < mhReq.Pos.AdCount; i++ {
		maplehazeAdId := uuid.NewV4().String()
		respReportItem := map[string]interface{}{}
		// respReportItem["adid"] = maplehazeAdId

		platformPos := models.PlatformPosStu{PlatformAppID: "", PlatformPosID: "", PlatformAppType: "1"}
		bigdataParams := up_common.EncodeParams(mhReq, localPosInfo, &platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, 0)

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		mhImpParams.Add("pappid", "__P_APP_ID__")
		mhImpParams.Add("pposid", "__P_POS_ID__")
		mhImpParams.Add("sdklogep", "__DPPLOGEP__")
		// mhImpParams.Add("ppflp", "__PPFLP__") // floor_price
		mhImpParams.Add("ppfnp", "__PPFNP__") // final_price
		if localPosInfo.LocalPosEcpmType == 0 {
		} else {
			mhImpParams.Add("sdkprice", "__AUCTION_PRICE__")
		}

		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", "__REQ_WIDTH__")
		mhClkParams.Add("req_height", "__REQ_HEIGHT__")
		mhClkParams.Add("width", "__WIDTH__")
		mhClkParams.Add("height", "__HEIGHT__")
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("pappid", "__P_APP_ID__")
		mhClkParams.Add("pposid", "__P_POS_ID__")
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respReportItem["impression_link"] = respListItemImpArray
		respReportItem["click_link"] = respListItemClkArray

		// 价格上报log
		bigdataPriceParams := url.Values{}
		bigdataPriceParams.Add("uid", bigdataUID)
		bigdataPriceParams.Add("adid", maplehazeAdId)
		bigdataPriceParams.Add("app_id", localPosInfo.LocalAppID)
		bigdataPriceParams.Add("pos_id", localPosInfo.LocalPosID)
		encodeStr, _ := utils.EncodeString([]byte(bigdataPriceParams.Encode()))

		if localPosInfo.LocalPosEcpmType == 0 {
		} else {
			// 价格上报win
			mhWinParams := url.Values{}
			mhWinParams.Add("pappid", "__P_APP_ID__")
			mhWinParams.Add("pposid", "__P_POS_ID__")
			mhWinParams.Add("sdklogep", "__PPLOGEP__")
			mhWinParams.Add("sdkprice", "__AUCTION_PRICE__")
			mhWinParams.Add("log", encodeStr)
			respReportItem["win_notice_url"] = "https://ssp.maplehaze.cn/sdk/win" + "?" + mhWinParams.Encode()

			// 价格上报loss
			mhLossParams := url.Values{}
			mhLossParams.Add("pappid", "__P_APP_ID__")
			mhLossParams.Add("pposid", "__P_POS_ID__")
			mhLossParams.Add("sdklogep", "__PPLOGEP__")
			mhLossParams.Add("sdkprice", "__AUCTION_PRICE__")
			mhLossParams.Add("reason", "__AUCTION_LOSS__")
			mhLossParams.Add("seatid", "__AUCTION_SEAT_ID__")
			mhLossParams.Add("log", encodeStr)
			respReportItem["loss_notice_url"] = "https://ssp.maplehaze.cn/sdk/loss" + "?" + mhLossParams.Encode()
		}

		respReportArray = append(respReportArray, respReportItem)
	}

	// resp
	resp := map[string]interface{}{}
	resp["ret"] = 1
	resp["msg"] = ""
	resp["report_list"] = respReportArray

	// width height
	resp["pos_width"] = localPosInfo.LocalPosWidth
	resp["pos_height"] = localPosInfo.LocalPosHeight

	// req id
	resp["reqid"] = bigdataUID

	// ecpm type
	resp["ecpm_type"] = localPosInfo.LocalPosEcpmType
	// ecpm
	if localPosInfo.LocalPosEcpmType == 2 {
		tmpEcpm := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(localPosInfo.LocalPosEcpm)), []byte(config.EncryptKEY))
		resp["pos_log_ep"] = base64.StdEncoding.EncodeToString(tmpEcpm)
	}

	// sdk ban keyword
	resp["ban_keyword"] = ""

	// 是否并发
	if IsSDKVersionMoreThanOrEqual3021(c, mhReq.SDKVersion) {
		resp["is_concurrent"] = 1
	} else {
		resp["is_concurrent"] = 0
	}
	// 超时
	resp["timeout"] = localPosInfo.LocalPosSDKTimeOut
	// 支持开屏点击区域
	// resp["splash_click_region"] = localPosInfo.LocalAppSplashClickRegion
	// 支持原生下载合规
	// resp["native_download_compliance"] = localPosInfo.LocalAppNativeDownloadCompliance
	// 限频
	if localPosInfo.LocalAppIsLimitFrequency == 0 {
		resp["limit_frequency"] = 0
	} else {
		resp["limit_frequency"] = 0
		if len(localPosInfo.LocalAppLimitFrequencyJson) > 0 {
			var limitFrequencyArray []models.SDKLimitFrequencyStu
			json.Unmarshal([]byte(localPosInfo.LocalAppLimitFrequencyJson), &limitFrequencyArray)
			currentHour := time.Now().Hour()
			if len(limitFrequencyArray) > 0 {
				for _, item := range limitFrequencyArray {
					if item.BeginHour <= currentHour &&
						currentHour <= item.EndHour {
						resp["limit_frequency"] = utils.ConvertStringToInt(item.Interval)
						break
					}
				}
			}
		}
	}

	// 支持clipboard
	if localPosInfo.LocalAppSupportExtraClipboard == 1 {
		resp["ec"] = localPosInfo.LocalAppSupportExtraClipboard
	}
	if localPosInfo.LocalAppSupportExtraClipboard == 0 {
		resp["ec_event"] = 0
	} else {
		resp["ec_event"] = localPosInfo.LocalAppExtraClipboardType
	}
	// 支持notification
	if localPosInfo.LocalAppSupportExtraNotification == 1 {
		resp["en"] = localPosInfo.LocalAppSupportExtraNotification
	}
	// 支持wakeup
	if localPosInfo.LocalAppSupportExtraWakeUp == 1 {
		resp["ew"] = localPosInfo.LocalAppSupportExtraWakeUp
	}

	// extra限频
	if localPosInfo.LocalAppIsExtraLimitFrequency == 0 {
		resp["extra_limit_frequency"] = 0
	} else {
		resp["extra_limit_frequency"] = utils.ConvertStringToInt(localPosInfo.LocalAppExtraLimitFrequency)
	}

	// 是否失败价格上报
	resp["is_report_price_failed"] = localPosInfo.LocalAppIsReportPriceLoss

	// sdk云控
	if mhReq.Device.Os == "ios" {
		// 0(默认), 1(摇一摇), 2(滑动), 3(点滑摇)
		tmpInteractionType := 0
		tmpInteractionYaoSpeed := 0
		tmpInteractionYaoTriggerTime := 0
		if len(localPosInfo.LocalPosNewInteractionTypeListJson) > 0 {
			tmpSDKInteractionConfigArray := []models.SDKNewInteractionConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosNewInteractionTypeListJson), &tmpSDKInteractionConfigArray)
			for _, tmpConfigItem := range tmpSDKInteractionConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("jiahu is_time:", isTimeOK)
				// fmt.Println("jiahu is_white:", isWhiteOK)
				// fmt.Println("jiahu is_black:", isBlackOK)
				// fmt.Println("jiahu is_weight:", isWeightOK)

				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					tmpInteractionType = utils.ConvertStringToInt(tmpConfigItem.InteractionType)
					tmpInteractionYaoSpeed = utils.ConvertStringToInt(tmpConfigItem.YaoSpeed)
					tmpInteractionYaoTriggerTime = utils.ConvertStringToInt(tmpConfigItem.YaoTriggerTime)
				}
			}
		}
		resp["interaction_type"] = tmpInteractionType
		// 摇一摇加速度(单位:m/s²)
		resp["yao_speed"] = tmpInteractionYaoSpeed
		// 摇一摇触发时间(单位:ms)
		resp["yao_trigger_time"] = tmpInteractionYaoTriggerTime

		resp["yao_direction"] = localPosInfo.LocalPosNewYaoDirection

		// 滑动触发方向, 0 向上(默认), 1 所有
		resp["slide_direction"] = localPosInfo.LocalPosNewSlideDirection

		// 交互样式配置, 0 无 1 撒花 2 宝箱
		// 是否开启撒花
		if localPosInfo.LocalPosNewInteractionStyle == 1 && len(localPosInfo.LocalPosNewSahuaConfigListJson) > 0 {
			tmpSDKSaHuaConfigArray := []models.SDKSaHuaConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosNewSahuaConfigListJson), &tmpSDKSaHuaConfigArray)
			for _, tmpConfigItem := range tmpSDKSaHuaConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("sahua is_time:", isTimeOK)
				// fmt.Println("sahua is_white:", isWhiteOK)
				// fmt.Println("sahua is_black:", isBlackOK)
				// fmt.Println("sahua is_weight:", isWeightOK)
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					resp["interaction_style"] = 1
					resp["is_flower_config"] = 1
					// 撒花图片触发时间(单位:ms)
					resp["flower_image_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosNewSahuaImageTriggerTime)
					// 撒花视频触发时间(单位:ms)
					resp["flower_video_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosNewSahuaVideoTriggerTime)
					// 撒花图片播放时间(单位:ms)
					resp["flower_image_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosNewSahuaImageDurationTime)
					// 撒花视频播放时间(单位:ms)
					resp["flower_video_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosNewSahuaVideoDurationTime)
					break
				}
			}
		}

		// 是否开启宝箱
		if localPosInfo.LocalPosNewInteractionStyle == 2 && len(localPosInfo.LocalPosNewTreasurechestConfigListJson) > 0 {
			tmpSDKTreasureConfigArray := []models.SDKTreasureConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosNewTreasurechestConfigListJson), &tmpSDKTreasureConfigArray)
			for _, tmpConfigItem := range tmpSDKTreasureConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					resp["interaction_style"] = 2
					resp["treasure_trigger_time"] = utils.ConvertStringToInt(tmpConfigItem.TriggerTime)
					resp["treasure_is_clk_allscreen"] = tmpConfigItem.IsClkAllscreen
					resp["treasure_is_shake"] = tmpConfigItem.IsShake
					if rand.Intn(100) < tmpConfigItem.TreasureMagicWeight {
						resp["treasure_is_magic"] = 1
					} else {
						resp["treasure_is_magic"] = 0
					}

					// hack 2025.06.06
					// treasure_is_magic = 0: android version < *******, ios version < 1.3.4
					if mhReq.Device.Os == "android" {
						if IsSDKVersionMoreThanOrEqualFourVersionCode(c, mhReq.SDKVersion, 3, 3, 9, 6) {
						} else {
							resp["treasure_is_magic"] = 0
						}
					} else if mhReq.Device.Os == "ios" {
						if IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 1, 3, 4) {
						} else {
							resp["treasure_is_magic"] = 0
						}
					}
					break
				}
			}
		}

		// magic配置
		// 是否开启撒花
		if localPosInfo.LocalPosNewIsMagic == 1 && len(localPosInfo.LocalPosNewMagicConfigListJson) > 0 {
			tmpSDKMagicConfigArray := []models.SDKMagicConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosNewMagicConfigListJson), &tmpSDKMagicConfigArray)
			for _, tmpConfigItem := range tmpSDKMagicConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("sahua is_time:", isTimeOK)
				// fmt.Println("sahua is_white:", isWhiteOK)
				// fmt.Println("sahua is_black:", isBlackOK)
				// fmt.Println("sahua is_weight:", isWeightOK)
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					// magic开关
					resp["is_magic"] = 1
					// magic范围
					resp["magic_type"] = localPosInfo.LocalPosNewMagicType
					// magic区域, 0 关闭(默认), 1 全屏
					resp["magic_region"] = localPosInfo.LocalPosNewMagicRegion
					break
				}
			}
		}

		// 激励视频样式, 0 旧版 1 新版
		resp["reward_video_type"] = localPosInfo.LocalPosNewRewardVideoType
		// 激励视频点击手势引导
		resp["reward_video_clk_guide"] = localPosInfo.LocalPosNewRewardVideoClkGuide

		// 是否下载二次确认弹窗, 0(关), 1(开)
		resp["is_download_dialog"] = localPosInfo.LocalPosNewIsDownloadDialog
		// 是否移动网络自动播放, 0(关), 1(开)
		resp["is_auto_play_mobile_network"] = localPosInfo.LocalPosNewIsAutoPlayMobileNetwork
		// 是否点击区域下载合规弹窗
		resp["is_clickview_download_compliance"] = localPosInfo.LocalPosNewIsClickViewDownloadCompliance
		// 是否非点击区域下载合规弹窗
		resp["is_adview_download_compliance"] = localPosInfo.LocalPosNewIsAdViewDownloadCompliance
		// 是否激励视频退出弹窗
		if localPosInfo.LocalPosNewIsRewardVideoExitConfirm == 1 {
			resp["reward_video_exit_confirm"] = 0
		} else {
			resp["reward_video_exit_confirm"] = 1
		}
		// 是否摇一摇后台关闭传感器
		if localPosInfo.LocalPosNewIsYaoBgDisableSensor == 0 {
			resp["yao_disable_sensor"] = 0
		} else {
			resp["yao_disable_sensor"] = 1
		}
	} else {
		// 0(默认), 1(摇一摇), 2(滑动), 3(点滑摇)
		tmpInteractionType := 0
		tmpInteractionYaoSpeed := 0
		tmpInteractionYaoTriggerTime := 0
		if len(localPosInfo.LocalPosInteractionTypeListJson) > 0 {
			tmpSDKInteractionConfigArray := []models.SDKInteractionConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosInteractionTypeListJson), &tmpSDKInteractionConfigArray)
			for _, tmpConfigItem := range tmpSDKInteractionConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("jiahu is_time:", isTimeOK)
				// fmt.Println("jiahu is_white:", isWhiteOK)
				// fmt.Println("jiahu is_black:", isBlackOK)
				// fmt.Println("jiahu is_weight:", isWeightOK)

				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					tmpInteractionType = utils.ConvertStringToInt(tmpConfigItem.InteractionType)
					tmpInteractionYaoSpeed = utils.ConvertStringToInt(tmpConfigItem.YaoSpeed)
					tmpInteractionYaoTriggerTime = utils.ConvertStringToInt(tmpConfigItem.YaoTriggerTime)
				}
			}
		}
		resp["interaction_type"] = tmpInteractionType
		// 摇一摇加速度(单位:m/s²)
		resp["yao_speed"] = tmpInteractionYaoSpeed
		// 摇一摇触发时间(单位:ms)
		resp["yao_trigger_time"] = tmpInteractionYaoTriggerTime

		resp["yao_direction"] = localPosInfo.LocalPosYaoDirection

		// 滑动触发方向, 0 向上(默认), 1 所有
		resp["slide_direction"] = localPosInfo.LocalPosSlideDirection

		// 交互样式配置, 0 无 1 撒花 2 宝箱
		// 是否开启撒花
		if localPosInfo.LocalPosInteractionStyle == 1 && len(localPosInfo.LocalPosSahuaConfigListJson) > 0 {
			tmpSDKSaHuaConfigArray := []models.SDKSaHuaConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosSahuaConfigListJson), &tmpSDKSaHuaConfigArray)
			for _, tmpConfigItem := range tmpSDKSaHuaConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("sahua is_time:", isTimeOK)
				// fmt.Println("sahua is_white:", isWhiteOK)
				// fmt.Println("sahua is_black:", isBlackOK)
				// fmt.Println("sahua is_weight:", isWeightOK)
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					resp["interaction_style"] = 1
					resp["is_flower_config"] = 1
					// 撒花图片触发时间(单位:ms)
					resp["flower_image_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaImageTriggerTime)
					// 撒花视频触发时间(单位:ms)
					resp["flower_video_trigger_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaVideoTriggerTime)
					// 撒花图片播放时间(单位:ms)
					resp["flower_image_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaImageDurationTime)
					// 撒花视频播放时间(单位:ms)
					resp["flower_video_duration_time"] = utils.ConvertStringToInt(localPosInfo.LocalPosSahuaVideoDurationTime)
					break
				}
			}
		}

		// 是否开启宝箱
		if localPosInfo.LocalPosInteractionStyle == 2 && len(localPosInfo.LocalPosTreasurechestConfigListJson) > 0 {
			tmpSDKTreasureConfigArray := []models.SDKTreasureConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosTreasurechestConfigListJson), &tmpSDKTreasureConfigArray)
			for _, tmpConfigItem := range tmpSDKTreasureConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					resp["interaction_style"] = 2
					resp["treasure_trigger_time"] = utils.ConvertStringToInt(tmpConfigItem.TriggerTime)
					resp["treasure_is_clk_allscreen"] = tmpConfigItem.IsClkAllscreen
					resp["treasure_is_shake"] = tmpConfigItem.IsShake
					if rand.Intn(100) < tmpConfigItem.TreasureMagicWeight {
						resp["treasure_is_magic"] = 1
					} else {
						resp["treasure_is_magic"] = 0
					}

					// hack 2025.06.06
					// treasure_is_magic = 0: android version < *******, ios version < 1.3.4
					if mhReq.Device.Os == "android" {
						if IsSDKVersionMoreThanOrEqualFourVersionCode(c, mhReq.SDKVersion, 3, 3, 9, 6) {
						} else {
							resp["treasure_is_magic"] = 0
						}
					} else if mhReq.Device.Os == "ios" {
						if IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 1, 3, 4) {
						} else {
							resp["treasure_is_magic"] = 0
						}
					}
					break
				}
			}
		}

		// magic配置
		// 是否开启撒花
		if localPosInfo.LocalPosIsMagic == 1 && len(localPosInfo.LocalPosMagicConfigListJson) > 0 {
			tmpSDKMagicConfigArray := []models.SDKMagicConfigStu{}
			json.Unmarshal([]byte(localPosInfo.LocalPosMagicConfigListJson), &tmpSDKMagicConfigArray)
			for _, tmpConfigItem := range tmpSDKMagicConfigArray {
				isTimeOK := false
				isWhiteOK := true
				isBlackOK := true
				isWeightOK := false

				hh := time.Now().Hour()
				if utils.ConvertStringToInt(tmpConfigItem.From) <= hh && hh <= utils.ConvertStringToInt(tmpConfigItem.To) {
					isTimeOK = true
				}
				if len(tmpConfigItem.WhiteList) > 0 {
					tmpConfigWhiteListStr := strings.Join(tmpConfigItem.WhiteList, ",")
					if strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigWhiteListStr, mhReq.Device.IPCity) {
					} else {
						isWhiteOK = false
					}
				}

				if len(tmpConfigItem.BlackList) > 0 {
					tmpConfigBlackListStr := strings.Join(tmpConfigItem.BlackList, ",")
					if strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPProvince) || strings.Contains(tmpConfigBlackListStr, mhReq.Device.IPCity) {
						isBlackOK = false
					}
				}

				if rand.Intn(100) < tmpConfigItem.Weight {
					isWeightOK = true
				}
				// fmt.Println("IP:", mhReq.Device.IP)
				// fmt.Println("IPCity:", mhReq.Device.IPCity)
				// fmt.Println("IPProvince:", mhReq.Device.IPProvince)
				// fmt.Println("sahua is_time:", isTimeOK)
				// fmt.Println("sahua is_white:", isWhiteOK)
				// fmt.Println("sahua is_black:", isBlackOK)
				// fmt.Println("sahua is_weight:", isWeightOK)
				if isTimeOK && isWhiteOK && isBlackOK && isWeightOK {
					// magic开关
					resp["is_magic"] = 1
					// magic范围
					resp["magic_type"] = localPosInfo.LocalPosMagicType
					// magic区域, 0 关闭(默认), 1 全屏
					resp["magic_region"] = localPosInfo.LocalPosMagicRegion
					break
				}
			}
		}

		// 激励视频样式, 0 旧版 1 新版
		resp["reward_video_type"] = localPosInfo.LocalPosRewardVideoType
		// 激励视频点击手势引导
		resp["reward_video_clk_guide"] = localPosInfo.LocalPosRewardVideoClkGuide

		// 是否下载二次确认弹窗, 0(关), 1(开)
		resp["is_download_dialog"] = localPosInfo.LocalPosIsDownloadDialog
		// 是否移动网络自动播放, 0(关), 1(开)
		resp["is_auto_play_mobile_network"] = localPosInfo.LocalPosIsAutoPlayMobileNetwork
		// 是否点击区域下载合规弹窗
		resp["is_clickview_download_compliance"] = localPosInfo.LocalPosIsClickViewDownloadCompliance
		// 是否非点击区域下载合规弹窗
		resp["is_adview_download_compliance"] = localPosInfo.LocalPosIsAdViewDownloadCompliance
		// 是否激励视频退出弹窗
		if localPosInfo.LocalPosIsRewardVideoExitConfirm == 1 {
			resp["reward_video_exit_confirm"] = 0
		} else {
			resp["reward_video_exit_confirm"] = 1
		}
		// 是否摇一摇后台关闭传感器
		if localPosInfo.LocalPosIsYaoBgDisableSensor == 0 {
			resp["yao_disable_sensor"] = 0
		} else {
			resp["yao_disable_sensor"] = 1
		}
	}

	// SDK摇一摇组件 (关闭: 0; 枫岚组件:1; 枫岚动画+三方组件: 2)
	// 可支持SDK广告源(优量汇:1; 快手:2; 京东:3; Tanx:4; 穿山甲:5; 百度:6; 爱奇艺:7)
	// jd 2.4.0, ks 3.3.36
	resp["extsdk_interaction_type"] = localPosInfo.LocalPosIsSupportSDKInteraction
	//  SDK摇一摇组件, 在小于(<3.0.23)下发关闭选项
	if IsSDKVersionMoreThanOrEqual3023(c, mhReq.SDKVersion) {
	} else {
		localPosInfo.LocalPosIsSupportSDKInteraction = 0
	}

	if localPosInfo.LocalPosIsSupportSDKInteraction == 0 {
		resp["extsdk_interaction_support"] = []string{}
	} else {
		var itemMapArray []map[string]interface{}
		for _, item := range strings.Split(localPosInfo.LocalPosSDKInteractionSupportTypes, ",") {
			// 枫岚组件:1, extsdk_interaction_support, 不返回优量汇: 1
			if localPosInfo.LocalPosIsSupportSDKInteraction == 1 {
				if item == "1" {
					continue
				}
			}
			// 枫岚动画+三方组件: 2, extsdk_interaction_support, 只返回优量汇, 快手, 京东
			if localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				if item == "1" || item == "2" || item == "3" {
				} else {
					continue
				}
			}
			itemMap := map[string]interface{}{}
			itemMap["type"] = utils.ConvertStringToInt(item)
			if item == "3" && localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				itemMap["min_version"] = "2.4.0"
				itemMap["max_version"] = "10"
			} else if item == "2" && localPosInfo.LocalPosIsSupportSDKInteraction == 2 {
				itemMap["min_version"] = "3.3.36"
				itemMap["max_version"] = "10"
			} else {
				itemMap["min_version"] = "1"
				itemMap["max_version"] = "10"
			}

			itemMapArray = append(itemMapArray, itemMap)
		}
		resp["extsdk_interaction_support"] = itemMapArray
	}

	// 替换SDK广告来源
	if localPosInfo.LocalPosIsRadst == 0 {
	} else {
		resp["radst"] = utils.ConvertStringToInt(localPosInfo.LocalPosRadst)
	}

	// 是否trace report
	resp["trace_report"] = localPosInfo.LocalPosIsTraceReport

	var respListArray []map[string]interface{}

	for _, item := range *category {
		if item.IsActive == 1 {
		} else {
			continue
		}
		respListItem := map[string]interface{}{}
		respListItem["platform_pos_id"] = item.PlatformPosID
		respListItem["platform_pos_type"] = item.PlatformPosType
		respListItem["platform_pos_sub_type"] = item.PlatformPosMaterialType
		respListItem["platform_app_id"] = item.PlatformAppID
		respListItem["platform_media_id"] = item.PlatformMediaID
		respListItem["floor_price"] = 0               // 此字段废弃, floor_price版本设置0
		respListItem["final_price"] = item.FinalPrice // 此字段废弃, final_price和新字段platform_pos_fnp保持一致

		// 反作弊
		if item.Label == 3 {
			_, anticheatSDKCacheError := db.GlbBigCacheMinute.Get(fmt.Sprintf(config.AnticheatConfigSDK, item.PlatformMediaID))
			if anticheatSDKCacheError != nil {
			} else {
				// redis
				tmpRedisKey := fmt.Sprintf(rediskeys.ADX_SSP_ANTICHEAT_KEY, mhReq.Device.DIDMd5)
				_, tmpRedisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()
				if tmpRedisErr != nil {
				} else {
					continue
				}
			}
		}

		// sdk不支持底价设置
		// tmpFloorPrice := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(item.FloorPrice)), []byte(config.EncryptKEY))
		// respListItem["platform_pos_flp"] = base64.StdEncoding.EncodeToString(tmpFloorPrice)
		// sdk, ecpm_type = 1(实时竞价-动态价), 截断价不能大于0, 否则违反bidding概念
		// ecpm_type = 0, 2, 配置下发platform_pos_fnp最小值为1。
		if localPosInfo.LocalPosEcpmType == 1 {
			tmpFinalPrice := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(0)), []byte(config.EncryptKEY))
			respListItem["platform_pos_fnp"] = base64.StdEncoding.EncodeToString(tmpFinalPrice)
		} else {
			if item.FinalPrice < 1 {
				tmpFinalPrice := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(1)), []byte(config.EncryptKEY))
				respListItem["platform_pos_fnp"] = base64.StdEncoding.EncodeToString(tmpFinalPrice)
			} else {
				tmpFinalPrice := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(item.FinalPrice)), []byte(config.EncryptKEY))
				respListItem["platform_pos_fnp"] = base64.StdEncoding.EncodeToString(tmpFinalPrice)
			}
		}

		tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
		if tmpSDKPlatformPos != nil {
			respListItem["is_replace_xy"] = tmpSDKPlatformPos.PlatformPosIsReplaceXYDefault
			respListItem["replace_xy_default"] = tmpSDKPlatformPos.PlatformPosReplaceXYDefault
			if item.Label == 3 {
				//sdk固定价格
				if tmpSDKPlatformPos.PlatformPosEcpmType == 1 {
					tmpDecrypt := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpSDKPlatformPos.PlatformPosEcpm)), []byte(config.EncryptKEY))
					tmpDecrypt1 := base64.StdEncoding.EncodeToString(tmpDecrypt)
					respListItem["platform_pos_log_sp"] = tmpDecrypt1
				}
			}

			if item.Label == 3 {
				if localPosInfo.LocalAppIsSDKAntiCheat == 1 {
					// sdk_times_frequency_rule:{
					// 	"appid": 1,
					// 	"channel": 1,
					// 	"p_pos_id": 1
					//    }
					isSDKAntiCheatPlatformOK := false
					if localPosInfo.LocalAppIsSDKAntiCheatPlatformIDs == 0 {
						isSDKAntiCheatPlatformOK = true
					} else if localPosInfo.LocalAppIsSDKAntiCheatPlatformIDs == 1 && len(localPosInfo.LocalAppSDKAntiCheatPlatformIDs) > 0 {
						tmpPlatformIDsArray := strings.Split(localPosInfo.LocalAppSDKAntiCheatPlatformIDs, ",")
						for _, tmpItem := range tmpPlatformIDsArray {
							if tmpItem == tmpSDKPlatformPos.PlatformMediaID {
								isSDKAntiCheatPlatformOK = true
								break
							}
						}
					}
					if isSDKAntiCheatPlatformOK {
						tmpSDKTimesFrequencyRuleMap := map[string]interface{}{}
						tmpSDKTimesFrequencyRuleMap["channel"] = 1

						respListItem["sdk_times_frequency_rule"] = tmpSDKTimesFrequencyRuleMap
						respListItem["sdk_expose_times_frequency_type"] = localPosInfo.LocalAppSDKAntiCheatExposeType
						respListItem["sdk_expose_times_frequency_value"] = utils.ConvertStringToInt(localPosInfo.LocalAppSDKAntiCheatExposeValue)
						respListItem["sdk_click_times_frequency_type"] = localPosInfo.LocalAppSDKAntiCheatClickType
						respListItem["sdk_click_times_frequency_value"] = utils.ConvertStringToInt(localPosInfo.LocalAppSDKAntiCheatClickValue)
					}
				}
			}
		}
		// black did 过滤jd sdk配置
		// if item.Label == 3 && item.PlatformMediaID == "19" {
		// 	isInBlack := false
		// 	for _, tmpBlackItem := range utilities.BlackDIDList {
		// 		// fmt.Println("debug_black_did:", tmpBlackItem)
		// 		if len(mhReq.Device.Imei) > 0 && tmpBlackItem == mhReq.Device.Imei {
		// 			isInBlack = true
		// 			break
		// 		}
		// 		if len(mhReq.Device.Oaid) > 0 && tmpBlackItem == mhReq.Device.Oaid {
		// 			isInBlack = true
		// 			break
		// 		}
		// 	}
		// 	if isInBlack {
		// 		continue
		// 	}
		// }

		if item.PlatformMediaID == "19" {
			for _, jdSplashSelfRenderPlatformPosIdItem := range utilities.JDSDKSplashSelfRenderPlatformPosIdList {
				if jdSplashSelfRenderPlatformPosIdItem == item.PlatformPosID {
					respListItem["jd_splash_cr"] = 1
					break
				}
			}
		}

		if item.Label == 3 {
			if mhReq.SDKVersion == "3.1.5" || mhReq.SDKVersion == "3.2.9" || mhReq.SDKVersion == "3.2.10" || mhReq.SDKVersion == "3.2.11" || mhReq.SDKVersion == "3.2.12" {
				randNum := rand.Intn(100)
				if randNum < localPosInfo.LocalAppSDKFilterSDKWeight {
					continue
				}
			}
		}

		//sdk利润率，api不下发
		if item.Label == 3 {
			// 利润率
			tmpProfitRate := float32(utils.ConvertStringToFloat(item.ProfitRate))
			if len(item.ProfitRate) == 0 {
				tmpProfitRate = localPosInfo.LocalPosProfitRate
			}

			tmpDecrypt := utils.AesECBEncrypt([]byte(utils.ConvertFloat32ToString(tmpProfitRate)), []byte(config.EncryptKEY))
			tmpDecrypt1 := base64.StdEncoding.EncodeToString(tmpDecrypt)
			respListItem["platform_pos_log_pr"] = tmpDecrypt1
		}

		if item.Label == 3 {
			respListItem["mode"] = "1"
		} else {
			respListItem["mode"] = "0"
		}
		if item.PlatformMediaID == "43" {
			if tmpSDKPlatformPos != nil {
				if len(tmpSDKPlatformPos.PlatformAppKey) > 0 {
					respListItem["platform_app_key"] = tmpSDKPlatformPos.PlatformAppKey
				}
				if len(tmpSDKPlatformPos.PlatformAppSecret) > 0 {
					respListItem["platform_app_secret"] = tmpSDKPlatformPos.PlatformAppSecret
				}
			}
		}

		// logo app id
		sdkLogoConfig, apiLogoConfig := GetLogoConfig(localAppID, utils.ConvertIntToString(localPosID), mhReq)
		if item.Label == 3 {
			respListItem["logo_style_id"] = utils.ConvertStringToInt(sdkLogoConfig)
		} else {
			respListItem["logo_style_id"] = utils.ConvertStringToInt(apiLogoConfig)
		}

		// fmt.Println("kbg_debug p_app_id:", item.PlatformAppID)
		// fmt.Println("kbg_debug p_pos_id:", item.PlatformPosID)
		// fmt.Println("kbg_debug floor price:", item.FloorPrice)
		// fmt.Println("kbg_debug final price:", item.FinalPrice)
		// fmt.Println("kbg_debug white:", item.WhiteList)
		// fmt.Println("kbg_debug black:", item.BlackList)
		// fmt.Println("kbg_debug ip:", mhReq.Device.IP)

		// if localPosInfo.LocalPosID == "57858" {
		// 	fmt.Println("kbg_debug 57858 debug label:", item.Label, item.PlatformAppID, item.PlatformPosID)
		// }
		if item.Label == 3 {
			// if localPosInfo.LocalPosID == "57858" {
			// 	fmt.Println("kbg_debug 57858 debug config:", item.WhiteList, item.BlackList, item.MaxReqNum, item.MaxExpNum, item.MaxClkNum)
			// }
			// label == 3 sdk配置, 判定空did_md5生成规则为空, 直接不返回sdk配置
			if mhReq.Device.Os == "android" {
				if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
					// fmt.Println("kbg_debug_sdk_config sdk_verison:" + sdkVersion + ", os:" + mhReq.Device.Os + ",osv:" + mhReq.Device.OsVersion + ",imei:" + mhReq.Device.Imei + ",android_id:" + mhReq.Device.AndroidID + ",oaid:" + mhReq.Device.Oaid)
					continue
				}
			}

			// 判定地域
			if len(item.WhiteList) > 0 || len(item.BlackList) > 0 {
				if strings.Contains(mhReq.Device.IP, ":") {
				} else {
					// 地域白名单
					if len(item.WhiteList) > 0 {
						if strings.Contains(item.WhiteList, mhReq.Device.IPProvince) || strings.Contains(item.WhiteList, mhReq.Device.IPCountry) {
						} else {
							fmt.Println("sdk white failed")
							continue
						}
					}
					// 地域黑名单
					if len(item.BlackList) > 0 {
						if strings.Contains(item.BlackList, mhReq.Device.IPProvince) || strings.Contains(item.BlackList, mhReq.Device.IPCountry) {
							fmt.Println("sdk black failed")
							continue
						}
					}
				}
			}

			// 判定厂商白名单
			isManufacturerOK, _ := IsManufacturerCategoryOK(c, mhReq, &item)
			if isManufacturerOK {
			} else {
				fmt.Println("sdk category manufacturer failed :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
				continue
			}

			// 判定max req
			if item.MaxReqNum > 0 {
				maxReqRedisKey := "max_req_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxReqRedisValue, cacheErr := models.CacheFromRedis(c, maxReqRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxReqRedisValue) == 0 {
						fmt.Println("sdk max req return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max exp
			if item.MaxExpNum > 0 {
				maxExpRedisKey := "max_exp_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxExpRedisValue, cacheErr := models.CacheFromRedis(c, maxExpRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxExpRedisValue) == 0 {
						fmt.Println("sdk max exp return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// 判定max clk
			if item.MaxClkNum > 0 {
				maxClkRedisKey := "max_clk_" + time.Now().Format("2006-01-02") + "_" + localPosInfo.LocalAppID + "_" + localPosInfo.LocalPosID + "_" + item.PlatformAppID + "_" + item.PlatformPosID
				maxClkRedisValue, cacheErr := models.CacheFromRedis(c, maxClkRedisKey)

				if cacheErr != nil {
					// fmt.Println("redis error:", redisErr)
				} else {
					if utils.ConvertStringToInt(maxClkRedisValue) == 0 {
						fmt.Println("sdk max clk return :", localPosInfo.LocalAppID, localPosInfo.LocalPosID, item.PlatformAppID, item.PlatformPosID)
						continue
					}
				}
			}

			// sdk人群包
			if IsCategoryCrowdPackageOK(c, mhReq, localPosInfo, &item, nil) {
			} else {
				continue
			}

			// 判定流水, dau
			tmpSDKPlatformPos := models.GetPlatformPosInfo(c, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID)
			if tmpSDKPlatformPos != nil {
				isPlatformPolicyOK := models.IsPlatformPolicyOK(c, mhReq, localPosInfo, tmpSDKPlatformPos.PlatformMediaID, tmpSDKPlatformPos.PlatformAppCorpID)
				// fmt.Println("sdk is policy ok: ", isPlatformPolicyOK)
				if isPlatformPolicyOK {
				} else {
					fmt.Println("sdk platform policy not ok , app id: " + localPosInfo.LocalAppID)
					continue
				}

				// sdk人群包
				if IsCrowdPackageOK(c, mhReq, localPosInfo, tmpSDKPlatformPos, nil) {
				} else {
					continue
				}
			}

			// sdk限制
			if len(mhReq.SDKVersion) > 0 {
				// fmt.Println(sdkVersion)
				if len(item.WhiteVersion) > 0 {
					tmpSDKVersionArray := strings.Split(mhReq.SDKVersion, ".")
					tmpWhiteVersionArray := strings.Split(item.WhiteVersion, ".")

					tmpSDKVersion0 := 0
					tmpWhiteVersion0 := 0

					tmpSDKVersion1 := 0
					tmpWhiteVersion1 := 0

					tmpSDKVersion2 := 0
					tmpWhiteVersion2 := 0

					tmpSDKVersion3 := 0
					tmpWhiteVersion3 := 0

					if len(tmpSDKVersionArray) > 0 && len(tmpWhiteVersionArray) > 0 {
						if utils.IsNum(tmpSDKVersionArray[0]) && utils.IsNum(tmpWhiteVersionArray[0]) {
							tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
							tmpWhiteVersion0 = utils.ConvertStringToInt(tmpWhiteVersionArray[0])
							if tmpSDKVersion0 < tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 1 && len(tmpWhiteVersionArray) > 1 {
						if utils.IsNum(tmpSDKVersionArray[1]) && utils.IsNum(tmpWhiteVersionArray[1]) {
							tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
							tmpWhiteVersion1 = utils.ConvertStringToInt(tmpWhiteVersionArray[1])
							if tmpSDKVersion1 < tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 2 && len(tmpWhiteVersionArray) > 2 {
						if utils.IsNum(tmpSDKVersionArray[2]) && utils.IsNum(tmpWhiteVersionArray[2]) {
							tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
							tmpWhiteVersion2 = utils.ConvertStringToInt(tmpWhiteVersionArray[2])
							if tmpSDKVersion2 < tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
					if len(tmpSDKVersionArray) > 3 && len(tmpWhiteVersionArray) > 3 {
						if utils.IsNum(tmpSDKVersionArray[3]) && utils.IsNum(tmpWhiteVersionArray[3]) {
							tmpSDKVersion3 = utils.ConvertStringToInt(tmpSDKVersionArray[3])
							tmpWhiteVersion3 = utils.ConvertStringToInt(tmpWhiteVersionArray[3])
							if tmpSDKVersion3 < tmpWhiteVersion3 && tmpSDKVersion2 == tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
								continue
							}
						}
					}
				}
				if len(item.BlackVersion) > 0 {
					if strings.Contains(item.BlackVersion, mhReq.SDKVersion) {
						continue
					}
				}
			}
		}
		respListArray = append(respListArray, respListItem)
	}

	// if localPosInfo.LocalPosID == "57858" {
	// 	tmpByte, _ := json.Marshal(respListArray)
	// 	fmt.Println("kbg_debug 57858 config:", mhReq.Device.IP, string(tmpByte))
	// }

	if len(respListArray) == 0 {
		return MhSDKErrorRespMap(102006, "", localPosInfo)
	}
	resp["data"] = respListArray

	// fmt.Println(resp)
	// fmt.Println("adx sdk end")
	return &resp
}

// 针对sdk 3.0.23以下版本, 如果大于等于3.0.23返回true, 如果小于3.0.23返回false
func IsSDKVersionMoreThanOrEqual3023(c context.Context, sdkVersion string) bool {
	if len(sdkVersion) <= 0 {
		return false
	}
	// fmt.Println(sdkVersion)
	tmpSDKVersionArray := strings.Split(sdkVersion, ".")

	tmpSDKVersion0 := 0
	tmpWhiteVersion0 := 3

	tmpSDKVersion1 := 0
	tmpWhiteVersion1 := 0

	tmpSDKVersion2 := 0
	tmpWhiteVersion2 := 23

	if len(tmpSDKVersionArray) > 0 {
		if utils.IsNum(tmpSDKVersionArray[0]) {
			tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
			if tmpSDKVersion0 < tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 1 {
		if utils.IsNum(tmpSDKVersionArray[1]) {
			tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
			if tmpSDKVersion1 < tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 2 {
		if utils.IsNum(tmpSDKVersionArray[2]) {
			tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
			if tmpSDKVersion2 < tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}

	return true
}

// 针对sdk 3.0.21以下版本, 如果大于等于3.0.21返回true, 如果小于3.0.21返回false
func IsSDKVersionMoreThanOrEqual3021(c context.Context, sdkVersion string) bool {
	if len(sdkVersion) <= 0 {
		return false
	}
	// fmt.Println(sdkVersion)
	tmpSDKVersionArray := strings.Split(sdkVersion, ".")

	tmpSDKVersion0 := 0
	tmpWhiteVersion0 := 3

	tmpSDKVersion1 := 0
	tmpWhiteVersion1 := 0

	tmpSDKVersion2 := 0
	tmpWhiteVersion2 := 21

	if len(tmpSDKVersionArray) > 0 {
		if utils.IsNum(tmpSDKVersionArray[0]) {
			tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
			if tmpSDKVersion0 < tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 1 {
		if utils.IsNum(tmpSDKVersionArray[1]) {
			tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
			if tmpSDKVersion1 < tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 2 {
		if utils.IsNum(tmpSDKVersionArray[2]) {
			tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
			if tmpSDKVersion2 < tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}

	return true
}

func paramRepair(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu) {
	// 如果需要置空model, 置空model
	if models.IsNeedReplaceModel(c, strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1))) {
		// fmt.Println("kbg_debug_wrong_model: ", mhReq.Device.Model, localPos.LocalAppID, localPos.LocalPosID)
		mhReq.Device.Model = ""
	}

	if models.IsNeedReplaceModel(c, strings.ToLower(strings.Replace(mhReq.Device.HardwareMachine, " ", "", -1))) {
		// fmt.Println("kbg_debug_wrong_model: ", mhReq.Device.Model, localPos.LocalAppID, localPos.LocalPosID)
		mhReq.Device.HardwareMachine = ""
	}

	if models.IsNeedReplaceModel(c, strings.ToLower(strings.Replace(mhReq.Device.HardwareModel, " ", "", -1))) {
		// fmt.Println("kbg_debug_wrong_model: ", mhReq.Device.Model, localPos.LocalAppID, localPos.LocalPosID)
		mhReq.Device.HardwareModel = ""
	}

	// did_md5
	mhReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(mhReq)

	// 补model osv(android, ios)
	if len(mhReq.Device.Model) == 0 || len(mhReq.Device.OsVersion) == 0 {
		if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		} else {
			redisKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_DATA_WITH_DIDMD5_PREFIX, mhReq.Device.DIDMd5)

			redisValue, redisErr := models.CacheFromRedis(c, redisKey)
			if redisErr != nil {
			} else {
				didDataStu := models.SspDidDataObject{}
				json.Unmarshal([]byte(redisValue), &didDataStu)

				if len(mhReq.Device.Model) == 0 && len(didDataStu.RawModel) > 0 {
					mhReq.Device.Model = didDataStu.RawModel
				}

				if len(mhReq.Device.OsVersion) == 0 && len(didDataStu.Osv) > 0 {
					mhReq.Device.OsVersion = didDataStu.Osv
				}

				if mhReq.Device.Os == "android" {
					if len(didDataStu.Manufacturer) > 0 && len(mhReq.Device.Manufacturer) == 0 {
						mhReq.Device.Manufacturer = didDataStu.Manufacturer
					}
				}
			}
		}
	}

	// model补manufacturer
	if mhReq.Device.Os == "android" {
		if len(mhReq.Device.Manufacturer) == 0 {
			if len(mhReq.Device.Model) > 0 {
				md5Key := utils.GetMd5(strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
				redisKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_DID_MANUFACTURER_WITH_MODELMD5_PREFIX, md5Key)

				redisValue, redisErr := models.CacheFromRedis(c, redisKey)
				if redisErr != nil {
				} else {
					didDataStu := models.SspDidModelAndManufacturer{}
					json.Unmarshal([]byte(redisValue), &didDataStu)

					if len(didDataStu.Manufacturer) > 0 {
						mhReq.Device.Manufacturer = didDataStu.Manufacturer
					}
				}
			}
		}
	}

	// osv hack  -> 转小些替换 "android ", "android", "ios ", "ios"
	// osv 包含"," 取,的最后一个
	mhReq.Device.OsVersion = strings.ToLower(mhReq.Device.OsVersion)
	if mhReq.Device.Os == "android" {
		if strings.Contains(mhReq.Device.OsVersion, "android") {
			mhReq.Device.OsVersion = strings.Replace(mhReq.Device.OsVersion, "android ", "", -1)
			mhReq.Device.OsVersion = strings.Replace(mhReq.Device.OsVersion, "android", "", -1)
		}

		if strings.Contains(mhReq.Device.OsVersion, ",") {
			tmpOsvArray := strings.Split(mhReq.Device.OsVersion, ",")
			mhReq.Device.OsVersion = tmpOsvArray[len(tmpOsvArray)-1]
		}
	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(mhReq.Device.OsVersion, "ios") {
			mhReq.Device.OsVersion = strings.Replace(mhReq.Device.OsVersion, "ios ", "", -1)
			mhReq.Device.OsVersion = strings.Replace(mhReq.Device.OsVersion, "ios", "", -1)
		}
	}

	// 补osv
	if len(mhReq.Device.OsVersion) > 0 {
		if mhReq.Device.Os == "android" {
			osvTmpVersion1 := 0
			osvTmpVersionArray := strings.Split(mhReq.Device.OsVersion, ".")
			if len(osvTmpVersionArray) > 0 {
				if utils.IsNum(osvTmpVersionArray[0]) {
					osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
				}
			}
			if osvTmpVersion1 == 21 {
				mhReq.Device.OsVersion = "5"
			} else if osvTmpVersion1 == 22 {
				mhReq.Device.OsVersion = "5.1"
			} else if osvTmpVersion1 == 23 {
				mhReq.Device.OsVersion = "6"
			} else if osvTmpVersion1 == 24 {
				mhReq.Device.OsVersion = "7"
			} else if osvTmpVersion1 == 25 {
				mhReq.Device.OsVersion = "7.1.1"
			} else if osvTmpVersion1 == 26 {
				mhReq.Device.OsVersion = "8"
			} else if osvTmpVersion1 == 27 {
				mhReq.Device.OsVersion = "8.1"
			} else if osvTmpVersion1 == 28 {
				mhReq.Device.OsVersion = "9"
			} else if osvTmpVersion1 == 29 {
				mhReq.Device.OsVersion = "10"
			} else if osvTmpVersion1 == 30 {
				mhReq.Device.OsVersion = "11"
			} else if osvTmpVersion1 == 31 {
				mhReq.Device.OsVersion = "12"
			} else if osvTmpVersion1 == 32 {
				mhReq.Device.OsVersion = "12"
			} else if osvTmpVersion1 == 33 {
				mhReq.Device.OsVersion = "13"
			} else if osvTmpVersion1 == 34 {
				mhReq.Device.OsVersion = "14"
			} else if osvTmpVersion1 == 35 {
				mhReq.Device.OsVersion = "15"
			}
		}
	}

	// 不区分os，osv末尾去".0"，譬如"14.0.0" -> "14"
	mhReq.Device.OsVersion, _ = strings.CutSuffix(mhReq.Device.OsVersion, ".0.0")
	mhReq.Device.OsVersion, _ = strings.CutSuffix(mhReq.Device.OsVersion, ".0")

	// 补 model
	if len(mhReq.Device.Model) > 0 {
		if mhReq.Device.Os == "android" {
			mhReq.Device.Model = strings.Replace(mhReq.Device.Model, "+", " ", -1)
		} else if mhReq.Device.Os == "ios" {
			if strings.HasPrefix(mhReq.Device.Model, "iP") && strings.Contains(mhReq.Device.Model, "-") {
				if len(mhReq.Device.Model) < 15 {
					regexpOK, regexpErr := regexp.MatchString("[a-zA-Z]+\\d+-\\d+", mhReq.Device.Model)
					if regexpErr == nil && regexpOK {
						mhReq.Device.Model = strings.Replace(mhReq.Device.Model, "-", ",", -1)
					}
				}
			} else if strings.HasPrefix(mhReq.Device.Model, "iP") && strings.Contains(mhReq.Device.Model, "_") {
				mhReq.Device.Model = strings.Replace(mhReq.Device.Model, "_", " ", -1)
			}

			if strings.Contains(mhReq.Device.Model, ",") {
			} else {
				if strings.HasPrefix(mhReq.Device.Model, "iP") {
					mhReq.Device.Model = utils.GetIOSModel(mhReq.Device.Model)
				}
			}
		}
	}

	// url decode
	unescapeUa, _ := url.QueryUnescape(mhReq.Device.Ua)
	mhReq.Device.Ua = unescapeUa
	// 补UA规则
	// android先取redis,没查到用打底的
	// 打底的android: "Mozilla/5.0 (Linux; Android " + osv + "; " + model + " Build/" + manufacturer + model + "; wv)"
	// 打底的ios: "Mozilla/5.0 (iPhone; CPU iPhone OS " + strings.Replace(osv, ".", "_", -1) + " like Mac OS X)"
	// 如果配置为补空和替换, 媒体未传ua, 均先补空ua
	if localPos.LocalAppIsFixUA == 0 {
	} else if localPos.LocalAppIsFixUA == 1 || localPos.LocalAppIsFixUA == 2 {
		if len(mhReq.Device.Ua) == 0 {
			mhReq.Device.Ua = GetDeviceUA(c, mhReq)
		}
	}

	// app_id: 11009, ua去掉 VivoBrowser/********
	if localPos.LocalAppID == "11009" {
		if strings.Contains(mhReq.Device.Ua, " VivoBrowser") {
			tmpUAArray := strings.Split(mhReq.Device.Ua, " VivoBrowser")
			mhReq.Device.Ua = tmpUAArray[0]
		}
	}

	if mhReq.Device.Os == "android" {
		if strings.ToLower(mhReq.Device.Manufacturer) == "huawei" {
			mhReq.Device.Manufacturer = "HUAWEI"
		} else if strings.ToLower(mhReq.Device.Manufacturer) == "vivo" {
			mhReq.Device.Manufacturer = "vivo"
		} else if strings.ToLower(mhReq.Device.Manufacturer) == "oppo" {
			mhReq.Device.Manufacturer = "OPPO"
		} else if strings.ToLower(mhReq.Device.Manufacturer) == "xiaomi" {
			mhReq.Device.Manufacturer = "Xiaomi"
		}
	} else if mhReq.Device.Os == "ios" {
		mhReq.Device.Manufacturer = "Apple"
	}

	if mhReq.Device.Os == "android" {
		// imei, android_id, oaid去空格
		mhReq.Device.Imei = strings.Replace(mhReq.Device.Imei, " ", "", -1)
		mhReq.Device.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
		mhReq.Device.AndroidID = strings.Replace(mhReq.Device.AndroidID, " ", "", -1)
		mhReq.Device.Oaid = strings.Replace(mhReq.Device.Oaid, " ", "", -1)

		// osv >= 10, imei = ""
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if strings.ToLower(mhReq.Device.Imei) == "unknown" ||
			strings.ToLower(mhReq.Device.Imei) == "null" ||
			len(mhReq.Device.Imei) < 3 ||
			osvMajor >= 10 {
			mhReq.Device.Imei = ""
		}

		if len(mhReq.Device.ImeiMd5) != 32 ||
			osvMajor >= 10 {
			mhReq.Device.ImeiMd5 = ""
		}

		if len(mhReq.Device.AndroidID) < 3 {
			mhReq.Device.AndroidID = ""
		}

		if strings.Contains(mhReq.Device.Oaid, "00000000") {
			mhReq.Device.Oaid = ""
		}

		if strings.Contains(strings.ToLower(mhReq.Device.Oaid), "unknown") ||
			strings.Contains(strings.ToLower(mhReq.Device.Oaid), "null") {
			mhReq.Device.Oaid = ""
		}

	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(mhReq.Device.Idfa, "00000000") {
			mhReq.Device.Idfa = ""
			mhReq.Device.IdfaMd5 = ""
		}
		if strings.Contains(mhReq.Device.IdfaMd5, "00000000") {
			mhReq.Device.IdfaMd5 = ""
		}
		if mhReq.Device.IdfaMd5 == utils.GetMd5("") {
			mhReq.Device.IdfaMd5 = ""
		}

		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 14 {
		} else {
			// 参数验证: ios osv >= 14, idfa = "" idfa_md5 = ""
			// 因为上游有iOS14以上允许idfa上报开关, 所以参数验证的时候, ios osv >= 14, idfa, idfa_md5不能为空
			// mhReq.Device.Idfa = ""
			// mhReq.Device.IdfaMd5 = ""
		}

		mhReq.Device.Language = "zh-Hans-CN"
		mhReq.Device.TimeZone = "28800"
		mhReq.Device.Country = "CN"

		// 修正caid_multi
		if len(mhReq.Device.CAIDMulti) > 0 {
			var tmpCAIDMulti []models.MHReqCAIDMulti
			for _, tmpItem := range mhReq.Device.CAIDMulti {
				if len(tmpItem.CAID) > 0 && len(tmpItem.CAIDVersion) > 0 {
					tmpCAIDMulti = append(tmpCAIDMulti, tmpItem)
				}
			}
			mhReq.Device.CAIDMulti = tmpCAIDMulti

			// caid排序
			sort.Sort(models.CAIDMultiSort(mhReq.Device.CAIDMulti))
		}
	}

	// 屏幕值不论宽高统一按照：大值为高，小值为宽修正
	tmpScreenWidth := mhReq.Device.ScreenWidth
	tmpScreenHeight := mhReq.Device.ScreenHeight
	if tmpScreenWidth > tmpScreenHeight {
		mhReq.Device.ScreenWidth = tmpScreenHeight
		mhReq.Device.ScreenHeight = tmpScreenWidth
	}

	// 补dpi
	if mhReq.Device.DPI <= 0 {
		mhReq.Device.DPI = float32(GetDeviceDpi(c, mhReq.Device.Manufacturer, mhReq.Device.Model, mhReq.Device.Os, mhReq.Device.OsVersion))
	}

	if mhReq.Device.DPI > 120 {
		mhReq.Device.XDPI = mhReq.Device.DPI / 160
	} else {
		mhReq.Device.XDPI = mhReq.Device.DPI
	}

	// 通过ip补country, province, city
	if strings.Contains(mhReq.Device.IP, ":") {
	} else {
		region, err := utils.New(config.IPDBPath)
		defer region.Close()
		if err != nil {
		} else {
			// fmt.Println("kbg_ip, ip: " + mhReq.Device.IP)
			ip, _ := region.BtreeSearch(mhReq.Device.IP)
			// fmt.Println(ip.Country)
			// fmt.Println(ip.Province)
			// fmt.Println(ip, err)
			mhReq.Device.IPCountry = ip.Country
			mhReq.Device.IPProvince = ip.Province
			mhReq.Device.IPCity = ip.City
		}
	}

	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, mhReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		mhReq.Device.LBSIPCountry = ip2locationResp.Country
		mhReq.Device.LBSIPProvince = ip2locationResp.Region
		mhReq.Device.LBSIPCity = ip2locationResp.City
	}
}

func paramVerify(c context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu) (int, int) {

	if len(mhReq.Device.IP) == 0 {
		return 104440, 900001
	}
	whiteAppIDs := "10012,10013,10017"
	// fmt.Println(whiteAppIDs)
	// fmt.Println(mhReq.App.AppID)
	// fmt.Println(mhReq.App.AppBundleID)
	// fmt.Println(localPos.LocalAppBundleID)
	// fmt.Println(whiteAppIDs)
	// fmt.Println(strings.Contains(whiteAppIDs, mhReq.App.AppID))
	if strings.Contains(whiteAppIDs, mhReq.App.AppID) || mhReq.App.AppType == 4 {
		if mhReq.App.AppBundleID != localPos.LocalAppBundleID {
			models.BigDataAdxMismatchBundle(c, bigdataUID, mhReq, localPos)
		}
	} else {
		if localPos.LocalAppIsVerifyPackage == 1 {
			if mhReq.App.AppBundleID != localPos.LocalAppBundleID {
				return 107006, 900001
			}
		}
	}

	if len(mhReq.Device.Os) == 0 {
		return 104440, 900001
	}

	if len(mhReq.Device.OsVersion) == 0 {
		return 107024, 900001
	}

	// sdk 高/宽<1.6, 返回102008
	if localPos.LocalAppType == "1" {
		if mhReq.Device.ScreenWidth == 0 || mhReq.Device.ScreenHeight == 0 {
			return 102008, 900001
		}
		if float32(mhReq.Device.ScreenHeight)/float32(mhReq.Device.ScreenWidth) < 1.6 {
			return 102008, 900001
		}
	}

	if mhReq.Device.Os == "android" {
		// fmt.Println(mhReq.Device.Model)
		// fmt.Println(mhReq.Device.Manufacturer)
		osvTmpVersion1 := 0
		osvTmpVersion2 := 0
		osvTmpVersion3 := 0
		osvTmpVersionArray := strings.Split(mhReq.Device.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				fmt.Println("android osv return 1:", mhReq.Device.OsVersion)
				return 107016, 900001
			}
		}
		if len(osvTmpVersionArray) > 1 {
			if utils.IsNum(osvTmpVersionArray[1]) {
				osvTmpVersion2 = utils.ConvertStringToInt(osvTmpVersionArray[1])
			} else {
				fmt.Println("android osv return 2:", mhReq.Device.OsVersion)
				return 107016, 900001
			}
		}
		if len(osvTmpVersionArray) > 2 {
			if utils.IsNum(osvTmpVersionArray[2]) {
				osvTmpVersion3 = utils.ConvertStringToInt(osvTmpVersionArray[2])
			} else {
				fmt.Println("android osv return 3:", mhReq.Device.OsVersion)
				return 107016, 900001
			}
		}
		if osvTmpVersion1 < 5 {
			fmt.Println("android osv return 4:", mhReq.Device.OsVersion)
			return 107016, 900001
		}
		if osvTmpVersion2 > 9 {
			fmt.Println("android osv return 5:", mhReq.Device.OsVersion)
			return 107016, 900001
		}
		if osvTmpVersion3 > 99 {
			fmt.Println("android osv return 6:", mhReq.Device.OsVersion)
			return 107016, 900001
		}
		// fmt.Println("osv tmp v1:", osvTmpVersion1)
		// fmt.Println("osv tmp v2:", osvTmpVersion2)
		// fmt.Println("osv tmp v3:", osvTmpVersion3)

		if utils.VerifyDID(mhReq.Device.Imei, "imei") == false {
			return 107016, 900001
		}
		if utils.VerifyDID(mhReq.Device.ImeiMd5, "imei_md5") == false {
			return 107016, 900001
		}
		if utils.VerifyDID(mhReq.Device.AndroidID, "android_id") == false {
			return 107016, 900001
		}
		if utils.VerifyDID(mhReq.Device.AndroidIDMd5, "android_id_md5") == false {
			return 107016, 900001
		}
		if utils.VerifyDID(mhReq.Device.Oaid, "oaid") == false {
			return 107016, 900001
		}

		if len(mhReq.Device.Imei) > 0 {
			if utils.IsImei(mhReq.Device.Imei) == false {
				fmt.Println("wrong imei: " + mhReq.Device.Imei)
				return 104013, 900001
			}
		} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
		} else if len(mhReq.Device.AndroidID) > 0 {
		} else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
		}
	} else if mhReq.Device.Os == "ios" {

		// fmt.Println(mhReq.Device.Os)
		idfa := mhReq.Device.Idfa
		idfaMd5 := mhReq.Device.IdfaMd5
		osv := mhReq.Device.OsVersion

		if len(osv) == 0 {
			return 107024, 900001
		}

		// fmt.Println(idfa)
		// fmt.Println(idfaMd5)
		// if len(idfa) == 0 && len(idfaMd5) == 0 {
		// 	return 104014, 900001
		// }

		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 14 {
			if utils.VerifyDID(idfa, "idfa") == false {
				return 107016, 900001
			}
			if utils.VerifyDID(idfaMd5, "idfa_md5") == false {
				return 107016, 900001
			}
		}
		// if len(mhReq.Device.Idfa) == 0 && len(mhReq.Device.IdfaMd5) == 0 &&
		// 	len(mhReq.Device.DeviceStartSec) == 0 && len(mhReq.Device.Country) == 0 && len(mhReq.Device.Language) == 0 &&
		// 	len(mhReq.Device.DeviceNameMd5) == 0 && len(mhReq.Device.HardwareMachine) == 0 && len(mhReq.Device.HardwareModel) == 0 &&
		// 	len(mhReq.Device.PhysicalMemoryByte) == 0 && len(mhReq.Device.HarddiskSizeByte) == 0 && len(mhReq.Device.SystemUpdateSec) == 0 &&
		// 	len(mhReq.Device.TimeZone) == 0 {
		// 	return 104014, 900001
		// }

		osvTmpVersion1 := 0
		osvTmpVersion2 := 0
		osvTmpVersion3 := 0
		osvTmpVersionArray := strings.Split(mhReq.Device.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				fmt.Println("ios osv  return 1:", mhReq.Device.OsVersion)
				return 107024, 900001
			}
		}
		if len(osvTmpVersionArray) > 1 {
			if utils.IsNum(osvTmpVersionArray[1]) {
				osvTmpVersion2 = utils.ConvertStringToInt(osvTmpVersionArray[1])
			} else {
				fmt.Println("ios osv return 2:", mhReq.Device.OsVersion)
				return 107024, 900001
			}
		}
		if len(osvTmpVersionArray) > 2 {
			if utils.IsNum(osvTmpVersionArray[2]) {
				osvTmpVersion3 = utils.ConvertStringToInt(osvTmpVersionArray[2])
			} else {
				fmt.Println("ios osv return 3:", mhReq.Device.OsVersion)
				return 107024, 900001
			}
		}
		if osvTmpVersion1 < 9 || osvTmpVersion1 > 99 {
			fmt.Println("ios osv return 4:", mhReq.Device.OsVersion)
			return 107024, 900001
		}
		if osvTmpVersion2 > 9 {
			fmt.Println("ios osv return 5:", mhReq.Device.OsVersion)
			return 107024, 900001
		}
		if osvTmpVersion3 > 99 {
			fmt.Println("ios osv return 6:", mhReq.Device.OsVersion)
			return 107024, 900001
		}

		if len(idfa) > 0 {
			idfaRn := []rune(idfa)
			if len(idfa) != 36 || string(idfaRn[8]) != "-" || string(idfaRn[13]) != "-" || string(idfaRn[18]) != "-" || string(idfaRn[23]) != "-" {
				return 104014, 900001
			}
		} else if len(idfaMd5) > 0 {
			// 空idfa_md5
			if idfaMd5 == utils.GetMd5("") {
				return 104014, 900001
			}
		}
	}

	// 判定ua不包含Mozilla或Dalvik请求直接返回
	// if strings.HasPrefix(mhReq.Device.Ua, "Mozilla") || strings.HasPrefix(mhReq.Device.Ua, "Dalvik") {
	// } else {
	// 	return 104022, 900001
	// }

	if len(mhReq.Device.Ua) == 0 {
		return 104022, 900001
	}

	if len(mhReq.Device.Model) == 0 {
		return 107016, 900001
	}
	if len(mhReq.Device.Manufacturer) == 0 {
		return 107031, 900001
	}

	// black ip
	// fmt.Println("ip: ", mhReq.Device.IP)
	if strings.Contains(mhReq.Device.IP, ":") {
	} else {
		if utils.IsInnerIP(mhReq.Device.IP) {
			return 104021, 900001
		}
	}

	// 临时黑ip
	if mhReq.Device.IP == "*************" {
		return 104440, 900005
	}

	// 国外ip
	if strings.Contains(mhReq.Device.IP, ":") {
	} else {
		if mhReq.Device.IPCountry != "中国" {
			fmt.Println("wrong ip: " + mhReq.Device.IP + "_" + mhReq.Device.IPCountry + "_" + mhReq.Device.IPProvince + "_" + mhReq.App.AppID)
			return 104440, 900005
		} else if mhReq.Device.IPProvince == "香港" || mhReq.Device.IPProvince == "台湾省" {
			fmt.Println("wrong ip: " + mhReq.Device.IP + "_" + mhReq.Device.IPCountry + "_" + mhReq.Device.IPProvince + "_" + mhReq.App.AppID)
			return 104440, 900005
		}
	}

	if mhReq.Device.LBSIPCountry != "中国" {
		fmt.Println("wrong ip: " + mhReq.Device.IP + "_" + mhReq.Device.LBSIPCountry + "_" + mhReq.Device.LBSIPProvince + "_" + mhReq.App.AppID)
		return 104440, 900005
	} else if mhReq.Device.LBSIPProvince == "香港" || mhReq.Device.LBSIPProvince == "台湾" {
		fmt.Println("wrong ip: " + mhReq.Device.IP + "_" + mhReq.Device.LBSIPCountry + "_" + mhReq.Device.LBSIPProvince + "_" + mhReq.App.AppID)
		return 104440, 900005
	}
	return 0, 900000
}

func paramSDKRepair(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu) {
	if mhReq.Device.Os == "android" {
		// imei, android_id, oaid去空格
		mhReq.Device.Imei = strings.Replace(mhReq.Device.Imei, " ", "", -1)
		mhReq.Device.AndroidID = strings.Replace(mhReq.Device.AndroidID, " ", "", -1)
		mhReq.Device.Oaid = strings.Replace(mhReq.Device.Oaid, " ", "", -1)

		if strings.Contains(mhReq.Device.Oaid, "00000000") {
			mhReq.Device.Oaid = ""
		}
	}

	// 补dpi
	if mhReq.Device.DPI <= 0 {
		mhReq.Device.DPI = float32(GetDeviceDpi(c, mhReq.Device.Manufacturer, mhReq.Device.Model, mhReq.Device.Os, mhReq.Device.OsVersion))
	}

	if mhReq.Device.DPI > 120 {
		mhReq.Device.XDPI = mhReq.Device.DPI / 160
	} else {
		mhReq.Device.XDPI = mhReq.Device.DPI
	}

	// 不区分os，osv末尾去".0"，譬如"14.0.0" -> "14"
	mhReq.Device.OsVersion, _ = strings.CutSuffix(mhReq.Device.OsVersion, ".0.0")
	mhReq.Device.OsVersion, _ = strings.CutSuffix(mhReq.Device.OsVersion, ".0")

	// did_md5
	mhReq.Device.DIDMd5 = models.GetDeviceIDMd5Key(mhReq)

	// 通过ip补country, province, city
	if strings.Contains(mhReq.Device.IP, ":") {
	} else {
		region, err := utils.New(config.IPDBPath)
		defer region.Close()
		if err != nil {
		} else {
			// fmt.Println("kbg_ip, ip: " + mhReq.Device.IP)
			ip, _ := region.BtreeSearch(mhReq.Device.IP)
			// fmt.Println(ip.Country)
			// fmt.Println(ip.Province)
			// fmt.Println(ip, err)
			mhReq.Device.IPCountry = ip.Country
			mhReq.Device.IPProvince = ip.Province
			mhReq.Device.IPCity = ip.City
		}
	}

	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, mhReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		mhReq.Device.LBSIPCountry = ip2locationResp.Country
		mhReq.Device.LBSIPProvince = ip2locationResp.Region
		mhReq.Device.LBSIPCity = ip2locationResp.City
	}
}

// MhApiErrorRespMap ...
func MhApiErrorRespMap(errorCode int, msg string) *models.MHResp {
	respMap := models.MHResp{}
	respMap.Ret = errorCode
	respMap.Msg = msg

	return &respMap
}

// MhErrorRespMap ...
func MhErrorRespMap(errorCode int, msg string) *map[string]interface{} {
	respMap := map[string]interface{}{}
	respMap["ret"] = errorCode
	respMap["msg"] = msg

	return &respMap
}

// MhSDKErrorRespMap 下发ec, en, ew
func MhSDKErrorRespMap(errorCode int, msg string, localPos *models.LocalPosStu) *map[string]interface{} {
	respMap := map[string]interface{}{}
	respMap["ret"] = errorCode
	respMap["msg"] = msg

	// 支持clipboard
	if localPos.LocalAppSupportExtraClipboard == 1 {
		respMap["ec"] = localPos.LocalAppSupportExtraClipboard
	}
	if localPos.LocalAppSupportExtraClipboard == 0 {
		respMap["ec_event"] = 0
	} else {
		respMap["ec_event"] = localPos.LocalAppExtraClipboardType
	}
	// 支持notification
	if localPos.LocalAppSupportExtraNotification == 1 {
		respMap["en"] = localPos.LocalAppSupportExtraNotification
	}
	// 支持wakeup
	if localPos.LocalAppSupportExtraWakeUp == 1 {
		respMap["ew"] = localPos.LocalAppSupportExtraWakeUp
	}

	return &respMap
}

// MhUpErrorRespMap ...
func MhUpErrorRespMap(msg string, respExtra models.MHUpRespExtra) *models.MHUpResp {
	// respMap := map[string]interface{}{}
	// respMap["ret"] = respExtra.ExternalCode
	// respMap["msg"] = msg
	respMap := models.MHResp{}
	respMap.Ret = respExtra.ExternalCode
	respMap.Msg = msg

	resp := models.MHUpResp{}
	resp.RespData = &respMap
	resp.Extra = respExtra
	// resp.Extra.ExternalCode = respExtra.ExternalCode
	// resp.Extra.InternalCode = respExtra.InternalCode
	// resp.Extra.UpReqTime = respExtra.UpReqTime
	// resp.Extra.UpReqNum = respExtra.UpReqNum
	// resp.Extra.UpRespTime = respExtra.UpRespTime
	// resp.Extra.UpRespNum = respExtra.UpRespNum
	// resp.Extra.UpRespOKNum = respExtra.UpRespOKNum
	// resp.Extra.UpRespFailedNum = respExtra.UpRespFailedNum
	// resp.Extra.BitCode = respExtra.BitCode
	// resp.Extra.UpRespCode = respExtra.UpRespCode
	// resp.Extra.UpPriceReportWinNum = respExtra.UpPriceReportWinNum
	// resp.Extra.UpPriceReportFailedNum = respExtra.UpPriceReportFailedNum

	return &resp
}

// EncodeExpParams ...
func EncodeExpParams(mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, bigdataUID string,
	localFloorPrice int, localFinalPrice int, ecpm int, winURL string) string {
	bigdataParams := url.Values{}
	bigdataParams.Add("uid", bigdataUID)
	bigdataParams.Add("app_id", localPos.LocalAppID)
	bigdataParams.Add("pos_id", localPos.LocalPosID)
	bigdataParams.Add("p_app_id", platformPos.PlatformAppID)
	bigdataParams.Add("p_pos_id", platformPos.PlatformPosID)
	bigdataParams.Add("app_name", localPos.LocalAppName)
	bigdataParams.Add("app_bundle", localPos.LocalAppBundleID)
	bigdataParams.Add("os", mhReq.Device.Os)
	bigdataParams.Add("osv", mhReq.Device.OsVersion)
	if len(mhReq.Device.Imei) > 0 {
		bigdataParams.Add("imei", mhReq.Device.Imei)
	}
	if len(mhReq.Device.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", mhReq.Device.ImeiMd5)
	}
	if len(mhReq.Device.AndroidID) > 0 {
		bigdataParams.Add("android_id", mhReq.Device.AndroidID)
	}
	if len(mhReq.Device.AndroidIDMd5) > 0 {
		bigdataParams.Add("android_id_md5", mhReq.Device.AndroidIDMd5)
	}
	if len(mhReq.Device.Oaid) > 0 {
		bigdataParams.Add("oaid", mhReq.Device.Oaid)
	}
	if len(mhReq.Device.Idfa) > 0 {
		bigdataParams.Add("idfa", mhReq.Device.Idfa)
	}
	bigdataParams.Add("model", mhReq.Device.Model)
	bigdataParams.Add("manufacturer", mhReq.Device.Manufacturer)
	bigdataParams.Add("floor_price", utils.ConvertIntToString(localFloorPrice))
	bigdataParams.Add("final_price", utils.ConvertIntToString(localFinalPrice))
	bigdataParams.Add("ecpm", utils.ConvertIntToString(ecpm))
	if len(winURL) > 0 {
		bigdataParams.Add("win_url", winURL)
	}

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

// EncodeRtbParams ...
func EncodeRtbParams(localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, maplehazeAdId string) string {
	rtbParams := url.Values{}
	rtbParams.Add("app_id", localPos.LocalAppID)
	rtbParams.Add("pos_id", localPos.LocalPosID)
	rtbParams.Add("p_app_id", platformPos.PlatformAppID)
	rtbParams.Add("p_pos_id", platformPos.PlatformPosID)
	rtbParams.Add("adid", maplehazeAdId)

	encodeStr, _ := utils.EncodeString([]byte(rtbParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

// isMHRespVaild ...
func isMHRespVaild(c context.Context, localPosInfo *models.LocalPosStu, respData *map[string]interface{}) bool {
	tmpDataByte, _ := json.Marshal(respData)
	tmpMHResp := models.MHResp{}
	json.Unmarshal(tmpDataByte, &tmpMHResp)

	if tmpMHResp.Ret == 0 {
		// fmt.Println("kbg_debug_resp a:", tmpMHResp.Ret)
		// fmt.Println("kbg_debug_resp b:", tmpMHResp.Msg)
		// fmt.Println("kbg_debug_resp c:", tmpMHResp.Data)
		// fmt.Println("kbg_debug_resp d:", tmpMHResp.Data[localPosInfo.LocalPosID])
		// fmt.Println("kbg_debug_resp e:", tmpMHResp.Data[localPosInfo.LocalPosID].List)
		// fmt.Println("kbg_debug_resp len():", len(tmpDataByte))
		// fmt.Println("kbg_debug_resp len():", len(string(tmpDataByte)))
		if len(tmpMHResp.Data[localPosInfo.LocalPosID].List) == 0 {
			return false
		}
		if len(tmpDataByte) > 256*1024 {
			return false
		}
	}

	return true
}

type MaplehazeEcpmSort []models.MHRespDataItem

func (s MaplehazeEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s MaplehazeEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s MaplehazeEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Ecpm > s[j].Ecpm
}
