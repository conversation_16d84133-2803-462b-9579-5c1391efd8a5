package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

// GetFromMaplehazeRTB ...
func GetFromMaplehazeRTB(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from maplehaze")

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 3.3.5以下的版本，对优量汇rtb和api都直接屏蔽
	if localPos.LocalAppType == "1" {
		if IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 5) {
		} else {
			bigdataExtra.InternalCode = 900001
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["id"] = platformPos.PlatformAppID
	reqAppInfoMap["name"] = platformPos.PlatformAppName
	reqAppInfoMap["bundle"] = platformPos.PlatformAppBundle
	if platformPos.PlatformAppIsReplaceAppBundleID == 1 {
		reqAppInfoMap["bundle_union"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)
	}

	reqAppInfoMap["ver"] = platformPos.PlatformAppVersion

	// imp
	reqImpArray := []map[string]interface{}{}
	reqImpItemMap := map[string]interface{}{}
	reqImpItemMap["id"] = "1"
	reqImpItemMap["tagid"] = platformPos.PlatformPosID
	reqImpItemMap["bidfloor"] = localPosFloorPrice
	reqImpItemMap["bidtype"] = 0
	// <el-form-item prop="type" label="广告位类型">
	// 	<el-radio-group v-model="radio">
	// 	<el-radio label="1">Banner</el-radio>
	// 	<el-radio label="2">开屏</el-radio>
	// 	<el-radio label="3">插屏</el-radio>
	// 	<el-radio label="4">原生</el-radio>
	// 	<el-radio label="13">贴片</el-radio>
	// 	<el-radio label="9">全屏视频</el-radio>
	// 	<el-radio label="11">激励视频</el-radio>
	// 	<el-radio label="10">单Feed流</el-radio>
	// 	<el-radio label="12">双Feed流</el-radio>
	// 	<el-radio label="8">原生模版(待删除)</el-radio>
	// 	<el-radio label="7">原生2.0(待删除)</el-radio>
	// 	<el-radio label="5">原生视频(待删除)</el-radio>
	// 	</el-radio-group>
	// </el-form-item>
	// <el-form-item label="原生素材类型">
	// 	<el-radio-group v-model="material_type" :disabled="radio!=='4'">
	// 	<el-radio label="0">图片</el-radio>
	// 	<el-radio label="1">视频</el-radio>
	// 	<el-radio label="2">图片+视频</el-radio>
	// 	<el-radio label="3">模版</el-radio>
	// 	</el-radio-group>
	// </el-form-item>
	if platformPos.PlatformPosType == 2 {
		if platformPos.PlatformPosMaterialType == 0 || platformPos.PlatformPosMaterialType == 2 {
			reqImpItemNativeMap := map[string]interface{}{}
			reqImpItemNativeMap["w"] = platformPos.PlatformPosWidth
			reqImpItemNativeMap["h"] = platformPos.PlatformPosHeight
			// reqImpItemNativeMap["formid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")
			reqImpItemMap["native"] = reqImpItemNativeMap
		}

		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			reqImpItemVideoMap := map[string]interface{}{}
			reqImpItemVideoMap["w"] = platformPos.PlatformPosWidth
			reqImpItemVideoMap["h"] = platformPos.PlatformPosHeight
			reqImpItemVideoMap["minduration"] = 1
			reqImpItemVideoMap["maxduration"] = 5
			reqImpItemMap["video"] = reqImpItemVideoMap
		}

		reqImpItemMap["styleid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")

		reqImpItemMap["adtype"] = 20
	} else if platformPos.PlatformPosType == 4 {
		if platformPos.PlatformPosMaterialType == 0 {
			reqImpItemNativeMap := map[string]interface{}{}
			reqImpItemNativeMap["w"] = platformPos.PlatformPosWidth
			reqImpItemNativeMap["h"] = platformPos.PlatformPosHeight
			reqImpItemNativeMap["formid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")
			reqImpItemMap["native"] = reqImpItemNativeMap

			reqImpItemMap["adtype"] = 40
		} else if platformPos.PlatformPosMaterialType == 1 {
			reqImpItemNativeMap := map[string]interface{}{}
			reqImpItemNativeMap["w"] = platformPos.PlatformPosWidth
			reqImpItemNativeMap["h"] = platformPos.PlatformPosHeight
			reqImpItemNativeMap["formid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")
			reqImpItemMap["native"] = reqImpItemNativeMap

			reqImpItemVideoMap := map[string]interface{}{}
			reqImpItemVideoMap["w"] = platformPos.PlatformPosWidth
			reqImpItemVideoMap["h"] = platformPos.PlatformPosHeight
			reqImpItemVideoMap["minduration"] = 5
			reqImpItemVideoMap["maxduration"] = 30
			reqImpItemMap["video"] = reqImpItemVideoMap

			reqImpItemMap["adtype"] = 40
		} else if platformPos.PlatformPosMaterialType == 2 {
			reqImpItemNativeMap := map[string]interface{}{}
			reqImpItemNativeMap["w"] = platformPos.PlatformPosWidth
			reqImpItemNativeMap["h"] = platformPos.PlatformPosHeight
			reqImpItemNativeMap["formid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")
			reqImpItemMap["native"] = reqImpItemNativeMap

			reqImpItemVideoMap := map[string]interface{}{}
			reqImpItemVideoMap["w"] = platformPos.PlatformPosWidth
			reqImpItemVideoMap["h"] = platformPos.PlatformPosHeight
			reqImpItemVideoMap["minduration"] = 5
			reqImpItemVideoMap["maxduration"] = 30
			reqImpItemMap["video"] = reqImpItemVideoMap

			reqImpItemMap["adtype"] = 40
		}
	} else if platformPos.PlatformPosType == 11 {
		if platformPos.PlatformPosMaterialType == 0 || platformPos.PlatformPosMaterialType == 2 {
			reqImpItemNativeMap := map[string]interface{}{}
			reqImpItemNativeMap["w"] = platformPos.PlatformPosWidth
			reqImpItemNativeMap["h"] = platformPos.PlatformPosHeight
			// reqImpItemNativeMap["formid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")
			reqImpItemMap["native"] = reqImpItemNativeMap
		}

		if platformPos.PlatformPosMaterialType == 1 || platformPos.PlatformPosMaterialType == 2 {
			reqImpItemVideoMap := map[string]interface{}{}
			reqImpItemVideoMap["w"] = platformPos.PlatformPosWidth
			reqImpItemVideoMap["h"] = platformPos.PlatformPosHeight
			reqImpItemVideoMap["minduration"] = 5
			reqImpItemVideoMap["maxduration"] = 60
			reqImpItemMap["video"] = reqImpItemVideoMap
		}

		reqImpItemMap["styleid"] = strings.Split(platformPos.PlatformPosStyleIDs, ",")

		reqImpItemMap["adtype"] = 50
	}

	reqImpArray = append(reqImpArray, reqImpItemMap)

	// device
	reqDeviceInfoMap := map[string]interface{}{}
	if platformPos.PlatformAppIsReportUa == 1 {
		reqDeviceInfoMap["ua"] = destConfigUA
	}
	reqDeviceInfoMap["ip"] = mhReq.Device.IP

	// ori默认是1, 激励视频按照配置素材方向
	if platformPos.PlatformPosType == 11 {
		if platformPos.PlatformPosDirection == 0 {
			reqDeviceInfoMap["ori"] = 2
		} else if platformPos.PlatformPosDirection == 1 {
			reqDeviceInfoMap["ori"] = 1
		}
	}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false
	if mhReq.Device.Os == "android" {
		if mhReq.Device.Os == "android" {
			// fmt.Println("os version: " + mhReq.Device.OsVersion)
			osvMajor := 0
			if len(mhReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// fmt.Println(osvMajor)
			}
			if osvMajor < 10 {
				if len(mhReq.Device.Imei) > 0 {
					isAndroidDeviceOK = true
					reqDeviceInfoMap["didmd5"] = utils.GetMd5(mhReq.Device.Imei)
				} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
					isAndroidDeviceOK = true
					reqDeviceInfoMap["didmd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
				}
			} else {
				if len(mhReq.Device.Oaid) > 0 {
					isAndroidDeviceOK = true
					reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
				} else if len(mhReq.Device.OaidMd5) > 0 {
					isAndroidDeviceOK = true
					reqDeviceInfoMap["oaid_md5"] = mhReq.Device.OaidMd5
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from maplehaze error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
		reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer
		reqDeviceInfoMap["os"] = "android"
		reqDeviceInfoMap["model"] = mhReq.Device.Model
		reqDeviceInfoMap["osv"] = mhReq.Device.OsVersion
	} else if mhReq.Device.Os == "ios" {
		// fmt.Println("get from maplehaze ios")
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	reqDeviceInfoMap["sw"] = mhReq.Device.ScreenWidth
	reqDeviceInfoMap["sh"] = mhReq.Device.ScreenHeight
	reqDeviceInfoMap["js"] = 0
	// reqDeviceInfoMap["devicetype"] = 4

	if mhReq.Device.DeviceType == 1 {
		reqDeviceInfoMap["devicetype"] = 4
	} else if mhReq.Device.DeviceType == 2 {
		reqDeviceInfoMap["devicetype"] = 5
	} else {
		reqDeviceInfoMap["devicetype"] = 4
	}

	if mhReq.Network.ConnectType == 0 {
		reqDeviceInfoMap["ctype"] = 0
	} else if mhReq.Network.ConnectType == 1 {
		reqDeviceInfoMap["ctype"] = 1
	} else if mhReq.Network.ConnectType == 2 {
		reqDeviceInfoMap["ctype"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		reqDeviceInfoMap["ctype"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		reqDeviceInfoMap["ctype"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		reqDeviceInfoMap["ctype"] = 5
	} else {
		reqDeviceInfoMap["ctype"] = 0
	}

	if mhReq.Network.Carrier == 0 {
		reqDeviceInfoMap["carrier"] = 0
	} else if mhReq.Network.Carrier == 1 {
		reqDeviceInfoMap["carrier"] = 1
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceInfoMap["carrier"] = 2
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceInfoMap["carrier"] = 3
	} else {
		reqDeviceInfoMap["carrier"] = 0
	}

	if len(mhReq.Device.BootMark) > 0 {
		reqDeviceInfoMap["boot_mark"] = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		reqDeviceInfoMap["update_mark"] = mhReq.Device.UpdateMark
	}

	// 替换包
	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)

	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					delete(reqDeviceInfoMap, "didmd5")
					delete(reqDeviceInfoMap, "oaid")
					delete(reqDeviceInfoMap, "boot_mark")
					delete(reqDeviceInfoMap, "update_mark")

					reqDeviceInfoMap["osv"] = didRedisData.OsVersion
					reqDeviceInfoMap["model"] = didRedisData.Model
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer

					if didRedisData.Version == 1 {
						delete(reqDeviceInfoMap, "ctype")
						delete(reqDeviceInfoMap, "carrier")

						if didRedisData.ConnectType == 0 {
							reqDeviceInfoMap["ctype"] = 0
						} else if didRedisData.ConnectType == 1 {
							reqDeviceInfoMap["ctype"] = 1
						} else if didRedisData.ConnectType == 2 {
							reqDeviceInfoMap["ctype"] = 2
						} else if didRedisData.ConnectType == 3 {
							reqDeviceInfoMap["ctype"] = 3
						} else if didRedisData.ConnectType == 4 {
							reqDeviceInfoMap["ctype"] = 4
						} else if didRedisData.ConnectType == 7 {
							reqDeviceInfoMap["ctype"] = 5
						} else {
							reqDeviceInfoMap["ctype"] = 0
						}

						if didRedisData.Carrier == 0 {
							reqDeviceInfoMap["carrier"] = 0
						} else if didRedisData.Carrier == 1 {
							reqDeviceInfoMap["carrier"] = 1
						} else if didRedisData.Carrier == 2 {
							reqDeviceInfoMap["carrier"] = 2
						} else if didRedisData.Carrier == 3 {
							reqDeviceInfoMap["carrier"] = 3
						} else {
							reqDeviceInfoMap["carrier"] = 0
						}
					}

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["didmd5"] = utils.GetMd5(didRedisData.Imei)

						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["didmd5"] = didRedisData.ImeiMd5
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from maplehaze error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from maplehaze error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	postData := map[string]interface{}{
		"id":       bigdataUID,
		"imp":      reqImpArray,
		"app":      reqAppInfoMap,
		"device":   reqDeviceInfoMap,
		"ad_count": mhReq.Pos.AdCount,
	}

	if platformPos.PlatformPosIsTMax == 1 {
		postData["tmax"] = utils.ConvertStringToInt(platformPos.PlatformPosTMaxValue)
	} else {
		postData["tmax"] = 0
	}

	if platformPos.PlatformPosSupportQuerysPkgsType == 0 {
	} else if platformPos.PlatformPosSupportQuerysPkgsType == 1 || platformPos.PlatformPosSupportQuerysPkgsType == 2 {
		tmpSupportQuerys, tmpSupportPkgs := GetSupportQuerysPkgs(c, mhReq, localPos, platformPos)
		if len(tmpSupportQuerys) > 0 {
			postData["query"] = tmpSupportQuerys[rand.Intn(len(tmpSupportQuerys))]
		} else if len(tmpSupportPkgs) > 0 {
			postData["pkg_whitelist"] = tmpSupportPkgs
		} else {
			bigdataExtra.InternalCode = 900001
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if platformPos.PlatformPosSupportQuerysPkgsType == 3 {
		tmpSupportQuerys, tmpSupportPkgs := GetSupportQuerysPkgs(c, mhReq, localPos, platformPos)
		if len(tmpSupportQuerys) > 0 || len(tmpSupportPkgs) > 0 {
			if len(tmpSupportQuerys) > 0 {
				postData["query"] = tmpSupportQuerys[rand.Intn(len(tmpSupportQuerys))]
			}
			if len(tmpSupportPkgs) > 0 {
				postData["pkg_whitelist"] = tmpSupportPkgs
			}
		} else {
			bigdataExtra.InternalCode = 900001
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	// pkg_whitelist字段
	// if platformPos.PlatformPosIsSupportPkgs == 1 {
	// 	if platformPos.PlatformPosSupportPkgsType == 0 {
	// 		if len(mhReq.Pos.PkgWhitelist) > 0 {
	// 			postData["pkg_whitelist"] = mhReq.Pos.PkgWhitelist
	// 		}
	// 	} else if platformPos.PlatformPosSupportPkgsType == 1 {
	// 		if len(platformPos.PlatformPosSupportPkgs) > 0 {
	// 			postData["pkg_whitelist"] = strings.Split(platformPos.PlatformPosSupportPkgs, ",")
	// 		}
	// 	}
	// }
	if len(mhReq.Pos.PkgBlacklist) > 0 {
		postData["pkg_blacklist"] = mhReq.Pos.PkgBlacklist
	}
	if len(platformPos.PlatformPosReqPosTypes) > 0 {
		// postData["interact"] = strings.Split(platformPos.PlatformPosReqPosTypes, ",")
		var interactArray []int
		for _, item := range strings.Split(platformPos.PlatformPosReqPosTypes, ",") {
			interactArray = append(interactArray, utils.ConvertStringToInt(item))
		}
		postData["interact"] = interactArray
	}
	// query字段
	// if platformPos.PlatformPosIsSupportQuerys == 1 {
	// 	if platformPos.PlatformPosSupportQuerysType == 0 {
	// 		if len(mhReq.Pos.Query) > 0 {
	// 			postData["query"] = mhReq.Pos.Query
	// 		}
	// 	} else if platformPos.PlatformPosSupportQuerysType == 1 {
	// 		if len(platformPos.PlatformPosSupportQuerys) > 0 {
	// 			tmpArray := strings.Split(platformPos.PlatformPosSupportQuerys, ",")
	// 			postData["query"] = tmpArray[rand.Intn(len(tmpArray))]
	// 		}
	// 	}
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	jsonData, _ := json.Marshal(postData)
	// fmt.Println("maplehaze req: ", string(jsonData))

	// if platformPos.PlatformPosID == "storesearchbar001" {
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&storesearchbar001&req", string(jsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	// }

	// if localPos.LocalAppID == "10913" {
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&10913&req", string(jsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	// }

	if platformPos.PlatformPosID == "browserfeeds006" || platformPos.PlatformPosID == "browsersplash006" {
		tmpOaids := "074d005b6d8243fd8c895cc861bc8fae,0a1482c41a6fd80c70df7144523fa2f4,1edd04e5b14fd35dd7b79438df530f0d," +
			"2a34b102b6b7b62f89fb2ed11418b6ec,22a8a528d229e05ccfe75f3b44fe1d70,4757990cf344ba468a5e92ec212076ab," +
			"d30e838f437d531e72351b1b0cbe971d,d33a0599e179c77ece26b621975c6c8c,d3c36b6ab4434dccc36148d7f4ab17cf,a4be7a23c3355b92dbe1f968b07a1645," +
			"c3ca07c5-c2b9-4735-ad2d-7adaa939c2f3,1cc8f4d9dbe6695820e992b83ceaa1c5bdd30f00866a7f47267d6b0664d1576b,3fec237b725934da," +
			"d639d675-59aa-4db5-8eac-f44811ef6e64,7efefe2f-e975-062d-3fdf-f07bffef9991,daddad26810e9b85529d9bed4289b7d0568a6adfffa182fd6c2a1af7ea84ba4d"
		if isHaveReplace {
			if len(bigdataReplaceDIDData.Oaid) > 0 && strings.Contains(tmpOaids, bigdataReplaceDIDData.Oaid) {
				go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 1, string(jsonData), bigdataReplaceDIDData,
					mhReq)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 && strings.Contains(tmpOaids, mhReq.Device.Oaid) {
				go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 0, string(jsonData), bigdataReplaceDIDData,
					mhReq)
			}
		}

		bigdataExtra.InternalCode = 900009
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	} else {
		if mhReq.Device.Os == "android" {
			tmpOaids := "074d005b6d8243fd8c895cc861bc8fae,0a1482c41a6fd80c70df7144523fa2f4,1edd04e5b14fd35dd7b79438df530f0d," +
				"2a34b102b6b7b62f89fb2ed11418b6ec,22a8a528d229e05ccfe75f3b44fe1d70,4757990cf344ba468a5e92ec212076ab," +
				"d30e838f437d531e72351b1b0cbe971d,d33a0599e179c77ece26b621975c6c8c,d3c36b6ab4434dccc36148d7f4ab17cf,a4be7a23c3355b92dbe1f968b07a1645," +
				"c3ca07c5-c2b9-4735-ad2d-7adaa939c2f3,1cc8f4d9dbe6695820e992b83ceaa1c5bdd30f00866a7f47267d6b0664d1576b,3fec237b725934da," +
				"d639d675-59aa-4db5-8eac-f44811ef6e64,7efefe2f-e975-062d-3fdf-f07bffef9991,daddad26810e9b85529d9bed4289b7d0568a6adfffa182fd6c2a1af7ea84ba4d"
			if isHaveReplace {
				if len(bigdataReplaceDIDData.Oaid) > 0 && strings.Contains(tmpOaids, bigdataReplaceDIDData.Oaid) {
					go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 1, string(jsonData), bigdataReplaceDIDData,
						mhReq)
				}
			} else {
				if len(mhReq.Device.Oaid) > 0 && strings.Contains(tmpOaids, mhReq.Device.Oaid) {
					go models.BigDataHoloDebugAnticheat(bigdataUID, localPos, platformPos, 0, string(jsonData), bigdataReplaceDIDData,
						mhReq)
				}
			}
		}
	}

	if platformPos.PlatformPosID == "84110107601001" {
		go models.BigDataHoloDebugJson2(bigdataUID+"&maplehazertb&replace", string(jsonData), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
		bigdataExtra.InternalCode = 900009
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode == 204 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// debug
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&maplehazertb", string(bodyContent), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	maplehazeRespStu := MaplehazeRtbRespStu{}
	json.Unmarshal([]byte(bodyContent), &maplehazeRespStu)

	if len(maplehazeRespStu.SeatBids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(maplehazeRespStu.SeatBids[0].Bids) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	// debug
	// if localPos.LocalPosID == "60216" && platformPos.PlatformPosID == "cocloudfeeds0v0v817" {
	// 	go models.BigDataHoloDebugJson(bigdataUID, localPos, platformPos, string(bodyContent))
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	// maplehazeRespDataList := maplehazeRespStu.SeatBids[0].Bids
	// if len(gdtRespDataListItemStu) > 1 {
	// 	sort.Sort(GdtEcpmSort(gdtRespDataListItemStu))
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for _, maplehazeInfoItem := range maplehazeRespStu.SeatBids[0].Bids {
		adInfoItem := maplehazeInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		maplehazeEcpm := int(adInfoItem.Price)

		respTmpPrice = respTmpPrice + maplehazeEcpm

		// if platformPos.PlatformAppID == "841101817" && localPos.LocalPosID == "60128" {
		// 	go models.BigDataHoloDebugJson(bigdataUID, localPos, platformPos, utils.ConvertIntToString(maplehazeEcpm))
		// }
		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if maplehazeEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			maplehazeEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > maplehazeEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(maplehazeEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceHexValue := "${AUCTION_PRICE}"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("maplehaze price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 100
			if platformPos.PlatformAppReportWinType == 0 {
				randPRValue = 100
			} else if platformPos.PlatformAppReportWinType == 1 {
				tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
				tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
				if tmp1 <= tmp2 {
					randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
				}
			}

			macroPrice := utils.ConvertIntToString(int(maplehazeEcpm * randPRValue / 100))
			maplehazekey := platformPos.PlatformAppPriceEncrypt
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, maplehazekey)
			encodePriceHexValue = string(utils.HexEncode(string(encodePrice)))
		}

		// win notice url
		winNoticeURL := adInfoItem.NURL

		if len(winNoticeURL) > 0 {
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_ID}", bigdataUID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_ID__", bigdataUID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_BID_ID}", adInfoItem.ID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_BID_ID__", adInfoItem.ID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_IMP_ID}", adInfoItem.ImpID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_SEAT_ID}", maplehazeRespStu.SeatBids[0].Seat, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_SEAT_ID__", maplehazeRespStu.SeatBids[0].Seat, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_CRID}", adInfoItem.CRID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_CRID__", adInfoItem.CRID, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "${AUCTION_PRICE}", encodePriceHexValue, -1)
			winNoticeURL = strings.Replace(winNoticeURL, "__AUCTION_PRICE__", encodePriceHexValue, -1)

			// if platformPos.PlatformAppID == "841101004" {
			// 	go models.BigDataHoloDebugJson2(bigdataUID+"&841101004&fill", winNoticeURL, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
			// }
		}

		maplehazeAdm := MaplehazeAdmStu{}
		json.Unmarshal([]byte(adInfoItem.Adm), &maplehazeAdm)

		// debug
		// if len(maplehazeAdm.WakeAppName) > 0 || len(maplehazeAdm.WakeIconURL) > 0 {
		// 	go models.BigDataHoloDebugJson(bigdataUID, localPos, platformPos, string(bodyContent))
		// }

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(maplehazeAdm.Title) > 0 {
			respListItemMap["title"] = maplehazeAdm.Title
		}

		// description
		if len(maplehazeAdm.Description) > 0 {
			respListItemMap["description"] = maplehazeAdm.Description
		}

		// crid
		respListItemMap["crid"] = adInfoItem.CRID

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(maplehazeAdm.Deeplink) > 0 {
			respListItemMap["deep_link"] = maplehazeAdm.Deeplink

			// deeplink ok track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(maplehazeAdm.IconURL) > 0 {
			respListItemMap["icon_url"] = maplehazeAdm.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(maplehazeAdm.Deeplink)
		}

		// 微信小程序类型icon_url用 wake_icon_url
		if maplehazeAdm.Interact == 3 {
			if len(maplehazeAdm.WakeIconURL) > 0 {
				respListItemMap["icon_url"] = maplehazeAdm.WakeIconURL
			}
		}

		isVideo := false

		// 视频
		if maplehazeAdm.CType == 2 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if maplehazeAdm.Video.VideoDuration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, maplehazeAdm.Video.VideoDuration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
				respListVideoItemMap["duration"] = maplehazeAdm.Video.VideoDuration * 1000
			}
			if maplehazeAdm.Video.VideoWidth > 0 {
				respListVideoItemMap["width"] = maplehazeAdm.Video.VideoWidth
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if maplehazeAdm.Video.VideoHeight > 0 {
				respListVideoItemMap["height"] = maplehazeAdm.Video.VideoHeight
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}
			if maplehazeAdm.Video.VideoWidth > 0 && maplehazeAdm.Video.VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, maplehazeAdm.Video.VideoWidth, maplehazeAdm.Video.VideoHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}
			respListVideoItemMap["video_url"] = maplehazeAdm.Video.VideoURL

			// cover_url
			if len(maplehazeAdm.Video.CoverURL) > 0 {
				respListVideoItemMap["cover_url"] = maplehazeAdm.Video.CoverURL
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if maplehazeAdm.CType == 1 {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(maplehazeAdm.Image[0].ImageURL) > 0 {
				respListImageItemMap["url"] = maplehazeAdm.Image[0].ImageURL
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if maplehazeAdm.Image[0].ImageWidth > 0 {
				respListImageItemMap["width"] = maplehazeAdm.Image[0].ImageWidth
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if maplehazeAdm.Image[0].ImageHeight > 0 {
				respListImageItemMap["height"] = maplehazeAdm.Image[0].ImageHeight
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if maplehazeAdm.Image[0].ImageWidth > 0 && maplehazeAdm.Image[0].ImageHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, maplehazeAdm.Image[0].ImageWidth, maplehazeAdm.Image[0].ImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		} else {
			continue
		}

		// interact_type ad_url
		if maplehazeAdm.Interact == 1 || maplehazeAdm.Interact == 3 || maplehazeAdm.Interact == 4 || maplehazeAdm.Interact == 5 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = maplehazeAdm.LandingPangeURL
			respListItemMap["landpage_url"] = maplehazeAdm.LandingPangeURL
			if len(maplehazeAdm.LandingPangeURL) == 0 {
				continue
			}
		} else if maplehazeAdm.Interact == 2 || maplehazeAdm.Interact == 6 || maplehazeAdm.Interact == 7 {
			if len(maplehazeAdm.DownloadURL) == 0 {
				continue
			}

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = maplehazeAdm.DownloadURL
			respListItemMap["download_url"] = maplehazeAdm.DownloadURL

			if len(maplehazeAdm.LandingPangeURL) > 0 {
				respListItemMap["landpage_url"] = maplehazeAdm.LandingPangeURL
			}
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		// 2025.03.28 10913 10071 interact_type - 1
		if localPos.LocalAppID == "10913" || localPos.LocalAppID == "10071" {
			respListItemMap["interact_type"] = maplehazeAdm.Interact - 1
			// 2025.04.22 枫岚rtb给联想应用商店的下游广告类型返回是，上游返回2给下游返回6，即2（上）=6（下）
			if maplehazeAdm.Interact == 2 {
				respListItemMap["interact_type"] = 6
			}
		}

		if localPos.LocalAppIsLenovoSecureDownload == 1 {
			if maplehazeAdm.Interact == 2 {
				// 获取lenovo厂商
				manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"lenovo")

				isInManufactureConfig := false
				for _, manufactureConfigItem := range manufactureConfigArray {
					if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
						isInManufactureConfig = true
					}
				}
				if isInManufactureConfig {
					if len(maplehazeAdm.AppName1) > 0 {
						respListItemMap["deep_link"] = "leapp://ptn/url_dl.do?appdlinfo=" +
							url.QueryEscape(
								"downurl="+maplehazeAdm.DownloadURL+
									"&pn="+maplehazeAdm.PackageName+
									"&vc="+maplehazeAdm.AppVersion+
									"&size="+utils.ConvertIntToString(int(maplehazeAdm.AppSize))+
									"&name="+maplehazeAdm.AppName1+
									"&icon="+maplehazeAdm.IconURL+
									"&replaced=1"+
									"&extendinfo="+maplehazeAdm.IconURL)
					} else {
						respListItemMap["deep_link"] = "leapp://ptn/url_dl.do?appdlinfo=" +
							url.QueryEscape(
								"downurl="+maplehazeAdm.DownloadURL+
									"&pn="+maplehazeAdm.PackageName+
									"&vc="+maplehazeAdm.AppVersion+
									"&size="+utils.ConvertIntToString(int(maplehazeAdm.AppSize))+
									"&name="+maplehazeAdm.AppName+
									"&icon="+maplehazeAdm.IconURL+
									"&replaced=1"+
									"&extendinfo="+maplehazeAdm.IconURL)
					}
				}
			}
		}

		if len(maplehazeAdm.AppName) > 0 {
			respListItemMap["app_name"] = maplehazeAdm.AppName
		}
		if len(maplehazeAdm.AppName1) > 0 {
			respListItemMap["app_name"] = maplehazeAdm.AppName1
		}
		// 微信小程序类型app_name用 wake_app_name
		if maplehazeAdm.Interact == 3 {
			if len(maplehazeAdm.WakeAppName) > 0 {
				respListItemMap["app_name"] = maplehazeAdm.WakeAppName
			}
		}
		if len(maplehazeAdm.PackageName) > 0 {
			respListItemMap["package_name"] = maplehazeAdm.PackageName
		}
		if strings.Contains(maplehazeAdm.Deeplink, "weixin://") {
			respListItemMap["package_name"] = "com.tencent.mm"
		}
		if len(maplehazeAdm.Publisher) > 0 {
			respListItemMap["publisher"] = maplehazeAdm.Publisher
		}
		if len(maplehazeAdm.AppVersion) > 0 {
			respListItemMap["app_version"] = maplehazeAdm.AppVersion
		}
		if len(maplehazeAdm.VersionCode) > 0 {
			respListItemMap["app_version_code"] = maplehazeAdm.VersionCode
		}
		if len(maplehazeAdm.PrivacyLink) > 0 {
			respListItemMap["privacy_url"] = maplehazeAdm.PrivacyLink
		}
		if len(maplehazeAdm.PermissionURL) > 0 {
			respListItemMap["permission_url"] = maplehazeAdm.PermissionURL
		}
		if maplehazeAdm.AppSize > 0 {
			respListItemMap["package_size"] = maplehazeAdm.AppSize
		}

		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, maplehazeAdm.AppName, maplehazeAdm.PackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		if len(maplehazeAdm.AppInfoURL) > 0 {
			respListItemMap["appinfo_url"] = maplehazeAdm.AppInfoURL
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				if IsClikPointLibInWhite(c, localPos, platformPos) {
				} else {
					redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
					// fmt.Println("kbg_debug_maplehaze:", redisDownX, redisDownY, redisUpX, redisUpY)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900101
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {

						tmpDownX = utils.ConvertIntToString(redisDownX)
						tmpDownY = utils.ConvertIntToString(redisDownY)
						tmpUpX = utils.ConvertIntToString(redisUpX)
						tmpUpY = utils.ConvertIntToString(redisUpY)

						if redisClickXYType == "0" { // 物理像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 0
						} else if redisClickXYType == "1" { // 逻辑像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 1
						}

						go models.BigDataHoloDebugJson2(bigdataUID+"&maplehazertb&click", tmpDownX+","+tmpDownY+","+tmpUpX+","+tmpUpY, localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, maplehazeEcpm, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)
		if platformPos.PlatformAppIsReportWin == 1 && platformPos.PlatformAppReportWinConfig == 0 {
			if len(winNoticeURL) > 0 {
				var tmpWinNoticeURLs []string
				tmpWinNoticeURLs = append(tmpWinNoticeURLs, winNoticeURL)
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}
		}

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.ImpTrackers {
			impItem := trackInfoItem
			impItem = strings.Replace(impItem, "${AUCTION_ID}", bigdataUID, -1)
			impItem = strings.Replace(impItem, "__AUCTION_ID__", bigdataUID, -1)
			impItem = strings.Replace(impItem, "${AUCTION_BID_ID}", adInfoItem.ID, -1)
			impItem = strings.Replace(impItem, "__AUCTION_BID_ID__", adInfoItem.ID, -1)
			impItem = strings.Replace(impItem, "${AUCTION_IMP_ID}", adInfoItem.ImpID, -1)
			impItem = strings.Replace(impItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
			impItem = strings.Replace(impItem, "${AUCTION_SEAT_ID}", maplehazeRespStu.SeatBids[0].Seat, -1)
			impItem = strings.Replace(impItem, "__AUCTION_SEAT_ID__", maplehazeRespStu.SeatBids[0].Seat, -1)
			impItem = strings.Replace(impItem, "${AUCTION_CRID}", adInfoItem.CRID, -1)
			impItem = strings.Replace(impItem, "__AUCTION_CRID__", adInfoItem.CRID, -1)
			impItem = strings.Replace(impItem, "${AUCTION_PRICE}", encodePriceHexValue, -1)
			impItem = strings.Replace(impItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}
		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.ClkTrackers {
			clkItem := trackInfoItem
			clkItem = strings.Replace(clkItem, "${AUCTION_ID}", bigdataUID, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_ID__", bigdataUID, -1)
			clkItem = strings.Replace(clkItem, "${AUCTION_BID_ID}", adInfoItem.ID, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_BID_ID__", adInfoItem.ID, -1)
			clkItem = strings.Replace(clkItem, "${AUCTION_IMP_ID}", adInfoItem.ImpID, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
			clkItem = strings.Replace(clkItem, "${AUCTION_SEAT_ID}", maplehazeRespStu.SeatBids[0].Seat, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_SEAT_ID__", maplehazeRespStu.SeatBids[0].Seat, -1)
			clkItem = strings.Replace(clkItem, "${AUCTION_CRID}", adInfoItem.CRID, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_CRID__", adInfoItem.CRID, -1)
			clkItem = strings.Replace(clkItem, "${AUCTION_PRICE}", encodePriceHexValue, -1)
			clkItem = strings.Replace(clkItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			clkItem = strings.Replace(clkItem, "__X_MAX_ACC__", "", -1)
			clkItem = strings.Replace(clkItem, "__Y_MAX_ACC__", "", -1)
			clkItem = strings.Replace(clkItem, "__Z_MAX_ACC__", "", -1)
			clkItem = strings.Replace(clkItem, "__TURN_X__", "", -1)
			clkItem = strings.Replace(clkItem, "__TURN_Y__", "", -1)
			clkItem = strings.Replace(clkItem, "__TURN_Z__", "", -1)
			clkItem = strings.Replace(clkItem, "__TURN_TIME__", "", -1)

			clkItem = strings.Replace(clkItem, "__REQ_WIDTH__", "", -1)
			clkItem = strings.Replace(clkItem, "__REQ_HEIGHT__", "", -1)
			// clkItem = strings.Replace(clkItem, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			// clkItem = strings.Replace(clkItem, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
				if mhReq.Device.XDPI > 0 {
					tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
					tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

					clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
					clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
				} else {
					clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
					clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
				}
			} else {
				clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
				clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
			}
			clkItem = strings.Replace(clkItem, "__DOWN_X__", tmpDownX, -1)
			clkItem = strings.Replace(clkItem, "__DOWN_Y__", tmpDownY, -1)
			clkItem = strings.Replace(clkItem, "__UP_X__", tmpUpX, -1)
			clkItem = strings.Replace(clkItem, "__UP_Y__", tmpUpY, -1)

			if platformPos.PlatformPosIsReportSLD == 1 {
				clkItem = strings.Replace(clkItem, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
			}

			if localPos.LocalAppClickPointType == 0 {
				clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "", -1)
			} else if localPos.LocalAppClickPointType == 1 {
				clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "0", -1)
			} else if localPos.LocalAppClickPointType == 2 {
				clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "1", -1)
			}

			if platformPos.PlatformPosIsReplaceXY == 2 {
			} else {
				respListItemClkArray = append(respListItemClkArray, clkItem)
			}
		}

		if platformPos.PlatformPosIsReplaceXY == 2 {
			var tmpClkArray []string

			for _, trackInfoItem := range adInfoItem.ClkTrackers {
				clkItem := trackInfoItem
				clkItem = strings.Replace(clkItem, "${AUCTION_ID}", bigdataUID, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_ID__", bigdataUID, -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_BID_ID}", adInfoItem.ID, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_BID_ID__", adInfoItem.ID, -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_IMP_ID}", adInfoItem.ImpID, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_IMP_ID__", adInfoItem.ImpID, -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_SEAT_ID}", maplehazeRespStu.SeatBids[0].Seat, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_SEAT_ID__", maplehazeRespStu.SeatBids[0].Seat, -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_CRID}", adInfoItem.CRID, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_CRID__", adInfoItem.CRID, -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_PRICE}", encodePriceHexValue, -1)
				clkItem = strings.Replace(clkItem, "__AUCTION_PRICE__", encodePriceHexValue, -1)

				clkItem = strings.Replace(clkItem, "__X_MAX_ACC__", "", -1)
				clkItem = strings.Replace(clkItem, "__Y_MAX_ACC__", "", -1)
				clkItem = strings.Replace(clkItem, "__Z_MAX_ACC__", "", -1)
				clkItem = strings.Replace(clkItem, "__TURN_X__", "", -1)
				clkItem = strings.Replace(clkItem, "__TURN_Y__", "", -1)
				clkItem = strings.Replace(clkItem, "__TURN_Z__", "", -1)
				clkItem = strings.Replace(clkItem, "__TURN_TIME__", "", -1)

				clkItem = strings.Replace(clkItem, "__REQ_WIDTH__", "", -1)
				clkItem = strings.Replace(clkItem, "__REQ_HEIGHT__", "", -1)
				// clkItem = strings.Replace(clkItem, "__REQ_WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
				// clkItem = strings.Replace(clkItem, "__REQ_HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
				if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
					if mhReq.Device.XDPI > 0 {
						tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
						tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

						clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
						clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
					} else {
						clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
				} else {
					clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
					clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
				}

				if platformPos.PlatformPosIsReportSLD == 1 {
					clkItem = strings.Replace(clkItem, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
				}

				if localPos.LocalAppClickPointType == 0 {
					clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "", -1)
				} else if localPos.LocalAppClickPointType == 1 {
					clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "0", -1)
				} else if localPos.LocalAppClickPointType == 2 {
					clkItem = strings.Replace(clkItem, "__PIXEL_TYPE__", "1", -1)
				}

				tmpClkArray = append(tmpClkArray, clkItem)
			}

			if len(tmpClkArray) > 0 {
				tmpClkArrayJSON, _ := json.Marshal(tmpClkArray)
				tmpEncryptClkArray := utils.AesECBEncrypt([]byte(tmpClkArrayJSON), []byte(config.EncryptKEY))
				mhClkParams.Add("mh_link", string(base64.StdEncoding.EncodeToString(tmpEncryptClkArray)))

				// go models.BigDataHoloDebugJson2(bigdataUID+"&mh_rtb_a", string(tmpClkArrayJSON), "a", "a", platformPos.PlatformAppID, platformPos.PlatformPosID)
			}
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		// 上报竞价成功
		if platformPos.PlatformAppIsReportWin == 1 && platformPos.PlatformAppReportWinConfig == 1 {
			if len(winNoticeURL) > 0 {
				var tmpWinNoticeURLs []string
				tmpWinNoticeURLs = append(tmpWinNoticeURLs, winNoticeURL)
				respListItemMap["demand_win_notice_urls"] = tmpWinNoticeURLs

				// if platformPos.PlatformAppID == "841101027" {
				// 	go models.BigDataHoloDebugJson2(bigdataUID+"&debug_demand_win", strings.Join(tmpWinNoticeURLs, "___"), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
				// }
			}
		}

		respListItemMap["p_ecpm"] = maplehazeEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	// if platformPos.PlatformPosID == "84110101702001" {
	// 	jsonData, _ := json.Marshal(respMap)
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&84110101702001", string(jsonData), string(bodyContent), "", "", "84110101702001")

	// 	bigdataExtra.InternalCode = respTmpInternalCode
	// 	bigdataExtra.ExternalCode = 102006
	// 	bigdataExtra.UpRespNum = respTmpRespAllNum
	// 	bigdataExtra.UpRespFailedNum = respTmpRespAllNum
	// 	return MhUpErrorRespMap("", bigdataExtra)
	// }

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// maplehaze resp
	respMaplehaze := models.MHUpResp{}
	respMaplehaze.RespData = &mhResp
	respMaplehaze.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respMaplehaze
}

// MaplehazeRtbRespStu ...
type MaplehazeRtbRespStu struct {
	ID       string                        `json:"id"`
	BIDID    string                        `json:"bidid"`
	SeatBids []MaplehazeRtbRespSeatBidsStu `json:"seatbid"`
}

type MaplehazeRtbRespSeatBidsStu struct {
	Bids []MaplehazeRespSeatBidItemStu `json:"bid"`
	Seat string                        `json:"seat"`
}

type MaplehazeRespSeatBidItemStu struct {
	ID          string   `json:"id"`
	ImpID       string   `json:"impid"`
	Price       float32  `json:"price"`
	DealID      string   `json:"dealid"`
	CRID        string   `json:"crid"`
	NURL        string   `json:"nurl"` // 竞价成功通知url
	Adm         string   `json:"adm"`
	ImpTrackers []string `json:"imp_trackers"`   // 曝光上报地址
	ClkTrackers []string `json:"click_trackers"` // 点击上报地址
}

// MaplehazeAdmStu ...
type MaplehazeAdmStu struct {
	Title           string                 `json:"title"`
	Description     string                 `json:"desc"`
	Image           []MaplehazeAdmImageStu `json:"img"`
	IconURL         string                 `json:"icon"`
	Video           MaplehazeAdmVideoStu   `json:"video"`
	LandingPangeURL string                 `json:"target"`       // 落地页
	DownloadURL     string                 `json:"download_url"` // 下载链接
	Interact        int                    `json:"interact"`     // 推广类型: 1 应用内或浏览器打开; 2 直接下载; 3 微信小程序
	CType           int                    `json:"ctype"`        // 创意类型: 1 图片; 2 视频
	Deeplink        string                 `json:"dplink"`
	PackageName     string                 `json:"packageName"`
	AppName         string                 `json:"app_name"`
	AppName1        string                 `json:"appName"`
	Publisher       string                 `json:"publisher"`
	AppVersion      string                 `json:"app_version"`
	AppSize         int                    `json:"package_size"`
	PermissionURL   string                 `json:"permission_url"`
	PrivacyLink     string                 `json:"privacy_url"`
	AppInfoURL      string                 `json:"appinfo_url"`
	VersionCode     string                 `json:"version_code"`
	WakeIconURL     string                 `json:"wake_icon_url"`
	WakeAppName     string                 `json:"wake_app_name"`
}

// MaplehazeAdmImageStu ...
type MaplehazeAdmImageStu struct {
	ImageURL    string `json:"url"`
	ImageWidth  int    `json:"w"`
	ImageHeight int    `json:"h"`
}

// MaplehazeAdmVideoStu ...
type MaplehazeAdmVideoStu struct {
	VideoURL      string `json:"url"`
	VideoWidth    int    `json:"w"`
	VideoHeight   int    `json:"h"`
	VideoDuration int    `json:"duration"`
	CoverURL      string `json:"cover_img"`
}
