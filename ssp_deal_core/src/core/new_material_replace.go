package core

import (
	"context"
	"encoding/json"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/crid_generate/utilities"
)

// ReplaceType defines the type of element being replaced.

func NewMaterialReplace(ctx context.Context, bigdataUID string, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, item *models.MHRespDataItem, replaceData *models.MHRespDataItem) {
	var replaceResults []*models.ReplaceResult

	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}
	appKey := "go_ssp_material_replace_config_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_material_replace_config_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)

	if len(appValue) == 0 && len(posValue) == 0 {
		return
	}

	var materialReplace []models.NewMaterialReplaceBigCache
	if len(appValue) > 0 {
		_ = json.Unmarshal(appValue, &materialReplace)
	}

	if len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &materialReplace)
	}

	randNum := rand.Intn(100)
	for _, materialItem := range materialReplace {
		platformIds := materialItem.PlatformIds

		if len(platformIds) > 0 {
			if !strings.Contains(platformIds, tmpId) {
				continue
			}
		}

		if randNum > materialItem.Chance {
			continue
		}
		var (
			adUrlArray       []string
			replaceKeyArray  []string
			materialUrlArray []string
		)

		if len(materialItem.ReplaceKey) > 0 {
			replaceKeyArray = strings.Split(materialItem.ReplaceKey, ",")
		}

		if len(materialItem.AdUrl) > 0 {
			adUrlArray = strings.Split(materialItem.AdUrl, ",")
		}

		if len(materialItem.MaterialUrl) > 0 {
			materialUrlArray = strings.Split(materialItem.MaterialUrl, ",")
		}

		params := url.Values{}
		params.Set("date", time.Now().Format("20060102"))

		s := utils.Base64URLEncode([]byte(params.Encode()))

		// 配置定义
		var (
			titleArray []string
			descArray  []string
		)
		titleArray = materialItem.Title
		descArray = materialItem.Desc
		group := random(materialItem.Group, int64(len(materialItem.Group)))

		flag := 1
		titleFlag := "0" // 标题
		descFlag := "0"  // 描述
		imageFlag := "0" // 图片
		videoFlag := "0" // 视频链接
		iconFlag := "0"  // icon
		coverFlag := "0" // 视频封面
		FillVacanciesFlag := "0000"

		isReplace := titleFlag + descFlag + iconFlag + imageFlag + coverFlag + videoFlag + FillVacanciesFlag
		item.IsReplace = isReplace

		if len(replaceKeyArray) > 0 {
			for _, replaceKeyItem := range replaceKeyArray {
				if len(item.Title) > 0 {
					if strings.Contains(item.Title, replaceKeyItem) {
						flag = 2
						break
					}
				}
				if len(item.Description) > 0 {
					if strings.Contains(item.Description, replaceKeyItem) {
						flag = 2
						break
					}
				}
			}
		}

		if len(adUrlArray) > 0 && flag == 1 {
			for _, adUrlItem := range adUrlArray {
				if len(item.LandpageURL) > 0 {
					if strings.Contains(item.LandpageURL, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.DownloadURL) > 0 {
					if strings.Contains(item.DownloadURL, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.DeepLink) > 0 {
					if strings.Contains(item.DeepLink, adUrlItem) {
						flag = 2
						break
					}
				}
				if len(item.AdURL) > 0 {
					if strings.Contains(item.AdURL, adUrlItem) {
						flag = 2
						break
					}
				}
			}
		}

		if len(materialUrlArray) > 0 && flag == 1 {
			for _, materialUrlItem := range materialUrlArray {
				if len(item.Image) > 0 {
					if strings.Contains(item.Image[0].URL, materialUrlItem) {
						flag = 2
						break
					}
				}
				if item.Video != nil && len(item.Video.VideoURL) > 0 {
					if strings.Contains(item.Video.VideoURL, materialUrlItem) {
						flag = 2
						break
					}
					if strings.Contains(item.Video.CoverURL, materialUrlItem) {
						flag = 2
						break
					}
				}

				if len(item.IconURL) > 0 {
					if strings.Contains(item.IconURL, materialUrlItem) {
						flag = 2
						break
					}
				}
			}
		}

		if materialItem.UnconditionalSwitch == 2 {
			flag = 2
		}

		if flag == 2 {
			if len(titleArray) > 0 {
				titleFlag = "1"
				originalValue := item.Title
				newValue := random(titleArray, 0)[0]
				item.Title = newValue
				replaceResults = append(replaceResults, &models.ReplaceResult{
					Type:          models.ReplaceTypeTitle,
					OriginalValue: originalValue,
					ReplaceValue:  newValue,
					RuleID:        materialItem.MaterialReplaceRuleID,
					RuleName:      materialItem.MaterialReplaceRuleName,
				})
			}
			if len(descArray) > 0 {
				descFlag = "1"
				originalValue := item.Description
				newValue := random(descArray, 0)[0]
				item.Description = newValue
				replaceResults = append(replaceResults, &models.ReplaceResult{
					Type:          models.ReplaceTypeDescription,
					OriginalValue: originalValue,
					ReplaceValue:  newValue,
					RuleID:        materialItem.MaterialReplaceRuleID,
					RuleName:      materialItem.MaterialReplaceRuleName,
				})
			}

			for _, groupItem := range group {
				switch groupItem.ReplaceType {
				case "3", "4":
					iconFlag = "1"
					originalValue := item.IconURL
					newValue := groupItem.ReplaceValue
					if groupItem.DynamicMaterials == 2 {
						if strings.Contains(newValue, "?") {
							newValue = newValue + "&" + s
						} else {
							newValue = newValue + "?" + s
						}
					}
					item.IconURL = newValue
					replaceResults = append(replaceResults, &models.ReplaceResult{
						Type:          models.ReplaceTypeIcon,
						OriginalValue: originalValue,
						ReplaceValue:  newValue,
						RuleID:        materialItem.MaterialReplaceRuleID,
						RuleName:      materialItem.MaterialReplaceRuleName,
					})
					break
				}
			}
			switch item.CrtType {
			case 11:
				if item.Image != nil {
					var originalImage []models.Image
					for _, img := range item.Image {
						var mhRespImage models.Image
						mhRespImage.URL = img.URL
						mhRespImage.Width = img.Width
						mhRespImage.Height = img.Height
						originalImage = append(originalImage, mhRespImage)
					}

					if materialItem.Type == 2 {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "5", "6":
								if len(groupItem.ReplaceValue) > 0 {
									var mhRespImageArray []models.MHRespImage
									var mhRespImage models.MHRespImage
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(groupItem.ReplaceValue, "?") {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "&" + s
										} else {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "?" + s
										}
									}
									mhRespImage.URL = groupItem.ReplaceValue
									mhRespImage.Width = groupItem.Width
									mhRespImage.Height = groupItem.Height
									mhRespImageArray = append(mhRespImageArray, mhRespImage)
									imageFlag = "1"
									item.Image = mhRespImageArray
									break
								}
							}
						}
					} else {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "5", "6":
								if len(groupItem.ReplaceValue) > 0 {
									var mhRespImageArray []models.MHRespImage

									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(groupItem.ReplaceValue, "?") {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "&" + s
										} else {
											groupItem.ReplaceValue = groupItem.ReplaceValue + "?" + s
										}
									}
									for _, img := range item.Image {
										var mhRespImage models.MHRespImage
										mhRespImage.URL = img.URL
										if img.Width == img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width == groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
										if img.Width > img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
										if img.Width < img.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
											mhRespImage.URL = groupItem.ReplaceValue
											mhRespImage.Width = groupItem.Width
											mhRespImage.Height = groupItem.Height
											mhRespImageArray = append(mhRespImageArray, mhRespImage)
										}
									}

									if len(mhRespImageArray) > 0 {
										imageFlag = "1"
										item.Image = mhRespImageArray
										break
									}
								}
							}
						}
					}
					if imageFlag == "1" {
						replaceResults = append(replaceResults, &models.ReplaceResult{
							Type:        models.ReplaceTypeImage,
							RuleID:      materialItem.MaterialReplaceRuleID,
							RuleName:    materialItem.MaterialReplaceRuleName,
							OriginalImg: originalImage,
						})
					}
				}
			case 20:
				if item.Video != nil {
					originalVideo := models.Video{
						Duration: item.Video.Duration,
						Width:    item.Video.Width,
						Height:   item.Video.Height,
						VideoURL: item.Video.VideoURL,
						CoverURL: item.Video.CoverURL,
					}
					if materialItem.Type == 2 {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "7", "8":
								if len(groupItem.ReplaceValue) > 0 {
									item.Video.VideoURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									videoFlag = "1"
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.VideoURL, "?") {
											item.Video.VideoURL = item.Video.VideoURL + "&" + s
										} else {
											item.Video.VideoURL = item.Video.VideoURL + "?" + s
										}
									}
									break
								}
							}
						}

						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "9", "10":
								if len(groupItem.ReplaceValue) > 0 {
									item.Video.CoverURL = groupItem.ReplaceValue
									coverFlag = "1"
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
							}
						}
					} else {
						for _, groupItem := range group {
							switch groupItem.ReplaceType {
							case "7", "8":
								if item.Video.Width > item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
									videoFlag = "1"
									item.Video.VideoURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.VideoURL, "?") {
											item.Video.VideoURL = item.Video.VideoURL + "&" + s
										} else {
											item.Video.VideoURL = item.Video.VideoURL + "?" + s
										}
									}
									break
								}
								if item.Video.Width < item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
									videoFlag = "1"
									item.Video.VideoURL = groupItem.ReplaceValue
									item.Video.Width = groupItem.Width
									item.Video.Height = groupItem.Height
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.VideoURL, "?") {
											item.Video.VideoURL = item.Video.VideoURL + "&" + s
										} else {
											item.Video.VideoURL = item.Video.VideoURL + "?" + s
										}
									}
									break
								}
							case "9", "10":
								if item.Video.Width > item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width > groupItem.Height {
									coverFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
								if item.Video.Width < item.Video.Height && len(groupItem.ReplaceValue) > 0 && groupItem.Width < groupItem.Height {
									coverFlag = "1"
									item.Video.CoverURL = groupItem.ReplaceValue
									if groupItem.DynamicMaterials == 2 {
										if strings.Contains(item.Video.CoverURL, "?") {
											item.Video.CoverURL = item.Video.CoverURL + "&" + s
										} else {
											item.Video.CoverURL = item.Video.CoverURL + "?" + s
										}
									}
									break
								}
							}
						}
					}

					// If video or cover was replaced, add to replace results
					if videoFlag == "1" || coverFlag == "1" {
						replaceResults = append(replaceResults, &models.ReplaceResult{
							Type:          models.ReplaceTypeVideo,
							RuleID:        materialItem.MaterialReplaceRuleID,
							RuleName:      materialItem.MaterialReplaceRuleName,
							OriginalVideo: originalVideo,
						})
					}
				}
			}

			isReplace = titleFlag + descFlag + iconFlag + imageFlag + coverFlag + videoFlag + FillVacanciesFlag
			item.IsReplace = isReplace

			replaceData.IsReplace = item.IsReplace
			replaceData.ReplaceTitle = item.Title
			replaceData.ReplaceDescription = item.Description
			replaceData.ReplaceIconURL = item.IconURL
			replaceData.ReplaceImage = item.Image
			replaceData.ReplaceVideo = item.Video

			if len(replaceResults) > 0 {
				jsonData, _ := json.Marshal(replaceResults)
				var (
					imgUrl   string
					videoUrl string
					coverUrl string
				)
				if item.Image != nil && len(item.Image) > 0 {
					imgUrl = item.Image[0].URL
				}
				if item.Video != nil {
					if len(item.Video.VideoURL) > 0 {
						videoUrl = item.Video.VideoURL
					}
					if len(item.Video.CoverURL) > 0 {
						coverUrl = item.Video.CoverURL
					}
				}
				crid := utilities.GetCrid(item.Title, item.Description, item.IconURL, imgUrl, coverUrl, videoUrl)
				item.SupplyCrid = crid

				// 确保所有参数都不为nil
				if mhReq != nil && len(jsonData) > 0 {
					// Create a deep copy of the item to avoid race conditions in the goroutine
					itemCopy := *item // Copy the struct

					// Create deep copies of nested fields
					if item.Video != nil {
						videoCopy := *item.Video
						itemCopy.Video = &videoCopy
					}

					if len(item.Image) > 0 {
						imageCopy := make([]models.MHRespImage, len(item.Image))
						copy(imageCopy, item.Image)
						itemCopy.Image = imageCopy
					}

					go models.MaterialReplaceStatisticsRawDataKafka(ctx, bigdataUID, mhReq, localPos, platformPos, &itemCopy, jsonData, 1)
				}
			}
		}
	}
}
