package core

import (
	"encoding/json"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func MaterialReplaceType(platformPos *models.PlatformPosStu, localPos *models.LocalPosStu, item *models.MHRespDataItem) {
	var tmpId string
	if platformPos.PlatformMediaID == "99" {
		tmpId = "99_1"
	} else {
		tmpId = platformPos.PlatformMediaID + "_" + strconv.Itoa(platformPos.PlatformAppCorpID) + "_" + platformPos.PlatformAppID
	}

	appKey := "go_ssp_material_replace_type_config_" + localPos.LocalAppID + tmpId
	posKey := "go_ssp_material_replace_type_config_" + localPos.LocalPosID + tmpId

	appValue, _ := db.GlbBigCacheMinute.Get(appKey)
	posValue, _ := db.GlbBigCacheMinute.Get(posKey)
	if len(appValue) == 0 && len(posValue) == 0 {
		return
	}

	var materialReplace []models.NewMaterialReplaceType
	if len(appValue) > 0 {
		_ = json.Unmarshal(appValue, &materialReplace)
	}

	if len(appValue) == 0 && len(posValue) > 0 {
		_ = json.Unmarshal(posValue, &materialReplace)
	}

	params := url.Values{}
	params.Set("date", time.Now().Format("20060102"))

	s := utils.Base64URLEncode([]byte(params.Encode()))

	isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0

	for _, materialItem := range materialReplace {
		if len(materialItem.MaterialReplaceGroup) == 0 {
			continue
		}
		materialReplaceGroup := random(materialItem.MaterialReplaceGroup, int64(len(materialItem.MaterialReplaceGroup)))
		for _, materialReplaceGroupItem := range materialReplaceGroup {
			if isVideoAd && materialReplaceGroupItem.ReplaceType == "8" {
				if len(materialReplaceGroupItem.ImgUrl) > 0 {
					var mhRespImageArray []models.MHRespImage
					var mhRespImage models.MHRespImage
					mhRespImage.URL = materialReplaceGroupItem.ImgUrl
					mhRespImage.Width = materialReplaceGroupItem.Width
					mhRespImage.Height = materialReplaceGroupItem.Height

					if materialReplaceGroupItem.DynamicMaterials == 2 {
						if strings.Contains(mhRespImage.URL, "?") {
							mhRespImage.URL = mhRespImage.URL + "&" + s
						} else {
							mhRespImage.URL = mhRespImage.URL + "?" + s
						}
					}

					mhRespImageArray = append(mhRespImageArray, mhRespImage)

					if len(mhRespImageArray) > 0 {
						item.CrtType = 11
						item.Image = mhRespImageArray
						item.Video = nil
						break
					}
				}
			} else if !isVideoAd && materialReplaceGroupItem.ReplaceType == "6" {
				if len(materialReplaceGroupItem.VideoUrl) > 0 {
					var video models.MHRespVideo
					video.VideoURL = materialReplaceGroupItem.VideoUrl
					video.CoverURL = materialReplaceGroupItem.CoverUrl
					video.Width = materialReplaceGroupItem.Width
					video.Height = materialReplaceGroupItem.Height
					video.Duration = materialReplaceGroupItem.Duration * 1000
					if len(video.VideoURL) > 0 {
						if materialReplaceGroupItem.DynamicMaterials == 2 {
							if strings.Contains(video.VideoURL, "?") {
								video.VideoURL = video.VideoURL + "&" + s
							} else {
								video.VideoURL = video.VideoURL + "?" + s
							}
						}
						item.Video = &video
						item.CrtType = 20
						item.Image = nil
						break
					}
				}
			}
		}
	}
}
