package core

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromAntiCheat ...
func GetFromAntiCheat(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from anticheat")
	// fmt.Println("get from anticheat, local app id: " + localPos.LocalAppID)
	// fmt.Println("get from anticheat, local pos id: " + localPos.LocalPosID)
	// fmt.Println("get from anticheat, up app id: " + platformPos.PlatformAppID)
	// fmt.Println("get from anticheat, up pos id: " + platformPos.PlatformPosID)
	// fmt.Println("get from anticheat, up media id: " + platformPos.PlatformMediaID)
	var platformPos models.PlatformPosStu
	platformPos.PlatformAppID = "anticheat"
	platformPos.PlatformPosID = "999"

	if false {
		fmt.Println("get from anticheat")
	}

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0
	bigdataExtra.PlatformAppID = platformPos.PlatformAppID
	bigdataExtra.PlatformPosID = platformPos.PlatformPosID

	if localPos.LocalAppSupportAntiCheatH5 == 1 {
	} else {
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if mhReq.Device.Os == "android" {
	} else {
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if localPos.LocalPosType == 4 {
	} else {
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if localPos.LocalPosMaterialType == 0 || localPos.LocalPosMaterialType == 2 {
	} else {
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// fmt.Println("kbg_debug_anticheat os version: ", mhReq.Device.OsVersion)
	// fmt.Println("kbg_debug_anticheat imei: ", mhReq.Device.Imei)
	// fmt.Println("kbg_debug_anticheat imei_md5: ", mhReq.Device.ImeiMd5)
	// fmt.Println("kbg_debug_anticheat oaid: ", mhReq.Device.Oaid)
	if mhReq.Device.Os == "android" {
		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true
			}

			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
			} else {

				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}
	cacheKey := "anticheat_app_id_" + localPos.LocalAppID
	_, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	if cacheError != nil {
		db.GlbBigCacheMinute.Set(cacheKey, []byte("1"))
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("cache value:", redisValue)
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	redisValue, _ := db.GlbRedis.SetNX(c, cacheKey, 1, 1*time.Minute).Result()
	// fmt.Println(redisValue)
	// fmt.Println(redisErr)
	if redisValue {
		fmt.Println("=========== ok, app_id:", cacheKey)
	} else {
		fmt.Println("=========== not ok, app_id:", cacheKey)
		bigdataExtra.InternalCode = 900002
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	for i := 0; i < 1; i++ {

		respListItemMap := map[string]interface{}{}
		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId
		respListItemMap["title"] = "C位游戏"
		respListItemMap["description"] = "精彩游戏尽在C位"
		respListItemMap["crid"] = utils.GetMd5("C位游戏")

		respListItemMap["icon_url"] = "https://static.maplehaze.cn/CweiGame/logo.png"

		respListImageItemMap := map[string]interface{}{}

		respListImageItemMap["url"] = "https://static.maplehaze.cn/adimg/tymyd/tymyd-Cover-1280X720-1.jpg"
		respListImageItemMap["width"] = 1280
		respListImageItemMap["height"] = 720

		var respListItemImageArray []map[string]interface{}
		respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

		respListItemMap["imgs"] = respListItemImageArray

		// crt_type
		respListItemMap["crt_type"] = 11

		respListItemMap["req_width"] = 1280
		respListItemMap["req_height"] = 720

		// respListItemMap["ecpm"] = 100

		// interact_type ad_url
		landingPangeURL := "https://landingpage-statistics.maplehaze.cn/?apkurl=https://static.maplehaze.cn/CweiGame/apk/com.maplehaze.cweigame.apk&author=%E5%8C%97%E4%BA%AC%E6%A0%BC%E7%91%9E%E5%88%9B%E6%96%B0%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&description=%E7%B2%BE%E5%BD%A9%E6%B8%B8%E6%88%8F%E5%B0%BD%E5%9C%A8C%E4%BD%8D&icon=https://static.maplehaze.cn/CweiGame/logo.png&image=https://static.maplehaze.cn/adimg/tymyd/tymyd-Cover-1280X720-1.jpg&privacy=https://static.maplehaze.cn/static/privacy_ad.html&title=C%E4%BD%8D%E6%B8%B8%E6%88%8F&version=0.0.1"
		landingPangeURL = landingPangeURL + "&app_id=" + localPos.LocalAppID
		respListItemMap["interact_type"] = 0
		respListItemMap["ad_url"] = landingPangeURL
		respListItemMap["landpage_url"] = landingPangeURL

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, &platformPos, bigdataUID, bigdataUID, 0, 0, 0, 0)
		mhImpParams.Add("log", bigdataParams)

		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("log", bigdataParams)

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
		respListItemMap["click_link"] = respListItemClkArray

		// direction
		respListItemMap["material_direction"] = 0

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, &platformPos, maplehazeAdId)

		respListArray = append(respListArray, respListItemMap)
	}

	bigdataExtra.UpRespTime = 1
	bigdataExtra.UpRespNum = 1

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[utils.ConvertIntToString(mhReq.Pos.ID)] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.UpRespOKNum = len(respListArray)

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// extra resp
	respExtra := models.MHUpResp{}
	respExtra.RespData = &mhResp
	respExtra.Extra = bigdataExtra
	// respExtra.Extra.RespCount = len(respListArray)
	// respExtra.Extra.ExternalCode = 0
	// respExtra.Extra.InternalCode = 900000

	return &respExtra
}
