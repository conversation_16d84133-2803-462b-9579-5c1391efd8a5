package up_wangmai_freeze

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/wangmai"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	"google.golang.org/protobuf/proto"
)

/**
* Init
**/

func NewPipline(common *up_common.UpCommonPipline) up_common.PiplineInterface {
	return &WangmaiPipline{Common: common}
}

/**
* Private Methods
**/

func (p *WangmaiPipline) replaceLinkString(link string, price int) string {
	linkString := link
	// linkString = strings.Replace(linkString, "__DOWN_X__", "__DOWN_X__", -1)
	// linkString = strings.Replace(linkString, "__DOWN_Y__", "__DOWN_Y__", -1)
	// linkString = strings.Replace(linkString, "__UP_X__", "__UP_X__", -1)
	// linkString = strings.Replace(linkString, "__UP_Y__", "__UP_Y__", -1)

	if p.Common.IsLogicPixel {
		linkString = strings.Replace(linkString, "__REQ_WIDTH__", utils.ConvertIntToString(p.Common.LogicPixelWidth), -1)
		linkString = strings.Replace(linkString, "__REQ_HEIGHT__", utils.ConvertIntToString(p.Common.LogicPixelHeight), -1)
	} else {
		linkString = strings.Replace(linkString, "__REQ_WIDTH__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosWidth), -1)
		linkString = strings.Replace(linkString, "__REQ_HEIGHT__", utils.ConvertIntToString(p.Common.LocalPos.LocalPosHeight), -1)
	}

	if p.Common.PlatformPos.PlatformPosIsReplaceXY == 1 {
		linkString = strings.Replace(linkString, "__DOWN_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__DOWN_Y__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_X__", "-999", -1)
		linkString = strings.Replace(linkString, "__UP_Y__", "-999", -1)
	}

	linkString = strings.Replace(linkString, "__AUCTION_PRICE__", utils.ConvertIntToString(price), -1)

	return linkString
}

// func (p *WangmaiPipline) encryptKey() string {
// 	return p.Common.PlatformPos.PlatformAppPriceEncrypt
// }

// func (p *WangmaiPipline) encryptPrice(price string) string {
// 	encryptKey := p.encryptKey()

// 	if len(encryptKey) == 0 {
// 		fmt.Println("price: ", price, "PlatformAppPriceEncrypt: ", p.Common.PlatformPos.PlatformAppPriceEncrypt)
// 		fmt.Printf("PlatformPos: %+v", p.Common.PlatformPos)
// 		return ""
// 	}
// 	return base64.StdEncoding.EncodeToString(utils.AesCBCPKCS5Encrypt(price, p.encryptKey()))
// }

// func (p *WangmaiPipline) decryptPrice(encryptedPrice string) (string, error) {
// 	var priceString string
// 	decodeString, err := base64.StdEncoding.DecodeString(encryptedPrice)
// 	if err != nil {
// 		return priceString, err
// 	}
// 	priceString = string(utils.AesCBCPKCS5Decrypt(decodeString, []byte(p.encryptKey())))
// 	return priceString, nil
// }

/**
 * * 函数式方法模板
 */
func (p *WangmaiPipline) actionTemplate() up_common.PiplineInterface {

	return p
}

/**
* Public Methods
**/

func (p *WangmaiPipline) Run() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("GetFromWangmai error:", p.Common.Error)
			// piplineBytes, _ := utils.JSONNoEscapeMarshal(p)
			// utils.MailDebuggerSend(string(piplineBytes))
		}
	}()

	p.
		SetupRequest().
		ReplaceRequest().
		RequestAd().
		SetupCommonResponse()
}

func (p *WangmaiPipline) SetupRequest() up_common.PiplineInterface {

	requestObject := wangmai.BidRequest{}
	{

		requestImpObject := wangmai.BidRequest_Imp{}
		{

			requestImpAdslotSize := wangmai.BidRequest_AdslotSize{}
			{
				requestImpAdslotSize.Width = int32(p.Common.PlatformPos.PlatformPosWidth)
				requestImpAdslotSize.Height = int32(p.Common.PlatformPos.PlatformPosHeight)

			}
			requestImpObject.Id = p.Common.UUID
			requestImpObject.TagId = p.Common.PlatformPos.PlatformPosID
			requestImpObject.AdslotSize = &requestImpAdslotSize
			requestImpObject.Bidfloor = float64(p.Common.CategoryInfo.FloorPrice)
			requestImpObject.Deeplink = true
			requestImpObject.Secure = 1
		}

		var requestImpObjects []*wangmai.BidRequest_Imp
		requestImpObjects = append(requestImpObjects, &requestImpObject)

		// Setup BidRequest_App
		requestAppObject := wangmai.BidRequest_App{}
		{
			requestAppObject.Bundle = p.Common.PlatformPos.PlatformAppBundle
			requestAppObject.AppVersion = p.Common.PlatformPos.PlatformAppVersion
			requestAppObject.AppName = p.Common.PlatformPos.PlatformAppName
		}

		// Setup WangmaiRequestDeviceObject
		requestAdDiveceObject := wangmai.BidRequest_Device{}
		{
			if p.Common.IsAndroid() {
				requestAdDiveceObject.Os = "Android"

				if p.Common.IsAndroidMajorLessThanTen() {
					if len(p.Common.MhReq.Device.Imei) > 0 {
						requestAdDiveceObject.Imei = p.Common.MhReq.Device.Imei
					} else if len(p.Common.MhReq.Device.ImeiMd5) > 0 {
						requestAdDiveceObject.ImeiMd5 = p.Common.MhReq.Device.ImeiMd5
					} else {
						p.Common.SetPanicStringAndCodes("osv < 10, invalid imei",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				} else {
					if len(p.Common.MhReq.Device.Oaid) > 0 {
						requestAdDiveceObject.Oaid = p.Common.MhReq.Device.Oaid
					} else {
						p.Common.SetPanicStringAndCodes("osv >= 10, invalid oaid",
							up_common.MH_UP_ERROR_CODE_900101,
							up_common.MH_UP_ERROR_CODE_104013)
					}
				}
			} else {
				requestAdDiveceObject.Os = "iOS"

				if len(p.Common.MhReq.Device.Idfa) > 0 {
					requestAdDiveceObject.Idfa = p.Common.MhReq.Device.Idfa
				} else if len(p.Common.MhReq.Device.IdfaMd5) > 0 {
					requestAdDiveceObject.IdfaMd5 = p.Common.MhReq.Device.IdfaMd5
				}

			}

			requestAdDiveceObject.Osv = p.Common.MhReq.Device.OsVersion
			requestAdDiveceObject.Ip = p.Common.MhReq.Device.IP
			requestAdDiveceObject.Ua = p.Common.MhReq.Device.Ua
			requestAdDiveceObject.ConnectionType = int32(NewWangmaiConnectType(up_common.UpCommonConnectTypeEnum(p.Common.MhReq.Network.ConnectType)))
			requestAdDiveceObject.Brand = p.Common.MhReq.Device.Manufacturer
			requestAdDiveceObject.Make = p.Common.MhReq.Device.Manufacturer
			requestAdDiveceObject.Model = p.Common.MhReq.Device.Model
			requestAdDiveceObject.Carrier = int32(NewWangmaiCarrier(up_common.UpCommonCarrierEnum(p.Common.MhReq.Network.Carrier)))
			requestAdDiveceObject.ScreenWidth = int32(p.Common.MhReq.Device.ScreenWidth)
			requestAdDiveceObject.ScreenHeight = int32(p.Common.MhReq.Device.ScreenHeight)
			requestAdDiveceObject.Orientation = int32(NewWangmaiOrientation(up_common.UpCommonScreenDirection(p.Common.MhReq.Device.Orientation)))
		}

		requestObject.Id = p.Common.UUID
		requestObject.Imp = requestImpObjects
		requestObject.App = &requestAppObject
		requestObject.Device = &requestAdDiveceObject
		requestObject.ApiVersion = "1.02"
	}

	p.Request = &requestObject

	return p
}

func (p *WangmaiPipline) ReplaceRequest() up_common.PiplineInterface {

	if p.Common.ReplacedValues == nil {
		return p
	}

	p.Request.Device.Os = NewWangmaiOS(p.Common.ReplacedValues.Os)

	if len(p.Common.ReplacedValues.OsVersion) > 0 {
		p.Request.Device.Osv = p.Common.ReplacedValues.OsVersion
	}

	if len(p.Common.ReplacedValues.Model) > 0 {
		p.Request.Device.Model = p.Common.ReplacedValues.Model
	}

	if len(p.Common.ReplacedValues.Manufacturer) > 0 {
		p.Request.Device.Make = p.Common.ReplacedValues.Manufacturer
		p.Request.Device.Brand = p.Common.ReplacedValues.Manufacturer
	}

	if len(p.Common.ReplacedValues.Imei) > 0 {
		p.Request.Device.Imei = p.Common.ReplacedValues.Imei
	}

	if len(p.Common.ReplacedValues.ImeiMd5) > 0 {
		p.Request.Device.ImeiMd5 = p.Common.ReplacedValues.ImeiMd5
	}

	if len(p.Common.ReplacedValues.Oaid) > 0 {
		p.Request.Device.Oaid = p.Common.ReplacedValues.Oaid
	}

	if len(p.Common.ReplacedValues.AndroidId) > 0 {
		p.Request.Device.AndroidId = p.Common.ReplacedValues.AndroidId
	}

	if len(p.Common.ReplacedValues.Idfa) > 0 {
		p.Request.Device.Idfa = p.Common.ReplacedValues.Idfa
	}

	if len(p.Common.ReplacedValues.IdfaMd5) > 0 {
		p.Request.Device.IdfaMd5 = p.Common.ReplacedValues.IdfaMd5
	}

	if len(p.Common.ReplacedValues.Ua) > 0 {
		p.Request.Device.Ua = p.Common.ReplacedValues.Ua
	}

	p.Request.Device.Orientation = int32(NewWangmaiOrientation(up_common.UpCommonScreenDirection(p.Common.ReplacedValues.ScreenDirection)))

	return p
}

func (p *WangmaiPipline) RequestAd() up_common.PiplineInterface {

	jsonData, _ := utils.JSONNoEscapeMarshal(p.Request)
	protoData, err := proto.Marshal(p.Request)
	if err != nil {
		panic(err)
	}

	// TODO: 去掉debug
	fmt.Println("raw request:", string(jsonData))
	//fmt.Println("raw request:", string(protoData))

	httpRequest, _ := http.NewRequest("POST", p.Common.PlatformPos.PlatformAppUpURL, bytes.NewReader(protoData))

	// err = os.WriteFile("/Users/<USER>/Downloads/WangmaiRequest.dat", protoData, 0644)
	if err != nil {
		fmt.Println(err)
	}

	httpRequest.Header.Add("Content-Type", "application/x-protobuf")
	httpRequest.Header.Add("Connection", "keep-alive")

	if p.Common.ReplacedValues != nil && len(p.Common.ReplacedValues.Ua) > 0 {
		httpRequest.Header.Del("User-Agent")
		httpRequest.Header.Add("User-Agent", p.Common.ReplacedValues.Ua)
	}

	p.Common.ResponseExtra.UpReqTime = 1
	p.Common.ResponseExtra.UpReqNum = p.Common.MhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(p.Common.LocalPos.LocalPosTimeOut) * time.Millisecond}

	response, err := client.Do(httpRequest)

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())

		var errCode = 0
		if strings.Contains(err.Error(), "Timeout") {
			errCode = up_common.MH_UP_ERROR_CODE_900106
		} else {
			errCode = up_common.MH_UP_ERROR_CODE_900102
		}
		p.Common.SetPanicStringAndCodes("网络错误: "+err.Error(),
			errCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	defer response.Body.Close()

	bodyContent, err := io.ReadAll(response.Body)

	if response.StatusCode != 200 {
		if response.StatusCode == 204 {
			p.Common.SetPanicStringAndCodes("status is 204",
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}

		if err != nil {
			p.Common.SetPanicStringAndCodes("status is no 200: "+err.Error(),
				up_common.MH_UP_ERROR_CODE_900107,
				up_common.MH_UP_ERROR_CODE_102006)
		}
	}

	var responseObject wangmai.BidResponse

	proto.Unmarshal(bodyContent, &responseObject)

	p.Response = &responseObject

	if p.Response == nil {
		p.Common.SetPanicStringAndCodes("upstream request error: "+string(bodyContent),
			up_common.MH_UP_ERROR_CODE_900107,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	return p
}

func (p *WangmaiPipline) SetupCommonResponse() up_common.PiplineInterface {

	commonResponse := up_common.UpCommonResponseObject{}
	var list []*up_common.UpCommonAPIResponseObject

	p.Common.ResponseExtra.UpRespNum = 0
	p.Common.ResponseExtra.UpRespFailedNum = 0
	p.Common.ResponseExtra.UpRespTime = 1

	// 上游可能会返回1个或多个，目前仅一个
	if len(p.Response.Seatbid) > 0 {
		for _, responseItem := range p.Response.Seatbid[0].Bid {
			//fmt.Println(responseItem)
			bidPrice := int(responseItem.Price)

			p.Common.ResponseExtra.UpRespNum = p.Common.ResponseExtra.UpRespNum + 1
			p.Common.ResponseExtra.UpPrice = p.Common.ResponseExtra.UpPrice + bidPrice

			// 成功时的价格比例
			randPriceRatio := p.Common.RandPriceRatio()
			winPrice := int(bidPrice * randPriceRatio / 100)

			// 失败时的价格比例
			//randFailedPriceRatio := p.Common.RandFailedPriceRatio()

			if p.Common.CategoryInfo.FinalPrice > bidPrice {
				p.Common.ResponseExtra.UpRespFailedNum = p.Common.ResponseExtra.UpRespFailedNum + 1

				// 如果有竞败链接，需要回调处理
				continue
			}

			var commonAPIResponseObject up_common.UpCommonAPIResponseObject

			if responseItem.Items != nil && len(responseItem.Items.Title) > 0 {
				commonAPIResponseObject.AdId = utils.GetMd5(responseItem.Items.Title)
				commonAPIResponseObject.Title = responseItem.Items.Title
			}

			if responseItem.Items != nil && len(responseItem.Items.Desc) > 0 {
				commonAPIResponseObject.Description = responseItem.Items.Desc
			}

			if responseItem.Items != nil && len(responseItem.Items.PackageName) > 0 {
				commonAPIResponseObject.PackageName = responseItem.Items.PackageName
			}

			commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_LANDPAGE

			if responseItem.Items.MediaStyle == 2 {
				commonAPIResponseObject.InteractType = up_common.MH_UP_COMMON_INTERACTTYPE_DOWNLOAD
				commonAPIResponseObject.DownloadUrl = responseItem.Items.DownloadUrl
			}

			if responseItem.Items != nil && len(responseItem.Items.ClickUrl) > 0 {
				commonAPIResponseObject.LandPageUrl = responseItem.Items.ClickUrl
				commonAPIResponseObject.AdUrl = responseItem.Items.ClickUrl
			}

			if responseItem.Items != nil &&
				responseItem.Items.DownloadAppInfo != nil &&
				len(responseItem.Items.DownloadAppInfo.Developer) > 0 {
				commonAPIResponseObject.Publisher = responseItem.Items.DownloadAppInfo.Developer
			}

			if responseItem.Items != nil &&
				responseItem.Items.DownloadAppInfo != nil &&
				len(responseItem.Items.DownloadAppInfo.Version) > 0 {
				commonAPIResponseObject.AppVersion = responseItem.Items.DownloadAppInfo.Version
			}

			if responseItem.Items != nil &&
				responseItem.Items.DownloadAppInfo != nil &&
				len(responseItem.Items.DownloadAppInfo.AppName) > 0 {
				commonAPIResponseObject.AppName = responseItem.Items.DownloadAppInfo.AppName
			}

			if responseItem.Items != nil &&
				responseItem.Items.DownloadAppInfo != nil &&
				len(responseItem.Items.DownloadAppInfo.Privacy) > 0 {
				commonAPIResponseObject.PrivacyUrl = responseItem.Items.DownloadAppInfo.Privacy
			}

			if responseItem.Items != nil &&
				responseItem.Items.DownloadAppInfo != nil &&
				len(responseItem.Items.DownloadAppInfo.Permission) > 0 {
				commonAPIResponseObject.PermissionUrl = responseItem.Items.DownloadAppInfo.Permission
			} else {
				commonAPIResponseObject.Permission = up_common.MH_UP_COMMON_DEFAULT_PERMISSION
			}

			commonAPIResponseObject.PackageSize = (200 + rand.Intn(800)) * 1024 * 1024

			commonAPIResponseObject.Crid = commonAPIResponseObject.AdId

			commonAPIResponseObject.ReqWidth = p.Common.PlatformPos.PlatformPosWidth
			commonAPIResponseObject.ReqHeight = p.Common.PlatformPos.PlatformPosHeight

			var commonAPIResponseConvTrackObjects []*up_common.UpCommonAPIResponseConvTrackObject

			if responseItem.Items != nil &&
				len(responseItem.Items.DplUrl) > 0 {
				commonAPIResponseObject.DeepLink = responseItem.Items.DplUrl

				var convTrack up_common.UpCommonAPIResponseConvTrackObject
				convTrack.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_SUCCESS

				var respListItemDeepLinkArray []string

				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				bigdataParams := up_common.EncodeParams(
					p.Common.MhReq,
					p.Common.LocalPos,
					p.Common.PlatformPos,
					p.Common.UUID,
					p.Common.UUID,
					0,
					0,
					0,
					0)
				mhDPParams.Add("log", bigdataParams)

				respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

				// TODO: 需要取认
				if responseItem.Items != nil && len(responseItem.Items.DpSuccessTrackUrls) > 0 {
					respListItemDeepLinkArray = append(respListItemDeepLinkArray, responseItem.Items.DpSuccessTrackUrls...)
				}

				convTrack.ConvUrls = respListItemDeepLinkArray
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrack)

				if p.Common.PlatformPos.PlatformAppIsDeepLinkFailed == 1 {

					var convTrackFailed up_common.UpCommonAPIResponseConvTrackObject
					convTrackFailed.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DEEPLINK_FAIL

					var respListItemDeepLinkFailedArray []string

					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())
					convTrackFailed.ConvUrls = respListItemDeepLinkFailedArray
					commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &convTrackFailed)
				}
			}

			if responseItem.Items != nil && len(responseItem.Items.DownloadTrackUrls) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_START
				commonAPIResponseConvTrackObject.ConvUrls = responseItem.Items.DownloadTrackUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			}

			if responseItem.Items != nil && len(responseItem.Items.DownloadedTrackUrls) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_DOWNLOAD_END
				commonAPIResponseConvTrackObject.ConvUrls = responseItem.Items.DownloadedTrackUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			}

			if responseItem.Items != nil && len(responseItem.Items.InstalledTrackUrls) > 0 {
				var commonAPIResponseConvTrackObject up_common.UpCommonAPIResponseConvTrackObject
				commonAPIResponseConvTrackObject.ConvType = up_common.MH_UP_COMMON_CONVTYPE_INSTALL_END
				commonAPIResponseConvTrackObject.ConvUrls = responseItem.Items.InstalledTrackUrls
				commonAPIResponseConvTrackObjects = append(commonAPIResponseConvTrackObjects, &commonAPIResponseConvTrackObject)
			}

			if len(commonAPIResponseConvTrackObjects) > 0 {
				commonAPIResponseObject.ConvTracks = commonAPIResponseConvTrackObjects
			}

			if responseItem.Items != nil && len(responseItem.Items.Icon) > 0 {
				commonAPIResponseObject.IconUrl = responseItem.Items.Icon
			} else {
				commonAPIResponseObject.IconUrl = utils.GetIconURLByDeeplink(responseItem.Items.DplUrl)
			}
			if responseItem.Items != nil {
				if responseItem.Items.Video != nil && len(responseItem.Items.Video.VideoUrl) > 0 {
					// 视频
					var commonAPIResponseVideoObject up_common.UpCommonAPIResponseVideoObject
					if responseItem.Items.Video.VideoDuration > 0 {
						commonAPIResponseVideoObject.Duration = int(responseItem.Items.Video.VideoDuration)
					}
					commonAPIResponseVideoObject.Width = p.Common.PlatformPos.PlatformPosWidth
					commonAPIResponseVideoObject.Height = p.Common.PlatformPos.PlatformPosHeight

					commonAPIResponseVideoObject.VideoUrl = responseItem.Items.Video.VideoUrl

					if len(responseItem.Items.Video.VideoPreImgurl) > 0 {
						commonAPIResponseVideoObject.CoverUrl = responseItem.Items.Video.VideoPreImgurl
					} else if len(responseItem.Items.Imgs) > 0 {
						commonAPIResponseVideoObject.CoverUrl = responseItem.Items.Imgs[0]
					} else {
						if p.Common.LocalPos.LocalPosWidth > p.Common.LocalPos.LocalPosHeight {
							commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
							if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
								commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
							}
						} else {
							commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
							if p.Common.LocalPos.LocalAppID == "10166" || p.Common.LocalPos.LocalAppID == "10167" {
								commonAPIResponseVideoObject.CoverUrl = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
							}
						}
					}
					commonAPIResponseVideoObject.EndcardType = 1
					commonAPIResponseVideoObject.EndcardRange = 1

					var commonAPIResponseVideoEventTrackObjects []*up_common.UpCommonAPIResponseVideoEventTrackObject

					if len(responseItem.Items.Video.VideoStartUrl) > 0 {
						var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
						commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_START
						commonAPIResponseVideoEventTrackObject.EventTrackUrls = append(commonAPIResponseVideoEventTrackObject.EventTrackUrls, responseItem.Items.Video.VideoStartUrl)
						commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
					}

					if len(responseItem.Items.Video.VideoFinishUrl) > 0 {
						var commonAPIResponseVideoEventTrackObject up_common.UpCommonAPIResponseVideoEventTrackObject
						commonAPIResponseVideoEventTrackObject.EventType = up_common.MH_UP_COMMON_VIDEO_EVENTTYPE_FIRSTQUARTILE
						commonAPIResponseVideoEventTrackObject.EventTrackUrls = append(commonAPIResponseVideoEventTrackObject.EventTrackUrls, responseItem.Items.Video.VideoFinishUrl)
						commonAPIResponseVideoEventTrackObjects = append(commonAPIResponseVideoEventTrackObjects, &commonAPIResponseVideoEventTrackObject)
					}

					if len(commonAPIResponseVideoEventTrackObjects) > 0 {
						commonAPIResponseVideoObject.EventTracks = commonAPIResponseVideoEventTrackObjects
					}

					commonAPIResponseObject.Video = &commonAPIResponseVideoObject
					commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_VIDEO
				} else {
					// 图片
					var commonAPIResponseImageObjects []*up_common.UpCommonAPIResponseImageObject

					if len(responseItem.Items.Imgs) > 0 {
						for _, imageUrl := range responseItem.Items.Imgs {
							var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
							commonAPIResponseImageObject.Url = imageUrl
							commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
							commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
							commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)
						}
					}
					// else if len(responseItem.ImageUrl) > 0 {
					// 	var commonAPIResponseImageObject up_common.UpCommonAPIResponseImageObject
					// 	commonAPIResponseImageObject.Url = responseItem.ImageUrl
					// 	commonAPIResponseImageObject.Width = p.Common.PlatformPos.PlatformPosWidth
					// 	commonAPIResponseImageObject.Height = p.Common.PlatformPos.PlatformPosHeight
					// 	commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, &commonAPIResponseImageObject)

					// 	commonAPIResponseImageObject.Url = audit.ReplaceImageURLByKey(p.Common.Context,
					// 		p.Common.LocalPos,
					// 		commonAPIResponseObject.Title,
					// 		commonAPIResponseObject.Description,
					// 		commonAPIResponseImageObject.Url,
					// 		commonAPIResponseObject.IconUrl,
					// 		commonAPIResponseObject.AdUrl)
					// }

					commonAPIResponseObject.Images = commonAPIResponseImageObjects
					commonAPIResponseObject.CrtType = up_common.MH_UP_COMMON_CRTTYPE_IMAGE

					if commonAPIResponseObject.Images == nil || len(commonAPIResponseObject.Images) == 0 {
						p.Common.SetPanicStringAndCodes("images is empty",
							up_common.MH_UP_ERROR_CODE_900201,
							up_common.MH_UP_ERROR_CODE_102006)
					}
				}
			}
			bigdataParams := up_common.EncodeParams(
				p.Common.MhReq,
				p.Common.LocalPos,
				p.Common.PlatformPos,
				p.Common.UUID,
				p.Common.UUID,
				p.Common.CategoryInfo.FloorPrice,
				p.Common.CategoryInfo.FinalPrice,
				bidPrice,
				0)

			if responseItem.Items != nil && len(responseItem.Items.ExposalUrls) > 0 {
				var impressionLinks []string

				p.Common.MhImpressionLink.AddBigDataParams(bigdataParams)

				impressionLinks = append(impressionLinks, p.Common.MhImpressionLink.StringWithUrl(config.ExternalExpURL))

				for _, linkString := range responseItem.Items.ExposalUrls {
					replacedLinkString := p.replaceLinkString(linkString, winPrice)
					impressionLinks = append(impressionLinks, replacedLinkString)
				}

				commonAPIResponseObject.ImpressionLink = impressionLinks

			}

			if responseItem.Items != nil && len(responseItem.Items.ClickMonitorUrls) > 0 {
				var clickLinks []string

				p.Common.MhClickLink.AddBigDataParams(bigdataParams)

				clickLinks = append(clickLinks, p.Common.MhClickLink.StringWithUrl(config.ExternalClkURL))

				for _, linkString := range responseItem.Items.ClickMonitorUrls {

					replacedLinkString := p.replaceLinkString(linkString, winPrice)

					clickLinks = append(clickLinks, replacedLinkString)
				}

				// 2022-10-28 与产品确认 DpSuccessTrackUrls 不属于 ClickLink
				// if len(responseItem.Items.DplUrl) > 0 {
				// 	clickLinks = append(clickLinks, responseItem.Items.DpSuccessTrackUrls...)
				// }

				commonAPIResponseObject.ClickLink = clickLinks
			}

			if p.Common.IsReportToWin {
				if len(responseItem.Nurl) > 0 && bidPrice > 0 && p.Common.PlatformPos.PlatformAppIsPriceEncrypt == 1 {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								fmt.Println("gdt win url panic:", err)
							}
						}()

						replacedLinkString := p.replaceLinkString(responseItem.Nurl, winPrice)

						p.Common.RequestUrl(replacedLinkString)

					}()
				}
			}

			commonAPIResponseObject.MaterialDirection = p.Common.PlatformPos.PlatformPosDirection
			commonAPIResponseObject.Log = p.Common.EncodeRtbParams()

			list = append(list, &commonAPIResponseObject)
		}
	}

	if len(list) == 0 {
		p.Common.SetPanicStringAndCodes("ad item is empty",
			p.Common.ResponseExtra.InternalCode,
			up_common.MH_UP_ERROR_CODE_102006)
	}

	// 补充 ResponseExtra
	p.Common.ResponseExtra.UpRespOKNum = len(list)
	p.Common.ResponseExtra.FloorPrice = p.Common.CategoryInfo.FloorPrice * len(list)
	p.Common.ResponseExtra.FinalPrice = p.Common.CategoryInfo.FinalPrice * len(list)

	commonResponseDataObject := up_common.UpCommonResponseDataObject{
		Ret: 0,
		Msg: "",
		Data: &up_common.UpCommonResponseDataDataMapObject{
			p.Common.LocalPos.LocalPosID: {
				List: list,
			},
		},
		IsLogicPixel: utils.ConvertBoolToInt(p.Common.IsLogicPixel),
		IsClickLimit: utils.ConvertBoolToInt(p.Common.IsClickLimit),
	}
	commonResponse.RespData = &commonResponseDataObject
	commonResponse.Extra = p.Common.ResponseExtra
	p.Common.Response = &commonResponse

	// TODO: debug用
	responseJson, _ := utils.JSONNoEscapeMarshal(p.Common.Response)
	fmt.Println("responseJson: ", string(responseJson))

	commonResponseDataJSON, err := utils.JSONNoEscapeMarshal(commonResponseDataObject)
	if err == nil {
		var commonResponseDataMap map[string]interface{}
		json.Unmarshal(commonResponseDataJSON, &commonResponseDataMap)
	}

	return p
}

func (p *WangmaiPipline) ResponseResult() *models.MHUpResp {
	return p.
		Common.
		ResponseResult()
}
