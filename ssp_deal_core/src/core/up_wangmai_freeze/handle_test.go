package up_wangmai_freeze

import (
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"testing"

	"github.com/gin-gonic/gin"
)

func Test_GetFromWangmai(ct *testing.T) {
	db.InitRedis()
	db.InitBigCache()
	db.InitMysql()

	c := gin.Context{}

	mhReqApp := models.MHReqApp{}
	mhReqDevice := models.MHReqDevice{
		DeviceType: 1,
		IP:         "*************",
		//Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
		Ua:        "PostmanRuntime/7.29.2",
		OsVersion: "8",
		Imei:      "869154023994200",
		ImeiMd5:   "",
		Oaid:      "2c06c2722e300206e4c6c79f07c7d2efd7ec74b82daa67e1019b1efd92a2b488",
		Os:        "android",
		//Idfa:         "idfs_string",
		Manufacturer: "HUAWEI",
		ScreenWidth:  1080,
		ScreenHeight: 1792,
		Model:        "EVA-AL10",
	}

	mhReqNetwork := models.MHReqNetwork{
		Carrier:     1,
		ConnectType: 1,
	}

	mhReq := models.MHReq{
		App:     mhReqApp,
		Device:  mhReqDevice,
		Network: mhReqNetwork,
	}

	localPosInfo := models.LocalPosStu{
		LocalPosID: "57863",
	}

	platformPosInfo := models.PlatformPosStu{
		PlatformAppID: "2444",
		PlatformPosID: "4195130",
		//PlatformPosID:       "4195130",
		//PlatformAppVersion:  "1.0",
		PlatformAppIsActive: 1,
		PlatformPosIsActive: 1,
		//PlatformAppUpURL:    "http://*************:9299/Wmrtbs/rtbRequest.api?adx=983",
		PlatformAppUpURL: "http://cn.bj.adx.adwangmai.com/Wmrtbs/rtbRequest.api?adx=983",
		//PlatformAppName:   "maplehaze",
		PlatformAppBundle: "com.maplehaze.adsdk.demo",
		//PlatformAppIsPriceEncrypt: 0,
		//PlatformAppPriceEncrypt:   "0276af914fee82a4",
		PlatformPosType:   2,
		PlatformPosWidth:  1080,
		PlatformPosHeight: 1920,
		// PlatformAppKey:    "f061422e6aab1270",
		// PlatformAppSecret: "jmnt85s9js",
	}

	categoryInfo := models.CategoryStu{
		FloorPrice: 1,
	}

	bigdataUID := "8d679d5e-0f6f-4b99-9927-b534026745ad"

	respData := GetFromWangmai(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

	fmt.Printf("\t\t%#v,\n", platformPosInfo)
	fmt.Printf("\t\t%#v,\n", respData)
}
