package up_wangmai_freeze

import (
	"fmt"
	"mh_proxy/core/up_common"
	"mh_proxy/pb/wangmai"
)

func NewWangmaiOS(os up_common.UpCommonOSEnum) string {
	if os == up_common.MH_UP_COMMON_OS_IOS {
		return "iOS"
	}
	return "Android"
}

func NewWangmaiConnectType(connectType up_common.UpCommonConnectTypeEnum) int {
	switch connectType {
	case up_common.MH_UP_COMMON_CONNECTTYPE_2G:
		return 2
	case up_common.MH_UP_COMMON_CONNECTTYPE_3G:
		return 3
	case up_common.MH_UP_COMMON_CONNECTTYPE_4G:
		return 4
	case up_common.MH_UP_COMMON_CONNECTTYPE_5G:
		return 5
	case up_common.MH_UP_COMMON_CONNECTTYPE_WIFI:
		return 100
	}

	return 1
}

func NewWangmaiCarrier(carrier up_common.UpCommonCarrierEnum) int {
	switch carrier {
	case up_common.MH_UP_COMMON_CARRIER_CM:
		return 1
	case up_common.MH_UP_COMMON_CARRIER_CT:
		return 2
	case up_common.MH_UP_COMMON_CARRIER_CU:
		return 3
	}

	return 0
}

func NewWangmaiOrientation(screenDirection up_common.UpCommonScreenDirection) int {
	switch screenDirection {
	case up_common.MH_UP_COMMON_SCREENDIRECTION_LANDSCAPE:
		return 1
	}

	return 2
}

type WangmaiPipline struct {
	Common *up_common.UpCommonPipline

	Request  *wangmai.BidRequest
	Response *wangmai.BidResponse
}

func (r *WangmaiPipline) String() string {
	return fmt.Sprintf("%+v", *r)
}
