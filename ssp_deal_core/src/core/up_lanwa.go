package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromupLanwa 蓝蛙
func GetFromupLanwa(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpCPUNum := mhReq.Device.CPUNum

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType int
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = 1
	case 2:
		connectionType = 2
	case 3:
		connectionType = 3
	case 4, 5, 6:
		connectionType = 4
	case 7:
		connectionType = 5
	default:
		connectionType = 0
	}

	var platform int
	var os string
	switch mhReq.Device.Os {
	case "android":
		platform = 1
		os = "Android"
	case "ios":
		platform = 2
		os = "iOS"
	}

	var screenOrient int
	switch platformPos.PlatformPosDirection {
	case 0:
		screenOrient = 2
	case 1:
		screenOrient = 1
	}

	request := LanwaRequestObject{
		APIVersion:       "1.4.0",
		AppName:          platformPos.PlatformAppName,
		PkgName:          GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		AppVersion:       platformPos.PlatformAppVersion,
		Make:             mhReq.Device.Manufacturer,
		Brand:            mhReq.Device.Manufacturer,
		Model:            mhReq.Device.Model,
		Bidfloor:         categoryInfo.FloorPrice,
		Count:            mhReq.Pos.AdCount,
		Mac:              mhReq.Device.Mac,
		NetType:          connectionType,
		ScreenWidth:      mhReq.Device.ScreenWidth,
		ScreenHeight:     mhReq.Device.ScreenHeight,
		Platform:         platform,
		Os:               os,
		OsVersion:        mhReq.Device.OsVersion,
		AppStoreVersion:  mhReq.Device.AppStoreVersion,
		IP:               mhReq.Device.IP,
		ScreenOrient:     screenOrient,
		Carrier:          mhReq.Network.Carrier,
		AdWidth:          platformPos.PlatformPosWidth,
		AdHeight:         platformPos.PlatformPosHeight,
		SupportDeeplink:  1,
		SupportUniversal: 1,
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Imei = mhReq.Device.Imei
				request.Imeimd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.Imeimd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Oaid = mhReq.Device.Oaid
			} else if len(mhReq.Device.AndroidID) > 0 {
				request.AndroidID = mhReq.Device.AndroidID
				if len(mhReq.Device.AndroidIDMd5) > 0 {
					request.Auidmd5 = mhReq.Device.AndroidIDMd5
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Idfa = mhReq.Device.Idfa
				request.Idfamd5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Idfamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var caidList []LanwaRequestCaidObject
				for _, item := range mhReq.Device.CAIDMulti {
					var caidItem LanwaRequestCaidObject
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion
						caidList = append(caidList, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
				request.Caids = caidList
			} else {
				var caidList []LanwaRequestCaidObject
				for _, item := range mhReq.Device.CAIDMulti {
					var caidItem LanwaRequestCaidObject
					caidItem.Id = item.CAID
					caidItem.Version = item.CAIDVersion
					caidList = append(caidList, caidItem)

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
				request.Caids = caidList
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				request.OsUpdateTime = tmpSystemUpdateSec
				request.OsStartupTime = tmpDeviceStartSec
				request.OsBirthTime = tmpDeviceBirthSec
				request.PhoneNameMd5 = tmpDeviceNameMd5
				request.HardwareMachine = tmpHardwareMachine
				request.SysMemory = tmpPhysicalMemoryByte
				request.SysDisksize = tmpHarddiskSizeByte
				request.Language = tmpLanguage
				request.Country = tmpCountry
				request.Timezone = tmpTimeZone
				request.CpuNum = tmpCPUNum
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfa = mhReq.Device.Idfa
					request.Idfamd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidList []LanwaRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidItem LanwaRequestCaidObject
							caidItem.Id = item.CAID
							caidItem.Version = item.CAIDVersion

							caidList = append(caidList, caidItem)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
					request.Caids = caidList
				} else {
					var caidList []LanwaRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						var caidItem LanwaRequestCaidObject
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion

						caidList = append(caidList, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
					request.Caids = caidList
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					request.OsUpdateTime = tmpSystemUpdateSec
					request.OsStartupTime = tmpDeviceStartSec
					request.OsBirthTime = tmpDeviceBirthSec
					request.PhoneNameMd5 = tmpDeviceNameMd5
					request.HardwareMachine = tmpHardwareMachine
					request.SysMemory = tmpPhysicalMemoryByte
					request.SysDisksize = tmpHarddiskSizeByte
					request.Language = tmpLanguage
					request.Country = tmpCountry
					request.Timezone = tmpTimeZone
					request.CpuNum = tmpCPUNum
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfa = mhReq.Device.Idfa
					request.Idfamd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfamd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidList []LanwaRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidItem LanwaRequestCaidObject
							caidItem.Id = item.CAID
							caidItem.Version = item.CAIDVersion

							caidList = append(caidList, caidItem)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
					request.Caids = caidList
				} else {
					var caidList []LanwaRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						var caidItem LanwaRequestCaidObject
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion

						caidList = append(caidList, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
					request.Caids = caidList
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					request.OsUpdateTime = tmpSystemUpdateSec
					request.OsStartupTime = tmpDeviceStartSec
					request.OsBirthTime = tmpDeviceBirthSec
					request.PhoneNameMd5 = tmpDeviceNameMd5
					request.HardwareMachine = tmpHardwareMachine
					request.SysMemory = tmpPhysicalMemoryByte
					request.SysDisksize = tmpHarddiskSizeByte
					request.Language = tmpLanguage
					request.Country = tmpCountry
					request.Timezone = tmpTimeZone
					request.CpuNum = tmpCPUNum
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.Platform = 2
						request.Os = "iOS"
					case "android":
						request.Platform = 1
						request.Os = "Android"
					}
					request.OsVersion = didRedisData.OsVersion
					request.Model = didRedisData.Model
					request.Make = didRedisData.Manufacturer
					request.Brand = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Imei = didRedisData.Imei
							request.Imeimd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.Imeimd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Oaid = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							request.AndroidID = didRedisData.AndroidID
							if len(didRedisData.AndroidIDMd5) > 0 {
								request.Auidmd5 = didRedisData.AndroidIDMd5
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.Platform = 2
							request.Os = "iOS"
						case "android":
							request.Platform = 1
							request.Os = "Android"
						}
						request.OsVersion = didRedisData.OsVersion
						request.Model = didRedisData.Model
						request.Make = didRedisData.Manufacturer
						request.Brand = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Idfa = didRedisData.Idfa
								request.Idfamd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.Idfamd5 = strings.ToLower(didRedisData.IdfaMd5)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidList []LanwaRequestCaidObject
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidItem LanwaRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidList = append(caidList, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
								request.Caids = caidList
							} else {
								var caidList []LanwaRequestCaidObject
								for _, item := range tmpCAIDMulti {
									var caidItem LanwaRequestCaidObject
									caidItem.Id = item.CAID
									caidItem.Version = item.CAIDVersion

									caidList = append(caidList, caidItem)

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
								}
								request.Caids = caidList
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

							if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								request.OsUpdateTime = tmpSystemUpdateSec
								request.OsStartupTime = tmpDeviceStartSec
								request.OsBirthTime = tmpDeviceBirthSec
								request.PhoneNameMd5 = tmpDeviceNameMd5
								request.HardwareMachine = tmpHardwareMachine
								request.SysMemory = tmpPhysicalMemoryByte
								request.SysDisksize = tmpHarddiskSizeByte
								request.Language = tmpLanguage
								request.Country = tmpCountry
								request.Timezone = tmpTimeZone
								request.CpuNum = tmpCPUNum
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Idfa = didRedisData.Idfa
									request.Idfamd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Idfamd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidList []LanwaRequestCaidObject
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidItem LanwaRequestCaidObject
											caidItem.Id = item.CAID
											caidItem.Version = item.CAIDVersion

											caidList = append(caidList, caidItem)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
									request.Caids = caidList
								} else {
									var caidList []LanwaRequestCaidObject
									for _, item := range tmpCAIDMulti {
										var caidItem LanwaRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidList = append(caidList, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
									request.Caids = caidList
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									request.OsUpdateTime = tmpSystemUpdateSec
									request.OsStartupTime = tmpDeviceStartSec
									request.OsBirthTime = tmpDeviceBirthSec
									request.PhoneNameMd5 = tmpDeviceNameMd5
									request.HardwareMachine = tmpHardwareMachine
									request.SysMemory = tmpPhysicalMemoryByte
									request.SysDisksize = tmpHarddiskSizeByte
									request.Language = tmpLanguage
									request.Country = tmpCountry
									request.Timezone = tmpTimeZone
									request.CpuNum = tmpCPUNum
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Idfa = didRedisData.Idfa
									request.Idfamd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Idfamd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidList []LanwaRequestCaidObject
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidItem LanwaRequestCaidObject
											caidItem.Id = item.CAID
											caidItem.Version = item.CAIDVersion

											caidList = append(caidList, caidItem)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
									request.Caids = caidList
								} else {
									var caidList []LanwaRequestCaidObject
									for _, item := range tmpCAIDMulti {
										var caidItem LanwaRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidList = append(caidList, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
									request.Caids = caidList
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpCPUNum = strconv.Itoa(didRedisData.CPUNum)

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpTimeZone) > 0 && len(tmpCPUNum) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									request.OsUpdateTime = tmpSystemUpdateSec
									request.OsStartupTime = tmpDeviceStartSec
									request.OsBirthTime = tmpDeviceBirthSec
									request.PhoneNameMd5 = tmpDeviceNameMd5
									request.HardwareMachine = tmpHardwareMachine
									request.SysMemory = tmpPhysicalMemoryByte
									request.SysDisksize = tmpHarddiskSizeByte
									request.Language = tmpLanguage
									request.Country = tmpCountry
									request.Timezone = tmpTimeZone
									request.CpuNum = tmpCPUNum
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}
	requestMarshal, _ := json.Marshal(request)

	params := url.Values{}
	params.Set("ad_code", platformPos.PlatformPosID)

	platformAppUpURL := platformPos.PlatformAppUpURL + "?" + params.Encode()

	requestGet, _ := http.NewRequestWithContext(c, "POST", platformAppUpURL, bytes.NewReader(requestMarshal))
	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	if platformPos.PlatformAppIsReportUa == 1 {
		requestGet.Header.Add("User-Agent", mhReq.Device.Ua)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)

	respStu := LanwaResponseObject{}
	_ = json.Unmarshal(bodyContent, &respStu)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if respStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = respStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if respStu.Data == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	for _, bidItem := range respStu.Data {
		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		price := bidItem.Price

		respTmpPrice = respTmpPrice + price

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
			price = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > price {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				if bidItem.Event != nil && bidItem.Event.Lurls != nil {
					go curlLanwaFailedURL(bidItem.Event.Lurls)
				}

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.Event != nil && bidItem.Event.Lurls != nil {
				go curlLanwaFailedURL(bidItem.Event.Lurls)
			}

			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}

			item.Ecpm = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			item.Ecpm = localPos.LocalPosEcpm
		}

		maplehazeAdId := uuid.NewV4().String()

		randPRValue := 95 + rand.Intn(4)
		macroPrice := utils.ConvertIntToString(price * randPRValue / 100)

		switch bidItem.InteractionType {
		case 0, 2:
			item.InteractType = 0
			item.LandpageURL = bidItem.Clk

		case 1:
			item.InteractType = 1
			item.DownloadURL = bidItem.Clk
		default:
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.Event != nil && bidItem.Event.Lurls != nil {
				go curlLanwaFailedURL(bidItem.Event.Lurls)
			}

			continue
		}

		if len(bidItem.Deeplink) > 0 {
			item.DeepLink = bidItem.Deeplink
		}

		// adid
		item.AdID = maplehazeAdId
		item.ReqWidth = platformPos.PlatformPosWidth
		item.ReqHeight = platformPos.PlatformPosHeight
		item.Crid = bidItem.Id
		item.MarketURL = bidItem.MarketUrl

		if bidItem.Appinfo != nil {
			item.AppName = bidItem.Appinfo.AppName
			item.PackageName = bidItem.Appinfo.PackageName
			item.AppVersion = bidItem.Appinfo.AppVersion
			item.Publisher = bidItem.Appinfo.Developer
			item.PrivacyLink = bidItem.Appinfo.PrivacyUrl
			item.PermissionURL = bidItem.Appinfo.PermUrl
			item.Permission = bidItem.Appinfo.PermContent
			item.AppInfoURL = bidItem.Appinfo.FeatureUrl
			item.PackageSize = int64(bidItem.Appinfo.Size)
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					if bidItem.Event != nil && bidItem.Event.Lurls != nil {
						go curlLanwaFailedURL(bidItem.Event.Lurls)
					}

					continue
				} else {
					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		// impression_link
		var respListItemImpArray []string
		mhImpParams := url.Values{}
		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		// click_link
		var respListItemClickArray []string
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)
		respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		if bidItem.Event != nil {
			if len(bidItem.Event.Click) > 0 {
				for _, clkUrl := range bidItem.Event.Click {
					clkUrl = strings.Replace(clkUrl, "__LW_BID_PRICE__", macroPrice, -1)
					clkUrl = strings.Replace(clkUrl, "__WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					clkUrl = strings.Replace(clkUrl, "__HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							clkUrl = strings.Replace(clkUrl, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
							clkUrl = strings.Replace(clkUrl, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							clkUrl = strings.Replace(clkUrl, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							clkUrl = strings.Replace(clkUrl, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						clkUrl = strings.Replace(clkUrl, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkUrl = strings.Replace(clkUrl, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					if platformPos.PlatformPosIsReportSLD == 1 {
						clkUrl = strings.Replace(clkUrl, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					}
					clkUrl = strings.Replace(clkUrl, "__DOWN_X__", tmpDownX, -1)
					clkUrl = strings.Replace(clkUrl, "__DOWN_Y__", tmpDownY, -1)
					clkUrl = strings.Replace(clkUrl, "__UP_X__", tmpUpX, -1)
					clkUrl = strings.Replace(clkUrl, "__UP_Y__", tmpUpY, -1)

					respListItemClickArray = append(respListItemClickArray, clkUrl)
				}
			}

			if len(bidItem.Event.Show) > 0 {
				for _, impUrl := range bidItem.Event.Show {
					impUrl = strings.Replace(impUrl, "__LW_BID_PRICE__", macroPrice, -1)
					impUrl = strings.Replace(impUrl, "__WIDTH__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					impUrl = strings.Replace(impUrl, "__HEIGHT__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							impUrl = strings.Replace(impUrl, "__WIDTH__", utils.ConvertIntToString(tmpWidth), -1)
							impUrl = strings.Replace(impUrl, "__HEIGHT__", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							impUrl = strings.Replace(impUrl, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							impUrl = strings.Replace(impUrl, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						impUrl = strings.Replace(impUrl, "__WIDTH__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						impUrl = strings.Replace(impUrl, "__HEIGHT__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					if platformPos.PlatformPosIsReportSLD == 1 {
						impUrl = strings.Replace(impUrl, "__SLD__", utils.ConvertIntToString(platformPos.PlatformPosSLDType), -1)
					}
					impUrl = strings.Replace(impUrl, "__DOWN_X__", tmpDownX, -1)
					impUrl = strings.Replace(impUrl, "__DOWN_Y__", tmpDownY, -1)
					impUrl = strings.Replace(impUrl, "__UP_X__", tmpUpX, -1)
					impUrl = strings.Replace(impUrl, "__UP_Y__", tmpUpY, -1)

					respListItemImpArray = append(respListItemImpArray, impUrl)
				}
			}

			if len(item.DeepLink) > 0 {
				var convTrackArray []models.MHRespConvTracks
				var convTracks models.MHRespConvTracks
				// deeplink track
				var respListItemSuccDeepLinkArray []string
				if len(bidItem.Event.OpenAppSuccess) > 0 {
					respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, bidItem.Event.OpenAppSuccess...)
				}
				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				mhDPParams.Add("log", bigdataParams)
				respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
				convTracks.ConvType = 10
				convTracks.ConvURLS = respListItemSuccDeepLinkArray
				convTrackArray = append(convTrackArray, convTracks)

				if platformPos.PlatformAppIsDeepLinkFailed == 1 {
					var respListItemDeepLinkFailedArray []string
					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

					if len(bidItem.Event.OpenAppFalse) > 0 {
						respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, bidItem.Event.OpenAppFalse...)
					}

					var deeplinkFailedConvTrack models.MHRespConvTracks
					deeplinkFailedConvTrack.ConvType = 11
					deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
					convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
				}

				item.ConvTracks = convTrackArray
			}
		}

		item.ImpressionLink = respListItemImpArray
		item.ClickLink = respListItemClickArray

		item.IconURL = bidItem.Logo
		// title
		if len(bidItem.Title) > 0 {
			item.Title = bidItem.Title
		}
		// description
		if len(bidItem.Desc) > 0 {
			item.Description = bidItem.Desc
		}
		if len(bidItem.Imgs) == 0 && bidItem.Video == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.Event != nil && bidItem.Event.Lurls != nil {
				go curlLanwaFailedURL(bidItem.Event.Lurls)
			}

			continue
		}
		if bidItem.Imgs != nil {
			var imgList []models.MHRespImage
			for _, imgItem := range bidItem.Imgs {
				var img models.MHRespImage
				img.URL = imgItem
				img.Width = bidItem.AdWidth
				img.Height = bidItem.AdHeight
				imgList = append(imgList, img)
			}
			item.Image = imgList
			item.CrtType = 11
		} else {
			if bidItem.Video != nil {
				var video models.MHRespVideo
				video.VideoURL = bidItem.Video.URL
				video.CoverURL = bidItem.Video.Cover
				video.Duration = bidItem.Video.Duration * 1000
				video.Width = bidItem.Video.Width
				video.Height = bidItem.Video.Height
				item.Video = &video
				item.CrtType = 20
				var videoEventArray []models.MHEventTrackItem
				if bidItem.VideoEvent != nil {
					if len(bidItem.VideoEvent.StartTracks) > 0 {
						videoStart := models.MHEventTrackItem{
							EventType: 100,
							EventURLS: bidItem.VideoEvent.StartTracks,
						}
						videoEventArray = append(videoEventArray, videoStart)
					}
					if len(bidItem.VideoEvent.CompleteTracks) > 0 {
						videoEnd := models.MHEventTrackItem{
							EventType: 103,
							EventURLS: bidItem.VideoEvent.CompleteTracks,
						}
						videoEventArray = append(videoEventArray, videoEnd)
					}
					item.Video.EventTracks = videoEventArray
				}
			}
		}

		if item.Video == nil && item.Image == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			if bidItem.Event != nil && bidItem.Event.Lurls != nil {
				go curlLanwaFailedURL(bidItem.Event.Lurls)
			}

			continue
		}

		// win notice url
		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}
		item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		if bidItem.Event != nil && bidItem.Event.Wurls != nil {
			go curlLanwaSuccURL(bidItem.Event.Wurls, macroPrice)
		}

		item.MaterialDirection = platformPos.PlatformPosDirection
		item.PEcpm = price
		list = append(list, item)
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	lanwaResp := models.MHUpResp{}
	lanwaResp.RespData = &mhResp
	lanwaResp.Extra = bigdataExtra

	return &lanwaResp

}

type LanwaRequestObject struct {
	AdWidth          int                      `json:"ad_width,omitempty"`
	AdHeight         int                      `json:"ad_height,omitempty"`
	SupportDeeplink  int                      `json:"support_deeplink,omitempty"`
	SupportUniversal int                      `json:"support_universal,omitempty"`
	NetType          int                      `json:"net_type,omitempty"`
	ScreenWidth      int                      `json:"screen_width,omitempty"`
	ScreenHeight     int                      `json:"screen_height,omitempty"`
	Dpi              int                      `json:"dpi,omitempty"`
	Ppi              int                      `json:"ppi,omitempty"`
	Platform         int                      `json:"platform,omitempty"`
	Carrier          int                      `json:"carrier,omitempty"`
	Latitude         int                      `json:"latitude,omitempty"`
	Longitude        int                      `json:"longitude,omitempty"`
	Bidfloor         int                      `json:"bidfloor,omitempty"`
	Count            int                      `json:"count,omitempty"`
	ScreenOrient     int                      `json:"screen_orient,omitempty"`
	Density          float64                  `json:"density,omitempty"`
	Osapilevel       float64                  `json:"osapilevel,omitempty"`
	APIVersion       string                   `json:"api_version,omitempty"`
	AppName          string                   `json:"app_name,omitempty"`
	PkgName          string                   `json:"pkg_name,omitempty"`
	AppVersion       string                   `json:"app_version,omitempty"`
	Make             string                   `json:"make,omitempty"`
	Brand            string                   `json:"brand,omitempty"`
	Model            string                   `json:"model,omitempty"`
	Hwv              string                   `json:"hwv,omitempty"`
	AndroidID        string                   `json:"android_id,omitempty"`
	Auidmd5          string                   `json:"auidmd5,omitempty"`
	Auidsha1         string                   `json:"auidsha1,omitempty"`
	Imei             string                   `json:"imei,omitempty"`
	Imeimd5          string                   `json:"imeimd5,omitempty"`
	Imeisha1         string                   `json:"imeisha1,omitempty"`
	Imsi             string                   `json:"imsi,omitempty"`
	Ua               string                   `json:"ua,omitempty"`
	Mac              string                   `json:"mac,omitempty"`
	Os               string                   `json:"os,omitempty"`
	OsVersion        string                   `json:"os_version,omitempty"`
	IP               string                   `json:"ip,omitempty"`
	Serialno         string                   `json:"serialno,omitempty"`
	Iccid            string                   `json:"iccid,omitempty"`
	Bssid            string                   `json:"bssid,omitempty"`
	Pkgs             string                   `json:"pkgs,omitempty"`
	Mnc              string                   `json:"mnc,omitempty"`
	Mcc              string                   `json:"mcc,omitempty"`
	Oaid             string                   `json:"oaid,omitempty"`
	Idfa             string                   `json:"idfa,omitempty"`
	Idfamd5          string                   `json:"idfamd5,omitempty"`
	BootMark         string                   `json:"boot_mark,omitempty"`
	UpdateMark       string                   `json:"update_mark,omitempty"`
	OsBirthTime      string                   `json:"os_birth_time,omitempty"`
	OsCompilingTime  string                   `json:"os_compiling_time,omitempty"`
	OsUpdateTime     string                   `json:"os_update_time,omitempty"`
	OsStartupTime    string                   `json:"os_startup_time,omitempty"`
	PhoneName        string                   `json:"phone_name,omitempty"`
	PhoneNameMd5     string                   `json:"phone_name_md5,omitempty"`
	SysMemory        string                   `json:"sys_memory,omitempty"`
	SysDisksize      string                   `json:"sys_disksize,omitempty"`
	CpuNum           string                   `json:"cpu_num,omitempty"`
	HardwareMachine  string                   `json:"hardware_machine,omitempty"`
	HardwareModel    string                   `json:"hardware_model,omitempty"`
	Country          string                   `json:"country,omitempty"`
	Language         string                   `json:"language,omitempty"`
	Timezone         string                   `json:"timezone,omitempty"`
	AppStoreVersion  string                   `json:"app_store_version,omitempty"`
	RomVersion       string                   `json:"rom_version,omitempty"`
	Installed        []string                 `json:"installed,omitempty"`
	Caids            []LanwaRequestCaidObject `json:"caids,omitempty"`
}

type LanwaRequestCaidObject struct {
	Id      string `json:"id,omitempty"`
	Version string `json:"version,omitempty"`
}

type LanwaResponseObject struct {
	Code   int                        `json:"code"`
	Msg    string                     `json:"msg"`
	Unique string                     `json:"unique"`
	Data   []*LanwaResponseDataObject `json:"data"`
}

type LanwaResponseDataObject struct {
	Price           int                                `json:"price"`
	AdWidth         int                                `json:"ad_width"`
	AdHeight        int                                `json:"ad_height"`
	Replace         int                                `json:"replace"`
	InteractionType int                                `json:"interaction_type"`
	FallbackType    int                                `json:"fallback_type"`
	AdType          int                                `json:"ad_type"`
	IsGdt           int                                `json:"is_gdt"`
	Id              string                             `json:"id"`
	Logo            string                             `json:"ad_logo"`
	Title           string                             `json:"title"`
	Desc            string                             `json:"desc"`
	Clk             string                             `json:"clk"`
	Deeplink        string                             `json:"deeplink"`
	MarketUrl       string                             `json:"market_url"`
	Imgs            []string                           `json:"imgs"`
	Video           *LanwaResponseDataVideoObject      `json:"video"`
	VideoEvent      *LanwaResponseDataVideoEventObject `json:"video_event"`
	Event           *LanwaResponseDataEventObject      `json:"event"`
	Appinfo         *LanwaResponseDataAppinfoObject    `json:"appinfo"`
}

type LanwaResponseDataEventObject struct {
	Wurls          []string `json:"wurls"`
	Lurls          []string `json:"lurls"`
	Show           []string `json:"show"`
	Click          []string `json:"click"`
	StartDown      []string `json:"start_down"`
	DownDone       []string `json:"down_done"`
	StartInstall   []string `json:"start_install"`
	InstallDone    []string `json:"install_done"`
	OpenAppSuccess []string `json:"open_app_success"`
	OpenAppFalse   []string `json:"open_app_false"`
	OpenAppClick   []string `json:"open_app_click"`
	ActiveApp      []string `json:"active_app"`
}

type LanwaResponseDataVideoObject struct {
	URL      string `json:"url"`
	Cover    string `json:"cover"`
	Desc     string `json:"desc"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Duration int    `json:"duration"`
}

type LanwaResponseDataVideoEventObject struct {
	StartTracks             []string `json:"start_tracks"`
	ClickTracks             []string `json:"click_tracks"`
	CompleteTracks          []string `json:"complete_tracks"`
	SkipTracks              []string `json:"skip_tracks"`
	StopTracks              []string `json:"stop_tracks"`
	UnstopTracks            []string `json:"unstop_tracks"`
	MuteTracks              []string `json:"mute_tracks"`
	UnmuteTracks            []string `json:"unmute_tracks"`
	CloseTracks             []string `json:"close_tracks"`
	PlayFirstQuartileTracks []string `json:"play_first_quartile_tracks"`
	PlayMidpointTracks      []string `json:"play_midpoint_tracks"`
	PlayThirdQuartileTracks []string `json:"play_third_quartile_tracks"`
}

type LanwaResponseDataAppinfoObject struct {
	Size        int    `json:"size"`
	AppName     string `json:"app_name"`
	PackageName string `json:"package_name"`
	AppVersion  string `json:"app_version"`
	PrivacyUrl  string `json:"privacy_url"`
	PermUrl     string `json:"perm_url"`
	PermContent string `json:"perm_content"`
	Developer   string `json:"developer"`
	FeatureUrl  string `json:"feature_url"`
}

func curlLanwaFailedURL(urls []string) {
	for _, urlItem := range urls {
		client := &http.Client{Timeout: 1000 * time.Millisecond}
		urlItem = strings.Replace(urlItem, "__ACTION_LOSS__", "0", -1)
		requestGet, _ := http.NewRequest("GET", urlItem, nil)
		requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
		resp, err := client.Do(requestGet)
		if err != nil {
			return
		}

		defer resp.Body.Close()
	}
}

func curlLanwaSuccURL(urls []string, price string) {
	for _, urlItem := range urls {
		client := &http.Client{Timeout: 1000 * time.Millisecond}
		urlItem = strings.Replace(urlItem, "__LW_BID_PRICE__", price, -1)
		requestGet, _ := http.NewRequest("GET", urlItem, nil)
		requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
		resp, err := client.Do(requestGet)
		if err != nil {
			return
		}

		defer resp.Body.Close()
	}
}
