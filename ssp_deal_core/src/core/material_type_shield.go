package core

import (
	"encoding/json"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"net/url"
	"strconv"
	"strings"
)

func IsMaterialTypeShieldOK(localPos *models.LocalPosStu, mhReq *models.MHReq, item *models.MHRespDataItem) (bool, int) {
	downloadAppKey := "go_ssp_type_shield_download_app_" + localPos.LocalAppID
	downloadPosKey := "go_ssp_type_shield_download_pos_" + localPos.LocalPosID
	downloadAppValue, _ := db.GlbBigCacheMinute.Get(downloadAppKey)
	downloadPosValue, _ := db.GlbBigCacheMinute.Get(downloadPosKey)

	dpAppKey := "go_ssp_type_shield_dp_app_" + localPos.LocalAppID
	dpPosKey := "go_ssp_type_shield_dp_pos_" + localPos.LocalPosID
	dpAppValue, _ := db.GlbBigCacheMinute.Get(dpAppKey)
	dpPosValue, _ := db.GlbBigCacheMinute.Get(dpPosKey)

	h5AppKey := "go_ssp_type_shield_h5_app_" + localPos.LocalAppID
	h5PosKey := "go_ssp_type_shield_h5_pos_" + localPos.LocalPosID
	h5AppValue, _ := db.GlbBigCacheMinute.Get(h5AppKey)
	h5PosValue, _ := db.GlbBigCacheMinute.Get(h5PosKey)

	var downloadRuleBigCache models.TypeRuleListBigCache
	var dpRuleBigCache models.TypeRuleListBigCache
	var h5RuleBigCache models.TypeRuleListBigCache

	if len(downloadAppValue) > 0 && len(downloadPosValue) == 0 {
		_ = json.Unmarshal(downloadAppValue, &downloadRuleBigCache)
	}
	if len(downloadPosValue) > 0 {
		_ = json.Unmarshal(downloadPosValue, &downloadRuleBigCache)
	}

	if len(h5AppValue) > 0 && len(h5PosValue) == 0 {
		_ = json.Unmarshal(h5AppValue, &h5RuleBigCache)
	}
	if len(h5PosValue) > 0 {
		_ = json.Unmarshal(h5PosValue, &h5RuleBigCache)
	}

	if len(dpAppValue) > 0 && len(dpPosValue) == 0 {
		_ = json.Unmarshal(dpAppValue, &dpRuleBigCache)
	}
	if len(dpPosValue) > 0 {
		_ = json.Unmarshal(dpPosValue, &dpRuleBigCache)
	}

	if len(downloadAppValue) == 0 && len(downloadPosValue) == 0 && len(h5AppValue) == 0 && len(h5PosValue) == 0 && len(dpAppValue) == 0 && len(dpPosValue) == 0 {
		return true, 900000
	}

	interactType := item.InteractType

	tmpSDKVersion := strings.Split(mhReq.SDKVersion, ".")
	lastReqSDKVersion := tmpSDKVersion[len(tmpSDKVersion)-1]
	if len(lastReqSDKVersion) == 1 {
		lastReqSDKVersion = "0" + lastReqSDKVersion
		tmpSDKVersion[len(tmpSDKVersion)-1] = lastReqSDKVersion
	}

	sDKVersionStr := strings.Replace(strings.Join(tmpSDKVersion, "."), ".", "", -1)
	reqSDKVersion, _ := strconv.Atoi(sDKVersionStr)

	downloadFlag := 0
	dpFlag := 0
	h5Flag := 0

	fmt.Println("reqSDKVersion:", reqSDKVersion)

	if interactType == 1 && (len(downloadAppValue) > 0 || len(downloadPosValue) > 0) {
		if len(downloadRuleBigCache.Sdk) == 0 && len(downloadRuleBigCache.SdkFilter) == 0 {
			if downloadRuleBigCache.AppId == localPos.LocalAppID || downloadRuleBigCache.PosId == localPos.LocalPosID {
				return false, 900111
			}
		}

		tmpSDK := strings.Split(downloadRuleBigCache.Sdk, ".")
		lastReqSDK := tmpSDK[len(tmpSDK)-1]
		if len(lastReqSDK) == 1 {
			lastReqSDK = "0" + lastReqSDK
			tmpSDK[len(tmpSDK)-1] = lastReqSDK
		}
		sdkStr := strings.Replace(strings.Join(tmpSDK, "."), ".", "", -1)
		cacheSDKVersion, _ := strconv.Atoi(sdkStr)
		fmt.Println("cacheSDKVersion:", cacheSDKVersion)
		if reqSDKVersion >= cacheSDKVersion {
			if len(downloadRuleBigCache.SdkFilter) > 0 {
				sdkFilter := strings.Split(downloadRuleBigCache.SdkFilter, ",")
				for _, filterItem := range sdkFilter {
					fmt.Println("filterItem:", filterItem)
					if filterItem == mhReq.SDKVersion {
						downloadFlag = 1
						break
					}
				}

				if downloadFlag == 0 {
					if mhReq.Device.Os == "android" && downloadRuleBigCache.LandingPageSwitch == 1 {
						setLandingPage(item)
					} else {
						return false, 900111
					}
				}
			} else {
				if mhReq.Device.Os == "android" && downloadRuleBigCache.LandingPageSwitch == 1 {
					setLandingPage(item)
				} else {
					return false, 900111
				}
			}
		}
	} else {
		if len(item.LandpageURL) > 0 && (len(h5AppValue) > 0 || len(h5PosValue) > 0) {
			if len(h5RuleBigCache.Sdk) == 0 && len(h5RuleBigCache.SdkFilter) == 0 {
				if h5RuleBigCache.AppId == localPos.LocalAppID || h5RuleBigCache.PosId == localPos.LocalPosID {
					return false, 900111
				}
			}

			tmpSDK := strings.Split(h5RuleBigCache.Sdk, ".")
			lastReqSDK := tmpSDK[len(tmpSDK)-1]
			if len(lastReqSDK) == 1 {
				lastReqSDK = "0" + lastReqSDK
				tmpSDK[len(tmpSDK)-1] = lastReqSDK
			}
			sdkStr := strings.Replace(strings.Join(tmpSDK, "."), ".", "", -1)
			cacheSDKVersion, _ := strconv.Atoi(sdkStr)
			fmt.Println("cacheSDKVersion:", cacheSDKVersion)
			if reqSDKVersion >= cacheSDKVersion {
				if len(h5RuleBigCache.SdkFilter) > 0 {
					sdkFilter := strings.Split(h5RuleBigCache.SdkFilter, ",")
					for _, filterItem := range sdkFilter {
						if filterItem == mhReq.SDKVersion {
							h5Flag = 1
							break
						}
					}
					if h5Flag == 0 {
						return false, 900111
					}
				} else {
					return false, 900111
				}
			}
		}

		if len(item.DeepLink) > 0 && (len(dpAppValue) > 0 || len(dpPosValue) > 0) {
			if len(dpRuleBigCache.Sdk) == 0 && len(dpRuleBigCache.SdkFilter) == 0 {
				if dpRuleBigCache.AppId == localPos.LocalAppID || dpRuleBigCache.PosId == localPos.LocalPosID {
					return false, 900111
				}
			}
			tmpSDK := strings.Split(dpRuleBigCache.Sdk, ".")
			lastReqSDK := tmpSDK[len(tmpSDK)-1]
			if len(lastReqSDK) == 1 {
				lastReqSDK = "0" + lastReqSDK
				tmpSDK[len(tmpSDK)-1] = lastReqSDK
			}
			sdkStr := strings.Replace(strings.Join(tmpSDK, "."), ".", "", -1)
			cacheSDKVersion, _ := strconv.Atoi(sdkStr)
			fmt.Println("cacheSDKVersion:", cacheSDKVersion)
			if reqSDKVersion >= cacheSDKVersion {
				if len(dpRuleBigCache.SdkFilter) > 0 {
					sdkFilter := strings.Split(dpRuleBigCache.SdkFilter, ",")
					for _, filterItem := range sdkFilter {
						if filterItem == mhReq.SDKVersion {
							dpFlag = 1
							break
						}
					}
					if dpFlag == 0 {
						return false, 900111
					}
				} else {
					return false, 900111
				}
			}
		}
	}
	return true, 900000
}

func setLandingPage(item *models.MHRespDataItem) {
	item.InteractType = 0
	if len(item.LandpageURL) == 0 {
		landingPageParams := url.Values{}
		landingPageParams.Add("apkurl", item.DownloadURL)
		landingPageParams.Add("title", item.Title)
		landingPageParams.Add("description", item.Description)
		landingPageParams.Add("icon", item.IconURL)
		if item.Image != nil {
			landingPageParams.Add("image", item.Image[0].URL)
		} else {
			landingPageParams.Add("image", item.Video.CoverURL)
		}
		if len(item.AppVersion) > 0 {
			landingPageParams.Add("version", item.AppVersion)
		} else {
			landingPageParams.Add("version", "1.0.0")
		}
		if len(item.Publisher) > 0 {
			landingPageParams.Add("author", item.Publisher)
		} else {
			landingPageParams.Add("author", "ADS")
		}
		landingPageParams.Add("privacy", item.PrivacyLink)
		maplehazeLandingPageURL := "https://static.maplehaze.cn/static/landingpage" + "?" + landingPageParams.Encode()
		item.AdURL = maplehazeLandingPageURL
		item.LandpageURL = maplehazeLandingPageURL
	}
}
