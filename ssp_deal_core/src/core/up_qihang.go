package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/qihang_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromQiHang ...
func GetFromQiHang(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from qihang")

	// platformPos.PlatformAppUnionBaiduMediaID = "1126"
	// platformPos.PlatformAppID = "b3c3ab06"
	// platformPos.PlatformPosID = "android01"
	// platformPos.PlatformAppBundle = "com.test"
	// platformPos.PlatformAppUpURL = "http://debug.mobads.baidu.com/pbssp"
	// platformPos.PlatformAppVersion = "1.0"
	// categoryInfo.FloorPrice = 15
	// fmt.Println("get from qihang, local app id: ", localPos.LocalAppID)
	// fmt.Println("get from qihang, local pos id: ", localPos.LocalPosID)
	// fmt.Println("get from qihang, local app type: ", localPos.LocalAppType)
	// fmt.Println("get from qihang, platform app id: ", platformPos.PlatformAppID)
	// fmt.Println("get from qihang, platform pos id: ", platformPos.PlatformPosID)
	// fmt.Println("get from qihang, platform pos type: ", platformPos.PlatformPosType)
	// fmt.Println("get from qihang, platform pos direction: ", platformPos.PlatformPosDirection)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	// tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	// tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	// tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	// tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	// device
	qihangReqDevice := &qihang_up.BidRequest_Device{}

	// device ip
	qihangReqDevice.Ip = proto.String(mhReq.Device.IP)

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		qihangReqDevice.Ua = proto.String(destConfigUA)
	}

	// device carrier
	if mhReq.Network.Carrier == 1 {
		qihangReqDevice.Carrier = proto.Int32(1)
	} else if mhReq.Network.Carrier == 2 {
		qihangReqDevice.Carrier = proto.Int32(2)
	} else if mhReq.Network.Carrier == 3 {
		qihangReqDevice.Carrier = proto.Int32(3)
	} else {
		qihangReqDevice.Carrier = proto.Int32(0)
	}

	// device network
	if mhReq.Network.ConnectType == 0 {
		qihangReqDevice.ConnectionType = proto.Int32(0)
	} else if mhReq.Network.ConnectType == 1 {
		qihangReqDevice.ConnectionType = proto.Int32(2)
	} else if mhReq.Network.ConnectType == 2 {
		qihangReqDevice.ConnectionType = proto.Int32(4)
	} else if mhReq.Network.ConnectType == 3 {
		qihangReqDevice.ConnectionType = proto.Int32(5)
	} else if mhReq.Network.ConnectType == 4 {
		qihangReqDevice.ConnectionType = proto.Int32(6)
	} else if mhReq.Network.ConnectType == 7 {
		qihangReqDevice.ConnectionType = proto.Int32(7)
	}

	// device type
	qihangReqDevice.DeviceType = proto.Int32(4)

	// device os
	if mhReq.Device.Os == "android" {
		qihangReqDevice.Os = proto.String("android")
	} else if mhReq.Device.Os == "ios" {
		qihangReqDevice.Os = proto.String("ios")
	}

	// device osv
	qihangReqDevice.Osv = proto.String(mhReq.Device.OsVersion)

	// device model
	qihangReqDevice.Model = proto.String(mhReq.Device.Model)

	// device make
	qihangReqDevice.Make = proto.String(mhReq.Device.Manufacturer)

	// device bootmark
	qihangReqDevice.BootMark = proto.String(mhReq.Device.BootMark)

	// device updatemark
	qihangReqDevice.UpdateMark = proto.String(mhReq.Device.UpdateMark)

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				qihangReqDevice.ImeiMd5 = proto.String(utils.GetMd5(mhReq.Device.Imei))

			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				qihangReqDevice.ImeiMd5 = proto.String(strings.ToLower(mhReq.Device.ImeiMd5))
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				qihangReqDevice.Oaid = proto.String(mhReq.Device.Oaid)
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from qihang error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(mhReq.Device.Idfa))

			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
							tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

							qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
						tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
						tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

						qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
		}
		if strings.Contains(iosReportMainParameter, "paid") {
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(mhReq.Device.Idfa))

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
								tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
								tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

								qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
							tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

							qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(mhReq.Device.Idfa))

				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
								tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
								tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

								qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)

								break
							}
						}
					} else {
						for _, item := range mhReq.Device.CAIDMulti {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
							tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
							tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

							qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from qihang error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug qihang android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					qihangReqDevice.Osv = proto.String(didRedisData.OsVersion)

					// device model
					qihangReqDevice.Model = proto.String(didRedisData.Model)

					// device make
					qihangReqDevice.Make = proto.String(didRedisData.Manufacturer)

					qihangReqDevice.ImeiMd5 = proto.String("")
					qihangReqDevice.Oaid = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							qihangReqDevice.ImeiMd5 = proto.String(utils.GetMd5(didRedisData.Imei))
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							qihangReqDevice.ImeiMd5 = proto.String(utils.GetMd5(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							qihangReqDevice.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						qihangReqDevice.Ua = proto.String(didRedisData.Ua)

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug qihang ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						qihangReqDevice.Osv = proto.String(didRedisData.OsVersion)

						// device model
						qihangReqDevice.Model = proto.String(didRedisData.Model)

						// device make
						qihangReqDevice.Make = proto.String(didRedisData.Manufacturer)

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						// 初始化为空
						qihangReqDevice.IdfaMd5 = proto.String("")
						qihangReqDevice.CaidInfos = nil

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(didRedisData.Idfa))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

								qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
										tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

										qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)

										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
									tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
									tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

									qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(didRedisData.Idfa))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
											tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
											tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

											qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)

											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
										tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

										qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									qihangReqDevice.IdfaMd5 = proto.String(utils.GetMd5(didRedisData.Idfa))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {

									qihangReqDevice.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
											tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
											tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

											qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)

											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										tmpQihhangReqDeviceCaid := &qihang_up.BidRequest_Device_CaidInfo{}
										tmpQihhangReqDeviceCaid.Caid = proto.String(item.CAID)
										tmpQihhangReqDeviceCaid.Version = proto.String(item.CAIDVersion)

										qihangReqDevice.CaidInfos = append(qihangReqDevice.CaidInfos, tmpQihhangReqDeviceCaid)
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							qihangReqDevice.Ua = proto.String(didRedisData.Ua)

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from qihang error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from qihang error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)

		// debug
		// if platformPos.PlatformAppID == "1109920434" {
		// 	models.BigDataDebugReq(c, bigdataUID, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
		// }
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// req imp
	qihangReqImp := &qihang_up.BidRequest_Imp{
		Id:       proto.String("1"),
		TagId:    proto.String(platformPos.PlatformPosID),
		SubTagId: proto.String(platformPos.PlatformPosID),
		BidFloor: proto.Int64(int64(localPosFloorPrice)),
		BidType:  proto.Int32(0),
	}
	// 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	if platformPos.PlatformPosType == 1 {
		qihangReqImp.AdType = proto.Int32(3)
	} else if platformPos.PlatformPosType == 2 {
		qihangReqImp.AdType = proto.Int32(2)
	} else if platformPos.PlatformPosType == 3 {
		qihangReqImp.AdType = proto.Int32(4)
	} else if platformPos.PlatformPosType == 4 {
		qihangReqImp.AdType = proto.Int32(1)
	} else if platformPos.PlatformPosType == 11 {
		qihangReqImp.AdType = proto.Int32(9)
	} else {
		bigdataExtra.InternalCode = 900101
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(platformPos.PlatformPosStyleIDs) > 0 {
		platformPosStyles := strings.Split(platformPos.PlatformPosStyleIDs, ",")
		for _, styleItem := range platformPosStyles {
			qihangReqImpAsset := &qihang_up.BidRequest_Imp_Asset{
				TemplateId: proto.String(styleItem),
				Width:      proto.Int32(int32(platformPos.PlatformPosWidth)),
				Height:     proto.Int32(int32(platformPos.PlatformPosHeight)),
			}

			qihangReqImp.Asset = append(qihangReqImp.Asset, qihangReqImpAsset)
		}
	}

	// req app
	qihangReqApp := &qihang_up.BidRequest_App{
		Name:   proto.String(platformPos.PlatformAppName),
		Bundle: proto.String(GetAppBundleByConfig(c, mhReq, localPos, platformPos)),
		Verion: proto.String(platformPos.PlatformAppVersion),
	}

	qihangReqPb := &qihang_up.BidRequest{
		Id:         proto.String(bigdataUID),
		ApiVersion: proto.String("1.0.0"),
		Test:       proto.Uint32(0),
		Timeout:    proto.Int32(int32(timeout)),
		App:        qihangReqApp,
		Device:     qihangReqDevice,
		MediaTime:  proto.Int64(utils.GetCurrentMilliSecond()),
		SspTime:    proto.Int64(utils.GetCurrentMilliSecond()),
	}
	qihangReqPb.Imp = append(qihangReqPb.Imp, qihangReqImp)

	// installed app
	qihangReqCustomizedUserTagInstalledApp := &qihang_up.BidRequest_CustomizedUserTag_InstalledApp{
		Id: proto.Uint32(1),
		// Name: proto.String("淘宝"),
	}
	qihangReqPbCustomizedUserTag := &qihang_up.BidRequest_CustomizedUserTag{}
	qihangReqPbCustomizedUserTag.InstalledAppList = append(qihangReqPbCustomizedUserTag.InstalledAppList, qihangReqCustomizedUserTagInstalledApp)
	qihangReqPb.CustomizedUserTag = qihangReqPbCustomizedUserTag

	qihangReqPbByte, xxxxxx := proto.Marshal(qihangReqPb)
	if xxxxxx != nil {
		panic(xxxxxx)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	tmpReqByte, _ := json.Marshal(qihangReqPb)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("qihang url:", platformPos.PlatformAppUpURL)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("qihang req:", string(tmpReqByte))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(qihangReqPbByte))

	requestGet.Header.Add("Content-Type", "application/octet-stream; charset=utf-8")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println("qihang resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	qihangResp := &qihang_up.BidResponse{}
	err = proto.Unmarshal(bodyContent, qihangResp)
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println("========================================================================================================================")
	tmpRespByte, _ := json.Marshal(qihangResp)
	// fmt.Println("qihang resp code:", resp.StatusCode)
	fmt.Println("qihang resp:", string(tmpRespByte))
	fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if rand.Intn(5000000) == 0 {
		go models.BigDataHoloDebugJson2(bigdataUID+"&qihang", string(tmpRespByte), string(tmpReqByte), localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if qihangResp.GetNbr() != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(qihangResp.GetNbr())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(qihangResp.SeatBid) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(qihangResp.GetNbr())

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(qihangResp.SeatBid[0].Bid) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = int(qihangResp.GetNbr())

		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// if platformPos.PlatformAppID == "dae57f48" || platformPos.PlatformAppID == "df53e75a" {
	// 	go func() {
	// 		defer func() {
	// 			if err := recover(); err != nil {
	// 				fmt.Println("debug panic:", err)
	// 			}
	// 		}()

	// 		tmpRespByte, _ := json.Marshal(qihangResp)
	// 		models.SaveDemandRespDataToHolo(c, bigdataUID, platformPos.PlatformAppID, platformPos.PlatformPosID, string(tmpRespByte))
	// 	}()
	// }
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := qihangResp.SeatBid[0].Bid

	if len(respDataListItemStu) > 1 {
		sort.Sort(qihangEcpmSort(respDataListItemStu))
	}

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		respTmpPrice = respTmpPrice + int(item.GetPrice())

		// ad_type返回的基本是3: deeplink+普通网址类, 其他值可不关心. 可以只处理这一种类型.
		// 只可能是3或者4，不会出现其他的值。
		if item.GetAdm().GetAdType() == 3 {
		} else {
			continue
		}

		// ecpm
		qihangEcpm := int(item.GetPrice())

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if qihangEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			qihangEcpm = platformPos.PlatformPosEcpm
		}

		qihangLossNoticeURL := getQiHangPriceFailedURL(item.GetLurl(), platformPos, qihangEcpm)

		// fmt.Println("gdt_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(qihangEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > qihangEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
			continue
		}

		// win_notice价格替换宏
		encodePriceWinHexValue := "${AUCTION_PRICE}"
		if qihangEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			// 随机95-98%替换价格宏
			randPRValue := 100
			if platformPos.PlatformAppReportWinType == 0 {
				randPRValue = 100
			} else if platformPos.PlatformAppReportWinType == 1 {
				tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
				tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
				if tmp1 <= tmp2 {
					randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
				}
			}
			randPrice := int(qihangEcpm * randPRValue / 100)

			encodePriceValue := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(randPrice)), []byte(platformPos.PlatformAppPriceEncrypt))
			encodePriceWinHexValue = string(utils.HexEncode(string(encodePriceValue)))
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(qihangEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := item.GetAdm().GetTitle()
		respDescription := item.GetAdm().GetDesc()
		respIconURL := item.GetIconUrl()
		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		if len(item.GetAdm().GetImage()) > 0 {
			respImageURL = string(item.GetAdm().GetImage()[0].GetUrl())
			respImageWidth = int(item.GetAdm().GetImage()[0].GetWidth())
			respImageHeight = int(item.GetAdm().GetImage()[0].GetHeight())
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		if mhReq.Device.Os == "android" {
			if item.GetAdm().GetAdType() == 3 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = string(item.GetAdm().GetLandingSite())
				respListItemMap["landpage_url"] = string(item.GetAdm().GetLandingSite())
			} else {
				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}

			if len(string(item.GetAppName())) > 0 {
				respListItemMap["app_name"] = item.GetAppName()
			} else {
				// respListItemMap["app_name"] = "手机淘宝"
			}
		} else if mhReq.Device.Os == "ios" {
			if len(string(item.GetAdm().GetLandingSite())) > 0 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.GetAdm().GetLandingSite()
				respListItemMap["landpage_url"] = item.GetAdm().GetLandingSite()
			} else {
				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}
		}

		if len(string(item.GetAdm().GetVideo().GetVideoUrl())) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.GetAdm().GetVideo().GetDuration() > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(item.GetAdm().GetVideo().GetDuration()))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
					continue
				}

				respListVideoItemMap["duration"] = item.GetAdm().GetVideo().GetDuration() * 1000
			}
			respListVideoItemMap["width"] = item.GetAdm().GetVideo().GetWidth()
			respListVideoItemMap["height"] = item.GetAdm().GetVideo().GetHeight()
			respListVideoItemMap["video_url"] = item.GetAdm().GetVideo().GetVideoUrl()

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(item.GetAdm().GetVideo().GetWidth()), int(item.GetAdm().GetVideo().GetHeight()))
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}

			// cover_url
			if len(item.GetAdm().GetCoverImgUrl()) > 0 {
				respListVideoItemMap["cover_url"] = item.GetAdm().GetCoverImgUrl()
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL
				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		if len(string(item.GetCreativeId())) > 0 {
			respListItemMap["crid"] = item.GetCreativeId()
		}

		// deeplink
		destDeepLink := ""
		if len(item.GetAdm().GetDeepLink()) > 0 || len(item.GetAdm().GetUniversalLink()) > 0 {
			if len(item.GetAdm().GetDeepLink()) > 0 {
				destDeepLink = item.GetAdm().GetDeepLink()
			} else if len(item.GetAdm().GetUniversalLink()) > 0 {
				destDeepLink = item.GetAdm().GetUniversalLink()
			}

			respListItemMap["deep_link"] = destDeepLink

			// deeplink track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(item.GetPackageName()) > 0 {
			respListItemMap["package_name"] = item.GetPackageName()

			destPackageName = item.GetPackageName()
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, string(item.GetAppName()), destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, qihangEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		qihangWinNoticeURL := getQiHangPriceWinURL(item.GetLurl(), platformPos, qihangEcpm, encodePriceWinHexValue)
		if len(qihangWinNoticeURL) > 0 {
			var tmpWinNoticeURLs []string
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, qihangWinNoticeURL)
			tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
			tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
			mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
		}

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link qihang
		for _, showItem := range item.GetImpTrackers() {
			tmpItem := string(showItem)
			tmpItem = strings.Replace(tmpItem, "${AUCTION_PRICE}", encodePriceWinHexValue, -1)

			respListItemImpArray = append(respListItemImpArray, tmpItem)
		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, clickItem := range item.GetClkTrackers() {
			tmpItem := clickItem
			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					tmpItem = tmpItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}
			respListItemClkArray = append(respListItemClkArray, string(tmpItem))
		}

		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			if len(qihangLossNoticeURL) > 0 {
				var tmpLossNoticeURLs []string
				tmpLossNoticeURLs = append(tmpLossNoticeURLs, qihangLossNoticeURL)
				tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
				tmpEncryptLossArray := utils.AesECBEncrypt([]byte(tmpLossArrayJSON), []byte(config.EncryptKEY))
				mhLossNoticeURLParams.Add("demand_loss_links", string(base64.StdEncoding.EncodeToString(tmpEncryptLossArray)))
			}
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			curlQiHangPriceFailedURL(qihangLossNoticeURL, &bigdataExtra)
			continue
		}

		respListItemMap["p_ecpm"] = qihangEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("qihang resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// qihang resp
	respGdt := models.MHUpResp{}
	respGdt.RespData = &mhResp
	respGdt.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respGdt
}

type qihangEcpmSort []*qihang_up.BidResponse_Bid

func (s qihangEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s qihangEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s qihangEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].GetPrice() > s[j].GetPrice()
}

func getQiHangPriceWinURL(winNoticeURL string, platformPos *models.PlatformPosStu, qihangEcpm int, encodePriceWinHexValue string) string {
	if qihangEcpm <= 0 || len(winNoticeURL) == 0 {
		return ""
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return ""
	}

	tmpWinNoticeURL := strings.Replace(winNoticeURL, "${AUCTION_PRICE}", encodePriceWinHexValue, -1)
	return tmpWinNoticeURL
}

func getQiHangPriceFailedURL(lossNoticeURL string, platformPos *models.PlatformPosStu, qihangEcpm int) string {
	tmpRandValue := 100
	tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
	tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
	if tmp1 <= tmp2 {
		tmpRandValue = tmp1 + rand.Intn(tmp2-tmp1+1)
	} else {
		return ""
	}

	if qihangEcpm > 0 && len(lossNoticeURL) > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		randPrice := int(qihangEcpm * tmpRandValue / 100)

		encodePriceValue := utils.AesECBEncrypt([]byte(utils.ConvertIntToString(randPrice)), []byte(platformPos.PlatformAppPriceEncrypt))
		encodePriceHexValue := string(utils.HexEncode(string(encodePriceValue)))

		tmpNoticeURL := strings.Replace(lossNoticeURL, "${AUCTION_PRICE}", encodePriceHexValue, -1)

		return tmpNoticeURL
	}

	return ""
}

// curl price failed
func curlQiHangPriceFailedURL(lossNoticeURL string, bigdataExtra *models.MHUpRespExtra) {
	// winurl ok
	if len(lossNoticeURL) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("ks loss url panic:", err)
				}
			}()

			utils.CurlWinLossNoticeURL(lossNoticeURL)
		}()

		bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
	}
}
