package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromBaiHe ...
func GetFromBaiHe(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType int
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = 1
	case 2:
		connectionType = 2
	case 3:
		connectionType = 3
	case 4, 5, 6:
		connectionType = 4
	case 7:
		connectionType = 5
	default:
		connectionType = 0
	}

	var carrier int
	switch mhReq.Network.Carrier {
	case 1:
		carrier = 1
	case 2:
		carrier = 3
	case 3:
		carrier = 2
	default:
		carrier = 0
	}

	var orientation int
	switch platformPos.PlatformPosDirection {
	case 0:
		orientation = 3
	case 1:
		orientation = 1
	case 2:
		//orientation = 0
	}

	request := BaiHeRequestObject{
		AccessSign:     "mt111",
		Sid:            platformPos.PlatformPosID,
		UserID:         "0",
		Name:           platformPos.PlatformAppID,
		Position:       "1",
		Bundle:         GetAppBundleByConfig(c, mhReq, localPos, platformPos),
		Brand:          mhReq.Device.Manufacturer,
		Make:           mhReq.Device.Manufacturer,
		Model:          mhReq.Device.Model,
		Mac:            mhReq.Device.Mac,
		Ip:             mhReq.Device.IP,
		OsType:         mhReq.Device.Os,
		OsVersion:      mhReq.Device.OsVersion,
		ConnectionType: connectionType,
		Orientation:    orientation,
		Carrier:        carrier,
		Mtprice:        categoryInfo.FloorPrice,
		Screen: BaiHeRequestScreenObject{
			W: platformPos.PlatformPosWidth,
			H: platformPos.PlatformPosHeight,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Imei = mhReq.Device.Imei
				request.ImeiMD5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.ImeiMD5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Oaid = mhReq.Device.Oaid
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true
				request.OaidMD5 = strings.ToLower(mhReq.Device.OaidMd5)
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					request.AndroidId = mhReq.Device.AndroidID
					if len(mhReq.Device.AndroidIDMd5) > 0 {
						request.AndroididMd5 = mhReq.Device.AndroidIDMd5
					}
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Idfa = mhReq.Device.Idfa
				request.IdfaMD5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				request.IdfaMD5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var caidArray []BaiHeRequestCaidObject
				for _, item := range mhReq.Device.CAIDMulti {
					var caidItem BaiHeRequestCaidObject
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion

						caidArray = append(caidArray, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
				request.Caid = caidArray
			} else {
				var caidArray []BaiHeRequestCaidObject
				for _, item := range mhReq.Device.CAIDMulti {
					var caidItem BaiHeRequestCaidObject
					caidItem.Id = item.CAID
					caidItem.Version = item.CAIDVersion

					caidArray = append(caidArray, caidItem)

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
				request.Caid = caidArray
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				request.DeviceStartSec = tmpDeviceStartSec
				request.Country = tmpCountry
				request.Language = tmpLanguage
				request.DeviceNameMd5 = tmpDeviceNameMd5
				request.HardwareMachine = tmpHardwareMachine
				request.HardwareModel = tmpHardwareModel
				request.PhysicalMemoryByte = tmpPhysicalMemoryByte
				request.HarddiskSizeByte = tmpHarddiskSizeByte
				request.SystemUpdateSec = tmpSystemUpdateSec
				request.TimeZone = tmpTimeZone
				request.BirthTime = tmpDeviceBirthSec
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfa = mhReq.Device.Idfa
					request.IdfaMD5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.IdfaMD5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []BaiHeRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						var caidItem BaiHeRequestCaidObject
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							caidItem.Id = item.CAID
							caidItem.Version = item.CAIDVersion

							caidArray = append(caidArray, caidItem)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}

					request.Caid = caidArray
				} else {
					var caidArray []BaiHeRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						var caidItem BaiHeRequestCaidObject
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion

						caidArray = append(caidArray, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
					request.Caid = caidArray
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
					request.DeviceStartSec = tmpDeviceStartSec
					request.Country = tmpCountry
					request.Language = tmpLanguage
					request.DeviceNameMd5 = tmpDeviceNameMd5
					request.HardwareMachine = tmpHardwareMachine
					request.HardwareModel = tmpHardwareModel
					request.PhysicalMemoryByte = tmpPhysicalMemoryByte
					request.HarddiskSizeByte = tmpHarddiskSizeByte
					request.SystemUpdateSec = tmpSystemUpdateSec
					request.TimeZone = tmpTimeZone
					request.BirthTime = tmpDeviceBirthSec

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Idfa = mhReq.Device.Idfa
					request.IdfaMD5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.IdfaMD5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []BaiHeRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidItem BaiHeRequestCaidObject
							caidItem.Id = item.CAID
							caidItem.Version = item.CAIDVersion

							caidArray = append(caidArray, caidItem)

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
					request.Caid = caidArray
				} else {
					var caidArray []BaiHeRequestCaidObject
					for _, item := range mhReq.Device.CAIDMulti {
						var caidItem BaiHeRequestCaidObject
						caidItem.Id = item.CAID
						caidItem.Version = item.CAIDVersion

						caidArray = append(caidArray, caidItem)

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
					request.Caid = caidArray
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
					request.DeviceStartSec = tmpDeviceStartSec
					request.Country = tmpCountry
					request.Language = tmpLanguage
					request.DeviceNameMd5 = tmpDeviceNameMd5
					request.HardwareMachine = tmpHardwareMachine
					request.HardwareModel = tmpHardwareModel
					request.PhysicalMemoryByte = tmpPhysicalMemoryByte
					request.HarddiskSizeByte = tmpHarddiskSizeByte
					request.SystemUpdateSec = tmpSystemUpdateSec
					request.TimeZone = tmpTimeZone
					request.BirthTime = tmpDeviceBirthSec

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.OsType = "ios"
					case "android":
						request.OsType = "android"
					}
					request.OsVersion = didRedisData.OsVersion
					request.Model = didRedisData.Model
					request.Make = didRedisData.Manufacturer
					request.Brand = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Imei = didRedisData.Imei
							request.ImeiMD5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.ImeiMD5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Oaid = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								request.AndroidId = didRedisData.AndroidID
								if len(didRedisData.AndroidIDMd5) > 0 {
									request.AndroididMd5 = didRedisData.AndroidIDMd5
								}
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.OsType = "ios"
						case "android":
							request.OsType = "android"
						}
						request.OsVersion = didRedisData.OsVersion
						request.Model = didRedisData.Model
						request.Make = didRedisData.Manufacturer
						request.Brand = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Idfa = didRedisData.Idfa
								request.IdfaMD5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.IdfaMD5 = strings.ToLower(didRedisData.IdfaMd5)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidArray []BaiHeRequestCaidObject
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidItem BaiHeRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidArray = append(caidArray, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}

								request.Caid = caidArray
							} else {
								var caidArray []BaiHeRequestCaidObject
								for _, item := range tmpCAIDMulti {
									var caidItem BaiHeRequestCaidObject
									caidItem.Id = item.CAID
									caidItem.Version = item.CAIDVersion

									caidArray = append(caidArray, caidItem)

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
								}
								request.Caid = caidArray
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone
							tmpHardwareMachine = didRedisData.HardwareMachine

							if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
								request.DeviceStartSec = tmpDeviceStartSec
								request.Country = tmpCountry
								request.Language = tmpLanguage
								request.DeviceNameMd5 = tmpDeviceNameMd5
								request.HardwareMachine = tmpHardwareMachine
								request.HardwareModel = tmpHardwareModel
								request.PhysicalMemoryByte = tmpPhysicalMemoryByte
								request.HarddiskSizeByte = tmpHarddiskSizeByte
								request.SystemUpdateSec = tmpSystemUpdateSec
								request.TimeZone = tmpTimeZone
								request.BirthTime = tmpDeviceBirthSec

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Idfa = didRedisData.Idfa
									request.IdfaMD5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.IdfaMD5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []BaiHeRequestCaidObject
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidItem BaiHeRequestCaidObject
											caidItem.Id = item.CAID
											caidItem.Version = item.CAIDVersion

											caidArray = append(caidArray, caidItem)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
									request.Caid = caidArray
								} else {
									var caidArray []BaiHeRequestCaidObject
									for _, item := range tmpCAIDMulti {
										var caidItem BaiHeRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidArray = append(caidArray, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
									request.Caid = caidArray
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpHardwareMachine = didRedisData.HardwareMachine

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
									request.DeviceStartSec = tmpDeviceStartSec
									request.Country = tmpCountry
									request.Language = tmpLanguage
									request.DeviceNameMd5 = tmpDeviceNameMd5
									request.HardwareMachine = tmpHardwareMachine
									request.HardwareModel = tmpHardwareModel
									request.PhysicalMemoryByte = tmpPhysicalMemoryByte
									request.HarddiskSizeByte = tmpHarddiskSizeByte
									request.SystemUpdateSec = tmpSystemUpdateSec
									request.TimeZone = tmpTimeZone
									request.BirthTime = tmpDeviceBirthSec

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Idfa = didRedisData.Idfa
									request.IdfaMD5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.IdfaMD5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []BaiHeRequestCaidObject
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidItem BaiHeRequestCaidObject
											caidItem.Id = item.CAID
											caidItem.Version = item.CAIDVersion

											caidArray = append(caidArray, caidItem)

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
									request.Caid = caidArray
								} else {
									var caidArray []BaiHeRequestCaidObject
									for _, item := range tmpCAIDMulti {
										var caidItem BaiHeRequestCaidObject
										caidItem.Id = item.CAID
										caidItem.Version = item.CAIDVersion

										caidArray = append(caidArray, caidItem)

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
									request.Caid = caidArray
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone
								tmpHardwareMachine = didRedisData.HardwareMachine

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 && len(tmpDeviceBirthSec) > 0 && len(tmpDeviceStartSec) > 0 && len(tmpDeviceNameMd5) > 0 && len(tmpHardwareModel) > 0 && len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 && len(tmpTimeZone) > 0 && len(tmpHardwareMachine) > 0 {
									request.DeviceStartSec = tmpDeviceStartSec
									request.Country = tmpCountry
									request.Language = tmpLanguage
									request.DeviceNameMd5 = tmpDeviceNameMd5
									request.HardwareMachine = tmpHardwareMachine
									request.HardwareModel = tmpHardwareModel
									request.PhysicalMemoryByte = tmpPhysicalMemoryByte
									request.HarddiskSizeByte = tmpHarddiskSizeByte
									request.SystemUpdateSec = tmpSystemUpdateSec
									request.TimeZone = tmpTimeZone
									request.BirthTime = tmpDeviceBirthSec

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	requestMarshal, _ := json.Marshal(request)
	requestGet, _ := http.NewRequestWithContext(c, "GET", platformPos.PlatformAppUpURL, nil)
	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	if platformPos.PlatformAppIsReportUa == 1 {
		requestGet.Header.Add("User-Agent", request.Ua)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	q := requestGet.URL.Query()
	q.Add("params", string(requestMarshal))
	requestGet.URL.RawQuery = q.Encode()

	resp, err := client.Do(requestGet)
	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)

	baiheRespStu := BaiHeResponseObject{}
	_ = json.Unmarshal(bodyContent, &baiheRespStu)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	if baiheRespStu.Code != 200 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if baiheRespStu.Data == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	for _, bidItem := range baiheRespStu.Data.Result {
		respTmpRespAllNum = respTmpRespAllNum + 1
		// ecpm
		priceFloat, _ := strconv.ParseFloat(bidItem.Price, 64)
		price := int(priceFloat * 100)

		respTmpPrice = respTmpPrice + price

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
			price = platformPos.PlatformPosEcpm
		}

		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			// 价格拦截, 跟之前一样
			if localPosFinalPrice > price {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			item.Ecpm = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			item.Ecpm = localPos.LocalPosEcpm
		}

		if bidItem.Link == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		if strings.Contains(bidItem.Link.Url, ".apk") || strings.Contains(bidItem.Link.Url, "download") {
			item.InteractType = 1
			item.DownloadURL = bidItem.Link.Url
		} else {
			item.InteractType = 0
			item.LandpageURL = bidItem.Link.Url
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		item.AdID = maplehazeAdId
		item.ReqWidth = platformPos.PlatformPosWidth
		item.ReqHeight = platformPos.PlatformPosHeight
		item.IconURL = bidItem.LogoUrl
		item.DeepLink = bidItem.Deeplink

		// title
		if len(bidItem.Title) > 0 {
			item.Title = bidItem.Title
		}

		// description
		if len(bidItem.Introduction) > 0 {
			item.Description = bidItem.Introduction
		}

		if bidItem.Pic == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		var imgList []models.MHRespImage
		var img models.MHRespImage
		img.URL = bidItem.Pic.Url
		img.Width = bidItem.Pic.W
		img.Height = bidItem.Pic.H
		imgList = append(imgList, img)
		item.Image = imgList
		item.CrtType = 11

		if item.Image == nil {
			bigdataExtra.InternalCode = 900105
			bigdataExtra.ExternalCode = 102006
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		tmpDownX := ""
		tmpDownY := ""
		tmpUpX := ""
		tmpUpY := ""

		tmpIsServerRealReplaceXY := false
		tmpServerRealReplaceXYType := 0
		if platformPos.PlatformPosIsReplaceXY == 1 {
			if platformPos.PlatformPosReplaceXYType == 0 {
				redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
				if redisErr != nil {
					bigdataExtra.InternalCode = 900101
					bigdataExtra.ExternalCode = 102006
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				} else {
					tmpDownX = utils.ConvertIntToString(redisDownX)
					tmpDownY = utils.ConvertIntToString(redisDownY)
					tmpUpX = utils.ConvertIntToString(redisUpX)
					tmpUpY = utils.ConvertIntToString(redisUpY)

					if redisClickXYType == "0" { // 物理像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 0
					} else if redisClickXYType == "1" { // 逻辑像素
						tmpIsServerRealReplaceXY = true
						tmpServerRealReplaceXYType = 1
					}
				}
			} else if platformPos.PlatformPosReplaceXYType == 1 {
				// -999
				tmpDownX = "-999"
				tmpDownY = "-999"
				tmpUpX = "-999"
				tmpUpY = "-999"
			} else if platformPos.PlatformPosReplaceXYType == 2 {
				// 替换空
				tmpDownX = ""
				tmpDownY = ""
				tmpUpX = ""
				tmpUpY = ""
			} else if platformPos.PlatformPosReplaceXYType == 3 {
				// 替换0
				tmpDownX = "0"
				tmpDownY = "0"
				tmpUpX = "0"
				tmpUpY = "0"
			}
		}

		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

		if len(bidItem.Deeplink) > 0 {
			var convTrackArray []models.MHRespConvTracks
			var convTracks models.MHRespConvTracks
			// deeplink track
			var respListItemSuccDeepLinkArray []string
			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			mhDPParams.Add("log", bigdataParams)
			respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
			convTracks.ConvType = 10
			convTracks.ConvURLS = respListItemSuccDeepLinkArray
			convTrackArray = append(convTrackArray, convTracks)

			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string
				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				var deeplinkFailedConvTrack models.MHRespConvTracks
				deeplinkFailedConvTrack.ConvType = 11
				deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
				convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
			}

			item.ConvTracks = convTrackArray
		}

		randPRValue := 95 + rand.Intn(4)
		macroPrice := float64(price*randPRValue/100) / 100
		macroPriceStr := strconv.FormatFloat(macroPrice, 'f', -1, 64)

		// impression_link
		var respListItemImpArray []string
		mhImpParams := url.Values{}
		mhImpParams.Add("log", bigdataParams)
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		if len(bidItem.View.TraceUrl) > 0 {
			for _, impUrl := range bidItem.View.TraceUrl {
				impUrl = strings.Replace(impUrl, "__FINALPRICE__", macroPriceStr, -1)
				if bidItem.Click.ClickParams != nil {
					if len(bidItem.Click.ClickParams.DownX) > 0 {
						impUrl = strings.Replace(impUrl, bidItem.Click.ClickParams.DownX, tmpDownX, -1)
					}
					if len(bidItem.Click.ClickParams.DownY) > 0 {
						impUrl = strings.Replace(impUrl, bidItem.Click.ClickParams.DownY, tmpDownY, -1)
					}
					if len(bidItem.Click.ClickParams.UpX) > 0 {
						impUrl = strings.Replace(impUrl, bidItem.Click.ClickParams.UpX, tmpUpX, -1)
					}
					if len(bidItem.Click.ClickParams.UpY) > 0 {
						impUrl = strings.Replace(impUrl, bidItem.Click.ClickParams.UpY, tmpUpY, -1)
					}
				}

				respListItemImpArray = append(respListItemImpArray, impUrl)
			}
		}
		item.ImpressionLink = respListItemImpArray

		// click_link
		var respListItemClickArray []string
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)
		respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
		if len(bidItem.Click.TraceUrl) > 0 {
			for _, clkUrl := range bidItem.Click.TraceUrl {
				clkUrl = strings.Replace(clkUrl, "__FINALPRICE__", macroPriceStr, -1)
				if bidItem.Click.ClickParams != nil {
					if len(bidItem.Click.ClickParams.DownX) > 0 {
						clkUrl = strings.Replace(clkUrl, bidItem.Click.ClickParams.DownX, tmpDownX, -1)
					}
					if len(bidItem.Click.ClickParams.DownY) > 0 {
						clkUrl = strings.Replace(clkUrl, bidItem.Click.ClickParams.DownY, tmpDownY, -1)
					}
					if len(bidItem.Click.ClickParams.UpX) > 0 {
						clkUrl = strings.Replace(clkUrl, bidItem.Click.ClickParams.UpX, tmpUpX, -1)
					}
					if len(bidItem.Click.ClickParams.UpY) > 0 {
						clkUrl = strings.Replace(clkUrl, bidItem.Click.ClickParams.UpY, tmpUpY, -1)
					}
				}

				respListItemClickArray = append(respListItemClickArray, clkUrl)
			}
		}
		item.ClickLink = respListItemClickArray

		// win notice url
		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}
		item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		item.MaterialDirection = platformPos.PlatformPosDirection
		item.PEcpm = price
		list = append(list, item)
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	baiheQuna := models.MHUpResp{}
	baiheQuna.RespData = &mhResp
	baiheQuna.Extra = bigdataExtra

	return &baiheQuna
}

type BaiHeRequestObject struct {
	AccessSign         string                   `json:"access_sign"`
	Sid                string                   `json:"sid"`
	UserID             string                   `json:"userID"`
	Name               string                   `json:"name"`
	Position           string                   `json:"position"`
	Bundle             string                   `json:"bundle"`
	Brand              string                   `json:"brand"`
	Make               string                   `json:"make"`
	Model              string                   `json:"model"`
	Imei               string                   `json:"imei"`
	ImeiMD5            string                   `json:"imeiMD5"`
	Oaid               string                   `json:"oaid"`
	OaidMD5            string                   `json:"oaidMD5"`
	AndroidId          string                   `json:"android_id"`
	AndroididMd5       string                   `json:"androididMd5"`
	Idfa               string                   `json:"idfa"`
	IdfaMD5            string                   `json:"idfaMD5"`
	Mac                string                   `json:"mac"`
	Ip                 string                   `json:"ip"`
	OsType             string                   `json:"os_type"`
	OsVersion          string                   `json:"os_version"`
	Apver              string                   `json:"apver"`
	Ua                 string                   `json:"ua"`
	DeviceStartSec     string                   `json:"deviceStartSec"`
	Country            string                   `json:"country"`
	Language           string                   `json:"language"`
	DeviceNameMd5      string                   `json:"deviceNameMd5"`
	HardwareMachine    string                   `json:"hardwareMachine"`
	HardwareModel      string                   `json:"hardwareModel"`
	PhysicalMemoryByte string                   `json:"physicalMemoryByte"`
	HarddiskSizeByte   string                   `json:"harddiskSizeByte"`
	SystemUpdateSec    string                   `json:"systemUpdateSec"`
	TimeZone           string                   `json:"timeZone"`
	BirthTime          string                   `json:"birthTime"`
	Caid               []BaiHeRequestCaidObject `json:"caid"`
	ConnectionType     int                      `json:"connection_type"`
	Orientation        int                      `json:"orientation"`
	Carrier            int                      `json:"carrier"`
	Mtprice            int                      `json:"mtprice"`
	Screen             BaiHeRequestScreenObject `json:"screen"`
}

type BaiHeRequestCaidObject struct {
	Id      string `json:"id"`
	Version string `json:"version"`
}

type BaiHeRequestScreenObject struct {
	W   int `json:"w"`
	H   int `json:"h"`
	Dpi int `json:"dpi"`
}

type BaiHeResponseObject struct {
	Code int                      `json:"code"`
	Msg  string                   `json:"msg"`
	Data *BaiHeResponseDataObject `json:"data"`
}

type BaiHeResponseDataObject struct {
	Resid  string                           `json:"resid"`
	Result []*BaiHeResponseDataResultObject `json:"result"`
}

type BaiHeResponseDataResultObject struct {
	PagePositionAd  string                              `json:"page_position_ad"`
	AdLogoUrl       string                              `json:"ad_logo_url"`
	AdServer        string                              `json:"ad_server"`
	BaiheClickCount string                              `json:"baihe_click_count"`
	Title           string                              `json:"title"`
	Introduction    string                              `json:"introduction"`
	Price           string                              `json:"price"`
	AdProxyCompany  string                              `json:"ad_proxy_company"`
	Deeplink        string                              `json:"deeplink"`
	LogoUrl         string                              `json:"logoUrl"`
	Adw             int                                 `json:"adw"`
	Adh             int                                 `json:"adh"`
	Link            *BaiHeResponseDataResultLinkObject  `json:"link"`
	Pic             *BaiHeResponseDataResultPicObject   `json:"pic"`
	View            *BaiHeResponseDataResultViewObject  `json:"view"`
	Click           *BaiHeResponseDataResultClickObject `json:"click"`
}

type BaiHeResponseDataResultLinkObject struct {
	Url  string `json:"url"`
	Type int    `json:"type"`
}

type BaiHeResponseDataResultViewObject struct {
	TraceUrl []string `json:"trace_url"`
}

type BaiHeResponseDataResultClickObject struct {
	TraceUrl    []string                                  `json:"trace_url"`
	ClickParams *BaiHeResponseDataResultClickParamsObject `json:"clickParams"`
}

type BaiHeResponseDataResultClickParamsObject struct {
	DownX string `json:"downX"`
	DownY string `json:"downY"`
	UpX   string `json:"upX"`
	UpY   string `json:"upY"`
}

type BaiHeResponseDataResultPicObject struct {
	Url string `json:"url"`
	W   int    `json:"w"`
	H   int    `json:"h"`
}
