package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromQingShan ...
func GetFromQingShan(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	fmt.Println("get from qingshan")

	// platformPos.PlatformAppID = "62b949c105844627b5c98494"
	// platformPos.PlatformPosID = "100001781"
	// platformPos.PlatformAppBundle = "com.test"
	// platformPos.PlatformAppUpURL = "http://dsp.ads.umeng.com/uunion/api"
	// platformPos.PlatformAppUpURL = "http://test-ssp.ads.umeng.com/uunion/api"
	// platformPos.PlatformAppVersion = "1.0"
	// categoryInfo.FloorPrice = 15
	// fmt.Println("get from qingshan, local app id: ", localPos.LocalAppID)
	// fmt.Println("get from qingshan, local pos id: ", localPos.LocalPosID)
	// fmt.Println("get from qingshan, local app type: ", localPos.LocalAppType)
	// fmt.Println("get from qingshan, platform app id: ", platformPos.PlatformAppID)
	// fmt.Println("get from qingshan, platform pos id: ", platformPos.PlatformPosID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false
	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	////////////////////////////////////////////////////////////////////////////////////////
	// device
	reqDeviceInfoMap := map[string]interface{}{}

	reqDeviceInfoMap["deviceType"] = 1
	if mhReq.Device.Os == "android" {
		reqDeviceInfoMap["osType"] = 1
	} else if mhReq.Device.Os == "ios" {
		reqDeviceInfoMap["osType"] = 2
	}

	reqDeviceInfoMap["osVersion"] = mhReq.Device.OsVersion
	reqDeviceInfoMap["model"] = mhReq.Device.Model
	reqDeviceInfoMap["brand"] = mhReq.Device.Manufacturer
	reqDeviceInfoMap["make"] = mhReq.Device.Manufacturer

	reqDeviceInfoMap["lng"] = 0
	reqDeviceInfoMap["lat"] = 0

	reqDeviceInfoMap["screenWidth"] = mhReq.Device.ScreenWidth
	reqDeviceInfoMap["screenHeight"] = mhReq.Device.ScreenHeight

	if strings.Contains(mhReq.Device.IP, ":") {
		reqDeviceInfoMap["ipv6"] = mhReq.Device.IP
	} else {
		reqDeviceInfoMap["ip"] = mhReq.Device.IP
	}

	// device ua
	if platformPos.PlatformAppIsReportUa == 1 {
		reqDeviceInfoMap["ua"] = destConfigUA
	}

	// device carrier
	if mhReq.Network.Carrier == 1 {
		reqDeviceInfoMap["operatorType"] = 1
	} else if mhReq.Network.Carrier == 2 {
		reqDeviceInfoMap["operatorType"] = 3
	} else if mhReq.Network.Carrier == 3 {
		reqDeviceInfoMap["operatorType"] = 2
	} else {
		reqDeviceInfoMap["operatorType"] = 0
	}

	// device network
	if mhReq.Network.ConnectType == 0 {
		reqDeviceInfoMap["connectionType"] = 0
	} else if mhReq.Network.ConnectType == 1 {
		reqDeviceInfoMap["connectionType"] = 100
	} else if mhReq.Network.ConnectType == 2 {
		reqDeviceInfoMap["connectionType"] = 2
	} else if mhReq.Network.ConnectType == 3 {
		reqDeviceInfoMap["connectionType"] = 3
	} else if mhReq.Network.ConnectType == 4 {
		reqDeviceInfoMap["connectionType"] = 4
	} else if mhReq.Network.ConnectType == 7 {
		reqDeviceInfoMap["connectionType"] = 5
	}

	// device boot_mark, update_mark
	reqDeviceInfoMap["bootMark"] = mhReq.Device.BootMark
	reqDeviceInfoMap["updateMark"] = mhReq.Device.UpdateMark

	// uunionReqDevice := &uunion_up.BidRequest_Device{}

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true

				reqDeviceInfoMap["imeiMd5"] = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true

				reqDeviceInfoMap["imeiMd5"] = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true

				reqDeviceInfoMap["oaid"] = mhReq.Device.Oaid
			}
		}

		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from qingshan error req > 10")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		// osvMajor := 0
		// if len(mhReq.Device.OsVersion) > 0 {
		// 	osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
		// 	osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// 	// fmt.Println(osvMajor)
		// }
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				reqDeviceInfoMap["idfaMd5"] = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							reqDeviceInfoMap["caid"] = item.CAID
							reqDeviceInfoMap["caidVer"] = item.CAIDVersion

							break
						}
					}
				} else {
					var reqDeviceInfoCaidMapArray []map[string]interface{}

					for _, item := range mhReq.Device.CAIDMulti {
						reqDeviceInfoCaidMap := map[string]interface{}{}
						reqDeviceInfoCaidMap["caid"] = item.CAID
						reqDeviceInfoCaidMap["version"] = item.CAIDVersion
						reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
					}

					if len(reqDeviceInfoCaidMapArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
				reqDeviceInfoMap["country"] = tmpCountry
				reqDeviceInfoMap["language"] = tmpLanguage
				reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
				reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
				reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
				reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
				reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
				reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
				reqDeviceInfoMap["timeZone"] = tmpTimeZone
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfaMd5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								reqDeviceInfoMap["caid"] = item.CAID
								reqDeviceInfoMap["caidVer"] = item.CAIDVersion

								break
							}
						}
					} else {
						var reqDeviceInfoCaidMapArray []map[string]interface{}

						for _, item := range mhReq.Device.CAIDMulti {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["caid"] = item.CAID
							reqDeviceInfoCaidMap["version"] = item.CAIDVersion
							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
						}

						if len(reqDeviceInfoCaidMapArray) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
					reqDeviceInfoMap["country"] = tmpCountry
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
					reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
					reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
					reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
					reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
					reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
					reqDeviceInfoMap["timeZone"] = tmpTimeZone
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					reqDeviceInfoMap["idfaMd5"] = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								isIosDeviceOK = true
								isIOSToUpReportCAID = true

								reqDeviceInfoMap["caid"] = item.CAID
								reqDeviceInfoMap["caidVer"] = item.CAIDVersion

								break
							}
						}
					} else {
						var reqDeviceInfoCaidMapArray []map[string]interface{}

						for _, item := range mhReq.Device.CAIDMulti {
							reqDeviceInfoCaidMap := map[string]interface{}{}
							reqDeviceInfoCaidMap["caid"] = item.CAID
							reqDeviceInfoCaidMap["version"] = item.CAIDVersion
							reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
						}

						if len(reqDeviceInfoCaidMapArray) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
					reqDeviceInfoMap["country"] = tmpCountry
					reqDeviceInfoMap["language"] = tmpLanguage
					reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
					reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
					reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
					reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
					reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
					reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
					reqDeviceInfoMap["timeZone"] = tmpTimeZone
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			// fmt.Println("get from qingshan error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug qingshan android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// device osv
					reqDeviceInfoMap["osVersion"] = didRedisData.OsVersion

					// device model
					reqDeviceInfoMap["model"] = didRedisData.Model

					// device make
					reqDeviceInfoMap["brand"] = didRedisData.Manufacturer
					reqDeviceInfoMap["make"] = didRedisData.Manufacturer

					// device 清除
					delete(reqDeviceInfoMap, "imeiMd5")
					delete(reqDeviceInfoMap, "oaid")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							reqDeviceInfoMap["imeiMd5"] = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							reqDeviceInfoMap["imeiMd5"] = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							reqDeviceInfoMap["oaid"] = didRedisData.Oaid
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						reqDeviceInfoMap["ua"] = didRedisData.Ua

						replaceUA = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug qingshan ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						// device osv
						reqDeviceInfoMap["osVersion"] = didRedisData.OsVersion

						// device model
						reqDeviceInfoMap["model"] = didRedisData.Model

						// device make
						reqDeviceInfoMap["brand"] = didRedisData.Manufacturer
						reqDeviceInfoMap["make"] = didRedisData.Manufacturer

						// device 清除
						delete(reqDeviceInfoMap, "idfaMd5")
						delete(reqDeviceInfoMap, "caid")
						delete(reqDeviceInfoMap, "caidVer")
						delete(reqDeviceInfoMap, "caids")
						delete(reqDeviceInfoMap, "startupTime")
						delete(reqDeviceInfoMap, "country")
						delete(reqDeviceInfoMap, "language")
						delete(reqDeviceInfoMap, "deviceNameMd5")
						delete(reqDeviceInfoMap, "hardwareMachine")
						delete(reqDeviceInfoMap, "hardwareModel")
						delete(reqDeviceInfoMap, "memorySize")
						delete(reqDeviceInfoMap, "hardDiskSize")
						delete(reqDeviceInfoMap, "syscmpTime")
						delete(reqDeviceInfoMap, "timeZone")

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								reqDeviceInfoMap["idfaMd5"] = strings.ToLower(didRedisData.IdfaMd5)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caid"] = item.CAID
										reqDeviceInfoMap["caidVer"] = item.CAIDVersion

										break
									}
								}
							} else {
								var reqDeviceInfoCaidMapArray []map[string]interface{}

								for _, item := range tmpCAIDMulti {
									reqDeviceInfoCaidMap := map[string]interface{}{}
									reqDeviceInfoCaidMap["caid"] = item.CAID
									reqDeviceInfoCaidMap["version"] = item.CAIDVersion
									reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
								}

								if len(reqDeviceInfoCaidMapArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
								reqDeviceInfoMap["country"] = tmpCountry
								reqDeviceInfoMap["language"] = tmpLanguage
								reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
								reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
								reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
								reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
								reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
								reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
								reqDeviceInfoMap["timeZone"] = tmpTimeZone
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									reqDeviceInfoMap["idfaMd5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											reqDeviceInfoMap["caid"] = item.CAID
											reqDeviceInfoMap["caidVer"] = item.CAIDVersion

											break
										}
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["caid"] = item.CAID
										reqDeviceInfoCaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
									}

									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
									reqDeviceInfoMap["country"] = tmpCountry
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
									reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
									reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
									reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
									reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
									reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
									reqDeviceInfoMap["timeZone"] = tmpTimeZone
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									reqDeviceInfoMap["idfaMd5"] = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									reqDeviceInfoMap["idfaMd5"] = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true

											reqDeviceInfoMap["caid"] = item.CAID
											reqDeviceInfoMap["caidVer"] = item.CAIDVersion

											break
										}
									}
								} else {
									var reqDeviceInfoCaidMapArray []map[string]interface{}

									for _, item := range tmpCAIDMulti {
										reqDeviceInfoCaidMap := map[string]interface{}{}
										reqDeviceInfoCaidMap["caid"] = item.CAID
										reqDeviceInfoCaidMap["version"] = item.CAIDVersion
										reqDeviceInfoCaidMapArray = append(reqDeviceInfoCaidMapArray, reqDeviceInfoCaidMap)
									}

									if len(reqDeviceInfoCaidMapArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										reqDeviceInfoMap["caids"] = reqDeviceInfoCaidMapArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									reqDeviceInfoMap["startupTime"] = tmpDeviceStartSec
									reqDeviceInfoMap["country"] = tmpCountry
									reqDeviceInfoMap["language"] = tmpLanguage
									reqDeviceInfoMap["deviceNameMd5"] = tmpDeviceNameMd5
									reqDeviceInfoMap["hardwareMachine"] = tmpHardwareMachine
									reqDeviceInfoMap["hardwareModel"] = tmpHardwareModel
									reqDeviceInfoMap["memorySize"] = utils.ConvertStringToInt(tmpPhysicalMemoryByte)
									reqDeviceInfoMap["hardDiskSize"] = utils.ConvertStringToInt(tmpHarddiskSizeByte)
									reqDeviceInfoMap["syscmpTime"] = tmpSystemUpdateSec
									reqDeviceInfoMap["timeZone"] = tmpTimeZone
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							reqDeviceInfoMap["ua"] = didRedisData.Ua

							replaceUA = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				// fmt.Println("get from qingshan error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from qingshan error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	// req app
	reqAppInfoMap := map[string]interface{}{}
	reqAppInfoMap["channelId"] = 2066
	reqAppInfoMap["name"] = platformPos.PlatformAppName
	reqAppInfoMap["bundle"] = GetAppBundleByConfig(c, mhReq, localPos, platformPos)

	reqAppInfoMap["appVersion"] = platformPos.PlatformAppVersion
	reqAppInfoMap["deepLink"] = true
	reqAppInfoMap["secure"] = 1

	// req imp
	reqImpInfoArray := []map[string]interface{}{}
	reqImpInfoItemMap := map[string]interface{}{}
	reqImpInfoItemMap["impId"] = "1"
	reqImpInfoItemMap["pid"] = platformPos.PlatformAppID
	reqImpInfoItemMap["slotId"] = platformPos.PlatformPosID
	reqImpInfoItemMap["width"] = platformPos.PlatformPosWidth
	reqImpInfoItemMap["height"] = platformPos.PlatformPosHeight
	// 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	if platformPos.PlatformPosType == 1 {
		reqImpInfoItemMap["type"] = 1
	} else if platformPos.PlatformPosType == 2 {
		reqImpInfoItemMap["type"] = 3
	} else if platformPos.PlatformPosType == 3 {
		reqImpInfoItemMap["type"] = 4
	} else if platformPos.PlatformPosType == 4 {
		reqImpInfoItemMap["type"] = 0
	} else if platformPos.PlatformPosType == 11 {
		reqImpInfoItemMap["type"] = 6
	}
	reqImpInfoItemMap["bidFloor"] = localPosFloorPrice
	reqImpInfoItemMap["bidFloorCur"] = "CNY"
	reqImpInfoItemMap["bidType"] = 0

	reqImpInfoArray = append(reqImpInfoArray, reqImpInfoItemMap)

	// req post data
	postData := map[string]interface{}{
		"id":     bigdataUID,
		"app":    reqAppInfoMap,
		"device": reqDeviceInfoMap,
		"imps":   reqImpInfoArray,
	}

	jsonData, _ := json.Marshal(postData)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("qingshan url:", platformPos.PlatformAppUpURL)
	// fmt.Println("app id:", platformPos.PlatformAppID)
	// fmt.Println("pos id:", platformPos.PlatformPosID)
	// fmt.Println("========================================================================================================================")
	// fmt.Println("qingshan req:", string(jsonData))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(jsonData))

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println("qingshan resp: ", bigdataUID, localPos.LocalPosID, mhReq.Device.Os, string(bodyContent))

	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// fmt.Println("========================================================================================================================")
	qingshanRespStu := QingShanRespStu{}
	json.Unmarshal([]byte(bodyContent), &qingshanRespStu)

	// fmt.Println("qingshan resp code:", resp.StatusCode)
	// fmt.Println("qingshan resp:", string(bodyContent))
	// fmt.Println("========================================================================================================================")
	////////////////////////////////////////////////////////////////////////////////////////
	if qingshanRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = qingshanRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(qingshanRespStu.Ads) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = qingshanRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	respDataListItemStu := qingshanRespStu.Ads

	// if len(gdtRespDataListItemStu) > 1 {
	// 	sort.Sort(qingshanEcpmSort(gdtRespDataListItemStu))
	// }

	for _, item := range respDataListItemStu {

		respTmpRespAllNum = respTmpRespAllNum + 1

		// ecpm
		qingshanEcpm := int(item.Ecpm)

		respTmpPrice = respTmpPrice + qingshanEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if qingshanEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			qingshanEcpm = platformPos.PlatformPosEcpm
		}

		// fmt.Println("uunion_ecpm local_pos_id: " + localPos.LocalPosID + ", ecpm: " + utils.ConvertIntToString(qingshanEcpm))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > qingshanEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		respListItemMap := map[string]interface{}{}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(qingshanEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		isVideo := false

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// resp item
		respTitle := item.Title
		respDescription := item.Description
		respIconURL := ""
		if len(item.Icons) > 0 {
			respIconURL = item.Icons[0].URL
		}
		if len(item.PackageAppIcon) > 0 {
			respIconURL = item.PackageAppIcon
		}

		respImageURL := ""
		respImageWidth := 0
		respImageHeight := 0
		if len(item.Images) > 0 {
			respImageURL = item.Images[0].URL
			respImageWidth = item.Images[0].Width
			respImageHeight = item.Images[0].Height
		}

		// title
		if len(respTitle) > 0 {
			respListItemMap["title"] = respTitle
		}

		// description
		if len(respDescription) > 0 {
			respListItemMap["description"] = respDescription
		}

		tmpDownX := "__DOWN_X__"
		tmpDownY := "__DOWN_Y__"
		tmpUpX := "__UP_X__"
		tmpUpY := "__UP_Y__"

		if mhReq.Device.Os == "android" {
			if item.InteractionType == 2 {
				respListItemMap["interact_type"] = 1
				respListItemMap["ad_url"] = item.LandingURL
				respListItemMap["download_url"] = item.LandingURL
			} else if item.InteractionType == 1 {
				respListItemMap["interact_type"] = 0
				respListItemMap["ad_url"] = item.LandingURL
				respListItemMap["landpage_url"] = item.LandingURL
			} else {
				continue
			}

			if len(item.PackageAppName) > 0 {
				respListItemMap["app_name"] = item.PackageAppName
			} else {
			}
			if len(item.AppDeveloper) > 0 {
				respListItemMap["publisher"] = item.AppDeveloper
			}
			if len(item.PackageAppVer) > 0 {
				respListItemMap["app_version"] = item.PackageAppVer
			}
			if len(item.AppPrivacyUrl) > 0 {
				respListItemMap["privacy_url"] = item.AppPrivacyUrl
			}
			if len(item.AppPermissionsUrl) > 0 {
				respListItemMap["permission_url"] = item.AppPermissionsUrl
			}
		} else if mhReq.Device.Os == "ios" {
			if item.InteractionType == 1 {
				respListItemMap["ad_url"] = item.LandingURL
				respListItemMap["interact_type"] = 0
				respListItemMap["landpage_url"] = item.LandingURL
			} else if item.InteractionType == 2 {
				respListItemMap["ad_url"] = item.LandingURL
				respListItemMap["interact_type"] = 1
				respListItemMap["download_url"] = item.LandingURL
			} else {
				continue
			}
		}

		if len(item.Video.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if item.Video.Duration > 0 {
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, item.Video.Duration)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					continue
				}

				respListVideoItemMap["duration"] = item.Video.Duration * 1000
			}
			respListVideoItemMap["width"] = item.Video.Width
			respListVideoItemMap["height"] = item.Video.Height
			respListVideoItemMap["video_url"] = item.Video.VideoURL

			// 过滤素材方向, 大小
			isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, item.Video.Width, item.Video.Height)
			if isMaterialFilter {
				respTmpInternalCode = filterErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// cover_url
			if len(respImageURL) > 0 {
				respListVideoItemMap["cover_url"] = respImageURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			isVideoTrack := 0
			// event track
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, monitorItem := range item.Tracks {
				if monitorItem.Type == 9 {
					for _, monitorURLItem := range monitorItem.TrackURLs {
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorURLItem)
					}
				}
			}
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				isVideoTrack = 1
				respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, monitorItem := range item.Tracks {
				if monitorItem.Type == 11 {
					for _, monitorURLItem := range monitorItem.TrackURLs {
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, monitorURLItem)
					}
				}
			}
			if len(respListVideoEndEventTrackURLMap) > 0 {
				isVideoTrack = 1

				respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if isVideoTrack == 1 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}
			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			// image url
			if len(respImageURL) > 0 {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = respImageURL
				respListImageItemMap["width"] = respImageWidth
				respListImageItemMap["height"] = respImageHeight

				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, respImageWidth, respImageHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					continue
				}

				var respListItemImageArray []map[string]interface{}
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

				respListItemMap["imgs"] = respListItemImageArray
			}
			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// crid
		// respListItemMap["crid"] = GetCRIDByMd5(&respListItemMap)

		// deeplink
		destDeepLink := ""
		if len(item.DeeplinkURL) > 0 {
			destDeepLink = item.DeeplinkURL

			respListItemMap["deep_link"] = destDeepLink

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, monitorItem := range item.Tracks {
				if monitorItem.Type == 19 {
					for _, monitorURLItem := range monitorItem.TrackURLs {
						respListItemDeepLinkArray = append(respListItemDeepLinkArray, monitorURLItem)
					}
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, monitorItem := range item.Tracks {
					if monitorItem.Type == 33 {
						for _, monitorURLItem := range monitorItem.TrackURLs {
							respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, monitorURLItem)
						}
					}
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// package name
		destPackageName := ""
		if len(item.PackageName) > 0 {
			respListItemMap["package_name"] = item.PackageName

			destPackageName = item.PackageName
		} else {
			hackPackageName := GetPackageNameByDeeplink(destDeepLink)
			if len(hackPackageName) > 0 {
				respListItemMap["package_name"] = hackPackageName

				destPackageName = hackPackageName
			}
		}

		// android
		if mhReq.Device.Os == "android" {
			appInfoFromRedis, appInfoFromRedisErr := models.GetAppInfoFromRedis(c, item.PackageAppName, destPackageName)
			if appInfoFromRedisErr == nil {
				respListItemMap["appinfo"] = appInfoFromRedis.AppInfo
				respListItemMap["appinfo_url"] = appInfoFromRedis.AppInfoURL
			}
		}

		// icon_url
		if len(respIconURL) > 0 {
			respListItemMap["icon_url"] = respIconURL
		}
		//} else {
		//	respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		//}

		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// impression_link map
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, qingshanEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
		// impression_link

		// 随机95-98%替换价格宏
		encodePriceValue := "__WIN_NOTICE_PRICE__"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("qingshan price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			macroPrice := utils.ConvertIntToString(int(qingshanEcpm * randPRValue / 100))
			encodePrice := utils.AesCBCPKCS5Encrypt(macroPrice, platformPos.PlatformAppPriceEncrypt)
			encodePriceValue = base64.RawURLEncoding.EncodeToString(encodePrice)
		}

		for _, monitorItem := range item.Tracks {
			if monitorItem.Type == 1 {
				for _, monitorURLItem := range monitorItem.TrackURLs {
					tmpItem := monitorURLItem
					tmpItem = strings.Replace(tmpItem, "__WIN_NOTICE_PRICE__", encodePriceValue, -1)
					respListItemImpArray = append(respListItemImpArray, tmpItem)
				}
			}
		}

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link
		var respListItemClkArray []string
		for _, monitorItem := range item.Tracks {
			if monitorItem.Type == 2 {
				for _, monitorURLItem := range monitorItem.TrackURLs {
					tmpItem := monitorURLItem
					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							tmpItem = tmpItem + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							tmpItem = tmpItem + "&replace_adid_sim_xy_default=1"
							respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}
					respListItemClkArray = append(respListItemClkArray, tmpItem)
				}
			}
		}

		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))

		if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
			if mhReq.Device.XDPI > 0 {
				tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
				tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

				mhClkParams.Add("width", utils.ConvertIntToString(tmpWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(tmpHeight))
			} else {
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			}
		} else {
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		}

		mhClkParams.Add("down_x", tmpDownX)
		mhClkParams.Add("down_y", tmpDownY)
		mhClkParams.Add("up_x", tmpUpX)
		mhClkParams.Add("up_y", tmpUpY)
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = qingshanEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	// sdk是否需要逻辑像素替换坐标
	if isLogicPixel == 1 {
		respMap["is_logic_pixel"] = 1
	}

	// onlyTest, _ := json.Marshal(respMap)
	// fmt.Println("qingshan resp: ", string(onlyTest))

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// qingshan resp
	respUUnion := models.MHUpResp{}
	respUUnion.RespData = &mhResp
	respUUnion.Extra = bigdataExtra
	// respGdt.Extra.RespCount = len(respListArray)
	// respGdt.Extra.ExternalCode = 0
	// respGdt.Extra.InternalCode = 900000

	return &respUUnion
}

func curlQingShanNurl(nurl string) string {

	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// fmt.Println(requestGet.URL.String())
	// fmt.Println("gdt nurl req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return ""
	}

	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Println("gdt nurl  resp: " + string(bodyContent))
	return string(bodyContent)
}

// QingShanRespStu ...
type QingShanRespStu struct {
	ID   string              `json:"id"`
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Ads  []QingShanRespAdStu `json:"ads"`
}

type QingShanRespAdStu struct {
	ImpID              string                   `json:"impId"`
	Title              string                   `json:"title"`
	Description        string                   `json:"description"`
	Images             []QingShanRespAdImageStu `json:"images"`
	Video              QingShanRespAdVideoStu   `json:"video"`
	Width              int                      `json:"width"`
	Height             int                      `json:"height"`
	Icons              []QingShanRespAdImageStu `json:"icons"`
	LandingURLType     int                      `json:"landingURLType"`
	LandingURL         string                   `json:"landingURL"`
	DeeplinkURL        string                   `json:"deeplinkURL"`
	FallbackUrl        string                   `json:"fallbackUrl"`
	PositionMacro      int                      `json:"positionMacro"`
	VideoMacro         int                      `json:"videoMacro"`
	CreativeType       int                      `json:"creativeType"`
	InteractionType    int                      `json:"interactionType"` // 1:BROWSE 浏览; 2:DOWNLOAD 下载。
	PackageName        string                   `json:"packageName"`
	PackageAppName     string                   `json:"packageAppName"`
	PackageAppSize     string                   `json:"packageAppSize"`
	PackageAppVer      string                   `json:"packageAppVer"`
	Tracks             []QingShanRespAdTrackStu `json:"tracks"`
	Source             string                   `json:"source"`
	Brand              string                   `json:"brand"`
	AdLogoTxt          string                   `json:"adLogoTxt"`
	AdLogoImg          string                   `json:"adLogoImg"`
	IsFullScreenClick  int                      `json:"isFullScreenClick"`
	AdActionText       string                   `json:"adActionText"`
	AdActionImg        string                   `json:"adActionImg"`
	Ecpm               float32                  `json:"price"`
	PackageAppIcon     string                   `json:"packageAppIcon"`
	PackageAppDesc     string                   `json:"packageAppDesc"`
	PackageAppDescUrl  string                   `json:"packageAppDescUrl"`
	AppDeveloper       string                   `json:"appDeveloper"`
	AppPermissionsUrl  string                   `json:"appPermissionsUrl"`
	AppPermissionsDesc string                   `json:"appPermissionsDesc"`
	AppPrivacyUrl      string                   `json:"appPrivacyUrl"`
}

type QingShanRespAdImageStu struct {
	Width  int    `json:"width"`
	Height int    `json:"height"`
	URL    string `json:"url"`
}

type QingShanRespAdVideoStu struct {
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	VideoURL string `json:"videoURL"`
	Duration int    `json:"duration"`
}

type QingShanRespAdTrackStu struct {
	Type      int      `json:"type"`
	TrackURLs []string `json:"urls"`
}
