package core

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromupXingFan 星梵 adx
func GetFromupXingFan(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤UA请求
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// sdk是否需要逻辑像素替换坐标
	isLogicPixel := 0
	if localPos.LocalAppType == "1" {
		isLogicPixel = 1
		if len(localPos.LocalAppPhysicalPixelPlatformIDs) > 0 {
			isOK := false
			logicPixelArray := strings.Split(localPos.LocalAppPhysicalPixelPlatformIDs, ",")
			for _, tmpItem := range logicPixelArray {
				if tmpItem == platformPos.PlatformMediaID {
					isOK = true
					break
				}
			}
			if isOK {
				isLogicPixel = 0
			}
		}
	} else if localPos.LocalAppClickPointType == 2 {
		isLogicPixel = 1
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpLanguage := mhReq.Device.Language
	tmpCountry := mhReq.Device.Country

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	// 原始请求参数是否ok
	isIosDeviceOK := false
	isAndroidDeviceOK := false

	var connectionType int
	switch mhReq.Network.ConnectType {
	case 1:
		connectionType = 1
	case 2:
		connectionType = 3
	case 3:
		connectionType = 5
	case 4, 5, 6:
		connectionType = 6
	case 7:
		connectionType = 7
	}

	var impType string
	switch platformPos.PlatformPosType {
	case 1:
		impType = "1"
	case 2:
		impType = "5"
	case 3:
		impType = "4"
	case 4:
		impType = "2"
	case 11:
		impType = "3"
	default:
		impType = "0"
	}

	var carrier string
	switch mhReq.Network.Carrier {
	case 1:
		carrier = "mobile"
	case 2:
		carrier = "unicom"
	case 3:
		carrier = "telecom"
	default:
		carrier = "unknown"
	}

	var impList []*XingFanRequestImpObject
	imp := &XingFanRequestImpObject{
		Id:       bigdataUID,
		Tagid:    platformPos.PlatformPosID,
		ImpType:  impType,
		Secure:   1,
		DeepLink: 1,
		Bidfloor: categoryInfo.FloorPrice,
		Width:    platformPos.PlatformPosWidth,
		Height:   platformPos.PlatformPosHeight,
	}
	impList = append(impList, imp)

	request := XingFanRequestObject{
		Id: bigdataUID,
		App: &XingFanRequestAppObject{
			AppID:      platformPos.PlatformAppID,
			Name:       platformPos.PlatformAppName,
			Bundle:     GetAppBundleByConfig(c, mhReq, localPos, platformPos),
			AppVersion: platformPos.PlatformAppVersion,
		},
		Imp: impList,
		Device: &XingFanRequestDeviceObject{
			Ip:             mhReq.Device.IP,
			Make:           mhReq.Device.Manufacturer,
			Model:          mhReq.Device.Model,
			Os:             mhReq.Device.Os,
			Osv:            mhReq.Device.OsVersion,
			Carrier:        carrier,
			AppVersion:     mhReq.Device.AppStoreVersion,
			BootMark:       mhReq.Device.BootMark,
			UpdateMark:     mhReq.Device.UpdateMark,
			Devicetype:     4,
			H:              mhReq.Device.ScreenWidth,
			W:              mhReq.Device.ScreenHeight,
			Connectiontype: connectionType,
		},
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		request.Device.Ua = destConfigUA
	}

	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isAndroidDeviceOK = true
				request.Device.Did = mhReq.Device.Imei
				request.Device.Didmd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isAndroidDeviceOK = true
				request.Device.Didmd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isAndroidDeviceOK = true
				request.Device.OaId = mhReq.Device.Oaid
			} else if len(mhReq.Device.OaidMd5) > 0 {
				isAndroidDeviceOK = true
				request.Device.OaIdMd5 = strings.ToLower(mhReq.Device.OaidMd5)
			} else if len(mhReq.Device.AndroidID) > 0 {
				if platformPos.PlatformAppIsReportAndroidID == 1 {
					request.Device.AndroidId = mhReq.Device.AndroidID
					if len(mhReq.Device.AndroidIDMd5) > 0 {
						request.Device.AndroidIdMd5 = mhReq.Device.AndroidIDMd5
					}
				}
			}
		}
		// 如果替换包开也走替换包判定
		if isAndroidDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		if len(iosReportMainParameter) == 0 {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
				request.Device.Idfa = mhReq.Device.Idfa
				request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						request.Device.Caid = item.CAID
						request.Device.Caidv = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						break
					}
				}
			} else {

				for _, item := range mhReq.Device.CAIDMulti {
					request.Device.Caid = item.CAID
					request.Device.Caidv = item.CAIDVersion

					isIosDeviceOK = true
					isIOSToUpReportCAID = true
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				request.Device.Lang = tmpLanguage
				request.Device.Region = tmpCountry
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.Caidv = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.Caidv = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportMainParameter, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
					request.Device.Lang = tmpLanguage
					request.Device.Region = tmpCountry

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.Idfa = mhReq.Device.Idfa
					request.Device.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					request.Device.IdfaMd5 = strings.ToLower(mhReq.Device.IdfaMd5)
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							request.Device.Caid = item.CAID
							request.Device.Caidv = item.CAIDVersion

							isIosDeviceOK = true
							isIOSToUpReportCAID = true
							break
						}
					}
				} else {
					for _, item := range mhReq.Device.CAIDMulti {
						request.Device.Caid = item.CAID
						request.Device.Caidv = item.CAIDVersion

						isIosDeviceOK = true
						isIOSToUpReportCAID = true
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
					request.Device.Lang = tmpLanguage
					request.Device.Region = tmpCountry

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")
				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					switch strings.ToLower(mhReq.Device.Os) {
					case "ios":
						request.Device.Os = "ios"
					case "android":
						request.Device.Os = "android"
					}
					request.Device.Osv = didRedisData.OsVersion
					request.Device.Model = didRedisData.Model
					request.Device.Make = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							request.Device.Did = didRedisData.Imei
							request.Device.Didmd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							request.Device.Didmd5 = strings.ToLower(didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							request.Device.OaId = didRedisData.Oaid
						} else if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								request.Device.AndroidId = didRedisData.AndroidID
								if len(didRedisData.AndroidIDMd5) > 0 {
									request.Device.AndroidIdMd5 = didRedisData.AndroidIDMd5
								}
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016

								return MhUpErrorRespMap("", bigdataExtra)
							}
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						request.Device.Ua = didRedisData.Ua

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						switch strings.ToLower(mhReq.Device.Os) {
						case "ios":
							request.Device.Os = "ios"
						case "android":
							request.Device.Os = "android"
						}
						request.Device.Osv = didRedisData.OsVersion
						request.Device.Model = didRedisData.Model
						request.Device.Make = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								request.Device.Idfa = didRedisData.Idfa
								request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}

						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										request.Device.Caid = item.CAID
										request.Device.Caidv = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										break
									}
								}
							} else {
								for _, item := range tmpCAIDMulti {
									request.Device.Caid = item.CAID
									request.Device.Caidv = item.CAIDVersion

									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
								}
							}
						}

						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpLanguage = didRedisData.Language
							tmpCountry = didRedisData.Country

							if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
								request.Device.Lang = tmpLanguage
								request.Device.Region = tmpCountry
								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.Caidv = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.Caidv = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpLanguage = didRedisData.Language
								tmpCountry = didRedisData.Country

								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
									request.Device.Lang = tmpLanguage
									request.Device.Region = tmpCountry

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									request.Device.Idfa = didRedisData.Idfa
									request.Device.IdfaMd5 = utils.GetMd5(didRedisData.Idfa)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									request.Device.IdfaMd5 = strings.ToLower(didRedisData.IdfaMd5)

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											request.Device.Caid = item.CAID
											request.Device.Caidv = item.CAIDVersion

											isIosReplaceDeviceOK = true
											isIOSToUpReportCAID = true
											break
										}
									}
								} else {
									for _, item := range tmpCAIDMulti {
										request.Device.Caid = item.CAID
										request.Device.Caidv = item.CAIDVersion

										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpLanguage = didRedisData.Language
								tmpCountry = didRedisData.Country
								if len(tmpLanguage) > 0 && len(tmpCountry) > 0 {
									request.Device.Lang = tmpLanguage
									request.Device.Region = tmpCountry

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							request.Device.Ua = didRedisData.Ua

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
			if isAndroidDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	requestMarshal, _ := json.Marshal(request)
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(requestMarshal))
	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	if platformPos.PlatformAppIsReportUa == 1 {
		requestGet.Header.Add("User-Agent", mhReq.Device.Ua)
	}

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	resp, err := client.Do(requestGet)
	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)

	xfRespStu := XingFanResponseObject{}
	_ = json.Unmarshal(bodyContent, &xfRespStu)

	// 返回数据
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if xfRespStu.Code != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpRespCode = xfRespStu.Code

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if xfRespStu.Seatbid == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	var list []models.MHRespDataItem
	var item models.MHRespDataItem

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	for _, seatbidItem := range xfRespStu.Seatbid {
		for _, bidItem := range seatbidItem.Bid {
			respTmpRespAllNum = respTmpRespAllNum + 1

			// ecpm
			price := bidItem.Price

			respTmpPrice = respTmpPrice + price

			// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
			if price <= 0 && platformPos.PlatformPosEcpmType == 1 {
				price = platformPos.PlatformPosEcpm
			}

			// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
			if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
				// 价格拦截, 跟之前一样
				if localPosFinalPrice > price {
					respTmpInternalCode = 900104
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			// 填充后限制
			isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
			if isAllInOneLimitAfterUpRespOK {
			} else {
				respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}

			// 下发ecpm
			// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
			tmpEcpm := 0
			if localPos.LocalPosEcpmType == 0 {
				// 不下发
				tmpEcpm = localPos.LocalPosEcpm
			} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
				tmpEcpm = int(float32(price) * (float32(100) - localPosProfitRate) / 100)
				if tmpEcpm < 1 {
					tmpEcpm = 0
				}

				item.Ecpm = tmpEcpm
			} else if localPos.LocalPosEcpmType == 2 {
				tmpEcpm = localPos.LocalPosEcpm
				item.Ecpm = localPos.LocalPosEcpm
			}

			switch bidItem.IsDownload {
			case 1:
				item.InteractType = 1
				if len(bidItem.Ldp) > 0 {
					item.DownloadURL = bidItem.Ldp
				}
			case 2:
				item.InteractType = 0
				if len(bidItem.Ldp) > 0 {
					item.LandpageURL = bidItem.Ldp
				}
			default:
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}

			// adid
			maplehazeAdId := uuid.NewV4().String()
			item.AdID = maplehazeAdId
			item.ReqWidth = platformPos.PlatformPosWidth
			item.ReqHeight = platformPos.PlatformPosHeight
			item.Crid = bidItem.Crid
			item.IconURL = bidItem.BrandLogo

			// title
			if len(bidItem.Title) > 0 {
				item.Title = bidItem.Title
			}

			// description
			if len(bidItem.Desc) > 0 {
				item.Description = bidItem.Desc
			}

			if bidItem.Image == nil && bidItem.Video == nil {
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}

			if bidItem.Image != nil {
				var imgList []models.MHRespImage
				for _, imgItem := range bidItem.Image {
					var img models.MHRespImage
					img.URL = imgItem.Url
					img.Width = imgItem.Width
					img.Height = imgItem.Height
					imgList = append(imgList, img)
				}
				item.Image = imgList
				item.CrtType = 11
			} else {
				if bidItem.Video != nil {
					var video models.MHRespVideo
					video.VideoURL = bidItem.Video.Vurl
					video.Duration = bidItem.Video.Duration * 1000
					video.Width = bidItem.Video.Vw
					video.Height = bidItem.Video.Vh
					item.Video = &video
					item.CrtType = 20
				}
			}

			if item.Video == nil && item.Image == nil {
				bigdataExtra.InternalCode = 900105
				bigdataExtra.ExternalCode = 102006
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}

			if bidItem.AppInfo != nil {
				item.AppName = bidItem.AppInfo.AppName
				item.PackageName = bidItem.AppInfo.Pkgname
				item.AppVersion = bidItem.AppInfo.VersionCode
				item.Publisher = bidItem.AppInfo.DeveloperName
				item.PrivacyLink = bidItem.AppInfo.PrivacyPolicy
				item.PermissionURL = bidItem.AppInfo.AppPermissionsLink
				item.AppInfoURL = bidItem.AppInfo.AppDescUrl
				item.PackageSize = int64(bidItem.AppInfo.Size)
			}

			tmpDownX := ""
			tmpDownY := ""
			tmpUpX := ""
			tmpUpY := ""

			tmpIsServerRealReplaceXY := false
			tmpServerRealReplaceXYType := 0
			if platformPos.PlatformPosIsReplaceXY == 1 {
				if platformPos.PlatformPosReplaceXYType == 0 {
					redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900101
						bigdataExtra.ExternalCode = 102006
						respTmpRespFailedNum = respTmpRespFailedNum + 1
						continue
					} else {
						tmpDownX = utils.ConvertIntToString(redisDownX)
						tmpDownY = utils.ConvertIntToString(redisDownY)
						tmpUpX = utils.ConvertIntToString(redisUpX)
						tmpUpY = utils.ConvertIntToString(redisUpY)

						if redisClickXYType == "0" { // 物理像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 0
						} else if redisClickXYType == "1" { // 逻辑像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 1
						}
					}
				} else if platformPos.PlatformPosReplaceXYType == 1 {
					// -999
					tmpDownX = "-999"
					tmpDownY = "-999"
					tmpUpX = "-999"
					tmpUpY = "-999"
				} else if platformPos.PlatformPosReplaceXYType == 2 {
					// 替换空
					tmpDownX = ""
					tmpDownY = ""
					tmpUpX = ""
					tmpUpY = ""
				} else if platformPos.PlatformPosReplaceXYType == 3 {
					// 替换0
					tmpDownX = "0"
					tmpDownY = "0"
					tmpUpX = "0"
					tmpUpY = "0"
				}
			}

			bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, price, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)

			if len(bidItem.Deeplink) > 0 {
				var convTrackArray []models.MHRespConvTracks
				var convTracks models.MHRespConvTracks
				item.DeepLink = bidItem.Deeplink
				// deeplink track
				var respListItemSuccDeepLinkArray []string
				if len(bidItem.DpTracking) > 0 {
					respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, bidItem.DpTracking...)
				}
				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				mhDPParams.Add("log", bigdataParams)
				respListItemSuccDeepLinkArray = append(respListItemSuccDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())
				convTracks.ConvType = 10
				convTracks.ConvURLS = respListItemSuccDeepLinkArray
				convTrackArray = append(convTrackArray, convTracks)

				if platformPos.PlatformAppIsDeepLinkFailed == 1 {
					var respListItemDeepLinkFailedArray []string
					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

					var deeplinkFailedConvTrack models.MHRespConvTracks
					deeplinkFailedConvTrack.ConvType = 11
					deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray
					convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
				}

				item.ConvTracks = convTrackArray
			}

			randPRValue := 95 + rand.Intn(4)
			macroPrice := uint64(price * randPRValue / 100)
			macroPriceStr := encrypt([]byte(platformPos.PlatformAppPriceEncrypt), []byte(platformPos.PlatformAppPriceEncrypt2), macroPrice)

			// impression_link
			var respListItemImpArray []string
			mhImpParams := url.Values{}
			mhImpParams.Add("log", bigdataParams)
			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
			if len(bidItem.Imptrackers) > 0 {
				for _, impUrl := range bidItem.Imptrackers {
					impUrl = strings.Replace(impUrl, "${AUCTION_PRICE}", macroPriceStr, -1)
					impUrl = strings.Replace(impUrl, "__acw__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					impUrl = strings.Replace(impUrl, "__ach__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							impUrl = strings.Replace(impUrl, "__acw__", utils.ConvertIntToString(tmpWidth), -1)
							impUrl = strings.Replace(impUrl, "__ach__", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							impUrl = strings.Replace(impUrl, "__acw__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							impUrl = strings.Replace(impUrl, "__ach__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						impUrl = strings.Replace(impUrl, "__acw__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						impUrl = strings.Replace(impUrl, "__ach__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					impUrl = strings.Replace(impUrl, "__down_x__", tmpDownX, -1)
					impUrl = strings.Replace(impUrl, "__down_y__", tmpDownY, -1)
					impUrl = strings.Replace(impUrl, "__up_x__", tmpUpX, -1)
					impUrl = strings.Replace(impUrl, "__up_y__", tmpUpY, -1)

					respListItemImpArray = append(respListItemImpArray, impUrl)
				}
			}
			item.ImpressionLink = respListItemImpArray

			// click_link
			var respListItemClickArray []string
			mhClkParams := url.Values{}
			mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
			mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			mhClkParams.Add("down_x", "__DOWN_X__")
			mhClkParams.Add("down_y", "__DOWN_Y__")
			mhClkParams.Add("up_x", "__UP_X__")
			mhClkParams.Add("up_y", "__UP_Y__")
			mhClkParams.Add("mh_down_x", "__DOWN_X__")
			mhClkParams.Add("mh_down_y", "__DOWN_Y__")
			mhClkParams.Add("mh_up_x", "__UP_X__")
			mhClkParams.Add("mh_up_y", "__UP_Y__")
			mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
			mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
			mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
			mhClkParams.Add("turnx", "__TURN_X__")
			mhClkParams.Add("turny", "__TURN_Y__")
			mhClkParams.Add("turnz", "__TURN_Z__")
			mhClkParams.Add("turntime", "__TURN_TIME__")

			if platformPos.PlatformPosIsReportSLD == 1 {
				mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
			} else {
				mhClkParams.Add("sld", "__SLD__")
			}
			mhClkParams.Add("log", bigdataParams)
			respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
			if len(bidItem.Clicktrackers) > 0 {
				for _, clkUrl := range bidItem.Clicktrackers {
					clkUrl = strings.Replace(clkUrl, "${AUCTION_PRICE}", macroPriceStr, -1)
					clkUrl = strings.Replace(clkUrl, "__acw__", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
					clkUrl = strings.Replace(clkUrl, "__ach__", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
					if isLogicPixel == 1 || platformPos.PlatformPosIsReplaceXY == 2 {
						if mhReq.Device.XDPI > 0 {
							tmpWidth := int(float32(localPos.LocalPosWidth)/mhReq.Device.XDPI + 0.5)
							tmpHeight := int(float32(localPos.LocalPosHeight)/mhReq.Device.XDPI + 0.5)

							clkUrl = strings.Replace(clkUrl, "__acw__", utils.ConvertIntToString(tmpWidth), -1)
							clkUrl = strings.Replace(clkUrl, "__ach__", utils.ConvertIntToString(tmpHeight), -1)
						} else {
							clkUrl = strings.Replace(clkUrl, "__acw__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
							clkUrl = strings.Replace(clkUrl, "__ach__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
						}
					} else {
						clkUrl = strings.Replace(clkUrl, "__acw__", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
						clkUrl = strings.Replace(clkUrl, "__ach__", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
					}
					clkUrl = strings.Replace(clkUrl, "__down_x__", tmpDownX, -1)
					clkUrl = strings.Replace(clkUrl, "__down_y__", tmpDownY, -1)
					clkUrl = strings.Replace(clkUrl, "__up_x__", tmpUpX, -1)
					clkUrl = strings.Replace(clkUrl, "__up_y__", tmpUpY, -1)

					respListItemClickArray = append(respListItemClickArray, clkUrl)
				}
			}
			item.ClickLink = respListItemClickArray

			// win notice url
			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				item.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
				item.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}
			item.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

			item.MaterialDirection = platformPos.PlatformPosDirection
			item.PEcpm = price
			list = append(list, item)
		}
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = 900201
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// tiangong resp
	respHx := models.MHUpResp{}
	respHx.RespData = &mhResp
	respHx.Extra = bigdataExtra

	return &respHx

}

func encrypt(encryptionKey, integrityKey []byte, price uint64) string {
	iv := []byte(fmt.Sprintf("%d", time.Now().UnixNano()/1000))

	if len(integrityKey) == 0 || len(encryptionKey) == 0 {
		return ""
	}

	if len(iv) != 16 {
		return ""
	}

	h := hmac.New(sha1.New, encryptionKey)
	h.Write(iv)
	pad := h.Sum(nil)[:8]

	p := make([]byte, 8)
	binary.BigEndian.PutUint64(p, price)
	encPrice := safeXORBytes(pad, p)

	h = hmac.New(sha1.New, integrityKey)
	h.Write(p)
	h.Write(iv)
	sig := h.Sum(nil)[:4]

	b := make([]byte, 0, len(iv)+len(encPrice)+len(sig))
	buf := bytes.NewBuffer(b)
	buf.Write(iv)
	buf.Write(encPrice)
	buf.Write(sig)
	n := base64.URLEncoding.EncodedLen(len(buf.Bytes()))
	ret := make([]byte, n, n)
	base64.URLEncoding.Encode(ret, buf.Bytes())

	return string(ret)
}

func safeXORBytes(a, b []byte) []byte {
	n := len(a)
	if len(b) < n {
		n = len(b)
	}

	if n == 0 {
		return nil
	}

	ret := make([]byte, n)

	for i := 0; i < n; i++ {
		ret[i] = a[i] ^ b[i]
	}

	return ret
}

type XingFanRequestObject struct {
	Id     string                      `json:"id"`
	App    *XingFanRequestAppObject    `json:"app"`
	Imp    []*XingFanRequestImpObject  `json:"imp"`
	Device *XingFanRequestDeviceObject `json:"device"`
}

type XingFanRequestAppObject struct {
	AppID      string `json:"id"`
	Name       string `json:"name"`
	Bundle     string `json:"bundle"`
	AppVersion string `json:"ver"`
}

type XingFanRequestImpObject struct {
	Id       string `json:"id"`
	Tagid    string `json:"tagid"`
	ImpType  string `json:"impType"`
	Secure   int    `json:"secure"`
	Bidfloor int    `json:"bidfloor"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	DeepLink int    `json:"deepLink"`
}

type XingFanRequestDeviceObject struct {
	Ua             string `json:"ua"`
	Ip             string `json:"ip"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	Os             string `json:"os"`
	Osv            string `json:"osv"`
	Did            string `json:"did"`
	Didmd5         string `json:"didmd5"`
	AndroidId      string `json:"androidId"`
	AndroidIdMd5   string `json:"androidIdMd5"`
	Carrier        string `json:"carrier"`
	Region         string `json:"region"`
	Lang           string `json:"lang"`
	AppVersion     string `json:"appVersion"`
	OaId           string `json:"oaId"`
	OaIdMd5        string `json:"oaIdMd5"`
	BootMark       string `json:"boot_mark"`
	UpdateMark     string `json:"update_mark"`
	Idfa           string `json:"idfa"`
	IdfaMd5        string `json:"idfa_md5"`
	Caid           string `json:"caid"`
	Caidv          string `json:"caidv"`
	Devicetype     int    `json:"devicetype"`
	H              int    `json:"h"`
	W              int    `json:"w"`
	Connectiontype int    `json:"connectiontype"`
}

type XingFanResponseObject struct {
	Code    int                             `json:"code"`
	Id      string                          `json:"id"`
	Bidid   string                          `json:"bidid"`
	Seatbid []*XingFanResponseSeatbidObject `json:"seatbid"`
}
type XingFanResponseSeatbidObject struct {
	Bid []*XingFanResponseBidObject `json:"bid"`
}

type XingFanResponseBidObject struct {
	Price         int                              `json:"price"`
	IsDownload    int                              `json:"is_download"`
	Impid         string                           `json:"impid"`
	Title         string                           `json:"title"`
	Desc          string                           `json:"desc"`
	Ldp           string                           `json:"ldp"`
	Deeplink      string                           `json:"deeplink"`
	Crid          string                           `json:"crid"`
	BrandName     string                           `json:"brand_name"`
	BrandLogo     string                           `json:"brand_logo"`
	Imptrackers   []string                         `json:"imptrackers"`
	Clicktrackers []string                         `json:"clicktrackers"`
	DpTracking    []string                         `json:"dp_tracking"`
	AppInfo       *XingFanResponseBidAppInfoObject `json:"app_info"`
	Image         []*XingFanResponseBidImageObject `json:"image"`
	Video         *XingFanResponseBidVideoObject   `json:"video"`
}

type XingFanResponseBidAppInfoObject struct {
	Size               int                              `json:"size"`
	AppName            string                           `json:"app_name"`
	Pkgname            string                           `json:"pkgname"`
	AppDesc            string                           `json:"app_desc"`
	VersionCode        string                           `json:"version_code"`
	DeveloperName      string                           `json:"developer_name"`
	PrivacyPolicy      string                           `json:"privacy_policy"`
	AppPermissionsLink string                           `json:"app_permissions_link"`
	AppDescUrl         string                           `json:"app_desc_url"`
	AppLogo            []*XingFanResponseBidImageObject `json:"app_logo"`
}

type XingFanResponseBidImageObject struct {
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Url    string `json:"url"`
}

type XingFanResponseBidVideoObject struct {
	Duration int    `json:"duration"`
	Vw       int    `json:"vw"`
	Vh       int    `json:"vh"`
	Vurl     string `json:"vurl"`
}
