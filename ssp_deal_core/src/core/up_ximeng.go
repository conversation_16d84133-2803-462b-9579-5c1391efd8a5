package core

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/ximeng_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func GetFromXimeng(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo *models.CategoryStu, bigdataUID string) *models.MHUpResp {
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2

	var assetArray []*ximeng_up.BidRequest_Imp_Asset
	if len(platformPos.PlatformPosStyleIDs) > 0 {
		styleIds := strings.Split(platformPos.PlatformPosStyleIDs, ",")
		for _, id := range styleIds {
			asset := ximeng_up.BidRequest_Imp_Asset{
				TemplateId: id,
				Width:      int32(platformPos.PlatformPosWidth),
				Height:     int32(platformPos.PlatformPosHeight),
			}
			assetArray = append(assetArray, &asset)
		}
	}

	var adType int
	switch platformPos.PlatformPosType {
	case 1:
		adType = 5
	case 2:
		adType = 1
	case 4:
		adType = 2
	case 11:
		adType = 3
	case 13:
		adType = 4
	}

	var imps []*ximeng_up.BidRequest_Imp
	imp := ximeng_up.BidRequest_Imp{
		Id:       uuid.NewV4().String(),
		TagId:    platformPos.PlatformPosID,
		AdType:   int32(adType),
		BidType:  0,
		BidFloor: int64(categoryInfo.FloorPrice),
		Assets:   assetArray,
	}
	imps = append(imps, &imp)

	requestObject := &ximeng_up.BidRequest{
		Id:          bigdataUID,
		ApiVersion:  "v1.2",
		RequestType: 1,
		Imps:        imps,
		App: &ximeng_up.BidRequest_App{
			Name:    platformPos.PlatformAppName,
			Bundle:  GetAppBundleByConfig(c, mhReq, localPos, platformPos),
			Version: platformPos.PlatformAppVersion,
		},
		Device: &ximeng_up.BidRequest_Device{
			Ua:           destConfigUA,
			Ipv4:         proto.String(mhReq.Device.IP),
			Osv:          mhReq.Device.OsVersion,
			DeviceType:   0,
			ScreenHeight: proto.Int32(int32(mhReq.Device.ScreenHeight)),
			ScreenWidth:  proto.Int32(int32(mhReq.Device.ScreenWidth)),
			HmsVersion:   proto.String(mhReq.Device.HMSCoreVersion),
			AgVersion:    proto.String(mhReq.Device.AppStoreVersion),
			MiuiVersion:  proto.String(mhReq.Device.MiuiVersion),
		},
		Timeout: uint32(time.Duration(timeout) * time.Millisecond),
	}

	switch mhReq.Network.ConnectType {
	case 0:
		requestObject.Device.NetworkType = 0
	case 1:
		requestObject.Device.NetworkType = 1
	case 2:
		requestObject.Device.NetworkType = 2
	case 3:
		requestObject.Device.NetworkType = 3
	case 4:
		requestObject.Device.NetworkType = 4
	case 7:
		requestObject.Device.NetworkType = 5
	default:
		requestObject.Device.NetworkType = 0
	}

	switch mhReq.Network.Carrier {
	case 1:
		requestObject.Device.Carrier = 1
	case 2:
		requestObject.Device.Carrier = 2
	case 3:
		requestObject.Device.Carrier = 3
	}

	if len(mhReq.Device.Manufacturer) > 0 {
		requestObject.Device.Make = mhReq.Device.Manufacturer
	}

	if len(mhReq.Device.Model) > 0 {
		requestObject.Device.Model = mhReq.Device.Model
	}

	// 原始请求ios参数是否ok
	isIosDeviceOK := false
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		requestObject.Device.Os = "android"
		requestObject.Device.AndroidId = proto.String(mhReq.Device.AndroidID)
		requestObject.Device.AndroidIdMd5 = proto.String(mhReq.Device.AndroidIDMd5)
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
		}
		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				requestObject.Device.Imei = proto.String(mhReq.Device.Imei)
				requestObject.Device.ImeiMd5 = proto.String(mhReq.Device.ImeiMd5)
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013
				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				requestObject.Device.Oaid = proto.String(mhReq.Device.Oaid)
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013
				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else {
		requestObject.Device.Os = "ios"
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				requestObject.Device.Idfa = proto.String(mhReq.Device.Idfa)

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				requestObject.Device.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				var caidArray []*ximeng_up.BidRequest_Caid
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						var caidInfo ximeng_up.BidRequest_Caid
						caidInfo.Id = item.CAID
						caidInfo.Version = item.CAIDVersion
						caidArray = append(caidArray, &caidInfo)
						break
					}
				}
				if len(caidArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					requestObject.Device.Caids = caidArray
				}
			} else {
				var caidArray []*ximeng_up.BidRequest_Caid
				for _, item := range mhReq.Device.CAIDMulti {
					var caidInfo ximeng_up.BidRequest_Caid
					caidInfo.Id = item.CAID
					caidInfo.Version = item.CAIDVersion
					caidArray = append(caidArray, &caidInfo)
				}
				if len(caidArray) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportCAID = true
					requestObject.Device.Caids = caidArray
				}
			}
		}

		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpTimeZone) > 0 {

				var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
				CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
				CAIDCriteria.CaidCountryCode = mhReq.Device.Country
				CAIDCriteria.CaidLanguage = mhReq.Device.Language
				CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
				CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
				CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
				CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
				CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
				CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
				CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
				CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
				requestObject.Device.CaidCriteria = &CAIDCriteria
				requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
				requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
				requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true
			}
		}

		if strings.Contains(iosReportMainParameter, "paid") {
			if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpDeviceBirthSec) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true
				requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
				requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					requestObject.Device.Idfa = proto.String(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					requestObject.Device.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []*ximeng_up.BidRequest_Caid
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo ximeng_up.BidRequest_Caid
							caidInfo.Id = item.CAID
							caidInfo.Version = item.CAIDVersion
							caidArray = append(caidArray, &caidInfo)
							break
						}
					}
					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						requestObject.Device.Caids = caidArray
					}
				} else {
					var caidArray []*ximeng_up.BidRequest_Caid
					for _, item := range mhReq.Device.CAIDMulti {
						var caidInfo ximeng_up.BidRequest_Caid
						caidInfo.Id = item.CAID
						caidInfo.Version = item.CAIDVersion
						caidArray = append(caidArray, &caidInfo)
					}
					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						requestObject.Device.Caids = caidArray
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
					CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
					CAIDCriteria.CaidCountryCode = mhReq.Device.Country
					CAIDCriteria.CaidLanguage = mhReq.Device.Language
					CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
					CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
					CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
					CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
					CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
					CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
					CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
					CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
					requestObject.Device.CaidCriteria = &CAIDCriteria
					requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
					requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
					requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
			if strings.Contains(iosReportSubParameter1, "paid") {
				if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpDeviceBirthSec) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
					requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
					requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
				}
			}
		}
		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					requestObject.Device.Idfa = proto.String(mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					requestObject.Device.IdfaMd5 = proto.String(strings.ToUpper(mhReq.Device.IdfaMd5))

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					var caidArray []*ximeng_up.BidRequest_Caid
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							var caidInfo ximeng_up.BidRequest_Caid
							caidInfo.Id = item.CAID
							caidInfo.Version = item.CAIDVersion
							caidArray = append(caidArray, &caidInfo)
							break
						}
					}
					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						requestObject.Device.Caids = caidArray
					}
				} else {
					var caidArray []*ximeng_up.BidRequest_Caid
					for _, item := range mhReq.Device.CAIDMulti {
						var caidInfo ximeng_up.BidRequest_Caid
						caidInfo.Id = item.CAID
						caidInfo.Version = item.CAIDVersion
						caidArray = append(caidArray, &caidInfo)
					}
					if len(caidArray) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true
						requestObject.Device.Caids = caidArray
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpTimeZone) > 0 {

					var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
					CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
					CAIDCriteria.CaidCountryCode = mhReq.Device.Country
					CAIDCriteria.CaidLanguage = mhReq.Device.Language
					CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
					CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
					CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
					CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
					CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
					CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
					CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
					CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
					requestObject.Device.CaidCriteria = &CAIDCriteria
					requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
					requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
					requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
				}
			}
			if strings.Contains(iosReportSubParameter2, "paid") {
				if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
					len(tmpDeviceBirthSec) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true
					requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
					requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
				}
			}
		}
		// 如果替换包开走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			fmt.Println("get from jd error req idfa idfa_md5")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006
					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					requestObject.Device.Osv = didRedisData.OsVersion
					requestObject.Device.Model = didRedisData.Model
					requestObject.Device.Make = didRedisData.Manufacturer

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							requestObject.Device.Imei = proto.String(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							requestObject.Device.ImeiMd5 = proto.String(strings.ToUpper(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							requestObject.Device.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}
						if len(didRedisData.AndroidID) > 0 {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								requestObject.Device.AndroidId = proto.String(didRedisData.AndroidID)
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016
								return MhUpErrorRespMap("", bigdataExtra)
							}
						}
						if len(didRedisData.AndroidIDMd5) > 0 && didRedisData.AndroidIDMd5 != utils.GetMd5("") {
							if platformPos.PlatformAppIsReportAndroidID == 1 {
								requestObject.Device.AndroidIdMd5 = proto.String(strings.ToLower(didRedisData.AndroidIDMd5))
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 104016
								return MhUpErrorRespMap("", bigdataExtra)
							}
						}
					}

					// network -> ua, headers -> ua
					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						requestObject.Device.Ua = didRedisData.Ua
						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006
							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						_ = json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						_ = json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						requestObject.Device.Osv = didRedisData.OsVersion
						requestObject.Device.Model = didRedisData.Model
						requestObject.Device.Make = didRedisData.Manufacturer

						// 替换请求ios参数是否ok
						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								requestObject.Device.Idfa = proto.String(didRedisData.Idfa)
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								requestObject.Device.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))
								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								var caidArray []*ximeng_up.BidRequest_Caid
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										var caidInfo ximeng_up.BidRequest_Caid
										caidInfo.Id = item.CAID
										caidInfo.Version = item.CAIDVersion
										caidArray = append(caidArray, &caidInfo)
										break
									}
								}
								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									requestObject.Device.Caids = caidArray
								}
							} else {
								var caidArray []*ximeng_up.BidRequest_Caid
								for _, item := range tmpCAIDMulti {
									var caidInfo ximeng_up.BidRequest_Caid
									caidInfo.Id = item.CAID
									caidInfo.Version = item.CAIDVersion
									caidArray = append(caidArray, &caidInfo)
								}
								if len(caidArray) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true
									requestObject.Device.Caids = caidArray
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpTimeZone) > 0 {

								var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
								CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
								CAIDCriteria.CaidCountryCode = mhReq.Device.Country
								CAIDCriteria.CaidLanguage = mhReq.Device.Language
								CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
								CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
								CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
								CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
								CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
								CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
								CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
								CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
								requestObject.Device.CaidCriteria = &CAIDCriteria
								requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
								requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
								requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true
							}
						}

						if strings.Contains(iosReportMainParameter, "paid") {
							tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpDeviceBirthSec = didRedisData.DeviceBirthSec
							tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
								len(tmpDeviceBirthSec) > 0 {
								isIosDeviceOK = true
								isIOSToUpReportYinZi = true
								requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
								requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									requestObject.Device.Idfa = proto.String(didRedisData.Idfa)
									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}

								if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									requestObject.Device.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))
									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*ximeng_up.BidRequest_Caid
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo ximeng_up.BidRequest_Caid
											caidInfo.Id = item.CAID
											caidInfo.Version = item.CAIDVersion
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										requestObject.Device.Caids = caidArray
									}
								} else {
									var caidArray []*ximeng_up.BidRequest_Caid
									for _, item := range tmpCAIDMulti {
										var caidInfo ximeng_up.BidRequest_Caid
										caidInfo.Id = item.CAID
										caidInfo.Version = item.CAIDVersion
										caidArray = append(caidArray, &caidInfo)
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										requestObject.Device.Caids = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
									CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
									CAIDCriteria.CaidCountryCode = mhReq.Device.Country
									CAIDCriteria.CaidLanguage = mhReq.Device.Language
									CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
									CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
									CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
									CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
									CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
									CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
									CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
									CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
									requestObject.Device.CaidCriteria = &CAIDCriteria
									requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
									requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
									requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							} else if strings.Contains(iosReportSubParameter1, "paid") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpDeviceBirthSec) > 0 {
									isIosDeviceOK = true
									isIOSToUpReportYinZi = true
									requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
									requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									requestObject.Device.Idfa = proto.String(didRedisData.Idfa)
									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}

								if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									requestObject.Device.IdfaMd5 = proto.String(strings.ToLower(didRedisData.IdfaMd5))
									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								_ = json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									var caidArray []*ximeng_up.BidRequest_Caid
									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											var caidInfo ximeng_up.BidRequest_Caid
											caidInfo.Id = item.CAID
											caidInfo.Version = item.CAIDVersion
											caidArray = append(caidArray, &caidInfo)
											break
										}
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										requestObject.Device.Caids = caidArray
									}
								} else {
									var caidArray []*ximeng_up.BidRequest_Caid
									for _, item := range tmpCAIDMulti {
										var caidInfo ximeng_up.BidRequest_Caid
										caidInfo.Id = item.CAID
										caidInfo.Version = item.CAIDVersion
										caidArray = append(caidArray, &caidInfo)
									}
									if len(caidArray) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true
										requestObject.Device.Caids = caidArray
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpDeviceStartSec) > 0 && len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 && len(tmpHardwareModel) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpTimeZone) > 0 {

									var CAIDCriteria ximeng_up.BidRequest_Device_CAIDCriteria
									CAIDCriteria.CaidBootSec = mhReq.Device.DeviceStartSec
									CAIDCriteria.CaidCountryCode = mhReq.Device.Country
									CAIDCriteria.CaidLanguage = mhReq.Device.Language
									CAIDCriteria.CaidDeviceNameMd5 = mhReq.Device.DeviceNameMd5
									CAIDCriteria.CaidHardwareMachine = mhReq.Device.HardwareMachine
									CAIDCriteria.CaidPhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
									CAIDCriteria.CaidHarddiskSizeByte = mhReq.Device.HarddiskSizeByte
									CAIDCriteria.CaidSystemUpdateSec = mhReq.Device.SystemUpdateSec
									CAIDCriteria.CaidHardwareModel = mhReq.Device.HardwareModel
									CAIDCriteria.CaidTimeZone = mhReq.Device.TimeZone
									CAIDCriteria.CaidFileInitTime = mhReq.Device.DeviceBirthSec
									requestObject.Device.CaidCriteria = &CAIDCriteria
									requestObject.Device.BootTs = proto.String(mhReq.Device.DeviceStartSec)
									requestObject.Device.UpdateTs = proto.String(mhReq.Device.SystemUpdateSec)
									requestObject.Device.BirthTs = proto.String(mhReq.Device.DeviceBirthSec)

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true
								}
							} else if strings.Contains(iosReportSubParameter2, "paid") {
								tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpDeviceBirthSec = didRedisData.DeviceBirthSec
								tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
									len(tmpDeviceBirthSec) > 0 {
									isIosDeviceOK = true
									isIOSToUpReportYinZi = true
									requestObject.Device.Paid_1_3 = proto.String(utils.GetMd5(tmpDeviceStartSec + ":" + tmpSystemUpdateSec))
									requestObject.Device.Paid_1_4 = proto.String(utils.GetMd5(tmpDeviceBirthSec) + "-" + utils.GetMd5(tmpSystemUpdateSec) + "-" + utils.GetMd5(tmpDeviceStartSec))
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							requestObject.Device.Ua = didRedisData.Ua
							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006
								return MhUpErrorRespMap("", bigdataExtra)
							}
						}
						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006
					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006
				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006
			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}
	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006
				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	marshal, _ := proto.Marshal(requestObject)
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/x-protobuf"
	httpHeader["Connection"] = "keep-alive"
	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(bytes.NewReader(marshal)))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode == 204 {
		fmt.Println("no fill")
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		if err != nil {
			fmt.Println("status is no 200: " + err.Error())
		}
		fmt.Println("status is no 200")
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	var responseObject ximeng_up.BidResponse
	_ = proto.Unmarshal(bodyContent, &responseObject)

	respCode := responseObject.GetCode()
	if respCode != 1000 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	bigdataExtra.UpRespTime = 1

	seatBids := responseObject.GetSeatBids()
	if seatBids == nil {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 配置price
	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, categoryInfo)

	var list []*models.MHRespDataItem
	var commonAPIResponseObject models.MHRespDataItem

	for _, seatBid := range seatBids {
		bids := seatBid.GetBids()
		for _, bid := range bids {
			respTmpRespAllNum = respTmpRespAllNum + 1

			bidPrice := int(bid.GetPrice())

			respTmpPrice = respTmpPrice + bidPrice

			if platformPos.PlatformPosEcpmType == 1 {
				bidPrice = platformPos.PlatformPosEcpm
			}

			macroLossPrice := getXimengLossPrice(platformPos, bidPrice)

			if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
				if localPosFinalPrice > bidPrice {
					respTmpInternalCode = 900104
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					if len(bid.GetLossNoticeUrls()) > 0 {
						go func() {
							defer func() {
								if err := recover(); err != nil {
									fmt.Println("xm loss url panic:", err)
								}
							}()
							for _, lossUrl := range bid.GetLossNoticeUrls() {
								lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
								curlXmNurl(lossUrl)
							}
						}()
						bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
					}
					continue
				}
			}

			// 填充后限制
			isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, categoryInfo, &bigdataExtra)
			if isAllInOneLimitAfterUpRespOK {
			} else {
				respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				if len(bid.GetLossNoticeUrls()) > 0 {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								fmt.Println("xm loss url panic:", err)
							}
						}()
						for _, lossUrl := range bid.GetLossNoticeUrls() {
							lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
							curlXmNurl(lossUrl)
						}
					}()
					bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
				}
				continue
			}

			maplehazeAdId := uuid.NewV4().String()
			commonAPIResponseObject.AdID = maplehazeAdId

			// 下发ecpm
			tmpEcpm := 0
			if localPos.LocalPosEcpmType == 0 {
				// 不下发
				tmpEcpm = localPos.LocalPosEcpm
			} else if localPos.LocalPosEcpmType == 1 {
				tmpEcpm = int(float32(bidPrice) * (float32(100) - localPosProfitRate) / 100)
				if tmpEcpm <= 0 {
					tmpEcpm = 1
				}
				commonAPIResponseObject.Ecpm = tmpEcpm
			} else if localPos.LocalPosEcpmType == 2 {
				tmpEcpm = localPos.LocalPosEcpm
				commonAPIResponseObject.Ecpm = localPos.LocalPosEcpm
			}

			if len(platformPos.PlatformPosStyleIDs) > 0 {
				styleFlag := 0
				styleIds := strings.Split(platformPos.PlatformPosStyleIDs, ",")
				for _, styleId := range styleIds {
					if styleId == bid.GetAdm().GetTemplateId() {
						styleFlag = 1
					}
				}

				if styleFlag == 0 {
					respTmpInternalCode = 900103
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					if len(bid.GetLossNoticeUrls()) > 0 {
						go func() {
							defer func() {
								if err := recover(); err != nil {
									fmt.Println("xm loss url panic:", err)
								}
							}()
							for _, lossUrl := range bid.GetLossNoticeUrls() {
								lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
								curlXmNurl(lossUrl)
							}
						}()
						bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
					}
					continue
				}
			}

			if len(bid.GetAdm().GetTitle()) > 0 {
				commonAPIResponseObject.Title = bid.GetAdm().GetTitle()
			}

			if len(bid.GetAdm().GetDesc()) > 0 {
				commonAPIResponseObject.Description = bid.GetAdm().GetDesc()
			}

			switch bid.GetAdm().GetActionType() {
			case 1, 2:
				commonAPIResponseObject.InteractType = 0
			case 3, 4:
				commonAPIResponseObject.InteractType = 1
			}

			commonAPIResponseObject.LandpageURL = bid.GetAdm().GetLandingPage()
			commonAPIResponseObject.DeepLink = bid.GetAdm().GetDeepLink()
			commonAPIResponseObject.Crid = bid.GetAdm().GetCreativeId()
			commonAPIResponseObject.ReqWidth = platformPos.PlatformPosWidth
			commonAPIResponseObject.ReqHeight = platformPos.PlatformPosHeight

			if len(bid.GetAdm().GetDeepLink()) > 0 {
				// deeplink track
				var respListItemDeepLinkArray []string

				mhDPParams := url.Values{}
				mhDPParams.Add("result", "0")
				mhDPParams.Add("reason", "")
				mhDPParams.Add("deeptype", "__DEEP_TYPE__")
				bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
				mhDPParams.Add("log", bigdataParams)

				respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

				var convTrackArray []models.MHRespConvTracks

				var deeplinkConvTrack models.MHRespConvTracks
				deeplinkConvTrack.ConvType = 10
				deeplinkConvTrack.ConvURLS = respListItemDeepLinkArray

				convTrackArray = append(convTrackArray, deeplinkConvTrack)

				// deeplink failed track
				if platformPos.PlatformAppIsDeepLinkFailed == 1 {
					var respListItemDeepLinkFailedArray []string

					mhDPFailedParams := url.Values{}
					mhDPFailedParams.Add("result", "1")
					mhDPFailedParams.Add("reason", "3")
					mhDPFailedParams.Add("log", bigdataParams)

					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

					var deeplinkFailedConvTrack models.MHRespConvTracks
					deeplinkFailedConvTrack.ConvType = 11
					deeplinkFailedConvTrack.ConvURLS = respListItemDeepLinkFailedArray

					convTrackArray = append(convTrackArray, deeplinkFailedConvTrack)
				}

				commonAPIResponseObject.ConvTracks = convTrackArray
			}

			var respListItemDeepLinkArray []string
			var respListItemShowArray []string
			var respListItemClickArray []string

			macroWinPrice := getXimengWinPrice(platformPos, bidPrice)

			tmpDownX := "__DOWN_X__"
			tmpDownY := "__DOWN_Y__"
			tmpUpX := "__UP_X__"
			tmpUpY := "__UP_Y__"
			tmpIsServerRealReplaceXY := false
			tmpServerRealReplaceXYType := 0
			if platformPos.PlatformPosIsReplaceXY == 1 {
				if platformPos.PlatformPosReplaceXYType == 0 {
					redisDownX, redisDownY, redisUpX, redisUpY, redisClickXYType, redisErr := models.GetNewReplaceClickXYFromRedis(c, localPos, platformPos, bigdataUID)
					if redisErr != nil {
						if len(bid.GetLossNoticeUrls()) > 0 {
							go func() {
								defer func() {
									if err := recover(); err != nil {
										fmt.Println("xm loss url panic:", err)
									}
								}()
								for _, lossUrl := range bid.GetLossNoticeUrls() {
									lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
									curlXmNurl(lossUrl)
								}
							}()
							bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
						}
						respTmpInternalCode = 900101
						respTmpRespFailedNum = respTmpRespFailedNum + 1

						continue
					} else {
						tmpDownX = utils.ConvertIntToString(redisDownX)
						tmpDownY = utils.ConvertIntToString(redisDownY)
						tmpUpX = utils.ConvertIntToString(redisUpX)
						tmpUpY = utils.ConvertIntToString(redisUpY)

						if redisClickXYType == "0" { // 物理像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 0
						} else if redisClickXYType == "1" { // 逻辑像素
							tmpIsServerRealReplaceXY = true
							tmpServerRealReplaceXYType = 1
						}
					}
				} else if platformPos.PlatformPosReplaceXYType == 1 {
					// -999
					tmpDownX = "-999"
					tmpDownY = "-999"
					tmpUpX = "-999"
					tmpUpY = "-999"
				} else if platformPos.PlatformPosReplaceXYType == 2 {
					// 替换空
					tmpDownX = ""
					tmpDownY = ""
					tmpUpX = ""
					tmpUpY = ""
				} else if platformPos.PlatformPosReplaceXYType == 3 {
					// 替换0
					tmpDownX = "0"
					tmpDownY = "0"
					tmpUpX = "0"
					tmpUpY = "0"
				}
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(
				mhReq,
				localPos,
				platformPos,
				bigdataUID,
				maplehazeAdId,
				categoryInfo.FloorPrice,
				categoryInfo.FinalPrice,
				bidPrice,
				tmpEcpm)

			mhDPParams.Add("log", bigdataParams)
			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			if len(bid.GetImpTrackers()) > 0 {
				for _, impUrl := range bid.GetImpTrackers() {
					respListItemShowArray = append(respListItemShowArray, strings.Replace(impUrl, "__SETTLE_PRICE__", macroWinPrice, -1))
				}
			}

			if len(bid.GetClkTrackers()) > 0 {
				for _, clkUrl := range bid.GetClkTrackers() {
					if IsAdIDReplaceXYByOS(c, mhReq) {
						if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
							clkUrl = clkUrl + "&is_adid_replace_xy=0"
						}
						if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
							clkUrl = clkUrl + "&replace_adid_sim_xy_default=1"
							commonAPIResponseObject.ReplaceAdIdSimXYDefaultValue = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
						}
					}
					clkUrl = strings.Replace(clkUrl, "__DOWN_X__", tmpDownX, -1)
					clkUrl = strings.Replace(clkUrl, "__DOWN_Y__", tmpDownY, -1)
					clkUrl = strings.Replace(clkUrl, "__UP_X__", tmpUpX, -1)
					clkUrl = strings.Replace(clkUrl, "__UP_Y__", tmpUpY, -1)
					respListItemClickArray = append(respListItemClickArray, strings.Replace(clkUrl, "__SETTLE_PRICE__", macroWinPrice, -1))
				}
			}

			// click_link maplehaze
			if len(respListItemClickArray) > 0 {
				mhClkParams := url.Values{}
				mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
				mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
				mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
				mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
				mhClkParams.Add("down_x", tmpDownX)
				mhClkParams.Add("down_y", tmpDownY)
				mhClkParams.Add("up_x", tmpUpX)
				mhClkParams.Add("up_y", tmpUpY)
				mhClkParams.Add("mh_down_x", "__DOWN_X__")
				mhClkParams.Add("mh_down_y", "__DOWN_Y__")
				mhClkParams.Add("mh_up_x", "__UP_X__")
				mhClkParams.Add("mh_up_y", "__UP_Y__")
				mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
				mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
				mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
				mhClkParams.Add("turnx", "__TURN_X__")
				mhClkParams.Add("turny", "__TURN_Y__")
				mhClkParams.Add("turnz", "__TURN_Z__")
				mhClkParams.Add("turntime", "__TURN_TIME__")

				if platformPos.PlatformPosIsReportSLD == 1 {
					mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
				} else {
					mhClkParams.Add("sld", "__SLD__")
				}
				mhClkParams.Add("log", bigdataParams)

				respListItemClickArray = append(respListItemClickArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
				commonAPIResponseObject.ClickLink = respListItemClickArray
			}

			if len(respListItemShowArray) > 0 {
				expUrl := url.Values{}
				if len(bid.GetWinNoticeUrls()) > 0 {
					var tmpWinNoticeURLs []string
					for _, winUrl := range bid.GetWinNoticeUrls() {
						winUrl = strings.Replace(winUrl, "__SETTLE_PRICE__", macroWinPrice, -1)
						tmpWinNoticeURLs = append(tmpWinNoticeURLs, winUrl)
					}
					tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
					tmpEncryptWinArray := utils.AesECBEncrypt(tmpWinArrayJSON, []byte(config.EncryptKEY))
					expUrl.Add("demand_win_links", base64.StdEncoding.EncodeToString(tmpEncryptWinArray))
				}
				bigdataParams = up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, bidPrice, isHaveReplace, bigdataReplaceDIDData, tmpIsServerRealReplaceXY, tmpServerRealReplaceXYType, tmpEcpm)
				expUrl.Add("log", bigdataParams)
				respListItemShowArray = append(respListItemShowArray, config.ExternalExpURL+"?"+expUrl.Encode())
				commonAPIResponseObject.ImpressionLink = respListItemShowArray
			}

			// win notice url
			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				commonAPIResponseObject.WinNoticeURL = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()
				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")

				if len(bid.GetLossNoticeUrls()) > 0 {
					var tmpLossNoticeURLs []string
					for _, lossUrl := range bid.GetLossNoticeUrls() {
						lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
						tmpLossNoticeURLs = append(tmpLossNoticeURLs, lossUrl)
					}

					tmpLossArrayJSON, _ := json.Marshal(tmpLossNoticeURLs)
					tmpEncryptLossArray := utils.AesECBEncrypt(tmpLossArrayJSON, []byte(config.EncryptKEY))
					mhLossNoticeURLParams.Add("demand_loss_links", base64.StdEncoding.EncodeToString(tmpEncryptLossArray))
				}

				commonAPIResponseObject.LossNoticeURL = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}
			commonAPIResponseObject.Log = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

			if bid.GetAdm().GetImages() == nil && bid.GetAdm().GetVideo() == nil {
				respTmpInternalCode = 900103
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				if len(bid.GetLossNoticeUrls()) > 0 {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								fmt.Println("xm loss url panic:", err)
							}
						}()
						for _, lossUrl := range bid.GetLossNoticeUrls() {
							lossUrl = strings.Replace(lossUrl, "__SETTLE_PRICE__", macroLossPrice, -1)
							curlXmNurl(lossUrl)
						}
					}()
					bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
				}
				continue
			}

			if bid.GetAdm().GetVideo() != nil {
				var commonAPIResponseVideoObject models.MHRespVideo
				commonAPIResponseVideoObject.VideoURL = bid.GetAdm().GetVideo().GetVideoUrl()
				if int(bid.GetAdm().GetVideo().GetWidth()) > 0 {
					commonAPIResponseVideoObject.Width = int(bid.GetAdm().GetVideo().GetWidth())
				} else {
					commonAPIResponseVideoObject.Width = platformPos.PlatformPosWidth
				}

				if int(bid.GetAdm().GetVideo().GetHeight()) > 0 {
					commonAPIResponseVideoObject.Height = int(bid.GetAdm().GetVideo().GetHeight())
				} else {
					commonAPIResponseVideoObject.Height = platformPos.PlatformPosHeight
				}

				commonAPIResponseVideoObject.CoverURL = bid.GetAdm().GetVideo().GetCover()
				commonAPIResponseVideoObject.Duration = int(bid.GetAdm().GetVideo().GetDuration() * 1000)

				commonAPIResponseObject.CrtType = 20
				commonAPIResponseObject.Video = &commonAPIResponseVideoObject
			} else {
				var commonAPIResponseImageObjects []models.MHRespImage
				for _, image := range bid.GetAdm().GetImages() {
					var commonAPIResponseImageObject models.MHRespImage
					commonAPIResponseImageObject.URL = image.GetUrl()
					if int(image.GetWidth()) > 0 {
						commonAPIResponseImageObject.Width = int(image.GetWidth())
					} else {
						commonAPIResponseImageObject.Width = platformPos.PlatformPosWidth
					}
					if int(image.GetHeight()) > 0 {
						commonAPIResponseImageObject.Height = int(image.GetWidth())
					} else {
						commonAPIResponseImageObject.Height = platformPos.PlatformPosHeight
					}
					commonAPIResponseImageObjects = append(commonAPIResponseImageObjects, commonAPIResponseImageObject)
				}
				commonAPIResponseObject.CrtType = 11
				commonAPIResponseObject.Image = commonAPIResponseImageObjects
			}
			if bid.GetAdm().GetAppInfo() != nil {
				commonAPIResponseObject.DownloadURL = bid.GetAdm().GetAppInfo().GetDownloadUrl()
				commonAPIResponseObject.PackageName = bid.GetAdm().GetAppInfo().GetPackageName()
				commonAPIResponseObject.IconURL = bid.GetAdm().GetAppInfo().GetIconImageUrl()

				if bid.GetAdm().GetAppInfo().GetComplianceInfo() != nil {
					commonAPIResponseObject.AppName = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetName()
					commonAPIResponseObject.AppVersion = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetVersion()
					commonAPIResponseObject.Publisher = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetDeveloper()
					commonAPIResponseObject.PrivacyLink = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetPrivacyUrl()
					commonAPIResponseObject.PermissionURL = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetPermissionsUrl()
					commonAPIResponseObject.AppInfoURL = bid.GetAdm().GetAppInfo().GetComplianceInfo().GetFunctionDescUrl()
				}
			}

			commonAPIResponseObject.MaterialDirection = platformPos.PlatformPosDirection
			commonAPIResponseObject.PEcpm = bidPrice
			list = append(list, &commonAPIResponseObject)
		}
	}

	if len(list) == 0 {
		fmt.Println("ad item is empty")
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespFailedNum
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// 补充 ResponseExtra
	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	respListMap := map[string]interface{}{}
	respListMap["list"] = list

	commonResponseDataObject := map[string]interface{}{}
	commonResponseDataObject[localPos.LocalPosID] = respListMap

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	respMap["data"] = commonResponseDataObject

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// 喜盟 resp
	respXM := models.MHUpResp{}
	respXM.RespData = &mhResp
	respXM.Extra = bigdataExtra

	respTJStr, _ := json.Marshal(respXM)
	fmt.Println(string(respTJStr))
	return &respXM
}

func aesEcbPkcs5PaddingEncryptHex(content string, key string) (string, error) {
	rawKey := []byte(key)
	block, err := aes.NewCipher(rawKey)
	if err != nil {
		return "", err
	}

	contentBytes := []byte(content)
	paddedContent := pkcs5Padding(contentBytes, aes.BlockSize)

	encrypted := make([]byte, len(paddedContent))
	ecb := NewECBEncrypter(block)
	ecb.CryptBlocks(encrypted, paddedContent)

	return hex.EncodeToString(encrypted), nil
}

func pkcs5Padding(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

type ecbEncrypter struct {
	b         cipher.Block
	blockSize int
}

func NewECBEncrypter(b cipher.Block) cipher.BlockMode {
	return &ecbEncrypter{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

func curlXmNurl(nurl string) string {
	client := &http.Client{Timeout: 1000 * time.Millisecond}
	requestGet, _ := http.NewRequest("GET", nurl, nil)
	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		return ""
	}

	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	return string(bodyContent)
}

func getXimengWinPrice(platformPos *models.PlatformPosStu, ecpm int) string {
	if ecpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && platformPos.PlatformAppIsReportWin == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}

		macroPrice := utils.ConvertIntToString(ecpm * randPRValue / 100)
		priceKey := platformPos.PlatformAppPriceEncrypt
		macroPrice, _ = aesEcbPkcs5PaddingEncryptHex(macroPrice, priceKey)

		return macroPrice
	}

	return ""
}

func getXimengLossPrice(platformPos *models.PlatformPosStu, ecpm int) string {
	if ecpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 && platformPos.PlatformAppIsReportLoss == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
		randPRValue := 100
		tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMinWeight)
		tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportLossMaxWeight)
		if tmp1 <= tmp2 {
			randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
		} else {
			return ""
		}

		macroPrice := utils.ConvertIntToString(ecpm * randPRValue / 100)
		priceKey := platformPos.PlatformAppPriceEncrypt
		macroPrice, _ = aesEcbPkcs5PaddingEncryptHex(macroPrice, priceKey)

		return macroPrice
	}

	return ""
}
