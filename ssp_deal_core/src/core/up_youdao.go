package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromYouDao ...
func GetFromYouDao(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from youdao")

	// test 开屏
	// platformPos.PlatformPosID = "2deb58278e91552039bfe8dd64ca70fd"
	// test 原生
	// platformPos.PlatformPosID = "d17484fd4499c52f20e0c9a53154ab0a"
	// test 原生视频
	// platformPos.PlatformPosID = "aa6b24e82a340504a5b7bc8af7a1a01a"
	// test 激励视频
	// platformPos.PlatformPosID = "fbf4ad6fe7cfaedb9a2d4c9604e317ff"

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// client := &http.Client{}

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	connectTypeStr := "0"
	dctStr := "0"
	if mhReq.Network.ConnectType == 0 {
		connectTypeStr = "0"
	} else if mhReq.Network.ConnectType == 1 {
		connectTypeStr = "2"
	} else if mhReq.Network.ConnectType == 2 {
		connectTypeStr = "3"
		dctStr = "11"
	} else if mhReq.Network.ConnectType == 3 {
		connectTypeStr = "3"
		dctStr = "12"
	} else if mhReq.Network.ConnectType == 4 {
		connectTypeStr = "3"
		dctStr = "13"
	} else if mhReq.Network.ConnectType == 7 {
		connectTypeStr = "3"
		dctStr = "50"
	}

	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	requestGet, _ := http.NewRequestWithContext(c, "GET", config.UpYouDaoURL, nil)

	requestGet.Header.Add("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)

	q := requestGet.URL.Query()
	q.Add("id", platformPos.PlatformPosID)
	q.Add("av", platformPos.PlatformAppVersion)
	q.Add("ct", connectTypeStr)
	q.Add("dct", dctStr)
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			// if len(mhReq.Device.AndroidID) > 0 {
			// 	q.Add("udid", strings.ToUpper(mhReq.Device.AndroidID))
			// 	q.Add("auidmd5", "")

			// 	extraReportParams = extraReportParams + ",android_id"
			// } else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
			// 	q.Add("udid", "")
			// 	q.Add("auidmd5", strings.ToUpper(mhReq.Device.AndroidIDMd5))

			// 	extraReportParams = extraReportParams + ",android_id_md5"
			// } else {
			// 	q.Add("udid", "")
			// 	q.Add("auidmd5", "")
			// }
			q.Add("udid", "")
			q.Add("auidmd5", "")

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true

				q.Add("imei", mhReq.Device.Imei)
				q.Add("imeimd5", "")
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true

				q.Add("imei", "")
				q.Add("imeimd5", strings.ToUpper(mhReq.Device.ImeiMd5))
			} else {
				q.Add("imei", "")
				q.Add("imeimd5", "")
			}
			q.Add("oaid", "")

			if isImeiOK {
			} else {
				fmt.Println("get from youdao error req < 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			// if len(mhReq.Device.AndroidID) > 0 {
			// 	q.Add("udid", strings.ToUpper(mhReq.Device.AndroidID))
			// 	q.Add("auidmd5", "")

			// 	extraReportParams = extraReportParams + ",android_id"
			// } else if len(mhReq.Device.AndroidIDMd5) > 0 && mhReq.Device.AndroidIDMd5 != utils.GetMd5("") {
			// 	q.Add("udid", "")
			// 	q.Add("auidmd5", strings.ToUpper(mhReq.Device.AndroidIDMd5))

			// 	extraReportParams = extraReportParams + ",android_id_md5"
			// } else {
			// 	q.Add("udid", "")
			// 	q.Add("auidmd5", "")
			// }
			q.Add("udid", "")
			q.Add("auidmd5", "")

			q.Add("imei", "")
			q.Add("imeimd5", "")

			if len(mhReq.Device.Oaid) > 0 {
				q.Add("oaid", mhReq.Device.Oaid)
			} else {
				fmt.Println("get from youdao error req > 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		if len(mhReq.Device.Idfa) > 0 {
			q.Add("udid", strings.ToUpper(mhReq.Device.Idfa))
			q.Add("auidmd5", "")
			q.Add("imei", "")
			q.Add("imeimd5", "")
			q.Add("oaid", "")
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}
	q.Add("aaid", "")
	q.Add("caid", "")
	q.Add("rip", mhReq.Device.IP)
	q.Add("ll", "")
	q.Add("lla", "")
	q.Add("llt", "")
	q.Add("llp", "")
	q.Add("wifi", "")
	if mhReq.Device.Os == "android" {
		q.Add("dn", mhReq.Device.Manufacturer+","+mhReq.Device.Model+","+mhReq.Device.Model)
	} else if mhReq.Device.Os == "ios" {
		q.Add("dn", mhReq.Device.Manufacturer)
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					q.Del("udid")
					q.Del("auidmd5")
					q.Del("imei")
					q.Del("imeimd5")
					q.Del("oaid")
					q.Del("dn")

					q.Add("udid", "")
					q.Add("auidmd5", "")
					q.Add("imei", "")
					q.Add("dn", didRedisData.Manufacturer+","+didRedisData.Model+","+didRedisData.Model)
					// gdtReplaceDeviceMap["manufacturer"] = didRedisData.Manufacturer
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}
					if osvMajor < 10 {

						if len(didRedisData.Imei) > 0 {
							q.Add("imeimd5", strings.ToUpper(utils.GetMd5(didRedisData.Imei)))
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							q.Add("imeimd5", strings.ToUpper(didRedisData.ImeiMd5))
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
						q.Add("oaid", "")

					} else {
						q.Add("imeimd5", "")
						if len(didRedisData.Oaid) > 0 {
							q.Add("oaid", didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true

			} else if mhReq.Device.Os == "ios" {
				// osv model key
				redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					q.Del("udid")
					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}
					if len(didRedisData.Idfa) > 0 {
						q.Add("udid", strings.ToUpper(didRedisData.Idfa))
					} else {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					}
				}
				isHaveReplace = true
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	requestGet.Header.Add("User-Agent", destConfigUA)

	requestGet.URL.RawQuery = q.Encode()

	// fmt.Println("youdao req: " + requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("youdao resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// fmt.Println("youdao resp header: ", resp.Header)
	// fmt.Println("youdao resp header: ", resp.Header.Get("X-Adstate"))
	// fmt.Println("youdao resp len(content): ", len(bodyContent))
	if len(bodyContent) == 0 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	youdaoRespStu := YouDaoRespStu{}
	json.Unmarshal([]byte(bodyContent), &youdaoRespStu)

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for i := 0; i < 1; i++ {
		adInfoItem := youdaoRespStu

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		} else if len(adInfoItem.AppName) > 0 {
			respListItemMap["title"] = adInfoItem.AppName
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		// crid
		respListItemMap["crid"] = utils.ConvertIntToString(adInfoItem.CreativeID)

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		tmpIsHasDeepLink := false
		if len(adInfoItem.Deeplink) > 0 {
			tmpIsHasDeepLink = true
			respListItemMap["deep_link"] = adInfoItem.Deeplink
		}

		// icon_url
		if len(adInfoItem.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Deeplink)
		}

		isVideo := false

		youdaoEcpm := int(adInfoItem.Ecpm)

		respTmpPrice = respTmpPrice + youdaoEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if youdaoEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			youdaoEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > youdaoEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(youdaoEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// 新增deepLink 判断
		if tmpIsHasDeepLink == true {
			// deeplink track
			var respListItemDeepLinkArray []string

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFloorPrice, youdaoEcpm, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// 视频
		if len(adInfoItem.VideoURL) > 0 {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if len(adInfoItem.VideoDuration) > 0 {
				timeDuration, err := time.Parse("15:04:05", adInfoItem.VideoDuration)
				if err == nil {
					respListVideoItemMap["duration"] = timeDuration.Minute()*60 + timeDuration.Second()

					// 过滤video_duration
					isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, (timeDuration.Minute()*60+timeDuration.Second())/1000)
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1
						continue
					}
				}
			}

			// cover_url
			if len(adInfoItem.CoverImage) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.CoverImage
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["video_url"] = adInfoItem.VideoURL
			if adInfoItem.VideoWidth > 0 {
				respListVideoItemMap["width"] = adInfoItem.VideoWidth
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}

			if adInfoItem.VideoHeight > 0 {
				respListVideoItemMap["height"] = adInfoItem.VideoHeight
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.VideoWidth > 0 && adInfoItem.VideoHeight > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.VideoWidth, adInfoItem.VideoHeight)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			// if platformPos.PlatformPosType == 11 {
			// 	var respListEventTrackURLMap []map[string]interface{}

			// 	respListVideoBeginEventTrackMap := map[string]interface{}{}
			// 	respListVideoBeginEventTrackMap["event_type"] = 100
			// 	var respListVideoBeginEventTrackURLMap []string
			// 	for _, monitorItem := range adInfoItem.VideoStartMonitorURLs {
			// 		respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorItem)
			// 	}
			// 	respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			// 	if len(respListVideoBeginEventTrackURLMap) > 0 {
			// 		respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			// 	}

			// 	respListVideoEndEventTrackMap := map[string]interface{}{}
			// 	respListVideoEndEventTrackMap["event_type"] = 103
			// 	var respListVideoEndEventTrackURLMap []string
			// 	for _, monitorItem := range adInfoItem.VideoFinishMonitorURLs {
			// 		respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, monitorItem)
			// 	}
			// 	respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			// 	if len(respListVideoEndEventTrackURLMap) > 0 {
			// 		respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			// 	}

			// 	if len(respListEventTrackURLMap) > 0 {
			// 		respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			// 	}
			// }

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if platformPos.PlatformPosType == 4 || platformPos.PlatformPosType == 2 {
			isVideo = false

			// imgs
			isHasImage := false
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.MainImage) > 0 {
				isHasImage = true
				respListImageItemMap["url"] = adInfoItem.MainImage
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			} else if len(adInfoItem.MainImage1) > 0 {
				isHasImage = true
				respListImageItemMap["url"] = adInfoItem.MainImage1
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}
			if isHasImage {
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// if adInfoItem.AdStyle == 4 {
			// 	respListImageItemMap["width"] = 1000
			// 	respListImageItemMap["height"] = 500
			// } else if adInfoItem.AdStyle == 6 || adInfoItem.AdStyle == 7 || adInfoItem.AdStyle == 50 {
			// 	respListImageItemMap["width"] = 480
			// 	respListImageItemMap["height"] = 320
			// } else if adInfoItem.AdStyle == 40 {
			// 	respListImageItemMap["width"] = 720
			// 	respListImageItemMap["height"] = 1280
			// }

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if adInfoItem.YdAdType == 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.ClickURL
			respListItemMap["landpage_url"] = adInfoItem.ClickURL
		} else if adInfoItem.YdAdType == 1 {

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.ClickURL
			respListItemMap["download_url"] = adInfoItem.ClickURL

		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoItem.PackageName
		}

		if len(adInfoItem.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppName
		}
		// 以下是有道新增返回字段
		if len(adInfoItem.YouDaoDownloadAppInfo.DeveloperName) > 0 {
			respListItemMap["publisher"] = adInfoItem.YouDaoDownloadAppInfo.DeveloperName
		}
		if len(adInfoItem.YouDaoDownloadAppInfo.AppVersion) > 0 {
			respListItemMap["app_version"] = adInfoItem.YouDaoDownloadAppInfo.AppVersion
		}
		if len(adInfoItem.YouDaoDownloadAppInfo.PrivacyPolicy) > 0 {
			respListItemMap["privacy_url"] = adInfoItem.YouDaoDownloadAppInfo.PrivacyPolicy
		}
		if len(adInfoItem.YouDaoDownloadAppInfo.AppPermission) > 0 {
			respListItemMap["permission_url"] = adInfoItem.YouDaoDownloadAppInfo.AppPermission
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, youdaoEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, impItem := range adInfoItem.Imptracker {
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, monitorItem := range adInfoItem.Clktrackers {
			clkItem := monitorItem

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}

		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = youdaoEcpm

		respListArray = append(respListArray, respListItemMap)
	}

	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// kuaishou resp
	respYouDao := models.MHUpResp{}
	respYouDao.RespData = &mhResp
	respYouDao.Extra = bigdataExtra
	// respYouDao.Extra.RespCount = len(respListArray)
	// respYouDao.Extra.ExternalCode = 0
	// respYouDao.Extra.InternalCode = 900000

	return &respYouDao
}

// YouDaoRespStu ...
type YouDaoRespStu struct {
	ClickURL              string                `json:"clk"`
	YdBid                 string                `json:"ydBid"`
	YdAdType              int                   `json:"ydAdType"`
	Imptracker            []string              `json:"imptracker"`
	Clktrackers           []string              `json:"clktrackers"`
	AppName               string                `json:"appName"`
	PackageName           string                `json:"packageName"`
	CreativeID            int                   `json:"creativeid"`
	MainImage             string                `json:"mainimage"`
	MainImage1            string                `json:"mainimage1"`
	Deeplink              string                `json:"deeplink"`
	IconURL               string                `json:"iconimage"`
	Title                 string                `json:"title"`
	Description           string                `json:"text"`
	VideoURL              string                `json:"videourl"`
	CoverImage            string                `json:"coverimage"`
	VideoWidth            int                   `json:"videowidth"`
	VideoHeight           int                   `json:"videoheight"`
	VideoDuration         string                `json:"videoduration"`
	VideoSize             int                   `json:"videosize"`
	Ecpm                  float32               `json:"ecpm"`
	YouDaoDownloadAppInfo YouDaoDownloadAppInfo `json:"downloadAppInfo"`
}

type YouDaoDownloadAppInfo struct {
	PrivacyPolicy string `json:"privacyPolicy"`
	DeveloperName string `json:"developerName"`
	AppVersion    string `json:"appVersion"`
	AppPermission string `json:"appPermission"`
}
