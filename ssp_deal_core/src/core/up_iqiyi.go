package core

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/iqiyi_up"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromIQiYi ...
func GetFromIQiYi(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from iqiyi")

	// test
	// platformPos.PlatformPosType = 4
	// platformPos.PlatformAppUpURL = "http://api-test.ssp.iqiyi.com/bid?a=1722430387709188&token=2b4f3e74d7f64cd4960c09a18f24b8fe"
	// platformPos.PlatformAppUpURL = "http://ssp.iqiyi.com/bid?a=1722430387709188"
	// platformPos.PlatformPosID = "1722443437525252"

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	// tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	tmpCountry := mhReq.Device.Country
	tmpLanguage := mhReq.Device.Language
	tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	// tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////
	iqiyiReqApp := &iqiyi_up.BidRequest_App{
		Name:          proto.String(platformPos.PlatformAppName),
		Bundle:        proto.String(GetAppBundleByConfig(c, mhReq, localPos, platformPos)),
		Ver:           proto.String(platformPos.PlatformAppVersion),
		Deeplinkstate: proto.Int32(2),
		// Content:       iqiyiReqAppContent,
	}

	iqiyiReqDevice := &iqiyi_up.BidRequest_Device{
		Geo: &iqiyi_up.BidRequest_Geo{
			Lat: proto.Float64(0),
			Lon: proto.Float64(0),
		},
		Ip:         &mhReq.Device.IP,
		Devicetype: iqiyi_up.BidRequest_Device_PHONE.Enum(),
		Mac:        &mhReq.Device.Mac,
		Osv:        &mhReq.Device.OsVersion,
		W:          proto.Int32(int32(mhReq.Device.ScreenWidth)),
		H:          proto.Int32(int32(mhReq.Device.ScreenHeight)),
		Model:      proto.String(mhReq.Device.Model),
	}

	var iqiyiReqUser iqiyi_up.BidRequest_User
	if platformPos.PlatformAppIsReportAppList == 1 {
		if len(mhReq.Device.AppList) > 0 {
			var applistArray []string
			// appListMapGroup := models2.GetAppListMapGroup1()
			// for _, appId := range mhReq.Device.AppList {
			// 	if val, ok := appListMapGroup[appId]; ok {
			// 		applistArray = append(applistArray, val.AndroidPackageName)
			// 	}
			// }

			for _, appListItem := range mhReq.Device.AppList {
				applistArray = append(applistArray, utils.ConvertIntToString(appListItem))

			}
			iqiyiReqUser.Applist = proto.String(strings.Join(applistArray, ","))
		}

		// 上报虚拟applist
		if platformPos.PlatformAppIsReportVirtualAppList == 1 &&
			utils.ConvertStringToInt(platformPos.PlatformAppVirtualPkgNum) > 0 &&
			len(platformPos.PlatformAppVirtualPkgLib) > 0 {
			if rand.Intn(100) < utils.ConvertStringToInt(platformPos.PlatformAppVirtualPkgWeight) {
				var tmpIQiYiAppList []string

				virtualPkgLibArray := strings.Split(platformPos.PlatformAppVirtualPkgLib, ",")
				virtualPkgLibLength := len(virtualPkgLibArray)
				if utils.ConvertStringToInt(platformPos.PlatformAppVirtualPkgNum) >= virtualPkgLibLength {
					tmpIQiYiAppList = append(tmpIQiYiAppList, virtualPkgLibArray...)
				} else {
					randPerms := rand.Perm(virtualPkgLibLength)
					randPerms = randPerms[:utils.ConvertStringToInt(platformPos.PlatformAppVirtualPkgNum)]
					for _, index := range randPerms {
						tmpIQiYiAppList = append(tmpIQiYiAppList, virtualPkgLibArray[index])
					}
				}

				if len(tmpIQiYiAppList) > 0 {
					tmpIQiYiAppList = append(tmpIQiYiAppList, strings.Split(iqiyiReqUser.GetApplist(), ",")...)
					iqiyiReqUser.Applist = proto.String(strings.Join(removeDuplicates(tmpIQiYiAppList), ","))
				}
			}
		}
	}

	iqiyiImeiMd5 := ""
	iqiyiAndroidID := ""
	iqiyiIDFA := ""
	if mhReq.Device.Os == "android" {
		manufacturer := strings.ToLower(mhReq.Device.Manufacturer)
		if _, ok := iqiyiMakeMap[manufacturer]; ok {
			iqiyiReqDevice.Make = proto.String(manufacturer)
		} else {
			iqiyiReqDevice.Make = proto.String("other")
		}

		iqiyiReqDevice.Os = proto.String("Android")

		// fmt.Println("os version: " + mhReq.Device.OsVersion)
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		isDeviceOK := false

		if osvMajor < 10 {
			if len(mhReq.Device.Imei) > 0 {
				isDeviceOK = true
				iqiyiReqDevice.ImeiMd5 = proto.String(utils.GetMd5(mhReq.Device.Imei))
				iqiyiImeiMd5 = utils.GetMd5(mhReq.Device.ImeiMd5)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isDeviceOK = true
				iqiyiReqDevice.ImeiMd5 = proto.String(strings.ToLower(mhReq.Device.ImeiMd5))
				iqiyiImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			} else {
				fmt.Println("get from iqiyi error no android_id req < 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				isDeviceOK = true
				iqiyiReqDevice.Oaid = proto.String(mhReq.Device.Oaid)
			}

			if len(mhReq.Device.Oaid) > 0 {
			} else {
				fmt.Println("get from iqiyi error req > 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}

		if isDeviceOK {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	} else if mhReq.Device.Os == "ios" {
		iqiyiReqDevice.Make = proto.String("apple")
		iqiyiReqDevice.Os = proto.String("iOS")

		isIosDeviceOK := false

		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

				iqiyiIDFA = mhReq.Device.Idfa
			} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
				isIosDeviceOK = true
				isIOSToUpReportIDFA = true

				iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if len(mhReq.Device.CAIDMulti) > 0 {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
								Caid:    proto.String(item.CAID),
								Version: proto.String(item.CAIDVersion),
							}

							iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
							break
						}
					}

					if len(iqiyiDeviceCaidInfo.Caid) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
					}
				} else {
					iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
					for _, item := range mhReq.Device.CAIDMulti {
						iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
							Caid:    proto.String(item.CAID),
							Version: proto.String(item.CAIDVersion),
						}

						iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
					}
					if len(iqiyiDeviceCaidInfo.Caid) > 0 {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
					}
				}
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
				len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
				len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
				len(tmpTimeZone) > 0 {
				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
				iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
				iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
				iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
				iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
				iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
				iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

					iqiyiIDFA = mhReq.Device.Idfa
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
									Caid:    proto.String(item.CAID),
									Version: proto.String(item.CAIDVersion),
								}

								iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
								break
							}
						}

						if len(iqiyiDeviceCaidInfo.Caid) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
						}
					} else {
						iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
						for _, item := range mhReq.Device.CAIDMulti {
							iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
								Caid:    proto.String(item.CAID),
								Version: proto.String(item.CAIDVersion),
							}

							iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
						}
						if len(iqiyiDeviceCaidInfo.Caid) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
					len(tmpTimeZone) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
					iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
					iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
					iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
					iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
					iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
					iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

					iqiyiIDFA = mhReq.Device.Idfa
				} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
					isIosDeviceOK = true
					isIOSToUpReportIDFA = true

					iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if len(mhReq.Device.CAIDMulti) > 0 {
					if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
						iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

						for _, item := range mhReq.Device.CAIDMulti {
							if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
								iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
									Caid:    proto.String(item.CAID),
									Version: proto.String(item.CAIDVersion),
								}

								iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
								break
							}
						}

						if len(iqiyiDeviceCaidInfo.Caid) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
						}
					} else {
						iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
						for _, item := range mhReq.Device.CAIDMulti {
							iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
								Caid:    proto.String(item.CAID),
								Version: proto.String(item.CAIDVersion),
							}

							iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
						}
						if len(iqiyiDeviceCaidInfo.Caid) > 0 {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
						}
					}
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
					len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
					len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
					len(tmpTimeZone) > 0 {
					isIosDeviceOK = true
					isIOSToUpReportYinZi = true

					iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
					iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
					iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
					iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
					iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
					iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
					iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
				}
			}
		}

		if isIosDeviceOK {
		} else {
			fmt.Println("get from iqiyi error req idfa")
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if len(mhReq.Device.BootMark) > 0 {
		iqiyiReqDevice.BootMark = proto.String(mhReq.Device.BootMark)
	}

	if len(mhReq.Device.UpdateMark) > 0 {
		iqiyiReqDevice.UpdateMark = proto.String(mhReq.Device.UpdateMark)
	}

	if mhReq.Network.Carrier == 1 {
		iqiyiReqDevice.Carrier = proto.Int32(1)
		iqiyiReqDevice.CarrierName = proto.String("中国移动")
	} else if mhReq.Network.Carrier == 2 {
		iqiyiReqDevice.CarrierName = proto.String("中国联通")
		iqiyiReqDevice.Carrier = proto.Int32(2)
	} else if mhReq.Network.Carrier == 3 {
		iqiyiReqDevice.CarrierName = proto.String("中国电信")
		iqiyiReqDevice.Carrier = proto.Int32(3)
	} else {
		iqiyiReqDevice.Carrier = proto.Int32(0)
	}

	if mhReq.Network.ConnectType == 0 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_CONNECTION_UNKNOWN.Enum()
	} else if mhReq.Network.ConnectType == 1 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_WIFI.Enum()
	} else if mhReq.Network.ConnectType == 2 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_CELL_2G.Enum()
	} else if mhReq.Network.ConnectType == 3 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_CELL_3G.Enum()
	} else if mhReq.Network.ConnectType == 4 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_CELL_4G.Enum()
	} else if mhReq.Network.ConnectType == 7 {
		iqiyiReqDevice.Connectiontype = iqiyi_up.BidRequest_Device_CELL_5G.Enum()
	}

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if platformPos.PlatformAppIsReportUa == 1 {
		iqiyiReqDevice.Ua = proto.String(destConfigUA)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	iqiyiReqImp := &iqiyi_up.BidRequest_Imp{
		AdzoneId: proto.String(platformPos.PlatformPosID),
		// MediaAdzoneId: proto.String(platformPos.PlatformAppID),
		Bidfloor: proto.Int32(int32(localPosFloorPrice)),
	}
	if platformPos.PlatformPosType == 13 {
		// 贴片
		// 1 – Linear/In-Stream，在视频 流中展现 即线性贴片素材，包 括前贴中贴后贴
		// 2 – Non-Linear/Overlay, 即 视频播放中的悬浮广告(角标)
		// 3 – pause，即视频播放暂停中 的悬浮广告
		// 1:图片
		// 2:视频
		// 3:任意
		// max duration测试15s、30s、60s、90
		// 视频广告开始播放的延时时间
		// > 0:中贴(值代表延时时间)
		// 0:前贴
		// -1:中贴
		// -2:后贴
		iqiyiReqImpVideo := &iqiyi_up.BidRequest_Imp_Video{
			Linearity:             iqiyi_up.BidRequest_Imp_Video_LINEAR.Enum(),
			AcceptedCreativeTypes: proto.Int32(3),
			W:                     proto.Int32(0),
			H:                     proto.Int32(0),
			Minduration:           proto.Int32(5),
			Maxduration:           proto.Int32(30),
			Startdelay:            proto.Int32(0),
			Maxadscount:           proto.Int32(1),
			Format:                iqiyi_up.BidRequest_Imp_VIDEO_MP4.Enum(),
		}

		iqiyiReqImp.Video = iqiyiReqImpVideo
	} else if platformPos.PlatformPosType == 4 {
		if platformPos.PlatformPosMaterialType == 0 {
			// 原生图片
			iqiyiReqImpNative := &iqiyi_up.BidRequest_Imp_Native{
				TitleLen: proto.Int32(30),
			}

			iqiyiReqImpNativeImg := &iqiyi_up.BidRequest_Imp_Native_Image{
				Type: iqiyi_up.BidRequest_Imp_Native_Image_MAIN.Enum(),
				// W:    proto.Int32(1920),
				// Wmin: proto.Int32(50),
				// H:    proto.Int32(1080),
				// Hmin: proto.Int32(50),
			}

			iqiyiReqImpNative.Imgs = append(iqiyiReqImpNative.Imgs, iqiyiReqImpNativeImg)

			iqiyiReqImp.Native = iqiyiReqImpNative

		} else if platformPos.PlatformPosMaterialType == 1 {
			// 原生视频
			iqiyiReqImpNative := &iqiyi_up.BidRequest_Imp_Native{
				TitleLen: proto.Int32(30),
			}

			iqiyiReqImpVideo := &iqiyi_up.BidRequest_Imp_Native_Video{
				W:           proto.Int32(1280),
				H:           proto.Int32(720),
				Minduration: proto.Int32(5),
				Maxduration: proto.Int32(30),
				Format:      iqiyi_up.BidRequest_Imp_VIDEO_MP4.Enum(),
			}
			iqiyiReqImpNative.Video = iqiyiReqImpVideo

			iqiyiReqImp.Native = iqiyiReqImpNative

		} else if platformPos.PlatformPosMaterialType == 2 {
			// 原生图片视频混出
			iqiyiReqImpNative := &iqiyi_up.BidRequest_Imp_Native{
				TitleLen: proto.Int32(30),
			}

			iqiyiReqImpNativeImg := &iqiyi_up.BidRequest_Imp_Native_Image{
				Type: iqiyi_up.BidRequest_Imp_Native_Image_MAIN.Enum(),
				// W:    proto.Int32(1920),
				// Wmin: proto.Int32(50),
				// H:    proto.Int32(1080),
				// Hmin: proto.Int32(50),
			}

			iqiyiReqImpNative.Imgs = append(iqiyiReqImpNative.Imgs, iqiyiReqImpNativeImg)

			iqiyiReqImpVideo := &iqiyi_up.BidRequest_Imp_Native_Video{
				W:           proto.Int32(1280),
				H:           proto.Int32(720),
				Minduration: proto.Int32(5),
				Maxduration: proto.Int32(30),
				Format:      iqiyi_up.BidRequest_Imp_VIDEO_MP4.Enum(),
			}
			iqiyiReqImpNative.Video = iqiyiReqImpVideo

			iqiyiReqImp.Native = iqiyiReqImpNative
		}
	} else if platformPos.PlatformPosType == 2 {
		// 开屏

		// iqiyiReqImpNative := &iqiyi_up.BidRequest_Imp_Native{}

		// iqiyiReqImpNativeImg := &iqiyi_up.BidRequest_Imp_Native_Image{
		// 	Type: iqiyi_up.BidRequest_Imp_Native_Image_MAIN.Enum(),
		// 	W:    proto.Int32(640),
		// 	Wmin: proto.Int32(0),
		// 	H:    proto.Int32(960),
		// 	Hmin: proto.Int32(0),
		// }

		// iqiyiReqImpNative.Imgs = append(iqiyiReqImpNative.Imgs, iqiyiReqImpNativeImg)
		// iqiyiReqImpNative.Maxadscount = proto.Int32(1)
		// iqiyiReqImp.Native = iqiyiReqImpNative

		iqiyiReqImp.AdType = iqiyi_up.BidRequest_Imp_OPENING.Enum()
	} else {
		bigdataExtra.InternalCode = 900009
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	//
	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2
		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				// fmt.Println("kbg_debug iqiyi android replace_key did value: ", localPos.LocalPosID, redisDIDValue)
				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					iqiyiReqDevice.Os = proto.String("Android")
					iqiyiReqDevice.Osv = proto.String(didRedisData.OsVersion)
					iqiyiReqDevice.Model = proto.String(didRedisData.Model)

					manufacturer := strings.ToLower(didRedisData.Manufacturer)
					if _, ok := iqiyiMakeMap[manufacturer]; ok {
						iqiyiReqDevice.Make = proto.String(manufacturer)
					} else {
						iqiyiReqDevice.Make = proto.String("other")
					}

					iqiyiReqDevice.ImeiMd5 = proto.String("")
					iqiyiReqDevice.Oaid = proto.String("")

					if osvMajor < 10 {
						if len(didRedisData.Imei) > 0 {
							iqiyiReqDevice.ImeiMd5 = proto.String(utils.GetMd5(didRedisData.Imei))

							iqiyiImeiMd5 = utils.GetMd5(didRedisData.Imei)
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							iqiyiReqDevice.ImeiMd5 = proto.String(strings.ToLower(didRedisData.ImeiMd5))

							iqiyiImeiMd5 = strings.ToLower(didRedisData.Imei)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							iqiyiReqDevice.Oaid = proto.String(didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
						iqiyiReqDevice.Ua = proto.String(didRedisData.Ua)

						if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}

					isHaveReplace = true
				}
			} else if mhReq.Device.Os == "ios" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}
				if osvMajor > 0 {
					redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)
					// fmt.Println("kbg_debug iqiyi ios replace_key did value: ", localPos.LocalPosID, redisDIDValue)

					if redisErr != nil {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					} else {
						var didRedisData models.ReplaceDIDStu
						json.Unmarshal([]byte(redisDIDValue), &didRedisData)
						json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

						iqiyiReqDevice.Os = proto.String("iOS")
						iqiyiReqDevice.Osv = proto.String(didRedisData.OsVersion)
						iqiyiReqDevice.Model = proto.String(didRedisData.Model)
						iqiyiReqDevice.Make = proto.String("apple")

						iqiyiReqDevice.Idfa = proto.String("")
						iqiyiReqDevice.IdfaMd5 = proto.String("")
						iqiyiReqDevice.CaidInfo = &iqiyi_up.BidRequest_Device_CaidInfo{}
						iqiyiReqDevice.CountryCode = proto.String("")
						iqiyiReqDevice.TimeZoneSec = proto.String("")
						iqiyiReqDevice.DeviceNameMd5 = proto.String("")
						iqiyiReqDevice.DeviceLanguage = proto.String("")
						iqiyiReqDevice.MachineOfDevice = proto.String("")
						iqiyiReqDevice.DiskTotal = proto.Int64(0)
						iqiyiReqDevice.MemTotal = proto.Int64(0)

						isIosReplaceDeviceOK := false
						isIOSToUpReportIDFA = false
						isIOSToUpReportCAID = false
						isIOSToUpReportYinZi = false

						if strings.Contains(iosReportMainParameter, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

								iqiyiIDFA = mhReq.Device.Idfa

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
								iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						}
						if strings.Contains(iosReportMainParameter, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
											Caid:    proto.String(item.CAID),
											Version: proto.String(item.CAIDVersion),
										}

										iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
										break
									}
								}

								if len(iqiyiDeviceCaidInfo.Caid) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
								}
							} else {
								iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
								for _, item := range tmpCAIDMulti {
									iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
										Caid:    proto.String(item.CAID),
										Version: proto.String(item.CAIDVersion),
									}

									iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
								}
								if len(iqiyiDeviceCaidInfo.Caid) > 0 {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
								}
							}
						}
						if strings.Contains(iosReportMainParameter, "yinzi") {
							// tmpDeviceStartSec = didRedisData.DeviceStartSec
							tmpCountry = didRedisData.Country
							tmpLanguage = didRedisData.Language
							tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
							tmpHardwareMachine = didRedisData.HardwareMachine
							// tmpHardwareModel = didRedisData.HardwareModel
							tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
							tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
							// tmpSystemUpdateSec = didRedisData.SystemUpdateSec
							tmpTimeZone = didRedisData.TimeZone

							if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
								len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
								len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
								len(tmpTimeZone) > 0 {

								isIosReplaceDeviceOK = true
								isIOSToUpReportYinZi = true

								iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
								iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
								iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
								iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
								iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
								iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
								iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter1) > 0 {
							if strings.Contains(iosReportSubParameter1, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

									iqiyiIDFA = mhReq.Device.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter1, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
												Caid:    proto.String(item.CAID),
												Version: proto.String(item.CAIDVersion),
											}

											iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
											break
										}
									}

									if len(iqiyiDeviceCaidInfo.Caid) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
									}
								} else {
									iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
									for _, item := range tmpCAIDMulti {
										iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
											Caid:    proto.String(item.CAID),
											Version: proto.String(item.CAIDVersion),
										}

										iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
									}
									if len(iqiyiDeviceCaidInfo.Caid) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
									}
								}
							} else if strings.Contains(iosReportSubParameter1, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								// tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
									iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
									iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
									iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
									iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
									iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
									iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else if len(iosReportSubParameter2) > 0 {
							if strings.Contains(iosReportSubParameter2, "idfa") {
								if len(didRedisData.Idfa) > 0 {
									iqiyiReqDevice.Idfa = proto.String(mhReq.Device.Idfa)

									iqiyiIDFA = mhReq.Device.Idfa

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								} else if len(didRedisData.IdfaMd5) > 0 && didRedisData.IdfaMd5 != utils.GetMd5("") {
									iqiyiReqDevice.IdfaMd5 = proto.String(strings.ToLower(mhReq.Device.IdfaMd5))

									isIosReplaceDeviceOK = true
									isIOSToUpReportIDFA = true
								}
							} else if strings.Contains(iosReportSubParameter2, "caid") {
								var tmpCAIDMulti []models.MHReqCAIDMulti
								json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
								sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

								if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
									iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}

									for _, item := range tmpCAIDMulti {
										if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
											iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
												Caid:    proto.String(item.CAID),
												Version: proto.String(item.CAIDVersion),
											}

											iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
											break
										}
									}

									if len(iqiyiDeviceCaidInfo.Caid) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
									}
								} else {
									iqiyiDeviceCaidInfo := &iqiyi_up.BidRequest_Device_CaidInfo{}
									for _, item := range tmpCAIDMulti {
										iqiyiDeviceCaid := &iqiyi_up.BidRequest_Device_Caid{
											Caid:    proto.String(item.CAID),
											Version: proto.String(item.CAIDVersion),
										}

										iqiyiDeviceCaidInfo.Caid = append(iqiyiDeviceCaidInfo.Caid, iqiyiDeviceCaid)
									}
									if len(iqiyiDeviceCaidInfo.Caid) > 0 {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										iqiyiReqDevice.CaidInfo = iqiyiDeviceCaidInfo
									}
								}
							} else if strings.Contains(iosReportSubParameter2, "yinzi") {
								// tmpDeviceStartSec = didRedisData.DeviceStartSec
								tmpCountry = didRedisData.Country
								tmpLanguage = didRedisData.Language
								tmpDeviceNameMd5 = didRedisData.DeviceNameMd5
								tmpHardwareMachine = didRedisData.HardwareMachine
								// tmpHardwareModel = didRedisData.HardwareModel
								tmpPhysicalMemoryByte = didRedisData.PhysicalMemoryByte
								tmpHarddiskSizeByte = didRedisData.HarddiskSizeByte
								// tmpSystemUpdateSec = didRedisData.SystemUpdateSec
								tmpTimeZone = didRedisData.TimeZone

								if len(tmpCountry) > 0 && len(tmpLanguage) > 0 &&
									len(tmpDeviceNameMd5) > 0 && len(tmpHardwareMachine) > 0 &&
									len(tmpPhysicalMemoryByte) > 0 && len(tmpHarddiskSizeByte) > 0 &&
									len(tmpTimeZone) > 0 {

									isIosReplaceDeviceOK = true
									isIOSToUpReportYinZi = true

									iqiyiReqDevice.CountryCode = proto.String(tmpCountry)
									iqiyiReqDevice.TimeZoneSec = proto.String(tmpTimeZone)
									iqiyiReqDevice.DeviceNameMd5 = proto.String(tmpDeviceNameMd5)
									iqiyiReqDevice.DeviceLanguage = proto.String(tmpLanguage)
									iqiyiReqDevice.MachineOfDevice = proto.String(tmpHardwareMachine)
									iqiyiReqDevice.DiskTotal = proto.Int64(utils.ConvertStringToInt64(tmpHarddiskSizeByte))
									iqiyiReqDevice.MemTotal = proto.Int64(utils.ConvertStringToInt64(tmpPhysicalMemoryByte))
								}
							}
						}

						if isIosReplaceDeviceOK {
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}

						// network -> ua, headers -> ua
						if platformPos.PlatformAppIsReportUa == 1 && platformPos.PlatformAppIsReplaceDIDUa == 1 {
							iqiyiReqDevice.Ua = proto.String(didRedisData.Ua)

							if strings.HasPrefix(didRedisData.Ua, "Mozilla") || strings.HasPrefix(didRedisData.Ua, "Dalvik") {
							} else {
								bigdataExtra.InternalCode = 900011
								bigdataExtra.ExternalCode = 102006

								return MhUpErrorRespMap("", bigdataExtra)
							}
						}

						isHaveReplace = true
					}
				} else {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				}
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()

		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	iqiyiReqPb := &iqiyi_up.BidRequest{
		Id:           proto.String(bigdataUID),
		Timestamp:    proto.Int64(time.Now().UnixNano() / 1e6),
		Resourcetype: proto.Int32(1),
		App:          iqiyiReqApp,
		Device:       iqiyiReqDevice,
		User:         &iqiyiReqUser,
	}
	iqiyiReqPb.Imp = append(iqiyiReqPb.Imp, iqiyiReqImp)
	// iqiyiReqPb.Test = proto.Bool(false)
	// fmt.Println(iqiyiReqPb)
	iqiyiReqPbByte, xxxxxx := proto.Marshal(iqiyiReqPb)
	if xxxxxx != nil {
		panic(xxxxxx)
	}
	// iqiyiReqByte, _ := json.Marshal(iqiyiReqPb)
	// fmt.Println("iqiyi req:", string(iqiyiReqByte))

	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(timeout)*time.Millisecond, http.MethodPost, platformPos.PlatformAppUpURL,
		u.WithHeaders(httpHeader),
		u.WithJSONBody(iqiyiReqPbByte))

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	if err != nil {
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	if statusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	iqiyiResp := &iqiyi_up.BidResponse{}
	err = proto.Unmarshal(bodyContent, iqiyiResp)
	if err != nil {
		fmt.Println(err)
	}

	// debug
	if rand.Intn(5000000) == 0 {
		tmpRespByte, _ := json.Marshal(iqiyiResp)
		go models.BigDataHoloDebugJson2(bigdataUID+"&iqiyi", string(tmpRespByte), localPos.LocalAppID, localPos.LocalPosID, platformPos.PlatformAppID, platformPos.PlatformPosID)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	if len(iqiyiResp.GetBid()) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////

	// iqiyiRespByte, _ := json.Marshal(iqiyiResp)
	// fmt.Println("iqiyi resp:", string(iqiyiRespByte))

	// fmt.Println(iqiyiResp.GetId())
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""

	var respListArray []map[string]interface{}
	for _, bidItem := range iqiyiResp.GetBid() {

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// 随机95-98%替换价格宏
		randPRValue := 100
		if platformPos.PlatformAppReportWinType == 0 {
			randPRValue = 100
		} else if platformPos.PlatformAppReportWinType == 1 {
			tmp1 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMinWeight)
			tmp2 := utils.ConvertStringToInt(platformPos.PlatformAppReportWinMaxWeight)
			if tmp1 <= tmp2 {
				randPRValue = tmp1 + rand.Intn(tmp2-tmp1+1)
			}
		}

		// ecpm
		iqiyiEcpm := int(bidItem.GetPrice())

		respTmpPrice = respTmpPrice + iqiyiEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if iqiyiEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			iqiyiEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > iqiyiEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			bigdataExtra.UpPriceReportFailedNum = bigdataExtra.UpPriceReportFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(iqiyiEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		macroPrice := "${AUCTION_PRICE}"
		// winurl ok
		if iqiyiEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 &&
			len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {

			randPrice := iqiyiEcpm * randPRValue / 100
			macroPrice, _ = encodeIQiYiPrice(int64(randPrice), platformPos.PlatformAppPriceEncrypt, platformPos.PlatformAppPriceEncrypt2)
		}

		// 获取上游竞价成功上报链接
		tmpWinNoticeURLs := getIQiYiPriceWinURL(bidItem.WinNoticeUrl, platformPos, iqiyiEcpm, macroPrice)

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// crid
		respListItemMap["crid"] = bidItem.GetCrid()

		isVideo := false
		if bidItem.GetAdmvideo() != nil {

			isVideo = true
			// 贴片
			admVideo := bidItem.GetAdmvideo()

			if admVideo.GetLink() != nil {
			} else {
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// title
			if len(admVideo.GetTitle()) > 0 {
				respListItemMap["title"] = admVideo.GetTitle()
			} else if len(admVideo.GetAppName()) > 0 {
				respListItemMap["title"] = admVideo.GetAppName()
			}

			// description
			if len(admVideo.GetDesc()) > 0 {
				respListItemMap["description"] = admVideo.GetDesc()
			}
			// ad_url
			respListItemMap["ad_url"] = admVideo.GetLink().GetCurl()

			// app_name
			if len(admVideo.GetAppName()) > 0 {
				respListItemMap["app_name"] = admVideo.GetAppName()
			}

			// app version
			if len(admVideo.GetAppVersion()) > 0 {
				respListItemMap["app_version"] = admVideo.GetAppVersion()
			}

			// package_name
			if len(admVideo.GetPackageName()) > 0 {
				respListItemMap["package_name"] = admVideo.GetPackageName()
			}

			// icon_url
			if len(admVideo.GetAppIcon()) > 0 {
				respListItemMap["icon_url"] = admVideo.GetAppIcon()
			} else {
				respListItemMap["icon_url"] = GetIconURLByDeeplink(admVideo.GetLink().GetDeeplink())
			}

			// req_width, req_height
			respListItemMap["req_width"] = platformPos.PlatformPosWidth
			respListItemMap["req_height"] = platformPos.PlatformPosHeight

			// deep_link
			if len(admVideo.GetLink().GetDeeplink()) > 0 {
				respListItemMap["deep_link"] = admVideo.GetLink().GetDeeplink()
			}

			// crt_type
			respListItemMap["crt_type"] = 20

			// video
			if len(admVideo.GetVideo().GetStartCover()) == 0 {
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
			respListVideoItemMap := map[string]interface{}{}
			respListVideoItemMap["duration"] = admVideo.GetVideo().GetDuration() * 1000
			respListVideoItemMap["video_url"] = admVideo.GetVideo().GetUrl()
			respListVideoItemMap["width"] = admVideo.GetVideo().GetW()
			respListVideoItemMap["height"] = admVideo.GetVideo().GetH()
			respListVideoItemMap["cover_url"] = admVideo.GetVideo().GetStartCover()
			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			respListItemMap["video"] = respListVideoItemMap

			// impression_link array
			var respListItemImpArray []string
			// impression_link maplehaze
			mhImpParams := url.Values{}
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, iqiyiEcpm, tmpEcpm)
			mhImpParams.Add("log", bigdataParams)
			if len(tmpWinNoticeURLs) > 0 {
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}

			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
			// impression_link iqiyi imp url
			for _, impItem := range admVideo.GetLink().GetImptrackers() {

				impItem = strings.Replace(impItem, "${AUCTION_PRICE}", macroPrice, -1)
				respListItemImpArray = append(respListItemImpArray, impItem)
			}

			respListItemMap["impression_link"] = respListItemImpArray

			// click_link array
			var respListItemClkArray []string
			// click_link maplehaze
			mhClkParams := url.Values{}
			mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
			mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			mhClkParams.Add("down_x", "__DOWN_X__")
			mhClkParams.Add("down_y", "__DOWN_Y__")
			mhClkParams.Add("up_x", "__UP_X__")
			mhClkParams.Add("up_y", "__UP_Y__")
			mhClkParams.Add("mh_down_x", "__DOWN_X__")
			mhClkParams.Add("mh_down_y", "__DOWN_Y__")
			mhClkParams.Add("mh_up_x", "__UP_X__")
			mhClkParams.Add("mh_up_y", "__UP_Y__")
			mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
			mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
			mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
			mhClkParams.Add("turnx", "__TURN_X__")
			mhClkParams.Add("turny", "__TURN_Y__")
			mhClkParams.Add("turnz", "__TURN_Z__")
			mhClkParams.Add("turntime", "__TURN_TIME__")

			if platformPos.PlatformPosIsReportSLD == 1 {
				mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
			} else {
				mhClkParams.Add("sld", "__SLD__")
			}
			mhClkParams.Add("log", bigdataParams)

			// click_link iqiyi clk url
			for _, clkItem := range admVideo.GetLink().GetClicktrackers() {
				clkItem = strings.Replace(clkItem, "${AUCTION_PRICE}", macroPrice, -1)

				if IsAdIDReplaceXYByOS(c, mhReq) {
					if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
						clkItem = clkItem + "&is_adid_replace_xy=0"
					}
					if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
						clkItem = clkItem + "&replace_adid_sim_xy_default=1"
						respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
					}
				}

				respListItemClkArray = append(respListItemClkArray, clkItem)
			}

			respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

			respListItemMap["click_link"] = respListItemClkArray

			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				// win notice url
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
				respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}
		} else if bidItem.GetAdmnative() != nil {
			// 原生
			admNative := bidItem.GetAdmnative()

			if admNative.GetLink() != nil {
			} else {
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// title
			if len(admNative.GetTitle()) > 0 {
				respListItemMap["title"] = admNative.GetTitle()
			} else if len(admNative.GetAppName()) > 0 {
				respListItemMap["title"] = admNative.GetAppName()
			}

			// ad_url
			respListItemMap["ad_url"] = admNative.GetLink().GetCurl()

			// app_name
			if len(admNative.GetAppName()) > 0 {
				respListItemMap["app_name"] = admNative.GetAppName()
			}

			// package_name
			if len(admNative.GetPackageName()) > 0 {
				respListItemMap["package_name"] = admNative.GetPackageName()
			}

			// icon_url
			if len(admNative.GetAppIcon()) > 0 {
				respListItemMap["icon_url"] = admNative.GetAppIcon()
			} else {
				respListItemMap["icon_url"] = GetIconURLByDeeplink(admNative.GetLink().GetDeeplink())
			}

			// app version
			if len(admNative.GetAppVersion()) > 0 {
				respListItemMap["app_version"] = admNative.GetAppVersion()
			}

			// req_width, req_height
			respListItemMap["req_width"] = platformPos.PlatformPosWidth
			respListItemMap["req_height"] = platformPos.PlatformPosHeight

			// deep_link
			if len(admNative.GetLink().GetDeeplink()) > 0 {
				respListItemMap["deep_link"] = admNative.GetLink().GetDeeplink()
			}

			// imgs
			var respListItemImageArray []map[string]interface{}
			for _, imgItem := range admNative.GetImgs() {
				respListImageItemMap := map[string]interface{}{}
				respListImageItemMap["url"] = imgItem.GetUrl()

				respListImageItemMap["width"] = imgItem.GetW()
				respListImageItemMap["height"] = imgItem.GetH()
				respListItemImageArray = append(respListItemImageArray, respListImageItemMap)
			}
			if len(respListItemImageArray) > 0 {
				respListItemMap["imgs"] = respListItemImageArray
			}

			// video
			if admNative.GetVideo() != nil {
				isVideo = true
				// if admNative.GetVideo().GetDuration() <= 5 || admNative.GetVideo().GetDuration() > 20 {
				// continue
				// }
				if len(admNative.GetVideo().GetStartCover()) == 0 {
					respTmpInternalCode = 900105
					respTmpRespFailedNum = respTmpRespFailedNum + 1

					continue
				}
				respListVideoItemMap := map[string]interface{}{}
				respListVideoItemMap["duration"] = admNative.GetVideo().GetDuration() * 1000
				respListVideoItemMap["video_url"] = admNative.GetVideo().GetUrl()
				respListVideoItemMap["width"] = admNative.GetVideo().GetW()
				respListVideoItemMap["height"] = admNative.GetVideo().GetH()
				respListVideoItemMap["cover_url"] = admNative.GetVideo().GetStartCover()
				respListVideoItemMap["endcard_type"] = 1
				respListVideoItemMap["endcard_range"] = 1

				respListItemMap["video"] = respListVideoItemMap
			}

			if len(admNative.GetImgs()) == 0 && admNative.GetVideo() == nil {
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			// crt_type
			if admNative.GetVideo() != nil {
				respListItemMap["crt_type"] = 20
			} else if len(admNative.GetImgs()) > 0 {
				respListItemMap["crt_type"] = 11
			}

			// impression_link array
			var respListItemImpArray []string
			// impression_link maplehaze
			mhImpParams := url.Values{}
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, iqiyiEcpm, tmpEcpm)
			mhImpParams.Add("log", bigdataParams)
			if len(tmpWinNoticeURLs) > 0 {
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}

			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
			// impression_link iqiyi imp url
			for _, impItem := range admNative.GetLink().GetImptrackers() {
				impItem = strings.Replace(impItem, "__IP__", mhReq.Device.IP, -1)
				if mhReq.Device.Os == "android" {
					impItem = strings.Replace(impItem, "__OS__", "0", -1)
					impItem = strings.Replace(impItem, "__IMEI__", iqiyiImeiMd5, -1)
					impItem = strings.Replace(impItem, "__ANDROIDID__", iqiyiAndroidID, -1)
				} else if mhReq.Device.Os == "ios" {
					impItem = strings.Replace(impItem, "__OS__", "1", -1)
					impItem = strings.Replace(impItem, "__IDFA__", iqiyiIDFA, -1)
				}
				impItem = strings.Replace(impItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
				impItem = strings.Replace(impItem, "${AUCTION_PRICE}", macroPrice, -1)

				respListItemImpArray = append(respListItemImpArray, impItem)
			}

			respListItemMap["impression_link"] = respListItemImpArray

			// click_link array
			var respListItemClkArray []string
			// click_link maplehaze
			mhClkParams := url.Values{}
			mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
			mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			mhClkParams.Add("down_x", "__DOWN_X__")
			mhClkParams.Add("down_y", "__DOWN_Y__")
			mhClkParams.Add("up_x", "__UP_X__")
			mhClkParams.Add("up_y", "__UP_Y__")
			mhClkParams.Add("mh_down_x", "__DOWN_X__")
			mhClkParams.Add("mh_down_y", "__DOWN_Y__")
			mhClkParams.Add("mh_up_x", "__UP_X__")
			mhClkParams.Add("mh_up_y", "__UP_Y__")
			mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
			mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
			mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
			mhClkParams.Add("turnx", "__TURN_X__")
			mhClkParams.Add("turny", "__TURN_Y__")
			mhClkParams.Add("turnz", "__TURN_Z__")
			mhClkParams.Add("turntime", "__TURN_TIME__")

			if platformPos.PlatformPosIsReportSLD == 1 {
				mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
			} else {
				mhClkParams.Add("sld", "__SLD__")
			}
			mhClkParams.Add("log", bigdataParams)

			// click_link iqiyi clk url
			for _, clkItem := range admNative.GetLink().GetClicktrackers() {
				clkItem = strings.Replace(clkItem, "__IP__", mhReq.Device.IP, -1)
				if mhReq.Device.Os == "android" {
					clkItem = strings.Replace(clkItem, "__OS__", "0", -1)
					clkItem = strings.Replace(clkItem, "__IMEI__", iqiyiImeiMd5, -1)
					clkItem = strings.Replace(clkItem, "__ANDROIDID__", iqiyiAndroidID, -1)
				} else if mhReq.Device.Os == "ios" {
					clkItem = strings.Replace(clkItem, "__OS__", "1", -1)
					clkItem = strings.Replace(clkItem, "__IDFA__", iqiyiIDFA, -1)
				}
				clkItem = strings.Replace(clkItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_PRICE}", macroPrice, -1)

				if IsAdIDReplaceXYByOS(c, mhReq) {
					if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
						clkItem = clkItem + "&is_adid_replace_xy=0"
					}
					if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
						clkItem = clkItem + "&replace_adid_sim_xy_default=1"
						respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
					}
				}

				respListItemClkArray = append(respListItemClkArray, clkItem)
			}
			respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

			respListItemMap["click_link"] = respListItemClkArray

			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				// win notice url
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
				respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}
		} else if platformPos.PlatformPosType == 2 {

			if bidItem.GetCreativeType() == iqiyi_up.BidResponse_Bid_VIDEO {
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
			// title
			if len(bidItem.GetTitle()) > 0 {
				respListItemMap["title"] = bidItem.GetTitle()
			} else if len(bidItem.GetAppName()) > 0 {
				respListItemMap["title"] = bidItem.GetAppName()
			}

			// ad_url
			respListItemMap["ad_url"] = bidItem.GetLink().GetCurl()

			// app_name
			if len(bidItem.GetAppName()) > 0 {
				respListItemMap["app_name"] = bidItem.GetAppName()
			}

			// package_name
			if len(bidItem.GetApkName()) > 0 {
				respListItemMap["package_name"] = bidItem.GetApkName()
			}

			// icon_url
			respListItemMap["icon_url"] = GetIconURLByDeeplink(bidItem.GetLink().GetDeeplink())

			// app version
			if len(bidItem.GetAppVersion()) > 0 {
				respListItemMap["app_version"] = bidItem.GetAppVersion()
			}

			// req_width, req_height
			respListItemMap["req_width"] = platformPos.PlatformPosWidth
			respListItemMap["req_height"] = platformPos.PlatformPosHeight

			// deep_link
			if len(bidItem.GetLink().GetDeeplink()) > 0 {
				respListItemMap["deep_link"] = bidItem.GetLink().GetDeeplink()
			}

			// 新增 下载六要素
			if len(bidItem.GetAppDeveloper()) > 0 {
				respListItemMap["publisher"] = bidItem.GetAppDeveloper()
			}
			if len(bidItem.GetAppPermission()) > 0 {
				respListItemMap["permission"] = bidItem.GetAppPermission()
			}
			if len(bidItem.GetAppPrivacy()) > 0 {
				respListItemMap["privacy_url"] = bidItem.GetAppPrivacy()
			}
			if len(bidItem.GetAppFeature()) > 0 {
				respListItemMap["appinfo"] = bidItem.GetAppFeature()
			}

			// imgs
			var respListItemImageArray []map[string]interface{}
			respListImageItemMap := map[string]interface{}{}
			respListImageItemMap["url"] = bidItem.GetAdUrl()
			respListImageItemMap["width"] = platformPos.PlatformPosWidth
			respListImageItemMap["height"] = platformPos.PlatformPosHeight
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			if len(respListItemImageArray) > 0 {
				respListItemMap["imgs"] = respListItemImageArray
			}

			// crt_type
			respListItemMap["crt_type"] = 11

			// impression_link array
			var respListItemImpArray []string
			// impression_link maplehaze
			mhImpParams := url.Values{}
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, iqiyiEcpm, tmpEcpm)
			mhImpParams.Add("log", bigdataParams)
			if len(tmpWinNoticeURLs) > 0 {
				tmpWinArrayJSON, _ := json.Marshal(tmpWinNoticeURLs)
				tmpEncryptWinArray := utils.AesECBEncrypt([]byte(tmpWinArrayJSON), []byte(config.EncryptKEY))
				mhImpParams.Add("demand_win_links", string(base64.StdEncoding.EncodeToString(tmpEncryptWinArray)))
			}

			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())
			// impression_link iqiyi imp url
			for _, impItem := range bidItem.GetLink().GetImptrackers() {
				impItem = strings.Replace(impItem, "__IP__", mhReq.Device.IP, -1)
				if mhReq.Device.Os == "android" {
					impItem = strings.Replace(impItem, "__OS__", "0", -1)
					impItem = strings.Replace(impItem, "__IMEI__", iqiyiImeiMd5, -1)
					impItem = strings.Replace(impItem, "__ANDROIDID__", iqiyiAndroidID, -1)
				} else if mhReq.Device.Os == "ios" {
					impItem = strings.Replace(impItem, "__OS__", "1", -1)
					impItem = strings.Replace(impItem, "__IDFA__", iqiyiIDFA, -1)
				}
				impItem = strings.Replace(impItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
				impItem = strings.Replace(impItem, "${AUCTION_PRICE}", macroPrice, -1)

				respListItemImpArray = append(respListItemImpArray, impItem)
			}

			respListItemMap["impression_link"] = respListItemImpArray

			// click_link array
			var respListItemClkArray []string
			// click_link maplehaze
			mhClkParams := url.Values{}
			mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
			mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
			mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
			mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
			mhClkParams.Add("down_x", "__DOWN_X__")
			mhClkParams.Add("down_y", "__DOWN_Y__")
			mhClkParams.Add("up_x", "__UP_X__")
			mhClkParams.Add("up_y", "__UP_Y__")
			mhClkParams.Add("mh_down_x", "__DOWN_X__")
			mhClkParams.Add("mh_down_y", "__DOWN_Y__")
			mhClkParams.Add("mh_up_x", "__UP_X__")
			mhClkParams.Add("mh_up_y", "__UP_Y__")
			mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
			mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
			mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
			mhClkParams.Add("turnx", "__TURN_X__")
			mhClkParams.Add("turny", "__TURN_Y__")
			mhClkParams.Add("turnz", "__TURN_Z__")
			mhClkParams.Add("turntime", "__TURN_TIME__")

			if platformPos.PlatformPosIsReportSLD == 1 {
				mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
			} else {
				mhClkParams.Add("sld", "__SLD__")
			}
			mhClkParams.Add("log", bigdataParams)

			// click_link iqiyi clk url
			for _, clkItem := range bidItem.GetLink().GetClicktrackers() {
				clkItem = strings.Replace(clkItem, "__IP__", mhReq.Device.IP, -1)
				if mhReq.Device.Os == "android" {
					clkItem = strings.Replace(clkItem, "__OS__", "0", -1)
					clkItem = strings.Replace(clkItem, "__IMEI__", iqiyiImeiMd5, -1)
					clkItem = strings.Replace(clkItem, "__ANDROIDID__", iqiyiAndroidID, -1)
				} else if mhReq.Device.Os == "ios" {
					clkItem = strings.Replace(clkItem, "__OS__", "1", -1)
					clkItem = strings.Replace(clkItem, "__IDFA__", iqiyiIDFA, -1)
				}
				clkItem = strings.Replace(clkItem, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()), -1)
				clkItem = strings.Replace(clkItem, "${AUCTION_PRICE}", macroPrice, -1)

				if IsAdIDReplaceXYByOS(c, mhReq) {
					if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
						clkItem = clkItem + "&is_adid_replace_xy=0"
					}
					if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
						clkItem = clkItem + "&replace_adid_sim_xy_default=1"
						respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
					}
				}

				respListItemClkArray = append(respListItemClkArray, clkItem)
			}
			respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

			respListItemMap["click_link"] = respListItemClkArray

			if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
				// win notice url
				mhWinNoticeURLParams := url.Values{}
				mhWinNoticeURLParams.Add("log", bigdataParams)
				mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
				mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
				respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

				// loss notice url
				mhLossNoticeURLParams := url.Values{}
				mhLossNoticeURLParams.Add("log", bigdataParams)
				mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
				mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
				mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
				respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
			}
		}

		// interact_type
		tmpDestURL := ""
		if bidItem.GetAdmvideo() != nil {
			tmpDestURL = bidItem.GetAdmvideo().GetLink().GetCurl()
		} else if bidItem.GetAdmnative() != nil {
			tmpDestURL = bidItem.GetAdmnative().GetLink().GetCurl()
		} else if platformPos.PlatformPosType == 2 {
			tmpDestURL = bidItem.GetLink().GetCurl()
		}

		if bidItem.GetAction() == iqiyi_up.AdActionType_OPEN_IN_WEBVIEW {
			respListItemMap["interact_type"] = 0
			respListItemMap["landpage_url"] = tmpDestURL
		} else if bidItem.GetAction() == iqiyi_up.AdActionType_DOWNLOAD_APP {

			respListItemMap["interact_type"] = 1
			respListItemMap["download_url"] = tmpDestURL

		} else {
			respListItemMap["interact_type"] = 0
			respListItemMap["landpage_url"] = tmpDestURL
			respListItemMap["ad_url"] = tmpDestURL
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = iqiyiEcpm

		respListArray = append(respListArray, respListItemMap)
	}

	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = mhReq.Pos.AdCount
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// iqiyi resp
	respIQiYi := models.MHUpResp{}
	respIQiYi.RespData = &mhResp
	respIQiYi.Extra = bigdataExtra
	// respIQiYi.Extra.RespCount = len(respListArray)
	// respIQiYi.Extra.ExternalCode = 0
	// respIQiYi.Extra.InternalCode = 900000

	// if true {
	// 	tmpRespByte, _ := json.Marshal(&respMap)
	// 	go models.BigDataHoloDebugJson(bigdataUID+"_iqiyi", localPos, platformPos, string(tmpRespByte))
	// }

	return &respIQiYi
}

// get price win url
func getIQiYiPriceWinURL(winNoticeURLArray []string, platformPos *models.PlatformPosStu, iqiyiEcpm int, macroPrice string) []string {
	if iqiyiEcpm <= 0 || len(winNoticeURLArray) == 0 {
		return []string{}
	}

	if platformPos.PlatformAppIsReportWin == 0 {
		return []string{}
	}

	var tmpWinNoticeURLs []string
	if len(winNoticeURLArray) > 0 && iqiyiEcpm > 0 && platformPos.PlatformAppIsPriceEncrypt == 1 &&
		len(platformPos.PlatformAppPriceEncrypt) > 0 && len(platformPos.PlatformAppPriceEncrypt2) > 0 {

		for _, winItem := range winNoticeURLArray {
			tmpWinNoticeURL := winItem
			tmpWinNoticeURL = strings.Replace(tmpWinNoticeURL, "${AUCTION_PRICE}", macroPrice, -1)
			tmpWinNoticeURLs = append(tmpWinNoticeURLs, tmpWinNoticeURL)
		}
	}

	return tmpWinNoticeURLs
}

func encodeIQiYiPrice(price int64, encryptionKey string, integrityKey string) (string, error) {
	// var (
	// 	INITIALIZATION_VECTOR_SIZE = 16
	// 	ENCODE_FORMAT              = "US-ASCII"
	// 	SECRET_FORMAT              = "HmacSHA1"
	// )
	INITIALIZATION_VECTOR := "1a2b3c4d5e6f7g8h"
	INITIALIZATION_VECTOR_SIZE := 16

	iv := []byte(INITIALIZATION_VECTOR)[:INITIALIZATION_VECTOR_SIZE]

	var demoEncryptionKey, demoIntegrityKey []byte
	if encryptionKey != "" && integrityKey != "" {
		demoEncryptionKey = []byte(encryptionKey)
		demoIntegrityKey = []byte(integrityKey)
	}

	hmacPad := hmac.New(sha1.New, demoEncryptionKey)
	hmacPad.Write(iv)
	pad := hmacPad.Sum(nil)[:8]

	str := strconv.FormatInt(price, 10)
	priceBytes := make([]byte, 8)
	copy(priceBytes, str)
	// encPrice := make([]byte, 8)
	// for i := 0; i < 8; i++ {
	// 	encPrice[i] = pad[i] ^ priceBytes[i]
	// }
	encPriceSize := len(utils.ConvertInt64ToString(price))
	encPrice := make([]byte, encPriceSize)
	for i := 0; i < encPriceSize; i++ {
		encPrice[i] = pad[i] ^ priceBytes[i]
	}

	hmacSig := hmac.New(sha1.New, demoIntegrityKey)
	// hmacSig.Write(append(priceBytes[:], iv[:]...))
	hmacSig.Write(append([]byte(str), []byte(INITIALIZATION_VECTOR)...))
	sig := hmacSig.Sum(nil)[:4]

	finalMessage := make([]byte, INITIALIZATION_VECTOR_SIZE+len(encPrice)+len(sig))
	copy(finalMessage[:INITIALIZATION_VECTOR_SIZE], iv)
	copy(finalMessage[INITIALIZATION_VECTOR_SIZE:], encPrice)
	copy(finalMessage[INITIALIZATION_VECTOR_SIZE+len(encPrice):], sig)

	r := base64.RawURLEncoding.EncodeToString(finalMessage)

	return r, nil
}

var iqiyiMakeMap = map[string]struct{}{
	"apple":     {},
	"oppo":      {},
	"vivo":      {},
	"huawei":    {},
	"xiaomi":    {},
	"samsung":   {},
	"meizu":     {},
	"motorola":  {},
	"nokia":     {},
	"sony":      {},
	"lge":       {},
	"smartisan": {},
	"oneplus":   {},
	"nexus":     {},
	"honor":     {},
}
