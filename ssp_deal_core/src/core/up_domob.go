package core

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/pb/domob"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// GetFromDomob ...
func GetFromDomob(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from domob")

	// fmt.Println("get from domob, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from domob, p_pos_id:", platformPos.PlatformPosID)

	// test
	if platformPos.PlatformPosIsDebug == 1 {
		platformPos.PlatformAppUpURL = "http://ssp-sandbox-api.dmrtb.com/fl"
		mhReq.Device.ImeiMd5 = "3bf8b91f93c11486bbb6ae68abf960b3"
		mhReq.Device.OsVersion = "9.0"
	}

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// client := &http.Client{}
	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)
	////////////////////////////////////////////////////////////////////////////////////////
	domobReq := &domob.RTBAdsRequest{
		ReqId:         utils.GetCurrentNanoSecond() + rand.Int63n(1000),
		ReqTms:        utils.GetCurrentMilliSecond(),
		HttpsRequired: false,
		IsDeeplink:    true,
	}

	// app
	domobReqApp := &domob.RTBAdsRequest_App{
		AppName:     platformPos.PlatformAppName,
		PackageName: GetAppBundleByConfig(c, mhReq, localPos, platformPos),
	}
	if len(mhReq.Device.AppStoreVersion) > 0 {
		domobReqApp.AppStoreVersion = mhReq.Device.AppStoreVersion
	}
	domobReq.App = domobReqApp

	// device
	domobReqDevice := &domob.RTBAdsRequest_Device{}
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			if platformPos.PlatformPosIsOnlyMore10 == 1 {
				bigdataExtra.InternalCode = 900110
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true

				domobReqDevice.ImeiMd5 = utils.GetMd5(mhReq.Device.Imei)
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true

				domobReqDevice.ImeiMd5 = strings.ToLower(mhReq.Device.ImeiMd5)
			}

			if isImeiOK {
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				domobReqDevice.Oaid = mhReq.Device.Oaid
			} else {
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		if len(mhReq.Device.Idfa) > 0 {
			domobReqDevice.IdfaMd5 = utils.GetMd5(mhReq.Device.Idfa)
		} else if len(mhReq.Device.IdfaMd5) > 0 && mhReq.Device.IdfaMd5 != utils.GetMd5("") {
			domobReqDevice.IdfaMd5 = mhReq.Device.IdfaMd5
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 104013

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	if mhReq.Device.Os == "android" {
		domobReqDevice.Os = 1

		domobReqDevice.Model = mhReq.Device.Model
		domobReqDevice.Brand = mhReq.Device.Manufacturer
	} else if mhReq.Device.Os == "ios" {
		domobReqDevice.Os = 2

		domobReqDevice.Model = mhReq.Device.Model
		domobReqDevice.Brand = "Apple"
	}
	domobReqDevice.DeviceType = 0
	domobReqDevice.Osv = mhReq.Device.OsVersion
	domobReqDevice.UserAgent = destConfigUA
	domobReqDevice.Ip = mhReq.Device.IP
	domobReqDevice.Width = int32(mhReq.Device.ScreenWidth)
	domobReqDevice.Height = int32(mhReq.Device.ScreenHeight)

	if mhReq.Network.ConnectType == 0 {
		// 未知
		domobReqDevice.Network = 0
	} else if mhReq.Network.ConnectType == 1 {
		// wifi
		domobReqDevice.Network = 1
	} else if mhReq.Network.ConnectType == 2 {
		// 2G
		domobReqDevice.Network = 2
	} else if mhReq.Network.ConnectType == 3 {
		// 3G
		domobReqDevice.Network = 3
	} else if mhReq.Network.ConnectType == 4 {
		// 4G
		domobReqDevice.Network = 4
	} else if mhReq.Network.ConnectType == 7 {
		// 5G
		domobReqDevice.Network = 5
	}
	if mhReq.Network.Carrier == 1 {
		domobReqDevice.Operator = 1
	} else if mhReq.Network.Carrier == 2 {
		domobReqDevice.Operator = 2
	} else if mhReq.Network.Carrier == 3 {
		domobReqDevice.Operator = 3
	} else {
		domobReqDevice.Operator = 0
	}
	if len(mhReq.Device.BootMark) > 0 {
		domobReqDevice.BootMark = mhReq.Device.BootMark
	}
	if len(mhReq.Device.UpdateMark) > 0 {
		domobReqDevice.UpdateMark = mhReq.Device.UpdateMark
	}
	domobReq.Device = domobReqDevice

	// imp
	domobReqImp := &domob.RTBAdsRequest_Impression{
		Id:       0,
		AdslotId: platformPos.PlatformPosID,
	}
	// template id
	if platformPos.PlatformPosWidth == 360 && platformPos.PlatformPosHeight == 640 {
		domobReqImp.TemplateId = append(domobReqImp.TemplateId, 20003)
	} else if platformPos.PlatformPosWidth == 640 && platformPos.PlatformPosHeight == 960 {
		domobReqImp.TemplateId = append(domobReqImp.TemplateId, 20004)
	} else if platformPos.PlatformPosWidth == 1280 && platformPos.PlatformPosHeight == 720 {
		if platformPos.PlatformPosMaterialType == 0 {
			domobReqImp.TemplateId = append(domobReqImp.TemplateId, 20005)
		} else if platformPos.PlatformPosMaterialType == 1 {
			domobReqImp.TemplateId = append(domobReqImp.TemplateId, 70001)
		} else {
			domobReqImp.TemplateId = append(domobReqImp.TemplateId, 20005)
		}
	} else if platformPos.PlatformPosWidth == 1080 && platformPos.PlatformPosHeight == 1920 {
		domobReqImp.TemplateId = append(domobReqImp.TemplateId, 60002)
	}
	domobReqImp.BidFloor = int32(localPosFloorPrice)
	domobReq.Imp = append(domobReq.Imp, domobReqImp)

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		var bigdataReplaceDIDStu models.ReplaceDIDStu
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, false, bigdataReplaceDIDStu)
	}()

	// log
	// domobReqTmpByte, _ := json.Marshal(domobReq)
	// domobReqTmpString := string(domobReqTmpByte)
	// fmt.Println("domob req: ", domobReqTmpString)

	// fmt.Println("domob req url: ", platformPos.PlatformAppUpURL)
	////////////////////////////////////////////////////////////////////////////////////////
	domobReqPbByte, _ := proto.Marshal(domobReq)

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "POST", platformPos.PlatformAppUpURL, bytes.NewReader(domobReqPbByte))

	requestGet.Header.Add("Content-Type", "application/x-protobuf;charset=UTF-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	// fmt.Println(requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("domob resp body: " + string(bodyContent))
	if resp.StatusCode == 204 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	domobResp := &domob.RTBAdsResponse{}
	err = proto.Unmarshal(bodyContent, domobResp)
	if err != nil {
		fmt.Println(err)
		bigdataExtra.InternalCode = 900102
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// log
	// domobRespTmpByte, _ := json.Marshal(domobResp)
	// domobRespTmpString := string(domobRespTmpByte)
	// fmt.Println("domob resp: ", domobRespTmpString)

	if domobResp.Status != 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(domobResp.Info) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(domobResp.Info[0].Seat) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	if len(domobResp.Info[0].Seat[0].Ad) == 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	////////////////////////////////////////////////////////////////////////////////////////
	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	domoRespDataItemList := domobResp.Info[0].Seat[0].Ad

	if len(domoRespDataItemList) > 1 {
		sort.Sort(DomobEcpmSort(domoRespDataItemList))
	}

	for _, domobInfoItem := range domoRespDataItemList {
		adInfoItem := domobInfoItem

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		domobEcpm := int(adInfoItem.BidPrice)

		respTmpPrice = respTmpPrice + domobEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if domobEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			domobEcpm = platformPos.PlatformPosEcpm
		}
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > domobEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1
				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(domobEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		encodePriceValue := "{PRICE}"
		if platformPos.PlatformAppIsPriceEncrypt == 1 && len(platformPos.PlatformAppPriceEncrypt) > 0 {
			fmt.Println("domob price encrypt:", platformPos.PlatformAppPriceEncrypt)
			// 随机95-98%替换__PR__
			randPRValue := 95 + rand.Intn(4)
			randPrice := domobEcpm * randPRValue / 100

			tmpPriceValue := utils.AesCBCPKCS5Encrypt(utils.ConvertIntToString(randPrice), platformPos.PlatformAppPriceEncrypt)
			encodePriceValue = url.QueryEscape(base64.StdEncoding.EncodeToString(tmpPriceValue))
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Material.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Material.Title
		} else if len(adInfoItem.AppName) > 0 {
			respListItemMap["title"] = adInfoItem.AppName
		}

		// description
		if len(adInfoItem.Material.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Material.Description
		}

		// crid
		respListItemMap["crid"] = utils.ConvertInt64ToString(adInfoItem.CreativeId)

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		destDeepLink := ""
		if len(adInfoItem.DeeplinkUrl) > 0 || len(adInfoItem.UniversalLink) > 0 {
			if len(adInfoItem.DeeplinkUrl) > 0 {
				destDeepLink = adInfoItem.DeeplinkUrl

				respListItemMap["deep_link"] = adInfoItem.DeeplinkUrl
			} else if len(adInfoItem.UniversalLink) > 0 {
				destDeepLink = adInfoItem.UniversalLink

				respListItemMap["deep_link"] = adInfoItem.UniversalLink
			}

			// deeplink ok track
			var respListItemDeepLinkArray []string

			for _, dpTrackInfoItem := range adInfoItem.DpTrackingUrl {
				tmpDPTrackURL := dpTrackInfoItem
				tmpDPTrackURL = strings.Replace(tmpDPTrackURL, "{DP_RESULT}", "0", -1)
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, tmpDPTrackURL)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, 0, 0, 0, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				for _, dpTrackInfoItem := range adInfoItem.DpTrackingUrl {
					tmpDPTrackURL := dpTrackInfoItem
					tmpDPTrackURL = strings.Replace(tmpDPTrackURL, "{DP_RESULT}", "1", -1)
					tmpDPTrackURL = strings.Replace(tmpDPTrackURL, "{DP_REASON}", "2", -1)
					respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, tmpDPTrackURL)
				}

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}

			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.Material.IconUrl) > 0 {
			respListItemMap["icon_url"] = adInfoItem.Material.IconUrl
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(destDeepLink)
		}

		isVideo := false

		// 视频
		if adInfoItem.CreativeType == domob.RTBAdsResponseInfo_Seat_Ad_Video {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.Material.VideoDuration > 0 {
				respListVideoItemMap["duration"] = adInfoItem.Material.VideoDuration

				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, int(adInfoItem.Material.VideoDuration)/1000)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}
			if adInfoItem.Material.Width > 0 {
				respListVideoItemMap["width"] = adInfoItem.Material.Width
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Material.Height > 0 {
				respListVideoItemMap["height"] = adInfoItem.Material.Height
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Material.Width > 0 && adInfoItem.Material.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.Material.Width), int(adInfoItem.Material.Height))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}
			respListVideoItemMap["video_url"] = adInfoItem.Material.VideoUrl

			// cover_url
			if len(adInfoItem.Material.VideoCover) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.Material.VideoCover
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			var respListEventTrackURLMap []map[string]interface{}

			respListVideoBeginEventTrackMap := map[string]interface{}{}
			respListVideoBeginEventTrackMap["event_type"] = 100
			var respListVideoBeginEventTrackURLMap []string
			for _, videoStartTrackInfoItem := range adInfoItem.EventTrack {
				if videoStartTrackInfoItem.Type == 1 {
					for _, videoStartURLItem := range videoStartTrackInfoItem.Url {
						respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, videoStartURLItem)
					}
				}
			}
			respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			if len(respListVideoBeginEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			}

			respListVideoEndEventTrackMap := map[string]interface{}{}
			respListVideoEndEventTrackMap["event_type"] = 103
			var respListVideoEndEventTrackURLMap []string
			for _, videoEndTrackInfoItem := range adInfoItem.EventTrack {
				if videoEndTrackInfoItem.Type == 3 {
					for _, videoEndURLItem := range videoEndTrackInfoItem.Url {
						respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, videoEndURLItem)
					}
				}
			}
			respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			if len(respListVideoEndEventTrackURLMap) > 0 {
				respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			}

			if len(respListEventTrackURLMap) > 0 {
				respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			}

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else if adInfoItem.CreativeType == domob.RTBAdsResponseInfo_Seat_Ad_Image {
			isVideo = false

			// imgs
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.Material.ImgUrls) > 0 {
				respListImageItemMap["url"] = adInfoItem.Material.ImgUrls[0]
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			if adInfoItem.Material.Width > 0 {
				respListImageItemMap["width"] = adInfoItem.Material.Width
			} else {
				respListImageItemMap["width"] = platformPos.PlatformPosWidth
			}
			if adInfoItem.Material.Height > 0 {
				respListImageItemMap["height"] = adInfoItem.Material.Height
			} else {
				respListImageItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Material.Width > 0 && adInfoItem.Material.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, int(adInfoItem.Material.Width), int(adInfoItem.Material.Height))
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		} else {
			continue
		}

		// interact_type ad_url
		if len(adInfoItem.DownloadUrl) > 0 {
			// respListItemMap["interact_type"] = 1
			// respListItemMap["ad_url"] = adInfoItem.DownloadURL
			// respListItemMap["landpage_url"] = adInfoItem.DownloadURL
			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.DownloadUrl
			respListItemMap["download_url"] = adInfoItem.DownloadUrl

		} else if len(adInfoItem.LandingUrl) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.LandingUrl
			respListItemMap["landpage_url"] = adInfoItem.LandingUrl
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		if len(adInfoItem.PackageName) > 0 {
			respListItemMap["package_name"] = adInfoItem.PackageName
		}

		if len(adInfoItem.AppName) > 0 {
			respListItemMap["app_name"] = adInfoItem.AppName
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, domobEcpm, tmpEcpm)
		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, trackInfoItem := range adInfoItem.ImpressionTrackingUrl {
			impItem := trackInfoItem
			impItem = strings.Replace(impItem, "{PRICE}", encodePriceValue, -1)

			respListItemImpArray = append(respListItemImpArray, impItem)
		}

		for _, winItem := range adInfoItem.WinNoticeUrl {
			tmpWinItem := winItem
			tmpWinItem = strings.Replace(tmpWinItem, "{PRICE}", encodePriceValue, -1)
			respListItemImpArray = append(respListItemImpArray, tmpWinItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, trackInfoItem := range adInfoItem.ClickTrackingUrl {
			clkItem := trackInfoItem
			clkItem = strings.Replace(clkItem, "{DOWN_X}", "__DOWN_X__", -1)
			clkItem = strings.Replace(clkItem, "{DOWN_Y}", "__DOWN_Y__", -1)
			clkItem = strings.Replace(clkItem, "{UP_X}", "__UP_X__", -1)
			clkItem = strings.Replace(clkItem, "{UP_Y}", "__UP_Y__", -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = domobEcpm

		respListArray = append(respListArray, respListItemMap)
	}
	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// domob resp
	respDomob := models.MHUpResp{}
	respDomob.RespData = &mhResp
	respDomob.Extra = bigdataExtra
	// respKuaiShou.Extra.RespCount = len(respListArray)
	// respKuaiShou.Extra.ExternalCode = 0
	// respKuaiShou.Extra.InternalCode = 900000

	return &respDomob
}

type DomobEcpmSort []*domob.RTBAdsResponseInfo_Seat_Ad

func (s DomobEcpmSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s DomobEcpmSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s DomobEcpmSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].BidPrice > s[j].BidPrice
}
