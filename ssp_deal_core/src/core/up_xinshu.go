package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core/up_common"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// GetFromXinShu ...
func GetFromXinShu(c context.Context, mhReq *models.MHReq, localPos *models.LocalPosStu, platformPos *models.PlatformPosStu, categoryInfo models.CategoryStu, bigdataUID string) *models.MHUpResp {
	// fmt.Println("get from xinshu")
	// test 开屏: 1000100001, 信息流: 1000100002, 视频信息流 1000100004
	// platformPos.PlatformAppUpURL = "http://tserver.kejet.net/r/m/b"
	// platformPos.PlatformPosID = "1000100001"
	// fmt.Println("get from xinshu, p_app_id:", platformPos.PlatformAppID)
	// fmt.Println("get from xinshu, p_pos_id:", platformPos.PlatformPosID)

	// timeout
	timeout := localPos.LocalPosTimeOut

	// bigdata extra params
	bigdataExtra := models.MHUpRespExtra{}
	bigdataExtra.InternalCode = 900000
	bigdataExtra.ExternalCode = 0
	bigdataExtra.UpReqTime = 0
	bigdataExtra.UpReqNum = 0
	bigdataExtra.UpRespTime = 0
	bigdataExtra.UpRespNum = 0
	bigdataExtra.UpRespOKNum = 0

	// 是否AllInOnLimit OK
	isAllInOneLimitOK, isAllInOneLimitOKErrCode := IsAllInOneLimitOK(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
	if isAllInOneLimitOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// resp 内部临时code
	respTmpInternalCode := 900000

	// client := &http.Client{}

	// 根据配置获取ua
	destConfigUA := GetDestConfigUA(c, mhReq, localPos, platformPos)

	// 过滤ua
	isFilterUA, isFilterUAErrCode := IsFilterUA(c, mhReq, localPos, platformPos, destConfigUA)
	if isFilterUA {
		bigdataExtra.InternalCode = isFilterUAErrCode
		bigdataExtra.ExternalCode = 104022

		return MhUpErrorRespMap("", bigdataExtra)
	}

	localPosFloorPrice, localPosFinalPrice, localPosProfitRate := GetPriceConfig(c, mhReq, localPos, platformPos, &categoryInfo)

	// ios是否上报
	isIOSToUpReportIDFA := false
	isIOSToUpReportCAID := false
	isIOSToUpReportYinZi := false

	////////////////////////////////////////////////////////////////////////////////////////
	// 判定是否上报ios因子
	tmpDeviceStartSec := mhReq.Device.DeviceStartSec
	// tmpCountry := mhReq.Device.Country
	// tmpLanguage := mhReq.Device.Language
	// tmpDeviceNameMd5 := mhReq.Device.DeviceNameMd5
	// tmpHardwareMachine := mhReq.Device.HardwareMachine
	// tmpHardwareModel := mhReq.Device.HardwareModel
	// tmpPhysicalMemoryByte := mhReq.Device.PhysicalMemoryByte
	// tmpHarddiskSizeByte := mhReq.Device.HarddiskSizeByte
	tmpSystemUpdateSec := mhReq.Device.SystemUpdateSec
	// tmpTimeZone := mhReq.Device.TimeZone
	// tmpCPUNum := mhReq.Device.CPUNum
	// tmpCAID := mhReq.Device.CAID
	// tmpCAIDVersion := mhReq.Device.CAIDVersion
	tmpDeviceBirthSec := mhReq.Device.DeviceBirthSec

	// ios上报主参数, 附属参数1, 附属参数2
	iosReportMainParameter := platformPos.PlatformAppIOSReportMainParameter
	iosReportSubParameter1 := platformPos.PlatformAppIOSReportSubParameter1
	iosReportSubParameter2 := platformPos.PlatformAppIOSReportSubParameter2
	////////////////////////////////////////////////////////////////////////////////////////

	// send req
	requestGet, _ := http.NewRequestWithContext(c, "GET", platformPos.PlatformAppUpURL, nil)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)

	q := requestGet.URL.Query()
	q.Add("pid", platformPos.PlatformPosID)
	q.Add("apitype", "native")
	q.Add("isapp", "Y")
	q.Add("ip", mhReq.Device.IP)
	q.Add("brand", mhReq.Device.Manufacturer)
	q.Add("model", mhReq.Device.Model)
	q.Add("osv", mhReq.Device.OsVersion)
	q.Add("bidfloor", utils.ConvertIntToString(localPosFloorPrice))
	if mhReq.Device.Os == "android" {
		q.Add("os", "ANDROID")
	} else if mhReq.Device.Os == "ios" {
		q.Add("os", "IOS")
	}
	q.Add("appid", GetAppBundleByConfig(c, mhReq, localPos, platformPos))

	// 原始请求ios参数是否ok
	isIosDeviceOK := false
	if mhReq.Device.Os == "android" {
		osvMajor := 0
		if len(mhReq.Device.OsVersion) > 0 {
			osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
			osvMajor = utils.ConvertStringToInt(osvMajorStr)
			// fmt.Println(osvMajor)
		}
		if osvMajor < 10 {
			isImeiOK := false

			if len(mhReq.Device.Imei) > 0 {
				isImeiOK = true

				q.Add("imeimd5", utils.GetMd5(mhReq.Device.Imei))
			} else if len(mhReq.Device.ImeiMd5) > 0 && mhReq.Device.ImeiMd5 != utils.GetMd5("") {
				isImeiOK = true

				q.Add("imeimd5", mhReq.Device.ImeiMd5)
			}

			if isImeiOK {
			} else {
				fmt.Println("get from xinshu error req < 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			if len(mhReq.Device.Oaid) > 0 {
				q.Add("oaid", mhReq.Device.Oaid)
			} else {
				fmt.Println("get from xinshu error req > 10")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 104013

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	} else if mhReq.Device.Os == "ios" {
		if strings.Contains(iosReportMainParameter, "idfa") {
			if len(mhReq.Device.Idfa) > 0 {
				q.Add("idfa", mhReq.Device.Idfa)

				isIosDeviceOK = true
				isIOSToUpReportIDFA = true
			}
		}
		if strings.Contains(iosReportMainParameter, "caid") {
			if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
				for _, item := range mhReq.Device.CAIDMulti {
					if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
						isIosDeviceOK = true
						isIOSToUpReportCAID = true

						q.Add("current_caid", item.CAID)
						q.Add("current_caid_ver", item.CAIDVersion)
						break
					}
				}
			} else {
				// TODO
				// for _, item := range mhReq.Device.CAIDMulti {
				// 	isIosDeviceOK = true
				// 	isIOSToUpReportCAID = true

				// 	q.Add("current_caid", item.CAID)
				// 	q.Add("current_caid_ver", item.CAIDVersion)
				// 	break
				// }
			}
		}
		if strings.Contains(iosReportMainParameter, "yinzi") {
			if len(mhReq.Device.DeviceBirthSec) > 0 {
				q.Add("birth_time", mhReq.Device.DeviceBirthSec)
			}

			if len(mhReq.Device.SystemUpdateSec) > 0 {
				q.Add("update_mark_p", mhReq.Device.SystemUpdateSec)
			}
		}
		if strings.Contains(iosReportMainParameter, "paid") {
			if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
				len(tmpDeviceBirthSec) > 0 {

				isIosDeviceOK = true
				isIOSToUpReportYinZi = true

				q.Add("paid", utils.GetMd5(tmpDeviceBirthSec)+"-"+utils.GetMd5(tmpSystemUpdateSec)+"-"+utils.GetMd5(tmpDeviceStartSec))
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter1) > 0 {
			if strings.Contains(iosReportSubParameter1, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					q.Add("idfa", mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter1, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							q.Add("current_caid", item.CAID)
							q.Add("current_caid_ver", item.CAIDVersion)
							break
						}
					}
				} else {
					// TODO
					// for _, item := range mhReq.Device.CAIDMulti {
					// 	isIosDeviceOK = true
					// 	isIOSToUpReportCAID = true

					// 	q.Add("current_caid", item.CAID)
					// 	q.Add("current_caid_ver", item.CAIDVersion)
					// 	break
					// }
				}
			} else if strings.Contains(iosReportSubParameter1, "yinzi") {
				if len(mhReq.Device.DeviceBirthSec) > 0 {
					q.Add("birth_time", mhReq.Device.DeviceBirthSec)
				}

				if len(mhReq.Device.SystemUpdateSec) > 0 {
					q.Add("update_mark_p", mhReq.Device.SystemUpdateSec)
				}
			}
		}

		if isIosDeviceOK {
		} else if len(iosReportSubParameter2) > 0 {
			if strings.Contains(iosReportSubParameter2, "idfa") {
				if len(mhReq.Device.Idfa) > 0 {
					q.Add("idfa", mhReq.Device.Idfa)

					isIosDeviceOK = true
					isIOSToUpReportIDFA = true
				}
			} else if strings.Contains(iosReportSubParameter2, "caid") {
				if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
					for _, item := range mhReq.Device.CAIDMulti {
						if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
							isIosDeviceOK = true
							isIOSToUpReportCAID = true

							q.Add("current_caid", item.CAID)
							q.Add("current_caid_ver", item.CAIDVersion)
							break
						}
					}
				} else {
					// TODO
					// for _, item := range mhReq.Device.CAIDMulti {
					// 	isIosDeviceOK = true
					// 	isIOSToUpReportCAID = true

					// 	q.Add("current_caid", item.CAID)
					// 	q.Add("current_caid_ver", item.CAIDVersion)
					// 	break
					// }
				}
			} else if strings.Contains(iosReportSubParameter2, "yinzi") {
				if len(mhReq.Device.DeviceBirthSec) > 0 {
					q.Add("birth_time", mhReq.Device.DeviceBirthSec)
				}

				if len(mhReq.Device.SystemUpdateSec) > 0 {
					q.Add("update_mark_p", mhReq.Device.SystemUpdateSec)
				}
			}
		}

		// 如果替换包开也走替换包判定
		if isIosDeviceOK || platformPos.PlatformAppIsReplaceDID == 1 {
		} else {
			bigdataExtra.InternalCode = 900101
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}
	}

	var bigdataReplaceDIDData models.ReplaceDIDStu
	isHaveReplace := false

	replaceUA := ""

	// 获取替换did flag
	replaceDIDFlag := GetReplaceDIDFlag(c, mhReq, localPos, platformPos)
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag != 0 {
		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<2

		if len(platformPos.PlatformAppMagicReplaceDIDLibMd5Key) > 0 {
			if mhReq.Device.Os == "android" {
				osvMajor := 0
				if len(mhReq.Device.OsVersion) > 0 {
					osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
					osvMajor = utils.ConvertStringToInt(osvMajorStr)
					// fmt.Println(osvMajor)
				}

				redisDIDValue, redisErr := GetReplaceDIDAndroidFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag, "")

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					q.Del("imeimd5")
					q.Del("oaid")

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}
					if osvMajor < 10 {

						if len(didRedisData.Imei) > 0 {
							q.Add("imeimd5", utils.GetMd5(didRedisData.Imei))
						} else if len(didRedisData.ImeiMd5) > 0 && didRedisData.ImeiMd5 != utils.GetMd5("") {
							q.Add("imeimd5", didRedisData.ImeiMd5)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					} else {
						if len(didRedisData.Oaid) > 0 {
							q.Add("oaid", didRedisData.Oaid)
						} else {
							bigdataExtra.InternalCode = 900011
							bigdataExtra.ExternalCode = 102006

							return MhUpErrorRespMap("", bigdataExtra)
						}
					}
				}
				isHaveReplace = true

			} else if mhReq.Device.Os == "ios" {
				// osv model key
				redisDIDValue, redisErr := GetReplaceDIDIOSFromRedis(c, mhReq, localPos, platformPos, replaceDIDFlag)

				if redisErr != nil {
					bigdataExtra.InternalCode = 900011
					bigdataExtra.ExternalCode = 102006

					return MhUpErrorRespMap("", bigdataExtra)
				} else {
					var didRedisData models.ReplaceDIDStu
					json.Unmarshal([]byte(redisDIDValue), &didRedisData)
					json.Unmarshal([]byte(redisDIDValue), &bigdataReplaceDIDData)

					// 替换请求ios参数是否ok
					isIosReplaceDeviceOK := false
					isIOSToUpReportIDFA = false
					isIOSToUpReportCAID = false
					// isIOSToUpReportYinZi = false

					q.Del("idfa")
					q.Del("current_caid")
					q.Del("current_caid_ver")
					q.Del("birth_time")
					q.Del("update_mark_p")
					q.Del("paid")

					if strings.Contains(iosReportMainParameter, "idfa") {
						if len(didRedisData.Idfa) > 0 {
							q.Add("idfa", didRedisData.Idfa)

							isIosReplaceDeviceOK = true
							isIOSToUpReportIDFA = true
						}
					}
					if strings.Contains(iosReportMainParameter, "caid") {
						var tmpCAIDMulti []models.MHReqCAIDMulti
						json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
						sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

						if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
							for _, item := range tmpCAIDMulti {
								if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
									isIosReplaceDeviceOK = true
									isIOSToUpReportCAID = true

									q.Add("current_caid", item.CAID)
									q.Add("current_caid_ver", item.CAIDVersion)
									break
								}
							}
						} else {
							// TODO
							// for _, item := range tmpCAIDMulti {
							// 	isIosReplaceDeviceOK = true
							// 	isIOSToUpReportCAID = true

							// 	q.Add("current_caid", item.CAID)
							// 	q.Add("current_caid_ver", item.CAIDVersion)
							// 	break
							// }
						}
					}
					if strings.Contains(iosReportMainParameter, "yinzi") {
						if len(didRedisData.DeviceBirthSec) > 0 {
							isIosReplaceDeviceOK = true
							q.Add("birth_time", didRedisData.DeviceBirthSec)
						}
						if len(didRedisData.SystemUpdateSec) > 0 {
							isIosReplaceDeviceOK = true
							q.Add("update_mark_p", didRedisData.SystemUpdateSec)
						}
					}
					if strings.Contains(iosReportMainParameter, "paid") {
						tmpDeviceStartSec = didRedisData.DeviceStartSec
						tmpSystemUpdateSec = didRedisData.SystemUpdateSec
						tmpDeviceBirthSec = didRedisData.DeviceBirthSec

						if len(tmpDeviceStartSec) > 0 && len(tmpSystemUpdateSec) > 0 &&
							len(tmpDeviceBirthSec) > 0 {
							isIosReplaceDeviceOK = true
							isIOSToUpReportYinZi = true

							q.Add("paid", utils.GetMd5(tmpDeviceBirthSec)+"-"+utils.GetMd5(tmpSystemUpdateSec)+"-"+utils.GetMd5(tmpDeviceStartSec))
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter1) > 0 {
						if strings.Contains(iosReportSubParameter1, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								q.Add("idfa", didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter1, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										q.Add("current_caid", item.CAID)
										q.Add("current_caid_ver", item.CAIDVersion)
										break
									}
								}
							} else {
								// TODO
								// for _, item := range tmpCAIDMulti {
								// 	isIosReplaceDeviceOK = true
								// 	isIOSToUpReportCAID = true

								// 	q.Add("current_caid", item.CAID)
								// 	q.Add("current_caid_ver", item.CAIDVersion)
								// 	break
								// }
							}
						} else if strings.Contains(iosReportSubParameter1, "yinzi") {
							if len(didRedisData.DeviceBirthSec) > 0 {
								isIosReplaceDeviceOK = true
								q.Add("birth_time", didRedisData.DeviceBirthSec)
							}
							if len(didRedisData.SystemUpdateSec) > 0 {
								isIosReplaceDeviceOK = true
								q.Add("update_mark_p", didRedisData.SystemUpdateSec)
							}
						}
					}

					if isIosReplaceDeviceOK {
					} else if len(iosReportSubParameter2) > 0 {
						if strings.Contains(iosReportSubParameter2, "idfa") {
							if len(didRedisData.Idfa) > 0 {
								q.Add("idfa", didRedisData.Idfa)

								isIosReplaceDeviceOK = true
								isIOSToUpReportIDFA = true
							}
						} else if strings.Contains(iosReportSubParameter2, "caid") {
							var tmpCAIDMulti []models.MHReqCAIDMulti
							json.Unmarshal([]byte(didRedisData.CAIDMultiJson), &tmpCAIDMulti)
							sort.Sort(models.CAIDMultiSort(tmpCAIDMulti))

							if platformPos.PlatformAppReportCaidType == 0 && len(platformPos.PlatformAppSingleCaidVersion) > 0 {
								for _, item := range tmpCAIDMulti {
									if utils.ConvertStringToInt(item.CAIDVersion) <= utils.ConvertStringToInt(platformPos.PlatformAppSingleCaidVersion) {
										isIosReplaceDeviceOK = true
										isIOSToUpReportCAID = true

										q.Add("current_caid", item.CAID)
										q.Add("current_caid_ver", item.CAIDVersion)
										break
									}
								}
							} else {
								// TODO
								// for _, item := range tmpCAIDMulti {
								// 	isIosReplaceDeviceOK = true
								// 	isIOSToUpReportCAID = true

								// 	q.Add("current_caid", item.CAID)
								// 	q.Add("current_caid_ver", item.CAIDVersion)
								// 	break
								// }
							}
						} else if strings.Contains(iosReportSubParameter2, "yinzi") {
							if len(didRedisData.DeviceBirthSec) > 0 {
								isIosReplaceDeviceOK = true
								q.Add("birth_time", didRedisData.DeviceBirthSec)
							}
							if len(didRedisData.SystemUpdateSec) > 0 {
								isIosReplaceDeviceOK = true
								q.Add("update_mark_p", didRedisData.SystemUpdateSec)
							}
						}
					}

					if platformPos.PlatformAppIsReplaceDIDUa == 1 {
						destConfigUA = didRedisData.Ua
						replaceUA = didRedisData.Ua
					}
					if isIosReplaceDeviceOK {
					} else {
						bigdataExtra.InternalCode = 900011
						bigdataExtra.ExternalCode = 102006

						return MhUpErrorRespMap("", bigdataExtra)
					}
				}
				isHaveReplace = true
			} else {
				bigdataExtra.InternalCode = 900011
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		} else {
			bigdataExtra.InternalCode = 900011
			bigdataExtra.ExternalCode = 102006

			return MhUpErrorRespMap("", bigdataExtra)
		}

		bigdataExtra.BitCode = bigdataExtra.BitCode + 1<<5
	}

	q.Add("ua", destConfigUA)

	// 验证如果no replace, ios参数
	if platformPos.PlatformAppIsReplaceDID == 1 && replaceDIDFlag == 0 {
		if mhReq.Device.Os == "android" {
		} else if mhReq.Device.Os == "ios" {
			if isIosDeviceOK {
			} else {
				// fmt.Println("get from xinshu error")
				bigdataExtra.InternalCode = 900101
				bigdataExtra.ExternalCode = 102006

				return MhUpErrorRespMap("", bigdataExtra)
			}
		}
	}

	isAllInOneLimitAfterReplaceDIDOK, isAllInOneLimitAfterReplaceDIDOKErrCode := IsAllInOneLimitOKAfterReplaceDID(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra, isHaveReplace, bigdataReplaceDIDData)
	if isAllInOneLimitAfterReplaceDIDOK {
	} else {
		bigdataExtra.InternalCode = isAllInOneLimitAfterReplaceDIDOKErrCode
		bigdataExtra.ExternalCode = 102006

		return MhUpErrorRespMap("", bigdataExtra)
	}

	// bigdata up did
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata up did panic:", err)
			}
		}()
		models.BigDataDIDUPReq(c, mhReq, localPos, platformPos, isHaveReplace, bigdataReplaceDIDData)
	}()

	// requestGet.Header.Add("User-Agent", destUA)

	requestGet.URL.RawQuery = q.Encode()

	// fmt.Println("xinshu req: " + requestGet.URL.String())

	bigdataExtra.UpReqTime = 1
	bigdataExtra.UpReqNum = mhReq.Pos.AdCount
	if isIOSToUpReportIDFA {
		bigdataExtra.UpReportIDFA = 1
	}
	if isIOSToUpReportCAID {
		bigdataExtra.UpReportCAID = 1
	}
	if isIOSToUpReportYinZi {
		bigdataExtra.UpReportYinZi = 1
	}

	client := &http.Client{Timeout: time.Duration(timeout) * time.Millisecond}

	resp, err := client.Do(requestGet)
	if err != nil {
		// fmt.Printf("get request failed, err:[%s]", err.Error())
		if strings.Contains(err.Error(), "Timeout") {
			bigdataExtra.InternalCode = 900106
		} else {
			bigdataExtra.InternalCode = 900102
		}
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}
	defer resp.Body.Close()

	bodyContent, err := io.ReadAll(resp.Body)
	// fmt.Printf("resp status code:[%d]\n", resp.StatusCode)
	// fmt.Printf("resp body data:[%s]\n", string(bodyContent))
	// fmt.Println(resp.StatusCode)
	// fmt.Println("xinshu resp: " + string(bodyContent))
	if resp.StatusCode != 200 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	// fmt.Println("xinshu resp header: ", resp.Header)
	// fmt.Println("xinshu resp header: ", resp.Header.Get("X-Adstate"))
	// fmt.Println("xinshu resp len(content): ", len(bodyContent))
	if len(bodyContent) == 0 {
		bigdataExtra.InternalCode = 900107
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	////////////////////////////////////////////////////////////////////////////////////////
	xinshuRespStu := XinShuRespStu{}
	json.Unmarshal([]byte(bodyContent), &xinshuRespStu)

	if len(xinshuRespStu.Error) > 0 {
		bigdataExtra.InternalCode = 900103
		bigdataExtra.ExternalCode = 102006
		return MhUpErrorRespMap("", bigdataExtra)
	}

	bigdataExtra.UpRespTime = 1

	respTmpPrice := 0
	respTmpRespAllNum := 0
	respTmpRespFailedNum := 0

	respMap := map[string]interface{}{}
	respMap["ret"] = 0
	respMap["msg"] = ""
	////////////////////////////////////////////////////////////////////////////////////////
	var respListArray []map[string]interface{}
	for i := 0; i < 1; i++ {
		adInfoItem := xinshuRespStu

		respTmpRespAllNum = respTmpRespAllNum + 1

		respListItemMap := map[string]interface{}{}

		// ecpm
		xinshuEcpm := adInfoItem.MyPrice

		respTmpPrice = respTmpPrice + xinshuEcpm

		// 上游配置bidding, 同时类型为固定价, 直接修改上游返回ecpm
		if xinshuEcpm <= 0 && platformPos.PlatformPosEcpmType == 1 {
			xinshuEcpm = platformPos.PlatformPosEcpm
		}

		// 随机95-98%替换__PR__
		randPRValue := 95 + rand.Intn(4)
		macroPrice := utils.ConvertIntToString(int(xinshuEcpm * randPRValue / 100))
		// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
		if localPos.LocalPosEcpmType == 0 || localPos.LocalPosEcpmType == 2 {
			if localPosFinalPrice > xinshuEcpm {
				respTmpInternalCode = 900104
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}
		}

		// 填充后限制
		isAllInOneLimitAfterUpRespOK, isAllInOneLimitAfterUpRespOKErrCode := IsAllInOneLimitOKAfterUpResp(c, mhReq, localPos, platformPos, &categoryInfo, &bigdataExtra)
		if isAllInOneLimitAfterUpRespOK {
		} else {
			respTmpInternalCode = isAllInOneLimitAfterUpRespOKErrCode
			respTmpRespFailedNum = respTmpRespFailedNum + 1
			continue
		}

		// 下发ecpm
		// SDK -> api, ecpm_type = 2, "ecpm” = 实时竞价-动态价逻辑一样
		tmpEcpm := 0
		if localPos.LocalPosEcpmType == 0 {
			// 不下发
			tmpEcpm = localPos.LocalPosEcpm
		} else if localPos.LocalPosEcpmType == 1 || (localPos.LocalPosEcpmType == 2 && localPos.LocalAppType == "1") {
			tmpEcpm = int(float32(xinshuEcpm) * (float32(100) - localPosProfitRate) / 100)
			if tmpEcpm < 1 {
				tmpEcpm = 0
			}
			respListItemMap["ecpm"] = tmpEcpm
		} else if localPos.LocalPosEcpmType == 2 {
			tmpEcpm = localPos.LocalPosEcpm
			respListItemMap["ecpm"] = localPos.LocalPosEcpm
		}

		// adid
		maplehazeAdId := uuid.NewV4().String()
		respListItemMap["ad_id"] = maplehazeAdId

		// title
		if len(adInfoItem.Title) > 0 {
			respListItemMap["title"] = adInfoItem.Title
		}

		// description
		if len(adInfoItem.Description) > 0 {
			respListItemMap["description"] = adInfoItem.Description
		}

		// crid
		respListItemMap["crid"] = adInfoItem.CreativeID

		// req_width, req_height
		respListItemMap["req_width"] = platformPos.PlatformPosWidth
		respListItemMap["req_height"] = platformPos.PlatformPosHeight

		// deep link
		if len(adInfoItem.Deeplink) > 0 {
			respListItemMap["deep_link"] = adInfoItem.Deeplink

			// deeplink track
			var respListItemDeepLinkArray []string

			for _, deeplinkTrackItem := range adInfoItem.DeeplinkSuccessTrackers {
				respListItemDeepLinkArray = append(respListItemDeepLinkArray, deeplinkTrackItem)
			}

			mhDPParams := url.Values{}
			mhDPParams.Add("result", "0")
			mhDPParams.Add("reason", "")
			mhDPParams.Add("deeptype", "__DEEP_TYPE__")
			bigdataParams := up_common.EncodeParams(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, xinshuEcpm, tmpEcpm)
			mhDPParams.Add("log", bigdataParams)

			respListItemDeepLinkArray = append(respListItemDeepLinkArray, config.ExternalDPURL+"?"+mhDPParams.Encode())

			var respListItemConvArray []map[string]interface{}

			respListItemConvMap := map[string]interface{}{}
			respListItemConvMap["conv_type"] = 10
			respListItemConvMap["conv_urls"] = respListItemDeepLinkArray

			respListItemConvArray = append(respListItemConvArray, respListItemConvMap)

			// deeplink failed track
			if platformPos.PlatformAppIsDeepLinkFailed == 1 {
				var respListItemDeepLinkFailedArray []string

				mhDPFailedParams := url.Values{}
				mhDPFailedParams.Add("result", "1")
				mhDPFailedParams.Add("reason", "3")
				mhDPFailedParams.Add("log", bigdataParams)

				respListItemDeepLinkFailedArray = append(respListItemDeepLinkFailedArray, config.ExternalDPURL+"?"+mhDPFailedParams.Encode())

				respListItemConv11Map := map[string]interface{}{}
				respListItemConv11Map["conv_type"] = 11
				respListItemConv11Map["conv_urls"] = respListItemDeepLinkFailedArray

				respListItemConvArray = append(respListItemConvArray, respListItemConv11Map)
			}
			respListItemMap["conv_tracks"] = respListItemConvArray
		}

		// icon_url
		if len(adInfoItem.IconURL) > 0 {
			respListItemMap["icon_url"] = adInfoItem.IconURL
		} else {
			respListItemMap["icon_url"] = GetIconURLByDeeplink(adInfoItem.Deeplink)
		}

		isVideo := false

		// 视频
		if strings.Contains(adInfoItem.Mime, "video") {
			isVideo = true

			respListVideoItemMap := map[string]interface{}{}
			if adInfoItem.VideoDuration > 0 {
				respListVideoItemMap["duration"] = adInfoItem.VideoDuration
				// 过滤video_duration
				isMaterialFilter, filterErrCode := IsMaterialFilterByVideoDuration(c, mhReq, localPos, platformPos, adInfoItem.VideoDuration/1000)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			// cover_url
			if len(adInfoItem.VideoCoverURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.VideoCoverURL
			} else if len(adInfoItem.VideoEndImageURL) > 0 {
				respListVideoItemMap["cover_url"] = adInfoItem.VideoEndImageURL
			} else {
				if localPos.LocalPosWidth > localPos.LocalPosHeight {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_h.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-native-cover.jpg"
					}
				} else {
					respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/static/ad_cover_v.jpg"
					if localPos.LocalAppID == "10166" || localPos.LocalAppID == "10167" {
						respListVideoItemMap["cover_url"] = "https://static.maplehaze.cn/adimg/kkmh/kk-reward-cover.jpg"
					}
				}
			}

			respListVideoItemMap["video_url"] = adInfoItem.MaterialURLs[0]
			if adInfoItem.Width > 0 {
				respListVideoItemMap["width"] = adInfoItem.Width
			} else {
				respListVideoItemMap["width"] = platformPos.PlatformPosWidth
			}

			if adInfoItem.Height > 0 {
				respListVideoItemMap["height"] = adInfoItem.Height
			} else {
				respListVideoItemMap["height"] = platformPos.PlatformPosHeight
			}

			if adInfoItem.Width > 0 && adInfoItem.Height > 0 {
				// 过滤素材方向, 大小
				isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Width, adInfoItem.Height)
				if isMaterialFilter {
					respTmpInternalCode = filterErrCode
					respTmpRespFailedNum = respTmpRespFailedNum + 1
					continue
				}
			}

			respListVideoItemMap["endcard_type"] = 1
			if localPos.LocalPosType == 9 {
				respListVideoItemMap["skip_min_time"] = 0
				respListVideoItemMap["endcard_range"] = 0
			}

			// event track 开始播放和播放完成
			// if platformPos.PlatformPosType == 11 {
			// 	var respListEventTrackURLMap []map[string]interface{}

			// 	respListVideoBeginEventTrackMap := map[string]interface{}{}
			// 	respListVideoBeginEventTrackMap["event_type"] = 100
			// 	var respListVideoBeginEventTrackURLMap []string
			// 	for _, monitorItem := range adInfoItem.VideoStartMonitorURLs {
			// 		respListVideoBeginEventTrackURLMap = append(respListVideoBeginEventTrackURLMap, monitorItem)
			// 	}
			// 	respListVideoBeginEventTrackMap["event_track_urls"] = respListVideoBeginEventTrackURLMap
			// 	if len(respListVideoBeginEventTrackURLMap) > 0 {
			// 		respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoBeginEventTrackMap)
			// 	}

			// 	respListVideoEndEventTrackMap := map[string]interface{}{}
			// 	respListVideoEndEventTrackMap["event_type"] = 103
			// 	var respListVideoEndEventTrackURLMap []string
			// 	for _, monitorItem := range adInfoItem.VideoFinishMonitorURLs {
			// 		respListVideoEndEventTrackURLMap = append(respListVideoEndEventTrackURLMap, monitorItem)
			// 	}
			// 	respListVideoEndEventTrackMap["event_track_urls"] = respListVideoEndEventTrackURLMap
			// 	if len(respListVideoEndEventTrackURLMap) > 0 {
			// 		respListEventTrackURLMap = append(respListEventTrackURLMap, respListVideoEndEventTrackMap)
			// 	}

			// 	if len(respListEventTrackURLMap) > 0 {
			// 		respListVideoItemMap["event_tracks"] = respListEventTrackURLMap
			// 	}
			// }

			respListItemMap["video"] = respListVideoItemMap

			// crt_type
			respListItemMap["crt_type"] = 20
		} else {
			isVideo = false

			// imgs
			isHasImage := false
			respListImageItemMap := map[string]interface{}{}
			if len(adInfoItem.MaterialURLs) > 0 {
				isHasImage = true
				respListImageItemMap["url"] = adInfoItem.MaterialURLs[0]
				if adInfoItem.Width > 0 {
					respListImageItemMap["width"] = adInfoItem.Width
				} else {
					respListImageItemMap["width"] = platformPos.PlatformPosWidth
				}
				if adInfoItem.Height > 0 {
					respListImageItemMap["height"] = adInfoItem.Height

				} else {
					respListImageItemMap["height"] = platformPos.PlatformPosHeight
				}

				if adInfoItem.Width > 0 && adInfoItem.Height > 0 {
					// 过滤素材方向, 大小
					isMaterialFilter, filterErrCode := IsMaterialFilterByOrientationAndSize(c, mhReq, localPos, platformPos, adInfoItem.Width, adInfoItem.Height)
					if isMaterialFilter {
						respTmpInternalCode = filterErrCode
						respTmpRespFailedNum = respTmpRespFailedNum + 1
						continue
					}
				}
			}
			if isHasImage {
			} else {
				fmt.Println("wrong image url")
				respTmpInternalCode = 900105
				respTmpRespFailedNum = respTmpRespFailedNum + 1

				continue
			}

			var respListItemImageArray []map[string]interface{}
			respListItemImageArray = append(respListItemImageArray, respListImageItemMap)

			respListItemMap["imgs"] = respListItemImageArray

			// crt_type
			respListItemMap["crt_type"] = 11
		}

		// interact_type ad_url
		if len(adInfoItem.DownloadURL) > 0 {

			respListItemMap["interact_type"] = 1
			respListItemMap["ad_url"] = adInfoItem.DownloadURL
			respListItemMap["download_url"] = adInfoItem.DownloadURL

			// if len(adInfoItem.PackageName) > 0 {
			// 	respListItemMap["package_name"] = adInfoItem.PackageName
			// }

			// if len(adInfoItem.AppName) > 0 {
			// 	respListItemMap["app_name"] = adInfoItem.AppName
			// }
		} else if len(adInfoItem.LandingPageURL) > 0 {
			respListItemMap["interact_type"] = 0
			respListItemMap["ad_url"] = adInfoItem.LandingPageURL
			respListItemMap["landpage_url"] = adInfoItem.LandingPageURL
		} else {
			fmt.Println("wrong interact type")
			respTmpInternalCode = 900105
			respTmpRespFailedNum = respTmpRespFailedNum + 1

			continue
		}

		// impression_link array
		var respListItemImpArray []string
		// impression_link maplehaze
		mhImpParams := url.Values{}
		bigdataParams := up_common.EncodeParamsWithReplaceData(mhReq, localPos, platformPos, bigdataUID, maplehazeAdId, localPosFloorPrice, localPosFinalPrice, xinshuEcpm, isHaveReplace, bigdataReplaceDIDData, false, 0, tmpEcpm)

		mhImpParams.Add("log", bigdataParams)

		// impression_link track url
		for _, monitorItem := range adInfoItem.ImpTrackers {
			impItem := monitorItem
			impItem = strings.Replace(impItem, "%%WM_WIN_PRICE%%", macroPrice, -1)
			respListItemImpArray = append(respListItemImpArray, impItem)
		}
		// impression_link maplehaze
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		respListItemMap["impression_link"] = respListItemImpArray

		// click_link array
		var respListItemClkArray []string
		// click_link maplehaze
		mhClkParams := url.Values{}
		mhClkParams.Add("req_width", utils.ConvertIntToString(platformPos.PlatformPosWidth))
		mhClkParams.Add("req_height", utils.ConvertIntToString(platformPos.PlatformPosHeight))
		mhClkParams.Add("width", utils.ConvertIntToString(localPos.LocalPosWidth))
		mhClkParams.Add("height", utils.ConvertIntToString(localPos.LocalPosHeight))
		mhClkParams.Add("down_x", "__DOWN_X__")
		mhClkParams.Add("down_y", "__DOWN_Y__")
		mhClkParams.Add("up_x", "__UP_X__")
		mhClkParams.Add("up_y", "__UP_Y__")
		mhClkParams.Add("mh_down_x", "__DOWN_X__")
		mhClkParams.Add("mh_down_y", "__DOWN_Y__")
		mhClkParams.Add("mh_up_x", "__UP_X__")
		mhClkParams.Add("mh_up_y", "__UP_Y__")
		mhClkParams.Add("xmaxacc", "__X_MAX_ACC__")
		mhClkParams.Add("ymaxacc", "__Y_MAX_ACC__")
		mhClkParams.Add("zmaxacc", "__Z_MAX_ACC__")
		mhClkParams.Add("turnx", "__TURN_X__")
		mhClkParams.Add("turny", "__TURN_Y__")
		mhClkParams.Add("turnz", "__TURN_Z__")
		mhClkParams.Add("turntime", "__TURN_TIME__")

		if platformPos.PlatformPosIsReportSLD == 1 {
			mhClkParams.Add("sld", utils.ConvertIntToString(platformPos.PlatformPosSLDType))
		} else {
			mhClkParams.Add("sld", "__SLD__")
		}
		mhClkParams.Add("log", bigdataParams)

		// click_link track url
		for _, monitorItem := range adInfoItem.ClkTrackers {
			clkItem := monitorItem
			clkItem = strings.Replace(clkItem, "${__WX_REQ_WIDTH__}", utils.ConvertIntToString(platformPos.PlatformPosWidth), -1)
			clkItem = strings.Replace(clkItem, "${__WX_REQ_HEIGHT__}", utils.ConvertIntToString(platformPos.PlatformPosHeight), -1)
			clkItem = strings.Replace(clkItem, "${__WX_WIDTH__}", utils.ConvertIntToString(localPos.LocalPosWidth), -1)
			clkItem = strings.Replace(clkItem, "${__WX_HEIGHT__}", utils.ConvertIntToString(localPos.LocalPosHeight), -1)
			clkItem = strings.Replace(clkItem, "${__WX_DOWN_X__}", "__DOWN_X__", -1)
			clkItem = strings.Replace(clkItem, "${__WX_DOWN_Y__}", "__DOWN_Y__", -1)
			clkItem = strings.Replace(clkItem, "${__WX_UP_X__}", "__UP_X__", -1)
			clkItem = strings.Replace(clkItem, "${__WX_UP_Y__}", "__UP_Y__", -1)
			clkItem = strings.Replace(clkItem, "${__WX_TS__}", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)

			if IsAdIDReplaceXYByOS(c, mhReq) {
				if platformPos.PlatformPosIsAdIDReplaceXY == 0 {
					clkItem = clkItem + "&is_adid_replace_xy=0"
				}
				if platformPos.PlatformPosIsAdIDReplaceXY == 1 && platformPos.PlatformPosReplaceAdIDSimXYDefault == 1 {
					clkItem = clkItem + "&replace_adid_sim_xy_default=1"
					respListItemMap["replace_adid_sim_xy_default_value"] = platformPos.PlatformPosReplaceAdIDSimXYDefaultValue
				}
			}

			respListItemClkArray = append(respListItemClkArray, clkItem)
		}
		// click_link maplehaze
		respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

		respListItemMap["click_link"] = respListItemClkArray

		if localPos.LocalPosEcpmType == 1 || localPos.LocalPosEcpmType == 2 {
			// win notice url
			mhWinNoticeURLParams := url.Values{}
			mhWinNoticeURLParams.Add("log", bigdataParams)
			mhWinNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhWinNoticeURLParams.Add("sdklogep", "__PPLOGEP__")
			mhWinNoticeURLParams.Add("apilogep", base64.StdEncoding.EncodeToString(utils.AesECBEncrypt([]byte(utils.ConvertIntToString(tmpEcpm)), []byte(config.EncryptKEY))))
			respListItemMap["win_notice_url"] = config.ExternalWinNoticeURL + "?" + mhWinNoticeURLParams.Encode()

			// loss notice url
			mhLossNoticeURLParams := url.Values{}
			mhLossNoticeURLParams.Add("log", bigdataParams)
			mhLossNoticeURLParams.Add("price", "__AUCTION_PRICE__")
			mhLossNoticeURLParams.Add("loss", "__AUCTION_LOSS__")
			mhLossNoticeURLParams.Add("seatid", "__AUCTION_SEAT_ID__")
			respListItemMap["loss_notice_url"] = config.ExternalLossNoticeURL + "?" + mhLossNoticeURLParams.Encode()
		}

		// direction
		respListItemMap["material_direction"] = platformPos.PlatformPosDirection

		// log
		respListItemMap["log"] = EncodeRtbParams(localPos, platformPos, maplehazeAdId)

		// 下发sdk曝光点击ua
		if localPos.LocalAppType == "1" {
			if platformPos.PlatformAppIsReplaceDIDUa == 1 && len(replaceUA) > 0 {
				respListItemMap["ua"] = replaceUA
			}
		}

		// 如果上游配置图片 or 下游配置图片, 过滤视频
		// 如果上游配置视频 or 下游配置视频, 过滤图片
		// 如果上游配置图片+视频, 不过滤
		// 最后返回请求的个数
		if platformPos.PlatformPosMaterialType == 0 || localPos.LocalPosMaterialType == 0 {
			if isVideo {
				continue
			}
		}
		if platformPos.PlatformPosMaterialType == 1 || localPos.LocalPosMaterialType == 1 {
			if isVideo {
			} else {
				continue
			}
		}
		if len(respListArray) >= mhReq.Pos.AdCount {
			continue
		}

		respListItemMap["p_ecpm"] = xinshuEcpm

		respListArray = append(respListArray, respListItemMap)
	}

	if len(respListArray) == 0 {
		bigdataExtra.InternalCode = respTmpInternalCode
		bigdataExtra.ExternalCode = 102006
		bigdataExtra.UpPrice = respTmpPrice
		bigdataExtra.UpRespNum = respTmpRespAllNum
		bigdataExtra.UpRespFailedNum = respTmpRespAllNum
		return MhUpErrorRespMap("", bigdataExtra)
	}
	respListMap := map[string]interface{}{}
	respListMap["list"] = respListArray

	respPosMap := map[string]interface{}{}
	respPosMap[localPos.LocalPosID] = respListMap

	respMap["data"] = respPosMap

	bigdataExtra.FloorPrice = localPosFloorPrice
	bigdataExtra.FinalPrice = localPosFinalPrice
	bigdataExtra.UpPrice = respTmpPrice
	bigdataExtra.InternalCode = respTmpInternalCode
	bigdataExtra.UpRespNum = respTmpRespAllNum
	bigdataExtra.UpRespFailedNum = respTmpRespFailedNum

	tmpMHJsonData, _ := json.Marshal(respMap)
	mhResp := models.MHResp{}
	json.Unmarshal(tmpMHJsonData, &mhResp)

	// xinshu resp
	respXinShu := models.MHUpResp{}
	respXinShu.RespData = &mhResp
	respXinShu.Extra = bigdataExtra
	// respXinShu.Extra.RespCount = len(respListArray)
	// respXinShu.Extra.ExternalCode = 0
	// respXinShu.Extra.InternalCode = 900000

	return &respXinShu
}

// XinShuRespStu ...
type XinShuRespStu struct {
	ImpTrackers             []string `json:"pm"`
	ClkTrackers             []string `json:"cm"`
	DeeplinkSuccessTrackers []string `json:"dpts"`
	MaterialURLs            []string `json:"adurl"`
	CreativeID              string   `json:"creative_id"`
	Description             string   `json:"title"`
	LandingPageURL          string   `json:"landingpage_url"`
	DownloadURL             string   `json:"app_download_url"`
	Title                   string   `json:"body"`
	IconURL                 string   `json:"app_icon"`
	Advertiser              string   `json:"adv"`
	Deeplink                string   `json:"deeplink"`
	VideoDuration           int      `json:"duration"`
	Mime                    string   `json:"mime"`
	Width                   int      `json:"w"`
	Height                  int      `json:"h"`
	MyPrice                 int      `json:"myprice"`
	VideoEndImageURL        string   `json:"efl"`
	VideoCoverURL           string   `json:"cfl"`
	Widths                  []int    `json:"ws"`
	Heights                 []int    `json:"hs"`
	Seat                    string   `json:"seat"`
	Target                  string   `json:"target"`
	Error                   string   `json:"error"`
}
