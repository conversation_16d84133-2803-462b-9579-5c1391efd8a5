package core

import (
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestGetFromXimeng(ct *testing.T) {
	db.InitRedis()
	db.InitBigCache()
	db.InitMysql()

	c := gin.Context{}

	mhReqApp := models.MHReqApp{}
	mhReqDevice := models.MHReqDevice{
		DeviceType: 1,
		IP:         "***********",
		Ua:         "Mozilla/5.0(Linux;Android4.0.4;GT-I9220 Build/IMM76D)",
		OsVersion:  "12.14.0",
		Imei:       "865166020069038",
		Oaid:       "2c06c2722e300206e4c6c79f07c7d2efd7ec74b82daa67e1019b1efd92a2b488",
		Os:         "android",
	}

	mhReq := models.MHReq{
		App:    mhReqApp,
		Device: mhReqDevice,
		Network: models.MHReqNetwork{
			ConnectType: 1,
			Carrier:     1,
		},
	}

	localPosInfo := models.LocalPosStu{
		LocalPosID:                       "1010012",
		LocalAppID:                       "10387",
		LocalPosWidth:                    1280,
		LocalPosHeight:                   720,
		LocalPosType:                     4,
		LocalOs:                          "0",
		LocalAppName:                     "七读免费小说API-Android",
		LocalAppBundleID:                 "com.dj.sevenRead",
		LocalAppType:                     "4",
		LocalAppSplashClickRegion:        0,
		LocalAppNativeDownloadCompliance: 0,
		LocalPosIsActive:                 1,
		LocalPosMHLandPage:               0,
		LocalPosTimeOut:                  260,
		LocalPosMaterialType:             0,
		LocalAppSupportExtraClipboard:    0,
		LocalAppSupportExtraNotification: 0,
		LocalAppSupportExtraWakeUp:       0,
		LocalAppIsExtraLimitFrequency:    1,
		LocalAppExtraLimitFrequency:      "60",
	}

	platformPosInfo := models.PlatformPosStu{
		PlatformPosID:               "33453538148386",
		PlatformAppCorpID:           7,
		PlatformMediaID:             "14",
		PlatformAppBundle:           "com.maplehaze",
		PlatformPosStyleIDs:         "HORIZONTAL_VIDEO_1",
		PlatformOs:                  "0",
		PlatformPosWidth:            400,
		PlatformPosHeight:           694,
		PlatformPosType:             4,
		PlatformPosMaterialType:     2,
		PlatformPosIsDebug:          0,
		PlatformPosIsReplaceXY:      0,
		PlatformPosDirection:        0,
		PlatformAppVersion:          "1.0",
		PlatformAppUpURL:            "http://adse.ximalaya.com/adn/union/bidding/v1.2/FENGLAN",
		PlatformAppFilterUa:         0,
		PlatformAppIsActive:         1,
		PlatformPosIsActive:         1,
		PlatformAppIsReplaceDID:     0,
		PlatformAppIsReplaceDIDUa:   1,
		PlatformAppMaxDAUType:       0,
		PlatformAppMaxDAU:           0,
		PlatformAppIsDeepLinkFailed: 0,
	}

	categoryInfo := models.CategoryStu{
		LocalPosID:              "57650",
		LocalAppID:              "10387",
		PlatformPosID:           "100000000",
		PlatformAppID:           "1001379",
		PlatformMediaID:         "37",
		PlatformPosType:         4,
		PlatformPosMaterialType: 2,
		FloorPrice:              20,
		FinalPrice:              0,
		WhiteList:               "",
		BlackList:               "",
		WhiteVersion:            "",
		BlackVersion:            "",
	}

	bigdataUID := "123456"

	respData := GetFromXimeng(&c, &mhReq, &localPosInfo, &platformPosInfo, &categoryInfo, bigdataUID)

	fmt.Printf("\t\t%#v,\n", respData)
}
