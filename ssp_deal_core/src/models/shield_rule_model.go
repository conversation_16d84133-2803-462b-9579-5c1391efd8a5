package models

// ShieldRuleStu ...
type ShieldRuleStu struct {
	ShieldRuleID   string `json:"ShieldRuleID,omitempty"`
	ShieldKey      string `json:"ShieldKey,omitempty"`
	PackageName    string `json:"PackageName,omitempty"`
	AdURL          string `json:"AdURL,omitempty"`
	MaterialURL    string `json:"MaterialURL,omitempty"`
	ExpUrl         string `json:"exp_url,omitempty"`
	ShieldRuleType int    `json:"ShieldRuleType,omitempty"`
	AdShieldType   int    `json:"AdShieldType,omitempty"`
	TimeList       string `json:"time_list,omitempty"`
	RegionList     string `json:"region_list,omitempty"`
	IsActive       int    `json:"is_active,omitempty"`
}
