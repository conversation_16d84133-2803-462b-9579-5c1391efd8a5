package models

import "mh_proxy/utils"

// MHReqApp ...
type MHReqApp struct {
	AppID       string `json:"app_id"`
	AppBundleID string `json:"app_bundle_id"`
	AppName     string `json:"app_bundle_name"`
	AppType     int    `json:"app_ssp_type"`
}

// MHReqPos ...
type MHReqPos struct {
	ID      int `json:"id"`
	Width   int `json:"width"`
	Height  int `json:"height"`
	AdCount int `json:"ad_count"`
	// cpm floor
	CPMBidFloor int `json:"cpm_bid_floor"`
	// RTB tagid
	TagID string `json:"tagid"`
	// query
	Query string `json:"query"`
	// 包名白名单
	PkgWhitelist []string `json:"pkg_whitelist,omitempty"`
	// 包名黑名单
	PkgBlacklist []string `json:"pkg_blacklist,omitempty"`
}

// MHReqDevice ...
type MHReqDevice struct {
	Os           string  `json:"os"`
	OsVersion    string  `json:"os_version,omitempty"`
	Model        string  `json:"model,omitempty"`
	Manufacturer string  `json:"manufacturer,omitempty"`
	DeviceType   int     `json:"device_type"`
	ScreenWidth  int     `json:"screen_width"`
	ScreenHeight int     `json:"screen_height"`
	DPI          float32 `json:"dpi"`
	XDPI         float32 `json:"xdpi"`
	Orientation  int     `json:"orientation"`
	Imei         string  `json:"imei,omitempty"`
	ImeiMd5      string  `json:"imei_md5,omitempty"`
	AndroidID    string  `json:"android_id,omitempty"`
	AndroidIDMd5 string  `json:"android_id_md5,omitempty"`
	Oaid         string  `json:"oaid,omitempty"`
	OaidMd5      string  `json:"oaid_md5,omitempty"`
	Idfa         string  `json:"idfa,omitempty"`
	IdfaMd5      string  `json:"idfa_md5,omitempty"`
	Ua           string  `json:"ua,omitempty"`
	IP           string  `json:"ip,omitempty"`
	IPCountry    string  `json:"ip_country,omitempty"`
	IPProvince   string  `json:"ip_province,omitempty"`
	IPCity       string  `json:"ip_city,omitempty"`
	// lbs库ip
	LBSIPCountry  string `json:"lbs_ip_country,omitempty"`
	LBSIPProvince string `json:"lbs_ip_province,omitempty"`
	LBSIPCity     string `json:"lbs_ip_city,omitempty"`
	// lbs库geo
	LBSGPSCountry  string `json:"lbs_gps_country,omitempty"`
	LBSGPSProvince string `json:"lbs_gps_province,omitempty"`
	LBSGPSCity     string `json:"lbs_gps_city,omitempty"`
	// mac
	Mac string `json:"mac,omitempty"`
	// appstore version, hms version
	AppStoreVersion string `json:"appstore_version,omitempty"`
	HMSCoreVersion  string `json:"hms_version,omitempty"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec,omitempty"`
	Country            string `json:"country,omitempty"`
	Language           string `json:"language,omitempty"`
	DeviceNameMd5      string `json:"device_name_md5,omitempty"`
	HardwareMachine    string `json:"hardware_machine,omitempty"` // iPhone10,3
	HardwareModel      string `json:"hardware_model,omitempty"`   // D22AP
	PhysicalMemoryByte string `json:"physical_memory_byte,omitempty"`
	HarddiskSizeByte   string `json:"harddisk_size_byte,omitempty"`
	SystemUpdateSec    string `json:"system_update_sec,omitempty"`
	TimeZone           string `json:"time_zone,omitempty"`
	CPUNum             string `json:"cpu_num,omitempty"`
	// 只有接ios媒体接api用, 待ios媒体升级完api caid_multi字段, 删除
	CAID        string `json:"caid,omitempty"`
	CAIDVersion string `json:"caid_version,omitempty"`
	// boot_mark update_mark
	BootMark   string `json:"boot_mark,omitempty"`
	UpdateMark string `json:"update_mark,omitempty"`
	// miui version
	MiuiVersion    string           `json:"miui_version,omitempty"`
	OAIDSource     int              `json:"oaid_source,omitempty"`
	SDFreeSpace    string           `json:"sd_free_space,omitempty"`
	DeviceBirthSec string           `json:"device_birth_sec,omitempty"`
	CAIDMulti      []MHReqCAIDMulti `json:"caid_multi,omitempty"`
	// ios sdk上传spec_multi, 对应caid_multi
	SpecMulti []MHReqSpecMulti `json:"spec_multi,omitempty"`
	// applist
	AppList []int `json:"applist,omitempty"`
	// applist_package_name, 目前水印相机传这个
	AppPackageNameList []string `json:"applist_package_name,omitempty"`

	// 设备did md5 key
	DIDMd5 string `json:"did_md5,omitempty"`

	// 待删除
	ScreenDensityToDeleted float32 `json:"screen_density"`
}

type MHReqCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}

type MHReqSpecMulti struct {
	Spec        string `json:"spec"`
	SpecVersion string `json:"spec_version"`
}

// MHReqNetwork ...
type MHReqNetwork struct {
	ConnectType int `json:"connect_type"`
	Carrier     int `json:"carrier"`
}

// MHReqGeo ...
type MHReqGeo struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type MHReqDebug struct {
	Posid                 string `json:"posid"`
	Step                  string `json:"step"`
	PostType              string `json:"post_type"`
	AdType                string `json:"ad_type"`
	Reqid                 string `json:"reqid"`
	LocalReqidAdid        string `json:"local_reqid_adid"`
	AdId                  string `json:"ad_id"`
	Reason                string `json:"reason"`
	EcpmType              string `json:"ecpm_type"`
	FinalPrice            string `json:"final_price"`
	PosLogEp              string `json:"pos_log_ep"`
	PosLogSp              string `json:"pos_log_sp"`
	ServerAuctionPrice    string `json:"server_auction_price"`
	ClientPrice           string `json:"client_price"`
	OriginalPrice         string `json:"org_price"`
	DeductingProfitsPrice string `json:"deducting_profits_price"`
	Lrl                   string `json:"lrl"`
	Date                  string `json:"date"`
}

type MHReqDebugDid struct {
	Oaid string `json:"oaid"`
}

// MHReq ...
type MHReq struct {
	App     MHReqApp     `json:"media"`
	Pos     MHReqPos     `json:"pos"`
	Device  MHReqDevice  `json:"device"`
	Network MHReqNetwork `json:"network"`
	Geo     MHReqGeo     `json:"geo"`
	// sdk加密设备号, imei, android_id, oaid, harmony_os, harmony_os_version
	DID string `json:"did"`
	// sdk加密geo
	LBS string `json:"lbs"`
	// sdk version
	SDKVersion string `json:"sdk_version"`
	// sdk extra(包括上游p_app_id, p_pos_id, code, resp_num)
	ExtSDK MHReqExtSDK `json:"extsdk,omitempty"`

	Debug  MHReqDebug `json:"debug,omitempty"`
	Sdkalp string     `json:"sdkalp,omitempty"`
}

// MHReqExtSDK ...
type MHReqExtSDK struct {
	ReqID         string `json:"reqid,omitempty"`
	PlatformAppID string `json:"p_app_id,omitempty"`
	PlatformPosID string `json:"p_pos_id,omitempty"`
	Code          int    `json:"code"`
	RespNum       int    `json:"resp_num"`
}

type CAIDMultiSort []MHReqCAIDMulti

func (s CAIDMultiSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CAIDMultiSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CAIDMultiSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].CAIDVersion) > utils.ConvertStringToInt(s[j].CAIDVersion)
}
