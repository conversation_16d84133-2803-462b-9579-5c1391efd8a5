package models

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/db"
	"mh_proxy/device/clk"
	"mh_proxy/device/exp"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	deviceidgenerate "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate"
	deviceidgeneratemodel "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

var bigdataReqArray []BigdataReqStu
var bigdataReqMutex sync.Mutex

var bigdataExpArray []BigdataExpStu
var bigdataExpMutex sync.Mutex

var bigdataClkArray []BigdataClkStu
var bigdataClkMutex sync.Mutex

var bigdataDeepLinkArray []BigdataDeepLinkStu
var bigdataDeepLinkMutex sync.Mutex

var bigdataAuditArray []BigdataAuditStu
var bigdataAuditMutex sync.Mutex
var bigdataApiAuditMutex sync.Mutex

var bigdataClickXYArray []BigdataClickXYStu
var bigdataClickXYMutex sync.Mutex

var bigdataReqTime = utils.GetCurrentSecond()
var bigdataExpTime = utils.GetCurrentSecond()
var bigdataClkTime = utils.GetCurrentSecond()

var bigdataDeepLinkTime = utils.GetCurrentSecond()

var bigdataAuditTime = utils.GetCurrentSecond()
var bigdataClickXYTime = utils.GetCurrentSecond()

// BigDataAdxCoReq ...
// is_win 0 未到上游, 1 并发竞争成功 2 并发竞争失败
func BigDataAdxCoReq(c context.Context, mhReq *MHReq, localPos *LocalPosStu, extra *MHUpRespExtra) {
	// fmt.Println("bigdata co req")

	var bigdataAdxReqItem BigdataReqStu
	bigdataAdxReqItem.UID = extra.BigdataUID
	bigdataAdxReqItem.LocalAppID = mhReq.App.AppID
	bigdataAdxReqItem.LocalPosID = utils.ConvertIntToString(mhReq.Pos.ID)
	bigdataAdxReqItem.PlatformAppID = extra.PlatformAppID
	bigdataAdxReqItem.PlatformPosID = extra.PlatformPosID
	bigdataAdxReqItem.AppName = mhReq.App.AppName
	bigdataAdxReqItem.AppBundle = mhReq.App.AppBundleID
	bigdataAdxReqItem.OS = mhReq.Device.Os
	bigdataAdxReqItem.OSV = mhReq.Device.OsVersion

	bigdataAdxReqItem.DIDMd5 = mhReq.Device.DIDMd5
	bigdataAdxReqItem.Imei = mhReq.Device.Imei
	bigdataAdxReqItem.ImeiMd5 = mhReq.Device.ImeiMd5
	bigdataAdxReqItem.AndroidID = mhReq.Device.AndroidID
	bigdataAdxReqItem.AndroidIDMd5 = mhReq.Device.AndroidIDMd5
	bigdataAdxReqItem.Idfa = mhReq.Device.Idfa
	bigdataAdxReqItem.IdfaMd5 = mhReq.Device.IdfaMd5
	bigdataAdxReqItem.ScreenWidth = mhReq.Device.ScreenWidth
	bigdataAdxReqItem.ScreenHeight = mhReq.Device.ScreenHeight
	bigdataAdxReqItem.IP = mhReq.Device.IP
	bigdataAdxReqItem.UA = mhReq.Device.Ua
	bigdataAdxReqItem.Oaid = mhReq.Device.Oaid
	if len(mhReq.Device.CAIDMulti) > 0 {
		tmpCAIDMulti, _ := json.Marshal(mhReq.Device.CAIDMulti)
		bigdataAdxReqItem.CAIDMulti = string(tmpCAIDMulti)
	}
	bigdataAdxReqItem.Model = mhReq.Device.Model
	bigdataAdxReqItem.Manufacturer = mhReq.Device.Manufacturer
	bigdataAdxReqItem.AppStoreVersion = mhReq.Device.AppStoreVersion
	bigdataAdxReqItem.HMSCoreVersion = mhReq.Device.HMSCoreVersion
	bigdataAdxReqItem.DeviceType = utils.ConvertIntToString(mhReq.Device.DeviceType)
	bigdataAdxReqItem.ConnectType = utils.ConvertIntToString(mhReq.Network.ConnectType)
	bigdataAdxReqItem.Carrier = utils.ConvertIntToString(mhReq.Network.Carrier)
	bigdataAdxReqItem.DownReqNum = mhReq.Pos.AdCount
	bigdataAdxReqItem.DownRespNum = extra.UpRespOKNum
	bigdataAdxReqItem.DownCostTime = int(extra.DownCostTime)
	bigdataAdxReqItem.UpReqTime = extra.UpReqTime
	bigdataAdxReqItem.UpReqNum = extra.UpReqNum
	bigdataAdxReqItem.UpRespTime = extra.UpRespTime
	bigdataAdxReqItem.UpRespNum = extra.UpRespNum
	bigdataAdxReqItem.UpRespOkNum = extra.UpRespOKNum
	bigdataAdxReqItem.UpCostTime = utils.ConvertStringToInt(utils.ConvertInt64ToString(extra.RespCostTime))
	bigdataAdxReqItem.Code = extra.ExternalCode
	bigdataAdxReqItem.InternalCode = extra.InternalCode
	bigdataAdxReqItem.UpRespCode = extra.UpRespCode
	bigdataAdxReqItem.UpPriceReportWinNum = extra.UpPriceReportWinNum
	bigdataAdxReqItem.UpPriceReportFailedNum = extra.UpPriceReportFailedNum
	bigdataAdxReqItem.IsWin = extra.IsWin
	bigdataAdxReqItem.FloorPrice = extra.FloorPrice
	bigdataAdxReqItem.FinalPrice = extra.FinalPrice
	bigdataAdxReqItem.UpPrice = extra.UpPrice
	bigdataAdxReqItem.UpRespFailedNum = extra.UpRespFailedNum
	bigdataAdxReqItem.BitCode = extra.BitCode
	bigdataAdxReqItem.UpReportIDFA = extra.UpReportIDFA
	bigdataAdxReqItem.UpReportCAID = extra.UpReportCAID
	bigdataAdxReqItem.UpReportYinZi = extra.UpReportYinZi

	// DebugHaoKanToBigData2(c, bigdataAdxReqItem)
	// DebugLenovoToBigData2(c, bigdataAdxReqItem)
	// DebugWifiToBigData2(c, bigdataAdxReqItem)
	// DebugSoulToBigData2(c, bigdataAdxReqItem)
	// DebugBilibiliToBigData2(c, bigdataAdxReqItem)
	// DebugBaiduNebulaToBigData2(c, bigdataAdxReqItem)
	// DebugBaiduToBigData2(c, bigdataAdxReqItem)
	// DebugMOOCToBigData2(c, bigdataAdxReqItem)
	// GoBigDataHoloReq(bigdataAdxReqItem)

	bigdataReqMutex.Lock()
	bigdataReqArray = append(bigdataReqArray, bigdataAdxReqItem)

	if len(bigdataReqArray) < config.ClickHouseReqMaxNum && utils.GetCurrentSecond()-bigdataReqTime < config.ClickHouseInterval {
		bigdataReqMutex.Unlock()
		return
	}

	destReqArray := bigdataReqArray[0:]
	bigdataReqArray = bigdataReqArray[0:0]
	bigdataReqMutex.Unlock()

	bigdataReqTime = utils.GetCurrentSecond()
	// fmt.Println("clickhouse req ms 0: ", utils.GetCurrentMilliSecond())

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare("INSERT INTO ssp_rawdata.req_data (uid, app_id, pos_id, p_app_id, p_pos_id, channel, app_name," +
		"app_bundle, os, osv, did_md5, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, caid_multi, ip, ua, oaid, model," +
		"manufacturer, screen_width, screen_height, appstore_version, hms_version, device_type, connect_type, carrier, down_req_num, down_resp_num, down_cost_time," +
		"up_req_time, up_req_num, up_resp_time, up_resp_num, up_resp_ok_num, up_cost_time, code, internal_code, up_resp_code, up_price_report_win_num, up_price_report_failed_num, is_win," +
		"floor_price, final_price, up_price, up_resp_failed_num, bitcode, is_report_idfa, is_report_caid, is_report_ios_element, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	if stmt == nil {
		fmt.Println("bigdata err co_req:", len(destReqArray), err)
		return
	}

	defer stmt.Close()

	for _, item := range destReqArray {
		if _, err := stmt.Exec(
			item.UID,
			item.LocalAppID,
			item.LocalPosID,
			item.PlatformAppID,
			item.PlatformPosID,
			item.Channel,
			item.AppName,
			item.AppBundle,
			item.OS,
			item.OSV,
			item.DIDMd5,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.CAIDMulti,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.ScreenWidth,
			item.ScreenHeight,
			item.AppStoreVersion,
			item.HMSCoreVersion,
			item.DeviceType,
			item.ConnectType,
			item.Carrier,
			item.DownReqNum,
			item.DownRespNum,
			item.DownCostTime,
			item.UpReqTime,
			item.UpReqNum,
			item.UpRespTime,
			item.UpRespNum,
			item.UpRespOkNum,
			item.UpCostTime,
			item.Code,
			item.InternalCode,
			item.UpRespCode,
			item.UpPriceReportWinNum,
			item.UpPriceReportFailedNum,
			item.IsWin,
			item.FloorPrice,
			item.FinalPrice,
			item.UpPrice,
			item.UpRespFailedNum,
			item.BitCode,
			item.UpReportIDFA,
			item.UpReportCAID,
			item.UpReportYinZi,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}

	// 统计请求, redis
	// StatisticsReq(c, destReqArray)

	// fmt.Println("clickhouse req ms 1: ", utils.GetCurrentMilliSecond())
	// fmt.Println("clickhouse req end")
}

// GoBigDataHoloReq ...
func GoBigDataHoloReq(bigdataAdxReqItem BigdataReqStu) {
	if utilities.SkipHologress {
		return
	}

	// if true {
	//  return
	// }

	// if utilities.WriteHoloReqDataRatio == 0 {
	//  return
	// }

	// if rand.Intn(100) < utilities.WriteHoloReqDataRatio {
	//  return
	// }

	tableName := "req_data"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxReqItem.UID, len(bigdataAdxReqItem.UID))
	put.SetTextValByColName("app_id", bigdataAdxReqItem.LocalAppID, len(bigdataAdxReqItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataAdxReqItem.LocalPosID, len(bigdataAdxReqItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxReqItem.PlatformAppID, len(bigdataAdxReqItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataAdxReqItem.PlatformPosID, len(bigdataAdxReqItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxReqItem.Channel, len(bigdataAdxReqItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxReqItem.AppName, len(bigdataAdxReqItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxReqItem.AppBundle, len(bigdataAdxReqItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxReqItem.OS, len(bigdataAdxReqItem.OS))
	put.SetTextValByColName("osv", bigdataAdxReqItem.OSV, len(bigdataAdxReqItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxReqItem.DIDMd5, len(bigdataAdxReqItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxReqItem.Imei, len(bigdataAdxReqItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxReqItem.ImeiMd5, len(bigdataAdxReqItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxReqItem.AndroidID, len(bigdataAdxReqItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxReqItem.AndroidIDMd5, len(bigdataAdxReqItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxReqItem.Idfa, len(bigdataAdxReqItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxReqItem.IdfaMd5, len(bigdataAdxReqItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxReqItem.IP, len(bigdataAdxReqItem.IP))
	put.SetTextValByColName("ua", bigdataAdxReqItem.UA, len(bigdataAdxReqItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxReqItem.Oaid, len(bigdataAdxReqItem.Oaid))
	put.SetTextValByColName("model", bigdataAdxReqItem.Model, len(bigdataAdxReqItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxReqItem.Manufacturer, len(bigdataAdxReqItem.Manufacturer))
	put.SetInt32ValByColName("screen_width", int32(bigdataAdxReqItem.ScreenWidth))
	put.SetInt32ValByColName("screen_height", int32(bigdataAdxReqItem.ScreenHeight))
	put.SetTextValByColName("appstore_version", bigdataAdxReqItem.AppStoreVersion, len(bigdataAdxReqItem.AppStoreVersion))
	put.SetTextValByColName("hms_version", bigdataAdxReqItem.HMSCoreVersion, len(bigdataAdxReqItem.HMSCoreVersion))
	put.SetTextValByColName("device_type", bigdataAdxReqItem.DeviceType, len(bigdataAdxReqItem.DeviceType))
	put.SetTextValByColName("connect_type", bigdataAdxReqItem.ConnectType, len(bigdataAdxReqItem.ConnectType))
	put.SetTextValByColName("carrier", bigdataAdxReqItem.Carrier, len(bigdataAdxReqItem.Carrier))
	put.SetInt32ValByColName("down_req_num", int32(bigdataAdxReqItem.DownReqNum))
	put.SetInt32ValByColName("down_resp_num", int32(bigdataAdxReqItem.DownRespNum))
	put.SetInt32ValByColName("down_cost_time", int32(bigdataAdxReqItem.DownCostTime))
	put.SetInt32ValByColName("up_req_time", int32(bigdataAdxReqItem.UpReqTime))
	put.SetInt32ValByColName("up_req_num", int32(bigdataAdxReqItem.UpReqNum))
	put.SetInt32ValByColName("up_resp_time", int32(bigdataAdxReqItem.UpRespTime))
	put.SetInt32ValByColName("up_resp_num", int32(bigdataAdxReqItem.UpRespNum))
	put.SetInt32ValByColName("up_resp_ok_num", int32(bigdataAdxReqItem.UpRespOkNum))
	put.SetInt32ValByColName("up_cost_time", int32(bigdataAdxReqItem.UpCostTime))
	put.SetInt32ValByColName("code", int32(bigdataAdxReqItem.Code))
	put.SetInt32ValByColName("internal_code", int32(bigdataAdxReqItem.InternalCode))
	put.SetInt32ValByColName("up_resp_code", int32(bigdataAdxReqItem.UpRespCode))
	put.SetInt32ValByColName("up_price_report_win_num", int32(bigdataAdxReqItem.UpPriceReportWinNum))
	put.SetInt32ValByColName("up_price_report_failed_num", int32(bigdataAdxReqItem.UpPriceReportFailedNum))
	put.SetInt32ValByColName("is_win", int32(bigdataAdxReqItem.IsWin))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxReqItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxReqItem.FinalPrice))
	put.SetInt32ValByColName("up_price", int32(bigdataAdxReqItem.UpPrice))
	put.SetInt32ValByColName("up_resp_failed_num", int32(bigdataAdxReqItem.UpRespFailedNum))
	put.SetInt64ValByColName("bitcode", int64(bigdataAdxReqItem.BitCode))
	put.SetInt32ValByColName("is_report_idfa", int32(bigdataAdxReqItem.UpReportIDFA))
	put.SetInt32ValByColName("is_report_caid", int32(bigdataAdxReqItem.UpReportCAID))
	put.SetInt32ValByColName("is_report_ios_element", int32(bigdataAdxReqItem.UpReportYinZi))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataClickHouseExp ...
func BigDataClickHouseExp(c *gin.Context) {
	// fmt.Println("bigdata exp")

	logQuery := c.Query("log")
	// logQuery = strings.Replace(logQuery, " ", "+", -1)

	if len(logQuery) == 0 {
		fmt.Println("bigdata exp nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// sdk p_app_id
	pAppID := c.Query("p_app_id")
	// sdk3.3使用宏 pappid=__P_APP_ID__
	if len(c.Query("pappid")) > 0 && c.Query("pappid") != "__P_APP_ID__" {
		pAppID = c.Query("pappid")
	}
	if len(log.Get("p_app_id")) > 0 {
		pAppID = log.Get("p_app_id")
	}

	// sdk p_pos_id
	pPosID := c.Query("p_pos_id")
	// sdk3.3使用宏 pposid=__P_POS_ID__
	if len(c.Query("pposid")) > 0 && c.Query("pposid") != "__P_POS_ID__" {
		pPosID = c.Query("pposid")
	}
	if len(log.Get("p_pos_id")) > 0 {
		pPosID = log.Get("p_pos_id")
	}

	// sdk floor_price
	floorPrice := utils.ConvertStringToInt(c.Query("floor_price"))
	if floorPrice == 0 {
		floorPrice = utils.ConvertStringToInt(log.Get("floor_price"))
	}
	// sdk final_price
	finalPrice := utils.ConvertStringToInt(c.Query("final_price"))
	if finalPrice == 0 {
		finalPrice = utils.ConvertStringToInt(log.Get("final_price"))
	}
	if len(c.Query("ppfnp")) > 0 && c.Query("ppfnp") != "__PPFNP__" {
		sdkTmpDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("ppfnp"))
		sdkTmpDecrypt := utils.AesECBDecrypt(sdkTmpDecryptBase64, []byte(config.EncryptKEY))
		finalPrice = utils.ConvertStringToInt(string(sdkTmpDecrypt))
	}
	// sdk up_price
	ecpm := utils.ConvertStringToInt(c.Query("ecpm"))
	if ecpm == 0 {
		ecpm = utils.ConvertStringToInt(log.Get("ecpm"))
	}
	if len(c.Query("sdklogep")) > 0 && c.Query("sdklogep") != "__DPPLOGEP__" {
		sdkEcpmDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("sdklogep"))
		sdkEcpmDecrypt := utils.AesECBDecrypt(sdkEcpmDecryptBase64, []byte(config.EncryptKEY))
		ecpm = utils.ConvertStringToInt(string(sdkEcpmDecrypt))
	}

	// sdk channel
	channel := log.Get("channel")
	if len(channel) == 0 {
		if pAppID == "sop" {
			channel = "99"
		} else {
			channel = GetPlatformIDByPlatformInfo(c, pPosID, pAppID)
		}
	}

	// supply ecpm
	supplyEcpm := utils.ConvertStringToInt(log.Get("supply_ecpm"))
	if len(c.Query("sdkprice")) > 0 && c.Query("sdkprice") != "__AUCTION_PRICE__" {
		sdkPriceDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("sdkprice"))
		sdkPriceDecrypt := utils.AesECBDecrypt(sdkPriceDecryptBase64, []byte(config.EncryptKEY))
		supplyEcpm = utils.ConvertStringToInt(string(sdkPriceDecrypt))
	}

	var bigdataAdxExpItem BigdataExpStu
	bigdataAdxExpItem.UID = log.Get("uid")
	bigdataAdxExpItem.AdID = log.Get("adid")
	bigdataAdxExpItem.LocalAppID = log.Get("app_id")
	bigdataAdxExpItem.LocalPosID = log.Get("pos_id")
	bigdataAdxExpItem.LocalAppType = log.Get("app_type")
	bigdataAdxExpItem.PlatformAppID = pAppID
	bigdataAdxExpItem.PlatformPosID = pPosID
	bigdataAdxExpItem.PlatformAppType = log.Get("p_app_type")
	bigdataAdxExpItem.Channel = channel
	bigdataAdxExpItem.AppName = log.Get("app_name")
	bigdataAdxExpItem.AppBundle = log.Get("app_bundle")
	bigdataAdxExpItem.OS = log.Get("os")
	bigdataAdxExpItem.OSV = log.Get("osv")
	if len(log.Get("did_md5")) > 0 {
		bigdataAdxExpItem.DIDMd5 = log.Get("did_md5")
	}
	bigdataAdxExpItem.Imei = log.Get("imei")
	bigdataAdxExpItem.ImeiMd5 = log.Get("imei_md5")
	bigdataAdxExpItem.AndroidID = log.Get("android_id")
	bigdataAdxExpItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxExpItem.Idfa = log.Get("idfa")
	bigdataAdxExpItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataAdxExpItem.CAIDMulti = log.Get("caid_multi")
	bigdataAdxExpItem.IP = c.ClientIP()
	bigdataAdxExpItem.UA = c.GetHeader("User-Agent")
	bigdataAdxExpItem.Oaid = log.Get("oaid")
	bigdataAdxExpItem.OaidMd5 = log.Get("oaid_md5")
	bigdataAdxExpItem.Model = log.Get("model")
	bigdataAdxExpItem.Manufacturer = log.Get("manufacturer")
	bigdataAdxExpItem.FloorPrice = floorPrice
	bigdataAdxExpItem.FinalPrice = finalPrice
	bigdataAdxExpItem.Ecpm = ecpm
	// ecpm_type = 0, 存supply_ecpm, 计算媒体消耗
	localPosInfo := GetLocalPosInfo(c, bigdataAdxExpItem.LocalPosID, bigdataAdxExpItem.LocalAppID)
	if localPosInfo != nil &&
		localPosInfo.LocalPosEcpmType == 0 &&
		localPosInfo.LocalPosEcpm > 0 {
		supplyEcpm = localPosInfo.LocalPosEcpm
	}
	bigdataAdxExpItem.SupplyEcpm = supplyEcpm
	bigdataAdxExpItem.ExpTime = utils.GetCurrentMilliSecond()
	bigdataAdxExpItem.SDKVersion = log.Get("sdk_version")
	bigdataAdxExpItem.MGDealID = c.Query("mg_deal_id")
	bigdataAdxExpItem.ConnectType = utils.ConvertStringToInt(log.Get("connect_type"))
	bigdataAdxExpItem.Carrier = utils.ConvertStringToInt(log.Get("carrier"))
	bigdataAdxExpItem.DealTime = utils.ConvertStringToInt64(log.Get("deal_time"))
	bigdataAdxExpItem.SupplyCRID = c.Query("supply_crid")
	bigdataAdxExpItem.DemandCRID = c.Query("demand_crid")

	if bigdataAdxExpItem.OS == "android" {
		// Mozilla/5.0 (Linux; Android 12; GIA-AN00 Build/HONORGIA-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.105 Mobile Safari/537.36
		// 12
		// GIA-AN00
		tmpOSV := bigdataAdxExpItem.OSV
		tmpModel := bigdataAdxExpItem.Model
		tmpUA := bigdataAdxExpItem.UA
		if strings.Contains(tmpUA, "Android "+tmpOSV) {
			bigdataAdxExpItem.WrongOSVInUA = 0
		} else {
			bigdataAdxExpItem.WrongOSVInUA = 1
		}

		if strings.Contains(strings.ToLower(tmpUA), strings.ToLower(tmpModel)) {
			bigdataAdxExpItem.WrongModelInUA = 0

		} else {
			bigdataAdxExpItem.WrongModelInUA = 1
		}
	} else if bigdataAdxExpItem.OS == "ios" {
		// Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
		// 16.4.1
		// iPhone15,3
		tmpOSV := bigdataAdxExpItem.OSV
		if strings.Count(tmpOSV, ".") >= 2 && strings.HasSuffix(tmpOSV, ".0") {
			tmpOSV = tmpOSV[:len(tmpOSV)-2]
		}

		tmpUA := bigdataAdxExpItem.UA
		tmpUA = strings.Replace(tmpUA, "_", ".", -1)
		if strings.Contains(tmpUA, tmpOSV) {
			bigdataAdxExpItem.WrongOSVInUA = 0
		} else {
			bigdataAdxExpItem.WrongOSVInUA = 1
		}

		bigdataAdxExpItem.WrongModelInUA = 0
	}

	// if bigdataAdxExpItem.PlatformAppID == "sop" {
	// } else {
	// 	if strings.HasPrefix(bigdataAdxExpItem.UA, "Mozilla") || strings.HasPrefix(bigdataAdxExpItem.UA, "Dalvik") {
	// 	} else {
	// 		// wrong ua 曝光点击直接拉黑1个月
	// 		if len(log.Get("origin_did_md5")) > 0 {
	// 			originDIDMd5 := log.Get("origin_did_md5")
	// 			if len(originDIDMd5) > 0 {
	// 				db.GlbRedis.Set(c, "black_didmd5_new_"+originDIDMd5, 1, time.Duration(30*24)*time.Hour).Err()
	// 			}
	// 		}
	// 	}
	// }

	//if len(bigdataAdxExpItem.MGDealID) > 0 {
	//	now := time.Now()
	//	nowTimestamp := now.Unix()
	//	tomorrow := now.Add(time.Hour * 24)
	//	tomorrow = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, time.Local)
	//	tomorrowTimestamp := tomorrow.Unix()
	//	dayLongTime := tomorrowTimestamp - nowTimestamp
	//	durationTime := time.Duration(dayLongTime)
	//
	//	redisKey := fmt.Sprintf(rediskeys.ADX_SSP_EXTRA_EXP_TOTAL_KEY, utils.GetMd5(bigdataAdxExpItem.MGDealID))
	//	db.GlbRedis.Incr(c, redisKey)
	//	_, _ = db.GlbRedis.Expire(c, redisKey, durationTime*time.Second).Result()
	//}

	demandWinLinks := c.Query("demand_win_links")
	if len(demandWinLinks) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("bigdata exp panic:", err)
				}
			}()

			decryptBase64, _ := base64.StdEncoding.DecodeString(demandWinLinks)
			tmpDemandWinLinks := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))

			var tmpWinArray []string
			json.Unmarshal([]byte(tmpDemandWinLinks), &tmpWinArray)
			for _, tmpLink := range tmpWinArray {

				// http请求
				client := &http.Client{Timeout: 1000 * time.Millisecond}
				requestGet, _ := http.NewRequest("GET", tmpLink, nil)

				requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

				resp, err := client.Do(requestGet)
				if err != nil {
					fmt.Println("get request failed, err:", err.Error())
					return
				}
				defer resp.Body.Close()

				// if bigdataAdxExpItem.PlatformAppID == "516400015" || bigdataAdxExpItem.PlatformAppID == "516400016" {
				// 	go BigDataHoloDebugJson2(bigdataAdxExpItem.UID+"&demand_win_url", string(tmpLink),
				// 		bigdataAdxExpItem.LocalAppID, bigdataAdxExpItem.LocalPosID, bigdataAdxExpItem.PlatformAppID, bigdataAdxExpItem.PlatformPosID)
				// }
				// go BigDataHoloDebugJson2(bigdataAdxExpItem.UID+"&demand_win_url", string(tmpLink),
				// 	bigdataAdxExpItem.LocalAppID, bigdataAdxExpItem.LocalPosID, bigdataAdxExpItem.PlatformAppID, bigdataAdxExpItem.PlatformPosID)
			}
		}()
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata exp panic:", err)
			}
		}()

		GoBigDataHoloExp(bigdataAdxExpItem)

		GoBigDataClickHouseExp(bigdataAdxExpItem)

		// StatisticsExp(c, bigdataAdxExpItem.LocalAppID, bigdataAdxExpItem.LocalPosID, bigdataAdxExpItem.PlatformAppID, bigdataAdxExpItem.PlatformPosID)

		// ecpm_type = 0, 存win_price_data, 计算媒体消耗
		localPosInfo := GetLocalPosInfo(c, bigdataAdxExpItem.LocalPosID, bigdataAdxExpItem.LocalAppID)
		if localPosInfo != nil &&
			localPosInfo.LocalPosEcpmType == 0 &&
			localPosInfo.LocalPosEcpm > 0 {
			var bigdataPriceItem BigdataPriceStu
			bigdataPriceItem.UID = bigdataAdxExpItem.UID
			bigdataPriceItem.TagID = ""
			bigdataPriceItem.LocalAppID = bigdataAdxExpItem.LocalAppID
			bigdataPriceItem.LocalPosID = bigdataAdxExpItem.LocalPosID
			bigdataPriceItem.PlatformAppID = bigdataAdxExpItem.PlatformAppID
			bigdataPriceItem.PlatformPosID = bigdataAdxExpItem.PlatformPosID
			bigdataPriceItem.BidPrice = localPosInfo.LocalPosEcpm
			bigdataPriceItem.WinPrice = localPosInfo.LocalPosEcpm
			bigdataPriceItem.AdID = bigdataAdxExpItem.AdID
			bigdataPriceItem.MGDealID = bigdataAdxExpItem.MGDealID

			BigDataWinPriceToHolo(c, &bigdataPriceItem)
		}

		// TODO: huwenzhi exp
		go exp.DidDemandDidExp(c, bigdataAdxExpItem.DIDMd5, bigdataAdxExpItem.IP, bigdataAdxExpItem.PlatformAppID)
		go exp.DidDemandDidLastTimeExp(c, bigdataAdxExpItem.DIDMd5, bigdataAdxExpItem.IP, bigdataAdxExpItem.PlatformAppID)
		go exp.DidDemandIpExp(c, bigdataAdxExpItem.DIDMd5, bigdataAdxExpItem.IP, bigdataAdxExpItem.PlatformAppID)

		if rand.Intn(100) < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugPAppId) > 0 {
			pAppIdArr := strings.Split(utilities.DebugPAppId, ",")
			fmt.Println("test:", pAppIdArr)
			for _, pAppId := range pAppIdArr {
				if pAppId == bigdataAdxExpItem.PlatformAppID {
					GoBigDataExpHolo(&bigdataAdxExpItem)
					break
				}
			}
		}
	}()
}
func GoBigDataExpHolo(bigdataAdxExpItem *BigdataExpStu) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "demand_replace_exp_data"))
	uuidStr := uuid.NewV4().String()
	put.SetTextValByColName("id", uuidStr, len(uuidStr))
	put.SetTextValByColName("reqid", bigdataAdxExpItem.UID, len(bigdataAdxExpItem.UID))
	put.SetTextValByColName("app_id", bigdataAdxExpItem.LocalAppID, len(bigdataAdxExpItem.LocalAppID))
	put.SetInt32ValByColName("app_type", int32(utils.ConvertStringToInt(bigdataAdxExpItem.LocalAppType)))
	put.SetTextValByColName("pos_id", bigdataAdxExpItem.LocalPosID, len(bigdataAdxExpItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxExpItem.PlatformAppID, len(bigdataAdxExpItem.PlatformAppID))
	put.SetInt32ValByColName("p_app_type", int32(utils.ConvertStringToInt(bigdataAdxExpItem.PlatformAppType)))
	put.SetTextValByColName("p_pos_id", bigdataAdxExpItem.PlatformPosID, len(bigdataAdxExpItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxExpItem.Channel, len(bigdataAdxExpItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxExpItem.AppName, len(bigdataAdxExpItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxExpItem.AppBundle, len(bigdataAdxExpItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxExpItem.OS, len(bigdataAdxExpItem.OS))
	put.SetTextValByColName("osv", bigdataAdxExpItem.OSV, len(bigdataAdxExpItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxExpItem.DIDMd5, len(bigdataAdxExpItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxExpItem.Imei, len(bigdataAdxExpItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxExpItem.ImeiMd5, len(bigdataAdxExpItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxExpItem.AndroidID, len(bigdataAdxExpItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxExpItem.AndroidIDMd5, len(bigdataAdxExpItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxExpItem.Idfa, len(bigdataAdxExpItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxExpItem.IdfaMd5, len(bigdataAdxExpItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxExpItem.IP, len(bigdataAdxExpItem.IP))
	put.SetTextValByColName("ua", bigdataAdxExpItem.UA, len(bigdataAdxExpItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxExpItem.Oaid, len(bigdataAdxExpItem.Oaid))
	put.SetTextValByColName("model", bigdataAdxExpItem.Model, len(bigdataAdxExpItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxExpItem.Manufacturer, len(bigdataAdxExpItem.Manufacturer))
	put.SetInt32ValByColName("wrong_osv_in_ua", int32(bigdataAdxExpItem.WrongOSVInUA))
	put.SetInt32ValByColName("wrong_model_in_ua", int32(bigdataAdxExpItem.WrongModelInUA))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxExpItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxExpItem.FinalPrice))
	put.SetInt32ValByColName("ecpm", int32(bigdataAdxExpItem.Ecpm))
	put.SetInt64ValByColName("exp_time", bigdataAdxExpItem.ExpTime)

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// GoBigDataClickHouseExp ...
func GoBigDataClickHouseExp(bigdataAdxExpItem BigdataExpStu) {

	bigdataExpMutex.Lock()
	bigdataExpArray = append(bigdataExpArray, bigdataAdxExpItem)

	if len(bigdataExpArray) < config.ClickHouseExpMaxNum && utils.GetCurrentSecond()-bigdataExpTime < config.ClickHouseInterval {
		bigdataExpMutex.Unlock()
		return
	}

	destExpArray := bigdataExpArray[0:]
	bigdataExpArray = bigdataExpArray[0:0]
	bigdataExpMutex.Unlock()

	bigdataExpTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare("INSERT INTO ssp_rawdata.exp_data (uid, app_id, app_type, pos_id, p_app_id, p_app_type, p_pos_id, channel, app_name," +
		"app_bundle, os, osv, did_md5, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, model," +
		"manufacturer, wrong_osv_in_ua, wrong_model_in_ua, floor_price, final_price, ecpm, exp_time, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	if stmt == nil {
		fmt.Println("bigdata err exp:", len(destExpArray), err)
		return
	}

	defer stmt.Close()

	for _, item := range destExpArray {
		if _, err := stmt.Exec(
			item.UID,
			item.LocalAppID,
			utils.ConvertStringToInt(item.LocalAppType),
			item.LocalPosID,
			item.PlatformAppID,
			utils.ConvertStringToInt(item.PlatformAppType),
			item.PlatformPosID,
			item.Channel,
			item.AppName,
			item.AppBundle,
			item.OS,
			item.OSV,
			item.DIDMd5,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.WrongOSVInUA,
			item.WrongModelInUA,
			item.FloorPrice,
			item.FinalPrice,
			item.Ecpm,
			item.ExpTime,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}
	// fmt.Println("clickhouse exp end")
}

// GoBigDataHoloExp ...
func GoBigDataHoloExp(bigdataAdxExpItem BigdataExpStu) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", "exp_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxExpItem.UID, len(bigdataAdxExpItem.UID))
	put.SetTextValByColName("adid", bigdataAdxExpItem.AdID, len(bigdataAdxExpItem.AdID))
	put.SetTextValByColName("app_id", bigdataAdxExpItem.LocalAppID, len(bigdataAdxExpItem.LocalAppID))
	put.SetInt32ValByColName("app_type", int32(utils.ConvertStringToInt(bigdataAdxExpItem.LocalAppType)))
	put.SetTextValByColName("pos_id", bigdataAdxExpItem.LocalPosID, len(bigdataAdxExpItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxExpItem.PlatformAppID, len(bigdataAdxExpItem.PlatformAppID))
	put.SetInt32ValByColName("p_app_type", int32(utils.ConvertStringToInt(bigdataAdxExpItem.PlatformAppType)))
	put.SetTextValByColName("p_pos_id", bigdataAdxExpItem.PlatformPosID, len(bigdataAdxExpItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxExpItem.Channel, len(bigdataAdxExpItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxExpItem.AppName, len(bigdataAdxExpItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxExpItem.AppBundle, len(bigdataAdxExpItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxExpItem.OS, len(bigdataAdxExpItem.OS))
	put.SetTextValByColName("osv", bigdataAdxExpItem.OSV, len(bigdataAdxExpItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxExpItem.DIDMd5, len(bigdataAdxExpItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxExpItem.Imei, len(bigdataAdxExpItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxExpItem.ImeiMd5, len(bigdataAdxExpItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxExpItem.AndroidID, len(bigdataAdxExpItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxExpItem.AndroidIDMd5, len(bigdataAdxExpItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxExpItem.Idfa, len(bigdataAdxExpItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxExpItem.IdfaMd5, len(bigdataAdxExpItem.IdfaMd5))
	put.SetTextValByColName("caid_multi", bigdataAdxExpItem.CAIDMulti, len(bigdataAdxExpItem.CAIDMulti))
	put.SetTextValByColName("ip", bigdataAdxExpItem.IP, len(bigdataAdxExpItem.IP))
	put.SetTextValByColName("ua", bigdataAdxExpItem.UA, len(bigdataAdxExpItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxExpItem.Oaid, len(bigdataAdxExpItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataAdxExpItem.OaidMd5, len(bigdataAdxExpItem.OaidMd5))
	put.SetTextValByColName("model", bigdataAdxExpItem.Model, len(bigdataAdxExpItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxExpItem.Manufacturer, len(bigdataAdxExpItem.Manufacturer))
	put.SetInt32ValByColName("wrong_osv_in_ua", int32(bigdataAdxExpItem.WrongOSVInUA))
	put.SetInt32ValByColName("wrong_model_in_ua", int32(bigdataAdxExpItem.WrongModelInUA))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxExpItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxExpItem.FinalPrice))
	put.SetInt32ValByColName("ecpm", int32(bigdataAdxExpItem.Ecpm))
	put.SetInt32ValByColName("supply_ecpm", int32(bigdataAdxExpItem.SupplyEcpm))
	put.SetInt64ValByColName("exp_time", bigdataAdxExpItem.ExpTime)
	put.SetTextValByColName("mg_deal_id", bigdataAdxExpItem.MGDealID, len(bigdataAdxExpItem.MGDealID))
	put.SetTextValByColName("sdk_version", bigdataAdxExpItem.SDKVersion, len(bigdataAdxExpItem.SDKVersion))
	put.SetInt32ValByColName("connect_type", int32(bigdataAdxExpItem.ConnectType))
	put.SetInt32ValByColName("carrier", int32(bigdataAdxExpItem.Carrier))
	if bigdataAdxExpItem.DealTime > 0 {
		put.SetTimestamptzValByColName("deal_time", bigdataAdxExpItem.DealTime-946684800000000)
	}
	put.SetTextValByColName("supply_crid", bigdataAdxExpItem.SupplyCRID, len(bigdataAdxExpItem.SupplyCRID))
	put.SetTextValByColName("demand_crid", bigdataAdxExpItem.DemandCRID, len(bigdataAdxExpItem.DemandCRID))
	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataClickHouseClk ...
func BigDataClickHouseClk(c *gin.Context) {
	// fmt.Println("bigdata clk")

	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("bigdata clk nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// sdk p_app_id
	pAppID := c.Query("p_app_id")
	// sdk3.3使用宏 pappid=__P_APP_ID__
	if len(c.Query("pappid")) > 0 && c.Query("pappid") != "__P_APP_ID__" {
		pAppID = c.Query("pappid")
	}
	if len(log.Get("p_app_id")) > 0 {
		pAppID = log.Get("p_app_id")
	}

	// sdk p_pos_id
	pPosID := c.Query("p_pos_id")
	// sdk3.3使用宏 pposid=__P_POS_ID__
	if len(c.Query("pposid")) > 0 && c.Query("pposid") != "__P_POS_ID__" {
		pPosID = c.Query("pposid")
	}
	if len(log.Get("p_pos_id")) > 0 {
		pPosID = log.Get("p_pos_id")
	}

	// sdk floor_price
	floorPrice := utils.ConvertStringToInt(c.Query("floor_price"))
	if floorPrice == 0 {
		floorPrice = utils.ConvertStringToInt(log.Get("floor_price"))
	}
	// sdk final_price
	finalPrice := utils.ConvertStringToInt(c.Query("final_price"))
	if finalPrice == 0 {
		finalPrice = utils.ConvertStringToInt(log.Get("final_price"))
	}
	if len(c.Query("ppfnp")) > 0 && c.Query("ppfnp") != "__PPFNP__" {
		sdkTmpDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("ppfnp"))
		sdkTmpDecrypt := utils.AesECBDecrypt(sdkTmpDecryptBase64, []byte(config.EncryptKEY))
		finalPrice = utils.ConvertStringToInt(string(sdkTmpDecrypt))
	}
	// sdk up_price
	ecpm := utils.ConvertStringToInt(c.Query("ecpm"))
	if ecpm == 0 {
		ecpm = utils.ConvertStringToInt(log.Get("ecpm"))
	}
	if len(c.Query("sdklogep")) > 0 && c.Query("sdklogep") != "__DPPLOGEP__" {
		sdkEcpmDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("sdklogep"))
		sdkEcpmDecrypt := utils.AesECBDecrypt(sdkEcpmDecryptBase64, []byte(config.EncryptKEY))
		ecpm = utils.ConvertStringToInt(string(sdkEcpmDecrypt))
	}

	// sdk channel
	channel := log.Get("channel")
	if len(channel) == 0 {
		if pAppID == "sop" {
			channel = "99"
		} else {
			channel = GetPlatformIDByPlatformInfo(c, pPosID, pAppID)
		}
	}

	var bigdataAdxClkItem BigdataClkStu
	bigdataAdxClkItem.UID = log.Get("uid")
	bigdataAdxClkItem.AdID = log.Get("adid")
	bigdataAdxClkItem.LocalAppID = log.Get("app_id")
	bigdataAdxClkItem.LocalPosID = log.Get("pos_id")
	bigdataAdxClkItem.LocalAppType = log.Get("app_type")
	bigdataAdxClkItem.Channel = channel
	bigdataAdxClkItem.PlatformAppID = pAppID
	bigdataAdxClkItem.PlatformPosID = pPosID
	bigdataAdxClkItem.PlatformAppType = log.Get("p_app_type")
	bigdataAdxClkItem.AppName = log.Get("app_name")
	bigdataAdxClkItem.AppBundle = log.Get("app_bundle")
	bigdataAdxClkItem.OS = log.Get("os")
	bigdataAdxClkItem.OSV = log.Get("osv")
	if len(log.Get("did_md5")) > 0 {
		bigdataAdxClkItem.DIDMd5 = log.Get("did_md5")
	}
	bigdataAdxClkItem.Imei = log.Get("imei")
	bigdataAdxClkItem.ImeiMd5 = log.Get("imei_md5")
	bigdataAdxClkItem.AndroidID = log.Get("android_id")
	bigdataAdxClkItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxClkItem.Idfa = log.Get("idfa")
	bigdataAdxClkItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataAdxClkItem.CAIDMulti = log.Get("caid_multi")
	bigdataAdxClkItem.IP = c.ClientIP()
	bigdataAdxClkItem.UA = c.GetHeader("User-Agent")
	bigdataAdxClkItem.Oaid = log.Get("oaid")
	bigdataAdxClkItem.OaidMd5 = log.Get("oaid_md5")
	bigdataAdxClkItem.Model = log.Get("model")
	bigdataAdxClkItem.Manufacturer = log.Get("manufacturer")
	bigdataAdxClkItem.DPI = log.Get("dpi")
	bigdataAdxClkItem.XDPI = log.Get("xdpi")
	bigdataAdxClkItem.ReqWidth = c.Query("req_width")
	bigdataAdxClkItem.ReqHeight = c.Query("req_height")
	bigdataAdxClkItem.Width = c.Query("width")
	bigdataAdxClkItem.Height = c.Query("height")
	bigdataAdxClkItem.DownX = c.Query("down_x")
	bigdataAdxClkItem.DownY = c.Query("down_y")
	bigdataAdxClkItem.UpX = c.Query("up_x")
	bigdataAdxClkItem.UpY = c.Query("up_y")
	bigdataAdxClkItem.MaplehazeDownX = c.Query("mh_down_x")
	bigdataAdxClkItem.MaplehazeDownY = c.Query("mh_down_y")
	bigdataAdxClkItem.MaplehazeUpX = c.Query("mh_up_x")
	bigdataAdxClkItem.MaplehazeUpY = c.Query("mh_up_y")
	bigdataAdxClkItem.SLD = c.Query("sld")
	bigdataAdxClkItem.IsServerSLD = log.Get("is_server_sld")
	bigdataAdxClkItem.IsServerReplaceXY = log.Get("is_server_replace_xy")
	bigdataAdxClkItem.IsLogicPixel = log.Get("is_logic_pixel")
	bigdataAdxClkItem.IsServerRealReplaceXY = log.Get("is_server_real_replace_xy")
	bigdataAdxClkItem.ServerRealReplaceXYType = log.Get("server_real_replace_xy_type")
	bigdataAdxClkItem.FloorPrice = floorPrice
	bigdataAdxClkItem.FinalPrice = finalPrice
	bigdataAdxClkItem.Ecpm = ecpm
	bigdataAdxClkItem.ClkTime = utils.GetCurrentMilliSecond()
	bigdataAdxClkItem.SDKVersion = log.Get("sdk_version")
	bigdataAdxClkItem.MGDealID = c.Query("mg_deal_id")
	bigdataAdxClkItem.IsSkipReport = c.Query("is_skip")
	bigdataAdxClkItem.ConnectType = utils.ConvertStringToInt(log.Get("connect_type"))
	bigdataAdxClkItem.Carrier = utils.ConvertStringToInt(log.Get("carrier"))
	bigdataAdxClkItem.ClickPointType = log.Get("clickpoint_type")
	bigdataAdxClkItem.ClickPointReplaceType = log.Get("clickpoint_replace_type")
	bigdataAdxClkItem.DealTime = utils.ConvertStringToInt64(log.Get("deal_time"))
	bigdataAdxClkItem.SupplyCRID = c.Query("supply_crid")
	bigdataAdxClkItem.DemandCRID = c.Query("demand_crid")

	// 服务端替换坐标
	if bigdataAdxClkItem.ClickPointReplaceType == "2" {
		// 配置的空, 替换为空
		// 配置的物理坐标, 转换为逻辑坐标
		// 配置的逻辑坐标, 替换为客户端上报xy
		if bigdataAdxClkItem.ClickPointType == "0" {
			bigdataAdxClkItem.DownX = ""
			bigdataAdxClkItem.DownY = ""
			bigdataAdxClkItem.UpX = ""
			bigdataAdxClkItem.UpY = ""
		} else if bigdataAdxClkItem.ClickPointType == "1" {
			tmpDPI := float32(utils.ConvertStringToFloat(bigdataAdxClkItem.XDPI))
			// 临时兼容dpi
			if tmpDPI == 0 {
				tmpDPI = float32(utils.ConvertStringToFloat(bigdataAdxClkItem.DPI))
				if tmpDPI > 120 {
					tmpDPI = tmpDPI / 160
				}
				bigdataAdxClkItem.XDPI = utils.ConvertFloat32ToString(tmpDPI)
			}
			if tmpDPI > 0 {
				bigdataAdxClkItem.DownX = utils.ConvertIntToString(int(float32(utils.ConvertStringToInt(bigdataAdxClkItem.DownX))/tmpDPI + 0.5))
				bigdataAdxClkItem.DownY = utils.ConvertIntToString(int(float32(utils.ConvertStringToInt(bigdataAdxClkItem.DownY))/tmpDPI + 0.5))
				bigdataAdxClkItem.UpX = utils.ConvertIntToString(int(float32(utils.ConvertStringToInt(bigdataAdxClkItem.UpX))/tmpDPI + 0.5))
				bigdataAdxClkItem.UpY = utils.ConvertIntToString(int(float32(utils.ConvertStringToInt(bigdataAdxClkItem.UpY))/tmpDPI + 0.5))
			}
		}
	}
	bigdataAdxClkItem.MaplehazeReportLink = c.Query("mh_link")
	if bigdataAdxClkItem.OS == "android" {
		// Mozilla/5.0 (Linux; Android 12; GIA-AN00 Build/HONORGIA-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.105 Mobile Safari/537.36
		// 12
		// GIA-AN00
		tmpOSV := bigdataAdxClkItem.OSV
		tmpModel := bigdataAdxClkItem.Model
		tmpUA := bigdataAdxClkItem.UA
		if strings.Contains(tmpUA, "Android "+tmpOSV) {
			bigdataAdxClkItem.WrongOSVInUA = 0
		} else {
			bigdataAdxClkItem.WrongOSVInUA = 1
		}

		if strings.Contains(strings.ToLower(tmpUA), strings.ToLower(tmpModel)) {
			bigdataAdxClkItem.WrongModelInUA = 0

		} else {
			bigdataAdxClkItem.WrongModelInUA = 1
		}
	} else if bigdataAdxClkItem.OS == "ios" {
		// Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
		// 16.4.1
		// iPhone15,3
		tmpOSV := bigdataAdxClkItem.OSV
		if strings.Count(tmpOSV, ".") >= 2 && strings.HasSuffix(tmpOSV, ".0") {
			tmpOSV = tmpOSV[:len(tmpOSV)-2]
		}

		tmpUA := bigdataAdxClkItem.UA
		tmpUA = strings.Replace(tmpUA, "_", ".", -1)
		if strings.Contains(tmpUA, tmpOSV) {
			bigdataAdxClkItem.WrongOSVInUA = 0
		} else {
			bigdataAdxClkItem.WrongOSVInUA = 1
		}

		bigdataAdxClkItem.WrongModelInUA = 0
	}

	// if bigdataAdxClkItem.PlatformAppID == "sop" {
	// } else {
	// 	if strings.HasPrefix(bigdataAdxClkItem.UA, "Mozilla") || strings.HasPrefix(bigdataAdxClkItem.UA, "Dalvik") {
	// 	} else {
	// 		// wrong ua 曝光点击直接拉黑1个月
	// 		if len(log.Get("origin_did_md5")) > 0 {
	// 			originDIDMd5 := log.Get("origin_did_md5")
	// 			if len(originDIDMd5) > 0 {
	// 				db.GlbRedis.Set(c, "black_didmd5_new_"+originDIDMd5, 1, time.Duration(30*24)*time.Hour).Err()
	// 			}
	// 		}
	// 	}
	// }

	// 生成点击坐标库
	// setClickXYToRedis(c, bigdataAdxClkItem)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata clk panic:", err)
			}
		}()

		// 统计点击
		GoBigDataClickHouseClk(bigdataAdxClkItem)

		// 根据新点击坐标配置库存点击坐标库
		BigDataClickXYToBigData2(c, bigdataAdxClkItem)

		// StatisticsClk(c, bigdataAdxClkItem.LocalAppID, bigdataAdxClkItem.LocalPosID, bigdataAdxClkItem.PlatformAppID, bigdataAdxClkItem.PlatformPosID)

		GoBigDataHoloClk(bigdataAdxClkItem)

		// TODO: huwenzhi clk
		go clk.DidDemandDidClk(c, bigdataAdxClkItem.DIDMd5, bigdataAdxClkItem.IP, bigdataAdxClkItem.PlatformAppID)
		go clk.DidDemandDidLastTimeClk(c, bigdataAdxClkItem.DIDMd5, bigdataAdxClkItem.IP, bigdataAdxClkItem.PlatformAppID)
		go clk.DidDemandIpClk(c, bigdataAdxClkItem.DIDMd5, bigdataAdxClkItem.IP, bigdataAdxClkItem.PlatformAppID)

		if rand.Intn(100) < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugPAppId) > 0 {
			pAppIdArr := strings.Split(utilities.DebugPAppId, ",")
			for _, pAppId := range pAppIdArr {
				if pAppId == bigdataAdxClkItem.PlatformAppID {
					GoBigDataClickHolo(&bigdataAdxClkItem)
					break
				}
			}
		}

		// 上报点击
		ReportClkLink(c, bigdataAdxClkItem)
	}()
}

func GoBigDataClickHolo(bigdataAdxClkItem *BigdataClkStu) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "demand_replace_clk_data"))
	uuidStr := uuid.NewV4().String()
	put.SetTextValByColName("id", uuidStr, len(uuidStr))
	put.SetTextValByColName("reqid", bigdataAdxClkItem.UID, len(bigdataAdxClkItem.UID))
	put.SetTextValByColName("app_id", bigdataAdxClkItem.LocalAppID, len(bigdataAdxClkItem.LocalAppID))
	put.SetInt32ValByColName("app_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.LocalAppType)))
	put.SetTextValByColName("pos_id", bigdataAdxClkItem.LocalPosID, len(bigdataAdxClkItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxClkItem.PlatformAppID, len(bigdataAdxClkItem.PlatformAppID))
	put.SetInt32ValByColName("p_app_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.PlatformAppType)))
	put.SetTextValByColName("p_pos_id", bigdataAdxClkItem.PlatformPosID, len(bigdataAdxClkItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxClkItem.Channel, len(bigdataAdxClkItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxClkItem.AppName, len(bigdataAdxClkItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxClkItem.AppBundle, len(bigdataAdxClkItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxClkItem.OS, len(bigdataAdxClkItem.OS))
	put.SetTextValByColName("osv", bigdataAdxClkItem.OSV, len(bigdataAdxClkItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxClkItem.DIDMd5, len(bigdataAdxClkItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxClkItem.Imei, len(bigdataAdxClkItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxClkItem.ImeiMd5, len(bigdataAdxClkItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxClkItem.AndroidID, len(bigdataAdxClkItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxClkItem.AndroidIDMd5, len(bigdataAdxClkItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxClkItem.Idfa, len(bigdataAdxClkItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxClkItem.IdfaMd5, len(bigdataAdxClkItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxClkItem.IP, len(bigdataAdxClkItem.IP))
	put.SetTextValByColName("ua", bigdataAdxClkItem.UA, len(bigdataAdxClkItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxClkItem.Oaid, len(bigdataAdxClkItem.Oaid))
	put.SetTextValByColName("model", bigdataAdxClkItem.Model, len(bigdataAdxClkItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxClkItem.Manufacturer, len(bigdataAdxClkItem.Manufacturer))
	put.SetInt32ValByColName("wrong_osv_in_ua", int32(bigdataAdxClkItem.WrongOSVInUA))
	put.SetInt32ValByColName("wrong_model_in_ua", int32(bigdataAdxClkItem.WrongModelInUA))
	put.SetTextValByColName("req_width", bigdataAdxClkItem.ReqWidth, len(bigdataAdxClkItem.ReqWidth))
	put.SetTextValByColName("req_height", bigdataAdxClkItem.ReqHeight, len(bigdataAdxClkItem.ReqHeight))
	put.SetTextValByColName("width", bigdataAdxClkItem.Width, len(bigdataAdxClkItem.Width))
	put.SetTextValByColName("height", bigdataAdxClkItem.Height, len(bigdataAdxClkItem.Height))
	put.SetTextValByColName("down_x", bigdataAdxClkItem.DownX, len(bigdataAdxClkItem.DownX))
	put.SetTextValByColName("down_y", bigdataAdxClkItem.DownY, len(bigdataAdxClkItem.DownY))
	put.SetTextValByColName("up_x", bigdataAdxClkItem.UpX, len(bigdataAdxClkItem.UpX))
	put.SetTextValByColName("up_y", bigdataAdxClkItem.UpY, len(bigdataAdxClkItem.UpY))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxClkItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxClkItem.FinalPrice))
	put.SetInt32ValByColName("ecpm", int32(bigdataAdxClkItem.Ecpm))
	put.SetInt64ValByColName("clk_time", bigdataAdxClkItem.ClkTime)
	put.SetTextValByColName("sld", bigdataAdxClkItem.SLD, len(bigdataAdxClkItem.SLD))
	put.SetInt32ValByColName("is_server_sld", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsServerSLD)))
	put.SetInt32ValByColName("is_server_replace_xy", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsServerReplaceXY)))
	put.SetInt32ValByColName("is_logic_pixel", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsLogicPixel)))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func ReportClkLink(c context.Context, bigdataAdxClkItem BigdataClkStu) {
	if len(bigdataAdxClkItem.MaplehazeReportLink) == 0 {
		return
	}

	decryptBase64, _ := base64.StdEncoding.DecodeString(bigdataAdxClkItem.MaplehazeReportLink)
	tmpMaplehazeReportLink := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))

	var tmpClkLinkArray []string
	json.Unmarshal([]byte(tmpMaplehazeReportLink), &tmpClkLinkArray)
	for _, tmpLink := range tmpClkLinkArray {
		tmpItem := tmpLink
		tmpDownX := bigdataAdxClkItem.DownX
		tmpDownY := bigdataAdxClkItem.DownY
		tmpUpX := bigdataAdxClkItem.UpX
		tmpUpY := bigdataAdxClkItem.UpY
		tmpSLD := bigdataAdxClkItem.SLD
		tmpDPI := float32(utils.ConvertStringToFloat(bigdataAdxClkItem.XDPI))

		tmpClickPointType := utils.ConvertStringToInt(bigdataAdxClkItem.ClickPointType)
		if tmpClickPointType == 0 {
		} else if tmpClickPointType == 1 {
			if tmpDPI == 0 {
				return
			}
		}

		tmpItem = strings.Replace(tmpItem, "__DOWN_X__", tmpDownX, -1)
		tmpItem = strings.Replace(tmpItem, "__DOWN_Y__", tmpDownY, -1)
		tmpItem = strings.Replace(tmpItem, "__UP_X__", tmpUpX, -1)
		tmpItem = strings.Replace(tmpItem, "__UP_Y__", tmpUpY, -1)

		tmpItem = strings.Replace(tmpItem, "__SLD__", tmpSLD, -1)

		// http请求
		client := &http.Client{Timeout: 1000 * time.Millisecond}
		requestGet, _ := http.NewRequest("GET", tmpItem, nil)

		requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
		requestGet.Header.Add("X-Forwarded-For", bigdataAdxClkItem.IP)
		requestGet.Header.Add("User-Agent", bigdataAdxClkItem.UA)
		fmt.Println("nurl req: ", requestGet.URL.String())

		resp, err := client.Do(requestGet)
		if err != nil {
			fmt.Println("get request failed, err:", err.Error())
			return
		}

		// go BigDataHoloDebugJson2(bigdataAdxClkItem.UID+"&mh_rtb_r", string(tmpLink), "dx:"+tmpDownX+", dy:"+tmpDownY+", ux:"+tmpUpX+", uy:"+tmpUpY+", dpi:"+bigdataAdxClkItem.XDPI+", clickpoint_type:"+bigdataAdxClkItem.ClickPointType,
		// 	string(tmpItem), bigdataAdxClkItem.PlatformAppID, bigdataAdxClkItem.PlatformPosID)

		defer resp.Body.Close()
	}
}

// GoBigDataClickHouseClk ...
func GoBigDataClickHouseClk(bigdataAdxClkItem BigdataClkStu) {

	bigdataClkMutex.Lock()
	bigdataClkArray = append(bigdataClkArray, bigdataAdxClkItem)

	if len(bigdataClkArray) < config.ClickHouseClkMaxNum && utils.GetCurrentSecond()-bigdataClkTime < config.ClickHouseInterval {
		bigdataClkMutex.Unlock()
		return
	}

	destClkArray := bigdataClkArray[0:]
	bigdataClkArray = bigdataClkArray[0:0]
	bigdataClkMutex.Unlock()

	bigdataClkTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare("INSERT INTO ssp_rawdata.clk_data (uid, app_id, app_type, pos_id, p_app_id, p_app_type, p_pos_id, channel, app_name," +
		"app_bundle, os, osv, did_md5, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, model," +
		"manufacturer, wrong_osv_in_ua, wrong_model_in_ua, req_width, req_height, width, height, down_x, down_y, up_x, up_y, sld, " +
		"is_server_sld, is_server_replace_xy, is_logic_pixel, " +
		"floor_price, final_price, ecpm, clk_time, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	if stmt == nil {
		fmt.Println("bigdata err clk:", len(destClkArray), err)
		return
	}

	defer stmt.Close()

	for _, item := range destClkArray {
		if _, err := stmt.Exec(
			item.UID,
			item.LocalAppID,
			utils.ConvertStringToInt(item.LocalAppType),
			item.LocalPosID,
			item.PlatformAppID,
			utils.ConvertStringToInt(item.PlatformAppType),
			item.PlatformPosID,
			item.Channel,
			item.AppName,
			item.AppBundle,
			item.OS,
			item.OSV,
			item.DIDMd5,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.WrongOSVInUA,
			item.WrongModelInUA,
			item.ReqWidth,
			item.ReqHeight,
			item.Width,
			item.Height,
			item.DownX,
			item.DownY,
			item.UpX,
			item.UpY,
			item.SLD,
			utils.ConvertStringToInt(item.IsServerSLD),
			utils.ConvertStringToInt(item.IsServerReplaceXY),
			utils.ConvertStringToInt(item.IsLogicPixel),
			item.FloorPrice,
			item.FinalPrice,
			item.Ecpm,
			item.ClkTime,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}
	// fmt.Println("clickhouse clk end")
}

// GoBigDataHoloClk ...
func GoBigDataHoloClk(bigdataAdxClkItem BigdataClkStu) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", "clk_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxClkItem.UID, len(bigdataAdxClkItem.UID))
	put.SetTextValByColName("adid", bigdataAdxClkItem.AdID, len(bigdataAdxClkItem.AdID))
	put.SetTextValByColName("app_id", bigdataAdxClkItem.LocalAppID, len(bigdataAdxClkItem.LocalAppID))
	put.SetInt32ValByColName("app_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.LocalAppType)))
	put.SetTextValByColName("pos_id", bigdataAdxClkItem.LocalPosID, len(bigdataAdxClkItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxClkItem.PlatformAppID, len(bigdataAdxClkItem.PlatformAppID))
	put.SetInt32ValByColName("p_app_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.PlatformAppType)))
	put.SetTextValByColName("p_pos_id", bigdataAdxClkItem.PlatformPosID, len(bigdataAdxClkItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxClkItem.Channel, len(bigdataAdxClkItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxClkItem.AppName, len(bigdataAdxClkItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxClkItem.AppBundle, len(bigdataAdxClkItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxClkItem.OS, len(bigdataAdxClkItem.OS))
	put.SetTextValByColName("osv", bigdataAdxClkItem.OSV, len(bigdataAdxClkItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxClkItem.DIDMd5, len(bigdataAdxClkItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxClkItem.Imei, len(bigdataAdxClkItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxClkItem.ImeiMd5, len(bigdataAdxClkItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxClkItem.AndroidID, len(bigdataAdxClkItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxClkItem.AndroidIDMd5, len(bigdataAdxClkItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxClkItem.Idfa, len(bigdataAdxClkItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxClkItem.IdfaMd5, len(bigdataAdxClkItem.IdfaMd5))
	put.SetTextValByColName("caid_multi", bigdataAdxClkItem.CAIDMulti, len(bigdataAdxClkItem.CAIDMulti))
	put.SetTextValByColName("ip", bigdataAdxClkItem.IP, len(bigdataAdxClkItem.IP))
	put.SetTextValByColName("ua", bigdataAdxClkItem.UA, len(bigdataAdxClkItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxClkItem.Oaid, len(bigdataAdxClkItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataAdxClkItem.OaidMd5, len(bigdataAdxClkItem.OaidMd5))
	put.SetTextValByColName("model", bigdataAdxClkItem.Model, len(bigdataAdxClkItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxClkItem.Manufacturer, len(bigdataAdxClkItem.Manufacturer))
	put.SetInt32ValByColName("wrong_osv_in_ua", int32(bigdataAdxClkItem.WrongOSVInUA))
	put.SetInt32ValByColName("wrong_model_in_ua", int32(bigdataAdxClkItem.WrongModelInUA))
	put.SetTextValByColName("req_width", bigdataAdxClkItem.ReqWidth, len(bigdataAdxClkItem.ReqWidth))
	put.SetTextValByColName("req_height", bigdataAdxClkItem.ReqHeight, len(bigdataAdxClkItem.ReqHeight))
	put.SetTextValByColName("width", bigdataAdxClkItem.Width, len(bigdataAdxClkItem.Width))
	put.SetTextValByColName("height", bigdataAdxClkItem.Height, len(bigdataAdxClkItem.Height))
	put.SetTextValByColName("down_x", bigdataAdxClkItem.DownX, len(bigdataAdxClkItem.DownX))
	put.SetTextValByColName("down_y", bigdataAdxClkItem.DownY, len(bigdataAdxClkItem.DownY))
	put.SetTextValByColName("up_x", bigdataAdxClkItem.UpX, len(bigdataAdxClkItem.UpX))
	put.SetTextValByColName("up_y", bigdataAdxClkItem.UpY, len(bigdataAdxClkItem.UpY))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxClkItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxClkItem.FinalPrice))
	put.SetInt32ValByColName("ecpm", int32(bigdataAdxClkItem.Ecpm))
	put.SetInt64ValByColName("clk_time", bigdataAdxClkItem.ClkTime)
	put.SetTextValByColName("sld", bigdataAdxClkItem.SLD, len(bigdataAdxClkItem.SLD))
	put.SetInt32ValByColName("is_server_sld", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsServerSLD)))
	put.SetInt32ValByColName("is_server_replace_xy", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsServerReplaceXY)))
	put.SetInt32ValByColName("is_logic_pixel", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsLogicPixel)))
	put.SetTextValByColName("mg_deal_id", bigdataAdxClkItem.MGDealID, len(bigdataAdxClkItem.MGDealID))
	put.SetTextValByColName("sdk_version", bigdataAdxClkItem.SDKVersion, len(bigdataAdxClkItem.SDKVersion))
	put.SetInt32ValByColName("is_skip_report", int32(utils.ConvertStringToInt(bigdataAdxClkItem.IsSkipReport)))
	put.SetInt32ValByColName("connect_type", int32(bigdataAdxClkItem.ConnectType))
	put.SetInt32ValByColName("carrier", int32(bigdataAdxClkItem.Carrier))
	put.SetTextValByColName("dpi", bigdataAdxClkItem.DPI, len(bigdataAdxClkItem.DPI))
	put.SetTextValByColName("xdpi", bigdataAdxClkItem.XDPI, len(bigdataAdxClkItem.XDPI))
	put.SetInt32ValByColName("clickpoint_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.ClickPointType)))
	put.SetInt32ValByColName("clickpoint_replace_type", int32(utils.ConvertStringToInt(bigdataAdxClkItem.ClickPointReplaceType)))
	if bigdataAdxClkItem.DealTime > 0 {
		put.SetTimestamptzValByColName("deal_time", bigdataAdxClkItem.DealTime-946684800000000)
	}
	put.SetTextValByColName("supply_crid", bigdataAdxClkItem.SupplyCRID, len(bigdataAdxClkItem.SupplyCRID))
	put.SetTextValByColName("demand_crid", bigdataAdxClkItem.DemandCRID, len(bigdataAdxClkItem.DemandCRID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataAdxMismatchBundle ...
func BigDataAdxMismatchBundle(c context.Context, bigdataUID string, mhReq *MHReq, localPos *LocalPosStu) {
}

// BigDataRtbReqPrice ...
func BigDataRtbReqPrice(c context.Context, tagID string, price int) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("clickhouse rtb req price panic:", err)
			}
		}()

		// rtb请求价存holo
		if utilities.SkipHologress {
			return
		}

		randNum := rand.Intn(100)
		if randNum != 0 {
			return
		}

		tableName := "rtb_cpm_bid_floor_data"

		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

		uid := uuid.NewV4().String()
		put.SetTextValByColName("id", uid, len(uid))
		put.SetTextValByColName("tagid", tagID, len(tagID))
		put.SetInt32ValByColName("cpm_bid_floor", int32(price))

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPDataDb.Submit(put)
	}()
}

// BigDataRtbEcpmToHolo ...
func BigDataRtbEcpmToHolo(c context.Context, bigdataUID string, tagID string, localAppID string, localPosID string, cpmBidFloor int, ecpm int) {

	go func() {
		if utilities.SkipHologress {
			return
		}

		defer func() {
			if err := recover(); err != nil {
				fmt.Println("holo rtb ecpm panic:", err)
			}
		}()

		tableName := "rtb_cpm_data"

		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

		put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
		put.SetTextValByColName("tagid", tagID, len(tagID))
		put.SetTextValByColName("app_id", localAppID, len(localAppID))
		put.SetTextValByColName("pos_id", localPosID, len(localPosID))
		put.SetInt32ValByColName("cpm_bid_floor", int32(cpmBidFloor))
		put.SetInt32ValByColName("ecpm", int32(ecpm))

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPDataDb.Submit(put)
	}()
}

// BigDataWinPriceToHolo ...
func BigDataWinPriceToHolo(c context.Context, bigdataPriceItem *BigdataPriceStu) {
	if utilities.SkipHologress {
		return
	}

	tableName := "win_price_data"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataPriceItem.UID, len(bigdataPriceItem.UID))
	put.SetTextValByColName("adid", bigdataPriceItem.AdID, len(bigdataPriceItem.AdID))
	put.SetTextValByColName("tagid", bigdataPriceItem.TagID, len(bigdataPriceItem.TagID))
	put.SetTextValByColName("app_id", bigdataPriceItem.LocalAppID, len(bigdataPriceItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataPriceItem.LocalPosID, len(bigdataPriceItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataPriceItem.PlatformAppID, len(bigdataPriceItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataPriceItem.PlatformPosID, len(bigdataPriceItem.PlatformPosID))
	put.SetInt32ValByColName("ecpm", int32(bigdataPriceItem.BidPrice))
	put.SetInt32ValByColName("win_price", int32(bigdataPriceItem.WinPrice))
	put.SetTextValByColName("mg_deal_id", bigdataPriceItem.MGDealID, len(bigdataPriceItem.MGDealID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataLossPriceToHolo ...
func BigDataLossPriceToHolo(c context.Context, bigdataPriceItem *BigdataPriceStu) {
	if utilities.SkipHologress {
		return
	}

	tableName := "loss_price_data"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataPriceItem.UID, len(bigdataPriceItem.UID))
	put.SetTextValByColName("adid", bigdataPriceItem.AdID, len(bigdataPriceItem.AdID))
	put.SetTextValByColName("tagid", bigdataPriceItem.TagID, len(bigdataPriceItem.TagID))
	put.SetTextValByColName("app_id", bigdataPriceItem.LocalAppID, len(bigdataPriceItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataPriceItem.LocalPosID, len(bigdataPriceItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataPriceItem.PlatformAppID, len(bigdataPriceItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataPriceItem.PlatformPosID, len(bigdataPriceItem.PlatformPosID))
	put.SetInt32ValByColName("ecpm", int32(bigdataPriceItem.BidPrice))
	put.SetInt32ValByColName("loss_price", int32(bigdataPriceItem.LossPrice))
	put.SetTextValByColName("mg_deal_id", bigdataPriceItem.MGDealID, len(bigdataPriceItem.MGDealID))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataAdxDeepLink ...
func BigDataAdxDeepLink(c *gin.Context) {
	// fmt.Println("bigdata exp")

	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("bigdata deeplink nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	var bigdataAdxDeepLinkItem BigdataDeepLinkStu
	bigdataAdxDeepLinkItem.UID = log.Get("uid")
	bigdataAdxDeepLinkItem.LocalAppID = log.Get("app_id")
	bigdataAdxDeepLinkItem.LocalPosID = log.Get("pos_id")
	bigdataAdxDeepLinkItem.PlatformAppID = log.Get("p_app_id")
	bigdataAdxDeepLinkItem.PlatformPosID = log.Get("p_pos_id")
	bigdataAdxDeepLinkItem.OS = log.Get("os")
	bigdataAdxDeepLinkItem.OSV = log.Get("osv")
	bigdataAdxDeepLinkItem.Imei = log.Get("imei")
	bigdataAdxDeepLinkItem.ImeiMd5 = log.Get("imei_md5")
	bigdataAdxDeepLinkItem.AndroidID = log.Get("android_id")
	bigdataAdxDeepLinkItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxDeepLinkItem.Idfa = log.Get("idfa")
	bigdataAdxDeepLinkItem.IP = c.ClientIP()
	bigdataAdxDeepLinkItem.UA = c.GetHeader("User-Agent")
	bigdataAdxDeepLinkItem.Oaid = log.Get("oaid")
	bigdataAdxDeepLinkItem.Model = log.Get("model")
	bigdataAdxDeepLinkItem.Manufacturer = log.Get("manufacturer")
	bigdataAdxDeepLinkItem.DeepLinkResult = c.Query("result")
	bigdataAdxDeepLinkItem.DeepLinkReason = c.Query("reason")

	bigdataDeepLinkMutex.Lock()
	bigdataDeepLinkArray = append(bigdataDeepLinkArray, bigdataAdxDeepLinkItem)

	if len(bigdataDeepLinkArray) < config.ClickHouseDeepLinkMaxNum && utils.GetCurrentSecond()-bigdataDeepLinkTime < config.ClickHouseInterval {
		bigdataDeepLinkMutex.Unlock()
		return
	}

	destDeepLinkArray := bigdataDeepLinkArray[0:]
	bigdataDeepLinkArray = bigdataDeepLinkArray[0:0]
	bigdataDeepLinkMutex.Unlock()

	bigdataDeepLinkTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare("INSERT INTO ssp_rawdata.deeplink_data (uid, app_id, pos_id, p_app_id, p_pos_id," +
		"os, osv, imei, imei_md5, android_id, android_id_md5, idfa, ip, ua, oaid, model," +
		"manufacturer, deeplink_result, deeplink_reason, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	if stmt == nil {
		fmt.Println("bigdata err deeplink:", len(destDeepLinkArray), err)
		return
	}

	defer stmt.Close()

	for _, item := range destDeepLinkArray {
		if _, err := stmt.Exec(
			item.UID,
			item.LocalAppID,
			item.LocalPosID,
			item.PlatformAppID,
			item.PlatformPosID,
			item.OS,
			item.OSV,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.DeepLinkResult,
			item.DeepLinkReason,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}
	// fmt.Println("clickhouse exp end")
}

// BigDataAdxVideoStart ...
func BigDataAdxVideoStart(c *gin.Context) {
	// fmt.Println("bigdata video start")
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("bigdata video start nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	var bigdataItem BigdataVideoTrackStu
	bigdataItem.UID = log.Get("uid")
	bigdataItem.LocalAppID = log.Get("app_id")
	bigdataItem.LocalPosID = log.Get("pos_id")
	bigdataItem.PlatformAppID = log.Get("p_app_id")
	bigdataItem.PlatformPosID = log.Get("p_pos_id")

	// save to holo
	tableName := "video_start_data"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataItem.UID, len(bigdataItem.UID))
	put.SetTextValByColName("app_id", bigdataItem.LocalAppID, len(bigdataItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataItem.LocalPosID, len(bigdataItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataItem.PlatformAppID, len(bigdataItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataItem.PlatformPosID, len(bigdataItem.PlatformPosID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataAdxVideoEnd ...
func BigDataAdxVideoEnd(c *gin.Context) {
	// fmt.Println("bigdata exp")
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("bigdata video end nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	var bigdataItem BigdataVideoTrackStu
	bigdataItem.UID = log.Get("uid")
	bigdataItem.LocalAppID = log.Get("app_id")
	bigdataItem.LocalPosID = log.Get("pos_id")
	bigdataItem.PlatformAppID = log.Get("p_app_id")
	bigdataItem.PlatformPosID = log.Get("p_pos_id")

	// save to holo
	tableName := "video_end_data"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("deal", tableName))

	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataItem.UID, len(bigdataItem.UID))
	put.SetTextValByColName("app_id", bigdataItem.LocalAppID, len(bigdataItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataItem.LocalPosID, len(bigdataItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataItem.PlatformAppID, len(bigdataItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataItem.PlatformPosID, len(bigdataItem.PlatformPosID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// BigDataClickXYToBigData2 ...
func BigDataClickXYToBigData2(c context.Context, bigdataClkStu BigdataClkStu) {
	// fmt.Println("bigdata click_xy")
	// return

	var clickPointLibStorageArray []ClickPointLibStorageStu

	cacheValue, cacheError := db.GlbBigCache.Get("go_click_point_lib_storage")
	if cacheError != nil {
		return
	}

	json.Unmarshal(cacheValue, &clickPointLibStorageArray)
	if len(clickPointLibStorageArray) == 0 {
		return
	}

	isInConfigLocalKey := ""
	pixelType := 0
	for _, item := range clickPointLibStorageArray {
		if len(item.LocalAppID) > 0 && len(item.LocalPosID) > 0 {
			if item.LocalAppID == bigdataClkStu.LocalAppID &&
				item.LocalPosID == bigdataClkStu.LocalPosID {
				isInConfigLocalKey = item.Key
				pixelType = item.PixelType
			}
		}
	}

	if len(isInConfigLocalKey) > 0 {
	} else {
		return
	}

	dpi := float32(utils.ConvertStringToFloat(bigdataClkStu.XDPI))
	// if dpi < 0 {
	//  return
	// }

	// ios dpi > 3, 直接置0, 之后变为3
	if bigdataClkStu.OS == "ios" && dpi > 3 {
		dpi = 0
	} else if bigdataClkStu.OS == "android" && dpi > 4 {
		dpi = 0
	}
	// 如果没传dpi, 设置为3
	if dpi <= 0 {
		dpi = 3
	}

	rawDownX := utils.ConvertStringToInt(bigdataClkStu.MaplehazeDownX)
	rawDownY := utils.ConvertStringToInt(bigdataClkStu.MaplehazeDownY)
	rawUpX := utils.ConvertStringToInt(bigdataClkStu.MaplehazeUpX)
	rawUpY := utils.ConvertStringToInt(bigdataClkStu.MaplehazeUpY)

	physicalDownX := 0
	physicalDownY := 0
	physicalUpX := 0
	physicalUpY := 0

	logicDownX := 0
	logicDownY := 0
	logicUpX := 0
	logicUpY := 0

	ptDownX := 0
	ptDownY := 0
	ptUpX := 0
	ptUpY := 0

	if pixelType == 0 {
		// 1. 初始化物理像素
		physicalDownX = rawDownX
		physicalDownY = rawDownY
		physicalUpX = rawUpX
		physicalUpY = rawUpY

		// 2. 计算逻辑像素
		logicDownX = int(float32(physicalDownX)/dpi + 0.5)
		logicDownY = int(float32(physicalDownY)/dpi + 0.5)
		logicUpX = int(float32(physicalUpX)/dpi + 0.5)
		logicUpY = int(float32(physicalUpY)/dpi + 0.5)

		// 3. 计算独立像素
		ptDownX = physicalDownX / 3
		ptDownY = physicalDownY / 3
		ptUpX = physicalUpX / 3
		ptUpY = physicalUpY / 3
	} else if pixelType == 1 {
		// 1. 初始化逻辑像素
		logicDownX = rawDownX
		logicDownY = rawDownY
		logicUpX = rawUpX
		logicUpY = rawUpY

		// 2. 计算物理像素
		physicalDownX = int(float32(rawDownX) * dpi)
		physicalDownY = int(float32(rawDownY) * dpi)
		physicalUpX = int(float32(rawUpX) * dpi)
		physicalUpY = int(float32(rawUpY) * dpi)

		// 3. 计算独立像素
		ptDownX = physicalDownX / 3
		ptDownY = physicalDownY / 3
		ptUpX = physicalUpX / 3
		ptUpY = physicalUpY / 3

	} else if pixelType == 2 {
		// 1. 初始化独立像素
		ptDownX = rawDownX
		ptDownY = rawDownY
		ptUpX = rawUpX
		ptUpY = rawUpY

		// 2. 计算物理像素
		physicalDownX = ptDownX * 3
		physicalDownY = ptDownY * 3
		physicalUpX = ptUpX * 3
		physicalUpY = ptUpY * 3

		// 3. 计算逻辑像素
		logicDownX = int(float32(physicalDownX)/dpi + 0.5)
		logicDownY = int(float32(physicalDownY)/dpi + 0.5)
		logicUpX = int(float32(physicalUpX)/dpi + 0.5)
		logicUpY = int(float32(physicalUpY)/dpi + 0.5)
	} else {
		return
	}

	// 热力图物理像素宽高最大值 & 坐标库（旧）宽高过滤值: 1440x3200
	// 热力图逻辑像素宽高最大值 & 坐标库（旧）宽高过滤值: 360x800
	// 热力图独立像素宽高最大值 & 坐标库（旧）宽高过滤值: 390x844
	// TODO: 原始数据先不过滤, 观察观察数据再说
	if physicalDownX <= 0 || physicalDownY <= 0 || physicalUpX <= 0 || physicalUpY <= 0 ||
		logicDownX <= 0 || logicDownY <= 0 || logicUpX <= 0 || logicUpY <= 0 ||
		ptDownX <= 0 || ptDownY <= 0 || ptUpX <= 0 || ptUpY <= 0 {
		return
	}

	var bigdataItem BigdataClickXYStu
	bigdataItem.LocalAppID = bigdataClkStu.LocalAppID
	bigdataItem.LocalPosID = bigdataClkStu.LocalPosID
	bigdataItem.PlatformAppID = bigdataClkStu.PlatformAppID
	bigdataItem.PlatformPosID = bigdataClkStu.PlatformPosID

	// bigdataItem.Md5Key = destConfigStorageInfo.Key
	bigdataItem.LocalAppType = bigdataClkStu.LocalAppType
	bigdataItem.PhysicalDownX = physicalDownX
	bigdataItem.PhysicalDownY = physicalDownY
	bigdataItem.PhysicalUpX = physicalUpX
	bigdataItem.PhysicalUpY = physicalUpY
	bigdataItem.LogicDownX = logicDownX
	bigdataItem.LogicDownY = logicDownY
	bigdataItem.LogicUpX = logicUpX
	bigdataItem.LogicUpY = logicUpY
	bigdataItem.PtDownX = ptDownX
	bigdataItem.PtDownY = ptDownY
	bigdataItem.PtUpX = ptUpX
	bigdataItem.PtUpY = ptUpY
	bigdataItem.DPI = dpi
	bigdataItem.Os = bigdataClkStu.OS
	bigdataItem.IsLogicPixel = pixelType

	bigdataClickXYMutex.Lock()
	if len(isInConfigLocalKey) > 0 {
		bigdataItem.Md5Key = isInConfigLocalKey
		bigdataClickXYArray = append(bigdataClickXYArray, bigdataItem)
	}

	if len(bigdataClickXYArray) < config.ClickHouseClickXYMaxNum && utils.GetCurrentSecond()-bigdataClickXYTime < config.ClickHouseInterval {
		bigdataClickXYMutex.Unlock()
		return
	}

	destArray := bigdataClickXYArray[0:]
	bigdataClickXYArray = bigdataClickXYArray[0:0]
	bigdataClickXYMutex.Unlock()

	bigdataClickXYTime = utils.GetCurrentSecond()

	// 存点击坐标到holo
	tableName := "clickpoint_lib"

	if utilities.SkipHologress {
		return
	}
	for _, item := range destArray {

		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("magic", tableName))
		uuid := uuid.NewV4().String()

		put.SetTextValByColName("id", uuid, len(uuid))
		put.SetTextValByColName("clickpoint_lib_id", item.Md5Key, len(item.Md5Key))
		put.SetInt32ValByColName("down_x", int32(item.PhysicalDownX))
		put.SetInt32ValByColName("down_y", int32(item.PhysicalDownY))
		put.SetInt32ValByColName("up_x", int32(item.PhysicalUpX))
		put.SetInt32ValByColName("up_y", int32(item.PhysicalUpY))
		put.SetInt32ValByColName("logic_down_x", int32(item.LogicDownX))
		put.SetInt32ValByColName("logic_down_y", int32(item.LogicDownY))
		put.SetInt32ValByColName("logic_up_x", int32(item.LogicUpX))
		put.SetInt32ValByColName("logic_up_y", int32(item.LogicUpY))
		put.SetInt32ValByColName("pt_down_x", int32(item.PtDownX))
		put.SetInt32ValByColName("pt_down_y", int32(item.PtDownY))
		put.SetInt32ValByColName("pt_up_x", int32(item.PtUpX))
		put.SetInt32ValByColName("pt_up_y", int32(item.PtUpY))
		put.SetFloat32ValByColName("dpi", item.DPI)

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPDataDb.Submit(put)
	}

	// fmt.Println("clickhouse click_xy end")
}

// 向dmp中写入数据
// !!! HARD CODE !!!
// TODO: 写死了dmp数据来源
func insertDmpDidRoleData(c context.Context, destArrayPoint *[]BigdataDIDAppListStu) {
	destArray := *destArrayPoint
	roleId1 := []*DmpDidRoleData{}
	roleId2 := []*DmpDidRoleData{}
	role2Keywords := []string{
		"高德地图",
		"T3出行",
		"曹操出行",
		"嘀嗒出行",
		"花小猪打车",
		"如祺出行",
		"美团打车",
		"万顺叫车",
		"享道出行",
		"东风出行",
		"首汽约车",
		"快狗打车",
		"阳光出行",
		"叮嗒出行",
	}

	for _, item := range destArray {
		_, hh, mm, _ := utils.GetNowDDHHMMSS()
		// role1 如果命中关键词
		if strings.Contains(item.AppList, "滴滴") {
			roleId1 = append(roleId1, &DmpDidRoleData{
				RoleId:    1,
				DidMd5:    item.DIDMd5,
				Imei:      item.Imei,
				AndroidId: item.AndroidID,
				Oaid:      item.Oaid,
				Date:      time.Now(),
				Hour:      hh,
				Minute:    mm,
			})
		}

		for _, keyword := range role2Keywords {
			if strings.Contains(item.AppList, keyword) {
				roleId2 = append(roleId2, &DmpDidRoleData{
					RoleId:    2,
					DidMd5:    item.DIDMd5,
					Imei:      item.Imei,
					AndroidId: item.AndroidID,
					Oaid:      item.Oaid,
					Date:      time.Now(),
					Hour:      hh,
					Minute:    mm,
				})
				break
			}
		}
	}

	insertDmpDidRoleDataFromRole(c, roleId1)
	insertDmpDidRoleDataFromRole(c, roleId2)
}

func insertDmpDidRoleDataFromAPI(c context.Context, destArrayPoint *[]BigdataDIDStu) {
	destArray := *destArrayPoint
	_, hh, mm, _ := utils.GetNowDDHHMMSS()

	roleId3 := []*DmpDidRoleData{}
	for _, item := range destArray {
		if item.LocalAppID == "10681" {
			roleId3 = append(roleId3, &DmpDidRoleData{
				RoleId:    3,
				DidMd5:    item.DIDMd5,
				Imei:      item.Imei,
				AndroidId: item.AndroidID,
				Oaid:      item.Oaid,
				Date:      time.Now(),
				Hour:      hh,
				Minute:    mm,
			})
		}
	}
	insertDmpDidRoleDataFromRole(c, roleId3)
}

type DmpDidRoleData struct {
	RoleId    int       `json:"role_id" db:"role_id"`
	DidMd5    string    `json:"did_md5" db:"did_md5"`
	Imei      string    `json:"imei" db:"imei"`
	AndroidId string    `json:"android_id" db:"android_id"`
	Oaid      string    `json:"oaid" db:"oaid"`
	Date      time.Time `json:"dd" db:"dd"`
	Hour      string    `json:"hh" db:"hh"`
	Minute    string    `json:"mm" db:"mm"`
}

func insertDmpDidRoleDataFromRole(c context.Context, didData []*DmpDidRoleData) {
	return
	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare(`
        insert into dmp_rawdata.did_role_data (
            role_id,
            did_md5,
            imei,
            android_id,
            oaid,
            dd,
            hh,
            mm
        ) values (
            ?,?,?,?,?,?,?,?
        )
    `)

	if stmt == nil {
		fmt.Println("bigdata err dmp did_req:", len(didData), err)
		return
	}

	defer stmt.Close()

	for _, item := range didData {
		if _, err := stmt.Exec(
			item.RoleId,
			item.DidMd5,
			item.Imei,
			item.AndroidId,
			item.Oaid,
			item.Date,
			item.Hour,
			item.Minute,
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}
}

func GoBigDataHoloDeviceDpi(mhReq *MHReq) {
	if utilities.SkipHologress {
		return
	}

	if mhReq.Device.DPI < 0.5 {
		return
	}

	dpi := strconv.FormatFloat(float64(mhReq.Device.DPI), 'f', 2, 32)

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("device", "dpi"))
	put.SetValWithTextByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetValWithTextByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetValWithTextByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetValWithTextByColName("osv", mhReq.Device.OsVersion, len(mhReq.Device.OsVersion))
	put.SetValWithTextByColName("dpi", dpi, len(dpi))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func GoBigDataHoloDeviceDictModel(mhReq *MHReq) {
	if utilities.SkipHologress {
		return
	}

	if mhReq.Device.DPI < 0.5 {
		return
	}

	dpi := strconv.FormatFloat(float64(mhReq.Device.DPI), 'f', 2, 32)

	regex, err := regexp.Compile(`Build/(.*?);`)
	if err != nil {
		fmt.Println(err)
		return
	}

	matches := regex.FindAllString(mhReq.Device.Ua, -1)
	if len(matches) == 0 {
		return
	}

	build := matches[0]

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("device", "dict_model"))
	put.SetValWithTextByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetValWithTextByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetValWithTextByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetValWithTextByColName("osv", mhReq.Device.OsVersion, len(mhReq.Device.OsVersion))
	put.SetValWithTextByColName("dpi", dpi, len(dpi))
	put.SetTextValByColName("ua", mhReq.Device.Ua, len(mhReq.Device.Ua))
	put.SetTextValByColName("build", build, len(build))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func GoBigDataHoloDebugeDpi(appId string, dpi float32) {
	if utilities.SkipHologress {
		return
	}

	dpiStr := strconv.FormatFloat(float64(dpi), 'f', 2, 32)

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("debug", "dpi_debug"))
	put.SetValWithTextByColName("app_id", appId, len(appId))
	put.SetValWithTextByColName("dpi", dpiStr, len(dpiStr))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func GoBigDataHoloDebugAppListData(localPos *LocalPosStu, mhReq *MHReq) {
	return
	if len(mhReq.Device.AppList) == 0 {
		return
	}
	if utilities.SkipHologress {
		return
	}

	var (
		appNames          string
		packageNames      string
		appNamesArray     []string
		packageNamesArray []string
	)
	appListMap := models.GetAppListMapGroup1()
	for _, appId := range mhReq.Device.AppList {
		if v, ok := appListMap[appId]; ok {
			appNamesArray = append(appNamesArray, v.AppName)
			if mhReq.Device.Os == "android" {
				packageNamesArray = append(packageNamesArray, v.AndroidPackageName)
			} else if mhReq.Device.Os == "ios" {
				packageNamesArray = append(packageNamesArray, v.IosPackageName)
			}
		}
	}

	if len(appNamesArray) == 0 || len(packageNamesArray) == 0 {
		return
	}
	appNames = strings.Join(appNamesArray, ",")
	packageNames = strings.Join(packageNamesArray, ",")

	// did md5
	if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		return
	}

	caidMulti := ""
	if len(mhReq.Device.CAIDMulti) > 0 {
		marshal, _ := json.Marshal(mhReq.Device.CAIDMulti)
		caidMulti = string(marshal)
	}

	if mhReq.Device.Os == "android" {
		var num int
		if strings.Contains(mhReq.Device.OsVersion, ".") {
			tmp := strings.Split(mhReq.Device.OsVersion, ".")
			if len(tmp) > 0 {
				num, _ = strconv.Atoi(tmp[0])
			}
		} else {
			num, _ = strconv.Atoi(mhReq.Device.OsVersion)
		}

		if num < 6 || num > 15 {
			return
		}
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("debug", "applist_data"))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("did_md5", mhReq.Device.DIDMd5, len(mhReq.Device.DIDMd5))
	put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetTextValByColName("osv", mhReq.Device.OsVersion, len(mhReq.Device.OsVersion))
	put.SetTextValByColName("imei", mhReq.Device.Imei, len(mhReq.Device.Imei))
	put.SetTextValByColName("imei_md5", mhReq.Device.ImeiMd5, len(mhReq.Device.ImeiMd5))
	put.SetTextValByColName("android_id", mhReq.Device.AndroidID, len(mhReq.Device.AndroidID))
	put.SetTextValByColName("android_id_md5", mhReq.Device.AndroidIDMd5, len(mhReq.Device.AndroidIDMd5))
	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
	put.SetTextValByColName("ua", mhReq.Device.Ua, len(mhReq.Device.Ua))
	put.SetTextValByColName("oaid", mhReq.Device.Oaid, len(mhReq.Device.Oaid))
	put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetInt32ValByColName("screen_width", int32(mhReq.Device.ScreenWidth))
	put.SetInt32ValByColName("screen_height", int32(mhReq.Device.ScreenHeight))
	put.SetTextValByColName("app_name", appNames, len(appNames))
	put.SetTextValByColName("package_name", packageNames, len(packageNames))

	put.SetTextValByColName("oaid_md5", mhReq.Device.Oaid, len(mhReq.Device.Oaid))
	put.SetTextValByColName("idfa", mhReq.Device.OaidMd5, len(mhReq.Device.OaidMd5))
	put.SetTextValByColName("idfa_md5", mhReq.Device.IdfaMd5, len(mhReq.Device.IdfaMd5))
	put.SetTextValByColName("caid_multi", caidMulti, len(caidMulti))

	lat := ""
	lng := ""
	gpsCountry := ""
	gpsProvince := ""
	gpsCity := ""
	dataSource := 0

	put.SetTextValByColName("lat", lat, len(lat))
	put.SetTextValByColName("lng", lng, len(lng))
	put.SetTextValByColName("gps_country", gpsCountry, len(gpsCountry))
	put.SetTextValByColName("gps_province", gpsProvince, len(gpsProvince))
	put.SetTextValByColName("gps_city", gpsCity, len(gpsCity))

	put.SetInt32ValByColName("data_source", int32(dataSource))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func GoBigDataHoloAppListDidData(mhReq *MHReq) {
	return
	if len(mhReq.Device.AppList) == 0 {
		return
	}
	if utilities.SkipHologress {
		return
	}

	// did md5
	if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		return
	}
	lat := ""
	lng := ""
	gpsCountry := ""
	gpsProvince := ""
	gpsCity := ""
	oaidMd5 := ""
	caid := ""
	caidVersion := ""

	if len(mhReq.Device.CAIDMulti) > 0 {
		caid = mhReq.Device.CAIDMulti[0].CAID
		caidVersion = mhReq.Device.CAIDMulti[0].CAIDVersion
	}

	caidMulti := ""
	if len(mhReq.Device.CAIDMulti) > 0 {
		marshal, _ := json.Marshal(mhReq.Device.CAIDMulti)
		caidMulti = string(marshal)
	}

	if mhReq.Device.Os == "android" {
		var num int
		if strings.Contains(mhReq.Device.OsVersion, ".") {
			tmp := strings.Split(mhReq.Device.OsVersion, ".")
			if len(tmp) > 0 {
				num, _ = strconv.Atoi(tmp[0])
			}
		} else {
			num, _ = strconv.Atoi(mhReq.Device.OsVersion)
		}

		if num < 6 || num > 15 {
			return
		}
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("device", "applist_did_data"))
	put.SetValWithTextByColName("did_md5", mhReq.Device.DIDMd5, len(mhReq.Device.DIDMd5))
	put.SetValWithTextByColName("imei", mhReq.Device.Imei, len(mhReq.Device.Imei))
	put.SetValWithTextByColName("imei_md5", mhReq.Device.ImeiMd5, len(mhReq.Device.ImeiMd5))
	put.SetValWithTextByColName("android_id", mhReq.Device.AndroidID, len(mhReq.Device.AndroidID))
	put.SetValWithTextByColName("android_id_md5", mhReq.Device.AndroidIDMd5, len(mhReq.Device.AndroidIDMd5))
	put.SetValWithTextByColName("oaid", mhReq.Device.Oaid, len(mhReq.Device.Oaid))
	put.SetValWithTextByColName("oaid_md5", oaidMd5, len(oaidMd5))
	put.SetValWithTextByColName("caid", caid, len(caid))
	put.SetValWithTextByColName("caid_version", caidVersion, len(caidVersion))
	put.SetValWithTextByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetValWithTextByColName("osv", mhReq.Device.OsVersion, len(mhReq.Device.OsVersion))
	put.SetValWithTextByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetValWithTextByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetValWithTextByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetValWithTextByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetValWithTextByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetValWithTextByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
	put.SetValWithTextByColName("idfa", mhReq.Device.Idfa, len(mhReq.Device.Idfa))
	put.SetValWithTextByColName("idfa_md5", mhReq.Device.IdfaMd5, len(mhReq.Device.IdfaMd5))
	put.SetValWithTextByColName("caid_multi", caidMulti, len(caidMulti))
	put.SetValWithTextByColName("lat", lat, len(lat))
	put.SetValWithTextByColName("lng", lng, len(lng))
	put.SetValWithTextByColName("gps_country", gpsCountry, len(gpsCountry))
	put.SetValWithTextByColName("gps_province", gpsProvince, len(gpsProvince))
	put.SetValWithTextByColName("gps_city", gpsCity, len(gpsCity))

	for key, value := range models.AppListMapping {
		isActive := 0
		for _, v := range mhReq.Device.AppList {
			if key == v {
				isActive = 1
				break
			}
		}
		put.SetInt32ValByColName(value, int32(isActive))
	}

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetValWithTextByColName("dd", day, len(day))
	put.SetValWithTextByColName("hh", hour, len(hour))
	put.SetValWithTextByColName("mm", minute, len(minute))
	put.SetInt64ValByColName("update_time", time.Now().Unix())

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHoloMaterialWinData(bigdataUID string, platformPos *PlatformPosStu, localPos *LocalPosStu, mhReq *MHReq, item MHRespDataItem) {
	if utilities.SkipHologress {
		return
	}

	isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0
	imageURL := ""
	videoURL := ""
	coverURL := ""
	lat := ""
	lng := ""
	gpsCountry := ""
	gpsProvince := ""
	gpsCity := ""
	adURL := ""
	downloadURL := ""
	landpageURL := ""
	deepLink := ""
	packageName := ""
	iconURL := ""
	isReplace := 0
	isEmpty := 0
	width := 0
	height := 0
	videoDuration := 0
	posType := 0
	ecpm := 0
	packageSize := ""
	demandCrid := ""
	supplyCrid := ""

	caidMulti := ""
	if len(mhReq.Device.CAIDMulti) > 0 {
		marshal, _ := json.Marshal(mhReq.Device.CAIDMulti)
		caidMulti = string(marshal)
	}

	if item.PackageSize > 0 {
		packageSize = strconv.FormatInt(item.PackageSize, 10)
	}

	if len(item.DemandCrid) > 0 {
		demandCrid = item.DemandCrid
	}

	if len(item.SupplyCrid) > 0 {
		supplyCrid = item.SupplyCrid
	}

	id := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("material", "win_data"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("reqid", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("demand_crid", demandCrid, len(demandCrid))
	put.SetTextValByColName("supply_crid", supplyCrid, len(supplyCrid))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
	put.SetTextValByColName("channel", platformPos.PlatformMediaID, len(platformPos.PlatformMediaID))
	put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
	put.SetTextValByColName("did_md5", mhReq.Device.AndroidIDMd5, len(mhReq.Device.AndroidIDMd5))
	put.SetTextValByColName("sdk_version", mhReq.SDKVersion, len(mhReq.SDKVersion))
	put.SetTextValByColName("lat", lat, len(lat))
	put.SetTextValByColName("lng", lng, len(lng))
	put.SetTextValByColName("gps_country", gpsCountry, len(gpsCountry))
	put.SetTextValByColName("gps_province", gpsProvince, len(gpsProvince))
	put.SetTextValByColName("gps_city", gpsCity, len(gpsCity))
	put.SetTextValByColName("title", item.Title, len(item.Title))
	put.SetTextValByColName("description", item.Description, len(item.Description))
	put.SetTextValByColName("adid", item.AdID, len(item.AdID))

	put.SetTextValByColName("imei", mhReq.Device.Imei, len(mhReq.Device.Imei))
	put.SetTextValByColName("imei_md5", mhReq.Device.ImeiMd5, len(mhReq.Device.ImeiMd5))
	put.SetTextValByColName("android_id", mhReq.Device.AndroidID, len(mhReq.Device.AndroidID))
	put.SetTextValByColName("android_id_md5", mhReq.Device.AndroidIDMd5, len(mhReq.Device.AndroidIDMd5))
	put.SetTextValByColName("oaid", mhReq.Device.Oaid, len(mhReq.Device.Oaid))
	put.SetTextValByColName("oaid_md5", mhReq.Device.OaidMd5, len(mhReq.Device.OaidMd5))
	put.SetTextValByColName("idfa", mhReq.Device.Idfa, len(mhReq.Device.Idfa))
	put.SetTextValByColName("idfa_md5", mhReq.Device.IdfaMd5, len(mhReq.Device.IdfaMd5))
	put.SetTextValByColName("publisher", item.Publisher, len(item.Publisher))
	put.SetTextValByColName("caid_multi", caidMulti, len(caidMulti))

	if isVideoAd {
		if item.Video != nil {
			videoURL = item.Video.VideoURL
			coverURL = item.Video.CoverURL
			width = item.Video.Width
			height = item.Video.Height
			videoDuration = item.Video.Duration
		}
	} else {
		if item.Image != nil {
			imageURL = item.Image[0].URL
			width = item.Image[0].Width
			height = item.Image[0].Height
		}
	}
	if localPos.LocalPosType > 0 {
		posType = localPos.LocalPosType
	}
	if len(item.AdURL) > 0 {
		adURL = item.AdURL
	}
	if len(item.DownloadURL) > 0 {
		downloadURL = item.DownloadURL
	}
	if len(item.LandpageURL) > 0 {
		landpageURL = item.LandpageURL
	}
	if len(item.DeepLink) > 0 {
		deepLink = item.DeepLink
	}
	if len(item.PackageName) > 0 {
		packageName = item.PackageName
	}
	if len(item.IconURL) > 0 {
		iconURL = item.IconURL
	}

	size := 1
	if width < height {
		size = 2
	}

	put.SetTextValByColName("image_url", imageURL, len(imageURL))
	put.SetTextValByColName("icon_url", iconURL, len(iconURL))
	put.SetTextValByColName("video_url", videoURL, len(videoURL))
	put.SetTextValByColName("cover_url", coverURL, len(coverURL))
	put.SetTextValByColName("ad_url", adURL, len(adURL))
	put.SetTextValByColName("download_url", downloadURL, len(downloadURL))
	put.SetTextValByColName("landpage_url", landpageURL, len(landpageURL))
	put.SetTextValByColName("deep_link", deepLink, len(deepLink))
	put.SetTextValByColName("package_name", packageName, len(packageName))
	if len(item.IsReplace) > 0 && strings.Contains(item.IsReplace, "1") {
		isReplace = 1
	}
	if len(item.IsEmpty) > 0 {
		isEmpty = 1
	}
	if item.Ecpm > 0 {
		ecpm = item.Ecpm
	}
	put.SetInt32ValByColName("is_replace", int32(isReplace))
	put.SetInt32ValByColName("is_fill", int32(isEmpty))
	put.SetInt32ValByColName("ecpm", int32(ecpm))
	put.SetInt32ValByColName("interact_type", int32(item.InteractType))
	put.SetInt32ValByColName("width", int32(width))
	put.SetInt32ValByColName("height", int32(height))
	put.SetInt32ValByColName("video_duration", int32(videoDuration))
	put.SetInt32ValByColName("pos_type", int32(posType))

	put.SetTextValByColName("app_name", item.AppName, len(item.AppName))
	put.SetTextValByColName("app_version", item.AppVersion, len(item.AppVersion))
	put.SetTextValByColName("privacy_url", item.PrivacyLink, len(item.PrivacyLink))
	put.SetTextValByColName("permission_url", item.PermissionURL, len(item.PermissionURL))
	put.SetTextValByColName("appinfo", item.AppInfo, len(item.AppInfo))
	put.SetTextValByColName("package_size", packageSize, len(packageSize))

	put.SetInt32ValByColName("size", int32(size))
	put.SetInt32ValByColName("corp_id", int32(platformPos.PlatformAppCorpID))
	put.SetInt32ValByColName("p_ecpm", int32(item.PEcpm))

	put.SetTextValByColName("market_url", item.MarketURL, len(item.MarketURL))
	put.SetTextValByColName("ks_user_id", "", 0)

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloMaterialDictQuery(bigdataUID string, platform *PlatformPosStu, item MHRespDataItem) {
	if utilities.SkipHologress {
		return
	}

	id := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("material", "dict_query"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("reqid", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("adid", item.AdID, len(item.AdID))
	put.SetTextValByColName("p_app_id", platform.PlatformAppID, len(platform.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platform.PlatformPosID, len(platform.PlatformPosID))
	put.SetTextValByColName("app_name", item.AppName, len(item.AppName))
	put.SetTextValByColName("package_name", item.PackageName, len(item.PackageName))
	put.SetInt32ValByColName("p_ecpm", int32(item.PEcpm))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)
	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugKsug(bigdataUID string, platformPos *PlatformPosStu, localPos *LocalPosStu, mhReq *MHReq, item *MHRespDataItem) {
	if utilities.SkipHologress {
		return
	}

	if platformPos.PlatformMediaID == "14" {
		isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0
		imageURL := ""
		videoURL := ""
		coverURL := ""
		lat := ""
		lng := ""
		gpsCountry := ""
		gpsProvince := ""
		gpsCity := ""
		adTrack := ""
		flag := 1

		if item.PackageName == "com.smile.gifmaker" || item.PackageName == "com.kuaishou.nebula" {
			var link []string
			for _, click := range item.ClickLink {
				if strings.Contains(click, "dsp.maplehaze.cn") {
					link = append(link, click)
					flag = 2
				}
			}

			for _, impression := range item.ImpressionLink {
				if strings.Contains(impression, "dsp.maplehaze.cn") {
					link = append(link, impression)
					flag = 2
				}
			}

			bytes, _ := json.Marshal(link)
			adTrack = string(bytes)
		}

		if flag == 1 {
			return
		}

		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "ksug_debug"))
		put.SetTextValByColName("uid", bigdataUID, len(bigdataUID))
		put.SetTextValByColName("demand_crid", item.DemandCrid, len(item.DemandCrid))
		put.SetTextValByColName("supply_crid", item.SupplyCrid, len(item.SupplyCrid))
		put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
		put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
		put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
		put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
		put.SetTextValByColName("channel", platformPos.PlatformMediaID, len(platformPos.PlatformMediaID))
		put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
		put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
		put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
		put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
		put.SetTextValByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
		put.SetTextValByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
		put.SetTextValByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
		put.SetTextValByColName("ad_track", adTrack, len(adTrack))
		put.SetTextValByColName("lat", lat, len(lat))
		put.SetTextValByColName("lng", lng, len(lng))
		put.SetTextValByColName("gps_country", gpsCountry, len(gpsCountry))
		put.SetTextValByColName("gps_province", gpsProvince, len(gpsProvince))
		put.SetTextValByColName("gps_city", gpsCity, len(gpsCity))
		put.SetTextValByColName("title", item.Title, len(item.Title))
		put.SetTextValByColName("description", item.Description, len(item.Description))
		if isVideoAd {
			videoURL = item.Video.VideoURL
			coverURL = item.Video.CoverURL
		} else {
			imageURL = item.Image[0].URL
		}
		put.SetTextValByColName("image_url", imageURL, len(imageURL))
		put.SetTextValByColName("icon_url", item.IconURL, len(item.IconURL))
		put.SetTextValByColName("video_url", videoURL, len(videoURL))
		put.SetTextValByColName("cover_url", coverURL, len(coverURL))
		put.SetTextValByColName("ad_url", item.AdURL, len(item.AdURL))
		put.SetTextValByColName("download_url", item.DownloadURL, len(item.DownloadURL))
		put.SetTextValByColName("landpage_url", item.LandpageURL, len(item.LandpageURL))
		put.SetTextValByColName("deep_link", item.DeepLink, len(item.DeepLink))
		put.SetTextValByColName("package_name", item.PackageName, len(item.PackageName))
		put.SetInt32ValByColName("ecpm", int32(item.Ecpm))
		put.SetInt32ValByColName("interact_type", int32(item.InteractType))

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPDataDb.Submit(put)
	}
}

func BigDataHoloDebugAnticheat(bigdataUID string, localPos *LocalPosStu, platformPos *PlatformPosStu,
	isReplace int, reqData string, replaceDID ReplaceDIDStu, mhReq *MHReq) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "anticheat_debug"))
	put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
	put.SetTextValByColName("req_data", reqData, len(reqData))
	put.SetInt32ValByColName("is_replace", int32(isReplace))
	if isReplace == 1 {
		put.SetTextValByColName("oaid", replaceDID.Oaid, len(replaceDID.Oaid))
	} else {
		put.SetTextValByColName("oaid", mhReq.Device.Oaid, len(mhReq.Device.Oaid))

	}
	connectTypeStr := ""
	if isReplace == 1 {
		if replaceDID.ConnectType == 0 {
			connectTypeStr = "未知"
		} else if replaceDID.ConnectType == 1 {
			connectTypeStr = "wifi"
		} else if replaceDID.ConnectType == 2 {
			connectTypeStr = "2G"
		} else if replaceDID.ConnectType == 3 {
			connectTypeStr = "3G"
		} else if replaceDID.ConnectType == 4 {
			connectTypeStr = "4G"
		} else if replaceDID.ConnectType == 7 {
			connectTypeStr = "5G"
		} else {
			connectTypeStr = "未知"
		}
	} else {
		if mhReq.Network.ConnectType == 0 {
			connectTypeStr = "未知"
		} else if mhReq.Network.ConnectType == 1 {
			connectTypeStr = "wifi"
		} else if mhReq.Network.ConnectType == 2 {
			connectTypeStr = "2G"
		} else if mhReq.Network.ConnectType == 3 {
			connectTypeStr = "3G"
		} else if mhReq.Network.ConnectType == 4 {
			connectTypeStr = "4G"
		} else if mhReq.Network.ConnectType == 7 {
			connectTypeStr = "5G"
		} else {
			connectTypeStr = "未知"
		}
	}

	put.SetTextValByColName("connect_type", connectTypeStr, len(connectTypeStr))

	carrierStr := ""
	if isReplace == 1 {
		if replaceDID.Carrier == 0 {
			carrierStr = "未知"
		} else if replaceDID.Carrier == 1 {
			carrierStr = "移动"
		} else if replaceDID.Carrier == 2 {
			carrierStr = "联通"
		} else if replaceDID.Carrier == 3 {
			carrierStr = "电信"
		} else {
			carrierStr = "未知"
		}
	} else {
		if mhReq.Network.Carrier == 0 {
			carrierStr = "未知"
		} else if mhReq.Network.Carrier == 1 {
			carrierStr = "移动"
		} else if mhReq.Network.Carrier == 2 {
			carrierStr = "联通"
		} else if mhReq.Network.Carrier == 3 {
			carrierStr = "电信"
		} else {
			carrierStr = "未知"
		}
	}

	put.SetTextValByColName("carrier", carrierStr, len(carrierStr))

	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugReplaceIP(bigdataUID string, localPos *LocalPosStu, platformPos *PlatformPosStu,
	mhReq *MHReq, didMd5 string, didMd5Replace string, deviceJson string, devicejsonReplace string) {
	if utilities.SkipHologress {
		return
	}

	if platformPos.PlatformAppID == "1207079506" || platformPos.PlatformAppID == "1207079507" {
	} else {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "debug_replace_ip"))
	put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
	put.SetTextValByColName("did_md5", didMd5, len(didMd5))
	put.SetTextValByColName("did_md5_replace", didMd5Replace, len(didMd5Replace))
	put.SetTextValByColName("device_json", deviceJson, len(deviceJson))
	put.SetTextValByColName("device_json_replace", devicejsonReplace, len(devicejsonReplace))

	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// func BigDataHoloDebugJson(bigdataUID string, localPos *LocalPosStu, platformPos *PlatformPosStu, json string) {
// 	if utilities.SkipHologress {
// 		return
// 	}

// 	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "json_debug"))
// 	put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
// 	put.SetTextValByColName("test", json, len(json))
// 	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
// 	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
// 	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
// 	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
// 	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

// 	db.GlbHologresAdxSSPDataDb.Submit(put)
// }

func BigDataHoloDebugKsBundle(bundle string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "ks_bundle_debug"))
	put.SetTextValByColName("bundle", bundle, len(bundle))

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugVivoBundle(tagid string, bundle string, price int) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "vivo_bundle_debug"))
	put.SetTextValByColName("tagid", tagid, len(tagid))
	put.SetTextValByColName("bundle", bundle, len(bundle))
	put.SetInt32ValByColName("price", int32(price))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHoloDebugBesBundle(tagid string, bundle string, os string, adBlockId string, adslotType string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "bes_bundle_debug"))
	put.SetTextValByColName("tag_id", tagid, len(tagid))
	put.SetTextValByColName("bundle", bundle, len(bundle))
	put.SetTextValByColName("os", os, len(os))
	put.SetTextValByColName("ad_block_id", adBlockId, len(adBlockId))
	put.SetTextValByColName("adslot_type", adslotType, len(adslotType))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHoloDebugKsTagid(tagid string, bundle string, os string, remark string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("debug", "ks_tagid"))
	put.SetTextValByColName("tagid", tagid, len(tagid))
	put.SetTextValByColName("bundle", bundle, len(bundle))
	put.SetTextValByColName("os", os, len(os))
	if len(remark) > 0 {
		put.SetTextValByColName("remark", remark, len(remark))
	}
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHoloSspDealStackDebug(errorInfo string, stackInfo string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "ssp_deal_stack"))
	put.SetTextValByColName("error_info", errorInfo, len(errorInfo))
	put.SetTextValByColName("stack_info", stackInfo, len(stackInfo))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloOppoDebug(oppoRespId string, adid string, reqId string, oppoPrice string, macroPrice string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "oppo_debug"))
	put.SetTextValByColName("oppo_resp_id", oppoRespId, len(oppoRespId))
	put.SetTextValByColName("adid", adid, len(adid))
	put.SetTextValByColName("req_id", reqId, len(reqId))
	put.SetTextValByColName("oppo_price", oppoPrice, len(oppoPrice))
	put.SetTextValByColName("macro_price", macroPrice, len(macroPrice))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloSdkAntiCheatV4(deviceData SdkAnticheatV4Model, didData SdkAnticheatV4DidModel) {
	if utilities.SkipHologress {
		return
	}

	var deviceInfo deviceidgeneratemodel.DeviceModel
	deviceInfo.Os = deviceData.Device.Os
	deviceInfo.OsVersion = deviceData.Device.OsVersion
	deviceInfo.Imei = didData.Imei
	deviceInfo.Oaid = didData.Oaid

	didMd5 := deviceidgenerate.GenerateDeviceID(deviceInfo)

	id := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("anticheat", "sdk_anticheat_data"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("did_md5", didMd5, len(didMd5))
	put.SetTextValByColName("sdk_version", deviceData.SDKVersion, len(deviceData.SDKVersion))
	put.SetTextValByColName("os_version", deviceData.Device.OsVersion, len(deviceData.Device.OsVersion))
	put.SetTextValByColName("app_id", deviceData.AppID, len(deviceData.AppID))
	put.SetTextValByColName("pos_id", deviceData.PosID, len(deviceData.PosID))
	put.SetTextValByColName("p_app_id", deviceData.PAppID, len(deviceData.PAppID))
	put.SetTextValByColName("p_pos_id", deviceData.PPosID, len(deviceData.PPosID))
	put.SetTextValByColName("channel", deviceData.Channel, len(deviceData.Channel))
	put.SetTextValByColName("model", deviceData.Model, len(deviceData.Model))
	put.SetTextValByColName("manufacturer", deviceData.Manufacturer, len(deviceData.Manufacturer))
	put.SetTextValByColName("dpi", deviceData.Device.Dpi, len(deviceData.Device.Dpi))
	put.SetTextValByColName("appstore_version", deviceData.Device.AppstoreVersion, len(deviceData.Device.AppstoreVersion))
	put.SetTextValByColName("hms_version", deviceData.Device.HmsVersion, len(deviceData.Device.HmsVersion))
	put.SetTextValByColName("boot_mark", deviceData.Device.BootMark, len(deviceData.Device.BootMark))
	put.SetTextValByColName("update_mark", deviceData.Device.UpdateMark, len(deviceData.Device.UpdateMark))
	put.SetTextValByColName("oaid_source", strconv.Itoa(deviceData.Device.OaidSource), len(strconv.Itoa(deviceData.Device.OaidSource)))
	put.SetTextValByColName("sd_free_space", deviceData.Device.SdFreeSpace, len(deviceData.Device.SdFreeSpace))
	put.SetTextValByColName("miui_version", deviceData.Device.MiuiVersion, len(deviceData.Device.MiuiVersion))

	put.SetInt32ValByColName("screen_width", int32(deviceData.Device.ScreenWidth))
	put.SetInt32ValByColName("screen_height", int32(deviceData.Device.ScreenHeight))
	put.SetInt32ValByColName("expose_times", int32(deviceData.ExposeTimes))
	put.SetInt32ValByColName("click_times", int32(deviceData.ClickTimes))
	put.SetInt32ValByColName("event", int32(deviceData.Event))

	put.SetTextValByColName("imei", didData.Imei, len(didData.Imei))
	put.SetTextValByColName("android_id", didData.AndroidId, len(didData.AndroidId))
	put.SetTextValByColName("oaid", didData.Oaid, len(didData.Oaid))
	put.SetTextValByColName("harmony_os_version", didData.HarmonyOsVersion, len(didData.HarmonyOsVersion))
	put.SetTextValByColName("harmony_os", strconv.Itoa(didData.HarmonyOs), len(strconv.Itoa(didData.HarmonyOs)))

	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHolovVvoPublisherDebug(publisher string) {
	if utilities.SkipHologress {
		return
	}

	uid := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "vivo_publisher_debug"))
	put.SetTextValByColName("id", uid, len(uid))
	if len(publisher) > 0 {
		put.SetTextValByColName("publisher", publisher, len(publisher))
	}
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
}

func BigDataHoloDebugJson2(bigdataUID string, json string, appId string, posId string, pAppId string, pPosId string) {
	if utilities.SkipHologress {
		return
	}

	//appId := "10807"
	//posId := "60180"
	//pAppId := "fl_ios_xs"
	//pPosId := "1870100034"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "json_debug"))
	put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("test", json, len(json))
	put.SetTextValByColName("app_id", appId, len(appId))
	put.SetTextValByColName("pos_id", posId, len(posId))
	put.SetTextValByColName("p_app_id", pAppId, len(pAppId))
	put.SetTextValByColName("p_pos_id", pPosId, len(pPosId))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugJson3(bigdataUID string, didmd5 string, appId string, posId string, pAppId string, pPosId string, osv string,
	model string, manufacturer string, ip string, ipcountry string, ipprovince string, ipcity string, test string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "demand_json_debug"))
	put.SetTextValByColName("id", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("app_id", appId, len(appId))
	put.SetTextValByColName("pos_id", posId, len(posId))
	put.SetTextValByColName("p_app_id", pAppId, len(pAppId))
	put.SetTextValByColName("p_pos_id", pPosId, len(pPosId))
	put.SetTextValByColName("didmd5", didmd5, len(didmd5))
	put.SetTextValByColName("osv", osv, len(osv))
	put.SetTextValByColName("model", model, len(model))
	put.SetTextValByColName("manufacturer", manufacturer, len(manufacturer))
	put.SetTextValByColName("ip", ip, len(ip))
	put.SetTextValByColName("ip_country", ipcountry, len(ipcountry))
	put.SetTextValByColName("ip_province", ipprovince, len(ipprovince))
	put.SetTextValByColName("ip_city", ipcity, len(ipcity))
	put.SetTextValByColName("test", test, len(test))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugKsAppList(appId string, appListStr string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "debug_ks_applist"))
	put.SetTextValByColName("app_id", appId, len(appId))
	put.SetTextValByColName("app_name", appListStr, len(appListStr))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHoloDebugSdkTrack(mhReq MHReq) {
	if utilities.SkipHologress {
		return
	}
	id := uuid.NewV4().String()
	media, _ := json.Marshal(mhReq.App)
	device, _ := json.Marshal(mhReq.Device)
	network, _ := json.Marshal(mhReq.Network)

	var finalPrice string
	if len(mhReq.Debug.FinalPrice) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.FinalPrice)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		finalPrice = string(decodePrice)
	}

	var posLogEp string
	if len(mhReq.Debug.PosLogEp) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.PosLogEp)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		posLogEp = string(decodePrice)
	}

	var posLogSp string
	if len(mhReq.Debug.PosLogSp) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.PosLogSp)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		posLogSp = string(decodePrice)
	}

	var serverAuctionPrice string
	if len(mhReq.Debug.ServerAuctionPrice) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.ServerAuctionPrice)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		serverAuctionPrice = string(decodePrice)
	}

	var clientPrice string
	if len(mhReq.Debug.ClientPrice) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.ClientPrice)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		clientPrice = string(decodePrice)
	}

	var deductingProfitsPrice string
	if len(mhReq.Debug.DeductingProfitsPrice) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.DeductingProfitsPrice)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		deductingProfitsPrice = string(decodePrice)
	}

	var lrl string
	if len(mhReq.Debug.Lrl) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.Lrl)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		lrl = string(decodePrice)
	}

	var originalPrice string
	if len(mhReq.Debug.OriginalPrice) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Debug.OriginalPrice)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		originalPrice = string(decodePrice)
	}

	var did string
	var reqDebugDid MHReqDebugDid
	if len(mhReq.DID) > 0 {
		decryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		decodePrice := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		_ = json.Unmarshal(decodePrice, &reqDebugDid)
		did = string(decodePrice)
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "sdk_track"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("sdk_version", mhReq.SDKVersion, len(mhReq.SDKVersion))
	put.SetTextValByColName("did", did, len(did))
	put.SetTextValByColName("media", string(media), len(string(media)))
	put.SetTextValByColName("device", string(device), len(string(device)))
	put.SetTextValByColName("network", string(network), len(string(network)))

	put.SetTextValByColName("app_id", mhReq.App.AppID, len(mhReq.App.AppID))
	put.SetTextValByColName("pos_id", mhReq.Debug.Posid, len(mhReq.Debug.Posid))
	put.SetTextValByColName("app_bundle_id", mhReq.App.AppBundleID, len(mhReq.App.AppBundleID))
	put.SetTextValByColName("oaid", reqDebugDid.Oaid, len(reqDebugDid.Oaid))
	put.SetTextValByColName("step", mhReq.Debug.Step, len(mhReq.Debug.Step))
	put.SetTextValByColName("post_type", mhReq.Debug.PostType, len(mhReq.Debug.PostType))
	put.SetTextValByColName("ad_type", mhReq.Debug.AdType, len(mhReq.Debug.AdType))
	put.SetTextValByColName("reqid", mhReq.Debug.Reqid, len(mhReq.Debug.Reqid))
	put.SetTextValByColName("local_reqid_adid", mhReq.Debug.LocalReqidAdid, len(mhReq.Debug.LocalReqidAdid))
	put.SetTextValByColName("ad_id", mhReq.Debug.AdId, len(mhReq.Debug.AdId))
	put.SetTextValByColName("ecpm_type", mhReq.Debug.EcpmType, len(mhReq.Debug.EcpmType))
	put.SetTextValByColName("reason", mhReq.Debug.Reason, len(mhReq.Debug.Reason))
	put.SetTextValByColName("date", mhReq.Debug.Date, len(mhReq.Debug.Date))
	put.SetTextValByColName("final_price", finalPrice, len(finalPrice))
	put.SetTextValByColName("pos_log_ep", posLogEp, len(posLogEp))
	put.SetTextValByColName("pos_log_sp", posLogSp, len(posLogSp))
	put.SetTextValByColName("original_price", originalPrice, len(originalPrice))
	put.SetTextValByColName("server_auction_price", serverAuctionPrice, len(serverAuctionPrice))
	put.SetTextValByColName("client_price", clientPrice, len(clientPrice))
	put.SetTextValByColName("deducting_profits_price", deductingProfitsPrice, len(deductingProfitsPrice))
	put.SetTextValByColName("lrl", lrl, len(lrl))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHolMaterialReplaceData(bigdataUID string, platformPos *PlatformPosStu, localPos *LocalPosStu, mhReq *MHReq, item *MHRespDataItem) {
	if utilities.SkipHologress {
		return
	}

	isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0
	imageURL := ""
	videoURL := ""
	coverURL := ""
	replaceImageURL := ""
	replaceVideoURL := ""
	replaceCoverURL := ""
	lat := ""
	lng := ""
	gpsCountry := ""
	gpsProvince := ""
	gpsCity := ""
	id := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("material", "replace_data"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("reqid", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("demand_crid", item.DemandCrid, len(item.DemandCrid))
	put.SetTextValByColName("supply_crid", item.SupplyCrid, len(item.SupplyCrid))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
	put.SetTextValByColName("channel", platformPos.PlatformMediaID, len(platformPos.PlatformMediaID))
	put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
	put.SetTextValByColName("lat", lat, len(lat))
	put.SetTextValByColName("lng", lng, len(lng))
	put.SetTextValByColName("gps_country", gpsCountry, len(gpsCountry))
	put.SetTextValByColName("gps_province", gpsProvince, len(gpsProvince))
	put.SetTextValByColName("gps_city", gpsCity, len(gpsCity))
	put.SetTextValByColName("title", item.Title, len(item.Title))
	put.SetTextValByColName("description", item.Description, len(item.Description))
	if isVideoAd {
		if item.Video != nil {
			videoURL = item.Video.VideoURL
			coverURL = item.Video.CoverURL
		}
		if item.ReplaceVideo != nil {
			replaceVideoURL = item.ReplaceVideo.VideoURL
			replaceCoverURL = item.ReplaceVideo.CoverURL
		}
	} else {
		if item.Image != nil {
			imageURL = item.Image[0].URL
		}
		if item.ReplaceImage != nil {
			replaceImageURL = item.ReplaceImage[0].URL
		}
	}
	put.SetTextValByColName("image_url", imageURL, len(imageURL))
	put.SetTextValByColName("icon_url", item.IconURL, len(item.IconURL))
	put.SetTextValByColName("video_url", videoURL, len(videoURL))
	put.SetTextValByColName("cover_url", coverURL, len(coverURL))
	put.SetTextValByColName("ad_url", item.AdURL, len(item.AdURL))
	put.SetTextValByColName("download_url", item.DownloadURL, len(item.DownloadURL))
	put.SetTextValByColName("landpage_url", item.LandpageURL, len(item.LandpageURL))
	put.SetTextValByColName("deep_link", item.DeepLink, len(item.DeepLink))
	put.SetTextValByColName("package_name", item.PackageName, len(item.PackageName))
	put.SetTextValByColName("event_code", item.IsReplace, len(item.IsReplace))
	put.SetInt32ValByColName("ecpm", int32(item.Ecpm))
	put.SetInt32ValByColName("interact_type", int32(item.InteractType))

	put.SetTextValByColName("replace_title", item.ReplaceTitle, len(item.ReplaceTitle))
	put.SetTextValByColName("replace_description", item.ReplaceDescription, len(item.ReplaceDescription))
	put.SetTextValByColName("replace_image_url", replaceImageURL, len(replaceImageURL))
	put.SetTextValByColName("replace_icon_url", item.ReplaceIconURL, len(item.ReplaceIconURL))
	put.SetTextValByColName("replace_video_url", replaceVideoURL, len(replaceVideoURL))
	put.SetTextValByColName("replace_cover_url", replaceCoverURL, len(replaceCoverURL))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func BigDataHolMaterialAllData(bigdataUID string, platformPos *PlatformPosStu, localPos *LocalPosStu, mhReq *MHReq, item *MHRespDataItem) {
	if utilities.SkipHologress {
		return
	}
	isVideoAd := item.Video != nil && len(item.Video.VideoURL) > 0
	imageURL := ""
	videoURL := ""
	coverURL := ""
	width := 0
	height := 0
	videoDuration := 0
	platformPosType := 0
	id := uuid.NewV4().String()
	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("material", "all_data"))
	put.SetTextValByColName("id", id, len(id))
	put.SetTextValByColName("reqid", bigdataUID, len(bigdataUID))
	put.SetTextValByColName("demand_crid", item.Crid, len(item.Crid))
	put.SetTextValByColName("supply_crid", item.SupplyCrid, len(item.SupplyCrid))
	put.SetTextValByColName("app_id", localPos.LocalAppID, len(localPos.LocalAppID))
	put.SetTextValByColName("pos_id", localPos.LocalPosID, len(localPos.LocalPosID))
	put.SetTextValByColName("p_app_id", platformPos.PlatformAppID, len(platformPos.PlatformAppID))
	put.SetTextValByColName("p_pos_id", platformPos.PlatformPosID, len(platformPos.PlatformPosID))
	put.SetTextValByColName("channel", platformPos.PlatformMediaID, len(platformPos.PlatformMediaID))
	put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
	put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
	put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
	put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
	put.SetTextValByColName("ip_country", mhReq.Device.LBSIPCountry, len(mhReq.Device.LBSIPCountry))
	put.SetTextValByColName("ip_province", mhReq.Device.LBSIPProvince, len(mhReq.Device.LBSIPProvince))
	put.SetTextValByColName("ip_city", mhReq.Device.LBSIPCity, len(mhReq.Device.LBSIPCity))
	put.SetTextValByColName("title", item.Title, len(item.Title))
	put.SetTextValByColName("description", item.Description, len(item.Description))
	if isVideoAd {
		if item.Video != nil {
			videoURL = item.Video.VideoURL
			coverURL = item.Video.CoverURL
			width = item.Video.Width
			height = item.Video.Height
			videoDuration = item.Video.Duration
		}
	} else {
		if item.Image != nil {
			imageURL = item.Image[0].URL
			width = item.Image[0].Width
			height = item.Image[0].Height
		}
	}
	if platformPos.PlatformPosType > 0 {
		platformPosType = platformPos.PlatformPosType
	}
	put.SetTextValByColName("image_url", imageURL, len(imageURL))
	put.SetTextValByColName("icon_url", item.IconURL, len(item.IconURL))
	put.SetTextValByColName("video_url", videoURL, len(videoURL))
	put.SetTextValByColName("cover_url", coverURL, len(coverURL))
	put.SetTextValByColName("ad_url", item.AdURL, len(item.AdURL))
	put.SetTextValByColName("download_url", item.DownloadURL, len(item.DownloadURL))
	put.SetTextValByColName("landpage_url", item.LandpageURL, len(item.LandpageURL))
	put.SetTextValByColName("deep_link", item.DeepLink, len(item.DeepLink))
	put.SetTextValByColName("package_name", item.PackageName, len(item.PackageName))

	put.SetInt32ValByColName("ecpm", int32(item.Ecpm))
	put.SetInt32ValByColName("interact_type", int32(item.InteractType))
	put.SetInt32ValByColName("width", int32(width))
	put.SetInt32ValByColName("height", int32(height))
	put.SetInt32ValByColName("video_duration", int32(videoDuration))
	put.SetInt32ValByColName("p_pos_type", int32(platformPosType))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)

}

// BigDataAudit ...
func BigDataAudit(c context.Context, bigdataUID string, reqRtbConfig *RtbConfigByTagIDStu, mhReq *MHReq, mhResp *MHResp, channel string) {

	bigdataAuditMutex.Lock()

	for _, item := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		imageURL := ""
		imageWidth := 0
		imageHeight := 0

		videoURL := ""
		coverURL := ""
		isVideoType := 0

		videoWidth := 0
		videoHeight := 0
		videoDuration := 0

		desc := item.Description
		iconURL := item.IconURL
		landpageURL := item.LandpageURL
		downloadURL := item.DownloadURL

		if item.Video != nil && len(item.Video.VideoURL) > 0 {
			isVideoType = 1
			coverURL = item.Video.CoverURL
			videoURL = item.Video.VideoURL
			videoWidth = item.Video.Width
			videoHeight = item.Video.Height
			videoDuration = item.Video.Duration
		} else {
			imageURL = item.Image[0].URL
			imageWidth = item.Image[0].Width
			imageHeight = item.Image[0].Height
		}

		md5Key := utils.GetMd5(imageURL)
		if isVideoType == 1 {
			md5Key = utils.GetMd5(videoURL)
		}

		// for tanx
		if channel == "21" {
			if strings.HasPrefix(imageURL, "http://") {
				imageURL = strings.Replace(imageURL, "http://", "https://", -1)
			}
			if strings.HasPrefix(videoURL, "http://") {
				videoURL = strings.Replace(videoURL, "http://", "https://", -1)
			}
			if strings.HasPrefix(coverURL, "http://") {
				coverURL = strings.Replace(coverURL, "http://", "https://", -1)
			}
			if strings.HasPrefix(iconURL, "http://") {
				iconURL = strings.Replace(iconURL, "http://", "https://", -1)
			}
			if strings.HasPrefix(landpageURL, "http://") {
				landpageURL = strings.Replace(landpageURL, "http://", "https://", -1)
			}
			if strings.HasPrefix(downloadURL, "http://") {
				downloadURL = strings.Replace(downloadURL, "http://", "https://", -1)
			}

			if isVideoType == 0 {
				// image_url是否添加.jpg
				isHasJPGSuffixKey := false
				if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
					tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
					for _, item := range tmpArrays {
						if strings.Contains(imageURL, item) {
							isHasJPGSuffixKey = true
						}
					}
				}

				if isHasJPGSuffixKey {
					if strings.HasSuffix(imageURL, ".jpg") || strings.HasSuffix(imageURL, ".jpeg") {
					} else {
						imageURL = imageURL + ".jpg"
					}
				}
			}

			// icon_url是否添加.png
			if len(iconURL) > 0 {
				isHasJPGSuffixKey := false
				if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
					tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
					for _, item := range tmpArrays {
						if strings.Contains(iconURL, item) {
							isHasJPGSuffixKey = true
						}
					}
				}
				if isHasJPGSuffixKey {
					if strings.HasSuffix(iconURL, ".png") || strings.HasSuffix(iconURL, ".jpg") || strings.HasSuffix(iconURL, ".jpeg") {
					} else {
						iconURL = iconURL + ".png"
					}
				}
			}

			md5Key = utils.GetMd5(imageURL + iconURL + reqRtbConfig.LocalPosID)
			if isVideoType == 1 {
				md5Key = utils.GetMd5(videoURL + iconURL + reqRtbConfig.LocalPosID)

				// // Tanx视频时长逻辑：优酷(10252、10253)>=5s，<=15s（hack appid 10252、10253），其他>=5s
				// if reqRtbConfig.LocalAppID == "10252" || reqRtbConfig.LocalAppID == "10253" {
				//  if item.Video.Duration > 15000 {
				//      continue
				//  }
				// }
				// if item.Video.Duration < 5000 {
				//  continue
				// }
			}
		}
		title := item.Title
		// for iqiyi
		if channel == "35" {
			// 爱奇艺临时改为仅dsp广告进行素材送审
			// isDSPAd := false
			// for _, item := range item.ImpressionLink {
			// 	if strings.Contains(item, "dsp.maplehaze.cn") {
			// 		isDSPAd = true
			// 		break
			// 	}
			// }
			// if isDSPAd {
			// } else {
			// 	continue
			// }

			if utf8.RuneCountInString(title) > 6 {
				titleRune := []rune(title)
				title = string(titleRune[0:6])
			}

			if isVideoType == 0 {
				// image_url是否添加.jpg
				isHasJPGSuffixKey := false
				if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
					tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
					for _, item := range tmpArrays {
						if strings.Contains(imageURL, item) {
							isHasJPGSuffixKey = true
						}
					}
				}

				if isHasJPGSuffixKey {
					if strings.HasSuffix(imageURL, ".jpg") || strings.HasSuffix(imageURL, ".jpeg") {
					} else {
						imageURL = imageURL + ".jpg"
					}
				}
			}

			// icon_url是否添加.png
			if len(iconURL) > 0 {
				isHasJPGSuffixKey := false
				if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
					tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
					for _, item := range tmpArrays {
						if strings.Contains(iconURL, item) {
							isHasJPGSuffixKey = true
						}
					}
				}
				if isHasJPGSuffixKey {
					if strings.HasSuffix(iconURL, ".png") || strings.HasSuffix(iconURL, ".jpg") || strings.HasSuffix(iconURL, ".jpeg") {
					} else {
						iconURL = iconURL + ".png"
					}
				}
			}
			// 爱奇艺触点贴片tagid­_type=98：
			// icon替换规则：
			// 1、dp链接中带有pinduoduo替换：https://static.maplehaze.cn/adimg/iqiyi/icon/pdd-icon-120X120.png
			// 2、dp链接中带有taobao替换：https://static.maplehaze.cn/adimg/iqiyi/icon/taobao-icon-120x120.png
			// 3、dp链接中带有jdmobile、jd.com、openjd替换：https://static.maplehaze.cn/adimg/iqiyi/icon/jd-icon-120X120.png
			// 4、非以上规则，均替换：https://static.maplehaze.cn/adimg/iqiyi/icon/default-icon-120X120.png
			if reqRtbConfig.TagIDType == "98" {
				if strings.Contains(item.DeepLink, "pinduoduo") {
					iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/pdd-icon-120X120.png"
				} else if strings.Contains(item.DeepLink, "taobao") {
					iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/taobao-icon-120x120.png"
				} else if strings.Contains(item.DeepLink, "jdmobile") || strings.Contains(item.DeepLink, "jd.com") || strings.Contains(item.DeepLink, "openjd") {
					iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/jd-icon-120X120.png"
				} else {
					iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/default-icon-120X120.png"
				}
			}

			md5Key = utils.GetMd5(imageURL + iconURL + reqRtbConfig.LocalPosID)
			if isVideoType == 1 {
				md5Key = utils.GetMd5(videoURL + iconURL + reqRtbConfig.LocalPosID)
			}

			if isVideoType == 1 {
				// // iqiyi 1,2,3,99 duration > 15s 不到送审模块, 也不填充
				// if reqRtbConfig.TagIDType == "1" || reqRtbConfig.TagIDType == "2" ||
				//  reqRtbConfig.TagIDType == "3" || reqRtbConfig.TagIDType == "99" {
				//  if item.Video.Duration > 15000 || item.Video.Duration < 5000 {
				//      continue
				//  }
				// } else if reqRtbConfig.TagIDType == "98" {
				//  // tagid_type=98：过滤小于15s和过滤大于60s的视频
				//  if item.Video.Duration < 15000 || item.Video.Duration > 60000 {
				//      continue
				//  }
				// }
			}
		}

		if channel == "46" {
			md5Key = utils.GetMd5(title + desc + imageURL + iconURL + reqRtbConfig.LocalPosID)
			if isVideoType == 1 {
				md5Key = utils.GetMd5(title + desc + videoURL + coverURL + iconURL + reqRtbConfig.LocalPosID)
			}
		}

		var bigdataAuditItem BigdataAuditStu
		bigdataAuditItem.UID = bigdataUID
		bigdataAuditItem.LocalAppID = reqRtbConfig.LocalAppID
		bigdataAuditItem.LocalPosID = reqRtbConfig.LocalPosID

		// get local info by app_id, pos_id
		localPosInfo := GetLocalPosInfo(c, reqRtbConfig.LocalPosID, reqRtbConfig.LocalAppID)
		if localPosInfo != nil {
			bigdataAuditItem.LocalPosType = localPosInfo.LocalPosType
			bigdataAuditItem.LocalPosWidth = localPosInfo.LocalPosWidth
			bigdataAuditItem.LocalPosHeight = localPosInfo.LocalPosHeight
		}

		bigdataAuditItem.TagID = reqRtbConfig.TagID
		bigdataAuditItem.OriginTagID = reqRtbConfig.OriginTagID
		if isVideoType == 0 {
			bigdataAuditItem.TemplateID = reqRtbConfig.ImageStyleID
		} else {
			bigdataAuditItem.TemplateID = reqRtbConfig.VideoStyleID
		}
		bigdataAuditItem.MediaGroup = channel
		bigdataAuditItem.ExtraTag = reqRtbConfig.ExtraTag
		bigdataAuditItem.Md5Key = md5Key
		bigdataAuditItem.CRID = item.Crid
		bigdataAuditItem.Title = title
		bigdataAuditItem.Description = item.Description
		bigdataAuditItem.ImageURL = imageURL
		bigdataAuditItem.ImageWidth = imageWidth
		bigdataAuditItem.ImageHeight = imageHeight
		bigdataAuditItem.VideoURL = videoURL
		bigdataAuditItem.CoverURL = coverURL
		bigdataAuditItem.VideoWidth = videoWidth
		bigdataAuditItem.VideoHeight = videoHeight
		bigdataAuditItem.VideoDuration = videoDuration
		bigdataAuditItem.LandpageURL = landpageURL
		bigdataAuditItem.DownloadURL = downloadURL
		bigdataAuditItem.OS = mhReq.Device.Os
		bigdataAuditItem.PackageName = item.PackageName
		bigdataAuditItem.DeepLink = item.DeepLink
		bigdataAuditItem.InteractType = item.InteractType
		bigdataAuditItem.IconURL = iconURL

		bigdataAuditArray = append(bigdataAuditArray, bigdataAuditItem)
	}

	if len(bigdataAuditArray) == 0 {
		bigdataAuditMutex.Unlock()
		return
	}

	if len(bigdataAuditArray) < config.ClickHouseAuditMaxNum && utils.GetCurrentSecond()-bigdataAuditTime < config.ClickHouseInterval {
		bigdataAuditMutex.Unlock()
		return
	}

	destAuditArray := bigdataAuditArray[0:]
	bigdataAuditArray = bigdataAuditArray[0:0]
	bigdataAuditMutex.Unlock()

	bigdataAuditTime = utils.GetCurrentSecond()

	BigDataAuditToClickHouse(destAuditArray)
}

// BigDataApiAudit ...
func BigDataApiAudit(c context.Context, bigdataUID string, localPos *LocalPosStu, mhReq *MHReq, mhResp *MHResp) {

	bigdataApiAuditMutex.Lock()

	for _, item := range mhResp.Data[localPos.LocalPosID].List {
		imageURL := ""
		imageWidth := 0
		imageHeight := 0

		videoURL := ""
		coverURL := ""
		isVideoType := 0

		videoWidth := 0
		videoHeight := 0
		videoDuration := 0

		iconURL := item.IconURL
		landpageURL := item.LandpageURL
		downloadURL := item.DownloadURL

		if item.Video != nil && len(item.Video.VideoURL) > 0 {
			isVideoType = 1
			coverURL = item.Video.CoverURL
			videoURL = item.Video.VideoURL
			videoWidth = item.Video.Width
			videoHeight = item.Video.Height
			videoDuration = item.Video.Duration
		} else {
			imageURL = item.Image[0].URL
			imageWidth = item.Image[0].Width
			imageHeight = item.Image[0].Height
		}

		md5Key := utils.GetMd5(imageURL)
		if isVideoType == 1 {
			md5Key = utils.GetMd5(videoURL)
		}

		title := item.Title

		var bigdataAuditItem BigdataAuditStu
		bigdataAuditItem.UID = bigdataUID
		bigdataAuditItem.LocalAppID = localPos.LocalAppID
		bigdataAuditItem.LocalPosID = localPos.LocalPosID

		bigdataAuditItem.LocalPosType = localPos.LocalPosType
		bigdataAuditItem.LocalPosWidth = localPos.LocalPosWidth
		bigdataAuditItem.LocalPosHeight = localPos.LocalPosHeight

		bigdataAuditItem.TagID = ""
		bigdataAuditItem.OriginTagID = ""
		bigdataAuditItem.TemplateID = ""
		bigdataAuditItem.MediaGroup = ""
		bigdataAuditItem.ExtraTag = ""
		bigdataAuditItem.Md5Key = md5Key
		bigdataAuditItem.CRID = item.Crid
		bigdataAuditItem.Title = title
		bigdataAuditItem.Description = item.Description
		bigdataAuditItem.ImageURL = imageURL
		bigdataAuditItem.ImageWidth = imageWidth
		bigdataAuditItem.ImageHeight = imageHeight
		bigdataAuditItem.VideoURL = videoURL
		bigdataAuditItem.CoverURL = coverURL
		bigdataAuditItem.VideoWidth = videoWidth
		bigdataAuditItem.VideoHeight = videoHeight
		bigdataAuditItem.VideoDuration = videoDuration
		bigdataAuditItem.LandpageURL = landpageURL
		bigdataAuditItem.DownloadURL = downloadURL
		bigdataAuditItem.OS = mhReq.Device.Os
		bigdataAuditItem.PackageName = item.PackageName
		bigdataAuditItem.DeepLink = item.DeepLink
		bigdataAuditItem.InteractType = item.InteractType
		bigdataAuditItem.IconURL = iconURL

		bigdataAuditArray = append(bigdataAuditArray, bigdataAuditItem)
	}

	if len(bigdataAuditArray) == 0 {
		bigdataApiAuditMutex.Unlock()
		return
	}

	if len(bigdataAuditArray) < config.ClickHouseAuditMaxNum && utils.GetCurrentSecond()-bigdataAuditTime < config.ClickHouseInterval {
		bigdataApiAuditMutex.Unlock()
		return
	}

	destAuditArray := bigdataAuditArray[0:]
	bigdataAuditArray = bigdataAuditArray[0:0]
	bigdataApiAuditMutex.Unlock()

	bigdataAuditTime = utils.GetCurrentSecond()

	BigDataAuditToClickHouse(destAuditArray)
}

// BigDataAuditToClickHouse ...
func BigDataAuditToClickHouse(destAuditArray []BigdataAuditStu) {

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, err := tx.Prepare("INSERT INTO ssp_rawdata.audit_data (uid, app_id, pos_id, pos_type, pos_width, pos_height, " +
		"tag_id, origin_tag_id, style_id, media_group, extra_tag, " +
		"md5_key, crid, title, description, image_url, image_width, image_height, video_url, cover_url, video_width, video_height, video_duration, " +
		"landpage_url, download_url, os, package_name, deeplink, interact_type, icon_url, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	if stmt == nil {
		fmt.Println("bigdata err audit:", len(destAuditArray), err)
		return
	}

	defer stmt.Close()

	for _, item := range destAuditArray {
		if _, err := stmt.Exec(
			item.UID,
			item.LocalAppID,
			item.LocalPosID,
			item.LocalPosType,
			item.LocalPosWidth,
			item.LocalPosHeight,
			item.TagID,
			item.OriginTagID,
			item.TemplateID,
			item.MediaGroup,
			item.ExtraTag,
			item.Md5Key,
			item.CRID,
			item.Title,
			item.Description,
			item.ImageURL,
			item.ImageWidth,
			item.ImageHeight,
			item.VideoURL,
			item.CoverURL,
			item.VideoWidth,
			item.VideoHeight,
			item.VideoDuration,
			item.LandpageURL,
			item.DownloadURL,
			item.OS,
			item.PackageName,
			item.DeepLink,
			item.InteractType,
			item.IconURL,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			fmt.Println(err)
		}
	}

	if err := tx.Commit(); err != nil {
		fmt.Println(err)
	}
}

// GoBigDataHoloDID ...
func GoBigDataHoloDID(c context.Context, didMD5 string, mhReq *MHReq, localPos *LocalPosStu) {
	// if true {
	//  return
	// }

	// 加前置写入使用概率10%
	if rand.Intn(10) == 0 {
		return
	}
	if localPos.LocalAppIsDIDStorageVerifyParameter == 1 {
		// 过滤
		if len(mhReq.Device.OsVersion) == 0 {
			return
		}
		if len(mhReq.Device.Model) == 0 {
			return
		}
		if len(mhReq.Device.Ua) == 0 {
			return
		}
	}

	// UA, 可信任model存入时不做判定
	// if strings.HasPrefix(mhReq.Device.Ua, "Mozilla") || strings.HasPrefix(mhReq.Device.Ua, "Dalvik") {
	// } else {
	//  return
	// }

	// TODO: error
	// isTrustedModel, _ := IsTrustedModel(c, strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
	// if isTrustedModel {
	//  // fmt.Println("kbg_debug_trusted_model_have: ", mhReq.Device.Model, localPos.LocalAppID, localPos.LocalPosID)
	// } else {
	//  // fmt.Println("kbg_debug_trusted_model_no: ", mhReq.Device.Model, localPos.LocalAppID, localPos.LocalPosID)
	//  DebugTrustedModelToBigData2(c, mhReq, localPos)
	//  return
	// }

	// if mhReq.Device.Os == "android" {
	//  // Mozilla/5.0 (Linux; Android 12; GIA-AN00 Build/HONORGIA-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.105 Mobile Safari/537.36
	//  // 12
	//  // GIA-AN00
	//  tmpOSV := mhReq.Device.OsVersion
	//  tmpModel := mhReq.Device.Model
	//  tmpUA := mhReq.Device.Ua
	//  if strings.Contains(tmpUA, "Android "+tmpOSV) && strings.Contains(strings.ToLower(tmpUA), strings.ToLower(tmpModel)) {
	//  } else {
	//      return
	//  }

	// } else if mhReq.Device.Os == "ios" {
	//  // Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
	//  // 16.4.1
	//  // iPhone15,3
	//  tmpOSV := mhReq.Device.OsVersion
	//  if strings.Count(tmpOSV, ".") >= 2 && strings.HasSuffix(tmpOSV, ".0") {
	//      tmpOSV = tmpOSV[:len(tmpOSV)-2]
	//  }

	//  tmpUA := mhReq.Device.Ua
	//  tmpUA = strings.Replace(tmpUA, "_", ".", -1)
	//  if strings.Contains(tmpUA, tmpOSV) {
	//  } else {
	//      return
	//  }
	// }

	// if strings.Contains(mhReq.Device.IP, ":") {
	//  return
	// }
	// if utils.IsInnerIP(mhReq.Device.IP) {
	//  return
	// }
	if localPos.LocalAppIsDIDStorageVerifyParameter == 1 {
		// 通过iplocation ip判定
		country := mhReq.Device.LBSIPCountry
		province := mhReq.Device.LBSIPProvince
		city := mhReq.Device.LBSIPCity
		if len(country) == 0 || len(province) == 0 || len(city) == 0 {
			return
		}
		if country != "中国" {
			return
		}
		if len(city) > 2 {
		} else {
			return
		}
	}

	var replaceDIDHoloData ReplaceDIDHoloStu
	if mhReq.Device.Os == "android" {
		imei := mhReq.Device.Imei
		imeiMd5 := mhReq.Device.ImeiMd5
		androidId := mhReq.Device.AndroidID
		androidIdMd5 := mhReq.Device.AndroidIDMd5
		oaid := mhReq.Device.Oaid

		if utils.VerifyDID(androidId, "android_id") == false {
			return
		}
		if utils.VerifyDID(androidIdMd5, "android_id_md5") == false {
			return
		}

		if imeiMd5 == utils.GetMd5("") {
			imeiMd5 = ""
		}

		if androidIdMd5 == utils.GetMd5("") {
			androidIdMd5 = ""
		}

		if localPos.LocalAppIsDIDStorageVerifyParameter == 1 {
			osvMajor := 0
			if len(mhReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(mhReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// fmt.Println(osvMajor)
			}
			if osvMajor < 10 {
				if len(imei) > 0 || len(imeiMd5) > 0 {
				} else {
					return
				}

				// 小于10验证imei
				if utils.VerifyDID(imei, "imei") == false {
					return
				}
				if utils.VerifyDID(imeiMd5, "imei_md5") == false {
					return
				}
				if len(imei) > 0 {
					if utils.IsImei(imei) == false {
						fmt.Println("wrong imei: " + imei)
						return
					}
				}
			} else {
				if len(oaid) > 0 {
				} else {
					return
				}

				// 大于10验证oaid
				if utils.VerifyDID(oaid, "oaid") == false {
					return
				}
			}
		}

		replaceDIDHoloData.Imei = imei
		replaceDIDHoloData.ImeiMd5 = imeiMd5
		replaceDIDHoloData.AndroidID = androidId
		replaceDIDHoloData.AndroidIDMd5 = androidIdMd5
		replaceDIDHoloData.Oaid = oaid
		replaceDIDHoloData.OsVersion = mhReq.Device.OsVersion
		replaceDIDHoloData.Model = mhReq.Device.Model
		replaceDIDHoloData.Manufacturer = mhReq.Device.Manufacturer
		replaceDIDHoloData.Ua = mhReq.Device.Ua
		replaceDIDHoloData.IP = mhReq.Device.IP
		replaceDIDHoloData.LBSIPCountry = mhReq.Device.LBSIPCountry
		replaceDIDHoloData.LBSIPProvince = mhReq.Device.LBSIPProvince
		replaceDIDHoloData.LBSIPCity = mhReq.Device.LBSIPCity
		replaceDIDHoloData.ID = utils.Get16Md5(localPos.LocalAppID + didMD5)
		replaceDIDHoloData.LocalAppID = localPos.LocalAppID
		replaceDIDHoloData.DIDMd5 = didMD5
		replaceDIDHoloData.Os = mhReq.Device.Os
		replaceDIDHoloData.DeviceType = mhReq.Device.DeviceType
		replaceDIDHoloData.ConnectType = mhReq.Network.ConnectType
		replaceDIDHoloData.Carrier = mhReq.Network.Carrier

		//////////////////////////////////////////////////////////////////////////////////
		saveReplaceAndroidDIDToHolo(c, []ReplaceDIDHoloStu{replaceDIDHoloData})
		//////////////////////////////////////////////////////////////////////////////////

	} else if mhReq.Device.Os == "ios" {
		idfa := mhReq.Device.Idfa
		idfaMd5 := mhReq.Device.IdfaMd5

		if utils.VerifyDID(idfa, "idfa") == false {
			return
		}
		if utils.VerifyDID(idfaMd5, "idfa_md5") == false {
			return
		}
		if idfaMd5 == utils.GetMd5("") {
			idfaMd5 = ""
		}
		if len(idfa) > 0 {
			idfaRn := []rune(idfa)
			if len(idfa) != 36 || string(idfaRn[8]) != "-" || string(idfaRn[13]) != "-" || string(idfaRn[18]) != "-" || string(idfaRn[23]) != "-" {
				return
			}
		}
		// if len(idfa) == 0 && len(idfaMd5) == 0 {
		//  return
		// }

		// replaceDIDHoloData.Idfa = idfa
		// replaceDIDHoloData.IdfaMd5 = idfaMd5
		replaceDIDHoloData.OsVersion = mhReq.Device.OsVersion
		replaceDIDHoloData.Model = mhReq.Device.Model
		replaceDIDHoloData.Manufacturer = "Apple"
		replaceDIDHoloData.Ua = mhReq.Device.Ua
		replaceDIDHoloData.IP = mhReq.Device.IP
		replaceDIDHoloData.LBSIPCountry = mhReq.Device.LBSIPCountry
		replaceDIDHoloData.LBSIPProvince = mhReq.Device.LBSIPProvince
		replaceDIDHoloData.LBSIPCity = mhReq.Device.LBSIPCity
		replaceDIDHoloData.ID = utils.Get16Md5(localPos.LocalAppID + didMD5)
		replaceDIDHoloData.LocalAppID = localPos.LocalAppID
		replaceDIDHoloData.DIDMd5 = didMD5
		replaceDIDHoloData.Os = mhReq.Device.Os
		replaceDIDHoloData.DeviceType = mhReq.Device.DeviceType
		replaceDIDHoloData.ConnectType = mhReq.Network.ConnectType
		replaceDIDHoloData.Carrier = mhReq.Network.Carrier

		if len(idfa) > 0 {
			replaceDIDHoloData.Idfa = idfa
		}

		if len(idfaMd5) > 0 && idfaMd5 != utils.GetMd5("") {
			replaceDIDHoloData.IdfaMd5 = idfaMd5
		}

		if len(mhReq.Device.CAIDMulti) > 0 {
			jsonData, _ := json.Marshal(mhReq.Device.CAIDMulti)
			replaceDIDHoloData.CAIDMulti = string(jsonData)
		}

		if len(mhReq.Device.DeviceStartSec) > 0 && len(mhReq.Device.Country) > 0 && len(mhReq.Device.Language) > 0 &&
			len(mhReq.Device.DeviceNameMd5) > 0 && len(mhReq.Device.HardwareMachine) > 0 && len(mhReq.Device.HardwareModel) > 0 &&
			len(mhReq.Device.PhysicalMemoryByte) > 0 && len(mhReq.Device.HarddiskSizeByte) > 0 && len(mhReq.Device.SystemUpdateSec) > 0 &&
			len(mhReq.Device.TimeZone) > 0 {

			replaceDIDHoloData.DeviceStartSec = mhReq.Device.DeviceStartSec
			replaceDIDHoloData.Country = mhReq.Device.Country
			replaceDIDHoloData.Language = mhReq.Device.Language
			replaceDIDHoloData.DeviceNameMd5 = mhReq.Device.DeviceNameMd5
			replaceDIDHoloData.HardwareMachine = mhReq.Device.HardwareMachine
			replaceDIDHoloData.HardwareModel = mhReq.Device.HardwareModel
			replaceDIDHoloData.PhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
			replaceDIDHoloData.HarddiskSizeByte = mhReq.Device.HarddiskSizeByte
			replaceDIDHoloData.SystemUpdateSec = mhReq.Device.SystemUpdateSec
			replaceDIDHoloData.TimeZone = mhReq.Device.TimeZone
			replaceDIDHoloData.CPUNum = utils.ConvertStringToInt(mhReq.Device.CPUNum)
		}

		if len(mhReq.Device.DeviceBirthSec) > 0 {
			replaceDIDHoloData.DeviceBirthSec = mhReq.Device.DeviceBirthSec
		}

		//////////////////////////////////////////////////////////////////////////////////
		saveReplaceiOSDIDToHolo(c, []ReplaceDIDHoloStu{replaceDIDHoloData})
		//////////////////////////////////////////////////////////////////////////////////
	}
}

// saveReplaceAndroidDIDToHolo ...
func saveReplaceAndroidDIDToHolo(c context.Context, replaceDIDHoloDataList []ReplaceDIDHoloStu) {
	if utilities.SkipHologress {
		return
	}

	tableName := "didlib_android"

	for _, item := range replaceDIDHoloDataList {
		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("magic", tableName))
		put.SetTextValByColName("app_id", item.LocalAppID, len(item.LocalAppID))
		put.SetTextValByColName("did_md5", item.DIDMd5, len(item.DIDMd5))
		put.SetTextValByColName("os", item.Os, len(item.Os))
		put.SetTextValByColName("osv", item.OsVersion, len(item.OsVersion))
		put.SetTextValByColName("imei", item.Imei, len(item.Imei))
		put.SetTextValByColName("imei_md5", item.ImeiMd5, len(item.ImeiMd5))
		put.SetTextValByColName("android_id", item.AndroidID, len(item.AndroidID))
		put.SetTextValByColName("android_id_md5", item.AndroidIDMd5, len(item.AndroidIDMd5))
		put.SetTextValByColName("ip", item.IP, len(item.IP))
		put.SetTextValByColName("ip_country", item.LBSIPCountry, len(item.LBSIPCountry))
		put.SetTextValByColName("ip_province", item.LBSIPProvince, len(item.LBSIPProvince))
		put.SetTextValByColName("ip_city", item.LBSIPCity, len(item.LBSIPCity))
		put.SetTextValByColName("ua", item.Ua, len(item.Ua))
		put.SetTextValByColName("oaid", item.Oaid, len(item.Oaid))
		put.SetTextValByColName("model", item.Model, len(item.Model))
		put.SetTextValByColName("manufacturer", item.Manufacturer, len(item.Manufacturer))
		put.SetInt32ValByColName("device_type", int32(item.DeviceType))
		put.SetInt32ValByColName("connect_type", int32(item.ConnectType))
		put.SetInt32ValByColName("carrier", int32(item.Carrier))

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
	}
}

// saveReplaceiOSDIDToHolo ...
func saveReplaceiOSDIDToHolo(c context.Context, replaceDIDHoloDataList []ReplaceDIDHoloStu) {
	if utilities.SkipHologress {
		return
	}

	tableName := "didlib_ios"

	for _, item := range replaceDIDHoloDataList {

		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataUpdateTableSchema("magic", tableName))
		put.SetTextValByColName("app_id", item.LocalAppID, len(item.LocalAppID))
		put.SetTextValByColName("did_md5", item.DIDMd5, len(item.DIDMd5))
		put.SetTextValByColName("os", item.Os, len(item.Os))
		put.SetTextValByColName("osv", item.OsVersion, len(item.OsVersion))
		put.SetTextValByColName("idfa", item.Idfa, len(item.Idfa))
		put.SetTextValByColName("idfa_md5", item.IdfaMd5, len(item.IdfaMd5))
		put.SetTextValByColName("caid_multi", item.CAIDMulti, len(item.CAIDMulti))
		put.SetTextValByColName("device_start_sec", item.DeviceStartSec, len(item.DeviceStartSec))
		put.SetTextValByColName("country", item.Country, len(item.Country))
		put.SetTextValByColName("language", item.Language, len(item.Language))
		put.SetTextValByColName("device_name_md5", item.DeviceNameMd5, len(item.DeviceNameMd5))
		put.SetTextValByColName("hardware_machine", item.HardwareMachine, len(item.HardwareMachine))
		put.SetTextValByColName("hardware_model", item.HardwareModel, len(item.HardwareModel))
		put.SetTextValByColName("physical_memory_byte", item.PhysicalMemoryByte, len(item.PhysicalMemoryByte))
		put.SetTextValByColName("harddisk_size_byte", item.HarddiskSizeByte, len(item.HarddiskSizeByte))
		put.SetTextValByColName("system_update_sec", item.SystemUpdateSec, len(item.SystemUpdateSec))
		put.SetTextValByColName("time_zone", item.TimeZone, len(item.TimeZone))
		put.SetTextValByColName("device_birth_sec", item.DeviceBirthSec, len(item.DeviceBirthSec))
		put.SetTextValByColName("ip", item.IP, len(item.IP))
		put.SetTextValByColName("ip_country", item.LBSIPCountry, len(item.LBSIPCountry))
		put.SetTextValByColName("ip_province", item.LBSIPProvince, len(item.LBSIPProvince))
		put.SetTextValByColName("ip_city", item.LBSIPCity, len(item.LBSIPCity))
		put.SetTextValByColName("ua", item.Ua, len(item.Ua))
		put.SetTextValByColName("model", item.Model, len(item.Model))
		put.SetTextValByColName("manufacturer", item.Manufacturer, len(item.Manufacturer))
		put.SetInt32ValByColName("device_type", int32(item.DeviceType))
		put.SetInt32ValByColName("connect_type", int32(item.ConnectType))
		put.SetInt32ValByColName("carrier", int32(item.Carrier))

		day := time.Now().Format("2006-01-02")
		hour := time.Now().Format("15")
		minute := time.Now().Format("04")
		put.SetTextValByColName("dd", day, len(day))
		put.SetTextValByColName("hh", hour, len(hour))
		put.SetTextValByColName("mm", minute, len(minute))
		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

		db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
	}
}

// SaveLimitIPErrorToHolo ...
func SaveLimitIPErrorToHolo(c context.Context, ip string, platformAppID string, redisValue int64, limitIPType int, isPass bool) {
	return

	if utilities.SkipHologress {
		return
	}

	tableName := "limit_ip"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("magic", tableName))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("p_app_id", platformAppID, len(platformAppID))
	put.SetInt32ValByColName("limit_ip_type", int32(limitIPType))
	put.SetTextValByColName("ip", ip, len(ip))
	put.SetInt64ValByColName("redis_value", redisValue)
	if isPass {
		put.SetInt32ValByColName("is_pass", 1)
	} else {
		put.SetInt32ValByColName("is_pass", 0)
	}

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// SaveLimitDIDErrorToHolo ...
func SaveLimitDIDErrorToHolo(c context.Context, didMd5 string, platformAppID string, redisValue int64, limitDIDType int, isPass bool) {
	return

	if utilities.SkipHologress {
		return
	}
	tableName := "limit_did"

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("magic", tableName))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("p_app_id", platformAppID, len(platformAppID))
	put.SetInt32ValByColName("limit_did_type", int32(limitDIDType))
	put.SetTextValByColName("did_md5", didMd5, len(didMd5))
	put.SetInt64ValByColName("redis_value", redisValue)
	if isPass {
		put.SetInt32ValByColName("is_pass", 1)
	} else {
		put.SetInt32ValByColName("is_pass", 0)
	}

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

// GoBigDataHoloDebugTimeout ...
func GoBigDataHoloDebugTimeout(rtbChannel string, localAppID string, localPosID string, platformAppID string, platformPosID string, costTime int) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "debug_timeout_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("rtb_channel", rtbChannel, len(rtbChannel))
	put.SetTextValByColName("app_id", localAppID, len(localAppID))
	put.SetTextValByColName("pos_id", localPosID, len(localPosID))
	put.SetTextValByColName("p_app_id", platformAppID, len(platformAppID))
	put.SetTextValByColName("p_pos_id", platformPosID, len(platformPosID))
	put.SetInt32ValByColName("cost_time", int32(costTime))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

type AppInfoStu struct {
	ID          string `json:"id,omitempty"`
	AppName     string `json:"app_name,omitempty"`
	PackageName string `json:"package_name,omitempty"`
	AppInfo     string `json:"app_info,omitempty"`
	AppInfoURL  string `json:"app_info_url,omitempty"`
}

// BigdataReqStu ...
type BigdataReqStu struct {
	UID             string
	LocalAppID      string
	LocalPosID      string
	PlatformAppID   string
	PlatformPosID   string
	Channel         string
	AppName         string
	AppBundle       string
	OS              string
	OSV             string
	DIDMd5          string
	Imei            string
	ImeiMd5         string
	AndroidID       string
	AndroidIDMd5    string
	Idfa            string
	IdfaMd5         string
	CAIDMulti       string
	ScreenWidth     int
	ScreenHeight    int
	IP              string
	UA              string
	Oaid            string
	Model           string
	Manufacturer    string
	AppStoreVersion string
	HMSCoreVersion  string
	DeviceType      string
	ConnectType     string
	Carrier         string
	DownReqNum      int
	DownRespNum     int
	DownCostTime    int
	UpReqTime       int
	UpReqNum        int
	UpRespTime      int
	UpRespNum       int
	UpRespOkNum     int
	UpCostTime      int
	Code            int
	InternalCode    int
	IsWin           int
	FloorPrice      int
	FinalPrice      int
	UpPrice         int
	UpRespFailedNum int
	BitCode         int
	UpRespCode      int
	// 价格上报个数
	UpPriceReportWinNum    int // 上游竞胜上传个数
	UpPriceReportFailedNum int // 上游竞败上传个数
	UpReportIDFA           int // 是否上报idfa
	UpReportCAID           int // 是否上报caid
	UpReportYinZi          int // 是否上报yinzi
}

// BigdataExpStu ...
type BigdataExpStu struct {
	UID             string
	AdID            string
	LocalAppID      string
	LocalPosID      string
	LocalAppType    string
	PlatformAppID   string
	PlatformPosID   string
	PlatformAppType string
	Channel         string
	AppName         string
	AppBundle       string
	OS              string
	OSV             string
	DIDMd5          string
	Imei            string
	ImeiMd5         string
	AndroidID       string
	AndroidIDMd5    string
	Idfa            string
	IdfaMd5         string
	CAIDMulti       string // 多个caid json
	IP              string
	UA              string
	Oaid            string
	OaidMd5         string
	Model           string
	Manufacturer    string
	FloorPrice      int
	FinalPrice      int
	Ecpm            int
	SupplyEcpm      int
	ExpTime         int64
	SDKVersion      string
	WrongOSVInUA    int
	WrongModelInUA  int
	MGDealID        string
	ConnectType     int
	Carrier         int
	// 填充时间
	DealTime int64
	// 创意id
	SupplyCRID string
	DemandCRID string
}

// BigdataClkStu ...
type BigdataClkStu struct {
	UID                     string
	AdID                    string
	LocalAppID              string
	LocalPosID              string
	LocalAppType            string
	PlatformAppID           string
	PlatformPosID           string
	PlatformAppType         string
	Channel                 string
	AppName                 string
	AppBundle               string
	OS                      string
	OSV                     string
	DIDMd5                  string
	Imei                    string
	ImeiMd5                 string
	AndroidID               string
	AndroidIDMd5            string
	Idfa                    string
	IdfaMd5                 string
	CAIDMulti               string // 多个caid json
	IP                      string
	UA                      string
	Oaid                    string
	OaidMd5                 string
	Model                   string
	Manufacturer            string
	DPI                     string
	XDPI                    string
	ReqWidth                string
	ReqHeight               string
	Width                   string
	Height                  string
	DownX                   string
	DownY                   string
	UpX                     string
	UpY                     string
	MaplehazeDownX          string
	MaplehazeDownY          string
	MaplehazeUpX            string
	MaplehazeUpY            string
	SLD                     string
	IsServerSLD             string
	IsServerReplaceXY       string
	IsServerRealReplaceXY   string
	ServerRealReplaceXYType string
	IsLogicPixel            string
	FloorPrice              int
	FinalPrice              int
	Ecpm                    int
	ClkTime                 int64
	SDKVersion              string
	WrongOSVInUA            int
	WrongModelInUA          int
	MGDealID                string
	IsSkipReport            string
	ConnectType             int
	Carrier                 int
	// 媒体app设置的点击坐标类型, 0 无, 1 物理坐标, 2 逻辑坐标
	ClickPointType string
	// 是否替换xy, 0为客户端替换, 1为服务端替换, 2为服务端替换(逻辑像素)
	ClickPointReplaceType string
	// 枫岚收到上报点击link数组, 替换__DOWN_X__, __DOWN_Y__, __UP_X__, __UP_Y__, __SLD__, 服务器端上报点击事件
	MaplehazeReportLink string
	// 填充时间
	DealTime int64
	// 创意id
	SupplyCRID string
	DemandCRID string
}

// BigdataMaterialStu ...
type BigdataMaterialStu struct {
	UID           string
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
	OS            string
	Manufacturer  string
	Title         string
	Description   string
	IconURL       string
	ImageURL      string
	VideoURL      string
	DownloadURL   string
	LandpageURL   string
	DeepLink      string
	PackageName   string
	Channel       string
	JSONResp      string
}

// BigdataPriceStu ...
type BigdataPriceStu struct {
	UID           string
	TagID         string
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
	BidPrice      int
	WinPrice      int
	LossPrice     int
	AdID          string
	MGDealID      string
}

// BigdataMismatchBundleStu ...
type BigdataMismatchBundleStu struct {
	UID        string
	LocalAppID string
	LocalPosID string
	Bundle     string
}

// BigdataRtbPriceStu ...
// type BigdataRtbPriceStu struct {
//  TagID string
//  Price int
// }

// BigdataDeepLinkStu ...
type BigdataDeepLinkStu struct {
	UID            string
	LocalAppID     string
	LocalPosID     string
	PlatformAppID  string
	PlatformPosID  string
	OS             string
	OSV            string
	Imei           string
	ImeiMd5        string
	AndroidID      string
	AndroidIDMd5   string
	Idfa           string
	IP             string
	UA             string
	Oaid           string
	Model          string
	Manufacturer   string
	DeepLinkResult string
	DeepLinkReason string
}

// BigdataClickXYStu ...
type BigdataClickXYStu struct {
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
	Md5Key        string
	LocalAppType  string // 1. SDK; 2. API; 3. SDK+API; 4. RTB; 5. ADX; 6. 商店
	PhysicalDownX int
	PhysicalDownY int
	PhysicalUpX   int
	PhysicalUpY   int
	LogicDownX    int
	LogicDownY    int
	LogicUpX      int
	LogicUpY      int
	PtDownX       int
	PtDownY       int
	PtUpX         int
	PtUpY         int
	DPI           float32
	Os            string
	IsLogicPixel  int
}

// BigdataDIDStu ...
type BigdataDIDStu struct {
	DIDMd5            string
	LocalAppID        string
	LocalPosID        string
	PlatformAppID     string
	PlatformPosID     string
	PlatformMediaID   string
	PlatformAppCorpID int
	IsUP              int
	Imei              string
	ImeiMd5           string
	AndroidID         string
	AndroidIDMd5      string
	Oaid              string
	Idfa              string
	IdfaMd5           string
	Caid              string
	CaidVersion       string
	Model             string
	Manufacturer      string
	ConnectType       int
	Carrier           int
	IP                string
	UA                string
	SDKVersion        string
	ReqTime           int64
	Os                string
	Osv               string
	HmsVersion        string
	AppstoreVersion   string
	// ios 11 因子
	DeviceStartSec     string
	Country            string
	Language           string
	DeviceNameMd5      string
	HardwareMachine    string
	HardwareModel      string
	PhysicalMemoryByte string
	HarddiskSizeByte   string
	SystemUpdateSec    string
	TimeZone           string
	CPUNum             int
}

// BigdataDIDStu ...
type BigdataDIDAppListStu struct {
	LocalAppID   string
	DIDMd5       string
	Imei         string
	AndroidID    string
	Oaid         string
	Model        string
	Manufacturer string
	AppList      string
	IP           string
	UA           string
	ScreenWidth  int
	ScreenHeight int
}

// BigdataWinPriceStu ...
type BigdataWinPriceStu struct {
	UID           string
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
	IsWin         int
	// Req           string
	// Resp          string
}

// BigdataTimeOutStu ...
type BigdataTimeOutStu struct {
	RTB        string
	LocalAppID string
	LocalPosID string
	Type       string
	TimeOut    int
}

// BigdataAuditStu ...
type BigdataAuditStu struct {
	UID            string
	LocalAppID     string
	LocalPosID     string
	LocalPosType   int
	LocalPosWidth  int
	LocalPosHeight int
	TagID          string
	OriginTagID    string
	TemplateID     string
	MediaGroup     string
	ExtraTag       string
	Md5Key         string
	CRID           string
	Title          string
	Description    string
	ImageURL       string
	ImageWidth     int
	ImageHeight    int
	VideoURL       string
	CoverURL       string
	VideoWidth     int
	VideoHeight    int
	VideoDuration  int
	LandpageURL    string
	DownloadURL    string
	OS             string
	PackageName    string
	DeepLink       string
	InteractType   int
	IconURL        string
}

// BigdataReplaceRedisKeyStu ...
type BigdataReplaceRedisKeyStu struct {
	LocalAppID      string
	ReplaceRedisKey string
}

// BigdataDebugStu ...
type BigdataDebugStu struct {
	UID           string
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
	OS            string
	OSV           string
	Oaid          string
	Model         string
	UA            string
	ReplaceOaid   string
	ReplaceOSV    string
	ReplaceModel  string
	ReplaceUA     string
}

// BigdataVideoTrackStu ...
type BigdataVideoTrackStu struct {
	UID           string
	LocalAppID    string
	LocalPosID    string
	PlatformAppID string
	PlatformPosID string
}

// 内部错误码
// 900000 默认无错误
// 900001 内部参数错误(未走到上游)
// 900002 内部达到req最大
// 900003 内部达到exp最大
// 900004 内部达到clk最大
// 900005 内部参数黑名单
// 900009 内部其他错误
// 900010 请求限频
// 900011 请求未命中替换包
// 900012 请求未命中人群包
// 900101 上游参数错误(上游验证参数错误, 未走到取广告)
// 900102 上游返回错误(不算超时, 网络)
// 900103 上游无填充
// 900104 上游填充价格不匹配(ks, jd)
// 900105 上游填充错误,譬如video url为空
// 900106 上游返回错误,超时错误
// 900107 上游返回错误,网络错误
// 900108 IP错误, 下游广告位里设置IP未在白名单或者在黑名单里
// // 900109 上游返回错误,大航海错误
// 900110 机型不对,譬如oppo, vivo广告传非oppo, vivo机型
// 900111 上游返回素材屏蔽, 标题关键字
// 900112 上游返回素材屏蔽, 描述关键字
// 900113 上游返回素材屏蔽, 域名
// 900114 上游返回素材屏蔽, 包名
// 900115 上游返回素材屏蔽, 素材
// 900116 上游返回素材屏蔽, 过滤下载类型素材
// 900117 上游请求过滤非法UA
// 900201 返回素材方向过滤
// 900202 返回素材分辨率过滤
// 900203 返回素材时长过滤

// bit code
// 1<<1 2 上游人群包总数
// 1<<2 4 替换包总数
// 1<<3 8 限频总数
// 1<<4 16 上游人群包通过总数
// 1<<5 32 替换包通过总数
// 1<<6 64 下游人群包总数
// 1<<7 128 下游人群包通过总数
// 1<<8 256 上游人群包未命中时强制通过比例通过总数
