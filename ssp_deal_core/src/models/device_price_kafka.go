package models

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"sync"
	"time"

	commonKafka "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/kafka"

	"github.com/segmentio/kafka-go"
)

var devicePriceKafkaProducer = kafka.Writer{
	Addr:     kafka.TCP(config.KafkaWriterConfig.Brokers...),
	Balancer: &kafka.Hash{},
	Topic:    commonKafka.TOPIC_SSP_DEAL_DEVICE_PRICE,
	Async:    true, // make the writer asynchronous
}

var (
	devicePriceBatchArray      []kafka.Message
	devicePriceBatchMutex      sync.Mutex
	devicePriceBatchTickerOnce sync.Once
)

const (
	devicePriceBatchSize    = 5000
	devicePriceBatchTimeout = 3 * time.Second
)

func startDevicePriceBatchTicker() {
	devicePriceBatchTickerOnce.Do(func() {
		ticker := time.NewTicker(devicePriceBatchTimeout)
		go func() {
			for range ticker.C {
				flushDevicePriceBatch()
			}
		}()
	})
}

func flushDevicePriceBatch() {
	devicePriceBatchMutex.Lock()
	batch := devicePriceBatchArray
	if len(batch) == 0 {
		devicePriceBatchMutex.Unlock()
		return
	}
	devicePriceBatchArray = nil
	devicePriceBatchMutex.Unlock()

	err := devicePriceKafkaProducer.WriteMessages(context.Background(), batch...)
	if err != nil {
		fmt.Printf("Kafka批量写入失败: %v\n", err)
	}
}

func DevicePirceRawDataKafka(ctx context.Context, mhReq *MHReq, localPos *LocalPosStu, mhUpResp *MHUpResp) {
	startDevicePriceBatchTicker()
	mhResp := mhUpResp.RespData
	mhRespCount := len(mhResp.Data[localPos.LocalPosID].List)
	if mhRespCount == 0 {
		return
	}

	var devicePirce DevicePirceRawData
	devicePirce.DidMd5 = mhReq.Device.DIDMd5
	devicePirce.Model = mhReq.Device.Model
	devicePirce.Manufacturer = mhReq.Device.Manufacturer
	devicePirce.Os = mhReq.Device.Os
	devicePirce.Osv = mhReq.Device.OsVersion
	devicePirce.Imei = mhReq.Device.Imei
	devicePirce.ImeiMd5 = mhReq.Device.ImeiMd5
	devicePirce.AndroidID = mhReq.Device.AndroidID
	devicePirce.AndroidIDMd5 = mhReq.Device.AndroidIDMd5
	devicePirce.Oaid = mhReq.Device.Oaid
	devicePirce.OaidMd5 = mhReq.Device.OaidMd5
	devicePirce.Idfa = mhReq.Device.Idfa
	devicePirce.IdfaMd5 = mhReq.Device.IdfaMd5
	devicePirce.CAIDMulti = mhReq.Device.CAIDMulti
	devicePirce.Channel = mhUpResp.Extra.PlatformPos.PlatformMediaID

	devicePirce.AppId = localPos.LocalAppID
	devicePirce.PosId = localPos.LocalPosID
	devicePirce.PAppId = mhUpResp.Extra.PlatformPos.PlatformAppID
	devicePirce.PPosId = mhUpResp.Extra.PlatformPos.PlatformPosID

	var messages []kafka.Message
	for _, item := range mhResp.Data[localPos.LocalPosID].List {
		devicePirce.PEcpm = item.PEcpm
		marshal, err := json.Marshal(devicePirce)
		if err != nil {
			continue
		}
		messages = append(messages, kafka.Message{
			Key:   []byte(devicePirce.DidMd5),
			Value: marshal,
		})
	}

	if len(messages) == 0 {
		return
	}

	devicePriceBatchMutex.Lock()
	devicePriceBatchArray = append(devicePriceBatchArray, messages...)
	if len(devicePriceBatchArray) >= devicePriceBatchSize {
		batch := devicePriceBatchArray
		devicePriceBatchArray = nil
		devicePriceBatchMutex.Unlock()
		err := devicePriceKafkaProducer.WriteMessages(context.Background(), batch...)
		if err != nil {
			fmt.Printf("Kafka批量写入失败: %v\n", err)
		}
		return
	}
	devicePriceBatchMutex.Unlock()
}

type DevicePirceRawData struct {
	PEcpm        int              `json:"p_ecpm"`
	DidMd5       string           `json:"did_md5"`
	AppId        string           `json:"app_id"`
	PosId        string           `json:"pos_id"`
	PAppId       string           `json:"p_app_id"`
	PPosId       string           `json:"p_pos_id"`
	Channel      string           `json:"channel"`
	Model        string           `json:"model"`
	Manufacturer string           `json:"manufacturer"`
	Os           string           `json:"os"`
	Osv          string           `json:"osv"`
	Imei         string           `json:"imei,omitempty"`
	ImeiMd5      string           `json:"imei_md5,omitempty"`
	AndroidID    string           `json:"android_id,omitempty"`
	AndroidIDMd5 string           `json:"android_id_md5,omitempty"`
	Oaid         string           `json:"oaid,omitempty"`
	OaidMd5      string           `json:"oaid_md5,omitempty"`
	Idfa         string           `json:"idfa,omitempty"`
	IdfaMd5      string           `json:"idfa_md5,omitempty"`
	CAIDMulti    []MHReqCAIDMulti `json:"caid_multi,omitempty"`
}
