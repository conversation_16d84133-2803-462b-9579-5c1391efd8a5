package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mh_proxy/db"
)

// CacheFromRedis ...
func CacheFromRedis(c context.Context, cacheKey string) (string, error) {

	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)

		redisValue, redisErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisErr != nil {
			// fmt.Println("redis error:", redisErr)
			db.GlbBigCacheMinute.Set(cacheKey, []byte(""))
		} else {
			cacheValue = []byte(redisValue)
			db.GlbBigCacheMinute.Set(cacheKey, []byte(redisValue))
		}
	}
	if len(cacheValue) > 0 {
		return string(cacheValue), nil

	}
	return "", errors.New("cache error")
}

// GetAppInfoFromRedis ...
func GetAppInfoFromRedis(c context.Context, appName string, packageName string) (*AppInfoStu, error) {

	if len(packageName) > 0 {
		appInfoRedisKey := "appinfo_" + packageName

		appInfoRedisValue, cacheErr := CacheFromRedis(c, appInfoRedisKey)
		if cacheErr != nil {
			// fmt.Println("redis error:", redisErr)
			// 未找到包名redis, 找"appinfo_" + "appinfo.maplehaze.cn"
			appInfoRedisKey = "appinfo_" + "appinfo.maplehaze.cn"
			appInfoRedisValue, cacheErr = CacheFromRedis(c, appInfoRedisKey)
			if cacheErr != nil {
				// fmt.Println("redis error:", redisErr)
			} else {
				var appInfoRedisData AppInfoStu
				json.Unmarshal([]byte(appInfoRedisValue), &appInfoRedisData)
				if len(appInfoRedisData.AppInfo) > 0 {
					if len(appName) > 0 {
						appInfoRedisData.AppInfo = appName + appInfoRedisData.AppInfo
					} else {
						appInfoRedisData.AppInfo = "本应用" + appInfoRedisData.AppInfo
					}
				}

				return &appInfoRedisData, nil
			}
		} else {
			var appInfoRedisData AppInfoStu
			json.Unmarshal([]byte(appInfoRedisValue), &appInfoRedisData)

			return &appInfoRedisData, nil
		}
	}

	return nil, errors.New("appinfo error")
}
