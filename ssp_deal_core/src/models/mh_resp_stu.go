package models

// MHUpResp ...
type MHUpResp struct {
	// RespData *map[string]interface{}
	RespData *MHResp
	Extra    MHUpRespExtra
}

// MHUpRespExtra ...
type MHUpRespExtra struct {
	// RespCount           int
	ExternalCode    int
	InternalCode    int
	PlatformAppID   string
	PlatformPosID   string
	RespCostTime    int64
	UpReqTime       int // 请求上游次数
	UpReqNum        int // 请求上游个数
	UpRespTime      int // 上游返回次数
	UpRespNum       int // 上游返回个数
	UpRespOKNum     int // 上游返回填充媒体个数
	FloorPrice      int // 设置的底价
	FinalPrice      int // 设置的成交价
	UpPrice         int // 上游返回的价格
	UpRespFailedNum int // 上游返回失败个数
	BitCode         int // bit code
	UpRespCode      int // 上游返回码
	// 价格上报个数
	UpPriceReportWinNum    int // 上游竞胜上传个数
	UpPriceReportFailedNum int // 上游竞败上传个数
	// extra
	BigdataUID    string
	DownCostTime  int64
	UpCostTime    int64
	IsWin         int
	UpReportIDFA  int
	UpReportCAID  int
	UpReportYinZi int
	// 上游配置
	PlatformPos *PlatformPosStu
	Category    *CategoryStu
}

// MHResp ...
type MHResp struct {
	Ret           int                    `json:"ret"`
	Msg           string                 `json:"msg"`
	Data          map[string]*MHRespData `json:"data,omitempty"`
	ClickMaxLimit int                    `json:"click_max_limit,omitempty"`
	ExpMaxLimit   int                    `json:"exp_max_limit,omitempty"`
	IsLogicPixel  int                    `json:"is_logic_pixel,omitempty"`
}

// MHRespData ...
type MHRespData struct {
	List []MHRespDataItem `json:"list"`
}

// MHRespDataItem ...
type MHRespDataItem struct {
	AdID                string             `json:"ad_id"`
	ImpressionLink      []string           `json:"impression_link"`
	ClickLink           []string           `json:"click_link"`
	InteractType        int                `json:"interact_type"`
	CrtType             int                `json:"crt_type"`
	Title               string             `json:"title,omitempty"`
	Description         string             `json:"description,omitempty"`
	Image               []MHRespImage      `json:"imgs,omitempty"`
	IconURL             string             `json:"icon_url,omitempty"`
	AppName             string             `json:"app_name,omitempty"`
	PackageName         string             `json:"package_name,omitempty"`
	DeepLink            string             `json:"deep_link,omitempty"`
	MarketURL           string             `json:"market_url,omitempty"`
	QuickAppLink        string             `json:"quick_app_link,omitempty"`
	AdURL               string             `json:"ad_url,omitempty"`
	DownloadURL         string             `json:"download_url,omitempty"`
	LandpageURL         string             `json:"landpage_url,omitempty"`
	Crid                string             `json:"crid,omitempty"`
	Video               *MHRespVideo       `json:"video,omitempty"`
	ConvTracks          []MHRespConvTracks `json:"conv_tracks,omitempty"`
	AppInfo             string             `json:"appinfo,omitempty"`        // 安卓应用介绍
	AppInfoURL          string             `json:"appinfo_url,omitempty"`    // 安卓应用介绍链接
	Publisher           string             `json:"publisher,omitempty"`      // 开发者名称
	AppVersion          string             `json:"app_version,omitempty"`    // app版本号
	PrivacyLink         string             `json:"privacy_url,omitempty"`    // 隐私链接
	Permission          string             `json:"permission,omitempty"`     // 权限
	PermissionURL       string             `json:"permission_url,omitempty"` // 权限链接
	PackageSize         int64              `json:"package_size,omitempty"`   // 应用大小, 单位Byte字节, 1GB=1024MB，1MB=1024KB，1KB=1024B
	Log                 string             `json:"log,omitempty"`
	MaterialDirection   int                `json:"material_direction"`
	AppstorePackageName string             `json:"appstore_package_name,omitempty"` // appstore bundle
	QuickAppStorePkg    string             `json:"quickapp_store_pkg,omitempty"`    // quicke store pkg
	ReqWidth            int                `json:"req_width"`
	ReqHeight           int                `json:"req_height"`
	UA                  string             `json:"ua,omitempty"`
	Ecpm                int                `json:"ecpm,omitempty"`

	// 安卓应用内部版本号
	AppVersionCode string `json:"app_version_code,omitempty"` // app版本号

	MarketDP int `json:"market_dp,omitempty"`

	// 点击链接里非标准点击坐标替换值
	ReplaceAdIdSimXYDefaultValue string `json:"replace_adid_sim_xy_default_value,omitempty"`

	// 内部上游ecpm, 不能外发
	PEcpm int `json:"p_ecpm,omitempty"`

	// win notice url
	WinNoticeURL string `json:"win_notice_url,omitempty"`

	// loss notice url
	LossNoticeURL string `json:"loss_notice_url,omitempty"`

	// 新增4个dsp返回
	FilterPackageName  []string `json:"filter_package_name"`
	InstallPackageName []string `json:"install_package_name"`
	MGAdEvent          int      `json:"mgad_event"`

	// 快手出价类型(cpm, cpc)
	KsPriceType string `json:"ks_price_type,omitempty"`
	// 快手点击率
	KsClickRate float64 `json:"ks_click_rate,omitempty"`

	DemandCrid         string        `json:"-"`
	SupplyCrid         string        `json:"-"`
	IsReplace          string        `json:"-"`
	IsEmpty            string        `json:"-"`
	ReplaceTitle       string        `json:"-"`
	ReplaceDescription string        `json:"-"`
	ReplaceIconURL     string        `json:"-"`
	ReplaceImage       []MHRespImage `json:"-"`
	ReplaceVideo       *MHRespVideo  `json:"-"`

	// 是否跳过点击监测
	IsSkipClick int `json:"is_skip_click,omitempty"`

	// sdk dp h5配置
	DPH5Config int `json:"dp_h5_cfg_value,omitempty"`

	// 上游上报竞价成功
	DemandWinNoticeURLs []string `json:"demand_win_notice_urls,omitempty"`
	// 上游上报竞价失败
	DemandLossNoticeURLs []string `json:"demand_loss_notice_urls,omitempty"`

	// sdk -> api 点击延迟上报时间
	IntervalTime int `json:"interval_time,omitempty"`

	// rtb媒体, 下发上游平台
	AdChannel string `json:"ad_channel,omitempty"`

	// sdk -> api下发字段, magic关闭键替换点击坐标概率, 配置概率假设15%，0-15 下发2，15-85下发1
	IsMagicCloseReplaceClick int `json:"is_magic_close_replace_click,omitempty"`

	// sdk 下载广告通知栏展示
	IsDownloadShowNotification int `json:"is_download_show_notification,omitempty"`
}

// MHRespImage ...
type MHRespImage struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

// MHRespVideo ...
type MHRespVideo struct {
	Duration     int                `json:"duration,omitempty"`
	Width        int                `json:"width,omitempty"`
	Height       int                `json:"height,omitempty"`
	VideoURL     string             `json:"video_url,omitempty"`
	CoverURL     string             `json:"cover_url,omitempty"`
	EventTracks  []MHEventTrackItem `json:"event_tracks,omitempty"`
	EndcardType  int                `json:"endcard_type"`
	SkipMinTime  int                `json:"skip_min_time"`
	EndcardRange int                `json:"endcard_range"`
}

// MHEventTrackItem ...
type MHEventTrackItem struct {
	EventType int      `json:"event_type,omitempty"`
	EventURLS []string `json:"event_track_urls,omitempty"`
}

// MHRespConvTracks ...
type MHRespConvTracks struct {
	ConvType int      `json:"conv_type,omitempty"`
	ConvURLS []string `json:"conv_urls,omitempty"`
}
