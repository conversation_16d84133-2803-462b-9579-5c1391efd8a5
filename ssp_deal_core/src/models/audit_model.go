package models

import (
	"encoding/json"
	"fmt"
	"mh_proxy/db"
	"strconv"
)

// GetYoukuVideoURLByMd5Key ...
func GetYoukuVideoURLByMd5Key(md5Key string, appID string) string {
	// fmt.Println("GetYoukuVideoURLByMd5Key")
	// fmt.Println(md5Key)
	// fmt.Println(appID)

	var videoURL string

	row := db.GlbMySQLDb.QueryRow("SELECT youku_video_url FROM audit_ad_list a where a.md5_key_youku = ? and a.app_id = ? and status = 0 and type = 2", md5Key, appID)
	err := row.Scan(&videoURL)
	if err != nil {
		// fmt.Println("get data failed, error:[%v]" + err.Error())
		return ""
	}
	return videoURL
}

// GetYoukuImageURLByMd5Key ...
func GetYoukuImageURLByMd5Key(md5Key string, appID string) string {
	// fmt.Println("GetYoukuImageURLByMd5Key")
	// fmt.Println(md5Key)
	// fmt.Println(appID)

	var imgURL string
	var maplehazeURL string

	row := db.GlbMySQLDb.QueryRow("SELECT a.img_url, IFNULL(a.maplehaze_img_url, '') FROM audit_ad_list a where a.md5_key_youku = ? and a.app_id = ? and status = 0 and type = 1", md5Key, appID)
	err := row.Scan(&imgURL, &maplehazeURL)
	if err != nil {
		// fmt.Println("get data failed, error:[%v]" + err.Error())
		return ""
	}
	if len(maplehazeURL) > 0 {
		return maplehazeURL + "?x-oss-process=image/resize,limit_0,m_fixed,w_1280,h_720"
	}
	return imgURL
}

// GetTanxByMd5Key ...
func GetTanxByMd5Key(md5Key string) (int, TanxAuditStu) {
	// fmt.Println("GetTanxByMd5Key, md5_key:", md5Key)
	cacheKey := "audit_tanx_md5key_" + md5Key
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)

	var tanxAuditData TanxAuditStu

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("cache value:", string(cacheValue))
		if len(cacheValue) == 0 {
			return 0, tanxAuditData
		}
		json.Unmarshal([]byte(cacheValue), &tanxAuditData)

		return 1, tanxAuditData
	}

	row := db.GlbMySQLDb.QueryRow("SELECT IFNULL(extra_tag, ''), IFNULL(image_url, ''), IFNULL(video_url, ''), IFNULL(youku_video_url, ''), IFNULL(status, 0) FROM tanx_audit_list a where a.md5_key_tanx = ? and status = 1", md5Key)
	err := row.Scan(&tanxAuditData.ExtraTag, &tanxAuditData.ImageURL, &tanxAuditData.VideoURL, &tanxAuditData.YouKuVideoURL, &tanxAuditData.Status)
	if err != nil {
		// fmt.Println("get data failed, error:[%v]" + err.Error())
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))

		return 0, tanxAuditData
	}

	// cache
	tmpJSON, _ := json.Marshal(tanxAuditData)

	db.GlbBigCacheMinute.Set(cacheKey, tmpJSON)

	return 1, tanxAuditData
}

// TanxAuditStu ...
type TanxAuditStu struct {
	ExtraTag      string
	ImageURL      string
	VideoURL      string
	YouKuVideoURL string
	Status        int
}

// GetIQiYiByMd5Key ...
func GetIQiYiByMd5Key(md5Key string) (int, string) {
	// fmt.Println("GetTanxByMd5Key, md5_key:", md5Key)
	cacheKey := "audit_iqiyi_md5key_" + md5Key
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("cache value:", string(cacheValue))
		if len(cacheValue) == 0 {
			return 0, ""
		}

		return 1, string(cacheValue)
	}

	var status int
	var iqiyiTvID string
	row := db.GlbMySQLDb.QueryRow("SELECT IFNULL(status, 0), IFNULL(iqiyi_tv_id, '') FROM iqiyi_audit_list a where a.md5_key_iqiyi = ? and status = 1", md5Key)
	err := row.Scan(&status, &iqiyiTvID)
	if err != nil {
		// fmt.Println("get data failed, error:[%v]" + err.Error())
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))

		return 0, ""
	}

	// cache
	db.GlbBigCacheMinute.Set(cacheKey, []byte(iqiyiTvID))
	return 1, iqiyiTvID
}

func GetUcByMd5Key(md5Key string) (bool, string) {
	cacheKey := "audit_uc_md5key_" + md5Key
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		if len(cacheValue) == 0 {
			return false, ""
		}
		return true, string(cacheValue)
	}

	var id int
	row := db.GlbMySQLDb.QueryRow("SELECT id FROM audit_uc_list a where a.md5key = ? and status = 1", md5Key)
	err := row.Scan(&id)
	if err != nil {
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))
		return false, ""
	}

	idStr := strconv.Itoa(id)
	db.GlbBigCacheMinute.Set(cacheKey, []byte(idStr))
	return true, idStr
}

// GetZhihuVideoIDByMd5Key ...
func GetZhihuVideoIDByMd5Key(md5Key string, appID string) string {
	fmt.Println("GetZhihuVideoIDByMd5Key")
	fmt.Println(md5Key)
	fmt.Println(appID)

	var videoID string

	row := db.GlbMySQLDb.QueryRow("SELECT IFNULL(a.zhihu_video_id, '') FROM audit_ad_list a where a.md5_key_zhihu = ? and a.app_id = ? and status = 0 and type = 2", md5Key, appID)
	err := row.Scan(&videoID)
	if err != nil {
		fmt.Println("get data failed, error:[%v]" + err.Error())
		return ""
	}
	return videoID
}

// GetMangoMaterialIDByKey ...
func GetMangoMaterialIDByKey(key string) string {
	fmt.Println("GetMangoMaterialIDByKey")
	var materialID string

	row := db.GlbMySQLDb.QueryRow("SELECT IFNULL(a.mango_material_id, '') FROM audit_mango_ad_list a where a.md5_material_url = ?", key)
	err := row.Scan(&materialID)
	if err != nil {
		fmt.Println("get mango data failed, error:[%v]" + err.Error())
		return ""
	}
	return materialID
}
