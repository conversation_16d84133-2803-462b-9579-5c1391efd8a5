package models

type AdShieldStu struct {
	ShieldRuleType int    `json:"shield_rule_type"`
	AdShieldType   int    `json:"ad_shield_type"`
	IsWhite        int    `json:"is_white"`
	ShieldKey      string `json:"shield_key"`
	PackageName    string `json:"package_name"`
	ExpUrl         string `json:"exp_url"`
	AdUrl          string `json:"ad_url"`
	MaterialUrl    string `json:"material_url"`
	PlatformIds    string `json:"platform_ids"`
	AppIds         string `json:"app_ids"`
	PosIds         string `json:"pos_ids"`
	TimeList       string `json:"time_list"`
	RegionList     string `json:"region_list"`
}

type AdShieldBigCache struct {
	ShieldRuleType int    `json:"shield_rule_type"`
	AdShieldType   int    `json:"ad_shield_type"`
	ShieldKey      string `json:"shield_key"`
	PackageName    string `json:"package_name"`
	ExpUrl         string `json:"exp_url"`
	AdUrl          string `json:"ad_url"`
	MaterialUrl    string `json:"material_url"`
	TimeList       string `json:"time_list"`
	RegionList     string `json:"region_list"`
}
