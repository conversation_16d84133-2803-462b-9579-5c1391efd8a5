package models

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/db"
)

// GetLocalPosInfo ...
func GetLocalPosInfo(c context.Context, localPosID string, localAppID string) *LocalPosStu {

	cacheKey := "go_local_" + localAppID + "_" + localPosID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	// fmt.Println(redisValue)
	// fmt.Println(redisErr)
	var localPos LocalPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &localPos)

		return &localPos
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// GetLocalAppExtraInfoByBundle ...
func GetLocalAppExtraInfoByBundle(c context.Context, appBundleID string) *LocalPosStu {

	cacheKey := "go_local_extra_info_by_bundle_" + appBundleID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	// fmt.Println(redisValue)
	// fmt.Println(redisErr)
	var localPos LocalPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &localPos)

		return &localPos
	}

	redisValue, redisErr := db.GlbRedis.Get(c, cacheKey).Result()
	if redisErr != nil {
		// fmt.Println("redis error:", redisErr)
	} else {
		// fmt.Println("redis value:", redisValue)
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisValue))

		if len(redisValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(redisValue), &localPos)

		return &localPos
	}

	row := db.GlbMySQLDb.QueryRow("SELECT a.id, a.app_bundle_id, "+
		"IFNULL(a.sp_extra_clipboard, 0), IFNULL(a.sp_extra_notification, 0), IFNULL(a.sp_extra_wakeup, 0) "+
		"FROM app a where a.app_bundle_id = ? and a.type = 1", appBundleID)
	err := row.Scan(&localPos.LocalAppID, &localPos.LocalAppBundleID,
		&localPos.LocalAppSupportExtraClipboard, &localPos.LocalAppSupportExtraNotification, &localPos.LocalAppSupportExtraWakeUp)
	if err != nil {
		fmt.Println("get local app data failed, error:[%v]" + err.Error())
		fmt.Println(appBundleID)
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))
		db.GlbRedis.Set(c, cacheKey, "", config.RedisKeyConfigTTL).Err()

		return nil
	}

	localPosJSON, _ := json.Marshal(localPos)

	db.GlbBigCacheMinute.Set(cacheKey, localPosJSON)
	db.GlbRedis.Set(c, cacheKey, localPosJSON, config.RedisKeyConfigTTL).Err()

	// fmt.Println(redisErr)

	return &localPos
}

// GetLocalAppInfoByBundle ...
func GetLocalAppExtraInfoByAppID(c context.Context, localAppID string) *LocalPosStu {

	cacheKey := "go_local_extra_info_by_app_id_" + localAppID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	// fmt.Println(redisValue)
	// fmt.Println(redisErr)
	var localPos LocalPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &localPos)

		return &localPos
	}

	redisValue, redisErr := db.GlbRedis.Get(c, cacheKey).Result()
	if redisErr != nil {
		// fmt.Println("redis error:", redisErr)
	} else {
		// fmt.Println("redis value:", redisValue)
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisValue))

		if len(redisValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(redisValue), &localPos)

		return &localPos
	}

	row := db.GlbMySQLDb.QueryRow("SELECT a.id, a.app_bundle_id, "+
		"IFNULL(a.sp_extra_clipboard, 0), IFNULL(a.sp_extra_notification, 0), IFNULL(a.sp_extra_wakeup, 0) "+
		"FROM app a where a.id = ? and a.type = 1", localAppID)
	err := row.Scan(&localPos.LocalAppID, &localPos.LocalAppBundleID,
		&localPos.LocalAppSupportExtraClipboard, &localPos.LocalAppSupportExtraNotification, &localPos.LocalAppSupportExtraWakeUp)
	if err != nil {
		fmt.Println("get local app data failed, error:[%v]" + err.Error())
		fmt.Println(localAppID)
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))
		db.GlbRedis.Set(c, cacheKey, "", config.RedisKeyConfigTTL).Err()

		return nil
	}

	localPosJSON, _ := json.Marshal(localPos)

	db.GlbBigCacheMinute.Set(cacheKey, localPosJSON)
	db.GlbRedis.Set(c, cacheKey, localPosJSON, config.RedisKeyConfigTTL).Err()

	// fmt.Println(redisErr)

	return &localPos
}

// GetLocalAppInfoByAppID ...
func GetLocalAppInfoByAppID(c context.Context, localAppID string) *LocalAppStu {

	cacheKey := "go_local_" + localAppID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	// fmt.Println(redisValue)
	// fmt.Println(redisErr)
	var localApp LocalAppStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &localApp)

		return &localApp
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// LocalAppStu ...
type LocalAppStu struct {
	LocalAppID               string `json:"app_id,omitempty"`
	LocalAppIsGetImei        int    `json:"is_get_imei"`
	LocalAppIsGetOaid        int    `json:"is_get_oaid"`
	LocalAppIsGetAndroidID   int    `json:"is_get_android_id"`
	LocalAppIsGetGPS         int    `json:"is_get_gps"`
	LocalAppIsGetAppList     int    `json:"is_get_applist"`
	LocalAppIsSilenceMonitor int    `json:"is_silence_monitor"`
	// 是否下发给sdk
	LocalAppIsUploadPackageNames int `json:"is_pkgs"`
	// 不下发给sdk
	LocalAppUploadPackageNames string `json:"upload_package_names,omitempty"`
	// 下发加密的包名
	LocalAppUploadEncryptionPackageNames string `json:"pkgs"`
	// 三方SDK初始化权限等级
	ExtSdkInitPermissions int `json:"ext_sdk_init_permissions"`

	// 三方监测链接
	LocalAppIsThirdReqMonitor int `json:"is_third_req_monitor,omitempty"`
	// 媒体全请求监测链接
	LocalAppThirdReqMonitor string `json:"third_req_monitor,omitempty"`
	// 媒体有效请求监测链接
	LocalAppThirdValidReqMonitor string `json:"third_valid_req_monitor,omitempty"`
	// 三方监测链接
	LocalAppThirdValidPosIDs string `json:"third_valid_pos_ids,omitempty"`
	// SDK客户端崩溃熔断
	LocalAppIsCrashProtect int `json:"is_crash_protect,omitempty"`
	// SDK客户端崩溃上报
	LocalAppIsUploadCrash int `json:"is_upload_crash,omitempty"`

	// 是否上报boot id, 0 不上报, 1 上报
	LocalAppIsBootID int `json:"LocalAppIsBootID,omitempty"`

	// 上报boot id黑厂商list
	LocalAppBootIDBlackManufacturerList string `json:"LocalAppBootIDBlackManufacturerList,omitempty"`
}

// LocalPosStu ...
type LocalPosStu struct {
	LocalPosID                       string
	LocalAppID                       string
	LocalPosWidth                    int
	LocalPosHeight                   int
	LocalPosPinDuoDuoStyleIDs        string `json:"LocalPosPinDuoDuoStyleIDs,omitempty"` // localpos拼多多样式id, 优先使用下游拼多多样式id
	LocalPosType                     int
	LocalOs                          string
	LocalAppName                     string
	LocalAppBundleID                 string
	LocalAppType                     string // 1. SDK; 2. API; 3. SDK+API; 4. RTB; 5. ADX; 6. 商店
	LocalAppSupportAntiCheatH5       int
	LocalAppSplashClickRegion        int `json:"LocalAppSplashClickRegion,omitempty"`        // 支持开屏点击区域
	LocalAppNativeDownloadCompliance int `json:"LocalAppNativeDownloadCompliance,omitempty"` // 支持原生下载合规
	// LocalAppRewardCompliance         int `json:"LocalAppRewardCompliance,omitempty"`         // 支持激励视频合规
	// LocalAppRewardSkipTime           int `json:"LocalAppRewardSkipTime,omitempty"`           // 激励视频跳过时间
	LocalPosIsRetainActive           int `json:"LocalPosIsRetainActive,omitempty"` // api设置保留流量总开关是否开关
	LocalPosIsActive                 int
	LocalPosMHLandPage               int    // 是否用maplehaze落地页
	LocalPosDownloadType             int    // 下载路径类型: 0 普通下载, 1 商店页下载(需媒体支持), 2 服务端转商店页下载(未实现)
	LocalPosTimeOut                  int    // api超时时间
	LocalPosSDKTimeOut               int    // sdk超时时间
	LocalPosMaterialType             int    // 素材类型: 0 图片, 1 视频, 2图片+视频
	LocalAppSupportExtraClipboard    int    // 是否支持剪贴板
	LocalAppExtraClipboardType       int    `json:"LocalAppExtraClipboardType,omitempty"` // 支持剪贴板类型: 0 不支持, 1 请求, 2 曝光, 3 点击
	LocalAppSupportExtraNotification int    // 是否支持通知
	LocalAppSupportExtraWakeUp       int    // 是否支持唤醒
	LocalAppIsLenovoSecureDownload   int    // 是否联想机型安全下载(仅下载类广告)
	LocalAppIsLimitFrequency         int    `json:"LocalAppIsLimitFrequency,omitempty"`   // 是否限频
	LocalAppLimitFrequencyJson       string `json:"LocalAppLimitFrequencyJson,omitempty"` // 限频配置
	LocalAppIsExtraLimitFrequency    int    `json:"LocalAppIsExtraLimitFrequency,omitempty"`
	LocalAppExtraLimitFrequency      string `json:"LocalAppExtraLimitFrequency,omitempty"`
	LocalAppIsReportPriceLoss        int    `json:"LocalAppIsReportPriceLoss,omitempty"`
	LocalAppIsNeedAudit              int    `json:"LocalAppIsNeedAudit,omitempty"`
	LocalPosIsNeedAudit              int    `json:"LocalPosIsNeedAudit,omitempty"`
	LocalPosIsTraceReport            int    `json:"LocalPosIsTraceReport,omitempty"`
	LocalAppIsVerifyPackage          int    `json:"LocalAppIsVerifyPackage,omitempty"`
	// LocalAppIsLogicPixel             int    `json:"LocalAppIsLogicPixel,omitempty"`
	// LocalAppLogicPixelList           string `json:"LocalAppLogicPixelList,omitempty"`
	// LocalAppIsClickLimit             int    `json:"LocalAppIsClickLimit,omitempty"`   // 点击上报去重
	// LocalAppClickLimitList           string `json:"LocalAppClickLimitList,omitempty"` // 点击上报去重广告源
	LocalAppIsVerifyParam int `json:"LocalAppIsVerifyParam,omitempty"` // 是否验证媒体参数
	// 素材过滤
	LocalPosIsMaterialFilterOrientation   int    `json:"LocalPosIsMaterialFilterOrientation,omitempty"`   // 是否素材过滤方向, 0 不过滤, 1 仅横向, 2 仅竖向
	LocalPosIsMaterialFilterDuration      int    `json:"LocalPosIsMaterialFilterDuration,omitempty"`      // 是否素材过滤时长
	LocalPosMaterialFilterMinDuration     string `json:"LocalPosMaterialFilterMinDuration,omitempty"`     // 素材过滤最小时长
	LocalPosMaterialFilterMaxDuration     string `json:"LocalPosMaterialFilterMaxDuration,omitempty"`     // 素材过滤最大时长
	LocalPosIsMaterialFilterFixedDuration int    `json:"LocalPosIsMaterialFilterFixedDuration,omitempty"` // 是否素材过滤固定时长
	LocalPosMaterialFilterFixedDurations  string `json:"LocalPosMaterialFilterFixedDurations,omitempty"`  // 素材过滤固定时长值
	LocalPosMaterialFilterSizes           string `json:"LocalPosMaterialFilterSizes,omitempty"`           // 素材过滤固定素材尺寸
	// local app shield rule
	LocalAppShieldIsActive    int    `json:"LocalAppShieldIsActive,omitempty"`
	LocalAppShieldRuleID      string `json:"LocalAppShieldRuleID,omitempty"`
	LocalAppShieldKey         string `json:"LocalAppShieldKey,omitempty"`
	LocalAppShieldPackageName string `json:"LocalAppShieldPackageName,omitempty"`
	LocalAppShieldAdURL       string `json:"LocalAppShieldAdURL,omitempty"`
	LocalAppShieldMaterialURL string `json:"LocalAppShieldMaterialURL,omitempty"`
	LocalAppShieldExpUrl      string `json:"LocalAppShieldExpUrl,omitempty"`
	LocalAppShieldRuleType    int    `json:"LocalAppShieldRuleType,omitempty"`
	LocalAppAdShieldType      int    `json:"LocalAppAdShieldType,omitempty"`
	LocalAppShieldTimeList    string `json:"LocalAppShieldTimeList,omitempty"`
	LocalAppShieldRegionList  string `json:"LocalAppShieldRegionList,omitempty"`
	// local pos shield rule
	LocalPosShieldIsActive    int    `json:"LocalPosShieldIsActive,omitempty"`
	LocalPosShieldRuleID      string `json:"LocalPosShieldRuleID,omitempty"`
	LocalPosShieldKey         string `json:"LocalPosShieldKey,omitempty"`
	LocalPosShieldPackageName string `json:"LocalPosShieldPackageName,omitempty"`
	LocalPosShieldAdURL       string `json:"LocalPosShieldAdURL,omitempty"`
	LocalPosShieldMaterialURL string `json:"LocalPosShieldMaterialURL,omitempty"`
	LocalPosShieldExpUrl      string `json:"LocalPosShieldExpUrl,omitempty"`
	LocalPosShieldRuleType    int    `json:"LocalPosShieldRuleType,omitempty"`
	LocalPosAdShieldType      int    `json:"LocalPosAdShieldType,omitempty"`
	LocalPosShieldTimeList    string `json:"LocalPosShieldTimeList,omitempty"`
	LocalPosShieldRegionList  string `json:"LocalPosShieldRegionList,omitempty"`
	// local app shield white rule
	LocalAppShieldWhiteIsActive    int    `json:"LocalAppShieldWhiteIsActive,omitempty"`
	LocalAppShieldWhiteRuleID      string `json:"LocalAppShieldWhiteRuleID,omitempty"`
	LocalAppShieldWhiteKey         string `json:"LocalAppShieldWhiteKey,omitempty"`
	LocalAppShieldWhitePackageName string `json:"LocalAppShieldWhitePackageName,omitempty"`
	LocalAppShieldWhiteAdURL       string `json:"LocalAppShieldWhiteAdURL,omitempty"`
	LocalAppShieldWhiteMaterialURL string `json:"LocalAppShieldWhiteMaterialURL,omitempty"`
	LocalAppShieldWhiteExpUrl      string `json:"LocalAppShieldWhiteExpUrl,omitempty"`
	LocalAppShieldWhiteRuleType    int    `json:"LocalAppShieldWhiteRuleType,omitempty"`
	LocalAppAdShieldWhiteType      int    `json:"LocalAppAdShieldWhiteType,omitempty"`
	LocalAppShieldWhiteTimeList    string `json:"LocalAppShieldWhiteTimeList,omitempty"`
	LocalAppShieldWhiteRegionList  string `json:"LocalAppShieldWhiteRegionList,omitempty"`
	// local pos shield white rule
	LocalPosShieldWhiteIsActive    int    `json:"LocalPosShieldWhiteIsActive,omitempty"`
	LocalPosShieldWhiteRuleID      string `json:"LocalPosShieldWhiteRuleID,omitempty"`
	LocalPosShieldWhiteKey         string `json:"LocalPosShieldWhiteKey,omitempty"`
	LocalPosShieldWhitePackageName string `json:"LocalPosShieldWhitePackageName,omitempty"`
	LocalPosShieldWhiteAdURL       string `json:"LocalPosShieldWhiteAdURL,omitempty"`
	LocalPosShieldWhiteMaterialURL string `json:"LocalPosShieldWhiteMaterialURL,omitempty"`
	LocalPosShieldWhiteExpUrl      string `json:"LocalPosShieldWhiteExpUrl,omitempty"`
	LocalPosShieldWhiteRuleType    int    `json:"LocalPosShieldWhiteRuleType,omitempty"`
	LocalPosAdShieldWhiteType      int    `json:"LocalPosAdShieldWhiteType,omitempty"`
	LocalPosShieldWhiteTimeList    string `json:"LocalPosShieldWhiteTimeList,omitempty"`
	LocalPosShieldWhiteRegionList  string `json:"LocalPosShieldWhiteRegionList,omitempty"`
	// local pos 云控选项
	LocalPosInteractionConfigID     string `json:"LocalPosInteractionConfigID,omitempty"`
	LocalPosInteractionTypeListJson string `json:"LocalPosInteractionTypeListJson,omitempty"`
	LocalPosYaoDirection            int    `json:"LocalPosYaoDirection,omitempty"`
	LocalPosSahuaConfigListJson     string `json:"LocalPosSahuaConfigListJson,omitempty"`
	LocalPosSahuaImageTriggerTime   string `json:"LocalPosSahuaImageTriggerTime,omitempty"`
	LocalPosSahuaVideoTriggerTime   string `json:"LocalPosSahuaVideoTriggerTime,omitempty"`
	LocalPosSahuaImageDurationTime  string `json:"LocalPosSahuaImageDurationTime,omitempty"`
	LocalPosSahuaVideoDurationTime  string `json:"LocalPosSahuaVideoDurationTime,omitempty"`
	LocalPosIsDownloadDialog        int    `json:"LocalPosIsDownloadDialog,omitempty"`
	LocalPosIsAutoPlayMobileNetwork int    `json:"LocalPosIsAutoPlayMobileNetwork,omitempty"`
	// local pos 云控选项, 是否点击区域下载合规弹窗
	LocalPosIsClickViewDownloadCompliance int `json:"LocalPosIsClickViewDownloadCompliance,omitempty"`
	// local pos 云控选项, 是否非点击区域下载合规弹窗
	LocalPosIsAdViewDownloadCompliance int `json:"LocalPosIsAdViewDownloadCompliance,omitempty"`
	// local pos 云控选项, 是否摇一摇后台关闭传感器
	LocalPosIsYaoBgDisableSensor int `json:"LocalPosIsYaoBgDisableSensor,omitempty"`
	// local pos 云控选项, 是否激励视频退出弹窗
	LocalPosIsRewardVideoExitConfirm int `json:"LocalPosIsRewardVideoExitConfirm,omitempty"`
	// local pos 云控选项, 样式配置 0 无 1 撒花 2 宝箱
	LocalPosInteractionStyle int `json:"LocalPosInteractionStyle,omitempty"`
	// local pos 云控选项, 宝箱配置
	LocalPosTreasurechestConfigListJson string `json:"LocalPosTreasurechestConfigListJson,omitempty"`
	// local pos 云控选项, magic开关 is_magic 0 关 1 开
	LocalPosIsMagic int `json:"LocalPosIsMagic,omitempty"`
	// local pos 云控选项, magic_type 1 全部, 2 下载, 0 非下载(默认)
	LocalPosMagicType int `json:"LocalPosMagicType,omitempty"`
	// local pos 云控选项, magic配置
	LocalPosMagicConfigListJson string `json:"LocalPosMagicConfigListJson,omitempty"`
	// local pos 云控选项, 激励视频样式 reward_video_type 0 旧版 1 新版
	LocalPosRewardVideoType int `json:"LocalPosRewardVideoType,omitempty"`
	// local pos 云控选项, 激励视频点击手势引导 0 关闭 1 开启
	LocalPosRewardVideoClkGuide int `json:"LocalPosRewardVideoClkGuide,omitempty"`
	// local pos 云控选项, magic关闭键替换点击坐标概率
	LocalPosMagicCloseReplaceClickWeight int `json:"LocalPosMagicCloseReplaceClickWeight,omitempty"`
	// local pos 云控选项, 滑动触发方向, 0 向上(默认), 1 所有
	LocalPosSlideDirection int `json:"LocalPosSlideDirection,omitempty"`
	// local pos 云控选项, magic区域, 0 关闭(默认), 1 全屏
	LocalPosMagicRegion int `json:"LocalPosMagicRegion,omitempty"`
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 以下为new 云控选项
	////////////////////////////////////////////////////////////////////////////////////////////////////
	LocalPosNewInteractionTypeListJson string `json:"LocalPosNewInteractionTypeListJson,omitempty"`
	LocalPosNewYaoDirection            int    `json:"LocalPosNewYaoDirection,omitempty"`
	LocalPosNewSahuaConfigListJson     string `json:"LocalPosNewSahuaConfigListJson,omitempty"`
	LocalPosNewSahuaImageTriggerTime   string `json:"LocalPosNewSahuaImageTriggerTime,omitempty"`
	LocalPosNewSahuaVideoTriggerTime   string `json:"LocalPosNewSahuaVideoTriggerTime,omitempty"`
	LocalPosNewSahuaImageDurationTime  string `json:"LocalPosNewSahuaImageDurationTime,omitempty"`
	LocalPosNewSahuaVideoDurationTime  string `json:"LocalPosNewSahuaVideoDurationTime,omitempty"`
	LocalPosNewIsDownloadDialog        int    `json:"LocalPosNewIsDownloadDialog,omitempty"`
	LocalPosNewIsAutoPlayMobileNetwork int    `json:"LocalPosNewIsAutoPlayMobileNetwork,omitempty"`
	// local pos 云控选项, 是否点击区域下载合规弹窗
	LocalPosNewIsClickViewDownloadCompliance int `json:"LocalPosNewIsClickViewDownloadCompliance,omitempty"`
	// local pos 云控选项, 是否非点击区域下载合规弹窗
	LocalPosNewIsAdViewDownloadCompliance int `json:"LocalPosNewIsAdViewDownloadCompliance,omitempty"`
	// local pos 云控选项, 是否摇一摇后台关闭传感器
	LocalPosNewIsYaoBgDisableSensor int `json:"LocalPosNewIsYaoBgDisableSensor,omitempty"`
	// local pos 云控选项, 是否激励视频退出弹窗
	LocalPosNewIsRewardVideoExitConfirm int `json:"LocalPosNewIsRewardVideoExitConfirm,omitempty"`
	// local pos 云控选项, 样式配置 0 无 1 撒花 2 宝箱
	LocalPosNewInteractionStyle int `json:"LocalPosNewInteractionStyle,omitempty"`
	// local pos 云控选项, 宝箱配置
	LocalPosNewTreasurechestConfigListJson string `json:"LocalPosNewTreasurechestConfigListJson,omitempty"`
	// local pos 云控选项, magic开关 is_magic 0 关 1 开
	LocalPosNewIsMagic int `json:"LocalPosNewIsMagic,omitempty"`
	// local pos 云控选项, magic_type 1 全部, 2 下载, 0 非下载(默认)
	LocalPosNewMagicType int `json:"LocalPosNewMagicType,omitempty"`
	// local pos 云控选项, magic配置
	LocalPosNewMagicConfigListJson string `json:"LocalPosNewMagicConfigListJson,omitempty"`
	// local pos 云控选项, 激励视频样式 reward_video_type 0 旧版 1 新版
	LocalPosNewRewardVideoType int `json:"LocalPosNewRewardVideoType,omitempty"`
	// local pos 云控选项, 激励视频点击手势引导 0 关闭 1 开启
	LocalPosNewRewardVideoClkGuide int `json:"LocalPosNewRewardVideoClkGuide,omitempty"`
	// local pos 云控选项, magic关闭键替换点击坐标概率
	LocalPosNewMagicCloseReplaceClickWeight int `json:"LocalPosNewMagicCloseReplaceClickWeight,omitempty"`
	// local pos 云控选项, 滑动触发方向, 0 向上(默认), 1 所有
	LocalPosNewSlideDirection int `json:"LocalPosNewSlideDirection,omitempty"`
	// local pos 云控选项, magic区域, 0 关闭(默认), 1 全屏
	LocalPosNewMagicRegion int `json:"LocalPosNewMagicRegion,omitempty"`

	// local pos sdk摇一摇2个选项
	// LocalPosIsSupportSDKInteraction 0不用; 1 枫岚组件, 2 枫岚动画+三方组件
	LocalPosIsSupportSDKInteraction    int    `json:"LocalPosIsSupportSDKInteraction,omitempty"`
	LocalPosSDKInteractionSupportTypes string `json:"LocalPosSDKInteractionSupportTypes,omitempty"`
	// local app是否存储为替换包原始包(didlib_android, didlib_ios)
	LocalAppIsDIDStorage                int `json:"LocalAppIsDIDStorage,omitempty"`
	LocalAppIsDIDStorageVerifyParameter int `json:"LocalAppIsDIDStorageVerifyParameter,omitempty"` // 存储为替换包原始包是否验证参数

	// local app material replace rule
	LocalAppMaterialReplaceRuleID                   string                            `json:"LocalAppMaterialReplaceRuleID,omitempty"`
	LocalAppMaterialReplaceRuleReplaceKey           string                            `json:"LocalAppMaterialReplaceRuleReplaceKey,omitempty"`
	LocalAppMaterialReplaceRuleAdUrl                string                            `json:"LocalAppMaterialReplaceRuleAdUrl,omitempty"`
	LocalAppMaterialReplaceRuleMaterialUrl          string                            `json:"LocalAppMaterialReplaceRuleMaterialUrl,omitempty"`
	LocalAppMaterialReplaceRuleMaterialReplaceGroup []LocalAppPosMaterialReplaceGroup `json:"LocalAppMaterialReplaceRuleMaterialReplaceGroup,omitempty"`

	// local pos material replace rule
	LocalPosMaterialReplaceRuleID                   string                            `json:"LocalPosMaterialReplaceRuleID,omitempty"`
	LocalPosMaterialReplaceRuleReplaceKey           string                            `json:"LocalPosMaterialReplaceRuleReplaceKey,omitempty"`
	LocalPosMaterialReplaceRuleAdUrl                string                            `json:"LocalPosMaterialReplaceRuleAdUrl,omitempty"`
	LocalPosMaterialReplaceRuleMaterialUrl          string                            `json:"LocalPosMaterialReplaceRuleMaterialUrl,omitempty"`
	LocalPosMaterialReplaceRuleMaterialReplaceGroup []LocalAppPosMaterialReplaceGroup `json:"LocalPosMaterialReplaceRuleMaterialReplaceGroup,omitempty"`

	// 是否bidding
	// LocalPosIsBidding int `json:"LocalPosIsBidding,omitempty"`
	// ecpm type, 0(非实时竞价), 1(实时竞价-动态价), 2(实时竞价-固定价)
	LocalPosEcpmType int `json:"LocalPosEcpmType,omitempty"`
	// ecpm(分)
	LocalPosEcpm int `json:"LocalPosEcpm,omitempty"`
	// profit rate
	LocalPosProfitRate float32 `json:"LocalPosProfitRate,omitempty"`
	// 自定义竞价底价选中的平台 custom_cpm_bid_floor_platform_list
	LocalPosCustomCPMBidFloorPlatformList string `json:"LocalPosCustomCPMBidFloorPlatformList,omitempty"`

	// 是否替换SDK广告来源
	LocalPosIsRadst int `json:"LocalPosIsRadst,omitempty"`
	// 替换SDK广告来源
	LocalPosRadst string `json:"LocalPosRadst,omitempty"`

	// 价格加解密token
	LocalAppPriceToken string `json:"LocalAppPriceToken,omitempty"`

	// SDK流量过滤-API(3.2.11)比例
	LocalAppSDKFilterApiWeight int `json:"sdk_filter_api_weight,omitempty"`
	// SDK流量过滤-SDK(3.2.11)比例
	LocalAppSDKFilterSDKWeight int `json:"sdk_filter_sdk_weight,omitempty"`

	// 是否UA修正, 0关闭, 1补空, 2替换
	LocalAppIsFixUA int `json:"LocalAppIsFixUA,omitempty"`

	// 是否UA修正为替换类型, UA不替换的广告源
	LocalAppUANoReplaceDemandPlatformIDs string `json:"LocalAppUANoReplaceDemandPlatformIDs,omitempty"`

	// 是否强制http转https
	LocalAppIsForceHttps int `json:"LocalAppIsForceHttps,omitempty"`

	// 点击坐标类型, 0 无, 1 物理像素, 2 逻辑像素
	LocalAppClickPointType int `json:"LocalAppClickPointType,omitempty"`

	// SDK物理像素广告源
	LocalAppPhysicalPixelPlatformIDs string `json:"LocalAppPhysicalPixelPlatformIDs,omitempty"`

	// dp和h5并行开关, 0 关, 1 开
	LocalAppIsCoDPH5 int `json:"LocalAppIsCoDPH5,omitempty"`

	// dp和h5并行类型, 0 全部, 1 限制
	LocalAppDPH5Type int `json:"LocalAppDPH5Type,omitempty"`

	// dp和h5并行API广告源
	LocalAppDPH5PlatformIDS string `json:"LocalAppDPH5PlatformIDS,omitempty"`

	// 分流作用范围, 0 有效预算配置(不包括置灰), 1 全预算配置(不包括置灰)
	LocalPosDiffluenceRange int `json:"LocalPosDiffluenceRange,omitempty"`

	// 分流策略类型, 0 优先限制, 1 优先权重
	LocalPosDiffluenceStrategy int `json:"LocalPosDiffluenceStrategy,omitempty"`

	// sdk点击延迟上报最小时间
	LocalAppClickDelayReportMinTime string `json:"LocalAppClickDelayReportMinTime,omitempty"`

	// sdk点击延迟上报最大时间
	LocalAppClickDelayReportMaxTime string `json:"LocalAppClickDelayReportMaxTime,omitempty"`

	// SDK下载广告通知栏展示广告位类型列表
	LocalAppSDKNotificationPosTypeList string `json:"LocalAppSDKNotificationPosTypeList,omitempty"`

	// SDK反作弊
	LocalAppIsSDKAntiCheat int `json:"LocalAppIsSDKAntiCheat,omitempty"`
	// SDK反作弊, 0 全部屏蔽, 1 选择屏蔽
	LocalAppIsSDKAntiCheatPlatformIDs int    `json:"LocalAppIsSDKAntiCheatPlatformIDs,omitempty"`
	LocalAppSDKAntiCheatPlatformIDs   string `json:"LocalAppSDKAntiCheatPlatformIDs,omitempty"`
	LocalAppSDKAntiCheatExposeType    int    `json:"LocalAppSDKAntiCheatExposeType,omitempty"`
	LocalAppSDKAntiCheatExposeValue   string `json:"LocalAppSDKAntiCheatExposeValue,omitempty"`
	LocalAppSDKAntiCheatClickType     int    `json:"LocalAppSDKAntiCheatClickType,omitempty"`
	LocalAppSDKAntiCheatClickValue    string `json:"LocalAppSDKAntiCheatClickValue,omitempty"`

	// 是否厂商, 是否快应用
	LocalAppIsManufacturer int `json:"LocalAppIsManufacturer,omitempty"`
	LocalAppIsQuickApp     int `json:"LocalAppIsQuickApp,omitempty"`
}

type LocalAppPosMaterialReplaceGroup struct {
	LocalAppPosMaterialReplaceRuleReplaceType  string `json:"LocalAppPosMaterialReplaceRuleReplaceType,omitempty"`
	LocalAppPosMaterialReplaceRuleMaterialType string `json:"LocalAppPosMaterialReplaceRuleMaterialType,omitempty"`
	LocalAppPosMaterialReplaceRuleReplaceValue string `json:"LocalAppPosMaterialReplaceRuleReplaceValue,omitempty"`
}
