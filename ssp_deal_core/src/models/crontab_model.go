package models

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/db"
	"mh_proxy/utils"
	"sort"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
)

// CrontabReadConfig ...
func CrontabReadConfig() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("cron panic:", err)
			}
		}()
		GoCrontabReadConfig()
	}()
}

// GoCrontabReadConfig ...
func GoCrontabReadConfig() {
	sspBeginTime := utils.GetCurrentMilliSecond()

	getRtbConfigFromMySQL()
	getSSPConfigFromMySQL()
	getSSPPlatformAppConfigFromMySQL()
	getSSPMaterialFullConfigFromMySQL()
	getSSPMaterialReplaceConfigFromMySQL()
	getSSPAdShieldConfigFromMySQL()
	getSSPExtraConfigFromMySQL()
	MaterialStatisticsTrafficConfig()
	getSSPTypeShieldConfigFromMySQL()
	getSSPLocalAppConfigFromMySQL()

	getSSPPackageFullConfigFromMySQL()

	getSSPMaterialReplaceTypeConfigFromMySQL()

	clearCacheFromMySQL()

	getSSPMaterialReplaceAdContentFromMySQL()

	getClickPointLibConfigFromMySQL()

	getSDKLogoConfigFromMySQL()

	getLossDataConfigFromMySQL()

	getDSPConfigFromMySQL()

	// 反作弊配置
	getAntiCheatConfigFromRedis()

	// query pkg 字典
	getQuerysPkgsFromRedis()

	// debug超时最大时间, 存holo百分比
	getDebugTimeOutFromRedis()

	// 设备白名单
	getWhiteDIDFromRedis()

	// ua
	getUAConfigFromRedis()

	// dpi
	getDPIConfigFromRedis()

	fmt.Println("crontab ssp cost --->", utils.GetCurrentMilliSecond()-sspBeginTime, db.GlbBigCache.Capacity(), db.GlbBigCache.Len(), db.GlbBigCache.Stats())
}
func getSSPMaterialReplaceTypeConfigFromMySQL() {
	sqlStr := "select rule_id,name,replace_key,ad_url,material_url,platform_ids,app_ids,pos_ids from ssp.material_replace_type_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var materialReplaceTypeArray []NewMaterialReplaceType

	for rows.Next() {
		var materialReplaceTypeItem NewMaterialReplaceType
		err = rows.Scan(&materialReplaceTypeItem.RuleID, &materialReplaceTypeItem.RuleName, &materialReplaceTypeItem.ReplaceKey, &materialReplaceTypeItem.AdUrl, &materialReplaceTypeItem.MaterialUrl, &materialReplaceTypeItem.PlatformIds, &materialReplaceTypeItem.AppIds, &materialReplaceTypeItem.PosIds)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		groupSqlStr := "select material_type,replace_type,img_url,video_url,cover_url,width,height,duration,dynamic_materials from ssp.material_replace_type_group where rule_id='%s'"
		groupSqlStr = fmt.Sprintf(groupSqlStr, materialReplaceTypeItem.RuleID)

		groupRows, err := db.GlbMySQLDb.Query(groupSqlStr)
		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}
		defer groupRows.Close()

		var replaceTypeGroupArray []NewMaterialReplaceTypeGroup
		for groupRows.Next() {
			var replaceTypeGroup NewMaterialReplaceTypeGroup
			var err = groupRows.Scan(&replaceTypeGroup.MaterialType, &replaceTypeGroup.ReplaceType, &replaceTypeGroup.ImgUrl, &replaceTypeGroup.VideoUrl, &replaceTypeGroup.CoverUrl, &replaceTypeGroup.Width, &replaceTypeGroup.Height, &replaceTypeGroup.Duration, &replaceTypeGroup.DynamicMaterials)
			if err != nil {
				fmt.Printf("scan failed, err:%v\n", err)
				return
			}
			replaceTypeGroupArray = append(replaceTypeGroupArray, replaceTypeGroup)
		}
		materialReplaceTypeItem.MaterialReplaceGroup = replaceTypeGroupArray
		materialReplaceTypeArray = append(materialReplaceTypeArray, materialReplaceTypeItem)
	}

	replaceMap := make(map[string][]NewMaterialReplaceType)
	for _, replaceRule := range materialReplaceTypeArray {
		if len(replaceRule.PlatformIds) == 0 {
			continue
		}
		platformIds := strings.Split(replaceRule.PlatformIds, ",")
		if len(replaceRule.AppIds) > 0 {
			appIds := strings.Split(replaceRule.AppIds, ",")
			for _, appId := range appIds {
				for _, platformId := range platformIds {
					replaceMap[appId+platformId] = append(replaceMap[appId+platformId], replaceRule)
				}
			}
		}
		if len(replaceRule.PosIds) > 0 {
			posIds := strings.Split(replaceRule.PosIds, ",")
			for _, posId := range posIds {
				for _, platformId := range platformIds {
					replaceMap[posId+platformId] = append(replaceMap[posId+platformId], replaceRule)
				}
			}
		}
	}

	for key, value := range replaceMap {
		cacheKey := "go_ssp_material_replace_type_config_" + key
		marshal, err := json.Marshal(value)
		if err != nil {
			fmt.Println(err)
			continue
		}
		_ = db.GlbBigCacheMinute.Set(cacheKey, marshal)
	}
}

func getSSPMaterialReplaceAdContentFromMySQL() {
	sqlStr := "select rule_id,platform_ids,chance,app_ids,pos_ids from ssp.material_replace_ad_content_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var materialReplaceAdContentArray []MaterialReplaceAdContentModel
	for rows.Next() {
		var materialReplaceAdContentItem MaterialReplaceAdContentModel
		err = rows.Scan(&materialReplaceAdContentItem.RuleID, &materialReplaceAdContentItem.PlatformIds, &materialReplaceAdContentItem.Chance, &materialReplaceAdContentItem.LocalAppIDs, &materialReplaceAdContentItem.LocalPosIDs)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		groupSqlStr := "select title,description,image_url,video_url,cover_url,width,height,duration,h5_url,download_url,dp_url,chance from ssp.material_replace_ad_content_group where rule_id='%s'"
		groupSqlStr = fmt.Sprintf(groupSqlStr, materialReplaceAdContentItem.RuleID)
		groupRows, err := db.GlbMySQLDb.Query(groupSqlStr)
		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}
		defer groupRows.Close()

		var materialReplaceAdContentGroup []MaterialReplaceAdContentGroupModel

		for groupRows.Next() {
			var materialReplaceAdContentGroupItem MaterialReplaceAdContentGroupModel
			var err = groupRows.Scan(&materialReplaceAdContentGroupItem.Title, &materialReplaceAdContentGroupItem.Description, &materialReplaceAdContentGroupItem.ImageUrl, &materialReplaceAdContentGroupItem.VideoUrl, &materialReplaceAdContentGroupItem.CoverUrl, &materialReplaceAdContentGroupItem.Width, &materialReplaceAdContentGroupItem.Height, &materialReplaceAdContentGroupItem.Duration, &materialReplaceAdContentGroupItem.H5Url, &materialReplaceAdContentGroupItem.DownloadUrl, &materialReplaceAdContentGroupItem.DpUrl, &materialReplaceAdContentGroupItem.Chance)
			if err != nil {
				fmt.Printf("scan failed, err:%v\n", err)
				return
			}
			materialReplaceAdContentItem.GroupChance = materialReplaceAdContentItem.GroupChance + materialReplaceAdContentGroupItem.Chance
			materialReplaceAdContentGroup = append(materialReplaceAdContentGroup, materialReplaceAdContentGroupItem)
			materialReplaceAdContentItem.Group = materialReplaceAdContentGroup
		}

		materialReplaceAdContentArray = append(materialReplaceAdContentArray, materialReplaceAdContentItem)
	}

	if len(materialReplaceAdContentArray) > 0 {
		replaceMap := make(map[string][]MaterialReplaceAdContentModel)
		for _, replaceRule := range materialReplaceAdContentArray {
			if len(replaceRule.PlatformIds) == 0 {
				continue
			}
			platformIds := strings.Split(replaceRule.PlatformIds, ",")
			if len(replaceRule.LocalAppIDs) > 0 {
				appIds := strings.Split(replaceRule.LocalAppIDs, ",")
				for _, appId := range appIds {
					for _, platformId := range platformIds {
						replaceMap[appId+platformId] = append(replaceMap[appId+platformId], replaceRule)
					}
				}
			}
			if len(replaceRule.LocalPosIDs) > 0 {
				posIds := strings.Split(replaceRule.LocalPosIDs, ",")
				for _, posId := range posIds {
					for _, platformId := range platformIds {
						replaceMap[posId+platformId] = append(replaceMap[posId+platformId], replaceRule)
					}
				}
			}
		}

		for key, value := range replaceMap {
			cacheKey := "go_ssp_material_replace_ad_content_" + key
			marshal, err := json.Marshal(value)
			if err != nil {
				fmt.Println(err)
				continue
			}
			_ = db.GlbBigCacheMinute.Set(cacheKey, marshal)
		}
	}
}

func getSSPMaterialReplaceConfigFromMySQL() {
	sqlStr := "select material_replace_rule_id,name,replace_key,ad_url,material_url,app_ids,pos_ids,type,platform_ids,chance,unconditional_switch from ssp.material_replace_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var materialReplaceArray []NewMaterialReplace

	for rows.Next() {
		var materialReplaceItem NewMaterialReplace
		err = rows.Scan(&materialReplaceItem.MaterialReplaceRuleID, &materialReplaceItem.MaterialReplaceRuleName, &materialReplaceItem.ReplaceKey, &materialReplaceItem.AdUrl, &materialReplaceItem.MaterialUrl, &materialReplaceItem.AppIds, &materialReplaceItem.PosIds, &materialReplaceItem.Type, &materialReplaceItem.PlatformIds, &materialReplaceItem.Chance, &materialReplaceItem.UnconditionalSwitch)

		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		groupSqlStr := "select replace_type,replace_value,material_type,width,height,dynamic_materials from ssp.material_replace_group where material_replace_rule_id='%s'"
		groupSqlStr = fmt.Sprintf(groupSqlStr, materialReplaceItem.MaterialReplaceRuleID)
		groupRows, err := db.GlbMySQLDb.Query(groupSqlStr)
		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}
		defer groupRows.Close()

		var (
			titleArray             []string
			descArray              []string
			iconArray              []string
			imgArray               []string
			imgWArray              []string
			proportionallyImgArray []string
			imgHArray              []string
			videoArray             []string
			videoWArray            []string
			videoHArray            []string
			coverArray             []string
			coverWArray            []string
			coverHArray            []string
		)

		var newReplaceGroupArray []MaterialReplaceGroup
		for groupRows.Next() {
			var replaceGroup NewMaterialReplaceGroup
			var newReplaceGroup MaterialReplaceGroup

			var err = groupRows.Scan(&replaceGroup.ReplaceType, &replaceGroup.ReplaceValue, &replaceGroup.MaterialType, &replaceGroup.Width, &replaceGroup.Height, &replaceGroup.DynamicMaterials)
			if err != nil {
				fmt.Printf("scan failed, err:%v\n", err)
				return
			}

			switch replaceGroup.ReplaceType {
			case "1":
				titleArray = append(titleArray, replaceGroup.ReplaceValue)
			case "2":
				descArray = append(descArray, replaceGroup.ReplaceValue)
			case "3", "4":
				iconArray = append(iconArray, replaceGroup.ReplaceValue)
			case "5", "6":
				imgArray = append(imgArray, replaceGroup.ReplaceValue)
				if replaceGroup.MaterialType == 1 {
					imgWArray = append(imgWArray, replaceGroup.ReplaceValue)
				} else if replaceGroup.MaterialType == 2 {
					imgHArray = append(imgHArray, replaceGroup.ReplaceValue)
				} else if replaceGroup.MaterialType == 3 {
					proportionallyImgArray = append(proportionallyImgArray, replaceGroup.ReplaceValue)
				}
			case "7", "8":
				videoArray = append(videoArray, replaceGroup.ReplaceValue)
				if replaceGroup.MaterialType == 1 {
					videoWArray = append(videoWArray, replaceGroup.ReplaceValue)
				} else if replaceGroup.MaterialType == 2 {
					videoHArray = append(videoHArray, replaceGroup.ReplaceValue)
				}
			case "9", "10":
				coverArray = append(coverArray, replaceGroup.ReplaceValue)
				if replaceGroup.MaterialType == 1 {
					coverWArray = append(coverWArray, replaceGroup.ReplaceValue)
				} else if replaceGroup.MaterialType == 2 {
					coverHArray = append(coverHArray, replaceGroup.ReplaceValue)
				}
			}
			newReplaceGroup.ReplaceType = replaceGroup.ReplaceType
			newReplaceGroup.ReplaceValue = replaceGroup.ReplaceValue
			newReplaceGroup.Width = replaceGroup.Width
			newReplaceGroup.Height = replaceGroup.Height
			newReplaceGroup.DynamicMaterials = replaceGroup.DynamicMaterials
			newReplaceGroupArray = append(newReplaceGroupArray, newReplaceGroup)
		}

		materialReplaceItem.Title = titleArray
		materialReplaceItem.Desc = descArray
		materialReplaceItem.Icon = iconArray
		materialReplaceItem.Img = imgArray
		materialReplaceItem.ProportionallyImg = proportionallyImgArray
		materialReplaceItem.ImgW = imgWArray
		materialReplaceItem.ImgH = imgHArray
		materialReplaceItem.Video = videoArray
		materialReplaceItem.VideoW = videoWArray
		materialReplaceItem.VideoH = videoHArray
		materialReplaceItem.Cover = coverArray
		materialReplaceItem.CoverW = coverWArray
		materialReplaceItem.CoverH = coverHArray
		materialReplaceItem.Group = newReplaceGroupArray
		materialReplaceArray = append(materialReplaceArray, materialReplaceItem)
	}

	if len(materialReplaceArray) == 0 {
		return
	}

	replaceMap := make(map[string][]NewMaterialReplaceBigCache)
	for _, replaceRule := range materialReplaceArray {
		ruleBigCache := NewMaterialReplaceBigCache{
			MaterialReplaceRuleID:   replaceRule.MaterialReplaceRuleID,
			MaterialReplaceRuleName: replaceRule.MaterialReplaceRuleName,
			Type:                    replaceRule.Type,
			ReplaceKey:              replaceRule.ReplaceKey,
			AdUrl:                   replaceRule.AdUrl,
			MaterialUrl:             replaceRule.MaterialUrl,
			Title:                   replaceRule.Title,
			Desc:                    replaceRule.Desc,
			Icon:                    replaceRule.Icon,
			Img:                     replaceRule.Img,
			ProportionallyImg:       replaceRule.ProportionallyImg,
			ImgW:                    replaceRule.ImgW,
			ImgH:                    replaceRule.ImgH,
			Video:                   replaceRule.Video,
			VideoW:                  replaceRule.VideoW,
			VideoH:                  replaceRule.VideoH,
			Cover:                   replaceRule.Cover,
			CoverW:                  replaceRule.CoverW,
			CoverH:                  replaceRule.CoverH,
			Group:                   replaceRule.Group,
			PlatformIds:             replaceRule.PlatformIds,
			Chance:                  replaceRule.Chance,
			UnconditionalSwitch:     replaceRule.UnconditionalSwitch,
		}
		var platformIds []string
		if len(replaceRule.PlatformIds) > 0 {
			platformIds = strings.Split(replaceRule.PlatformIds, ",")
		}

		if len(replaceRule.AppIds) > 0 {
			appIds := strings.Split(replaceRule.AppIds, ",")
			for _, appId := range appIds {
				for _, platformId := range platformIds {
					replaceMap[appId+platformId] = append(replaceMap[appId+platformId], ruleBigCache)
				}
			}
		}
		if len(replaceRule.PosIds) > 0 {
			posIds := strings.Split(replaceRule.PosIds, ",")
			for _, posId := range posIds {
				for _, platformId := range platformIds {
					replaceMap[posId+platformId] = append(replaceMap[posId+platformId], ruleBigCache)
				}
			}
		}
	}

	for key, value := range replaceMap {
		cacheKey := "go_ssp_material_replace_config_" + key
		marshal, err := json.Marshal(value)
		if err != nil {
			fmt.Println(err)
			continue
		}
		_ = db.GlbBigCacheMinute.Set(cacheKey, marshal)
	}
}

func MaterialStatisticsTrafficConfig() {
	sqlStr := "select app_id,traffic from ssp.material_statistics_traffic_config"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	for rows.Next() {
		var appId string
		var traffic int
		err = rows.Scan(&appId, &traffic)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		trafficStr := strconv.Itoa(traffic)
		cacheKey := "go_ssp_material_statistics_traffic_config_app_" + appId
		_ = db.GlbBigCacheMinute.Set(cacheKey, []byte(trafficStr))
	}
}

func getSSPTypeShieldConfigFromMySQL() {
	sqlStr := "select id,sdk,sdk_filter,app_ids,pos_ids,landing_page_switch from ssp.type_shield_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var typeRuleArray []TypeRuleListStu
	for rows.Next() {
		var typeRuleItem TypeRuleListStu
		err = rows.Scan(&typeRuleItem.Id, &typeRuleItem.Sdk, &typeRuleItem.SdkFilter, &typeRuleItem.AppIds, &typeRuleItem.PosIds, &typeRuleItem.LandingPageSwitch)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		typeRuleArray = append(typeRuleArray, typeRuleItem)
	}

	if len(typeRuleArray) == 0 {
		return
	}

	for _, typeRule := range typeRuleArray {
		ruleBigCache := TypeRuleListBigCache{
			Sdk:               typeRule.Sdk,
			SdkFilter:         typeRule.SdkFilter,
			LandingPageSwitch: typeRule.LandingPageSwitch,
		}

		if len(typeRule.AppIds) > 0 {
			appIds := strings.Split(typeRule.AppIds, ",")
			for _, appId := range appIds {
				switch typeRule.Id {
				case 1:
					ruleBigCache.AppId = appId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_download_app_" + appId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				case 2:
					ruleBigCache.AppId = appId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_dp_app_" + appId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				case 3:
					ruleBigCache.AppId = appId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_h5_app_" + appId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				}
			}
		}

		if len(typeRule.PosIds) > 0 {
			posIds := strings.Split(typeRule.PosIds, ",")
			for _, posId := range posIds {
				switch typeRule.Id {
				case 1:
					ruleBigCache.PosId = posId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_download_pos_" + posId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				case 2:
					ruleBigCache.PosId = posId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_dp_pos_" + posId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				case 3:
					ruleBigCache.PosId = posId
					jsonStr, _ := json.Marshal(ruleBigCache)
					cacheKey := "go_ssp_type_shield_h5_pos_" + posId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				}
			}
		}
	}
}

func getSSPPackageFullConfigFromMySQL() {
	sqlStr := "select android_package_name,ios_package_name,deep_link from ssp.material_package_empty_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var packageArray []PackageRuleListStu
	for rows.Next() {
		var packageItem PackageRuleListStu
		err = rows.Scan(&packageItem.AndroidPackageName, &packageItem.IosPackageName, &packageItem.DeepLink)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		packageArray = append(packageArray, packageItem)
	}

	packageMap := make(map[string]string)
	for _, packageVal := range packageArray {
		packageMap[packageVal.DeepLink] = packageVal.AndroidPackageName + "_" + packageVal.IosPackageName
	}
	jsonStr, err := json.Marshal(packageMap)
	if err != nil {
		return
	}
	cacheKey := "go_material_package_full"
	_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
}

func getSSPExtraConfigFromMySQL() {
	sqlStr := "select extra_rule_id,extra_key,package_name,ad_url,material_url,exp_url,condition_type,extra_type,exp_weight,exp_target_num,click_rate,price_intercept,mg_deal_id,rta_id,platform_ids,app_ids,pos_ids,status from ssp.extra_rules where is_active = 1 and status < 2"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var extraArray []SSPExtraStu
	for rows.Next() {
		var extra SSPExtraStu
		err = rows.Scan(&extra.ExtraRuleId, &extra.ExtraKey, &extra.PackageName, &extra.AdUrl, &extra.MaterialUrl, &extra.ExpUrl, &extra.ConditionType, &extra.ExtraType, &extra.ExpWeight, &extra.ExpTargetNum, &extra.ClickRate, &extra.PriceIntercept, &extra.MgDealId, &extra.RtaId, &extra.PlatformIds, &extra.AppIds, &extra.PosIds, &extra.Status)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		var groupList []SSPExtraGroupStu
		groupSqlStr := fmt.Sprintf("select time_period,exp_count from ssp.extra_rules_group where extra_rule_id='%s'", extra.ExtraRuleId)
		groupRows, err := db.GlbMySQLDb.Query(groupSqlStr)
		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}
		defer groupRows.Close()
		for groupRows.Next() {
			var group SSPExtraGroupStu
			err = groupRows.Scan(&group.TimePeriod, &group.ExpCount)
			if err != nil {
				fmt.Printf("scan failed, err:%v\n", err)
				return
			}
			groupList = append(groupList, group)
		}
		extra.GroupList = groupList
		extraArray = append(extraArray, extra)
	}

	for _, extraItem := range extraArray {
		if len(extraItem.PlatformIds) == 0 {
			continue
		}

		extraBigCache := SSPExtraBigCache{
			ExtraKey:       extraItem.ExtraKey,
			ExtraType:      extraItem.ExtraType,
			ExpWeight:      extraItem.ExpWeight,
			Status:         extraItem.Status,
			ConditionType:  extraItem.ConditionType,
			ExpUrl:         extraItem.ExpUrl,
			PackageName:    extraItem.PackageName,
			AdUrl:          extraItem.AdUrl,
			MaterialUrl:    extraItem.MaterialUrl,
			ExpTargetNum:   extraItem.ExpTargetNum,
			ClickRate:      extraItem.ClickRate,
			PriceIntercept: extraItem.PriceIntercept,
			MgDealId:       extraItem.MgDealId,
			RtaId:          extraItem.RtaId,
			PlatformIds:    extraItem.PlatformIds,
			GroupList:      extraItem.GroupList,
		}
		platformIds := strings.Split(extraItem.PlatformIds, ",")
		jsonStr, _ := json.Marshal(extraBigCache)
		if len(extraItem.AppIds) > 0 {
			appIds := strings.Split(extraItem.AppIds, ",")
			for _, appId := range appIds {
				for _, platformId := range platformIds {
					cacheKey := "go_ssp_extra_app_" + appId + platformId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				}
			}
		}

		if len(extraItem.PosIds) > 0 {
			posIds := strings.Split(extraItem.PosIds, ",")
			for _, posId := range posIds {
				for _, platformId := range platformIds {
					cacheKey := "go_ssp_extra_pos_" + posId + platformId
					_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
				}

			}
		}
	}
}

func getSSPAdShieldConfigFromMySQL() {
	sqlStr := "select shield_key,package_name,exp_url,ad_url,material_url,ad_shield_type,shield_rule_type,platform_ids,app_ids,pos_ids,time_list,region_list,is_white from ssp.ad_shield_rules where is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	defer func(rows *sql.Rows) {
		_ = rows.Close()
	}(rows)

	var adShieldArray []AdShieldStu
	for rows.Next() {
		var adShield AdShieldStu
		err = rows.Scan(&adShield.ShieldKey, &adShield.PackageName, &adShield.ExpUrl, &adShield.AdUrl, &adShield.MaterialUrl, &adShield.AdShieldType, &adShield.ShieldRuleType, &adShield.PlatformIds, &adShield.AppIds, &adShield.PosIds, &adShield.TimeList, &adShield.RegionList, &adShield.IsWhite)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		adShieldArray = append(adShieldArray, adShield)
	}

	for _, adShieldItem := range adShieldArray {
		adShieldBigCache := AdShieldBigCache{
			ShieldRuleType: adShieldItem.ShieldRuleType,
			AdShieldType:   adShieldItem.AdShieldType,
			ShieldKey:      adShieldItem.ShieldKey,
			PackageName:    adShieldItem.PackageName,
			ExpUrl:         adShieldItem.ExpUrl,
			AdUrl:          adShieldItem.AdUrl,
			MaterialUrl:    adShieldItem.MaterialUrl,
			TimeList:       adShieldItem.TimeList,
			RegionList:     adShieldItem.RegionList,
		}

		jsonStr, _ := json.Marshal(adShieldBigCache)
		if len(adShieldItem.PlatformIds) > 0 {
			platformIds := strings.Split(adShieldItem.PlatformIds, ",")
			for _, platformId := range platformIds {
				cacheKey := "go_ad_fhield_platform_" + platformId
				if adShieldItem.IsWhite == 2 {
					cacheKey = "go_ad_fhield_white_platform_" + platformId
				}
				_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
			}
		}
		if len(adShieldItem.AppIds) > 0 {
			appIds := strings.Split(adShieldItem.AppIds, ",")
			for _, appId := range appIds {
				cacheKey := "go_ad_fhield_app_" + appId
				if adShieldItem.IsWhite == 2 {
					cacheKey = "go_ad_fhield_white_app_" + appId
				}
				_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
			}
		}

		if len(adShieldItem.PosIds) > 0 {
			posIds := strings.Split(adShieldItem.PosIds, ",")
			for _, posId := range posIds {
				cacheKey := "go_ad_fhield_pos_" + posId
				if adShieldItem.IsWhite == 2 {
					cacheKey = "go_ad_fhield_white_pos_" + posId
				}
				_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
			}
		}
	}
}

func getSSPMaterialFullConfigFromMySQL() {
	sqlStr := "select id, supply_pos_type, IFNULL(empty_rule_list, '') from ssp.material_empty_rules  where id in (1,2,3,4,5,9) and is_active = 1"
	rows, err := db.GlbMySQLDb.Query(sqlStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	var materialFullArray []MaterialFullStu
	defer rows.Close()
	for rows.Next() {
		var materialFull MaterialFullStu
		err := rows.Scan(&materialFull.ID, &materialFull.SupplyPosType, &materialFull.EmptyRuleList)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		materialFullArray = append(materialFullArray, materialFull)
	}
	var platformIdArray []string
	materialFullMap := make(map[string]map[string][]string)
	for _, materialFullItem := range materialFullArray {
		var emptyRuleList []EmptyRuleListStu
		_ = json.Unmarshal([]byte(materialFullItem.EmptyRuleList), &emptyRuleList)
		for _, emptyRuleItem := range emptyRuleList {
			platformIdArray = append(platformIdArray, emptyRuleItem.PlatformId)
			for _, emptyItem := range emptyRuleItem.EmptyItemList {
				_, platKeyOk := materialFullMap[emptyRuleItem.PlatformId]
				if !platKeyOk {
					materialFullMap[emptyRuleItem.PlatformId] = make(map[string][]string)
				}
				switch materialFullItem.ID {
				case 1:
					var titleArr []string
					titleVal, ok := materialFullMap[emptyRuleItem.PlatformId]["title"]
					if ok {
						materialFullMap[emptyRuleItem.PlatformId]["title"] = append(titleVal, emptyItem.Value)
					} else {
						materialFullMap[emptyRuleItem.PlatformId]["title"] = append(titleArr, emptyItem.Value)
					}
				case 2:
					var descArr []string
					descVal, ok := materialFullMap[emptyRuleItem.PlatformId]["desc"]
					if ok {
						materialFullMap[emptyRuleItem.PlatformId]["desc"] = append(descVal, emptyItem.Value)
					} else {
						materialFullMap[emptyRuleItem.PlatformId]["desc"] = append(descArr, emptyItem.Value)
					}
				case 3:
					var iconArr []string
					iconVal, ok := materialFullMap[emptyRuleItem.PlatformId]["icon"]
					if ok {
						materialFullMap[emptyRuleItem.PlatformId]["icon"] = append(iconVal, emptyItem.Value)
					} else {
						materialFullMap[emptyRuleItem.PlatformId]["icon"] = append(iconArr, emptyItem.Value)
					}
				case 4:
					var (
						nativeCoverUrlArr  []string
						nativeCoverUrlWArr []string
						nativeCoverUrlHArr []string
					)
					switch emptyItem.EmptyType {
					case "cover_url", "cover_url_oss":
						nativeCoverUrlVal, nativeCoverUrlOk := materialFullMap[emptyRuleItem.PlatformId]["native_cover_url"]
						if nativeCoverUrlOk {
							materialFullMap[emptyRuleItem.PlatformId]["native_cover_url"] = append(nativeCoverUrlVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["native_cover_url"] = append(nativeCoverUrlArr, emptyItem.Value)
						}
						if emptyItem.MaterialType == "1" {
							nativeCoverUrlWVal, nativeCoverUrlHOk := materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_w"]
							if nativeCoverUrlHOk {
								materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_w"] = append(nativeCoverUrlWVal, emptyItem.Value)
							} else {
								materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_w"] = append(nativeCoverUrlWArr, emptyItem.Value)
							}
						}
						if emptyItem.MaterialType == "2" {
							nativeCoverUrlHVal, nativeCoverUrlHOk := materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_h"]
							if nativeCoverUrlHOk {
								materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_h"] = append(nativeCoverUrlHVal, emptyItem.Value)
							} else {
								materialFullMap[emptyRuleItem.PlatformId]["native_cover_url_h"] = append(nativeCoverUrlHArr, emptyItem.Value)
							}
						}
					}
				case 5:
					var (
						incentiveCoverUrlArr  []string
						incentiveCoverUrlWArr []string
						incentiveCoverUrlHArr []string
					)
					switch emptyItem.EmptyType {
					case "cover_url", "cover_url_oss":
						incentiveCoverUrlVal, incentiveCoverUrlOk := materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url"]
						if incentiveCoverUrlOk {
							materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url"] = append(incentiveCoverUrlVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url"] = append(incentiveCoverUrlArr, emptyItem.Value)
						}
						if emptyItem.MaterialType == "1" {
							incentiveCoverUrlWVal, incentiveCoverUrlWOk := materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_w"]
							if incentiveCoverUrlWOk {
								materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_w"] = append(incentiveCoverUrlWVal, emptyItem.Value)
							} else {
								materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_w"] = append(incentiveCoverUrlWArr, emptyItem.Value)
							}
						}
						if emptyItem.MaterialType == "2" {
							incentiveCoverUrlHVal, incentiveCoverUrlHOk := materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_h"]
							if incentiveCoverUrlHOk {
								materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_h"] = append(incentiveCoverUrlHVal, emptyItem.Value)
							} else {
								materialFullMap[emptyRuleItem.PlatformId]["incentive_cover_url_h"] = append(incentiveCoverUrlHArr, emptyItem.Value)
							}
						}
					}
				case 9:
					var (
						appNameArr       []string
						appVersionArr    []string
						appInfoArr       []string
						publisherArr     []string
						permissionArr    []string
						permissionUrlArr []string
						privacyLinkArr   []string
						packageSizeArr   []string
					)
					switch emptyItem.EmptyType {
					case "app_name":
						appNameVal, appNameOk := materialFullMap[emptyRuleItem.PlatformId]["app_name"]
						if appNameOk {
							materialFullMap[emptyRuleItem.PlatformId]["app_name"] = append(appNameVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["app_name"] = append(appNameArr, emptyItem.Value)
						}
					case "app_version":
						appVersionVal, appVersionOk := materialFullMap[emptyRuleItem.PlatformId]["app_version"]
						if appVersionOk {
							materialFullMap[emptyRuleItem.PlatformId]["app_version"] = append(appVersionVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["app_version"] = append(appVersionArr, emptyItem.Value)
						}
					case "publisher":
						publisherVal, publisherOk := materialFullMap[emptyRuleItem.PlatformId]["publisher"]
						if publisherOk {
							materialFullMap[emptyRuleItem.PlatformId]["publisher"] = append(publisherVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["publisher"] = append(publisherArr, emptyItem.Value)
						}
					case "2":
						privacyLinkVal, privacyLinkOk := materialFullMap[emptyRuleItem.PlatformId]["privacy_link"]
						if privacyLinkOk {
							materialFullMap[emptyRuleItem.PlatformId]["privacy_link"] = append(privacyLinkVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["privacy_link"] = append(privacyLinkArr, emptyItem.Value)
						}
					case "permission_url":
						permissionUrlVal, permissionUrlOk := materialFullMap[emptyRuleItem.PlatformId]["permission_url"]
						if permissionUrlOk {
							materialFullMap[emptyRuleItem.PlatformId]["permission_url"] = append(permissionUrlVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["permission_url"] = append(permissionUrlArr, emptyItem.Value)
						}
					case "permisson":
						permissionVal, permissionOk := materialFullMap[emptyRuleItem.PlatformId]["permission"]
						if permissionOk {
							materialFullMap[emptyRuleItem.PlatformId]["permission"] = append(permissionVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["permission"] = append(permissionArr, emptyItem.Value)
						}
					case "appinfo":
						appInfoVal, appInfoOk := materialFullMap[emptyRuleItem.PlatformId]["app_info"]
						if appInfoOk {
							materialFullMap[emptyRuleItem.PlatformId]["app_info"] = append(appInfoVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["app_info"] = append(appInfoArr, emptyItem.Value)
						}
					case "package_size":
						packageSizeVal, packageSizeOk := materialFullMap[emptyRuleItem.PlatformId]["package_size"]
						if packageSizeOk {
							materialFullMap[emptyRuleItem.PlatformId]["package_size"] = append(packageSizeVal, emptyItem.Value)
						} else {
							materialFullMap[emptyRuleItem.PlatformId]["package_size"] = append(packageSizeArr, emptyItem.Value)
						}
					}
				}
			}
		}
	}

	uniquePlatformIdArray := removeDuplicates(platformIdArray)
	for _, platformID := range uniquePlatformIdArray {
		cacheKey := "go_material_full"
		if platformID != "0" {
			cacheKey = "go_material_full_" + platformID
		}
		mapVal, ok := materialFullMap[platformID]
		if ok {
			jsonStr, _ := json.Marshal(mapVal)
			_ = db.GlbBigCacheMinute.Set(cacheKey, jsonStr)
		}
	}
}

func removeDuplicates(nums []string) []string {
	result := make([]string, 0) // 创建新的切片来保存结果
	seen := map[string]bool{}   // 创建一个空的map来记录已经遇到过的数字

	for _, num := range nums {
		if !seen[num] { // 如果该数字还没有被添加进result或者seen中
			result = append(result, num) // 将其添加到result中
			seen[num] = true             // 标记这个数字为已经处理过了
		}
	}

	return result
}

// getRtbConfigFromMySQL ...
func getRtbConfigFromMySQL() {

	// rtb media
	var rtbMediaListArray []RtbMediaStu

	sqlMediaStr := "select IFNULL(a.id, 0), IFNULL(a.channel, ''), IFNULL(a.os, '') " +
		"from rtb_medias a where a.is_active = 1"

	mediaRows, err := db.GlbMySQLDb.Query(sqlMediaStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer mediaRows.Close()

	// 循环读取结果集中的数据
	for mediaRows.Next() {
		var rtbMediaItem RtbMediaStu
		err := mediaRows.Scan(&rtbMediaItem.MediaID, &rtbMediaItem.Channel, &rtbMediaItem.Os)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		if rtbMediaItem.MediaID == 0 || len(rtbMediaItem.Channel) == 0 ||
			len(rtbMediaItem.Os) == 0 {
			continue
		}

		if rtbMediaItem.Os == "0" {
			rtbMediaItem.Os = "android"
		} else if rtbMediaItem.Os == "1" {
			rtbMediaItem.Os = "ios"
		}
		rtbMediaListArray = append(rtbMediaListArray, rtbMediaItem)
	}

	// rtb pos
	var rtbPosListArray []RtbPosStu

	sqlPosStr := "select IFNULL(a.media_id, 0), IFNULL(b.channel, ''), IFNULL(b.os, ''), IFNULL(a.tagid, ''), IFNULL(a.sub_tagids, ''), IFNULL(a.sub_tagids_types, '')," +
		"IFNULL(a.sub_image_styleids, ''), IFNULL(a.sub_video_styleids, ''), " +
		"IFNULL(a.crowd_package_ids, ''), " +
		"IFNULL(a.image_virtual_crid, ''), IFNULL(a.video_virtual_crid, ''), " +
		"IFNULL(a.sub_dealid, ''), IFNULL(a.package_name, ''), IFNULL(b.is_exchange_model_make, 0), IFNULL(a.is_audit_need_styleid, 0), IFNULL(a.is_audit_to_server, 0), " +
		"IFNULL(a.is_audit, 0), IFNULL(a.is_audit_platform, ''), IFNULL(a.is_audit_image_styleid, ''), IFNULL(a.is_audit_video_styleid, ''), IFNULL(a.is_audit_ad_type, ''), IFNULL(a.is_fill_material, 0), " +
		"IFNULL(a.audit_jpgsuffix_key, ''), " +
		"IFNULL(a.is_custom_cpm_bid_floor, 0), " +
		"IFNULL(a.custom_cpm_bid_floor, 1), " +
		"IFNULL(a.app_id, ''), IFNULL(a.pos_id, ''), IFNULL(a.ks_ad_type, '') " +
		"from rtb_positions a INNER JOIN rtb_medias b where a.media_id = b.id and a.is_active = 1 and b.is_active = 1"

	posRows, err := db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var rtbPosItem RtbPosStu
		err := posRows.Scan(&rtbPosItem.MediaID, &rtbPosItem.Channel, &rtbPosItem.Os, &rtbPosItem.TagID, &rtbPosItem.SubTagIDs, &rtbPosItem.SubTagIDsTypes,
			&rtbPosItem.SubImageStyleIDs, &rtbPosItem.SubVideoStyleIDs,
			&rtbPosItem.CrowdPackageIDs,
			&rtbPosItem.ImageVirtualCrid, &rtbPosItem.VideoVirtualCrid,
			&rtbPosItem.SubDealIDs, &rtbPosItem.PackageName, &rtbPosItem.IsMediaExchangeModelMake, &rtbPosItem.IsTagIDAuditNeedStyleID, &rtbPosItem.IsTagIDAuditToServer,
			&rtbPosItem.IsPosAudit, &rtbPosItem.AuditPlatform, &rtbPosItem.AuditImageStyleID, &rtbPosItem.AuditVideoStyleID, &rtbPosItem.AuditAdType, &rtbPosItem.IsFillMaterial,
			&rtbPosItem.AuditJPGSuffixKey,
			&rtbPosItem.IsCustomCPMBidFloor,
			&rtbPosItem.CustomCPMBidFloor,
			&rtbPosItem.LocalAppID, &rtbPosItem.LocalPosID, &rtbPosItem.AdType)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		if rtbPosItem.MediaID == 0 || len(rtbPosItem.SubTagIDs) == 0 ||
			len(rtbPosItem.LocalAppID) == 0 || len(rtbPosItem.LocalPosID) == 0 {
			continue
		}

		if rtbPosItem.Os == "0" {
			rtbPosItem.Os = "android"
		} else if rtbPosItem.Os == "1" {
			rtbPosItem.Os = "ios"
		}

		rtbPosListArray = append(rtbPosListArray, rtbPosItem)
	}

	for _, mediaItem := range rtbMediaListArray {
		var tmpPosArray []RtbPosStu
		for _, posItem := range rtbPosListArray {
			if posItem.Channel == mediaItem.Channel && posItem.Os == mediaItem.Os {
				tmpPosArray = append(tmpPosArray, posItem)
			}
		}
		if len(tmpPosArray) > 0 {
			cacheKey := "go_rtb_config_" + mediaItem.Channel + "_" + mediaItem.Os

			tmpJSON, _ := json.Marshal(tmpPosArray)
			db.GlbBigCache.Set(cacheKey, tmpJSON)
		}
	}
}

// getSSPConfigFromMySQL ...
func getSSPConfigFromMySQL() {
	// shield rule
	var shieldRuleListArray []ShieldRuleStu
	sqlPosStr := "SELECT " +
		"IFNULL(a.shield_rule_id, ''), " +
		"IFNULL(a.shield_key, ''), " +
		"IFNULL(a.package_name, ''), " +
		"IFNULL(a.ad_url, ''), " +
		"IFNULL(a.material_url, ''), " +
		"IFNULL(a.exp_url, ''), " +
		"IFNULL(a.shield_rule_type, 0), " +
		"IFNULL(a.ad_shield_type, 0), " +
		"IFNULL(a.time_list, ''), " +
		"IFNULL(a.region_list, ''), " +
		"IFNULL(a.is_active, 1) " +
		"FROM ssp.shield_rules a"

	posRows, err := db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var shieldRuleItem ShieldRuleStu
		err := posRows.Scan(
			&shieldRuleItem.ShieldRuleID,
			&shieldRuleItem.ShieldKey,
			&shieldRuleItem.PackageName,
			&shieldRuleItem.AdURL,
			&shieldRuleItem.MaterialURL,
			&shieldRuleItem.ExpUrl,
			&shieldRuleItem.ShieldRuleType,
			&shieldRuleItem.AdShieldType,
			&shieldRuleItem.TimeList,
			&shieldRuleItem.RegionList,
			&shieldRuleItem.IsActive,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		// fmt.Println("shield_rule_id:", shieldRuleItem.ShieldRuleID)
		// fmt.Println("shield_key:", shieldRuleItem.ShieldKey)
		// fmt.Println("pacakge_name:", shieldRuleItem.PackageName)
		// fmt.Println("domain_name:", shieldRuleItem.DomainName)
		// fmt.Println("material:", shieldRuleItem.Material)
		shieldRuleListArray = append(shieldRuleListArray, shieldRuleItem)
	}

	var materialReplaceArray []MaterialReplace
	sqlPosStr = "SELECT material_replace_rule_id,replace_key,ad_url,material_url FROM ssp.material_replace_rules WHERE is_active = 1"
	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)
	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}
	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()
	// 循环读取结果集中的数据
	for posRows.Next() {
		var materialReplaceItem MaterialReplace
		err = posRows.Scan(
			&materialReplaceItem.MaterialReplaceRuleID,
			&materialReplaceItem.ReplaceKey,
			&materialReplaceItem.AdUrl,
			&materialReplaceItem.MaterialUrl,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		var materialReplaceGroupArray []MaterialReplaceGroup
		groupSqlString := `
		SELECT
			replace_type,
			material_type,
			replace_value
		FROM
			ssp.material_replace_group
		WHERE
			material_replace_rule_id = ?
	`
		replaceGroupRows, err := db.GlbMySQLDb.Query(groupSqlString, materialReplaceItem.MaterialReplaceRuleID)
		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}
		for replaceGroupRows.Next() {
			var materialReplaceGroupItem MaterialReplaceGroup
			err = replaceGroupRows.Scan(
				&materialReplaceGroupItem.ReplaceType,
				&materialReplaceGroupItem.MaterialType,
				&materialReplaceGroupItem.ReplaceValue,
			)
			if err != nil {
				fmt.Printf("scan failed, err:%v\n", err)
				return
			}
			materialReplaceGroupArray = append(materialReplaceGroupArray, materialReplaceGroupItem)
		}
		materialReplaceItem.ReplaceGroup = materialReplaceGroupArray
		materialReplaceArray = append(materialReplaceArray, materialReplaceItem)
		replaceGroupRows.Close()
	}

	// shield white rule
	var shieldWhiteRuleListArray []ShieldRuleStu
	sqlPosStr = "SELECT " +
		"IFNULL(a.shield_rule_id, ''), " +
		"IFNULL(a.shield_key, ''), " +
		"IFNULL(a.package_name, ''), " +
		"IFNULL(a.ad_url, ''), " +
		"IFNULL(a.material_url, ''), " +
		"IFNULL(a.exp_url, ''), " +
		"IFNULL(a.shield_rule_type, 0), " +
		"IFNULL(a.ad_shield_type, 0), " +
		"IFNULL(a.time_list, ''), " +
		"IFNULL(a.region_list, '') ," +
		"IFNULL(a.is_active, 1) " +
		"FROM ssp.shield_white_rules a"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var shieldRuleItem ShieldRuleStu
		err := posRows.Scan(
			&shieldRuleItem.ShieldRuleID,
			&shieldRuleItem.ShieldKey,
			&shieldRuleItem.PackageName,
			&shieldRuleItem.AdURL,
			&shieldRuleItem.MaterialURL,
			&shieldRuleItem.ExpUrl,
			&shieldRuleItem.ShieldRuleType,
			&shieldRuleItem.AdShieldType,
			&shieldRuleItem.TimeList,
			&shieldRuleItem.RegionList,
			&shieldRuleItem.IsActive,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		// fmt.Println("shield_rule_id:", shieldRuleItem.ShieldRuleID)
		// fmt.Println("shield_key:", shieldRuleItem.ShieldKey)
		// fmt.Println("pacakge_name:", shieldRuleItem.PackageName)
		// fmt.Println("domain_name:", shieldRuleItem.DomainName)
		// fmt.Println("material:", shieldRuleItem.Material)
		shieldWhiteRuleListArray = append(shieldWhiteRuleListArray, shieldRuleItem)
	}

	// sdk云控选项列表
	var sdkConfigListArray []SDKConfigStu
	sqlPosStr = "SELECT " +
		"IFNULL(a.config_id, ''), " +
		"IFNULL(a.interaction_type_list_json, ''), " +
		"IFNULL(a.yao_direction, 0), " +
		"IFNULL(a.sahua_config_list_json, ''), " +
		"IFNULL(a.sahua_image_trigger_time, ''), " +
		"IFNULL(a.sahua_video_trigger_time, ''), " +
		"IFNULL(a.sahua_image_duration_time, ''), " +
		"IFNULL(a.sahua_video_duration_time, ''), " +
		"IFNULL(a.is_download_dialog, 0), " +
		"IFNULL(a.is_clickview_download_compliance, 0), " +
		"IFNULL(a.is_adview_download_compliance, 0), " +
		"IFNULL(a.is_auto_play_mobile_network, 0), " +
		"IFNULL(a.is_yao_bg_disable_sensor, 0), " +
		"IFNULL(a.is_reward_video_exit_confirm, 1), " +
		"IFNULL(a.interaction_style, 0), " +
		"IFNULL(a.treasure_chest_config_list_json, ''), " +
		"IFNULL(a.is_magic, 0), " +
		"IFNULL(a.magic_type, 0), " +
		"IFNULL(a.magic_config_list_json, ''), " +
		"IFNULL(a.reward_video_type, 0), " +
		"IFNULL(a.reward_video_clk_guide, 0), " +
		"IFNULL(a.magic_close_replace_click_weight, 0), " +
		"IFNULL(a.slide_direction, 0), " +
		"IFNULL(a.magic_region, 0), " +
		////////////////////////////////////////////////////////////////////////////////////////////////////
		// 以下为new
		////////////////////////////////////////////////////////////////////////////////////////////////////
		"IFNULL(a.new_interaction_type_list_json, ''), " +
		"IFNULL(a.new_shake_direction, 0), " +
		"IFNULL(a.new_sahua_config_list_json, ''), " +
		"IFNULL(a.new_sahua_image_trigger_time, ''), " +
		"IFNULL(a.new_sahua_video_trigger_time, ''), " +
		"IFNULL(a.new_sahua_image_duration_time, ''), " +
		"IFNULL(a.new_sahua_video_duration_time, ''), " +
		"IFNULL(a.new_is_download_dialog, 0), " +
		"IFNULL(a.new_is_clickview_download_compliance, 0), " +
		"IFNULL(a.new_is_adview_download_compliance, 0), " +
		"IFNULL(a.new_is_auto_play_mobile_network, 0), " +
		"IFNULL(a.new_is_shake_bg_disable_sensor, 0), " +
		"IFNULL(a.new_is_reward_video_exit_confirm, 1), " +
		"IFNULL(a.new_interaction_style, 0), " +
		"IFNULL(a.new_treasure_chest_config_list_json, ''), " +
		"IFNULL(a.new_is_magic, 0), " +
		"IFNULL(a.new_magic_type, 0), " +
		"IFNULL(a.new_magic_config_list_json, ''), " +
		"IFNULL(a.new_reward_video_type, 0), " +
		"IFNULL(a.new_reward_video_clk_guide, 0), " +
		"IFNULL(a.new_magic_close_replace_click_weight, 0), " +
		"IFNULL(a.new_slide_direction, 0), " +
		"IFNULL(a.new_magic_region, 0) " +
		"FROM ssp.interaction_configs a where a.is_active = 1"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var sdkConfigItem SDKConfigStu
		err := posRows.Scan(
			&sdkConfigItem.ConfigID,
			&sdkConfigItem.InteractionTypeListJson,
			&sdkConfigItem.YaoDirection,
			&sdkConfigItem.SahuaConfigListJson,
			&sdkConfigItem.SahuaImageTriggerTime,
			&sdkConfigItem.SahuaVideoTriggerTime,
			&sdkConfigItem.SahuaImageDurationTime,
			&sdkConfigItem.SahuaVideoDurationTime,
			&sdkConfigItem.IsDownloadDialog,
			&sdkConfigItem.IsClickViewDownloadCompliance,
			&sdkConfigItem.IsAdViewDownloadCompliance,
			&sdkConfigItem.IsAutoPlayMobileNetwork,
			&sdkConfigItem.IsYaoBgDisableSensor,
			&sdkConfigItem.IsRewardVideoExitConfirm,
			&sdkConfigItem.InteractionStyle,
			&sdkConfigItem.TreasurechestConfigListJson,
			&sdkConfigItem.IsMagic,
			&sdkConfigItem.MagicType,
			&sdkConfigItem.MagicConfigListJson,
			&sdkConfigItem.RewardVideoType,
			&sdkConfigItem.RewardVideoClkGuide,
			&sdkConfigItem.MagicCloseReplaceClickWeight,
			&sdkConfigItem.SlideDirection,
			&sdkConfigItem.MagicRegion,
			////////////////////////////////////////////////////////////////////////////////////////////////////
			// 以下为new
			////////////////////////////////////////////////////////////////////////////////////////////////////
			&sdkConfigItem.NewInteractionTypeListJson,
			&sdkConfigItem.NewYaoDirection,
			&sdkConfigItem.NewSahuaConfigListJson,
			&sdkConfigItem.NewSahuaImageTriggerTime,
			&sdkConfigItem.NewSahuaVideoTriggerTime,
			&sdkConfigItem.NewSahuaImageDurationTime,
			&sdkConfigItem.NewSahuaVideoDurationTime,
			&sdkConfigItem.NewIsDownloadDialog,
			&sdkConfigItem.NewIsClickViewDownloadCompliance,
			&sdkConfigItem.NewIsAdViewDownloadCompliance,
			&sdkConfigItem.NewIsAutoPlayMobileNetwork,
			&sdkConfigItem.NewIsYaoBgDisableSensor,
			&sdkConfigItem.NewIsRewardVideoExitConfirm,
			&sdkConfigItem.NewInteractionStyle,
			&sdkConfigItem.NewTreasurechestConfigListJson,
			&sdkConfigItem.NewIsMagic,
			&sdkConfigItem.NewMagicType,
			&sdkConfigItem.NewMagicConfigListJson,
			&sdkConfigItem.NewRewardVideoType,
			&sdkConfigItem.NewRewardVideoClkGuide,
			&sdkConfigItem.NewMagicCloseReplaceClickWeight,
			&sdkConfigItem.NewSlideDirection,
			&sdkConfigItem.NewMagicRegion,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		sdkConfigListArray = append(sdkConfigListArray, sdkConfigItem)
	}

	// 读取替换库存储管理配置, 是否生成替换库
	var didStorageListArray []DIDStorageStu
	sqlPosStr = "SELECT " +
		"IFNULL(a.app_id, ''), " +
		"IFNULL(a.max_num, 0), " +
		"IFNULL(a.real_max_num, 0), " +
		"IFNULL(a.is_verify_parameter, 1) " +
		"FROM ssp.magic_didlib_storage_list a where a.status = 1 and a.type = 0"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var configItem DIDStorageStu
		err := posRows.Scan(
			&configItem.LocalAppID,
			&configItem.MaxNum,
			&configItem.RealMaxNum,
			&configItem.IsVerifyParameter,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}
		// fmt.Println("did storage localappid:", configItem.LocalAppID)
		// fmt.Println("did storage max_num:", configItem.MaxNum)
		// fmt.Println("did storage real_max_num:", configItem.RealMaxNum)
		if configItem.MaxNum > configItem.RealMaxNum {
			// fmt.Println("did storage localappid:", configItem.LocalAppID)
			// fmt.Println("did storage max_num:", configItem.MaxNum)
			// fmt.Println("did storage real_max_num:", configItem.RealMaxNum)
			didStorageListArray = append(didStorageListArray, configItem)
		}
	}

	// local pos
	var localPosListArray []LocalPosStu

	sqlPosStr = "SELECT a.id, IFNULL(a.type, 0), a.app_id, IFNULL(a.rel_width, 0), IFNULL(a.rel_height, 0), b.os, b.app_name, b.app_bundle_id, " +
		"IFNULL(a.pdd_styleids, ''), " +
		"IFNULL(b.sp_anticheat_h5, 0), " +
		"IFNULL(b.is_verify_param, 1), " +
		"IFNULL(a.sp_mh_landingpage, 0), " +
		"IFNULL(a.download_type, 0), " +
		"IFNULL(b.sp_splash_click, 0), IFNULL(b.sp_native_download, 0), " +
		"IFNULL(a.diffluence_range, 0), IFNULL(a.diffluence_strategy, 0), " +
		// "IFNULL(b.sp_reward_compliance, 0), " +
		// "IFNULL(b.reward_skip_time, 0), " +
		"IFNULL(a.api_retain_status, 0), " +
		"IFNULL(b.sp_extra_clipboard, 0), IFNULL(b.extra_clipboard_type, 0), IFNULL(b.sp_extra_notification, 0), IFNULL(b.sp_extra_wakeup, 0), " +
		"IFNULL(b.is_lenovo_secure_download, 0), " +
		"IFNULL(b.is_fix_ua, 0), " +
		"IFNULL(b.ua_noreplace_demand_platform_ids, ''), " +
		"IFNULL(b.is_force_https, 0), " +
		"IFNULL(b.is_limit_frequency, 0), " +
		"IFNULL(b.limit_frequency_config, ''), " +
		"IFNULL(b.is_extra_limit_frequency, 0), IFNULL(b.extra_limit_frequency, ''), IFNULL(b.is_report_loss_price, 0), IFNULL(b.is_need_audit, 0), " +
		"IFNULL(a.is_need_audit, 0), " +
		"IFNULL(a.is_trace_report, 0), " +
		"IFNULL(b.is_verify_package, 1), " +
		"IFNULL(b.clickpoint_type, 0), " +
		"IFNULL(b.physical_pixel_platform_ids, ''), " +
		"IFNULL(b.is_co_dp_h5, 0), " +
		"IFNULL(b.dp_h5_type, 0), " +
		"IFNULL(b.dp_h5_platformids, ''), " +
		// "IFNULL(b.is_logic_pixel, 0), IFNULL(b.logic_pixel_list, ''), " +
		// "IFNULL(b.is_click_limit, 0), IFNULL(b.click_limit_list, ''), " +
		"IFNULL(a.is_material_filter_orientation, 0), " +
		"IFNULL(a.is_material_filter_duration, 0), IFNULL(a.material_filter_min_duration, ''), " +
		"IFNULL(a.material_filter_max_duration, ''), IFNULL(a.is_material_filter_fixed_duration, 0), " +
		"IFNULL(a.material_filter_fixed_durations, ''), " +
		"IFNULL(a.material_filter_sizes, ''), " +
		"a.is_active, IFNULL(a.api_timeout, 0), IFNULL(a.sdk_timeout, 0), IFNULL(a.material_type, 0), IFNULL(b.type, ''), " +
		"IFNULL(a.shield_rule_id, ''), IFNULL(b.shield_rule_id, ''), " +
		"IFNULL(a.material_replace_rule_id, ''), IFNULL(b.material_replace_rule_id, ''), " +
		"IFNULL(a.shield_white_rule_id, ''), IFNULL(b.shield_white_rule_id, ''), IFNULL(a.interaction_config_id, ''), " +
		"IFNULL(a.is_support_sdk_interaction, 0), IFNULL(a.sdk_interaction_support_type, ''), " +
		"IFNULL(a.is_radst, 0), " +
		"IFNULL(a.radst, ''), " +
		"IFNULL(b.click_delay_report_min_time, ''), " +
		"IFNULL(b.click_delay_report_max_time, ''), " +
		// "IFNULL(a.is_bidding, 0), " +
		"IFNULL(a.ecpm_type, 0), " +
		"IFNULL(a.ecpm, 0), " +
		"IFNULL(a.profit_rate, 0), " +
		"IFNULL(a.custom_cpm_bid_floor_platform_list, ''), " +
		"IFNULL(b.sdk_filter_api_weight, 100), " +
		"IFNULL(b.sdk_filter_sdk_weight, 0), " +
		"IFNULL(b.sdk_notification_pos_type_list, ''), " +
		"IFNULL(b.is_sdk_anticheat, 0), " +
		"IFNULL(b.is_sdk_anticheat_platformid, 0), " +
		"IFNULL(b.sdk_anticheat_platformids, ''), " +
		"IFNULL(b.sdk_anticheat_expose_type, 0), " +
		"IFNULL(b.sdk_anticheat_expose_value, ''), " +
		"IFNULL(b.sdk_anticheat_click_type, 0), " +
		"IFNULL(b.sdk_anticheat_click_value, ''), " +
		"IFNULL(b.is_manufacturer, 0), " +
		"IFNULL(b.is_quick_app, 0), " +
		"IFNULL(b.price_token, '') " +
		"FROM pos a INNER JOIN app b where a.is_active = 1 and a.app_id = b.id"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("local pos query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var localPos LocalPosStu
		err := posRows.Scan(&localPos.LocalPosID, &localPos.LocalPosType, &localPos.LocalAppID, &localPos.LocalPosWidth,
			&localPos.LocalPosHeight, &localPos.LocalOs, &localPos.LocalAppName,
			&localPos.LocalAppBundleID,
			&localPos.LocalPosPinDuoDuoStyleIDs,
			&localPos.LocalAppSupportAntiCheatH5,
			&localPos.LocalAppIsVerifyParam,
			&localPos.LocalPosMHLandPage,
			&localPos.LocalPosDownloadType,
			&localPos.LocalAppSplashClickRegion, &localPos.LocalAppNativeDownloadCompliance,
			&localPos.LocalPosDiffluenceRange, &localPos.LocalPosDiffluenceStrategy,
			// &localPos.LocalAppRewardCompliance,
			// &localPos.LocalAppRewardSkipTime,
			&localPos.LocalPosIsRetainActive,
			&localPos.LocalAppSupportExtraClipboard, &localPos.LocalAppExtraClipboardType, &localPos.LocalAppSupportExtraNotification, &localPos.LocalAppSupportExtraWakeUp,
			&localPos.LocalAppIsLenovoSecureDownload,
			&localPos.LocalAppIsFixUA,
			&localPos.LocalAppUANoReplaceDemandPlatformIDs,
			&localPos.LocalAppIsForceHttps,
			&localPos.LocalAppIsLimitFrequency,
			&localPos.LocalAppLimitFrequencyJson,
			&localPos.LocalAppIsExtraLimitFrequency, &localPos.LocalAppExtraLimitFrequency, &localPos.LocalAppIsReportPriceLoss, &localPos.LocalAppIsNeedAudit,
			&localPos.LocalPosIsNeedAudit,
			&localPos.LocalPosIsTraceReport,
			&localPos.LocalAppIsVerifyPackage,
			&localPos.LocalAppClickPointType,
			&localPos.LocalAppPhysicalPixelPlatformIDs,
			&localPos.LocalAppIsCoDPH5,
			&localPos.LocalAppDPH5Type,
			&localPos.LocalAppDPH5PlatformIDS,
			// &localPos.LocalAppIsLogicPixel, &localPos.LocalAppLogicPixelList,
			// &localPos.LocalAppIsClickLimit, &localPos.LocalAppClickLimitList,
			&localPos.LocalPosIsMaterialFilterOrientation,
			&localPos.LocalPosIsMaterialFilterDuration, &localPos.LocalPosMaterialFilterMinDuration,
			&localPos.LocalPosMaterialFilterMaxDuration, &localPos.LocalPosIsMaterialFilterFixedDuration,
			&localPos.LocalPosMaterialFilterFixedDurations,
			&localPos.LocalPosMaterialFilterSizes,
			&localPos.LocalPosIsActive, &localPos.LocalPosTimeOut, &localPos.LocalPosSDKTimeOut, &localPos.LocalPosMaterialType, &localPos.LocalAppType,
			&localPos.LocalPosShieldRuleID, &localPos.LocalAppShieldRuleID,
			&localPos.LocalPosMaterialReplaceRuleID, &localPos.LocalAppMaterialReplaceRuleID,
			&localPos.LocalPosShieldWhiteRuleID, &localPos.LocalAppShieldWhiteRuleID,
			&localPos.LocalPosInteractionConfigID,
			&localPos.LocalPosIsSupportSDKInteraction, &localPos.LocalPosSDKInteractionSupportTypes,
			&localPos.LocalPosIsRadst,
			&localPos.LocalPosRadst,
			&localPos.LocalAppClickDelayReportMinTime,
			&localPos.LocalAppClickDelayReportMaxTime,
			// &localPos.LocalPosIsBidding,
			&localPos.LocalPosEcpmType,
			&localPos.LocalPosEcpm,
			&localPos.LocalPosProfitRate,
			&localPos.LocalPosCustomCPMBidFloorPlatformList,
			&localPos.LocalAppSDKFilterApiWeight,
			&localPos.LocalAppSDKFilterSDKWeight,
			&localPos.LocalAppSDKNotificationPosTypeList,
			&localPos.LocalAppIsSDKAntiCheat,
			&localPos.LocalAppIsSDKAntiCheatPlatformIDs,
			&localPos.LocalAppSDKAntiCheatPlatformIDs,
			&localPos.LocalAppSDKAntiCheatExposeType,
			&localPos.LocalAppSDKAntiCheatExposeValue,
			&localPos.LocalAppSDKAntiCheatClickType,
			&localPos.LocalAppSDKAntiCheatClickValue,
			&localPos.LocalAppIsManufacturer,
			&localPos.LocalAppIsQuickApp,
			&localPos.LocalAppPriceToken,
		)
		if err != nil {
			fmt.Printf("local pos scan failed, err:%v\n", err)
			return
		}
		// local pos material replace rule
		if len(localPos.LocalPosMaterialReplaceRuleID) > 0 {
			for _, materialReplaceItem := range materialReplaceArray {
				if materialReplaceItem.MaterialReplaceRuleID == localPos.LocalPosMaterialReplaceRuleID {
					var localPosMaterialReplaceGroupArray []LocalAppPosMaterialReplaceGroup
					localPos.LocalPosMaterialReplaceRuleReplaceKey = materialReplaceItem.ReplaceKey
					localPos.LocalPosMaterialReplaceRuleAdUrl = materialReplaceItem.AdUrl
					localPos.LocalPosMaterialReplaceRuleMaterialUrl = materialReplaceItem.MaterialUrl
					for _, item := range materialReplaceItem.ReplaceGroup {
						var localPosMaterialReplaceGroupItem LocalAppPosMaterialReplaceGroup
						localPosMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleReplaceType = item.ReplaceType
						localPosMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleReplaceValue = item.ReplaceValue
						localPosMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleMaterialType = item.MaterialType
						localPosMaterialReplaceGroupArray = append(localPosMaterialReplaceGroupArray, localPosMaterialReplaceGroupItem)
					}
					localPos.LocalPosMaterialReplaceRuleMaterialReplaceGroup = localPosMaterialReplaceGroupArray
				}
			}
		}

		// local app material replace rule
		if len(localPos.LocalAppMaterialReplaceRuleID) > 0 {
			for _, materialReplaceItem := range materialReplaceArray {
				if materialReplaceItem.MaterialReplaceRuleID == localPos.LocalAppMaterialReplaceRuleID {
					var localAppMaterialReplaceGroupArray []LocalAppPosMaterialReplaceGroup
					localPos.LocalAppMaterialReplaceRuleReplaceKey = materialReplaceItem.ReplaceKey
					localPos.LocalAppMaterialReplaceRuleAdUrl = materialReplaceItem.AdUrl
					localPos.LocalAppMaterialReplaceRuleMaterialUrl = materialReplaceItem.MaterialUrl
					for _, item := range materialReplaceItem.ReplaceGroup {
						var localAppMaterialReplaceGroupItem LocalAppPosMaterialReplaceGroup
						localAppMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleReplaceType = item.ReplaceType
						localAppMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleReplaceValue = item.ReplaceValue
						localAppMaterialReplaceGroupItem.LocalAppPosMaterialReplaceRuleMaterialType = item.MaterialType
						localAppMaterialReplaceGroupArray = append(localAppMaterialReplaceGroupArray, localAppMaterialReplaceGroupItem)
					}
					localPos.LocalAppMaterialReplaceRuleMaterialReplaceGroup = localAppMaterialReplaceGroupArray
				}
			}
		}

		// local pos shield rule
		if len(localPos.LocalPosShieldRuleID) > 0 {
			for _, shieldRuleItem := range shieldRuleListArray {
				if shieldRuleItem.ShieldRuleID == localPos.LocalPosShieldRuleID {
					localPos.LocalPosShieldIsActive = shieldRuleItem.IsActive
					localPos.LocalPosShieldKey = shieldRuleItem.ShieldKey
					localPos.LocalPosShieldPackageName = shieldRuleItem.PackageName
					localPos.LocalPosShieldAdURL = shieldRuleItem.AdURL
					localPos.LocalPosShieldMaterialURL = shieldRuleItem.MaterialURL
					localPos.LocalPosShieldExpUrl = shieldRuleItem.ExpUrl
					localPos.LocalPosShieldRuleType = shieldRuleItem.ShieldRuleType
					localPos.LocalPosAdShieldType = shieldRuleItem.AdShieldType
					localPos.LocalPosShieldTimeList = shieldRuleItem.TimeList
					localPos.LocalPosShieldRegionList = shieldRuleItem.RegionList
				}
			}
		}

		// local app shield rule
		if len(localPos.LocalAppShieldRuleID) > 0 {
			for _, shieldRuleItem := range shieldRuleListArray {
				if shieldRuleItem.ShieldRuleID == localPos.LocalAppShieldRuleID {
					localPos.LocalAppShieldIsActive = shieldRuleItem.IsActive
					localPos.LocalAppShieldKey = shieldRuleItem.ShieldKey
					localPos.LocalAppShieldPackageName = shieldRuleItem.PackageName
					localPos.LocalAppShieldAdURL = shieldRuleItem.AdURL
					localPos.LocalAppShieldMaterialURL = shieldRuleItem.MaterialURL
					localPos.LocalAppShieldExpUrl = shieldRuleItem.ExpUrl
					localPos.LocalAppShieldRuleType = shieldRuleItem.ShieldRuleType
					localPos.LocalAppAdShieldType = shieldRuleItem.AdShieldType
					localPos.LocalAppShieldTimeList = shieldRuleItem.TimeList
					localPos.LocalAppShieldRegionList = shieldRuleItem.RegionList
				}
			}
		}

		// local pos shield white rule
		if len(localPos.LocalPosShieldWhiteRuleID) > 0 {
			for _, shieldRuleItem := range shieldWhiteRuleListArray {
				if shieldRuleItem.ShieldRuleID == localPos.LocalPosShieldWhiteRuleID {
					localPos.LocalPosShieldWhiteIsActive = shieldRuleItem.IsActive
					localPos.LocalPosShieldWhiteKey = shieldRuleItem.ShieldKey
					localPos.LocalPosShieldWhitePackageName = shieldRuleItem.PackageName
					localPos.LocalPosShieldWhiteAdURL = shieldRuleItem.AdURL
					localPos.LocalPosShieldWhiteMaterialURL = shieldRuleItem.MaterialURL
					localPos.LocalPosShieldWhiteExpUrl = shieldRuleItem.ExpUrl
					localPos.LocalPosShieldWhiteRuleType = shieldRuleItem.ShieldRuleType
					localPos.LocalPosAdShieldWhiteType = shieldRuleItem.AdShieldType
					localPos.LocalPosShieldWhiteTimeList = shieldRuleItem.TimeList
					localPos.LocalPosShieldWhiteRegionList = shieldRuleItem.RegionList
				}
			}
		}

		// local app shield white rule
		if len(localPos.LocalAppShieldWhiteRuleID) > 0 {
			for _, shieldRuleItem := range shieldWhiteRuleListArray {
				if shieldRuleItem.ShieldRuleID == localPos.LocalAppShieldWhiteRuleID {
					localPos.LocalAppShieldWhiteIsActive = shieldRuleItem.IsActive
					localPos.LocalAppShieldWhiteKey = shieldRuleItem.ShieldKey
					localPos.LocalAppShieldWhitePackageName = shieldRuleItem.PackageName
					localPos.LocalAppShieldWhiteAdURL = shieldRuleItem.AdURL
					localPos.LocalAppShieldWhiteMaterialURL = shieldRuleItem.MaterialURL
					localPos.LocalAppShieldWhiteMaterialURL = shieldRuleItem.ExpUrl
					localPos.LocalAppShieldWhiteRuleType = shieldRuleItem.ShieldRuleType
					localPos.LocalAppAdShieldWhiteType = shieldRuleItem.AdShieldType
					localPos.LocalAppShieldWhiteTimeList = shieldRuleItem.TimeList
					localPos.LocalAppShieldWhiteRegionList = shieldRuleItem.RegionList
				}
			}
		}

		// local pos sdk云控
		for _, sdkConfigItem := range sdkConfigListArray {
			if sdkConfigItem.ConfigID == localPos.LocalPosInteractionConfigID {
				localPos.LocalPosInteractionTypeListJson = sdkConfigItem.InteractionTypeListJson
				localPos.LocalPosYaoDirection = sdkConfigItem.YaoDirection
				localPos.LocalPosSahuaConfigListJson = sdkConfigItem.SahuaConfigListJson
				localPos.LocalPosSahuaImageTriggerTime = sdkConfigItem.SahuaImageTriggerTime
				localPos.LocalPosSahuaVideoTriggerTime = sdkConfigItem.SahuaVideoTriggerTime
				localPos.LocalPosSahuaImageDurationTime = sdkConfigItem.SahuaImageDurationTime
				localPos.LocalPosSahuaVideoDurationTime = sdkConfigItem.SahuaVideoDurationTime
				localPos.LocalPosIsDownloadDialog = sdkConfigItem.IsDownloadDialog
				localPos.LocalPosIsAutoPlayMobileNetwork = sdkConfigItem.IsAutoPlayMobileNetwork
				localPos.LocalPosIsClickViewDownloadCompliance = sdkConfigItem.IsClickViewDownloadCompliance
				localPos.LocalPosIsAdViewDownloadCompliance = sdkConfigItem.IsAdViewDownloadCompliance
				localPos.LocalPosIsYaoBgDisableSensor = sdkConfigItem.IsYaoBgDisableSensor
				localPos.LocalPosIsRewardVideoExitConfirm = sdkConfigItem.IsRewardVideoExitConfirm
				localPos.LocalPosInteractionStyle = sdkConfigItem.InteractionStyle
				localPos.LocalPosTreasurechestConfigListJson = sdkConfigItem.TreasurechestConfigListJson
				localPos.LocalPosIsMagic = sdkConfigItem.IsMagic
				localPos.LocalPosMagicType = sdkConfigItem.MagicType
				localPos.LocalPosMagicConfigListJson = sdkConfigItem.MagicConfigListJson
				localPos.LocalPosRewardVideoType = sdkConfigItem.RewardVideoType
				localPos.LocalPosRewardVideoClkGuide = sdkConfigItem.RewardVideoClkGuide
				localPos.LocalPosMagicCloseReplaceClickWeight = sdkConfigItem.MagicCloseReplaceClickWeight
				localPos.LocalPosSlideDirection = sdkConfigItem.SlideDirection
				localPos.LocalPosMagicRegion = sdkConfigItem.MagicRegion
				////////////////////////////////////////////////////////////////////////////////////////////////////
				// 以下为new
				////////////////////////////////////////////////////////////////////////////////////////////////////
				localPos.LocalPosNewInteractionTypeListJson = sdkConfigItem.NewInteractionTypeListJson
				localPos.LocalPosNewYaoDirection = sdkConfigItem.NewYaoDirection
				localPos.LocalPosNewSahuaConfigListJson = sdkConfigItem.NewSahuaConfigListJson
				localPos.LocalPosNewSahuaImageTriggerTime = sdkConfigItem.NewSahuaImageTriggerTime
				localPos.LocalPosNewSahuaVideoTriggerTime = sdkConfigItem.NewSahuaVideoTriggerTime
				localPos.LocalPosNewSahuaImageDurationTime = sdkConfigItem.NewSahuaImageDurationTime
				localPos.LocalPosNewSahuaVideoDurationTime = sdkConfigItem.NewSahuaVideoDurationTime
				localPos.LocalPosNewIsDownloadDialog = sdkConfigItem.NewIsDownloadDialog
				localPos.LocalPosNewIsAutoPlayMobileNetwork = sdkConfigItem.NewIsAutoPlayMobileNetwork
				localPos.LocalPosNewIsClickViewDownloadCompliance = sdkConfigItem.NewIsClickViewDownloadCompliance
				localPos.LocalPosNewIsAdViewDownloadCompliance = sdkConfigItem.NewIsAdViewDownloadCompliance
				localPos.LocalPosNewIsYaoBgDisableSensor = sdkConfigItem.NewIsYaoBgDisableSensor
				localPos.LocalPosNewIsRewardVideoExitConfirm = sdkConfigItem.NewIsRewardVideoExitConfirm
				localPos.LocalPosNewInteractionStyle = sdkConfigItem.NewInteractionStyle
				localPos.LocalPosNewTreasurechestConfigListJson = sdkConfigItem.NewTreasurechestConfigListJson
				localPos.LocalPosNewIsMagic = sdkConfigItem.NewIsMagic
				localPos.LocalPosNewMagicType = sdkConfigItem.NewMagicType
				localPos.LocalPosNewMagicConfigListJson = sdkConfigItem.NewMagicConfigListJson
				localPos.LocalPosNewRewardVideoType = sdkConfigItem.NewRewardVideoType
				localPos.LocalPosNewRewardVideoClkGuide = sdkConfigItem.NewRewardVideoClkGuide
				localPos.LocalPosNewMagicCloseReplaceClickWeight = sdkConfigItem.NewMagicCloseReplaceClickWeight
				localPos.LocalPosNewSlideDirection = sdkConfigItem.NewSlideDirection
				localPos.LocalPosNewMagicRegion = sdkConfigItem.NewMagicRegion
			}
		}

		// local app是否存储替换库
		for _, didStorageItem := range didStorageListArray {
			if didStorageItem.LocalAppID == localPos.LocalAppID {
				localPos.LocalAppIsDIDStorage = 1
				localPos.LocalAppIsDIDStorageVerifyParameter = didStorageItem.IsVerifyParameter
			}
		}

		localPosListArray = append(localPosListArray, localPos)
	}

	// platform pos
	var platformPosListArray []PlatformPosStu

	sqlPosStr = "SELECT a.id, a.app_id, b.media_id, IFNULL(b.corp_id, 0), " +
		"IFNULL(b.pattern, ''), IFNULL(b.protocol_type, 0), b.app_bundle_id, IFNULL(a.styleids, ''), b.os, a.width, a.height, " +
		"IFNULL(b.union_baidu_media_id, ''), " +
		"IFNULL(a.pos_key, ''), " +
		"IFNULL(b.app_name, ''), IFNULL(b.app_version, ''), IFNULL(b.app_key, ''), IFNULL(b.app_secret, ''), " +
		"IFNULL(b.pdd_channel, ''), " +
		"a.type, IFNULL(b.up_url, ''), IFNULL(b.up_debug_url, ''), IFNULL(a.is_debug, 0), " +
		"IFNULL(a.is_tmax, 0), " +
		"IFNULL(a.tmax_value, ''), " +
		"IFNULL(a.is_xy_replaced, 0), " +
		"IFNULL(a.xy_replaced_type, 0), " +
		"IFNULL(a.clickpoint_lib_key, ''), " +
		"IFNULL(a.click_point_lib_config, ''), " +
		"IFNULL(a.is_xy_replaced_lib_offset, 0), " +
		"IFNULL(a.is_replace_xy_default, 0), " +
		"IFNULL(a.replace_xy_default, ''), " +
		"IFNULL(a.is_adid_replace_xy, 1), " +
		"IFNULL(a.replace_adid_sim_xy_default, 0), " +
		"IFNULL(a.replace_adid_sim_xy_default_value, ''), " +
		"IFNULL(a.is_only_osv_more10, 0), IFNULL(a.is_hms_core, 0), IFNULL(a.direction, 0), IFNULL(a.material_type, 0), " +
		"IFNULL(b.filter_ua, 1), IFNULL(b.filter_illegal_ua, 0), IFNULL(b.is_active, 0), IFNULL(a.is_active, 0), " +
		"IFNULL(b.is_report_ua, 0), " +
		"IFNULL(b.ios_report_main_parameter, ''), " +
		"IFNULL(b.ios_report_sub_parameter_1, ''), " +
		"IFNULL(b.ios_report_sub_parameter_2, ''), " +
		"IFNULL(b.report_caid_type, 0), " +
		"IFNULL(b.single_caid_version, ''), " +
		"IFNULL(b.is_report_applist, 0), " +
		"IFNULL(b.is_report_android_id, 1), " +
		"IFNULL(a.is_report_sld, 1), IFNULL(a.sld_type, 0), " +
		"IFNULL(b.is_report_idfa_md5, 0), " +
		"IFNULL(a.is_skip_report_click, 0), " +
		"IFNULL(a.skip_report_click_config, ''), " +
		"IFNULL(a.expected_click_rate, 0), " +
		"IFNULL(a.expected_skip_report_click_config, ''), " +
		"IFNULL(b.is_support_app_store, 1), " +
		"IFNULL(b.is_support_quick_app, 0), " +
		"IFNULL(b.req_filter_weight, 0), " +
		"IFNULL(a.is_req_limit_frequency, 0), " +
		"IFNULL(a.req_limit_frequency_config, ''), " +
		"IFNULL(a.req_limit_frequency_white_media_config, ''), " +
		"IFNULL(a.is_discard_deeplink, 0), " +
		"IFNULL(a.discard_deeplink_config, ''), " +
		"IFNULL(a.is_third_monitor, 0), " +
		"IFNULL(a.third_monitor_white_media_config, ''), " +
		"IFNULL(a.third_monitor_exp_link, ''), " +
		"IFNULL(a.third_monitor_clk_link, ''), " +
		"IFNULL(b.is_limit_ip_req, 0), " +
		"IFNULL(b.max_num_limit_ip_req, ''), " +
		"IFNULL(b.limit_ip_req_rule, ''), " +
		"IFNULL(b.is_limit_ip_exp, 0), " +
		"IFNULL(b.max_num_limit_ip_exp, ''), " +
		"IFNULL(b.is_limit_ip_clk, 0), " +
		"IFNULL(b.max_num_limit_ip_clk, ''), " +
		"IFNULL(b.is_limit_did_req, 0), " +
		"IFNULL(b.max_num_limit_did_req, ''), " +
		"IFNULL(b.is_limit_did_exp, 0), " +
		"IFNULL(b.max_num_limit_did_exp, ''), " +
		"IFNULL(b.is_limit_did_clk, 0), " +
		"IFNULL(b.max_num_limit_did_clk, ''), " +
		"IFNULL(b.is_limit_did_req_interval, 0), " +
		"IFNULL(b.max_num_limit_did_req_interval, ''), " +
		"IFNULL(b.is_limit_did_exp_interval, 0), " +
		"IFNULL(b.max_num_limit_did_exp_interval, ''), " +
		"IFNULL(b.is_limit_did_clk_interval, 0), " +
		"IFNULL(b.max_num_limit_did_clk_interval, ''), " +
		"IFNULL(b.app_sha1, ''), IFNULL(b.itunes_id, ''), IFNULL(b.adx_name, ''), IFNULL(b.adx_password, ''), " +
		"IFNULL(b.publisher_id, ''), IFNULL(b.key_id, ''), IFNULL(b.sign_key, ''), " +
		"IFNULL(a.manufacturer_list, ''), IFNULL(b.is_price_encrypt, 0), IFNULL(b.price_encrypt, ''), IFNULL(b.price_encrypt_2, ''), " +
		"IFNULL(a.req_pos_types, ''), " +
		"IFNULL(b.is_replace_did, 0), IFNULL(b.is_replace_did_ua, 1), " +
		"IFNULL(b.replace_no_ratio, 0), " +
		"IFNULL(b.replace_white_media, ''), " +
		"IFNULL(b.replace_white_media_platform_pos, ''), " +
		"IFNULL(b.magic_replaced_didlib_md5_key, ''), " +
		"IFNULL(b.is_replace_app_bundle_id, 0), " +
		"IFNULL(b.replace_app_bundle_id_config, ''), " +
		"IFNULL(b.custom_app_bundle_id_type, 0), " +
		"IFNULL(b.custom_app_bundle_id, ''), " +
		"IFNULL(b.max_dau_type, 0), IFNULL(b.max_dau, 0), IFNULL(b.max_dau_random, 0), " +
		"IFNULL(a.support_querys_pkgs_type, 0), " +
		"IFNULL(a.is_support_querys, 0), IFNULL(a.support_querys_type, 0), " +
		"IFNULL(a.is_support_pkgs, 0), IFNULL(a.support_pkgs_type, 0), " +
		"IFNULL(b.is_crowd_package, 0), IFNULL(b.crowd_package_type, 0), IFNULL(b.crowd_package_white_media_config, ''), IFNULL(b.crowd_package_id, ''), " +
		"IFNULL(b.crowd_package_failed_config, ''), " +
		"IFNULL(b.is_deeplink_failed, 0), IFNULL(b.is_clickxy_fix, 0), IFNULL(b.clickxy_fix_ratio, 0), " +
		"IFNULL(b.is_mh_landingpage, 0), " +
		"IFNULL(b.report_win_config, 0), " +
		"IFNULL(b.is_report_win, 0), " +
		"IFNULL(b.report_win_type, 0), " +
		"IFNULL(b.report_win_min_weight, ''), " +
		"IFNULL(b.report_win_max_weight, ''), " +
		"IFNULL(b.is_report_loss, 0), " +
		"IFNULL(b.report_loss_min_weight, ''), " +
		"IFNULL(b.report_loss_max_weight, ''), " +
		"IFNULL(b.is_report_loss_price, 0), " +
		"IFNULL(b.is_report_loss_price_title, 0), " +
		"IFNULL(b.is_report_loss_price_publisher, 0), " +
		"IFNULL(b.is_report_loss_price_platform_name, 0), " +
		"IFNULL(b.report_loss_price_csj_weight, ''), " +
		"IFNULL(b.report_loss_price_gdt_weight, ''), " +
		"IFNULL(b.report_loss_price_baidu_weight, ''), " +
		"IFNULL(b.report_loss_price_other_weight, ''), " +
		"IFNULL(b.is_report_loss_price_exp, 0), " +
		"IFNULL(b.report_loss_price_exp_min_weight, ''), " +
		"IFNULL(b.report_loss_price_exp_max_weight, ''), " +
		"IFNULL(b.is_report_loss_price_clk, 0), " +
		"IFNULL(b.report_loss_price_clk_min_weight, ''), " +
		"IFNULL(b.report_loss_price_clk_max_weight, ''), " +
		"IFNULL(b.is_limit_ip_dau, 0), " +
		"IFNULL(b.is_replace_did_max_num, 0), " +
		"IFNULL(b.max_num_replace_did, 0), " +
		"IFNULL(b.is_replace_did_diff_region, 0), " +
		"IFNULL(b.max_num_replace_did_diff_region, 0), " +
		"IFNULL(b.is_replace_did_ip, 0), " +
		"IFNULL(b.replace_did_ip_ttl, 0), " +
		"IFNULL(b.max_num_replace_did_ip, 0), " +
		"IFNULL(b.is_report_virtual_applist, 0), " +
		"IFNULL(b.virtual_pkg_num, ''), " +
		"IFNULL(b.virtual_pkg_weight, ''), " +
		"IFNULL(b.virtual_pkg_lib, ''), " +
		"IFNULL(a.ecpm_type, 0), " +
		"IFNULL(a.ecpm, 0), " +
		"IFNULL(a.max_exp_limit_num, 1), " +
		"IFNULL(a.max_click_limit_num, 1) " +
		"FROM platform_pos a INNER JOIN platform_app b where a.app_id = b.id and a.media_id = b.media_id and a.os = b.os and a.app_bundle_id = b.app_bundle_id and b.is_active = 1 and a.is_active = 1"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("platform pos query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var platformPos PlatformPosStu
		err := posRows.Scan(&platformPos.PlatformPosID, &platformPos.PlatformAppID, &platformPos.PlatformMediaID, &platformPos.PlatformAppCorpID,
			&platformPos.PlatformAppType, &platformPos.PlatformAppProtocolType, &platformPos.PlatformAppBundle, &platformPos.PlatformPosStyleIDs, &platformPos.PlatformOs, &platformPos.PlatformPosWidth, &platformPos.PlatformPosHeight,
			&platformPos.PlatformAppUnionBaiduMediaID,
			&platformPos.PlatformPosKey,
			&platformPos.PlatformAppName, &platformPos.PlatformAppVersion, &platformPos.PlatformAppKey, &platformPos.PlatformAppSecret,
			&platformPos.PlatformAppPinDuoDuoChannel,
			&platformPos.PlatformPosType, &platformPos.PlatformAppUpURL, &platformPos.PlatformAppUpDebugURL, &platformPos.PlatformPosIsDebug,
			&platformPos.PlatformPosIsTMax,
			&platformPos.PlatformPosTMaxValue,
			&platformPos.PlatformPosIsReplaceXY,
			&platformPos.PlatformPosReplaceXYType,
			&platformPos.PlatformPosClickPointLibKey,
			&platformPos.PlatformPosClickPointLibConfigJson,
			&platformPos.PlatformPosIsReplaceXYLibOffset,
			&platformPos.PlatformPosIsReplaceXYDefault,
			&platformPos.PlatformPosReplaceXYDefault,
			&platformPos.PlatformPosIsAdIDReplaceXY,
			&platformPos.PlatformPosReplaceAdIDSimXYDefault,
			&platformPos.PlatformPosReplaceAdIDSimXYDefaultValue,
			&platformPos.PlatformPosIsOnlyMore10, &platformPos.PlatformPosIsHmsCore, &platformPos.PlatformPosDirection, &platformPos.PlatformPosMaterialType,
			&platformPos.PlatformAppFilterUa, &platformPos.PlatformAppFilterIllegalUa, &platformPos.PlatformAppIsActive, &platformPos.PlatformPosIsActive,
			&platformPos.PlatformAppIsReportUa,
			&platformPos.PlatformAppIOSReportMainParameter,
			&platformPos.PlatformAppIOSReportSubParameter1,
			&platformPos.PlatformAppIOSReportSubParameter2,
			&platformPos.PlatformAppReportCaidType,
			&platformPos.PlatformAppSingleCaidVersion,
			&platformPos.PlatformAppIsReportAppList,
			&platformPos.PlatformAppIsReportAndroidID,
			&platformPos.PlatformPosIsReportSLD, &platformPos.PlatformPosSLDType,
			&platformPos.PlatformAppIsReportIDFAMd5,
			&platformPos.PlatformPosIsSkipReportClick,
			&platformPos.PlatformPosSkipReportClickConfigJson,
			&platformPos.PlatformPosExpectedClickRate,
			&platformPos.PlatformPosExpectedSkipReportClickConfigJson,
			&platformPos.PlatformAppIsSupportAppStore,
			&platformPos.PlatformAppIsSupportQuickApp,
			&platformPos.PlatformAppReqFilterWeight,
			&platformPos.PlatformPosIsReqLimitFrequency,
			&platformPos.PlatformPosReqLimitFrequencyConfigJson,
			&platformPos.PlatformPosReqLimitFrequencyWhiteMediasConfigJson,
			&platformPos.PlatformPosIsDiscardDeeplink,
			&platformPos.PlatformPosDiscardDeeplinkConfigJson,
			&platformPos.PlatformPosIsThirdMonitor,
			&platformPos.PlatformPosThirdMonitorWhiteMediasConfigJson,
			&platformPos.PlatformPosThirdMonitorExpLinkJson,
			&platformPos.PlatformPosThirdMonitorClkLinkJson,
			&platformPos.PlatformAppIsLimitIPReq,
			&platformPos.PlatformAppMaxNumLimitIPReq,
			&platformPos.PlatformAppLimitIPReqRuleJson,
			&platformPos.PlatformAppIsLimitIPExp,
			&platformPos.PlatformAppMaxNumLimitIPExp,
			&platformPos.PlatformAppIsLimitIPClk,
			&platformPos.PlatformAppMaxNumLimitIPClk,
			&platformPos.PlatformAppIsLimitDIDReq,
			&platformPos.PlatformAppMaxNumLimitDIDReq,
			&platformPos.PlatformAppIsLimitDIDExp,
			&platformPos.PlatformAppMaxNumLimitDIDExp,
			&platformPos.PlatformAppIsLimitDIDClk,
			&platformPos.PlatformAppMaxNumLimitDIDClk,
			&platformPos.PlatformAppIsLimitDIDReqInterval,
			&platformPos.PlatformAppMaxNumLimitDIDReqInterval,
			&platformPos.PlatformAppIsLimitDIDExpInterval,
			&platformPos.PlatformAppMaxNumLimitDIDExpInterval,
			&platformPos.PlatformAppIsLimitDIDClkInterval,
			&platformPos.PlatformAppMaxNumLimitDIDClkInterval,
			&platformPos.PlatformAppSHA1, &platformPos.PlatformAppItunesID, &platformPos.PlatformAppAdxName, &platformPos.PlatformAppAdxPassword,
			&platformPos.PlatformAppPublisherID,
			&platformPos.PlatformAppKeyID,
			&platformPos.PlatformAppSignKey,
			&platformPos.PlatformPosManufacturerList, &platformPos.PlatformAppIsPriceEncrypt, &platformPos.PlatformAppPriceEncrypt, &platformPos.PlatformAppPriceEncrypt2,
			&platformPos.PlatformPosReqPosTypes,
			&platformPos.PlatformAppIsReplaceDID, &platformPos.PlatformAppIsReplaceDIDUa,
			&platformPos.PlatformAppReplaceNoRatio,
			&platformPos.PlatformAppReplaceWhiteMedias,
			&platformPos.PlatformAppReplaceWhiteMediasPlatformPos,
			&platformPos.PlatformAppMagicReplaceDIDLibMd5Key,
			&platformPos.PlatformAppIsReplaceAppBundleID,
			&platformPos.PlatformAppReplaceAppBundleIDJsonConfig,
			&platformPos.PlatformAppCustomReplaceAppBundleIDType,
			&platformPos.PlatformAppCustomReplaceAppBundleID,
			&platformPos.PlatformAppMaxDAUType, &platformPos.PlatformAppMaxDAU, &platformPos.PlatformAppMaxDAURandom,
			&platformPos.PlatformPosSupportQuerysPkgsType,
			&platformPos.PlatformPosIsSupportQuerys,
			&platformPos.PlatformPosSupportQuerysType,
			&platformPos.PlatformPosIsSupportPkgs,
			&platformPos.PlatformPosSupportPkgsType,
			&platformPos.PlatformAppIsCrowdPackage, &platformPos.PlatformAppCrowdPackageType, &platformPos.PlatformAppCrowdPackageWhiteMediasConfigJson, &platformPos.PlatformAppCrowdPackageID,
			&platformPos.PlatformAppCrowdPackageFailedMediasConfigJson,
			&platformPos.PlatformAppIsDeepLinkFailed,
			&platformPos.PlatformAppIsClickXYFix,
			&platformPos.PlatformAppClickXYFixRatio,
			&platformPos.PlatformAppIsMaplehazeLandingPage,
			&platformPos.PlatformAppReportWinConfig,
			&platformPos.PlatformAppIsReportWin,
			&platformPos.PlatformAppReportWinType,
			&platformPos.PlatformAppReportWinMinWeight,
			&platformPos.PlatformAppReportWinMaxWeight,
			&platformPos.PlatformAppIsReportLoss,
			&platformPos.PlatformAppReportLossMinWeight,
			&platformPos.PlatformAppReportLossMaxWeight,
			&platformPos.PlatformAppIsReportLossPrice,
			&platformPos.PlatformAppIsReportLossPriceTitle,
			&platformPos.PlatformAppIsReportLossPricePublisher,
			&platformPos.PlatformAppIsReportLossPricePlatformName,
			&platformPos.PlatformAppReportLossPriceCsjWeight,
			&platformPos.PlatformAppReportLossPriceGdtWeight,
			&platformPos.PlatformAppReportLossPriceBaiduWeight,
			&platformPos.PlatformAppReportLossPriceOtherWeight,
			&platformPos.PlatformAppIsReportLossPriceExp,
			&platformPos.PlatformAppReportLossPriceExpMinWeight,
			&platformPos.PlatformAppReportLossPriceExpMaxWeight,
			&platformPos.PlatformAppIsReportLossPriceClk,
			&platformPos.PlatformAppReportLossPriceClkMinWeight,
			&platformPos.PlatformAppReportLossPriceClkMaxWeight,
			&platformPos.PlatformAppIsLimitIPDau,
			&platformPos.PlatformAppIsReplaceDIDMaxNum,
			&platformPos.PlatformAppMaxNumReplaceDID,
			&platformPos.PlatformAppIsReplaceDIDDiffRegion,
			&platformPos.PlatformAppMaxNumReplaceDIDDiffRegion,
			&platformPos.PlatformAppIsReplaceDIDIP,
			&platformPos.PlatformAppReplaceDIDIPTTL,
			&platformPos.PlatformAppMaxNumReplaceDIDIP,
			&platformPos.PlatformAppIsReportVirtualAppList,
			&platformPos.PlatformAppVirtualPkgNum,
			&platformPos.PlatformAppVirtualPkgWeight,
			&platformPos.PlatformAppVirtualPkgLib,
			&platformPos.PlatformPosEcpmType,
			&platformPos.PlatformPosEcpm,
			&platformPos.PlatformPosMaxExpLimit,
			&platformPos.PlatformPosMaxClickLimit,
		)
		if err != nil {
			fmt.Printf("platform pos scan failed, err:%v\n", err)
			return
		}

		if platformPos.PlatformAppIsCrowdPackage == 1 {
			// 人群包白名单
			if len(platformPos.PlatformAppCrowdPackageWhiteMediasConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformAppCrowdPackageWhiteMediasConfigJson), &platformPos.PlatformAppCrowdPackageWhiteMediasConfig)
			}
			// 人群包未命中时强制通过比例
			if len(platformPos.PlatformAppCrowdPackageFailedMediasConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformAppCrowdPackageFailedMediasConfigJson), &platformPos.PlatformAppCrowdPackageFailedMediasConfig)
				sort.Sort(PlatformPosWhiteMediaConfigSort(platformPos.PlatformAppCrowdPackageFailedMediasConfig))
			}
		}
		platformPos.PlatformAppCrowdPackageWhiteMediasConfigJson = ""
		platformPos.PlatformAppCrowdPackageFailedMediasConfigJson = ""

		// 请求限频白名单
		if platformPos.PlatformPosIsReqLimitFrequency == 1 {
			if len(platformPos.PlatformPosReqLimitFrequencyWhiteMediasConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosReqLimitFrequencyWhiteMediasConfigJson), &platformPos.PlatformPosReqLimitFrequencyWhiteMediasConfig)
			}
		}

		if len(platformPos.PlatformPosClickPointLibConfigJson) > 2 {
			json.Unmarshal([]byte(platformPos.PlatformPosClickPointLibConfigJson), &platformPos.PlatformPosClickPointLibConfig)
		}
		platformPos.PlatformPosClickPointLibConfigJson = ""

		// 忽略点击上报配置
		if platformPos.PlatformPosIsSkipReportClick == 1 {
			if len(platformPos.PlatformPosSkipReportClickConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosSkipReportClickConfigJson), &platformPos.PlatformPosSkipReportClickConfig)
				sort.Sort(PlatformPosWhiteMediaConfigSort(platformPos.PlatformPosSkipReportClickConfig))
			}
		} else if platformPos.PlatformPosIsSkipReportClick == 2 {
			if len(platformPos.PlatformPosExpectedSkipReportClickConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosExpectedSkipReportClickConfigJson), &platformPos.PlatformPosExpectedSkipReportClickConfig)
				sort.Sort(PlatformPosWhiteMediaConfigSort(platformPos.PlatformPosExpectedSkipReportClickConfig))
			}
		}
		platformPos.PlatformPosSkipReportClickConfigJson = ""
		platformPos.PlatformPosExpectedSkipReportClickConfigJson = ""

		if platformPos.PlatformPosIsDiscardDeeplink == 1 {
			if len(platformPos.PlatformPosDiscardDeeplinkConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosDiscardDeeplinkConfigJson), &platformPos.PlatformPosDiscardDeeplinkConfig)
				sort.Sort(PlatformPosWhiteMediaConfigSort(platformPos.PlatformPosDiscardDeeplinkConfig))
			}
		}
		platformPos.PlatformPosDiscardDeeplinkConfigJson = ""

		// 曝光点击监测链接
		if platformPos.PlatformPosIsThirdMonitor == 1 {
			if len(platformPos.PlatformPosThirdMonitorWhiteMediasConfigJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosThirdMonitorWhiteMediasConfigJson), &platformPos.PlatformPosThirdMonitorWhiteMediasConfig)
			}
			if len(platformPos.PlatformPosThirdMonitorExpLinkJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosThirdMonitorExpLinkJson), &platformPos.PlatformPosThirdMonitorExpLink)
			}
			if len(platformPos.PlatformPosThirdMonitorClkLinkJson) > 2 {
				json.Unmarshal([]byte(platformPos.PlatformPosThirdMonitorClkLinkJson), &platformPos.PlatformPosThirdMonitorClkLink)
			}
		}
		platformPos.PlatformPosThirdMonitorWhiteMediasConfigJson = ""
		platformPos.PlatformPosThirdMonitorExpLinkJson = ""
		platformPos.PlatformPosThirdMonitorClkLinkJson = ""

		platformPosListArray = append(platformPosListArray, platformPos)
	}

	for _, platformPosItem := range platformPosListArray {
		// if len(platformPosItem.PlatformAppIOSReportMainParameter) > 0 {
		// 	fmt.Println("a:", platformPosItem.PlatformAppID, platformPosItem.PlatformPosID)
		// 	fmt.Println("b:", platformPosItem.PlatformAppIOSReportMainParameter)
		// 	fmt.Println("c:", platformPosItem.PlatformAppIOSReportSubParameter1)
		// 	fmt.Println("d:", platformPosItem.PlatformAppIOSReportSubParameter2)
		// }
		if platformPosItem.PlatformMediaID == "99" {
			continue
		}

		cacheKeyPlatformPos := "go_platform_" + platformPosItem.PlatformAppID + "_" + platformPosItem.PlatformMediaID + "_" + platformPosItem.PlatformPosID

		platformPosJSON, _ := json.Marshal(platformPosItem)
		err = db.GlbBigCache.Set(cacheKeyPlatformPos, platformPosJSON)
		if err != nil {
			fmt.Println("set platform pos error:", platformPosItem.PlatformAppID, platformPosItem.PlatformPosID)
		}
		// 设置上游平台id
		cacheKeyPlatformID := "go_platform_id_" + platformPosItem.PlatformAppID + "_" + platformPosItem.PlatformPosID
		db.GlbBigCache.Set(cacheKeyPlatformID, []byte(platformPosItem.PlatformMediaID))
	}

	// category bidding price
	var categoryPriceListArray []CategoryStu

	sqlPosStr = "select a.pos_id, a.app_id, a.platform_pos_id, a.platform_app_id, a.platform_media_id, a.label, " +
		"IFNULL(a.priority, 0), " +
		"IFNULL(a.sub_api_weight, 0), " +
		"IFNULL(a.max_req_num, 0), IFNULL(a.is_max_did_req_num, 0), IFNULL(a.max_did_req_num, 0), IFNULL(a.max_exp_num, 0), IFNULL(a.max_clk_num, 0), " +
		"IFNULL(a.white_list, ''), IFNULL(a.black_list, ''), IFNULL(a.white_version, ''), IFNULL(a.black_version, ''), " +
		"IFNULL(a.manufacturer_list, ''), " +
		"IFNULL(a.req_limit_frequency, ''), " +
		"IFNULL(a.ks_price_type, ''), " +
		"IFNULL(a.is_crowd_package, 0), " +
		"IFNULL(a.crowd_package_type, 0), " +
		"IFNULL(a.crowd_package_id, ''), " +
		"IFNULL(a.bidding_floor_price, 0), " +
		"IFNULL(a.bidding_profit_rate, ''), " +
		"IFNULL(a.bidding_final_price, 0), " +
		"IFNULL(a.deal_id, ''), " +
		"IFNULL(a.is_force_win, 0), " +
		"IFNULL(a.bidding_is_active, 0) " +
		"from category a"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("catergory query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var item CategoryStu
		err := posRows.Scan(&item.LocalPosID, &item.LocalAppID, &item.PlatformPosID, &item.PlatformAppID, &item.PlatformMediaID, &item.Label,
			&item.Priority,
			&item.APISubWeight,
			&item.MaxReqNum, &item.IsMaxDIDReqNum, &item.MaxDIDReqNum, &item.MaxExpNum, &item.MaxClkNum,
			&item.WhiteList, &item.BlackList, &item.WhiteVersion, &item.BlackVersion,
			&item.ManufacturerList,
			&item.ReqLimitFrequencyJson,
			&item.KsPriceType,
			&item.IsCrowdPackage,
			&item.CrowdPackageType,
			&item.CrowdPackageID,
			&item.FloorPrice,
			&item.ProfitRate,
			&item.FinalPrice,
			&item.DealID,
			&item.IsForceWin,
			&item.IsActive,
		)
		if err != nil {
			fmt.Printf("catergory scan failed, err:%v\n", err)
			return
		}

		if item.PlatformMediaID == "99" {
			categoryPriceListArray = append(categoryPriceListArray, item)
		} else {
			isOK := false
			for _, platformPosItem := range platformPosListArray {
				if platformPosItem.PlatformAppID == item.PlatformAppID &&
					platformPosItem.PlatformPosID == item.PlatformPosID {
					isOK = true
					item.PlatformPosType = platformPosItem.PlatformPosType
					item.PlatformPosMaterialType = platformPosItem.PlatformPosMaterialType

					break
				}
			}
			if isOK {
				// fmt.Println("kbg_debug type:", item.LocalPosID, item.LocalAppID, item.PlatformPosID, item.PlatformAppID, item.PlatformMediaID, item.PlatformPosType, item.PlatformPosMaterialType)
				categoryPriceListArray = append(categoryPriceListArray, item)
			} else {
				// fmt.Println("kbg_debug wrong:", item.LocalAppID, item.LocalPosID, item.PlatformAppID, item.PlatformPosID, len(item.PlatformPosID))
			}
		}
	}

	// fmt.Println("kbg_debug category_length:", len(categoryListArray), len(categoryListArray1))

	for _, localPosItem := range localPosListArray {
		if len(localPosItem.LocalAppID) > 0 && len(localPosItem.LocalPosID) > 0 {
			cacheKey := "go_local_" + localPosItem.LocalAppID + "_" + localPosItem.LocalPosID

			localPosJSON, _ := json.Marshal(localPosItem)
			db.GlbBigCache.Set(cacheKey, localPosJSON)

			setCategoryPriceToCache(localPosItem, categoryPriceListArray)
		}
	}
}

// getSSPPlatformAppConfigFromMySQL ...
func getSSPPlatformAppConfigFromMySQL() {
	// platform pos
	sqlPosStr := "SELECT a.id," +
		"IFNULL(a.is_limit_ip_req, 0), " +
		"IFNULL(a.is_limit_ip_exp, 0), " +
		"IFNULL(a.is_limit_ip_clk, 0), " +
		"IFNULL(a.is_limit_did_req, 0), " +
		"IFNULL(a.is_limit_did_exp, 0), " +
		"IFNULL(a.is_limit_did_clk, 0), " +
		"IFNULL(a.is_limit_did_req_interval, 0), " +
		"IFNULL(a.is_limit_did_exp_interval, 0), " +
		"IFNULL(a.is_limit_did_clk_interval, 0) " +
		"FROM platform_app a where a.is_active = 1"

	posRows, err := db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("platform pos query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var platformPos PlatformPosStu
		err := posRows.Scan(&platformPos.PlatformAppID,
			&platformPos.PlatformAppIsLimitIPReq,
			&platformPos.PlatformAppIsLimitIPExp,
			&platformPos.PlatformAppIsLimitIPClk,
			&platformPos.PlatformAppIsLimitDIDReq,
			&platformPos.PlatformAppIsLimitDIDExp,
			&platformPos.PlatformAppIsLimitDIDClk,
			&platformPos.PlatformAppIsLimitDIDReqInterval,
			&platformPos.PlatformAppIsLimitDIDExpInterval,
			&platformPos.PlatformAppIsLimitDIDClkInterval,
		)
		if err != nil {
			fmt.Printf("platform pos scan failed, err:%v\n", err)
			return
		}

		cacheKeyPlatformApp := "go_platform_app_" + platformPos.PlatformAppID

		platformPosJSON, _ := json.Marshal(platformPos)
		err = db.GlbBigCache.Set(cacheKeyPlatformApp, platformPosJSON)
		if err != nil {
			fmt.Println("set platform app error:", cacheKeyPlatformApp)
		}
	}
}

// getSSPLocalAppConfigFromMySQL ...
func getSSPLocalAppConfigFromMySQL() {
	// local app
	var localAppListArray []LocalAppStu

	sqlPosStr := "SELECT a.id," +
		"IFNULL(a.is_get_imei, 1), " +
		"IFNULL(a.is_get_oaid, 1), " +
		"IFNULL(a.is_get_android_id, 0), " +
		"IFNULL(a.is_get_gps, 1), " +
		"IFNULL(a.is_get_applist, 0), " +
		"IFNULL(a.is_silence_monitor, 1), " +
		"IFNULL(a.is_upload_package_names, 0), " +
		"IFNULL(a.upload_package_names, ''), " +
		"IFNULL(a.ext_sdk_init_permissions, 1), " +
		"IFNULL(a.is_third_req_monitor, 0), " +
		"IFNULL(a.third_req_monitor, ''), " +
		"IFNULL(a.third_valid_req_monitor, ''), " +
		"IFNULL(a.third_valid_pos_ids, ''), " +
		"IFNULL(a.is_bootid, 0), " +
		"IFNULL(a.bootid_black_manufacturer_list, ''), " +
		"IFNULL(a.is_crash_protect, 0), " +
		"IFNULL(a.is_upload_crash, 0) " +
		"FROM app a where a.type = '1' and a.is_show = 1"

	posRows, err := db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var localApp LocalAppStu
		err := posRows.Scan(&localApp.LocalAppID,
			&localApp.LocalAppIsGetImei,
			&localApp.LocalAppIsGetOaid,
			&localApp.LocalAppIsGetAndroidID,
			&localApp.LocalAppIsGetGPS,
			&localApp.LocalAppIsGetAppList,
			&localApp.LocalAppIsSilenceMonitor,
			&localApp.LocalAppIsUploadPackageNames,
			&localApp.LocalAppUploadPackageNames,
			&localApp.ExtSdkInitPermissions,
			&localApp.LocalAppIsThirdReqMonitor,
			&localApp.LocalAppThirdReqMonitor,
			&localApp.LocalAppThirdValidReqMonitor,
			&localApp.LocalAppThirdValidPosIDs,
			&localApp.LocalAppIsBootID,
			&localApp.LocalAppBootIDBlackManufacturerList,
			&localApp.LocalAppIsCrashProtect,
			&localApp.LocalAppIsUploadCrash,
		)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		localAppListArray = append(localAppListArray, localApp)
	}

	for _, localAppItem := range localAppListArray {
		if len(localAppItem.LocalAppID) > 0 {
			cacheKey := "go_local_" + localAppItem.LocalAppID

			localPosJSON, _ := json.Marshal(localAppItem)
			err := db.GlbBigCache.Set(cacheKey, localPosJSON)
			if err != nil {
				fmt.Println("set app info:", cacheKey)
			}
		}
	}
}

func setCategoryPriceToCache(localPosItem LocalPosStu, categoryListArray []CategoryStu) {
	var categoryAPIListArray []CategoryStu
	var categorySDKListArray []CategoryStu

	for _, categoryItem := range categoryListArray {
		if localPosItem.LocalPosDiffluenceRange == 0 {
			if categoryItem.LocalAppID == localPosItem.LocalAppID && categoryItem.LocalPosID == localPosItem.LocalPosID && categoryItem.IsActive == 1 {
				if categoryItem.Label == 1 {
					categoryAPIListArray = append(categoryAPIListArray, categoryItem)
				} else if categoryItem.Label == 3 {
					categorySDKListArray = append(categorySDKListArray, categoryItem)
				}
			}
		} else if localPosItem.LocalPosDiffluenceRange == 1 {
			if categoryItem.LocalAppID == localPosItem.LocalAppID && categoryItem.LocalPosID == localPosItem.LocalPosID {

				if categoryItem.Label == 1 {
					categoryAPIListArray = append(categoryAPIListArray, categoryItem)
				} else if categoryItem.Label == 3 {
					categorySDKListArray = append(categorySDKListArray, categoryItem)
				}
			}
		}
	}

	var destApiCategoryListArray []CategoryStu
	if true {
		// api categroy
		cachKeyApiCategory := "go_category_price_api_" + localPosItem.LocalAppID + "_" + localPosItem.LocalPosID
		if len(categoryAPIListArray) == 0 {
			db.GlbBigCache.Set(cachKeyApiCategory, []byte(""))
		} else {
			// 计算配置平台个数
			var platformArray []string
			for _, item := range categoryAPIListArray {
				isExist := false
				for i := 0; i < len(platformArray); i++ {
					if platformArray[i] == item.PlatformMediaID {
						isExist = true
					}
				}
				if isExist {
				} else {
					platformArray = append(platformArray, item.PlatformMediaID)
				}
			}

			for i := 0; i < len(platformArray); i++ {
				var tmpCategoryArray []CategoryStu
				for _, item := range categoryAPIListArray {
					if platformArray[i] == item.PlatformMediaID {
						////////////////////////////////////////////////////////////////////////////////////////
						if localPosItem.LocalPosDiffluenceStrategy == 0 {
							if len(item.ReqLimitFrequencyJson) > 2 {
								var reqLimitFrequencyArray []ReqLimitFrequencyStu
								json.Unmarshal([]byte(item.ReqLimitFrequencyJson), &reqLimitFrequencyArray)
								currentHour := time.Now().Hour()
								currentMinute := time.Now().Minute()
								if len(reqLimitFrequencyArray) > 0 {
									isPass := true
									for _, limitItem := range reqLimitFrequencyArray {
										if (60*utils.ConvertStringToInt(limitItem.BeginHour)+utils.ConvertStringToInt(limitItem.BeginMinute)) <= (60*currentHour+currentMinute) &&
											(60*currentHour+currentMinute) <= (60*utils.ConvertStringToInt(limitItem.EndHour)+utils.ConvertStringToInt(limitItem.EndMinute)) {
											randValue := rand.Intn(10000)
											if randValue < limitItem.Rate {
											} else {
												isPass = false
											}
										}
									}
									if isPass {
										tmpCategoryArray = append(tmpCategoryArray, item)
									}
								}
							} else {
								tmpCategoryArray = append(tmpCategoryArray, item)
							}
						} else if localPosItem.LocalPosDiffluenceStrategy == 1 {
							tmpCategoryArray = append(tmpCategoryArray, item)
						}
						////////////////////////////////////////////////////////////////////////////////////////
					}
				}
				var tmpTotalWeight = 0
				for _, item := range tmpCategoryArray {
					tmpTotalWeight = tmpTotalWeight + item.APISubWeight
				}
				if tmpTotalWeight == 0 {
					continue
				}

				tmpRandWeight := rand.Intn(tmpTotalWeight)

				tmpWeight1 := 0
				for _, item := range tmpCategoryArray {
					tmpWeight1 = tmpWeight1 + item.APISubWeight
					if tmpRandWeight < tmpWeight1 {
						////////////////////////////////////////////////////////////////////////////////////////
						if localPosItem.LocalPosDiffluenceStrategy == 0 {
							destApiCategoryListArray = append(destApiCategoryListArray, item)
						} else if localPosItem.LocalPosDiffluenceStrategy == 1 {
							if len(item.ReqLimitFrequencyJson) > 2 {
								var reqLimitFrequencyArray []ReqLimitFrequencyStu
								json.Unmarshal([]byte(item.ReqLimitFrequencyJson), &reqLimitFrequencyArray)
								currentHour := time.Now().Hour()
								currentMinute := time.Now().Minute()
								if len(reqLimitFrequencyArray) > 0 {
									isPass := true
									for _, limitItem := range reqLimitFrequencyArray {
										if (60*utils.ConvertStringToInt(limitItem.BeginHour)+utils.ConvertStringToInt(limitItem.BeginMinute)) <= (60*currentHour+currentMinute) &&
											(60*currentHour+currentMinute) <= (60*utils.ConvertStringToInt(limitItem.EndHour)+utils.ConvertStringToInt(limitItem.EndMinute)) {
											randValue := rand.Intn(10000)
											if randValue < limitItem.Rate {
											} else {
												isPass = false
											}
										}
									}
									if isPass {
										destApiCategoryListArray = append(destApiCategoryListArray, item)
									}
								}
							} else {
								destApiCategoryListArray = append(destApiCategoryListArray, item)
							}
						}
						////////////////////////////////////////////////////////////////////////////////////////
						break
					}
				}
			}
			// fmt.Println("api category length:", localAppID, localPosID, len(destApiCategoryListArray))

			// cache
			tmpJSON, _ := json.Marshal(destApiCategoryListArray)
			db.GlbBigCache.Set(cachKeyApiCategory, tmpJSON)
		}
	}

	// sdk categroy
	if true {
		cachKeySdkCategory := "go_category_price_sdk_" + localPosItem.LocalAppID + "_" + localPosItem.LocalPosID

		var destSdkCategoryListArray []CategoryStu
		// 计算配置平台个数
		var platformArray []string
		for _, item := range categorySDKListArray {
			isExist := false
			for i := 0; i < len(platformArray); i++ {
				if platformArray[i] == item.PlatformMediaID {
					isExist = true
				}
			}
			if isExist {
			} else {
				platformArray = append(platformArray, item.PlatformMediaID)
			}
		}

		for i := 0; i < len(platformArray); i++ {
			var tmpCategoryArray []CategoryStu
			for _, item := range categorySDKListArray {
				if platformArray[i] == item.PlatformMediaID {
					////////////////////////////////////////////////////////////////////////////////////////
					if localPosItem.LocalPosDiffluenceStrategy == 0 {
						if len(item.ReqLimitFrequencyJson) > 2 {
							var reqLimitFrequencyArray []ReqLimitFrequencyStu
							json.Unmarshal([]byte(item.ReqLimitFrequencyJson), &reqLimitFrequencyArray)
							currentHour := time.Now().Hour()
							currentMinute := time.Now().Minute()
							if len(reqLimitFrequencyArray) > 0 {
								isPass := true
								for _, limitItem := range reqLimitFrequencyArray {
									if (60*utils.ConvertStringToInt(limitItem.BeginHour)+utils.ConvertStringToInt(limitItem.BeginMinute)) <= (60*currentHour+currentMinute) &&
										(60*currentHour+currentMinute) <= (60*utils.ConvertStringToInt(limitItem.EndHour)+utils.ConvertStringToInt(limitItem.EndMinute)) {
										randValue := rand.Intn(10000)
										if randValue < limitItem.Rate {
										} else {
											isPass = false
										}
									}
								}
								if isPass {
									tmpCategoryArray = append(tmpCategoryArray, item)
								}
							}
						} else {
							tmpCategoryArray = append(tmpCategoryArray, item)
						}
					} else if localPosItem.LocalPosDiffluenceStrategy == 1 {
						tmpCategoryArray = append(tmpCategoryArray, item)
					}
					////////////////////////////////////////////////////////////////////////////////////////
				}
			}
			var tmpTotalWeight = 0
			for _, item := range tmpCategoryArray {
				tmpTotalWeight = tmpTotalWeight + item.APISubWeight
			}
			if tmpTotalWeight == 0 {
				continue
			}

			tmpRandWeight := rand.Intn(tmpTotalWeight)

			tmpWeight1 := 0
			for _, item := range tmpCategoryArray {
				tmpWeight1 = tmpWeight1 + item.APISubWeight
				if tmpRandWeight < tmpWeight1 {
					////////////////////////////////////////////////////////////////////////////////////////
					if localPosItem.LocalPosDiffluenceStrategy == 0 {
						destSdkCategoryListArray = append(destSdkCategoryListArray, item)
					} else if localPosItem.LocalPosDiffluenceStrategy == 1 {
						if len(item.ReqLimitFrequencyJson) > 2 {
							var reqLimitFrequencyArray []ReqLimitFrequencyStu
							json.Unmarshal([]byte(item.ReqLimitFrequencyJson), &reqLimitFrequencyArray)
							currentHour := time.Now().Hour()
							currentMinute := time.Now().Minute()
							if len(reqLimitFrequencyArray) > 0 {
								isPass := true
								for _, limitItem := range reqLimitFrequencyArray {
									if (60*utils.ConvertStringToInt(limitItem.BeginHour)+utils.ConvertStringToInt(limitItem.BeginMinute)) <= (60*currentHour+currentMinute) &&
										(60*currentHour+currentMinute) <= (60*utils.ConvertStringToInt(limitItem.EndHour)+utils.ConvertStringToInt(limitItem.EndMinute)) {
										randValue := rand.Intn(10000)
										if randValue < limitItem.Rate {
										} else {
											isPass = false
										}
									}
								}
								if isPass {
									destSdkCategoryListArray = append(destSdkCategoryListArray, item)
								}
							}
						} else {
							destSdkCategoryListArray = append(destSdkCategoryListArray, item)
						}
					}
					////////////////////////////////////////////////////////////////////////////////////////
					break
				}
			}
		}

		// sdk -> api
		if len(destApiCategoryListArray) > 0 {
			for _, item := range destApiCategoryListArray {
				if item.Label == 1 && item.IsActive == 1 {
					destSdkCategoryListArray = append(destSdkCategoryListArray, item)
					break
				}
			}
		}

		// sort
		if len(destSdkCategoryListArray) > 1 {
			sort.Sort(CategoryPrioritySort(destSdkCategoryListArray))
		}

		// fmt.Println("sdk category length:", localAppID, localPosID, len(destSdkCategoryListArray))
		// cache
		tmpJSON, _ := json.Marshal(destSdkCategoryListArray)
		db.GlbBigCache.Set(cachKeySdkCategory, tmpJSON)
	}
}

type CategoryPrioritySort []CategoryStu

func (s CategoryPrioritySort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s CategoryPrioritySort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s CategoryPrioritySort) Less(i, j int) bool {
	//按字段比较大小,此处是升序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return s[i].Priority < s[j].Priority
}

// clearCacheFromMySQL ...
func clearCacheFromMySQL() {

	// clear local pos
	sqlPosStr := "SELECT a.id, a.app_id " +
		"FROM pos a INNER JOIN app b where a.is_active != 1 and a.app_id = b.id"

	posRows, err := db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var localPos LocalPosStu
		err := posRows.Scan(&localPos.LocalPosID, &localPos.LocalAppID)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		// fmt.Println("local pos id:", localPos.LocalPosID)
		// fmt.Println("local pos shield_rule_id:", localPos.LocalPosShieldRuleID)
		// fmt.Println("local pos shield_key:", localPos.LocalPosShieldKey)
		// fmt.Println("local pos pacakge_name:", localPos.LocalPosShieldPackageName)
		// fmt.Println("local pos domain_name:", localPos.LocalPosShieldDomainName)
		// fmt.Println("local pos material:", localPos.LocalPosShieldMaterial)

		// fmt.Println("local app id:", localPos.LocalAppID)
		// fmt.Println("local app shield_rule_id:", localPos.LocalAppShieldRuleID)
		// fmt.Println("local app shield_key:", localPos.LocalAppShieldKey)
		// fmt.Println("local app pacakge_name:", localPos.LocalAppShieldPackageName)
		// fmt.Println("local app domain_name:", localPos.LocalAppShieldDomainName)
		// fmt.Println("local app material:", localPos.LocalAppShieldMaterial)

		cacheKey := "go_local_" + localPos.LocalAppID + "_" + localPos.LocalPosID
		_, cacheError := db.GlbBigCache.Get(cacheKey)
		if cacheError != nil {
			// fmt.Println("cache error:", cacheKey, cacheError)
		} else {
			// fmt.Println("cache value:", cacheValue)
			db.GlbBigCache.Delete(cacheKey)
		}
	}

	// clear platform pos
	sqlPosStr = "SELECT a.id, a.app_id, b.media_id " +
		"FROM platform_pos a INNER JOIN platform_app b where a.app_id = b.id and a.media_id = b.media_id and a.os = b.os and a.app_bundle_id = b.app_bundle_id and (b.is_active != 1 or a.is_active != 1)"

	posRows, err = db.GlbMySQLDb.Query(sqlPosStr)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer posRows.Close()

	// 循环读取结果集中的数据
	for posRows.Next() {
		var platformPos PlatformPosStu
		err := posRows.Scan(&platformPos.PlatformPosID, &platformPos.PlatformAppID, &platformPos.PlatformMediaID)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		cacheKeyPlatformPos := "go_platform_" + platformPos.PlatformAppID + "_" + platformPos.PlatformMediaID + "_" + platformPos.PlatformPosID

		_, cacheError := db.GlbBigCache.Get(cacheKeyPlatformPos)
		if cacheError != nil {
			// fmt.Println("cache error:", cacheKey, cacheError)
		} else {
			// fmt.Println("cache value:", cacheValue)
			db.GlbBigCache.Delete(cacheKeyPlatformPos)
		}
	}
}

// getClickPointLibConfigFromMySQL ...
func getClickPointLibConfigFromMySQL() {

	// click point storage array
	var clickPointLibStorageArray []ClickPointLibStorageStu

	sql := "select IFNULL(a.id, 0), IFNULL(a.key, ''), IFNULL(a.name, '')," +
		"IFNULL(a.app_id, ''), IFNULL(a.pos_id, ''), IFNULL(a.pixel_type, 0) " +
		"from ssp.magic_clickpointlib_storage_list a where a.type = 0"

	rows, err := db.GlbMySQLDb.Query(sql)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var item ClickPointLibStorageStu
		err := rows.Scan(&item.ID, &item.Key, &item.Name,
			&item.LocalAppID, &item.LocalPosID, &item.PixelType)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		clickPointLibStorageArray = append(clickPointLibStorageArray, item)
	}

	cacheKey := "go_click_point_lib_storage"
	if len(clickPointLibStorageArray) > 0 {
		tmpJSON, _ := json.Marshal(clickPointLibStorageArray)

		// fmt.Println("---------->:", string(tmpJSON))
		db.GlbBigCache.Set(cacheKey, tmpJSON)
	} else {
		_, cacheError := db.GlbBigCache.Get(cacheKey)
		if cacheError != nil {
			// fmt.Println("cache error:", cacheKey, cacheError)
		} else {
			// fmt.Println("cache value:", cacheValue)
			db.GlbBigCache.Delete(cacheKey)
		}
	}
}

// getSDKLogoConfigFromMySQL ...
func getSDKLogoConfigFromMySQL() {

	sql := "select IFNULL(a.api_config, ''), IFNULL(a.sdk_config, ''), IFNULL(a.app_ids, ''), IFNULL(a.pos_ids, '')  " +
		"from ssp.logo_configs a WHERE a.is_active = 1"

	rows, err := db.GlbMySQLDb.Query(sql)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var tmpLogoDataStu LogoDataStu
		err := rows.Scan(&tmpLogoDataStu.ApiConfigJson, &tmpLogoDataStu.SdkConfigJson, &tmpLogoDataStu.LocalAppIDs, &tmpLogoDataStu.LocalPosIDs)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		if len(tmpLogoDataStu.LocalAppIDs) == 0 && len(tmpLogoDataStu.LocalPosIDs) == 0 {
			continue
		}

		if len(tmpLogoDataStu.ApiConfigJson) > 2 {
			json.Unmarshal([]byte(tmpLogoDataStu.ApiConfigJson), &tmpLogoDataStu.ApiConfig)
		}

		if len(tmpLogoDataStu.SdkConfigJson) > 2 {
			json.Unmarshal([]byte(tmpLogoDataStu.SdkConfigJson), &tmpLogoDataStu.SdkConfig)
		}
		tmpLogoDataStu.ApiConfigJson = ""
		tmpLogoDataStu.SdkConfigJson = ""

		localAppIDArray := strings.Split(tmpLogoDataStu.LocalAppIDs, ",")
		if len(localAppIDArray) > 0 {
			for _, localAppIDItem := range localAppIDArray {
				if len(localAppIDItem) == 0 {
					continue
				}
				hh := time.Now().Hour()

				var tmpLogoCache LogoDataCacheConfigStu
				for _, configItem := range tmpLogoDataStu.ApiConfig {
					if utils.ConvertStringToInt(configItem.From) <= hh && hh <= utils.ConvertStringToInt(configItem.To) {
						tmpLogoCache.ApiBlackList = configItem.BlackList
						tmpLogoCache.ApiWhiteList = configItem.WhiteList
						tmpLogoCache.ApiLogo = configItem.Logo
					}
				}

				for _, configItem := range tmpLogoDataStu.SdkConfig {
					if utils.ConvertStringToInt(configItem.From) <= hh && hh <= utils.ConvertStringToInt(configItem.To) {
						tmpLogoCache.SdkBlackList = configItem.BlackList
						tmpLogoCache.SdkWhiteList = configItem.WhiteList
						tmpLogoCache.SdkLogo = configItem.Logo
					}
				}

				cacheKey := "go_logo_config_app_id:" + localAppIDItem
				tmpJSON, _ := json.Marshal(tmpLogoCache)
				// fmt.Println("kbg_debug_logo_app:", cacheKey, len(tmpJSON), string(tmpJSON))

				err := db.GlbBigCacheMinute.Set(cacheKey, tmpJSON)
				if err != nil {
					fmt.Println("set logo data error:", cacheKey)
				}
			}
		}

		localPosIDArray := strings.Split(tmpLogoDataStu.LocalPosIDs, ",")
		if len(localPosIDArray) > 0 {
			for _, localPosIDItem := range localPosIDArray {
				if len(localPosIDItem) == 0 {
					continue
				}
				hh := time.Now().Hour()

				var tmpLogoCache LogoDataCacheConfigStu
				for _, configItem := range tmpLogoDataStu.ApiConfig {
					if utils.ConvertStringToInt(configItem.From) <= hh && hh <= utils.ConvertStringToInt(configItem.To) {
						tmpLogoCache.ApiBlackList = configItem.BlackList
						tmpLogoCache.ApiWhiteList = configItem.WhiteList
						tmpLogoCache.ApiLogo = configItem.Logo
					}
				}

				for _, configItem := range tmpLogoDataStu.SdkConfig {
					if utils.ConvertStringToInt(configItem.From) <= hh && hh <= utils.ConvertStringToInt(configItem.To) {
						tmpLogoCache.SdkBlackList = configItem.BlackList
						tmpLogoCache.SdkWhiteList = configItem.WhiteList
						tmpLogoCache.SdkLogo = configItem.Logo
					}
				}

				cacheKey := "go_logo_config_pos_id:" + localPosIDItem
				tmpJSON, _ := json.Marshal(tmpLogoCache)
				// fmt.Println("kbg_debug_logo_pos:", cacheKey, len(tmpJSON), string(tmpJSON))

				err := db.GlbBigCacheMinute.Set(cacheKey, tmpJSON)
				if err != nil {
					fmt.Println("set logo data error:", cacheKey)
				}
			}
		}
	}
}

// getLossDataConfigFromMySQL ...
func getLossDataConfigFromMySQL() {

	// loss data array
	var lossDataArray []LossDataStu

	sql := "select IFNULL(a.title, ''), IFNULL(a.publisher, '') " +
		"from ssp.loss_data a"

	rows, err := db.GlbMySQLDb.Query(sql)

	if err != nil {
		fmt.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var item LossDataStu
		err := rows.Scan(&item.Title, &item.Publisher)
		if err != nil {
			fmt.Printf("scan failed, err:%v\n", err)
			return
		}

		lossDataArray = append(lossDataArray, item)
	}

	cacheKey := "go_loss_data"
	if len(lossDataArray) > 0 {
		tmpJSON, _ := json.Marshal(lossDataArray)

		// fmt.Println("---------->:", string(tmpJSON))
		err = db.GlbBigCache.Set(cacheKey, tmpJSON)
		if err != nil {
			fmt.Println("set loss data error:")
		}
	} else {
		_, cacheError := db.GlbBigCache.Get(cacheKey)
		if cacheError != nil {
			// fmt.Println("cache error:", cacheKey, cacheError)
		} else {
			// fmt.Println("cache value:", cacheValue)
			db.GlbBigCache.Delete(cacheKey)
		}
	}
}

// getDSPConfigFromMySQL ...
func getDSPConfigFromMySQL() {

	var supplyGroupIdsArray []string

	if true {
		sql := "SELECT IFNULL(a.supply_app_group_ids, '') " +
			"FROM dsp.ad_plans a LEFT JOIN dsp.ad_groups b on a.gid = b.gid " +
			"WHERE a.deleted_at is null AND a.status = 1 " +
			"AND b.deleted_at is null AND b.status = 1 " +
			"AND LENGTH(a.supply_app_group_ids) > 2"

		rows, err := db.GlbMySQLDb.Query(sql)

		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}

		// 非常重要：关闭rows释放持有的数据库链接
		defer rows.Close()

		// 循环读取结果集中的数据
		for rows.Next() {
			var item string
			err := rows.Scan(&item)
			if err != nil {
				fmt.Println("scan failed, err:", err)
				return
			}
			// fmt.Println("scan item:", item)

			if len(item) > 2 {
				var tmpArray []string
				json.Unmarshal([]byte(item), &tmpArray)
				supplyGroupIdsArray = append(supplyGroupIdsArray, tmpArray...)
			}
		}
	}
	/////////////////////////////////////////////////////////////////////////////////////
	if len(supplyGroupIdsArray) == 0 {
		return
	}
	// fmt.Println("----------> 0:", supplyGroupIdsArray)
	supplyGroupIdsArray = removeDuplicates(supplyGroupIdsArray)
	// fmt.Println("----------> 1:", supplyGroupIdsArray)
	/////////////////////////////////////////////////////////////////////////////////////
	var tmpDspFlowGroupArray []DspFlowGroupStu
	var tmpSupplyAppIDArray []string
	if true {
		query := fmt.Sprintf("SELECT a.uid, IFNULL(a.supply_app_ids, ''), IFNULL(a.pos_types, ''), IFNULL(a.weight, 0) "+
			"FROM dsp.supply_app_group a WHERE a.uid IN ('%s')", strings.Join(supplyGroupIdsArray, "','"))
		rows, err := db.GlbMySQLDb.Query(query)

		if err != nil {
			fmt.Printf("query failed, err:%v\n", err)
			return
		}

		// 非常重要：关闭rows释放持有的数据库链接
		defer rows.Close()

		// 循环读取结果集中的数据
		for rows.Next() {
			var item0 string
			var tmpAppIDsJson string
			var tmpPosTypesJson string
			var tmpDspFlowGroupStu DspFlowGroupStu
			err := rows.Scan(&item0, &tmpAppIDsJson, &tmpPosTypesJson, &tmpDspFlowGroupStu.Weight)
			if err != nil {
				fmt.Println("scan failed, err:", err)
				return
			}

			if tmpDspFlowGroupStu.Weight == 0 {
				continue
			}
			json.Unmarshal([]byte(tmpAppIDsJson), &tmpDspFlowGroupStu.AppIDs)
			json.Unmarshal([]byte(tmpPosTypesJson), &tmpDspFlowGroupStu.PosTypes)
			// fmt.Println("scan item--------->:", item0, tmpDspFlowGroupStu)

			tmpDspFlowGroupArray = append(tmpDspFlowGroupArray, tmpDspFlowGroupStu)
			tmpSupplyAppIDArray = append(tmpSupplyAppIDArray, tmpDspFlowGroupStu.AppIDs...)
			tmpSupplyAppIDArray = removeDuplicates(tmpSupplyAppIDArray)
		}
	}
	// fmt.Println("tmp local_app_ids:", tmpSupplyAppIDArray)
	for _, tmpSupplyAppID := range tmpSupplyAppIDArray {
		cacheKey := "dsp_config_supply_app_id_" + tmpSupplyAppID

		var dspFlowStu DspFlowStu
		dspFlowStu.AppID = tmpSupplyAppID

		for _, tmpDspFlowGroup := range tmpDspFlowGroupArray {
			isExist := false
			for _, tmpDspFlowAppID := range tmpDspFlowGroup.AppIDs {
				if tmpDspFlowAppID == tmpSupplyAppID {
					isExist = true
				}
			}

			if isExist {
				dspFlowStu.PosTypes = append(dspFlowStu.PosTypes, tmpDspFlowGroup.PosTypes...)
				if tmpDspFlowGroup.Weight > dspFlowStu.Weight {
					dspFlowStu.Weight = tmpDspFlowGroup.Weight
				}
			}
		}
		dspFlowStu.PosTypes = removeDuplicates(dspFlowStu.PosTypes)

		tmpByte, _ := json.Marshal(dspFlowStu)
		// fmt.Println("tmp json:", cacheKey, string(tmpByte))
		db.GlbBigCacheMinute.Set(cacheKey, []byte(tmpByte))
	}
}

func getAntiCheatConfigFromRedis() {
	redisValue, redisErr := db.GlbRedis.Get(context.Background(), rediskeys.ADX_SSP_ANTICHEAT_CONFFIG_KEY).Result()
	if redisErr != nil {
	} else {
		if len(redisValue) == 0 {
			return
		}
		var anticheatConfig AnticheatConfigStu
		json.Unmarshal([]byte(redisValue), &anticheatConfig)

		if len(anticheatConfig.APIPlatform) > 0 {
			apiAnticheatPlatformArray := strings.Split(anticheatConfig.APIPlatform, ",")
			for _, item := range apiAnticheatPlatformArray {
				tmpCacheKey := fmt.Sprintf(config.AnticheatConfigAPI, item)
				db.GlbBigCacheMinute.Set(tmpCacheKey, []byte("1"))
			}
		}

		if len(anticheatConfig.SDKPlatform) > 0 {
			sdkAnticheatPlatformArray := strings.Split(anticheatConfig.SDKPlatform, ",")
			for _, item := range sdkAnticheatPlatformArray {
				tmpCacheKey := fmt.Sprintf(config.AnticheatConfigSDK, item)
				db.GlbBigCacheMinute.Set(tmpCacheKey, []byte("1"))
			}
		}
	}
}

func getQuerysPkgsFromRedis() {
	redisValue, redisErr := db.GlbRedis.Get(context.Background(), rediskeys.ADX_SSP_DICT_QUERY_KEY).Result()
	if redisErr != nil {
	} else {
		if len(redisValue) == 0 {
			return
		}
		db.GlbBigCacheMinute.Set(rediskeys.ADX_SSP_DICT_QUERY_KEY, []byte(redisValue))
	}
}

func getDebugTimeOutFromRedis() {
	if true {
		redisValue, redisErr := db.GlbRedis.Get(context.Background(), rediskeys.ADX_SSP_DEBUG_TIMEOUT_RANDOM_KEY).Result()
		if redisErr != nil {
		} else {
			if len(redisValue) == 0 {
				return
			}
			db.GlbBigCacheMinute.Set(rediskeys.ADX_SSP_DEBUG_TIMEOUT_RANDOM_KEY, []byte(redisValue))
		}
	}

	if true {
		redisValue, redisErr := db.GlbRedis.Get(context.Background(), rediskeys.ADX_SSP_DEBUG_TIMEOUT_MAX_KEY).Result()
		if redisErr != nil {
		} else {
			if len(redisValue) == 0 {
				return
			}
			db.GlbBigCacheMinute.Set(rediskeys.ADX_SSP_DEBUG_TIMEOUT_MAX_KEY, []byte(redisValue))
		}
	}
}

func getWhiteDIDFromRedis() {
	extraWhiteDIDCacheKey := "mh_extra_white_did"
	redisValue, redisErr := db.GlbRedis.Get(context.Background(), "mh_extra_white_did_"+utils.ConvertIntToString(rand.Intn(100))).Result()
	if redisErr != nil {
	} else {
		if len(redisValue) == 0 {
			return
		}
		db.GlbBigCacheMinute.Set(extraWhiteDIDCacheKey, []byte(redisValue))
	}
}

func getUAConfigFromRedis() {
	type TmpModel struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	tmpKey := "adx_ssp_new_device_ua_dict"
	// tmpKey := rediskeys.ADX_SSP_NEW_DEVICE_UA_DICT_KEY
	redisValue, redisErr := db.GlbRedis.Get(context.Background(), tmpKey).Result()
	if redisErr != nil {
	} else {
		if len(redisValue) == 0 {
			return
		}
		tmpArray := []TmpModel{}
		json.Unmarshal([]byte(redisValue), &tmpArray)
		for _, tmpItem := range tmpArray {
			err := db.GlbBigCache.Set(fmt.Sprintf(config.ADX_SSP_CACHE_UA_KEY, tmpItem.Key), []byte(tmpItem.Value))
			if err != nil {
				fmt.Println("set ua dict error:", fmt.Sprintf(config.ADX_SSP_CACHE_UA_KEY, tmpItem.Key))
			}
		}
	}
}

func getDPIConfigFromRedis() {
	type TmpModel struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	tmpKey := "adx_ssp_new_device_dpi"
	// tmpKey := rediskeys.ADX_SSP_NEW_DEVICE_DPI_KEY
	redisValue, redisErr := db.GlbRedis.Get(context.Background(), tmpKey).Result()
	if redisErr != nil {
	} else {
		if len(redisValue) == 0 {
			return
		}
		tmpArray := []TmpModel{}
		json.Unmarshal([]byte(redisValue), &tmpArray)
		for _, tmpItem := range tmpArray {
			err := db.GlbBigCache.Set(fmt.Sprintf(config.ADX_SSP_CACHE_DPI_KEY, tmpItem.Key), []byte(tmpItem.Value))
			if err != nil {
				fmt.Println("set dpi dict error:", fmt.Sprintf(config.ADX_SSP_CACHE_UA_KEY, tmpItem.Key))
			}
		}
	}
}
