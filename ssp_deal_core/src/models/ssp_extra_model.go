package models

type SSPExtraStu struct {
	ExtraType      int                `json:"extra_type"`
	ExpWeight      int                `json:"exp_weight"`
	Status         int                `json:"status"`
	ConditionType  int                `json:"condition_type"`
	ExtraRuleId    string             `json:"extra_rule_id"`
	ExpUrl         string             `json:"exp_url"`
	ExtraKey       string             `json:"extra_key"`
	PackageName    string             `json:"package_name"`
	AdUrl          string             `json:"ad_url"`
	MaterialUrl    string             `json:"material_url"`
	PlatformIds    string             `json:"platform_ids"`
	AppIds         string             `json:"app_ids"`
	PosIds         string             `json:"pos_ids"`
	ExpTargetNum   string             `json:"exp_target_num"`
	ClickRate      string             `json:"click_rate"`
	PriceIntercept string             `json:"price_intercept"`
	MgDealId       string             `json:"mg_deal_id"`
	RtaId          string             `json:"rta_id"`
	GroupList      []SSPExtraGroupStu `json:"group_list"`
}

type SSPExtraGroupStu struct {
	ExpCount   int    `json:"exp_count"`
	TimePeriod string `json:"time_period"`
}

type SSPExtraBigCache struct {
	ExtraType      int                `json:"extra_type"`
	ExpWeight      int                `json:"exp_weight"`
	Status         int                `json:"status"`
	ConditionType  int                `json:"condition_type"`
	ExpUrl         string             `json:"exp_url"`
	ExtraKey       string             `json:"extra_key"`
	PackageName    string             `json:"package_name"`
	AdUrl          string             `json:"ad_url"`
	MaterialUrl    string             `json:"material_url"`
	ExpTargetNum   string             `json:"exp_target_num"`
	ClickRate      string             `json:"click_rate"`
	PriceIntercept string             `json:"price_intercept"`
	MgDealId       string             `json:"mg_deal_id"`
	RtaId          string             `json:"rta_id"`
	PlatformIds    string             `json:"platform_ids"`
	GroupList      []SSPExtraGroupStu `json:"group_list"`
}
