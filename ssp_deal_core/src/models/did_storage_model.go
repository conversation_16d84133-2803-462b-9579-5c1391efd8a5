package models

// 读取是否存储did配置
type DIDStorageStu struct {
	LocalAppID        string `json:"app_id"`
	MaxNum            int    `json:"max_num"`
	RealMaxNum        int    `json:"real_max_num"`
	IsVerifyParameter int    `json:"is_verify_parameter"`
}

// 存holo替换包结构体
type ReplaceDIDHoloStu struct {
	//
	ID         string `json:"id,omitempty"`
	LocalAppID string `json:"app_id,omitempty"`
	DIDMd5     string `json:"did_md5,omitempty"`
	Os         string `json:"os,omitempty"`

	// ip location
	IP            string `json:"ip,omitempty"`
	LBSIPCountry  string `json:"lbs_ip_country"`
	LBSIPProvince string `json:"lbs_ip_province"`
	LBSIPCity     string `json:"lbs_ip_city"`

	//
	Imei         string `json:"imei,omitempty"`
	ImeiMd5      string `json:"imei_md5,omitempty"`
	AndroidID    string `json:"android_id,omitempty"`
	AndroidIDMd5 string `json:"android_id_md5,omitempty"`
	Oaid         string `json:"oaid,omitempty"`
	Idfa         string `json:"idfa,omitempty"`
	IdfaMd5      string `json:"idfa_md5,omitempty"`
	OsVersion    string `json:"os_version,omitempty"`
	Model        string `json:"model,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	Ua           string `json:"ua,omitempty"`
	// 因子
	DeviceStartSec     string `json:"device_start_sec,omitempty"`
	Country            string `json:"country,omitempty"`
	Language           string `json:"language,omitempty"`
	DeviceNameMd5      string `json:"device_name_md5,omitempty"`
	HardwareMachine    string `json:"hardware_machine,omitempty"` // iPhone10,3
	HardwareModel      string `json:"hardware_model,omitempty"`   // D22AP
	PhysicalMemoryByte string `json:"physical_memory_byte,omitempty"`
	HarddiskSizeByte   string `json:"harddisk_size_byte,omitempty"`
	SystemUpdateSec    string `json:"system_update_sec,omitempty"`
	TimeZone           string `json:"time_zone,omitempty"`
	CPUNum             int    `json:"cpu_num,omitempty"`
	DeviceBirthSec     string `json:"device_birth_sec"`
	// caid multi
	CAIDMulti string `json:"caid_multi"` // 多个caid json

	ConnectType int `json:"connect_type,omitempty"`
	Carrier     int `json:"carrier,omitempty"`
	DeviceType  int `json:"device_type,omitempty"`
}
