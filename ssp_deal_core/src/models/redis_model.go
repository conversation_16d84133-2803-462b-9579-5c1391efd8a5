package models

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/device/dau"
	"mh_proxy/device/info"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"strings"
	"time"

	deviceidgenerate "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate"
	deviceidgeneratemodel "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"
)

// GetDeviceIDMd5Key ...
func GetDeviceIDMd5Key(mhReq *MHReq) string {
	var deviceInfo deviceidgeneratemodel.DeviceModel
	deviceInfo.Os = mhReq.Device.Os
	deviceInfo.OsVersion = mhReq.Device.OsVersion
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = mhReq.Device.Imei
		deviceInfo.ImeiMd5 = mhReq.Device.ImeiMd5
		deviceInfo.Oaid = mhReq.Device.Oaid
		deviceInfo.OaidMd5 = mhReq.Device.OaidMd5
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = mhReq.Device.Idfa
		deviceInfo.IdfaMd5 = mhReq.Device.IdfaMd5

		deviceInfo.DeviceStartSec = mhReq.Device.DeviceStartSec
		deviceInfo.Country = mhReq.Device.Country
		deviceInfo.Language = mhReq.Device.Language
		deviceInfo.DeviceNameMd5 = mhReq.Device.DeviceNameMd5
		deviceInfo.HardwareMachine = mhReq.Device.HardwareMachine
		deviceInfo.HardwareModel = mhReq.Device.HardwareModel
		deviceInfo.PhysicalMemoryByte = mhReq.Device.PhysicalMemoryByte
		deviceInfo.HarddiskSizeByte = mhReq.Device.HarddiskSizeByte
		deviceInfo.SystemUpdateSec = mhReq.Device.SystemUpdateSec
		deviceInfo.TimeZone = mhReq.Device.TimeZone

		if len(mhReq.Device.CAIDMulti) > 0 {
			var tmpCAIDMulti []deviceidgeneratemodel.DeviceCAIDMultiModel
			for _, tmpItem := range mhReq.Device.CAIDMulti {
				if len(tmpItem.CAID) > 0 && len(tmpItem.CAIDVersion) > 0 {
					var tmpCAIDItem deviceidgeneratemodel.DeviceCAIDMultiModel

					tmpCAIDItem.CAID = tmpItem.CAID
					tmpCAIDItem.CAIDVersion = tmpItem.CAIDVersion
					tmpCAIDMulti = append(tmpCAIDMulti, tmpCAIDItem)
				}
			}

			deviceInfo.CAIDMulti = tmpCAIDMulti
		}
	}

	return deviceidgenerate.GenerateDeviceID(deviceInfo)
}

// RedisEntryDID ...
func RedisEntryDID(c context.Context, mhReq *MHReq, localPos *LocalPosStu, sdkVersion string) {
	if mhReq.Device.DIDMd5 == utils.Get16Md5("") {
		return
	}
	md5Key := mhReq.Device.DIDMd5
	// 待删除
	md5KeyType := 0

	if utilities.DauSaveHolo {
		if mhReqBytes, err := json.Marshal(mhReq); err == nil {
			newMhReq := device.ModelsMHReqToDeviceReq(mhReqBytes)
			if localPosBytes, err := json.Marshal(localPos); err == nil {
				newLocalPos := device.ModelsLocalPosStuToDeviceLocalPosStu(localPosBytes)
				// dau.DauReq(
				// 	c,
				// 	md5Key,
				// 	md5KeyType,
				// 	newMhReq,
				// 	newLocalPos,
				// 	sdkVersion,
				// )
				go info.DeviceInfoReq(
					c,
					md5Key,
					md5KeyType,
					newMhReq,
					newLocalPos,
					sdkVersion,
				)
				go dau.DauSupplyReqKafka(
					c,
					md5Key,
					md5KeyType,
					newMhReq,
					newLocalPos,
					sdkVersion,
				)
			}
		}

	}

	// huawei hms_version, appstore_version
	lowerManufacturer := strings.ToLower(mhReq.Device.Manufacturer)
	if strings.Contains(lowerManufacturer, "huawei") {
		// 2025.05.06 华为的hms和ag按照1/10000写入
		if rand.Intn(10000) == 0 {
			if len(mhReq.Device.HMSCoreVersion) == 8 && len(mhReq.Device.AppStoreVersion) == 9 {
				var tmpDIDHuaWeiHmsData DIDHuaWeiHMSStu
				tmpDIDHuaWeiHmsData.HMSCoreVersion = mhReq.Device.HMSCoreVersion
				tmpDIDHuaWeiHmsData.AppStoreVersion = mhReq.Device.AppStoreVersion

				if strings.Contains(mhReq.Device.Oaid, "00000000") {
				} else {
					tmpJSON, _ := json.Marshal(tmpDIDHuaWeiHmsData)
					db.GlbRedis.Set(c, "huawei_"+md5Key, tmpJSON, time.Duration(7*24)*time.Hour).Err()
				}
			}
		}
	}

	// save did to holo
	if localPos.LocalAppIsDIDStorage == 1 {
		GoBigDataHoloDID(c, md5Key, mhReq, localPos)
	}

	if rand.Intn(100) < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugAppId) > 0 {
		localAppID := strings.Split(utilities.DebugAppId, ",")
		for _, appId := range localAppID {
			if appId == localPos.LocalAppID {
				var platformPos PlatformPosStu
				platformPos.PlatformAppID = ""
				platformPos.PlatformPosID = ""
				fmt.Println("RedisEntryDID:", localPos.LocalAppID)
				DebugDataAssembly(mhReq, localPos, &platformPos, "supply_req_data")
				break
			}
		}
	}
	if rand.Intn(100) < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugTmpAppId) > 0 {
		localAppID := strings.Split(utilities.DebugTmpAppId, ",")
		for _, appId := range localAppID {
			if appId == localPos.LocalAppID {
				var platformPos PlatformPosStu
				platformPos.PlatformAppID = ""
				platformPos.PlatformPosID = ""
				fmt.Println("RedisEntryDID:", localPos.LocalAppID)
				DebugDataAssembly(mhReq, localPos, &platformPos, "supply_tmp_req_data")
				break
			}
		}
	}
}

type DIDRedisDataStu struct {
	AppIDs string `json:"app_ids"` // all app_ids
	// PlatformData []DIDRedisPlatformData `json:"p_app_ids"` // p_app_id
	// ReplaceAppIDs string                 `json:"rep_app_ids,omitempty"` // 替换app_ids
}

// type DIDRedisPlatformData struct {
// 	Date          string `json:"date"`
// 	PlatformAppID string `json:"p_app_id"` // p_app_id
// }

type ReplaceDIDStu struct {
	Imei         string `json:"imei,omitempty"`
	ImeiMd5      string `json:"imei_md5,omitempty"`
	AndroidID    string `json:"android_id,omitempty"`
	AndroidIDMd5 string `json:"android_id_md5,omitempty"`
	Oaid         string `json:"oaid,omitempty"`
	Idfa         string `json:"idfa,omitempty"`
	IdfaMd5      string `json:"idfa_md5,omitempty"`
	OsVersion    string `json:"os_version,omitempty"`
	Model        string `json:"model,omitempty"`
	Manufacturer string `json:"manufacturer,omitempty"`
	Ua           string `json:"ua,omitempty"`
	// 因子
	DeviceStartSec     string `json:"device_start_sec,omitempty"`
	Country            string `json:"country,omitempty"`
	Language           string `json:"language,omitempty"`
	DeviceNameMd5      string `json:"device_name_md5,omitempty"`
	HardwareMachine    string `json:"hardware_machine,omitempty"` // iPhone10,3
	HardwareModel      string `json:"hardware_model,omitempty"`   // D22AP
	PhysicalMemoryByte string `json:"physical_memory_byte,omitempty"`
	HarddiskSizeByte   string `json:"harddisk_size_byte,omitempty"`
	SystemUpdateSec    string `json:"system_update_sec,omitempty"`
	TimeZone           string `json:"time_zone,omitempty"`
	CPUNum             int    `json:"cpu_num,omitempty"`
	DeviceBirthSec     string `json:"device_birth_sec,omitempty"`
	CAIDMultiJson      string `json:"caid_multi,omitempty"` // 多个caid json

	//
	DeviceType  int `json:"device_type,omitempty"`
	ConnectType int `json:"connect_type,omitempty"`
	Carrier     int `json:"carrier,omitempty"`
	Version     int `json:"version,omitempty"`

	Os string `json:"os,omitempty"`
}

type DIDHuaWeiHMSStu struct {
	// appstore version, hms version
	AppStoreVersion string `json:"appstore_version"`
	HMSCoreVersion  string `json:"hms_version"`
}
