package models

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/device/dau"
	"mh_proxy/device/req"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"regexp"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

type SspDidModelAndManufacturer struct {
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
}

type SspModelBlacklistRules []string

type SspDidDataObject struct {
	Did          string `json:"did"`
	Model        string `json:"model"`
	RawModel     string `json:"raw_model"`
	Manufacturer string `json:"manufacturer"`
	Osv          string `json:"osv"`
}

// BigDataDIDUPReq ...
func BigDataDIDUPReq(c context.Context, mhReq *MHReq,
	localPos *LocalPosStu, platformPos *PlatformPosStu, isReplaced bool, replaceDIDStu ReplaceDIDStu) {

	// // 2023年07月05日 下线dau
	// return

	randNum := rand.Intn(100)
	if randNum < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugPAppId) > 0 {
		pAppIdArr := strings.Split(utilities.DebugPAppId, ",")
		for _, pAppId := range pAppIdArr {
			if pAppId == platformPos.PlatformAppID {
				DebugDataAssembly(mhReq, localPos, platformPos, "demand_req_data")
				break
			}
		}
	}

	tmpMHReq := *mhReq

	if isReplaced {
		tmpMHReq.Device.Imei = replaceDIDStu.Imei
		tmpMHReq.Device.ImeiMd5 = replaceDIDStu.ImeiMd5
		tmpMHReq.Device.AndroidID = replaceDIDStu.AndroidID
		tmpMHReq.Device.AndroidIDMd5 = replaceDIDStu.AndroidIDMd5
		tmpMHReq.Device.Oaid = replaceDIDStu.Oaid
		tmpMHReq.Device.Idfa = replaceDIDStu.Idfa
		tmpMHReq.Device.IdfaMd5 = replaceDIDStu.IdfaMd5
		tmpMHReq.Device.OsVersion = replaceDIDStu.OsVersion
		tmpMHReq.Device.Model = replaceDIDStu.Model
		tmpMHReq.Device.Manufacturer = replaceDIDStu.Manufacturer
		tmpMHReq.Device.DeviceStartSec = replaceDIDStu.DeviceStartSec
		tmpMHReq.Device.Country = replaceDIDStu.Country
		tmpMHReq.Device.Language = replaceDIDStu.Language
		tmpMHReq.Device.DeviceNameMd5 = replaceDIDStu.DeviceNameMd5
		tmpMHReq.Device.HardwareMachine = replaceDIDStu.HardwareMachine
		tmpMHReq.Device.HardwareModel = replaceDIDStu.HardwareModel
		tmpMHReq.Device.PhysicalMemoryByte = replaceDIDStu.PhysicalMemoryByte
		tmpMHReq.Device.HarddiskSizeByte = replaceDIDStu.HarddiskSizeByte
		tmpMHReq.Device.SystemUpdateSec = replaceDIDStu.SystemUpdateSec
		tmpMHReq.Device.TimeZone = replaceDIDStu.TimeZone
		// tmpMHReq.Device.CAID = replaceDIDStu.CAID
		// tmpMHReq.Device.CAIDVersion = replaceDIDStu.CAIDVersion

		if len(replaceDIDStu.CAIDMultiJson) > 0 {
			var tmpCAIDMulti []MHReqCAIDMulti
			json.Unmarshal([]byte(replaceDIDStu.CAIDMultiJson), &tmpCAIDMulti)

			tmpMHReq.Device.CAIDMulti = tmpCAIDMulti
		}

		tmpMHReq.Network.ConnectType = replaceDIDStu.ConnectType
		tmpMHReq.Network.Carrier = replaceDIDStu.Carrier

		tmpMHReq.Device.DIDMd5 = GetDeviceIDMd5Key(&tmpMHReq)
	}

	if randNum < utilities.WriteHoloDebugReqDataRatio && utilities.WriteHoloDebugReqDataRatio > 0 && len(utilities.DebugPAppId) > 0 {
		pAppIdArr := strings.Split(utilities.DebugPAppId, ",")
		for _, pAppId := range pAppIdArr {
			if pAppId == platformPos.PlatformAppID {
				DebugDataAssembly(&tmpMHReq, localPos, platformPos, "demand_replace_req_data")
				break
			}
		}
	}

	if tmpMHReq.Device.DIDMd5 == utils.Get16Md5("") {
		return
	}
	didMd5Key := tmpMHReq.Device.DIDMd5

	// 以上是上游的处理逻辑, 给业务提供 tmpMHReq 和 didMd5Key

	// 只有替换包用
	// limit_ip, req dau功能, 存redis维度ip did_md5, p_app_id, dd
	if isReplaced {
		saveLimitIPDIDMd5ToRedisV3(c, &tmpMHReq, localPos, platformPos, didMd5Key)
	}

	if utilities.DauSaveHolo {
		if mhReqBytes, err := json.Marshal(tmpMHReq); err == nil {
			newMhReq := device.ModelsMHReqToDeviceReq(mhReqBytes)
			if localPosBytes, err := json.Marshal(localPos); err == nil {
				newLocalPos := device.ModelsLocalPosStuToDeviceLocalPosStu(localPosBytes)
				if platformPosBytes, err := json.Marshal(platformPos); err == nil {
					newPlatformPos := device.ModelsPlatformPosStuToDevicePlatformPosStu(platformPosBytes)
					// dau.DauUpstreamReq(
					// 	c,
					// 	newMhReq,
					// 	newLocalPos,
					// 	newPlatformPos,
					// 	didMd5Key,
					// )
					go dau.DauDemandReqKafka(
						c,
						newMhReq,
						newLocalPos,
						newPlatformPos,
						didMd5Key,
					)
					// mau.MauUpstreamReq(
					// 	c,
					// 	newMhReq,
					// 	newLocalPos,
					// 	newPlatformPos,
					// 	didMd5Key,
					// )

					// did ip req
					go req.DidDemandDidReq(c, didMd5Key, tmpMHReq.Device.IP, platformPos.PlatformAppID)
					go req.DidDemandDidLastTimeReq(c, didMd5Key, tmpMHReq.Device.IP, platformPos.PlatformAppID)
					go req.DidDemandIpReq(c, didMd5Key, tmpMHReq.Device.IP, platformPos.PlatformAppID)

				}
			}
		}
	}
}

func saveLimitIPDIDMd5ToRedisV3(c context.Context, mhReq *MHReq, localPos *LocalPosStu, platformPos *PlatformPosStu, didMd5 string) {
	// 是否replace
	isNeedReplace := false
	if platformPos.PlatformAppIsLimitIPDau == 1 {
		isNeedReplace = true
	}

	if isNeedReplace {
	} else {
		return
	}

	// save to redis
	go func(platformAppID string, ip string, didMd5 string, osv string, model string) {

		// logUID := uuid.NewV4().String()

		exhIPRDIDKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_RDID_PREFIX, ip)
		exhIPRDIDField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_RDID_FIELDKEY,
			platformAppID, time.Now().Format("2006-01-02"),
			didMd5,
			utils.Get16Md5(strings.Replace(osv, " ", "", -1)+strings.ToLower(strings.Replace(model, " ", "", -1))))

		exhIPRDIDDAUField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_RDID_DAU_FIELDKEY,
			platformAppID, time.Now().Format("2006-01-02"),
		)

		// fmt.Println("kbg_debug_aaa 0:", logUID, exhIPRDIDKey, exhIPRDIDField, exhIPRDIDDAUField)

		// exHGETRDAUValue, redisErr := utils.RedisSendCommand(c, "EXHGET", exhIPRDIDKey, exhIPRDIDDAUField)
		_, redisErr := utils.RedisSendCommand(c, "EXHGET", exhIPRDIDKey, exhIPRDIDDAUField)
		if redisErr != nil {
			// fmt.Println("kbg_debug_aaa 1:", logUID, exhIPRDIDKey, exhIPRDIDField, exhIPRDIDDAUField)

			// redis error
			// 2、当日每个新出现的ip会被写入rdid max值
			// 80%的rdid max=3，ip key的ttl为4小时
			// 15%的rdid max=5，ip key的ttl为6小时
			// 5%的rdid max=10，ip key的ttl为12小时
			random1 := 80
			max1 := 3
			ttl1 := 4

			random2 := 15
			max2 := 5
			ttl2 := 6

			random3 := 5
			max3 := 10
			ttl3 := 12

			currentHour := utils.ConvertStringToInt(time.Now().Format("15"))
			maxIPDAUNum := 0
			ttlhour := 0
			randValue := rand.Intn(random1 + random2 + random3)
			if randValue < random1 {
				maxIPDAUNum = max1
				ttlhour = ttl1
				if ttl1 > 24-currentHour {
					ttlhour = 24 - currentHour
				}
			} else if randValue < (random1 + random2) {
				maxIPDAUNum = max2
				ttlhour = ttl2
				if ttl2 > 24-currentHour {
					ttlhour = 24 - currentHour
				}
			} else {
				maxIPDAUNum = max3
				ttlhour = ttl3
				if currentHour > 24-ttl3 {
					ttlhour = 24 - currentHour
				}
			}
			// fmt.Println("kbg_debug_aaa 2:", logUID, maxIPDAUNum, exhIPRDIDField, exhIPRDIDDAUField)
			if maxIPDAUNum > 0 {
				// set ip dau ttl
				utils.RedisSendCommand(c, "EXHSET", exhIPRDIDKey, exhIPRDIDDAUField, maxIPDAUNum, "EX", ttlhour*3600)

				// set ip did ttl
				utils.RedisSendCommand(c, "EXHSET", exhIPRDIDKey, exhIPRDIDField, osv+","+model, "EX", ttlhour*3600)

				// 删除对应的 field
				// redisResult, redisErr := utils.RedisSendCommand(c, "EXHKEYS", exhIPRDIDKey)
				// if redisErr != nil {
				// } else {
				// 	jsonByte, _ := json.Marshal(redisResult)

				// 	var tmpDIDMd5Array []string
				// 	json.Unmarshal(jsonByte, &tmpDIDMd5Array)
				// 	for _, item := range tmpDIDMd5Array {
				// 		if strings.HasPrefix(item, "did_"+platformAppID+"_"+time.Now().Format("2006-01-02")+"_") {
				// 			utils.RedisSendCommand(c, "EXHDEL", exhIPRDIDKey, item)
				// 		}
				// 	}
				// }
			}
		} else {
			// fmt.Println("kbg_debug_aaa 3:", logUID, exhIPRDIDKey, exhIPRDIDField, exhIPRDIDDAUField)

			// 取ip dau ttl设置
			redisResult, redisErr := utils.RedisSendCommand(c, "EXHTTL", exhIPRDIDKey, exhIPRDIDDAUField)
			if redisErr != nil {
			} else {
				utils.RedisSendCommand(c, "EXHSET", exhIPRDIDKey, exhIPRDIDField, osv+","+model, "EX", redisResult.(int64))
			}
		}

		randTTLKey, _ := u.RandomTTL(utilities.RandTTLMinHour, utilities.RandTTLMaxHour)
		db.GlbRedis.Expire(c, exhIPRDIDKey, randTTLKey)
	}(platformPos.PlatformAppID, mhReq.Device.IP, didMd5, mhReq.Device.OsVersion, mhReq.Device.Model)
}

func DebugDataAssembly(mhReq *MHReq, localPos *LocalPosStu, platformPos *PlatformPosStu, tableName string) {
	var bigdataAdxReqItem BigdataReqStu
	bigdataAdxReqItem.LocalAppID = localPos.LocalAppID
	bigdataAdxReqItem.LocalPosID = localPos.LocalPosID
	bigdataAdxReqItem.AppName = mhReq.App.AppName
	bigdataAdxReqItem.AppBundle = mhReq.App.AppBundleID
	bigdataAdxReqItem.OS = mhReq.Device.Os
	bigdataAdxReqItem.OSV = mhReq.Device.OsVersion

	bigdataAdxReqItem.PlatformAppID = platformPos.PlatformAppID
	bigdataAdxReqItem.PlatformPosID = platformPos.PlatformPosID

	bigdataAdxReqItem.DIDMd5 = mhReq.Device.DIDMd5
	bigdataAdxReqItem.Imei = mhReq.Device.Imei
	bigdataAdxReqItem.ImeiMd5 = mhReq.Device.ImeiMd5
	bigdataAdxReqItem.AndroidID = mhReq.Device.AndroidID
	bigdataAdxReqItem.AndroidIDMd5 = mhReq.Device.AndroidIDMd5
	bigdataAdxReqItem.Idfa = mhReq.Device.Idfa
	bigdataAdxReqItem.IdfaMd5 = mhReq.Device.IdfaMd5
	bigdataAdxReqItem.ScreenWidth = mhReq.Device.ScreenWidth
	bigdataAdxReqItem.ScreenHeight = mhReq.Device.ScreenHeight
	bigdataAdxReqItem.IP = mhReq.Device.IP
	bigdataAdxReqItem.UA = mhReq.Device.Ua
	bigdataAdxReqItem.Oaid = mhReq.Device.Oaid
	bigdataAdxReqItem.Model = mhReq.Device.Model
	bigdataAdxReqItem.Manufacturer = mhReq.Device.Manufacturer
	bigdataAdxReqItem.AppStoreVersion = mhReq.Device.AppStoreVersion
	bigdataAdxReqItem.HMSCoreVersion = mhReq.Device.HMSCoreVersion
	bigdataAdxReqItem.DeviceType = utils.ConvertIntToString(mhReq.Device.DeviceType)
	bigdataAdxReqItem.ConnectType = utils.ConvertIntToString(mhReq.Network.ConnectType)
	bigdataAdxReqItem.Carrier = utils.ConvertIntToString(mhReq.Network.Carrier)
	bigdataAdxReqItem.DownReqNum = mhReq.Pos.AdCount

	InsertDebugReq(bigdataAdxReqItem, tableName)
}

func InsertDebugReq(bigdataAdxReqItem BigdataReqStu, tableName string) {
	if utilities.SkipHologress {
		return
	}

	put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", tableName))
	uuidStr := uuid.NewV4().String()

	put.SetTextValByColName("id", uuidStr, len(uuidStr))
	put.SetTextValByColName("reqid", bigdataAdxReqItem.UID, len(bigdataAdxReqItem.UID))
	put.SetTextValByColName("app_id", bigdataAdxReqItem.LocalAppID, len(bigdataAdxReqItem.LocalAppID))
	put.SetTextValByColName("pos_id", bigdataAdxReqItem.LocalPosID, len(bigdataAdxReqItem.LocalPosID))
	put.SetTextValByColName("p_app_id", bigdataAdxReqItem.PlatformAppID, len(bigdataAdxReqItem.PlatformAppID))
	put.SetTextValByColName("p_pos_id", bigdataAdxReqItem.PlatformPosID, len(bigdataAdxReqItem.PlatformPosID))
	put.SetTextValByColName("channel", bigdataAdxReqItem.Channel, len(bigdataAdxReqItem.Channel))
	put.SetTextValByColName("app_name", bigdataAdxReqItem.AppName, len(bigdataAdxReqItem.AppName))
	put.SetTextValByColName("app_bundle", bigdataAdxReqItem.AppBundle, len(bigdataAdxReqItem.AppBundle))
	put.SetTextValByColName("os", bigdataAdxReqItem.OS, len(bigdataAdxReqItem.OS))
	put.SetTextValByColName("osv", bigdataAdxReqItem.OSV, len(bigdataAdxReqItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxReqItem.DIDMd5, len(bigdataAdxReqItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxReqItem.Imei, len(bigdataAdxReqItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxReqItem.ImeiMd5, len(bigdataAdxReqItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxReqItem.AndroidID, len(bigdataAdxReqItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxReqItem.AndroidIDMd5, len(bigdataAdxReqItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxReqItem.Idfa, len(bigdataAdxReqItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxReqItem.IdfaMd5, len(bigdataAdxReqItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxReqItem.IP, len(bigdataAdxReqItem.IP))
	put.SetTextValByColName("ua", bigdataAdxReqItem.UA, len(bigdataAdxReqItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxReqItem.Oaid, len(bigdataAdxReqItem.Oaid))
	put.SetTextValByColName("model", bigdataAdxReqItem.Model, len(bigdataAdxReqItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxReqItem.Manufacturer, len(bigdataAdxReqItem.Manufacturer))
	put.SetInt32ValByColName("screen_width", int32(bigdataAdxReqItem.ScreenWidth))
	put.SetInt32ValByColName("screen_height", int32(bigdataAdxReqItem.ScreenHeight))
	put.SetTextValByColName("appstore_version", bigdataAdxReqItem.AppStoreVersion, len(bigdataAdxReqItem.AppStoreVersion))
	put.SetTextValByColName("hms_version", bigdataAdxReqItem.HMSCoreVersion, len(bigdataAdxReqItem.HMSCoreVersion))
	put.SetTextValByColName("device_type", bigdataAdxReqItem.DeviceType, len(bigdataAdxReqItem.DeviceType))
	put.SetTextValByColName("connect_type", bigdataAdxReqItem.ConnectType, len(bigdataAdxReqItem.ConnectType))
	put.SetTextValByColName("carrier", bigdataAdxReqItem.Carrier, len(bigdataAdxReqItem.Carrier))
	put.SetInt32ValByColName("down_req_num", int32(bigdataAdxReqItem.DownReqNum))
	put.SetInt32ValByColName("down_resp_num", int32(bigdataAdxReqItem.DownRespNum))
	put.SetInt32ValByColName("down_cost_time", int32(bigdataAdxReqItem.DownCostTime))
	put.SetInt32ValByColName("up_req_time", int32(bigdataAdxReqItem.UpReqTime))
	put.SetInt32ValByColName("up_req_num", int32(bigdataAdxReqItem.UpReqNum))
	put.SetInt32ValByColName("up_resp_time", int32(bigdataAdxReqItem.UpRespTime))
	put.SetInt32ValByColName("up_resp_num", int32(bigdataAdxReqItem.UpRespNum))
	put.SetInt32ValByColName("up_resp_ok_num", int32(bigdataAdxReqItem.UpRespOkNum))
	put.SetInt32ValByColName("up_cost_time", int32(bigdataAdxReqItem.UpCostTime))
	put.SetInt32ValByColName("code", int32(bigdataAdxReqItem.Code))
	put.SetInt32ValByColName("internal_code", int32(bigdataAdxReqItem.InternalCode))
	put.SetInt32ValByColName("up_resp_code", int32(bigdataAdxReqItem.UpRespCode))
	put.SetInt32ValByColName("up_price_report_win_num", int32(bigdataAdxReqItem.UpPriceReportWinNum))
	put.SetInt32ValByColName("up_price_report_failed_num", int32(bigdataAdxReqItem.UpPriceReportFailedNum))
	put.SetInt32ValByColName("is_win", int32(bigdataAdxReqItem.IsWin))
	put.SetInt32ValByColName("floor_price", int32(bigdataAdxReqItem.FloorPrice))
	put.SetInt32ValByColName("final_price", int32(bigdataAdxReqItem.FinalPrice))
	put.SetInt32ValByColName("up_price", int32(bigdataAdxReqItem.UpPrice))
	put.SetInt32ValByColName("up_resp_failed_num", int32(bigdataAdxReqItem.UpRespFailedNum))
	put.SetInt64ValByColName("bitcode", int64(bigdataAdxReqItem.BitCode))
	put.SetInt32ValByColName("is_report_idfa", int32(bigdataAdxReqItem.UpReportIDFA))
	put.SetInt32ValByColName("is_report_caid", int32(bigdataAdxReqItem.UpReportCAID))
	put.SetInt32ValByColName("is_report_ios_element", int32(bigdataAdxReqItem.UpReportYinZi))

	day := time.Now().Format("2006-01-02")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxSSPDataDb.Submit(put)
}

func IsNeedReplaceModel(c context.Context, model string) bool {
	isBanned := false

	var modelBlacklistRules SspModelBlacklistRules

	cacheKey := "ssp_model_replacelist_rules"
	cacheValue, cacheErr := db.GlbBigCacheMinute.Get(cacheKey)

	if cacheErr != nil {
		redisCacheValue, redisCacheErr := db.GlbRedis.Get(c, cacheKey).Result()
		if redisCacheErr == nil {
			json.Unmarshal([]byte(redisCacheValue), &modelBlacklistRules)
		}
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisCacheValue))
	} else {
		json.Unmarshal(cacheValue, &modelBlacklistRules)
	}

	for _, modelBlacklistRule := range modelBlacklistRules {
		matched, _ := regexp.MatchString(modelBlacklistRule, model)
		if matched {
			return true
		}
	}

	return isBanned
}
