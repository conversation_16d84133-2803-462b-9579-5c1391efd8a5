package models

type MaterialFullStu struct {
	ID            int    `json:"id,omitempty"`
	SupplyPosType int    `json:"supply_pos_type,omitempty"`
	EmptyRuleList string `json:"empty_rule_list,omitempty"`
}

type EmptyRuleListStu struct {
	PlatformId    string         `json:"platform_id,omitempty"`
	EmptyItemList []EmptyItemStu `json:"empty_item_list,omitempty"`
}

type EmptyItemStu struct {
	EmptyType    string `json:"empty_type,omitempty"`
	Value        string `json:"value,omitempty"`
	MaterialType string `json:"material_type,omitempty"`
}

type PackageRuleListStu struct {
	AndroidPackageName string `json:"android_package_name"`
	IosPackageName     string `json:"ios_package_name"`
	DeepLink           string `json:"deep_link"`
}

type TypeRuleListStu struct {
	Id                int    `json:"id"`
	LandingPageSwitch int    `json:"landing_page_switch"`
	Sdk               string `json:"sdk"`
	SdkFilter         string `json:"sdk_filter"`
	AppIds            string `json:"app_ids"`
	PosIds            string `json:"pos_ids"`
}

type TypeRuleListBigCache struct {
	LandingPageSwitch int    `json:"landing_page_switch"`
	Sdk               string `json:"sdk"`
	SdkFilter         string `json:"sdk_filter"`
	AppId             string `json:"app_id"`
	PosId             string `json:"pos_id"`
}
