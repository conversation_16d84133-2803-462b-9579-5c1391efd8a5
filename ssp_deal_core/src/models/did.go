package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/utils"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
)

// GetReplaceDIDFromRedis ...
func GetReplaceDIDFromRedis(c context.Context, localPos *LocalPosStu, redisReplaceKey string, replaceDIDFlag int, manufactureKey string) (string, error) {

	firstLetter := GetReplaceDIDRandomSliceID(localPos, replaceDIDFlag)

	tmpRedisKey := redisReplaceKey + "_" + firstLetter
	if len(manufactureKey) > 0 {
		tmpRedisKey = tmpRedisKey + "_" + manufactureKey
	}

	scanRedisKey := GetRandom16()

	result, redisError := getReplaceDIDLessFromRedis(c, tmpRedisKey, scanRedisKey)
	if redisError != nil {
		result, redisError = getReplaceDIDMoreFromRedis(c, tmpRedisKey, scanRedisKey)

	}

	return result, redisError
}

func getReplaceDIDLessFromRedis(c context.Context, replaceRedisKey string, scanRedisKey string) (string, error) {

	redisValue, redisErr := utils.RedisSendCommand(c, "EXHSCAN", replaceRedisKey, "<=", scanRedisKey, "COUNT", 1)

	// fmt.Println("kbg_debug less random key:", replaceRedisKey, scanRedisKey)
	// fmt.Println(redisErr)
	if redisErr != nil {
	} else {
		// fmt.Println(redisValue)

		jsonByte, _ := json.Marshal(redisValue)
		// fmt.Println(string(resByte))
		// fmt.Println(jsonByteErr)

		var element1 []interface{}
		json.Unmarshal(jsonByte, &element1)
		// fmt.Println("kbg_debug less element1 len: ", len(element1), element1)
		if len(element1) < 2 {
			return "", errors.New("redis error 0")
		}

		jsonByte, _ = json.Marshal(element1[1])
		var element2 []string
		json.Unmarshal(jsonByte, &element2)
		if len(element2) < 2 {
			return "", errors.New("redis error 1")
		}
		// fmt.Println("kbg_debug less element2 len: ", len(element2), element2)

		return element2[1], nil
	}
	// fmt.Println(redisErr)

	return "", errors.New("redis error 2")
}

func getReplaceDIDMoreFromRedis(c context.Context, replaceRedisKey string, scanRedisKey string) (string, error) {

	redisValue, redisErr := utils.RedisSendCommand(c, "EXHSCAN", replaceRedisKey, ">", scanRedisKey, "COUNT", 1)

	// fmt.Println("kbg_debug more random key:", replaceRedisKey, scanRedisKey)
	// fmt.Println(redisErr)
	if redisErr != nil {
	} else {
		// fmt.Println(redisValue)

		jsonByte, _ := json.Marshal(redisValue)
		// fmt.Println(string(resByte))
		// fmt.Println(jsonByteErr)

		var element1 []interface{}
		json.Unmarshal(jsonByte, &element1)
		// fmt.Println("kbg_debug more element1 len: ", len(element1), element1)
		if len(element1) < 2 {
			return "", errors.New("redis error 3")
		}

		jsonByte, _ = json.Marshal(element1[1])
		var element2 []string
		json.Unmarshal(jsonByte, &element2)
		if len(element2) < 2 {
			return "", errors.New("redis error 4")
		}
		// fmt.Println("kbg_debug more element2 len: ", len(element2), element2)

		return element2[1], nil
	}
	// fmt.Println(redisErr)

	return "", errors.New("redis error 5")
}

func GetRandom16() string {
	baseSliceArray := []string{"0", "1", "2", "3", "4", "5", "6", "7",
		"8", "9", "a", "b", "c", "d", "e", "f"}

	return baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)]
}

func GetRandom15() string {
	baseSliceArray := []string{"0", "1", "2", "3", "4", "5", "6", "7",
		"8", "9", "a", "b", "c", "d", "e", "f"}

	return baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)]
}

func GetRandom14() string {
	baseSliceArray := []string{"0", "1", "2", "3", "4", "5", "6", "7",
		"8", "9", "a", "b", "c", "d", "e", "f"}

	return baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)] +
		baseSliceArray[rand.Intn(16)] + baseSliceArray[rand.Intn(16)]
}

func GetRandom1() string {
	baseSliceArray := []string{"0", "1", "2", "3", "4", "5", "6", "7",
		"8", "9", "a", "b", "c", "d", "e", "f"}

	return baseSliceArray[rand.Intn(16)]
}

func GetRandemScan(key string) string {
	if "key" == "0" {
		baseSliceArray := []string{"0", "1"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "1" {
		baseSliceArray := []string{"2", "3"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "2" {
		baseSliceArray := []string{"4", "5"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "3" {
		baseSliceArray := []string{"6", "7"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "4" {
		baseSliceArray := []string{"8", "9"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "5" {
		baseSliceArray := []string{"a", "b"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "6" {
		baseSliceArray := []string{"c", "d"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	} else if "key" == "7" {
		baseSliceArray := []string{"e", "f"}
		return baseSliceArray[rand.Intn(len(baseSliceArray))]
	}

	return GetRandom1()
}

func GetReplaceDIDSliceID(localPos *LocalPosStu, str string, replaceParams string) string {

	if replaceParams == "ip+osv+model" {
		return "0"
	} else if replaceParams == "ip+osv" {
		return "0"
	} else if replaceParams == "ip+model" {
		return "0"
	}

	// ip包 16分片
	return str

	// // from hex to dec
	// dec, err := strconv.ParseUint(str, 16, 32)
	// if err != nil {
	// 	return "0"
	// }

	// // ip包
	// if dec > 11 {
	// 	return "3"
	// } else if dec > 7 {
	// 	return "2"
	// } else if dec > 3 {
	// 	return "1"
	// }
	// return "0"
}

func GetReplaceDIDRandomSliceID(localPos *LocalPosStu, replaceDIDFlag int) string {

	if replaceDIDFlag == 1 {
		return "0"
	} else if replaceDIDFlag == 2 {
		return "0"
	} else if replaceDIDFlag == 3 {
		return "0"
	}

	// ip包
	// baseSliceArray := []string{"0", "1", "2", "3"}
	baseSliceArray := []string{"0", "1", "2", "3", "4", "5", "6", "7",
		"8", "9", "a", "b", "c", "d", "e", "f"}
	return baseSliceArray[rand.Intn(len(baseSliceArray))]
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// GetMagicReplaceDIDLibFromRedis ...
func GetMagicReplaceDIDLibFromRedis(c context.Context, didMd5Key string, exhashBaseKey string, mhReq *MHReq,
	localPos *LocalPosStu, platformPos *PlatformPosStu) (string, error) {

	return GetMagicReplaceDIDLibFromRedisV3(c, didMd5Key, exhashBaseKey, mhReq, localPos, platformPos)
}

// GetMagicReplaceDIDLibFromRedisV1 ...
func GetMagicReplaceDIDLibFromRedisV1(c context.Context, didMd5Key string, exhashBaseKey string) (string, error) {

	tmpRedisKey := exhashBaseKey + "_" + getSliceValue(didMd5Key)

	// random 128片
	slice1 := GetRandom1()
	slice2 := utils.ConvertIntToString(rand.Intn(8))
	scan1 := slice1
	scan2 := GetRandemScan(scan1)

	randSlice := slice1 + slice2
	tmpRedisKey1 := exhashBaseKey + "_" + randSlice

	tmpDynamicRedisKey := exhashBaseKey + "_" + "dynamic"

	// 1. 查是否请求didmd5在替换包里
	// 2. 查替换包随机128片是否包含 (举例: ssp_mrlib_fb90bb6370f63202_13_rmo-an00_南京_30, 313bd7609b75674e)
	// 3. 查替换包dynamic是否包含 (举例: ssp_mrlib_2e692486156404f9_13_pgw110_沈阳_dynamic)
	result, redisError := getMagicRealReplaceDIDFromRedis(c, tmpRedisKey, didMd5Key)
	if redisError != nil {
		scanRedisKey := scan1 + scan2 + GetRandom14()

		result, redisError = getReplaceDIDLessFromRedis(c, tmpRedisKey1, scanRedisKey)
		if redisError != nil {
			result, redisError = getReplaceDIDMoreFromRedis(c, tmpRedisKey1, scanRedisKey)
			if redisError != nil {
				result, redisError = getReplaceDIDLessFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
				if redisError != nil {
					result, redisError = getReplaceDIDMoreFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
				}
			}
		}
	}

	return result, redisError
}

// 替换包v3：使用方案更新
// 1、概念上分为did（待使用的替换包库）和rdid（已使用的的替换包库）
// 2、当日每个新出现的ip会被写入rdid max值，80%的rdid max=3，ip key的ttl为4小时；15%的rdid max=5，ip key的ttl为6小时；5%的rdid max=10，ip key的ttl为12小时
// 3、同ip下的rdid的osv+model按规则处理：android不允许重复出现，ios时rdid max=3时最大允许重复出现2次，rdid max=5/10时最大允许重复出现3次
// 4、同ip下，如果请求时rdid能匹配成功，则ttl延长一小时
// 5、先在快手中实现，然后优量汇，后续支持ui配置

// ip: *************

// exhash key :
// ssp_ip_rdid_[ip]
// ssp_ip_rdid_*************

// exhash field:
// did_[p_app_id]_[dd]_[did_md5]_[md5(osv+model)]
// did_dau_[p_app_id]_[dd]

// did_516400007_2023-11-01_516fe254e3abeac0_2484401e5528d33b   (max_num, ttl 4 hour),  如果匹配成功, 所有did和dau  ttl +1小时
// did_516400007_2023-11-01_979e80fa2f70d7f0_fefa8ec8818797db
// did_516400007_2023-11-01_03fcfb58a8e886b6_e69af4ee4747d7db

// did_516400008_2023-11-01_4800567012234bda_e69af4ee4747d7db
// did_516400008_2023-11-01_5401844223b4f1ee_e69af4ee4747d7db
// did_516400008_2023-11-01_c404b3a82902e1e8_e69af4ee4747d7db
// did_516400009_2023-11-01_017be417bb7405f9_2484401e5528d33b
// did_516400009_2023-11-01_2066073b337c979a_2484401e5528d33b
// did_516400012_2023-11-01_15dfabce737282b8_e69af4ee4747d7db
// did_516400012_2023-11-01_ec064fd7a723dd7e_e69af4ee4747d7db

// did_dau_516400007_2023-11-01  —> 80% = 3 (max_num, ttl 4 hour, 删除did_[p_app_id]_[dd]_[did_md5]_[md5(osv+model)]) ,  如果匹配成功, 所有did, ttl +1小时 <= 4+1
// did_dau_516400008_2023-11-01  —> 15% = 5  (max_num, ttl 6 hour),  如果匹配成功, 如果匹配成功, 所有did，ttl, +1小时 <= 6+1
// did_dau_516400009_2023-11-01  —> 5% = 10  (max_num, ttl 12 hour),  如果匹配成功, 如果匹配成功, 所有did，ttl, +1小时 <= 12+1
// GetMagicReplaceDIDLibFromRedisV3 ...
func GetMagicReplaceDIDLibFromRedisV3(c context.Context, didMd5Key string, exhashBaseKey string,
	mhReq *MHReq, localPos *LocalPosStu, platformPos *PlatformPosStu) (string, error) {
	// logUID := uuid.NewV4().String()

	tmpRedisKey := exhashBaseKey + "_" + getSliceValue(didMd5Key)

	// random 128片
	slice1 := GetRandom1()
	slice2 := utils.ConvertIntToString(rand.Intn(8))
	scan1 := slice1
	scan2 := GetRandemScan(scan1)

	randSlice := slice1 + slice2
	tmpRedisKey1 := exhashBaseKey + "_" + randSlice

	tmpDynamicRedisKey := exhashBaseKey + "_" + "dynamic"

	// 1. 查是否请求didmd5在替换包里
	// 2. ip dau v3版本(描述见注释)
	// 3. 查替换包随机128片是否包含 (举例: ssp_mrlib_fb90bb6370f63202_13_rmo-an00_南京_30, 313bd7609b75674e)
	// 4. 查替换包dynamic是否包含 (举例: ssp_mrlib_2e692486156404f9_13_pgw110_沈阳_dynamic)
	result, redisError := getMagicRealReplaceDIDFromRedis(c, tmpRedisKey, didMd5Key)

	// 是否走step 2
	isNeedReplace := false
	if platformPos.PlatformAppIsLimitIPDau == 1 {
		isNeedReplace = true
	}
	if isNeedReplace {
		if redisError != nil {
			tmpDauDIDMd5Key := ""

			// md5(osv+model)
			tmpOSVModel16Md5 := utils.Get16Md5(strings.Replace(mhReq.Device.OsVersion, " ", "", -1) + strings.ToLower(strings.Replace(mhReq.Device.Model, " ", "", -1)))
			// 是否cache中存在
			miniuteDIDCacheKey := "ssp_ip_" + mhReq.Device.IP + "_did_" + platformPos.PlatformAppID + "_" + time.Now().Format("2006-01-02")
			miniuteDIDDAUCacheKey := "ssp_ip_" + mhReq.Device.IP + "_did_dau_" + platformPos.PlatformAppID + "_" + time.Now().Format("2006-01-02")
			didCacheValue, cacheErr := db.GlbBigCacheMinute.Get(miniuteDIDCacheKey)
			didDAUCacheValue, cacheErr := db.GlbBigCacheMinute.Get(miniuteDIDDAUCacheKey)

			// fmt.Println("kbg_debug_replace 1:", logUID, miniuteDIDDAUCacheKey, miniuteDIDCacheKey, mhReq.Device.OsVersion, mhReq.Device.Model, tmpOSVModel16Md5)
			// fmt.Println("kbg_debug_replace 2:", logUID, cacheErr)
			// fmt.Println("kbg_debug_replace 3:", logUID, string(didDAUCacheValue), string(didCacheValue))
			if cacheErr != nil {
				// cache error
				// EXHKEYS ssp_ip_rdid_111.18.147.111
				exhIPRDIDKey := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_RDID_PREFIX, mhReq.Device.IP)
				exhIPRDIDDAUField := fmt.Sprintf(rediskeys.ADX_SSP_DEVICE_IP_RDID_DAU_FIELDKEY, platformPos.PlatformAppID, time.Now().Format("2006-01-02"))

				redisResult, redisErr := utils.RedisSendCommand(c, "EXHGET", exhIPRDIDKey, exhIPRDIDDAUField)
				if redisErr != nil {
				} else {
					maxIPDIDDAUNum := utils.ConvertStringToInt64(redisResult.(string))
					// fmt.Println("kbg_debug_replace 4:", logUID, maxIPDIDDAUNum)

					////////////////////////////////////////////////////////////////////////////////////////
					redisResult, redisErr := utils.RedisSendCommand(c, "EXHKEYS", exhIPRDIDKey)
					if redisErr != nil {
					} else {
						jsonByte, _ := json.Marshal(redisResult)

						// 通过 EXHKEYS 取所有的key
						var tmpDIDMd5Array []string
						json.Unmarshal(jsonByte, &tmpDIDMd5Array)
						// fmt.Println("kbg_debug_replace 5:", logUID, exhIPRDIDKey, tmpDIDMd5Array)

						// 过滤字符串: did_[p_app_id]_[dd]_
						var tmpDIDMd5ArrayFilter []string
						for _, item := range tmpDIDMd5Array {
							if strings.HasPrefix(item, "did_"+platformPos.PlatformAppID+"_"+time.Now().Format("2006-01-02")+"_") {
								tmpItem := item
								// tmpItem = strings.Replace(tmpItem, "did_"+platformPos.PlatformAppID+"_"+time.Now().Format("2006-01-02")+"_", "", -1)
								tmpDIDMd5ArrayFilter = append(tmpDIDMd5ArrayFilter, tmpItem)
							}
						}
						// fmt.Println("kbg_debug_replace 6:", logUID, tmpDIDMd5ArrayFilter)

						// 过滤字符串 md5(osv+model)
						var tmpDIDMd5ArrayFilterOsvModel []string
						for _, item := range tmpDIDMd5ArrayFilter {
							if strings.HasSuffix(item, tmpOSVModel16Md5) {
								tmpDIDMd5ArrayFilterOsvModel = append(tmpDIDMd5ArrayFilterOsvModel, item)
								break
							}
						}
						// fmt.Println("kbg_debug_replace 7:", logUID, tmpDIDMd5ArrayFilterOsvModel)

						// android不允许重复出现, 取到直接返回
						// ios时rdid max=3时最大允许重复出现2次, rdid max=5/10时最大允许重复出现3次
						isFound := false
						if mhReq.Device.Os == "android" {
							if len(tmpDIDMd5ArrayFilterOsvModel) > 0 {
								isFound = true
								// fmt.Println("kbg_debug_replace 7 android true:", logUID)
							}
						} else if mhReq.Device.Os == "ios" {
							if maxIPDIDDAUNum == 3 && len(tmpDIDMd5ArrayFilterOsvModel) >= 2 {
								isFound = true
								// fmt.Println("kbg_debug_replace 7 ios true 1:", logUID)
							} else if maxIPDIDDAUNum == 5 && len(tmpDIDMd5ArrayFilterOsvModel) >= 3 {
								isFound = true
								// fmt.Println("kbg_debug_replace 7 ios true 2:", logUID)
							} else if maxIPDIDDAUNum == 10 && len(tmpDIDMd5ArrayFilterOsvModel) >= 3 {
								isFound = true
								// fmt.Println("kbg_debug_replace 7 ios true 3:", logUID)
							}
						}

						if isFound {
							// 设置cache
							db.GlbBigCacheMinute.Set(miniuteDIDCacheKey, []byte(strings.Join(tmpDIDMd5ArrayFilter, ",")))
							db.GlbBigCacheMinute.Set(miniuteDIDDAUCacheKey, []byte(utils.ConvertInt64ToString(maxIPDIDDAUNum)))

							// ttl如果不满1小时, +1小时
							redisResult, redisErr := utils.RedisSendCommand(c, "EXHTTL", exhIPRDIDKey, exhIPRDIDDAUField)
							if redisErr != nil {
							} else {
								if redisResult.(int64) < 3600 {
									// dau ttl + 1小时
									utils.RedisSendCommand(c, "EXHEXPIRE", exhIPRDIDKey, exhIPRDIDDAUField, redisResult.(int64)+3600)

									// dau did ttl + 1小时
									for _, item := range tmpDIDMd5ArrayFilter {
										utils.RedisSendCommand(c, "EXHEXPIRE", exhIPRDIDKey, item, redisResult.(int64)+3600)
									}
								}
							}

							// fmt.Println("kbg_debug_replace 7 a:", logUID, tmpDIDMd5ArrayFilterOsvModel)

							// 查找rediskey md5
							tmpDIDMd5Value := tmpDIDMd5ArrayFilterOsvModel[rand.Intn(len(tmpDIDMd5ArrayFilterOsvModel))]
							// fmt.Println("kbg_debug_replace 7 b:", logUID, tmpDIDMd5Value)

							tmpDIDMd5Value = strings.Replace(tmpDIDMd5Value, "did_"+platformPos.PlatformAppID+"_"+time.Now().Format("2006-01-02")+"_", "", -1)
							// fmt.Println("kbg_debug_replace 7 c:", logUID, tmpDIDMd5Value)

							tmpDauDIDMd5Array := strings.Split(tmpDIDMd5Value, "_")
							// fmt.Println("kbg_debug_replace 7 d:", logUID, tmpDauDIDMd5Array)

							if len(tmpDauDIDMd5Array) >= 2 {
								tmpDauDIDMd5Key = tmpDauDIDMd5Array[0]
								// fmt.Println("kbg_debug_replace 7 e:", logUID, tmpDauDIDMd5Array)

							}
						} else {
							// 未查到, 查看是否满足dau
							if len(tmpDIDMd5ArrayFilter) > int(maxIPDIDDAUNum) {
								// fmt.Println("kbg_debug_replace 8 error:", logUID, len(tmpDIDMd5ArrayFilter), maxIPDIDDAUNum)

								return "", errors.New("dau is error")
							}
						}
						// fmt.Println("kbg_debug_replace 9:", logUID, tmpDauDIDMd5Key)
						////////////////////////////////////////////////////////////////////////////////////////
					}
				}
			} else {
				tmpDIDMd5ArrayFilter := strings.Split(string(didCacheValue), ",")
				// fmt.Println("kbg_debug_replace 10:", logUID, tmpDIDMd5ArrayFilter)

				// 过滤字符串 md5(osv+model)
				var tmpDIDMd5ArrayFilterOsvModel []string
				for _, item := range tmpDIDMd5ArrayFilter {
					if strings.HasSuffix(item, tmpOSVModel16Md5) {
						tmpDIDMd5ArrayFilterOsvModel = append(tmpDIDMd5ArrayFilterOsvModel, item)
						break
					}
				}

				if len(tmpDIDMd5ArrayFilterOsvModel) > 0 {
					tmpDIDMd5Value := tmpDIDMd5ArrayFilterOsvModel[rand.Intn(len(tmpDIDMd5ArrayFilterOsvModel))]
					tmpDIDMd5Value = strings.Replace(tmpDIDMd5Value, "did_"+platformPos.PlatformAppID+"_"+time.Now().Format("2006-01-02")+"_", "", -1)
					tmpDauDIDMd5Array := strings.Split(tmpDIDMd5Value, "_")
					if len(tmpDauDIDMd5Array) >= 2 {
						tmpDauDIDMd5Key = tmpDauDIDMd5Array[0]
					}
					// fmt.Println("kbg_debug_replace 11:", logUID, tmpDauDIDMd5Key)
				}
				// fmt.Println("kbg_debug_replace 12:", logUID, tmpDauDIDMd5Key)

				if len(tmpDauDIDMd5Key) == 0 {
					// fmt.Println("kbg_debug_replace 13:", logUID, len(tmpDIDMd5ArrayFilter), utils.ConvertStringToInt(string(didDAUCacheValue)))

					if len(tmpDIDMd5ArrayFilter) >= utils.ConvertStringToInt(string(didDAUCacheValue)) {
						// fmt.Println("kbg_debug_replace 14:", logUID, len(tmpDIDMd5ArrayFilter), utils.ConvertStringToInt(string(didDAUCacheValue)))

						return "", errors.New("dau is error")
					}
				}
			}

			if len(tmpDauDIDMd5Key) > 0 {
				tmpDauRedisKey := exhashBaseKey + "_" + getSliceValue(tmpDauDIDMd5Key)
				// fmt.Println("kbg_debug_replace 15:", logUID, tmpDauRedisKey, tmpDauDIDMd5Key)
				result, redisError = getMagicRealReplaceDIDFromRedis(c, tmpDauRedisKey, tmpDauDIDMd5Key)
			}
		}
	}
	if redisError != nil {
		scanRedisKey := scan1 + scan2 + GetRandom14()

		result, redisError = getReplaceDIDLessFromRedis(c, tmpRedisKey1, scanRedisKey)
		if redisError != nil {
			result, redisError = getReplaceDIDMoreFromRedis(c, tmpRedisKey1, scanRedisKey)
			if redisError != nil {
				result, redisError = getReplaceDIDLessFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
				if redisError != nil {
					result, redisError = getReplaceDIDMoreFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
				}
			}
		}
	}

	return result, redisError
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// debug 1e6853da015a4d0a
// func GetMagicReplaceDIDLibFromRedisDebug(c context.Context, didMd5Key string, exhashBaseKey string) (string, error) {
// 	debugStepCode := 0

// 	tmpRedisKey := exhashBaseKey + "_" + getSliceValue(didMd5Key)

// 	// random 128片
// 	randSlice := GetRandom1() + utils.ConvertIntToString(rand.Intn(8))
// 	tmpRedisKey1 := exhashBaseKey + "_" + randSlice

// 	tmpDynamicRedisKey := exhashBaseKey + "_" + "dynamic"

// 	// 1. 查是否请求didmd5在替换包里
// 	// 2. 查替换包随机128片是否包含 (举例: ssp_mrlib_fb90bb6370f63202_13_rmo-an00_南京_30, 313bd7609b75674e)
// 	// 3. 查替换包dynamic是否包含 (举例: ssp_mrlib_2e692486156404f9_13_pgw110_沈阳_dynamic)
// 	scanRedisKey := randSlice + GetRandom14()

// 	result, redisError := getMagicRealReplaceDIDFromRedis(c, tmpRedisKey, didMd5Key)
// 	if redisError != nil {
// 		debugStepCode = 1

// 		result, redisError = getReplaceDIDLessFromRedis(c, tmpRedisKey1, scanRedisKey)
// 		if redisError != nil {
// 			debugStepCode = 2

// 			result, redisError = getReplaceDIDMoreFromRedis(c, tmpRedisKey1, scanRedisKey)
// 			if redisError != nil {
// 				debugStepCode = 3

// 				result, redisError = getReplaceDIDLessFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
// 				if redisError != nil {
// 					debugStepCode = 4

// 					result, redisError = getReplaceDIDMoreFromRedis(c, tmpDynamicRedisKey, scanRedisKey)
// 				}
// 			}
// 		}
// 	}

// 	if strings.Contains(exhashBaseKey, "1e6853da015a4d0a") {
// 		isFoundCode := 0
// 		if redisError != nil {
// 		} else {
// 			isFoundCode = 1
// 		}

// 		go func() {
// 			defer func() {
// 				if err := recover(); err != nil {
// 					fmt.Println("redis did panic:", err)
// 				}
// 			}()
// 			saveReplaceDIDDebugToHolo(c, didMd5Key, tmpRedisKey, tmpRedisKey1, tmpDynamicRedisKey, scanRedisKey, debugStepCode, isFoundCode)
// 		}()
// 	}

// 	return result, redisError
// }

// var debugReplaceDIDToHoloArray []DebugReplaceDIDStu
// var debugReplaceDIDToHoloMutex sync.Mutex

// func saveReplaceDIDDebugToHolo(c context.Context, didMd5 string, realRedisKey string, randomRedisKey string, dynamicRedisKey string, scanRedisKey string, stepCode int, isFound int) {
// 	var debugReplaceDIDStu DebugReplaceDIDStu
// 	debugReplaceDIDStu.DIDMd5 = didMd5
// 	debugReplaceDIDStu.RealRedisKey = realRedisKey
// 	debugReplaceDIDStu.RandomRedisKey = randomRedisKey
// 	debugReplaceDIDStu.DynamicRedisKey = dynamicRedisKey
// 	debugReplaceDIDStu.ScanRedisKey = scanRedisKey
// 	debugReplaceDIDStu.StepCode = stepCode
// 	debugReplaceDIDStu.IsFound = isFound

// 	debugReplaceDIDToHoloMutex.Lock()
// 	debugReplaceDIDToHoloArray = append(debugReplaceDIDToHoloArray, debugReplaceDIDStu)

// 	if len(debugReplaceDIDToHoloArray) < 10000 {
// 		debugReplaceDIDToHoloMutex.Unlock()
// 		return
// 	}

// 	destDebugArray := debugReplaceDIDToHoloArray[0:]
// 	debugReplaceDIDToHoloArray = debugReplaceDIDToHoloArray[0:0]
// 	debugReplaceDIDToHoloMutex.Unlock()

// 	tableName := "replace_did_redis"

// 	for _, item := range destDebugArray {

// 		put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", tableName))

// 		uuid := uuid.NewV4().String()
// 		put.SetTextValByColName("id", uuid, len(uuid))
// 		put.SetTextValByColName("did_md5", item.DIDMd5, len(item.DIDMd5))
// 		put.SetTextValByColName("real_redis_key", item.RealRedisKey, len(item.RealRedisKey))
// 		put.SetTextValByColName("random_redis_key", item.RandomRedisKey, len(item.RandomRedisKey))
// 		put.SetTextValByColName("dynamic_redis_key", item.DynamicRedisKey, len(item.DynamicRedisKey))
// 		put.SetTextValByColName("scan_redis_key", item.ScanRedisKey, len(item.ScanRedisKey))
// 		put.SetInt32ValByColName("step_code", int32(item.StepCode))
// 		put.SetInt32ValByColName("is_found", int32(item.IsFound))

// 		day := time.Now().Format("2006-01-02")
// 		hour := time.Now().Format("15")
// 		minute := time.Now().Format("04")
// 		put.SetTextValByColName("dd", day, len(day))
// 		put.SetTextValByColName("hh", hour, len(hour))
// 		put.SetTextValByColName("mm", minute, len(minute))
// 		put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

// 		db.GlbHologresAdxSSPDataDb.Submit(put)
// 	}
// }

// // DebugReplaceDIDStu ...
// type DebugReplaceDIDStu struct {
// 	DIDMd5          string `json:"did_md5,omitempty"`
// 	RealRedisKey    string `json:"real_redis_key,omitempty"`
// 	RandomRedisKey  string `json:"random_redis_key,omitempty"`
// 	DynamicRedisKey string `json:"dynamic_redis_key,omitempty"`
// 	ScanRedisKey    string `json:"scan_redis_key,omitempty"`
// 	StepCode        int    `json:"step_code,omitempty"`
// 	IsFound         int    `json:"is_found,omitempty"`
// }

// /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

func getMagicRealReplaceDIDFromRedis(c context.Context, replaceRedisKey string, didMd5Key string) (string, error) {

	result, redisErr := utils.RedisSendCommand(c, "EXHGET", replaceRedisKey, didMd5Key)

	if redisErr != nil {
	} else {
		return result.(string), nil
	}

	return "", errors.New("redis error")
}

// 返回128分片
func getSliceValue(didMD5Key string) string {
	firstLetter := didMD5Key[:1]
	secondLetter := didMD5Key[1:2]
	// from hex to dec
	dec, err := strconv.ParseUint(secondLetter, 16, 32)
	if err != nil {
		return "00"
	}

	return firstLetter + utils.ConvertIntToString(int(dec/2))
}
