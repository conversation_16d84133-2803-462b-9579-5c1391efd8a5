package models

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/db"
	"strings"
)

// GetAdxInfoByRtbTagID ...
func GetAdxInfoByRtbTagID(c context.Context, channel string, tagID string, os string, styleID string, dealID string, price int) *[]RtbConfigByTagIDStu {
	// fmt.Println("kbg_debug GetAdxInfoByRtbTagID 0: ", channel, tagID, os, styleID, dealID, price)
	if len(tagID) == 0 {
		return nil
	}

	cacheKey := "go_rtb_config_" + channel + "_" + os

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
		return nil
	}
	// fmt.Println("cache value:", cacheValue)
	if len(cacheValue) == 0 {
		return nil
	}

	var rtbPosArray []RtbPosStu

	json.Unmarshal([]byte(cacheValue), &rtbPosArray)

	if len(rtbPosArray) == 0 {
		return nil
	}

	var respListArray []RtbConfigByTagIDStu

	for _, item := range rtbPosArray {
		if isSplitContainForTagID(item.SubTagIDs, tagID) &&
			(isSplitContain(item.SubImageStyleIDs, styleID) || isSplitContain(item.SubVideoStyleIDs, styleID)) &&
			isSplitContain(item.SubDealIDs, dealID) {

			var respItem RtbConfigByTagIDStu
			// fmt.Println(price)
			// fmt.Println(item.Price)
			if len(item.LocalAppID) == 0 || len(item.LocalPosID) == 0 {
				fmt.Println("bid no match app_id, pos_id continue")
				continue
			}

			//if price == 0 {
			//	go func() {
			//		defer func() {
			//			if err := recover(); err != nil {
			//				fmt.Println("rtb debug panic:", err)
			//			}
			//		}()
			//		debugRTBToHolo(c, tagID, item.LocalAppID, item.LocalPosID)
			//	}()
			//}

			respItem.TagID = item.TagID
			if price == 0 {
				respItem.Price = 1
			} else {
				respItem.Price = price
			}
			if item.IsCustomCPMBidFloor == 1 {
				respItem.Price = item.CustomCPMBidFloor
			}
			respItem.OriginPrice = price
			respItem.LocalAppID = item.LocalAppID
			respItem.LocalPosID = item.LocalPosID
			respItem.InputStyleID = styleID
			respItem.ImageStyleID = getStyleIDByStyleIDArray(item.SubImageStyleIDs, styleID)
			respItem.VideoStyleID = getStyleIDByStyleIDArray(item.SubVideoStyleIDs, styleID)
			respItem.AllImageStyleID = item.SubImageStyleIDs
			respItem.AllVideoStyleID = item.SubVideoStyleIDs
			respItem.DealID = dealID
			respItem.CrowdPackageIDs = item.CrowdPackageIDs
			respItem.PackageName = item.PackageName
			respItem.OriginTagID = tagID
			respItem.IsMediaExchangeModelMake = item.IsMediaExchangeModelMake
			respItem.IsTagIDAuditToServer = item.IsTagIDAuditToServer
			respItem.IsTagIDAuditNeedStyleID = item.IsTagIDAuditNeedStyleID
			respItem.IsPosAudit = item.IsPosAudit
			respItem.AuditPlatform = item.AuditPlatform
			respItem.AuditImageStyleID = item.AuditImageStyleID
			respItem.AuditVideoStyleID = item.AuditVideoStyleID
			respItem.AuditAdType = item.AuditAdType
			respItem.IsFillMaterial = item.IsFillMaterial
			respItem.AuditJPGSuffixKey = item.AuditJPGSuffixKey

			respListArray = append(respListArray, respItem)

			// 包名设置为空时, 存rtb请求价格到holo
			if len(respItem.PackageName) == 0 {
				BigDataRtbReqPrice(c, item.TagID, price)
			}
		}
	}
	if len(respListArray) == 0 {
		fmt.Println("no match tagid ->:", channel, tagID, os, styleID, dealID, price)
		return nil
	}

	return &respListArray
}

// GetAdxInfoByRtbTagIDAndTagIDType ...
func GetAdxInfoByRtbTagIDAndTagIDType(c context.Context, channel string, tagID string, tagIDType string, os string, styleIDs []string, dealID string, price int) *[]RtbConfigByTagIDStu {
	// fmt.Println("kbg_debug GetAdxInfoByRtbTagID 0: ", channel, tagID, os, styleID, dealID, price)
	if len(tagID) == 0 {
		return nil
	}

	cacheKey := "go_rtb_config_" + channel + "_" + os

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
		return nil
	}
	// fmt.Println("cache value:", cacheValue)
	if len(cacheValue) == 0 {
		return nil
	}

	var rtbPosArray []RtbPosStu

	json.Unmarshal([]byte(cacheValue), &rtbPosArray)

	if len(rtbPosArray) == 0 {
		return nil
	}

	var respListArray []RtbConfigByTagIDStu

	for _, item := range rtbPosArray {
		if isSplitContainForTagID(item.SubTagIDs, tagID) &&
			isSplitContain(item.SubTagIDsTypes, tagIDType) &&
			(isSplitContainByArray(item.SubImageStyleIDs, styleIDs) || isSplitContainByArray(item.SubVideoStyleIDs, styleIDs)) &&
			isSplitContain(item.SubDealIDs, dealID) {

			var respItem RtbConfigByTagIDStu
			// fmt.Println(price)
			// fmt.Println(item.Price)
			if len(item.LocalAppID) == 0 || len(item.LocalPosID) == 0 {
				fmt.Println("bid no match app_id, pos_id continue")
				continue
			}

			//if price == 0 {
			//	go func() {
			//		defer func() {
			//			if err := recover(); err != nil {
			//				fmt.Println("rtb debug panic:", err)
			//			}
			//		}()
			//		debugRTBToHolo(c, tagID, item.LocalAppID, item.LocalPosID)
			//	}()
			//}

			respItem.TagID = item.TagID
			if price == 0 {
				respItem.Price = 1
			} else {
				respItem.Price = price
			}
			if item.IsCustomCPMBidFloor == 1 {
				respItem.Price = item.CustomCPMBidFloor
			}
			respItem.OriginPrice = price
			respItem.LocalAppID = item.LocalAppID
			respItem.LocalPosID = item.LocalPosID
			respItem.ImageStyleID = getStyleIDByStyleIDArrayFromMultiStyleIDArray(item.SubImageStyleIDs, styleIDs)
			respItem.VideoStyleID = getStyleIDByStyleIDArrayFromMultiStyleIDArray(item.SubVideoStyleIDs, styleIDs)
			respItem.ImageVirtualCrid = item.ImageVirtualCrid
			respItem.VideoVirtualCrid = item.VideoVirtualCrid
			respItem.AllImageStyleID = item.SubImageStyleIDs
			respItem.AllVideoStyleID = item.SubVideoStyleIDs
			respItem.DealID = dealID
			respItem.CrowdPackageIDs = item.CrowdPackageIDs
			respItem.PackageName = item.PackageName
			respItem.OriginTagID = tagID
			respItem.IsMediaExchangeModelMake = item.IsMediaExchangeModelMake
			respItem.IsTagIDAuditToServer = item.IsTagIDAuditToServer
			respItem.IsTagIDAuditNeedStyleID = item.IsTagIDAuditNeedStyleID
			respItem.IsPosAudit = item.IsPosAudit
			respItem.AuditPlatform = item.AuditPlatform
			respItem.AuditImageStyleID = item.AuditImageStyleID
			respItem.AuditVideoStyleID = item.AuditVideoStyleID
			respItem.AuditAdType = item.AuditAdType
			respItem.IsFillMaterial = item.IsFillMaterial
			respItem.AuditJPGSuffixKey = item.AuditJPGSuffixKey

			respListArray = append(respListArray, respItem)

			// 包名设置为空时, 存rtb请求价格到holo
			if len(respItem.PackageName) == 0 {
				BigDataRtbReqPrice(c, item.TagID, price)
			}
		}
	}
	if len(respListArray) == 0 {
		fmt.Println("no match tagid ->:", channel, tagID, os, styleIDs, dealID, price)
		return nil
	}

	return &respListArray
}

// isSplitContainForTagID
// tagid(外部传string, 内部是[]string):
// 判定:
// step 1. 如果内部配置数组空 || 外部传入空, return false
// step 2. 如果外部在内部配置数组内, return true; 不在 return false
func isSplitContainForTagID(configIDs string, inputID string) bool {
	if len(configIDs) == 0 || len(inputID) == 0 {
		return false
	}
	tmpArrays := strings.Split(configIDs, ",")
	for _, item := range tmpArrays {
		if item == inputID {
			return true
		}
	}
	return false
}

// isSplitContain ...
// tagidtype(外部传string, 内部是[]string):
// styleid:
// 情况1. 外部传string, 内部是[]string
// 情况2. 外部传[]string, 内部是[]string
// dealid(外部传string, 内部是[]string):
// 判定:
// step 1. 如果内部配置数组空 && 外部传入空, return true
// step 2. 如果内部配置数组空 || 外部传入空, return false
// step 3. 如果外部在内部配置数组内, return true; 不在 return false
func isSplitContain(configIDs string, inputID string) bool {
	if len(configIDs) == 0 && len(inputID) == 0 {
		return true
	}
	if len(configIDs) == 0 || len(inputID) == 0 {
		return false
	}
	tmpArrays := strings.Split(configIDs, ",")
	for _, item := range tmpArrays {
		if item == inputID {
			return true
		}
	}
	return false
}

// getStyleIDByStyleIDArray ...
func getStyleIDByStyleIDArray(str string, str1 string) string {
	if len(str) == 0 || len(str1) == 0 {
		return ""
	}
	tmpArrays := strings.Split(str, ",")
	for _, item := range tmpArrays {
		if item == str1 {
			return str1
		}
	}
	return ""
}

// isSplitContainByArray ...
func isSplitContainByArray(configIDs string, inputArray []string) bool {
	if len(configIDs) == 0 && len(inputArray) == 0 {
		return true
	}
	if len(configIDs) == 0 || len(inputArray) == 0 {
		return false
	}
	tmpArrays := strings.Split(configIDs, ",")
	for _, item := range tmpArrays {
		for _, inputItem := range inputArray {
			if item == inputItem {
				return true
			}
		}
	}
	return false
}

// getStyleIDByStyleIDArrayFromMultiStyleIDArray ...
func getStyleIDByStyleIDArrayFromMultiStyleIDArray(str string, inputArray []string) string {
	if len(str) == 0 {
		return ""
	}
	if len(inputArray) == 0 {
		return ""
	}
	tmpArrays := strings.Split(str, ",")
	for _, item := range tmpArrays {
		for _, inputItem := range inputArray {
			if item == inputItem {
				return inputItem
			}
		}
	}
	return ""
}

// GetAdxInfoByRtbTagIDAndStyles ...
func GetAdxInfoByRtbTagIDAndStyles(c context.Context, channel string, tagID string, os string, styleIDs []string, dealID string, price int) *[]RtbConfigByTagIDStu {
	// fmt.Println("kbg_debug GetAdxInfoByRtbTagID:", channel, tagID, os, styleIDs, dealID, price)
	if len(tagID) == 0 {
		fmt.Println("cache value err0")
		return nil
	}

	cacheKey := "go_rtb_config_" + channel + "_" + os

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
		return nil
	}
	if len(cacheValue) == 0 {
		fmt.Println("cache value err")
		return nil
	}

	var rtbPosArray []RtbPosStu

	_ = json.Unmarshal(cacheValue, &rtbPosArray)

	if len(rtbPosArray) == 0 {
		fmt.Println("cache value err1")
		return nil
	}

	var respListArray []RtbConfigByTagIDStu

	for _, item := range rtbPosArray {
		if isSplitContainForTagID(item.SubTagIDs, tagID) &&
			(isSplitContainByArray(item.SubImageStyleIDs, styleIDs) || isSplitContainByArray(item.SubVideoStyleIDs, styleIDs)) &&
			isSplitContain(item.SubDealIDs, dealID) {

			var respItem RtbConfigByTagIDStu
			// fmt.Println(price)
			// fmt.Println(item.Price)
			if len(item.LocalAppID) == 0 || len(item.LocalPosID) == 0 {
				fmt.Println("bid no match app_id, pos_id continue")
				continue
			}

			//if price == 0 {
			//	go func() {
			//		defer func() {
			//			if err := recover(); err != nil {
			//				fmt.Println("rtb debug panic:", err)
			//			}
			//		}()
			//		debugRTBToHolo(c, tagID, item.LocalAppID, item.LocalPosID)
			//	}()
			//}

			respItem.TagID = item.TagID
			if price == 0 {
				respItem.Price = 1
			} else {
				respItem.Price = price
			}
			if item.IsCustomCPMBidFloor == 1 {
				respItem.Price = item.CustomCPMBidFloor
			}
			respItem.OriginPrice = price
			respItem.LocalAppID = item.LocalAppID
			respItem.LocalPosID = item.LocalPosID
			respItem.ImageStyleID = getStyleIDByStyleIDArrayFromMultiStyleIDArray(item.SubImageStyleIDs, styleIDs)
			respItem.VideoStyleID = getStyleIDByStyleIDArrayFromMultiStyleIDArray(item.SubVideoStyleIDs, styleIDs)
			respItem.ImageVirtualCrid = item.ImageVirtualCrid
			respItem.VideoVirtualCrid = item.VideoVirtualCrid
			respItem.AllImageStyleID = item.SubImageStyleIDs
			respItem.AllVideoStyleID = item.SubVideoStyleIDs
			respItem.DealID = dealID
			respItem.CrowdPackageIDs = item.CrowdPackageIDs
			respItem.PackageName = item.PackageName
			respItem.OriginTagID = tagID
			respItem.IsMediaExchangeModelMake = item.IsMediaExchangeModelMake
			respItem.IsTagIDAuditToServer = item.IsTagIDAuditToServer
			respItem.IsTagIDAuditNeedStyleID = item.IsTagIDAuditNeedStyleID
			respItem.IsPosAudit = item.IsPosAudit
			respItem.AuditPlatform = item.AuditPlatform
			respItem.AuditImageStyleID = item.AuditImageStyleID
			respItem.AuditVideoStyleID = item.AuditVideoStyleID
			respItem.AuditAdType = item.AuditAdType
			respItem.IsFillMaterial = item.IsFillMaterial
			respItem.AuditJPGSuffixKey = item.AuditJPGSuffixKey
			respItem.AdType = item.AdType

			respListArray = append(respListArray, respItem)

			// 包名设置为空时, 存rtb请求价格到holo
			if len(respItem.PackageName) == 0 {
				BigDataRtbReqPrice(c, item.TagID, price)
			}
		}
	}
	if len(respListArray) == 0 {
		fmt.Println("no match multi tagid ->:", channel, tagID, os, styleIDs, dealID, price)
		return nil
	}

	return &respListArray
}

// RtbConfigByTagIDStu ...
type RtbConfigByTagIDStu struct {
	LocalAppID               string
	LocalPosID               string
	ExposeNum                int
	RealExposeNum            int
	Price                    int // rtb媒体请求的底价, 如果自定义竞价底价开关开, 替换为自定义竞价底价
	TagID                    string
	InputStyleID             string
	DealID                   string
	CrowdPackageIDs          string
	PackageName              string
	MangoInteract            int
	MangoSpaceSize           int
	OriginTagID              string // rtb请求的tagid
	ImageStyleID             string // rtb配置的image style id
	VideoStyleID             string // rtb配置的video style id
	ImageVirtualCrid         string
	VideoVirtualCrid         string
	AllImageStyleID          string // rtb配置的all image style id
	AllVideoStyleID          string // rtb配置的all video style id
	IsMediaExchangeModelMake int
	IsTagIDAuditToServer     int
	IsTagIDAuditNeedStyleID  int
	IsPosAudit               int
	AuditPlatform            string
	AuditImageStyleID        string
	AuditVideoStyleID        string
	AuditAdType              string
	IsFillMaterial           int
	AuditJPGSuffixKey        string
	ExtraTag                 string
	// tagid_type
	TagIDType string
	AdType    string
	// rtb请求的price
	OriginPrice int
}

// RtbMediaStu ...
type RtbMediaStu struct {
	MediaID int    `json:"media_id"`
	Channel string `json:"channel"`
	Os      string `json:"os"`
}

// RtbPosStu ...
type RtbPosStu struct {
	MediaID                  int    `json:"media_id"`
	Channel                  string `json:"channel"`
	Os                       string `json:"os"`
	TagID                    string `json:"tagid"`
	IsCustomCPMBidFloor      int    `json:"is_custom_cpm_bid_floor"`
	CustomCPMBidFloor        int    `json:"custom_cpm_bid_floor"`
	LocalAppID               string `json:"app_id"`
	LocalPosID               string `json:"pos_id"`
	SubTagIDs                string `json:"sub_tagids"`
	SubTagIDsTypes           string `json:"sub_tagids_types,omitempty"`
	SubImageStyleIDs         string `json:"sub_image_styleids,omitempty"`
	SubVideoStyleIDs         string `json:"sub_video_styleids,omitempty"`
	CrowdPackageIDs          string `json:"CrowdPackageIDs,omitempty"`
	ImageVirtualCrid         string `json:"image_virtual_crid,omitempty"`
	VideoVirtualCrid         string `json:"video_virtual_crid,omitempty"`
	SubDealIDs               string `json:"sub_dealids,omitempty"`
	PackageName              string `json:"package_name,omitempty"`
	IsMediaExchangeModelMake int    `json:"is_media_exchange_model_make,omitempty"`
	IsTagIDAuditToServer     int    `json:"is_tagid_audit_to_server,omitempty"`
	IsTagIDAuditNeedStyleID  int    `json:"is_tagid_audit_need_styleid,omitempty"`
	IsPosAudit               int    `json:"is_pos_audit,omitempty"`
	AuditPlatform            string `json:"audit_platform,omitempty"`
	AuditImageStyleID        string `json:"audit_image_styleid,omitempty"`
	AuditVideoStyleID        string `json:"audit_video_styleid,omitempty"`
	AuditAdType              string `json:"audit_ad_type,omitempty"`
	IsFillMaterial           int    `json:"is_fill_material,omitempty"`
	AuditJPGSuffixKey        string `json:"audit_jpg_suffix_key,omitempty"`
	AdType                   string `json:"ad_type,omitempty"`
}
