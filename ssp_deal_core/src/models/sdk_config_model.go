package models

// SDKConfigStu ...
type SDKConfigStu struct {
	ConfigID                string `json:"ConfigID,omitempty"`
	InteractionTypeListJson string `json:"InteractionTypeListJson,omitempty"`
	YaoDirection            int    `json:"YaoDirection"`
	SahuaConfigListJson     string `json:"SahuaConfigListJson,omitempty"`
	SahuaImageTriggerTime   string `json:"SahuaImageTriggerTime,omitempty"`
	SahuaVideoTriggerTime   string `json:"SahuaVideoTriggerTime,omitempty"`
	SahuaImageDurationTime  string `json:"SahuaImageDurationTime,omitempty"`
	SahuaVideoDurationTime  string `json:"SahuaVideoDurationTime,omitempty"`
	IsDownloadDialog        int    `json:"IsDownloadDialog,omitempty"`
	IsAutoPlayMobileNetwork int    `json:"IsAutoPlayMobileNetwork,omitempty"`

	// 是否点击区域下载合规弹窗
	IsClickViewDownloadCompliance int `json:"IsClickViewDownloadCompliance,omitempty"`
	// 是否非点击区域下载合规弹窗
	IsAdViewDownloadCompliance int `json:"IsAdViewDownloadCompliance,omitempty"`
	// 是否摇一摇后台关闭传感器
	IsYaoBgDisableSensor int `json:"IsYaoBgDisableSensor,omitempty"`
	// 是否激励视频退出弹窗
	IsRewardVideoExitConfirm int `json:"IsRewardVideoExitConfirm,omitempty"`
	// 样式配置 interaction_style, 0 无 1 撒花 2 宝箱
	InteractionStyle int `json:"InteractionStyle,omitempty"`
	// 宝箱配置
	TreasurechestConfigListJson string `json:"TreasurechestConfigListJson,omitempty"`
	// magic开关 is_magic 0 关 1 开
	IsMagic int `json:"IsMagic,omitempty"`
	// magic范围 magic_type 1 全部, 2 下载, 0 非下载(默认)
	MagicType int `json:"MagicType,omitempty"`
	// magic配置 magic config list json
	MagicConfigListJson string `json:"MagicConfigListJson,omitempty"`
	// 激励视频样式 reward_video_type 0 旧版 1 新版
	RewardVideoType int `json:"RewardVideoType,omitempty"`
	// 激励视频点击手势引导 0 关闭 1 开启
	RewardVideoClkGuide int `json:"RewardVideoClkGuide,omitempty"`
	// magic关闭键替换点击坐标概率
	MagicCloseReplaceClickWeight int `json:"MagicCloseReplaceClickWeight,omitempty"`
	// 滑动触发方向, 0 向上(默认), 1 所有
	SlideDirection int `json:"SlideDirection,omitempty"`
	// magic区域, 0 关闭(默认), 1 全屏
	MagicRegion int `json:"MagicRegion,omitempty"`
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 以下为new
	////////////////////////////////////////////////////////////////////////////////////////////////////
	NewInteractionTypeListJson string `json:"NewInteractionTypeListJson,omitempty"`
	NewYaoDirection            int    `json:"NewYaoDirection"`
	NewSahuaConfigListJson     string `json:"NewSahuaConfigListJson,omitempty"`
	NewSahuaImageTriggerTime   string `json:"NewSahuaImageTriggerTime,omitempty"`
	NewSahuaVideoTriggerTime   string `json:"NewSahuaVideoTriggerTime,omitempty"`
	NewSahuaImageDurationTime  string `json:"NewSahuaImageDurationTime,omitempty"`
	NewSahuaVideoDurationTime  string `json:"NewSahuaVideoDurationTime,omitempty"`
	NewIsDownloadDialog        int    `json:"NewIsDownloadDialog,omitempty"`
	NewIsAutoPlayMobileNetwork int    `json:"NewIsAutoPlayMobileNetwork,omitempty"`

	// 是否点击区域下载合规弹窗
	NewIsClickViewDownloadCompliance int `json:"NewIsClickViewDownloadCompliance,omitempty"`
	// 是否非点击区域下载合规弹窗
	NewIsAdViewDownloadCompliance int `json:"NewIsAdViewDownloadCompliance,omitempty"`
	// 是否摇一摇后台关闭传感器
	NewIsYaoBgDisableSensor int `json:"NewIsYaoBgDisableSensor,omitempty"`
	// 是否激励视频退出弹窗
	NewIsRewardVideoExitConfirm int `json:"NewIsRewardVideoExitConfirm,omitempty"`
	// 样式配置 interaction_style, 0 无 1 撒花 2 宝箱
	NewInteractionStyle int `json:"NewInteractionStyle,omitempty"`
	// 宝箱配置
	NewTreasurechestConfigListJson string `json:"NewTreasurechestConfigListJson,omitempty"`
	// magic开关 is_magic 0 关 1 开
	NewIsMagic int `json:"NewIsMagic,omitempty"`
	// magic范围 magic_type 1 全部, 2 下载, 0 非下载(默认)
	NewMagicType int `json:"NewMagicType,omitempty"`
	// magic配置 magic config list json
	NewMagicConfigListJson string `json:"NewMagicConfigListJson,omitempty"`
	// 激励视频样式 reward_video_type 0 旧版 1 新版
	NewRewardVideoType int `json:"NewRewardVideoType,omitempty"`
	// 激励视频点击手势引导 0 关闭 1 开启
	NewRewardVideoClkGuide int `json:"NewRewardVideoClkGuide,omitempty"`
	// magic关闭键替换点击坐标概率
	NewMagicCloseReplaceClickWeight int `json:"NewMagicCloseReplaceClickWeight,omitempty"`
	// 滑动触发方向, 0 向上(默认), 1 所有
	NewSlideDirection int `json:"NewSlideDirection,omitempty"`
	// magic区域, 0 关闭(默认), 1 全屏
	NewMagicRegion int `json:"NewMagicRegion,omitempty"`
}

// SDKInteractionConfigStu ...
type SDKInteractionConfigStu struct {
	InteractionType string   `json:"interaction_type,omitempty"`
	From            string   `json:"from,omitempty"`
	To              string   `json:"to,omitempty"`
	YaoSpeed        string   `json:"yao_speed"`
	YaoTriggerTime  string   `json:"yao_trigger_time"`
	Weight          int      `json:"weight"`
	WhiteList       []string `json:"white_list,omitempty"`
	BlackList       []string `json:"black_list,omitempty"`
}

// SDKNewInteractionConfigStu ...
type SDKNewInteractionConfigStu struct {
	InteractionType string   `json:"interaction_type,omitempty"`
	From            string   `json:"from,omitempty"`
	To              string   `json:"to,omitempty"`
	YaoSpeed        string   `json:"shake_speed"`
	YaoTriggerTime  string   `json:"shake_trigger_time"`
	Weight          int      `json:"weight"`
	WhiteList       []string `json:"white_list,omitempty"`
	BlackList       []string `json:"black_list,omitempty"`
}

// SDKSaHuaConfigStu ...
type SDKSaHuaConfigStu struct {
	From      string   `json:"from,omitempty"`
	To        string   `json:"to,omitempty"`
	Weight    int      `json:"weight"`
	WhiteList []string `json:"white_list,omitempty"`
	BlackList []string `json:"black_list,omitempty"`
}

// SDKTreasureConfigStu ...
type SDKTreasureConfigStu struct {
	From                string   `json:"from,omitempty"`
	To                  string   `json:"to,omitempty"`
	Weight              int      `json:"weight"`
	WhiteList           []string `json:"white_list,omitempty"`
	BlackList           []string `json:"black_list,omitempty"`
	TriggerTime         string   `json:"trigger_time"`
	IsClkAllscreen      int      `json:"is_clk_allscreen"`
	IsShake             int      `json:"is_shake"`
	TreasureMagicWeight int      `json:"treasure_magic_weight"`
}

// SDKMagicConfigStu ...
type SDKMagicConfigStu struct {
	From      string   `json:"from,omitempty"`
	To        string   `json:"to,omitempty"`
	Weight    int      `json:"magic_weight"`
	WhiteList []string `json:"white_list,omitempty"`
	BlackList []string `json:"black_list,omitempty"`
}
