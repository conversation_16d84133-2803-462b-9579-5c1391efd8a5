package models

// SensorBatteryDataStu ...
type SensorBatteryDataStu struct {
	Data         SensorBatteryDetailStu `json:"data,omitempty"`
	Os           string                 `json:"os,omitempty"`
	OsVersion    string                 `json:"os_version,omitempty"`
	Imei         string                 `json:"imei,omitempty"`
	AndroidID    string                 `json:"android_id,omitempty"`
	Oaid         string                 `json:"oaid,omitempty"`
	Model        string                 `json:"model,omitempty"`
	Manufacturer string                 `json:"manufacturer,omitempty"`
	LocalAppID   string                 `json:"appid,omitempty"`
	LocalPosID   string                 `json:"posid,omitempty"`
	SDKVersion   string                 `json:"sdk_version,omitempty"`
}

// SensorBatteryDetailStu ...
type SensorBatteryDetailStu struct {
	BatteryPercentage  int             `json:"battery_percentage,omitempty"`
	BatteryTemperature int             `json:"battery_temperature,omitempty"`
	SensorList         []SensorItemStu `json:"list,omitempty"`
}

// SensorItemStu ...
type SensorItemStu struct {
	Name string `json:"name,omitempty"`
	X    string `json:"x,omitempty"`
	Y    string `json:"y,omitempty"`
	Z    string `json:"z,omitempty"`
}
