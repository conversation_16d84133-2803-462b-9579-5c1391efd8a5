package models

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/db"
)

// GetAPIConfigFromCategoryPrice ...
func GetAPIConfigFromCategoryPrice(c context.Context, localPosID string, localAppID string) *[]CategoryStu {
	// fmt.Println("GetAPIConfigFromCategoryDev")
	// fmt.Println(localPosID)
	// fmt.Println(localAppID)

	cacheKey := "go_category_price_api_" + localAppID + "_" + localPosID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var respListArray []CategoryStu

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &respListArray)

		return &respListArray
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// GetSdkConfigFromCategoryPrice ...
func GetSdkConfigFromCategoryPrice(c context.Context, localPosID string, localAppID string) *[]CategoryStu {
	// fmt.Println("GetSdkConfigFromCategoryDev")
	// fmt.Println(localPosID)
	// fmt.Println(localAppID)

	cacheKey := "go_category_price_sdk_" + localAppID + "_" + localPosID
	// fmt.Println(redisKey)
	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var respListArray []CategoryStu

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &respListArray)

		return &respListArray
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// CategoryStu ...
type CategoryStu struct {
	LocalPosID string
	LocalAppID string
	// LocalPosWidth       string
	// LocalPosHeight      string
	// LocalOs             string
	// LocalAppName        string
	// LocalAppBundleID    string
	// LocalUnsupportGdtH5 int
	// LocalPosIsActive    int
	PlatformPosID   string
	PlatformAppID   string
	PlatformMediaID string
	// PlatformAppBundle   string
	// PlatformOs          string
	// PlatformPosWidth    int
	// PlatformPosHeight   int
	PlatformPosType         int
	PlatformPosMaterialType int
	// PlatformAppName     string
	// PlatformAppVersion  string
	// PlatformAppKey    string
	// PlatformAppSecret string

	// 1 api, 2 保留, 3 sdk
	Label    int
	Priority int
	// SubPriority  int
	// Status       int
	// SubStatus    int
	// Sub1Status   int
	// APIWeight    int
	APISubWeight   int
	MaxReqNum      int
	IsMaxDIDReqNum int `json:"IsMaxDIDReqNum,omitempty"`
	MaxDIDReqNum   int `json:"MaxDIDReqNum,omitempty"`
	MaxExpNum      int
	MaxClkNum      int
	FloorPrice     int    `json:"FloorPrice,omitempty"`
	FinalPrice     int    `json:"FinalPrice,omitempty"`
	WhiteList      string `json:"WhiteList,omitempty"`
	BlackList      string `json:"BlackList,omitempty"`
	WhiteVersion   string `json:"WhiteVersion,omitempty"`
	BlackVersion   string `json:"BlackVersion,omitempty"`

	// <el-checkbox label="all">全部</el-checkbox>
	// <el-checkbox label="oppo">OPPO</el-checkbox>
	// <el-checkbox label="vivo">VIVO</el-checkbox>
	// <el-checkbox label="huawei">HUAWEI</el-checkbox>
	// <el-checkbox label="xiaomi">小米</el-checkbox>
	// <el-checkbox label="lenovo">联想</el-checkbox>
	// <el-checkbox label="meizu">魅族</el-checkbox>
	// <el-checkbox label="honor">荣耀</el-checkbox>
	// <el-checkbox label="others">其他</el-checkbox>
	ManufacturerList string `json:"ManufacturerList,omitempty"`

	// 请求频率限制json
	ReqLimitFrequencyJson string `json:"ReqLimitFrequency,omitempty"`

	// bidding price
	ProfitRate string // string类型解决null默认0
	DealID     string
	IsForceWin int

	// 是否人群包
	IsCrowdPackage int `json:"IsCrowdPackage,omitempty"`
	// 人群包类型, 0 正常, 1 反向
	CrowdPackageType int `json:"CrowdPackageType,omitempty"`
	// 人群包id
	CrowdPackageID string `json:"CrowdPackageID,omitempty"`
	// 快手出价类型, cpm, cpc, cpc_dynamic(临时)
	KsPriceType string `json:"KsPriceType,omitempty"`
	// 是否开启
	IsActive int `json:"IsActive,omitempty"`
}

// ReqLimitFrequencyStu ...
type ReqLimitFrequencyStu struct {
	BeginHour   string `json:"begin,omitempty"`
	BeginMinute string `json:"beginminute,omitempty"`
	EndHour     string `json:"end,omitempty"`
	EndMinute   string `json:"endminute,omitempty"`
	Rate        int    `json:"rate,omitempty"`
}

// ReqLimitFrequency1Stu ...
type ReqLimitFrequency1Stu struct {
	BeginHour   int `json:"begin,omitempty"`
	BeginMinute int `json:"beginminute,omitempty"`
	EndHour     int `json:"end,omitempty"`
	EndMinute   int `json:"endminute,omitempty"`
	Rate        int `json:"rate,omitempty"`
}

// SDKLimitFrequencyStu ...
type SDKLimitFrequencyStu struct {
	BeginHour int    `json:"begin,omitempty"`
	EndHour   int    `json:"end,omitempty"`
	Interval  string `json:"interval,omitempty"`
}
