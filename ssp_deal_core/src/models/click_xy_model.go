package models

import (
	"context"
	"encoding/json"
	"errors"
	"math/rand"
	"mh_proxy/utils"
	"strconv"
	"strings"
)

// ClickXYConfigInfoStu ...
type ClickXYConfigInfoStu struct {
	LocalAppID     string
	LocalPosID     string
	Md5Key         string
	LocalAppType   string // 1. SDK; 2. API; 3. SDK+API; 4. RTB; 5. ADX; 6. 商店
	ClickXYTTL     int
	ClickXYTTLUnit string
}

// ClickXYInfoStu ...
type ClickXYInfoStu struct {
	DownX        int     `json:"DownX,omitempty"`
	DownY        int     `json:"DownY,omitempty"`
	UpX          int     `json:"UpX,omitempty"`
	UpY          int     `json:"UpY,omitempty"`
	DPI          float32 `json:"DPI,omitempty"`
	Os           string  `json:"os,omitempty"`
	IsLogicPixel int     `json:"IsLogicPixel,omitempty"` // 是否支持逻辑像素
}

// GetNewReplaceClickXYFromRedis ...
// 物理像素：下发点击坐标时，（7，7）≤4个点击坐标≤（1433，3193），上下随机浮动±6像素
// 逻辑像素：下发点击坐标时，（3，3）≤当4个坐标点≤（357，797），上下随机浮动±2个像素
// 独立像素：下发点击坐标时，（3，3）≤当4个坐标点≤（387，837），上下随机浮动±2个像素
func GetNewReplaceClickXYFromRedis(c context.Context, localPos *LocalPosStu, platformPos *PlatformPosStu, bigdataUID string) (int, int, int, int, string, error) {

	// fmt.Println("kbg_debug_test:", platformPos.PlatformAppID)
	if len(platformPos.PlatformPosClickPointLibKey) == 0 {
		return 0, 0, 0, 0, "", errors.New("点击坐标库key为空")
	}

	xyType := ""
	randXYField := 0
	tmpRedisKey := ""
	if len(platformPos.PlatformPosClickPointLibKey) > 0 {
		// EXHGET adx_ssp_magic_clickpoint_lib_0ec0612254ef88aa_12 33
		// 128分片
		// fmt.Println("kbg_debug_clickpoint 1:", platformPos.PlatformAppID, platformPos.PlatformPosID, platformPos.PlatformPosClickPointLibKey)

		tmpXYConfigKey := platformPos.PlatformPosClickPointLibKey
		xyConfigArray := strings.Split(tmpXYConfigKey, ",")
		xyType = xyConfigArray[0]
		xyKey := xyConfigArray[1]
		xyMaxNum := xyConfigArray[2]

		randXYSlice := rand.Intn(128)

		tmp1 := utils.ConvertStringToInt(xyMaxNum) / 128
		tmp2 := utils.ConvertStringToInt(xyMaxNum) % 128

		if randXYSlice < tmp2 {
			tmp1 = tmp1 + 1
		}

		randXYField = rand.Intn(tmp1)

		tmpRedisKey = "adx_ssp_magic_clickpoint_lib_" + xyKey + "_" + strconv.Itoa(randXYSlice)

		// fmt.Println("kbg_debug_clickpoint 2:", tmpRedisKey, randXYField)
	} else {
		return 0, 0, 0, 0, "", errors.New("点击坐标key为空")
	}

	result, redisErr := utils.RedisSendCommand(c, "EXHGET", tmpRedisKey, randXYField)

	tmpDownX := 0
	tmpDownY := 0
	tmpUpX := 0
	tmpUpY := 0
	if redisErr != nil {
	} else {
		var clickXYRedisData ClickXYNewInfoStu
		json.Unmarshal([]byte(result.(string)), &clickXYRedisData)

		tmpDownX = clickXYRedisData.DownX
		tmpDownY = clickXYRedisData.DownY
		tmpUpX = clickXYRedisData.UpX
		tmpUpY = clickXYRedisData.UpY
		// fmt.Println("kbg_debug_test 0:", tmpDownX, tmpDownY, tmpUpX, tmpUpY)
		// 是否点击坐标offset
		if platformPos.PlatformPosIsReplaceXYLibOffset == 1 {
			if xyType == "0" {
				if tmpDownX >= 7 && tmpDownX <= 1433 &&
					tmpDownY >= 7 && tmpDownY <= 3193 &&
					tmpUpX >= 7 && tmpUpX <= 1433 &&
					tmpUpY >= 7 && tmpUpY <= 3193 {
					tmpDownX = tmpDownX + rand.Intn(13) - 6
					tmpDownY = tmpDownY + rand.Intn(13) - 6
					tmpUpX = tmpUpX + rand.Intn(13) - 6
					tmpUpY = tmpUpY + rand.Intn(13) - 6
				}
			} else if xyType == "1" {
				if tmpDownX >= 3 && tmpDownX <= 357 &&
					tmpDownY >= 3 && tmpDownY <= 797 &&
					tmpUpX >= 3 && tmpUpX <= 357 &&
					tmpUpY >= 3 && tmpUpY <= 797 {
					tmpDownX = tmpDownX + rand.Intn(5) - 2
					tmpDownY = tmpDownY + rand.Intn(5) - 2
					tmpUpX = tmpUpX + rand.Intn(5) - 2
					tmpUpY = tmpUpY + rand.Intn(5) - 2
				}
			} else if xyType == "2" {
				if tmpDownX >= 3 && tmpDownX <= 387 &&
					tmpDownY >= 3 && tmpDownY <= 837 &&
					tmpUpX >= 3 && tmpUpX <= 387 &&
					tmpUpY >= 3 && tmpUpY <= 837 {
					tmpDownX = tmpDownX + rand.Intn(5) - 2
					tmpDownY = tmpDownY + rand.Intn(5) - 2
					tmpUpX = tmpUpX + rand.Intn(5) - 2
					tmpUpY = tmpUpY + rand.Intn(5) - 2
				}
			}
		}

		// 如果上游偏移开, 并且在百分比范围内
		// 1. up = down
		// 2. up走一遍规则
		if platformPos.PlatformAppIsClickXYFix == 1 && rand.Intn(100) < platformPos.PlatformAppClickXYFixRatio {
			tmpUpX = tmpDownX
			tmpUpY = tmpDownY

			if xyType == "0" {
				if tmpUpX >= 7 && tmpUpX <= 1433 &&
					tmpUpY >= 7 && tmpUpY <= 3193 {
					tmpUpX = tmpUpX + rand.Intn(13) - 6
					tmpUpY = tmpUpY + rand.Intn(13) - 6
				}
			} else if xyType == "1" {
				if tmpUpX >= 3 && tmpUpX <= 357 &&
					tmpUpY >= 3 && tmpUpY <= 797 {
					tmpUpX = tmpUpX + rand.Intn(5) - 2
					tmpUpY = tmpUpY + rand.Intn(5) - 2
				}
			} else if xyType == "2" {
				if tmpUpX >= 3 && tmpUpX <= 387 &&
					tmpUpY >= 3 && tmpUpY <= 837 {
					tmpUpX = tmpUpX + rand.Intn(5) - 2
					tmpUpY = tmpUpY + rand.Intn(5) - 2
				}
			}
		}
		// fmt.Println("kbg_debug_test 1:", tmpDownX, tmpDownY, tmpUpX, tmpUpY)
	}

	return tmpDownX, tmpDownY, tmpUpX, tmpUpY, xyType, redisErr
}

// ClickXYNewInfoStu ...
type ClickXYNewInfoStu struct {
	DownX int `json:"down_x,omitempty"`
	DownY int `json:"down_y,omitempty"`
	UpX   int `json:"up_x,omitempty"`
	UpY   int `json:"up_y,omitempty"`
}
