package models

type SdkAnticheatV4Model struct {
	AppID        string                      `json:"appid"`
	PosID        string                      `json:"posid"`
	PAppID       string                      `json:"p_app_id"`
	PPosID       string                      `json:"p_pos_id"`
	SDKVersion   string                      `json:"sdk_version"`
	Channel      string                      `json:"channel"`
	Model        string                      `json:"model"`
	Manufacturer string                      `json:"manufacturer"`
	DID          string                      `json:"did"`
	Event        int                         `json:"event"`
	ExposeTimes  int                         `json:"expose_times"`
	ClickTimes   int                         `json:"click_times"`
	Device       *SdkAnticheatV4DeviceModel  `json:"device"`
	Network      *SdkAnticheatV4NetworkModel `json:"network"`
}

type SdkAnticheatV4NetworkModel struct {
	ConnectType int `json:"connect_type"`
	Carrier     int `json:"carrier"`
}

type SdkAnticheatV4DeviceModel struct {
	Os              string `json:"os"`
	OsVersion       string `json:"os_version"`
	Dpi             string `json:"dpi"`
	AppstoreVersion string `json:"appstore_version"`
	HmsVersion      string `json:"hms_version"`
	BootMark        string `json:"boot_mark"`
	UpdateMark      string `json:"update_mark"`
	SdFreeSpace     string `json:"sd_free_space"`
	MiuiVersion     string `json:"miui_version"`
	ScreenWidth     int    `json:"screen_width"`
	ScreenHeight    int    `json:"screen_height"`
	OaidSource      int    `json:"oaid_source"`
}

type SdkAnticheatV4DidModel struct {
	HarmonyOs        int    `json:"harmony_os"`
	Imei             string `json:"imei"`
	AndroidId        string `json:"android_id"`
	Oaid             string `json:"oaid"`
	HarmonyOsVersion string `json:"harmony_os_version"`
}
