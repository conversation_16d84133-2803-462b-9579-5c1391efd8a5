package models

// LogoDataStu ...
// api_config: [{"from":"00","logo":"2","to":"02","white_list":["北京"],"black_list":[]}]
// sdk_config: [{"from":"02","logo":"1","to":"07","white_list":["北京","广东-深圳","浙江"],"black_list":[]}]
type LogoDataStu struct {
	LocalAppIDs   string              `json:"local_app_ids,omitempty"`
	LocalPosIDs   string              `json:"local_pos_ids,omitempty"`
	ApiConfigJson string              `json:"api_config_json,omitempty"`
	SdkConfigJson string              `json:"sdk_config_json,omitempty"`
	ApiConfig     []LogoDataConfigStu `json:"api_config,omitempty"`
	SdkConfig     []LogoDataConfigStu `json:"sdk_config,omitempty"`
}

type LogoDataConfigStu struct {
	From      string   `json:"from,omitempty"`
	To        string   `json:"to,omitempty"`
	Logo      string   `json:"logo,omitempty"`
	WhiteList []string `json:"white_list,omitempty"`
	BlackList []string `json:"black_list,omitempty"`
}

type LogoDataCacheConfigStu struct {
	SdkLogo      string   `json:"sdk_logo,omitempty"`
	SdkWhiteList []string `json:"sdk_white_list,omitempty"`
	SdkBlackList []string `json:"sdk_black_list,omitempty"`

	ApiLogo      string   `json:"api_logo,omitempty"`
	ApiWhiteList []string `json:"api_white_list,omitempty"`
	ApiBlackList []string `json:"api_black_list,omitempty"`
}
