package models

import (
	"encoding/json"
	"mh_proxy/db"
	"mh_proxy/utilities"
	"strconv"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	uuid "github.com/satori/go.uuid"
)

type SdkOaidModel struct {
	PlatformAppId   string `json:"platform_app_id"`
	PlatformMediaId string `json:"platform_media_id"`
	PlatformPosId   string `json:"platform_pos_id"`
}

func BigDataHoloSdkOaidDebug(reqid string, data []byte, mhReq *MHReq) {
	if utilities.SkipHologress {
		return
	}

	var sdkOaidArray []SdkOaidModel
	_ = json.Unmarshal(data, &sdkOaidArray)

	didMd5Key := mhReq.Device.DIDMd5
	deviceType := "1"
	connectType := strconv.Itoa(mhReq.Network.ConnectType)
	carrier := strconv.Itoa(mhReq.Network.Carrier)
	posId := strconv.Itoa(mhReq.Pos.ID)

	for _, sdkOaidItem := range sdkOaidArray {
		if sdkOaidItem.PlatformPosId == "6009273625476997" || sdkOaidItem.PlatformPosId == "4142414524648106" {
			id := uuid.NewV4().String()
			put := holoclient.NewMutationRequest(db.NewHologresAdxSSPDataTableSchema("debug", "sdk_oaid_debug"))
			put.SetTextValByColName("id", id, len(id))
			put.SetTextValByColName("reqid", reqid, len(reqid))
			put.SetTextValByColName("app_id", mhReq.App.AppID, len(mhReq.App.AppID))
			put.SetTextValByColName("pos_id", posId, len(posId))
			put.SetTextValByColName("channel", sdkOaidItem.PlatformMediaId, len(sdkOaidItem.PlatformMediaId))
			put.SetTextValByColName("p_app_id", sdkOaidItem.PlatformAppId, len(sdkOaidItem.PlatformAppId))
			put.SetTextValByColName("p_pos_id", sdkOaidItem.PlatformPosId, len(sdkOaidItem.PlatformPosId))
			put.SetTextValByColName("app_name", mhReq.App.AppName, len(mhReq.App.AppName))
			put.SetTextValByColName("app_bundle", mhReq.App.AppBundleID, len(mhReq.App.AppBundleID))
			put.SetTextValByColName("os", mhReq.Device.Os, len(mhReq.Device.Os))
			put.SetTextValByColName("osv", mhReq.Device.OsVersion, len(mhReq.Device.OsVersion))
			put.SetTextValByColName("did_md5", didMd5Key, len(didMd5Key))
			put.SetTextValByColName("imei", mhReq.Device.Imei, len(mhReq.Device.Imei))
			put.SetTextValByColName("imei_md5", mhReq.Device.ImeiMd5, len(mhReq.Device.ImeiMd5))
			put.SetTextValByColName("android_id", mhReq.Device.AndroidID, len(mhReq.Device.AndroidID))
			put.SetTextValByColName("android_id_md5", mhReq.Device.AndroidIDMd5, len(mhReq.Device.AndroidIDMd5))
			put.SetTextValByColName("idfa", mhReq.Device.Idfa, len(mhReq.Device.Idfa))
			put.SetTextValByColName("idfa_md5", mhReq.Device.IdfaMd5, len(mhReq.Device.IdfaMd5))
			put.SetTextValByColName("ip", mhReq.Device.IP, len(mhReq.Device.IP))
			put.SetTextValByColName("ua", mhReq.Device.Ua, len(mhReq.Device.Ua))
			put.SetTextValByColName("oaid", mhReq.Device.Oaid, len(mhReq.Device.Oaid))
			put.SetTextValByColName("model", mhReq.Device.Model, len(mhReq.Device.Model))
			put.SetTextValByColName("manufacturer", mhReq.Device.Manufacturer, len(mhReq.Device.Manufacturer))
			put.SetInt32ValByColName("screen_width", int32(mhReq.Device.ScreenWidth))
			put.SetInt32ValByColName("screen_height", int32(mhReq.Device.ScreenHeight))
			put.SetTextValByColName("appstore_version", mhReq.Device.AppStoreVersion, len(mhReq.Device.AppStoreVersion))
			put.SetTextValByColName("hms_version", mhReq.Device.HMSCoreVersion, len(mhReq.Device.HMSCoreVersion))
			put.SetTextValByColName("device_type", deviceType, len(deviceType))
			put.SetTextValByColName("connect_type", connectType, len(connectType))
			put.SetTextValByColName("carrier", carrier, len(carrier))

			day := time.Now().Format("2006-01-02")
			hour := time.Now().Format("15")
			minute := time.Now().Format("04")
			put.SetTextValByColName("dd", day, len(day))
			put.SetTextValByColName("hh", hour, len(hour))
			put.SetTextValByColName("mm", minute, len(minute))
			put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

			db.GlbHologresAdxSSPUpdateDataDb.Submit(put)
		}
	}
}
