package models

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/db"
	"mh_proxy/utils"
	"time"
)

// GetPlatformPosInfo ...
func GetPlatformPosInfo(c context.Context, platformPosID string, platformAppID string, platformMediaID string) *PlatformPosStu {

	cacheKey := "go_platform_" + platformAppID + "_" + platformMediaID + "_" + platformPosID

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var platformPos PlatformPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &platformPos)

		return &platformPos
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// GetPlatformIDByPlatformInfo ...
func GetPlatformIDByPlatformInfo(c context.Context, platformPosID string, platformAppID string) string {

	cacheKey := "go_platform_id_" + platformAppID + "_" + platformPosID
	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return ""
		}

		return string(cacheValue)
	}

	fmt.Println("-----------------------------------", cacheKey)

	return ""
}

// GetPlatformAppInfo ...
func GetPlatformAppInfo(c context.Context, platformAppID string) *PlatformPosStu {

	cacheKey := "go_platform_app_" + platformAppID

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var platformPos PlatformPosStu

	if cacheError != nil {
		// fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &platformPos)

		return &platformPos
	}

	fmt.Println("-----------------------------------", cacheKey)

	return nil
}

// IsPlatformPolicyAPIOK 是否平台策略流水限制, 目前api使用
// 2025.05.21 增加请求通过率
func IsPlatformPolicyAPIOK(c context.Context, mhReq *MHReq, localPos *LocalPosStu, platformMediaID string, platformCorpID int) bool {
	if localPos.LocalAppID == "10012" || localPos.LocalAppID == "10016" || localPos.LocalAppID == "10981" || localPos.LocalAppID == "10999" {
		return true
	}

	platformPolicy := GetPlatformPolicy(c, platformMediaID, platformCorpID)

	if platformPolicy == nil {
		// fmt.Println("fail to get")
		return true
	}

	// 是否大开关
	if platformPolicy.PlatformIsActive == 1 {
		// fmt.Println("is active")
	} else {
		// fmt.Println("is not active")
		return true
	}

	weekday := time.Now().Weekday()
	// fmt.Println(int(weekday))
	if weekday == 1 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType1 == 2 {
			if platformPolicy.PlatformMaxCost1 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 1")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 2 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType2 == 2 {
			if platformPolicy.PlatformMaxCost2 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 2")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 3 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType3 == 2 {
			if platformPolicy.PlatformMaxCost3 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 3")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 4 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType4 == 2 {
			if platformPolicy.PlatformMaxCost4 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 4")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 5 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType5 == 2 {
			if platformPolicy.PlatformMaxCost5 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 5")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 6 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType6 == 2 {
			if platformPolicy.PlatformMaxCost6 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 6")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	} else if weekday == 0 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType0 == 2 {
			if platformPolicy.PlatformMaxCost0 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 0")
				return rand.Intn(100) < platformPolicy.PlatformMaxReqPassWeight
			}
		}
	}
	return true
}

// IsPlatformPolicyOK 是否平台策略流水限制, 目前sdk使用
func IsPlatformPolicyOK(c context.Context, mhReq *MHReq, localPos *LocalPosStu, platformMediaID string, platformCorpID int) bool {
	if localPos.LocalAppID == "10012" || localPos.LocalAppID == "10016" || localPos.LocalAppID == "10981" || localPos.LocalAppID == "10999" {
		return true
	}

	platformPolicy := GetPlatformPolicy(c, platformMediaID, platformCorpID)

	if platformPolicy == nil {
		// fmt.Println("fail to get")
		return true
	}

	// 是否大开关
	if platformPolicy.PlatformIsActive == 1 {
		// fmt.Println("is active")
	} else {
		// fmt.Println("is not active")
		return true
	}

	weekday := time.Now().Weekday()
	// fmt.Println(int(weekday))
	if weekday == 1 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType1 == 2 {
			if platformPolicy.PlatformMaxCost1 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 1")
				return false
			}
		}
	} else if weekday == 2 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType2 == 2 {
			if platformPolicy.PlatformMaxCost2 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 2")
				return false
			}
		}
	} else if weekday == 3 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType3 == 2 {
			if platformPolicy.PlatformMaxCost3 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 3")
				return false
			}
		}
	} else if weekday == 4 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType4 == 2 {
			if platformPolicy.PlatformMaxCost4 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 4")
				return false
			}
		}
	} else if weekday == 5 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType5 == 2 {
			if platformPolicy.PlatformMaxCost5 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 5")
				return false
			}
		}
	} else if weekday == 6 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType6 == 2 {
			if platformPolicy.PlatformMaxCost6 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 6")
				return false
			}
		}
	} else if weekday == 0 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType0 == 2 {
			if platformPolicy.PlatformMaxCost0 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 0")
				return false
			}
		}
	}
	return true
}

// IsPlatformPolicyCostOK 是否平台策略流水限制OK, 目前api使用
func IsPlatformPolicyCostOK(c context.Context, mhReq *MHReq, localPos *LocalPosStu, platformMediaID string, platformCorpID int) bool {
	if localPos.LocalAppID == "10012" || localPos.LocalAppID == "10016" || localPos.LocalAppID == "10981" || localPos.LocalAppID == "10999" {
		return true
	}

	platformPolicy := GetPlatformPolicy(c, platformMediaID, platformCorpID)

	if platformPolicy == nil {
		// fmt.Println("fail to get")
		return true
	}

	// 是否大开关
	if platformPolicy.PlatformIsActive == 1 {
		// fmt.Println("is active")
	} else {
		// fmt.Println("is not active")
		return true
	}

	weekday := time.Now().Weekday()
	// fmt.Println(int(weekday))
	if weekday == 1 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType1 == 2 {
			if platformPolicy.PlatformMaxCost1 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 1")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 2 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType2 == 2 {
			if platformPolicy.PlatformMaxCost2 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 2")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 3 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType3 == 2 {
			if platformPolicy.PlatformMaxCost3 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 3")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 4 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType4 == 2 {
			if platformPolicy.PlatformMaxCost4 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 4")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 5 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType5 == 2 {
			if platformPolicy.PlatformMaxCost5 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 5")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 6 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType6 == 2 {
			if platformPolicy.PlatformMaxCost6 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 6")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	} else if weekday == 0 {
		// 流水限制
		if platformPolicy.PlatformMaxCostType0 == 2 {
			if platformPolicy.PlatformMaxCost0 < platformPolicy.PlatformBigDataCost {
				fmt.Println("cost false 0")
				// min_weight到max_weight概率返回true
				rand1 := 0
				if platformPolicy.PlatformMaxCostWeight > platformPolicy.PlatformMinCostWeight {
					rand1 = platformPolicy.PlatformMinCostWeight + rand.Intn(platformPolicy.PlatformMaxCostWeight-platformPolicy.PlatformMinCostWeight)
				} else {
					rand1 = platformPolicy.PlatformMinCostWeight
				}
				if rand.Intn(100) < rand1 {
					return true
				}
				return false
			}
		}
	}
	return true
}

// GetPlatformPolicy ...
func GetPlatformPolicy(c context.Context, platformMediaID string, platformCorpID int) *PlatformPolicyStu {

	cacheKey := "platform_policy_" + platformMediaID + "_" + utils.ConvertIntToString(platformCorpID)

	cacheValue, cacheError := db.GlbBigCacheMinute.Get(cacheKey)
	var platformPolicy PlatformPolicyStu

	if cacheError != nil {
		fmt.Println("cache error:", cacheKey, cacheError)
	} else {
		// fmt.Println("cache value:", string(cacheValue))
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &platformPolicy)

		return &platformPolicy
	}

	redisValue, redisErr := db.GlbRedis.Get(c, cacheKey).Result()
	if redisErr != nil {
		// fmt.Println("redis error:", redisErr)
	} else {
		// fmt.Println("redis value:", redisValue)
		db.GlbBigCacheMinute.Set(cacheKey, []byte(redisValue))

		if len(redisValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(redisValue), &platformPolicy)

		return &platformPolicy
	}

	// 取platform policy配置
	row := db.GlbMySQLDb.QueryRow("SELECT a.id, a.type, IFNULL(a.is_active, 0),"+
		" IFNULL(a.real_exp_cost, 0),"+
		" IFNULL(a.max_cost_type_1, 0), IFNULL(a.max_cost_type_2, 0), IFNULL(a.max_cost_type_3, 0), IFNULL(a.max_cost_type_4, 0), IFNULL(a.max_cost_type_5, 0), IFNULL(a.max_cost_type_6, 0), IFNULL(a.max_cost_type_0, 0),"+
		" IFNULL(a.max_cost_1, 0), IFNULL(a.max_cost_2, 0), IFNULL(a.max_cost_3, 0), IFNULL(a.max_cost_4, 0), IFNULL(a.max_cost_5, 0), IFNULL(a.max_cost_6, 0), IFNULL(a.max_cost_0, 0),"+
		" IFNULL(a.min_cost_weight, 0), IFNULL(a.max_cost_weight, 0), IFNULL(a.req_pass_weight, 0) "+
		" FROM platform_policy_list a where a.type = ? and a.corp_id = ?", platformMediaID, platformCorpID)
	err := row.Scan(&platformPolicy.ID, &platformPolicy.PlatformMediaID, &platformPolicy.PlatformIsActive,
		&platformPolicy.PlatformBigDataCost,
		&platformPolicy.PlatformMaxCostType1, &platformPolicy.PlatformMaxCostType2, &platformPolicy.PlatformMaxCostType3, &platformPolicy.PlatformMaxCostType4, &platformPolicy.PlatformMaxCostType5, &platformPolicy.PlatformMaxCostType6, &platformPolicy.PlatformMaxCostType0,
		&platformPolicy.PlatformMaxCost1, &platformPolicy.PlatformMaxCost2, &platformPolicy.PlatformMaxCost3, &platformPolicy.PlatformMaxCost4, &platformPolicy.PlatformMaxCost5, &platformPolicy.PlatformMaxCost6, &platformPolicy.PlatformMaxCost0,
		&platformPolicy.PlatformMinCostWeight, &platformPolicy.PlatformMaxCostWeight, &platformPolicy.PlatformMaxReqPassWeight)
	if err != nil {
		fmt.Println("get platform policy data failed, error:[%v]" + err.Error())
		db.GlbBigCacheMinute.Set(cacheKey, []byte(""))
		db.GlbRedis.Set(c, cacheKey, "", config.RedisKeyConfigTTL).Err()

		return nil
	}

	// fmt.Println("999999999999999999999999999999999999999999999")
	// fmt.Println(platformPolicy.PlatformBigDataAndroidReq)
	// fmt.Println(platformPolicy.PlatformBigDataIosReq)
	// fmt.Println(platformPolicy.PlatformBigDataCost)
	// fmt.Println("999999999999999999999999999999999999999999999")
	// fmt.Println(platformPolicy.PlatformMaxCost5)
	// fmt.Println("999999999999999999999999999999999999999999999")

	// cache
	platformPosJSON, _ := json.Marshal(platformPolicy)

	db.GlbBigCacheMinute.Set(cacheKey, platformPosJSON)
	db.GlbRedis.Set(c, cacheKey, platformPosJSON, config.RedisKeyConfigTTL).Err()

	return &platformPolicy
}

// PlatformPosStu ...
type PlatformPosStu struct {
	PlatformPosID                   string
	PlatformAppID                   string
	PlatformAppCorpID               int
	PlatformMediaID                 string
	PlatformAppType                 string `json:"PlatformAppType,omitempty"`         // 0 api; 1 sdk
	PlatformAppProtocolType         int    `json:"PlatformAppProtocolType,omitempty"` // 上游api协议类型, 0: protobuf, 1: json
	PlatformAppBundle               string `json:"PlatformAppBundle,omitempty"`
	PlatformAppUnionBaiduMediaID    string `json:"PlatformAppUnionBaiduMediaID,omitempty"` // 百青藤media id
	PlatformPosStyleIDs             string `json:"PlatformPosStyleIDs,omitempty"`          // pos styleids
	PlatformOs                      string // 0 -> Android; 1 -> iOS; 2 -> 通用
	PlatformPosWidth                int
	PlatformPosHeight               int
	PlatformPosType                 int    // 1 -> Banner; 2 -> 开屏; 3 -> 插屏; 4 -> 原生; 13 -> 贴片; 9 -> 全屏视频; 11 -> 激励视频; 14 -> 图标
	PlatformPosMaterialType         int    // 0 -> 图片; 1 -> 视频; 2 -> 图片+视频; 3 -> 模版
	PlatformPosIsDebug              int    `json:"PlatformPosIsDebug,omitempty"`
	PlatformPosIsTMax               int    `json:"PlatformPosIsTMax,omitempty"`               // 是否上报tmax, gdt, 枫岚rtb
	PlatformPosTMaxValue            string `json:"PlatformPosTMaxValue,omitempty"`            // 上报tmax值, gdt, 枫岚rtb
	PlatformPosIsReplaceXY          int    `json:"PlatformPosIsReplaceXY,omitempty"`          // 是否替换xy, 0为客户端替换, 1为服务端替换, 2为服务端替换(逻辑像素)
	PlatformPosReplaceXYType        int    `json:"PlatformPosReplaceXYType,omitempty"`        // 替换xy类型, 0为替换坐标库, 1为替换-999, 2为替换空, 3为替换0
	PlatformPosClickPointLibKey     string `json:"PlatformPosClickPointLibKey,omitempty"`     // 替换clickpoint lib key
	PlatformPosIsReplaceXYLibOffset int    `json:"PlatformPosIsReplaceXYLibOffset,omitempty"` // 替换xy 新库 key offset
	PlatformPosIsReplaceXYDefault   int    `json:"PlatformPosIsReplaceXYDefault,omitempty"`   // 是否SDK替换默认点击坐标
	PlatformPosReplaceXYDefault     string `json:"PlatformPosReplaceXYDefault,omitempty"`     // SDK替换默认点击坐标
	// 替换clickpoint lib key配置
	PlatformPosClickPointLibConfigJson string                           `json:"PlatformPosClickPointLibConfigJson,omitempty"`
	PlatformPosClickPointLibConfig     []PlatformPosWhiteMediaConfigStu `json:"PlatformPosClickPointLibConfig,omitempty"`
	// 是否各种点击坐标替换, 填充下发
	PlatformPosIsAdIDReplaceXY              int    `json:"PlatformPosIsAdIDReplaceXY,omitempty"`              // SDK点击坐标替换(3.3.0以上), 填充下发, 替换(默认值)
	PlatformPosReplaceAdIDSimXYDefault      int    `json:"PlatformPosReplaceAdIDSimXYDefault,omitempty"`      // SDK非标准点击坐标默认值替换(3.3.0以上), 填充下发, 不替换(默认值)
	PlatformPosReplaceAdIDSimXYDefaultValue string `json:"PlatformPosReplaceAdIDSimXYDefaultValue,omitempty"` // SDK非标准点击坐标默认值替换(3.3.0以上), 替换值
	PlatformPosDirection                    int    `json:"PlatformPosDirection,omitempty"`                    // 0 -> 横屏; 1 -> 竖屏; 2 -> 全方向; 3 -> 自适应
	PlatformAppName                         string `json:"PlatformAppName,omitempty"`
	PlatformAppVersion                      string `json:"PlatformAppVersion,omitempty"`
	PlatformAppKey                          string `json:"PlatformAppKey,omitempty"`
	PlatformAppSecret                       string `json:"PlatformAppSecret,omitempty"`
	PlatformAppUpURL                        string `json:"PlatformAppUpURL,omitempty"`
	PlatformAppUpDebugURL                   string `json:"PlatformAppUpDebugURL,omitempty"`
	PlatformAppFilterUa                     int    `json:"PlatformAppFilterUa,omitempty"`
	PlatformAppFilterIllegalUa              int    `json:"PlatformAppFilterIllegalUa,omitempty"`
	PlatformAppIsActive                     int    `json:"PlatformAppIsActive,omitempty"`
	PlatformPosIsActive                     int    `json:"PlatformPosIsActive,omitempty"`
	// 厂商多选
	// <el-checkbox-group v-model="manufacturerList">
	// 	<el-checkbox label="all">全部</el-checkbox>
	// 	<el-checkbox label="oppo">OPPO</el-checkbox>
	// 	<el-checkbox label="vivo">VIVO</el-checkbox>
	// 	<el-checkbox label="huawei">HUAWEI</el-checkbox>
	// 	<el-checkbox label="honor">荣耀</el-checkbox>
	// 	<el-checkbox label="xiaomi">小米</el-checkbox>
	// 	<el-checkbox label="lenovo">联想</el-checkbox>
	// 	<el-checkbox label="meizu">魅族</el-checkbox>
	// 	<el-checkbox label="others">其他</el-checkbox>
	// </el-checkbox-group>
	PlatformPosManufacturerList string `json:"PlatformPosManufacturerList,omitempty"`
	// 枫岚RTB请求广告类型
	PlatformPosReqPosTypes string `json:"PlatformPosReqPosTypes,omitempty"` // 多选: 1 H5+DP; 2 下载; 3 小程序
	// 价格是否加密
	PlatformAppIsPriceEncrypt int    `json:"PlatformAppIsPriceEncrypt,omitempty"`
	PlatformAppPriceEncrypt   string `json:"platform_app_price_encrypt,omitempty"`
	PlatformAppPriceEncrypt2  string `json:"platform_app_price_encrypt_2,omitempty"`
	// 是否替换did
	PlatformAppIsReplaceDID   int `json:"PlatformAppIsReplaceDID,omitempty"`
	PlatformAppIsReplaceDIDUa int `json:"PlatformAppIsReplaceDIDUa,omitempty"`
	// 替换did md5_key, 5 ratio
	PlatformAppReplaceNoRatio     int    `json:"PlatformAppReplaceNoRatio,omitempty"`
	PlatformAppReplaceWhiteMedias string `json:"PlatformAppReplaceWhiteMedias,omitempty"`
	// 前提是替换包白名单媒体, 如果是替换包白名单媒体并且走到上游配置的广告位, 就忽略替换包白名单媒体功能
	PlatformAppReplaceWhiteMediasPlatformPos string `json:"PlatformAppReplaceWhiteMediasPlatformPos,omitempty"`
	// magic 替换包库md5key, 新版本
	PlatformAppMagicReplaceDIDLibMd5Key string `json:"PlatformAppMagicReplaceDIDLibMd5Key,omitempty"`
	// dau
	PlatformAppMaxDAUType   int `json:"PlatformAppMaxDAUType,omitempty"` // 1 无限制, 2 限量数(单位万)
	PlatformAppMaxDAU       int `json:"PlatformAppMaxDAU,omitempty"`
	PlatformAppMaxDAURandom int `json:"PlatformAppMaxDAURandom,omitempty"`
	// 是否人群包
	PlatformAppIsCrowdPackage int `json:"PlatformAppIsCrowdPackage,omitempty"`
	// 人群包类型, 0 正常, 1 反向
	PlatformAppCrowdPackageType int `json:"PlatformAppCrowdPackageType,omitempty"`
	// 人群包媒体白名单
	PlatformAppCrowdPackageWhiteMediasConfigJson string `json:"PlatformAppCrowdPackageWhiteMediasConfigJson,omitempty"`
	// 人群包媒体白名单配置
	PlatformAppCrowdPackageWhiteMediasConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformAppCrowdPackageWhiteMediasConfig,omitempty"`
	// 人群包id
	PlatformAppCrowdPackageID string `json:"PlatformAppCrowdPackageID,omitempty"`
	// 人群包未命中时强制通过比例
	PlatformAppCrowdPackageFailedMediasConfigJson string `json:"PlatformAppCrowdPackageFailedMediasConfigJson,omitempty"`
	// 人群包未命中时强制通过比例
	PlatformAppCrowdPackageFailedMediasConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformAppCrowdPackageFailedMediasConfig,omitempty"`
	// 是否dp上报失败
	PlatformAppIsDeepLinkFailed int `json:"PlatformAppIsDeepLinkFailed,omitempty"`
	// 是否点击坐标修正
	PlatformAppIsClickXYFix int `json:"PlatformAppIsClickXYFix,omitempty"`
	// 点击坐标修正生效比例
	PlatformAppClickXYFixRatio int `json:"PlatformAppClickXYFixRatio,omitempty"`
	// 只要osv>=10
	PlatformPosIsOnlyMore10 int `json:"PlatformPosIsOnlyMore10,omitempty"`
	// 只要hms_core
	PlatformPosIsHmsCore int `json:"PlatformPosIsHmsCore,omitempty"`
	// pos key, 360
	PlatformPosKey string `json:"PlatformPosKey,omitempty"`
	// 拼多多channel
	PlatformAppPinDuoDuoChannel string `json:"platform_app_pdd_channel,omitempty"`
	// 是否上报ua
	PlatformAppIsReportUa int `json:"PlatformAppIsReportUa,omitempty"`
	// ios上报主参数,
	// 1. 多选, 谁合适就报谁; 如果有其中有一个合适了, 就不走附属参数1,2;
	// 2. 如果都不合适, 先走附属参数1;
	// 3. 如果附属参数1不合适, 先走附属参数2
	PlatformAppIOSReportMainParameter string `json:"PlatformAppIOSReportMainParameter,omitempty"`
	// ios附属参数1
	PlatformAppIOSReportSubParameter1 string `json:"PlatformAppIOSReportSubParameter1,omitempty"`
	// ios附属参数2
	PlatformAppIOSReportSubParameter2 string `json:"PlatformAppIOSReportSubParameter2,omitempty"`
	// caid上报类型, 单版本or多版本, 0 单版本, 1 多版本
	PlatformAppReportCaidType int `json:"PlatformAppReportCaidType,omitempty"`
	// caid单版本版本号
	PlatformAppSingleCaidVersion string `json:"PlatformAppSingleCaidVersion,omitempty"`
	// 是否上报app list
	PlatformAppIsReportAppList int `json:"PlatformAppIsReportAppList,omitempty"`
	// 是否上报android_id
	PlatformAppIsReportAndroidID int `json:"PlatformAppIsReportAndroidID,omitempty"`
	// 是否上报sld, 目前只是gdt
	PlatformPosIsReportSLD int `json:"PlatformPosIsReportSLD,omitempty"`
	// sld类型, 目前只是gdt
	PlatformPosSLDType int `json:"PlatformPosSLDType,omitempty"`
	// idfa明文转MD5
	PlatformAppIsReportIDFAMd5 int `json:"PlatformAppIsReportIDFAMd5,omitempty"`
	// 是否支持厂商应用商店
	PlatformAppIsSupportAppStore int `json:"PlatformAppIsSupportAppStore,omitempty"`
	// 是否请求快应用广告
	PlatformAppIsSupportQuickApp int `json:"PlatformAppIsSupportQuickApp,omitempty"`
	// 过滤请求百分比
	PlatformAppReqFilterWeight int `json:"PlatformAppReqFilterWeight,omitempty"`
	// 是否ip请求限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPReq int `json:"PlatformAppIsLimitIPReq,omitempty"`
	// 单ip允许最大请求数
	PlatformAppMaxNumLimitIPReq string `json:"PlatformAppMaxNumLimitIPReq,omitempty"`
	// 单ip请求数规则
	PlatformAppLimitIPReqRuleJson string `json:"PlatformAppLimitIPReqRuleJson,omitempty"`
	// 是否ip曝光限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPExp int `json:"PlatformAppIsLimitIPExp,omitempty"`
	// 单ip允许最大曝光数
	PlatformAppMaxNumLimitIPExp string `json:"PlatformAppMaxNumLimitIPExp,omitempty"`
	// 是否ip点击限制, 0 不限制, 1 限制
	PlatformAppIsLimitIPClk int `json:"PlatformAppIsLimitIPClk,omitempty"`
	// 单ip允许最大点击数
	PlatformAppMaxNumLimitIPClk string `json:"PlatformAppMaxNumLimitIPClk,omitempty"`
	// 是否did请求限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDReq int `json:"PlatformAppIsLimitDIDReq,omitempty"`
	// 单did允许最大请求数
	PlatformAppMaxNumLimitDIDReq string `json:"PlatformAppMaxNumLimitDIDReq,omitempty"`
	// 是否did曝光限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDExp int `json:"PlatformAppIsLimitDIDExp,omitempty"`
	// 单did允许最大曝光数
	PlatformAppMaxNumLimitDIDExp string `json:"PlatformAppMaxNumLimitDIDExp,omitempty"`
	// 是否did点击限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDClk int `json:"PlatformAppIsLimitDIDClk,omitempty"`
	// 单did允许最大点击数
	PlatformAppMaxNumLimitDIDClk string `json:"PlatformAppMaxNumLimitDIDClk,omitempty"`
	// 是否did req间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDReqInterval int `json:"PlatformAppIsLimitDIDReqInterval,omitempty"`
	// 单did req允许最大间隔数
	PlatformAppMaxNumLimitDIDReqInterval string `json:"PlatformAppMaxNumLimitDIDReqInterval,omitempty"`
	// 是否did exp间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDExpInterval int `json:"PlatformAppIsLimitDIDExpInterval,omitempty"`
	// 单did exp允许最大间隔数
	PlatformAppMaxNumLimitDIDExpInterval string `json:"PlatformAppMaxNumLimitDIDExpInterval,omitempty"`
	// 是否did clk间隔限制, 0 不限制, 1 限制
	PlatformAppIsLimitDIDClkInterval int `json:"PlatformAppIsLimitDIDClkInterval,omitempty"`
	// 单did clk允许最大间隔数
	PlatformAppMaxNumLimitDIDClkInterval string `json:"PlatformAppMaxNumLimitDIDClkInterval,omitempty"`
	// csj配置
	PlatformAppSHA1        string `json:"PlatformAppSHA1,omitempty"`
	PlatformAppItunesID    string `json:"PlatformAppItunesID,omitempty"`
	PlatformAppAdxName     string `json:"PlatformAppAdxName,omitempty"`
	PlatformAppAdxPassword string `json:"PlatformAppAdxPassword,omitempty"`
	// support_query support_pkg 类型, 0 不上报, 1 优先关键词, 2 优先包名, 3 全上报
	PlatformPosSupportQuerysPkgsType int `json:"PlatformPosSupportQuerysPkgsType,omitempty"`
	// support_query 配置
	PlatformPosIsSupportQuerys   int `json:"PlatformPosIsSupportQuerys,omitempty"`
	PlatformPosSupportQuerysType int `json:"PlatformPosSupportQuerysType,omitempty"`
	// support_pkg 配置
	PlatformPosIsSupportPkgs   int `json:"PlatformPosIsSupportPkgs,omitempty"`
	PlatformPosSupportPkgsType int `json:"PlatformPosSupportPkgsType,omitempty"`
	// 是否替换上报包名 0 关, 1 开
	PlatformAppIsReplaceAppBundleID         int    `json:"PlatformAppIsReplaceAppBundleID,omitempty"`
	PlatformAppReplaceAppBundleIDJsonConfig string `json:"PlatformAppReplaceAppBundleIDJsonConfig,omitempty"`
	PlatformAppCustomReplaceAppBundleIDType int    `json:"PlatformAppCustomReplaceAppBundleIDType,omitempty"`
	PlatformAppCustomReplaceAppBundleID     string `json:"PlatformAppCustomReplaceAppBundleID,omitempty"`
	// ecpm type, 0(实时竞价), 1(固定价)
	PlatformPosEcpmType int `json:"PlatformPosEcpmType,omitempty"`
	// ecpm(分)
	PlatformPosEcpm int `json:"PlatformPosEcpm,omitempty"`
	// 单请求最大曝光次数
	PlatformPosMaxExpLimit int `json:"PlatformPosMaxExpLimit,omitempty"`
	// 单请求最大点击次数
	PlatformPosMaxClickLimit int `json:"PlatformPosMaxClickLimit,omitempty"`

	// 是否请求频率限制
	PlatformPosIsReqLimitFrequency int `json:"PlatformPosIsReqLimitFrequency,omitempty"`
	// 请求频率限制json
	PlatformPosReqLimitFrequencyConfigJson string `json:"PlatformPosReqLimitFrequencyConfigJson,omitempty"`
	// 请求频率限制白名单json
	PlatformPosReqLimitFrequencyWhiteMediasConfigJson string `json:"PlatformPosReqLimitFrequencyWhiteMediasConfigJson,omitempty"`
	// 请求频率限制白名单配置
	PlatformPosReqLimitFrequencyWhiteMediasConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformPosReqLimitFrequencyWhiteMediasConfig,omitempty"`

	// 是否丢弃deeplink
	PlatformPosIsDiscardDeeplink int `json:"PlatformPosIsDiscardDeeplink,omitempty"`
	// 丢弃deeplink json
	PlatformPosDiscardDeeplinkConfigJson string `json:"PlatformPosDiscardDeeplinkConfigJson,omitempty"`
	// 丢弃deeplink config
	PlatformPosDiscardDeeplinkConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformPosDiscardDeeplinkConfig,omitempty"`

	// 是否三方监测
	PlatformPosIsThirdMonitor int `json:"PlatformPosIsThirdMonitor,omitempty"`
	// 三方监测白名单
	PlatformPosThirdMonitorWhiteMediasConfigJson string                           `json:"PlatformPosThirdMonitorWhiteMediasConfigJson,omitempty"`
	PlatformPosThirdMonitorWhiteMediasConfig     []PlatformPosWhiteMediaConfigStu `json:"PlatformPosThirdMonitorWhiteMediasConfig,omitempty"`
	// 三方监测曝光链接
	PlatformPosThirdMonitorExpLinkJson string   `json:"PlatformPosThirdMonitorExpLinkJson,omitempty"`
	PlatformPosThirdMonitorExpLink     []string `json:"PlatformPosThirdMonitorExpLink,omitempty"`
	// 三方监测点击链接
	PlatformPosThirdMonitorClkLinkJson string   `json:"PlatformPosThirdMonitorClkLinkJson,omitempty"`
	PlatformPosThirdMonitorClkLink     []string `json:"PlatformPosThirdMonitorClkLink,omitempty"`
	// 是否忽略点击上报, 0 关, 1 按照配置比例跳过, 2 按照设置点击预期值跳过
	PlatformPosIsSkipReportClick int `json:"PlatformPosIsSkipReportClick,omitempty"`
	// 忽略点击上报配置json
	PlatformPosSkipReportClickConfigJson string `json:"PlatformPosSkipReportClickConfigJson,omitempty"`
	// 忽略点击上报配置
	PlatformPosSkipReportClickConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformPosSkipReportClickConfig,omitempty"`
	// 点击率预期值
	PlatformPosExpectedClickRate float32 `json:"PlatformPosExpectedClickRate,omitempty"`
	// 预期忽略点击上报配置json
	PlatformPosExpectedSkipReportClickConfigJson string `json:"PlatformPosExpectedSkipReportClickConfigJson,omitempty"`
	// 预期忽略点击上报配置
	PlatformPosExpectedSkipReportClickConfig []PlatformPosWhiteMediaConfigStu `json:"PlatformPosExpectedSkipReportClickConfig,omitempty"`

	// 是否上报竞价成功, 竞价失败
	PlatformAppIsReportWin int `json:"PlatformAppIsReportWin,omitempty"`
	// 0 曝光时上报, 1 平台竞胜时上报(填充上报)
	PlatformAppReportWinConfig int `json:"PlatformAppReportWinConfig,omitempty"`
	// PlatformAppReportWinType: 0 一价, 1 二价
	PlatformAppReportWinType       int    `json:"PlatformAppReportWinType,omitempty"`
	PlatformAppReportWinMinWeight  string `json:"PlatformAppReportWinMinWeight,omitempty"`
	PlatformAppReportWinMaxWeight  string `json:"PlatformAppReportWinMaxWeight,omitempty"`
	PlatformAppIsReportLoss        int    `json:"PlatformAppIsReportLoss,omitempty"`
	PlatformAppReportLossMinWeight string `json:"PlatformAppReportLossMinWeight,omitempty"`
	PlatformAppReportLossMaxWeight string `json:"PlatformAppReportLossMaxWeight,omitempty"`
	// ks竞价失败配置, 0为老版本, 1为新版本
	PlatformAppIsReportLossPrice int `json:"PlatformAppIsReportLossPrice,omitempty"`
	// 替换宏 __AD_TI__
	PlatformAppIsReportLossPriceTitle int `json:"PlatformAppIsReportLossPriceTitle,omitempty"`
	// 替换宏 __AD_N__
	PlatformAppIsReportLossPricePublisher int `json:"PlatformAppIsReportLossPricePublisher,omitempty"`
	// 替换宏 __ADN_NAME__
	PlatformAppIsReportLossPricePlatformName int    `json:"PlatformAppIsReportLossPricePlatformName,omitempty"`
	PlatformAppReportLossPriceCsjWeight      string `json:"PlatformAppReportLossPriceCsjWeight,omitempty"`
	PlatformAppReportLossPriceGdtWeight      string `json:"PlatformAppReportLossPriceGdtWeight,omitempty"`
	PlatformAppReportLossPriceBaiduWeight    string `json:"PlatformAppReportLossPriceBaiduWeight,omitempty"`
	PlatformAppReportLossPriceOtherWeight    string `json:"PlatformAppReportLossPriceOtherWeight,omitempty"`
	// 替换宏 __IS_S__
	PlatformAppIsReportLossPriceExp        int    `json:"PlatformAppIsReportLossPriceExp,omitempty"`
	PlatformAppReportLossPriceExpMinWeight string `json:"PlatformAppReportLossPriceExpMinWeight,omitempty"`
	PlatformAppReportLossPriceExpMaxWeight string `json:"PlatformAppReportLossPriceExpMaxWeight,omitempty"`
	// 替换宏 __IS_C__
	PlatformAppIsReportLossPriceClk        int    `json:"PlatformAppIsReportLossPriceClk,omitempty"`
	PlatformAppReportLossPriceClkMinWeight string `json:"PlatformAppReportLossPriceClkMinWeight,omitempty"`
	PlatformAppReportLossPriceClkMaxWeight string `json:"PlatformAppReportLossPriceClkMaxWeight,omitempty"`

	// H5为空使用枫岚落地页
	PlatformAppIsMaplehazeLandingPage int `json:"PlatformAppIsMaplehazeLandingPage,omitempty"`

	// HuaWei鉴权配置
	PlatformAppPublisherID string `json:"PlatformAppPublisherID,omitempty"`
	PlatformAppKeyID       string `json:"PlatformAppKeyID,omitempty"`
	PlatformAppSignKey     string `json:"PlatformAppSignKey,omitempty"`

	// 替换包配置
	// ip dau限制开关
	PlatformAppIsLimitIPDau int `json:"PlatformAppIsLimitIPDau,omitempty"`
	// 单设备替换最大次数开关
	PlatformAppIsReplaceDIDMaxNum int `json:"PlatformAppIsReplaceDIDMaxNum,omitempty"`
	// 单设备替换最大次数
	PlatformAppMaxNumReplaceDID int `json:"PlatformAppMaxNumReplaceDID,omitempty"`
	// 单设备替换地域跳动最大次数开关, 0 关, 1 开(地域), 2 开(IP)
	PlatformAppIsReplaceDIDDiffRegion int `json:"PlatformAppIsReplaceDIDDiffRegion,omitempty"`
	// 单设备替换地域跳动最大次数
	PlatformAppMaxNumReplaceDIDDiffRegion int `json:"PlatformAppMaxNumReplaceDIDDiffRegion,omitempty"`
	// 单设备单ip替换包功能, 0 关, 1 开
	PlatformAppIsReplaceDIDIP int `json:"PlatformAppIsReplaceDIDIP,omitempty"`
	// 单设备单ip替换包ttl, 单位小时
	PlatformAppReplaceDIDIPTTL int `json:"PlatformAppReplaceDIDIPTTL,omitempty"`
	// 单设备单ip替换包最大数
	PlatformAppMaxNumReplaceDIDIP int `json:"PlatformAppMaxNumReplaceDIDIP,omitempty"`
	// 是否上报虚拟applist
	PlatformAppIsReportVirtualAppList int `json:"PlatformAppIsReportVirtualAppList,omitempty"`
	// 虚拟applist随机包名个数
	PlatformAppVirtualPkgNum string `json:"PlatformAppVirtualPkgNum,omitempty"`
	// 虚拟applist随机包名生效概率
	PlatformAppVirtualPkgWeight string `json:"PlatformAppVirtualPkgWeight,omitempty"`
	// 虚拟applist随机包名库
	PlatformAppVirtualPkgLib string `json:"PlatformAppVirtualPkgLib,omitempty"`
}

// PlatformPosWhiteMediaConfigStu ...
type PlatformPosWhiteMediaConfigStu struct {
	LocalAppID string `json:"local_app_id,omitempty"`
	// 使用weight: 上游广告位限频白名单, 人群包媒体白名单, 忽略点击上报配置
	// 不使用weight: 三方监测白名单, 预期忽略点击上报配置
	Weight int `json:"weight,omitempty"`
	// 使用deeplink: 忽略点击上报配置
	Deeplink string `json:"deeplink,omitempty"`

	// 使用filter_type: 忽略点击上报配置
	// all或者jd
	FilterType string `json:"filter_type,omitempty"`
}

type PlatformPosWhiteMediaConfigSort []PlatformPosWhiteMediaConfigStu

func (s PlatformPosWhiteMediaConfigSort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s PlatformPosWhiteMediaConfigSort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s PlatformPosWhiteMediaConfigSort) Less(i, j int) bool {
	//按字段比较大小,此处是降序排序
	//返回数组中下标为i的数据是否小于下标为j的数据
	return utils.ConvertStringToInt(s[i].LocalAppID) > utils.ConvertStringToInt(s[j].LocalAppID)
}

// PlatformPolicyStu ...
type PlatformPolicyStu struct {
	ID                   string
	PlatformMediaID      string
	PlatformIsActive     int
	PlatformMaxCostType1 int
	PlatformMaxCostType2 int
	PlatformMaxCostType3 int
	PlatformMaxCostType4 int
	PlatformMaxCostType5 int
	PlatformMaxCostType6 int
	PlatformMaxCostType0 int
	PlatformMaxCost1     int
	PlatformMaxCost2     int
	PlatformMaxCost3     int
	PlatformMaxCost4     int
	PlatformMaxCost5     int
	PlatformMaxCost6     int
	PlatformMaxCost0     int
	PlatformBigDataCost  int // 单位: 元
	// 到限制后竞价通过率范围(api预算)(在请求上游填充之后)
	PlatformMinCostWeight int
	PlatformMaxCostWeight int
	// 到限制后请求通过率(在请求上游之前)
	PlatformMaxReqPassWeight int
}

// PlatformAppLimitIPReqRuleStu ...
type PlatformAppLimitIPReqRuleStu struct {
	LimitIPReqMaxRatio    string `json:"limit_ip_req_max_ratio,omitempty"`
	LimitIPReqFilterRatio string `json:"limit_ip_req_filter_ratio,omitempty"`
}
