package models

import (
	"context"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"strconv"
	"strings"
	"sync"
	"time"

	commonKafka "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/kafka"

	"github.com/segmentio/kafka-go"
)

var materialShieldStatisticsKafkaProducer = kafka.Writer{
	Addr:     kafka.TCP(config.KafkaWriterConfig.Brokers...),
	Balancer: &kafka.Hash{},
	Topic:    commonKafka.TOPIC_SSP_MATERIAL_STATISTICS,
	Async:    true, // make the writer asynchronous
}

var (
	materialStatisticsBatchArray      []kafka.Message
	materialStatisticsBatchMutex      sync.Mutex
	materialStatisticsBatchTickerOnce sync.Once
)

const (
	materialShieldStatisticsBatchSize    = 5000
	materialShieldStatisticsBatchTimeout = 3 * time.Second
)

func startMaterialShieldStatisticsBatchTicker() {
	materialStatisticsBatchTickerOnce.Do(func() {
		ticker := time.NewTicker(materialShieldStatisticsBatchTimeout)
		go func() {
			for range ticker.C {
				flushMaterialShieldStatisticsBatch()
			}
		}()
	})
}

func flushMaterialShieldStatisticsBatch() {
	materialStatisticsBatchMutex.Lock()
	batch := materialStatisticsBatchArray
	if len(batch) == 0 {
		materialStatisticsBatchMutex.Unlock()
		return
	}
	materialStatisticsBatchArray = nil
	materialStatisticsBatchMutex.Unlock()

	err := materialShieldStatisticsKafkaProducer.WriteMessages(context.Background(), batch...)
	if err != nil {
		fmt.Printf("Kafka批量写入失败: %v\n", err)
	}
}

func MaterialShieldStatisticsRawDataKafka(ctx context.Context, bigdataUID string, mhReq *MHReq, localPos *LocalPosStu, platformPos *PlatformPosStu, item *MHRespDataItem, blockList []byte, shieldSource int) {
	if mhReq == nil || localPos == nil || platformPos == nil || item == nil {
		fmt.Printf("MaterialShieldStatisticsRawDataKafka received nil pointer, reqid: %s\n", bigdataUID)
		return
	}
	startMaterialShieldStatisticsBatchTicker()

	if len(blockList) == 0 {
		return
	}

	var shieldResult []ShieldResult
	err := json.Unmarshal(blockList, &shieldResult)
	if err != nil {
		fmt.Printf("JSON反序列化失败: %v\n", err)
		return
	}

	materialShieldStatistics := &MaterialStatisticsRawData{
		PosType:      localPos.LocalPosType,
		AppType:      localPos.LocalAppType,
		PPosType:     platformPos.PlatformPosType,
		InteractType: item.InteractType,
		CrtType:      item.CrtType,
		Reqid:        bigdataUID,
		Adid:         item.AdID,
		DemandCrid:   item.DemandCrid,
		AppId:        localPos.LocalAppID,
		PosId:        localPos.LocalPosID,
		PAppId:       platformPos.PlatformAppID,
		PPosId:       platformPos.PlatformPosID,
		CorpId:       strconv.Itoa(platformPos.PlatformAppCorpID),
		Channel:      platformPos.PlatformMediaID,
		PAppType:     platformPos.PlatformAppType,
		Os:           mhReq.Device.Os,
		Ip:           mhReq.Device.IP,
		Manufacturer: mhReq.Device.Manufacturer,
		Model:        mhReq.Device.Model,
		Title:        item.Title,
		Description:  item.Description,
		IconUrl:      item.IconURL,
		AdUrl:        item.AdURL,
		DownloadUrl:  item.DownloadURL,
		LandpageUrl:  item.LandpageURL,
		DeepLink:     item.DeepLink,
		PackageName:  item.PackageName,
		IpCountry:    mhReq.Device.IPCountry,
		IpProvince:   mhReq.Device.IPProvince,
		IpCity:       mhReq.Device.IPCity,
		ShieldSource: shieldSource,
	}

	// 设置视频相关信息
	if item.Video != nil {
		materialShieldStatistics.VideoDuration = item.Video.Duration
		materialShieldStatistics.VideoUrl = item.Video.VideoURL
		materialShieldStatistics.CoverUrl = item.Video.CoverURL
		materialShieldStatistics.Width = item.Video.Width
		materialShieldStatistics.Height = item.Video.Height
	}

	// 设置图片URL
	if len(item.Image) > 0 {
		materialShieldStatistics.ImageUrl = item.Image[0].URL
		materialShieldStatistics.Width = item.Image[0].Width
		materialShieldStatistics.Height = item.Image[0].Height
	}

	for _, shieldItem := range shieldResult {
		switch shieldItem.BlockType {
		case 1:
			materialShieldStatistics.ShieldKeywords = shieldItem.BlockValue
		case 2:
			materialShieldStatistics.ShieldPackageName = shieldItem.BlockValue
		case 3:
			materialShieldStatistics.ShieldAdUrl = shieldItem.BlockValue
		case 4:
			materialShieldStatistics.ShieldMaterialUrl = shieldItem.BlockValue
		case 5:
			materialShieldStatistics.ShieldTime = shieldItem.BlockValue
		case 6:
			materialShieldStatistics.ShieldRegion = shieldItem.BlockValue
		case 7:
			timeAndRegion := strings.Split(shieldItem.BlockValue, "_")
			if len(timeAndRegion) >= 2 {
				materialShieldStatistics.ShieldTime = timeAndRegion[1]
				materialShieldStatistics.ShieldRegion = timeAndRegion[0]
			}
		case 8:
			materialShieldStatistics.ShieldAdType = shieldItem.BlockValue
		case 9:
			materialShieldStatistics.ShieldMonitorUrl = shieldItem.BlockValue
		}
	}

	// 将结构体转换为JSON
	jsonData, err := json.Marshal(materialShieldStatistics)
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}

	// 准备Kafka消息
	message := kafka.Message{
		Key:   []byte(materialShieldStatistics.Adid),
		Value: jsonData,
	}

	materialStatisticsBatchMutex.Lock()
	materialStatisticsBatchArray = append(materialStatisticsBatchArray, message)
	if len(materialStatisticsBatchArray) >= materialShieldStatisticsBatchSize {
		batch := materialStatisticsBatchArray
		materialStatisticsBatchArray = nil
		materialStatisticsBatchMutex.Unlock()
		err := materialShieldStatisticsKafkaProducer.WriteMessages(context.Background(), batch...)
		if err != nil {
			fmt.Printf("Kafka批量写入失败: %v\n", err)
		}
		return
	}
	materialStatisticsBatchMutex.Unlock()
}

func MaterialReplaceStatisticsRawDataKafka(ctx context.Context, bigdataUID string, mhReq *MHReq, localPos *LocalPosStu, platformPos *PlatformPosStu, item *MHRespDataItem, data []byte, replaceSource int) {
	if mhReq == nil || localPos == nil || platformPos == nil || item == nil {
		fmt.Printf("MaterialReplaceStatisticsRawDataKafka received nil pointer, reqid: %s\n", bigdataUID)
		return
	}
	startMaterialShieldStatisticsBatchTicker()

	if len(data) == 0 {
		return
	}

	var replaceResult []ReplaceResult
	err := json.Unmarshal(data, &replaceResult)
	if err != nil {
		fmt.Printf("JSON反序列化失败: %v\n", err)
		return
	}

	// Create a deep copy of the item to avoid race conditions
	itemCopy := *item // Make a copy of the struct, not just the pointer

	// Create a deep copy of nested structures if they exist
	if item.Video != nil {
		videoCopy := *item.Video
		itemCopy.Video = &videoCopy
	}

	if len(item.Image) > 0 {
		imageCopy := make([]MHRespImage, len(item.Image))
		copy(imageCopy, item.Image)
		itemCopy.Image = imageCopy
	}

	materialReplaceStatistics := &MaterialStatisticsRawData{
		PosType:       localPos.LocalPosType,
		AppType:       localPos.LocalAppType,
		PPosType:      platformPos.PlatformPosType,
		InteractType:  itemCopy.InteractType,
		CrtType:       itemCopy.CrtType,
		Reqid:         bigdataUID,
		Adid:          itemCopy.AdID,
		DemandCrid:    itemCopy.DemandCrid,
		SupplyCrid:    itemCopy.SupplyCrid,
		AppId:         localPos.LocalAppID,
		PosId:         localPos.LocalPosID,
		PAppId:        platformPos.PlatformAppID,
		PPosId:        platformPos.PlatformPosID,
		CorpId:        strconv.Itoa(platformPos.PlatformAppCorpID),
		Channel:       platformPos.PlatformMediaID,
		PAppType:      platformPos.PlatformAppType,
		Os:            mhReq.Device.Os,
		Manufacturer:  mhReq.Device.Manufacturer,
		Model:         mhReq.Device.Model,
		Title:         itemCopy.Title,
		Description:   itemCopy.Description,
		IconUrl:       itemCopy.IconURL,
		Ip:            mhReq.Device.IP,
		IpCountry:     mhReq.Device.IPCountry,
		IpProvince:    mhReq.Device.IPProvince,
		IpCity:        mhReq.Device.IPCity,
		ReplaceSource: replaceSource,
	}

	// 设置视频相关信息
	if itemCopy.Video != nil {
		materialReplaceStatistics.VideoDuration = itemCopy.Video.Duration
		materialReplaceStatistics.VideoUrl = itemCopy.Video.VideoURL
		materialReplaceStatistics.CoverUrl = itemCopy.Video.CoverURL
		materialReplaceStatistics.Width = itemCopy.Video.Width
		materialReplaceStatistics.Height = itemCopy.Video.Height
	}

	// 设置图片URL
	if len(itemCopy.Image) > 0 {
		materialReplaceStatistics.ImageUrl = itemCopy.Image[0].URL
		materialReplaceStatistics.Width = itemCopy.Image[0].Width
		materialReplaceStatistics.Height = itemCopy.Image[0].Height
	}

	for _, replaceItem := range replaceResult {
		materialReplaceStatistics.ReplaceRuleId = replaceItem.RuleID
		materialReplaceStatistics.ReplaceRuleName = replaceItem.RuleName
		switch replaceItem.Type {
		case "title":
			materialReplaceStatistics.ReplaceTitle = replaceItem.OriginalValue
		case "description":
			materialReplaceStatistics.ReplaceDescription = replaceItem.OriginalValue
		case "icon":
			materialReplaceStatistics.ReplaceIconUrl = replaceItem.OriginalValue
		case "image":
			if len(replaceItem.OriginalImg) == 0 {
				continue
			}
			materialReplaceStatistics.ReplaceImageUrl = replaceItem.OriginalImg[0].URL
			materialReplaceStatistics.ReplaceWidth = replaceItem.OriginalImg[0].Width
			materialReplaceStatistics.ReplaceHeight = replaceItem.OriginalImg[0].Height
		case "video":
			if len(replaceItem.OriginalVideo.VideoURL) == 0 {
				continue
			}
			materialReplaceStatistics.ReplaceVideoDuration = replaceItem.OriginalVideo.Duration
			materialReplaceStatistics.ReplaceVideoUrl = replaceItem.OriginalVideo.VideoURL
			materialReplaceStatistics.ReplaceCoverUrl = replaceItem.OriginalVideo.CoverURL
			materialReplaceStatistics.ReplaceWidth = replaceItem.OriginalVideo.Width
			materialReplaceStatistics.ReplaceHeight = replaceItem.OriginalVideo.Height
		}
	}

	jsonData, err := json.Marshal(materialReplaceStatistics)
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}

	if len(jsonData) == 0 {
		return
	}
	// 准备Kafka消息
	message := kafka.Message{
		Key:   []byte(materialReplaceStatistics.Adid),
		Value: jsonData,
	}

	materialStatisticsBatchMutex.Lock()
	materialStatisticsBatchArray = append(materialStatisticsBatchArray, message)
	if len(materialStatisticsBatchArray) >= materialShieldStatisticsBatchSize {
		batch := materialStatisticsBatchArray
		materialStatisticsBatchArray = nil
		materialStatisticsBatchMutex.Unlock()
		err := materialShieldStatisticsKafkaProducer.WriteMessages(context.Background(), batch...)
		if err != nil {
			fmt.Printf("Kafka批量写入失败: %v\n", err)
		}
		return
	}
	materialStatisticsBatchMutex.Unlock()
}

type MaterialStatisticsRawData struct {
	PosType              int    `json:"pos_type,omitempty"`
	PPosType             int    `json:"p_pos_type,omitempty"`
	InteractType         int    `json:"interact_type,omitempty"`
	CrtType              int    `json:"crt_type,omitempty"`
	VideoDuration        int    `json:"video_duration,omitempty"`
	Height               int    `json:"height,omitempty"`
	Width                int    `json:"width,omitempty"`
	ShieldSource         int    `json:"shield_source,omitempty"`
	ReplaceSource        int    `json:"replace_source,omitempty"`
	ReplaceVideoDuration int    `json:"replace_video_duration,omitempty"`
	ReplaceHeight        int    `json:"replace_height,omitempty"`
	ReplaceWidth         int    `json:"replace_width,omitempty"`
	Reqid                string `json:"reqid,omitempty"`
	Adid                 string `json:"adid,omitempty"`
	DemandCrid           string `json:"demand_crid,omitempty"`
	SupplyCrid           string `json:"supply_crid,omitempty"`
	AppId                string `json:"app_id,omitempty"`
	PosId                string `json:"pos_id,omitempty"`
	PAppId               string `json:"p_app_id,omitempty"`
	PPosId               string `json:"p_pos_id,omitempty"`
	CorpId               string `json:"corp_id,omitempty"`
	Channel              string `json:"channel,omitempty"`
	PAppType             string `json:"p_app_type,omitempty"`
	AppType              string `json:"app_type,omitempty"`
	Os                   string `json:"os,omitempty"`
	Ip                   string `json:"ip,omitempty"`
	Manufacturer         string `json:"manufacturer,omitempty"`
	Model                string `json:"model,omitempty"`
	Title                string `json:"title,omitempty"`
	Description          string `json:"description,omitempty"`
	IconUrl              string `json:"icon_url,omitempty"`
	ImageUrl             string `json:"image_url,omitempty"`
	CoverUrl             string `json:"cover_url,omitempty"`
	VideoUrl             string `json:"video_url,omitempty"`
	AdUrl                string `json:"ad_url,omitempty"`
	DownloadUrl          string `json:"download_url,omitempty"`
	LandpageUrl          string `json:"landpage_url,omitempty"`
	DeepLink             string `json:"deep_link,omitempty"`
	PackageName          string `json:"package_name,omitempty"`
	IpCountry            string `json:"ip_country,omitempty"`
	IpProvince           string `json:"ip_province,omitempty"`
	IpCity               string `json:"ip_city,omitempty"`
	ShieldKeywords       string `json:"shield_keywords,omitempty"`
	ShieldPackageName    string `json:"shield_package_name,omitempty"`
	ShieldAdUrl          string `json:"shield_ad_url,omitempty"`
	ShieldMaterialUrl    string `json:"shield_material_url,omitempty"`
	ShieldMonitorUrl     string `json:"shield_monitor_url,omitempty"`
	ShieldTime           string `json:"shield_time,omitempty"`
	ShieldRegion         string `json:"shield_region,omitempty"`
	ShieldAdType         string `json:"shield_ad_type,omitempty"`
	ReplaceTitle         string `json:"replace_title,omitempty"`
	ReplaceDescription   string `json:"replace_description,omitempty"`
	ReplaceIconUrl       string `json:"replace_icon_url,omitempty"`
	ReplaceImageUrl      string `json:"replace_image_url,omitempty"`
	ReplaceCoverUrl      string `json:"replace_cover_url,omitempty"`
	ReplaceVideoUrl      string `json:"replace_video_url,omitempty"`
	ReplaceRuleId        string `json:"replace_rule_id,omitempty"`
	ReplaceRuleName      string `json:"replace_rule_name,omitempty"`
}

// ShieldResult 存储屏蔽结果信息
type ShieldResult struct {
	BlockType  int    // 被拉黑的类型
	BlockValue string // 具体被拉黑的值
}

type ReplaceType string

const (
	ReplaceTypeTitle       ReplaceType = "title"
	ReplaceTypeDescription ReplaceType = "description"
	ReplaceTypeIcon        ReplaceType = "icon"
	ReplaceTypeImage       ReplaceType = "image"
	ReplaceTypeVideo       ReplaceType = "video"
)

// ReplaceResult holds information about a replacement.
type ReplaceResult struct {
	Type          ReplaceType `json:"type,omitempty"`
	OriginalValue string      `json:"original_value,omitempty"`
	ReplaceValue  string      `json:"replace_value,omitempty"`
	RuleID        string      `json:"rule_id,omitempty"`
	RuleName      string      `json:"rule_name,omitempty"`
	OriginalImg   []Image     `json:"original_img,omitempty"`
	OriginalVideo Video       `json:"original_video,omitempty"`
}

type Video struct {
	Duration int    `json:"duration,omitempty"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	VideoURL string `json:"video_url,omitempty"`
	CoverURL string `json:"cover_url,omitempty"`
}

type Image struct {
	URL    string `json:"url,omitempty"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
}
