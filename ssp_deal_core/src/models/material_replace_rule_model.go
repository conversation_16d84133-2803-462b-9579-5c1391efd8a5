package models

// MaterialReplace ...
type MaterialReplace struct {
	MaterialReplaceRuleID string                 `db:"material_replace_rule_id" json:"MaterialReplaceRuleID,omitempty"`
	Name                  string                 `db:"name" json:"Name,omitempty"`
	ReplaceKey            string                 `db:"replace_key" json:"ReplaceKey,omitempty"`
	AdUrl                 string                 `db:"ad_url" json:"AdUrl,omitempty"`
	MaterialUrl           string                 `db:"material_url" json:"MaterialUrl,omitempty"`
	ReplaceGroup          []MaterialReplaceGroup `json:"ReplaceGroup,omitempty"`
}

type MaterialReplaceGroup struct {
	ReplaceType      string `db:"replace_type" json:"ReplaceType,omitempty"`
	MaterialType     string `db:"material_type" json:"MaterialType,omitempty"`
	ReplaceValue     string `db:"replace_value" json:"ReplaceValue,omitempty"`
	Width            int    `db:"width" json:"width"`
	Height           int    `db:"height" json:"height"`
	DynamicMaterials int    `json:"dynamic_materials"`
}

type NewMaterialReplace struct {
	Type                    int    `json:"type"`
	Chance                  int    `json:"chance"`
	UnconditionalSwitch     int    `json:"unconditional_switch"`
	MaterialReplaceRuleID   string `json:"material_replace_rule_id"`
	MaterialReplaceRuleName string `json:"name"`
	ReplaceKey              string `json:"replace_key"`
	AdUrl                   string `json:"ad_url"`
	MaterialUrl             string `json:"material_url"`
	PlatformIds             string `json:"platform_ids"`
	AppIds                  string `json:"app_ids"`
	PosIds                  string `json:"pos_ids"`

	Title             []string `json:"title"`
	Desc              []string `json:"desc"`
	Icon              []string `json:"icon"`
	Img               []string `json:"img"`
	ProportionallyImg []string `json:"proportionally_img"`
	ImgW              []string `json:"img_w"`
	ImgH              []string `json:"img_h"`
	Video             []string `json:"video"`
	VideoW            []string `json:"video_w"`
	VideoH            []string `json:"video_h"`
	Cover             []string `json:"cover"`
	CoverW            []string `json:"cover_w"`
	CoverH            []string `json:"cover_h"`

	Group []MaterialReplaceGroup `json:"group"`
}

type NewMaterialReplaceGroup struct {
	MaterialType     int    `json:"material_type"`
	ReplaceType      string `json:"replace_type"`
	ReplaceValue     string `json:"replace_value"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	DynamicMaterials int    `json:"dynamic_materials"`

	Title  []string `json:"title"`
	Desc   []string `json:"desc"`
	Icon   []string `json:"icon"`
	Img    []string `json:"img"`
	ImgW   []string `json:"img_w"`
	ImgH   []string `json:"img_h"`
	Video  []string `json:"video"`
	VideoW []string `json:"video_w"`
	VideoH []string `json:"video_h"`
	Cover  []string `json:"cover"`
	CoverW []string `json:"cover_w"`
	CoverH []string `json:"cover_h"`
}

type NewMaterialReplaceBigCache struct {
	Type                    int    `json:"type"`
	Chance                  int    `json:"chance"`
	UnconditionalSwitch     int    `json:"unconditional_switch"`
	MaterialReplaceRuleID   string `json:"material_replace_rule_id"`
	MaterialReplaceRuleName string `json:"name"`
	ReplaceKey              string `json:"replace_key"`
	AdUrl                   string `json:"ad_url"`
	MaterialUrl             string `json:"material_url"`
	PlatformIds             string `json:"platform_ids"`

	Title             []string `json:"title"`
	Desc              []string `json:"desc"`
	Icon              []string `json:"icon"`
	Img               []string `json:"img"`
	ProportionallyImg []string `json:"proportionally_img"`
	ImgW              []string `json:"img_w"`
	ImgH              []string `json:"img_h"`
	Video             []string `json:"video"`
	VideoW            []string `json:"video_w"`
	VideoH            []string `json:"video_h"`
	Cover             []string `json:"cover"`
	CoverW            []string `json:"cover_w"`
	CoverH            []string `json:"cover_h"`

	Group []MaterialReplaceGroup `json:"group"`
}

type MaterialReplaceAdContentModel struct {
	IsActive    int                                  `json:"is_active" db:"is_active"`
	Chance      int                                  `json:"chance" db:"chance"`
	GroupChance int                                  `json:"group_chance"`
	RuleID      string                               `json:"rule_id" db:"rule_id"`
	PlatformIds string                               `json:"platform_ids" db:"platform_ids"`
	LocalAppIDs string                               `json:"app_ids" db:"app_ids"`
	LocalPosIDs string                               `json:"pos_ids" db:"pos_ids"`
	Group       []MaterialReplaceAdContentGroupModel `json:"group"`
}

type MaterialReplaceAdContentGroupModel struct {
	Chance      int    `json:"chance" db:"chance"`
	Width       int    `json:"width" db:"width"`
	Height      int    `json:"height" db:"height"`
	Duration    int    `json:"duration" db:"duration"`
	H5Url       string `json:"h5_url" db:"h5_url"`
	DownloadUrl string `json:"download_url" db:"download_url"`
	DpUrl       string `json:"dp_url" db:"dp_url"`
	Title       string `json:"title" db:"title"`
	Description string `json:"description" db:"description"`
	ImageUrl    string `json:"image_url" db:"image_url"`
	VideoUrl    string `json:"video_url" db:"video_url"`
	CoverUrl    string `json:"cover_url" db:"cover_url"`
	ReplaceType string `json:"replace_type" db:"replace_type"`
}

type NewMaterialReplaceType struct {
	RuleID               string                        `json:"rule_id"`
	RuleName             string                        `json:"rule_name"`
	ReplaceKey           string                        `json:"replace_key"`
	AdUrl                string                        `json:"ad_url"`
	MaterialUrl          string                        `json:"material_url"`
	PlatformIds          string                        `json:"platform_ids"`
	AppIds               string                        `json:"app_ids"`
	PosIds               string                        `json:"pos_ids"`
	MaterialReplaceGroup []NewMaterialReplaceTypeGroup `json:"replace_group,omitempty"`
}

type NewMaterialReplaceTypeGroup struct {
	MaterialType     int    `json:"material_type"`
	Width            int    `json:"width"`
	Height           int    `json:"height"`
	Duration         int    `json:"duration"`
	DynamicMaterials int    `json:"dynamic_materials"`
	ReplaceType      string `json:"replace_type"`
	ImgUrl           string `json:"img_url"`
	VideoUrl         string `json:"video_url"`
	CoverUrl         string `json:"cover_url"`
}
