package main

import (
	"context"
	"log"
	"mh_proxy/utilities"
	"net"
	"testing"

	commonKafka "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/kafka"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"github.com/segmentio/kafka-go"
)

func TestKafkaGoWriter(t *testing.T) {
	ctx := context.Background()

	// s := getServiceForTest(ctx)
	// s.producer.Topic = commonKafka.TOPIC_SSP_CRASH_REPORT_DETAIL

	dialer := databases.NewKafkaSASLSSLDialer(
		utilities.MHCaPath,
		utilities.KafkaUserName,
		utilities.KafkaPassword,
	)

	producer := kafka.Writer{
		Addr: kafka.TCP(
			"alikafka-serverless-cn-fhh3zns0s04-1000.alikafka.aliyuncs.com:9093",
			"alikafka-serverless-cn-fhh3zns0s04-2000.alikafka.aliyuncs.com:9093",
			"alikafka-serverless-cn-fhh3zns0s04-3000.alikafka.aliyuncs.com:9093",
		),
		Balancer: &kafka.Hash{},
		Transport: &kafka.Transport{
			Dial: (&net.Dialer{}).DialContext,
			SASL: dialer.SASLMechanism,
			TLS:  dialer.TLS,
		},
		Topic: commonKafka.TOPIC_TEST,
	}

	err := producer.WriteMessages(
		ctx,
		kafka.Message{Value: []byte("test1!")},
		kafka.Message{Value: []byte("test2!")},
		kafka.Message{Value: []byte("test3!")},
	)
	if err != nil {
		log.Fatal("failed to write messages:", err)
	}
}
