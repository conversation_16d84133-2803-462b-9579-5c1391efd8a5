package api

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ApiPriceWin ...
func ApiPriceWin(c *gin.Context) {
	// fmt.Println("api win notice")
	encodeApiWinPrice := c.Query("price")
	encodeApiLogEcpm := c.Query("apilogep")
	encodeSDKLogEcpm := c.Query("sdklogep")

	if len(encodeApiWinPrice) == 0 || len(encodeApiLogEcpm) == 0 {
		fmt.Println("nothing to do")
		return
	}

	// 取app_id, pos_id, price_token
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))
	localPosInfo := models.GetLocalPosInfo(c, log.Get("pos_id"), log.Get("app_id"))
	if localPosInfo == nil {
		fmt.Println("local pos info is nil")
		return
	}

	// 解密ecpm
	var decodeLogEcpm []byte
	// 解密api win price
	var decodeWinPrice []byte
	// sdk跑api价格解密使用 1234567887654321
	// api跑api价格解密使用 localPosInfo.LocalAppPriceToken
	if localPosInfo.LocalAppType == "1" {
		if encodeSDKLogEcpm == "__PPLOGEP__" {
			return
		}

		// 解密sdk win price
		decryptBase64, _ := base64.StdEncoding.DecodeString(encodeApiWinPrice)
		decodeWinPrice = utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
		fmt.Println("sdk -> api encode_win_price_hex:", string(decodeWinPrice))

		// 解密sdk ecpm
		decryptSDKLogEcpmBase64, _ := base64.StdEncoding.DecodeString(encodeSDKLogEcpm)
		decodeLogEcpm = utils.AesECBDecrypt(decryptSDKLogEcpmBase64, []byte(config.EncryptKEY))
	} else {
		if len(localPosInfo.LocalAppPriceToken) == 0 {
			return
		}

		// 解密api win price
		encodePriceValue := utils.HexDecode(encodeApiWinPrice)
		decodeWinPrice = utils.AesECBDecrypt(encodePriceValue, []byte(localPosInfo.LocalAppPriceToken))
		// fmt.Println("api -> api encode_win_price_hex:", string(decodeWinPrice))

		// 解密api ecpm
		decryptApiLogEcpmBase64, _ := base64.StdEncoding.DecodeString(encodeApiLogEcpm)
		decodeLogEcpm = utils.AesECBDecrypt(decryptApiLogEcpmBase64, []byte(config.EncryptKEY))
	}

	bigdataUID := log.Get("uid")

	// save holo
	var bigdataPriceItem models.BigdataPriceStu
	bigdataPriceItem.UID = bigdataUID
	bigdataPriceItem.TagID = ""
	bigdataPriceItem.LocalAppID = log.Get("app_id")
	bigdataPriceItem.LocalPosID = log.Get("pos_id")
	bigdataPriceItem.PlatformAppID = log.Get("p_app_id")
	bigdataPriceItem.PlatformPosID = log.Get("p_pos_id")
	bigdataPriceItem.BidPrice = utils.ConvertStringToInt(string(decodeLogEcpm))
	bigdataPriceItem.WinPrice = utils.ConvertStringToInt(string(decodeWinPrice))
	bigdataPriceItem.AdID = log.Get("adid")

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("api win price panic:", err)
			}
		}()

		// api竞胜存holo
		models.BigDataWinPriceToHolo(c, &bigdataPriceItem)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok"
	c.PureJSON(200, resp)
}

// ApiPriceLoss ...
func ApiPriceLoss(c *gin.Context) {
	fmt.Println("api loss notice")
	// 七猫读取auction, 1 七猫竞价成功, 直接返回
	auctionQuery := c.Query("auction")
	if auctionQuery == "1" {
		return
	}

	// 取app_id, pos_id, price_token
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}
	// logDecode, _ := utils.DecodeString(logQuery)
	// log, _ := url.ParseQuery(string(logDecode))

	demandLossLinks := c.Query("demand_loss_links")
	if len(demandLossLinks) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("loss panic:", err)
				}
			}()

			decryptBase64, _ := base64.StdEncoding.DecodeString(demandLossLinks)
			tmpDemandLossLinks := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))

			var tmpLossArray []string
			json.Unmarshal([]byte(tmpDemandLossLinks), &tmpLossArray)
			for _, tmpLink := range tmpLossArray {
				if strings.HasPrefix(tmpLink, "http") {
					// http请求
					client := &http.Client{Timeout: 1000 * time.Millisecond}
					requestGet, _ := http.NewRequest("GET", tmpLink, nil)

					requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

					resp, err := client.Do(requestGet)
					if err != nil {
						fmt.Println("get request failed, err:", err.Error())
						return
					}
					defer resp.Body.Close()
				} else {
					client := &http.Client{Timeout: 1000 * time.Millisecond}

					// send req
					requestGet, _ := http.NewRequest("POST", "https://www.pangolin-dsp-toutiao.com/api/common/ads/failreason", bytes.NewReader([]byte(tmpLink)))

					requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

					resp, err := client.Do(requestGet)
					if err != nil {
						// fmt.Printf("get request failed, err:[%s]", err.Error())
						return
					}
					defer resp.Body.Close()
				}

				// if log.Get("p_app_id") == "5668884" || log.Get("p_app_id") == "5668885" {
				// 	go models.BigDataHoloDebugJson2(log.Get("uid")+"&demand_loss_url", string(tmpLink),
				// 		log.Get("app_id"), log.Get("pos_id"), log.Get("p_app_id"), log.Get("p_pos_id"))
				// }
				// go models.BigDataHoloDebugJson2(log.Get("uid")+"&demand_loss_url", string(tmpLink),
				// 	log.Get("app_id"), log.Get("pos_id"), log.Get("p_app_id"), log.Get("p_pos_id"))
			}
		}()
	}

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok"
	c.PureJSON(200, resp)
}

// SdkPriceWin ...
func SdkPriceWin(c *gin.Context) {
	fmt.Println("sdk win notice")

	// 取app_id, pos_id, price_token
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	bigdataUID := log.Get("uid")

	sdkEcpmQuery := c.Query("sdklogep")
	sdkWinPriceQuery := c.Query("sdkprice")
	if len(sdkEcpmQuery) == 0 || len(sdkWinPriceQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}

	if sdkEcpmQuery == "__PPLOGEP__" || sdkWinPriceQuery == "__AUCTION_PRICE__" {
		fmt.Println("nothing to do")
		return
	}

	// win ecpm
	sdkEcpmDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkEcpmQuery)
	sdkEcpmDecrypt := utils.AesECBDecrypt(sdkEcpmDecryptBase64, []byte(config.EncryptKEY))

	// win price
	sdkWinPriceDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkWinPriceQuery)
	sdkWinPriceDecrypt := utils.AesECBDecrypt(sdkWinPriceDecryptBase64, []byte(config.EncryptKEY))

	// save holo
	var bigdataPriceItem models.BigdataPriceStu
	bigdataPriceItem.UID = bigdataUID
	bigdataPriceItem.TagID = ""
	bigdataPriceItem.LocalAppID = log.Get("app_id")
	bigdataPriceItem.LocalPosID = log.Get("pos_id")
	bigdataPriceItem.PlatformAppID = c.Query("pappid")
	bigdataPriceItem.PlatformPosID = c.Query("pposid")
	bigdataPriceItem.BidPrice = utils.ConvertStringToInt(string(sdkEcpmDecrypt))
	bigdataPriceItem.WinPrice = utils.ConvertStringToInt(string(sdkWinPriceDecrypt))
	bigdataPriceItem.AdID = log.Get("adid")

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("sdk win price panic:", err)
			}
		}()

		// sdk竞胜存holo
		models.BigDataWinPriceToHolo(c, &bigdataPriceItem)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok"
	c.PureJSON(200, resp)
}

// SdkPriceLoss ...
func SdkPriceLoss(c *gin.Context) {
	// fmt.Println("sdk loss notice")

	// 取app_id, pos_id, price_token
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	bigdataUID := log.Get("uid")

	sdkEcpmQuery := c.Query("sdklogep")
	sdkWinPriceQuery := c.Query("sdkprice")
	if len(sdkEcpmQuery) == 0 || len(sdkWinPriceQuery) == 0 {
		fmt.Println("nothing to do")
		return
	}

	if sdkEcpmQuery == "__PPLOGEP__" || sdkWinPriceQuery == "__AUCTION_PRICE__" {
		fmt.Println("nothing to do")
		return
	}

	// loss ecpm
	sdkEcpmDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkEcpmQuery)
	sdkEcpmDecrypt := utils.AesECBDecrypt(sdkEcpmDecryptBase64, []byte(config.EncryptKEY))

	// loss price
	sdkLossPriceDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkWinPriceQuery)
	sdkLossPriceDecrypt := utils.AesECBDecrypt(sdkLossPriceDecryptBase64, []byte(config.EncryptKEY))

	// fmt.Println("sdk loss notice:", bigdataUID,
	// 	log.Get("app_id"), log.Get("pos_id"),
	// 	c.Query("pappid"), c.Query("pposid"),
	// 	utils.ConvertStringToInt(string(sdkEcpmDecrypt)),
	// 	utils.ConvertStringToInt(string(sdkLossPriceDecrypt)))

	// save holo
	var bigdataPriceItem models.BigdataPriceStu
	bigdataPriceItem.UID = bigdataUID
	bigdataPriceItem.TagID = ""
	bigdataPriceItem.LocalAppID = log.Get("app_id")
	bigdataPriceItem.LocalPosID = log.Get("pos_id")
	bigdataPriceItem.PlatformAppID = c.Query("pappid")
	bigdataPriceItem.PlatformPosID = c.Query("pposid")
	bigdataPriceItem.BidPrice = utils.ConvertStringToInt(string(sdkEcpmDecrypt))
	bigdataPriceItem.LossPrice = utils.ConvertStringToInt(string(sdkLossPriceDecrypt))
	bigdataPriceItem.AdID = log.Get("adid")

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("sdk loss price panic:", err)
			}
		}()

		// sdk竞败存holo
		models.BigDataLossPriceToHolo(c, &bigdataPriceItem)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok"
	c.PureJSON(200, resp)
}
