package api

import (
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/proto"
	"math/rand"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/pb/iqiyi_down"
	"mh_proxy/pb/mucang"
	"mh_proxy/rtb"
	"mh_proxy/rtb/rtb_2345"
	rtb_three60 "mh_proxy/rtb/rtb_360"
	"mh_proxy/rtb/rtb_bayes"
	"mh_proxy/rtb/rtb_bes"
	"mh_proxy/rtb/rtb_blued"
	"mh_proxy/rtb/rtb_dianzhong"
	"mh_proxy/rtb/rtb_fengfei"
	"mh_proxy/rtb/rtb_honor"
	"mh_proxy/rtb/rtb_hupuV2"
	"mh_proxy/rtb/rtb_ifeng"
	"mh_proxy/rtb/rtb_jxedt"
	"mh_proxy/rtb/rtb_kuaishou"
	"mh_proxy/rtb/rtb_maimai"
	"mh_proxy/rtb/rtb_meetyou"
	"mh_proxy/rtb/rtb_mgtv"
	"mh_proxy/rtb/rtb_moji"
	"mh_proxy/rtb/rtb_mucang_freeze"
	"mh_proxy/rtb/rtb_netease"
	"mh_proxy/rtb/rtb_qimao"
	"mh_proxy/rtb/rtb_syxj"
	"mh_proxy/rtb/rtb_topon"
	"mh_proxy/rtb/rtb_uc"
	"mh_proxy/rtb/rtb_uc_audit"
	"mh_proxy/rtb/rtb_vivo"
	"mh_proxy/rtb/rtb_yoyo"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"
)

// RtbPostRequest http://localhost:8081/rtb/request?channel=3
func RtbPostRequest(c *gin.Context) {
	channel := c.Query("channel")
	beginTime := utils.GetCurrentMilliSecond()
	label := ""

	switch channel {
	case "1":
		// sogou
		// fmt.Println("sogou, channel: " + channel)
	case "2":
		// sigmob
		label = "sigmob"

		// fmt.Println("sigmob, channel: " + channel)
		sigmobResp, code := rtb.HandleBySigmob(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, sigmobResp)
	case "3":
		// zuiyou json
	case "5":
		// zuoyebang
		label = "zuoyebang"
	case "6":
		// qingting
	case "7":
		// zuiyou
		label = "zuiyou"

		zuiyouResp := rtb.HandleByZuiyou(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(200, zuiyouResp)
	case "8":
		// youku
	case "9":
		// hupu
		label = "hupu"

		// fmt.Println("hupu, channel: " + channel)
		hupuResp, code := rtb.HandleByHupu(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, hupuResp)
	case "10":
		// xmly
		label = "xmly"

		// fmt.Println("xmly, channel: " + channel)

		xmlyResp := rtb.HandleByXmly(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(200, xmlyResp)
	case "11":
		// zhiboba
		label = "zhiboba"

		zhibobaResp, code := rtb.HandleByZhiboba(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, zhibobaResp)
	case "12":
		// zhihu
		label = "zhihu"

		zhihuResp := rtb.HandleByZhihu(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(200, zhihuResp)
	case "13":
		// wps
	case "14":
		// netease 163
		label = "163"

		neteaseResp, code := rtb_netease.HandleByNetease(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, neteaseResp)
	case "15":
		// mango alliance
	case "16":
		// migu
	case "17":
		// youdao
		label = "youdao"

		youdaoResp := rtb.HandleByYoudao(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(200, youdaoResp)
	case "18":
		// mango
	case "19":
		// 2345
		label = "2345"
		resp, code := rtb_2345.HandleBy2345(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "20":
		// xiaoniu
	case "21":
		// tanx
		label = "tanx"

		// protobuf version
		tanxResp := rtb.HandleByTanxProtobuf(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(200, tanxResp)
	case "22":
		// tadu
	case "23":
		// meishu
	case "24":
		// chelaile
	case "25":
		// huangyou
	case "26":
		// zhangyue
		label = "zhangyue"

		zhangyueResp, code := rtb.HandleByZhangyue(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, zhangyueResp)
	case "27":
		// meitu
	case "28":
		// zhongqingkandian
	case "29":
		// wifi
		label = "wifi"

		wifiResp := rtb.HandleByWifi(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(200, wifiResp)
	case "30":
		// yidian
	case "31":
		// fenghuang
		label = "fenghuang"

		fenghuangResp, code := rtb.HandleByFengHuang(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(code, fenghuangResp)
	case "32":
		// camscanner
		label = "camscanner"

		camscannerResp, code := rtb.HandleByCamScanner(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, camscannerResp)
	case "33":
		// yoloho
	case "34":
		// qutoutiao
	case "35":
		// iqiyi
		label = "iqiyi"

		iqiyiResp, code := rtb.HandleByIQiYi(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.ProtoBuf(code, iqiyiResp)
	case "36":
		// netease music
	case "37":
		// zhihu
		label = "zhihu"

		zhihuResp := rtb.HandleByZhihu(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(200, zhihuResp)
	case "38":
		// fancy music
	case "39":
		// xmly v2
		label = "xmly_v2"

		// fmt.Println("xmly_v2, channel: " + channel)
		xmlyResp, rtbConfig := rtb.HandleByXmlyV2(c, channel)
		DebugTimeOutWithConfig(label, beginTime, rtbConfig)

		c.ProtoBuf(200, xmlyResp)
	case "40":
		// zhiboba
		label = "zhiboba"

		zhibobaResp, code := rtb.HandleByZhiboba(c, channel)

		DebugTimeOut(label, beginTime, "")

		c.PureJSON(code, zhibobaResp)

	case "41":
		// 驾考宝典
		label = "mucang"
		resp, code := rtb_mucang_freeze.HandleByMucang(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "42":
		// 七猫
		label = "qimao"
		resp := rtb_qimao.HandleByQimao(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(200, resp)
	case "43":
		// 驾校一点通
		label = "jxedt"
		resp := rtb_jxedt.HandleByJxedt(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(200, resp)
	case "44":
		// uc
		label = "uc"
		ucResp, code := rtb_uc.HandleByUc(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ucResp)
	case "45":
		// 凤凰
		label = "ifeng"
		ifengResp, code := rtb_ifeng.HandleByFancy(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ifengResp)
	case "46":
		// uc_audit
		label = "uc_audit"
		ucResp, code := rtb_uc_audit.HandleByUcAudit(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ucResp)
	case "47":
		// 脉脉
		label = "maimai"
		ucResp, code := rtb_maimai.HandleByMaimai(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ucResp)
	case "48":
		// topon
		label = "topon"
		ucResp, code := rtb_topon.HandleByTopon(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ucResp)
	case "49", "52", "53":
		// 360
		label = "360"
		ucResp, code := rtb_three60.HandleByThree60(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, ucResp)
	case "50":
		// hupuV2
		label = "hupuV2"
		hupuResp, code := rtb_hupuV2.HandleByHupuV2(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, hupuResp)
	case "51":
		// 美柚
		label = "meetyou"
		hupuResp, code := rtb_meetyou.HandleByMeetyou(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, hupuResp)
	case "54", "998", "999":
		// ks
		label = "kuaishou"
		resp, code := rtb_kuaishou.HandleKuaishou(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "55":
		// 水印相机
		label = "syxj"
		syxjResp, code := rtb_syxj.HandleBySyxj(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, syxjResp)
	case "56":
		// 优优
		label = "yoyo"
		resp, code := rtb_yoyo.HandleByYoYo(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, resp)
	case "57":
		// 芒果TV
		label = "mgtv"
		resp, code := rtb_mgtv.HandleByMgTv(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, resp)
	case "58":
		// 凤飞
		label = "fengfei"
		resp, code := rtb_fengfei.HandleFengfei(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "59":
		// bes
		label = "bes"
		resp, code := rtb_bes.HandleByBes(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "60":
		// vivo
		label = "vivo"
		resp, code := rtb_vivo.HandleVivo(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)
	case "61":
		// 点众
		label = "dianzhong"
		resp, code := rtb_dianzhong.HandleDianZhong(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, resp)
	case "62":
		// blued
		label = "blued"
		resp, code := rtb_blued.HandleByBlued(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, resp)
	case "63":
		// 倍业科技
		label = "bayes"
		resp, code := rtb_bayes.HandleByBayes(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.PureJSON(code, resp)
	case "64":
		// honor
		label = "honor"
		resp, code := rtb_honor.HandleHonor(c, channel)
		DebugTimeOut(label, beginTime, "")
		c.ProtoBuf(code, resp)

	default:
		fmt.Println("empty channel post: " + channel)
	}

}

// DebugTimeOut
func DebugTimeOut(label string, beginTime int64, retMsg string) {
	debugTimeoutRandomValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DEBUG_TIMEOUT_RANDOM_KEY)
	if cacheError != nil {
		return
	}

	debugTimeoutMaxValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DEBUG_TIMEOUT_MAX_KEY)
	if cacheError != nil {
		return
	}

	timeoutRandom := utils.ConvertStringToInt(string(debugTimeoutRandomValue))
	timeoutMaxValue := utils.ConvertStringToInt(string(debugTimeoutMaxValue))

	if timeoutRandom <= 0 || timeoutMaxValue <= 0 {
		return
	}

	if int(utils.GetCurrentMilliSecond()-beginTime) > timeoutMaxValue && rand.Intn(1000) < timeoutRandom {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("timeout panic:", err)
				}
			}()

			models.GoBigDataHoloDebugTimeout(label, "", "", "", retMsg, int(utils.GetCurrentMilliSecond()-beginTime))
		}()
	}
}

// DebugTimeOutWithConfig
func DebugTimeOutWithConfig(label string, beginTime int64, rtbConfig *models.RtbConfigByTagIDStu) {
	debugTimeoutRandomValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DEBUG_TIMEOUT_RANDOM_KEY)
	if cacheError != nil {
		return
	}

	debugTimeoutMaxValue, cacheError := db.GlbBigCacheMinute.Get(rediskeys.ADX_SSP_DEBUG_TIMEOUT_MAX_KEY)
	if cacheError != nil {
		return
	}

	timeoutRandom := utils.ConvertStringToInt(string(debugTimeoutRandomValue))
	timeoutMaxValue := utils.ConvertStringToInt(string(debugTimeoutMaxValue))

	if timeoutRandom <= 0 || timeoutMaxValue <= 0 {
		return
	}

	if int(utils.GetCurrentMilliSecond()-beginTime) > timeoutMaxValue && rand.Intn(1000) < timeoutRandom {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("timeout panic:", err)
				}
			}()

			if rtbConfig == nil {
				models.GoBigDataHoloDebugTimeout(label, "", "", "", "", int(utils.GetCurrentMilliSecond()-beginTime))
			} else {
				models.GoBigDataHoloDebugTimeout(label, rtbConfig.LocalAppID, rtbConfig.LocalPosID, "", "", int(utils.GetCurrentMilliSecond()-beginTime))
			}
		}()
	}
}

// RtbGetRequest moji
func RtbGetRequest(c *gin.Context) {
	channel := c.Query("channel")

	switch channel {
	case "4":
		// moji
		mojiResp, _ := rtb_moji.HandleByMoji(c, channel)
		c.PureJSON(200, mojiResp)
		return
	default:
		fmt.Println("empty channel get: " + channel)
	}

	// fmt.Println("rtb return")

	// c.JSON(200, gin.H{
	// 	"message": "pong v1 1",
	// })
}

// RtbPrice ...
// /rtb/price?uid=5acd5c5d-f65e-432a-a901-4d89985a40f5&tagid=zuiyoudsp0101&bp=120&channel=7&price=YWEzNzhkZjU2YTk2NTMwNTOeP7gggS1x5aNbzQ==
func RtbPrice(c *gin.Context) {
	// fmt.Println("rtb price")

	channel := c.Query("channel")

	winPrice := 0

	switch channel {
	case "1":
		// sogou
	case "2":
		// sigmob
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "3":
		// zuiyou json
	case "4":
		// moji
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "5":
		// zuoyebang
	case "6":
		// qingting
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "7":
		// zuiyou
		// fmt.Println("zuiyou pb, channel: " + channel)

		price := c.Query("price")

		EKey := "dc31f9c99fd64b724ad58628f1cbcdc17571ddcd"
		IKey := "48fdef1455de9f986dd280ce1fd910b9e4f7b13c"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "8":
		// youku
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "10":
		// xmly
		price := c.Query("price")
		if price == "__SETTLE_PRICE__" {
			return
		}

		xmlyKey := "b97bbe1d8a904eb1"
		tmpPrice, _ := utils.Base64URLDecode(price)
		decodePrice := utils.AesECBNoPaddingDecrypt([]byte(tmpPrice), []byte(xmlyKey))

		// 去空格
		decodeDestPrice := string(decodePrice)
		decodeDestPrice = strings.Replace(decodeDestPrice, " ", "", -1)

		winPrice = utils.ConvertStringToInt(decodeDestPrice)
	case "11":
		// zhiboba

		price := c.Query("price")
		// fmt.Println("zhiboba pb, price: " + price)
		EKey := "4EhmoHEBioqksJhPjPcaHDL5rnlEwhAK"
		IKey := "k1BoJu5fm4vEBBrnxNxDjcMnEq9dnQJ9"
		decodePrice := rtb.DecryptZhibobaPrice(price, EKey, IKey)

		winPrice = decodePrice
	case "12":
		// zhihu

		price := c.Query("price")

		EKey := "OTV4SW3v0svUPJ1S"
		IKey := "qrtEPt43BETh6S6Y"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "13":
		// wps

		price := c.Query("price")

		EKey := "kingsoft"
		IKey := "adx"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice / 1000000)
	case "14":
		// netease 163
		price := c.Query("price")

		token := "OGEzZGQ3N2I3NDMy"
		iv := "MDE3NDMyZWUyMWQx"

		tmpPrice := strings.Replace(price, "%3D%3D", "==", -1)
		s1, _ := base64.StdEncoding.DecodeString(tmpPrice)
		decodePrice, _ := utils.AesCBCDecryptWithIV(s1, []byte(token), []byte(iv))

		winPrice = utils.ConvertStringToInt(string(decodePrice))
	case "17":
		// youdao
		price := c.Query("price")

		youdaoEKey := "0362b71c166bf8ef0938b32e98f02d45bdad24144475888d2732b68f83941da0"
		youdaoIKey := "fcb578c7fe968a417788da3b22cd83a7fac9b6368ebdcab251cc9972458069c4"
		youdaoDecodeEKey, _ := hex.DecodeString(youdaoEKey)
		youdaoDecodeIKey, _ := hex.DecodeString(youdaoIKey)
		decodePrice, _ := utils.DecryptPrice(price, string(youdaoDecodeEKey), string(youdaoDecodeIKey))

		winPrice = int(decodePrice)
	case "19":
		// 2345
		price := c.Query("price")

		EKey := "9a83c25c527265b706b16f56ac6a70ff"
		IKey := "74386d13165f5d61052fc093421f12e4"
		decodePrice, _ := utils.Decrypt2345Price(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "20":
		// xiaoniu
		price := c.Query("price")

		xiaoniuKey := "68b10c9cc519934c9066ecff2db3264a"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(xiaoniuKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "21":
		// tanx

		price := c.Query("price")
		bidid := c.Query("bidid")

		encodePrice, _ := base64.RawURLEncoding.DecodeString(price)
		token, _ := hex.DecodeString("1eea9f481cff464a83ab252ea9107ec0")
		decodePrice := utils.AesECBDecrypt([]byte(encodePrice), token)

		hexPrice := hex.EncodeToString(decodePrice)
		// fmt.Println(hexPrice)
		hexPrice1 := strings.Replace(hexPrice, bidid, "", -1)

		// 将字符串转换为整数
		num, err := strconv.ParseInt(hexPrice1, 16, 32)
		if err != nil {
			fmt.Println("tanx price failed:", err)
			return
		}

		// 将整数转换为字符串
		price = strconv.Itoa(int(num))

		winPrice = utils.ConvertStringToInt(price)

	case "22":
		// tadu
		price := c.Query("price")

		taduKey := "2b27b3c7641942c0110ba275ecbd83a1"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(taduKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "23":
		// meishu
		price := c.Query("price")

		meishuKey := "68b10c9cc519934c9066ecff2db3264a"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(meishuKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "24":
		// chelaile
		price := c.Query("price")

		chelaileKey := "f77f93f5b049f8c0dc235e3eb74ecc79"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(chelaileKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "25":
		// huangyou
		price := c.Query("price")

		huangyouKey := "52078a49aafce5ac890970a458a0509b"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(huangyouKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt

	case "26":
		// zhangyue
		price := c.Query("price")

		zhangyueKey := "8kAHpjhwaOw6Q8APcCOTaHrWPXn15S5g"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(zhangyueKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "27":
		// meitu

		price := c.Query("price")

		EKey := "ce7a8033fc93417e88dddabf34cc703f"
		IKey := "be980f628658b114d500a322eb222dec"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "28":
		// zhongqingkandian
		price := c.Query("price")

		zhongqingkandianKey := "09106e1dc218d842a34554f902af4faf"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(zhongqingkandianKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "29":
		// wifi
		price := c.Query("price")

		wifiKey := "oMq_rc1fuEbtXgBOkKlIxyzNFYd2Js0D-Tv8495A6iheCpWS3QGUamwVjRH7nLPZ"
		en := base64.NewEncoding(wifiKey)
		n := en.WithPadding(base64.NoPadding).DecodedLen(len(price))
		dst := make([]byte, n)
		en.WithPadding(base64.NoPadding).Decode(dst, []byte(price))
		// fmt.Println("priceEncode: " + string(dst))

		tmpURLValues, _ := url.ParseQuery(string(dst))
		decodePrice := utils.ConvertStringToInt(tmpURLValues.Get("cpm"))

		winPrice = decodePrice

	case "30":
		// yidian
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "31":
		// fenghuang
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)

	case "32":
		// camscanner
		price := c.Query("price")

		priceFloat := utils.ConvertStringToFloat(price)
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "33":
		// yoloho
		price := c.Query("price")

		yolohoKey := "8kAHpjhwaOw6Q8APcCOTaHrWPXn15S5g"
		s, _ := url.QueryUnescape(price)
		s1, _ := base64.RawURLEncoding.DecodeString(s)

		decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(yolohoKey))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "34":
		// qutoutiao
		price := c.Query("price")

		// qutoutiaoKey := "2y8cy8xvncd43cr0"
		// s, _ := url.QueryUnescape(price)
		// s1, _ := hex.DecodeString(s)

		// decodePrice := utils.AesCBCPKCS5Decrypt(s1, []byte(qutoutiaoKey))

		winPrice = utils.ConvertStringToInt(price)
	case "35":
		// iqiyi
		price := c.Query("price")

		fmt.Println("iqiyi price:", price)
		price = strings.Replace(price, "!", "=", -1)

		b64 := base64.NewEncoding("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-")
		priceSettle, _ := b64.DecodeString(price)

		iqiyiSettlement := &iqiyi_down.Settlement{}
		err := proto.Unmarshal(priceSettle, iqiyiSettlement)
		if err != nil {
			fmt.Println(err)
			return
		}

		// token
		token, _ := hex.DecodeString("60ec357c4202309269aa48478c97a214")
		decodePrice := utils.AesECBDecrypt(iqiyiSettlement.Price, token)

		winPrice = utils.ConvertStringToInt(string(decodePrice))
	case "37":
		// zhihu

		price := c.Query("price")
		// fmt.Println("zhihu pb, price: " + price)

		EKey := "gCh2UvwEg337dYgk"
		IKey := "0c8BQcZzVDeYm3DK"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "38":
		// fancy

		price := c.Query("price")

		EKey := "KDE3P9ObsXTqvpnwkXTldBHyrzVFcXPm"
		IKey := "bUfrSuIBHcr4IVpts0ULAbAsRWxutDWL"
		decodePrice, _ := utils.DecryptPrice(price, EKey, IKey)

		winPrice = int(decodePrice)
	case "39":
		// xmly v2
		price := c.Query("price")
		if price == "__SETTLE_PRICE__" {
			return
		}

		xmlyKey := "de8d074a58304ff4"
		tmpPrice, _ := utils.Base64URLDecode(price)
		decodePrice := utils.AesECBNoPaddingDecrypt([]byte(tmpPrice), []byte(xmlyKey))

		// 去空格
		decodeDestPrice := string(decodePrice)
		decodeDestPrice = strings.Replace(decodeDestPrice, " ", "", -1)

		winPrice = utils.ConvertStringToInt(decodeDestPrice)
	case "40":
		// zhiboba
		price := c.Query("price")
		// fmt.Println("zhiboba pb, price: " + price)
		EKey := "4EhmoHEBioqksJhPjPcaHDL5rnlEwhAK"
		IKey := "k1BoJu5fm4vEBBrnxNxDjcMnEq9dnQJ9"
		decodePrice := rtb.DecryptZhibobaPrice(price, EKey, IKey)

		winPrice = decodePrice

	case "41":
		// 驾考宝典
		price := c.Query("price")

		key := "mc5120aba7dd8880"
		tmpPrice, _ := utils.Base64URLDecode(price)
		decodePrice := utils.AesCBCPKCS5Decrypt(tmpPrice, []byte(key))

		winNotice := &mucang.WinNotice{}
		err := proto.Unmarshal(decodePrice, winNotice)
		if err != nil {
			fmt.Println(err)
			return
		}

		winPrice = utils.ConvertStringToInt(strconv.FormatUint(winNotice.GetBidWinPrice(), 10))
	case "42":
		// 七猫
		price := c.Query("price")
		auction := c.Query("auction")

		if auction == "0" || len(auction) == 0 {
			return
		}

		key := "6f89f298213415c9b656fc61bccaf0d6"

		var decodeStr string
		if strings.Contains(price, "%3D") {
			decodeStr, _ = url.QueryUnescape(price)
		} else {
			decodeStr = price
		}

		crypted, err := rtb_qimao.Base64URLDecode(decodeStr)
		if err != nil {
			fmt.Println(err)
			return
		}
		decodePrice := rtb_qimao.AesDecrypt(crypted, []byte(key))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "43":
		// 驾校一点通
		price := c.Query("price")

		key := "9ea02ed090154438"
		tmpPrice, _ := utils.Base64URLDecode(price)
		decodePrice := rtb_jxedt.AesUtil(tmpPrice, []byte(key))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "44":
		// uc
		price := c.Query("price")

		unescape, _ := url.QueryUnescape(price)
		decodePrice, _ := utils.Base64URLDecode(unescape)

		winPrice = utils.ConvertStringToInt(string(decodePrice))
	case "45":
		// 凤凰
		price := c.Query("price")

		key := "cwM4A76VhwL9U6P3"
		tmpPrice, _ := utils.Base64URLDecode(price)
		decodePrice := rtb_ifeng.AESDecrypt(tmpPrice, []byte(key))

		priceFloat := utils.ConvertStringToFloat(string(decodePrice))
		priceInt := int(priceFloat)

		winPrice = priceInt

	case "46":
		// uc_audit
		price := c.Query("price")

		unescape, _ := url.QueryUnescape(price)
		decodePrice, _ := utils.Base64URLDecode(unescape)

		winPrice = utils.ConvertStringToInt(string(decodePrice))

	case "47":
		// 脉脉
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)
	case "48":
		price := c.Query("price")

		winPrice = utils.ConvertStringToInt(price)

	case "49", "52", "53":
		// 360
		price := c.Query("price")

		//str := "{channel:49,price:" + price + ",tagid:" + bidPosID + ",bp:" + bidPrice + "}"
		//go models.BigDataHoloDebugJson(bigdataUID, str)

		key := "6213FC1A2C51C632"
		decodePrice := rtb_three60.AesCBCDecrypte(price, key)

		priceFloat := utils.ConvertStringToFloat(decodePrice)
		priceInt := int(priceFloat)

		winPrice = priceInt
	case "50":
		// 虎扑V2
		price := c.Query("price")

		//str := "{channel:50,price:" + price + ",tagid:" + bidPosID + ",bp:" + bidPrice + "}"
		//go models.BigDataHoloDebugJson(bigdataUID, str)

		key := "f4ea2aa4274a4a94"
		decodePrice := rtb_hupuV2.Decrypt(price, key)
		tmpPrice := strings.Split(decodePrice, ".")
		decodePrice = tmpPrice[0]

		winPrice = utils.ConvertStringToInt(decodePrice)

	case "51":
		// 美柚
		price := c.Query("price")

		key := "89bde57804ed006ef11c7cbb4ca17850"
		decodePrice, _ := rtb_meetyou.Decrypt(price, key)

		winPrice = int(decodePrice * 100)
	case "54":
		// ks 格瑞
		price := c.Query("price")
		priceType := c.Query("price_type")
		clickRate := c.Query("click_rate")
		key := "530D69B3678AFA8E"

		var decodeStr string
		if strings.Contains(price, "%3D%3D") {
			decodeStr, _ = url.QueryUnescape(price)
		} else {
			decodeStr = price
		}

		crypted, _ := rtb_kuaishou.Base64URLDecode(decodeStr)
		decodePrice := rtb_kuaishou.Decrypt(crypted, []byte(key))
		winPrice = utils.ConvertStringToInt(decodePrice)

		if priceType == "cpc" {
			winPrice = 0
			if len(decodePrice) > 0 {
				rate, _ := strconv.ParseFloat(clickRate, 64)
				cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
				winPrice = int(cpcPrice * (1000 * rate / 100))
			}
		}
	case "998":
		// ks 思晴
		price := c.Query("price")
		priceType := c.Query("price_type")
		clickRate := c.Query("click_rate")
		key := "CB2F3C18DE996CAB"

		var decodeStr string
		if strings.Contains(price, "%3D%3D") {
			decodeStr, _ = url.QueryUnescape(price)
		} else {
			decodeStr = price
		}

		crypted, _ := rtb_kuaishou.Base64URLDecode(decodeStr)
		decodePrice := rtb_kuaishou.Decrypt(crypted, []byte(key))
		winPrice = utils.ConvertStringToInt(decodePrice)

		if priceType == "cpc" {
			winPrice = 0
			if len(decodePrice) > 0 {
				rate, _ := strconv.ParseFloat(clickRate, 64)
				cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
				winPrice = int(cpcPrice * (1000 * rate / 100))
			}
		}
	case "999":
		// ks 亿联
		price := c.Query("price")
		priceType := c.Query("price_type")
		clickRate := c.Query("click_rate")
		key := "54D8B30AE9E4B1B7"

		var decodeStr string
		if strings.Contains(price, "%3D%3D") {
			decodeStr, _ = url.QueryUnescape(price)
		} else {
			decodeStr = price
		}

		crypted, _ := rtb_kuaishou.Base64URLDecode(decodeStr)
		decodePrice := rtb_kuaishou.Decrypt(crypted, []byte(key))
		winPrice = utils.ConvertStringToInt(decodePrice)

		if priceType == "cpc" {
			winPrice = 0
			if len(decodePrice) > 0 {
				rate, _ := strconv.ParseFloat(clickRate, 64)
				cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
				winPrice = int(cpcPrice * (1000 * rate / 100))
			}
		}
	case "55":
		// 水印相机
		price := c.Query("price")
		priceFloat := utils.ConvertStringToFloat(price)
		winPrice = int(priceFloat)
	case "56":
		price := c.Query("price")
		ekey := []byte("3d629d2970b3499bb6050825694f2260")
		ikey := []byte("e90d2eb5080045629075a1a249883a1c")

		decodePrice := rtb_yoyo.Decrypt(price, ekey, ikey)
		priceFloat := utils.ConvertStringToFloat(decodePrice)
		winPrice = int(priceFloat)
	case "57":
		key := "a91ff983caf9b405"
		price := c.Query("price")
		decodePrice, _ := rtb_mgtv.Decrypt(price, key)
		tmpPrice := strings.Split(decodePrice, "|")
		if len(tmpPrice) == 2 {
			winPrice = utils.ConvertStringToInt(tmpPrice[0])
		}
	case "58":
		price := c.Query("price")
		key := "FriJCMGusRGBkw54"
		tmpPrice, _ := base64.RawURLEncoding.DecodeString(price)
		decodePrice := rtb_fengfei.AESDecrypt(tmpPrice, []byte(key))
		winPrice = utils.ConvertStringToInt(string(decodePrice))
	case "59":
		price := c.Query("price")

		encryptionStr := "038fd633002088e58019c1db002088e5801a1b21002088e5801a1d194ddd2755"
		encryptionKey := rtb_bes.HexToBytes(encryptionStr)

		integrityStr := "038fd633002088e5801aa142002088e5801aa2cb002088e5801aa3e53143809a"
		integrityKey := rtb_bes.HexToBytes(integrityStr)

		decodePrice := rtb_bes.Decrypt(price, encryptionKey, integrityKey)
		winPrice = int(decodePrice)
	case "60":
		price := c.Query("price")
		encryptionKey := []byte("b7989e744a9a46ab9dc9c0c93c7d6532")
		integrityKey := []byte("3ff682f07d324f7fa6252e30086f9c65")
		decodePrice := rtb_vivo.Decrypt(price, encryptionKey, integrityKey)
		winPrice = int(decodePrice)
	case "61":
		price := c.Query("price")
		winPrice = utils.ConvertStringToInt(price)
	case "62":
		price := c.Query("price")
		winPrice = utils.ConvertStringToInt(price)
	case "63":
		price := c.Query("price")
		winPrice = utils.ConvertStringToInt(price)
	case "64":
		// honor
		price := c.Query("price")
		secretKey := "9GjLj1ao+grEld/JrSRfo5/v5gVT4T7H27H8CroZnTw="
		decodePrice, _ := rtb_honor.Decrypt(price, secretKey)
		floatPrice := utils.ConvertStringToFloat(decodePrice)

		winPrice = int(floatPrice * 100)

	default:
		fmt.Println("empty price: " + channel)
		return
	}

	// save
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		fmt.Println("price nothing to do")
		return
	}

	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	var bigdataPriceItem models.BigdataPriceStu
	bigdataPriceItem.UID = c.Query("uid")
	bigdataPriceItem.TagID = c.Query("tagid")
	bigdataPriceItem.LocalAppID = log.Get("app_id")
	bigdataPriceItem.LocalPosID = log.Get("pos_id")
	bigdataPriceItem.PlatformAppID = log.Get("p_app_id")
	bigdataPriceItem.PlatformPosID = log.Get("p_pos_id")
	bigdataPriceItem.BidPrice = utils.ConvertStringToInt(c.Query("bp"))
	bigdataPriceItem.WinPrice = winPrice
	bigdataPriceItem.AdID = log.Get("adid")

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata win price panic:", err)
			}
		}()

		models.BigDataWinPriceToHolo(c, &bigdataPriceItem)
	}()
}

func QimaoAd(c *gin.Context) {
	resp := rtb_qimao.HandleByQimaoAd(c)
	c.ProtoBuf(200, resp)
}
