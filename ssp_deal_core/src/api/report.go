package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// Request ...
func Request(c *gin.Context) {
	// fmt.Println("req begin")
	// apiVersion := c.Query("api_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	// fmt.Println(apiVersion)
	// fmt.Println(pos)
	// fmt.Println(media)
	// fmt.Println(device)
	// fmt.Println(network)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)

	// geo
	geoStu := models.MHReqGeo{}
	json.Unmarshal([]byte(geo), &geoStu)
	// fmt.Println(geoStu.Lat)
	// fmt.Println(geoStu.Lng)

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()
	// fmt.Println("http ua: " + ua)
	// fmt.Println("http ip: " + ip)

	// fmt.Println("device ua before: " + deviceStu.Ua)
	// fmt.Println("device ip before: " + deviceStu.Ip)
	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	// fmt.Println("device ua after: " + deviceStu.Ua)
	// fmt.Println("device ip after: " + deviceStu.IP)

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu

	var respExtraData models.MHUpRespExtra
	respExtraData.BigdataUID = uuid.NewV4().String()
	respExtraData.PlatformAppID = c.Query("platform_app_id")
	respExtraData.PlatformPosID = c.Query("platform_pos_id")
	respExtraData.UpReqTime = 1
	respExtraData.UpReqNum = reqStu.Pos.AdCount
	respExtraData.UpRespTime = 1
	respExtraData.UpRespNum = utils.ConvertStringToInt(c.Query("resp_num"))
	respExtraData.UpRespOKNum = utils.ConvertStringToInt(c.Query("resp_num"))
	if c.Query("code") == "-1" {
		respExtraData.ExternalCode = 102006
		respExtraData.IsWin = 0
	} else {
		respExtraData.ExternalCode = 0
		respExtraData.IsWin = 1
	}
	respExtraData.InternalCode = 900000

	localPos := models.LocalPosStu{LocalAppID: appStu.AppID, LocalPosID: utils.ConvertIntToString(posStu.ID), LocalAppType: "1"}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata req panic:", err)
			}
		}()

		models.BigDataAdxCoReq(c, &reqStu, &localPos, &respExtraData)
	}()
}

// RequestV3 ...
func RequestV3(c *gin.Context) {
	// fmt.Println("req begin")
	// apiVersion := c.Query("api_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	did := c.Query("did")
	// fmt.Println(apiVersion)
	// fmt.Println(pos)
	// fmt.Println(media)
	// fmt.Println(device)
	// fmt.Println(network)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(did)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(deviceStu)
	json.Unmarshal([]byte(didDecrypt), &deviceStu)

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)

	// geo
	geoStu := models.MHReqGeo{}
	json.Unmarshal([]byte(geo), &geoStu)
	// fmt.Println(geoStu.Lat)
	// fmt.Println(geoStu.Lng)

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()
	// fmt.Println("http ua: " + ua)
	// fmt.Println("http ip: " + ip)

	// fmt.Println("device ua before: " + deviceStu.Ua)
	// fmt.Println("device ip before: " + deviceStu.Ip)
	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	// fmt.Println("device ua after: " + deviceStu.Ua)
	// fmt.Println("device ip after: " + deviceStu.IP)

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu

	var respExtraData models.MHUpRespExtra
	respExtraData.BigdataUID = uuid.NewV4().String()
	respExtraData.PlatformAppID = c.Query("platform_app_id")
	respExtraData.PlatformPosID = c.Query("platform_pos_id")
	respExtraData.UpReqTime = 1
	respExtraData.UpReqNum = reqStu.Pos.AdCount
	respExtraData.UpRespTime = 1
	respExtraData.UpRespNum = utils.ConvertStringToInt(c.Query("resp_num"))
	respExtraData.UpRespOKNum = utils.ConvertStringToInt(c.Query("resp_num"))
	respExtraData.FloorPrice = utils.ConvertStringToInt(c.Query("floor_price")) * utils.ConvertStringToInt(c.Query("resp_num"))
	respExtraData.FinalPrice = utils.ConvertStringToInt(c.Query("final_price")) * utils.ConvertStringToInt(c.Query("resp_num"))
	respExtraData.UpPrice = utils.ConvertStringToInt(c.Query("up_price"))
	if c.Query("code") == "-1" {
		respExtraData.ExternalCode = 102006
		respExtraData.IsWin = 0
	} else {
		respExtraData.ExternalCode = 0
		respExtraData.IsWin = 1
	}
	respExtraData.InternalCode = 900000

	localPos := models.LocalPosStu{LocalAppID: appStu.AppID, LocalPosID: utils.ConvertIntToString(posStu.ID), LocalAppType: "1"}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata req panic:", err)
			}
		}()

		models.BigDataAdxCoReq(c, &reqStu, &localPos, &respExtraData)
	}()
}

// RequestV33 ...
func RequestV33(c *gin.Context) {
	// fmt.Println("req begin")
	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk req report 3.3:", len(bodyContent))

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk config 3.3 encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}
	// fmt.Println("kbg_debug sdk req report 3.3 body:", string(bodyContent))

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk report req parser error:", err)
	}

	// hack ad_count
	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	// 解密device
	if len(mhReq.DID) > 0 {
		// var deviceDIDStu models.MHReqDevice
		didDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(deviceStu)
		json.Unmarshal([]byte(didDecrypt), &mhReq.Device)

		if len(mhReq.Device.CAIDMulti) == 0 && len(mhReq.Device.SpecMulti) > 0 {
			for _, item := range mhReq.Device.SpecMulti {
				var caidItem models.MHReqCAIDMulti
				caidItem.CAID = item.Spec
				caidItem.CAIDVersion = item.SpecVersion
				mhReq.Device.CAIDMulti = append(mhReq.Device.CAIDMulti, caidItem)
			}
		}
	}

	// 解密geo
	if len(mhReq.LBS) > 0 {
		// fmt.Println("geo encode:", mhReq.LBS)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.LBS)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println("geo decode:", string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &mhReq.Geo)
	}
	// fmt.Println("device ua after: " + deviceStu.Ua)
	// fmt.Println("device ip after: " + deviceStu.IP)

	// tmpDataByte, _ := json.Marshal(mhReq.Device)
	// fmt.Println("kbg_debug_device: ", string(tmpDataByte))

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata req panic:", err)
			}
		}()

		var respExtraData models.MHUpRespExtra
		respExtraData.BigdataUID = mhReq.ExtSDK.ReqID
		respExtraData.PlatformAppID = mhReq.ExtSDK.PlatformAppID
		respExtraData.PlatformPosID = mhReq.ExtSDK.PlatformPosID
		respExtraData.UpReqTime = 1
		respExtraData.UpReqNum = mhReq.Pos.AdCount
		respExtraData.UpRespTime = 1
		respExtraData.UpRespNum = mhReq.ExtSDK.RespNum
		respExtraData.UpRespOKNum = mhReq.ExtSDK.RespNum
		if mhReq.ExtSDK.Code == 0 {
			respExtraData.ExternalCode = 0
			respExtraData.IsWin = 1
		} else {
			respExtraData.ExternalCode = mhReq.ExtSDK.Code
			respExtraData.IsWin = 0
		}
		respExtraData.InternalCode = 900000

		localPos := models.LocalPosStu{LocalAppID: mhReq.App.AppID, LocalPosID: utils.ConvertIntToString(mhReq.Pos.ID), LocalAppType: "1"}
		models.BigDataAdxCoReq(c, &mhReq, &localPos, &respExtraData)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok req"
	c.PureJSON(200, resp)
}

// Expose ...
func Expose(c *gin.Context) {
	// fmt.Println("expose begin")

	// 上报大数据
	models.BigDataClickHouseExp(c)

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok exp"
	mhDebug := c.Query("mh_debug")
	if mhDebug == "1" {
		logQuery := c.Query("log")
		// logQuery = strings.Replace(logQuery, " ", "+", -1)

		if len(logQuery) == 0 {
			fmt.Println("bigdata exp nothing to do")
			return
		}
		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))

		// sdk p_app_id
		pAppID := c.Query("p_app_id")
		// sdk3.3使用宏 pappid=__P_APP_ID__
		if len(c.Query("pappid")) > 0 && c.Query("pappid") != "__P_APP_ID__" {
			pAppID = c.Query("pappid")
		}
		if len(log.Get("p_app_id")) > 0 {
			pAppID = log.Get("p_app_id")
		}

		// sdk p_pos_id
		pPosID := c.Query("p_pos_id")
		// sdk3.3使用宏 pposid=__P_POS_ID__
		if len(c.Query("pposid")) > 0 && c.Query("pposid") != "__P_POS_ID__" {
			pPosID = c.Query("pposid")
		}
		if len(log.Get("p_pos_id")) > 0 {
			pPosID = log.Get("p_pos_id")
		}

		// sdk floor_price
		// floorPrice := utils.ConvertStringToInt(c.Query("floor_price"))
		// if floorPrice == 0 {
		// 	floorPrice = utils.ConvertStringToInt(log.Get("floor_price"))
		// }
		// sdk final_price
		// finalPrice := utils.ConvertStringToInt(c.Query("final_price"))
		// if finalPrice == 0 {
		// 	finalPrice = utils.ConvertStringToInt(log.Get("final_price"))
		// }
		// sdk up_price
		ecpm := utils.ConvertStringToInt(c.Query("ecpm"))
		if ecpm == 0 {
			ecpm = utils.ConvertStringToInt(log.Get("ecpm"))
		}
		if len(c.Query("sdklogep")) > 0 && c.Query("sdklogep") != "__DPPLOGEP__" {
			sdkEcpmDecryptBase64, _ := base64.StdEncoding.DecodeString(c.Query("sdklogep"))
			sdkEcpmDecrypt := utils.AesECBDecrypt(sdkEcpmDecryptBase64, []byte(config.EncryptKEY))
			ecpm = utils.ConvertStringToInt(string(sdkEcpmDecrypt))
		}

		debugInfoMap := map[string]interface{}{}
		debugInfoMap["reqid"] = log.Get("uid")
		debugInfoMap["adid"] = log.Get("adid")
		debugInfoMap["app_id"] = log.Get("app_id")
		debugInfoMap["pos_id"] = log.Get("pos_id")
		debugInfoMap["p_app_id"] = pAppID
		debugInfoMap["p_pos_id"] = pPosID
		debugInfoMap["app_name"] = log.Get("app_name")
		debugInfoMap["app_bundle"] = log.Get("app_bundle")
		debugInfoMap["os"] = log.Get("os")
		debugInfoMap["osv"] = log.Get("osv")
		debugInfoMap["did_md5"] = log.Get("did_md5")
		debugInfoMap["imei"] = log.Get("imei")
		debugInfoMap["imei_md5"] = log.Get("imei_md5")
		debugInfoMap["android_id"] = log.Get("android_id")
		debugInfoMap["android_id_md5"] = log.Get("android_id_md5")
		debugInfoMap["idfa"] = log.Get("idfa")
		debugInfoMap["idfa_md5"] = log.Get("idfa_md5")
		debugInfoMap["ip"] = c.ClientIP()
		debugInfoMap["ua"] = c.GetHeader("User-Agent")
		debugInfoMap["oaid"] = log.Get("oaid")
		debugInfoMap["oaid_md5"] = log.Get("oaid_md5")
		debugInfoMap["model"] = log.Get("model")
		debugInfoMap["manufacturer"] = log.Get("manufacturer")
		debugInfoMap["ecpm"] = ecpm

		debugInfoMap["sdk_version"] = log.Get("sdk_version")
		debugInfoMap["mg_deal_id"] = c.Query("mg_deal_id")

		resp["debug"] = debugInfoMap
	}

	c.PureJSON(200, resp)
}

// Click ...
func Click(c *gin.Context) {
	fmt.Println("click begin")

	// 上报大数据
	models.BigDataClickHouseClk(c)

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok clk"
	mhDebug := c.Query("mh_debug")
	if mhDebug == "1" {
		logQuery := c.Query("log")
		// logQuery = strings.Replace(logQuery, " ", "+", -1)

		if len(logQuery) == 0 {
			fmt.Println("bigdata exp nothing to do")
			return
		}
		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))

		// sdk p_app_id
		pAppID := c.Query("p_app_id")
		// sdk3.3使用宏 pappid=__P_APP_ID__
		if len(c.Query("pappid")) > 0 && c.Query("pappid") != "__P_APP_ID__" {
			pAppID = c.Query("pappid")
		}
		if len(log.Get("p_app_id")) > 0 {
			pAppID = log.Get("p_app_id")
		}

		// sdk p_pos_id
		pPosID := c.Query("p_pos_id")
		// sdk3.3使用宏 pposid=__P_POS_ID__
		if len(c.Query("pposid")) > 0 && c.Query("pposid") != "__P_POS_ID__" {
			pPosID = c.Query("pposid")
		}
		if len(log.Get("p_pos_id")) > 0 {
			pPosID = log.Get("p_pos_id")
		}

		// sdk floor_price
		// floorPrice := utils.ConvertStringToInt(c.Query("floor_price"))
		// if floorPrice == 0 {
		// 	floorPrice = utils.ConvertStringToInt(log.Get("floor_price"))
		// }
		// sdk final_price
		// finalPrice := utils.ConvertStringToInt(c.Query("final_price"))
		// if finalPrice == 0 {
		// 	finalPrice = utils.ConvertStringToInt(log.Get("final_price"))
		// }
		// sdk up_price
		ecpm := utils.ConvertStringToInt(c.Query("ecpm"))
		if ecpm == 0 {
			ecpm = utils.ConvertStringToInt(log.Get("ecpm"))
		}

		debugInfoMap := map[string]interface{}{}
		debugInfoMap["reqid"] = log.Get("uid")
		debugInfoMap["adid"] = log.Get("adid")
		debugInfoMap["app_id"] = log.Get("app_id")
		debugInfoMap["pos_id"] = log.Get("pos_id")
		debugInfoMap["p_app_id"] = pAppID
		debugInfoMap["p_pos_id"] = pPosID
		debugInfoMap["app_name"] = log.Get("app_name")
		debugInfoMap["app_bundle"] = log.Get("app_bundle")
		debugInfoMap["os"] = log.Get("os")
		debugInfoMap["osv"] = log.Get("osv")
		debugInfoMap["did_md5"] = log.Get("did_md5")
		debugInfoMap["imei"] = log.Get("imei")
		debugInfoMap["imei_md5"] = log.Get("imei_md5")
		debugInfoMap["android_id"] = log.Get("android_id")
		debugInfoMap["android_id_md5"] = log.Get("android_id_md5")
		debugInfoMap["idfa"] = log.Get("idfa")
		debugInfoMap["idfa_md5"] = log.Get("idfa_md5")
		debugInfoMap["ip"] = c.ClientIP()
		debugInfoMap["ua"] = c.GetHeader("User-Agent")
		debugInfoMap["oaid"] = log.Get("oaid")
		debugInfoMap["oaid_md5"] = log.Get("oaid_md5")
		debugInfoMap["model"] = log.Get("model")
		debugInfoMap["manufacturer"] = log.Get("manufacturer")
		debugInfoMap["ecpm"] = ecpm

		debugInfoMap["sdk_version"] = log.Get("sdk_version")
		debugInfoMap["mg_deal_id"] = c.Query("mg_deal_id")

		resp["debug"] = debugInfoMap
	}

	c.PureJSON(200, resp)
}

// DP ...
func DP(c *gin.Context) {
	fmt.Println("dp")
	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		fmt.Println("dp nothing to do")
		return
	}

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata click panic:", err)
			}
		}()
		models.BigDataAdxDeepLink(c)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok dp"
	c.PureJSON(200, resp)
}

// VideoStart ...
func VideoStart(c *gin.Context) {
	fmt.Println("video start")
	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		fmt.Println("video start nothing to do")
		return
	}

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata video start panic:", err)
			}
		}()
		models.BigDataAdxVideoStart(c)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok video start"
	c.PureJSON(200, resp)
}

// VideoEnd ...
func VideoEnd(c *gin.Context) {
	fmt.Println("video end")
	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		fmt.Println("video end nothing to do")
		return
	}

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata video end panic:", err)
			}
		}()
		models.BigDataAdxVideoEnd(c)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok video end"
	c.PureJSON(200, resp)
}
