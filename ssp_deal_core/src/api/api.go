package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"strconv"
	"strings"

	adx_common_models "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// http://localhost:8081/api/v1?api_version=1.0&pos={"id":55874,"width":640,"height":960,"support_full_screen_interstitial":false,"ad_count":1,"need_rendered_ad":false,"channel":0,"pagenumber":0}&media={"app_id":"10013","app_bundle_id":"com.wppai.adapi.demo"}&device={"os":"ios","os_version":"6.2.1","model":"iPhone6,2","manufacturer":"Apple","device_type":1,"screen_width":1800,"screen_height":1920,"dpi":0,"orientation":0,"idfa":"","idfa_md5":"8F3FF7F9B6A8E5A9EEC346B479AE3449","imei":"","imei_md5":""}&network={"connect_type":0,"carrier":2}

// 1.0
// {"id":55874,"width":640,"height":960,"support_full_screen_interstitial":false,"ad_count":1,"need_rendered_ad":false,"channel":0,"pagenumber":0}
// {"app_id":"10013","app_bundle_id":"com.wppai.adapi.demo"}
// {"os":"ios","os_version":"6.2.1","model":"iPhone6,2","manufacturer":"Apple","device_type":1,"screen_width":1800,"screen_height":1920,"dpi":0,"orientation":0,"idfa":"","idfa_md5":"8F3FF7F9B6A8E5A9EEC346B479AE3449","imei":"","imei_md5":""}
// {"connect_type":0,"carrier":2}

// 10012 55871
// curl "http://localhost:8083/api/v1?api_version=1.0&pos=%7B%22id%22%3A55871%2C%22width%22%3A960%2C%22height%22%3A640%2C%22ad_count%22%3A1%2C%22video_orientation%22%3A1%7D&media=%7B%22app_id%22%3A%2210013%22%2C%22app_bundle_id%22%3A%22com.esbook.reader%22%2C%22app_bundle_name%22%3Anull%7D&device=%7B%22ip%22%3A%2239.146.116.1%22%2C%22mac%22%3A%22%22%2C%22ua%22%3A%22Mozilla%2F5.0+%28Linux%3B+Android+15%3B+24122RKC7C+Build%2FAQ3A.240829.003%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F128.0.6613.127+Mobile+Safari%2F537.36%22%2C%22os%22%3A%22android%22%2C%22os_version%22%3A%2215%22%2C%22model%22%3A%2224122RKC7C%22%2C%22manufacturer%22%3A%22Redmi%22%2C%22device_type%22%3A1%2C%22screen_width%22%3A1080%2C%22screen_height%22%3A2251%2C%22idfa%22%3Anull%2C%22imei%22%3A%22%22%2C%22imei_md5%22%3A%22%22%2C%22android_id%22%3A%2244e75e1cb4a09030%22%2C%22android_id_md5%22%3A%224c4ea855bc439934f06fdab98da7e220%22%2C%22oaid%22%3A%22855555bf6d25ffa7%22%2C%22device_start_sec%22%3Anull%2C%22device_birth_sec%22%3Anull%2C%22country%22%3Anull%2C%22language%22%3Anull%2C%22device_name_md5%22%3Anull%2C%22hardware_machine%22%3Anull%2C%22hardware_model%22%3Anull%2C%22physical_memory_byte%22%3Anull%2C%22harddisk_size_byte%22%3Anull%2C%22system_update_sec%22%3Anull%2C%22time_zone%22%3Anull%2C%22openudid%22%3Anull%2C%22hms_version%22%3A%22%22%2C%22miui_version%22%3A%22%22%2C%22boot_mark%22%3A%22f14258c7-a57a-471d-aa53-46606115da03%22%2C%22update_mark%22%3A%221748155867.911685613%22%2C%22appstore_version%22%3A%22%22%2C%22cpu_num%22%3Anull%2C%22caid%22%3Anull%2C%22caid_version%22%3Anull%2C%22applist%22%3A%5B%5D%2C%22caid_multi%22%3Anull%7D&network=%7B%22connect_type%22%3A1%2C%22carrier%22%3A1%7D&geo=%7B%22coordinate_type%22%3A0%2C%22lon%22%3A121.40979003906%2C%22lat%22%3A31.229669570923%2C%22timestamp%22%3A0%2C%22location_accuracy%22%3A0.0%2C%22coord_time%22%3A0%7D"

// API ...
func API(c *gin.Context) {
	pos := c.Query("pos")
	media := c.Query("media")

	// app
	appStu := models.MHReqApp{}
	err := json.Unmarshal([]byte(media), &appStu)
	if err != nil {
		fmt.Println("api app unmarshal err: ", appStu.AppID, err)
		c.PureJSON(200, models.MHResp{Ret: 100001, Msg: ""})
		return
	}
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	err = json.Unmarshal([]byte(pos), &posStu)
	if err != nil {
		fmt.Println("api pos unmarshal err: ", appStu.AppID, err)
		c.PureJSON(200, models.MHResp{Ret: 100001, Msg: ""})
		return
	}

	// 默认为1
	if posStu.AdCount == 0 {
		posStu.AdCount = 1
	}

	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")

	// device
	deviceStu := models.MHReqDevice{}
	err = json.Unmarshal([]byte(device), &deviceStu)
	if err != nil {
		fmt.Println("api device unmarshal err: ", appStu.AppID, err)
		c.PureJSON(200, models.MHResp{Ret: 100001, Msg: ""})
		return
	}

	if deviceStu.DPI <= 0 {
		if deviceStu.ScreenDensityToDeleted > 0 {
			deviceStu.DPI = deviceStu.ScreenDensityToDeleted
		}
	}

	if len(deviceStu.CAID) > 0 && len(deviceStu.CAIDVersion) > 0 && len(deviceStu.CAIDMulti) == 0 {
		var tmpCaid models.MHReqCAIDMulti
		tmpCaid.CAID = deviceStu.CAID
		tmpCaid.CAIDVersion = deviceStu.CAIDVersion
		deviceStu.CAIDMulti = append(deviceStu.CAIDMulti, tmpCaid)
	}

	// applist
	if len(deviceStu.AppList) == 0 {
		if len(deviceStu.AppPackageNameList) > 0 {
			for _, item := range deviceStu.AppPackageNameList {
				appCode, isOK := adx_common_models.GetAppIdByPackageName(item)
				if isOK {
					deviceStu.AppList = append(deviceStu.AppList, appCode)
				}
			}
		}
	}

	// network
	networkStu := models.MHReqNetwork{}
	if len(network) > 0 {
		err = json.Unmarshal([]byte(network), &networkStu)
		if err != nil {
			fmt.Println("api network unmarshal err: ", appStu.AppID, err)
			c.PureJSON(200, models.MHResp{Ret: 100001, Msg: ""})
			return
		}
	}

	// geo
	geoStu := models.MHReqGeo{}
	if len(geo) > 0 {
		err = json.Unmarshal([]byte(geo), &geoStu)
		if err != nil {
			fmt.Println("api geo unmarshal err: ", appStu.AppID, err)
			c.PureJSON(200, models.MHResp{Ret: 100001, Msg: ""})
			return
		}
	}

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()
	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu

	////////////////////////////////////////////////////////////////////////////////////////////////////
	bigdataUID := uuid.NewV4().String()
	resp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if resp == nil {
		c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
		return
	}

	c.PureJSON(200, *resp)
}

// SDKAPIV3 sdk -> api get请求
func SDKAPIV3(c *gin.Context) {
	// beginTime := utils.GetCurrentMilliSecond()

	// fmt.Println("api begin")
	// apiVersion := c.Query("api_version")
	sdkVersion := c.Query("sdk_version")
	// fmt.Println(apiVersion)
	// fmt.Println(sdkVersion)

	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	did := c.Query("did")
	// fmt.Println(pos)
	// fmt.Println(media)
	// fmt.Println(device)
	// fmt.Println(network)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)
	// fmt.Println(posStu.Width)
	// fmt.Println(posStu.Height)
	// fmt.Println(posStu.AdCount)
	// hack ad_count
	if posStu.AdCount == 0 {
		posStu.AdCount = 1
	}

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(did)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(deviceStu)
	json.Unmarshal([]byte(didDecrypt), &deviceStu)
	// fmt.Println(deviceStu)
	if deviceStu.DPI <= 0 {
		if deviceStu.ScreenDensityToDeleted > 0 {
			deviceStu.DPI = deviceStu.ScreenDensityToDeleted
		}
	}

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)
	if networkStu.ConnectType == 5 {
		networkStu.ConnectType = 7
	}
	// fmt.Println(networkStu.Connect_type)
	// fmt.Println(networkStu.Carrier)

	// geo
	geoStu := models.MHReqGeo{}
	// json.Unmarshal([]byte(geo), &geoStu)
	if len(sdkVersion) > 0 && utils.ConvertStringToInt(strings.Split(sdkVersion, ".")[0]) >= 3 && len(geo) > 0 {
		// fmt.Println("geo:", geo)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(geo)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &geoStu)
	}

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()
	// fmt.Println("http ua: " + ua)
	// fmt.Println("http ip: " + ip)

	// fmt.Println("device ua before: " + deviceStu.Ua)
	// fmt.Println("device ip before: " + deviceStu.Ip)
	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	// fmt.Println("device ua after: " + deviceStu.Ua)
	// fmt.Println("device ip after: " + deviceStu.IP)

	var mhReq models.MHReq
	mhReq.App = appStu
	mhReq.Pos = posStu
	mhReq.Device = deviceStu
	mhReq.Network = networkStu
	mhReq.Geo = geoStu
	mhReq.SDKVersion = sdkVersion

	////////////////////////////////////////////////////////////////////////////////////////////////////
	bigdataUID := uuid.NewV4().String()
	resp := core.GetADFromAdxWithContext(c, &mhReq, bigdataUID)
	if resp == nil {
		c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
		return
	}

	if mhReq.SDKVersion == "3.1.5" || mhReq.SDKVersion == "3.2.9" || mhReq.SDKVersion == "3.2.10" || mhReq.SDKVersion == "3.2.11" || mhReq.SDKVersion == "3.2.12" {
		localAppID := mhReq.App.AppID
		localPosID := mhReq.Pos.ID

		localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
		if localPosInfo == nil {
			c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
			return
		}

		randNum := rand.Intn(100)
		if randNum < localPosInfo.LocalAppSDKFilterApiWeight {
			c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
			return
		}
	}

	c.PureJSON(200, *resp)
}

// SDKAPIV33 sdk -> api post请求
func SDKAPIV33(c *gin.Context) {
	// beginTime := utils.GetCurrentMilliSecond()
	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk api 3.3:", len(bodyContent))
	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk api 3.3 encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk api parser error:", err)
	}

	// hack ad_count
	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	// 解密device
	if len(mhReq.DID) > 0 {
		// var deviceDIDStu models.MHReqDevice
		didDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(deviceStu)
		json.Unmarshal([]byte(didDecrypt), &mhReq.Device)

		if len(mhReq.Device.CAIDMulti) == 0 && len(mhReq.Device.SpecMulti) > 0 {
			for _, item := range mhReq.Device.SpecMulti {
				var caidItem models.MHReqCAIDMulti
				caidItem.CAID = item.Spec
				caidItem.CAIDVersion = item.SpecVersion
				mhReq.Device.CAIDMulti = append(mhReq.Device.CAIDMulti, caidItem)
			}
		}
	}

	// 解密geo
	if len(mhReq.LBS) > 0 {
		// fmt.Println("geo encode:", mhReq.LBS)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.LBS)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println("geo decode:", string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &mhReq.Geo)
	}

	if mhReq.Network.ConnectType == 5 {
		mhReq.Network.ConnectType = 7
	}

	if len(mhReq.Sdkalp) > 0 {
		var appListCode []int
		packageNameDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.Sdkalp)
		packageNameDecrypt := utils.AesECBDecrypt(packageNameDecryptBase64, []byte(config.EncryptKEY))
		packageName := string(packageNameDecrypt)

		for key, value := range adx_common_models.GetMHAppListMap() {
			if strings.Contains(packageName, key) {
				appListCode = append(appListCode, value)
			}
		}
		mhReq.Device.AppList = appListCode
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	bigdataUID := uuid.NewV4().String()
	resp := core.GetADFromAdxWithContext(c, &mhReq, bigdataUID)
	if resp == nil {
		c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
		return
	}
	// fmt.Println(respStu)
	// time.Sleep(5 * time.Second)

	if mhReq.SDKVersion == "3.1.5" || mhReq.SDKVersion == "3.2.9" || mhReq.SDKVersion == "3.2.10" || mhReq.SDKVersion == "3.2.11" || mhReq.SDKVersion == "3.2.12" {
		localAppID := mhReq.App.AppID
		localPosID := mhReq.Pos.ID

		localPosInfo := models.GetLocalPosInfo(c, strconv.Itoa(localPosID), localAppID)
		if localPosInfo == nil {
			c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
			return
		}

		randNum := rand.Intn(100)
		if randNum < localPosInfo.LocalAppSDKFilterApiWeight {
			c.PureJSON(200, models.MHResp{Ret: 102006, Msg: ""})
			return
		}
	}

	// jsonData, _ := json.Marshal(respStu)
	// fmt.Println("kbg_debug_sdk_api_3.3 resp: " + string(jsonData))
	// if mhReq.Pos.ID == 59081 {
	// 	jsonData, _ := json.Marshal(respStu)
	// 	mhResp := models.MHResp{}
	// 	json.Unmarshal(jsonData, &mhResp)
	// 	if mhResp.Ret == 0 {
	// 		// fmt.Println("kbg_debug_sdk_api_3 resp: ", string(jsonData))
	// 		go models.BigDataHoloDebugJson2(bigdataUID+"_59081", string(jsonData), "10797", "59081", "", "")
	// 	}
	// }

	if mhReq.Device.Os == "ios" {
		// 2025.05.28 ios sdk_version ＜ 1.3.2.4, win_notice_url, loss_notice_url为空时置为空格, hack data
		if core.IsSDKVersionMoreThanOrEqualFourVersionCode(c, mhReq.SDKVersion, 1, 3, 2, 4) {
		} else {
			if resp.Ret == 0 {
				if resp.Data[utils.ConvertIntToString(mhReq.Pos.ID)].List != nil {
					for id, item := range resp.Data[utils.ConvertIntToString(mhReq.Pos.ID)].List {
						if len(item.WinNoticeURL) == 0 {
							resp.Data[utils.ConvertIntToString(mhReq.Pos.ID)].List[id].WinNoticeURL = " "
						}

						if len(item.LossNoticeURL) == 0 {
							resp.Data[utils.ConvertIntToString(mhReq.Pos.ID)].List[id].LossNoticeURL = " "
						}
					}
				}
			}
		}
	}

	c.PureJSON(200, *resp)
}
