package api

import (
	"encoding/base64"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/rtb/rtb_kuaishou"
	"mh_proxy/utils"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
)

// http://localhost:8081/api/v1?api_version=1.0&pos={"id":55874,"width":640,"height":960,"support_full_screen_interstitial":false,"ad_count":1,"need_rendered_ad":false,"channel":0,"pagenumber":0}&media={"app_id":"10013","app_bundle_id":"com.wppai.adapi.demo"}&device={"os":"ios","os_version":"6.2.1","model":"iPhone6,2","manufacturer":"Apple","device_type":1,"screen_width":1800,"screen_height":1920,"dpi":0,"orientation":0,"idfa":"","idfa_md5":"8F3FF7F9B6A8E5A9EEC346B479AE3449","imei":"","imei_md5":""}&network={"connect_type":0,"carrier":2}

// 1.0
// {"id":55874,"width":640,"height":960,"support_full_screen_interstitial":false,"ad_count":1,"need_rendered_ad":false,"channel":0,"pagenumber":0}
// {"app_id":"10013","app_bundle_id":"com.wppai.adapi.demo"}
// {"os":"ios","os_version":"6.2.1","model":"iPhone6,2","manufacturer":"Apple","device_type":1,"screen_width":1800,"screen_height":1920,"dpi":0,"orientation":0,"idfa":"","idfa_md5":"8F3FF7F9B6A8E5A9EEC346B479AE3449","imei":"","imei_md5":""}
// {"connect_type":0,"carrier":2}

func TestApiTaskWithTimeout(t *testing.T) {
	db.InitBigCache()
	db.InitRedis()

	// 创建一个真实的 http.ResponseWriter 用于测试
	w := httptest.NewRecorder()

	c, _ := gin.CreateTestContext(w)
	c.Request = &http.Request{
		URL: &url.URL{
			Scheme: "http",
			Host:   "localhost:8081",
			Path:   "/api/v1",
		},
		Header: make(http.Header),
		Method: "GET",
	}

	media := `{"app_id":"11221","app_bundle_id":"com.wppai.adapi.demo","app_bundle_name":"com.wppai.adapi.demo","app_ssp_type":1}`
	pos := `{"id":55874,"width":640,"height":960,"support_full_screen_interstitial":false,"ad_count":1,"cpm_bid_floor":1,"tagid":"1234567890","query":"1234567890","pkg_whitelist":["com.wppai.adapi.demo"],"pkg_blacklist":["com.wppai.adapi.demo"]}`
	device := `{"os":"ios","os_version":"6.2.1","model":"iPhone6,2","manufacturer":"Apple","device_type":1,"screen_width":1800,"screen_height":1920,"dpi":0,"orientation":0,"idfa":"","idfa_md5":"8F3FF7F9B6A8E5A9EEC346B479AE3449","imei":"","imei_md5":""}`
	network := `{"connect_type":0,"carrier":2}`
	geo := `{"lat":123.456,"lng":78.910}`

	urlStr := c.Request.URL.Query()
	urlStr.Add("pos", pos)
	urlStr.Add("media", media)
	urlStr.Add("device", device)
	urlStr.Add("network", network)
	urlStr.Add("api_version", "1.0")
	urlStr.Add("channel", "1")
	urlStr.Add("pagenumber", "1")
	urlStr.Add("geo", geo)
	c.Request.URL.RawQuery = urlStr.Encode()

	API(c)
}

func TestPriceDecrypy(t *testing.T) {
	// 测试数据 - 使用正确格式的加密字符串
	key := "530D69B3678AFA8E"
	price := rtb_kuaishou.Encrypt([]byte("100"), []byte(key))
	price = base64.StdEncoding.EncodeToString([]byte(price))
	priceType := "cpm" // 使用cpm或cpc
	clickRate := "5"   // 5%的点击率

	// 记录开始时间
	startTime := time.Now()

	var decodeStr string
	if strings.Contains(price, "%3D%3D") {
		decodeStr, _ = url.QueryUnescape(price)
	} else {
		decodeStr = price
	}

	crypted, err := rtb_kuaishou.Base64URLDecode(decodeStr)
	if err != nil {
		return
	}
	fmt.Printf("crypted=%v", string(crypted))
	decodePrice := rtb_kuaishou.Decrypt(crypted, []byte(key))
	var winPrice int = utils.ConvertStringToInt(decodePrice)

	if priceType == "cpc" {
		winPrice = 0
		if len(decodePrice) > 0 {
			rate, _ := strconv.ParseFloat(clickRate, 64)
			cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
			winPrice = int(cpcPrice * (1000 * rate / 100))
		}
	}

	// 计算耗时并输出
	elapsedTime := time.Since(startTime)
	fmt.Printf("解密价格: %s, 结果: %d, 耗时: %s\n", price, winPrice, elapsedTime)

	// 使用多个不同的价格测试，做多次测试计算平均耗时
	prices := []string{
		// 使用实际加密算法加密的价格字符串
		base64.StdEncoding.EncodeToString([]byte(rtb_kuaishou.Encrypt([]byte("100"), []byte(key)))),
		base64.StdEncoding.EncodeToString([]byte(rtb_kuaishou.Encrypt([]byte("200"), []byte(key)))),
		base64.StdEncoding.EncodeToString([]byte(rtb_kuaishou.Encrypt([]byte("500"), []byte(key)))),
		base64.StdEncoding.EncodeToString([]byte(rtb_kuaishou.Encrypt([]byte("1000"), []byte(key)))),
		base64.StdEncoding.EncodeToString([]byte(rtb_kuaishou.Encrypt([]byte("5000"), []byte(key)))),
	}

	totalTime := time.Duration(0)
	for i, p := range prices {
		start := time.Now()

		if strings.Contains(p, "%3D%3D") {
			decodeStr, _ = url.QueryUnescape(p)
		} else {
			decodeStr = p
		}

		crypted, _ := rtb_kuaishou.Base64URLDecode(decodeStr)
		decodePrice := rtb_kuaishou.Decrypt(crypted, []byte(key))
		winPrice = utils.ConvertStringToInt(decodePrice)

		if priceType == "cpc" {
			winPrice = 0
			if len(decodePrice) > 0 {
				rate, _ := strconv.ParseFloat(clickRate, 64)
				cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
				winPrice = int(cpcPrice * (1000 * rate / 100))
			}
		}

		iterTime := time.Since(start)
		totalTime += iterTime
		fmt.Printf("测试 #%d: 价格字符串: %s, 解密结果: %d, 耗时: %s\n", i+1, p, winPrice, iterTime)
	}

	fmt.Printf("平均耗时: %s\n", totalTime/time.Duration(len(prices)))
}
