package api

import (
	"github.com/gin-gonic/gin"
	"mh_proxy/utils"
	"runtime"
)

// Test http://localhost:8081/api/test
func Test(c *gin.Context) {
	// fmt.Println("runtime num cpu:", runtime.NumCPU())
	// fmt.Println("runtime go max procs:", runtime.GOMAXPROCS(-1))

	cpuNum := utils.ConvertIntToString(runtime.NumCPU())
	goMaxProcsNum := utils.ConvertIntToString(runtime.GOMAXPROCS(-1))

	c.JSON(200, gin.H{
		"message": "ssp-deal-core server OK" + ", runtime num cpu: " + cpuNum + ", runtime GOMAXPROCS: " + goMaxProcsNum,
	})
}
