package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/models"
	"mh_proxy/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// SdkDebug debug
func SdkDebug(c *gin.Context) {
	fmt.Println("sdk debug")
	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk debug:", len(bodyContent))

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk debug encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}
	// fmt.Println("kbg_debug sdk debug body:", string(bodyContent))

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk debug parser error:", err)
	}

	// hack ad_count
	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	// 解密device
	if len(mhReq.DID) > 0 {
		// var deviceDIDStu models.MHReqDevice
		didDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(deviceStu)
		_ = json.Unmarshal(didDecrypt, &mhReq.Device)
	}

	// 解密geo
	if len(mhReq.LBS) > 0 {
		// fmt.Println("geo encode:", mhReq.LBS)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.LBS)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println("geo decode:", string(geoDecrypt))
		_ = json.Unmarshal(geoDecrypt, &mhReq.Geo)
	}

	// save to holo

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println(err)
			}
		}()
		models.BigDataHoloDebugSdkTrack(mhReq)
	}()

	c.PureJSON(200, nil)
}
