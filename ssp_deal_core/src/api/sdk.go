package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/core/up_common"
	"mh_proxy/db"
	"mh_proxy/device"
	"mh_proxy/device/dau"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	u "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/ip_to_region/core/ip2location"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// SdkConfig ...
func SdkConfig(c *gin.Context) {
	// fmt.Println("sdk begin")
	// apiVersion := c.Query("api_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	// fmt.Println(apiVersion)
	// fmt.Println(pos)
	// fmt.Println(media)
	// fmt.Println(device)
	// fmt.Println(network)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)
	// fmt.Println(posStu.Width)
	// fmt.Println(posStu.Height)
	// fmt.Println(posStu.AdCount)

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)
	// fmt.Println("os: " + deviceStu.Os)
	// fmt.Println("osv: " + deviceStu.Os_version)
	// fmt.Println("osv: " + deviceStu.Model)
	// fmt.Println("osv: " + deviceStu.Manufacturer)
	// fmt.Println(deviceStu.Device_type)
	// fmt.Println(deviceStu.Screen_width)
	// fmt.Println(deviceStu.Screen_height)
	// fmt.Println(deviceStu.Orientation)
	// fmt.Println("imei: " + deviceStu.Imei)
	// fmt.Println("imei_md5: " + deviceStu.Imei_md5)
	// fmt.Println("android_id: " + deviceStu.Android_id)
	// fmt.Println("android_id_md5: " + deviceStu.Android_id_md5)
	// fmt.Println("oaid: " + deviceStu.Oaid)
	// fmt.Println("idfa: " + deviceStu.Idfa)
	// fmt.Println("idfa_md5: " + deviceStu.Idfa_md5)

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)
	// fmt.Println(networkStu.Connect_type)
	// fmt.Println(networkStu.Carrier)
	if networkStu.ConnectType == 5 {
		networkStu.ConnectType = 7
	}

	// geo
	geoStu := models.MHReqGeo{}
	json.Unmarshal([]byte(geo), &geoStu)

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()
	// fmt.Println("http ua: " + ua)
	// fmt.Println("http ip: " + ip)

	// fmt.Println("device ua before: " + deviceStu.Ua)
	// fmt.Println("device ip before: " + deviceStu.Ip)
	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	// fmt.Println("device ua after: " + deviceStu.Ua)
	// fmt.Println("device ip after: " + deviceStu.IP)

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu

	respStu := core.GetSDKConfigFromAdx(c, &reqStu)
	// fmt.Println(respStu)
	// time.Sleep(5 * time.Second)
	c.PureJSON(200, respStu)

	// c.JSON(200, gin.H{
	// 	"message": "pong v1 1",
	// })
	// fmt.Println("sdk end")
}

// SdkConfigV2 ...
func SdkConfigV2(c *gin.Context) {
	// fmt.Println("sdk v2 begin")
	// apiVersion := c.Query("api_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	// fmt.Println(apiVersion)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)
	if networkStu.ConnectType == 5 {
		networkStu.ConnectType = 7
	}
	// geo
	geoStu := models.MHReqGeo{}
	json.Unmarshal([]byte(geo), &geoStu)

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu
	reqStu.SDKVersion = c.Query("sdk_version")

	respStu := core.GetSDKConfigV2FromAdx(c, &reqStu)
	// fmt.Println(respStu)
	// time.Sleep(5 * time.Second)
	c.PureJSON(200, respStu)

	// c.JSON(200, gin.H{
	// 	"message": "pong v1 1",
	// })
	// fmt.Println("sdk end")
}

// SdkConfigV3 ...
func SdkConfigV3(c *gin.Context) {
	// fmt.Println("sdk v2 begin")
	// apiVersion := c.Query("api_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	did := c.Query("did")
	// fmt.Println(apiVersion)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)

	// hack ad_count
	if posStu.AdCount == 0 {
		posStu.AdCount = 1
	}

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(did)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(deviceStu)
	json.Unmarshal([]byte(didDecrypt), &deviceStu)
	// fmt.Println(deviceStu)

	// sdk osv < 9 return
	if deviceStu.Os == "android" {
		osvTmpVersion1 := 0
		osvTmpVersionArray := strings.Split(deviceStu.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				respMap := map[string]interface{}{}
				respMap["ret"] = 102006
				respMap["msg"] = ""

				c.PureJSON(200, respMap)
				return
			}
		}
		if osvTmpVersion1 < 9 {
			respMap := map[string]interface{}{}
			respMap["ret"] = 102006
			respMap["msg"] = ""

			c.PureJSON(200, respMap)
			return
		}
	}

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)
	if networkStu.ConnectType == 5 {
		networkStu.ConnectType = 7
	}

	// geo
	geoStu := models.MHReqGeo{}
	json.Unmarshal([]byte(geo), &geoStu)

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu
	reqStu.SDKVersion = c.Query("sdk_version")

	respStu := core.GetSDKConfigV2FromAdx(c, &reqStu)
	// fmt.Println(respStu)
	// time.Sleep(5 * time.Second)
	c.PureJSON(200, respStu)
}

// SdkConfigV30 sdk获取配置3.0版本
func SdkConfigV30(c *gin.Context) {
	// fmt.Println("sdk v3.0 begin")
	// apiVersion := c.Query("api_version")
	sdkVersion := c.Query("sdk_version")
	pos := c.Query("pos")
	media := c.Query("media")
	device := c.Query("device")
	network := c.Query("network")
	geo := c.Query("geo")
	did := c.Query("did")
	// fmt.Println(apiVersion)

	// app
	appStu := models.MHReqApp{}
	json.Unmarshal([]byte(media), &appStu)
	// fmt.Println(appStu.AppID)
	// fmt.Println(appStu.AppBundleID)

	// pos
	posStu := models.MHReqPos{}
	json.Unmarshal([]byte(pos), &posStu)
	// fmt.Println(posStu.ID)

	// hack ad_count
	if posStu.AdCount == 0 {
		posStu.AdCount = 1
	}

	// device
	deviceStu := models.MHReqDevice{}
	json.Unmarshal([]byte(device), &deviceStu)

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(did)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(deviceStu)
	json.Unmarshal([]byte(didDecrypt), &deviceStu)
	// fmt.Println(deviceStu)

	// network
	networkStu := models.MHReqNetwork{}
	json.Unmarshal([]byte(network), &networkStu)
	if networkStu.ConnectType == 5 {
		networkStu.ConnectType = 7
	}

	// geo
	geoStu := models.MHReqGeo{}
	// json.Unmarshal([]byte(geo), &geoStu)
	if len(sdkVersion) > 0 && utils.ConvertStringToInt(strings.Split(sdkVersion, ".")[0]) >= 3 && len(geo) > 0 {
		// fmt.Println("geo:", geo)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(geo)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &geoStu)
	}

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	if len(deviceStu.Ua) > 0 {
	} else {
		deviceStu.Ua = ua
	}
	if len(deviceStu.IP) > 0 {
	} else {
		deviceStu.IP = ip
	}

	var reqStu models.MHReq
	reqStu.App = appStu
	reqStu.Pos = posStu
	reqStu.Device = deviceStu
	reqStu.Network = networkStu
	reqStu.Geo = geoStu
	reqStu.SDKVersion = sdkVersion

	// sdk osv < 9 return
	if deviceStu.Os == "android" {
		osvTmpVersion1 := 0
		osvTmpVersionArray := strings.Split(deviceStu.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				respMap := map[string]interface{}{}
				respMap["ret"] = 102006
				respMap["msg"] = ""

				c.PureJSON(200, respMap)
				return
			}
		}
		if osvTmpVersion1 < 9 {
			respMap := map[string]interface{}{}
			respMap["ret"] = 102006
			respMap["msg"] = ""

			c.PureJSON(200, respMap)
			return
		}
	}

	respStu := core.GetSDKConfigV30FromAdx(c, &reqStu)
	// fmt.Println(respStu)
	// time.Sleep(5 * time.Second)
	// fmt.Println("sdk v3.0 end")

	// 验证最后返回是否有效
	isRespVaild := isMHRespSDKConfigVaild(c, respStu)
	if isRespVaild {
	} else {
		fmt.Println("kbg_debug_sdk_config_resp is not valid 105002")
		respStu = core.MhErrorRespMap(105002, "")
	}

	if len(reqStu.Device.Oaid) == 0 {
		if respStu != nil {
			(*respStu)["msg"] = "warning! no oaid no money!"
		}
	}

	c.PureJSON(200, respStu)
}

// SdkConfigV33 sdk获取配置3.3版本
func SdkConfigV33(c *gin.Context) {
	// fmt.Println("sdk v3.3 begin")
	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk config 3.3:", len(bodyContent))

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk config 3.3 encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, err := gzip.NewReader(buf)
		if err != nil {
			respMap := map[string]interface{}{}
			respMap["ret"] = 102006
			respMap["msg"] = ""

			c.PureJSON(200, respMap)
			return
		}
		defer reader.Close()
		bodyContent, err = io.ReadAll(reader)
		if err != nil {
			respMap := map[string]interface{}{}
			respMap["ret"] = 102006
			respMap["msg"] = ""

			c.PureJSON(200, respMap)
			return
		}
	}
	// fmt.Println("kbg_debug sdk config 3.3 body:", string(bodyContent))

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk config parser error:", err)
	}

	// hack ad_count
	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	// 解密device
	if len(mhReq.DID) > 0 {
		// var deviceDIDStu models.MHReqDevice
		didDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(deviceStu)
		json.Unmarshal([]byte(didDecrypt), &mhReq.Device)
	}

	// 解密geo
	if len(mhReq.LBS) > 0 {
		// fmt.Println("geo encode:", mhReq.LBS)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.LBS)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println("geo decode:", string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &mhReq.Geo)
	}

	// fmt.Println("model:", mhReq.Device.Model)
	// fmt.Println("manufacturer:", mhReq.Device.Manufacturer)
	// fmt.Println("oaid:", mhReq.Device.Oaid)
	// fmt.Println("ua:", mhReq.Device.Ua)
	// fmt.Println("ip:", mhReq.Device.IP)
	// fmt.Println("pos:", mhReq.Pos)
	// fmt.Println("app:", mhReq.App)
	// fmt.Println("network:", mhReq.Network)
	// fmt.Println("geo:", mhReq.Geo)

	// sdk osv < 9 return
	if mhReq.Device.Os == "android" {
		osvTmpVersion1 := 0
		osvTmpVersionArray := strings.Split(mhReq.Device.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				respMap := map[string]interface{}{}
				respMap["ret"] = 102006
				respMap["msg"] = ""

				c.PureJSON(200, respMap)
				return
			}
		}
		if osvTmpVersion1 < 9 {
			respMap := map[string]interface{}{}
			respMap["ret"] = 102006
			respMap["msg"] = ""

			c.PureJSON(200, respMap)
			return
		}
	}

	if core.IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 4) {
	} else {
		if mhReq.Network.ConnectType == 5 {
			mhReq.Network.ConnectType = 7
		}
	}

	respStu := core.GetSDKConfigV33FromAdx(c, &mhReq)

	// 验证最后返回是否有效
	// isRespVaild := isMHRespSDKConfigVaild(c, respStu)
	// if isRespVaild {
	// } else {
	// 	fmt.Println("kbg_debug_sdk_config_resp is not valid 105002")
	// 	respStu = core.MhErrorRespMap(105002, "")
	// }

	if len(mhReq.Device.Oaid) == 0 {
		if respStu != nil {
			(*respStu)["msg"] = "warning! no oaid no money!"
		}
	}

	data := (*respStu)["data"]
	reqid := (*respStu)["reqid"]
	marshal, _ := json.Marshal(data)

	if strVal, ok := reqid.(string); ok {
		go models.BigDataHoloSdkOaidDebug(strVal, marshal, &mhReq)
	}

	c.PureJSON(200, respStu)
}

// SdkConfigV34 sdk获取配置3.4版本
func SdkConfigV34(c *gin.Context) {
	// fmt.Println("sdk v3.3 begin")
	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk config 3.3:", len(bodyContent))

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk config 3.3 encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, err := gzip.NewReader(buf)
		if err != nil {
			tmpRespMap := map[string]interface{}{}
			tmpRespMap["ret"] = 102006
			tmpRespMap["msg"] = ""

			tmpJsonData, _ := json.Marshal(tmpRespMap)
			respMap := map[string]interface{}{}
			respMap["config"] = utils.AesECBEncrypt([]byte(tmpJsonData), []byte(config.EncryptKEY))

			c.PureJSON(200, respMap)
			return
		}
		defer reader.Close()
		bodyContent, err = io.ReadAll(reader)
		if err != nil {
			tmpRespMap := map[string]interface{}{}
			tmpRespMap["ret"] = 102006
			tmpRespMap["msg"] = ""

			tmpJsonData, _ := json.Marshal(tmpRespMap)
			respMap := map[string]interface{}{}
			respMap["config"] = utils.AesECBEncrypt([]byte(tmpJsonData), []byte(config.EncryptKEY))

			c.PureJSON(200, respMap)
			return
		}
	}

	// fmt.Println("kbg_debug sdk config 3.3 body:", string(bodyContent))

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk config parser error:", err)
	}

	// hack ad_count
	if mhReq.Pos.AdCount == 0 {
		mhReq.Pos.AdCount = 1
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	// 解密device
	if len(mhReq.DID) > 0 {
		// var deviceDIDStu models.MHReqDevice
		didDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.DID)
		didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println(deviceStu)
		json.Unmarshal([]byte(didDecrypt), &mhReq.Device)

		if len(mhReq.Device.CAIDMulti) == 0 && len(mhReq.Device.SpecMulti) > 0 {
			for _, item := range mhReq.Device.SpecMulti {
				var caidItem models.MHReqCAIDMulti
				caidItem.CAID = item.Spec
				caidItem.CAIDVersion = item.SpecVersion
				mhReq.Device.CAIDMulti = append(mhReq.Device.CAIDMulti, caidItem)
			}
		}
	}

	// 解密geo
	if len(mhReq.LBS) > 0 {
		// fmt.Println("geo encode:", mhReq.LBS)
		geoDecryptBase64, _ := base64.StdEncoding.DecodeString(mhReq.LBS)
		geoDecrypt := utils.AesECBDecrypt(geoDecryptBase64, []byte(config.EncryptKEY))
		// fmt.Println("geo decode:", string(geoDecrypt))
		json.Unmarshal([]byte(geoDecrypt), &mhReq.Geo)
	}

	// fmt.Println("model:", mhReq.Device.Model)
	// fmt.Println("manufacturer:", mhReq.Device.Manufacturer)
	// fmt.Println("oaid:", mhReq.Device.Oaid)
	// fmt.Println("ua:", mhReq.Device.Ua)
	// fmt.Println("ip:", mhReq.Device.IP)
	// fmt.Println("pos:", mhReq.Pos)
	// fmt.Println("app:", mhReq.App)
	// fmt.Println("network:", mhReq.Network)
	// fmt.Println("geo:", mhReq.Geo)

	// sdk osv < 9 return
	if mhReq.Device.Os == "android" {
		osvTmpVersion1 := 0
		osvTmpVersionArray := strings.Split(mhReq.Device.OsVersion, ".")
		if len(osvTmpVersionArray) > 0 {
			if utils.IsNum(osvTmpVersionArray[0]) {
				osvTmpVersion1 = utils.ConvertStringToInt(osvTmpVersionArray[0])
			} else {
				tmpRespMap := map[string]interface{}{}
				tmpRespMap["ret"] = 102006
				tmpRespMap["msg"] = ""

				tmpJsonData, _ := json.Marshal(tmpRespMap)
				respMap := map[string]interface{}{}
				respMap["config"] = utils.AesECBEncrypt([]byte(tmpJsonData), []byte(config.EncryptKEY))

				c.PureJSON(200, respMap)
				return
			}
		}
		if osvTmpVersion1 < 9 {
			tmpRespMap := map[string]interface{}{}
			tmpRespMap["ret"] = 102006
			tmpRespMap["msg"] = ""

			tmpJsonData, _ := json.Marshal(tmpRespMap)
			respMap := map[string]interface{}{}
			respMap["config"] = utils.AesECBEncrypt([]byte(tmpJsonData), []byte(config.EncryptKEY))

			c.PureJSON(200, respMap)
			return
		}
	}

	if core.IsSDKVersionMoreThanOrEqualVersionCode(c, mhReq.SDKVersion, 3, 3, 4) {
	} else {
		if mhReq.Network.ConnectType == 5 {
			mhReq.Network.ConnectType = 7
		}
	}

	respStu := core.GetSDKConfigV33FromAdx(c, &mhReq)

	// 验证最后返回是否有效
	// isRespVaild := isMHRespSDKConfigVaild(c, respStu)
	// if isRespVaild {
	// } else {
	// 	fmt.Println("kbg_debug_sdk_config_resp is not valid 105002")
	// 	respStu = core.MhErrorRespMap(105002, "")
	// }

	if len(mhReq.Device.Oaid) == 0 {
		if respStu != nil {
			(*respStu)["msg"] = "warning! no oaid no money!"
		}
	}

	tmpJsonData, _ := json.Marshal(respStu)
	respMap := map[string]interface{}{}
	respMap["config"] = utils.AesECBEncrypt([]byte(tmpJsonData), []byte(config.EncryptNewKEY))

	data := (*respStu)["data"]
	reqid := (*respStu)["reqid"]
	marshal, _ := json.Marshal(data)

	if strVal, ok := reqid.(string); ok {
		go models.BigDataHoloSdkOaidDebug(strVal, marshal, &mhReq)
	}

	c.PureJSON(200, respMap)
}

// isMHRespVaild ...
func isMHRespSDKConfigVaild(c *gin.Context, respData *map[string]interface{}) bool {
	tmpDataByte, _ := json.Marshal(respData)
	tmpMHSDKConfigResp := MHSDKConfigResp{}
	json.Unmarshal(tmpDataByte, &tmpMHSDKConfigResp)

	if tmpMHSDKConfigResp.Ret == 1 {
		// fmt.Println("kbg_debug_resp len():", len(tmpMHSDKConfigResp.Data))
		if len(tmpMHSDKConfigResp.Data) == 0 {
			return false
		}
	}

	return true
}

// MHSDKConfigResp ...
type MHSDKConfigResp struct {
	Ret  int                   `json:"ret"`
	Data []MHSDKConfigDataResp `json:"data"`
}

// MHSDKConfigResp ...
type MHSDKConfigDataResp struct {
	FinalPrice         int    `json:"final_price"`
	FloorPrice         int    `json:"floor_price"`
	Mode               string `json:"mode"`
	PlatformAppID      string `json:"platform_app_id"`
	PlatformMediaID    string `json:"platform_media_id"`
	PlatformPosID      string `json:"platform_pos_id"`
	PlatformPosSubType int    `json:"platform_pos_sub_type"`
	PlatformPosType    int    `json:"platform_pos_type"`
}

// SdkReportSensorBattory ...
func SdkReportSensorBattory(c *gin.Context) {
	bodyContent, _ := c.GetRawData()

	// 解密data
	decryptDataBase64, _ := base64.StdEncoding.DecodeString(string(bodyContent))
	decryptData := utils.AesECBDecrypt(decryptDataBase64, []byte(config.EncryptKEY))
	fmt.Println("decode xxx:", string(decryptData))

	// 解析sensor
	var sensorbatteryStu models.SensorBatteryDataStu
	json.Unmarshal(decryptData, &sensorbatteryStu)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata click_xy panic:", err)
			}
		}()

		// 存bigdata TODO
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok report"
	c.PureJSON(200, resp)
}

// SdkExtraNull ...
func SdkExtraNull(c *gin.Context) {
}

// SdkExtraCN ...
func SdkExtraCN(c *gin.Context) {
	fmt.Println("sdk extra cn")
	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, err := gzip.NewReader(buf)
		if err != nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		}
		defer reader.Close()
		bodyContent, err = io.ReadAll(reader)
		if err != nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		}
	}

	var sdkExtraCNW SDKExtraCNW
	json.Unmarshal([]byte(bodyContent), &sdkExtraCNW)
	// fmt.Println(sdkExtraCNW.DID)

	// get appid config by bundle
	if len(sdkExtraCNW.LocalAppID) > 0 {
		localPosInfo := models.GetLocalAppExtraInfoByAppID(c, sdkExtraCNW.LocalAppID)
		if localPosInfo == nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		} else {
			sdkExtraCNW.LocalAppID = localPosInfo.LocalAppID
			sdkExtraCNW.LocalAppBundle = localPosInfo.LocalAppBundleID
			sdkExtraCNW.ExtraClipBoard = localPosInfo.LocalAppSupportExtraClipboard
			sdkExtraCNW.ExtraNotification = localPosInfo.LocalAppSupportExtraNotification
			sdkExtraCNW.ExtraWakeUp = localPosInfo.LocalAppSupportExtraWakeUp
		}
	} else if len(sdkExtraCNW.LocalAppBundle) > 0 {
		localPosInfo := models.GetLocalAppExtraInfoByBundle(c, sdkExtraCNW.LocalAppBundle)
		if localPosInfo == nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		} else {
			sdkExtraCNW.LocalAppID = localPosInfo.LocalAppID
			sdkExtraCNW.LocalAppBundle = localPosInfo.LocalAppBundleID
			sdkExtraCNW.ExtraClipBoard = localPosInfo.LocalAppSupportExtraClipboard
			sdkExtraCNW.ExtraNotification = localPosInfo.LocalAppSupportExtraNotification
			sdkExtraCNW.ExtraWakeUp = localPosInfo.LocalAppSupportExtraWakeUp
		}
	} else {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	if sdkExtraCNW.ExtraClipBoard == 0 && sdkExtraCNW.ExtraNotification == 0 {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	deviceStu := models.MHReqDevice{}

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkExtraCNW.DID)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	json.Unmarshal([]byte(didDecrypt), &deviceStu)

	if sdkExtraCNW.LocalAppID == "10012" {
		isExtraWhite := false
		cacheValue, cacheError := db.GlbBigCacheMinute.Get("mh_extra_white_did")
		if cacheError != nil {
		} else {
			if len(deviceStu.Oaid) > 3 && strings.Contains(string(cacheValue), deviceStu.Oaid) {
				fmt.Println("adx contain oaid")
				isExtraWhite = true
			}
		}

		if isExtraWhite {
		} else {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1
			// fmt.Println("kbg_debug extra black 10012")

			c.PureJSON(200, respErrorExtra)
			return
		}
	}

	deviceStu.Os = "android"
	deviceStu.Ua = ua
	deviceStu.IP = ip
	// imei, android_id, oaid去空格
	deviceStu.Imei = strings.Replace(deviceStu.Imei, " ", "", -1)
	deviceStu.AndroidID = strings.Replace(deviceStu.AndroidID, " ", "", -1)
	deviceStu.Oaid = strings.Replace(deviceStu.Oaid, " ", "", -1)

	// fmt.Println(deviceStu)
	var mhReqStu models.MHReq
	mhReqStu.Device = deviceStu
	// 空did_md5直接返回
	tmpDeviceIDKey := models.GetDeviceIDMd5Key(&mhReqStu)
	if tmpDeviceIDKey == utils.Get16Md5("") {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	reqStu := core.DspReqStu{
		SDKVersion: sdkExtraCNW.SDKVersion,
		Pos: core.DspReqPosStu{
			PlanID:      "",
			Width:       0,
			Height:      0,
			SupplyAppID: sdkExtraCNW.LocalAppID,
		},
		Device: core.DspReqDeviceStu{
			Os:           "android",
			OsVersion:    deviceStu.OsVersion,
			Model:        deviceStu.Model,
			Manufacturer: deviceStu.Manufacturer,
			Imei:         deviceStu.Imei,
			ImeiMd5:      deviceStu.ImeiMd5,
			AndroidID:    deviceStu.AndroidID,
			AndroidIDMd5: deviceStu.AndroidIDMd5,
			Oaid:         deviceStu.Oaid,
			DIDMd5Key:    tmpDeviceIDKey,
			Ua:           deviceStu.Ua,
			IP:           deviceStu.IP,
		},
		ExtraClipboard:    sdkExtraCNW.ExtraClipBoard,
		ExtraNotification: sdkExtraCNW.ExtraNotification,
	}

	jsonData, _ := json.Marshal(reqStu)
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(200)*time.Millisecond, http.MethodPost, config.UpDspURL+"/extra/cn",
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	if err != nil {
		return
	}

	if statusCode != 200 {
		respExtra := map[string]interface{}{}
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// resp
	dspExtraResp := DspExtraRespStu{}
	json.Unmarshal([]byte(bodyContent), &dspExtraResp)

	respExtra := map[string]interface{}{}
	if dspExtraResp.Code != 10000 {
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}

	if len(dspExtraResp.ExtraClipboard.ClipboardText) == 0 &&
		len(dspExtraResp.ExtraNotification.NotificationDeepLink) == 0 {
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}

	respExtra["ret"] = 0

	var mhReq models.MHReq
	mhReq.Device = deviceStu
	mhReq.SDKVersion = sdkExtraCNW.SDKVersion

	var localPos models.LocalPosStu
	localPos.LocalAppID = sdkExtraCNW.LocalAppID

	var platformPos models.PlatformPosStu
	platformPos.PlatformAppID = "sop"

	if len(dspExtraResp.ExtraClipboard.ClipboardText) > 0 {
		localPos.LocalPosID = "99997"
		platformPos.PlatformPosID = "b08d168e365122f0"

		bigdataUID := uuid.NewV4().String()

		// impression_link maplehaze
		mhImpParams := url.Values{}
		mhImpParams.Add("app_id", sdkExtraCNW.LocalAppID)
		bigdataParams := up_common.EncodeParams(&mhReq, &localPos, &platformPos, bigdataUID, bigdataUID, 0, 0, 0, 0)
		mhImpParams.Add("log", bigdataParams)

		var respListItemImpArray []string
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		for _, impItem := range dspExtraResp.ExtraClipboard.ExpLinks {
			respListItemImpArray = append(respListItemImpArray, impItem)
		}

		dspExtraResp.ExtraClipboard.ExpLinks = respListItemImpArray

		tmpEncryptClipboard := utils.AesECBEncrypt([]byte(dspExtraResp.ExtraClipboard.ClipboardText), []byte(config.EncryptKEY))
		dspExtraResp.ExtraClipboard.ClipboardTextEncrypt = base64.StdEncoding.EncodeToString(tmpEncryptClipboard)

		respExtra["extra_clipboard"] = dspExtraResp.ExtraClipboard
	}
	if len(dspExtraResp.ExtraNotification.NotificationDeepLink) > 0 {
		localPos.LocalPosID = "99998"
		platformPos.PlatformPosID = "74870e763bf3ec7e"

		bigdataUID := uuid.NewV4().String()

		// impression_link maplehaze
		mhImpParams := url.Values{}
		mhImpParams.Add("app_id", sdkExtraCNW.LocalAppID)
		bigdataParams := up_common.EncodeParams(&mhReq, &localPos, &platformPos, bigdataUID, bigdataUID, 0, 0, 0, 0)
		mhImpParams.Add("log", bigdataParams)

		var respListItemImpArray []string
		respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

		for _, impItem := range dspExtraResp.ExtraNotification.ExpLinks {
			respListItemImpArray = append(respListItemImpArray, impItem)
		}

		dspExtraResp.ExtraNotification.ExpLinks = respListItemImpArray

		respExtra["extra_notification"] = dspExtraResp.ExtraNotification
	}

	c.PureJSON(200, respExtra)
	return
}

// SdkExtraCP ...
func SdkExtraCP(c *gin.Context) {
	// fmt.Println("sdk extra cp")
	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, err := gzip.NewReader(buf)
		if err != nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		}
		defer reader.Close()
		bodyContent, err = io.ReadAll(reader)
		if err != nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		}
	}

	var sdkExtraCNW SDKExtraCNW
	json.Unmarshal([]byte(bodyContent), &sdkExtraCNW)
	// fmt.Println(sdkExtraCNW.DID)

	// get appid config by bundle
	if len(sdkExtraCNW.LocalAppID) > 0 {
		localPosInfo := models.GetLocalAppExtraInfoByAppID(c, sdkExtraCNW.LocalAppID)
		if localPosInfo == nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		} else {
			sdkExtraCNW.LocalAppID = localPosInfo.LocalAppID
			sdkExtraCNW.LocalAppBundle = localPosInfo.LocalAppBundleID
			sdkExtraCNW.ExtraClipBoard = localPosInfo.LocalAppSupportExtraClipboard
		}
	} else if len(sdkExtraCNW.LocalAppBundle) > 0 {
		localPosInfo := models.GetLocalAppExtraInfoByBundle(c, sdkExtraCNW.LocalAppBundle)
		if localPosInfo == nil {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1

			c.PureJSON(200, respErrorExtra)
			return
		} else {
			sdkExtraCNW.LocalAppID = localPosInfo.LocalAppID
			sdkExtraCNW.LocalAppBundle = localPosInfo.LocalAppBundleID
			sdkExtraCNW.ExtraClipBoard = localPosInfo.LocalAppSupportExtraClipboard
		}
	} else {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	if sdkExtraCNW.ExtraClipBoard == 0 {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	deviceStu := models.MHReqDevice{}

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkExtraCNW.DID)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	json.Unmarshal([]byte(didDecrypt), &deviceStu)

	if sdkExtraCNW.LocalAppID == "10012" {
		isExtraWhite := false
		cacheValue, cacheError := db.GlbBigCacheMinute.Get("mh_extra_white_did")
		if cacheError != nil {
		} else {
			if len(deviceStu.Oaid) > 3 && strings.Contains(string(cacheValue), deviceStu.Oaid) {
				fmt.Println("adx contain oaid")
				isExtraWhite = true
			}
		}

		if isExtraWhite {
		} else {
			respErrorExtra := map[string]interface{}{}
			respErrorExtra["ret"] = 1
			// fmt.Println("kbg_debug extra black 10012")

			c.PureJSON(200, respErrorExtra)
			return
		}
	}

	deviceStu.Os = "android"
	deviceStu.Ua = ua
	deviceStu.IP = ip
	// imei, android_id, oaid去空格
	deviceStu.Imei = strings.Replace(deviceStu.Imei, " ", "", -1)
	deviceStu.AndroidID = strings.Replace(deviceStu.AndroidID, " ", "", -1)
	deviceStu.Oaid = strings.Replace(deviceStu.Oaid, " ", "", -1)

	// fmt.Println(deviceStu)
	var mhReqStu models.MHReq
	mhReqStu.Device = deviceStu
	// 空did_md5直接返回
	tmpDeviceIDKey := models.GetDeviceIDMd5Key(&mhReqStu)
	if tmpDeviceIDKey == utils.Get16Md5("") {
		respErrorExtra := map[string]interface{}{}
		respErrorExtra["ret"] = 1

		c.PureJSON(200, respErrorExtra)
		return
	}

	reqStu := core.DspReqStu{
		SDKVersion: sdkExtraCNW.SDKVersion,
		Pos: core.DspReqPosStu{
			PlanID:      "",
			Width:       0,
			Height:      0,
			SupplyAppID: sdkExtraCNW.LocalAppID,
		},
		Device: core.DspReqDeviceStu{
			Os:           "android",
			OsVersion:    deviceStu.OsVersion,
			Model:        deviceStu.Model,
			Manufacturer: deviceStu.Manufacturer,
			Imei:         deviceStu.Imei,
			ImeiMd5:      deviceStu.ImeiMd5,
			AndroidID:    deviceStu.AndroidID,
			AndroidIDMd5: deviceStu.AndroidIDMd5,
			Oaid:         deviceStu.Oaid,
			DIDMd5Key:    tmpDeviceIDKey,
			Ua:           deviceStu.Ua,
			IP:           deviceStu.IP,
		},
		ExtraClipboard: sdkExtraCNW.ExtraClipBoard,
	}

	jsonData, _ := json.Marshal(reqStu)
	////////////////////////////////////////////////////////////////////////////////////////
	// send req
	httpHeader := map[string]string{}
	httpHeader["Content-Type"] = "application/json; charset=utf-8"
	httpHeader["Connection"] = "keep-alive"

	bodyContent, statusCode, err := utils.GetHttpClient().DoWithTimeout(c, time.Duration(200)*time.Millisecond, http.MethodPost, config.UpDspURL+"/extra/cp",
		u.WithHeaders(httpHeader),
		u.WithJSONBody(jsonData))

	if err != nil {
		return
	}

	if statusCode != 200 {
		respExtra := map[string]interface{}{}
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// resp
	dspExtraResp := DspExtraClipboardRespStu{}
	json.Unmarshal([]byte(bodyContent), &dspExtraResp)

	respExtra := map[string]interface{}{}
	if dspExtraResp.Code != 10000 {
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}

	if len(dspExtraResp.ExtraClipboard) == 0 {
		respExtra["ret"] = 1

		c.PureJSON(200, respExtra)
		return
	}

	respExtra["ret"] = 0

	var mhReq models.MHReq
	mhReq.Device = deviceStu
	mhReq.SDKVersion = sdkExtraCNW.SDKVersion

	var localPos models.LocalPosStu
	localPos.LocalAppID = sdkExtraCNW.LocalAppID

	var platformPos models.PlatformPosStu
	platformPos.PlatformAppID = "sop"

	var tmpExtraClipboardArray []ExtraClipboardStu
	for _, item := range dspExtraResp.ExtraClipboard {
		var tmpExtraClipboard ExtraClipboardStu

		tmpEncryptClipboard := utils.AesECBEncrypt([]byte(item.ClipboardText), []byte(config.EncryptKEY))
		tmpExtraClipboard.ClipboardTextEncrypt = base64.StdEncoding.EncodeToString(tmpEncryptClipboard)

		tmpExtraClipboard.PackageName = item.PackageName
		tmpExtraClipboard.Weight = item.Weight

		localPos.LocalPosID = "99997"
		platformPos.PlatformPosID = "b08d168e365122f0"

		bigdataUID := uuid.NewV4().String()
		// impression_link maplehaze
		mhImpParams := url.Values{}
		mhImpParams.Add("app_id", sdkExtraCNW.LocalAppID)
		bigdataParams := up_common.EncodeParams(&mhReq, &localPos, &platformPos, bigdataUID, bigdataUID, 0, 0, 0, 0)
		mhImpParams.Add("log", bigdataParams)

		tmpExtraClipboard.ExpLinks = append(tmpExtraClipboard.ExpLinks, config.ExternalExpURL+"?"+mhImpParams.Encode())
		tmpExtraClipboard.ExpLinks = append(tmpExtraClipboard.ExpLinks, item.ExpLinks...)

		tmpExtraClipboardArray = append(tmpExtraClipboardArray, tmpExtraClipboard)
	}

	respExtra["extra_clipboards"] = tmpExtraClipboardArray

	c.PureJSON(200, respExtra)
}

// SdkExtraWK ...
func SdkExtraWK(c *gin.Context) {
	fmt.Println("sdk extra wakeup")
	respErrorExtra := map[string]interface{}{}
	respErrorExtra["ret"] = 1

	c.PureJSON(200, respErrorExtra)
}

// SdkExtraWKConfig ...
func SdkExtraWKConfig(c *gin.Context) {
	fmt.Println("sdk extra wakeup config")
	respErrorExtra := map[string]interface{}{}
	respErrorExtra["ret"] = 1

	c.PureJSON(200, respErrorExtra)
}

// DspExtraRespStu ...
type DspExtraRespStu struct {
	Code              int                  `json:"code"`
	ExtraClipboard    ExtraClipboardStu    `json:"extra_clipboard"`
	ExtraNotification ExtraNotificationStu `json:"extra_notification"`
	ExtraWakeUp       ExtraWakeUpStu       `json:"extra_wakeup"`
}

// ExtraNotificationStu ...
type ExtraNotificationStu struct {
	NotificationTitle        string `json:"notification_title"`
	NotificationDescription  string `json:"notification_description"`
	NotificationIconURL      string `json:"notification_icon_url"`
	NotificationDeepLink     string `json:"notification_dp_link"`
	NotificationH5Link       string `json:"notification_h5_link"`
	NotificationDownloadLink string `json:"notification_download_link"`

	ExpLinks []string `json:"exp_links"`
	// ClkLinks []string `json:"clk_links"`
}

// ExtraClipboardStu ...
type ExtraClipboardStu struct {
	ClipboardText string   `json:"clipboard_text,omitempty"`
	ExpLinks      []string `json:"exp_links"`
	// 加密的剪贴板
	ClipboardTextEncrypt string `json:"cte"`

	// 包名
	PackageName string `json:"package_name,omitempty"`
	// 比重
	Weight int `json:"weight"`
}

// ExtraWakeUpStu ...
type ExtraWakeUpStu struct {
	WakeUpDeepLink string   `json:"wakeup_dp_link"`
	ExpLinks       []string `json:"exp_links"`
	// ClkLinks       []string `json:"clk_links"`
}

// DspExtraClipboardRespStu ...
type DspExtraClipboardRespStu struct {
	Code           int                 `json:"code"`
	ExtraClipboard []ExtraClipboardStu `json:"extra_clipboards"`
}

// SdkExtraAL ...
func SdkExtraAL(c *gin.Context) {
	fmt.Println("sdk extra al")

	ua := c.GetHeader("User-Agent")
	ip := c.ClientIP()

	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, err := gzip.NewReader(buf)
		if err != nil {
			return
		}
		defer reader.Close()
		bodyContent, err = io.ReadAll(reader)
		if err != nil {
			return
		}
	}

	var sdkExtraAL SDKExtraAL
	_ = json.Unmarshal(bodyContent, &sdkExtraAL)
	if len(sdkExtraAL.ALP) == 0 {
		return
	}

	deviceStu := models.MHReqDevice{}

	// did
	didDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkExtraAL.DID)
	didDecrypt := utils.AesECBDecrypt(didDecryptBase64, []byte(config.EncryptKEY))
	_ = json.Unmarshal(didDecrypt, &deviceStu)

	if len(deviceStu.Os) == 0 {
		deviceStu.Os = "android"
	}

	deviceStu.IP = ip
	deviceStu.Ua = ua
	if deviceStu.Os == "android" {
		deviceStu.Imei = strings.Replace(deviceStu.Imei, " ", "", -1)
		deviceStu.AndroidID = strings.Replace(deviceStu.AndroidID, " ", "", -1)
		deviceStu.Oaid = strings.Replace(deviceStu.Oaid, " ", "", -1)
	}

	if len(deviceStu.OsVersion) == 0 {
		return
	}

	// appNameDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkExtraAL.ALN)
	// appNameDecrypt := utils.AesECBDecrypt(appNameDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(string(appNameDecrypt))

	packageNameDecryptBase64, _ := base64.StdEncoding.DecodeString(sdkExtraAL.ALP)
	packageNameDecrypt := utils.AesECBDecrypt(packageNameDecryptBase64, []byte(config.EncryptKEY))
	// fmt.Println(string(packageNameDecrypt))

	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, deviceStu.IP)
	if ip2locationResp == nil {
	} else {
		deviceStu.LBSIPCountry = ip2locationResp.Country
		deviceStu.LBSIPProvince = ip2locationResp.Region
		deviceStu.LBSIPCity = ip2locationResp.City
	}

	var appListCode []int
	packageName := string(packageNameDecrypt)

	for key, value := range models2.GetMHAppListMap() {
		if strings.Contains(packageName, key) {
			appListCode = append(appListCode, value)
		}
	}

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("bigdata extra al panic:", err)
			}
		}()

		var reqStu models.MHReq
		var localPosStu models.LocalPosStu
		deviceStu.AppList = appListCode
		reqStu.Device = deviceStu
		// did_md5
		reqStu.Device.DIDMd5 = models.GetDeviceIDMd5Key(&reqStu)
		reqStu.SDKVersion = sdkExtraAL.SDKVersion
		localPosStu.LocalAppID = sdkExtraAL.LocalAppID

		if mhReqBytes, err := json.Marshal(reqStu); err == nil {
			newMhReq := device.ModelsMHReqToDeviceReq(mhReqBytes)
			if localPosBytes, err := json.Marshal(localPosStu); err == nil {
				newLocalPos := device.ModelsLocalPosStuToDeviceLocalPosStu(localPosBytes)

				dau.DauSupplyReqKafka(
					c,
					reqStu.Device.DIDMd5,
					0,
					newMhReq,
					newLocalPos,
					reqStu.SDKVersion,
				)
			}
		}
		//models.GoBigDataHoloAppListDidData(&reqStu)

		//if localPosStu.LocalAppID == "10012" || localPosStu.LocalAppID == "10016" {
		//	models.GoBigDataHoloDebugAppListData(&localPosStu, &reqStu)
		//} else {
		//	randomNumber := rand.Intn(10000)
		//	if randomNumber == 0 {
		//		models.GoBigDataHoloDebugAppListData(&localPosStu, &reqStu)
		//	}
		//}
	}()
}

type DMPRole struct {
	ID       int      `json:"id"`
	Name     string   `json:"name"`
	Keywords []string `json:"keywords"`
}

// SDKExtraAL ...
type SDKExtraAL struct {
	DID        string `json:"did,omitempty"`
	ALN        string `json:"aln,omitempty"`
	ALP        string `json:"alp,omitempty"`
	LocalAppID string `json:"app_id,omitempty"`
	SDKVersion string `json:"sdk_version,omitempty"`
}

// SDKExtraCNW ...
type SDKExtraCNW struct {
	LocalAppID        string `json:"app_id"`
	LocalAppBundle    string `json:"app_bundle_id"`
	SDKVersion        string `json:"sdk_version,omitempty"`
	DID               string `json:"did"`
	ExtraClipBoard    int    `json:"ec"`
	ExtraNotification int    `json:"en"`
	ExtraWakeUp       int    `json:"ew"`
}

// SdkAntiCheat ...
func SdkAntiCheat(c *gin.Context) {
	fmt.Println("anticheat")

	c.JSON(200, gin.H{
		"ret": 0,
		"msg": "white",
	})
}

// SdkAntiCheatV3 ...
func SdkAntiCheatV3(c *gin.Context) {
	c.JSON(200, gin.H{
		"ret": 0,
		"msg": "white",
	})
}

// SdkPriceFailed ...
func SdkPriceFailed(c *gin.Context) {
	fmt.Println("price failed")

	localAppID := c.Query("app_id")
	localPosID := c.Query("pos_id")
	platformAppID := c.Query("platform_app_id")
	platformPosID := c.Query("platform_pos_id")
	floorPrice := c.Query("floor_price")
	finalPrice := c.Query("final_price")
	ecpm := c.Query("ecpm")

	fmt.Println("price failed", localAppID, localPosID, platformAppID, platformPosID, floorPrice, finalPrice, ecpm)

	resp := map[string]interface{}{}
	resp["ret"] = 0

	c.PureJSON(200, resp)
}

// SdkAppInfo ...
func SdkAppInfo(c *gin.Context) {
	fmt.Println("sdk app info")

	localAppID := c.Query("app_id")

	appInfo := models.GetLocalAppInfoByAppID(c, localAppID)

	// 客户端不需要返回app_id
	if appInfo != nil {
		appInfo.LocalAppID = ""
	}
	// fmt.Println("-----------------------------------", appInfo.LocalAppUploadPackageNames)

	if appInfo != nil && len(appInfo.LocalAppUploadPackageNames) > 0 {
		if appInfo.LocalAppIsUploadPackageNames == 1 {
			packageNameArray := strings.Split(appInfo.LocalAppUploadPackageNames, ",")
			tmpByte, _ := json.Marshal(packageNameArray)
			encodePackageName := utils.AesECBEncrypt([]byte(tmpByte), []byte(config.EncryptKEY))
			appInfo.LocalAppUploadEncryptionPackageNames = url.QueryEscape(base64.StdEncoding.EncodeToString(encodePackageName))
		}
		appInfo.LocalAppUploadPackageNames = ""
	}

	respCode := 0
	if appInfo == nil {
		respCode = 1
	}

	respAppInfoMap := map[string]interface{}{}
	if appInfo != nil {
		respAppInfoMap["is_get_imei"] = appInfo.LocalAppIsGetImei
		respAppInfoMap["is_get_oaid"] = appInfo.LocalAppIsGetOaid
		respAppInfoMap["is_get_android_id"] = appInfo.LocalAppIsGetAndroidID
		respAppInfoMap["is_get_gps"] = appInfo.LocalAppIsGetGPS
		respAppInfoMap["is_get_applist"] = appInfo.LocalAppIsGetAppList
		if localAppID == "10948" || localAppID == "10949" {
			respAppInfoMap["is_silence_monitor"] = 0
		} else {
			respAppInfoMap["is_silence_monitor"] = 1
		}
		respAppInfoMap["is_pkgs"] = appInfo.LocalAppIsUploadPackageNames
		respAppInfoMap["pkgs"] = appInfo.LocalAppUploadEncryptionPackageNames
		respAppInfoMap["ext_sdk_init_permissions"] = appInfo.ExtSdkInitPermissions
		respAppInfoMap["is_crash_protect"] = appInfo.LocalAppIsCrashProtect
		respAppInfoMap["is_upload_crash"] = appInfo.LocalAppIsUploadCrash

		if appInfo.LocalAppIsThirdReqMonitor == 1 {
			respAppInfoMap["is_third_req_monitor"] = appInfo.LocalAppIsThirdReqMonitor
			respAppInfoMap["third_req_monitor"] = appInfo.LocalAppThirdReqMonitor
			respAppInfoMap["third_valid_req_monitor"] = appInfo.LocalAppThirdValidReqMonitor
			respAppInfoMap["third_valid_pos_ids"] = strings.Split(appInfo.LocalAppThirdValidPosIDs, ",")
		}

		// applist上报参数配置
		var appListParamsArray []string
		appListParamsArray = append(appListParamsArray, "imei", "oaid", "model", "manufacturer", "os_version", "screen_width", "screen_height", "aln", "alp", "app_id", "sdk_version")
		tmpAppListByte, _ := json.Marshal(appListParamsArray)
		tmpAppListDecrypt := utils.AesECBEncrypt([]byte(tmpAppListByte), []byte(config.EncryptKEY))
		respAppInfoMap["app_param"] = base64.StdEncoding.EncodeToString(tmpAppListDecrypt)
		// extra上报参数配置
		var extraParamsArray []string
		extraParamsArray = append(extraParamsArray, "imei", "oaid", "model", "manufacturer", "os_version", "app_id", "sdk_version")
		tmpExtraByte, _ := json.Marshal(extraParamsArray)
		tmpExtraDecrypt := utils.AesECBEncrypt([]byte(tmpExtraByte), []byte(config.EncryptKEY))
		respAppInfoMap["ext_param"] = base64.StdEncoding.EncodeToString(tmpExtraDecrypt)
	}
	resp := map[string]interface{}{}
	resp["ret"] = respCode
	resp["data"] = respAppInfoMap
	// resp["data"] = appInfo

	c.PureJSON(200, resp)
}

// SdkAppInfoPost ...
func SdkAppInfoPost(c *gin.Context) {
	fmt.Println("sdk app info post")

	bodyContent, _ := c.GetRawData()
	// fmt.Println("kbg_debug sdk app info", len(bodyContent))

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")
	// fmt.Println("kbg_debug sdk app info  encoding:", contentEncoding)
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}
	// fmt.Println("kbg_debug sdk app info body:", string(bodyContent))

	var mhReq models.MHReq
	err := json.Unmarshal(bodyContent, &mhReq)
	if err != nil {
		fmt.Println("sdk config parser error:", err)
	}

	mhReq.Device.Ua = c.GetHeader("User-Agent")
	mhReq.Device.IP = c.ClientIP()

	appInfo := models.GetLocalAppInfoByAppID(c, mhReq.App.AppID)

	// 客户端不需要返回app_id
	if appInfo != nil {
		appInfo.LocalAppID = ""
	}
	// fmt.Println("-----------------------------------", appInfo.LocalAppUploadPackageNames)

	if appInfo != nil && len(appInfo.LocalAppUploadPackageNames) > 0 {
		if appInfo.LocalAppIsUploadPackageNames == 1 {
			packageNameArray := strings.Split(appInfo.LocalAppUploadPackageNames, ",")
			tmpByte, _ := json.Marshal(packageNameArray)
			encodePackageName := utils.AesECBEncrypt([]byte(tmpByte), []byte(config.EncryptKEY))
			appInfo.LocalAppUploadEncryptionPackageNames = url.QueryEscape(base64.StdEncoding.EncodeToString(encodePackageName))
		}
		appInfo.LocalAppUploadPackageNames = ""
	}

	respCode := 0
	if appInfo == nil {
		respCode = 1
	}

	respAppInfoMap := map[string]interface{}{}
	if appInfo != nil {
		respAppInfoMap["is_get_imei"] = appInfo.LocalAppIsGetImei
		respAppInfoMap["is_get_oaid"] = appInfo.LocalAppIsGetOaid
		respAppInfoMap["is_get_android_id"] = appInfo.LocalAppIsGetAndroidID
		respAppInfoMap["is_get_gps"] = appInfo.LocalAppIsGetGPS
		respAppInfoMap["is_get_applist"] = appInfo.LocalAppIsGetAppList
		respAppInfoMap["is_silence_monitor"] = appInfo.LocalAppIsSilenceMonitor
		respAppInfoMap["is_pkgs"] = appInfo.LocalAppIsUploadPackageNames
		respAppInfoMap["pkgs"] = appInfo.LocalAppUploadEncryptionPackageNames
		respAppInfoMap["ext_sdk_init_permissions"] = appInfo.ExtSdkInitPermissions
		respAppInfoMap["is_crash_protect"] = appInfo.LocalAppIsCrashProtect
		respAppInfoMap["is_upload_crash"] = appInfo.LocalAppIsUploadCrash

		if appInfo.LocalAppIsThirdReqMonitor == 1 {
			respAppInfoMap["is_third_req_monitor"] = appInfo.LocalAppIsThirdReqMonitor
			respAppInfoMap["third_req_monitor"] = appInfo.LocalAppThirdReqMonitor
			respAppInfoMap["third_valid_req_monitor"] = appInfo.LocalAppThirdValidReqMonitor
			respAppInfoMap["third_valid_pos_ids"] = strings.Split(appInfo.LocalAppThirdValidPosIDs, ",")
		}

		// applist上报参数配置
		var appListParamsArray []string
		appListParamsArray = append(appListParamsArray, "imei", "oaid", "model", "manufacturer", "os_version", "screen_width", "screen_height", "aln", "alp", "app_id", "sdk_version")
		tmpAppListByte, _ := json.Marshal(appListParamsArray)
		tmpAppListDecrypt := utils.AesECBEncrypt([]byte(tmpAppListByte), []byte(config.EncryptKEY))
		respAppInfoMap["app_param"] = base64.StdEncoding.EncodeToString(tmpAppListDecrypt)
		// extra上报参数配置
		var extraParamsArray []string
		extraParamsArray = append(extraParamsArray, "imei", "oaid", "model", "manufacturer", "os_version", "app_id", "sdk_version")
		tmpExtraByte, _ := json.Marshal(extraParamsArray)
		tmpExtraDecrypt := utils.AesECBEncrypt([]byte(tmpExtraByte), []byte(config.EncryptKEY))
		respAppInfoMap["ext_param"] = base64.StdEncoding.EncodeToString(tmpExtraDecrypt)

		// 是否上报bootid
		if mhReq.Device.Os == "android" {
			if appInfo.LocalAppIsBootID == 0 {
				respAppInfoMap["is_bootid"] = 0
			} else {
				respAppInfoMap["is_bootid"] = 1
				if len(appInfo.LocalAppBootIDBlackManufacturerList) > 0 {
					if strings.Contains(appInfo.LocalAppBootIDBlackManufacturerList, "all") {
						respAppInfoMap["is_bootid"] = 0
					} else {
						for _, tmpItem := range strings.Split(appInfo.LocalAppBootIDBlackManufacturerList, ",") {
							if tmpItem == "all" {
								continue
							}
							isblack := false
							manufactureConfigArray := core.GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+tmpItem)
							for _, manufactureConfigItem := range manufactureConfigArray {
								if strings.ToLower(mhReq.Device.Manufacturer) == manufactureConfigItem {
									isblack = true
									break
								}
							}
							if isblack {
								respAppInfoMap["is_bootid"] = 0
								break
							}
						}
					}
				}
			}
		}
	}
	resp := map[string]interface{}{}
	resp["ret"] = respCode
	resp["data"] = respAppInfoMap
	// resp["data"] = appInfo

	c.PureJSON(200, resp)
}

func SdkAntiCheatV4(c *gin.Context) {
	bodyContent, _ := c.GetRawData()
	contentEncoding := c.GetHeader("Content-Encoding")
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}
	fmt.Println(string(bodyContent))
	req := models.SdkAnticheatV4Model{}
	_ = json.Unmarshal(bodyContent, &req)

	var sdkAnticheatV4Did models.SdkAnticheatV4DidModel
	decryptBase64, _ := base64.StdEncoding.DecodeString(req.DID)
	decodeDid := utils.AesECBDecrypt(decryptBase64, []byte(config.EncryptKEY))
	_ = json.Unmarshal(decodeDid, &sdkAnticheatV4Did)
	go models.BigDataHoloSdkAntiCheatV4(req, sdkAnticheatV4Did)

	resp := map[string]interface{}{}
	resp["ret"] = 0

	c.PureJSON(200, resp)
}
