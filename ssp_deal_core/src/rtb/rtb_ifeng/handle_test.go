package rtb_ifeng

import (
	"bytes"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"io"
	"mh_proxy/utils"
	"net/http"
	"testing"
)

func Test_Aes(t *testing.T) {
	price := "Wlu2KkShLAvnrCbz5N0EXw"
	key := "cwM4A76VhwL9U6P3"
	tmpPrice, _ := utils.Base64URLDecode(price)
	decodePrice := AESDecrypt(tmpPrice, []byte(key))
	fmt.Println(string(decodePrice))
}

func Test_HandleByFancy(t *testing.T) {
	var impList []*IFengRequestImpObject
	imp := &IFengRequestImpObject{
		Id:       uuid.NewV4().String(),
		Tagid:    "13650",
		BidFloor: 10,
	}
	impList = append(impList, imp)
	request := &IFengRequestObject{
		Id:  "15ae86edf4f54c0197023b8852ae4c76",
		Imp: impList,
		App: &IFengRequestAppObject{
			Id:     "213123123",
			Name:   "凤凰",
			Domain: "com.ifeng.cn",
			Ver:    "1.2.3",
		},
		Device: &IFengRequestDeviceObject{
			Ua:             "Mozilla/5.0 (Linux; Android 11; PEGM00 Build/RKQ1.200903.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36",
			Ip:             "***************",
			Make:           "OPPO",
			Model:          "PEGM00",
			Os:             "android",
			Osv:            "11",
			Carries:        "移动",
			Didmd5:         "922b091cff8e4eec",
			Oaid:           "1C60574BEB4A44FE9469D682E0E5B271388b1e9af5e48b00027668ca9fc294f8",
			BootMark:       "763d5649-3c21-4c3b-83f2-ac2570800440",
			UpdateMark:     "17857.107999990",
			W:              1080,
			H:              2285,
			Connectiontype: 2,
			Devicetype:     2,
		},
		Site:     nil,
		User:     nil,
		At:       2,
		Platform: 1,
	}

	client := &http.Client{}
	reqByte, _ := json.Marshal(request)
	requestUrl := "https://api-debug.maplehaze.cn/rtb/request?channel=45"
	requestPost, _ := http.NewRequest("POST", requestUrl, bytes.NewReader(reqByte))
	requestPost.Header.Add("Content-Type", "application/json")
	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	fmt.Println(string(bodyContent))
}
