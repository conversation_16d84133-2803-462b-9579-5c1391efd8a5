package rtb_ifeng

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
)

func AESEncrypt(plaintext []byte, key []byte) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	plaintext = padding(plaintext, block.BlockSize())
	cipertext := make([]byte, len(plaintext))
	ecb := NewECBEncrypter(block)
	ecb.CryptBlocks(cipertext, plaintext)
	return cipertext
}
func AESDecrypt(ciphertext []byte, key []byte) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	decrypted := make([]byte, len(ciphertext))
	ecb := NewECBDecrypter(block)
	ecb.CryptBlocks(decrypted, ciphertext)
	return unPadding(decrypted)
}
func padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func unPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

type ecbEncrypter struct {
	b         cipher.Block
	blockSize int
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }
func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/ecbEncrypter: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/ecbEncrypter: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst[:x.blockSize], src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}
func NewECBEncrypter(b cipher.Block) *ecbEncrypter {
	return &ecbEncrypter{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbDecrypter struct {
	b         cipher.Block
	blockSize int
}

func (x *ecbDecrypter) BlockSize() int { return x.blockSize }
func (x *ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/ecbDecrypter: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/ecbDecrypter: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Decrypt(dst[:x.blockSize], src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}
func NewECBDecrypter(b cipher.Block) *ecbDecrypter {
	return &ecbDecrypter{
		b:         b,
		blockSize: b.BlockSize(),
	}
}
