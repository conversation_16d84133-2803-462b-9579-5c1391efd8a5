package rtb_ifeng

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// IFengRequest Objects
type IFengRequestObject struct {
	Id       string                    `json:"id"`
	Imp      []*IFengRequestImpObject  `json:"imp"`
	App      *IFengRequestAppObject    `json:"app"`
	Device   *IFengRequestDeviceObject `json:"device"`
	Site     *IFengRequestSiteObject   `json:"site"`
	User     *IFengRequestUserObject   `json:"user"`
	At       int                       `json:"at"`
	Platform int                       `json:"platform"`
}
type IFengRequestSiteObject struct {
	Id     string `json:"id"`
	Domain string `json:"domain"`
	Page   string `json:"page"`
}
type IFengRequestDeviceObject struct {
	Ua               string                       `json:"ua"`
	Ip               string                       `json:"ip"`
	Make             string                       `json:"make"`
	Model            string                       `json:"model"`
	Os               string                       `json:"os"`
	Osv              string                       `json:"osv"`
	Carries          string                       `json:"carries"`
	Oaid             string                       `json:"oaid"`
	Idfa             string                       `json:"ifa"`
	Didmd5           string                       `json:"didmd5"`
	Dpidmd5          string                       `json:"dpidmd5"`
	Macmd5           string                       `json:"macmd5"`
	StartupTime      string                       `json:"startup_time"`
	MbTime           string                       `json:"mb_time"`
	Language         string                       `json:"language"`
	UpdateMark       string                       `json:"update_mark"`
	BootMark         string                       `json:"boot_mark"`
	MemTotal         string                       `json:"mem_total"`
	DiskTotal        string                       `json:"disk_total"`
	BirthTime        string                       `json:"birth_time"`
	RomVersion       string                       `json:"rom_version"`
	CpuNum           string                       `json:"cpu_num"`
	SysCompilingTime string                       `json:"sys_compiling_time"`
	HardwareMachine  string                       `json:"hardware_machine"`
	HardwareModel    string                       `json:"hardware_model"`
	DeviceNameMd5    string                       `json:"device_name_md5"`
	W                int                          `json:"w"`
	H                int                          `json:"h"`
	Ppi              int                          `json:"ppi"`
	Connectiontype   int                          `json:"connectiontype"`
	Devicetype       int                          `json:"devicetype"`
	Geo              *IFengRequestDeviceGeoObject `json:"geo"`
}

type IFengRequestDeviceGeoObject struct {
	Lon float64 `json:"lon"`
	Lat float64 `json:"lat"`
}

type IFengRequestImpObject struct {
	Id       string                         `json:"id"`
	Tagid    string                         `json:"tagid"`
	BidFloor float64                        `json:"bidfloor"`
	Banner   []*IFengRequestImpBannerObject `json:"banner"`
	Video    []*IFengRequestImpVideoObject  `json:"video"`
}
type IFengRequestImpBannerObject struct {
	Type    int `json:"type"`
	W       int `json:"w"`
	H       int `json:"h"`
	Textlen int `json:"textlen"`
	Vtime   int `json:"vtime"`
	Vid     int `json:"vid"`
}
type IFengRequestImpVideoObject struct {
	Minduration int      `json:"minduration"`
	Maxduration int      `json:"maxduration"`
	W           int      `json:"w"`
	H           int      `json:"h"`
	Minbitrate  int      `json:"minbitrate"`
	Maxbitrate  int      `json:"maxbitrate"`
	Protocols   []int    `json:"protocols"`
	Mimes       []string `json:"mimes"`
}

type IFengRequestAppObject struct {
	Id       string `json:"id"`
	Name     string `json:"name"`
	Domain   string `json:"domain"`
	Ver      string `json:"ver"`
	Category string `json:"category"`
}

type IFengRequestUserObject struct {
	Id           string   `json:"id"`
	UserCategory []string `json:"user_category"`
}

// IFengResponseObject Objects
type IFengResponseObject struct {
	Id      string                        `json:"id"`
	Bid     string                        `json:"bidid"`
	Seatbid []*IFengResponseSeatbidObject `json:"seatbid"`
}

type IFengResponseSeatbidObject struct {
	Bid  []*IFengResponseSeatbidBidObject `json:"bid"`
	Seat string                           `json:"seat"`
}

type IFengResponseSeatbidBidObject struct {
	Id      string   `json:"id"`
	Impid   string   `json:"impid"`
	Adid    string   `json:"adid"`
	Nurl    string   `json:"nurl"`
	Adm     string   `json:"adm"`
	Crid    string   `json:"crid"`
	Adomain []string `json:"adomain"`
	Price   float64  `json:"price"`
}

type IFengResponseAdmObject struct {
	Type                      int      `json:"type,omitempty"`
	Stype                     int      `json:"stype,omitempty"`
	H                         int      `json:"h,omitempty"`
	W                         int      `json:"w,omitempty"`
	ActionType                int      `json:"actionType,omitempty"`
	Videotime                 int      `json:"videotime,omitempty"`
	Videourl                  string   `json:"videourl,omitempty"`
	Xml                       string   `json:"xml,omitempty"`
	Html                      string   `json:"html,omitempty"`
	Text                      string   `json:"text,omitempty"`
	Source                    string   `json:"source,omitempty"`
	Curl                      string   `json:"curl,omitempty"`
	Durl                      string   `json:"durl,omitempty"`
	Appname                   string   `json:"appname,omitempty"`
	Appicon                   string   `json:"appicon,omitempty"`
	Appversion                string   `json:"appversion,omitempty"`
	Appdeveloper              string   `json:"appdeveloper,omitempty"`
	Apppermission             string   `json:"apppermission,omitempty"`
	Appprivacypolicy          string   `json:"appprivacypolicy,omitempty"`
	Bundle                    string   `json:"bundle,omitempty"`
	Desc                      string   `json:"desc,omitempty"`
	Dplurl                    string   `json:"dplurl,omitempty"`
	Downloadcancelurl         string   `json:"downloadcancelurl,omitempty"`
	Dcurl                     []string `json:"dcurl,omitempty"`
	Dsurl                     []string `json:"dsurl,omitempty"`
	Dicurl                    []string `json:"dicurl,omitempty"`
	Aurl                      []string `json:"aurl,omitempty"`
	Texts                     []string `json:"texts,omitempty"`
	Descs                     []string `json:"descs,omitempty"`
	Curllist                  []string `json:"curllist,omitempty"`
	Acurl                     []string `json:"acurl,omitempty"`
	Murl                      []string `json:"murl,omitempty"`
	InstallStarturl           []string `json:"installStarturl,omitempty"`
	InstallCompletedOntimeurl []string `json:"installCompletedOntimeurl,omitempty"`
	Dpturl                    []string `json:"dpturl,omitempty"`
	Dpsurl                    []string `json:"dpsurl,omitempty"`
}

type IFengPipline struct {
	Context *gin.Context
	Channel string

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	Request  *IFengRequestObject
	Response *IFengResponseObject

	UUID          string
	DeviceOs      rtb.MHRtbOSEnum
	Manufacturer  string
	ConnectType   rtb.MHRtbConnectTypeEnum
	Carrier       rtb.MHRtbCarrierEnum
	ResultfulImp  []*IFengRequestImpObject
	ConfigList    []*models.RtbConfigByTagIDStu
	AdxAdResponse *models.MHResp
}
