package rtb_ifeng

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *IFengPipline) Init(c *gin.Context, channel string) *IFengPipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()
	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject IFengRequestObject
	err = json.Unmarshal(bodyContent, &requestObject)
	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()
	p.Context = c
	p.Channel = channel

	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCarrier()

}

// CheckImp 检查Imp对象
func (p *IFengPipline) CheckImp() *IFengPipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *IFengPipline) SetupOsString() *IFengPipline {
	// 检查
	switch strings.ToLower(p.Request.Device.Os) {
	case "ios":
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	case "android":
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	default:
		return p.SetErrorString("invalid os")
	}
	return p
}

// CheckUA 检查UA
func (p *IFengPipline) CheckUA() *IFengPipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *IFengPipline) SetupManufacturer() *IFengPipline {
	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Make
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *IFengPipline) SetupConnectType() *IFengPipline {
	switch p.Request.Device.Connectiontype {
	case 0:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	case 4:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_2G
	case 5:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_3G
	case 6:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}
	return p
}

// SetupCarrier 设置运营商
func (p *IFengPipline) SetupCarrier() *IFengPipline {
	switch p.Request.Device.Carries {
	case "移动":
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case "联通":
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case "电信":
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}
	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *IFengPipline) RequestRtbConfig() *IFengPipline {
	var resultfulImp []*IFengRequestImpObject
	var configList []*models.RtbConfigByTagIDStu

	for _, imp := range p.Request.Imp {
		//var styleIds []string
		tagId := imp.Tagid
		price := imp.BidFloor

		if price == 0 {
			continue
		}

		//for _, bannerItem := range imp.Banner {
		//	styleIds = append(styleIds, strconv.Itoa(bannerItem.Type))
		//}

		var adxInfo *[]models.RtbConfigByTagIDStu
		//adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(p.Context, p.Channel, tagId, p.DeviceOs.String(), styleIds, "", int(price))
		adxInfo = models.GetAdxInfoByRtbTagID(p.Context, p.Channel, tagId, p.DeviceOs.String(), "", "", int(price))
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		conf := (*adxInfo)[0]
		resultfulImp = append(resultfulImp, imp)
		configList = append(configList, &conf)
	}

	p.ResultfulImp = resultfulImp
	p.ConfigList = configList

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return p.SetErrorString("resultful imp is empty")
	}
	return p
}

// RequestAdxAd 请求广告
func (p *IFengPipline) RequestAdxAd() *IFengPipline {
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	w := p.Request.Device.W
	h := p.Request.Device.H

	//for _, imp := range p.ResultfulImp {
	//	for _, bannerItem := range imp.Banner {
	//		if bannerItem.Type == 3 || bannerItem.Type == 7 || bannerItem.Type == 47 || bannerItem.Type == 50 || bannerItem.Type == 59 || bannerItem.Type == 60 {
	//			w = 1080
	//			h = 1920
	//			if bannerItem.W == 706 && bannerItem.H == 398 {
	//				w = 1280
	//				h = 720
	//				break
	//			}
	//		}
	//	}
	//}

	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: p.Request.App.Domain,
			AppName:     p.Request.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: len(p.ResultfulImp),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 p.DeviceOs.String(),
			OsVersion:          p.Request.Device.Osv,
			Model:              p.Request.Device.Model,
			Manufacturer:       p.Manufacturer,
			ImeiMd5:            p.Request.Device.Didmd5,
			AndroidID:          p.Request.Device.Dpidmd5,
			Oaid:               p.Request.Device.Oaid,
			Idfa:               p.Request.Device.Idfa,
			Ua:                 p.Request.Device.Ua,
			ScreenWidth:        w,
			ScreenHeight:       h,
			DeviceType:         1,
			IP:                 p.Request.Device.Ip,
			BootMark:           p.Request.Device.BootMark,
			UpdateMark:         p.Request.Device.UpdateMark,
			DeviceStartSec:     p.Request.Device.StartupTime,
			Language:           p.Request.Device.Language,
			PhysicalMemoryByte: p.Request.Device.MemTotal,
			HarddiskSizeByte:   p.Request.Device.DiskTotal,
			SystemUpdateSec:    p.Request.Device.MbTime,
			CPUNum:             p.Request.Device.CpuNum,
			HardwareMachine:    p.Request.Device.HardwareMachine,
			HardwareModel:      p.Request.Device.HardwareModel,
			DeviceNameMd5:      p.Request.Device.DeviceNameMd5,
		},

		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func (p *IFengPipline) SetupResponse() *IFengPipline {
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	var impItem *IFengRequestImpObject
	for _, imp := range p.ResultfulImp {
		if imp.Tagid == requestConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return p.SetErrorString("error imp item")

	}

	var responseObject IFengResponseObject
	var responseSeatbidObject IFengResponseSeatbidObject
	var responseSeatbidObjectList []*IFengResponseSeatbidObject
	var BidList []*IFengResponseSeatbidBidObject

	for _, adxAd := range p.AdxAdResponse.Data[requestConfig.LocalPosID].List {
		flag := 0
		sType := 0
		for _, bannerItem := range impItem.Banner {
			if bannerItem.Type == 3 || bannerItem.Type == 7 || bannerItem.Type == 8 || bannerItem.Type == 48 {
				flag = 1
				sType = bannerItem.Type
				break
			}
		}
		if flag == 0 {
			continue
		}

		// 统计
		models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if adxAd.Ecpm < requestConfig.Price {
			continue
		}
		ecpm := adxAd.Ecpm

		var bidObject IFengResponseSeatbidBidObject

		winNotice := config.ExternalRtbPriceURL +
			"?uid=" + p.UUID +
			"&tagid=" + requestConfig.TagID +
			"&bp=" + utils.ConvertIntToString(ecpm) +
			"&channel=" + p.Channel +
			"&price=${AUCTION_PRICE_AES}" +
			"&log=" + url.QueryEscape(adxAd.Log)

		bidObject.Price = float64(ecpm)
		bidObject.Nurl = winNotice

		bidObject.Id = uuid.NewV4().String()
		bidObject.Impid = impItem.Id
		bidObject.Adid = adxAd.AdID
		bidObject.Crid = adxAd.Crid

		var images []string
		var actionType int
		var adm IFengResponseAdmObject
		if adxAd.InteractType == 0 {
			actionType = 1

			if len(adxAd.DeepLink) > 0 {
				adm.Dplurl = adxAd.DeepLink
			} else {
				if len(adxAd.MarketURL) > 0 {
					adm.Dplurl = adxAd.MarketURL
				}
			}
		} else {
			actionType = 2

			if len(adxAd.MarketURL) > 0 {
				adm.Dplurl = adxAd.MarketURL
			} else {
				if len(adxAd.DeepLink) > 0 {
					adm.Dplurl = adxAd.DeepLink
				}
			}
		}

		if len(adxAd.DeepLink) > 0 || len(adxAd.MarketURL) > 0 {
			actionType = 3
		}
		switch adxAd.CrtType {
		case 11:
			for _, imageItem := range adxAd.Image {
				images = append(images, imageItem.URL)
			}
			adm.Aurl = images
			adm.W = adxAd.Image[0].Width
			adm.H = adxAd.Image[0].Height
			//num, _ := strconv.Atoi(requestConfig.ImageStyleID)
			//sType = num
		case 20:
			adm.W = adxAd.Video.Width
			adm.H = adxAd.Video.Height
			adm.Videourl = adxAd.Video.VideoURL
			adm.Videotime = adxAd.Video.Duration / 1000

			//num, _ := strconv.Atoi(requestConfig.VideoStyleID)
			//sType = num
		}

		var impLinkUrl string
		var impTrackArray []string
		if len(adxAd.ImpressionLink) > 3 {
			for _, impLink := range adxAd.ImpressionLink {
				if strings.Contains(impLink, "maplehaze") {
					impLinkUrl = impLink
					break
				} else {
					impTrackArray = append(impTrackArray, impLink)
				}
			}
			if len(impLinkUrl) > 0 {
				impTrackArray = append(impTrackArray, impLinkUrl)
			}
		} else {
			impTrackArray = append(impTrackArray, adxAd.ImpressionLink...)
		}

		var clickLinkUrl string
		var replacedClickLinkArray []string
		var clickLinkArray []string
		if len(adxAd.ClickLink) > 3 {
			for _, clickLink := range adxAd.ClickLink {
				tempLink := clickLink
				if strings.Contains(tempLink, "maplehaze") {
					clickLinkUrl = tempLink
					break
				} else {
					replacedClickLinkArray = append(replacedClickLinkArray, tempLink)
				}
			}
			if len(clickLinkUrl) > 0 {
				replacedClickLinkArray = append(replacedClickLinkArray, clickLinkUrl)
			}
		} else {
			replacedClickLinkArray = append(replacedClickLinkArray, adxAd.ClickLink...)
		}

		for _, clickLink := range replacedClickLinkArray {
			width := utils.ConvertIntToString(p.Request.Device.W)
			height := utils.ConvertIntToString(p.Request.Device.H)

			clickLink = strings.Replace(clickLink, "__WIDTH__", width, -1)
			clickLink = strings.Replace(clickLink, "__HEIGHT__", height, -1)

			clickLink = strings.Replace(clickLink, "__DOWN_X__ ", "__DOWN_X_TX__", -1)
			clickLink = strings.Replace(clickLink, "__DOWN_Y__", "__DOWN_Y_TX__", -1)
			clickLink = strings.Replace(clickLink, "__UP_X__", "__UP_X_TX__", -1)
			clickLink = strings.Replace(clickLink, "__UP_Y__", "__UP_Y_TX__", -1)
			clickLinkArray = append(clickLinkArray, clickLink)
		}

		adm.Type = 1
		adm.Stype = sType
		adm.ActionType = actionType
		adm.Text = adxAd.Description
		adm.Murl = impTrackArray
		adm.Acurl = clickLinkArray
		adm.Durl = adxAd.DownloadURL
		adm.Appname = adxAd.AppName
		adm.Appicon = adxAd.IconURL
		adm.Appversion = adxAd.AppVersion
		adm.Apppermission = adxAd.Permission
		adm.Appprivacypolicy = adxAd.PrivacyLink
		adm.Appdeveloper = adxAd.Publisher
		adm.Bundle = adxAd.PackageName
		adm.Curl = adxAd.LandpageURL
		admStr, err := json.Marshal(adm)
		if err != nil {
			fmt.Println(err)
			return nil
		}
		escapeUrl := url.QueryEscape(string(admStr))
		bidObject.Adm = escapeUrl
		BidList = append(BidList, &bidObject)
	}
	responseSeatbidObject.Bid = BidList
	responseSeatbidObject.Seat = uuid.NewV4().String()
	responseSeatbidObjectList = append(responseSeatbidObjectList, &responseSeatbidObject)

	// 构造返回结果
	responseObject.Id = p.Request.Id
	responseObject.Seatbid = responseSeatbidObjectList

	status := http.StatusOK
	if len(responseSeatbidObjectList) == 0 {
		status = http.StatusNoContent
	}
	p.Response = &responseObject
	p.ResponseStatus = status
	return p
}

// ResponseResult 返回结果
func (p *IFengPipline) ResponseResult() (*IFengResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &IFengResponseObject{
			Id:      p.Request.Id,
			Seatbid: nil,
		}, http.StatusNoContent
	}
	return p.Response, http.StatusOK
}

// SetErrorString 设置字符串类型的错误信息
func (p *IFengPipline) SetErrorString(err string) *IFengPipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *IFengPipline) SetError(err error) *IFengPipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}

// Print 打印状态
func (p *IFengPipline) Print() *IFengPipline {
	fmt.Println("<<<<<<<<<<<<<<<< DEBUG <<<<<<<<<<<<<<<")
	fmt.Printf("%+v\n", p)
	fmt.Println(">>>>>>>>>>>>>>>> DEBUG >>>>>>>>>>>>>>>")
	return p
}
