package rtb_bes

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/bes"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleByBes(c *gin.Context, channel string) (*bes.BidResponse, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()
	req := &bes.BidRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var deviceOs string
	var hacktTagId string
	switch req.GetMobile().GetPlatform() {
	case bes.BidRequest_Mobile_IOS:
		deviceOs = "ios"
		hacktTagId = "bes-alliance-ios"
	case bes.BidRequest_Mobile_ANDROID:
		deviceOs = "android"
		hacktTagId = "bes-alliance-and"
	default:
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetMobile().GetWirelessNetworkType() {
	case bes.BidRequest_Mobile_WIFI:
		connectType = 1
	case bes.BidRequest_Mobile_MOBILE_2G:
		connectType = 2
	case bes.BidRequest_Mobile_MOBILE_3G:
		connectType = 3
	case bes.BidRequest_Mobile_MOBILE_4G:
		connectType = 4
	default:
		connectType = 0
	}

	var carrier int
	switch req.GetMobile().GetCarrierId() {
	case 46000:
		carrier = 1
	case 46001:
		carrier = 2
	case 46003:
		carrier = 3
	default:
		carrier = 0
	}

	adsCount := 1
	var resultfulImp []*bes.BidRequest_AdSlot
	var configList []models.RtbConfigByTagIDStu

	marshal, _ := json.Marshal(req.GetTagId())
	randomNumber := rand.Intn(100)

	for _, imp := range req.GetAdslot() {
		var styleIds []string
		if randomNumber == 0 {
			tagId := string(imp.GetAdBlockId())
			go models.BigDataHoloDebugBesBundle(string(marshal), req.GetMobile().GetMobileApp().GetAppBundleId(), deviceOs, tagId, fmt.Sprintf("%d", imp.GetAdslotType()))
		}

		for _, styleId := range imp.GetCreativeType() {
			styleIds = append(styleIds, fmt.Sprintf("%d", styleId))
		}
		price := imp.GetMinimumCpm()

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndTagIDType(c, channel, hacktTagId, fmt.Sprintf("%d", imp.GetAdslotType()), deviceOs, styleIds, "", int(price))
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.GetMobile().GetMobileApp().GetAppBundleId()) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.GetMobile().GetMobileApp().GetAppBundleId() {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(req.GetTagId()) > 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.CrowdPackageIDs) > 0 {
					crowdPackageIDArray := strings.Split(infoItem.CrowdPackageIDs, ",")
					matchFound := false
					for _, crowdId := range crowdPackageIDArray {
						for _, tagIdItem := range req.GetTagId() {
							if crowdId == fmt.Sprintf("%d", tagIdItem) {
								configList = append(configList, infoItem)
								matchFound = true
								break
							}
						}
						if matchFound {
							break
						}
					}
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 && len(infoItem.CrowdPackageIDs) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 || len(item.CrowdPackageIDs) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.GetMobile().GetBrand()
	}

	reqRtbConfig := configList[0]

	ip := req.GetIp()
	if len(ip) == 0 {
		ip = req.GetIp6()
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetMobile().GetMobileApp().GetAppBundleId(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:             deviceOs,
			Manufacturer:   manufacturer,
			Model:          req.GetMobile().GetModel(),
			IP:             ip,
			Ua:             req.GetUserAgent(),
			OsVersion:      fmt.Sprintf("%d", req.GetMobile().GetOsVersion().GetOsVersionMajor()),
			ScreenWidth:    int(req.GetMobile().GetScreenWidth()),
			ScreenHeight:   int(req.GetMobile().GetScreenHeight()),
			BootMark:       string(req.GetMobile().GetBootMark()),
			UpdateMark:     string(req.GetMobile().GetUpdateMark()),
			DeviceBirthSec: string(req.GetMobile().GetInitMark()),
			DeviceType:     1,
			AppList:        getBesAppList(req.GetTagId()),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if req.GetMobile().GetId() != nil {
		var caidMultiList []models.MHReqCAIDMulti
		for _, item := range req.GetMobile().GetId() {
			switch item.GetType() {
			case bes.BidRequest_Mobile_MobileID_CAID:
				var caidMulti models.MHReqCAIDMulti
				caidMulti.CAID = item.GetId()
				caidMulti.CAIDVersion = string(item.GetVersion())
				caidMultiList = append(caidMultiList, caidMulti)
			case bes.BidRequest_Mobile_MobileID_IMEI:
				reqStu.Device.ImeiMd5 = item.GetId()
			case bes.BidRequest_Mobile_MobileID_OAID:
				reqStu.Device.OaidMd5 = item.GetId()
			case bes.BidRequest_Mobile_MobileID_MAC:
				reqStu.Device.Mac = item.GetId()
			}
		}
		reqStu.Device.CAIDMulti = caidMultiList
	}

	if req.GetMobile().GetForAdvertisingId() != nil {
		for _, item := range req.GetMobile().GetForAdvertisingId() {
			switch item.GetType() {
			case bes.BidRequest_Mobile_ForAdvertisingID_ANDROID_ID:
				reqStu.Device.AndroidIDMd5 = item.GetId()
			case bes.BidRequest_Mobile_ForAdvertisingID_IDFA:
				reqStu.Device.IdfaMd5 = item.GetId()
			}
		}
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var adList []*bes.BidResponse_Ad

	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	for index, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		var impUrl []string

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=%%PRICE%%" + "&log=" + url.QueryEscape(mhDataItem.Log)
		impUrl = append(impUrl, winURL)
		for _, impLink := range mhDataItem.ImpressionLink {
			impUrl = append(impUrl, impLink)
		}

		ad := &bes.BidResponse_Ad{
			SequenceId:   proto.Int32(int32(index + 1)),
			CreativeId:   proto.Int64(0),
			AdvertiserId: proto.Uint64(2024102501),
			Category:     proto.Int32(203101),
			LandingPage:  proto.String(mhDataItem.LandpageURL),
			TargetUrl:    mhDataItem.ClickLink,
			MonitorUrls:  impUrl,
			MaxCpm:       proto.Int32(int32(ecpm)),
			NativeAd: &bes.BidResponse_Ad_NativeAd{
				Title: []byte(mhDataItem.Title),
				Desc:  []byte(mhDataItem.Description),
			},
		}
		var actionType int
		var deeplinkInfo bes.BidResponse_Ad_DeeplinkInfo
		var metaInfo bes.BidResponse_Ad_MetaInfo
		var actionInfo bes.BidResponse_Ad_MetaInfo_ActionInfo
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				deeplinkInfo.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					deeplinkInfo.DeeplinkUrl = proto.String(mhDataItem.MarketURL)
				}
			}
			actionType = 0
			deeplinkInfo.FallbackType = proto.Uint32(1)
			deeplinkInfo.FallbackUrl = proto.String(mhDataItem.LandpageURL)
			deeplinkInfo.AppBundleId = []byte(mhDataItem.PackageName)
			//deeplinkInfo.Publisher = []byte(mhDataItem.Publisher)
			//deeplinkInfo.DownloadAppVersion = []byte(mhDataItem.AppVersion)
			//deeplinkInfo.PrivacyLink = []byte(mhDataItem.PrivacyLink)
			//deeplinkInfo.PermissionLink = []byte(mhDataItem.PermissionURL)
			//deeplinkInfo.AppName = []byte(mhDataItem.AppName)
			actionInfo.ActionUrl = []byte(mhDataItem.LandpageURL)
		} else {
			if len(mhDataItem.MarketURL) > 0 {
				deeplinkInfo.DeeplinkUrl = proto.String(mhDataItem.MarketURL)
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					deeplinkInfo.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
				}
			}
			downloadInfo := &bes.BidResponse_Ad_DownloadInfo{
				AppSize:        proto.Int32(int32(mhDataItem.PackageSize)),
				AppPackageName: proto.String(mhDataItem.PackageName),
				Publisher:      []byte(mhDataItem.Publisher),
				AppVersion:     []byte(mhDataItem.AppVersion),
				PrivacyLink:    []byte(mhDataItem.PrivacyLink),
				PermissionLink: []byte(mhDataItem.PermissionURL),
				AppName:        []byte(mhDataItem.AppName),
			}
			ad.DownloadInfo = downloadInfo
			actionType = 16
			deeplinkInfo.FallbackType = proto.Uint32(2)
			deeplinkInfo.FallbackUrl = proto.String(mhDataItem.DownloadURL)
			actionInfo.ActionUrl = []byte(mhDataItem.DownloadURL)
		}
		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			actionType = 1024
		}
		ad.DeeplinkInfo = &deeplinkInfo
		metaInfo.DeeplinkInfo = &deeplinkInfo

		appInfo := bes.BidResponse_Ad_MetaInfo_AppInfo{
			AppName:        []byte(mhDataItem.AppName),
			PackageName:    []byte(mhDataItem.PackageName),
			Size:           proto.Int32(int32(mhDataItem.PackageSize)),
			Publisher:      []byte(mhDataItem.Publisher),
			AppVersion:     []byte(mhDataItem.AppVersion),
			PrivacyLink:    []byte(mhDataItem.PrivacyLink),
			PermissionLink: []byte(mhDataItem.PermissionURL),
		}
		metaInfo.AppInfo = &appInfo

		actionInfo.LandingPage = []byte(mhDataItem.LandpageURL)
		actionInfo.ActionType = proto.Int32(int32(actionType))
		metaInfo.ActionInfo = &actionInfo
		metaInfo.Category = []int32{203101}

		if len(mhDataItem.Title) > 0 {
			var titleTxtArray []*bes.BidResponse_Ad_MetaInfo_TxtMaterial
			titleTxt := bes.BidResponse_Ad_MetaInfo_TxtMaterial{
				Txt: []byte(mhDataItem.Title),
			}
			titleTxtArray = append(titleTxtArray, &titleTxt)
			metaInfo.Title = titleTxtArray
		}

		if len(mhDataItem.Description) > 0 {
			var descTxtArray []*bes.BidResponse_Ad_MetaInfo_TxtMaterial
			descTxt := bes.BidResponse_Ad_MetaInfo_TxtMaterial{
				Txt: []byte(mhDataItem.Description),
			}
			descTxtArray = append(descTxtArray, &descTxt)
			metaInfo.Desc = descTxtArray
		}

		if len(mhDataItem.IconURL) > 0 {
			var iconArray []*bes.BidResponse_Ad_MetaInfo_SrcMaterial
			icon := bes.BidResponse_Ad_MetaInfo_SrcMaterial{
				Type:   proto.Int32(int32(101)),
				Url:    []byte(mhDataItem.IconURL),
				Width:  proto.Int32(int32(512)),
				Height: proto.Int32(int32(512)),
			}
			iconArray = append(iconArray, &icon)
			metaInfo.Icon = iconArray
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image == nil || len(reqRtbConfig.ImageStyleID) == 0 {
				continue
			}

			num, _ := strconv.Atoi(reqRtbConfig.ImageStyleID)
			ad.Type = proto.Int32(int32(num))

			if num == 1 {
				var materialInfoArray []*bes.BidResponse_Ad_MaterialInfo
				materialInfo := bes.BidResponse_Ad_MaterialInfo{
					Title: proto.String(mhDataItem.Title),
					Desc:  proto.String(mhDataItem.Description),
				}
				for _, imageItem := range mhDataItem.Image {
					materialInfo.MaterialUrl = proto.String(imageItem.URL)
					ad.Width = proto.Int32(int32(imageItem.Width))
					ad.Height = proto.Int32(int32(imageItem.Height))
				}

				materialInfoArray = append(materialInfoArray, &materialInfo)
				ad.MaterialInfo = materialInfoArray
				ad.NativeAd = nil
			} else {
				var nativeAdImgList []*bes.BidResponse_Ad_NativeAd_Image
				var metaInfoImgList []*bes.BidResponse_Ad_MetaInfo_SrcMaterial
				for _, imageItem := range mhDataItem.Image {
					metaInfoImg := bes.BidResponse_Ad_MetaInfo_SrcMaterial{
						Type:   proto.Int32(int32(101)),
						Url:    []byte(imageItem.URL),
						Width:  proto.Int32(int32(imageItem.Width)),
						Height: proto.Int32(int32(imageItem.Height)),
					}
					metaInfoImgList = append(metaInfoImgList, &metaInfoImg)

					var nativeAdImg bes.BidResponse_Ad_NativeAd_Image
					nativeAdImg.Url = proto.String(imageItem.URL)
					nativeAdImg.Width = proto.Int32(int32(imageItem.Width))
					nativeAdImg.Height = proto.Int32(int32(imageItem.Height))
					ad.Width = proto.Int32(int32(imageItem.Width))
					ad.Height = proto.Int32(int32(imageItem.Height))
					nativeAdImgList = append(nativeAdImgList, &nativeAdImg)
				}
				metaInfo.Image = metaInfoImgList
				ad.NativeAd.Image = nativeAdImgList
				metaInfo.CreativeType = proto.Int32(int32(num))
				ad.StyleType = proto.Int32(42)

				var metaInfoGroupArray []*bes.BidResponse_Ad_MetaInfoGroup
				var metaInfoGroup bes.BidResponse_Ad_MetaInfoGroup
				var metaInfoArray []*bes.BidResponse_Ad_MetaInfo

				metaInfoArray = append(metaInfoArray, &metaInfo)
				metaInfoGroup.MetaInfo = metaInfoArray
				metaInfoGroupArray = append(metaInfoGroupArray, &metaInfoGroup)
				ad.MetaInfoGroup = metaInfoGroupArray
			}
		case 20:
			if mhDataItem.Video == nil || len(reqRtbConfig.VideoStyleID) == 0 {
				continue
			}
			var metaInfoImgList []*bes.BidResponse_Ad_MetaInfo_SrcMaterial
			metaInfoImg := bes.BidResponse_Ad_MetaInfo_SrcMaterial{
				Type:   proto.Int32(int32(101)),
				Url:    []byte(mhDataItem.Video.CoverURL),
				Width:  proto.Int32(int32(mhDataItem.Video.Width)),
				Height: proto.Int32(int32(mhDataItem.Video.Height)),
			}
			metaInfoImgList = append(metaInfoImgList, &metaInfoImg)
			metaInfo.Image = metaInfoImgList

			var videoArray []*bes.BidResponse_Ad_MetaInfo_SrcMaterial
			video := bes.BidResponse_Ad_MetaInfo_SrcMaterial{
				Type:          proto.Int32(int32(1)),
				Url:           []byte(mhDataItem.Video.VideoURL),
				Width:         proto.Int32(int32(mhDataItem.Video.Width)),
				Height:        proto.Int32(int32(mhDataItem.Video.Height)),
				VideoDuration: proto.Int32(int32(mhDataItem.Video.Duration / 1000)),
			}
			videoArray = append(videoArray, &video)
			metaInfo.Video = videoArray

			ad.Width = proto.Int32(int32(mhDataItem.Video.Width))
			ad.Height = proto.Int32(int32(mhDataItem.Video.Height))
			ad.NativeAd.VideoUrl = proto.String(mhDataItem.Video.VideoURL)
			ad.NativeAd.VideoWidth = proto.Int32(int32(mhDataItem.Video.Width))
			ad.NativeAd.VideoHeight = proto.Int32(int32(mhDataItem.Video.Height))
			ad.NativeAd.VideoDuration = proto.Int32(int32(mhDataItem.Video.Duration / 1000))

			num, _ := strconv.Atoi(reqRtbConfig.VideoStyleID)
			ad.Type = proto.Int32(int32(num))
			metaInfo.CreativeType = proto.Int32(int32(num))

			ad.StyleType = proto.Int32(102)

			var metaInfoGroupArray []*bes.BidResponse_Ad_MetaInfoGroup
			var metaInfoGroup bes.BidResponse_Ad_MetaInfoGroup
			var metaInfoArray []*bes.BidResponse_Ad_MetaInfo

			metaInfoArray = append(metaInfoArray, &metaInfo)
			metaInfoGroup.MetaInfo = metaInfoArray
			metaInfoGroupArray = append(metaInfoGroupArray, &metaInfoGroup)
			ad.MetaInfoGroup = metaInfoGroupArray
		}

		adList = append(adList, ad)
	}

	if len(adList) == 0 {
		return &bes.BidResponse{
			Id: req.Id,
		}, http.StatusNoContent
	}

	resp := &bes.BidResponse{
		Id: req.Id,
		Ad: adList,
	}

	return resp, http.StatusOK
}

func getBesAppList(appIDList []uint32) []int {
	if len(appIDList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := besAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var besAppListCodeMap = map[uint32]int{
	100606: 1004,
	100299: 1005,
}
