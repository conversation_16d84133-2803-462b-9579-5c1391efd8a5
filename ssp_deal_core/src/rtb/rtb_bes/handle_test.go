package rtb_bes

import (
	"fmt"
	"mh_proxy/rtb/rtb_yoyo"
	"testing"
)

func TestHandlePrice(t *testing.T) {
	price := "Uja0xQADFz97jEpgW5IA8g0f455XNIjPRj8IqA"
	ekey := []byte("005e38cb01abc22fd5acd77001abc22fd5acfe8001abc22fd5ad06509b1bb875")
	ikey := []byte("005e38cb01abc22fd5ad6be001abc22fd5ad73b001abc22fd5ad7b802e88c532")

	decodePrice := rtb_yoyo.Decrypt(price, ekey, ikey)
	fmt.Println(decodePrice)
}
