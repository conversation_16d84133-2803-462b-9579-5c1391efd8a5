package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/wifi"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByWifi ...
func HandleByWifi(c *gin.Context, channel string) *wifi.RTBResponse {

	beginTime := utils.GetCurrentMilliSecond()

	bodyContent, err := c.GetRawData()

	wifiReq := &wifi.RTBRequest{}
	err = proto.Unmarshal(bodyContent, wifiReq)

	if err != nil {
		fmt.Println(err)
		return wifiNoBidReturn("error", "parser error")
	}
	// fmt.Println(wifiReq)
	// fmt.Println(wifiReq.GetSid())
	// fmt.Println(wifiReq.GetClientIp())
	// fmt.Println(wifiReq.GetUserAgent())
	// fmt.Println(wifiReq.GetNetType())
	// fmt.Println(wifiReq.GetOs())
	// fmt.Println(wifiReq.GetOsVersion())
	// fmt.Println(wifiReq.GetDeviceInfo())
	// fmt.Println(wifiReq.GetAppInfo())
	// fmt.Println(wifiReq.GetIdInfo().GetImei())
	// fmt.Println(wifiReq.GetIdInfo().GetMac())
	// fmt.Println(wifiReq.GetIdInfo().GetAndroidId())
	// fmt.Println(wifiReq.GetIdInfo().GetIdfa())
	// fmt.Println(wifiReq.GetAdSlots())
	// fmt.Println(wifiReq.GetIsTest())
	// fmt.Println(wifiReq.GetMediaIndex())

	reqOs := ""
	if strings.ToLower(wifiReq.GetOs()) == "android" {
		reqOs = "android"
	} else if strings.ToLower(wifiReq.GetOs()) == "ios" {
		reqOs = "ios"
	} else {
		return wifiNoBidReturn(wifiReq.GetSid(), "wrong os")
	}

	reqDeivceMake := wifiReq.GetDeviceInfo().GetVendor()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqImei := wifiReq.GetIdInfo().GetImei()
	reqAndroidID := wifiReq.GetIdInfo().GetAndroidId()
	reqOaid := wifiReq.GetIdInfo().GetOaid()
	reqIdfa := wifiReq.GetIdInfo().GetIdfa()

	reqConnectType := 1
	if wifiReq.GetNetType() == wifi.RTBRequest_NT_UnKnown {
		reqConnectType = 0
	} else if wifiReq.GetNetType() == wifi.RTBRequest_NT_Wifi {
		reqConnectType = 1
	} else if wifiReq.GetNetType() == wifi.RTBRequest_NT_Cellular_2G {
		reqConnectType = 2
	} else if wifiReq.GetNetType() == wifi.RTBRequest_NT_Cellular_3G {
		reqConnectType = 3
	} else if wifiReq.GetNetType() == wifi.RTBRequest_NT_Cellular_4G {
		reqConnectType = 4
	}

	reqCarrier := 0 // ???
	// if wifiReq.GetDevice().GetCarrier() == "中国移动" {
	// 	reqCarrier = 1
	// } else if wifiReq.GetDevice().GetCarrier() == "中国联通" {
	// 	reqCarrier = 2
	// } else if wifiReq.GetDevice().GetCarrier() == "中国电信" {
	// 	reqCarrier = 3
	// }

	reqSlots := wifiReq.GetAdSlots()
	// fmt.Println(len(reqSlots))
	if len(reqSlots) == 0 {
		return wifiNoBidReturn(wifiReq.GetSid(), "wrong slots")
	}

	var reqOKSlots []*wifi.RTBRequest_AdSlotInfo
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqSlots {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.GetId()
		reqPrice := item.GetMinCpm()

		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return wifiNoBidReturn(wifiReq.GetSid(), "not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKSlots = append(reqOKSlots, item)
	}
	// fmt.Println("aaaaaa")
	// fmt.Println(reqRtbConfig)
	// fmt.Println("bbbbbb")

	if len(reqOKSlots) == 0 {
		return wifiNoBidReturn(wifiReq.GetSid(), "wrong slots")
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: wifiReq.GetAppInfo().GetPkgName(),
			AppName:     wifiReq.GetAppInfo().GetAppName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    wifiReq.GetOsVersion(),
			Model:        wifiReq.GetDeviceInfo().GetModel(),
			Manufacturer: reqDeivceMake,
			Imei:         reqImei,
			AndroidID:    reqAndroidID,
			Oaid:         reqOaid,
			Idfa:         reqIdfa,
			Ua:           wifiReq.GetUserAgent(),
			ScreenWidth:  int(wifiReq.GetDeviceInfo().GetScreenWidth()),
			ScreenHeight: int(wifiReq.GetDeviceInfo().GetScreenHeight()),
			DeviceType:   1,
			IP:           wifiReq.GetClientIp(),
			AppList:      getWifiAppList(wifiReq.AppList),
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	//reqStu.Device.IPCity

	if mhResp.Ret != 0 {
		return wifiNoBidReturn(wifiReq.GetSid(), "no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return wifiNoBidReturn(wifiReq.GetSid(), "no fill")
	}
	//////////////////////////////////////////////////////////////////////////////////////////////
	wifiResp := &wifi.RTBResponse{
		Sid: proto.String(wifiReq.GetSid()),
	}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		wifiAdInfoItem := wifi.RTBResponse_AdInfo{
			Id:         proto.String(reqRtbConfig.OriginTagID),
			AdId:       proto.String(mhDataItem.AdID),
			CreativeId: proto.String(mhDataItem.Crid),
			IsHtml:     proto.Bool(false),
			MaxCpm:     proto.Uint32(uint32(ecpm)),
		}

		wifiJsonItem := wifi.Ad{
			Adid:    proto.String(mhDataItem.AdID),
			Title:   proto.String(mhDataItem.Title),
			Content: proto.String(mhDataItem.Description),
		}

		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				wifiJsonItem.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					wifiJsonItem.DeeplinkUrl = proto.String(mhDataItem.MarketURL)
				}
			}

			// wifiJsonItem.Template = proto.String("FeedsHugeImageTitle_1103")

			wifiJsonItem.AdType = proto.String("redirect")
			wifiJsonItem.LandingPageUrl = proto.String(mhDataItem.LandpageURL)
		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				wifiJsonItem.DeeplinkUrl = proto.String(mhDataItem.MarketURL)
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					wifiJsonItem.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
				}
			}

			// wifiJsonItem.Template = proto.String("FeedsAppImageTextButton_107")

			wifiJsonItem.AdType = proto.String("download")
			wifiJsonItem.DownloadUrl = proto.String(mhDataItem.DownloadURL)
		} else {
			continue
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			if strings.Contains(mhDataItem.DeepLink, "pinduoduo") || strings.Contains(mhDataItem.MarketURL, "pinduoduo") {
				wifiJsonItem.PkgName = proto.String("com.xunmeng.pinduoduo")
			} else if strings.Contains(mhDataItem.DeepLink, "taobao") || strings.Contains(mhDataItem.MarketURL, "taobao") {
				wifiJsonItem.PkgName = proto.String("com.taobao.taobao")
			}
		}

		if len(mhDataItem.IconURL) > 0 {
			wifiJsonItem.AppIcon = proto.String(mhDataItem.IconURL)
		}

		if reqRtbConfig.LocalAppID == "10399" && reqRtbConfig.LocalPosID == "58266" {
			wifiJsonItem.AppIcon = proto.String("https://static.maplehaze.cn/adimg/wifi/catenation-icon.png")
		}

		if len(mhDataItem.AppName) > 0 {
			wifiJsonItem.AppName = proto.String(mhDataItem.AppName)
		}

		if len(mhDataItem.PackageName) > 0 {
			wifiJsonItem.PkgName = proto.String(mhDataItem.PackageName)
		}

		if isVideoType == 0 {
			wifiJsonItem.ImageUrls = append(wifiJsonItem.ImageUrls, mhDataItem.Image[0].URL)
		} else {
			wifiJsonItem.VideoUrl = proto.String(mhDataItem.Video.VideoURL)
		}

		// deeplink links
		for _, clkItem := range mhDataItem.ClickLink {
			wifiJsonItem.DeeplinkClickUrls = append(wifiJsonItem.DeeplinkClickUrls, clkItem)
		}
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					wifiJsonItem.DeeplinkClickUrls = append(wifiJsonItem.DeeplinkClickUrls, trackEventItem)
				}
			}
		}
		// exp links
		for _, impItem := range mhDataItem.ImpressionLink {
			wifiJsonItem.ShowUrls = append(wifiJsonItem.ShowUrls, impItem)
		}
		// win price
		// https://adx.maplehaze.cn/rtb/price?price=%%PRICE%%&ext=%%EXT_DATA%%
		// winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(reqRtbConfig.Price) + "&channel=" + channel + "&price=%%PRICE%%" + "&log=" + url.QueryEscape(mhDataItem.Log)
		winURLExtraData := "1&" + "uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&log=" + url.QueryEscape(mhDataItem.Log)
		wifiAdInfoItem.ExtData = proto.String(winURLExtraData)

		// click links
		for _, clkItem := range mhDataItem.ClickLink {
			wifiJsonItem.ClickUrls = append(wifiJsonItem.ClickUrls, clkItem)
		}

		// ???
		// video start finish link
		// for _, trackItem := range mhDataItem.Video.EventTracks {
		// 	if trackItem.EventType == 100 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			wifiJsonItem.VideoStart = append(wifiJsonItem.VideoStart, trackEventItem)
		// 		}
		// 	} else if trackItem.EventType == 103 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			wifiJsonItem.VideoEnd = append(wifiJsonItem.VideoEnd, trackEventItem)
		// 		}
		// 	}
		// }

		// json
		var wifiCommonTemplateAds []*wifi.Ad
		wifiCommonTemplateAds = append(wifiCommonTemplateAds, &wifiJsonItem)

		wifiCommonTemplate := wifi.CommonTemplate{
			// Template: wifiJsonItem.Template,
			Ads: wifiCommonTemplateAds,
		}
		wifiReqTypeIDList := wifiReq.GetTypeIdList()
		if len(wifiReqTypeIDList) > 0 {
			wifiCommonTemplate.TypeId = &wifiReqTypeIDList[0]
		} else {
			if isVideoType == 0 {
				wifiCommonTemplate.TypeId = proto.String("23")
			} else {
				if mhDataItem.Video.Width > mhDataItem.Video.Height {
					wifiCommonTemplate.TypeId = proto.String("200000089")
				} else {
					wifiCommonTemplate.TypeId = proto.String("200000162")
				}
			}
		}

		tmpjsonByte, _ := json.Marshal(&wifiCommonTemplate)
		tmpJsonString := string(tmpjsonByte)
		// fmt.Println("wifi json: ", tmpJsonString)

		wifiAdInfoItem.Json = proto.String(tmpJsonString)

		wifiResp.AdInfos = append(wifiResp.AdInfos, &wifiAdInfoItem)
	}

	wifiResp.ProcessTimeMs = proto.Uint32(uint32(utils.GetCurrentMilliSecond() - beginTime))

	// tmpByte, _ := json.Marshal(wifiResp)
	// fmt.Println("wifi resp:" + string(tmpByte))

	return wifiResp
}

func wifiNoBidReturn(reqID string, reason string) *wifi.RTBResponse {
	// fmt.Println(reason)

	wifiNoResp := &wifi.RTBResponse{
		Sid: proto.String(reqID),
	}
	return wifiNoResp
}

func getWifiAppList(appIDList []string) []int {
	if len(appIDList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := wifiAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var wifiAppListCodeMap = map[string]int{
	"1001": 1001,
	"1002": 1002,
	"1003": 1003,
	"1004": 1004,
	"1005": 1005,
	"1006": 1006,
	"1007": 1007,
	"1008": 1008,
	"1009": 1009,
	"1010": 1010}
