package rtb

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByZhiboba ...
func HandleByZhiboba(c *gin.Context, channel string) (*map[string]interface{}, int) {
	// fmt.Println("zhiboba rtb")
	// timeBegin := utils.GetCurrentMilliSecond()
	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	acceptEncoding := c.GetHeader("Accept-Encoding")
	contentEncoding := c.GetHeader("Content-Encoding")

	if strings.Contains(acceptEncoding, "gzip") && strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	// fmt.Println(timeBegin)
	var zhiBoBaReq ZhiBoBaReq

	err := json.Unmarshal(bodyContent, &zhiBoBaReq)

	if err != nil {
		fmt.Println("zhiboba_error:", err)

		return jsonZhibobaNoBidReturn("parser error")
	}

	// fmt.Println(zhiBoBaReq.ID)
	// fmt.Println(string(bodyContent))
	// fmt.Println(err)

	reqOs := ""
	if zhiBoBaReq.Device.Os == 4 {
		reqOs = "android"
	} else if zhiBoBaReq.Device.Os == 3 {
		reqOs = "ios"
	} else {
		return jsonZhibobaNoBidReturn("wrong os")
	}

	reqDeivceMake := zhiBoBaReq.Device.Make
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(zhiBoBaReq.Device.UA) == 0 {
		return jsonZhibobaNoBidReturn("wrong ua")
	}

	reqConnectType := 0
	if zhiBoBaReq.Device.ConnectionType == 0 {
		reqConnectType = 1
	} else if zhiBoBaReq.Device.ConnectionType == 1 {
		reqConnectType = 2
	} else if zhiBoBaReq.Device.ConnectionType == 2 {
		reqConnectType = 3
	} else if zhiBoBaReq.Device.ConnectionType == 3 {
		reqConnectType = 4
	} else {
		reqConnectType = 0
	}

	reqCarrier := 0
	if zhiBoBaReq.Device.Carrier == 0 {
		reqCarrier = 3
	} else if zhiBoBaReq.Device.Carrier == 1 {
		reqCarrier = 1
	} else if zhiBoBaReq.Device.Carrier == 2 {
		reqCarrier = 2
	} else if zhiBoBaReq.Device.Carrier == 3 {
		reqCarrier = 3
	} else {
		reqCarrier = 0
	}

	if len(zhiBoBaReq.Imp) == 0 {
		return jsonZhibobaNoBidReturn("wrong imp")
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(zhiBoBaReq.Device.CAIDList) > 0 {
		for _, item := range zhiBoBaReq.Device.CAIDList {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = item.Caid
			caidMulti.CAIDVersion = item.CaidVersion
			caidMultiList = append(caidMultiList, caidMulti)
		}
	}

	var reqOKImps []ZhiBoBaReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range zhiBoBaReq.Imp {
		if len(item.Native.Assets) == 0 {
			return jsonZhibobaNoBidReturn("wrong native")
		}

		reqTagID := item.SlotID
		reqPrice := item.BidInfo[0].BidFloor

		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		if len(*rtbConfigArrayByTagID) == 0 {
			return jsonZhibobaNoBidReturn("not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return jsonZhibobaNoBidReturn("wrong imp")
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: zhiBoBaReq.App.Bundle,
			AppName:     zhiBoBaReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    zhiBoBaReq.Device.Osv,
			Model:        zhiBoBaReq.Device.Model,
			Manufacturer: reqDeivceMake,
			ImeiMd5:      zhiBoBaReq.Device.ImeiMd5,
			AndroidIDMd5: zhiBoBaReq.Device.AndroidIDMd5,
			Oaid:         zhiBoBaReq.Device.Oaid,
			OaidMd5:      zhiBoBaReq.Device.OaidMd5,
			// MacMd5:       zhiBoBaReq.Device.MacMd5,
			Idfa:               zhiBoBaReq.Device.Idfa,
			IdfaMd5:            zhiBoBaReq.Device.IdfaMd5,
			Ua:                 zhiBoBaReq.Device.UA,
			ScreenWidth:        zhiBoBaReq.Device.W,
			ScreenHeight:       zhiBoBaReq.Device.H,
			DeviceType:         1,
			IP:                 zhiBoBaReq.Device.IP,
			DeviceStartSec:     zhiBoBaReq.Device.DeviceStartSec,
			Country:            zhiBoBaReq.Device.Country,
			Language:           zhiBoBaReq.Device.Language,
			DeviceNameMd5:      zhiBoBaReq.Device.DeviceNameMd5,
			HardwareMachine:    zhiBoBaReq.Device.Model,
			HardwareModel:      zhiBoBaReq.Device.HardwareModel,
			PhysicalMemoryByte: zhiBoBaReq.Device.PhysicalMemoryByte,
			HarddiskSizeByte:   zhiBoBaReq.Device.HarddiskSizeByte,
			SystemUpdateSec:    zhiBoBaReq.Device.SystemUpdateSec,
			TimeZone:           zhiBoBaReq.Device.TimeZone,
			BootMark:           zhiBoBaReq.Device.BootMark,
			UpdateMark:         zhiBoBaReq.Device.UpdateMark,
			DeviceBirthSec:     zhiBoBaReq.Device.DeviceBirthSec,
			CAIDMulti:          caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	if zhiBoBaReq.Device.AppStoreVersion != 0 {
		reqStu.Device.AppStoreVersion = utils.ConvertIntToString(zhiBoBaReq.Device.AppStoreVersion)
	}
	if zhiBoBaReq.Device.HMSCoreVersion != 0 {
		reqStu.Device.HMSCoreVersion = utils.ConvertIntToString(zhiBoBaReq.Device.HMSCoreVersion)
	}

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return jsonZhibobaNoBidReturn("no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonZhibobaNoBidReturn("no fill")
	}

	var impItem ZhiBoBaReqImp
	for _, imp := range reqOKImps {
		if imp.SlotID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}
	if len(impItem.SlotID) == 0 {
		return jsonZhibobaNoBidReturn("no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	var zhibobaRespSeatIDArrayMap []map[string]interface{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// crid
		crid := utils.GetMd5(mhDataItem.Title)
		if len(mhDataItem.Crid) > 0 {
			crid = mhDataItem.Crid
		}

		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}
		impTrackArray = append(impTrackArray, winURL)

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(zhiBoBaReq.Device.W), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(zhiBoBaReq.Device.H), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}

		// admnative
		zhibobaRespBidAdmNativeMap := map[string]interface{}{}
		zhibobaRespBidAdmNativeMap["imptrackers"] = impTrackArray

		// admnative link
		zhibobaRespBidAdmNativeLinkMap := map[string]interface{}{}
		zhibobaRespBidAdmNativeLinkMap["clicktrackers"] = clkTrackArray

		if mhDataItem.InteractType == 0 {
			zhibobaRespBidAdmNativeLinkMap["url"] = mhDataItem.LandpageURL
		} else if mhDataItem.InteractType == 1 {
			zhibobaRespBidAdmNativeLinkMap["url"] = mhDataItem.DownloadURL
		}

		// 原生广告展示区块允许的物料交互方式列表
		// 0: LANDING_OPEN_IN_BROWSER 1: LANDING_OPEN_IN_WEBVIEW 2: LANDING_DOWNLOAD_IN_APP 3: LANDING_JUMP_TO_APPSTORE
		if reqOs == "ios" {
			zhibobaRespBidAdmNativeLinkMap["landingtype"] = 3
		} else if reqOs == "android" {
			if mhDataItem.InteractType == 0 {
				if landingTypeContains(impItem.Native.Landingtype, 1) {
					zhibobaRespBidAdmNativeLinkMap["landingtype"] = 1
				} else if landingTypeContains(impItem.Native.Landingtype, 0) {
					zhibobaRespBidAdmNativeLinkMap["landingtype"] = 0
				} else {
					continue
				}
			} else if mhDataItem.InteractType == 1 {
				if landingTypeContains(impItem.Native.Landingtype, 2) {
					zhibobaRespBidAdmNativeLinkMap["landingtype"] = 2
				} else {
					continue
				}
			}
		}

		zhibobaRespBidAdmNativeMap["link"] = zhibobaRespBidAdmNativeLinkMap

		isAssetaHasVideo := 0
		for _, assetItem := range impItem.Native.Assets {
			// fmt.Println(assetItem.Video.Size)
			if assetItem.Video.Size > 0 {
				isAssetaHasVideo = 1
				break
			}
		}

		var zhibobaRespAssets []map[string]interface{}

		// assets title
		zhibobaRespAssetsTitle := map[string]interface{}{}
		zhibobaRespAssetsTitle["id"] = len(zhibobaRespAssets)
		zhibobaRespAssetsTitle["required"] = 0
		zhibobaRespAssetsTitleTxt := map[string]interface{}{}
		zhibobaRespAssetsTitleTxt["text"] = mhDataItem.Description
		zhibobaRespAssetsTitle["title"] = zhibobaRespAssetsTitleTxt
		zhibobaRespAssets = append(zhibobaRespAssets, zhibobaRespAssetsTitle)

		// assets img
		zhibobaRespAssetsImg := map[string]interface{}{}
		zhibobaRespAssetsImg["id"] = len(zhibobaRespAssets)
		zhibobaRespAssetsImg["required"] = 1
		if isVideoType == 0 {
			zhibobaRespAssetsImgURL := map[string]interface{}{}
			zhibobaRespAssetsImgURL["w"] = mhDataItem.Image[0].Width
			zhibobaRespAssetsImgURL["h"] = mhDataItem.Image[0].Height
			zhibobaRespAssetsImgURL["url"] = mhDataItem.Image[0].URL
			if strings.Contains(mhDataItem.Image[0].URL, "mp4") {
				fmt.Println("wrong img url: " + mhDataItem.Image[0].URL)
				continue
			}

			zhibobaRespAssetsImg["img"] = zhibobaRespAssetsImgURL
		} else if isVideoType == 1 {
			zhibobaRespAssetsImgURL := map[string]interface{}{}
			zhibobaRespAssetsImgURL["url"] = mhDataItem.Video.CoverURL
			if strings.Contains(mhDataItem.Video.CoverURL, "mp4") {
				fmt.Println("wrong cover url: " + mhDataItem.Image[0].URL)
				continue
			}
			zhibobaRespAssetsImg["img"] = zhibobaRespAssetsImgURL
		}
		zhibobaRespAssets = append(zhibobaRespAssets, zhibobaRespAssetsImg)

		// assets video
		if isVideoType == 1 && isAssetaHasVideo == 1 {
			zhibobaRespAssetsVideo := map[string]interface{}{}
			zhibobaRespAssetsVideo["id"] = len(zhibobaRespAssets)
			zhibobaRespAssetsVideo["required"] = 1

			zhibobaRespAssetsVideoURL := map[string]interface{}{}
			zhibobaRespAssetsVideoURL["w"] = mhDataItem.Video.Height
			zhibobaRespAssetsVideoURL["h"] = mhDataItem.Video.Width
			zhibobaRespAssetsVideoURL["url"] = mhDataItem.Video.VideoURL

			zhibobaRespAssetsVideo["video"] = zhibobaRespAssetsVideoURL

			zhibobaRespAssets = append(zhibobaRespAssets, zhibobaRespAssetsVideo)
		}

		// assets deeplink, app name
		if len(mhDataItem.AppName) > 0 || len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 ||
			len(mhDataItem.Publisher) > 0 || len(mhDataItem.AppVersion) > 0 ||
			len(mhDataItem.PrivacyLink) > 0 || len(mhDataItem.Permission) > 0 || mhDataItem.PackageSize > 0 {
			zhibobaRespAssetsExtra := map[string]interface{}{}
			zhibobaRespAssetsExtra["id"] = len(zhibobaRespAssets)
			zhibobaRespAssetsExtra["required"] = 0

			zhibobaRespAssetsExtraData := map[string]interface{}{}
			if len(mhDataItem.AppName) > 0 {
				zhibobaRespAssetsExtraData["adname"] = mhDataItem.AppName

			}
			if mhDataItem.InteractType == 1 {
				if len(mhDataItem.MarketURL) > 0 {
					zhibobaRespAssetsExtraData["deeplink"] = mhDataItem.MarketURL
				} else {
					if len(mhDataItem.DeepLink) > 0 {
						zhibobaRespAssetsExtraData["deeplink"] = mhDataItem.DeepLink
					}
				}
			} else if mhDataItem.InteractType == 0 {
				if len(mhDataItem.DeepLink) > 0 {
					zhibobaRespAssetsExtraData["deeplink"] = mhDataItem.DeepLink
				} else {
					if len(mhDataItem.MarketURL) > 0 {
						zhibobaRespAssetsExtraData["deeplink"] = mhDataItem.MarketURL
					}
				}
			}

			if len(mhDataItem.Publisher) > 0 {
				zhibobaRespAssetsExtraData["publisher"] = mhDataItem.Publisher
			}
			if len(mhDataItem.AppVersion) > 0 {
				zhibobaRespAssetsExtraData["app_version"] = mhDataItem.AppVersion
			}
			if len(mhDataItem.PrivacyLink) > 0 {
				zhibobaRespAssetsExtraData["privacy_link"] = mhDataItem.PrivacyLink
			}
			if len(mhDataItem.Permission) > 0 {
				zhibobaRespAssetsExtraData["permission_link"] = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
			}
			if mhDataItem.PackageSize > 0 {
				zhibobaRespAssetsExtraData["app_size"] = mhDataItem.PackageSize
			}
			if mhDataItem.InteractType == 0 {
				zhibobaRespAssetsExtraData["isApp"] = 0
			} else if mhDataItem.InteractType == 1 {
				zhibobaRespAssetsExtraData["isApp"] = 1
			}

			zhibobaRespAssetsExtra["data"] = zhibobaRespAssetsExtraData

			zhibobaRespAssets = append(zhibobaRespAssets, zhibobaRespAssetsExtra)
		}

		zhibobaRespBidAdmNativeMap["assets"] = zhibobaRespAssets

		// deeplink tracking
		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}

		// 视频播放监控链接
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				}
			}
		}

		isNeedActionTrackers := false
		zhibobaRespBidAdmNativeActionTrackersMap := map[string]interface{}{}

		if len(videoStartTrackArray) > 0 || len(videoFinishTrackArray) > 0 {
			isNeedActionTrackers = true

			zhibobaRespBidAdmNativeVideoEventMap := map[string]interface{}{}
			if len(videoStartTrackArray) > 0 {
				zhibobaRespBidAdmNativeVideoEventMap["start"] = videoStartTrackArray
			}
			if len(videoFinishTrackArray) > 0 {
				zhibobaRespBidAdmNativeVideoEventMap["complete"] = videoFinishTrackArray
			}

			zhibobaRespBidAdmNativeActionTrackersMap["video_events"] = zhibobaRespBidAdmNativeVideoEventMap
		}

		if len(deepLinkTrackOKArray) > 0 || len(deepLinkTrackFailedArray) > 0 {
			isNeedActionTrackers = true

			zhibobaRespBidAdmNativeDeepLinkEventMap := map[string]interface{}{}
			if len(deepLinkTrackOKArray) > 0 {
				zhibobaRespBidAdmNativeDeepLinkEventMap["success"] = deepLinkTrackOKArray
			}
			if len(deepLinkTrackFailedArray) > 0 {
				zhibobaRespBidAdmNativeDeepLinkEventMap["failed"] = deepLinkTrackFailedArray
			}

			zhibobaRespBidAdmNativeActionTrackersMap["deeplink_events"] = zhibobaRespBidAdmNativeDeepLinkEventMap
		}

		if isNeedActionTrackers {
			zhibobaRespBidAdmNativeMap["actiontrackers"] = zhibobaRespBidAdmNativeActionTrackersMap
		}

		zhibobaRespBidMap := map[string]interface{}{}
		zhibobaRespBidMap["impid"] = impItem.ID
		zhibobaRespBidMap["bidtype"] = 0
		zhibobaRespBidMap["cid"] = crid
		zhibobaRespBidMap["price"] = ecpm
		zhibobaRespBidMap["admnative"] = zhibobaRespBidAdmNativeMap

		zhibobaRespBidObjMap := map[string]interface{}{}
		zhibobaRespBidObjMap["bid"] = zhibobaRespBidMap

		zhibobaRespSeatIDArrayMap = append(zhibobaRespSeatIDArrayMap, zhibobaRespBidObjMap)
	}

	if len(zhibobaRespSeatIDArrayMap) == 0 {
		return jsonZhibobaNoBidReturn("not fill")
	}

	zhibobaRespMap := map[string]interface{}{}
	zhibobaRespMap["id"] = zhiBoBaReq.ID
	zhibobaRespMap["bidid"] = "bid_" + utils.ConvertInt64ToString(utils.GetCurrentSecond())
	zhibobaRespMap["seatbid"] = zhibobaRespSeatIDArrayMap

	return jsonZhibobaOKBidReturn(&zhibobaRespMap)
}

func jsonZhibobaOKBidReturn(resp *map[string]interface{}) (*map[string]interface{}, int) {
	return resp, 200
}

func jsonZhibobaNoBidReturn(reason string) (*map[string]interface{}, int) {
	return nil, 204
}

func landingTypeContains(array []int, val int) bool {
	for i := 0; i < len(array); i++ {
		if array[i] == val {
			return true
		}
	}
	return false
}

// DecryptZhibobaPrice ...
func DecryptZhibobaPrice(finalMessage string, eKey string, iKey string) int {
	encryptedPrice := utils.AddBase64Padding(finalMessage)
	decoded, _ := base64.URLEncoding.DecodeString(encryptedPrice)

	var (
		iv         = make([]byte, 16)
		p          = make([]byte, 8)
		signature  = make([]byte, 4)
		priceBytes = make([]byte, 8)
	)

	copy(iv[:], decoded[0:16])
	copy(p[:], decoded[16:24])
	copy(signature[:], decoded[24:28])

	pad, _ := utils.GenerateHmac(eKey, iv[:])
	pad = []byte(fmt.Sprintf("%x", pad))

	pad = pad[:8]

	for i := range p {
		priceBytes[i] = pad[i] ^ p[i]
	}

	sig, _ := utils.GenerateHmac(iKey, append(priceBytes[:], iv[:]...))
	sig = []byte(fmt.Sprintf("%x", sig))

	sig = sig[:4]

	for i := range sig {
		if sig[i] != signature[i] {
			fmt.Println("Failed to decrypt")
			return 0
		}
	}

	return utils.ConvertStringToInt(string(priceBytes))
}

// ZhiBoBaReq ...
type ZhiBoBaReq struct {
	ID     string           `json:"id"`
	App    ZhiBoBaReqApp    `json:"app"`
	Device ZhiBoBaReqDevice `json:"device"`
	Imp    []ZhiBoBaReqImp  `json:"imp"`
}

// ZhiBoBaReqApp ...
type ZhiBoBaReqApp struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Version string `json:"ver"`
	Bundle  string `json:"bundle"`
	KeyWord string `json:"keywords"`
}

// ZhiBoBaReqDevice ...
type ZhiBoBaReqDevice struct {
	UA             string              `json:"ua"`
	IP             string              `json:"ip"`
	IPV6           string              `json:"ipv6"`
	DeviceType     int                 `json:"devicetype"`
	Make           string              `json:"make"`
	Model          string              `json:"model"`
	Os             int                 `json:"os"`
	Osv            string              `json:"osv"`
	W              int                 `json:"w"`
	H              int                 `json:"h"`
	Carrier        int                 `json:"carrier"`
	ConnectionType int                 `json:"connectiontype"`
	MacMd5         string              `json:"macmd5"`
	ImeiMd5        string              `json:"didmd5"`
	AndroidIDMd5   string              `json:"dpidmd5"`
	Oaid           string              `json:"oaid"`
	OaidMd5        string              `json:"oaidmd5"`
	Idfa           string              `json:"idfa"`
	IdfaMd5        string              `json:"idfamd5"`
	Geo            ZhiBoBaReqDeviceGeo `json:"geo"`
	// appstore version, hms version
	AppStoreVersion int `json:"as_version_code"`
	HMSCoreVersion  int `json:"hms_core_version_code"`
	// ios 14 新增字段
	DeviceStartSec string `json:"boot_time_sec"`
	Country        string `json:"geo_country"`
	Language       string `json:"device_lan"`
	DeviceNameMd5  string `json:"phone_name_md5"`
	//HardwareMachine    string `json:"model"`
	HardwareModel      string                 `json:"model_code"`
	PhysicalMemoryByte string                 `json:"memory_size"`
	HarddiskSizeByte   string                 `json:"disk_size"`
	SystemUpdateSec    string                 `json:"os_update_time_sec"`
	DeviceBirthSec     string                 `json:"init_time"`
	TimeZone           string                 `json:"time_zone"`
	CAID               []string               `json:"caid"`
	CAIDList           []ZhiBoBaReqDeviceCAID `json:"caid_list"`
	// boot_mark update_mark
	BootMark   string `json:"android_boot_mark"`
	UpdateMark string `json:"android_update_mark"`
}

// ZhiBoBaReqDeviceGeo ...
type ZhiBoBaReqDeviceGeo struct {
	Lat float32 `json:"lat"`
	Lon float32 `json:"lon"`
}

type ZhiBoBaReqDeviceCAID struct {
	Caid        string `json:"caid"`
	CaidVersion string `json:"version"`
}

// ZhiBoBaReqImp ...
type ZhiBoBaReqImp struct {
	ID      string                 `json:"id"`
	SlotID  string                 `json:"slotid"`
	BidInfo []ZhiBoBaReqImpBidInfo `json:"bidinfo"`
	Native  ZhiBoBaReqImpNative    `json:"native"`
}

// ZhiBoBaReqImpBidInfo ...
type ZhiBoBaReqImpBidInfo struct {
	BidType  int `json:"bidtype"`
	BidFloor int `json:"bidfloor"`
}

// ZhiBoBaReqImpNative ...
type ZhiBoBaReqImpNative struct {
	Assets      []ZhiBoBaReqImpNativeAssets `json:"assets"`
	Landingtype []int                       `json:"landingtype"`
}

// ZhiBoBaReqImpNativeAssets ...
type ZhiBoBaReqImpNativeAssets struct {
	ID       int                            `json:"id"`
	Required int                            `json:"required"`
	Title    ZhiBoBaReqImpNativeAssetsTitle `json:"title"`
	Img      ZhiBoBaReqImpNativeAssetsImg   `json:"img"`
	Video    ZhiBoBaReqImpNativeAssetsVideo `json:"video"`
}

// ZhiBoBaReqImpNativeAssetsTitle ...
type ZhiBoBaReqImpNativeAssetsTitle struct {
	Len int `json:"len"`
}

// ZhiBoBaReqImpNativeAssetsImg ...
type ZhiBoBaReqImpNativeAssetsImg struct {
	W int `json:"w"`
	H int `json:"h"`
}

// ZhiBoBaReqImpNativeAssetsVideo ...
type ZhiBoBaReqImpNativeAssetsVideo struct {
	Minduration int `json:"minduration"`
	Maxduration int `json:"maxduration"`
	Size        int `json:"size"`
}
