package rtb

import (
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/zuiyou"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByZuiyou ...
func HandleByZuiyou(c *gin.Context, channel string) *zuiyou.RtbResponse {
	// fmt.Println("zuiyou rtb")

	timeBegin := utils.GetCurrentMilliSecond()
	bodyContent, err := c.GetRawData()

	zuiyouReq := &zuiyou.RtbRequest{}
	err = proto.Unmarshal(bodyContent, zuiyouReq)
	if err != nil {
		fmt.Println(err)
		return NoBidReturn("parser error")
	}

	// fmt.Println(zuiyouReq)
	// fmt.Println(zuiyouReq.GetApiVersion())
	// fmt.Println(zuiyouReq.GetDevice().GetOsType())
	// fmt.Println(zuiyouReq.GetDevice().GetIdfa())
	// fmt.Println(zuiyouReq.GetDevice().GetUa())
	// fmt.Println(zuiyouReq.GetDevice().GetIp())

	reqOs := ""
	if zuiyouReq.GetDevice().GetOsType() == zuiyou.RtbRequest_Device_ANDROID {
		reqOs = "android"
	} else if zuiyouReq.GetDevice().GetOsType() == zuiyou.RtbRequest_Device_IOS {
		reqOs = "ios"
	} else {
		return NoBidReturn("wrong os")
	}

	reqDeivceMake := zuiyouReq.GetDevice().GetMake()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqConnectType := 1
	if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_CONN_UNKNOWN {
		reqConnectType = 0
	} else if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_WIFI {
		reqConnectType = 1
	} else if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_MOBILE_2G {
		reqConnectType = 2
	} else if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_MOBILE_3G {
		reqConnectType = 3
	} else if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_MOBILE_4G {
		reqConnectType = 4
	} else if zuiyouReq.GetDevice().GetConnType() == zuiyou.RtbRequest_Device_MOBILE_5G {
		reqConnectType = 7
	}

	reqCarrier := 0
	if zuiyouReq.GetDevice().GetCarrier() == zuiyou.RtbRequest_Device_CARRIER_UNKNOWN {
		reqCarrier = 0
	} else if zuiyouReq.GetDevice().GetCarrier() == zuiyou.RtbRequest_Device_MOBILE {
		reqCarrier = 1
	} else if zuiyouReq.GetDevice().GetCarrier() == zuiyou.RtbRequest_Device_UNICOM {
		reqCarrier = 2
	} else if zuiyouReq.GetDevice().GetCarrier() == zuiyou.RtbRequest_Device_TELECOM {
		reqCarrier = 3
	}

	reqEnableSplashDownload := 0
	reqEnableFeedDpDownload := 0
	if zuiyouReq.GetFeaturesSupport().GetEnableSplashDownload() == zuiyou.RtbRequest_ENABLE {
		reqEnableSplashDownload = 1
	}
	if zuiyouReq.GetFeaturesSupport().GetEnableFeedDpDownload() == zuiyou.RtbRequest_ENABLE {
		reqEnableFeedDpDownload = 1
	}
	// fmt.Println(reqEnableSplashDownload)
	// fmt.Println(reqEnableFeedDpDownload)

	reqSlots := zuiyouReq.GetAdslots()
	// fmt.Println(len(reqSlots))
	if len(reqSlots) == 0 {
		return NoBidReturn("wrong slots")
	}

	// zuiyouTagIDArray = []string{
	// 	"zuiyouadx0001", "zuiyouadx0101", "zuiyouadx1101", "zuiyouadx1001", "zuiyouadx1100", "zuiyouadx0100",
	// 	"zuiyoudsp0001", "zuiyoudsp0101", "zuiyoudsp1101", "zuiyoudsp1001", "zuiyoudsp1100", "zuiyoudsp0100",
	// 	"zuiyouadx0000", "zuiyouadx1000", "zuiyouadx0102", "zuiyouadx0002", "zuiyouadx1102", "zuiyouadx1002",
	// }
	// xuleiTagIDArray = []string{
	// 	"zuiyouadx2101", "zuiyouadx2100", "zuiyouadx2001", "zuiyouadx2000", "zuiyouadx2102", "zuiyouadx2002",
	// }

	var reqOKSlots []*zuiyou.RtbRequest_Adslot
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqSlots {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.GetTagId()[0:13]
		reqPrice := item.GetPrice()
		reqMaterialType := item.GetMaterialTypes()
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// fmt.Println(reqMaterialType)
		reqStyleID := ""
		if reqTagID == "zuiyouadx2107" {
			if mtTypeContains(reqMaterialType, zuiyou.MType_VIDEO) {
				// reqTagID = "zuiyouadx2107_video"
				reqStyleID = "video"
			} else {
				// reqTagID = "zuiyouadx2107_image"
				reqStyleID = "image"
			}
		}
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return NoBidReturn("not active")
		// }

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, reqStyleID, "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return NoBidReturn("not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKSlots = append(reqOKSlots, item)
	}
	// fmt.Println("aaaaaa")
	// fmt.Println(reqRtbConfig)
	// fmt.Println("bbbbbb")

	if len(reqOKSlots) == 0 {
		return NoBidReturn("wrong slots")
	}
	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	devicestartsec := zuiyouReq.GetDevice().GetStartupTime()
	if len(devicestartsec) > 0 {
		tmpDevicestartsecArray := strings.Split(devicestartsec, ",")
		if len(tmpDevicestartsecArray) > 0 {
			devicestartsec = tmpDevicestartsecArray[0]
		}
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(zuiyouReq.GetDevice().GetCaidList()) > 0 {
		for _, caidItem := range zuiyouReq.GetDevice().GetCaidList() {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.GetCaid()
			caidMulti.CAIDVersion = caidItem.GetVersion()
			caidMultiList = append(caidMultiList, caidMulti)
		}
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: zuiyouReq.GetApp().GetPackageName(),
			AppName:     zuiyouReq.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 reqOs,
			OsVersion:          zuiyouReq.GetDevice().GetOsVersion(),
			Model:              zuiyouReq.GetDevice().GetModel(),
			Manufacturer:       reqDeivceMake,
			Imei:               zuiyouReq.GetDevice().GetImei(),
			AndroidID:          zuiyouReq.GetDevice().GetAndroidId(),
			Oaid:               zuiyouReq.GetDevice().GetOaid(),
			Mac:                zuiyouReq.GetDevice().GetMac(),
			Idfa:               zuiyouReq.GetDevice().GetIdfa(),
			Ua:                 zuiyouReq.GetDevice().GetUa(),
			ScreenWidth:        int(zuiyouReq.GetDevice().GetScreenWidth()),
			ScreenHeight:       int(zuiyouReq.GetDevice().GetScreenHeight()),
			DeviceType:         1,
			IP:                 zuiyouReq.GetDevice().GetIp(),
			DeviceStartSec:     devicestartsec,
			Country:            zuiyouReq.GetDevice().GetCountryCode(),
			Language:           zuiyouReq.GetDevice().GetLanguage(),
			DeviceNameMd5:      zuiyouReq.GetDevice().GetPhoneName(),
			HardwareMachine:    zuiyouReq.GetDevice().GetOrgModel(),
			HardwareModel:      zuiyouReq.GetDevice().GetDeviceModel(),
			PhysicalMemoryByte: utils.ConvertIntToString(int(zuiyouReq.GetDevice().GetMemTotal())),
			HarddiskSizeByte:   utils.ConvertIntToString(int(zuiyouReq.GetDevice().GetDiskTotal())),
			SystemUpdateSec:    zuiyouReq.GetDevice().GetMbTime(),
			TimeZone:           zuiyouReq.GetDevice().GetLocalTzTime(),
			BootMark:           zuiyouReq.GetDevice().GetBootMark(),
			UpdateMark:         zuiyouReq.GetDevice().GetUpdateMark(),
			DeviceBirthSec:     zuiyouReq.GetDevice().GetBirthTime(),
			AppList:            getZuiyouAppList(zuiyouReq.PkgList),
			CAIDMulti:          caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return NoBidReturn("no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return NoBidReturn("no fill")
	}

	var impItem *zuiyou.RtbRequest_Adslot
	for _, imp := range reqOKSlots {
		if imp.GetTagId()[0:13] == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return NoBidReturn("no fill")
	}

	//////////////////////////////////////////////////////////////////////////////////////////////
	zuiyouResp := &zuiyou.RtbResponse{
		ResponseId:       zuiyouReq.GetRequestId(),
		SiteName:         "maplehaze",
		ProcessingTimeMs: uint32(utils.GetCurrentMilliSecond() - timeBegin),
		StatusCode:       zuiyou.RtbResponse_REQ_OK,
	}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__WIN_PRICE_SECURE__" + "&log=" + url.QueryEscape(mhDataItem.Log)
		zuiyouRespAd := zuiyou.RtbResponse_Ad{
			Impid:     impItem.GetImpid(),
			Price:     uint64(ecpm),
			PriceType: zuiyou.RtbResponse_Ad_CPM,
			WinUrl:    winURL,
		}
		// crid
		zuiyouRespAd.CreativeId = utils.GetMd5(mhDataItem.Title)
		if len(mhDataItem.Crid) > 0 {
			zuiyouRespAd.CreativeId = mhDataItem.Crid
		}

		// fmt.Println("xxx")
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(int(zuiyouReq.GetDevice().GetScreenWidth())), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(int(zuiyouReq.GetDevice().GetScreenHeight())), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}

		// video start finish link
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				}
			}
		}

		// deeplink tracking
		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}

		zuiyouRespAd.Tracking = &zuiyou.RtbResponse_Ad_Tracking{
			ImpTracking:            impTrackArray,
			ClickTracking:          clkTrackArray,
			VideoStartTracking:     videoStartTrackArray,
			VideoFinishTracking:    videoFinishTrackArray,
			InvokeWaitSuccTracking: deepLinkTrackOKArray,
			InvokeFailedTracking:   deepLinkTrackFailedArray,
		}

		// templates
		templatesArray := impItem.GetTemplates()
		var respTemplateID zuiyou.Template
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
				if templateContains(templatesArray, zuiyou.Template_FEED_OPEN_APP) {
					respTemplateID = zuiyou.Template_FEED_OPEN_APP
				} else if templateContains(templatesArray, zuiyou.Template_FEED_DEFAULT) {
					respTemplateID = zuiyou.Template_FEED_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_FEED_BROWSER) {
					respTemplateID = zuiyou.Template_FEED_BROWSER
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_OPEN_APP) {
					respTemplateID = zuiyou.Template_SPLASH_OPEN_APP
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_DEFAULT) {
					respTemplateID = zuiyou.Template_SPLASH_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_BROWSER) {
					respTemplateID = zuiyou.Template_SPLASH_BROWSER
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_OPEN_APP) {
					respTemplateID = zuiyou.Template_REVIEW_OPEN_APP
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_DEFAULT) {
					respTemplateID = zuiyou.Template_REVIEW_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_BROWSER) {
					respTemplateID = zuiyou.Template_REVIEW_BROWSER
				} else {
					fmt.Println("continue 0")
					continue
				}
			} else {
				if templateContains(templatesArray, zuiyou.Template_FEED_DEFAULT) {
					respTemplateID = zuiyou.Template_FEED_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_FEED_BROWSER) {
					respTemplateID = zuiyou.Template_FEED_BROWSER
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_DEFAULT) {
					respTemplateID = zuiyou.Template_SPLASH_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_BROWSER) {
					respTemplateID = zuiyou.Template_SPLASH_BROWSER
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_DEFAULT) {
					respTemplateID = zuiyou.Template_REVIEW_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_BROWSER) {
					respTemplateID = zuiyou.Template_REVIEW_BROWSER
				} else {
					fmt.Println("continue 1")
					continue
				}
			}
		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
				if templateContains(templatesArray, zuiyou.Template_FEED_OPEN_APP) && reqEnableFeedDpDownload == 1 {
					respTemplateID = zuiyou.Template_FEED_OPEN_APP
				} else if templateContains(templatesArray, zuiyou.Template_FEED_DOWNLOAD) {
					respTemplateID = zuiyou.Template_FEED_DOWNLOAD
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_OPEN_APP) {
					respTemplateID = zuiyou.Template_REVIEW_OPEN_APP
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_DOWNLOAD) {
					respTemplateID = zuiyou.Template_REVIEW_DOWNLOAD
				} else {
					fmt.Println("continue 2")
					continue
				}
			} else {
				if templateContains(templatesArray, zuiyou.Template_FEED_DOWNLOAD) {
					respTemplateID = zuiyou.Template_FEED_DOWNLOAD
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_DEFAULT) && reqOs == "ios" {
					respTemplateID = zuiyou.Template_SPLASH_DEFAULT
				} else if templateContains(templatesArray, zuiyou.Template_SPLASH_DOWNLOAD) && reqOs == "android" && reqEnableSplashDownload == 1 {
					respTemplateID = zuiyou.Template_SPLASH_DOWNLOAD
				} else if templateContains(templatesArray, zuiyou.Template_REVIEW_DOWNLOAD) {
					respTemplateID = zuiyou.Template_REVIEW_DOWNLOAD
				} else {
					fmt.Println("continue 3")
					continue
				}
			}
		}
		// fmt.Println(mhDataItem.InteractType)
		// fmt.Println(mhDataItem.DeepLink)
		// fmt.Println(respTemplateID)
		// fmt.Println(mhDataItem.Image)

		zuiyouRespAd.Template = respTemplateID

		// material
		if respTemplateID == zuiyou.Template_FEED_DOWNLOAD || respTemplateID == zuiyou.Template_REVIEW_DOWNLOAD {
			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name:       mhDataItem.Title,
				Desc:       mhDataItem.Description,
				IconUrl:    mhDataItem.IconURL,
				ButtonText: "立即下载",
			}

			if isVideoType == 0 {
				zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
					CoverUrl: mhDataItem.Image[0].URL,
					Width:    uint32(mhDataItem.Image[0].Width),
					Height:   uint32(mhDataItem.Image[0].Height),
				}
				zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg
			} else if isVideoType == 1 {
				zuiyouRespAdMaterialVideo := zuiyou.RtbResponse_Ad_Material_Video{
					CoverUrl: mhDataItem.Video.CoverURL,
					VideoUrl: mhDataItem.Video.VideoURL,
					Width:    uint32(mhDataItem.Video.Width),
					Height:   uint32(mhDataItem.Video.Height),
				}
				zuiyouRespAdMaterial.Video = &zuiyouRespAdMaterialVideo
			}
			if mhDataItem.InteractType == 1 {
				zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
					PkgName:     mhDataItem.PackageName,
					DownloadUrl: mhDataItem.DownloadURL,
				}
				if len(mhDataItem.Publisher) > 0 {
					zuiyouRespAdInterAction.DeveloperName = mhDataItem.Publisher
				}
				if len(mhDataItem.AppVersion) > 0 {
					zuiyouRespAdInterAction.AppVersion = mhDataItem.AppVersion
				}
				if len(mhDataItem.PrivacyLink) > 0 {
					zuiyouRespAdInterAction.PrivacyUrl = mhDataItem.PrivacyLink
				}
				if len(mhDataItem.AppInfoURL) > 0 {
					zuiyouRespAdInterAction.AppIntroUrl = mhDataItem.AppInfoURL
				}
				if len(mhDataItem.Permission) > 0 {
					zuiyouRespAdInterAction.PermissionUrl = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
				}
				if reqOs == "ios" {
					if strings.Contains(mhDataItem.DownloadURL, "apple.com") {
						tmpArray := strings.SplitN(mhDataItem.DownloadURL, "/id", 2)
						tmpArray = strings.SplitN(tmpArray[1], "?", 2)
						zuiyouRespAdInterAction.DownloadUrl = "https://apps.apple.com/cn/app/id" + tmpArray[0]
					}
				}
				zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			} else {
				continue
			}
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_FEED_DEFAULT || respTemplateID == zuiyou.Template_REVIEW_DEFAULT {
			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name:       mhDataItem.Title,
				Desc:       mhDataItem.Description,
				IconUrl:    mhDataItem.IconURL,
				ButtonText: "查看详情",
			}
			if isVideoType == 0 {
				zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
					CoverUrl: mhDataItem.Image[0].URL,
					Width:    uint32(mhDataItem.Image[0].Width),
					Height:   uint32(mhDataItem.Image[0].Height),
				}
				zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg
			} else if isVideoType == 1 {
				zuiyouRespAdMaterialVideo := zuiyou.RtbResponse_Ad_Material_Video{
					CoverUrl: mhDataItem.Video.CoverURL,
					VideoUrl: mhDataItem.Video.VideoURL,
					Width:    uint32(mhDataItem.Video.Width),
					Height:   uint32(mhDataItem.Video.Height),
				}
				zuiyouRespAdMaterial.Video = &zuiyouRespAdMaterialVideo
			}

			if mhDataItem.InteractType == 0 {
				zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
					LandingUrl: mhDataItem.LandpageURL,
				}
				zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			} else {
				continue
			}
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_FEED_BROWSER || respTemplateID == zuiyou.Template_REVIEW_BROWSER {
			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name:       mhDataItem.Title,
				Desc:       mhDataItem.Description,
				IconUrl:    mhDataItem.IconURL,
				ButtonText: "查看详情",
			}
			if isVideoType == 0 {
				zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
					CoverUrl: mhDataItem.Image[0].URL,
					Width:    uint32(mhDataItem.Image[0].Width),
					Height:   uint32(mhDataItem.Image[0].Height),
				}
				zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg
			} else if isVideoType == 1 {
				zuiyouRespAdMaterialVideo := zuiyou.RtbResponse_Ad_Material_Video{
					CoverUrl: mhDataItem.Video.CoverURL,
					VideoUrl: mhDataItem.Video.VideoURL,
					Width:    uint32(mhDataItem.Video.Width),
					Height:   uint32(mhDataItem.Video.Height),
				}
				zuiyouRespAdMaterial.Video = &zuiyouRespAdMaterialVideo
			}

			if mhDataItem.InteractType == 0 {
				zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
					BrowserUrl: mhDataItem.LandpageURL,
				}
				zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			} else {
				continue
			}
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_FEED_OPEN_APP || respTemplateID == zuiyou.Template_REVIEW_OPEN_APP {
			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name:    mhDataItem.Title,
				Desc:    mhDataItem.Description,
				IconUrl: mhDataItem.IconURL,
			}
			if mhDataItem.InteractType == 0 {
				zuiyouRespAdMaterial.ButtonText = "查看详情"
			} else if mhDataItem.InteractType == 1 {
				zuiyouRespAdMaterial.ButtonText = "立即下载"
			} else {
				continue
			}

			if isVideoType == 0 {
				zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
					CoverUrl: mhDataItem.Image[0].URL,
					Width:    uint32(mhDataItem.Image[0].Width),
					Height:   uint32(mhDataItem.Image[0].Height),
				}
				zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg
			} else if isVideoType == 1 {
				zuiyouRespAdMaterialVideo := zuiyou.RtbResponse_Ad_Material_Video{
					CoverUrl: mhDataItem.Video.CoverURL,
					VideoUrl: mhDataItem.Video.VideoURL,
					Width:    uint32(mhDataItem.Video.Width),
					Height:   uint32(mhDataItem.Video.Height),
				}
				zuiyouRespAdMaterial.Video = &zuiyouRespAdMaterialVideo
			}

			var deepLink string
			if mhDataItem.InteractType == 0 {
				if len(mhDataItem.DeepLink) > 0 {
					deepLink = mhDataItem.DeepLink
				} else {
					if len(mhDataItem.MarketURL) > 0 {
						deepLink = mhDataItem.MarketURL
					}
				}
				zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
					LandingUrl: mhDataItem.LandpageURL,
					InvokeUrl:  deepLink,
				}
				zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			} else if mhDataItem.InteractType == 1 {
				if len(mhDataItem.MarketURL) > 0 {
					deepLink = mhDataItem.MarketURL
				} else {
					if len(mhDataItem.DeepLink) > 0 {
						deepLink = mhDataItem.DeepLink
					}
				}

				zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
					PkgName:     mhDataItem.PackageName,
					DownloadUrl: mhDataItem.DownloadURL,
					InvokeUrl:   deepLink,
				}
				if len(mhDataItem.Publisher) > 0 {
					zuiyouRespAdInterAction.DeveloperName = mhDataItem.Publisher
				}
				if len(mhDataItem.AppVersion) > 0 {
					zuiyouRespAdInterAction.AppVersion = mhDataItem.AppVersion
				}
				if len(mhDataItem.PrivacyLink) > 0 {
					zuiyouRespAdInterAction.PrivacyUrl = mhDataItem.PrivacyLink
				}
				if len(mhDataItem.Permission) > 0 {
					zuiyouRespAdInterAction.PermissionUrl = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
				}
				if reqOs == "ios" {
					if strings.Contains(mhDataItem.DownloadURL, "apple.com") {
						tmpArray := strings.SplitN(mhDataItem.DownloadURL, "/id", 2)
						tmpArray = strings.SplitN(tmpArray[1], "?", 2)
						zuiyouRespAdInterAction.DownloadUrl = "https://apps.apple.com/cn/app/id" + tmpArray[0]
					}
				}
				zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			} else {
				continue
			}

			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_SPLASH_DEFAULT {
			if isVideoType == 1 {
				continue
			}

			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name: mhDataItem.Title,
			}

			zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
				CoverUrl: mhDataItem.Image[0].URL,
				Width:    uint32(mhDataItem.Image[0].Width),
				Height:   uint32(mhDataItem.Image[0].Height),
			}
			zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg

			zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
				LandingUrl: mhDataItem.LandpageURL,
			}
			zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_SPLASH_BROWSER {
			if isVideoType == 1 {
				continue
			}

			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name: mhDataItem.Title,
			}

			zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
				CoverUrl: mhDataItem.Image[0].URL,
				Width:    uint32(mhDataItem.Image[0].Width),
				Height:   uint32(mhDataItem.Image[0].Height),
			}
			zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg

			zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
				// BrowserUrl: mhDataItem.AdURL,
			}
			if len(mhDataItem.DownloadURL) > 0 {
				zuiyouRespAdInterAction.BrowserUrl = mhDataItem.DownloadURL
			} else if len(mhDataItem.LandpageURL) > 0 {
				zuiyouRespAdInterAction.BrowserUrl = mhDataItem.LandpageURL
			} else {
				continue
			}
			zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_SPLASH_OPEN_APP {
			if isVideoType == 1 {
				continue
			}

			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name: mhDataItem.Title,
			}

			if mhDataItem.Image == nil || len(mhDataItem.Image) == 0 {
				continue
			}

			zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
				CoverUrl: mhDataItem.Image[0].URL,
				Width:    uint32(mhDataItem.Image[0].Width),
				Height:   uint32(mhDataItem.Image[0].Height),
			}
			zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg

			var deepLink string
			if mhDataItem.InteractType == 0 {
				if len(mhDataItem.DeepLink) > 0 {
					deepLink = mhDataItem.DeepLink
				} else {
					if len(mhDataItem.MarketURL) > 0 {
						deepLink = mhDataItem.MarketURL
					}
				}
			} else if mhDataItem.InteractType == 1 {
				if len(mhDataItem.MarketURL) > 0 {
					deepLink = mhDataItem.MarketURL
				} else {
					if len(mhDataItem.DeepLink) > 0 {
						deepLink = mhDataItem.DeepLink
					}
				}
			}

			zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
				LandingUrl: mhDataItem.LandpageURL,
				InvokeUrl:  deepLink,
			}

			zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		} else if respTemplateID == zuiyou.Template_SPLASH_DOWNLOAD {
			if isVideoType == 1 {
				continue
			}

			zuiyouRespAdMaterial := zuiyou.RtbResponse_Ad_Material{
				Name: mhDataItem.Title,
				Desc: mhDataItem.Description,
			}

			zuiyouRespAdMaterialImg := zuiyou.RtbResponse_Ad_Material_Image{
				CoverUrl: mhDataItem.Image[0].URL,
				Width:    uint32(mhDataItem.Image[0].Width),
				Height:   uint32(mhDataItem.Image[0].Height),
			}
			zuiyouRespAdMaterial.Img = &zuiyouRespAdMaterialImg

			zuiyouRespAdInterAction := zuiyou.RtbResponse_Ad_Interaction{
				DownloadUrl: mhDataItem.DownloadURL,
			}
			if len(mhDataItem.Publisher) > 0 {
				zuiyouRespAdInterAction.DeveloperName = mhDataItem.Publisher
			}
			if len(mhDataItem.AppVersion) > 0 {
				zuiyouRespAdInterAction.AppVersion = mhDataItem.AppVersion
			}
			if len(mhDataItem.PrivacyLink) > 0 {
				zuiyouRespAdInterAction.PrivacyUrl = mhDataItem.PrivacyLink
			}
			if len(mhDataItem.Permission) > 0 {
				zuiyouRespAdInterAction.PermissionUrl = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
			}
			// 特殊处理
			if len(mhDataItem.PackageName) > 0 {
				zuiyouRespAdInterAction.PkgName = mhDataItem.PackageName
				if len(mhDataItem.Title) == 0 {
					defaultTitleArray := []string{"打开美好世界", "一起来玩耍", "大家都在用", "人气流行", "这些最好玩"}
					zuiyouRespAdMaterial.Name = defaultTitleArray[rand.Intn(len(defaultTitleArray))]
				}
			}
			if len(mhDataItem.IconURL) > 0 {
				zuiyouRespAdMaterial.IconUrl = mhDataItem.IconURL
			} else {
				zuiyouRespAdMaterial.IconUrl = "https://static.maplehaze.cn/static/logo.png"
			}
			zuiyouRespAd.Interaction = &zuiyouRespAdInterAction
			zuiyouRespAd.Material = &zuiyouRespAdMaterial
		}

		zuiyouResp.Ads = append(zuiyouResp.Ads, &zuiyouRespAd)
	}

	if len(zuiyouResp.Ads) == 0 {
		return NoBidReturn("no fill")
	}

	return zuiyouResp
}

func getZuiyouAppList(appList []string) []int {
	if len(appList) == 0 {
		return []int{}
	}
	var appListIdArray []int
	for _, appId := range appList {
		if v, ok := zuiyouAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var zuiyouAppListCodeMap = map[string]int{
	"pkg_code_0000_01": 1001,
	"pkg_code_0000_02": 1002,
	"pkg_code_0000_03": 1006,
	"pkg_code_0000_04": 1005}

func templateContains(array []zuiyou.Template, val zuiyou.Template) bool {
	for i := 0; i < len(array); i++ {
		if array[i] == val {
			return true
		}
	}
	return false
}

func mtTypeContains(array []zuiyou.MType, val zuiyou.MType) bool {
	for i := 0; i < len(array); i++ {
		if array[i] == val {
			return true
		}
	}
	return false
}

// NoBidReturn ...
func NoBidReturn(reason string) *zuiyou.RtbResponse {
	// fmt.Println(reason)

	zuiyouNoResp := &zuiyou.RtbResponse{
		SiteName:   "maplehaze",
		StatusCode: zuiyou.RtbResponse_DROP_OTHER,
	}
	return zuiyouNoResp
}
