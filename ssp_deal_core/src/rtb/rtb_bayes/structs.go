package rtb_bayes

type BayesRequestObject struct {
	Version string                   `json:"version"`
	Sid     string                   `json:"sid"`
	Adx     string                   `json:"adx"`
	Time    string                   `json:"time"`
	Token   string                   `json:"token"`
	Impsize int                      `json:"impsize"`
	Adspot  BayesRequestImpObject    `json:"adspot"`
	Media   BayesRequestAppObject    `json:"media"`
	Device  BayesRequestDeviceObject `json:"device"`
}

type BayesRequestDeviceObject struct {
	Sw           int                            `json:"sw"`
	Sh           int                            `json:"sh"`
	Ppi          int                            `json:"ppi"`
	Dpi          int                            `json:"dpi"`
	Os           int                            `json:"os"`
	Network      int                            `json:"network"`
	Operator     int                            `json:"operator"`
	Devicetype   int                            `json:"devicetype"`
	Density      float64                        `json:"density"`
	Ua           string                         `json:"ua"`
	IP           string                         `json:"ip"`
	Make         string                         `json:"make"`
	Model        string                         `json:"model"`
	Osv          string                         `json:"osv"`
	Imei         string                         `json:"imei"`
	ImeiMd5      string                         `json:"imei_md5"`
	Mac          string                         `json:"mac"`
	MacMd5       string                         `json:"mac_md5"`
	Androidid    string                         `json:"androidid"`
	AndroididMd5 string                         `json:"androidid_md5"`
	Oaid         string                         `json:"oaid"`
	OaidMd5      string                         `json:"oaid_md5"`
	Idfa         string                         `json:"idfa"`
	IdfaMd5      string                         `json:"idfa_md5"`
	Carrier      string                         `json:"carrier"`
	BootMark     string                         `json:"boot_mark"`
	UpdateMark   string                         `json:"update_mark"`
	HmsCode      string                         `json:"hms_code"`
	AgCode       string                         `json:"ag_code"`
	BootTime     string                         `json:"boot_time"`
	UpdateTime   string                         `json:"update_time"`
	BirthTime    string                         `json:"birth_time"`
	Caid         string                         `json:"caid"`
	CaidVersion  string                         `json:"caid_version"`
	CaidList     []BayesRequestDeviceCaidObject `json:"caid_list"`
}

type BayesRequestDeviceCaidObject struct {
	Caid        string `json:"caid"`
	CaidVersion string `json:"version"`
}
type BayesRequestAppObject struct {
	ID          string `json:"id"`
	Appver      string `json:"appver"`
	Name        string `json:"name"`
	Cname       string `json:"cname"`
	Bundle      string `json:"bundle"`
	SupportHTTP int    `json:"support_http"`
}

type BayesRequestImpObject struct {
	ID                  string `json:"id"`
	Bidfloor            int    `json:"bidfloor"`
	Adtype              int    `json:"adtype"`
	W                   int    `json:"w"`
	H                   int    `json:"h"`
	SupportCreativeType []int  `json:"support_creative_type"`
}

type BayesResponseObject struct {
	Code int                       `json:"code"`
	Sid  string                    `json:"sid"`
	Bid  []*BayesResponseBidObject `json:"bid,omitempty"`
}

type BayesResponseBidObject struct {
	Adtype       int                        `json:"adtype,omitempty"`
	CreativeType int                        `json:"creative_type,omitempty"`
	Price        int                        `json:"price,omitempty"`
	Action       int                        `json:"action,omitempty"`
	Duration     int                        `json:"duration,omitempty"`
	Width        float64                    `json:"width,omitempty"`
	Height       float64                    `json:"height,omitempty"`
	Impid        string                     `json:"impid,omitempty"`
	Nurl         string                     `json:"nurl,omitempty"`
	Link         string                     `json:"link,omitempty"`
	Adsource     string                     `json:"adsource,omitempty"`
	Vurl         string                     `json:"vurl,omitempty"`
	VideoImage   string                     `json:"video_image,omitempty"`
	Crid         string                     `json:"crid,omitempty"`
	Deeplink     string                     `json:"deeplink,omitempty"`
	PackageName  string                     `json:"package_name,omitempty"`
	Logo         string                     `json:"logo,omitempty"`
	Desc         string                     `json:"desc,omitempty"`
	Title        string                     `json:"title,omitempty"`
	Deeplinktk   []string                   `json:"deeplinktk,omitempty"`
	Starttk      []string                   `json:"starttk,omitempty"`
	Endtk        []string                   `json:"endtk,omitempty"`
	Imptk        []string                   `json:"imptk,omitempty"`
	Clicktk      []string                   `json:"clicktk,omitempty"`
	Image        []string                   `json:"image,omitempty"`
	DownloadApp  *BayesResponseBidAppObject `json:"download_app,omitempty"`
}

type BayesResponseBidAppObject struct {
	Size          int    `json:"size"`
	Name          string `json:"name"`
	Version       string `json:"appver"`
	Developer     string `json:"developer"`
	PermissionURL string `json:"permission_url"`
	PrivacyURL    string `json:"privacy_url"`
	DescUrl       string `json:"desc_url"`
	Bundle        string `json:"bundle"`
	Icon          string `json:"icon"`
}
