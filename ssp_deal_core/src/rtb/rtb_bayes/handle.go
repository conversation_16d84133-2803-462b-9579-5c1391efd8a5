package rtb_bayes

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"io"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleByBayes(c *gin.Context, channel string) (*BayesResponseObject, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()

	// 判断是否开启gzip
	acceptEncoding := c.GetHeader("Accept-Encoding")
	contentEncoding := c.GetHeader("Content-Encoding")

	if strings.Contains(acceptEncoding, "gzip") || strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	req := &BayesRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	var deviceOs string
	switch req.Device.Os {
	case 1:
		deviceOs = "ios"
	case 2:
		deviceOs = "android"
	default:
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	var connectType int
	switch req.Device.Network {
	case 1:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.Device.Operator {
	case 1:
		carrier = 1
	case 2:
		carrier = 2
	case 3:
		carrier = 3
	default:
		carrier = 0
	}

	adsCount := 1
	var resultfulImp []*BayesRequestImpObject
	var configList []models.RtbConfigByTagIDStu

	price := req.Adspot.Bidfloor
	tagId := req.Adspot.ID

	var styleIds []string
	for _, creativeType := range req.Adspot.SupportCreativeType {
		styleIds = append(styleIds, strconv.Itoa(creativeType))
	}

	var adxInfo *[]models.RtbConfigByTagIDStu
	adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, tagId, deviceOs, styleIds, "", price)
	if adxInfo == nil || len(*adxInfo) == 0 {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	if len(req.Media.Bundle) > 0 {
		for _, infoItem := range *adxInfo {
			if infoItem.PackageName == req.Media.Bundle {
				configList = append(configList, infoItem)
			}
		}
	}

	if len(configList) == 0 {
		for _, infoItem := range *adxInfo {
			if len(infoItem.PackageName) == 0 {
				configList = append(configList, infoItem)
			}
		}
	}

	resultfulImp = append(resultfulImp, &req.Adspot)

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Make
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.Media.Bundle,
			AppName:     req.Media.Name,
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			Manufacturer:    manufacturer,
			Model:           req.Device.Model,
			IP:              req.Device.IP,
			Ua:              req.Device.Ua,
			OsVersion:       req.Device.Osv,
			Mac:             req.Device.Mac,
			Oaid:            req.Device.Oaid,
			OaidMd5:         req.Device.OaidMd5,
			Idfa:            req.Device.Idfa,
			IdfaMd5:         req.Device.IdfaMd5,
			AndroidID:       req.Device.Androidid,
			AndroidIDMd5:    req.Device.AndroididMd5,
			ScreenWidth:     req.Device.Sw,
			ScreenHeight:    req.Device.Sh,
			HMSCoreVersion:  req.Device.HmsCode,
			BootMark:        req.Device.BootMark,
			UpdateMark:      req.Device.UpdateMark,
			DeviceStartSec:  req.Device.BootTime,
			SystemUpdateSec: req.Device.UpdateTime,
			DeviceBirthSec:  req.Device.BirthTime,
			DeviceType:      1,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if len(req.Device.CaidList) > 0 {
		var caidMultiList []models.MHReqCAIDMulti
		for _, caidItem := range req.Device.CaidList {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.Caid
			caidMulti.CAIDVersion = caidItem.CaidVersion
			caidMultiList = append(caidMultiList, caidMulti)
		}
	} else {
		if len(req.Device.Caid) > 0 && len(req.Device.CaidVersion) > 0 {
			var caidMultiList []models.MHReqCAIDMulti
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = req.Device.Caid
			caidMulti.CAIDVersion = req.Device.CaidVersion
			caidMultiList = append(caidMultiList, caidMulti)
			reqStu.Device.CAIDMulti = caidMultiList
		}
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	var impItem *BayesRequestImpObject
	for _, imp := range resultfulImp {
		if req.Adspot.ID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	var bidList []*BayesResponseBidObject
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__DSP_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		var clickLinkArray []string
		for _, clickUrl := range mhDataItem.ClickLink {
			clickUrl = strings.Replace(clickUrl, "__SLD__", "__INTACT_MODE__", -1)
			clickUrl = strings.Replace(clickUrl, "__X_MAX_ACC__", "__SHAKE_X_MAX_ACC__", -1)
			clickUrl = strings.Replace(clickUrl, "__Y_MAX_ACC__", "__SHAKE_Y_MAX_ACC__", -1)
			clickUrl = strings.Replace(clickUrl, "__Z_MAX_ACC__", "__SHAKE_Z_MAX_ACC__", -1)
			clickLinkArray = append(clickLinkArray, clickUrl)
		}

		bidItem := &BayesResponseBidObject{
			Adtype:      impItem.Adtype,
			Price:       ecpm,
			Impid:       bigdataUID,
			Nurl:        winURL,
			Link:        mhDataItem.LandpageURL,
			Adsource:    "MH",
			Crid:        mhDataItem.Crid,
			PackageName: mhDataItem.PackageName,
			Logo:        mhDataItem.IconURL,
			Desc:        mhDataItem.Description,
			Title:       mhDataItem.Title,
			Imptk:       mhDataItem.ImpressionLink,
			Clicktk:     clickLinkArray,
		}

		switch mhDataItem.InteractType {
		case 0:
			bidItem.Action = 1
			if len(mhDataItem.DeepLink) > 0 {
				bidItem.Deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					bidItem.Deeplink = mhDataItem.MarketURL
				}
			}
		case 1:
			bidItem.Action = 2
			if len(mhDataItem.MarketURL) > 0 {
				bidItem.Deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					bidItem.Deeplink = mhDataItem.DeepLink
				}
			}
			app := &BayesResponseBidAppObject{
				Size:          int(mhDataItem.PackageSize),
				Name:          mhDataItem.AppName,
				Bundle:        mhDataItem.PackageName,
				Version:       mhDataItem.AppVersion,
				Icon:          mhDataItem.IconURL,
				Developer:     mhDataItem.Publisher,
				PermissionURL: mhDataItem.PermissionURL,
				PrivacyURL:    mhDataItem.PrivacyLink,
				DescUrl:       mhDataItem.AppInfoURL,
			}
			bidItem.DownloadApp = app
		}

		if len(bidItem.Deeplink) > 0 {
			if len(mhDataItem.ConvTracks) > 0 {
				for _, convTracks := range mhDataItem.ConvTracks {
					if convTracks.ConvType == 10 {
						bidItem.Deeplinktk = convTracks.ConvURLS
					}
				}
			}
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image == nil || len(reqRtbConfig.ImageStyleID) == 0 {
				continue
			}

			var imgList []string
			for _, imageItem := range mhDataItem.Image {
				bidItem.Width = float64(imageItem.Width)
				bidItem.Height = float64(imageItem.Height)
				imgList = append(imgList, imageItem.URL)
			}
			bidItem.Image = imgList

			imageStyleID, _ := strconv.Atoi(reqRtbConfig.ImageStyleID)
			bidItem.CreativeType = imageStyleID
		case 20:
			if mhDataItem.Video == nil || len(reqRtbConfig.VideoStyleID) == 0 {
				continue
			}
			bidItem.VideoImage = mhDataItem.Video.CoverURL
			bidItem.Vurl = mhDataItem.Video.VideoURL
			bidItem.Width = float64(mhDataItem.Video.Width)
			bidItem.Height = float64(mhDataItem.Video.Height)
			bidItem.Duration = mhDataItem.Video.Duration / 1000

			if mhDataItem.Video.EventTracks != nil && len(mhDataItem.Video.EventTracks) > 0 {
				for _, eventTracks := range mhDataItem.Video.EventTracks {
					if eventTracks.EventType == 100 {
						bidItem.Starttk = eventTracks.EventURLS
					}
					if eventTracks.EventType == 103 {
						bidItem.Endtk = eventTracks.EventURLS
					}
				}
			}

			videoStyleID, _ := strconv.Atoi(reqRtbConfig.VideoStyleID)
			bidItem.CreativeType = videoStyleID
		}

		bidList = append(bidList, bidItem)
	}

	if len(bidList) == 0 {
		return &BayesResponseObject{
			Code: http.StatusNoContent,
			Sid:  req.Sid,
		}, http.StatusOK
	}

	resp := &BayesResponseObject{
		Code: http.StatusOK,
		Sid:  req.Sid,
		Bid:  bidList,
	}

	jsonByte, _ := json.Marshal(resp)
	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"bayes_resp", string(jsonByte), "10807", "60180", "fl_ios_xs", "1870100034")

	return resp, http.StatusOK
}
