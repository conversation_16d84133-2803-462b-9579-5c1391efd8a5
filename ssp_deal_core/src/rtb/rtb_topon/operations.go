package rtb_topon

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"
	"mh_proxy/vast"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *ToponPipline) Init(c *gin.Context, channel string) *ToponPipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()
	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject ToponRequestObject
	err = json.Unmarshal(bodyContent, &requestObject)

	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()
	p.Context = c
	p.Channel = channel
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCarrier()
}

// CheckImp 检查Imp对象
func (p *ToponPipline) CheckImp() *ToponPipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *ToponPipline) SetupOsString() *ToponPipline {
	switch strings.ToLower(p.Request.Device.Os) {
	case "ios":
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	case "android":
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	default:
		// 错误信息
		return p.SetErrorString("invalid os")
	}

	return p
}

// CheckUA 检查UA
func (p *ToponPipline) CheckUA() *ToponPipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *ToponPipline) SetupManufacturer() *ToponPipline {
	// 每次需要判断状态
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Make
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *ToponPipline) SetupConnectType() *ToponPipline {
	switch p.Request.Device.Connectiontype {
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	case 4:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_2G
	case 5:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_3G
	case 6:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}

	return p
}

// SetupCarrier 设置运营商
func (p *ToponPipline) SetupCarrier() *ToponPipline {
	switch p.Request.Device.Carrier {
	case "46000", "46002", "46007":
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case "46001", "46006":
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case "46003", "46011":
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}

	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *ToponPipline) RequestRtbConfig() *ToponPipline {
	var resultImp []*ToponRequestImpObject
	var configList []models.RtbConfigByTagIDStu

	for _, imp := range p.Request.Imp {
		tagId := imp.Tagid
		price := 0

		if imp.Bidfloorcur != "CNY" {
			continue
		}

		if imp.Bidfloor > 0 {
			price = int(imp.Bidfloor)
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagID(p.Context, p.Channel, tagId, p.DeviceOs.String(), "", "", price)

		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(p.Request.App.Bundle) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == p.Request.App.Bundle {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultImp = append(resultImp, imp)
	}

	// 判断是否有效填充
	if len(configList) == 0 || len(resultImp) == 0 {
		return p.SetErrorString("resultImp or configList imp is empty")
	}

	p.ResultImp = resultImp
	p.ConfigList = configList

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(p.Context, item.TagID, item.OriginPrice)
		}
	}

	return p
}

// RequestAdxAd 请求广告
func (p *ToponPipline) RequestAdxAd() *ToponPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	var extCommon ToponRequestDeviceExtCommonObject
	_ = json.Unmarshal([]byte(p.Request.Device.Ext.Common), &extCommon)

	var caidMultiList []models.MHReqCAIDMulti
	if len(extCommon.CaidList) > 0 && p.DeviceOs.String() == "ios" {
		for _, caidItem := range extCommon.CaidList {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.Caid
			caidMulti.CAIDVersion = caidItem.Version
			caidMultiList = append(caidMultiList, caidMulti)
		}
	}

	requestConfig := p.ConfigList[0]
	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: p.Request.App.Bundle,
			AppName:     p.Request.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              p.DeviceOs.String(),
			OsVersion:       p.Request.Device.Osv,
			Model:           p.Request.Device.Model,
			Manufacturer:    p.Manufacturer,
			Ua:              p.Request.Device.Ua,
			DeviceType:      1,
			IP:              p.Request.Device.Ip,
			Language:        p.Request.Device.Language,
			ScreenWidth:     p.Request.Device.W,
			ScreenHeight:    p.Request.Device.H,
			SystemUpdateSec: extCommon.SysUpdateTime,
			DeviceStartSec:  extCommon.SysBootTime,
			DeviceBirthSec:  extCommon.BirthTime,
			BootMark:        extCommon.BootMark,
			UpdateMark:      extCommon.UpdateMark,
			AppStoreVersion: extCommon.FirmVersion,
			AppList:         getTononAppList(extCommon.AppList),
			CAIDMulti:       caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		adxRequest.Device.Idfa = p.Request.Device.Ifa
	} else {
		adxRequest.Device.Oaid = extCommon.Oaid
		adxRequest.Device.Imei = extCommon.Did
		adxRequest.Device.ImeiMd5 = p.Request.Device.Didmd5
		adxRequest.Device.AndroidID = extCommon.Dpid
		adxRequest.Device.AndroidIDMd5 = p.Request.Device.Dpidmd5
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func (p *ToponPipline) SetupResponse() *ToponPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	var (
		responseSeatbidObject     ToponResponseSeatbidObject
		responseBidObjectList     []*ToponResponseBidObject
		responseSeatbidObjectList []*ToponResponseSeatbidObject
	)
	var impItem *ToponRequestImpObject
	for _, imp := range p.ResultImp {
		if imp.Tagid == requestConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return p.SetErrorString("no fill")
	}

	for _, adxAd := range p.AdxAdResponse.Data[requestConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if adxAd.Ecpm < requestConfig.Price {
			continue
		}
		ecpm := adxAd.Ecpm

		winNotice := config.ExternalRtbPriceURL +
			"?uid=" + p.UUID +
			"&tagid=" + requestConfig.TagID +
			"&bp=" + utils.ConvertIntToString(ecpm) +
			"&channel=" + p.Channel +
			"&price=${AUCTION_PRICE}" +
			"&log=" + url.QueryEscape(adxAd.Log)

		responseBidObject := ToponResponseBidObject{
			Id:    uuid.NewV4().String(),
			Impid: impItem.Id,
			Nurl:  winNotice,
			Adid:  adxAd.AdID,
			Cid:   adxAd.AdID,
			Crid:  adxAd.Crid,
			Price: float32(ecpm),
		}

		switch adxAd.CrtType {
		case 11:
			if len(impItem.Native.Request) == 0 || adxAd.Image == nil {
				continue
			}

			var adURL string
			var assetsList []*ToponResponseAssetsObject
			var responseLinkObject ToponResponseLinkObject
			if len(adxAd.DeepLink) > 0 {
				responseLinkObject.Fallback = adxAd.DeepLink
			}
			if len(adxAd.ClickLink) > 0 {
				responseLinkObject.Clicktrackers = adxAd.ClickLink
			}
			if adxAd.InteractType == 1 {
				adURL = adxAd.DownloadURL
			} else {
				if len(adxAd.LandpageURL) > 0 {
					adURL = adxAd.LandpageURL
				} else {
					adURL = adxAd.AdURL
				}
			}
			responseLinkObject.Url = adURL

			var nativeBidObject ToponRequestNativeBidObject
			_ = json.Unmarshal([]byte(impItem.Native.Request), &nativeBidObject)

			for _, assetItem := range nativeBidObject.Native.Assets {
				if assetItem.Title != nil {
					var assetsTitle ToponResponseAssetsObject
					assetsTitle.Id = assetItem.Id
					assetsTitle.Title = &ToponResponseTitleObject{
						Text: adxAd.Title,
					}
					assetsList = append(assetsList, &assetsTitle)
				}

				if assetItem.Data != nil {
					if assetItem.Data.Type == 2 && len(adxAd.Description) > 0 {
						var assetsData ToponResponseAssetsObject
						assetsData.Id = assetItem.Id
						assetsData.Data = &ToponResponseDataObject{
							Label: "description",
							Value: adxAd.Description,
						}
						assetsList = append(assetsList, &assetsData)
					}
				}

				if assetItem.Img != nil {
					switch assetItem.Img.Type {
					case 1:
						if len(adxAd.IconURL) > 0 {
							var assetsIcon ToponResponseAssetsObject
							assetsIcon.Id = assetItem.Id
							assetsIcon.Img = &ToponResponseImgObject{
								W:    512,
								H:    512,
								Type: 1,
								Url:  adxAd.IconURL,
							}
							assetsList = append(assetsList, &assetsIcon)
						}
					case 3:
						var assetsImg ToponResponseAssetsObject
						assetsImg.Id = assetItem.Id
						assetsImg.Img = &ToponResponseImgObject{
							W:    adxAd.Image[0].Width,
							H:    adxAd.Image[0].Height,
							Url:  adxAd.Image[0].URL,
							Type: 3,
						}
						assetsList = append(assetsList, &assetsImg)
					}
				}
			}

			responseNativeObject := &ToponResponseNativeObject{
				Link:        &responseLinkObject,
				Assets:      assetsList,
				Imptrackers: adxAd.ImpressionLink,
			}
			responseBidAdmObject := ToponResponseAdmObject{
				Native: responseNativeObject,
			}
			addStr, _ := json.Marshal(responseBidAdmObject)
			responseBidObject.Adm = string(addStr)
		case 20:
			if impItem.Video == nil || adxAd.Video == nil {
				continue
			}
			vastStu := vast.VAST{
				Version: "3.0",
			}

			var vastImpressionTrackArray []vast.Impression
			for tmpIndex, impItem := range adxAd.ImpressionLink {
				tmpImpression := vast.Impression{}
				tmpImpression.ID = utils.ConvertIntToString(tmpIndex)
				tmpImpression.URI = impItem
				vastImpressionTrackArray = append(vastImpressionTrackArray, tmpImpression)
			}
			var vastClickTrackArray []vast.VideoClick
			for tmpIndex, clkItem := range adxAd.ClickLink {
				tmpClickTrack := vast.VideoClick{}
				tmpClickTrack.ID = utils.ConvertIntToString(tmpIndex)
				tmpClickTrack.URI = clkItem
				vastClickTrackArray = append(vastClickTrackArray, tmpClickTrack)
			}
			var vastVideoTrackArray []vast.Tracking
			for _, trackItem := range adxAd.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						tmpTrack := vast.Tracking{}
						tmpTrack.Event = vast.Event_type_start
						tmpTrack.URI = trackEventItem
						vastVideoTrackArray = append(vastVideoTrackArray, tmpTrack)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						tmpTrack := vast.Tracking{}
						tmpTrack.Event = vast.Event_type_complete
						tmpTrack.URI = trackEventItem
						vastVideoTrackArray = append(vastVideoTrackArray, tmpTrack)
					}
				}
			}
			// material
			var vastMediaFileArray []vast.MediaFile
			duration := adxAd.Video.Duration / 1000
			tmpMediaFile := vast.MediaFile{
				Delivery: "progressive",
				Type:     "video/mp4",
				Width:    adxAd.Video.Width,
				Height:   adxAd.Video.Height,
				URI:      adxAd.Video.VideoURL,
			}
			vastMediaFileArray = append(vastMediaFileArray, tmpMediaFile)
			vastAd := vast.Ad{
				ID: "1",
				InLine: &vast.InLine{
					AdSystem:    &vast.AdSystem{Name: "Maplehaze"},
					AdTitle:     vast.CDATAString{CDATA: adxAd.Title},
					Description: &vast.CDATAString{CDATA: adxAd.Description},
					Impressions: vastImpressionTrackArray,
					Creatives: []vast.Creative{
						{
							ID:       adxAd.Crid,
							Sequence: 0,
							Linear: &vast.Linear{
								Duration: vast.Duration(duration),
								VideoClicks: &vast.VideoClicks{
									//ClickThroughs:  vastClickThroughArray,
									ClickTrackings: vastClickTrackArray,
								},
								TrackingEvents: vastVideoTrackArray,
								MediaFiles:     vastMediaFileArray,
								Icons: &vast.Icons{
									Icon: []vast.Icon{
										{
											StaticResource: &vast.StaticResource{
												URI: adxAd.IconURL,
											},
										},
									},
								},
							},
						},
					},
				},
			}
			vastStu.Ads = append(vastStu.Ads, vastAd)
			vastXMLText, _ := xml.Marshal(vastStu)
			responseBidObject.Adm = string(vastXMLText)
		}

		var extObject ToponResponseExtObject
		var extCommonCnObject ToponResponseExtCommonCnObject
		if len(adxAd.DeepLink) > 0 || len(adxAd.MarketURL) > 0 {
			var convUrls []string
			if len(adxAd.ConvTracks) > 0 {
				for _, convTracks := range adxAd.ConvTracks {
					if convTracks.ConvType == 10 {
						convUrls = convTracks.ConvURLS
					}
				}
			}
			extCommonCnObject.DpSucc = convUrls
		}

		if adxAd.InteractType == 1 {
			if len(adxAd.MarketURL) > 0 {
				extCommonCnObject.Deeplink = adxAd.MarketURL
			} else {
				if len(adxAd.DeepLink) > 0 {
					extCommonCnObject.Deeplink = adxAd.DeepLink
				}
			}

			extCommonCnObject.LinkType = 4
			extCommonCnObject.AppName = adxAd.AppName
			extCommonCnObject.AppDeveloper = adxAd.Publisher
			extCommonCnObject.AppPrivacy = adxAd.PrivacyLink
			extCommonCnObject.AppDesc = adxAd.AppInfo
			extCommonCnObject.AppVersion = adxAd.AppVersion
			extCommonCnObject.DownloadLink = adxAd.DownloadURL
			if len(adxAd.Permission) > 0 {
				extCommonCnObject.AppPermissions = adxAd.Permission
			} else {
				extCommonCnObject.AppPermissions = adxAd.PermissionURL
			}
		} else {
			if len(adxAd.DeepLink) > 0 {
				extCommonCnObject.Deeplink = adxAd.DeepLink
			} else {
				if len(adxAd.MarketURL) > 0 {
					extCommonCnObject.Deeplink = adxAd.MarketURL
				}
			}
		}

		extObject.CommonCn = &extCommonCnObject
		responseBidObject.Ext = &extObject

		responseBidObjectList = append(responseBidObjectList, &responseBidObject)
	}
	if len(responseBidObjectList) > 0 {
		responseSeatbidObject.Bid = responseBidObjectList
	}
	responseSeatbidObjectList = append(responseSeatbidObjectList, &responseSeatbidObject)

	// 构造返回结果
	nbr := 200
	if len(responseBidObjectList) == 0 {
		nbr = 204
	}

	responseObject := ToponResponseObject{
		Id:      p.Request.Id,
		BidId:   uuid.NewV4().String(),
		Cur:     "CNY",
		Nbr:     nbr,
		Seatbid: responseSeatbidObjectList,
	}

	status := http.StatusOK
	if len(responseBidObjectList) == 0 {
		status = http.StatusNoContent
	}

	p.Response = &responseObject
	p.ResponseStatus = status

	return p
}

// ResponseResult 返回结果
func (p *ToponPipline) ResponseResult() (*ToponResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &ToponResponseObject{
			Nbr:     204,
			BidId:   uuid.NewV4().String(),
			Id:      p.Request.Id,
			Cur:     "CNY",
			Seatbid: nil,
		}, http.StatusNoContent
	}
	return p.Response, http.StatusOK
}

func getTononAppList(appIDList []string) []int {
	if len(appIDList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := toppnAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var toppnAppListCodeMap = map[string]int{
	"1019": 1001,
	"1018": 1002,
	"1017": 1003,
	"1016": 1004,
	"1015": 1005,
	"1014": 1006,
	"1013": 1008,
	"1012": 1009,
	"1011": 1010,
	"1010": 1011,
	"1009": 1013,
	"1008": 1014,
	"1007": 1015,
	"1006": 1016,
	"1005": 1017,
	"1004": 1018,
	"1003": 1019,
	"1002": 1020,
	"1001": 1021}

// SetErrorString 设置字符串类型的错误信息
func (p *ToponPipline) SetErrorString(err string) *ToponPipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *ToponPipline) SetError(err error) *ToponPipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}

func (p *ToponPipline) Print() *ToponPipline {
	fmt.Println("<<<<<<<<<<<<<<<< DEBUG <<<<<<<<<<<<<<<")
	fmt.Printf("%+v\n", p)
	fmt.Println(">>>>>>>>>>>>>>>> DEBUG >>>>>>>>>>>>>>>")
	return p
}
