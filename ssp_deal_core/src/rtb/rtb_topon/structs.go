package rtb_topon

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// ToponRequestObject Objects
type ToponRequestObject struct {
	Id     string                    `json:"id"`
	Device *ToponRequestDeviceObject `json:"device"`
	App    *ToponRequestAppObject    `json:"app"`
	Imp    []*ToponRequestImpObject  `json:"imp"`
}

type ToponRequestDeviceObject struct {
	Ua             string                       `json:"ua"`
	Ip             string                       `json:"ip"`
	Ipv6           string                       `json:"ipv6"`
	Make           string                       `json:"make"`
	Model          string                       `json:"model"`
	Os             string                       `json:"os"`
	Osv            string                       `json:"osv"`
	Hwv            string                       `json:"hwv"`
	Language       string                       `json:"language"`
	Carrier        string                       `json:"carrier"`
	Ifa            string                       `json:"ifa"`
	Didmd5         string                       `json:"didmd5"`
	Dpidmd5        string                       `json:"dpidmd5"`
	Macidmd5       string                       `json:"macidmd5"`
	Devicetype     int                          `json:"devicetype"`
	W              int                          `json:"w"`
	H              int                          `json:"h"`
	Ppi            int                          `json:"ppi"`
	Connectiontype int                          `json:"connectiontype"`
	Ext            *ToponRequestDeviceExtObject `json:"ext"`
}

type ToponRequestAppObject struct {
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
}

type ToponRequestImpObject struct {
	Id          string                       `json:"id"`
	Tagid       string                       `json:"tagid"`
	Bidfloorcur string                       `json:"bidfloorcur"`
	Bidfloor    float32                      `json:"bidfloor"`
	Banner      *ToponRequestImpBannerObject `json:"banner"`
	Video       *ToponRequestImpVideoObject  `json:"video"`
	Native      *ToponRequestImpNativeObject `json:"native"`
}

type ToponRequestImpBannerObject struct {
	W     int   `json:"w"`
	H     int   `json:"h"`
	Btype []int `json:"btype"`
	Battr []int `json:"battr"`
}

type ToponRequestImpNativeObject struct {
	Request string `json:"request"`
}

type ToponRequestNativeBidObject struct {
	Native *ToponRequestNativeRequestObject `json:"native"`
}

type ToponRequestNativeRequestObject struct {
	Assets []*ToponRequestNativeAssetsObject `json:"assets"`
}

type ToponRequestNativeAssetsObject struct {
	Id       int                                         `json:"id"`
	Required int                                         `json:"required"`
	Title    *ToponRequestnativeRequestAssetsTitleObject `json:"title"`
	Img      *ToponRequestnativeRequestAssetsImgObject   `json:"img"`
	Data     *ToponRequestnativeRequestAssetsDataObject  `json:"data"`
}

type ToponRequestnativeRequestAssetsTitleObject struct {
	Len int `json:"len"`
}
type ToponRequestnativeRequestAssetsImgObject struct {
	Type int `json:"type"`
}
type ToponRequestnativeRequestAssetsDataObject struct {
	Type int `json:"type"`
}

type ToponRequestImpVideoObject struct {
	W     int   `json:"w"`
	H     int   `json:"h"`
	Battr []int `json:"battr"`
}

type ToponRequestDeviceExtObject struct {
	Common string `json:"common"`
}

type ToponRequestDeviceExtCommonObject struct {
	SysBootTime   string                                      `json:"sys_boot_time"`
	SysUpdateTime string                                      `json:"sys_update_time"`
	BirthTime     string                                      `json:"birth_time"`
	BootMark      string                                      `json:"boot_mark"`
	UpdateMark    string                                      `json:"update_mark"`
	Oaid          string                                      `json:"oaid"`
	Did           string                                      `json:"did"`
	Dpid          string                                      `json:"dpid"`
	FirmVersion   string                                      `json:"firm_version"`
	AppList       []string                                    `json:"app_list"`
	CaidList      []ToponRequestDeviceExtCommonCaidListObject `json:"caid_list"`
}

type ToponRequestDeviceExtCommonCaidListObject struct {
	Version string `json:"version"`
	Caid    string `json:"caid"`
}

// ToponResponseObject Objects
type ToponResponseObject struct {
	Id      string                        `json:"id"`
	BidId   string                        `json:"bidid"`
	Cur     string                        `json:"cur"`
	Nbr     int                           `json:"nbr"`
	Seatbid []*ToponResponseSeatbidObject `json:"seatbid"`
}

type ToponResponseSeatbidObject struct {
	Bid []*ToponResponseBidObject `json:"bid"`
}

type ToponResponseBidObject struct {
	Id    string                  `json:"id"`
	Impid string                  `json:"impid"`
	Nurl  string                  `json:"nurl"`
	Adid  string                  `json:"adid"`
	Cid   string                  `json:"cid"`
	Crid  string                  `json:"crid"`
	Adm   string                  `json:"adm"`
	Price float32                 `json:"price"`
	Ext   *ToponResponseExtObject `json:"ext,omitempty"`
}

type ToponResponseAdmObject struct {
	Native *ToponResponseNativeObject `json:"native"`
}

type ToponResponseNativeObject struct {
	Link        *ToponResponseLinkObject     `json:"link"`
	Assets      []*ToponResponseAssetsObject `json:"assets"`
	Imptrackers []string                     `json:"imptrackers"`
}

type ToponResponseAssetsObject struct {
	Id    int                       `json:"id"`
	Title *ToponResponseTitleObject `json:"title,omitempty"`
	Img   *ToponResponseImgObject   `json:"img,omitempty"`
	Video *ToponResponseVideoObject `json:"video,omitempty"`
	Data  *ToponResponseDataObject  `json:"data,omitempty"`
}

type ToponResponseExtObject struct {
	CommonCn *ToponResponseExtCommonCnObject `json:"common_cn"`
}
type ToponResponseExtCommonCnObject struct {
	LinkType       int      `json:"link_type,omitempty"`
	Deeplink       string   `json:"deeplink,omitempty"`
	AppDeveloper   string   `json:"app_developer,omitempty"`
	AppPrivacy     string   `json:"app_privacy,omitempty"`
	AppPermissions string   `json:"app_permissions,omitempty"`
	AppName        string   `json:"app_name,omitempty"`
	AppDesc        string   `json:"app_desc,omitempty"`
	AppVersion     string   `json:"app_version,omitempty"`
	DownloadLink   string   `json:"download_link,omitempty"`
	DpSucc         []string `json:"dp_succ,omitempty"`
}

type ToponResponseDataObject struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type ToponResponseTitleObject struct {
	Text string `json:"text"`
}

type ToponResponseImgObject struct {
	W    int    `json:"w"`
	H    int    `json:"h"`
	Type int    `json:"type"`
	Url  string `json:"url"`
}

type ToponResponseVideoObject struct {
	Duration int    `json:"Duration"`
	Size     int    `json:"Size"`
	Url      string `json:"url"`
}

type ToponResponseLinkObject struct {
	Url           string   `json:"url"`
	Fallback      string   `json:"fallback"`
	Clicktrackers []string `json:"clicktrackers"`
}

type ToponPipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	Request  *ToponRequestObject
	Response *ToponResponseObject

	ConfigList    []models.RtbConfigByTagIDStu
	ResultImp     []*ToponRequestImpObject
	AdxAdResponse *models.MHResp
}
