package rtb_yoyo

type YoYoRequestObject struct {
	Id             string                   `json:"id"`
	InstallAppList []string                 `json:"installAppList"`
	Device         *YoYoRequestDeviceObject `json:"device"`
	App            *YoYoRequestAppObject    `json:"app"`
	Imp            []*YoYoRequestImpObject  `json:"imp"`
}

type YoYoRequestDeviceObject struct {
	DeviceType      int                          `json:"deviceType"`
	Height          int                          `json:"h"`
	Width           int                          `json:"w"`
	Carrier         int                          `json:"carrier"`
	Network         int                          `json:"connectionType"`
	Ua              string                       `json:"ua"`
	Ip              string                       `json:"ip"`
	Make            string                       `json:"make"`
	Model           string                       `json:"model"`
	MobileModel     string                       `json:"mobileModel"`
	Os              string                       `json:"os"`
	OsVersion       string                       `json:"osv"`
	Hwv             string                       `json:"hwv"`
	Language        string                       `json:"language"`
	Imei            string                       `json:"did"`
	ImeiMd5         string                       `json:"didMd5"`
	Oaid            string                       `json:"oaId"`
	OaidMd5         string                       `json:"oaIdMd5"`
	Idfa            string                       `json:"idfa"`
	IdfaMd5         string                       `json:"idfaMd5"`
	Mac             string                       `json:"mac"`
	MacMd5          string                       `json:"macMd5"`
	AppStoreVersion string                       `json:"appStoreVersion"`
	HmsCoreVersion  string                       `json:"hmsCoreVersion"`
	CaidList        *YoYoRequestDeviceCaidObject `json:"caidList"`
}

type YoYoRequestDeviceCaidObject struct {
	Caid        string `json:"caid"`
	CaidVersion string `json:"version"`
}

type YoYoRequestAppObject struct {
	Name    string `json:"name"`
	Bundle  string `json:"bundle"`
	Version string `json:"version"`
}

type YoYoRequestImpObject struct {
	AdsCount      int                                `json:"adsCount"`
	Id            string                             `json:"id"`
	TagId         string                             `json:"tagId"`
	SearchKeyword string                             `json:"searchKeyword"`
	PkgWhitelist  []string                           `json:"pkgWhitelist"`
	TemplateList  []*YoYoRequestImpTemplateObject    `json:"templateList"`
	BidInfoList   []*YoYoRequestImpBidInfoListObject `json:"bidInfoList"`
}

type YoYoRequestImpTemplateObject struct {
	TemplateId    string `json:"yoyoTemplateId"`
	TargetTypeStr string `json:"targetTypeStr"`
}

type YoYoRequestImpBidInfoListObject struct {
	BillingType int     `json:"billingType"`
	BidFloor    float64 `json:"bidFloor"`
}

type YoYoResponseObject struct {
	Id      string                       `json:"id"`
	BidList []*YoYoResponseBidListObject `json:"bidList,omitempty"`
}

type YoYoResponseBidListObject struct {
	Price      int                             `json:"price,omitempty"`
	TargetType int                             `json:"targetType,omitempty"`
	Id         string                          `json:"id,omitempty"`
	ImpId      string                          `json:"impId,omitempty"`
	TagId      string                          `json:"tagId,omitempty"`
	TemplateId string                          `json:"templateId,omitempty"`
	Track      *YoYoResponseBidListTrackObject `json:"track,omitempty"`
	Items      *YoYoResponseBidItemObject      `json:"bidAdContent,omitempty"`
}

type YoYoResponseBidItemObject struct {
	Duration          int      `json:"duration,omitempty"`
	VideoFileSize     int      `json:"videoFileSize,omitempty"`
	AppVerCode        int      `json:"appVerCode,omitempty"`
	AppFileSize       int      `json:"appFileSize,omitempty"`
	Title             string   `json:"title,omitempty"`
	Desc              string   `json:"description,omitempty"`
	Source            string   `json:"source,omitempty"`
	Icon              string   `json:"icon,omitempty"`
	Image             string   `json:"image,omitempty"`
	Video             string   `json:"video,omitempty"`
	LandingUrl        string   `json:"landingUrl,omitempty"`
	ActionUrl         string   `json:"actionUrl,omitempty"`
	AppIcon           string   `json:"appIcon,omitempty"`
	AppName           string   `json:"appName,omitempty"`
	PkgName           string   `json:"pkgName,omitempty"`
	AppVerName        string   `json:"appVerName,omitempty"`
	AppPrivacy        string   `json:"appPrivacy,omitempty"`
	AppPrivacyUrl     string   `json:"appPrivacyUrl,omitempty"`
	AppPermission     string   `json:"appPermission,omitempty"`
	AppPermissionUrl  string   `json:"appPermissionUrl,omitempty"`
	AppDeveloper      string   `json:"appDeveloper,omitempty"`
	AppDescription    string   `json:"appDescription,omitempty"`
	AppDescriptionUrl string   `json:"appDescriptionUrl,omitempty"`
	Deeplink          string   `json:"deeplink,omitempty"`
	VideoCover        string   `json:"videoCover,omitempty"`
	UniversalLink     string   `json:"universalLink,omitempty"`
	ImgUrlList        []string `json:"imgUrlList,omitempty"`
}

type YoYoResponseBidListTrackObject struct {
	WinUrl string   `json:"winUrl,omitempty"`
	Imp    []string `json:"imp,omitempty"`
	Clk    []string `json:"clk,omitempty"`
}
