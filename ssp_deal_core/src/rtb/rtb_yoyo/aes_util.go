package rtb_yoyo

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
)

const (
	INITIALIZATION_VECTOR_SIZE = 16
	SIGNATURE_SIZE             = 4
	BLOCK_SIZE                 = 20
)

func Decrypt(priceEncodeStr string, encryptionKey, integrityKey []byte) string {
	ciphertext, err := base64.RawURLEncoding.DecodeString(priceEncodeStr)
	if err != nil {
		return ""
	}

	plaintextLength := len(ciphertext) - INITIALIZATION_VECTOR_SIZE - SIGNATURE_SIZE
	if plaintextLength < 0 {
		return ""
	}

	iv := ciphertext[:INITIALIZATION_VECTOR_SIZE]
	ekHmac := hmac.New(sha1.New, encryptionKey)
	var plaintext = make([]byte, plaintextLength)
	ciphertextEnd := INITIALIZATION_VECTOR_SIZE + plaintextLength
	addIVCounterByte := true
	for ciphertextBegin, plaintextBegin := INITIALIZATION_VECTOR_SIZE, 0; ciphertextBegin < ciphertextEnd; {
		ekHmac.Reset()
		ekHmac.Write(iv)
		pad := ekHmac.Sum(nil)
		for i := 0; i < BLOCK_SIZE && ciphertextBegin != ciphertextEnd; i++ {
			plaintext[plaintextBegin] = ciphertext[ciphertextBegin] ^ pad[i]
			ciphertextBegin++
			plaintextBegin++
		}
		if !addIVCounterByte {
			index := len(iv) - 1
			addIVCounterByte = incrementByte(&iv[index])
		}
		if addIVCounterByte {
			addIVCounterByte = false
			iv = append(iv, 0)
		}
	}

	ikHmac := hmac.New(sha1.New, integrityKey)
	ikHmac.Write(plaintext)
	ikHmac.Write(ciphertext[:INITIALIZATION_VECTOR_SIZE])
	computedSignature := ikHmac.Sum(nil)[:SIGNATURE_SIZE]
	signature := ciphertext[ciphertextEnd : ciphertextEnd+SIGNATURE_SIZE]
	if !bytes.Equal(signature, computedSignature) {
		return ""
	}

	plaintext = bytes.TrimSpace(plaintext)
	return string(removeNullBytes(plaintext))
}

func incrementByte(b *byte) bool {
	*b++
	return *b == 0
}

func removeNullBytes(b []byte) []byte {
	j := 0
	for i := 0; i < len(b); i++ {
		if b[i] != 0 {
			b[j] = b[i]
			j++
		}
	}
	return b[:j]
}
