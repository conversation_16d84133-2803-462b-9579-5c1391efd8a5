package rtb_yoyo

import (
	"fmt"
	"mh_proxy/utils"
	"testing"
)

func TestHandlePrice(t *testing.T) {
	price := "NTAuMAAAAAAAAAAAAAAAAD2wk0oQ2WS1e-w0VQ"
	ekey := []byte("YqXPuzTpvB5EoUy0nnYV1YKznjKJgo67")
	ikey := []byte("dX5JyLAdqFGiTt9owjAceB6pk0uwffW2")

	decodePrice := Decrypt(price, ekey, ikey)
	priceFloat := utils.ConvertStringToFloat(decodePrice)
	priceInt := int(priceFloat)
	fmt.Println(priceInt)
}
