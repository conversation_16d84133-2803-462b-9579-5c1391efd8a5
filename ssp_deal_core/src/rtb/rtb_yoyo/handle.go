package rtb_yoyo

import (
	"encoding/json"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleByYoYo(c *gin.Context, channel string) (*YoYoResponseObject, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()
	req := &YoYoRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var deviceOs string
	switch req.Device.Os {
	case "1":
		deviceOs = "ios"
	case "2":
		deviceOs = "android"
	default:
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.Device.Network {
	case 1:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.Device.Carrier {
	case 1:
		carrier = 1
	case 3:
		carrier = 2
	case 2:
		carrier = 3
	default:
		carrier = 0
	}

	adsCount := 1
	var resultfulImp []*YoYoRequestImpObject
	var configList []models.RtbConfigByTagIDStu
	for _, imp := range req.Imp {
		var price float64
		var styleIds []string
		for _, template := range imp.TemplateList {
			styleIds = append(styleIds, template.TemplateId)
		}

		for _, bidInfo := range imp.BidInfoList {
			if bidInfo.BillingType == 1 {
				price = bidInfo.BidFloor
			}
		}

		if imp.AdsCount > 0 {
			adsCount = imp.AdsCount
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.TagId, deviceOs, styleIds, "", int(price))
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.App.Bundle) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.App.Bundle {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Make
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.App.Bundle,
			AppName:     req.App.Name,
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			Manufacturer:    manufacturer,
			Model:           req.Device.Model,
			IP:              req.Device.Ip,
			Ua:              req.Device.Ua,
			Mac:             req.Device.Mac,
			OsVersion:       req.Device.OsVersion,
			Imei:            req.Device.Imei,
			ImeiMd5:         req.Device.ImeiMd5,
			Oaid:            req.Device.Oaid,
			OaidMd5:         req.Device.OaidMd5,
			Idfa:            req.Device.Idfa,
			IdfaMd5:         req.Device.IdfaMd5,
			ScreenWidth:     req.Device.Width,
			ScreenHeight:    req.Device.Height,
			AppStoreVersion: req.Device.AppStoreVersion,
			HMSCoreVersion:  req.Device.HmsCoreVersion,
			DeviceType:      1,
			AppList:         getYoYoAppList(req.InstallAppList),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if req.Device.CaidList != nil {
		var caidMultiList []models.MHReqCAIDMulti
		if len(req.Device.CaidList.Caid) > 0 && len(req.Device.CaidList.CaidVersion) > 0 {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = req.Device.CaidList.Caid
			caidMulti.CAIDVersion = req.Device.CaidList.CaidVersion
			caidMultiList = append(caidMultiList, caidMulti)
		}
		reqStu.Device.CAIDMulti = caidMultiList
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var impItem *YoYoRequestImpObject
	for _, imp := range resultfulImp {
		if imp.TagId == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	var bidList []*YoYoResponseBidListObject
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price={price}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		bidItem := YoYoResponseBidListObject{
			Price:      mhDataItem.Ecpm,
			Id:         mhDataItem.AdID,
			ImpId:      impItem.Id,
			TagId:      impItem.TagId,
			TemplateId: "",
			Track: &YoYoResponseBidListTrackObject{
				WinUrl: winURL,
				Imp:    mhDataItem.ImpressionLink,
				Clk:    mhDataItem.ClickLink,
			},
			Items: &YoYoResponseBidItemObject{
				AppFileSize: int(mhDataItem.PackageSize / 1024),
				Title:       mhDataItem.Title,
				Desc:        mhDataItem.Description,
				Source:      "Maplehaze",
				Icon:        mhDataItem.IconURL,
				LandingUrl:  mhDataItem.LandpageURL,
				ActionUrl:   mhDataItem.DownloadURL,
				AppIcon:     mhDataItem.IconURL,
				AppName:     mhDataItem.AppName,
				PkgName:     mhDataItem.PackageName,
				AppVerName:  mhDataItem.AppVersionCode,
				//AppVerCode:        mhDataItem.AppVersion,
				AppPrivacyUrl:     mhDataItem.PrivacyLink,
				AppPermission:     mhDataItem.Permission,
				AppPermissionUrl:  mhDataItem.PermissionURL,
				AppDeveloper:      mhDataItem.Publisher,
				AppDescription:    mhDataItem.AppInfo,
				AppDescriptionUrl: mhDataItem.AppInfoURL,
			},
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				bidItem.Items.Deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					bidItem.Items.Deeplink = mhDataItem.MarketURL
				}
			}

			bidItem.TargetType = 1
			if len(bidItem.Items.Deeplink) > 0 {
				bidItem.TargetType = 3
			}
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				bidItem.Items.Deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					bidItem.Items.Deeplink = mhDataItem.DeepLink
				}
			}

			bidItem.TargetType = 2
			if len(bidItem.Items.Deeplink) > 0 {
				bidItem.TargetType = 4
			}
		}

		if deviceOs == "ios" && len(bidItem.Items.Deeplink) > 0 {
			bidItem.Items.UniversalLink = bidItem.Items.Deeplink
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image != nil {
				for _, imageItem := range mhDataItem.Image {
					bidItem.Items.Image = imageItem.URL
				}
				bidItem.TemplateId = reqRtbConfig.ImageStyleID
			}
		case 20:
			if mhDataItem.Video != nil {
				bidItem.Items.Video = mhDataItem.Video.VideoURL
				bidItem.Items.Image = mhDataItem.Video.CoverURL
				bidItem.Items.Duration = mhDataItem.Video.Duration / 1000

				bidItem.TemplateId = reqRtbConfig.VideoStyleID
			}
		}

		if len(bidItem.TemplateId) == 0 {
			continue
		}

		flag := 1
		for _, template := range impItem.TemplateList {
			if strings.Contains(template.TargetTypeStr, strconv.Itoa(bidItem.TargetType)) {
				flag = 2
			}
		}
		if flag == 1 {
			continue
		}

		bidList = append(bidList, &bidItem)
	}

	if len(bidList) == 0 {
		return &YoYoResponseObject{
			Id: req.Id,
		}, http.StatusNoContent
	}

	resp := &YoYoResponseObject{
		Id:      req.Id,
		BidList: bidList,
	}

	return resp, http.StatusOK
}

func getYoYoAppList(appList []string) []int {
	if len(appList) == 0 {
		return []int{}
	}
	var appListIdArray []int

	appListMap := models2.GetMHAppListMap()
	for _, packageName := range appList {
		if v, ok := appListMap[packageName]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}
