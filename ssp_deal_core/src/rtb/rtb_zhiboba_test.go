package rtb

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"testing"
)

func Test_HandleByZhiboba(t *testing.T) {
	str := `{
    "id": "a5c1a127acaa007130df109ce828e7ec",
    "imp": [
        {
            "id": "1",
            "slotid": "main_splash_android2",
            "bidinfo": [
                {
                    "bidtype": 0,
                    "bidfloor": 2
                }
            ],
            "native": {
                "landingtype": [
                    1,
                    2,
                    3
                ],
                "assets": [
                    {
                        "id": 0,
                        "required": 0,
                        "title": {
                            "len": 30
                        }
                    },
                    {
                        "id": 1,
                        "required": 1,
                        "img": {
                            "type": 3,
                            "w": 720,
                            "h": 1280,
                            "wmin": 388,
                            "hmin": 690
                        }
                    }
                ]
            }
        }
    ],
    "app": {
        "id": "10001001",
        "name": "\u76f4\u64ad\u5427",
        "ver": "6.4.5",
        "bundle": "android.zhibo8",
        "paid": 0,
        "keywords": "\u4f53\u80b2"
    },
    "device": {
        "dnt": 0,
        "ua": "Mozilla\/5.0 (Linux; Android 11; M2102K1C Build\/RKQ1.201112.002; wv) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/107.0.5304.91 Mobile Safari\/537.36",
        "ip": "*************",
        "geo": {
            "type": 1,
            "lat": 0,
            "lon": 0
        },
        "devicetype": 1,
        "device_type": 0,
        "make": "Xiaomi",
        "model": "M2102K1C",
        "os": 4,
        "osv": "11",
        "h": 3007,
        "w": 1440,
        "carrier": 1,
        "connectiontype": 0,
        "net": 2,
        "mac": "02:00:00:00:00:00",
        "macsha1": "c1976429369bfe063ed8b3409db7c7e7d87196d9",
        "macmd5": "0f607264fc6318a92b9e13c65db7cd3c",
        "orientation": 0,
        "density": 560,
        "dpidsha1": "df7a6fe59448b92d13a1bb8f09d0c02120a6c7de",
        "dpidmd5": "36e7f81a07248f20578a71fa5d33f95a",
        "dpid": "14665b318df39ac2",
        "didsha1": "",
        "didmd5": "",
        "did": "",
        "oaid": "7769a3697d7bc323",
        "as_package_name": "com.xiaomi.market",
        "as_version_code": 40005121,
        "as_version_name": "4.54.1",
        "hms_core_version_name": "",
        "hms_core_version_code": 0,
        "android_boot_mark": "5801c839-6e14-4937-a050-6e9800393c94",
        "android_update_mark": "353201.519999996"
    },
    "bcat": [],
    "badv": [],
    "splash_again_num": 0,
    "time": 1686118317,
    "sign": "ecfd04a0da2804ee498861146833195b",
    "time_ms": 1686118317.008837,
    "no_device": 0,
    "_abtest": 0,
    "is_support_shake": true,
    "ab_test": 0,
    "ad_info": {
        "position": 0,
        "channel": ""
    },
    "ser_timeout_ab": 2
}`

	//var buf bytes.Buffer
	//
	//gzipWriter := gzip.NewWriter(&buf)
	//_, err := gzipWriter.Write([]byte(str))
	//if err != nil {
	//	_ = gzipWriter.Close()
	//	fmt.Println(err)
	//	return
	//}
	//if err := gzipWriter.Close(); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//// 返回压缩后的 bytes 数组
	//write := buf.Bytes()

	client := &http.Client{}
	request, _ := http.NewRequest("POST", "http://test.maplehaze.cn/rtb/request?channel=40", bytes.NewReader([]byte(str)))
	request.Header.Add("Content-Type", "application/json; charset=utf-8")
	//request.Header.Add("Accept-Encoding", "gzip,deflate,br")
	//request.Header.Add("Content-Encoding", "gzip")

	resp, _ := client.Do(request)
	defer resp.Body.Close()
	bodyContent, _ := io.ReadAll(resp.Body)
	fmt.Println(string(bodyContent))
}

func Test_HandleByZhibobaGzip(t *testing.T) {
	str := `{
    "id": "a5c1a127acaa007130df109ce828e7ec",
    "imp": [
        {
            "id": "1",
            "slotid": "main_splash_android2",
            "bidinfo": [
                {
                    "bidtype": 0,
                    "bidfloor": 2
                }
            ],
            "native": {
                "landingtype": [
                    1,
                    2,
                    3
                ],
                "assets": [
                    {
                        "id": 0,
                        "required": 0,
                        "title": {
                            "len": 30
                        }
                    },
                    {
                        "id": 1,
                        "required": 1,
                        "img": {
                            "type": 3,
                            "w": 720,
                            "h": 1280,
                            "wmin": 388,
                            "hmin": 690
                        }
                    }
                ]
            }
        }
    ],
    "app": {
        "id": "10001001",
        "name": "\u76f4\u64ad\u5427",
        "ver": "6.4.5",
        "bundle": "android.zhibo8",
        "paid": 0,
        "keywords": "\u4f53\u80b2"
    },
    "device": {
        "dnt": 0,
        "ua": "Mozilla\/5.0 (Linux; Android 11; M2102K1C Build\/RKQ1.201112.002; wv) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/107.0.5304.91 Mobile Safari\/537.36",
        "ip": "*************",
        "geo": {
            "type": 1,
            "lat": 0,
            "lon": 0
        },
        "devicetype": 1,
        "device_type": 0,
        "make": "Xiaomi",
        "model": "M2102K1C",
        "os": 4,
        "osv": "11",
        "h": 3007,
        "w": 1440,
        "carrier": 1,
        "connectiontype": 0,
        "net": 2,
        "mac": "02:00:00:00:00:00",
        "macsha1": "c1976429369bfe063ed8b3409db7c7e7d87196d9",
        "macmd5": "0f607264fc6318a92b9e13c65db7cd3c",
        "orientation": 0,
        "density": 560,
        "dpidsha1": "df7a6fe59448b92d13a1bb8f09d0c02120a6c7de",
        "dpidmd5": "36e7f81a07248f20578a71fa5d33f95a",
        "dpid": "14665b318df39ac2",
        "didsha1": "",
        "didmd5": "",
        "did": "",
        "oaid": "7769a3697d7bc323",
        "as_package_name": "com.xiaomi.market",
        "as_version_code": 40005121,
        "as_version_name": "4.54.1",
        "hms_core_version_name": "",
        "hms_core_version_code": 0,
        "android_boot_mark": "5801c839-6e14-4937-a050-6e9800393c94",
        "android_update_mark": "353201.519999996"
    },
    "bcat": [],
    "badv": [],
    "splash_again_num": 0,
    "time": 1686118317,
    "sign": "ecfd04a0da2804ee498861146833195b",
    "time_ms": 1686118317.008837,
    "no_device": 0,
    "_abtest": 0,
    "is_support_shake": true,
    "ab_test": 0,
    "ad_info": {
        "position": 0,
        "channel": ""
    },
    "ser_timeout_ab": 2
}`

	var buf bytes.Buffer

	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write([]byte(str))
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
		return
	}
	if err := gzipWriter.Close(); err != nil {
		fmt.Println(err)
		return
	}
	// 返回压缩后的 bytes 数组
	write := buf.Bytes()

	client := &http.Client{}
	request, _ := http.NewRequest("POST", "http://test.maplehaze.cn/rtb/request?channel=40", bytes.NewReader(write))
	request.Header.Add("Content-Type", "application/json; charset=utf-8")
	request.Header.Add("Accept-Encoding", "gzip,deflate,br")
	request.Header.Add("Content-Encoding", "gzip")

	resp, _ := client.Do(request)
	defer resp.Body.Close()
	bodyContent, _ := io.ReadAll(resp.Body)
	fmt.Println(string(bodyContent))
}
