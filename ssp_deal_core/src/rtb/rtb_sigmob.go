package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleBySigmob ...
func HandleBySigmob(c *gin.Context, channel string) (*map[string]interface{}, int) {

	bodyContent, err := c.GetRawData()
	var sigmobReq SigmobReq

	err = json.Unmarshal([]byte(bodyContent), &sigmobReq)

	if err != nil {
		fmt.Println(err)
		return jsonSigmobNoBidReturn("null", "parser error")
	}

	// fmt.Println("sigmob req: " + string(bodyContent))

	reqOs := ""
	if sigmobReq.Device.Os == 1 {
		reqOs = "ios"
	} else if sigmobReq.Device.Os == 2 {
		reqOs = "android"
	} else {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "wrong os")
	}

	reqDeivceMake := sigmobReq.Device.Make
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(sigmobReq.Device.UA) == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "wrong ua")
	}

	reqConnectType := 1
	if sigmobReq.Device.ConnectionType == 100 {
		reqConnectType = 1
	} else if sigmobReq.Device.ConnectionType == 0 {
		reqConnectType = 0
	} else if sigmobReq.Device.ConnectionType == 2 {
		reqConnectType = 2
	} else if sigmobReq.Device.ConnectionType == 3 {
		reqConnectType = 3
	} else if sigmobReq.Device.ConnectionType == 4 {
		reqConnectType = 4
	} else if sigmobReq.Device.ConnectionType == 5 {
		reqConnectType = 7
	}
	reqCarrier := 0
	if strings.Contains(sigmobReq.Device.Carrier, "Mobile") {
		reqCarrier = 1
	} else if strings.Contains(sigmobReq.Device.Carrier, "Unicom") {
		reqCarrier = 2
	} else if strings.Contains(sigmobReq.Device.Carrier, "Telecom") {
		reqCarrier = 3
	}

	if len(sigmobReq.Imp) == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "wrong imp")
	}

	var reqOKImps []SigmobReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range sigmobReq.Imp {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.TagID
		// if reqOs == "android" {
		// 	reqTagID = "sigmob_" + item.TagID + "_android"
		// } else if reqOs == "ios" {
		// 	reqTagID = "sigmob_" + item.TagID + "_ios"
		// } else {
		// 	continue
		// }
		reqPrice := item.BidFloor
		if item.Pmp.Deal.BidFloor > 0 {
			reqPrice = item.Pmp.Deal.BidFloor
		}
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return jsonSigmobNoBidReturn(sigmobReq.ID, "not active 0")
		// }

		reqDeallID := item.Pmp.Deal.ID
		// fmt.Println("sigmob dealid: " + reqDeallID)
		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", reqDeallID, int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return jsonSigmobNoBidReturn(sigmobReq.ID, "not active 1")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "wrong imp")
	}

	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: sigmobReq.App.Bundle,
			AppName:     sigmobReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: len(reqOKImps),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    sigmobReq.Device.Osv,
			Model:        sigmobReq.Device.Model,
			Manufacturer: reqDeivceMake,
			ImeiMd5:      sigmobReq.Device.ImeiMd5,
			AndroidIDMd5: sigmobReq.Device.AndroidIDMd5,
			Oaid:         sigmobReq.Device.Oaid,
			IdfaMd5:      sigmobReq.Device.IdfaMd5,
			Ua:           sigmobReq.Device.UA,
			DeviceType:   1,
			IP:           sigmobReq.Device.IP,
			BootMark:     sigmobReq.Device.BootMark,
			UpdateMark:   sigmobReq.Device.UpdateMark,
			// Mac:       sigmobReq.Device.Mac,
			DeviceStartSec:     sigmobReq.Device.DeviceStartSec,
			Country:            "CN",
			Language:           sigmobReq.Device.Language,
			DeviceNameMd5:      sigmobReq.Device.DeviceNameMd5,
			HardwareMachine:    sigmobReq.Device.HardwareMachine,
			HardwareModel:      sigmobReq.Device.HardwareModel,
			PhysicalMemoryByte: sigmobReq.Device.PhysicalMemoryByte,
			HarddiskSizeByte:   sigmobReq.Device.HarddiskSizeByte,
			SystemUpdateSec:    sigmobReq.Device.SystemUpdateSec,
			TimeZone:           sigmobReq.Device.TimeZone,
			// appstore version
			AppStoreVersion: sigmobReq.User.AppStoreVersion,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
		Geo: models.MHReqGeo{
			Lat: sigmobReq.Device.Geo.Lat,
			Lng: sigmobReq.Device.Geo.Lon,
		},
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "no fill 0")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "no fill 1")
	}

	var impItem SigmobReqImp
	for _, imp := range reqOKImps {
		if imp.TagID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if len(impItem.TagID) == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	sigmobBidObjArray := []map[string]interface{}{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		var winURLArray []string
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)
		winURLArray = append(winURLArray, winURL)

		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impItem = strings.Replace(impItem, "__DOWN_X__", "${DOWN_X}", -1)
			impItem = strings.Replace(impItem, "__DOWN_Y__", "${DOWN_Y}", -1)
			impItem = strings.Replace(impItem, "__UP_X__", "${UP_X}", -1)
			impItem = strings.Replace(impItem, "__UP_Y__", "${UP_Y}", -1)

			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__DOWN_X__", "${DOWN_X}", -1)
			clkItem = strings.Replace(clkItem, "__DOWN_Y__", "${DOWN_Y}", -1)
			clkItem = strings.Replace(clkItem, "__UP_X__", "${UP_X}", -1)
			clkItem = strings.Replace(clkItem, "__UP_Y__", "${UP_Y}", -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}

		// video start finish link
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		var video1QuarterTrackArray []string
		var video2QuarterTrackArray []string
		var video3QuarterTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 2 {
					for _, trackEventItem := range trackItem.EventURLS {
						video1QuarterTrackArray = append(video1QuarterTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 3 {
					for _, trackEventItem := range trackItem.EventURLS {
						video2QuarterTrackArray = append(video2QuarterTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 4 {
					for _, trackEventItem := range trackItem.EventURLS {
						video3QuarterTrackArray = append(video3QuarterTrackArray, trackEventItem)
					}
				}
			}
		}

		// crid
		crid := mhDataItem.Crid
		if len(crid) == 0 {
			crid = utils.GetMd5(mhDataItem.Title)
		}

		sigmobBidObjItemMap := map[string]interface{}{}
		sigmobBidObjItemMap["id"] = bigdataUID
		sigmobBidObjItemMap["impid"] = impItem.ID
		sigmobBidObjItemMap["price"] = ecpm
		sigmobBidObjItemMap["wurl"] = winURLArray
		sigmobBidObjItemMap["adid"] = mhDataItem.AdID
		sigmobBidObjItemMap["crid"] = crid
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				sigmobBidObjItemMap["deeplinkurl"] = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					sigmobBidObjItemMap["deeplinkurl"] = mhDataItem.MarketURL
				}
			}

			sigmobBidObjItemMap["interactiontype"] = 1
			sigmobBidObjItemMap["landingpage"] = mhDataItem.LandpageURL

		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				sigmobBidObjItemMap["deeplinkurl"] = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					sigmobBidObjItemMap["deeplinkurl"] = mhDataItem.DeepLink
				}
			}

			sigmobBidObjItemMap["interactiontype"] = 2
			sigmobBidObjItemMap["landingpage"] = mhDataItem.DownloadURL

		} else {
			continue
		}
		// sigmobBidObjItemMap["landingpage"] = mhDataItem.AdURL
		if len(mhDataItem.PackageName) > 0 {
			sigmobBidObjItemMap["bundle"] = mhDataItem.PackageName
		}

		if len(mhDataItem.Publisher) > 0 {
			sigmobBidObjItemMap["developer"] = mhDataItem.Publisher
		}
		if len(mhDataItem.AppVersion) > 0 {
			sigmobBidObjItemMap["app_ver"] = mhDataItem.AppVersion
		}
		if len(mhDataItem.Permission) > 0 {
			sigmobBidObjItemMap["permissions_url"] = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
		}
		if len(mhDataItem.PrivacyLink) > 0 {
			sigmobBidObjItemMap["privacy"] = mhDataItem.PrivacyLink
		}

		sigmobBidObjItemMaterialMap := map[string]interface{}{}
		sigmobBidObjItemMaterialMap["title"] = mhDataItem.Title
		sigmobBidObjItemMaterialMap["desc"] = mhDataItem.Description
		if len(mhDataItem.IconURL) > 0 {
			sigmobBidObjItemMaterialMap["icon"] = mhDataItem.IconURL
		}

		if isVideoType == 1 {
			// 视频
			sigmobBidObjItemMaterialVideoMap := map[string]interface{}{}
			sigmobBidObjItemMaterialVideoMap["url"] = mhDataItem.Video.VideoURL

			sigmobBidObjItemMaterialVideoCoverMap := map[string]interface{}{}
			sigmobBidObjItemMaterialVideoCoverMap["url"] = mhDataItem.Video.CoverURL
			sigmobBidObjItemMaterialVideoMap["img"] = sigmobBidObjItemMaterialVideoCoverMap
			sigmobBidObjItemMaterialMap["video"] = sigmobBidObjItemMaterialVideoMap
		} else {
			// 图片
			sigmobBidObjItemMaterialImgMap := map[string]interface{}{}
			sigmobBidObjItemMaterialImgMap["url"] = mhDataItem.Image[0].URL
			sigmobBidObjItemMaterialMap["img"] = sigmobBidObjItemMaterialImgMap
		}
		sigmobBidObjItemMap["material"] = sigmobBidObjItemMaterialMap

		// attr
		if isVideoType == 1 {
			// 视频
			sigmobBidObjItemMap["attr"] = 4
		} else {
			// 图片
			sigmobBidObjItemMap["attr"] = 3
		}

		// deal id
		if len(reqRtbConfig.DealID) > 0 {
			sigmobBidObjItemMap["dealid"] = reqRtbConfig.DealID
		}

		// imp tracking
		sigmobTrackingMapArray := []map[string]interface{}{}
		if len(impTrackArray) > 0 {
			sigmobTrackingMap := map[string]interface{}{}
			sigmobTrackingMap["etype"] = "imp"
			sigmobTrackingMap["eurl"] = impTrackArray

			sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
		}

		// clk tracking
		if len(clkTrackArray) > 0 {
			sigmobTrackingMap := map[string]interface{}{}
			sigmobTrackingMap["etype"] = "click"
			sigmobTrackingMap["eurl"] = clkTrackArray

			sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
		}

		// video tracking
		if isVideoType == 1 {
			if len(videoStartTrackArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "start"
				sigmobTrackingMap["eurl"] = videoStartTrackArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}

			if len(videoFinishTrackArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "complete"
				sigmobTrackingMap["eurl"] = videoFinishTrackArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}

			if len(video1QuarterTrackArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "play_quarter"
				sigmobTrackingMap["eurl"] = video1QuarterTrackArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}

			if len(video2QuarterTrackArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "play_two_quarters"
				sigmobTrackingMap["eurl"] = video2QuarterTrackArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}

			if len(video3QuarterTrackArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "play_three_quarters"
				sigmobTrackingMap["eurl"] = video3QuarterTrackArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}
		}

		// deeplink tracking
		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			if len(deepLinkTrackOKArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "open_deeplink"
				sigmobTrackingMap["eurl"] = deepLinkTrackOKArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}

			if len(deepLinkTrackFailedArray) > 0 {
				sigmobTrackingMap := map[string]interface{}{}
				sigmobTrackingMap["etype"] = "open_deeplink_failed"
				sigmobTrackingMap["eurl"] = deepLinkTrackFailedArray

				sigmobTrackingMapArray = append(sigmobTrackingMapArray, sigmobTrackingMap)
			}
		}

		sigmobBidObjItemMap["trackings"] = sigmobTrackingMapArray

		sigmobBidObjArray = append(sigmobBidObjArray, sigmobBidObjItemMap)
	}

	if len(sigmobBidObjArray) == 0 {
		return jsonSigmobNoBidReturn(sigmobReq.ID, "no fill 2")
	}

	// bid array
	sigmobRespBidMap := map[string]interface{}{}
	sigmobRespBidMap["bid"] = sigmobBidObjArray
	sigmobRespBidMap["seat"] = "maplehaze"

	// seat bid array
	zhihuRespSeatBidArrayMap := []map[string]interface{}{}
	zhihuRespSeatBidArrayMap = append(zhihuRespSeatBidArrayMap, sigmobRespBidMap)

	// resp
	sigmobRespMap := map[string]interface{}{}
	sigmobRespMap["id"] = sigmobReq.ID
	sigmobRespMap["nbr"] = 0
	sigmobRespMap["seatbid"] = zhihuRespSeatBidArrayMap

	// sigmobByte, _ := json.Marshal(sigmobRespMap)
	// sigmobString := string(sigmobByte)
	// fmt.Println("sigmob resp: " + sigmobString)

	return &sigmobRespMap, 200
}

func jsonSigmobNoBidReturn(reqID string, reason string) (*map[string]interface{}, int) {
	// fmt.Println("sigmob no bid reason: " + reason)

	// sigmobRespMap := map[string]interface{}{}
	// sigmobRespMap["id"] = reqID
	// sigmobRespMap["nbr"] = 1
	return nil, 204
}

// SigmobReq ...
type SigmobReq struct {
	ID      string          `json:"id"`
	Version string          `json:"version"`
	App     SigmobReqApp    `json:"app"`
	Device  SigmobReqDevice `json:"device"`
	Imp     []SigmobReqImp  `json:"imp"`
	User    SigmobReqUser   `json:"user"`
}

// SigmobReqImp ...
type SigmobReqImp struct {
	ID                string             `json:"id"`
	Instl             int                `json:"instl"`
	TagID             string             `json:"tagid"`
	BidFloor          int                `json:"bidfloor"`
	IsSupportDeeplink bool               `json:"issupportdeeplink"`
	Video             SigmobReqImpVideo  `json:"video"`
	Banner            SigmobReqImpBanner `json:"banner"`
	Pmp               SigmobReqImpPmp    `json:"pmp"`
}

// SigmobReqImpVideo ...
type SigmobReqImpVideo struct {
	Mimes       []string `json:"mimes"`
	MinDuration int      `json:"minduration"`
	MaxDuration int      `json:"maxduration"`
	Width       int      `json:"w"`
	Height      int      `json:"h"`
}

// SigmobReqImpBanner ...
type SigmobReqImpBanner struct {
	Mimes  []string `json:"mimes"`
	Width  int      `json:"w"`
	Height int      `json:"h"`
}

// SigmobReqImpPmp ...
type SigmobReqImpPmp struct {
	Private int                 `json:"private"`
	Deal    SigmobReqImpPmpDeal `json:"deals"`
}

// SigmobReqImpPmpDeal ...
type SigmobReqImpPmpDeal struct {
	ID       string `json:"id"`
	BidFloor int    `json:"bidfloor"`
	At       int    `json:"at"`
}

// SigmobReqApp ...
type SigmobReqApp struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Bundle      string `json:"bundle"`
	SupportHTTP int    `json:"support_http"`
}

// SigmobReqUser ...
type SigmobReqUser struct {
	AppStoreVersion string `json:"as_version"`
}

// SigmobReqDevice ...
type SigmobReqDevice struct {
	Os             int                `json:"os"`
	Osv            string             `json:"osv"`
	UA             string             `json:"ua"`
	IP             string             `json:"ip"`
	IPV6           string             `json:"ipv6"`
	Width          int                `json:"w"`
	Height         int                `json:"h"`
	Make           string             `json:"make"`
	Model          string             `json:"model"`
	ImeiMd5        string             `json:"orimeimd5"`
	AndroidIDMd5   string             `json:"androididmd5"`
	Mac            string             `json:"mac"`
	Oaid           string             `json:"oaid"`
	IdfaMd5        string             `json:"ifamd5"`
	ConnectionType int                `json:"connectiontype"`
	Carrier        string             `json:"carrier"`
	BootMark       string             `json:"boot_mark"`
	UpdateMark     string             `json:"update_mark"`
	Geo            SigmobReqDeviceGeo `json:"geo"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec"`
	Language           string `json:"device_language"`
	DeviceNameMd5      string `json:"device_name_md5"`
	HardwareMachine    string `json:"hardware_machine"`
	HardwareModel      string `json:"hardware_model"`
	PhysicalMemoryByte string `json:"physical_memory"`
	HarddiskSizeByte   string `json:"harddisk_size"`
	SystemUpdateSec    string `json:"system_update_sec"`
	TimeZone           string `json:"time_zone"`
}

// SigmobReqDeviceGeo ...
type SigmobReqDeviceGeo struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
}
