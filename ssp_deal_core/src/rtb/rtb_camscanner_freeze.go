package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByCamScanner ...
func HandleByCamScanner(c *gin.Context, channel string) (*map[string]interface{}, int) {

	bodyContent, err := c.GetRawData()

	var camscannerReq CamScannerReq

	err = json.Unmarshal([]byte(bodyContent), &camscannerReq)

	if err != nil {
		fmt.Println(err)
		return jsonCamScannerNoBidReturn("parser error")
	}

	var reqOs = ""

	if strings.ToLower(camscannerReq.Device.Os) == "android" {
		reqOs = "android"
	} else {
		reqOs = "ios"
	}

	// iOS / Android
	if reqOs == "android" || reqOs == "ios" {
	} else {
		return jsonCamScannerNoBidReturn("wrong os")
	}

	reqDeivceMake := camscannerReq.Device.Brand
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(camscannerReq.Device.UA) == 0 {
		return jsonCamScannerNoBidReturn("wrong ua")
	}

	/*
		0-未知
		1-wifi
		2-2G
		3-3G
		4-4G
		5–5G

		0-未知
		1-移动
		2-联通
		3-电信
	*/
	reqConnectType := camscannerReq.Device.Network
	if reqConnectType == 5 {
		reqConnectType = 7
	}

	reqCarrier := camscannerReq.Device.Carrier

	if len(camscannerReq.ImpList) == 0 {
		return jsonCamScannerNoBidReturn("wrong imp: empty")
	}

	var reqOKImps []CamScannerReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	var reqOKImpsCounter [100]int

	for index, item := range camscannerReq.ImpList {
		reqOKImpsCounter[index] = 0

		reqTagID := item.PID
		reqPrice := item.BidFloor

		var rtbConfigArrayByTagID *[]models.RtbConfigByTagIDStu

		rtbConfigArrayByTagID = models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		fmt.Println(rtbConfigArrayByTagID, channel, reqTagID, reqOs, "", "", int(reqPrice))
		if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
			continue
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)

		reqOKImpsCounter[index]++

	}

	if len(reqOKImps) == 0 {
		return jsonCamScannerNoBidReturn("wrong imp: reqOKImps is empty")
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: camscannerReq.App.PackageName,
			AppName:     camscannerReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: len(reqOKImps),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 reqOs,
			OsVersion:          camscannerReq.Device.Osv,
			Model:              camscannerReq.Device.Model,
			Manufacturer:       reqDeivceMake,
			Imei:               camscannerReq.Device.Imei,
			ImeiMd5:            camscannerReq.Device.ImeiMd5,
			AndroidID:          camscannerReq.Device.AndroidID,
			Oaid:               camscannerReq.Device.Oaid,
			Idfa:               camscannerReq.Device.Idfa,
			Ua:                 camscannerReq.Device.UA,
			ScreenWidth:        camscannerReq.Device.Width,
			ScreenHeight:       camscannerReq.Device.Height,
			DeviceType:         1,
			IP:                 camscannerReq.Device.IP,
			DeviceStartSec:     camscannerReq.Device.DeviceStartSec,
			Country:            camscannerReq.Device.Country,
			Language:           camscannerReq.Device.Language,
			DeviceNameMd5:      camscannerReq.Device.DeviceNameMd5,
			HardwareModel:      camscannerReq.Device.Model,
			PhysicalMemoryByte: camscannerReq.Device.PhysicalMemoryByte,
			HarddiskSizeByte:   camscannerReq.Device.HarddiskSizeByte,
			SystemUpdateSec:    camscannerReq.Device.SystemUpdateSec,
			TimeZone:           camscannerReq.Device.TimeZone,
			BootMark:           camscannerReq.Device.BootMark,
			UpdateMark:         camscannerReq.Device.UpdateMark,
			// CAID:               camscannerReq.Device.Caid,
			// CAIDVersion:        camscannerReq.Device.CaidVersion,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}

	// fmt.Println("reqStu", reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		fmt.Println(mhResp.Ret)
		return jsonCamScannerNoBidReturn("no filli 190")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonCamScannerNoBidReturn("no fill 194")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	camscannerRespPIDArrayMap := []map[string]interface{}{}

	for i := 0; i < len(reqOKImps); i++ {
		if i > mhRespCount-1 {
			continue
		}

		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		camscannerBidObjItemMap := map[string]interface{}{}
		camscannerBidObjItemMap["id"] = mhDataItem.AdID
		camscannerBidObjItemMap["title"] = mhDataItem.Title
		camscannerBidObjItemMap["description"] = mhDataItem.Description
		camscannerBidObjItemMap["price"] = ecpm

		nurl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		camscannerBidObjItemMap["nurl"] = nurl

		if reqOs == "ios" {
			if mhDataItem.InteractType == 0 {
				camscannerBidObjItemMap["target_url"] = mhDataItem.LandpageURL
			} else if mhDataItem.InteractType == 1 {
				camscannerBidObjItemMap["target_url"] = mhDataItem.DownloadURL
			}
		} else {
			if mhDataItem.InteractType == 0 {
				// H5
				camscannerBidObjItemMap["target_url"] = mhDataItem.LandpageURL
			} else if mhDataItem.InteractType == 1 {
				// 直接下载
				camscannerBidObjItemMap["target_url"] = mhDataItem.DownloadURL
			}
		}

		if len(mhDataItem.DeepLink) > 0 {
			camscannerBidObjItemMap["dp_url"] = mhDataItem.DeepLink
		}

		camscannerBidObjItemMap["imptrackers"] = mhDataItem.ImpressionLink

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(camscannerReq.Device.Width), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(camscannerReq.Device.Height), -1)
			clkItem = strings.Replace(clkItem, "__DOWN_X__", "CS_REL_DOWN_X", -1)
			clkItem = strings.Replace(clkItem, "__DOWN_Y__", "CS_REL_DOWN_Y", -1)
			clkItem = strings.Replace(clkItem, "__UP_X__", "CS_REL_UP_X", -1)
			clkItem = strings.Replace(clkItem, "__UP_Y__", "CS_REL_UP_Y", -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}
		camscannerBidObjItemMap["clicktrackers"] = clkTrackArray

		// deeplink tracking
		camscannerBidObjItemDpTrackerMap := map[string]interface{}{}
		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}
		camscannerBidObjItemDpTrackerMap["suc"] = deepLinkTrackOKArray
		camscannerBidObjItemDpTrackerMap["fail"] = deepLinkTrackFailedArray

		camscannerBidObjItemMap["dptrackers"] = camscannerBidObjItemDpTrackerMap

		camscannerBidObjItemVideotrackersMap := map[string]interface{}{}

		// video start finish link
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				}
			}
		}
		camscannerBidObjItemVideotrackersMap["start"] = videoStartTrackArray
		camscannerBidObjItemVideotrackersMap["complete"] = videoFinishTrackArray
		camscannerBidObjItemMap["videotrackers"] = camscannerBidObjItemVideotrackersMap

		camscannerBidObjItemMap["expire"] = 3600

		camscannerBidObjItemNativeMap := map[string]interface{}{}
		camscannerBidObjItemNativeMap["adpid"] = mhDataItem.AdID

		camscannerBidObjItemNativeAssetMapArray := []map[string]interface{}{}

		if isVideoType == 1 {

			camscannerBidObjItemNativeAssetMap := map[string]interface{}{}
			camscannerBidObjItemNativeAssetMap["type"] = 1

			videoObjMap := map[string]interface{}{}
			videoObjMap["w"] = mhDataItem.Video.Width
			videoObjMap["h"] = mhDataItem.Video.Height
			videoObjMap["duration"] = mhDataItem.Video.Duration
			videoObjMap["url"] = mhDataItem.Video.VideoURL
			camscannerBidObjItemNativeAssetMap["video"] = videoObjMap
			camscannerBidObjItemNativeAssetMapArray = append(camscannerBidObjItemNativeAssetMapArray, camscannerBidObjItemNativeAssetMap)

		} else {
			for _, assetItem := range mhDataItem.Image {
				camscannerBidObjItemNativeAssetMap := map[string]interface{}{}
				camscannerBidObjItemNativeAssetMap["type"] = 2

				imageObjMap := map[string]interface{}{}
				imageObjMap["w"] = assetItem.Width
				imageObjMap["h"] = assetItem.Height
				imageObjMap["url"] = assetItem.URL
				camscannerBidObjItemNativeAssetMap["image"] = imageObjMap
				camscannerBidObjItemNativeAssetMapArray = append(camscannerBidObjItemNativeAssetMapArray, camscannerBidObjItemNativeAssetMap)

			}
		}

		camscannerBidObjItemNativeMap["assets"] = camscannerBidObjItemNativeAssetMapArray
		camscannerBidObjItemMap["native"] = camscannerBidObjItemNativeMap

		camscannerRespPIDArrayMap = append(camscannerRespPIDArrayMap, camscannerBidObjItemMap)
	}

	// seat id

	// resp
	camscannerRespMap := map[string]interface{}{}
	camscannerRespMap["id"] = camscannerReq.ID
	camscannerRespMap["status"] = 0
	camscannerRespMap["meessage"] = ""
	camscannerRespMap["seatbid"] = camscannerRespPIDArrayMap
	camscannerRespMap["currency"] = "CNY"

	return jsonCamScannerOKBidReturn(&camscannerRespMap)
}

func jsonCamScannerOKBidReturn(resp *map[string]interface{}) (*map[string]interface{}, int) {
	return resp, 200
}

func jsonCamScannerNoBidReturn(reason string) (*map[string]interface{}, int) {
	fmt.Println(reason)
	return nil, 204
}

type CamScannerReq struct {
	Version string              `json:"version"`
	ID      string              `json:"id"`
	ImpList []CamScannerReqImp  `json:"imp"`
	App     CamScannerReqApp    `json:"app"`
	Device  CamScannerReqDevice `json:"device"`
	User    CamScannerReqUser   `json:"user"`
}

type CamScannerReqImp struct {
	ID       int    `json:"id"`
	PID      string `json:"pid"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	BidFloor int    `json:"bidfloor"`
}

type CamScannerReqApp struct {
	Name        string `json:"appname"`
	PackageName string `json:"pkgname"`
	Ver         string `json:"appver"`
}

type CamScannerReqDevice struct {
	IP                 string                 `json:"ip"`
	UA                 string                 `json:"ua"`
	Idfa               string                 `json:"idfa"`
	Openudid           string                 `json:"oid"`
	Imei               string                 `json:"imei"`
	ImeiMd5            string                 `json:"imei_md5"`
	Oaid               string                 `json:"oaid"`
	Mac                string                 `json:"mac"`
	AndroidID          string                 `json:"dpid"`
	DeviceType         int                    `json:"device_type"` //0 手机  1 平板 2 PC 3 互联网电视
	Brand              string                 `json:"brand"`
	Model              string                 `json:"model"`
	Os                 string                 `json:"os"`
	Osv                string                 `json:"osv"`
	Oshv               string                 `json:"oshv"`
	Network            int                    `json:"conntype"` //0 未知 1 Wifi 2 2G 3 3G 4 4G 5 5G
	HmsVercode         string                 `json:"hms_vercode"`
	AppmarketVercode   string                 `json:"appmarket_vercode"`
	Carrier            int                    `json:"operator"` //0 未知 1 移动 2 联通 3 电信
	Width              int                    `json:"width"`
	Height             int                    `json:"height"`
	PPI                string                 `json:"pixel_ratio"`
	Orientation        string                 `json:"orientation"`
	Geo                CamScannerReqDeviceGeo `json:"geo"`
	BootMark           string                 `json:"boot_mark"`
	UpdateMark         string                 `json:"update_mark"`
	Aaid               string                 `json:"aaid"`
	DeviceStartSec     string                 `json:"device_start_sec"`
	Country            string                 `json:"country"`
	Language           string                 `json:"language"`
	DeviceNameMd5      string                 `json:"device_name_md5"`
	HardwareMachine    string                 `json:"hardware_machine"`
	PhysicalMemoryByte string                 `json:"physical_memory"`
	HarddiskSizeByte   string                 `json:"harddisk_size"`
	SystemUpdateSec    string                 `json:"system_update_sec"`
	TimeZone           string                 `json:"time_zone"`
}

type CamScannerReqDeviceGeo struct {
	Lat float32 `json:"lat"`
	Lon float32 `json:"lon"`
}

type CamScannerReqUser struct {
	Gender   int    `json:"gender"`
	Birth    int    `json:"birth"`
	DeviceID string `json:"device_id"`
}
