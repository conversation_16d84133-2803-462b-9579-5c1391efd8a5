package rtb

import (
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/xmly"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByXmly ...
func HandleByXmly(c *gin.Context, channel string) *xmly.BidResponse {

	bodyContent, err := c.GetRawData()

	xmlyReq := &xmly.BidRequest{}
	err = proto.Unmarshal(bodyContent, xmlyReq)
	if err != nil {
		fmt.Println(err)
		return xmlyNoBidReturn("error", "parser error")
	}
	// fmt.Println(xmlyReq)
	// fmt.Println(xmlyReq.GetApiVersion())
	// fmt.Println(xmlyReq.GetDevice().GetOs())
	// fmt.Println(xmlyReq.GetDevice().GetIdfa())
	// fmt.Println(xmlyReq.GetDevice().GetUa())
	// fmt.Println(xmlyReq.GetDevice().GetIp())

	reqOs := ""
	if strings.Contains(strings.ToLower(xmlyReq.GetDevice().GetOs()), "android") {
		reqOs = "android"
	} else if strings.Contains(strings.ToLower(xmlyReq.GetDevice().GetOs()), "ios") {
		reqOs = "ios"
	} else {
		return xmlyNoBidReturn(xmlyReq.GetId(), "wrong os")
	}

	reqDeivceMake := xmlyReq.GetDevice().GetMake()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqConnectType := 1
	// 网络连接类型 0，2G；1，3G；2，4G；3，wifi；4，其它；5，5G
	if xmlyReq.GetDevice().GetConnectiontype() == 0 {
		reqConnectType = 2
	} else if xmlyReq.GetDevice().GetConnectiontype() == 1 {
		reqConnectType = 3
	} else if xmlyReq.GetDevice().GetConnectiontype() == 2 {
		reqConnectType = 4
	} else if xmlyReq.GetDevice().GetConnectiontype() == 3 {
		reqConnectType = 1
	} else if xmlyReq.GetDevice().GetConnectiontype() == 4 {
		reqConnectType = 0
	} else if xmlyReq.GetDevice().GetConnectiontype() == 5 {
		reqConnectType = 5
	} else {
		reqConnectType = 1
	}

	reqCarrier := 0
	// 运营商 0，移动；1，联通； 2，电信；3，其它
	if xmlyReq.GetDevice().GetCarrier() == 0 {
		reqCarrier = 1
	} else if xmlyReq.GetDevice().GetCarrier() == 1 {
		reqCarrier = 2
	} else if xmlyReq.GetDevice().GetCarrier() == 2 {
		reqCarrier = 3
	} else if xmlyReq.GetDevice().GetCarrier() == 3 {
		reqCarrier = 0
	} else {
		reqCarrier = 0
	}

	reqOsv := xmlyReq.GetDevice().GetOsv()
	if len(reqOsv) > 0 {
	} else {
		return xmlyNoBidReturn(xmlyReq.GetId(), "wrong osv")
	}
	reqImps := xmlyReq.GetImp()
	// fmt.Println(len(reqSlots))
	if len(reqImps) == 0 {
		return xmlyNoBidReturn(xmlyReq.GetId(), "wrong slots")
	}

	var reqOKImps []*xmly.BidRequest_Imp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqImps {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.GetTagid()

		if len(item.GetStyle()) == 0 {
			continue
		}
		reqStyleID := item.GetStyle()[0].GetStyle()

		if reqOs == "android" {
			// reqTagID = "xmly_" + reqTagID + "_" + utils.ConvertIntToString(int(reqStyleID)) + "_android"
		} else if reqOs == "ios" {
			// reqTagID = "xmly_" + reqTagID + "_" + utils.ConvertIntToString(int(reqStyleID)) + "_ios"
		} else {
			continue
		}

		reqPrice := item.GetBidfloor()
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return xmlyNoBidReturn(xmlyReq.GetId(), "not active")
		// }
		reqDealIDArray := item.GetDealid()
		// fmt.Println("xmly_dealid: ", reqDealIDArray)
		reqDealID := ""
		if len(reqDealIDArray) > 0 {
			reqDealID = reqDealIDArray[0]
		}

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, utils.ConvertIntToString(int(reqStyleID)), reqDealID, int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return xmlyNoBidReturn(xmlyReq.GetId(), "not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}
	// fmt.Println("aaaaaa")
	// fmt.Println(reqRtbConfig)
	// fmt.Println("bbbbbb")

	if len(reqOKImps) == 0 {
		return xmlyNoBidReturn(xmlyReq.GetId(), "wrong imp")
	}
	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	var caidMultiList []models.MHReqCAIDMulti
	if len(xmlyReq.Device.GetCaids()) > 0 {
		for _, item := range xmlyReq.Device.GetCaids() {
			var tmpCaid models.MHReqCAIDMulti
			tmpCaid.CAID = item.GetCaid()
			tmpCaid.CAIDVersion = item.GetVersion()
			caidMultiList = append(caidMultiList, tmpCaid)
		}
	}
	// debug
	// if reqOs == "ios" {
	// 	if len(xmlyReq.Device.GetCaids()) > 0 {
	// 		go func() {
	// 			defer func() {
	// 				if err := recover(); err != nil {
	// 					fmt.Println("bigdata debug up panic:", err)
	// 				}
	// 			}()
	// 			tmpCaidString := ""
	// 			tmpCaidVersionString := ""
	// 			for _, item := range xmlyReq.Device.GetCaids() {
	// 				tmpCaidString = tmpCaidString + "," + item.GetCaid()
	// 				tmpCaidVersionString = tmpCaidVersionString + "," + item.GetVersion()

	// 			}
	// 			models.DebugXmlyToBigData2(c, uuid.NewV4().String(), tmpCaidString, tmpCaidVersionString)
	// 		}()
	// 	}
	// }

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: xmlyReq.GetApp().GetPkgname(),
			AppName:     xmlyReq.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: len(reqOKImps),
			Width:   640,
			Height:  960,
		},
		Device: models.MHReqDevice{
			Os:              reqOs,
			OsVersion:       reqOsv,
			Model:           xmlyReq.GetDevice().GetModel(),
			Manufacturer:    reqDeivceMake,
			ImeiMd5:         xmlyReq.GetDevice().GetImeimd5(),
			AndroidIDMd5:    xmlyReq.GetDevice().GetAndroididmd5(),
			Oaid:            xmlyReq.GetDevice().GetOaid(),
			Idfa:            xmlyReq.GetDevice().GetIdfa(),
			Ua:              xmlyReq.GetDevice().GetUa(),
			ScreenWidth:     int(xmlyReq.GetDevice().GetW()),
			ScreenHeight:    int(xmlyReq.GetDevice().GetH()),
			DeviceType:      1,
			IP:              xmlyReq.GetDevice().GetIp(),
			AppStoreVersion: xmlyReq.GetDevice().GetAsVersion(),
			HMSCoreVersion:  xmlyReq.GetDevice().GetHmsVersion(),
			BootMark:        xmlyReq.GetDevice().GetBootMark(),
			UpdateMark:      xmlyReq.GetDevice().GetUpdateMark(),
			CAIDMulti:       caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// fmt.Println("cccccc")
	// fmt.Println(reqStu)
	// fmt.Println("dddddd")

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	// fmt.Println("xmly 0")
	// fmt.Println(respStu)
	// fmt.Println("xmly 1")
	// fmt.Println(mhResp.Ret)
	// fmt.Println("xmly 2")

	// fmt.Println(mhResp.Data)
	// fmt.Println("xmly 3")

	if mhResp.Ret != 0 {
		return xmlyNoBidReturn(xmlyReq.GetId(), "no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return xmlyNoBidReturn(xmlyReq.GetId(), "no fill")
	}
	//////////////////////////////////////////////////////////////////////////////////////////////
	xmlyRespSeatBid := xmly.BidResponse_SeatBid{}

	for i := 0; i < len(reqOKImps); i++ {
		if i > mhRespCount-1 {
			continue
		}
		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]
		xmlyReqSlotItem := reqOKImps[i]

		ecpm := reqRtbConfig.Price
		if mhDataItem.Ecpm > 0 {
			ecpm = mhDataItem.Ecpm
		}

		xmlyRespBid := xmly.BidResponse_Bid{
			Id:    proto.String("bid_" + utils.ConvertInt64ToString(utils.GetCurrentSecond())),
			Impid: proto.String(xmlyReqSlotItem.GetId()),
			Price: proto.Float32(float32(ecpm)),
		}

		// winurl
		winUrl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__SETTLE_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// crid
		xmlyRespBid.Crid = proto.String(utils.GetMd5(mhDataItem.Title))
		if len(mhDataItem.Crid) > 0 {
			xmlyRespBid.Crid = proto.String(mhDataItem.Crid)
		}

		// fmt.Println("xxx")
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// 样式id
		destStyleID := reqRtbConfig.ImageStyleID
		if isVideoType == 1 {
			destStyleID = reqRtbConfig.VideoStyleID
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}
		impTrackArray = append(impTrackArray, winUrl)
		xmlyRespBid.Monitorurls = impTrackArray

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(int(xmlyReq.GetDevice().GetW())), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(int(xmlyReq.GetDevice().GetH())), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}
		xmlyRespBid.Clickmonitorurls = clkTrackArray

		// video start finish link
		// var videoStartTrackArray []string
		// var videoFinishTrackArray []string
		// for _, trackItem := range mhDataItem.Video.EventTracks {
		// 	if trackItem.EventType == 100 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
		// 		}
		// 	} else if trackItem.EventType == 103 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
		// 		}
		// 	}
		// }

		// interact type
		// xmlyRespBid.Clickurl = proto.String(mhDataItem.AdURL)
		if mhDataItem.InteractType == 0 {
			xmlyRespBid.Clicktype = proto.Int32(0)
			xmlyRespBid.Clickurl = proto.String(mhDataItem.LandpageURL)
		} else if mhDataItem.InteractType == 1 {
			xmlyRespBid.Clicktype = proto.Int32(2)
			xmlyRespBid.Clickurl = proto.String(mhDataItem.DownloadURL)
		}

		if reqOs == "ios" {
			xmlyRespBid.Clicktype = proto.Int32(0)
		}

		// app info
		xmlyRespBidAppInfo := xmly.BidResponse_Bid_AppInfo{}
		if len(mhDataItem.DeepLink) > 0 {
			xmlyRespBid.Clicktype = proto.Int32(2)
			xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.DeepLink)
		}

		if len(mhDataItem.AppName) > 0 {
			xmlyRespBidAppInfo.Appname = proto.String(mhDataItem.AppName)
		}

		if len(mhDataItem.PackageName) > 0 {
			xmlyRespBidAppInfo.Pkgname = proto.String(mhDataItem.PackageName)
		}

		// deeplink tracking
		if len(mhDataItem.DeepLink) > 0 {
			var deepLinkTrackOKArray []string
			var deepLinkTrackFailedArray []string
			for _, trackItem := range mhDataItem.ConvTracks {
				if trackItem.ConvType == 10 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
					}
				} else if trackItem.ConvType == 11 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
					}
				}
			}
			if len(deepLinkTrackOKArray) > 0 {
				xmlyRespBidAppInfo.Evokemonitorurl = deepLinkTrackOKArray
			}

			if len(deepLinkTrackFailedArray) > 0 {
				xmlyRespBidAppInfo.Evokefailmonitorurl = deepLinkTrackFailedArray
			}
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.AppName) > 0 || len(mhDataItem.PackageName) > 0 {
			xmlyRespBid.Appinfo = &xmlyRespBidAppInfo
		}

		if len(xmlyReqSlotItem.GetStyle()) == 0 {
			continue
		}

		// styles
		reqStyle := xmlyReqSlotItem.GetStyle()[0]
		reqStyleID := xmlyReqSlotItem.GetStyle()[0].GetStyle()
		xmlyRespBid.Style = &reqStyleID

		// nativie, assets
		reqAssets := reqStyle.GetNative().GetAssets()
		if len(reqAssets) == 0 {
			continue
		}

		xmlyRespBidNative := xmly.BidResponse_Bid_Native{}
		for _, assetsItem := range reqAssets {
			// fmt.Println("asset 0 ")
			// fmt.Println(assetsItem.GetText())
			// fmt.Println(assetsItem.GetAudit())
			// fmt.Println(assetsItem.GetImage())
			// fmt.Println(assetsItem.GetVideo())
			// fmt.Println("asset 1 ")
			if assetsItem.GetText() != nil {
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				if assetsItem.GetText().GetLen() > 35 {
					xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.Description)
				} else if assetsItem.GetText().GetLen() > 15 {
					xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.Title)
				} else {
					xmlyRespBidNativeAssetText.V = proto.String("查看详情")
				}
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			if assetsItem.GetImage() != nil {
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetImage := xmly.BidResponse_Bid_Native_Asset_Image{}
				if assetsItem.GetImage().GetW() > 500 || assetsItem.GetImage().GetH() > 500 {
					if isVideoType == 1 {
						xmlyRespBidNativeAssetImage.Url = proto.String(mhDataItem.Video.CoverURL)
					} else {
						xmlyRespBidNativeAssetImage.Url = proto.String(mhDataItem.Image[0].URL)
						xmlyRespBidNativeAssetImage.W = proto.Int32(int32(mhDataItem.Image[0].Width))
						xmlyRespBidNativeAssetImage.H = proto.Int32(int32(mhDataItem.Image[0].Height))
					}
				} else {
					xmlyRespBidNativeAssetImage.Url = proto.String(mhDataItem.IconURL)
				}
				xmlyRespBidNativeAsset.Image = &xmlyRespBidNativeAssetImage

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}

			if assetsItem.GetVideo() != nil {
				if isVideoType == 1 {
					xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
					xmlyRespBidNativeAssetVideo := xmly.BidResponse_Bid_Native_Asset_Video{}
					xmlyRespBidNativeAssetVideo.Url = proto.String(mhDataItem.Video.VideoURL)
					xmlyRespBidNativeAssetVideo.W = proto.Int32(int32(mhDataItem.Video.Width))
					xmlyRespBidNativeAssetVideo.H = proto.Int32(int32(mhDataItem.Video.Height))
					xmlyRespBidNativeAsset.Video = &xmlyRespBidNativeAssetVideo

					xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
				}
			}
		}

		if (reqRtbConfig.OriginTagID == "1463024" && destStyleID == "24201") || (reqRtbConfig.OriginTagID == "1463025" && destStyleID == "24201") {
			isOK := true
			xmlyRespBidNative.Assets = xmlyRespBidNative.Assets[0:0]
			// 下载APP logo图
			if isOK {
				if len(mhDataItem.IconURL) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no icon url")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetImage := xmly.BidResponse_Bid_Native_Asset_Image{}
				xmlyRespBidNativeAssetImage.Url = proto.String(mhDataItem.IconURL)
				xmlyRespBidNativeAsset.Image = &xmlyRespBidNativeAssetImage

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP名称
			if isOK {
				if len(mhDataItem.AppName) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app name")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.AppName)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP描述
			if isOK {
				if len(mhDataItem.Description) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app description")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.Description)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP包名
			if isOK {
				if len(mhDataItem.PackageName) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app bundle")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.PackageName)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP版本号
			if isOK {
				if len(mhDataItem.AppVersion) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app bundle")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.AppVersion)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP包大小，单位KB，MB，GB
			if isOK {
				if mhDataItem.PackageSize == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app size")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(utils.ConvertInt64ToString(mhDataItem.PackageSize/1024/1024) + "M")
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP开发者
			if isOK {
				if len(mhDataItem.Publisher) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app publisher")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.Publisher)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
			// 下载APP隐私政策链接
			if isOK {
				if len(mhDataItem.PrivacyLink) == 0 {
					return xmlyNoBidReturn(xmlyReq.GetId(), "no app privacy link")
				}
				xmlyRespBidNativeAsset := xmly.BidResponse_Bid_Native_Asset{}
				xmlyRespBidNativeAssetText := xmly.BidResponse_Bid_Native_Asset_Text{}
				xmlyRespBidNativeAssetText.V = proto.String(mhDataItem.PrivacyLink)
				xmlyRespBidNativeAsset.Text = &xmlyRespBidNativeAssetText

				xmlyRespBidNative.Assets = append(xmlyRespBidNative.Assets, &xmlyRespBidNativeAsset)
			}
		}
		xmlyRespBid.Native = &xmlyRespBidNative

		xmlyRespSeatBid.Bid = append(xmlyRespSeatBid.Bid, &xmlyRespBid)
	}
	if len(xmlyRespSeatBid.GetBid()) == 0 {
		return xmlyNoBidReturn(xmlyReq.GetId(), "no fill")
	}

	xmlyResp := &xmly.BidResponse{
		Id: proto.String(xmlyReq.GetId()),
	}
	xmlyResp.Seatbid = append(xmlyResp.Seatbid, &xmlyRespSeatBid)

	// fmt.Println("1")
	// fmt.Println(xmlyResp)
	// fmt.Println("2")

	return xmlyResp
}

func xmlyNoBidReturn(reqID string, reason string) *xmly.BidResponse {
	// fmt.Println(reason)

	xmlyNoResp := &xmly.BidResponse{
		Id: proto.String(reqID),
	}
	return xmlyNoResp
}
