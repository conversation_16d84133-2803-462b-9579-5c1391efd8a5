package rtb_honor

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"google.golang.org/protobuf/proto"
	"io"
	"mh_proxy/pb/honor"
	"net/http"
	"testing"
)

func TestHandlePrice(t *testing.T) {
	price := "vBjlu%2FbL2ZgFVpgz%3AGaQfkBiKjXqmSEST35lybfH4d1gY"
	secretKey := "fjgP5gf5bv4yfjCDA4Lg0IC1ScCeMbpiUk5qSJbOnwU="
	decodePrice, err := Decrypt(price, secretKey)
	fmt.Println(decodePrice)
	fmt.Println(err)

}

func TestHandleHonor(t *testing.T) {
	var imps []*honor.BidRequest_Imp

	imp := &honor.BidRequest_Imp{
		Id:       "0672f9958d724c7d89da9af0f3363ef3",
		Tagid:    "1706743160619663360",
		Bidfloor: 13.89,
		Count:    1,
		Ext: &honor.BidRequest_ImpExt{
			AdType:     3,
			TemplateId: []int32{10},
		},
	}

	imps = append(imps, imp)
	request := &honor.BidRequest{
		Id:  "9f09245f204d42eeae370d750e90b973",
		Imp: imps,
		App: &honor.BidRequest_App{
			Id:     "1839130042731331584",
			Name:   "WiFi万能钥匙",
			Bundle: "com.snda.wifilocating",
		},
		Device: &honor.BidRequest_Device{
			Ua:             "Mozilla/5.0 (Linux; Android 12; NTH-AN00 Build/HONORNTH-AN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36",
			Ip:             "************",
			Devicetype:     1,
			Make:           "HONOR",
			Model:          "MGI-AN00",
			Os:             "android",
			Osv:            "12",
			H:              2340,
			W:              1080,
			Language:       "en",
			Connectiontype: 2,
			Ifa:            "340e9145-6869-4d20-94cd-282a4d228fd8",
			Deviceflag:     5,
		},
		Tmax: 200,
	}

	reqPbByte, _ := proto.Marshal(request)

	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(reqPbByte)
	if err != nil {
		_ = gzipWriter.Close()
		fmt.Println(err)
	}
	if err = gzipWriter.Close(); err != nil {
		fmt.Println(err)
	}

	client := &http.Client{}
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/request?channel=64", bytes.NewReader(buf.Bytes()))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")
	requestPost.Header.Add("Accept-Encoding", "gzip,deflate,br")
	requestPost.Header.Add("Content-Encoding", "gzip")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	ksResp := &honor.BidResponse{}
	err = proto.Unmarshal(bodyContent, ksResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(ksResp)
	fmt.Println(string(marshal))
}
