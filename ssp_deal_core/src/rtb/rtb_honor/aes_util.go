package rtb_honor

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"
)

func Decrypt(data, secretKey string) (string, error) {
	// 查找分隔符位置
	if !strings.Contains(data, ":") {
		data, _ = url.QueryUnescape(data)
	}
	index := strings.Index(data, ":")
	if index == -1 {
		return "", fmt.Errorf("invalid data format: missing ':'")
	}
	// 提取 IV 和加密数据
	ivStr := data[:index]
	encryptData := data[index+1:]

	// 解码 base64 数据
	encrypted, err := base64.StdEncoding.DecodeString(encryptData)
	if err != nil {
		return "", fmt.Errorf("base64 decode error: %w", err)
	}
	iv, err := base64.StdEncoding.DecodeString(ivStr)
	if err != nil {
		return "", fmt.Errorf("base64 decode error: %w", err)
	}
	// 创建 AES 块
	secretKeyDecode, err := base64.StdEncoding.DecodeString(secretKey)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(secretKeyDecode)
	if err != nil {
		return "", fmt.Errorf("aes new cipher error: %w", err)
	}
	// 创建 GCM 模式
	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("new gcm error: %w", err)
	}
	// 解密数据
	original, err := aesgcm.Open(nil, iv, encrypted, nil)
	if err != nil {
		return "", fmt.Errorf("decrypt error: %w", err)
	}
	// 去除字符串首尾空格
	originalString := string(bytes.TrimSpace(original))
	return originalString, nil
}
