package rtb_honor

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/honor"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleHonor(c *gin.Context, channel string) (*honor.BidResponse, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()

	// 判断是否开启gzip
	acceptEncoding := c.GetHeader("Accept-Encoding")
	contentEncoding := c.<PERSON>eader("Content-Encoding")

	if strings.Contains(acceptEncoding, "gzip") || strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	req := &honor.BidRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetDevice().GetConnectiontype() {
	case 2:
		connectType = 1
	case 4:
		connectType = 2
	case 5:
		connectType = 3
	case 6:
		connectType = 4
	case 7:
		connectType = 5
	default:
		connectType = 0
	}
	carrier := 0
	deviceOs := strings.ToLower(req.GetDevice().GetOs())

	var adsCount int
	var keyword string
	var resultfulImp []*honor.BidRequest_Imp
	var configList []models.RtbConfigByTagIDStu

	for _, imp := range req.GetImp() {
		price := 0
		adsCount = int(imp.GetCount())
		keyword = imp.GetKeywords()

		var styleIds []string
		if imp.GetBidfloor() > 0 {
			price = int(imp.GetBidfloor() * 100)
		}
		for _, templateId := range imp.GetExt().GetTemplateId() {
			styleIds = append(styleIds, strconv.Itoa(int(templateId)))
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.GetTagid(), deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.GetApp().GetBundle()) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.GetApp().GetBundle() {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	reqRtbConfig := configList[0]

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppName:     req.GetApp().GetName(),
			AppBundleID: req.GetApp().GetBundle(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
			Query:       keyword,
		},
		Device: models.MHReqDevice{
			Os:           deviceOs,
			OsVersion:    req.GetDevice().GetOsv(),
			Ua:           req.GetDevice().GetUa(),
			IP:           req.GetDevice().GetIp(),
			Manufacturer: req.GetDevice().GetMake(),
			Model:        req.GetDevice().GetModel(),
			ScreenWidth:  int(req.GetDevice().GetW()),
			ScreenHeight: int(req.GetDevice().GetH()),
			Language:     req.GetDevice().GetLanguage(),
			Oaid:         req.GetDevice().GetIfa(),
			DeviceType:   1,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var impItem *honor.BidRequest_Imp
	for _, imp := range resultfulImp {
		if imp.GetTagid() == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var bids []*honor.BidResponse_Bid
	var seatBids []*honor.BidResponse_SeatBid

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		priceStr := fmt.Sprintf("%.2f", float64(mhDataItem.Ecpm)/100)
		price, _ := strconv.ParseFloat(priceStr, 64)

		bidEventTracker := &honor.BidResponse_EventTracker{
			ImpressionUrls: mhDataItem.ImpressionLink,
			ClickUrls:      mhDataItem.ClickLink,
		}

		var deeplink string
		var buttonText string
		var officialWebsite string
		var targetUrl string
		var promotionPurpose int
		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					deeplink = mhDataItem.MarketURL
				}
			}
			promotionPurpose = 1
			buttonText = "查看详情"
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					deeplink = mhDataItem.DeepLink
				}
			}

			promotionPurpose = 0
			if len(mhDataItem.PackageName) > 0 {
				officialWebsite = "https://sj.qq.com/appdetail/" + mhDataItem.PackageName
			}

			buttonText = "立即下载"
			targetUrl = mhDataItem.LandpageURL
		}

		if len(deeplink) > 0 {
			promotionPurpose = 2
		}

		var icon honor.BidResponse_ImageObject
		if len(mhDataItem.IconURL) > 0 {
			icon.Url = mhDataItem.IconURL
			icon.W = 200
			icon.H = 200
			icon.ImageType = "jpg"
		}

		var (
			brandName  string
			categoryId string
		)

		if value, ok := honorDataMap[mhDataItem.PackageName]; ok {
			brandName = value.BrandName
			categoryId = value.CategoryId
		} else {
			categoryId = "815"
			if len(mhDataItem.AppName) > 0 {
				for idx, char := range []rune(mhDataItem.AppName) {
					if idx > 9 {
						continue
					}
					brandName = brandName + fmt.Sprintf("%c", char)
				}
			} else {
				brandName = "Maplehaze"
			}
		}

		bidCreative := &honor.BidResponse_ExtCreative{
			PkgName:             mhDataItem.PackageName,
			OfficialWebsite:     officialWebsite,
			Deeplink:            deeplink,
			H5Link:              mhDataItem.LandpageURL,
			AppName:             mhDataItem.AppName,
			DownloadUrl:         mhDataItem.DownloadURL,
			PkgSize:             mhDataItem.PackageSize,
			PkgVersion:          mhDataItem.AppVersion,
			Title:               truncateString(mhDataItem.Title),
			Description:         mhDataItem.Description,
			TargetUrl:           targetUrl,
			DeveloperName:       mhDataItem.Publisher,
			PermissionsUrl:      mhDataItem.PermissionURL,
			PrivacyAgreementUrl: mhDataItem.PrivacyLink,
			PromotionPurpose:    int32(promotionPurpose),
			LogoInfo:            &icon,
			IconInfo:            &icon,
			ButtonText:          buttonText,
			BrandName:           brandName,
		}

		bidExt := &honor.BidResponse_BidExt{
			EventTracker: bidEventTracker,
			ExtCreative:  bidCreative,
		}

		var (
			iurl      string
			imageType string
			w         int32
			h         int32
		)

		switch mhDataItem.CrtType {
		case 11:
			if len(reqRtbConfig.ImageStyleID) == 0 || mhDataItem.Image == nil {
				continue
			}
			var imgList []*honor.BidResponse_ImageObject
			for _, imageItem := range mhDataItem.Image {
				img := honor.BidResponse_ImageObject{
					W:         int32(imageItem.Width),
					H:         int32(imageItem.Height),
					Url:       imageItem.URL,
					ImageType: "jpg",
				}
				iurl = imageItem.URL
				imageType = "jpg"
				w = int32(imageItem.Width)
				h = int32(imageItem.Height)
				imgList = append(imgList, &img)
			}
			bidCreative.ImageList = imgList
			bidCreative.TemplateId = reqRtbConfig.ImageStyleID
		case 20:
			if len(reqRtbConfig.VideoStyleID) == 0 || mhDataItem.Video == nil {
				continue
			}
			videoCover := &honor.BidResponse_ImageObject{
				Url:       mhDataItem.Video.CoverURL,
				W:         int32(mhDataItem.Video.Width),
				H:         int32(mhDataItem.Video.Height),
				ImageType: "jpg",
			}

			video := &honor.BidResponse_VideoObject{
				W:             int32(mhDataItem.Video.Width),
				H:             int32(mhDataItem.Video.Height),
				VideoDuration: int32(mhDataItem.Video.Duration / 1000),
				Url:           mhDataItem.Video.VideoURL,
				VideoType:     "mp4",
				Size:          int32(5 * 1014 * 1024),
				TitleScreen:   videoCover,
			}
			bidCreative.Video = video
			bidCreative.TemplateId = reqRtbConfig.VideoStyleID
		}

		bid := honor.BidResponse_Bid{
			Id:       uuid.NewV4().String(),
			Impid:    impItem.GetId(),
			Price:    float32(price),
			Nurl:     winURL,
			Lurl:     proto.String(mhDataItem.LossNoticeURL),
			Crid:     mhDataItem.Crid,
			Cat:      []string{categoryId},
			Language: "zh",
			Ext:      bidExt,
		}

		if len(iurl) > 0 {
			bid.Iurl = iurl
			bid.ImageType = imageType
			bid.W = w
			bid.H = h
		}
		bids = append(bids, &bid)
	}

	if len(bids) == 0 {
		return &honor.BidResponse{
			Id:    req.GetId(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	seatBid := honor.BidResponse_SeatBid{
		Bid: bids,
	}
	seatBids = append(seatBids, &seatBid)

	resp := &honor.BidResponse{
		Id:      req.GetId(),
		Bidid:   bigdataUID,
		Seatbid: seatBids,
		Cur:     "CNY",
	}

	return resp, http.StatusOK
}

func truncateString(s string) string {
	var tmp string
	i := 1
	for index, char := range []rune(s) {
		if index > 20 {
			continue
		}
		tmp = tmp + fmt.Sprintf("%c", char)
		i++
	}

	if i <= 6 {
		tmp = tmp + "..."
	}
	return tmp
}

type HonorModel struct {
	CategoryId string
	BrandName  string
}

var honorDataMap = map[string]HonorModel{
	"com.taobao.taobao":        {BrandName: "淘宝", CategoryId: "832"},
	"com.taobao.taobao4iphone": {BrandName: "淘宝", CategoryId: "832"},
	"com.jingdong.app.mall":    {BrandName: "京东", CategoryId: "832"},
	"com.360buy.jdmobile":      {BrandName: "京东", CategoryId: "832"},
	"com.xunmeng.pinduoduo":    {BrandName: "拼多多", CategoryId: "832"},
	"com.tmall.wireless":       {BrandName: "天猫", CategoryId: "832"},
	"com.taobao.tmall":         {BrandName: "天猫", CategoryId: "832"},
	"com.achievo.vipshop":      {BrandName: "唯品会", CategoryId: "832"},
	"com.vipshop.iphone":       {BrandName: "唯品会", CategoryId: "832"},
	"com.shizhuang.duapp":      {BrandName: "得物", CategoryId: "832"},
	"com.taobao.idlefish":      {BrandName: "闲鱼", CategoryId: "832"},
	"com.taobao.fleamarket":    {BrandName: "闲鱼", CategoryId: "832"},
}
