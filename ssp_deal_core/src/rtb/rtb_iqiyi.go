package rtb

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/iqiyi_down"
	"mh_proxy/utils"
	"mh_proxy/vast"
	"net/url"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByIQiYi ...
func HandleByIQiYi(c *gin.Context, channel string) (*iqiyi_down.BidResponse, int) {
	beginTime := utils.GetCurrentMilliSecond()

	bodyContent, err := c.GetRawData()
	if err != nil {
		fmt.Println(err)
		return iqiyiNoBidReturn("error", "get raw data error")
	}

	iqiyiReq := &iqiyi_down.BidRequest{}
	err = proto.Unmarshal(bodyContent, iqiyiReq)
	if err != nil {
		fmt.Println(err)
		return iqiyiNoBidReturn("error", "parser error")
	}

	// if rand.Intn(10000) == 0 {
	// 	tmpReqByte, _ := json.Marshal(iqiyiReq)
	// 	go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"&iqiyi&req", string(tmpReqByte), "", "", "", "")
	// }

	// tmpReqByte, _ := json.Marshal(iqiyiReq)
	// fmt.Println("iqiyi req:", string(tmpReqByte))
	// fmt.Println(iqiyiReq)
	// fmt.Println(iqiyiReq.GetApiVersion())
	// fmt.Println(iqiyiReq.GetDevice().GetOs())
	// fmt.Println(iqiyiReq.GetDevice().GetIdfa())
	// fmt.Println(iqiyiReq.GetDevice().GetUa())
	// fmt.Println(iqiyiReq.GetDevice().GetIp())

	// debug
	// iqiyiReq.GetDevice().Model = proto.String("M2103K19C")
	// iqiyiReq.GetDevice().Os = proto.String("android")
	// iqiyiReq.GetDevice().OsVersion = proto.String("10.0")

	reqOs := ""
	if strings.Contains(strings.ToLower(iqiyiReq.GetDevice().GetOs()), "android") {
		reqOs = "android"
	} else if strings.Contains(strings.ToLower(iqiyiReq.GetDevice().GetOs()), "ios") {
		reqOs = "ios"
	} else {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "wrong os")
	}

	reqDeivceMake := iqiyiReq.GetDevice().GetManufacturer()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqConnectType := 1
	// Return the detected connection type for the device.
	// 0 Unknown
	// 1 Ethernet
	// 2 Wifi
	// 3 Cellular data - 2G
	// 4 Cellular data - 3G
	// 5 Cellular data - 4G
	// 6 Cellular data - 5G
	if iqiyiReq.GetDevice().GetConnectionType() == 0 {
		reqConnectType = 0
	} else if iqiyiReq.GetDevice().GetConnectionType() == 1 {
		reqConnectType = 0
	} else if iqiyiReq.GetDevice().GetConnectionType() == 2 {
		reqConnectType = 1
	} else if iqiyiReq.GetDevice().GetConnectionType() == 3 {
		reqConnectType = 2
	} else if iqiyiReq.GetDevice().GetConnectionType() == 4 {
		reqConnectType = 3
	} else if iqiyiReq.GetDevice().GetConnectionType() == 5 {
		reqConnectType = 4
	} else if iqiyiReq.GetDevice().GetConnectionType() == 6 {
		reqConnectType = 7
	} else {
		reqConnectType = 1
	}

	reqCarrier := 0
	// 运营商 0，移动；1，联通； 2，电信；3，其它
	if strings.Contains(iqiyiReq.GetDevice().GetCarrierName(), "移动") {
		reqCarrier = 1
	} else if strings.Contains(iqiyiReq.GetDevice().GetCarrierName(), "联通") {
		reqCarrier = 2
	} else if strings.Contains(iqiyiReq.GetDevice().GetCarrierName(), "电信") {
		reqCarrier = 3
	} else {
		reqCarrier = 0
	}

	reqOsv := iqiyiReq.GetDevice().GetOsVersion()
	if len(reqOsv) > 0 {
	} else {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "wrong osv")
	}
	reqImps := iqiyiReq.GetImp()
	// fmt.Println(len(reqSlots))
	if len(reqImps) == 0 {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "wrong slots")
	}

	var reqOKImps []*iqiyi_down.Impression
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqImps {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := ""

		// 视频判定tagid_type, 判定贴片是否5s, 如果是, 自定义tagid_type=99
		reqTagIDType := "0" // 1 前贴, 2 中贴, 3 后贴
		reqIsVideo := false
		var templateIds []string
		if item.GetBanner().GetAdZoneId() != 0 {
			reqTagID = utils.ConvertInt64ToString(item.GetBanner().GetAdZoneId())

			for _, templateItem := range item.GetBanner().GetCreativeTemplate() {
				templateIds = append(templateIds, utils.ConvertInt64ToString(templateItem))
			}
		} else if item.GetVideo().GetAdZoneId() != 0 {
			reqTagID = utils.ConvertInt64ToString(item.GetVideo().GetAdZoneId())

			reqIsVideo = true
			reqTagIDType = utils.ConvertIntToString(int(item.GetVideo().GetAdType()))
			if reqTagIDType == "1" || reqTagIDType == "2" || reqTagIDType == "3" {
				if item.GetVideo().GetMinduration() <= 5 && item.GetExtendedAdsPosition() != 0 {
					reqTagIDType = "99"
				}
				if item.GetMaxSkippableRollAds() >= 1 {
					reqTagIDType = "98"
				}
			}
		}

		// fmt.Println("kbg_debug banner tagid:", reqTagID, item.GetBanner().GetAdZoneId())
		// fmt.Println("kbg_debug video tagid:", reqTagID, item.GetVideo().GetAdZoneId())

		if len(reqTagID) == 0 {
			continue
		}

		reqPrice := item.GetBidfloor()
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return iqiyiNoBidReturn(iqiyiReq.GetId(), "not active")
		// }

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		var rtbConfigArrayByTagID *[]models.RtbConfigByTagIDStu

		if reqIsVideo {
			rtbConfigArrayByTagID = models.GetAdxInfoByRtbTagIDAndTagIDType(c, channel, reqTagID, reqTagIDType, reqOs, []string{}, "", int(reqPrice))
		} else {
			rtbConfigArrayByTagID = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, reqTagID, reqOs, templateIds, "", int(reqPrice))
		}

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return iqiyiNoBidReturn(iqiyiReq.GetId(), "not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqRtbConfig.ExtraTag = reqRtbConfig.AuditAdType + "," + reqRtbConfig.AuditImageStyleID + "," + reqRtbConfig.AuditVideoStyleID + "," + reqTagIDType + "," + reqRtbConfig.AuditPlatform
		reqRtbConfig.TagIDType = reqTagIDType
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "wrong imp")
	}

	// 2022-10-31 爱奇艺：
	// 1、UA修正：model首字母包含Mozilla、Dalvik映射到UA，非以上规则爱奇艺ua置空，补充标准ua
	// 2、Model修正：大于20个字符model置空，根据设备号补充model，找不到则直接过滤
	reqModel := iqiyiReq.GetDevice().GetModel()
	reqUa := ""
	if strings.HasPrefix(iqiyiReq.GetDevice().GetUa(), "Mozilla") || strings.HasPrefix(iqiyiReq.GetDevice().GetUa(), "Dalvik") {
		reqUa = iqiyiReq.GetDevice().GetUa()
	} else if strings.HasPrefix(reqModel, "Mozilla") || strings.HasPrefix(reqModel, "Dalvik") {
		reqUa = reqModel
	}
	if len(reqModel) > 20 {
		reqModel = ""
	}

	reqIP := iqiyiReq.GetDevice().GetIp()
	if len(reqIP) == 0 {
		reqIP = iqiyiReq.GetDevice().GetIpv6()
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(iqiyiReq.GetDevice().GetCaidInfo().GetCaid()) > 0 {
		for _, item := range iqiyiReq.GetDevice().GetCaidInfo().GetCaid() {
			var tmpCaid models.MHReqCAIDMulti
			tmpCaid.CAID = item.GetCaid()
			tmpCaid.CAIDVersion = item.GetVersion()
			caidMultiList = append(caidMultiList, tmpCaid)
		}
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: "",
			AppName:     "",
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: len(reqOKImps),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 reqOs,
			OsVersion:          reqOsv,
			Model:              reqModel,
			Manufacturer:       reqDeivceMake,
			ImeiMd5:            iqiyiReq.GetDevice().GetImei(),
			AndroidIDMd5:       iqiyiReq.GetDevice().GetAndroidId(),
			Oaid:               iqiyiReq.GetDevice().GetOaid(),
			Idfa:               iqiyiReq.GetDevice().GetIdfa(),
			Ua:                 reqUa,
			ScreenWidth:        int(iqiyiReq.GetDevice().GetScreenWidth()),
			ScreenHeight:       int(iqiyiReq.GetDevice().GetScreenHeight()),
			DeviceType:         1,
			IP:                 reqIP,
			DeviceStartSec:     utils.ConvertInt64ToString(iqiyiReq.GetDevice().GetStartupTime()),
			Country:            "CN",
			Language:           "zh-Hans-CN",
			DeviceNameMd5:      "", // TODO
			HardwareMachine:    "", // TODO
			HardwareModel:      "", // TODO
			PhysicalMemoryByte: utils.ConvertInt64ToString(iqiyiReq.GetDevice().GetMemTotal()),
			HarddiskSizeByte:   utils.ConvertInt64ToString(iqiyiReq.GetDevice().GetDiskTotal()),
			SystemUpdateSec:    utils.ConvertInt64ToString(iqiyiReq.GetDevice().GetOsUpdateTime()),
			TimeZone:           iqiyiReq.GetDevice().GetLocalTimezone(),
			CPUNum:             utils.ConvertIntToString(int(iqiyiReq.GetDevice().GetCpuNum())),
			AppStoreVersion:    "",
			HMSCoreVersion:     "",
			BootMark:           iqiyiReq.Device.GetBootMark(),
			UpdateMark:         iqiyiReq.Device.GetUpdateMark(),
			CAIDMulti:          caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// if len(iqiyiReq.GetDevice().GetCaidInfo().GetCaid()) > 0 {
	// 	reqStu.Device.CAID = iqiyiReq.GetDevice().GetCaidInfo().GetCaid()[0].GetCaid()
	// 	reqStu.Device.CAIDVersion = iqiyiReq.GetDevice().GetCaidInfo().GetCaid()[0].GetVersion()
	// }
	// iqiyi临时过滤caid verision 20230330
	// if len(iqiyiReq.GetDevice().GetCaidInfo().GetCaid()) > 0 {
	// 	for _, item := range iqiyiReq.GetDevice().GetCaidInfo().GetCaid() {
	// 		if item.GetVersion() != "20230330" {
	// 			reqStu.Device.CAID = item.GetCaid()
	// 			reqStu.Device.CAIDVersion = item.GetVersion()

	// 			break
	// 		}
	// 	}
	// }

	// fmt.Println("cccccc")
	// fmt.Println(reqStu)
	// fmt.Println("dddddd")

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	// fmt.Println("iqiyi 0")
	// fmt.Println(respStu)
	// fmt.Println("iqiyi 1")

	// fmt.Println(mhResp.Ret)
	// fmt.Println("iqiyi 2")

	// fmt.Println(mhResp.Data)
	// fmt.Println("iqiyi 3")

	if mhResp.Ret != 0 {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "no fill")
	}

	// fmt.Println("kbg_debug tag_id:", reqRtbConfig.TagID)
	// fmt.Println("kbg_debug origin_tag_id:", reqRtbConfig.OriginTagID)
	// fmt.Println("kbg_debug style:", reqRtbConfig.Style)
	// fmt.Println("kbg_debug app_id:", reqRtbConfig.LocalAppID)
	// fmt.Println("kbg_debug pos_id:", reqRtbConfig.LocalPosID)
	// fmt.Println("kbg_debug is pos audit:", reqRtbConfig.IsPosAudit)
	////////////////////////////////////////////////////////////////////////////////////////
	// save to audit
	if reqRtbConfig.IsPosAudit == 1 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("iqiyi save to audit panic:", err)
				}
			}()
			models.BigDataAudit(c, bigdataUID, &reqRtbConfig, &reqStu, mhResp, channel)
		}()
	}
	//////////////////////////////////////////////////////////////////////////////////////////////
	iqiyiRespSeatBid := iqiyi_down.Seatbid{}

	for i := 0; i < len(reqOKImps); i++ {
		if i > mhRespCount-1 {
			continue
		}
		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]
		iqiyiReqSlotItem := reqOKImps[i]

		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		iqiyiRespBid := iqiyi_down.Bid{
			Id:    proto.String("bid_" + utils.ConvertInt64ToString(utils.GetCurrentSecond())),
			Impid: proto.String(iqiyiReqSlotItem.GetId()),
			Price: proto.Int32(int32(ecpm)),
		}

		iqiyiRespBid.AdSource = proto.String(mhDataItem.Publisher)
		iqiyiRespBid.ProductName = proto.String(mhDataItem.AppName)

		// winurl
		winUrl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${SETTLEMENT}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1

			// iqiyi 1,2,3,99 duration > 15s 不到送审模块, 也不填充
			if reqRtbConfig.TagIDType == "1" || reqRtbConfig.TagIDType == "2" || reqRtbConfig.TagIDType == "3" || reqRtbConfig.TagIDType == "99" {
				if mhDataItem.Video.Duration > 15000 {
					continue
				}
			}
		}

		imageURL := ""
		videoURL := ""
		// coverURL := ""
		if isVideoType == 0 {
			imageURL = mhDataItem.Image[0].URL

			isHasJPGSuffixKey := false
			if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
				tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
				for _, item := range tmpArrays {
					if strings.Contains(imageURL, item) {
						isHasJPGSuffixKey = true
					}
				}
			}

			if isHasJPGSuffixKey {
				if strings.HasSuffix(imageURL, ".jpg") || strings.HasSuffix(imageURL, ".jpeg") {
				} else {
					imageURL = imageURL + ".jpg"
				}
			}
		} else {
			videoURL = mhDataItem.Video.VideoURL
			// coverURL = mhDataItem.Video.CoverURL
		}
		iconURL := mhDataItem.IconURL
		if len(iconURL) > 0 {
			isHasJPGSuffixKey := false
			if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
				tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
				for _, item := range tmpArrays {
					if strings.Contains(iconURL, item) {
						isHasJPGSuffixKey = true
					}
				}
			}
			if isHasJPGSuffixKey {
				if strings.HasSuffix(iconURL, ".png") || strings.HasSuffix(iconURL, ".jpg") || strings.HasSuffix(iconURL, ".jpeg") {
				} else {
					iconURL = iconURL + ".png"
				}
			}
		}

		// 爱奇艺触点贴片tagid­_type=98：
		// icon替换规则：
		// 1、dp链接中带有pinduoduo替换：https://static.maplehaze.cn/adimg/iqiyi/icon/pdd-icon-120X120.png
		// 2、dp链接中带有taobao替换：https://static.maplehaze.cn/adimg/iqiyi/icon/taobao-icon-120x120.png
		// 3、dp链接中带有jdmobile、jd.com、openjd替换：https://static.maplehaze.cn/adimg/iqiyi/icon/jd-icon-120X120.png
		// 4、非以上规则，均替换：https://static.maplehaze.cn/adimg/iqiyi/icon/default-icon-120X120.png
		if reqRtbConfig.TagIDType == "98" {
			if strings.Contains(mhDataItem.DeepLink, "pinduoduo") {
				iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/pdd-icon-120X120.png"
			} else if strings.Contains(mhDataItem.DeepLink, "taobao") {
				iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/taobao-icon-120x120.png"
			} else if strings.Contains(mhDataItem.DeepLink, "jdmobile") || strings.Contains(mhDataItem.DeepLink, "jd.com") || strings.Contains(mhDataItem.DeepLink, "openjd") {
				iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/jd-icon-120X120.png"
			} else {
				iconURL = "https://static.maplehaze.cn/adimg/iqiyi/icon/default-icon-120X120.png"
			}
		}

		md5Key := ""
		if isVideoType == 1 {
			md5Key = utils.GetMd5(videoURL + iconURL + reqRtbConfig.LocalPosID)
		} else {
			md5Key = utils.GetMd5(imageURL + iconURL + reqRtbConfig.LocalPosID)
		}

		if reqRtbConfig.IsPosAudit == 1 {
			iqiyiTvID := ""

			isOK, tmpIQiYiTvID := models.GetIQiYiByMd5Key(md5Key)
			if isOK != 1 {
				// fmt.Println("continue 0")
				continue
			}
			if len(tmpIQiYiTvID) == 0 {
				continue
			}
			iqiyiTvID = tmpIQiYiTvID

			// crid TODO 即 tv_id,素材审核通过后获取,请务必不要使用 m_id
			if len(iqiyiTvID) == 0 {
				continue
			}
			iqiyiRespBid.Crid = proto.String(iqiyiTvID)
		}

		// 免审
		if reqRtbConfig.IsPosAudit == 0 {

			// creative_id
			iqiyiRespBid.CreativeId = proto.String(mhDataItem.Crid)

			// 返回图片类型, 使用图片模版id
			// 返回视频类型, 使用视频模版id
			creativeContentMap := map[string]interface{}{}
			if isVideoType == 0 {
				iqiyiRespBid.Crid = proto.String(reqRtbConfig.ImageVirtualCrid)

				if reqRtbConfig.ImageStyleID == "*************" {
					creativeContentMap["portrait"] = iconURL
					creativeContentMap["account"] = "枫岚"
					creativeContentMap["title"] = mhDataItem.Title
					creativeContentMap["url"] = imageURL
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					// creativeContentMap["appleId"] = ""
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = iconURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["desc"] = mhDataItem.Description
					creativeContentMap["width"] = mhDataItem.Image[0].Width
					creativeContentMap["height"] = mhDataItem.Image[0].Height
				} else if reqRtbConfig.ImageStyleID == "*************" {
					creativeContentMap["url"] = imageURL
					// creativeContentMap["appleId"] = ""
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = iconURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["title"] = mhDataItem.Title
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
				} else if reqRtbConfig.ImageStyleID == "5000000000150" {
					creativeContentMap["creativeUrl"] = imageURL
					creativeContentMap["advertiser_id"] = 1000
					// creativeContentMap["appleId"] = ""
					creativeContentMap["title"] = mhDataItem.Title
					creativeContentMap["appName"] = mhDataItem.AppName
				} else if reqRtbConfig.ImageStyleID == "*************" {
					creativeContentMap["url"] = imageURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["title"] = mhDataItem.Title
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
					// creativeContentMap["appleId"] = ""
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = iconURL
					// creativeContentMap["subMaterialImgUrl"] = ""
					// creativeContentMap["backgroundImg"] = ""
				} else if reqRtbConfig.ImageStyleID == "5000000000105" || reqRtbConfig.ImageStyleID == "5000000000104" {
					creativeContentMap["url"] = imageURL
					creativeContentMap["newBigPortraitUrl"] = imageURL
					creativeContentMap["newGradeAUrl"] = imageURL
					creativeContentMap["newGradeCUrl"] = imageURL
					creativeContentMap["newGradeEUrl"] = imageURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["displayStyle"] = "0"
					creativeContentMap["slipPost"] = "1"
					// creativeContentMap["appleId"] = ""
					creativeContentMap["title"] = mhDataItem.Title
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = mhDataItem.IconURL
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					creativeContentMap["duration"] = 0
					creativeContentMap["fileSize"] = 10240
					creativeContentMap["desc"] = mhDataItem.Description
					creativeContentMap["width"] = mhDataItem.Image[0].Width
					creativeContentMap["height"] = mhDataItem.Image[0].Height
					creativeContentMap["type"] = 0
				} else {
					// 2025-04-15 媒体不传模版id, 默认开屏
					creativeContentMap["url"] = imageURL
					creativeContentMap["newBigPortraitUrl"] = imageURL
					creativeContentMap["newGradeAUrl"] = imageURL
					creativeContentMap["newGradeCUrl"] = imageURL
					creativeContentMap["newGradeEUrl"] = imageURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["displayStyle"] = "0"
					creativeContentMap["slipPost"] = "1"
					// creativeContentMap["appleId"] = ""
					creativeContentMap["title"] = mhDataItem.Title
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = mhDataItem.IconURL
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					creativeContentMap["duration"] = 0
					creativeContentMap["fileSize"] = 10240
					creativeContentMap["desc"] = mhDataItem.Description
					creativeContentMap["width"] = mhDataItem.Image[0].Width
					creativeContentMap["height"] = mhDataItem.Image[0].Height
					creativeContentMap["type"] = 0
				}
			} else {
				iqiyiRespBid.Crid = proto.String(reqRtbConfig.VideoVirtualCrid)

				if reqRtbConfig.VideoStyleID == "5000000000148" {
					creativeContentMap["icon"] = iconURL
					creativeContentMap["title"] = mhDataItem.Title
					creativeContentMap["promotion"] = mhDataItem.Description
					creativeContentMap["streamline"] = "广告"
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
					// creativeContentMap["qipuid"] = ""
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					// creativeContentMap["appleId"] = ""
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = mhDataItem.IconURL
					creativeContentMap["advertiser_id"] = 1000
				} else if reqRtbConfig.VideoStyleID == "*************" {
					creativeContentMap["url"] = videoURL
					creativeContentMap["title"] = mhDataItem.Title
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
					// creativeContentMap["appleId"] = ""
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = iconURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["account"] = "枫岚"
					creativeContentMap["background"] = mhDataItem.Video.CoverURL
					creativeContentMap["gMp4Url"] = videoURL
					// creativeContentMap["bMp4Url"] = ""
					if mhDataItem.Video.Width > mhDataItem.Video.Height {
						creativeContentMap["creative_orientation"] = 1
					} else {
						creativeContentMap["creative_orientation"] = 2
					}
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					creativeContentMap["duration"] = mhDataItem.Video.Duration / 1000
					creativeContentMap["fileSize"] = 10240
					creativeContentMap["desc"] = mhDataItem.Description
					creativeContentMap["width"] = mhDataItem.Video.Width
					creativeContentMap["height"] = mhDataItem.Video.Height

				} else if reqRtbConfig.VideoStyleID == "*************" {
					creativeContentMap["url"] = videoURL
					creativeContentMap["advertiser_id"] = 1000
					creativeContentMap["title"] = mhDataItem.Title
					if mhDataItem.InteractType == 0 {
						creativeContentMap["buttonTitle"] = "了解详情"
					} else {
						creativeContentMap["buttonTitle"] = "立即下载"
					}
					// creativeContentMap["appleId"] = ""
					creativeContentMap["detailPage"] = mhDataItem.LandpageURL
					creativeContentMap["appName"] = mhDataItem.AppName
					creativeContentMap["appIcon"] = mhDataItem.IconURL
					// creativeContentMap["subMaterialImgUrl"] = ""
					// creativeContentMap["backgroundImg"] = ""

				} else {
					continue
				}
			}

			creativeContentJson, _ := json.Marshal(creativeContentMap)
			iqiyiRespBid.CreativeContent = proto.String(string(creativeContentJson))
		}

		// deeplink tracking
		if len(mhDataItem.DeepLink) > 0 {
			// var deepLinkTrackOKArray []string
			// var deepLinkTrackFailedArray []string
			// for _, trackItem := range mhDataItem.ConvTracks {
			// 	if trackItem.ConvType == 10 {
			// 		for _, trackEventItem := range trackItem.ConvURLS {
			// 			deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
			// 		}
			// 	} else if trackItem.ConvType == 11 {
			// 		for _, trackEventItem := range trackItem.ConvURLS {
			// 			deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
			// 		}
			// 	}
			// }
			iqiyiRespBid.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
			if reqOs == "ios" {
				iqiyiRespBid.UniversalLinkUrl = proto.String(mhDataItem.DeepLink)
			}
		}

		if len(mhDataItem.AppstorePackageName) > 0 {
			iqiyiRespBid.ApkName = proto.String(mhDataItem.AppstorePackageName)
			iqiyiRespBid.DownloadFromStore = proto.Bool(true)
		}

		// if strings.HasPrefix(mhDataItem.DeepLink, "leapp") ||
		// 	strings.HasPrefix(mhDataItem.DeepLink, "hwpps") ||
		// 	strings.HasPrefix(mhDataItem.DeepLink, "market") ||
		// 	strings.HasPrefix(mhDataItem.DeepLink, "mimarket") ||
		// 	strings.HasPrefix(mhDataItem.DeepLink, "oppostore") ||
		// 	strings.HasPrefix(mhDataItem.DeepLink, "hap://app") {

		// 	iqiyiRespBid.ApkName = proto.String(mhDataItem.PackageName)
		// 	iqiyiRespBid.DownloadFromStore = proto.Bool(true)
		// }

		if mhDataItem.InteractType == 1 {
			iqiyiRespBid.ApkName = proto.String(mhDataItem.PackageName)
			iqiyiRespBid.DownloadFromStore = proto.Bool(true)
		}

		iqiyiRespBid.WinNoticeUrl = proto.String(winUrl)

		// TODO
		vastStu := vast.VAST{
			Version: "3.0",
		}

		// imp
		var vastImpressionTrackArray []vast.Impression
		for tmpIndex, impItem := range mhDataItem.ImpressionLink {
			tmpImpression := vast.Impression{}
			tmpImpression.ID = utils.ConvertIntToString(tmpIndex)
			tmpImpression.URI = impItem
			vastImpressionTrackArray = append(vastImpressionTrackArray, tmpImpression)
		}

		// clk
		var vastClickTrackArray []vast.VideoClick
		for tmpIndex, clkItem := range mhDataItem.ClickLink {
			tmpClickTrack := vast.VideoClick{}
			tmpClickTrack.ID = utils.ConvertIntToString(tmpIndex)
			tmpClickTrack.URI = clkItem
			vastClickTrackArray = append(vastClickTrackArray, tmpClickTrack)
		}

		// video start, end track
		var vastVideoTrackArray []vast.Tracking
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						tmpTrack := vast.Tracking{}
						tmpTrack.Event = vast.Event_type_start
						tmpTrack.URI = trackEventItem
						vastVideoTrackArray = append(vastVideoTrackArray, tmpTrack)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						tmpTrack := vast.Tracking{}
						tmpTrack.Event = vast.Event_type_complete
						tmpTrack.URI = trackEventItem
						vastVideoTrackArray = append(vastVideoTrackArray, tmpTrack)
					}
				}
			}
		}

		// interact type
		var vastClickThroughArray []vast.VideoClick
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				iqiyiRespBid.DeeplinkUrl = proto.String(mhDataItem.DeepLink)
				if reqOs == "ios" {
					iqiyiRespBid.UniversalLinkUrl = proto.String(mhDataItem.DeepLink)
				}
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					iqiyiRespBid.DeeplinkUrl = proto.String(mhDataItem.MarketURL)
					if reqOs == "ios" {
						iqiyiRespBid.UniversalLinkUrl = proto.String(mhDataItem.MarketURL)
					}
				}
			}

			tmpClickTrough := vast.VideoClick{}
			tmpClickTrough.ID = utils.ConvertIntToString(0)
			tmpClickTrough.URI = mhDataItem.LandpageURL
			if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
				if reqOs == "ios" {
					tmpClickTrough.Type = "15"
				} else {
					tmpClickTrough.Type = "14"
				}
			} else {
				tmpClickTrough.Type = "0"
			}

			vastClickThroughArray = append(vastClickThroughArray, tmpClickTrough)
		} else if mhDataItem.InteractType == 1 {
			tmpClickTrough := vast.VideoClick{}
			tmpClickTrough.ID = utils.ConvertIntToString(0)
			tmpClickTrough.URI = mhDataItem.DownloadURL
			if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
				if reqOs == "ios" {
					tmpClickTrough.Type = "15"
				} else {
					tmpClickTrough.Type = "14"
				}
			} else {
				tmpClickTrough.Type = "4"
			}

			vastClickThroughArray = append(vastClickThroughArray, tmpClickTrough)
		} else {
			continue
		}

		// material
		var vastMediaFileArray []vast.MediaFile

		duration := 0
		if isVideoType == 1 {
			duration = mhDataItem.Video.Duration / 1000

			tmpMediaFile := vast.MediaFile{
				Delivery: "progressive",
				Type:     "video/mp4",
				Width:    mhDataItem.Video.Width,
				Height:   mhDataItem.Video.Height,
				URI:      videoURL,
			}
			vastMediaFileArray = append(vastMediaFileArray, tmpMediaFile)
		} else {
			tmpMediaFile := vast.MediaFile{
				Delivery: "progressive",
				Type:     "image/jpeg",
				Width:    mhDataItem.Image[0].Width,
				Height:   mhDataItem.Image[0].Height,
				URI:      imageURL,
			}
			vastMediaFileArray = append(vastMediaFileArray, tmpMediaFile)
		}

		// 截断为6个字以内
		title := mhDataItem.Title
		if utf8.RuneCountInString(title) > 6 {
			titleRune := []rune(title)
			title = string(titleRune[0:6])
		}

		vastAd := vast.Ad{
			ID: "1",
			InLine: &vast.InLine{
				AdSystem:    &vast.AdSystem{Name: "Maplehaze"},
				AdTitle:     vast.CDATAString{CDATA: title},
				Description: &vast.CDATAString{CDATA: mhDataItem.Description},
				Impressions: vastImpressionTrackArray,
				Creatives: []vast.Creative{
					{
						ID:       mhDataItem.Crid,
						Sequence: 0,
						Linear: &vast.Linear{
							Duration: vast.Duration(duration),
							VideoClicks: &vast.VideoClicks{
								ClickThroughs:  vastClickThroughArray,
								ClickTrackings: vastClickTrackArray,
							},
							TrackingEvents: vastVideoTrackArray,
							MediaFiles:     vastMediaFileArray,
							Icons: &vast.Icons{
								Icon: []vast.Icon{
									{
										StaticResource: &vast.StaticResource{
											URI: iconURL,
										},
									},
								},
							},
						},
					},
				},
			},
		}

		vastStu.Ads = append(vastStu.Ads, vastAd)

		// vast text
		vastXMLText, _ := xml.Marshal(vastStu)
		iqiyiRespBid.Adm = proto.String(string(vastXMLText))

		iqiyiRespSeatBid.Bid = append(iqiyiRespSeatBid.Bid, &iqiyiRespBid)
	}
	if len(iqiyiRespSeatBid.GetBid()) == 0 {
		return iqiyiNoBidReturn(iqiyiReq.GetId(), "no fill")
	}

	iqiyiResp := &iqiyi_down.BidResponse{
		Id:               proto.String(iqiyiReq.GetId()),
		ProcessingTimeMs: proto.Int32(int32(utils.GetCurrentMilliSecond() - beginTime)),
	}
	iqiyiResp.Seatbid = append(iqiyiResp.Seatbid, &iqiyiRespSeatBid)

	// iqiyiByte, _ := json.Marshal(iqiyiResp)
	// fmt.Println("iqiyi resp:", string(iqiyiByte))

	// tmpIQiYiReqByte, _ := json.Marshal(iqiyiReq)
	// tmpIQiYiRespByte, _ := json.Marshal(iqiyiResp)
	// go models.BigDataHoloDebugJson2(bigdataUID+"&iqiyi&log", string(tmpIQiYiReqByte), reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, string(tmpIQiYiRespByte), string(dataByte))

	// if reqRtbConfig.LocalPosID == "58270" {
	// 	tmpIQiYiReqByte, _ := json.Marshal(iqiyiReq)
	// 	tmpIQiYiRespByte, _ := json.Marshal(iqiyiResp)
	// 	go models.BigDataHoloDebugJson2(bigdataUID+"&iqiyi&log", string(tmpIQiYiReqByte), reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, string(tmpIQiYiRespByte), string(dataByte))
	// }

	return iqiyiResp, 200
}

func iqiyiNoBidReturn(reqID string, reason string) (*iqiyi_down.BidResponse, int) {
	// fmt.Println(reason)

	return nil, 204
}
