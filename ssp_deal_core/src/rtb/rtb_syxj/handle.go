package rtb_syxj

import (
	"encoding/json"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"

	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleBySyxj(c *gin.Context, channel string) (*SyxjResponseObject, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()
	req := &SyxjRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}

	var deviceOs string
	switch req.Device.Os {
	case "iOS":
		deviceOs = "ios"
	case "Android":
		deviceOs = "android"
	default:
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.Device.Network {
	case 100:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	case 1:
		connectType = 0
	default:
		connectType = 0
	}

	var carrier int
	switch req.Device.Carrier {
	case 1:
		carrier = 1
	case 3:
		carrier = 2
	case 2:
		carrier = 3
	default:
		carrier = 0
	}

	var resultfulImp []*SyxjRequestImpObject
	var configList []models.RtbConfigByTagIDStu
	for _, imp := range req.Imp {
		price := 0

		if imp.Bidfloor > 0 {
			price = int(imp.Bidfloor)
		}
		if price == 0 {
			for _, dealItem := range imp.Deals {
				price = dealItem.Bidfloor
			}
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagID(c, channel, imp.TagId, deviceOs, "", "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.App.Bundle) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.App.Bundle {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Brand
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.App.Bundle,
			AppName:     req.App.Name,
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     1,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 deviceOs,
			Manufacturer:       manufacturer,
			Model:              req.Device.Model,
			IP:                 req.Device.Ip,
			Ua:                 req.Device.Ua,
			Mac:                req.Device.Mac,
			OsVersion:          req.Device.OsVersion,
			Imei:               req.Device.Imei,
			ImeiMd5:            req.Device.ImeiMd5,
			Oaid:               req.Device.Oaid,
			OaidMd5:            req.Device.OaidMd5,
			Idfa:               req.Device.Idfa,
			IdfaMd5:            req.Device.IdfaMd5,
			ScreenWidth:        req.Device.Width,
			ScreenHeight:       req.Device.Height,
			AndroidID:          req.Device.AndroidId,
			BootMark:           req.Device.BootMark,
			UpdateMark:         req.Device.UpdateMark,
			HMSCoreVersion:     req.Device.VerCodeOfHms,
			DeviceStartSec:     req.Device.BootTimeSec,
			DeviceNameMd5:      req.Device.PhoneName,
			SystemUpdateSec:    req.Device.OsUpdateTimeSec,
			TimeZone:           req.Device.TimeZone,
			PhysicalMemoryByte: strconv.FormatInt(req.Device.MemorySize, 10),
			HarddiskSizeByte:   strconv.FormatInt(req.Device.DiskSize, 10),
			DeviceType:         1,
			AppList:            getSyxjAppList(req.Appendix01, req.InstalledApp),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if req.Device.CaidWithFactors != nil {
		var caidMultiList []models.MHReqCAIDMulti
		if len(req.Device.CaidWithFactors.Caid) > 0 && len(req.Device.CaidWithFactors.CaidVersion) > 0 {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = req.Device.CaidWithFactors.Caid
			caidMulti.CAIDVersion = req.Device.CaidWithFactors.CaidVersion
			caidMultiList = append(caidMultiList, caidMulti)
		}
		reqStu.Device.CAIDMulti = caidMultiList

		if req.Device.CaidWithFactors.Factors != nil {
			reqStu.Device.DeviceStartSec = req.Device.CaidWithFactors.Factors.BootTimeInSec
			reqStu.Device.Country = req.Device.CaidWithFactors.Factors.CountryCode
			reqStu.Device.Language = req.Device.CaidWithFactors.Factors.Language
			reqStu.Device.DeviceNameMd5 = req.Device.CaidWithFactors.Factors.DeviceName
			reqStu.Device.AppStoreVersion = req.Device.CaidWithFactors.Factors.SystemVersion
			reqStu.Device.HardwareMachine = req.Device.CaidWithFactors.Factors.Machine
			reqStu.Device.PhysicalMemoryByte = req.Device.CaidWithFactors.Factors.Memory
			reqStu.Device.HarddiskSizeByte = req.Device.CaidWithFactors.Factors.Disk
			reqStu.Device.SystemUpdateSec = req.Device.CaidWithFactors.Factors.SysFileTime
			reqStu.Device.HardwareModel = req.Device.CaidWithFactors.Factors.Model
			reqStu.Device.TimeZone = req.Device.CaidWithFactors.Factors.TimeZone
			reqStu.Device.DeviceBirthSec = req.Device.CaidWithFactors.Factors.DeviceInitTime
		}
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}

	var impItem *SyxjRequestImpObject
	for _, imp := range resultfulImp {
		if imp.TagId == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &SyxjResponseObject{
			Id:    req.Id,
			BidId: bigdataUID,
		}, http.StatusNoContent
	}

	var bids []*SyxjResponseBidObject
	var seatBids []*SyxjResponseSeatbidObject
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__AUCTION_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		bidItem := SyxjResponseBidItemObject{
			Title:            mhDataItem.Title,
			Desc:             mhDataItem.Description,
			Icon:             mhDataItem.IconURL,
			DownloadUrl:      mhDataItem.DownloadURL,
			ClickUrl:         mhDataItem.LandpageURL,
			PackageName:      mhDataItem.PackageName,
			ExposalUrls:      mhDataItem.ImpressionLink,
			ClickMonitorUrls: mhDataItem.ClickLink,
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			if len(mhDataItem.ConvTracks) > 0 {
				for _, convTracks := range mhDataItem.ConvTracks {
					if convTracks.ConvType == 10 {
						bidItem.DpSuccessTrackUrls = convTracks.ConvURLS
					}
				}
			}
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				bidItem.DplUrl = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					bidItem.DplUrl = mhDataItem.MarketURL
				}
			}

			bidItem.MediaStyle = 1
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				bidItem.DplUrl = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					bidItem.DplUrl = mhDataItem.DeepLink
				}
			}

			bidItem.MediaStyle = 2
		}

		appInfo := &SyxjResponseSeatbidAppInfoObject{
			AppName:    mhDataItem.AppName,
			Developer:  mhDataItem.Publisher,
			Version:    mhDataItem.AppVersion,
			PacketSize: strconv.FormatInt(mhDataItem.PackageSize/1024/1024, 10) + "MB",
			Privacy:    mhDataItem.PrivacyLink,
			Permission: mhDataItem.PermissionURL,
			Desc:       mhDataItem.AppInfo,
			DescURL:    mhDataItem.AppInfoURL,
		}

		bidItem.DownloadAppInfo = appInfo

		var adStyle int
		var adType int
		switch mhDataItem.CrtType {
		case 11:
			var imgArray []string
			for _, imageItem := range mhDataItem.Image {
				imgArray = append(imgArray, imageItem.URL)
			}

			bidItem.Imgs = imgArray
			adStyle = 2
			// todo 水印请求不带的⼴告类型，但是返回的时候需要，所以把样式id当成 ⼴告类型返回
			if len(reqRtbConfig.AllImageStyleID) == 0 {
				continue
			}
			adType, _ = strconv.Atoi(reqRtbConfig.AllImageStyleID)
		case 20:
			if mhDataItem.Video != nil {
				video := &SyxjResponseSeatbidVideoObject{
					VideoUrl:       mhDataItem.Video.VideoURL,
					VideoDuration:  mhDataItem.Video.Duration / 1000,
					VideoPreImgurl: mhDataItem.Video.CoverURL,
				}
				// todo 水印请求不带的⼴告类型，但是返回的时候需要，所以把样式id当成 ⼴告类型返回
				if len(reqRtbConfig.AllVideoStyleID) == 0 {
					continue
				}
				adType, _ = strconv.Atoi(reqRtbConfig.AllVideoStyleID)
				bidItem.Video = video
				adStyle = 3
			}
		}

		bid := SyxjResponseBidObject{
			AdType:  adType,
			AdStyle: adStyle,
			Price:   float32(ecpm),
			Impid:   impItem.Id,
			Nurl:    winURL,
			Items:   &bidItem,
		}
		bids = append(bids, &bid)
	}

	var seatBid SyxjResponseSeatbidObject
	seatBid.Bid = bids

	seatBids = append(seatBids, &seatBid)

	return &SyxjResponseObject{
		Id:      req.Id,
		BidId:   bigdataUID,
		Seatbid: seatBids,
	}, http.StatusOK
}

func getSyxjAppList(appListInt []int, appListStr []string) []int {
	if len(appListInt) == 0 && len(appListStr) == 0 {
		return []int{}
	}
	var appListIdArray []int

	if len(appListInt) > 0 {
		for _, appId := range appListInt {
			if v, ok := syxjAppListCodeMap[appId]; ok {
				appListIdArray = append(appListIdArray, v)
			}
		}
	} else if len(appListStr) > 0 {
		appListMap := models2.GetMHAppListMap()
		for _, packageName := range appListStr {
			if v, ok := appListMap[packageName]; ok {
				appListIdArray = append(appListIdArray, v)
			}
		}
	}

	return appListIdArray
}

var syxjAppListCodeMap = map[int]int{
	100001: 1002,
	100002: 1001,
	100003: 1015,
	100004: 1008,
	100005: 1005,
	100006: 1006,
	100007: 1020,
	100008: 1011,
	100009: 1016,
	100010: 1040,
	100011: 1025,
	100012: 1033,
	100013: 1026,
	100014: 1039,
	100015: 1013,
	100016: 1019,
	100017: 1004,
	100018: 1009,
	100019: 1014,
	100022: 1023,
	100023: 1017,
	100024: 1029,
	100027: 1045,
	100028: 1047,
	100031: 1046,
	100032: 1043,
	100034: 1031,
	100035: 1035,
	100042: 1012,
	100044: 1032,
	100045: 1038,
	100051: 1003,
	100052: 1010,
	100053: 1018,
	100054: 1030,
	100068: 1044,
	100069: 1007,
	100070: 1021,
	100071: 1022,
	100072: 1024,
	100073: 1027,
	100074: 1028,
	100075: 1034,
	100076: 1036,
	100077: 1041,
	100078: 1042,
}
