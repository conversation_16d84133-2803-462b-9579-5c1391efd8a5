package rtb_syxj

type SyxjRequestObject struct {
	Id           string                    `json:"id"`
	InstalledApp []string                  `json:"installedApp"`
	Device       *Syxj0RequestDeviceObject `json:"device"`
	App          *SyxjRequestAppObject     `json:"app"`
	Imp          []*SyxjRequestImpObject   `json:"imp"`
	Appendix01   []int                     `json:"appendix01"`
}

type Syxj0RequestDeviceObject struct {
	Os              string                       `json:"os"`
	OsVersion       string                       `json:"osv"`
	Imei            string                       `json:"imei"`
	ImeiMd5         string                       `json:"imeiMd5"`
	Oaid            string                       `json:"oaid"`
	OaidMd5         string                       `json:"oaidMd5"`
	AndroidId       string                       `json:"androidId"`
	Idfa            string                       `json:"idfa"`
	IdfaMd5         string                       `json:"idfaMd5"`
	Mac             string                       `json:"mac"`
	MacMd5          string                       `json:"macMd5"`
	Ip              string                       `json:"ip"`
	Ua              string                       `json:"ua"`
	Make            string                       `json:"make"`
	Brand           string                       `json:"brand"`
	Model           string                       `json:"model"`
	Hwv             string                       `json:"hwv"`
	AppList         string                       `json:"appList"`
	BootMark        string                       `json:"bootMark"`
	UpdateMark      string                       `json:"updateMark"`
	VerCodeOfHms    string                       `json:"verCodeOfHms"`
	BootTimeSec     string                       `json:"bootTimeSec"`
	PhoneName       string                       `json:"phoneName"`
	OsUpdateTimeSec string                       `json:"osUpdateTimeSec"`
	ModelCode       string                       `json:"modelCode"`
	TimeZone        string                       `json:"timeZone"`
	MemorySize      int64                        `json:"memorySize"`
	DiskSize        int64                        `json:"diskSize"`
	Network         int                          `json:"connectionType"`
	Carrier         int                          `json:"carrier"`
	Height          int                          `json:"screenHeight"`
	Width           int                          `json:"screenWidth"`
	CaidWithFactors *SyxjRequestDeviceCaidObject `json:"caidWithFactors"`
}

type SyxjRequestDeviceCaidObject struct {
	Caid        string                              `json:"caid"`
	CaidVersion string                              `json:"caidVersion"`
	Factors     *SyxjRequestDeviceCaidFactorsObject `json:"factors"`
}

type SyxjRequestDeviceCaidFactorsObject struct {
	BootTimeInSec  string `json:"bootTimeInSec"`
	CountryCode    string `json:"countryCode"`
	Language       string `json:"language"`
	DeviceName     string `json:"deviceName"`
	SystemVersion  string `json:"systemVersion"`
	Machine        string `json:"machine"`
	Memory         string `json:"memory"`
	Disk           string `json:"disk"`
	SysFileTime    string `json:"sysFileTime"`
	Model          string `json:"model"`
	TimeZone       string `json:"timeZone"`
	DeviceInitTime string `json:"deviceInitTime"`
}

type SyxjRequestAppObject struct {
	Name    string `json:"appName"`
	Bundle  string `json:"bundle"`
	Version string `json:"appVersion"`
}

type SyxjRequestImpObject struct {
	Id       string                    `json:"id"`
	TagId    string                    `json:"tagId"`
	Bidfloor float32                   `json:"bidfloor"`
	Deals    []*SyxjRequestDealsObject `json:"deals"`
}

type SyxjRequestDealsObject struct {
	Bidfloor int    `json:"bidfloor"`
	Id       string `json:"id"`
}

type SyxjResponseObject struct {
	Id      string                       `json:"id"`
	BidId   string                       `json:"bidid"`
	Seatbid []*SyxjResponseSeatbidObject `json:"seatbid,omitempty"`
}

type SyxjResponseSeatbidObject struct {
	Bid []*SyxjResponseBidObject `json:"bid,omitempty"`
}

type SyxjResponseBidObject struct {
	AdType  int                        `json:"adType,omitempty"`
	AdStyle int                        `json:"adStyle,omitempty"`
	Price   float32                    `json:"price,omitempty"`
	Impid   string                     `json:"impid,omitempty"`
	Nurl    string                     `json:"nurl,omitempty"`
	Items   *SyxjResponseBidItemObject `json:"items,omitempty"`
}

type SyxjResponseBidItemObject struct {
	MediaStyle         int                               `json:"mediaStyle,omitempty"`
	Title              string                            `json:"title,omitempty"`
	Desc               string                            `json:"desc,omitempty"`
	Icon               string                            `json:"icon,omitempty"`
	DownloadUrl        string                            `json:"downloadUrl,omitempty"`
	ClickUrl           string                            `json:"clickUrl,omitempty"`
	DplUrl             string                            `json:"dplUrl,omitempty"`
	PackageName        string                            `json:"packageName,omitempty"`
	Imgs               []string                          `json:"imgs,omitempty"`
	ExposalUrls        []string                          `json:"exposalUrls,omitempty"`
	ClickMonitorUrls   []string                          `json:"clickMonitorUrls,omitempty"`
	DpSuccessTrackUrls []string                          `json:"dpSuccessTrackUrls,omitempty"`
	Video              *SyxjResponseSeatbidVideoObject   `json:"video,omitempty"`
	DownloadAppInfo    *SyxjResponseSeatbidAppInfoObject `json:"downloadAppInfo,omitempty"`
}

type SyxjResponseSeatbidVideoObject struct {
	VideoDuration  int    `json:"videoDuration,omitempty"`
	VideoUrl       string `json:"videoUrl,omitempty"`
	VideoPreImgurl string `json:"videoPreImgurl,omitempty"`
}

type SyxjResponseSeatbidAppInfoObject struct {
	AppName    string `json:"appName,omitempty"`
	Developer  string `json:"developer,omitempty"`
	Version    string `json:"version,omitempty"`
	PacketSize string `json:"packetSize,omitempty"`
	Privacy    string `json:"privacy,omitempty"`
	Permission string `json:"permission,omitempty"`
	Desc       string `json:"desc,omitempty"`
	DescURL    string `json:"descURL,omitempty"`
}
