package rtb_mucang_freeze

import (
	"bytes"
	"fmt"
	"google.golang.org/protobuf/proto"
	"io"
	"mh_proxy/pb/mucang"
	"net/http"
	"testing"
)

func Test_HandleByMucang(t *testing.T) {
	//var acceptedSizeArray []*mucang.BidRequest_AdSlot_Size
	//var acceptedSize *mucang.BidRequest_AdSlot_Size
	//acceptedSize.Width = 720
	//acceptedSize.Height = 1280
	//acceptedSizeArray = append(acceptedSizeArray, acceptedSize)
	request := &mucang.BidRequest{
		RequestId:  "74c739d8529b45e8a9100f901284469f",
		ApiVersion: "1.8",
		Device: &mucang.BidRequest_Device{
			Idfa: "7CC25A5F-67F4-4380-8E90-F635521E75D9",
			//Imei: "82106cd54358d4f3439cf5457ba77506",
			//AndroidId: "eec7dc4b68cb8c2d",
			//Oaid:      "9ffe6e37-7f7f-f24c-feaf-bdb37e5f7451",
			IdfaMd5: "82106cd54358d4f3439cf5457ba77506",
			//ImeiMd5:      "",
			//AndroididMd5: "9b8d9c1cbbefba1cd30f0f2469c2257b",
			//OaidMd5:      "933827a8c555bdc570938e119c975d80",
			Type:      1,
			OsVersion: "13.7",
			Vendor:    "Apple",
			Model:     "iPhone XS",
			Ua:        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/113.0",
			Ip:        "*************",
			//Mac:          "",
			Network: &mucang.BidRequest_Device_Network{
				ConnectionType: 5,
				//OperatorType:   0,
			},
			Platform: 1,
			Brand:    "HUAWEI",
			//BootMark:         "",
			//UpdateMark:       "",
			ScreenHeight: 2259,
			ScreenWidth:  1080,
			//DeviceInitTime:   "",
			//SystemUpdateTime: "",
			//SystemBootTime:   "",
		},
		//App: &mucang.BidRequest_App{
		//	Name:    "",
		//	Version: "",
		//	PkgName: "",
		//},
		Geo: &mucang.BidRequest_Geo{
			Latitude:  30.491837,
			Longitude: 114.41049,
		},
		//User: &mucang.BidRequest_User{
		//	Gender:   0,
		//	Age:      0,
		//	Keywords: "",
		//},
		Adslots: &mucang.BidRequest_AdSlot{
			Id:                      "jkbd101",
			Adtype:                  2,
			Pos:                     4,
			AcceptedSize:            nil,
			AcceptedCreativeTypes:   nil,
			AcceptedInteractionType: nil,
			MinimumCpm:              10,
		},
	}

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://test.maplehaze.cn/rtb/request?channel=41", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()
	bodyContent, err := io.ReadAll(resp.Body)
	fmt.Println(string(bodyContent))
}
