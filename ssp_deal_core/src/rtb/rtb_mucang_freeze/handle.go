package rtb_mucang_freeze

import (
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/mucang"
	"mh_proxy/utils"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleByMucang(c *gin.Context, channel string) (*mucang.BidResponse, int) {
	bodyContent, _ := c.GetRawData()
	req := &mucang.BidRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "解码失败",
			Ads:       nil,
		}, http.StatusNotFound
	}

	var deviceOs string
	switch req.GetDevice().GetPlatform() {
	case mucang.BidRequest_Device_ANDROID:
		deviceOs = "android"
	case mucang.BidRequest_Device_IPHONE:
		deviceOs = "ios"
	default:
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "设备类型错误",
			Ads:       nil,
		}, http.StatusNotFound
	}
	deviceMake := req.GetDevice().GetVendor()
	if deviceOs == "ios" {
		deviceMake = "Apple"
	}

	var connectType int
	switch req.GetDevice().GetNetwork().GetConnectionType() {
	case mucang.BidRequest_Device_Network_WIFI:
		connectType = 1
	case mucang.BidRequest_Device_Network_G2:
		connectType = 2
	case mucang.BidRequest_Device_Network_G3:
		connectType = 3
	case mucang.BidRequest_Device_Network_G4:
		connectType = 4
	case mucang.BidRequest_Device_Network_G5:
		connectType = 5
	case mucang.BidRequest_Device_Network_UNKNOWN:
		connectType = 0
	default:
		connectType = 1
	}

	var carrier int
	switch req.GetDevice().GetNetwork().GetOperatorType() {
	case mucang.BidRequest_Device_Network_M:
		carrier = 1
	case mucang.BidRequest_Device_Network_T:
		carrier = 2
	case mucang.BidRequest_Device_Network_C:
		carrier = 3
	default:
		carrier = 0
	}

	slots := req.GetAdslots()
	if len(slots.Id) == 0 {
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "slots.Id为空",
			Ads:       nil,
		}, http.StatusNotFound
	}

	var reqRtbConfig models.RtbConfigByTagIDStu
	tagId := slots.GetId()
	price := slots.GetMinimumCpm()

	rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, tagId, deviceOs, "", "", int(price))
	if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "tagId 为空",
			Ads:       nil,
		}, http.StatusNotFound
	}

	reqRtbConfig = (*rtbConfigArrayByTagID)[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetApp().GetPkgName(),
			AppName:     req.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			OsVersion:       req.GetDevice().GetOsVersion(),
			Model:           req.GetDevice().GetModel(),
			Manufacturer:    deviceMake,
			Imei:            req.GetDevice().GetImei(),
			ImeiMd5:         req.GetDevice().GetImeiMd5(),
			AndroidID:       req.GetDevice().GetAndroidId(),
			AndroidIDMd5:    req.GetDevice().GetAndroididMd5(),
			Oaid:            req.GetDevice().GetOaid(),
			Mac:             req.GetDevice().GetMac(),
			Idfa:            req.GetDevice().GetIdfa(),
			IdfaMd5:         req.GetDevice().GetIdfaMd5(),
			Ua:              req.GetDevice().GetUa(),
			ScreenWidth:     int(req.GetDevice().GetScreenWidth()),
			ScreenHeight:    int(req.GetDevice().GetScreenHeight()),
			DeviceType:      1,
			IP:              req.GetDevice().GetIp(),
			DeviceStartSec:  req.GetDevice().GetSystemBootTime(),
			SystemUpdateSec: req.GetDevice().GetSystemUpdateTime(),
			BootMark:        req.GetDevice().GetBootMark(),
			UpdateMark:      req.GetDevice().GetUpdateMark(),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}
	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "adx返回为空",
			Ads:       nil,
		}, http.StatusNotFound
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &mucang.BidResponse{
			RequestId: req.GetRequestId() + "adx返回个数为空",
			Ads:       nil,
		}, http.StatusNotFound
	}

	var adx []*mucang.BidResponse_Ad
	for i := 0; i < mhRespCount; i++ {
		var ad = new(mucang.BidResponse_Ad)
		var creative = new(mucang.BidResponse_Ad_Creative)
		var crtType mucang.BidResponse_Ad_CreativeType
		var interactType mucang.BidResponse_Ad_InteractionType

		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]
		ad.AdId = req.GetRequestId()

		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		ad.BidPrice = uint64(ecpm)

		switch mhDataItem.CrtType {
		case 11:
			crtType = mucang.BidResponse_Ad_TEXT_ICON
		case 20:
			crtType = mucang.BidResponse_Ad_VIDEO
		}
		creative.CreativeType = crtType

		switch mhDataItem.InteractType {
		case 0:
			interactType = mucang.BidResponse_Ad_SURFING
		case 1:
			interactType = mucang.BidResponse_Ad_DOWNLOAD
		}
		creative.InteractionType = interactType

		creative.Title = mhDataItem.Title
		creative.Description = mhDataItem.Description

		var media *mucang.BidResponse_Ad_Media
		var appInfo *mucang.BidResponse_Ad_AppInfo
		var iconImage *mucang.BidResponse_Ad_Image
		var image *mucang.BidResponse_Ad_Image
		var images = make([]*mucang.BidResponse_Ad_Image, 0, len(mhDataItem.Image))
		if interactType == mucang.BidResponse_Ad_SURFING {
			for _, imageItem := range mhDataItem.Image {
				image = &mucang.BidResponse_Ad_Image{
					Url:    imageItem.URL,
					Width:  uint32(imageItem.Width),
					Height: uint32(imageItem.Height),
				}
			}
			images = append(images, image)
			creative.Images = images

			iconImage = &mucang.BidResponse_Ad_Image{
				Url:    mhDataItem.Image[0].URL,
				Width:  uint32(mhDataItem.Image[0].Width),
				Height: uint32(mhDataItem.Image[0].Height),
			}
			creative.Icon = iconImage

			creative.TargetUrl = mhDataItem.LandpageURL
		} else if interactType == mucang.BidResponse_Ad_DOWNLOAD {
			if mhDataItem.InteractType == 1 {
				appInfo = &mucang.BidResponse_Ad_AppInfo{
					IconUrl:           mhDataItem.IconURL,
					AppName:           mhDataItem.AppName,
					VersionName:       mhDataItem.AppVersion,
					CompanyName:       mhDataItem.Publisher,
					AppPermissionsUrl: mhDataItem.Permission,
					PrivacyPolicyUrl:  mhDataItem.PrivacyLink,
					DownloadUrl:       mhDataItem.DownloadURL,
					PackageName:       mhDataItem.PackageName,
				}
				creative.AppInfo = appInfo
			} else {
				continue
			}

			media = &mucang.BidResponse_Ad_Media{
				Type:       "video",
				Duration:   uint32(mhDataItem.Video.Duration / 1000), // 毫秒转秒
				Url:        mhDataItem.Video.VideoURL,
				MiddleUrl:  mhDataItem.Video.VideoURL,
				Width:      uint32(mhDataItem.Video.Width),
				Height:     uint32(mhDataItem.Video.Height),
				FirstFrame: mhDataItem.Video.CoverURL,
			}
			creative.Media = media
		}

		var impTrackArray = make([]string, 0, len(mhDataItem.ImpressionLink))
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}
		creative.ShowUrl = impTrackArray

		//winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price={MC_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)
		winURL := "https://test.maplehaze.cn/rtb/price?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price={MC_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)
		//var winNoticeUrl mucang.BidResponse
		creative.WinNoticeUrl = winURL

		// click link
		var clkTrackArray = make([]string, 0, len(mhDataItem.ClickLink))
		for _, clkItem := range mhDataItem.ClickLink {
			//clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(int(req.GetDevice().GetScreenWidth())), -1)
			//clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(int(req.GetDevice().GetScreenHeight())), -1)
			clkTrackArray = append(clkTrackArray, clkItem)
		}
		creative.ClickUrl = clkTrackArray
		creative.Deeplink = mhDataItem.DeepLink

		var deepLinkTrackOKArray = make([]string, 0, len(mhDataItem.ConvTracks))
		var deepLinkTrackFailedArray = make([]string, 0, len(mhDataItem.ConvTracks))
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}
		creative.DplSuccessUrl = deepLinkTrackOKArray
		creative.DplFailUrl = deepLinkTrackFailedArray

		ad.Creative = creative

		adx = append(adx, ad)
	}

	return &mucang.BidResponse{
		RequestId: req.GetRequestId(),
		Ads:       adx,
	}, http.StatusOK
}
