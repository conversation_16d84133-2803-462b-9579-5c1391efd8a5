package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByZhihu ...
func HandleByZhihu(c *gin.Context, channel string) *map[string]interface{} {

	bodyContent, err := c.GetRawData()
	// fmt.Println(timeBegin)
	var zhihuReq ZhihuReq

	err = json.Unmarshal([]byte(bodyContent), &zhihuReq)

	if err != nil {
		fmt.Println(err)
		return jsonZhihuNoBidReturn("null", "parser error")
	}
	// fmt.Println(zhihuReq.ID)
	// fmt.Println(zhihuReq.Version)
	// fmt.Println(zhihuReq.Imp[0].BidFloor)
	// fmt.Println(zhihuReq.Device.Geo.Lat)
	// fmt.Println(zhihuReq.Device.Geo.Lon)

	// fmt.Println("zhihu req: " + string(bodyContent))

	reqOs := strings.ToLower(zhihuReq.Device.Os)
	if reqOs == "android" || reqOs == "ios" {
	} else {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "wrong os")
	}

	reqDeivceMake := zhihuReq.Device.Make
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(zhihuReq.Device.UA) == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "wrong ua")
	}

	reqConnectType := zhihuReq.Device.ConnectionType
	if reqConnectType == 5 {
		reqConnectType = 7
	}
	reqCarrier := 0

	if len(zhihuReq.Imp) == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "wrong imp")
	}

	var reqOKImps []ZhihuReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range zhihuReq.Imp {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.TagID
		if reqOs == "android" {
			// reqTagID = "zhihu_" + item.TagID + "_android"
		} else if reqOs == "ios" {
			// reqTagID = "zhihu_" + item.TagID + "_ios"
		} else {
			continue
		}
		reqPrice := item.BidFloor
		if len(item.Pmp.Deal) > 0 {
			reqPrice = item.Pmp.Deal[0].BidFloor
		}
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return jsonZhihuNoBidReturn(zhihuReq.ID, "not active 0")
		// }

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return jsonZhihuNoBidReturn(zhihuReq.ID, "not active 1")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "wrong imp")
	}

	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	var caidMultiList []models.MHReqCAIDMulti
	if len(zhihuReq.Device.Caids) > 0 {
		for _, item := range zhihuReq.Device.Caids {
			var tmpCaid models.MHReqCAIDMulti
			tmpCaid.CAID = item.Caid
			tmpCaid.CAIDVersion = item.CaidVersion
			caidMultiList = append(caidMultiList, tmpCaid)
		}
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: zhihuReq.App.Bundle,
			AppName:     zhihuReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    zhihuReq.Device.Osv,
			Model:        zhihuReq.Device.Model,
			Manufacturer: reqDeivceMake,
			ImeiMd5:      zhihuReq.Device.ImeiMd5,
			AndroidIDMd5: zhihuReq.Device.AndroidIDMd5,
			Oaid:         zhihuReq.Device.Oaid,
			Idfa:         zhihuReq.Device.Idfa,
			Ua:           zhihuReq.Device.UA,
			DeviceType:   1,
			IP:           zhihuReq.Device.IP,
			// MacMd5:       zhihuReq.Device.MacMd5,
			CAIDMulti: caidMultiList,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "no fill 0")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "no fill 1")
	}

	var impItem ZhihuReqImp
	for _, imp := range reqOKImps {
		if imp.TagID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}
	if len(impItem.TagID) == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "no fill 1")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	zhihuBidObjArray := []map[string]interface{}{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__AUCTION_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkTrackArray = append(clkTrackArray, clkItem)
		}

		// video start finish link
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				}
			}
		}
		// crid
		// crid := mhDataItem.Crid
		// if len(crid) == 0 {
		// 	crid = utils.GetMd5(mhDataItem.Title)
		// }
		md5Key := ""
		if isVideoType == 1 {
			tmpVideoURL := strings.Split(mhDataItem.Video.VideoURL, "?tag=")[0]
			md5Key = utils.GetMd5(tmpVideoURL)
		} else {
			tmpImageURL := strings.Split(mhDataItem.Image[0].URL, "?tag=")[0]
			md5Key = utils.GetMd5(tmpImageURL)
		}

		// title 8～20; description 15～60
		tmpTitle := mhDataItem.Title
		if utf8.RuneCountInString(tmpTitle) < 8 {
			tmpTitle = "每日精品新发现~~~"
		} else if utf8.RuneCountInString(tmpTitle) > 20 {
			tmpTitle = utils.InterceptStringWith3Dots(mhDataItem.Title, 17)

		}
		tmpDescription := mhDataItem.Description
		if utf8.RuneCountInString(tmpDescription) < 15 {
			tmpDescription = "精品推荐 —— " + tmpDescription
		} else if utf8.RuneCountInString(tmpDescription) > 60 {
			tmpDescription = utils.InterceptStringWith3Dots(mhDataItem.Description, 57)
		}

		zhihuBidObjItemMap := map[string]interface{}{}
		zhihuBidObjItemMap["id"] = bigdataUID
		zhihuBidObjItemMap["impid"] = impItem.ID
		zhihuBidObjItemMap["price"] = ecpm
		zhihuBidObjItemMap["nurl"] = winURL
		zhihuBidObjItemMap["adid"] = mhDataItem.AdID
		zhihuBidObjItemMap["cid"] = md5Key
		zhihuBidObjItemMap["impurl"] = impTrackArray
		zhihuBidObjItemMap["clickurl"] = clkTrackArray
		// zhihuBidObjItemMap["landurl"] = mhDataItem.AdURL
		if mhDataItem.InteractType == 0 {
			zhihuBidObjItemMap["landurl"] = mhDataItem.LandpageURL
		} else {
			zhihuBidObjItemMap["landurl"] = mhDataItem.DownloadURL
		}
		if len(mhDataItem.PackageName) > 0 {
			zhihuBidObjItemMap["bundle"] = mhDataItem.PackageName
		}
		if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				zhihuBidObjItemMap["deeplinkurl"] = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					zhihuBidObjItemMap["deeplinkurl"] = mhDataItem.DeepLink
				}
			}
		} else if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				zhihuBidObjItemMap["deeplinkurl"] = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					zhihuBidObjItemMap["deeplinkurl"] = mhDataItem.MarketURL
				}
			}
		}

		zhihuBidObAttrMapArray := []map[string]interface{}{}

		// advname
		zhihuBidObjAttrItemMap := map[string]interface{}{}
		zhihuBidObjAttrItemMap["key"] = "advname"
		zhihuBidObjAttrItemMap["value"] = "枫岚"
		zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItemMap)

		if isVideoType == 1 {
			// 视频
			zhihuBidObjItemMap["tpl"] = 8

			// img
			zhihuBidObjAttrItem1Map := map[string]interface{}{}
			zhihuBidObjAttrItem1Map["key"] = "img"
			zhihuBidObjAttrItem1Map["value"] = mhDataItem.Video.CoverURL
			zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem1Map)

			// title
			if len(tmpTitle) > 0 {
				zhihuBidObjAttrItem2Map := map[string]interface{}{}
				zhihuBidObjAttrItem2Map["key"] = "title"
				zhihuBidObjAttrItem2Map["value"] = tmpTitle
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem2Map)
			}

			// description
			if len(tmpDescription) > 0 {
				zhihuBidObjAttrItem3Map := map[string]interface{}{}
				zhihuBidObjAttrItem3Map["key"] = "description"
				zhihuBidObjAttrItem3Map["value"] = tmpDescription
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem3Map)
			}

			// icon_url
			if len(mhDataItem.IconURL) > 0 {
				zhihuBidObjAttrItem4Map := map[string]interface{}{}
				zhihuBidObjAttrItem4Map["key"] = "logo"
				zhihuBidObjAttrItem4Map["value"] = mhDataItem.IconURL
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem4Map)
			}

			// video_url
			if len(mhDataItem.Video.VideoURL) > 0 {
				tmpVideoURL := strings.Split(mhDataItem.Video.VideoURL, "?tag=")[0]
				zhihuVideoID := models.GetZhihuVideoIDByMd5Key(utils.GetMd5(tmpVideoURL), reqRtbConfig.LocalAppID)
				if len(zhihuVideoID) == 0 {
					fmt.Println("continue 0")
					continue
				}

				zhihuBidObjAttrItem5Map := map[string]interface{}{}
				zhihuBidObjAttrItem5Map["key"] = zhihuVideoID
				zhihuBidObjAttrItem5Map["value"] = mhDataItem.Video.VideoURL
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem5Map)
			}
		} else {
			// 大图
			zhihuBidObjItemMap["tpl"] = 2
			// 开屏
			if strings.Contains(reqRtbConfig.TagID, "_21_") {
				zhihuBidObjItemMap["tpl"] = 7
			} else if strings.Contains(reqRtbConfig.TagID, "_20_") {
				zhihuBidObjItemMap["tpl"] = 3
			}

			// img
			zhihuBidObjAttrItem1Map := map[string]interface{}{}
			zhihuBidObjAttrItem1Map["key"] = "img"
			zhihuBidObjAttrItem1Map["value"] = mhDataItem.Image[0].URL
			zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem1Map)

			// title
			if len(tmpTitle) > 0 {
				zhihuBidObjAttrItem2Map := map[string]interface{}{}
				zhihuBidObjAttrItem2Map["key"] = "title"
				zhihuBidObjAttrItem2Map["value"] = tmpTitle
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem2Map)
			}

			// description
			if len(tmpDescription) > 0 {
				zhihuBidObjAttrItem3Map := map[string]interface{}{}
				zhihuBidObjAttrItem3Map["key"] = "description"
				zhihuBidObjAttrItem3Map["value"] = tmpDescription
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem3Map)
			}

			// icon_url
			if len(mhDataItem.IconURL) > 0 {
				zhihuBidObjAttrItem4Map := map[string]interface{}{}
				zhihuBidObjAttrItem4Map["key"] = "logo"
				zhihuBidObjAttrItem4Map["value"] = mhDataItem.IconURL
				zhihuBidObAttrMapArray = append(zhihuBidObAttrMapArray, zhihuBidObjAttrItem4Map)
			}
		}

		// videotracks
		if isVideoType == 1 {
			zhihuBidObjTrackMap := map[string]interface{}{}
			zhihuBidObjTrackMap["clickplay"] = videoStartTrackArray
			zhihuBidObjTrackMap["play100"] = videoFinishTrackArray
			zhihuBidObjItemMap["videotracks"] = zhihuBidObjTrackMap
		}

		zhihuBidObjItemMap["attr"] = zhihuBidObAttrMapArray

		zhihuBidObjArray = append(zhihuBidObjArray, zhihuBidObjItemMap)
	}

	if len(zhihuBidObjArray) == 0 {
		return jsonZhihuNoBidReturn(zhihuReq.ID, "no fill 2")
	}

	// bid array
	zhihuRespBidMap := map[string]interface{}{}
	zhihuRespBidMap["bid"] = zhihuBidObjArray

	// seat bid array
	zhihuRespSeatBidArrayMap := []map[string]interface{}{}
	zhihuRespSeatBidArrayMap = append(zhihuRespSeatBidArrayMap, zhihuRespBidMap)

	// resp
	zhihuRespMap := map[string]interface{}{}
	zhihuRespMap["id"] = zhihuReq.ID
	zhihuRespMap["nbr"] = 0
	zhihuRespMap["seatbid"] = zhihuRespSeatBidArrayMap

	return &zhihuRespMap
}

func jsonZhihuNoBidReturn(reqID string, reason string) *map[string]interface{} {
	// fmt.Println("zhihu no bid reason: " + reason)

	zhihuRespMap := map[string]interface{}{}
	zhihuRespMap["id"] = reqID
	zhihuRespMap["nbr"] = 2
	return &zhihuRespMap
}

// ZhihuReq ...
type ZhihuReq struct {
	ID      string         `json:"id"`
	Version string         `json:"version"`
	App     ZhihuReqApp    `json:"app"`
	Device  ZhihuReqDevice `json:"device"`
	Imp     []ZhihuReqImp  `json:"imp"`
}

// ZhihuReqApp ...
type ZhihuReqApp struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
}

// ZhihuReqImp ...
type ZhihuReqImp struct {
	ID       string         `json:"id"`
	TagID    string         `json:"tagid"`
	BidFloor int64          `json:"bidfloor"`
	Pmp      ZhihuReqImpPmp `json:"pmp"`
}

// ZhihuReqImpPmp ...
type ZhihuReqImpPmp struct {
	PrivateAuction int64                `json:"private_auction"`
	Deal           []ZhihuReqImpPmpDeal `json:"deals"`
}

// ZhihuReqImpPmpDeal ...
type ZhihuReqImpPmpDeal struct {
	ID       string `json:"id"`
	BidFloor int64  `json:"bidfloor"`
	At       int64  `json:"at"`
}

// ZhihuReqDevice ...
type ZhihuReqDevice struct {
	Os             string               `json:"os"`
	Osv            string               `json:"osv"`
	UA             string               `json:"ua"`
	IP             string               `json:"ip"`
	Make           string               `json:"make"`
	Model          string               `json:"model"`
	ImeiMd5        string               `json:"didmd5"`
	AndroidIDMd5   string               `json:"dpidmd5"`
	MacMd5         string               `json:"macmd5"`
	Oaid           string               `json:"oaid"`
	Idfa           string               `json:"idfa"`
	ConnectionType int                  `json:"connectiontype"`
	Geo            ZhihuReqDeviceGeo    `json:"geo"`
	Caids          []ZhihuReqDeviceCaid `json:"caid"`
}

// ZhihuReqDeviceGeo ...
type ZhihuReqDeviceGeo struct {
	Lat float32 `json:"lat"`
	Lon float32 `json:"lon"`
}

// ZhihuReqDeviceCaid ...
type ZhihuReqDeviceCaid struct {
	Caid        string `json:"caid"`
	CaidVersion string `json:"version"`
}
