package rtb_maimai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
)

func Test_HandleByMaimai(t *testing.T) {

	bodyContent := `{
    "id":"2b2bf147a75149629c5bba5d475d86dc",
    "version":"1.0.1",
    "imp":{
        "z":"3000021",
        "template_ids":"1",
        "banner":{
            "w":750,
            "h":1334
        },
        "bid":{
            "bid_type":1,
            "bid_floor":1000
        }
    },
    "app":{
        "apk_name":"com.taou.maimai",
        "name":"脉脉",
        "ver":"6.3.12"
    },
    "device":{
        "ip":"**************",
        "ua":"Mozilla/5.0 (Linux; Android 9; vivo X21A Build/PKQ1.180819.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Mobile Safari/537.36",
        "w":1080,
        "h":2201,
        "os":1,
        "osv":"9",
        "brand":"vivo",
        "model":"vivo X21",
        "network":1,
        "operator":3,
        "android_id":"yecyuj9g413ukhel",
        "imei":"866919039263672",
        "oaid":"531ec9f7-d96f-451b-91a0-adca58c8d7f0",
        "density":3.0,
        "screen_type":3,
        "boot_mark":"8f6cdff7-856b-4640-9827-3a72fd55686d",
        "update_mark":"35254146.49999997"
    }
}`

	var request MaimaiRequestObject
	_ = json.Unmarshal([]byte(bodyContent), &request)

	client := &http.Client{}
	reqByte, _ := json.Marshal(request)
	requestUrl := "https://api-debug.maplehaze.cn/rtb/request?channel=47"
	requestPost, _ := http.NewRequest("POST", requestUrl, bytes.NewReader(reqByte))
	requestPost.Header.Add("Content-Type", "application/json")
	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	fmt.Println(string(body))
}
