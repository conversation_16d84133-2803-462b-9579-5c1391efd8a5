package rtb_maimai

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *MaimaiPipline) Init(c *gin.Context, channel string) *MaimaiPipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()

	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject MaimaiRequestObject
	err = json.Unmarshal(bodyContent, &requestObject)

	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()
	p.Context = c
	p.Channel = channel

	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	//models.DebugXXXXXXToMysql(p.Context, string(bodyContent), "", "", "", "", "", "", "", "", "", "")

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCAID().
		SetupCarrier()
}

// CheckImp 检查Imp对象
func (p *MaimaiPipline) CheckImp() *MaimaiPipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *MaimaiPipline) SetupOsString() *MaimaiPipline {
	switch p.Request.Device.Os {
	case 1:
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	case 2:
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	default:
		// 错误信息
		return p.SetErrorString("invalid os")
	}

	return p
}

// SetupCAID 设置 CAID
func (p *MaimaiPipline) SetupCAID() *MaimaiPipline {
	var caidMulti models.MHReqCAIDMulti
	var caidMultiList []models.MHReqCAIDMulti

	caidMulti.CAID = p.Request.Device.Caid
	caidMulti.CAIDVersion = p.Request.Device.CaidVersion
	caidMultiList = append(caidMultiList, caidMulti)

	p.CaidMulti = caidMultiList
	return p
}

// CheckUA 检查UA
func (p *MaimaiPipline) CheckUA() *MaimaiPipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *MaimaiPipline) SetupManufacturer() *MaimaiPipline {
	// 每次需要判断状态
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Brand
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *MaimaiPipline) SetupConnectType() *MaimaiPipline {
	switch p.Request.Device.Network {
	case 1:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	case 3:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_5G
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}

	return p
}

// SetupCarrier 设置运营商
func (p *MaimaiPipline) SetupCarrier() *MaimaiPipline {
	switch p.Request.Device.Carrier {
	case 1:
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case 2:
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case 3:
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}

	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *MaimaiPipline) RequestRtbConfig() *MaimaiPipline {
	var styleIds []string
	var resultImp []*MaimaiRequestImpObject
	var configList []*models.RtbConfigByTagIDStu

	imp := p.Request.Imp
	tagId := imp.Z
	price := 0
	if imp.Bid.BidType != 1 {
		return p.SetErrorString("bid_type error")
	}
	if imp.Bid.BidFloor > 0 {
		price = imp.Bid.BidFloor
	}
	styleIds = strings.Split(imp.TemplateIds, ",")

	var adxInfo *[]models.RtbConfigByTagIDStu
	adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(p.Context, p.Channel, tagId, p.DeviceOs.String(), styleIds, "", price)

	if adxInfo == nil || len(*adxInfo) == 0 {
		return p.SetErrorString("adx_info error")
	}

	configData := (*adxInfo)[0]
	resultImp = append(resultImp, imp)
	configList = append(configList, &configData)

	// 判断是否有效填充
	if len(configList) == 0 || len(resultImp) == 0 {
		return p.SetErrorString("resultImp or configList imp is empty")
	}

	p.ResultImp = resultImp
	p.ConfigList = configList

	return p
}
func ConvertStrSlice2Map(sl []string) map[string]struct{} {
	set := make(map[string]struct{}, len(sl))
	for _, v := range sl {
		set[v] = struct{}{}
	}
	return set
}

// RequestAdxAd 请求广告
func (p *MaimaiPipline) RequestAdxAd() *MaimaiPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]
	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: p.Request.App.Bundle,
			AppName:     p.Request.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              p.DeviceOs.String(),
			OsVersion:       p.Request.Device.Osv,
			Model:           p.Request.Device.Model,
			Manufacturer:    p.Manufacturer,
			Imei:            p.Request.Device.Imei,
			ImeiMd5:         p.Request.Device.ImeiMd5,
			Oaid:            p.Request.Device.Oaid,
			Idfa:            p.Request.Device.Idfa,
			Ua:              p.Request.Device.Ua,
			ScreenWidth:     p.Request.Device.W,
			ScreenHeight:    p.Request.Device.H,
			Mac:             p.Request.Device.MAC,
			AndroidID:       p.Request.Device.AndroidId,
			AndroidIDMd5:    p.Request.Device.AndroidIdMd5,
			DeviceType:      1,
			IP:              p.Request.Device.Ip,
			CAIDMulti:       p.CaidMulti,
			BootMark:        p.Request.Device.BootMark,
			UpdateMark:      p.Request.Device.UpdateMark,
			DPI:             p.Request.Device.Density,
			AppStoreVersion: p.Request.Device.AppStoreVersion,
		},
		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func (p *MaimaiPipline) SetupResponse() *MaimaiPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	// 获得对应的返回广告数据
	adxAd := p.AdxAdResponse.Data[requestConfig.LocalPosID].List[0]
	isVideoAd := adxAd.Video != nil && len(adxAd.Video.VideoURL) > 0

	// 样式id
	allStyleID := requestConfig.AllImageStyleID
	if isVideoAd {
		allStyleID = requestConfig.AllVideoStyleID
	}

	var templateid string
	templateIdArr := strings.Split(p.Request.Imp.TemplateIds, ",")
	allStyleIDArr := strings.Split(allStyleID, ",")
	allStyleIDMap := ConvertStrSlice2Map(allStyleIDArr)
	for _, templateId := range templateIdArr {
		_, ok := allStyleIDMap[templateId]
		if ok {
			templateid = templateId
			break
		}
	}

	// 统计
	models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

	// 判定上游返回ecpm是否大于底价
	if adxAd.Ecpm < requestConfig.Price {
		return p.SetErrorString("price is wrong")
	}
	ecpm := adxAd.Ecpm

	winNotice := config.ExternalRtbPriceURL +
		"?uid=" + p.UUID +
		"&tagid=" + requestConfig.TagID +
		"&bp=" + utils.ConvertIntToString(ecpm) +
		"&channel=" + p.Channel +
		"&price=__MM_AUCTION_PRICE__" +
		"&log=" + url.QueryEscape(adxAd.Log)

	var interactionType int
	if adxAd.InteractType == 1 {
		interactionType = 2
	} else {
		interactionType = 0
	}

	if len(adxAd.DeepLink) > 0 || len(adxAd.MarketURL) > 0 {
		interactionType = 1
	}

	var imgObject MaimaiResponseImgObject
	var videoObject MaimaiResponseVideoObject
	switch adxAd.CrtType {
	case 11:
		var img []string
		img = append(img, adxAd.Image[0].URL)
		imgObject.Width = adxAd.Image[0].Width
		imgObject.Height = adxAd.Image[0].Height
		imgObject.ImgUrls = img
	case 20:
		videoObject.Width = adxAd.Video.Width
		videoObject.Height = adxAd.Video.Height
		videoObject.CoverUrl = adxAd.Video.CoverURL
		videoObject.VideoUrl = adxAd.Video.VideoURL
		videoObject.Skip = 0
		videoObject.Duration = adxAd.Video.Duration / 1000
	}
	atoi, _ := strconv.Atoi(templateid)
	if atoi == 0 {
		return p.SetErrorString("templateid is empty")
	}
	responseObject := &MaimaiResponseObject{
		Status:          1000,
		TemplateId:      atoi,
		InteractionType: interactionType,
		Id:              p.Request.Id,
		Msg:             "成功",
		Title:           adxAd.Title,
		CreativeId:      adxAd.Crid,
		LandingUrl:      adxAd.LandpageURL,
		Deeplink:        adxAd.DeepLink,
		Icon:            adxAd.IconURL,
		ImpressionUrls:  adxAd.ImpressionLink,
		ClickUrls:       adxAd.ClickLink,
		Bid: &MaimaiResponseBidObject{
			BidType:      1,
			Price:        ecpm,
			WinNoticeUrl: winNotice,
		},
	}

	if interactionType == 1 {
		responseObject.UniversalLink = adxAd.DeepLink
	}

	if adxAd.InteractType == 1 {
		if len(adxAd.MarketURL) > 0 {
			responseObject.Deeplink = adxAd.MarketURL
		} else {
			if len(adxAd.DeepLink) > 0 {
				responseObject.Deeplink = adxAd.DeepLink
			}
		}

		responseObject.DownloadUrl = adxAd.DownloadURL
		responseObject.AppName = adxAd.AppName
		responseObject.AppVersion = adxAd.AppVersion
		responseObject.Bundle = adxAd.PackageName
		responseObject.UserRights = adxAd.Permission
		responseObject.Privacy = adxAd.PrivacyLink
	} else {
		if len(adxAd.DeepLink) > 0 {
			responseObject.Deeplink = adxAd.DeepLink
		} else {
			if len(adxAd.MarketURL) > 0 {
				responseObject.Deeplink = adxAd.MarketURL
			}
		}
	}

	if isVideoAd {
		responseObject.Video = &videoObject
	} else {
		responseObject.Img = &imgObject
	}

	status := http.StatusOK
	if len(responseObject.Title) == 0 {
		status = http.StatusNoContent
	}
	p.Response = responseObject
	p.ResponseStatus = status

	return p
}

// ResponseResult 返回结果
func (p *MaimaiPipline) ResponseResult() (*MaimaiResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &MaimaiResponseObject{
			Status: 1001,
			Id:     p.Request.Id,
			Msg:    "未获取到广告",
		}, http.StatusOK
	}
	return p.Response, http.StatusOK
}

// SetErrorString 设置字符串类型的错误信息
func (p *MaimaiPipline) SetErrorString(err string) *MaimaiPipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *MaimaiPipline) SetError(err error) *MaimaiPipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}
