package rtb_maimai

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// MaimaiRequestObject Objects
type MaimaiRequestObject struct {
	Id     string                     `json:"id"`
	Device *MaimaiRequestDeviceObject `json:"device"`
	App    *MaimaiRequestAppObject    `json:"app"`
	Imp    *MaimaiRequestImpObject    `json:"imp"`
}

type MaimaiRequestDeviceObject struct {
	Ip              string  `json:"ip"`
	Ua              string  `json:"ua"`
	Osv             string  `json:"osv"`
	Brand           string  `json:"brand"`
	Model           string  `json:"model"`
	MAC             string  `json:"mac"`
	MacMd5          string  `json:"mac_md5"`
	AndroidId       string  `json:"android_id"`
	AndroidIdMd5    string  `json:"android_id_md5"`
	Imei            string  `json:"imei"`
	ImeiMd5         string  `json:"imei_md5"`
	Oaid            string  `json:"oaid"`
	Idfa            string  `json:"idfa"`
	Imsi            string  `json:"imsi"`
	BootMark        string  `json:"boot_mark"`
	UpdateMark      string  `json:"update_mark"`
	AppStoreVersion string  `json:"app_store_version"`
	Caid            string  `json:"caid"`
	CaidVersion     string  `json:"caid_version"`
	Carrier         int     `json:"carrier"`
	Network         int     `json:"network"`
	Dpi             int     `json:"dpi"`
	Os              int     `json:"os"`
	W               int     `json:"w"`
	H               int     `json:"h"`
	CaidVendor      int     `json:"caid_vendor"`
	Density         float32 `json:"density"`
}

type MaimaiRequestAppObject struct {
	Name   string `json:"name"`
	Bundle string `json:"apk_name"`
}

type MaimaiRequestImpObject struct {
	Z           string                    `json:"z"`
	TemplateIds string                    `json:"template_ids"`
	DealId      string                    `json:"deal_id"`
	Bid         MaimaiRequestImpBidObject `json:"bid"`
}

type MaimaiRequestImpBidObject struct {
	BidType  int `json:"bid_type"`
	BidFloor int `json:"bid_floor"`
}

// MaimaiResponseObject Objects
type MaimaiResponseObject struct {
	Status          int                           `json:"status"`
	TemplateId      int                           `json:"template_id,omitempty"`
	InteractionType int                           `json:"interaction_type,omitempty"`
	Id              string                        `json:"id"`
	Msg             string                        `json:"msg,omitempty"`
	Title           string                        `json:"title,omitempty"`
	Advertiser      string                        `json:"advertiser,omitempty"`
	CreativeId      string                        `json:"creative_id,omitempty"`
	LandingUrl      string                        `json:"landing_url,omitempty"`
	Deeplink        string                        `json:"deeplink,omitempty"`
	Icon            string                        `json:"icon,omitempty"`
	DownloadUrl     string                        `json:"download_url,omitempty"`
	AppName         string                        `json:"app_name,omitempty"`
	AppVersion      string                        `json:"app_version,omitempty"`
	Bundle          string                        `json:"bundle,omitempty"`
	UserRights      string                        `json:"user_rights,omitempty"`
	Privacy         string                        `json:"privacy,omitempty"`
	MarketUrl       string                        `json:"market_url,omitempty"`
	UniversalLink   string                        `json:"universal_link,omitempty"`
	ImpressionUrls  []string                      `json:"impression_urls,omitempty"`
	EvokeTrackUrls  []string                      `json:"evoke_track_urls,omitempty"`
	ClickUrls       []string                      `json:"click_urls,omitempty"`
	Img             *MaimaiResponseImgObject      `json:"img,omitempty"`
	Video           *MaimaiResponseVideoObject    `json:"video,omitempty"`
	Bid             *MaimaiResponseBidObject      `json:"bid,omitempty"`
	TrackExt        *MaimaiResponseTrackExtObject `json:"track_ext,omitempty"`
}

type MaimaiResponseImgObject struct {
	Width   int      `json:"width,omitempty"`
	Height  int      `json:"height,omitempty"`
	ImgUrls []string `json:"img_urls,omitempty"`
}
type MaimaiResponseVideoObject struct {
	Width       int    `json:"width,omitempty"`
	Height      int    `json:"height,omitempty"`
	Duration    int    `json:"duration,omitempty"`
	Skip        int    `json:"skip,omitempty"`
	SkipMinTime int    `json:"skip_min_time,omitempty"`
	VideoUrl    string `json:"video_url,omitempty"`
	CoverUrl    string `json:"cover_url,omitempty"`
}
type MaimaiResponseBidObject struct {
	BidType      int    `json:"bid_type"`
	Price        int    `json:"price"`
	WinNoticeUrl string `json:"win_notice_url"`
}
type MaimaiResponseTrackExtObject struct {
	Event     int      `json:"event,omitempty"`
	TrackUrls []string `json:"track_urls,omitempty"`
}

// MaimaiPipline
type MaimaiPipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	CaidMulti []models.MHReqCAIDMulti

	Request  *MaimaiRequestObject
	Response *MaimaiResponseObject

	ConfigList    []*models.RtbConfigByTagIDStu
	ResultImp     []*MaimaiRequestImpObject
	AdxAdResponse *models.MHResp
}
