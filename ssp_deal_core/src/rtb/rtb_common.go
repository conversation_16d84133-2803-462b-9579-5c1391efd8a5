package rtb

type MHRtbOSEnum int

const (
	MH_RTB_OS_ANDROID MHRtbOSEnum = iota
	MH_RTB_OS_IOS
	MH_RTB_OS_UNKNOWN
)

func (os MHRtbOSEnum) String() string {
	switch os {
	case MH_RTB_OS_ANDROID:
		return "android"
	case MH_RTB_OS_IOS:
		return "ios"
	}
	return "unknow"
}

const MH_MANUFACTURER_APPLE = "Apple"

type MHRtbConnectTypeEnum int

const (
	MH_RTB_CONNECTTYPE_UNKNOWN MHRtbConnectTypeEnum = iota
	MH_RTB_CONNECTTYPE_WIFI
	MH_RTB_CONNECTTYPE_2G
	MH_RTB_CONNECTTYPE_3G
	MH_RTB_CONNECTTYPE_4G
	MH_RTB_CONNECTTYPE_5G = 7
)

func (connectType MHRtbConnectTypeEnum) String() string {
	switch connectType {
	case 1:
		return "wifi"
	case 2:
		return "2G"
	case 3:
		return "3G"
	case 4:
		return "4G"
	case 7:
		return "5G"
	}
	return "unknown"
}

type MHRtbCarrierEnum int

const (
	MH_RTB_CARRIER_UNKNOWN MHRtbCarrierEnum = iota
	MH_RTB_CARRIER_CM
	MH_RTB_CARRIER_CU
	MH_RTB_CARRIER_CT
)

func (carrier MHRtbCarrierEnum) String() string {
	switch carrier {
	case MH_RTB_CARRIER_CM:
		return "cm"
	case MH_RTB_CARRIER_CU:
		return "cu"
	case MH_RTB_CARRIER_CT:
		return "ct"
	}
	return "unknown"
}

type MHRtbEventTrackTypeEnum int

const (
	MH_RTB_EVENTTRACK_TYPE_UNKNOW MHRtbEventTrackTypeEnum = iota
	MH_RTB_EVENTTRACK_TYPE_START
	MH_RTB_EVENTTRACK_TYPE_FIRSTQUARTILE
	MH_RTB_EVENTTRACK_TYPE_MIDPOINT
	MH_RTB_EVENTTRACK_TYPE_THIRDQUARTILE
	MH_RTB_EVENTTRACK_TYPE_END
	MH_RTB_EVENTTRACK_TYPE_MUTE
	MH_RTB_EVENTTRACK_TYPE_SKIP
	MH_RTB_EVENTTRACK_TYPE_CLOSE
)

type MHRtbPiplineStatusEnum int

const (
	MH_RTB_PIPLINE_STATUS_EMPTY MHRtbPiplineStatusEnum = iota
	MH_RTB_PIPLINE_STATUS_RUNNING
	MH_RTB_PIPLINE_STATUS_BREAK
)
