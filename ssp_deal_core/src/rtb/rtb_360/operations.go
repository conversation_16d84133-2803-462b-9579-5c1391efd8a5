package rtb_three60

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *Three60Pipline) Init(c *gin.Context, channel string) *Three60Pipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()
	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject Three60RequestObject
	err = json.Unmarshal(bodyContent, &requestObject)

	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()

	p.Context = c
	p.Channel = channel

	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCarrier()
}

// CheckImp 检查Imp对象
func (p *Three60Pipline) CheckImp() *Three60Pipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *Three60Pipline) SetupOsString() *Three60Pipline {
	switch strings.ToLower(p.Request.Device.Os) {
	case "ios":
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	case "android":
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	default:
		// 错误信息
		return p.SetErrorString("invalid os")
	}

	return p
}

// CheckUA 检查UA
func (p *Three60Pipline) CheckUA() *Three60Pipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *Three60Pipline) SetupManufacturer() *Three60Pipline {
	// 每次需要判断状态
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Brand
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *Three60Pipline) SetupConnectType() *Three60Pipline {
	switch p.Request.Device.Network {
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_2G
	case 3:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_3G
	case 4:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	case 5:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_5G
	case 6:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}

	return p
}

// SetupCarrier 设置运营商
func (p *Three60Pipline) SetupCarrier() *Three60Pipline {
	switch p.Request.Device.Carrier {
	case 1:
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case 2:
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case 3:
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}

	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *Three60Pipline) RequestRtbConfig() *Three60Pipline {
	var styleIds []string
	var resultImp []*Three60RequestImpObject
	var configList []*models.RtbConfigByTagIDStu

	tagId := p.Request.Id[0:12]

	for _, imp := range p.Request.Imp {
		price := 0

		if imp.Bidfloor > 0 {
			price = int(imp.Bidfloor)
		}
		if imp.W == 1080 && imp.H == 607 {
			styleIds = append(styleIds, "1")
		} else {
			styleIds = append(styleIds, "2")
		}
		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(p.Context, p.Channel, tagId, p.DeviceOs.String(), styleIds, "", price)

		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		configData := (*adxInfo)[0]
		resultImp = append(resultImp, imp)
		configList = append(configList, &configData)
	}

	// 判断是否有效填充
	if len(configList) == 0 || len(resultImp) == 0 {
		return p.SetErrorString("resultImp or configList imp is empty")
	}

	p.ResultImp = resultImp
	p.ConfigList = configList

	return p
}

// RequestAdxAd 请求广告
func (p *Three60Pipline) RequestAdxAd() *Three60Pipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]
	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: p.Request.App.Bundle,
			AppName:     p.Request.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: len(p.ResultImp),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           p.DeviceOs.String(),
			Imei:         p.Request.Device.Imei,
			ImeiMd5:      p.Request.Device.ImeiMd5,
			OsVersion:    p.Request.Device.OsVersion,
			Model:        p.Request.Device.Model,
			Manufacturer: p.Manufacturer,
			Oaid:         p.Request.Device.Oaid,
			Idfa:         p.Request.Device.Idfa,
			IdfaMd5:      p.Request.Device.IdfaMd5,
			Ua:           p.Request.Device.Ua,
			AndroidID:    p.Request.Device.AndroidId,
			AndroidIDMd5: p.Request.Device.AndroidIdMd5,
			DeviceType:   1,
			IP:           p.Request.Device.Ip,
			ScreenWidth:  p.Request.Device.ScreenWidth,
			ScreenHeight: p.Request.Device.ScreenHeight,
			BootMark:     p.Request.Device.BootMark,
			UpdateMark:   p.Request.Device.UpdateMark,
		},
		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func (p *Three60Pipline) SetupResponse() *Three60Pipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	var (
		responseObject        Three60ResponseObject
		responseDataObject    Three60ResponseDataObject
		responseBidObjectList []*Three60ResponseBidObject
	)

	for index, imp := range p.ResultImp {
		// 如果返回广告少 就跳过没有返回的情况
		if index >= len(p.AdxAdResponse.Data[requestConfig.LocalPosID].List) {
			continue
		}

		// 获得对应的返回广告数据
		adxAd := p.AdxAdResponse.Data[requestConfig.LocalPosID].List[index]

		// 统计
		models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if adxAd.Ecpm < requestConfig.Price {
			continue
		}
		ecpm := adxAd.Ecpm

		winNotice := config.ExternalRtbPriceURL +
			"?uid=" + p.UUID +
			"&tagid=" + requestConfig.TagID +
			"&bp=" + utils.ConvertIntToString(ecpm) +
			"&channel=" + p.Channel +
			"&price=__win_price__" +
			"&log=" + url.QueryEscape(adxAd.Log)

		var (
			responseBidObject     Three60ResponseBidObject
			responseVideoObject   Three60ResponseVideoObject
			responseAppInfoObject Three60ResponseAppInfoObject
			responseImgObjectList []*Three60ResponseImgObject
		)

		responseBidAdmObject := &Three60ResponseAdmObject{
			Id:             1,
			DownloadAd:     adxAd.InteractType,
			Price:          float32(ecpm),
			CreativeId:     adxAd.Crid,
			Title:          adxAd.Title,
			Desc:           adxAd.Description,
			AdIcon:         adxAd.IconURL,
			ClickTrackUrls: adxAd.ClickLink,
		}

		if len(adxAd.DeepLink) > 0 || len(adxAd.MarketURL) > 0 {
			var (
				eventTrack  Three60ResponseAppInfoEventTracksObject
				eventTracks []*Three60ResponseAppInfoEventTracksObject
			)
			var convUrls []string
			if len(adxAd.ConvTracks) > 0 {
				for _, convTracks := range adxAd.ConvTracks {
					if convTracks.ConvType == 10 {
						convUrls = convTracks.ConvURLS
					}
				}
			}
			eventTrack.EventType = 14
			eventTrack.EventTrackUrls = convUrls
			eventTracks = append(eventTracks, &eventTrack)

			responseAppInfoObject.EventTracks = eventTracks
		}

		if adxAd.InteractType == 1 {
			if len(adxAd.MarketURL) > 0 {
				responseAppInfoObject.DeepLink = adxAd.MarketURL
			} else {
				if len(adxAd.DeepLink) > 0 {
					responseAppInfoObject.DeepLink = adxAd.DeepLink
				}
			}

			responseBidAdmObject.Link = adxAd.DownloadURL
			responseAppInfoObject.DownUrl = adxAd.DownloadURL
			responseAppInfoObject.PkgName = adxAd.PackageName
			responseAppInfoObject.AppName = adxAd.AppName
			responseAppInfoObject.Developer = adxAd.Publisher
			responseAppInfoObject.Privacy = adxAd.PrivacyLink
			responseAppInfoObject.Permission = adxAd.Permission
			responseAppInfoObject.Version = adxAd.AppVersion
		} else {
			if len(adxAd.DeepLink) > 0 {
				responseAppInfoObject.DeepLink = adxAd.DeepLink
			} else {
				if len(adxAd.MarketURL) > 0 {
					responseAppInfoObject.DeepLink = adxAd.MarketURL
				}
			}

			if len(adxAd.LandpageURL) > 0 {
				responseBidAdmObject.Link = adxAd.LandpageURL
			} else {
				responseBidAdmObject.Link = adxAd.AdURL
			}
		}
		responseAppInfoObject.PkgName = adxAd.PackageName

		var impTracking []string
		impTracking = append(adxAd.ImpressionLink, winNotice)

		switch adxAd.CrtType {
		case 11:
			for _, imageItem := range adxAd.Image {
				var responseImgObject Three60ResponseImgObject
				responseImgObject.W = imageItem.Width
				responseImgObject.H = imageItem.Height
				responseImgObject.Url = imageItem.URL
				responseImgObjectList = append(responseImgObjectList, &responseImgObject)
			}

			responseBidAdmObject.Img = responseImgObjectList
			responseBidAdmObject.NativeAdType = 2
		case 20:
			responseVideoObject.W = adxAd.Video.Width
			responseVideoObject.H = adxAd.Video.Height
			responseVideoObject.VideoUrl = adxAd.Video.VideoURL
			responseVideoObject.CoverUrl = adxAd.Video.CoverURL
			responseVideoObject.Duration = adxAd.Video.Duration / 1000
			responseBidAdmObject.Video = &responseVideoObject
		}

		responseBidAdmObject.ImpTrackUrls = impTracking
		responseBidAdmObject.AppInfo = &responseAppInfoObject

		responseBidObject.Impid = imp.Id
		responseBidObject.Adm = append(responseBidObject.Adm, responseBidAdmObject)
		responseBidObjectList = append(responseBidObjectList, &responseBidObject)
	}

	// 构造返回结果
	responseDataObject.RequestId = p.Request.Id
	responseDataObject.Ts = time.Now().Unix()
	responseDataObject.Bid = responseBidObjectList

	status := http.StatusOK
	if len(responseBidObjectList) == 0 {
		status = http.StatusNoContent
	}

	responseObject.Code = 0
	responseObject.Data = &responseDataObject
	p.Response = &responseObject
	p.ResponseStatus = status

	return p
}

// ResponseResult 返回结果
func (p *Three60Pipline) ResponseResult() (*Three60ResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &Three60ResponseObject{
			Code:    200411,
			Message: "",
			Data:    nil,
		}, http.StatusNoContent
	}

	return p.Response, http.StatusOK
}

// SetErrorString 设置字符串类型的错误信息
func (p *Three60Pipline) SetErrorString(err string) *Three60Pipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *Three60Pipline) SetError(err error) *Three60Pipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}

func (p *Three60Pipline) Print() *Three60Pipline {
	fmt.Println("<<<<<<<<<<<<<<<< DEBUG <<<<<<<<<<<<<<<")
	fmt.Printf("%+v\n", p)
	fmt.Println(">>>>>>>>>>>>>>>> DEBUG >>>>>>>>>>>>>>>")
	return p
}
