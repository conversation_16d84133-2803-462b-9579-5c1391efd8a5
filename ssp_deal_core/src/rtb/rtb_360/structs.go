package rtb_three60

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// Three60RequestObject Objects
type Three60RequestObject struct {
	Id     string                      `json:"requestId"`
	Device *Three60RequestDeviceObject `json:"device"`
	App    *Three60RequestAppObject    `json:"app"`
	Imp    []*Three60RequestImpObject  `json:"imps"`
}

type Three60RequestDeviceObject struct {
	DeviceId     string `json:"deviceId"`
	Imei         string `json:"imei"`
	ImeiMd5      string `json:"imeiMd5"`
	Idfa         string `json:"idfa"`
	IdfaMd5      string `json:"idfaMd5"`
	AndroidId    string `json:"androidId"`
	AndroidIdMd5 string `json:"androidIdMd5"`
	Oaid         string `json:"oaid"`
	OaidMd5      string `json:"oaidMd5"`
	Brand        string `json:"brand"`
	Model        string `json:"model"`
	Os           string `json:"os"`
	OsVersion    string `json:"osVersion"`
	Mac          string `json:"mac"`
	MacMd5       string `json:"macMd5"`
	Ip           string `json:"ip"`
	Ua           string `json:"userAgent"`
	BootMark     string `json:"boot_mark"`
	UpdateMark   string `json:"update_mark"`
	Network      int    `json:"network"`
	DeviceType   int    `json:"deviceType"`
	Carrier      int    `json:"carrier"`
	ScreenWidth  int    `json:"screenWidth"`
	ScreenHeight int    `json:"screenHeight"`
}

type Three60RequestAppObject struct {
	Name    string `json:"appName"`
	Bundle  string `json:"pkgName"`
	Version string `json:"version"`
}

type Three60RequestImpObject struct {
	Id       int     `json:"id"`
	Count    int     `json:"count"`
	W        int     `json:"width"`
	H        int     `json:"height"`
	Bidfloor float32 `json:"bidfloor"`
}

// Three60ResponseObject Objects
type Three60ResponseObject struct {
	Code    int                        `json:"code"`
	Message string                     `json:"message"`
	Data    *Three60ResponseDataObject `json:"data"`
}

type Three60ResponseDataObject struct {
	RequestId string                      `json:"requestId"`
	Ts        int64                       `json:"ts"`
	Bid       []*Three60ResponseBidObject `json:"groups"`
}

type Three60ResponseBidObject struct {
	Impid int                         `json:"impId"`
	Adm   []*Three60ResponseAdmObject `json:"ads"`
}

type Three60ResponseAdmObject struct {
	Id             int                           `json:"id,omitempty"`
	AdWidth        int                           `json:"adWidth,omitempty"`
	AdHeight       int                           `json:"adHeight,omitempty"`
	AdType         int                           `json:"adType,omitempty"`
	NativeAdType   int                           `json:"nativeAdType,omitempty"`
	DownloadAd     int                           `json:"downloadAd,omitempty"`
	Price          float32                       `json:"price,omitempty"`
	CreativeId     string                        `json:"creativeId,omitempty"`
	Title          string                        `json:"title"`
	Desc           string                        `json:"desc"`
	Link           string                        `json:"link"`
	AdIcon         string                        `json:"adIcon"`
	MatterIcon     string                        `json:"matterIcon,omitempty"`
	ClickTrackUrls []string                      `json:"clickTrackUrls"`
	ImpTrackUrls   []string                      `json:"impTrackUrls"`
	Video          *Three60ResponseVideoObject   `json:"video,omitempty"`
	AppInfo        *Three60ResponseAppInfoObject `json:"appInfo,omitempty"`
	Img            []*Three60ResponseImgObject   `json:"imgs,omitempty"`
}

type Three60ResponseAppInfoObject struct {
	AppName     string                                     `json:"name,omitempty"`
	PkgName     string                                     `json:"pkgName,omitempty"`
	Version     string                                     `json:"version,omitempty"`
	DownUrl     string                                     `json:"downUrl,omitempty"`
	DeepLink    string                                     `json:"deepLink,omitempty"`
	Developer   string                                     `json:"softCorpName,omitempty"`
	Permission  string                                     `json:"usesPermission,omitempty"`
	Privacy     string                                     `json:"sensitiveUrl,omitempty"`
	Size        int32                                      `json:"size,omitempty"`
	EventTracks []*Three60ResponseAppInfoEventTracksObject `json:"eventTracks,omitempty"`
}

type Three60ResponseAppInfoEventTracksObject struct {
	EventType      int      `json:"eventType"`
	EventTrackUrls []string `json:"eventTrackUrls"`
}

type Three60ResponseImgObject struct {
	W   int    `json:"width"`
	H   int    `json:"height"`
	Url string `json:"url"`
}

type Three60ResponseVideoObject struct {
	Duration  int    `json:"duration"`
	MimeType  int    `json:"mimeType,omitempty"`
	Size      int    `json:"length,omitempty"`
	W         int    `json:"width"`
	H         int    `json:"height"`
	VideoType int    `json:"videoType,omitempty"`
	VideoUrl  string `json:"videoUrl"`
	CoverUrl  string `json:"coverUrl"`
}

type Three60Pipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	Request  *Three60RequestObject
	Response *Three60ResponseObject

	ConfigList    []*models.RtbConfigByTagIDStu
	ResultImp     []*Three60RequestImpObject
	AdxAdResponse *models.MHResp
}
