package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByZhangyue ...
// https://q7w8vltyes.feishu.cn/docx/doxcnfSpsiQ7mBQieX44Ekajohg
func HandleByZhangyue(c *gin.Context, channel string) (*map[string]interface{}, int) {

	bodyContent, err := c.GetRawData()
	// fmt.Println(timeBegin)
	var zhangyueReq ZhangyueReq

	err = json.Unmarshal(bodyContent, &zhangyueReq)

	if err != nil {
		fmt.Println(err)
		return jsonZhangyueNoBidReturn("parser error")
	}
	// fmt.Println(zhangyueReq.ID)
	// fmt.Println(zhangyueReq.Device)
	// fmt.Println(zhangyueReq.ImpList[0].BidFloor)
	// fmt.Println(zhangyueReq.Device.Geo.Lat)
	// fmt.Println(zhangyueReq.Device.Geo.Lon)

	//fmt.Println("zhangyue req: " + string(bodyContent))

	var reqOs = ""

	if zhangyueReq.Device.Os == "Android" {
		reqOs = "android"
	} else if zhangyueReq.Device.DeviceType == 1 || zhangyueReq.Device.DeviceType == 3 {
		reqOs = "ios"
	} else if zhangyueReq.Device.DeviceType == 2 || zhangyueReq.Device.DeviceType == 5 {
		reqOs = "android"
	}

	// 3: iOS, 4: Android
	if reqOs == "android" || reqOs == "ios" {
	} else {
		return jsonZhangyueNoBidReturn("wrong os")
	}

	reqDeivceMake := zhangyueReq.Device.Brand
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(zhangyueReq.Device.UA) == 0 {
		return jsonZhangyueNoBidReturn("wrong ua")
	}

	/*
		0-未知
		1-wifi
		2-2G
		3-3G
		4-4G
		5–5G

		0-未知
		1-移动
		2-联通
		3-电信
	*/
	reqConnectType := 0
	if zhangyueReq.Device.Network == 2 {
		reqConnectType = 1
	} else if zhangyueReq.Device.Network == 4 {
		reqConnectType = 2
	} else if zhangyueReq.Device.Network == 5 {
		reqConnectType = 3
	} else if zhangyueReq.Device.Network == 6 {
		reqConnectType = 4
	} else if zhangyueReq.Device.Network == 7 {
		reqConnectType = 7
	}

	reqCarrier := 0
	if zhangyueReq.Device.Carrier == "46000" {
		reqCarrier = 1
	} else if zhangyueReq.Device.Carrier == "46001" {
		reqCarrier = 2
	} else if zhangyueReq.Device.Carrier == "46003" {
		reqCarrier = 3
	}

	if len(zhangyueReq.ImpList) == 0 {
		return jsonZhangyueNoBidReturn("wrong imp: empty")
	}

	var reqOKImps []ZhangyueReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	var reqOKImpsCounter [100]int

	for index, item := range zhangyueReq.ImpList {
		//fmt.Println("=======================")
		//fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqOKImpsCounter[index] = 0

		reqTagID := item.PID
		if reqOs == "android" {
			// reqTagID = "zhangyue_" + item.PID + "_" + utils.ConvertIntToString(templateStyle) + "_android"
		} else if reqOs == "ios" {
			// reqTagID = "zhangyue_" + item.PID + "_" + utils.ConvertIntToString(templateStyle) + "_ios"
		} else {
			continue
		}

		reqPrice := item.BidFloor
		if reqPrice == 0 {
			//return jsonZhangyueNoBidReturn("wrong price")
			continue
		}

		var rtbConfigArrayByTagID *[]models.RtbConfigByTagIDStu

		if len(item.Nativead.TemplateIds) != 0 {
			rtbConfigArrayByTagID = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, reqTagID, reqOs, item.Nativead.TemplateIds, "", int(reqPrice))
		} else {
			rtbConfigArrayByTagID = models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))
		}

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println("rtbConfigArrayByTagID", rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			//return jsonZhangyueNoBidReturn("not active 1")
			continue
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)

		reqOKImpsCounter[index]++

	}

	if len(reqOKImps) == 0 {
		return jsonZhangyueNoBidReturn("wrong imp: reqOKImps is empty")
	}

	// fmt.Println("len of reqOKImps", len(reqOKImps))

	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	if len(zhangyueReq.User.UPS) > 0 {

		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("bigdata extra al panic:", err)
				}
			}()

			// 序号	 数字	 app名称	包名
			// 1	90000	支付宝	   com.eg.android.AlipayGphone
			// 2	90001	京东	com.jingdong.app.mall
			// 3	90002	懂车帝	com.ss.android.auto
			// 17	90019	淘宝	 com.taobao.taobao
			// 19	90021	京东金融	 com.jd.jrapp
			// 23	90026	新浪微博	com.sina.weibo
			// 24	90027	小红书	com.xingin.xhs
			// 33	90036	拼多多	com.xunmeng.pinduoduo
			// 44	90047	映客直播	com.meelive.ingkee
			// 53	90056	唯品会	com.achievo.vipshop
			// 61	90064	美团	com.sankuai.meituan
			// 73	90076	自如	com.ziroom.ziroomcustomer
			// 75	90080	我爱我家	com.wawj.app.t
			// 77	90082	滴滴出行	com.sdu.didi.psnger
			// 78	90083	天猫	com.tmall.wireless
			// 79	90084	饿了么	me.ele
			// 81	90086	美团外卖	com.sankuai.meituan.takeoutnew
			// 83	90090	美图秀秀	com.mt.mtxx.mtxx
			// 93	90101	淘特	com.taobo.litetao
			// 95	90103	点淘	com.taobao.live
			var appListArray []string
			for _, tmpUPS := range zhangyueReq.User.UPS {
				switch tmpUPS {
				case 90000:
					appListArray = append(appListArray, "支付宝")
				case 90001:
					appListArray = append(appListArray, "京东")
				case 90002:
					appListArray = append(appListArray, "懂车帝")
				case 90019:
					appListArray = append(appListArray, "淘宝")
				case 90021:
					appListArray = append(appListArray, "京东金融")
				case 90026:
					appListArray = append(appListArray, "新浪微博")
				case 90027:
					appListArray = append(appListArray, "小红书")
				case 90036:
					appListArray = append(appListArray, "拼多多")
				case 90047:
					appListArray = append(appListArray, "映客直播")
				case 90056:
					appListArray = append(appListArray, "唯品会")
				case 90064:
					appListArray = append(appListArray, "美团")
				case 90076:
					appListArray = append(appListArray, "自如")
				case 90080:
					appListArray = append(appListArray, "我爱我家")
				case 90082:
					appListArray = append(appListArray, "滴滴出行")
				case 90083:
					appListArray = append(appListArray, "天猫")
				case 90084:
					appListArray = append(appListArray, "饿了么")
				case 90086:
					appListArray = append(appListArray, "美团外卖")
				case 90090:
					appListArray = append(appListArray, "美图秀秀")
				case 90101:
					appListArray = append(appListArray, "淘特")
				case 90103:
					appListArray = append(appListArray, "点淘")
				}
			}
		}()
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(zhangyueReq.Device.Caid) > 0 {
		var caidArray []ZhangyueReqDeviceCaid
		_ = json.Unmarshal([]byte(zhangyueReq.Device.Caid), &caidArray)
		for _, caidItem := range caidArray {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.Id
			caidMulti.CAIDVersion = caidItem.Version
			caidMultiList = append(caidMultiList, caidMulti)
		}
	}
	var caidInfo ZhangyueReqDeviceCaidInfo
	if len(zhangyueReq.Device.CaidInfo) > 0 {
		_ = json.Unmarshal([]byte(zhangyueReq.Device.CaidInfo), &caidInfo)
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: zhangyueReq.App.PackageName,
			AppName:     zhangyueReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 reqOs,
			OsVersion:          zhangyueReq.Device.Osv,
			Model:              zhangyueReq.Device.Model,
			Manufacturer:       reqDeivceMake,
			Imei:               zhangyueReq.Device.Imei,
			ImeiMd5:            zhangyueReq.Device.ImeiMd5,
			AndroidID:          zhangyueReq.Device.AndroidID,
			Oaid:               zhangyueReq.Device.Oaid,
			Idfa:               zhangyueReq.Device.Idfa,
			Ua:                 zhangyueReq.Device.UA,
			ScreenWidth:        utils.ConvertStringToInt(zhangyueReq.Device.Width),
			ScreenHeight:       utils.ConvertStringToInt(zhangyueReq.Device.Height),
			DeviceType:         1,
			IP:                 zhangyueReq.Device.IP,
			AppStoreVersion:    zhangyueReq.Device.AppStoreVersion,
			HMSCoreVersion:     zhangyueReq.Device.HMSCoreVersion,
			BootMark:           zhangyueReq.Device.BootMark,
			UpdateMark:         zhangyueReq.Device.UpdateMark,
			CAIDMulti:          caidMultiList,
			Country:            caidInfo.Country,
			SystemUpdateSec:    caidInfo.SystemUpdateSec,
			DeviceStartSec:     caidInfo.DeviceStartSec,
			PhysicalMemoryByte: caidInfo.PhysicalMemoryByte,
			HardwareModel:      caidInfo.HardwareModel,
			HardwareMachine:    caidInfo.HardwareMachine,
			Language:           caidInfo.Language,
			HarddiskSizeByte:   caidInfo.HarddiskSizeByte,
			TimeZone:           caidInfo.TimeZone,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}

	// fmt.Println("reqStu", reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	// fmt.Println(mhResp.Ret)
	// fmt.Println("zhangyue 2")

	// fmt.Println(mhResp.Data)
	// fmt.Println("zhangyue 3")

	if mhResp.Ret != 0 {
		return jsonZhangyueNoBidReturn("no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonZhangyueNoBidReturn("no fill")
	}

	var impItem ZhangyueReqImp
	for _, imp := range reqOKImps {
		if imp.PID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if len(impItem.PID) == 0 {
		return jsonZhangyueNoBidReturn("no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////

	zhangyueRespBidArrayMap := []map[string]interface{}{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		displayID := impItem.Type

		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// 样式id
		destStyleID := reqRtbConfig.ImageStyleID
		if isVideoType == 1 {
			destStyleID = reqRtbConfig.VideoStyleID
		}

		templateId := destStyleID
		fmt.Println("displayID:", displayID, "templateId:", templateId)

		// fmt.Println("displayID", displayID)

		zhangyueBidObjItemMap := map[string]interface{}{}
		zhangyueBidObjItemMap["ader_id"] = "10120" // TODO: ader_id
		zhangyueBidObjItemMap["price"] = ecpm
		//zhangyueBidObjItemMap["cid"] = crid // 白名单用户不用填cid

		//0: 横幅   1: 开屏    3: 插屏   4: 视频贴片   5: 原生   6: VR广告   7: 激励视频
		// 0  1  3  4  5  7

		zhangyueAdmJsonObj := map[string]interface{}{}

		if displayID == 0 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title
			var imgurls []string
			for _, url := range mhDataItem.Image {
				imgurls = append(imgurls, url.URL)
			}
			zhangyueAdmJsonObj["imgurl"] = imgurls
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName
		} else if displayID == 1 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title
			var imgurls []string
			for _, url := range mhDataItem.Image {
				imgurls = append(imgurls, url.URL)
			}
			zhangyueAdmJsonObj["imgurl"] = imgurls
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName
		} else if displayID == 3 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title
			var imgurls []string
			for _, url := range mhDataItem.Image {
				imgurls = append(imgurls, url.URL)
			}
			zhangyueAdmJsonObj["imgurl"] = imgurls
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName
		} else if displayID == 4 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title
			var imgurls []string
			for _, url := range mhDataItem.Image {
				imgurls = append(imgurls, url.URL)
			}
			zhangyueAdmJsonObj["imgurl"] = imgurls
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName
			zhangyueAdmJsonObj["content"] = mhDataItem.Description
			zhangyueBidObjItemMap["templateid"] = templateId
		} else if displayID == 5 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title

			if isVideoType == 1 {
				zhangyueAdmJsonObj["imgurl"] = mhDataItem.Video.CoverURL
				zhangyueAdmJsonObj["videourl"] = mhDataItem.Video.VideoURL
				zhangyueAdmJsonObj["videoduration"] = mhDataItem.Video.Duration / 1000

				zhangyueBidObjItemMap["templateid"] = templateId

			} else {
				var imgurls []string
				for _, url := range mhDataItem.Image {
					imgurls = append(imgurls, url.URL)
				}
				zhangyueAdmJsonObj["imgurl"] = imgurls

				zhangyueBidObjItemMap["templateid"] = templateId

			}
			zhangyueAdmJsonObj["content"] = mhDataItem.Description
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName
			zhangyueAdmJsonObj["download_url"] = mhDataItem.DownloadURL

		} else if displayID == 6 {
			// 激励视频
			zhangyueRewardVideoJsonObj := map[string]interface{}{}

			zhangyueRewardVideoJsonObj["duration"] = mhDataItem.Video.Duration
			zhangyueRewardVideoJsonObj["title"] = mhDataItem.Title
			zhangyueRewardVideoJsonObj["desc"] = mhDataItem.Description
			zhangyueRewardVideoJsonObj["cover_url"] = mhDataItem.Video.CoverURL
			zhangyueRewardVideoJsonObj["video_url"] = mhDataItem.Video.VideoURL
			zhangyueRewardVideoJsonObj["icon_url"] = mhDataItem.IconURL
			if mhDataItem.InteractType == 0 {
				zhangyueRewardVideoJsonObj["btn_txt"] = "查看详情"
			} else {
				zhangyueRewardVideoJsonObj["btn_txt"] = "点击下载"
			}
			zhangyueRewardVideoJsonObj["download_url"] = mhDataItem.DownloadURL

			zhangyueBidObjItemMap["reward_video"] = zhangyueRewardVideoJsonObj

		} else if displayID == 7 {
			zhangyueAdmJsonObj["title"] = mhDataItem.Title
			zhangyueAdmJsonObj["cover"] = mhDataItem.Video.CoverURL
			zhangyueAdmJsonObj["end_cover"] = mhDataItem.Video.CoverURL
			zhangyueAdmJsonObj["videourl"] = mhDataItem.Video.VideoURL
			zhangyueAdmJsonObj["videoduration"] = mhDataItem.Video.Duration / 1000
			zhangyueAdmJsonObj["icon"] = mhDataItem.IconURL
			zhangyueAdmJsonObj["icon_title"] = mhDataItem.AppName

			if mhDataItem.InteractType == 0 {
				zhangyueAdmJsonObj["action_text"] = "查看详情"
			} else {
				zhangyueAdmJsonObj["action_text"] = "点击下载"
			}
			zhangyueBidObjItemMap["templateid"] = templateId
		}

		jsonByte, _ := json.Marshal(zhangyueAdmJsonObj)

		if displayID == 0 || displayID == 1 || displayID == 3 {
			zhangyueBidObjItemMap["adm"] = mhDataItem.Image[0].URL
		} else {
			zhangyueBidObjItemMap["adm"] = string(jsonByte)
		}

		if isVideoType == 1 {
			zhangyueBidObjItemMap["ad_type"] = 2
		} else {
			zhangyueBidObjItemMap["ad_type"] = 1
		}

		if reqOs == "ios" {
			zhangyueBidObjItemMap["adck"] = 2
			if mhDataItem.InteractType == 0 {
				zhangyueBidObjItemMap["durl"] = mhDataItem.LandpageURL
			} else if mhDataItem.InteractType == 1 {
				zhangyueBidObjItemMap["durl"] = mhDataItem.DownloadURL
			}
		} else {
			if mhDataItem.InteractType == 0 {
				// H5
				zhangyueBidObjItemMap["adck"] = 1
				zhangyueBidObjItemMap["durl"] = mhDataItem.LandpageURL

			} else if mhDataItem.InteractType == 1 {
				// 直接下载
				zhangyueBidObjItemMap["adck"] = 2
				zhangyueBidObjItemMap["durl"] = mhDataItem.DownloadURL
			}
		}

		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				zhangyueBidObjItemMap["deep_link"] = mhDataItem.DeepLink
				if reqOs == "ios" {
					zhangyueBidObjItemMap["universal_link"] = mhDataItem.DeepLink
				}
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					zhangyueBidObjItemMap["deep_link"] = mhDataItem.MarketURL
					if reqOs == "ios" {
						zhangyueBidObjItemMap["universal_link"] = mhDataItem.MarketURL
					}
				}
			}
		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				zhangyueBidObjItemMap["deep_link"] = mhDataItem.MarketURL
				if reqOs == "ios" {
					zhangyueBidObjItemMap["universal_link"] = mhDataItem.MarketURL
				}
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					zhangyueBidObjItemMap["deep_link"] = mhDataItem.DeepLink
					if reqOs == "ios" {
						zhangyueBidObjItemMap["universal_link"] = mhDataItem.DeepLink
					}
				}
			}
		}

		if len(mhDataItem.PackageName) > 0 {
			zhangyueBidObjItemMap["package_name"] = mhDataItem.PackageName
		}

		if len(mhDataItem.AppName) > 0 {
			zhangyueBidObjItemMap["app_name"] = mhDataItem.AppName
		}

		// 新增5要素 2022-03-18
		// https://gitee.com/ChuanyueAds/dsp-docs/blob/master/pages/request_and_response.md#bid%E4%BF%A1%E6%81%AFbidresponseseatbidbid
		if len(mhDataItem.Publisher) > 0 {
			zhangyueBidObjItemMap["app_developer"] = mhDataItem.Publisher
		}
		if len(mhDataItem.AppVersion) > 0 {
			zhangyueBidObjItemMap["app_version"] = mhDataItem.AppVersion
		}
		if len(mhDataItem.PrivacyLink) > 0 {
			zhangyueBidObjItemMap["app_privacy_url"] = mhDataItem.PrivacyLink
		}
		if len(mhDataItem.Permission) > 0 {
			zhangyueBidObjItemMap["app_perm_content"] = mhDataItem.Permission
		}
		if mhDataItem.PackageSize > 0 {
			zhangyueBidObjItemMap["app_size"] = mhDataItem.PackageSize
		}

		// appstore bundle
		if len(mhDataItem.AppstorePackageName) > 0 {
			zhangyueBidObjItemMap["appstore_package_name"] = mhDataItem.AppstorePackageName
		}

		// if len(mhDataItem.IconURL) > 0 {
		// 	zhangyueBidObjItemMap["from_logo"] = mhDataItem.IconURL
		// }

		// deeplink tracking
		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}

		if len(deepLinkTrackOKArray) > 0 {
			zhangyueBidObjItemMap["deeplink_trackers"] = deepLinkTrackOKArray
		}
		// if len(deepLinkTrackFailedArray) > 0 {
		//zhangyueRespBidAdmNativeDeepLinkEventMap["failed"] = deepLinkTrackFailedArray
		// }

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=%%PRICE%%" + "&log=" + url.QueryEscape(mhDataItem.Log)
		impressionLinkMap := mhDataItem.ImpressionLink
		impressionLinkMap = append(impressionLinkMap, winURL)
		zhangyueBidObjItemMap["nurl"] = impressionLinkMap

		// var deepLinkTrackOKArray []string
		// var deepLinkTrackFailedArray []string
		// for _, trackItem := range mhDataItem.ConvTracks {
		// 	if trackItem.ConvType == 10 {
		// 		for _, trackEventItem := range trackItem.ConvURLS {
		// 			deepLinkTrackOKURL := strings.Replace(trackEventItem, "__DP_RESULT__", "0", -1)
		// 			deepLinkTrackOKArray = append(deepLinkTrackOKArray, deepLinkTrackOKURL)

		// 			deepLinkTrackFailedURL := strings.Replace(trackEventItem, "__DP_RESULT__", "1", -1)
		// 			deepLinkTrackFailedURL = strings.Replace(deepLinkTrackFailedURL, "__DP_REASON__", "3", -1)
		// 			deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, deepLinkTrackFailedURL)
		// 		}
		// 	}
		// }
		// zhangyueBidObjItemMap["dpsucc"] = deepLinkTrackOKArray
		// zhangyueBidObjItemMap["dpfail"] = deepLinkTrackFailedArray

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", zhangyueReq.Device.Width, -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", zhangyueReq.Device.Height, -1)
			// clkItem = strings.Replace(clkItem, "__DOWN_X__", "__re_down_x__", -1)
			// clkItem = strings.Replace(clkItem, "__DOWN_Y__", "__re_down_y__", -1)
			// clkItem = strings.Replace(clkItem, "__UP_X__", "__re_up_x__", -1)
			// clkItem = strings.Replace(clkItem, "__UP_Y__", "__re_up_y__", -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}
		zhangyueBidObjItemMap["curl"] = clkTrackArray

		// video start finish link
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		if isVideoType == 1 {
			for _, trackItem := range mhDataItem.Video.EventTracks {
				if trackItem.EventType == 100 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
					}
				} else if trackItem.EventType == 103 {
					for _, trackEventItem := range trackItem.EventURLS {
						videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
					}
				}
			}
		}

		if len(videoStartTrackArray) > 0 {
			zhangyueBidObjItemMap["video_start"] = videoStartTrackArray
		}

		if len(videoFinishTrackArray) > 0 {
			zhangyueBidObjItemMap["video_complete"] = videoFinishTrackArray
		}

		zhangyueRespBidArrayMap = append(zhangyueRespBidArrayMap, zhangyueBidObjItemMap)

	}

	// bid
	zhangyueRespBidObjMap := map[string]interface{}{}
	zhangyueRespBidObjMap["bid"] = zhangyueRespBidArrayMap

	// seat id
	zhangyueRespPIDArrayMap := []map[string]interface{}{}
	zhangyueRespPIDArrayMap = append(zhangyueRespPIDArrayMap, zhangyueRespBidObjMap)

	// resp
	zhangyueRespMap := map[string]interface{}{}
	zhangyueRespMap["id"] = zhangyueReq.ID
	zhangyueRespMap["seatbid"] = zhangyueRespPIDArrayMap

	return jsonZhangyueOKBidReturn(&zhangyueRespMap)
	//return jsonZhangyueNoBidReturn("end")
}

func jsonZhangyueOKBidReturn(resp *map[string]interface{}) (*map[string]interface{}, int) {
	return resp, 200
}

func jsonZhangyueNoBidReturn(reason string) (*map[string]interface{}, int) {
	// fmt.Println(reason)

	// c.JSON(204, nil)
	return nil, 204
}

type ZhangyueReq struct {
	ID      string            `json:"id"`
	ImpList []ZhangyueReqImp  `json:"imp"`
	Site    ZhangyueReqSite   `json:"site"`
	App     ZhangyueReqApp    `json:"app"`
	Device  ZhangyueReqDevice `json:"device"`
	User    ZhangyueReqUser   `json:"user"`
	Bcat    []string          `json:"bcat"`
}

type ZhangyueReqSite struct {
	ID   string `json:"id"`
	Page string `json:"page"`
	Ref  string `json:"ref"`
}

type ZhangyueReqUser struct {
	UPS []int `json:"ups"`
}

type ZhangyueReqImp struct {
	ID       string                 `json:"id"`
	PID      string                 `json:"pid"`
	Type     int                    `json:"type"` // 广告类型（0:横幅,1:开屏,3:插屏,4:视频,5:原生） （0,1,3 对应banner 4. 对应video 5. nativead）
	Ext      ZhangyueReqImpExt      `json:"ext"`
	BidFloor int                    `json:"bidfloor"`
	Pmp      ZhangyueReqImpPmp      `json:"pmp"`
	Nativead ZhangyueReqImpNativead `json:"nativead"`
}

type ZhangyueReqImpExt struct {
	Atype int `json:"atype"`
}

type ZhangyueReqImpNativead struct {
	TemplateIds []string `json:"template_ids"`
}
type ZhangyueReqImpPmp struct {
	Deals []ZhangyueReqImpPmpDeals `json:"deals"`
}

type ZhangyueReqImpPmpDeals struct {
	ID    string `json:"id"`
	At    int    `json:"at"`
	Price int    `json:"price"`
}

type ZhangyueReqApp struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	PackageName string `json:"bundle"`
	Cat         []int  `json:"cat"`
	Ver         string `json:"ver"`
}

type ZhangyueReqDevice struct {
	Os              string               `json:"os"`
	Osv             string               `json:"osv"`
	IP              string               `json:"ip"`
	IPv6            string               `json:"ipv6"`
	UA              string               `json:"ua"`
	Carrier         string               `json:"carrier"`
	Network         int                  `json:"connectiontype"`
	DeviceType      int                  `json:"devicetype"`
	Imei            string               `json:"imei"`
	ImeiMd5         string               `json:"imei_md5"`
	Oaid            string               `json:"oaid"`
	Imsi            string               `json:"imsi"`
	Mac             string               `json:"mac"`
	MacMd5          string               `json:"mac_md5"`
	Idfa            string               `json:"idfa"`
	Caid            string               `json:"caid"`
	CaidInfo        string               `json:"caid_info"`
	AndroidID       string               `json:"androidid"`
	Openudid        string               `json:"openudid"`
	Brand           string               `json:"make"`
	Model           string               `json:"model"`
	Lang            string               `json:"lan"`
	Height          string               `json:"h"`
	Width           string               `json:"w"`
	Orientation     string               `json:"orientation"`
	OperatorType    int                  `json:"operator_type"`
	Geo             ZhangyueReqDeviceGeo `json:"geo"`
	AppStoreVersion string               `json:"appstore_version"`
	HMSCoreVersion  string               `json:"huawei_hms_version"`
	BootMark        string               `json:"device_boot_mark"`
	UpdateMark      string               `json:"device_update_mark"`
}

type ZhangyueReqDeviceGeo struct {
	Lat     float32 `json:"lat"`
	Lon     float32 `json:"lon"`
	City    string  `json:"city"`
	Metro   string  `json:"metro"`
	Country string  `json:"country"`
	Zip     string  `json:"zip"`
}

type ZhangyueReqDeviceCaid struct {
	Id      string `json:"id"`
	Version string `json:"version"`
}

type ZhangyueReqDeviceCaidInfo struct {
	Country            string `json:"country"`
	SystemUpdateSec    string `json:"system_update_sec"`
	DeviceStartSec     string `json:"device_start_sec"`
	PhysicalMemoryByte string `json:"physical_memory_byte"`
	HardwareModel      string `json:"hardware_model"`
	HardwareMachine    string `json:"hardware_machine"`
	Language           string `json:"language"`
	HarddiskSizeByte   string `json:"harddisk_size_byte"`
	DeviceNameMd5      string `json:"device_name_md5"`
	TimeZone           string `json:"time_zone"`
}
