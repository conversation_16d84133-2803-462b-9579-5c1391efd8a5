package rtb_fengfei

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
	"io"
	"mh_proxy/pb/fengfei"
	"net/http"
	"testing"
)

func TestAes(t *testing.T) {
	price := "DH892aBMgMh6FW0hF0H6-Q"
	key := "FriJCMGusRGBkw54"
	tmpPrice, _ := base64.RawURLEncoding.DecodeString(price)
	decodePrice := AESDecrypt(tmpPrice, []byte(key))
	fmt.Println(string(decodePrice))
}

func Test_HandleByFancy(t *testing.T) {
	var nativeList []*fengfei.BidRequest_Imp_Native
	native := fengfei.BidRequest_Imp_Native{
		Type: "O_FULL_SCREEN",
	}
	native1 := fengfei.BidRequest_Imp_Native{
		Type: "O_HALF_SCREEN",
	}
	nativeList = append(nativeList, &native, &native1)

	var impList []*fengfei.BidRequest_Imp
	imp := &fengfei.BidRequest_Imp{
		Adtype:   1,
		Bidfloor: 500,
		Id:       uuid.NewV4().String(),
		Tagid:    "130",
		Native:   nativeList,
	}
	impList = append(impList, imp)
	request := &fengfei.BidRequest{
		Id:       uuid.NewV4().String(),
		At:       0,
		Platform: 0,
		Imp:      impList,
		App: &fengfei.BidRequest_App{
			Id:     "28",
			Name:   "凤飞",
			Bundle: "com.fengfei.cn",
			Ver:    "1.2.3",
		},
		Device: &fengfei.BidRequest_Device{
			W:              1080,
			H:              2236,
			Carrier:        1,
			Connectiontype: 5,
			Devicetype:     1,
			Ua:             "Mozilla/5.0 (Linux; Android 11; PEGM00 Build/RKQ1.200903.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36",
			Ip:             "***************",
			Make:           "OPPO",
			Model:          "PEGM00",
			Brand:          "OPPO",
			Os:             "Android",
			Osv:            "11",
			Dpidmd5:        "1ec0c7fde56207cdacc4bbec91d0701c",
			Oaid:           "50543940-69e6-4590-8851-6adbc76b4df8",
			UpdateMark:     "1561353344.550000000",
			BootMark:       "90f2fe3e-460b-4d78-a65a-614bd43301a1",
		},
		User: &fengfei.BidRequest_User{
			Id:          "",
			Gender:      0,
			Age:         0,
			InstallApps: nil,
		},
		Test:          0,
		Bindus:        nil,
		DeliverTrade:  nil,
		Forbidenwords: nil,
		Onlywords:     nil,
		Site: &fengfei.BidRequest_Site{
			Id:         "",
			Name:       "",
			Domain:     "",
			Page:       "",
			Ref:        "",
			Siteadsize: nil,
			Keywords:   nil,
			Cat:        nil,
			Ext:        "",
		},
	}
	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/request?channel=58", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	ksResp := &fengfei.BidResponse{}
	err = proto.Unmarshal(bodyContent, ksResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(ksResp)
	fmt.Println(string(marshal))
}
