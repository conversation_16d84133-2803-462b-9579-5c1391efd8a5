package rtb_fengfei

import (
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/fengfei"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleFengfei(c *gin.Context, channel string) (*fengfei.BidResponse, int) {
	bigdataUID := uuid.NewV4().String()
	bodyContent, _ := c.GetRawData()

	var req fengfei.BidRequest
	err := proto.Unmarshal(bodyContent, &req)
	if err != nil {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var deviceOs string
	switch strings.ToLower(req.GetDevice().GetOs()) {
	case "ios":
		deviceOs = "ios"
	case "android":
		deviceOs = "android"
	default:
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetDevice().GetConnectiontype() {
	case 1:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	carrier := req.GetDevice().GetCarrier()

	var resultfulImp []*fengfei.BidRequest_Imp
	var configList []*models.RtbConfigByTagIDStu

	for _, imp := range req.GetImp() {
		price := imp.GetBidfloor()
		var adxInfo *[]models.RtbConfigByTagIDStu

		if imp.GetAdtype() == 1 {
			var styleIds []string
			for _, nativeItem := range imp.GetNative() {
				styleIds = append(styleIds, nativeItem.GetType())
			}
			adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.GetTagid(), deviceOs, styleIds, "", int(price))
		}

		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		configData := (*adxInfo)[0]
		resultfulImp = append(resultfulImp, imp)
		configList = append(configList, &configData)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	manufacturer := req.GetDevice().GetBrand()
	if deviceOs == "ios" {
		manufacturer = "Apple"
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.App.Bundle,
			AppName:     req.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:               deviceOs,
			OsVersion:        req.GetDevice().GetOsv(),
			Model:            req.GetDevice().GetModel(),
			Manufacturer:     manufacturer,
			Imei:             req.GetDevice().GetDid(),
			ImeiMd5:          req.GetDevice().GetDidmd5(),
			AndroidIDMd5:     req.GetDevice().GetDpidmd5(),
			Oaid:             req.GetDevice().GetOaid(),
			Idfa:             req.GetDevice().GetIfa(),
			Ua:               req.GetDevice().GetUa(),
			ScreenWidth:      int(req.GetDevice().GetW()),
			ScreenHeight:     int(req.GetDevice().GetH()),
			DeviceType:       1,
			IP:               req.GetDevice().GetIp(),
			BootMark:         req.GetDevice().GetBootMark(),
			UpdateMark:       req.GetDevice().GetUpdateMark(),
			Language:         req.GetDevice().GetLanguage(),
			HardwareModel:    req.GetDevice().GetHardwareModel(),
			DeviceNameMd5:    req.GetDevice().GetDeviceNameMd5(),
			TimeZone:         req.GetDevice().GetTimeZone(),
			Country:          req.GetDevice().GetCountry(),
			HarddiskSizeByte: req.GetDevice().GetSdFreeSpace(),
		},

		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     int(carrier),
		},
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var impItem *fengfei.BidRequest_Imp
	for _, imp := range resultfulImp {
		if imp.GetTagid() == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var seatbid fengfei.BidResponse_SeatBid
	var seatBids []*fengfei.BidResponse_SeatBid
	var bids []*fengfei.BidResponse_SeatBid_Bid

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE_AES}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		bid := &fengfei.BidResponse_SeatBid_Bid{
			Adtype: impItem.Adtype,
			Price:  int64(ecpm),
			Id:     uuid.NewV4().String(),
			Impid:  mhDataItem.AdID,
			Nurl:   winURL,
			Aimps:  mhDataItem.ImpressionLink,
			Aclks:  mhDataItem.ClickLink,
		}
		if impItem.Adtype == 1 {
			var dplurl string
			if mhDataItem.InteractType == 0 {
				if len(mhDataItem.DeepLink) > 0 {
					dplurl = mhDataItem.DeepLink
				} else {
					if len(mhDataItem.MarketURL) > 0 {
						dplurl = mhDataItem.MarketURL
					}
				}
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					dplurl = mhDataItem.MarketURL
				} else {
					if len(mhDataItem.DeepLink) > 0 {
						dplurl = mhDataItem.DeepLink
					}
				}
			}
			var convUrls []string
			if len(dplurl) > 0 {
				if len(mhDataItem.ConvTracks) > 0 {
					for _, convTracks := range mhDataItem.ConvTracks {
						if convTracks.ConvType == 10 {
							convUrls = convTracks.ConvURLS
						}
					}
				}
			}
			var app fengfei.BidResponse_SeatBid_Bid_Creative_App
			creative := &fengfei.BidResponse_SeatBid_Bid_Creative{
				InteractionType: 1,
				Id:              mhDataItem.Crid,
				Title:           mhDataItem.Title,
				Desc:            mhDataItem.Description,
				Landpage:        mhDataItem.LandpageURL,
				Dplurl:          dplurl,
				Icon:            mhDataItem.IconURL,
				Dpsurls:         convUrls,
			}
			switch mhDataItem.CrtType {
			case 11:
				if mhDataItem.Image == nil {
					continue
				}
				for _, imageItem := range mhDataItem.Image {
					creative.Images = append(creative.Images, imageItem.URL)
				}
				creative.Type = reqRtbConfig.ImageStyleID
			case 20:
				if mhDataItem.Video == nil {
					continue
				}
				video := &fengfei.BidResponse_SeatBid_Bid_Creative_Video{
					VideoUrl:  mhDataItem.Video.VideoURL,
					VideoTime: strconv.Itoa(mhDataItem.Video.Duration / 1000),
					CoverUrl:  mhDataItem.Video.CoverURL,
				}
				creative.Video = video
				creative.Type = reqRtbConfig.VideoStyleID
			}
			bid.Creative = creative

			if len(mhDataItem.AppName) > 0 {
				app.Name = mhDataItem.AppName
			}
			if len(mhDataItem.DownloadURL) > 0 {
				app.Dlurl = mhDataItem.DownloadURL
			}
			if len(mhDataItem.PackageName) > 0 {
				app.Package = mhDataItem.PackageName
			}
			if len(mhDataItem.Publisher) > 0 {
				app.DeveloperName = mhDataItem.Publisher
			}
			if len(mhDataItem.AppVersion) > 0 {
				app.AppVersion = mhDataItem.AppVersion
			}
			if len(mhDataItem.PrivacyLink) > 0 {
				app.AppPrivacyPolicyUrl = mhDataItem.PrivacyLink
			}
			if len(mhDataItem.PermissionURL) > 0 {
				app.AppPermissionUrl = mhDataItem.PermissionURL
			}
			if len(mhDataItem.IconURL) > 0 {
				app.AppIconUrl = mhDataItem.IconURL
			}
			if len(mhDataItem.AppInfoURL) > 0 {
				app.AppInfoUrl = mhDataItem.AppInfoURL
			}
			if mhDataItem.PackageSize > 0 {
				app.AppApkSize = mhDataItem.PackageSize
			}
			creative.App = &app
		} else {
			continue
		}

		bids = append(bids, bid)
	}

	if len(bids) == 0 {
		return &fengfei.BidResponse{
			Id:    req.Id,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	seatbid.Bid = bids
	seatBids = append(seatBids, &seatbid)

	resp := &fengfei.BidResponse{
		Id:      req.Id,
		Bidid:   bigdataUID,
		Seatbid: seatBids,
	}

	return resp, http.StatusOK
}
