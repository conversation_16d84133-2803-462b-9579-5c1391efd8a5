package rtb_blued

type BluedRequestObject struct {
	RequestID string                   `json:"request_id"`
	Version   string                   `json:"version"`
	Imp       []*BluedRequestImpObject `json:"imp"`
	App       BluedRequestAppObject    `json:"app"`
	Device    BluedRequestDeviceObject `json:"device"`
}

type BluedRequestDeviceObject struct {
	Ua           string `json:"ua"`
	Os           string `json:"os"`
	Osv          string `json:"osv"`
	Uiv          string `json:"uiv"`
	Idfa         string `json:"idfa"`
	IdfaMd5      string `json:"idfa_md5"`
	Oaid         string `json:"oaid"`
	OaidMd5      string `json:"oaid_md5"`
	AndroidID    string `json:"android_id"`
	AndroidIdMd5 string `json:"android_id_md5"`
	Paid         string `json:"paid"`
	Aaid         string `json:"aaid"`
	IP           string `json:"ip"`
	ConnType     int    `json:"conn_type"`
	Carrier      int    `json:"carrier"`
	Make         string `json:"make"`
	Model        string `json:"model"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	Lang         string `json:"lang"`
	BootMark     string `json:"boot_mark"`
	UpdateMark   string `json:"update_mark"`
	Caid         string `json:"caid"`
	CaidMd5      string `json:"caid_md5"`
}

type BluedRequestAppObject struct {
	Bundle  string `json:"bundle"`
	Version string `json:"version"`
}

type BluedRequestImpObject struct {
	TagID         string                        `json:"tag_id"`
	ID            int                           `json:"id"`
	BidFloor      int                           `json:"bid_floor"`
	Secure        int                           `json:"secure"`
	Download      int                           `json:"download"`
	Deeplink      int                           `json:"deeplink"`
	UniversalLink int                           `json:"universal_link"`
	Native        []BluedRequestImpNativeObject `json:"native"`
}

type BluedRequestImpNativeObject struct {
	CreativeType int `json:"creative_type"`
}

type BluedResponseObject struct {
	ID      string                        `json:"id"`
	Bidid   string                        `json:"bidid"`
	Seatbid []*BluedResponseSeatbidObject `json:"seatbid,omitempty"`
}

type BluedResponseSeatbidObject struct {
	Bid []*BluedResponseBidObject `json:"bid,omitempty"`
}

type BluedResponseBidObject struct {
	ImpID         int                              `json:"imp_id,omitempty"`
	CreativeType  int                              `json:"creative_type,omitempty"`
	Price         int                              `json:"price,omitempty"`
	ID            string                           `json:"id,omitempty"`
	DealID        string                           `json:"deal_id,omitempty"`
	Nurl          string                           `json:"nurl,omitempty"`
	Title         string                           `json:"title,omitempty"`
	Description   string                           `json:"description,omitempty"`
	Target        string                           `json:"target,omitempty"`
	Deeplink      string                           `json:"deeplink,omitempty"`
	UniversalLink string                           `json:"universal_link,omitempty"`
	App           *BluedResponseBidAppObject       `json:"app,omitempty"`
	Image         []*BluedResponseBidImageObject   `json:"image,omitempty"`
	Video         *BluedResponseBidVideoObject     `json:"video,omitempty"`
	Icon          *BluedResponseBidIconObject      `json:"icon,omitempty"`
	Monitor       []*BluedResponseBidMonitorObject `json:"monitor,omitempty"`
}

type BluedResponseBidAppObject struct {
	Size          int    `json:"size"`
	Name          string `json:"name"`
	Bundle        string `json:"bundle"`
	Version       string `json:"version"`
	Icon          string `json:"icon"`
	URL           string `json:"url"`
	Developer     string `json:"developer"`
	PermissionURL string `json:"permission_url"`
	PrivacyURL    string `json:"privacy_url"`
	FunctionURL   string `json:"function_url"`
}

type BluedResponseBidImageObject struct {
	URL string `json:"url"`
	W   int    `json:"w"`
	H   int    `json:"h"`
}

type BluedResponseBidVideoObject struct {
	URL      string `json:"url"`
	W        int    `json:"w"`
	H        int    `json:"h"`
	Duration int    `json:"duration"`
}

type BluedResponseBidIconObject struct {
	URL string `json:"url"`
	W   int    `json:"w"`
	H   int    `json:"h"`
}

type BluedResponseBidMonitorObject struct {
	URL    string `json:"url"`
	Event  int    `json:"event"`
	Method int    `json:"method"`
}
