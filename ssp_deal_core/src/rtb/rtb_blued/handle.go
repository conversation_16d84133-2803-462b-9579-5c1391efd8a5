package rtb_blued

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"io"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleByBlued(c *gin.Context, channel string) (*BluedResponseObject, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()

	// 判断是否开启gzip
	acceptEncoding := c.GetHeader("Accept-Encoding")
	contentEncoding := c.GetHeader("Content-Encoding")

	if strings.Contains(acceptEncoding, "gzip") && strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	req := &BluedRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	deviceOs := strings.ToLower(req.Device.Os)

	var connectType int
	switch req.Device.ConnType {
	case 2:
		connectType = 1
	case 4:
		connectType = 2
	case 5:
		connectType = 3
	case 6:
		connectType = 4
	case 7:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.Device.Carrier {
	case 1:
		carrier = 1
	case 2:
		carrier = 2
	case 3:
		carrier = 3
	default:
		carrier = 0
	}

	adsCount := 1
	var resultfulImp []*BluedRequestImpObject
	var configList []models.RtbConfigByTagIDStu
	for _, imp := range req.Imp {
		var styleIds []string
		for _, nativeItem := range imp.Native {
			styleIds = append(styleIds, strconv.Itoa(nativeItem.CreativeType))
		}

		price := imp.BidFloor

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.TagID, deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.App.Bundle) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.App.Bundle {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Make
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.App.Bundle,
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           deviceOs,
			Manufacturer: manufacturer,
			Model:        req.Device.Model,
			IP:           req.Device.IP,
			Ua:           req.Device.Ua,
			OsVersion:    req.Device.Osv,
			Oaid:         req.Device.Oaid,
			OaidMd5:      req.Device.OaidMd5,
			Idfa:         req.Device.Idfa,
			IdfaMd5:      req.Device.IdfaMd5,
			AndroidID:    req.Device.AndroidID,
			AndroidIDMd5: req.Device.AndroidIdMd5,
			ScreenWidth:  req.Device.Width,
			ScreenHeight: req.Device.Height,
			Language:     req.Device.Lang,
			BootMark:     req.Device.BootMark,
			UpdateMark:   req.Device.UpdateMark,
			DeviceType:   1,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if len(req.Device.Caid) > 0 && len(req.Device.CaidMd5) > 0 {
		var caidMultiList []models.MHReqCAIDMulti
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = req.Device.Caid
		caidMulti.CAIDVersion = req.Device.CaidMd5
		caidMultiList = append(caidMultiList, caidMulti)
		reqStu.Device.CAIDMulti = caidMultiList
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var impItem *BluedRequestImpObject
	for _, imp := range resultfulImp {
		if imp.TagID == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var seatbidList []*BluedResponseSeatbidObject
	var bidList []*BluedResponseBidObject
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		bidItem := &BluedResponseBidObject{
			ImpID:       impItem.ID,
			Price:       ecpm,
			ID:          mhDataItem.AdID,
			Nurl:        winURL,
			Title:       mhDataItem.Title,
			Description: mhDataItem.Description,
			Target:      mhDataItem.LandpageURL,
		}

		if len(mhDataItem.IconURL) > 0 {
			bidItem.Icon = &BluedResponseBidIconObject{
				URL: mhDataItem.IconURL,
			}
		}

		var monitorArray []*BluedResponseBidMonitorObject
		if len(mhDataItem.ImpressionLink) > 0 {
			for _, impLink := range mhDataItem.ImpressionLink {
				impMonitor := &BluedResponseBidMonitorObject{
					URL:    impLink,
					Event:  1,
					Method: 1,
				}
				monitorArray = append(monitorArray, impMonitor)
			}
		}

		if len(mhDataItem.ClickLink) > 0 {
			for _, clkLink := range mhDataItem.ClickLink {
				tmlClkLink := strings.Replace(clkLink, "__DOWN_X__", "__CLICK_REL_X__", -1)
				tmlClkLink = strings.Replace(tmlClkLink, "__DOWN_Y__", "__CLICK_REL_Y__", -1)
				tmlClkLink = strings.Replace(tmlClkLink, "__UP_X__", "__CLICK_UP_REL_X__", -1)
				tmlClkLink = strings.Replace(tmlClkLink, "__UP_Y__", "__CLICK_UP_REL_Y__", -1)
				clkMonitor := &BluedResponseBidMonitorObject{
					URL:    tmlClkLink,
					Event:  2,
					Method: 1,
				}
				monitorArray = append(monitorArray, clkMonitor)
			}
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				bidItem.Deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					bidItem.Deeplink = mhDataItem.MarketURL
				}
			}
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				bidItem.Deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					bidItem.Deeplink = mhDataItem.DeepLink
				}
			}
			if deviceOs == "android" {
				if len(mhDataItem.PermissionURL) == 0 {
					mhDataItem.PermissionURL = "https://static.maplehaze.cn/static/permisson_ad.html"
				}
				if len(mhDataItem.AppInfoURL) == 0 {
					mhDataItem.AppInfoURL = "https://static.maplehaze.cn/static/appinfo.html"
				}
				app := &BluedResponseBidAppObject{
					Size:          int(mhDataItem.PackageSize),
					Name:          mhDataItem.AppName,
					Bundle:        mhDataItem.PackageName,
					Version:       mhDataItem.AppVersion,
					Icon:          mhDataItem.IconURL,
					URL:           mhDataItem.DownloadURL,
					Developer:     mhDataItem.Publisher,
					PermissionURL: mhDataItem.PermissionURL,
					PrivacyURL:    mhDataItem.PrivacyLink,
					FunctionURL:   mhDataItem.AppInfoURL,
				}
				bidItem.App = app
			}
		}

		if deviceOs == "ios" && len(bidItem.Deeplink) > 0 {
			bidItem.UniversalLink = bidItem.Deeplink
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image == nil || len(reqRtbConfig.ImageStyleID) == 0 {
				continue
			}
			var imgList []*BluedResponseBidImageObject
			for _, imageItem := range mhDataItem.Image {
				img := BluedResponseBidImageObject{
					URL: imageItem.URL,
					W:   imageItem.Width,
					H:   imageItem.Height,
				}
				imgList = append(imgList, &img)
			}
			bidItem.Image = imgList

			imageStyleID, _ := strconv.Atoi(reqRtbConfig.ImageStyleID)
			bidItem.CreativeType = imageStyleID
		case 20:
			if mhDataItem.Video == nil || len(reqRtbConfig.VideoStyleID) == 0 {
				continue
			}
			if len(mhDataItem.Video.CoverURL) > 0 {
				var imgList []*BluedResponseBidImageObject
				img := BluedResponseBidImageObject{
					URL: mhDataItem.Video.CoverURL,
					W:   mhDataItem.Video.Width,
					H:   mhDataItem.Video.Height,
				}
				imgList = append(imgList, &img)
				bidItem.Image = imgList
			}
			video := &BluedResponseBidVideoObject{
				URL:      mhDataItem.Video.VideoURL,
				W:        mhDataItem.Video.Width,
				H:        mhDataItem.Video.Height,
				Duration: mhDataItem.Video.Duration / 1000,
			}
			bidItem.Video = video

			if mhDataItem.Video.EventTracks != nil && len(mhDataItem.Video.EventTracks) > 0 {
				for _, eventTracks := range mhDataItem.Video.EventTracks {
					if eventTracks.EventType == 100 {
						for _, eventUrl := range eventTracks.EventURLS {
							videoStartTrack := &BluedResponseBidMonitorObject{
								URL:    eventUrl,
								Event:  4,
								Method: 1,
							}
							monitorArray = append(monitorArray, videoStartTrack)
						}
					}
					if eventTracks.EventType == 103 {
						for _, eventUrl := range eventTracks.EventURLS {
							videoEndTrack := &BluedResponseBidMonitorObject{
								URL:    eventUrl,
								Event:  5,
								Method: 1,
							}
							monitorArray = append(monitorArray, videoEndTrack)
						}
					}
				}
			}

			videoStyleID, _ := strconv.Atoi(reqRtbConfig.VideoStyleID)
			bidItem.CreativeType = videoStyleID
		}

		bidItem.Monitor = monitorArray

		bidList = append(bidList, bidItem)
	}

	if len(bidList) == 0 {
		return &BluedResponseObject{
			ID:    req.RequestID,
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	seatbid := &BluedResponseSeatbidObject{
		Bid: bidList,
	}
	seatbidList = append(seatbidList, seatbid)

	return &BluedResponseObject{
		ID:      req.RequestID,
		Bidid:   bigdataUID,
		Seatbid: seatbidList,
	}, http.StatusOK
}
