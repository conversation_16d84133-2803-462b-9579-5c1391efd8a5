package rtb_dianzhong

import (
	"encoding/json"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func <PERSON><PERSON><PERSON><PERSON><PERSON>(c *gin.Context, channel string) (*<PERSON>an<PERSON>hongResponseObject, int) {
	bodyContent, _ := c.GetRawData()

	req := &DianZhongRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &DianZhongResponseObject{
			Code: 20000,
			Msg:  "json格式错误",
		}, http.StatusNoContent
	}

	var deviceOs string
	switch strings.ToLower(req.Device.Os) {
	case "android":
		deviceOs = "android"
	case "ios":
		deviceOs = "ios"
	default:
		return &DianZhongResponseObject{
			Code: 20000,
			Msg:  "os错误",
		}, http.StatusNoContent
	}

	var connectType int
	switch req.Env.Network {
	case 1:
		connectType = 1
	case 10:
		connectType = 2
	case 11:
		connectType = 3
	case 12:
		connectType = 4
	case 13:
		connectType = 7
	default:
		connectType = 0
	}

	carrier := req.Env.Carrier

	adsCount := 1
	var keyword string
	var configList []*models.RtbConfigByTagIDStu

	tagId := req.Imp.Id
	price := req.Imp.BidFloor

	var adxInfo *[]models.RtbConfigByTagIDStu
	adxInfo = models.GetAdxInfoByRtbTagID(c, channel, tagId, deviceOs, "", "", price)
	if adxInfo == nil || len(*adxInfo) == 0 {
		return &DianZhongResponseObject{
			Code: 20000,
			Msg:  "imp错误",
		}, http.StatusNoContent
	}

	configData := (*adxInfo)[0]
	configList = append(configList, &configData)

	var caidMultiList []models.MHReqCAIDMulti
	if req.Device.Caids != nil {
		for _, caidItem := range req.Device.Caids {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.Caid
			caidMulti.CAIDVersion = caidItem.Version
			caidMultiList = append(caidMultiList, caidMulti)
		}
	}

	var manufacturer string

	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Brand
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.App.Pkg,
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
			Query:       keyword,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			Manufacturer:    manufacturer,
			Model:           req.Device.Model,
			IP:              req.Env.Ip,
			Ua:              req.Device.Ua,
			OsVersion:       req.Device.Osv,
			Mac:             req.Device.Mac,
			AndroidID:       req.Device.Androidid,
			AndroidIDMd5:    req.Device.AndroididMd5,
			Imei:            req.Device.Imei,
			ImeiMd5:         req.Device.ImeiMd5,
			Oaid:            req.Device.Oaid,
			OaidMd5:         req.Device.OaidMd5,
			Idfa:            req.Device.Idfa,
			IdfaMd5:         req.Device.IdfaMd5,
			ScreenWidth:     req.Device.W,
			ScreenHeight:    req.Device.H,
			Language:        req.Device.Lang,
			AppStoreVersion: req.Device.AppstoreVer,
			TimeZone:        req.Env.TimeZone,
			Country:         req.Env.Country,
			DeviceType:      1,
			CAIDMulti:       caidMultiList,
			AppList:         getDzAppList(req.Env.InstalledIds),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}
	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &DianZhongResponseObject{
			Code: 10000,
			Msg:  "",
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &DianZhongResponseObject{
			Code: 10000,
			Msg:  "",
		}, http.StatusNoContent
	}

	var bids []*DianZhongResponseBidObject

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		var trackingArray []*DianZhongResponseTrackerObject

		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=" + utils.ConvertIntToString(ecpm) + "&log=" + url.QueryEscape(mhDataItem.Log)

		var winTrack DianZhongResponseTrackerObject
		winTrack.Type = 1
		winTrack.Urls = []string{winURL}
		trackingArray = append(trackingArray, &winTrack)

		if len(mhDataItem.ImpressionLink) > 0 {
			var impTrack DianZhongResponseTrackerObject
			impTrack.Type = 2
			impTrack.Urls = mhDataItem.ImpressionLink
			trackingArray = append(trackingArray, &impTrack)
		}

		if len(mhDataItem.ClickLink) > 0 {
			var clkTrack DianZhongResponseTrackerObject
			clkTrack.Type = 3
			clkTrack.Urls = mhDataItem.ClickLink
			trackingArray = append(trackingArray, &clkTrack)
		}

		bid := &DianZhongResponseBidObject{
			Price:   ecpm,
			Id:      mhDataItem.AdID,
			Title:   mhDataItem.Title,
			Desc:    mhDataItem.Description,
			IconUrl: mhDataItem.IconURL,
			DlApp: &DianZhongResponseAppObject{
				Size:      int(mhDataItem.PackageSize),
				Name:      mhDataItem.AppName,
				Pkg:       mhDataItem.PackageName,
				Ver:       mhDataItem.AppVersion,
				PerUrl:    mhDataItem.PermissionURL,
				PriUrl:    mhDataItem.PrivacyLink,
				Publisher: mhDataItem.Publisher,
				IntroUrl:  mhDataItem.AppInfoURL,
			},
		}

		var dplurl string
		if mhDataItem.InteractType == 0 {
			bid.Adck = 1
			bid.FallbackType = 1
			bid.ActionUrl = mhDataItem.LandpageURL
			bid.FallbackUrl = mhDataItem.LandpageURL
			if len(mhDataItem.DeepLink) > 0 {
				dplurl = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					dplurl = mhDataItem.MarketURL
				}
			}
		} else {
			bid.Adck = 2
			bid.ActionUrl = mhDataItem.DownloadURL

			bid.FallbackType = 2
			bid.FallbackUrl = mhDataItem.DownloadURL
			if len(mhDataItem.LandpageURL) > 0 {
				bid.FallbackType = 1
				bid.FallbackUrl = mhDataItem.LandpageURL
			}
			if len(mhDataItem.MarketURL) > 0 {
				dplurl = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					dplurl = mhDataItem.DeepLink
				}
			}
		}

		if len(dplurl) > 0 {
			bid.Adck = 3
			bid.ActionUrl = dplurl
			var convUrls []string
			if len(mhDataItem.ConvTracks) > 0 {
				for _, convTracks := range mhDataItem.ConvTracks {
					if convTracks.ConvType == 10 {
						convUrls = convTracks.ConvURLS
					}
				}
				if len(convUrls) > 0 {
					var convTrack DianZhongResponseTrackerObject
					convTrack.Type = 11
					convTrack.Urls = convUrls
					trackingArray = append(trackingArray, &convTrack)
				}
			}
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image == nil {
				continue
			}
			var imgList []*DianZhongResponseImgObject
			for _, imageItem := range mhDataItem.Image {
				var img DianZhongResponseImgObject
				img.Url = imageItem.URL
				img.W = imageItem.Width
				img.H = imageItem.Height
				imgList = append(imgList, &img)
			}

			bid.CreativeType = 1
			bid.Images = imgList
		case 20:
			if mhDataItem.Video == nil {
				continue
			}
			video := &DianZhongResponseVideoObject{
				Duration: mhDataItem.Video.Duration / 1000,
				W:        mhDataItem.Video.Width,
				H:        mhDataItem.Video.Height,
				Url:      mhDataItem.Video.VideoURL,
			}
			bid.CreativeType = 2
			bid.Video = video

			if mhDataItem.Video.EventTracks != nil && len(mhDataItem.Video.EventTracks) > 0 {
				for _, eventTracks := range mhDataItem.Video.EventTracks {
					if eventTracks.EventType == 100 {
						var videoStartEventTrack DianZhongResponseTrackerObject
						videoStartEventTrack.Type = 8
						videoStartEventTrack.Urls = eventTracks.EventURLS
						trackingArray = append(trackingArray, &videoStartEventTrack)
					}
					if eventTracks.EventType == 103 {
						var videoEndEventTrack DianZhongResponseTrackerObject
						videoEndEventTrack.Type = 9
						videoEndEventTrack.Urls = eventTracks.EventURLS
						trackingArray = append(trackingArray, &videoEndEventTrack)
					}
				}
			}
		}

		bid.Trackers = trackingArray
		bids = append(bids, bid)
	}

	if len(bids) == 0 {
		return &DianZhongResponseObject{
			Code: 10000,
		}, http.StatusNoContent
	}

	milliseconds := time.Now().UnixNano() / 1e6
	seatbid := &DianZhongResponseSeatbidObject{
		Ts:  milliseconds,
		Rid: req.Rid,
		Bid: bids,
	}

	resp := &DianZhongResponseObject{
		Code: 0,
		Msg:  "成功",
		Data: seatbid,
	}

	return resp, http.StatusOK
}

func getDzAppList(appList []int) []int {
	if len(appList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appList {
		if v, ok := dianzhongAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var dianzhongAppListCodeMap = map[int]int{
	10000: 1001,
	10001: 1008,
	10002: 1019,
	10003: 1031,
	10004: 1032,
	10005: 1024,
	10006: 1033,
	10008: 1035,
	10009: 1036,
	10010: 1015,
	10011: 1037,
	10012: 1038,
	10013: 1039,
	10014: 1040,
	10015: 1041,
	10016: 1042,
	10017: 1005,
	10018: 1020,
	10019: 1014,
	10020: 1002,
	10021: 1017,
	10022: 1043,
	10023: 1006,
	10025: 1018,
	10026: 1044,
	10027: 1011,
	10028: 1045,
	10029: 1004,
	10030: 1009,
	10038: 1016,
	10039: 1012,
	10040: 1003,
	10041: 1010,
	10054: 1027,
	10067: 1026,
	10071: 1002,
	10072: 1001,
	10073: 1006,
	10074: 1004,
	10076: 1017,
	10078: 1046,
	10079: 1043,
	10081: 1035,
}
