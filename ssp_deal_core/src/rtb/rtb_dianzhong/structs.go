package rtb_dianzhong

// Dian<PERSON>hongRequestObject Objects
type DianZhongRequestObject struct {
	Rid    string                        `json:"rid"`
	Ver    string                        `json:"ver"`
	Device *DianZhongRequestDeviceObject `json:"device"`
	App    *DianZhongRequestAppObject    `json:"app"`
	Imp    *DianZhongRequestImpObject    `json:"imp"`
	Env    *DianZhongRequestEnvObject    `json:"env"`
}

type DianZhongRequestDeviceObject struct {
	W            int                                 `json:"w"`
	H            int                                 `json:"h"`
	Vender       string                              `json:"vender"`
	Model        string                              `json:"model"`
	Brand        string                              `json:"brand"`
	Os           string                              `json:"os"`
	Osv          string                              `json:"osv"`
	Lang         string                              `json:"lang"`
	Ua           string                              `json:"ua"`
	Mac          string                              `json:"mac"`
	Imei         string                              `json:"imei"`
	ImeiMd5      string                              `json:"imei_md5"`
	Androidid    string                              `json:"androidid"`
	AndroididMd5 string                              `json:"androidid_md5"`
	Oaid         string                              `json:"oaid"`
	OaidMd5      string                              `json:"oaid_md5"`
	Idfa         string                              `json:"idfa"`
	IdfaMd5      string                              `json:"idfa_md5"`
	AppstoreVer  string                              `json:"appstore_ver"`
	Caids        []*DianZhongRequestDeviceCaidObject `json:"caids"`
}

type DianZhongRequestEnvObject struct {
	Carrier       int                                      `json:"carrier"`
	Network       int                                      `json:"network"`
	InstalledIds  []int                                    `json:"installed_ids"`
	Ip            string                                   `json:"ip"`
	TimeZone      string                                   `json:"time_zone"`
	Country       string                                   `json:"country"`
	Province      string                                   `json:"province"`
	City          string                                   `json:"city"`
	InstalledPkgs []DianZhongRequestEnvInstalledPkgsObject `json:"installed_pkgs"`
}

type DianZhongRequestEnvInstalledPkgsObject struct {
	Pkg string `json:"pkg"`
	Ver string `json:"ver"`
}

type DianZhongRequestAppObject struct {
	Key string `json:"key"`
	Pkg string `json:"pkg"`
	Ver string `json:"ver"`
}

type DianZhongRequestImpObject struct {
	Id       string `json:"id"`
	Type     int    `json:"type"`
	BidFloor int    `json:"bid_floor"`
	Count    int    `json:"count"`
	W        int    `json:"w"`
	H        int    `json:"h"`
}

type DianZhongRequestDeviceCaidObject struct {
	Caid    string `json:"caid"`
	Version string `json:"version"`
}

// DianZhongResponseObject Objects
type DianZhongResponseObject struct {
	Code int                             `json:"code"`
	Msg  string                          `json:"msg"`
	Data *DianZhongResponseSeatbidObject `json:"data,omitempty"`
}

type DianZhongResponseSeatbidObject struct {
	Ts  int64                         `json:"ts,omitempty"`
	Rid string                        `json:"rid,omitempty"`
	Bid []*DianZhongResponseBidObject `json:"bid,omitempty"`
}

type DianZhongResponseBidObject struct {
	Price        int                               `json:"price,omitempty"`
	Adck         int                               `json:"adck,omitempty"`
	CreativeType int                               `json:"creative_type,omitempty"`
	FallbackType int                               `json:"fallback_type,omitempty"`
	Id           string                            `json:"id,omitempty"`
	Title        string                            `json:"title,omitempty"`
	Desc         string                            `json:"desc,omitempty"`
	IconUrl      string                            `json:"icon_url,omitempty"`
	ActionUrl    string                            `json:"action_url,omitempty"`
	FallbackUrl  string                            `json:"fallback_url,omitempty"`
	Video        *DianZhongResponseVideoObject     `json:"video,omitempty"`
	DlApp        *DianZhongResponseAppObject       `json:"dl_app,omitempty"`
	Images       []*DianZhongResponseImgObject     `json:"images,omitempty"`
	Trackers     []*DianZhongResponseTrackerObject `json:"trackers,omitempty"`
}

type DianZhongResponseImgObject struct {
	W   int    `json:"w,omitempty"`
	H   int    `json:"h,omitempty"`
	Url string `json:"url,omitempty"`
}

type DianZhongResponseAppObject struct {
	Size      int    `json:"size,omitempty"`
	Name      string `json:"name,omitempty"`
	Pkg       string `json:"pkg,omitempty"`
	Ver       string `json:"ver,omitempty"`
	PerUrl    string `json:"per_url,omitempty"`
	PriUrl    string `json:"pri_url,omitempty"`
	Publisher string `json:"publisher,omitempty"`
	IntroUrl  string `json:"intro_url,omitempty"`
}
type DianZhongResponseVideoObject struct {
	Duration int    `json:"duration,omitempty"`
	W        int    `json:"w,omitempty"`
	H        int    `json:"h,omitempty"`
	Url      string `json:"url,omitempty"`
}

type DianZhongResponseTrackerObject struct {
	Type int      `json:"type,omitempty"`
	Urls []string `json:"urls,omitempty"`
}
