package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/youdao"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByYoudao ...
func HandleByYoudao(c *gin.Context, channel string) *youdao.BidResponse {
	// fmt.Println("youdao rtb")

	bodyContent, err := c.GetRawData()
	youdaoReq := &youdao.BidRequest{}
	err = proto.Unmarshal(bodyContent, youdaoReq)
	if err != nil {
		fmt.Println(err)
		return NoYoudaoBidReturn(youdaoReq.GetId(), "parser error")
	}

	// youdaoReqByte, _ := json.Marshal(youdaoReq)
	// youdaoReqString := string(youdaoReqByte)
	// fmt.Println("youdao req: ", youdaoReqString)

	// fmt.Println("youdao req: ", youdaoReq)
	// fmt.Println("youdao os: ", youdaoReq.GetDevice().GetOs())
	reqImei := proto.GetExtension(youdaoReq.Device, youdao.E_Did).(string)
	reqAndroidID := proto.GetExtension(youdaoReq.Device, youdao.E_Dpid).(string)
	reqOaid := proto.GetExtension(youdaoReq.Device, youdao.E_Oaid).(string)
	reqLocalTzName := proto.GetExtension(youdaoReq.Device, youdao.E_LocalTzName).(string)
	reqDeviceStartUpTime := proto.GetExtension(youdaoReq.Device, youdao.E_DeviceStartUpTime).(float64)
	reqDeviceUpdateTime := proto.GetExtension(youdaoReq.Device, youdao.E_DeviceUpdateTime).(float64)
	reqCpuNumber := proto.GetExtension(youdaoReq.Device, youdao.E_CpuNumber).(int32)
	reqDisktotal := proto.GetExtension(youdaoReq.Device, youdao.E_Disktotal).(int64)
	reqMemTotal := proto.GetExtension(youdaoReq.Device, youdao.E_MemTotal).(int64)
	reqBootMark := proto.GetExtension(youdaoReq.Device, youdao.E_BootMark).(string)
	reqUpdateMark := proto.GetExtension(youdaoReq.Device, youdao.E_UpdateMark).(string)
	reqIdfaMd5 := proto.GetExtension(youdaoReq.Device, youdao.E_IdfaMd5).(string)
	reqCountryCode := proto.GetExtension(youdaoReq.Device, youdao.E_CountryCode).(string)
	reqLanguage := proto.GetExtension(youdaoReq.Device, youdao.E_Language).(string)

	reqCaids := proto.GetExtension(youdaoReq.Device, youdao.E_Caids)
	caidList := reqCaids.([]*youdao.Caid)

	var caidMultiList []models.MHReqCAIDMulti
	for _, caidItem := range caidList {
		var tmpCaid models.MHReqCAIDMulti
		tmpCaid.CAID = caidItem.GetCaid()
		tmpCaid.CAIDVersion = caidItem.GetVersion()
		caidMultiList = append(caidMultiList, tmpCaid)
	}

	//reqCaid := reqCaids.GetCaid()
	//reqCaidVersion := reqCaids.GetVersion()
	//if strings.ToLower(youdaoReq.GetDevice().GetOs()) == "android" {
	//	fmt.Println("kbg_debug_youdao imei: ", reqImei)
	//	fmt.Println("kbg_debug_youdao imei_md5: ", youdaoReq.GetDevice().GetDidmd5())
	//	fmt.Println("kbg_debug_youdao android_id: ", reqAndroidID)
	//	fmt.Println("kbg_debug_youdao android_id_md5: ", youdaoReq.GetDevice().GetDpidmd5())
	//	fmt.Println("kbg_debug_youdao oaid: ", reqOaid)
	//}

	// fmt.Println("youdao imei: ", reqImei)
	// fmt.Println("youdao imei_md5: ", youdaoReq.GetDevice().GetDidmd5())
	// fmt.Println("youdao android_id: ", reqAndroidID)
	// fmt.Println("youdao android_id_md5: ", youdaoReq.GetDevice().GetDpidmd5())
	// fmt.Println("youdao oaid: ", reqOaid)
	// fmt.Println("youdao idfa: ", youdaoReq.GetDevice().GetIfa())
	// fmt.Println("youdao ua: ", youdaoReq.GetDevice().GetUa())

	reqOs := ""
	if strings.ToLower(youdaoReq.GetDevice().GetOs()) == "android" {
		reqOs = "android"
	} else if strings.ToLower(youdaoReq.GetDevice().GetOs()) == "ios" {
		reqOs = "ios"
	} else {
		return NoYoudaoBidReturn(youdaoReq.GetId(), "wrong os")
	}

	reqDeivceMake := youdaoReq.GetDevice().GetMake()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqConnectType := 1
	if youdaoReq.GetDevice().GetConnectiontype() == youdao.BidRequest_Device_CELL_UNKNOWN {
		reqConnectType = 0
	} else if youdaoReq.GetDevice().GetConnectiontype() == youdao.BidRequest_Device_WIFI {
		reqConnectType = 1
	} else if youdaoReq.GetDevice().GetConnectiontype() == youdao.BidRequest_Device_CELL_2G {
		reqConnectType = 2
	} else if youdaoReq.GetDevice().GetConnectiontype() == youdao.BidRequest_Device_CELL_3G {
		reqConnectType = 3
	} else if youdaoReq.GetDevice().GetConnectiontype() == youdao.BidRequest_Device_CELL_4G {
		reqConnectType = 4
	}

	reqCarrier := 0
	if youdaoReq.GetDevice().GetCarrier() == "中国移动" {
		reqCarrier = 1
	} else if youdaoReq.GetDevice().GetCarrier() == "中国联通" {
		reqCarrier = 2
	} else if youdaoReq.GetDevice().GetCarrier() == "中国电信" {
		reqCarrier = 3
	}

	reqImps := youdaoReq.GetImp()
	// fmt.Println(len(reqSlots))
	if len(reqImps) == 0 {
		return NoYoudaoBidReturn(youdaoReq.GetId(), "wrong slots")
	}

	var reqOKImps []*youdao.BidRequest_Imp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqImps {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.GetTagid()
		reqPrice := item.GetBidfloor()
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// fmt.Println(reqMaterialType)
		// if len(item.Pmp.Deals) > 0 {
		// 	reqPrice = item.Pmp.Deals[0].GetBidfloor()
		// }

		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return NoYoudaoBidReturn(youdaoReq.GetId(), "not active")
		// }
		// fmt.Println("asset req 0")
		// fmt.Println(item.GetNative())
		// fmt.Println("asset req 1")
		// fmt.Println(item.GetNative().GetRequestNative().GetAssets())
		// fmt.Println("asset req 2")

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return NoYoudaoBidReturn(youdaoReq.GetId(), "not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}
	// fmt.Println("aaaaaa")
	// fmt.Println(reqRtbConfig)
	// fmt.Println("bbbbbb")

	if len(reqOKImps) == 0 {
		return NoYoudaoBidReturn(youdaoReq.GetId(), "wrong imps")
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: youdaoReq.GetApp().GetBundle(),
			AppName:     youdaoReq.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1, //len(reqOKImps),
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    youdaoReq.GetDevice().GetOsv(),
			Model:        youdaoReq.GetDevice().GetModel(),
			Manufacturer: reqDeivceMake,
			Imei:         reqImei,
			ImeiMd5:      strings.ToLower(youdaoReq.GetDevice().GetDidmd5()),
			AndroidID:    reqAndroidID,
			AndroidIDMd5: strings.ToLower(youdaoReq.GetDevice().GetDpidmd5()),
			Oaid:         reqOaid,
			// MacMd5:       youdaoReq.GetDevice().GetMacmd5(),
			Idfa:               youdaoReq.GetDevice().GetIfa(),
			IdfaMd5:            reqIdfaMd5,
			Ua:                 youdaoReq.GetDevice().GetUa(),
			ScreenWidth:        int(youdaoReq.GetDevice().GetW()),
			ScreenHeight:       int(youdaoReq.GetDevice().GetH()),
			DeviceType:         1,
			IP:                 youdaoReq.GetDevice().GetIp(),
			CAIDMulti:          caidMultiList,
			TimeZone:           reqLocalTzName,
			DeviceStartSec:     strconv.FormatFloat(reqDeviceStartUpTime, 'f', 6, 64),
			SystemUpdateSec:    strconv.FormatFloat(reqDeviceUpdateTime, 'f', 6, 64),
			CPUNum:             utils.ConvertIntToString(int(reqCpuNumber)),
			HarddiskSizeByte:   strconv.FormatInt(reqDisktotal, 10),
			PhysicalMemoryByte: strconv.FormatInt(reqMemTotal, 10),
			BootMark:           reqBootMark,
			UpdateMark:         reqUpdateMark,
			Country:            reqCountryCode,
			Language:           reqLanguage,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// fmt.Println("cccccc")
	// fmt.Println(reqStu)
	// fmt.Println("dddddd")

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	// fmt.Println(mhResp.Ret)

	// fmt.Println(mhResp.Data)

	// youdao no fill
	// return NoYoudaoBidReturn(youdaoReq.GetId(), "no fill")

	if mhResp.Ret != 0 {
		return NoYoudaoBidReturn(youdaoReq.GetId(), "no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return NoYoudaoBidReturn(youdaoReq.GetId(), "no fill")
	}

	// youdao 不填充
	// return NoYoudaoBidReturn(youdaoReq.GetId(), "no fill")

	//////////////////////////////////////////////////////////////////////////////////////////////
	youdaoResp := &youdao.BidResponse{
		Id:    proto.String(youdaoReq.GetId()),
		Bidid: proto.String("maplehaze_id"),
	}

	// youdaoResp.Seatbid =
	for i := 0; i < 1; i++ {
		if i > mhRespCount-1 {
			continue
		}

		// youdaoReqImpItem := reqOKImps[i]
		var youdaoReqImpItem *youdao.BidRequest_Imp
		for _, reqImpItem := range reqOKImps {
			isAssetsOK := true
			for _, reqAssetsItem := range reqImpItem.GetNative().GetRequestNative().GetAssets() {
				if reqAssetsItem.GetVideo().GetW() > 0 {
					isAssetsOK = false
				}
			}
			if isAssetsOK {
				youdaoReqImpItem = reqImpItem
				break
			}
		}

		if youdaoReqImpItem == nil {
			return NoYoudaoBidReturn(youdaoReq.GetId(), "no imp item")
		}

		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]

		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// seatbid
		youdaoRespSeatbid := youdao.BidResponse_SeatBid{
			Seat: proto.String("maplehaze"),
		}

		// winurl
		winUrl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// float 64 price
		floatPrice, _ := strconv.ParseFloat(utils.ConvertIntToString(ecpm), 64)

		// seatbid -> bid
		youdaoRespSeatbidBid := youdao.BidResponse_SeatBid_Bid{
			Id:    proto.String(mhDataItem.AdID),
			Impid: youdaoReqImpItem.Id,
			Price: proto.Float64(floatPrice),
			Nurl:  proto.String(winUrl),
		}
		if len(mhDataItem.PackageName) > 0 {
			youdaoRespSeatbidBid.Bundle = proto.String(mhDataItem.PackageName)
		}
		youdaoRespSeatbidBid.Cid = proto.String(mhDataItem.Crid)
		youdaoRespSeatbidBid.Crid = proto.String(mhDataItem.Crid)
		// youdaoRespSeatbidBid.Dealid = youdaoReqImpItem.Pmp.Deals[0].Id

		// adm
		// youdaoRespSeatbidBidAdm := youdao.BidResponse_SeatBid_Bid_Adm{}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(int(youdaoReq.GetDevice().GetW())), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(int(youdaoReq.GetDevice().GetH())), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}

		var deepLinkTrackOKArray []string
		var deepLinkTrackFailedArray []string
		for _, trackItem := range mhDataItem.ConvTracks {
			if trackItem.ConvType == 10 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
				}
			} else if trackItem.ConvType == 11 {
				for _, trackEventItem := range trackItem.ConvURLS {
					deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
				}
			}
		}

		// native response
		youdaoRespSeatbidBidAdmNativeResponseLink := youdao.NativeResponse_Link{
			// Url:           proto.String(mhDataItem.AdURL),
			Clicktrackers: clkTrackArray,
		}
		if len(deepLinkTrackOKArray) > 0 {
			proto.SetExtension(&youdaoRespSeatbidBidAdmNativeResponseLink, youdao.E_DpTrackers, deepLinkTrackOKArray)
		}
		if len(deepLinkTrackFailedArray) > 0 {
			proto.SetExtension(&youdaoRespSeatbidBidAdmNativeResponseLink, youdao.E_DpFailedTrackers, deepLinkTrackFailedArray)
		}

		if mhDataItem.InteractType == 0 {
			var attriArray []int32
			attriArray = append(attriArray, 101)

			proto.SetExtension(&youdaoRespSeatbidBid, youdao.E_Attri, attriArray)
			//proto.SetExtension(&youdaoRespSeatbidBid, youdao.E_UrlType, youdao.UrlType_LANDINGPAGE)
		} else {
			var attriArray []int32
			attriArray = append(attriArray, 100)
			proto.SetExtension(&youdaoRespSeatbidBid, youdao.E_Attri, attriArray)

			//if len(mhDataItem.DeepLink) > 0 {
			//	proto.SetExtension(&youdaoRespSeatbidBid, youdao.E_UrlType, youdao.UrlType_DEEPLINK)
			//	if strings.ToLower(youdaoReq.GetDevice().GetOs()) == "ios" {
			//		proto.SetExtension(&youdaoRespSeatbidBid, youdao.E_UrlType, youdao.UrlType_ULINK)
			//	}
			//}
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			if mhDataItem.InteractType == 0 {
				if len(mhDataItem.DeepLink) > 0 {
					youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.DeepLink)
				} else {
					if len(mhDataItem.MarketURL) > 0 {
						youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.MarketURL)
					}
				}
				youdaoRespSeatbidBidAdmNativeResponseLink.Fallback = proto.String(mhDataItem.LandpageURL)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.MarketURL)
				} else {
					if len(mhDataItem.DeepLink) > 0 {
						youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.DeepLink)
					}
				}
				youdaoRespSeatbidBidAdmNativeResponseLink.Fallback = proto.String(mhDataItem.DownloadURL)
			}

		} else {
			if mhDataItem.InteractType == 0 {
				youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.LandpageURL)
			} else {
				youdaoRespSeatbidBidAdmNativeResponseLink.Url = proto.String(mhDataItem.DownloadURL)
			}
		}

		// assets
		var youdaoRespSeatbidBidAdmNativeResponseAssetArray []*youdao.NativeResponse_Asset
		fmt.Println("asset resp begin")
		fmt.Println(youdaoReqImpItem.GetNative())
		fmt.Println("asset resp 0")
		fmt.Println(youdaoReqImpItem.GetNative().GetRequestNative().GetAssets())
		fmt.Println("asset resp 1")

		for _, assetsItem := range youdaoReqImpItem.GetNative().GetRequestNative().GetAssets() {
			// fmt.Println(assetsItem.ID)
			// fmt.Println(assetsItem.Title.Len)
			// fmt.Println(assetsItem.Img.W)
			// fmt.Println(assetsItem.Img.H)
			// fmt.Println(assetsItem.Data.Len)
			if assetsItem.GetTitle().GetLen() > 0 {
				youdaoRespSeatbidBidAdmNativeResponseAssetTitle0 := youdao.NativeResponse_Asset_Title{
					Text: proto.String(mhDataItem.Title),
				}
				youdaoRespSeatbidBidAdmNativeResponseAssetTitle := youdao.NativeResponse_Asset_Title_{
					Title: &youdaoRespSeatbidBidAdmNativeResponseAssetTitle0,
				}
				youdaoRespSeatbidBidAdmNativeResponseAsset := youdao.NativeResponse_Asset{
					Id:         proto.Int32(assetsItem.GetId()),
					AssetOneof: &youdaoRespSeatbidBidAdmNativeResponseAssetTitle,
				}

				youdaoRespSeatbidBidAdmNativeResponseAssetArray = append(youdaoRespSeatbidBidAdmNativeResponseAssetArray, &youdaoRespSeatbidBidAdmNativeResponseAsset)
			} else if assetsItem.GetImg().GetW() > 0 {
				if isVideoType == 0 {
					youdaoRespSeatbidBidAdmNativeResponseAssetImg0 := youdao.NativeResponse_Asset_Image{
						Url: proto.String(mhDataItem.Image[0].URL),
						W:   proto.Int32(int32(mhDataItem.Image[0].Width)),
						H:   proto.Int32(int32(mhDataItem.Image[0].Height)),
					}
					youdaoRespSeatbidBidAdmNativeResponseAssetImg := youdao.NativeResponse_Asset_Img{
						Img: &youdaoRespSeatbidBidAdmNativeResponseAssetImg0,
					}
					youdaoRespSeatbidBidAdmNativeResponseAsset := youdao.NativeResponse_Asset{
						Id:         proto.Int32(assetsItem.GetId()),
						AssetOneof: &youdaoRespSeatbidBidAdmNativeResponseAssetImg,
					}

					youdaoRespSeatbidBidAdmNativeResponseAssetArray = append(youdaoRespSeatbidBidAdmNativeResponseAssetArray, &youdaoRespSeatbidBidAdmNativeResponseAsset)
				} else if isVideoType == 1 {
					//ydVideo := youdao.YdVideo{
					//	Url:      proto.String(mhDataItem.Video.VideoURL),
					//	Duration: proto.Int32(int32(mhDataItem.Video.Duration / 1000)),
					//	Width:    proto.Int32(int32(mhDataItem.Video.Width)),
					//	Height:   proto.Int32(int32(mhDataItem.Video.Height)),
					//}
					youdaoRespSeatbidBidAdmNativeResponseAssetImg0 := youdao.NativeResponse_Asset_Image{
						Url: proto.String(mhDataItem.Video.CoverURL),
					}
					youdaoRespSeatbidBidAdmNativeResponseAssetImg := youdao.NativeResponse_Asset_Img{
						Img: &youdaoRespSeatbidBidAdmNativeResponseAssetImg0,
					}
					youdaoRespSeatbidBidAdmNativeResponseAsset := youdao.NativeResponse_Asset{
						Id:         proto.Int32(assetsItem.GetId()),
						AssetOneof: &youdaoRespSeatbidBidAdmNativeResponseAssetImg,
					}
					youdaoRespSeatbidBidAdmNativeResponseAssetArray = append(youdaoRespSeatbidBidAdmNativeResponseAssetArray, &youdaoRespSeatbidBidAdmNativeResponseAsset)
					//proto.SetExtension(&youdaoRespSeatbidBidAdmNativeResponseAsset, youdao.E_YdVideo, ydVideo)

				}

			} else if assetsItem.GetData().GetLen() > 0 {
				youdaoRespSeatbidBidAdmNativeResponseAssetData0 := youdao.NativeResponse_Asset_Data{
					Value: proto.String(mhDataItem.Description),
				}
				youdaoRespSeatbidBidAdmNativeResponseAssetData := youdao.NativeResponse_Asset_Data_{
					Data: &youdaoRespSeatbidBidAdmNativeResponseAssetData0,
				}
				youdaoRespSeatbidBidAdmNativeResponseAsset := youdao.NativeResponse_Asset{
					Id:         proto.Int32(assetsItem.GetId()),
					AssetOneof: &youdaoRespSeatbidBidAdmNativeResponseAssetData,
				}

				youdaoRespSeatbidBidAdmNativeResponseAssetArray = append(youdaoRespSeatbidBidAdmNativeResponseAssetArray, &youdaoRespSeatbidBidAdmNativeResponseAsset)
			}
		}

		fmt.Println("asset resp end")

		//downloadAppInfo := youdao.DownloadAppInfo{
		//	AppIconImage:       proto.String(mhDataItem.IconURL),
		//	AppPermissionArray: nil,
		//	PrivacyPolicy:      proto.String(mhDataItem.PrivacyLink),
		//	DeveloperName:      proto.String(mhDataItem.Publisher),
		//	AppTitle:           proto.String(mhDataItem.AppName),
		//	AppVersion:         proto.String(mhDataItem.AppVersion),
		//	AppPermission:      proto.String(mhDataItem.Permission),
		//}

		youdaoRespSeatbidBidAdmNativeResponse := youdao.NativeResponse{
			Assets:      youdaoRespSeatbidBidAdmNativeResponseAssetArray,
			Link:        &youdaoRespSeatbidBidAdmNativeResponseLink,
			Imptrackers: impTrackArray,
		}

		//proto.SetExtension(&youdaoRespSeatbidBidAdmNativeResponse, youdao.E_DownloadAppInfo, downloadAppInfo)

		youdaoRespSeatbidBidAdmNative := youdao.BidResponse_SeatBid_Bid_AdmNative{
			AdmNative: &youdaoRespSeatbidBidAdmNativeResponse,
		}

		youdaoRespSeatbidBid.AdmOneof = &youdaoRespSeatbidBidAdmNative

		youdaoRespSeatbid.Bid = append(youdaoRespSeatbid.Bid, &youdaoRespSeatbidBid)

		youdaoResp.Seatbid = append(youdaoResp.Seatbid, &youdaoRespSeatbid)

	}

	youdaoRespByte, _ := json.Marshal(youdaoResp)
	youdaoRespString := string(youdaoRespByte)
	fmt.Println("youdao resp: ", youdaoRespString)

	return youdaoResp
}

// NoYoudaoBidReturn ...
func NoYoudaoBidReturn(reqID string, reason string) *youdao.BidResponse {
	// fmt.Println("youdao no bid reason: " + reason)

	youdaoNoResp := &youdao.BidResponse{
		Id:  proto.String(reqID),
		Nbr: youdao.BidResponse_UNMATCHED_USER.Enum(),
	}
	return youdaoNoResp
}
