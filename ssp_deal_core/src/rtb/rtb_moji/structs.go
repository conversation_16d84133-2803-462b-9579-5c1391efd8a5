package rtb_moji

type MojiResponseObject struct {
	Code int                     `json:"code"`
	Data *MojiResponseDataObject `json:"data"`
}

type MojiResponseDataObject struct {
	Type               int     `json:"type,omitempty"`
	Adwidth            int     `json:"adwidth,omitempty"`
	Adheight           int     `json:"adheight,omitempty"`
	Price              int     `json:"price,omitempty"`
	Adstyle            int     `json:"adstyle"`
	Version            int     `json:"version,omitempty"`
	Chargingtype       int     `json:"chargingtype,omitempty"`
	InteractionType    int     `json:"interaction_type,omitempty"`
	Duration           int     `json:"duration,omitempty"`
	ShortVideoType     int     `json:"shortVideoType,omitempty"`
	Splashtype         int     `json:"splashtype,omitempty"`
	Adtype             int     `json:"adtype,omitempty"`
	Substyle           int     `json:"substyle,omitempty"`
	FeedStyle          int     `json:"feed_style,omitempty"`
	AdstyleControl     int     `json:"adstyle_control,omitempty"`
	FileSize           float64 `json:"fileSize,omitempty"`
	Adid               string  `json:"adid,omitempty"`
	Sessionid          string  `json:"sessionid,omitempty"`
	Imgurl             string  `json:"imgurl,omitempty"`
	Iconurl            string  `json:"iconurl,omitempty"`
	Clickurl           string  `json:"clickurl,omitempty"`
	Clktrack           string  `json:"clktrack,omitempty"`
	Imptrack           string  `json:"imptrack,omitempty"`
	Winnoticeurl       string  `json:"winnoticeurl,omitempty"`
	Adtitle            string  `json:"adtitle,omitempty"`
	Adtext             string  `json:"adtext,omitempty"`
	Deeplinkurl        string  `json:"deeplinkurl,omitempty"`
	DeeplinkClktrack   string  `json:"deeplinkClktrack,omitempty"`
	AppName            string  `json:"app_name,omitempty"`
	Developer          string  `json:"developer,omitempty"`
	AppVersion         string  `json:"app_version,omitempty"`
	PermissionDescUrl  string  `json:"permission_desc_url,omitempty"`
	PrivacyPolicyUrl   string  `json:"privacy_policy_url,omitempty"`
	ProductDescription string  `json:"product_description,omitempty"`
	VideoUrl           string  `json:"videoUrl,omitempty"`
	VideoImageUrl      string  `json:"videoImageUrl,omitempty"`
	LastFrameText      string  `json:"lastFrameText,omitempty"`
	VideoLogo          string  `json:"videoLogo,omitempty"`
	LastFrameIcon      string  `json:"lastFrameIcon,omitempty"`
	IconDesc           string  `json:"iconDesc,omitempty"`
}
