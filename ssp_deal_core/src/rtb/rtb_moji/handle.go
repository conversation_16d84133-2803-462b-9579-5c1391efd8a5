package rtb_moji

import (
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleByMoji(c *gin.Context, channel string) (*MojiResponseObject, int) {
	adid := c.Query("adid")
	sessionid := c.Query("sessionid")
	adtype := c.Query("adtype")
	adstyle := c.Query("adstyle")
	pkgname := c.Query("pkgname")
	appname := c.Query("appname")
	net := c.Query("net")
	reqCarrier := c.Query("carrier")
	os := c.Query("os")
	osv := c.Query("osv")
	basicPrice := c.Query("basic_price")
	device := c.Query("device")
	ua := c.Query("ua")
	ip := c.Query("ip")
	imei := c.Query("imei")
	andid := c.Query("andid")
	idfa := c.Query("idfa")
	oaid := c.Query("oaid")
	width := c.Query("scrwidth")
	height := c.Query("scrheight")
	bootMark := c.Query("boot_mark")
	updateMark := c.Query("update_mark")
	brand := c.Query("brand")
	updateTime := c.Query("update_time")
	birthTime := c.Query("birth_time")

	var deviceOs string
	switch os {
	case "1":
		deviceOs = "ios"
	case "0":
		deviceOs = "android"
	default:
		return &MojiResponseObject{
			Code: 402,
		}, http.StatusOK
	}
	deviceMake := device
	if deviceOs == "ios" {
		deviceMake = "Apple"
	}

	var connectType int
	switch net {
	case "1":
		connectType = 1
	case "2":
		connectType = 2
	case "3":
		connectType = 3
	case "4":
		connectType = 4
	default:
		connectType = 0
	}

	var carrier int
	switch reqCarrier {
	case "1":
		carrier = 1
	case "2":
		carrier = 3
	case "3":
		carrier = 2
	default:
		carrier = 0
	}

	if len(adid) == 0 {
		return &MojiResponseObject{
			Code: 402,
		}, http.StatusOK
	}

	var reqRtbConfig models.RtbConfigByTagIDStu
	price, _ := strconv.Atoi(basicPrice)

	var styleIds []string
	styleIds = append(styleIds, adstyle)
	rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, adid, deviceOs, styleIds, "", price)

	if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
		return &MojiResponseObject{
			Code: 401,
		}, http.StatusOK
	}

	reqRtbConfig = (*rtbConfigArrayByTagID)[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: pkgname,
			AppName:     appname,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			OsVersion:       osv,
			Model:           deviceMake,
			Manufacturer:    brand,
			Imei:            imei,
			AndroidID:       andid,
			Oaid:            oaid,
			Idfa:            idfa,
			Ua:              ua,
			ScreenWidth:     utils.ConvertStringToInt(width),
			ScreenHeight:    utils.ConvertStringToInt(height),
			DeviceType:      1,
			IP:              ip,
			DeviceBirthSec:  birthTime,
			SystemUpdateSec: updateTime,
			BootMark:        bootMark,
			UpdateMark:      updateMark,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}
	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &MojiResponseObject{
			Code: 400,
		}, http.StatusOK
	}

	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &MojiResponseObject{
			Code: 400,
		}, http.StatusOK
	}

	mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[0]
	// 统计
	models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

	// 判定上游返回ecpm是否大于底价
	if mhDataItem.Ecpm < reqRtbConfig.Price {
		return &MojiResponseObject{
			Code: 400,
		}, http.StatusOK
	}
	ecpm := mhDataItem.Ecpm

	winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${winprice}" + "&log=" + url.QueryEscape(mhDataItem.Log)

	var clickLinkArray []string
	for _, clickLink := range mhDataItem.ClickLink {
		clickLink = strings.Replace(clickLink, "__SLD__", "__MOJI_SLD__", -1)
		clickLinkArray = append(clickLinkArray, clickLink)
	}

	resp := &MojiResponseDataObject{
		Price:        ecpm,
		Adstyle:      utils.ConvertStringToInt(adstyle),
		Version:      273,
		Chargingtype: 1,
		Adid:         adid,
		Sessionid:    sessionid,
		Iconurl:      mhDataItem.IconURL,
		Clktrack:     strings.Join(clickLinkArray, ";"),
		Imptrack:     strings.Join(mhDataItem.ImpressionLink, ";"),
		Winnoticeurl: winURL,
		Adtitle:      mhDataItem.Title,
		Adtext:       mhDataItem.Description,
		Adtype:       utils.ConvertStringToInt(adtype),
	}

	if mhDataItem.InteractType == 0 {
		// deep link
		if len(mhDataItem.DeepLink) > 0 {
			resp.Deeplinkurl = mhDataItem.DeepLink
		} else {
			if len(mhDataItem.MarketURL) > 0 {
				resp.Deeplinkurl = mhDataItem.MarketURL
			}
		}
		resp.InteractionType = 1
		resp.Clickurl = mhDataItem.LandpageURL
	} else if mhDataItem.InteractType == 1 {
		if len(mhDataItem.MarketURL) > 0 {
			resp.Deeplinkurl = mhDataItem.MarketURL
		} else {
			if len(mhDataItem.DeepLink) > 0 {
				resp.Deeplinkurl = mhDataItem.DeepLink
			}
		}
		resp.InteractionType = 4
		resp.Clickurl = mhDataItem.DownloadURL

		if deviceOs == "android" {
			if len(mhDataItem.PermissionURL) == 0 {
				mhDataItem.PermissionURL = "https://static.maplehaze.cn/static/permisson_ad.html"
			}
		}

		resp.AppName = mhDataItem.AppName
		resp.Developer = mhDataItem.Publisher
		resp.AppVersion = mhDataItem.AppVersion
		resp.PermissionDescUrl = mhDataItem.PermissionURL
		resp.PrivacyPolicyUrl = mhDataItem.PrivacyLink
		resp.ProductDescription = mhDataItem.AppInfo
	}

	if len(resp.Deeplinkurl) > 0 {
		var convUrls []string
		if len(mhDataItem.ConvTracks) > 0 {
			for _, convTracks := range mhDataItem.ConvTracks {
				if convTracks.ConvType == 10 {
					convUrls = convTracks.ConvURLS
				}
			}
		}
		if len(convUrls) > 0 {
			resp.DeeplinkClktrack = strings.Join(convUrls, ";")
		}
		resp.InteractionType = 3
	}

	var substyle int
	var splashtype int
	var feedStyle int
	var adstyleControl int
	switch mhDataItem.CrtType {
	case 11:
		if mhDataItem.Image == nil {
			return &MojiResponseObject{
				Code: 400,
			}, http.StatusOK
		}
		resp.Type = 1
		splashtype = 0
		for _, imageItem := range mhDataItem.Image {
			resp.Imgurl = imageItem.URL
			resp.Adwidth = imageItem.Width
			resp.Adheight = imageItem.Height
			if imageItem.Width > imageItem.Height {
				substyle = 1
				feedStyle = 1
			} else {
				substyle = 2
				feedStyle = 2
			}
		}
	case 20:
		if mhDataItem.Video == nil {
			return &MojiResponseObject{
				Code: 400,
			}, http.StatusOK
		}

		resp.Type = 2
		splashtype = 2
		resp.VideoUrl = mhDataItem.Video.VideoURL
		resp.VideoImageUrl = mhDataItem.Video.CoverURL
		resp.Duration = mhDataItem.Video.Duration / 1000
		resp.FileSize = float64(rand.Intn(4) + 10)
		resp.VideoLogo = mhDataItem.IconURL
		resp.LastFrameIcon = mhDataItem.IconURL
		resp.IconDesc = mhDataItem.AppName
		resp.Adwidth = mhDataItem.Video.Width
		resp.Adheight = mhDataItem.Video.Height

		if mhDataItem.Video.Width > mhDataItem.Video.Height {
			substyle = 1
			feedStyle = 3
		} else {
			substyle = 2
			feedStyle = 4
		}

		adstyleControl = 8

		if mhDataItem.InteractType == 0 {
			resp.ShortVideoType = 1
			resp.LastFrameText = "查看详情"
		} else if mhDataItem.InteractType == 1 {
			resp.ShortVideoType = 0
			resp.LastFrameText = "立即下载"
		}
	}

	if adtype == "3" {
		resp.Splashtype = splashtype
	}

	if adtype == "2" {
		resp.Substyle = substyle
		resp.FeedStyle = feedStyle
		resp.AdstyleControl = adstyleControl
	}

	return &MojiResponseObject{
		Code: 200,
		Data: resp,
	}, http.StatusOK
}
