package rtb_mgtv

type MgtvRequestObject struct {
	Version int                      `json:"version"`
	Id      string                   `json:"bid"`
	Device  *MgtvRequestDeviceObject `json:"device"`
	Imp     []*MgtvRequestImpObject  `json:"imp"`
}

type MgtvRequestDeviceObject struct {
	Height     int    `json:"sh"`
	Width      int    `json:"sw"`
	Network    int    `json:"connectiontype"`
	Carrier    int    `json:"carrier"`
	DeviceType int    `json:"type"`
	Imei       string `json:"imei"`
	Idfa       string `json:"idfa"`
	AndroidId  string `json:"anid"`
	Mac        string `json:"mac"`
	MacMd5     string `json:"mac_md5"`
	Os         string `json:"os"`
	Brand      string `json:"brand"`
	Model      string `json:"model"`
	Ip         string `json:"ip"`
	Ua         string `json:"ua"`
	OsVersion  string `json:"version"`
	Oaid       string `json:"oaid"`
}

type MgtvRequestImpObject struct {
	Location int                             `json:"location"`
	Price    int                             `json:"min_cpm_price"`
	Ctype    []int                           `json:"ctype"`
	TagId    string                          `json:"space_id"`
	Template []*MgtvRequestImpTemplateObject `json:"template"`
}

type MgtvRequestImpTemplateObject struct {
	TemplateId  int                                   `json:"style"`
	AdSpaceSize int                                   `json:"ad_space_size"`
	Interact    *MgtvRequestImpTemplateInteractObject `json:"interact"`
}

type MgtvRequestImpTemplateInteractObject struct {
	TmplId []int `json:"tmpl_id,omitempty"`
}

type MgtvResponseObject struct {
	Version int                          `json:"version"`
	Code    int                          `json:"err_code"`
	Id      string                       `json:"bid"`
	BidList []*MgtvResponseBidListObject `json:"ads,omitempty"`
}

type MgtvResponseBidListObject struct {
	Duration                int                             `json:"duration"`
	Ctype                   int                             `json:"ctype"`
	WakeType                int                             `json:"wake_type"`
	AdSpaceSize             int                             `json:"ad_space_size"`
	LandOpenMode            int                             `json:"land_open_mode"`
	Width                   int                             `json:"width,omitempty"`
	Height                  int                             `json:"height,omitempty"`
	Style                   int                             `json:"style,omitempty"`
	Price                   int                             `json:"price,omitempty"`
	TmplId                  int                             `json:"tmpl_id,omitempty"`
	TagId                   string                          `json:"space_id,omitempty"`
	Deeplink                string                          `json:"schema_url,omitempty"`
	CreativeId              string                          `json:"creative_id,omitempty"`
	AdUrl                   string                          `json:"ad_url,omitempty"`
	IconUrl                 string                          `json:"icon_url,omitempty"`
	ClickThroughUrl         string                          `json:"click_through_url,omitempty"`
	DownloadClickThroughUrl string                          `json:"download_click_through_url,omitempty"`
	Title                   string                          `json:"title,omitempty"`
	AdDesc                  string                          `json:"ad_desc,omitempty"`
	WinNotice               string                          `json:"win_notice,omitempty"`
	Curl                    []string                        `json:"curl,omitempty"`
	Iurl                    []MgtvResponseBidListIurlObject `json:"iurl,omitempty"`
}

type MgtvResponseBidListIurlObject struct {
	Event int    `json:"event"`
	Url   string `json:"url"`
}
