package rtb_mgtv

import (
	"encoding/json"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func HandleByMgTv(c *gin.Context, channel string) (*MgtvResponseObject, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()
	req := &MgtvRequestObject{}
	err := json.Unmarshal(bodyContent, req)
	if err != nil {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	var deviceOs string
	tmpOs := strings.Split(req.Device.Os, "_")

	if len(tmpOs) < 2 {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	switch strings.ToLower(tmpOs[0]) {
	case "ios":
		deviceOs = "ios"
	case "android":
		deviceOs = "android"
	default:
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	var connectType int
	switch req.Device.Network {
	case 1:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.Device.Carrier {
	case 0, 2:
		carrier = 1
	case 1:
		carrier = 2
	case 3:
		carrier = 3
	default:
		carrier = 0
	}

	adsCount := 1
	var resultfulImp []*MgtvRequestImpObject
	var configList []*models.RtbConfigByTagIDStu
	for _, imp := range req.Imp {
		var price int
		var styleIds []string
		for _, template := range imp.Template {
			styleId := strconv.Itoa(template.TemplateId) + "-" + strconv.Itoa(template.AdSpaceSize)
			if template.Interact != nil {
				if template.Interact.TmplId != nil && len(template.Interact.TmplId) > 0 {
					for _, tmplId := range template.Interact.TmplId {
						tmpStyleId := styleId + "-" + strconv.Itoa(tmplId)
						styleIds = append(styleIds, tmpStyleId)
					}
				} else {
					styleIds = append(styleIds, styleId)
				}
			} else {
				styleIds = append(styleIds, styleId)
			}
		}

		price = imp.Price
		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.TagId, deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		configData := (*adxInfo)[0]
		resultfulImp = append(resultfulImp, imp)
		configList = append(configList, &configData)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.Device.Brand
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: "",
			AppName:     "",
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           deviceOs,
			Manufacturer: manufacturer,
			Model:        req.Device.Model,
			IP:           req.Device.Ip,
			Ua:           req.Device.Ua,
			Mac:          req.Device.Mac,
			AndroidID:    req.Device.AndroidId,
			OsVersion:    tmpOs[1],
			Imei:         req.Device.Imei,
			Oaid:         req.Device.Oaid,
			Idfa:         req.Device.Idfa,
			ScreenWidth:  req.Device.Width,
			ScreenHeight: req.Device.Height,
			DeviceType:   1,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	var impItem *MgtvRequestImpObject
	for _, imp := range resultfulImp {
		if imp.TagId == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}

	var bidList []*MgtvResponseBidListObject
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=%%SETTLE_PRICE%%" + "&log=" + url.QueryEscape(mhDataItem.Log)

		var iurlArray []MgtvResponseBidListIurlObject
		for _, link := range mhDataItem.ImpressionLink {
			var iurl MgtvResponseBidListIurlObject
			iurl.Event = 0
			iurl.Url = link
			iurlArray = append(iurlArray, iurl)
		}

		bidItem := MgtvResponseBidListObject{
			WakeType:                0,
			LandOpenMode:            0,
			Price:                   ecpm,
			TagId:                   impItem.TagId,
			ClickThroughUrl:         mhDataItem.LandpageURL,
			DownloadClickThroughUrl: mhDataItem.DownloadURL,
			Title:                   mhDataItem.Title,
			AdDesc:                  mhDataItem.Description,
			WinNotice:               winURL,
			Curl:                    mhDataItem.ClickLink,
			Iurl:                    iurlArray,
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				bidItem.Deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					bidItem.Deeplink = mhDataItem.MarketURL
				}
			}

			if deviceOs == "ios" {
				bidItem.LandOpenMode = 2
			}
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				bidItem.Deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					bidItem.Deeplink = mhDataItem.DeepLink
				}
			}
		}

		if deviceOs == "ios" {
			if strings.HasPrefix(bidItem.Deeplink, "http://") || strings.HasPrefix(bidItem.Deeplink, "https://") {
				bidItem.Deeplink = ""
			}
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image != nil {
				for _, imageItem := range mhDataItem.Image {
					bidItem.AdUrl = imageItem.URL
					bidItem.Width = imageItem.Width
					bidItem.Height = imageItem.Height
				}
			}
			if reqRtbConfig.ImageStyleID == "" {
				continue
			}

			styleArray := strings.Split(reqRtbConfig.ImageStyleID, "-")

			var style int
			var adSpaceSize int
			var tmplId int
			if len(styleArray) == 4 {
				style, _ = strconv.Atoi(styleArray[0])
				tmpAdSpaceSize, _ := strconv.Atoi(styleArray[2])
				tmplId, _ = strconv.Atoi(styleArray[3])
				adSpaceSize = -tmpAdSpaceSize
			} else {
				style, _ = strconv.Atoi(styleArray[0])
				adSpaceSize, _ = strconv.Atoi(styleArray[1])
				if len(styleArray) == 3 {
					tmplId, _ = strconv.Atoi(styleArray[2])
					bidItem.TmplId = tmplId
				}
			}

			bidItem.Style = style
			bidItem.AdSpaceSize = adSpaceSize
			bidItem.Ctype = 1
		case 20:
			if mhDataItem.Video != nil {
				bidItem.AdUrl = mhDataItem.Video.VideoURL
				bidItem.Duration = mhDataItem.Video.Duration / 1000
				bidItem.Width = mhDataItem.Video.Width
				bidItem.Height = mhDataItem.Video.Height
			}
			if reqRtbConfig.VideoStyleID == "" {
				continue
			}

			styleArray := strings.Split(reqRtbConfig.VideoStyleID, "-")

			var style int
			var adSpaceSize int
			var tmplId int
			if len(styleArray) == 4 {
				style, _ = strconv.Atoi(styleArray[0])
				tmpAdSpaceSize, _ := strconv.Atoi(styleArray[2])
				tmplId, _ = strconv.Atoi(styleArray[3])
				adSpaceSize = -tmpAdSpaceSize
			} else {
				style, _ = strconv.Atoi(styleArray[0])
				adSpaceSize, _ = strconv.Atoi(styleArray[1])
				if len(styleArray) == 3 {
					tmplId, _ = strconv.Atoi(styleArray[2])
					bidItem.TmplId = tmplId
				}
			}

			bidItem.Style = style
			bidItem.AdSpaceSize = adSpaceSize
			bidItem.Ctype = 2
		}

		bidList = append(bidList, &bidItem)
	}

	if len(bidList) == 0 {
		return &MgtvResponseObject{
			Id:      req.Id,
			Version: req.Version,
			Code:    http.StatusNoContent,
		}, http.StatusOK
	}
	resp := &MgtvResponseObject{
		Id:      req.Id,
		Version: req.Version,
		Code:    http.StatusOK,
		BidList: bidList,
	}

	return resp, http.StatusOK
}
