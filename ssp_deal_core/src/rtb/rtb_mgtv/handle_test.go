package rtb_mgtv

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"
)

func TestHandlePrice(t *testing.T) {
	client := &http.Client{}
	reqByte := []byte("")
	requestUrl := "https://sandbox.maplehaze.cn/rtb/request?channel=57"
	requestPost, _ := http.NewRequest("POST", requestUrl, bytes.NewReader(reqByte))
	requestPost.Header.Add("Content-Type", "application/json")
	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	fmt.Println(string(bodyContent))
}
