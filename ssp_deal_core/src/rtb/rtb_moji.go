package rtb

import (
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByMoji ...
func HandleByMoji(c *gin.Context, channel string) *map[string]interface{} {
	// channel := c.Query("channel")

	reqAdID := c.Query("adid")
	reqAppBundle := c.Query("pkgname")
	reqAppName := c.Query("appname")
	reqSessionID := c.Query("sessionid")
	reqAdType := c.Query("adtype")
	reqAdStyle := c.Query("adstyle")
	// reqTradeLevel := c.Query("tradelevel")
	reqNet := c.Query("net")
	reqCarrier := c.Query("carrier")
	reqOs := c.Query("os")
	reqOsv := c.Query("osv")
	reqBasicPrice := c.Query("basic_price")
	reqImei := c.Query("imei")
	reqAndroidID := c.Query("andid")
	reqOaid := c.Query("oaid")
	reqIdfa := c.Query("idfa")
	// reqOpenUDID := c.Query("openudid")
	reqDevice := c.Query("device")
	reqScreenWidth := c.Query("scrwidth")
	reqScreenHeight := c.Query("scrheight")
	reqUA := c.Query("ua")
	reqIP := c.Query("ip")
	// fmt.Println(reqAdID)
	// fmt.Println(reqAppBundle)
	// fmt.Println(reqAppName)
	// fmt.Println(reqSessionID)
	// fmt.Println(reqAdType)
	// fmt.Println(reqAdStyle)
	// fmt.Println(reqTradeLevel)
	// fmt.Println(reqNet)
	// fmt.Println(reqCarrier)
	// fmt.Println(reqOs)
	// fmt.Println(reqOsv)
	// fmt.Println(reqBasicPrice)
	// fmt.Println(reqImei)
	// fmt.Println(reqAndroidID)
	// fmt.Println(reqOaid)
	// fmt.Println(reqIdfa)
	// fmt.Println(reqOpenUDID)
	// fmt.Println(reqDevice)
	// fmt.Println(reqScreenWidth)
	// fmt.Println(reqScreenHeight)
	// fmt.Println(reqUA)
	// fmt.Println(reqIP)

	reqDeviceOs := ""
	reqDeviceImei := ""
	reqDeviceIdfa := ""
	reqDeviceAndroidID := ""
	reqDeviceOaid := ""

	if reqOs == "0" {
		reqDeviceOs = "android"
		reqDeviceImei = reqImei
		reqDeviceAndroidID = reqAndroidID
		reqDeviceOaid = reqOaid
	} else if reqOs == "1" {
		reqDeviceOs = "ios"
		reqDeviceIdfa = reqIdfa
	} else {
		return jsonMojiNoBidReturn("")
	}

	if reqNet == "5" {
		reqNet = "7"
	}

	// reqTagID := "mjtq_" + reqAdID + "_" + reqDeviceOs
	reqTagID := reqAdID
	// for test
	// reqTagID = "hupu_1001_android"
	// if rand.Intn(10000) == 0 {
	// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, utils.ConvertStringToInt(reqBasicPrice))
	// }

	// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
	// 	return jsonMojiNoBidReturn("not active 0")
	// }

	// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, utils.ConvertStringToInt(reqBasicPrice))
	rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqDeviceOs, "", "", utils.ConvertStringToInt(reqBasicPrice))

	if rtbConfigArrayByTagID == nil {
		return jsonMojiNoBidReturn("not active 1")
	}

	// fmt.Println("xxxxxx")
	// fmt.Println(len(*rtbConfigArrayByTagID))
	// fmt.Println(rtbConfigArrayByTagID)
	// fmt.Println("yyyyyy")
	if len(*rtbConfigArrayByTagID) == 0 {
		return jsonMojiNoBidReturn("not active 1")
	}

	reqRtbConfig := (*rtbConfigArrayByTagID)[0]

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: reqAppBundle,
			AppName:     reqAppName,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqDeviceOs,
			OsVersion:    reqOsv,
			Model:        reqDevice,
			Manufacturer: "",
			Imei:         reqDeviceImei,
			AndroidID:    reqDeviceAndroidID,
			Oaid:         reqDeviceOaid,
			Idfa:         reqDeviceIdfa,
			Ua:           reqUA,
			DeviceType:   1,
			IP:           reqIP,
			ScreenWidth:  utils.ConvertStringToInt(reqScreenWidth),
			ScreenHeight: utils.ConvertStringToInt(reqScreenHeight),
			// Mac:          wpsReq.Device.Mac,
			// MacMd5:       wpsReq.Device.MacMd5,
		},
		Network: models.MHReqNetwork{
			ConnectType: utils.ConvertStringToInt(reqNet),
			Carrier:     utils.ConvertStringToInt(reqCarrier),
		},
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return jsonMojiNoBidReturn("no fill 0")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonMojiNoBidReturn("no fill 1")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[0]

	// 统计
	models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

	// 判定上游返回ecpm是否大于底价
	if mhDataItem.Ecpm < reqRtbConfig.Price {
		return jsonMojiNoBidReturn("no fill 1")
	}
	ecpm := mhDataItem.Ecpm

	winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${winprice}" + "&log=" + url.QueryEscape(mhDataItem.Log)

	isVideoType := 0
	if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
		isVideoType = 1
	}

	mojiBidObjMap := map[string]interface{}{}
	if isVideoType == 1 {
		// 视频类型
		mojiBidObjMap["adid"] = reqAdID
		mojiBidObjMap["sessionid"] = reqSessionID
		mojiBidObjMap["type"] = 2
		mojiBidObjMap["price"] = ecpm
		mojiBidObjMap["adtype"] = utils.ConvertStringToInt(reqAdType)
		mojiBidObjMap["adstyle"] = utils.ConvertStringToInt(reqAdStyle)
		mojiBidObjMap["chargingtype"] = 1
		mojiBidObjMap["winnoticeurl"] = winURL
		// mojiBidObjMap["clickurl"] = mhDataItem.AdURL
		mojiBidObjMap["adwidth"] = mhDataItem.Video.Width
		mojiBidObjMap["adheight"] = mhDataItem.Video.Height
		mojiBidObjMap["adtitle"] = mhDataItem.Title
		mojiBidObjMap["adtext"] = mhDataItem.Description
		mojiBidObjMap["imptrack"] = strings.Join(mhDataItem.ImpressionLink, ";")
		mojiBidObjMap["clktrack"] = strings.Join(mhDataItem.ClickLink, ";")

		// track url
		var videoStartTrackArray []string
		var videoFinishTrackArray []string
		for _, trackItem := range mhDataItem.Video.EventTracks {
			if trackItem.EventType == 100 {
				for _, trackEventItem := range trackItem.EventURLS {
					videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
				}
			} else if trackItem.EventType == 103 {
				for _, trackEventItem := range trackItem.EventURLS {
					videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
				}
			}
		}
		if len(videoFinishTrackArray) > 0 {
			mojiBidTrackObjItemMap := map[string]interface{}{}
			mojiBidTrackObjItemMap["100"] = strings.Join(videoFinishTrackArray, ";")
			mojiBidObjMap["videoprogressurl"] = mojiBidTrackObjItemMap
		}

		// icon url
		if len(mhDataItem.IconURL) > 0 {
			mojiBidObjMap["iconurl"] = mhDataItem.IconURL
			mojiBidObjMap["videoLogo"] = mhDataItem.IconURL
			mojiBidObjMap["lastFrameIcon"] = mhDataItem.IconURL
		}
		if mhDataItem.InteractType == 0 {
			// deep link
			if len(mhDataItem.DeepLink) > 0 {
				mojiBidObjMap["deeplinkurl"] = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					mojiBidObjMap["deeplinkurl"] = mhDataItem.MarketURL
				}
			}

			mojiBidObjMap["shortVideoType"] = 1
			mojiBidObjMap["lastFrameText"] = "查看详情"
			mojiBidObjMap["clickurl"] = mhDataItem.LandpageURL
		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				mojiBidObjMap["deeplinkurl"] = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					mojiBidObjMap["deeplinkurl"] = mhDataItem.DeepLink
				}
			}

			mojiBidObjMap["shortVideoType"] = 0
			mojiBidObjMap["lastFrameText"] = "立即下载"
			mojiBidObjMap["clickurl"] = mhDataItem.DownloadURL
		}

		// app name
		if len(mhDataItem.AppName) > 0 {
			mojiBidObjMap["iconDesc"] = mhDataItem.AppName
		}
		// video
		mojiBidObjMap["videoImageUrl"] = mhDataItem.Video.CoverURL
		mojiBidObjMap["videoUrl"] = mhDataItem.Video.VideoURL
		mojiBidObjMap["duration"] = mhDataItem.Video.Duration
		mojiBidObjMap["fileSize"] = rand.Intn(4) + 10

	} else {
		// 图片类型
		mojiBidObjMap["adid"] = reqAdID
		mojiBidObjMap["sessionid"] = reqSessionID
		mojiBidObjMap["type"] = 1
		mojiBidObjMap["price"] = ecpm
		mojiBidObjMap["adtype"] = utils.ConvertStringToInt(reqAdType)
		mojiBidObjMap["adstyle"] = utils.ConvertStringToInt(reqAdStyle)
		mojiBidObjMap["chargingtype"] = 1
		mojiBidObjMap["winnoticeurl"] = winURL
		// mojiBidObjMap["clickurl"] = mhDataItem.AdURL
		if len(mhDataItem.DownloadURL) > 0 {
			mojiBidObjMap["clickurl"] = mhDataItem.DownloadURL
		} else {
			mojiBidObjMap["clickurl"] = mhDataItem.LandpageURL
		}
		mojiBidObjMap["imgurl"] = mhDataItem.Image[0].URL
		mojiBidObjMap["adwidth"] = mhDataItem.Image[0].Width
		mojiBidObjMap["adheight"] = mhDataItem.Image[0].Height
		mojiBidObjMap["adtitle"] = mhDataItem.Title
		mojiBidObjMap["adtext"] = mhDataItem.Description
		mojiBidObjMap["imptrack"] = strings.Join(mhDataItem.ImpressionLink, ";")
		mojiBidObjMap["clktrack"] = strings.Join(mhDataItem.ClickLink, ";")
		if len(mhDataItem.IconURL) > 0 {
			mojiBidObjMap["iconurl"] = mhDataItem.IconURL
		}
		// deep link
		if len(mhDataItem.DeepLink) > 0 {
			mojiBidObjMap["deeplinkurl"] = mhDataItem.DeepLink
		}
	}

	respMap := map[string]interface{}{}
	respMap["code"] = 200
	respMap["data"] = mojiBidObjMap

	return &respMap
}

func jsonMojiNoBidReturn(reason string) *map[string]interface{} {
	// fmt.Println(reason)

	respMap := map[string]interface{}{}
	respMap["code"] = 400

	return &respMap
}
