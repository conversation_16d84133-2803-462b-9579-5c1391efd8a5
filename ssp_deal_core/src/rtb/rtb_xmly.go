package rtb

import (
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/xmly_v2"
	"mh_proxy/utils"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByXmlyV2 ...
func HandleByXmlyV2(c *gin.Context, channel string) (*xmly_v2.BidResponse, *models.RtbConfigByTagIDStu) {

	bodyContent, err := c.GetRawData()

	xmlyReq := &xmly_v2.BidRequest{}
	err = proto.Unmarshal(bodyContent, xmlyReq)

	if err != nil {
		fmt.Println(err)
		return xmlyV2NoBidReturn("error", "parser error", nil)
	}
	// fmt.Println(xmlyReq)
	// fmt.Println(xmlyReq.GetApiVersion())
	// fmt.Println(xmlyReq.GetDevice().GetOs())
	// fmt.Println(xmlyReq.GetDevice().GetIdfa())
	// fmt.Println(xmlyReq.GetDevice().GetUa())
	// fmt.Println(xmlyReq.GetDevice().GetIp())

	reqOs := ""
	if strings.Contains(strings.ToLower(xmlyReq.GetDevice().GetOs()), "android") {
		reqOs = "android"
	} else if strings.Contains(strings.ToLower(xmlyReq.GetDevice().GetOs()), "ios") {
		reqOs = "ios"
	} else {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "wrong os", nil)
	}

	reqDeivceMake := xmlyReq.GetDevice().GetMake()
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	reqConnectType := 1
	// 网络连接类型 0，2G；1，3G；2，4G；3，wifi；4，其它；5，5G
	if xmlyReq.GetDevice().GetConnectiontype() == 0 {
		reqConnectType = 2
	} else if xmlyReq.GetDevice().GetConnectiontype() == 1 {
		reqConnectType = 3
	} else if xmlyReq.GetDevice().GetConnectiontype() == 2 {
		reqConnectType = 4
	} else if xmlyReq.GetDevice().GetConnectiontype() == 3 {
		reqConnectType = 1
	} else if xmlyReq.GetDevice().GetConnectiontype() == 4 {
		reqConnectType = 0
	} else if xmlyReq.GetDevice().GetConnectiontype() == 5 {
		reqConnectType = 7
	} else {
		reqConnectType = 1
	}

	reqCarrier := 0
	// 运营商 0，移动；1，联通； 2，电信；3，其它
	if xmlyReq.GetDevice().GetCarrier() == 0 {
		reqCarrier = 1
	} else if xmlyReq.GetDevice().GetCarrier() == 1 {
		reqCarrier = 2
	} else if xmlyReq.GetDevice().GetCarrier() == 2 {
		reqCarrier = 3
	} else if xmlyReq.GetDevice().GetCarrier() == 3 {
		reqCarrier = 0
	} else {
		reqCarrier = 0
	}

	reqOsv := xmlyReq.GetDevice().GetOsv()
	if len(reqOsv) > 0 {
	} else {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "wrong osv", nil)
	}
	reqImps := xmlyReq.GetImp()
	// fmt.Println(len(reqSlots))
	if len(reqImps) == 0 {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "wrong slots", nil)
	}

	var reqOKImps []*xmly_v2.BidRequest_Imp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range reqImps {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := item.GetTagid()

		reqTemplateID := item.GetTemplateid()

		reqPrice := item.GetBidfloor()
		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return xmlyV2NoBidReturn(xmlyReq.GetId(), "not active")
		// }
		reqDealIDArray := item.GetDealid()
		// fmt.Println("xmly_dealid: ", reqDealIDArray)
		reqDealID := ""
		if len(reqDealIDArray) > 0 {
			reqDealID = reqDealIDArray[0]
		}

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, reqTemplateID, reqDealID, int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return xmlyV2NoBidReturn(xmlyReq.GetId(), "not active", nil)
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}
	// fmt.Println("aaaaaa")
	// fmt.Println(reqRtbConfig)
	// fmt.Println("bbbbbb")

	if len(reqOKImps) == 0 {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "wrong imp", nil)
	}
	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	var caidMultiList []models.MHReqCAIDMulti
	if len(xmlyReq.Device.GetCaids()) > 0 {
		for _, item := range xmlyReq.Device.GetCaids() {
			var tmpCaid models.MHReqCAIDMulti
			tmpCaid.CAID = item.GetCaid()
			tmpCaid.CAIDVersion = item.GetVersion()
			caidMultiList = append(caidMultiList, tmpCaid)
		}
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: xmlyReq.GetApp().GetPkgname(),
			AppName:     xmlyReq.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              reqOs,
			OsVersion:       reqOsv,
			Model:           xmlyReq.GetDevice().GetModel(),
			Manufacturer:    reqDeivceMake,
			ImeiMd5:         strings.ToLower(xmlyReq.GetDevice().GetImeimd5()),
			AndroidIDMd5:    strings.ToLower(xmlyReq.GetDevice().GetAndroididmd5()),
			Oaid:            xmlyReq.GetDevice().GetOaid(),
			Idfa:            xmlyReq.GetDevice().GetIdfa(),
			Ua:              xmlyReq.GetDevice().GetUa(),
			ScreenWidth:     int(xmlyReq.GetDevice().GetW()),
			ScreenHeight:    int(xmlyReq.GetDevice().GetH()),
			DeviceType:      1,
			IP:              xmlyReq.GetDevice().GetIp(),
			AppStoreVersion: xmlyReq.GetDevice().GetAsVersion(),
			HMSCoreVersion:  xmlyReq.GetDevice().GetHmsVersion(),
			BootMark:        xmlyReq.GetDevice().GetBootMark(),
			UpdateMark:      xmlyReq.GetDevice().GetUpdateMark(),
			CAIDMulti:       caidMultiList,
			SystemUpdateSec: xmlyReq.GetDevice().GetOsUpdateTime(),
			AppList:         getXmlyAppList(xmlyReq.GetUser().GetApplist()),
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "no fill", &reqRtbConfig)
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "no fill", &reqRtbConfig)
	}

	var impItem *xmly_v2.BidRequest_Imp
	for _, imp := range reqOKImps {
		if imp.GetTagid() == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "no fill", &reqRtbConfig)
	}
	//////////////////////////////////////////////////////////////////////////////////////////////
	xmlyRespSeatBid := xmly_v2.BidResponse_SeatBid{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		xmlyRespBid := xmly_v2.BidResponse_Bid{
			Id:    proto.String("bid_" + utils.ConvertInt64ToString(utils.GetCurrentSecond())),
			Impid: proto.String(impItem.GetId()),
			Price: proto.Float32(float32(ecpm)),
		}

		// winurl
		winUrl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__SETTLE_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// crid
		xmlyRespBid.Crid = proto.String(utils.GetMd5(mhDataItem.Title))
		if len(mhDataItem.Crid) > 0 {
			xmlyRespBid.Crid = proto.String(mhDataItem.Crid)
		}

		// fmt.Println("xxx")
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}
		impTrackArray = append(impTrackArray, winUrl)
		xmlyRespBid.Monitorurls = impTrackArray

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(int(xmlyReq.GetDevice().GetW())), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(int(xmlyReq.GetDevice().GetH())), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}
		xmlyRespBid.Clickmonitorurls = clkTrackArray

		// video start finish link
		// var videoStartTrackArray []string
		// var videoFinishTrackArray []string
		// for _, trackItem := range mhDataItem.Video.EventTracks {
		// 	if trackItem.EventType == 100 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
		// 		}
		// 	} else if trackItem.EventType == 103 {
		// 		for _, trackEventItem := range trackItem.EventURLS {
		// 			videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
		// 		}
		// 	}
		// }

		// interact type
		// xmlyRespBid.Clickurl = proto.String(mhDataItem.AdURL)
		xmlyRespBid.Clickurl = proto.String(mhDataItem.LandpageURL)
		if mhDataItem.InteractType == 0 {
			xmlyRespBid.Clicktype = proto.Int32(0)
		} else if mhDataItem.InteractType == 1 {
			xmlyRespBid.Clicktype = proto.Int32(2)
		}

		if reqOs == "ios" {
			xmlyRespBid.Clicktype = proto.Int32(0)
		}

		// app info
		xmlyRespBidAppInfo := xmly_v2.BidResponse_Bid_AppInfo{}

		if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				xmlyRespBid.Clicktype = proto.Int32(2)
				xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.MarketURL)
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					xmlyRespBid.Clicktype = proto.Int32(2)
					xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.DeepLink)
				}
			}
		} else if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				xmlyRespBid.Clicktype = proto.Int32(2)
				xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.DeepLink)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					xmlyRespBid.Clicktype = proto.Int32(2)
					xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.MarketURL)
				}
			}
		}

		if len(mhDataItem.DeepLink) > 0 {
			xmlyRespBid.Clicktype = proto.Int32(2)
			xmlyRespBidAppInfo.Dplink = proto.String(mhDataItem.DeepLink)
		}

		if len(mhDataItem.AppName) > 0 {
			xmlyRespBidAppInfo.Appname = proto.String(mhDataItem.AppName)
		}

		if len(mhDataItem.PackageName) > 0 {
			xmlyRespBidAppInfo.Pkgname = proto.String(mhDataItem.PackageName)
		}
		if len(mhDataItem.AppVersion) > 0 {
			xmlyRespBidAppInfo.Appversion = proto.String(mhDataItem.AppVersion)
		}
		if len(mhDataItem.Publisher) > 0 {
			xmlyRespBidAppInfo.Appdeveloper = proto.String(mhDataItem.Publisher)
		}
		if len(mhDataItem.PrivacyLink) > 0 {
			xmlyRespBidAppInfo.Appprivacypolicy = proto.String(mhDataItem.PrivacyLink)
		}
		if len(mhDataItem.Permission) > 0 {
			xmlyRespBidAppInfo.Apppermissionurl = proto.String(mhDataItem.Permission)
		}
		if len(mhDataItem.DownloadURL) > 0 {
			xmlyRespBidAppInfo.Downloadurl = proto.String(mhDataItem.DownloadURL)
		}
		if mhDataItem.PackageSize > 0 {
			xmlyRespBidAppInfo.Appsize = proto.String(strconv.FormatInt(mhDataItem.PackageSize, 10))
		}
		if len(mhDataItem.ConvTracks) > 0 {
			for _, convTracks := range mhDataItem.ConvTracks {
				if convTracks.ConvType == 10 {
					xmlyRespBidAppInfo.Evokemonitorurl = convTracks.ConvURLS
				}
			}

		}

		// deeplink tracking
		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			var deepLinkTrackOKArray []string
			var deepLinkTrackFailedArray []string
			for _, trackItem := range mhDataItem.ConvTracks {
				if trackItem.ConvType == 10 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
					}
				} else if trackItem.ConvType == 11 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
					}
				}
			}
			if len(deepLinkTrackOKArray) > 0 {
				xmlyRespBidAppInfo.Evokemonitorurl = deepLinkTrackOKArray
			}

			if len(deepLinkTrackFailedArray) > 0 {
				xmlyRespBidAppInfo.Evokefailmonitorurl = deepLinkTrackFailedArray
			}
		}

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 || len(mhDataItem.AppName) > 0 || len(mhDataItem.PackageName) > 0 {
			xmlyRespBid.Appinfo = &xmlyRespBidAppInfo
		}

		xmlyRespBidNative := xmly_v2.BidResponse_Bid_Native{}
		// 参考: https://github.com/xmlyads/adx/wiki/%E5%AE%9E%E6%97%B6%E5%8D%8F%E8%AE%AE%E2%80%94V2.0
		if isVideoType == 0 {
			// title
			xmlyRespBidNativeTitle := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeTitle.Name = proto.String("name")
			xmlyRespBidNativeTitle.Value = proto.String(mhDataItem.Title)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeTitle)

			// description
			xmlyRespBidNativeDescription := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeDescription.Name = proto.String("description")
			xmlyRespBidNativeDescription.Value = proto.String(mhDataItem.Description)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeDescription)

			// image url
			xmlyRespBidNativeImageURL := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeImageURL.Name = proto.String("cover")
			xmlyRespBidNativeImageURL.Value = proto.String(mhDataItem.Image[0].URL)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeImageURL)

			// icon url
			xmlyRespBidNativeIconURL := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeIconURL.Name = proto.String("logo")
			xmlyRespBidNativeIconURL.Value = proto.String(mhDataItem.IconURL)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeIconURL)
		} else {
			// title
			xmlyRespBidNativeTitle := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeTitle.Name = proto.String("name")
			xmlyRespBidNativeTitle.Value = proto.String(mhDataItem.Title)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeTitle)

			// description
			xmlyRespBidNativeDescription := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeDescription.Name = proto.String("description")
			xmlyRespBidNativeDescription.Value = proto.String(mhDataItem.Description)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeDescription)

			// image url
			xmlyRespBidNativeImageURL := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeImageURL.Name = proto.String("cover")
			xmlyRespBidNativeImageURL.Value = proto.String(mhDataItem.Video.CoverURL)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeImageURL)

			// video url
			xmlyRespBidNativeVideoURL := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeVideoURL.Name = proto.String("videoCover")
			xmlyRespBidNativeVideoURL.Value = proto.String(mhDataItem.Video.VideoURL)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeVideoURL)

			// icon url
			xmlyRespBidNativeIconURL := xmly_v2.BidResponse_Bid_Native_Attr{}
			xmlyRespBidNativeIconURL.Name = proto.String("logo")
			xmlyRespBidNativeIconURL.Value = proto.String(mhDataItem.IconURL)

			xmlyRespBidNative.Attr = append(xmlyRespBidNative.Attr, &xmlyRespBidNativeIconURL)
		}

		xmlyRespBid.Native = &xmlyRespBidNative

		xmlyRespSeatBid.Bid = append(xmlyRespSeatBid.Bid, &xmlyRespBid)
	}
	if len(xmlyRespSeatBid.GetBid()) == 0 {
		return xmlyV2NoBidReturn(xmlyReq.GetId(), "no fill", &reqRtbConfig)
	}

	xmlyResp := &xmly_v2.BidResponse{
		Id: proto.String(xmlyReq.GetId()),
	}
	xmlyResp.Seatbid = append(xmlyResp.Seatbid, &xmlyRespSeatBid)

	// fmt.Println("1")
	// fmt.Println(xmlyResp)
	// fmt.Println("2")

	return xmlyResp, &reqRtbConfig
}

func xmlyV2NoBidReturn(reqID string, reason string, reqRtbConfig *models.RtbConfigByTagIDStu) (*xmly_v2.BidResponse, *models.RtbConfigByTagIDStu) {
	// fmt.Println(reason)

	xmlyNoResp := &xmly_v2.BidResponse{
		Id: proto.String(reqID),
	}
	return xmlyNoResp, reqRtbConfig
}

func getXmlyAppList(appListStr string) []int {
	if len(appListStr) == 0 {
		return []int{}
	}

	appIDList := strings.Split(appListStr, ",")
	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := xmlyAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var xmlyAppListCodeMap = map[string]int{
	"1088":     1001,
	"1002":     1002,
	"10125":    1003,
	"10102674": 1004,
	"10126":    1005,
	"10112":    1006,
	"10127":    1007,
	"100059":   1008,
	"10065042": 1009,
	"10128":    1010,
	"10113":    1011,
	"100019":   1012,
	"10129":    1013,
	"10120":    1014,
	"100092":   1015,
	"1090":     1016,
	"1085":     1017,
	"10130":    1018,
	"100028":   1019,
	"10114":    1020,
	"10131":    1021,
	"10132":    1022,
	"100033":   1023,
	"10133":    1024,
	"1091":     1025,
	"1093":     1026,
	"10134":    1027,
	"10135":    1028,
	"1096":     1029,
	"10136":    1030,
}
