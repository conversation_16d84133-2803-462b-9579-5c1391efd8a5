package rtb_uc

import (
	"bytes"
	"fmt"
	"io"
	"mh_proxy/db"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestHandleByUc(t *testing.T) {
	db.InitBigCache()
	c := &gin.Context{Request: &http.Request{}}

	c.Request.Body = io.NopCloser(bytes.NewBufferString(`{"id": "123", "device": {"os": "ios", "ua": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1"}, "app": {"id": "123", "name": "test"}, "imp": [{"id": "123", "cpmfloor": 100, "ext": {"templateid": [1, 2, 3]}}]}`))
	resp, status := HandleByUc(c, "44")
	fmt.Println(resp, status)
}
