package rtb_uc

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// UcRequestObject Objects
type UcRequestObject struct {
	Id     string                 `json:"id"`
	Device *UcRequestDeviceObject `json:"device"`
	App    *UcRequestAppObject    `json:"app"`
	Imp    []*UcRequestImpObject  `json:"imp"`
}

type UcRequestDeviceObject struct {
	Ua             string `json:"ua"`
	Ip             string `json:"ip"`
	Model          string `json:"model"`
	Brand          string `json:"brand"`
	Os             string `json:"os"`
	Osv            string `json:"osv"`
	Carrier        string `json:"carrier"`
	Idfa           string `json:"idfa"`
	Didmd5         string `json:"didmd5"`
	Oaid           string `json:"oaid"`
	Oaidmd5        string `json:"oaidmd5"`
	Caid           string `json:"caid"`
	Version        string `json:"version"`
	Aaid           string `json:"aaid"`
	Ditmd5         string `json:"ditmd5"`
	Sutmd5         string `json:"sutmd5"`
	Sstmd5         string `json:"sstmd5"`
	Connectiontype int    `json:"connectiontype"`
}

type UcRequestAppObject struct {
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
}

type UcRequestImpObject struct {
	Id        string             `json:"id"`
	SceneType int                `json:"scene_type"`
	CpmFloor  int                `json:"cpm_floor"`
	AdNum     int                `json:"ad_num"`
	W         int                `json:"w"`
	H         int                `json:"h"`
	Ext       UcRequestExtObject `json:"ext"`
}

type UcRequestDeviceCaidObject struct {
	Caid    string `json:"caid"`
	Version string `json:"version"`
}

type UcRequestExtObject struct {
	SupportWechat int   `json:"support_wechat"`
	Templateid    []int `json:"templateid"`
}

// UcResponseObject Objects
type UcResponseObject struct {
	Nbr     int                        `json:"nbr"`
	Id      string                     `json:"id"`
	Seatbid []*UcResponseSeatbidObject `json:"seatbid"`
}

type UcResponseSeatbidObject struct {
	Bid []*UcResponseBidObject `json:"bid"`
}

type UcResponseBidObject struct {
	Price int                  `json:"price"`
	Id    string               `json:"id"`
	Impid string               `json:"impid"`
	Nurl  string               `json:"nurl"`
	Adid  string               `json:"adid"`
	Ext   *UcResponseExtObject `json:"ext"`
	Adm   *UcResponseAdmObject `json:"adm"`
}

type UcResponseExtObject struct {
	Templateid   int    `json:"templateid"`
	Industry     int    `json:"industry"`
	DspIndustry1 string `json:"dsp_industry1"`
	DspIndustry2 string `json:"dsp_industry2"`
	DspIndustry3 string `json:"dsp_industry3"`
}

type UcResponseAdmObject struct {
	Imptrackers []string                  `json:"imptrackers"`
	Link        *UcResponseLinkObject     `json:"link"`
	Assets      []*UcResponseAssetsObject `json:"assets"`
}

type UcResponseAssetsObject struct {
	Id      int                        `json:"id"`
	Icon    string                     `json:"icon,omitempty"`
	Title   *UcResponseTitleObject     `json:"title,omitempty"`
	Img     *UcResponseImgObject       `json:"img,omitempty"`
	Video   *UcResponseVideoObject     `json:"video,omitempty"`
	Data    *UcResponseDataObject      `json:"data,omitempty"`
	AppInfo *UcResponseAppInfoObject   `json:"app_info,omitempty"`
	Ext     *UcResponseAssetsExtObject `json:"ext,omitempty"`
}

type UcResponseDataObject struct {
	Type  int    `json:"type"`
	Value string `json:"value"`
}

type UcResponseAppInfoObject struct {
	AppName      string `json:"app_name"`
	VersionName  string `json:"version_name"`
	Developer    string `json:"developer"`
	UpdateTime   string `json:"update_time"`
	Permission   string `json:"permission"`
	Privacy      string `json:"privacy"`
	FunctionDesc string `json:"function_desc"`
}

type UcResponseAssetsExtObject struct {
	OpMark      string `json:"op_mark"`
	Rank        string `json:"rank"`
	AccountName string `json:"account_name"`
}

type UcResponseTitleObject struct {
	Text string `json:"text"`
}

type UcResponseImgObject struct {
	W   int    `json:"w"`
	H   int    `json:"h"`
	Url string `json:"url"`
	T   string `json:"t"`
}

type UcResponseVideoObject struct {
	Duration int    `json:"Duration"`
	Size     int    `json:"Size"`
	Url      string `json:"url"`
}

type UcResponseLinkObject struct {
	Url           string                   `json:"url"`
	Clicktrackers []string                 `json:"clicktrackers"`
	Ext           *UcResponseLinkExtObject `json:"ext"`
}
type UcResponseLinkExtObject struct {
	Deeplinkurl   string `json:"deeplinkurl"`
	Downloadurl   string `json:"downloadurl"`
	UniversalLink string `json:"universal_link"`
}

// UcPipline
type UcPipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	CaidMulti []models.MHReqCAIDMulti

	Request  *UcRequestObject
	Response *UcResponseObject

	ConfigList    []*models.RtbConfigByTagIDStu
	ResultImp     []*UcRequestImpObject
	AdxAdResponse *models.MHResp
}
