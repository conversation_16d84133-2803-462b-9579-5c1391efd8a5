package rtb_uc

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *UcPipline) Init(c *gin.Context, channel string) *UcPipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()

	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject UcRequestObject
	err = json.Unmarshal(bodyContent, &requestObject)

	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()

	p.Context = c
	p.Channel = channel

	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCAID().
		SetupCarrier()
}

// CheckImp 检查Imp对象
func (p *UcPipline) CheckImp() *UcPipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *UcPipline) SetupOsString() *UcPipline {
	switch strings.ToLower(p.Request.Device.Os) {
	case "ios":
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	case "android":
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	default:
		// 错误信息
		return p.SetErrorString("invalid os")
	}

	return p
}

// SetupCAID 设置 CAID
func (p *UcPipline) SetupCAID() *UcPipline {
	var caidList []UcRequestDeviceCaidObject
	dataByte := []byte(p.Request.Device.Caid)
	_ = json.Unmarshal(dataByte, &caidList)
	var caidMultiList []models.MHReqCAIDMulti

	if len(caidList) > 0 {
		for _, caidItem := range caidList {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = caidItem.Caid
			caidMulti.CAIDVersion = caidItem.Version

			caidMultiList = append(caidMultiList, caidMulti)
		}
	}

	p.CaidMulti = caidMultiList

	return p
}

// CheckUA 检查UA
func (p *UcPipline) CheckUA() *UcPipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *UcPipline) SetupManufacturer() *UcPipline {
	// 每次需要判断状态
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Brand
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *UcPipline) SetupConnectType() *UcPipline {
	switch p.Request.Device.Connectiontype {
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	case 4:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_2G
	case 5:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_3G
	case 6:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	case 7:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_5G
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}

	return p
}

// SetupCarrier 设置运营商
func (p *UcPipline) SetupCarrier() *UcPipline {
	switch p.Request.Device.Carrier {
	case "ChinaMobile":
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case "ChinaUnicom":
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case "ChinaTelecom":
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}

	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *UcPipline) RequestRtbConfig() *UcPipline {
	var styleIds []string
	var resultImp []*UcRequestImpObject
	var configList []*models.RtbConfigByTagIDStu

	for _, imp := range p.Request.Imp {
		tagId := imp.Id
		price := 0

		if imp.CpmFloor > 0 {
			price = imp.CpmFloor
		}

		for _, templateId := range imp.Ext.Templateid {
			styleIds = append(styleIds, strconv.Itoa(templateId))
		}

		adxInfo := models.GetAdxInfoByRtbTagIDAndStyles(p.Context, p.Channel, tagId, p.DeviceOs.String(), styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		configData := (*adxInfo)[0]
		resultImp = append(resultImp, imp)
		configList = append(configList, &configData)
	}

	// 判断是否有效填充
	if len(configList) == 0 || len(resultImp) == 0 {
		return p.SetErrorString("resultImp or configList imp is empty")
	}

	p.ResultImp = resultImp
	p.ConfigList = configList

	return p
}

// RequestAdxAd 请求广告
func (p *UcPipline) RequestAdxAd() *UcPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	requestConfig := p.ConfigList[0]
	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID: requestConfig.LocalAppID,
			//AppBundleID: p.Request.App.Bundle, //  写死uc 包名，他们传的包名有问题
			AppName: p.Request.App.Name,
			AppType: 4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           p.DeviceOs.String(),
			OsVersion:    p.Request.Device.Osv,
			Model:        p.Request.Device.Model,
			Manufacturer: p.Manufacturer,
			Oaid:         p.Request.Device.Oaid,
			OaidMd5:      p.Request.Device.Oaidmd5,
			Idfa:         p.Request.Device.Idfa,
			Ua:           p.Request.Device.Ua,
			DeviceType:   1,
			IP:           p.Request.Device.Ip,
			CAIDMulti:    p.CaidMulti,
		},
		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		adxRequest.App.AppBundleID = "com.ucweb.iphone.lowversion"
		adxRequest.Device.IdfaMd5 = p.Request.Device.Didmd5
	} else {
		adxRequest.App.AppBundleID = "com.UCMobile"
		adxRequest.Device.ImeiMd5 = p.Request.Device.Didmd5
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func ConvertStrSlice2Map(sl []string) map[string]struct{} {
	set := make(map[string]struct{}, len(sl))
	for _, v := range sl {
		set[v] = struct{}{}
	}
	return set
}

func (p *UcPipline) SetupResponse() *UcPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	var impItem *UcRequestImpObject
	for _, imp := range p.ResultImp {
		if imp.Id == requestConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return p.SetErrorString("no fill")
	}

	var (
		responseObject            UcResponseObject
		responseSeatbidObject     UcResponseSeatbidObject
		responseBidObjectList     []*UcResponseBidObject
		responseSeatbidObjectList []*UcResponseSeatbidObject
	)

	for _, adxAd := range p.AdxAdResponse.Data[requestConfig.LocalPosID].List {
		isVideoAd := adxAd.Video != nil && len(adxAd.Video.VideoURL) > 0

		// 样式id
		allStyleID := requestConfig.AllImageStyleID
		if isVideoAd {
			allStyleID = requestConfig.AllVideoStyleID
		}

		// 统计
		models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if adxAd.Ecpm < requestConfig.Price {
			continue
		}
		ecpm := adxAd.Ecpm

		winNotice := config.ExternalRtbPriceURL +
			"?uid=" + p.UUID +
			"&tagid=" + requestConfig.TagID +
			"&bp=" + utils.ConvertIntToString(ecpm) +
			"&channel=" + p.Channel +
			"&price=${AUCTION_PRICE}" +
			"&log=" + url.QueryEscape(adxAd.Log)

		winNotice = strings.Replace(winNotice, "https", "http", -1)

		var adURL string
		var assetsList []*UcResponseAssetsObject
		var responseBidAdmLinkExtObject UcResponseLinkExtObject
		if adxAd.InteractType == 1 {
			if len(adxAd.MarketURL) > 0 {
				responseBidAdmLinkExtObject.Deeplinkurl = adxAd.MarketURL
				if p.DeviceOs == rtb.MH_RTB_OS_IOS {
					responseBidAdmLinkExtObject.UniversalLink = adxAd.MarketURL
				}
			} else {
				if len(adxAd.DeepLink) > 0 {
					responseBidAdmLinkExtObject.Deeplinkurl = adxAd.DeepLink
					if p.DeviceOs == rtb.MH_RTB_OS_IOS {
						responseBidAdmLinkExtObject.UniversalLink = adxAd.DeepLink
					}
				}
			}

			adURL = adxAd.DownloadURL
			responseBidAdmLinkExtObject.Downloadurl = adxAd.DownloadURL
		} else {
			if len(adxAd.DeepLink) > 0 {
				responseBidAdmLinkExtObject.Deeplinkurl = adxAd.DeepLink
				if p.DeviceOs == rtb.MH_RTB_OS_IOS {
					responseBidAdmLinkExtObject.UniversalLink = adxAd.DeepLink
				}
			} else {
				if len(adxAd.MarketURL) > 0 {
					responseBidAdmLinkExtObject.Deeplinkurl = adxAd.MarketURL
					if p.DeviceOs == rtb.MH_RTB_OS_IOS {
						responseBidAdmLinkExtObject.UniversalLink = adxAd.MarketURL
					}
				}
			}

			if len(adxAd.LandpageURL) > 0 {
				adURL = adxAd.LandpageURL
			} else {
				adURL = adxAd.AdURL
			}
		}

		var impTracking []string
		for _, impLink := range adxAd.ImpressionLink {
			impTracking = append(impTracking, strings.Replace(impLink, "https", "http", -1))
		}

		responseBidAdmLinkObject := UcResponseLinkObject{
			Url:           adURL,
			Clicktrackers: adxAd.ClickLink,
			Ext:           &responseBidAdmLinkExtObject,
		}

		assetsId := 0
		var assetsTitle UcResponseAssetsObject
		assetsId = assetsId + 1
		assetsTitle.Id = assetsId
		title := adxAd.Title + adxAd.Description
		if len(title)/3 < 15 {
			title = title + "更多发现，更多精彩，点击即刻获取"
		}
		assetsTitle.Title = &UcResponseTitleObject{
			Text: title,
		}
		assetsList = append(assetsList, &assetsTitle)

		if len(adxAd.IconURL) > 0 {
			var assetsIcon UcResponseAssetsObject
			assetsId = assetsId + 1
			assetsIcon.Id = assetsId
			assetsIcon.Icon = adxAd.IconURL
			assetsList = append(assetsList, &assetsIcon)
		}

		var assetsData UcResponseAssetsObject
		assetsId = assetsId + 1
		assetsData.Id = assetsId
		assetsData.Data = &UcResponseDataObject{
			Type:  1,
			Value: "MH",
		}
		assetsList = append(assetsList, &assetsData)

		var responseBidExtObject UcResponseExtObject

		allStyleIDArr := strings.Split(allStyleID, ",")
		allStyleIDMap := ConvertStrSlice2Map(allStyleIDArr)
		for _, templateId := range impItem.Ext.Templateid {
			_, ok := allStyleIDMap[strconv.Itoa(templateId)]
			if ok {
				responseBidExtObject.Templateid = templateId
				break
			}
		}
		if responseBidExtObject.Templateid == 0 {
			continue
		}
		switch adxAd.CrtType {
		case 11:
			var assetsImg UcResponseAssetsObject
			assetsId = assetsId + 1
			assetsImg.Id = assetsId
			assetsImg.Img = &UcResponseImgObject{
				W:   adxAd.Image[0].Width,
				H:   adxAd.Image[0].Height,
				Url: adxAd.Image[0].URL,
			}
			assetsList = append(assetsList, &assetsImg)
		case 20:
			var assetsImg UcResponseAssetsObject
			assetsId = assetsId + 1
			assetsImg.Id = assetsId
			assetsImg.Img = &UcResponseImgObject{
				W:   adxAd.Video.Width,
				H:   adxAd.Video.Height,
				Url: adxAd.Video.CoverURL,
			}
			assetsList = append(assetsList, &assetsImg)

			var assetsVideo UcResponseAssetsObject
			assetsId = assetsId + 1
			assetsVideo.Id = assetsId
			assetsVideo.Video = &UcResponseVideoObject{
				Duration: adxAd.Video.Duration / 1000,
				Size:     1000,
				Url:      adxAd.Video.VideoURL,
			}
			assetsList = append(assetsList, &assetsVideo)
		}
		dateOnly := time.Now().Format(time.DateOnly)
		timeArr := strings.Split(dateOnly, "-")
		timeStr := timeArr[0] + "年" + timeArr[1] + "月" + timeArr[2] + "日"

		if adxAd.InteractType == 1 {
			if len(adxAd.AppInfoURL) == 0 {
				continue
			}
			var assetsAppInfo UcResponseAssetsObject
			assetsId = assetsId + 1
			assetsAppInfo.Id = assetsId
			assetsAppInfo.AppInfo = &UcResponseAppInfoObject{
				AppName:      adxAd.AppName,
				VersionName:  adxAd.AppVersion,
				Developer:    adxAd.Publisher,
				Permission:   adxAd.Permission,
				Privacy:      adxAd.PrivacyLink,
				FunctionDesc: adxAd.AppInfoURL,
				UpdateTime:   timeStr,
			}
			assetsList = append(assetsList, &assetsAppInfo)
		}

		responseBidAdmObject := UcResponseAdmObject{
			Imptrackers: impTracking,
			Link:        &responseBidAdmLinkObject,
			Assets:      assetsList,
		}

		responseBidObject := UcResponseBidObject{
			Id:    uuid.NewV4().String(),
			Impid: impItem.Id,
			Nurl:  winNotice,
			Price: ecpm * 1000,
			Adid:  adxAd.Crid,
			Ext:   &responseBidExtObject,
			Adm:   &responseBidAdmObject,
		}

		responseBidObjectList = append(responseBidObjectList, &responseBidObject)
	}
	if len(responseBidObjectList) > 0 {
		responseSeatbidObject.Bid = responseBidObjectList
	}
	responseSeatbidObjectList = append(responseSeatbidObjectList, &responseSeatbidObject)

	// 构造返回结果
	responseObject.Id = p.Request.Id
	responseObject.Nbr = 0
	responseObject.Seatbid = responseSeatbidObjectList

	status := http.StatusOK
	if len(responseBidObjectList) == 0 {
		status = http.StatusNoContent
	}

	p.Response = &responseObject
	p.ResponseStatus = status

	return p
}

// ResponseResult 返回结果
func (p *UcPipline) ResponseResult() (*UcResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &UcResponseObject{
			Nbr:     0,
			Id:      p.Request.Id,
			Seatbid: nil,
		}, http.StatusNoContent
	}

	return p.Response, http.StatusOK
}

// SetErrorString 设置字符串类型的错误信息
func (p *UcPipline) SetErrorString(err string) *UcPipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *UcPipline) SetError(err error) *UcPipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}
