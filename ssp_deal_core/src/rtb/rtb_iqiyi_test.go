package rtb

import (
	"bytes"
	"fmt"
	"io"
	"mh_proxy/db"
	"mh_proxy/pb/iqiyi_down"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/proto"
)

func TestIqiyi(t *testing.T) {
	db.InitBigCache()
	db.InitRedis()

	id := "123"
	ua := "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1"
	os := "ios"
	osVersion := "14.4"
	adZoneId := int64(1234567890)
	adType := int32(1)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = &http.Request{
		URL: &url.URL{
			Scheme: "http",
			Host:   "localhost:8081",
			Path:   "/api/v1",
		},
		Header: make(http.Header),
		Method: "GET",
	}

	probufData, _ := proto.Marshal(&iqiyi_down.BidRequest{
		Id: &id,
		Device: &iqiyi_down.Device{
			Os:        &os,
			Ua:        &ua,
			OsVersion: &osVersion,
		},
		Imp: []*iqiyi_down.Impression{
			{
				Id: &id,
				Banner: &iqiyi_down.Banner{
					AdZoneId: &adZoneId,
				},
				Video: &iqiyi_down.Video{
					AdZoneId: &adZoneId,
					AdType:   &adType,
				},
			},
		},
	})

	c.Request.Body = io.NopCloser(bytes.NewBuffer(probufData))
	resp, status := HandleByIQiYi(c, "35")
	fmt.Println(resp, status)
}
