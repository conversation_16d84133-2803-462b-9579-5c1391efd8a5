package rtb

import (
	"bytes"
	"fmt"
	"io"
	"mh_proxy/db"
	"mh_proxy/pb/tanx_down"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/proto"
)

func GetTanxProtobuf() []byte {
	// 基本参数
	id := "123456"
	ua := "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1"
	os := int32(2) // 2 = iOS, 3 = Android
	osVersion := "14.4"
	make := "Apple"
	model := "iPhone"
	ip := "***********"
	var screenWidth int32 = 1080
	var screenHeight int32 = 1920
	var connectionType int32 = 2 // WiFi
	var carrier int32 = 1        // 移动

	// App信息
	packageName := "com.example.app"
	appName := "TestApp"

	// 创建模板
	var templateId int32 = 1380 // 图片广告模板ID

	// 广告位和价格
	impId := "imp_123456"
	tagId := "123456"
	var minCpmPrice int32 = 100

	// 构造测试请求
	probufData, _ := proto.Marshal(&tanx_down.BidRequest{
		Id:      &id,
		Version: proto.Int32(1),
		Device: &tanx_down.BidRequest_Device{
			Os:             &os,
			UserAgent:      &ua,
			Osv:            &osVersion,
			Make:           &make,
			Model:          &model,
			Ip:             &ip,
			ScreenWidth:    &screenWidth,
			ScreenHeight:   &screenHeight,
			ConnectionType: &connectionType,
			Carrier:        &carrier,
			// 添加其他可能需要的参数
			ImeiMd5:   proto.String("imei_md5_example"),
			AndroidId: proto.String("android_id_example"),
			Oaid:      proto.String("oaid_example"),
			Idfa:      proto.String("idfa_example"),
			IdfaMd5:   proto.String("idfa_md5_example"),
		},
		App: &tanx_down.BidRequest_App{
			PackageName: &packageName,
			Name:        &appName,
		},
		Imps: []*tanx_down.BidRequest_Imp{
			{
				Pid:         proto.String("123456"),
				ImpId:       &impId,
				TagId:       &tagId,
				MinCpmPrice: &minCpmPrice,
				Templates: []*tanx_down.BidRequest_Imp_Template{
					{
						Id: proto.Uint64(uint64(templateId)),
					},
				},
			},
		},
	})
	return probufData
}

func TestTanx(t *testing.T) {
	db.InitBigCache()
	db.InitRedis()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = &http.Request{
		URL: &url.URL{
			Scheme: "http",
			Host:   "localhost:8081",
			Path:   "/api/v1",
		},
		Header: make(http.Header),
		Method: "POST",
	}

	c.Request.Body = io.NopCloser(bytes.NewBuffer(GetTanxProtobuf()))
	rsp := HandleByTanxProtobuf(c, "21")
	fmt.Println(rsp)

}
