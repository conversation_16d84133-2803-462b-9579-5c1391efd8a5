package rtb_kuaishou

import (
	models2 "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/models"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/kuaishou"
	"mh_proxy/utilities"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
)

func HandleKuaishou(c *gin.Context, channel string) (*kuaishou.BidResponse, int) {
	bodyContent, _ := c.GetRawData()

	req := &kuaishou.BidRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_INVALID_REQUEST.Enum(),
		}, http.StatusNoContent
	}

	var deviceOs string
	switch req.GetDevice().GetOsType() {
	case kuaishou.Device_ANDROID:
		deviceOs = "android"
	case kuaishou.Device_IOS:
		deviceOs = "ios"
	default:
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNSUPPORTED_DEVICE.Enum(),
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetNetwork().GetConnectionType() {
	case kuaishou.Network_WIFI:
		connectType = 1
	case kuaishou.Network_CELL_2G:
		connectType = 2
	case kuaishou.Network_CELL_3G:
		connectType = 3
	case kuaishou.Network_CELL_4G:
		connectType = 4
	case kuaishou.Network_CELL_5G:
		connectType = 7
	case kuaishou.Network_CELL_UNKNOWN:
		connectType = 0
	default:
		connectType = 0
	}

	var carrier int
	switch req.GetNetwork().GetOperatorType() {
	case kuaishou.Network_CHINA_MOBILE:
		carrier = 1
	case kuaishou.Network_CHINA_UNICOM:
		carrier = 2
	case kuaishou.Network_CHINA_TELECOM:
		carrier = 3
	default:
		carrier = 0
	}

	var tagId string
	var adsCount int
	var keyword string
	var resultfulImp []*kuaishou.Imp
	var configList []models.RtbConfigByTagIDStu

	for _, imp := range req.GetImp() {
		price := 0
		adsCount = int(imp.GetAdsCount())

		if len(imp.GetBlockingKeyword()) > 0 {
			keyword = imp.GetBlockingKeyword()
		}

		tagId = imp.GetTagId()

		var styleIds []string
		if imp.GetCpmBidFloor() > 0 {
			price = int(imp.GetCpmBidFloor() * 100)
		}
		for _, creativeType := range imp.GetCreativeType() {
			styleIds = append(styleIds, creativeType.Enum().String())
		}

		adxInfo := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.GetTagId(), deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.GetApp().GetBundle()) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.GetApp().GetBundle() {
					configList = append(configList, infoItem)
				}
			}
		} else {
			if len(imp.GetMediumPackageName()) > 0 {
				for _, infoItem := range *adxInfo {
					if infoItem.PackageName == imp.GetMediumPackageName() {
						configList = append(configList, infoItem)
					}
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNSUPPORTED_DEVICE.Enum(),
		}, http.StatusNoContent
	}

	reqRtbConfig := configList[0]

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(req.GetDevice().GetUdid().GetCurrentCaid()) > 0 && len(req.GetDevice().GetUdid().GetCurrentCaidVersion()) > 0 {
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = req.GetDevice().GetUdid().GetCurrentCaid()
		caidMulti.CAIDVersion = req.GetDevice().GetUdid().GetCurrentCaidVersion()
		caidMultiList = append(caidMultiList, caidMulti)
	}
	if len(req.GetDevice().GetUdid().GetLastCaid()) > 0 && len(req.GetDevice().GetUdid().GetLastCaidVersion()) > 0 {
		var preCaidMulti models.MHReqCAIDMulti
		preCaidMulti.CAID = req.GetDevice().GetUdid().GetLastCaid()
		preCaidMulti.CAIDVersion = req.GetDevice().GetUdid().GetLastCaidVersion()
		caidMultiList = append(caidMultiList, preCaidMulti)
	}

	var model string
	var manufacturer string

	if len(req.GetDevice().GetDeviceModel()) > 0 {
		if deviceOs == "ios" {
			manufacturer = "Apple"
			model = req.GetDevice().GetDeviceModel()
		} else {
			makeArray := strings.Split(req.GetDevice().GetDeviceModel(), "(")
			if len(makeArray) > 0 && len(makeArray) == 2 {
				manufacturer = strings.Replace(makeArray[0], "(", "", -1)
				model = strings.Replace(makeArray[1], ")", "", -1)
			}
		}
	}

	osVersion := strconv.Itoa(int(req.GetDevice().GetOsVersion().GetMajor()))
	if deviceOs == "ios" {
		regex := regexp.MustCompile(`OS (\w+) like`)
		// 查找匹配的字符串
		match := regex.FindStringSubmatch(req.GetDevice().GetUserAgent())
		if len(match) > 1 {
			tmpOsVersion := match[1]
			osVersion = strings.Replace(tmpOsVersion, "_", ".", -1)
		}
	}

	var impItem *kuaishou.Imp
	for _, imp := range resultfulImp {
		if imp.GetTagId() == reqRtbConfig.OriginTagID {
			impItem = imp
			adsCount = int(imp.GetAdsCount())
			break
		}
	}

	if impItem == nil {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNMATCHED_USER.Enum(),
		}, http.StatusNoContent
	}

	if adsCount == 0 {
		adsCount = 1
	}

	// 准备请求数据
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetApp().GetBundle(),
			AppName:     req.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
			Query:       keyword,
		},
		Device: models.MHReqDevice{
			Os:           deviceOs,
			Manufacturer: manufacturer,
			Model:        model,
			IP:           req.GetNetwork().GetIpv4(),
			Ua:           req.GetDevice().GetUserAgent(),
			OsVersion:    osVersion,
			ImeiMd5:      req.GetDevice().GetUdid().GetImeiMd5(),
			Oaid:         req.GetDevice().GetUdid().GetOaid(),
			Idfa:         req.GetDevice().GetUdid().GetIdfa(),
			ScreenWidth:  int(req.GetDevice().GetScreenSize().GetWidth()),
			ScreenHeight: int(req.GetDevice().GetScreenSize().GetHeight()),
			DeviceType:   1,
			CAIDMulti:    caidMultiList,
			AppList:      getKsAppList(reqRtbConfig.LocalAppID, req.GetDevice().GetInstallApp(), deviceOs),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNMATCHED_USER.Enum(),
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNMATCHED_USER.Enum(),
		}, http.StatusNoContent
	}

	adChhnnelMap := make(map[string]string)

	var seatBids []*kuaishou.SeatBid

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		adChhnnelMap[mhDataItem.AdID] = mhDataItem.AdChannel

		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${WIN_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log) + "&price_type=" + mhDataItem.KsPriceType + "&click_rate=" + fmt.Sprintf("%f", mhDataItem.KsClickRate)
		if channel == "999" {
			winURL = strings.Replace(winURL, "ssp.maplehaze.cn", "ssp.adsuni.com", -1)
		}

		var trackingArray []*kuaishou.Tracking
		if len(mhDataItem.ImpressionLink) > 0 {
			var impressionLink []string
			for _, impLink := range mhDataItem.ImpressionLink {
				if channel == "999" {
					if strings.Contains(impLink, "ssp.maplehaze.cn") {
						impLink = strings.Replace(impLink, "ssp.maplehaze.cn", "ssp.adsuni.com", -1)
					}
				}
				impressionLink = append(impressionLink, impLink)
			}
			var impTrackingArray kuaishou.Tracking
			impTrackingArray.TrackingEvent = kuaishou.Tracking_AD_EXPOSURE.Enum()
			impTrackingArray.TrackingUrl = impressionLink
			trackingArray = append(trackingArray, &impTrackingArray)
		}

		if len(mhDataItem.ClickLink) > 0 {
			var clickLink []string
			for _, clkLink := range mhDataItem.ClickLink {
				if channel == "999" {
					if strings.Contains(clkLink, "ssp.maplehaze.cn") {
						clkLink = strings.Replace(clkLink, "ssp.maplehaze.cn", "ssp.adsuni.com", -1)
					}
				}
				clickLink = append(clickLink, clkLink)
			}
			var clkTrackingArray kuaishou.Tracking
			clkTrackingArray.TrackingEvent = kuaishou.Tracking_AD_CLICK.Enum()
			clkTrackingArray.TrackingUrl = clickLink
			trackingArray = append(trackingArray, &clkTrackingArray)
		}

		landpageURL := mhDataItem.LandpageURL
		if channel == "999" {
			if strings.Contains(mhDataItem.LandpageURL, "www.maplehaze.cn") {
				landpageURL = strings.Replace(mhDataItem.LandpageURL, "www.maplehaze.cn", "www.adsuni.com", -1)
			}
		}

		var bids []*kuaishou.Bid
		bid := kuaishou.Bid{
			BidId:      proto.String(uuid.NewV4().String()),
			ImpId:      proto.String(impItem.GetImpId()),
			Price:      proto.Float64(float64(ecpm) / float64(100)),
			AdId:       proto.String(mhDataItem.AdID),
			NoticeUrl:  proto.String(winURL),
			AdTracking: trackingArray,
			Adm: &kuaishou.Adm{
				Title:    proto.String(mhDataItem.Title),
				ClickUrl: proto.String(landpageURL),
				IconUrl:  proto.String(mhDataItem.IconURL),
			},
			CreativeId: proto.String(mhDataItem.Crid),
			BidType:    kuaishou.Bid_CPM.Enum(),
		}

		// 广告类型 默认CPM CPC需要把price进行转换
		if mhDataItem.KsPriceType == "cpc" {
			bid.BidType = kuaishou.Bid_CPC.Enum()
			cpcPrice := float64(ecpm) / (1000 * mhDataItem.KsClickRate / 100)
			bid.Price = proto.Float64(cpcPrice / float64(100))
		}

		if len(reqRtbConfig.AdType) > 0 {
			adType := kuaishou.AdTypeEnum_AdType(kuaishou.AdTypeEnum_AdType_value[reqRtbConfig.AdType]).Enum()
			bid.Adm.AdType = adType
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				deepLink := mhDataItem.DeepLink + "&backurl=__KSBACKURL__"
				bid.Adm.DeeplinkUrl = proto.String(deepLink)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					deepLink := mhDataItem.MarketURL + "&backurl=__KSBACKURL__"
					bid.Adm.DeeplinkUrl = proto.String(deepLink)
				}
			}

			bid.Adm.InteractionType = kuaishou.Adm_SURFING.Enum()
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				deepLink := mhDataItem.MarketURL + "&backurl=__KSBACKURL__"
				bid.Adm.DeeplinkUrl = proto.String(deepLink)
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					deepLink := mhDataItem.DeepLink + "&backurl=__KSBACKURL__"
					bid.Adm.DeeplinkUrl = proto.String(deepLink)
				}
			}

			bid.Adm.InteractionType = kuaishou.Adm_DOWNLOAD.Enum()

			losPos := models.GetLocalPosInfo(c, reqRtbConfig.LocalPosID, reqRtbConfig.LocalAppID)
			if losPos != nil {
				if losPos.LocalPosDownloadType == 1 {
					bid.Adm.MarketDirect = proto.Bool(true)
				}
			}
		}

		if len(mhDataItem.PackageName) > 0 {
			bid.Adm.PackageName = proto.String(mhDataItem.PackageName)
			if deviceOs == "ios" {
				bid.Adm.BundleId = proto.String(mhDataItem.PackageName)
			}
		}

		if mhDataItem.PackageSize > 0 {
			bid.Adm.PackageSize = proto.Uint32(uint32(mhDataItem.PackageSize))
			bid.Adm.PackageSizeV2 = proto.Uint64(uint64(mhDataItem.PackageSize))
		}

		if len(mhDataItem.AppName) > 0 {
			bid.Adm.AppName = proto.String(mhDataItem.AppName)
		}

		if len(mhDataItem.AppVersion) > 0 {
			bid.Adm.AppVersion = proto.String(mhDataItem.AppVersion)
		}

		if len(mhDataItem.Publisher) > 0 {
			if mhDataItem.Publisher == "枫岚互联" && channel == "999" {
				bid.Adm.DeveloperName = proto.String("时代亿联")
			} else {
				bid.Adm.DeveloperName = proto.String(mhDataItem.Publisher)
			}
		}

		if len(mhDataItem.PrivacyLink) > 0 {
			if mhDataItem.PrivacyLink == "https://static.maplehaze.cn/static/privacy_ad.html" && channel == "999" {
				bid.Adm.AppPrivacyUrl = proto.String("https://docs.qq.com/doc/p/9ba0f71b5346fc5412c7b1da712169e0622a9e1e")
			} else {
				bid.Adm.AppPrivacyUrl = proto.String(mhDataItem.PrivacyLink)
			}
		}

		if len(mhDataItem.PermissionURL) > 0 {
			bid.Adm.AppPermission = proto.String(mhDataItem.PermissionURL)
		}

		if len(mhDataItem.AppInfo) > 0 {
			bid.Adm.FunctionIntroduction = proto.String(mhDataItem.AppInfo)
		}

		switch mhDataItem.CrtType {
		case 11:
			if len(reqRtbConfig.ImageStyleID) == 0 || mhDataItem.Image == nil {
				continue
			}
			var imgArray []string
			var imgSize kuaishou.Size
			for _, imageItem := range mhDataItem.Image {
				imgArray = append(imgArray, imageItem.URL)
				imgSize.Width = proto.Uint32(uint32(imageItem.Width))
				imgSize.Height = proto.Uint32(uint32(imageItem.Height))
			}
			bid.Adm.PicUrls = imgArray
			bid.Size = &imgSize

			if len(imgArray) == 0 {
				continue
			}
			cridArray := []string{"1fb7a390c8af1f0269fc0354cd3f85ff", "97635680f6af3e28e86edceae02c9cb8"}
			creativeType := kuaishou.Adm_CreativeType(kuaishou.Adm_CreativeType_value[reqRtbConfig.ImageStyleID]).Enum()
			bid.Adm.CreativeType = creativeType
			if utilities.KsCridReplaceSwitch {
				bid.CreativeId = proto.String(cridArray[rand.Intn(2)])
			}
		case 20:
			if len(reqRtbConfig.VideoStyleID) == 0 || mhDataItem.Video == nil {
				continue
			}

			if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
				videoSize := &kuaishou.Size{
					Width:  proto.Uint32(uint32(mhDataItem.Video.Width)),
					Height: proto.Uint32(uint32(mhDataItem.Video.Height)),
				}
				bid.Adm.VideoUrl = proto.String(mhDataItem.Video.VideoURL)
				bid.Adm.VideoDuration = proto.Uint32(uint32(mhDataItem.Video.Duration) / uint32(1000))
				bid.Adm.CoverUrl = proto.String(mhDataItem.Video.CoverURL)
				bid.VideoSize = videoSize
				bid.Size = videoSize

				creativeType := kuaishou.Adm_CreativeType(kuaishou.Adm_CreativeType_value[reqRtbConfig.VideoStyleID]).Enum()
				bid.Adm.CreativeType = creativeType
				if utilities.KsCridReplaceSwitch {
					bid.CreativeId = proto.String("99a3c3239754e76cc609636fba02a377")
				}
			} else {
				continue
			}
		}

		bids = append(bids, &bid)
		seatBid := kuaishou.SeatBid{
			Bid: bids,
		}
		seatBids = append(seatBids, &seatBid)
	}

	if len(seatBids) == 0 {
		return &kuaishou.BidResponse{
			RequestId:   proto.String(req.GetRequestId()),
			Status:      proto.Uint32(3),
			NoBidReason: kuaishou.BidResponse_UNMATCHED_USER.Enum(),
		}, http.StatusNoContent
	}

	if channel == "999" {
		flag := 1
		marshal, _ := json.Marshal(seatBids)
		respStr := string(marshal)

		if strings.Contains(respStr, "maplehaze") {
			flag = 2
		}
		if strings.Contains(respStr, "枫岚") {
			flag = 2
		}

		if flag == 2 {
			for _, item := range seatBids {
				for _, bidItem := range item.GetBid() {
					val, ok := adChhnnelMap[bidItem.GetAdId()]
					if ok {
						if val == "99" {
							continue
						}
					}
					//  win url
					if strings.Contains(bidItem.GetNoticeUrl(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetNoticeUrl(), "枫岚") {
						flag = 3
						break
					}
					// 标题
					if strings.Contains(bidItem.GetAdm().GetTitle(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetTitle(), "枫岚") {
						flag = 3
						break
					}
					// package_name
					if strings.Contains(bidItem.GetAdm().GetPackageName(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetPackageName(), "枫岚") {
						flag = 3
						break
					}
					// app_version
					if strings.Contains(bidItem.GetAdm().GetAppVersion(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetAppVersion(), "枫岚") {
						flag = 3
						break
					}
					// developer_name
					if strings.Contains(bidItem.GetAdm().GetDeveloperName(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetDeveloperName(), "枫岚") {
						flag = 3
						break
					}
					// pic
					for _, pic := range bidItem.GetAdm().GetPicUrls() {
						if strings.Contains(pic, "maplehaze") {
							flag = 3
							break
						}
						if strings.Contains(pic, "枫岚") {
							flag = 3
							break
						}
					}

					// video
					if strings.Contains(bidItem.GetAdm().GetVideoUrl(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetVideoUrl(), "枫岚") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetCoverUrl(), "maplehaze") {
						flag = 3
						break
					}
					if strings.Contains(bidItem.GetAdm().GetCoverUrl(), "枫岚") {
						flag = 3
						break
					}
					// ad_tracking
					for _, adTrackItem := range bidItem.GetAdTracking() {
						for _, adTrackUrlItem := range adTrackItem.GetTrackingUrl() {
							if strings.Contains(adTrackUrlItem, "dsp.maplehaze.cn") {
								continue
							}
							if strings.Contains(adTrackUrlItem, "maplehaze") {
								flag = 3
								break
							}
							if strings.Contains(adTrackUrlItem, "枫岚") {
								flag = 3
								break
							}
						}
					}

					if flag == 3 {
						break
					}

					// h5
					if deviceOs == "ios" && strings.Contains(bidItem.GetAdm().GetClickUrl(), "net.maplehaze") {
						continue
					} else {
						if strings.Contains(bidItem.GetAdm().GetClickUrl(), "maplehaze") {
							flag = 3
							break
						}
						if strings.Contains(bidItem.GetAdm().GetClickUrl(), "枫岚") {
							flag = 3
							break
						}
					}

					// deeplink
					if strings.Contains(bidItem.GetAdm().GetDeeplinkUrl(), "com.maplehaze") || strings.Contains(bidItem.GetAdm().GetDeeplinkUrl(), "net.maplehaze") {
						continue
					} else {
						if strings.Contains(bidItem.GetAdm().GetDeeplinkUrl(), "maplehaze") {
							flag = 3
							break
						}
						if strings.Contains(bidItem.GetAdm().GetDeeplinkUrl(), "枫岚") {
							flag = 3
							break
						}
					}
				}
			}

			if flag == 3 {
				return &kuaishou.BidResponse{
					RequestId:   proto.String(req.GetRequestId()),
					Status:      proto.Uint32(3),
					NoBidReason: kuaishou.BidResponse_UNMATCHED_USER.Enum(),
				}, http.StatusNoContent
			}
		}
	}

	resp := &kuaishou.BidResponse{
		RequestId: proto.String(req.GetRequestId()),
		SeatBid:   seatBids,
		Status:    proto.Uint32(0),
		BidId:     proto.String(bigdataUID),
	}

	if channel == "998" {
		if tagId == "600009" || tagId == "600029" {
			marshal, _ := json.Marshal(resp)
			go models.BigDataHoloDebugJson2(uuid.NewV4().String()+"&ks_resp_"+tagId, string(marshal), "11", "22", "33", "44")
		}
	}

	return resp, http.StatusOK
}

func getKsAppList(appId string, appList []string, deviceOs string) []int {
	if len(appList) == 0 {
		return []int{}
	}
	var appListIdArray []int

	if deviceOs == "android" {
		appListMap := models2.GetMHAppListMap()
		for _, packageName := range appList {
			if v, ok := appListMap[packageName]; ok {
				appListIdArray = append(appListIdArray, v)
			}
		}
	} else {
		appListMap := models2.GetAppListMapGroup1()
		for _, appName := range appList {
			for key, item := range appListMap {
				if appName == "夸克" {
					appName = "夸克浏览器"
				}
				if appName == item.AppName {
					appListIdArray = append(appListIdArray, key)
					break
				}
			}
		}
	}

	return appListIdArray
}
