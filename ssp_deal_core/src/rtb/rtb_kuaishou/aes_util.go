package rtb_k<PERSON><PERSON>ou

import (
	"crypto/aes"
	"encoding/base64"
	"errors"
	"strings"
)

func Encrypt(data, key []byte) string {
	block, _ := aes.NewCipher(key)
	data = PKCS7Padding(data, block.BlockSize())
	encrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(encrypted[bs:be], data[bs:be])
	}

	return string(encrypted)
}

func Decrypt(data, key []byte) string {
	block, _ := aes.NewCipher(key)
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	origData, err := PKCS7UnPadding(decrypted)
	if err != nil {
		return ""
	}
	return string(origData)
}

// PKCS7Padding 用于填充数据至块大小的整数倍
func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

func PKCS7UnPadding(origData []byte) ([]byte, error) {
	//获取数据长度
	length := len(origData)
	if length == 0 {
		return nil, errors.New("加密字符串错误！")
	} else {
		unPadding := int(origData[length-1])
		return origData[:(length - unPadding)], nil
	}
}

func Base64URLDecode(data string) ([]byte, error) {
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing)
	return base64.StdEncoding.DecodeString(data)
}

func Base64URLEncode(data string) string {
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing)
	return base64.StdEncoding.EncodeToString([]byte(data))
}
