package rtb_kuaishou

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/pb/kuaishou"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"testing"

	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func TestKuaishou999(t *testing.T) {
	var imps []*kuaishou.Imp
	var creativeType []kuaishou.Adm_CreativeType
	creativeType = append(creativeType, kuaishou.Adm_HORIZONTAL_SCREEN, kuaishou.Adm_VIDEO, kuaishou.Adm_VERTICAL_SCREEN)
	imp := kuaishou.Imp{
		ImpId:        proto.String(uuid.NewV4().String()),
		TagId:        proto.String("3543025"),
		AdsCount:     proto.Uint32(1),
		CpmBidFloor:  proto.Float64(0.1),
		CreativeType: creativeType,
	}
	imps = append(imps, &imp)

	request := &kuaishou.BidRequest{
		RequestId: proto.String(uuid.NewV4().String()),
		Imp:       imps,
		App: &kuaishou.App{
			AppId:  proto.String("kuaishou_android"),
			Bundle: proto.String("com.smile.gifmaker"),
		},
		Device: &kuaishou.Device{
			UserAgent:  proto.String("Mozilla/5.0(Linux;Android5.1.1;OPPOR9PlusmABuild/LMY47V;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/55.0.2883.91MobileSafari/537.36"),
			Ip:         proto.String("**************"),
			DeviceType: kuaishou.Device_PHONE.Enum(),
			OsType:     kuaishou.Device_ANDROID.Enum(),
			OsVersion: &kuaishou.Version{
				Major: proto.Uint32(8),
			},
			ScreenSize: &kuaishou.Size{
				Width:  proto.Uint32(1080),
				Height: proto.Uint32(1920),
			},
			Udid: &kuaishou.Udid{
				//ImeiMd5: proto.String("9957b554fdacdfa11dd8e6e71bd11eb6"),
				Oaid: proto.String("95951F3AEF1346999FC51E20528A00D9d1b3310ac1ae9f82554eae62197eb75d"),
			},
			DeviceModel: proto.String("OPPO(R9Plus)"),
		},
		Network: &kuaishou.Network{
			Ipv4:           proto.String("***************"),
			ConnectionType: kuaishou.Network_WIFI.Enum(),
			OperatorType:   kuaishou.Network_CHINA_UNICOM.Enum(),
		},
	}

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/request?channel=998", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	ksResp := &kuaishou.BidResponse{}
	err = proto.Unmarshal(bodyContent, ksResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(ksResp)
	fmt.Println(string(marshal))
}

func TestHandleKuaishou(t *testing.T) {
	var imps []*kuaishou.Imp
	var creativeType []kuaishou.Adm_CreativeType
	creativeType = append(creativeType, kuaishou.Adm_HORIZONTAL_SCREEN, kuaishou.Adm_VIDEO, kuaishou.Adm_VERTICAL_SCREEN)
	imp := kuaishou.Imp{
		ImpId:    proto.String(uuid.NewV4().String()),
		TagId:    proto.String("3543025"),
		AdsCount: proto.Uint32(1),
		//CpmBidFloor:  proto.Float64(0.1),
		CreativeType: creativeType,
	}
	imps = append(imps, &imp)

	request := &kuaishou.BidRequest{
		RequestId: proto.String(uuid.NewV4().String()),
		Imp:       imps,
		App: &kuaishou.App{
			AppId: proto.String("kuaishou_android"),
			//Bundle: proto.String("com.smile.gifmaker"),
		},
		Device: &kuaishou.Device{
			UserAgent:  proto.String("Mozilla/5.0(Linux;Android5.1.1;OPPOR9PlusmABuild/LMY47V;wv)AppleWebKit/537.36(KHTML,likeGecko)Version/4.0Chrome/55.0.2883.91MobileSafari/537.36"),
			Ip:         proto.String("**************"),
			DeviceType: kuaishou.Device_PHONE.Enum(),
			OsType:     kuaishou.Device_ANDROID.Enum(),
			OsVersion: &kuaishou.Version{
				Major: proto.Uint32(8),
			},
			ScreenSize: &kuaishou.Size{
				Width:  proto.Uint32(1080),
				Height: proto.Uint32(1920),
			},
			Udid: &kuaishou.Udid{
				//ImeiMd5: proto.String("9957b554fdacdfa11dd8e6e71bd11eb6"),
				Oaid: proto.String("95951F3AEF1346999FC51E20528A00D9d1b3310ac1ae9f82554eae62197eb75d"),
			},
			DeviceModel: proto.String("OPPO(R9Plus)"),
		},
		Network: &kuaishou.Network{
			Ipv4:           proto.String("***************"),
			ConnectionType: kuaishou.Network_WIFI.Enum(),
			OperatorType:   kuaishou.Network_CHINA_UNICOM.Enum(),
		},
	}

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/request?channel=998", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	ksResp := &kuaishou.BidResponse{}
	err = proto.Unmarshal(bodyContent, ksResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(ksResp)
	fmt.Println(string(marshal))
}

func TestHandleType(t *testing.T) {
	price := "iCU%2BfPUfNQG3xM6dLrorDw%3D%3D"
	key := "530D69B3678AFA8E"
	clickRate := "39.000000"

	decodeStr, _ := url.QueryUnescape(price)
	fmt.Println(decodeStr)
	crypted, _ := Base64URLDecode(decodeStr)
	decodePrice := Decrypt(crypted, []byte(key))
	fmt.Println(decodePrice)
	winPrice := utils.ConvertStringToInt(decodePrice)

	fmt.Println(winPrice)

	rate, _ := strconv.ParseFloat(clickRate, 64)
	cpcPrice, _ := strconv.ParseFloat(decodePrice, 64)
	winPrice = int(cpcPrice * (1000 * rate / 100))

	fmt.Println(winPrice)
}
