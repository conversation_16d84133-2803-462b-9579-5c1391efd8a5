package rtb_meetyou

import (
	"encoding/base64"
	"strconv"
	"strings"

	"github.com/forgoer/openssl"
)

func Decrypt(price string, key string) (float32, error) {
	bt, err := Base64SafeDecode(price)
	if err != nil {
		return 0, err
	}
	deRes, err := openssl.AesECBDecrypt(bt, []byte(key), "PKCS5")
	if err != nil {
		return 0, err
	}
	tempPrice, err := strconv.ParseFloat(string(deRes), 64)
	decryptPrice := float32(tempPrice)
	return decryptPrice, nil
}

func Base64SafeDecode(data string) ([]byte, error) {
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing)
	res, err := base64.URLEncoding.DecodeString(data)
	return res, err
}
