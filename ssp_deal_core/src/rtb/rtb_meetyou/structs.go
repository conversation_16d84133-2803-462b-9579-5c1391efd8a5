package rtb_meetyou

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// MeetyouRequestObject Objects
type MeetyouRequestObject struct {
	Id      string                       `json:"id"`
	Device  *MeetyouRequestDeviceObject  `json:"device"`
	Imp     *MeetyouRequestImpObject     `json:"pos"`
	Network *MeetyouRequestNetworkObject `json:"network"`
	App     *MeetyouRequestAppObject     `json:"media"`
	AppUser *MeetyouRequestAppUserObject `json:"app_user,omitempty"`
}

type MeetyouRequestDeviceObject struct {
	Os                 int    `json:"platform"`
	ScreenWidth        int    `json:"device_width"`
	ScreenHeight       int    `json:"device_height"`
	AppStoreVersion    int    `json:"app_store_version"`
	OsVersion          string `json:"os_version"`
	Brand              string `json:"osbrand"`
	Model              string `json:"osmodel"`
	Ua                 string `json:"ua"`
	Idfa               string `json:"idfa"`
	IdfaMd5            string `json:"idfa_md5"`
	Imei               string `json:"imei"`
	ImeiMd5            string `json:"imei_md5"`
	AndroidId          string `json:"android_id"`
	AndroidIdMd5       string `json:"android_id_md5"`
	Oaid               string `json:"oaid"`
	Mac                string `json:"mac"`
	MacMd5             string `json:"mac_md5"`
	Ip                 string `json:"ip"`
	BootMark           string `json:"boot_mark"`
	UpdateMark         string `json:"update_mark"`
	DeviceStartSec     string `json:"device_start_sec"`
	Country            string `json:"country"`
	Language           string `json:"language"`
	HardwareMachine    string `json:"hardware_machine"`
	HardwareModel      string `json:"hardware_model"`
	PhysicalMemoryByte string `json:"physical_memory_byte"`
	HarddiskSizeByte   string `json:"harddisk_size_byte"`
	SystemUpdateSec    string `json:"system_update_sec"`
	TimeZone           string `json:"time_zone"`
	HmsVersion         string `json:"hms_version"`
	BirthTime          string `json:"birth_time"`
	CaidVersion        string `json:"caid_version"`
	Caid               string `json:"caid"`
	PreCaidVersion     string `json:"pre_caid_version"`
	PreCaid            string `json:"pre_caid"`
}

type MeetyouRequestNetworkObject struct {
	Carrier     int `json:"carrier"`
	ConnectType int `json:"connect_type"`
}

type MeetyouRequestAppObject struct {
	Name    string `json:"app_name"`
	Bundle  string `json:"app_bundle_id"`
	Version string `json:"app_version"`
}

type MeetyouRequestAppUserObject struct {
	AppInstallList []string `json:"app_install_list,omitempty"`
}

type MeetyouRequestImpObject struct {
	AdId            string              `json:"ad_id"`
	Count           int                 `json:"count"`
	BidType         int                 `json:"bid_type"`
	LimitPrice      float64             `json:"limit_price"`
	VideoLimitPrice float64             `json:"video_limit_price"`
	ImageSize       map[string][]string `json:"image_size"`
}

// MeetyouResponseObject Objects
type MeetyouResponseObject struct {
	Code    int                        `json:"ret"`
	Message string                     `json:"message"`
	Id      string                     `json:"id"`
	Data    *MeetyouResponseDataObject `json:"data"`
}

type MeetyouResponseDataObject struct {
	AdId string                      `json:"ad_id"`
	Bid  []*MeetyouResponseAdmObject `json:"items"`
}

type MeetyouResponseAdmObject struct {
	Title        string                         `json:"title"`
	ImgUrl       string                         `json:"img_url,omitempty"`
	Icon         string                         `json:"icon,omitempty"`
	Nurl         string                         `json:"bid_success_url,omitempty"`
	Desc         string                         `json:"description,omitempty"`
	ClickUrl     string                         `json:"click_url,omitempty"`
	DeepLink     string                         `json:"deep_link,omitempty"`
	MaterialType int                            `json:"material_type"`
	ImgWidth     int                            `json:"img_width,omitempty"`
	ImgHeight    int                            `json:"img_height,omitempty"`
	InteractType int                            `json:"interact_type,omitempty"`
	DeepLinkType int                            `json:"deep_link_type,omitempty"`
	Price        float64                        `json:"bid_price,omitempty"`
	Video        *MeetyouResponseVideoObject    `json:"video,omitempty"`
	AppInfo      *MeetyouResponseAppInfoObject  `json:"apk_info,omitempty"`
	Tracking     *MeetyouResponseTrackingObject `json:"tracking,omitempty"`
}

type MeetyouResponseAppInfoObject struct {
	DownLoadUrl string                                    `json:"url"`
	VersionCode string                                    `json:"version_code"`
	Version     string                                    `json:"version_name"`
	PkgName     string                                    `json:"package_name"`
	AppName     string                                    `json:"app_name"`
	AppIcon     string                                    `json:"app_icon"`
	Developer   string                                    `json:"develop_name"`
	Privacy     string                                    `json:"private_policy"`
	Permission  []*MeetyouResponseAppInfoPermissionObject `json:"app_permissions"`
}

type MeetyouResponseTrackingObject struct {
	ShowPing         []string `json:"show_ping"`
	ClickPing        []string `json:"click_ping"`
	OverDeepLinkPing []string `json:"over_deep_link_ping,omitempty"`
}

type MeetyouResponseAppInfoPermissionObject struct {
	Title   string `json:"title"`
	Content string `json:"content"`
}

type MeetyouResponseVideoObject struct {
	Duration int    `json:"video_duration"`
	Size     int    `json:"video_file_size"`
	Width    int    `json:"video_width"`
	Height   int    `json:"video_height"`
	VideoUrl string `json:"video_url"`
}

type MeetyouPipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	CaidMulti []models.MHReqCAIDMulti

	Request  *MeetyouRequestObject
	Response *MeetyouResponseObject

	ConfigList    []*models.RtbConfigByTagIDStu
	ResultImp     []*MeetyouRequestImpObject
	AdxAdResponse *models.MHResp
}
