package rtb_meetyou

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

func (p *MeetyouPipline) Init(c *gin.Context, channel string) *MeetyouPipline {
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_EMPTY
	p.ResponseStatus = http.StatusNoContent

	bodyContent, err := c.GetRawData()
	if err != nil {
		p.SetError(err)
		return p
	}

	var requestObject MeetyouRequestObject
	err = json.Unmarshal(bodyContent, &requestObject)

	if err != nil {
		p.SetError(err)
		return p
	}

	p.UUID = uuid.NewV4().String()

	p.Context = c
	p.Channel = channel

	p.Status = rtb.MH_RTB_PIPLINE_STATUS_RUNNING
	p.Request = &requestObject

	return p.
		CheckImp().
		CheckUA().
		SetupOsString().
		SetupManufacturer().
		SetupConnectType().
		SetupCarrier().
		SetupCAID()
}

// CheckImp 检查Imp对象
func (p *MeetyouPipline) CheckImp() *MeetyouPipline {
	// 检查
	if p.Request.Imp != nil {
		return p
	}

	// 错误信息
	return p.SetErrorString("imp is empty")
}

// SetupOsString 设置系统类型
func (p *MeetyouPipline) SetupOsString() *MeetyouPipline {
	switch p.Request.Device.Os {
	case 2:
		p.DeviceOs = rtb.MH_RTB_OS_IOS
	case 3:
		p.DeviceOs = rtb.MH_RTB_OS_ANDROID
	default:
		// 错误信息
		return p.SetErrorString("invalid os")
	}

	return p
}

// CheckUA 检查UA
func (p *MeetyouPipline) CheckUA() *MeetyouPipline {
	// 检查
	if len(p.Request.Device.Ua) != 0 {
		return p
	}

	// 错误信息
	return p.SetErrorString("invalid ua")
}

// SetupManufacturer 设置制造商
func (p *MeetyouPipline) SetupManufacturer() *MeetyouPipline {
	// 每次需要判断状态
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	if p.DeviceOs == rtb.MH_RTB_OS_IOS {
		p.Manufacturer = rtb.MH_MANUFACTURER_APPLE
	} else {
		p.Manufacturer = p.Request.Device.Brand
	}

	return p
}

// SetupConnectType 设置网络类型
func (p *MeetyouPipline) SetupConnectType() *MeetyouPipline {
	switch p.Request.Network.ConnectType {
	case 1:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_WIFI
	case 2:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_2G
	case 3:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_3G
	case 4:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_4G
	case 5:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_5G
	default:
		p.ConnectType = rtb.MH_RTB_CONNECTTYPE_UNKNOWN
	}

	return p
}

// SetupCarrier 设置运营商
func (p *MeetyouPipline) SetupCarrier() *MeetyouPipline {
	switch p.Request.Network.Carrier {
	case 1:
		p.Carrier = rtb.MH_RTB_CARRIER_CM
	case 2:
		p.Carrier = rtb.MH_RTB_CARRIER_CU
	case 3:
		p.Carrier = rtb.MH_RTB_CARRIER_CT
	default:
		p.Carrier = rtb.MH_RTB_CARRIER_UNKNOWN
	}

	return p
}

// SetupCAID 设置 CAID
func (p *MeetyouPipline) SetupCAID() *MeetyouPipline {
	var caidMultiList []models.MHReqCAIDMulti

	if len(p.Request.Device.PreCaid) > 0 && len(p.Request.Device.PreCaidVersion) > 0 {
		var preCaidMulti models.MHReqCAIDMulti
		preCaidMulti.CAID = p.Request.Device.PreCaid
		preCaidMulti.CAIDVersion = p.Request.Device.PreCaidVersion
		caidMultiList = append(caidMultiList, preCaidMulti)
	}

	if len(p.Request.Device.Caid) > 0 && len(p.Request.Device.CaidVersion) > 0 {
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = p.Request.Device.Caid
		caidMulti.CAIDVersion = p.Request.Device.CaidVersion
		caidMultiList = append(caidMultiList, caidMulti)
	}

	p.CaidMulti = caidMultiList
	return p
}

// RequestRtbConfig 请求服务端配置的广告价格和信息
func (p *MeetyouPipline) RequestRtbConfig() *MeetyouPipline {
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}

	imp := p.Request.Imp
	price := 0
	tagId := imp.AdId
	var styleIds []string

	if imp.LimitPrice > 0 || imp.VideoLimitPrice > 0 {
		if imp.LimitPrice > imp.VideoLimitPrice {
			price = int(imp.LimitPrice)
		} else {
			price = int(imp.VideoLimitPrice)
		}
	}

	if len(imp.ImageSize) > 0 {
		for k := range imp.ImageSize {
			styleIds = append(styleIds, k)
		}
	}

	if price > 0 {
		price = price * 100
	}

	var adxInfo *[]models.RtbConfigByTagIDStu
	if len(styleIds) == 0 {
		adxInfo = models.GetAdxInfoByRtbTagID(p.Context, p.Channel, tagId, p.DeviceOs.String(), "", "", price)
	} else {
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(p.Context, p.Channel, tagId, p.DeviceOs.String(), styleIds, "", price)
	}

	if adxInfo == nil || len(*adxInfo) == 0 {
		return p.SetErrorString("adxInfo is empty")
	}

	var resultImp []*MeetyouRequestImpObject
	var configList []*models.RtbConfigByTagIDStu

	configData := (*adxInfo)[0]
	resultImp = append(resultImp, imp)
	configList = append(configList, &configData)

	// 判断是否有效填充
	if len(configList) == 0 || len(resultImp) == 0 {
		return p.SetErrorString("resultImp or configList imp is empty")
	}

	p.ResultImp = resultImp
	p.ConfigList = configList

	return p
}

// RequestAdxAd 请求广告
func (p *MeetyouPipline) RequestAdxAd() *MeetyouPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]
	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: p.Request.App.Bundle,
			AppName:     p.Request.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 p.DeviceOs.String(),
			Imei:               p.Request.Device.Imei,
			ImeiMd5:            p.Request.Device.ImeiMd5,
			OsVersion:          p.Request.Device.OsVersion,
			Model:              p.Request.Device.Model,
			Manufacturer:       p.Manufacturer,
			Oaid:               p.Request.Device.Oaid,
			Idfa:               p.Request.Device.Idfa,
			IdfaMd5:            p.Request.Device.IdfaMd5,
			Ua:                 p.Request.Device.Ua,
			AndroidID:          p.Request.Device.AndroidId,
			AndroidIDMd5:       p.Request.Device.AndroidIdMd5,
			Mac:                p.Request.Device.Mac,
			DeviceType:         1,
			IP:                 p.Request.Device.Ip,
			ScreenWidth:        p.Request.Device.ScreenWidth,
			ScreenHeight:       p.Request.Device.ScreenHeight,
			BootMark:           p.Request.Device.BootMark,
			UpdateMark:         p.Request.Device.UpdateMark,
			DeviceStartSec:     p.Request.Device.DeviceStartSec,
			Country:            p.Request.Device.Country,
			Language:           p.Request.Device.Language,
			HardwareMachine:    p.Request.Device.HardwareMachine,
			HardwareModel:      p.Request.Device.HardwareModel,
			PhysicalMemoryByte: p.Request.Device.PhysicalMemoryByte,
			HarddiskSizeByte:   p.Request.Device.HarddiskSizeByte,
			SystemUpdateSec:    p.Request.Device.SystemUpdateSec,
			TimeZone:           p.Request.Device.TimeZone,
			HMSCoreVersion:     p.Request.Device.HmsVersion,
			AppStoreVersion:    strconv.Itoa(p.Request.Device.AppStoreVersion),
			DeviceBirthSec:     p.Request.Device.BirthTime,
			CAIDMulti:          p.CaidMulti,
		},
		Network: models.MHReqNetwork{
			ConnectType: int(p.ConnectType),
			Carrier:     int(p.Carrier),
		},
	}

	if p.Request.AppUser != nil {
		adxRequest.Device.AppList = getAppList(p.Request.AppUser.AppInstallList)
	}

	mhResp := core.GetADFromAdxWithContext(p.Context, &adxRequest, p.UUID)

	p.AdxAdResponse = mhResp

	if mhResp.Ret != 0 {
		return p.SetErrorString(fmt.Sprintf("no fill, ret: %d", mhResp.Ret))
	} else if len(mhResp.Data[requestConfig.LocalPosID].List) == 0 {
		return p.SetErrorString("no fill, ad list is empty")
	}

	return p
}

func (p *MeetyouPipline) SetupResponse() *MeetyouPipline {
	// 目前仅需取第一个配置
	if p.Status != rtb.MH_RTB_PIPLINE_STATUS_RUNNING {
		return p
	}
	requestConfig := p.ConfigList[0]

	var (
		responseObject        MeetyouResponseObject
		responseDataObject    MeetyouResponseDataObject
		responseBidObjectList []*MeetyouResponseAdmObject
	)

	for _, adxAd := range p.AdxAdResponse.Data[requestConfig.LocalPosID].List {
		ecpm := 0
		// 判定是否上游bidding
		if adxAd.Ecpm > 0 {
			// 统计
			models.BigDataRtbEcpmToHolo(p.Context, p.UUID, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

			// 判定上游返回ecpm是否大于底价
			if adxAd.Ecpm < requestConfig.Price {
				continue
			}
			ecpm = adxAd.Ecpm
		}

		winNotice := config.ExternalRtbPriceURL +
			"?uid=" + p.UUID +
			"&tagid=" + requestConfig.TagID +
			"&bp=" + utils.ConvertIntToString(ecpm) +
			"&channel=" + p.Channel +
			"&price=__WIN_SIGN_PRICE__" +
			"&log=" + url.QueryEscape(adxAd.Log)

		var (
			responseAppInfoObject  MeetyouResponseAppInfoObject
			responseTrackingObject MeetyouResponseTrackingObject
		)

		responseBidAdmObject := &MeetyouResponseAdmObject{
			Title:    adxAd.Title,
			Icon:     adxAd.IconURL,
			Nurl:     winNotice,
			Desc:     adxAd.Description,
			DeepLink: adxAd.DeepLink,
			Price:    float64(ecpm) / float64(100),
		}

		if adxAd.InteractType == 1 {
			if len(adxAd.MarketURL) > 0 {
				responseBidAdmObject.DeepLink = adxAd.MarketURL
			} else {
				if len(adxAd.DeepLink) > 0 {
					responseBidAdmObject.DeepLink = adxAd.DeepLink
				}
			}

			var permission MeetyouResponseAppInfoPermissionObject
			var permissionList []*MeetyouResponseAppInfoPermissionObject
			permission.Title = "权限说明"
			permission.Content = adxAd.Permission
			permissionList = append(permissionList, &permission)
			responseAppInfoObject.DownLoadUrl = adxAd.DownloadURL
			responseAppInfoObject.PkgName = adxAd.PackageName
			responseAppInfoObject.AppName = adxAd.AppName
			responseAppInfoObject.Developer = adxAd.Publisher
			responseAppInfoObject.Privacy = adxAd.PrivacyLink
			responseAppInfoObject.Version = adxAd.AppVersion
			responseAppInfoObject.VersionCode = adxAd.AppVersion
			responseAppInfoObject.AppIcon = adxAd.IconURL
			responseAppInfoObject.Permission = permissionList

			responseBidAdmObject.AppInfo = &responseAppInfoObject
			responseBidAdmObject.InteractType = 3
		} else {
			if len(adxAd.DeepLink) > 0 {
				responseBidAdmObject.DeepLink = adxAd.DeepLink
			} else {
				if len(adxAd.MarketURL) > 0 {
					responseBidAdmObject.DeepLink = adxAd.MarketURL
				}
			}

			if len(adxAd.LandpageURL) > 0 {
				responseBidAdmObject.ClickUrl = adxAd.LandpageURL
			} else {
				responseBidAdmObject.ClickUrl = adxAd.AdURL
			}
			responseBidAdmObject.InteractType = 1
		}

		if len(adxAd.DeepLink) > 0 || len(adxAd.MarketURL) > 0 {
			if p.DeviceOs.String() == "ios" {
				responseBidAdmObject.DeepLinkType = 2
			} else {
				responseBidAdmObject.DeepLinkType = 1
			}
			responseBidAdmObject.InteractType = 2
		}

		responseTrackingObject.ClickPing = adxAd.ClickLink
		responseTrackingObject.ShowPing = adxAd.ImpressionLink
		if len(adxAd.ConvTracks) > 0 {
			for _, convTracks := range adxAd.ConvTracks {
				if convTracks.ConvType == 10 {
					responseTrackingObject.OverDeepLinkPing = convTracks.ConvURLS
				}
			}
		}
		responseBidAdmObject.Tracking = &responseTrackingObject

		switch adxAd.CrtType {
		case 11:
			responseBidAdmObject.ImgUrl = adxAd.Image[0].URL
			responseBidAdmObject.ImgWidth = adxAd.Image[0].Width
			responseBidAdmObject.ImgHeight = adxAd.Image[0].Height
		case 20:
			responseBidAdmObject.ImgUrl = adxAd.Video.CoverURL
			responseBidAdmObject.ImgWidth = adxAd.Video.Width
			responseBidAdmObject.ImgHeight = adxAd.Video.Height
			responseVideoObject := &MeetyouResponseVideoObject{
				Duration: adxAd.Video.Duration / 1000,
				Size:     (200 + rand.Intn(800)) * 1024 * 1024,
				Width:    adxAd.Video.Width,
				Height:   adxAd.Video.Height,
				VideoUrl: adxAd.Video.VideoURL,
			}
			responseBidAdmObject.Video = responseVideoObject
		}

		isVideoAd := adxAd.Video != nil && len(adxAd.Video.VideoURL) > 0

		switch p.Request.Imp.AdId {
		case "1001", "1002", "1005", "1006":
			if isVideoAd {
				responseBidAdmObject.MaterialType = 9
			} else {
				responseBidAdmObject.MaterialType = 6
			}
		case "1003", "1004", "1007", "1008":
			if isVideoAd {
				responseBidAdmObject.MaterialType = 7
			} else {
				responseBidAdmObject.MaterialType = 2
			}
		case "1009", "1010":
			if isVideoAd {
				continue
			} else {
				responseBidAdmObject.MaterialType = 18
			}
		}

		responseBidObjectList = append(responseBidObjectList, responseBidAdmObject)
	}

	// 构造返回结果
	responseDataObject.AdId = p.Request.Imp.AdId
	responseDataObject.Bid = responseBidObjectList

	status := http.StatusOK
	if len(responseBidObjectList) == 0 {
		status = http.StatusNoContent
	}

	responseObject.Code = 0
	responseObject.Id = p.Request.Id
	responseObject.Data = &responseDataObject
	p.Response = &responseObject
	p.ResponseStatus = status

	return p
}

// ResponseResult 返回结果
func (p *MeetyouPipline) ResponseResult() (*MeetyouResponseObject, int) {
	if p.ResponseStatus == http.StatusNoContent {
		return &MeetyouResponseObject{
			Code:    102006,
			Message: "无填充",
			Data:    nil,
		}, http.StatusNoContent
	}
	return p.Response, http.StatusOK
}

// SetErrorString 设置字符串类型的错误信息
func (p *MeetyouPipline) SetErrorString(err string) *MeetyouPipline {
	return p.SetError(fmt.Errorf(err))
}

// SetError 设置错误信息
func (p *MeetyouPipline) SetError(err error) *MeetyouPipline {
	p.Error = err
	p.ResponseStatus = http.StatusNoContent
	p.Status = rtb.MH_RTB_PIPLINE_STATUS_BREAK

	if p.IsDebugging {
		fmt.Println("<<<<<<<<<<<<<< ERROR <<<<<<<<<<<<<<<<<")
		fmt.Printf("%+v\n", p)
		fmt.Println(">>>>>>>>>>>>>> ERROR >>>>>>>>>>>>>>>>>")
	}
	return p
}

func (p *MeetyouPipline) Print() *MeetyouPipline {
	fmt.Println("<<<<<<<<<<<<<<<< DEBUG <<<<<<<<<<<<<<<")
	fmt.Printf("%+v\n", p)
	fmt.Println(">>>>>>>>>>>>>>>> DEBUG >>>>>>>>>>>>>>>")
	return p
}

func getAppList(appList []string) []int {
	if len(appList) == 0 {
		return []int{}
	}
	var appListIdArray []int
	for _, appId := range appList {
		if v, ok := appListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var appListCodeMap = map[string]int{
	"2": 1002,
}
