package rtb_2345

import (
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/down_2345"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleBy2345(c *gin.Context, channel string) (*down_2345.Response, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()
	req := &down_2345.Request{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var deviceOs string
	switch req.GetDevice().GetOs() {
	case 3:
		deviceOs = "ios"
	case 4:
		deviceOs = "android"
	default:
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetDevice().GetNetwork() {
	case 0:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.GetDevice().GetCarrier() {
	case 0:
		carrier = 3
	case 1:
		carrier = 1
	case 2:
		carrier = 2
	default:
		carrier = 0
	}

	var resultfulImp []*down_2345.Request_Imp
	var configList []*models.RtbConfigByTagIDStu
	for _, imp := range req.GetImpList() {
		price := 0
		var styleIds []string

		for _, bidInfoItem := range imp.GetBidInfoList() {
			price = int(bidInfoItem.GetBidFloor())
		}

		for _, displayItem := range imp.GetDisplayList() {
			styleIds = append(styleIds, strconv.FormatInt(int64(displayItem.GetTemplateId()), 10))
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, strconv.FormatInt(int64(imp.GetSeatId()), 10), deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		configData := (*adxInfo)[0]
		resultfulImp = append(resultfulImp, imp)
		configList = append(configList, &configData)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var manufacturer string
	if deviceOs == "ios" {
		manufacturer = "Apple"
	} else {
		manufacturer = req.GetDevice().GetBrand()
	}

	reqRtbConfig := configList[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetApp().GetPackageName(),
			AppName:     req.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     1,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:               deviceOs,
			Manufacturer:     manufacturer,
			Model:            req.GetDevice().GetModel(),
			IP:               req.GetDevice().GetIp(),
			Ua:               req.GetDevice().GetUa(),
			Mac:              req.GetDevice().GetMac(),
			OsVersion:        req.GetDevice().GetOsv(),
			Imei:             req.GetDevice().GetImei(),
			ImeiMd5:          req.GetDevice().GetImeiMd5(),
			Oaid:             req.GetDevice().GetOaid(),
			OaidMd5:          req.GetDevice().GetOaidMd5(),
			Idfa:             req.GetDevice().GetIdfa(),
			IdfaMd5:          req.GetDevice().GetIdfaMd5(),
			ScreenWidth:      int(req.GetDevice().GetWidth()),
			ScreenHeight:     int(req.GetDevice().GetHeight()),
			AndroidID:        req.GetDevice().GetDpid(),
			AndroidIDMd5:     req.GetDevice().GetDpidMd5(),
			BootMark:         req.GetDevice().GetBootMark(),
			UpdateMark:       req.GetDevice().GetUpdateMark(),
			HMSCoreVersion:   req.GetDevice().GetHmsVersion(),
			DeviceBirthSec:   req.GetDevice().GetBirthTime(),
			DeviceStartSec:   req.GetDevice().GetBootTime(),
			SystemUpdateSec:  req.GetDevice().GetUpdateTime(),
			HarddiskSizeByte: req.GetDevice().GetSdFreeSpace(),
			CPUNum:           strconv.FormatInt(int64(req.GetDevice().GetSysCpuNum()), 10),
			DeviceType:       1,
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	if req.GetDevice().GetCaid() != nil {
		var caidMultiList []models.MHReqCAIDMulti
		if len(req.GetDevice().GetCaid().GetId()) > 0 && len(req.GetDevice().GetCaid().GetVersion()) > 0 {
			var caidMulti models.MHReqCAIDMulti
			caidMulti.CAID = req.GetDevice().GetCaid().GetId()
			caidMulti.CAIDVersion = req.GetDevice().GetCaid().GetVersion()
			caidMultiList = append(caidMultiList, caidMulti)
		}
		reqStu.Device.CAIDMulti = caidMultiList

		reqStu.Device.DeviceStartSec = req.GetDevice().GetCaid().GetBootTimeInSec()
		reqStu.Device.Country = req.GetDevice().GetCaid().GetCountryCode()
		reqStu.Device.Language = req.GetDevice().GetCaid().GetLanguage()
		reqStu.Device.DeviceNameMd5 = req.GetDevice().GetCaid().GetDeviceName()
		reqStu.Device.AppStoreVersion = req.GetDevice().GetCaid().GetSystemVersion()
		reqStu.Device.HardwareMachine = req.GetDevice().GetCaid().GetMachine()
		reqStu.Device.PhysicalMemoryByte = req.GetDevice().GetCaid().GetMemory()
		reqStu.Device.HarddiskSizeByte = req.GetDevice().GetCaid().GetDisk()
		reqStu.Device.SystemUpdateSec = req.GetDevice().GetCaid().GetSysFileTime()
		reqStu.Device.HardwareModel = req.GetDevice().GetCaid().GetModel()
		reqStu.Device.TimeZone = req.GetDevice().GetCaid().GetTimeZone()
		reqStu.Device.DeviceBirthSec = req.GetDevice().GetCaid().GetBootTimeInSec()
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var impItem *down_2345.Request_Imp
	for _, imp := range resultfulImp {
		if strconv.FormatInt(int64(imp.GetSeatId()), 10) == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var bids []*down_2345.BidOptions
	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__WIN_PRICE__" + "&log=" + url.QueryEscape(mhDataItem.Log)

		bid := down_2345.BidOptions{
			ImpId:      impItem.GetId(),
			Price:      int32(ecpm),
			CreativeId: mhDataItem.Crid,
			DirectiveResponse: &down_2345.DirectiveResponseOptions{
				CreativeId:     mhDataItem.Crid,
				AdvertiserId:   1,
				AdvertiserName: "FL",
				Vocation:       1519,
				Material: &down_2345.DirectiveResponseOptions_Material{
					Title:       mhDataItem.Title,
					Description: mhDataItem.Description,
					Icon: &down_2345.DirectiveResponseOptions_Material_Image{
						Url: mhDataItem.IconURL,
					},
				},
				AppInfo: &down_2345.DirectiveResponseOptions_AppInfo{
					PackageName:   mhDataItem.PackageName,
					AppName:       mhDataItem.AppName,
					AppSize:       int32(mhDataItem.PackageSize / 1024),
					AppLogo:       mhDataItem.IconURL,
					Version:       mhDataItem.AppVersion,
					Developer:     mhDataItem.Publisher,
					PrivacyUrl:    mhDataItem.PrivacyLink,
					PermissionUrl: mhDataItem.PermissionURL,
					FunctionDesc:  mhDataItem.AppInfo,
				},
				Url:   mhDataItem.LandpageURL,
				Imptk: mhDataItem.ImpressionLink,
				Clktk: mhDataItem.ClickLink,
				Nurl:  winURL,
			},
		}

		switch mhDataItem.CrtType {
		case 11:
			if mhDataItem.Image == nil || len(reqRtbConfig.ImageStyleID) == 0 {
				continue
			}
			var imgList []*down_2345.DirectiveResponseOptions_Material_Image
			for _, imageItem := range mhDataItem.Image {
				img := &down_2345.DirectiveResponseOptions_Material_Image{
					Url:    imageItem.URL,
					Width:  int32(imageItem.Width),
					Height: int32(imageItem.Height),
				}
				imgList = append(imgList, img)
			}
			bid.DirectiveResponse.Material.Images = imgList
			imageStyleID, _ := strconv.ParseInt(reqRtbConfig.ImageStyleID, 10, 32)
			bid.DirectiveResponse.TemplateId = int32(imageStyleID)

		case 20:
			if mhDataItem.Video == nil || len(reqRtbConfig.VideoStyleID) == 0 {
				continue
			}
			video := &down_2345.DirectiveResponseOptions_Material_Video{
				Url:      mhDataItem.Video.VideoURL,
				Duration: int32(mhDataItem.Video.Duration / 1000),
				Cover: &down_2345.DirectiveResponseOptions_Material_Image{
					Url:    mhDataItem.Video.CoverURL,
					Width:  int32(mhDataItem.Video.Width),
					Height: int32(mhDataItem.Video.Height),
				},
			}
			bid.DirectiveResponse.Material.Video = video
			videoStyleID, _ := strconv.ParseInt(reqRtbConfig.VideoStyleID, 10, 32)
			bid.DirectiveResponse.TemplateId = int32(videoStyleID)
		}
		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			if len(mhDataItem.ConvTracks) > 0 {
				for _, convTracks := range mhDataItem.ConvTracks {
					if convTracks.ConvType == 10 {
						bid.DirectiveResponse.Deeplinktk = convTracks.ConvURLS
					}
					if convTracks.ConvType == 11 {
						bid.DirectiveResponse.Deeplinkfailedtk = convTracks.ConvURLS
					}
				}
			}
		}

		var deepLink string
		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				deepLink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					deepLink = mhDataItem.MarketURL
				}
			}
			bid.DirectiveResponse.AppInfo.ProductType = 1
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				deepLink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					deepLink = mhDataItem.DeepLink
				}
			}

			if deviceOs == "ios" {
				bid.DirectiveResponse.AppInfo.IosUrl = mhDataItem.DownloadURL
				bid.DirectiveResponse.AppInfo.ProductType = 3
			} else {
				bid.DirectiveResponse.AppInfo.ProductType = 2
				bid.DirectiveResponse.AppInfo.AndroidUrl = mhDataItem.DownloadURL
			}
		}

		if len(deepLink) > 0 {
			bid.DirectiveResponse.AppInfo.Deeplink = deepLink
			if deviceOs == "ios" {
				bid.DirectiveResponse.AppInfo.UniversalLink = deepLink
			}
		}

		bids = append(bids, &bid)
	}

	if len(bids) == 0 {
		return &down_2345.Response{
			Resid: req.GetReqid(),
			Bidid: bigdataUID,
		}, http.StatusNoContent
	}

	var seatBids []*down_2345.SeatBidOptions
	seatBid := down_2345.SeatBidOptions{
		BidList: bids,
	}

	seatBids = append(seatBids, &seatBid)
	resp := &down_2345.Response{
		Resid:       req.GetReqid(),
		SeatBidList: seatBids,
		Bidid:       bigdataUID,
	}

	return resp, http.StatusOK
}
