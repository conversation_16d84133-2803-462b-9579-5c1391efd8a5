package rtb_uc_audit

import (
	"mh_proxy/models"
	"mh_proxy/rtb"

	"github.com/gin-gonic/gin"
)

// UcAuditRequestObject Objects
type UcAuditRequestObject struct {
	Id     string                      `json:"id"`
	Device *UcAuditRequestDeviceObject `json:"device"`
	App    *UcAuditRequestAppObject    `json:"app"`
	Imp    []*UcAuditRequestImpObject  `json:"imp"`
}

type UcAuditRequestDeviceObject struct {
	Ua             string `json:"ua"`
	Ip             string `json:"ip"`
	Model          string `json:"model"`
	Brand          string `json:"brand"`
	Os             string `json:"os"`
	Osv            string `json:"osv"`
	Carrier        string `json:"carrier"`
	Idfa           string `json:"idfa"`
	Didmd5         string `json:"didmd5"`
	Oaid           string `json:"oaid"`
	Oaidmd5        string `json:"oaidmd5"`
	Caid           string `json:"caid"`
	Version        string `json:"version"`
	Aaid           string `json:"aaid"`
	Ditmd5         string `json:"ditmd5"`
	Sutmd5         string `json:"sutmd5"`
	Sstmd5         string `json:"sstmd5"`
	Connectiontype int    `json:"connectiontype"`
}

type UcAuditRequestAppObject struct {
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
}

type UcAuditRequestImpObject struct {
	Id        string                  `json:"id"`
	SceneType int                     `json:"scene_type"`
	CpmFloor  int                     `json:"cpm_floor"`
	AdNum     int                     `json:"ad_num"`
	W         int                     `json:"w"`
	H         int                     `json:"h"`
	Ext       UcAuditRequestExtObject `json:"ext"`
}

type UcAuditRequestDeviceCaidObject struct {
	Caid    string `json:"caid"`
	Version string `json:"version"`
}

type UcAuditRequestExtObject struct {
	SupportWechat int   `json:"support_wechat"`
	Templateid    []int `json:"templateid"`
}

// UcAuditResponseObject Objects
type UcAuditResponseObject struct {
	Nbr     int                             `json:"nbr"`
	Id      string                          `json:"id"`
	Seatbid []*UcAuditResponseSeatbidObject `json:"seatbid"`
}

type UcAuditResponseSeatbidObject struct {
	Bid []*UcAuditResponseBidObject `json:"bid"`
}

type UcAuditResponseBidObject struct {
	Price int                       `json:"price"`
	Id    string                    `json:"id"`
	Impid string                    `json:"impid"`
	Nurl  string                    `json:"nurl"`
	Adid  string                    `json:"adid"`
	Ext   *UcAuditResponseExtObject `json:"ext"`
	Adm   *UcAuditResponseAdmObject `json:"adm"`
}

type UcAuditResponseExtObject struct {
	Templateid   int    `json:"templateid"`
	Industry     int    `json:"industry"`
	DspIndustry1 string `json:"dsp_industry1"`
	DspIndustry2 string `json:"dsp_industry2"`
	DspIndustry3 string `json:"dsp_industry3"`
}

type UcAuditResponseAdmObject struct {
	Imptrackers []string                       `json:"imptrackers"`
	Link        *UcAuditResponseLinkObject     `json:"link"`
	Assets      []*UcAuditResponseAssetsObject `json:"assets"`
}

type UcAuditResponseAssetsObject struct {
	Id      int                             `json:"id"`
	Icon    string                          `json:"icon,omitempty"`
	Title   *UcAuditResponseTitleObject     `json:"title,omitempty"`
	Img     *UcAuditResponseImgObject       `json:"img,omitempty"`
	Video   *UcAuditResponseVideoObject     `json:"video,omitempty"`
	Data    *UcAuditResponseDataObject      `json:"data,omitempty"`
	AppInfo *UcAuditResponseAppInfoObject   `json:"app_info,omitempty"`
	Ext     *UcAuditResponseAssetsExtObject `json:"ext,omitempty"`
}

type UcAuditResponseDataObject struct {
	Type  int    `json:"type"`
	Value string `json:"value"`
}

type UcAuditResponseAppInfoObject struct {
	AppName      string `json:"app_name"`
	VersionName  string `json:"version_name"`
	Developer    string `json:"developer"`
	UpdateTime   string `json:"update_time"`
	Permission   string `json:"permission"`
	Privacy      string `json:"privacy"`
	FunctionDesc string `json:"function_desc"`
}

type UcAuditResponseAssetsExtObject struct {
	OpMark      string `json:"op_mark,omitempty"`
	Rank        string `json:"rank,omitempty"`
	AccountName string `json:"account_name,omitempty"`
}

type UcAuditResponseTitleObject struct {
	Text string `json:"text"`
}

type UcAuditResponseImgObject struct {
	W   int    `json:"w"`
	H   int    `json:"h"`
	Url string `json:"url"`
	T   string `json:"t"`
}

type UcAuditResponseVideoObject struct {
	Duration int    `json:"Duration"`
	Size     int    `json:"Size"`
	Url      string `json:"url"`
}

type UcAuditResponseLinkObject struct {
	Url           string                        `json:"url"`
	Clicktrackers []string                      `json:"clicktrackers"`
	Ext           *UcAuditResponseLinkExtObject `json:"ext"`
}
type UcAuditResponseLinkExtObject struct {
	Deeplinkurl   string `json:"deeplinkurl"`
	Downloadurl   string `json:"downloadurl"`
	UniversalLink string `json:"universal_link"`
}

type UcAuditPipline struct {
	Context      *gin.Context
	UUID         string
	Channel      string
	Manufacturer string
	DeviceOs     rtb.MHRtbOSEnum
	ConnectType  rtb.MHRtbConnectTypeEnum
	Carrier      rtb.MHRtbCarrierEnum
	CaidMulti    []models.MHReqCAIDMulti

	IsDebugging bool

	ResponseStatus int
	Status         rtb.MHRtbPiplineStatusEnum
	Error          error

	Request  *UcAuditRequestObject
	Response *UcAuditResponseObject

	ConfigList    []*models.RtbConfigByTagIDStu
	ResultImp     []*UcAuditRequestImpObject
	AdxAdResponse *models.MHResp
}
