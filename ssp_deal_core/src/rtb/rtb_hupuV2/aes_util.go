package rtb_hupuV2

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/hex"
)

func Decrypt(str string, key string) string {
	raw := []byte(key)
	keySpec, _ := aes.NewCipher(raw)
	iv := []byte("0000000000000000")
	cipherText := hex2byte(str)
	plainText := make([]byte, len(cipherText))
	cipher.NewCBCDecrypter(keySpec, iv).CryptBlocks(plainText, cipherText)
	return string(plainText)
}

func hex2byte(str string) []byte {
	l := len(str)
	if l%2 == 1 {
		return nil
	}
	b := make([]byte, l/2)
	for i := 0; i < l/2; i++ {
		val, _ := hex.DecodeString(str[i*2 : i*2+2])
		b[i] = val[0]
	}
	return b
}
