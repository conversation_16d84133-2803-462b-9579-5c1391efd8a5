package rtb_vivo

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/pb/vivo"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rediskeys"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleVivo(c *gin.Context, channel string) (*vivo.BidResponse, int) {
	bodyContent, _ := c.GetRawData()
	bigdataUID := uuid.NewV4().String()

	req := &vivo.BidRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	var deviceOs string
	switch req.GetDevice().GetOs() {
	case "Android":
		deviceOs = "android"
	case "IOS":
		deviceOs = "ios"
	default:
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	var connectType int
	switch req.GetDevice().GetConnectionType() {
	case 100:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	case 0:
		connectType = 0
	default:
		connectType = 0
	}

	carrier := 0

	var adsCount int
	var keyword string
	var resultfulImp []*vivo.BidRequest_Impression
	var configList []models.RtbConfigByTagIDStu

	for _, imp := range req.GetImp() {
		price := 0
		adsCount = int(imp.GetAdsCount())

		var styleIds []string
		if imp.GetBidFloor() > 0 {
			price = int(imp.GetBidFloor())
		}
		for _, templateId := range imp.GetTemplateIds() {
			styleIds = append(styleIds, strconv.Itoa(int(templateId)))
		}

		var adxInfo *[]models.RtbConfigByTagIDStu
		adxInfo = models.GetAdxInfoByRtbTagIDAndStyles(c, channel, imp.GetTagId(), deviceOs, styleIds, "", price)
		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		if len(req.GetApp().GetBundle()) > 0 {
			for _, infoItem := range *adxInfo {
				if infoItem.PackageName == req.GetApp().GetBundle() {
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *adxInfo {
				if len(infoItem.PackageName) == 0 {
					configList = append(configList, infoItem)
				}
			}
		}

		resultfulImp = append(resultfulImp, imp)
	}

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	reqRtbConfig := configList[0]
	var impItem *vivo.BidRequest_Impression
	for _, imp := range resultfulImp {
		if imp.GetTagId() == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	adsCount = int(impItem.GetAdsCount())

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetApp().GetBundle(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     adsCount,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
			Query:       keyword,
		},
		Device: models.MHReqDevice{
			Os:           deviceOs,
			Manufacturer: req.GetDevice().GetMake(),
			Model:        req.GetDevice().GetModel(),
			IP:           req.GetDevice().GetIp(),
			Ua:           req.GetDevice().GetUa(),
			OsVersion:    req.GetDevice().GetOsv(),
			ImeiMd5:      req.GetDevice().GetDidMd5(),
			AndroidIDMd5: req.GetDevice().GetDpidMd5(),
			Oaid:         req.GetDevice().GetOaid(),
			DeviceType:   1,
			AppList:      getVivoAppList(req.GetInstalledAppIds(), req.GetInstalledAppStatus()),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}

	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	var bids []*vivo.BidResponse_SeatBid_Bid
	var seatBids []*vivo.BidResponse_SeatBid

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}

		var advertiserId int
		var category int

		if len(mhDataItem.Publisher) > 0 {
			redisKey := fmt.Sprintf(rediskeys.ADX_SSP_VIVO_ADVERTISER_KEY, mhDataItem.Publisher)
			redisValue, _ := db.GlbRedis.Get(c, redisKey).Result()

			if len(redisValue) == 0 {
				for key, value := range vivoAdvertiserDataMap {
					if strings.Contains(mhDataItem.DeepLink, key) {
						advertiserId = value.AdvertiserId
						category = value.CategoryId
						break
					}
				}
			} else {
				var vivoCache VivoCacheModel
				_ = json.Unmarshal([]byte(redisValue), &vivoCache)
				advertiserId, _ = strconv.Atoi(vivoCache.AdvertiserId)
				category, _ = strconv.Atoi(vivoCache.CategoryId)
			}
		}

		if advertiserId == 0 || category == 0 {
			go models.BigDataHolovVvoPublisherDebug(mhDataItem.Publisher)
			continue
		}

		ecpm := mhDataItem.Ecpm
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${WIN_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		appInfo := &vivo.BidResponse_SeatBid_Bid_Adm_AppInfo{
			IconUrl:          mhDataItem.IconURL,
			Version:          mhDataItem.AppVersion,
			Developer:        mhDataItem.Publisher,
			Permission:       getPermission(),
			PrivacyPolicyUrl: mhDataItem.PrivacyLink,
			Name:             mhDataItem.AppName,
		}

		adm := &vivo.BidResponse_SeatBid_Bid_Adm{
			Title:        truncateString(mhDataItem.Description),
			Text:         mhDataItem.Description,
			LandingSite:  mhDataItem.LandpageURL,
			AppPackage:   mhDataItem.PackageName,
			AdvertiserId: int64(advertiserId),
			Category:     int64(category),
			Source:       "MH",
			SourceAvatar: "https://images.maplehaze.cn/images/c0bcc6935755ee7bf1b422ac54f97cb5.png",
			AppInfo:      appInfo,
			IconUrl:      mhDataItem.IconURL,
			AppName:      mhDataItem.AppName,
		}

		switch mhDataItem.InteractType {
		case 0:
			if len(mhDataItem.DeepLink) > 0 {
				adm.Deeplink = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					adm.Deeplink = mhDataItem.MarketURL
				}
			}
			adm.AdType = 1
			if len(adm.Deeplink) > 0 {
				adm.AdType = 3
			}
		case 1:
			if len(mhDataItem.MarketURL) > 0 {
				adm.Deeplink = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					adm.Deeplink = mhDataItem.DeepLink
				}
			}
			adm.AdType = 2
			appInfo.DownloadUrl = mhDataItem.DownloadURL

			losPos := models.GetLocalPosInfo(c, reqRtbConfig.LocalPosID, reqRtbConfig.LocalAppID)
			if losPos != nil {
				if losPos.LocalPosDownloadType == 1 {
					adm.PackageChannel = 1
				}
			}

			if len(adm.Deeplink) > 0 {
				adm.AdType = 4
			}
		}

		// todo vivo dp+download都有的情况下，强制把AdType=4(佳坤定的)
		if len(adm.Deeplink) > 0 && len(mhDataItem.DownloadURL) > 0 {
			adm.AdType = 4
		}

		var convUrls []string
		if len(adm.Deeplink) > 0 {
			if len(mhDataItem.ConvTracks) > 0 {
				for _, convTracks := range mhDataItem.ConvTracks {
					if convTracks.ConvType == 10 {
						convUrls = convTracks.ConvURLS
					}
				}
			}
		}

		switch mhDataItem.CrtType {
		case 11:
			if len(reqRtbConfig.ImageStyleID) == 0 || mhDataItem.Image == nil {
				continue
			}
			var imgList []*vivo.BidResponse_SeatBid_Bid_Adm_Img
			for _, imageItem := range mhDataItem.Image {
				img := vivo.BidResponse_SeatBid_Bid_Adm_Img{
					W:   int32(imageItem.Width),
					H:   int32(imageItem.Height),
					Url: imageItem.URL,
				}
				imgList = append(imgList, &img)
			}
			styleID, _ := strconv.ParseInt(reqRtbConfig.ImageStyleID, 10, 32)
			adm.Img = imgList
			adm.TemplateId = int32(styleID)
		case 20:
			if len(reqRtbConfig.VideoStyleID) == 0 || mhDataItem.Video == nil {
				continue
			}
			video := vivo.BidResponse_SeatBid_Bid_Adm_Video{
				W:        int32(mhDataItem.Video.Width),
				H:        int32(mhDataItem.Video.Height),
				Duration: int32(mhDataItem.Video.Duration / 1000),
				VideoUrl: mhDataItem.Video.VideoURL,
				Bitrate:  170,
				Size:     int32(5 * 1024 * 1024),
				Type:     "video/mp4",
			}
			if len(mhDataItem.Video.CoverURL) > 0 {
				var imgList []*vivo.BidResponse_SeatBid_Bid_Adm_Img
				img := vivo.BidResponse_SeatBid_Bid_Adm_Img{
					W:   int32(mhDataItem.Video.Width),
					H:   int32(mhDataItem.Video.Height),
					Url: mhDataItem.Video.CoverURL,
				}
				imgList = append(imgList, &img)
				adm.Img = imgList
			}

			styleID, _ := strconv.ParseInt(reqRtbConfig.VideoStyleID, 10, 32)
			adm.Video = &video
			adm.TemplateId = int32(styleID)
		}
		bid := vivo.BidResponse_SeatBid_Bid{
			Id:          bigdataUID,
			ImpId:       impItem.GetId(),
			Price:       int64(ecpm),
			CreativeId:  mhDataItem.Crid,
			Nurl:        winURL,
			ImpTrackers: mhDataItem.ImpressionLink,
			ClkTrackers: mhDataItem.ClickLink,
			Adm:         adm,
			DpTrackers:  convUrls,
			BidType:     0,
		}

		bids = append(bids, &bid)
		seatBid := vivo.BidResponse_SeatBid{
			Bid: bids,
		}
		seatBids = append(seatBids, &seatBid)
	}

	if len(seatBids) == 0 {
		return &vivo.BidResponse{
			Id:     req.GetId(),
			BidId:  bigdataUID,
			Status: 1,
			Nbr:    3,
		}, http.StatusNoContent
	}

	resp := &vivo.BidResponse{
		Id:      req.GetId(),
		BidId:   bigdataUID,
		SeatBid: seatBids,
		Status:  0,
	}

	return resp, http.StatusOK
}

func getPermission() string {
	var allPermission []Permission
	contactsPermission := Permission{
		PermissionType: "0",
		Describe:       "允许该应用读取您手机上存储的联系人的相关数据，包括您通过打电话、发送电子邮件或以其他方式与特定个人通信的频率。此权限可让应用保存您的联系人数据，而恶意应用可能会在您不知情的情况下分享联系人数据",
		Title:          "读取联系人",
	}
	lbsPermission := Permission{
		PermissionType: "0",
		Describe:       "允许程序通过WiFi或移动基站的方式获取用户粗略位置信息",
		Title:          "读取用户位置信息",
	}
	memoryPermission := Permission{
		PermissionType: "0",
		Describe:       "允许程序可以读取设备外部存储空间",
		Title:          "读取存储空间",
	}

	allPermission = append(allPermission, contactsPermission, lbsPermission, memoryPermission)
	marshal, _ := json.Marshal(allPermission)
	return string(marshal)
}

func truncateString(s string) string {
	var tmp string
	i := 1
	for index, char := range []rune(s) {
		if index > 24 {
			continue
		}
		tmp = tmp + fmt.Sprintf("%c", char)
		i++
	}

	if i <= 6 {
		tmp = tmp + "......"
	}
	return tmp
}

type Permission struct {
	PermissionType string `json:"permissionType"`
	Describe       string `json:"describe"`
	Title          string `json:"title"`
}

type VivoAdvertiserData struct {
	AdvertiserId int
	CategoryId   int
}

type VivoCacheModel struct {
	Name         string `json:"name"`
	CategoryId   string `json:"category_id"`
	AdvertiserId string `json:"advertiser_id"`
}

var vivoAdvertiserDataMap = map[string]VivoAdvertiserData{
	"dewu.com":              {AdvertiserId: 3188211, CategoryId: 220168},
	"dewuapp":               {AdvertiserId: 3188211, CategoryId: 220168},
	"jdmobile":              {AdvertiserId: 3188208, CategoryId: 220168},
	"jd.com":                {AdvertiserId: 3188208, CategoryId: 220168},
	"openjd":                {AdvertiserId: 3188208, CategoryId: 220168},
	"com.xunmeng.pinduoduo": {AdvertiserId: 3188209, CategoryId: 220168},
	"meituan.com":           {AdvertiserId: 3188212, CategoryId: 220039},
	"taobao.com":            {AdvertiserId: 3188207, CategoryId: 220168},
	"tpopen":                {AdvertiserId: 3188207, CategoryId: 220168},
}

func getVivoAppList(appIDList []string, installedAppStatus int32) []int {
	if len(appIDList) == 0 || installedAppStatus != 1 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := vivoAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var vivoAppListCodeMap = map[string]int{
	"1c77975e5341da64997b4e17a56e5fdd": 1001,
	"e935b56cd77eca4d291fd0c22e3b6bb1": 1002,
	"29efcc56a931d1a369a23dae134d9837": 1004,
	"d23d0e67df4e17399f9ea8b9b6f2010d": 1009,
}
