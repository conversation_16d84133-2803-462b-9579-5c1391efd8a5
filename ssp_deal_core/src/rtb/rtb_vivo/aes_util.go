package rtb_vivo

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"fmt"
)

const (
	INITIALIZATION_VECTOR_SIZE = 16
	CIPHER_TEXT_SIZE           = 8
	SIGNATURE_SIZE             = 4
)

func Decrypt(priceEncodeStr string, encryptionKey, integrityKey []byte) float64 {
	ciphertext, err := base64.RawURLEncoding.DecodeString(priceEncodeStr)
	if err != nil {
		return 0
	}

	plaintextLength := len(ciphertext) - INITIALIZATION_VECTOR_SIZE - SIGNATURE_SIZE
	if plaintextLength < 0 {
		return 0
	}

	iv := ciphertext[:INITIALIZATION_VECTOR_SIZE]
	ciphertextBytes := ciphertext[INITIALIZATION_VECTOR_SIZE : INITIALIZATION_VECTOR_SIZE+CIPHER_TEXT_SIZE]
	signature := ciphertext[INITIALIZATION_VECTOR_SIZE+CIPHER_TEXT_SIZE : INITIALIZATION_VECTOR_SIZE+CIPHER_TEXT_SIZE+SIGNATURE_SIZE]

	ekHmac := hmac.New(sha1.New, encryptionKey)
	ekHmac.Write(iv)
	pad := ekHmac.Sum(nil)

	plaintext := make([]byte, CIPHER_TEXT_SIZE)
	for i := 0; i < CIPHER_TEXT_SIZE; i++ {
		plaintext[i] = pad[i] ^ ciphertextBytes[i]
	}

	value := int64(binary.BigEndian.Uint64(plaintext))

	ikHmac := hmac.New(sha1.New, integrityKey)
	ikHmac.Write(plaintext)
	ikHmac.Write(ciphertext[:INITIALIZATION_VECTOR_SIZE])
	computedSignature := ikHmac.Sum(nil)[:SIGNATURE_SIZE]

	if !bytes.Equal(signature, computedSignature) {
		fmt.Println("signature error")
		return 0
	}

	return float64(value) / 1000000
}
