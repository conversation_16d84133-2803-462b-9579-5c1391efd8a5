package rtb_jxedt

import (
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/jxedt"
	"mh_proxy/utils"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleByJxedt(c *gin.Context, channel string) *jxedt.Response {
	bodyContent, _ := c.GetRawData()
	req := &jxedt.Request{}

	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "解码失败",
		}
	}

	deviceOs := req.GetDevice().GetOs()
	deviceMake := req.GetDevice().GetMake()
	if deviceOs == "ios" {
		deviceMake = "Apple"
	}

	var connectType int
	switch req.GetDevice().GetNetwork() {
	case 1:
		connectType = 1
	case 2:
		connectType = 2
	case 3:
		connectType = 3
	case 4:
		connectType = 4
	case 5:
		connectType = 7
	default:
		connectType = 0
	}

	var carrier int
	switch req.GetDevice().GetCarrier() {
	case 1:
		carrier = 1
	case 2:
		carrier = 3
	case 3:
		carrier = 2
	default:
		carrier = 0
	}

	imp := req.GetImp()
	if len(imp.Id) == 0 {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "imp.id为空",
		}
	}

	var reqRtbConfig models.RtbConfigByTagIDStu
	tagId := imp.GetTagId()
	price := imp.GetBidFloor()
	styleIds := imp.GetTemplateId()

	rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, tagId, deviceOs, styleIds, "", int(price))
	if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "tagId 为空",
		}
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(req.GetDevice().GetCaid()) > 0 && len(req.GetDevice().GetCaidVersion()) > 0 {
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = req.GetDevice().GetCaid()
		caidMulti.CAIDVersion = req.GetDevice().GetCaidVersion()
		caidMultiList = append(caidMultiList, caidMulti)
	}

	reqRtbConfig = (*rtbConfigArrayByTagID)[0]
	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetApp().GetBundle(),
			AppName:     req.GetApp().GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:                 deviceOs,
			OsVersion:          req.GetDevice().GetOsv(),
			Model:              req.GetDevice().GetModel(),
			Manufacturer:       deviceMake,
			Imei:               req.GetDevice().GetImei(),
			ImeiMd5:            req.GetDevice().GetImeiMd5(),
			AndroidID:          req.GetDevice().GetAndroidId(),
			Oaid:               req.GetDevice().GetOaid(),
			Mac:                req.GetDevice().GetMac(),
			Idfa:               req.GetDevice().GetIdfa(),
			IdfaMd5:            req.GetDevice().GetIdfaMd5(),
			Ua:                 req.GetDevice().GetUa(),
			ScreenWidth:        int(req.GetDevice().GetWidth()),
			ScreenHeight:       int(req.GetDevice().GetHeight()),
			DeviceType:         1,
			IP:                 req.GetDevice().GetIp(),
			DeviceStartSec:     req.GetDevice().GetBootTime(),
			SystemUpdateSec:    req.GetDevice().GetUpdateMark(),
			BootMark:           req.GetDevice().GetBootMark(),
			UpdateMark:         req.GetDevice().GetUpdateMark(),
			DeviceNameMd5:      req.GetDevice().GetPhoneName(),
			PhysicalMemoryByte: req.GetDevice().GetMemorySize(),
			HarddiskSizeByte:   req.GetDevice().GetDiskSize(),
			Language:           req.GetDevice().GetLanguage(),
			Country:            req.GetDevice().GetCountry(),
			TimeZone:           req.GetDevice().GetTimezone(),
			HardwareModel:      req.GetDevice().GetModelCode(),
			HMSCoreVersion:     req.GetDevice().GetHmsVersion(),
			CAIDMulti:          caidMultiList,
			AppList:            getJxedtAppList(req.GetApp().GetUserTag()),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}
	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)
	if mhResp.Ret != 0 {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "adx返回为空",
		}
	}

	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "adx返回广告个数为空",
		}
	}

	mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[0]

	// 统计
	models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

	// 判定上游返回ecpm是否大于底价
	if mhDataItem.Ecpm < reqRtbConfig.Price {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "价格错误",
		}
	}
	ecpm := mhDataItem.Ecpm

	nurl := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__win_price__" + "&log=" + url.QueryEscape(mhDataItem.Log)

	bid := &jxedt.Response_Bid{
		ImpId:                 req.GetImp().GetId(),
		Price:                 int32(ecpm),
		Nurl:                  nurl,
		Crid:                  mhDataItem.Crid,
		ImpressionTrackingUrl: mhDataItem.ImpressionLink,
		ClickTrackingUrl:      mhDataItem.ClickLink,
		AppPackageName:        mhDataItem.PackageName,
		AppName:               mhDataItem.AppName,
		IconUrl:               mhDataItem.IconURL,
		Title:                 mhDataItem.Title,
		Desc:                  mhDataItem.Description,
	}

	switch mhDataItem.InteractType {
	case 0:
		if len(mhDataItem.DeepLink) > 0 {
			bid.DeeplinkUrl = mhDataItem.DeepLink
		} else {
			if len(mhDataItem.MarketURL) > 0 {
				bid.DeeplinkUrl = mhDataItem.MarketURL
			}
		}

		bid.LandingPageUrl = mhDataItem.LandpageURL
	case 1:
		if len(mhDataItem.MarketURL) > 0 {
			bid.DeeplinkUrl = mhDataItem.MarketURL
		} else {
			if len(mhDataItem.DeepLink) > 0 {
				bid.DeeplinkUrl = mhDataItem.DeepLink
			}
		}

		bid.LandingPageUrl = mhDataItem.DownloadURL
	}

	if len(bid.DeeplinkUrl) > 0 {
		if len(mhDataItem.ConvTracks) > 0 {
			for _, convTracks := range mhDataItem.ConvTracks {
				if convTracks.ConvType == 10 {
					bid.WakeUpTrackingUrl = convTracks.ConvURLS
				}
			}
		}
		if deviceOs == "ios" {
			bid.UlkUrl = bid.DeeplinkUrl
		}
	}

	switch mhDataItem.CrtType {
	case 11:
		if mhDataItem.Image != nil {
			bid.ImgWidth = int32(mhDataItem.Image[0].Width)
			bid.ImgHeight = int32(mhDataItem.Image[0].Height)
			bid.ImgUrl = mhDataItem.Image[0].URL

			bid.TemplateId = reqRtbConfig.ImageStyleID
		} else {
			return &jxedt.Response{
				Id:   req.GetId(),
				Code: http.StatusNoContent,
				Msg:  "返回为空",
			}
		}

	case 20:
		if mhDataItem.Video != nil {
			bid.ImgUrl = mhDataItem.Video.VideoURL
			bid.CoverUrl = mhDataItem.Video.CoverURL
			bid.VideoDuration = int32(mhDataItem.Video.Duration / 1000)

			bid.TemplateId = reqRtbConfig.VideoStyleID
		} else {
			return &jxedt.Response{
				Id:   req.GetId(),
				Code: http.StatusNoContent,
				Msg:  "返回为空",
			}
		}
	}

	if len(bid.TemplateId) == 0 {
		return &jxedt.Response{
			Id:   req.GetId(),
			Code: http.StatusNoContent,
			Msg:  "返回为空",
		}
	}

	resp := &jxedt.Response{
		Id:    req.GetId(),
		BidId: bigdataUID,
		Bid:   bid,
		Code:  http.StatusOK,
		Msg:   "成功",
	}

	return resp
}

func getJxedtAppList(appList []string) []int {
	if len(appList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appList {
		if v, ok := jxedtAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var jxedtAppListCodeMap = map[string]int{
	"1": 1005,
	"2": 1001,
	"3": 1002,
	"4": 1006,
	"5": 1020,
	"6": 1008,
	"7": 1019,
	"8": 1004,
	"9": 1009}
