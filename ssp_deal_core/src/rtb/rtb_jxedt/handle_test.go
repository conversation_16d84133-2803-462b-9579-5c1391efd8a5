package rtb_jxedt

import (
	"bytes"
	"fmt"
	"io"
	"mh_proxy/utils"
	"net/http"
	"testing"

	"mh_proxy/pb/jxedt"

	"google.golang.org/protobuf/proto"
)

func Test_HandleByJxedt(t *testing.T) {
	request := &jxedt.Request{
		Id:         "15ae86e334f54c0197023b8852ae4c76",
		ApiVersion: "1.0.0",
		Site: &jxedt.Request_Site{
			Id:   "23882",
			Name: "驾校一点通",
		},
		App: &jxedt.Request_App{
			Id:         "438946",
			Name:       "驾校一点通",
			Bundle:     "com.jxedt",
			AppVersion: "12.9.3",
		},
		Imp: &jxedt.Request_Imp{
			Id:    "46466",
			TagId: "2992354",
			Banner: &jxedt.Request_Banner{
				Width:           1080,
				Height:          1920,
				PosType:         1,
				MaterialType:    []string{"jpg", "png", "gif"},
				InteractionType: []string{"h5", "deeplink"},
			},
			/*Video: &jxedt.Request_Video{
				Width:        0,
				Height:       0,
				PosType:      0,
				MaterialType: nil,
				MaxDuration:  0,
				MinDuration:  0,
				MaxBitrate:   0,
				MaxSize:      0,
			},*/
			BidFloor:   1,
			TemplateId: []string{"3"},
		},
		Device: &jxedt.Request_Device{
			Ua: "Mozilla/5.0 (Linux; Android 11; PEGM00 Build/RKQ1.200903.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36",
			Geo: &jxedt.Request_Geo{
				Lat: 24.24152946472168,
				Lon: 109.28942108154297,
			},
			Ip:         "***************",
			DeviceType: 0,
			Make:       "OPPO",
			Model:      "PEGM00",
			Os:         "android",
			Osv:        "11",
			Height:     2285,
			Width:      1080,
			Carrier:    1,
			Network:    1,
			Oaid:       "1C60574BEB4A44FE9469D682E0E5B271388b1e9af5e48b00027668ca9fc294f8",
			AndroidId:  "2a29f2c9cb1a8e11",
			BootMark:   "763d5649-3c21-4c3b-83f2-ac2570800440",
			UpdateMark: "17857.107999990",
		},
	}

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://test.maplehaze.cn/rtb/request?channel=43", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	jxedtResp := &jxedt.Response{}
	err = proto.Unmarshal(bodyContent, jxedtResp)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(jxedtResp)
}

func Test_HandleByJxedtPrice(t *testing.T) {
	price := "663DCzzUt027HK0cx_Ioaw"
	key := "1583db0219e544a5"
	tmpPrice, _ := utils.Base64URLDecode(price)

	decodePrice := AesUtil(tmpPrice, []byte(key))
	//decodePrice := utils.AesCBCPKCS5Decrypt([]byte("JxKh0dN9mvZTSyQZd8mqeQ"), []byte("404d152d70524247"))
	fmt.Println(string(decodePrice))
}
