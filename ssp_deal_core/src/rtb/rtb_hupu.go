package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// HandleByHupu ...
func HandleByHupu(c *gin.Context, channel string) (*map[string]interface{}, int) {

	bodyContent, err := c.GetRawData()
	var hupuReq HupuReq

	err = json.Unmarshal([]byte(bodyContent), &hupuReq)

	if err != nil {
		fmt.Println(err)
		return jsonHupuNoBidReturn("parser error")
	}

	// fmt.Println(hupuReq.ID)
	// fmt.Println(string(bodyContent))
	// fmt.Println(err)

	reqOs := strings.ToLower(hupuReq.Device.Os)
	if reqOs == "android" || reqOs == "ios" {
	} else {
		return jsonHupuNoBidReturn("wrong os")
	}

	reqDeivceMake := hupuReq.Device.Make
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(hupuReq.Device.UA) == 0 {
		return jsonHupuNoBidReturn("wrong ua")
	}

	reqConnectType := 0
	if hupuReq.Device.ConnectionType == 0 {
		reqConnectType = 0
	} else if hupuReq.Device.ConnectionType == 2 {
		reqConnectType = 1
	} else if hupuReq.Device.ConnectionType == 4 {
		reqConnectType = 2
	} else if hupuReq.Device.ConnectionType == 5 {
		reqConnectType = 3
	} else if hupuReq.Device.ConnectionType == 6 {
		reqConnectType = 4
	} else if hupuReq.Device.ConnectionType == 7 {
		reqConnectType = 7
	} else {
		reqConnectType = 0
	}

	reqCarrier := 0
	if hupuReq.Device.Carrier == "1" {
		reqCarrier = 1
	} else if hupuReq.Device.Carrier == "2" {
		reqCarrier = 2
	} else if hupuReq.Device.Carrier == "3" {
		reqCarrier = 3
	} else {
		reqCarrier = 0
	}

	if len(hupuReq.Imp) == 0 {
		return jsonHupuNoBidReturn("wrong imp")
	}

	var reqOKImps []HupuReqImp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range hupuReq.Imp {
		// if len(item.Native.Assets) == 0 {
		// 	return jsonHupuNoBidReturn("wrong native")
		// }
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := utils.ConvertIntToString(item.TagID)
		if reqOs == "android" {
			// reqTagID = "hupu_" + utils.ConvertIntToString(item.TagID) + "_android"
		} else if reqOs == "ios" {
			// reqTagID = "hupu_" + utils.ConvertIntToString(item.TagID) + "_ios"
		} else {
			continue
		}
		reqPrice := utils.ConvertStringToInt(item.BidFloor)
		if len(item.Pmp.Deal) > 0 {
			if len(item.Pmp.Deal[0].BidFloor) > 0 {
				reqPrice = utils.ConvertStringToInt(item.Pmp.Deal[0].BidFloor)
			}
			if len(item.Pmp.Deal[0].ID) > 0 {
				if reqOs == "android" {
					// reqTagID = "hupu_" + item.Pmp.Deal[0].ID + "_android"
					reqTagID = item.Pmp.Deal[0].ID
				} else if reqOs == "ios" {
					// reqTagID = "hupu_" + item.Pmp.Deal[0].ID + "_ios"
					reqTagID = item.Pmp.Deal[0].ID
				}
			}
		}

		// fmt.Println(reqTagID)
		// fmt.Println(reqPrice)
		// if rand.Intn(10000) == 0 {
		// 	models.UpdateRtbFloorPriceByTagID(c, reqTagID, int(reqPrice))
		// }

		// if isWhiteDID == 0 && models.IsRtbTagIDActive(c, reqTagID) == 0 {
		// 	return jsonHupuNoBidReturn("not active")
		// }

		// rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, reqTagID, reqOs, int(reqPrice))
		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return jsonHupuNoBidReturn("not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return jsonHupuNoBidReturn("wrong imp")
	}

	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	hupuHarddiskSizeByte := hupuReq.Device.HarddiskSizeByte
	if strings.Contains(hupuHarddiskSizeByte, ".") {
		tmpHardDiskSizeByte := int(utils.ConvertStringToFloat(hupuHarddiskSizeByte) * 1024 * 1024)
		hupuHarddiskSizeByte = utils.ConvertIntToString(tmpHardDiskSizeByte)
	}

	// hupu hms core version
	hmsCoreVersion := ""
	if len(hupuReq.Device.HMSCoreVersion) > 0 && hupuReq.Device.HMSCoreVersion != "-1" {
		hmsCoreVersion = hupuReq.Device.HMSCoreVersion
	}
	// hupu appstore version
	appStoreVersion := ""
	if len(hupuReq.Device.AgVersion) > 3 && hupuReq.Device.AgVersion != "-1" {
		appStoreVersion = hupuReq.Device.AgVersion
	} else if hupuReq.Device.OppoStoreVersion > 0 {
		appStoreVersion = utils.ConvertIntToString(hupuReq.Device.OppoStoreVersion)
	} else if hupuReq.Device.VivoStoreVersion > 0 {
		appStoreVersion = utils.ConvertIntToString(hupuReq.Device.VivoStoreVersion)
	} else if len(hupuReq.Device.MiStoreVersion) > 3 && hupuReq.Device.MiStoreVersion != "-1" {
		// appStoreVersion = hupuReq.Device.MiuiStoreVersion
	}

	var oldCaidMulti models.MHReqCAIDMulti
	var newCaidMulti models.MHReqCAIDMulti
	var caidMultiList []models.MHReqCAIDMulti
	oldCaidMulti.CAID = hupuReq.Device.OldCaid
	oldCaidMulti.CAIDVersion = hupuReq.Device.CaidOldVersion
	if len(oldCaidMulti.CAID) > 0 && len(oldCaidMulti.CAIDVersion) > 0 {
		caidMultiList = append(caidMultiList, oldCaidMulti)
	}
	newCaidMulti.CAID = hupuReq.Device.NewCaid
	newCaidMulti.CAIDVersion = hupuReq.Device.CaidNewVersion
	if len(newCaidMulti.CAID) > 0 && len(newCaidMulti.CAIDVersion) > 0 {
		caidMultiList = append(caidMultiList, newCaidMulti)
	}
	// caidMultiList = append(caidMultiList, newCaidMulti, oldCaidMulti)

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: hupuReq.App.Bundle,
			AppName:     hupuReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    hupuReq.Device.Osv,
			Model:        hupuReq.Device.Model,
			Manufacturer: reqDeivceMake,
			Imei:         hupuReq.Device.Imei,
			AndroidID:    hupuReq.Device.AndroidID,
			Oaid:         hupuReq.Device.Oaid,
			Mac:          hupuReq.Device.Mac,
			Idfa:         hupuReq.Device.Idfa,
			Ua:           hupuReq.Device.UA,
			ScreenWidth:  hupuReq.Device.ScreenWidth,
			ScreenHeight: hupuReq.Device.ScreenHeight,
			DeviceType:   1,
			IP:           hupuReq.Device.IP,
			// ios 14 新增字段
			DeviceStartSec:     hupuReq.Device.DeviceStartSec,
			Country:            hupuReq.Device.Country,
			Language:           hupuReq.Device.Language,
			DeviceNameMd5:      hupuReq.Device.DeviceNameMd5,
			HardwareMachine:    hupuReq.Device.HardwareMachine,
			HardwareModel:      hupuReq.Device.HardwareModel,
			PhysicalMemoryByte: hupuReq.Device.PhysicalMemoryByte,
			HarddiskSizeByte:   hupuHarddiskSizeByte,
			SystemUpdateSec:    hupuReq.Device.SystemUpdateSec,
			TimeZone:           hupuReq.Device.TimeZone,
			BootMark:           hupuReq.Device.BootMark,
			UpdateMark:         hupuReq.Device.UpdateMark,
			HMSCoreVersion:     hmsCoreVersion,
			AppStoreVersion:    appStoreVersion,
			CAIDMulti:          caidMultiList,
			DeviceBirthSec:     hupuReq.Device.DeviceFileTime,
			AppList:            getHupuAppList(hupuReq.Device.LogicNum),
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return jsonHupuNoBidReturn("no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonHupuNoBidReturn("no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////

	hupuRespBidArrayMap := []map[string]interface{}{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)

		// crid
		crid := utils.GetMd5(mhDataItem.Title)
		if len(mhDataItem.Crid) > 0 {
			crid = mhDataItem.Crid
		}

		// fmt.Println("xxx")
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
		}

		// req_imp_id = 1 小图
		// req_imp_id = 2 大图
		// req_imp_id = 3 视频
		reqImpID := 1
		var reqImpItem HupuReqImp
		for _, hupuImpItem := range hupuReq.Imp {
			if hupuImpItem.ID == 2 {
				reqImpID = 2
				reqImpItem = hupuImpItem

			}
		}

		for _, hupuImpItem := range hupuReq.Imp {
			if hupuImpItem.ID == 3 && isVideoType == 1 {
				reqImpID = 3
				reqImpItem = hupuImpItem
			}
		}
		// fmt.Println("req imp id: " + utils.ConvertIntToString(reqImpID))
		// fmt.Println("resp is video: " + utils.ConvertIntToString(isVideoType))

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			clkItem = strings.Replace(clkItem, "__WIDTH__", utils.ConvertIntToString(hupuReq.Device.ScreenWidth), -1)
			clkItem = strings.Replace(clkItem, "__HEIGHT__", utils.ConvertIntToString(hupuReq.Device.ScreenHeight), -1)

			clkTrackArray = append(clkTrackArray, clkItem)
		}
		// fmt.Println("--------")

		// 标题和描述最长25个汉字左右
		tmpTitle := utils.InterceptStringWith3Dots(mhDataItem.Title, 17)
		tmpDescription := utils.InterceptStringWith3Dots(mhDataItem.Description, 22)

		// bid native obj
		hupuRespBidNativeMap := map[string]interface{}{}
		if reqImpID == 1 {
			if isVideoType == 0 {
				hupuRespBidNativeMap["title"] = tmpDescription
				hupuRespBidNativeMap["brand"] = tmpTitle

				// image
				hupuRespBidNativeImgMap := map[string]interface{}{}
				hupuRespBidNativeImgMap["url"] = mhDataItem.Image[0].URL
				hupuRespBidNativeImgMap["width"] = mhDataItem.Image[0].Width
				hupuRespBidNativeImgMap["height"] = mhDataItem.Image[0].Height
				hupuRespBidNativeImgMap["type"] = "jpg"
				hupuRespBidNativeMap["image"] = hupuRespBidNativeImgMap
			} else {
				continue
			}
		} else if reqImpID == 2 {
			if isVideoType == 0 {
				hupuRespBidNativeMap["title"] = tmpDescription
				hupuRespBidNativeMap["brand"] = tmpTitle

				// image
				hupuRespBidNativeImgMap := map[string]interface{}{}
				hupuRespBidNativeImgMap["url"] = mhDataItem.Image[0].URL
				hupuRespBidNativeImgMap["width"] = mhDataItem.Image[0].Width
				hupuRespBidNativeImgMap["height"] = mhDataItem.Image[0].Height
				hupuRespBidNativeImgMap["type"] = "jpg"
				hupuRespBidNativeMap["image"] = hupuRespBidNativeImgMap
			} else if isVideoType == 1 {
				hupuRespBidNativeMap["title"] = tmpDescription
				hupuRespBidNativeMap["brand"] = tmpTitle

				// image
				hupuRespBidNativeImgMap := map[string]interface{}{}
				hupuRespBidNativeImgMap["url"] = mhDataItem.Video.CoverURL
				hupuRespBidNativeImgMap["type"] = "jpg"
				hupuRespBidNativeMap["image"] = hupuRespBidNativeImgMap
			}
		} else if reqImpID == 3 {
			// video obj
			hupuRespBidNativeMap["title"] = tmpDescription
			hupuRespBidNativeMap["brand"] = tmpTitle

			// video
			hupuRespBidNativeVideoMap := map[string]interface{}{}
			hupuRespBidNativeVideoMap["url"] = mhDataItem.Video.VideoURL
			hupuRespBidNativeVideoMap["width"] = mhDataItem.Video.Width
			hupuRespBidNativeVideoMap["height"] = mhDataItem.Video.Height
			hupuRespBidNativeVideoMap["type"] = "mp4"
			hupuRespBidNativeMap["video"] = hupuRespBidNativeVideoMap

			// image
			hupuRespBidNativeImgMap := map[string]interface{}{}
			hupuRespBidNativeImgMap["url"] = mhDataItem.Video.CoverURL
			hupuRespBidNativeImgMap["type"] = "jpg"
			hupuRespBidNativeMap["image"] = hupuRespBidNativeImgMap
		} else {
			continue
		}

		// bid nativie link
		hupuRespBidNativeLinkMap := map[string]interface{}{}
		// hupuRespBidNativeLinkMap["ldp"] = mhDataItem.AdURL
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				hupuRespBidNativeLinkMap["dp"] = mhDataItem.DeepLink
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					hupuRespBidNativeLinkMap["dp"] = mhDataItem.MarketURL
				}
			}

			hupuRespBidNativeLinkMap["ldptype"] = 0
			hupuRespBidNativeLinkMap["ldp"] = mhDataItem.LandpageURL
		} else if mhDataItem.InteractType == 1 {
			if len(mhDataItem.MarketURL) > 0 {
				hupuRespBidNativeLinkMap["dp"] = mhDataItem.MarketURL
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					hupuRespBidNativeLinkMap["dp"] = mhDataItem.DeepLink
				}
			}

			hupuRespBidNativeLinkMap["ldptype"] = 2
			hupuRespBidNativeLinkMap["ldp"] = mhDataItem.DownloadURL
		} else {
			continue
		}
		if reqOs == "ios" {
			hupuRespBidNativeLinkMap["ldptype"] = 1
		}

		hupuRespBidNativeMap["link"] = hupuRespBidNativeLinkMap

		// bid ext obj
		hupuRespBidExtMap := map[string]interface{}{}
		hupuRespBidExtMap["pm"] = impTrackArray
		hupuRespBidExtMap["cm"] = clkTrackArray

		if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
			var deepLinkTrackOKArray []string
			var deepLinkTrackFailedArray []string
			for _, trackItem := range mhDataItem.ConvTracks {
				if trackItem.ConvType == 10 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
					}
				} else if trackItem.ConvType == 11 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
					}
				}
			}
			if len(deepLinkTrackOKArray) > 0 {
				hupuRespBidExtMap["lm"] = deepLinkTrackOKArray
			}
		}
		if reqImpID == 3 {
			// video start finish link
			var videoStartTrackArray []string
			var videoFinishTrackArray []string
			if isVideoType == 1 {
				for _, trackItem := range mhDataItem.Video.EventTracks {
					if trackItem.EventType == 100 {
						for _, trackEventItem := range trackItem.EventURLS {
							videoStartTrackArray = append(videoStartTrackArray, trackEventItem)
						}
					} else if trackItem.EventType == 103 {
						for _, trackEventItem := range trackItem.EventURLS {
							videoFinishTrackArray = append(videoFinishTrackArray, trackEventItem)
						}
					}
				}
			}
			hupuRespBidExtTmArrayMap := []map[string]interface{}{}

			for _, videoStartItem := range videoStartTrackArray {
				hupuRespBidExtTmMap := map[string]interface{}{}
				hupuRespBidExtTmMap["t"] = 1
				hupuRespBidExtTmMap["url"] = videoStartItem
				hupuRespBidExtTmArrayMap = append(hupuRespBidExtTmArrayMap, hupuRespBidExtTmMap)
			}
			hupuRespBidExtMap["tm"] = hupuRespBidExtTmArrayMap
			hupuRespBidExtMap["em"] = videoFinishTrackArray
		}

		if mhDataItem.InteractType == 1 {
			hupuRespBidExtPkdMap := map[string]interface{}{}
			if len(mhDataItem.AppName) > 0 {
				hupuRespBidExtPkdMap["name"] = mhDataItem.AppName
			}
			if len(mhDataItem.IconURL) > 0 {
				hupuRespBidExtPkdMap["icon"] = mhDataItem.IconURL
			}
			if len(mhDataItem.PackageName) > 0 {
				hupuRespBidExtPkdMap["package_name"] = mhDataItem.PackageName
			}
			if len(mhDataItem.Publisher) > 0 {
				hupuRespBidExtPkdMap["developer"] = mhDataItem.Publisher
			}
			if len(mhDataItem.AppVersion) > 0 {
				hupuRespBidExtPkdMap["version"] = mhDataItem.AppVersion
			}
			if len(mhDataItem.Permission) > 0 {
				hupuRespBidExtPkdMap["permission_url"] = "https://static.maplehaze.cn/static/permission?info=" + url.QueryEscape(mhDataItem.Permission)
			}
			if len(mhDataItem.PrivacyLink) > 0 {
				hupuRespBidExtPkdMap["privacy_url"] = mhDataItem.PrivacyLink
			}

			hupuRespBidExtMap["pkd"] = hupuRespBidExtPkdMap
		}

		// icon地址，信息流广告必填
		if len(mhDataItem.IconURL) > 0 {
			hupuRespBidExtMap["icon_url"] = mhDataItem.IconURL
		}

		// bid obj
		hupuRespBidMap := map[string]interface{}{}
		hupuRespBidMap["id"] = "maplehaze_id"
		hupuRespBidMap["impid"] = reqImpID
		hupuRespBidMap["crid"] = crid
		hupuRespBidMap["price"] = ecpm
		hupuRespBidMap["native"] = hupuRespBidNativeMap
		hupuRespBidMap["ext"] = hupuRespBidExtMap
		hupuRespBidMap["nurl"] = winURL

		// deal id
		if len(reqImpItem.Pmp.Deal) > 0 {
			hupuRespBidMap["dealid"] = reqImpItem.Pmp.Deal[0].ID
		}

		hupuRespBidArrayMap = append(hupuRespBidArrayMap, hupuRespBidMap)
	}

	if len(hupuRespBidArrayMap) == 0 {
		return jsonHupuNoBidReturn("not fill")
	}

	// bid
	hupuRespBidObjMap := map[string]interface{}{}
	hupuRespBidObjMap["bid"] = hupuRespBidArrayMap

	// seat id
	hupuRespSeatIDArrayMap := []map[string]interface{}{}
	hupuRespSeatIDArrayMap = append(hupuRespSeatIDArrayMap, hupuRespBidObjMap)

	// resp
	hupuRespMap := map[string]interface{}{}
	hupuRespMap["id"] = hupuReq.ID
	hupuRespMap["bidid"] = "maplehaze_id"
	hupuRespMap["seatbid"] = hupuRespSeatIDArrayMap

	return jsonHupuOKBidReturn(&hupuRespMap)
	// return jsonHupuNoBidReturn("end")
}

func jsonHupuOKBidReturn(resp *map[string]interface{}) (*map[string]interface{}, int) {
	return resp, 200
}

func jsonHupuNoBidReturn(reason string) (*map[string]interface{}, int) {
	// fmt.Println(reason)

	// c.JSON(204, nil)
	return nil, 204
}

func getHupuAppList(appListStr string) []int {
	if len(appListStr) == 0 {
		return []int{}
	}

	appIDList := strings.Split(appListStr, ",")
	var appListIdArray []int
	for _, appId := range appIDList {
		if v, ok := hupuAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var hupuAppListCodeMap = map[string]int{
	"0":  1019,
	"1":  1005,
	"2":  1002,
	"3":  1001,
	"4":  1035,
	"5":  1033,
	"6":  1006,
	"7":  1011,
	"8":  1023,
	"10": 1020,
	"11": 1043,
	"14": 1018,
	"15": 1045,
	"18": 1017,
	"19": 1004,
	"21": 1003,
	"24": 1014,
	"25": 1012,
	"26": 1047,
	"27": 1009,
	"30": 1008,
	"37": 1046,
	"38": 1039,
	"39": 1021,
	"44": 1042,
	"49": 1031,
	"50": 1013,
	"56": 1030,
	"63": 1037,
	"65": 1016,
	"67": 1044,
	"68": 1029,
	"76": 1026,
	"78": 1040}

// HupuReq ...
type HupuReq struct {
	ID     string        `json:"id"`
	App    HupuReqApp    `json:"app"`
	Device HupuReqDevice `json:"device"`
	Imp    []HupuReqImp  `json:"imp"`
}

// HupuReqApp ...
type HupuReqApp struct {
	Name    string `json:"name"`
	Bundle  string `json:"bundle"`
	Content string `json:"content"`
}

// HupuReqDevice ...
type HupuReqDevice struct {
	UA             string `json:"ua"`
	IP             string `json:"ip"`
	DeviceType     int    `json:"devicetype"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	Os             string `json:"os"`
	Osv            string `json:"osv"`
	ScreenWidth    int    `json:"screenwidth"`
	ScreenHeight   int    `json:"screenheight"`
	Carrier        string `json:"carrier"`
	ConnectionType int    `json:"connectiontype"`
	Mac            string `json:"mac"`
	Imei           string `json:"imei"`
	AndroidID      string `json:"androidid"`
	Oaid           string `json:"oaid"`
	Idfa           string `json:"idfa"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec"`
	Country            string `json:"country"`
	Language           string `json:"language"`
	DeviceNameMd5      string `json:"device_name_md5"`
	HardwareMachine    string `json:"hardware_machine"`
	HardwareModel      string `json:"hardware_model"`
	PhysicalMemoryByte string `json:"physical_memory_byte"`
	HarddiskSizeByte   string `json:"harddisk_size_byte"`
	SystemUpdateSec    string `json:"system_update_sec"`
	TimeZone           string `json:"time_zone"`
	BootMark           string `json:"boot_mark"`
	UpdateMark         string `json:"update_mark"`
	HMSCoreVersion     string `json:"hms_version"`
	AgVersion          string `json:"ag_version"`
	AgCountryCode      string `json:"ag_country_code"`
	OppoStoreVersion   int    `json:"oppo_store_version"`
	VivoStoreVersion   int    `json:"vivo_store_version"`
	MiStoreVersion     string `json:"mi_store_version"`
	MiUiStoreVersion   string `json:"miui_version"`
	MntID              string `json:"mnt_id"`
	DeviceFileTime     string `json:"device_file_time"`
	NewCaid            string `json:"newCaid"`
	OldCaid            string `json:"oldCaid"`
	CaidNewVersion     string `json:"caidNewVersion"`
	CaidOldVersion     string `json:"caidOldVersion"`
	LogicNum           string `json:"logicNum"`
}

// HupuReqImp ...
type HupuReqImp struct {
	ID       int              `json:"id"`
	TagID    int              `json:"tagid"`
	BidFloor string           `json:"bidfloor"`
	Native   HupuReqImpNative `json:"native"`
	Pmp      HupuReqImpPmp    `json:"pmp"`
}

// HupuReqImpNative ...
type HupuReqImpNative struct {
	Title HupuReqImpNativeTitle `json:"title"`
	Image HupuReqImpNativeImage `json:"image_url"`
	Video HupuReqImpNativeVideo `json:"video_url"`
}

// HupuReqImpNativeTitle ...
type HupuReqImpNativeTitle struct {
	Len int `json:"len"`
}

// HupuReqImpNativeImage ...
type HupuReqImpNativeImage struct {
	W     int      `json:"w"`
	H     int      `json:"h"`
	Mimes []string `json:"mimes"`
}

// HupuReqImpNativeVideo ...
type HupuReqImpNativeVideo struct {
	W           int      `json:"w"`
	H           int      `json:"h"`
	MinDuration int      `json:"minduration"`
	MaxDuration int      `json:"maxduration"`
	Mimes       []string `json:"mimes"`
}

// HupuReqImpPmp ...
type HupuReqImpPmp struct {
	Deal []HupuReqImpPmpDeal `json:"deals"`
}

// HupuReqImpPmpDeal ...
type HupuReqImpPmpDeal struct {
	ID       string `json:"id"`
	BidFloor string `json:"bidfloor"`
}
