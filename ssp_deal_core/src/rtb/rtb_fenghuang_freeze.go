package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/ifeng"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByFengHuang ...
func HandleByFengHuang(c *gin.Context, channel string) (*ifeng.BidResponse, int) {
	bodyContent, err := c.GetRawData()

	fenghuangReq := &ifeng.BidRequest{}
	err = proto.Unmarshal(bodyContent, fenghuangReq)
	if err != nil {
		fmt.Println(err)
		return jsonFengHuangNoBidReturn("parser error")
	}

	tmpReqByte, _ := json.Marshal(fenghuangReq)
	fmt.Println("fenghuang req: " + string(tmpReqByte))

	reqOs := ""
	if strings.ToLower(fenghuangReq.Device.Os) == "android" {
		reqOs = "android"
	} else if strings.ToLower(fenghuangReq.Device.Os) == "ios" {
		reqOs = "ios"
	} else {
		return jsonFengHuangNoBidReturn("wrong os")
	}

	reqDeivceMake := fenghuangReq.Device.Make
	if reqOs == "ios" {
		reqDeivceMake = "Apple"
	}

	if len(fenghuangReq.Device.Ua) == 0 {
		return jsonFengHuangNoBidReturn("wrong ua")
	}

	reqConnectType := 0
	if fenghuangReq.Device.Connectiontype == 0 {
		reqConnectType = 0
	} else if fenghuangReq.Device.Connectiontype == 1 {
		reqConnectType = 1
	} else if fenghuangReq.Device.Connectiontype == 2 {
		reqConnectType = 2
	} else if fenghuangReq.Device.Connectiontype == 3 {
		reqConnectType = 3
	} else if fenghuangReq.Device.Connectiontype == 4 {
		reqConnectType = 4
	} else if fenghuangReq.Device.Connectiontype == 5 {
		reqConnectType = 7
	} else {
		reqConnectType = 0
	}

	reqCarrier := 0
	if fenghuangReq.Device.Carrier == 1 {
		reqCarrier = 1
	} else if fenghuangReq.Device.Carrier == 2 {
		reqCarrier = 2
	} else if fenghuangReq.Device.Carrier == 3 {
		reqCarrier = 3
	} else {
		reqCarrier = 0
	}

	if len(fenghuangReq.Imp) == 0 {
		return jsonFengHuangNoBidReturn("wrong imp")
	}

	var reqOKImps []*ifeng.BidRequest_Imp
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range fenghuangReq.Imp {
		// fmt.Println(item)
		// fmt.Println(item.GetTagId())
		reqTagID := ""
		if reqOs == "android" {
			reqTagID = item.Tagid
		} else if reqOs == "ios" {
			reqTagID = item.Tagid
		} else {
			continue
		}
		reqPrice := item.Bidfloor

		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagID(c, channel, reqTagID, reqOs, "", "", int(reqPrice))

		if rtbConfigArrayByTagID == nil {
			continue
		}

		// fmt.Println("xxxxxx")
		// fmt.Println(len(*rtbConfigArrayByTagID))
		// fmt.Println(rtbConfigArrayByTagID)
		// fmt.Println("yyyyyy")
		if len(*rtbConfigArrayByTagID) == 0 {
			return jsonFengHuangNoBidReturn("not active")
		}

		reqRtbConfig = (*rtbConfigArrayByTagID)[0]
		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 {
		return jsonFengHuangNoBidReturn("wrong imp")
	}

	// fmt.Println(reqRtbConfig)
	// for test
	// reqRtbConfig.LocalAppID = "10012"
	// reqRtbConfig.LocalPosID = "55889"
	// for test
	// fmt.Println(reqRtbConfig)

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: fenghuangReq.App.Bundle,
			AppName:     fenghuangReq.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: len(reqOKImps),
			Width:   640,
			Height:  960,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    fenghuangReq.Device.Osv,
			Model:        fenghuangReq.Device.Model,
			Manufacturer: reqDeivceMake,
			Imei:         fenghuangReq.Device.Did,
			ImeiMd5:      fenghuangReq.Device.Didmd5,
			AndroidIDMd5: fenghuangReq.Device.Dpidmd5,
			Oaid:         fenghuangReq.Device.Oaid,
			Idfa:         fenghuangReq.Device.Ifa,
			Ua:           fenghuangReq.Device.Ua,
			ScreenWidth:  int(fenghuangReq.Device.W),
			ScreenHeight: int(fenghuangReq.Device.H),
			DeviceType:   1,
			IP:           fenghuangReq.Device.Ip,
			BootMark:     fenghuangReq.Device.BootMark,
			UpdateMark:   fenghuangReq.Device.UpdateMark,
			// MacMd5:       fenghuangReq.Device.Macmd5,
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	// fmt.Println(mhResp.Ret)

	// fmt.Println(mhResp.Data)

	if mhResp.Ret != 0 {
		return jsonFengHuangNoBidReturn("no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return jsonFengHuangNoBidReturn("no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	fenghuangRespSeatBid := ifeng.BidResponse_SeatBid{}

	for i := 0; i < len(reqOKImps); i++ {
		if i > mhRespCount-1 {
			continue
		}
		mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[i]
		fenghuangReqImpItem := reqOKImps[i]
		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(reqRtbConfig.Price) + "&channel=" + channel + "&price=${AUCTION_PRICE}" + "&log=" + url.QueryEscape(mhDataItem.Log)
		var winArray []string
		winArray = append(winArray, winURL)

		// crid
		crid := utils.GetMd5(mhDataItem.Title)
		if len(mhDataItem.Crid) > 0 {
			crid = mhDataItem.Crid
		}

		// fmt.Println("xxx")
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1
			continue
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			impTrackArray = append(impTrackArray, impItem)
		}

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			tmpClkItem := clkItem
			tmpClkItem = strings.Replace(tmpClkItem, "__DOWN_X__", "__CLICK_DOWN_X__", -1)
			tmpClkItem = strings.Replace(tmpClkItem, "__DOWN_Y__", "__CLICK_DOWN_Y__", -1)
			tmpClkItem = strings.Replace(tmpClkItem, "__UP_X__", "__CLICK_UP_X__", -1)
			tmpClkItem = strings.Replace(tmpClkItem, "__UP_Y__", "__CLICK_UP_Y__", -1)
			clkTrackArray = append(clkTrackArray, tmpClkItem)
		}

		fenghuangRespBid := ifeng.BidResponse_SeatBid_Bid{
			Id:     bigdataUID,
			Impid:  fenghuangReqImpItem.Id,
			Price:  int64(reqRtbConfig.Price),
			Adtype: fenghuangReqImpItem.Adtype,
			Aimps:  impTrackArray,
			Aclks:  clkTrackArray,
			Nurl:   winURL,
		}

		// creative
		respCreativeType := "I_IMAGE_TEXT_FLOW"
		for _, nativeItem := range fenghuangReqImpItem.Native {
			if nativeItem.Type == "O_FULL_SCREEN" || nativeItem.Type == "I_SINGLE_IMAGE_FLOW" || nativeItem.Type == "B_ONLY_IMAGE" || nativeItem.Type == "T_ONLY_IMAGE" {
				respCreativeType = nativeItem.Type
			}
		}
		fenghuangRespBidCreative := ifeng.BidResponse_SeatBid_Bid_Creative{
			Id:    crid,
			Type:  respCreativeType,
			Title: mhDataItem.Title,
			Desc:  mhDataItem.Description,
			Text:  mhDataItem.Description,
		}
		if isVideoType == 0 {
			fenghuangRespBidCreative.Images = append(fenghuangRespBidCreative.Images, mhDataItem.Image[0].URL)

		} else {
			fenghuangRespBidCreative.Images = append(fenghuangRespBidCreative.Images, mhDataItem.Video.CoverURL)
		}

		if len(mhDataItem.IconURL) > 0 {
			fenghuangRespBidCreative.Icon = mhDataItem.IconURL
		}

		if len(mhDataItem.LandpageURL) > 0 {
			fenghuangRespBidCreative.Landpage = mhDataItem.LandpageURL
		}

		if len(mhDataItem.DeepLink) > 0 {
			fenghuangRespBidCreative.Dplurl = mhDataItem.DeepLink

			var deepLinkTrackOKArray []string
			var deepLinkTrackFailedArray []string
			for _, trackItem := range mhDataItem.ConvTracks {
				if trackItem.ConvType == 10 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackOKArray = append(deepLinkTrackOKArray, trackEventItem)
					}
				} else if trackItem.ConvType == 11 {
					for _, trackEventItem := range trackItem.ConvURLS {
						deepLinkTrackFailedArray = append(deepLinkTrackFailedArray, trackEventItem)
					}
				}
			}
			if len(deepLinkTrackOKArray) > 0 {
				fenghuangRespBidCreative.Dpsurls = deepLinkTrackOKArray
			}
		}

		if mhDataItem.InteractType == 1 {
			fenghuangRespBidCreative.Daction = 0

			// creative app
			fenghuangRespBidCreativeApp := ifeng.BidResponse_SeatBid_Bid_Creative_App{
				Name:    mhDataItem.AppName,
				Dlurl:   mhDataItem.DownloadURL,
				Package: mhDataItem.PackageName,
			}

			if reqOs == "ios" {
				if strings.Contains(mhDataItem.DownloadURL, "apple.com") {
					tmpArray := strings.SplitN(mhDataItem.DownloadURL, "/id", 2)
					tmpArray = strings.SplitN(tmpArray[1], "?", 2)
					fenghuangRespBidCreativeApp.Appid = tmpArray[0]
				}
			}
			fenghuangRespBidCreative.App = &fenghuangRespBidCreativeApp
		}

		fenghuangRespBid.Creative = &fenghuangRespBidCreative

		fenghuangRespSeatBid.Bid = append(fenghuangRespSeatBid.Bid, &fenghuangRespBid)
	}

	if len(fenghuangRespSeatBid.Bid) == 0 {
		return jsonFengHuangNoBidReturn("not fill")
	}

	fenghuangResp := &ifeng.BidResponse{
		Id:    fenghuangReq.Id,
		Bidid: bigdataUID,
	}

	fenghuangResp.Seatbid = append(fenghuangResp.Seatbid, &fenghuangRespSeatBid)

	tmpRespByte, _ := json.Marshal(fenghuangResp)
	fmt.Println("fenghuang resp: " + string(tmpRespByte))

	return fenghuangResp, 200
}

func jsonFengHuangNoBidReturn(reason string) (*ifeng.BidResponse, int) {
	fmt.Println("fenghuang no bid reason: " + reason)

	// c.JSON(204, nil)
	return nil, 204
}
