package rtb_qimao

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mh_proxy/models"
	"mh_proxy/pb/qimao"
	"mh_proxy/utils"
	"net/http"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"

	"google.golang.org/protobuf/proto"
)

func TestQimaoTask(t *testing.T) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println(err)
		}
	}()

	channel := "42"
	timeout := 8000 // 默认800ms
	reqRtbConfig := models.RtbConfigByTagIDStu{}
	req := &qimao.PriceRequest{}
	deviceOs := "android"
	styleIds := []string{"1", "2", "3"}

	c := &gin.Context{Request: &http.Request{}}
	// 创建一个带超时的context
	ctx, cancel := context.WithTimeout(c.Request.Context(), time.Duration(timeout)*time.Millisecond)
	defer cancel()

	// 创建Gin context的安全副本，并更新其中的Request context
	ctxCopy := c.Copy()
	ctxCopy.Request = ctxCopy.Request.WithContext(ctx)

	// 使用goroutine和channel实现超时控制
	startTime := time.Now()
	resultChan := make(chan *qimao.PriceResponse, 1)
	go func() {
		// 在goroutine中增加recover防止panic影响主流程
		defer func() {
			if err := recover(); err != nil {
				fmt.Printf("qimao task goroutine panic, channel=%v, err=%v\n", channel, err)
				resultChan <- &qimao.PriceResponse{
					Code: 3,
					Msg:  "处理请求异常",
					Data: nil,
				}
			}
		}()
		resultChan <- runQimaoTask(ctxCopy, channel, reqRtbConfig, req, deviceOs, styleIds)
	}()

	// 等待结果或超时
	select {
	case result := <-resultChan:
		fmt.Printf("qimao task complate channel=%v, deviceOs=%v, time=%v\n", channel, deviceOs, time.Since(startTime))
		fmt.Println(result)
	case <-ctx.Done():
		// 请求超时，返回无填充
		fmt.Printf("qimao task timeout return empty, channel=%v, deviceOs=%v, time=%v\n", channel, deviceOs, time.Since(startTime))
	}
}

func TestHandleByQimao(t *testing.T) {
	var materialTypeArray []qimao.PriceRequest_Pos_MaterialType
	materialTypeArray = append(materialTypeArray, qimao.PriceRequest_Pos_ImageWord, qimao.PriceRequest_Pos_VideoWord)

	request := &qimao.PriceRequest{
		RequestId: uuid.NewV4().String(),
		Device: &qimao.PriceRequest_Device{
			Ua:    proto.String("Mozilla/5.0(Linux;Android5.1.1;OPPOR9PlusmABuild/LMY47V;wv)AppleWebKit/537.36(KHTML,likeG ecko)Version/4.0Chrome/55.0.2883.91MobileSafari/537.36"),
			Make:  proto.String("OPPO"),
			Brand: proto.String("Apple"),
			Model: proto.String("iPhone7,1"),
			//ImeiMd5: "f9eae96ea051c772755e33ed34f32e54",
			//Oaid:    proto.String(""),
			//OaidMd5: "f353a7a2bcb37613095a55e5bd994731",
			Idfa:    proto.String("AC79318F-0EBC-4EED-BAE5-6FE92E685052"),
			IdfaMd5: "9957b554fdacdfa11dd8e6e71bd11eb6",
			Os:      2,
			Osv:     proto.String("12"),
			W:       proto.Uint32(1280),
			H:       proto.Uint32(960),
			//Ext:               nil,
			//Idfv:              nil,
			//AndroidId:         nil,
			//Usecret:           nil,
			//Usecretversion:    nil,
			//Preusecret:        nil,
			//Preusecretversion: nil,
			//Mac:               nil,
			//Hwoaid:            nil,
			//Dpi:               nil,
			//BootMark:          "",
			//UpdateMark:        "",
			//PddBootMark:       "",
			//PddUpdateMark:     "",
			//BirthTime:         "",
			//AppStoreVersion:   "",
			//HmsCoreVersion:    "",
		},
		Media: &qimao.PriceRequest_Media{
			Appid:  "48029230",
			Name:   "七猫免费小说",
			Bundle: "com.qimao.ad.demo",
		},
		Pos: &qimao.PriceRequest_Pos{
			Id:           "qimao333",
			Floor:        1,
			AdType:       qimao.AdType_Banner.Enum(),
			W:            620,
			H:            120,
			MaterialType: materialTypeArray,
		},
		User: &qimao.PriceRequest_User{
			Pp: "eE4LggEAAAAAAAAAAAAAAAAAAg==",
		},
		Network: &qimao.PriceRequest_Network{
			Carrier:        qimao.PriceRequest_Network_CarrierChinaMobile.Enum(),
			ConnectionType: qimao.PriceRequest_Network_ConnTypeCellular4g.Enum(),
		},
		Ip:         "**************",
		ReqSegment: 2,
	}

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/request?channel=42", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	qmResp := &qimao.PriceResponse{}
	err = proto.Unmarshal(bodyContent, qmResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(qmResp)
	fmt.Println(string(marshal))
}

func TestHandleGetAd(t *testing.T) {
	var materialTypeArray []qimao.PriceRequest_Pos_MaterialType
	materialTypeArray = append(materialTypeArray, qimao.PriceRequest_Pos_ImageWord, qimao.PriceRequest_Pos_VideoWord)

	adxSecret := "6f89f298213415c9b656fc61bccaf0d6"
	timeMicro := time.Now().UnixMicro()
	ts := strconv.FormatInt(timeMicro, 10)
	request := &qimao.AdRequest{
		RequestId: uuid.NewV4().String(),
		Token:     "9a08bcb6-0fb0-433a-bf7b-687d7ac032c0",
		Ts:        ts,
	}
	tmpSign := utils.GetMd5(utils.GetMd5(request.RequestId+utils.GetMd5(request.Token)) + adxSecret + request.Ts)
	request.Sign = tmpSign

	client := &http.Client{}
	reqPbByte, _ := proto.Marshal(request)
	requestPost, _ := http.NewRequest("POST", "http://sandbox.maplehaze.cn/rtb/qimao/getAd", bytes.NewReader(reqPbByte))
	requestPost.Header.Add("Content-Type", "application/octet-stream")
	requestPost.Header.Add("Connection", "keep-alive")

	resp, err := client.Do(requestPost)
	if err != nil {
		fmt.Println(err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	bodyContent, err := io.ReadAll(resp.Body)
	qmResp := &qimao.AdResponse{}
	err = proto.Unmarshal(bodyContent, qmResp)
	if err != nil {
		fmt.Println(err)
	}
	marshal, _ := json.Marshal(qmResp)
	fmt.Println(string(marshal))
}

func TestHandlePriceDecode(t *testing.T) {
	src := "Ak/oVHwFGQA22uEqYLGtqA=="
	key := "abcdefghijklmnop"

	decodeStr, _ := url.QueryUnescape(src)
	fmt.Println(decodeStr)
	crypted, err := Base64URLDecode(decodeStr)
	if err != nil {
		fmt.Println(err)
		return
	}
	price := AesDecrypt(crypted, []byte(key))
	fmt.Println(string(price))
}
