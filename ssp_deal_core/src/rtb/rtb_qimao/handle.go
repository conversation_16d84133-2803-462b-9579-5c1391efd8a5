package rtb_qimao

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"io"
	"mh_proxy/config"
	"net/url"
	"strconv"
	"strings"
	"time"

	"mh_proxy/core"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/pb/qimao"
	"mh_proxy/utils"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

func HandleByQimao(c *gin.Context, channel string) *qimao.PriceResponse {
	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	contentEncoding := c.Get<PERSON>eader("Content-Encoding")
	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	req := &qimao.PriceRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "解码失败",
			Data: nil,
		}
	}

	var deviceOs string
	switch req.GetDevice().GetOs() {
	case qimao.PriceRequest_Device_OsAndroid:
		deviceOs = "android"
	case qimao.PriceRequest_Device_OsIos:
		deviceOs = "ios"
	default:
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "设备类型错误",
			Data: nil,
		}
	}

	pos := req.GetPos()
	if len(pos.GetId()) == 0 {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "广告位为空",
			Data: nil,
		}
	}

	var styleIds []string
	var reqRtbConfig models.RtbConfigByTagIDStu

	tagId := pos.GetId()
	price := pos.GetFloor()

	for _, id := range pos.GetMaterialType() {
		styleId := qimao.PriceRequest_Pos_MaterialType_value[id.Enum().String()]
		idStr := strconv.FormatInt(int64(styleId), 10)
		styleIds = append(styleIds, idStr)
	}

	rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, tagId, deviceOs, styleIds, "", int(price))
	if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "tagId为空",
			Data: nil,
		}
	}

	reqRtbConfig = (*rtbConfigArrayByTagID)[0]

	deviceMake := req.GetDevice().GetBrand()
	if len(deviceMake) == 0 {
		deviceMake = req.GetDevice().GetMake()
	}
	if deviceOs == "ios" {
		deviceMake = "Apple"
	}

	var connectType int
	switch req.GetNetwork().GetConnectionType() {
	case qimao.PriceRequest_Network_ConnTypeWifi:
		connectType = 1
	case qimao.PriceRequest_Network_ConnTypeCellular2g:
		connectType = 2
	case qimao.PriceRequest_Network_ConnTypeCellular3g:
		connectType = 3
	case qimao.PriceRequest_Network_ConnTypeCellular4g:
		connectType = 4
	case qimao.PriceRequest_Network_ConnTypeCellular5g:
		connectType = 7
	case qimao.PriceRequest_Network_ConnTypeUnknown:
		connectType = 0
	default:
		connectType = 1
	}

	var carrier int
	switch req.GetNetwork().GetCarrier() {
	case qimao.PriceRequest_Network_CarrierChinaMobile:
		carrier = 1
	case qimao.PriceRequest_Network_CarrierChinaUnicom:
		carrier = 2
	case qimao.PriceRequest_Network_CarrierChinaTelecom:
		carrier = 3
	default:
		carrier = 0
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(req.GetDevice().GetUsecret()) > 0 && len(req.GetDevice().GetUsecretversion()) > 0 {
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = req.GetDevice().GetUsecret()
		caidMulti.CAIDVersion = req.GetDevice().GetUsecretversion()
		caidMultiList = append(caidMultiList, caidMulti)
	}
	if len(req.GetDevice().GetPreusecret()) > 0 && len(req.GetDevice().GetPreusecretversion()) > 0 {
		var preCaidMulti models.MHReqCAIDMulti
		preCaidMulti.CAID = req.GetDevice().GetPreusecret()
		preCaidMulti.CAIDVersion = req.GetDevice().GetPreusecretversion()
		caidMultiList = append(caidMultiList, preCaidMulti)
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: req.GetMedia().GetBundle(),
			AppName:     req.GetMedia().GetName(),
			AppType:     4, // 4 - RTB
		},
		Pos: models.MHReqPos{
			ID:          utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount:     1,
			Width:       640,
			Height:      960,
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:              deviceOs,
			IP:              req.GetIp(),
			Mac:             req.GetDevice().GetMac(),
			Ua:              req.GetDevice().GetUa(),
			DPI:             float32(req.GetDevice().GetDpi()),
			OsVersion:       req.GetDevice().GetOsv(),
			Model:           req.GetDevice().GetModel(),
			Manufacturer:    deviceMake,
			ImeiMd5:         req.GetDevice().GetImeiMd5(),
			AndroidID:       req.GetDevice().GetAndroidId(),
			Oaid:            req.GetDevice().GetOaid(),
			OaidMd5:         req.GetDevice().GetOaidMd5(),
			Idfa:            req.GetDevice().GetIdfa(),
			IdfaMd5:         req.GetDevice().GetIdfaMd5(),
			ScreenWidth:     int(req.GetDevice().GetW()),
			ScreenHeight:    int(req.GetDevice().GetH()),
			DeviceType:      1,
			BootMark:        req.GetDevice().GetBootMark(),
			UpdateMark:      req.GetDevice().GetUpdateMark(),
			DeviceStartSec:  req.GetDevice().GetBirthTime(),
			AppStoreVersion: req.GetDevice().GetAppStoreVersion(),
			CAIDMulti:       caidMultiList,
			AppList:         getQMAppList(req.GetUser().GetPp()),
		},
		Network: models.MHReqNetwork{
			ConnectType: connectType,
			Carrier:     carrier,
		},
	}
	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "无填充",
			Data: nil,
		}
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "无填充",
			Data: nil,
		}
	}

	mhDataItem := mhResp.Data[reqRtbConfig.LocalPosID].List[0]

	// 统计
	models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

	// 判定上游返回ecpm是否大于底价
	if mhDataItem.Ecpm < reqRtbConfig.Price {
		return &qimao.PriceResponse{
			Code: 3,
			Msg:  "无填充",
			Data: nil,
		}
	}
	ecpm := mhDataItem.Ecpm

	var (
		buttonText string
		action     qimao.AdsAction
	)

	switch mhDataItem.InteractType {
	case 0:
		buttonText = "查看详情"
		action = qimao.AdsAction_Webview
	case 1:
		buttonText = "立即下载"
		action = qimao.AdsAction_Download

	}
	var deeplinkSuccConvUrls []string
	var deeplinkFailConvUrls []string
	if len(mhDataItem.DeepLink) > 0 || len(mhDataItem.MarketURL) > 0 {
		action = qimao.AdsAction_Deeplink
		if len(mhDataItem.ConvTracks) > 0 {
			for _, convTracks := range mhDataItem.ConvTracks {
				if convTracks.ConvType == 10 {
					deeplinkSuccConvUrls = convTracks.ConvURLS
				}
				if convTracks.ConvType == 11 {
					deeplinkFailConvUrls = convTracks.ConvURLS
				}
			}
		}
	}

	ads := &qimao.PriceResponse_Data_Ads{
		Action:     action,
		TargetUrl:  mhDataItem.LandpageURL,
		ButtonText: buttonText,
		Title:      mhDataItem.Title,
		Desc:       mhDataItem.Description,
		Deeplink:   mhDataItem.DeepLink,
		Aid:        mhDataItem.AdID,
		Cid:        mhDataItem.Crid,
	}

	if action == qimao.AdsAction_Download {
		var appDescription string
		if len(mhDataItem.AppInfo) > 0 {
			appDescription = mhDataItem.AppInfo
		}
		if len(appDescription) == 0 && len(mhDataItem.AppInfoURL) > 0 {
			appDescription = mhDataItem.AppInfoURL
		}

		var appInfo qimao.PriceResponse_Data_Ads_AppInfo
		appInfo.AppName = mhDataItem.AppName
		appInfo.AppPermission = mhDataItem.Permission
		appInfo.AppPrivacy = mhDataItem.PrivacyLink
		appInfo.AppVersion = mhDataItem.AppVersion
		appInfo.Developer = mhDataItem.Publisher
		appInfo.DownloadUrl = mhDataItem.DownloadURL
		appInfo.PackageName = mhDataItem.PackageName
		appInfo.AppDescription = appDescription

		ads.AppInfo = &appInfo
	}

	switch mhDataItem.InteractType {
	case 0:
		if len(mhDataItem.DeepLink) > 0 {
			ads.Deeplink = mhDataItem.DeepLink
		} else {
			if len(mhDataItem.MarketURL) > 0 {
				ads.Deeplink = mhDataItem.MarketURL
			}
		}
	case 1:
		if len(mhDataItem.MarketURL) > 0 {
			ads.Deeplink = mhDataItem.MarketURL
		} else {
			if len(mhDataItem.DeepLink) > 0 {
				ads.Deeplink = mhDataItem.DeepLink
			}
		}
	}

	flag := 0
	switch mhDataItem.CrtType {
	case 11:
		for _, styleId := range styleIds {
			if styleId == "1" {
				flag = 1
				break
			}
		}
		if flag == 0 {
			return &qimao.PriceResponse{
				Code: 3,
				Msg:  "无填充",
				Data: nil,
			}
		}
		var images []*qimao.PriceResponse_Data_Ads_Image
		for _, imageItem := range mhDataItem.Image {
			image := &qimao.PriceResponse_Data_Ads_Image{
				Url: imageItem.URL,
				W:   uint32(imageItem.Width),
				H:   uint32(imageItem.Height),
			}
			images = append(images, image)
		}
		ads.Image = images
	case 20:
		for _, styleId := range styleIds {
			if styleId == "2" {
				flag = 1
				break
			}
		}
		if flag == 0 {
			return &qimao.PriceResponse{
				Code: 3,
				Msg:  "无填充",
				Data: nil,
			}
		}
		var video qimao.PriceResponse_Data_Ads_Video
		video.CoverUrl = mhDataItem.Video.CoverURL
		video.Duration = uint32(mhDataItem.Video.Duration / 1000)
		video.H = uint32(mhDataItem.Video.Height)
		video.W = uint32(mhDataItem.Video.Width)
		video.Url = mhDataItem.Video.VideoURL

		ads.Video = &video
	}

	icon := &qimao.PriceResponse_Data_Ads_Icon{
		Url: mhDataItem.IconURL,
		W:   uint32(512),
		H:   uint32(512),
	}

	ads.Icon = icon

	winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__PRICE__" + "&auction={AUCTION}" + "&log=" + url.QueryEscape(mhDataItem.Log)

	var lossURL string
	if len(mhDataItem.LossNoticeURL) > 0 {
		lossURL = mhDataItem.LossNoticeURL + "&auction={AUCTION}"
	}

	trackUrls := &qimao.PriceResponse_Data_TrackUrls{
		ExposeUrls:      mhDataItem.ImpressionLink,
		ClickUrls:       mhDataItem.ClickLink,
		DeeplinkFail:    deeplinkFailConvUrls,
		DeeplinkSuccess: deeplinkSuccConvUrls,
	}

	if req.GetReqSegment() == 2 {
		cacheKey := "rtb_qimao_get_ad:" + bigdataUID
		cacheData := &qimao.PriceResponse_Data{
			Token:     bigdataUID,
			Ads:       ads,
			TrackUrls: trackUrls,
		}
		cacheVal, _ := json.Marshal(cacheData)
		_, err := db.GlbRedis.Set(c, cacheKey, cacheVal, 1*time.Minute).Result()
		if err != nil {
			return &qimao.PriceResponse{
				Code: 0,
				Msg:  "设置缓存失败",
				Data: nil,
			}
		}
	}

	data := &qimao.PriceResponse_Data{
		RequestId: req.GetRequestId(),
		Lnurl:     []string{winURL, lossURL},
		Price:     int32(ecpm),
		Token:     bigdataUID,
		Ads:       ads,
		TrackUrls: trackUrls,
	}

	return &qimao.PriceResponse{
		Code: 0,
		Msg:  "成功",
		Data: data,
	}
}

func HandleByQimaoAd(c *gin.Context) *qimao.AdResponse {
	bodyContent, _ := c.GetRawData()

	// 判断是否开启gzip
	contentEncoding := c.GetHeader("Content-Encoding")

	if strings.Contains(contentEncoding, "gzip") {
		buf := bytes.NewBuffer(bodyContent)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ = io.ReadAll(reader)
	}

	req := &qimao.AdRequest{}
	err := proto.Unmarshal(bodyContent, req)
	if err != nil {
		return &qimao.AdResponse{
			Code: 3,
			Msg:  proto.String("解码失败"),
			Data: nil,
		}
	}

	adxSecret := "6f89f298213415c9b656fc61bccaf0d6"
	sign := req.GetSign()
	tmpSign := utils.GetMd5(utils.GetMd5(req.GetRequestId()+utils.GetMd5(req.GetToken())) + adxSecret + req.GetTs())

	if sign != tmpSign {
		return &qimao.AdResponse{
			Code: 3,
			Msg:  proto.String("sign校验失败"),
			Data: nil,
		}
	}

	priceData := &qimao.PriceResponse_Data{}
	cacheKey := "rtb_qimao_get_ad:" + req.GetToken()
	cacheVal, err := db.GlbRedis.Get(c, cacheKey).Result()
	if err != nil {
		return &qimao.AdResponse{
			Code: 3,
			Msg:  proto.String("获取缓存失败"),
			Data: nil,
		}
	}

	err = json.Unmarshal([]byte(cacheVal), priceData)
	if err != nil {
		return &qimao.AdResponse{
			Code: 3,
			Msg:  proto.String("缓存解码失败"),
			Data: nil,
		}
	}

	adData := &qimao.AdResponse_Data{
		RequestId: req.GetRequestId(),
		Aid:       priceData.GetAds().GetAid(),
		Cid:       priceData.GetAds().GetCid(),
		Token:     req.GetToken(),
		Ads: &qimao.AdResponse_Data_Ads{
			Action:     priceData.GetAds().GetAction(),
			TargetUrl:  priceData.GetAds().GetTargetUrl(),
			ButtonText: priceData.GetAds().GetButtonText(),
			Title:      priceData.GetAds().GetTitle(),
			Desc:       priceData.GetAds().GetDesc(),
			Icon: &qimao.AdResponse_Data_Ads_Icon{
				Url: priceData.GetAds().GetIcon().GetUrl(),
				W:   priceData.GetAds().GetIcon().GetW(),
				H:   priceData.GetAds().GetIcon().GetH(),
			},
			Deeplink: priceData.GetAds().GetDeeplink(),
		},
		TrackUrls: &qimao.AdResponse_Data_TrackUrls{
			ExposeUrls:      priceData.GetTrackUrls().GetExposeUrls(),
			ClickUrls:       priceData.GetTrackUrls().GetClickUrls(),
			DeeplinkSuccess: priceData.GetTrackUrls().GetDeeplinkSuccess(),
			DeeplinkFail:    priceData.GetTrackUrls().GetDeeplinkFail(),
		},
	}

	if priceData.GetAds().GetImage() != nil {
		var images []*qimao.AdResponse_Data_Ads_Image
		var image qimao.AdResponse_Data_Ads_Image
		for _, img := range priceData.GetAds().GetImage() {
			image.Url = img.GetUrl()
			image.W = img.GetW()
			image.H = img.GetH()
			images = append(images, &image)
		}
		adData.Ads.Image = images
	}

	if priceData.GetAds().GetVideo() != nil {
		var video qimao.AdResponse_Data_Ads_Video
		video.CoverUrl = priceData.GetAds().GetVideo().GetCoverUrl()
		video.Duration = priceData.GetAds().GetVideo().GetDuration()
		video.H = priceData.GetAds().GetVideo().GetH()
		video.W = priceData.GetAds().GetVideo().GetW()
		video.Size = priceData.GetAds().GetVideo().GetSize()
		video.Url = priceData.GetAds().GetVideo().GetUrl()

		adData.Ads.Video = &video
	}

	if priceData.GetAds().GetAppInfo() != nil {
		var appInfo qimao.AdResponse_Data_Ads_AppInfo
		appInfo.AppName = priceData.GetAds().GetAppInfo().GetAppName()
		appInfo.AppPermission = priceData.GetAds().GetAppInfo().GetAppPermission()
		appInfo.AppPrivacy = priceData.GetAds().GetAppInfo().GetAppPrivacy()
		appInfo.AppSize = priceData.GetAds().GetAppInfo().GetAppSize()
		appInfo.AppVersion = priceData.GetAds().GetAppInfo().GetAppVersion()
		appInfo.Developer = priceData.GetAds().GetAppInfo().GetDeveloper()
		appInfo.DownloadUrl = priceData.GetAds().GetAppInfo().GetDownloadUrl()
		appInfo.PackageName = priceData.GetAds().GetAppInfo().GetPackageName()
		appInfo.AppDescription = priceData.GetAds().GetAppInfo().GetAppDescription()

		adData.Ads.AppInfo = &appInfo
	}

	return &qimao.AdResponse{
		Code: 0,
		Msg:  proto.String("成功"),
		Data: adData,
	}
}

func getQMAppList(appListStr string) []int {
	if len(appListStr) == 0 {
		return []int{}
	}

	var appListIdArray []int
	decode, _ := utils.Base64URLDecode(appListStr)
	decodeLen := len(decode)

	for key, value := range QMAppListCodeMap {
		if decodeLen <= key/8 {
			continue
		}
		if decode[key/8]>>(key%8)&1 == 1 {
			appListIdArray = append(appListIdArray, value)
		}
	}

	return appListIdArray
}

var QMAppListCodeMap = map[int]int{
	4:   1001,
	9:   1002,
	17:  1003,
	16:  1004,
	10:  1005,
	25:  1006,
	8:   1008,
	93:  1009,
	95:  1010,
	83:  1011,
	88:  1012,
	33:  1013,
	19:  1014,
	5:   1015,
	26:  1016,
	3:   1017,
	92:  1018,
	2:   1019,
	87:  1020,
	148: 1021,
	163: 1022,
	11:  1023,
	147: 1024,
	0:   1025,
	118: 1026,
	198: 1027,
	111: 1029,
	171: 1030,
	7:   1031,
	18:  1032,
	145: 1033,
	157: 1034,
	31:  1035,
	158: 1036,
	6:   1037,
	32:  1038,
	29:  1040,
	159: 1041,
	146: 1042,
	14:  1043,
	106: 1044,
	86:  1045,
	138: 1046,
	84:  1047,
}
