package rtb_netease

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/rtb"
	"mh_proxy/utils"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// NeteaseRequest Objects
type NeteaseRequestObject struct {
	Id                  string                     `json:"id"`
	Imp                 []NeteaseRequestImpObject  `json:"imp"`
	Device              NeteaseRequestDeviceObject `json:"device"`
	User                NeteaseRequestUserObject   `json:"user"`
	Test                int                        `json:"test"`
	Tmax                int                        `json:"tmax"`
	Wseat               []string                   `json:"wseat"`
	Cur                 []string                   `json:"cur"`
	Bcat                []string                   `json:"bcat"`
	App                 NeteaseRequestAppObject    `json:"app"`
	CoordinateSupported bool                       `json:"coordinate_supported"`
}

type NeteaseRequestImpObject struct {
	Id                    string                  `json:"id"`
	Tagid                 string                  `json:"tagid"`
	Bidfloor              int64                   `json:"bidfloor"`
	Bidfloorcur           string                  `json:"bidfloorcur"`
	Pmp                   NeteaseRequestPmpObject `json:"pmp"`
	Allowstyle            []int                   `json:"allowstyle"`
	InteractModeSupported []int                   `json:"interact_mode_supported"`
}

type NeteaseRequestPmpObject struct {
	PrivateAuction int                         `json:"private_auction"`
	Deals          []NeteaseRequestDealsObject `json:"deals"`
}

type NeteaseRequestDealsObject struct {
	Id          string `json:"id"`
	Bidfloor    int64  `json:"bidfloor"`
	Bidfloorcur string `json:"bidfloorcur"`
	At          int    `json:"at"`
	Allowstyle  []int  `json:"allowstyle"`
}

type NeteaseRequestDeviceObject struct {
	Ua              string                            `json:"ua"`
	Geo             NeteaseRequestGeoObject           `json:"geo"`
	Ip              string                            `json:"ip"`
	Model           string                            `json:"model"`
	Os              string                            `json:"os"`
	OsVersion       string                            `json:"os_version"`
	Carrier         string                            `json:"carrier"`        // NeteaseRequestCarrierEnum.String()
	Connectiontype  string                            `json:"connectiontype"` // NeteaseRequestConnectiontypeEnum.String()
	Idfa            string                            `json:"idfa"`
	IdfaMd5         string                            `json:"idfa_md5"`
	Imei            string                            `json:"imei"`
	ImeiMd5         string                            `json:"imei_md5"`
	Mac             string                            `json:"mac"`
	MacMd5          string                            `json:"mac_md5"`
	Oaid            string                            `json:"oaid"`
	OaidMd5         string                            `json:"oaid_md5"`
	ScreenWidth     int                               `json:"screen_width"`
	ScreenHeight    int                               `json:"screen_height"`
	AndroidId       string                            `json:"android_id"`
	HmsCoreVersion  string                            `json:"hms_core_version"`
	HuaweiAgVersion string                            `json:"huawei_ag_version"`
	Manufacturer    string                            `json:"manufacturer"`
	BootMark        string                            `json:"boot_mark"`
	UpdateMark      string                            `json:"update_mark"`
	Caid            string                            `json:"caid"`
	CaidVersion     string                            `json:"caid_version"`
	AppList         []string                          `json:"app_list"`
	OptDeviceInfo   NeteaseRequestOptDeviceInfoObject `json:"opt_device_info"`
}

type NeteaseRequestOptDeviceInfoObject struct {
	DeviceStartSec     string `json:"startup_time"`
	Country            string `json:"country"`
	Language           string `json:"language"`
	DeviceNameMd5      string `json:"phone_name"`
	HardwareModel      string `json:"device_type"`
	PhysicalMemoryByte string `json:"mem_total"`
	HarddiskSizeByte   string `json:"disk_total"`
	SystemUpdateSec    string `json:"mb_time"`
	TimeZone           string `json:"time_zone"`
}

type NeteaseRequestCarrierEnum int

const (
	NETEASE_CARRIER_CM NeteaseRequestCarrierEnum = iota
	NETEASE_CARRIER_CU
	NETEASE_CARRIER_CT
	NETEASE_CARRIER_UNKNOWN
)

func (carrier NeteaseRequestCarrierEnum) String() string {
	switch carrier {
	case NETEASE_CARRIER_CM:
		return "cm"
	case NETEASE_CARRIER_CU:
		return "cu"
	case NETEASE_CARRIER_CT:
		return "ct"
	}
	return "unknown"
}

type NeteaseRequestConnectiontypeEnum int

const (
	NETEASE_CONNECTIONTYPE_WIFI NeteaseRequestConnectiontypeEnum = iota
	NETEASE_CONNECTIONTYPE_2G
	NETEASE_CONNECTIONTYPE_3G
	NETEASE_CONNECTIONTYPE_4G
	NETEASE_CONNECTIONTYPE_UNKNOWN
)

func (connectiontype NeteaseRequestConnectiontypeEnum) String() string {
	switch connectiontype {
	case NETEASE_CONNECTIONTYPE_WIFI:
		return "wifi"
	case NETEASE_CONNECTIONTYPE_2G:
		return "2G"
	case NETEASE_CONNECTIONTYPE_3G:
		return "3G"
	case NETEASE_CONNECTIONTYPE_4G:
		return "4G"
	}
	return "unknown"
}

type NeteaseResponseEventTrackerTypeEnum int

const (
	NETEASE_EVENTTRACKER_TYPE_UNKNOWN NeteaseResponseEventTrackerTypeEnum = iota
	NETEASE_EVENTTRACKER_TYPE_DOWNLOAD
	NETEASE_EVENTTRACKER_TYPE_DOWNLOAD_FINISH
	NETEASE_EVENTTRACKER_TYPE_DOWNLOAD_SETUP
	NETEASE_EVENTTRACKER_TYPE_DOWNLOAD_OPEN
	NETEASE_EVENTTRACKER_TYPE_CALL_APP
	NETEASE_EVENTTRACKER_TYPE_PLAYSTART
	NETEASE_EVENTTRACKER_TYPE_PLAYPAUSE
	NETEASE_EVENTTRACKER_TYPE_PLAYEND
)

type NeteaseRequestGeoObject struct {
	Lat  string `json:"lat"`
	Lon  string `json:"lon"`
	Type int    `json:"type"`
}

type NeteaseRequestUserObject struct {
	Id string `json:"id"`
}

type NeteaseRequestAppObject struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Ver    string `json:"ver"`
	Bundle string `json:"bundle"`
}

// NeteaseResponse Objects
type NeteaseResponseObject struct {
	Id      string                         `json:"id"`
	Seatbid []NeteaseResponseSeatBidObject `json:"seatbid"`
	Bidid   string                         `json:"bidid"`
	Cur     string                         `json:"cur"`
}

type NeteaseResponseSeatBidObject struct {
	Bid []NeteaseResponseBidObject `json:"bid"`
}

type NeteaseResponseBidObject struct {
	Id             string                              `json:"id"`
	Impid          string                              `json:"impid"`
	Price          int64                               `json:"price"`
	Nurl           string                              `json:"nurl"`
	Crid           string                              `json:"crid"`
	Pvm            []string                            `json:"pvm"`
	Clickm         []string                            `json:"clickm"`
	Dealid         string                              `json:"dealid"`
	Ext            NeteaseResponseExtObject            `json:"ext"`
	EventTracker   []NeteaseResponseEventTrackerObject `json:"eventTracker"`
	InteractMode   int                                 `json:"interact_mode"`
	NeedCoordinate int                                 `json:"need_coordinate"`
}

type NeteaseResponseExtObject struct {
	Title         string                          `json:"title"`
	SubTitle      string                          `json:"subTitle"`
	LinkUrl       string                          `json:"linkUrl"`
	Adm           []NeteaseResponseAdmObject      `json:"adm"`
	Style         int                             `json:"style"`
	AndroidUrl    string                          `json:"androidUrl"`
	IosUrl        string                          `json:"iosUrl"`
	Advertiser    NeteaseResponseAdvertiserObject `json:"advertiser"`
	Macros        []NeteaseResponseMacrosObject   `json:"macros"`
	Deeplink      string                          `json:"deeplink"`
	AppType       int                             `json:"appType"`
	PrgId         string                          `json:"prgId"`
	PrgPath       string                          `json:"prgPath"`
	ActionType    int                             `json:"actionType"`
	Telephone     string                          `json:"telephone"`
	AppMsg        string                          `json:"appMsg"`
	Authority     string                          `json:"authority"`
	PrivacyPolicy string                          `json:"privacyPolicy"`
	Icon          string                          `json:"icon"`
	IsFullScreen  int                             `json:"isFullScreen"`
	AppName       string                          `json:"appName"`
	PackageName   string                          `json:"packageName"`
}

type NeteaseResponseAdmObject struct {
	Url  string `json:"url"`
	Type int    `json:"type"`
	W    int    `json:"w"`
	H    int    `json:"h"`
}

type NeteaseResponseAdvertiserObject struct {
	Id          string `json:"id"`
	Industry    string `json:"industry"`
	SubIndustry string `json:"subIndustry"`
}

type NeteaseResponseMacrosObject struct {
	Macro      string `json:"macro"`
	Value      string `json:"value"`
	Expression []int  `json:"expression"`
}

type NeteaseResponseEventTrackerObject struct {
	Type int    `json:"type"`
	Url  string `json:"url"`
}

func HandleByNetease(c *gin.Context, channel string) (*NeteaseResponseObject, int) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		fmt.Println(err)
		return responseNoBidReturn("get raw data error")
	}

	uuid := uuid.NewV4().String()

	var requestObject NeteaseRequestObject
	err = json.Unmarshal([]byte(bodyContent), &requestObject)

	if err != nil {
		fmt.Println(err)
		return responseNoBidReturn("parser error")
	}

	// // !特殊处理开始
	// // 由于对方70.4及以前的版本UA不标准
	// // 过滤掉 app.ver 小于等于"70.4"的请求
	// // 2022-08-19 添加
	// targetVersion, err := version.NewVersion(requestObject.App.Ver)
	// if err != nil {
	// 	fmt.Println(err)
	// 	return responseNoBidReturn("app.ver error")
	// }
	// comparedVersion, err := version.NewVersion("70.4")
	// if err != nil {
	// 	fmt.Println(err)
	// 	return responseNoBidReturn("compared version error")
	// }
	// if targetVersion.LessThanOrEqual(comparedVersion) {
	// 	return responseNoBidReturn("app.ver is less or equal 70.4")
	// }
	// // !特殊处理结束

	// 2023年11月23日 产品要求去掉这段逻辑

	// 检查Imp是否为空
	if !isValidImp(&requestObject) {
		return responseNoBidReturn("imp is empty")
	}

	// 获取OS字段
	deviceOs, err := getOsString(&requestObject)
	if err != nil {
		return responseNoBidReturn(err.Error())
	}

	// 检查UA
	if !isValidUA(&requestObject) {
		return responseNoBidReturn("invalid ua")
	}

	// 获取制造商
	manufacturer := getManufacturer(&requestObject, deviceOs)

	// 获取网络类型
	connectType := getConnectType(&requestObject)

	// 获取运营商
	carrier := getCarrier(&requestObject)

	resultfulImp, configList := getResultfulImp(&requestObject, c, channel, deviceOs)

	// 判断是否有效填充
	if len(resultfulImp) == 0 || len(configList) == 0 {
		return responseNoBidReturn("resultful imp is empty")
	}

	// 目前仅需取第一个配置
	requestConfig := configList[0]

	// 请求Adx广告
	adxAd := getAdxAd(uuid, &requestObject, c, resultfulImp, requestConfig, deviceOs, manufacturer, connectType, carrier)
	if adxAd.Ret != 0 {
		return responseNoBidReturn(fmt.Sprintf("no fill, ret: %d", adxAd.Ret))
	} else if len(adxAd.Data[requestConfig.LocalPosID].List) == 0 {
		return responseNoBidReturn("no fill, ad list is empty")
	}

	var impItem NeteaseRequestImpObject
	for _, imp := range resultfulImp {
		if imp.Tagid == requestConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if len(impItem.Id) == 0 {
		return responseNoBidReturn("no fill, ad list is empty")
	}

	var responseObject NeteaseResponseObject

	responseBidObjectList := []NeteaseResponseBidObject{}

	for _, adxAd := range adxAd.Data[requestConfig.LocalPosID].List {
		isVideoAd := isVideoAd(adxAd)

		// 样式id
		destStyleID := requestConfig.ImageStyleID
		if isVideoAd {
			destStyleID = requestConfig.VideoStyleID
		}

		if len(destStyleID) == 0 {
			continue
		}

		// 统计
		models.BigDataRtbEcpmToHolo(c, uuid, requestConfig.TagID, requestConfig.LocalAppID, requestConfig.LocalPosID, requestConfig.Price, adxAd.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if adxAd.Ecpm < requestConfig.Price {
			continue
		}
		ecpm := adxAd.Ecpm

		responseBidObject := NeteaseResponseBidObject{}
		responseBidObject.Id = uuid
		responseBidObject.Impid = impItem.Id
		responseBidObject.Price = int64(ecpm)
		responseBidObject.Nurl = getWinUrl(uuid, channel, requestConfig, adxAd, ecpm)
		responseBidObject.Crid = adxAd.Crid
		responseBidObject.Pvm = adxAd.ImpressionLink
		responseBidObject.Clickm = getClickLink(adxAd.ClickLink,
			utils.ConvertIntToString(requestObject.Device.ScreenWidth),
			utils.ConvertIntToString(requestObject.Device.ScreenHeight))
		responseBidObject.Dealid = ""

		ext := NeteaseResponseExtObject{}
		if len(adxAd.Description) > 0 {
			ext.Title = adxAd.Description
			ext.SubTitle = adxAd.Description
		} else if len(adxAd.Title) > 0 {
			ext.Title = adxAd.Title
			ext.SubTitle = adxAd.Title
		}
		adms := []NeteaseResponseAdmObject{}

		if isVideoAd {
			if len(adxAd.Video.VideoURL) > 0 {
				adm := NeteaseResponseAdmObject{}

				adm.Type = 1
				adm.Url = adxAd.Video.VideoURL
				adm.W = adxAd.Video.Width
				adm.H = adxAd.Video.Height

				adms = append(adms, adm)

			}

			if len(adxAd.Video.CoverURL) > 0 {
				adm := NeteaseResponseAdmObject{}

				adm.Type = 0

				adm.Url = adxAd.Video.CoverURL
				adm.W = adxAd.Video.Width
				adm.H = adxAd.Video.Height

				adms = append(adms, adm)
			}
		} else {

			if len(adxAd.Image) > 0 && len(adxAd.Image[0].URL) > 0 {
				adm := NeteaseResponseAdmObject{}

				adm.Type = 0

				adm.Url = adxAd.Image[0].URL
				adm.W = adxAd.Image[0].Width
				adm.H = adxAd.Image[0].Height

				adms = append(adms, adm)
			}
		}

		ext.Adm = adms

		ext.Style = utils.ConvertStringToInt(destStyleID)
		ext.Advertiser = NeteaseResponseAdvertiserObject{
			Id:          "16918",
			Industry:    "18",
			SubIndustry: "1802",
		}
		if len(adxAd.DeepLink) > 0 {
			ext.Deeplink = adxAd.DeepLink
		}
		if len(adxAd.AppName) > 0 {
			ext.AppName = adxAd.AppName
		}
		if len(adxAd.PackageName) > 0 {
			ext.PackageName = adxAd.PackageName
		}
		if len(adxAd.IconURL) > 0 {
			ext.Icon = adxAd.IconURL
		}
		if len(adxAd.PrivacyLink) > 0 {
			ext.PrivacyPolicy = adxAd.PrivacyLink
		}
		if len(adxAd.Permission) > 0 {
			ext.Authority = adxAd.Permission
		}

		if len(adxAd.LandpageURL) > 0 {
			ext.LinkUrl = adxAd.LandpageURL
		}
		//  else {
		// 	responseNoBidReturn("landing page is empty")
		// }

		//2022-10-31 针对网易新闻特殊处理，只支持H5不支持download

		if adxAd.InteractType == 0 {
			if len(adxAd.DeepLink) > 0 {
				ext.Deeplink = adxAd.DeepLink
			} else {
				if len(adxAd.MarketURL) > 0 {
					ext.Deeplink = adxAd.MarketURL
				}
			}

			ext.LinkUrl = adxAd.LandpageURL
			if deviceOs == rtb.MH_RTB_OS_ANDROID.String() {
				ext.AndroidUrl = adxAd.LandpageURL
			} else if deviceOs == rtb.MH_RTB_OS_IOS.String() {
				ext.IosUrl = adxAd.LandpageURL
			}
		} else if adxAd.InteractType == 1 {
			if len(adxAd.MarketURL) > 0 {
				ext.Deeplink = adxAd.MarketURL
			} else {
				if len(adxAd.DeepLink) > 0 {
					ext.Deeplink = adxAd.DeepLink
				}
			}

			ext.LinkUrl = adxAd.DownloadURL
			if deviceOs == rtb.MH_RTB_OS_ANDROID.String() {
				ext.AndroidUrl = adxAd.DownloadURL
			} else if deviceOs == rtb.MH_RTB_OS_IOS.String() {
				ext.IosUrl = adxAd.DownloadURL
			}
		}

		responseBidObject.Ext = ext

		if isVideoAd {
			responseBidObject.EventTracker = getEventTrack(adxAd.Video.EventTracks)
		}
		responseBidObject.InteractMode = 0
		responseBidObject.NeedCoordinate = 1

		responseBidObjectList = append(responseBidObjectList, responseBidObject)
	}

	responseSeatBidObjectList := []NeteaseResponseSeatBidObject{
		{
			Bid: responseBidObjectList,
		},
	}

	// 构造返回结果
	responseObject.Id = requestObject.Id
	responseObject.Bidid = requestConfig.TagID
	responseObject.Cur = "CNY"
	responseObject.Seatbid = responseSeatBidObjectList

	// if requestConfig.LocalPosID == "58116" {
	// 	jsonByte, _ := utils.JSONNoEscapeMarshal(responseObject)

	// 	jsonString := string(jsonByte)
	// 	fmt.Println("NETEASE_DEBUG:", jsonString)
	// 	utils.MailDebuggerSend(jsonString)
	// }

	return responseBidReturn(&responseObject)
}

func responseBidReturn(resp *NeteaseResponseObject) (*NeteaseResponseObject, int) {
	return resp, 200
}

func responseNoBidReturn(reason string) (*NeteaseResponseObject, int) {
	fmt.Println("responseNoBidReturn: ", reason)
	return nil, 204
}

// 适配 os 字符串
func getOsString(requestObject *NeteaseRequestObject) (string, error) {
	switch requestObject.Device.Os {
	case rtb.MH_RTB_OS_ANDROID.String():
		return rtb.MH_RTB_OS_ANDROID.String(), nil
	case rtb.MH_RTB_OS_IOS.String():
		return rtb.MH_RTB_OS_IOS.String(), nil
	}
	return rtb.MH_RTB_OS_UNKNOWN.String(), fmt.Errorf("invalid os")
}

// 判断是否有效的UA
func isValidUA(requestObject *NeteaseRequestObject) bool {
	return len(requestObject.Device.Ua) != 0
}

// 判断广告是否为视频类型
func isVideoAd(adxAd models.MHRespDataItem) bool {
	return adxAd.Video != nil && len(adxAd.Video.VideoURL) > 0
}

// 获取制造商
func getManufacturer(requestObject *NeteaseRequestObject, osString string) string {
	if osString == rtb.MH_RTB_OS_IOS.String() {
		return rtb.MH_MANUFACTURER_APPLE
	}
	return requestObject.Device.Manufacturer
}

// 获取网络类型
func getConnectType(requestObject *NeteaseRequestObject) rtb.MHRtbConnectTypeEnum {
	switch requestObject.Device.Connectiontype {
	case NETEASE_CONNECTIONTYPE_WIFI.String():
		return rtb.MH_RTB_CONNECTTYPE_WIFI
	case NETEASE_CONNECTIONTYPE_2G.String():
		return rtb.MH_RTB_CONNECTTYPE_2G
	case NETEASE_CONNECTIONTYPE_3G.String():
		return rtb.MH_RTB_CONNECTTYPE_3G
	case NETEASE_CONNECTIONTYPE_4G.String():
		return rtb.MH_RTB_CONNECTTYPE_4G
	}

	return rtb.MH_RTB_CONNECTTYPE_UNKNOWN
}

// 获取运营商
func getCarrier(requestObject *NeteaseRequestObject) rtb.MHRtbCarrierEnum {
	switch requestObject.Device.Carrier {
	case NETEASE_CARRIER_CM.String():
		return rtb.MH_RTB_CARRIER_CM
	case NETEASE_CARRIER_CU.String():
		return rtb.MH_RTB_CARRIER_CU
	case NETEASE_CARRIER_CT.String():
		return rtb.MH_RTB_CARRIER_CT
	}

	return rtb.MH_RTB_CARRIER_UNKNOWN
}

// 判断Imp是否有效
func isValidImp(requestObject *NeteaseRequestObject) bool {
	return len(requestObject.Imp) != 0
}

// 获得有填充的Imp
func getResultfulImp(requestObject *NeteaseRequestObject, c *gin.Context, channel string, osString string) ([]NeteaseRequestImpObject, []models.RtbConfigByTagIDStu) {
	var resultfulImp []NeteaseRequestImpObject
	var configList []models.RtbConfigByTagIDStu

	for _, imp := range requestObject.Imp {
		tagId := imp.Tagid
		price := imp.Bidfloor

		if price == 0 {
			continue
		}

		var styleIds []string
		for _, styleId := range imp.Allowstyle {
			styleIds = append(styleIds, utils.ConvertIntToString(styleId))
		}

		adxInfo := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, tagId, osString, styleIds, "", int(price))

		fmt.Println("channel: ", channel, ", tagId: ", tagId, ", osString: ", osString, ", styleIds: ", styleIds, ", price: ", price)
		fmt.Printf("adxInfo: %#v", adxInfo)

		if adxInfo == nil || len(*adxInfo) == 0 {
			continue
		}

		config := (*adxInfo)[0]

		resultfulImp = append(resultfulImp, imp)
		configList = append(configList, config)
	}

	return resultfulImp, configList
}

// 获得Adx广告
func getAdxAd(
	uuid string,
	requestObject *NeteaseRequestObject,
	c *gin.Context,
	resultfulImp []NeteaseRequestImpObject,
	requestConfig models.RtbConfigByTagIDStu,
	osString string,
	manufacturer string,
	connectType rtb.MHRtbConnectTypeEnum,
	carrier rtb.MHRtbCarrierEnum) models.MHResp {

	var caidMultiList []models.MHReqCAIDMulti
	if len(requestObject.Device.Caid) > 0 && len(requestObject.Device.CaidVersion) > 0 {
		var caidMulti models.MHReqCAIDMulti
		caidMulti.CAID = requestObject.Device.Caid
		caidMulti.CAIDVersion = requestObject.Device.CaidVersion
		caidMultiList = append(caidMultiList, caidMulti)
	}

	device := models.MHReqDevice{
		Os:                 osString,
		OsVersion:          requestObject.Device.OsVersion,
		Model:              requestObject.Device.Model,
		Manufacturer:       manufacturer,
		Imei:               requestObject.Device.Imei,
		ImeiMd5:            requestObject.Device.ImeiMd5,
		AndroidID:          requestObject.Device.AndroidId,
		Oaid:               requestObject.Device.Oaid,
		Idfa:               requestObject.Device.Idfa,
		Ua:                 requestObject.Device.Ua,
		ScreenWidth:        int(requestObject.Device.ScreenWidth),
		ScreenHeight:       int(requestObject.Device.ScreenHeight),
		DeviceType:         1,
		IP:                 requestObject.Device.Ip,
		DeviceStartSec:     requestObject.Device.OptDeviceInfo.DeviceStartSec,
		Country:            requestObject.Device.OptDeviceInfo.Country,
		Language:           requestObject.Device.OptDeviceInfo.Language,
		DeviceNameMd5:      requestObject.Device.OptDeviceInfo.DeviceNameMd5,
		HardwareModel:      requestObject.Device.OptDeviceInfo.HardwareModel,
		HardwareMachine:    requestObject.Device.Model,
		PhysicalMemoryByte: requestObject.Device.OptDeviceInfo.PhysicalMemoryByte,
		HarddiskSizeByte:   requestObject.Device.OptDeviceInfo.HarddiskSizeByte,
		SystemUpdateSec:    requestObject.Device.OptDeviceInfo.SystemUpdateSec,
		TimeZone:           requestObject.Device.OptDeviceInfo.TimeZone,
		BootMark:           requestObject.Device.BootMark,
		UpdateMark:         requestObject.Device.UpdateMark,
		CAIDMulti:          caidMultiList,
		AppList:            getNexAppList(requestObject.Device.AppList),
	}

	adxRequest := models.MHReq{
		App: models.MHReqApp{
			AppID:       requestConfig.LocalAppID,
			AppBundleID: requestObject.App.Bundle,
			AppName:     requestObject.App.Name,
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(requestConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: requestConfig.Price,
		},
		Device: device,
		Network: models.MHReqNetwork{
			ConnectType: int(connectType),
			Carrier:     int(carrier),
		},
	}

	mhResp := core.GetADFromAdxWithContext(c, &adxRequest, uuid)

	return *mhResp
}

// 获得 win url
func getWinUrl(uuid string, channel string, requestConfig models.RtbConfigByTagIDStu, adxAd models.MHRespDataItem, ecpm int) string {
	return config.ExternalRtbPriceURL +
		"?uid=" + uuid +
		"&tagid=" + requestConfig.TagID +
		"&bp=" + utils.ConvertIntToString(ecpm) +
		"&channel=" + channel +
		"&price=${AUCTION_PRICE}" +
		"&log=" + url.QueryEscape(adxAd.Log)
}

// 获得替换后的click link
func getClickLink(clickLinkArray []string, width string, height string) []string {
	var replacedClickLinkArray []string
	for _, clickLink := range clickLinkArray {
		tempLink := clickLink
		tempLink = strings.Replace(tempLink, "__WIDTH__", width, -1)
		tempLink = strings.Replace(tempLink, "__HEIGHT__", height, -1)
		// tempLink = strings.Replace(tempLink, "__DOWN_X__", "__DOWN_X__", -1)
		// tempLink = strings.Replace(tempLink, "__DOWN_Y__", "__DOWN_Y__", -1)
		// tempLink = strings.Replace(tempLink, "__UP_X__", "__UP_X__", -1)
		// tempLink = strings.Replace(tempLink, "__UP_Y__", "__UP_Y__", -1)
		replacedClickLinkArray = append(replacedClickLinkArray, tempLink)
	}

	return replacedClickLinkArray
}

// 获得Event Tracker
func getEventTrack(eventTrackArray []models.MHEventTrackItem) []NeteaseResponseEventTrackerObject {
	var eventTrackerArray []NeteaseResponseEventTrackerObject

	for _, eventTrack := range eventTrackArray {
		var newEventTracker NeteaseResponseEventTrackerObject
		if eventTrack.EventType == int(rtb.MH_RTB_EVENTTRACK_TYPE_START) {
			newEventTracker.Type = int(NETEASE_EVENTTRACKER_TYPE_PLAYSTART)
		} else if eventTrack.EventType == int(rtb.MH_RTB_EVENTTRACK_TYPE_END) {
			newEventTracker.Type = int(NETEASE_EVENTTRACKER_TYPE_PLAYEND)
		}
		newEventTracker.Url = eventTrack.EventURLS[0]

		eventTrackerArray = append(eventTrackerArray, newEventTracker)
	}

	return eventTrackerArray
}

func getNexAppList(appList []string) []int {
	if len(appList) == 0 {
		return []int{}
	}

	var appListIdArray []int
	for _, appId := range appList {
		if v, ok := nexAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, v)
		}
	}

	return appListIdArray
}

var nexAppListCodeMap = map[string]int{
	"1":  1004,
	"2":  1009,
	"3":  1001,
	"4":  1015,
	"5":  1002,
	"6":  1005,
	"7":  1017,
	"8":  1018,
	"9":  1046,
	"10": 1035,
	"11": 1047,
	"13": 1006,
	"14": 1020,
	"15": 1011,
	"16": 1012,
}
