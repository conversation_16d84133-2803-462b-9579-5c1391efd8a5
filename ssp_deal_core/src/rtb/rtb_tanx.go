package rtb

import (
	"encoding/json"
	"fmt"
	"mh_proxy/config"
	"mh_proxy/core"
	"mh_proxy/models"
	"mh_proxy/pb/tanx_down"
	"mh_proxy/utils"
	"net/url"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/protobuf/proto"
)

// HandleByTanxProtobuf ...
func HandleByTanxProtobuf(c *gin.Context, channel string) *tanx_down.BidResponse {
	bodyContent, err := c.GetRawData()
	tanxReq := &tanx_down.BidRequest{}
	err = proto.Unmarshal(bodyContent, tanxReq)
	if err != nil {
		fmt.Println(err)
		return nil
	}

	reqOs := ""
	if tanxReq.Device.GetOs() == 3 {
		reqOs = "android"
	} else if tanxReq.Device.GetOs() == 2 {
		reqOs = "ios"
	} else {
		return tanxNoBidReturn(tanxReq, "wrong os")
	}

	reqDeviceMake := tanxReq.Device.GetMake()
	if reqOs == "ios" {
		reqDeviceMake = "Apple"
	}

	if len(tanxReq.Device.GetUserAgent()) == 0 {
		// return tanxNoBidReturn(tanxReq, "wrong ua")
	}

	reqDeviceUA := tanxReq.Device.GetUserAgent()

	// 举例: GDTMobSDK4.491.1400-[Dalvik/2.1.0 (Linux; U; Android 10; STK-AL00 Build/HUAWEISTK-AL00)]
	if strings.Contains(reqDeviceUA, "GDTMobSDK") && strings.Contains(reqDeviceUA, "[") && strings.Contains(reqDeviceUA, "]") {
		reqDeviceUA = strings.SplitN(reqDeviceUA, "[", 2)[1]
		reqDeviceUA = strings.SplitN(reqDeviceUA, "]", 2)[0]
	}

	reqConnectType := 0
	if tanxReq.Device.GetConnectionType() == 0 {
		reqConnectType = 0
	} else if tanxReq.Device.GetConnectionType() == 1 {
		reqConnectType = 1
	} else if tanxReq.Device.GetConnectionType() == 2 {
		reqConnectType = 2
	} else if tanxReq.Device.GetConnectionType() == 3 {
		reqConnectType = 3
	} else if tanxReq.Device.GetConnectionType() == 4 {
		reqConnectType = 4
	} else if tanxReq.Device.GetConnectionType() == 5 {
		reqConnectType = 7
	} else {
		reqConnectType = 0
	}

	reqCarrier := 0
	if tanxReq.Device.GetCarrier() == 1 {
		reqCarrier = 1
	} else if tanxReq.Device.GetCarrier() == 2 {
		reqCarrier = 2
	} else if tanxReq.Device.GetCarrier() == 3 {
		reqCarrier = 3
	} else {
		reqCarrier = 0
	}

	if len(tanxReq.GetImps()) == 0 {
		return tanxNoBidReturn(tanxReq, "wrong imp")
	}

	// 只有当请求体长度超过8K时才打印日志
	if len(bodyContent) >= 16384 {
		fmt.Printf("HandleByTanxProtobuf bodySize reqOs=%v, ua=%v, reqConnectType=%v, reqCarrier=%v, rawLen=%v\n", reqOs, reqDeviceUA, reqConnectType, reqCarrier, len(bodyContent))
	}

	var reqOKImps []*tanx_down.BidRequest_Imp
	var configList []models.RtbConfigByTagIDStu
	var reqRtbConfig models.RtbConfigByTagIDStu

	for _, item := range tanxReq.GetImps() {
		reqTagID := item.GetTagId()
		reqPrice := item.GetMinCpmPrice()

		var templateIds []string
		for _, templateItem := range item.GetTemplates() {
			templateIds = append(templateIds, utils.ConvertInt64ToString(int64(templateItem.GetId())))
		}

		rtbConfigArrayByTagID := models.GetAdxInfoByRtbTagIDAndStyles(c, channel, reqTagID, reqOs, templateIds, "", int(reqPrice))
		if rtbConfigArrayByTagID == nil || len(*rtbConfigArrayByTagID) == 0 {
			continue
		}

		if len(tanxReq.GetApp().GetPackageName()) > 0 {
			for _, infoItem := range *rtbConfigArrayByTagID {
				if infoItem.PackageName == tanxReq.GetApp().GetPackageName() {
					infoItem.ExtraTag = utils.ConvertIntToString(infoItem.IsTagIDAuditNeedStyleID) + "," + utils.ConvertIntToString(infoItem.IsTagIDAuditToServer)
					configList = append(configList, infoItem)
				}
			}
		}

		if len(configList) == 0 {
			for _, infoItem := range *rtbConfigArrayByTagID {
				if len(infoItem.PackageName) == 0 {
					infoItem.ExtraTag = utils.ConvertIntToString(infoItem.IsTagIDAuditNeedStyleID) + "," + utils.ConvertIntToString(infoItem.IsTagIDAuditToServer)
					configList = append(configList, infoItem)
				}
			}
		}

		reqOKImps = append(reqOKImps, item)
	}

	if len(reqOKImps) == 0 || len(configList) == 0 {
		return tanxNoBidReturn(tanxReq, "wrong imp")
	}

	// 过滤包名之后存holo请求
	for _, item := range configList {
		if len(item.PackageName) > 0 {
			models.BigDataRtbReqPrice(c, item.TagID, item.OriginPrice)
		}
	}

	reqRtbConfig = configList[0]

	// osv hack  -> 转小些替换 "android ", "ios "
	// osv 包含"," 取,的最后一个
	osVersion := strings.ToLower(tanxReq.Device.GetOsv())

	// model hack
	model := tanxReq.Device.GetModel()
	if reqOs == "android" {
		// model = strings.Replace(model, "苹果 ", "", -1)
	} else if reqOs == "ios" {
		model = strings.Replace(model, "苹果", "", -1)
	}
	if strings.Contains(model, "（") {
		model = strings.Split(model, "（")[0]
	}

	// manufacturer hack
	if reqOs == "android" {
		if strings.Contains(reqDeviceMake, "小米") {
			reqDeviceMake = "Xiaomi"
		} else if strings.Contains(reqDeviceMake, "华为") {
			reqDeviceMake = "HUAWEI"
		} else if strings.Contains(reqDeviceMake, "荣耀") {
			reqDeviceMake = "HONOR"
		} else if strings.Contains(reqDeviceMake, "真我") {
			reqDeviceMake = "realme"
		} else if strings.Contains(reqDeviceMake, "酷比") {
			reqDeviceMake = "koobee"
		} else if strings.Contains(reqDeviceMake, "黑鲨") {
			reqDeviceMake = "Xiaomi"
		} else if strings.Contains(reqDeviceMake, "三星") {
			reqDeviceMake = "samsung"
		} else if strings.Contains(strings.ToLower(reqDeviceMake), "unknown") {
			reqDeviceMake = ""
		}
	}

	// mode <-> manufacturer
	if reqOs == "android" {
		if reqRtbConfig.IsMediaExchangeModelMake == 1 {
			tmpModel := model

			model = reqDeviceMake
			reqDeviceMake = tmpModel
		}
	}

	var caidMultiList []models.MHReqCAIDMulti
	if len(tanxReq.Device.GetCaids()) > 0 {
		for _, item := range tanxReq.Device.GetCaids() {
			var tmpCaid models.MHReqCAIDMulti
			tmpCaid.CAID = item.GetCaid()
			tmpCaid.CAIDVersion = item.GetVer()
			caidMultiList = append(caidMultiList, tmpCaid)
		}
	}

	reqStu := models.MHReq{
		App: models.MHReqApp{
			AppID:       reqRtbConfig.LocalAppID,
			AppBundleID: tanxReq.App.GetPackageName(),
			AppName:     tanxReq.App.GetName(),
			AppType:     4, // 4 - RTB
		},

		Pos: models.MHReqPos{
			ID:      utils.ConvertStringToInt(reqRtbConfig.LocalPosID),
			AdCount: 1,
			Width:   640,
			Height:  960,
			// cpm_bid_floor 底价
			CPMBidFloor: reqRtbConfig.Price,
		},
		Device: models.MHReqDevice{
			Os:           reqOs,
			OsVersion:    osVersion,
			Model:        model,
			Manufacturer: reqDeviceMake,
			ImeiMd5:      tanxReq.Device.GetImeiMd5(),
			AndroidID:    tanxReq.Device.GetAndroidId(),
			Oaid:         tanxReq.Device.GetOaid(),
			Idfa:         tanxReq.Device.GetIdfa(),
			IdfaMd5:      tanxReq.Device.GetIdfaMd5(),
			Ua:           reqDeviceUA,
			ScreenWidth:  int(tanxReq.Device.GetScreenWidth()),
			ScreenHeight: int(tanxReq.Device.GetScreenHeight()),
			DeviceType:   1,
			IP:           tanxReq.Device.GetIp(),
			// MacMd5:       tanxReq.Device.MacMd5,
			CAIDMulti: caidMultiList,

			// installed app list
			AppList: getTanxAppList(tanxReq.Device.GetInstalledAppIds()),
		},
		Network: models.MHReqNetwork{
			ConnectType: reqConnectType,
			Carrier:     reqCarrier,
		},
		// Geo: models.MHReqGeo{
		// 	Lat: tanxReq.Device.Geo.GetLat(),
		// 	Lng: tanxReq.Device.Geo.GetLon(),
		// },
	}
	// fmt.Println(reqStu)

	bigdataUID := uuid.NewV4().String()
	mhResp := core.GetADFromAdxWithContext(c, &reqStu, bigdataUID)

	if mhResp.Ret != 0 {
		return tanxNoBidReturn(tanxReq, "no fill")
	}
	mhRespCount := len(mhResp.Data[reqRtbConfig.LocalPosID].List)
	if mhRespCount == 0 {
		return tanxNoBidReturn(tanxReq, "no fill")
	}

	var impItem *tanx_down.BidRequest_Imp
	for _, imp := range reqOKImps {
		if imp.GetTagId() == reqRtbConfig.OriginTagID {
			impItem = imp
			break
		}
	}

	if impItem == nil {
		return tanxNoBidReturn(tanxReq, "no fill")
	}
	////////////////////////////////////////////////////////////////////////////////////////
	// save to audit
	if reqRtbConfig.IsPosAudit == 1 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					fmt.Println("tanx save to audit panic:", err)
				}
			}()
			models.BigDataAudit(c, bigdataUID, &reqRtbConfig, &reqStu, mhResp, channel)
		}()
	}
	////////////////////////////////////////////////////////////////////////////////////////
	tanxRespSeatBid := tanx_down.BidResponse_SeatBid{}

	for _, mhDataItem := range mhResp.Data[reqRtbConfig.LocalPosID].List {
		// 统计
		models.BigDataRtbEcpmToHolo(c, bigdataUID, reqRtbConfig.TagID, reqRtbConfig.LocalAppID, reqRtbConfig.LocalPosID, reqRtbConfig.Price, mhDataItem.Ecpm)

		// 判定上游返回ecpm是否大于底价
		if mhDataItem.Ecpm < reqRtbConfig.Price {
			continue
		}
		ecpm := mhDataItem.Ecpm

		winURL := config.ExternalRtbPriceURL + "?uid=" + bigdataUID + "&tagid=" + reqRtbConfig.TagID + "&bp=" + utils.ConvertIntToString(ecpm) + "&channel=" + channel + "&price=__AUCTION_PRICE__" + "&bidid=" + tanxReq.GetId() + "&log=" + url.QueryEscape(mhDataItem.Log)

		tanxRespBid := tanx_down.BidResponse_Bid{
			MaxCpmPrice: proto.Int32(int32(ecpm)),
		}
		// fmt.Println(mhDataItem.Video.VideoURL)
		isVideoType := 0
		if mhDataItem.Video != nil && len(mhDataItem.Video.VideoURL) > 0 {
			isVideoType = 1

			// tanx duration > 30s 不到送审模块, 也不填充
			if mhDataItem.Video.Duration > 30000 {
				continue
			}
		}

		// 样式id
		destStyleID := reqRtbConfig.ImageStyleID
		if isVideoType == 1 {
			destStyleID = reqRtbConfig.VideoStyleID
		}

		imageURL := ""
		videoURL := ""
		coverURL := ""
		if isVideoType == 0 {
			imageURL = mhDataItem.Image[0].URL
			if strings.HasPrefix(imageURL, "http://") {
				imageURL = strings.Replace(imageURL, "http://", "https://", -1)
			}

			isHasJPGSuffixKey := false
			if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
				tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
				for _, item := range tmpArrays {
					if strings.Contains(imageURL, item) {
						isHasJPGSuffixKey = true
					}
				}
			}

			if isHasJPGSuffixKey {
				if strings.HasSuffix(imageURL, ".jpg") || strings.HasSuffix(imageURL, ".jpeg") {
				} else {
					imageURL = imageURL + ".jpg"
				}
			}
		} else {
			videoURL = mhDataItem.Video.VideoURL
			if strings.HasPrefix(videoURL, "http://") {
				videoURL = strings.Replace(videoURL, "http://", "https://", -1)
			}
			coverURL = mhDataItem.Video.CoverURL
			if strings.HasPrefix(coverURL, "http://") {
				coverURL = strings.Replace(coverURL, "http://", "https://", -1)
			}
		}

		iconURL := mhDataItem.IconURL
		if strings.HasPrefix(iconURL, "http://") {
			iconURL = strings.Replace(iconURL, "http://", "https://", -1)
		}
		if len(iconURL) > 0 {
			isHasJPGSuffixKey := false
			if len(reqRtbConfig.AuditJPGSuffixKey) > 0 {
				tmpArrays := strings.Split(reqRtbConfig.AuditJPGSuffixKey, ",")
				for _, item := range tmpArrays {
					if strings.Contains(iconURL, item) {
						isHasJPGSuffixKey = true
					}
				}
			}
			if isHasJPGSuffixKey {
				if strings.HasSuffix(iconURL, ".png") || strings.HasSuffix(iconURL, ".jpg") || strings.HasSuffix(iconURL, ".jpeg") {
				} else {
					iconURL = iconURL + ".png"
				}
			}
		}
		landpageURL := mhDataItem.LandpageURL
		if strings.HasPrefix(landpageURL, "http://") {
			landpageURL = strings.Replace(landpageURL, "http://", "https://", -1)
		}
		downloadURL := mhDataItem.DownloadURL
		if strings.HasPrefix(downloadURL, "http://") {
			downloadURL = strings.Replace(downloadURL, "http://", "https://", -1)
		}

		// impress link
		var impTrackArray []string
		for _, impItem := range mhDataItem.ImpressionLink {
			tmpItem := impItem
			if strings.HasPrefix(tmpItem, "http://") {
				tmpItem = strings.Replace(tmpItem, "http://", "https://", -1)
			}
			impTrackArray = append(impTrackArray, tmpItem)
		}
		impTrackArray = append(impTrackArray, winURL)

		// click link
		var clkTrackArray []string
		for _, clkItem := range mhDataItem.ClickLink {
			tmpItem := clkItem
			if strings.HasPrefix(tmpItem, "http://") {
				tmpItem = strings.Replace(tmpItem, "http://", "https://", -1)
			}
			clkTrackArray = append(clkTrackArray, tmpItem)
		}

		admURL := ""
		md5Key := ""
		if isVideoType == 1 {
			md5Key = utils.GetMd5(videoURL + iconURL + reqRtbConfig.LocalPosID)
		} else {
			md5Key = utils.GetMd5(imageURL + iconURL + reqRtbConfig.LocalPosID)
		}

		if reqRtbConfig.IsPosAudit == 1 {
			isOK, tanxAuditData := models.GetTanxByMd5Key(md5Key)
			if isOK != 1 {
				// fmt.Println("continue 0")
				continue
			}
			extraTag := tanxAuditData.ExtraTag
			extraTagArray := strings.Split(extraTag, ",")
			if utils.ConvertStringToInt(extraTagArray[0]) == reqRtbConfig.IsTagIDAuditNeedStyleID {
			} else {
				continue
			}
			if reqRtbConfig.IsTagIDAuditNeedStyleID == 0 {
				if isVideoType == 1 {
					admURL = tanxAuditData.YouKuVideoURL
					if len(admURL) == 0 {
						// fmt.Println("continue 0")
						continue
					}
				} else {
					admURL = tanxAuditData.ImageURL
					if len(admURL) == 0 {
						// fmt.Println("continue 1")
						continue
					}
				}
			}
		}

		tanxRespBid.CreativeId = proto.String(md5Key)
		if isVideoType == 0 {
			tanxRespBid.CreativeType = proto.Int32(2)
		} else if isVideoType == 1 {
			tanxRespBid.CreativeType = proto.Int32(4)
		} else {
			continue
		}

		// 获取模版宽高
		auditImageWidth := 0
		auditImageHeight := 0
		auditVideoWidth := 0
		auditVideoHeight := 0
		auditMaterialType := 0

		var tanxTemplateIDStu []TanxWidthHeightByTemplateIDStu
		json.Unmarshal([]byte(TanxTemplateIDJson), &tanxTemplateIDStu)

		isFound := false
		for _, tmpTemplateIDItem := range tanxTemplateIDStu {
			// fmt.Println("xxx item:", item.TemplateID, item.MaterialType, item.AuditImageWidth, item.AuditImageHeight, item.AuditVideoWidth, item.AuditVideoHeight)
			if tmpTemplateIDItem.TemplateID == destStyleID {
				auditImageWidth = tmpTemplateIDItem.AuditImageWidth
				auditImageHeight = tmpTemplateIDItem.AuditImageHeight
				auditVideoWidth = tmpTemplateIDItem.AuditVideoWidth
				auditVideoHeight = tmpTemplateIDItem.AuditVideoHeight
				auditMaterialType = tmpTemplateIDItem.MaterialType

				isFound = true
				break
			}
		}

		if isFound {
		} else {
			fmt.Println("tanx error width height, style id:", destStyleID)
			continue
		}

		// 判定填充是否跟模版类型匹配
		if isVideoType != auditMaterialType {
			continue
		}

		// template data
		tanxRespBidTemplateData := tanx_down.BidResponse_TemplateData{
			TemplateId: proto.Uint64(uint64(utils.ConvertStringToInt(destStyleID))),
		}

		// template Fields adv_name
		tanxRespBidTemplateFieldAdvName := tanx_down.BidResponse_TemplateData_Field{
			Name:  proto.String("adv_name"),
			Value: proto.String("maplehaze"),
		}
		tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldAdvName)

		// template Fields title
		tanxRespBidTemplateFieldTitle := tanx_down.BidResponse_TemplateData_Field{
			Name:  proto.String("title"),
			Value: proto.String(mhDataItem.Title),
		}
		tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldTitle)

		// template Fields description
		// 描述 6-20
		description := mhDataItem.Description
		if len(description) > 0 {
			if utf8.RuneCountInString(description) < 6 {
				description = description + "......"
			}
			if utf8.RuneCountInString(description) > 20 {
				descriptionRune := []rune(description)
				description = string(descriptionRune[0:20])
			}
		}
		tanxRespBidTemplateFieldDescription := tanx_down.BidResponse_TemplateData_Field{
			Name:  proto.String("description"),
			Value: proto.String(description),
		}
		tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldDescription)

		// template Fields img_sm, img_sm_width, img_sm_height
		if len(iconURL) > 0 {
			// icon
			tanxRespBidTemplateFieldIcon := tanx_down.BidResponse_TemplateData_Field{
				Name:  proto.String("img_sm"),
				Value: proto.String(iconURL),
			}
			tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldIcon)

			// icon width
			tanxRespBidTemplateFieldIconWidth := tanx_down.BidResponse_TemplateData_Field{
				Name:  proto.String("img_sm_width"),
				Value: proto.String("512"),
			}
			tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldIconWidth)

			// icon height
			tanxRespBidTemplateFieldIconHeight := tanx_down.BidResponse_TemplateData_Field{
				Name:  proto.String("img_sm_height"),
				Value: proto.String("512"),
			}
			tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldIconHeight)
		}
		// template Fields img_url img_url_width img_url_height
		if isVideoType == 1 {
			if auditImageWidth > 0 && auditImageHeight > 0 {
				// img_url
				tanxRespBidTemplateFieldImageURL := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url"),
					Value: proto.String(coverURL),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageURL)

				// img_url_width
				tanxRespBidTemplateFieldImageWidth := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url_width"),
					Value: proto.String(utils.ConvertIntToString(auditImageWidth)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageWidth)

				// img_url_height
				tanxRespBidTemplateFieldImageHeight := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url_height"),
					Value: proto.String(utils.ConvertIntToString(auditImageHeight)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageHeight)
			}
		} else {
			if auditImageWidth > 0 && auditImageHeight > 0 {
				// img_url
				tanxRespBidTemplateFieldImageURL := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url"),
					Value: proto.String(imageURL),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageURL)

				// img_url_width
				tanxRespBidTemplateFieldImageWidth := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url_width"),
					Value: proto.String(utils.ConvertIntToString(auditImageWidth)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageWidth)

				// img_url_height
				tanxRespBidTemplateFieldImageHeight := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("img_url_height"),
					Value: proto.String(utils.ConvertIntToString(auditImageHeight)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldImageHeight)
			}
		}

		// template Fields video video_width video_height video_duration
		if isVideoType == 1 {
			if auditVideoWidth > 0 && auditVideoHeight > 0 {
				// video
				tanxRespBidTemplateFieldVideoURL := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("video"),
					Value: proto.String(videoURL),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldVideoURL)

				// video_width
				tanxRespBidTemplateFieldVideoWidth := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("video_width"),
					Value: proto.String(utils.ConvertIntToString(auditVideoWidth)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldVideoWidth)

				// video_height
				tanxRespBidTemplateFieldVideoHeight := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("video_height"),
					Value: proto.String(utils.ConvertIntToString(auditVideoHeight)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldVideoHeight)

				// video_duration
				tanxRespBidTemplateFieldVideoDuration := tanx_down.BidResponse_TemplateData_Field{
					Name:  proto.String("video_duration"),
					Value: proto.String(utils.ConvertIntToString(mhDataItem.Video.Duration)),
				}
				tanxRespBidTemplateData.Fields = append(tanxRespBidTemplateData.Fields, &tanxRespBidTemplateFieldVideoDuration)
			}
		}

		tanxRespBid.TemplateData = &tanxRespBidTemplateData

		// monitor object
		tanxRespBidMonitor := tanx_down.BidResponse_Monitor{}
		tanxRespBidMonitor.ImpTrackingUrl = impTrackArray
		tanxRespBidMonitor.ClickTrackingUrl = clkTrackArray
		// tanxRespBidMonitor.WinnoticeUrl = proto.String(winURL)
		tanxRespBid.Monitor = &tanxRespBidMonitor

		// landding object
		tanxRespBidLanding := tanx_down.BidResponse_Landing{}
		if mhDataItem.InteractType == 0 {
			if len(mhDataItem.DeepLink) > 0 {
				tanxRespBidLanding.DeeplinkUri = proto.String(mhDataItem.DeepLink)
				tanxRespBidLanding.OpenType = proto.Int32(3)
			} else {
				if len(mhDataItem.MarketURL) > 0 {
					tanxRespBidLanding.DeeplinkUri = proto.String(mhDataItem.MarketURL)
					tanxRespBidLanding.OpenType = proto.Int32(3)
				}
			}

			tanxRespBidLanding.ClickThroughUrl = []string{landpageURL}
		} else {
			if len(mhDataItem.MarketURL) > 0 {
				tanxRespBidLanding.DeeplinkUri = proto.String(mhDataItem.MarketURL)
				tanxRespBidLanding.OpenType = proto.Int32(3)
			} else {
				if len(mhDataItem.DeepLink) > 0 {
					tanxRespBidLanding.DeeplinkUri = proto.String(mhDataItem.DeepLink)
					tanxRespBidLanding.OpenType = proto.Int32(3)
				}
			}

			tanxRespBidLanding.ClickThroughUrl = []string{downloadURL}
		}

		tanxRespBid.Landing = &tanxRespBidLanding

		// app info object
		if mhDataItem.InteractType == 1 {
			tanxRespBidAppInfo := tanx_down.BidResponse_AppInfo{}
			tanxRespBidAppInfo.AppName = proto.String(mhDataItem.AppName)

			if reqOs == "android" {
				if len(mhDataItem.PackageName) > 0 {
					tanxRespBidAppInfo.PackageName = proto.String(mhDataItem.PackageName)
				}
				tanxRespBidAppInfo.DownloadType = proto.Int32(1)
			} else if reqOs == "ios" {
				tmpArray := strings.SplitN(downloadURL, "/id", 2)
				tmpArray = strings.SplitN(tmpArray[1], "?", 2)
				if len(tmpArray) > 0 {
					tanxRespBidAppInfo.PackageName = proto.String(tmpArray[0])
					tanxRespBidAppInfo.AppStoreId = proto.String(tmpArray[0])
				}
				tanxRespBidAppInfo.DownloadType = proto.Int32(2)
			}
			tanxRespBidAppInfo.DownloadUrl = proto.String(downloadURL)

			tanxRespBid.AppInfo = &tanxRespBidAppInfo
		}

		tanxRespSeatBid.ImpId = impItem.ImpId
		tanxRespSeatBid.Bids = append(tanxRespSeatBid.Bids, &tanxRespBid)
	}

	if len(tanxRespSeatBid.GetBids()) == 0 {
		return tanxNoBidReturn(tanxReq, "not fill")
	}

	// tanx resp
	tanxResp := &tanx_down.BidResponse{
		Version: tanxReq.Version,
		Id:      tanxReq.Id,
	}
	tanxResp.SeatBids = append(tanxResp.SeatBids, &tanxRespSeatBid)

	return tanxResp
}

func tanxNoBidReturn(tanxReq *tanx_down.BidRequest, reason string) *tanx_down.BidResponse {
	// fmt.Println(reason)

	tanxNoResp := &tanx_down.BidResponse{
		Version: tanxReq.Version,
		Id:      tanxReq.Id,
		// CacheDuration: proto.Int32(60),
	}
	return tanxNoResp
}

func getTanxAppList(appIDList []int32) []int {
	if len(appIDList) == 0 {
		return []int{}
	}

	var (
		appListIdArray []int
	)
	for _, appId := range appIDList {
		if v, ok := tanxAppListCodeMap[appId]; ok {
			appListIdArray = append(appListIdArray, int(v))
		}
	}

	return appListIdArray
}

// 用户已安装的app列表：
// 1-手淘
// 2-支付宝
// 3-饿了么
// 4-闲鱼
// 5-口碑
// 6-钉钉
// 7-飞猪
// 8-零售通
// 9-优酷
// 10-网易考拉
// 11-天猫
// 12-淘特
// 13-点淘
// 14-1688
// 15-夸克浏览器
// 16-盒马
// 17-菜鸟裹裹
// 101-拼多多
// 102-美团外卖
// 103-滴滴出行
// 104-京东
// 105-唯品会
// 106-爱奇艺
// 107-美团
// 109-手机百度
// 110-小红书
// 111-天眼查
// 112-携程
// 113-贝壳
var tanxAppListCodeMap = map[int32]int32{
	1:   1001,
	2:   1008,
	3:   1019,
	4:   1031,
	5:   1032,
	6:   1024,
	7:   1033,
	8:   1034,
	9:   1035,
	10:  1036,
	11:  1015,
	12:  1037,
	13:  1038,
	14:  1039,
	15:  1040,
	16:  1041,
	17:  1042,
	101: 1005,
	102: 1020,
	103: 1014,
	104: 1002,
	105: 1017,
	106: 1043,
	107: 1006,
	109: 1025,
	110: 1018,
	111: 1044,
	112: 1011,
	113: 1045,
}

// TanxWidthHeightByTemplateIDStu ...
type TanxWidthHeightByTemplateIDStu struct {
	TemplateID       string `json:"template_id"`
	MaterialType     int    `json:"material_type"` // 0 -> 图片; 1 -> 视频;
	AuditImageWidth  int    `json:"audit_image_width"`
	AuditImageHeight int    `json:"audit_image_height"`
	AuditVideoWidth  int    `json:"audit_video_width"`
	AuditVideoHeight int    `json:"audit_video_height"`
}

var TanxTemplateIDJson = `
		[
			{ "template_id":"1029", "audit_image_width": 0, "audit_image_height": 0, "audit_video_width": 1280, "audit_video_height": 720, "material_type": 1 },
			{ "template_id":"1380", "audit_image_width": 625, "audit_image_height": 350, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1342", "audit_image_width": 750, "audit_image_height": 200, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1925", "audit_image_width": 750, "audit_image_height": 320, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1441", "audit_image_width": 625, "audit_image_height": 350, "audit_video_width": 1920, "audit_video_height": 1080, "material_type": 1 },
			{ "template_id":"1000368", "audit_image_width": 960, "audit_image_height": 540, "audit_video_width": 960, "audit_video_height": 540, "material_type": 1 },
			{ "template_id":"1000370", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 1280, "audit_video_height": 720, "material_type": 1 },
			{ "template_id":"1000469", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"808", "audit_image_width": 1080, "audit_image_height": 2016, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000389", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1458", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1673", "audit_image_width": 960, "audit_image_height": 540, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000390", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 1280, "audit_video_height": 720, "material_type": 1 },
			{ "template_id":"1000369", "audit_image_width": 960, "audit_image_height": 540, "audit_video_width": 960, "audit_video_height": 540, "material_type": 1 },
			{ "template_id":"471", "audit_image_width": 1000, "audit_image_height": 560, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1927", "audit_image_width": 640, "audit_image_height": 360, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000120", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000515", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"1000216", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"1000730", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"612", "audit_image_width": 640, "audit_image_height": 400, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1893", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1908", "audit_image_width": 720, "audit_image_height": 360, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000736", "audit_image_width": 0, "audit_image_height": 0, "audit_video_width": 720, "audit_video_height": 360, "material_type": 1 },
			{ "template_id":"1000303", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1924", "audit_image_width": 720, "audit_image_height": 1080, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"439", "audit_image_width": 960, "audit_image_height": 540, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"654", "audit_image_width": 240, "audit_image_height": 180, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000463", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"1000965", "audit_image_width": 512, "audit_image_height": 512, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000701", "audit_image_width": 896, "audit_image_height": 504, "audit_video_width": 896, "audit_video_height": 504, "material_type": 1 },
			{ "template_id":"1000702", "audit_image_width": 896, "audit_image_height": 504, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000729", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000603", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000386", "audit_image_width": 1080, "audit_image_height": 1800, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10000097", "audit_image_width": 608, "audit_image_height": 608, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010433", "audit_image_width": 100, "audit_image_height": 100, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010548", "audit_image_width": 100, "audit_image_height": 100, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1001005", "audit_image_width": 640, "audit_image_height": 960, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1000537", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"1000727", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 1280, "audit_video_height": 720, "material_type": 1 },
			{ "template_id":"10010197", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010427", "audit_image_width": 1000, "audit_image_height": 560, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010428", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010424", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"10010401", "audit_image_width": 1080, "audit_image_height": 1920, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1333", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 720, "audit_video_height": 1280, "material_type": 1 },
			{ "template_id":"1446", "audit_image_width": 720, "audit_image_height": 1280, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1136", "audit_image_width": 592, "audit_image_height": 296, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1192", "audit_image_width": 592, "audit_image_height": 296, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1113", "audit_image_width": 580, "audit_image_height": 326, "audit_video_width": 580, "audit_video_height": 326, "material_type": 1 },
			{ "template_id":"1774", "audit_image_width": 580, "audit_image_height": 326, "audit_video_width": 580, "audit_video_height": 326, "material_type": 1 },
			{ "template_id":"1097", "audit_image_width": 640, "audit_image_height": 160, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1319", "audit_image_width": 1140, "audit_image_height": 640, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 },
			{ "template_id":"1819", "audit_image_width": 1140, "audit_image_height": 640, "audit_video_width": 1140, "audit_video_height": 640, "material_type": 1 },
			{ "template_id":"1427", "audit_image_width": 1280, "audit_image_height": 720, "audit_video_width": 0, "audit_video_height": 0, "material_type": 0 }
    	]
		`
