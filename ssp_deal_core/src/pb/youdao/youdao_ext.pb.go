// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        (unknown)
// source: youdao_ext.proto

package youdao

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// 视频物料的对接格式
type VideoFormat int32

const (
	// vast格式
	VideoFormat_VAST VideoFormat = 1
	// url格式
	VideoFormat_RAW VideoFormat = 2
)

// Enum value maps for VideoFormat.
var (
	VideoFormat_name = map[int32]string{
		1: "VAST",
		2: "RAW",
	}
	VideoFormat_value = map[string]int32{
		"VAST": 1,
		"RAW":  2,
	}
)

func (x VideoFormat) Enum() *VideoFormat {
	p := new(VideoFormat)
	*p = x
	return p
}

func (x VideoFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_ext_proto_enumTypes[0].Descriptor()
}

func (VideoFormat) Type() protoreflect.EnumType {
	return &file_youdao_ext_proto_enumTypes[0]
}

func (x VideoFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *VideoFormat) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = VideoFormat(num)
	return nil
}

// Deprecated: Use VideoFormat.Descriptor instead.
func (VideoFormat) EnumDescriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{0}
}

type VideoPlayTrackingEvent int32

const (
	// 视频开始播放
	VideoPlayTrackingEvent_START VideoPlayTrackingEvent = 1
	// 视频播放到1/4进度
	VideoPlayTrackingEvent_FIRST_QUARTILE VideoPlayTrackingEvent = 2
	// 视频播放到1/2进度
	VideoPlayTrackingEvent_MID_POINT VideoPlayTrackingEvent = 3
	// 视频播放到3/4进度
	VideoPlayTrackingEvent_THIRD_QUARTILE VideoPlayTrackingEvent = 4
	// 视频播放完成
	VideoPlayTrackingEvent_COMPLETE VideoPlayTrackingEvent = 5
	// 视频静音
	VideoPlayTrackingEvent_MUTE VideoPlayTrackingEvent = 6
	// 视频未静音
	VideoPlayTrackingEvent_UNMUTE VideoPlayTrackingEvent = 7
	// 视频暂停
	VideoPlayTrackingEvent_PAUSE VideoPlayTrackingEvent = 8
)

// Enum value maps for VideoPlayTrackingEvent.
var (
	VideoPlayTrackingEvent_name = map[int32]string{
		1: "START",
		2: "FIRST_QUARTILE",
		3: "MID_POINT",
		4: "THIRD_QUARTILE",
		5: "COMPLETE",
		6: "MUTE",
		7: "UNMUTE",
		8: "PAUSE",
	}
	VideoPlayTrackingEvent_value = map[string]int32{
		"START":          1,
		"FIRST_QUARTILE": 2,
		"MID_POINT":      3,
		"THIRD_QUARTILE": 4,
		"COMPLETE":       5,
		"MUTE":           6,
		"UNMUTE":         7,
		"PAUSE":          8,
	}
)

func (x VideoPlayTrackingEvent) Enum() *VideoPlayTrackingEvent {
	p := new(VideoPlayTrackingEvent)
	*p = x
	return p
}

func (x VideoPlayTrackingEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoPlayTrackingEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_ext_proto_enumTypes[1].Descriptor()
}

func (VideoPlayTrackingEvent) Type() protoreflect.EnumType {
	return &file_youdao_ext_proto_enumTypes[1]
}

func (x VideoPlayTrackingEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *VideoPlayTrackingEvent) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = VideoPlayTrackingEvent(num)
	return nil
}

// Deprecated: Use VideoPlayTrackingEvent.Descriptor instead.
func (VideoPlayTrackingEvent) EnumDescriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{1}
}

type UrlType int32

const (
	// 默认值，未声明点击url类型，按照老版本的逻辑处理
	UrlType_UNDEFINED UrlType = 0
	// 普通点击跳转落地页，
	UrlType_LANDINGPAGE UrlType = 1
	// 点击deeplink + fallback
	UrlType_DEEPLINK UrlType = 2
	// 点击ulink
	UrlType_ULINK UrlType = 3
)

// Enum value maps for UrlType.
var (
	UrlType_name = map[int32]string{
		0: "UNDEFINED",
		1: "LANDINGPAGE",
		2: "DEEPLINK",
		3: "ULINK",
	}
	UrlType_value = map[string]int32{
		"UNDEFINED":   0,
		"LANDINGPAGE": 1,
		"DEEPLINK":    2,
		"ULINK":       3,
	}
)

func (x UrlType) Enum() *UrlType {
	p := new(UrlType)
	*p = x
	return p
}

func (x UrlType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UrlType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_ext_proto_enumTypes[2].Descriptor()
}

func (UrlType) Type() protoreflect.EnumType {
	return &file_youdao_ext_proto_enumTypes[2]
}

func (x UrlType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *UrlType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = UrlType(num)
	return nil
}

// Deprecated: Use UrlType.Descriptor instead.
func (UrlType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{2}
}

type Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid    *string `protobuf:"bytes,1,opt,name=caid" json:"caid,omitempty"`
	CaidMd5 *string `protobuf:"bytes,2,opt,name=caid_md5,json=caidMd5" json:"caid_md5,omitempty"`
	Version *string `protobuf:"bytes,3,opt,name=version" json:"version,omitempty"`
}

func (x *Caid) Reset() {
	*x = Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Caid) ProtoMessage() {}

func (x *Caid) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Caid.ProtoReflect.Descriptor instead.
func (*Caid) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{0}
}

func (x *Caid) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *Caid) GetCaidMd5() string {
	if x != nil && x.CaidMd5 != nil {
		return *x.CaidMd5
	}
	return ""
}

func (x *Caid) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

type Paid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Paid    *string `protobuf:"bytes,1,opt,name=paid" json:"paid,omitempty"`
	Version *string `protobuf:"bytes,2,opt,name=version" json:"version,omitempty"`
}

func (x *Paid) Reset() {
	*x = Paid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Paid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Paid) ProtoMessage() {}

func (x *Paid) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Paid.ProtoReflect.Descriptor instead.
func (*Paid) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{1}
}

func (x *Paid) GetPaid() string {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return ""
}

func (x *Paid) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

type YdVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url *string `protobuf:"bytes,1,req,name=url" json:"url,omitempty"`
	// 视频时长,单位s
	Duration *int32 `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	Width    *int32 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	Height   *int32 `protobuf:"varint,4,opt,name=height" json:"height,omitempty"`
	// 视频文件大小，单位Byte
	Size          *int64               `protobuf:"varint,5,opt,name=size" json:"size,omitempty"`
	PlayTrackings []*VideoPlayTracking `protobuf:"bytes,6,rep,name=playTrackings" json:"playTrackings,omitempty"`
}

func (x *YdVideo) Reset() {
	*x = YdVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YdVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YdVideo) ProtoMessage() {}

func (x *YdVideo) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YdVideo.ProtoReflect.Descriptor instead.
func (*YdVideo) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{2}
}

func (x *YdVideo) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *YdVideo) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *YdVideo) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *YdVideo) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *YdVideo) GetSize() int64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *YdVideo) GetPlayTrackings() []*VideoPlayTracking {
	if x != nil {
		return x.PlayTrackings
	}
	return nil
}

type VideoPlayTracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event            *VideoPlayTrackingEvent `protobuf:"varint,1,req,name=event,enum=youdao.VideoPlayTrackingEvent" json:"event,omitempty"`
	EventTrackingUrl *string                 `protobuf:"bytes,2,req,name=eventTrackingUrl" json:"eventTrackingUrl,omitempty"`
}

func (x *VideoPlayTracking) Reset() {
	*x = VideoPlayTracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoPlayTracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayTracking) ProtoMessage() {}

func (x *VideoPlayTracking) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayTracking.ProtoReflect.Descriptor instead.
func (*VideoPlayTracking) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{3}
}

func (x *VideoPlayTracking) GetEvent() VideoPlayTrackingEvent {
	if x != nil && x.Event != nil {
		return *x.Event
	}
	return VideoPlayTrackingEvent_START
}

func (x *VideoPlayTracking) GetEventTrackingUrl() string {
	if x != nil && x.EventTrackingUrl != nil {
		return *x.EventTrackingUrl
	}
	return ""
}

// 下载应用的信息
type DownloadAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppIconImage *string `protobuf:"bytes,1,opt,name=appIconImage" json:"appIconImage,omitempty"`
	// 应⽤权限，DSP平台拿到的是数组格式的，使用该字段
	AppPermissionArray []*DownloadAppInfo_Permissions `protobuf:"bytes,2,rep,name=appPermissionArray" json:"appPermissionArray,omitempty"`
	// 隐私政策⽹址
	PrivacyPolicy *string `protobuf:"bytes,3,opt,name=privacyPolicy" json:"privacyPolicy,omitempty"`
	// 开发者名称
	DeveloperName *string `protobuf:"bytes,4,opt,name=developerName" json:"developerName,omitempty"`
	// 应⽤名称
	AppTitle *string `protobuf:"bytes,5,opt,name=appTitle" json:"appTitle,omitempty"`
	// 应用版本号
	AppVersion *string `protobuf:"bytes,6,opt,name=appVersion" json:"appVersion,omitempty"`
	// 应用权限链接，适配 BS
	AppPermission *string `protobuf:"bytes,7,opt,name=appPermission" json:"appPermission,omitempty"`
}

func (x *DownloadAppInfo) Reset() {
	*x = DownloadAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadAppInfo) ProtoMessage() {}

func (x *DownloadAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadAppInfo.ProtoReflect.Descriptor instead.
func (*DownloadAppInfo) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{4}
}

func (x *DownloadAppInfo) GetAppIconImage() string {
	if x != nil && x.AppIconImage != nil {
		return *x.AppIconImage
	}
	return ""
}

func (x *DownloadAppInfo) GetAppPermissionArray() []*DownloadAppInfo_Permissions {
	if x != nil {
		return x.AppPermissionArray
	}
	return nil
}

func (x *DownloadAppInfo) GetPrivacyPolicy() string {
	if x != nil && x.PrivacyPolicy != nil {
		return *x.PrivacyPolicy
	}
	return ""
}

func (x *DownloadAppInfo) GetDeveloperName() string {
	if x != nil && x.DeveloperName != nil {
		return *x.DeveloperName
	}
	return ""
}

func (x *DownloadAppInfo) GetAppTitle() string {
	if x != nil && x.AppTitle != nil {
		return *x.AppTitle
	}
	return ""
}

func (x *DownloadAppInfo) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *DownloadAppInfo) GetAppPermission() string {
	if x != nil && x.AppPermission != nil {
		return *x.AppPermission
	}
	return ""
}

type DownloadAppInfo_Permissions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionName *string `protobuf:"bytes,1,opt,name=permissionName" json:"permissionName,omitempty"`
	PermissionDesc *string `protobuf:"bytes,2,opt,name=permissionDesc" json:"permissionDesc,omitempty"`
}

func (x *DownloadAppInfo_Permissions) Reset() {
	*x = DownloadAppInfo_Permissions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_ext_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadAppInfo_Permissions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadAppInfo_Permissions) ProtoMessage() {}

func (x *DownloadAppInfo_Permissions) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_ext_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadAppInfo_Permissions.ProtoReflect.Descriptor instead.
func (*DownloadAppInfo_Permissions) Descriptor() ([]byte, []int) {
	return file_youdao_ext_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DownloadAppInfo_Permissions) GetPermissionName() string {
	if x != nil && x.PermissionName != nil {
		return *x.PermissionName
	}
	return ""
}

func (x *DownloadAppInfo_Permissions) GetPermissionDesc() string {
	if x != nil && x.PermissionDesc != nil {
		return *x.PermissionDesc
	}
	return ""
}

var file_youdao_ext_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*NativeRequest_Asset)(nil),
		ExtensionType: (*NativeRequest_Asset)(nil),
		Field:         100,
		Name:          "youdao.sasset",
		Tag:           "bytes,100,opt,name=sasset",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeRequest_Asset)(nil),
		ExtensionType: (*string)(nil),
		Field:         101,
		Name:          "youdao.label",
		Tag:           "bytes,101,opt,name=label",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeRequest_Asset)(nil),
		ExtensionType: (*VideoFormat)(nil),
		Field:         102,
		Name:          "youdao.videoFormat",
		Tag:           "varint,102,opt,name=videoFormat,enum=youdao.VideoFormat",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeRequest_Asset_Data)(nil),
		ExtensionType: (*int32)(nil),
		Field:         100,
		Name:          "youdao.dataAssetType",
		Tag:           "varint,100,opt,name=dataAssetType",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp)(nil),
		ExtensionType: (*int32)(nil),
		Field:         100,
		Name:          "youdao.ssid",
		Tag:           "varint,100,opt,name=ssid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp)(nil),
		ExtensionType: (*bool)(nil),
		Field:         101,
		Name:          "youdao.dlp",
		Tag:           "varint,101,opt,name=dlp",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp)(nil),
		ExtensionType: (*bool)(nil),
		Field:         102,
		Name:          "youdao.clp",
		Tag:           "varint,102,opt,name=clp",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp)(nil),
		ExtensionType: (*int64)(nil),
		Field:         103,
		Name:          "youdao.startTime",
		Tag:           "varint,103,opt,name=startTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp)(nil),
		ExtensionType: (*int64)(nil),
		Field:         104,
		Name:          "youdao.endTime",
		Tag:           "varint,104,opt,name=endTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Imp_Native)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         100,
		Name:          "youdao.battri",
		Tag:           "varint,100,rep,packed,name=battri",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest)(nil),
		ExtensionType: (*bool)(nil),
		Field:         100,
		Name:          "youdao.tfc",
		Tag:           "varint,100,opt,name=tfc,def=0",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest)(nil),
		ExtensionType: (*bool)(nil),
		Field:         101,
		Name:          "youdao.is_support_ulink",
		Tag:           "varint,101,opt,name=is_support_ulink",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         206,
		Name:          "youdao.caid",
		Tag:           "bytes,206,opt,name=caid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         207,
		Name:          "youdao.alid",
		Tag:           "bytes,207,opt,name=alid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         210,
		Name:          "youdao.idfv",
		Tag:           "bytes,210,opt,name=idfv",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         211,
		Name:          "youdao.localTzName",
		Tag:           "bytes,211,opt,name=localTzName",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*float64)(nil),
		Field:         212,
		Name:          "youdao.deviceStartUpTime",
		Tag:           "fixed64,212,opt,name=deviceStartUpTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*float64)(nil),
		Field:         213,
		Name:          "youdao.deviceUpdateTime",
		Tag:           "fixed64,213,opt,name=deviceUpdateTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int32)(nil),
		Field:         214,
		Name:          "youdao.cpuNumber",
		Tag:           "varint,214,opt,name=cpuNumber",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int64)(nil),
		Field:         215,
		Name:          "youdao.disktotal",
		Tag:           "varint,215,opt,name=disktotal",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int64)(nil),
		Field:         216,
		Name:          "youdao.memTotal",
		Tag:           "varint,216,opt,name=memTotal",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int32)(nil),
		Field:         217,
		Name:          "youdao.iosAuthStatus",
		Tag:           "varint,217,opt,name=iosAuthStatus",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         218,
		Name:          "youdao.bootMark",
		Tag:           "bytes,218,opt,name=bootMark",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         219,
		Name:          "youdao.updateMark",
		Tag:           "bytes,219,opt,name=updateMark",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         220,
		Name:          "youdao.country_code",
		Tag:           "bytes,220,opt,name=country_code",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         221,
		Name:          "youdao.language",
		Tag:           "bytes,221,opt,name=language",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         222,
		Name:          "youdao.phone_name",
		Tag:           "bytes,222,opt,name=phone_name",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         223,
		Name:          "youdao.phone_device_code",
		Tag:           "bytes,223,opt,name=phone_device_code",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         224,
		Name:          "youdao.device_name",
		Tag:           "bytes,224,opt,name=device_name",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         100,
		Name:          "youdao.did",
		Tag:           "bytes,100,opt,name=did",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         101,
		Name:          "youdao.dpid",
		Tag:           "bytes,101,opt,name=dpid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         102,
		Name:          "youdao.oaid",
		Tag:           "bytes,102,opt,name=oaid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int32)(nil),
		Field:         103,
		Name:          "youdao.media_age",
		Tag:           "varint,103,opt,name=media_age",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int32)(nil),
		Field:         104,
		Name:          "youdao.media_gender",
		Tag:           "varint,104,opt,name=media_gender",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         105,
		Name:          "youdao.media_portrait_keywords",
		Tag:           "bytes,105,opt,name=media_portrait_keywords",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         106,
		Name:          "youdao.dsid",
		Tag:           "bytes,106,opt,name=dsid",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*int64)(nil),
		Field:         107,
		Name:          "youdao.power_on_time",
		Tag:           "varint,107,opt,name=power_on_time",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         108,
		Name:          "youdao.imsi",
		Tag:           "bytes,108,opt,name=imsi",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         109,
		Name:          "youdao.rom_version",
		Tag:           "bytes,109,opt,name=rom_version",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*float64)(nil),
		Field:         110,
		Name:          "youdao.system_compiling_time",
		Tag:           "fixed64,110,opt,name=system_compiling_time",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: ([]*Caid)(nil),
		Field:         115,
		Name:          "youdao.caids",
		Tag:           "bytes,115,rep,name=caids",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: ([]*Paid)(nil),
		Field:         116,
		Name:          "youdao.paids",
		Tag:           "bytes,116,rep,name=paids",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         225,
		Name:          "youdao.idfa_md5",
		Tag:           "bytes,225,opt,name=idfa_md5",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidRequest_Device)(nil),
		ExtensionType: (*string)(nil),
		Field:         226,
		Name:          "youdao.oaid_md5",
		Tag:           "bytes,226,opt,name=oaid_md5",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidResponse_SeatBid_Bid)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         100,
		Name:          "youdao.attri",
		Tag:           "varint,100,rep,packed,name=attri",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidResponse_SeatBid_Bid)(nil),
		ExtensionType: (*int64)(nil),
		Field:         101,
		Name:          "youdao.deliveryStartTime",
		Tag:           "varint,101,opt,name=deliveryStartTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidResponse_SeatBid_Bid)(nil),
		ExtensionType: (*int64)(nil),
		Field:         102,
		Name:          "youdao.deliveryEndTime",
		Tag:           "varint,102,opt,name=deliveryEndTime",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidResponse_SeatBid_Bid)(nil),
		ExtensionType: (*string)(nil),
		Field:         103,
		Name:          "youdao.wechatOriginId",
		Tag:           "bytes,103,opt,name=wechatOriginId",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*BidResponse_SeatBid_Bid)(nil),
		ExtensionType: (*string)(nil),
		Field:         104,
		Name:          "youdao.wechatPath",
		Tag:           "bytes,104,opt,name=wechatPath",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         100,
		Name:          "youdao.dpTrackers",
		Tag:           "bytes,100,rep,name=dpTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         101,
		Name:          "youdao.dpFailedTrackers",
		Tag:           "bytes,101,rep,name=dpFailedTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         102,
		Name:          "youdao.downloadFinishTrackers",
		Tag:           "bytes,102,rep,name=downloadFinishTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: (*string)(nil),
		Field:         103,
		Name:          "youdao.videoPlayTracker",
		Tag:           "bytes,103,opt,name=videoPlayTracker",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         104,
		Name:          "youdao.installFinishTrackers",
		Tag:           "bytes,104,rep,name=installFinishTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         105,
		Name:          "youdao.dpSuccessTrackers",
		Tag:           "bytes,105,rep,name=dpSuccessTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         106,
		Name:          "youdao.dpNotInstallTrackers",
		Tag:           "bytes,106,rep,name=dpNotInstallTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         107,
		Name:          "youdao.downloadStartTrackers",
		Tag:           "bytes,107,rep,name=downloadStartTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         108,
		Name:          "youdao.installStartTrackers",
		Tag:           "bytes,108,rep,name=installStartTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         109,
		Name:          "youdao.wxTrackers",
		Tag:           "bytes,109,rep,name=wxTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         110,
		Name:          "youdao.wxSuccessTrackers",
		Tag:           "bytes,110,rep,name=wxSuccessTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         111,
		Name:          "youdao.wxFailedTrackers",
		Tag:           "bytes,111,rep,name=wxFailedTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: ([]string)(nil),
		Field:         112,
		Name:          "youdao.dpInstallTrackers",
		Tag:           "bytes,112,rep,name=dpInstallTrackers",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Link)(nil),
		ExtensionType: (*UrlType)(nil),
		Field:         113,
		Name:          "youdao.url_type",
		Tag:           "varint,113,opt,name=url_type,enum=youdao.UrlType,def=0",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse_Asset)(nil),
		ExtensionType: (*YdVideo)(nil),
		Field:         100,
		Name:          "youdao.ydVideo",
		Tag:           "bytes,100,opt,name=ydVideo",
		Filename:      "youdao_ext.proto",
	},
	{
		ExtendedType:  (*NativeResponse)(nil),
		ExtensionType: (*DownloadAppInfo)(nil),
		Field:         100,
		Name:          "youdao.downloadAppInfo",
		Tag:           "bytes,100,opt,name=downloadAppInfo",
		Filename:      "youdao_ext.proto",
	},
}

// Extension fields to NativeRequest_Asset.
var (
	// *
	// Standard asset.
	//
	// optional youdao.NativeRequest.Asset sasset = 100;
	E_Sasset = &file_youdao_ext_proto_extTypes[0]
	// *
	// the ad content map's key
	//
	// optional string label = 101;
	E_Label = &file_youdao_ext_proto_extTypes[1]
	// optional youdao.VideoFormat videoFormat = 102;
	E_VideoFormat = &file_youdao_ext_proto_extTypes[2]
)

// Extension fields to NativeRequest_Asset_Data.
var (
	// *
	// google openrtb NativeRequest.Asset.Data.type is enum, could not extended easily.
	// using int dataAssetType instead.
	// DSP will ignore NativeRequest.Asset.Data.type even request has to set it.
	//
	// optional int32 dataAssetType = 100;
	E_DataAssetType = &file_youdao_ext_proto_extTypes[3]
)

// Extension fields to BidRequest_Imp.
var (
	// Standard schema ID.
	//
	// optional int32 ssid = 100;
	E_Ssid = &file_youdao_ext_proto_extTypes[4]
	// 是否支持直接在客户端通过 url scheme 唤醒应用
	//
	// optional bool dlp = 101;
	E_Dlp = &file_youdao_ext_proto_extTypes[5]
	// 是否支持通过url scheme和普通网页的合成链接唤醒应用
	//
	// optional bool clp = 102;
	E_Clp = &file_youdao_ext_proto_extTypes[6]
	// 品牌广告投放的起止时间戳，单位：ms，为空则表示请求当前在投广告
	//
	// optional int64 startTime = 103;
	E_StartTime = &file_youdao_ext_proto_extTypes[7]
	// optional int64 endTime = 104;
	E_EndTime = &file_youdao_ext_proto_extTypes[8]
)

// Extension fields to BidRequest_Imp_Native.
var (
	// *
	// google openrtb BidRequest.Imp.Native.battr is enum, could not extended easily.
	// using battri to identify another battr
	//
	// repeated int32 battri = 100;
	E_Battri = &file_youdao_ext_proto_extTypes[9]
)

// Extension fields to BidRequest.
var (
	// *
	// trackFromClient标识对于本次竞价请求的获胜者，是否将从客户端发送tracker
	//
	// optional bool tfc = 100;
	E_Tfc = &file_youdao_ext_proto_extTypes[10]
	// 是否支持ulink
	//
	// optional bool is_support_ulink = 101;
	E_IsSupportUlink = &file_youdao_ext_proto_extTypes[11]
)

// Extension fields to BidRequest_Device.
var (
	// *
	// 广告协会设备id（ios）
	//
	// optional string caid = 206;
	E_Caid = &file_youdao_ext_proto_extTypes[12]
	// *
	// 阿里巴巴广告设备id（ios）
	//
	// optional string alid = 207;
	E_Alid = &file_youdao_ext_proto_extTypes[13]
	// *
	// ios 设备标识符
	//
	// optional string idfv = 210;
	E_Idfv = &file_youdao_ext_proto_extTypes[14]
	// *
	// local时区，eg："Asia/Shanghai"
	//
	// optional string localTzName = 211;
	E_LocalTzName = &file_youdao_ext_proto_extTypes[15]
	// *
	// 手机开机时间,格式eg：1591601319.629868，小数点前为秒级时间戳，小数点后到ns级别
	//
	// optional double deviceStartUpTime = 212;
	E_DeviceStartUpTime = &file_youdao_ext_proto_extTypes[16]
	// *
	// 系统版本更新时间,格式eg：1591601319.629868，小数点前为秒级时间戳，小数点后到ns级别
	//
	// optional double deviceUpdateTime = 213;
	E_DeviceUpdateTime = &file_youdao_ext_proto_extTypes[17]
	// *
	// cpu数目
	//
	// optional int32 cpuNumber = 214;
	E_CpuNumber = &file_youdao_ext_proto_extTypes[18]
	// *
	// 磁盘总空间，单位：字节
	//
	// optional int64 disktotal = 215;
	E_Disktotal = &file_youdao_ext_proto_extTypes[19]
	// *
	// 系统总内容空间，单位：字节
	//
	// optional int64 memTotal = 216;
	E_MemTotal = &file_youdao_ext_proto_extTypes[20]
	// *
	// ios广告标识授权情况
	//
	// optional int32 iosAuthStatus = 217;
	E_IosAuthStatus = &file_youdao_ext_proto_extTypes[21]
	// optional string bootMark = 218;
	E_BootMark = &file_youdao_ext_proto_extTypes[22]
	// optional string updateMark = 219;
	E_UpdateMark = &file_youdao_ext_proto_extTypes[23]
	// *
	// 国家代码,eg:cn
	//
	// optional string country_code = 220;
	E_CountryCode = &file_youdao_ext_proto_extTypes[24]
	// *
	// 设备语言（中文，英文，其他）
	//
	// optional string language = 221;
	E_Language = &file_youdao_ext_proto_extTypes[25]
	// *
	// 手机名称,eg: xxx的iphone
	//
	// optional string phone_name = 222;
	E_PhoneName = &file_youdao_ext_proto_extTypes[26]
	// *
	// iphone 内部设备代码
	//
	// optional string phone_device_code = 223;
	E_PhoneDeviceCode = &file_youdao_ext_proto_extTypes[27]
	// *
	// 设备名称信息，eg：iphone5,3
	//
	// optional string device_name = 224;
	E_DeviceName = &file_youdao_ext_proto_extTypes[28]
	// *
	// Hardware device ID (e.g., IMEI), in the clear (i.e., not hashed).
	//
	// optional string did = 100;
	E_Did = &file_youdao_ext_proto_extTypes[29]
	// *
	// Platform device ID (e.g., Android ID) in the clear (i.e., not hashed).
	//
	// optional string dpid = 101;
	E_Dpid = &file_youdao_ext_proto_extTypes[30]
	// optional string oaid = 102;
	E_Oaid = &file_youdao_ext_proto_extTypes[31]
	// *
	// 媒体传入的用户年龄
	//
	// optional int32 media_age = 103;
	E_MediaAge = &file_youdao_ext_proto_extTypes[32]
	// *
	// 媒体传入的用户性别 0:未知；1：男；2：女
	//
	// optional int32 media_gender = 104;
	E_MediaGender = &file_youdao_ext_proto_extTypes[33]
	// *
	// 媒体传入的用户画像关键词集合
	//
	// optional string media_portrait_keywords = 105;
	E_MediaPortraitKeywords = &file_youdao_ext_proto_extTypes[34]
	// *
	// 苹果账号id
	//
	// optional string dsid = 106;
	E_Dsid = &file_youdao_ext_proto_extTypes[35]
	// *
	// 打开手机到请求广告的时间长度，单位：ms
	//
	// optional int64 power_on_time = 107;
	E_PowerOnTime = &file_youdao_ext_proto_extTypes[36]
	// *
	// sim卡串号
	//
	// optional string imsi = 108;
	E_Imsi = &file_youdao_ext_proto_extTypes[37]
	// *
	// 手机ROM的版本
	//
	// optional string rom_version = 109;
	E_RomVersion = &file_youdao_ext_proto_extTypes[38]
	// *
	// 系统编译时间，手机ROM的编译时间
	//
	// optional double system_compiling_time = 110;
	E_SystemCompilingTime = &file_youdao_ext_proto_extTypes[39]
	// *
	// caid 列表，版本高的在前面
	//
	// repeated youdao.Caid caids = 115;
	E_Caids = &file_youdao_ext_proto_extTypes[40]
	// repeated youdao.Paid paids = 116;
	E_Paids = &file_youdao_ext_proto_extTypes[41]
	// optional string idfa_md5 = 225;
	E_IdfaMd5 = &file_youdao_ext_proto_extTypes[42]
	// optional string oaid_md5 = 226;
	E_OaidMd5 = &file_youdao_ext_proto_extTypes[43]
)

// Extension fields to BidResponse_SeatBid_Bid.
var (
	// *
	// google openrtb BidResponse.SeatBid.Bid.attri is enum, could not extended easily.
	// using attri to identify another attri
	//
	// repeated int32 attri = 100;
	E_Attri = &file_youdao_ext_proto_extTypes[44]
	// 品牌广告投放的起止时间戳，单位：ms，请求中startTime和endTime字段不为空时必填
	//
	// optional int64 deliveryStartTime = 101;
	E_DeliveryStartTime = &file_youdao_ext_proto_extTypes[45]
	// optional int64 deliveryEndTime = 102;
	E_DeliveryEndTime = &file_youdao_ext_proto_extTypes[46]
	// optional string wechatOriginId = 103;
	E_WechatOriginId = &file_youdao_ext_proto_extTypes[47]
	// optional string wechatPath = 104;
	E_WechatPath = &file_youdao_ext_proto_extTypes[48]
)

// Extension fields to NativeResponse_Link.
var (
	// *
	//
	//	尝试吊起deeplink
	//
	// repeated string dpTrackers = 100;
	E_DpTrackers = &file_youdao_ext_proto_extTypes[49]
	// *
	// deep link call failed trackers
	//
	// repeated string dpFailedTrackers = 101;
	E_DpFailedTrackers = &file_youdao_ext_proto_extTypes[50]
	// *
	// 下载完成的上报链接
	//
	// repeated string downloadFinishTrackers = 102;
	E_DownloadFinishTrackers = &file_youdao_ext_proto_extTypes[51]
	// 视频的播放监测链接，map形式，目前供信息流视频使用
	//
	// optional string videoPlayTracker = 103;
	E_VideoPlayTracker = &file_youdao_ext_proto_extTypes[52]
	// 用户点击安装并完成安装的上报链接
	//
	// repeated string installFinishTrackers = 104;
	E_InstallFinishTrackers = &file_youdao_ext_proto_extTypes[53]
	// deepLink调起成功的上报链接
	//
	// repeated string dpSuccessTrackers = 105;
	E_DpSuccessTrackers = &file_youdao_ext_proto_extTypes[54]
	// deepLink应用未安装导致调起失败的上报链接
	//
	// repeated string dpNotInstallTrackers = 106;
	E_DpNotInstallTrackers = &file_youdao_ext_proto_extTypes[55]
	// 应用开始下载的上报链接
	//
	// repeated string downloadStartTrackers = 107;
	E_DownloadStartTrackers = &file_youdao_ext_proto_extTypes[56]
	// 用户开始安装的上报链接
	//
	// repeated string installStartTrackers = 108;
	E_InstallStartTrackers = &file_youdao_ext_proto_extTypes[57]
	// 微信小程序尝试吊起上报链接
	//
	// repeated string wxTrackers = 109;
	E_WxTrackers = &file_youdao_ext_proto_extTypes[58]
	// 微信小程序吊起成功上报链接
	//
	// repeated string wxSuccessTrackers = 110;
	E_WxSuccessTrackers = &file_youdao_ext_proto_extTypes[59]
	// 微信小程序吊起失败上报链接
	//
	// repeated string wxFailedTrackers = 111;
	E_WxFailedTrackers = &file_youdao_ext_proto_extTypes[60]
	// deepLink安装成功上报链接
	//
	// repeated string dpInstallTrackers = 112;
	E_DpInstallTrackers = &file_youdao_ext_proto_extTypes[61]
	// 标记OpenRTB协议中NativeResponse.Link.url参数对应的类型：普通落地页，deeplink，ulink
	//
	// optional youdao.UrlType url_type = 113;
	E_UrlType = &file_youdao_ext_proto_extTypes[62]
)

// Extension fields to NativeResponse_Asset.
var (
	// optional youdao.YdVideo ydVideo = 100;
	E_YdVideo = &file_youdao_ext_proto_extTypes[63]
)

// Extension fields to NativeResponse.
var (
	// 工信部四件套要求：应⽤权限、隐私政策⽹址、开发者名称、应⽤名称
	//
	// optional youdao.DownloadAppInfo downloadAppInfo = 100;
	E_DownloadAppInfo = &file_youdao_ext_proto_extTypes[64]
)

var File_youdao_ext_proto protoreflect.FileDescriptor

var file_youdao_ext_proto_rawDesc = []byte{
	0x0a, 0x10, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x5f, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x1a, 0x0c, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64,
	0x12, 0x0c, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10,
	0x0a, 0x08, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x0f, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x22, 0x25, 0x0a, 0x04, 0x50, 0x61, 0x69, 0x64, 0x12, 0x0c, 0x0a, 0x04, 0x70, 0x61, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x22, 0x87, 0x01, 0x0a, 0x07, 0x59, 0x64, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x12, 0x0b, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x12, 0x10, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x12, 0x0d, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x12, 0x0e, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x12, 0x0c, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x12, 0x30, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x22, 0x5c, 0x0a, 0x11, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x10, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09,
	0x22, 0x92, 0x02, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x3f, 0x0a, 0x12, 0x61, 0x70,
	0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x72, 0x61, 0x79,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x15, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x15, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x1a, 0x3d, 0x0a, 0x0b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x16, 0x0a,
	0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x2a, 0x20, 0x0a, 0x0b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x08, 0x0a, 0x04, 0x56, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x07,
	0x0a, 0x03, 0x52, 0x41, 0x57, 0x10, 0x02, 0x2a, 0x89, 0x01, 0x0a, 0x16, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x49, 0x4c, 0x45, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x49, 0x44, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x49,
	0x4c, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45,
	0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x55, 0x54, 0x45, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06,
	0x55, 0x4e, 0x4d, 0x55, 0x54, 0x45, 0x10, 0x07, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x55, 0x53,
	0x45, 0x10, 0x08, 0x2a, 0x42, 0x0a, 0x07, 0x55, 0x72, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d,
	0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x50, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05,
	0x55, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x03, 0x3a, 0x48, 0x0a, 0x06, 0x73, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x3a, 0x2a, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x45, 0x0a,
	0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1b, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x66, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x3a, 0x37, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x18, 0x64, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x24, 0x0a,
	0x04, 0x73, 0x73, 0x69, 0x64, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x18, 0x64, 0x20,
	0x01, 0x28, 0x05, 0x3a, 0x23, 0x0a, 0x03, 0x64, 0x6c, 0x70, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x18, 0x65, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x23, 0x0a, 0x03, 0x63, 0x6c, 0x70, 0x12,
	0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x18, 0x66, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x29, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x18, 0x67, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x27, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x18, 0x68, 0x20, 0x01, 0x28,
	0x03, 0x3a, 0x31, 0x0a, 0x06, 0x62, 0x61, 0x74, 0x74, 0x72, 0x69, 0x12, 0x1d, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x64, 0x20, 0x03, 0x28, 0x05,
	0x42, 0x02, 0x10, 0x01, 0x3a, 0x26, 0x0a, 0x03, 0x74, 0x66, 0x63, 0x12, 0x12, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x64, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x3a, 0x2c, 0x0a, 0x10,
	0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x12, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x65, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x28, 0x0a, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xce, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x3a, 0x28, 0x0a, 0x04, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x19, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xcf, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x28,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x18, 0xd2, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x2f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x54, 0x7a, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0xd3, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x35, 0x0a, 0x11, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xd4, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x3a, 0x34, 0x0a, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0xd5, 0x01, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x2d, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xd6,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x2d, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xd7, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x3a, 0x2c, 0x0a, 0x08, 0x6d, 0x65, 0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xd8, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x3a, 0x31, 0x0a, 0x0d, 0x69, 0x6f, 0x73, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xd9,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x2c, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72,
	0x6b, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xda, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x2e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72,
	0x6b, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xdb, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x30, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xdc,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x2c, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xdd, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x2e, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xde, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x35, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0xdf, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x2f, 0x0a, 0x0b, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0xe0, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x26, 0x0a, 0x03, 0x64,
	0x69, 0x64, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x64, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x27, 0x0a, 0x04, 0x64, 0x70, 0x69, 0x64, 0x12, 0x19, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x27, 0x0a, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x66, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x2c, 0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61,
	0x67, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x67, 0x20,
	0x01, 0x28, 0x05, 0x3a, 0x2f, 0x0a, 0x0c, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x68,
	0x20, 0x01, 0x28, 0x05, 0x3a, 0x3a, 0x0a, 0x17, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x72, 0x61, 0x69, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12,
	0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x69, 0x20, 0x01, 0x28, 0x09,
	0x3a, 0x27, 0x0a, 0x04, 0x64, 0x73, 0x69, 0x64, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x30, 0x0a, 0x0d, 0x70, 0x6f, 0x77,
	0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x6b, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x27, 0x0a, 0x04, 0x69,
	0x6d, 0x73, 0x69, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x6c,
	0x20, 0x01, 0x28, 0x09, 0x3a, 0x2e, 0x0a, 0x0b, 0x72, 0x6f, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x6d,
	0x20, 0x01, 0x28, 0x09, 0x3a, 0x38, 0x0a, 0x15, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x36,
	0x0a, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x73, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x3a, 0x36, 0x0a, 0x05, 0x70, 0x61, 0x69, 0x64, 0x73, 0x12,
	0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x74, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x50, 0x61, 0x69, 0x64, 0x3a, 0x2c,
	0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0xe1, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x2c, 0x0a, 0x08,
	0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x12, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0xe2, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x32, 0x0a, 0x05, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x12, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x2e, 0x42, 0x69, 0x64, 0x18, 0x64, 0x20, 0x03, 0x28, 0x05, 0x42, 0x02, 0x10, 0x01, 0x3a, 0x3a,
	0x0a, 0x11, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x2e, 0x42, 0x69, 0x64, 0x18, 0x65, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x38, 0x0a, 0x0f, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x18, 0x66,
	0x20, 0x01, 0x28, 0x03, 0x3a, 0x37, 0x0a, 0x0e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74,
	0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x33, 0x0a,
	0x0a, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x18, 0x68, 0x20, 0x01,
	0x28, 0x09, 0x3a, 0x2f, 0x0a, 0x0a, 0x64, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73,
	0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x64, 0x20,
	0x03, 0x28, 0x09, 0x3a, 0x35, 0x0a, 0x10, 0x64, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x65, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x3b, 0x0a, 0x16, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e,
	0x6b, 0x18, 0x66, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x35, 0x0a, 0x10, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x50, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x1b, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x3a,
	0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x68, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x36, 0x0a, 0x11, 0x64, 0x70,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12,
	0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x69, 0x20, 0x03,
	0x28, 0x09, 0x3a, 0x39, 0x0a, 0x14, 0x64, 0x70, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x6a, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x3a, 0x0a,
	0x15, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c,
	0x69, 0x6e, 0x6b, 0x18, 0x6b, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x39, 0x0a, 0x14, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x6c,
	0x20, 0x03, 0x28, 0x09, 0x3a, 0x2f, 0x0a, 0x0a, 0x77, 0x78, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18,
	0x6d, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x36, 0x0a, 0x11, 0x77, 0x78, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x6e, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x35, 0x0a,
	0x10, 0x77, 0x78, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x6f,
	0x20, 0x03, 0x28, 0x09, 0x3a, 0x36, 0x0a, 0x11, 0x64, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x70, 0x20, 0x03, 0x28, 0x09, 0x3a, 0x49, 0x0a, 0x08,
	0x75, 0x72, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x71, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x55, 0x72, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x09, 0x55, 0x4e,
	0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x3a, 0x3e, 0x0a, 0x07, 0x79, 0x64, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x1c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x59, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x3a, 0x48, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x1f, 0x42, 0x12, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x74, 0x62, 0x59, 0x44, 0x45, 0x78,
	0x74, 0x46, 0x6f, 0x72, 0x44, 0x73, 0x70, 0x5a, 0x09, 0x2e, 0x2e, 0x2f, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f,
}

var (
	file_youdao_ext_proto_rawDescOnce sync.Once
	file_youdao_ext_proto_rawDescData = file_youdao_ext_proto_rawDesc
)

func file_youdao_ext_proto_rawDescGZIP() []byte {
	file_youdao_ext_proto_rawDescOnce.Do(func() {
		file_youdao_ext_proto_rawDescData = protoimpl.X.CompressGZIP(file_youdao_ext_proto_rawDescData)
	})
	return file_youdao_ext_proto_rawDescData
}

var file_youdao_ext_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_youdao_ext_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_youdao_ext_proto_goTypes = []interface{}{
	(VideoFormat)(0),                    // 0: youdao.VideoFormat
	(VideoPlayTrackingEvent)(0),         // 1: youdao.VideoPlayTrackingEvent
	(UrlType)(0),                        // 2: youdao.UrlType
	(*Caid)(nil),                        // 3: youdao.Caid
	(*Paid)(nil),                        // 4: youdao.Paid
	(*YdVideo)(nil),                     // 5: youdao.YdVideo
	(*VideoPlayTracking)(nil),           // 6: youdao.VideoPlayTracking
	(*DownloadAppInfo)(nil),             // 7: youdao.DownloadAppInfo
	(*DownloadAppInfo_Permissions)(nil), // 8: youdao.DownloadAppInfo.Permissions
	(*NativeRequest_Asset)(nil),         // 9: youdao.NativeRequest.Asset
	(*NativeRequest_Asset_Data)(nil),    // 10: youdao.NativeRequest.Asset.Data
	(*BidRequest_Imp)(nil),              // 11: youdao.BidRequest.Imp
	(*BidRequest_Imp_Native)(nil),       // 12: youdao.BidRequest.Imp.Native
	(*BidRequest)(nil),                  // 13: youdao.BidRequest
	(*BidRequest_Device)(nil),           // 14: youdao.BidRequest.Device
	(*BidResponse_SeatBid_Bid)(nil),     // 15: youdao.BidResponse.SeatBid.Bid
	(*NativeResponse_Link)(nil),         // 16: youdao.NativeResponse.Link
	(*NativeResponse_Asset)(nil),        // 17: youdao.NativeResponse.Asset
	(*NativeResponse)(nil),              // 18: youdao.NativeResponse
}
var file_youdao_ext_proto_depIdxs = []int32{
	6,  // 0: youdao.YdVideo.playTrackings:type_name -> youdao.VideoPlayTracking
	1,  // 1: youdao.VideoPlayTracking.event:type_name -> youdao.VideoPlayTrackingEvent
	8,  // 2: youdao.DownloadAppInfo.appPermissionArray:type_name -> youdao.DownloadAppInfo.Permissions
	9,  // 3: youdao.sasset:extendee -> youdao.NativeRequest.Asset
	9,  // 4: youdao.label:extendee -> youdao.NativeRequest.Asset
	9,  // 5: youdao.videoFormat:extendee -> youdao.NativeRequest.Asset
	10, // 6: youdao.dataAssetType:extendee -> youdao.NativeRequest.Asset.Data
	11, // 7: youdao.ssid:extendee -> youdao.BidRequest.Imp
	11, // 8: youdao.dlp:extendee -> youdao.BidRequest.Imp
	11, // 9: youdao.clp:extendee -> youdao.BidRequest.Imp
	11, // 10: youdao.startTime:extendee -> youdao.BidRequest.Imp
	11, // 11: youdao.endTime:extendee -> youdao.BidRequest.Imp
	12, // 12: youdao.battri:extendee -> youdao.BidRequest.Imp.Native
	13, // 13: youdao.tfc:extendee -> youdao.BidRequest
	13, // 14: youdao.is_support_ulink:extendee -> youdao.BidRequest
	14, // 15: youdao.caid:extendee -> youdao.BidRequest.Device
	14, // 16: youdao.alid:extendee -> youdao.BidRequest.Device
	14, // 17: youdao.idfv:extendee -> youdao.BidRequest.Device
	14, // 18: youdao.localTzName:extendee -> youdao.BidRequest.Device
	14, // 19: youdao.deviceStartUpTime:extendee -> youdao.BidRequest.Device
	14, // 20: youdao.deviceUpdateTime:extendee -> youdao.BidRequest.Device
	14, // 21: youdao.cpuNumber:extendee -> youdao.BidRequest.Device
	14, // 22: youdao.disktotal:extendee -> youdao.BidRequest.Device
	14, // 23: youdao.memTotal:extendee -> youdao.BidRequest.Device
	14, // 24: youdao.iosAuthStatus:extendee -> youdao.BidRequest.Device
	14, // 25: youdao.bootMark:extendee -> youdao.BidRequest.Device
	14, // 26: youdao.updateMark:extendee -> youdao.BidRequest.Device
	14, // 27: youdao.country_code:extendee -> youdao.BidRequest.Device
	14, // 28: youdao.language:extendee -> youdao.BidRequest.Device
	14, // 29: youdao.phone_name:extendee -> youdao.BidRequest.Device
	14, // 30: youdao.phone_device_code:extendee -> youdao.BidRequest.Device
	14, // 31: youdao.device_name:extendee -> youdao.BidRequest.Device
	14, // 32: youdao.did:extendee -> youdao.BidRequest.Device
	14, // 33: youdao.dpid:extendee -> youdao.BidRequest.Device
	14, // 34: youdao.oaid:extendee -> youdao.BidRequest.Device
	14, // 35: youdao.media_age:extendee -> youdao.BidRequest.Device
	14, // 36: youdao.media_gender:extendee -> youdao.BidRequest.Device
	14, // 37: youdao.media_portrait_keywords:extendee -> youdao.BidRequest.Device
	14, // 38: youdao.dsid:extendee -> youdao.BidRequest.Device
	14, // 39: youdao.power_on_time:extendee -> youdao.BidRequest.Device
	14, // 40: youdao.imsi:extendee -> youdao.BidRequest.Device
	14, // 41: youdao.rom_version:extendee -> youdao.BidRequest.Device
	14, // 42: youdao.system_compiling_time:extendee -> youdao.BidRequest.Device
	14, // 43: youdao.caids:extendee -> youdao.BidRequest.Device
	14, // 44: youdao.paids:extendee -> youdao.BidRequest.Device
	14, // 45: youdao.idfa_md5:extendee -> youdao.BidRequest.Device
	14, // 46: youdao.oaid_md5:extendee -> youdao.BidRequest.Device
	15, // 47: youdao.attri:extendee -> youdao.BidResponse.SeatBid.Bid
	15, // 48: youdao.deliveryStartTime:extendee -> youdao.BidResponse.SeatBid.Bid
	15, // 49: youdao.deliveryEndTime:extendee -> youdao.BidResponse.SeatBid.Bid
	15, // 50: youdao.wechatOriginId:extendee -> youdao.BidResponse.SeatBid.Bid
	15, // 51: youdao.wechatPath:extendee -> youdao.BidResponse.SeatBid.Bid
	16, // 52: youdao.dpTrackers:extendee -> youdao.NativeResponse.Link
	16, // 53: youdao.dpFailedTrackers:extendee -> youdao.NativeResponse.Link
	16, // 54: youdao.downloadFinishTrackers:extendee -> youdao.NativeResponse.Link
	16, // 55: youdao.videoPlayTracker:extendee -> youdao.NativeResponse.Link
	16, // 56: youdao.installFinishTrackers:extendee -> youdao.NativeResponse.Link
	16, // 57: youdao.dpSuccessTrackers:extendee -> youdao.NativeResponse.Link
	16, // 58: youdao.dpNotInstallTrackers:extendee -> youdao.NativeResponse.Link
	16, // 59: youdao.downloadStartTrackers:extendee -> youdao.NativeResponse.Link
	16, // 60: youdao.installStartTrackers:extendee -> youdao.NativeResponse.Link
	16, // 61: youdao.wxTrackers:extendee -> youdao.NativeResponse.Link
	16, // 62: youdao.wxSuccessTrackers:extendee -> youdao.NativeResponse.Link
	16, // 63: youdao.wxFailedTrackers:extendee -> youdao.NativeResponse.Link
	16, // 64: youdao.dpInstallTrackers:extendee -> youdao.NativeResponse.Link
	16, // 65: youdao.url_type:extendee -> youdao.NativeResponse.Link
	17, // 66: youdao.ydVideo:extendee -> youdao.NativeResponse.Asset
	18, // 67: youdao.downloadAppInfo:extendee -> youdao.NativeResponse
	9,  // 68: youdao.sasset:type_name -> youdao.NativeRequest.Asset
	0,  // 69: youdao.videoFormat:type_name -> youdao.VideoFormat
	3,  // 70: youdao.caids:type_name -> youdao.Caid
	4,  // 71: youdao.paids:type_name -> youdao.Paid
	2,  // 72: youdao.url_type:type_name -> youdao.UrlType
	5,  // 73: youdao.ydVideo:type_name -> youdao.YdVideo
	7,  // 74: youdao.downloadAppInfo:type_name -> youdao.DownloadAppInfo
	75, // [75:75] is the sub-list for method output_type
	75, // [75:75] is the sub-list for method input_type
	68, // [68:75] is the sub-list for extension type_name
	3,  // [3:68] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_youdao_ext_proto_init() }
func file_youdao_ext_proto_init() {
	if File_youdao_ext_proto != nil {
		return
	}
	file_youdao_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_youdao_ext_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_youdao_ext_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Paid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_youdao_ext_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YdVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_youdao_ext_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoPlayTracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_youdao_ext_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_youdao_ext_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadAppInfo_Permissions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_youdao_ext_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   6,
			NumExtensions: 65,
			NumServices:   0,
		},
		GoTypes:           file_youdao_ext_proto_goTypes,
		DependencyIndexes: file_youdao_ext_proto_depIdxs,
		EnumInfos:         file_youdao_ext_proto_enumTypes,
		MessageInfos:      file_youdao_ext_proto_msgTypes,
		ExtensionInfos:    file_youdao_ext_proto_extTypes,
	}.Build()
	File_youdao_ext_proto = out.File
	file_youdao_ext_proto_rawDesc = nil
	file_youdao_ext_proto_goTypes = nil
	file_youdao_ext_proto_depIdxs = nil
}
