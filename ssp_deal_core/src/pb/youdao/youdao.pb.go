// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        (unknown)
// source: youdao.proto

package youdao

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OpenRTB 5.1: The following list represents the IAB's contextual taxonomy for
// categorization. Standard IDs have been adopted to easily support the
// communication of primary and secondary categories for various objects.
//
// This OpenRTB table has values derived from the IAB Quality Assurance
// Guidelines (QAG). Practitioners should keep in sync with updates to the
// QAG values as published on IAB.net.
type ContentCategory int32

const (
	ContentCategory_IAB1     ContentCategory = 1   // Arts & Entertainment
	ContentCategory_IAB1_1   ContentCategory = 2   // Books & Literature
	ContentCategory_IAB1_2   ContentCategory = 3   // Celebrity Fan/Gossip
	ContentCategory_IAB1_3   ContentCategory = 4   // Fine Art
	ContentCategory_IAB1_4   ContentCategory = 5   // Humor
	ContentCategory_IAB1_5   ContentCategory = 6   // Movies
	ContentCategory_IAB1_6   ContentCategory = 7   // Music
	ContentCategory_IAB1_7   ContentCategory = 8   // Television
	ContentCategory_IAB2     ContentCategory = 9   // Automotive
	ContentCategory_IAB2_1   ContentCategory = 10  // Auto Parts
	ContentCategory_IAB2_2   ContentCategory = 11  // Auto Repair
	ContentCategory_IAB2_3   ContentCategory = 12  // Buying/Selling Cars
	ContentCategory_IAB2_4   ContentCategory = 13  // Car Culture
	ContentCategory_IAB2_5   ContentCategory = 14  // Certified Pre-Owned
	ContentCategory_IAB2_6   ContentCategory = 15  // Convertible
	ContentCategory_IAB2_7   ContentCategory = 16  // Coupe
	ContentCategory_IAB2_8   ContentCategory = 17  // Crossover
	ContentCategory_IAB2_9   ContentCategory = 18  // Diesel
	ContentCategory_IAB2_10  ContentCategory = 19  // Electric Vehicle
	ContentCategory_IAB2_11  ContentCategory = 20  // Hatchback
	ContentCategory_IAB2_12  ContentCategory = 21  // Hybrid
	ContentCategory_IAB2_13  ContentCategory = 22  // Luxury
	ContentCategory_IAB2_14  ContentCategory = 23  // MiniVan
	ContentCategory_IAB2_15  ContentCategory = 24  // Mororcycles
	ContentCategory_IAB2_16  ContentCategory = 25  // Off-Road Vehicles
	ContentCategory_IAB2_17  ContentCategory = 26  // Performance Vehicles
	ContentCategory_IAB2_18  ContentCategory = 27  // Pickup
	ContentCategory_IAB2_19  ContentCategory = 28  // Road-Side Assistance
	ContentCategory_IAB2_20  ContentCategory = 29  // Sedan
	ContentCategory_IAB2_21  ContentCategory = 30  // Trucks & Accessories
	ContentCategory_IAB2_22  ContentCategory = 31  // Vintage Cars
	ContentCategory_IAB2_23  ContentCategory = 32  // Wagon
	ContentCategory_IAB3     ContentCategory = 33  // Business
	ContentCategory_IAB3_1   ContentCategory = 34  // Advertising
	ContentCategory_IAB3_2   ContentCategory = 35  // Agriculture
	ContentCategory_IAB3_3   ContentCategory = 36  // Biotech/Biomedical
	ContentCategory_IAB3_4   ContentCategory = 37  // Business Software
	ContentCategory_IAB3_5   ContentCategory = 38  // Construction
	ContentCategory_IAB3_6   ContentCategory = 39  // Forestry
	ContentCategory_IAB3_7   ContentCategory = 40  // Government
	ContentCategory_IAB3_8   ContentCategory = 41  // Green Solutions
	ContentCategory_IAB3_9   ContentCategory = 42  // Human Resources
	ContentCategory_IAB3_10  ContentCategory = 43  // Logistics
	ContentCategory_IAB3_11  ContentCategory = 44  // Marketing
	ContentCategory_IAB3_12  ContentCategory = 45  // Metals
	ContentCategory_IAB4     ContentCategory = 46  // Careers
	ContentCategory_IAB4_1   ContentCategory = 47  // Career Planning
	ContentCategory_IAB4_2   ContentCategory = 48  // College
	ContentCategory_IAB4_3   ContentCategory = 49  // Financial  Aid
	ContentCategory_IAB4_4   ContentCategory = 50  // Job Fairs
	ContentCategory_IAB4_5   ContentCategory = 51  // Job Search
	ContentCategory_IAB4_6   ContentCategory = 52  // Resume Writing/Advice
	ContentCategory_IAB4_7   ContentCategory = 53  // Nursing
	ContentCategory_IAB4_8   ContentCategory = 54  // Scholarships
	ContentCategory_IAB4_9   ContentCategory = 55  // Telecommuting
	ContentCategory_IAB4_10  ContentCategory = 56  // U.S. Military
	ContentCategory_IAB4_11  ContentCategory = 57  // Career Advice
	ContentCategory_IAB5     ContentCategory = 58  // Education
	ContentCategory_IAB5_1   ContentCategory = 59  // 7-12 Education
	ContentCategory_IAB5_2   ContentCategory = 60  // Adult Education
	ContentCategory_IAB5_3   ContentCategory = 61  // Art History
	ContentCategory_IAB5_4   ContentCategory = 62  // Colledge Administration
	ContentCategory_IAB5_5   ContentCategory = 63  // College Life
	ContentCategory_IAB5_6   ContentCategory = 64  // Distance Learning
	ContentCategory_IAB5_7   ContentCategory = 65  // English as a 2nd Language
	ContentCategory_IAB5_8   ContentCategory = 66  // Language Learning
	ContentCategory_IAB5_9   ContentCategory = 67  // Graduate School
	ContentCategory_IAB5_10  ContentCategory = 68  // Homeschooling
	ContentCategory_IAB5_11  ContentCategory = 69  // Homework/Study Tips
	ContentCategory_IAB5_12  ContentCategory = 70  // K-6 Educators
	ContentCategory_IAB5_13  ContentCategory = 71  // Private School
	ContentCategory_IAB5_14  ContentCategory = 72  // Special Education
	ContentCategory_IAB5_15  ContentCategory = 73  // Studying Business
	ContentCategory_IAB6     ContentCategory = 74  // Family & Parenting
	ContentCategory_IAB6_1   ContentCategory = 75  // Adoption
	ContentCategory_IAB6_2   ContentCategory = 76  // Babies & Toddlers
	ContentCategory_IAB6_3   ContentCategory = 77  // Daycare/Pre School
	ContentCategory_IAB6_4   ContentCategory = 78  // Family Internet
	ContentCategory_IAB6_5   ContentCategory = 79  // Parenting - K-6 Kids
	ContentCategory_IAB6_6   ContentCategory = 80  // Parenting teens
	ContentCategory_IAB6_7   ContentCategory = 81  // Pregnancy
	ContentCategory_IAB6_8   ContentCategory = 82  // Special Needs Kids
	ContentCategory_IAB6_9   ContentCategory = 83  // Eldercare
	ContentCategory_IAB7     ContentCategory = 84  // Health & Fitness
	ContentCategory_IAB7_1   ContentCategory = 85  // Exercise
	ContentCategory_IAB7_2   ContentCategory = 86  // A.D.D.
	ContentCategory_IAB7_3   ContentCategory = 87  // AIDS/HIV
	ContentCategory_IAB7_4   ContentCategory = 88  // Allergies
	ContentCategory_IAB7_5   ContentCategory = 89  // Alternative Medicine
	ContentCategory_IAB7_6   ContentCategory = 90  // Arthritis
	ContentCategory_IAB7_7   ContentCategory = 91  // Asthma
	ContentCategory_IAB7_8   ContentCategory = 92  // Autism/PDD
	ContentCategory_IAB7_9   ContentCategory = 93  // Bipolar Disorder
	ContentCategory_IAB7_10  ContentCategory = 94  // Brain Tumor
	ContentCategory_IAB7_11  ContentCategory = 95  // Cancer
	ContentCategory_IAB7_12  ContentCategory = 96  // Cholesterol
	ContentCategory_IAB7_13  ContentCategory = 97  // Chronic Fatigue Syndrome
	ContentCategory_IAB7_14  ContentCategory = 98  // Chronic Pain
	ContentCategory_IAB7_15  ContentCategory = 99  // Cold & Flu
	ContentCategory_IAB7_16  ContentCategory = 100 // Deafness
	ContentCategory_IAB7_17  ContentCategory = 101 // Dental Care
	ContentCategory_IAB7_18  ContentCategory = 102 // Depression
	ContentCategory_IAB7_19  ContentCategory = 103 // Dermatology
	ContentCategory_IAB7_20  ContentCategory = 104 // Diabetes
	ContentCategory_IAB7_21  ContentCategory = 105 // Epilepsy
	ContentCategory_IAB7_22  ContentCategory = 106 // GERD/Acid Reflux
	ContentCategory_IAB7_23  ContentCategory = 107 // Headaches/Migraines
	ContentCategory_IAB7_24  ContentCategory = 108 // Heart Disease
	ContentCategory_IAB7_25  ContentCategory = 109 // Herbs for Health
	ContentCategory_IAB7_26  ContentCategory = 110 // Holistic Healing
	ContentCategory_IAB7_27  ContentCategory = 111 // IBS/Crohn's Disease
	ContentCategory_IAB7_28  ContentCategory = 112 // Incest/Abuse Support
	ContentCategory_IAB7_29  ContentCategory = 113 // Incontinence
	ContentCategory_IAB7_30  ContentCategory = 114 // Infertility
	ContentCategory_IAB7_31  ContentCategory = 115 // Men's Health
	ContentCategory_IAB7_32  ContentCategory = 116 // Nutrition
	ContentCategory_IAB7_33  ContentCategory = 117 // Orthopedics
	ContentCategory_IAB7_34  ContentCategory = 118 // Panic/Anxiety Disorders
	ContentCategory_IAB7_35  ContentCategory = 119 // Pediatrics
	ContentCategory_IAB7_36  ContentCategory = 120 // Physical Therapy
	ContentCategory_IAB7_37  ContentCategory = 121 // Psychology/Psychiatry
	ContentCategory_IAB7_38  ContentCategory = 122 // Senor Health
	ContentCategory_IAB7_39  ContentCategory = 123 // Sexuality
	ContentCategory_IAB7_40  ContentCategory = 124 // Sleep Disorders
	ContentCategory_IAB7_41  ContentCategory = 125 // Smoking Cessation
	ContentCategory_IAB7_42  ContentCategory = 126 // Substance Abuse
	ContentCategory_IAB7_43  ContentCategory = 127 // Thyroid Disease
	ContentCategory_IAB7_44  ContentCategory = 128 // Weight Loss
	ContentCategory_IAB7_45  ContentCategory = 129 // Women's Health
	ContentCategory_IAB8     ContentCategory = 130 // Food & Drink
	ContentCategory_IAB8_1   ContentCategory = 131 // American Cuisine
	ContentCategory_IAB8_2   ContentCategory = 132 // Barbecues & Grilling
	ContentCategory_IAB8_3   ContentCategory = 133 // Cajun/Creole
	ContentCategory_IAB8_4   ContentCategory = 134 // Chinese Cuisine
	ContentCategory_IAB8_5   ContentCategory = 135 // Cocktails/Beer
	ContentCategory_IAB8_6   ContentCategory = 136 // Coffee/Tea
	ContentCategory_IAB8_7   ContentCategory = 137 // Cuisine-Specific
	ContentCategory_IAB8_8   ContentCategory = 138 // Desserts & Baking
	ContentCategory_IAB8_9   ContentCategory = 139 // Dining Out
	ContentCategory_IAB8_10  ContentCategory = 140 // Food Allergies
	ContentCategory_IAB8_11  ContentCategory = 141 // French Cuisine
	ContentCategory_IAB8_12  ContentCategory = 142 // Health/Lowfat Cooking
	ContentCategory_IAB8_13  ContentCategory = 143 // Italian Cuisine
	ContentCategory_IAB8_14  ContentCategory = 144 // Japanese Cuisine
	ContentCategory_IAB8_15  ContentCategory = 145 // Mexican Cuisine
	ContentCategory_IAB8_16  ContentCategory = 146 // Vegan
	ContentCategory_IAB8_17  ContentCategory = 147 // Vegetarian
	ContentCategory_IAB8_18  ContentCategory = 148 // Wine
	ContentCategory_IAB9     ContentCategory = 149 // Hobbies & Interests
	ContentCategory_IAB9_1   ContentCategory = 150 // Art/Technology
	ContentCategory_IAB9_2   ContentCategory = 151 // Arts & Crafts
	ContentCategory_IAB9_3   ContentCategory = 152 // Beadwork
	ContentCategory_IAB9_4   ContentCategory = 153 // Birdwatching
	ContentCategory_IAB9_5   ContentCategory = 154 // Board Games/Puzzles
	ContentCategory_IAB9_6   ContentCategory = 155 // Candle & Soap Making
	ContentCategory_IAB9_7   ContentCategory = 156 // Card Games
	ContentCategory_IAB9_8   ContentCategory = 157 // Chess
	ContentCategory_IAB9_9   ContentCategory = 158 // Cigars
	ContentCategory_IAB9_10  ContentCategory = 159 // Collecting
	ContentCategory_IAB9_11  ContentCategory = 160 // Comic Books
	ContentCategory_IAB9_12  ContentCategory = 161 // Drawing/Sketching
	ContentCategory_IAB9_13  ContentCategory = 162 // Freelance Writing
	ContentCategory_IAB9_14  ContentCategory = 163 // Geneaology
	ContentCategory_IAB9_15  ContentCategory = 164 // Getting Published
	ContentCategory_IAB9_16  ContentCategory = 165 // Guitar
	ContentCategory_IAB9_17  ContentCategory = 166 // Home Recording
	ContentCategory_IAB9_18  ContentCategory = 167 // Investors & Patents
	ContentCategory_IAB9_19  ContentCategory = 168 // Jewelry Making
	ContentCategory_IAB9_20  ContentCategory = 169 // Magic & Illusion
	ContentCategory_IAB9_21  ContentCategory = 170 // Needlework
	ContentCategory_IAB9_22  ContentCategory = 171 // Painting
	ContentCategory_IAB9_23  ContentCategory = 172 // Photography
	ContentCategory_IAB9_24  ContentCategory = 173 // Radio
	ContentCategory_IAB9_25  ContentCategory = 174 // Roleplaying Games
	ContentCategory_IAB9_26  ContentCategory = 175 // Sci-Fi & Fantasy
	ContentCategory_IAB9_27  ContentCategory = 176 // Scrapbooking
	ContentCategory_IAB9_28  ContentCategory = 177 // Screenwriting
	ContentCategory_IAB9_29  ContentCategory = 178 // Stamps & Coins
	ContentCategory_IAB9_30  ContentCategory = 179 // Video & Computer Games
	ContentCategory_IAB9_31  ContentCategory = 180 // Woodworking
	ContentCategory_IAB10    ContentCategory = 181 // Home & Garden
	ContentCategory_IAB10_1  ContentCategory = 182 // Appliances
	ContentCategory_IAB10_2  ContentCategory = 183 // Entertaining
	ContentCategory_IAB10_3  ContentCategory = 184 // Environmental Safety
	ContentCategory_IAB10_4  ContentCategory = 185 // Gardening
	ContentCategory_IAB10_5  ContentCategory = 186 // Home Repair
	ContentCategory_IAB10_6  ContentCategory = 187 // Home Theater
	ContentCategory_IAB10_7  ContentCategory = 188 // Interior  Decorating
	ContentCategory_IAB10_8  ContentCategory = 189 // Landscaping
	ContentCategory_IAB10_9  ContentCategory = 190 // Remodeling & Construction
	ContentCategory_IAB11    ContentCategory = 191 // Law, Gov't & Politics
	ContentCategory_IAB11_1  ContentCategory = 192 // Immigration
	ContentCategory_IAB11_2  ContentCategory = 193 // Legal Issues
	ContentCategory_IAB11_3  ContentCategory = 194 // U.S. Government Resources
	ContentCategory_IAB11_4  ContentCategory = 195 // Politics
	ContentCategory_IAB11_5  ContentCategory = 196 // Commentary
	ContentCategory_IAB12    ContentCategory = 197 // News
	ContentCategory_IAB12_1  ContentCategory = 198 // International News
	ContentCategory_IAB12_2  ContentCategory = 199 // National News
	ContentCategory_IAB12_3  ContentCategory = 200 // Local News
	ContentCategory_IAB13    ContentCategory = 201 // Personal Finance
	ContentCategory_IAB13_1  ContentCategory = 202 // Beginning Investing
	ContentCategory_IAB13_2  ContentCategory = 203 // Credit/Debt & Loans
	ContentCategory_IAB13_3  ContentCategory = 204 // Financial News
	ContentCategory_IAB13_4  ContentCategory = 205 // Financial Planning
	ContentCategory_IAB13_5  ContentCategory = 206 // Hedge Fund
	ContentCategory_IAB13_6  ContentCategory = 207 // Insurance
	ContentCategory_IAB13_7  ContentCategory = 208 // Investing
	ContentCategory_IAB13_8  ContentCategory = 209 // Mutual Funds
	ContentCategory_IAB13_9  ContentCategory = 210 // Options
	ContentCategory_IAB13_10 ContentCategory = 211 // Retirement Planning
	ContentCategory_IAB13_11 ContentCategory = 212 // Stocks
	ContentCategory_IAB13_12 ContentCategory = 213 // Tax Planning
	ContentCategory_IAB14    ContentCategory = 214 // Society
	ContentCategory_IAB14_1  ContentCategory = 215 // Dating
	ContentCategory_IAB14_2  ContentCategory = 216 // Divorce Support
	ContentCategory_IAB14_3  ContentCategory = 217 // Gay Life
	ContentCategory_IAB14_4  ContentCategory = 218 // Marriage
	ContentCategory_IAB14_5  ContentCategory = 219 // Senior Living
	ContentCategory_IAB14_6  ContentCategory = 220 // Teens
	ContentCategory_IAB14_7  ContentCategory = 221 // Weddings
	ContentCategory_IAB14_8  ContentCategory = 222 // Ethnic Specific
	ContentCategory_IAB15    ContentCategory = 223 // Science
	ContentCategory_IAB15_1  ContentCategory = 224 // Astrology
	ContentCategory_IAB15_2  ContentCategory = 225 // Biology
	ContentCategory_IAB15_3  ContentCategory = 226 // Chemistry
	ContentCategory_IAB15_4  ContentCategory = 227 // Geology
	ContentCategory_IAB15_5  ContentCategory = 228 // Paranormal Phenomena
	ContentCategory_IAB15_6  ContentCategory = 229 // Physics
	ContentCategory_IAB15_7  ContentCategory = 230 // Space/Astronomy
	ContentCategory_IAB15_8  ContentCategory = 231 // Geography
	ContentCategory_IAB15_9  ContentCategory = 232 // Botany
	ContentCategory_IAB15_10 ContentCategory = 233 // Weather
	ContentCategory_IAB16    ContentCategory = 234 // Pets
	ContentCategory_IAB16_1  ContentCategory = 235 // Aquariums
	ContentCategory_IAB16_2  ContentCategory = 236 // Birds
	ContentCategory_IAB16_3  ContentCategory = 237 // Cats
	ContentCategory_IAB16_4  ContentCategory = 238 // Dogs
	ContentCategory_IAB16_5  ContentCategory = 239 // Large Animals
	ContentCategory_IAB16_6  ContentCategory = 240 // Reptiles
	ContentCategory_IAB16_7  ContentCategory = 241 // Veterinary Medicine
	ContentCategory_IAB17    ContentCategory = 242 // Sports
	ContentCategory_IAB17_1  ContentCategory = 243 // Auto Racing
	ContentCategory_IAB17_2  ContentCategory = 244 // Baseball
	ContentCategory_IAB17_3  ContentCategory = 245 // Bicycling
	ContentCategory_IAB17_4  ContentCategory = 246 // Bodybuilding
	ContentCategory_IAB17_5  ContentCategory = 247 // Boxing
	ContentCategory_IAB17_6  ContentCategory = 248 // Canoeing/Kayaking
	ContentCategory_IAB17_7  ContentCategory = 249 // Cheerleading
	ContentCategory_IAB17_8  ContentCategory = 250 // Climbing
	ContentCategory_IAB17_9  ContentCategory = 251 // Cricket
	ContentCategory_IAB17_10 ContentCategory = 252 // Figure Skating
	ContentCategory_IAB17_11 ContentCategory = 253 // Fly Fishing
	ContentCategory_IAB17_12 ContentCategory = 254 // Football
	ContentCategory_IAB17_13 ContentCategory = 255 // Freshwater Fishing
	ContentCategory_IAB17_14 ContentCategory = 256 // Game & Fish
	ContentCategory_IAB17_15 ContentCategory = 257 // Golf
	ContentCategory_IAB17_16 ContentCategory = 258 // Horse Racing
	ContentCategory_IAB17_17 ContentCategory = 259 // Horses
	ContentCategory_IAB17_18 ContentCategory = 260 // Hunting/Shooting
	ContentCategory_IAB17_19 ContentCategory = 261 // Inline  Skating
	ContentCategory_IAB17_20 ContentCategory = 262 // Martial Arts
	ContentCategory_IAB17_21 ContentCategory = 263 // Mountain Biking
	ContentCategory_IAB17_22 ContentCategory = 264 // NASCAR Racing
	ContentCategory_IAB17_23 ContentCategory = 265 // Olympics
	ContentCategory_IAB17_24 ContentCategory = 266 // Paintball
	ContentCategory_IAB17_25 ContentCategory = 267 // Power & Motorcycles
	ContentCategory_IAB17_26 ContentCategory = 268 // Pro Basketball
	ContentCategory_IAB17_27 ContentCategory = 269 // Pro Ice Hockey
	ContentCategory_IAB17_28 ContentCategory = 270 // Rodeo
	ContentCategory_IAB17_29 ContentCategory = 271 // Rugby
	ContentCategory_IAB17_30 ContentCategory = 272 // Running/Jogging
	ContentCategory_IAB17_31 ContentCategory = 273 // Sailing
	ContentCategory_IAB17_32 ContentCategory = 274 // Saltwater Fishing
	ContentCategory_IAB17_33 ContentCategory = 275 // Scuba Diving
	ContentCategory_IAB17_34 ContentCategory = 276 // Skateboarding
	ContentCategory_IAB17_35 ContentCategory = 277 // Skiing
	ContentCategory_IAB17_36 ContentCategory = 278 // Snowboarding
	ContentCategory_IAB17_37 ContentCategory = 279 // Surfing/Bodyboarding
	ContentCategory_IAB17_38 ContentCategory = 280 // Swimming
	ContentCategory_IAB17_39 ContentCategory = 281 // Table Tennis/Ping-Pong
	ContentCategory_IAB17_40 ContentCategory = 282 // Tennis
	ContentCategory_IAB17_41 ContentCategory = 283 // Volleyball
	ContentCategory_IAB17_42 ContentCategory = 284 // Walking
	ContentCategory_IAB17_43 ContentCategory = 285 // Waterski/Wakeboard
	ContentCategory_IAB17_44 ContentCategory = 286 // World Soccer
	ContentCategory_IAB18    ContentCategory = 287 // Style & Fashion
	ContentCategory_IAB18_1  ContentCategory = 288 // Beauty
	ContentCategory_IAB18_2  ContentCategory = 289 // Body Art
	ContentCategory_IAB18_3  ContentCategory = 290 // Fashion
	ContentCategory_IAB18_4  ContentCategory = 291 // Jewelry
	ContentCategory_IAB18_5  ContentCategory = 292 // Clothing
	ContentCategory_IAB18_6  ContentCategory = 293 // Accessories
	ContentCategory_IAB19    ContentCategory = 294 // Technology & Computing
	ContentCategory_IAB19_1  ContentCategory = 295 // 3-D Graphics
	ContentCategory_IAB19_2  ContentCategory = 296 // Animation
	ContentCategory_IAB19_3  ContentCategory = 297 // Antivirus Software
	ContentCategory_IAB19_4  ContentCategory = 298 // C/C++
	ContentCategory_IAB19_5  ContentCategory = 299 // Cameras & Camcorders
	ContentCategory_IAB19_6  ContentCategory = 300 // Cell  Phones
	ContentCategory_IAB19_7  ContentCategory = 301 // Computer Certification
	ContentCategory_IAB19_8  ContentCategory = 302 // Computer Networking
	ContentCategory_IAB19_9  ContentCategory = 303 // Computer Peripherals
	ContentCategory_IAB19_10 ContentCategory = 304 // Computer Reviews
	ContentCategory_IAB19_11 ContentCategory = 305 // Data Centers
	ContentCategory_IAB19_12 ContentCategory = 306 // Databases
	ContentCategory_IAB19_13 ContentCategory = 307 // Desktop Publishing
	ContentCategory_IAB19_14 ContentCategory = 308 // Desktop Video
	ContentCategory_IAB19_15 ContentCategory = 309 // Email
	ContentCategory_IAB19_16 ContentCategory = 310 // Graphics Software
	ContentCategory_IAB19_17 ContentCategory = 311 // Home Video/DVD
	ContentCategory_IAB19_18 ContentCategory = 312 // Internet Technology
	ContentCategory_IAB19_19 ContentCategory = 313 // Java
	ContentCategory_IAB19_20 ContentCategory = 314 // Javascript
	ContentCategory_IAB19_21 ContentCategory = 315 // Mac Support
	ContentCategory_IAB19_22 ContentCategory = 316 // MP3/MIDI
	ContentCategory_IAB19_23 ContentCategory = 317 // Net Conferencing
	ContentCategory_IAB19_24 ContentCategory = 318 // Net for Beginners
	ContentCategory_IAB19_25 ContentCategory = 319 // Network Security
	ContentCategory_IAB19_26 ContentCategory = 320 // Palmtops/PDAs
	ContentCategory_IAB19_27 ContentCategory = 321 // PC Support
	ContentCategory_IAB19_28 ContentCategory = 322 // Portable
	ContentCategory_IAB19_29 ContentCategory = 323 // Entertainment
	ContentCategory_IAB19_30 ContentCategory = 324 // Shareware/Freeware
	ContentCategory_IAB19_31 ContentCategory = 325 // Unix
	ContentCategory_IAB19_32 ContentCategory = 326 // Visual Basic
	ContentCategory_IAB19_33 ContentCategory = 327 // Web Clip Art
	ContentCategory_IAB19_34 ContentCategory = 328 // Web Design/HTML
	ContentCategory_IAB19_35 ContentCategory = 329 // Web Search
	ContentCategory_IAB19_36 ContentCategory = 330 // Windows
	ContentCategory_IAB20    ContentCategory = 331 // Travel
	ContentCategory_IAB20_1  ContentCategory = 332 // Adventure Travel
	ContentCategory_IAB20_2  ContentCategory = 333 // Africa
	ContentCategory_IAB20_3  ContentCategory = 334 // Air Travel
	ContentCategory_IAB20_4  ContentCategory = 335 // Australia & New Zealand
	ContentCategory_IAB20_5  ContentCategory = 336 // Bed & Breakfasts
	ContentCategory_IAB20_6  ContentCategory = 337 // Budget Travel
	ContentCategory_IAB20_7  ContentCategory = 338 // Business Travel
	ContentCategory_IAB20_8  ContentCategory = 339 // By US Locale
	ContentCategory_IAB20_9  ContentCategory = 340 // Camping
	ContentCategory_IAB20_10 ContentCategory = 341 // Canada
	ContentCategory_IAB20_11 ContentCategory = 342 // Caribbean
	ContentCategory_IAB20_12 ContentCategory = 343 // Cruises
	ContentCategory_IAB20_13 ContentCategory = 344 // Eastern  Europe
	ContentCategory_IAB20_14 ContentCategory = 345 // Europe
	ContentCategory_IAB20_15 ContentCategory = 346 // France
	ContentCategory_IAB20_16 ContentCategory = 347 // Greece
	ContentCategory_IAB20_17 ContentCategory = 348 // Honeymoons/Getaways
	ContentCategory_IAB20_18 ContentCategory = 349 // Hotels
	ContentCategory_IAB20_19 ContentCategory = 350 // Italy
	ContentCategory_IAB20_20 ContentCategory = 351 // Japan
	ContentCategory_IAB20_21 ContentCategory = 352 // Mexico & Central America
	ContentCategory_IAB20_22 ContentCategory = 353 // National Parks
	ContentCategory_IAB20_23 ContentCategory = 354 // South America
	ContentCategory_IAB20_24 ContentCategory = 355 // Spas
	ContentCategory_IAB20_25 ContentCategory = 356 // Theme Parks
	ContentCategory_IAB20_26 ContentCategory = 357 // Traveling with Kids
	ContentCategory_IAB20_27 ContentCategory = 358 // United Kingdom
	ContentCategory_IAB21    ContentCategory = 359 // Real Estate
	ContentCategory_IAB21_1  ContentCategory = 360 // Apartments
	ContentCategory_IAB21_2  ContentCategory = 361 // Architects
	ContentCategory_IAB21_3  ContentCategory = 362 // Buying/Selling Homes
	ContentCategory_IAB22    ContentCategory = 363 // Shopping
	ContentCategory_IAB22_1  ContentCategory = 364 // Contests & Freebies
	ContentCategory_IAB22_2  ContentCategory = 365 // Couponing
	ContentCategory_IAB22_3  ContentCategory = 366 // Comparison
	ContentCategory_IAB22_4  ContentCategory = 367 // Engines
	ContentCategory_IAB23    ContentCategory = 368 // Religion & Spirituality
	ContentCategory_IAB23_1  ContentCategory = 369 // Alternative Religions
	ContentCategory_IAB23_2  ContentCategory = 370 // Atheism/Agnosticism
	ContentCategory_IAB23_3  ContentCategory = 371 // Buddhism
	ContentCategory_IAB23_4  ContentCategory = 372 // Catholicism
	ContentCategory_IAB23_5  ContentCategory = 373 // Christianity
	ContentCategory_IAB23_6  ContentCategory = 374 // Hinduism
	ContentCategory_IAB23_7  ContentCategory = 375 // Islam
	ContentCategory_IAB23_8  ContentCategory = 376 // Judaism
	ContentCategory_IAB23_9  ContentCategory = 377 // Latter-Day Saints
	ContentCategory_IAB23_10 ContentCategory = 378 // Paga/Wiccan
	ContentCategory_IAB24    ContentCategory = 379 // Uncategorized
	ContentCategory_IAB25    ContentCategory = 380 // Non-Standard Content
	ContentCategory_IAB25_1  ContentCategory = 381 // Unmoderated UGC
	ContentCategory_IAB25_2  ContentCategory = 382 // Extreme Graphic/Explicit Violence
	ContentCategory_IAB25_3  ContentCategory = 383 // Pornography
	ContentCategory_IAB25_4  ContentCategory = 384 // Profane Content
	ContentCategory_IAB25_5  ContentCategory = 385 // Hate Content
	ContentCategory_IAB25_6  ContentCategory = 386 // Under Construction
	ContentCategory_IAB25_7  ContentCategory = 387 // Incentivized
	ContentCategory_IAB26    ContentCategory = 388 // Illegal Content
	ContentCategory_IAB26_1  ContentCategory = 389 // Illegal Content
	ContentCategory_IAB26_2  ContentCategory = 390 // Warez
	ContentCategory_IAB26_3  ContentCategory = 391 // Spyware/Malware
	ContentCategory_IAB26_4  ContentCategory = 392 // Copyright Infringement
)

// Enum value maps for ContentCategory.
var (
	ContentCategory_name = map[int32]string{
		1:   "IAB1",
		2:   "IAB1_1",
		3:   "IAB1_2",
		4:   "IAB1_3",
		5:   "IAB1_4",
		6:   "IAB1_5",
		7:   "IAB1_6",
		8:   "IAB1_7",
		9:   "IAB2",
		10:  "IAB2_1",
		11:  "IAB2_2",
		12:  "IAB2_3",
		13:  "IAB2_4",
		14:  "IAB2_5",
		15:  "IAB2_6",
		16:  "IAB2_7",
		17:  "IAB2_8",
		18:  "IAB2_9",
		19:  "IAB2_10",
		20:  "IAB2_11",
		21:  "IAB2_12",
		22:  "IAB2_13",
		23:  "IAB2_14",
		24:  "IAB2_15",
		25:  "IAB2_16",
		26:  "IAB2_17",
		27:  "IAB2_18",
		28:  "IAB2_19",
		29:  "IAB2_20",
		30:  "IAB2_21",
		31:  "IAB2_22",
		32:  "IAB2_23",
		33:  "IAB3",
		34:  "IAB3_1",
		35:  "IAB3_2",
		36:  "IAB3_3",
		37:  "IAB3_4",
		38:  "IAB3_5",
		39:  "IAB3_6",
		40:  "IAB3_7",
		41:  "IAB3_8",
		42:  "IAB3_9",
		43:  "IAB3_10",
		44:  "IAB3_11",
		45:  "IAB3_12",
		46:  "IAB4",
		47:  "IAB4_1",
		48:  "IAB4_2",
		49:  "IAB4_3",
		50:  "IAB4_4",
		51:  "IAB4_5",
		52:  "IAB4_6",
		53:  "IAB4_7",
		54:  "IAB4_8",
		55:  "IAB4_9",
		56:  "IAB4_10",
		57:  "IAB4_11",
		58:  "IAB5",
		59:  "IAB5_1",
		60:  "IAB5_2",
		61:  "IAB5_3",
		62:  "IAB5_4",
		63:  "IAB5_5",
		64:  "IAB5_6",
		65:  "IAB5_7",
		66:  "IAB5_8",
		67:  "IAB5_9",
		68:  "IAB5_10",
		69:  "IAB5_11",
		70:  "IAB5_12",
		71:  "IAB5_13",
		72:  "IAB5_14",
		73:  "IAB5_15",
		74:  "IAB6",
		75:  "IAB6_1",
		76:  "IAB6_2",
		77:  "IAB6_3",
		78:  "IAB6_4",
		79:  "IAB6_5",
		80:  "IAB6_6",
		81:  "IAB6_7",
		82:  "IAB6_8",
		83:  "IAB6_9",
		84:  "IAB7",
		85:  "IAB7_1",
		86:  "IAB7_2",
		87:  "IAB7_3",
		88:  "IAB7_4",
		89:  "IAB7_5",
		90:  "IAB7_6",
		91:  "IAB7_7",
		92:  "IAB7_8",
		93:  "IAB7_9",
		94:  "IAB7_10",
		95:  "IAB7_11",
		96:  "IAB7_12",
		97:  "IAB7_13",
		98:  "IAB7_14",
		99:  "IAB7_15",
		100: "IAB7_16",
		101: "IAB7_17",
		102: "IAB7_18",
		103: "IAB7_19",
		104: "IAB7_20",
		105: "IAB7_21",
		106: "IAB7_22",
		107: "IAB7_23",
		108: "IAB7_24",
		109: "IAB7_25",
		110: "IAB7_26",
		111: "IAB7_27",
		112: "IAB7_28",
		113: "IAB7_29",
		114: "IAB7_30",
		115: "IAB7_31",
		116: "IAB7_32",
		117: "IAB7_33",
		118: "IAB7_34",
		119: "IAB7_35",
		120: "IAB7_36",
		121: "IAB7_37",
		122: "IAB7_38",
		123: "IAB7_39",
		124: "IAB7_40",
		125: "IAB7_41",
		126: "IAB7_42",
		127: "IAB7_43",
		128: "IAB7_44",
		129: "IAB7_45",
		130: "IAB8",
		131: "IAB8_1",
		132: "IAB8_2",
		133: "IAB8_3",
		134: "IAB8_4",
		135: "IAB8_5",
		136: "IAB8_6",
		137: "IAB8_7",
		138: "IAB8_8",
		139: "IAB8_9",
		140: "IAB8_10",
		141: "IAB8_11",
		142: "IAB8_12",
		143: "IAB8_13",
		144: "IAB8_14",
		145: "IAB8_15",
		146: "IAB8_16",
		147: "IAB8_17",
		148: "IAB8_18",
		149: "IAB9",
		150: "IAB9_1",
		151: "IAB9_2",
		152: "IAB9_3",
		153: "IAB9_4",
		154: "IAB9_5",
		155: "IAB9_6",
		156: "IAB9_7",
		157: "IAB9_8",
		158: "IAB9_9",
		159: "IAB9_10",
		160: "IAB9_11",
		161: "IAB9_12",
		162: "IAB9_13",
		163: "IAB9_14",
		164: "IAB9_15",
		165: "IAB9_16",
		166: "IAB9_17",
		167: "IAB9_18",
		168: "IAB9_19",
		169: "IAB9_20",
		170: "IAB9_21",
		171: "IAB9_22",
		172: "IAB9_23",
		173: "IAB9_24",
		174: "IAB9_25",
		175: "IAB9_26",
		176: "IAB9_27",
		177: "IAB9_28",
		178: "IAB9_29",
		179: "IAB9_30",
		180: "IAB9_31",
		181: "IAB10",
		182: "IAB10_1",
		183: "IAB10_2",
		184: "IAB10_3",
		185: "IAB10_4",
		186: "IAB10_5",
		187: "IAB10_6",
		188: "IAB10_7",
		189: "IAB10_8",
		190: "IAB10_9",
		191: "IAB11",
		192: "IAB11_1",
		193: "IAB11_2",
		194: "IAB11_3",
		195: "IAB11_4",
		196: "IAB11_5",
		197: "IAB12",
		198: "IAB12_1",
		199: "IAB12_2",
		200: "IAB12_3",
		201: "IAB13",
		202: "IAB13_1",
		203: "IAB13_2",
		204: "IAB13_3",
		205: "IAB13_4",
		206: "IAB13_5",
		207: "IAB13_6",
		208: "IAB13_7",
		209: "IAB13_8",
		210: "IAB13_9",
		211: "IAB13_10",
		212: "IAB13_11",
		213: "IAB13_12",
		214: "IAB14",
		215: "IAB14_1",
		216: "IAB14_2",
		217: "IAB14_3",
		218: "IAB14_4",
		219: "IAB14_5",
		220: "IAB14_6",
		221: "IAB14_7",
		222: "IAB14_8",
		223: "IAB15",
		224: "IAB15_1",
		225: "IAB15_2",
		226: "IAB15_3",
		227: "IAB15_4",
		228: "IAB15_5",
		229: "IAB15_6",
		230: "IAB15_7",
		231: "IAB15_8",
		232: "IAB15_9",
		233: "IAB15_10",
		234: "IAB16",
		235: "IAB16_1",
		236: "IAB16_2",
		237: "IAB16_3",
		238: "IAB16_4",
		239: "IAB16_5",
		240: "IAB16_6",
		241: "IAB16_7",
		242: "IAB17",
		243: "IAB17_1",
		244: "IAB17_2",
		245: "IAB17_3",
		246: "IAB17_4",
		247: "IAB17_5",
		248: "IAB17_6",
		249: "IAB17_7",
		250: "IAB17_8",
		251: "IAB17_9",
		252: "IAB17_10",
		253: "IAB17_11",
		254: "IAB17_12",
		255: "IAB17_13",
		256: "IAB17_14",
		257: "IAB17_15",
		258: "IAB17_16",
		259: "IAB17_17",
		260: "IAB17_18",
		261: "IAB17_19",
		262: "IAB17_20",
		263: "IAB17_21",
		264: "IAB17_22",
		265: "IAB17_23",
		266: "IAB17_24",
		267: "IAB17_25",
		268: "IAB17_26",
		269: "IAB17_27",
		270: "IAB17_28",
		271: "IAB17_29",
		272: "IAB17_30",
		273: "IAB17_31",
		274: "IAB17_32",
		275: "IAB17_33",
		276: "IAB17_34",
		277: "IAB17_35",
		278: "IAB17_36",
		279: "IAB17_37",
		280: "IAB17_38",
		281: "IAB17_39",
		282: "IAB17_40",
		283: "IAB17_41",
		284: "IAB17_42",
		285: "IAB17_43",
		286: "IAB17_44",
		287: "IAB18",
		288: "IAB18_1",
		289: "IAB18_2",
		290: "IAB18_3",
		291: "IAB18_4",
		292: "IAB18_5",
		293: "IAB18_6",
		294: "IAB19",
		295: "IAB19_1",
		296: "IAB19_2",
		297: "IAB19_3",
		298: "IAB19_4",
		299: "IAB19_5",
		300: "IAB19_6",
		301: "IAB19_7",
		302: "IAB19_8",
		303: "IAB19_9",
		304: "IAB19_10",
		305: "IAB19_11",
		306: "IAB19_12",
		307: "IAB19_13",
		308: "IAB19_14",
		309: "IAB19_15",
		310: "IAB19_16",
		311: "IAB19_17",
		312: "IAB19_18",
		313: "IAB19_19",
		314: "IAB19_20",
		315: "IAB19_21",
		316: "IAB19_22",
		317: "IAB19_23",
		318: "IAB19_24",
		319: "IAB19_25",
		320: "IAB19_26",
		321: "IAB19_27",
		322: "IAB19_28",
		323: "IAB19_29",
		324: "IAB19_30",
		325: "IAB19_31",
		326: "IAB19_32",
		327: "IAB19_33",
		328: "IAB19_34",
		329: "IAB19_35",
		330: "IAB19_36",
		331: "IAB20",
		332: "IAB20_1",
		333: "IAB20_2",
		334: "IAB20_3",
		335: "IAB20_4",
		336: "IAB20_5",
		337: "IAB20_6",
		338: "IAB20_7",
		339: "IAB20_8",
		340: "IAB20_9",
		341: "IAB20_10",
		342: "IAB20_11",
		343: "IAB20_12",
		344: "IAB20_13",
		345: "IAB20_14",
		346: "IAB20_15",
		347: "IAB20_16",
		348: "IAB20_17",
		349: "IAB20_18",
		350: "IAB20_19",
		351: "IAB20_20",
		352: "IAB20_21",
		353: "IAB20_22",
		354: "IAB20_23",
		355: "IAB20_24",
		356: "IAB20_25",
		357: "IAB20_26",
		358: "IAB20_27",
		359: "IAB21",
		360: "IAB21_1",
		361: "IAB21_2",
		362: "IAB21_3",
		363: "IAB22",
		364: "IAB22_1",
		365: "IAB22_2",
		366: "IAB22_3",
		367: "IAB22_4",
		368: "IAB23",
		369: "IAB23_1",
		370: "IAB23_2",
		371: "IAB23_3",
		372: "IAB23_4",
		373: "IAB23_5",
		374: "IAB23_6",
		375: "IAB23_7",
		376: "IAB23_8",
		377: "IAB23_9",
		378: "IAB23_10",
		379: "IAB24",
		380: "IAB25",
		381: "IAB25_1",
		382: "IAB25_2",
		383: "IAB25_3",
		384: "IAB25_4",
		385: "IAB25_5",
		386: "IAB25_6",
		387: "IAB25_7",
		388: "IAB26",
		389: "IAB26_1",
		390: "IAB26_2",
		391: "IAB26_3",
		392: "IAB26_4",
	}
	ContentCategory_value = map[string]int32{
		"IAB1":     1,
		"IAB1_1":   2,
		"IAB1_2":   3,
		"IAB1_3":   4,
		"IAB1_4":   5,
		"IAB1_5":   6,
		"IAB1_6":   7,
		"IAB1_7":   8,
		"IAB2":     9,
		"IAB2_1":   10,
		"IAB2_2":   11,
		"IAB2_3":   12,
		"IAB2_4":   13,
		"IAB2_5":   14,
		"IAB2_6":   15,
		"IAB2_7":   16,
		"IAB2_8":   17,
		"IAB2_9":   18,
		"IAB2_10":  19,
		"IAB2_11":  20,
		"IAB2_12":  21,
		"IAB2_13":  22,
		"IAB2_14":  23,
		"IAB2_15":  24,
		"IAB2_16":  25,
		"IAB2_17":  26,
		"IAB2_18":  27,
		"IAB2_19":  28,
		"IAB2_20":  29,
		"IAB2_21":  30,
		"IAB2_22":  31,
		"IAB2_23":  32,
		"IAB3":     33,
		"IAB3_1":   34,
		"IAB3_2":   35,
		"IAB3_3":   36,
		"IAB3_4":   37,
		"IAB3_5":   38,
		"IAB3_6":   39,
		"IAB3_7":   40,
		"IAB3_8":   41,
		"IAB3_9":   42,
		"IAB3_10":  43,
		"IAB3_11":  44,
		"IAB3_12":  45,
		"IAB4":     46,
		"IAB4_1":   47,
		"IAB4_2":   48,
		"IAB4_3":   49,
		"IAB4_4":   50,
		"IAB4_5":   51,
		"IAB4_6":   52,
		"IAB4_7":   53,
		"IAB4_8":   54,
		"IAB4_9":   55,
		"IAB4_10":  56,
		"IAB4_11":  57,
		"IAB5":     58,
		"IAB5_1":   59,
		"IAB5_2":   60,
		"IAB5_3":   61,
		"IAB5_4":   62,
		"IAB5_5":   63,
		"IAB5_6":   64,
		"IAB5_7":   65,
		"IAB5_8":   66,
		"IAB5_9":   67,
		"IAB5_10":  68,
		"IAB5_11":  69,
		"IAB5_12":  70,
		"IAB5_13":  71,
		"IAB5_14":  72,
		"IAB5_15":  73,
		"IAB6":     74,
		"IAB6_1":   75,
		"IAB6_2":   76,
		"IAB6_3":   77,
		"IAB6_4":   78,
		"IAB6_5":   79,
		"IAB6_6":   80,
		"IAB6_7":   81,
		"IAB6_8":   82,
		"IAB6_9":   83,
		"IAB7":     84,
		"IAB7_1":   85,
		"IAB7_2":   86,
		"IAB7_3":   87,
		"IAB7_4":   88,
		"IAB7_5":   89,
		"IAB7_6":   90,
		"IAB7_7":   91,
		"IAB7_8":   92,
		"IAB7_9":   93,
		"IAB7_10":  94,
		"IAB7_11":  95,
		"IAB7_12":  96,
		"IAB7_13":  97,
		"IAB7_14":  98,
		"IAB7_15":  99,
		"IAB7_16":  100,
		"IAB7_17":  101,
		"IAB7_18":  102,
		"IAB7_19":  103,
		"IAB7_20":  104,
		"IAB7_21":  105,
		"IAB7_22":  106,
		"IAB7_23":  107,
		"IAB7_24":  108,
		"IAB7_25":  109,
		"IAB7_26":  110,
		"IAB7_27":  111,
		"IAB7_28":  112,
		"IAB7_29":  113,
		"IAB7_30":  114,
		"IAB7_31":  115,
		"IAB7_32":  116,
		"IAB7_33":  117,
		"IAB7_34":  118,
		"IAB7_35":  119,
		"IAB7_36":  120,
		"IAB7_37":  121,
		"IAB7_38":  122,
		"IAB7_39":  123,
		"IAB7_40":  124,
		"IAB7_41":  125,
		"IAB7_42":  126,
		"IAB7_43":  127,
		"IAB7_44":  128,
		"IAB7_45":  129,
		"IAB8":     130,
		"IAB8_1":   131,
		"IAB8_2":   132,
		"IAB8_3":   133,
		"IAB8_4":   134,
		"IAB8_5":   135,
		"IAB8_6":   136,
		"IAB8_7":   137,
		"IAB8_8":   138,
		"IAB8_9":   139,
		"IAB8_10":  140,
		"IAB8_11":  141,
		"IAB8_12":  142,
		"IAB8_13":  143,
		"IAB8_14":  144,
		"IAB8_15":  145,
		"IAB8_16":  146,
		"IAB8_17":  147,
		"IAB8_18":  148,
		"IAB9":     149,
		"IAB9_1":   150,
		"IAB9_2":   151,
		"IAB9_3":   152,
		"IAB9_4":   153,
		"IAB9_5":   154,
		"IAB9_6":   155,
		"IAB9_7":   156,
		"IAB9_8":   157,
		"IAB9_9":   158,
		"IAB9_10":  159,
		"IAB9_11":  160,
		"IAB9_12":  161,
		"IAB9_13":  162,
		"IAB9_14":  163,
		"IAB9_15":  164,
		"IAB9_16":  165,
		"IAB9_17":  166,
		"IAB9_18":  167,
		"IAB9_19":  168,
		"IAB9_20":  169,
		"IAB9_21":  170,
		"IAB9_22":  171,
		"IAB9_23":  172,
		"IAB9_24":  173,
		"IAB9_25":  174,
		"IAB9_26":  175,
		"IAB9_27":  176,
		"IAB9_28":  177,
		"IAB9_29":  178,
		"IAB9_30":  179,
		"IAB9_31":  180,
		"IAB10":    181,
		"IAB10_1":  182,
		"IAB10_2":  183,
		"IAB10_3":  184,
		"IAB10_4":  185,
		"IAB10_5":  186,
		"IAB10_6":  187,
		"IAB10_7":  188,
		"IAB10_8":  189,
		"IAB10_9":  190,
		"IAB11":    191,
		"IAB11_1":  192,
		"IAB11_2":  193,
		"IAB11_3":  194,
		"IAB11_4":  195,
		"IAB11_5":  196,
		"IAB12":    197,
		"IAB12_1":  198,
		"IAB12_2":  199,
		"IAB12_3":  200,
		"IAB13":    201,
		"IAB13_1":  202,
		"IAB13_2":  203,
		"IAB13_3":  204,
		"IAB13_4":  205,
		"IAB13_5":  206,
		"IAB13_6":  207,
		"IAB13_7":  208,
		"IAB13_8":  209,
		"IAB13_9":  210,
		"IAB13_10": 211,
		"IAB13_11": 212,
		"IAB13_12": 213,
		"IAB14":    214,
		"IAB14_1":  215,
		"IAB14_2":  216,
		"IAB14_3":  217,
		"IAB14_4":  218,
		"IAB14_5":  219,
		"IAB14_6":  220,
		"IAB14_7":  221,
		"IAB14_8":  222,
		"IAB15":    223,
		"IAB15_1":  224,
		"IAB15_2":  225,
		"IAB15_3":  226,
		"IAB15_4":  227,
		"IAB15_5":  228,
		"IAB15_6":  229,
		"IAB15_7":  230,
		"IAB15_8":  231,
		"IAB15_9":  232,
		"IAB15_10": 233,
		"IAB16":    234,
		"IAB16_1":  235,
		"IAB16_2":  236,
		"IAB16_3":  237,
		"IAB16_4":  238,
		"IAB16_5":  239,
		"IAB16_6":  240,
		"IAB16_7":  241,
		"IAB17":    242,
		"IAB17_1":  243,
		"IAB17_2":  244,
		"IAB17_3":  245,
		"IAB17_4":  246,
		"IAB17_5":  247,
		"IAB17_6":  248,
		"IAB17_7":  249,
		"IAB17_8":  250,
		"IAB17_9":  251,
		"IAB17_10": 252,
		"IAB17_11": 253,
		"IAB17_12": 254,
		"IAB17_13": 255,
		"IAB17_14": 256,
		"IAB17_15": 257,
		"IAB17_16": 258,
		"IAB17_17": 259,
		"IAB17_18": 260,
		"IAB17_19": 261,
		"IAB17_20": 262,
		"IAB17_21": 263,
		"IAB17_22": 264,
		"IAB17_23": 265,
		"IAB17_24": 266,
		"IAB17_25": 267,
		"IAB17_26": 268,
		"IAB17_27": 269,
		"IAB17_28": 270,
		"IAB17_29": 271,
		"IAB17_30": 272,
		"IAB17_31": 273,
		"IAB17_32": 274,
		"IAB17_33": 275,
		"IAB17_34": 276,
		"IAB17_35": 277,
		"IAB17_36": 278,
		"IAB17_37": 279,
		"IAB17_38": 280,
		"IAB17_39": 281,
		"IAB17_40": 282,
		"IAB17_41": 283,
		"IAB17_42": 284,
		"IAB17_43": 285,
		"IAB17_44": 286,
		"IAB18":    287,
		"IAB18_1":  288,
		"IAB18_2":  289,
		"IAB18_3":  290,
		"IAB18_4":  291,
		"IAB18_5":  292,
		"IAB18_6":  293,
		"IAB19":    294,
		"IAB19_1":  295,
		"IAB19_2":  296,
		"IAB19_3":  297,
		"IAB19_4":  298,
		"IAB19_5":  299,
		"IAB19_6":  300,
		"IAB19_7":  301,
		"IAB19_8":  302,
		"IAB19_9":  303,
		"IAB19_10": 304,
		"IAB19_11": 305,
		"IAB19_12": 306,
		"IAB19_13": 307,
		"IAB19_14": 308,
		"IAB19_15": 309,
		"IAB19_16": 310,
		"IAB19_17": 311,
		"IAB19_18": 312,
		"IAB19_19": 313,
		"IAB19_20": 314,
		"IAB19_21": 315,
		"IAB19_22": 316,
		"IAB19_23": 317,
		"IAB19_24": 318,
		"IAB19_25": 319,
		"IAB19_26": 320,
		"IAB19_27": 321,
		"IAB19_28": 322,
		"IAB19_29": 323,
		"IAB19_30": 324,
		"IAB19_31": 325,
		"IAB19_32": 326,
		"IAB19_33": 327,
		"IAB19_34": 328,
		"IAB19_35": 329,
		"IAB19_36": 330,
		"IAB20":    331,
		"IAB20_1":  332,
		"IAB20_2":  333,
		"IAB20_3":  334,
		"IAB20_4":  335,
		"IAB20_5":  336,
		"IAB20_6":  337,
		"IAB20_7":  338,
		"IAB20_8":  339,
		"IAB20_9":  340,
		"IAB20_10": 341,
		"IAB20_11": 342,
		"IAB20_12": 343,
		"IAB20_13": 344,
		"IAB20_14": 345,
		"IAB20_15": 346,
		"IAB20_16": 347,
		"IAB20_17": 348,
		"IAB20_18": 349,
		"IAB20_19": 350,
		"IAB20_20": 351,
		"IAB20_21": 352,
		"IAB20_22": 353,
		"IAB20_23": 354,
		"IAB20_24": 355,
		"IAB20_25": 356,
		"IAB20_26": 357,
		"IAB20_27": 358,
		"IAB21":    359,
		"IAB21_1":  360,
		"IAB21_2":  361,
		"IAB21_3":  362,
		"IAB22":    363,
		"IAB22_1":  364,
		"IAB22_2":  365,
		"IAB22_3":  366,
		"IAB22_4":  367,
		"IAB23":    368,
		"IAB23_1":  369,
		"IAB23_2":  370,
		"IAB23_3":  371,
		"IAB23_4":  372,
		"IAB23_5":  373,
		"IAB23_6":  374,
		"IAB23_7":  375,
		"IAB23_8":  376,
		"IAB23_9":  377,
		"IAB23_10": 378,
		"IAB24":    379,
		"IAB25":    380,
		"IAB25_1":  381,
		"IAB25_2":  382,
		"IAB25_3":  383,
		"IAB25_4":  384,
		"IAB25_5":  385,
		"IAB25_6":  386,
		"IAB25_7":  387,
		"IAB26":    388,
		"IAB26_1":  389,
		"IAB26_2":  390,
		"IAB26_3":  391,
		"IAB26_4":  392,
	}
)

func (x ContentCategory) Enum() *ContentCategory {
	p := new(ContentCategory)
	*p = x
	return p
}

func (x ContentCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[0].Descriptor()
}

func (ContentCategory) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[0]
}

func (x ContentCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ContentCategory) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ContentCategory(num)
	return nil
}

// Deprecated: Use ContentCategory.Descriptor instead.
func (ContentCategory) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0}
}

// OpenRTB 5.3: The following table specifies a standard list of creative
// attributes that can describe an ad being served or serve as restrictions
// of thereof.
type CreativeAttribute int32

const (
	CreativeAttribute_AUDIO_AUTO_PLAY                CreativeAttribute = 1
	CreativeAttribute_AUDIO_USER_INITIATED           CreativeAttribute = 2
	CreativeAttribute_EXPANDABLE_AUTOMATIC           CreativeAttribute = 3
	CreativeAttribute_EXPANDABLE_CLICK_INITIATED     CreativeAttribute = 4
	CreativeAttribute_EXPANDABLE_ROLLOVER_INITIATED  CreativeAttribute = 5
	CreativeAttribute_VIDEO_IN_BANNER_AUTO_PLAY      CreativeAttribute = 6
	CreativeAttribute_VIDEO_IN_BANNER_USER_INITIATED CreativeAttribute = 7
	CreativeAttribute_POP                            CreativeAttribute = 8 // Pop (e.g., Over, Under, or upon Exit).
	CreativeAttribute_PROVOCATIVE_OR_SUGGESTIVE      CreativeAttribute = 9
	// Defined as "Shaky, Flashing, Flickering, Extreme Animation, Smileys".
	CreativeAttribute_ANNOYING                      CreativeAttribute = 10
	CreativeAttribute_SURVEYS                       CreativeAttribute = 11
	CreativeAttribute_TEXT_ONLY                     CreativeAttribute = 12
	CreativeAttribute_USER_INTERACTIVE              CreativeAttribute = 13
	CreativeAttribute_WINDOWS_DIALOG_OR_ALERT_STYLE CreativeAttribute = 14
	CreativeAttribute_HAS_AUDIO_ON_OFF_BUTTON       CreativeAttribute = 15
	CreativeAttribute_AD_CAN_BE_SKIPPED             CreativeAttribute = 16
)

// Enum value maps for CreativeAttribute.
var (
	CreativeAttribute_name = map[int32]string{
		1:  "AUDIO_AUTO_PLAY",
		2:  "AUDIO_USER_INITIATED",
		3:  "EXPANDABLE_AUTOMATIC",
		4:  "EXPANDABLE_CLICK_INITIATED",
		5:  "EXPANDABLE_ROLLOVER_INITIATED",
		6:  "VIDEO_IN_BANNER_AUTO_PLAY",
		7:  "VIDEO_IN_BANNER_USER_INITIATED",
		8:  "POP",
		9:  "PROVOCATIVE_OR_SUGGESTIVE",
		10: "ANNOYING",
		11: "SURVEYS",
		12: "TEXT_ONLY",
		13: "USER_INTERACTIVE",
		14: "WINDOWS_DIALOG_OR_ALERT_STYLE",
		15: "HAS_AUDIO_ON_OFF_BUTTON",
		16: "AD_CAN_BE_SKIPPED",
	}
	CreativeAttribute_value = map[string]int32{
		"AUDIO_AUTO_PLAY":                1,
		"AUDIO_USER_INITIATED":           2,
		"EXPANDABLE_AUTOMATIC":           3,
		"EXPANDABLE_CLICK_INITIATED":     4,
		"EXPANDABLE_ROLLOVER_INITIATED":  5,
		"VIDEO_IN_BANNER_AUTO_PLAY":      6,
		"VIDEO_IN_BANNER_USER_INITIATED": 7,
		"POP":                            8,
		"PROVOCATIVE_OR_SUGGESTIVE":      9,
		"ANNOYING":                       10,
		"SURVEYS":                        11,
		"TEXT_ONLY":                      12,
		"USER_INTERACTIVE":               13,
		"WINDOWS_DIALOG_OR_ALERT_STYLE":  14,
		"HAS_AUDIO_ON_OFF_BUTTON":        15,
		"AD_CAN_BE_SKIPPED":              16,
	}
)

func (x CreativeAttribute) Enum() *CreativeAttribute {
	p := new(CreativeAttribute)
	*p = x
	return p
}

func (x CreativeAttribute) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreativeAttribute) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[1].Descriptor()
}

func (CreativeAttribute) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[1]
}

func (x CreativeAttribute) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CreativeAttribute) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CreativeAttribute(num)
	return nil
}

// Deprecated: Use CreativeAttribute.Descriptor instead.
func (CreativeAttribute) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{1}
}

type BidRequest_AuctionType int32

const (
	BidRequest_FIRST_PRICE  BidRequest_AuctionType = 1
	BidRequest_SECOND_PRICE BidRequest_AuctionType = 2
	BidRequest_FIXED_PRICE  BidRequest_AuctionType = 3
)

// Enum value maps for BidRequest_AuctionType.
var (
	BidRequest_AuctionType_name = map[int32]string{
		1: "FIRST_PRICE",
		2: "SECOND_PRICE",
		3: "FIXED_PRICE",
	}
	BidRequest_AuctionType_value = map[string]int32{
		"FIRST_PRICE":  1,
		"SECOND_PRICE": 2,
		"FIXED_PRICE":  3,
	}
)

func (x BidRequest_AuctionType) Enum() *BidRequest_AuctionType {
	p := new(BidRequest_AuctionType)
	*p = x
	return p
}

func (x BidRequest_AuctionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AuctionType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[2].Descriptor()
}

func (BidRequest_AuctionType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[2]
}

func (x BidRequest_AuctionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AuctionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AuctionType(num)
	return nil
}

// Deprecated: Use BidRequest_AuctionType.Descriptor instead.
func (BidRequest_AuctionType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0}
}

// OpenRTB 5.4: The following table specifies the position of the ad as a
// relative measure of visibility or prominence.
//
// This OpenRTB table has values derived from the IAB Quality Assurance
// Guidelines (QAG). Practitioners should keep in sync with updates to the
// QAG values as published on IAB.net. Values "3" - "6" apply to apps
// per the mobile addendum to QAG version 1.5.
type BidRequest_Imp_AdPosition int32

const (
	BidRequest_Imp_UNKNOWN        BidRequest_Imp_AdPosition = 0
	BidRequest_Imp_ABOVE_THE_FOLD BidRequest_Imp_AdPosition = 1
	// May or may not be immediately visible depending on screen size and
	// resolution.
	// @deprecated
	BidRequest_Imp_DEPRECATED_LIKELY_BELOW_THE_FOLD BidRequest_Imp_AdPosition = 2
	BidRequest_Imp_BELOW_THE_FOLD                   BidRequest_Imp_AdPosition = 3
	BidRequest_Imp_HEADER                           BidRequest_Imp_AdPosition = 4
	BidRequest_Imp_FOOTER                           BidRequest_Imp_AdPosition = 5
	BidRequest_Imp_SIDEBAR                          BidRequest_Imp_AdPosition = 6
	BidRequest_Imp_AD_POSITION_FULLSCREEN           BidRequest_Imp_AdPosition = 7
)

// Enum value maps for BidRequest_Imp_AdPosition.
var (
	BidRequest_Imp_AdPosition_name = map[int32]string{
		0: "UNKNOWN",
		1: "ABOVE_THE_FOLD",
		2: "DEPRECATED_LIKELY_BELOW_THE_FOLD",
		3: "BELOW_THE_FOLD",
		4: "HEADER",
		5: "FOOTER",
		6: "SIDEBAR",
		7: "AD_POSITION_FULLSCREEN",
	}
	BidRequest_Imp_AdPosition_value = map[string]int32{
		"UNKNOWN":                          0,
		"ABOVE_THE_FOLD":                   1,
		"DEPRECATED_LIKELY_BELOW_THE_FOLD": 2,
		"BELOW_THE_FOLD":                   3,
		"HEADER":                           4,
		"FOOTER":                           5,
		"SIDEBAR":                          6,
		"AD_POSITION_FULLSCREEN":           7,
	}
)

func (x BidRequest_Imp_AdPosition) Enum() *BidRequest_Imp_AdPosition {
	p := new(BidRequest_Imp_AdPosition)
	*p = x
	return p
}

func (x BidRequest_Imp_AdPosition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_AdPosition) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Imp_AdPosition) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[3]
}

func (x BidRequest_Imp_AdPosition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_AdPosition) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_AdPosition(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_AdPosition.Descriptor instead.
func (BidRequest_Imp_AdPosition) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 0}
}

// OpenRTB 5.6: The following table is a list of API frameworks supported
// by the publisher.  Note that MRAID-1 is a subset of MRAID-2.
// In OpenRTB 2.1 and prior, value "3" was "MRAID".  However, not all
// MRAID capable APIs understand MRAID-2 features and as such the only
// safe interpretation of value "3" is MRAID-1. In OpenRTB 2.2, this was
// made explicit and MRAID-2 has been added as value "5".
type BidRequest_Imp_APIFramework int32

const (
	BidRequest_Imp_VPAID_1 BidRequest_Imp_APIFramework = 1
	BidRequest_Imp_VPAID_2 BidRequest_Imp_APIFramework = 2
	BidRequest_Imp_MRAID_1 BidRequest_Imp_APIFramework = 3
	BidRequest_Imp_ORMMA   BidRequest_Imp_APIFramework = 4
	BidRequest_Imp_MRAID_2 BidRequest_Imp_APIFramework = 5
)

// Enum value maps for BidRequest_Imp_APIFramework.
var (
	BidRequest_Imp_APIFramework_name = map[int32]string{
		1: "VPAID_1",
		2: "VPAID_2",
		3: "MRAID_1",
		4: "ORMMA",
		5: "MRAID_2",
	}
	BidRequest_Imp_APIFramework_value = map[string]int32{
		"VPAID_1": 1,
		"VPAID_2": 2,
		"MRAID_1": 3,
		"ORMMA":   4,
		"MRAID_2": 5,
	}
)

func (x BidRequest_Imp_APIFramework) Enum() *BidRequest_Imp_APIFramework {
	p := new(BidRequest_Imp_APIFramework)
	*p = x
	return p
}

func (x BidRequest_Imp_APIFramework) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_APIFramework) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[4].Descriptor()
}

func (BidRequest_Imp_APIFramework) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[4]
}

func (x BidRequest_Imp_APIFramework) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_APIFramework) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_APIFramework(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_APIFramework.Descriptor instead.
func (BidRequest_Imp_APIFramework) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1}
}

// OpenRTB 5.2: types of ads that can be accepted by the exchange unless
// restricted by publisher site settings.
type BidRequest_Imp_Banner_BannerAdType int32

const (
	// "Usually mobile".
	BidRequest_Imp_Banner_XHTML_TEXT_AD BidRequest_Imp_Banner_BannerAdType = 1
	// "Usually mobile".
	BidRequest_Imp_Banner_XHTML_BANNER_AD BidRequest_Imp_Banner_BannerAdType = 2
	// Javascript must be valid XHTML (ie, script tags included).
	BidRequest_Imp_Banner_JAVASCRIPT_AD BidRequest_Imp_Banner_BannerAdType = 3
	// Iframe.
	BidRequest_Imp_Banner_IFRAME BidRequest_Imp_Banner_BannerAdType = 4
)

// Enum value maps for BidRequest_Imp_Banner_BannerAdType.
var (
	BidRequest_Imp_Banner_BannerAdType_name = map[int32]string{
		1: "XHTML_TEXT_AD",
		2: "XHTML_BANNER_AD",
		3: "JAVASCRIPT_AD",
		4: "IFRAME",
	}
	BidRequest_Imp_Banner_BannerAdType_value = map[string]int32{
		"XHTML_TEXT_AD":   1,
		"XHTML_BANNER_AD": 2,
		"JAVASCRIPT_AD":   3,
		"IFRAME":          4,
	}
)

func (x BidRequest_Imp_Banner_BannerAdType) Enum() *BidRequest_Imp_Banner_BannerAdType {
	p := new(BidRequest_Imp_Banner_BannerAdType)
	*p = x
	return p
}

func (x BidRequest_Imp_Banner_BannerAdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Banner_BannerAdType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[5].Descriptor()
}

func (BidRequest_Imp_Banner_BannerAdType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[5]
}

func (x BidRequest_Imp_Banner_BannerAdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Banner_BannerAdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Banner_BannerAdType(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Banner_BannerAdType.Descriptor instead.
func (BidRequest_Imp_Banner_BannerAdType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

// OpenRTB 5.5: The following table lists the directions in which an
// expandable ad may expand, given the positioning of the ad unit on the
// page and constraints imposed by the content.
type BidRequest_Imp_Banner_ExpandableDirection int32

const (
	BidRequest_Imp_Banner_LEFT                  BidRequest_Imp_Banner_ExpandableDirection = 1
	BidRequest_Imp_Banner_RIGHT                 BidRequest_Imp_Banner_ExpandableDirection = 2
	BidRequest_Imp_Banner_UP                    BidRequest_Imp_Banner_ExpandableDirection = 3
	BidRequest_Imp_Banner_DOWN                  BidRequest_Imp_Banner_ExpandableDirection = 4
	BidRequest_Imp_Banner_EXPANDABLE_FULLSCREEN BidRequest_Imp_Banner_ExpandableDirection = 5
)

// Enum value maps for BidRequest_Imp_Banner_ExpandableDirection.
var (
	BidRequest_Imp_Banner_ExpandableDirection_name = map[int32]string{
		1: "LEFT",
		2: "RIGHT",
		3: "UP",
		4: "DOWN",
		5: "EXPANDABLE_FULLSCREEN",
	}
	BidRequest_Imp_Banner_ExpandableDirection_value = map[string]int32{
		"LEFT":                  1,
		"RIGHT":                 2,
		"UP":                    3,
		"DOWN":                  4,
		"EXPANDABLE_FULLSCREEN": 5,
	}
)

func (x BidRequest_Imp_Banner_ExpandableDirection) Enum() *BidRequest_Imp_Banner_ExpandableDirection {
	p := new(BidRequest_Imp_Banner_ExpandableDirection)
	*p = x
	return p
}

func (x BidRequest_Imp_Banner_ExpandableDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Banner_ExpandableDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[6].Descriptor()
}

func (BidRequest_Imp_Banner_ExpandableDirection) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[6]
}

func (x BidRequest_Imp_Banner_ExpandableDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Banner_ExpandableDirection) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Banner_ExpandableDirection(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Banner_ExpandableDirection.Descriptor instead.
func (BidRequest_Imp_Banner_ExpandableDirection) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 0, 1}
}

// OpenRTB 5.7: The following table indicates the options for video
// linearity. "In-stream" or "linear" video refers to pre-roll, post-roll,
// or mid-roll video ads where the user is forced to watch ad in order to
// see the video content. "Overlay" or "non-linear" refer to ads that are
// shown on top of the video content.
//
// This field is optional. The following is the interpretation of the
// bidder based upon presence or absence of the field in the bid request:
//   - If no value is set, any ad (linear or not) can be present
//     in the response.
//   - If a value is set, only ads of the corresponding type can be present
//     in the response.
//
// This OpenRTB table has values derived from the IAB Quality Assurance
// Guidelines (QAG). Practitioners should keep in sync with updates to the
// QAG values as published on IAB.net.
type BidRequest_Imp_Video_VideoLinearity int32

const (
	BidRequest_Imp_Video_LINEAR     BidRequest_Imp_Video_VideoLinearity = 1 // Linear/In-stream
	BidRequest_Imp_Video_NON_LINEAR BidRequest_Imp_Video_VideoLinearity = 2 // Non-linear/Overlay
)

// Enum value maps for BidRequest_Imp_Video_VideoLinearity.
var (
	BidRequest_Imp_Video_VideoLinearity_name = map[int32]string{
		1: "LINEAR",
		2: "NON_LINEAR",
	}
	BidRequest_Imp_Video_VideoLinearity_value = map[string]int32{
		"LINEAR":     1,
		"NON_LINEAR": 2,
	}
)

func (x BidRequest_Imp_Video_VideoLinearity) Enum() *BidRequest_Imp_Video_VideoLinearity {
	p := new(BidRequest_Imp_Video_VideoLinearity)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VideoLinearity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VideoLinearity) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[7].Descriptor()
}

func (BidRequest_Imp_Video_VideoLinearity) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[7]
}

func (x BidRequest_Imp_Video_VideoLinearity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VideoLinearity) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VideoLinearity(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VideoLinearity.Descriptor instead.
func (BidRequest_Imp_Video_VideoLinearity) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 0}
}

// OpenRTB 5.8: The following table lists the options for video bid
// response protocols that could be supported by an exchange.
type BidRequest_Imp_Video_VideoBidResponseProtocol int32

const (
	BidRequest_Imp_Video_VAST_1_0         BidRequest_Imp_Video_VideoBidResponseProtocol = 1
	BidRequest_Imp_Video_VAST_2_0         BidRequest_Imp_Video_VideoBidResponseProtocol = 2
	BidRequest_Imp_Video_VAST_3_0         BidRequest_Imp_Video_VideoBidResponseProtocol = 3
	BidRequest_Imp_Video_VAST_1_0_WRAPPER BidRequest_Imp_Video_VideoBidResponseProtocol = 4
	BidRequest_Imp_Video_VAST_2_0_WRAPPER BidRequest_Imp_Video_VideoBidResponseProtocol = 5
	BidRequest_Imp_Video_VAST_3_0_WRAPPER BidRequest_Imp_Video_VideoBidResponseProtocol = 6
)

// Enum value maps for BidRequest_Imp_Video_VideoBidResponseProtocol.
var (
	BidRequest_Imp_Video_VideoBidResponseProtocol_name = map[int32]string{
		1: "VAST_1_0",
		2: "VAST_2_0",
		3: "VAST_3_0",
		4: "VAST_1_0_WRAPPER",
		5: "VAST_2_0_WRAPPER",
		6: "VAST_3_0_WRAPPER",
	}
	BidRequest_Imp_Video_VideoBidResponseProtocol_value = map[string]int32{
		"VAST_1_0":         1,
		"VAST_2_0":         2,
		"VAST_3_0":         3,
		"VAST_1_0_WRAPPER": 4,
		"VAST_2_0_WRAPPER": 5,
		"VAST_3_0_WRAPPER": 6,
	}
)

func (x BidRequest_Imp_Video_VideoBidResponseProtocol) Enum() *BidRequest_Imp_Video_VideoBidResponseProtocol {
	p := new(BidRequest_Imp_Video_VideoBidResponseProtocol)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VideoBidResponseProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VideoBidResponseProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[8].Descriptor()
}

func (BidRequest_Imp_Video_VideoBidResponseProtocol) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[8]
}

func (x BidRequest_Imp_Video_VideoBidResponseProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VideoBidResponseProtocol) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VideoBidResponseProtocol(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VideoBidResponseProtocol.Descriptor instead.
func (BidRequest_Imp_Video_VideoBidResponseProtocol) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 1}
}

// OpenRTB 5.9: The following table lists the various
// video playback methods.
type BidRequest_Imp_Video_VideoPlaybackMethod int32

const (
	BidRequest_Imp_Video_AUTO_PLAY_SOUND_ON  BidRequest_Imp_Video_VideoPlaybackMethod = 1
	BidRequest_Imp_Video_AUTO_PLAY_SOUND_OFF BidRequest_Imp_Video_VideoPlaybackMethod = 2
	BidRequest_Imp_Video_CLICK_TO_PLAY       BidRequest_Imp_Video_VideoPlaybackMethod = 3
	BidRequest_Imp_Video_MOUSE_OVER          BidRequest_Imp_Video_VideoPlaybackMethod = 4
)

// Enum value maps for BidRequest_Imp_Video_VideoPlaybackMethod.
var (
	BidRequest_Imp_Video_VideoPlaybackMethod_name = map[int32]string{
		1: "AUTO_PLAY_SOUND_ON",
		2: "AUTO_PLAY_SOUND_OFF",
		3: "CLICK_TO_PLAY",
		4: "MOUSE_OVER",
	}
	BidRequest_Imp_Video_VideoPlaybackMethod_value = map[string]int32{
		"AUTO_PLAY_SOUND_ON":  1,
		"AUTO_PLAY_SOUND_OFF": 2,
		"CLICK_TO_PLAY":       3,
		"MOUSE_OVER":          4,
	}
)

func (x BidRequest_Imp_Video_VideoPlaybackMethod) Enum() *BidRequest_Imp_Video_VideoPlaybackMethod {
	p := new(BidRequest_Imp_Video_VideoPlaybackMethod)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VideoPlaybackMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VideoPlaybackMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[9].Descriptor()
}

func (BidRequest_Imp_Video_VideoPlaybackMethod) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[9]
}

func (x BidRequest_Imp_Video_VideoPlaybackMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VideoPlaybackMethod) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VideoPlaybackMethod(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VideoPlaybackMethod.Descriptor instead.
func (BidRequest_Imp_Video_VideoPlaybackMethod) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 2}
}

// OpenRTB 5.10: The following table lists the various options for the
// video start delay.  If the start delay value is greater than 0,
// then the position is mid-roll and the value indicates the start delay.
type BidRequest_Imp_Video_VideoStartDelay int32

const (
	BidRequest_Imp_Video_PRE_ROLL          BidRequest_Imp_Video_VideoStartDelay = 0
	BidRequest_Imp_Video_GENERIC_MID_ROLL  BidRequest_Imp_Video_VideoStartDelay = -1
	BidRequest_Imp_Video_GENERIC_POST_ROLL BidRequest_Imp_Video_VideoStartDelay = -2
)

// Enum value maps for BidRequest_Imp_Video_VideoStartDelay.
var (
	BidRequest_Imp_Video_VideoStartDelay_name = map[int32]string{
		0:  "PRE_ROLL",
		-1: "GENERIC_MID_ROLL",
		-2: "GENERIC_POST_ROLL",
	}
	BidRequest_Imp_Video_VideoStartDelay_value = map[string]int32{
		"PRE_ROLL":          0,
		"GENERIC_MID_ROLL":  -1,
		"GENERIC_POST_ROLL": -2,
	}
)

func (x BidRequest_Imp_Video_VideoStartDelay) Enum() *BidRequest_Imp_Video_VideoStartDelay {
	p := new(BidRequest_Imp_Video_VideoStartDelay)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VideoStartDelay) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VideoStartDelay) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[10].Descriptor()
}

func (BidRequest_Imp_Video_VideoStartDelay) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[10]
}

func (x BidRequest_Imp_Video_VideoStartDelay) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VideoStartDelay) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VideoStartDelay(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VideoStartDelay.Descriptor instead.
func (BidRequest_Imp_Video_VideoStartDelay) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 3}
}

// OpenRTB 5.12: The following table lists the options for the
// video quality. These values are defined by the IAB -
// http://www.iab.net/media/file/long-form-video-final.pdf.
type BidRequest_Imp_Video_VASTCompanionType int32

const (
	BidRequest_Imp_Video_STATIC           BidRequest_Imp_Video_VASTCompanionType = 1
	BidRequest_Imp_Video_HTML             BidRequest_Imp_Video_VASTCompanionType = 2
	BidRequest_Imp_Video_COMPANION_IFRAME BidRequest_Imp_Video_VASTCompanionType = 3
)

// Enum value maps for BidRequest_Imp_Video_VASTCompanionType.
var (
	BidRequest_Imp_Video_VASTCompanionType_name = map[int32]string{
		1: "STATIC",
		2: "HTML",
		3: "COMPANION_IFRAME",
	}
	BidRequest_Imp_Video_VASTCompanionType_value = map[string]int32{
		"STATIC":           1,
		"HTML":             2,
		"COMPANION_IFRAME": 3,
	}
)

func (x BidRequest_Imp_Video_VASTCompanionType) Enum() *BidRequest_Imp_Video_VASTCompanionType {
	p := new(BidRequest_Imp_Video_VASTCompanionType)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VASTCompanionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VASTCompanionType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[11].Descriptor()
}

func (BidRequest_Imp_Video_VASTCompanionType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[11]
}

func (x BidRequest_Imp_Video_VASTCompanionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VASTCompanionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VASTCompanionType(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VASTCompanionType.Descriptor instead.
func (BidRequest_Imp_Video_VASTCompanionType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 4}
}

// OpenRTB 5.13: The following table lists the various options for the
// delivery of video content.
type BidRequest_Imp_Video_ContentDeliveryMethod int32

const (
	BidRequest_Imp_Video_STREAMING   BidRequest_Imp_Video_ContentDeliveryMethod = 1
	BidRequest_Imp_Video_PROGRESSIVE BidRequest_Imp_Video_ContentDeliveryMethod = 2
)

// Enum value maps for BidRequest_Imp_Video_ContentDeliveryMethod.
var (
	BidRequest_Imp_Video_ContentDeliveryMethod_name = map[int32]string{
		1: "STREAMING",
		2: "PROGRESSIVE",
	}
	BidRequest_Imp_Video_ContentDeliveryMethod_value = map[string]int32{
		"STREAMING":   1,
		"PROGRESSIVE": 2,
	}
)

func (x BidRequest_Imp_Video_ContentDeliveryMethod) Enum() *BidRequest_Imp_Video_ContentDeliveryMethod {
	p := new(BidRequest_Imp_Video_ContentDeliveryMethod)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_ContentDeliveryMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_ContentDeliveryMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[12].Descriptor()
}

func (BidRequest_Imp_Video_ContentDeliveryMethod) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[12]
}

func (x BidRequest_Imp_Video_ContentDeliveryMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_ContentDeliveryMethod) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_ContentDeliveryMethod(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_ContentDeliveryMethod.Descriptor instead.
func (BidRequest_Imp_Video_ContentDeliveryMethod) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 5}
}

// OpenRTB 5.11: The following table lists the options for the video quality.
// These values are defined by the IAB -
// http://www.iab.net/media/file/long-form-video-final.pdf.
type BidRequest_Content_VideoQuality int32

const (
	BidRequest_Content_QUALITY_UNKNOWN BidRequest_Content_VideoQuality = 0
	BidRequest_Content_PROFESSIONAL    BidRequest_Content_VideoQuality = 1
	BidRequest_Content_PROSUMER        BidRequest_Content_VideoQuality = 2
	BidRequest_Content_USER_GENERATED  BidRequest_Content_VideoQuality = 3
)

// Enum value maps for BidRequest_Content_VideoQuality.
var (
	BidRequest_Content_VideoQuality_name = map[int32]string{
		0: "QUALITY_UNKNOWN",
		1: "PROFESSIONAL",
		2: "PROSUMER",
		3: "USER_GENERATED",
	}
	BidRequest_Content_VideoQuality_value = map[string]int32{
		"QUALITY_UNKNOWN": 0,
		"PROFESSIONAL":    1,
		"PROSUMER":        2,
		"USER_GENERATED":  3,
	}
)

func (x BidRequest_Content_VideoQuality) Enum() *BidRequest_Content_VideoQuality {
	p := new(BidRequest_Content_VideoQuality)
	*p = x
	return p
}

func (x BidRequest_Content_VideoQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_VideoQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[13].Descriptor()
}

func (BidRequest_Content_VideoQuality) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[13]
}

func (x BidRequest_Content_VideoQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_VideoQuality) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_VideoQuality(num)
	return nil
}

// Deprecated: Use BidRequest_Content_VideoQuality.Descriptor instead.
func (BidRequest_Content_VideoQuality) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 4, 0}
}

// OpenRTB 5.14: The following table lists the various options for
// indicating the type of content in which the impression will appear.
//
// This OpenRTB table has values derived from the IAB Quality Assurance
// Guidelines (QAG). Practitioners should keep in sync with updates to the
// QAG values as published on IAB.net.
type BidRequest_Content_ContentContext int32

const (
	BidRequest_Content_VIDEO           BidRequest_Content_ContentContext = 1
	BidRequest_Content_GAME            BidRequest_Content_ContentContext = 2
	BidRequest_Content_MUSIC           BidRequest_Content_ContentContext = 3
	BidRequest_Content_APPLICATION     BidRequest_Content_ContentContext = 4
	BidRequest_Content_TEXT            BidRequest_Content_ContentContext = 5
	BidRequest_Content_OTHER           BidRequest_Content_ContentContext = 6
	BidRequest_Content_CONTEXT_UNKNOWN BidRequest_Content_ContentContext = 7
)

// Enum value maps for BidRequest_Content_ContentContext.
var (
	BidRequest_Content_ContentContext_name = map[int32]string{
		1: "VIDEO",
		2: "GAME",
		3: "MUSIC",
		4: "APPLICATION",
		5: "TEXT",
		6: "OTHER",
		7: "CONTEXT_UNKNOWN",
	}
	BidRequest_Content_ContentContext_value = map[string]int32{
		"VIDEO":           1,
		"GAME":            2,
		"MUSIC":           3,
		"APPLICATION":     4,
		"TEXT":            5,
		"OTHER":           6,
		"CONTEXT_UNKNOWN": 7,
	}
)

func (x BidRequest_Content_ContentContext) Enum() *BidRequest_Content_ContentContext {
	p := new(BidRequest_Content_ContentContext)
	*p = x
	return p
}

func (x BidRequest_Content_ContentContext) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_ContentContext) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[14].Descriptor()
}

func (BidRequest_Content_ContentContext) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[14]
}

func (x BidRequest_Content_ContentContext) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_ContentContext) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_ContentContext(num)
	return nil
}

// Deprecated: Use BidRequest_Content_ContentContext.Descriptor instead.
func (BidRequest_Content_ContentContext) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 4, 1}
}

// OpenRTB 5.15: The following table lists the media ratings used in
// describing content based on the QAG categorization.
// Refer to http://www.iab.net/ne_guidelines for more information.
type BidRequest_Content_QAGMediaRating int32

const (
	BidRequest_Content_ALL_AUDIENCES    BidRequest_Content_QAGMediaRating = 1
	BidRequest_Content_EVERYONE_OVER_12 BidRequest_Content_QAGMediaRating = 2
	BidRequest_Content_MATURE           BidRequest_Content_QAGMediaRating = 3
)

// Enum value maps for BidRequest_Content_QAGMediaRating.
var (
	BidRequest_Content_QAGMediaRating_name = map[int32]string{
		1: "ALL_AUDIENCES",
		2: "EVERYONE_OVER_12",
		3: "MATURE",
	}
	BidRequest_Content_QAGMediaRating_value = map[string]int32{
		"ALL_AUDIENCES":    1,
		"EVERYONE_OVER_12": 2,
		"MATURE":           3,
	}
)

func (x BidRequest_Content_QAGMediaRating) Enum() *BidRequest_Content_QAGMediaRating {
	p := new(BidRequest_Content_QAGMediaRating)
	*p = x
	return p
}

func (x BidRequest_Content_QAGMediaRating) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_QAGMediaRating) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[15].Descriptor()
}

func (BidRequest_Content_QAGMediaRating) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[15]
}

func (x BidRequest_Content_QAGMediaRating) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_QAGMediaRating) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_QAGMediaRating(num)
	return nil
}

// Deprecated: Use BidRequest_Content_QAGMediaRating.Descriptor instead.
func (BidRequest_Content_QAGMediaRating) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 4, 2}
}

// OpenRTB 5.17: The following table lists the type of device from which the
// impression originated.
//
// OpenRTB version 2.2 of the specification added distinct values for Mobile
// and Tablet. It is recommended that any bidder adding support for 2.2
// treat a value of 1 as an acceptable alias of 4 & 5.
//
// This OpenRTB table has values derived from the IAB Quality Assurance
// Guidelines (QAG). Practitioners should keep in sync with updates to the
// QAG values as published on IAB.net.
type BidRequest_Device_DeviceType int32

const (
	BidRequest_Device_MOBILE            BidRequest_Device_DeviceType = 1 // Mobile/Tablet (also see TABLET).
	BidRequest_Device_PERSONAL_COMPUTER BidRequest_Device_DeviceType = 2 // Personal Computer.
	BidRequest_Device_CONNECTED_TV      BidRequest_Device_DeviceType = 3 // Connected TV.
	BidRequest_Device_PHONE             BidRequest_Device_DeviceType = 4 // Phone.
	BidRequest_Device_TABLET            BidRequest_Device_DeviceType = 5 // Tablet.
	BidRequest_Device_CONNECTED_DEVICE  BidRequest_Device_DeviceType = 6 // Connected device.
	BidRequest_Device_SET_TOP_BOX       BidRequest_Device_DeviceType = 7 // Set top box.
)

// Enum value maps for BidRequest_Device_DeviceType.
var (
	BidRequest_Device_DeviceType_name = map[int32]string{
		1: "MOBILE",
		2: "PERSONAL_COMPUTER",
		3: "CONNECTED_TV",
		4: "PHONE",
		5: "TABLET",
		6: "CONNECTED_DEVICE",
		7: "SET_TOP_BOX",
	}
	BidRequest_Device_DeviceType_value = map[string]int32{
		"MOBILE":            1,
		"PERSONAL_COMPUTER": 2,
		"CONNECTED_TV":      3,
		"PHONE":             4,
		"TABLET":            5,
		"CONNECTED_DEVICE":  6,
		"SET_TOP_BOX":       7,
	}
)

func (x BidRequest_Device_DeviceType) Enum() *BidRequest_Device_DeviceType {
	p := new(BidRequest_Device_DeviceType)
	*p = x
	return p
}

func (x BidRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[16].Descriptor()
}

func (BidRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[16]
}

func (x BidRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_DeviceType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_DeviceType.Descriptor instead.
func (BidRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 6, 0}
}

// OpenRTB 5.18: The following table lists the various options for the
// type of device connectivity.
type BidRequest_Device_ConnectionType int32

const (
	BidRequest_Device_CONNECTION_UNKNOWN BidRequest_Device_ConnectionType = 0
	BidRequest_Device_ETHERNET           BidRequest_Device_ConnectionType = 1
	BidRequest_Device_WIFI               BidRequest_Device_ConnectionType = 2
	BidRequest_Device_CELL_UNKNOWN       BidRequest_Device_ConnectionType = 3
	BidRequest_Device_CELL_2G            BidRequest_Device_ConnectionType = 4
	BidRequest_Device_CELL_3G            BidRequest_Device_ConnectionType = 5
	BidRequest_Device_CELL_4G            BidRequest_Device_ConnectionType = 6
)

// Enum value maps for BidRequest_Device_ConnectionType.
var (
	BidRequest_Device_ConnectionType_name = map[int32]string{
		0: "CONNECTION_UNKNOWN",
		1: "ETHERNET",
		2: "WIFI",
		3: "CELL_UNKNOWN",
		4: "CELL_2G",
		5: "CELL_3G",
		6: "CELL_4G",
	}
	BidRequest_Device_ConnectionType_value = map[string]int32{
		"CONNECTION_UNKNOWN": 0,
		"ETHERNET":           1,
		"WIFI":               2,
		"CELL_UNKNOWN":       3,
		"CELL_2G":            4,
		"CELL_3G":            5,
		"CELL_4G":            6,
	}
)

func (x BidRequest_Device_ConnectionType) Enum() *BidRequest_Device_ConnectionType {
	p := new(BidRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x BidRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[17].Descriptor()
}

func (BidRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[17]
}

func (x BidRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_ConnectionType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_ConnectionType.Descriptor instead.
func (BidRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 6, 1}
}

// OpenRTB 5.16: The following table lists the options to indicate how the
// geographic information was determined.
type BidRequest_Geo_LocationType int32

const (
	BidRequest_Geo_GPS_LOCATION  BidRequest_Geo_LocationType = 1
	BidRequest_Geo_IP            BidRequest_Geo_LocationType = 2
	BidRequest_Geo_USER_PROVIDED BidRequest_Geo_LocationType = 3
)

// Enum value maps for BidRequest_Geo_LocationType.
var (
	BidRequest_Geo_LocationType_name = map[int32]string{
		1: "GPS_LOCATION",
		2: "IP",
		3: "USER_PROVIDED",
	}
	BidRequest_Geo_LocationType_value = map[string]int32{
		"GPS_LOCATION":  1,
		"IP":            2,
		"USER_PROVIDED": 3,
	}
)

func (x BidRequest_Geo_LocationType) Enum() *BidRequest_Geo_LocationType {
	p := new(BidRequest_Geo_LocationType)
	*p = x
	return p
}

func (x BidRequest_Geo_LocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Geo_LocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[18].Descriptor()
}

func (BidRequest_Geo_LocationType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[18]
}

func (x BidRequest_Geo_LocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Geo_LocationType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Geo_LocationType(num)
	return nil
}

// Deprecated: Use BidRequest_Geo_LocationType.Descriptor instead.
func (BidRequest_Geo_LocationType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 7, 0}
}

type BidRequest_User_Gender int32

const (
	BidRequest_User_MALE   BidRequest_User_Gender = 1 // "M"
	BidRequest_User_FEMALE BidRequest_User_Gender = 2 // "F"
	BidRequest_User_OTHER  BidRequest_User_Gender = 3 // "O"
)

// Enum value maps for BidRequest_User_Gender.
var (
	BidRequest_User_Gender_name = map[int32]string{
		1: "MALE",
		2: "FEMALE",
		3: "OTHER",
	}
	BidRequest_User_Gender_value = map[string]int32{
		"MALE":   1,
		"FEMALE": 2,
		"OTHER":  3,
	}
)

func (x BidRequest_User_Gender) Enum() *BidRequest_User_Gender {
	p := new(BidRequest_User_Gender)
	*p = x
	return p
}

func (x BidRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[19].Descriptor()
}

func (BidRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[19]
}

func (x BidRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_User_Gender) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_User_Gender(num)
	return nil
}

// Deprecated: Use BidRequest_User_Gender.Descriptor instead.
func (BidRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 8, 0}
}

// OpenRTB 5.19: The following table lists the options for a bidder to signal
// the exchange as to why it did not offer a bid for the impression.
type BidResponse_NoBidReason int32

const (
	BidResponse_UNKNOWN_ERROR              BidResponse_NoBidReason = 0
	BidResponse_TECHNICAL_ERROR            BidResponse_NoBidReason = 1
	BidResponse_INVALID_REQUEST            BidResponse_NoBidReason = 2
	BidResponse_KNOWN_WEB_SPIDER           BidResponse_NoBidReason = 3
	BidResponse_SUSPECTED_NONHUMAN_TRAFFIC BidResponse_NoBidReason = 4
	BidResponse_CLOUD_DATACENTER_PROXYIP   BidResponse_NoBidReason = 5
	BidResponse_UNSUPPORTED_DEVICE         BidResponse_NoBidReason = 6
	BidResponse_BLOCKED_PUBLISHER          BidResponse_NoBidReason = 7
	BidResponse_UNMATCHED_USER             BidResponse_NoBidReason = 8
)

// Enum value maps for BidResponse_NoBidReason.
var (
	BidResponse_NoBidReason_name = map[int32]string{
		0: "UNKNOWN_ERROR",
		1: "TECHNICAL_ERROR",
		2: "INVALID_REQUEST",
		3: "KNOWN_WEB_SPIDER",
		4: "SUSPECTED_NONHUMAN_TRAFFIC",
		5: "CLOUD_DATACENTER_PROXYIP",
		6: "UNSUPPORTED_DEVICE",
		7: "BLOCKED_PUBLISHER",
		8: "UNMATCHED_USER",
	}
	BidResponse_NoBidReason_value = map[string]int32{
		"UNKNOWN_ERROR":              0,
		"TECHNICAL_ERROR":            1,
		"INVALID_REQUEST":            2,
		"KNOWN_WEB_SPIDER":           3,
		"SUSPECTED_NONHUMAN_TRAFFIC": 4,
		"CLOUD_DATACENTER_PROXYIP":   5,
		"UNSUPPORTED_DEVICE":         6,
		"BLOCKED_PUBLISHER":          7,
		"UNMATCHED_USER":             8,
	}
)

func (x BidResponse_NoBidReason) Enum() *BidResponse_NoBidReason {
	p := new(BidResponse_NoBidReason)
	*p = x
	return p
}

func (x BidResponse_NoBidReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_NoBidReason) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[20].Descriptor()
}

func (BidResponse_NoBidReason) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[20]
}

func (x BidResponse_NoBidReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_NoBidReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_NoBidReason(num)
	return nil
}

// Deprecated: Use BidResponse_NoBidReason.Descriptor instead.
func (BidResponse_NoBidReason) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{1, 0}
}

// OpenRTB Native 7.1: Core layouts. An implementing exchange may not support
// all asset variants or introduce new ones unique to that system.
type NativeRequest_LayoutId int32

const (
	NativeRequest_CONTENT_WALL   NativeRequest_LayoutId = 1
	NativeRequest_APP_WALL       NativeRequest_LayoutId = 2
	NativeRequest_NEWS_FEED      NativeRequest_LayoutId = 3
	NativeRequest_CHAT_LIST      NativeRequest_LayoutId = 4
	NativeRequest_CAROUSEL       NativeRequest_LayoutId = 5
	NativeRequest_CONTENT_STREAM NativeRequest_LayoutId = 6
	NativeRequest_GRID           NativeRequest_LayoutId = 7 // Exchange-specific values above 500.
)

// Enum value maps for NativeRequest_LayoutId.
var (
	NativeRequest_LayoutId_name = map[int32]string{
		1: "CONTENT_WALL",
		2: "APP_WALL",
		3: "NEWS_FEED",
		4: "CHAT_LIST",
		5: "CAROUSEL",
		6: "CONTENT_STREAM",
		7: "GRID",
	}
	NativeRequest_LayoutId_value = map[string]int32{
		"CONTENT_WALL":   1,
		"APP_WALL":       2,
		"NEWS_FEED":      3,
		"CHAT_LIST":      4,
		"CAROUSEL":       5,
		"CONTENT_STREAM": 6,
		"GRID":           7,
	}
)

func (x NativeRequest_LayoutId) Enum() *NativeRequest_LayoutId {
	p := new(NativeRequest_LayoutId)
	*p = x
	return p
}

func (x NativeRequest_LayoutId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NativeRequest_LayoutId) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[21].Descriptor()
}

func (NativeRequest_LayoutId) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[21]
}

func (x NativeRequest_LayoutId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NativeRequest_LayoutId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NativeRequest_LayoutId(num)
	return nil
}

// Deprecated: Use NativeRequest_LayoutId.Descriptor instead.
func (NativeRequest_LayoutId) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0}
}

// OpenRTB Native 7.2: Below is a list of the core ad unit ids described by
// IAB: http://www.iab.net/media/file/IABNativeAdvertisingPlaybook120413.pdf.
// In feed unit is essentially a layout, it has been removed from the list.
// The in feed units can be identified via the layout parameter on the
// request. An implementing exchange may not support all asset variants or
// introduce new ones unique to that system.
type NativeRequest_AdUnitId int32

const (
	NativeRequest_PAID_SEARCH_UNIT      NativeRequest_AdUnitId = 1
	NativeRequest_RECOMMENDATION_WIDGET NativeRequest_AdUnitId = 2
	NativeRequest_PROMOTED_LISTING      NativeRequest_AdUnitId = 3
	NativeRequest_IAB_IN_AD_NATIVE      NativeRequest_AdUnitId = 4
	NativeRequest_CUSTOM                NativeRequest_AdUnitId = 5 // Exchange-specific values above 500.
)

// Enum value maps for NativeRequest_AdUnitId.
var (
	NativeRequest_AdUnitId_name = map[int32]string{
		1: "PAID_SEARCH_UNIT",
		2: "RECOMMENDATION_WIDGET",
		3: "PROMOTED_LISTING",
		4: "IAB_IN_AD_NATIVE",
		5: "CUSTOM",
	}
	NativeRequest_AdUnitId_value = map[string]int32{
		"PAID_SEARCH_UNIT":      1,
		"RECOMMENDATION_WIDGET": 2,
		"PROMOTED_LISTING":      3,
		"IAB_IN_AD_NATIVE":      4,
		"CUSTOM":                5,
	}
)

func (x NativeRequest_AdUnitId) Enum() *NativeRequest_AdUnitId {
	p := new(NativeRequest_AdUnitId)
	*p = x
	return p
}

func (x NativeRequest_AdUnitId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NativeRequest_AdUnitId) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[22].Descriptor()
}

func (NativeRequest_AdUnitId) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[22]
}

func (x NativeRequest_AdUnitId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NativeRequest_AdUnitId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NativeRequest_AdUnitId(num)
	return nil
}

// Deprecated: Use NativeRequest_AdUnitId.Descriptor instead.
func (NativeRequest_AdUnitId) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 1}
}

// OpenRTB Native 7.4: Common image asset element types of native
// advertising at the time of writing this spec. This list is
// non-exhaustive and intended to be extended by the buyers and sellers
// as the format evolves.
type NativeRequest_Asset_Image_ImageAssetType int32

const (
	// Icon image.
	NativeRequest_Asset_Image_ICON NativeRequest_Asset_Image_ImageAssetType = 1
	// Logo image for the brand/app.
	NativeRequest_Asset_Image_LOGO NativeRequest_Asset_Image_ImageAssetType = 2
	// Large image preview for the ad.
	NativeRequest_Asset_Image_MAIN NativeRequest_Asset_Image_ImageAssetType = 3 // Exchange-specific values above 500.
)

// Enum value maps for NativeRequest_Asset_Image_ImageAssetType.
var (
	NativeRequest_Asset_Image_ImageAssetType_name = map[int32]string{
		1: "ICON",
		2: "LOGO",
		3: "MAIN",
	}
	NativeRequest_Asset_Image_ImageAssetType_value = map[string]int32{
		"ICON": 1,
		"LOGO": 2,
		"MAIN": 3,
	}
)

func (x NativeRequest_Asset_Image_ImageAssetType) Enum() *NativeRequest_Asset_Image_ImageAssetType {
	p := new(NativeRequest_Asset_Image_ImageAssetType)
	*p = x
	return p
}

func (x NativeRequest_Asset_Image_ImageAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NativeRequest_Asset_Image_ImageAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[23].Descriptor()
}

func (NativeRequest_Asset_Image_ImageAssetType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[23]
}

func (x NativeRequest_Asset_Image_ImageAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NativeRequest_Asset_Image_ImageAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NativeRequest_Asset_Image_ImageAssetType(num)
	return nil
}

// Deprecated: Use NativeRequest_Asset_Image_ImageAssetType.Descriptor instead.
func (NativeRequest_Asset_Image_ImageAssetType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0, 1, 0}
}

// OpenRTB Native 7.3: Common asset element types of native advertising.
// This list is non-exhaustive and intended to be extended by the buyers
// and sellers as the format evolves. An implementing exchange may not
// support all asset variants or introduce new ones unique to that system.
type NativeRequest_Asset_Data_DataAssetType int32

const (
	// Sponsored By message where response should contain the brand name
	// of the sponsor.
	NativeRequest_Asset_Data_SPONSORED NativeRequest_Asset_Data_DataAssetType = 1
	// Descriptive text associated with the product or service being
	// advertised.
	NativeRequest_Asset_Data_DESC NativeRequest_Asset_Data_DataAssetType = 2
	// Rating of the product being offered to the user.
	// For example an app's rating in an app store from 0-5.
	NativeRequest_Asset_Data_RATING NativeRequest_Asset_Data_DataAssetType = 3
	// Number of social ratings or "likes" of the product being offered
	// to the user.
	NativeRequest_Asset_Data_LIKES NativeRequest_Asset_Data_DataAssetType = 4
	// Number downloads/installs of this product.
	NativeRequest_Asset_Data_DOWNLOADS NativeRequest_Asset_Data_DataAssetType = 5
	// Price for product / app / in-app purchase.
	// Value should include currency symbol in localised format.
	NativeRequest_Asset_Data_PRICE NativeRequest_Asset_Data_DataAssetType = 6
	// Sale price that can be used together with price to indicate a
	// discounted price compared to a regular price. Value should include
	// currency symbol in localised format.
	NativeRequest_Asset_Data_SALEPRICE NativeRequest_Asset_Data_DataAssetType = 7
	// Phone number.
	NativeRequest_Asset_Data_PHONE NativeRequest_Asset_Data_DataAssetType = 8
	// Address.
	NativeRequest_Asset_Data_ADDRESS NativeRequest_Asset_Data_DataAssetType = 9
	// Additional descriptive text associated with the product or service
	// being advertised.
	NativeRequest_Asset_Data_DESC2 NativeRequest_Asset_Data_DataAssetType = 10
	// Display URL for the text ad.
	NativeRequest_Asset_Data_DISPLAYURL NativeRequest_Asset_Data_DataAssetType = 11
	// Text describing a "call to action" button for the destination URL.
	NativeRequest_Asset_Data_CTATEXT NativeRequest_Asset_Data_DataAssetType = 12 // Exchange-specific values above 500.
)

// Enum value maps for NativeRequest_Asset_Data_DataAssetType.
var (
	NativeRequest_Asset_Data_DataAssetType_name = map[int32]string{
		1:  "SPONSORED",
		2:  "DESC",
		3:  "RATING",
		4:  "LIKES",
		5:  "DOWNLOADS",
		6:  "PRICE",
		7:  "SALEPRICE",
		8:  "PHONE",
		9:  "ADDRESS",
		10: "DESC2",
		11: "DISPLAYURL",
		12: "CTATEXT",
	}
	NativeRequest_Asset_Data_DataAssetType_value = map[string]int32{
		"SPONSORED":  1,
		"DESC":       2,
		"RATING":     3,
		"LIKES":      4,
		"DOWNLOADS":  5,
		"PRICE":      6,
		"SALEPRICE":  7,
		"PHONE":      8,
		"ADDRESS":    9,
		"DESC2":      10,
		"DISPLAYURL": 11,
		"CTATEXT":    12,
	}
)

func (x NativeRequest_Asset_Data_DataAssetType) Enum() *NativeRequest_Asset_Data_DataAssetType {
	p := new(NativeRequest_Asset_Data_DataAssetType)
	*p = x
	return p
}

func (x NativeRequest_Asset_Data_DataAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NativeRequest_Asset_Data_DataAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_youdao_proto_enumTypes[24].Descriptor()
}

func (NativeRequest_Asset_Data_DataAssetType) Type() protoreflect.EnumType {
	return &file_youdao_proto_enumTypes[24]
}

func (x NativeRequest_Asset_Data_DataAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NativeRequest_Asset_Data_DataAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NativeRequest_Asset_Data_DataAssetType(num)
	return nil
}

// Deprecated: Use NativeRequest_Asset_Data_DataAssetType.Descriptor instead.
func (NativeRequest_Asset_Data_DataAssetType) EnumDescriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0, 2, 0}
}

// OpenRTB 3.2.1: The top-level bid request object contains a globally unique
// bid request or auction ID. This id attribute is required as is at least one
// impression object (Section 3.2.2). Other attributes in this top-level object
// establish rules and restrictions that apply to all impressions being offered.
//
// There are also several subordinate objects that provide detailed data to
// potential buyers. Among these are the Site and App objects, which describe
// the type of published media in which the impression(s) appear.
// These objects are highly recommended, but only one applies to a given
// bid request depending on whether the media is browser-based web content
// or a non-browser application, respectively.
type BidRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Unique ID of the bid request, provided by the exchange.
	// REQUIRED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// Array of Imp objects (Section 3.2.2) representing the impressions offered.
	// At least 1 Imp object is required.
	Imp []*BidRequest_Imp `protobuf:"bytes,2,rep,name=imp" json:"imp,omitempty"`
	// Types that are assignable to DistributionchannelOneof:
	//
	//	*BidRequest_Site_
	//	*BidRequest_App_
	DistributionchannelOneof isBidRequest_DistributionchannelOneof `protobuf_oneof:"distributionchannel_oneof"`
	// Details via a Device object (Section 3.2.11) about the user's
	// device to which the impression will be delivered.
	Device *BidRequest_Device `protobuf:"bytes,5,opt,name=device" json:"device,omitempty"`
	// A Regs object (Section 3.2.16) that specifies any industry, legal,
	// or governmental regulations in force for this request.
	Regs *BidRequest_Regs `protobuf:"bytes,14,opt,name=regs" json:"regs,omitempty"`
	// Details via a User object (Section 3.2.13) about the human
	// user of the device; the advertising audience.
	User *BidRequest_User `protobuf:"bytes,6,opt,name=user" json:"user,omitempty"`
	// Auction type, where 1 = First Price, 2 = Second Price Plus.
	// Exchange-specific auction types can be defined using values > 500.
	At *BidRequest_AuctionType `protobuf:"varint,7,opt,name=at,enum=youdao.BidRequest_AuctionType,def=2" json:"at,omitempty"`
	// Maximum time in milliseconds to submit a bid to avoid timeout.
	// This value is commonly communicated offline.
	Tmax *int32 `protobuf:"varint,8,opt,name=tmax" json:"tmax,omitempty"`
	// Whitelist of buyer seats allowed to bid on this impression.
	// Seat IDs must be communicated between bidders and the exchange a priori.
	// Omission implies no seat restrictions.
	Wseat []string `protobuf:"bytes,9,rep,name=wseat" json:"wseat,omitempty"`
	// Flag to indicate if Exchange can verify that the impressions offered
	// represent all of the impressions available in context (e.g., all on the
	// web page, all video spots such as pre/mid/post roll) to support
	// road-blocking. 0 = no or unknown, 1 = yes, the impressions offered
	// represent all that are available.
	Allimps *bool `protobuf:"varint,10,opt,name=allimps,def=0" json:"allimps,omitempty"`
	// Array of allowed currencies for bids on this bid request using ISO-4217
	// alpha codes. Recommended only if the exchange accepts multiple currencies.
	Cur []string `protobuf:"bytes,11,rep,name=cur" json:"cur,omitempty"`
	// Blocked advertiser categories using the IAB content categories.
	// See enum ContentCategory.
	Bcat []string `protobuf:"bytes,12,rep,name=bcat" json:"bcat,omitempty"`
	// Block list of advertisers by their domains (e.g., "ford.com").
	Badv []string `protobuf:"bytes,13,rep,name=badv" json:"badv,omitempty"`
	// Indicator of test mode in which auctions are not billable,
	// where 0 = live mode, 1 = test mode.
	Test *bool `protobuf:"varint,15,opt,name=test,def=0" json:"test,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_At      = BidRequest_SECOND_PRICE
	Default_BidRequest_Allimps = bool(false)
	Default_BidRequest_Test    = bool(false)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (m *BidRequest) GetDistributionchannelOneof() isBidRequest_DistributionchannelOneof {
	if m != nil {
		return m.DistributionchannelOneof
	}
	return nil
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x, ok := x.GetDistributionchannelOneof().(*BidRequest_Site_); ok {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x, ok := x.GetDistributionchannelOneof().(*BidRequest_App_); ok {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetRegs() *BidRequest_Regs {
	if x != nil {
		return x.Regs
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetAt() BidRequest_AuctionType {
	if x != nil && x.At != nil {
		return *x.At
	}
	return Default_BidRequest_At
}

func (x *BidRequest) GetTmax() int32 {
	if x != nil && x.Tmax != nil {
		return *x.Tmax
	}
	return 0
}

func (x *BidRequest) GetWseat() []string {
	if x != nil {
		return x.Wseat
	}
	return nil
}

func (x *BidRequest) GetAllimps() bool {
	if x != nil && x.Allimps != nil {
		return *x.Allimps
	}
	return Default_BidRequest_Allimps
}

func (x *BidRequest) GetCur() []string {
	if x != nil {
		return x.Cur
	}
	return nil
}

func (x *BidRequest) GetBcat() []string {
	if x != nil {
		return x.Bcat
	}
	return nil
}

func (x *BidRequest) GetBadv() []string {
	if x != nil {
		return x.Badv
	}
	return nil
}

func (x *BidRequest) GetTest() bool {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return Default_BidRequest_Test
}

type isBidRequest_DistributionchannelOneof interface {
	isBidRequest_DistributionchannelOneof()
}

type BidRequest_Site_ struct {
	// Details via a Site object (Section 3.2.6) about the publisher's website.
	// Only applicable and recommended for websites.
	Site *BidRequest_Site `protobuf:"bytes,3,opt,name=site,oneof"`
}

type BidRequest_App_ struct {
	// Details via an App object (Section 3.2.7) about the publisher's app
	// (non-browser applications). Only applicable and recommended for apps.
	App *BidRequest_App `protobuf:"bytes,4,opt,name=app,oneof"`
}

func (*BidRequest_Site_) isBidRequest_DistributionchannelOneof() {}

func (*BidRequest_App_) isBidRequest_DistributionchannelOneof() {}

// OpenRTB 4.2.1: This object is the top-level bid response object (i.e., the
// unnamed outer JSON object). The id attribute is a reflection of the bid
// request ID for logging purposes. Similarly, bidid is an optional response
// tracking ID for bidders. If specified, it can be included in the subsequent
// win notice call if the bidder wins. At least one seatbid object is required,
// which contains at least one bid for an impression. Other attributes are
// optional. To express a "no-bid", the options are to return an empty response
// with HTTP 204. Alternately if the bidder wishes to convey to the exchange a
// reason for not bidding, just a BidResponse object is returned with a
// reason code in the nbr attribute.
type BidResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// ID of the bid request to which this is a response.
	// REQUIRED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// Array of seatbid objects; 1+ required if a bid is to be made.
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,2,rep,name=seatbid" json:"seatbid,omitempty"`
	// Bidder generated response ID to assist with logging/tracking.
	Bidid *string `protobuf:"bytes,3,opt,name=bidid" json:"bidid,omitempty"`
	// Bid currency using ISO-4217 alpha codes.
	Cur *string `protobuf:"bytes,4,opt,name=cur,def=USD" json:"cur,omitempty"`
	// Optional feature to allow a bidder to set data in the exchange's cookie.
	// The string must be in base85 cookie safe characters and be in any format.
	// Proper JSON encoding must be used to include "escaped" quotation marks.
	Customdata *string `protobuf:"bytes,5,opt,name=customdata" json:"customdata,omitempty"`
	// Reason for not bidding.
	Nbr *BidResponse_NoBidReason `protobuf:"varint,6,opt,name=nbr,enum=youdao.BidResponse_NoBidReason" json:"nbr,omitempty"`
}

// Default values for BidResponse fields.
const (
	Default_BidResponse_Cur = string("USD")
)

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetBidid() string {
	if x != nil && x.Bidid != nil {
		return *x.Bidid
	}
	return ""
}

func (x *BidResponse) GetCur() string {
	if x != nil && x.Cur != nil {
		return *x.Cur
	}
	return Default_BidResponse_Cur
}

func (x *BidResponse) GetCustomdata() string {
	if x != nil && x.Customdata != nil {
		return *x.Customdata
	}
	return ""
}

func (x *BidResponse) GetNbr() BidResponse_NoBidReason {
	if x != nil && x.Nbr != nil {
		return *x.Nbr
	}
	return BidResponse_UNKNOWN_ERROR
}

// OpenRTB Native 4.1: The Native Object defines the native advertising
// opportunity available for bid via this bid request. It must be included
// directly in the impression object if the impression offered for auction
// is a native ad format.
type NativeRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Version of the Native Markup version in use.
	Ver *string `protobuf:"bytes,1,opt,name=ver" json:"ver,omitempty"`
	// The Layout ID of the native ad unit.
	// This field is not required, but it's highly recommended.
	// RECOMMENDED by the OpenRTB Native specification.
	Layout *NativeRequest_LayoutId `protobuf:"varint,2,opt,name=layout,enum=youdao.NativeRequest_LayoutId" json:"layout,omitempty"`
	// The Ad unit ID of the native ad unit. This corresponds to one of
	// IAB Core-6 native ad units.
	// RECOMMENDED by the OpenRTB Native specification.
	Adunit *NativeRequest_AdUnitId `protobuf:"varint,3,opt,name=adunit,enum=youdao.NativeRequest_AdUnitId" json:"adunit,omitempty"`
	// The number of identical placements in this Layout.
	Plcmtcnt *int32 `protobuf:"varint,4,opt,name=plcmtcnt,def=1" json:"plcmtcnt,omitempty"`
	// 0 for the first ad, 1 for the second ad, and so on.
	// This is not the sequence number of the content in the stream.
	Seq *int32 `protobuf:"varint,5,opt,name=seq,def=0" json:"seq,omitempty"`
	// Any bid must comply with the array of elements expressed by the Exchange.
	// REQUIRED by the OpenRTB Native specification: at least 1 element.
	Assets []*NativeRequest_Asset `protobuf:"bytes,6,rep,name=assets" json:"assets,omitempty"`
}

// Default values for NativeRequest fields.
const (
	Default_NativeRequest_Plcmtcnt = int32(1)
	Default_NativeRequest_Seq      = int32(0)
)

func (x *NativeRequest) Reset() {
	*x = NativeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest) ProtoMessage() {}

func (x *NativeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest.ProtoReflect.Descriptor instead.
func (*NativeRequest) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2}
}

func (x *NativeRequest) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *NativeRequest) GetLayout() NativeRequest_LayoutId {
	if x != nil && x.Layout != nil {
		return *x.Layout
	}
	return NativeRequest_CONTENT_WALL
}

func (x *NativeRequest) GetAdunit() NativeRequest_AdUnitId {
	if x != nil && x.Adunit != nil {
		return *x.Adunit
	}
	return NativeRequest_PAID_SEARCH_UNIT
}

func (x *NativeRequest) GetPlcmtcnt() int32 {
	if x != nil && x.Plcmtcnt != nil {
		return *x.Plcmtcnt
	}
	return Default_NativeRequest_Plcmtcnt
}

func (x *NativeRequest) GetSeq() int32 {
	if x != nil && x.Seq != nil {
		return *x.Seq
	}
	return Default_NativeRequest_Seq
}

func (x *NativeRequest) GetAssets() []*NativeRequest_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

// OpenRTB Native 5.2: The native response object is the top level JSON object
// which identifies an native response.
type NativeResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Version of the Native Markup version in use.
	Ver *string `protobuf:"bytes,1,opt,name=ver" json:"ver,omitempty"`
	// List of native ad's assets.
	Assets []*NativeResponse_Asset `protobuf:"bytes,2,rep,name=assets" json:"assets,omitempty"`
	// Destination Link.
	// REQUIRED by the OpenRTB Native specification.
	Link *NativeResponse_Link `protobuf:"bytes,3,req,name=link" json:"link,omitempty"`
	// Array of impression tracking URLs, expected to return a 1x1 image or
	// 204 response - typically only passed when using 3rd party trackers.
	Imptrackers []string `protobuf:"bytes,4,rep,name=imptrackers" json:"imptrackers,omitempty"`
	// Optional javascript impression tracker. Contains <script> tags to be
	// executed at impression time where it can be supported.
	Jstracker *string `protobuf:"bytes,5,opt,name=jstracker" json:"jstracker,omitempty"`
}

func (x *NativeResponse) Reset() {
	*x = NativeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse) ProtoMessage() {}

func (x *NativeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse.ProtoReflect.Descriptor instead.
func (*NativeResponse) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3}
}

func (x *NativeResponse) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *NativeResponse) GetAssets() []*NativeResponse_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *NativeResponse) GetLink() *NativeResponse_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

func (x *NativeResponse) GetImptrackers() []string {
	if x != nil {
		return x.Imptrackers
	}
	return nil
}

func (x *NativeResponse) GetJstracker() string {
	if x != nil && x.Jstracker != nil {
		return *x.Jstracker
	}
	return ""
}

// OpenRTB 3.2.2: This object describes an ad placement or impression
// being auctioned.  A single bid request can include multiple Imp objects,
// a use case for which might be an exchange that supports selling all
// ad positions on a given page.  Each Imp object has a required ID so that
// bids can reference them individually.
//
// The presence of Banner (Section 3.2.3), Video (Section 3.2.4),
// and/or Native (Section 3.2.5) objects subordinate to the Imp object
// indicates the type of impression being offered. The publisher can choose
// one such type which is the typical case or mix them at their discretion.
// Any given bid for the impression must conform to one of the offered types.
type BidRequest_Imp struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// A unique identifier for this impression within the context of the bid
	// request (typically, value starts with 1, and increments up to n
	// for n impressions).
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// A Banner object (Section 3.2.3); required if this impression is
	// offered as a banner ad opportunity.
	Banner *BidRequest_Imp_Banner `protobuf:"bytes,2,opt,name=banner" json:"banner,omitempty"`
	// A Video object (Section 3.2.4); required if this impression is
	// offered as a video ad opportunity.
	Video *BidRequest_Imp_Video `protobuf:"bytes,3,opt,name=video" json:"video,omitempty"`
	// Name of ad mediation partner, SDK technology, or player responsible
	// for rendering ad (typically video or mobile). Used by some ad servers
	// to customize ad code by partner. Recommended for video and/or apps.
	Displaymanager *string `protobuf:"bytes,4,opt,name=displaymanager" json:"displaymanager,omitempty"`
	// Version of ad mediation partner, SDK technology, or player responsible
	// for rendering ad (typically video or mobile). Used by some ad servers
	// to customize ad code by partner. Recommended for video and/or apps.
	Displaymanagerver *string `protobuf:"bytes,5,opt,name=displaymanagerver" json:"displaymanagerver,omitempty"`
	// 1 = the ad is interstitial or full screen, 0 = not interstitial.
	Instl *bool `protobuf:"varint,6,opt,name=instl" json:"instl,omitempty"`
	// Identifier for specific ad placement or ad tag that was used to
	// initiate the auction. This can be useful for debugging of any issues,
	// or for optimization by the buyer.
	Tagid *string `protobuf:"bytes,7,opt,name=tagid" json:"tagid,omitempty"`
	// Minimum bid for this impression expressed in CPM.
	Bidfloor *float64 `protobuf:"fixed64,8,opt,name=bidfloor,def=0" json:"bidfloor,omitempty"`
	// Currency specified using ISO-4217 alpha codes. This may be different
	// from bid currency returned by bidder if this is allowed by the exchange.
	Bidfloorcur *string `protobuf:"bytes,9,opt,name=bidfloorcur,def=USD" json:"bidfloorcur,omitempty"`
	// Flag to indicate if the impression requires secure HTTPS URL creative
	// assets and markup, where 0 = non-secure, 1 = secure.  If omitted,
	// the secure state is unknown, but non-secure HTTP support can be assumed.
	Secure *bool `protobuf:"varint,12,opt,name=secure" json:"secure,omitempty"`
	// Array of exchange-specific names of supported iframe busters.
	Iframebuster []string `protobuf:"bytes,10,rep,name=iframebuster" json:"iframebuster,omitempty"`
	// A Pmp object (Section 3.2.17) containing any private marketplace deals
	// in effect for this impression.
	Pmp *BidRequest_Imp_Pmp `protobuf:"bytes,11,opt,name=pmp" json:"pmp,omitempty"`
	// A Native object (Section 3.2.5); required if this impression is
	// offered as a native ad opportunity.
	Native *BidRequest_Imp_Native `protobuf:"bytes,13,opt,name=native" json:"native,omitempty"`
}

// Default values for BidRequest_Imp fields.
const (
	Default_BidRequest_Imp_Bidfloor    = float64(0)
	Default_BidRequest_Imp_Bidfloorcur = string("USD")
)

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetBanner() *BidRequest_Imp_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetDisplaymanager() string {
	if x != nil && x.Displaymanager != nil {
		return *x.Displaymanager
	}
	return ""
}

func (x *BidRequest_Imp) GetDisplaymanagerver() string {
	if x != nil && x.Displaymanagerver != nil {
		return *x.Displaymanagerver
	}
	return ""
}

func (x *BidRequest_Imp) GetInstl() bool {
	if x != nil && x.Instl != nil {
		return *x.Instl
	}
	return false
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil && x.Tagid != nil {
		return *x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetBidfloor() float64 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return Default_BidRequest_Imp_Bidfloor
}

func (x *BidRequest_Imp) GetBidfloorcur() string {
	if x != nil && x.Bidfloorcur != nil {
		return *x.Bidfloorcur
	}
	return Default_BidRequest_Imp_Bidfloorcur
}

func (x *BidRequest_Imp) GetSecure() bool {
	if x != nil && x.Secure != nil {
		return *x.Secure
	}
	return false
}

func (x *BidRequest_Imp) GetIframebuster() []string {
	if x != nil {
		return x.Iframebuster
	}
	return nil
}

func (x *BidRequest_Imp) GetPmp() *BidRequest_Imp_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_Imp) GetNative() *BidRequest_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

// OpenRTB 3.2.6: This object should be included if the ad supported content
// is a website as opposed to a non-browser application. A bid request must
// not contain both a Site and an App object. At a minimum, it is useful to
// provide a site ID or page URL, but this is not strictly required.
type BidRequest_Site struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Site ID on the exchange.
	// RECOMMENDED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Site name (may be masked at publisher's request).
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Domain of the site, used for advertiser side blocking.
	// For example, "foo.com".
	Domain *string `protobuf:"bytes,3,opt,name=domain" json:"domain,omitempty"`
	// Array of IAB content categories of the site.
	// See enum ContentCategory.
	Cat []string `protobuf:"bytes,4,rep,name=cat" json:"cat,omitempty"`
	// Array of IAB content categories that describe the current section
	// of the site. See enum ContentCategory.
	Sectioncat []string `protobuf:"bytes,5,rep,name=sectioncat" json:"sectioncat,omitempty"`
	// Array of IAB content categories that describe the current page or view
	// of the site. See enum ContentCategory.
	Pagecat []string `protobuf:"bytes,6,rep,name=pagecat" json:"pagecat,omitempty"`
	// URL of the page where the impression will be shown.
	Page *string `protobuf:"bytes,7,opt,name=page" json:"page,omitempty"`
	// Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
	Privacypolicy *bool `protobuf:"varint,8,opt,name=privacypolicy" json:"privacypolicy,omitempty"`
	// Referrer URL that caused navigation to the current page.
	Ref *string `protobuf:"bytes,9,opt,name=ref" json:"ref,omitempty"`
	// Search string that caused navigation to the current page.
	Search *string `protobuf:"bytes,10,opt,name=search" json:"search,omitempty"`
	// Details about the Publisher (Section 3.2.8) of the site.
	Publisher *BidRequest_Publisher `protobuf:"bytes,11,opt,name=publisher" json:"publisher,omitempty"`
	// Details about the Content (Section 3.2.9) within the site.
	Content *BidRequest_Content `protobuf:"bytes,12,opt,name=content" json:"content,omitempty"`
	// Comma separated list of keywords about this site.
	// Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
	// but this was fixed in 2.3+ where it's definitely a single string with CSV
	// content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
	// the alternate representation may require custom handling of the JSON.
	Keywords *string `protobuf:"bytes,13,opt,name=keywords" json:"keywords,omitempty"`
	// Mobile-optimized signal, where 0 = no, 1 = yes.
	Mobile *bool `protobuf:"varint,15,opt,name=mobile" json:"mobile,omitempty"`
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Site) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Site) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Site) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *BidRequest_Site) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Site) GetSectioncat() []string {
	if x != nil {
		return x.Sectioncat
	}
	return nil
}

func (x *BidRequest_Site) GetPagecat() []string {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

func (x *BidRequest_Site) GetPage() string {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return ""
}

func (x *BidRequest_Site) GetPrivacypolicy() bool {
	if x != nil && x.Privacypolicy != nil {
		return *x.Privacypolicy
	}
	return false
}

func (x *BidRequest_Site) GetRef() string {
	if x != nil && x.Ref != nil {
		return *x.Ref
	}
	return ""
}

func (x *BidRequest_Site) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

func (x *BidRequest_Site) GetPublisher() *BidRequest_Publisher {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidRequest_Site) GetContent() *BidRequest_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *BidRequest_Site) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_Site) GetMobile() bool {
	if x != nil && x.Mobile != nil {
		return *x.Mobile
	}
	return false
}

// OpenRTB 3.2.7: This object should be included if the ad supported content
// is a non-browser application (typically in mobile) as opposed to a website.
// A bid request must not contain both an App and a Site object.
// At a minimum, it is useful to provide an App ID or bundle,
// but this is not strictly required.
type BidRequest_App struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Application ID on the exchange.
	// RECOMMENDED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Application name (may be aliased at publisher's request).
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Domain of the application. For example, "mygame.foo.com".
	Domain *string `protobuf:"bytes,3,opt,name=domain" json:"domain,omitempty"`
	// Array of IAB content categories of the app.
	// See enum ContentCategory.
	Cat []string `protobuf:"bytes,4,rep,name=cat" json:"cat,omitempty"`
	// Array of IAB content categories that describe the current section
	// of the app. See enum ContentCategory.
	Sectioncat []string `protobuf:"bytes,5,rep,name=sectioncat" json:"sectioncat,omitempty"`
	// Array of IAB content categories that describe the current page or view
	// of the app. See enum ContentCategory.
	Pagecat []string `protobuf:"bytes,6,rep,name=pagecat" json:"pagecat,omitempty"`
	// Application version.
	Ver *string `protobuf:"bytes,7,opt,name=ver" json:"ver,omitempty"`
	// Application bundle or package name (e.g., com.foo.mygame).
	// This is intended to be a unique ID across multiple exchanges.
	Bundle *string `protobuf:"bytes,8,opt,name=bundle" json:"bundle,omitempty"`
	// Indicates if the app has a privacy policy, where 0 = no, 1 = yes.
	Privacypolicy *bool `protobuf:"varint,9,opt,name=privacypolicy" json:"privacypolicy,omitempty"`
	// 0 = app is free, 1 = the app is a paid version.
	Paid *bool `protobuf:"varint,10,opt,name=paid" json:"paid,omitempty"`
	// Details about the Publisher (Section 3.2.8) of the app.
	Publisher *BidRequest_Publisher `protobuf:"bytes,11,opt,name=publisher" json:"publisher,omitempty"`
	// Details about the Content (Section 3.2.9) within the app.
	Content *BidRequest_Content `protobuf:"bytes,12,opt,name=content" json:"content,omitempty"`
	// Comma separated list of keywords about the app.
	Keywords *string `protobuf:"bytes,13,opt,name=keywords" json:"keywords,omitempty"`
	// App store URL for an installed app; for QAG 1.5 compliance.
	Storeurl *string `protobuf:"bytes,16,opt,name=storeurl" json:"storeurl,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_App) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *BidRequest_App) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_App) GetSectioncat() []string {
	if x != nil {
		return x.Sectioncat
	}
	return nil
}

func (x *BidRequest_App) GetPagecat() []string {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

func (x *BidRequest_App) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetPrivacypolicy() bool {
	if x != nil && x.Privacypolicy != nil {
		return *x.Privacypolicy
	}
	return false
}

func (x *BidRequest_App) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return false
}

func (x *BidRequest_App) GetPublisher() *BidRequest_Publisher {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidRequest_App) GetContent() *BidRequest_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *BidRequest_App) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_App) GetStoreurl() string {
	if x != nil && x.Storeurl != nil {
		return *x.Storeurl
	}
	return ""
}

// OpenRTB 3.2.8: This object describes the publisher of the media in which
// the ad will be displayed. The publisher is typically the seller
// in an OpenRTB transaction.
type BidRequest_Publisher struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Exchange-specific publisher ID.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Publisher name (may be aliased at publisher's request).
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Array of IAB content categories that describe the publisher.
	// See enum ContentCategory.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Highest level domain of the publisher (e.g., "publisher.com").
	Domain *string `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`
}

func (x *BidRequest_Publisher) Reset() {
	*x = BidRequest_Publisher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Publisher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Publisher) ProtoMessage() {}

func (x *BidRequest_Publisher) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Publisher.ProtoReflect.Descriptor instead.
func (*BidRequest_Publisher) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Publisher) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Publisher) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Publisher) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Publisher) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

// OpenRTB 3.2.9: This object describes the content in which the impression
// will appear, which may be syndicated or non-syndicated content.
// This object may be useful when syndicated content contains impressions and
// does not necessarily match the publisher's general content.
// The exchange might or might not have knowledge of the page where the
// content is running, as a result of the syndication method.
// For example might be a video impression embedded in an iframe on an
// unknown web property or device.
type BidRequest_Content struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// ID uniquely identifying the content.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Content episode number (typically applies to video content).
	Episode *int32 `protobuf:"varint,2,opt,name=episode" json:"episode,omitempty"`
	// Content title.
	// Video Examples: "Search Committee" (television), "A New Hope" (movie),
	// or "Endgame" (made for web).
	// Non-Video Example: "Why an Antarctic Glacier Is Melting So Quickly"
	// (Time magazine article).
	Title *string `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`
	// Content series.
	// Video Examples: "The Office" (television), "Star Wars" (movie),
	// or "Arby 'N' The Chief" (made for web).
	// Non-Video Example: "Ecocentric" (Time Magazine blog).
	Series *string `protobuf:"bytes,4,opt,name=series" json:"series,omitempty"`
	// Content season; typically for video content (e.g., "Season 3").
	Season *string `protobuf:"bytes,5,opt,name=season" json:"season,omitempty"`
	// URL of the content, for buy-side contextualization or review.
	Url *string `protobuf:"bytes,6,opt,name=url" json:"url,omitempty"`
	// Array of IAB content categories that describe the content.
	// See enum ContentCategory.
	Cat []string `protobuf:"bytes,7,rep,name=cat" json:"cat,omitempty"`
	// Video quality per IAB's classification.
	Videoquality *BidRequest_Content_VideoQuality `protobuf:"varint,8,opt,name=videoquality,enum=youdao.BidRequest_Content_VideoQuality" json:"videoquality,omitempty"`
	// Comma separated list of keywords describing the content.
	// Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
	// but this was fixed in 2.3+ where it's definitely a single string with CSV
	// content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
	// the alternate representation may require custom handling of the JSON.
	Keywords *string `protobuf:"bytes,9,opt,name=keywords" json:"keywords,omitempty"`
	// Content rating (e.g., MPAA).
	Contentrating *string `protobuf:"bytes,10,opt,name=contentrating" json:"contentrating,omitempty"`
	// User rating of the content (e.g., number of stars, likes, etc.).
	Userrating *string `protobuf:"bytes,11,opt,name=userrating" json:"userrating,omitempty"`
	// Type of content (game, video, text, etc.).
	Context *BidRequest_Content_ContentContext `protobuf:"varint,20,opt,name=context,enum=youdao.BidRequest_Content_ContentContext" json:"context,omitempty"`
	// OpenRTB <= 2.2 compatibility; use context for 2.3+.
	Context_22 *string `protobuf:"bytes,12,opt,name=context_22,json=context22" json:"context_22,omitempty"`
	// 0 = not live, 1 = content is live (e.g., stream, live blog).
	Livestream *bool `protobuf:"varint,13,opt,name=livestream" json:"livestream,omitempty"`
	// 0 = indirect, 1 = direct.
	Sourcerelationship *bool `protobuf:"varint,14,opt,name=sourcerelationship" json:"sourcerelationship,omitempty"`
	// Details about the content Producer (Section 3.2.10).
	Producer *BidRequest_Producer `protobuf:"bytes,15,opt,name=producer" json:"producer,omitempty"`
	// Length of content in seconds; appropriate for video or audio.
	Len *int32 `protobuf:"varint,16,opt,name=len" json:"len,omitempty"`
	// Media rating per QAG guidelines.
	Qagmediarating *BidRequest_Content_QAGMediaRating `protobuf:"varint,17,opt,name=qagmediarating,enum=youdao.BidRequest_Content_QAGMediaRating" json:"qagmediarating,omitempty"`
	// Indicator of whether or not the content is embeddable (e.g., an
	// embeddable video player), where 0 = no, 1 = yes.
	Embeddable *bool `protobuf:"varint,18,opt,name=embeddable" json:"embeddable,omitempty"`
	// Content language using ISO-639-1-alpha-2.
	Language *string `protobuf:"bytes,19,opt,name=language" json:"language,omitempty"`
}

func (x *BidRequest_Content) Reset() {
	*x = BidRequest_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Content) ProtoMessage() {}

func (x *BidRequest_Content) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Content.ProtoReflect.Descriptor instead.
func (*BidRequest_Content) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Content) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Content) GetEpisode() int32 {
	if x != nil && x.Episode != nil {
		return *x.Episode
	}
	return 0
}

func (x *BidRequest_Content) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidRequest_Content) GetSeries() string {
	if x != nil && x.Series != nil {
		return *x.Series
	}
	return ""
}

func (x *BidRequest_Content) GetSeason() string {
	if x != nil && x.Season != nil {
		return *x.Season
	}
	return ""
}

func (x *BidRequest_Content) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidRequest_Content) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Content) GetVideoquality() BidRequest_Content_VideoQuality {
	if x != nil && x.Videoquality != nil {
		return *x.Videoquality
	}
	return BidRequest_Content_QUALITY_UNKNOWN
}

func (x *BidRequest_Content) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_Content) GetContentrating() string {
	if x != nil && x.Contentrating != nil {
		return *x.Contentrating
	}
	return ""
}

func (x *BidRequest_Content) GetUserrating() string {
	if x != nil && x.Userrating != nil {
		return *x.Userrating
	}
	return ""
}

func (x *BidRequest_Content) GetContext() BidRequest_Content_ContentContext {
	if x != nil && x.Context != nil {
		return *x.Context
	}
	return BidRequest_Content_VIDEO
}

func (x *BidRequest_Content) GetContext_22() string {
	if x != nil && x.Context_22 != nil {
		return *x.Context_22
	}
	return ""
}

func (x *BidRequest_Content) GetLivestream() bool {
	if x != nil && x.Livestream != nil {
		return *x.Livestream
	}
	return false
}

func (x *BidRequest_Content) GetSourcerelationship() bool {
	if x != nil && x.Sourcerelationship != nil {
		return *x.Sourcerelationship
	}
	return false
}

func (x *BidRequest_Content) GetProducer() *BidRequest_Producer {
	if x != nil {
		return x.Producer
	}
	return nil
}

func (x *BidRequest_Content) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *BidRequest_Content) GetQagmediarating() BidRequest_Content_QAGMediaRating {
	if x != nil && x.Qagmediarating != nil {
		return *x.Qagmediarating
	}
	return BidRequest_Content_ALL_AUDIENCES
}

func (x *BidRequest_Content) GetEmbeddable() bool {
	if x != nil && x.Embeddable != nil {
		return *x.Embeddable
	}
	return false
}

func (x *BidRequest_Content) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

// OpenRTB 3.2.10: This object defines the producer of the content in which
// the ad will be shown. This is particularly useful when the content is
// syndicated and may be distributed through different publishers and thus
// when the producer and publisher are not necessarily the same entity.
type BidRequest_Producer struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Content producer or originator ID. Useful if content is syndicated,
	// and may be posted on a site using embed tags.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Content producer or originator name (e.g., "Warner Bros").
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Array of IAB content categories that describe the content producer.
	// See enum ContentCategory.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Highest level domain of the content producer (e.g., "producer.com").
	Domain *string `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`
}

func (x *BidRequest_Producer) Reset() {
	*x = BidRequest_Producer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Producer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Producer) ProtoMessage() {}

func (x *BidRequest_Producer) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Producer.ProtoReflect.Descriptor instead.
func (*BidRequest_Producer) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_Producer) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Producer) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Producer) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Producer) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

// OpenRTB 3.2.11: This object provides information pertaining to the device
// through which the user is interacting. Device information includes its
// hardware, platform, location, and carrier data. The device can refer to a
// mobile handset, a desktop computer, set top box, or other digital device.
type BidRequest_Device struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Standard "Do Not Track" flag as set in the header by the browser,
	// where 0 = tracking is unrestricted, 1 = do not track.
	// This field is not required, but it's highly recommended.
	// RECOMMENDED by the OpenRTB specification.
	Dnt *bool `protobuf:"varint,1,opt,name=dnt" json:"dnt,omitempty"`
	// Browser user agent string.
	// This field is not required, but it's highly recommended.
	// RECOMMENDED by the OpenRTB specification.
	Ua *string `protobuf:"bytes,2,opt,name=ua" json:"ua,omitempty"`
	// IPv4 address closest to device.
	// This field is not required, but it's highly recommended.
	// RECOMMENDED by the OpenRTB specification.
	Ip *string `protobuf:"bytes,3,opt,name=ip" json:"ip,omitempty"`
	// Location of the device assumed to be the user's current location defined
	// by a Geo object (Section 3.2.12).
	// This field is not required, but it's highly recommended.
	// RECOMMENDED by the OpenRTB specification.
	Geo *BidRequest_Geo `protobuf:"bytes,4,opt,name=geo" json:"geo,omitempty"`
	// Hardware device ID (e.g., IMEI); hashed via SHA1.
	Didsha1 *string `protobuf:"bytes,5,opt,name=didsha1" json:"didsha1,omitempty"`
	// Hardware device ID (e.g., IMEI); hashed via MD5.
	Didmd5 *string `protobuf:"bytes,6,opt,name=didmd5" json:"didmd5,omitempty"`
	// Platform device ID (e.g., Android ID); hashed via SHA1.
	Dpidsha1 *string `protobuf:"bytes,7,opt,name=dpidsha1" json:"dpidsha1,omitempty"`
	// Platform device ID (e.g., Android ID); hashed via MD5.
	Dpidmd5 *string `protobuf:"bytes,8,opt,name=dpidmd5" json:"dpidmd5,omitempty"`
	// IPv6 address closest to device.
	Ipv6 *string `protobuf:"bytes,9,opt,name=ipv6" json:"ipv6,omitempty"`
	// Carrier or ISP, e.g. "VERIZON", specified using Mobile Network Code (MNC).
	// "WIFI" is often used in mobile to indicate high bandwidth
	// (e.g., video friendly vs. cellular).
	Carrier *string `protobuf:"bytes,10,opt,name=carrier" json:"carrier,omitempty"`
	// Browser language using ISO-639-1-alpha-2.
	Language *string `protobuf:"bytes,11,opt,name=language" json:"language,omitempty"`
	// Device make (e.g., "Apple").
	Make *string `protobuf:"bytes,12,opt,name=make" json:"make,omitempty"`
	// Device model (e.g., "iPhone").
	Model *string `protobuf:"bytes,13,opt,name=model" json:"model,omitempty"`
	// Device operating system (e.g., "iOS").
	Os *string `protobuf:"bytes,14,opt,name=os" json:"os,omitempty"`
	// Device operating system version (e.g., "3.1.2").
	Osv *string `protobuf:"bytes,15,opt,name=osv" json:"osv,omitempty"`
	// Hardware version of the device (e.g., "5S" for iPhone 5S).
	Hwv *string `protobuf:"bytes,24,opt,name=hwv" json:"hwv,omitempty"`
	// Physical width of the screen in pixels.
	W *int32 `protobuf:"varint,25,opt,name=w" json:"w,omitempty"`
	// Physical height of the screen in pixels.
	H *int32 `protobuf:"varint,26,opt,name=h" json:"h,omitempty"`
	// Screen size as pixels per linear inch.
	Ppi *int32 `protobuf:"varint,27,opt,name=ppi" json:"ppi,omitempty"`
	// The ratio of physical pixels to device independent pixels.
	Pxratio *float64 `protobuf:"fixed64,28,opt,name=pxratio" json:"pxratio,omitempty"`
	// Support for JavaScript, where 0 = no, 1 = yes.
	Js *bool `protobuf:"varint,16,opt,name=js" json:"js,omitempty"`
	// Network connection type.
	Connectiontype *BidRequest_Device_ConnectionType `protobuf:"varint,17,opt,name=connectiontype,enum=youdao.BidRequest_Device_ConnectionType" json:"connectiontype,omitempty"`
	// The general type of device.
	Devicetype *BidRequest_Device_DeviceType `protobuf:"varint,18,opt,name=devicetype,enum=youdao.BidRequest_Device_DeviceType" json:"devicetype,omitempty"`
	// Version of Flash supported by the browser.
	Flashver *string `protobuf:"bytes,19,opt,name=flashver" json:"flashver,omitempty"`
	// ID sanctioned for advertiser use in the clear (i.e., not hashed).
	Ifa *string `protobuf:"bytes,20,opt,name=ifa" json:"ifa,omitempty"`
	// MAC address of the device; hashed via SHA1.
	Macsha1 *string `protobuf:"bytes,21,opt,name=macsha1" json:"macsha1,omitempty"`
	// MAC address of the device; hashed via MD5.
	Macmd5 *string `protobuf:"bytes,22,opt,name=macmd5" json:"macmd5,omitempty"`
	// "Limit Ad Tracking" signal commercially endorsed (e.g., iOS, Android),
	// where 0 = tracking is unrestricted, 1 = tracking must be limited per
	// commercial guidelines.
	// RECOMMENDED by the OpenRTB specification.
	Lmt *bool `protobuf:"varint,23,opt,name=lmt" json:"lmt,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_Device) GetDnt() bool {
	if x != nil && x.Dnt != nil {
		return *x.Dnt
	}
	return false
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetDidsha1() string {
	if x != nil && x.Didsha1 != nil {
		return *x.Didsha1
	}
	return ""
}

func (x *BidRequest_Device) GetDidmd5() string {
	if x != nil && x.Didmd5 != nil {
		return *x.Didmd5
	}
	return ""
}

func (x *BidRequest_Device) GetDpidsha1() string {
	if x != nil && x.Dpidsha1 != nil {
		return *x.Dpidsha1
	}
	return ""
}

func (x *BidRequest_Device) GetDpidmd5() string {
	if x != nil && x.Dpidmd5 != nil {
		return *x.Dpidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() string {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return ""
}

func (x *BidRequest_Device) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil && x.Hwv != nil {
		return *x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil && x.Ppi != nil {
		return *x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetPxratio() float64 {
	if x != nil && x.Pxratio != nil {
		return *x.Pxratio
	}
	return 0
}

func (x *BidRequest_Device) GetJs() bool {
	if x != nil && x.Js != nil {
		return *x.Js
	}
	return false
}

func (x *BidRequest_Device) GetConnectiontype() BidRequest_Device_ConnectionType {
	if x != nil && x.Connectiontype != nil {
		return *x.Connectiontype
	}
	return BidRequest_Device_CONNECTION_UNKNOWN
}

func (x *BidRequest_Device) GetDevicetype() BidRequest_Device_DeviceType {
	if x != nil && x.Devicetype != nil {
		return *x.Devicetype
	}
	return BidRequest_Device_MOBILE
}

func (x *BidRequest_Device) GetFlashver() string {
	if x != nil && x.Flashver != nil {
		return *x.Flashver
	}
	return ""
}

func (x *BidRequest_Device) GetIfa() string {
	if x != nil && x.Ifa != nil {
		return *x.Ifa
	}
	return ""
}

func (x *BidRequest_Device) GetMacsha1() string {
	if x != nil && x.Macsha1 != nil {
		return *x.Macsha1
	}
	return ""
}

func (x *BidRequest_Device) GetMacmd5() string {
	if x != nil && x.Macmd5 != nil {
		return *x.Macmd5
	}
	return ""
}

func (x *BidRequest_Device) GetLmt() bool {
	if x != nil && x.Lmt != nil {
		return *x.Lmt
	}
	return false
}

// OpenRTB 3.2.12: This object encapsulates various methods for specifying a
// geographic location. When subordinate to a Device object, it indicates the
// location of the device which can also be interpreted as the user's current
// location. When subordinate to a User object, it indicates the location of
// the user's home base (i.e., not necessarily their current location).
//
// The lat/lon attributes should only be passed if they conform to the
// accuracy depicted in the type attribute. For example, the centroid of a
// geographic region such as postal code should not be passed.
type BidRequest_Geo struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Latitude from -90.0 to +90.0, where negative is south.
	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"`
	// Longitude from -180.0 to +180.0, where negative is west.
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"`
	// Country using ISO-3166-1 Alpha-3.
	Country *string `protobuf:"bytes,3,opt,name=country" json:"country,omitempty"`
	// Region code using ISO-3166-2; 2-letter state code if USA.
	Region *string `protobuf:"bytes,4,opt,name=region" json:"region,omitempty"`
	// Region of a country using FIPS 10-4 notation. While OpenRTB supports
	// this attribute, it has been withdrawn by NIST in 2008.
	Regionfips104 *string `protobuf:"bytes,5,opt,name=regionfips104" json:"regionfips104,omitempty"`
	// Google metro code; similar to but not exactly Nielsen DMAs.
	// See Appendix A for a link to the codes.
	// (http://code.google.com/apis/adwords/docs/appendix/metrocodes.html).
	Metro *string `protobuf:"bytes,6,opt,name=metro" json:"metro,omitempty"`
	// City using United Nations Code for Trade & Transport Locations.
	// See Appendix A for a link to the codes.
	// (http://www.unece.org/cefact/locode/service/location.htm).
	City *string `protobuf:"bytes,7,opt,name=city" json:"city,omitempty"`
	// Zip/postal code.
	Zip *string `protobuf:"bytes,8,opt,name=zip" json:"zip,omitempty"`
	// Source of location data; recommended when passing lat/lon.
	Type *BidRequest_Geo_LocationType `protobuf:"varint,9,opt,name=type,enum=youdao.BidRequest_Geo_LocationType" json:"type,omitempty"`
	// Local time as the number +/- of minutes from UTC.
	Utcoffset *int32 `protobuf:"varint,10,opt,name=utcoffset" json:"utcoffset,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 7}
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *BidRequest_Geo) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *BidRequest_Geo) GetRegionfips104() string {
	if x != nil && x.Regionfips104 != nil {
		return *x.Regionfips104
	}
	return ""
}

func (x *BidRequest_Geo) GetMetro() string {
	if x != nil && x.Metro != nil {
		return *x.Metro
	}
	return ""
}

func (x *BidRequest_Geo) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *BidRequest_Geo) GetZip() string {
	if x != nil && x.Zip != nil {
		return *x.Zip
	}
	return ""
}

func (x *BidRequest_Geo) GetType() BidRequest_Geo_LocationType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Geo_GPS_LOCATION
}

func (x *BidRequest_Geo) GetUtcoffset() int32 {
	if x != nil && x.Utcoffset != nil {
		return *x.Utcoffset
	}
	return 0
}

// OpenRTB 3.2.13: This object contains information known or derived about
// the human user of the device (i.e., the audience for advertising).
// The user id is an exchange artifact and may be subject to rotation or other
// privacy policies. However, this user ID must be stable long enough to serve
// reasonably as the basis for frequency capping and retargeting.
type BidRequest_User struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Exchange-specific ID for the user. At least one of id or buyerid
	// is recommended.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Buyer-specific ID for the user as mapped by the exchange for the buyer.
	// At least one of buyerid or id is recommended.
	Buyeruid *string `protobuf:"bytes,2,opt,name=buyeruid" json:"buyeruid,omitempty"`
	// Year of birth as a 4-digit integer.
	Yob *int32 `protobuf:"varint,3,opt,name=yob" json:"yob,omitempty"`
	// Gender as "M" male, "F" female, "O" Other. (Null indicates unknown)
	Gender *string `protobuf:"bytes,4,opt,name=gender" json:"gender,omitempty"`
	// Comma separated list of keywords, interests, or intent.
	// Note: OpenRTB 2.2 allowed an array-of-strings as alternate implementation
	// but this was fixed in 2.3+ where it's definitely a single string with CSV
	// content again. Compatibility with some OpenRTB 2.2 exchanges that adopted
	// the alternate representation may require custom handling of the JSON.
	Keywords *string `protobuf:"bytes,5,opt,name=keywords" json:"keywords,omitempty"`
	// Optional feature to pass bidder data set in the exchange's cookie.
	// The string must be in base85 cookie safe characters and be in any format.
	// Proper JSON encoding must be used to include "escaped" quotation marks.
	Customdata *string `protobuf:"bytes,6,opt,name=customdata" json:"customdata,omitempty"`
	// Location of the user's home base defined by a Geo object
	// (Section 3.2.12). This is not necessarily their current location.
	Geo *BidRequest_Geo `protobuf:"bytes,7,opt,name=geo" json:"geo,omitempty"`
	// Additional user data. Each Data object (Section 3.2.14) represents a
	// different data source.
	Data []*BidRequest_Data `protobuf:"bytes,8,rep,name=data" json:"data,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 8}
}

func (x *BidRequest_User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_User) GetBuyeruid() string {
	if x != nil && x.Buyeruid != nil {
		return *x.Buyeruid
	}
	return ""
}

func (x *BidRequest_User) GetYob() int32 {
	if x != nil && x.Yob != nil {
		return *x.Yob
	}
	return 0
}

func (x *BidRequest_User) GetGender() string {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return ""
}

func (x *BidRequest_User) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_User) GetCustomdata() string {
	if x != nil && x.Customdata != nil {
		return *x.Customdata
	}
	return ""
}

func (x *BidRequest_User) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_User) GetData() []*BidRequest_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// OpenRTB 3.2.14: The data and segment objects together allow additional data
// about the user to be specified. This data may be from multiple sources
// whether from the exchange itself or third party providers as specified by
// the id field. A bid request can mix data objects from multiple providers.
// The specific data providers in use should be published by the exchange
// a priori to its bidders.
type BidRequest_Data struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Exchange-specific ID for the data provider.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Exchange-specific name for the data provider.
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Array of Segment (Section 3.2.15) objects that contain the actual
	// data values.
	Segment []*BidRequest_Data_Segment `protobuf:"bytes,3,rep,name=segment" json:"segment,omitempty"`
}

func (x *BidRequest_Data) Reset() {
	*x = BidRequest_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Data) ProtoMessage() {}

func (x *BidRequest_Data) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Data.ProtoReflect.Descriptor instead.
func (*BidRequest_Data) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 9}
}

func (x *BidRequest_Data) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Data) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Data) GetSegment() []*BidRequest_Data_Segment {
	if x != nil {
		return x.Segment
	}
	return nil
}

// OpenRTB 3.2.16: This object contains any legal, governmental, or industry
// regulations that apply to the request. The coppa flag signals whether
// or not the request falls under the United States Federal Trade Commission's
// regulations for the United States Children's Online Privacy Protection Act
// ("COPPA"). Refer to Section 7.1 for more information.
type BidRequest_Regs struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Flag indicating if this request is subject to the COPPA regulations
	// established by the USA FTC, where 0 = no, 1 = yes.
	Coppa *bool `protobuf:"varint,1,opt,name=coppa" json:"coppa,omitempty"`
}

func (x *BidRequest_Regs) Reset() {
	*x = BidRequest_Regs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Regs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Regs) ProtoMessage() {}

func (x *BidRequest_Regs) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Regs.ProtoReflect.Descriptor instead.
func (*BidRequest_Regs) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 10}
}

func (x *BidRequest_Regs) GetCoppa() bool {
	if x != nil && x.Coppa != nil {
		return *x.Coppa
	}
	return false
}

// OpenRTB 3.2.3: This object represents the most general type of
// impression.  Although the term "banner" may have very specific meaning
// in other contexts, here it can be many things including a simple static
// image, an expandable ad unit, or even in-banner video (refer to the Video
// object in Section 3.2.4 for the more generalized and full featured video
// ad units). An array of Banner objects can also appear within the Video
// to describe optional companion ads defined in the VAST specification.
//
// The presence of a Banner as a subordinate of the Imp object indicates
// that this impression is offered as a banner type impression.
// At the publisher's discretion, that same impression may also be offered
// as video and/or native by also including as Imp subordinates the Video
// and/or Native objects, respectively. However, any given bid for the
// impression must conform to one of the offered types.
type BidRequest_Imp_Banner struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Width of the impression in pixels.
	// If neither wmin nor wmax are specified, this value is an exact
	// width requirement. Otherwise it is a preferred width.
	W *int32 `protobuf:"varint,1,opt,name=w" json:"w,omitempty"`
	// Height of the impression in pixels.
	// If neither hmin nor hmax are specified, this value is an exact
	// height requirement. Otherwise it is a preferred height.
	H *int32 `protobuf:"varint,2,opt,name=h" json:"h,omitempty"`
	// Maximum width of the impression in pixels.
	// If included along with a w value then w should be interpreted
	// as a recommended or preferred width.
	Wmax *int32 `protobuf:"varint,11,opt,name=wmax" json:"wmax,omitempty"`
	// Maximum height of the impression in pixels.
	// If included along with an h value then h should be interpreted
	// as a recommended or preferred height.
	Hmax *int32 `protobuf:"varint,12,opt,name=hmax" json:"hmax,omitempty"`
	// Minimum width of the impression in pixels.
	// If included along with a w value then w should be interpreted
	// as a recommended or preferred width.
	Wmin *int32 `protobuf:"varint,13,opt,name=wmin" json:"wmin,omitempty"`
	// Minimum height of the impression in pixels.
	// If included along with an h value then h should be interpreted
	// as a recommended or preferred height.
	Hmin *int32 `protobuf:"varint,14,opt,name=hmin" json:"hmin,omitempty"`
	// Unique identifier for this banner object. Recommended when Banner
	// objects are used with a Video object (Section 3.2.4) to represent
	// an array of companion ads. Values usually start at 1 and increase
	// with each object; should be unique within an impression.
	Id *string `protobuf:"bytes,3,opt,name=id" json:"id,omitempty"`
	// Ad position on screen.
	Pos *BidRequest_Imp_AdPosition `protobuf:"varint,4,opt,name=pos,enum=youdao.BidRequest_Imp_AdPosition" json:"pos,omitempty"`
	// Blocked banner ad types.
	Btype []BidRequest_Imp_Banner_BannerAdType `protobuf:"varint,5,rep,packed,name=btype,enum=youdao.BidRequest_Imp_Banner_BannerAdType" json:"btype,omitempty"`
	// Blocked creative attributes.
	Battr []CreativeAttribute `protobuf:"varint,6,rep,packed,name=battr,enum=youdao.CreativeAttribute" json:"battr,omitempty"`
	// Whitelist of content MIME types supported. Popular MIME types include,
	// but are not limited to "image/jpg", "image/gif" and
	// "application/x-shockwave-flash".
	Mimes []string `protobuf:"bytes,7,rep,name=mimes" json:"mimes,omitempty"`
	// Specify if the banner is delivered in the top frame (true)
	// or in an iframe (false).
	Topframe *bool `protobuf:"varint,8,opt,name=topframe" json:"topframe,omitempty"`
	// Directions in which the banner may expand.
	Expdir []BidRequest_Imp_Banner_ExpandableDirection `protobuf:"varint,9,rep,packed,name=expdir,enum=youdao.BidRequest_Imp_Banner_ExpandableDirection" json:"expdir,omitempty"`
	// List of supported API frameworks for this impression.
	// If an API is not explicitly listed, it is assumed not to be supported.
	Api []BidRequest_Imp_APIFramework `protobuf:"varint,10,rep,packed,name=api,enum=youdao.BidRequest_Imp_APIFramework" json:"api,omitempty"`
}

func (x *BidRequest_Imp_Banner) Reset() {
	*x = BidRequest_Imp_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Banner) ProtoMessage() {}

func (x *BidRequest_Imp_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Banner.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Banner) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Banner) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetWmax() int32 {
	if x != nil && x.Wmax != nil {
		return *x.Wmax
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetHmax() int32 {
	if x != nil && x.Hmax != nil {
		return *x.Hmax
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetWmin() int32 {
	if x != nil && x.Wmin != nil {
		return *x.Wmin
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetHmin() int32 {
	if x != nil && x.Hmin != nil {
		return *x.Hmin
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Banner) GetPos() BidRequest_Imp_AdPosition {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return BidRequest_Imp_UNKNOWN
}

func (x *BidRequest_Imp_Banner) GetBtype() []BidRequest_Imp_Banner_BannerAdType {
	if x != nil {
		return x.Btype
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetBattr() []CreativeAttribute {
	if x != nil {
		return x.Battr
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetTopframe() bool {
	if x != nil && x.Topframe != nil {
		return *x.Topframe
	}
	return false
}

func (x *BidRequest_Imp_Banner) GetExpdir() []BidRequest_Imp_Banner_ExpandableDirection {
	if x != nil {
		return x.Expdir
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetApi() []BidRequest_Imp_APIFramework {
	if x != nil {
		return x.Api
	}
	return nil
}

// OpenRTB 3.2.4: This object represents an in-stream video impression.
// Many of the fields are non-essential for minimally viable transactions,
// but are included to offer fine control when needed. Video in OpenRTB
// generally assumes compliance with the VAST standard. As such, the notion
// of companion ads is supported by optionally including an array of Banner
// objects (refer to the Banner object in Section 3.2.3) that define these
// companion ads.
//
// The presence of a Video as a subordinate of the Imp object indicates
// that this impression is offered as a video type impression. At the
// publisher's discretion, that same impression may also be offered as
// banner and/or native by also including as Imp subordinates the Banner
// and/or Native objects, respectively. However, any given bid for the
// impression must conform to one of the offered types.
type BidRequest_Imp_Video struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Whitelist of content MIME types supported. Popular MIME types include,
	// but are not limited to "image/jpg", "image/gif" and
	// "application/x-shockwave-flash".
	// REQUIRED by the OpenRTB specification: at least 1 element.
	Mimes []string `protobuf:"bytes,1,rep,name=mimes" json:"mimes,omitempty"`
	// Indicates if the impression must be linear, nonlinear, etc.
	// If none specified, assume all are allowed.
	Linearity *BidRequest_Imp_Video_VideoLinearity `protobuf:"varint,2,opt,name=linearity,enum=youdao.BidRequest_Imp_Video_VideoLinearity" json:"linearity,omitempty"`
	// Minimum video ad duration in seconds.
	// RECOMMENDED by the OpenRTB specification.
	Minduration *int32 `protobuf:"varint,3,opt,name=minduration" json:"minduration,omitempty"`
	// Maximum video ad duration in seconds.
	// RECOMMENDED by the OpenRTB specification.
	Maxduration *int32 `protobuf:"varint,4,opt,name=maxduration" json:"maxduration,omitempty"`
	// Deprecated; use protocols.
	//
	// Deprecated: Marked as deprecated in youdao.proto.
	Protocol *BidRequest_Imp_Video_VideoBidResponseProtocol `protobuf:"varint,5,opt,name=protocol,enum=youdao.BidRequest_Imp_Video_VideoBidResponseProtocol" json:"protocol,omitempty"`
	// Array of supported video bid response protocols.
	// At least one supported protocol must be specified.
	Protocols []BidRequest_Imp_Video_VideoBidResponseProtocol `protobuf:"varint,21,rep,packed,name=protocols,enum=youdao.BidRequest_Imp_Video_VideoBidResponseProtocol" json:"protocols,omitempty"`
	// Width of the player in pixels.
	// RECOMMENDED by the OpenRTB specification.
	W *int32 `protobuf:"varint,6,opt,name=w" json:"w,omitempty"`
	// Height of the player in pixels.
	// RECOMMENDED by the OpenRTB specification.
	H *int32 `protobuf:"varint,7,opt,name=h" json:"h,omitempty"`
	// Indicates the start delay in seconds for pre-roll, mid-roll, or
	// post-roll ad placements. Refer to enum VideoStartDelay for generic
	// values. This field is not required, but it's highly recommended.
	Startdelay *int32 `protobuf:"varint,8,opt,name=startdelay" json:"startdelay,omitempty"`
	// If multiple ad impressions are offered in the same bid request,
	// the sequence number will allow for the coordinated delivery of
	// multiple creatives.
	Sequence *int32 `protobuf:"varint,9,opt,name=sequence,def=1" json:"sequence,omitempty"`
	// Blocked creative attributes.
	Battr []CreativeAttribute `protobuf:"varint,10,rep,packed,name=battr,enum=youdao.CreativeAttribute" json:"battr,omitempty"`
	// Maximum extended video ad duration, if extension is allowed.
	// If blank or 0, extension is not allowed. If -1, extension is allowed,
	// and there is no time limit imposed. If greater than 0, then the value
	// represents the number of seconds of extended play supported beyond
	// the maxduration value.
	Maxextended *int32 `protobuf:"varint,11,opt,name=maxextended" json:"maxextended,omitempty"`
	// Minimum bit rate in Kbps. Exchange may set this dynamically,
	// or universally across their set of publishers.
	Minbitrate *int32 `protobuf:"varint,12,opt,name=minbitrate" json:"minbitrate,omitempty"`
	// Maximum bit rate in Kbps. Exchange may set this dynamically,
	// or universally across their set of publishers.
	Maxbitrate *int32 `protobuf:"varint,13,opt,name=maxbitrate" json:"maxbitrate,omitempty"`
	// Indicates if letter-boxing of 4:3 content into a 16:9 window is
	// allowed, where 0 = no, 1 = yes.
	Boxingallowed *bool `protobuf:"varint,14,opt,name=boxingallowed,def=1" json:"boxingallowed,omitempty"`
	// Allowed playback methods. If none specified, assume all are allowed.
	Playbackmethod []BidRequest_Imp_Video_VideoPlaybackMethod `protobuf:"varint,15,rep,packed,name=playbackmethod,enum=youdao.BidRequest_Imp_Video_VideoPlaybackMethod" json:"playbackmethod,omitempty"`
	// Supported delivery methods (e.g., streaming, progressive).
	// If none specified, assume all are supported.
	Delivery []BidRequest_Imp_Video_ContentDeliveryMethod `protobuf:"varint,16,rep,packed,name=delivery,enum=youdao.BidRequest_Imp_Video_ContentDeliveryMethod" json:"delivery,omitempty"`
	// Ad position on screen.
	Pos *BidRequest_Imp_AdPosition `protobuf:"varint,17,opt,name=pos,enum=youdao.BidRequest_Imp_AdPosition" json:"pos,omitempty"`
	// Array of Banner objects (Section 3.2.3) if companion ads are available.
	Companionad []*BidRequest_Imp_Banner `protobuf:"bytes,18,rep,name=companionad" json:"companionad,omitempty"`
	// Companion ads in OpenRTB 2.1 format. (Or to be precise, interpretations
	// based on the buggy sample message in 5.1.4, fixed later in 2.2.)
	//
	// Deprecated: Marked as deprecated in youdao.proto.
	Companionad_21 *BidRequest_Imp_Video_CompanionAd `protobuf:"bytes,22,opt,name=companionad_21,json=companionad21" json:"companionad_21,omitempty"`
	// List of supported API frameworks for this impression.
	// If an API is not explicitly listed, it is assumed not to be supported.
	Api []BidRequest_Imp_APIFramework `protobuf:"varint,19,rep,packed,name=api,enum=youdao.BidRequest_Imp_APIFramework" json:"api,omitempty"`
	// Supported VAST companion ad types.  Recommended if companion Banner
	// objects are included via the companionad array.
	Companiontype []BidRequest_Imp_Video_VASTCompanionType `protobuf:"varint,20,rep,packed,name=companiontype,enum=youdao.BidRequest_Imp_Video_VASTCompanionType" json:"companiontype,omitempty"`
}

// Default values for BidRequest_Imp_Video fields.
const (
	Default_BidRequest_Imp_Video_Sequence      = int32(1)
	Default_BidRequest_Imp_Video_Boxingallowed = bool(true)
)

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Imp_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetLinearity() BidRequest_Imp_Video_VideoLinearity {
	if x != nil && x.Linearity != nil {
		return *x.Linearity
	}
	return BidRequest_Imp_Video_LINEAR
}

func (x *BidRequest_Imp_Video) GetMinduration() int32 {
	if x != nil && x.Minduration != nil {
		return *x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxduration() int32 {
	if x != nil && x.Maxduration != nil {
		return *x.Maxduration
	}
	return 0
}

// Deprecated: Marked as deprecated in youdao.proto.
func (x *BidRequest_Imp_Video) GetProtocol() BidRequest_Imp_Video_VideoBidResponseProtocol {
	if x != nil && x.Protocol != nil {
		return *x.Protocol
	}
	return BidRequest_Imp_Video_VAST_1_0
}

func (x *BidRequest_Imp_Video) GetProtocols() []BidRequest_Imp_Video_VideoBidResponseProtocol {
	if x != nil {
		return x.Protocols
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetStartdelay() int32 {
	if x != nil && x.Startdelay != nil {
		return *x.Startdelay
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetSequence() int32 {
	if x != nil && x.Sequence != nil {
		return *x.Sequence
	}
	return Default_BidRequest_Imp_Video_Sequence
}

func (x *BidRequest_Imp_Video) GetBattr() []CreativeAttribute {
	if x != nil {
		return x.Battr
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetMaxextended() int32 {
	if x != nil && x.Maxextended != nil {
		return *x.Maxextended
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMinbitrate() int32 {
	if x != nil && x.Minbitrate != nil {
		return *x.Minbitrate
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxbitrate() int32 {
	if x != nil && x.Maxbitrate != nil {
		return *x.Maxbitrate
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetBoxingallowed() bool {
	if x != nil && x.Boxingallowed != nil {
		return *x.Boxingallowed
	}
	return Default_BidRequest_Imp_Video_Boxingallowed
}

func (x *BidRequest_Imp_Video) GetPlaybackmethod() []BidRequest_Imp_Video_VideoPlaybackMethod {
	if x != nil {
		return x.Playbackmethod
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetDelivery() []BidRequest_Imp_Video_ContentDeliveryMethod {
	if x != nil {
		return x.Delivery
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetPos() BidRequest_Imp_AdPosition {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return BidRequest_Imp_UNKNOWN
}

func (x *BidRequest_Imp_Video) GetCompanionad() []*BidRequest_Imp_Banner {
	if x != nil {
		return x.Companionad
	}
	return nil
}

// Deprecated: Marked as deprecated in youdao.proto.
func (x *BidRequest_Imp_Video) GetCompanionad_21() *BidRequest_Imp_Video_CompanionAd {
	if x != nil {
		return x.Companionad_21
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetApi() []BidRequest_Imp_APIFramework {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetCompaniontype() []BidRequest_Imp_Video_VASTCompanionType {
	if x != nil {
		return x.Companiontype
	}
	return nil
}

// OpenRTB 3.2.5: This object represents a native type impression.
// Native ad units are intended to blend seamlessly into the surrounding
// content (e.g., a sponsored Twitter or Facebook post). As such, the
// response must be well-structured to afford the publisher fine-grained
// control over rendering.
//
// The Native Subcommittee has developed a companion specification to
// OpenRTB called the Native Ad Specification. It defines the request
// parameters and response markup structure of native ad units.
// This object provides the means of transporting request parameters as an
// opaque string so that the specific parameters can evolve separately
// under the auspices of the Native Ad Specification. Similarly, the
// ad markup served will be structured according to that specification.
//
// The presence of a Native as a subordinate of the Imp object indicates
// that this impression is offered as a native type impression.
// At the publisher's discretion, that same impression may also be offered
// as banner and/or video by also including as Imp subordinates the Banner
// and/or Video objects, respectively. However, any given bid for the
// impression must conform to one of the offered types.
type BidRequest_Imp_Native struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Types that are assignable to RequestOneof:
	//
	//	*BidRequest_Imp_Native_Request
	//	*BidRequest_Imp_Native_RequestNative
	RequestOneof isBidRequest_Imp_Native_RequestOneof `protobuf_oneof:"request_oneof"`
	// Version of the Native Ad Specification to which request complies;
	// highly recommended for efficient parsing.
	Ver *string `protobuf:"bytes,2,opt,name=ver" json:"ver,omitempty"`
	// List of supported API frameworks for this impression.
	// If an API is not explicitly listed, it is assumed not to be supported.
	Api []BidRequest_Imp_APIFramework `protobuf:"varint,3,rep,packed,name=api,enum=youdao.BidRequest_Imp_APIFramework" json:"api,omitempty"`
	// Blocked creative attributes.
	Battr []CreativeAttribute `protobuf:"varint,4,rep,packed,name=battr,enum=youdao.CreativeAttribute" json:"battr,omitempty"`
}

func (x *BidRequest_Imp_Native) Reset() {
	*x = BidRequest_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native) ProtoMessage() {}

func (x *BidRequest_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (m *BidRequest_Imp_Native) GetRequestOneof() isBidRequest_Imp_Native_RequestOneof {
	if m != nil {
		return m.RequestOneof
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetRequest() string {
	if x, ok := x.GetRequestOneof().(*BidRequest_Imp_Native_Request); ok {
		return x.Request
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetRequestNative() *NativeRequest {
	if x, ok := x.GetRequestOneof().(*BidRequest_Imp_Native_RequestNative); ok {
		return x.RequestNative
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetApi() []BidRequest_Imp_APIFramework {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetBattr() []CreativeAttribute {
	if x != nil {
		return x.Battr
	}
	return nil
}

type isBidRequest_Imp_Native_RequestOneof interface {
	isBidRequest_Imp_Native_RequestOneof()
}

type BidRequest_Imp_Native_Request struct {
	// Request payload complying with the Native Ad Specification.
	// Exactly one of {request, request_native} should be used;
	// this is the OpenRTB-compliant field for JSON serialization.
	Request string `protobuf:"bytes,1,opt,name=request,oneof"`
}

type BidRequest_Imp_Native_RequestNative struct {
	// Request payload complying with the Native Ad Specification.
	// Exactly one of {request, request_native} should be used;
	// this is an alternate field preferred for Protobuf serialization.
	RequestNative *NativeRequest `protobuf:"bytes,50,opt,name=request_native,json=requestNative,oneof"`
}

func (*BidRequest_Imp_Native_Request) isBidRequest_Imp_Native_RequestOneof() {}

func (*BidRequest_Imp_Native_RequestNative) isBidRequest_Imp_Native_RequestOneof() {}

// OpenRTB 3.2.17: This object is the private marketplace container for
// direct deals between buyers and sellers that may pertain to this
// impression. The actual deals are represented as a collection of
// Deal objects. Refer to Section 7.2 for more details.
type BidRequest_Imp_Pmp struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Indicator of auction eligibility to seats named in the Direct Deals
	// object, where 0 = all bids are accepted, 1 = bids are restricted to
	// the deals specified and the terms thereof.
	PrivateAuction *bool `protobuf:"varint,1,opt,name=private_auction,json=privateAuction" json:"private_auction,omitempty"`
	// Array of Deal (Section 3.2.18) objects that convey the specific deals
	// applicable to this impression.
	Deals []*BidRequest_Imp_Pmp_Deal `protobuf:"bytes,2,rep,name=deals" json:"deals,omitempty"`
}

func (x *BidRequest_Imp_Pmp) Reset() {
	*x = BidRequest_Imp_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 3}
}

func (x *BidRequest_Imp_Pmp) GetPrivateAuction() bool {
	if x != nil && x.PrivateAuction != nil {
		return *x.PrivateAuction
	}
	return false
}

func (x *BidRequest_Imp_Pmp) GetDeals() []*BidRequest_Imp_Pmp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

// OpenRTB 2.1 compatibility.
type BidRequest_Imp_Video_CompanionAd struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Banner []*BidRequest_Imp_Banner `protobuf:"bytes,1,rep,name=banner" json:"banner,omitempty"`
}

func (x *BidRequest_Imp_Video_CompanionAd) Reset() {
	*x = BidRequest_Imp_Video_CompanionAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video_CompanionAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video_CompanionAd) ProtoMessage() {}

func (x *BidRequest_Imp_Video_CompanionAd) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video_CompanionAd.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video_CompanionAd) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 1, 0}
}

func (x *BidRequest_Imp_Video_CompanionAd) GetBanner() []*BidRequest_Imp_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

// OpenRTB 3.2.18: This object constitutes a specific deal that was struck
// a priori between a buyer and a seller. Its presence with the Pmp
// collection indicates that this impression is available under the terms
// of that deal. Refer to Section 7.2 for more details.
type BidRequest_Imp_Pmp_Deal struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// A unique identifier for the direct deal.
	// REQUIRED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// Minimum bid for this impression expressed in CPM.
	Bidfloor *float64 `protobuf:"fixed64,2,opt,name=bidfloor,def=0" json:"bidfloor,omitempty"`
	// Currency specified using ISO-4217 alpha codes. This may be different
	// from bid currency returned by bidder if this is allowed
	// by the exchange.
	Bidfloorcur *string `protobuf:"bytes,3,opt,name=bidfloorcur,def=USD" json:"bidfloorcur,omitempty"`
	// Whitelist of buyer seats allowed to bid on this deal. Seat IDs must
	// be communicated between bidders and the exchange a priori.
	// Omission implies no seat restrictions.
	Wseat []string `protobuf:"bytes,4,rep,name=wseat" json:"wseat,omitempty"`
	// Array of advertiser domains (e.g., advertiser.com) allowed to
	// bid on this deal. Omission implies no advertiser restrictions.
	Wadomain []string `protobuf:"bytes,5,rep,name=wadomain" json:"wadomain,omitempty"`
	// Optional override of the overall auction type of the bid request,
	// where 1 = First Price, 2 = Second Price Plus, 3 = the value passed
	// in bidfloor is the agreed upon deal price. Additional auction types
	// can be defined by the exchange.
	At *BidRequest_AuctionType `protobuf:"varint,6,opt,name=at,enum=youdao.BidRequest_AuctionType" json:"at,omitempty"`
}

// Default values for BidRequest_Imp_Pmp_Deal fields.
const (
	Default_BidRequest_Imp_Pmp_Deal_Bidfloor    = float64(0)
	Default_BidRequest_Imp_Pmp_Deal_Bidfloorcur = string("USD")
)

func (x *BidRequest_Imp_Pmp_Deal) Reset() {
	*x = BidRequest_Imp_Pmp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp_Deal) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp_Deal) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 0, 3, 0}
}

func (x *BidRequest_Imp_Pmp_Deal) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Pmp_Deal) GetBidfloor() float64 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return Default_BidRequest_Imp_Pmp_Deal_Bidfloor
}

func (x *BidRequest_Imp_Pmp_Deal) GetBidfloorcur() string {
	if x != nil && x.Bidfloorcur != nil {
		return *x.Bidfloorcur
	}
	return Default_BidRequest_Imp_Pmp_Deal_Bidfloorcur
}

func (x *BidRequest_Imp_Pmp_Deal) GetWseat() []string {
	if x != nil {
		return x.Wseat
	}
	return nil
}

func (x *BidRequest_Imp_Pmp_Deal) GetWadomain() []string {
	if x != nil {
		return x.Wadomain
	}
	return nil
}

func (x *BidRequest_Imp_Pmp_Deal) GetAt() BidRequest_AuctionType {
	if x != nil && x.At != nil {
		return *x.At
	}
	return BidRequest_FIRST_PRICE
}

// OpenRTB 3.2.15: Segment objects are essentially key-value pairs that
// convey specific units of data about the user. The parent Data object
// is a collection of such values from a given data provider.
// The specific segment names and value options must be published by the
// exchange a priori to its bidders.
type BidRequest_Data_Segment struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// ID of the data segment specific to the data provider.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Name of the data segment specific to the data provider.
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// String representation of the data segment value.
	Value *string `protobuf:"bytes,3,opt,name=value" json:"value,omitempty"`
}

func (x *BidRequest_Data_Segment) Reset() {
	*x = BidRequest_Data_Segment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Data_Segment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Data_Segment) ProtoMessage() {}

func (x *BidRequest_Data_Segment) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Data_Segment.ProtoReflect.Descriptor instead.
func (*BidRequest_Data_Segment) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{0, 9, 0}
}

func (x *BidRequest_Data_Segment) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Data_Segment) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Data_Segment) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// OpenRTB 4.2.2: A bid response can contain multiple SeatBid objects, each on
// behalf of a different bidder seat and each containing one or more
// individual bids. If multiple impressions are presented in the request, the
// group attribute can be used to specify if a seat is willing to accept any
// impressions that it can win (default) or if it is only interested in
// winning any if it can win them all as a group.
type BidResponse_SeatBid struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Array of 1+ Bid objects (Section 4.2.3) each related to an impression.
	// Multiple bids can relate to the same impression.
	Bid []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`
	// ID of the bidder seat on whose behalf this bid is made.
	Seat *string `protobuf:"bytes,2,opt,name=seat" json:"seat,omitempty"`
	// 0 = impressions can be won individually; 1 = impressions must be won or
	// lost as a group.
	Group *bool `protobuf:"varint,3,opt,name=group" json:"group,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() string {
	if x != nil && x.Seat != nil {
		return *x.Seat
	}
	return ""
}

func (x *BidResponse_SeatBid) GetGroup() bool {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return false
}

// OpenRTB 4.2.3: A SeatBid object contains one or more Bid objects,
// each of which relates to a specific impression in the bid request
// via the impid attribute and constitutes an offer to buy that impression
// for a given price.
type BidResponse_SeatBid_Bid struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Bidder generated bid ID to assist with logging/tracking.
	// REQUIRED by the OpenRTB specification.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// ID of the Imp object in the related bid request.
	// REQUIRED by the OpenRTB specification.
	Impid *string `protobuf:"bytes,2,req,name=impid" json:"impid,omitempty"`
	// Bid price expressed as CPM although the actual transaction is for a
	// unit impression only. Note that while the type indicates float, integer
	// math is highly recommended when handling currencies
	// (e.g., BigDecimal in Java).
	// REQUIRED by the OpenRTB specification.
	Price *float64 `protobuf:"fixed64,3,req,name=price" json:"price,omitempty"`
	// ID of a preloaded ad to be served if the bid wins.
	Adid *string `protobuf:"bytes,4,opt,name=adid" json:"adid,omitempty"`
	// Win notice URL called by the exchange if the bid wins; optional means
	// of serving ad markup.
	Nurl *string `protobuf:"bytes,5,opt,name=nurl" json:"nurl,omitempty"`
	// Types that are assignable to AdmOneof:
	//
	//	*BidResponse_SeatBid_Bid_Adm
	//	*BidResponse_SeatBid_Bid_AdmNative
	AdmOneof isBidResponse_SeatBid_Bid_AdmOneof `protobuf_oneof:"adm_oneof"`
	// Advertiser domain for block list checking (e.g., "ford.com"). This can
	// be an array of for the case of rotating creatives. Exchanges can
	// mandate that only one domain is allowed.
	Adomain []string `protobuf:"bytes,7,rep,name=adomain" json:"adomain,omitempty"`
	// Bundle or package name (e.g., com.foo.mygame) of the app being
	// advertised, if applicable; intended to be a unique ID across exchanges.
	Bundle *string `protobuf:"bytes,14,opt,name=bundle" json:"bundle,omitempty"`
	// URL without cache-busting to an image that is representative of the
	// content of the campaign for ad quality/safety checking.
	Iurl *string `protobuf:"bytes,8,opt,name=iurl" json:"iurl,omitempty"`
	// Campaign ID to assist with ad quality checking; the collection of
	// creatives for which iurl should be representative.
	Cid *string `protobuf:"bytes,9,opt,name=cid" json:"cid,omitempty"`
	// Creative ID to assist with ad quality checking.
	Crid *string `protobuf:"bytes,10,opt,name=crid" json:"crid,omitempty"`
	// IAB content categories of the creative. See enum ContentCategory.
	Cat []string `protobuf:"bytes,15,rep,name=cat" json:"cat,omitempty"`
	// Set of attributes describing the creative.
	Attr []CreativeAttribute `protobuf:"varint,11,rep,packed,name=attr,enum=youdao.CreativeAttribute" json:"attr,omitempty"`
	// Reference to the deal.id from the bid request if this bid pertains to a
	// private marketplace direct deal.
	Dealid *string `protobuf:"bytes,13,opt,name=dealid" json:"dealid,omitempty"`
	// Width of the ad in pixels.
	W *int32 `protobuf:"varint,16,opt,name=w" json:"w,omitempty"`
	// Height of the ad in pixels.
	H *int32 `protobuf:"varint,17,opt,name=h" json:"h,omitempty"`
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpid() string {
	if x != nil && x.Impid != nil {
		return *x.Impid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetAdid() string {
	if x != nil && x.Adid != nil {
		return *x.Adid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetNurl() string {
	if x != nil && x.Nurl != nil {
		return *x.Nurl
	}
	return ""
}

func (m *BidResponse_SeatBid_Bid) GetAdmOneof() isBidResponse_SeatBid_Bid_AdmOneof {
	if m != nil {
		return m.AdmOneof
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAdm() string {
	if x, ok := x.GetAdmOneof().(*BidResponse_SeatBid_Bid_Adm); ok {
		return x.Adm
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetAdmNative() *NativeResponse {
	if x, ok := x.GetAdmOneof().(*BidResponse_SeatBid_Bid_AdmNative); ok {
		return x.AdmNative
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAdomain() []string {
	if x != nil {
		return x.Adomain
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetIurl() string {
	if x != nil && x.Iurl != nil {
		return *x.Iurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCid() string {
	if x != nil && x.Cid != nil {
		return *x.Cid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAttr() []CreativeAttribute {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetDealid() string {
	if x != nil && x.Dealid != nil {
		return *x.Dealid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type isBidResponse_SeatBid_Bid_AdmOneof interface {
	isBidResponse_SeatBid_Bid_AdmOneof()
}

type BidResponse_SeatBid_Bid_Adm struct {
	// Optional means of conveying ad markup in case the bid wins;
	// supersedes the win notice if markup is included in both.
	// For native ad bids, exactly one of {adm, adm_native} should be used;
	// this is the OpenRTB-compliant field for JSON serialization.
	Adm string `protobuf:"bytes,6,opt,name=adm,oneof"`
}

type BidResponse_SeatBid_Bid_AdmNative struct {
	// Native ad response.
	// For native ad bids, exactly one of {adm, adm_native} should be used;
	// this is an alternate field preferred for Protobuf serialization.
	AdmNative *NativeResponse `protobuf:"bytes,50,opt,name=adm_native,json=admNative,oneof"`
}

func (*BidResponse_SeatBid_Bid_Adm) isBidResponse_SeatBid_Bid_AdmOneof() {}

func (*BidResponse_SeatBid_Bid_AdmNative) isBidResponse_SeatBid_Bid_AdmOneof() {}

// OpenRTB Native 4.2: The main container object for each asset requested or
// supported by Exchange on behalf of the rendering client.
// Any object that is required is to be flagged as such. Only one of the
// {title,img,video,data} objects should be present in each object.
// All others should be null/absent. The id is to be unique within the
// Asset array so that the response can be aligned.
type NativeRequest_Asset struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Unique asset ID, assigned by exchange. Typically a counter for the array.
	// REQUIRED by the OpenRTB Native specification.
	Id *int32 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// Set to true if asset is required
	// (exchange will not accept a bid without it).
	Required *bool `protobuf:"varint,2,opt,name=required,def=0" json:"required,omitempty"`
	// Types that are assignable to AssetOneof:
	//
	//	*NativeRequest_Asset_Title_
	//	*NativeRequest_Asset_Img
	//	*NativeRequest_Asset_Video
	//	*NativeRequest_Asset_Data_
	AssetOneof isNativeRequest_Asset_AssetOneof `protobuf_oneof:"asset_oneof"`
}

// Default values for NativeRequest_Asset fields.
const (
	Default_NativeRequest_Asset_Required = bool(false)
)

func (x *NativeRequest_Asset) Reset() {
	*x = NativeRequest_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset) ProtoMessage() {}

func (x *NativeRequest_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0}
}

func (x *NativeRequest_Asset) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NativeRequest_Asset) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return Default_NativeRequest_Asset_Required
}

func (m *NativeRequest_Asset) GetAssetOneof() isNativeRequest_Asset_AssetOneof {
	if m != nil {
		return m.AssetOneof
	}
	return nil
}

func (x *NativeRequest_Asset) GetTitle() *NativeRequest_Asset_Title {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Title_); ok {
		return x.Title
	}
	return nil
}

func (x *NativeRequest_Asset) GetImg() *NativeRequest_Asset_Image {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Img); ok {
		return x.Img
	}
	return nil
}

func (x *NativeRequest_Asset) GetVideo() *BidRequest_Imp_Video {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Video); ok {
		return x.Video
	}
	return nil
}

func (x *NativeRequest_Asset) GetData() *NativeRequest_Asset_Data {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Data_); ok {
		return x.Data
	}
	return nil
}

type isNativeRequest_Asset_AssetOneof interface {
	isNativeRequest_Asset_AssetOneof()
}

type NativeRequest_Asset_Title_ struct {
	// Title object for title assets.
	Title *NativeRequest_Asset_Title `protobuf:"bytes,3,opt,name=title,oneof"`
}

type NativeRequest_Asset_Img struct {
	// Image object for image assets.
	Img *NativeRequest_Asset_Image `protobuf:"bytes,4,opt,name=img,oneof"`
}

type NativeRequest_Asset_Video struct {
	// Video object for video assets.
	// Note that in-stream video ads are not part of Native.
	// Native ads may contain a video as the ad creative itself.
	Video *BidRequest_Imp_Video `protobuf:"bytes,5,opt,name=video,oneof"`
}

type NativeRequest_Asset_Data_ struct {
	// Data object for ratings, prices etc.
	Data *NativeRequest_Asset_Data `protobuf:"bytes,6,opt,name=data,oneof"`
}

func (*NativeRequest_Asset_Title_) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Img) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Video) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Data_) isNativeRequest_Asset_AssetOneof() {}

// OpenRTB Native 4.3: The Title object is to be used for title element
// of the Native ad.
type NativeRequest_Asset_Title struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Maximum length of the text in the title element.
	// REQUIRED by the OpenRTB Native specification.
	Len *int32 `protobuf:"varint,1,req,name=len" json:"len,omitempty"`
}

func (x *NativeRequest_Asset_Title) Reset() {
	*x = NativeRequest_Asset_Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Title) ProtoMessage() {}

func (x *NativeRequest_Asset_Title) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Title.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Title) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *NativeRequest_Asset_Title) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

// OpenRTB Native 4.4: The Image object to be used for all image elements
// of the Native ad such as Icons, Main Image, etc.
type NativeRequest_Asset_Image struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Type ID of the image element supported by the publisher.
	// The publisher can display this information in an appropriate format.
	Type *NativeRequest_Asset_Image_ImageAssetType `protobuf:"varint,1,opt,name=type,enum=youdao.NativeRequest_Asset_Image_ImageAssetType" json:"type,omitempty"`
	// Width of the image in pixels.
	W *int32 `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	// Height of the image in pixels.
	H *int32 `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
	// The minimum requested width of the image in pixels. This option should
	// be used for any rescaling of images by the client. Either w or wmin
	// should be transmitted. If only w is included, it should be considered
	// an exact requirement.
	// RECOMMENDED by the OpenRTB Native specification.
	Wmin *int32 `protobuf:"varint,4,opt,name=wmin" json:"wmin,omitempty"`
	// The minimum requested height of the image in pixels. This option should
	// be used for any rescaling of images by the client. Either h or hmin
	// should be transmitted. If only h is included, it should be considered
	// an exact requirement.
	// RECOMMENDED by the OpenRTB Native specification.
	Hmin *int32 `protobuf:"varint,5,opt,name=hmin" json:"hmin,omitempty"`
	// Whitelist of content MIME types supported. Popular MIME types include,
	// but are not limited to "image/jpg" and "image/gif". Each implementing
	// Exchange should have their own list of supported types in the
	// integration docs. See Wikipedia's MIME page for more information and
	// links to all IETF RFCs. If blank, assume all types are allowed.
	Mimes []string `protobuf:"bytes,6,rep,name=mimes" json:"mimes,omitempty"`
}

func (x *NativeRequest_Asset_Image) Reset() {
	*x = NativeRequest_Asset_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Image) ProtoMessage() {}

func (x *NativeRequest_Asset_Image) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Image.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Image) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *NativeRequest_Asset_Image) GetType() NativeRequest_Asset_Image_ImageAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return NativeRequest_Asset_Image_ICON
}

func (x *NativeRequest_Asset_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetWmin() int32 {
	if x != nil && x.Wmin != nil {
		return *x.Wmin
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetHmin() int32 {
	if x != nil && x.Hmin != nil {
		return *x.Hmin
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

// OpenRTB Native 4.6: The Data Object is to be used for all non-core
// elements of the native unit such as Ratings, Review Count, Stars,
// Download count, descriptions etc. It is also generic for future of Native
// elements not contemplated at the time of the writing of this document.
type NativeRequest_Asset_Data struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Type ID of the element supported by the publisher. The publisher can
	// display this information in an appropriate format.
	// REQUIRED by the OpenRTB Native specification.
	Type *NativeRequest_Asset_Data_DataAssetType `protobuf:"varint,1,req,name=type,enum=youdao.NativeRequest_Asset_Data_DataAssetType" json:"type,omitempty"`
	// Maximum length of the text in the element's response.
	Len *int32 `protobuf:"varint,2,opt,name=len" json:"len,omitempty"`
}

func (x *NativeRequest_Asset_Data) Reset() {
	*x = NativeRequest_Asset_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Data) ProtoMessage() {}

func (x *NativeRequest_Asset_Data) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Data.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Data) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{2, 0, 2}
}

func (x *NativeRequest_Asset_Data) GetType() NativeRequest_Asset_Data_DataAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return NativeRequest_Asset_Data_SPONSORED
}

func (x *NativeRequest_Asset_Data) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

// OpenRTB Native 5.8: Used for "call to action" assets, or other links from
// the Native ad. This Object should be associated to its peer object in the
// parent Asset Object. When that peer object is activated (clicked)
// the action should take the user to the location of the link.
type NativeResponse_Link struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Landing URL of the clickable link.
	Url *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	// List of third-party tracker URLs to be fired on click of the URL.
	Clicktrackers []string `protobuf:"bytes,2,rep,name=clicktrackers" json:"clicktrackers,omitempty"`
	// Fallback URL for deeplink. To be used if the URL given in url is not
	// supported by the device.
	Fallback *string `protobuf:"bytes,3,opt,name=fallback" json:"fallback,omitempty"`
}

func (x *NativeResponse_Link) Reset() {
	*x = NativeResponse_Link{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Link) ProtoMessage() {}

func (x *NativeResponse_Link) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Link.ProtoReflect.Descriptor instead.
func (*NativeResponse_Link) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 0}
}

func (x *NativeResponse_Link) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Link) GetClicktrackers() []string {
	if x != nil {
		return x.Clicktrackers
	}
	return nil
}

func (x *NativeResponse_Link) GetFallback() string {
	if x != nil && x.Fallback != nil {
		return *x.Fallback
	}
	return ""
}

// OpenRTB Native 5.3: Corresponds to the Asset Object in the request.
// The main container object for each asset requested or supported by Exchange
// on behalf of the rendering client. Any object that is required is to be
// flagged as such. Only one of the {title,img,video,data} objects should be
// present in each object. All others should be null/absent. The id is to be
// unique within the Asset array so that the response can be aligned.
type NativeResponse_Asset struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Unique asset ID, assigned by exchange, must match one of the asset IDs
	// in request.
	// REQUIRED by the OpenRTB Native specification.
	Id *int32 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// Set to 1 if asset is required. (bidder requires it to be displayed).
	Required *bool `protobuf:"varint,2,opt,name=required,def=0" json:"required,omitempty"`
	// Types that are assignable to AssetOneof:
	//
	//	*NativeResponse_Asset_Title_
	//	*NativeResponse_Asset_Img
	//	*NativeResponse_Asset_Video_
	//	*NativeResponse_Asset_Data_
	AssetOneof isNativeResponse_Asset_AssetOneof `protobuf_oneof:"asset_oneof"`
	// Link object for call to actions. This link is to associated to the other
	// populated field within the object.
	Link *NativeResponse_Link `protobuf:"bytes,7,opt,name=link" json:"link,omitempty"`
}

// Default values for NativeResponse_Asset fields.
const (
	Default_NativeResponse_Asset_Required = bool(false)
)

func (x *NativeResponse_Asset) Reset() {
	*x = NativeResponse_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset) ProtoMessage() {}

func (x *NativeResponse_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 1}
}

func (x *NativeResponse_Asset) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NativeResponse_Asset) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return Default_NativeResponse_Asset_Required
}

func (m *NativeResponse_Asset) GetAssetOneof() isNativeResponse_Asset_AssetOneof {
	if m != nil {
		return m.AssetOneof
	}
	return nil
}

func (x *NativeResponse_Asset) GetTitle() *NativeResponse_Asset_Title {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Title_); ok {
		return x.Title
	}
	return nil
}

func (x *NativeResponse_Asset) GetImg() *NativeResponse_Asset_Image {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Img); ok {
		return x.Img
	}
	return nil
}

func (x *NativeResponse_Asset) GetVideo() *NativeResponse_Asset_Video {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Video_); ok {
		return x.Video
	}
	return nil
}

func (x *NativeResponse_Asset) GetData() *NativeResponse_Asset_Data {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Data_); ok {
		return x.Data
	}
	return nil
}

func (x *NativeResponse_Asset) GetLink() *NativeResponse_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

type isNativeResponse_Asset_AssetOneof interface {
	isNativeResponse_Asset_AssetOneof()
}

type NativeResponse_Asset_Title_ struct {
	// Title object for title assets.
	Title *NativeResponse_Asset_Title `protobuf:"bytes,3,opt,name=title,oneof"`
}

type NativeResponse_Asset_Img struct {
	// Image object for image assets.
	Img *NativeResponse_Asset_Image `protobuf:"bytes,4,opt,name=img,oneof"`
}

type NativeResponse_Asset_Video_ struct {
	// Video object for video assets. Note that in-stream video ads are not part
	// of Native. Native ads may contain a video as the ad creative itself.
	Video *NativeResponse_Asset_Video `protobuf:"bytes,5,opt,name=video,oneof"`
}

type NativeResponse_Asset_Data_ struct {
	// Data object for ratings, prices etc.
	Data *NativeResponse_Asset_Data `protobuf:"bytes,6,opt,name=data,oneof"`
}

func (*NativeResponse_Asset_Title_) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Img) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Video_) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Data_) isNativeResponse_Asset_AssetOneof() {}

// OpenRTB Native 5.4: Corresponds to the Title Object in the request,
// with the value filled in.
type NativeResponse_Asset_Title struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// The text associated with the text element.
	// REQUIRED by the OpenRTB Native specification.
	Text *string `protobuf:"bytes,1,req,name=text" json:"text,omitempty"`
}

func (x *NativeResponse_Asset_Title) Reset() {
	*x = NativeResponse_Asset_Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Title) ProtoMessage() {}

func (x *NativeResponse_Asset_Title) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Title.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Title) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 1, 0}
}

func (x *NativeResponse_Asset_Title) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

// OpenRTB Native 5.5: Corresponds to the Image Object in the request.
// The Image object to be used for all image elements of the Native ad
// such as Icons, Main Image, etc.
type NativeResponse_Asset_Image struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// URL of the image asset.
	Url *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	// Width of the image in pixels.
	// RECOMMENDED by the OpenRTB Native specification.
	W *int32 `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	// Height of the image in pixels.
	// RECOMMENDED by the OpenRTB Native specification.
	H *int32 `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
}

func (x *NativeResponse_Asset_Image) Reset() {
	*x = NativeResponse_Asset_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Image) ProtoMessage() {}

func (x *NativeResponse_Asset_Image) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Image.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Image) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 1, 1}
}

func (x *NativeResponse_Asset_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Asset_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeResponse_Asset_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

// OpenRTB Native 5.6: Corresponds to the Data Object in the request, with
// the value filled in. The Data Object is to be used for all miscellaneous
// elements of the native unit such as Ratings, Review Count, Stars,
// Downloads, Price count etc. It is also generic for future of Native
// elements not contemplated at the time of the writing of this document.
type NativeResponse_Asset_Data struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// The optional formatted string name of the data type to be displayed.
	Label *string `protobuf:"bytes,1,opt,name=label" json:"label,omitempty"`
	// The formatted string of data to be displayed. Can contain a formatted
	// value such as "5 stars" or "$10" or "3.4 stars out of 5".
	// REQUIRED by the OpenRTB Native specification.
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *NativeResponse_Asset_Data) Reset() {
	*x = NativeResponse_Asset_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Data) ProtoMessage() {}

func (x *NativeResponse_Asset_Data) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Data.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Data) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 1, 2}
}

func (x *NativeResponse_Asset_Data) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *NativeResponse_Asset_Data) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// OpenRTB Native 5.7: Corresponds to the Video Object in the request,
// yet containing a value of a conforming VAST tag as a value.
type NativeResponse_Asset_Video struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// VAST xml.
	// REQUIRED by the OpenRTB Native specification: at least 1 element.
	Vasttag []string `protobuf:"bytes,1,rep,name=vasttag" json:"vasttag,omitempty"`
}

func (x *NativeResponse_Asset_Video) Reset() {
	*x = NativeResponse_Asset_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_youdao_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Video) ProtoMessage() {}

func (x *NativeResponse_Asset_Video) ProtoReflect() protoreflect.Message {
	mi := &file_youdao_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Video.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Video) Descriptor() ([]byte, []int) {
	return file_youdao_proto_rawDescGZIP(), []int{3, 1, 3}
}

func (x *NativeResponse_Asset_Video) GetVasttag() []string {
	if x != nil {
		return x.Vasttag
	}
	return nil
}

var File_youdao_proto protoreflect.FileDescriptor

var file_youdao_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x22, 0xf5, 0x34, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x12, 0x23, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x48, 0x00, 0x12,
	0x25, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x70, 0x70, 0x48, 0x00, 0x12, 0x29, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x25, 0x0a, 0x04, 0x72, 0x65, 0x67, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x38, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x0c, 0x53, 0x45, 0x43,
	0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x12, 0x0c, 0x0a, 0x04, 0x74, 0x6d, 0x61,
	0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0d, 0x0a, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x12, 0x16, 0x0a, 0x07, 0x61, 0x6c, 0x6c, 0x69, 0x6d, 0x70,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x0b,
	0x0a, 0x03, 0x63, 0x75, 0x72, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x62,
	0x63, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x62, 0x61, 0x64,
	0x76, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x12, 0x13, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x1a, 0xfb, 0x18, 0x0a,
	0x03, 0x49, 0x6d, 0x70, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x12, 0x2d, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12,
	0x2b, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x16, 0x0a, 0x0e,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x19, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0d, 0x0a, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x12, 0x0d,
	0x0a, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x13, 0x0a,
	0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x3a,
	0x01, 0x30, 0x12, 0x18, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x03, 0x55, 0x53, 0x44, 0x12, 0x0e, 0x0a, 0x06,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x12, 0x14, 0x0a, 0x0c,
	0x69, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x62, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x09, 0x12, 0x27, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x1a, 0xd4, 0x04, 0x0a, 0x06, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x09, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x12, 0x09, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0c, 0x0a, 0x04, 0x77,
	0x6d, 0x61, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0c, 0x0a, 0x04, 0x68, 0x6d, 0x61,
	0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0c, 0x0a, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0c, 0x0a, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x2e, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3d, 0x0a, 0x05, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x10, 0x01, 0x12, 0x2c,
	0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x05,
	0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x74,
	0x6f, 0x70, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x12, 0x45, 0x0a,
	0x06, 0x65, 0x78, 0x70, 0x64, 0x69, 0x72, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70,
	0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x02, 0x10, 0x01, 0x12, 0x34, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x02, 0x10, 0x01, 0x22, 0x55, 0x0a, 0x0c, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x58, 0x48,
	0x54, 0x4d, 0x4c, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x58, 0x48, 0x54, 0x4d, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x44,
	0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x41, 0x56, 0x41, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x5f, 0x41, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10,
	0x04, 0x22, 0x57, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x45, 0x46, 0x54,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x02, 0x12, 0x06, 0x0a,
	0x02, 0x55, 0x50, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x12,
	0x19, 0x0a, 0x15, 0x45, 0x58, 0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x55,
	0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90,
	0x4e, 0x1a, 0xed, 0x0b, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0d, 0x0a, 0x05, 0x6d,
	0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x12, 0x3e, 0x0a, 0x09, 0x6c, 0x69,
	0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x13, 0x0a, 0x0b, 0x6d, 0x69,
	0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x12,
	0x13, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x12, 0x4b, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42, 0x02, 0x18,
	0x01, 0x12, 0x4c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x18, 0x15,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42, 0x02, 0x10, 0x01, 0x12,
	0x09, 0x0a, 0x01, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x12, 0x09, 0x0a, 0x01, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x12, 0x12, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65,
	0x6c, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x12, 0x13, 0x0a, 0x08, 0x73, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31, 0x12, 0x2c,
	0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0b,
	0x6d, 0x61, 0x78, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x12, 0x12, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x12, 0x12, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x62, 0x69, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x12, 0x1b, 0x0a, 0x0d, 0x62, 0x6f, 0x78,
	0x69, 0x6e, 0x67, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x12, 0x4c, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x79, 0x62, 0x61,
	0x63, 0x6b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x42, 0x02, 0x10, 0x01, 0x12, 0x48, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x18, 0x10, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x02, 0x10, 0x01, 0x12, 0x2e,
	0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6d, 0x70, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f, 0x6e, 0x61, 0x64, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x12, 0x44, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f, 0x6e, 0x61,
	0x64, 0x5f, 0x32, 0x31, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69,
	0x6f, 0x6e, 0x41, 0x64, 0x42, 0x02, 0x18, 0x01, 0x12, 0x34, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18,
	0x13, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x50,
	0x49, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x02, 0x10, 0x01, 0x12, 0x49,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x2e, 0x56, 0x41, 0x53, 0x54, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x10, 0x01, 0x1a, 0x43, 0x0a, 0x0b, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0x2c,
	0x0a, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x49, 0x4e, 0x45, 0x41, 0x52, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x4e, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x41, 0x52, 0x10, 0x02, 0x22, 0x86, 0x01, 0x0a,
	0x18, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0c, 0x0a, 0x08, 0x56, 0x41, 0x53,
	0x54, 0x5f, 0x31, 0x5f, 0x30, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x56, 0x41, 0x53, 0x54, 0x5f,
	0x32, 0x5f, 0x30, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x56, 0x41, 0x53, 0x54, 0x5f, 0x33, 0x5f,
	0x30, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x41, 0x53, 0x54, 0x5f, 0x31, 0x5f, 0x30, 0x5f,
	0x57, 0x52, 0x41, 0x50, 0x50, 0x45, 0x52, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x41, 0x53,
	0x54, 0x5f, 0x32, 0x5f, 0x30, 0x5f, 0x57, 0x52, 0x41, 0x50, 0x50, 0x45, 0x52, 0x10, 0x05, 0x12,
	0x14, 0x0a, 0x10, 0x56, 0x41, 0x53, 0x54, 0x5f, 0x33, 0x5f, 0x30, 0x5f, 0x57, 0x52, 0x41, 0x50,
	0x50, 0x45, 0x52, 0x10, 0x06, 0x22, 0x69, 0x0a, 0x13, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c,
	0x61, 0x79, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x12,
	0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x5f,
	0x4f, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x03,
	0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x04,
	0x22, 0x5e, 0x0a, 0x0f, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x10,
	0x00, 0x12, 0x1d, 0x0a, 0x10, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x4d, 0x49, 0x44,
	0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x12, 0x1e, 0x0a, 0x11, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x50, 0x4f, 0x53, 0x54,
	0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x22, 0x3f, 0x0a, 0x11, 0x56, 0x41, 0x53, 0x54, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54, 0x4d, 0x4c, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43,
	0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10,
	0x03, 0x22, 0x37, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54,
	0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x49, 0x56, 0x45, 0x10, 0x02, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90,
	0x4e, 0x1a, 0xd5, 0x01, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x11, 0x0a, 0x07,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x12,
	0x2f, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x12, 0x0b, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x34, 0x0a,
	0x03, 0x61, 0x70, 0x69, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x2e, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42,
	0x02, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10,
	0x01, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x42, 0x0f, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x1a, 0xed, 0x01, 0x0a, 0x03, 0x50, 0x6d,
	0x70, 0x12, 0x17, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x12, 0x2e, 0x0a, 0x05, 0x64, 0x65,
	0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d,
	0x70, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x1a, 0x95, 0x01, 0x0a, 0x04, 0x44,
	0x65, 0x61, 0x6c, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x12,
	0x13, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x3a, 0x01, 0x30, 0x12, 0x18, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x63, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x03, 0x55, 0x53, 0x44, 0x12, 0x0d,
	0x0a, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x12, 0x10, 0x0a,
	0x08, 0x77, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x12,
	0x2a, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0xa8, 0x01, 0x0a, 0x0a, 0x41, 0x64,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x42, 0x4f, 0x56, 0x45, 0x5f, 0x54,
	0x48, 0x45, 0x5f, 0x46, 0x4f, 0x4c, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x44, 0x45, 0x50,
	0x52, 0x45, 0x43, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x4c, 0x59, 0x5f, 0x42,
	0x45, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x48, 0x45, 0x5f, 0x46, 0x4f, 0x4c, 0x44, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x42, 0x45, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x48, 0x45, 0x5f, 0x46, 0x4f, 0x4c,
	0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x4f, 0x4f, 0x54, 0x45, 0x52, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x49, 0x44, 0x45, 0x42, 0x41, 0x52, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x44, 0x5f, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x10, 0x07, 0x22, 0x4d, 0x0a, 0x0c, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x31, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x4d, 0x52, 0x41, 0x49, 0x44, 0x5f, 0x31, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4f,
	0x52, 0x4d, 0x4d, 0x41, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x52, 0x41, 0x49, 0x44, 0x5f,
	0x32, 0x10, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xab, 0x02, 0x0a, 0x04, 0x53,
	0x69, 0x74, 0x65, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a,
	0x03, 0x63, 0x61, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0f,
	0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x12,
	0x0c, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a,
	0x0d, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x12, 0x0b, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0e, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x10, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0e, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x08, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xac, 0x02, 0x0a, 0x03, 0x41, 0x70, 0x70,
	0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x63, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x63, 0x61, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x70,
	0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03,
	0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x12, 0x0c, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x12, 0x2f,
	0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12,
	0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10,
	0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x75, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x49, 0x0a, 0x09, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x0c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b,
	0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x1a, 0xa4, 0x06, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0a,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x65, 0x70,
	0x69, 0x73, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0d, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x73, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x12, 0x3d, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x51, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x12, 0x0a, 0x0a,
	0x75, 0x73, 0x65, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x3a, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x32, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x12, 0x0a, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x12, 0x1a, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x12, 0x2d, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x12,
	0x0b, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x12, 0x41, 0x0a, 0x0e,
	0x71, 0x61, 0x67, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x2e, 0x51, 0x41, 0x47, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x12, 0x0a, 0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x08, 0x12, 0x10, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x22, 0x57, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x13, 0x0a, 0x0f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52,
	0x4f, 0x46, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x50, 0x52, 0x4f, 0x53, 0x55, 0x4d, 0x45, 0x52, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x22, 0x6b,
	0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x47,
	0x41, 0x4d, 0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x55, 0x53, 0x49, 0x43, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x04, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58,
	0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x07, 0x22, 0x45, 0x0a, 0x0e, 0x51,
	0x41, 0x47, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x11, 0x0a,
	0x0d, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x45, 0x56, 0x45, 0x52, 0x59, 0x4f, 0x4e, 0x45, 0x5f, 0x4f, 0x56, 0x45,
	0x52, 0x5f, 0x31, 0x32, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x10, 0x03, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x48, 0x0a, 0x08, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x65, 0x72, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0b, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64,
	0x10, 0x90, 0x4e, 0x1a, 0x96, 0x06, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0b,
	0x0a, 0x03, 0x64, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x12, 0x0a, 0x0a, 0x02, 0x75,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x23, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x12, 0x0f, 0x0a, 0x07, 0x64, 0x69, 0x64, 0x73,
	0x68, 0x61, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06, 0x64, 0x69, 0x64,
	0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x64, 0x70, 0x69,
	0x64, 0x73, 0x68, 0x61, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x64,
	0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04,
	0x69, 0x70, 0x76, 0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0a, 0x0a, 0x02, 0x6f, 0x73,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x68, 0x77, 0x76, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x09, 0x0a, 0x01, 0x77, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x12, 0x09, 0x0a, 0x01, 0x68,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0b, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x05, 0x12, 0x0f, 0x0a, 0x07, 0x70, 0x78, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x01, 0x12, 0x0a, 0x0a, 0x02, 0x6a, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x12, 0x40, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x08,
	0x66, 0x6c, 0x61, 0x73, 0x68, 0x76, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b,
	0x0a, 0x03, 0x69, 0x66, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0f, 0x0a, 0x07, 0x6d,
	0x61, 0x63, 0x73, 0x68, 0x61, 0x31, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0e, 0x0a, 0x06,
	0x6d, 0x61, 0x63, 0x6d, 0x64, 0x35, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03,
	0x6c, 0x6d, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x22, 0x7f, 0x0a, 0x0a, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f,
	0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x56, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x54, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x42, 0x4f, 0x58, 0x10, 0x07, 0x22, 0x79, 0x0a, 0x0e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c,
	0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x32, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43,
	0x45, 0x4c, 0x4c, 0x5f, 0x33, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c,
	0x5f, 0x34, 0x47, 0x10, 0x06, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x8b, 0x02, 0x0a,
	0x03, 0x47, 0x65, 0x6f, 0x12, 0x0b, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x12, 0x0b, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x12, 0x0f,
	0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x0e, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x15, 0x0a, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x66, 0x69, 0x70, 0x73, 0x31, 0x30, 0x34,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x6d, 0x65, 0x74, 0x72, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x7a, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x09, 0x75, 0x74, 0x63, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x22, 0x3b, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x50, 0x53, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x50, 0x10, 0x02,
	0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45,
	0x44, 0x10, 0x03, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xe5, 0x01, 0x0a, 0x04, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12,
	0x10, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x65, 0x72, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x0b, 0x0a, 0x03, 0x79, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0e,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10,
	0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x12, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x12, 0x23, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x29, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x03, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x1a, 0x94, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0a, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x30, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x39, 0x0a, 0x07, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x1c, 0x0a, 0x04, 0x52, 0x65, 0x67,
	0x73, 0x12, 0x0d, 0x0a, 0x05, 0x63, 0x6f, 0x70, 0x70, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0x41, 0x0a, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f,
	0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x45, 0x43, 0x4f, 0x4e,
	0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x49, 0x58,
	0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x03, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90,
	0x4e, 0x42, 0x1b, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0xbc,
	0x06, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0a,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x65,
	0x61, 0x74, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f,
	0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x0d, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x75, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x3a, 0x03, 0x55, 0x53, 0x44, 0x12, 0x12, 0x0a, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x12, 0x2c, 0x0a,
	0x03, 0x6e, 0x62, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x1a, 0xa4, 0x03, 0x0a, 0x07,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x2e, 0x42, 0x69, 0x64, 0x12, 0x0c, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x1a, 0xc6, 0x02, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x02, 0x28, 0x01, 0x12, 0x0c, 0x0a, 0x04, 0x61, 0x64, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x12, 0x0d, 0x0a, 0x03, 0x61, 0x64, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x12,
	0x2c, 0x0a, 0x0a, 0x61, 0x64, 0x6d, 0x5f, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x32, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x12, 0x0f, 0x0a,
	0x07, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x12, 0x0e,
	0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c,
	0x0a, 0x04, 0x69, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03,
	0x63, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0c, 0x0a, 0x04, 0x63, 0x72, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0b, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x09, 0x12, 0x2b, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10,
	0x01, 0x12, 0x0e, 0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x09, 0x0a, 0x01, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x12, 0x09, 0x0a, 0x01,
	0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x42, 0x0b,
	0x0a, 0x09, 0x61, 0x64, 0x6d, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x22, 0xe1, 0x01, 0x0a, 0x0b, 0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x49, 0x43,
	0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x02, 0x12,
	0x14, 0x0a, 0x10, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x50, 0x49,
	0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54,
	0x45, 0x44, 0x5f, 0x4e, 0x4f, 0x4e, 0x48, 0x55, 0x4d, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x46,
	0x46, 0x49, 0x43, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x49,
	0x50, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54,
	0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x42,
	0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x52,
	0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x10, 0x08, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0xb1, 0x09,
	0x0a, 0x0d, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0b, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x2e, 0x0a, 0x06,
	0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x06,
	0x61, 0x64, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x08,
	0x70, 0x6c, 0x63, 0x6d, 0x74, 0x63, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01,
	0x31, 0x12, 0x0e, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01,
	0x30, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x1a, 0xee,
	0x05, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x05, 0x12, 0x17, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x32, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x48,
	0x00, 0x12, 0x30, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x48, 0x00, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x48, 0x00, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x1a, 0x1b, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x0b, 0x0a,
	0x03, 0x6c, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90,
	0x4e, 0x1a, 0xbf, 0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x79, 0x6f, 0x75, 0x64,
	0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x01, 0x77,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x12, 0x09, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x12, 0x0c, 0x0a, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x12,
	0x0c, 0x0a, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x12, 0x0d, 0x0a,
	0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x22, 0x2e, 0x0a, 0x0e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08,
	0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x4f, 0x47, 0x4f,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x03, 0x2a, 0x05, 0x08, 0x64,
	0x10, 0x90, 0x4e, 0x1a, 0x83, 0x02, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x79, 0x6f, 0x75,
	0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x03, 0x6c, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x22, 0xa8, 0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x4f, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x45, 0x53, 0x43,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x09,
	0x0a, 0x05, 0x4c, 0x49, 0x4b, 0x45, 0x53, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x4f, 0x57,
	0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x53, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x52, 0x49, 0x43,
	0x45, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x41, 0x4c, 0x45, 0x50, 0x52, 0x49, 0x43, 0x45,
	0x10, 0x07, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x08, 0x12, 0x0b, 0x0a,
	0x07, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45,
	0x53, 0x43, 0x32, 0x10, 0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59,
	0x55, 0x52, 0x4c, 0x10, 0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x54, 0x41, 0x54, 0x45, 0x58, 0x54,
	0x10, 0x0c, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e,
	0x42, 0x0d, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22,
	0x74, 0x0a, 0x08, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a,
	0x08, 0x41, 0x50, 0x50, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x45, 0x57, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x48,
	0x41, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x52,
	0x4f, 0x55, 0x53, 0x45, 0x4c, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x47,
	0x52, 0x49, 0x44, 0x10, 0x07, 0x22, 0x73, 0x0a, 0x08, 0x41, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x5f, 0x55, 0x4e, 0x49, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x43, 0x4f, 0x4d,
	0x4d, 0x45, 0x4e, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x52, 0x4f, 0x4d, 0x4f, 0x54, 0x45, 0x44, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x41, 0x42, 0x5f,
	0x49, 0x4e, 0x5f, 0x41, 0x44, 0x5f, 0x4e, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90,
	0x4e, 0x22, 0xc9, 0x05, 0x0a, 0x0e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0b, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x12, 0x2c, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x29, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x13, 0x0a, 0x0b, 0x69, 0x6d,
	0x70, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x12,
	0x11, 0x0a, 0x09, 0x6a, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x1a, 0x43, 0x0a, 0x04, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x0b, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x15, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x12, 0x10,
	0x0a, 0x08, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xdc, 0x03, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x0a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x12, 0x17, 0x0a,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a,
	0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x12, 0x31, 0x0a, 0x03, 0x69,
	0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61,
	0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x00, 0x12, 0x33,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x48, 0x00, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f, 0x75, 0x64, 0x61, 0x6f, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e,
	0x6b, 0x1a, 0x1c, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x0c, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a,
	0x31, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0b, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x09, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x12, 0x09, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x1a, 0x2b, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0d, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x12, 0x0d, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a,
	0x1f, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0f, 0x0a, 0x07, 0x76, 0x61, 0x73, 0x74,
	0x74, 0x61, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x42, 0x0d, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0xcb, 0x29,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x41, 0x42, 0x31, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49,
	0x41, 0x42, 0x31, 0x5f, 0x31, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x31, 0x5f,
	0x32, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x31, 0x5f, 0x33, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x31, 0x5f, 0x34, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x49,
	0x41, 0x42, 0x31, 0x5f, 0x35, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x31, 0x5f,
	0x36, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x31, 0x5f, 0x37, 0x10, 0x08, 0x12,
	0x08, 0x0a, 0x04, 0x49, 0x41, 0x42, 0x32, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x32, 0x5f, 0x31, 0x10, 0x0a, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x32, 0x10,
	0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x33, 0x10, 0x0c, 0x12, 0x0a, 0x0a,
	0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x34, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x32, 0x5f, 0x35, 0x10, 0x0e, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x36, 0x10,
	0x0f, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x37, 0x10, 0x10, 0x12, 0x0a, 0x0a,
	0x06, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x38, 0x10, 0x11, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x32, 0x5f, 0x39, 0x10, 0x12, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x30,
	0x10, 0x13, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x31, 0x10, 0x14, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x32, 0x10, 0x15, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x33, 0x10, 0x16, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x32, 0x5f, 0x31, 0x34, 0x10, 0x17, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31,
	0x35, 0x10, 0x18, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x36, 0x10, 0x19,
	0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x37, 0x10, 0x1a, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x31, 0x38, 0x10, 0x1b, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x32, 0x5f, 0x31, 0x39, 0x10, 0x1c, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f,
	0x32, 0x30, 0x10, 0x1d, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x32, 0x31, 0x10,
	0x1e, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x32, 0x32, 0x10, 0x1f, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x5f, 0x32, 0x33, 0x10, 0x20, 0x12, 0x08, 0x0a, 0x04, 0x49,
	0x41, 0x42, 0x33, 0x10, 0x21, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x31, 0x10,
	0x22, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x32, 0x10, 0x23, 0x12, 0x0a, 0x0a,
	0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x33, 0x10, 0x24, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x33, 0x5f, 0x34, 0x10, 0x25, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x35, 0x10,
	0x26, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x36, 0x10, 0x27, 0x12, 0x0a, 0x0a,
	0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x37, 0x10, 0x28, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x33, 0x5f, 0x38, 0x10, 0x29, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x39, 0x10,
	0x2a, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x31, 0x30, 0x10, 0x2b, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x33, 0x5f, 0x31, 0x31, 0x10, 0x2c, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x33, 0x5f, 0x31, 0x32, 0x10, 0x2d, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x41, 0x42, 0x34,
	0x10, 0x2e, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x31, 0x10, 0x2f, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x32, 0x10, 0x30, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x34, 0x5f, 0x33, 0x10, 0x31, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x34,
	0x10, 0x32, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x35, 0x10, 0x33, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x36, 0x10, 0x34, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x34, 0x5f, 0x37, 0x10, 0x35, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x38,
	0x10, 0x36, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x39, 0x10, 0x37, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x34, 0x5f, 0x31, 0x30, 0x10, 0x38, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x34, 0x5f, 0x31, 0x31, 0x10, 0x39, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x41, 0x42, 0x35,
	0x10, 0x3a, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x31, 0x10, 0x3b, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x32, 0x10, 0x3c, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x35, 0x5f, 0x33, 0x10, 0x3d, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x34,
	0x10, 0x3e, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x35, 0x10, 0x3f, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x36, 0x10, 0x40, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x35, 0x5f, 0x37, 0x10, 0x41, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x38,
	0x10, 0x42, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x39, 0x10, 0x43, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x31, 0x30, 0x10, 0x44, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x35, 0x5f, 0x31, 0x31, 0x10, 0x45, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x35,
	0x5f, 0x31, 0x32, 0x10, 0x46, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x31, 0x33,
	0x10, 0x47, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x31, 0x34, 0x10, 0x48, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x35, 0x5f, 0x31, 0x35, 0x10, 0x49, 0x12, 0x08, 0x0a, 0x04,
	0x49, 0x41, 0x42, 0x36, 0x10, 0x4a, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x31,
	0x10, 0x4b, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x32, 0x10, 0x4c, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x33, 0x10, 0x4d, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x36, 0x5f, 0x34, 0x10, 0x4e, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x35,
	0x10, 0x4f, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x36, 0x10, 0x50, 0x12, 0x0a,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x37, 0x10, 0x51, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x36, 0x5f, 0x38, 0x10, 0x52, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x36, 0x5f, 0x39,
	0x10, 0x53, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x41, 0x42, 0x37, 0x10, 0x54, 0x12, 0x0a, 0x0a, 0x06,
	0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x10, 0x55, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37,
	0x5f, 0x32, 0x10, 0x56, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x10, 0x57,
	0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x34, 0x10, 0x58, 0x12, 0x0a, 0x0a, 0x06,
	0x49, 0x41, 0x42, 0x37, 0x5f, 0x35, 0x10, 0x59, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37,
	0x5f, 0x36, 0x10, 0x5a, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x37, 0x10, 0x5b,
	0x12, 0x0a, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x38, 0x10, 0x5c, 0x12, 0x0a, 0x0a, 0x06,
	0x49, 0x41, 0x42, 0x37, 0x5f, 0x39, 0x10, 0x5d, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37,
	0x5f, 0x31, 0x30, 0x10, 0x5e, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x31,
	0x10, 0x5f, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x32, 0x10, 0x60, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x33, 0x10, 0x61, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x34, 0x10, 0x62, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x37, 0x5f, 0x31, 0x35, 0x10, 0x63, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31,
	0x36, 0x10, 0x64, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x37, 0x10, 0x65,
	0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x38, 0x10, 0x66, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x31, 0x39, 0x10, 0x67, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x37, 0x5f, 0x32, 0x30, 0x10, 0x68, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f,
	0x32, 0x31, 0x10, 0x69, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x32, 0x10,
	0x6a, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x33, 0x10, 0x6b, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x34, 0x10, 0x6c, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x37, 0x5f, 0x32, 0x35, 0x10, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37,
	0x5f, 0x32, 0x36, 0x10, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x37,
	0x10, 0x6f, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x38, 0x10, 0x70, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x32, 0x39, 0x10, 0x71, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x30, 0x10, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x37, 0x5f, 0x33, 0x31, 0x10, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33,
	0x32, 0x10, 0x74, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x33, 0x10, 0x75,
	0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x34, 0x10, 0x76, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x35, 0x10, 0x77, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x37, 0x5f, 0x33, 0x36, 0x10, 0x78, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f,
	0x33, 0x37, 0x10, 0x79, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x38, 0x10,
	0x7a, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x33, 0x39, 0x10, 0x7b, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x34, 0x30, 0x10, 0x7c, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x37, 0x5f, 0x34, 0x31, 0x10, 0x7d, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37,
	0x5f, 0x34, 0x32, 0x10, 0x7e, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x34, 0x33,
	0x10, 0x7f, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x34, 0x34, 0x10, 0x80, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x37, 0x5f, 0x34, 0x35, 0x10, 0x81, 0x01, 0x12, 0x09,
	0x0a, 0x04, 0x49, 0x41, 0x42, 0x38, 0x10, 0x82, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42,
	0x38, 0x5f, 0x31, 0x10, 0x83, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x32,
	0x10, 0x84, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x33, 0x10, 0x85, 0x01,
	0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x34, 0x10, 0x86, 0x01, 0x12, 0x0b, 0x0a,
	0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x35, 0x10, 0x87, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41,
	0x42, 0x38, 0x5f, 0x36, 0x10, 0x88, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f,
	0x37, 0x10, 0x89, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x38, 0x10, 0x8a,
	0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x39, 0x10, 0x8b, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x30, 0x10, 0x8c, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x31, 0x10, 0x8d, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x38, 0x5f, 0x31, 0x32, 0x10, 0x8e, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x38,
	0x5f, 0x31, 0x33, 0x10, 0x8f, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31,
	0x34, 0x10, 0x90, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x35, 0x10,
	0x91, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x36, 0x10, 0x92, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x37, 0x10, 0x93, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x38, 0x5f, 0x31, 0x38, 0x10, 0x94, 0x01, 0x12, 0x09, 0x0a, 0x04,
	0x49, 0x41, 0x42, 0x39, 0x10, 0x95, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f,
	0x31, 0x10, 0x96, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x10, 0x97,
	0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x33, 0x10, 0x98, 0x01, 0x12, 0x0b,
	0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x34, 0x10, 0x99, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49,
	0x41, 0x42, 0x39, 0x5f, 0x35, 0x10, 0x9a, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39,
	0x5f, 0x36, 0x10, 0x9b, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x37, 0x10,
	0x9c, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x38, 0x10, 0x9d, 0x01, 0x12,
	0x0b, 0x0a, 0x06, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x39, 0x10, 0x9e, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x30, 0x10, 0x9f, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x39, 0x5f, 0x31, 0x31, 0x10, 0xa0, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39,
	0x5f, 0x31, 0x32, 0x10, 0xa1, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x31,
	0x33, 0x10, 0xa2, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x34, 0x10,
	0xa3, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x35, 0x10, 0xa4, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x36, 0x10, 0xa5, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x37, 0x10, 0xa6, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x39, 0x5f, 0x31, 0x38, 0x10, 0xa7, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x39, 0x5f, 0x31, 0x39, 0x10, 0xa8, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39,
	0x5f, 0x32, 0x30, 0x10, 0xa9, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32,
	0x31, 0x10, 0xaa, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x32, 0x10,
	0xab, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x33, 0x10, 0xac, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x34, 0x10, 0xad, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x35, 0x10, 0xae, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x39, 0x5f, 0x32, 0x36, 0x10, 0xaf, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x39, 0x5f, 0x32, 0x37, 0x10, 0xb0, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39,
	0x5f, 0x32, 0x38, 0x10, 0xb1, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x32,
	0x39, 0x10, 0xb2, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x33, 0x30, 0x10,
	0xb3, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x39, 0x5f, 0x33, 0x31, 0x10, 0xb4, 0x01,
	0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x30, 0x10, 0xb5, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x31, 0x10, 0xb6, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x31, 0x30, 0x5f, 0x32, 0x10, 0xb7, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x30, 0x5f, 0x33, 0x10, 0xb8, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x30, 0x5f,
	0x34, 0x10, 0xb9, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x35, 0x10,
	0xba, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x36, 0x10, 0xbb, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x37, 0x10, 0xbc, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x38, 0x10, 0xbd, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x31, 0x30, 0x5f, 0x39, 0x10, 0xbe, 0x01, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41,
	0x42, 0x31, 0x31, 0x10, 0xbf, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x31, 0x5f,
	0x31, 0x10, 0xc0, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x31, 0x5f, 0x32, 0x10,
	0xc1, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x31, 0x5f, 0x33, 0x10, 0xc2, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x31, 0x5f, 0x34, 0x10, 0xc3, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x31, 0x5f, 0x35, 0x10, 0xc4, 0x01, 0x12, 0x0a, 0x0a, 0x05,
	0x49, 0x41, 0x42, 0x31, 0x32, 0x10, 0xc5, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x32, 0x5f, 0x31, 0x10, 0xc6, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x32, 0x5f,
	0x32, 0x10, 0xc7, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x32, 0x5f, 0x33, 0x10,
	0xc8, 0x01, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x33, 0x10, 0xc9, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x31, 0x10, 0xca, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x32, 0x10, 0xcb, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x31, 0x33, 0x5f, 0x33, 0x10, 0xcc, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x33, 0x5f, 0x34, 0x10, 0xcd, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f,
	0x35, 0x10, 0xce, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x36, 0x10,
	0xcf, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x37, 0x10, 0xd0, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x38, 0x10, 0xd1, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x39, 0x10, 0xd2, 0x01, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x31, 0x33, 0x5f, 0x31, 0x30, 0x10, 0xd3, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x31, 0x33, 0x5f, 0x31, 0x31, 0x10, 0xd4, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x31, 0x33, 0x5f, 0x31, 0x32, 0x10, 0xd5, 0x01, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42,
	0x31, 0x34, 0x10, 0xd6, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x34, 0x5f, 0x31,
	0x10, 0xd7, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x34, 0x5f, 0x32, 0x10, 0xd8,
	0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x34, 0x5f, 0x33, 0x10, 0xd9, 0x01, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x34, 0x5f, 0x34, 0x10, 0xda, 0x01, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x31, 0x34, 0x5f, 0x35, 0x10, 0xdb, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x31, 0x34, 0x5f, 0x36, 0x10, 0xdc, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x31, 0x34, 0x5f, 0x37, 0x10, 0xdd, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x34,
	0x5f, 0x38, 0x10, 0xde, 0x01, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x35, 0x10, 0xdf,
	0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x31, 0x10, 0xe0, 0x01, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x32, 0x10, 0xe1, 0x01, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x33, 0x10, 0xe2, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x31, 0x35, 0x5f, 0x34, 0x10, 0xe3, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x31, 0x35, 0x5f, 0x35, 0x10, 0xe4, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35,
	0x5f, 0x36, 0x10, 0xe5, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x37,
	0x10, 0xe6, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x38, 0x10, 0xe7,
	0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x39, 0x10, 0xe8, 0x01, 0x12,
	0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x35, 0x5f, 0x31, 0x30, 0x10, 0xe9, 0x01, 0x12, 0x0a,
	0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x36, 0x10, 0xea, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x31, 0x36, 0x5f, 0x31, 0x10, 0xeb, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x36, 0x5f, 0x32, 0x10, 0xec, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x36, 0x5f,
	0x33, 0x10, 0xed, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x36, 0x5f, 0x34, 0x10,
	0xee, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x36, 0x5f, 0x35, 0x10, 0xef, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x36, 0x5f, 0x36, 0x10, 0xf0, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x36, 0x5f, 0x37, 0x10, 0xf1, 0x01, 0x12, 0x0a, 0x0a, 0x05,
	0x49, 0x41, 0x42, 0x31, 0x37, 0x10, 0xf2, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x37, 0x5f, 0x31, 0x10, 0xf3, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f,
	0x32, 0x10, 0xf4, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x10,
	0xf5, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x34, 0x10, 0xf6, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x35, 0x10, 0xf7, 0x01, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x36, 0x10, 0xf8, 0x01, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x37, 0x10, 0xf9, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x31, 0x37, 0x5f, 0x38, 0x10, 0xfa, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31,
	0x37, 0x5f, 0x39, 0x10, 0xfb, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f,
	0x31, 0x30, 0x10, 0xfc, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31,
	0x31, 0x10, 0xfd, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x32,
	0x10, 0xfe, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x33, 0x10,
	0xff, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x34, 0x10, 0x80,
	0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x35, 0x10, 0x81, 0x02,
	0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x36, 0x10, 0x82, 0x02, 0x12,
	0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x37, 0x10, 0x83, 0x02, 0x12, 0x0d,
	0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x38, 0x10, 0x84, 0x02, 0x12, 0x0d, 0x0a,
	0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x31, 0x39, 0x10, 0x85, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x32, 0x30, 0x10, 0x86, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x31, 0x37, 0x5f, 0x32, 0x31, 0x10, 0x87, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x31, 0x37, 0x5f, 0x32, 0x32, 0x10, 0x88, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42,
	0x31, 0x37, 0x5f, 0x32, 0x33, 0x10, 0x89, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31,
	0x37, 0x5f, 0x32, 0x34, 0x10, 0x8a, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37,
	0x5f, 0x32, 0x35, 0x10, 0x8b, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f,
	0x32, 0x36, 0x10, 0x8c, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x32,
	0x37, 0x10, 0x8d, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x32, 0x38,
	0x10, 0x8e, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x32, 0x39, 0x10,
	0x8f, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x30, 0x10, 0x90,
	0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x31, 0x10, 0x91, 0x02,
	0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x32, 0x10, 0x92, 0x02, 0x12,
	0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x33, 0x10, 0x93, 0x02, 0x12, 0x0d,
	0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x34, 0x10, 0x94, 0x02, 0x12, 0x0d, 0x0a,
	0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x35, 0x10, 0x95, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x36, 0x10, 0x96, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x31, 0x37, 0x5f, 0x33, 0x37, 0x10, 0x97, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x31, 0x37, 0x5f, 0x33, 0x38, 0x10, 0x98, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42,
	0x31, 0x37, 0x5f, 0x33, 0x39, 0x10, 0x99, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31,
	0x37, 0x5f, 0x34, 0x30, 0x10, 0x9a, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37,
	0x5f, 0x34, 0x31, 0x10, 0x9b, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f,
	0x34, 0x32, 0x10, 0x9c, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x34,
	0x33, 0x10, 0x9d, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x37, 0x5f, 0x34, 0x34,
	0x10, 0x9e, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x38, 0x10, 0x9f, 0x02, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x38, 0x5f, 0x31, 0x10, 0xa0, 0x02, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x31, 0x38, 0x5f, 0x32, 0x10, 0xa1, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x31, 0x38, 0x5f, 0x33, 0x10, 0xa2, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x31, 0x38, 0x5f, 0x34, 0x10, 0xa3, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x38,
	0x5f, 0x35, 0x10, 0xa4, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x38, 0x5f, 0x36,
	0x10, 0xa5, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x31, 0x39, 0x10, 0xa6, 0x02, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x31, 0x10, 0xa7, 0x02, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x10, 0xa8, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x31, 0x39, 0x5f, 0x33, 0x10, 0xa9, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x31, 0x39, 0x5f, 0x34, 0x10, 0xaa, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39,
	0x5f, 0x35, 0x10, 0xab, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x36,
	0x10, 0xac, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x37, 0x10, 0xad,
	0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x38, 0x10, 0xae, 0x02, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x39, 0x10, 0xaf, 0x02, 0x12, 0x0d, 0x0a,
	0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x31, 0x30, 0x10, 0xb0, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x31, 0x31, 0x10, 0xb1, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x31, 0x39, 0x5f, 0x31, 0x32, 0x10, 0xb2, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x31, 0x39, 0x5f, 0x31, 0x33, 0x10, 0xb3, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42,
	0x31, 0x39, 0x5f, 0x31, 0x34, 0x10, 0xb4, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31,
	0x39, 0x5f, 0x31, 0x35, 0x10, 0xb5, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39,
	0x5f, 0x31, 0x36, 0x10, 0xb6, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f,
	0x31, 0x37, 0x10, 0xb7, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x31,
	0x38, 0x10, 0xb8, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x31, 0x39,
	0x10, 0xb9, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x30, 0x10,
	0xba, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x31, 0x10, 0xbb,
	0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x32, 0x10, 0xbc, 0x02,
	0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x33, 0x10, 0xbd, 0x02, 0x12,
	0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x34, 0x10, 0xbe, 0x02, 0x12, 0x0d,
	0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x35, 0x10, 0xbf, 0x02, 0x12, 0x0d, 0x0a,
	0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x36, 0x10, 0xc0, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x37, 0x10, 0xc1, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x31, 0x39, 0x5f, 0x32, 0x38, 0x10, 0xc2, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x31, 0x39, 0x5f, 0x32, 0x39, 0x10, 0xc3, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42,
	0x31, 0x39, 0x5f, 0x33, 0x30, 0x10, 0xc4, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31,
	0x39, 0x5f, 0x33, 0x31, 0x10, 0xc5, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39,
	0x5f, 0x33, 0x32, 0x10, 0xc6, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f,
	0x33, 0x33, 0x10, 0xc7, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x33,
	0x34, 0x10, 0xc8, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x33, 0x35,
	0x10, 0xc9, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x31, 0x39, 0x5f, 0x33, 0x36, 0x10,
	0xca, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x32, 0x30, 0x10, 0xcb, 0x02, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x31, 0x10, 0xcc, 0x02, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x10, 0xcd, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x32, 0x30, 0x5f, 0x33, 0x10, 0xce, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32,
	0x30, 0x5f, 0x34, 0x10, 0xcf, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f,
	0x35, 0x10, 0xd0, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x36, 0x10,
	0xd1, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x37, 0x10, 0xd2, 0x02,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x38, 0x10, 0xd3, 0x02, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x39, 0x10, 0xd4, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x31, 0x30, 0x10, 0xd5, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x32, 0x30, 0x5f, 0x31, 0x31, 0x10, 0xd6, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41,
	0x42, 0x32, 0x30, 0x5f, 0x31, 0x32, 0x10, 0xd7, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42,
	0x32, 0x30, 0x5f, 0x31, 0x33, 0x10, 0xd8, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32,
	0x30, 0x5f, 0x31, 0x34, 0x10, 0xd9, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30,
	0x5f, 0x31, 0x35, 0x10, 0xda, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f,
	0x31, 0x36, 0x10, 0xdb, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x31,
	0x37, 0x10, 0xdc, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x31, 0x38,
	0x10, 0xdd, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x31, 0x39, 0x10,
	0xde, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x30, 0x10, 0xdf,
	0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x31, 0x10, 0xe0, 0x02,
	0x12, 0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x32, 0x10, 0xe1, 0x02, 0x12,
	0x0d, 0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x33, 0x10, 0xe2, 0x02, 0x12, 0x0d,
	0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x34, 0x10, 0xe3, 0x02, 0x12, 0x0d, 0x0a,
	0x08, 0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x35, 0x10, 0xe4, 0x02, 0x12, 0x0d, 0x0a, 0x08,
	0x49, 0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x36, 0x10, 0xe5, 0x02, 0x12, 0x0d, 0x0a, 0x08, 0x49,
	0x41, 0x42, 0x32, 0x30, 0x5f, 0x32, 0x37, 0x10, 0xe6, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41,
	0x42, 0x32, 0x31, 0x10, 0xe7, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x31, 0x5f,
	0x31, 0x10, 0xe8, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x31, 0x5f, 0x32, 0x10,
	0xe9, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x31, 0x5f, 0x33, 0x10, 0xea, 0x02,
	0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x32, 0x32, 0x10, 0xeb, 0x02, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x32, 0x32, 0x5f, 0x31, 0x10, 0xec, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x32, 0x32, 0x5f, 0x32, 0x10, 0xed, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32,
	0x32, 0x5f, 0x33, 0x10, 0xee, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x32, 0x5f,
	0x34, 0x10, 0xef, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x32, 0x33, 0x10, 0xf0, 0x02,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x31, 0x10, 0xf1, 0x02, 0x12, 0x0c,
	0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x32, 0x10, 0xf2, 0x02, 0x12, 0x0c, 0x0a, 0x07,
	0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x33, 0x10, 0xf3, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41,
	0x42, 0x32, 0x33, 0x5f, 0x34, 0x10, 0xf4, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32,
	0x33, 0x5f, 0x35, 0x10, 0xf5, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f,
	0x36, 0x10, 0xf6, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x37, 0x10,
	0xf7, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x38, 0x10, 0xf8, 0x02,
	0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x39, 0x10, 0xf9, 0x02, 0x12, 0x0d,
	0x0a, 0x08, 0x49, 0x41, 0x42, 0x32, 0x33, 0x5f, 0x31, 0x30, 0x10, 0xfa, 0x02, 0x12, 0x0a, 0x0a,
	0x05, 0x49, 0x41, 0x42, 0x32, 0x34, 0x10, 0xfb, 0x02, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42,
	0x32, 0x35, 0x10, 0xfc, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x35, 0x5f, 0x31,
	0x10, 0xfd, 0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x35, 0x5f, 0x32, 0x10, 0xfe,
	0x02, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x35, 0x5f, 0x33, 0x10, 0xff, 0x02, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x35, 0x5f, 0x34, 0x10, 0x80, 0x03, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x32, 0x35, 0x5f, 0x35, 0x10, 0x81, 0x03, 0x12, 0x0c, 0x0a, 0x07, 0x49,
	0x41, 0x42, 0x32, 0x35, 0x5f, 0x36, 0x10, 0x82, 0x03, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42,
	0x32, 0x35, 0x5f, 0x37, 0x10, 0x83, 0x03, 0x12, 0x0a, 0x0a, 0x05, 0x49, 0x41, 0x42, 0x32, 0x36,
	0x10, 0x84, 0x03, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x36, 0x5f, 0x31, 0x10, 0x85,
	0x03, 0x12, 0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x36, 0x5f, 0x32, 0x10, 0x86, 0x03, 0x12,
	0x0c, 0x0a, 0x07, 0x49, 0x41, 0x42, 0x32, 0x36, 0x5f, 0x33, 0x10, 0x87, 0x03, 0x12, 0x0c, 0x0a,
	0x07, 0x49, 0x41, 0x42, 0x32, 0x36, 0x5f, 0x34, 0x10, 0x88, 0x03, 0x2a, 0xa1, 0x03, 0x0a, 0x11,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x18, 0x0a, 0x14, 0x45, 0x58, 0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x58,
	0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58,
	0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x56, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1d, 0x0a,
	0x19, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e,
	0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x07,
	0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f, 0x50, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f,
	0x56, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x47, 0x47,
	0x45, 0x53, 0x54, 0x49, 0x56, 0x45, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x4e, 0x4f,
	0x59, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59,
	0x53, 0x10, 0x0b, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x4f, 0x4e, 0x4c, 0x59,
	0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x0d, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x49, 0x4e, 0x44,
	0x4f, 0x57, 0x53, 0x5f, 0x44, 0x49, 0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x4c,
	0x45, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17, 0x48,
	0x41, 0x53, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x5f, 0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x42, 0x55, 0x54, 0x54, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x44, 0x5f, 0x43,
	0x41, 0x4e, 0x5f, 0x42, 0x45, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x10, 0x42,
	0x14, 0x42, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x74, 0x62, 0x5a, 0x09, 0x2e, 0x2e, 0x2f, 0x79,
	0x6f, 0x75, 0x64, 0x61, 0x6f,
}

var (
	file_youdao_proto_rawDescOnce sync.Once
	file_youdao_proto_rawDescData = file_youdao_proto_rawDesc
)

func file_youdao_proto_rawDescGZIP() []byte {
	file_youdao_proto_rawDescOnce.Do(func() {
		file_youdao_proto_rawDescData = protoimpl.X.CompressGZIP(file_youdao_proto_rawDescData)
	})
	return file_youdao_proto_rawDescData
}

var file_youdao_proto_enumTypes = make([]protoimpl.EnumInfo, 25)
var file_youdao_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_youdao_proto_goTypes = []interface{}{
	(ContentCategory)(0),                               // 0: youdao.ContentCategory
	(CreativeAttribute)(0),                             // 1: youdao.CreativeAttribute
	(BidRequest_AuctionType)(0),                        // 2: youdao.BidRequest.AuctionType
	(BidRequest_Imp_AdPosition)(0),                     // 3: youdao.BidRequest.Imp.AdPosition
	(BidRequest_Imp_APIFramework)(0),                   // 4: youdao.BidRequest.Imp.APIFramework
	(BidRequest_Imp_Banner_BannerAdType)(0),            // 5: youdao.BidRequest.Imp.Banner.BannerAdType
	(BidRequest_Imp_Banner_ExpandableDirection)(0),     // 6: youdao.BidRequest.Imp.Banner.ExpandableDirection
	(BidRequest_Imp_Video_VideoLinearity)(0),           // 7: youdao.BidRequest.Imp.Video.VideoLinearity
	(BidRequest_Imp_Video_VideoBidResponseProtocol)(0), // 8: youdao.BidRequest.Imp.Video.VideoBidResponseProtocol
	(BidRequest_Imp_Video_VideoPlaybackMethod)(0),      // 9: youdao.BidRequest.Imp.Video.VideoPlaybackMethod
	(BidRequest_Imp_Video_VideoStartDelay)(0),          // 10: youdao.BidRequest.Imp.Video.VideoStartDelay
	(BidRequest_Imp_Video_VASTCompanionType)(0),        // 11: youdao.BidRequest.Imp.Video.VASTCompanionType
	(BidRequest_Imp_Video_ContentDeliveryMethod)(0),    // 12: youdao.BidRequest.Imp.Video.ContentDeliveryMethod
	(BidRequest_Content_VideoQuality)(0),               // 13: youdao.BidRequest.Content.VideoQuality
	(BidRequest_Content_ContentContext)(0),             // 14: youdao.BidRequest.Content.ContentContext
	(BidRequest_Content_QAGMediaRating)(0),             // 15: youdao.BidRequest.Content.QAGMediaRating
	(BidRequest_Device_DeviceType)(0),                  // 16: youdao.BidRequest.Device.DeviceType
	(BidRequest_Device_ConnectionType)(0),              // 17: youdao.BidRequest.Device.ConnectionType
	(BidRequest_Geo_LocationType)(0),                   // 18: youdao.BidRequest.Geo.LocationType
	(BidRequest_User_Gender)(0),                        // 19: youdao.BidRequest.User.Gender
	(BidResponse_NoBidReason)(0),                       // 20: youdao.BidResponse.NoBidReason
	(NativeRequest_LayoutId)(0),                        // 21: youdao.NativeRequest.LayoutId
	(NativeRequest_AdUnitId)(0),                        // 22: youdao.NativeRequest.AdUnitId
	(NativeRequest_Asset_Image_ImageAssetType)(0),      // 23: youdao.NativeRequest.Asset.Image.ImageAssetType
	(NativeRequest_Asset_Data_DataAssetType)(0),        // 24: youdao.NativeRequest.Asset.Data.DataAssetType
	(*BidRequest)(nil),                                 // 25: youdao.BidRequest
	(*BidResponse)(nil),                                // 26: youdao.BidResponse
	(*NativeRequest)(nil),                              // 27: youdao.NativeRequest
	(*NativeResponse)(nil),                             // 28: youdao.NativeResponse
	(*BidRequest_Imp)(nil),                             // 29: youdao.BidRequest.Imp
	(*BidRequest_Site)(nil),                            // 30: youdao.BidRequest.Site
	(*BidRequest_App)(nil),                             // 31: youdao.BidRequest.App
	(*BidRequest_Publisher)(nil),                       // 32: youdao.BidRequest.Publisher
	(*BidRequest_Content)(nil),                         // 33: youdao.BidRequest.Content
	(*BidRequest_Producer)(nil),                        // 34: youdao.BidRequest.Producer
	(*BidRequest_Device)(nil),                          // 35: youdao.BidRequest.Device
	(*BidRequest_Geo)(nil),                             // 36: youdao.BidRequest.Geo
	(*BidRequest_User)(nil),                            // 37: youdao.BidRequest.User
	(*BidRequest_Data)(nil),                            // 38: youdao.BidRequest.Data
	(*BidRequest_Regs)(nil),                            // 39: youdao.BidRequest.Regs
	(*BidRequest_Imp_Banner)(nil),                      // 40: youdao.BidRequest.Imp.Banner
	(*BidRequest_Imp_Video)(nil),                       // 41: youdao.BidRequest.Imp.Video
	(*BidRequest_Imp_Native)(nil),                      // 42: youdao.BidRequest.Imp.Native
	(*BidRequest_Imp_Pmp)(nil),                         // 43: youdao.BidRequest.Imp.Pmp
	(*BidRequest_Imp_Video_CompanionAd)(nil),           // 44: youdao.BidRequest.Imp.Video.CompanionAd
	(*BidRequest_Imp_Pmp_Deal)(nil),                    // 45: youdao.BidRequest.Imp.Pmp.Deal
	(*BidRequest_Data_Segment)(nil),                    // 46: youdao.BidRequest.Data.Segment
	(*BidResponse_SeatBid)(nil),                        // 47: youdao.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),                    // 48: youdao.BidResponse.SeatBid.Bid
	(*NativeRequest_Asset)(nil),                        // 49: youdao.NativeRequest.Asset
	(*NativeRequest_Asset_Title)(nil),                  // 50: youdao.NativeRequest.Asset.Title
	(*NativeRequest_Asset_Image)(nil),                  // 51: youdao.NativeRequest.Asset.Image
	(*NativeRequest_Asset_Data)(nil),                   // 52: youdao.NativeRequest.Asset.Data
	(*NativeResponse_Link)(nil),                        // 53: youdao.NativeResponse.Link
	(*NativeResponse_Asset)(nil),                       // 54: youdao.NativeResponse.Asset
	(*NativeResponse_Asset_Title)(nil),                 // 55: youdao.NativeResponse.Asset.Title
	(*NativeResponse_Asset_Image)(nil),                 // 56: youdao.NativeResponse.Asset.Image
	(*NativeResponse_Asset_Data)(nil),                  // 57: youdao.NativeResponse.Asset.Data
	(*NativeResponse_Asset_Video)(nil),                 // 58: youdao.NativeResponse.Asset.Video
}
var file_youdao_proto_depIdxs = []int32{
	29, // 0: youdao.BidRequest.imp:type_name -> youdao.BidRequest.Imp
	30, // 1: youdao.BidRequest.site:type_name -> youdao.BidRequest.Site
	31, // 2: youdao.BidRequest.app:type_name -> youdao.BidRequest.App
	35, // 3: youdao.BidRequest.device:type_name -> youdao.BidRequest.Device
	39, // 4: youdao.BidRequest.regs:type_name -> youdao.BidRequest.Regs
	37, // 5: youdao.BidRequest.user:type_name -> youdao.BidRequest.User
	2,  // 6: youdao.BidRequest.at:type_name -> youdao.BidRequest.AuctionType
	47, // 7: youdao.BidResponse.seatbid:type_name -> youdao.BidResponse.SeatBid
	20, // 8: youdao.BidResponse.nbr:type_name -> youdao.BidResponse.NoBidReason
	21, // 9: youdao.NativeRequest.layout:type_name -> youdao.NativeRequest.LayoutId
	22, // 10: youdao.NativeRequest.adunit:type_name -> youdao.NativeRequest.AdUnitId
	49, // 11: youdao.NativeRequest.assets:type_name -> youdao.NativeRequest.Asset
	54, // 12: youdao.NativeResponse.assets:type_name -> youdao.NativeResponse.Asset
	53, // 13: youdao.NativeResponse.link:type_name -> youdao.NativeResponse.Link
	40, // 14: youdao.BidRequest.Imp.banner:type_name -> youdao.BidRequest.Imp.Banner
	41, // 15: youdao.BidRequest.Imp.video:type_name -> youdao.BidRequest.Imp.Video
	43, // 16: youdao.BidRequest.Imp.pmp:type_name -> youdao.BidRequest.Imp.Pmp
	42, // 17: youdao.BidRequest.Imp.native:type_name -> youdao.BidRequest.Imp.Native
	32, // 18: youdao.BidRequest.Site.publisher:type_name -> youdao.BidRequest.Publisher
	33, // 19: youdao.BidRequest.Site.content:type_name -> youdao.BidRequest.Content
	32, // 20: youdao.BidRequest.App.publisher:type_name -> youdao.BidRequest.Publisher
	33, // 21: youdao.BidRequest.App.content:type_name -> youdao.BidRequest.Content
	13, // 22: youdao.BidRequest.Content.videoquality:type_name -> youdao.BidRequest.Content.VideoQuality
	14, // 23: youdao.BidRequest.Content.context:type_name -> youdao.BidRequest.Content.ContentContext
	34, // 24: youdao.BidRequest.Content.producer:type_name -> youdao.BidRequest.Producer
	15, // 25: youdao.BidRequest.Content.qagmediarating:type_name -> youdao.BidRequest.Content.QAGMediaRating
	36, // 26: youdao.BidRequest.Device.geo:type_name -> youdao.BidRequest.Geo
	17, // 27: youdao.BidRequest.Device.connectiontype:type_name -> youdao.BidRequest.Device.ConnectionType
	16, // 28: youdao.BidRequest.Device.devicetype:type_name -> youdao.BidRequest.Device.DeviceType
	18, // 29: youdao.BidRequest.Geo.type:type_name -> youdao.BidRequest.Geo.LocationType
	36, // 30: youdao.BidRequest.User.geo:type_name -> youdao.BidRequest.Geo
	38, // 31: youdao.BidRequest.User.data:type_name -> youdao.BidRequest.Data
	46, // 32: youdao.BidRequest.Data.segment:type_name -> youdao.BidRequest.Data.Segment
	3,  // 33: youdao.BidRequest.Imp.Banner.pos:type_name -> youdao.BidRequest.Imp.AdPosition
	5,  // 34: youdao.BidRequest.Imp.Banner.btype:type_name -> youdao.BidRequest.Imp.Banner.BannerAdType
	1,  // 35: youdao.BidRequest.Imp.Banner.battr:type_name -> youdao.CreativeAttribute
	6,  // 36: youdao.BidRequest.Imp.Banner.expdir:type_name -> youdao.BidRequest.Imp.Banner.ExpandableDirection
	4,  // 37: youdao.BidRequest.Imp.Banner.api:type_name -> youdao.BidRequest.Imp.APIFramework
	7,  // 38: youdao.BidRequest.Imp.Video.linearity:type_name -> youdao.BidRequest.Imp.Video.VideoLinearity
	8,  // 39: youdao.BidRequest.Imp.Video.protocol:type_name -> youdao.BidRequest.Imp.Video.VideoBidResponseProtocol
	8,  // 40: youdao.BidRequest.Imp.Video.protocols:type_name -> youdao.BidRequest.Imp.Video.VideoBidResponseProtocol
	1,  // 41: youdao.BidRequest.Imp.Video.battr:type_name -> youdao.CreativeAttribute
	9,  // 42: youdao.BidRequest.Imp.Video.playbackmethod:type_name -> youdao.BidRequest.Imp.Video.VideoPlaybackMethod
	12, // 43: youdao.BidRequest.Imp.Video.delivery:type_name -> youdao.BidRequest.Imp.Video.ContentDeliveryMethod
	3,  // 44: youdao.BidRequest.Imp.Video.pos:type_name -> youdao.BidRequest.Imp.AdPosition
	40, // 45: youdao.BidRequest.Imp.Video.companionad:type_name -> youdao.BidRequest.Imp.Banner
	44, // 46: youdao.BidRequest.Imp.Video.companionad_21:type_name -> youdao.BidRequest.Imp.Video.CompanionAd
	4,  // 47: youdao.BidRequest.Imp.Video.api:type_name -> youdao.BidRequest.Imp.APIFramework
	11, // 48: youdao.BidRequest.Imp.Video.companiontype:type_name -> youdao.BidRequest.Imp.Video.VASTCompanionType
	27, // 49: youdao.BidRequest.Imp.Native.request_native:type_name -> youdao.NativeRequest
	4,  // 50: youdao.BidRequest.Imp.Native.api:type_name -> youdao.BidRequest.Imp.APIFramework
	1,  // 51: youdao.BidRequest.Imp.Native.battr:type_name -> youdao.CreativeAttribute
	45, // 52: youdao.BidRequest.Imp.Pmp.deals:type_name -> youdao.BidRequest.Imp.Pmp.Deal
	40, // 53: youdao.BidRequest.Imp.Video.CompanionAd.banner:type_name -> youdao.BidRequest.Imp.Banner
	2,  // 54: youdao.BidRequest.Imp.Pmp.Deal.at:type_name -> youdao.BidRequest.AuctionType
	48, // 55: youdao.BidResponse.SeatBid.bid:type_name -> youdao.BidResponse.SeatBid.Bid
	28, // 56: youdao.BidResponse.SeatBid.Bid.adm_native:type_name -> youdao.NativeResponse
	1,  // 57: youdao.BidResponse.SeatBid.Bid.attr:type_name -> youdao.CreativeAttribute
	50, // 58: youdao.NativeRequest.Asset.title:type_name -> youdao.NativeRequest.Asset.Title
	51, // 59: youdao.NativeRequest.Asset.img:type_name -> youdao.NativeRequest.Asset.Image
	41, // 60: youdao.NativeRequest.Asset.video:type_name -> youdao.BidRequest.Imp.Video
	52, // 61: youdao.NativeRequest.Asset.data:type_name -> youdao.NativeRequest.Asset.Data
	23, // 62: youdao.NativeRequest.Asset.Image.type:type_name -> youdao.NativeRequest.Asset.Image.ImageAssetType
	24, // 63: youdao.NativeRequest.Asset.Data.type:type_name -> youdao.NativeRequest.Asset.Data.DataAssetType
	55, // 64: youdao.NativeResponse.Asset.title:type_name -> youdao.NativeResponse.Asset.Title
	56, // 65: youdao.NativeResponse.Asset.img:type_name -> youdao.NativeResponse.Asset.Image
	58, // 66: youdao.NativeResponse.Asset.video:type_name -> youdao.NativeResponse.Asset.Video
	57, // 67: youdao.NativeResponse.Asset.data:type_name -> youdao.NativeResponse.Asset.Data
	53, // 68: youdao.NativeResponse.Asset.link:type_name -> youdao.NativeResponse.Link
	69, // [69:69] is the sub-list for method output_type
	69, // [69:69] is the sub-list for method input_type
	69, // [69:69] is the sub-list for extension type_name
	69, // [69:69] is the sub-list for extension extendee
	0,  // [0:69] is the sub-list for field type_name
}

func init() { file_youdao_proto_init() }
func file_youdao_proto_init() {
	if File_youdao_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_youdao_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Publisher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Producer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Regs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video_CompanionAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Data_Segment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Link); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_youdao_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
	}
	file_youdao_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*BidRequest_Site_)(nil),
		(*BidRequest_App_)(nil),
	}
	file_youdao_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*BidRequest_Imp_Native_Request)(nil),
		(*BidRequest_Imp_Native_RequestNative)(nil),
	}
	file_youdao_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*BidResponse_SeatBid_Bid_Adm)(nil),
		(*BidResponse_SeatBid_Bid_AdmNative)(nil),
	}
	file_youdao_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*NativeRequest_Asset_Title_)(nil),
		(*NativeRequest_Asset_Img)(nil),
		(*NativeRequest_Asset_Video)(nil),
		(*NativeRequest_Asset_Data_)(nil),
	}
	file_youdao_proto_msgTypes[29].OneofWrappers = []interface{}{
		(*NativeResponse_Asset_Title_)(nil),
		(*NativeResponse_Asset_Img)(nil),
		(*NativeResponse_Asset_Video_)(nil),
		(*NativeResponse_Asset_Data_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_youdao_proto_rawDesc,
			NumEnums:      25,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_youdao_proto_goTypes,
		DependencyIndexes: file_youdao_proto_depIdxs,
		EnumInfos:         file_youdao_proto_enumTypes,
		MessageInfos:      file_youdao_proto_msgTypes,
	}.Build()
	File_youdao_proto = out.File
	file_youdao_proto_rawDesc = nil
	file_youdao_proto_goTypes = nil
	file_youdao_proto_depIdxs = nil
}
