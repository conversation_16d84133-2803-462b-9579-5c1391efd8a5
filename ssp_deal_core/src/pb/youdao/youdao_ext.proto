/**
 * 此文件用于传输数据给外部dsp
 * 若传给YEX，请用openrtb-ydext.proto。
 */
syntax = "proto2";
option go_package = "mh_proxy/pb/youdao";
package youdao;

option java_outer_classname = "OpenRtbYDExtForDsp";

import "youdao.proto";

extend NativeRequest.Asset {
	/**
         * Standard asset.
         */
	optional NativeRequest.Asset sasset = 100;
	/**
	* the ad content map's key
	*/
	optional string label = 101;

	optional VideoFormat videoFormat = 102;

}

extend NativeRequest.Asset.Data {
	/**
	* google openrtb NativeRequest.Asset.Data.type is enum, could not extended easily.
	* using int dataAssetType instead.
	* DSP will ignore NativeRequest.Asset.Data.type even request has to set it.
	*/
	optional int32 dataAssetType = 100;
}

extend BidRequest.Imp {
	// Standard schema ID.
	optional int32 ssid = 100;
	// 是否支持直接在客户端通过 url scheme 唤醒应用
	optional bool dlp = 101;
	// 是否支持通过url scheme和普通网页的合成链接唤醒应用
	optional bool clp = 102;
	// 品牌广告投放的起止时间戳，单位：ms，为空则表示请求当前在投广告
	optional int64 startTime = 103;
	optional int64 endTime = 104;
}

extend BidRequest.Imp.Native {
	/**
	* google openrtb BidRequest.Imp.Native.battr is enum, could not extended easily.
	* using battri to identify another battr
	*/
	repeated int32 battri = 100 [packed = true];
}

extend BidRequest {
	/**
         * trackFromClient标识对于本次竞价请求的获胜者，是否将从客户端发送tracker
         */
	optional bool tfc = 100 [default = false];

	// 是否支持ulink
	optional bool is_support_ulink = 101;
}


extend BidRequest.Device {

	/**
	广告协会设备id（ios）
	 */
	optional string caid = 206;
	/**
	阿里巴巴广告设备id（ios）
	 */
	optional string alid = 207;

	/**
	ios 设备标识符
	 */
	optional string idfv = 210;

	/**
	local时区，eg："Asia/Shanghai"
	 */
	optional string localTzName = 211;

	/**
	手机开机时间,格式eg：1591601319.629868，小数点前为秒级时间戳，小数点后到ns级别
	 */
	optional double deviceStartUpTime = 212;

	/**
	系统版本更新时间,格式eg：1591601319.629868，小数点前为秒级时间戳，小数点后到ns级别
	 */
	optional double deviceUpdateTime = 213;

	/**
	cpu数目
	 */
	optional int32 cpuNumber = 214;

	/**
	磁盘总空间，单位：字节
	 */
	optional int64 disktotal = 215;

	/**
	系统总内容空间，单位：字节
	 */
	optional int64 memTotal = 216;

	/**
	ios广告标识授权情况
	 */
	optional int32 iosAuthStatus = 217;

	optional string bootMark = 218;

	optional string updateMark = 219;

	/**
	国家代码,eg:cn
	 */
	optional string country_code = 220;

	/**
	设备语言（中文，英文，其他）
	 */
	optional string language = 221;

	/**
	手机名称,eg: xxx的iphone
	 */
	optional string phone_name = 222;
	/**
	iphone 内部设备代码
	 */
	optional string phone_device_code = 223;

	/**
	设备名称信息，eg：iphone5,3
	 */
	optional string device_name = 224;

	/**
	* Hardware device ID (e.g., IMEI), in the clear (i.e., not hashed).
	*/
	optional string did = 100;

	/**
	* Platform device ID (e.g., Android ID) in the clear (i.e., not hashed).
	*/
	optional string dpid = 101;

	/**
	* oaid in the clear (i.e., not hashed).
	*/

	optional string oaid = 102;

	/**
	 * 媒体传入的用户年龄
	 */
	optional int32 media_age = 103;

	/**
	 * 媒体传入的用户性别 0:未知；1：男；2：女
	 */
	optional int32 media_gender = 104;

	/**
	 * 媒体传入的用户画像关键词集合
	 */
	optional string media_portrait_keywords = 105;

	/**
	 * 苹果账号id
	 */
	optional string dsid = 106;

	/**
	 * 打开手机到请求广告的时间长度，单位：ms
	 */
	optional int64 power_on_time = 107;

	/**
	 * sim卡串号
	 */
	optional string imsi = 108;

	/**
	 * 手机ROM的版本
	 */
	optional string rom_version = 109;

	/**
	 * 系统编译时间，手机ROM的编译时间
	 */
	optional double system_compiling_time = 110;

	/**
	 * caid 列表，版本高的在前面
	 */
	repeated Caid caids = 115;
	repeated Paid paids = 116;

	optional string idfa_md5 = 225;
	optional string oaid_md5 = 226;
}

message Caid {

	optional string caid = 1;

	optional string caid_md5 = 2;

	optional string version = 3;

}

message Paid {

	optional string paid = 1;

	optional string version = 2;

}

extend BidResponse.SeatBid.Bid {
	/**
	* google openrtb BidResponse.SeatBid.Bid.attri is enum, could not extended easily.
	* using attri to identify another attri
	*/
	repeated int32 attri = 100 [packed = true];
	// 品牌广告投放的起止时间戳，单位：ms，请求中startTime和endTime字段不为空时必填
	optional int64 deliveryStartTime = 101;
	optional int64 deliveryEndTime = 102;
	optional string wechatOriginId = 103;
	optional string wechatPath = 104;
}

extend NativeResponse.Link {
	/**
	*  尝试吊起deeplink
	*/
	repeated string dpTrackers = 100;

	/**
	* deep link call failed trackers
	*/
	repeated string dpFailedTrackers = 101;

	/**
	*下载完成的上报链接
	*/
	repeated string downloadFinishTrackers = 102;
	//视频的播放监测链接，map形式，目前供信息流视频使用
	optional string videoPlayTracker = 103;
	//用户点击安装并完成安装的上报链接
	repeated string installFinishTrackers = 104;
	//deepLink调起成功的上报链接
	repeated string dpSuccessTrackers = 105;
	//deepLink应用未安装导致调起失败的上报链接
	repeated string dpNotInstallTrackers = 106;
	//应用开始下载的上报链接
	repeated string downloadStartTrackers = 107;
	//用户开始安装的上报链接
	repeated string installStartTrackers = 108;
	// 微信小程序尝试吊起上报链接
	repeated string wxTrackers = 109;
	// 微信小程序吊起成功上报链接
	repeated string wxSuccessTrackers = 110;
	// 微信小程序吊起失败上报链接
	repeated string wxFailedTrackers = 111;
	// deepLink安装成功上报链接
	repeated string dpInstallTrackers = 112;
	// 标记OpenRTB协议中NativeResponse.Link.url参数对应的类型：普通落地页，deeplink，ulink
	optional UrlType url_type = 113 [default = UNDEFINED];
}

extend NativeResponse.Asset {
	optional YdVideo ydVideo= 100;
}

message YdVideo {
	required string url = 1;
	// 视频时长,单位s
	optional int32 duration = 2;
	optional int32 width = 3;
	optional int32 height = 4;
	// 视频文件大小，单位Byte
	optional int64 size = 5;
	repeated VideoPlayTracking playTrackings = 6;
}

message VideoPlayTracking{
	required VideoPlayTrackingEvent event = 1;
	required string eventTrackingUrl = 2;
}

/**
视频物料的对接格式
 */
enum VideoFormat{
	// vast格式
	VAST = 1;
	// url格式
	RAW = 2;
}

enum VideoPlayTrackingEvent{
	// 视频开始播放
	START = 1;
	// 视频播放到1/4进度
	FIRST_QUARTILE = 2;
	// 视频播放到1/2进度
	MID_POINT = 3;
	// 视频播放到3/4进度
	THIRD_QUARTILE = 4;
	// 视频播放完成
	COMPLETE = 5;
	// 视频静音
	MUTE = 6;
	// 视频未静音
	UNMUTE = 7;
	// 视频暂停
	PAUSE = 8;
}

enum UrlType {
	// 默认值，未声明点击url类型，按照老版本的逻辑处理
	UNDEFINED = 0;
	// 普通点击跳转落地页，
	LANDINGPAGE = 1;
	// 点击deeplink + fallback
	DEEPLINK = 2;
	// 点击ulink
	ULINK = 3;
}

extend NativeResponse {
	// 工信部四件套要求：应⽤权限、隐私政策⽹址、开发者名称、应⽤名称
	optional DownloadAppInfo downloadAppInfo = 100;
}

// 下载应用的信息
message DownloadAppInfo {
	optional string appIconImage = 1;
	// 应⽤权限，DSP平台拿到的是数组格式的，使用该字段
	repeated Permissions appPermissionArray = 2;
	// 隐私政策⽹址
	optional string privacyPolicy = 3;
	// 开发者名称
	optional string developerName = 4;
	// 应⽤名称
	optional string appTitle = 5;
	// 应用版本号
	optional string appVersion = 6;
	// 应用权限链接，适配 BS
	optional string appPermission = 7;


	message Permissions {
		optional string permissionName = 1;
		optional string permissionDesc = 2;
	}

}