const protobuf = require('protobufjs')
const fs = require('fs')

const bidRequestData = require('./bidRequest.json')
const axios = require('axios')

const protobufRoot = new protobuf.Root()
const root = protobufRoot.loadSync('./meitu-bidding.proto', { keepCase: true })
const bidResponse = root.lookupType('meitu.BidResponse')

var resultBuffer = fs.readFileSync('./res.dat')
const result = bidResponse.decode(resultBuffer)

const resultString = JSON.stringify(result)

console.info(resultString)
console.info(resultString.length)
