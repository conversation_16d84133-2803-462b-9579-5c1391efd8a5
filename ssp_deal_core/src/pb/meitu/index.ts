const protobuf = require('protobufjs')

const bidRequestData = require('./bidRequest.json')
const axios = require('axios')

const protobufRoot = new protobuf.Root()
const root = protobufRoot.loadSync('./meitu-bidding.proto', { keepCase: true })
const bidRequest = root.lookupType('meitu.BidRequest')

let error = bidRequest.verify(bidRequestData)

let url =
    process.env.NODE_ENV === 'debug'
        ? 'http://localhost:8080/rtb/request?channel=27'
        : 'https://adx.maplehaze.cn/rtb/request?channel=27'

if (!error) {
    const message = bidRequest.create(bidRequestData)
    const buffer = bidRequest.encode(message).finish()

    axios
        .post(url, buffer, {
            responseType: 'arraybuffer',
        })
        .then(function (response) {
            if (response.status == 200) {
                const resultBuffer = response.data

                const bidResponse = root.lookupType('meitu.BidResponse')

                const result = bidResponse.decode(resultBuffer)
                const resultString = JSON.stringify(result)

                console.info(resultString)
            } else {
                console.error('error no: ', response.status)
            }
        })
        .catch(function (error) {
            console.log(error)
        })
} else {
    throw error
}
