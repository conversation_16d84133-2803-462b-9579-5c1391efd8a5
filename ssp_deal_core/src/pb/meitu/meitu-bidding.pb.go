// Copyright 2016 Meitu Inc. All Rights Reserved.
// 基于OpenRTB 2.4 若无特殊说明以官方文档为准

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.17.3
// source: meitu-bidding.proto

package meitu

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContentCategory int32

const (
	ContentCategory_CATE001 ContentCategory = 1  // 网络游戏
	ContentCategory_CATE002 ContentCategory = 2  // 服饰
	ContentCategory_CATE003 ContentCategory = 3  // 日化
	ContentCategory_CATE004 ContentCategory = 4  // 网络服务
	ContentCategory_CATE005 ContentCategory = 5  // 个人用品
	ContentCategory_CATE006 ContentCategory = 6  // 零售及服务
	ContentCategory_CATE007 ContentCategory = 7  // 娱乐消闲
	ContentCategory_CATE008 ContentCategory = 8  // 教育出国
	ContentCategory_CATE009 ContentCategory = 9  // 家具装饰
	ContentCategory_CATE010 ContentCategory = 10 // 食品饮料
	ContentCategory_CATE011 ContentCategory = 11 // 交通
	ContentCategory_CATE012 ContentCategory = 12 // IT 产品
	ContentCategory_CATE013 ContentCategory = 13 // 消费电子
	ContentCategory_CATE014 ContentCategory = 14 // 医疗服务
	ContentCategory_CATE015 ContentCategory = 15 // 金融服务
	ContentCategory_CATE016 ContentCategory = 16 // 信息通讯(运营商)
	ContentCategory_CATE017 ContentCategory = 17 // 房地产
	ContentCategory_CATE018 ContentCategory = 18 // 其他
)

// Enum value maps for ContentCategory.
var (
	ContentCategory_name = map[int32]string{
		1:  "CATE001",
		2:  "CATE002",
		3:  "CATE003",
		4:  "CATE004",
		5:  "CATE005",
		6:  "CATE006",
		7:  "CATE007",
		8:  "CATE008",
		9:  "CATE009",
		10: "CATE010",
		11: "CATE011",
		12: "CATE012",
		13: "CATE013",
		14: "CATE014",
		15: "CATE015",
		16: "CATE016",
		17: "CATE017",
		18: "CATE018",
	}
	ContentCategory_value = map[string]int32{
		"CATE001": 1,
		"CATE002": 2,
		"CATE003": 3,
		"CATE004": 4,
		"CATE005": 5,
		"CATE006": 6,
		"CATE007": 7,
		"CATE008": 8,
		"CATE009": 9,
		"CATE010": 10,
		"CATE011": 11,
		"CATE012": 12,
		"CATE013": 13,
		"CATE014": 14,
		"CATE015": 15,
		"CATE016": 16,
		"CATE017": 17,
		"CATE018": 18,
	}
)

func (x ContentCategory) Enum() *ContentCategory {
	p := new(ContentCategory)
	*p = x
	return p
}

func (x ContentCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[0].Descriptor()
}

func (ContentCategory) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[0]
}

func (x ContentCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ContentCategory) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ContentCategory(num)
	return nil
}

// Deprecated: Use ContentCategory.Descriptor instead.
func (ContentCategory) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0}
}

type AuctionType int32

const (
	AuctionType_FIRST_PRICE  AuctionType = 1
	AuctionType_SECOND_PRICE AuctionType = 2
	AuctionType_FIXED_PRICE  AuctionType = 3
)

// Enum value maps for AuctionType.
var (
	AuctionType_name = map[int32]string{
		1: "FIRST_PRICE",
		2: "SECOND_PRICE",
		3: "FIXED_PRICE",
	}
	AuctionType_value = map[string]int32{
		"FIRST_PRICE":  1,
		"SECOND_PRICE": 2,
		"FIXED_PRICE":  3,
	}
)

func (x AuctionType) Enum() *AuctionType {
	p := new(AuctionType)
	*p = x
	return p
}

func (x AuctionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuctionType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[1].Descriptor()
}

func (AuctionType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[1]
}

func (x AuctionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AuctionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AuctionType(num)
	return nil
}

// Deprecated: Use AuctionType.Descriptor instead.
func (AuctionType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{1}
}

type BannerAdType int32

const (
	BannerAdType_XHTML_TEXT_AD   BannerAdType = 1
	BannerAdType_XHTML_BANNER_AD BannerAdType = 2
	BannerAdType_JAVASCRIPT_AD   BannerAdType = 3
	BannerAdType_IFRAME          BannerAdType = 4
)

// Enum value maps for BannerAdType.
var (
	BannerAdType_name = map[int32]string{
		1: "XHTML_TEXT_AD",
		2: "XHTML_BANNER_AD",
		3: "JAVASCRIPT_AD",
		4: "IFRAME",
	}
	BannerAdType_value = map[string]int32{
		"XHTML_TEXT_AD":   1,
		"XHTML_BANNER_AD": 2,
		"JAVASCRIPT_AD":   3,
		"IFRAME":          4,
	}
)

func (x BannerAdType) Enum() *BannerAdType {
	p := new(BannerAdType)
	*p = x
	return p
}

func (x BannerAdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BannerAdType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[2].Descriptor()
}

func (BannerAdType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[2]
}

func (x BannerAdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BannerAdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BannerAdType(num)
	return nil
}

// Deprecated: Use BannerAdType.Descriptor instead.
func (BannerAdType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2}
}

type CreativeAttribute int32

const (
	CreativeAttribute_AUDIO_AUTO_PLAY                CreativeAttribute = 1
	CreativeAttribute_AUDIO_USER_INITIATED           CreativeAttribute = 2
	CreativeAttribute_EXPANDABLE_AUTOMATIC           CreativeAttribute = 3
	CreativeAttribute_EXPANDABLE_CLICK_INITIATED     CreativeAttribute = 4
	CreativeAttribute_EXPANDABLE_ROLLOVER_INITIATED  CreativeAttribute = 5
	CreativeAttribute_VIDEO_IN_BANNER_AUTO_PLAY      CreativeAttribute = 6
	CreativeAttribute_VIDEO_IN_BANNER_USER_INITIATED CreativeAttribute = 7
	CreativeAttribute_POP                            CreativeAttribute = 8
	CreativeAttribute_PROVOCATIVE_OR_SUGGESTIVE      CreativeAttribute = 9
	CreativeAttribute_ANNOYING                       CreativeAttribute = 10
	CreativeAttribute_SURVEYS                        CreativeAttribute = 11
	CreativeAttribute_TEXT_ONLY                      CreativeAttribute = 12
	CreativeAttribute_USER_INTERACTIVE               CreativeAttribute = 13
	CreativeAttribute_WINDOWS_DIALOG_OR_ALERT_STYLE  CreativeAttribute = 14
	CreativeAttribute_HAS_AUDIO_ON_OFF_BUTTON        CreativeAttribute = 15
	CreativeAttribute_AD_CAN_BE_SKIPPED              CreativeAttribute = 16
	CreativeAttribute_FLASH                          CreativeAttribute = 17
)

// Enum value maps for CreativeAttribute.
var (
	CreativeAttribute_name = map[int32]string{
		1:  "AUDIO_AUTO_PLAY",
		2:  "AUDIO_USER_INITIATED",
		3:  "EXPANDABLE_AUTOMATIC",
		4:  "EXPANDABLE_CLICK_INITIATED",
		5:  "EXPANDABLE_ROLLOVER_INITIATED",
		6:  "VIDEO_IN_BANNER_AUTO_PLAY",
		7:  "VIDEO_IN_BANNER_USER_INITIATED",
		8:  "POP",
		9:  "PROVOCATIVE_OR_SUGGESTIVE",
		10: "ANNOYING",
		11: "SURVEYS",
		12: "TEXT_ONLY",
		13: "USER_INTERACTIVE",
		14: "WINDOWS_DIALOG_OR_ALERT_STYLE",
		15: "HAS_AUDIO_ON_OFF_BUTTON",
		16: "AD_CAN_BE_SKIPPED",
		17: "FLASH",
	}
	CreativeAttribute_value = map[string]int32{
		"AUDIO_AUTO_PLAY":                1,
		"AUDIO_USER_INITIATED":           2,
		"EXPANDABLE_AUTOMATIC":           3,
		"EXPANDABLE_CLICK_INITIATED":     4,
		"EXPANDABLE_ROLLOVER_INITIATED":  5,
		"VIDEO_IN_BANNER_AUTO_PLAY":      6,
		"VIDEO_IN_BANNER_USER_INITIATED": 7,
		"POP":                            8,
		"PROVOCATIVE_OR_SUGGESTIVE":      9,
		"ANNOYING":                       10,
		"SURVEYS":                        11,
		"TEXT_ONLY":                      12,
		"USER_INTERACTIVE":               13,
		"WINDOWS_DIALOG_OR_ALERT_STYLE":  14,
		"HAS_AUDIO_ON_OFF_BUTTON":        15,
		"AD_CAN_BE_SKIPPED":              16,
		"FLASH":                          17,
	}
)

func (x CreativeAttribute) Enum() *CreativeAttribute {
	p := new(CreativeAttribute)
	*p = x
	return p
}

func (x CreativeAttribute) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreativeAttribute) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[3].Descriptor()
}

func (CreativeAttribute) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[3]
}

func (x CreativeAttribute) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CreativeAttribute) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CreativeAttribute(num)
	return nil
}

// Deprecated: Use CreativeAttribute.Descriptor instead.
func (CreativeAttribute) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3}
}

type APIFramework int32

const (
	APIFramework_VPAID_1 APIFramework = 1
	APIFramework_VPAID_2 APIFramework = 2
	APIFramework_MRAID_1 APIFramework = 3
	APIFramework_ORMMA   APIFramework = 4
	APIFramework_MRAID_2 APIFramework = 5
)

// Enum value maps for APIFramework.
var (
	APIFramework_name = map[int32]string{
		1: "VPAID_1",
		2: "VPAID_2",
		3: "MRAID_1",
		4: "ORMMA",
		5: "MRAID_2",
	}
	APIFramework_value = map[string]int32{
		"VPAID_1": 1,
		"VPAID_2": 2,
		"MRAID_1": 3,
		"ORMMA":   4,
		"MRAID_2": 5,
	}
)

func (x APIFramework) Enum() *APIFramework {
	p := new(APIFramework)
	*p = x
	return p
}

func (x APIFramework) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (APIFramework) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[4].Descriptor()
}

func (APIFramework) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[4]
}

func (x APIFramework) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *APIFramework) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = APIFramework(num)
	return nil
}

// Deprecated: Use APIFramework.Descriptor instead.
func (APIFramework) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{4}
}

type AdPosition int32

const (
	AdPosition_UNKNOWN                AdPosition = 0
	AdPosition_ABOVE_THE_FOLD         AdPosition = 1
	AdPosition_BELOW_THE_FOLD         AdPosition = 3
	AdPosition_HEADER                 AdPosition = 4
	AdPosition_FOOTER                 AdPosition = 5
	AdPosition_SIDEBAR                AdPosition = 6
	AdPosition_AD_POSITION_FULLSCREEN AdPosition = 7
)

// Enum value maps for AdPosition.
var (
	AdPosition_name = map[int32]string{
		0: "UNKNOWN",
		1: "ABOVE_THE_FOLD",
		3: "BELOW_THE_FOLD",
		4: "HEADER",
		5: "FOOTER",
		6: "SIDEBAR",
		7: "AD_POSITION_FULLSCREEN",
	}
	AdPosition_value = map[string]int32{
		"UNKNOWN":                0,
		"ABOVE_THE_FOLD":         1,
		"BELOW_THE_FOLD":         3,
		"HEADER":                 4,
		"FOOTER":                 5,
		"SIDEBAR":                6,
		"AD_POSITION_FULLSCREEN": 7,
	}
)

func (x AdPosition) Enum() *AdPosition {
	p := new(AdPosition)
	*p = x
	return p
}

func (x AdPosition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdPosition) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[5].Descriptor()
}

func (AdPosition) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[5]
}

func (x AdPosition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AdPosition) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AdPosition(num)
	return nil
}

// Deprecated: Use AdPosition.Descriptor instead.
func (AdPosition) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{5}
}

type ConnectionType int32

const (
	ConnectionType_CONNECTION_UNKNOWN ConnectionType = 0
	ConnectionType_ETHERNET           ConnectionType = 1
	ConnectionType_WIFI               ConnectionType = 2
	ConnectionType_CELL_UNKNOWN       ConnectionType = 3
	ConnectionType_CELL_2G            ConnectionType = 4
	ConnectionType_CELL_3G            ConnectionType = 5
	ConnectionType_CELL_4G            ConnectionType = 6
)

// Enum value maps for ConnectionType.
var (
	ConnectionType_name = map[int32]string{
		0: "CONNECTION_UNKNOWN",
		1: "ETHERNET",
		2: "WIFI",
		3: "CELL_UNKNOWN",
		4: "CELL_2G",
		5: "CELL_3G",
		6: "CELL_4G",
	}
	ConnectionType_value = map[string]int32{
		"CONNECTION_UNKNOWN": 0,
		"ETHERNET":           1,
		"WIFI":               2,
		"CELL_UNKNOWN":       3,
		"CELL_2G":            4,
		"CELL_3G":            5,
		"CELL_4G":            6,
	}
)

func (x ConnectionType) Enum() *ConnectionType {
	p := new(ConnectionType)
	*p = x
	return p
}

func (x ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[6].Descriptor()
}

func (ConnectionType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[6]
}

func (x ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ConnectionType(num)
	return nil
}

// Deprecated: Use ConnectionType.Descriptor instead.
func (ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{6}
}

type ExpandableDirection int32

const (
	ExpandableDirection_LEFT                  ExpandableDirection = 1
	ExpandableDirection_RIGHT                 ExpandableDirection = 2
	ExpandableDirection_UP                    ExpandableDirection = 3
	ExpandableDirection_DOWN                  ExpandableDirection = 4
	ExpandableDirection_EXPANDABLE_FULLSCREEN ExpandableDirection = 5
)

// Enum value maps for ExpandableDirection.
var (
	ExpandableDirection_name = map[int32]string{
		1: "LEFT",
		2: "RIGHT",
		3: "UP",
		4: "DOWN",
		5: "EXPANDABLE_FULLSCREEN",
	}
	ExpandableDirection_value = map[string]int32{
		"LEFT":                  1,
		"RIGHT":                 2,
		"UP":                    3,
		"DOWN":                  4,
		"EXPANDABLE_FULLSCREEN": 5,
	}
)

func (x ExpandableDirection) Enum() *ExpandableDirection {
	p := new(ExpandableDirection)
	*p = x
	return p
}

func (x ExpandableDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpandableDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[7].Descriptor()
}

func (ExpandableDirection) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[7]
}

func (x ExpandableDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ExpandableDirection) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ExpandableDirection(num)
	return nil
}

// Deprecated: Use ExpandableDirection.Descriptor instead.
func (ExpandableDirection) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{7}
}

type LocationType int32

const (
	LocationType_GPS_LOCATION  LocationType = 1
	LocationType_IP            LocationType = 2
	LocationType_USER_PROVIDED LocationType = 3
)

// Enum value maps for LocationType.
var (
	LocationType_name = map[int32]string{
		1: "GPS_LOCATION",
		2: "IP",
		3: "USER_PROVIDED",
	}
	LocationType_value = map[string]int32{
		"GPS_LOCATION":  1,
		"IP":            2,
		"USER_PROVIDED": 3,
	}
)

func (x LocationType) Enum() *LocationType {
	p := new(LocationType)
	*p = x
	return p
}

func (x LocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[8].Descriptor()
}

func (LocationType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[8]
}

func (x LocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LocationType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LocationType(num)
	return nil
}

// Deprecated: Use LocationType.Descriptor instead.
func (LocationType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{8}
}

type LocationService int32

const (
	LocationService_IP2LOCATION LocationService = 1
	LocationService_NEUSTAR     LocationService = 2
	LocationService_MAXMIND     LocationService = 3
	LocationService_NETAQUITY   LocationService = 4
)

// Enum value maps for LocationService.
var (
	LocationService_name = map[int32]string{
		1: "IP2LOCATION",
		2: "NEUSTAR",
		3: "MAXMIND",
		4: "NETAQUITY",
	}
	LocationService_value = map[string]int32{
		"IP2LOCATION": 1,
		"NEUSTAR":     2,
		"MAXMIND":     3,
		"NETAQUITY":   4,
	}
)

func (x LocationService) Enum() *LocationService {
	p := new(LocationService)
	*p = x
	return p
}

func (x LocationService) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationService) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[9].Descriptor()
}

func (LocationService) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[9]
}

func (x LocationService) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LocationService) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LocationService(num)
	return nil
}

// Deprecated: Use LocationService.Descriptor instead.
func (LocationService) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{9}
}

type DeviceType int32

const (
	// 手机 或 tablet.
	DeviceType_MOBILE            DeviceType = 1
	DeviceType_PERSONAL_COMPUTER DeviceType = 2
	DeviceType_CONNECTED_TV      DeviceType = 3
	DeviceType_HIGHEND_PHONE     DeviceType = 4
	DeviceType_TABLET            DeviceType = 5
	DeviceType_CONNECTED_DEVICE  DeviceType = 6
	DeviceType_SET_TOP_BOX       DeviceType = 7
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		1: "MOBILE",
		2: "PERSONAL_COMPUTER",
		3: "CONNECTED_TV",
		4: "HIGHEND_PHONE",
		5: "TABLET",
		6: "CONNECTED_DEVICE",
		7: "SET_TOP_BOX",
	}
	DeviceType_value = map[string]int32{
		"MOBILE":            1,
		"PERSONAL_COMPUTER": 2,
		"CONNECTED_TV":      3,
		"HIGHEND_PHONE":     4,
		"TABLET":            5,
		"CONNECTED_DEVICE":  6,
		"SET_TOP_BOX":       7,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[10].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[10]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DeviceType(num)
	return nil
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{10}
}

type NoBidReason int32

const (
	NoBidReason_UNKNOWN_ERROR              NoBidReason = 0
	NoBidReason_TECHNICAL_ERROR            NoBidReason = 1
	NoBidReason_INVALID_REQUEST            NoBidReason = 2
	NoBidReason_KNOWN_WEB_SPIDER           NoBidReason = 3
	NoBidReason_SUSPECTED_NONHUMAN_TRAFFIC NoBidReason = 4
	NoBidReason_CLOUD_DATACENTER_PROXYIP   NoBidReason = 5
	NoBidReason_UNSUPPORTED_DEVICE         NoBidReason = 6
	NoBidReason_BLOCKED_PUBLISHER          NoBidReason = 7
	NoBidReason_UNMATCHED_USER             NoBidReason = 8
)

// Enum value maps for NoBidReason.
var (
	NoBidReason_name = map[int32]string{
		0: "UNKNOWN_ERROR",
		1: "TECHNICAL_ERROR",
		2: "INVALID_REQUEST",
		3: "KNOWN_WEB_SPIDER",
		4: "SUSPECTED_NONHUMAN_TRAFFIC",
		5: "CLOUD_DATACENTER_PROXYIP",
		6: "UNSUPPORTED_DEVICE",
		7: "BLOCKED_PUBLISHER",
		8: "UNMATCHED_USER",
	}
	NoBidReason_value = map[string]int32{
		"UNKNOWN_ERROR":              0,
		"TECHNICAL_ERROR":            1,
		"INVALID_REQUEST":            2,
		"KNOWN_WEB_SPIDER":           3,
		"SUSPECTED_NONHUMAN_TRAFFIC": 4,
		"CLOUD_DATACENTER_PROXYIP":   5,
		"UNSUPPORTED_DEVICE":         6,
		"BLOCKED_PUBLISHER":          7,
		"UNMATCHED_USER":             8,
	}
)

func (x NoBidReason) Enum() *NoBidReason {
	p := new(NoBidReason)
	*p = x
	return p
}

func (x NoBidReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NoBidReason) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[11].Descriptor()
}

func (NoBidReason) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[11]
}

func (x NoBidReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NoBidReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NoBidReason(num)
	return nil
}

// Deprecated: Use NoBidReason.Descriptor instead.
func (NoBidReason) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{11}
}

type LayoutId int32

const (
	LayoutId_CONTENT_WALL   LayoutId = 1
	LayoutId_APP_WALL       LayoutId = 2
	LayoutId_NEWS_FEED      LayoutId = 3
	LayoutId_CHAT_LIST      LayoutId = 4
	LayoutId_CAROUSEL       LayoutId = 5
	LayoutId_CONTENT_STREAM LayoutId = 6
	LayoutId_GRID           LayoutId = 7
)

// Enum value maps for LayoutId.
var (
	LayoutId_name = map[int32]string{
		1: "CONTENT_WALL",
		2: "APP_WALL",
		3: "NEWS_FEED",
		4: "CHAT_LIST",
		5: "CAROUSEL",
		6: "CONTENT_STREAM",
		7: "GRID",
	}
	LayoutId_value = map[string]int32{
		"CONTENT_WALL":   1,
		"APP_WALL":       2,
		"NEWS_FEED":      3,
		"CHAT_LIST":      4,
		"CAROUSEL":       5,
		"CONTENT_STREAM": 6,
		"GRID":           7,
	}
)

func (x LayoutId) Enum() *LayoutId {
	p := new(LayoutId)
	*p = x
	return p
}

func (x LayoutId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LayoutId) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[12].Descriptor()
}

func (LayoutId) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[12]
}

func (x LayoutId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LayoutId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LayoutId(num)
	return nil
}

// Deprecated: Use LayoutId.Descriptor instead.
func (LayoutId) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{12}
}

type AdUnitId int32

const (
	AdUnitId_PAID_SEARCH_UNIT      AdUnitId = 1
	AdUnitId_RECOMMENDATION_WIDGET AdUnitId = 2
	AdUnitId_PROMOTED_LISTING      AdUnitId = 3
	AdUnitId_IAB_IN_AD_NATIVE      AdUnitId = 4
	AdUnitId_ADUNITID_CUSTOM       AdUnitId = 5
)

// Enum value maps for AdUnitId.
var (
	AdUnitId_name = map[int32]string{
		1: "PAID_SEARCH_UNIT",
		2: "RECOMMENDATION_WIDGET",
		3: "PROMOTED_LISTING",
		4: "IAB_IN_AD_NATIVE",
		5: "ADUNITID_CUSTOM",
	}
	AdUnitId_value = map[string]int32{
		"PAID_SEARCH_UNIT":      1,
		"RECOMMENDATION_WIDGET": 2,
		"PROMOTED_LISTING":      3,
		"IAB_IN_AD_NATIVE":      4,
		"ADUNITID_CUSTOM":       5,
	}
)

func (x AdUnitId) Enum() *AdUnitId {
	p := new(AdUnitId)
	*p = x
	return p
}

func (x AdUnitId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdUnitId) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[13].Descriptor()
}

func (AdUnitId) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[13]
}

func (x AdUnitId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AdUnitId) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AdUnitId(num)
	return nil
}

// Deprecated: Use AdUnitId.Descriptor instead.
func (AdUnitId) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{13}
}

type ContextType int32

const (
	ContextType_CONTENT ContextType = 1
	ContextType_SOCIAL  ContextType = 2
	ContextType_PRODUCT ContextType = 3
)

// Enum value maps for ContextType.
var (
	ContextType_name = map[int32]string{
		1: "CONTENT",
		2: "SOCIAL",
		3: "PRODUCT",
	}
	ContextType_value = map[string]int32{
		"CONTENT": 1,
		"SOCIAL":  2,
		"PRODUCT": 3,
	}
)

func (x ContextType) Enum() *ContextType {
	p := new(ContextType)
	*p = x
	return p
}

func (x ContextType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContextType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[14].Descriptor()
}

func (ContextType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[14]
}

func (x ContextType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ContextType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ContextType(num)
	return nil
}

// Deprecated: Use ContextType.Descriptor instead.
func (ContextType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{14}
}

type ContextSubtype int32

const (
	ContextSubtype_CONTENT_GENERAL_OR_MIXED ContextSubtype = 10
	ContextSubtype_CONTENT_ARTICLE          ContextSubtype = 11
	ContextSubtype_CONTENT_VIDEO            ContextSubtype = 12
	ContextSubtype_CONTENT_AUDIO            ContextSubtype = 13
	ContextSubtype_CONTENT_IMAGE            ContextSubtype = 14
	ContextSubtype_CONTENT_USER_GENERATED   ContextSubtype = 15
	ContextSubtype_SOCIAL_GENERAL           ContextSubtype = 20
	ContextSubtype_SOCIAL_EMAIL             ContextSubtype = 21
	ContextSubtype_SOCIAL_CHAT_IM           ContextSubtype = 22
	ContextSubtype_PRODUCT_SELLING          ContextSubtype = 30
	ContextSubtype_PRODUCT_MARKETPLACE      ContextSubtype = 31
	ContextSubtype_PRODUCT_REVIEW           ContextSubtype = 32
)

// Enum value maps for ContextSubtype.
var (
	ContextSubtype_name = map[int32]string{
		10: "CONTENT_GENERAL_OR_MIXED",
		11: "CONTENT_ARTICLE",
		12: "CONTENT_VIDEO",
		13: "CONTENT_AUDIO",
		14: "CONTENT_IMAGE",
		15: "CONTENT_USER_GENERATED",
		20: "SOCIAL_GENERAL",
		21: "SOCIAL_EMAIL",
		22: "SOCIAL_CHAT_IM",
		30: "PRODUCT_SELLING",
		31: "PRODUCT_MARKETPLACE",
		32: "PRODUCT_REVIEW",
	}
	ContextSubtype_value = map[string]int32{
		"CONTENT_GENERAL_OR_MIXED": 10,
		"CONTENT_ARTICLE":          11,
		"CONTENT_VIDEO":            12,
		"CONTENT_AUDIO":            13,
		"CONTENT_IMAGE":            14,
		"CONTENT_USER_GENERATED":   15,
		"SOCIAL_GENERAL":           20,
		"SOCIAL_EMAIL":             21,
		"SOCIAL_CHAT_IM":           22,
		"PRODUCT_SELLING":          30,
		"PRODUCT_MARKETPLACE":      31,
		"PRODUCT_REVIEW":           32,
	}
)

func (x ContextSubtype) Enum() *ContextSubtype {
	p := new(ContextSubtype)
	*p = x
	return p
}

func (x ContextSubtype) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContextSubtype) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[15].Descriptor()
}

func (ContextSubtype) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[15]
}

func (x ContextSubtype) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ContextSubtype) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ContextSubtype(num)
	return nil
}

// Deprecated: Use ContextSubtype.Descriptor instead.
func (ContextSubtype) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{15}
}

type PlacementType int32

const (
	PlacementType_IN_FEED        PlacementType = 1
	PlacementType_ATOMIC_UNIT    PlacementType = 2
	PlacementType_OUTSIDE        PlacementType = 3
	PlacementType_RECOMMENDATION PlacementType = 4
)

// Enum value maps for PlacementType.
var (
	PlacementType_name = map[int32]string{
		1: "IN_FEED",
		2: "ATOMIC_UNIT",
		3: "OUTSIDE",
		4: "RECOMMENDATION",
	}
	PlacementType_value = map[string]int32{
		"IN_FEED":        1,
		"ATOMIC_UNIT":    2,
		"OUTSIDE":        3,
		"RECOMMENDATION": 4,
	}
)

func (x PlacementType) Enum() *PlacementType {
	p := new(PlacementType)
	*p = x
	return p
}

func (x PlacementType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlacementType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[16].Descriptor()
}

func (PlacementType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[16]
}

func (x PlacementType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PlacementType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PlacementType(num)
	return nil
}

// Deprecated: Use PlacementType.Descriptor instead.
func (PlacementType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{16}
}

type DataAssetType int32

const (
	DataAssetType_SPONSORED  DataAssetType = 1
	DataAssetType_DESC       DataAssetType = 2
	DataAssetType_RATING     DataAssetType = 3
	DataAssetType_LIKES      DataAssetType = 4
	DataAssetType_DOWNLOADS  DataAssetType = 5
	DataAssetType_PRICE      DataAssetType = 6
	DataAssetType_SALEPRICE  DataAssetType = 7
	DataAssetType_PHONE      DataAssetType = 8
	DataAssetType_ADDRESS    DataAssetType = 9
	DataAssetType_DESC2      DataAssetType = 10
	DataAssetType_DISPLAYURL DataAssetType = 11
	DataAssetType_CTATEXT    DataAssetType = 12
)

// Enum value maps for DataAssetType.
var (
	DataAssetType_name = map[int32]string{
		1:  "SPONSORED",
		2:  "DESC",
		3:  "RATING",
		4:  "LIKES",
		5:  "DOWNLOADS",
		6:  "PRICE",
		7:  "SALEPRICE",
		8:  "PHONE",
		9:  "ADDRESS",
		10: "DESC2",
		11: "DISPLAYURL",
		12: "CTATEXT",
	}
	DataAssetType_value = map[string]int32{
		"SPONSORED":  1,
		"DESC":       2,
		"RATING":     3,
		"LIKES":      4,
		"DOWNLOADS":  5,
		"PRICE":      6,
		"SALEPRICE":  7,
		"PHONE":      8,
		"ADDRESS":    9,
		"DESC2":      10,
		"DISPLAYURL": 11,
		"CTATEXT":    12,
	}
)

func (x DataAssetType) Enum() *DataAssetType {
	p := new(DataAssetType)
	*p = x
	return p
}

func (x DataAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[17].Descriptor()
}

func (DataAssetType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[17]
}

func (x DataAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DataAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DataAssetType(num)
	return nil
}

// Deprecated: Use DataAssetType.Descriptor instead.
func (DataAssetType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{17}
}

type ImageAssetType int32

const (
	ImageAssetType_ICON  ImageAssetType = 1
	ImageAssetType_LOGO  ImageAssetType = 2
	ImageAssetType_MAIN  ImageAssetType = 3
	ImageAssetType_COVER ImageAssetType = 4
)

// Enum value maps for ImageAssetType.
var (
	ImageAssetType_name = map[int32]string{
		1: "ICON",
		2: "LOGO",
		3: "MAIN",
		4: "COVER",
	}
	ImageAssetType_value = map[string]int32{
		"ICON":  1,
		"LOGO":  2,
		"MAIN":  3,
		"COVER": 4,
	}
)

func (x ImageAssetType) Enum() *ImageAssetType {
	p := new(ImageAssetType)
	*p = x
	return p
}

func (x ImageAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_meitu_bidding_proto_enumTypes[18].Descriptor()
}

func (ImageAssetType) Type() protoreflect.EnumType {
	return &file_meitu_bidding_proto_enumTypes[18]
}

func (x ImageAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ImageAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ImageAssetType(num)
	return nil
}

// Deprecated: Use ImageAssetType.Descriptor instead.
func (ImageAssetType) EnumDescriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{18}
}

type BidRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id     *string            `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Imp    []*BidRequest_Imp  `protobuf:"bytes,2,rep,name=imp" json:"imp,omitempty"`
	App    *BidRequest_App    `protobuf:"bytes,3,opt,name=app" json:"app,omitempty"`
	Device *BidRequest_Device `protobuf:"bytes,4,opt,name=device" json:"device,omitempty"`
	User   *BidRequest_User   `protobuf:"bytes,5,opt,name=user" json:"user,omitempty"`
	At     *AuctionType       `protobuf:"varint,6,opt,name=at,enum=meitu.AuctionType,def=2" json:"at,omitempty"`
	// 最大时间限制, 毫秒
	Tmax *int32 `protobuf:"varint,7,opt,name=tmax" json:"tmax,omitempty"`
	// 买方白名单
	Wseat []string `protobuf:"bytes,8,rep,name=wseat" json:"wseat,omitempty"`
	// 是否为当前环境全部展示机会, 1 => 是, 0 => 否
	Allimps *bool `protobuf:"varint,9,opt,name=allimps,def=0" json:"allimps,omitempty"`
	// 允许交易的货币, 使用ISO-4217 alpha编码
	Cur []string `protobuf:"bytes,10,rep,name=cur" json:"cur,omitempty"`
	// 禁止的广告主类别
	Bcat []ContentCategory `protobuf:"varint,11,rep,name=bcat,enum=meitu.ContentCategory" json:"bcat,omitempty"`
	// 禁止的广告主域名
	Badv []string `protobuf:"bytes,12,rep,name=badv" json:"badv,omitempty"`
	// 禁止的App. Android: 包名; iOS: ID.
	Bapp []string `protobuf:"bytes,13,rep,name=bapp" json:"bapp,omitempty"`
	// 1 => 测试请求, 0 => 非测试请求
	Test *bool `protobuf:"varint,14,opt,name=test,def=0" json:"test,omitempty"`
	// 1 =>是联盟流量，0 =>非联盟流量
	IsAffiliate *int32 `protobuf:"varint,15,opt,name=is_affiliate,json=isAffiliate,def=0" json:"is_affiliate,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_At          = AuctionType_SECOND_PRICE
	Default_BidRequest_Allimps     = bool(false)
	Default_BidRequest_Test        = bool(false)
	Default_BidRequest_IsAffiliate = int32(0)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetAt() AuctionType {
	if x != nil && x.At != nil {
		return *x.At
	}
	return Default_BidRequest_At
}

func (x *BidRequest) GetTmax() int32 {
	if x != nil && x.Tmax != nil {
		return *x.Tmax
	}
	return 0
}

func (x *BidRequest) GetWseat() []string {
	if x != nil {
		return x.Wseat
	}
	return nil
}

func (x *BidRequest) GetAllimps() bool {
	if x != nil && x.Allimps != nil {
		return *x.Allimps
	}
	return Default_BidRequest_Allimps
}

func (x *BidRequest) GetCur() []string {
	if x != nil {
		return x.Cur
	}
	return nil
}

func (x *BidRequest) GetBcat() []ContentCategory {
	if x != nil {
		return x.Bcat
	}
	return nil
}

func (x *BidRequest) GetBadv() []string {
	if x != nil {
		return x.Badv
	}
	return nil
}

func (x *BidRequest) GetBapp() []string {
	if x != nil {
		return x.Bapp
	}
	return nil
}

func (x *BidRequest) GetTest() bool {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return Default_BidRequest_Test
}

func (x *BidRequest) GetIsAffiliate() int32 {
	if x != nil && x.IsAffiliate != nil {
		return *x.IsAffiliate
	}
	return Default_BidRequest_IsAffiliate
}

type BidResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// bid request的ID
	Id      *string                `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,2,rep,name=seatbid" json:"seatbid,omitempty"`
	// 买方bid ID, 用以记录/跟踪
	Bidid *string `protobuf:"bytes,3,opt,name=bidid" json:"bidid,omitempty"`
	// 货币, 使用ISO-4217 alpha 代码.
	Cur *string      `protobuf:"bytes,4,opt,name=cur" json:"cur,omitempty"`
	Nbr *NoBidReason `protobuf:"varint,6,opt,name=nbr,enum=meitu.NoBidReason" json:"nbr,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetBidid() string {
	if x != nil && x.Bidid != nil {
		return *x.Bidid
	}
	return ""
}

func (x *BidResponse) GetCur() string {
	if x != nil && x.Cur != nil {
		return *x.Cur
	}
	return ""
}

func (x *BidResponse) GetNbr() NoBidReason {
	if x != nil && x.Nbr != nil {
		return *x.Nbr
	}
	return NoBidReason_UNKNOWN_ERROR
}

type NativeRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// OpenRTB中native广告协议版本号
	// "1.0" 对应 OpenRTB 2.3; "1.1" 对应 OpenRTB 2.4
	Ver            *string         `protobuf:"bytes,1,opt,name=ver" json:"ver,omitempty"`
	Layout         *LayoutId       `protobuf:"varint,2,opt,name=layout,enum=meitu.LayoutId" json:"layout,omitempty"`
	Adunit         *AdUnitId       `protobuf:"varint,3,opt,name=adunit,enum=meitu.AdUnitId" json:"adunit,omitempty"`
	Context        *ContextType    `protobuf:"varint,7,opt,name=context,enum=meitu.ContextType" json:"context,omitempty"`
	Contextsubtype *ContextSubtype `protobuf:"varint,8,opt,name=contextsubtype,enum=meitu.ContextSubtype" json:"contextsubtype,omitempty"`
	Plcmttype      *PlacementType  `protobuf:"varint,9,opt,name=plcmttype,enum=meitu.PlacementType" json:"plcmttype,omitempty"`
	// Layout中广告单元数量
	Plcmtcnt *int32                 `protobuf:"varint,4,opt,name=plcmtcnt,def=1" json:"plcmtcnt,omitempty"`
	Seq      *int32                 `protobuf:"varint,5,opt,name=seq,def=0" json:"seq,omitempty"`
	Assets   []*NativeRequest_Asset `protobuf:"bytes,6,rep,name=assets" json:"assets,omitempty"`
}

// Default values for NativeRequest fields.
const (
	Default_NativeRequest_Plcmtcnt = int32(1)
	Default_NativeRequest_Seq      = int32(0)
)

func (x *NativeRequest) Reset() {
	*x = NativeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest) ProtoMessage() {}

func (x *NativeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest.ProtoReflect.Descriptor instead.
func (*NativeRequest) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2}
}

func (x *NativeRequest) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *NativeRequest) GetLayout() LayoutId {
	if x != nil && x.Layout != nil {
		return *x.Layout
	}
	return LayoutId_CONTENT_WALL
}

func (x *NativeRequest) GetAdunit() AdUnitId {
	if x != nil && x.Adunit != nil {
		return *x.Adunit
	}
	return AdUnitId_PAID_SEARCH_UNIT
}

func (x *NativeRequest) GetContext() ContextType {
	if x != nil && x.Context != nil {
		return *x.Context
	}
	return ContextType_CONTENT
}

func (x *NativeRequest) GetContextsubtype() ContextSubtype {
	if x != nil && x.Contextsubtype != nil {
		return *x.Contextsubtype
	}
	return ContextSubtype_CONTENT_GENERAL_OR_MIXED
}

func (x *NativeRequest) GetPlcmttype() PlacementType {
	if x != nil && x.Plcmttype != nil {
		return *x.Plcmttype
	}
	return PlacementType_IN_FEED
}

func (x *NativeRequest) GetPlcmtcnt() int32 {
	if x != nil && x.Plcmtcnt != nil {
		return *x.Plcmtcnt
	}
	return Default_NativeRequest_Plcmtcnt
}

func (x *NativeRequest) GetSeq() int32 {
	if x != nil && x.Seq != nil {
		return *x.Seq
	}
	return Default_NativeRequest_Seq
}

func (x *NativeRequest) GetAssets() []*NativeRequest_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type NativeResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// OpenRTB中native广告协议版本号
	// "1.0" 对应 OpenRTB 2.3; "1.1" 对应 OpenRTB 2.4
	Ver         *string                 `protobuf:"bytes,1,opt,name=ver" json:"ver,omitempty"`
	Assets      []*NativeResponse_Asset `protobuf:"bytes,2,rep,name=assets" json:"assets,omitempty"`
	Link        *NativeResponse_Link    `protobuf:"bytes,3,req,name=link" json:"link,omitempty"`
	Imptrackers []string                `protobuf:"bytes,4,rep,name=imptrackers" json:"imptrackers,omitempty"`
	Jstracker   *string                 `protobuf:"bytes,5,opt,name=jstracker" json:"jstracker,omitempty"`
}

func (x *NativeResponse) Reset() {
	*x = NativeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse) ProtoMessage() {}

func (x *NativeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse.ProtoReflect.Descriptor instead.
func (*NativeResponse) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3}
}

func (x *NativeResponse) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *NativeResponse) GetAssets() []*NativeResponse_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *NativeResponse) GetLink() *NativeResponse_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

func (x *NativeResponse) GetImptrackers() []string {
	if x != nil {
		return x.Imptrackers
	}
	return nil
}

func (x *NativeResponse) GetJstracker() string {
	if x != nil && x.Jstracker != nil {
		return *x.Jstracker
	}
	return ""
}

type BidRequest_Imp struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id     *string                `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	Banner *BidRequest_Imp_Banner `protobuf:"bytes,2,opt,name=banner" json:"banner,omitempty"`
	// 负责渲染广告的ad mediation，SDK或者播放器的名称
	Displaymanager *string `protobuf:"bytes,3,opt,name=displaymanager" json:"displaymanager,omitempty"`
	// 负责渲染广告的ad mediation，SDK或者播放器的版本
	Displaymanagerver *string `protobuf:"bytes,4,opt,name=displaymanagerver" json:"displaymanagerver,omitempty"`
	// 1 => 当前广告是插屏或全屏， 0 => 非插屏
	Instl *bool `protobuf:"varint,5,opt,name=instl" json:"instl,omitempty"`
	// 广告位ID
	Tagid *string `protobuf:"bytes,6,opt,name=tagid" json:"tagid,omitempty"`
	// 底价, 单位: 分(人民币)/千次展示
	Bidfloor *float64 `protobuf:"fixed64,7,opt,name=bidfloor,def=0" json:"bidfloor,omitempty"`
	// 货币, 使用ISO-4217 alpha编码
	Bidfloorcur *string `protobuf:"bytes,8,opt,name=bidfloorcur,def=CNY" json:"bidfloorcur,omitempty"`
	// App中点击创意后使用的浏览器, 0 => 内嵌式, 1 => native.
	Clickbrowser *bool `protobuf:"varint,9,opt,name=clickbrowser" json:"clickbrowser,omitempty"`
	// 是否对创意要求HTTPS连接, 1 => 要求HTTPS连接, 0 => 不要求
	Secure *bool `protobuf:"varint,10,opt,name=secure" json:"secure,omitempty"`
	// 支持的iframe buster
	Iframebuster []string               `protobuf:"bytes,11,rep,name=iframebuster" json:"iframebuster,omitempty"`
	Pmp          *BidRequest_Imp_Pmp    `protobuf:"bytes,12,opt,name=pmp" json:"pmp,omitempty"`
	Native       *BidRequest_Imp_Native `protobuf:"bytes,13,opt,name=native" json:"native,omitempty"`
	// 竞价到展示的时间, 秒
	Exp *int32 `protobuf:"varint,14,opt,name=exp" json:"exp,omitempty"`
	// 此广告请求支持的的广告样式列表
	StyleIds []string `protobuf:"bytes,15,rep,name=style_ids,json=styleIds" json:"style_ids,omitempty"`
}

// Default values for BidRequest_Imp fields.
const (
	Default_BidRequest_Imp_Bidfloor    = float64(0)
	Default_BidRequest_Imp_Bidfloorcur = string("CNY")
)

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetBanner() *BidRequest_Imp_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *BidRequest_Imp) GetDisplaymanager() string {
	if x != nil && x.Displaymanager != nil {
		return *x.Displaymanager
	}
	return ""
}

func (x *BidRequest_Imp) GetDisplaymanagerver() string {
	if x != nil && x.Displaymanagerver != nil {
		return *x.Displaymanagerver
	}
	return ""
}

func (x *BidRequest_Imp) GetInstl() bool {
	if x != nil && x.Instl != nil {
		return *x.Instl
	}
	return false
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil && x.Tagid != nil {
		return *x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetBidfloor() float64 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return Default_BidRequest_Imp_Bidfloor
}

func (x *BidRequest_Imp) GetBidfloorcur() string {
	if x != nil && x.Bidfloorcur != nil {
		return *x.Bidfloorcur
	}
	return Default_BidRequest_Imp_Bidfloorcur
}

func (x *BidRequest_Imp) GetClickbrowser() bool {
	if x != nil && x.Clickbrowser != nil {
		return *x.Clickbrowser
	}
	return false
}

func (x *BidRequest_Imp) GetSecure() bool {
	if x != nil && x.Secure != nil {
		return *x.Secure
	}
	return false
}

func (x *BidRequest_Imp) GetIframebuster() []string {
	if x != nil {
		return x.Iframebuster
	}
	return nil
}

func (x *BidRequest_Imp) GetPmp() *BidRequest_Imp_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_Imp) GetNative() *BidRequest_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *BidRequest_Imp) GetExp() int32 {
	if x != nil && x.Exp != nil {
		return *x.Exp
	}
	return 0
}

func (x *BidRequest_Imp) GetStyleIds() []string {
	if x != nil {
		return x.StyleIds
	}
	return nil
}

type BidRequest_App struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id   *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// app官网的域名
	Domain *string `protobuf:"bytes,3,opt,name=domain" json:"domain,omitempty"`
	// App类别
	Cat []ContentCategory `protobuf:"varint,4,rep,name=cat,enum=meitu.ContentCategory" json:"cat,omitempty"`
	// App中当前区段类别
	Sectioncat []ContentCategory `protobuf:"varint,5,rep,name=sectioncat,enum=meitu.ContentCategory" json:"sectioncat,omitempty"`
	// App中当前页面类别
	Pagecat []ContentCategory `protobuf:"varint,6,rep,name=pagecat,enum=meitu.ContentCategory" json:"pagecat,omitempty"`
	Ver     *string           `protobuf:"bytes,7,opt,name=ver" json:"ver,omitempty"`
	// Android: 包名; iOS: ID.
	Bundle *string `protobuf:"bytes,8,opt,name=bundle" json:"bundle,omitempty"`
	// 该App是否有隐私策略, 1 => 有隐私策略, 0 => 没有
	Privacypolicy *bool `protobuf:"varint,9,opt,name=privacypolicy" json:"privacypolicy,omitempty"`
	// 是否付费App, 1 => 付费, 0 => 免费
	Paid     *bool   `protobuf:"varint,10,opt,name=paid" json:"paid,omitempty"`
	Keywords *string `protobuf:"bytes,12,opt,name=keywords" json:"keywords,omitempty"`
	Storeurl *string `protobuf:"bytes,13,opt,name=storeurl" json:"storeurl,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *BidRequest_App) GetCat() []ContentCategory {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_App) GetSectioncat() []ContentCategory {
	if x != nil {
		return x.Sectioncat
	}
	return nil
}

func (x *BidRequest_App) GetPagecat() []ContentCategory {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

func (x *BidRequest_App) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetPrivacypolicy() bool {
	if x != nil && x.Privacypolicy != nil {
		return *x.Privacypolicy
	}
	return false
}

func (x *BidRequest_App) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return false
}

func (x *BidRequest_App) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_App) GetStoreurl() string {
	if x != nil && x.Storeurl != nil {
		return *x.Storeurl
	}
	return ""
}

type BidRequest_Device struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// 1 => 开启了do not track, 0 => 未设置或未开启do not track
	Dnt *bool           `protobuf:"varint,1,opt,name=dnt" json:"dnt,omitempty"`
	Ua  *string         `protobuf:"bytes,2,opt,name=ua" json:"ua,omitempty"`
	Ip  *string         `protobuf:"bytes,3,opt,name=ip" json:"ip,omitempty"`
	Geo *BidRequest_Geo `protobuf:"bytes,4,opt,name=geo" json:"geo,omitempty"`
	// SHA1加密的硬件设备ID(IMEI), 字母采用小写形式
	Didsha1 *string `protobuf:"bytes,5,opt,name=didsha1" json:"didsha1,omitempty"`
	// MD5加密的硬件设备ID(IMEI), 字母采用小写形式
	Didmd5 *string `protobuf:"bytes,6,opt,name=didmd5" json:"didmd5,omitempty"`
	// SHA1加密的平台设备ID(Android ID), 字母采用小写形式
	Dpidsha1 *string `protobuf:"bytes,7,opt,name=dpidsha1" json:"dpidsha1,omitempty"`
	// MD5加密的平台设备ID(Android ID), 字母采用小写形式
	Dpidmd5 *string `protobuf:"bytes,8,opt,name=dpidmd5" json:"dpidmd5,omitempty"`
	Ipv6    *string `protobuf:"bytes,9,opt,name=ipv6" json:"ipv6,omitempty"`
	// 使用 MCC_MNC. 参考:
	// http://en.wikipedia.org/wiki/Mobile_Network_Code
	Carrier *string `protobuf:"bytes,10,opt,name=carrier" json:"carrier,omitempty"`
	// 浏览器语言, ISO-639-1-alpha-2.
	Language *string `protobuf:"bytes,11,opt,name=language" json:"language,omitempty"`
	// 设备厂商 ("Apple").
	Make *string `protobuf:"bytes,12,opt,name=make" json:"make,omitempty"`
	// 设备模型 ("iPhone").
	Model *string `protobuf:"bytes,13,opt,name=model" json:"model,omitempty"`
	// 设备操作系统 ("iOS", "android")
	Os *string `protobuf:"bytes,14,opt,name=os" json:"os,omitempty"`
	// 设备操作系统版本号
	Osv *string `protobuf:"bytes,15,opt,name=osv" json:"osv,omitempty"`
	// 设备硬件版本 ("5S" 对应 iPhone 5S)
	Hwv *string `protobuf:"bytes,24,opt,name=hwv" json:"hwv,omitempty"`
	// 设备的宽的物理像素数
	W *int32 `protobuf:"varint,25,opt,name=w" json:"w,omitempty"`
	// 设备的高的物理像素数
	H *int32 `protobuf:"varint,26,opt,name=h" json:"h,omitempty"`
	// 屏幕分辨率, pixels per linear inch.
	Ppi *int32 `protobuf:"varint,27,opt,name=ppi" json:"ppi,omitempty"`
	// 物理像素与独立像素的比值
	Pxratio *float64 `protobuf:"fixed64,28,opt,name=pxratio" json:"pxratio,omitempty"`
	// 是否支持JavaScript, 1 => 支持, 0 => 不支持
	Js *bool `protobuf:"varint,16,opt,name=js" json:"js,omitempty"`
	// 对于banner中的JS代码, 是否地理位置API可用, 1 => 是, 0 => 否
	Geofetch       *bool           `protobuf:"varint,29,opt,name=geofetch" json:"geofetch,omitempty"`
	Connectiontype *ConnectionType `protobuf:"varint,17,opt,name=connectiontype,enum=meitu.ConnectionType" json:"connectiontype,omitempty"`
	Devicetype     *DeviceType     `protobuf:"varint,18,opt,name=devicetype,enum=meitu.DeviceType" json:"devicetype,omitempty"`
	// 浏览器Flash版本
	Flashver *string `protobuf:"bytes,19,opt,name=flashver" json:"flashver,omitempty"`
	// 广告用ID, Android ID明文或IDFA明文, 或者其他SDK可取得的ID, 字母采用小写形式
	Ifa *string `protobuf:"bytes,20,opt,name=ifa" json:"ifa,omitempty"`
	// SHA1加密的MAC地址, 字母采用小写形式
	Macsha1 *string `protobuf:"bytes,21,opt,name=macsha1" json:"macsha1,omitempty"`
	// MD5加密的MAC地址, 字母采用小写形式
	Macmd5 *string `protobuf:"bytes,22,opt,name=macmd5" json:"macmd5,omitempty"`
	// 是否"Limit Ad Tracking", 1 => 是, 0 => 否
	Lmt *bool `protobuf:"varint,23,opt,name=lmt" json:"lmt,omitempty"`
	// 明文mac,此处只有为联盟流量的时候，才会赋值
	Mac *string `protobuf:"bytes,31,opt,name=mac" json:"mac,omitempty"`
	// 明文imei,此处只有为联盟流量的时候，才会赋值
	Did  *string `protobuf:"bytes,32,opt,name=did" json:"did,omitempty"`
	Oaid *string `protobuf:"bytes,33,opt,name=oaid" json:"oaid,omitempty"`
	Caid *string `protobuf:"bytes,34,opt,name=caid" json:"caid,omitempty"`
	Aaid *string `protobuf:"bytes,35,opt,name=aaid" json:"aaid,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetDnt() bool {
	if x != nil && x.Dnt != nil {
		return *x.Dnt
	}
	return false
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetDidsha1() string {
	if x != nil && x.Didsha1 != nil {
		return *x.Didsha1
	}
	return ""
}

func (x *BidRequest_Device) GetDidmd5() string {
	if x != nil && x.Didmd5 != nil {
		return *x.Didmd5
	}
	return ""
}

func (x *BidRequest_Device) GetDpidsha1() string {
	if x != nil && x.Dpidsha1 != nil {
		return *x.Dpidsha1
	}
	return ""
}

func (x *BidRequest_Device) GetDpidmd5() string {
	if x != nil && x.Dpidmd5 != nil {
		return *x.Dpidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() string {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return ""
}

func (x *BidRequest_Device) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil && x.Hwv != nil {
		return *x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil && x.Ppi != nil {
		return *x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetPxratio() float64 {
	if x != nil && x.Pxratio != nil {
		return *x.Pxratio
	}
	return 0
}

func (x *BidRequest_Device) GetJs() bool {
	if x != nil && x.Js != nil {
		return *x.Js
	}
	return false
}

func (x *BidRequest_Device) GetGeofetch() bool {
	if x != nil && x.Geofetch != nil {
		return *x.Geofetch
	}
	return false
}

func (x *BidRequest_Device) GetConnectiontype() ConnectionType {
	if x != nil && x.Connectiontype != nil {
		return *x.Connectiontype
	}
	return ConnectionType_CONNECTION_UNKNOWN
}

func (x *BidRequest_Device) GetDevicetype() DeviceType {
	if x != nil && x.Devicetype != nil {
		return *x.Devicetype
	}
	return DeviceType_MOBILE
}

func (x *BidRequest_Device) GetFlashver() string {
	if x != nil && x.Flashver != nil {
		return *x.Flashver
	}
	return ""
}

func (x *BidRequest_Device) GetIfa() string {
	if x != nil && x.Ifa != nil {
		return *x.Ifa
	}
	return ""
}

func (x *BidRequest_Device) GetMacsha1() string {
	if x != nil && x.Macsha1 != nil {
		return *x.Macsha1
	}
	return ""
}

func (x *BidRequest_Device) GetMacmd5() string {
	if x != nil && x.Macmd5 != nil {
		return *x.Macmd5
	}
	return ""
}

func (x *BidRequest_Device) GetLmt() bool {
	if x != nil && x.Lmt != nil {
		return *x.Lmt
	}
	return false
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetDid() string {
	if x != nil && x.Did != nil {
		return *x.Did
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetAaid() string {
	if x != nil && x.Aaid != nil {
		return *x.Aaid
	}
	return ""
}

type BidRequest_Geo struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// 纬度, [-90.0, +90.0] 负数表示南方
	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"`
	// 经度, [-180.0, +180.0] 负数表示西方
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"`
	// 国家, ISO-3166-1 Alpha-3编码
	Country *string `protobuf:"bytes,3,opt,name=country" json:"country,omitempty"`
	// 地区, ISO-3166-2编码
	Region *string `protobuf:"bytes,4,opt,name=region" json:"region,omitempty"`
	// 定位数据来源
	Type *LocationType `protobuf:"varint,9,opt,name=type,enum=meitu.LocationType" json:"type,omitempty"`
	// 位置信息精确度, 单位为米
	Accuracy *int32 `protobuf:"varint,11,opt,name=accuracy" json:"accuracy,omitempty"`
	// 距离上次位置信息修正的秒数
	Lastfix   *int32           `protobuf:"varint,12,opt,name=lastfix" json:"lastfix,omitempty"`
	Ipservice *LocationService `protobuf:"varint,13,opt,name=ipservice,enum=meitu.LocationService" json:"ipservice,omitempty"`
	// 当地时间与UTC相差的分钟数.
	Utcoffset *int32 `protobuf:"varint,10,opt,name=utcoffset" json:"utcoffset,omitempty"`
	Provider  *int32 `protobuf:"varint,14,opt,name=provider" json:"provider,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *BidRequest_Geo) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *BidRequest_Geo) GetType() LocationType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return LocationType_GPS_LOCATION
}

func (x *BidRequest_Geo) GetAccuracy() int32 {
	if x != nil && x.Accuracy != nil {
		return *x.Accuracy
	}
	return 0
}

func (x *BidRequest_Geo) GetLastfix() int32 {
	if x != nil && x.Lastfix != nil {
		return *x.Lastfix
	}
	return 0
}

func (x *BidRequest_Geo) GetIpservice() LocationService {
	if x != nil && x.Ipservice != nil {
		return *x.Ipservice
	}
	return LocationService_IP2LOCATION
}

func (x *BidRequest_Geo) GetUtcoffset() int32 {
	if x != nil && x.Utcoffset != nil {
		return *x.Utcoffset
	}
	return 0
}

func (x *BidRequest_Geo) GetProvider() int32 {
	if x != nil && x.Provider != nil {
		return *x.Provider
	}
	return 0
}

type BidRequest_User struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// 出生年份
	Yob *int32 `protobuf:"varint,3,opt,name=yob" json:"yob,omitempty"`
	// M: 男, F: 女, O: 其他, 不填表示未知
	Gender *string `protobuf:"bytes,4,opt,name=gender" json:"gender,omitempty"`
	// 以逗号分割的关键词, 兴趣或内容
	Keywords *string         `protobuf:"bytes,5,opt,name=keywords" json:"keywords,omitempty"`
	Geo      *BidRequest_Geo `protobuf:"bytes,7,opt,name=geo" json:"geo,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_User) GetYob() int32 {
	if x != nil && x.Yob != nil {
		return *x.Yob
	}
	return 0
}

func (x *BidRequest_User) GetGender() string {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return ""
}

func (x *BidRequest_User) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_User) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

type BidRequest_Imp_Banner struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// banner确切的宽
	W *int32 `protobuf:"varint,1,opt,name=w" json:"w,omitempty"`
	// banner确切的高
	H *int32 `protobuf:"varint,2,opt,name=h" json:"h,omitempty"`
	// banner可接受的宽高组合
	Format []*BidRequest_Imp_Banner_Format `protobuf:"bytes,11,rep,name=format" json:"format,omitempty"`
	Id     *string                         `protobuf:"bytes,3,opt,name=id" json:"id,omitempty"`
	Pos    *AdPosition                     `protobuf:"varint,4,opt,name=pos,enum=meitu.AdPosition" json:"pos,omitempty"`
	// 禁止的banner类别
	Btype []BannerAdType `protobuf:"varint,5,rep,packed,name=btype,enum=meitu.BannerAdType" json:"btype,omitempty"`
	// 禁止的创意属性
	Battr []CreativeAttribute `protobuf:"varint,6,rep,packed,name=battr,enum=meitu.CreativeAttribute" json:"battr,omitempty"`
	// MIME类别白名单, 如"image/jpg", "image/gif",
	// "application/x-shockwave-flash".
	Mimes []string `protobuf:"bytes,7,rep,name=mimes" json:"mimes,omitempty"`
	// 1 => 是topframe, 0 => 不是topframe
	Topframe *bool `protobuf:"varint,8,opt,name=topframe" json:"topframe,omitempty"`
	// banner可扩展的方向
	Expdir []ExpandableDirection `protobuf:"varint,9,rep,packed,name=expdir,enum=meitu.ExpandableDirection" json:"expdir,omitempty"`
	Api    []APIFramework        `protobuf:"varint,10,rep,packed,name=api,enum=meitu.APIFramework" json:"api,omitempty"`
}

func (x *BidRequest_Imp_Banner) Reset() {
	*x = BidRequest_Imp_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Banner) ProtoMessage() {}

func (x *BidRequest_Imp_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Banner.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Banner) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Banner) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetFormat() []*BidRequest_Imp_Banner_Format {
	if x != nil {
		return x.Format
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Banner) GetPos() AdPosition {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return AdPosition_UNKNOWN
}

func (x *BidRequest_Imp_Banner) GetBtype() []BannerAdType {
	if x != nil {
		return x.Btype
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetBattr() []CreativeAttribute {
	if x != nil {
		return x.Battr
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetTopframe() bool {
	if x != nil && x.Topframe != nil {
		return *x.Topframe
	}
	return false
}

func (x *BidRequest_Imp_Banner) GetExpdir() []ExpandableDirection {
	if x != nil {
		return x.Expdir
	}
	return nil
}

func (x *BidRequest_Imp_Banner) GetApi() []APIFramework {
	if x != nil {
		return x.Api
	}
	return nil
}

type BidRequest_Imp_Native struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Types that are assignable to RequestOneof:
	//	*BidRequest_Imp_Native_Request
	//	*BidRequest_Imp_Native_RequestNative
	RequestOneof isBidRequest_Imp_Native_RequestOneof `protobuf_oneof:"request_oneof"`
	// OpenRTB中native广告协议版本号
	// "1.0" 对应 OpenRTB 2.3; "1.1" 对应 OpenRTB 2.4
	Ver *string `protobuf:"bytes,2,opt,name=ver" json:"ver,omitempty"`
	// 支持的API框架
	Api []APIFramework `protobuf:"varint,3,rep,packed,name=api,enum=meitu.APIFramework" json:"api,omitempty"`
	// 禁止的创意属性
	Battr []CreativeAttribute `protobuf:"varint,4,rep,packed,name=battr,enum=meitu.CreativeAttribute" json:"battr,omitempty"`
}

func (x *BidRequest_Imp_Native) Reset() {
	*x = BidRequest_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native) ProtoMessage() {}

func (x *BidRequest_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (m *BidRequest_Imp_Native) GetRequestOneof() isBidRequest_Imp_Native_RequestOneof {
	if m != nil {
		return m.RequestOneof
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetRequest() string {
	if x, ok := x.GetRequestOneof().(*BidRequest_Imp_Native_Request); ok {
		return x.Request
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetRequestNative() *NativeRequest {
	if x, ok := x.GetRequestOneof().(*BidRequest_Imp_Native_RequestNative); ok {
		return x.RequestNative
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetApi() []APIFramework {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetBattr() []CreativeAttribute {
	if x != nil {
		return x.Battr
	}
	return nil
}

type isBidRequest_Imp_Native_RequestOneof interface {
	isBidRequest_Imp_Native_RequestOneof()
}

type BidRequest_Imp_Native_Request struct {
	Request string `protobuf:"bytes,1,opt,name=request,oneof"`
}

type BidRequest_Imp_Native_RequestNative struct {
	RequestNative *NativeRequest `protobuf:"bytes,6,opt,name=request_native,json=requestNative,oneof"`
}

func (*BidRequest_Imp_Native_Request) isBidRequest_Imp_Native_RequestOneof() {}

func (*BidRequest_Imp_Native_RequestNative) isBidRequest_Imp_Native_RequestOneof() {}

type BidRequest_Imp_Pmp struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Deal对象可得性, 0 => 所有bid均可得, 1 => bid由deal限定
	PrivateAuction *bool                      `protobuf:"varint,1,opt,name=private_auction,json=privateAuction,def=0" json:"private_auction,omitempty"`
	Deals          []*BidRequest_Imp_Pmp_Deal `protobuf:"bytes,2,rep,name=deals" json:"deals,omitempty"`
}

// Default values for BidRequest_Imp_Pmp fields.
const (
	Default_BidRequest_Imp_Pmp_PrivateAuction = bool(false)
)

func (x *BidRequest_Imp_Pmp) Reset() {
	*x = BidRequest_Imp_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *BidRequest_Imp_Pmp) GetPrivateAuction() bool {
	if x != nil && x.PrivateAuction != nil {
		return *x.PrivateAuction
	}
	return Default_BidRequest_Imp_Pmp_PrivateAuction
}

func (x *BidRequest_Imp_Pmp) GetDeals() []*BidRequest_Imp_Pmp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type BidRequest_Imp_Banner_Format struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	W *int32 `protobuf:"varint,1,opt,name=w" json:"w,omitempty"`
	H *int32 `protobuf:"varint,2,opt,name=h" json:"h,omitempty"`
}

func (x *BidRequest_Imp_Banner_Format) Reset() {
	*x = BidRequest_Imp_Banner_Format{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Banner_Format) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Banner_Format) ProtoMessage() {}

func (x *BidRequest_Imp_Banner_Format) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Banner_Format.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Banner_Format) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *BidRequest_Imp_Banner_Format) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Banner_Format) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type BidRequest_Imp_Pmp_Deal struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// 底价, 单位: 分(人民币)/千次展示
	Bidfloor *float64 `protobuf:"fixed64,2,opt,name=bidfloor,def=0" json:"bidfloor,omitempty"`
	// 货币, 使用ISO-4217 alpha代码.
	Bidfloorcur *string `protobuf:"bytes,3,opt,name=bidfloorcur,def=CNY" json:"bidfloorcur,omitempty"`
	// 买方白名单
	Wseat []string `protobuf:"bytes,4,rep,name=wseat" json:"wseat,omitempty"`
	// 广告主domain白名单
	Wadomain []string     `protobuf:"bytes,5,rep,name=wadomain" json:"wadomain,omitempty"`
	At       *AuctionType `protobuf:"varint,6,opt,name=at,enum=meitu.AuctionType" json:"at,omitempty"`
}

// Default values for BidRequest_Imp_Pmp_Deal fields.
const (
	Default_BidRequest_Imp_Pmp_Deal_Bidfloor    = float64(0)
	Default_BidRequest_Imp_Pmp_Deal_Bidfloorcur = string("CNY")
)

func (x *BidRequest_Imp_Pmp_Deal) Reset() {
	*x = BidRequest_Imp_Pmp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp_Deal) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp_Deal) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{0, 0, 2, 0}
}

func (x *BidRequest_Imp_Pmp_Deal) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Pmp_Deal) GetBidfloor() float64 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return Default_BidRequest_Imp_Pmp_Deal_Bidfloor
}

func (x *BidRequest_Imp_Pmp_Deal) GetBidfloorcur() string {
	if x != nil && x.Bidfloorcur != nil {
		return *x.Bidfloorcur
	}
	return Default_BidRequest_Imp_Pmp_Deal_Bidfloorcur
}

func (x *BidRequest_Imp_Pmp_Deal) GetWseat() []string {
	if x != nil {
		return x.Wseat
	}
	return nil
}

func (x *BidRequest_Imp_Pmp_Deal) GetWadomain() []string {
	if x != nil {
		return x.Wadomain
	}
	return nil
}

func (x *BidRequest_Imp_Pmp_Deal) GetAt() AuctionType {
	if x != nil && x.At != nil {
		return *x.At
	}
	return AuctionType_FIRST_PRICE
}

type BidResponse_SeatBid struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Bid []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`
	// 买方(广告主、代理)ID
	Seat *string `protobuf:"bytes,2,opt,name=seat" json:"seat,omitempty"`
	// 是否整批购买, 1 => 是, 0 => 否
	Group *bool `protobuf:"varint,3,opt,name=group,def=0" json:"group,omitempty"`
}

// Default values for BidResponse_SeatBid fields.
const (
	Default_BidResponse_SeatBid_Group = bool(false)
)

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() string {
	if x != nil && x.Seat != nil {
		return *x.Seat
	}
	return ""
}

func (x *BidResponse_SeatBid) GetGroup() bool {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return Default_BidResponse_SeatBid_Group
}

type BidResponse_SeatBid_Bid struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// 买方生成的ID, 用以记录/跟踪
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// bid request中对应的imp
	Impid *string `protobuf:"bytes,2,req,name=impid" json:"impid,omitempty"`
	// 出价, 单位: 分(人民币)/千次展示
	Price *float64 `protobuf:"fixed64,3,req,name=price" json:"price,omitempty"`
	Adid  *string  `protobuf:"bytes,4,opt,name=adid" json:"adid,omitempty"`
	// Win notice URL
	// 价格宏%%WINNING_PRICE%%可在nurl中使用，其含义为加密后的竞拍成交价格，单位: 分（人民币）/ 千次展示
	Nurl *string `protobuf:"bytes,5,opt,name=nurl" json:"nurl,omitempty"`
	// 推荐使用 adm_native, 暂不支持 adm
	// 广告展示内容, 若未填充则:
	//   曝光监测url取值为素材审核接口中的imp_track_urls,
	//   点击监测url取值为素材审核接口中的clk_through_url
	//
	// Types that are assignable to AdmOneof:
	//	*BidResponse_SeatBid_Bid_Adm
	//	*BidResponse_SeatBid_Bid_AdmNative
	AdmOneof isBidResponse_SeatBid_Bid_AdmOneof `protobuf_oneof:"adm_oneof"`
	// 广告主domain
	Adomain []string `protobuf:"bytes,7,rep,name=adomain" json:"adomain,omitempty"`
	// Android: 包名; iOS: ID.
	Bundle *string `protobuf:"bytes,14,opt,name=bundle" json:"bundle,omitempty"`
	// 用于审核备案的素材url
	Iurl *string `protobuf:"bytes,8,opt,name=iurl" json:"iurl,omitempty"`
	// Campaign ID
	Cid *string `protobuf:"bytes,9,opt,name=cid" json:"cid,omitempty"`
	// 创意ID
	Crid *string `protobuf:"bytes,10,opt,name=crid" json:"crid,omitempty"`
	// 创意的类别
	Cat []ContentCategory `protobuf:"varint,15,rep,name=cat,enum=meitu.ContentCategory" json:"cat,omitempty"`
	// 创意的属性
	Attr   []CreativeAttribute `protobuf:"varint,11,rep,packed,name=attr,enum=meitu.CreativeAttribute" json:"attr,omitempty"`
	Api    *APIFramework       `protobuf:"varint,18,opt,name=api,enum=meitu.APIFramework" json:"api,omitempty"`
	Dealid *string             `protobuf:"bytes,13,opt,name=dealid" json:"dealid,omitempty"`
	W      *int32              `protobuf:"varint,16,opt,name=w" json:"w,omitempty"`
	H      *int32              `protobuf:"varint,17,opt,name=h" json:"h,omitempty"`
	// 价格宏%%WINNING_PRICE%%可在imp_track_urls中使用，其含义为加密后的竞拍成交价格，单位: 分（人民币）/ 千次展示
	ImpTrackUrls  []string `protobuf:"bytes,19,rep,name=imp_track_urls,json=impTrackUrls" json:"imp_track_urls,omitempty"`
	ClkThroughUrl *string  `protobuf:"bytes,20,opt,name=clk_through_url,json=clkThroughUrl" json:"clk_through_url,omitempty"`
	// 买方愿意等待的从竞拍到实际展示的间隔秒数
	Exp *int32 `protobuf:"varint,21,opt,name=exp" json:"exp,omitempty"`
	// 该response的样式id (必须属于request.style_ids中的一个)
	StyleId      *string `protobuf:"bytes,22,opt,name=style_id,json=styleId" json:"style_id,omitempty"`
	CreativeType *int32  `protobuf:"varint,23,opt,name=creative_type,json=creativeType" json:"creative_type,omitempty"`
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpid() string {
	if x != nil && x.Impid != nil {
		return *x.Impid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetAdid() string {
	if x != nil && x.Adid != nil {
		return *x.Adid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetNurl() string {
	if x != nil && x.Nurl != nil {
		return *x.Nurl
	}
	return ""
}

func (m *BidResponse_SeatBid_Bid) GetAdmOneof() isBidResponse_SeatBid_Bid_AdmOneof {
	if m != nil {
		return m.AdmOneof
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAdm() string {
	if x, ok := x.GetAdmOneof().(*BidResponse_SeatBid_Bid_Adm); ok {
		return x.Adm
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetAdmNative() *NativeResponse {
	if x, ok := x.GetAdmOneof().(*BidResponse_SeatBid_Bid_AdmNative); ok {
		return x.AdmNative
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAdomain() []string {
	if x != nil {
		return x.Adomain
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetIurl() string {
	if x != nil && x.Iurl != nil {
		return *x.Iurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCid() string {
	if x != nil && x.Cid != nil {
		return *x.Cid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCat() []ContentCategory {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAttr() []CreativeAttribute {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetApi() APIFramework {
	if x != nil && x.Api != nil {
		return *x.Api
	}
	return APIFramework_VPAID_1
}

func (x *BidResponse_SeatBid_Bid) GetDealid() string {
	if x != nil && x.Dealid != nil {
		return *x.Dealid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetImpTrackUrls() []string {
	if x != nil {
		return x.ImpTrackUrls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetClkThroughUrl() string {
	if x != nil && x.ClkThroughUrl != nil {
		return *x.ClkThroughUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetExp() int32 {
	if x != nil && x.Exp != nil {
		return *x.Exp
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetStyleId() string {
	if x != nil && x.StyleId != nil {
		return *x.StyleId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCreativeType() int32 {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return 0
}

type isBidResponse_SeatBid_Bid_AdmOneof interface {
	isBidResponse_SeatBid_Bid_AdmOneof()
}

type BidResponse_SeatBid_Bid_Adm struct {
	// 点击宏%%CLICK_URL%%可在adm中使用,
	// 优先取值为实时响应返回的clk_through_url, 若响应中无对应值则取素材审核接口中的clk_through_url
	Adm string `protobuf:"bytes,6,opt,name=adm,oneof"`
}

type BidResponse_SeatBid_Bid_AdmNative struct {
	AdmNative *NativeResponse `protobuf:"bytes,50,opt,name=adm_native,json=admNative,oneof"`
}

func (*BidResponse_SeatBid_Bid_Adm) isBidResponse_SeatBid_Bid_AdmOneof() {}

func (*BidResponse_SeatBid_Bid_AdmNative) isBidResponse_SeatBid_Bid_AdmOneof() {}

type NativeRequest_Asset struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id *int32 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// 1 => 必需, 0 => 非必需
	Required *bool `protobuf:"varint,2,opt,name=required,def=0" json:"required,omitempty"`
	// Types that are assignable to AssetOneof:
	//	*NativeRequest_Asset_Title_
	//	*NativeRequest_Asset_Img
	//	*NativeRequest_Asset_Data_
	//	*NativeRequest_Asset_Video_
	AssetOneof isNativeRequest_Asset_AssetOneof `protobuf_oneof:"asset_oneof"`
}

// Default values for NativeRequest_Asset fields.
const (
	Default_NativeRequest_Asset_Required = bool(false)
)

func (x *NativeRequest_Asset) Reset() {
	*x = NativeRequest_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset) ProtoMessage() {}

func (x *NativeRequest_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2, 0}
}

func (x *NativeRequest_Asset) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NativeRequest_Asset) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return Default_NativeRequest_Asset_Required
}

func (m *NativeRequest_Asset) GetAssetOneof() isNativeRequest_Asset_AssetOneof {
	if m != nil {
		return m.AssetOneof
	}
	return nil
}

func (x *NativeRequest_Asset) GetTitle() *NativeRequest_Asset_Title {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Title_); ok {
		return x.Title
	}
	return nil
}

func (x *NativeRequest_Asset) GetImg() *NativeRequest_Asset_Image {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Img); ok {
		return x.Img
	}
	return nil
}

func (x *NativeRequest_Asset) GetData() *NativeRequest_Asset_Data {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Data_); ok {
		return x.Data
	}
	return nil
}

func (x *NativeRequest_Asset) GetVideo() *NativeRequest_Asset_Video {
	if x, ok := x.GetAssetOneof().(*NativeRequest_Asset_Video_); ok {
		return x.Video
	}
	return nil
}

type isNativeRequest_Asset_AssetOneof interface {
	isNativeRequest_Asset_AssetOneof()
}

type NativeRequest_Asset_Title_ struct {
	Title *NativeRequest_Asset_Title `protobuf:"bytes,3,opt,name=title,oneof"`
}

type NativeRequest_Asset_Img struct {
	Img *NativeRequest_Asset_Image `protobuf:"bytes,4,opt,name=img,oneof"`
}

type NativeRequest_Asset_Data_ struct {
	Data *NativeRequest_Asset_Data `protobuf:"bytes,6,opt,name=data,oneof"`
}

type NativeRequest_Asset_Video_ struct {
	Video *NativeRequest_Asset_Video `protobuf:"bytes,8,opt,name=video,oneof"`
}

func (*NativeRequest_Asset_Title_) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Img) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Data_) isNativeRequest_Asset_AssetOneof() {}

func (*NativeRequest_Asset_Video_) isNativeRequest_Asset_AssetOneof() {}

type NativeRequest_Asset_Title struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// 标题最大长度
	// 推荐长度: 25, 90, 140.
	Len *int32 `protobuf:"varint,1,req,name=len" json:"len,omitempty"`
}

func (x *NativeRequest_Asset_Title) Reset() {
	*x = NativeRequest_Asset_Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Title) ProtoMessage() {}

func (x *NativeRequest_Asset_Title) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Title.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Title) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *NativeRequest_Asset_Title) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

type NativeRequest_Asset_Image struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Type  *ImageAssetType `protobuf:"varint,1,opt,name=type,enum=meitu.ImageAssetType" json:"type,omitempty"`
	W     *int32          `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	H     *int32          `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
	Wmin  *int32          `protobuf:"varint,4,opt,name=wmin" json:"wmin,omitempty"`
	Hmin  *int32          `protobuf:"varint,5,opt,name=hmin" json:"hmin,omitempty"`
	Mimes []string        `protobuf:"bytes,6,rep,name=mimes" json:"mimes,omitempty"`
}

func (x *NativeRequest_Asset_Image) Reset() {
	*x = NativeRequest_Asset_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Image) ProtoMessage() {}

func (x *NativeRequest_Asset_Image) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Image.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Image) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *NativeRequest_Asset_Image) GetType() ImageAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ImageAssetType_ICON
}

func (x *NativeRequest_Asset_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetWmin() int32 {
	if x != nil && x.Wmin != nil {
		return *x.Wmin
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetHmin() int32 {
	if x != nil && x.Hmin != nil {
		return *x.Hmin
	}
	return 0
}

func (x *NativeRequest_Asset_Image) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

type NativeRequest_Asset_Data struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Type *DataAssetType `protobuf:"varint,1,req,name=type,enum=meitu.DataAssetType" json:"type,omitempty"`
	Len  *int32         `protobuf:"varint,2,opt,name=len" json:"len,omitempty"`
}

func (x *NativeRequest_Asset_Data) Reset() {
	*x = NativeRequest_Asset_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Data) ProtoMessage() {}

func (x *NativeRequest_Asset_Data) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Data.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Data) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2, 0, 2}
}

func (x *NativeRequest_Asset_Data) GetType() DataAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return DataAssetType_SPONSORED
}

func (x *NativeRequest_Asset_Data) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

type NativeRequest_Asset_Video struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	W *int32 `protobuf:"varint,1,opt,name=w" json:"w,omitempty"` // 视频宽
	H *int32 `protobuf:"varint,2,opt,name=h" json:"h,omitempty"` // 视频高
}

func (x *NativeRequest_Asset_Video) Reset() {
	*x = NativeRequest_Asset_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeRequest_Asset_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeRequest_Asset_Video) ProtoMessage() {}

func (x *NativeRequest_Asset_Video) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeRequest_Asset_Video.ProtoReflect.Descriptor instead.
func (*NativeRequest_Asset_Video) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{2, 0, 3}
}

func (x *NativeRequest_Asset_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeRequest_Asset_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type NativeResponse_Link struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Url            *string                       `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	Clicktrackers  []string                      `protobuf:"bytes,2,rep,name=clicktrackers" json:"clicktrackers,omitempty"`
	Fallback       *string                       `protobuf:"bytes,3,opt,name=fallback" json:"fallback,omitempty"`
	Download       *NativeResponse_Link_Download `protobuf:"bytes,4,opt,name=download" json:"download,omitempty"`
	Game           *NativeResponse_Link_Game     `protobuf:"bytes,5,opt,name=game" json:"game,omitempty"`
	Applet         *NativeResponse_Link_Applet   `protobuf:"bytes,6,opt,name=applet" json:"applet,omitempty"`
	Dplinktrackers []string                      `protobuf:"bytes,7,rep,name=dplinktrackers" json:"dplinktrackers,omitempty"`
}

func (x *NativeResponse_Link) Reset() {
	*x = NativeResponse_Link{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Link) ProtoMessage() {}

func (x *NativeResponse_Link) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Link.ProtoReflect.Descriptor instead.
func (*NativeResponse_Link) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 0}
}

func (x *NativeResponse_Link) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Link) GetClicktrackers() []string {
	if x != nil {
		return x.Clicktrackers
	}
	return nil
}

func (x *NativeResponse_Link) GetFallback() string {
	if x != nil && x.Fallback != nil {
		return *x.Fallback
	}
	return ""
}

func (x *NativeResponse_Link) GetDownload() *NativeResponse_Link_Download {
	if x != nil {
		return x.Download
	}
	return nil
}

func (x *NativeResponse_Link) GetGame() *NativeResponse_Link_Game {
	if x != nil {
		return x.Game
	}
	return nil
}

func (x *NativeResponse_Link) GetApplet() *NativeResponse_Link_Applet {
	if x != nil {
		return x.Applet
	}
	return nil
}

func (x *NativeResponse_Link) GetDplinktrackers() []string {
	if x != nil {
		return x.Dplinktrackers
	}
	return nil
}

type NativeResponse_Asset struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Id *int32 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// 1 => 必需, 0 => 非必需
	Required *bool `protobuf:"varint,2,opt,name=required,def=0" json:"required,omitempty"`
	// Types that are assignable to AssetOneof:
	//	*NativeResponse_Asset_Title_
	//	*NativeResponse_Asset_Img
	//	*NativeResponse_Asset_Data_
	//	*NativeResponse_Asset_Video_
	//	*NativeResponse_Asset_Groupimg
	AssetOneof isNativeResponse_Asset_AssetOneof `protobuf_oneof:"asset_oneof"`
	Link       *NativeResponse_Link              `protobuf:"bytes,7,opt,name=link" json:"link,omitempty"`
}

// Default values for NativeResponse_Asset fields.
const (
	Default_NativeResponse_Asset_Required = bool(false)
)

func (x *NativeResponse_Asset) Reset() {
	*x = NativeResponse_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset) ProtoMessage() {}

func (x *NativeResponse_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1}
}

func (x *NativeResponse_Asset) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *NativeResponse_Asset) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return Default_NativeResponse_Asset_Required
}

func (m *NativeResponse_Asset) GetAssetOneof() isNativeResponse_Asset_AssetOneof {
	if m != nil {
		return m.AssetOneof
	}
	return nil
}

func (x *NativeResponse_Asset) GetTitle() *NativeResponse_Asset_Title {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Title_); ok {
		return x.Title
	}
	return nil
}

func (x *NativeResponse_Asset) GetImg() *NativeResponse_Asset_Image {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Img); ok {
		return x.Img
	}
	return nil
}

func (x *NativeResponse_Asset) GetData() *NativeResponse_Asset_Data {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Data_); ok {
		return x.Data
	}
	return nil
}

func (x *NativeResponse_Asset) GetVideo() *NativeResponse_Asset_Video {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Video_); ok {
		return x.Video
	}
	return nil
}

func (x *NativeResponse_Asset) GetGroupimg() *NativeResponse_Asset_GroupImage {
	if x, ok := x.GetAssetOneof().(*NativeResponse_Asset_Groupimg); ok {
		return x.Groupimg
	}
	return nil
}

func (x *NativeResponse_Asset) GetLink() *NativeResponse_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

type isNativeResponse_Asset_AssetOneof interface {
	isNativeResponse_Asset_AssetOneof()
}

type NativeResponse_Asset_Title_ struct {
	Title *NativeResponse_Asset_Title `protobuf:"bytes,3,opt,name=title,oneof"`
}

type NativeResponse_Asset_Img struct {
	Img *NativeResponse_Asset_Image `protobuf:"bytes,4,opt,name=img,oneof"`
}

type NativeResponse_Asset_Data_ struct {
	Data *NativeResponse_Asset_Data `protobuf:"bytes,6,opt,name=data,oneof"`
}

type NativeResponse_Asset_Video_ struct {
	Video *NativeResponse_Asset_Video `protobuf:"bytes,8,opt,name=video,oneof"`
}

type NativeResponse_Asset_Groupimg struct {
	// 组图
	Groupimg *NativeResponse_Asset_GroupImage `protobuf:"bytes,9,opt,name=groupimg,oneof"`
}

func (*NativeResponse_Asset_Title_) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Img) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Data_) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Video_) isNativeResponse_Asset_AssetOneof() {}

func (*NativeResponse_Asset_Groupimg) isNativeResponse_Asset_AssetOneof() {}

type NativeResponse_Link_Download struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageName *string `protobuf:"bytes,1,req,name=package_name,json=packageName" json:"package_name,omitempty"`
	VersionCode *string `protobuf:"bytes,2,req,name=version_code,json=versionCode" json:"version_code,omitempty"`
	AppName     *string `protobuf:"bytes,3,req,name=app_name,json=appName" json:"app_name,omitempty"`
	DetailUrl   *string `protobuf:"bytes,4,opt,name=detail_url,json=detailUrl" json:"detail_url,omitempty"`
	Intent      *string `protobuf:"bytes,5,opt,name=intent" json:"intent,omitempty"`
}

func (x *NativeResponse_Link_Download) Reset() {
	*x = NativeResponse_Link_Download{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Link_Download) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Link_Download) ProtoMessage() {}

func (x *NativeResponse_Link_Download) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Link_Download.ProtoReflect.Descriptor instead.
func (*NativeResponse_Link_Download) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *NativeResponse_Link_Download) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *NativeResponse_Link_Download) GetVersionCode() string {
	if x != nil && x.VersionCode != nil {
		return *x.VersionCode
	}
	return ""
}

func (x *NativeResponse_Link_Download) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *NativeResponse_Link_Download) GetDetailUrl() string {
	if x != nil && x.DetailUrl != nil {
		return *x.DetailUrl
	}
	return ""
}

func (x *NativeResponse_Link_Download) GetIntent() string {
	if x != nil && x.Intent != nil {
		return *x.Intent
	}
	return ""
}

type NativeResponse_Link_Game struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       *string `protobuf:"bytes,1,req,name=type" json:"type,omitempty"`
	Platform   *string `protobuf:"bytes,2,opt,name=platform" json:"platform,omitempty"`
	Id         *string `protobuf:"bytes,3,req,name=id" json:"id,omitempty"`
	Url        *string `protobuf:"bytes,4,req,name=url" json:"url,omitempty"`
	SubpackUrl *string `protobuf:"bytes,5,opt,name=subpack_url,json=subpackUrl" json:"subpack_url,omitempty"`
	AdConfigId *string `protobuf:"bytes,6,req,name=ad_config_id,json=adConfigId" json:"ad_config_id,omitempty"`
}

func (x *NativeResponse_Link_Game) Reset() {
	*x = NativeResponse_Link_Game{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Link_Game) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Link_Game) ProtoMessage() {}

func (x *NativeResponse_Link_Game) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Link_Game.ProtoReflect.Descriptor instead.
func (*NativeResponse_Link_Game) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 0, 1}
}

func (x *NativeResponse_Link_Game) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *NativeResponse_Link_Game) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

func (x *NativeResponse_Link_Game) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *NativeResponse_Link_Game) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Link_Game) GetSubpackUrl() string {
	if x != nil && x.SubpackUrl != nil {
		return *x.SubpackUrl
	}
	return ""
}

func (x *NativeResponse_Link_Game) GetAdConfigId() string {
	if x != nil && x.AdConfigId != nil {
		return *x.AdConfigId
	}
	return ""
}

type NativeResponse_Link_Applet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username *string `protobuf:"bytes,1,req,name=username" json:"username,omitempty"`
	Type     *string `protobuf:"bytes,2,req,name=type" json:"type,omitempty"`
	Path     *string `protobuf:"bytes,3,req,name=path" json:"path,omitempty"`
}

func (x *NativeResponse_Link_Applet) Reset() {
	*x = NativeResponse_Link_Applet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Link_Applet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Link_Applet) ProtoMessage() {}

func (x *NativeResponse_Link_Applet) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Link_Applet.ProtoReflect.Descriptor instead.
func (*NativeResponse_Link_Applet) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 0, 2}
}

func (x *NativeResponse_Link_Applet) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *NativeResponse_Link_Applet) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *NativeResponse_Link_Applet) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

type NativeResponse_Asset_Title struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Text *string `protobuf:"bytes,1,req,name=text" json:"text,omitempty"`
}

func (x *NativeResponse_Asset_Title) Reset() {
	*x = NativeResponse_Asset_Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Title) ProtoMessage() {}

func (x *NativeResponse_Asset_Title) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Title.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Title) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1, 0}
}

func (x *NativeResponse_Asset_Title) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

type NativeResponse_Asset_Image struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Url *string `protobuf:"bytes,1,req,name=url" json:"url,omitempty"`
	W   *int32  `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	H   *int32  `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
}

func (x *NativeResponse_Asset_Image) Reset() {
	*x = NativeResponse_Asset_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Image) ProtoMessage() {}

func (x *NativeResponse_Asset_Image) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Image.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Image) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1, 1}
}

func (x *NativeResponse_Asset_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Asset_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeResponse_Asset_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type NativeResponse_Asset_GroupImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Img []*NativeResponse_Asset_Image `protobuf:"bytes,1,rep,name=img" json:"img,omitempty"`
}

func (x *NativeResponse_Asset_GroupImage) Reset() {
	*x = NativeResponse_Asset_GroupImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_GroupImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_GroupImage) ProtoMessage() {}

func (x *NativeResponse_Asset_GroupImage) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_GroupImage.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_GroupImage) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1, 2}
}

func (x *NativeResponse_Asset_GroupImage) GetImg() []*NativeResponse_Asset_Image {
	if x != nil {
		return x.Img
	}
	return nil
}

type NativeResponse_Asset_Video struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Url      *string `protobuf:"bytes,1,req,name=url" json:"url,omitempty"`
	Duration *int32  `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"` // 视频时长，单位秒
	W        *int32  `protobuf:"varint,3,opt,name=w" json:"w,omitempty"`               // 视频宽
	H        *int32  `protobuf:"varint,4,opt,name=h" json:"h,omitempty"`               // 视频高
}

func (x *NativeResponse_Asset_Video) Reset() {
	*x = NativeResponse_Asset_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Video) ProtoMessage() {}

func (x *NativeResponse_Asset_Video) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Video.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Video) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1, 3}
}

func (x *NativeResponse_Asset_Video) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *NativeResponse_Asset_Video) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *NativeResponse_Asset_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *NativeResponse_Asset_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type NativeResponse_Asset_Data struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	Label *string `protobuf:"bytes,1,opt,name=label" json:"label,omitempty"`
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *NativeResponse_Asset_Data) Reset() {
	*x = NativeResponse_Asset_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_meitu_bidding_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NativeResponse_Asset_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NativeResponse_Asset_Data) ProtoMessage() {}

func (x *NativeResponse_Asset_Data) ProtoReflect() protoreflect.Message {
	mi := &file_meitu_bidding_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NativeResponse_Asset_Data.ProtoReflect.Descriptor instead.
func (*NativeResponse_Asset_Data) Descriptor() ([]byte, []int) {
	return file_meitu_bidding_proto_rawDescGZIP(), []int{3, 1, 4}
}

func (x *NativeResponse_Asset_Data) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *NativeResponse_Asset_Data) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

var File_meitu_bidding_proto protoreflect.FileDescriptor

var file_meitu_bidding_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2d, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x22, 0xde, 0x1c, 0x0a,
	0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x03, 0x69,
	0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52,
	0x03, 0x69, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x30, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x2a, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x02, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e,
	0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x3a, 0x0c, 0x53, 0x45, 0x43,
	0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x52, 0x02, 0x61, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x6d, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x6d, 0x61,
	0x78, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74, 0x12, 0x1f, 0x0a, 0x07, 0x61, 0x6c, 0x6c, 0x69, 0x6d,
	0x70, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x07, 0x61, 0x6c, 0x6c, 0x69, 0x6d, 0x70, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x75, 0x72, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x75, 0x72, 0x12, 0x2a, 0x0a, 0x04, 0x62, 0x63,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x04, 0x62, 0x63, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x64, 0x76, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x64, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61,
	0x70, 0x70, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x70, 0x70, 0x12, 0x19,
	0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x61, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x3a,
	0x01, 0x30, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x66, 0x66, 0x69, 0x6c, 0x69, 0x61, 0x74, 0x65, 0x1a,
	0xf1, 0x0b, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x26, 0x0a,
	0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x3a, 0x01, 0x30, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x25,
	0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x03, 0x43, 0x4e, 0x59, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x63, 0x75, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x62, 0x72,
	0x6f, 0x77, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x62, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x62,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70,
	0x6d, 0x70, 0x12, 0x34, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x1a, 0xc2, 0x03, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x3b,
	0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x03, 0x70,
	0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x6f, 0x73,
	0x12, 0x2d, 0x0a, 0x05, 0x62, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x32, 0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62, 0x61,
	0x74, 0x74, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x70,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x70,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x64, 0x69, 0x72, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x45, 0x78,
	0x70, 0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x02, 0x10, 0x01, 0x52, 0x06, 0x65, 0x78, 0x70, 0x64, 0x69, 0x72, 0x12, 0x29, 0x0a,
	0x03, 0x61, 0x70, 0x69, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6d, 0x65, 0x69,
	0x74, 0x75, 0x2e, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42,
	0x02, 0x10, 0x01, 0x52, 0x03, 0x61, 0x70, 0x69, 0x1a, 0x2b, 0x0a, 0x06, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x2a, 0x05,
	0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xec, 0x01, 0x0a,
	0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1a, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x65,
	0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x02, 0x10, 0x01, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12,
	0x32, 0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62, 0x61,
	0x74, 0x74, 0x72, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x42, 0x0f, 0x0a, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x1a, 0xae, 0x02, 0x0a, 0x03,
	0x50, 0x6d, 0x70, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x41, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x44, 0x65,
	0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x1a, 0xb9, 0x01, 0x0a, 0x04, 0x44, 0x65,
	0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x3a, 0x01, 0x30, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x12, 0x25, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x03, 0x43, 0x4e, 0x59, 0x52, 0x0b, 0x62, 0x69, 0x64,
	0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x73, 0x65, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x77, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x77, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x02, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x61, 0x74, 0x2a, 0x05,
	0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64,
	0x10, 0x90, 0x4e, 0x1a, 0xf8, 0x02, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x03, 0x63, 0x61,
	0x74, 0x12, 0x36, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x63, 0x61, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x70, 0x61, 0x67,
	0x65, 0x63, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69,
	0x74, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x63, 0x79, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x75, 0x72, 0x6c, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xb2,
	0x06, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x64, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x75,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x27, 0x0a, 0x03, 0x67,
	0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52,
	0x03, 0x67, 0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x64, 0x73, 0x68, 0x61, 0x31, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x69, 0x64, 0x73, 0x68, 0x61, 0x31, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x70, 0x69, 0x64, 0x73, 0x68,
	0x61, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x70, 0x69, 0x64, 0x73, 0x68,
	0x61, 0x31, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x70, 0x76, 0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f,
	0x73, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x77, 0x76, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x68, 0x77, 0x76, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70,
	0x70, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x78, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x78, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x6a, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x6a, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x67, 0x65, 0x6f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x67, 0x65, 0x6f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x3d, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6d, 0x65,
	0x69, 0x74, 0x75, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x6c,
	0x61, 0x73, 0x68, 0x76, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6c,
	0x61, 0x73, 0x68, 0x76, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x66, 0x61, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x66, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x73,
	0x68, 0x61, 0x31, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x73, 0x68,
	0x61, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x63, 0x6d, 0x64, 0x35, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x6d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6d,
	0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6c, 0x6d, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x63, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x2a, 0x05, 0x08, 0x64,
	0x10, 0x90, 0x4e, 0x1a, 0xb1, 0x02, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c,
	0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63,
	0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x63,
	0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x66, 0x69,
	0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x66, 0x69, 0x78,
	0x12, 0x34, 0x0a, 0x09, 0x69, 0x70, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x09, 0x69, 0x70, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x74, 0x63, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x75, 0x74, 0x63, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x8c, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x79, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x79,
	0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x2a,
	0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0xad, 0x07,
	0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a,
	0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74,
	0x62, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x75, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x75, 0x72, 0x12, 0x24, 0x0a, 0x03, 0x6e,
	0x62, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x03, 0x6e, 0x62,
	0x72, 0x1a, 0x82, 0x06, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x30, 0x0a,
	0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x65, 0x69,
	0x74, 0x75, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x65, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x1a, 0x8c, 0x05, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x01, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x03,
	0x61, 0x64, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x61, 0x64, 0x6d,
	0x12, 0x36, 0x0a, 0x0a, 0x61, 0x64, 0x6d, 0x5f, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x32,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x09, 0x61,
	0x64, 0x6d, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x75,
	0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x75, 0x72, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x72, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x30,
	0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x6d,
	0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x02, 0x10, 0x01, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72,
	0x12, 0x25, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f,
	0x72, 0x6b, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x0c, 0x0a, 0x01, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a,
	0x01, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x69,
	0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x13, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6c, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x6b, 0x54,
	0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x42, 0x0b, 0x0a, 0x09, 0x61, 0x64, 0x6d, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x2a,
	0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0xe6, 0x07,
	0x0a, 0x0d, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65,
	0x72, 0x12, 0x27, 0x0a, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x49, 0x64, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x61, 0x64,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6d, 0x65, 0x69,
	0x74, 0x75, 0x2e, 0x41, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x52, 0x06, 0x61, 0x64, 0x75,
	0x6e, 0x69, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x73, 0x75, 0x62, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74,
	0x75, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x53, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65,
	0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x32, 0x0a, 0x09, 0x70, 0x6c, 0x63, 0x6d, 0x74, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x6c, 0x63, 0x6d, 0x74,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x08, 0x70, 0x6c, 0x63, 0x6d, 0x74, 0x63, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31, 0x52, 0x08, 0x70, 0x6c, 0x63, 0x6d, 0x74,
	0x63, 0x6e, 0x74, 0x12, 0x13, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x3a, 0x01, 0x30, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x32, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x1a, 0xe0, 0x04, 0x0a,
	0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e,
	0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x38, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x48, 0x00, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x1a, 0x20, 0x0a, 0x05, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05,
	0x52, 0x03, 0x6c, 0x65, 0x6e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x93, 0x01, 0x0a,
	0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12,
	0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x77, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x77, 0x6d, 0x69,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x68, 0x6d, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x2a, 0x05, 0x08, 0x64, 0x10,
	0x90, 0x4e, 0x1a, 0x49, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x2a, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x68, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e,
	0x42, 0x0d, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x2a,
	0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x22, 0x84, 0x0d, 0x0a, 0x0e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x06, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x65,
	0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x1a, 0xcb, 0x05, 0x0a, 0x04, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3f, 0x0a,
	0x08, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x08, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33,
	0x0a, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d,
	0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x67,
	0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x52, 0x06, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x64, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x1a, 0xa2, 0x01, 0x0a, 0x08, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x1a, 0x9b, 0x01, 0x0a, 0x04,
	0x47, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x70, 0x61, 0x63,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62,
	0x70, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x1a, 0x4c, 0x0a, 0x06, 0x41, 0x70, 0x70,
	0x6c, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0xe5,
	0x05, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x69,
	0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x36, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x65,
	0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x48, 0x00, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x44, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6d, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x69, 0x6d, 0x67, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0x22, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x3c, 0x0a, 0x05, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x68, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x41, 0x0a, 0x0a, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x1a, 0x58, 0x0a, 0x05, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x2a, 0x05,
	0x08, 0x64, 0x10, 0x90, 0x4e, 0x1a, 0x39, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e,
	0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x42, 0x0d, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x2a, 0x05, 0x08, 0x64, 0x10, 0x90, 0x4e, 0x2a, 0xfb, 0x01,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x31, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x32, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x43,
	0x41, 0x54, 0x45, 0x30, 0x30, 0x33, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45,
	0x30, 0x30, 0x34, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x35,
	0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x36, 0x10, 0x06, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x37, 0x10, 0x07, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x41, 0x54, 0x45, 0x30, 0x30, 0x38, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54,
	0x45, 0x30, 0x30, 0x39, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31,
	0x30, 0x10, 0x0a, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x31, 0x10, 0x0b,
	0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x32, 0x10, 0x0c, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x33, 0x10, 0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41,
	0x54, 0x45, 0x30, 0x31, 0x34, 0x10, 0x0e, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30,
	0x31, 0x35, 0x10, 0x0f, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x36, 0x10,
	0x10, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x37, 0x10, 0x11, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x41, 0x54, 0x45, 0x30, 0x31, 0x38, 0x10, 0x12, 0x2a, 0x41, 0x0a, 0x0b, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x49,
	0x52, 0x53, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x45, 0x43, 0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x03, 0x2a, 0x55,
	0x0a, 0x0c, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11,
	0x0a, 0x0d, 0x58, 0x48, 0x54, 0x4d, 0x4c, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x44, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x58, 0x48, 0x54, 0x4d, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45,
	0x52, 0x5f, 0x41, 0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x41, 0x56, 0x41, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x5f, 0x41, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x46, 0x52,
	0x41, 0x4d, 0x45, 0x10, 0x04, 0x2a, 0xac, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x41,
	0x55, 0x44, 0x49, 0x4f, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x58,
	0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54,
	0x49, 0x43, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x58, 0x50, 0x41, 0x4e, 0x44, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x50, 0x41, 0x4e, 0x44, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f,
	0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f,
	0x50, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x52, 0x4f, 0x56, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x56, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x4e, 0x4f, 0x59, 0x49, 0x4e, 0x47, 0x10, 0x0a,
	0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x53, 0x10, 0x0b, 0x12, 0x0d, 0x0a,
	0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x0d, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x5f, 0x44, 0x49,
	0x41, 0x4c, 0x4f, 0x47, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x53, 0x54,
	0x59, 0x4c, 0x45, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17, 0x48, 0x41, 0x53, 0x5f, 0x41, 0x55, 0x44,
	0x49, 0x4f, 0x5f, 0x4f, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x42, 0x55, 0x54, 0x54, 0x4f, 0x4e,
	0x10, 0x0f, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x42, 0x45, 0x5f,
	0x53, 0x4b, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x10, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x4c, 0x41,
	0x53, 0x48, 0x10, 0x11, 0x2a, 0x4d, 0x0a, 0x0c, 0x41, 0x50, 0x49, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x31, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x4d, 0x52, 0x41, 0x49, 0x44, 0x5f, 0x31, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4f,
	0x52, 0x4d, 0x4d, 0x41, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x52, 0x41, 0x49, 0x44, 0x5f,
	0x32, 0x10, 0x05, 0x2a, 0x82, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x41, 0x42, 0x4f, 0x56, 0x45, 0x5f, 0x54, 0x48, 0x45, 0x5f, 0x46, 0x4f, 0x4c,
	0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x45, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x48, 0x45,
	0x5f, 0x46, 0x4f, 0x4c, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x45, 0x41, 0x44, 0x45,
	0x52, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x4f, 0x4f, 0x54, 0x45, 0x52, 0x10, 0x05, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x49, 0x44, 0x45, 0x42, 0x41, 0x52, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16,
	0x41, 0x44, 0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x07, 0x2a, 0x79, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f,
	0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e, 0x45, 0x54, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x45,
	0x4c, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x32, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c,
	0x4c, 0x5f, 0x33, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x34,
	0x47, 0x10, 0x06, 0x2a, 0x57, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x45,
	0x46, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x02, 0x12,
	0x06, 0x0a, 0x02, 0x55, 0x50, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f, 0x57, 0x4e, 0x10,
	0x04, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x58, 0x50, 0x41, 0x4e, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x46, 0x55, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x05, 0x2a, 0x3b, 0x0a, 0x0c,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c,
	0x47, 0x50, 0x53, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x06,
	0x0a, 0x02, 0x49, 0x50, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50,
	0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x4b, 0x0a, 0x0f, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0f, 0x0a, 0x0b,
	0x49, 0x50, 0x32, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x4e, 0x45, 0x55, 0x53, 0x54, 0x41, 0x52, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x41,
	0x58, 0x4d, 0x49, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x45, 0x54, 0x41, 0x51,
	0x55, 0x49, 0x54, 0x59, 0x10, 0x04, 0x2a, 0x87, 0x01, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10,
	0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x4e,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x56, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x49,
	0x47, 0x48, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4e,
	0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12,
	0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x42, 0x4f, 0x58, 0x10, 0x07,
	0x2a, 0xe1, 0x01, 0x0a, 0x0b, 0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x49, 0x43, 0x41, 0x4c,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x50, 0x49, 0x44, 0x45,
	0x52, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x4e, 0x4f, 0x4e, 0x48, 0x55, 0x4d, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x46, 0x46, 0x49,
	0x43, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x58, 0x59, 0x49, 0x50, 0x10,
	0x05, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44,
	0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x4c, 0x4f,
	0x43, 0x4b, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x52, 0x10, 0x07,
	0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x10, 0x08, 0x2a, 0x74, 0x0a, 0x08, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x41, 0x4c, 0x4c,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x50, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x45, 0x57, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x43, 0x41, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x4c, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x10, 0x06,
	0x12, 0x08, 0x0a, 0x04, 0x47, 0x52, 0x49, 0x44, 0x10, 0x07, 0x2a, 0x7c, 0x0a, 0x08, 0x41, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15,
	0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57,
	0x49, 0x44, 0x47, 0x45, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x52, 0x4f, 0x4d, 0x4f,
	0x54, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x41, 0x42, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x44, 0x5f, 0x4e, 0x41, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x44, 0x55, 0x4e, 0x49, 0x54, 0x49, 0x44, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x05, 0x2a, 0x33, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x03, 0x2a, 0x94, 0x02,
	0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x53, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x4d, 0x49, 0x58, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x13,
	0x0a, 0x0f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x52, 0x54, 0x49, 0x43, 0x4c,
	0x45, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x0c, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x0e, 0x12, 0x1a, 0x0a, 0x16,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4f, 0x43, 0x49,
	0x41, 0x4c, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x14, 0x12, 0x10, 0x0a, 0x0c,
	0x53, 0x4f, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x15, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x49, 0x4d,
	0x10, 0x16, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x45,
	0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x1e, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x10, 0x1f,
	0x12, 0x12, 0x0a, 0x0e, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x20, 0x2a, 0x4e, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x5f, 0x46, 0x45, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x54, 0x4f, 0x4d, 0x49, 0x43, 0x5f, 0x55, 0x4e, 0x49,
	0x54, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x55, 0x54, 0x53, 0x49, 0x44, 0x45, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x04, 0x2a, 0xa8, 0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x4f,
	0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x45, 0x53, 0x43, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4c,
	0x49, 0x4b, 0x45, 0x53, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x53, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x06,
	0x12, 0x0d, 0x0a, 0x09, 0x53, 0x41, 0x4c, 0x45, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x07, 0x12,
	0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x45, 0x53, 0x43, 0x32,
	0x10, 0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x55, 0x52, 0x4c,
	0x10, 0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x54, 0x41, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0c, 0x2a,
	0x39, 0x0a, 0x0e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4c,
	0x4f, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x03, 0x12,
	0x09, 0x0a, 0x05, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x04, 0x42, 0x25, 0x42, 0x07, 0x4f, 0x70,
	0x65, 0x6e, 0x52, 0x74, 0x62, 0x5a, 0x11, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f,
	0x70, 0x62, 0x2f, 0x6d, 0x65, 0x69, 0x74, 0x75, 0x80, 0x01, 0x01, 0x88, 0x01, 0x01, 0x90, 0x01,
	0x01,
}

var (
	file_meitu_bidding_proto_rawDescOnce sync.Once
	file_meitu_bidding_proto_rawDescData = file_meitu_bidding_proto_rawDesc
)

func file_meitu_bidding_proto_rawDescGZIP() []byte {
	file_meitu_bidding_proto_rawDescOnce.Do(func() {
		file_meitu_bidding_proto_rawDescData = protoimpl.X.CompressGZIP(file_meitu_bidding_proto_rawDescData)
	})
	return file_meitu_bidding_proto_rawDescData
}

var file_meitu_bidding_proto_enumTypes = make([]protoimpl.EnumInfo, 19)
var file_meitu_bidding_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_meitu_bidding_proto_goTypes = []interface{}{
	(ContentCategory)(0),                    // 0: meitu.ContentCategory
	(AuctionType)(0),                        // 1: meitu.AuctionType
	(BannerAdType)(0),                       // 2: meitu.BannerAdType
	(CreativeAttribute)(0),                  // 3: meitu.CreativeAttribute
	(APIFramework)(0),                       // 4: meitu.APIFramework
	(AdPosition)(0),                         // 5: meitu.AdPosition
	(ConnectionType)(0),                     // 6: meitu.ConnectionType
	(ExpandableDirection)(0),                // 7: meitu.ExpandableDirection
	(LocationType)(0),                       // 8: meitu.LocationType
	(LocationService)(0),                    // 9: meitu.LocationService
	(DeviceType)(0),                         // 10: meitu.DeviceType
	(NoBidReason)(0),                        // 11: meitu.NoBidReason
	(LayoutId)(0),                           // 12: meitu.LayoutId
	(AdUnitId)(0),                           // 13: meitu.AdUnitId
	(ContextType)(0),                        // 14: meitu.ContextType
	(ContextSubtype)(0),                     // 15: meitu.ContextSubtype
	(PlacementType)(0),                      // 16: meitu.PlacementType
	(DataAssetType)(0),                      // 17: meitu.DataAssetType
	(ImageAssetType)(0),                     // 18: meitu.ImageAssetType
	(*BidRequest)(nil),                      // 19: meitu.BidRequest
	(*BidResponse)(nil),                     // 20: meitu.BidResponse
	(*NativeRequest)(nil),                   // 21: meitu.NativeRequest
	(*NativeResponse)(nil),                  // 22: meitu.NativeResponse
	(*BidRequest_Imp)(nil),                  // 23: meitu.BidRequest.Imp
	(*BidRequest_App)(nil),                  // 24: meitu.BidRequest.App
	(*BidRequest_Device)(nil),               // 25: meitu.BidRequest.Device
	(*BidRequest_Geo)(nil),                  // 26: meitu.BidRequest.Geo
	(*BidRequest_User)(nil),                 // 27: meitu.BidRequest.User
	(*BidRequest_Imp_Banner)(nil),           // 28: meitu.BidRequest.Imp.Banner
	(*BidRequest_Imp_Native)(nil),           // 29: meitu.BidRequest.Imp.Native
	(*BidRequest_Imp_Pmp)(nil),              // 30: meitu.BidRequest.Imp.Pmp
	(*BidRequest_Imp_Banner_Format)(nil),    // 31: meitu.BidRequest.Imp.Banner.Format
	(*BidRequest_Imp_Pmp_Deal)(nil),         // 32: meitu.BidRequest.Imp.Pmp.Deal
	(*BidResponse_SeatBid)(nil),             // 33: meitu.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),         // 34: meitu.BidResponse.SeatBid.Bid
	(*NativeRequest_Asset)(nil),             // 35: meitu.NativeRequest.Asset
	(*NativeRequest_Asset_Title)(nil),       // 36: meitu.NativeRequest.Asset.Title
	(*NativeRequest_Asset_Image)(nil),       // 37: meitu.NativeRequest.Asset.Image
	(*NativeRequest_Asset_Data)(nil),        // 38: meitu.NativeRequest.Asset.Data
	(*NativeRequest_Asset_Video)(nil),       // 39: meitu.NativeRequest.Asset.Video
	(*NativeResponse_Link)(nil),             // 40: meitu.NativeResponse.Link
	(*NativeResponse_Asset)(nil),            // 41: meitu.NativeResponse.Asset
	(*NativeResponse_Link_Download)(nil),    // 42: meitu.NativeResponse.Link.Download
	(*NativeResponse_Link_Game)(nil),        // 43: meitu.NativeResponse.Link.Game
	(*NativeResponse_Link_Applet)(nil),      // 44: meitu.NativeResponse.Link.Applet
	(*NativeResponse_Asset_Title)(nil),      // 45: meitu.NativeResponse.Asset.Title
	(*NativeResponse_Asset_Image)(nil),      // 46: meitu.NativeResponse.Asset.Image
	(*NativeResponse_Asset_GroupImage)(nil), // 47: meitu.NativeResponse.Asset.GroupImage
	(*NativeResponse_Asset_Video)(nil),      // 48: meitu.NativeResponse.Asset.Video
	(*NativeResponse_Asset_Data)(nil),       // 49: meitu.NativeResponse.Asset.Data
}
var file_meitu_bidding_proto_depIdxs = []int32{
	23, // 0: meitu.BidRequest.imp:type_name -> meitu.BidRequest.Imp
	24, // 1: meitu.BidRequest.app:type_name -> meitu.BidRequest.App
	25, // 2: meitu.BidRequest.device:type_name -> meitu.BidRequest.Device
	27, // 3: meitu.BidRequest.user:type_name -> meitu.BidRequest.User
	1,  // 4: meitu.BidRequest.at:type_name -> meitu.AuctionType
	0,  // 5: meitu.BidRequest.bcat:type_name -> meitu.ContentCategory
	33, // 6: meitu.BidResponse.seatbid:type_name -> meitu.BidResponse.SeatBid
	11, // 7: meitu.BidResponse.nbr:type_name -> meitu.NoBidReason
	12, // 8: meitu.NativeRequest.layout:type_name -> meitu.LayoutId
	13, // 9: meitu.NativeRequest.adunit:type_name -> meitu.AdUnitId
	14, // 10: meitu.NativeRequest.context:type_name -> meitu.ContextType
	15, // 11: meitu.NativeRequest.contextsubtype:type_name -> meitu.ContextSubtype
	16, // 12: meitu.NativeRequest.plcmttype:type_name -> meitu.PlacementType
	35, // 13: meitu.NativeRequest.assets:type_name -> meitu.NativeRequest.Asset
	41, // 14: meitu.NativeResponse.assets:type_name -> meitu.NativeResponse.Asset
	40, // 15: meitu.NativeResponse.link:type_name -> meitu.NativeResponse.Link
	28, // 16: meitu.BidRequest.Imp.banner:type_name -> meitu.BidRequest.Imp.Banner
	30, // 17: meitu.BidRequest.Imp.pmp:type_name -> meitu.BidRequest.Imp.Pmp
	29, // 18: meitu.BidRequest.Imp.native:type_name -> meitu.BidRequest.Imp.Native
	0,  // 19: meitu.BidRequest.App.cat:type_name -> meitu.ContentCategory
	0,  // 20: meitu.BidRequest.App.sectioncat:type_name -> meitu.ContentCategory
	0,  // 21: meitu.BidRequest.App.pagecat:type_name -> meitu.ContentCategory
	26, // 22: meitu.BidRequest.Device.geo:type_name -> meitu.BidRequest.Geo
	6,  // 23: meitu.BidRequest.Device.connectiontype:type_name -> meitu.ConnectionType
	10, // 24: meitu.BidRequest.Device.devicetype:type_name -> meitu.DeviceType
	8,  // 25: meitu.BidRequest.Geo.type:type_name -> meitu.LocationType
	9,  // 26: meitu.BidRequest.Geo.ipservice:type_name -> meitu.LocationService
	26, // 27: meitu.BidRequest.User.geo:type_name -> meitu.BidRequest.Geo
	31, // 28: meitu.BidRequest.Imp.Banner.format:type_name -> meitu.BidRequest.Imp.Banner.Format
	5,  // 29: meitu.BidRequest.Imp.Banner.pos:type_name -> meitu.AdPosition
	2,  // 30: meitu.BidRequest.Imp.Banner.btype:type_name -> meitu.BannerAdType
	3,  // 31: meitu.BidRequest.Imp.Banner.battr:type_name -> meitu.CreativeAttribute
	7,  // 32: meitu.BidRequest.Imp.Banner.expdir:type_name -> meitu.ExpandableDirection
	4,  // 33: meitu.BidRequest.Imp.Banner.api:type_name -> meitu.APIFramework
	21, // 34: meitu.BidRequest.Imp.Native.request_native:type_name -> meitu.NativeRequest
	4,  // 35: meitu.BidRequest.Imp.Native.api:type_name -> meitu.APIFramework
	3,  // 36: meitu.BidRequest.Imp.Native.battr:type_name -> meitu.CreativeAttribute
	32, // 37: meitu.BidRequest.Imp.Pmp.deals:type_name -> meitu.BidRequest.Imp.Pmp.Deal
	1,  // 38: meitu.BidRequest.Imp.Pmp.Deal.at:type_name -> meitu.AuctionType
	34, // 39: meitu.BidResponse.SeatBid.bid:type_name -> meitu.BidResponse.SeatBid.Bid
	22, // 40: meitu.BidResponse.SeatBid.Bid.adm_native:type_name -> meitu.NativeResponse
	0,  // 41: meitu.BidResponse.SeatBid.Bid.cat:type_name -> meitu.ContentCategory
	3,  // 42: meitu.BidResponse.SeatBid.Bid.attr:type_name -> meitu.CreativeAttribute
	4,  // 43: meitu.BidResponse.SeatBid.Bid.api:type_name -> meitu.APIFramework
	36, // 44: meitu.NativeRequest.Asset.title:type_name -> meitu.NativeRequest.Asset.Title
	37, // 45: meitu.NativeRequest.Asset.img:type_name -> meitu.NativeRequest.Asset.Image
	38, // 46: meitu.NativeRequest.Asset.data:type_name -> meitu.NativeRequest.Asset.Data
	39, // 47: meitu.NativeRequest.Asset.video:type_name -> meitu.NativeRequest.Asset.Video
	18, // 48: meitu.NativeRequest.Asset.Image.type:type_name -> meitu.ImageAssetType
	17, // 49: meitu.NativeRequest.Asset.Data.type:type_name -> meitu.DataAssetType
	42, // 50: meitu.NativeResponse.Link.download:type_name -> meitu.NativeResponse.Link.Download
	43, // 51: meitu.NativeResponse.Link.game:type_name -> meitu.NativeResponse.Link.Game
	44, // 52: meitu.NativeResponse.Link.applet:type_name -> meitu.NativeResponse.Link.Applet
	45, // 53: meitu.NativeResponse.Asset.title:type_name -> meitu.NativeResponse.Asset.Title
	46, // 54: meitu.NativeResponse.Asset.img:type_name -> meitu.NativeResponse.Asset.Image
	49, // 55: meitu.NativeResponse.Asset.data:type_name -> meitu.NativeResponse.Asset.Data
	48, // 56: meitu.NativeResponse.Asset.video:type_name -> meitu.NativeResponse.Asset.Video
	47, // 57: meitu.NativeResponse.Asset.groupimg:type_name -> meitu.NativeResponse.Asset.GroupImage
	40, // 58: meitu.NativeResponse.Asset.link:type_name -> meitu.NativeResponse.Link
	46, // 59: meitu.NativeResponse.Asset.GroupImage.img:type_name -> meitu.NativeResponse.Asset.Image
	60, // [60:60] is the sub-list for method output_type
	60, // [60:60] is the sub-list for method input_type
	60, // [60:60] is the sub-list for extension type_name
	60, // [60:60] is the sub-list for extension extendee
	0,  // [0:60] is the sub-list for field type_name
}

func init() { file_meitu_bidding_proto_init() }
func file_meitu_bidding_proto_init() {
	if File_meitu_bidding_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_meitu_bidding_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Banner_Format); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeRequest_Asset_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Link); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Link_Download); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Link_Game); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Link_Applet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_GroupImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_meitu_bidding_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NativeResponse_Asset_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
	}
	file_meitu_bidding_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*BidRequest_Imp_Native_Request)(nil),
		(*BidRequest_Imp_Native_RequestNative)(nil),
	}
	file_meitu_bidding_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*BidResponse_SeatBid_Bid_Adm)(nil),
		(*BidResponse_SeatBid_Bid_AdmNative)(nil),
	}
	file_meitu_bidding_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*NativeRequest_Asset_Title_)(nil),
		(*NativeRequest_Asset_Img)(nil),
		(*NativeRequest_Asset_Data_)(nil),
		(*NativeRequest_Asset_Video_)(nil),
	}
	file_meitu_bidding_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*NativeResponse_Asset_Title_)(nil),
		(*NativeResponse_Asset_Img)(nil),
		(*NativeResponse_Asset_Data_)(nil),
		(*NativeResponse_Asset_Video_)(nil),
		(*NativeResponse_Asset_Groupimg)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_meitu_bidding_proto_rawDesc,
			NumEnums:      19,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_meitu_bidding_proto_goTypes,
		DependencyIndexes: file_meitu_bidding_proto_depIdxs,
		EnumInfos:         file_meitu_bidding_proto_enumTypes,
		MessageInfos:      file_meitu_bidding_proto_msgTypes,
	}.Build()
	File_meitu_bidding_proto = out.File
	file_meitu_bidding_proto_rawDesc = nil
	file_meitu_bidding_proto_goTypes = nil
	file_meitu_bidding_proto_depIdxs = nil
}
