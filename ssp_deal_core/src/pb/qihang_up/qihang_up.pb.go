// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: qihang_up.proto

package qihang_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                *string                       `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                                                          // 请求ID，唯一标识本次请求，明文字符串
	ApiVersion        *string                       `protobuf:"bytes,2,opt,name=api_version,json=apiVersion" json:"api_version,omitempty"`                        // 接口版本号, 目前传1.0.0
	Test              *uint32                       `protobuf:"varint,3,opt,name=test,def=0" json:"test,omitempty"`                                               // 如果不为0，那么这是一个测试请求。
	Imp               []*BidRequest_Imp             `protobuf:"bytes,4,rep,name=imp" json:"imp,omitempty"`                                                        // 曝光机会列表，信息流类位置一次请求会有多个曝光机会
	App               *BidRequest_App               `protobuf:"bytes,5,opt,name=app" json:"app,omitempty"`                                                        // APP信息
	Device            *BidRequest_Device            `protobuf:"bytes,6,opt,name=device" json:"device,omitempty"`                                                  // 设备信息
	Pmp               *BidRequest_Pmp               `protobuf:"bytes,7,opt,name=pmp" json:"pmp,omitempty"`                                                        // 交易定义
	CustomizedUserTag *BidRequest_CustomizedUserTag `protobuf:"bytes,8,opt,name=customized_user_tag,json=customizedUserTag" json:"customized_user_tag,omitempty"` // 自定义标签信息
	Timeout           *int32                        `protobuf:"varint,9,opt,name=timeout" json:"timeout,omitempty"`                                               // 要求DSP必须在timeout时间内返回，单位【ms】
	MediaTime         *int64                        `protobuf:"varint,10,opt,name=media_time,json=mediaTime" json:"media_time,omitempty"`                         // 媒体下发的请求时间戳，单位【ms】
	SspTime           *int64                        `protobuf:"varint,11,opt,name=ssp_time,json=sspTime" json:"ssp_time,omitempty"`                               // ssp下发的请求时间戳，单位是【ms】
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_Test = uint32(0)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetTest() uint32 {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return Default_BidRequest_Test
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetPmp() *BidRequest_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest) GetCustomizedUserTag() *BidRequest_CustomizedUserTag {
	if x != nil {
		return x.CustomizedUserTag
	}
	return nil
}

func (x *BidRequest) GetTimeout() int32 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

func (x *BidRequest) GetMediaTime() int64 {
	if x != nil && x.MediaTime != nil {
		return *x.MediaTime
	}
	return 0
}

func (x *BidRequest) GetSspTime() int64 {
	if x != nil && x.SspTime != nil {
		return *x.SspTime
	}
	return 0
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          *string                `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                                         // 请求ID，需与BidRequest.id一致
	BidId       *string                `protobuf:"bytes,2,opt,name=bid_id,json=bidId" json:"bid_id,omitempty"`                      // surge侧竞价ID
	Nbr         *int32                 `protobuf:"varint,3,opt,name=nbr" json:"nbr,omitempty"`                                      // 未出价原因
	SeatBid     []*BidResponse_SeatBid `protobuf:"bytes,4,rep,name=seat_bid,json=seatBid" json:"seat_bid,omitempty"`                // DSP参与竞价的具体内容，目前只有一个
	ColdEndTime *int64                 `protobuf:"varint,5,opt,name=cold_end_time,json=coldEndTime" json:"cold_end_time,omitempty"` // 设备冷却结束时间，unix时间戳，单位是：秒
	BidTime     *int64                 `protobuf:"varint,6,opt,name=bid_time,json=bidTime" json:"bid_time,omitempty"`               // 返回的时间戳，单位是【ms】
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetBidId() string {
	if x != nil && x.BidId != nil {
		return *x.BidId
	}
	return ""
}

func (x *BidResponse) GetNbr() int32 {
	if x != nil && x.Nbr != nil {
		return *x.Nbr
	}
	return 0
}

func (x *BidResponse) GetSeatBid() []*BidResponse_SeatBid {
	if x != nil {
		return x.SeatBid
	}
	return nil
}

func (x *BidResponse) GetColdEndTime() int64 {
	if x != nil && x.ColdEndTime != nil {
		return *x.ColdEndTime
	}
	return 0
}

func (x *BidResponse) GetBidTime() int64 {
	if x != nil && x.BidTime != nil {
		return *x.BidTime
	}
	return 0
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua             *string                       `protobuf:"bytes,1,opt,name=ua" json:"ua,omitempty"`                                                 // UA信息
	Ip             *string                       `protobuf:"bytes,2,opt,name=ip" json:"ip,omitempty"`                                                 // 用户设备ip
	DeviceType     *int32                        `protobuf:"varint,3,opt,name=device_type,json=deviceType" json:"device_type,omitempty"`              // 设备类型：0-未知 3-PC 4-手机Phone 5-平板Tablet 6-联网设备Connected Device 7-机顶盒Set Top Box
	Make           *string                       `protobuf:"bytes,4,opt,name=make" json:"make,omitempty"`                                             // 手机品牌，如：iPhone，Xiaomi
	Model          *string                       `protobuf:"bytes,5,opt,name=model" json:"model,omitempty"`                                           // 手机型号，如：iPhoneX, KNT-AL10
	Idfa           *string                       `protobuf:"bytes,6,opt,name=idfa" json:"idfa,omitempty"`                                             // IDFA原始值
	IdfaMd5        *string                       `protobuf:"bytes,7,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`                        // IDFA MD5值
	Oaid           *string                       `protobuf:"bytes,8,opt,name=oaid" json:"oaid,omitempty"`                                             // Android OAID原始值
	OaidMd5        *string                       `protobuf:"bytes,9,opt,name=oaid_md5,json=oaidMd5" json:"oaid_md5,omitempty"`                        // Android OAID MD5值
	Imei           *string                       `protobuf:"bytes,10,opt,name=imei" json:"imei,omitempty"`                                            // Android IMEI原始值
	ImeiMd5        *string                       `protobuf:"bytes,11,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`                       // Android IMEI MD5值
	Os             *string                       `protobuf:"bytes,12,opt,name=os" json:"os,omitempty"`                                                // 设备操作系统类型，android/ios
	Osv            *string                       `protobuf:"bytes,13,opt,name=osv" json:"osv,omitempty"`                                              // 设备操作系统版本号buxian
	Carrier        *int32                        `protobuf:"varint,14,opt,name=carrier" json:"carrier,omitempty"`                                     // 运营商 0：未知 1：中国移动 2：中国联通 3：中国电信
	ConnectionType *int32                        `protobuf:"varint,15,opt,name=connection_type,json=connectionType" json:"connection_type,omitempty"` // 网络连接状态 0：未知 1：Ethernet以太网 2：WIFI 网络 3：蜂窝数据网络 - 未知 4：蜂窝数据网络 - 2G 5：蜂窝数据网络 - 3G 6：蜂窝数据网络 - 4G 7：蜂窝数据网络 - 5G
	AndroidId      *string                       `protobuf:"bytes,16,opt,name=android_id,json=androidId" json:"android_id,omitempty"`                 // Android Id原始值
	AndroidIdMd5   *string                       `protobuf:"bytes,17,opt,name=android_id_md5,json=androidIdMd5" json:"android_id_md5,omitempty"`      // Android Id MD5值
	Mac            *string                       `protobuf:"bytes,18,opt,name=mac" json:"mac,omitempty"`                                              // MAC地址原始值
	MacMd5         *string                       `protobuf:"bytes,19,opt,name=mac_md5,json=macMd5" json:"mac_md5,omitempty"`                          // MAC地址MD5值，加密前先去除分隔符':'
	CaidVersion    *string                       `protobuf:"bytes,20,opt,name=caid_version,json=caidVersion" json:"caid_version,omitempty"`           // caid版本
	Caid           *string                       `protobuf:"bytes,21,opt,name=caid" json:"caid,omitempty"`                                            // caid原始值
	CaidMd5        *string                       `protobuf:"bytes,22,opt,name=caid_md5,json=caidMd5" json:"caid_md5,omitempty"`                       // caid md5值
	BootMark       *string                       `protobuf:"bytes,23,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`                    // 系统启动时间
	UpdateMark     *string                       `protobuf:"bytes,24,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`              // 系统更新时间
	CaidInfos      []*BidRequest_Device_CaidInfo `protobuf:"bytes,25,rep,name=caid_infos,json=caidInfos" json:"caid_infos,omitempty"`                 // caid列表
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceType() int32 {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidIdMd5() string {
	if x != nil && x.AndroidIdMd5 != nil {
		return *x.AndroidIdMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil && x.MacMd5 != nil {
		return *x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetCaidVersion() string {
	if x != nil && x.CaidVersion != nil {
		return *x.CaidVersion
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetCaidMd5() string {
	if x != nil && x.CaidMd5 != nil {
		return *x.CaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetCaidInfos() []*BidRequest_Device_CaidInfo {
	if x != nil {
		return x.CaidInfos
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          *string                 `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                                         // 区分于请求ID，标识唯一一次曝光机会
	TagId       *string                 `protobuf:"bytes,2,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`                      // 广告位标识ID
	SubTagId    *string                 `protobuf:"bytes,3,opt,name=sub_tag_id,json=subTagId" json:"sub_tag_id,omitempty"`           // 明细广告位标识ID
	AdType      *int32                  `protobuf:"varint,4,opt,name=ad_type,json=adType" json:"ad_type,omitempty"`                  // 广告类型，1:信息流、2:开屏、3：banner 、4：插屏、9：激励视频
	BidType     *int32                  `protobuf:"varint,5,opt,name=bid_type,json=bidType" json:"bid_type,omitempty"`               // 出价类型, 0：cpm 1：cpc 默认为cpm
	BidFloor    *int64                  `protobuf:"varint,6,opt,name=bid_floor,json=bidFloor" json:"bid_floor,omitempty"`            // 竞价底价，单位：分/千次展现
	Asset       []*BidRequest_Imp_Asset `protobuf:"bytes,7,rep,name=asset" json:"asset,omitempty"`                                   // 模版信息，详见模版资源映射表
	CpcBidFloor *int64                  `protobuf:"varint,8,opt,name=cpc_bid_floor,json=cpcBidFloor" json:"cpc_bid_floor,omitempty"` // cpc竞价底价，单位：分/点击
	AdsCount    *int32                  `protobuf:"varint,9,opt,name=ads_count,json=adsCount" json:"ads_count,omitempty"`            // 创意数量，默认为1
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil && x.TagId != nil {
		return *x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetSubTagId() string {
	if x != nil && x.SubTagId != nil {
		return *x.SubTagId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdType() int32 {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return 0
}

func (x *BidRequest_Imp) GetBidType() int32 {
	if x != nil && x.BidType != nil {
		return *x.BidType
	}
	return 0
}

func (x *BidRequest_Imp) GetBidFloor() int64 {
	if x != nil && x.BidFloor != nil {
		return *x.BidFloor
	}
	return 0
}

func (x *BidRequest_Imp) GetAsset() []*BidRequest_Imp_Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *BidRequest_Imp) GetCpcBidFloor() int64 {
	if x != nil && x.CpcBidFloor != nil {
		return *x.CpcBidFloor
	}
	return 0
}

func (x *BidRequest_Imp) GetAdsCount() int32 {
	if x != nil && x.AdsCount != nil {
		return *x.AdsCount
	}
	return 0
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`     // 应用名称：例如：喜马拉雅FM
	Bundle *string `protobuf:"bytes,2,opt,name=bundle" json:"bundle,omitempty"` // 应用程序包名：例如：com.ximalaya.iting
	Verion *string `protobuf:"bytes,3,opt,name=verion" json:"verion,omitempty"` // 应用版本号: 例如: 2.3.5
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetVerion() string {
	if x != nil && x.Verion != nil {
		return *x.Verion
	}
	return ""
}

type BidRequest_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DealId      *string `protobuf:"bytes,1,req,name=deal_id,json=dealId" json:"deal_id,omitempty"`                 // 直接交易的唯一ID
	PmpBidType  *int32  `protobuf:"varint,2,req,name=pmp_bid_type,json=pmpBidType" json:"pmp_bid_type,omitempty"`  // 包段出价类型, 0：cpm 1：cpc
	PmpPrice    *int32  `protobuf:"varint,3,req,name=pmp_price,json=pmpPrice" json:"pmp_price,omitempty"`          // 包段价格, 单位：分/千次展现
	SupportQuit *bool   `protobuf:"varint,4,req,name=support_quit,json=supportQuit" json:"support_quit,omitempty"` // 是否支持退量
}

func (x *BidRequest_Pmp) Reset() {
	*x = BidRequest_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Pmp) ProtoMessage() {}

func (x *BidRequest_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Pmp) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Pmp) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *BidRequest_Pmp) GetPmpBidType() int32 {
	if x != nil && x.PmpBidType != nil {
		return *x.PmpBidType
	}
	return 0
}

func (x *BidRequest_Pmp) GetPmpPrice() int32 {
	if x != nil && x.PmpPrice != nil {
		return *x.PmpPrice
	}
	return 0
}

func (x *BidRequest_Pmp) GetSupportQuit() bool {
	if x != nil && x.SupportQuit != nil {
		return *x.SupportQuit
	}
	return false
}

type BidRequest_CustomizedUserTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstalledAppList []*BidRequest_CustomizedUserTag_InstalledApp `protobuf:"bytes,1,rep,name=installed_app_list,json=installedAppList" json:"installed_app_list,omitempty"`
}

func (x *BidRequest_CustomizedUserTag) Reset() {
	*x = BidRequest_CustomizedUserTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_CustomizedUserTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_CustomizedUserTag) ProtoMessage() {}

func (x *BidRequest_CustomizedUserTag) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_CustomizedUserTag.ProtoReflect.Descriptor instead.
func (*BidRequest_CustomizedUserTag) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_CustomizedUserTag) GetInstalledAppList() []*BidRequest_CustomizedUserTag_InstalledApp {
	if x != nil {
		return x.InstalledAppList
	}
	return nil
}

// 多版本CAID信息
type BidRequest_Device_CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CAID版本
	Version *string `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	// CAID原值
	Caid *string `protobuf:"bytes,2,opt,name=caid" json:"caid,omitempty"`
	// CAID md5值，caid原值进行md5后小写输出
	CaidMd5 *string `protobuf:"bytes,3,opt,name=caid_md5,json=caidMd5" json:"caid_md5,omitempty"`
}

func (x *BidRequest_Device_CaidInfo) Reset() {
	*x = BidRequest_Device_CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_CaidInfo) ProtoMessage() {}

func (x *BidRequest_Device_CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_CaidInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_CaidInfo) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Device_CaidInfo) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest_Device_CaidInfo) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *BidRequest_Device_CaidInfo) GetCaidMd5() string {
	if x != nil && x.CaidMd5 != nil {
		return *x.CaidMd5
	}
	return ""
}

type BidRequest_Imp_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId *string `protobuf:"bytes,1,opt,name=template_id,json=templateId" json:"template_id,omitempty"` //  广告位模版ID, 详见模版资源映射表
	Width      *int32  `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`                            // 广告位模版宽度
	Height     *int32  `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`                          // 广告位模版高度
}

func (x *BidRequest_Imp_Asset) Reset() {
	*x = BidRequest_Imp_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Asset) ProtoMessage() {}

func (x *BidRequest_Imp_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Asset.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Asset) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *BidRequest_Imp_Asset) GetTemplateId() string {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return ""
}

func (x *BidRequest_Imp_Asset) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_Imp_Asset) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

// 用户已安装app列表字典：id可自定义，name为app的包名
// 若SSP有汇总好的app字典，可以通过线上只传id，线下汇总统计
type BidRequest_CustomizedUserTag_InstalledApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   *uint32 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
}

func (x *BidRequest_CustomizedUserTag_InstalledApp) Reset() {
	*x = BidRequest_CustomizedUserTag_InstalledApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_CustomizedUserTag_InstalledApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_CustomizedUserTag_InstalledApp) ProtoMessage() {}

func (x *BidRequest_CustomizedUserTag_InstalledApp) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_CustomizedUserTag_InstalledApp.ProtoReflect.Descriptor instead.
func (*BidRequest_CustomizedUserTag_InstalledApp) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *BidRequest_CustomizedUserTag_InstalledApp) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BidRequest_CustomizedUserTag_InstalledApp) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"` // DSP参与竞价的位置，与BidRequest.imp对应，每个imp最多只可返回一个bid
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             *string              `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                                                   // dsp侧针对这次竞价的ID
	ImpId          *string              `protobuf:"bytes,2,opt,name=imp_id,json=impId" json:"imp_id,omitempty"`                                // 曝光ID，对应BidRequest.imp.id，必填！
	Price          *int64               `protobuf:"varint,3,opt,name=price" json:"price,omitempty"`                                            // DSP出价，单位：分/千次曝光
	CreativeId     *string              `protobuf:"bytes,4,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`                 // 创意ID
	Nurl           *string              `protobuf:"bytes,5,opt,name=nurl" json:"nurl,omitempty"`                                               // 竞价成功通知地址
	Lurl           *string              `protobuf:"bytes,6,opt,name=lurl" json:"lurl,omitempty"`                                               // 竞价失败通知地址
	ImpTrackers    []string             `protobuf:"bytes,7,rep,name=imp_trackers,json=impTrackers" json:"imp_trackers,omitempty"`              // 曝光监测地址
	ClkTrackers    []string             `protobuf:"bytes,8,rep,name=clk_trackers,json=clkTrackers" json:"clk_trackers,omitempty"`              // 点击监测地址
	Adm            *BidResponse_Bid_Adm `protobuf:"bytes,9,opt,name=adm" json:"adm,omitempty"`                                                 // 创意物料信息
	DealId         *string              `protobuf:"bytes,10,opt,name=deal_id,json=dealId" json:"deal_id,omitempty"`                            // 直接交易的唯一ID
	PackageName    *string              `protobuf:"bytes,11,opt,name=package_name,json=packageName" json:"package_name,omitempty"`             // 安装包包名
	AppName        *string              `protobuf:"bytes,12,opt,name=app_name,json=appName" json:"app_name,omitempty"`                         // 应用名称
	IconUrl        *string              `protobuf:"bytes,13,opt,name=icon_url,json=iconUrl" json:"icon_url,omitempty"`                         // 应用icon url
	UserScoreLevel *int32               `protobuf:"varint,14,opt,name=user_score_level,json=userScoreLevel" json:"user_score_level,omitempty"` // 用户质量分
	BidType        *int32               `protobuf:"varint,15,opt,name=bid_type,json=bidType" json:"bid_type,omitempty"`                        // 出价类型, 0 - CPM、1 — CPC、2 - CPA
	OriginBidType  *int32               `protobuf:"varint,16,opt,name=origin_bid_type,json=originBidType" json:"origin_bid_type,omitempty"`    // 原始出价类型, 0 CPM、1 CPC: 被低价过滤 用cpa参竞、广告主的算法也有按照cpm或cpc给过出价，originBidType表示算法出价的方式
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_Bid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse_Bid) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *BidResponse_Bid) GetPrice() int64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *BidResponse_Bid) GetNurl() string {
	if x != nil && x.Nurl != nil {
		return *x.Nurl
	}
	return ""
}

func (x *BidResponse_Bid) GetLurl() string {
	if x != nil && x.Lurl != nil {
		return *x.Lurl
	}
	return ""
}

func (x *BidResponse_Bid) GetImpTrackers() []string {
	if x != nil {
		return x.ImpTrackers
	}
	return nil
}

func (x *BidResponse_Bid) GetClkTrackers() []string {
	if x != nil {
		return x.ClkTrackers
	}
	return nil
}

func (x *BidResponse_Bid) GetAdm() *BidResponse_Bid_Adm {
	if x != nil {
		return x.Adm
	}
	return nil
}

func (x *BidResponse_Bid) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *BidResponse_Bid) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Bid) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Bid) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

func (x *BidResponse_Bid) GetUserScoreLevel() int32 {
	if x != nil && x.UserScoreLevel != nil {
		return *x.UserScoreLevel
	}
	return 0
}

func (x *BidResponse_Bid) GetBidType() int32 {
	if x != nil && x.BidType != nil {
		return *x.BidType
	}
	return 0
}

func (x *BidResponse_Bid) GetOriginBidType() int32 {
	if x != nil && x.OriginBidType != nil {
		return *x.OriginBidType
	}
	return 0
}

// 物料信息
type BidResponse_Bid_Adm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId    *string                      `protobuf:"bytes,1,opt,name=template_id,json=templateId" json:"template_id,omitempty"`          // 模版ID
	Title         *string                      `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`                                      // 标题
	Desc          *string                      `protobuf:"bytes,3,opt,name=desc" json:"desc,omitempty"`                                        // 描述
	Image         []*BidResponse_Bid_Adm_Image `protobuf:"bytes,4,rep,name=image" json:"image,omitempty"`                                      // 图片信息
	Video         *BidResponse_Bid_Adm_Video   `protobuf:"bytes,5,opt,name=video" json:"video,omitempty"`                                      // 视频信息
	AdType        *int32                       `protobuf:"varint,6,opt,name=ad_type,json=adType" json:"ad_type,omitempty"`                     // 推广物类型
	DeepLink      *string                      `protobuf:"bytes,7,opt,name=deep_link,json=deepLink" json:"deep_link,omitempty"`                // 客户端优先跳转deeplink链接,其次跳转普通网址类或应用下载类的落地页
	UniversalLink *string                      `protobuf:"bytes,8,opt,name=universal_link,json=universalLink" json:"universal_link,omitempty"` // ios 唤起链接
	LandingSite   *string                      `protobuf:"bytes,9,opt,name=landing_site,json=landingSite" json:"landing_site,omitempty"`       // 落地页url
	DealId        *string                      `protobuf:"bytes,10,opt,name=deal_id,json=dealId" json:"deal_id,omitempty"`                     // 交易ID
	CoverImgUrl   *string                      `protobuf:"bytes,11,opt,name=cover_img_url,json=coverImgUrl" json:"cover_img_url,omitempty"`    //视频素材封面图片
}

func (x *BidResponse_Bid_Adm) Reset() {
	*x = BidResponse_Bid_Adm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm) ProtoMessage() {}

func (x *BidResponse_Bid_Adm) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *BidResponse_Bid_Adm) GetTemplateId() string {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetImage() []*BidResponse_Bid_Adm_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetVideo() *BidResponse_Bid_Adm_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetAdType() int32 {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return 0
}

func (x *BidResponse_Bid_Adm) GetDeepLink() string {
	if x != nil && x.DeepLink != nil {
		return *x.DeepLink
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetUniversalLink() string {
	if x != nil && x.UniversalLink != nil {
		return *x.UniversalLink
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetLandingSite() string {
	if x != nil && x.LandingSite != nil {
		return *x.LandingSite
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetCoverImgUrl() string {
	if x != nil && x.CoverImgUrl != nil {
		return *x.CoverImgUrl
	}
	return ""
}

type BidResponse_Bid_Adm_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`        // 图片URL
	Width  *int32  `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`   // 图片宽度
	Height *int32  `protobuf:"varint,3,opt,name=height" json:"height,omitempty"` // 图片高度
	Mime   *string `protobuf:"bytes,4,opt,name=mime" json:"mime,omitempty"`      // 图片类型， 如：JPG/JPEG/PNG
}

func (x *BidResponse_Bid_Adm_Image) Reset() {
	*x = BidResponse_Bid_Adm_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_Image) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_Image) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_Image) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1, 1, 0, 0}
}

func (x *BidResponse_Bid_Adm_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Bid_Adm_Image) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Image) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Image) GetMime() string {
	if x != nil && x.Mime != nil {
		return *x.Mime
	}
	return ""
}

type BidResponse_Bid_Adm_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl *string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"` // 视频URL
	Width    *int32  `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`                      // 视频宽度
	Height   *int32  `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`                    // 视频高度
	Duration *int32  `protobuf:"varint,4,opt,name=duration" json:"duration,omitempty"`                // 视频素材的播放时长，单位：秒
	Bitrate  *int32  `protobuf:"varint,5,opt,name=bitrate" json:"bitrate,omitempty"`                  // 视频文件的码率
	Size     *int32  `protobuf:"varint,6,opt,name=size" json:"size,omitempty"`                        // 视频文件的大小，单位Byte
	Mime     *string `protobuf:"bytes,7,opt,name=mime" json:"mime,omitempty"`                         // 视频素材类型，以MIME类型表示，当前仅支持"video/mp4"
}

func (x *BidResponse_Bid_Adm_Video) Reset() {
	*x = BidResponse_Bid_Adm_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qihang_up_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_Video) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_Video) ProtoReflect() protoreflect.Message {
	mi := &file_qihang_up_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_Video) Descriptor() ([]byte, []int) {
	return file_qihang_up_proto_rawDescGZIP(), []int{1, 1, 0, 1}
}

func (x *BidResponse_Bid_Adm_Video) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_Video) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetBitrate() int32 {
	if x != nil && x.Bitrate != nil {
		return *x.Bitrate
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetMime() string {
	if x != nil && x.Mime != nil {
		return *x.Mime
	}
	return ""
}

var File_qihang_up_proto protoreflect.FileDescriptor

var file_qihang_up_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x22, 0xab, 0x0f, 0x0a,
	0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x04,
	0x74, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x04, 0x74,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70,
	0x12, 0x2b, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x34, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70,
	0x12, 0x57, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x73, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x73, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x80, 0x06,
	0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d,
	0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64,
	0x35, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f,
	0x73, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f,
	0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x63, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x17, 0x0a, 0x07,
	0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74,
	0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x44, 0x0a, 0x0a, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x71, 0x69, 0x68, 0x61,
	0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x63, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x53, 0x0a, 0x08, 0x43,
	0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35,
	0x1a, 0xeb, 0x02, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x35,
	0x0a, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x70,
	0x63, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x73,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x56, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x49,
	0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x76, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x1a, 0x80, 0x01, 0x0a, 0x03, 0x50, 0x6d,
	0x70, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x6d,
	0x70, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05,
	0x52, 0x0a, 0x70, 0x6d, 0x70, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6d, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x6d, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x71, 0x75, 0x69, 0x74, 0x18, 0x04, 0x20, 0x02, 0x28, 0x08, 0x52,
	0x0b, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x69, 0x74, 0x1a, 0xab, 0x01, 0x0a,
	0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x12, 0x62, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41,
	0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x32, 0x0a, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xf6, 0x0a, 0x0a, 0x0b, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x62, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x6e, 0x62, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0d, 0x63, 0x6f, 0x6c, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x64, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a,
	0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x1a, 0xfa, 0x08, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6c, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6b, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x64,
	0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67,
	0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x52, 0x03, 0x61, 0x64, 0x6d, 0x12, 0x17, 0x0a, 0x07,
	0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x28,
	0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x62, 0x69,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x95, 0x05, 0x0a, 0x03,
	0x41, 0x64, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3a,
	0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x71, 0x69, 0x68, 0x61,
	0x6e, 0x67, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52,
	0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x25, 0x0a, 0x0e,
	0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x69, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x69, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0d, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67,
	0x55, 0x72, 0x6c, 0x1a, 0x5b, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6d, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65,
	0x1a, 0xb0, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x69, 0x6d, 0x65, 0x42, 0x17, 0x5a, 0x15, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f,
	0x70, 0x62, 0x2f, 0x71, 0x69, 0x68, 0x61, 0x6e, 0x67, 0x5f, 0x75, 0x70,
}

var (
	file_qihang_up_proto_rawDescOnce sync.Once
	file_qihang_up_proto_rawDescData = file_qihang_up_proto_rawDesc
)

func file_qihang_up_proto_rawDescGZIP() []byte {
	file_qihang_up_proto_rawDescOnce.Do(func() {
		file_qihang_up_proto_rawDescData = protoimpl.X.CompressGZIP(file_qihang_up_proto_rawDescData)
	})
	return file_qihang_up_proto_rawDescData
}

var file_qihang_up_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_qihang_up_proto_goTypes = []interface{}{
	(*BidRequest)(nil),                                // 0: qihang_up.BidRequest
	(*BidResponse)(nil),                               // 1: qihang_up.BidResponse
	(*BidRequest_Device)(nil),                         // 2: qihang_up.BidRequest.Device
	(*BidRequest_Imp)(nil),                            // 3: qihang_up.BidRequest.Imp
	(*BidRequest_App)(nil),                            // 4: qihang_up.BidRequest.App
	(*BidRequest_Pmp)(nil),                            // 5: qihang_up.BidRequest.Pmp
	(*BidRequest_CustomizedUserTag)(nil),              // 6: qihang_up.BidRequest.CustomizedUserTag
	(*BidRequest_Device_CaidInfo)(nil),                // 7: qihang_up.BidRequest.Device.CaidInfo
	(*BidRequest_Imp_Asset)(nil),                      // 8: qihang_up.BidRequest.Imp.Asset
	(*BidRequest_CustomizedUserTag_InstalledApp)(nil), // 9: qihang_up.BidRequest.CustomizedUserTag.InstalledApp
	(*BidResponse_SeatBid)(nil),                       // 10: qihang_up.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),                           // 11: qihang_up.BidResponse.Bid
	(*BidResponse_Bid_Adm)(nil),                       // 12: qihang_up.BidResponse.Bid.Adm
	(*BidResponse_Bid_Adm_Image)(nil),                 // 13: qihang_up.BidResponse.Bid.Adm.Image
	(*BidResponse_Bid_Adm_Video)(nil),                 // 14: qihang_up.BidResponse.Bid.Adm.Video
}
var file_qihang_up_proto_depIdxs = []int32{
	3,  // 0: qihang_up.BidRequest.imp:type_name -> qihang_up.BidRequest.Imp
	4,  // 1: qihang_up.BidRequest.app:type_name -> qihang_up.BidRequest.App
	2,  // 2: qihang_up.BidRequest.device:type_name -> qihang_up.BidRequest.Device
	5,  // 3: qihang_up.BidRequest.pmp:type_name -> qihang_up.BidRequest.Pmp
	6,  // 4: qihang_up.BidRequest.customized_user_tag:type_name -> qihang_up.BidRequest.CustomizedUserTag
	10, // 5: qihang_up.BidResponse.seat_bid:type_name -> qihang_up.BidResponse.SeatBid
	7,  // 6: qihang_up.BidRequest.Device.caid_infos:type_name -> qihang_up.BidRequest.Device.CaidInfo
	8,  // 7: qihang_up.BidRequest.Imp.asset:type_name -> qihang_up.BidRequest.Imp.Asset
	9,  // 8: qihang_up.BidRequest.CustomizedUserTag.installed_app_list:type_name -> qihang_up.BidRequest.CustomizedUserTag.InstalledApp
	11, // 9: qihang_up.BidResponse.SeatBid.bid:type_name -> qihang_up.BidResponse.Bid
	12, // 10: qihang_up.BidResponse.Bid.adm:type_name -> qihang_up.BidResponse.Bid.Adm
	13, // 11: qihang_up.BidResponse.Bid.Adm.image:type_name -> qihang_up.BidResponse.Bid.Adm.Image
	14, // 12: qihang_up.BidResponse.Bid.Adm.video:type_name -> qihang_up.BidResponse.Bid.Adm.Video
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_qihang_up_proto_init() }
func file_qihang_up_proto_init() {
	if File_qihang_up_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_qihang_up_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_CustomizedUserTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_CustomizedUserTag_InstalledApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qihang_up_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_qihang_up_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_qihang_up_proto_goTypes,
		DependencyIndexes: file_qihang_up_proto_depIdxs,
		MessageInfos:      file_qihang_up_proto_msgTypes,
	}.Build()
	File_qihang_up_proto = out.File
	file_qihang_up_proto_rawDesc = nil
	file_qihang_up_proto_goTypes = nil
	file_qihang_up_proto_depIdxs = nil
}
