// Copyright Baidu Inc. All Rights Reserved.
//
// 文件内容: baidu ssp service 百度SSP协议文件
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: baiqingteng_1.0.17.proto

package baiqingteng_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 广告动作类型
type ActionType int32

const (
	ActionType_LANDINGPAGE ActionType = 0 // 落地页跳转,默认项
	ActionType_DOWNLOAD    ActionType = 1 // 下载
	ActionType_DEEPLINK    ActionType = 2 // 应用唤醒
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "LANDINGPAGE",
		1: "DOWNLOAD",
		2: "DEEPLINK",
	}
	ActionType_value = map[string]int32{
		"LANDINGPAGE": 0,
		"DOWNLOAD":    1,
		"DEEPLINK":    2,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[0].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[0]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ActionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ActionType(num)
	return nil
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0}
}

type AdType int32

const (
	AdType_NATIVE AdType = 0 // 信息流广告
	AdType_SPLASH AdType = 3 // 开屏广告
	AdType_RVIDEO AdType = 4 // 激励视频广告
	AdType_INSERT AdType = 6 // 插屏广告
)

// Enum value maps for AdType.
var (
	AdType_name = map[int32]string{
		0: "NATIVE",
		3: "SPLASH",
		4: "RVIDEO",
		6: "INSERT",
	}
	AdType_value = map[string]int32{
		"NATIVE": 0,
		"SPLASH": 3,
		"RVIDEO": 4,
		"INSERT": 6,
	}
)

func (x AdType) Enum() *AdType {
	p := new(AdType)
	*p = x
	return p
}

func (x AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[1].Descriptor()
}

func (AdType) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[1]
}

func (x AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AdType(num)
	return nil
}

// Deprecated: Use AdType.Descriptor instead.
func (AdType) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{1}
}

type Template int32

const (
	Template_SIMG Template = 1 // 小图广告，适用于左图右文或右图左文
	Template_BIMG Template = 2 // 大图广告
	Template_GIMG Template = 3 // 组图广告，目前仅支持3图，
	Template_VID  Template = 4 // 视频广告，带封面图
	Template_RVID Template = 5 // 激励视频
)

// Enum value maps for Template.
var (
	Template_name = map[int32]string{
		1: "SIMG",
		2: "BIMG",
		3: "GIMG",
		4: "VID",
		5: "RVID",
	}
	Template_value = map[string]int32{
		"SIMG": 1,
		"BIMG": 2,
		"GIMG": 3,
		"VID":  4,
		"RVID": 5,
	}
)

func (x Template) Enum() *Template {
	p := new(Template)
	*p = x
	return p
}

func (x Template) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Template) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[2].Descriptor()
}

func (Template) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[2]
}

func (x Template) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Template) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Template(num)
	return nil
}

// Deprecated: Use Template.Descriptor instead.
func (Template) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{2}
}

type AspectRatio int32

const (
	AspectRatio_RATIO_2X1  AspectRatio = 1 // 物料比例2:1
	AspectRatio_RATIO_3X2  AspectRatio = 2 // 物料比例3:2
	AspectRatio_RATIO_2X3  AspectRatio = 3 // 物料比例2:3
	AspectRatio_RATIO_16X9 AspectRatio = 4 // 物料比例16:9
	AspectRatio_RATIO_9X16 AspectRatio = 5 // 物料比例9:16
	AspectRatio_RATIO_1X1  AspectRatio = 6 // 物料比例1:1
	AspectRatio_RATIO_4X3  AspectRatio = 7 // 物料比例4:3
	AspectRatio_RATIO_3X1  AspectRatio = 8 // 物料比例3:1
)

// Enum value maps for AspectRatio.
var (
	AspectRatio_name = map[int32]string{
		1: "RATIO_2X1",
		2: "RATIO_3X2",
		3: "RATIO_2X3",
		4: "RATIO_16X9",
		5: "RATIO_9X16",
		6: "RATIO_1X1",
		7: "RATIO_4X3",
		8: "RATIO_3X1",
	}
	AspectRatio_value = map[string]int32{
		"RATIO_2X1":  1,
		"RATIO_3X2":  2,
		"RATIO_2X3":  3,
		"RATIO_16X9": 4,
		"RATIO_9X16": 5,
		"RATIO_1X1":  6,
		"RATIO_4X3":  7,
		"RATIO_3X1":  8,
	}
)

func (x AspectRatio) Enum() *AspectRatio {
	p := new(AspectRatio)
	*p = x
	return p
}

func (x AspectRatio) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AspectRatio) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[3].Descriptor()
}

func (AspectRatio) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[3]
}

func (x AspectRatio) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AspectRatio) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AspectRatio(num)
	return nil
}

// Deprecated: Use AspectRatio.Descriptor instead.
func (AspectRatio) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{3}
}

type Carrier int32

const (
	Carrier_Unknown Carrier = 0 // 未知运营商
	Carrier_Mobile  Carrier = 1 // 中国移动
	Carrier_Telecom Carrier = 2 // 中国电信
	Carrier_Unicom  Carrier = 3 // 中国联通
)

// Enum value maps for Carrier.
var (
	Carrier_name = map[int32]string{
		0: "Unknown",
		1: "Mobile",
		2: "Telecom",
		3: "Unicom",
	}
	Carrier_value = map[string]int32{
		"Unknown": 0,
		"Mobile":  1,
		"Telecom": 2,
		"Unicom":  3,
	}
)

func (x Carrier) Enum() *Carrier {
	p := new(Carrier)
	*p = x
	return p
}

func (x Carrier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Carrier) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[4].Descriptor()
}

func (Carrier) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[4]
}

func (x Carrier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Carrier) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Carrier(num)
	return nil
}

// Deprecated: Use Carrier.Descriptor instead.
func (Carrier) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{4}
}

type NetworkType int32

const (
	NetworkType_UNKNOWN_NETWORK NetworkType = 0 // 未知网络
	NetworkType_WIFI            NetworkType = 1 // WIFI 有线
	NetworkType_MOBILE_2G       NetworkType = 2 // 2G
	NetworkType_MOBILE_3G       NetworkType = 3 // 3G
	NetworkType_MOBILE_4G       NetworkType = 4 // 4G
	NetworkType_MOBILE_5G       NetworkType = 5 // 5G
)

// Enum value maps for NetworkType.
var (
	NetworkType_name = map[int32]string{
		0: "UNKNOWN_NETWORK",
		1: "WIFI",
		2: "MOBILE_2G",
		3: "MOBILE_3G",
		4: "MOBILE_4G",
		5: "MOBILE_5G",
	}
	NetworkType_value = map[string]int32{
		"UNKNOWN_NETWORK": 0,
		"WIFI":            1,
		"MOBILE_2G":       2,
		"MOBILE_3G":       3,
		"MOBILE_4G":       4,
		"MOBILE_5G":       5,
	}
)

func (x NetworkType) Enum() *NetworkType {
	p := new(NetworkType)
	*p = x
	return p
}

func (x NetworkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkType) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[5].Descriptor()
}

func (NetworkType) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[5]
}

func (x NetworkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NetworkType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NetworkType(num)
	return nil
}

// Deprecated: Use NetworkType.Descriptor instead.
func (NetworkType) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{5}
}

type DeviceType int32

const (
	DeviceType_UNKNOW_DEVICE DeviceType = 0 // 未知设备
	DeviceType_PHONE         DeviceType = 1 // 手机
	DeviceType_TABLET        DeviceType = 2 // 平板
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "UNKNOW_DEVICE",
		1: "PHONE",
		2: "TABLET",
	}
	DeviceType_value = map[string]int32{
		"UNKNOW_DEVICE": 0,
		"PHONE":         1,
		"TABLET":        2,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[6].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[6]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DeviceType(num)
	return nil
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{6}
}

type OS int32

const (
	OS_UNKNOWN_OS OS = 0 // 未知操作系统
	OS_IOS        OS = 1
	OS_ANDROID    OS = 2
	OS_WINDOWS    OS = 3
)

// Enum value maps for OS.
var (
	OS_name = map[int32]string{
		0: "UNKNOWN_OS",
		1: "IOS",
		2: "ANDROID",
		3: "WINDOWS",
	}
	OS_value = map[string]int32{
		"UNKNOWN_OS": 0,
		"IOS":        1,
		"ANDROID":    2,
		"WINDOWS":    3,
	}
)

func (x OS) Enum() *OS {
	p := new(OS)
	*p = x
	return p
}

func (x OS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OS) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[7].Descriptor()
}

func (OS) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[7]
}

func (x OS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *OS) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = OS(num)
	return nil
}

// Deprecated: Use OS.Descriptor instead.
func (OS) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{7}
}

// 经纬度坐标标准
type GeoType int32

const (
	GeoType_BD_09    GeoType = 0 // 百度地图的经纬度坐标标准
	GeoType_GCJ_02   GeoType = 1 // 国测局制定的经纬度坐标标准
	GeoType_WGS_84   GeoType = 2 // 国际经纬度坐标标准
	GeoType_BD_09_LL GeoType = 3 // 百度地图的墨卡坐标标准,以米为单位
)

// Enum value maps for GeoType.
var (
	GeoType_name = map[int32]string{
		0: "BD_09",
		1: "GCJ_02",
		2: "WGS_84",
		3: "BD_09_LL",
	}
	GeoType_value = map[string]int32{
		"BD_09":    0,
		"GCJ_02":   1,
		"WGS_84":   2,
		"BD_09_LL": 3,
	}
)

func (x GeoType) Enum() *GeoType {
	p := new(GeoType)
	*p = x
	return p
}

func (x GeoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GeoType) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[8].Descriptor()
}

func (GeoType) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[8]
}

func (x GeoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *GeoType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = GeoType(num)
	return nil
}

// Deprecated: Use GeoType.Descriptor instead.
func (GeoType) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{8}
}

type Gender int32

const (
	Gender_UNKNOWN Gender = 0 // 未知
	Gender_MALE    Gender = 1 // 男
	Gender_FEMALE  Gender = 2 // 女
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[9].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[9]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Gender) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Gender(num)
	return nil
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{9}
}

type TrackEvent int32

const (
	TrackEvent_VIDEO_START                    TrackEvent = 100 // 视频开始播放
	TrackEvent_VIDEO_CLOSE                    TrackEvent = 101 // 视频播放终止
	TrackEvent_VIDEO_READY_PLAY               TrackEvent = 102 // 视频准备播放
	TrackEvent_VIDEO_CONTINUE_PLAY            TrackEvent = 103 // 视频继续播放
	TrackEvent_VIDEO_PAUSE                    TrackEvent = 104 // 视频播放中止
	TrackEvent_VIDEO_PLAY_END                 TrackEvent = 105 // 视频播放完成
	TrackEvent_VIDEO_REPEATED_PLAY            TrackEvent = 106 // 视频重复播放
	TrackEvent_SKIP                           TrackEvent = 107 // 视频跳过
	TrackEvent_VIDEO_PLAY_FAIL                TrackEvent = 108 // 视频播放失败
	TrackEvent_VIDEO_TURN_ON_OFF_SOUND_BUTTON TrackEvent = 109 // 打开关闭声音
)

// Enum value maps for TrackEvent.
var (
	TrackEvent_name = map[int32]string{
		100: "VIDEO_START",
		101: "VIDEO_CLOSE",
		102: "VIDEO_READY_PLAY",
		103: "VIDEO_CONTINUE_PLAY",
		104: "VIDEO_PAUSE",
		105: "VIDEO_PLAY_END",
		106: "VIDEO_REPEATED_PLAY",
		107: "SKIP",
		108: "VIDEO_PLAY_FAIL",
		109: "VIDEO_TURN_ON_OFF_SOUND_BUTTON",
	}
	TrackEvent_value = map[string]int32{
		"VIDEO_START":                    100,
		"VIDEO_CLOSE":                    101,
		"VIDEO_READY_PLAY":               102,
		"VIDEO_CONTINUE_PLAY":            103,
		"VIDEO_PAUSE":                    104,
		"VIDEO_PLAY_END":                 105,
		"VIDEO_REPEATED_PLAY":            106,
		"SKIP":                           107,
		"VIDEO_PLAY_FAIL":                108,
		"VIDEO_TURN_ON_OFF_SOUND_BUTTON": 109,
	}
)

func (x TrackEvent) Enum() *TrackEvent {
	p := new(TrackEvent)
	*p = x
	return p
}

func (x TrackEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrackEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_baiqingteng_1_0_17_proto_enumTypes[10].Descriptor()
}

func (TrackEvent) Type() protoreflect.EnumType {
	return &file_baiqingteng_1_0_17_proto_enumTypes[10]
}

func (x TrackEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TrackEvent) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TrackEvent(num)
	return nil
}

// Deprecated: Use TrackEvent.Descriptor instead.
func (TrackEvent) EnumDescriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{10}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MediaId      *uint32                   `protobuf:"varint,1,opt,name=mediaId" json:"mediaId,omitempty"`           // 媒体id，唯一标识接入ADX
	ReqId        []byte                    `protobuf:"bytes,2,opt,name=reqId" json:"reqId,omitempty"`                // 请求ID，唯一标识本次请求，明文字符串
	Imp          []*BidRequest_Imp         `protobuf:"bytes,3,rep,name=imp" json:"imp,omitempty"`                    // 广告位,仅支持单广告位,即仅第一个imp生效
	Site         *BidRequest_Site          `protobuf:"bytes,4,opt,name=site" json:"site,omitempty"`                  // 网站站点信息
	App          *BidRequest_App           `protobuf:"bytes,5,opt,name=app" json:"app,omitempty"`                    // 应用信息
	Device       *BidRequest_Device        `protobuf:"bytes,6,opt,name=device" json:"device,omitempty"`              // 用户设备信息
	User         *BidRequest_User          `protobuf:"bytes,7,opt,name=user" json:"user,omitempty"`                  // 用户信息
	Video        *BidRequest_Video         `protobuf:"bytes,8,opt,name=video" json:"video,omitempty"`                // 视频信息
	Test         *bool                     `protobuf:"varint,9,opt,name=test,def=0" json:"test,omitempty"`           // 是否测试流量
	LastWinInfos []*BidRequest_LastWinInfo `protobuf:"bytes,10,rep,name=lastWinInfos" json:"lastWinInfos,omitempty"` // 上一次竞胜信息
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_Test = bool(false)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetMediaId() uint32 {
	if x != nil && x.MediaId != nil {
		return *x.MediaId
	}
	return 0
}

func (x *BidRequest) GetReqId() []byte {
	if x != nil {
		return x.ReqId
	}
	return nil
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetVideo() *BidRequest_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest) GetTest() bool {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return Default_BidRequest_Test
}

func (x *BidRequest) GetLastWinInfos() []*BidRequest_LastWinInfo {
	if x != nil {
		return x.LastWinInfos
	}
	return nil
}

type Adm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId          *Template    `protobuf:"varint,1,opt,name=templateId,enum=baiqingteng.Template" json:"templateId,omitempty"`                      // 广告的模板类型
	ActionType          *ActionType  `protobuf:"varint,2,opt,name=actionType,enum=baiqingteng.ActionType" json:"actionType,omitempty"`                    // 广告的交互类型
	Title               []byte       `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`                                                           // 标题
	Desc                []byte       `protobuf:"bytes,4,opt,name=desc" json:"desc,omitempty"`                                                             // 描述
	Icon                []byte       `protobuf:"bytes,5,opt,name=icon" json:"icon,omitempty"`                                                             // 图标地址
	Img                 []*Adm_Image `protobuf:"bytes,6,rep,name=img" json:"img,omitempty"`                                                               // 图片信息
	Video               *Adm_Video   `protobuf:"bytes,7,opt,name=video" json:"video,omitempty"`                                                           // 视频信息
	LanddingPage        []byte       `protobuf:"bytes,8,opt,name=landdingPage" json:"landdingPage,omitempty"`                                             // 广告点击落地页/下载地址，deeplink失败是备用地址
	Deeplink            []byte       `protobuf:"bytes,9,opt,name=deeplink" json:"deeplink,omitempty"`                                                     // deeplink地址
	AppName             []byte       `protobuf:"bytes,10,opt,name=appName" json:"appName,omitempty"`                                                      // 应用名称
	PackageName         []byte       `protobuf:"bytes,11,opt,name=packageName" json:"packageName,omitempty"`                                              // 应用包名
	PackageSize         *uint32      `protobuf:"varint,12,opt,name=packageSize" json:"packageSize,omitempty"`                                             // 应用包大小
	BundleId            []byte       `protobuf:"bytes,13,opt,name=bundleId" json:"bundleId,omitempty"`                                                    // 用于IOS下载的id
	Publisher           []byte       `protobuf:"bytes,14,opt,name=publisher" json:"publisher,omitempty"`                                                  // 开发者
	AppVersion          []byte       `protobuf:"bytes,15,opt,name=appVersion" json:"appVersion,omitempty"`                                                // 版本号
	Privacy             []byte       `protobuf:"bytes,16,opt,name=privacy" json:"privacy,omitempty"`                                                      // 隐私协议
	Permission          []byte       `protobuf:"bytes,17,opt,name=permission" json:"permission,omitempty"`                                                // 用户权限
	UlkUrl              []byte       `protobuf:"bytes,18,opt,name=ulk_url,json=ulkUrl" json:"ulk_url,omitempty"`                                          // universal link url, 用于ios调起
	UlkScheme           []byte       `protobuf:"bytes,19,opt,name=ulk_scheme,json=ulkScheme" json:"ulk_scheme,omitempty"`                                 // universal link scheme, 用于ios嗅探
	AppStoreLink        []byte       `protobuf:"bytes,20,opt,name=appStoreLink" json:"appStoreLink,omitempty"`                                            // 安卓直投下载地址
	AppIntroductionLink []byte       `protobuf:"bytes,21,opt,name=app_introduction_link,json=appIntroductionLink" json:"app_introduction_link,omitempty"` // 六要素之 APP产品功能介绍  链接
	DownloadMidPage     []byte       `protobuf:"bytes,22,opt,name=downloadMidPage" json:"downloadMidPage,omitempty"`                                      // 下载中间页
}

func (x *Adm) Reset() {
	*x = Adm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adm) ProtoMessage() {}

func (x *Adm) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adm.ProtoReflect.Descriptor instead.
func (*Adm) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{1}
}

func (x *Adm) GetTemplateId() Template {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return Template_SIMG
}

func (x *Adm) GetActionType() ActionType {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return ActionType_LANDINGPAGE
}

func (x *Adm) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Adm) GetDesc() []byte {
	if x != nil {
		return x.Desc
	}
	return nil
}

func (x *Adm) GetIcon() []byte {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *Adm) GetImg() []*Adm_Image {
	if x != nil {
		return x.Img
	}
	return nil
}

func (x *Adm) GetVideo() *Adm_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Adm) GetLanddingPage() []byte {
	if x != nil {
		return x.LanddingPage
	}
	return nil
}

func (x *Adm) GetDeeplink() []byte {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *Adm) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

func (x *Adm) GetPackageName() []byte {
	if x != nil {
		return x.PackageName
	}
	return nil
}

func (x *Adm) GetPackageSize() uint32 {
	if x != nil && x.PackageSize != nil {
		return *x.PackageSize
	}
	return 0
}

func (x *Adm) GetBundleId() []byte {
	if x != nil {
		return x.BundleId
	}
	return nil
}

func (x *Adm) GetPublisher() []byte {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *Adm) GetAppVersion() []byte {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *Adm) GetPrivacy() []byte {
	if x != nil {
		return x.Privacy
	}
	return nil
}

func (x *Adm) GetPermission() []byte {
	if x != nil {
		return x.Permission
	}
	return nil
}

func (x *Adm) GetUlkUrl() []byte {
	if x != nil {
		return x.UlkUrl
	}
	return nil
}

func (x *Adm) GetUlkScheme() []byte {
	if x != nil {
		return x.UlkScheme
	}
	return nil
}

func (x *Adm) GetAppStoreLink() []byte {
	if x != nil {
		return x.AppStoreLink
	}
	return nil
}

func (x *Adm) GetAppIntroductionLink() []byte {
	if x != nil {
		return x.AppIntroductionLink
	}
	return nil
}

func (x *Adm) GetDownloadMidPage() []byte {
	if x != nil {
		return x.DownloadMidPage
	}
	return nil
}

type TrackMonitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *TrackEvent `protobuf:"varint,1,opt,name=event,enum=baiqingteng.TrackEvent" json:"event,omitempty"` // 监控事件
	Url   [][]byte    `protobuf:"bytes,2,rep,name=url" json:"url,omitempty"`                                  // 监控URL
}

func (x *TrackMonitor) Reset() {
	*x = TrackMonitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackMonitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackMonitor) ProtoMessage() {}

func (x *TrackMonitor) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackMonitor.ProtoReflect.Descriptor instead.
func (*TrackMonitor) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{2}
}

func (x *TrackMonitor) GetEvent() TrackEvent {
	if x != nil && x.Event != nil {
		return *x.Event
	}
	return TrackEvent_VIDEO_START
}

func (x *TrackMonitor) GetUrl() [][]byte {
	if x != nil {
		return x.Url
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId   []byte                 `protobuf:"bytes,1,opt,name=reqId" json:"reqId,omitempty"`     // 对应请求ID
	BidId   []byte                 `protobuf:"bytes,2,opt,name=bidId" json:"bidId,omitempty"`     // 本次竞价ID
	SeatBid []*BidResponse_SeatBid `protobuf:"bytes,3,rep,name=seatBid" json:"seatBid,omitempty"` // 对应广告位，仅支持单广告位
	Nbr     *int32                 `protobuf:"varint,4,opt,name=nbr" json:"nbr,omitempty"`        // 过滤状态，具体解释见文档
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{3}
}

func (x *BidResponse) GetReqId() []byte {
	if x != nil {
		return x.ReqId
	}
	return nil
}

func (x *BidResponse) GetBidId() []byte {
	if x != nil {
		return x.BidId
	}
	return nil
}

func (x *BidResponse) GetSeatBid() []*BidResponse_SeatBid {
	if x != nil {
		return x.SeatBid
	}
	return nil
}

func (x *BidResponse) GetNbr() int32 {
	if x != nil && x.Nbr != nil {
		return *x.Nbr
	}
	return 0
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           []byte                  `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	AppId        []byte                  `protobuf:"bytes,2,opt,name=appId" json:"appId,omitempty"`                                        // 百度提供，广告app唯一id
	TagId        []byte                  `protobuf:"bytes,3,opt,name=tagId" json:"tagId,omitempty"`                                        // 广告位id
	BidFloor     *uint32                 `protobuf:"varint,4,opt,name=bidFloor" json:"bidFloor,omitempty"`                                 // CPM底价 --------百度单位为分，如有特殊情况请说明
	Secure       *int32                  `protobuf:"varint,5,opt,name=secure,def=1" json:"secure,omitempty"`                               // 0 HTTP, 1 HTTPS
	Instl        *int32                  `protobuf:"varint,6,opt,name=instl" json:"instl,omitempty"`                                       // 是否为全屏广告，0 非全屏， 1 全屏
	AdType       *AdType                 `protobuf:"varint,7,opt,name=adType,enum=baiqingteng.AdType" json:"adType,omitempty"`             // 广告位类型
	Assets       []*BidRequest_Imp_Asset `protobuf:"bytes,8,rep,name=assets" json:"assets,omitempty"`                                      // 广告样式诉求
	ActionType   []ActionType            `protobuf:"varint,9,rep,name=actionType,enum=baiqingteng.ActionType" json:"actionType,omitempty"` // 期望广告交互类型
	MaxCount     *uint32                 `protobuf:"varint,10,opt,name=maxCount,def=1" json:"maxCount,omitempty"`                          // 期望返回广告个数，最大值，返回广告数小于等于该值
	Query        []byte                  `protobuf:"bytes,11,opt,name=query" json:"query,omitempty"`                                       // 搜索词，SUG广告此项必填
	Ext          []byte                  `protobuf:"bytes,13,opt,name=ext" json:"ext,omitempty"`                                           // 扩展字段
	PkgWhitelist []string                `protobuf:"bytes,14,rep,name=pkg_whitelist,json=pkgWhitelist" json:"pkg_whitelist,omitempty"`     // 限制召回的应用包名集合(如果该集合不为空, 需要严格按照此包名集合进行召回)
	PkgBlacklist []string                `protobuf:"bytes,15,rep,name=pkg_blacklist,json=pkgBlacklist" json:"pkg_blacklist,omitempty"`     // 包名黑名单
}

// Default values for BidRequest_Imp fields.
const (
	Default_BidRequest_Imp_Secure   = int32(1)
	Default_BidRequest_Imp_MaxCount = uint32(1)
)

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *BidRequest_Imp) GetAppId() []byte {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *BidRequest_Imp) GetTagId() []byte {
	if x != nil {
		return x.TagId
	}
	return nil
}

func (x *BidRequest_Imp) GetBidFloor() uint32 {
	if x != nil && x.BidFloor != nil {
		return *x.BidFloor
	}
	return 0
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil && x.Secure != nil {
		return *x.Secure
	}
	return Default_BidRequest_Imp_Secure
}

func (x *BidRequest_Imp) GetInstl() int32 {
	if x != nil && x.Instl != nil {
		return *x.Instl
	}
	return 0
}

func (x *BidRequest_Imp) GetAdType() AdType {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return AdType_NATIVE
}

func (x *BidRequest_Imp) GetAssets() []*BidRequest_Imp_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *BidRequest_Imp) GetActionType() []ActionType {
	if x != nil {
		return x.ActionType
	}
	return nil
}

func (x *BidRequest_Imp) GetMaxCount() uint32 {
	if x != nil && x.MaxCount != nil {
		return *x.MaxCount
	}
	return Default_BidRequest_Imp_MaxCount
}

func (x *BidRequest_Imp) GetQuery() []byte {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *BidRequest_Imp) GetExt() []byte {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidRequest_Imp) GetPkgWhitelist() []string {
	if x != nil {
		return x.PkgWhitelist
	}
	return nil
}

func (x *BidRequest_Imp) GetPkgBlacklist() []string {
	if x != nil {
		return x.PkgBlacklist
	}
	return nil
}

type BidRequest_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title []byte `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"` // 当前页title
	Page  []byte `protobuf:"bytes,2,opt,name=page" json:"page,omitempty"`   // 当前页面url
	Ref   []byte `protobuf:"bytes,3,opt,name=ref" json:"ref,omitempty"`     // referer url
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Site) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BidRequest_Site) GetPage() []byte {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *BidRequest_Site) GetRef() []byte {
	if x != nil {
		return x.Ref
	}
	return nil
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bundle  []byte `protobuf:"bytes,1,opt,name=bundle" json:"bundle,omitempty"`   // 应用包名
	Version []byte `protobuf:"bytes,2,opt,name=version" json:"version,omitempty"` // 应用版本号,example:10.0.1
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_App) GetBundle() []byte {
	if x != nil {
		return x.Bundle
	}
	return nil
}

func (x *BidRequest_App) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            []byte                    `protobuf:"bytes,1,opt,name=ip" json:"ip,omitempty"`                                                 // ip地址
	Ipv6          []byte                    `protobuf:"bytes,2,opt,name=ipv6" json:"ipv6,omitempty"`                                             // ipv6地址
	Ua            []byte                    `protobuf:"bytes,3,opt,name=ua" json:"ua,omitempty"`                                                 // User Agent
	Geo           []*BidRequest_Device_Geo  `protobuf:"bytes,4,rep,name=geo" json:"geo,omitempty"`                                               // 经纬度信息
	Carrier       *Carrier                  `protobuf:"varint,5,opt,name=carrier,enum=baiqingteng.Carrier" json:"carrier,omitempty"`             // 运营商ID
	NetworkType   *NetworkType              `protobuf:"varint,6,opt,name=networkType,enum=baiqingteng.NetworkType" json:"networkType,omitempty"` // 网络类型
	DeviceType    *DeviceType               `protobuf:"varint,7,opt,name=deviceType,enum=baiqingteng.DeviceType" json:"deviceType,omitempty"`    // 设备类型
	Os            *OS                       `protobuf:"varint,8,opt,name=os,enum=baiqingteng.OS" json:"os,omitempty"`                            // 操作系统类型
	Osv           []byte                    `protobuf:"bytes,9,opt,name=osv" json:"osv,omitempty"`                                               // 操作系统版本号,例如4.2.1
	Make          []byte                    `protobuf:"bytes,10,opt,name=make" json:"make,omitempty"`                                            // 设备厂商名称，中文需要UTF-8 编码。例如：Apple
	Model         []byte                    `protobuf:"bytes,11,opt,name=model" json:"model,omitempty"`                                          // 设备型号，中文需要UTF-8 编码。样例：MX5
	RomName       []byte                    `protobuf:"bytes,12,opt,name=romName" json:"romName,omitempty"`                                      // 厂商定制化ROM名
	RomVersion    []byte                    `protobuf:"bytes,13,opt,name=romVersion" json:"romVersion,omitempty"`                                // 厂商定制化系统ROM版本
	W             *int32                    `protobuf:"varint,14,opt,name=w" json:"w,omitempty"`                                                 // 移动设备屏幕宽
	H             *int32                    `protobuf:"varint,15,opt,name=h" json:"h,omitempty"`                                                 // 移动设备屏幕高
	Uid           *BidRequest_Device_Uid    `protobuf:"bytes,16,opt,name=uid" json:"uid,omitempty"`                                              // 移动设备序列号标识字段。允许同时存储多个序列号
	AppList       []uint32                  `protobuf:"varint,17,rep,name=appList" json:"appList,omitempty"`                                     // 设备上已安装的APP信息，参考applist文档
	BoostTime     *uint64                   `protobuf:"varint,18,opt,name=boostTime" json:"boostTime,omitempty"`
	SysUpdateTime *uint64                   `protobuf:"varint,19,opt,name=sysUpdateTime" json:"sysUpdateTime,omitempty"`
	Bstime        []byte                    `protobuf:"bytes,20,opt,name=bstime" json:"bstime,omitempty"`
	Aftime        []byte                    `protobuf:"bytes,21,opt,name=aftime" json:"aftime,omitempty"`
	Sftime        []byte                    `protobuf:"bytes,22,opt,name=sftime" json:"sftime,omitempty"`
	Fbtime        []byte                    `protobuf:"bytes,23,opt,name=fbtime" json:"fbtime,omitempty"`
	Factor        *BidRequest_Device_Factor `protobuf:"bytes,24,opt,name=factor" json:"factor,omitempty"`
	Paid          []byte                    `protobuf:"bytes,25,opt,name=paid" json:"paid,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Device) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *BidRequest_Device) GetIpv6() []byte {
	if x != nil {
		return x.Ipv6
	}
	return nil
}

func (x *BidRequest_Device) GetUa() []byte {
	if x != nil {
		return x.Ua
	}
	return nil
}

func (x *BidRequest_Device) GetGeo() []*BidRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetCarrier() Carrier {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return Carrier_Unknown
}

func (x *BidRequest_Device) GetNetworkType() NetworkType {
	if x != nil && x.NetworkType != nil {
		return *x.NetworkType
	}
	return NetworkType_UNKNOWN_NETWORK
}

func (x *BidRequest_Device) GetDeviceType() DeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return DeviceType_UNKNOW_DEVICE
}

func (x *BidRequest_Device) GetOs() OS {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return OS_UNKNOWN_OS
}

func (x *BidRequest_Device) GetOsv() []byte {
	if x != nil {
		return x.Osv
	}
	return nil
}

func (x *BidRequest_Device) GetMake() []byte {
	if x != nil {
		return x.Make
	}
	return nil
}

func (x *BidRequest_Device) GetModel() []byte {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *BidRequest_Device) GetRomName() []byte {
	if x != nil {
		return x.RomName
	}
	return nil
}

func (x *BidRequest_Device) GetRomVersion() []byte {
	if x != nil {
		return x.RomVersion
	}
	return nil
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Device) GetUid() *BidRequest_Device_Uid {
	if x != nil {
		return x.Uid
	}
	return nil
}

func (x *BidRequest_Device) GetAppList() []uint32 {
	if x != nil {
		return x.AppList
	}
	return nil
}

func (x *BidRequest_Device) GetBoostTime() uint64 {
	if x != nil && x.BoostTime != nil {
		return *x.BoostTime
	}
	return 0
}

func (x *BidRequest_Device) GetSysUpdateTime() uint64 {
	if x != nil && x.SysUpdateTime != nil {
		return *x.SysUpdateTime
	}
	return 0
}

func (x *BidRequest_Device) GetBstime() []byte {
	if x != nil {
		return x.Bstime
	}
	return nil
}

func (x *BidRequest_Device) GetAftime() []byte {
	if x != nil {
		return x.Aftime
	}
	return nil
}

func (x *BidRequest_Device) GetSftime() []byte {
	if x != nil {
		return x.Sftime
	}
	return nil
}

func (x *BidRequest_Device) GetFbtime() []byte {
	if x != nil {
		return x.Fbtime
	}
	return nil
}

func (x *BidRequest_Device) GetFactor() *BidRequest_Device_Factor {
	if x != nil {
		return x.Factor
	}
	return nil
}

func (x *BidRequest_Device) GetPaid() []byte {
	if x != nil {
		return x.Paid
	}
	return nil
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender *Gender `protobuf:"varint,1,opt,name=gender,enum=baiqingteng.Gender" json:"gender,omitempty"` // 性别
	Age    *uint32 `protobuf:"varint,2,opt,name=age" json:"age,omitempty"`                               // 年龄
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_User) GetGender() Gender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return Gender_UNKNOWN
}

func (x *BidRequest_User) GetAge() uint32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

type BidRequest_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title  []byte   `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`    // 视频标题
	Tags   [][]byte `protobuf:"bytes,2,rep,name=tags" json:"tags,omitempty"`      // 视频的标签
	Length *int32   `protobuf:"varint,3,opt,name=length" json:"length,omitempty"` // 视频的播放时长
}

func (x *BidRequest_Video) Reset() {
	*x = BidRequest_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Video) ProtoMessage() {}

func (x *BidRequest_Video) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Video) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_Video) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BidRequest_Video) GetTags() [][]byte {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *BidRequest_Video) GetLength() int32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return 0
}

type BidRequest_LastWinInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dsp            *uint32 `protobuf:"varint,1,opt,name=dsp" json:"dsp,omitempty"`                      // 竞胜dsp
	Ecpm           *uint32 `protobuf:"varint,2,opt,name=ecpm" json:"ecpm,omitempty"`                    // 竞胜ecpm
	Type           *uint32 `protobuf:"varint,3,opt,name=type" json:"type,omitempty"`                    // 竞胜类型
	ExposeStatus   *uint32 `protobuf:"varint,4,opt,name=exposeStatus" json:"exposeStatus,omitempty"`    // 竞胜曝光状态
	ClickStatus    *uint32 `protobuf:"varint,5,opt,name=clickStatus" json:"clickStatus,omitempty"`      // 竞胜点击状态
	AdvertiserName []byte  `protobuf:"bytes,6,opt,name=advertiserName" json:"advertiserName,omitempty"` // 竞胜广告主名称
	AdTitle        []byte  `protobuf:"bytes,7,opt,name=adTitle" json:"adTitle,omitempty"`               // 竞胜广告标题
	FailReason     *uint32 `protobuf:"varint,8,opt,name=failReason" json:"failReason,omitempty"`        // 竞败原因
}

func (x *BidRequest_LastWinInfo) Reset() {
	*x = BidRequest_LastWinInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_LastWinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_LastWinInfo) ProtoMessage() {}

func (x *BidRequest_LastWinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_LastWinInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_LastWinInfo) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_LastWinInfo) GetDsp() uint32 {
	if x != nil && x.Dsp != nil {
		return *x.Dsp
	}
	return 0
}

func (x *BidRequest_LastWinInfo) GetEcpm() uint32 {
	if x != nil && x.Ecpm != nil {
		return *x.Ecpm
	}
	return 0
}

func (x *BidRequest_LastWinInfo) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidRequest_LastWinInfo) GetExposeStatus() uint32 {
	if x != nil && x.ExposeStatus != nil {
		return *x.ExposeStatus
	}
	return 0
}

func (x *BidRequest_LastWinInfo) GetClickStatus() uint32 {
	if x != nil && x.ClickStatus != nil {
		return *x.ClickStatus
	}
	return 0
}

func (x *BidRequest_LastWinInfo) GetAdvertiserName() []byte {
	if x != nil {
		return x.AdvertiserName
	}
	return nil
}

func (x *BidRequest_LastWinInfo) GetAdTitle() []byte {
	if x != nil {
		return x.AdTitle
	}
	return nil
}

func (x *BidRequest_LastWinInfo) GetFailReason() uint32 {
	if x != nil && x.FailReason != nil {
		return *x.FailReason
	}
	return 0
}

type BidRequest_Imp_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId *Template    `protobuf:"varint,1,opt,name=templateId,enum=baiqingteng.Template" json:"templateId,omitempty"` // 模板类型
	Ratio      *AspectRatio `protobuf:"varint,2,opt,name=ratio,enum=baiqingteng.AspectRatio" json:"ratio,omitempty"`        // 物料宽高比
	Duration   *uint32      `protobuf:"varint,3,opt,name=duration" json:"duration,omitempty"`                               // 视频最大时长，单位秒---------仅视频有用，且只支持一个时长
}

func (x *BidRequest_Imp_Asset) Reset() {
	*x = BidRequest_Imp_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Asset) ProtoMessage() {}

func (x *BidRequest_Imp_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Asset.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Asset) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Asset) GetTemplateId() Template {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return Template_SIMG
}

func (x *BidRequest_Imp_Asset) GetRatio() AspectRatio {
	if x != nil && x.Ratio != nil {
		return *x.Ratio
	}
	return AspectRatio_RATIO_2X1
}

func (x *BidRequest_Imp_Asset) GetDuration() uint32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

type BidRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type *GeoType `protobuf:"varint,1,opt,name=type,enum=baiqingteng.GeoType" json:"type,omitempty"` // 地图坐标标准
	Lat  *float32 `protobuf:"fixed32,2,opt,name=lat" json:"lat,omitempty"`                           // 维度
	Lon  *float32 `protobuf:"fixed32,3,opt,name=lon" json:"lon,omitempty"`                           // 经度
}

func (x *BidRequest_Device_Geo) Reset() {
	*x = BidRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Geo) ProtoMessage() {}

func (x *BidRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *BidRequest_Device_Geo) GetType() GeoType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return GeoType_BD_09
}

func (x *BidRequest_Device_Geo) GetLat() float32 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetLon() float32 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

type BidRequest_Device_Uid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Did     []byte                        `protobuf:"bytes,1,opt,name=did" json:"did,omitempty"`          // IMEI
	DidMd5  []byte                        `protobuf:"bytes,2,opt,name=didMd5" json:"didMd5,omitempty"`    // 经MD5加密的IMEI
	Dpid    []byte                        `protobuf:"bytes,3,opt,name=dpid" json:"dpid,omitempty"`        // ANDROID ID
	DpidMd5 []byte                        `protobuf:"bytes,4,opt,name=dpidMd5" json:"dpidMd5,omitempty"`  // 经MD5加密的ANDROID ID
	Idfa    []byte                        `protobuf:"bytes,5,opt,name=idfa" json:"idfa,omitempty"`        // IDFA
	IdfaMd5 []byte                        `protobuf:"bytes,6,opt,name=idfaMd5" json:"idfaMd5,omitempty"`  // 经MD5加密的IDFA
	Mac     []byte                        `protobuf:"bytes,7,opt,name=mac" json:"mac,omitempty"`          // MAC
	MacMd5  []byte                        `protobuf:"bytes,8,opt,name=macMd5" json:"macMd5,omitempty"`    // 经MD5加密的MAC
	Oaid    []byte                        `protobuf:"bytes,9,opt,name=oaid" json:"oaid,omitempty"`        // OAID
	OaidMd5 []byte                        `protobuf:"bytes,10,opt,name=oaidMd5" json:"oaidMd5,omitempty"` // 经MD5加密的OAID
	Caid    []*BidRequest_Device_Uid_Caid `protobuf:"bytes,11,rep,name=caid" json:"caid,omitempty"`       // Caid
}

func (x *BidRequest_Device_Uid) Reset() {
	*x = BidRequest_Device_Uid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Uid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Uid) ProtoMessage() {}

func (x *BidRequest_Device_Uid) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Uid.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Uid) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3, 1}
}

func (x *BidRequest_Device_Uid) GetDid() []byte {
	if x != nil {
		return x.Did
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetDidMd5() []byte {
	if x != nil {
		return x.DidMd5
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetDpid() []byte {
	if x != nil {
		return x.Dpid
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetDpidMd5() []byte {
	if x != nil {
		return x.DpidMd5
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetIdfa() []byte {
	if x != nil {
		return x.Idfa
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetIdfaMd5() []byte {
	if x != nil {
		return x.IdfaMd5
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetMac() []byte {
	if x != nil {
		return x.Mac
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetMacMd5() []byte {
	if x != nil {
		return x.MacMd5
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetOaid() []byte {
	if x != nil {
		return x.Oaid
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetOaidMd5() []byte {
	if x != nil {
		return x.OaidMd5
	}
	return nil
}

func (x *BidRequest_Device_Uid) GetCaid() []*BidRequest_Device_Uid_Caid {
	if x != nil {
		return x.Caid
	}
	return nil
}

type BidRequest_Device_Factor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appver     []byte                               `protobuf:"bytes,1,opt,name=appver" json:"appver,omitempty"`
	Sdkver     []byte                               `protobuf:"bytes,2,opt,name=sdkver" json:"sdkver,omitempty"`
	BundleId   []byte                               `protobuf:"bytes,3,opt,name=bundleId" json:"bundleId,omitempty"`
	CaidFactor *BidRequest_Device_Factor_CaidFactor `protobuf:"bytes,4,opt,name=caidFactor" json:"caidFactor,omitempty"`
}

func (x *BidRequest_Device_Factor) Reset() {
	*x = BidRequest_Device_Factor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Factor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Factor) ProtoMessage() {}

func (x *BidRequest_Device_Factor) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Factor.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Factor) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3, 2}
}

func (x *BidRequest_Device_Factor) GetAppver() []byte {
	if x != nil {
		return x.Appver
	}
	return nil
}

func (x *BidRequest_Device_Factor) GetSdkver() []byte {
	if x != nil {
		return x.Sdkver
	}
	return nil
}

func (x *BidRequest_Device_Factor) GetBundleId() []byte {
	if x != nil {
		return x.BundleId
	}
	return nil
}

func (x *BidRequest_Device_Factor) GetCaidFactor() *BidRequest_Device_Factor_CaidFactor {
	if x != nil {
		return x.CaidFactor
	}
	return nil
}

type BidRequest_Device_Uid_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           []byte  `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                      // caid的值
	GenerateTime *uint64 `protobuf:"varint,2,opt,name=generateTime" json:"generateTime,omitempty"` // id生成时间
	Version      []byte  `protobuf:"bytes,3,opt,name=version" json:"version,omitempty"`            // id版本
	Vendor       *uint32 `protobuf:"varint,5,opt,name=vendor" json:"vendor,omitempty"`             // id供应商，0热云 1通信院 3阿里因子AAID
}

func (x *BidRequest_Device_Uid_Caid) Reset() {
	*x = BidRequest_Device_Uid_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Uid_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Uid_Caid) ProtoMessage() {}

func (x *BidRequest_Device_Uid_Caid) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Uid_Caid.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Uid_Caid) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3, 1, 0}
}

func (x *BidRequest_Device_Uid_Caid) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *BidRequest_Device_Uid_Caid) GetGenerateTime() uint64 {
	if x != nil && x.GenerateTime != nil {
		return *x.GenerateTime
	}
	return 0
}

func (x *BidRequest_Device_Uid_Caid) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *BidRequest_Device_Uid_Caid) GetVendor() uint32 {
	if x != nil && x.Vendor != nil {
		return *x.Vendor
	}
	return 0
}

type BidRequest_Device_Factor_CaidFactor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Disk           []byte `protobuf:"bytes,1,opt,name=disk" json:"disk,omitempty"`
	Model          []byte `protobuf:"bytes,2,opt,name=model" json:"model,omitempty"`
	PhysicalMemory []byte `protobuf:"bytes,3,opt,name=physicalMemory" json:"physicalMemory,omitempty"`
	SysFileTime    []byte `protobuf:"bytes,4,opt,name=sysFileTime" json:"sysFileTime,omitempty"`
	BootSecTime    []byte `protobuf:"bytes,5,opt,name=bootSecTime" json:"bootSecTime,omitempty"`
	SystemVersion  []byte `protobuf:"bytes,6,opt,name=systemVersion" json:"systemVersion,omitempty"`
	Machine        []byte `protobuf:"bytes,7,opt,name=machine" json:"machine,omitempty"`
	DeviceName     []byte `protobuf:"bytes,8,opt,name=deviceName" json:"deviceName,omitempty"`
	Language       []byte `protobuf:"bytes,9,opt,name=language" json:"language,omitempty"`
	TimeZone       []byte `protobuf:"bytes,10,opt,name=timeZone" json:"timeZone,omitempty"`
	CarrierInfo    []byte `protobuf:"bytes,11,opt,name=carrierInfo" json:"carrierInfo,omitempty"`
	CountryCode    []byte `protobuf:"bytes,12,opt,name=countryCode" json:"countryCode,omitempty"`
}

func (x *BidRequest_Device_Factor_CaidFactor) Reset() {
	*x = BidRequest_Device_Factor_CaidFactor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Factor_CaidFactor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Factor_CaidFactor) ProtoMessage() {}

func (x *BidRequest_Device_Factor_CaidFactor) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Factor_CaidFactor.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Factor_CaidFactor) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{0, 3, 2, 0}
}

func (x *BidRequest_Device_Factor_CaidFactor) GetDisk() []byte {
	if x != nil {
		return x.Disk
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetModel() []byte {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetPhysicalMemory() []byte {
	if x != nil {
		return x.PhysicalMemory
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetSysFileTime() []byte {
	if x != nil {
		return x.SysFileTime
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetBootSecTime() []byte {
	if x != nil {
		return x.BootSecTime
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetSystemVersion() []byte {
	if x != nil {
		return x.SystemVersion
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetMachine() []byte {
	if x != nil {
		return x.Machine
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetDeviceName() []byte {
	if x != nil {
		return x.DeviceName
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetLanguage() []byte {
	if x != nil {
		return x.Language
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetTimeZone() []byte {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetCarrierInfo() []byte {
	if x != nil {
		return x.CarrierInfo
	}
	return nil
}

func (x *BidRequest_Device_Factor_CaidFactor) GetCountryCode() []byte {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

type Adm_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    []byte  `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`        // 图片地址
	Width  *uint32 `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`   // 图片宽
	Height *uint32 `protobuf:"varint,3,opt,name=height" json:"height,omitempty"` // 图片高
}

func (x *Adm_Image) Reset() {
	*x = Adm_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adm_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adm_Image) ProtoMessage() {}

func (x *Adm_Image) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adm_Image.ProtoReflect.Descriptor instead.
func (*Adm_Image) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Adm_Image) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *Adm_Image) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Adm_Image) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

type Adm_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      []byte  `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`            // 视频地址
	Width    *uint32 `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`       // 视频宽度
	Height   *uint32 `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`     // 视频高度
	Duration *uint32 `protobuf:"varint,4,opt,name=duration" json:"duration,omitempty"` // 视频时长
	Size     *uint32 `protobuf:"varint,5,opt,name=size" json:"size,omitempty"`         // 视频大小，单位kb
}

func (x *Adm_Video) Reset() {
	*x = Adm_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adm_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adm_Video) ProtoMessage() {}

func (x *Adm_Video) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adm_Video.ProtoReflect.Descriptor instead.
func (*Adm_Video) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Adm_Video) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *Adm_Video) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Adm_Video) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Adm_Video) GetDuration() uint32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *Adm_Video) GetSize() uint32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid    []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`       // 广告数组
	Seat   []byte                     `protobuf:"bytes,2,opt,name=seat" json:"seat,omitempty"`     // 出价者
	AdLogo []byte                     `protobuf:"bytes,3,opt,name=adLogo" json:"adLogo,omitempty"` // 广告图标
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{3, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() []byte {
	if x != nil {
		return x.Seat
	}
	return nil
}

func (x *BidResponse_SeatBid) GetAdLogo() []byte {
	if x != nil {
		return x.AdLogo
	}
	return nil
}

type BidResponse_SeatBid_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          []byte          `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                     // 唯一表示该bid
	ImpId       []byte          `protobuf:"bytes,2,opt,name=impId" json:"impId,omitempty"`               // 与请求中IMP的id对应
	TagId       []byte          `protobuf:"bytes,3,opt,name=tagId" json:"tagId,omitempty"`               // 广告位id
	Crid        []byte          `protobuf:"bytes,4,opt,name=crid" json:"crid,omitempty"`                 // 创意id，可用做广告id
	Adm         *Adm            `protobuf:"bytes,5,opt,name=adm" json:"adm,omitempty"`                   // 广告创意元素
	ShowUrl     [][]byte        `protobuf:"bytes,6,rep,name=showUrl" json:"showUrl,omitempty"`           // 广告在端上展现时的展现监测
	ClickUrl    [][]byte        `protobuf:"bytes,7,rep,name=clickUrl" json:"clickUrl,omitempty"`         // 点击监测
	Price       *uint32         `protobuf:"varint,8,opt,name=price" json:"price,omitempty"`              // 出价，单位分，以CPM表示
	Nurl        [][]byte        `protobuf:"bytes,9,rep,name=nurl" json:"nurl,omitempty"`                 // 竞标成功通知url
	Lurl        [][]byte        `protobuf:"bytes,10,rep,name=lurl" json:"lurl,omitempty"`                // 竞标失败通知url
	Mons        []*TrackMonitor `protobuf:"bytes,11,rep,name=mons" json:"mons,omitempty"`                // 监控
	MtCompLevel *uint32         `protobuf:"varint,12,opt,name=mtCompLevel" json:"mtCompLevel,omitempty"` // 高ctr 标识
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baiqingteng_1_0_17_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_baiqingteng_1_0_17_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_baiqingteng_1_0_17_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetImpId() []byte {
	if x != nil {
		return x.ImpId
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetTagId() []byte {
	if x != nil {
		return x.TagId
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetCrid() []byte {
	if x != nil {
		return x.Crid
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAdm() *Adm {
	if x != nil {
		return x.Adm
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetShowUrl() [][]byte {
	if x != nil {
		return x.ShowUrl
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetClickUrl() [][]byte {
	if x != nil {
		return x.ClickUrl
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetPrice() uint32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetNurl() [][]byte {
	if x != nil {
		return x.Nurl
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetLurl() [][]byte {
	if x != nil {
		return x.Lurl
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetMons() []*TrackMonitor {
	if x != nil {
		return x.Mons
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetMtCompLevel() uint32 {
	if x != nil && x.MtCompLevel != nil {
		return *x.MtCompLevel
	}
	return 0
}

var File_baiqingteng_1_0_17_proto protoreflect.FileDescriptor

var file_baiqingteng_1_0_17_proto_rawDesc = []byte{
	0x0a, 0x18, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x5f, 0x31, 0x2e,
	0x30, 0x2e, 0x31, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x62, 0x61, 0x69, 0x71,
	0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x22, 0xc4, 0x1a, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74,
	0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x36, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67,
	0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x30,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x62,
	0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x33, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x19, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74,
	0x12, 0x47, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x57, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67,
	0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4c, 0x61, 0x73, 0x74, 0x57, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x61, 0x73,
	0x74, 0x57, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0xcd, 0x04, 0x0a, 0x03, 0x49, 0x6d,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x06, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31, 0x52, 0x06, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x62, 0x61, 0x69,
	0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e,
	0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67,
	0x74, 0x65, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x08, 0x6d,
	0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31,
	0x52, 0x08, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x65,
	0x78, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6b, 0x67, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6b, 0x67, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6b, 0x67, 0x5f, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x6b, 0x67, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x8a, 0x01, 0x0a,
	0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x62, 0x61, 0x69,
	0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x62,
	0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x41, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x42, 0x0a, 0x04, 0x53, 0x69, 0x74,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72,
	0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x72, 0x65, 0x66, 0x1a, 0x37, 0x0a,
	0x03, 0x41, 0x70, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xa1, 0x0e, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x02, 0x75, 0x61, 0x12, 0x34, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x2e, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x62,
	0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x0b, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x62, 0x61,
	0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x62,
	0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x4f, 0x53, 0x52, 0x02, 0x6f,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03,
	0x6f, 0x73, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x72, 0x6f, 0x6d,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x68, 0x12, 0x34, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x55, 0x69, 0x64, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x79, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x73, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x62, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x66, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x61, 0x66, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x66, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x66, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x66, 0x62, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x66, 0x62, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e,
	0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x06,
	0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x1a, 0x53, 0x0a, 0x03, 0x47, 0x65,
	0x6f, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c,
	0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x1a,
	0x8e, 0x03, 0x0a, 0x03, 0x55, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x64,
	0x4d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x64, 0x69, 0x64, 0x4d, 0x64,
	0x35, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x64, 0x70, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x70, 0x69, 0x64, 0x4d, 0x64, 0x35,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x69,
	0x64, 0x66, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6f, 0x61,
	0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x3b, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x55, 0x69, 0x64, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x52, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x1a, 0x6c, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x1a, 0xa7, 0x04, 0x0a, 0x06, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x61, 0x70, 0x70,
	0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x64, 0x6b, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x64, 0x6b, 0x76, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x0a, 0x63, 0x61, 0x69, 0x64, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x62, 0x61,
	0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0a, 0x63,
	0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0xfe, 0x02, 0x0a, 0x0a, 0x43, 0x61,
	0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x73, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x14, 0x0a, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x70, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x79,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x73, 0x79, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x62, 0x6f, 0x6f, 0x74, 0x53, 0x65, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x53, 0x65, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x45, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67,
	0x65, 0x1a, 0x49, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x1a, 0xef, 0x01, 0x0a,
	0x0b, 0x4c, 0x61, 0x73, 0x74, 0x57, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x65, 0x63, 0x70, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x65, 0x63,
	0x70, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xb9,
	0x07, 0x0a, 0x03, 0x41, 0x64, 0x6d, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x62, 0x61, 0x69,
	0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e,
	0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x2c,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x6d, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x22, 0x0a, 0x0c,
	0x6c, 0x61, 0x6e, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x6c, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x75, 0x6c, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6c, 0x6b, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x75, 0x6c, 0x6b,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x70,
	0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x13, 0x61, 0x70, 0x70, 0x49, 0x6e,
	0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x28,
	0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x69, 0x64, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x4d, 0x69, 0x64, 0x50, 0x61, 0x67, 0x65, 0x1a, 0x47, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x1a, 0x77, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x4f, 0x0a, 0x0c, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x62, 0x61, 0x69, 0x71,
	0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xb8, 0x04, 0x0a, 0x0b,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72,
	0x65, 0x71, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69,
	0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74,
	0x42, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x62, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6e, 0x62, 0x72, 0x1a, 0xae, 0x03, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x12, 0x36, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x61,
	0x64, 0x4c, 0x6f, 0x67, 0x6f, 0x1a, 0xbe, 0x02, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x69, 0x6d,
	0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x22, 0x0a,
	0x03, 0x61, 0x64, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x61, 0x69,
	0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x2e, 0x41, 0x64, 0x6d, 0x52, 0x03, 0x61, 0x64,
	0x6d, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x75, 0x72,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x04, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x2d, 0x0a, 0x04, 0x6d, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e,
	0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x04,
	0x6d, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x2a, 0x39, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x50,
	0x41, 0x47, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41,
	0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10,
	0x02, 0x2a, 0x38, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e,
	0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41, 0x53,
	0x48, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x10, 0x06, 0x2a, 0x3b, 0x0a, 0x08, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x49, 0x4d, 0x47, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x49, 0x4d, 0x47, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x47,
	0x49, 0x4d, 0x47, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x56, 0x49, 0x44, 0x10, 0x04, 0x12, 0x08,
	0x0a, 0x04, 0x52, 0x56, 0x49, 0x44, 0x10, 0x05, 0x2a, 0x87, 0x01, 0x0a, 0x0b, 0x41, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x5f, 0x32, 0x58, 0x31, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x5f, 0x33, 0x58, 0x32, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f,
	0x32, 0x58, 0x33, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x31,
	0x36, 0x58, 0x39, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x39,
	0x58, 0x31, 0x36, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x31,
	0x58, 0x31, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x34, 0x58,
	0x33, 0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x33, 0x58, 0x31,
	0x10, 0x08, 0x2a, 0x3b, 0x0a, 0x07, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x65, 0x6c, 0x65, 0x63, 0x6f,
	0x6d, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x6e, 0x69, 0x63, 0x6f, 0x6d, 0x10, 0x03, 0x2a,
	0x68, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13,
	0x0a, 0x0f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x4b, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x35, 0x47, 0x10, 0x05, 0x2a, 0x36, 0x0a, 0x0a, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10,
	0x02, 0x2a, 0x37, 0x0a, 0x02, 0x4f, 0x53, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x5f, 0x4f, 0x53, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x03, 0x2a, 0x3a, 0x0a, 0x07, 0x47, 0x65,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x44, 0x5f, 0x30, 0x39, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x47, 0x43, 0x4a, 0x5f, 0x30, 0x32, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x57, 0x47, 0x53, 0x5f, 0x38, 0x34, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x44, 0x5f, 0x30,
	0x39, 0x5f, 0x4c, 0x4c, 0x10, 0x03, 0x2a, 0x2b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c,
	0x45, 0x10, 0x02, 0x2a, 0xde, 0x01, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x10, 0x64, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x43, 0x4c, 0x4f,
	0x53, 0x45, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x66, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x45, 0x5f, 0x50, 0x4c, 0x41,
	0x59, 0x10, 0x67, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x50, 0x41, 0x55,
	0x53, 0x45, 0x10, 0x68, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x50, 0x4c,
	0x41, 0x59, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x69, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x5f, 0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x10,
	0x6a, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x4b, 0x49, 0x50, 0x10, 0x6b, 0x12, 0x13, 0x0a, 0x0f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x6c,
	0x12, 0x22, 0x0a, 0x1e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4f,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x42, 0x55, 0x54, 0x54,
	0x4f, 0x4e, 0x10, 0x6d, 0x42, 0x1c, 0x5a, 0x1a, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x2f, 0x70, 0x62, 0x2f, 0x62, 0x61, 0x69, 0x71, 0x69, 0x6e, 0x67, 0x74, 0x65, 0x6e, 0x67, 0x5f,
	0x75, 0x70,
}

var (
	file_baiqingteng_1_0_17_proto_rawDescOnce sync.Once
	file_baiqingteng_1_0_17_proto_rawDescData = file_baiqingteng_1_0_17_proto_rawDesc
)

func file_baiqingteng_1_0_17_proto_rawDescGZIP() []byte {
	file_baiqingteng_1_0_17_proto_rawDescOnce.Do(func() {
		file_baiqingteng_1_0_17_proto_rawDescData = protoimpl.X.CompressGZIP(file_baiqingteng_1_0_17_proto_rawDescData)
	})
	return file_baiqingteng_1_0_17_proto_rawDescData
}

var file_baiqingteng_1_0_17_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_baiqingteng_1_0_17_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_baiqingteng_1_0_17_proto_goTypes = []interface{}{
	(ActionType)(0),                             // 0: baiqingteng.ActionType
	(AdType)(0),                                 // 1: baiqingteng.AdType
	(Template)(0),                               // 2: baiqingteng.Template
	(AspectRatio)(0),                            // 3: baiqingteng.AspectRatio
	(Carrier)(0),                                // 4: baiqingteng.Carrier
	(NetworkType)(0),                            // 5: baiqingteng.NetworkType
	(DeviceType)(0),                             // 6: baiqingteng.DeviceType
	(OS)(0),                                     // 7: baiqingteng.OS
	(GeoType)(0),                                // 8: baiqingteng.GeoType
	(Gender)(0),                                 // 9: baiqingteng.Gender
	(TrackEvent)(0),                             // 10: baiqingteng.TrackEvent
	(*BidRequest)(nil),                          // 11: baiqingteng.BidRequest
	(*Adm)(nil),                                 // 12: baiqingteng.Adm
	(*TrackMonitor)(nil),                        // 13: baiqingteng.TrackMonitor
	(*BidResponse)(nil),                         // 14: baiqingteng.BidResponse
	(*BidRequest_Imp)(nil),                      // 15: baiqingteng.BidRequest.Imp
	(*BidRequest_Site)(nil),                     // 16: baiqingteng.BidRequest.Site
	(*BidRequest_App)(nil),                      // 17: baiqingteng.BidRequest.App
	(*BidRequest_Device)(nil),                   // 18: baiqingteng.BidRequest.Device
	(*BidRequest_User)(nil),                     // 19: baiqingteng.BidRequest.User
	(*BidRequest_Video)(nil),                    // 20: baiqingteng.BidRequest.Video
	(*BidRequest_LastWinInfo)(nil),              // 21: baiqingteng.BidRequest.LastWinInfo
	(*BidRequest_Imp_Asset)(nil),                // 22: baiqingteng.BidRequest.Imp.Asset
	(*BidRequest_Device_Geo)(nil),               // 23: baiqingteng.BidRequest.Device.Geo
	(*BidRequest_Device_Uid)(nil),               // 24: baiqingteng.BidRequest.Device.Uid
	(*BidRequest_Device_Factor)(nil),            // 25: baiqingteng.BidRequest.Device.Factor
	(*BidRequest_Device_Uid_Caid)(nil),          // 26: baiqingteng.BidRequest.Device.Uid.Caid
	(*BidRequest_Device_Factor_CaidFactor)(nil), // 27: baiqingteng.BidRequest.Device.Factor.CaidFactor
	(*Adm_Image)(nil),                           // 28: baiqingteng.Adm.Image
	(*Adm_Video)(nil),                           // 29: baiqingteng.Adm.Video
	(*BidResponse_SeatBid)(nil),                 // 30: baiqingteng.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),             // 31: baiqingteng.BidResponse.SeatBid.Bid
}
var file_baiqingteng_1_0_17_proto_depIdxs = []int32{
	15, // 0: baiqingteng.BidRequest.imp:type_name -> baiqingteng.BidRequest.Imp
	16, // 1: baiqingteng.BidRequest.site:type_name -> baiqingteng.BidRequest.Site
	17, // 2: baiqingteng.BidRequest.app:type_name -> baiqingteng.BidRequest.App
	18, // 3: baiqingteng.BidRequest.device:type_name -> baiqingteng.BidRequest.Device
	19, // 4: baiqingteng.BidRequest.user:type_name -> baiqingteng.BidRequest.User
	20, // 5: baiqingteng.BidRequest.video:type_name -> baiqingteng.BidRequest.Video
	21, // 6: baiqingteng.BidRequest.lastWinInfos:type_name -> baiqingteng.BidRequest.LastWinInfo
	2,  // 7: baiqingteng.Adm.templateId:type_name -> baiqingteng.Template
	0,  // 8: baiqingteng.Adm.actionType:type_name -> baiqingteng.ActionType
	28, // 9: baiqingteng.Adm.img:type_name -> baiqingteng.Adm.Image
	29, // 10: baiqingteng.Adm.video:type_name -> baiqingteng.Adm.Video
	10, // 11: baiqingteng.TrackMonitor.event:type_name -> baiqingteng.TrackEvent
	30, // 12: baiqingteng.BidResponse.seatBid:type_name -> baiqingteng.BidResponse.SeatBid
	1,  // 13: baiqingteng.BidRequest.Imp.adType:type_name -> baiqingteng.AdType
	22, // 14: baiqingteng.BidRequest.Imp.assets:type_name -> baiqingteng.BidRequest.Imp.Asset
	0,  // 15: baiqingteng.BidRequest.Imp.actionType:type_name -> baiqingteng.ActionType
	23, // 16: baiqingteng.BidRequest.Device.geo:type_name -> baiqingteng.BidRequest.Device.Geo
	4,  // 17: baiqingteng.BidRequest.Device.carrier:type_name -> baiqingteng.Carrier
	5,  // 18: baiqingteng.BidRequest.Device.networkType:type_name -> baiqingteng.NetworkType
	6,  // 19: baiqingteng.BidRequest.Device.deviceType:type_name -> baiqingteng.DeviceType
	7,  // 20: baiqingteng.BidRequest.Device.os:type_name -> baiqingteng.OS
	24, // 21: baiqingteng.BidRequest.Device.uid:type_name -> baiqingteng.BidRequest.Device.Uid
	25, // 22: baiqingteng.BidRequest.Device.factor:type_name -> baiqingteng.BidRequest.Device.Factor
	9,  // 23: baiqingteng.BidRequest.User.gender:type_name -> baiqingteng.Gender
	2,  // 24: baiqingteng.BidRequest.Imp.Asset.templateId:type_name -> baiqingteng.Template
	3,  // 25: baiqingteng.BidRequest.Imp.Asset.ratio:type_name -> baiqingteng.AspectRatio
	8,  // 26: baiqingteng.BidRequest.Device.Geo.type:type_name -> baiqingteng.GeoType
	26, // 27: baiqingteng.BidRequest.Device.Uid.caid:type_name -> baiqingteng.BidRequest.Device.Uid.Caid
	27, // 28: baiqingteng.BidRequest.Device.Factor.caidFactor:type_name -> baiqingteng.BidRequest.Device.Factor.CaidFactor
	31, // 29: baiqingteng.BidResponse.SeatBid.bid:type_name -> baiqingteng.BidResponse.SeatBid.Bid
	12, // 30: baiqingteng.BidResponse.SeatBid.Bid.adm:type_name -> baiqingteng.Adm
	13, // 31: baiqingteng.BidResponse.SeatBid.Bid.mons:type_name -> baiqingteng.TrackMonitor
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_baiqingteng_1_0_17_proto_init() }
func file_baiqingteng_1_0_17_proto_init() {
	if File_baiqingteng_1_0_17_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_baiqingteng_1_0_17_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackMonitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_LastWinInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Uid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Factor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Uid_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Factor_CaidFactor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adm_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adm_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baiqingteng_1_0_17_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_baiqingteng_1_0_17_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_baiqingteng_1_0_17_proto_goTypes,
		DependencyIndexes: file_baiqingteng_1_0_17_proto_depIdxs,
		EnumInfos:         file_baiqingteng_1_0_17_proto_enumTypes,
		MessageInfos:      file_baiqingteng_1_0_17_proto_msgTypes,
	}.Build()
	File_baiqingteng_1_0_17_proto = out.File
	file_baiqingteng_1_0_17_proto_rawDesc = nil
	file_baiqingteng_1_0_17_proto_goTypes = nil
	file_baiqingteng_1_0_17_proto_depIdxs = nil
}
