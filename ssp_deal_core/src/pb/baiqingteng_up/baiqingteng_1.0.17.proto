// Copyright Baidu Inc. All Rights Reserved.  
//  
// 文件内容: baidu ssp service 百度SSP协议文件  
//  

syntax = "proto2";

option go_package = "mh_proxy/pb/baiqingteng_up";

package baiqingteng;  

// 广告动作类型  
enum ActionType {  
	LANDINGPAGE = 0;    // 落地页跳转,默认项  
	DOWNLOAD = 1;       // 下载  
	DEEPLINK = 2;       // 应用唤醒  
};  
	
enum AdType {
 	NATIVE = 0; // 信息流广告
	SPLASH = 3; // 开屏广告
	RVIDEO = 4; // 激励视频广告
	INSERT = 6; // 插屏广告
}; 
	
enum Template {  
	SIMG = 1;           // 小图广告，适用于左图右文或右图左文  
	BIMG = 2;           // 大图广告  
	GIMG = 3;           // 组图广告，目前仅支持3图，  
	VID = 4;            // 视频广告，带封面图  
	RVID = 5;           // 激励视频  
};  
	
enum AspectRatio {  
	RATIO_2X1 = 1;      // 物料比例2:1  
	RATIO_3X2 = 2;      // 物料比例3:2  
	RATIO_2X3 = 3;      // 物料比例2:3  
	RATIO_16X9 = 4;     // 物料比例16:9  
	RATIO_9X16 = 5;     // 物料比例9:16  
	RATIO_1X1 = 6;      // 物料比例1:1  
	RATIO_4X3 = 7;      // 物料比例4:3  
	RATIO_3X1 = 8;      // 物料比例3:1  
};  
	
enum Carrier{  
	Unknown = 0;        // 未知运营商  
	Mobile = 1;         // 中国移动  
	Telecom = 2;        // 中国电信  
	Unicom = 3;         // 中国联通  
};  
	
enum NetworkType {  
	UNKNOWN_NETWORK = 0;// 未知网络  
	WIFI = 1;           // WIFI 有线  
	MOBILE_2G = 2;      // 2G  
	MOBILE_3G = 3;      // 3G  
	MOBILE_4G = 4;      // 4G  
	MOBILE_5G = 5;      // 5G  
};  
	
enum DeviceType {  
	UNKNOW_DEVICE = 0;  // 未知设备  
	PHONE = 1;          // 手机  
	TABLET = 2;         // 平板  
};  
	
enum OS {  
	UNKNOWN_OS = 0;     // 未知操作系统  
	IOS = 1;  
	ANDROID = 2;  
	WINDOWS = 3;  
};  
	
// 经纬度坐标标准  
enum GeoType {  
	BD_09 = 0;          // 百度地图的经纬度坐标标准  
	GCJ_02 = 1;         // 国测局制定的经纬度坐标标准  
	WGS_84 = 2;         // 国际经纬度坐标标准  
	BD_09_LL = 3;       // 百度地图的墨卡坐标标准,以米为单位  
}  
	
enum Gender {  
	UNKNOWN = 0;        // 未知  
	MALE = 1;           // 男  
	FEMALE = 2;         // 女  
};  
	
message BidRequest {  
	message Imp {  
		optional bytes id = 1;  
		optional bytes appId = 2;               // 百度提供，广告app唯一id  
		optional bytes tagId = 3;               // 广告位id  
		optional uint32 bidFloor = 4;           // CPM底价 --------百度单位为分，如有特殊情况请说明  
		optional int32 secure = 5[default=1];   // 0 HTTP, 1 HTTPS  
		optional int32 instl = 6;               // 是否为全屏广告，0 非全屏， 1 全屏  
		optional AdType adType = 7;             // 广告位类型  
		message Asset {  
			optional Template templateId = 1;   // 模板类型  
			optional AspectRatio ratio = 2;     // 物料宽高比  
			optional uint32 duration = 3;       // 视频最大时长，单位秒---------仅视频有用，且只支持一个时长  
		};  
		repeated Asset assets = 8;              // 广告样式诉求  
		repeated ActionType actionType = 9;     // 期望广告交互类型  
		optional uint32 maxCount = 10[default=1];// 期望返回广告个数，最大值，返回广告数小于等于该值  
		optional bytes query = 11;              // 搜索词，SUG广告此项必填  
		optional bytes ext = 13;                // 扩展字段 
		repeated string pkg_whitelist = 14;     // 限制召回的应用包名集合(如果该集合不为空, 需要严格按照此包名集合进行召回)
		repeated string pkg_blacklist = 15;     // 包名黑名单
	}  
	message Site {  
		optional bytes title = 1;               // 当前页title  
		optional bytes page = 2;                // 当前页面url  
		optional bytes ref = 3;                 // referer url  
	}  
	message App {  
		optional bytes bundle = 1;              // 应用包名  
		optional bytes version = 2;             // 应用版本号,example:10.0.1  
	}  
	message Device {  
		optional bytes ip = 1;                  // ip地址  
		optional bytes ipv6 = 2;                // ipv6地址  
		optional bytes ua = 3;                  // User Agent  
		message Geo {  
			optional GeoType type = 1;          // 地图坐标标准  
			optional float lat = 2;             // 维度  
			optional float lon = 3;             // 经度  
		}  
		repeated Geo geo = 4;                   // 经纬度信息  
		optional Carrier carrier = 5;           // 运营商ID  
		optional NetworkType networkType = 6;   // 网络类型  
		optional DeviceType deviceType = 7;     // 设备类型  
		optional OS os = 8;                     // 操作系统类型  
		optional bytes osv = 9;                 // 操作系统版本号,例如4.2.1  
		optional bytes make = 10;               // 设备厂商名称，中文需要UTF-8 编码。例如：Apple  
		optional bytes model = 11;              // 设备型号，中文需要UTF-8 编码。样例：MX5  
		optional bytes romName = 12;            // 厂商定制化ROM名  
		optional bytes romVersion = 13;         // 厂商定制化系统ROM版本  
		optional int32 w = 14;                  // 移动设备屏幕宽  
		optional int32 h = 15;                  // 移动设备屏幕高  
		message Uid {  
			optional bytes did = 1;             // IMEI  
			optional bytes didMd5 = 2;          // 经MD5加密的IMEI  
			optional bytes dpid = 3;            // ANDROID ID  
			optional bytes dpidMd5 = 4;         // 经MD5加密的ANDROID ID  
			optional bytes idfa = 5;            // IDFA  
			optional bytes idfaMd5 = 6;         // 经MD5加密的IDFA  
			optional bytes mac = 7;             // MAC  
			optional bytes macMd5 = 8;          // 经MD5加密的MAC  
			optional bytes oaid = 9;            // OAID  
			optional bytes oaidMd5 = 10;        // 经MD5加密的OAID  
			message Caid {  
				optional bytes id = 1;              // caid的值  
				optional uint64 generateTime= 2;    // id生成时间  
				optional bytes version = 3;         // id版本  
				optional uint32 vendor = 5;         // id供应商，0热云 1通信院 3阿里因子AAID  
			}  
			repeated Caid caid = 11;            // Caid  
		};  
		optional Uid uid = 16;                  // 移动设备序列号标识字段。允许同时存储多个序列号  
		repeated uint32 appList = 17;           // 设备上已安装的APP信息，参考applist文档  
		optional uint64 boostTime = 18;  
		optional uint64 sysUpdateTime = 19;  
		optional bytes bstime = 20;  
		optional bytes aftime = 21;  
		optional bytes sftime = 22;  
		optional bytes fbtime = 23;  
		message Factor {
			optional bytes appver = 1;
			optional bytes sdkver = 2;
			optional bytes bundleId = 3;
			message CaidFactor {
				optional bytes disk = 1;
				optional bytes model = 2;
				optional bytes physicalMemory = 3;
				optional bytes sysFileTime = 4;
				optional bytes bootSecTime = 5;
				optional bytes systemVersion = 6;
				optional bytes machine = 7;
				optional bytes deviceName = 8;
				optional bytes language = 9;
				optional bytes timeZone = 10;
				optional bytes carrierInfo = 11;
				optional bytes countryCode = 12;
			}
			optional CaidFactor caidFactor = 4;
		};
		optional Factor factor = 24;
		optional bytes paid = 25;
	}  
	message User {  
		optional Gender gender = 1;             // 性别  
		optional uint32 age = 2;                // 年龄  
	}  
	message Video {  
		optional bytes title = 1;               // 视频标题  
		repeated bytes tags = 2;                // 视频的标签  
		optional int32 length = 3;              // 视频的播放时长  
	}  
	message LastWinInfo {  
		optional uint32 dsp = 1;                // 竞胜dsp  
		optional uint32 ecpm = 2;               // 竞胜ecpm  
		optional uint32 type = 3;               // 竞胜类型  
		optional uint32 exposeStatus = 4;       // 竞胜曝光状态  
		optional uint32 clickStatus = 5;        // 竞胜点击状态  
		optional bytes advertiserName = 6;      // 竞胜广告主名称  
		optional bytes adTitle = 7;             // 竞胜广告标题  
		optional uint32 failReason = 8;         // 竞败原因  
	}  
	optional uint32 mediaId = 1;                // 媒体id，唯一标识接入ADX  
	optional bytes reqId = 2;                   // 请求ID，唯一标识本次请求，明文字符串  
	repeated Imp imp = 3;                       // 广告位,仅支持单广告位,即仅第一个imp生效  
	optional Site site = 4;                     // 网站站点信息  
	optional App app = 5;                       // 应用信息  
	optional Device device = 6;                 // 用户设备信息  
	optional User user = 7;                     // 用户信息  
	optional Video video = 8;                   // 视频信息  
	optional bool test = 9 [default = false];   // 是否测试流量  
	repeated LastWinInfo lastWinInfos = 10;       // 上一次竞胜信息  
};  
	
message Adm {  
	optional Template templateId = 1;           // 广告的模板类型  
	optional ActionType actionType = 2;         // 广告的交互类型  
	optional bytes title = 3;                   // 标题  
	optional bytes desc = 4;                    // 描述  
	optional bytes icon = 5;                    // 图标地址  
	message Image {  
		optional bytes url = 1;                 // 图片地址  
		optional uint32 width = 2;              // 图片宽  
		optional uint32 height = 3;             // 图片高  
	};  
	repeated Image img = 6;                     // 图片信息  
	message Video {  
		optional bytes url = 1;                 // 视频地址  
		optional uint32 width = 2;              // 视频宽度  
		optional uint32 height = 3;             // 视频高度  
		optional uint32 duration = 4;           // 视频时长  
		optional uint32 size = 5;               // 视频大小，单位kb  
	};  
	optional Video video = 7;                   // 视频信息  
	optional bytes landdingPage = 8;            // 广告点击落地页/下载地址，deeplink失败是备用地址  
	optional bytes deeplink = 9;                // deeplink地址  
	optional bytes appName = 10;                // 应用名称  
	optional bytes packageName = 11;            // 应用包名  
	optional uint32 packageSize = 12;           // 应用包大小  
	optional bytes bundleId = 13;               // 用于IOS下载的id  
	optional bytes publisher = 14;              // 开发者  
	optional bytes appVersion = 15;             // 版本号  
	optional bytes privacy = 16;                // 隐私协议  
	optional bytes permission = 17;             // 用户权限  
	optional bytes ulk_url = 18;                // universal link url, 用于ios调起  
	optional bytes ulk_scheme = 19;             // universal link scheme, 用于ios嗅探  
	optional bytes appStoreLink = 20;           // 安卓直投下载地址
	optional bytes app_introduction_link = 21;   // 六要素之 APP产品功能介绍  链接  
	optional bytes downloadMidPage = 22; // 下载中间页
};  
	
enum TrackEvent {  
	VIDEO_START = 100;                      // 视频开始播放  
	VIDEO_CLOSE = 101;                      // 视频播放终止  
	VIDEO_READY_PLAY = 102;                 // 视频准备播放  
	VIDEO_CONTINUE_PLAY = 103;              // 视频继续播放  
	VIDEO_PAUSE = 104;                      // 视频播放中止  
	VIDEO_PLAY_END = 105;                   // 视频播放完成  
	VIDEO_REPEATED_PLAY = 106;              // 视频重复播放  
	SKIP = 107;                             // 视频跳过  
	VIDEO_PLAY_FAIL = 108;                  // 视频播放失败  
	VIDEO_TURN_ON_OFF_SOUND_BUTTON = 109;   // 打开关闭声音  
}  
	
message TrackMonitor {  
	optional TrackEvent event = 1;                   // 监控事件  
	repeated bytes url = 2;                     // 监控URL  
}  
	
message BidResponse {  
	optional bytes reqId = 1;                  // 对应请求ID  
	optional bytes bidId = 2;                  // 本次竞价ID  
	message SeatBid {  
		message Bid {  
			optional bytes id = 1;              // 唯一表示该bid  
			optional bytes impId = 2;           // 与请求中IMP的id对应  
			optional bytes tagId = 3;           // 广告位id  
			optional bytes crid = 4;            // 创意id，可用做广告id  
			optional Adm adm = 5;               // 广告创意元素  
			repeated bytes showUrl = 6;         // 广告在端上展现时的展现监测  
			repeated bytes clickUrl = 7;        // 点击监测  
			optional uint32 price = 8;          // 出价，单位分，以CPM表示  
			repeated bytes nurl = 9;            // 竞标成功通知url  
			repeated bytes lurl = 10;           // 竞标失败通知url  
			repeated TrackMonitor mons = 11;         // 监控  
			optional uint32 mtCompLevel = 12;         // 高ctr 标识  
		}  
		repeated Bid bid = 1;                   // 广告数组  
		optional bytes seat = 2;                // 出价者  
		optional bytes adLogo = 3;              // 广告图标  
	}  
	repeated SeatBid seatBid = 3;              // 对应广告位，仅支持单广告位  
	optional int32 nbr = 4;                     // 过滤状态，具体解释见文档  
}  
