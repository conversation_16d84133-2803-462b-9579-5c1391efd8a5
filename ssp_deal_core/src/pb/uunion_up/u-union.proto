syntax = "proto3";

option go_package = "mh_proxy/pb/uunion_up";

package uunion_up;

message BidRequest {
    int32 version = 1;
    string request_id = 2;
    repeated Impression imp = 3;
    App app = 4;
    Device device = 5;
    User user = 6;

    message Impression {
        int32 id = 1;          // 此impression在当前Request中的唯一id,从0开始
        optional int32 pos = 2; // 0 - 未知, 1~5 - 一~五屏, 6 - 五屏以下
        string slot_id = 3;     // 代码位id，u-union分配
        SlotType slot_type = 4; // 代码位类型
        int32 bid_floor = 5;
        enum SlotType {
            SLOT_TYPE_UNKNOWN = 0;
            BANNER   = 3;       // 横幅
            INTERSTITIAL   = 4; // 插屏
            NATIVE = 6;         // 信息流
            SPLASH  = 7;        // 开屏
        };
    }

    message App {
        string appkey = 1;          // 应用id(appkey)，uunion提供
        string name = 2;        // 应用名
        string version = 3;     // 应用版本
        string package_name = 4;// 应用包名
    };

    message Device {
        string ua = 1;
        string ip = 2;
        string ipv6 = 3;

        string ali_aaid = 4; // 阿里匿名设备标识，ios必填，android选填

        string imei = 5;
        string imei_md5 = 6;
        string android_id = 7;
        string mac = 8;
        string idfa = 9;
        string oaid = 10;
        string caid = 11;

        Carrier carrier = 12;
        ConnectionType connection_type = 13;

        string brand = 14;
        string model = 15;
        OS os = 16;
        string osv = 17;
        int32 w = 18;
        int32 h = 19;
        DeviceType device_type = 20;
        double lat = 21;
        double lon = 22;
        string boot_mark = 23;
        string update_mark = 24;

        enum Carrier {
            CARRIER_UNKNOWN   = 0;
            CHINA_MOBILE   = 1;
            CHINA_UNICOM  = 2;
            CHINA_TELECOM = 3;
            CHINA_NETCOM = 4;
        };

        enum OS {
            OS_UNKNOWN   = 0;
            IOS = 1;
            ANDROID =2;
        };

        enum ConnectionType {
            CON_UNKNOWN   = 0;
            CELL = 1;
            WIFI = 2;
            CELL_2G =3;
            CELL_3G =4;
            CELL_4G =5;
            CELL_5G =6;
        };

        enum DeviceType {
            DEVICE_UNKNOWN = 0;
            PHONE  = 1;
            TABLET = 2;
        };
    }

    message User {
        // 媒体侧用户关键词列表
        repeated string keywords = 1;
    }
}



message BidResponse {

    string request_id = 1; //与bidRequest中的一致
    string search_id = 2;
    int64 status = 3;

    repeated Seat seats = 4;

    message Seat {
        int32 id = 1; //对应请求中的impression的id，表示期望填充的对应impression
        repeated Ad ads = 2; // 广告素材
        message Ad {
            enum AdAction {
                AdActionUnknown = 0;
                Webview = 1; //webview
                Download = 2;//下载
                Deeplink = 3;//deeplink
            }
            AdAction action = 1;     // 跳转类型
            repeated Image image = 2;//选填，图片素材
            Video video = 3;         //选填，视频素材
            string landing_page_url = 4;   //选填，广告/创意的落地页url地址
            string title = 5;        //选填，广告标题
            string desc = 6;         //选填，广告描述
            Icon icon = 7;           //选填，创意中的icon的url
            string deeplink = 8;     //选填，deeplink跳转地址
            AppInfo app_info = 9;   //选填，下载类广告必传
            TrackUrls track_urls = 10; // 上报链接

            int64 bid_price = 11; //出价

            message TrackUrls {
                repeated string expose_urls = 1;     //选填，曝光上报
                repeated string click_urls = 2;      //选填，点击上报
                repeated string download_start = 3;  //选填，开始下载上报
                repeated string download_finish = 4; //选填，完成下载上报
                repeated string install_start = 5;   //选填，开始安装上报
                repeated string install_finish = 6;  //选填，安装完成上报
                repeated string deeplink_track = 7;
                repeated string deeplink_success = 8;//选填，deeplink调起成功上报
                repeated string deeplink_fail = 9;   //选填，deeplink调起失败上报
                repeated string video_play0 = 10;     //选填，视频播放进度上报，播放开始
                repeated string video_play1 = 11;    //选填，视频播放进度上报，播放25%
                repeated string video_play2 = 12;    //选填，视频播放进度上报，播放50%
                repeated string video_play3 = 13;    //选填，视频播放进度上报，播放75%
                repeated string video_play4 = 14;    //选填，视频播放进度上报，播放完成
                repeated string win_urls = 15;
            }

            message Image {
                string url = 1;//必填，图片地址
                uint32 w = 2;  //必填，宽度，像素
                uint32 h = 3;  //必填，高度，像素
            }
            message Icon {
                string url = 1;//必填，图片地址
                uint32 w = 2;  //必填，宽度，像素
                uint32 h = 3;  //必填，高度，像素
            }
            message Video {
                string cover_url = 1;//必填，视频封面图
                uint32 duration = 2; //必填，视频时长（单位秒）
                uint32 h = 3;        //必填，视频高，像素
                uint32 w = 4;        //必填，视频宽，像素
                uint64 size = 5;     //必填，视频大小，单位size
                string url = 6;      //必填，视频地址
            }
            message AppInfo {
                string app_name = 1;      //必填，应用名称
                string app_permission = 2;//必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
                string app_privacy = 3;   //必填，应用隐私政策url
                uint64 app_size = 4;      //必填，应用包的大小，单位byte
                string app_version = 5;   //必填，应用版本
                string developer = 6;     //必填，app开发者
                string download_url = 7;  //必填，应用下载url
                string package_name = 8;  //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
            }
        }
    }
}



