// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.2
// source: u-union.proto

package uunion_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest_Impression_SlotType int32

const (
	BidRequest_Impression_SLOT_TYPE_UNKNOWN BidRequest_Impression_SlotType = 0
	BidRequest_Impression_BANNER            BidRequest_Impression_SlotType = 3 // 横幅
	BidRequest_Impression_INTERSTITIAL      BidRequest_Impression_SlotType = 4 // 插屏
	BidRequest_Impression_NATIVE            BidRequest_Impression_SlotType = 6 // 信息流
	BidRequest_Impression_SPLASH            BidRequest_Impression_SlotType = 7 // 开屏
)

// Enum value maps for BidRequest_Impression_SlotType.
var (
	BidRequest_Impression_SlotType_name = map[int32]string{
		0: "SLOT_TYPE_UNKNOWN",
		3: "BANNER",
		4: "INTERSTITIAL",
		6: "NATIVE",
		7: "SPLASH",
	}
	BidRequest_Impression_SlotType_value = map[string]int32{
		"SLOT_TYPE_UNKNOWN": 0,
		"BANNER":            3,
		"INTERSTITIAL":      4,
		"NATIVE":            6,
		"SPLASH":            7,
	}
)

func (x BidRequest_Impression_SlotType) Enum() *BidRequest_Impression_SlotType {
	p := new(BidRequest_Impression_SlotType)
	*p = x
	return p
}

func (x BidRequest_Impression_SlotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Impression_SlotType) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[0].Descriptor()
}

func (BidRequest_Impression_SlotType) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[0]
}

func (x BidRequest_Impression_SlotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Impression_SlotType.Descriptor instead.
func (BidRequest_Impression_SlotType) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 0, 0}
}

type BidRequest_Device_Carrier int32

const (
	BidRequest_Device_CARRIER_UNKNOWN BidRequest_Device_Carrier = 0
	BidRequest_Device_CHINA_MOBILE    BidRequest_Device_Carrier = 1
	BidRequest_Device_CHINA_UNICOM    BidRequest_Device_Carrier = 2
	BidRequest_Device_CHINA_TELECOM   BidRequest_Device_Carrier = 3
	BidRequest_Device_CHINA_NETCOM    BidRequest_Device_Carrier = 4
)

// Enum value maps for BidRequest_Device_Carrier.
var (
	BidRequest_Device_Carrier_name = map[int32]string{
		0: "CARRIER_UNKNOWN",
		1: "CHINA_MOBILE",
		2: "CHINA_UNICOM",
		3: "CHINA_TELECOM",
		4: "CHINA_NETCOM",
	}
	BidRequest_Device_Carrier_value = map[string]int32{
		"CARRIER_UNKNOWN": 0,
		"CHINA_MOBILE":    1,
		"CHINA_UNICOM":    2,
		"CHINA_TELECOM":   3,
		"CHINA_NETCOM":    4,
	}
)

func (x BidRequest_Device_Carrier) Enum() *BidRequest_Device_Carrier {
	p := new(BidRequest_Device_Carrier)
	*p = x
	return p
}

func (x BidRequest_Device_Carrier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_Carrier) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[1].Descriptor()
}

func (BidRequest_Device_Carrier) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[1]
}

func (x BidRequest_Device_Carrier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_Carrier.Descriptor instead.
func (BidRequest_Device_Carrier) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 2, 0}
}

type BidRequest_Device_OS int32

const (
	BidRequest_Device_OS_UNKNOWN BidRequest_Device_OS = 0
	BidRequest_Device_IOS        BidRequest_Device_OS = 1
	BidRequest_Device_ANDROID    BidRequest_Device_OS = 2
)

// Enum value maps for BidRequest_Device_OS.
var (
	BidRequest_Device_OS_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "IOS",
		2: "ANDROID",
	}
	BidRequest_Device_OS_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"IOS":        1,
		"ANDROID":    2,
	}
)

func (x BidRequest_Device_OS) Enum() *BidRequest_Device_OS {
	p := new(BidRequest_Device_OS)
	*p = x
	return p
}

func (x BidRequest_Device_OS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_OS) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[2].Descriptor()
}

func (BidRequest_Device_OS) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[2]
}

func (x BidRequest_Device_OS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_OS.Descriptor instead.
func (BidRequest_Device_OS) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 2, 1}
}

type BidRequest_Device_ConnectionType int32

const (
	BidRequest_Device_CON_UNKNOWN BidRequest_Device_ConnectionType = 0
	BidRequest_Device_CELL        BidRequest_Device_ConnectionType = 1
	BidRequest_Device_WIFI        BidRequest_Device_ConnectionType = 2
	BidRequest_Device_CELL_2G     BidRequest_Device_ConnectionType = 3
	BidRequest_Device_CELL_3G     BidRequest_Device_ConnectionType = 4
	BidRequest_Device_CELL_4G     BidRequest_Device_ConnectionType = 5
	BidRequest_Device_CELL_5G     BidRequest_Device_ConnectionType = 6
)

// Enum value maps for BidRequest_Device_ConnectionType.
var (
	BidRequest_Device_ConnectionType_name = map[int32]string{
		0: "CON_UNKNOWN",
		1: "CELL",
		2: "WIFI",
		3: "CELL_2G",
		4: "CELL_3G",
		5: "CELL_4G",
		6: "CELL_5G",
	}
	BidRequest_Device_ConnectionType_value = map[string]int32{
		"CON_UNKNOWN": 0,
		"CELL":        1,
		"WIFI":        2,
		"CELL_2G":     3,
		"CELL_3G":     4,
		"CELL_4G":     5,
		"CELL_5G":     6,
	}
)

func (x BidRequest_Device_ConnectionType) Enum() *BidRequest_Device_ConnectionType {
	p := new(BidRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x BidRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[3]
}

func (x BidRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_ConnectionType.Descriptor instead.
func (BidRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 2, 2}
}

type BidRequest_Device_DeviceType int32

const (
	BidRequest_Device_DEVICE_UNKNOWN BidRequest_Device_DeviceType = 0
	BidRequest_Device_PHONE          BidRequest_Device_DeviceType = 1
	BidRequest_Device_TABLET         BidRequest_Device_DeviceType = 2
)

// Enum value maps for BidRequest_Device_DeviceType.
var (
	BidRequest_Device_DeviceType_name = map[int32]string{
		0: "DEVICE_UNKNOWN",
		1: "PHONE",
		2: "TABLET",
	}
	BidRequest_Device_DeviceType_value = map[string]int32{
		"DEVICE_UNKNOWN": 0,
		"PHONE":          1,
		"TABLET":         2,
	}
)

func (x BidRequest_Device_DeviceType) Enum() *BidRequest_Device_DeviceType {
	p := new(BidRequest_Device_DeviceType)
	*p = x
	return p
}

func (x BidRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[4].Descriptor()
}

func (BidRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[4]
}

func (x BidRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_DeviceType.Descriptor instead.
func (BidRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 2, 3}
}

type BidResponse_Seat_Ad_AdAction int32

const (
	BidResponse_Seat_Ad_AdActionUnknown BidResponse_Seat_Ad_AdAction = 0
	BidResponse_Seat_Ad_Webview         BidResponse_Seat_Ad_AdAction = 1 //webview
	BidResponse_Seat_Ad_Download        BidResponse_Seat_Ad_AdAction = 2 //下载
	BidResponse_Seat_Ad_Deeplink        BidResponse_Seat_Ad_AdAction = 3 //deeplink
)

// Enum value maps for BidResponse_Seat_Ad_AdAction.
var (
	BidResponse_Seat_Ad_AdAction_name = map[int32]string{
		0: "AdActionUnknown",
		1: "Webview",
		2: "Download",
		3: "Deeplink",
	}
	BidResponse_Seat_Ad_AdAction_value = map[string]int32{
		"AdActionUnknown": 0,
		"Webview":         1,
		"Download":        2,
		"Deeplink":        3,
	}
)

func (x BidResponse_Seat_Ad_AdAction) Enum() *BidResponse_Seat_Ad_AdAction {
	p := new(BidResponse_Seat_Ad_AdAction)
	*p = x
	return p
}

func (x BidResponse_Seat_Ad_AdAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Seat_Ad_AdAction) Descriptor() protoreflect.EnumDescriptor {
	return file_u_union_proto_enumTypes[5].Descriptor()
}

func (BidResponse_Seat_Ad_AdAction) Type() protoreflect.EnumType {
	return &file_u_union_proto_enumTypes[5]
}

func (x BidResponse_Seat_Ad_AdAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Seat_Ad_AdAction.Descriptor instead.
func (BidResponse_Seat_Ad_AdAction) EnumDescriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   int32                    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	RequestId string                   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Imp       []*BidRequest_Impression `protobuf:"bytes,3,rep,name=imp,proto3" json:"imp,omitempty"`
	App       *BidRequest_App          `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`
	Device    *BidRequest_Device       `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	User      *BidRequest_User         `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *BidRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Impression {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string              `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` //与bidRequest中的一致
	SearchId  string              `protobuf:"bytes,2,opt,name=search_id,json=searchId,proto3" json:"search_id,omitempty"`
	Status    int64               `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Seats     []*BidResponse_Seat `protobuf:"bytes,4,rep,name=seats,proto3" json:"seats,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BidResponse) GetSearchId() string {
	if x != nil {
		return x.SearchId
	}
	return ""
}

func (x *BidResponse) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BidResponse) GetSeats() []*BidResponse_Seat {
	if x != nil {
		return x.Seats
	}
	return nil
}

type BidRequest_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                           // 此impression在当前Request中的唯一id,从0开始
	Pos      *int32                         `protobuf:"varint,2,opt,name=pos,proto3,oneof" json:"pos,omitempty"`                                                                   // 0 - 未知, 1~5 - 一~五屏, 6 - 五屏以下
	SlotId   string                         `protobuf:"bytes,3,opt,name=slot_id,json=slotId,proto3" json:"slot_id,omitempty"`                                                      // 代码位id，u-union分配
	SlotType BidRequest_Impression_SlotType `protobuf:"varint,4,opt,name=slot_type,json=slotType,proto3,enum=uunion_up.BidRequest_Impression_SlotType" json:"slot_type,omitempty"` // 代码位类型
	BidFloor int32                          `protobuf:"varint,5,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"`
}

func (x *BidRequest_Impression) Reset() {
	*x = BidRequest_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Impression) ProtoMessage() {}

func (x *BidRequest_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Impression.ProtoReflect.Descriptor instead.
func (*BidRequest_Impression) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Impression) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BidRequest_Impression) GetPos() int32 {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return 0
}

func (x *BidRequest_Impression) GetSlotId() string {
	if x != nil {
		return x.SlotId
	}
	return ""
}

func (x *BidRequest_Impression) GetSlotType() BidRequest_Impression_SlotType {
	if x != nil {
		return x.SlotType
	}
	return BidRequest_Impression_SLOT_TYPE_UNKNOWN
}

func (x *BidRequest_Impression) GetBidFloor() int32 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appkey      string `protobuf:"bytes,1,opt,name=appkey,proto3" json:"appkey,omitempty"`                              // 应用id(appkey)，uunion提供
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  // 应用名
	Version     string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                            // 应用版本
	PackageName string `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` // 应用包名
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetAppkey() string {
	if x != nil {
		return x.Appkey
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidRequest_App) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua             string                           `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	Ip             string                           `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Ipv6           string                           `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	AliAaid        string                           `protobuf:"bytes,4,opt,name=ali_aaid,json=aliAaid,proto3" json:"ali_aaid,omitempty"` // 阿里匿名设备标识，ios必填，android选填
	Imei           string                           `protobuf:"bytes,5,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5        string                           `protobuf:"bytes,6,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	AndroidId      string                           `protobuf:"bytes,7,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	Mac            string                           `protobuf:"bytes,8,opt,name=mac,proto3" json:"mac,omitempty"`
	Idfa           string                           `protobuf:"bytes,9,opt,name=idfa,proto3" json:"idfa,omitempty"`
	Oaid           string                           `protobuf:"bytes,10,opt,name=oaid,proto3" json:"oaid,omitempty"`
	Caid           string                           `protobuf:"bytes,11,opt,name=caid,proto3" json:"caid,omitempty"`
	Carrier        BidRequest_Device_Carrier        `protobuf:"varint,12,opt,name=carrier,proto3,enum=uunion_up.BidRequest_Device_Carrier" json:"carrier,omitempty"`
	ConnectionType BidRequest_Device_ConnectionType `protobuf:"varint,13,opt,name=connection_type,json=connectionType,proto3,enum=uunion_up.BidRequest_Device_ConnectionType" json:"connection_type,omitempty"`
	Brand          string                           `protobuf:"bytes,14,opt,name=brand,proto3" json:"brand,omitempty"`
	Model          string                           `protobuf:"bytes,15,opt,name=model,proto3" json:"model,omitempty"`
	Os             BidRequest_Device_OS             `protobuf:"varint,16,opt,name=os,proto3,enum=uunion_up.BidRequest_Device_OS" json:"os,omitempty"`
	Osv            string                           `protobuf:"bytes,17,opt,name=osv,proto3" json:"osv,omitempty"`
	W              int32                            `protobuf:"varint,18,opt,name=w,proto3" json:"w,omitempty"`
	H              int32                            `protobuf:"varint,19,opt,name=h,proto3" json:"h,omitempty"`
	DeviceType     BidRequest_Device_DeviceType     `protobuf:"varint,20,opt,name=device_type,json=deviceType,proto3,enum=uunion_up.BidRequest_Device_DeviceType" json:"device_type,omitempty"`
	Lat            float64                          `protobuf:"fixed64,21,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon            float64                          `protobuf:"fixed64,22,opt,name=lon,proto3" json:"lon,omitempty"`
	BootMark       string                           `protobuf:"bytes,23,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	UpdateMark     string                           `protobuf:"bytes,24,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetAliAaid() string {
	if x != nil {
		return x.AliAaid
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() BidRequest_Device_Carrier {
	if x != nil {
		return x.Carrier
	}
	return BidRequest_Device_CARRIER_UNKNOWN
}

func (x *BidRequest_Device) GetConnectionType() BidRequest_Device_ConnectionType {
	if x != nil {
		return x.ConnectionType
	}
	return BidRequest_Device_CON_UNKNOWN
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() BidRequest_Device_OS {
	if x != nil {
		return x.Os
	}
	return BidRequest_Device_OS_UNKNOWN
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Device) GetDeviceType() BidRequest_Device_DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return BidRequest_Device_DEVICE_UNKNOWN
}

func (x *BidRequest_Device) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Device) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体侧用户关键词列表
	Keywords []string `protobuf:"bytes,1,rep,name=keywords,proto3" json:"keywords,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_User) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

type BidResponse_Seat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`  //对应请求中的impression的id，表示期望填充的对应impression
	Ads []*BidResponse_Seat_Ad `protobuf:"bytes,2,rep,name=ads,proto3" json:"ads,omitempty"` // 广告素材
}

func (x *BidResponse_Seat) Reset() {
	*x = BidResponse_Seat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat) ProtoMessage() {}

func (x *BidResponse_Seat) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_Seat) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BidResponse_Seat) GetAds() []*BidResponse_Seat_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

type BidResponse_Seat_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action         BidResponse_Seat_Ad_AdAction   `protobuf:"varint,1,opt,name=action,proto3,enum=uunion_up.BidResponse_Seat_Ad_AdAction" json:"action,omitempty"` // 跳转类型
	Image          []*BidResponse_Seat_Ad_Image   `protobuf:"bytes,2,rep,name=image,proto3" json:"image,omitempty"`                                                //选填，图片素材
	Video          *BidResponse_Seat_Ad_Video     `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`                                                //选填，视频素材
	LandingPageUrl string                         `protobuf:"bytes,4,opt,name=landing_page_url,json=landingPageUrl,proto3" json:"landing_page_url,omitempty"`      //选填，广告/创意的落地页url地址
	Title          string                         `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`                                                //选填，广告标题
	Desc           string                         `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`                                                  //选填，广告描述
	Icon           *BidResponse_Seat_Ad_Icon      `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`                                                  //选填，创意中的icon的url
	Deeplink       string                         `protobuf:"bytes,8,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                          //选填，deeplink跳转地址
	AppInfo        *BidResponse_Seat_Ad_AppInfo   `protobuf:"bytes,9,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`                             //选填，下载类广告必传
	TrackUrls      *BidResponse_Seat_Ad_TrackUrls `protobuf:"bytes,10,opt,name=track_urls,json=trackUrls,proto3" json:"track_urls,omitempty"`                      // 上报链接
	BidPrice       int64                          `protobuf:"varint,11,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"`                        //出价
}

func (x *BidResponse_Seat_Ad) Reset() {
	*x = BidResponse_Seat_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad) ProtoMessage() {}

func (x *BidResponse_Seat_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_Seat_Ad) GetAction() BidResponse_Seat_Ad_AdAction {
	if x != nil {
		return x.Action
	}
	return BidResponse_Seat_Ad_AdActionUnknown
}

func (x *BidResponse_Seat_Ad) GetImage() []*BidResponse_Seat_Ad_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Seat_Ad) GetVideo() *BidResponse_Seat_Ad_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Seat_Ad) GetLandingPageUrl() string {
	if x != nil {
		return x.LandingPageUrl
	}
	return ""
}

func (x *BidResponse_Seat_Ad) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Seat_Ad) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Seat_Ad) GetIcon() *BidResponse_Seat_Ad_Icon {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *BidResponse_Seat_Ad) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_Seat_Ad) GetAppInfo() *BidResponse_Seat_Ad_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Seat_Ad) GetTrackUrls() *BidResponse_Seat_Ad_TrackUrls {
	if x != nil {
		return x.TrackUrls
	}
	return nil
}

func (x *BidResponse_Seat_Ad) GetBidPrice() int64 {
	if x != nil {
		return x.BidPrice
	}
	return 0
}

type BidResponse_Seat_Ad_TrackUrls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposeUrls      []string `protobuf:"bytes,1,rep,name=expose_urls,json=exposeUrls,proto3" json:"expose_urls,omitempty"`             //选填，曝光上报
	ClickUrls       []string `protobuf:"bytes,2,rep,name=click_urls,json=clickUrls,proto3" json:"click_urls,omitempty"`                //选填，点击上报
	DownloadStart   []string `protobuf:"bytes,3,rep,name=download_start,json=downloadStart,proto3" json:"download_start,omitempty"`    //选填，开始下载上报
	DownloadFinish  []string `protobuf:"bytes,4,rep,name=download_finish,json=downloadFinish,proto3" json:"download_finish,omitempty"` //选填，完成下载上报
	InstallStart    []string `protobuf:"bytes,5,rep,name=install_start,json=installStart,proto3" json:"install_start,omitempty"`       //选填，开始安装上报
	InstallFinish   []string `protobuf:"bytes,6,rep,name=install_finish,json=installFinish,proto3" json:"install_finish,omitempty"`    //选填，安装完成上报
	DeeplinkTrack   []string `protobuf:"bytes,7,rep,name=deeplink_track,json=deeplinkTrack,proto3" json:"deeplink_track,omitempty"`
	DeeplinkSuccess []string `protobuf:"bytes,8,rep,name=deeplink_success,json=deeplinkSuccess,proto3" json:"deeplink_success,omitempty"` //选填，deeplink调起成功上报
	DeeplinkFail    []string `protobuf:"bytes,9,rep,name=deeplink_fail,json=deeplinkFail,proto3" json:"deeplink_fail,omitempty"`          //选填，deeplink调起失败上报
	VideoPlay0      []string `protobuf:"bytes,10,rep,name=video_play0,json=videoPlay0,proto3" json:"video_play0,omitempty"`               //选填，视频播放进度上报，播放开始
	VideoPlay1      []string `protobuf:"bytes,11,rep,name=video_play1,json=videoPlay1,proto3" json:"video_play1,omitempty"`               //选填，视频播放进度上报，播放25%
	VideoPlay2      []string `protobuf:"bytes,12,rep,name=video_play2,json=videoPlay2,proto3" json:"video_play2,omitempty"`               //选填，视频播放进度上报，播放50%
	VideoPlay3      []string `protobuf:"bytes,13,rep,name=video_play3,json=videoPlay3,proto3" json:"video_play3,omitempty"`               //选填，视频播放进度上报，播放75%
	VideoPlay4      []string `protobuf:"bytes,14,rep,name=video_play4,json=videoPlay4,proto3" json:"video_play4,omitempty"`               //选填，视频播放进度上报，播放完成
	WinUrls         []string `protobuf:"bytes,15,rep,name=win_urls,json=winUrls,proto3" json:"win_urls,omitempty"`
}

func (x *BidResponse_Seat_Ad_TrackUrls) Reset() {
	*x = BidResponse_Seat_Ad_TrackUrls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad_TrackUrls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad_TrackUrls) ProtoMessage() {}

func (x *BidResponse_Seat_Ad_TrackUrls) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad_TrackUrls.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad_TrackUrls) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetExposeUrls() []string {
	if x != nil {
		return x.ExposeUrls
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetDownloadStart() []string {
	if x != nil {
		return x.DownloadStart
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetDownloadFinish() []string {
	if x != nil {
		return x.DownloadFinish
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetInstallStart() []string {
	if x != nil {
		return x.InstallStart
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetInstallFinish() []string {
	if x != nil {
		return x.InstallFinish
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetDeeplinkTrack() []string {
	if x != nil {
		return x.DeeplinkTrack
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetDeeplinkSuccess() []string {
	if x != nil {
		return x.DeeplinkSuccess
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetDeeplinkFail() []string {
	if x != nil {
		return x.DeeplinkFail
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetVideoPlay0() []string {
	if x != nil {
		return x.VideoPlay0
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetVideoPlay1() []string {
	if x != nil {
		return x.VideoPlay1
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetVideoPlay2() []string {
	if x != nil {
		return x.VideoPlay2
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetVideoPlay3() []string {
	if x != nil {
		return x.VideoPlay3
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetVideoPlay4() []string {
	if x != nil {
		return x.VideoPlay4
	}
	return nil
}

func (x *BidResponse_Seat_Ad_TrackUrls) GetWinUrls() []string {
	if x != nil {
		return x.WinUrls
	}
	return nil
}

type BidResponse_Seat_Ad_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *BidResponse_Seat_Ad_Image) Reset() {
	*x = BidResponse_Seat_Ad_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad_Image) ProtoMessage() {}

func (x *BidResponse_Seat_Ad_Image) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad_Image) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *BidResponse_Seat_Ad_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_Seat_Ad_Image) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Image) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type BidResponse_Seat_Ad_Icon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *BidResponse_Seat_Ad_Icon) Reset() {
	*x = BidResponse_Seat_Ad_Icon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad_Icon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad_Icon) ProtoMessage() {}

func (x *BidResponse_Seat_Ad_Icon) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad_Icon.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad_Icon) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 2}
}

func (x *BidResponse_Seat_Ad_Icon) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_Seat_Ad_Icon) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Icon) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type BidResponse_Seat_Ad_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverUrl string `protobuf:"bytes,1,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填，视频封面图
	Duration uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`                //必填，视频时长（单位秒）
	H        uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`                              //必填，视频高，像素
	W        uint32 `protobuf:"varint,4,opt,name=w,proto3" json:"w,omitempty"`                              //必填，视频宽，像素
	Size     uint64 `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`                        //必填，视频大小，单位size
	Url      string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                           //必填，视频地址
}

func (x *BidResponse_Seat_Ad_Video) Reset() {
	*x = BidResponse_Seat_Ad_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad_Video) ProtoMessage() {}

func (x *BidResponse_Seat_Ad_Video) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad_Video) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 3}
}

func (x *BidResponse_Seat_Ad_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *BidResponse_Seat_Ad_Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Video) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Video) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Video) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidResponse_Seat_Ad_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type BidResponse_Seat_Ad_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName       string `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                   //必填，应用名称
	AppPermission string `protobuf:"bytes,2,opt,name=app_permission,json=appPermission,proto3" json:"app_permission,omitempty"` //必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
	AppPrivacy    string `protobuf:"bytes,3,opt,name=app_privacy,json=appPrivacy,proto3" json:"app_privacy,omitempty"`          //必填，应用隐私政策url
	AppSize       uint64 `protobuf:"varint,4,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`                  //必填，应用包的大小，单位byte
	AppVersion    string `protobuf:"bytes,5,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`          //必填，应用版本
	Developer     string `protobuf:"bytes,6,opt,name=developer,proto3" json:"developer,omitempty"`                              //必填，app开发者
	DownloadUrl   string `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`       //必填，应用下载url
	PackageName   string `protobuf:"bytes,8,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`       //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
}

func (x *BidResponse_Seat_Ad_AppInfo) Reset() {
	*x = BidResponse_Seat_Ad_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_u_union_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Seat_Ad_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Seat_Ad_AppInfo) ProtoMessage() {}

func (x *BidResponse_Seat_Ad_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_u_union_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Seat_Ad_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Seat_Ad_AppInfo) Descriptor() ([]byte, []int) {
	return file_u_union_proto_rawDescGZIP(), []int{1, 0, 0, 4}
}

func (x *BidResponse_Seat_Ad_AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetAppPermission() string {
	if x != nil {
		return x.AppPermission
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetAppPrivacy() string {
	if x != nil {
		return x.AppPrivacy
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetAppSize() uint64 {
	if x != nil {
		return x.AppSize
	}
	return 0
}

func (x *BidResponse_Seat_Ad_AppInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Seat_Ad_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

var File_u_union_proto protoreflect.FileDescriptor

var file_u_union_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x75, 0x2d, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x09, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x22, 0xb1, 0x0d, 0x0a, 0x0a, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03,
	0x61, 0x70, 0x70, 0x12, 0x34, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x92, 0x02, 0x0a, 0x0a, 0x49, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x17, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x09, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x75,
	0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6c,
	0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x73, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x22, 0x57, 0x0a,
	0x08, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x4c, 0x4f,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x54, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x4e, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x50,
	0x4c, 0x41, 0x53, 0x48, 0x10, 0x07, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x6f, 0x73, 0x1a, 0x6e,
	0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xf9,
	0x07, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76,
	0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x6c, 0x69, 0x5f, 0x61, 0x61, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x6c, 0x69, 0x41, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x61, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x52, 0x07, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2f, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x4f, 0x53, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x48, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x75, 0x75, 0x6e,
	0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x6c, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72,
	0x6b, 0x22, 0x67, 0x0a, 0x07, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x55, 0x4e, 0x49,
	0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x54,
	0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e,
	0x41, 0x5f, 0x4e, 0x45, 0x54, 0x43, 0x4f, 0x4d, 0x10, 0x04, 0x22, 0x2a, 0x0a, 0x02, 0x4f, 0x53,
	0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44,
	0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x22, 0x69, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x45, 0x4c,
	0x4c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x32, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45,
	0x4c, 0x4c, 0x5f, 0x33, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f,
	0x34, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x35, 0x47, 0x10,
	0x06, 0x22, 0x37, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x0e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x1a, 0x22, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xdd,
	0x0e, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x65, 0x61, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x52, 0x05,
	0x73, 0x65, 0x61, 0x74, 0x73, 0x1a, 0xc6, 0x0d, 0x0a, 0x04, 0x53, 0x65, 0x61, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30,
	0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x75, 0x75,
	0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73,
	0x1a, 0xfb, 0x0c, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x3f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x41, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74,
	0x2e, 0x41, 0x64, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x37, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e,
	0x41, 0x64, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x41, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x75, 0x75,
	0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x0a,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x75, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x1a, 0x9e, 0x04, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x25, 0x0a, 0x0e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x46,
	0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x30, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50,
	0x6c, 0x61, 0x79, 0x30, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c,
	0x61, 0x79, 0x31, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x50, 0x6c, 0x61, 0x79, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70,
	0x6c, 0x61, 0x79, 0x32, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x50, 0x6c, 0x61, 0x79, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x70, 0x6c, 0x61, 0x79, 0x33, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x33, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x34, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x34, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x77, 0x69, 0x6e, 0x55,
	0x72, 0x6c, 0x73, 0x1a, 0x35, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c,
	0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x1a, 0x34, 0x0a, 0x04, 0x49, 0x63,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68,
	0x1a, 0x82, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01,
	0x68, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x8c, 0x02, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x08, 0x41, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x13, 0x0a, 0x0f, 0x41, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x65, 0x62, 0x76, 0x69, 0x65, 0x77,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x10, 0x03, 0x42, 0x17,
	0x5a, 0x15, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x75, 0x75,
	0x6e, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_u_union_proto_rawDescOnce sync.Once
	file_u_union_proto_rawDescData = file_u_union_proto_rawDesc
)

func file_u_union_proto_rawDescGZIP() []byte {
	file_u_union_proto_rawDescOnce.Do(func() {
		file_u_union_proto_rawDescData = protoimpl.X.CompressGZIP(file_u_union_proto_rawDescData)
	})
	return file_u_union_proto_rawDescData
}

var file_u_union_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_u_union_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_u_union_proto_goTypes = []interface{}{
	(BidRequest_Impression_SlotType)(0),   // 0: uunion_up.BidRequest.Impression.SlotType
	(BidRequest_Device_Carrier)(0),        // 1: uunion_up.BidRequest.Device.Carrier
	(BidRequest_Device_OS)(0),             // 2: uunion_up.BidRequest.Device.OS
	(BidRequest_Device_ConnectionType)(0), // 3: uunion_up.BidRequest.Device.ConnectionType
	(BidRequest_Device_DeviceType)(0),     // 4: uunion_up.BidRequest.Device.DeviceType
	(BidResponse_Seat_Ad_AdAction)(0),     // 5: uunion_up.BidResponse.Seat.Ad.AdAction
	(*BidRequest)(nil),                    // 6: uunion_up.BidRequest
	(*BidResponse)(nil),                   // 7: uunion_up.BidResponse
	(*BidRequest_Impression)(nil),         // 8: uunion_up.BidRequest.Impression
	(*BidRequest_App)(nil),                // 9: uunion_up.BidRequest.App
	(*BidRequest_Device)(nil),             // 10: uunion_up.BidRequest.Device
	(*BidRequest_User)(nil),               // 11: uunion_up.BidRequest.User
	(*BidResponse_Seat)(nil),              // 12: uunion_up.BidResponse.Seat
	(*BidResponse_Seat_Ad)(nil),           // 13: uunion_up.BidResponse.Seat.Ad
	(*BidResponse_Seat_Ad_TrackUrls)(nil), // 14: uunion_up.BidResponse.Seat.Ad.TrackUrls
	(*BidResponse_Seat_Ad_Image)(nil),     // 15: uunion_up.BidResponse.Seat.Ad.Image
	(*BidResponse_Seat_Ad_Icon)(nil),      // 16: uunion_up.BidResponse.Seat.Ad.Icon
	(*BidResponse_Seat_Ad_Video)(nil),     // 17: uunion_up.BidResponse.Seat.Ad.Video
	(*BidResponse_Seat_Ad_AppInfo)(nil),   // 18: uunion_up.BidResponse.Seat.Ad.AppInfo
}
var file_u_union_proto_depIdxs = []int32{
	8,  // 0: uunion_up.BidRequest.imp:type_name -> uunion_up.BidRequest.Impression
	9,  // 1: uunion_up.BidRequest.app:type_name -> uunion_up.BidRequest.App
	10, // 2: uunion_up.BidRequest.device:type_name -> uunion_up.BidRequest.Device
	11, // 3: uunion_up.BidRequest.user:type_name -> uunion_up.BidRequest.User
	12, // 4: uunion_up.BidResponse.seats:type_name -> uunion_up.BidResponse.Seat
	0,  // 5: uunion_up.BidRequest.Impression.slot_type:type_name -> uunion_up.BidRequest.Impression.SlotType
	1,  // 6: uunion_up.BidRequest.Device.carrier:type_name -> uunion_up.BidRequest.Device.Carrier
	3,  // 7: uunion_up.BidRequest.Device.connection_type:type_name -> uunion_up.BidRequest.Device.ConnectionType
	2,  // 8: uunion_up.BidRequest.Device.os:type_name -> uunion_up.BidRequest.Device.OS
	4,  // 9: uunion_up.BidRequest.Device.device_type:type_name -> uunion_up.BidRequest.Device.DeviceType
	13, // 10: uunion_up.BidResponse.Seat.ads:type_name -> uunion_up.BidResponse.Seat.Ad
	5,  // 11: uunion_up.BidResponse.Seat.Ad.action:type_name -> uunion_up.BidResponse.Seat.Ad.AdAction
	15, // 12: uunion_up.BidResponse.Seat.Ad.image:type_name -> uunion_up.BidResponse.Seat.Ad.Image
	17, // 13: uunion_up.BidResponse.Seat.Ad.video:type_name -> uunion_up.BidResponse.Seat.Ad.Video
	16, // 14: uunion_up.BidResponse.Seat.Ad.icon:type_name -> uunion_up.BidResponse.Seat.Ad.Icon
	18, // 15: uunion_up.BidResponse.Seat.Ad.app_info:type_name -> uunion_up.BidResponse.Seat.Ad.AppInfo
	14, // 16: uunion_up.BidResponse.Seat.Ad.track_urls:type_name -> uunion_up.BidResponse.Seat.Ad.TrackUrls
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_u_union_proto_init() }
func file_u_union_proto_init() {
	if File_u_union_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_u_union_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad_TrackUrls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad_Icon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_u_union_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Seat_Ad_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_u_union_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_u_union_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_u_union_proto_goTypes,
		DependencyIndexes: file_u_union_proto_depIdxs,
		EnumInfos:         file_u_union_proto_enumTypes,
		MessageInfos:      file_u_union_proto_msgTypes,
	}.Build()
	File_u_union_proto = out.File
	file_u_union_proto_rawDesc = nil
	file_u_union_proto_goTypes = nil
	file_u_union_proto_depIdxs = nil
}
