// Copyright (c) 2019 www.ximalaya.com Inc.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.0
// source: xmly_v2_20230615.proto

package xmly_v2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiVersion *string            `protobuf:"bytes,1,opt,name=api_version,json=apiVersion" json:"api_version,omitempty"` // rtb协议版本（目前固定为"v2.0"）
	Id         *string            `protobuf:"bytes,2,opt,name=id" json:"id,omitempty"`                                   // 本次次请求的请求id，dsp需要在回复时在Response.id字段中填写该字段，带回给adx
	At         *int32             `protobuf:"varint,3,opt,name=at" json:"at,omitempty"`                                  // [保留字段]第一高价还是第二高价竞价
	Imp        []*BidRequest_Imp  `protobuf:"bytes,4,rep,name=imp" json:"imp,omitempty"`                                 // 曝光机会列表，一次请求可能会有多个曝光机会
	Site       *BidRequest_Site   `protobuf:"bytes,5,opt,name=site" json:"site,omitempty"`                               // [保留字段] 媒体站点信息(PC端)
	Device     *BidRequest_Device `protobuf:"bytes,6,opt,name=device" json:"device,omitempty"`                           // 设备信息
	User       *BidRequest_User   `protobuf:"bytes,7,opt,name=user" json:"user,omitempty"`                               // 用户信息
	App        *BidRequest_App    `protobuf:"bytes,8,opt,name=app" json:"app,omitempty"`                                 // APP信息
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetAt() int32 {
	if x != nil && x.At != nil {
		return *x.At
	}
	return 0
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      *string                `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`           // 请求ID，需与BidRequest.id一致，DSP参与竞价时必须具有该字段
	Bidid   *string                `protobuf:"bytes,2,opt,name=bidid" json:"bidid,omitempty"`     // dsp侧竞价ID，可不填
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,3,rep,name=seatbid" json:"seatbid,omitempty"` // DSP参与竞价的具体内容，与BidRequest.imp个数对应
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetBidid() string {
	if x != nil && x.Bidid != nil {
		return *x.Bidid
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           *string                `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                       // 区分于请求ID，标识唯一一次曝光机会，DSP回复时需将该ID填到BidResponse.SeatBid.Bid.impid字段上
	Tagid        *string                `protobuf:"bytes,2,opt,name=tagid" json:"tagid,omitempty"`                 // 广告位ID，详见资源映射表：https://github.com/wshg0809/xmadx/wiki/%E8%B5%84%E6%BA%90%E6%98%A0%E5%B0%84
	Bidfloor     *float32               `protobuf:"fixed32,4,opt,name=bidfloor" json:"bidfloor,omitempty"`         // RTB竞价底价(分/千次曝光，分后面的小数直接丢弃，未四舍五入)
	Banner       *BidRequest_Imp_Banner `protobuf:"bytes,5,opt,name=banner" json:"banner,omitempty"`               // [保留字段] Banner广告对象
	Video        *BidRequest_Imp_Video  `protobuf:"bytes,6,opt,name=video" json:"video,omitempty"`                 // [保留字段] Video广告对象（目前未使用)
	Dealid       []string               `protobuf:"bytes,8,rep,name=dealid" json:"dealid,omitempty"`               // 描述PDB/PD类型的Deal信息，目前只有一个
	Forbidtrade  *string                `protobuf:"bytes,9,opt,name=forbidtrade" json:"forbidtrade,omitempty"`     // [保留字段] 广告位行业限制信息
	Date         *string                `protobuf:"bytes,10,opt,name=date" json:"date,omitempty"`                  // 广告曝光日期，格式YYYY-MM-DD
	Brightscreen *bool                  `protobuf:"varint,11,opt,name=brightscreen" json:"brightscreen,omitempty"` // 屏幕是否点亮，屏幕关闭只出有声广告（仅有声广告需要关心）, true: 亮屏；false: 闭屏
	Templateid   *string                `protobuf:"bytes,12,opt,name=templateid" json:"templateid,omitempty"`      // 广告位模板ID，详情联系喜马运营
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil && x.Tagid != nil {
		return *x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetBidfloor() float32 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetBanner() *BidRequest_Imp_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetDealid() []string {
	if x != nil {
		return x.Dealid
	}
	return nil
}

func (x *BidRequest_Imp) GetForbidtrade() string {
	if x != nil && x.Forbidtrade != nil {
		return *x.Forbidtrade
	}
	return ""
}

func (x *BidRequest_Imp) GetDate() string {
	if x != nil && x.Date != nil {
		return *x.Date
	}
	return ""
}

func (x *BidRequest_Imp) GetBrightscreen() bool {
	if x != nil && x.Brightscreen != nil {
		return *x.Brightscreen
	}
	return false
}

func (x *BidRequest_Imp) GetTemplateid() string {
	if x != nil && x.Templateid != nil {
		return *x.Templateid
	}
	return ""
}

type BidRequest_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"` // 网站名称 （pc专用）
	Page *string `protobuf:"bytes,2,opt,name=page" json:"page,omitempty"` // 当前页面url （pc专用）
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Site) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Site) GetPage() string {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua             *string                   `protobuf:"bytes,1,opt,name=ua" json:"ua,omitempty"`                                    // UA信息
	Ip             *string                   `protobuf:"bytes,2,opt,name=ip" json:"ip,omitempty"`                                    // 用户设备ip
	Geo            *BidRequest_Device_Geo    `protobuf:"bytes,3,opt,name=geo" json:"geo,omitempty"`                                  // 地理位置
	Idfamd5        *string                   `protobuf:"bytes,4,opt,name=idfamd5" json:"idfamd5,omitempty"`                          // IDFA MD5加密转大写
	Imeimd5        *string                   `protobuf:"bytes,5,opt,name=imeimd5" json:"imeimd5,omitempty"`                          // IMEI MD5加密转大写
	Androididmd5   *string                   `protobuf:"bytes,6,opt,name=androididmd5" json:"androididmd5,omitempty"`                // AndroidID MD5加密转大写
	Openudidmd5    *string                   `protobuf:"bytes,7,opt,name=openudidmd5" json:"openudidmd5,omitempty"`                  // OpenUDID，MD5加密转大写
	Macmd5         *string                   `protobuf:"bytes,8,opt,name=macmd5" json:"macmd5,omitempty"`                            // MAC地址 MD5加密转大写，加密前先去除分隔符':'
	Devicetype     *int32                    `protobuf:"varint,9,opt,name=devicetype" json:"devicetype,omitempty"`                   // 设备类型：0，未知；1，Phone；2，PC；3，TV；4，PAD；5，outdoor（户外设备）目前设备类型只有1，4两种
	H              *int32                    `protobuf:"varint,10,opt,name=h" json:"h,omitempty"`                                    // 设备屏幕纵向分辨率
	W              *int32                    `protobuf:"varint,11,opt,name=w" json:"w,omitempty"`                                    // 设备屏幕横向分辨率
	Os             *string                   `protobuf:"bytes,12,opt,name=os" json:"os,omitempty"`                                   // 操作系统名称（iOS, Android等）
	Osv            *string                   `protobuf:"bytes,13,opt,name=osv" json:"osv,omitempty"`                                 // 操作系统版本
	Carrier        *int32                    `protobuf:"varint,14,opt,name=carrier" json:"carrier,omitempty"`                        // 运营商 0，移动；1，联通； 2，电信；3，其它
	Connectiontype *int32                    `protobuf:"varint,15,opt,name=connectiontype" json:"connectiontype,omitempty"`          // 网络连接类型 0，2G；1，3G；2，4G；3，wifi；4，其它
	Make           *string                   `protobuf:"bytes,16,opt,name=make" json:"make,omitempty"`                               // 手机品牌，如：iPhone，Xiaomi
	Oaidmd5        *string                   `protobuf:"bytes,17,opt,name=oaidmd5" json:"oaidmd5,omitempty"`                         // Android OAID MD5加密转大写
	Model          *string                   `protobuf:"bytes,18,opt,name=model" json:"model,omitempty"`                             // 手机型号，如：iPhoneX, KNT-AL10
	Idfa           *string                   `protobuf:"bytes,19,opt,name=idfa" json:"idfa,omitempty"`                               // IDFA原始值
	Oaid           *string                   `protobuf:"bytes,20,opt,name=oaid" json:"oaid,omitempty"`                               // Android OAID原始值
	Idfalimit      *int32                    `protobuf:"varint,21,opt,name=idfalimit" json:"idfalimit,omitempty"`                    // 目前该字段尚无意义. iOS设备用户限制IDFA情况：0:未确定，开发者尚未请求用户许可; 1:受限制，用户主动关闭了IDFA追踪，所有APP都无法获取到IDFA; 2: 被拒绝，弹窗后用户选择关闭IDFA授权，喜马自己生成了一个idfa；3.授权，可以继续获取;只有idfalimit=3时才会传递真正IDFA值IDFA
	Caid           *string                   `protobuf:"bytes,22,opt,name=caid" json:"caid,omitempty"`                               // 无效字段
	Aaid           *string                   `protobuf:"bytes,23,opt,name=aaid" json:"aaid,omitempty"`                               // 阿里AAID，当idfa无法获取时，填写
	BootMark       *string                   `protobuf:"bytes,24,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`       // 阿里反作弊字段
	UpdateMark     *string                   `protobuf:"bytes,25,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"` // 阿里反作弊字段
	HmsVersion     *string                   `protobuf:"bytes,26,opt,name=hms_version,json=hmsVersion" json:"hms_version,omitempty"` //华为机型HMS Core版本号
	AsVersion      *string                   `protobuf:"bytes,27,opt,name=as_version,json=asVersion" json:"as_version,omitempty"`    // 大陆厂商安卓设备AS版本号
	Paid           *string                   `protobuf:"bytes,28,opt,name=paid" json:"paid,omitempty"`                               // 拼多多ios替代idfa方案
	Ipv6           *string                   `protobuf:"bytes,29,opt,name=ipv6" json:"ipv6,omitempty"`                               // 用户ipv6地址
	Caids          []*BidRequest_Device_CAID `protobuf:"bytes,30,rep,name=caids" json:"caids,omitempty"`                             //caid及对应的version,有多个caid则传多个, 最多两个
	OsUpdateTime   *string                   `protobuf:"bytes,31,opt,name=osUpdateTime" json:"osUpdateTime,omitempty"`               //系统更新时间
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetIdfamd5() string {
	if x != nil && x.Idfamd5 != nil {
		return *x.Idfamd5
	}
	return ""
}

func (x *BidRequest_Device) GetImeimd5() string {
	if x != nil && x.Imeimd5 != nil {
		return *x.Imeimd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroididmd5() string {
	if x != nil && x.Androididmd5 != nil {
		return *x.Androididmd5
	}
	return ""
}

func (x *BidRequest_Device) GetOpenudidmd5() string {
	if x != nil && x.Openudidmd5 != nil {
		return *x.Openudidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetMacmd5() string {
	if x != nil && x.Macmd5 != nil {
		return *x.Macmd5
	}
	return ""
}

func (x *BidRequest_Device) GetDevicetype() int32 {
	if x != nil && x.Devicetype != nil {
		return *x.Devicetype
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetConnectiontype() int32 {
	if x != nil && x.Connectiontype != nil {
		return *x.Connectiontype
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetOaidmd5() string {
	if x != nil && x.Oaidmd5 != nil {
		return *x.Oaidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetIdfalimit() int32 {
	if x != nil && x.Idfalimit != nil {
		return *x.Idfalimit
	}
	return 0
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetAaid() string {
	if x != nil && x.Aaid != nil {
		return *x.Aaid
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetHmsVersion() string {
	if x != nil && x.HmsVersion != nil {
		return *x.HmsVersion
	}
	return ""
}

func (x *BidRequest_Device) GetAsVersion() string {
	if x != nil && x.AsVersion != nil {
		return *x.AsVersion
	}
	return ""
}

func (x *BidRequest_Device) GetPaid() string {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCaids() []*BidRequest_Device_CAID {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *BidRequest_Device) GetOsUpdateTime() string {
	if x != nil && x.OsUpdateTime != nil {
		return *x.OsUpdateTime
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`           // [保留字段] 用户唯一标识（喜马拉雅侧用户uid）
	Applist *string `protobuf:"bytes,2,opt,name=applist" json:"applist,omitempty"` // applist安装包
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_User) GetApplist() string {
	if x != nil && x.Applist != nil {
		return *x.Applist
	}
	return ""
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     *string  `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`         // 应用名称：喜马拉雅FM、喜马拉雅极速版等
	Pkgname  *string  `protobuf:"bytes,2,opt,name=pkgname" json:"pkgname,omitempty"`   // 应用程序包名：com.ximalaya.iting
	Category []string `protobuf:"bytes,3,rep,name=category" json:"category,omitempty"` // 应用所属分类：电台、有声书、娱乐
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetPkgname() string {
	if x != nil && x.Pkgname != nil {
		return *x.Pkgname
	}
	return ""
}

func (x *BidRequest_App) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

type BidRequest_Imp_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	W     *int32   `protobuf:"varint,1,opt,name=w" json:"w,omitempty"`        // 广告位宽度
	H     *int32   `protobuf:"varint,2,opt,name=h" json:"h,omitempty"`        // 广告位高度
	Mimes []string `protobuf:"bytes,3,rep,name=mimes" json:"mimes,omitempty"` // 支持的素材类型(如 "jpg","txt")
}

func (x *BidRequest_Imp_Banner) Reset() {
	*x = BidRequest_Imp_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Banner) ProtoMessage() {}

func (x *BidRequest_Imp_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Banner.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Banner) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Banner) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Banner) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

type BidRequest_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mimes       []string `protobuf:"bytes,1,rep,name=mimes" json:"mimes,omitempty"`              // 支持播放的mimes(如 "flv","mp4")
	Minduration *int32   `protobuf:"varint,2,opt,name=minduration" json:"minduration,omitempty"` // 最短时长，单位毫秒
	Maxduration *int32   `protobuf:"varint,3,opt,name=maxduration" json:"maxduration,omitempty"` // 最长时长，单位毫秒
	W           *int32   `protobuf:"varint,4,opt,name=w" json:"w,omitempty"`                     // 广告位宽度
	H           *int32   `protobuf:"varint,5,opt,name=h" json:"h,omitempty"`                     // 广告位高度
}

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Imp_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetMinduration() int32 {
	if x != nil && x.Minduration != nil {
		return *x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxduration() int32 {
	if x != nil && x.Maxduration != nil {
		return *x.Maxduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

type BidRequest_Device_CAID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version *string `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	Caid    *string `protobuf:"bytes,2,opt,name=caid" json:"caid,omitempty"`
}

func (x *BidRequest_Device_CAID) Reset() {
	*x = BidRequest_Device_CAID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_CAID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_CAID) ProtoMessage() {}

func (x *BidRequest_Device_CAID) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_CAID.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_CAID) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *BidRequest_Device_CAID) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest_Device_CAID) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

type BidRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"` // 维度：-90至+90
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"` // 经度：-180至+180
}

func (x *BidRequest_Device_Geo) Reset() {
	*x = BidRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Geo) ProtoMessage() {}

func (x *BidRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{0, 2, 1}
}

func (x *BidRequest_Device_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"` // DSP参与竞价的位置，目前仅返回一个bid
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               *string                    `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                              // dsp侧针对这次竞价的ID
	Impid            *string                    `protobuf:"bytes,2,opt,name=impid" json:"impid,omitempty"`                        // 曝光ID，对应BidRequest.imp.id，必填！
	Price            *float32                   `protobuf:"fixed32,3,opt,name=price" json:"price,omitempty"`                      // DSP出价，分/千次曝光，分后面的小数直接丢弃，不进行四舍五入等运算
	Curl             *string                    `protobuf:"bytes,4,opt,name=curl" json:"curl,omitempty"`                          // Banner或Video素材的URL
	Videoduration    *int32                     `protobuf:"varint,5,opt,name=videoduration" json:"videoduration,omitempty"`       // Video素材的时长
	Crid             *string                    `protobuf:"bytes,6,opt,name=crid" json:"crid,omitempty"`                          // DSP侧内部创意ID，建议填写
	Native           *BidResponse_Bid_Native    `protobuf:"bytes,7,opt,name=native" json:"native,omitempty"`                      // 【物料需要审核的DSP忽略该字段】Native素材对象
	Clickurl         *string                    `protobuf:"bytes,8,opt,name=clickurl" json:"clickurl,omitempty"`                  // 【物料需要审核的DSP忽略该字段】点击跳转地址，注意：iOS Universal Links不能放到这里，应放到AppInfo.dplink中
	Monitorurls      []string                   `protobuf:"bytes,9,rep,name=monitorurls" json:"monitorurls,omitempty"`            // 曝光监测地址
	Clickmonitorurls []string                   `protobuf:"bytes,10,rep,name=clickmonitorurls" json:"clickmonitorurls,omitempty"` // 点击监测地址
	Clicktype        *int32                     `protobuf:"varint,12,opt,name=clicktype" json:"clicktype,omitempty"`              // 点击类型：对应BidRequest中的clicktype（ 0：默认跳转落地页；1：不能跳转；2：app唤醒；3：app下载；4：拨打电话；5：跳转小程序）
	Appinfo          *BidResponse_Bid_AppInfo   `protobuf:"bytes,13,opt,name=appinfo" json:"appinfo,omitempty"`                   // 唤起APP或下载APP等高级功能涉及到的唤起或者下载的APP信息
	Phoneinfo        *BidResponse_Bid_PhoneInfo `protobuf:"bytes,14,opt,name=phoneinfo" json:"phoneinfo,omitempty"`               // clicktype=4时，必填，拨打电话相关信息
	Advertiserid     *string                    `protobuf:"bytes,15,opt,name=advertiserid" json:"advertiserid,omitempty"`         // 【仅物料需要审核的DSP需要】素材在喜马侧广告主ID
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_Bid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse_Bid) GetImpid() string {
	if x != nil && x.Impid != nil {
		return *x.Impid
	}
	return ""
}

func (x *BidResponse_Bid) GetPrice() float32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetCurl() string {
	if x != nil && x.Curl != nil {
		return *x.Curl
	}
	return ""
}

func (x *BidResponse_Bid) GetVideoduration() int32 {
	if x != nil && x.Videoduration != nil {
		return *x.Videoduration
	}
	return 0
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

func (x *BidResponse_Bid) GetNative() *BidResponse_Bid_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *BidResponse_Bid) GetClickurl() string {
	if x != nil && x.Clickurl != nil {
		return *x.Clickurl
	}
	return ""
}

func (x *BidResponse_Bid) GetMonitorurls() []string {
	if x != nil {
		return x.Monitorurls
	}
	return nil
}

func (x *BidResponse_Bid) GetClickmonitorurls() []string {
	if x != nil {
		return x.Clickmonitorurls
	}
	return nil
}

func (x *BidResponse_Bid) GetClicktype() int32 {
	if x != nil && x.Clicktype != nil {
		return *x.Clicktype
	}
	return 0
}

func (x *BidResponse_Bid) GetAppinfo() *BidResponse_Bid_AppInfo {
	if x != nil {
		return x.Appinfo
	}
	return nil
}

func (x *BidResponse_Bid) GetPhoneinfo() *BidResponse_Bid_PhoneInfo {
	if x != nil {
		return x.Phoneinfo
	}
	return nil
}

func (x *BidResponse_Bid) GetAdvertiserid() string {
	if x != nil && x.Advertiserid != nil {
		return *x.Advertiserid
	}
	return ""
}

type BidResponse_Bid_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attr []*BidResponse_Bid_Native_Attr `protobuf:"bytes,2,rep,name=attr" json:"attr,omitempty"` // [协议版本v2.0时使用] Native物料列表
}

func (x *BidResponse_Bid_Native) Reset() {
	*x = BidResponse_Bid_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Native) ProtoMessage() {}

func (x *BidResponse_Bid_Native) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Native.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Native) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *BidResponse_Bid_Native) GetAttr() []*BidResponse_Bid_Native_Attr {
	if x != nil {
		return x.Attr
	}
	return nil
}

type BidResponse_Bid_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dplink              *string                                  `protobuf:"bytes,1,opt,name=dplink" json:"dplink,omitempty"`                           // [APP唤醒][小程序]唤起APP Scheme/小程序路径Path，iOS Universal Links也填充到该字段中
	Appname             *string                                  `protobuf:"bytes,2,opt,name=appname" json:"appname,omitempty"`                         // [APP下载][APP唤醒]APP名称
	Pkgname             *string                                  `protobuf:"bytes,3,opt,name=pkgname" json:"pkgname,omitempty"`                         // [APP下载][APP唤醒]APP pkgname
	Evokemonitorurl     []string                                 `protobuf:"bytes,4,rep,name=evokemonitorurl" json:"evokemonitorurl,omitempty"`         // [APP唤醒]唤起APP成功检测地址
	Evokefailmonitorurl []string                                 `protobuf:"bytes,5,rep,name=evokefailmonitorurl" json:"evokefailmonitorurl,omitempty"` // [APP唤醒]唤起APP失败检测地址
	Appdesc             *string                                  `protobuf:"bytes,6,opt,name=appdesc" json:"appdesc,omitempty"`                         // [APP下载]APP描述
	Applogo             *string                                  `protobuf:"bytes,7,opt,name=applogo" json:"applogo,omitempty"`                         // [APP下载]APP logo
	Appversion          *string                                  `protobuf:"bytes,8,opt,name=appversion" json:"appversion,omitempty"`                   // [APP下载][下载合规五要素-必填]APP版本号
	Appsize             *string                                  `protobuf:"bytes,9,opt,name=appsize" json:"appsize,omitempty"`                         // [APP下载][下载合规五要素-必填]下载包大小
	Appdeveloper        *string                                  `protobuf:"bytes,10,opt,name=appdeveloper" json:"appdeveloper,omitempty"`              // [APP下载][下载合规五要素-必填]开发者信息
	Appprivacypolicy    *string                                  `protobuf:"bytes,11,opt,name=appprivacypolicy" json:"appprivacypolicy,omitempty"`      // [APP下载][下载合规五要素-必填]隐私协议链接
	Apppermissions      []*BidResponse_Bid_AppInfo_AppPermission `protobuf:"bytes,12,rep,name=apppermissions" json:"apppermissions,omitempty"`          // [已废弃]
	Wxminiprogramid     *string                                  `protobuf:"bytes,13,opt,name=wxminiprogramid" json:"wxminiprogramid,omitempty"`        // [小程序] 小程序ID
	Apppermissionurl    *string                                  `protobuf:"bytes,14,opt,name=apppermissionurl" json:"apppermissionurl,omitempty"`      // [APP下载][下载合规五要素-必填]权限列表
	Downloadurl         *string                                  `protobuf:"bytes,15,opt,name=downloadurl" json:"downloadurl,omitempty"`                // [APP下载]下载APP的地址
}

func (x *BidResponse_Bid_AppInfo) Reset() {
	*x = BidResponse_Bid_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_AppInfo) ProtoMessage() {}

func (x *BidResponse_Bid_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_AppInfo) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1, 1}
}

func (x *BidResponse_Bid_AppInfo) GetDplink() string {
	if x != nil && x.Dplink != nil {
		return *x.Dplink
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetAppname() string {
	if x != nil && x.Appname != nil {
		return *x.Appname
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetPkgname() string {
	if x != nil && x.Pkgname != nil {
		return *x.Pkgname
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetEvokemonitorurl() []string {
	if x != nil {
		return x.Evokemonitorurl
	}
	return nil
}

func (x *BidResponse_Bid_AppInfo) GetEvokefailmonitorurl() []string {
	if x != nil {
		return x.Evokefailmonitorurl
	}
	return nil
}

func (x *BidResponse_Bid_AppInfo) GetAppdesc() string {
	if x != nil && x.Appdesc != nil {
		return *x.Appdesc
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetApplogo() string {
	if x != nil && x.Applogo != nil {
		return *x.Applogo
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetAppversion() string {
	if x != nil && x.Appversion != nil {
		return *x.Appversion
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetAppsize() string {
	if x != nil && x.Appsize != nil {
		return *x.Appsize
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetAppdeveloper() string {
	if x != nil && x.Appdeveloper != nil {
		return *x.Appdeveloper
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetAppprivacypolicy() string {
	if x != nil && x.Appprivacypolicy != nil {
		return *x.Appprivacypolicy
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetApppermissions() []*BidResponse_Bid_AppInfo_AppPermission {
	if x != nil {
		return x.Apppermissions
	}
	return nil
}

func (x *BidResponse_Bid_AppInfo) GetWxminiprogramid() string {
	if x != nil && x.Wxminiprogramid != nil {
		return *x.Wxminiprogramid
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetApppermissionurl() string {
	if x != nil && x.Apppermissionurl != nil {
		return *x.Apppermissionurl
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo) GetDownloadurl() string {
	if x != nil && x.Downloadurl != nil {
		return *x.Downloadurl
	}
	return ""
}

type BidResponse_Bid_PhoneInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProviderName *string `protobuf:"bytes,1,opt,name=providerName" json:"providerName,omitempty"` // 广告主昵称，建议填写
	PhoneNumber  *string `protobuf:"bytes,2,opt,name=phoneNumber" json:"phoneNumber,omitempty"`   // 电话号码，必填
}

func (x *BidResponse_Bid_PhoneInfo) Reset() {
	*x = BidResponse_Bid_PhoneInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_PhoneInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_PhoneInfo) ProtoMessage() {}

func (x *BidResponse_Bid_PhoneInfo) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_PhoneInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_PhoneInfo) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1, 2}
}

func (x *BidResponse_Bid_PhoneInfo) GetProviderName() string {
	if x != nil && x.ProviderName != nil {
		return *x.ProviderName
	}
	return ""
}

func (x *BidResponse_Bid_PhoneInfo) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

// 属性（描述）信息
type BidResponse_Bid_Native_Attr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 属性名
	Name *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	// 属性值
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *BidResponse_Bid_Native_Attr) Reset() {
	*x = BidResponse_Bid_Native_Attr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Native_Attr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Native_Attr) ProtoMessage() {}

func (x *BidResponse_Bid_Native_Attr) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Native_Attr.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Native_Attr) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1, 0, 0}
}

func (x *BidResponse_Bid_Native_Attr) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidResponse_Bid_Native_Attr) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type BidResponse_Bid_AppInfo_AppPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Permissionname *string `protobuf:"bytes,1,opt,name=permissionname" json:"permissionname,omitempty"`
	Permissiondesc *string `protobuf:"bytes,2,opt,name=permissiondesc" json:"permissiondesc,omitempty"`
}

func (x *BidResponse_Bid_AppInfo_AppPermission) Reset() {
	*x = BidResponse_Bid_AppInfo_AppPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_xmly_v2_20230615_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_AppInfo_AppPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_AppInfo_AppPermission) ProtoMessage() {}

func (x *BidResponse_Bid_AppInfo_AppPermission) ProtoReflect() protoreflect.Message {
	mi := &file_xmly_v2_20230615_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_AppInfo_AppPermission.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_AppInfo_AppPermission) Descriptor() ([]byte, []int) {
	return file_xmly_v2_20230615_proto_rawDescGZIP(), []int{1, 1, 1, 0}
}

func (x *BidResponse_Bid_AppInfo_AppPermission) GetPermissionname() string {
	if x != nil && x.Permissionname != nil {
		return *x.Permissionname
	}
	return ""
}

func (x *BidResponse_Bid_AppInfo_AppPermission) GetPermissiondesc() string {
	if x != nil && x.Permissiondesc != nil {
		return *x.Permissiondesc
	}
	return ""
}

var File_xmly_v2_20230615_proto protoreflect.FileDescriptor

var file_xmly_v2_20230615_proto_rawDesc = []byte{
	0x0a, 0x16, 0x78, 0x6d, 0x6c, 0x79, 0x5f, 0x76, 0x32, 0x5f, 0x32, 0x30, 0x32, 0x33, 0x30, 0x36,
	0x31, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69,
	0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x22, 0x9d, 0x10, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x61,
	0x74, 0x12, 0x39, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x3c, 0x0a, 0x04,
	0x73, 0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x69, 0x74, 0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3c,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x03,
	0x61, 0x70, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x1a, 0xa1, 0x04, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x12, 0x46, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61,
	0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78,
	0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d,
	0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x74, 0x72, 0x61, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x62, 0x72, 0x69, 0x67, 0x68, 0x74, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x62, 0x72, 0x69, 0x67, 0x68, 0x74, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x69, 0x64,
	0x1a, 0x3a, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0x7d, 0x0a, 0x05,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d,
	0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0c, 0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a,
	0x01, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x1a, 0x2e, 0x0a, 0x04, 0x53,
	0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x1a, 0xc0, 0x07, 0x0a, 0x06,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x40, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61,
	0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x66, 0x61,
	0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x6d,
	0x64, 0x35, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x6d, 0x64, 0x35, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35,
	0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64, 0x6d,
	0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x63, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x6d, 0x64, 0x35, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61,
	0x6b, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x64, 0x66, 0x61, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x64, 0x66, 0x61, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6d,
	0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x68, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70,
	0x76, 0x36, 0x12, 0x45, 0x0a, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61,
	0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x41,
	0x49, 0x44, 0x52, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x73, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x34, 0x0a,
	0x04, 0x43, 0x41, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x61, 0x69, 0x64, 0x1a, 0x29, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x1a, 0x30,
	0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x73, 0x74,
	0x1a, 0x4f, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x6b, 0x67, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b,
	0x67, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x22, 0xf4, 0x0c, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78,
	0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x1a,
	0x45, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x03, 0x62, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69,
	0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x1a, 0xaf, 0x0b, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x6d, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x75, 0x72, 0x6c, 0x12, 0x24,
	0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78,
	0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x75, 0x72, 0x6c, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x73, 0x12,
	0x2a, 0x0a, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78,
	0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x72, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x69, 0x64, 0x1a, 0x84, 0x01, 0x0a, 0x06,
	0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c,
	0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72,
	0x1a, 0x30, 0x0a, 0x04, 0x41, 0x74, 0x74, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x1a, 0xb0, 0x05, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x6b, 0x67, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x76,
	0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x75, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x66, 0x61, 0x69,
	0x6c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x13, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x66, 0x61, 0x69, 0x6c, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70,
	0x70, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70,
	0x70, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x61, 0x70, 0x70, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x66, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41,
	0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x61, 0x70,
	0x70, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x77, 0x78, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x77, 0x78, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x61, 0x70, 0x70, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x75,
	0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x75, 0x72,
	0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x75, 0x72, 0x6c, 0x1a, 0x5f, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x64, 0x65, 0x73, 0x63, 0x1a, 0x51, 0x0a, 0x09, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x17, 0x42, 0x09, 0x52, 0x74, 0x62, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x5a, 0x0a, 0x2e, 0x2e, 0x2f, 0x78, 0x6d, 0x6c, 0x79, 0x5f, 0x76,
	0x32,
}

var (
	file_xmly_v2_20230615_proto_rawDescOnce sync.Once
	file_xmly_v2_20230615_proto_rawDescData = file_xmly_v2_20230615_proto_rawDesc
)

func file_xmly_v2_20230615_proto_rawDescGZIP() []byte {
	file_xmly_v2_20230615_proto_rawDescOnce.Do(func() {
		file_xmly_v2_20230615_proto_rawDescData = protoimpl.X.CompressGZIP(file_xmly_v2_20230615_proto_rawDescData)
	})
	return file_xmly_v2_20230615_proto_rawDescData
}

var file_xmly_v2_20230615_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_xmly_v2_20230615_proto_goTypes = []interface{}{
	(*BidRequest)(nil),                            // 0: com.ximalaya.ad.adx.api.BidRequest
	(*BidResponse)(nil),                           // 1: com.ximalaya.ad.adx.api.BidResponse
	(*BidRequest_Imp)(nil),                        // 2: com.ximalaya.ad.adx.api.BidRequest.Imp
	(*BidRequest_Site)(nil),                       // 3: com.ximalaya.ad.adx.api.BidRequest.Site
	(*BidRequest_Device)(nil),                     // 4: com.ximalaya.ad.adx.api.BidRequest.Device
	(*BidRequest_User)(nil),                       // 5: com.ximalaya.ad.adx.api.BidRequest.User
	(*BidRequest_App)(nil),                        // 6: com.ximalaya.ad.adx.api.BidRequest.App
	(*BidRequest_Imp_Banner)(nil),                 // 7: com.ximalaya.ad.adx.api.BidRequest.Imp.Banner
	(*BidRequest_Imp_Video)(nil),                  // 8: com.ximalaya.ad.adx.api.BidRequest.Imp.Video
	(*BidRequest_Device_CAID)(nil),                // 9: com.ximalaya.ad.adx.api.BidRequest.Device.CAID
	(*BidRequest_Device_Geo)(nil),                 // 10: com.ximalaya.ad.adx.api.BidRequest.Device.Geo
	(*BidResponse_SeatBid)(nil),                   // 11: com.ximalaya.ad.adx.api.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),                       // 12: com.ximalaya.ad.adx.api.BidResponse.Bid
	(*BidResponse_Bid_Native)(nil),                // 13: com.ximalaya.ad.adx.api.BidResponse.Bid.Native
	(*BidResponse_Bid_AppInfo)(nil),               // 14: com.ximalaya.ad.adx.api.BidResponse.Bid.AppInfo
	(*BidResponse_Bid_PhoneInfo)(nil),             // 15: com.ximalaya.ad.adx.api.BidResponse.Bid.PhoneInfo
	(*BidResponse_Bid_Native_Attr)(nil),           // 16: com.ximalaya.ad.adx.api.BidResponse.Bid.Native.Attr
	(*BidResponse_Bid_AppInfo_AppPermission)(nil), // 17: com.ximalaya.ad.adx.api.BidResponse.Bid.AppInfo.AppPermission
}
var file_xmly_v2_20230615_proto_depIdxs = []int32{
	2,  // 0: com.ximalaya.ad.adx.api.BidRequest.imp:type_name -> com.ximalaya.ad.adx.api.BidRequest.Imp
	3,  // 1: com.ximalaya.ad.adx.api.BidRequest.site:type_name -> com.ximalaya.ad.adx.api.BidRequest.Site
	4,  // 2: com.ximalaya.ad.adx.api.BidRequest.device:type_name -> com.ximalaya.ad.adx.api.BidRequest.Device
	5,  // 3: com.ximalaya.ad.adx.api.BidRequest.user:type_name -> com.ximalaya.ad.adx.api.BidRequest.User
	6,  // 4: com.ximalaya.ad.adx.api.BidRequest.app:type_name -> com.ximalaya.ad.adx.api.BidRequest.App
	11, // 5: com.ximalaya.ad.adx.api.BidResponse.seatbid:type_name -> com.ximalaya.ad.adx.api.BidResponse.SeatBid
	7,  // 6: com.ximalaya.ad.adx.api.BidRequest.Imp.banner:type_name -> com.ximalaya.ad.adx.api.BidRequest.Imp.Banner
	8,  // 7: com.ximalaya.ad.adx.api.BidRequest.Imp.video:type_name -> com.ximalaya.ad.adx.api.BidRequest.Imp.Video
	10, // 8: com.ximalaya.ad.adx.api.BidRequest.Device.geo:type_name -> com.ximalaya.ad.adx.api.BidRequest.Device.Geo
	9,  // 9: com.ximalaya.ad.adx.api.BidRequest.Device.caids:type_name -> com.ximalaya.ad.adx.api.BidRequest.Device.CAID
	12, // 10: com.ximalaya.ad.adx.api.BidResponse.SeatBid.bid:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid
	13, // 11: com.ximalaya.ad.adx.api.BidResponse.Bid.native:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid.Native
	14, // 12: com.ximalaya.ad.adx.api.BidResponse.Bid.appinfo:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid.AppInfo
	15, // 13: com.ximalaya.ad.adx.api.BidResponse.Bid.phoneinfo:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid.PhoneInfo
	16, // 14: com.ximalaya.ad.adx.api.BidResponse.Bid.Native.attr:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid.Native.Attr
	17, // 15: com.ximalaya.ad.adx.api.BidResponse.Bid.AppInfo.apppermissions:type_name -> com.ximalaya.ad.adx.api.BidResponse.Bid.AppInfo.AppPermission
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_xmly_v2_20230615_proto_init() }
func file_xmly_v2_20230615_proto_init() {
	if File_xmly_v2_20230615_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_xmly_v2_20230615_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_CAID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_PhoneInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Native_Attr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_xmly_v2_20230615_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_AppInfo_AppPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_xmly_v2_20230615_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_xmly_v2_20230615_proto_goTypes,
		DependencyIndexes: file_xmly_v2_20230615_proto_depIdxs,
		MessageInfos:      file_xmly_v2_20230615_proto_msgTypes,
	}.Build()
	File_xmly_v2_20230615_proto = out.File
	file_xmly_v2_20230615_proto_rawDesc = nil
	file_xmly_v2_20230615_proto_goTypes = nil
	file_xmly_v2_20230615_proto_depIdxs = nil
}
