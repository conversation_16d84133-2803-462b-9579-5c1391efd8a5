//*
//Copyright (c) 2021 Domob Inc. All rights reserved.
//@description 多盟实时竞价交易协议(对外)
//@版本: 2.8

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.4
// source: domob_2.8.proto

package domob

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 0-cpm,1-cpc,2-pmp,3-抄底分成
type BidMode int32

const (
	BidMode_CPM BidMode = 0
	BidMode_CPC BidMode = 1
)

// Enum value maps for BidMode.
var (
	BidMode_name = map[int32]string{
		0: "CPM",
		1: "CPC",
	}
	BidMode_value = map[string]int32{
		"CPM": 0,
		"CPC": 1,
	}
)

func (x BidMode) Enum() *BidMode {
	p := new(BidMode)
	*p = x
	return p
}

func (x BidMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidMode) Descriptor() protoreflect.EnumDescriptor {
	return file_domob_2_8_proto_enumTypes[0].Descriptor()
}

func (BidMode) Type() protoreflect.EnumType {
	return &file_domob_2_8_proto_enumTypes[0]
}

func (x BidMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidMode.Descriptor instead.
func (BidMode) EnumDescriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0}
}

// 0:未知、1:开屏、2:信息流、3:插屏、4:banner、5:贴片、6:icon、7:激励视频;
type InventoryType int32

const (
	InventoryType_UNKNOWN      InventoryType = 0
	InventoryType_SPLASH       InventoryType = 1
	InventoryType_NATIVE       InventoryType = 2
	InventoryType_INTERSTITIAL InventoryType = 3
	InventoryType_BANNER       InventoryType = 4
	InventoryType_PRE_VIDEO    InventoryType = 5
	InventoryType_ICON         InventoryType = 6
	InventoryType_INCENTIVE    InventoryType = 7
)

// Enum value maps for InventoryType.
var (
	InventoryType_name = map[int32]string{
		0: "UNKNOWN",
		1: "SPLASH",
		2: "NATIVE",
		3: "INTERSTITIAL",
		4: "BANNER",
		5: "PRE_VIDEO",
		6: "ICON",
		7: "INCENTIVE",
	}
	InventoryType_value = map[string]int32{
		"UNKNOWN":      0,
		"SPLASH":       1,
		"NATIVE":       2,
		"INTERSTITIAL": 3,
		"BANNER":       4,
		"PRE_VIDEO":    5,
		"ICON":         6,
		"INCENTIVE":    7,
	}
)

func (x InventoryType) Enum() *InventoryType {
	p := new(InventoryType)
	*p = x
	return p
}

func (x InventoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InventoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_domob_2_8_proto_enumTypes[1].Descriptor()
}

func (InventoryType) Type() protoreflect.EnumType {
	return &file_domob_2_8_proto_enumTypes[1]
}

func (x InventoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InventoryType.Descriptor instead.
func (InventoryType) EnumDescriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{1}
}

type RTBAdsResponseInfo_Seat_Ad_CreativeType int32

const (
	RTBAdsResponseInfo_Seat_Ad_None  RTBAdsResponseInfo_Seat_Ad_CreativeType = 0
	RTBAdsResponseInfo_Seat_Ad_Txt   RTBAdsResponseInfo_Seat_Ad_CreativeType = 1
	RTBAdsResponseInfo_Seat_Ad_Image RTBAdsResponseInfo_Seat_Ad_CreativeType = 2
	RTBAdsResponseInfo_Seat_Ad_Flash RTBAdsResponseInfo_Seat_Ad_CreativeType = 3
	RTBAdsResponseInfo_Seat_Ad_Video RTBAdsResponseInfo_Seat_Ad_CreativeType = 4
)

// Enum value maps for RTBAdsResponseInfo_Seat_Ad_CreativeType.
var (
	RTBAdsResponseInfo_Seat_Ad_CreativeType_name = map[int32]string{
		0: "None",
		1: "Txt",
		2: "Image",
		3: "Flash",
		4: "Video",
	}
	RTBAdsResponseInfo_Seat_Ad_CreativeType_value = map[string]int32{
		"None":  0,
		"Txt":   1,
		"Image": 2,
		"Flash": 3,
		"Video": 4,
	}
)

func (x RTBAdsResponseInfo_Seat_Ad_CreativeType) Enum() *RTBAdsResponseInfo_Seat_Ad_CreativeType {
	p := new(RTBAdsResponseInfo_Seat_Ad_CreativeType)
	*p = x
	return p
}

func (x RTBAdsResponseInfo_Seat_Ad_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RTBAdsResponseInfo_Seat_Ad_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_domob_2_8_proto_enumTypes[2].Descriptor()
}

func (RTBAdsResponseInfo_Seat_Ad_CreativeType) Type() protoreflect.EnumType {
	return &file_domob_2_8_proto_enumTypes[2]
}

func (x RTBAdsResponseInfo_Seat_Ad_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RTBAdsResponseInfo_Seat_Ad_CreativeType.Descriptor instead.
func (RTBAdsResponseInfo_Seat_Ad_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2, 0, 0, 0}
}

type RTBAdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId            int64                       `protobuf:"varint,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`                                  // 媒体请求ID
	IsPing           bool                        `protobuf:"varint,3,opt,name=is_ping,json=isPing,proto3" json:"is_ping,omitempty"`                               // ping 请求
	ReqTms           int64                       `protobuf:"varint,4,opt,name=req_tms,json=reqTms,proto3" json:"req_tms,omitempty"`                               // 请求时间 单位毫秒
	SearchLimitTms   int32                       `protobuf:"varint,7,opt,name=search_limit_tms,json=searchLimitTms,proto3" json:"search_limit_tms,omitempty"`     // 处理限制时间 ms 不得小于100ms
	Imp              []*RTBAdsRequest_Impression `protobuf:"bytes,8,rep,name=imp,proto3" json:"imp,omitempty"`                                                    // 资源位信息，一般情况下只允许填一个，开 屏广告如果需要同时请求多天预加载广告 的情况下，可以填写多个 imp
	Device           *RTBAdsRequest_Device       `protobuf:"bytes,10,opt,name=device,proto3" json:"device,omitempty"`                                             // 设备相关信息
	App              *RTBAdsRequest_App          `protobuf:"bytes,11,opt,name=app,proto3" json:"app,omitempty"`                                                   // App 相关信息
	DetectedLanguage string                      `protobuf:"bytes,13,opt,name=detected_language,json=detectedLanguage,proto3" json:"detected_language,omitempty"` // 页面或者用户的语言
	HttpsRequired    bool                        `protobuf:"varint,14,opt,name=https_required,json=httpsRequired,proto3" json:"https_required,omitempty"`         // 是否必须返回https广告
	IsDeeplink       bool                        `protobuf:"varint,15,opt,name=is_deeplink,json=isDeeplink,proto3" json:"is_deeplink,omitempty"`                  // 是否支持deeplink
}

func (x *RTBAdsRequest) Reset() {
	*x = RTBAdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest) ProtoMessage() {}

func (x *RTBAdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0}
}

func (x *RTBAdsRequest) GetReqId() int64 {
	if x != nil {
		return x.ReqId
	}
	return 0
}

func (x *RTBAdsRequest) GetIsPing() bool {
	if x != nil {
		return x.IsPing
	}
	return false
}

func (x *RTBAdsRequest) GetReqTms() int64 {
	if x != nil {
		return x.ReqTms
	}
	return 0
}

func (x *RTBAdsRequest) GetSearchLimitTms() int32 {
	if x != nil {
		return x.SearchLimitTms
	}
	return 0
}

func (x *RTBAdsRequest) GetImp() []*RTBAdsRequest_Impression {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *RTBAdsRequest) GetDevice() *RTBAdsRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RTBAdsRequest) GetApp() *RTBAdsRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *RTBAdsRequest) GetDetectedLanguage() string {
	if x != nil {
		return x.DetectedLanguage
	}
	return ""
}

func (x *RTBAdsRequest) GetHttpsRequired() bool {
	if x != nil {
		return x.HttpsRequired
	}
	return false
}

func (x *RTBAdsRequest) GetIsDeeplink() bool {
	if x != nil {
		return x.IsDeeplink
	}
	return false
}

// 响应
type RTBAdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     int32                 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 0为出价  1为不出价
	Message    string                `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReqId      int64                 `protobuf:"varint,3,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	ProcessTms int64                 `protobuf:"varint,4,opt,name=process_tms,json=processTms,proto3" json:"process_tms,omitempty"` // 内部处理的时间 单位毫秒
	Info       []*RTBAdsResponseInfo `protobuf:"bytes,5,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *RTBAdsResponse) Reset() {
	*x = RTBAdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponse) ProtoMessage() {}

func (x *RTBAdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponse.ProtoReflect.Descriptor instead.
func (*RTBAdsResponse) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{1}
}

func (x *RTBAdsResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RTBAdsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RTBAdsResponse) GetReqId() int64 {
	if x != nil {
		return x.ReqId
	}
	return 0
}

func (x *RTBAdsResponse) GetProcessTms() int64 {
	if x != nil {
		return x.ProcessTms
	}
	return 0
}

func (x *RTBAdsResponse) GetInfo() []*RTBAdsResponseInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type RTBAdsResponseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seat []*RTBAdsResponseInfo_Seat `protobuf:"bytes,5,rep,name=seat,proto3" json:"seat,omitempty"`
}

func (x *RTBAdsResponseInfo) Reset() {
	*x = RTBAdsResponseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponseInfo) ProtoMessage() {}

func (x *RTBAdsResponseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponseInfo.ProtoReflect.Descriptor instead.
func (*RTBAdsResponseInfo) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2}
}

func (x *RTBAdsResponseInfo) GetSeat() []*RTBAdsResponseInfo_Seat {
	if x != nil {
		return x.Seat
	}
	return nil
}

// 可展示的位置
type RTBAdsRequest_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32                            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                          // 此impression在当前Request中的唯一id,从0开始
	TemplateId []int64                          `protobuf:"varint,2,rep,packed,name=template_id,json=templateId,proto3" json:"template_id,omitempty"` //  广告位ID
	Deal       []*RTBAdsRequest_Impression_Deal `protobuf:"bytes,9,rep,name=deal,proto3" json:"deal,omitempty"`                                       // 媒体约定的 PMP 交易信息
	BidFloor   int32                            `protobuf:"varint,10,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"`             // RTB的底价，非RTB方式可不填, 单位：分
	AdslotId   string                           `protobuf:"bytes,11,opt,name=adslot_id,json=adslotId,proto3" json:"adslot_id,omitempty"`              // 广告资源位id
	// 支持的出价类型;仅RTB有效
	BidMode []BidMode `protobuf:"varint,12,rep,packed,name=bid_mode,json=bidMode,proto3,enum=domob.BidMode" json:"bid_mode,omitempty"`
	// cpc类型广告底价，单位：分; 仅RTB有效
	CpcBidFloor int64 `protobuf:"varint,13,opt,name=cpc_bid_floor,json=cpcBidFloor,proto3" json:"cpc_bid_floor,omitempty"`
	// 支持的结算类型;仅RTB有效
	CostMode []BidMode `protobuf:"varint,14,rep,packed,name=cost_mode,json=costMode,proto3,enum=domob.BidMode" json:"cost_mode,omitempty"`
	// 流量类型
	InventoryType []InventoryType `protobuf:"varint,15,rep,packed,name=inventory_type,json=inventoryType,proto3,enum=domob.InventoryType" json:"inventory_type,omitempty"`
}

func (x *RTBAdsRequest_Impression) Reset() {
	*x = RTBAdsRequest_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_Impression) ProtoMessage() {}

func (x *RTBAdsRequest_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_Impression.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_Impression) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RTBAdsRequest_Impression) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RTBAdsRequest_Impression) GetTemplateId() []int64 {
	if x != nil {
		return x.TemplateId
	}
	return nil
}

func (x *RTBAdsRequest_Impression) GetDeal() []*RTBAdsRequest_Impression_Deal {
	if x != nil {
		return x.Deal
	}
	return nil
}

func (x *RTBAdsRequest_Impression) GetBidFloor() int32 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

func (x *RTBAdsRequest_Impression) GetAdslotId() string {
	if x != nil {
		return x.AdslotId
	}
	return ""
}

func (x *RTBAdsRequest_Impression) GetBidMode() []BidMode {
	if x != nil {
		return x.BidMode
	}
	return nil
}

func (x *RTBAdsRequest_Impression) GetCpcBidFloor() int64 {
	if x != nil {
		return x.CpcBidFloor
	}
	return 0
}

func (x *RTBAdsRequest_Impression) GetCostMode() []BidMode {
	if x != nil {
		return x.CostMode
	}
	return nil
}

func (x *RTBAdsRequest_Impression) GetInventoryType() []InventoryType {
	if x != nil {
		return x.InventoryType
	}
	return nil
}

type RTBAdsRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ipv4 点分十进制, 必须为终端真实IP地址
	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	// user agent，来自http头, 必须是以Mozilla开头的标准格式ua
	UserAgent string `protobuf:"bytes,2,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// IOS6.0及以上的idfa号
	Idfa    string `protobuf:"bytes,3,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5 string `protobuf:"bytes,4,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`
	// 安卓设备的imei号
	Imei string `protobuf:"bytes,5,opt,name=imei,proto3" json:"imei,omitempty"`
	// 安卓设备的imei号的md5值,若填写imei原值，则不用填此字段
	ImeiMd5 string `protobuf:"bytes,6,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	// oaid
	Oaid    string `protobuf:"bytes,7,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5 string `protobuf:"bytes,8,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`
	// android_id
	AndroidId string `protobuf:"bytes,9,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	// 设备类型，0-手机;1-平板;2-PC;3-互联网电视
	DeviceType int32 `protobuf:"varint,10,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// 设备品牌
	// 例如：nokia, samsung
	Brand string `protobuf:"bytes,11,opt,name=brand,proto3" json:"brand,omitempty"`
	// 设备型号
	// 例如：n70, galaxy
	Model string `protobuf:"bytes,12,opt,name=model,proto3" json:"model,omitempty"`
	// 操作系统
	// 例如：1:Android,2:iOS
	Os int32 `protobuf:"varint,13,opt,name=os,proto3" json:"os,omitempty"`
	// 操作系统版本
	// 例如：7.0.2
	Osv string `protobuf:"bytes,14,opt,name=osv,proto3" json:"osv,omitempty"`
	// 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g, 5-5g
	Network int32 `protobuf:"varint,15,opt,name=network,proto3" json:"network,omitempty"` // default 1
	// 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
	Operator int32 `protobuf:"varint,16,opt,name=operator,proto3" json:"operator,omitempty"`
	// 设备屏幕尺寸：宽
	Width int32 `protobuf:"varint,17,opt,name=width,proto3" json:"width,omitempty"`
	// 设备屏幕尺寸：高
	Height int32 `protobuf:"varint,18,opt,name=height,proto3" json:"height,omitempty"`
	// 屏幕方向 0-未知, 1-竖屏, 2-横屏
	Orientation int32                     `protobuf:"varint,20,opt,name=orientation,proto3" json:"orientation,omitempty"`
	Geo         *RTBAdsRequest_Device_Geo `protobuf:"bytes,21,opt,name=geo,proto3" json:"geo,omitempty"`
	// 用户已安装 app 列表
	InstalledApp []string                     `protobuf:"bytes,22,rep,name=installed_app,json=installedApp,proto3" json:"installed_app,omitempty"`
	Caids        []*RTBAdsRequest_Device_CAID `protobuf:"bytes,23,rep,name=caids,proto3" json:"caids,omitempty"`
	// 安卓系统启动标识
	BootMark string `protobuf:"bytes,24,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	// 安卓系统启动更新标识
	UpdateMark string `protobuf:"bytes,25,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
}

func (x *RTBAdsRequest_Device) Reset() {
	*x = RTBAdsRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_Device) ProtoMessage() {}

func (x *RTBAdsRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_Device.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_Device) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 1}
}

func (x *RTBAdsRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetNetwork() int32 {
	if x != nil {
		return x.Network
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetOperator() int32 {
	if x != nil {
		return x.Operator
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *RTBAdsRequest_Device) GetGeo() *RTBAdsRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *RTBAdsRequest_Device) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *RTBAdsRequest_Device) GetCaids() []*RTBAdsRequest_Device_CAID {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *RTBAdsRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *RTBAdsRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

// APP属性
type RTBAdsRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用包名, 例如：com.moji.MojiWeather
	PackageName string `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// 应用名，例如：陌陌
	AppName string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// app类目, 参见多盟APP类目表
	Category []string `protobuf:"bytes,3,rep,name=category,proto3" json:"category,omitempty"`
	// 应用版本号 eg 3.2.9
	AppVersion string `protobuf:"bytes,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// 应用商店版本号 eg (oppo、vivo、华为等)
	AppStoreVersion string `protobuf:"bytes,5,opt,name=app_store_version,json=appStoreVersion,proto3" json:"app_store_version,omitempty"`
}

func (x *RTBAdsRequest_App) Reset() {
	*x = RTBAdsRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_App) ProtoMessage() {}

func (x *RTBAdsRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_App.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_App) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 2}
}

func (x *RTBAdsRequest_App) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *RTBAdsRequest_App) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *RTBAdsRequest_App) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *RTBAdsRequest_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *RTBAdsRequest_App) GetAppStoreVersion() string {
	if x != nil {
		return x.AppStoreVersion
	}
	return ""
}

// 和媒体约定 PMP 交易时使用，包括优先交易、直采交易等等。 必选填写 dealid 和价格。
type RTBAdsRequest_Impression_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体分配的dealid
	DealId string `protobuf:"bytes,1,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`
	//此Deal对应的价格, 单位(分)
	MinPrice int32 `protobuf:"varint,2,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
}

func (x *RTBAdsRequest_Impression_Deal) Reset() {
	*x = RTBAdsRequest_Impression_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_Impression_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_Impression_Deal) ProtoMessage() {}

func (x *RTBAdsRequest_Impression_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_Impression_Deal.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_Impression_Deal) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *RTBAdsRequest_Impression_Deal) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

func (x *RTBAdsRequest_Impression_Deal) GetMinPrice() int32 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

type RTBAdsRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 纬度, 取值范围[-90.0 , +90.0]
	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	// 经度, 取值范围[-180.0 , +180.0]
	Lon float64 `protobuf:"fixed64,2,opt,name=lon,proto3" json:"lon,omitempty"`
}

func (x *RTBAdsRequest_Device_Geo) Reset() {
	*x = RTBAdsRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_Device_Geo) ProtoMessage() {}

func (x *RTBAdsRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *RTBAdsRequest_Device_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *RTBAdsRequest_Device_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

// 广协CAID
type RTBAdsRequest_Device_CAID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必须和版本同时使用才有意义，版本形如"20201201"
	Ver  string `protobuf:"bytes,1,opt,name=ver,proto3" json:"ver,omitempty"`
	Caid string `protobuf:"bytes,2,opt,name=caid,proto3" json:"caid,omitempty"`
}

func (x *RTBAdsRequest_Device_CAID) Reset() {
	*x = RTBAdsRequest_Device_CAID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsRequest_Device_CAID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsRequest_Device_CAID) ProtoMessage() {}

func (x *RTBAdsRequest_Device_CAID) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsRequest_Device_CAID.ProtoReflect.Descriptor instead.
func (*RTBAdsRequest_Device_CAID) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{0, 1, 1}
}

func (x *RTBAdsRequest_Device_CAID) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *RTBAdsRequest_Device_CAID) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

// 一组广告，对应 Request 中的一个 Impression
type RTBAdsResponseInfo_Seat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对应 Request 中 Impression 的 id，表示需求方期 望填充对应的 Impression
	Id int32                         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Ad []*RTBAdsResponseInfo_Seat_Ad `protobuf:"bytes,2,rep,name=ad,proto3" json:"ad,omitempty"`
}

func (x *RTBAdsResponseInfo_Seat) Reset() {
	*x = RTBAdsResponseInfo_Seat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponseInfo_Seat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponseInfo_Seat) ProtoMessage() {}

func (x *RTBAdsResponseInfo_Seat) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponseInfo_Seat.ProtoReflect.Descriptor instead.
func (*RTBAdsResponseInfo_Seat) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RTBAdsResponseInfo_Seat) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat) GetAd() []*RTBAdsResponseInfo_Seat_Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

// 广告字段 包含一个素材及对应的监测 URL
type RTBAdsResponseInfo_Seat_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前ad在seat中的序号，从0开始 0
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创意类型，表示素材是一个图片或者视频片段
	CreativeType RTBAdsResponseInfo_Seat_Ad_CreativeType `protobuf:"varint,2,opt,name=creative_type,json=creativeType,proto3,enum=domob.RTBAdsResponseInfo_Seat_Ad_CreativeType" json:"creative_type,omitempty"`
	// 原生广告使用
	Material *RTBAdsResponseInfo_Seat_Ad_Material `protobuf:"bytes,3,opt,name=material,proto3" json:"material,omitempty"`
	// 表示此次报价使用的媒体 deallid
	DealId string `protobuf:"bytes,4,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`
	// 表示原生素材希望投放的日期，尤其在开屏这种 预加载时间较长的流量中使用, 仅开屏使用，如:"20210806"
	CampaignDate string `protobuf:"bytes,5,opt,name=campaign_date,json=campaignDate,proto3" json:"campaign_date,omitempty"`
	// 广告创意标识
	SponsorId int64 `protobuf:"varint,6,opt,name=sponsor_id,json=sponsorId,proto3" json:"sponsor_id,omitempty"`
	// 待废弃字段
	CreativeId int64 `protobuf:"varint,9,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`
	//广告来源
	AdSource string `protobuf:"bytes,10,opt,name=ad_source,json=adSource,proto3" json:"ad_source,omitempty"`
	// adx场景, 返回报价供上游adx竞价, 单位(分)
	BidPrice uint64 `protobuf:"varint,11,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"`
	// 下载类必填，下载app名
	AppName string `protobuf:"bytes,12,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 下载推广类型必填，安卓:应 用包名，iOS设备使用bundleid
	PackageName string `protobuf:"bytes,13,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// iOS应用下载app_id
	MarketId string `protobuf:"bytes,14,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// 最终目标landing(着陆页)页，非点击地址
	LandingUrl string `protobuf:"bytes,15,opt,name=landing_url,json=landingUrl,proto3" json:"landing_url,omitempty"`
	// 点击地址，在触发点击时，通过此地址到达落地 页
	ClickThroughUrl string `protobuf:"bytes,16,opt,name=click_through_url,json=clickThroughUrl,proto3" json:"click_through_url,omitempty"`
	//APP唤醒地址
	DeeplinkUrl string `protobuf:"bytes,17,opt,name=deeplink_url,json=deeplinkUrl,proto3" json:"deeplink_url,omitempty"`
	//APP下载地址
	DownloadUrl string `protobuf:"bytes,18,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// IOS 通用唤起链接
	UniversalLink string `protobuf:"bytes,19,opt,name=universal_link,json=universalLink,proto3" json:"universal_link,omitempty"`
	// 曝光监测地址
	ImpressionTrackingUrl []string `protobuf:"bytes,20,rep,name=impression_tracking_url,json=impressionTrackingUrl,proto3" json:"impression_tracking_url,omitempty"`
	// 点击监测地址
	ClickTrackingUrl []string `protobuf:"bytes,21,rep,name=click_tracking_url,json=clickTrackingUrl,proto3" json:"click_tracking_url,omitempty"`
	// 竞价成功通知，媒体 ADX 竞价成功时通过此 URL 通知
	WinNoticeUrl []string                                 `protobuf:"bytes,22,rep,name=win_notice_url,json=winNoticeUrl,proto3" json:"win_notice_url,omitempty"`
	EventTrack   []*RTBAdsResponseInfo_Seat_Ad_EventTrack `protobuf:"bytes,23,rep,name=event_track,json=eventTrack,proto3" json:"event_track,omitempty"`
	// deeplink调起事件监测地址
	DpTrackingUrl []string `protobuf:"bytes,29,rep,name=dp_tracking_url,json=dpTrackingUrl,proto3" json:"dp_tracking_url,omitempty"`
	// 出价类型
	BidMode BidMode `protobuf:"varint,31,opt,name=bid_mode,json=bidMode,proto3,enum=domob.BidMode" json:"bid_mode,omitempty"`
	// 结算类型
	CostMode BidMode `protobuf:"varint,32,opt,name=cost_mode,json=costMode,proto3,enum=domob.BidMode" json:"cost_mode,omitempty"`
	// 素材id
	MaterialId string `protobuf:"bytes,35,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
}

func (x *RTBAdsResponseInfo_Seat_Ad) Reset() {
	*x = RTBAdsResponseInfo_Seat_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponseInfo_Seat_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponseInfo_Seat_Ad) ProtoMessage() {}

func (x *RTBAdsResponseInfo_Seat_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponseInfo_Seat_Ad.ProtoReflect.Descriptor instead.
func (*RTBAdsResponseInfo_Seat_Ad) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetCreativeType() RTBAdsResponseInfo_Seat_Ad_CreativeType {
	if x != nil {
		return x.CreativeType
	}
	return RTBAdsResponseInfo_Seat_Ad_None
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetMaterial() *RTBAdsResponseInfo_Seat_Ad_Material {
	if x != nil {
		return x.Material
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetCampaignDate() string {
	if x != nil {
		return x.CampaignDate
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetCreativeId() int64 {
	if x != nil {
		return x.CreativeId
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetAdSource() string {
	if x != nil {
		return x.AdSource
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetBidPrice() uint64 {
	if x != nil {
		return x.BidPrice
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetLandingUrl() string {
	if x != nil {
		return x.LandingUrl
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetClickThroughUrl() string {
	if x != nil {
		return x.ClickThroughUrl
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetDeeplinkUrl() string {
	if x != nil {
		return x.DeeplinkUrl
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetUniversalLink() string {
	if x != nil {
		return x.UniversalLink
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetImpressionTrackingUrl() []string {
	if x != nil {
		return x.ImpressionTrackingUrl
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetClickTrackingUrl() []string {
	if x != nil {
		return x.ClickTrackingUrl
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetWinNoticeUrl() []string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetEventTrack() []*RTBAdsResponseInfo_Seat_Ad_EventTrack {
	if x != nil {
		return x.EventTrack
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetDpTrackingUrl() []string {
	if x != nil {
		return x.DpTrackingUrl
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetBidMode() BidMode {
	if x != nil {
		return x.BidMode
	}
	return BidMode_CPM
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetCostMode() BidMode {
	if x != nil {
		return x.CostMode
	}
	return BidMode_CPM
}

func (x *RTBAdsResponseInfo_Seat_Ad) GetMaterialId() string {
	if x != nil {
		return x.MaterialId
	}
	return ""
}

type RTBAdsResponseInfo_Seat_Ad_Material struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImgUrls       []string `protobuf:"bytes,1,rep,name=img_urls,json=imgUrls,proto3" json:"img_urls,omitempty"`                    // 广告展示素材对应的 URL 列表
	Title         string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                                       // 广告标题
	Description   string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                           // 广告描述
	AdWords       string   `protobuf:"bytes,4,opt,name=ad_words,json=adWords,proto3" json:"ad_words,omitempty"`                    // 广告语
	VideoCover    string   `protobuf:"bytes,5,opt,name=video_cover,json=videoCover,proto3" json:"video_cover,omitempty"`           // 视频缩略图
	VideoUrl      string   `protobuf:"bytes,6,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`                 // 视频素材地址
	Width         int32    `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`                                      // 素材尺寸宽度
	Height        int32    `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`                                    // 素材尺寸高度
	VideoDuration int32    `protobuf:"varint,9,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty"` // 视频时长，单位毫秒
	IconUrl       string   `protobuf:"bytes,10,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`                   // icon url
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) Reset() {
	*x = RTBAdsResponseInfo_Seat_Ad_Material{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponseInfo_Seat_Ad_Material) ProtoMessage() {}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponseInfo_Seat_Ad_Material.ProtoReflect.Descriptor instead.
func (*RTBAdsResponseInfo_Seat_Ad_Material) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2, 0, 0, 0}
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetImgUrls() []string {
	if x != nil {
		return x.ImgUrls
	}
	return nil
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetAdWords() string {
	if x != nil {
		return x.AdWords
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetVideoCover() string {
	if x != nil {
		return x.VideoCover
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad_Material) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

// 自定义事件监测地址
type RTBAdsResponseInfo_Seat_Ad_EventTrack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类型说明 1:视频广告播放开始监测打点 2:视频广告有效 3:视频广告播放完毕监测打点 4:下载开始 5:下载结束 6:安装完成 7:视频播放50%
	Type uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Url  []string `protobuf:"bytes,2,rep,name=url,proto3" json:"url,omitempty"`
}

func (x *RTBAdsResponseInfo_Seat_Ad_EventTrack) Reset() {
	*x = RTBAdsResponseInfo_Seat_Ad_EventTrack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domob_2_8_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBAdsResponseInfo_Seat_Ad_EventTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBAdsResponseInfo_Seat_Ad_EventTrack) ProtoMessage() {}

func (x *RTBAdsResponseInfo_Seat_Ad_EventTrack) ProtoReflect() protoreflect.Message {
	mi := &file_domob_2_8_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBAdsResponseInfo_Seat_Ad_EventTrack.ProtoReflect.Descriptor instead.
func (*RTBAdsResponseInfo_Seat_Ad_EventTrack) Descriptor() ([]byte, []int) {
	return file_domob_2_8_proto_rawDescGZIP(), []int{2, 0, 0, 1}
}

func (x *RTBAdsResponseInfo_Seat_Ad_EventTrack) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *RTBAdsResponseInfo_Seat_Ad_EventTrack) GetUrl() []string {
	if x != nil {
		return x.Url
	}
	return nil
}

var File_domob_2_8_proto protoreflect.FileDescriptor

var file_domob_2_8_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x5f, 0x32, 0x2e, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x22, 0xe7, 0x0d, 0x0a, 0x0d, 0x52, 0x54, 0x42,
	0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x5f, 0x74, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x71,
	0x54, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x74, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x6d, 0x73, 0x12, 0x31, 0x0a,
	0x03, 0x69, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x6f, 0x6d,
	0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x69, 0x6d, 0x70,
	0x12, 0x33, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70,
	0x70, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x74, 0x74, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0xa8, 0x03, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x65, 0x61, 0x6c, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42,
	0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x04, 0x64, 0x65, 0x61, 0x6c,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x62, 0x69,
	0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x64,
	0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x62, 0x69,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x70,
	0x63, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x09, 0x63, 0x6f, 0x73,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x64,
	0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x63, 0x6f,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x1a, 0x3c, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x64,
	0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x1a, 0xff, 0x05, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x64, 0x66, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d,
	0x65, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72,
	0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x03,
	0x67, 0x65, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x6f, 0x6d, 0x6f,
	0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12,
	0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x18, 0x16, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x12, 0x36, 0x0a, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x18, 0x17, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x43, 0x41, 0x49, 0x44, 0x52, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x1a, 0x29, 0x0a, 0x03, 0x47, 0x65,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x1a, 0x2c, 0x0a, 0x04, 0x43, 0x41, 0x49, 0x44, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x61, 0x69, 0x64, 0x1a, 0xac, 0x01, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xa9, 0x01, 0x0a, 0x0e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6d, 0x73, 0x12,
	0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xb2,
	0x0c, 0x0a, 0x12, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x1a, 0xe7, 0x0b, 0x0a, 0x04, 0x53, 0x65,
	0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x31, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41,
	0x64, 0x52, 0x02, 0x61, 0x64, 0x1a, 0x9b, 0x0b, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x46, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e, 0x52, 0x54, 0x42, 0x41,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52,
	0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54,
	0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12,
	0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0e,
	0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x4d, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e,
	0x52, 0x54, 0x42, 0x41, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x70, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x08, 0x62, 0x69, 0x64,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x64, 0x6f,
	0x6d, 0x6f, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x62, 0x69, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x64, 0x6f, 0x6d, 0x6f, 0x62, 0x2e,
	0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x49, 0x64, 0x1a, 0xa6, 0x02, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x1b,
	0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x1a, 0x32, 0x0a, 0x0a, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22,
	0x42, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x78, 0x74,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x02, 0x12, 0x09, 0x0a,
	0x05, 0x46, 0x6c, 0x61, 0x73, 0x68, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x10, 0x04, 0x2a, 0x1b, 0x0a, 0x07, 0x42, 0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x07,
	0x0a, 0x03, 0x43, 0x50, 0x4d, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x43, 0x10, 0x01,
	0x2a, 0x7a, 0x0a, 0x0d, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x41,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53,
	0x54, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41, 0x4e, 0x4e,
	0x45, 0x52, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x0d, 0x0a,
	0x09, 0x49, 0x4e, 0x43, 0x45, 0x4e, 0x54, 0x49, 0x56, 0x45, 0x10, 0x07, 0x42, 0x13, 0x5a, 0x11,
	0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x64, 0x6f, 0x6d, 0x6f,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_domob_2_8_proto_rawDescOnce sync.Once
	file_domob_2_8_proto_rawDescData = file_domob_2_8_proto_rawDesc
)

func file_domob_2_8_proto_rawDescGZIP() []byte {
	file_domob_2_8_proto_rawDescOnce.Do(func() {
		file_domob_2_8_proto_rawDescData = protoimpl.X.CompressGZIP(file_domob_2_8_proto_rawDescData)
	})
	return file_domob_2_8_proto_rawDescData
}

var file_domob_2_8_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_domob_2_8_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_domob_2_8_proto_goTypes = []interface{}{
	(BidMode)(0),       // 0: domob.BidMode
	(InventoryType)(0), // 1: domob.InventoryType
	(RTBAdsResponseInfo_Seat_Ad_CreativeType)(0),  // 2: domob.RTBAdsResponseInfo.Seat.Ad.CreativeType
	(*RTBAdsRequest)(nil),                         // 3: domob.RTBAdsRequest
	(*RTBAdsResponse)(nil),                        // 4: domob.RTBAdsResponse
	(*RTBAdsResponseInfo)(nil),                    // 5: domob.RTBAdsResponseInfo
	(*RTBAdsRequest_Impression)(nil),              // 6: domob.RTBAdsRequest.Impression
	(*RTBAdsRequest_Device)(nil),                  // 7: domob.RTBAdsRequest.Device
	(*RTBAdsRequest_App)(nil),                     // 8: domob.RTBAdsRequest.App
	(*RTBAdsRequest_Impression_Deal)(nil),         // 9: domob.RTBAdsRequest.Impression.Deal
	(*RTBAdsRequest_Device_Geo)(nil),              // 10: domob.RTBAdsRequest.Device.Geo
	(*RTBAdsRequest_Device_CAID)(nil),             // 11: domob.RTBAdsRequest.Device.CAID
	(*RTBAdsResponseInfo_Seat)(nil),               // 12: domob.RTBAdsResponseInfo.Seat
	(*RTBAdsResponseInfo_Seat_Ad)(nil),            // 13: domob.RTBAdsResponseInfo.Seat.Ad
	(*RTBAdsResponseInfo_Seat_Ad_Material)(nil),   // 14: domob.RTBAdsResponseInfo.Seat.Ad.Material
	(*RTBAdsResponseInfo_Seat_Ad_EventTrack)(nil), // 15: domob.RTBAdsResponseInfo.Seat.Ad.EventTrack
}
var file_domob_2_8_proto_depIdxs = []int32{
	6,  // 0: domob.RTBAdsRequest.imp:type_name -> domob.RTBAdsRequest.Impression
	7,  // 1: domob.RTBAdsRequest.device:type_name -> domob.RTBAdsRequest.Device
	8,  // 2: domob.RTBAdsRequest.app:type_name -> domob.RTBAdsRequest.App
	5,  // 3: domob.RTBAdsResponse.info:type_name -> domob.RTBAdsResponseInfo
	12, // 4: domob.RTBAdsResponseInfo.seat:type_name -> domob.RTBAdsResponseInfo.Seat
	9,  // 5: domob.RTBAdsRequest.Impression.deal:type_name -> domob.RTBAdsRequest.Impression.Deal
	0,  // 6: domob.RTBAdsRequest.Impression.bid_mode:type_name -> domob.BidMode
	0,  // 7: domob.RTBAdsRequest.Impression.cost_mode:type_name -> domob.BidMode
	1,  // 8: domob.RTBAdsRequest.Impression.inventory_type:type_name -> domob.InventoryType
	10, // 9: domob.RTBAdsRequest.Device.geo:type_name -> domob.RTBAdsRequest.Device.Geo
	11, // 10: domob.RTBAdsRequest.Device.caids:type_name -> domob.RTBAdsRequest.Device.CAID
	13, // 11: domob.RTBAdsResponseInfo.Seat.ad:type_name -> domob.RTBAdsResponseInfo.Seat.Ad
	2,  // 12: domob.RTBAdsResponseInfo.Seat.Ad.creative_type:type_name -> domob.RTBAdsResponseInfo.Seat.Ad.CreativeType
	14, // 13: domob.RTBAdsResponseInfo.Seat.Ad.material:type_name -> domob.RTBAdsResponseInfo.Seat.Ad.Material
	15, // 14: domob.RTBAdsResponseInfo.Seat.Ad.event_track:type_name -> domob.RTBAdsResponseInfo.Seat.Ad.EventTrack
	0,  // 15: domob.RTBAdsResponseInfo.Seat.Ad.bid_mode:type_name -> domob.BidMode
	0,  // 16: domob.RTBAdsResponseInfo.Seat.Ad.cost_mode:type_name -> domob.BidMode
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_domob_2_8_proto_init() }
func file_domob_2_8_proto_init() {
	if File_domob_2_8_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_domob_2_8_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_Impression_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsRequest_Device_CAID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponseInfo_Seat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponseInfo_Seat_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponseInfo_Seat_Ad_Material); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domob_2_8_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBAdsResponseInfo_Seat_Ad_EventTrack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_domob_2_8_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_domob_2_8_proto_goTypes,
		DependencyIndexes: file_domob_2_8_proto_depIdxs,
		EnumInfos:         file_domob_2_8_proto_enumTypes,
		MessageInfos:      file_domob_2_8_proto_msgTypes,
	}.Build()
	File_domob_2_8_proto = out.File
	file_domob_2_8_proto_rawDesc = nil
	file_domob_2_8_proto_goTypes = nil
	file_domob_2_8_proto_depIdxs = nil
}
