/**
Copyright (c) 2021 Domob Inc. All rights reserved.
@description 多盟实时竞价交易协议(对外)
@版本: 2.8
**/

syntax = "proto3";
package domob;
option go_package = "mh_proxy/pb/domob";

// 0-cpm,1-cpc,2-pmp,3-抄底分成
enum BidMode{
  CPM = 0;
  CPC = 1;
}

// 0:未知、1:开屏、2:信息流、3:插屏、4:banner、5:贴片、6:icon、7:激励视频;
enum InventoryType {
  UNKNOWN = 0;
  SPLASH = 1;
  NATIVE = 2;
  INTERSTITIAL = 3;
  BANNER = 4;
  PRE_VIDEO = 5;
  ICON = 6;
  INCENTIVE = 7;
}

message RTBAdsRequest {
  int64 req_id = 2; // 媒体请求ID
  bool is_ping = 3; // ping 请求
  int64 req_tms = 4;   // 请求时间 单位毫秒
  int32 search_limit_tms = 7; // 处理限制时间 ms 不得小于100ms
  // 可展示的位置
  message Impression  {
    int32 id = 1;     // 此impression在当前Request中的唯一id,从0开始
    repeated int64 template_id = 2;  //  广告位ID
    // 和媒体约定 PMP 交易时使用，包括优先交易、直采交易等等。 必选填写 dealid 和价格。
    message Deal {
      // 媒体分配的dealid
      string deal_id = 1;
      //此Deal对应的价格, 单位(分)
      int32 min_price = 2;
    }
    repeated Deal deal = 9;  // 媒体约定的 PMP 交易信息
    int32 bid_floor = 10;     // RTB的底价，非RTB方式可不填, 单位：分
    string adslot_id = 11; // 广告资源位id
    // 支持的出价类型;仅RTB有效
    repeated BidMode bid_mode = 12;
    // cpc类型广告底价，单位：分; 仅RTB有效
    int64 cpc_bid_floor = 13;
    // 支持的结算类型;仅RTB有效
    repeated BidMode cost_mode = 14;
    // 流量类型
    repeated InventoryType inventory_type = 15;
  }
  repeated Impression imp = 8; // 资源位信息，一般情况下只允许填一个，开 屏广告如果需要同时请求多天预加载广告 的情况下，可以填写多个 imp

  message Device {
    // ipv4 点分十进制, 必须为终端真实IP地址
    string ip = 1;
    // user agent，来自http头, 必须是以Mozilla开头的标准格式ua
    string user_agent = 2;
    // IOS6.0及以上的idfa号
    string idfa = 3;
    string idfa_md5 = 4;
    // 安卓设备的imei号
    string imei = 5;
    // 安卓设备的imei号的md5值,若填写imei原值，则不用填此字段
    string imei_md5 = 6;
    // oaid
    string oaid = 7;
    string oaid_md5 = 8;
    // android_id
    string android_id = 9;

    // 设备类型，0-手机;1-平板;2-PC;3-互联网电视
    int32 device_type = 10;

    // 设备品牌
    // 例如：nokia, samsung
    string brand = 11;

    // 设备型号
    // 例如：n70, galaxy
    string model = 12;

    // 操作系统
    // 例如：1:Android,2:iOS
    int32 os = 13;

    // 操作系统版本
    // 例如：7.0.2
    string osv = 14;
    // 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g, 5-5g
    int32 network = 15; // default 1

    // 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
    int32 operator = 16;

    // 设备屏幕尺寸：宽
    int32 width = 17;

    // 设备屏幕尺寸：高
    int32 height = 18;
    // 屏幕方向 0-未知, 1-竖屏, 2-横屏
    int32 orientation = 20;

    message Geo {
      // 纬度, 取值范围[-90.0 , +90.0]
      double lat = 1;
      // 经度, 取值范围[-180.0 , +180.0]
      double lon = 2;
    }
    Geo geo = 21;

    // 用户已安装 app 列表
    repeated string installed_app = 22;

    // 广协CAID
    message CAID {
      // 必须和版本同时使用才有意义，版本形如"20201201"
      string ver = 1;
      string caid = 2;
    }
    repeated CAID caids = 23;

    // 安卓系统启动标识
    string boot_mark = 24;
    // 安卓系统启动更新标识
    string update_mark = 25;
  }
  Device device = 10; // 设备相关信息

  // APP属性
  message App {
    // 应用包名, 例如：com.moji.MojiWeather
    string package_name = 1;
    // 应用名，例如：陌陌
    string app_name = 2;
    // app类目, 参见多盟APP类目表
    repeated string category = 3;
    // 应用版本号 eg 3.2.9
    string app_version = 4;
    // 应用商店版本号 eg (oppo、vivo、华为等)
    string app_store_version = 5;
  }
  App app = 11; // App 相关信息

  string detected_language = 13;   // 页面或者用户的语言

  bool https_required = 14;   // 是否必须返回https广告
  bool is_deeplink = 15; // 是否支持deeplink
}

// 响应
message RTBAdsResponse {
  int32 status = 1; // 0为出价  1为不出价
  string message = 2;
  int64 req_id = 3;
  int64 process_tms = 4;   // 内部处理的时间 单位毫秒
  repeated RTBAdsResponseInfo info = 5;
}

message RTBAdsResponseInfo {
  // 一组广告，对应 Request 中的一个 Impression
  message Seat {
    // 对应 Request 中 Impression 的 id，表示需求方期 望填充对应的 Impression
    int32 id = 1;
    // 广告字段 包含一个素材及对应的监测 URL
    message Ad {
      // 当前ad在seat中的序号，从0开始 0
      int32 id = 1;
      enum CreativeType {
        None = 0;
        Txt = 1;
        Image = 2;
        Flash = 3;
        Video = 4;
      }
      // 创意类型，表示素材是一个图片或者视频片段
      CreativeType creative_type = 2;

      message Material {
        repeated string img_urls = 1; // 广告展示素材对应的 URL 列表
        string title = 2;    // 广告标题
        string description = 3; // 广告描述
        string ad_words = 4; // 广告语
        string video_cover = 5; // 视频缩略图
        string video_url = 6; // 视频素材地址
        int32 width = 7; // 素材尺寸宽度
        int32 height = 8; // 素材尺寸高度
        int32 video_duration = 9; // 视频时长，单位毫秒
        string icon_url = 10; // icon url
      }
      // 原生广告使用
      Material material = 3;
      // 表示此次报价使用的媒体 deallid
      string deal_id = 4;
      // 表示原生素材希望投放的日期，尤其在开屏这种 预加载时间较长的流量中使用, 仅开屏使用，如:"20210806"
      string campaign_date = 5;
      // 广告创意标识
      int64 sponsor_id = 6;
      // 待废弃字段
      int64 creative_id = 9;

      //广告来源
      string ad_source = 10;

      // adx场景, 返回报价供上游adx竞价, 单位(分)
      uint64 bid_price = 11;
      // 下载类必填，下载app名
      string app_name = 12;
      // 下载推广类型必填，安卓:应 用包名，iOS设备使用bundleid
      string package_name = 13;
      // iOS应用下载app_id
      string market_id = 14;
      // 最终目标landing(着陆页)页，非点击地址
      string landing_url = 15;
      // 点击地址，在触发点击时，通过此地址到达落地 页
      string click_through_url = 16;
      //APP唤醒地址
      string deeplink_url = 17;
      //APP下载地址
      string download_url = 18;
      // IOS 通用唤起链接
      string universal_link = 19;
      // 曝光监测地址
      repeated string impression_tracking_url = 20;
      // 点击监测地址
      repeated string click_tracking_url = 21;
      // 竞价成功通知，媒体 ADX 竞价成功时通过此 URL 通知
      repeated string win_notice_url = 22;
      // 自定义事件监测地址
      message EventTrack {
        // 类型说明 1:视频广告播放开始监测打点 2:视频广告有效 3:视频广告播放完毕监测打点 4:下载开始 5:下载结束 6:安装完成 7:视频播放50%
        uint32 type = 1;
        repeated string url = 2;
      }
      repeated EventTrack event_track = 23;
      // deeplink调起事件监测地址
      repeated string dp_tracking_url = 29;
      // 出价类型
      BidMode bid_mode = 31;
      // 结算类型
      BidMode cost_mode = 32;
      // 素材id
      string material_id = 35;
    }
    repeated Ad ad = 2;
  }
  repeated Seat seat = 5;
}
