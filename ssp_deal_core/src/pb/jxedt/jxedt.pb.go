// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: jxedt.proto

package jxedt

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求唯一id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 接口协议版本
	ApiVersion string `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`
	// 媒体信息
	Site *Request_Site `protobuf:"bytes,3,opt,name=site,proto3" json:"site,omitempty"`
	// App信息
	App *Request_App `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`
	// 广告位信息
	Imp *Request_Imp `protobuf:"bytes,5,opt,name=imp,proto3" json:"imp,omitempty"`
	// 设备信息
	Device *Request_Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	// 用户信息
	User *Request_User `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *Request) GetSite() *Request_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *Request) GetApp() *Request_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Request) GetImp() *Request_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *Request) GetDevice() *Request_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Request) GetUser() *Request_User {
	if x != nil {
		return x.User
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对应请求唯一id，与请求中id相同
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// 唯一竞价id，由竞价者返回
	BidId string `protobuf:"bytes,3,opt,name=bid_id,json=bidId,proto3" json:"bid_id,omitempty"`
	// 广告内容对象
	Bid *Response_Bid `protobuf:"bytes,4,opt,name=bid,proto3" json:"bid,omitempty"`
	// 广告应答状态码，表示有无广告返回给媒体以及其他响应状态
	Code int32 `protobuf:"varint,5,opt,name=code,proto3" json:"code,omitempty"`
	// 广告应答描述
	Msg string `protobuf:"bytes,6,opt,name=msg,proto3" json:"msg,omitempty"`
	// DSP名称
	DspName string `protobuf:"bytes,7,opt,name=dsp_name,json=dspName,proto3" json:"dsp_name,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Response) GetBidId() string {
	if x != nil {
		return x.BidId
	}
	return ""
}

func (x *Response) GetBid() *Response_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *Response) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Response) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *Response) GetDspName() string {
	if x != nil {
		return x.DspName
	}
	return ""
}

type Request_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体id，我方提供
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 媒体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Request_Site) Reset() {
	*x = Request_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Site) ProtoMessage() {}

func (x *Request_Site) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Site.ProtoReflect.Descriptor instead.
func (*Request_Site) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Request_Site) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Site) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Request_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// App id，我方提供
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// App名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// App包名
	Bundle string `protobuf:"bytes,3,opt,name=bundle,proto3" json:"bundle,omitempty"`
	// App版本
	AppVersion string `protobuf:"bytes,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// 用户标签 1（拼多多），2（淘宝），3（京东），4（美团），5（美团外卖），6（支付宝），7（饿了么），8（快手），9（快手极速版）
	UserTag []string `protobuf:"bytes,5,rep,name=user_tag,json=userTag,proto3" json:"user_tag,omitempty"`
}

func (x *Request_App) Reset() {
	*x = Request_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_App) ProtoMessage() {}

func (x *Request_App) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_App.ProtoReflect.Descriptor instead.
func (*Request_App) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Request_App) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Request_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *Request_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *Request_App) GetUserTag() []string {
	if x != nil {
		return x.UserTag
	}
	return nil
}

type Request_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 曝光唯一id，与竞价返回值对应
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 广告位id，我方提供
	TagId string `protobuf:"bytes,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	// 图文广告相关信息
	Banner *Request_Banner `protobuf:"bytes,3,opt,name=banner,proto3" json:"banner,omitempty"`
	// 广告位底价每cpm，单位分，1000
	BidFloor int32 `protobuf:"varint,5,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"`
	// 模板类型：1：一图2：一图一文3：一图两文4：一视频5：一视频一文6：一视频两文
	TemplateId []string `protobuf:"bytes,6,rep,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// PD合作可选id
	Deals []string `protobuf:"bytes,7,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *Request_Imp) Reset() {
	*x = Request_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp) ProtoMessage() {}

func (x *Request_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp.ProtoReflect.Descriptor instead.
func (*Request_Imp) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Request_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *Request_Imp) GetBanner() *Request_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *Request_Imp) GetBidFloor() int32 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

func (x *Request_Imp) GetTemplateId() []string {
	if x != nil {
		return x.TemplateId
	}
	return nil
}

func (x *Request_Imp) GetDeals() []string {
	if x != nil {
		return x.Deals
	}
	return nil
}

type Request_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 广告位宽，像素
	Width int32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	// 广告位高，像素
	Height int32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	// 广告位类型  1开屏 2信息流 3banner 4插屏
	PosType int32 `protobuf:"varint,3,opt,name=pos_type,json=posType,proto3" json:"pos_type,omitempty"`
	// 支持素材类型 jpg/png/gif/mp4
	MaterialType []string `protobuf:"bytes,4,rep,name=material_type,json=materialType,proto3" json:"material_type,omitempty"`
	// 交互类型：h5/deeplink/universalink
	InteractionType []string `protobuf:"bytes,5,rep,name=interaction_type,json=interactionType,proto3" json:"interaction_type,omitempty"`
}

func (x *Request_Banner) Reset() {
	*x = Request_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Banner) ProtoMessage() {}

func (x *Request_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Banner.ProtoReflect.Descriptor instead.
func (*Request_Banner) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Request_Banner) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Request_Banner) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Request_Banner) GetPosType() int32 {
	if x != nil {
		return x.PosType
	}
	return 0
}

func (x *Request_Banner) GetMaterialType() []string {
	if x != nil {
		return x.MaterialType
	}
	return nil
}

func (x *Request_Banner) GetInteractionType() []string {
	if x != nil {
		return x.InteractionType
	}
	return nil
}

type Request_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备浏览器useragent
	Ua string `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	// 设备地理位置
	Geo *Request_Geo `protobuf:"bytes,2,opt,name=geo,proto3" json:"geo,omitempty"`
	// 设备ip
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 设备类型 0：手机，1：平板，2：PC，3：互联网电视
	DeviceType int32 `protobuf:"varint,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// 设备制造商，apple
	Make string `protobuf:"bytes,5,opt,name=make,proto3" json:"make,omitempty"`
	// 设备机型，iphone7,1
	Model string `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`
	// 设备系统，ios
	Os string `protobuf:"bytes,7,opt,name=os,proto3" json:"os,omitempty"`
	// 设备系统版本，13
	Osv string `protobuf:"bytes,8,opt,name=osv,proto3" json:"osv,omitempty"`
	// 设备屏幕高，像素
	Height int32 `protobuf:"varint,9,opt,name=height,proto3" json:"height,omitempty"`
	// 设备屏幕宽，像素
	Width int32 `protobuf:"varint,10,opt,name=width,proto3" json:"width,omitempty"`
	// 设备像素
	Ppi int32 `protobuf:"varint,11,opt,name=ppi,proto3" json:"ppi,omitempty"`
	// 设备运营商，0：未知，1：中国移动，2：中国电信，3：中国联通
	Carrier int32 `protobuf:"varint,12,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// 设备联网方式，0：未知，1：wifi，2：2g，3：3g，4：4g，5：5g
	Network int32 `protobuf:"varint,13,opt,name=network,proto3" json:"network,omitempty"`
	// Ios设备号，明文，明文md5二选一即可
	Idfa string `protobuf:"bytes,14,opt,name=idfa,proto3" json:"idfa,omitempty"`
	// Ios设备号，md5，明文md5二选一即可
	IdfaMd5 string `protobuf:"bytes,15,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`
	// android设备号，明文，明文md5二选一即可
	Imei string `protobuf:"bytes,16,opt,name=imei,proto3" json:"imei,omitempty"`
	// android设备号，md5，明文md5二选一即可
	ImeiMd5 string `protobuf:"bytes,17,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	// android设备号，明文
	Oaid string `protobuf:"bytes,18,opt,name=oaid,proto3" json:"oaid,omitempty"`
	// android设备号，明文
	AndroidId string `protobuf:"bytes,19,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	// 设备mac地址
	Mac string `protobuf:"bytes,20,opt,name=mac,proto3" json:"mac,omitempty"`
	// ios设备系统启动时间，1595214620.383940
	BootTime string `protobuf:"bytes,21,opt,name=boot_time,json=bootTime,proto3" json:"boot_time,omitempty"`
	// ios设备系统更新时间，1595214620.383940
	UpdateTime string `protobuf:"bytes,22,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// ios国家代码，使用 ISO-3166-1-alpha-2
	Country string `protobuf:"bytes,23,opt,name=country,proto3" json:"country,omitempty"`
	// ios设备语言，使用 ISO-3166-1-alpha-2
	Language string `protobuf:"bytes,24,opt,name=language,proto3" json:"language,omitempty"`
	// ios设备名称md5，小写
	PhoneName string `protobuf:"bytes,25,opt,name=phone_name,json=phoneName,proto3" json:"phone_name,omitempty"`
	// ios设备最大内存，KB，3955589120
	MemorySize string `protobuf:"bytes,26,opt,name=memory_size,json=memorySize,proto3" json:"memory_size,omitempty"`
	// ios设备最大容量，KB，63900340224
	DiskSize string `protobuf:"bytes,27,opt,name=disk_size,json=diskSize,proto3" json:"disk_size,omitempty"`
	// ios设备型号码，D22AP
	ModelCode string `protobuf:"bytes,28,opt,name=model_code,json=modelCode,proto3" json:"model_code,omitempty"`
	// ios设备时区，2880
	Timezone string `protobuf:"bytes,29,opt,name=timezone,proto3" json:"timezone,omitempty"`
	// 取原值回传 iOS：1623815045.970028；Android：ec7f4f33-411a-47bc-8067-744a4e7e0723
	BootMark string `protobuf:"bytes,30,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	// 取原值回传 iOS：1581141691.570419583 Android：1004697.709999999
	UpdateMark string `protobuf:"bytes,31,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	// ios系统初始化时间
	BirthTime string `protobuf:"bytes,32,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`
	// ag版本
	AgVersion string `protobuf:"bytes,34,opt,name=ag_version,json=agVersion,proto3" json:"ag_version,omitempty"`
	// hms版本
	HmsVersion string `protobuf:"bytes,35,opt,name=hms_version,json=hmsVersion,proto3" json:"hms_version,omitempty"`
	// 拼多多paid
	Paid string `protobuf:"bytes,36,opt,name=paid,proto3" json:"paid,omitempty"`
	// 设备caid值
	Caid string `protobuf:"bytes,37,opt,name=caid,proto3" json:"caid,omitempty"`
	// caid版本
	CaidVersion string `protobuf:"bytes,38,opt,name=caid_version,json=caidVersion,proto3" json:"caid_version,omitempty"`
}

func (x *Request_Device) Reset() {
	*x = Request_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device) ProtoMessage() {}

func (x *Request_Device) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device.ProtoReflect.Descriptor instead.
func (*Request_Device) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Request_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *Request_Device) GetGeo() *Request_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Request_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Request_Device) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *Request_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *Request_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Request_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *Request_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *Request_Device) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Request_Device) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Request_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *Request_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *Request_Device) GetNetwork() int32 {
	if x != nil {
		return x.Network
	}
	return 0
}

func (x *Request_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *Request_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *Request_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *Request_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *Request_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *Request_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *Request_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *Request_Device) GetBootTime() string {
	if x != nil {
		return x.BootTime
	}
	return ""
}

func (x *Request_Device) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Request_Device) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Request_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Request_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *Request_Device) GetMemorySize() string {
	if x != nil {
		return x.MemorySize
	}
	return ""
}

func (x *Request_Device) GetDiskSize() string {
	if x != nil {
		return x.DiskSize
	}
	return ""
}

func (x *Request_Device) GetModelCode() string {
	if x != nil {
		return x.ModelCode
	}
	return ""
}

func (x *Request_Device) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *Request_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *Request_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *Request_Device) GetBirthTime() string {
	if x != nil {
		return x.BirthTime
	}
	return ""
}

func (x *Request_Device) GetAgVersion() string {
	if x != nil {
		return x.AgVersion
	}
	return ""
}

func (x *Request_Device) GetHmsVersion() string {
	if x != nil {
		return x.HmsVersion
	}
	return ""
}

func (x *Request_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *Request_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *Request_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

type Request_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 纬度，精确到小数点后6位
	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	// 经度，精确到小数点后6位
	Lon float64 `protobuf:"fixed64,2,opt,name=lon,proto3" json:"lon,omitempty"`
}

func (x *Request_Geo) Reset() {
	*x = Request_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Geo) ProtoMessage() {}

func (x *Request_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Geo.ProtoReflect.Descriptor instead.
func (*Request_Geo) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Request_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Request_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

type Request_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户性别，0未知，1男性，2女性
	Gender int32 `protobuf:"varint,1,opt,name=gender,proto3" json:"gender,omitempty"`
	// 用户年龄，18或{17-20}
	Age string `protobuf:"bytes,2,opt,name=age,proto3" json:"age,omitempty"`
}

func (x *Request_User) Reset() {
	*x = Request_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_User) ProtoMessage() {}

func (x *Request_User) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_User.ProtoReflect.Descriptor instead.
func (*Request_User) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{0, 6}
}

func (x *Request_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *Request_User) GetAge() string {
	if x != nil {
		return x.Age
	}
	return ""
}

type Response_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对应请求imp中id
	ImpId string `protobuf:"bytes,1,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"`
	// 出价，每cpm价格，单位分
	Price int32 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	// 获胜通知地址，AES加密加密规则以我方为准，需支持宏替换
	Nurl string `protobuf:"bytes,3,opt,name=nurl,proto3" json:"nurl,omitempty"`
	// 广告创意id
	Crid string `protobuf:"bytes,4,opt,name=crid,proto3" json:"crid,omitempty"`
	// 广告宽度
	ImgWidth int32 `protobuf:"varint,5,opt,name=img_width,json=imgWidth,proto3" json:"img_width,omitempty"`
	// 广告高度
	ImgHeight int32 `protobuf:"varint,6,opt,name=img_height,json=imgHeight,proto3" json:"img_height,omitempty"`
	// 主素材地址
	ImgUrl string `protobuf:"bytes,7,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	// 封面地址
	CoverUrl string `protobuf:"bytes,8,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	// 视频时长，s
	VideoDuration int32 `protobuf:"varint,9,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty"`
	// 视频码率，Kbps
	VideoBitrate int32 `protobuf:"varint,10,opt,name=video_bitrate,json=videoBitrate,proto3" json:"video_bitrate,omitempty"`
	// 视频大小，KB
	VideoSize float32 `protobuf:"fixed32,11,opt,name=video_size,json=videoSize,proto3" json:"video_size,omitempty"`
	// 广告曝光监测地址，支持多个
	ImpressionTrackingUrl []string `protobuf:"bytes,12,rep,name=impression_tracking_url,json=impressionTrackingUrl,proto3" json:"impression_tracking_url,omitempty"`
	// 广告点击监测地址，支持多个
	ClickTrackingUrl []string `protobuf:"bytes,13,rep,name=click_tracking_url,json=clickTrackingUrl,proto3" json:"click_tracking_url,omitempty"`
	// 广告落地页，执行优先级最低
	LandingPageUrl string `protobuf:"bytes,14,opt,name=landing_page_url,json=landingPageUrl,proto3" json:"landing_page_url,omitempty"`
	// Deeplink地址，执行优先级低于ulkurl
	DeeplinkUrl string `protobuf:"bytes,15,opt,name=deeplink_url,json=deeplinkUrl,proto3" json:"deeplink_url,omitempty"`
	// Universallink地址，仅ios，执行优先级最高
	UlkUrl string `protobuf:"bytes,16,opt,name=ulk_url,json=ulkUrl,proto3" json:"ulk_url,omitempty"`
	// 推广应用包名
	AppPackageName string `protobuf:"bytes,17,opt,name=app_package_name,json=appPackageName,proto3" json:"app_package_name,omitempty"`
	// 推广应用名称
	AppName string `protobuf:"bytes,18,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 图标地址
	IconUrl string `protobuf:"bytes,19,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// 标题
	Title string `protobuf:"bytes,20,opt,name=title,proto3" json:"title,omitempty"`
	// 描述
	Desc string `protobuf:"bytes,21,opt,name=desc,proto3" json:"desc,omitempty"`
	// 模板类型：1：一图2：一图一文3：一图两文4：一视频5：一视频一文6：一视频两文
	TemplateId string `protobuf:"bytes,22,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 唤醒监测地址，支持多个
	WakeUpTrackingUrl []string `protobuf:"bytes,23,rep,name=wake_up_tracking_url,json=wakeUpTrackingUrl,proto3" json:"wake_up_tracking_url,omitempty"`
	// PD合作必填
	DealId string `protobuf:"bytes,24,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`
}

func (x *Response_Bid) Reset() {
	*x = Response_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jxedt_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Bid) ProtoMessage() {}

func (x *Response_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_jxedt_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Bid.ProtoReflect.Descriptor instead.
func (*Response_Bid) Descriptor() ([]byte, []int) {
	return file_jxedt_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Response_Bid) GetImpId() string {
	if x != nil {
		return x.ImpId
	}
	return ""
}

func (x *Response_Bid) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Response_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *Response_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *Response_Bid) GetImgWidth() int32 {
	if x != nil {
		return x.ImgWidth
	}
	return 0
}

func (x *Response_Bid) GetImgHeight() int32 {
	if x != nil {
		return x.ImgHeight
	}
	return 0
}

func (x *Response_Bid) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *Response_Bid) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *Response_Bid) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *Response_Bid) GetVideoBitrate() int32 {
	if x != nil {
		return x.VideoBitrate
	}
	return 0
}

func (x *Response_Bid) GetVideoSize() float32 {
	if x != nil {
		return x.VideoSize
	}
	return 0
}

func (x *Response_Bid) GetImpressionTrackingUrl() []string {
	if x != nil {
		return x.ImpressionTrackingUrl
	}
	return nil
}

func (x *Response_Bid) GetClickTrackingUrl() []string {
	if x != nil {
		return x.ClickTrackingUrl
	}
	return nil
}

func (x *Response_Bid) GetLandingPageUrl() string {
	if x != nil {
		return x.LandingPageUrl
	}
	return ""
}

func (x *Response_Bid) GetDeeplinkUrl() string {
	if x != nil {
		return x.DeeplinkUrl
	}
	return ""
}

func (x *Response_Bid) GetUlkUrl() string {
	if x != nil {
		return x.UlkUrl
	}
	return ""
}

func (x *Response_Bid) GetAppPackageName() string {
	if x != nil {
		return x.AppPackageName
	}
	return ""
}

func (x *Response_Bid) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Response_Bid) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *Response_Bid) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Response_Bid) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Response_Bid) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Response_Bid) GetWakeUpTrackingUrl() []string {
	if x != nil {
		return x.WakeUpTrackingUrl
	}
	return nil
}

func (x *Response_Bid) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

var File_jxedt_proto protoreflect.FileDescriptor

var file_jxedt_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6a,
	0x78, 0x65, 0x64, 0x74, 0x22, 0xae, 0x0e, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x69, 0x74, 0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x61, 0x70,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70,
	0x12, 0x24, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d,
	0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x2a,
	0x0a, 0x04, 0x53, 0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x7d, 0x0a, 0x03, 0x41, 0x70,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x1a, 0xaf, 0x01, 0x0a, 0x03, 0x49, 0x6d,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66,
	0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46,
	0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x1a, 0xa1, 0x01, 0x0a, 0x06,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x1a,
	0xc6, 0x07, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x24, 0x0a, 0x03, 0x67, 0x65,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f,
	0x73, 0x76, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x70, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12,
	0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f,
	0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x68, 0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x68, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x29, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x6c, 0x6f, 0x6e, 0x1a, 0x30, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x61, 0x67, 0x65, 0x22, 0x94, 0x07, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x03, 0x62, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x73, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x1a, 0xf8, 0x05, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x6d, 0x67, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x69, 0x6d, 0x67, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6d, 0x67, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x69, 0x6d, 0x67, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6d,
	0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x67,
	0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c,
	0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72,
	0x6c, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x6c, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x6c, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x77, 0x61, 0x6b, 0x65, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x11, 0x77, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x42, 0x31, 0x0a, 0x1b,
	0x63, 0x6f, 0x6d, 0x2e, 0x62, 0x6a, 0x35, 0x38, 0x2e, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x42, 0x08, 0x4a, 0x78, 0x65,
	0x64, 0x74, 0x53, 0x73, 0x70, 0x5a, 0x08, 0x2e, 0x2e, 0x2f, 0x6a, 0x78, 0x65, 0x64, 0x74, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jxedt_proto_rawDescOnce sync.Once
	file_jxedt_proto_rawDescData = file_jxedt_proto_rawDesc
)

func file_jxedt_proto_rawDescGZIP() []byte {
	file_jxedt_proto_rawDescOnce.Do(func() {
		file_jxedt_proto_rawDescData = protoimpl.X.CompressGZIP(file_jxedt_proto_rawDescData)
	})
	return file_jxedt_proto_rawDescData
}

var file_jxedt_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_jxedt_proto_goTypes = []interface{}{
	(*Request)(nil),        // 0: jxedt.Request
	(*Response)(nil),       // 1: jxedt.Response
	(*Request_Site)(nil),   // 2: jxedt.Request.Site
	(*Request_App)(nil),    // 3: jxedt.Request.App
	(*Request_Imp)(nil),    // 4: jxedt.Request.Imp
	(*Request_Banner)(nil), // 5: jxedt.Request.Banner
	(*Request_Device)(nil), // 6: jxedt.Request.Device
	(*Request_Geo)(nil),    // 7: jxedt.Request.Geo
	(*Request_User)(nil),   // 8: jxedt.Request.User
	(*Response_Bid)(nil),   // 9: jxedt.Response.Bid
}
var file_jxedt_proto_depIdxs = []int32{
	2, // 0: jxedt.Request.site:type_name -> jxedt.Request.Site
	3, // 1: jxedt.Request.app:type_name -> jxedt.Request.App
	4, // 2: jxedt.Request.imp:type_name -> jxedt.Request.Imp
	6, // 3: jxedt.Request.device:type_name -> jxedt.Request.Device
	8, // 4: jxedt.Request.user:type_name -> jxedt.Request.User
	9, // 5: jxedt.Response.bid:type_name -> jxedt.Response.Bid
	5, // 6: jxedt.Request.Imp.banner:type_name -> jxedt.Request.Banner
	7, // 7: jxedt.Request.Device.geo:type_name -> jxedt.Request.Geo
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_jxedt_proto_init() }
func file_jxedt_proto_init() {
	if File_jxedt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_jxedt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jxedt_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jxedt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_jxedt_proto_goTypes,
		DependencyIndexes: file_jxedt_proto_depIdxs,
		MessageInfos:      file_jxedt_proto_msgTypes,
	}.Build()
	File_jxedt_proto = out.File
	file_jxedt_proto_rawDesc = nil
	file_jxedt_proto_goTypes = nil
	file_jxedt_proto_depIdxs = nil
}
