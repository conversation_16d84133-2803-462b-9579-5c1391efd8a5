syntax = "proto3"; //指定使用proto3语法。没指定编译器默认会使用proto2
option java_package = "com.bj58.jxedt.adx.protobuf"; //包名
option java_outer_classname = "JxedtSsp"; //类名

option go_package = "mh_proxy/pb/jxedt";

package jxedt;

message Request{
  //请求唯一id
  string id = 1;
  //接口协议版本
  string api_version = 2;
  //媒体信息
  Site   site = 3;
  //App信息
  App    app = 4;
  //广告位信息
  Imp    imp = 5;
  //设备信息
  Device device = 6;
  //用户信息
  User   user = 7;

  message Site{
    //媒体id，我方提供
    string id = 1;
    //媒体名称
    string name = 2;
  }

  message App{
    //App id，我方提供
    string          id = 1;
    //App名称
    string          name = 2;
    //App包名
    string          bundle = 3;
    //App版本
    string          app_version = 4;
    //用户标签 1（拼多多），2（淘宝），3（京东），4（美团），5（美团外卖），6（支付宝），7（饿了么），8（快手），9（快手极速版）
    repeated string user_tag = 5;
  }

  message Imp{
    //曝光唯一id，与竞价返回值对应
    string          id = 1;
    //广告位id，我方提供
    string          tag_id = 2;
    //图文广告相关信息
    Banner          banner = 3;
    //广告位底价每cpm，单位分，1000
    int32           bid_floor = 5;
    //模板类型：1：一图2：一图一文3：一图两文4：一视频5：一视频一文6：一视频两文
    repeated string template_id = 6;
    //PD合作可选id
    repeated string deals = 7;
  }

  message Banner{
    //广告位宽，像素
    int32           width = 1;
    //广告位高，像素
    int32           height = 2;
    //广告位类型  1开屏 2信息流 3banner 4插屏
    int32           pos_type = 3;
    //支持素材类型 jpg/png/gif/mp4
    repeated string material_type = 4;
    //交互类型：h5/deeplink/universalink
    repeated string interaction_type = 5;
  }

  message Device{
    //设备浏览器useragent
    string ua = 1;
    //设备地理位置
    Geo    geo = 2;
    //设备ip
    string ip = 3;
    //设备类型 0：手机，1：平板，2：PC，3：互联网电视
    int32  device_type = 4;
    //设备制造商，apple
    string make = 5;
    //设备机型，iphone7,1
    string model = 6;
    //设备系统，ios
    string os = 7;
    //设备系统版本，13
    string osv = 8;
    //设备屏幕高，像素
    int32  height = 9;
    //设备屏幕宽，像素
    int32  width = 10;
    //设备像素
    int32  ppi = 11;
    //设备运营商，0：未知，1：中国移动，2：中国电信，3：中国联通
    int32  carrier = 12;
    //设备联网方式，0：未知，1：wifi，2：2g，3：3g，4：4g，5：5g
    int32  network = 13;
    //Ios设备号，明文，明文md5二选一即可
    string idfa = 14;
    //Ios设备号，md5，明文md5二选一即可
    string idfa_md5 = 15;
    //android设备号，明文，明文md5二选一即可
    string imei = 16;
    //android设备号，md5，明文md5二选一即可
    string imei_md5 = 17;
    //android设备号，明文
    string oaid = 18;
    //android设备号，明文
    string android_id = 19;
    //设备mac地址
    string mac = 20;
    //ios设备系统启动时间，1595214620.383940
    string boot_time = 21;
    //ios设备系统更新时间，1595214620.383940
    string update_time = 22;
    //ios国家代码，使用 ISO-3166-1-alpha-2
    string country = 23;
    //ios设备语言，使用 ISO-3166-1-alpha-2
    string language = 24;
    //ios设备名称md5，小写
    string phone_name = 25;
    //ios设备最大内存，KB，3955589120
    string memory_size = 26;
    //ios设备最大容量，KB，63900340224
    string disk_size = 27;
    //ios设备型号码，D22AP
    string model_code = 28;
    //ios设备时区，2880
    string timezone = 29;
    //取原值回传 iOS：1623815045.970028；Android：ec7f4f33-411a-47bc-8067-744a4e7e0723
    string boot_mark = 30;
    //取原值回传 iOS：1581141691.570419583 Android：1004697.709999999
    string update_mark = 31;
    // ios系统初始化时间
    string birth_time = 32;
    // ag版本
    string ag_version = 34;
    // hms版本
    string hms_version = 35;
    // 拼多多paid
    string paid = 36;
    // 设备caid值
    string caid = 37;
    // caid版本
    string caid_version = 38;
  }

  message Geo{
    //纬度，精确到小数点后6位
    double lat = 1;
    //经度，精确到小数点后6位
    double lon = 2;
  }

  message User{
    //用户性别，0未知，1男性，2女性
    int32  gender = 1;
    //用户年龄，18或{17-20}
    string age = 2;
  }
}

message Response{
  //对应请求唯一id，与请求中id相同
  string id = 2;
  //唯一竞价id，由竞价者返回
  string bid_id = 3;
  //广告内容对象
  Bid    bid = 4;
  //广告应答状态码，表示有无广告返回给媒体以及其他响应状态
  int32  code = 5;
  //广告应答描述
  string msg = 6;
  //DSP名称
  string dsp_name = 7;

  message Bid{
    //对应请求imp中id
    string          imp_id = 1;
    //出价，每cpm价格，单位分
    int32           price = 2;
    //获胜通知地址，AES加密加密规则以我方为准，需支持宏替换
    string          nurl = 3;
    //广告创意id
    string          crid = 4;
    //广告宽度
    int32           img_width = 5;
    //广告高度
    int32           img_height = 6;
    //主素材地址
    string          img_url = 7;
    //封面地址
    string          cover_url = 8;
    //视频时长，s
    int32           video_duration = 9;
    //视频码率，Kbps
    int32           video_bitrate = 10;
    //视频大小，KB
    float           video_size = 11;
    //广告曝光监测地址，支持多个
    repeated string impression_tracking_url = 12;
    //广告点击监测地址，支持多个
    repeated string click_tracking_url = 13;
    //广告落地页，执行优先级最低
    string          landing_page_url = 14;
    //Deeplink地址，执行优先级低于ulkurl
    string          deeplink_url = 15;
    //Universallink地址，仅ios，执行优先级最高
    string          ulk_url = 16;
    //推广应用包名
    string          app_package_name = 17;
    //推广应用名称
    string          app_name = 18;
    //图标地址
    string          icon_url = 19;
    //标题
    string          title = 20;
    //描述
    string          desc = 21;
    //模板类型：1：一图2：一图一文3：一图两文4：一视频5：一视频一文6：一视频两文
    string          template_id = 22;
    //唤醒监测地址，支持多个
    repeated string wake_up_tracking_url = 23;
    //PD合作必填
    string          deal_id = 24;

  }
}