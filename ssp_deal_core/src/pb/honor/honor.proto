syntax = "proto3";

package honor;

option go_package = "mh_proxy/pb/honor";


option java_multiple_files = true;
// option java_package = "com.honor.pb";
option java_package = "com.hihonor.ad.platform.common.dubbo.pd.openrtb";

message BidRequest {
    // required 全局唯一的竞价请求ID。最长支持128个字符，例如：498fa2e9acdb4566ad7ff8b0ae13f5f7
    string id = 1;

    // required 曝光或展示信息，Imp对象数组，应至少包含一个Imp对象
    repeated Imp imp = 2;

    // 移动应用详情信息
    App app = 3;

    // 移动应用详情信息
    Device device = 4;

    // default 0 标识是否为测试模式，为测试模式时，不进行计费。定义如下：0：live ,目前固定为0， 1：test mode
    int32 test = 5;

    // default 1 竞标类型，定义如下：1：First Price，当前固定为1
    int32 at = 6;

    // required 交易最大超时时间（包含网络延迟时间），单位为毫秒，默认值为150毫秒
    int32 tmax = 7;

    // required 当前固定为：["CNY"]，国内仅支持CNY
    repeated string cur = 8;

    BidRequestExt ext = 9;

    message App {
        // required 应用ID，最长支持128个字符，当前使用媒体id填充
        string id = 1;

        // required 媒体名称，例如：应用市场
        string name = 2;

        // required 媒体包名
        string bundle = 3;

        // required 媒体应用版本号
        string ver = 4;
    }

    message BidRequestExt {
        // 设备已安装应用包名ID列表，仅支持授权DSP
        repeated int64 ail = 1;
        // 对应一级流量来源id
        string traffic_tag_l1_id = 2;
    }

    message Device {
        // required 浏览器User Agent，明文
        string ua = 1;

        // required 地理位置信息
        Geo geo = 2;

        // required 用户是否允许广告跟踪（Limit Ad Tracking），定义如下：0：用户允许跟踪，1：用户不允许跟踪。默认值为0，如果用户不允许跟踪，DSP不能对用户进行画像及个性推荐
        int32 lmt = 3;

        // required 用户IPv4地址，例如：***************
        string ip = 4;

        // required 设备类型
        int32 devicetype = 5;

        // required 设备厂商，例如：honor
        string make = 6;

        // required 设备型号 ，例如NEM-AL10
        string model = 7;

        // required 操作系统，例如：android
        string os = 8;

        // required 设备操作系统版本号，例如12
        string osv = 9;

        // 设备硬件版本号，当前未传值
        string hwv = 10;

        // required 设备屏幕高度，单位为像素
        int32 h = 11;

        // required 设备屏幕宽度，单位为像素
        int32 w = 12;

        // required 设备屏幕尺寸，单位是像素/英寸
        int32 ppi = 13;

        // 设备物理像素与DIPs（Device Independent Pixels）比率，当前未传值
        float pxratio = 14;

        // 是否支持JavaScript，0：No，1：Yes；当前未传值
        int32 js = 15;

        // required 浏览器使用的语言，采用/ISO 639-1-alpha-2语言表
        string language = 16;

        // required 网络连接类型
        int32 connectiontype = 17;

        // required 广告标识OAID
        string ifa = 18;

        // OpenRTB标准定义扩展字段,当前未使用
        map<string, string> ext = 19;

        string imei = 20;

        int32 deviceflag = 21;
    }

    message Geo {
        // 国家或地区码，采用ISO-3166-1-alpha-3
        string country = 1;

        // Local time as the number +/- of minutes from UTC.，当前为空
        int32 utcoffset = 2;
    }

    message Imp {
        // required 请求上下文中曝光/展示的唯一标识符，最长支持128个字符。例如：92a1ff9c535f4d448d16c2504b111b7e
        string id = 1;

        // 包含对本Imp对象有效的私有市场交易内容
        Pmp pmp = 2;

        Banner banner = 3;

        // banner广告支持多规格时必传
        repeated Banner banner_list = 4;

        // 由于暂未实现OpenRTB Dynamic Native Ads API Specification，本字段暂为固定内容：{"request": "{}"}
        NativeObj native_req = 5;

        // SDK或呈现广告的应用名称，当前未使用
        string displaymanager = 6;

        // SDK或呈现广告的应用版本，当前未使用
        string displaymanagerver = 7;

        // default 0 . 0：非插页式广告；1：插页式或全屏广告； 该字段当前未使用
        int32 instl = 8;

        // required 广告版位标识
        string tagid = 9;

        // default 0；required 曝光/展示的最低出价（CPM），单位“元”，精度：小数点后两位
        float bidfloor = 10;

        // required default “CNY”
        string bidfloorcur = 11;

        // required
        ImpExt ext = 12;

        // 搜索关键词，搜索广告时有值
        string keywords = 13;

        string industry_id = 14;

        int32 count = 15;
    }

    message Pmp {
        // default 0 直接交易对象中指定席位的拍卖资格指 0：接受所有出价 ，1：出价仅限于指定的交易
        int32 private_auction = 1;

        // 用于传递展示/曝光的特定交易信息
        repeated Deal deals = 2;

        // OpenRTB标准定义扩展字段,当前未使用
        map<string, string> ext = 5;
    }

    message Deal {
        // 直接交易的唯一标识
        string id = 1;

        // default 0; required 曝光/展示的最低出价（CPM），单位“元”，精度：小数点后两位
        float bidfloor = 2;

        // default “CNY”
        string bidfloorcur = 3;

        // required 竞价类型，定义如下 1：First Price，2：Second Price Plus，3：Contact Price
        int32 at = 4;

        // OpenRTB标准定义扩展字段,当前未使用
        map<string, string> ext = 5;

        //合约类型：1:PDB 2:PD 3:CPT
        int32 transactionType = 6;
    }

    message Banner {
        // required 图片宽度，单位：像素
        int32 w = 1;

        // required 图片高度，单位：像素
        int32 h = 2;

        // required 支持的mime-type，支持格式如下：1. image/jpeg 2. image/png
        repeated string mimes = 3;
    }

    message ImpExt {
        // required 广告类型，定义如下：0：原生广告，1：图片广告，2：信息流广告，3：开屏广告，4：激励视频广告，5:  插屏广告
        int32 ad_type = 1;

        // required 行业白名单列表（见行业类别附录）
        repeated string catwlist = 2;

        // required 当前支持的推广目标定义如下，定义如下：0：应用下载，1：网页推广，2：应用直达，3：小程序推广，4：预约推广，103：快应用推广
        repeated int32 promotion_purpose = 3;

        // required 支持的创意模板ID
        repeated int32 template_id = 4;

        // 宿主应用包名
        optional string ui_pkg_name = 5;

        // 宿主应用商店分类
        optional string ui_cat = 6;

        // 应用商店分类id ，商店分类页使用
        optional string app_cat = 7;
    }

    message NativeObj {
        string request = 1;
    }
}


message BidResponse {
    // required 全局唯一的竞价请求ID。最长支持128个字符，例如：498fa2e9acdb4566ad7ff8b0ae13f5f7
    string id = 1;

    // seatbid对象（见6.1.9）数组，如果出价，则对象数组需大于等于1
    repeated SeatBid seatbid = 2;

    // Bidder生成的响应标识，用户日志记录及跟踪
    string bidid = 3;

    // default “CNY” 竞价使用的货币代码,仅支持CNY
    string cur = 4;

    // 不参与竞价的原因（见6.7）
    int32 nbr = 5;

    // OpenRTB标准定义扩展字段,当前未使用
    map<string, string> ext = 6;

    message SeatBid {
        // Bid对象数组（大于等于1），每一个Bid对象都应关联到一个Impression，允许多个Bid对象关联到相同Impression，详见（见6.1.10）
        repeated Bid bid = 1;

        // 代表广告主出价的买方席位（例如：DSP或代理机构）的标识，暂未使用，可以填写固定值
        string seat = 2;

        // 0：可以独立竟胜Impression；当前固定为0
        int32 group = 3;

        // OpenRTB标准定义扩展字段,当前未使用
        map<string, string> ext = 9;
    }

    message Bid {
        // required Bidder生成的竞价标识，用于日志/跟踪
        string id = 1;

        // required 与竞价请求Imp中的id对应
        string impid = 2;

        // required 广告主出价，采用CPM计费，单位“元”，精度：小数点后两位,例如：10.33
        float price = 3;

        // required 竞价成功时，Ad Exchange回调通知Bidder的URL，Ad Exchange将通过GET方式调用该URL，需提供HTTPS地址。当前支持的宏信息见（6.6）。
        string nurl = 4;

        // 竞价失败时，Ad Exchange回调通知Bidder的URL，Ad Exchange将通过GET方式调用该URL，需提供HTTPS地址。当前支持的宏信息见（错误!未找到引用源。）。
        optional string lurl = 5;

        // 表示广告活动内容的图片地址，不允许缓存，图片广告时取该值作为广告图片url
        string iurl = 6;

        // 取值：png， jpg， jpeg ,标识iurl 对应的图片的类型，iurl 不为空时必传
        string image_type = 7;

        // DSP代理的活动ID
        string cid = 8;

        // required DSP代理的创意ID,必填
        string crid = 9;

        // required 广告创意的内容类别（见行业类别附录）
        repeated string cat = 10;

        // required 广告创意的语言，采用alpha-2/ISO 639-1语言表，例如：zh、en，
        string language = 11;

        // 如果竞价是Private Marketplace直接交易，则需从竞价请求中引用deal.id
        string dealid = 12;

        // iurl 图片的宽，单位：像素，iurl 不为空时必传
        int32 w = 13;

        // iurl 图片对应的高，单位：像素，iurl 不为空时必传
        int32 h = 14;

        // 素材和监测上报数据
        BidExt ext = 15;

        // 应用商店分类id ，商店分类页使用
        optional string app_cat = 16;

    }

    message BidExt {
        // required 事件监测
        EventTracker event_tracker = 1;

        // required Creative扩展内容
        ExtCreative ext_creative = 2;
    }

    message EventTracker {
        // 展示曝光跟踪URL，支持宏替换
        repeated string impression_urls = 1;

        // 点击跟踪URL，支持宏替换
        repeated string click_urls = 2;

        // 下载跟踪URL，支持宏替换
        repeated string download_urls = 3;

        // 开始下载上报链接集合，支持宏替换
        repeated string start_download_urls = 4;

        // 下载成功上报链接集合，支持宏替换
        repeated string success_download_urls = 5;

        // 下载失败上报链接集合，支持宏替换
        repeated string failed_download_urls = 6;

        // 预约推广跟踪URL，支持宏替换
        repeated string reservation_urls = 7;
    }

    message ExtCreative {
        // 应用包名，最大长度256
        string pkg_name = 1;

        // deeplink链接，最大长度4096
        string deeplink = 2;

        // 备用h5link链接、网页推广链接（必须为HTTPS），最大长度4096
        string h5link = 3;

        // 小程序类型，定义如下：1：微信小程序（当推广目的为小程序推广时，本字段为必填）
        int32 mini_program_type = 4;

        // 微信小程序原始ID，最大长度256
        string app_id = 5;

        // 小程序路径Path（必填：微信小程序），最大长度4096
        string path = 6;

        // 应用名称
        string app_name = 7;

        // 应用包下载地址（必须为HTTPS），最大长度4096
        string download_url = 8;

        // 官网地址，最大长度512
        string official_website = 9;

        // 应用包大小（单位：字节），非商店渠道包应用下载必传，值要大于0
        int64 pkg_size = 10;

        // 应用包版本，最大长度64
        string pkg_version = 11;

        // 标题
        string title = 12;

        // 广告描述
        string description = 13;

        // 应用详情页URL（必须为：HTTPS），最大长度4096
        string target_url = 14;

        // 开发者主体名称，适用推广目标：应用下载
        string developer_name = 15;

        // 应用权限说明URL（必须为：HTTPS），需详细列出应用权限信息，最大长度2048
        string permissions_url = 16;

        // 应用隐私政策说明（必须为：HTTPS），最大长度2048
        string privacy_agreement_url = 17;

        // 当前支持的推广目标定义如下：0：应用下载，1：网页推广，2：应用直达，3：小程序推广，4:  预约推广，103：快应用推广
        int32 promotion_purpose = 18;

        // 品牌名称
        string brand_name = 19;

        // Logo 信息
        ImageObject logo_info = 20;

        // Icon 信息
        ImageObject icon_info = 21;

        // 适用视频广告
        VideoObject video = 22;

        // required 创意模板id
        string template_id = 23;

        // required 按钮文案，（字数上限4）
        string button_text = 24;

        // 渠道标识 18：应用宝（DSP） 不传或为空值，默认取全渠道
        optional int32 store_channel = 25;

        // 下载包渠道id
        optional string sub_channel = 26;

        // 多图广告时，填充多图素材信息
        repeated ImageObject image_list = 27;

        // 游戏dsp归因参数，gm 开头
        optional string channel_info = 28;

        // 游戏dsp扩展归因参数
        optional string extra_info = 29;
    }

    message VideoObject {
        // 视频url，（必须为：HTTPS），最大长度4096
        string url = 1;

        // 视频宽度 单位：像素
        int32 w = 2;

        // 视频高度 单位：像素
        int32 h = 3;

        // 视频格式，定义如下：mp4
        string video_type = 4;

        // 视频时长，单位秒
        int32 video_duration = 5;

        // 视频大小，单位byte
        int32 size = 6;

        // 视频封面图
        ImageObject title_screen = 7;

    }

    message ImageObject {
        // 图像url ，（必须为：HTTPS），最大长度4096，支持png,jpeg,jpg
        string url = 1;

        // 图像宽度 单位：像素
        int32 w = 2;

        // 图像宽度 单位：像素
        int32 h = 3;

        // 取值：png,jpeg,jpg
        string image_type = 4;
    }
}
