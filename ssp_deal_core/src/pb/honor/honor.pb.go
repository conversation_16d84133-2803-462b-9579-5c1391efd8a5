// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: honor.proto

package honor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 全局唯一的竞价请求ID。最长支持128个字符，例如：498fa2e9acdb4566ad7ff8b0ae13f5f7
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// required 曝光或展示信息，Imp对象数组，应至少包含一个Imp对象
	Imp []*BidRequest_Imp `protobuf:"bytes,2,rep,name=imp,proto3" json:"imp,omitempty"`
	// 移动应用详情信息
	App *BidRequest_App `protobuf:"bytes,3,opt,name=app,proto3" json:"app,omitempty"`
	// 移动应用详情信息
	Device *BidRequest_Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	// default 0 标识是否为测试模式，为测试模式时，不进行计费。定义如下：0：live ,目前固定为0， 1：test mode
	Test int32 `protobuf:"varint,5,opt,name=test,proto3" json:"test,omitempty"`
	// default 1 竞标类型，定义如下：1：First Price，当前固定为1
	At int32 `protobuf:"varint,6,opt,name=at,proto3" json:"at,omitempty"`
	// required 交易最大超时时间（包含网络延迟时间），单位为毫秒，默认值为150毫秒
	Tmax int32 `protobuf:"varint,7,opt,name=tmax,proto3" json:"tmax,omitempty"`
	// required 当前固定为：["CNY"]，国内仅支持CNY
	Cur []string                  `protobuf:"bytes,8,rep,name=cur,proto3" json:"cur,omitempty"`
	Ext *BidRequest_BidRequestExt `protobuf:"bytes,9,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetTest() int32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *BidRequest) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *BidRequest) GetTmax() int32 {
	if x != nil {
		return x.Tmax
	}
	return 0
}

func (x *BidRequest) GetCur() []string {
	if x != nil {
		return x.Cur
	}
	return nil
}

func (x *BidRequest) GetExt() *BidRequest_BidRequestExt {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 全局唯一的竞价请求ID。最长支持128个字符，例如：498fa2e9acdb4566ad7ff8b0ae13f5f7
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// seatbid对象（见6.1.9）数组，如果出价，则对象数组需大于等于1
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,2,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
	// Bidder生成的响应标识，用户日志记录及跟踪
	Bidid string `protobuf:"bytes,3,opt,name=bidid,proto3" json:"bidid,omitempty"`
	// default “CNY” 竞价使用的货币代码,仅支持CNY
	Cur string `protobuf:"bytes,4,opt,name=cur,proto3" json:"cur,omitempty"`
	// 不参与竞价的原因（见6.7）
	Nbr int32 `protobuf:"varint,5,opt,name=nbr,proto3" json:"nbr,omitempty"`
	// OpenRTB标准定义扩展字段,当前未使用
	Ext map[string]string `protobuf:"bytes,6,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

func (x *BidResponse) GetCur() string {
	if x != nil {
		return x.Cur
	}
	return ""
}

func (x *BidResponse) GetNbr() int32 {
	if x != nil {
		return x.Nbr
	}
	return 0
}

func (x *BidResponse) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 应用ID，最长支持128个字符，当前使用媒体id填充
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// required 媒体名称，例如：应用市场
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// required 媒体包名
	Bundle string `protobuf:"bytes,3,opt,name=bundle,proto3" json:"bundle,omitempty"`
	// required 媒体应用版本号
	Ver string `protobuf:"bytes,4,opt,name=ver,proto3" json:"ver,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_App) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

type BidRequest_BidRequestExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备已安装应用包名ID列表，仅支持授权DSP
	Ail []int64 `protobuf:"varint,1,rep,packed,name=ail,proto3" json:"ail,omitempty"`
	// 对应一级流量来源id
	TrafficTagL1Id string `protobuf:"bytes,2,opt,name=traffic_tag_l1_id,json=trafficTagL1Id,proto3" json:"traffic_tag_l1_id,omitempty"`
}

func (x *BidRequest_BidRequestExt) Reset() {
	*x = BidRequest_BidRequestExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_BidRequestExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_BidRequestExt) ProtoMessage() {}

func (x *BidRequest_BidRequestExt) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_BidRequestExt.ProtoReflect.Descriptor instead.
func (*BidRequest_BidRequestExt) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_BidRequestExt) GetAil() []int64 {
	if x != nil {
		return x.Ail
	}
	return nil
}

func (x *BidRequest_BidRequestExt) GetTrafficTagL1Id() string {
	if x != nil {
		return x.TrafficTagL1Id
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 浏览器User Agent，明文
	Ua string `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	// required 地理位置信息
	Geo *BidRequest_Geo `protobuf:"bytes,2,opt,name=geo,proto3" json:"geo,omitempty"`
	// required 用户是否允许广告跟踪（Limit Ad Tracking），定义如下：0：用户允许跟踪，1：用户不允许跟踪。默认值为0，如果用户不允许跟踪，DSP不能对用户进行画像及个性推荐
	Lmt int32 `protobuf:"varint,3,opt,name=lmt,proto3" json:"lmt,omitempty"`
	// required 用户IPv4地址，例如：***************
	Ip string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip,omitempty"`
	// required 设备类型
	Devicetype int32 `protobuf:"varint,5,opt,name=devicetype,proto3" json:"devicetype,omitempty"`
	// required 设备厂商，例如：honor
	Make string `protobuf:"bytes,6,opt,name=make,proto3" json:"make,omitempty"`
	// required 设备型号 ，例如NEM-AL10
	Model string `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`
	// required 操作系统，例如：android
	Os string `protobuf:"bytes,8,opt,name=os,proto3" json:"os,omitempty"`
	// required 设备操作系统版本号，例如12
	Osv string `protobuf:"bytes,9,opt,name=osv,proto3" json:"osv,omitempty"`
	// 设备硬件版本号，当前未传值
	Hwv string `protobuf:"bytes,10,opt,name=hwv,proto3" json:"hwv,omitempty"`
	// required 设备屏幕高度，单位为像素
	H int32 `protobuf:"varint,11,opt,name=h,proto3" json:"h,omitempty"`
	// required 设备屏幕宽度，单位为像素
	W int32 `protobuf:"varint,12,opt,name=w,proto3" json:"w,omitempty"`
	// required 设备屏幕尺寸，单位是像素/英寸
	Ppi int32 `protobuf:"varint,13,opt,name=ppi,proto3" json:"ppi,omitempty"`
	// 设备物理像素与DIPs（Device Independent Pixels）比率，当前未传值
	Pxratio float32 `protobuf:"fixed32,14,opt,name=pxratio,proto3" json:"pxratio,omitempty"`
	// 是否支持JavaScript，0：No，1：Yes；当前未传值
	Js int32 `protobuf:"varint,15,opt,name=js,proto3" json:"js,omitempty"`
	// required 浏览器使用的语言，采用/ISO 639-1-alpha-2语言表
	Language string `protobuf:"bytes,16,opt,name=language,proto3" json:"language,omitempty"`
	// required 网络连接类型
	Connectiontype int32 `protobuf:"varint,17,opt,name=connectiontype,proto3" json:"connectiontype,omitempty"`
	// required 广告标识OAID
	Ifa string `protobuf:"bytes,18,opt,name=ifa,proto3" json:"ifa,omitempty"`
	// OpenRTB标准定义扩展字段,当前未使用
	Ext        map[string]string `protobuf:"bytes,19,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Imei       string            `protobuf:"bytes,20,opt,name=imei,proto3" json:"imei,omitempty"`
	Deviceflag int32             `protobuf:"varint,21,opt,name=deviceflag,proto3" json:"deviceflag,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetLmt() int32 {
	if x != nil {
		return x.Lmt
	}
	return 0
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetDevicetype() int32 {
	if x != nil {
		return x.Devicetype
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil {
		return x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetPxratio() float32 {
	if x != nil {
		return x.Pxratio
	}
	return 0
}

func (x *BidRequest_Device) GetJs() int32 {
	if x != nil {
		return x.Js
	}
	return 0
}

func (x *BidRequest_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *BidRequest_Device) GetConnectiontype() int32 {
	if x != nil {
		return x.Connectiontype
	}
	return 0
}

func (x *BidRequest_Device) GetIfa() string {
	if x != nil {
		return x.Ifa
	}
	return ""
}

func (x *BidRequest_Device) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceflag() int32 {
	if x != nil {
		return x.Deviceflag
	}
	return 0
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 国家或地区码，采用ISO-3166-1-alpha-3
	Country string `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	// Local time as the number +/- of minutes from UTC.，当前为空
	Utcoffset int32 `protobuf:"varint,2,opt,name=utcoffset,proto3" json:"utcoffset,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BidRequest_Geo) GetUtcoffset() int32 {
	if x != nil {
		return x.Utcoffset
	}
	return 0
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 请求上下文中曝光/展示的唯一标识符，最长支持128个字符。例如：92a1ff9c535f4d448d16c2504b111b7e
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 包含对本Imp对象有效的私有市场交易内容
	Pmp    *BidRequest_Pmp    `protobuf:"bytes,2,opt,name=pmp,proto3" json:"pmp,omitempty"`
	Banner *BidRequest_Banner `protobuf:"bytes,3,opt,name=banner,proto3" json:"banner,omitempty"`
	// banner广告支持多规格时必传
	BannerList []*BidRequest_Banner `protobuf:"bytes,4,rep,name=banner_list,json=bannerList,proto3" json:"banner_list,omitempty"`
	// 由于暂未实现OpenRTB Dynamic Native Ads API Specification，本字段暂为固定内容：{"request": "{}"}
	NativeReq *BidRequest_NativeObj `protobuf:"bytes,5,opt,name=native_req,json=nativeReq,proto3" json:"native_req,omitempty"`
	// SDK或呈现广告的应用名称，当前未使用
	Displaymanager string `protobuf:"bytes,6,opt,name=displaymanager,proto3" json:"displaymanager,omitempty"`
	// SDK或呈现广告的应用版本，当前未使用
	Displaymanagerver string `protobuf:"bytes,7,opt,name=displaymanagerver,proto3" json:"displaymanagerver,omitempty"`
	// default 0 . 0：非插页式广告；1：插页式或全屏广告； 该字段当前未使用
	Instl int32 `protobuf:"varint,8,opt,name=instl,proto3" json:"instl,omitempty"`
	// required 广告版位标识
	Tagid string `protobuf:"bytes,9,opt,name=tagid,proto3" json:"tagid,omitempty"`
	// default 0；required 曝光/展示的最低出价（CPM），单位“元”，精度：小数点后两位
	Bidfloor float32 `protobuf:"fixed32,10,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	// required default “CNY”
	Bidfloorcur string `protobuf:"bytes,11,opt,name=bidfloorcur,proto3" json:"bidfloorcur,omitempty"`
	// required
	Ext *BidRequest_ImpExt `protobuf:"bytes,12,opt,name=ext,proto3" json:"ext,omitempty"`
	// 搜索关键词，搜索广告时有值
	Keywords   string `protobuf:"bytes,13,opt,name=keywords,proto3" json:"keywords,omitempty"`
	IndustryId string `protobuf:"bytes,14,opt,name=industry_id,json=industryId,proto3" json:"industry_id,omitempty"`
	Count      int32  `protobuf:"varint,15,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetPmp() *BidRequest_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_Imp) GetBanner() *BidRequest_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *BidRequest_Imp) GetBannerList() []*BidRequest_Banner {
	if x != nil {
		return x.BannerList
	}
	return nil
}

func (x *BidRequest_Imp) GetNativeReq() *BidRequest_NativeObj {
	if x != nil {
		return x.NativeReq
	}
	return nil
}

func (x *BidRequest_Imp) GetDisplaymanager() string {
	if x != nil {
		return x.Displaymanager
	}
	return ""
}

func (x *BidRequest_Imp) GetDisplaymanagerver() string {
	if x != nil {
		return x.Displaymanagerver
	}
	return ""
}

func (x *BidRequest_Imp) GetInstl() int32 {
	if x != nil {
		return x.Instl
	}
	return 0
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil {
		return x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetBidfloor() float32 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetBidfloorcur() string {
	if x != nil {
		return x.Bidfloorcur
	}
	return ""
}

func (x *BidRequest_Imp) GetExt() *BidRequest_ImpExt {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidRequest_Imp) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *BidRequest_Imp) GetIndustryId() string {
	if x != nil {
		return x.IndustryId
	}
	return ""
}

func (x *BidRequest_Imp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type BidRequest_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// default 0 直接交易对象中指定席位的拍卖资格指 0：接受所有出价 ，1：出价仅限于指定的交易
	PrivateAuction int32 `protobuf:"varint,1,opt,name=private_auction,json=privateAuction,proto3" json:"private_auction,omitempty"`
	// 用于传递展示/曝光的特定交易信息
	Deals []*BidRequest_Deal `protobuf:"bytes,2,rep,name=deals,proto3" json:"deals,omitempty"`
	// OpenRTB标准定义扩展字段,当前未使用
	Ext map[string]string `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BidRequest_Pmp) Reset() {
	*x = BidRequest_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Pmp) ProtoMessage() {}

func (x *BidRequest_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Pmp) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_Pmp) GetPrivateAuction() int32 {
	if x != nil {
		return x.PrivateAuction
	}
	return 0
}

func (x *BidRequest_Pmp) GetDeals() []*BidRequest_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *BidRequest_Pmp) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidRequest_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 直接交易的唯一标识
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// default 0; required 曝光/展示的最低出价（CPM），单位“元”，精度：小数点后两位
	Bidfloor float32 `protobuf:"fixed32,2,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	// default “CNY”
	Bidfloorcur string `protobuf:"bytes,3,opt,name=bidfloorcur,proto3" json:"bidfloorcur,omitempty"`
	// required 竞价类型，定义如下 1：First Price，2：Second Price Plus，3：Contact Price
	At int32 `protobuf:"varint,4,opt,name=at,proto3" json:"at,omitempty"`
	// OpenRTB标准定义扩展字段,当前未使用
	Ext map[string]string `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 合约类型：1:PDB 2:PD 3:CPT
	TransactionType int32 `protobuf:"varint,6,opt,name=transactionType,proto3" json:"transactionType,omitempty"`
}

func (x *BidRequest_Deal) Reset() {
	*x = BidRequest_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Deal) ProtoMessage() {}

func (x *BidRequest_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Deal) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Deal) GetBidfloor() float32 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Deal) GetBidfloorcur() string {
	if x != nil {
		return x.Bidfloorcur
	}
	return ""
}

func (x *BidRequest_Deal) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *BidRequest_Deal) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidRequest_Deal) GetTransactionType() int32 {
	if x != nil {
		return x.TransactionType
	}
	return 0
}

type BidRequest_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 图片宽度，单位：像素
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	// required 图片高度，单位：像素
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	// required 支持的mime-type，支持格式如下：1. image/jpeg 2. image/png
	Mimes []string `protobuf:"bytes,3,rep,name=mimes,proto3" json:"mimes,omitempty"`
}

func (x *BidRequest_Banner) Reset() {
	*x = BidRequest_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Banner) ProtoMessage() {}

func (x *BidRequest_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Banner.ProtoReflect.Descriptor instead.
func (*BidRequest_Banner) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 7}
}

func (x *BidRequest_Banner) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Banner) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Banner) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

type BidRequest_ImpExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 广告类型，定义如下：0：原生广告，1：图片广告，2：信息流广告，3：开屏广告，4：激励视频广告，5:  插屏广告
	AdType int32 `protobuf:"varint,1,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`
	// required 行业白名单列表（见行业类别附录）
	Catwlist []string `protobuf:"bytes,2,rep,name=catwlist,proto3" json:"catwlist,omitempty"`
	// required 当前支持的推广目标定义如下，定义如下：0：应用下载，1：网页推广，2：应用直达，3：小程序推广，4：预约推广，103：快应用推广
	PromotionPurpose []int32 `protobuf:"varint,3,rep,packed,name=promotion_purpose,json=promotionPurpose,proto3" json:"promotion_purpose,omitempty"`
	// required 支持的创意模板ID
	TemplateId []int32 `protobuf:"varint,4,rep,packed,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 宿主应用包名
	UiPkgName *string `protobuf:"bytes,5,opt,name=ui_pkg_name,json=uiPkgName,proto3,oneof" json:"ui_pkg_name,omitempty"`
	// 宿主应用商店分类
	UiCat *string `protobuf:"bytes,6,opt,name=ui_cat,json=uiCat,proto3,oneof" json:"ui_cat,omitempty"`
	// 应用商店分类id ，商店分类页使用
	AppCat *string `protobuf:"bytes,7,opt,name=app_cat,json=appCat,proto3,oneof" json:"app_cat,omitempty"`
}

func (x *BidRequest_ImpExt) Reset() {
	*x = BidRequest_ImpExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_ImpExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_ImpExt) ProtoMessage() {}

func (x *BidRequest_ImpExt) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_ImpExt.ProtoReflect.Descriptor instead.
func (*BidRequest_ImpExt) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 8}
}

func (x *BidRequest_ImpExt) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidRequest_ImpExt) GetCatwlist() []string {
	if x != nil {
		return x.Catwlist
	}
	return nil
}

func (x *BidRequest_ImpExt) GetPromotionPurpose() []int32 {
	if x != nil {
		return x.PromotionPurpose
	}
	return nil
}

func (x *BidRequest_ImpExt) GetTemplateId() []int32 {
	if x != nil {
		return x.TemplateId
	}
	return nil
}

func (x *BidRequest_ImpExt) GetUiPkgName() string {
	if x != nil && x.UiPkgName != nil {
		return *x.UiPkgName
	}
	return ""
}

func (x *BidRequest_ImpExt) GetUiCat() string {
	if x != nil && x.UiCat != nil {
		return *x.UiCat
	}
	return ""
}

func (x *BidRequest_ImpExt) GetAppCat() string {
	if x != nil && x.AppCat != nil {
		return *x.AppCat
	}
	return ""
}

type BidRequest_NativeObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request string `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
}

func (x *BidRequest_NativeObj) Reset() {
	*x = BidRequest_NativeObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_NativeObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_NativeObj) ProtoMessage() {}

func (x *BidRequest_NativeObj) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_NativeObj.ProtoReflect.Descriptor instead.
func (*BidRequest_NativeObj) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{0, 9}
}

func (x *BidRequest_NativeObj) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Bid对象数组（大于等于1），每一个Bid对象都应关联到一个Impression，允许多个Bid对象关联到相同Impression，详见（见6.1.10）
	Bid []*BidResponse_Bid `protobuf:"bytes,1,rep,name=bid,proto3" json:"bid,omitempty"`
	// 代表广告主出价的买方席位（例如：DSP或代理机构）的标识，暂未使用，可以填写固定值
	Seat string `protobuf:"bytes,2,opt,name=seat,proto3" json:"seat,omitempty"`
	// 0：可以独立竟胜Impression；当前固定为0
	Group int32 `protobuf:"varint,3,opt,name=group,proto3" json:"group,omitempty"`
	// OpenRTB标准定义扩展字段,当前未使用
	Ext map[string]string `protobuf:"bytes,9,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() string {
	if x != nil {
		return x.Seat
	}
	return ""
}

func (x *BidResponse_SeatBid) GetGroup() int32 {
	if x != nil {
		return x.Group
	}
	return 0
}

func (x *BidResponse_SeatBid) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required Bidder生成的竞价标识，用于日志/跟踪
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// required 与竞价请求Imp中的id对应
	Impid string `protobuf:"bytes,2,opt,name=impid,proto3" json:"impid,omitempty"`
	// required 广告主出价，采用CPM计费，单位“元”，精度：小数点后两位,例如：10.33
	Price float32 `protobuf:"fixed32,3,opt,name=price,proto3" json:"price,omitempty"`
	// required 竞价成功时，Ad Exchange回调通知Bidder的URL，Ad Exchange将通过GET方式调用该URL，需提供HTTPS地址。当前支持的宏信息见（6.6）。
	Nurl string `protobuf:"bytes,4,opt,name=nurl,proto3" json:"nurl,omitempty"`
	// 竞价失败时，Ad Exchange回调通知Bidder的URL，Ad Exchange将通过GET方式调用该URL，需提供HTTPS地址。当前支持的宏信息见（错误!未找到引用源。）。
	Lurl *string `protobuf:"bytes,5,opt,name=lurl,proto3,oneof" json:"lurl,omitempty"`
	// 表示广告活动内容的图片地址，不允许缓存，图片广告时取该值作为广告图片url
	Iurl string `protobuf:"bytes,6,opt,name=iurl,proto3" json:"iurl,omitempty"`
	// 取值：png， jpg， jpeg ,标识iurl 对应的图片的类型，iurl 不为空时必传
	ImageType string `protobuf:"bytes,7,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	// DSP代理的活动ID
	Cid string `protobuf:"bytes,8,opt,name=cid,proto3" json:"cid,omitempty"`
	// required DSP代理的创意ID,必填
	Crid string `protobuf:"bytes,9,opt,name=crid,proto3" json:"crid,omitempty"`
	// required 广告创意的内容类别（见行业类别附录）
	Cat []string `protobuf:"bytes,10,rep,name=cat,proto3" json:"cat,omitempty"`
	// required 广告创意的语言，采用alpha-2/ISO 639-1语言表，例如：zh、en，
	Language string `protobuf:"bytes,11,opt,name=language,proto3" json:"language,omitempty"`
	// 如果竞价是Private Marketplace直接交易，则需从竞价请求中引用deal.id
	Dealid string `protobuf:"bytes,12,opt,name=dealid,proto3" json:"dealid,omitempty"`
	// iurl 图片的宽，单位：像素，iurl 不为空时必传
	W int32 `protobuf:"varint,13,opt,name=w,proto3" json:"w,omitempty"`
	// iurl 图片对应的高，单位：像素，iurl 不为空时必传
	H int32 `protobuf:"varint,14,opt,name=h,proto3" json:"h,omitempty"`
	// 素材和监测上报数据
	Ext *BidResponse_BidExt `protobuf:"bytes,15,opt,name=ext,proto3" json:"ext,omitempty"`
	// 应用商店分类id ，商店分类页使用
	AppCat *string `protobuf:"bytes,16,opt,name=app_cat,json=appCat,proto3,oneof" json:"app_cat,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BidResponse_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_Bid) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *BidResponse_Bid) GetLurl() string {
	if x != nil && x.Lurl != nil {
		return *x.Lurl
	}
	return ""
}

func (x *BidResponse_Bid) GetIurl() string {
	if x != nil {
		return x.Iurl
	}
	return ""
}

func (x *BidResponse_Bid) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *BidResponse_Bid) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *BidResponse_Bid) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidResponse_Bid) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *BidResponse_Bid) GetDealid() string {
	if x != nil {
		return x.Dealid
	}
	return ""
}

func (x *BidResponse_Bid) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_Bid) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_Bid) GetExt() *BidResponse_BidExt {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidResponse_Bid) GetAppCat() string {
	if x != nil && x.AppCat != nil {
		return *x.AppCat
	}
	return ""
}

type BidResponse_BidExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// required 事件监测
	EventTracker *BidResponse_EventTracker `protobuf:"bytes,1,opt,name=event_tracker,json=eventTracker,proto3" json:"event_tracker,omitempty"`
	// required Creative扩展内容
	ExtCreative *BidResponse_ExtCreative `protobuf:"bytes,2,opt,name=ext_creative,json=extCreative,proto3" json:"ext_creative,omitempty"`
}

func (x *BidResponse_BidExt) Reset() {
	*x = BidResponse_BidExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_BidExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_BidExt) ProtoMessage() {}

func (x *BidResponse_BidExt) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_BidExt.ProtoReflect.Descriptor instead.
func (*BidResponse_BidExt) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 3}
}

func (x *BidResponse_BidExt) GetEventTracker() *BidResponse_EventTracker {
	if x != nil {
		return x.EventTracker
	}
	return nil
}

func (x *BidResponse_BidExt) GetExtCreative() *BidResponse_ExtCreative {
	if x != nil {
		return x.ExtCreative
	}
	return nil
}

type BidResponse_EventTracker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 展示曝光跟踪URL，支持宏替换
	ImpressionUrls []string `protobuf:"bytes,1,rep,name=impression_urls,json=impressionUrls,proto3" json:"impression_urls,omitempty"`
	// 点击跟踪URL，支持宏替换
	ClickUrls []string `protobuf:"bytes,2,rep,name=click_urls,json=clickUrls,proto3" json:"click_urls,omitempty"`
	// 下载跟踪URL，支持宏替换
	DownloadUrls []string `protobuf:"bytes,3,rep,name=download_urls,json=downloadUrls,proto3" json:"download_urls,omitempty"`
	// 开始下载上报链接集合，支持宏替换
	StartDownloadUrls []string `protobuf:"bytes,4,rep,name=start_download_urls,json=startDownloadUrls,proto3" json:"start_download_urls,omitempty"`
	// 下载成功上报链接集合，支持宏替换
	SuccessDownloadUrls []string `protobuf:"bytes,5,rep,name=success_download_urls,json=successDownloadUrls,proto3" json:"success_download_urls,omitempty"`
	// 下载失败上报链接集合，支持宏替换
	FailedDownloadUrls []string `protobuf:"bytes,6,rep,name=failed_download_urls,json=failedDownloadUrls,proto3" json:"failed_download_urls,omitempty"`
	// 预约推广跟踪URL，支持宏替换
	ReservationUrls []string `protobuf:"bytes,7,rep,name=reservation_urls,json=reservationUrls,proto3" json:"reservation_urls,omitempty"`
}

func (x *BidResponse_EventTracker) Reset() {
	*x = BidResponse_EventTracker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_EventTracker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_EventTracker) ProtoMessage() {}

func (x *BidResponse_EventTracker) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_EventTracker.ProtoReflect.Descriptor instead.
func (*BidResponse_EventTracker) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 4}
}

func (x *BidResponse_EventTracker) GetImpressionUrls() []string {
	if x != nil {
		return x.ImpressionUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetDownloadUrls() []string {
	if x != nil {
		return x.DownloadUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetStartDownloadUrls() []string {
	if x != nil {
		return x.StartDownloadUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetSuccessDownloadUrls() []string {
	if x != nil {
		return x.SuccessDownloadUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetFailedDownloadUrls() []string {
	if x != nil {
		return x.FailedDownloadUrls
	}
	return nil
}

func (x *BidResponse_EventTracker) GetReservationUrls() []string {
	if x != nil {
		return x.ReservationUrls
	}
	return nil
}

type BidResponse_ExtCreative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用包名，最大长度256
	PkgName string `protobuf:"bytes,1,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name,omitempty"`
	// deeplink链接，最大长度4096
	Deeplink string `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// 备用h5link链接、网页推广链接（必须为HTTPS），最大长度4096
	H5Link string `protobuf:"bytes,3,opt,name=h5link,proto3" json:"h5link,omitempty"`
	// 小程序类型，定义如下：1：微信小程序（当推广目的为小程序推广时，本字段为必填）
	MiniProgramType int32 `protobuf:"varint,4,opt,name=mini_program_type,json=miniProgramType,proto3" json:"mini_program_type,omitempty"`
	// 微信小程序原始ID，最大长度256
	AppId string `protobuf:"bytes,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// 小程序路径Path（必填：微信小程序），最大长度4096
	Path string `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`
	// 应用名称
	AppName string `protobuf:"bytes,7,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 应用包下载地址（必须为HTTPS），最大长度4096
	DownloadUrl string `protobuf:"bytes,8,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 官网地址，最大长度512
	OfficialWebsite string `protobuf:"bytes,9,opt,name=official_website,json=officialWebsite,proto3" json:"official_website,omitempty"`
	// 应用包大小（单位：字节），非商店渠道包应用下载必传，值要大于0
	PkgSize int64 `protobuf:"varint,10,opt,name=pkg_size,json=pkgSize,proto3" json:"pkg_size,omitempty"`
	// 应用包版本，最大长度64
	PkgVersion string `protobuf:"bytes,11,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version,omitempty"`
	// 标题
	Title string `protobuf:"bytes,12,opt,name=title,proto3" json:"title,omitempty"`
	// 广告描述
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// 应用详情页URL（必须为：HTTPS），最大长度4096
	TargetUrl string `protobuf:"bytes,14,opt,name=target_url,json=targetUrl,proto3" json:"target_url,omitempty"`
	// 开发者主体名称，适用推广目标：应用下载
	DeveloperName string `protobuf:"bytes,15,opt,name=developer_name,json=developerName,proto3" json:"developer_name,omitempty"`
	// 应用权限说明URL（必须为：HTTPS），需详细列出应用权限信息，最大长度2048
	PermissionsUrl string `protobuf:"bytes,16,opt,name=permissions_url,json=permissionsUrl,proto3" json:"permissions_url,omitempty"`
	// 应用隐私政策说明（必须为：HTTPS），最大长度2048
	PrivacyAgreementUrl string `protobuf:"bytes,17,opt,name=privacy_agreement_url,json=privacyAgreementUrl,proto3" json:"privacy_agreement_url,omitempty"`
	// 当前支持的推广目标定义如下：0：应用下载，1：网页推广，2：应用直达，3：小程序推广，4:  预约推广，103：快应用推广
	PromotionPurpose int32 `protobuf:"varint,18,opt,name=promotion_purpose,json=promotionPurpose,proto3" json:"promotion_purpose,omitempty"`
	// 品牌名称
	BrandName string `protobuf:"bytes,19,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	// Logo 信息
	LogoInfo *BidResponse_ImageObject `protobuf:"bytes,20,opt,name=logo_info,json=logoInfo,proto3" json:"logo_info,omitempty"`
	// Icon 信息
	IconInfo *BidResponse_ImageObject `protobuf:"bytes,21,opt,name=icon_info,json=iconInfo,proto3" json:"icon_info,omitempty"`
	// 适用视频广告
	Video *BidResponse_VideoObject `protobuf:"bytes,22,opt,name=video,proto3" json:"video,omitempty"`
	// required 创意模板id
	TemplateId string `protobuf:"bytes,23,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// required 按钮文案，（字数上限4）
	ButtonText string `protobuf:"bytes,24,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	// 渠道标识 18：应用宝（DSP） 不传或为空值，默认取全渠道
	StoreChannel *int32 `protobuf:"varint,25,opt,name=store_channel,json=storeChannel,proto3,oneof" json:"store_channel,omitempty"`
	// 下载包渠道id
	SubChannel *string `protobuf:"bytes,26,opt,name=sub_channel,json=subChannel,proto3,oneof" json:"sub_channel,omitempty"`
	// 多图广告时，填充多图素材信息
	ImageList []*BidResponse_ImageObject `protobuf:"bytes,27,rep,name=image_list,json=imageList,proto3" json:"image_list,omitempty"`
	// 游戏dsp归因参数，gm 开头
	ChannelInfo *string `protobuf:"bytes,28,opt,name=channel_info,json=channelInfo,proto3,oneof" json:"channel_info,omitempty"`
	// 游戏dsp扩展归因参数
	ExtraInfo *string `protobuf:"bytes,29,opt,name=extra_info,json=extraInfo,proto3,oneof" json:"extra_info,omitempty"`
}

func (x *BidResponse_ExtCreative) Reset() {
	*x = BidResponse_ExtCreative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_ExtCreative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_ExtCreative) ProtoMessage() {}

func (x *BidResponse_ExtCreative) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_ExtCreative.ProtoReflect.Descriptor instead.
func (*BidResponse_ExtCreative) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 5}
}

func (x *BidResponse_ExtCreative) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetH5Link() string {
	if x != nil {
		return x.H5Link
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetMiniProgramType() int32 {
	if x != nil {
		return x.MiniProgramType
	}
	return 0
}

func (x *BidResponse_ExtCreative) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetOfficialWebsite() string {
	if x != nil {
		return x.OfficialWebsite
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetPkgSize() int64 {
	if x != nil {
		return x.PkgSize
	}
	return 0
}

func (x *BidResponse_ExtCreative) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetTargetUrl() string {
	if x != nil {
		return x.TargetUrl
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetDeveloperName() string {
	if x != nil {
		return x.DeveloperName
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetPermissionsUrl() string {
	if x != nil {
		return x.PermissionsUrl
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetPrivacyAgreementUrl() string {
	if x != nil {
		return x.PrivacyAgreementUrl
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetPromotionPurpose() int32 {
	if x != nil {
		return x.PromotionPurpose
	}
	return 0
}

func (x *BidResponse_ExtCreative) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetLogoInfo() *BidResponse_ImageObject {
	if x != nil {
		return x.LogoInfo
	}
	return nil
}

func (x *BidResponse_ExtCreative) GetIconInfo() *BidResponse_ImageObject {
	if x != nil {
		return x.IconInfo
	}
	return nil
}

func (x *BidResponse_ExtCreative) GetVideo() *BidResponse_VideoObject {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_ExtCreative) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetButtonText() string {
	if x != nil {
		return x.ButtonText
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetStoreChannel() int32 {
	if x != nil && x.StoreChannel != nil {
		return *x.StoreChannel
	}
	return 0
}

func (x *BidResponse_ExtCreative) GetSubChannel() string {
	if x != nil && x.SubChannel != nil {
		return *x.SubChannel
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetImageList() []*BidResponse_ImageObject {
	if x != nil {
		return x.ImageList
	}
	return nil
}

func (x *BidResponse_ExtCreative) GetChannelInfo() string {
	if x != nil && x.ChannelInfo != nil {
		return *x.ChannelInfo
	}
	return ""
}

func (x *BidResponse_ExtCreative) GetExtraInfo() string {
	if x != nil && x.ExtraInfo != nil {
		return *x.ExtraInfo
	}
	return ""
}

type BidResponse_VideoObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 视频url，（必须为：HTTPS），最大长度4096
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// 视频宽度 单位：像素
	W int32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`
	// 视频高度 单位：像素
	H int32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`
	// 视频格式，定义如下：mp4
	VideoType string `protobuf:"bytes,4,opt,name=video_type,json=videoType,proto3" json:"video_type,omitempty"`
	// 视频时长，单位秒
	VideoDuration int32 `protobuf:"varint,5,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty"`
	// 视频大小，单位byte
	Size int32 `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	// 视频封面图
	TitleScreen *BidResponse_ImageObject `protobuf:"bytes,7,opt,name=title_screen,json=titleScreen,proto3" json:"title_screen,omitempty"`
}

func (x *BidResponse_VideoObject) Reset() {
	*x = BidResponse_VideoObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_VideoObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_VideoObject) ProtoMessage() {}

func (x *BidResponse_VideoObject) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_VideoObject.ProtoReflect.Descriptor instead.
func (*BidResponse_VideoObject) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 6}
}

func (x *BidResponse_VideoObject) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_VideoObject) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_VideoObject) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_VideoObject) GetVideoType() string {
	if x != nil {
		return x.VideoType
	}
	return ""
}

func (x *BidResponse_VideoObject) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *BidResponse_VideoObject) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidResponse_VideoObject) GetTitleScreen() *BidResponse_ImageObject {
	if x != nil {
		return x.TitleScreen
	}
	return nil
}

type BidResponse_ImageObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 图像url ，（必须为：HTTPS），最大长度4096，支持png,jpeg,jpg
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// 图像宽度 单位：像素
	W int32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`
	// 图像宽度 单位：像素
	H int32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`
	// 取值：png,jpeg,jpg
	ImageType string `protobuf:"bytes,4,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
}

func (x *BidResponse_ImageObject) Reset() {
	*x = BidResponse_ImageObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_honor_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_ImageObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_ImageObject) ProtoMessage() {}

func (x *BidResponse_ImageObject) ProtoReflect() protoreflect.Message {
	mi := &file_honor_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_ImageObject.ProtoReflect.Descriptor instead.
func (*BidResponse_ImageObject) Descriptor() ([]byte, []int) {
	return file_honor_proto_rawDescGZIP(), []int{1, 7}
}

func (x *BidResponse_ImageObject) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_ImageObject) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_ImageObject) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_ImageObject) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

var File_honor_proto protoreflect.FileDescriptor

var file_honor_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x68,
	0x6f, 0x6e, 0x6f, 0x72, 0x22, 0x97, 0x13, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x03,
	0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x6f, 0x6e, 0x6f,
	0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70,
	0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x6d, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x6d, 0x61, 0x78, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x75, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x75,
	0x72, 0x12, 0x31, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x78, 0x74, 0x52,
	0x03, 0x65, 0x78, 0x74, 0x1a, 0x53, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x1a, 0x4c, 0x0a, 0x0d, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a, 0x11,
	0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x6c, 0x31, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x54, 0x61, 0x67, 0x4c, 0x31, 0x49, 0x64, 0x1a, 0xb0, 0x04, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x75, 0x61, 0x12, 0x27, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c,
	0x6d, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6c, 0x6d, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x77, 0x76,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x68, 0x77, 0x76, 0x12, 0x0c, 0x0a, 0x01, 0x68,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x78, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x70, 0x78, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x6a, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x6a, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x66, 0x61, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x66, 0x61, 0x12, 0x33, 0x0a, 0x03, 0x65, 0x78, 0x74,
	0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d,
	0x65, 0x69, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x66, 0x6c, 0x61, 0x67,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x66, 0x6c,
	0x61, 0x67, 0x1a, 0x36, 0x0a, 0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x03, 0x47, 0x65,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x75,
	0x74, 0x63, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x75, 0x74, 0x63, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x1a, 0xa6, 0x04, 0x0a, 0x03, 0x49, 0x6d,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x27, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x6f, 0x6e,
	0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x0a, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0a, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x72, 0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x6f,
	0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x62, 0x6a, 0x52, 0x09, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x73,
	0x74, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x73, 0x74, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75, 0x72,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x63, 0x75, 0x72, 0x12, 0x2a, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x1a, 0xc6, 0x01, 0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x41, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c,
	0x73, 0x12, 0x30, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03,
	0x65, 0x78, 0x74, 0x1a, 0x36, 0x0a, 0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xf9, 0x01, 0x0a, 0x04,
	0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x20, 0x0a, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63, 0x75, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x63,
	0x75, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x61, 0x74, 0x12, 0x31, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x1a,
	0x36, 0x0a, 0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12,
	0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69,
	0x6d, 0x65, 0x73, 0x1a, 0x91, 0x02, 0x0a, 0x06, 0x49, 0x6d, 0x70, 0x45, 0x78, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x77, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x77, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x10,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0b, 0x75, 0x69, 0x5f, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x75, 0x69, 0x50, 0x6b, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x75, 0x69, 0x43, 0x61, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x06, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x88, 0x01, 0x01,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x75, 0x69, 0x5f, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x74, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x1a, 0x25, 0x0a, 0x09, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x4f, 0x62, 0x6a, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xea,
	0x15, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34,
	0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61,
	0x74, 0x62, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x75,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x75, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x62, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x62, 0x72, 0x12, 0x2d,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x68, 0x6f,
	0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0x36, 0x0a,
	0x08, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xcc, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x12, 0x28, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x35, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0x36, 0x0a, 0x08,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x89, 0x03, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x04,
	0x6c, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x75,
	0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65,
	0x61, 0x6c, 0x69, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68,
	0x12, 0x2b, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x1c, 0x0a,
	0x07, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x06, 0x61, 0x70, 0x70, 0x43, 0x61, 0x74, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x6c, 0x75, 0x72, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x74,
	0x1a, 0x91, 0x01, 0x0a, 0x06, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x12, 0x44, 0x0a, 0x0d, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x52, 0x0c, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x12, 0x41, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x1a, 0xbc, 0x02, 0x0a, 0x0c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x73, 0x1a, 0x8e, 0x09, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x35,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x35, 0x6c, 0x69,
	0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d,
	0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x57, 0x65, 0x62, 0x73, 0x69,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x34, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00,
	0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x6f,
	0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x22,
	0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x88,
	0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x1a, 0xd8, 0x01, 0x0a, 0x0b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x41, 0x0a, 0x0c,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x1a,
	0x5a, 0x0a, 0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c,
	0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x3d, 0x0a, 0x2f, 0x63,
	0x6f, 0x6d, 0x2e, 0x68, 0x69, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x2e, 0x61, 0x64, 0x2e, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x64, 0x75,
	0x62, 0x62, 0x6f, 0x2e, 0x70, 0x64, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x72, 0x74, 0x62, 0x50, 0x01,
	0x5a, 0x08, 0x2e, 0x2e, 0x2f, 0x68, 0x6f, 0x6e, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_honor_proto_rawDescOnce sync.Once
	file_honor_proto_rawDescData = file_honor_proto_rawDesc
)

func file_honor_proto_rawDescGZIP() []byte {
	file_honor_proto_rawDescOnce.Do(func() {
		file_honor_proto_rawDescData = protoimpl.X.CompressGZIP(file_honor_proto_rawDescData)
	})
	return file_honor_proto_rawDescData
}

var file_honor_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_honor_proto_goTypes = []interface{}{
	(*BidRequest)(nil),               // 0: honor.BidRequest
	(*BidResponse)(nil),              // 1: honor.BidResponse
	(*BidRequest_App)(nil),           // 2: honor.BidRequest.App
	(*BidRequest_BidRequestExt)(nil), // 3: honor.BidRequest.BidRequestExt
	(*BidRequest_Device)(nil),        // 4: honor.BidRequest.Device
	(*BidRequest_Geo)(nil),           // 5: honor.BidRequest.Geo
	(*BidRequest_Imp)(nil),           // 6: honor.BidRequest.Imp
	(*BidRequest_Pmp)(nil),           // 7: honor.BidRequest.Pmp
	(*BidRequest_Deal)(nil),          // 8: honor.BidRequest.Deal
	(*BidRequest_Banner)(nil),        // 9: honor.BidRequest.Banner
	(*BidRequest_ImpExt)(nil),        // 10: honor.BidRequest.ImpExt
	(*BidRequest_NativeObj)(nil),     // 11: honor.BidRequest.NativeObj
	nil,                              // 12: honor.BidRequest.Device.ExtEntry
	nil,                              // 13: honor.BidRequest.Pmp.ExtEntry
	nil,                              // 14: honor.BidRequest.Deal.ExtEntry
	nil,                              // 15: honor.BidResponse.ExtEntry
	(*BidResponse_SeatBid)(nil),      // 16: honor.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),          // 17: honor.BidResponse.Bid
	(*BidResponse_BidExt)(nil),       // 18: honor.BidResponse.BidExt
	(*BidResponse_EventTracker)(nil), // 19: honor.BidResponse.EventTracker
	(*BidResponse_ExtCreative)(nil),  // 20: honor.BidResponse.ExtCreative
	(*BidResponse_VideoObject)(nil),  // 21: honor.BidResponse.VideoObject
	(*BidResponse_ImageObject)(nil),  // 22: honor.BidResponse.ImageObject
	nil,                              // 23: honor.BidResponse.SeatBid.ExtEntry
}
var file_honor_proto_depIdxs = []int32{
	6,  // 0: honor.BidRequest.imp:type_name -> honor.BidRequest.Imp
	2,  // 1: honor.BidRequest.app:type_name -> honor.BidRequest.App
	4,  // 2: honor.BidRequest.device:type_name -> honor.BidRequest.Device
	3,  // 3: honor.BidRequest.ext:type_name -> honor.BidRequest.BidRequestExt
	16, // 4: honor.BidResponse.seatbid:type_name -> honor.BidResponse.SeatBid
	15, // 5: honor.BidResponse.ext:type_name -> honor.BidResponse.ExtEntry
	5,  // 6: honor.BidRequest.Device.geo:type_name -> honor.BidRequest.Geo
	12, // 7: honor.BidRequest.Device.ext:type_name -> honor.BidRequest.Device.ExtEntry
	7,  // 8: honor.BidRequest.Imp.pmp:type_name -> honor.BidRequest.Pmp
	9,  // 9: honor.BidRequest.Imp.banner:type_name -> honor.BidRequest.Banner
	9,  // 10: honor.BidRequest.Imp.banner_list:type_name -> honor.BidRequest.Banner
	11, // 11: honor.BidRequest.Imp.native_req:type_name -> honor.BidRequest.NativeObj
	10, // 12: honor.BidRequest.Imp.ext:type_name -> honor.BidRequest.ImpExt
	8,  // 13: honor.BidRequest.Pmp.deals:type_name -> honor.BidRequest.Deal
	13, // 14: honor.BidRequest.Pmp.ext:type_name -> honor.BidRequest.Pmp.ExtEntry
	14, // 15: honor.BidRequest.Deal.ext:type_name -> honor.BidRequest.Deal.ExtEntry
	17, // 16: honor.BidResponse.SeatBid.bid:type_name -> honor.BidResponse.Bid
	23, // 17: honor.BidResponse.SeatBid.ext:type_name -> honor.BidResponse.SeatBid.ExtEntry
	18, // 18: honor.BidResponse.Bid.ext:type_name -> honor.BidResponse.BidExt
	19, // 19: honor.BidResponse.BidExt.event_tracker:type_name -> honor.BidResponse.EventTracker
	20, // 20: honor.BidResponse.BidExt.ext_creative:type_name -> honor.BidResponse.ExtCreative
	22, // 21: honor.BidResponse.ExtCreative.logo_info:type_name -> honor.BidResponse.ImageObject
	22, // 22: honor.BidResponse.ExtCreative.icon_info:type_name -> honor.BidResponse.ImageObject
	21, // 23: honor.BidResponse.ExtCreative.video:type_name -> honor.BidResponse.VideoObject
	22, // 24: honor.BidResponse.ExtCreative.image_list:type_name -> honor.BidResponse.ImageObject
	22, // 25: honor.BidResponse.VideoObject.title_screen:type_name -> honor.BidResponse.ImageObject
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_honor_proto_init() }
func file_honor_proto_init() {
	if File_honor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_honor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_BidRequestExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_ImpExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_NativeObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_BidExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_EventTracker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_ExtCreative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_VideoObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_honor_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_ImageObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_honor_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_honor_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_honor_proto_msgTypes[20].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_honor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_honor_proto_goTypes,
		DependencyIndexes: file_honor_proto_depIdxs,
		MessageInfos:      file_honor_proto_msgTypes,
	}.Build()
	File_honor_proto = out.File
	file_honor_proto_rawDesc = nil
	file_honor_proto_goTypes = nil
	file_honor_proto_depIdxs = nil
}
