syntax = "proto2";

option go_package = "mh_proxy/pb/tanx_up";

package tanx_up;

message Request {

    // 当前协议版本号，目前为1
    required int32 version = 1;

    // 此请求的唯一id
    required string id = 2;

    // 可展示的位置
    message Impression  {
        // 此impression在当前Request中的唯一id,从0开始
        required int32 id = 1;

        // 广告位id, 由Tanx分配
        required string pid = 2;

        //广告位的宽和高
        optional int32 width = 3;
        optional int32 height = 4;

        // 0 - 未知, 1~5 - 一~五屏, 6 - 五屏以下
        optional int32 pos = 5;

        // 视频相关
        message Video {
            //中贴的情况下，相对于源片起始位置的偏移量，单位(秒)
            optional int32 start_delay = 1;

            // 当前imp相对于其所在前、中、后贴起始位置的偏移量，单位（毫秒）
            optional int32 section_start_delay = 2;

            // 最小播放时长,单位(毫秒)
            optional int32 min_duration = 3;

            // 最大播放时长,单位(毫秒)
            optional int32 max_duration = 4;
        }
        optional Video video = 6;

        // 是否全屏(插播广告or全屏广告)
        optional bool is_fullscreen = 7 [default = false];

        // 广告位支持的api frameworks
        // 1 VPAID 1.0; 2 VPAID 2.0; 3 MRAID-1; 4 ORMMA; 5 MRAID-2
        repeated int32 api  = 8;

        // 可以展示的创意数量
        optional int32 slot_num = 9 [default = 1];

        message Deal {
            // 媒体分配的dealid
            required string deal_id = 1;

            //此Deal对应的价格, 单位(分)
            required int32 min_price = 2;
        }
        repeated Deal deal = 10;

        //预投放日期, 仅开屏使用，格式:"20160602"
        optional string campaign_date = 11;

        // native模板id
        repeated string native_template_id = 12;

        // RTB的底价，非RTB方式可不填, 单位：分
        optional int32 bid_floor = 13;
        // 媒体优选投放较好的创意ID列表
        message TopCreative {
          optional string creative_id= 1;
        }
        repeated TopCreative top_creatives = 14;
    }
    repeated Impression imp = 3;

    message Site {
        // 当前资源位所在页面的url
        optional string page_url = 1;

        // 当前页面的refer url
        optional string refer_url = 2;

        // 网页信息
        message Content {
            // 标题
            optional string title = 1;

            // 关键词
            repeated string keywords = 2;

            // 网页类目
            repeated string category = 3;

            // 视频的播放时长,单位(秒)
            optional int32 duration = 4;

            // 用户的搜索关键词
            optional string query_term = 5;

            // 输入框提示词
            repeated string suggestion = 6;

            // 视频所属节目ID
            optional string program_id = 7;

            // 视频ID
            optional string video_id = 8;

            // 视频上传者ID
            optional string producer_id = 9;
        }
        optional Content content = 3;
    }
    optional Site site = 4;

    // 设备信息
    message Device {
        // ipv4 点分十进制, 必须为终端真实IP地址
        optional string ip = 1;

        // user agent，来自http头
        optional string user_agent = 2;

        // IOS6.0及以上的idfa号
        optional string idfa = 3;

        // 安卓设备的imei号
        optional string imei = 4;

        // 安卓设备的imei号的md5值,若填写imei原值，则不用填此字段
        optional string imei_md5 = 5;

        // 设备的mac地址
        optional string mac = 6;

        // 设备的mac地址的md5值, 若填写mac原值，则不用填此字段
        optional string mac_md5 = 7;

        // android_id
        optional string android_id = 8;

        // 设备类型，0-手机;1-平板;2-PC;3-互联网电视
        optional int32 device_type = 9;

        // 设备品牌
        // 例如：nokia, samsung
        optional string brand = 10;

        // 设备型号
        // 例如：n70, galaxy
        optional string model = 11;

        // 操作系统
        // 例如：Android,iOS
        optional string os = 12;

        // 操作系统版本
        // 例如：7.0.2
        optional string osv = 13;

        // 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g
        optional int32 network  = 14 [ default = 1 ];

        // 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
        optional int32 operator = 15;

        // 设备屏幕尺寸：宽
        optional int32 width = 16;

        // 设备屏幕尺寸：高
        optional int32 height = 17;

        // 设备密度，对应于pixel_ratio
        optional int32 pixel_ratio = 18 [default=1000];

        // 屏幕方向 0-未知, 1-竖屏, 2-横屏
        optional int32 orientation = 19;

        // 用户所处时区的分钟偏移量
        // 例如：如果是东八区，则 timezone_offset = 60 * 8 = 480.
        optional int32 timezone_offset = 20[ default = 480 ];

        message Geo {
            // 纬度, 取值范围[-90.0 , +90.0]
            optional double lat = 1;
            // 经度, 取值范围[-180.0 , +180.0]
            optional double lon = 2;
        }
        optional Geo geo = 21;

        // 用户已安装 app 列表
        repeated string installed_app = 22;

        // oaid
        optional string oaid = 23;

        // alibaba AAID
        optional string ali_aaid = 24;
        // 广协CAID
        message CAID {
          // 必须和版本同时使用才有意义，版本形如"20201201"
          required string ver = 1;
          required string caid = 2;
        }
        repeated CAID caids = 25;

        // 系统启动标识, 取值方式见附件
        optional string boot_mark = 26;

        // 系统更新标识, 取值方式见附件
        optional string update_mark = 27;

        // 原始IDFA的md5值，md5后的大写形式
        optional string idfa_md5 = 28;

        // 原始OAID(不做大小写转换)的md5值，md5后的大写形式
        optional string oaid_md5 = 29;

        // 原始IMEI的SHA256值，SHA256后的大写形式
        optional string imei_sha256 = 30;

        // 原始IDFA的SHA256值，SHA256后的大写形式
        optional string idfa_sha256 = 31;

        // 原始OAID(不做大小写转换)的SHA256值，SHA256后的大写形式
        optional string oaid_sha256 = 32;

        // ios上设备标识
        optional string open_udid = 33;
    }
    optional Device device = 5;

    // APP属性
    message App {
        // 应用包名, 例如：com.moji.MojiWeather
        optional string package_name = 1;

        // 应用名，例如：陌陌
        optional string app_name = 2;

        // app类目
        repeated string category = 3;

        // app版本号
        optional string version = 4;
    }
    optional App app = 6;

    // 用户数据
    message User {
        //PC、WAP流量为用户在阿里域下的cookie标记
        optional string id = 1;
        //阿里内部使用
        optional string aid = 2;
        //阿里内部使用
        optional string nick_name = 3;
        //阿里内部使用
        optional string ap_id = 4;
        // 用户标签列表
        message UserTag {
          // 用户标签分类: 1 - 兴趣标签
          required int32 type = 1;
          // 媒体侧 用户标签ID
          repeated string ids = 2;
        };
        repeated UserTag user_tags = 5;
        // 阿里内部使用
        optional string utdid = 6;
        // 阿里内部使用
        optional string taobao_id = 7;
    }
    optional User user = 7;

    //页面或者用户的语言
    optional string detected_language = 8;

    optional string trace_key = 9;

    // 是否必须返回https广告
    optional bool https_required = 10 [default = false];
    optional bool is_preview = 11 [ default = false ]; // 预览标记
    optional string page_session_id = 12;
    // 点淘透传字段
    optional string extend_info = 13;
    // 禁止投放的主播ID
    repeated uint64 excluded_streamer_ids = 14;
    // 禁止投放的广告类目ID
    repeated int32 excluded_category_ids = 15;
    // 媒体实验分桶ID
    repeated string bucket_ids = 16;
    // 广告请求触发类型: 0-未知,1-冷启,2-热启,3-下拉刷新,
    // 4-用户登陆,5-版本切换,6-智能刷新
    optional int32 trigger_type = 17;
    // 禁止投放的单品ID
    repeated uint64 excluded_item_ids = 18;
    // 是否实时请求
    optional bool is_realtime_request = 19;
}

message Response {
    //对应Request中的id
    optional string id = 1;

    // 0-ok，其他值表示无广告返回
    optional int32 status = 2 [ default = 0 ];
    optional string msg = 5;
    message TraceInfo {
      optional int32 imp_idx = 1;
      optional int32 status = 2;  // IMP状态码
      optional string msg = 3;
      message DspStatus {
        optional string dsp_name = 1;
        optional string creative_id = 2;
        optional int32 status = 3;  // 广告状态码
        optional string msg = 4;
      }
      repeated DspStatus dsp_status = 4;
    }
    repeated TraceInfo trace_infos = 6;

    // 一个位置上的广告
    message Seat {
        // 指定请求里的impression id
        required int32 id = 1;

        // 广告字段
        message Ad {
            // 广告序号，为0
            required int32 id = 1;
            optional string session_id = 27;  // 广告全局唯一标识ID

            // 广告代码片段，html/imgpath/displayurl
            optional string adcontent = 2;

            // 创意类型
            // 1 文字 2 图片 3 Flash 4 视频
            optional int32 creative_type = 3;

            // 广告类目
            repeated uint64 category = 4;

            // 最终目标landing页
            repeated string destination_url = 5;

            // 展现反馈地址
            repeated string impression_tracking_url = 6;

            // 点击跳转地址
            optional string click_through_url = 7;

            // 点击跟踪地址
            repeated string click_tracking_url = 8;

            message NativeAd {
                // 属性（描述）信息
                message Attr{
                    // 属性名
                    required string  name = 1;

                    // 属性值
                    required string value = 2;
                }
                repeated Attr attr = 1;

                //natvie模板id
                optional int32 template_id = 2;
            }
            optional NativeAd native_ad = 10;

            //需要的api支持
            repeated int32 api = 11;

            //指定媒体deal_id
            optional string deal_id = 12;

            // 计划投放日期, 仅开屏使用，如:"20160602"
            optional string campaign_date = 13;
            // 计划投放日期，优先使用campaign_date，天级曝光控制
            // begin_time和end_time如存在则只能在其区间内才可曝光
            optional uint64 begin_time = 28;
            optional uint64 end_time = 29;

            //广告创意的唯一标识
            optional string creative_id = 14;

            //广告来源
            optional string ad_source = 15;

            //APP唤醒地址
            optional string deeplink_url = 16;

            //APP下载地址
            optional string download_url = 17;

            //广告第一报价,内部使用,单位(分)
            optional int32 price = 18;

            //广告结算价,内部使用,单位(分)
            optional int32 settle_price = 19;

            //adx场景, 返回报价供上游adx竞价, 单位(分)
            optional int32 bid_price = 20;

            // 事件监测url
            message EventTrack {
                //1:播放开始监测;2:播放中间监测;3:播放完毕监测;4:主动播放开始监测;5:用户互动数据监测
                optional uint32 type = 1;
                repeated string url = 2;
                // 此时间后触发监测，相对时间，单位：秒
                optional uint32 time = 3;
            }
            repeated EventTrack event_track = 21;
            // 落地页打开方式：4-universal link, 7-微信小程序
            optional int32 open_type = 22;
            // 竞价成功通知，服务端发送
            optional string winnotice_url = 23;
            // 部分特殊场景需要，用法联系运营同学
            optional int32 type = 24;
            // 广告主ID
            optional uint64 advertiser_id = 25;
            // 用户评估数据
            message UserInfo {
                optional int32 type = 1;    // 用户评估分类：1-用户质量评估
                optional double quality = 2;  // 用户评估分值
            }
            repeated UserInfo user_infos = 26;
            // 扩展字段，DSP自定义数据
            optional string extend_data = 30;

            message MiniApp {
                // 小程序所在的主APP客户端，1-微信，表示调用微信SDK唤起微信小程序
                required int32 host_app = 1;
                // 主APP内的小程序ID
                required string app_id = 2;
                // 小程序path
                required string path = 3;
            }
            optional MiniApp mini_app = 31;
            // 期望广告展示的开始时间
            // 如果请求广告位上的start_delay是X，则该字段的有效值是 X + N * 15，N = 0, 1, 2, ...
            optional int32 start_delay = 32;
            // 竞价失败通知
            optional string loss_notice_url = 33;
        }
        repeated Ad ad = 2;
    }
    repeated Seat seat = 3;
    optional bool user_tag_expected = 4 [ default = false ]; // 期望提供媒体用户标签：默认false-不提供；true-提供
    optional string extend_info = 7;    // 扩展字段
}

