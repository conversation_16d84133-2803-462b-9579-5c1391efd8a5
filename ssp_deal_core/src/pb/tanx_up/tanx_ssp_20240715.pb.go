// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: tanx_ssp_20240715.proto

package tanx_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前协议版本号，目前为1
	Version *int32 `protobuf:"varint,1,req,name=version" json:"version,omitempty"`
	// 此请求的唯一id
	Id     *string               `protobuf:"bytes,2,req,name=id" json:"id,omitempty"`
	Imp    []*Request_Impression `protobuf:"bytes,3,rep,name=imp" json:"imp,omitempty"`
	Site   *Request_Site         `protobuf:"bytes,4,opt,name=site" json:"site,omitempty"`
	Device *Request_Device       `protobuf:"bytes,5,opt,name=device" json:"device,omitempty"`
	App    *Request_App          `protobuf:"bytes,6,opt,name=app" json:"app,omitempty"`
	User   *Request_User         `protobuf:"bytes,7,opt,name=user" json:"user,omitempty"`
	// 页面或者用户的语言
	DetectedLanguage *string `protobuf:"bytes,8,opt,name=detected_language,json=detectedLanguage" json:"detected_language,omitempty"`
	TraceKey         *string `protobuf:"bytes,9,opt,name=trace_key,json=traceKey" json:"trace_key,omitempty"`
	// 是否必须返回https广告
	HttpsRequired *bool   `protobuf:"varint,10,opt,name=https_required,json=httpsRequired,def=0" json:"https_required,omitempty"`
	IsPreview     *bool   `protobuf:"varint,11,opt,name=is_preview,json=isPreview,def=0" json:"is_preview,omitempty"` // 预览标记
	PageSessionId *string `protobuf:"bytes,12,opt,name=page_session_id,json=pageSessionId" json:"page_session_id,omitempty"`
	// 点淘透传字段
	ExtendInfo *string `protobuf:"bytes,13,opt,name=extend_info,json=extendInfo" json:"extend_info,omitempty"`
	// 禁止投放的主播ID
	ExcludedStreamerIds []uint64 `protobuf:"varint,14,rep,name=excluded_streamer_ids,json=excludedStreamerIds" json:"excluded_streamer_ids,omitempty"`
	// 禁止投放的广告类目ID
	ExcludedCategoryIds []int32 `protobuf:"varint,15,rep,name=excluded_category_ids,json=excludedCategoryIds" json:"excluded_category_ids,omitempty"`
	// 媒体实验分桶ID
	BucketIds []string `protobuf:"bytes,16,rep,name=bucket_ids,json=bucketIds" json:"bucket_ids,omitempty"`
	// 广告请求触发类型: 0-未知,1-冷启,2-热启,3-下拉刷新,
	// 4-用户登陆,5-版本切换,6-智能刷新
	TriggerType *int32 `protobuf:"varint,17,opt,name=trigger_type,json=triggerType" json:"trigger_type,omitempty"`
	// 禁止投放的单品ID
	ExcludedItemIds []uint64 `protobuf:"varint,18,rep,name=excluded_item_ids,json=excludedItemIds" json:"excluded_item_ids,omitempty"`
	// 是否实时请求
	IsRealtimeRequest *bool `protobuf:"varint,19,opt,name=is_realtime_request,json=isRealtimeRequest" json:"is_realtime_request,omitempty"`
}

// Default values for Request fields.
const (
	Default_Request_HttpsRequired = bool(false)
	Default_Request_IsPreview     = bool(false)
)

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *Request) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Request) GetImp() []*Request_Impression {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *Request) GetSite() *Request_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *Request) GetDevice() *Request_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Request) GetApp() *Request_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Request) GetUser() *Request_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Request) GetDetectedLanguage() string {
	if x != nil && x.DetectedLanguage != nil {
		return *x.DetectedLanguage
	}
	return ""
}

func (x *Request) GetTraceKey() string {
	if x != nil && x.TraceKey != nil {
		return *x.TraceKey
	}
	return ""
}

func (x *Request) GetHttpsRequired() bool {
	if x != nil && x.HttpsRequired != nil {
		return *x.HttpsRequired
	}
	return Default_Request_HttpsRequired
}

func (x *Request) GetIsPreview() bool {
	if x != nil && x.IsPreview != nil {
		return *x.IsPreview
	}
	return Default_Request_IsPreview
}

func (x *Request) GetPageSessionId() string {
	if x != nil && x.PageSessionId != nil {
		return *x.PageSessionId
	}
	return ""
}

func (x *Request) GetExtendInfo() string {
	if x != nil && x.ExtendInfo != nil {
		return *x.ExtendInfo
	}
	return ""
}

func (x *Request) GetExcludedStreamerIds() []uint64 {
	if x != nil {
		return x.ExcludedStreamerIds
	}
	return nil
}

func (x *Request) GetExcludedCategoryIds() []int32 {
	if x != nil {
		return x.ExcludedCategoryIds
	}
	return nil
}

func (x *Request) GetBucketIds() []string {
	if x != nil {
		return x.BucketIds
	}
	return nil
}

func (x *Request) GetTriggerType() int32 {
	if x != nil && x.TriggerType != nil {
		return *x.TriggerType
	}
	return 0
}

func (x *Request) GetExcludedItemIds() []uint64 {
	if x != nil {
		return x.ExcludedItemIds
	}
	return nil
}

func (x *Request) GetIsRealtimeRequest() bool {
	if x != nil && x.IsRealtimeRequest != nil {
		return *x.IsRealtimeRequest
	}
	return false
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对应Request中的id
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// 0-ok，其他值表示无广告返回
	Status          *int32                `protobuf:"varint,2,opt,name=status,def=0" json:"status,omitempty"`
	Msg             *string               `protobuf:"bytes,5,opt,name=msg" json:"msg,omitempty"`
	TraceInfos      []*Response_TraceInfo `protobuf:"bytes,6,rep,name=trace_infos,json=traceInfos" json:"trace_infos,omitempty"`
	Seat            []*Response_Seat      `protobuf:"bytes,3,rep,name=seat" json:"seat,omitempty"`
	UserTagExpected *bool                 `protobuf:"varint,4,opt,name=user_tag_expected,json=userTagExpected,def=0" json:"user_tag_expected,omitempty"` // 期望提供媒体用户标签：默认false-不提供；true-提供
	ExtendInfo      *string               `protobuf:"bytes,7,opt,name=extend_info,json=extendInfo" json:"extend_info,omitempty"`                         // 扩展字段
}

// Default values for Response fields.
const (
	Default_Response_Status          = int32(0)
	Default_Response_UserTagExpected = bool(false)
)

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Response) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Default_Response_Status
}

func (x *Response) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

func (x *Response) GetTraceInfos() []*Response_TraceInfo {
	if x != nil {
		return x.TraceInfos
	}
	return nil
}

func (x *Response) GetSeat() []*Response_Seat {
	if x != nil {
		return x.Seat
	}
	return nil
}

func (x *Response) GetUserTagExpected() bool {
	if x != nil && x.UserTagExpected != nil {
		return *x.UserTagExpected
	}
	return Default_Response_UserTagExpected
}

func (x *Response) GetExtendInfo() string {
	if x != nil && x.ExtendInfo != nil {
		return *x.ExtendInfo
	}
	return ""
}

// 可展示的位置
type Request_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 此impression在当前Request中的唯一id,从0开始
	Id *int32 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// 广告位id, 由Tanx分配
	Pid *string `protobuf:"bytes,2,req,name=pid" json:"pid,omitempty"`
	// 广告位的宽和高
	Width  *int32 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	Height *int32 `protobuf:"varint,4,opt,name=height" json:"height,omitempty"`
	// 0 - 未知, 1~5 - 一~五屏, 6 - 五屏以下
	Pos   *int32                    `protobuf:"varint,5,opt,name=pos" json:"pos,omitempty"`
	Video *Request_Impression_Video `protobuf:"bytes,6,opt,name=video" json:"video,omitempty"`
	// 是否全屏(插播广告or全屏广告)
	IsFullscreen *bool `protobuf:"varint,7,opt,name=is_fullscreen,json=isFullscreen,def=0" json:"is_fullscreen,omitempty"`
	// 广告位支持的api frameworks
	// 1 VPAID 1.0; 2 VPAID 2.0; 3 MRAID-1; 4 ORMMA; 5 MRAID-2
	Api []int32 `protobuf:"varint,8,rep,name=api" json:"api,omitempty"`
	// 可以展示的创意数量
	SlotNum *int32                     `protobuf:"varint,9,opt,name=slot_num,json=slotNum,def=1" json:"slot_num,omitempty"`
	Deal    []*Request_Impression_Deal `protobuf:"bytes,10,rep,name=deal" json:"deal,omitempty"`
	// 预投放日期, 仅开屏使用，格式:"20160602"
	CampaignDate *string `protobuf:"bytes,11,opt,name=campaign_date,json=campaignDate" json:"campaign_date,omitempty"`
	// native模板id
	NativeTemplateId []string `protobuf:"bytes,12,rep,name=native_template_id,json=nativeTemplateId" json:"native_template_id,omitempty"`
	// RTB的底价，非RTB方式可不填, 单位：分
	BidFloor     *int32                            `protobuf:"varint,13,opt,name=bid_floor,json=bidFloor" json:"bid_floor,omitempty"`
	TopCreatives []*Request_Impression_TopCreative `protobuf:"bytes,14,rep,name=top_creatives,json=topCreatives" json:"top_creatives,omitempty"`
}

// Default values for Request_Impression fields.
const (
	Default_Request_Impression_IsFullscreen = bool(false)
	Default_Request_Impression_SlotNum      = int32(1)
)

func (x *Request_Impression) Reset() {
	*x = Request_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Impression) ProtoMessage() {}

func (x *Request_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Impression.ProtoReflect.Descriptor instead.
func (*Request_Impression) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Request_Impression) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Request_Impression) GetPid() string {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return ""
}

func (x *Request_Impression) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Request_Impression) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Request_Impression) GetPos() int32 {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return 0
}

func (x *Request_Impression) GetVideo() *Request_Impression_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Request_Impression) GetIsFullscreen() bool {
	if x != nil && x.IsFullscreen != nil {
		return *x.IsFullscreen
	}
	return Default_Request_Impression_IsFullscreen
}

func (x *Request_Impression) GetApi() []int32 {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *Request_Impression) GetSlotNum() int32 {
	if x != nil && x.SlotNum != nil {
		return *x.SlotNum
	}
	return Default_Request_Impression_SlotNum
}

func (x *Request_Impression) GetDeal() []*Request_Impression_Deal {
	if x != nil {
		return x.Deal
	}
	return nil
}

func (x *Request_Impression) GetCampaignDate() string {
	if x != nil && x.CampaignDate != nil {
		return *x.CampaignDate
	}
	return ""
}

func (x *Request_Impression) GetNativeTemplateId() []string {
	if x != nil {
		return x.NativeTemplateId
	}
	return nil
}

func (x *Request_Impression) GetBidFloor() int32 {
	if x != nil && x.BidFloor != nil {
		return *x.BidFloor
	}
	return 0
}

func (x *Request_Impression) GetTopCreatives() []*Request_Impression_TopCreative {
	if x != nil {
		return x.TopCreatives
	}
	return nil
}

type Request_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前资源位所在页面的url
	PageUrl *string `protobuf:"bytes,1,opt,name=page_url,json=pageUrl" json:"page_url,omitempty"`
	// 当前页面的refer url
	ReferUrl *string               `protobuf:"bytes,2,opt,name=refer_url,json=referUrl" json:"refer_url,omitempty"`
	Content  *Request_Site_Content `protobuf:"bytes,3,opt,name=content" json:"content,omitempty"`
}

func (x *Request_Site) Reset() {
	*x = Request_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Site) ProtoMessage() {}

func (x *Request_Site) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Site.ProtoReflect.Descriptor instead.
func (*Request_Site) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Request_Site) GetPageUrl() string {
	if x != nil && x.PageUrl != nil {
		return *x.PageUrl
	}
	return ""
}

func (x *Request_Site) GetReferUrl() string {
	if x != nil && x.ReferUrl != nil {
		return *x.ReferUrl
	}
	return ""
}

func (x *Request_Site) GetContent() *Request_Site_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

// 设备信息
type Request_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ipv4 点分十进制, 必须为终端真实IP地址
	Ip *string `protobuf:"bytes,1,opt,name=ip" json:"ip,omitempty"`
	// user agent，来自http头
	UserAgent *string `protobuf:"bytes,2,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	// IOS6.0及以上的idfa号
	Idfa *string `protobuf:"bytes,3,opt,name=idfa" json:"idfa,omitempty"`
	// 安卓设备的imei号
	Imei *string `protobuf:"bytes,4,opt,name=imei" json:"imei,omitempty"`
	// 安卓设备的imei号的md5值,若填写imei原值，则不用填此字段
	ImeiMd5 *string `protobuf:"bytes,5,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`
	// 设备的mac地址
	Mac *string `protobuf:"bytes,6,opt,name=mac" json:"mac,omitempty"`
	// 设备的mac地址的md5值, 若填写mac原值，则不用填此字段
	MacMd5 *string `protobuf:"bytes,7,opt,name=mac_md5,json=macMd5" json:"mac_md5,omitempty"`
	// android_id
	AndroidId *string `protobuf:"bytes,8,opt,name=android_id,json=androidId" json:"android_id,omitempty"`
	// 设备类型，0-手机;1-平板;2-PC;3-互联网电视
	DeviceType *int32 `protobuf:"varint,9,opt,name=device_type,json=deviceType" json:"device_type,omitempty"`
	// 设备品牌
	// 例如：nokia, samsung
	Brand *string `protobuf:"bytes,10,opt,name=brand" json:"brand,omitempty"`
	// 设备型号
	// 例如：n70, galaxy
	Model *string `protobuf:"bytes,11,opt,name=model" json:"model,omitempty"`
	// 操作系统
	// 例如：Android,iOS
	Os *string `protobuf:"bytes,12,opt,name=os" json:"os,omitempty"`
	// 操作系统版本
	// 例如：7.0.2
	Osv *string `protobuf:"bytes,13,opt,name=osv" json:"osv,omitempty"`
	// 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g
	Network *int32 `protobuf:"varint,14,opt,name=network,def=1" json:"network,omitempty"`
	// 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
	Operator *int32 `protobuf:"varint,15,opt,name=operator" json:"operator,omitempty"`
	// 设备屏幕尺寸：宽
	Width *int32 `protobuf:"varint,16,opt,name=width" json:"width,omitempty"`
	// 设备屏幕尺寸：高
	Height *int32 `protobuf:"varint,17,opt,name=height" json:"height,omitempty"`
	// 设备密度，对应于pixel_ratio
	PixelRatio *int32 `protobuf:"varint,18,opt,name=pixel_ratio,json=pixelRatio,def=1000" json:"pixel_ratio,omitempty"`
	// 屏幕方向 0-未知, 1-竖屏, 2-横屏
	Orientation *int32 `protobuf:"varint,19,opt,name=orientation" json:"orientation,omitempty"`
	// 用户所处时区的分钟偏移量
	// 例如：如果是东八区，则 timezone_offset = 60 * 8 = 480.
	TimezoneOffset *int32              `protobuf:"varint,20,opt,name=timezone_offset,json=timezoneOffset,def=480" json:"timezone_offset,omitempty"`
	Geo            *Request_Device_Geo `protobuf:"bytes,21,opt,name=geo" json:"geo,omitempty"`
	// 用户已安装 app 列表
	InstalledApp []string `protobuf:"bytes,22,rep,name=installed_app,json=installedApp" json:"installed_app,omitempty"`
	// oaid
	Oaid *string `protobuf:"bytes,23,opt,name=oaid" json:"oaid,omitempty"`
	// alibaba AAID
	AliAaid *string                `protobuf:"bytes,24,opt,name=ali_aaid,json=aliAaid" json:"ali_aaid,omitempty"`
	Caids   []*Request_Device_CAID `protobuf:"bytes,25,rep,name=caids" json:"caids,omitempty"`
	// 系统启动标识, 取值方式见附件
	BootMark *string `protobuf:"bytes,26,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`
	// 系统更新标识, 取值方式见附件
	UpdateMark *string `protobuf:"bytes,27,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`
	// 原始IDFA的md5值，md5后的大写形式
	IdfaMd5 *string `protobuf:"bytes,28,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`
	// 原始OAID(不做大小写转换)的md5值，md5后的大写形式
	OaidMd5 *string `protobuf:"bytes,29,opt,name=oaid_md5,json=oaidMd5" json:"oaid_md5,omitempty"`
	// 原始IMEI的SHA256值，SHA256后的大写形式
	ImeiSha256 *string `protobuf:"bytes,30,opt,name=imei_sha256,json=imeiSha256" json:"imei_sha256,omitempty"`
	// 原始IDFA的SHA256值，SHA256后的大写形式
	IdfaSha256 *string `protobuf:"bytes,31,opt,name=idfa_sha256,json=idfaSha256" json:"idfa_sha256,omitempty"`
	// 原始OAID(不做大小写转换)的SHA256值，SHA256后的大写形式
	OaidSha256 *string `protobuf:"bytes,32,opt,name=oaid_sha256,json=oaidSha256" json:"oaid_sha256,omitempty"`
	// ios上设备标识
	OpenUdid *string `protobuf:"bytes,33,opt,name=open_udid,json=openUdid" json:"open_udid,omitempty"`
}

// Default values for Request_Device fields.
const (
	Default_Request_Device_Network        = int32(1)
	Default_Request_Device_PixelRatio     = int32(1000)
	Default_Request_Device_TimezoneOffset = int32(480)
)

func (x *Request_Device) Reset() {
	*x = Request_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device) ProtoMessage() {}

func (x *Request_Device) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device.ProtoReflect.Descriptor instead.
func (*Request_Device) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Request_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *Request_Device) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *Request_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *Request_Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *Request_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *Request_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *Request_Device) GetMacMd5() string {
	if x != nil && x.MacMd5 != nil {
		return *x.MacMd5
	}
	return ""
}

func (x *Request_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *Request_Device) GetDeviceType() int32 {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return 0
}

func (x *Request_Device) GetBrand() string {
	if x != nil && x.Brand != nil {
		return *x.Brand
	}
	return ""
}

func (x *Request_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *Request_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *Request_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *Request_Device) GetNetwork() int32 {
	if x != nil && x.Network != nil {
		return *x.Network
	}
	return Default_Request_Device_Network
}

func (x *Request_Device) GetOperator() int32 {
	if x != nil && x.Operator != nil {
		return *x.Operator
	}
	return 0
}

func (x *Request_Device) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Request_Device) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Request_Device) GetPixelRatio() int32 {
	if x != nil && x.PixelRatio != nil {
		return *x.PixelRatio
	}
	return Default_Request_Device_PixelRatio
}

func (x *Request_Device) GetOrientation() int32 {
	if x != nil && x.Orientation != nil {
		return *x.Orientation
	}
	return 0
}

func (x *Request_Device) GetTimezoneOffset() int32 {
	if x != nil && x.TimezoneOffset != nil {
		return *x.TimezoneOffset
	}
	return Default_Request_Device_TimezoneOffset
}

func (x *Request_Device) GetGeo() *Request_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Request_Device) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *Request_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *Request_Device) GetAliAaid() string {
	if x != nil && x.AliAaid != nil {
		return *x.AliAaid
	}
	return ""
}

func (x *Request_Device) GetCaids() []*Request_Device_CAID {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *Request_Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *Request_Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *Request_Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *Request_Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *Request_Device) GetImeiSha256() string {
	if x != nil && x.ImeiSha256 != nil {
		return *x.ImeiSha256
	}
	return ""
}

func (x *Request_Device) GetIdfaSha256() string {
	if x != nil && x.IdfaSha256 != nil {
		return *x.IdfaSha256
	}
	return ""
}

func (x *Request_Device) GetOaidSha256() string {
	if x != nil && x.OaidSha256 != nil {
		return *x.OaidSha256
	}
	return ""
}

func (x *Request_Device) GetOpenUdid() string {
	if x != nil && x.OpenUdid != nil {
		return *x.OpenUdid
	}
	return ""
}

// APP属性
type Request_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用包名, 例如：com.moji.MojiWeather
	PackageName *string `protobuf:"bytes,1,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// 应用名，例如：陌陌
	AppName *string `protobuf:"bytes,2,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	// app类目
	Category []string `protobuf:"bytes,3,rep,name=category" json:"category,omitempty"`
	// app版本号
	Version *string `protobuf:"bytes,4,opt,name=version" json:"version,omitempty"`
}

func (x *Request_App) Reset() {
	*x = Request_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_App) ProtoMessage() {}

func (x *Request_App) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_App.ProtoReflect.Descriptor instead.
func (*Request_App) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Request_App) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *Request_App) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *Request_App) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Request_App) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

// 用户数据
type Request_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PC、WAP流量为用户在阿里域下的cookie标记
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// 阿里内部使用
	Aid *string `protobuf:"bytes,2,opt,name=aid" json:"aid,omitempty"`
	// 阿里内部使用
	NickName *string `protobuf:"bytes,3,opt,name=nick_name,json=nickName" json:"nick_name,omitempty"`
	// 阿里内部使用
	ApId     *string                 `protobuf:"bytes,4,opt,name=ap_id,json=apId" json:"ap_id,omitempty"`
	UserTags []*Request_User_UserTag `protobuf:"bytes,5,rep,name=user_tags,json=userTags" json:"user_tags,omitempty"`
	// 阿里内部使用
	Utdid *string `protobuf:"bytes,6,opt,name=utdid" json:"utdid,omitempty"`
	// 阿里内部使用
	TaobaoId *string `protobuf:"bytes,7,opt,name=taobao_id,json=taobaoId" json:"taobao_id,omitempty"`
}

func (x *Request_User) Reset() {
	*x = Request_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_User) ProtoMessage() {}

func (x *Request_User) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_User.ProtoReflect.Descriptor instead.
func (*Request_User) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Request_User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Request_User) GetAid() string {
	if x != nil && x.Aid != nil {
		return *x.Aid
	}
	return ""
}

func (x *Request_User) GetNickName() string {
	if x != nil && x.NickName != nil {
		return *x.NickName
	}
	return ""
}

func (x *Request_User) GetApId() string {
	if x != nil && x.ApId != nil {
		return *x.ApId
	}
	return ""
}

func (x *Request_User) GetUserTags() []*Request_User_UserTag {
	if x != nil {
		return x.UserTags
	}
	return nil
}

func (x *Request_User) GetUtdid() string {
	if x != nil && x.Utdid != nil {
		return *x.Utdid
	}
	return ""
}

func (x *Request_User) GetTaobaoId() string {
	if x != nil && x.TaobaoId != nil {
		return *x.TaobaoId
	}
	return ""
}

// 视频相关
type Request_Impression_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 中贴的情况下，相对于源片起始位置的偏移量，单位(秒)
	StartDelay *int32 `protobuf:"varint,1,opt,name=start_delay,json=startDelay" json:"start_delay,omitempty"`
	// 当前imp相对于其所在前、中、后贴起始位置的偏移量，单位（毫秒）
	SectionStartDelay *int32 `protobuf:"varint,2,opt,name=section_start_delay,json=sectionStartDelay" json:"section_start_delay,omitempty"`
	// 最小播放时长,单位(毫秒)
	MinDuration *int32 `protobuf:"varint,3,opt,name=min_duration,json=minDuration" json:"min_duration,omitempty"`
	// 最大播放时长,单位(毫秒)
	MaxDuration *int32 `protobuf:"varint,4,opt,name=max_duration,json=maxDuration" json:"max_duration,omitempty"`
}

func (x *Request_Impression_Video) Reset() {
	*x = Request_Impression_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Impression_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Impression_Video) ProtoMessage() {}

func (x *Request_Impression_Video) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Impression_Video.ProtoReflect.Descriptor instead.
func (*Request_Impression_Video) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *Request_Impression_Video) GetStartDelay() int32 {
	if x != nil && x.StartDelay != nil {
		return *x.StartDelay
	}
	return 0
}

func (x *Request_Impression_Video) GetSectionStartDelay() int32 {
	if x != nil && x.SectionStartDelay != nil {
		return *x.SectionStartDelay
	}
	return 0
}

func (x *Request_Impression_Video) GetMinDuration() int32 {
	if x != nil && x.MinDuration != nil {
		return *x.MinDuration
	}
	return 0
}

func (x *Request_Impression_Video) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

type Request_Impression_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体分配的dealid
	DealId *string `protobuf:"bytes,1,req,name=deal_id,json=dealId" json:"deal_id,omitempty"`
	// 此Deal对应的价格, 单位(分)
	MinPrice *int32 `protobuf:"varint,2,req,name=min_price,json=minPrice" json:"min_price,omitempty"`
}

func (x *Request_Impression_Deal) Reset() {
	*x = Request_Impression_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Impression_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Impression_Deal) ProtoMessage() {}

func (x *Request_Impression_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Impression_Deal.ProtoReflect.Descriptor instead.
func (*Request_Impression_Deal) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *Request_Impression_Deal) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *Request_Impression_Deal) GetMinPrice() int32 {
	if x != nil && x.MinPrice != nil {
		return *x.MinPrice
	}
	return 0
}

// 媒体优选投放较好的创意ID列表
type Request_Impression_TopCreative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeId *string `protobuf:"bytes,1,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
}

func (x *Request_Impression_TopCreative) Reset() {
	*x = Request_Impression_TopCreative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Impression_TopCreative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Impression_TopCreative) ProtoMessage() {}

func (x *Request_Impression_TopCreative) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Impression_TopCreative.ProtoReflect.Descriptor instead.
func (*Request_Impression_TopCreative) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *Request_Impression_TopCreative) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

// 网页信息
type Request_Site_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标题
	Title *string `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// 关键词
	Keywords []string `protobuf:"bytes,2,rep,name=keywords" json:"keywords,omitempty"`
	// 网页类目
	Category []string `protobuf:"bytes,3,rep,name=category" json:"category,omitempty"`
	// 视频的播放时长,单位(秒)
	Duration *int32 `protobuf:"varint,4,opt,name=duration" json:"duration,omitempty"`
	// 用户的搜索关键词
	QueryTerm *string `protobuf:"bytes,5,opt,name=query_term,json=queryTerm" json:"query_term,omitempty"`
	// 输入框提示词
	Suggestion []string `protobuf:"bytes,6,rep,name=suggestion" json:"suggestion,omitempty"`
	// 视频所属节目ID
	ProgramId *string `protobuf:"bytes,7,opt,name=program_id,json=programId" json:"program_id,omitempty"`
	// 视频ID
	VideoId *string `protobuf:"bytes,8,opt,name=video_id,json=videoId" json:"video_id,omitempty"`
	// 视频上传者ID
	ProducerId *string `protobuf:"bytes,9,opt,name=producer_id,json=producerId" json:"producer_id,omitempty"`
}

func (x *Request_Site_Content) Reset() {
	*x = Request_Site_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Site_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Site_Content) ProtoMessage() {}

func (x *Request_Site_Content) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Site_Content.ProtoReflect.Descriptor instead.
func (*Request_Site_Content) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *Request_Site_Content) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Request_Site_Content) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *Request_Site_Content) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Request_Site_Content) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *Request_Site_Content) GetQueryTerm() string {
	if x != nil && x.QueryTerm != nil {
		return *x.QueryTerm
	}
	return ""
}

func (x *Request_Site_Content) GetSuggestion() []string {
	if x != nil {
		return x.Suggestion
	}
	return nil
}

func (x *Request_Site_Content) GetProgramId() string {
	if x != nil && x.ProgramId != nil {
		return *x.ProgramId
	}
	return ""
}

func (x *Request_Site_Content) GetVideoId() string {
	if x != nil && x.VideoId != nil {
		return *x.VideoId
	}
	return ""
}

func (x *Request_Site_Content) GetProducerId() string {
	if x != nil && x.ProducerId != nil {
		return *x.ProducerId
	}
	return ""
}

type Request_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 纬度, 取值范围[-90.0 , +90.0]
	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"`
	// 经度, 取值范围[-180.0 , +180.0]
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"`
}

func (x *Request_Device_Geo) Reset() {
	*x = Request_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_Geo) ProtoMessage() {}

func (x *Request_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_Geo.ProtoReflect.Descriptor instead.
func (*Request_Device_Geo) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *Request_Device_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *Request_Device_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

// 广协CAID
type Request_Device_CAID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必须和版本同时使用才有意义，版本形如"20201201"
	Ver  *string `protobuf:"bytes,1,req,name=ver" json:"ver,omitempty"`
	Caid *string `protobuf:"bytes,2,req,name=caid" json:"caid,omitempty"`
}

func (x *Request_Device_CAID) Reset() {
	*x = Request_Device_CAID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_CAID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_CAID) ProtoMessage() {}

func (x *Request_Device_CAID) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_CAID.ProtoReflect.Descriptor instead.
func (*Request_Device_CAID) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 2, 1}
}

func (x *Request_Device_CAID) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *Request_Device_CAID) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

// 用户标签列表
type Request_User_UserTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户标签分类: 1 - 兴趣标签
	Type *int32 `protobuf:"varint,1,req,name=type" json:"type,omitempty"`
	// 媒体侧 用户标签ID
	Ids []string `protobuf:"bytes,2,rep,name=ids" json:"ids,omitempty"`
}

func (x *Request_User_UserTag) Reset() {
	*x = Request_User_UserTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_User_UserTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_User_UserTag) ProtoMessage() {}

func (x *Request_User_UserTag) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_User_UserTag.ProtoReflect.Descriptor instead.
func (*Request_User_UserTag) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *Request_User_UserTag) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Request_User_UserTag) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type Response_TraceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpIdx    *int32                          `protobuf:"varint,1,opt,name=imp_idx,json=impIdx" json:"imp_idx,omitempty"`
	Status    *int32                          `protobuf:"varint,2,opt,name=status" json:"status,omitempty"` // IMP状态码
	Msg       *string                         `protobuf:"bytes,3,opt,name=msg" json:"msg,omitempty"`
	DspStatus []*Response_TraceInfo_DspStatus `protobuf:"bytes,4,rep,name=dsp_status,json=dspStatus" json:"dsp_status,omitempty"`
}

func (x *Response_TraceInfo) Reset() {
	*x = Response_TraceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_TraceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_TraceInfo) ProtoMessage() {}

func (x *Response_TraceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_TraceInfo.ProtoReflect.Descriptor instead.
func (*Response_TraceInfo) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Response_TraceInfo) GetImpIdx() int32 {
	if x != nil && x.ImpIdx != nil {
		return *x.ImpIdx
	}
	return 0
}

func (x *Response_TraceInfo) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *Response_TraceInfo) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

func (x *Response_TraceInfo) GetDspStatus() []*Response_TraceInfo_DspStatus {
	if x != nil {
		return x.DspStatus
	}
	return nil
}

// 一个位置上的广告
type Response_Seat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 指定请求里的impression id
	Id *int32              `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	Ad []*Response_Seat_Ad `protobuf:"bytes,2,rep,name=ad" json:"ad,omitempty"`
}

func (x *Response_Seat) Reset() {
	*x = Response_Seat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat) ProtoMessage() {}

func (x *Response_Seat) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat.ProtoReflect.Descriptor instead.
func (*Response_Seat) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Response_Seat) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Response_Seat) GetAd() []*Response_Seat_Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

type Response_TraceInfo_DspStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DspName    *string `protobuf:"bytes,1,opt,name=dsp_name,json=dspName" json:"dsp_name,omitempty"`
	CreativeId *string `protobuf:"bytes,2,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	Status     *int32  `protobuf:"varint,3,opt,name=status" json:"status,omitempty"` // 广告状态码
	Msg        *string `protobuf:"bytes,4,opt,name=msg" json:"msg,omitempty"`
}

func (x *Response_TraceInfo_DspStatus) Reset() {
	*x = Response_TraceInfo_DspStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_TraceInfo_DspStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_TraceInfo_DspStatus) ProtoMessage() {}

func (x *Response_TraceInfo_DspStatus) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_TraceInfo_DspStatus.ProtoReflect.Descriptor instead.
func (*Response_TraceInfo_DspStatus) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *Response_TraceInfo_DspStatus) GetDspName() string {
	if x != nil && x.DspName != nil {
		return *x.DspName
	}
	return ""
}

func (x *Response_TraceInfo_DspStatus) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *Response_TraceInfo_DspStatus) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *Response_TraceInfo_DspStatus) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

// 广告字段
type Response_Seat_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 广告序号，为0
	Id        *int32  `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	SessionId *string `protobuf:"bytes,27,opt,name=session_id,json=sessionId" json:"session_id,omitempty"` // 广告全局唯一标识ID
	// 广告代码片段，html/imgpath/displayurl
	Adcontent *string `protobuf:"bytes,2,opt,name=adcontent" json:"adcontent,omitempty"`
	// 创意类型
	// 1 文字 2 图片 3 Flash 4 视频
	CreativeType *int32 `protobuf:"varint,3,opt,name=creative_type,json=creativeType" json:"creative_type,omitempty"`
	// 广告类目
	Category []uint64 `protobuf:"varint,4,rep,name=category" json:"category,omitempty"`
	// 最终目标landing页
	DestinationUrl []string `protobuf:"bytes,5,rep,name=destination_url,json=destinationUrl" json:"destination_url,omitempty"`
	// 展现反馈地址
	ImpressionTrackingUrl []string `protobuf:"bytes,6,rep,name=impression_tracking_url,json=impressionTrackingUrl" json:"impression_tracking_url,omitempty"`
	// 点击跳转地址
	ClickThroughUrl *string `protobuf:"bytes,7,opt,name=click_through_url,json=clickThroughUrl" json:"click_through_url,omitempty"`
	// 点击跟踪地址
	ClickTrackingUrl []string                   `protobuf:"bytes,8,rep,name=click_tracking_url,json=clickTrackingUrl" json:"click_tracking_url,omitempty"`
	NativeAd         *Response_Seat_Ad_NativeAd `protobuf:"bytes,10,opt,name=native_ad,json=nativeAd" json:"native_ad,omitempty"`
	// 需要的api支持
	Api []int32 `protobuf:"varint,11,rep,name=api" json:"api,omitempty"`
	// 指定媒体deal_id
	DealId *string `protobuf:"bytes,12,opt,name=deal_id,json=dealId" json:"deal_id,omitempty"`
	// 计划投放日期, 仅开屏使用，如:"20160602"
	CampaignDate *string `protobuf:"bytes,13,opt,name=campaign_date,json=campaignDate" json:"campaign_date,omitempty"`
	// 计划投放日期，优先使用campaign_date，天级曝光控制
	// begin_time和end_time如存在则只能在其区间内才可曝光
	BeginTime *uint64 `protobuf:"varint,28,opt,name=begin_time,json=beginTime" json:"begin_time,omitempty"`
	EndTime   *uint64 `protobuf:"varint,29,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	// 广告创意的唯一标识
	CreativeId *string `protobuf:"bytes,14,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	// 广告来源
	AdSource *string `protobuf:"bytes,15,opt,name=ad_source,json=adSource" json:"ad_source,omitempty"`
	// APP唤醒地址
	DeeplinkUrl *string `protobuf:"bytes,16,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`
	// APP下载地址
	DownloadUrl *string `protobuf:"bytes,17,opt,name=download_url,json=downloadUrl" json:"download_url,omitempty"`
	// 广告第一报价,内部使用,单位(分)
	Price *int32 `protobuf:"varint,18,opt,name=price" json:"price,omitempty"`
	// 广告结算价,内部使用,单位(分)
	SettlePrice *int32 `protobuf:"varint,19,opt,name=settle_price,json=settlePrice" json:"settle_price,omitempty"`
	// adx场景, 返回报价供上游adx竞价, 单位(分)
	BidPrice   *int32                         `protobuf:"varint,20,opt,name=bid_price,json=bidPrice" json:"bid_price,omitempty"`
	EventTrack []*Response_Seat_Ad_EventTrack `protobuf:"bytes,21,rep,name=event_track,json=eventTrack" json:"event_track,omitempty"`
	// 落地页打开方式：4-universal link, 7-微信小程序
	OpenType *int32 `protobuf:"varint,22,opt,name=open_type,json=openType" json:"open_type,omitempty"`
	// 竞价成功通知，服务端发送
	WinnoticeUrl *string `protobuf:"bytes,23,opt,name=winnotice_url,json=winnoticeUrl" json:"winnotice_url,omitempty"`
	// 部分特殊场景需要，用法联系运营同学
	Type *int32 `protobuf:"varint,24,opt,name=type" json:"type,omitempty"`
	// 广告主ID
	AdvertiserId *uint64                      `protobuf:"varint,25,opt,name=advertiser_id,json=advertiserId" json:"advertiser_id,omitempty"`
	UserInfos    []*Response_Seat_Ad_UserInfo `protobuf:"bytes,26,rep,name=user_infos,json=userInfos" json:"user_infos,omitempty"`
	// 扩展字段，DSP自定义数据
	ExtendData *string                   `protobuf:"bytes,30,opt,name=extend_data,json=extendData" json:"extend_data,omitempty"`
	MiniApp    *Response_Seat_Ad_MiniApp `protobuf:"bytes,31,opt,name=mini_app,json=miniApp" json:"mini_app,omitempty"`
	// 期望广告展示的开始时间
	// 如果请求广告位上的start_delay是X，则该字段的有效值是 X + N * 15，N = 0, 1, 2, ...
	StartDelay *int32 `protobuf:"varint,32,opt,name=start_delay,json=startDelay" json:"start_delay,omitempty"`
	// 竞价失败通知
	LossNoticeUrl *string `protobuf:"bytes,33,opt,name=loss_notice_url,json=lossNoticeUrl" json:"loss_notice_url,omitempty"`
}

func (x *Response_Seat_Ad) Reset() {
	*x = Response_Seat_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad) ProtoMessage() {}

func (x *Response_Seat_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *Response_Seat_Ad) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Response_Seat_Ad) GetSessionId() string {
	if x != nil && x.SessionId != nil {
		return *x.SessionId
	}
	return ""
}

func (x *Response_Seat_Ad) GetAdcontent() string {
	if x != nil && x.Adcontent != nil {
		return *x.Adcontent
	}
	return ""
}

func (x *Response_Seat_Ad) GetCreativeType() int32 {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return 0
}

func (x *Response_Seat_Ad) GetCategory() []uint64 {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Response_Seat_Ad) GetDestinationUrl() []string {
	if x != nil {
		return x.DestinationUrl
	}
	return nil
}

func (x *Response_Seat_Ad) GetImpressionTrackingUrl() []string {
	if x != nil {
		return x.ImpressionTrackingUrl
	}
	return nil
}

func (x *Response_Seat_Ad) GetClickThroughUrl() string {
	if x != nil && x.ClickThroughUrl != nil {
		return *x.ClickThroughUrl
	}
	return ""
}

func (x *Response_Seat_Ad) GetClickTrackingUrl() []string {
	if x != nil {
		return x.ClickTrackingUrl
	}
	return nil
}

func (x *Response_Seat_Ad) GetNativeAd() *Response_Seat_Ad_NativeAd {
	if x != nil {
		return x.NativeAd
	}
	return nil
}

func (x *Response_Seat_Ad) GetApi() []int32 {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *Response_Seat_Ad) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *Response_Seat_Ad) GetCampaignDate() string {
	if x != nil && x.CampaignDate != nil {
		return *x.CampaignDate
	}
	return ""
}

func (x *Response_Seat_Ad) GetBeginTime() uint64 {
	if x != nil && x.BeginTime != nil {
		return *x.BeginTime
	}
	return 0
}

func (x *Response_Seat_Ad) GetEndTime() uint64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *Response_Seat_Ad) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *Response_Seat_Ad) GetAdSource() string {
	if x != nil && x.AdSource != nil {
		return *x.AdSource
	}
	return ""
}

func (x *Response_Seat_Ad) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *Response_Seat_Ad) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *Response_Seat_Ad) GetPrice() int32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *Response_Seat_Ad) GetSettlePrice() int32 {
	if x != nil && x.SettlePrice != nil {
		return *x.SettlePrice
	}
	return 0
}

func (x *Response_Seat_Ad) GetBidPrice() int32 {
	if x != nil && x.BidPrice != nil {
		return *x.BidPrice
	}
	return 0
}

func (x *Response_Seat_Ad) GetEventTrack() []*Response_Seat_Ad_EventTrack {
	if x != nil {
		return x.EventTrack
	}
	return nil
}

func (x *Response_Seat_Ad) GetOpenType() int32 {
	if x != nil && x.OpenType != nil {
		return *x.OpenType
	}
	return 0
}

func (x *Response_Seat_Ad) GetWinnoticeUrl() string {
	if x != nil && x.WinnoticeUrl != nil {
		return *x.WinnoticeUrl
	}
	return ""
}

func (x *Response_Seat_Ad) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Response_Seat_Ad) GetAdvertiserId() uint64 {
	if x != nil && x.AdvertiserId != nil {
		return *x.AdvertiserId
	}
	return 0
}

func (x *Response_Seat_Ad) GetUserInfos() []*Response_Seat_Ad_UserInfo {
	if x != nil {
		return x.UserInfos
	}
	return nil
}

func (x *Response_Seat_Ad) GetExtendData() string {
	if x != nil && x.ExtendData != nil {
		return *x.ExtendData
	}
	return ""
}

func (x *Response_Seat_Ad) GetMiniApp() *Response_Seat_Ad_MiniApp {
	if x != nil {
		return x.MiniApp
	}
	return nil
}

func (x *Response_Seat_Ad) GetStartDelay() int32 {
	if x != nil && x.StartDelay != nil {
		return *x.StartDelay
	}
	return 0
}

func (x *Response_Seat_Ad) GetLossNoticeUrl() string {
	if x != nil && x.LossNoticeUrl != nil {
		return *x.LossNoticeUrl
	}
	return ""
}

type Response_Seat_Ad_NativeAd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attr []*Response_Seat_Ad_NativeAd_Attr `protobuf:"bytes,1,rep,name=attr" json:"attr,omitempty"`
	// natvie模板id
	TemplateId *int32 `protobuf:"varint,2,opt,name=template_id,json=templateId" json:"template_id,omitempty"`
}

func (x *Response_Seat_Ad_NativeAd) Reset() {
	*x = Response_Seat_Ad_NativeAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad_NativeAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad_NativeAd) ProtoMessage() {}

func (x *Response_Seat_Ad_NativeAd) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad_NativeAd.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad_NativeAd) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0, 0}
}

func (x *Response_Seat_Ad_NativeAd) GetAttr() []*Response_Seat_Ad_NativeAd_Attr {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *Response_Seat_Ad_NativeAd) GetTemplateId() int32 {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return 0
}

// 事件监测url
type Response_Seat_Ad_EventTrack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1:播放开始监测;2:播放中间监测;3:播放完毕监测;4:主动播放开始监测;5:用户互动数据监测
	Type *uint32  `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	Url  []string `protobuf:"bytes,2,rep,name=url" json:"url,omitempty"`
	// 此时间后触发监测，相对时间，单位：秒
	Time *uint32 `protobuf:"varint,3,opt,name=time" json:"time,omitempty"`
}

func (x *Response_Seat_Ad_EventTrack) Reset() {
	*x = Response_Seat_Ad_EventTrack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad_EventTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad_EventTrack) ProtoMessage() {}

func (x *Response_Seat_Ad_EventTrack) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad_EventTrack.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad_EventTrack) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0, 1}
}

func (x *Response_Seat_Ad_EventTrack) GetType() uint32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Response_Seat_Ad_EventTrack) GetUrl() []string {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *Response_Seat_Ad_EventTrack) GetTime() uint32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

// 用户评估数据
type Response_Seat_Ad_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    *int32   `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`        // 用户评估分类：1-用户质量评估
	Quality *float64 `protobuf:"fixed64,2,opt,name=quality" json:"quality,omitempty"` // 用户评估分值
}

func (x *Response_Seat_Ad_UserInfo) Reset() {
	*x = Response_Seat_Ad_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad_UserInfo) ProtoMessage() {}

func (x *Response_Seat_Ad_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad_UserInfo.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad_UserInfo) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0, 2}
}

func (x *Response_Seat_Ad_UserInfo) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Response_Seat_Ad_UserInfo) GetQuality() float64 {
	if x != nil && x.Quality != nil {
		return *x.Quality
	}
	return 0
}

type Response_Seat_Ad_MiniApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 小程序所在的主APP客户端，1-微信，表示调用微信SDK唤起微信小程序
	HostApp *int32 `protobuf:"varint,1,req,name=host_app,json=hostApp" json:"host_app,omitempty"`
	// 主APP内的小程序ID
	AppId *string `protobuf:"bytes,2,req,name=app_id,json=appId" json:"app_id,omitempty"`
	// 小程序path
	Path *string `protobuf:"bytes,3,req,name=path" json:"path,omitempty"`
}

func (x *Response_Seat_Ad_MiniApp) Reset() {
	*x = Response_Seat_Ad_MiniApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad_MiniApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad_MiniApp) ProtoMessage() {}

func (x *Response_Seat_Ad_MiniApp) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad_MiniApp.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad_MiniApp) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0, 3}
}

func (x *Response_Seat_Ad_MiniApp) GetHostApp() int32 {
	if x != nil && x.HostApp != nil {
		return *x.HostApp
	}
	return 0
}

func (x *Response_Seat_Ad_MiniApp) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *Response_Seat_Ad_MiniApp) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

// 属性（描述）信息
type Response_Seat_Ad_NativeAd_Attr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 属性名
	Name *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	// 属性值
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *Response_Seat_Ad_NativeAd_Attr) Reset() {
	*x = Response_Seat_Ad_NativeAd_Attr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_ssp_20240715_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Seat_Ad_NativeAd_Attr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Seat_Ad_NativeAd_Attr) ProtoMessage() {}

func (x *Response_Seat_Ad_NativeAd_Attr) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_ssp_20240715_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Seat_Ad_NativeAd_Attr.ProtoReflect.Descriptor instead.
func (*Response_Seat_Ad_NativeAd_Attr) Descriptor() ([]byte, []int) {
	return file_tanx_ssp_20240715_proto_rawDescGZIP(), []int{1, 1, 0, 0, 0}
}

func (x *Response_Seat_Ad_NativeAd_Attr) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Response_Seat_Ad_NativeAd_Attr) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

var File_tanx_ssp_20240715_proto protoreflect.FileDescriptor

var file_tanx_ssp_20240715_proto_rawDesc = []byte{
	0x0a, 0x17, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x73, 0x73, 0x70, 0x5f, 0x32, 0x30, 0x32, 0x34, 0x30,
	0x37, 0x31, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x74, 0x61, 0x6e, 0x78, 0x5f,
	0x75, 0x70, 0x22, 0xa1, 0x1a, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x52, 0x04, 0x73, 0x69,
	0x74, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x4b, 0x65, 0x79,
	0x12, 0x2c, 0x0a, 0x0e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x0d, 0x68, 0x74, 0x74, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x24,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a,
	0x15, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x04, 0x52, 0x13, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x13, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x12, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x69, 0x73, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x86, 0x06, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x70, 0x6f, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x2a, 0x0a,
	0x0d, 0x69, 0x73, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0c, 0x69, 0x73, 0x46,
	0x75, 0x6c, 0x6c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70, 0x69,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12, 0x1c, 0x0a, 0x08, 0x73,
	0x6c, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31,
	0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x65, 0x61,
	0x6c, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75,
	0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x04, 0x64, 0x65, 0x61, 0x6c, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12,
	0x4c, 0x0a, 0x0d, 0x74, 0x6f, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x6f, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x0c, 0x74, 0x6f, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x1a, 0x9e, 0x01,
	0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3c,
	0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x02,
	0x28, 0x05, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x1a, 0x2e, 0x0a, 0x0b,
	0x54, 0x6f, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x1a, 0x87, 0x03, 0x0a,
	0x04, 0x53, 0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x53, 0x69, 0x74, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x1a, 0x8d, 0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x95, 0x08, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69,
	0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73,
	0x76, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x1b, 0x0a, 0x07,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0b, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x04, 0x31, 0x30, 0x30, 0x30, 0x52, 0x0a,
	0x70, 0x69, 0x78, 0x65, 0x6c, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72,
	0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x0f,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x03, 0x34, 0x38, 0x30, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x03, 0x67, 0x65,
	0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75,
	0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x16, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6c, 0x69, 0x5f, 0x61, 0x61, 0x69, 0x64, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6c, 0x69, 0x41, 0x61, 0x69, 0x64, 0x12, 0x32, 0x0a,
	0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x41, 0x49, 0x44, 0x52, 0x05, 0x63, 0x61, 0x69, 0x64,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61,
	0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61,
	0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x65, 0x69,
	0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x66,
	0x61, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x61, 0x69, 0x64, 0x5f,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x61,
	0x69, 0x64, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x75, 0x64, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x6e, 0x55, 0x64, 0x69, 0x64, 0x1a, 0x29, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e,
	0x1a, 0x2c, 0x0a, 0x04, 0x43, 0x41, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x1a, 0x79,
	0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xfa, 0x01, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x61, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x70, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x74, 0x64, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x75, 0x74, 0x64, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x6f, 0x62,
	0x61, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x6f,
	0x62, 0x61, 0x6f, 0x49, 0x64, 0x1a, 0x2f, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xe5, 0x10, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x3c, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x2a,
	0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x11, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0f, 0x75, 0x73,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x87,
	0x02, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69,
	0x6d, 0x70, 0x49, 0x64, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x44, 0x0a, 0x0a, 0x64, 0x73, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x44, 0x73, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x64, 0x73, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x71, 0x0a, 0x09, 0x44, 0x73, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0xd3, 0x0c, 0x0a, 0x04, 0x53, 0x65, 0x61,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x29, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x1a, 0x8f, 0x0c, 0x0a,
	0x02, 0x41, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55,
	0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x6f,
	0x75, 0x67, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x54, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x3f, 0x0a, 0x09,
	0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x41, 0x64, 0x52, 0x08, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x70, 0x69, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x61, 0x70, 0x69, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75, 0x70,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e, 0x41,
	0x64, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69,
	0x6e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x75,
	0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x2e,
	0x41, 0x64, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x08, 0x6d, 0x69, 0x6e, 0x69, 0x5f,
	0x61, 0x70, 0x70, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x52, 0x07, 0x6d, 0x69,
	0x6e, 0x69, 0x41, 0x70, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x73, 0x73, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x1a, 0x9a,
	0x01, 0x0a, 0x08, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x12, 0x3b, 0x0a, 0x04, 0x61,
	0x74, 0x74, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x75, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x2e, 0x41, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x2e, 0x41, 0x74,
	0x74, 0x72, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x1a, 0x30, 0x0a, 0x04, 0x41, 0x74, 0x74,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x46, 0x0a, 0x0a, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x1a, 0x38, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x1a, 0x4f, 0x0a,
	0x07, 0x4d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x61, 0x70, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x07, 0x68, 0x6f, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x42, 0x15,
	0x5a, 0x13, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x61,
	0x6e, 0x78, 0x5f, 0x75, 0x70,
}

var (
	file_tanx_ssp_20240715_proto_rawDescOnce sync.Once
	file_tanx_ssp_20240715_proto_rawDescData = file_tanx_ssp_20240715_proto_rawDesc
)

func file_tanx_ssp_20240715_proto_rawDescGZIP() []byte {
	file_tanx_ssp_20240715_proto_rawDescOnce.Do(func() {
		file_tanx_ssp_20240715_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanx_ssp_20240715_proto_rawDescData)
	})
	return file_tanx_ssp_20240715_proto_rawDescData
}

var file_tanx_ssp_20240715_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_tanx_ssp_20240715_proto_goTypes = []interface{}{
	(*Request)(nil),                        // 0: tanx_up.Request
	(*Response)(nil),                       // 1: tanx_up.Response
	(*Request_Impression)(nil),             // 2: tanx_up.Request.Impression
	(*Request_Site)(nil),                   // 3: tanx_up.Request.Site
	(*Request_Device)(nil),                 // 4: tanx_up.Request.Device
	(*Request_App)(nil),                    // 5: tanx_up.Request.App
	(*Request_User)(nil),                   // 6: tanx_up.Request.User
	(*Request_Impression_Video)(nil),       // 7: tanx_up.Request.Impression.Video
	(*Request_Impression_Deal)(nil),        // 8: tanx_up.Request.Impression.Deal
	(*Request_Impression_TopCreative)(nil), // 9: tanx_up.Request.Impression.TopCreative
	(*Request_Site_Content)(nil),           // 10: tanx_up.Request.Site.Content
	(*Request_Device_Geo)(nil),             // 11: tanx_up.Request.Device.Geo
	(*Request_Device_CAID)(nil),            // 12: tanx_up.Request.Device.CAID
	(*Request_User_UserTag)(nil),           // 13: tanx_up.Request.User.UserTag
	(*Response_TraceInfo)(nil),             // 14: tanx_up.Response.TraceInfo
	(*Response_Seat)(nil),                  // 15: tanx_up.Response.Seat
	(*Response_TraceInfo_DspStatus)(nil),   // 16: tanx_up.Response.TraceInfo.DspStatus
	(*Response_Seat_Ad)(nil),               // 17: tanx_up.Response.Seat.Ad
	(*Response_Seat_Ad_NativeAd)(nil),      // 18: tanx_up.Response.Seat.Ad.NativeAd
	(*Response_Seat_Ad_EventTrack)(nil),    // 19: tanx_up.Response.Seat.Ad.EventTrack
	(*Response_Seat_Ad_UserInfo)(nil),      // 20: tanx_up.Response.Seat.Ad.UserInfo
	(*Response_Seat_Ad_MiniApp)(nil),       // 21: tanx_up.Response.Seat.Ad.MiniApp
	(*Response_Seat_Ad_NativeAd_Attr)(nil), // 22: tanx_up.Response.Seat.Ad.NativeAd.Attr
}
var file_tanx_ssp_20240715_proto_depIdxs = []int32{
	2,  // 0: tanx_up.Request.imp:type_name -> tanx_up.Request.Impression
	3,  // 1: tanx_up.Request.site:type_name -> tanx_up.Request.Site
	4,  // 2: tanx_up.Request.device:type_name -> tanx_up.Request.Device
	5,  // 3: tanx_up.Request.app:type_name -> tanx_up.Request.App
	6,  // 4: tanx_up.Request.user:type_name -> tanx_up.Request.User
	14, // 5: tanx_up.Response.trace_infos:type_name -> tanx_up.Response.TraceInfo
	15, // 6: tanx_up.Response.seat:type_name -> tanx_up.Response.Seat
	7,  // 7: tanx_up.Request.Impression.video:type_name -> tanx_up.Request.Impression.Video
	8,  // 8: tanx_up.Request.Impression.deal:type_name -> tanx_up.Request.Impression.Deal
	9,  // 9: tanx_up.Request.Impression.top_creatives:type_name -> tanx_up.Request.Impression.TopCreative
	10, // 10: tanx_up.Request.Site.content:type_name -> tanx_up.Request.Site.Content
	11, // 11: tanx_up.Request.Device.geo:type_name -> tanx_up.Request.Device.Geo
	12, // 12: tanx_up.Request.Device.caids:type_name -> tanx_up.Request.Device.CAID
	13, // 13: tanx_up.Request.User.user_tags:type_name -> tanx_up.Request.User.UserTag
	16, // 14: tanx_up.Response.TraceInfo.dsp_status:type_name -> tanx_up.Response.TraceInfo.DspStatus
	17, // 15: tanx_up.Response.Seat.ad:type_name -> tanx_up.Response.Seat.Ad
	18, // 16: tanx_up.Response.Seat.Ad.native_ad:type_name -> tanx_up.Response.Seat.Ad.NativeAd
	19, // 17: tanx_up.Response.Seat.Ad.event_track:type_name -> tanx_up.Response.Seat.Ad.EventTrack
	20, // 18: tanx_up.Response.Seat.Ad.user_infos:type_name -> tanx_up.Response.Seat.Ad.UserInfo
	21, // 19: tanx_up.Response.Seat.Ad.mini_app:type_name -> tanx_up.Response.Seat.Ad.MiniApp
	22, // 20: tanx_up.Response.Seat.Ad.NativeAd.attr:type_name -> tanx_up.Response.Seat.Ad.NativeAd.Attr
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_tanx_ssp_20240715_proto_init() }
func file_tanx_ssp_20240715_proto_init() {
	if File_tanx_ssp_20240715_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanx_ssp_20240715_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Impression_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Impression_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Impression_TopCreative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Site_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_CAID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_User_UserTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_TraceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_TraceInfo_DspStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad_NativeAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad_EventTrack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad_MiniApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_ssp_20240715_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Seat_Ad_NativeAd_Attr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanx_ssp_20240715_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tanx_ssp_20240715_proto_goTypes,
		DependencyIndexes: file_tanx_ssp_20240715_proto_depIdxs,
		MessageInfos:      file_tanx_ssp_20240715_proto_msgTypes,
	}.Build()
	File_tanx_ssp_20240715_proto = out.File
	file_tanx_ssp_20240715_proto_rawDesc = nil
	file_tanx_ssp_20240715_proto_goTypes = nil
	file_tanx_ssp_20240715_proto_depIdxs = nil
}
