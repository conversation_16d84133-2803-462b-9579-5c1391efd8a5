//All Rights Reserved.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: vulcan_2.4.proto

package zuiyou_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 设备类型
type VulcanRequest_Device_DeviceType int32

const (
	VulcanRequest_Device_DEVICE_UNKNOWN VulcanRequest_Device_DeviceType = 0
	VulcanRequest_Device_PHONE          VulcanRequest_Device_DeviceType = 1 // 手机。
	VulcanRequest_Device_TABLET         VulcanRequest_Device_DeviceType = 2 // 平板。
	VulcanRequest_Device_OTT            VulcanRequest_Device_DeviceType = 3 //OTT设备.
)

// Enum value maps for VulcanRequest_Device_DeviceType.
var (
	VulcanRequest_Device_DeviceType_name = map[int32]string{
		0: "DEVICE_UNKNOWN",
		1: "PHONE",
		2: "TABLET",
		3: "OTT",
	}
	VulcanRequest_Device_DeviceType_value = map[string]int32{
		"DEVICE_UNKNOWN": 0,
		"PHONE":          1,
		"TABLET":         2,
		"OTT":            3,
	}
)

func (x VulcanRequest_Device_DeviceType) Enum() *VulcanRequest_Device_DeviceType {
	p := new(VulcanRequest_Device_DeviceType)
	*p = x
	return p
}

func (x VulcanRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[0].Descriptor()
}

func (VulcanRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[0]
}

func (x VulcanRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Device_DeviceType.Descriptor instead.
func (VulcanRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 0}
}

// 操作系统类型
type VulcanRequest_Device_OsType int32

const (
	VulcanRequest_Device_OS_UNKNOWN VulcanRequest_Device_OsType = 0
	VulcanRequest_Device_ANDROID    VulcanRequest_Device_OsType = 1
	VulcanRequest_Device_IOS        VulcanRequest_Device_OsType = 2
)

// Enum value maps for VulcanRequest_Device_OsType.
var (
	VulcanRequest_Device_OsType_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "ANDROID",
		2: "IOS",
	}
	VulcanRequest_Device_OsType_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"ANDROID":    1,
		"IOS":        2,
	}
)

func (x VulcanRequest_Device_OsType) Enum() *VulcanRequest_Device_OsType {
	p := new(VulcanRequest_Device_OsType)
	*p = x
	return p
}

func (x VulcanRequest_Device_OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Device_OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[1].Descriptor()
}

func (VulcanRequest_Device_OsType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[1]
}

func (x VulcanRequest_Device_OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Device_OsType.Descriptor instead.
func (VulcanRequest_Device_OsType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 1}
}

// 网络类型
type VulcanRequest_Device_ConnectionType int32

const (
	VulcanRequest_Device_CONN_UNKNOWN VulcanRequest_Device_ConnectionType = 0
	VulcanRequest_Device_WIFI         VulcanRequest_Device_ConnectionType = 1
	VulcanRequest_Device_MOBILE_2G    VulcanRequest_Device_ConnectionType = 2
	VulcanRequest_Device_MOBILE_3G    VulcanRequest_Device_ConnectionType = 3
	VulcanRequest_Device_MOBILE_4G    VulcanRequest_Device_ConnectionType = 4
	VulcanRequest_Device_MOBILE_5G    VulcanRequest_Device_ConnectionType = 5
)

// Enum value maps for VulcanRequest_Device_ConnectionType.
var (
	VulcanRequest_Device_ConnectionType_name = map[int32]string{
		0: "CONN_UNKNOWN",
		1: "WIFI",
		2: "MOBILE_2G",
		3: "MOBILE_3G",
		4: "MOBILE_4G",
		5: "MOBILE_5G",
	}
	VulcanRequest_Device_ConnectionType_value = map[string]int32{
		"CONN_UNKNOWN": 0,
		"WIFI":         1,
		"MOBILE_2G":    2,
		"MOBILE_3G":    3,
		"MOBILE_4G":    4,
		"MOBILE_5G":    5,
	}
)

func (x VulcanRequest_Device_ConnectionType) Enum() *VulcanRequest_Device_ConnectionType {
	p := new(VulcanRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x VulcanRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[2].Descriptor()
}

func (VulcanRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[2]
}

func (x VulcanRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Device_ConnectionType.Descriptor instead.
func (VulcanRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 2}
}

// 运营商类型
type VulcanRequest_Device_CarrierType int32

const (
	VulcanRequest_Device_CARRIER_UNKNOWN VulcanRequest_Device_CarrierType = 0
	VulcanRequest_Device_MOBILE          VulcanRequest_Device_CarrierType = 1
	VulcanRequest_Device_UNICOM          VulcanRequest_Device_CarrierType = 2
	VulcanRequest_Device_TELECOM         VulcanRequest_Device_CarrierType = 3
)

// Enum value maps for VulcanRequest_Device_CarrierType.
var (
	VulcanRequest_Device_CarrierType_name = map[int32]string{
		0: "CARRIER_UNKNOWN",
		1: "MOBILE",
		2: "UNICOM",
		3: "TELECOM",
	}
	VulcanRequest_Device_CarrierType_value = map[string]int32{
		"CARRIER_UNKNOWN": 0,
		"MOBILE":          1,
		"UNICOM":          2,
		"TELECOM":         3,
	}
)

func (x VulcanRequest_Device_CarrierType) Enum() *VulcanRequest_Device_CarrierType {
	p := new(VulcanRequest_Device_CarrierType)
	*p = x
	return p
}

func (x VulcanRequest_Device_CarrierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Device_CarrierType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[3].Descriptor()
}

func (VulcanRequest_Device_CarrierType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[3]
}

func (x VulcanRequest_Device_CarrierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Device_CarrierType.Descriptor instead.
func (VulcanRequest_Device_CarrierType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 3}
}

type VulcanRequest_User_Gender int32

const (
	VulcanRequest_User_UNKNOWN VulcanRequest_User_Gender = 0
	VulcanRequest_User_MALE    VulcanRequest_User_Gender = 1
	VulcanRequest_User_FEMALE  VulcanRequest_User_Gender = 2
)

// Enum value maps for VulcanRequest_User_Gender.
var (
	VulcanRequest_User_Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	VulcanRequest_User_Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x VulcanRequest_User_Gender) Enum() *VulcanRequest_User_Gender {
	p := new(VulcanRequest_User_Gender)
	*p = x
	return p
}

func (x VulcanRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[4].Descriptor()
}

func (VulcanRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[4]
}

func (x VulcanRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_User_Gender.Descriptor instead.
func (VulcanRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 2, 0}
}

type VulcanRequest_Adslot_AdType int32

const (
	VulcanRequest_Adslot_NATIVE VulcanRequest_Adslot_AdType = 0 //原生广告
	VulcanRequest_Adslot_SPLASH VulcanRequest_Adslot_AdType = 1 //开屏广告
	VulcanRequest_Adslot_REWARD VulcanRequest_Adslot_AdType = 2 //激励视频广告
	VulcanRequest_Adslot_DRAW   VulcanRequest_Adslot_AdType = 3 //Draw视频广告
)

// Enum value maps for VulcanRequest_Adslot_AdType.
var (
	VulcanRequest_Adslot_AdType_name = map[int32]string{
		0: "NATIVE",
		1: "SPLASH",
		2: "REWARD",
		3: "DRAW",
	}
	VulcanRequest_Adslot_AdType_value = map[string]int32{
		"NATIVE": 0,
		"SPLASH": 1,
		"REWARD": 2,
		"DRAW":   3,
	}
)

func (x VulcanRequest_Adslot_AdType) Enum() *VulcanRequest_Adslot_AdType {
	p := new(VulcanRequest_Adslot_AdType)
	*p = x
	return p
}

func (x VulcanRequest_Adslot_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Adslot_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[5].Descriptor()
}

func (VulcanRequest_Adslot_AdType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[5]
}

func (x VulcanRequest_Adslot_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Adslot_AdType.Descriptor instead.
func (VulcanRequest_Adslot_AdType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 3, 0}
}

type VulcanRequest_Adslot_MaterialType int32

const (
	VulcanRequest_Adslot_ONE_IMG   VulcanRequest_Adslot_MaterialType = 0 //支持单图素材
	VulcanRequest_Adslot_VIDEO     VulcanRequest_Adslot_MaterialType = 1 //支持视频素材
	VulcanRequest_Adslot_THREE_IMG VulcanRequest_Adslot_MaterialType = 2 //支持三图素材
	VulcanRequest_Adslot_NINE_IMG  VulcanRequest_Adslot_MaterialType = 3 //支持九图素材
)

// Enum value maps for VulcanRequest_Adslot_MaterialType.
var (
	VulcanRequest_Adslot_MaterialType_name = map[int32]string{
		0: "ONE_IMG",
		1: "VIDEO",
		2: "THREE_IMG",
		3: "NINE_IMG",
	}
	VulcanRequest_Adslot_MaterialType_value = map[string]int32{
		"ONE_IMG":   0,
		"VIDEO":     1,
		"THREE_IMG": 2,
		"NINE_IMG":  3,
	}
)

func (x VulcanRequest_Adslot_MaterialType) Enum() *VulcanRequest_Adslot_MaterialType {
	p := new(VulcanRequest_Adslot_MaterialType)
	*p = x
	return p
}

func (x VulcanRequest_Adslot_MaterialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Adslot_MaterialType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[6].Descriptor()
}

func (VulcanRequest_Adslot_MaterialType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[6]
}

func (x VulcanRequest_Adslot_MaterialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Adslot_MaterialType.Descriptor instead.
func (VulcanRequest_Adslot_MaterialType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 3, 1}
}

type VulcanRequest_Adslot_InteractionType int32

const (
	VulcanRequest_Adslot_LANDING_PAGE VulcanRequest_Adslot_InteractionType = 0 //落地页
	VulcanRequest_Adslot_INVOKE       VulcanRequest_Adslot_InteractionType = 1 //唤起
	VulcanRequest_Adslot_DOWNLOAD     VulcanRequest_Adslot_InteractionType = 2 //下载
	VulcanRequest_Adslot_BROWSER      VulcanRequest_Adslot_InteractionType = 3 //打开外部浏览器
)

// Enum value maps for VulcanRequest_Adslot_InteractionType.
var (
	VulcanRequest_Adslot_InteractionType_name = map[int32]string{
		0: "LANDING_PAGE",
		1: "INVOKE",
		2: "DOWNLOAD",
		3: "BROWSER",
	}
	VulcanRequest_Adslot_InteractionType_value = map[string]int32{
		"LANDING_PAGE": 0,
		"INVOKE":       1,
		"DOWNLOAD":     2,
		"BROWSER":      3,
	}
)

func (x VulcanRequest_Adslot_InteractionType) Enum() *VulcanRequest_Adslot_InteractionType {
	p := new(VulcanRequest_Adslot_InteractionType)
	*p = x
	return p
}

func (x VulcanRequest_Adslot_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanRequest_Adslot_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[7].Descriptor()
}

func (VulcanRequest_Adslot_InteractionType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[7]
}

func (x VulcanRequest_Adslot_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanRequest_Adslot_InteractionType.Descriptor instead.
func (VulcanRequest_Adslot_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 3, 2}
}

type VulcanResponse_Status int32

const (
	VulcanResponse_REQ_OK      VulcanResponse_Status = 0 //响应成功并填充广告
	VulcanResponse_REQ_FAIL    VulcanResponse_Status = 1 //响应失败，请求参数错误
	VulcanResponse_DROP_PRICE  VulcanResponse_Status = 2 //不满足价格需求而丢弃
	VulcanResponse_DROP_CHOICE VulcanResponse_Status = 3 //不满足筛选需求而丢弃(例如推荐模型)
	VulcanResponse_DROP_OTHER  VulcanResponse_Status = 4 //其它原因无法填充
	VulcanResponse_DROP_SMART  VulcanResponse_Status = 5 //精准策略丢弃,请勿重复请求
)

// Enum value maps for VulcanResponse_Status.
var (
	VulcanResponse_Status_name = map[int32]string{
		0: "REQ_OK",
		1: "REQ_FAIL",
		2: "DROP_PRICE",
		3: "DROP_CHOICE",
		4: "DROP_OTHER",
		5: "DROP_SMART",
	}
	VulcanResponse_Status_value = map[string]int32{
		"REQ_OK":      0,
		"REQ_FAIL":    1,
		"DROP_PRICE":  2,
		"DROP_CHOICE": 3,
		"DROP_OTHER":  4,
		"DROP_SMART":  5,
	}
)

func (x VulcanResponse_Status) Enum() *VulcanResponse_Status {
	p := new(VulcanResponse_Status)
	*p = x
	return p
}

func (x VulcanResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[8].Descriptor()
}

func (VulcanResponse_Status) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[8]
}

func (x VulcanResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanResponse_Status.Descriptor instead.
func (VulcanResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0}
}

type VulcanResponse_Ad_PriceType int32

const (
	VulcanResponse_Ad_CPM VulcanResponse_Ad_PriceType = 0 //目前均采用CPM方式计费
	VulcanResponse_Ad_CPC VulcanResponse_Ad_PriceType = 1
)

// Enum value maps for VulcanResponse_Ad_PriceType.
var (
	VulcanResponse_Ad_PriceType_name = map[int32]string{
		0: "CPM",
		1: "CPC",
	}
	VulcanResponse_Ad_PriceType_value = map[string]int32{
		"CPM": 0,
		"CPC": 1,
	}
)

func (x VulcanResponse_Ad_PriceType) Enum() *VulcanResponse_Ad_PriceType {
	p := new(VulcanResponse_Ad_PriceType)
	*p = x
	return p
}

func (x VulcanResponse_Ad_PriceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VulcanResponse_Ad_PriceType) Descriptor() protoreflect.EnumDescriptor {
	return file_vulcan_2_4_proto_enumTypes[9].Descriptor()
}

func (VulcanResponse_Ad_PriceType) Type() protoreflect.EnumType {
	return &file_vulcan_2_4_proto_enumTypes[9]
}

func (x VulcanResponse_Ad_PriceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VulcanResponse_Ad_PriceType.Descriptor instead.
func (VulcanResponse_Ad_PriceType) EnumDescriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 0}
}

type VulcanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiVersion string                `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"` // 必填。此API的版本。
	RequestId  string                `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`    // 必填。自定义的请求id，保证其请求的唯一性。
	Timeout    uint32                `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`                        // 必填。超时时间，要求再该时间范围内给予广告返回。
	App        *VulcanRequest_App    `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`                                 //必填。移动app信息。
	Device     *VulcanRequest_Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`                           //必填。移动设备的信息。
	User       *VulcanRequest_User   `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`                               //可选。用户信息，用于人群定向。
	Adslot     *VulcanRequest_Adslot `protobuf:"bytes,7,opt,name=adslot,proto3" json:"adslot,omitempty"`                           // 必填，至少一个。广告位的信息。
	Test       uint32                `protobuf:"varint,8,opt,name=test,proto3" json:"test,omitempty"`                              //可选。
	Ext        string                `protobuf:"bytes,9,opt,name=ext,proto3" json:"ext,omitempty"`                                 //可选。
}

func (x *VulcanRequest) Reset() {
	*x = VulcanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest) ProtoMessage() {}

func (x *VulcanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest.ProtoReflect.Descriptor instead.
func (*VulcanRequest) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0}
}

func (x *VulcanRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *VulcanRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VulcanRequest) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *VulcanRequest) GetApp() *VulcanRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *VulcanRequest) GetDevice() *VulcanRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *VulcanRequest) GetUser() *VulcanRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *VulcanRequest) GetAdslot() *VulcanRequest_Adslot {
	if x != nil {
		return x.Adslot
	}
	return nil
}

func (x *VulcanRequest) GetTest() uint32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *VulcanRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

type VulcanResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseId       string                `protobuf:"bytes,1,opt,name=response_id,json=responseId,proto3" json:"response_id,omitempty"`                                       // 必填。响应id，与请求id保持一致。
	SiteName         string                `protobuf:"bytes,2,opt,name=site_name,json=siteName,proto3" json:"site_name,omitempty"`                                             //必填。平台方名称。
	ProcessingTimeMs uint32                `protobuf:"varint,3,opt,name=processing_time_ms,json=processingTimeMs,proto3" json:"processing_time_ms,omitempty"`                  // 可选。从收到请求到返回响应所用的时间。
	StatusCode       VulcanResponse_Status `protobuf:"varint,4,opt,name=status_code,json=statusCode,proto3,enum=zuiyou_up.VulcanResponse_Status" json:"status_code,omitempty"` // 可选。响应状态码。
	Ads              []*VulcanResponse_Ad  `protobuf:"bytes,5,rep,name=ads,proto3" json:"ads,omitempty"`                                                                       //可选。广告内容。
	ErrMsg           string                `protobuf:"bytes,6,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                                                   //可选。错误信息。
	Ext              string                `protobuf:"bytes,7,opt,name=ext,proto3" json:"ext,omitempty"`                                                                       //可选。拓展。
}

func (x *VulcanResponse) Reset() {
	*x = VulcanResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse) ProtoMessage() {}

func (x *VulcanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse.ProtoReflect.Descriptor instead.
func (*VulcanResponse) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1}
}

func (x *VulcanResponse) GetResponseId() string {
	if x != nil {
		return x.ResponseId
	}
	return ""
}

func (x *VulcanResponse) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *VulcanResponse) GetProcessingTimeMs() uint32 {
	if x != nil {
		return x.ProcessingTimeMs
	}
	return 0
}

func (x *VulcanResponse) GetStatusCode() VulcanResponse_Status {
	if x != nil {
		return x.StatusCode
	}
	return VulcanResponse_REQ_OK
}

func (x *VulcanResponse) GetAds() []*VulcanResponse_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *VulcanResponse) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *VulcanResponse) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

// app
type VulcanRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid       string   `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                                //app应用id，由最右提供。
	Name        string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  // 必填。app应用名。
	PackageName string   `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` //必填。 包名。
	Version     string   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`                            // 必填。 app应用的版本。
	Industry    []string `protobuf:"bytes,5,rep,name=industry,proto3" json:"industry,omitempty"`                          //选填。行业类目。
}

func (x *VulcanRequest_App) Reset() {
	*x = VulcanRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_App) ProtoMessage() {}

func (x *VulcanRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_App.ProtoReflect.Descriptor instead.
func (*VulcanRequest_App) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 0}
}

func (x *VulcanRequest_App) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *VulcanRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VulcanRequest_App) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *VulcanRequest_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *VulcanRequest_App) GetIndustry() []string {
	if x != nil {
		return x.Industry
	}
	return nil
}

// device
type VulcanRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua           string                              `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`                                                                                   //必填。User-Agent。
	Ip           string                              `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                                                                   // 必填。设备的ip，用于定位，地域定向。
	Ipv6         string                              `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`                                                                               //选填。设备的ipv6，用于定位，地域定向。
	Make         string                              `protobuf:"bytes,4,opt,name=make,proto3" json:"make,omitempty"`                                                                               //必填。设备制造商。
	Model        string                              `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`                                                                             //必填。设备型号。
	DeviceType   VulcanRequest_Device_DeviceType     `protobuf:"varint,6,opt,name=device_type,json=deviceType,proto3,enum=zuiyou_up.VulcanRequest_Device_DeviceType" json:"device_type,omitempty"` //必填。设备类型。
	OsType       VulcanRequest_Device_OsType         `protobuf:"varint,7,opt,name=os_type,json=osType,proto3,enum=zuiyou_up.VulcanRequest_Device_OsType" json:"os_type,omitempty"`                 //必填。操作系统类型。
	OsVersion    string                              `protobuf:"bytes,8,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`                                                    //选填。操作系统版本号。
	ConnType     VulcanRequest_Device_ConnectionType `protobuf:"varint,9,opt,name=conn_type,json=connType,proto3,enum=zuiyou_up.VulcanRequest_Device_ConnectionType" json:"conn_type,omitempty"`   // 可选。设备的网络类型。
	Carrier      VulcanRequest_Device_CarrierType    `protobuf:"varint,10,opt,name=carrier,proto3,enum=zuiyou_up.VulcanRequest_Device_CarrierType" json:"carrier,omitempty"`                       // 可选。运营商类型。
	Mac          string                              `protobuf:"bytes,11,opt,name=mac,proto3" json:"mac,omitempty"`                                                                                // 必填。设备的mac地址。
	Idfa         string                              `protobuf:"bytes,12,opt,name=idfa,proto3" json:"idfa,omitempty"`                                                                              // 必填。设备的IDFA。
	IdfaMd5      string                              `protobuf:"bytes,13,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`                                                         // 可选。设备IDFA_MD5。
	Imei         string                              `protobuf:"bytes,14,opt,name=imei,proto3" json:"imei,omitempty"`                                                                              //设备IMEI（明文）。
	ImeiMd5      string                              `protobuf:"bytes,15,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`                                                         //设备IMEI_MD5。
	Oaid         string                              `protobuf:"bytes,16,opt,name=oaid,proto3" json:"oaid,omitempty"`                                                                              // 必填。匿名设备标识符。
	AndroidId    string                              `protobuf:"bytes,17,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`                                                   //设备Android ID。
	Geo          *VulcanRequest_Device_Geo           `protobuf:"bytes,18,opt,name=geo,proto3" json:"geo,omitempty"`                                                                                // 可选。设备的地理位置信息。
	ScreenWidth  uint32                              `protobuf:"varint,19,opt,name=screen_width,json=screenWidth,proto3" json:"screen_width,omitempty"`                                            //设备屏宽。
	ScreenHeight uint32                              `protobuf:"varint,20,opt,name=screen_height,json=screenHeight,proto3" json:"screen_height,omitempty"`                                         //设备屏高。
	StartupTime  string                              `protobuf:"bytes,21,opt,name=startup_time,json=startupTime,proto3" json:"startup_time,omitempty"`                                             //可选。手机开机时间。
	CountryCode  string                              `protobuf:"bytes,22,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`                                             //可选。local地区。
	Language     string                              `protobuf:"bytes,23,opt,name=language,proto3" json:"language,omitempty"`                                                                      //可选。设备设置的语言。
	PhoneName    string                              `protobuf:"bytes,24,opt,name=phone_name,json=phoneName,proto3" json:"phone_name,omitempty"`                                                   //可选。手机名称的MD5码。
	MemTotal     uint64                              `protobuf:"varint,25,opt,name=mem_total,json=memTotal,proto3" json:"mem_total,omitempty"`                                                     //可选。系统总内存空间。
	DiskTotal    uint64                              `protobuf:"varint,26,opt,name=disk_total,json=diskTotal,proto3" json:"disk_total,omitempty"`                                                  //可选。磁盘总空间。
	MbTime       string                              `protobuf:"bytes,27,opt,name=mb_time,json=mbTime,proto3" json:"mb_time,omitempty"`                                                            //可选。系统版本更新时间。
	DeviceModel  string                              `protobuf:"bytes,28,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`                                             //可选。设备model。
	LocalTzTime  string                              `protobuf:"bytes,29,opt,name=local_tz_time,json=localTzTime,proto3" json:"local_tz_time,omitempty"`                                           //可选。local时区。
	OrgModel     string                              `protobuf:"bytes,30,opt,name=org_model,json=orgModel,proto3" json:"org_model,omitempty"`                                                      //可选。iOS机型原始值。
	WxApiVer     int64                               `protobuf:"varint,31,opt,name=wx_api_ver,json=wxApiVer,proto3" json:"wx_api_ver,omitempty"`                                                   //可选。微信内部sdk版本。（安卓）
	WxInstalled  bool                                `protobuf:"varint,32,opt,name=wx_installed,json=wxInstalled,proto3" json:"wx_installed,omitempty"`                                            //可选。是否已安装微信。（iOS）
	WxOpensdkVer string                              `protobuf:"bytes,33,opt,name=wx_opensdk_ver,json=wxOpensdkVer,proto3" json:"wx_opensdk_ver,omitempty"`                                        //可选。微信open sdk的版本
	MacMd5       string                              `protobuf:"bytes,34,opt,name=mac_md5,json=macMd5,proto3" json:"mac_md5,omitempty"`                                                            //可选。设备的mac地址Md5 小写。
	BootMark     string                              `protobuf:"bytes,35,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`                                                      //可选。
	UpdateMark   string                              `protobuf:"bytes,36,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`                                                //可选。
	Caid         string                              `protobuf:"bytes,37,opt,name=caid,proto3" json:"caid,omitempty"`                                                                              //废弃。使用caid_list代替。
	CaidVersion  string                              `protobuf:"bytes,38,opt,name=caid_version,json=caidVersion,proto3" json:"caid_version,omitempty"`                                             //废弃。使用caid_list代替。
	Idfv         string                              `protobuf:"bytes,39,opt,name=idfv,proto3" json:"idfv,omitempty"`                                                                              //iOS必选。
	HwInfo       *VulcanRequest_Device_HWDevInfo     `protobuf:"bytes,40,opt,name=hw_info,json=hwInfo,proto3" json:"hw_info,omitempty"`                                                            //华为设备必选。
	OppoInfo     *VulcanRequest_Device_OppoDevInfo   `protobuf:"bytes,41,opt,name=oppo_info,json=oppoInfo,proto3" json:"oppo_info,omitempty"`                                                      //oppo设备必选。
	VivoInfo     *VulcanRequest_Device_VivoDevInfo   `protobuf:"bytes,42,opt,name=vivo_info,json=vivoInfo,proto3" json:"vivo_info,omitempty"`                                                      //vivo设备必选。
	SLanguage    string                              `protobuf:"bytes,43,opt,name=s_language,json=sLanguage,proto3" json:"s_language,omitempty"`                                                   //可选。设备设置的语言。示例: zh-Hans-CN
	CaidList     []*VulcanRequest_Device_CaidInfo    `protobuf:"bytes,44,rep,name=caid_list,json=caidList,proto3" json:"caid_list,omitempty"`                                                      //可选。
}

func (x *VulcanRequest_Device) Reset() {
	*x = VulcanRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device) ProtoMessage() {}

func (x *VulcanRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1}
}

func (x *VulcanRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *VulcanRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *VulcanRequest_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *VulcanRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *VulcanRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *VulcanRequest_Device) GetDeviceType() VulcanRequest_Device_DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return VulcanRequest_Device_DEVICE_UNKNOWN
}

func (x *VulcanRequest_Device) GetOsType() VulcanRequest_Device_OsType {
	if x != nil {
		return x.OsType
	}
	return VulcanRequest_Device_OS_UNKNOWN
}

func (x *VulcanRequest_Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *VulcanRequest_Device) GetConnType() VulcanRequest_Device_ConnectionType {
	if x != nil {
		return x.ConnType
	}
	return VulcanRequest_Device_CONN_UNKNOWN
}

func (x *VulcanRequest_Device) GetCarrier() VulcanRequest_Device_CarrierType {
	if x != nil {
		return x.Carrier
	}
	return VulcanRequest_Device_CARRIER_UNKNOWN
}

func (x *VulcanRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *VulcanRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *VulcanRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *VulcanRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *VulcanRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *VulcanRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *VulcanRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *VulcanRequest_Device) GetGeo() *VulcanRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *VulcanRequest_Device) GetScreenWidth() uint32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *VulcanRequest_Device) GetScreenHeight() uint32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *VulcanRequest_Device) GetStartupTime() string {
	if x != nil {
		return x.StartupTime
	}
	return ""
}

func (x *VulcanRequest_Device) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *VulcanRequest_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *VulcanRequest_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *VulcanRequest_Device) GetMemTotal() uint64 {
	if x != nil {
		return x.MemTotal
	}
	return 0
}

func (x *VulcanRequest_Device) GetDiskTotal() uint64 {
	if x != nil {
		return x.DiskTotal
	}
	return 0
}

func (x *VulcanRequest_Device) GetMbTime() string {
	if x != nil {
		return x.MbTime
	}
	return ""
}

func (x *VulcanRequest_Device) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *VulcanRequest_Device) GetLocalTzTime() string {
	if x != nil {
		return x.LocalTzTime
	}
	return ""
}

func (x *VulcanRequest_Device) GetOrgModel() string {
	if x != nil {
		return x.OrgModel
	}
	return ""
}

func (x *VulcanRequest_Device) GetWxApiVer() int64 {
	if x != nil {
		return x.WxApiVer
	}
	return 0
}

func (x *VulcanRequest_Device) GetWxInstalled() bool {
	if x != nil {
		return x.WxInstalled
	}
	return false
}

func (x *VulcanRequest_Device) GetWxOpensdkVer() string {
	if x != nil {
		return x.WxOpensdkVer
	}
	return ""
}

func (x *VulcanRequest_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *VulcanRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *VulcanRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *VulcanRequest_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *VulcanRequest_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *VulcanRequest_Device) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *VulcanRequest_Device) GetHwInfo() *VulcanRequest_Device_HWDevInfo {
	if x != nil {
		return x.HwInfo
	}
	return nil
}

func (x *VulcanRequest_Device) GetOppoInfo() *VulcanRequest_Device_OppoDevInfo {
	if x != nil {
		return x.OppoInfo
	}
	return nil
}

func (x *VulcanRequest_Device) GetVivoInfo() *VulcanRequest_Device_VivoDevInfo {
	if x != nil {
		return x.VivoInfo
	}
	return nil
}

func (x *VulcanRequest_Device) GetSLanguage() string {
	if x != nil {
		return x.SLanguage
	}
	return ""
}

func (x *VulcanRequest_Device) GetCaidList() []*VulcanRequest_Device_CaidInfo {
	if x != nil {
		return x.CaidList
	}
	return nil
}

// user
type VulcanRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string                           `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                                                 //可选。用户ID。
	Gender VulcanRequest_User_Gender        `protobuf:"varint,2,opt,name=gender,proto3,enum=zuiyou_up.VulcanRequest_User_Gender" json:"gender,omitempty"` // 可选。用户的性别。
	Age    uint32                           `protobuf:"varint,3,opt,name=age,proto3" json:"age,omitempty"`                                                //可选。年龄。
	Yob    uint32                           `protobuf:"varint,4,opt,name=yob,proto3" json:"yob,omitempty"`                                                //可选。出生年月。
	Apps   []*VulcanRequest_User_InstallApp `protobuf:"bytes,5,rep,name=apps,proto3" json:"apps,omitempty"`                                               //可选。用户安装的app列表
}

func (x *VulcanRequest_User) Reset() {
	*x = VulcanRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_User) ProtoMessage() {}

func (x *VulcanRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_User.ProtoReflect.Descriptor instead.
func (*VulcanRequest_User) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 2}
}

func (x *VulcanRequest_User) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VulcanRequest_User) GetGender() VulcanRequest_User_Gender {
	if x != nil {
		return x.Gender
	}
	return VulcanRequest_User_UNKNOWN
}

func (x *VulcanRequest_User) GetAge() uint32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *VulcanRequest_User) GetYob() uint32 {
	if x != nil {
		return x.Yob
	}
	return 0
}

func (x *VulcanRequest_User) GetApps() []*VulcanRequest_User_InstallApp {
	if x != nil {
		return x.Apps
	}
	return nil
}

// adslot
type VulcanRequest_Adslot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdType           VulcanRequest_Adslot_AdType            `protobuf:"varint,1,opt,name=ad_type,json=adType,proto3,enum=zuiyou_up.VulcanRequest_Adslot_AdType" json:"ad_type,omitempty"`                                               //必填。广告类型。
	MaterialTypes    []VulcanRequest_Adslot_MaterialType    `protobuf:"varint,2,rep,packed,name=material_types,json=materialTypes,proto3,enum=zuiyou_up.VulcanRequest_Adslot_MaterialType" json:"material_types,omitempty"`             //必填。(开屏仅支持图片素材，信息流支持图片和视频。)
	InteractionTypes []VulcanRequest_Adslot_InteractionType `protobuf:"varint,3,rep,packed,name=interaction_types,json=interactionTypes,proto3,enum=zuiyou_up.VulcanRequest_Adslot_InteractionType" json:"interaction_types,omitempty"` //必填。（该广告位支持的交互类型。）
	Price            uint32                                 `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                                                                                                          //必填。协商的底价。
	Size             []*VulcanRequest_Adslot_Size           `protobuf:"bytes,5,rep,name=size,proto3" json:"size,omitempty"`                                                                                                             //选填。当前广告位支持的尺寸。
	TagId            string                                 `protobuf:"bytes,6,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`                                                                                              //必填。广告位标识，由商务侧提供。
	Cnt              uint32                                 `protobuf:"varint,7,opt,name=cnt,proto3" json:"cnt,omitempty"`                                                                                                              //必填。请求广告个数。
}

func (x *VulcanRequest_Adslot) Reset() {
	*x = VulcanRequest_Adslot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Adslot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Adslot) ProtoMessage() {}

func (x *VulcanRequest_Adslot) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Adslot.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Adslot) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 3}
}

func (x *VulcanRequest_Adslot) GetAdType() VulcanRequest_Adslot_AdType {
	if x != nil {
		return x.AdType
	}
	return VulcanRequest_Adslot_NATIVE
}

func (x *VulcanRequest_Adslot) GetMaterialTypes() []VulcanRequest_Adslot_MaterialType {
	if x != nil {
		return x.MaterialTypes
	}
	return nil
}

func (x *VulcanRequest_Adslot) GetInteractionTypes() []VulcanRequest_Adslot_InteractionType {
	if x != nil {
		return x.InteractionTypes
	}
	return nil
}

func (x *VulcanRequest_Adslot) GetPrice() uint32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *VulcanRequest_Adslot) GetSize() []*VulcanRequest_Adslot_Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *VulcanRequest_Adslot) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *VulcanRequest_Adslot) GetCnt() uint32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

// 地理位置
type VulcanRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude  float32 `protobuf:"fixed32,1,opt,name=latitude,proto3" json:"latitude,omitempty"`   //纬度
	Longitude float32 `protobuf:"fixed32,2,opt,name=longitude,proto3" json:"longitude,omitempty"` //经度
	City      string  `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`             //城市，中文即可(utf-8编码)
	Province  string  `protobuf:"bytes,4,opt,name=province,proto3" json:"province,omitempty"`     //省份，中文即可(utf-8编码)
	District  string  `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`     //区县，中文即可(utf-8编码)
}

func (x *VulcanRequest_Device_Geo) Reset() {
	*x = VulcanRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device_Geo) ProtoMessage() {}

func (x *VulcanRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *VulcanRequest_Device_Geo) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *VulcanRequest_Device_Geo) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *VulcanRequest_Device_Geo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *VulcanRequest_Device_Geo) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *VulcanRequest_Device_Geo) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

type VulcanRequest_Device_HWDevInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgVersion  string `protobuf:"bytes,2,opt,name=ag_version,json=agVersion,proto3" json:"ag_version,omitempty"`    //应用市场版本号，verCodeOfAG >= 110002000。
	HmsVersion string `protobuf:"bytes,3,opt,name=hms_version,json=hmsVersion,proto3" json:"hms_version,omitempty"` //HMS Core 版本号,verCodeOfHms >= 50200100。
}

func (x *VulcanRequest_Device_HWDevInfo) Reset() {
	*x = VulcanRequest_Device_HWDevInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device_HWDevInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device_HWDevInfo) ProtoMessage() {}

func (x *VulcanRequest_Device_HWDevInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device_HWDevInfo.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device_HWDevInfo) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 1}
}

func (x *VulcanRequest_Device_HWDevInfo) GetAgVersion() string {
	if x != nil {
		return x.AgVersion
	}
	return ""
}

func (x *VulcanRequest_Device_HWDevInfo) GetHmsVersion() string {
	if x != nil {
		return x.HmsVersion
	}
	return ""
}

type VulcanRequest_Device_OppoDevInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OppoAppstoreVersion string `protobuf:"bytes,1,opt,name=oppo_appstore_version,json=oppoAppstoreVersion,proto3" json:"oppo_appstore_version,omitempty"` //oppo的应用商店版本号
}

func (x *VulcanRequest_Device_OppoDevInfo) Reset() {
	*x = VulcanRequest_Device_OppoDevInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device_OppoDevInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device_OppoDevInfo) ProtoMessage() {}

func (x *VulcanRequest_Device_OppoDevInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device_OppoDevInfo.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device_OppoDevInfo) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 2}
}

func (x *VulcanRequest_Device_OppoDevInfo) GetOppoAppstoreVersion() string {
	if x != nil {
		return x.OppoAppstoreVersion
	}
	return ""
}

type VulcanRequest_Device_VivoDevInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VivoSystemVersion   string `protobuf:"bytes,1,opt,name=vivo_system_version,json=vivoSystemVersion,proto3" json:"vivo_system_version,omitempty"`       //vivo的ROM版本
	VivoAppstoreVersion string `protobuf:"bytes,2,opt,name=vivo_appstore_version,json=vivoAppstoreVersion,proto3" json:"vivo_appstore_version,omitempty"` //vivo的应用商店版本
}

func (x *VulcanRequest_Device_VivoDevInfo) Reset() {
	*x = VulcanRequest_Device_VivoDevInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device_VivoDevInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device_VivoDevInfo) ProtoMessage() {}

func (x *VulcanRequest_Device_VivoDevInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device_VivoDevInfo.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device_VivoDevInfo) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 3}
}

func (x *VulcanRequest_Device_VivoDevInfo) GetVivoSystemVersion() string {
	if x != nil {
		return x.VivoSystemVersion
	}
	return ""
}

func (x *VulcanRequest_Device_VivoDevInfo) GetVivoAppstoreVersion() string {
	if x != nil {
		return x.VivoAppstoreVersion
	}
	return ""
}

type VulcanRequest_Device_CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KenyId  string `protobuf:"bytes,1,opt,name=kenyId,proto3" json:"kenyId,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *VulcanRequest_Device_CaidInfo) Reset() {
	*x = VulcanRequest_Device_CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Device_CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Device_CaidInfo) ProtoMessage() {}

func (x *VulcanRequest_Device_CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Device_CaidInfo.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Device_CaidInfo) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 1, 4}
}

func (x *VulcanRequest_Device_CaidInfo) GetKenyId() string {
	if x != nil {
		return x.KenyId
	}
	return ""
}

func (x *VulcanRequest_Device_CaidInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type VulcanRequest_User_InstallApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgName string `protobuf:"bytes,1,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name,omitempty"` //app的包名
	AppName string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"` //app的名称
}

func (x *VulcanRequest_User_InstallApp) Reset() {
	*x = VulcanRequest_User_InstallApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_User_InstallApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_User_InstallApp) ProtoMessage() {}

func (x *VulcanRequest_User_InstallApp) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_User_InstallApp.ProtoReflect.Descriptor instead.
func (*VulcanRequest_User_InstallApp) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *VulcanRequest_User_InstallApp) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *VulcanRequest_User_InstallApp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type VulcanRequest_Adslot_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  uint32 `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`   //可选。广告位的宽度。
	Height uint32 `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"` //可选。广告位的高度。
}

func (x *VulcanRequest_Adslot_Size) Reset() {
	*x = VulcanRequest_Adslot_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanRequest_Adslot_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanRequest_Adslot_Size) ProtoMessage() {}

func (x *VulcanRequest_Adslot_Size) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanRequest_Adslot_Size.ProtoReflect.Descriptor instead.
func (*VulcanRequest_Adslot_Size) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *VulcanRequest_Adslot_Size) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VulcanRequest_Adslot_Size) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type VulcanResponse_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid       string                         `protobuf:"bytes,1,opt,name=impid,proto3" json:"impid,omitempty"`                                                                      //必填。广告位唯一ID，与请求中的impid保持一致。
	PriceType   VulcanResponse_Ad_PriceType    `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3,enum=zuiyou_up.VulcanResponse_Ad_PriceType" json:"price_type,omitempty"` //必填。计费合作方式。
	Price       uint64                         `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                                                     //必填。广告出价，单位分。
	AdvName     string                         `protobuf:"bytes,4,opt,name=adv_name,json=advName,proto3" json:"adv_name,omitempty"`                                                   //可选。广告主名称。
	CreativeId  string                         `protobuf:"bytes,5,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`                                          //必填。广告创意ID(用于追溯对应广告的投放情况)。
	Material    *VulcanResponse_Ad_Material    `protobuf:"bytes,6,opt,name=material,proto3" json:"material,omitempty"`                                                                //必填。广告素材。
	Interaction *VulcanResponse_Ad_Interaction `protobuf:"bytes,7,opt,name=interaction,proto3" json:"interaction,omitempty"`                                                          //必填。交互链接。
	Tracking    *VulcanResponse_Ad_Tracking    `protobuf:"bytes,8,opt,name=tracking,proto3" json:"tracking,omitempty"`                                                                //必填。监测链接。
	WinUrl      string                         `protobuf:"bytes,9,opt,name=win_url,json=winUrl,proto3" json:"win_url,omitempty"`                                                      //必填。竞胜通知url。
	Ext         string                         `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`                                                                         //内部使用。
	LossUrl     string                         `protobuf:"bytes,11,opt,name=loss_url,json=lossUrl,proto3" json:"loss_url,omitempty"`                                                  //竞价失败通知url。
}

func (x *VulcanResponse_Ad) Reset() {
	*x = VulcanResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad) ProtoMessage() {}

func (x *VulcanResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0}
}

func (x *VulcanResponse_Ad) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *VulcanResponse_Ad) GetPriceType() VulcanResponse_Ad_PriceType {
	if x != nil {
		return x.PriceType
	}
	return VulcanResponse_Ad_CPM
}

func (x *VulcanResponse_Ad) GetPrice() uint64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *VulcanResponse_Ad) GetAdvName() string {
	if x != nil {
		return x.AdvName
	}
	return ""
}

func (x *VulcanResponse_Ad) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *VulcanResponse_Ad) GetMaterial() *VulcanResponse_Ad_Material {
	if x != nil {
		return x.Material
	}
	return nil
}

func (x *VulcanResponse_Ad) GetInteraction() *VulcanResponse_Ad_Interaction {
	if x != nil {
		return x.Interaction
	}
	return nil
}

func (x *VulcanResponse_Ad) GetTracking() *VulcanResponse_Ad_Tracking {
	if x != nil {
		return x.Tracking
	}
	return nil
}

func (x *VulcanResponse_Ad) GetWinUrl() string {
	if x != nil {
		return x.WinUrl
	}
	return ""
}

func (x *VulcanResponse_Ad) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *VulcanResponse_Ad) GetLossUrl() string {
	if x != nil {
		return x.LossUrl
	}
	return ""
}

type VulcanResponse_Ad_Material struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               //产品名称
	IconUrl    string                            `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`          //产品logo
	Video      *VulcanResponse_Ad_Material_Video `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`                             //视频素材。
	Img        *VulcanResponse_Ad_Material_Image `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`                                 //图片素材。
	Desc       string                            `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`                               //选填。广告描述。
	ButtonText string                            `protobuf:"bytes,6,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"` //选填。按钮文本。
}

func (x *VulcanResponse_Ad_Material) Reset() {
	*x = VulcanResponse_Ad_Material{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad_Material) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad_Material) ProtoMessage() {}

func (x *VulcanResponse_Ad_Material) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad_Material.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad_Material) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *VulcanResponse_Ad_Material) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VulcanResponse_Ad_Material) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Material) GetVideo() *VulcanResponse_Ad_Material_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *VulcanResponse_Ad_Material) GetImg() *VulcanResponse_Ad_Material_Image {
	if x != nil {
		return x.Img
	}
	return nil
}

func (x *VulcanResponse_Ad_Material) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *VulcanResponse_Ad_Material) GetButtonText() string {
	if x != nil {
		return x.ButtonText
	}
	return ""
}

type VulcanResponse_Ad_Interaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LandingUrl        string `protobuf:"bytes,1,opt,name=landing_url,json=landingUrl,proto3" json:"landing_url,omitempty"`                         //选填。落地页链接(内部H5唤起链接)
	BrowserUrl        string `protobuf:"bytes,2,opt,name=browser_url,json=browserUrl,proto3" json:"browser_url,omitempty"`                         //选填。外部浏览器链接
	DownloadUrl       string `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`                      //选填。下载链接
	PkgName           string `protobuf:"bytes,4,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name,omitempty"`                                  //选填。下载app的包名
	InvokeUrl         string `protobuf:"bytes,5,opt,name=invoke_url,json=invokeUrl,proto3" json:"invoke_url,omitempty"`                            //选填。 唤起第三方app链接
	DeveloperName     string `protobuf:"bytes,6,opt,name=developer_name,json=developerName,proto3" json:"developer_name,omitempty"`                //选填。下载app的开发者名称
	AppVersion        string `protobuf:"bytes,7,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                         //选填。下载app的版本号
	PermissionUrl     string `protobuf:"bytes,8,opt,name=permission_url,json=permissionUrl,proto3" json:"permission_url,omitempty"`                //选填。下载app的应用权限
	PrivacyUrl        string `protobuf:"bytes,9,opt,name=privacy_url,json=privacyUrl,proto3" json:"privacy_url,omitempty"`                         //选填。下载app的隐私协议
	WechatAppUsername string `protobuf:"bytes,10,opt,name=wechat_app_username,json=wechatAppUsername,proto3" json:"wechat_app_username,omitempty"` //选填。小程序原始Id
	WechatAppPath     string `protobuf:"bytes,11,opt,name=wechat_app_path,json=wechatAppPath,proto3" json:"wechat_app_path,omitempty"`             //选填。拉起小程序页面
	AppIntroUrl       string `protobuf:"bytes,12,opt,name=app_intro_url,json=appIntroUrl,proto3" json:"app_intro_url,omitempty"`                   //选填。产品信息介绍页面
}

func (x *VulcanResponse_Ad_Interaction) Reset() {
	*x = VulcanResponse_Ad_Interaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad_Interaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad_Interaction) ProtoMessage() {}

func (x *VulcanResponse_Ad_Interaction) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad_Interaction.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad_Interaction) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *VulcanResponse_Ad_Interaction) GetLandingUrl() string {
	if x != nil {
		return x.LandingUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetBrowserUrl() string {
	if x != nil {
		return x.BrowserUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetInvokeUrl() string {
	if x != nil {
		return x.InvokeUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetDeveloperName() string {
	if x != nil {
		return x.DeveloperName
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetPermissionUrl() string {
	if x != nil {
		return x.PermissionUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetPrivacyUrl() string {
	if x != nil {
		return x.PrivacyUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetWechatAppUsername() string {
	if x != nil {
		return x.WechatAppUsername
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetWechatAppPath() string {
	if x != nil {
		return x.WechatAppPath
	}
	return ""
}

func (x *VulcanResponse_Ad_Interaction) GetAppIntroUrl() string {
	if x != nil {
		return x.AppIntroUrl
	}
	return ""
}

type VulcanResponse_Ad_Tracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpTracking            []string `protobuf:"bytes,1,rep,name=imp_tracking,json=impTracking,proto3" json:"imp_tracking,omitempty"`                                       //曝光监测
	ClickTracking          []string `protobuf:"bytes,2,rep,name=click_tracking,json=clickTracking,proto3" json:"click_tracking,omitempty"`                                 //点击监测
	InvokeFailedTracking   []string `protobuf:"bytes,3,rep,name=invoke_failed_tracking,json=invokeFailedTracking,proto3" json:"invoke_failed_tracking,omitempty"`          //唤起失败监测
	InvokeWaitSuccTracking []string `protobuf:"bytes,4,rep,name=invoke_wait_succ_tracking,json=invokeWaitSuccTracking,proto3" json:"invoke_wait_succ_tracking,omitempty"`  //唤起成功监测(原理参考文档)
	VideoStartTracking     []string `protobuf:"bytes,5,rep,name=video_start_tracking,json=videoStartTracking,proto3" json:"video_start_tracking,omitempty"`                //视频播放开始监测
	VideoPlayXTracking     []string `protobuf:"bytes,6,rep,name=video_play_x_tracking,json=videoPlayXTracking,proto3" json:"video_play_x_tracking,omitempty"`              //视频播放x毫秒监测
	VideoPlayX             uint32   `protobuf:"varint,7,opt,name=video_play_x,json=videoPlayX,proto3" json:"video_play_x,omitempty"`                                       //视频播放的x毫秒
	VideoFinishTracking    []string `protobuf:"bytes,8,rep,name=video_finish_tracking,json=videoFinishTracking,proto3" json:"video_finish_tracking,omitempty"`             //视频播放完成监测
	VideoCloseTracking     []string `protobuf:"bytes,9,rep,name=video_close_tracking,json=videoCloseTracking,proto3" json:"video_close_tracking,omitempty"`                //视频被关闭监测
	VideoAutoStartTracking []string `protobuf:"bytes,10,rep,name=video_auto_start_tracking,json=videoAutoStartTracking,proto3" json:"video_auto_start_tracking,omitempty"` //视频自动播放开始监测
	VideoBreakTracking     []string `protobuf:"bytes,11,rep,name=video_break_tracking,json=videoBreakTracking,proto3" json:"video_break_tracking,omitempty"`               //视频暂停/终断监测
	InvokeTryTracking      []string `protobuf:"bytes,12,rep,name=invoke_try_tracking,json=invokeTryTracking,proto3" json:"invoke_try_tracking,omitempty"`                  //尝试唤起监测回传。
}

func (x *VulcanResponse_Ad_Tracking) Reset() {
	*x = VulcanResponse_Ad_Tracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad_Tracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad_Tracking) ProtoMessage() {}

func (x *VulcanResponse_Ad_Tracking) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad_Tracking.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad_Tracking) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 2}
}

func (x *VulcanResponse_Ad_Tracking) GetImpTracking() []string {
	if x != nil {
		return x.ImpTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetClickTracking() []string {
	if x != nil {
		return x.ClickTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetInvokeFailedTracking() []string {
	if x != nil {
		return x.InvokeFailedTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetInvokeWaitSuccTracking() []string {
	if x != nil {
		return x.InvokeWaitSuccTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoStartTracking() []string {
	if x != nil {
		return x.VideoStartTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoPlayXTracking() []string {
	if x != nil {
		return x.VideoPlayXTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoPlayX() uint32 {
	if x != nil {
		return x.VideoPlayX
	}
	return 0
}

func (x *VulcanResponse_Ad_Tracking) GetVideoFinishTracking() []string {
	if x != nil {
		return x.VideoFinishTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoCloseTracking() []string {
	if x != nil {
		return x.VideoCloseTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoAutoStartTracking() []string {
	if x != nil {
		return x.VideoAutoStartTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetVideoBreakTracking() []string {
	if x != nil {
		return x.VideoBreakTracking
	}
	return nil
}

func (x *VulcanResponse_Ad_Tracking) GetInvokeTryTracking() []string {
	if x != nil {
		return x.InvokeTryTracking
	}
	return nil
}

type VulcanResponse_Ad_Material_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"` //必填。视频url
	CoverUrl string `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //可选。视频封面图url
	Height   uint32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width    uint32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	Dur      uint32 `protobuf:"varint,5,opt,name=dur,proto3" json:"dur,omitempty"` //视频播放时长(毫秒)
}

func (x *VulcanResponse_Ad_Material_Video) Reset() {
	*x = VulcanResponse_Ad_Material_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad_Material_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad_Material_Video) ProtoMessage() {}

func (x *VulcanResponse_Ad_Material_Video) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad_Material_Video.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad_Material_Video) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *VulcanResponse_Ad_Material_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Material_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Material_Video) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VulcanResponse_Ad_Material_Video) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VulcanResponse_Ad_Material_Video) GetDur() uint32 {
	if x != nil {
		return x.Dur
	}
	return 0
}

type VulcanResponse_Ad_Material_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverUrl string   `protobuf:"bytes,1,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填。封面图片url
	ImgUrls  []string `protobuf:"bytes,2,rep,name=img_urls,json=imgUrls,proto3" json:"img_urls,omitempty"`    //可选。多图url
	Height   uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width    uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *VulcanResponse_Ad_Material_Image) Reset() {
	*x = VulcanResponse_Ad_Material_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vulcan_2_4_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulcanResponse_Ad_Material_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulcanResponse_Ad_Material_Image) ProtoMessage() {}

func (x *VulcanResponse_Ad_Material_Image) ProtoReflect() protoreflect.Message {
	mi := &file_vulcan_2_4_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulcanResponse_Ad_Material_Image.ProtoReflect.Descriptor instead.
func (*VulcanResponse_Ad_Material_Image) Descriptor() ([]byte, []int) {
	return file_vulcan_2_4_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *VulcanResponse_Ad_Material_Image) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *VulcanResponse_Ad_Material_Image) GetImgUrls() []string {
	if x != nil {
		return x.ImgUrls
	}
	return nil
}

func (x *VulcanResponse_Ad_Material_Image) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VulcanResponse_Ad_Material_Image) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

var File_vulcan_2_4_proto protoreflect.FileDescriptor

var file_vulcan_2_4_proto_rawDesc = []byte{
	0x0a, 0x10, 0x76, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x5f, 0x32, 0x2e, 0x34, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x09, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x22, 0xd3, 0x1d,
	0x0a, 0x0d, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x2e, 0x0a, 0x03, 0x61, 0x70, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f,
	0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x37, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x7a, 0x75, 0x69, 0x79,
	0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c,
	0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75,
	0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x52, 0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x65,
	0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x65, 0x78, 0x74, 0x1a, 0x88, 0x01, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x1a,
	0xbe, 0x12, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70,
	0x76, 0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61,
	0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x4b, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f,
	0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06,
	0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f,
	0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x45, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e,
	0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x64, 0x66, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d,
	0x65, 0x69, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x03,
	0x67, 0x65, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x7a, 0x75, 0x69, 0x79,
	0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03,
	0x67, 0x65, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x65, 0x6d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x6d, 0x65, 0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73,
	0x6b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x64,
	0x69, 0x73, 0x6b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x62, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x62, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x7a,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x54, 0x7a, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x67, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x0a, 0x77, 0x78, 0x5f, 0x61, 0x70, 0x69, 0x5f,
	0x76, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x77, 0x78, 0x41, 0x70, 0x69,
	0x56, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x78, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x65, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x77, 0x78, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x78, 0x5f, 0x6f, 0x70, 0x65,
	0x6e, 0x73, 0x64, 0x6b, 0x5f, 0x76, 0x65, 0x72, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x77, 0x78, 0x4f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07,
	0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61,
	0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x25, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64,
	0x66, 0x76, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76, 0x12, 0x42,
	0x0a, 0x07, 0x68, 0x77, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x48, 0x57, 0x44, 0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x68, 0x77, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x48, 0x0a, 0x09, 0x6f, 0x70, 0x70, 0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x29, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75,
	0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x70, 0x70, 0x6f, 0x44, 0x65, 0x76, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x6f, 0x70, 0x70, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x09,
	0x76, 0x69, 0x76, 0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x56, 0x69, 0x76, 0x6f, 0x44, 0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x69,
	0x76, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f,
	0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x8b, 0x01, 0x0a,
	0x03, 0x47, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x1a, 0x4b, 0x0a, 0x09, 0x48, 0x57,
	0x44, 0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6d, 0x73, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x6d, 0x73,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x41, 0x0a, 0x0b, 0x4f, 0x70, 0x70, 0x6f, 0x44,
	0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x70, 0x70, 0x6f, 0x5f, 0x61,
	0x70, 0x70, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x70, 0x70, 0x6f, 0x41, 0x70, 0x70, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x71, 0x0a, 0x0b, 0x56, 0x69,
	0x76, 0x6f, 0x44, 0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x69, 0x76,
	0x6f, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x76, 0x69, 0x76, 0x6f, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x76, 0x69, 0x76,
	0x6f, 0x5f, 0x61, 0x70, 0x70, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x76, 0x69, 0x76, 0x6f, 0x41, 0x70,
	0x70, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x3c, 0x0a,
	0x08, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6b, 0x65, 0x6e,
	0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6b, 0x65, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x40, 0x0a, 0x0a, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c,
	0x45, 0x54, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x54, 0x54, 0x10, 0x03, 0x22, 0x2e, 0x0a,
	0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x22, 0x68, 0x0a,
	0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x5f, 0x35, 0x47, 0x10, 0x05, 0x22, 0x47, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45,
	0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x49, 0x43, 0x4f,
	0x4d, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x03,
	0x1a, 0xa9, 0x02, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x7a, 0x75,
	0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x79,
	0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x79, 0x6f, 0x62, 0x12, 0x3c, 0x0a,
	0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x7a, 0x75,
	0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x1a, 0x42, 0x0a, 0x0a, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x2b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x02, 0x1a, 0xf4, 0x04, 0x0a,
	0x06, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f,
	0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c,
	0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f,
	0x74, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5c, 0x0a,
	0x11, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f,
	0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x38, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74,
	0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74,
	0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x63, 0x6e, 0x74, 0x1a, 0x34, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x36, 0x0a, 0x06, 0x41, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x52, 0x41, 0x57,
	0x10, 0x03, 0x22, 0x43, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x4e, 0x45, 0x5f, 0x49, 0x4d, 0x47, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x48,
	0x52, 0x45, 0x45, 0x5f, 0x49, 0x4d, 0x47, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x49, 0x4e,
	0x45, 0x5f, 0x49, 0x4d, 0x47, 0x10, 0x03, 0x22, 0x4a, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x49, 0x4e, 0x56, 0x4f, 0x4b, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e,
	0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x52, 0x4f, 0x57, 0x53, 0x45,
	0x52, 0x10, 0x03, 0x22, 0xdf, 0x12, 0x0a, 0x0e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75,
	0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56,
	0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x52, 0x03, 0x61, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74,
	0x1a, 0xdd, 0x0f, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x45, 0x0a,
	0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75,
	0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64,
	0x76, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64,
	0x76, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f,
	0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52,
	0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x4a, 0x0a, 0x0b, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75,
	0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x08,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x69, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x73, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x1a, 0xe3,
	0x03, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x7a, 0x75, 0x69, 0x79,
	0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x3d, 0x0a,
	0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x7a, 0x75, 0x69,
	0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x2e, 0x56, 0x75, 0x6c, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x1a, 0x81, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x64, 0x75, 0x72, 0x1a, 0x6d, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x69,
	0x6d, 0x67, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x1a, 0xb8, 0x03, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x6f, 0x77,
	0x73, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x55, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x50, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0d, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x55, 0x72, 0x6c, 0x1a,
	0xcf, 0x04, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65,
	0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x0a, 0x19,
	0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x16, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x57, 0x61, 0x69, 0x74, 0x53, 0x75, 0x63, 0x63, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x78, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50,
	0x6c, 0x61, 0x79, 0x58, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0c,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x78, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x58, 0x12, 0x32,
	0x0a, 0x15, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x0a, 0x19, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x75,
	0x74, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x30, 0x0a, 0x14, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x42, 0x72, 0x65, 0x61, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x74, 0x72, 0x79, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11,
	0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x54, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x22, 0x1d, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07,
	0x0a, 0x03, 0x43, 0x50, 0x4d, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x43, 0x10, 0x01,
	0x22, 0x63, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45,
	0x51, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x51, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x50, 0x52, 0x49,
	0x43, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x43, 0x48, 0x4f,
	0x49, 0x43, 0x45, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x53, 0x4d,
	0x41, 0x52, 0x54, 0x10, 0x05, 0x42, 0x17, 0x5a, 0x15, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x2f, 0x70, 0x62, 0x2f, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x75, 0x70, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_vulcan_2_4_proto_rawDescOnce sync.Once
	file_vulcan_2_4_proto_rawDescData = file_vulcan_2_4_proto_rawDesc
)

func file_vulcan_2_4_proto_rawDescGZIP() []byte {
	file_vulcan_2_4_proto_rawDescOnce.Do(func() {
		file_vulcan_2_4_proto_rawDescData = protoimpl.X.CompressGZIP(file_vulcan_2_4_proto_rawDescData)
	})
	return file_vulcan_2_4_proto_rawDescData
}

var file_vulcan_2_4_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_vulcan_2_4_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_vulcan_2_4_proto_goTypes = []interface{}{
	(VulcanRequest_Device_DeviceType)(0),      // 0: zuiyou_up.VulcanRequest.Device.DeviceType
	(VulcanRequest_Device_OsType)(0),          // 1: zuiyou_up.VulcanRequest.Device.OsType
	(VulcanRequest_Device_ConnectionType)(0),  // 2: zuiyou_up.VulcanRequest.Device.ConnectionType
	(VulcanRequest_Device_CarrierType)(0),     // 3: zuiyou_up.VulcanRequest.Device.CarrierType
	(VulcanRequest_User_Gender)(0),            // 4: zuiyou_up.VulcanRequest.User.Gender
	(VulcanRequest_Adslot_AdType)(0),          // 5: zuiyou_up.VulcanRequest.Adslot.AdType
	(VulcanRequest_Adslot_MaterialType)(0),    // 6: zuiyou_up.VulcanRequest.Adslot.MaterialType
	(VulcanRequest_Adslot_InteractionType)(0), // 7: zuiyou_up.VulcanRequest.Adslot.InteractionType
	(VulcanResponse_Status)(0),                // 8: zuiyou_up.VulcanResponse.Status
	(VulcanResponse_Ad_PriceType)(0),          // 9: zuiyou_up.VulcanResponse.Ad.PriceType
	(*VulcanRequest)(nil),                     // 10: zuiyou_up.VulcanRequest
	(*VulcanResponse)(nil),                    // 11: zuiyou_up.VulcanResponse
	(*VulcanRequest_App)(nil),                 // 12: zuiyou_up.VulcanRequest.App
	(*VulcanRequest_Device)(nil),              // 13: zuiyou_up.VulcanRequest.Device
	(*VulcanRequest_User)(nil),                // 14: zuiyou_up.VulcanRequest.User
	(*VulcanRequest_Adslot)(nil),              // 15: zuiyou_up.VulcanRequest.Adslot
	(*VulcanRequest_Device_Geo)(nil),          // 16: zuiyou_up.VulcanRequest.Device.Geo
	(*VulcanRequest_Device_HWDevInfo)(nil),    // 17: zuiyou_up.VulcanRequest.Device.HWDevInfo
	(*VulcanRequest_Device_OppoDevInfo)(nil),  // 18: zuiyou_up.VulcanRequest.Device.OppoDevInfo
	(*VulcanRequest_Device_VivoDevInfo)(nil),  // 19: zuiyou_up.VulcanRequest.Device.VivoDevInfo
	(*VulcanRequest_Device_CaidInfo)(nil),     // 20: zuiyou_up.VulcanRequest.Device.CaidInfo
	(*VulcanRequest_User_InstallApp)(nil),     // 21: zuiyou_up.VulcanRequest.User.InstallApp
	(*VulcanRequest_Adslot_Size)(nil),         // 22: zuiyou_up.VulcanRequest.Adslot.Size
	(*VulcanResponse_Ad)(nil),                 // 23: zuiyou_up.VulcanResponse.Ad
	(*VulcanResponse_Ad_Material)(nil),        // 24: zuiyou_up.VulcanResponse.Ad.Material
	(*VulcanResponse_Ad_Interaction)(nil),     // 25: zuiyou_up.VulcanResponse.Ad.Interaction
	(*VulcanResponse_Ad_Tracking)(nil),        // 26: zuiyou_up.VulcanResponse.Ad.Tracking
	(*VulcanResponse_Ad_Material_Video)(nil),  // 27: zuiyou_up.VulcanResponse.Ad.Material.Video
	(*VulcanResponse_Ad_Material_Image)(nil),  // 28: zuiyou_up.VulcanResponse.Ad.Material.Image
}
var file_vulcan_2_4_proto_depIdxs = []int32{
	12, // 0: zuiyou_up.VulcanRequest.app:type_name -> zuiyou_up.VulcanRequest.App
	13, // 1: zuiyou_up.VulcanRequest.device:type_name -> zuiyou_up.VulcanRequest.Device
	14, // 2: zuiyou_up.VulcanRequest.user:type_name -> zuiyou_up.VulcanRequest.User
	15, // 3: zuiyou_up.VulcanRequest.adslot:type_name -> zuiyou_up.VulcanRequest.Adslot
	8,  // 4: zuiyou_up.VulcanResponse.status_code:type_name -> zuiyou_up.VulcanResponse.Status
	23, // 5: zuiyou_up.VulcanResponse.ads:type_name -> zuiyou_up.VulcanResponse.Ad
	0,  // 6: zuiyou_up.VulcanRequest.Device.device_type:type_name -> zuiyou_up.VulcanRequest.Device.DeviceType
	1,  // 7: zuiyou_up.VulcanRequest.Device.os_type:type_name -> zuiyou_up.VulcanRequest.Device.OsType
	2,  // 8: zuiyou_up.VulcanRequest.Device.conn_type:type_name -> zuiyou_up.VulcanRequest.Device.ConnectionType
	3,  // 9: zuiyou_up.VulcanRequest.Device.carrier:type_name -> zuiyou_up.VulcanRequest.Device.CarrierType
	16, // 10: zuiyou_up.VulcanRequest.Device.geo:type_name -> zuiyou_up.VulcanRequest.Device.Geo
	17, // 11: zuiyou_up.VulcanRequest.Device.hw_info:type_name -> zuiyou_up.VulcanRequest.Device.HWDevInfo
	18, // 12: zuiyou_up.VulcanRequest.Device.oppo_info:type_name -> zuiyou_up.VulcanRequest.Device.OppoDevInfo
	19, // 13: zuiyou_up.VulcanRequest.Device.vivo_info:type_name -> zuiyou_up.VulcanRequest.Device.VivoDevInfo
	20, // 14: zuiyou_up.VulcanRequest.Device.caid_list:type_name -> zuiyou_up.VulcanRequest.Device.CaidInfo
	4,  // 15: zuiyou_up.VulcanRequest.User.gender:type_name -> zuiyou_up.VulcanRequest.User.Gender
	21, // 16: zuiyou_up.VulcanRequest.User.apps:type_name -> zuiyou_up.VulcanRequest.User.InstallApp
	5,  // 17: zuiyou_up.VulcanRequest.Adslot.ad_type:type_name -> zuiyou_up.VulcanRequest.Adslot.AdType
	6,  // 18: zuiyou_up.VulcanRequest.Adslot.material_types:type_name -> zuiyou_up.VulcanRequest.Adslot.MaterialType
	7,  // 19: zuiyou_up.VulcanRequest.Adslot.interaction_types:type_name -> zuiyou_up.VulcanRequest.Adslot.InteractionType
	22, // 20: zuiyou_up.VulcanRequest.Adslot.size:type_name -> zuiyou_up.VulcanRequest.Adslot.Size
	9,  // 21: zuiyou_up.VulcanResponse.Ad.price_type:type_name -> zuiyou_up.VulcanResponse.Ad.PriceType
	24, // 22: zuiyou_up.VulcanResponse.Ad.material:type_name -> zuiyou_up.VulcanResponse.Ad.Material
	25, // 23: zuiyou_up.VulcanResponse.Ad.interaction:type_name -> zuiyou_up.VulcanResponse.Ad.Interaction
	26, // 24: zuiyou_up.VulcanResponse.Ad.tracking:type_name -> zuiyou_up.VulcanResponse.Ad.Tracking
	27, // 25: zuiyou_up.VulcanResponse.Ad.Material.video:type_name -> zuiyou_up.VulcanResponse.Ad.Material.Video
	28, // 26: zuiyou_up.VulcanResponse.Ad.Material.img:type_name -> zuiyou_up.VulcanResponse.Ad.Material.Image
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_vulcan_2_4_proto_init() }
func file_vulcan_2_4_proto_init() {
	if File_vulcan_2_4_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_vulcan_2_4_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Adslot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device_HWDevInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device_OppoDevInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device_VivoDevInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Device_CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_User_InstallApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanRequest_Adslot_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad_Material); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad_Interaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad_Tracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad_Material_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vulcan_2_4_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulcanResponse_Ad_Material_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_vulcan_2_4_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_vulcan_2_4_proto_goTypes,
		DependencyIndexes: file_vulcan_2_4_proto_depIdxs,
		EnumInfos:         file_vulcan_2_4_proto_enumTypes,
		MessageInfos:      file_vulcan_2_4_proto_msgTypes,
	}.Build()
	File_vulcan_2_4_proto = out.File
	file_vulcan_2_4_proto_rawDesc = nil
	file_vulcan_2_4_proto_goTypes = nil
	file_vulcan_2_4_proto_depIdxs = nil
}
