//All Rights Reserved.
syntax = "proto3";

option go_package = "mh_proxy/pb/zuiyou_up";

package zuiyou_up;

message VulcanRequest {
  string api_version = 1; // 必填。此API的版本。
  string request_id = 2;  // 必填。自定义的请求id，保证其请求的唯一性。
  uint32 timeout = 3;     // 必填。超时时间，要求再该时间范围内给予广告返回。
  App app = 4;   //必填。移动app信息。
  Device device = 5; //必填。移动设备的信息。
  User user = 6; //可选。用户信息，用于人群定向。
  Adslot adslot = 7; // 必填，至少一个。广告位的信息。
  uint32 test = 8;   //可选。
  string ext = 9;   //可选。

  // app
  message App {
    string appid = 1;  //app应用id，由最右提供。
    string name = 2; // 必填。app应用名。
    string package_name = 3; //必填。 包名。
    string version = 4; // 必填。 app应用的版本。
    repeated string industry = 5; //选填。行业类目。
  }

  // device
  message Device {
    string ua = 1; //必填。User-Agent。
    string ip = 2; // 必填。设备的ip，用于定位，地域定向。
    string ipv6 = 3; //选填。设备的ipv6，用于定位，地域定向。
    string make = 4; //必填。设备制造商。
    string model = 5; //必填。设备型号。
    DeviceType device_type = 6;    //必填。设备类型。
    OsType os_type = 7; //必填。操作系统类型。
    string os_version = 8; //选填。操作系统版本号。
    ConnectionType conn_type = 9; // 可选。设备的网络类型。
    CarrierType carrier = 10; // 可选。运营商类型。
    string mac = 11; // 必填。设备的mac地址。
    string idfa = 12; // 必填。设备的IDFA。
    string idfa_md5 = 13; // 可选。设备IDFA_MD5。
    string imei = 14; //设备IMEI（明文）。
    string imei_md5 = 15; //设备IMEI_MD5。
    string oaid = 16; // 必填。匿名设备标识符。
    string android_id = 17; //设备Android ID。
    Geo geo = 18; // 可选。设备的地理位置信息。
    uint32 screen_width = 19; //设备屏宽。
    uint32 screen_height = 20; //设备屏高。
    string startup_time = 21;   //可选。手机开机时间。
    string country_code = 22;   //可选。local地区。
    string language = 23;   //可选。设备设置的语言。
    string phone_name = 24; //可选。手机名称的MD5码。
    uint64 mem_total = 25; //可选。系统总内存空间。
    uint64 disk_total = 26; //可选。磁盘总空间。
    string mb_time = 27; //可选。系统版本更新时间。
    string device_model = 28; //可选。设备model。
    string local_tz_time = 29; //可选。local时区。
    string org_model = 30; //可选。iOS机型原始值。
    int64 wx_api_ver = 31;  //可选。微信内部sdk版本。（安卓）
    bool wx_installed = 32; //可选。是否已安装微信。（iOS）
    string wx_opensdk_ver = 33; //可选。微信open sdk的版本
    string mac_md5 = 34;  //可选。设备的mac地址Md5 小写。
    string boot_mark = 35; //可选。
    string update_mark = 36;//可选。
    string caid = 37; //废弃。使用caid_list代替。
    string caid_version = 38; //废弃。使用caid_list代替。
    string idfv = 39; //iOS必选。
    HWDevInfo hw_info = 40; //华为设备必选。
    OppoDevInfo oppo_info = 41; //oppo设备必选。
    VivoDevInfo vivo_info = 42; //vivo设备必选。
    string s_language = 43; //可选。设备设置的语言。示例: zh-Hans-CN
    repeated CaidInfo caid_list = 44; //可选。


    // 设备类型
    enum DeviceType {
      DEVICE_UNKNOWN = 0;
      PHONE = 1; // 手机。
      TABLET = 2; // 平板。
      OTT = 3; //OTT设备.
    }

    // 操作系统类型
    enum OsType {
      OS_UNKNOWN = 0;
      ANDROID = 1;
      IOS = 2;
    }

    // 网络类型
    enum ConnectionType {
      CONN_UNKNOWN = 0;
      WIFI = 1;
      MOBILE_2G = 2;
      MOBILE_3G = 3;
      MOBILE_4G = 4;
      MOBILE_5G = 5;
    }

    // 运营商类型
    enum CarrierType {
      CARRIER_UNKNOWN = 0;
      MOBILE = 1;
      UNICOM = 2;
      TELECOM = 3;
    }

    //地理位置
    message Geo {
      float latitude = 1; //纬度
      float longitude = 2; //经度
      string city = 3; //城市，中文即可(utf-8编码)
      string province = 4; //省份，中文即可(utf-8编码)
      string district = 5; //区县，中文即可(utf-8编码)
    }

    message HWDevInfo {
      string ag_version = 2; //应用市场版本号，verCodeOfAG >= 110002000。
      string hms_version = 3;  //HMS Core 版本号,verCodeOfHms >= 50200100。
    }

    message OppoDevInfo {
      string oppo_appstore_version = 1; //oppo的应用商店版本号
    }

    message VivoDevInfo {
      string vivo_system_version = 1; //vivo的ROM版本
      string vivo_appstore_version = 2; //vivo的应用商店版本
    }

    message CaidInfo {
      string kenyId = 1;
      string version = 2;
    }

  }

  // user
  message User {
    string uid = 1; //可选。用户ID。
    Gender gender = 2; // 可选。用户的性别。
    uint32 age = 3; //可选。年龄。
    uint32 yob = 4; //可选。出生年月。
    repeated InstallApp apps = 5;//可选。用户安装的app列表

    enum Gender {
      UNKNOWN = 0;
      MALE = 1;
      FEMALE = 2;
    }

    message InstallApp {
      string pkg_name = 1;  //app的包名
      string app_name = 2;  //app的名称
    }
  }

  // adslot
  message Adslot {
    AdType ad_type = 1;  //必填。广告类型。
    repeated MaterialType material_types = 2;  //必填。(开屏仅支持图片素材，信息流支持图片和视频。)
    repeated InteractionType interaction_types = 3; //必填。（该广告位支持的交互类型。）
    uint32 price = 4; //必填。协商的底价。
    repeated Size size = 5; //选填。当前广告位支持的尺寸。
    string tag_id = 6; //必填。广告位标识，由商务侧提供。
    uint32 cnt = 7; //必填。请求广告个数。

    enum AdType {
      NATIVE = 0; //原生广告
      SPLASH = 1; //开屏广告
      REWARD = 2; //激励视频广告
      DRAW   = 3; //Draw视频广告
    }

    enum MaterialType {
      ONE_IMG = 0;    //支持单图素材
      VIDEO = 1;      //支持视频素材
      THREE_IMG = 2;  //支持三图素材
      NINE_IMG = 3;   //支持九图素材
    }

    enum InteractionType {
      LANDING_PAGE = 0; //落地页
      INVOKE = 1;   //唤起
      DOWNLOAD = 2; //下载
      BROWSER = 3; //打开外部浏览器
    }

    message Size {
      uint32 width = 3; //可选。广告位的宽度。
      uint32 height = 4; //可选。广告位的高度。
    }
  }

}

message VulcanResponse {
  string response_id = 1;  // 必填。响应id，与请求id保持一致。
  string site_name = 2;  //必填。平台方名称。
  uint32 processing_time_ms = 3; // 可选。从收到请求到返回响应所用的时间。
  Status status_code = 4; // 可选。响应状态码。
  repeated Ad ads = 5;    //可选。广告内容。
  string err_msg = 6; //可选。错误信息。
  string ext = 7; //可选。拓展。

  message Ad {
    string impid = 1; //必填。广告位唯一ID，与请求中的impid保持一致。
    PriceType price_type = 2; //必填。计费合作方式。
    uint64 price = 3; //必填。广告出价，单位分。
    string adv_name = 4; //可选。广告主名称。
    string creative_id = 5; //必填。广告创意ID(用于追溯对应广告的投放情况)。
    Material material = 6; //必填。广告素材。
    Interaction interaction = 7; //必填。交互链接。
    Tracking tracking = 8; //必填。监测链接。
    string win_url = 9; //必填。竞胜通知url。
    string ext = 10; //内部使用。
    string loss_url = 11; //竞价失败通知url。

    message Material {
      string name = 1; //产品名称
      string icon_url = 2; //产品logo
      Video video = 3; //视频素材。
      Image img = 4; //图片素材。
      string desc = 5; //选填。广告描述。
      string button_text = 6; //选填。按钮文本。

      message Video {
        string video_url = 1; //必填。视频url
        string cover_url = 2; //可选。视频封面图url
        uint32 height = 3;
        uint32 width = 4;
        uint32 dur = 5; //视频播放时长(毫秒)
      }

      message Image {
        string cover_url = 1; //必填。封面图片url
        repeated string img_urls = 2; //可选。多图url
        uint32 height = 3;
        uint32 width = 4;
      }
    }

    message Interaction {
      string landing_url = 1; //选填。落地页链接(内部H5唤起链接)
      string browser_url = 2; //选填。外部浏览器链接
      string download_url = 3; //选填。下载链接
      string pkg_name = 4; //选填。下载app的包名
      string invoke_url = 5; //选填。 唤起第三方app链接
      string developer_name = 6; //选填。下载app的开发者名称
      string app_version = 7; //选填。下载app的版本号
      string permission_url = 8; //选填。下载app的应用权限
      string privacy_url = 9; //选填。下载app的隐私协议
      string wechat_app_username = 10; //选填。小程序原始Id
      string wechat_app_path = 11; //选填。拉起小程序页面
      string app_intro_url = 12;//选填。产品信息介绍页面
    }

    message Tracking {
      repeated string imp_tracking = 1; //曝光监测
      repeated string click_tracking = 2; //点击监测
      repeated string invoke_failed_tracking = 3; //唤起失败监测
      repeated string invoke_wait_succ_tracking = 4; //唤起成功监测(原理参考文档)
      repeated string video_start_tracking = 5; //视频播放开始监测
      repeated string video_play_x_tracking = 6; //视频播放x毫秒监测
      uint32 video_play_x = 7;  //视频播放的x毫秒
      repeated string video_finish_tracking = 8; //视频播放完成监测
      repeated string video_close_tracking = 9; //视频被关闭监测
      repeated string video_auto_start_tracking = 10; //视频自动播放开始监测
      repeated string video_break_tracking = 11; //视频暂停/终断监测
      repeated string invoke_try_tracking = 12;  //尝试唤起监测回传。
    }

    enum PriceType {
      CPM = 0;    //目前均采用CPM方式计费
      CPC = 1;
    }
  }

  enum Status {
    REQ_OK = 0; //响应成功并填充广告
    REQ_FAIL = 1; //响应失败，请求参数错误
    DROP_PRICE = 2; //不满足价格需求而丢弃
    DROP_CHOICE = 3; //不满足筛选需求而丢弃(例如推荐模型)
    DROP_OTHER = 4; //其它原因无法填充
    DROP_SMART = 5; //精准策略丢弃,请勿重复请求
  }
}