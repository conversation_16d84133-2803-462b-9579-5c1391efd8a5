// Copyright 2020 xiaochuankeji Inc. All Rights Reserved.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: zuiyou_3.5.9.proto

package zuiyou

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Template int32

const (
	Template_SPLASH_DEFAULT  Template = 0  // 开屏打开内部H5
	Template_SPLASH_BROWSER  Template = 1  // 开屏打开外部浏览器(不推荐，后续可能会关闭)
	Template_SPLASH_OPEN_APP Template = 2  //开屏唤起第三方APP
	Template_SPLASH_DOWNLOAD Template = 3  //开屏下载APP(当且仅当enable_splash_download时支持)
	Template_FEED_DEFAULT    Template = 4  //原生广告打开内部H5
	Template_FEED_BROWSER    Template = 5  //原生广告打开外部浏览器(不推荐，后续可能会关闭)
	Template_FEED_DOWNLOAD   Template = 6  //原生广告下载APP
	Template_FEED_OPEN_APP   Template = 7  //原生广告唤起第三方APP
	Template_REVIEW_DEFAULT  Template = 8  //废弃。详情页广告打开内部H5
	Template_REVIEW_BROWSER  Template = 9  //废弃。详情页广告打开第三方浏览器
	Template_REVIEW_DOWNLOAD Template = 10 //废弃。详情页广告下载APP
	Template_REVIEW_OPEN_APP Template = 11 //废弃。详情页广告唤起第三方APP
	Template_POPUP_DEFAULT   Template = 12 //废弃。弹窗广告打开内部H5
	Template_POPUP_BROWSER   Template = 13 //废弃。弹窗广告打开第三方浏览器
	Template_POPUP_DOWNLOAD  Template = 14 //废弃。弹窗广告下载APP
	Template_POPUP_OPEN_APP  Template = 15 //废弃。弹窗广告唤起第三方APP
	Template_REWARD_DEFAULT  Template = 16 //内部使用。激励视频广告打开内部H5
	Template_REWARD_BROWSER  Template = 17 //内部使用。激励视频广告打开第三方浏览器
	Template_REWARD_DOWNLOAD Template = 18 //内部使用。激励视频广告下载APP
	Template_REWARD_OPEN_APP Template = 19 //内部使用。激励视频广告唤起第三方APP
)

// Enum value maps for Template.
var (
	Template_name = map[int32]string{
		0:  "SPLASH_DEFAULT",
		1:  "SPLASH_BROWSER",
		2:  "SPLASH_OPEN_APP",
		3:  "SPLASH_DOWNLOAD",
		4:  "FEED_DEFAULT",
		5:  "FEED_BROWSER",
		6:  "FEED_DOWNLOAD",
		7:  "FEED_OPEN_APP",
		8:  "REVIEW_DEFAULT",
		9:  "REVIEW_BROWSER",
		10: "REVIEW_DOWNLOAD",
		11: "REVIEW_OPEN_APP",
		12: "POPUP_DEFAULT",
		13: "POPUP_BROWSER",
		14: "POPUP_DOWNLOAD",
		15: "POPUP_OPEN_APP",
		16: "REWARD_DEFAULT",
		17: "REWARD_BROWSER",
		18: "REWARD_DOWNLOAD",
		19: "REWARD_OPEN_APP",
	}
	Template_value = map[string]int32{
		"SPLASH_DEFAULT":  0,
		"SPLASH_BROWSER":  1,
		"SPLASH_OPEN_APP": 2,
		"SPLASH_DOWNLOAD": 3,
		"FEED_DEFAULT":    4,
		"FEED_BROWSER":    5,
		"FEED_DOWNLOAD":   6,
		"FEED_OPEN_APP":   7,
		"REVIEW_DEFAULT":  8,
		"REVIEW_BROWSER":  9,
		"REVIEW_DOWNLOAD": 10,
		"REVIEW_OPEN_APP": 11,
		"POPUP_DEFAULT":   12,
		"POPUP_BROWSER":   13,
		"POPUP_DOWNLOAD":  14,
		"POPUP_OPEN_APP":  15,
		"REWARD_DEFAULT":  16,
		"REWARD_BROWSER":  17,
		"REWARD_DOWNLOAD": 18,
		"REWARD_OPEN_APP": 19,
	}
)

func (x Template) Enum() *Template {
	p := new(Template)
	*p = x
	return p
}

func (x Template) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Template) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[0].Descriptor()
}

func (Template) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[0]
}

func (x Template) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Template.Descriptor instead.
func (Template) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0}
}

type MType int32

const (
	MType_IMG   MType = 0 //支持图片素材
	MType_VIDEO MType = 1 //支持视频素材
)

// Enum value maps for MType.
var (
	MType_name = map[int32]string{
		0: "IMG",
		1: "VIDEO",
	}
	MType_value = map[string]int32{
		"IMG":   0,
		"VIDEO": 1,
	}
)

func (x MType) Enum() *MType {
	p := new(MType)
	*p = x
	return p
}

func (x MType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[1].Descriptor()
}

func (MType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[1]
}

func (x MType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MType.Descriptor instead.
func (MType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1}
}

type RtbRequest_EnableType int32

const (
	RtbRequest_DISABLE RtbRequest_EnableType = 0
	RtbRequest_ENABLE  RtbRequest_EnableType = 1
)

// Enum value maps for RtbRequest_EnableType.
var (
	RtbRequest_EnableType_name = map[int32]string{
		0: "DISABLE",
		1: "ENABLE",
	}
	RtbRequest_EnableType_value = map[string]int32{
		"DISABLE": 0,
		"ENABLE":  1,
	}
)

func (x RtbRequest_EnableType) Enum() *RtbRequest_EnableType {
	p := new(RtbRequest_EnableType)
	*p = x
	return p
}

func (x RtbRequest_EnableType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_EnableType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[2].Descriptor()
}

func (RtbRequest_EnableType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[2]
}

func (x RtbRequest_EnableType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_EnableType.Descriptor instead.
func (RtbRequest_EnableType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 0}
}

// 设备类型
type RtbRequest_Device_DeviceType int32

const (
	RtbRequest_Device_DEVICE_UNKNOWN RtbRequest_Device_DeviceType = 0
	RtbRequest_Device_PHONE          RtbRequest_Device_DeviceType = 1 // 手机。
	RtbRequest_Device_TABLET         RtbRequest_Device_DeviceType = 2 // 平板。
)

// Enum value maps for RtbRequest_Device_DeviceType.
var (
	RtbRequest_Device_DeviceType_name = map[int32]string{
		0: "DEVICE_UNKNOWN",
		1: "PHONE",
		2: "TABLET",
	}
	RtbRequest_Device_DeviceType_value = map[string]int32{
		"DEVICE_UNKNOWN": 0,
		"PHONE":          1,
		"TABLET":         2,
	}
)

func (x RtbRequest_Device_DeviceType) Enum() *RtbRequest_Device_DeviceType {
	p := new(RtbRequest_Device_DeviceType)
	*p = x
	return p
}

func (x RtbRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[3].Descriptor()
}

func (RtbRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[3]
}

func (x RtbRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_Device_DeviceType.Descriptor instead.
func (RtbRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2, 0}
}

// 操作系统类型
type RtbRequest_Device_OsType int32

const (
	RtbRequest_Device_OS_UNKNOWN RtbRequest_Device_OsType = 0
	RtbRequest_Device_ANDROID    RtbRequest_Device_OsType = 1
	RtbRequest_Device_IOS        RtbRequest_Device_OsType = 2
)

// Enum value maps for RtbRequest_Device_OsType.
var (
	RtbRequest_Device_OsType_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "ANDROID",
		2: "IOS",
	}
	RtbRequest_Device_OsType_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"ANDROID":    1,
		"IOS":        2,
	}
)

func (x RtbRequest_Device_OsType) Enum() *RtbRequest_Device_OsType {
	p := new(RtbRequest_Device_OsType)
	*p = x
	return p
}

func (x RtbRequest_Device_OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_Device_OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[4].Descriptor()
}

func (RtbRequest_Device_OsType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[4]
}

func (x RtbRequest_Device_OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_Device_OsType.Descriptor instead.
func (RtbRequest_Device_OsType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2, 1}
}

// 网络类型
type RtbRequest_Device_ConnectionType int32

const (
	RtbRequest_Device_CONN_UNKNOWN RtbRequest_Device_ConnectionType = 0
	RtbRequest_Device_WIFI         RtbRequest_Device_ConnectionType = 1
	RtbRequest_Device_MOBILE_2G    RtbRequest_Device_ConnectionType = 2
	RtbRequest_Device_MOBILE_3G    RtbRequest_Device_ConnectionType = 3
	RtbRequest_Device_MOBILE_4G    RtbRequest_Device_ConnectionType = 4
	RtbRequest_Device_MOBILE_5G    RtbRequest_Device_ConnectionType = 5
)

// Enum value maps for RtbRequest_Device_ConnectionType.
var (
	RtbRequest_Device_ConnectionType_name = map[int32]string{
		0: "CONN_UNKNOWN",
		1: "WIFI",
		2: "MOBILE_2G",
		3: "MOBILE_3G",
		4: "MOBILE_4G",
		5: "MOBILE_5G",
	}
	RtbRequest_Device_ConnectionType_value = map[string]int32{
		"CONN_UNKNOWN": 0,
		"WIFI":         1,
		"MOBILE_2G":    2,
		"MOBILE_3G":    3,
		"MOBILE_4G":    4,
		"MOBILE_5G":    5,
	}
)

func (x RtbRequest_Device_ConnectionType) Enum() *RtbRequest_Device_ConnectionType {
	p := new(RtbRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x RtbRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[5].Descriptor()
}

func (RtbRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[5]
}

func (x RtbRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_Device_ConnectionType.Descriptor instead.
func (RtbRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2, 2}
}

// 运营商类型
type RtbRequest_Device_CarrierType int32

const (
	RtbRequest_Device_CARRIER_UNKNOWN RtbRequest_Device_CarrierType = 0
	RtbRequest_Device_MOBILE          RtbRequest_Device_CarrierType = 1
	RtbRequest_Device_UNICOM          RtbRequest_Device_CarrierType = 2
	RtbRequest_Device_TELECOM         RtbRequest_Device_CarrierType = 3
)

// Enum value maps for RtbRequest_Device_CarrierType.
var (
	RtbRequest_Device_CarrierType_name = map[int32]string{
		0: "CARRIER_UNKNOWN",
		1: "MOBILE",
		2: "UNICOM",
		3: "TELECOM",
	}
	RtbRequest_Device_CarrierType_value = map[string]int32{
		"CARRIER_UNKNOWN": 0,
		"MOBILE":          1,
		"UNICOM":          2,
		"TELECOM":         3,
	}
)

func (x RtbRequest_Device_CarrierType) Enum() *RtbRequest_Device_CarrierType {
	p := new(RtbRequest_Device_CarrierType)
	*p = x
	return p
}

func (x RtbRequest_Device_CarrierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_Device_CarrierType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[6].Descriptor()
}

func (RtbRequest_Device_CarrierType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[6]
}

func (x RtbRequest_Device_CarrierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_Device_CarrierType.Descriptor instead.
func (RtbRequest_Device_CarrierType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2, 3}
}

type RtbRequest_User_Gender int32

const (
	RtbRequest_User_UNKNOWN RtbRequest_User_Gender = 0
	RtbRequest_User_MALE    RtbRequest_User_Gender = 1
	RtbRequest_User_FEMALE  RtbRequest_User_Gender = 2
)

// Enum value maps for RtbRequest_User_Gender.
var (
	RtbRequest_User_Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	RtbRequest_User_Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x RtbRequest_User_Gender) Enum() *RtbRequest_User_Gender {
	p := new(RtbRequest_User_Gender)
	*p = x
	return p
}

func (x RtbRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[7].Descriptor()
}

func (RtbRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[7]
}

func (x RtbRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbRequest_User_Gender.Descriptor instead.
func (RtbRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 3, 0}
}

type RtbResponse_Status int32

const (
	RtbResponse_REQ_OK      RtbResponse_Status = 0 //响应成功并填充广告
	RtbResponse_REQ_FAIL    RtbResponse_Status = 1 //响应失败，请求参数错误
	RtbResponse_DROP_PRICE  RtbResponse_Status = 2 //不满足价格需求而丢弃
	RtbResponse_DROP_CHOICE RtbResponse_Status = 3 //不满足筛选需求而丢弃(例如推荐模型)
	RtbResponse_DROP_OTHER  RtbResponse_Status = 4 //其它原因无法填充
)

// Enum value maps for RtbResponse_Status.
var (
	RtbResponse_Status_name = map[int32]string{
		0: "REQ_OK",
		1: "REQ_FAIL",
		2: "DROP_PRICE",
		3: "DROP_CHOICE",
		4: "DROP_OTHER",
	}
	RtbResponse_Status_value = map[string]int32{
		"REQ_OK":      0,
		"REQ_FAIL":    1,
		"DROP_PRICE":  2,
		"DROP_CHOICE": 3,
		"DROP_OTHER":  4,
	}
)

func (x RtbResponse_Status) Enum() *RtbResponse_Status {
	p := new(RtbResponse_Status)
	*p = x
	return p
}

func (x RtbResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[8].Descriptor()
}

func (RtbResponse_Status) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[8]
}

func (x RtbResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbResponse_Status.Descriptor instead.
func (RtbResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0}
}

type RtbResponse_Ad_PriceType int32

const (
	RtbResponse_Ad_CPM RtbResponse_Ad_PriceType = 0 //目前均采用CPM方式计费
	RtbResponse_Ad_CPC RtbResponse_Ad_PriceType = 1
)

// Enum value maps for RtbResponse_Ad_PriceType.
var (
	RtbResponse_Ad_PriceType_name = map[int32]string{
		0: "CPM",
		1: "CPC",
	}
	RtbResponse_Ad_PriceType_value = map[string]int32{
		"CPM": 0,
		"CPC": 1,
	}
)

func (x RtbResponse_Ad_PriceType) Enum() *RtbResponse_Ad_PriceType {
	p := new(RtbResponse_Ad_PriceType)
	*p = x
	return p
}

func (x RtbResponse_Ad_PriceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RtbResponse_Ad_PriceType) Descriptor() protoreflect.EnumDescriptor {
	return file_zuiyou_3_5_9_proto_enumTypes[9].Descriptor()
}

func (RtbResponse_Ad_PriceType) Type() protoreflect.EnumType {
	return &file_zuiyou_3_5_9_proto_enumTypes[9]
}

func (x RtbResponse_Ad_PriceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RtbResponse_Ad_PriceType.Descriptor instead.
func (RtbResponse_Ad_PriceType) EnumDescriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 0}
}

type RtbRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiVersion      string                      `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`                 // 必填。此API的版本。
	RequestId       string                      `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`                    // 必填。自定义的请求id，保证其请求的唯一性。
	Timeout         uint32                      `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`                                        // 必填。超时时间，要求再该时间范围内给予广告返回。
	App             *RtbRequest_App             `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`                                                 //必填。移动app信息。
	Device          *RtbRequest_Device          `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`                                           //必填。移动设备的信息。
	User            *RtbRequest_User            `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`                                               //可选。用户信息，用于人群定向。
	Adslots         []*RtbRequest_Adslot        `protobuf:"bytes,7,rep,name=adslots,proto3" json:"adslots,omitempty"`                                         // 必填，至少一个。广告位的信息。
	Cnt             uint32                      `protobuf:"varint,8,opt,name=cnt,proto3" json:"cnt,omitempty"`                                                //必填。请求广告个数。
	Test            uint32                      `protobuf:"varint,9,opt,name=test,proto3" json:"test,omitempty"`                                              //可选。内部使用。
	Ext             string                      `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`                                                //可选。内部使用。
	FeaturesSupport *RtbRequest_FeaturesSupport `protobuf:"bytes,11,opt,name=features_support,json=featuresSupport,proto3" json:"features_support,omitempty"` //可选。用户拓展功能的支持。
	Pmp             *RtbRequest_Pmp             `protobuf:"bytes,12,opt,name=pmp,proto3" json:"pmp,omitempty"`                                                // 可选，内部使用
	PlatformId      string                      `protobuf:"bytes,13,opt,name=PlatformId,proto3" json:"PlatformId,omitempty"`                                  //平台编号。
	PkgList         []string                    `protobuf:"bytes,14,rep,name=pkg_list,json=pkgList,proto3" json:"pkg_list,omitempty"`                         // 设备安装列表
}

func (x *RtbRequest) Reset() {
	*x = RtbRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest) ProtoMessage() {}

func (x *RtbRequest) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest.ProtoReflect.Descriptor instead.
func (*RtbRequest) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0}
}

func (x *RtbRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *RtbRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RtbRequest) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *RtbRequest) GetApp() *RtbRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *RtbRequest) GetDevice() *RtbRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RtbRequest) GetUser() *RtbRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RtbRequest) GetAdslots() []*RtbRequest_Adslot {
	if x != nil {
		return x.Adslots
	}
	return nil
}

func (x *RtbRequest) GetCnt() uint32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

func (x *RtbRequest) GetTest() uint32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *RtbRequest) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *RtbRequest) GetFeaturesSupport() *RtbRequest_FeaturesSupport {
	if x != nil {
		return x.FeaturesSupport
	}
	return nil
}

func (x *RtbRequest) GetPmp() *RtbRequest_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *RtbRequest) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *RtbRequest) GetPkgList() []string {
	if x != nil {
		return x.PkgList
	}
	return nil
}

type RtbResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseId       string             `protobuf:"bytes,1,opt,name=response_id,json=responseId,proto3" json:"response_id,omitempty"`                          // 必填。响应id，与请求id保持一致。
	SiteName         string             `protobuf:"bytes,2,opt,name=site_name,json=siteName,proto3" json:"site_name,omitempty"`                                //必填。平台方名称。
	ProcessingTimeMs uint32             `protobuf:"varint,3,opt,name=processing_time_ms,json=processingTimeMs,proto3" json:"processing_time_ms,omitempty"`     // 可选。从收到请求到返回响应所用的时间。
	StatusCode       RtbResponse_Status `protobuf:"varint,4,opt,name=status_code,json=statusCode,proto3,enum=RtbResponse_Status" json:"status_code,omitempty"` // 可选。响应状态码。
	Ads              []*RtbResponse_Ad  `protobuf:"bytes,5,rep,name=ads,proto3" json:"ads,omitempty"`                                                          //可选。广告内容。
}

func (x *RtbResponse) Reset() {
	*x = RtbResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse) ProtoMessage() {}

func (x *RtbResponse) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse.ProtoReflect.Descriptor instead.
func (*RtbResponse) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1}
}

func (x *RtbResponse) GetResponseId() string {
	if x != nil {
		return x.ResponseId
	}
	return ""
}

func (x *RtbResponse) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *RtbResponse) GetProcessingTimeMs() uint32 {
	if x != nil {
		return x.ProcessingTimeMs
	}
	return 0
}

func (x *RtbResponse) GetStatusCode() RtbResponse_Status {
	if x != nil {
		return x.StatusCode
	}
	return RtbResponse_REQ_OK
}

func (x *RtbResponse) GetAds() []*RtbResponse_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

// pmp
type RtbRequest_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DealId uint32 `protobuf:"varint,1,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"` // 内部使用
}

func (x *RtbRequest_Pmp) Reset() {
	*x = RtbRequest_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_Pmp) ProtoMessage() {}

func (x *RtbRequest_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_Pmp.ProtoReflect.Descriptor instead.
func (*RtbRequest_Pmp) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RtbRequest_Pmp) GetDealId() uint32 {
	if x != nil {
		return x.DealId
	}
	return 0
}

// app
type RtbRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid       string `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                                //app应用id，由最右提供。
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  // 必填。app应用名。
	PackageName string `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` //必填。 包名。
	Version     string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`                            // 必填。 app应用的版本。
}

func (x *RtbRequest_App) Reset() {
	*x = RtbRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_App) ProtoMessage() {}

func (x *RtbRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_App.ProtoReflect.Descriptor instead.
func (*RtbRequest_App) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 1}
}

func (x *RtbRequest_App) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *RtbRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RtbRequest_App) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *RtbRequest_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// device
type RtbRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua           string                           `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`                                                                      //必填。User-Agent。
	Ip           string                           `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                                                      // 必填。设备的ip，用于定位，地域定向。
	Ipv6         string                           `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`                                                                  //选填。设备的ipv6，用于定位，地域定向。
	Make         string                           `protobuf:"bytes,4,opt,name=make,proto3" json:"make,omitempty"`                                                                  //选填。设备制造商。
	Model        string                           `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`                                                                //选填。设备型号。
	DeviceType   RtbRequest_Device_DeviceType     `protobuf:"varint,6,opt,name=device_type,json=deviceType,proto3,enum=RtbRequest_Device_DeviceType" json:"device_type,omitempty"` //必填。设备类型。
	OsType       RtbRequest_Device_OsType         `protobuf:"varint,7,opt,name=os_type,json=osType,proto3,enum=RtbRequest_Device_OsType" json:"os_type,omitempty"`                 //必填。操作系统类型。
	OsVersion    string                           `protobuf:"bytes,8,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`                                       //选填。操作系统版本号。
	ConnType     RtbRequest_Device_ConnectionType `protobuf:"varint,9,opt,name=conn_type,json=connType,proto3,enum=RtbRequest_Device_ConnectionType" json:"conn_type,omitempty"`   // 可选。设备的网络类型。
	Carrier      RtbRequest_Device_CarrierType    `protobuf:"varint,10,opt,name=carrier,proto3,enum=RtbRequest_Device_CarrierType" json:"carrier,omitempty"`                       // 可选。运营商类型。
	Mac          string                           `protobuf:"bytes,11,opt,name=mac,proto3" json:"mac,omitempty"`                                                                   // 可选。设备的mac地址。
	Idfa         string                           `protobuf:"bytes,12,opt,name=idfa,proto3" json:"idfa,omitempty"`                                                                 // 可选。设备的IDFA。
	IdfaMd5      string                           `protobuf:"bytes,13,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`                                            // 可选。设备IDFA_MD5。
	Imei         string                           `protobuf:"bytes,14,opt,name=imei,proto3" json:"imei,omitempty"`                                                                 //设备IMEI（明文）。
	ImeiMd5      string                           `protobuf:"bytes,15,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`                                            //设备IMEI_MD5。
	Oaid         string                           `protobuf:"bytes,16,opt,name=oaid,proto3" json:"oaid,omitempty"`                                                                 // 可选。匿名设备标识符。
	AndroidId    string                           `protobuf:"bytes,17,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`                                      //设备Android ID。
	Geo          *RtbRequest_Device_Geo           `protobuf:"bytes,18,opt,name=geo,proto3" json:"geo,omitempty"`                                                                   // 可选。设备的地理位置信息。
	ScreenWidth  uint32                           `protobuf:"varint,19,opt,name=screen_width,json=screenWidth,proto3" json:"screen_width,omitempty"`                               //设备屏宽。
	ScreenHeight uint32                           `protobuf:"varint,20,opt,name=screen_height,json=screenHeight,proto3" json:"screen_height,omitempty"`                            //设备屏高。
	StartupTime  string                           `protobuf:"bytes,21,opt,name=startup_time,json=startupTime,proto3" json:"startup_time,omitempty"`                                //可选。手机开机时间。
	CountryCode  string                           `protobuf:"bytes,22,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`                                //可选。local地区。
	Language     string                           `protobuf:"bytes,23,opt,name=language,proto3" json:"language,omitempty"`                                                         //可选。设备设置的语言。
	PhoneName    string                           `protobuf:"bytes,24,opt,name=phone_name,json=phoneName,proto3" json:"phone_name,omitempty"`                                      //可选。手机名称的MD5码。
	MemTotal     uint64                           `protobuf:"varint,25,opt,name=mem_total,json=memTotal,proto3" json:"mem_total,omitempty"`                                        //可选。系统总内存空间。
	DiskTotal    uint64                           `protobuf:"varint,26,opt,name=disk_total,json=diskTotal,proto3" json:"disk_total,omitempty"`                                     //可选。磁盘总空间。
	MbTime       string                           `protobuf:"bytes,27,opt,name=mb_time,json=mbTime,proto3" json:"mb_time,omitempty"`                                               //可选。系统版本更新时间。
	DeviceModel  string                           `protobuf:"bytes,28,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`                                //可选。设备model。
	LocalTzTime  string                           `protobuf:"bytes,29,opt,name=local_tz_time,json=localTzTime,proto3" json:"local_tz_time,omitempty"`                              //可选。local时区。
	OrgModel     string                           `protobuf:"bytes,30,opt,name=org_model,json=orgModel,proto3" json:"org_model,omitempty"`                                         //可选。iOS机型原始值。
	BootMark     string                           `protobuf:"bytes,31,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`                                         //可选。系统启动标识
	UpdateMark   string                           `protobuf:"bytes,32,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`                                   //可选。系统更新标识
	BirthTime    string                           `protobuf:"bytes,33,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`                                      //可选。设备初始化时间
	Idfv         string                           `protobuf:"bytes,34,opt,name=idfv,proto3" json:"idfv,omitempty"`                                                                 //可选。iOS设备的IDFV。
	CaidList     []*RtbRequest_CaidSt             `protobuf:"bytes,35,rep,name=caid_list,json=caidList,proto3" json:"caid_list,omitempty"`                                         //可选。CAID列表
}

func (x *RtbRequest_Device) Reset() {
	*x = RtbRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_Device) ProtoMessage() {}

func (x *RtbRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_Device.ProtoReflect.Descriptor instead.
func (*RtbRequest_Device) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2}
}

func (x *RtbRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *RtbRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *RtbRequest_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *RtbRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *RtbRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *RtbRequest_Device) GetDeviceType() RtbRequest_Device_DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return RtbRequest_Device_DEVICE_UNKNOWN
}

func (x *RtbRequest_Device) GetOsType() RtbRequest_Device_OsType {
	if x != nil {
		return x.OsType
	}
	return RtbRequest_Device_OS_UNKNOWN
}

func (x *RtbRequest_Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *RtbRequest_Device) GetConnType() RtbRequest_Device_ConnectionType {
	if x != nil {
		return x.ConnType
	}
	return RtbRequest_Device_CONN_UNKNOWN
}

func (x *RtbRequest_Device) GetCarrier() RtbRequest_Device_CarrierType {
	if x != nil {
		return x.Carrier
	}
	return RtbRequest_Device_CARRIER_UNKNOWN
}

func (x *RtbRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *RtbRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *RtbRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *RtbRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *RtbRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *RtbRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *RtbRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *RtbRequest_Device) GetGeo() *RtbRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *RtbRequest_Device) GetScreenWidth() uint32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *RtbRequest_Device) GetScreenHeight() uint32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *RtbRequest_Device) GetStartupTime() string {
	if x != nil {
		return x.StartupTime
	}
	return ""
}

func (x *RtbRequest_Device) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *RtbRequest_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *RtbRequest_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *RtbRequest_Device) GetMemTotal() uint64 {
	if x != nil {
		return x.MemTotal
	}
	return 0
}

func (x *RtbRequest_Device) GetDiskTotal() uint64 {
	if x != nil {
		return x.DiskTotal
	}
	return 0
}

func (x *RtbRequest_Device) GetMbTime() string {
	if x != nil {
		return x.MbTime
	}
	return ""
}

func (x *RtbRequest_Device) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *RtbRequest_Device) GetLocalTzTime() string {
	if x != nil {
		return x.LocalTzTime
	}
	return ""
}

func (x *RtbRequest_Device) GetOrgModel() string {
	if x != nil {
		return x.OrgModel
	}
	return ""
}

func (x *RtbRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *RtbRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *RtbRequest_Device) GetBirthTime() string {
	if x != nil {
		return x.BirthTime
	}
	return ""
}

func (x *RtbRequest_Device) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *RtbRequest_Device) GetCaidList() []*RtbRequest_CaidSt {
	if x != nil {
		return x.CaidList
	}
	return nil
}

// user
type RtbRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`                                    //必填。用户ID。
	Gender RtbRequest_User_Gender `protobuf:"varint,2,opt,name=gender,proto3,enum=RtbRequest_User_Gender" json:"gender,omitempty"` // 可选。用户的性别。
	Age    uint32                 `protobuf:"varint,3,opt,name=age,proto3" json:"age,omitempty"`                                   //可选。年龄。
	Yob    uint32                 `protobuf:"varint,4,opt,name=yob,proto3" json:"yob,omitempty"`                                   //可选。出生年月。
}

func (x *RtbRequest_User) Reset() {
	*x = RtbRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_User) ProtoMessage() {}

func (x *RtbRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_User.ProtoReflect.Descriptor instead.
func (*RtbRequest_User) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 3}
}

func (x *RtbRequest_User) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *RtbRequest_User) GetGender() RtbRequest_User_Gender {
	if x != nil {
		return x.Gender
	}
	return RtbRequest_User_UNKNOWN
}

func (x *RtbRequest_User) GetAge() uint32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *RtbRequest_User) GetYob() uint32 {
	if x != nil {
		return x.Yob
	}
	return 0
}

// adslot
type RtbRequest_Adslot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid         string                    `protobuf:"bytes,1,opt,name=impid,proto3" json:"impid,omitempty"`                                                         //必填。广告位唯一ID。
	Templates     []Template                `protobuf:"varint,2,rep,packed,name=templates,proto3,enum=Template" json:"templates,omitempty"`                           //必填。该广告位支持的广告类型。
	Price         uint32                    `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                                        //必填。协商的底价。
	Size          []*RtbRequest_Adslot_Size `protobuf:"bytes,4,rep,name=size,proto3" json:"size,omitempty"`                                                           //选填。当前广告位支持的尺寸。
	TagId         string                    `protobuf:"bytes,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`                                            //选填。广告位标识。详见<<附录2最右广告位标识>>
	MaterialTypes []MType                   `protobuf:"varint,6,rep,packed,name=material_types,json=materialTypes,proto3,enum=MType" json:"material_types,omitempty"` //选填。(开屏仅支持图片素材，信息流支持图片和视。)
}

func (x *RtbRequest_Adslot) Reset() {
	*x = RtbRequest_Adslot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_Adslot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_Adslot) ProtoMessage() {}

func (x *RtbRequest_Adslot) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_Adslot.ProtoReflect.Descriptor instead.
func (*RtbRequest_Adslot) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 4}
}

func (x *RtbRequest_Adslot) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *RtbRequest_Adslot) GetTemplates() []Template {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *RtbRequest_Adslot) GetPrice() uint32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *RtbRequest_Adslot) GetSize() []*RtbRequest_Adslot_Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *RtbRequest_Adslot) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *RtbRequest_Adslot) GetMaterialTypes() []MType {
	if x != nil {
		return x.MaterialTypes
	}
	return nil
}

type RtbRequest_FeaturesSupport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnableSplashDownload RtbRequest_EnableType `protobuf:"varint,1,opt,name=enable_splash_download,json=enableSplashDownload,proto3,enum=RtbRequest_EnableType" json:"enable_splash_download,omitempty"`   //支持开屏下载类型
	EnableFeedDpDownload RtbRequest_EnableType `protobuf:"varint,2,opt,name=enable_feed_dp_download,json=enableFeedDpDownload,proto3,enum=RtbRequest_EnableType" json:"enable_feed_dp_download,omitempty"` //信息流支持Deeplink失败后下载
}

func (x *RtbRequest_FeaturesSupport) Reset() {
	*x = RtbRequest_FeaturesSupport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_FeaturesSupport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_FeaturesSupport) ProtoMessage() {}

func (x *RtbRequest_FeaturesSupport) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_FeaturesSupport.ProtoReflect.Descriptor instead.
func (*RtbRequest_FeaturesSupport) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 5}
}

func (x *RtbRequest_FeaturesSupport) GetEnableSplashDownload() RtbRequest_EnableType {
	if x != nil {
		return x.EnableSplashDownload
	}
	return RtbRequest_DISABLE
}

func (x *RtbRequest_FeaturesSupport) GetEnableFeedDpDownload() RtbRequest_EnableType {
	if x != nil {
		return x.EnableFeedDpDownload
	}
	return RtbRequest_DISABLE
}

type RtbRequest_CaidSt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid    string `protobuf:"bytes,1,opt,name=caid,proto3" json:"caid,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RtbRequest_CaidSt) Reset() {
	*x = RtbRequest_CaidSt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_CaidSt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_CaidSt) ProtoMessage() {}

func (x *RtbRequest_CaidSt) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_CaidSt.ProtoReflect.Descriptor instead.
func (*RtbRequest_CaidSt) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 6}
}

func (x *RtbRequest_CaidSt) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *RtbRequest_CaidSt) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// 地理位置
type RtbRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude  float32 `protobuf:"fixed32,1,opt,name=latitude,proto3" json:"latitude,omitempty"`   //纬度
	Longitude float32 `protobuf:"fixed32,2,opt,name=longitude,proto3" json:"longitude,omitempty"` //经度
	City      string  `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`             //城市，中文即可(utf-8编码)
	Province  string  `protobuf:"bytes,4,opt,name=province,proto3" json:"province,omitempty"`     //省份，中文即可(utf-8编码)
	District  string  `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`     //区县，中文即可(utf-8编码)
}

func (x *RtbRequest_Device_Geo) Reset() {
	*x = RtbRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_Device_Geo) ProtoMessage() {}

func (x *RtbRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*RtbRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *RtbRequest_Device_Geo) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *RtbRequest_Device_Geo) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *RtbRequest_Device_Geo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *RtbRequest_Device_Geo) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *RtbRequest_Device_Geo) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

type RtbRequest_Adslot_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  uint32 `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`   //可选。广告位的宽度。
	Height uint32 `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"` //可选。广告位的高度。
}

func (x *RtbRequest_Adslot_Size) Reset() {
	*x = RtbRequest_Adslot_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbRequest_Adslot_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbRequest_Adslot_Size) ProtoMessage() {}

func (x *RtbRequest_Adslot_Size) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbRequest_Adslot_Size.ProtoReflect.Descriptor instead.
func (*RtbRequest_Adslot_Size) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *RtbRequest_Adslot_Size) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RtbRequest_Adslot_Size) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type RtbResponse_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid        string                      `protobuf:"bytes,1,opt,name=impid,proto3" json:"impid,omitempty"`                                                         //必填。广告位唯一ID，与请求中的impid保持一致。
	PriceType    RtbResponse_Ad_PriceType    `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3,enum=RtbResponse_Ad_PriceType" json:"price_type,omitempty"` //必填。计费合作方式。
	Price        uint64                      `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                                        //必填。广告出价，单位分。
	AdvName      string                      `protobuf:"bytes,4,opt,name=adv_name,json=advName,proto3" json:"adv_name,omitempty"`                                      //广告主名称。
	CreativeId   string                      `protobuf:"bytes,5,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`                             //广告创意ID(用于追溯对应广告的投放情况，请确保其唯一性)。
	Industry     string                      `protobuf:"bytes,6,opt,name=industry,proto3" json:"industry,omitempty"`                                                   //行业类型。见附录<<附录1行业类型>>。
	Material     *RtbResponse_Ad_Material    `protobuf:"bytes,7,opt,name=material,proto3" json:"material,omitempty"`                                                   //广告素材。
	Template     Template                    `protobuf:"varint,8,opt,name=template,proto3,enum=Template" json:"template,omitempty"`                                    //必填。该广告位的广告类型。
	StyleId      string                      `protobuf:"bytes,9,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`                                      //内部使用。广告位样式id。
	Interaction  *RtbResponse_Ad_Interaction `protobuf:"bytes,10,opt,name=interaction,proto3" json:"interaction,omitempty"`                                            //必填。交互链接。
	Tracking     *RtbResponse_Ad_Tracking    `protobuf:"bytes,11,opt,name=tracking,proto3" json:"tracking,omitempty"`                                                  //监测链接
	WinUrl       string                      `protobuf:"bytes,12,opt,name=win_url,json=winUrl,proto3" json:"win_url,omitempty"`                                        //竞胜通知url
	Ext          string                      `protobuf:"bytes,13,opt,name=ext,proto3" json:"ext,omitempty"`                                                            //内部使用。携带信息
	BidFailedUrl string                      `protobuf:"bytes,14,opt,name=bid_failed_url,json=bidFailedUrl,proto3" json:"bid_failed_url,omitempty"`                    //竞价失败通知url
}

func (x *RtbResponse_Ad) Reset() {
	*x = RtbResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad) ProtoMessage() {}

func (x *RtbResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RtbResponse_Ad) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *RtbResponse_Ad) GetPriceType() RtbResponse_Ad_PriceType {
	if x != nil {
		return x.PriceType
	}
	return RtbResponse_Ad_CPM
}

func (x *RtbResponse_Ad) GetPrice() uint64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *RtbResponse_Ad) GetAdvName() string {
	if x != nil {
		return x.AdvName
	}
	return ""
}

func (x *RtbResponse_Ad) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *RtbResponse_Ad) GetIndustry() string {
	if x != nil {
		return x.Industry
	}
	return ""
}

func (x *RtbResponse_Ad) GetMaterial() *RtbResponse_Ad_Material {
	if x != nil {
		return x.Material
	}
	return nil
}

func (x *RtbResponse_Ad) GetTemplate() Template {
	if x != nil {
		return x.Template
	}
	return Template_SPLASH_DEFAULT
}

func (x *RtbResponse_Ad) GetStyleId() string {
	if x != nil {
		return x.StyleId
	}
	return ""
}

func (x *RtbResponse_Ad) GetInteraction() *RtbResponse_Ad_Interaction {
	if x != nil {
		return x.Interaction
	}
	return nil
}

func (x *RtbResponse_Ad) GetTracking() *RtbResponse_Ad_Tracking {
	if x != nil {
		return x.Tracking
	}
	return nil
}

func (x *RtbResponse_Ad) GetWinUrl() string {
	if x != nil {
		return x.WinUrl
	}
	return ""
}

func (x *RtbResponse_Ad) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *RtbResponse_Ad) GetBidFailedUrl() string {
	if x != nil {
		return x.BidFailedUrl
	}
	return ""
}

type RtbResponse_Ad_Material struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               //产品名称
	IconUrl    string                         `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`          //产品logo
	Video      *RtbResponse_Ad_Material_Video `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`                             //视频素材。
	Img        *RtbResponse_Ad_Material_Image `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`                                 //图片素材。
	Desc       string                         `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`                               //选填。广告描述。
	ButtonText string                         `protobuf:"bytes,6,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"` //选填。按钮文本。
}

func (x *RtbResponse_Ad_Material) Reset() {
	*x = RtbResponse_Ad_Material{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad_Material) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad_Material) ProtoMessage() {}

func (x *RtbResponse_Ad_Material) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad_Material.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad_Material) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *RtbResponse_Ad_Material) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RtbResponse_Ad_Material) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Material) GetVideo() *RtbResponse_Ad_Material_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *RtbResponse_Ad_Material) GetImg() *RtbResponse_Ad_Material_Image {
	if x != nil {
		return x.Img
	}
	return nil
}

func (x *RtbResponse_Ad_Material) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RtbResponse_Ad_Material) GetButtonText() string {
	if x != nil {
		return x.ButtonText
	}
	return ""
}

type RtbResponse_Ad_Interaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LandingUrl    string `protobuf:"bytes,1,opt,name=landing_url,json=landingUrl,proto3" json:"landing_url,omitempty"`             //选填。落地页链接(内部H5唤起链接)
	BrowserUrl    string `protobuf:"bytes,2,opt,name=browser_url,json=browserUrl,proto3" json:"browser_url,omitempty"`             //选填。外部浏览器链接
	DownloadUrl   string `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`          //选填。下载链接
	PkgName       string `protobuf:"bytes,4,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name,omitempty"`                      //选填。下载app的包名（下载必填）
	InvokeUrl     string `protobuf:"bytes,5,opt,name=invoke_url,json=invokeUrl,proto3" json:"invoke_url,omitempty"`                //选填。 唤起第三方app链接
	DeveloperName string `protobuf:"bytes,6,opt,name=developer_name,json=developerName,proto3" json:"developer_name,omitempty"`    //选填。下载app的开发者名称（下载必填）
	AppVersion    string `protobuf:"bytes,7,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`             //选填。下载app的版本号（下载必填）
	PermissionUrl string `protobuf:"bytes,8,opt,name=permission_url,json=permissionUrl,proto3" json:"permission_url,omitempty"`    //选填。下载app的应用权限（下载必填）
	PrivacyUrl    string `protobuf:"bytes,9,opt,name=privacy_url,json=privacyUrl,proto3" json:"privacy_url,omitempty"`             //选填。下载app的隐私协议（下载必填）
	AppIntroUrl   string `protobuf:"bytes,10,opt,name=app_intro_url,json=appIntroUrl,proto3" json:"app_intro_url,omitempty"`       //选填。下载app的产品介绍页面（下载必填）
	WxMiniappPath string `protobuf:"bytes,11,opt,name=wx_miniapp_path,json=wxMiniappPath,proto3" json:"wx_miniapp_path,omitempty"` //选填。微信小程序path
	WxMiniappId   string `protobuf:"bytes,12,opt,name=wx_miniapp_id,json=wxMiniappId,proto3" json:"wx_miniapp_id,omitempty"`       //选填。微信小程序id
}

func (x *RtbResponse_Ad_Interaction) Reset() {
	*x = RtbResponse_Ad_Interaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad_Interaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad_Interaction) ProtoMessage() {}

func (x *RtbResponse_Ad_Interaction) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad_Interaction.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad_Interaction) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *RtbResponse_Ad_Interaction) GetLandingUrl() string {
	if x != nil {
		return x.LandingUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetBrowserUrl() string {
	if x != nil {
		return x.BrowserUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetInvokeUrl() string {
	if x != nil {
		return x.InvokeUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetDeveloperName() string {
	if x != nil {
		return x.DeveloperName
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetPermissionUrl() string {
	if x != nil {
		return x.PermissionUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetPrivacyUrl() string {
	if x != nil {
		return x.PrivacyUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetAppIntroUrl() string {
	if x != nil {
		return x.AppIntroUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetWxMiniappPath() string {
	if x != nil {
		return x.WxMiniappPath
	}
	return ""
}

func (x *RtbResponse_Ad_Interaction) GetWxMiniappId() string {
	if x != nil {
		return x.WxMiniappId
	}
	return ""
}

type RtbResponse_Ad_Tracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpTracking            []string `protobuf:"bytes,1,rep,name=imp_tracking,json=impTracking,proto3" json:"imp_tracking,omitempty"`                                      //曝光监测
	ClickTracking          []string `protobuf:"bytes,2,rep,name=click_tracking,json=clickTracking,proto3" json:"click_tracking,omitempty"`                                //点击监测
	VideoStartTracking     []string `protobuf:"bytes,3,rep,name=video_start_tracking,json=videoStartTracking,proto3" json:"video_start_tracking,omitempty"`               //视频播放开始监测(暂不支持)
	VideoFinishTracking    []string `protobuf:"bytes,4,rep,name=video_finish_tracking,json=videoFinishTracking,proto3" json:"video_finish_tracking,omitempty"`            //视频播放结束监测(暂不支持)
	InvokeFailedTracking   []string `protobuf:"bytes,5,rep,name=invoke_failed_tracking,json=invokeFailedTracking,proto3" json:"invoke_failed_tracking,omitempty"`         //唤起失败监测
	InvokeWaitSuccTracking []string `protobuf:"bytes,6,rep,name=invoke_wait_succ_tracking,json=invokeWaitSuccTracking,proto3" json:"invoke_wait_succ_tracking,omitempty"` //唤起成功监测(原理参考文档)
	VideoPlayXTracking     []string `protobuf:"bytes,7,rep,name=video_play_x_tracking,json=videoPlayXTracking,proto3" json:"video_play_x_tracking,omitempty"`             //视频播放x毫秒的监测(暂不支持)
	VideoPlayX             uint32   `protobuf:"varint,8,opt,name=video_play_x,json=videoPlayX,proto3" json:"video_play_x,omitempty"`                                      //视频播放x毫秒的监测的触发时间间隔
	VideoCloseTracking     []string `protobuf:"bytes,9,rep,name=video_close_tracking,json=videoCloseTracking,proto3" json:"video_close_tracking,omitempty"`               //视频关闭监测(暂不支持)
}

func (x *RtbResponse_Ad_Tracking) Reset() {
	*x = RtbResponse_Ad_Tracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad_Tracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad_Tracking) ProtoMessage() {}

func (x *RtbResponse_Ad_Tracking) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad_Tracking.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad_Tracking) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 2}
}

func (x *RtbResponse_Ad_Tracking) GetImpTracking() []string {
	if x != nil {
		return x.ImpTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetClickTracking() []string {
	if x != nil {
		return x.ClickTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetVideoStartTracking() []string {
	if x != nil {
		return x.VideoStartTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetVideoFinishTracking() []string {
	if x != nil {
		return x.VideoFinishTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetInvokeFailedTracking() []string {
	if x != nil {
		return x.InvokeFailedTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetInvokeWaitSuccTracking() []string {
	if x != nil {
		return x.InvokeWaitSuccTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetVideoPlayXTracking() []string {
	if x != nil {
		return x.VideoPlayXTracking
	}
	return nil
}

func (x *RtbResponse_Ad_Tracking) GetVideoPlayX() uint32 {
	if x != nil {
		return x.VideoPlayX
	}
	return 0
}

func (x *RtbResponse_Ad_Tracking) GetVideoCloseTracking() []string {
	if x != nil {
		return x.VideoCloseTracking
	}
	return nil
}

type RtbResponse_Ad_Material_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"` //必填。视频url
	CoverUrl string `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填。视频封面图url
	Height   uint32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width    uint32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	Dur      uint32 `protobuf:"varint,5,opt,name=dur,proto3" json:"dur,omitempty"` //视频播放时长(毫秒)
}

func (x *RtbResponse_Ad_Material_Video) Reset() {
	*x = RtbResponse_Ad_Material_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad_Material_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad_Material_Video) ProtoMessage() {}

func (x *RtbResponse_Ad_Material_Video) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad_Material_Video.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad_Material_Video) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *RtbResponse_Ad_Material_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Material_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Material_Video) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *RtbResponse_Ad_Material_Video) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RtbResponse_Ad_Material_Video) GetDur() uint32 {
	if x != nil {
		return x.Dur
	}
	return 0
}

type RtbResponse_Ad_Material_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverUrl string   `protobuf:"bytes,1,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填。封面图片url
	ImgUrls  []string `protobuf:"bytes,2,rep,name=img_urls,json=imgUrls,proto3" json:"img_urls,omitempty"`    //内部使用。多图url
	Height   uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width    uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *RtbResponse_Ad_Material_Image) Reset() {
	*x = RtbResponse_Ad_Material_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_zuiyou_3_5_9_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtbResponse_Ad_Material_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtbResponse_Ad_Material_Image) ProtoMessage() {}

func (x *RtbResponse_Ad_Material_Image) ProtoReflect() protoreflect.Message {
	mi := &file_zuiyou_3_5_9_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtbResponse_Ad_Material_Image.ProtoReflect.Descriptor instead.
func (*RtbResponse_Ad_Material_Image) Descriptor() ([]byte, []int) {
	return file_zuiyou_3_5_9_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *RtbResponse_Ad_Material_Image) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *RtbResponse_Ad_Material_Image) GetImgUrls() []string {
	if x != nil {
		return x.ImgUrls
	}
	return nil
}

func (x *RtbResponse_Ad_Material_Image) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *RtbResponse_Ad_Material_Image) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

var File_zuiyou_3_5_9_proto protoreflect.FileDescriptor

var file_zuiyou_3_5_9_proto_rawDesc = []byte{
	0x0a, 0x12, 0x7a, 0x75, 0x69, 0x79, 0x6f, 0x75, 0x5f, 0x33, 0x2e, 0x35, 0x2e, 0x39, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x16, 0x0a, 0x0a, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x21, 0x0a,
	0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x52, 0x74, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70,
	0x12, 0x2a, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x52, 0x74, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x52, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x46, 0x0a, 0x10, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x21, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03,
	0x70, 0x6d, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x1e,
	0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x1a, 0x6c,
	0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x8e, 0x0c, 0x0a,
	0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x52, 0x74, 0x62,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x52, 0x74,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x52, 0x74, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61,
	0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d,
	0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x65, 0x6d,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x62, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x62, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x7a, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x54, 0x7a,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x66, 0x76, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x23, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x53, 0x74, 0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x1a, 0x8b, 0x01, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x22, 0x37, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x0e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x22, 0x2e, 0x0a, 0x06, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x22, 0x68, 0x0a, 0x0e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c,
	0x43, 0x4f, 0x4e, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45,
	0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f,
	0x35, 0x47, 0x10, 0x05, 0x22, 0x47, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x49, 0x43, 0x4f, 0x4d, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x1a, 0x9a, 0x01,
	0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x79,
	0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x79, 0x6f, 0x62, 0x22, 0x2b, 0x0a,
	0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x02, 0x1a, 0x86, 0x02, 0x0a, 0x06, 0x41,
	0x64, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x09, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x09,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x69, 0x7a,
	0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x06, 0x2e, 0x4d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x34, 0x0a,
	0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x1a, 0xae, 0x01, 0x0a, 0x0f, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x4c, 0x0a, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x73, 0x70, 0x6c, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x6c, 0x61, 0x73, 0x68, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4d, 0x0a, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x64, 0x5f, 0x64, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x65, 0x65, 0x64, 0x44, 0x70, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x1a, 0x36, 0x0a, 0x06, 0x43, 0x61, 0x69, 0x64, 0x53, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x25, 0x0a, 0x0a,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49,
	0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x41, 0x42, 0x4c,
	0x45, 0x10, 0x01, 0x22, 0xf9, 0x10, 0x0a, 0x0b, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12,
	0x34, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x1a, 0xcf, 0x0e, 0x0a, 0x02, 0x41, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x52, 0x74, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x76, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x76, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x34, 0x0a,
	0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x52, 0x74, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x69,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x69, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62,
	0x69, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x1a, 0xc9, 0x03, 0x0a, 0x08,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x52, 0x74, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x30, 0x0a,
	0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x52, 0x74, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x1a, 0x81, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b,
	0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x75, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x75, 0x72, 0x1a, 0x6d, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x1a, 0xac, 0x03, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x6f, 0x77,
	0x73, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x6b,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x76,
	0x6f, 0x6b, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e,
	0x74, 0x72, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x78,
	0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x78, 0x4d, 0x69, 0x6e, 0x69, 0x61, 0x70, 0x70, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x22, 0x0a, 0x0d, 0x77, 0x78, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x78, 0x4d, 0x69, 0x6e,
	0x69, 0x61, 0x70, 0x70, 0x49, 0x64, 0x1a, 0xb2, 0x03, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a,
	0x14, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x32, 0x0a, 0x15, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x6e, 0x76,
	0x6f, 0x6b, 0x65, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x69, 0x6e,
	0x76, 0x6f, 0x6b, 0x65, 0x57, 0x61, 0x69, 0x74, 0x53, 0x75, 0x63, 0x63, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x78, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x58, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x58, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x1d, 0x0a, 0x09, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x4d, 0x10,
	0x00, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x43, 0x10, 0x01, 0x22, 0x53, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x51, 0x5f, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x51, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0f,
	0x0a, 0x0b, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x03, 0x12,
	0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x04, 0x2a,
	0x98, 0x03, 0x0a, 0x08, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x42, 0x52, 0x4f, 0x57, 0x53,
	0x45, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x4f,
	0x50, 0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x50, 0x4c,
	0x41, 0x53, 0x48, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x03, 0x12, 0x10,
	0x0a, 0x0c, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x04,
	0x12, 0x10, 0x0a, 0x0c, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x42, 0x52, 0x4f, 0x57, 0x53, 0x45, 0x52,
	0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x4f, 0x50,
	0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x42, 0x52, 0x4f, 0x57, 0x53, 0x45, 0x52, 0x10, 0x09,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f,
	0x50, 0x55, 0x50, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x0c, 0x12, 0x11, 0x0a,
	0x0d, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x5f, 0x42, 0x52, 0x4f, 0x57, 0x53, 0x45, 0x52, 0x10, 0x0d,
	0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x10, 0x0e, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x5f, 0x4f, 0x50,
	0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x10, 0x12, 0x12, 0x0a, 0x0e,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x52, 0x4f, 0x57, 0x53, 0x45, 0x52, 0x10, 0x11,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x12, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x13, 0x2a, 0x1b, 0x0a, 0x05, 0x4d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4d, 0x47, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x01, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x2e, 0x2f, 0x7a, 0x75,
	0x69, 0x79, 0x6f, 0x75, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_zuiyou_3_5_9_proto_rawDescOnce sync.Once
	file_zuiyou_3_5_9_proto_rawDescData = file_zuiyou_3_5_9_proto_rawDesc
)

func file_zuiyou_3_5_9_proto_rawDescGZIP() []byte {
	file_zuiyou_3_5_9_proto_rawDescOnce.Do(func() {
		file_zuiyou_3_5_9_proto_rawDescData = protoimpl.X.CompressGZIP(file_zuiyou_3_5_9_proto_rawDescData)
	})
	return file_zuiyou_3_5_9_proto_rawDescData
}

var file_zuiyou_3_5_9_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_zuiyou_3_5_9_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_zuiyou_3_5_9_proto_goTypes = []interface{}{
	(Template)(0),                         // 0: Template
	(MType)(0),                            // 1: MType
	(RtbRequest_EnableType)(0),            // 2: RtbRequest.EnableType
	(RtbRequest_Device_DeviceType)(0),     // 3: RtbRequest.Device.DeviceType
	(RtbRequest_Device_OsType)(0),         // 4: RtbRequest.Device.OsType
	(RtbRequest_Device_ConnectionType)(0), // 5: RtbRequest.Device.ConnectionType
	(RtbRequest_Device_CarrierType)(0),    // 6: RtbRequest.Device.CarrierType
	(RtbRequest_User_Gender)(0),           // 7: RtbRequest.User.Gender
	(RtbResponse_Status)(0),               // 8: RtbResponse.Status
	(RtbResponse_Ad_PriceType)(0),         // 9: RtbResponse.Ad.PriceType
	(*RtbRequest)(nil),                    // 10: RtbRequest
	(*RtbResponse)(nil),                   // 11: RtbResponse
	(*RtbRequest_Pmp)(nil),                // 12: RtbRequest.Pmp
	(*RtbRequest_App)(nil),                // 13: RtbRequest.App
	(*RtbRequest_Device)(nil),             // 14: RtbRequest.Device
	(*RtbRequest_User)(nil),               // 15: RtbRequest.User
	(*RtbRequest_Adslot)(nil),             // 16: RtbRequest.Adslot
	(*RtbRequest_FeaturesSupport)(nil),    // 17: RtbRequest.FeaturesSupport
	(*RtbRequest_CaidSt)(nil),             // 18: RtbRequest.CaidSt
	(*RtbRequest_Device_Geo)(nil),         // 19: RtbRequest.Device.Geo
	(*RtbRequest_Adslot_Size)(nil),        // 20: RtbRequest.Adslot.Size
	(*RtbResponse_Ad)(nil),                // 21: RtbResponse.Ad
	(*RtbResponse_Ad_Material)(nil),       // 22: RtbResponse.Ad.Material
	(*RtbResponse_Ad_Interaction)(nil),    // 23: RtbResponse.Ad.Interaction
	(*RtbResponse_Ad_Tracking)(nil),       // 24: RtbResponse.Ad.Tracking
	(*RtbResponse_Ad_Material_Video)(nil), // 25: RtbResponse.Ad.Material.Video
	(*RtbResponse_Ad_Material_Image)(nil), // 26: RtbResponse.Ad.Material.Image
}
var file_zuiyou_3_5_9_proto_depIdxs = []int32{
	13, // 0: RtbRequest.app:type_name -> RtbRequest.App
	14, // 1: RtbRequest.device:type_name -> RtbRequest.Device
	15, // 2: RtbRequest.user:type_name -> RtbRequest.User
	16, // 3: RtbRequest.adslots:type_name -> RtbRequest.Adslot
	17, // 4: RtbRequest.features_support:type_name -> RtbRequest.FeaturesSupport
	12, // 5: RtbRequest.pmp:type_name -> RtbRequest.Pmp
	8,  // 6: RtbResponse.status_code:type_name -> RtbResponse.Status
	21, // 7: RtbResponse.ads:type_name -> RtbResponse.Ad
	3,  // 8: RtbRequest.Device.device_type:type_name -> RtbRequest.Device.DeviceType
	4,  // 9: RtbRequest.Device.os_type:type_name -> RtbRequest.Device.OsType
	5,  // 10: RtbRequest.Device.conn_type:type_name -> RtbRequest.Device.ConnectionType
	6,  // 11: RtbRequest.Device.carrier:type_name -> RtbRequest.Device.CarrierType
	19, // 12: RtbRequest.Device.geo:type_name -> RtbRequest.Device.Geo
	18, // 13: RtbRequest.Device.caid_list:type_name -> RtbRequest.CaidSt
	7,  // 14: RtbRequest.User.gender:type_name -> RtbRequest.User.Gender
	0,  // 15: RtbRequest.Adslot.templates:type_name -> Template
	20, // 16: RtbRequest.Adslot.size:type_name -> RtbRequest.Adslot.Size
	1,  // 17: RtbRequest.Adslot.material_types:type_name -> MType
	2,  // 18: RtbRequest.FeaturesSupport.enable_splash_download:type_name -> RtbRequest.EnableType
	2,  // 19: RtbRequest.FeaturesSupport.enable_feed_dp_download:type_name -> RtbRequest.EnableType
	9,  // 20: RtbResponse.Ad.price_type:type_name -> RtbResponse.Ad.PriceType
	22, // 21: RtbResponse.Ad.material:type_name -> RtbResponse.Ad.Material
	0,  // 22: RtbResponse.Ad.template:type_name -> Template
	23, // 23: RtbResponse.Ad.interaction:type_name -> RtbResponse.Ad.Interaction
	24, // 24: RtbResponse.Ad.tracking:type_name -> RtbResponse.Ad.Tracking
	25, // 25: RtbResponse.Ad.Material.video:type_name -> RtbResponse.Ad.Material.Video
	26, // 26: RtbResponse.Ad.Material.img:type_name -> RtbResponse.Ad.Material.Image
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_zuiyou_3_5_9_proto_init() }
func file_zuiyou_3_5_9_proto_init() {
	if File_zuiyou_3_5_9_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_zuiyou_3_5_9_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_Adslot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_FeaturesSupport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_CaidSt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbRequest_Adslot_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad_Material); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad_Interaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad_Tracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad_Material_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_zuiyou_3_5_9_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtbResponse_Ad_Material_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_zuiyou_3_5_9_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_zuiyou_3_5_9_proto_goTypes,
		DependencyIndexes: file_zuiyou_3_5_9_proto_depIdxs,
		EnumInfos:         file_zuiyou_3_5_9_proto_enumTypes,
		MessageInfos:      file_zuiyou_3_5_9_proto_msgTypes,
	}.Build()
	File_zuiyou_3_5_9_proto = out.File
	file_zuiyou_3_5_9_proto_rawDesc = nil
	file_zuiyou_3_5_9_proto_goTypes = nil
	file_zuiyou_3_5_9_proto_depIdxs = nil
}
