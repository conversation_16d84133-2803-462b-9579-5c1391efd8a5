// Copyright 2020 xiaochuankeji Inc. All Rights Reserved.
syntax = "proto3";

option go_package = "../zuiyou";

message RtbRequest {
    string api_version = 1; // 必填。此API的版本。
    string request_id = 2;  // 必填。自定义的请求id，保证其请求的唯一性。
    uint32 timeout = 3;     // 必填。超时时间，要求再该时间范围内给予广告返回。
    App app = 4;   //必填。移动app信息。
    Device device = 5; //必填。移动设备的信息。
    User user = 6; //可选。用户信息，用于人群定向。
    repeated Adslot adslots = 7; // 必填，至少一个。广告位的信息。
    uint32 cnt = 8; //必填。请求广告个数。
    uint32 test = 9;   //可选。内部使用。
    string ext = 10;   //可选。内部使用。
    FeaturesSupport features_support = 11;  //可选。用户拓展功能的支持。
    Pmp pmp = 12; // 可选，内部使用
    string PlatformId = 13; //平台编号。
    repeated string pkg_list = 14; // 设备安装列表

    // pmp
    message Pmp {
        uint32 deal_id = 1; // 内部使用
    }

    // app
    message App {
        string appid = 1;  //app应用id，由最右提供。
        string name = 2; // 必填。app应用名。
        string package_name = 3; //必填。 包名。
        string version = 4; // 必填。 app应用的版本。
    }

    // device
    message Device {
        string ua = 1; //必填。User-Agent。
        string ip = 2; // 必填。设备的ip，用于定位，地域定向。
        string ipv6 = 3; //选填。设备的ipv6，用于定位，地域定向。
        string make = 4; //选填。设备制造商。
        string model = 5; //选填。设备型号。
        DeviceType device_type = 6;    //必填。设备类型。
        OsType os_type = 7; //必填。操作系统类型。
        string os_version = 8; //选填。操作系统版本号。
        ConnectionType conn_type = 9; // 可选。设备的网络类型。
        CarrierType carrier = 10; // 可选。运营商类型。
        string mac = 11; // 可选。设备的mac地址。
        string idfa = 12; // 可选。设备的IDFA。
        string idfa_md5 = 13; // 可选。设备IDFA_MD5。
        string imei = 14; //设备IMEI（明文）。
        string imei_md5 = 15; //设备IMEI_MD5。
        string oaid = 16; // 可选。匿名设备标识符。
        string android_id = 17; //设备Android ID。
        Geo geo = 18; // 可选。设备的地理位置信息。
        uint32 screen_width = 19; //设备屏宽。
        uint32 screen_height = 20; //设备屏高。
        string startup_time = 21;   //可选。手机开机时间。
        string country_code = 22;   //可选。local地区。
        string language = 23;   //可选。设备设置的语言。
        string phone_name = 24; //可选。手机名称的MD5码。
        uint64 mem_total = 25; //可选。系统总内存空间。
        uint64 disk_total = 26; //可选。磁盘总空间。
        string mb_time = 27; //可选。系统版本更新时间。
        string device_model = 28; //可选。设备model。
        string local_tz_time = 29; //可选。local时区。
        string org_model = 30; //可选。iOS机型原始值。
        string boot_mark = 31; //可选。系统启动标识
        string update_mark = 32; //可选。系统更新标识
        string birth_time = 33; //可选。设备初始化时间
        string idfv = 34; //可选。iOS设备的IDFV。
        repeated CaidSt caid_list = 35; //可选。CAID列表


        // 设备类型
        enum DeviceType {
            DEVICE_UNKNOWN = 0;
            PHONE = 1; // 手机。
            TABLET = 2; // 平板。
        }

        // 操作系统类型
        enum OsType {
            OS_UNKNOWN = 0;
            ANDROID = 1;
            IOS = 2;
        }

        // 网络类型
        enum ConnectionType {
            CONN_UNKNOWN = 0;
            WIFI = 1;
            MOBILE_2G = 2;
            MOBILE_3G = 3;
            MOBILE_4G = 4;
            MOBILE_5G = 5;
        }

        // 运营商类型
        enum CarrierType {
            CARRIER_UNKNOWN = 0;
            MOBILE = 1;
            UNICOM = 2;
            TELECOM = 3;
        }

        //地理位置
        message Geo {
            float latitude = 1; //纬度
            float longitude = 2; //经度
            string city = 3; //城市，中文即可(utf-8编码)
            string province = 4; //省份，中文即可(utf-8编码)
            string district = 5; //区县，中文即可(utf-8编码)
        }
    }

    // user
    message User {
        string uid = 1; //必填。用户ID。
        Gender gender = 2; // 可选。用户的性别。
        uint32 age = 3; //可选。年龄。
        uint32 yob = 4; //可选。出生年月。

        enum Gender {
            UNKNOWN = 0;
            MALE = 1;
            FEMALE = 2;
        }
    }

    // adslot
    message Adslot {
        string impid = 1; //必填。广告位唯一ID。
        repeated Template templates = 2; //必填。该广告位支持的广告类型。
        uint32 price = 3; //必填。协商的底价。
        repeated Size size = 4; //选填。当前广告位支持的尺寸。
        string tag_id = 5; //选填。广告位标识。详见<<附录2最右广告位标识>>
        repeated MType material_types = 6;  //选填。(开屏仅支持图片素材，信息流支持图片和视。)

        message Size {
            uint32 width = 3; //可选。广告位的宽度。
            uint32 height = 4; //可选。广告位的高度。
        }
    }

    message FeaturesSupport {
        EnableType enable_splash_download = 1;  //支持开屏下载类型
        EnableType enable_feed_dp_download = 2; //信息流支持Deeplink失败后下载
    }

    enum EnableType {
        DISABLE = 0;
        ENABLE = 1;
    }

    message CaidSt {
        string caid = 1;
        string version = 2;
    }
}

message RtbResponse {
    string response_id = 1;  // 必填。响应id，与请求id保持一致。
    string site_name = 2;  //必填。平台方名称。
    uint32 processing_time_ms = 3; // 可选。从收到请求到返回响应所用的时间。
    Status status_code = 4; // 可选。响应状态码。
    repeated Ad ads = 5;    //可选。广告内容。

    message Ad {
        string impid = 1; //必填。广告位唯一ID，与请求中的impid保持一致。
        PriceType price_type = 2; //必填。计费合作方式。
        uint64 price = 3; //必填。广告出价，单位分。
        string adv_name = 4; //广告主名称。
        string creative_id = 5; //广告创意ID(用于追溯对应广告的投放情况，请确保其唯一性)。
        string industry = 6; //行业类型。见附录<<附录1行业类型>>。
        Material material = 7; //广告素材。
        Template template = 8; //必填。该广告位的广告类型。
        string style_id = 9; //内部使用。广告位样式id。
        Interaction interaction = 10; //必填。交互链接。
        Tracking tracking = 11; //监测链接
        string win_url = 12; //竞胜通知url
        string ext = 13; //内部使用。携带信息
        string bid_failed_url = 14; //竞价失败通知url

        message Material {
            string name = 1; //产品名称
            string icon_url = 2; //产品logo
            Video video = 3; //视频素材。
            Image img = 4; //图片素材。
            string desc = 5; //选填。广告描述。
            string button_text = 6; //选填。按钮文本。

            message Video {
                string video_url = 1; //必填。视频url
                string cover_url = 2; //必填。视频封面图url
                uint32 height = 3;
                uint32 width = 4;
                uint32 dur = 5; //视频播放时长(毫秒)
            }

            message Image {
                string cover_url = 1; //必填。封面图片url
                repeated string img_urls = 2; //内部使用。多图url
                uint32 height = 3;
                uint32 width = 4;
            }
        }

        message Interaction {
            string landing_url = 1; //选填。落地页链接(内部H5唤起链接)
            string browser_url = 2; //选填。外部浏览器链接
            string download_url = 3; //选填。下载链接
            string pkg_name = 4; //选填。下载app的包名（下载必填）
            string invoke_url = 5; //选填。 唤起第三方app链接
            string developer_name = 6; //选填。下载app的开发者名称（下载必填）
            string app_version = 7; //选填。下载app的版本号（下载必填）
            string permission_url = 8; //选填。下载app的应用权限（下载必填）
            string privacy_url = 9; //选填。下载app的隐私协议（下载必填）
            string app_intro_url = 10;//选填。下载app的产品介绍页面（下载必填）
            string wx_miniapp_path = 11;//选填。微信小程序path
            string wx_miniapp_id = 12;//选填。微信小程序id
        }

        message Tracking {
            repeated string imp_tracking = 1; //曝光监测
            repeated string click_tracking = 2; //点击监测
            repeated string video_start_tracking = 3; //视频播放开始监测(暂不支持)
            repeated string video_finish_tracking = 4; //视频播放结束监测(暂不支持)
            repeated string invoke_failed_tracking = 5; //唤起失败监测
            repeated string invoke_wait_succ_tracking = 6; //唤起成功监测(原理参考文档)
            repeated string video_play_x_tracking = 7;  //视频播放x毫秒的监测(暂不支持)
            uint32 video_play_x = 8;    //视频播放x毫秒的监测的触发时间间隔
            repeated string video_close_tracking = 9;   //视频关闭监测(暂不支持)
        }

        enum PriceType {
            CPM = 0;    //目前均采用CPM方式计费
            CPC = 1;
        }
    }

    enum Status {
        REQ_OK = 0; //响应成功并填充广告
        REQ_FAIL = 1; //响应失败，请求参数错误
        DROP_PRICE = 2; //不满足价格需求而丢弃
        DROP_CHOICE = 3; //不满足筛选需求而丢弃(例如推荐模型)
        DROP_OTHER = 4; //其它原因无法填充
    }
}

enum Template {
    SPLASH_DEFAULT = 0; // 开屏打开内部H5
    SPLASH_BROWSER = 1; // 开屏打开外部浏览器(不推荐，后续可能会关闭)
    SPLASH_OPEN_APP = 2; //开屏唤起第三方APP
    SPLASH_DOWNLOAD = 3; //开屏下载APP(当且仅当enable_splash_download时支持)

    FEED_DEFAULT = 4; //原生广告打开内部H5
    FEED_BROWSER = 5; //原生广告打开外部浏览器(不推荐，后续可能会关闭)
    FEED_DOWNLOAD = 6; //原生广告下载APP
    FEED_OPEN_APP = 7; //原生广告唤起第三方APP

    REVIEW_DEFAULT = 8; //废弃。详情页广告打开内部H5
    REVIEW_BROWSER = 9; //废弃。详情页广告打开第三方浏览器
    REVIEW_DOWNLOAD = 10; //废弃。详情页广告下载APP
    REVIEW_OPEN_APP = 11; //废弃。详情页广告唤起第三方APP

    POPUP_DEFAULT = 12; //废弃。弹窗广告打开内部H5
    POPUP_BROWSER = 13; //废弃。弹窗广告打开第三方浏览器
    POPUP_DOWNLOAD = 14; //废弃。弹窗广告下载APP
    POPUP_OPEN_APP = 15; //废弃。弹窗广告唤起第三方APP

    REWARD_DEFAULT = 16; //内部使用。激励视频广告打开内部H5
    REWARD_BROWSER = 17; //内部使用。激励视频广告打开第三方浏览器
    REWARD_DOWNLOAD = 18; //内部使用。激励视频广告下载APP
    REWARD_OPEN_APP = 19; //内部使用。激励视频广告唤起第三方APP
}

enum MType {
    IMG = 0;    //支持图片素材
    VIDEO = 1;  //支持视频素材
}