// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: ifeng-ad-rtb.proto

package fengfei

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户年龄
type UserAgeType int32

const (
	UserAgeType_UNKNOWN_AGE_TYPE UserAgeType = 0 // 未知年龄
	UserAgeType_ZERO_AGE_TYPE    UserAgeType = 1 // 0-17
	UserAgeType_TEN_AGE_TYPE     UserAgeType = 2 // 18-23
	UserAgeType_TWENTY_AGE_TYPE  UserAgeType = 3 // 24-30
	UserAgeType_THIRTY_AGE_TYPE  UserAgeType = 4 // 31-40
	UserAgeType_FORTY_AGE_TYPE   UserAgeType = 5 // 41-49
	UserAgeType_FIFTY_AGE_TYPE   UserAgeType = 6 // 50及以上
)

// Enum value maps for UserAgeType.
var (
	UserAgeType_name = map[int32]string{
		0: "UNKNOWN_AGE_TYPE",
		1: "ZERO_AGE_TYPE",
		2: "TEN_AGE_TYPE",
		3: "TWENTY_AGE_TYPE",
		4: "THIRTY_AGE_TYPE",
		5: "FORTY_AGE_TYPE",
		6: "FIFTY_AGE_TYPE",
	}
	UserAgeType_value = map[string]int32{
		"UNKNOWN_AGE_TYPE": 0,
		"ZERO_AGE_TYPE":    1,
		"TEN_AGE_TYPE":     2,
		"TWENTY_AGE_TYPE":  3,
		"THIRTY_AGE_TYPE":  4,
		"FORTY_AGE_TYPE":   5,
		"FIFTY_AGE_TYPE":   6,
	}
)

func (x UserAgeType) Enum() *UserAgeType {
	p := new(UserAgeType)
	*p = x
	return p
}

func (x UserAgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserAgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_ifeng_ad_rtb_proto_enumTypes[0].Descriptor()
}

func (UserAgeType) Type() protoreflect.EnumType {
	return &file_ifeng_ad_rtb_proto_enumTypes[0]
}

func (x UserAgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserAgeType.Descriptor instead.
func (UserAgeType) EnumDescriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 竞价请求唯一识别符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 竞价类型。1 = First Price, 2 = Second Price Plus. 当前固定为2。也就是采用第二名出价价格作为成交价。
	At int32 `protobuf:"varint,2,opt,name=at,proto3" json:"at,omitempty"`
	//平台类型。1、PC，2、WAP，3、APP
	Platform int32              `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	Imp      []*BidRequest_Imp  `protobuf:"bytes,4,rep,name=imp,proto3" json:"imp,omitempty"`
	App      *BidRequest_App    `protobuf:"bytes,5,opt,name=app,proto3" json:"app,omitempty"`
	Device   *BidRequest_Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	User     *BidRequest_User   `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
	//是否测试模式，0 = 线上， 1 = 测试模式
	Test int32 `protobuf:"varint,8,opt,name=test,proto3" json:"test,omitempty"`
	//禁投广告主行业类型
	Bindus []string `protobuf:"bytes,9,rep,name=bindus,proto3" json:"bindus,omitempty"`
	//只投广告主行业类型
	DeliverTrade []string `protobuf:"bytes,10,rep,name=deliverTrade,proto3" json:"deliverTrade,omitempty"`
	//禁投广告词
	Forbidenwords []string `protobuf:"bytes,11,rep,name=forbidenwords,proto3" json:"forbidenwords,omitempty"`
	//只投广告词
	Onlywords []string         `protobuf:"bytes,12,rep,name=onlywords,proto3" json:"onlywords,omitempty"`
	Site      *BidRequest_Site `protobuf:"bytes,13,opt,name=site,proto3" json:"site,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *BidRequest) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetTest() int32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *BidRequest) GetBindus() []string {
	if x != nil {
		return x.Bindus
	}
	return nil
}

func (x *BidRequest) GetDeliverTrade() []string {
	if x != nil {
		return x.DeliverTrade
	}
	return nil
}

func (x *BidRequest) GetForbidenwords() []string {
	if x != nil {
		return x.Forbidenwords
	}
	return nil
}

func (x *BidRequest) GetOnlywords() []string {
	if x != nil {
		return x.Onlywords
	}
	return nil
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 竞价响应识别符，如不等同于竞价请求，拒绝该竞价响应（对应bidreqeust中的id）
	Id      string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,2,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
	//竞价响应id，用于辅助竞价方追踪监测
	Bidid string `protobuf:"bytes,3,opt,name=bidid,proto3" json:"bidid,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

// 广告位对象广告曝光请求对象组。在一个竞价请求中可包含多个广告曝光请求。一次有效的竞价请求至少有一个广告曝光请求。
type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//竞价请求中广告曝光请求的唯一识别符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//广告类型。1、原生；2视频贴片
	Adtype int32                    `protobuf:"varint,2,opt,name=adtype,proto3" json:"adtype,omitempty"`
	Native []*BidRequest_Imp_Native `protobuf:"bytes,3,rep,name=native,proto3" json:"native,omitempty"`
	Video  *BidRequest_Imp_Video    `protobuf:"bytes,4,opt,name=video,proto3" json:"video,omitempty"`
	//特定广告位置或标签的识别符。当前版本设置为凤飞系统中的广告位id。
	Tagid string `protobuf:"bytes,5,opt,name=tagid,proto3" json:"tagid,omitempty"`
	//标明响应是否需要是https。0：http；1：https。
	Secure int32 `protobuf:"varint,6,opt,name=secure,proto3" json:"secure,omitempty"`
	//竞拍底价。CPM记，单位为人民币分。
	Bidfloor int64               `protobuf:"varint,7,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Pmp      *BidRequest_Imp_Pmp `protobuf:"bytes,8,opt,name=pmp,proto3" json:"pmp,omitempty"`
	//贴片位置 0-前贴片 1-中贴片 2-后贴片
	PatchPos int32 `protobuf:"varint,9,opt,name=patchPos,proto3" json:"patchPos,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetAdtype() int32 {
	if x != nil {
		return x.Adtype
	}
	return 0
}

func (x *BidRequest_Imp) GetNative() []*BidRequest_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil {
		return x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequest_Imp) GetBidfloor() int64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetPmp() *BidRequest_Imp_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_Imp) GetPatchPos() int32 {
	if x != nil {
		return x.PatchPos
	}
	return 0
}

//App信息
type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//凤飞系统app唯一标识符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//app名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	//app本次请求对应包名
	Bundle string `protobuf:"bytes,3,opt,name=bundle,proto3" json:"bundle,omitempty"`
	//app版本号
	Ver string `protobuf:"bytes,4,opt,name=ver,proto3" json:"ver,omitempty"`
	//app 类型
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *BidRequest_App) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 设备信息
type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 浏览器user agent
	Ua string `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	// IPv4地址
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// 设备IMEI号,MD5加密
	Didmd5 string `protobuf:"bytes,3,opt,name=didmd5,proto3" json:"didmd5,omitempty"`
	// iOS终端设备的identifier for advertising明文，保持字母大写。
	Ifa string `protobuf:"bytes,4,opt,name=ifa,proto3" json:"ifa,omitempty"`
	// Android终端设备的AndroidID，MD5加密
	Dpidmd5 string `protobuf:"bytes,5,opt,name=dpidmd5,proto3" json:"dpidmd5,omitempty"`
	// 终端网卡MAC地址，MD5加密
	Macmd5 string `protobuf:"bytes,6,opt,name=macmd5,proto3" json:"macmd5,omitempty"`
	// 网络连接类型。0.未知;1.WIFI;2.2G;3.3G;4.4G;5.5G
	Connectiontype int32 `protobuf:"varint,7,opt,name=connectiontype,proto3" json:"connectiontype,omitempty"`
	// 运营商。0.未知;1.中国移动;2.中国联通;3.中国电信;
	Carrier int32 `protobuf:"varint,8,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// 设备制造商(例如：Apple)
	Make string `protobuf:"bytes,9,opt,name=make,proto3" json:"make,omitempty"`
	// 设备型号(例如：iPhone)
	Model string `protobuf:"bytes,10,opt,name=model,proto3" json:"model,omitempty"`
	// 设备操作系统(例如：iOS)
	Os string `protobuf:"bytes,11,opt,name=os,proto3" json:"os,omitempty"`
	// 操作系统版本(例如：4.2.1)
	Osv string `protobuf:"bytes,12,opt,name=osv,proto3" json:"osv,omitempty"`
	// 设备屏幕像素高
	H int32 `protobuf:"varint,13,opt,name=h,proto3" json:"h,omitempty"`
	// 设备屏幕像素宽
	W int32 `protobuf:"varint,14,opt,name=w,proto3" json:"w,omitempty"`
	// 设备像素密度
	Ppi   int32                  `protobuf:"varint,15,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo   *BidRequest_Device_Geo `protobuf:"bytes,16,opt,name=geo,proto3" json:"geo,omitempty"`
	Brand string                 `protobuf:"bytes,17,opt,name=brand,proto3" json:"brand,omitempty"`
	Did   string                 `protobuf:"bytes,18,opt,name=did,proto3" json:"did,omitempty"`
	Oaid  string                 `protobuf:"bytes,19,opt,name=oaid,proto3" json:"oaid,omitempty"`
	//系统更新标识，原值传输，取值参见附录一， iOS：1581141691.570419583 Android： 1004697.70999999
	UpdateMark string `protobuf:"bytes,20,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	//系统启动标识，原值传输，取值参见附录一， iOS：1623815045.970028， Android： ec7f4f33-411a-47bc-8067-744a4e7e072
	BootMark string `protobuf:"bytes,21,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	// 设备类型：0.未知;1.pc;2.phone;3.pad;4.tv
	Devicetype int32 `protobuf:"varint,22,opt,name=devicetype,proto3" json:"devicetype,omitempty"`
	//ipv6地址
	Ipv6 string `protobuf:"bytes,23,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	//广协CAID方案线上算法生成的CAID
	Caid string `protobuf:"bytes,24,opt,name=caid,proto3" json:"caid,omitempty"`
	//广协CAID方案线上算法的版本
	CaidVersion string `protobuf:"bytes,25,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	//设备oaid 的md5值
	Oaidmd5 string `protobuf:"bytes,26,opt,name=oaidmd5,proto3" json:"oaidmd5,omitempty"`
	//设备名称的MD5值，取小写16进制的结果，长度为32个字节 示例："e910dddb2748c36b47fcde5dd720eec1"
	DeviceNameMd5 string `protobuf:"bytes,27,opt,name=device_name_md5,json=deviceNameMd5,proto3" json:"device_name_md5,omitempty"`
	//设备model值 示例："D22AP" 仅限iOS设备
	HardwareModel string `protobuf:"bytes,28,opt,name=hardware_model,json=hardwareModel,proto3" json:"hardware_model,omitempty"`
	//wifi名称
	Wifiname string `protobuf:"bytes,29,opt,name=wifiname,proto3" json:"wifiname,omitempty"`
	//磁盘剩余空间 单位 字节
	SdFreeSpace string `protobuf:"bytes,30,opt,name=sd_free_space,json=sdFreeSpace,proto3" json:"sd_free_space,omitempty"`
	//国家 示例："CN"
	Country string `protobuf:"bytes,31,opt,name=country,proto3" json:"country,omitempty"`
	//语言 示例："zh-Hans-CN"
	Language string `protobuf:"bytes,32,opt,name=language,proto3" json:"language,omitempty"`
	//时区 示例："28800"
	TimeZone string `protobuf:"bytes,33,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	//ifamd5值
	Ifamd5 string `protobuf:"bytes,34,opt,name=ifamd5,proto3" json:"ifamd5,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetDidmd5() string {
	if x != nil {
		return x.Didmd5
	}
	return ""
}

func (x *BidRequest_Device) GetIfa() string {
	if x != nil {
		return x.Ifa
	}
	return ""
}

func (x *BidRequest_Device) GetDpidmd5() string {
	if x != nil {
		return x.Dpidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetMacmd5() string {
	if x != nil {
		return x.Macmd5
	}
	return ""
}

func (x *BidRequest_Device) GetConnectiontype() int32 {
	if x != nil {
		return x.Connectiontype
	}
	return 0
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetDid() string {
	if x != nil {
		return x.Did
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetDevicetype() int32 {
	if x != nil {
		return x.Devicetype
	}
	return 0
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *BidRequest_Device) GetOaidmd5() string {
	if x != nil {
		return x.Oaidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceNameMd5() string {
	if x != nil {
		return x.DeviceNameMd5
	}
	return ""
}

func (x *BidRequest_Device) GetHardwareModel() string {
	if x != nil {
		return x.HardwareModel
	}
	return ""
}

func (x *BidRequest_Device) GetWifiname() string {
	if x != nil {
		return x.Wifiname
	}
	return ""
}

func (x *BidRequest_Device) GetSdFreeSpace() string {
	if x != nil {
		return x.SdFreeSpace
	}
	return ""
}

func (x *BidRequest_Device) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BidRequest_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *BidRequest_Device) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *BidRequest_Device) GetIfamd5() string {
	if x != nil {
		return x.Ifamd5
	}
	return ""
}

type BidRequest_InstallApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	//
	Bundle string `protobuf:"bytes,2,opt,name=bundle,proto3" json:"bundle,omitempty"`
	// source 用于区分来源
	Source string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *BidRequest_InstallApp) Reset() {
	*x = BidRequest_InstallApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_InstallApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_InstallApp) ProtoMessage() {}

func (x *BidRequest_InstallApp) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_InstallApp.ProtoReflect.Descriptor instead.
func (*BidRequest_InstallApp) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_InstallApp) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *BidRequest_InstallApp) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_InstallApp) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

// 用户对象
type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用用户ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//⽤户的性别。0: Unknown, 1: Male, 2: Female
	Gender int32 `protobuf:"varint,2,opt,name=gender,proto3" json:"gender,omitempty"`
	// 用户的年龄，分段枚举
	Age UserAgeType `protobuf:"varint,3,opt,name=age,proto3,enum=ifeng.UserAgeType" json:"age,omitempty"`
	// 安装得app
	InstallApps []*BidRequest_InstallApp `protobuf:"bytes,4,rep,name=installApps,proto3" json:"installApps,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *BidRequest_User) GetAge() UserAgeType {
	if x != nil {
		return x.Age
	}
	return UserAgeType_UNKNOWN_AGE_TYPE
}

func (x *BidRequest_User) GetInstallApps() []*BidRequest_InstallApp {
	if x != nil {
		return x.InstallApps
	}
	return nil
}

type BidRequest_SiteAdSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  int32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height int32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *BidRequest_SiteAdSize) Reset() {
	*x = BidRequest_SiteAdSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_SiteAdSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_SiteAdSize) ProtoMessage() {}

func (x *BidRequest_SiteAdSize) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_SiteAdSize.ProtoReflect.Descriptor instead.
func (*BidRequest_SiteAdSize) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_SiteAdSize) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_SiteAdSize) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BidRequest_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//id 网站识别符。
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//name 网址名称。
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	//网址域名
	Domain string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	//当前页面 url
	Page string `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty"`
	//当前页面的 Referrer url
	Ref string `protobuf:"bytes,5,opt,name=ref,proto3" json:"ref,omitempty"`
	// 允许的推广位的尺寸:
	Siteadsize []*BidRequest_SiteAdSize `protobuf:"bytes,6,rep,name=siteadsize,proto3" json:"siteadsize,omitempty"`
	//关键词
	Keywords []string `protobuf:"bytes,7,rep,name=keywords,proto3" json:"keywords,omitempty"`
	//行业
	Cat []string `protobuf:"bytes,8,rep,name=cat,proto3" json:"cat,omitempty"`
	//扩展字段
	Ext string `protobuf:"bytes,9,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_Site) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Site) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_Site) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BidRequest_Site) GetPage() string {
	if x != nil {
		return x.Page
	}
	return ""
}

func (x *BidRequest_Site) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *BidRequest_Site) GetSiteadsize() []*BidRequest_SiteAdSize {
	if x != nil {
		return x.Siteadsize
	}
	return nil
}

func (x *BidRequest_Site) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *BidRequest_Site) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Site) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

//原生广告请求对象
type BidRequest_Imp_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image []*BidRequest_Imp_Native_Image `protobuf:"bytes,1,rep,name=image,proto3" json:"image,omitempty"`
	//广告形式
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	//广告位宽度（pixels）
	W int32 `protobuf:"varint,3,opt,name=w,proto3" json:"w,omitempty"`
	//广告位高度（pixels）
	H    int32                       `protobuf:"varint,4,opt,name=h,proto3" json:"h,omitempty"`
	Icon *BidRequest_Imp_Native_Icon `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	//标题文字长度
	Titlelen int32 `protobuf:"varint,6,opt,name=titlelen,proto3" json:"titlelen,omitempty"`
	//描述文字长度
	Textlen int32 `protobuf:"varint,7,opt,name=textlen,proto3" json:"textlen,omitempty"`
	//1、下载；2、唤醒（deeplink）
	Support []int32 `protobuf:"varint,8,rep,packed,name=support,proto3" json:"support,omitempty"`
}

func (x *BidRequest_Imp_Native) Reset() {
	*x = BidRequest_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native) ProtoMessage() {}

func (x *BidRequest_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Native) GetImage() []*BidRequest_Imp_Native_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetIcon() *BidRequest_Imp_Native_Icon {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetTitlelen() int32 {
	if x != nil {
		return x.Titlelen
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetTextlen() int32 {
	if x != nil {
		return x.Textlen
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetSupport() []int32 {
	if x != nil {
		return x.Support
	}
	return nil
}

//视频贴片请求对象
type BidRequest_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//支持的文件类型，固定为：video/mp4
	Mimes []string `protobuf:"bytes,1,rep,name=mimes,proto3" json:"mimes,omitempty"`
	//视频素材宽度（pixels）
	W int32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`
	//视频素材高度（pixels）
	H int32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`
	//最大播放时长，单位秒
	Maxduration int32 `protobuf:"varint,4,opt,name=maxduration,proto3" json:"maxduration,omitempty"`
	//最小播放时长，单位秒
	Minduration int32 `protobuf:"varint,5,opt,name=minduration,proto3" json:"minduration,omitempty"`
	//时长的误差值，单位为毫秒。
	Diffduration int32 `protobuf:"varint,6,opt,name=diffduration,proto3" json:"diffduration,omitempty"`
}

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Imp_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxduration() int32 {
	if x != nil {
		return x.Maxduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMinduration() int32 {
	if x != nil {
		return x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetDiffduration() int32 {
	if x != nil {
		return x.Diffduration
	}
	return 0
}

//该广告位请求对应私有交易对象
type BidRequest_Imp_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deals   *BidRequest_Imp_Pmp_Deal `protobuf:"bytes,1,opt,name=deals,proto3" json:"deals,omitempty"`
	Withrtb int32                    `protobuf:"varint,2,opt,name=withrtb,proto3" json:"withrtb,omitempty"`
}

func (x *BidRequest_Imp_Pmp) Reset() {
	*x = BidRequest_Imp_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *BidRequest_Imp_Pmp) GetDeals() *BidRequest_Imp_Pmp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *BidRequest_Imp_Pmp) GetWithrtb() int32 {
	if x != nil {
		return x.Withrtb
	}
	return 0
}

//图片数组对象
type BidRequest_Imp_Native_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//素材宽度
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//素材高度
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
}

func (x *BidRequest_Imp_Native_Image) Reset() {
	*x = BidRequest_Imp_Native_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Image) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Image) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Image.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Image) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *BidRequest_Imp_Native_Image) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Image) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

//图标/Logo对象
type BidRequest_Imp_Native_Icon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//图标宽度
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//图标高度
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
}

func (x *BidRequest_Imp_Native_Icon) Reset() {
	*x = BidRequest_Imp_Native_Icon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Icon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Icon) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Icon) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Icon.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Icon) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 0, 1}
}

func (x *BidRequest_Imp_Native_Icon) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Icon) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

type BidRequest_Imp_Pmp_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//交易id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//约定交易价格
	Price int64 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *BidRequest_Imp_Pmp_Deal) Reset() {
	*x = BidRequest_Imp_Pmp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp_Deal) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp_Deal) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 0, 2, 0}
}

func (x *BidRequest_Imp_Pmp_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Pmp_Deal) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type BidRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 经度，取值范围[-180.0 , +180.0]
	Lon float32 `protobuf:"fixed32,1,opt,name=lon,proto3" json:"lon,omitempty"`
	// 纬度，取值范围[-90.0 , +90.0]
	Lat float32 `protobuf:"fixed32,2,opt,name=lat,proto3" json:"lat,omitempty"`
	// GPS坐标系类型。 1：GCJ-02;  2:WGS-84; 3：bd09ll
	LbsType int32 `protobuf:"varint,3,opt,name=lbsType,proto3" json:"lbsType,omitempty"`
}

func (x *BidRequest_Device_Geo) Reset() {
	*x = BidRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Geo) ProtoMessage() {}

func (x *BidRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *BidRequest_Device_Geo) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetLbsType() int32 {
	if x != nil {
		return x.LbsType
	}
	return 0
}

//竞价席位对象数组。
type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid,proto3" json:"bid,omitempty"`
	//代表出价的竞价方席位识别符，由凤飞系统生成并提供
	Seat string `protobuf:"bytes,2,opt,name=seat,proto3" json:"seat,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() string {
	if x != nil {
		return x.Seat
	}
	return ""
}

//出价对象数组。每个出价对象关联竞价请求中的一个广告曝光请求对象
type BidResponse_SeatBid_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Impid string `protobuf:"bytes,2,opt,name=impid,proto3" json:"impid,omitempty"`
	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Dealid string `protobuf:"bytes,3,opt,name=dealid,proto3" json:"dealid,omitempty"`
	//广告出价。CPM记，单位为人民币分。非私有交易必须返回
	Price int64 `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	//广告类型。1、原生；2视频贴片
	Adtype   int32                             `protobuf:"varint,5,opt,name=adtype,proto3" json:"adtype,omitempty"`
	Creative *BidResponse_SeatBid_Bid_Creative `protobuf:"bytes,6,opt,name=creative,proto3" json:"creative,omitempty"`
	//视频贴片vast代码
	Vast string `protobuf:"bytes,7,opt,name=vast,proto3" json:"vast,omitempty"`
	//曝光监测地址数组（异步发送，支持宏替换）
	Aimps []string `protobuf:"bytes,8,rep,name=aimps,proto3" json:"aimps,omitempty"`
	//点击监测地址数组（异步发送，支持宏替换）
	Aclks []string `protobuf:"bytes,9,rep,name=aclks,proto3" json:"aclks,omitempty"`
	//胜出通知。（由Server端发送，支持宏替换）
	Nurl string `protobuf:"bytes,10,opt,name=nurl,proto3" json:"nurl,omitempty"`
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetDealid() string {
	if x != nil {
		return x.Dealid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetAdtype() int32 {
	if x != nil {
		return x.Adtype
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetCreative() *BidResponse_SeatBid_Bid_Creative {
	if x != nil {
		return x.Creative
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetVast() string {
	if x != nil {
		return x.Vast
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetAimps() []string {
	if x != nil {
		return x.Aimps
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAclks() []string {
	if x != nil {
		return x.Aclks
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

//广告创意
type BidResponse_SeatBid_Bid_Creative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//广告创意ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//图片素材地址数组
	Images []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
	//创意落地页地址
	Landpage string `protobuf:"bytes,3,opt,name=landpage,proto3" json:"landpage,omitempty"`
	// 下载型创意直接下载地址,为防止出现问题，先标记弃用，将来可能会删除
	// 新的功能会移到app对象中
	//
	// Deprecated: Marked as deprecated in ifeng-ad-rtb.proto.
	Dlurl string `protobuf:"bytes,4,opt,name=dlurl,proto3" json:"dlurl,omitempty"`
	//Deep link地址
	Dplurl string `protobuf:"bytes,5,opt,name=dplurl,proto3" json:"dplurl,omitempty"`
	//图标/Logo地址或Base64编码（用http开头区分）
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	//创意标题，超出Request要求会被截断
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	//创意标题，超出Request要求会被截断
	Text string `protobuf:"bytes,8,opt,name=text,proto3" json:"text,omitempty"`
	//创意补充，简介
	Desc string `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`
	//原生广告返回素材对应广告形式
	Type string                                `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	App  *BidResponse_SeatBid_Bid_Creative_App `protobuf:"bytes,11,opt,name=app,proto3" json:"app,omitempty"`
	// 下载类型0.普通下载(默认)；1.广点通下载
	Daction int32 `protobuf:"varint,12,opt,name=daction,proto3" json:"daction,omitempty"`
	//Deep link唤起成功监测数组
	Dpsurls []string `protobuf:"bytes,13,rep,name=dpsurls,proto3" json:"dpsurls,omitempty"`
	//复制码
	Copycode string `protobuf:"bytes,14,opt,name=copycode,proto3" json:"copycode,omitempty"`
	//广告点击交互类型；1.app; 2.WeChat Mini Program 微信小程序（需接入微信SDK);
	InteractionType int32 `protobuf:"varint,15,opt,name=interactionType,proto3" json:"interactionType,omitempty"`
	//小程序广告 interactionType=2 的时候才会返回该字段
	WeChatMiniProgram *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram `protobuf:"bytes,16,opt,name=weChatMiniProgram,proto3" json:"weChatMiniProgram,omitempty"`
	//创业所属行业
	Category []string `protobuf:"bytes,17,rep,name=category,proto3" json:"category,omitempty"`
	//视频广告信息
	Video *BidResponse_SeatBid_Bid_Creative_Video `protobuf:"bytes,18,opt,name=video,proto3" json:"video,omitempty"`
	// 素材图片高
	H int32 `protobuf:"varint,19,opt,name=h,proto3" json:"h,omitempty"`
	// 素材图片宽
	W int32 `protobuf:"varint,20,opt,name=w,proto3" json:"w,omitempty"`
	//竞价模式 0-cpm 1-cpc
	BidMode int32 `protobuf:"varint,21,opt,name=bidMode,proto3" json:"bidMode,omitempty"`
	//iOS唤起通用链接
	UniversalLink string `protobuf:"bytes,22,opt,name=universal_link,json=universalLink,proto3" json:"universal_link,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Creative) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetLandpage() string {
	if x != nil {
		return x.Landpage
	}
	return ""
}

// Deprecated: Marked as deprecated in ifeng-ad-rtb.proto.
func (x *BidResponse_SeatBid_Bid_Creative) GetDlurl() string {
	if x != nil {
		return x.Dlurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDplurl() string {
	if x != nil {
		return x.Dplurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetApp() *BidResponse_SeatBid_Bid_Creative_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDaction() int32 {
	if x != nil {
		return x.Daction
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDpsurls() []string {
	if x != nil {
		return x.Dpsurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetCopycode() string {
	if x != nil {
		return x.Copycode
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetInteractionType() int32 {
	if x != nil {
		return x.InteractionType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetWeChatMiniProgram() *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram {
	if x != nil {
		return x.WeChatMiniProgram
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetVideo() *BidResponse_SeatBid_Bid_Creative_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetBidMode() int32 {
	if x != nil {
		return x.BidMode
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetUniversalLink() string {
	if x != nil {
		return x.UniversalLink
	}
	return ""
}

type BidResponse_SeatBid_Bid_Creative_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//app中文名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	//直接下载地址，目的是想要替换掉Creative里的dlurl
	Dlurl string `protobuf:"bytes,2,opt,name=dlurl,proto3" json:"dlurl,omitempty"`
	//android用，包名
	Package string `protobuf:"bytes,3,opt,name=package,proto3" json:"package,omitempty"`
	//ios用，APP_ID
	Appid string `protobuf:"bytes,4,opt,name=appid,proto3" json:"appid,omitempty"`
	//开始下载监测
	Dsurls []string `protobuf:"bytes,7,rep,name=dsurls,proto3" json:"dsurls,omitempty"`
	// 下载完成监测
	Dcurls []string `protobuf:"bytes,8,rep,name=dcurls,proto3" json:"dcurls,omitempty"`
	//开始安装监测
	Installstarturls []string `protobuf:"bytes,9,rep,name=installstarturls,proto3" json:"installstarturls,omitempty"`
	//安装完成监测
	Installedurls []string `protobuf:"bytes,10,rep,name=installedurls,proto3" json:"installedurls,omitempty"`
	//开发者名称
	DeveloperName string `protobuf:"bytes,11,opt,name=developerName,proto3" json:"developerName,omitempty"`
	//app版本号
	AppVersion string `protobuf:"bytes,12,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
	//隐私协议url
	AppPrivacyPolicyUrl string `protobuf:"bytes,13,opt,name=appPrivacyPolicyUrl,proto3" json:"appPrivacyPolicyUrl,omitempty"`
	//应用权限页面url
	AppPermissionUrl string `protobuf:"bytes,14,opt,name=appPermissionUrl,proto3" json:"appPermissionUrl,omitempty"`
	//应用权限说明列表
	AppPermissions []*BidResponse_SeatBid_Bid_Creative_App_AppPermission `protobuf:"bytes,15,rep,name=appPermissions,proto3" json:"appPermissions,omitempty"`
	//应用 icon 地址
	AppIconUrl string `protobuf:"bytes,16,opt,name=appIconUrl,proto3" json:"appIconUrl,omitempty"`
	//安卓应用 appinfo url
	AppInfoUrl string `protobuf:"bytes,17,opt,name=appInfoUrl,proto3" json:"appInfoUrl,omitempty"`
	//应用大小
	AppApkSize int64 `protobuf:"varint,18,opt,name=appApkSize,proto3" json:"appApkSize,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative_App) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative_App) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative_App) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative_App.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative_App) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDlurl() string {
	if x != nil {
		return x.Dlurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDsurls() []string {
	if x != nil {
		return x.Dsurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDcurls() []string {
	if x != nil {
		return x.Dcurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetInstallstarturls() []string {
	if x != nil {
		return x.Installstarturls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetInstalledurls() []string {
	if x != nil {
		return x.Installedurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDeveloperName() string {
	if x != nil {
		return x.DeveloperName
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppPrivacyPolicyUrl() string {
	if x != nil {
		return x.AppPrivacyPolicyUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppPermissionUrl() string {
	if x != nil {
		return x.AppPermissionUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppPermissions() []*BidResponse_SeatBid_Bid_Creative_App_AppPermission {
	if x != nil {
		return x.AppPermissions
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppIconUrl() string {
	if x != nil {
		return x.AppIconUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppInfoUrl() string {
	if x != nil {
		return x.AppInfoUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppApkSize() int64 {
	if x != nil {
		return x.AppApkSize
	}
	return 0
}

type BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//小程序广告  应用appid 需要客户端接入微信SDK
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	//小程序广告  小程序原始id
	OriginId string `protobuf:"bytes,2,opt,name=originId,proto3" json:"originId,omitempty"`
	//小程序广告 小程序路径
	Path string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0, 1}
}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) GetOriginId() string {
	if x != nil {
		return x.OriginId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type BidResponse_SeatBid_Bid_Creative_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//视频地址
	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	//开始播放监测地址
	VideoBegin []string `protobuf:"bytes,2,rep,name=video_begin,json=videoBegin,proto3" json:"video_begin,omitempty"`
	//播放结束监测地址
	VideoEnd []string `protobuf:"bytes,3,rep,name=video_end,json=videoEnd,proto3" json:"video_end,omitempty"`
	//视频时长
	VideoTime string `protobuf:"bytes,4,opt,name=video_time,json=videoTime,proto3" json:"video_time,omitempty"`
	//视频大小
	VideoSize string `protobuf:"bytes,5,opt,name=video_size,json=videoSize,proto3" json:"video_size,omitempty"`
	//视频封面
	CoverUrl string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	//视频播放至1/4(25%)，上报监测
	FirstQuartileUrls []string `protobuf:"bytes,7,rep,name=first_quartile_urls,json=firstQuartileUrls,proto3" json:"first_quartile_urls,omitempty"`
	//视频播放至1/2(50%)，上报监测
	MidPointUrls []string `protobuf:"bytes,8,rep,name=mid_point_urls,json=midPointUrls,proto3" json:"mid_point_urls,omitempty"`
	//视频播放至3/4(75%)，上报监测
	ThirdQuartileUrls []string `protobuf:"bytes,9,rep,name=third_quartile_urls,json=thirdQuartileUrls,proto3" json:"third_quartile_urls,omitempty"`
	Width             int32    `protobuf:"varint,10,opt,name=width,proto3" json:"width,omitempty"`
	Height            int32    `protobuf:"varint,11,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative_Video) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative_Video) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative_Video) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0, 2}
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetVideoBegin() []string {
	if x != nil {
		return x.VideoBegin
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetVideoEnd() []string {
	if x != nil {
		return x.VideoEnd
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetVideoTime() string {
	if x != nil {
		return x.VideoTime
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetVideoSize() string {
	if x != nil {
		return x.VideoSize
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetFirstQuartileUrls() []string {
	if x != nil {
		return x.FirstQuartileUrls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetMidPointUrls() []string {
	if x != nil {
		return x.MidPointUrls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetThirdQuartileUrls() []string {
	if x != nil {
		return x.ThirdQuartileUrls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative_Video) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BidResponse_SeatBid_Bid_Creative_App_AppPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionLabel string `protobuf:"bytes,1,opt,name=permissionLabel,proto3" json:"permissionLabel,omitempty"`
	PermissionDesc  string `protobuf:"bytes,2,opt,name=permissionDesc,proto3" json:"permissionDesc,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative_App_AppPermission) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative_App_AppPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_ad_rtb_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative_App_AppPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative_App_AppPermission) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative_App_AppPermission) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_ad_rtb_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative_App_AppPermission.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative_App_AppPermission) Descriptor() ([]byte, []int) {
	return file_ifeng_ad_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Creative_App_AppPermission) GetPermissionLabel() string {
	if x != nil {
		return x.PermissionLabel
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App_AppPermission) GetPermissionDesc() string {
	if x != nil {
		return x.PermissionDesc
	}
	return ""
}

var File_ifeng_ad_rtb_proto protoreflect.FileDescriptor

var file_ifeng_ad_rtb_proto_rawDesc = []byte{
	0x0a, 0x12, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2d, 0x61, 0x64, 0x2d, 0x72, 0x74, 0x62, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x22, 0xfa, 0x16, 0x0a, 0x0a,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12,
	0x27, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x69,
	0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69,
	0x6e, 0x64, 0x75, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64,
	0x65, 0x6e, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x6f, 0x72, 0x62, 0x69, 0x64, 0x65, 0x6e, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x6f, 0x6e, 0x6c, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x6f, 0x6e, 0x6c, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x73, 0x69,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74, 0x65,
	0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x1a, 0x98, 0x07, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x31, 0x0a, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x66,
	0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x03, 0x70, 0x6d, 0x70,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d,
	0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50,
	0x6f, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50,
	0x6f, 0x73, 0x1a, 0xc2, 0x02, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x38, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69,
	0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6c, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6c, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x65,
	0x78, 0x74, 0x6c, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x65, 0x78,
	0x74, 0x6c, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x23,
	0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x68, 0x1a, 0x22, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x77,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x1a, 0xa1, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x69, 0x66, 0x66, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64,
	0x69, 0x66, 0x66, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x83, 0x01, 0x0a, 0x03,
	0x50, 0x6d, 0x70, 0x12, 0x34, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x44, 0x65,
	0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x69, 0x74,
	0x68, 0x72, 0x74, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68,
	0x72, 0x74, 0x62, 0x1a, 0x2c, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x1a, 0x67, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xad, 0x07, 0x0a, 0x06, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x66, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x66, 0x61, 0x12,
	0x18, 0x0a, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x63,
	0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x6d, 0x64,
	0x35, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x6f, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x6f, 0x73, 0x76, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12,
	0x0c, 0x0a, 0x01, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a,
	0x01, 0x77, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x70, 0x69, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x2e, 0x0a,
	0x03, 0x67, 0x65, 0x6f, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x66, 0x65,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x14, 0x0a,
	0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f,
	0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x61, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x26, 0x0a, 0x0f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x64, 0x35, 0x12, 0x25, 0x0a, 0x0e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x69,
	0x66, 0x69, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x69,
	0x66, 0x69, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x64, 0x5f, 0x66, 0x72, 0x65,
	0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x64, 0x46, 0x72, 0x65, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x66, 0x61, 0x6d, 0x64, 0x35, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69,
	0x66, 0x61, 0x6d, 0x64, 0x35, 0x1a, 0x43, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x61, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6c, 0x62, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6c, 0x62, 0x73, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x50, 0x0a, 0x0a, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x1a, 0x94, 0x01, 0x0a,
	0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x24, 0x0a,
	0x03, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x69, 0x66, 0x65,
	0x6e, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x03,
	0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70,
	0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41,
	0x70, 0x70, 0x73, 0x1a, 0x3a, 0x0a, 0x0a, 0x53, 0x69, 0x74, 0x65, 0x41, 0x64, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a,
	0xe6, 0x01, 0x0a, 0x04, 0x53, 0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x69,
	0x74, 0x65, 0x61, 0x64, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x41, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x73, 0x69,
	0x74, 0x65, 0x61, 0x64, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x8e, 0x12, 0x0a, 0x0b, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74,
	0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x66, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x69, 0x64, 0x69, 0x64, 0x1a, 0xa2, 0x11, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x12, 0x30, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x1a, 0xd0, 0x10, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x6d, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69,
	0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x76, 0x61, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x76,
	0x61, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x69, 0x6d, 0x70, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x69, 0x6d, 0x70, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x63, 0x6c,
	0x6b, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63, 0x6c, 0x6b, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x75, 0x72, 0x6c, 0x1a, 0xc3, 0x0e, 0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x64,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x64,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x05, 0x64, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x64, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x70, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x70, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x03,
	0x61, 0x70, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x66, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x70, 0x73, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x70, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x11, 0x77, 0x65, 0x43, 0x68, 0x61, 0x74, 0x4d,
	0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x57, 0x65, 0x43, 0x68, 0x61, 0x74,
	0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x11, 0x77, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x66, 0x65, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12,
	0x0c, 0x0a, 0x01, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a,
	0x01, 0x77, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x62,
	0x69, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75,
	0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x1a, 0xab, 0x05, 0x0a,
	0x03, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x6c, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x63, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x63, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x2a,
	0x0a, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x75, 0x72, 0x6c, 0x73,
	0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x61, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x69,
	0x66, 0x65, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x49, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x41, 0x70,
	0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x41, 0x70, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x61, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x1a, 0x59, 0x0a, 0x11, 0x57, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x1a, 0xf1, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12,
	0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x71,
	0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x51, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c,
	0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x69, 0x64, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x69, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x74, 0x68, 0x69, 0x72, 0x64, 0x51,
	0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x2a, 0x9a, 0x01, 0x0a, 0x0b, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12,
	0x11, 0x0a, 0x0d, 0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x45, 0x4e, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x57, 0x45, 0x4e, 0x54, 0x59, 0x5f, 0x41,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x48, 0x49,
	0x52, 0x54, 0x59, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x12,
	0x0a, 0x0e, 0x46, 0x4f, 0x52, 0x54, 0x59, 0x5f, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x46, 0x54, 0x59, 0x5f, 0x41, 0x47, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x06, 0x42, 0x3b, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x69, 0x66,
	0x65, 0x6e, 0x67, 0x2e, 0x61, 0x64, 0x2e, 0x6d, 0x75, 0x74, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x62, 0x65, 0x61, 0x6e, 0x2e, 0x72, 0x74, 0x62, 0x42, 0x0a, 0x49, 0x66, 0x65,
	0x6e, 0x67, 0x41, 0x64, 0x52, 0x74, 0x62, 0x5a, 0x0a, 0x2e, 0x2e, 0x2f, 0x66, 0x65, 0x6e, 0x67,
	0x66, 0x65, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ifeng_ad_rtb_proto_rawDescOnce sync.Once
	file_ifeng_ad_rtb_proto_rawDescData = file_ifeng_ad_rtb_proto_rawDesc
)

func file_ifeng_ad_rtb_proto_rawDescGZIP() []byte {
	file_ifeng_ad_rtb_proto_rawDescOnce.Do(func() {
		file_ifeng_ad_rtb_proto_rawDescData = protoimpl.X.CompressGZIP(file_ifeng_ad_rtb_proto_rawDescData)
	})
	return file_ifeng_ad_rtb_proto_rawDescData
}

var file_ifeng_ad_rtb_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_ifeng_ad_rtb_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_ifeng_ad_rtb_proto_goTypes = []interface{}{
	(UserAgeType)(0),                                           // 0: ifeng.UserAgeType
	(*BidRequest)(nil),                                         // 1: ifeng.BidRequest
	(*BidResponse)(nil),                                        // 2: ifeng.BidResponse
	(*BidRequest_Imp)(nil),                                     // 3: ifeng.BidRequest.Imp
	(*BidRequest_App)(nil),                                     // 4: ifeng.BidRequest.App
	(*BidRequest_Device)(nil),                                  // 5: ifeng.BidRequest.Device
	(*BidRequest_InstallApp)(nil),                              // 6: ifeng.BidRequest.InstallApp
	(*BidRequest_User)(nil),                                    // 7: ifeng.BidRequest.User
	(*BidRequest_SiteAdSize)(nil),                              // 8: ifeng.BidRequest.SiteAdSize
	(*BidRequest_Site)(nil),                                    // 9: ifeng.BidRequest.Site
	(*BidRequest_Imp_Native)(nil),                              // 10: ifeng.BidRequest.Imp.Native
	(*BidRequest_Imp_Video)(nil),                               // 11: ifeng.BidRequest.Imp.Video
	(*BidRequest_Imp_Pmp)(nil),                                 // 12: ifeng.BidRequest.Imp.Pmp
	(*BidRequest_Imp_Native_Image)(nil),                        // 13: ifeng.BidRequest.Imp.Native.Image
	(*BidRequest_Imp_Native_Icon)(nil),                         // 14: ifeng.BidRequest.Imp.Native.Icon
	(*BidRequest_Imp_Pmp_Deal)(nil),                            // 15: ifeng.BidRequest.Imp.Pmp.Deal
	(*BidRequest_Device_Geo)(nil),                              // 16: ifeng.BidRequest.Device.Geo
	(*BidResponse_SeatBid)(nil),                                // 17: ifeng.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),                            // 18: ifeng.BidResponse.SeatBid.Bid
	(*BidResponse_SeatBid_Bid_Creative)(nil),                   // 19: ifeng.BidResponse.SeatBid.Bid.Creative
	(*BidResponse_SeatBid_Bid_Creative_App)(nil),               // 20: ifeng.BidResponse.SeatBid.Bid.Creative.App
	(*BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram)(nil), // 21: ifeng.BidResponse.SeatBid.Bid.Creative.WeChatMiniProgram
	(*BidResponse_SeatBid_Bid_Creative_Video)(nil),             // 22: ifeng.BidResponse.SeatBid.Bid.Creative.Video
	(*BidResponse_SeatBid_Bid_Creative_App_AppPermission)(nil), // 23: ifeng.BidResponse.SeatBid.Bid.Creative.App.AppPermission
}
var file_ifeng_ad_rtb_proto_depIdxs = []int32{
	3,  // 0: ifeng.BidRequest.imp:type_name -> ifeng.BidRequest.Imp
	4,  // 1: ifeng.BidRequest.app:type_name -> ifeng.BidRequest.App
	5,  // 2: ifeng.BidRequest.device:type_name -> ifeng.BidRequest.Device
	7,  // 3: ifeng.BidRequest.user:type_name -> ifeng.BidRequest.User
	9,  // 4: ifeng.BidRequest.site:type_name -> ifeng.BidRequest.Site
	17, // 5: ifeng.BidResponse.seatbid:type_name -> ifeng.BidResponse.SeatBid
	10, // 6: ifeng.BidRequest.Imp.native:type_name -> ifeng.BidRequest.Imp.Native
	11, // 7: ifeng.BidRequest.Imp.video:type_name -> ifeng.BidRequest.Imp.Video
	12, // 8: ifeng.BidRequest.Imp.pmp:type_name -> ifeng.BidRequest.Imp.Pmp
	16, // 9: ifeng.BidRequest.Device.geo:type_name -> ifeng.BidRequest.Device.Geo
	0,  // 10: ifeng.BidRequest.User.age:type_name -> ifeng.UserAgeType
	6,  // 11: ifeng.BidRequest.User.installApps:type_name -> ifeng.BidRequest.InstallApp
	8,  // 12: ifeng.BidRequest.Site.siteadsize:type_name -> ifeng.BidRequest.SiteAdSize
	13, // 13: ifeng.BidRequest.Imp.Native.image:type_name -> ifeng.BidRequest.Imp.Native.Image
	14, // 14: ifeng.BidRequest.Imp.Native.icon:type_name -> ifeng.BidRequest.Imp.Native.Icon
	15, // 15: ifeng.BidRequest.Imp.Pmp.deals:type_name -> ifeng.BidRequest.Imp.Pmp.Deal
	18, // 16: ifeng.BidResponse.SeatBid.bid:type_name -> ifeng.BidResponse.SeatBid.Bid
	19, // 17: ifeng.BidResponse.SeatBid.Bid.creative:type_name -> ifeng.BidResponse.SeatBid.Bid.Creative
	20, // 18: ifeng.BidResponse.SeatBid.Bid.Creative.app:type_name -> ifeng.BidResponse.SeatBid.Bid.Creative.App
	21, // 19: ifeng.BidResponse.SeatBid.Bid.Creative.weChatMiniProgram:type_name -> ifeng.BidResponse.SeatBid.Bid.Creative.WeChatMiniProgram
	22, // 20: ifeng.BidResponse.SeatBid.Bid.Creative.video:type_name -> ifeng.BidResponse.SeatBid.Bid.Creative.Video
	23, // 21: ifeng.BidResponse.SeatBid.Bid.Creative.App.appPermissions:type_name -> ifeng.BidResponse.SeatBid.Bid.Creative.App.AppPermission
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_ifeng_ad_rtb_proto_init() }
func file_ifeng_ad_rtb_proto_init() {
	if File_ifeng_ad_rtb_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ifeng_ad_rtb_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_InstallApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_SiteAdSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Icon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative_WeChatMiniProgram); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_ad_rtb_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative_App_AppPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ifeng_ad_rtb_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ifeng_ad_rtb_proto_goTypes,
		DependencyIndexes: file_ifeng_ad_rtb_proto_depIdxs,
		EnumInfos:         file_ifeng_ad_rtb_proto_enumTypes,
		MessageInfos:      file_ifeng_ad_rtb_proto_msgTypes,
	}.Build()
	File_ifeng_ad_rtb_proto = out.File
	file_ifeng_ad_rtb_proto_rawDesc = nil
	file_ifeng_ad_rtb_proto_goTypes = nil
	file_ifeng_ad_rtb_proto_depIdxs = nil
}
