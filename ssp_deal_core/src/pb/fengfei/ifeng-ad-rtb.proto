syntax = "proto3";

package ifeng;

option java_package = "com.ifeng.ad.muta.common.bean.rtb";
option java_outer_classname = "IfengAdRtb";

option go_package = "mh_proxy/pb/fengfei";

message BidRequest {
    // 竞价请求唯一识别符
    string id = 1;

    // 竞价类型。1 = First Price, 2 = Second Price Plus. 当前固定为2。也就是采用第二名出价价格作为成交价。
    int32 at = 2;

    //平台类型。1、PC，2、WAP，3、APP
    int32 platform = 3;

    // 广告位对象广告曝光请求对象组。在一个竞价请求中可包含多个广告曝光请求。一次有效的竞价请求至少有一个广告曝光请求。
    message Imp {
        //竞价请求中广告曝光请求的唯一识别符
        string id = 1;
        //广告类型。1、原生；2视频贴片
        int32 adtype = 2;

        //原生广告请求对象
        message Native {
            //图片数组对象
            message Image {
                //素材宽度
                int32 w = 1;

                //素材高度
                int32 h = 2;
            }
            repeated Image image = 1;

            //广告形式
            string type = 2;

            //广告位宽度（pixels）
            int32 w = 3;

            //广告位高度（pixels）
            int32 h = 4;

            //图标/Logo对象
            message Icon {
                //图标宽度
                int32 w = 1;

                //图标高度
                int32 h = 2;
            }
            Icon icon = 5;

            //标题文字长度
            int32 titlelen = 6;

            //描述文字长度
            int32 textlen = 7;

            //1、下载；2、唤醒（deeplink）
            repeated int32 support = 8;
        }
        repeated Native native = 3;

        //视频贴片请求对象
        message Video {
            //支持的文件类型，固定为：video/mp4
            repeated string mimes = 1;

            //视频素材宽度（pixels）
            int32 w = 2;

            //视频素材高度（pixels）
            int32 h = 3;

            //最大播放时长，单位秒
            int32 maxduration = 4;

            //最小播放时长，单位秒
            int32 minduration = 5;

            //时长的误差值，单位为毫秒。
            int32 diffduration = 6;

        }
        Video video = 4;

        //特定广告位置或标签的识别符。当前版本设置为凤飞系统中的广告位id。
        string tagid = 5;

        //标明响应是否需要是https。0：http；1：https。
        int32 secure = 6;

        //竞拍底价。CPM记，单位为人民币分。
        int64 bidfloor = 7;

        //该广告位请求对应私有交易对象
        message Pmp {
            message Deal {
                //交易id
                string id = 1;

                //约定交易价格
                int64 price = 2;
            }
            Deal deals = 1;
            int32 withrtb = 2;
        }
        Pmp pmp = 8;

        //贴片位置 0-前贴片 1-中贴片 2-后贴片
        int32 patchPos = 9;

    }
    repeated Imp imp = 4;

    //App信息
    message App {
        //凤飞系统app唯一标识符
        string id = 1;

        //app名称
        string name = 2;

        //app本次请求对应包名
        string bundle = 3;

        //app版本号
        string ver = 4;

        //app 类型
        string type = 5;
    }
    App app = 5;
    // 设备信息
    message Device {
        // 浏览器user agent
        string ua = 1;

        // IPv4地址
        string ip = 2;

        // 设备IMEI号,MD5加密
        string didmd5 = 3;

        // iOS终端设备的identifier for advertising明文，保持字母大写。
        string ifa = 4;

        // Android终端设备的AndroidID，MD5加密
        string dpidmd5 = 5;

        // 终端网卡MAC地址，MD5加密
        string macmd5 = 6;

        // 网络连接类型。0.未知;1.WIFI;2.2G;3.3G;4.4G;5.5G
        int32 connectiontype = 7;

        // 运营商。0.未知;1.中国移动;2.中国联通;3.中国电信;
        int32 carrier = 8;

        // 设备制造商(例如：Apple)
        string make = 9;

        // 设备型号(例如：iPhone)
        string model = 10;

        // 设备操作系统(例如：iOS)
        string os = 11;

        // 操作系统版本(例如：4.2.1)
        string osv = 12;

        // 设备屏幕像素高
        int32 h = 13;

        // 设备屏幕像素宽
        int32 w = 14;

        // 设备像素密度
        int32 ppi = 15;

        message Geo {
            // 经度，取值范围[-180.0 , +180.0]
            float lon = 1;

            // 纬度，取值范围[-90.0 , +90.0]
            float lat = 2;

            // GPS坐标系类型。 1：GCJ-02;  2:WGS-84; 3：bd09ll
            int32 lbsType =3;
        }
        Geo geo = 16;

        string brand = 17;

        string did = 18;

        string oaid = 19;

        //系统更新标识，原值传输，取值参见附录一， iOS：1581141691.570419583 Android： 1004697.70999999
        string update_mark = 20;

        //系统启动标识，原值传输，取值参见附录一， iOS：1623815045.970028， Android： ec7f4f33-411a-47bc-8067-744a4e7e072
        string boot_mark = 21;

        // 设备类型：0.未知;1.pc;2.phone;3.pad;4.tv
        int32 devicetype = 22;

        //ipv6地址
        string ipv6 = 23;

        //广协CAID方案线上算法生成的CAID
        string caid = 24;

        //广协CAID方案线上算法的版本
        string caidVersion = 25;

        //设备oaid 的md5值
        string oaidmd5 = 26;

        //设备名称的MD5值，取小写16进制的结果，长度为32个字节 示例："e910dddb2748c36b47fcde5dd720eec1"
        string device_name_md5 = 27;

        //设备model值 示例："D22AP" 仅限iOS设备
        string hardware_model = 28;

        //wifi名称
        string wifiname = 29;

        //磁盘剩余空间 单位 字节
        string sd_free_space = 30;

        //国家 示例："CN"
        string country = 31;

        //语言 示例："zh-Hans-CN"
        string language = 32;

        //时区 示例："28800"
        string time_zone = 33;
        //ifamd5值
        string ifamd5 = 34;
    }
    Device device = 6;
    message InstallApp {
        //
        string code = 1;

        //
        string bundle =2;

        // source 用于区分来源
        string source =3;
    }
    // 用户对象
    message User {
        // 应用用户ID
        string id = 1;

        //⽤户的性别。0: Unknown, 1: Male, 2: Female
        int32 gender =2;

        // 用户的年龄，分段枚举
        UserAgeType age =3;
        // 安装得app
        repeated InstallApp installApps =4;
    }
    User user = 7;

    //是否测试模式，0 = 线上， 1 = 测试模式
    int32 test = 8;

    //禁投广告主行业类型
    repeated string bindus = 9;

    //只投广告主行业类型
    repeated string deliverTrade = 10;

    //禁投广告词
    repeated string forbidenwords = 11;

    //只投广告词
    repeated string onlywords = 12;

    message SiteAdSize {
        int32 width = 1;
        int32 height = 2;
    }
    message Site {
        //id 网站识别符。
        string id = 1;

        //name 网址名称。
        string name = 2;

        //网址域名
        string domain = 3;

        //当前页面 url
        string page = 4;

        //当前页面的 Referrer url
        string ref = 5;

        // 允许的推广位的尺寸:
        repeated SiteAdSize siteadsize = 6;
        //关键词
        repeated string keywords = 7;
        //行业
        repeated string cat = 8;
        //扩展字段
        string ext =9;
    }
    Site site =13;
}
// 用户年龄
enum UserAgeType {
 UNKNOWN_AGE_TYPE = 0;  // 未知年龄
 ZERO_AGE_TYPE = 1;  // 0-17
 TEN_AGE_TYPE = 2;  // 18-23
 TWENTY_AGE_TYPE = 3;  // 24-30
 THIRTY_AGE_TYPE = 4;  // 31-40
 FORTY_AGE_TYPE = 5;  // 41-49
 FIFTY_AGE_TYPE = 6;  // 50及以上
};
message BidResponse {
    // 竞价响应识别符，如不等同于竞价请求，拒绝该竞价响应（对应bidreqeust中的id）
    string id = 1;

    //竞价席位对象数组。
    message SeatBid {
        //出价对象数组。每个出价对象关联竞价请求中的一个广告曝光请求对象
        message Bid {
            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string id = 1;

            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string impid = 2;

            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string dealid = 3;

            //广告出价。CPM记，单位为人民币分。非私有交易必须返回
            int64 price = 4;

            //广告类型。1、原生；2视频贴片
            int32 adtype = 5;

            //广告创意
            message Creative {
                //广告创意ID
                string id = 1;

                //图片素材地址数组
                repeated string images = 2;

                //创意落地页地址
                string landpage = 3;

                //下载型创意直接下载地址,为防止出现问题，先标记弃用，将来可能会删除
                //新的功能会移到app对象中
                string dlurl = 4 [deprecated = true];

                //Deep link地址
                string dplurl = 5;

                //图标/Logo地址或Base64编码（用http开头区分）
                string icon = 6;

                //创意标题，超出Request要求会被截断
                string title = 7;

                //创意标题，超出Request要求会被截断
                string text = 8;

                //创意补充，简介
                string desc = 9;

                //原生广告返回素材对应广告形式
                string type = 10;

                App app = 11;

                message App {
                    //app中文名称
                    string name = 1;

                    //直接下载地址，目的是想要替换掉Creative里的dlurl
                    string dlurl = 2;

                    //android用，包名
                    string package = 3;

                    //ios用，APP_ID
                    string appid = 4;

                    //开始下载监测
                    repeated string dsurls=7;

                    // 下载完成监测
                    repeated string dcurls = 8;

                    //开始安装监测
                    repeated string installstarturls=9;

                    //安装完成监测
                    repeated string installedurls=10;

                    //开发者名称
                    string developerName = 11;

                    //app版本号
                    string appVersion = 12;

                    //隐私协议url
                    string appPrivacyPolicyUrl = 13;

                    //应用权限页面url
                    string appPermissionUrl = 14;

                    //应用权限说明列表
                    repeated AppPermission appPermissions = 15;

                    message AppPermission {
                        string permissionLabel = 1;

                        string permissionDesc = 2;
                    }

                    //应用 icon 地址
                    string appIconUrl = 16;

                    //安卓应用 appinfo url
                    string appInfoUrl = 17;

                    //应用大小
                    int64 appApkSize = 18;

                }

                // 下载类型0.普通下载(默认)；1.广点通下载
                int32 daction = 12;

                //Deep link唤起成功监测数组
                repeated string dpsurls = 13;

                //复制码
                string copycode=14;

                //广告点击交互类型；1.app; 2.WeChat Mini Program 微信小程序（需接入微信SDK);
                int32 interactionType = 15;


                //小程序广告 interactionType=2 的时候才会返回该字段
                WeChatMiniProgram weChatMiniProgram = 16;

                message WeChatMiniProgram{
                     //小程序广告  应用appid 需要客户端接入微信SDK
                    string appId = 1;

                    //小程序广告  小程序原始id
                    string originId = 2;

                    //小程序广告 小程序路径
                    string path = 3;
                }

                //创业所属行业
                repeated string category = 17;
                //视频广告信息
                Video video = 18;

                message Video{
                    //视频地址
                    string video_url = 1;
                    //开始播放监测地址
                    repeated string video_begin = 2;
                    //播放结束监测地址
                    repeated string video_end = 3;
                    //视频时长
                    string video_time = 4;
                    //视频大小
                    string video_size = 5;

                    //视频封面
                    string cover_url = 6;

                    //视频播放至1/4(25%)，上报监测
                    repeated string first_quartile_urls = 7;

                    //视频播放至1/2(50%)，上报监测
                    repeated string mid_point_urls = 8;

                    //视频播放至3/4(75%)，上报监测
                    repeated string third_quartile_urls = 9;

                    int32 width = 10;

                    int32 height = 11;
                }

                // 素材图片高
                int32 h = 19;

                // 素材图片宽
                int32 w = 20;

                //竞价模式 0-cpm 1-cpc
                int32 bidMode = 21;

                //iOS唤起通用链接
                string universal_link = 22;
            }
            Creative creative = 6;

            //视频贴片vast代码
            string vast = 7;

            //曝光监测地址数组（异步发送，支持宏替换）
            repeated string aimps = 8;

            //点击监测地址数组（异步发送，支持宏替换）
            repeated string aclks = 9;

            //胜出通知。（由Server端发送，支持宏替换）
            string nurl = 10;
        }
        repeated Bid bid = 1;

        //代表出价的竞价方席位识别符，由凤飞系统生成并提供
        string seat = 2;
    }
    repeated SeatBid seatbid = 2;

    //竞价响应id，用于辅助竞价方追踪监测
    string bidid = 3;
}