// Copyright (c) 2023 JD Inc.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: jd_ssp_api.proto

package jd_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------------- BidRequest结构 -------------------------------------------
type ConnectionType int32

const (
	ConnectionType_UNKNOWN  ConnectionType = 0 // 无法探测当前网络状态
	ConnectionType_ETHERNET ConnectionType = 1 // 以太网接入
	ConnectionType_WIFI     ConnectionType = 2 // Wi-Fi网络接入
	// 备注：蜂窝数据，移动通信信号蜂窝状而命名，包含安卓的移动数据和IOS的蜂窝网络
	ConnectionType_CELLULAR_UNKNOWN ConnectionType = 3 // 蜂窝数据接入，未知网络类型
	ConnectionType_CELLULAR_2G      ConnectionType = 4 // 蜂窝数据2G网络
	ConnectionType_CELLULAR_3G      ConnectionType = 5 // 蜂窝数据3G网络
	ConnectionType_CELLULAR_4G      ConnectionType = 6 // 蜂窝数据4G网络
	ConnectionType_CELLULAR_5G      ConnectionType = 7 // 蜂窝数据5G网络
)

// Enum value maps for ConnectionType.
var (
	ConnectionType_name = map[int32]string{
		0: "UNKNOWN",
		1: "ETHERNET",
		2: "WIFI",
		3: "CELLULAR_UNKNOWN",
		4: "CELLULAR_2G",
		5: "CELLULAR_3G",
		6: "CELLULAR_4G",
		7: "CELLULAR_5G",
	}
	ConnectionType_value = map[string]int32{
		"UNKNOWN":          0,
		"ETHERNET":         1,
		"WIFI":             2,
		"CELLULAR_UNKNOWN": 3,
		"CELLULAR_2G":      4,
		"CELLULAR_3G":      5,
		"CELLULAR_4G":      6,
		"CELLULAR_5G":      7,
	}
)

func (x ConnectionType) Enum() *ConnectionType {
	p := new(ConnectionType)
	*p = x
	return p
}

func (x ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_jd_ssp_api_proto_enumTypes[0].Descriptor()
}

func (ConnectionType) Type() protoreflect.EnumType {
	return &file_jd_ssp_api_proto_enumTypes[0]
}

func (x ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionType.Descriptor instead.
func (ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{0}
}

type OperatingSystem int32

const (
	OperatingSystem_OS_UNKNOWN OperatingSystem = 0 // 未知类型
	OperatingSystem_OS_ANDROID OperatingSystem = 1 // 安卓
	OperatingSystem_OS_IOS     OperatingSystem = 2 // IOS
)

// Enum value maps for OperatingSystem.
var (
	OperatingSystem_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "OS_ANDROID",
		2: "OS_IOS",
	}
	OperatingSystem_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"OS_ANDROID": 1,
		"OS_IOS":     2,
	}
)

func (x OperatingSystem) Enum() *OperatingSystem {
	p := new(OperatingSystem)
	*p = x
	return p
}

func (x OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_jd_ssp_api_proto_enumTypes[1].Descriptor()
}

func (OperatingSystem) Type() protoreflect.EnumType {
	return &file_jd_ssp_api_proto_enumTypes[1]
}

func (x OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatingSystem.Descriptor instead.
func (OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{1}
}

type CarrierType int32

const (
	CarrierType_UNKNOWN_CARRIER CarrierType = 0 // 未知的运营商
	CarrierType_CHINA_MOBILE    CarrierType = 1 // 中国移动
	CarrierType_CHINA_UNICOM    CarrierType = 2 // 中国联通
	CarrierType_CHINA_TELECOM   CarrierType = 3 // 中国电信
)

// Enum value maps for CarrierType.
var (
	CarrierType_name = map[int32]string{
		0: "UNKNOWN_CARRIER",
		1: "CHINA_MOBILE",
		2: "CHINA_UNICOM",
		3: "CHINA_TELECOM",
	}
	CarrierType_value = map[string]int32{
		"UNKNOWN_CARRIER": 0,
		"CHINA_MOBILE":    1,
		"CHINA_UNICOM":    2,
		"CHINA_TELECOM":   3,
	}
)

func (x CarrierType) Enum() *CarrierType {
	p := new(CarrierType)
	*p = x
	return p
}

func (x CarrierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CarrierType) Descriptor() protoreflect.EnumDescriptor {
	return file_jd_ssp_api_proto_enumTypes[2].Descriptor()
}

func (CarrierType) Type() protoreflect.EnumType {
	return &file_jd_ssp_api_proto_enumTypes[2]
}

func (x CarrierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CarrierType.Descriptor instead.
func (CarrierType) EnumDescriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{2}
}

// --------------------------------------- BidResponse结构 --------------------------------------------
type SpecSetId int32

const (
	SpecSetId_SPEC_SET_UNKNOWN             SpecSetId = 0     // 未知
	SpecSetId_SPLASH_SINGLE_IMG_2_3        SpecSetId = 10001 // 开屏2:3单图
	SpecSetId_SPLASH_SINGLE_IMG_9_16       SpecSetId = 10002 // 开屏9:16单图
	SpecSetId_FLOW_FEED_SINGLE_IMG_16_9    SpecSetId = 10003 // 信息流16:9单图
	SpecSetId_FLOW_FEED_SINGLE_IMG_3_2     SpecSetId = 10004 // 信息流3:2单图
	SpecSetId_FLOW_FEED_MULTI_IMG_3_2      SpecSetId = 10005 // 信息流3:2组图
	SpecSetId_FLOW_FEED_MULTI_IMG_2_1      SpecSetId = 10006 // 信息流2:1组图
	SpecSetId_FLOW_FEED_SINGLE_VIDEO_16_9  SpecSetId = 10007 // 信息流16:9单视频
	SpecSetId_FLOW_FEED_SINGLE_VIDEO_9_16  SpecSetId = 10008 // 信息流9:16单视频
	SpecSetId_SPLASH_SINGLE_VIDEO_9_16     SpecSetId = 10009 // 开屏9:16单视频
	SpecSetId_FLOW_FEED_SINGLE_IMG_9_16    SpecSetId = 10010 // 信息流9:16单图
	SpecSetId_INTERSTITIAL_SINGLE_IMG_9_16 SpecSetId = 10011 // 插屏9:16单图
)

// Enum value maps for SpecSetId.
var (
	SpecSetId_name = map[int32]string{
		0:     "SPEC_SET_UNKNOWN",
		10001: "SPLASH_SINGLE_IMG_2_3",
		10002: "SPLASH_SINGLE_IMG_9_16",
		10003: "FLOW_FEED_SINGLE_IMG_16_9",
		10004: "FLOW_FEED_SINGLE_IMG_3_2",
		10005: "FLOW_FEED_MULTI_IMG_3_2",
		10006: "FLOW_FEED_MULTI_IMG_2_1",
		10007: "FLOW_FEED_SINGLE_VIDEO_16_9",
		10008: "FLOW_FEED_SINGLE_VIDEO_9_16",
		10009: "SPLASH_SINGLE_VIDEO_9_16",
		10010: "FLOW_FEED_SINGLE_IMG_9_16",
		10011: "INTERSTITIAL_SINGLE_IMG_9_16",
	}
	SpecSetId_value = map[string]int32{
		"SPEC_SET_UNKNOWN":             0,
		"SPLASH_SINGLE_IMG_2_3":        10001,
		"SPLASH_SINGLE_IMG_9_16":       10002,
		"FLOW_FEED_SINGLE_IMG_16_9":    10003,
		"FLOW_FEED_SINGLE_IMG_3_2":     10004,
		"FLOW_FEED_MULTI_IMG_3_2":      10005,
		"FLOW_FEED_MULTI_IMG_2_1":      10006,
		"FLOW_FEED_SINGLE_VIDEO_16_9":  10007,
		"FLOW_FEED_SINGLE_VIDEO_9_16":  10008,
		"SPLASH_SINGLE_VIDEO_9_16":     10009,
		"FLOW_FEED_SINGLE_IMG_9_16":    10010,
		"INTERSTITIAL_SINGLE_IMG_9_16": 10011,
	}
)

func (x SpecSetId) Enum() *SpecSetId {
	p := new(SpecSetId)
	*p = x
	return p
}

func (x SpecSetId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SpecSetId) Descriptor() protoreflect.EnumDescriptor {
	return file_jd_ssp_api_proto_enumTypes[3].Descriptor()
}

func (SpecSetId) Type() protoreflect.EnumType {
	return &file_jd_ssp_api_proto_enumTypes[3]
}

func (x SpecSetId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SpecSetId.Descriptor instead.
func (SpecSetId) EnumDescriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{3}
}

type Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 交易信息
	Deals []*Pmp_Deal `protobuf:"bytes,1,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *Pmp) Reset() {
	*x = Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pmp) ProtoMessage() {}

func (x *Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pmp.ProtoReflect.Descriptor instead.
func (*Pmp) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{0}
}

func (x *Pmp) GetDeals() []*Pmp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 展示id，唯一标识一个展示；由媒体侧生成，请确保全局唯一
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 广告位id，在媒体系统中唯一标识一个广告位。（确保与对接平台上登记的tagid保持一致，否则无法返回广告)
	TagId *string `protobuf:"bytes,2,opt,name=tag_id,json=tagId,proto3,oneof" json:"tag_id,omitempty"`
	// 私有交易信息，PMP模式下才需要此字段
	Pmp *Pmp `protobuf:"bytes,3,opt,name=pmp,proto3,oneof" json:"pmp,omitempty"`
	// 召回广告数，默认1，不多于5个
	AdsCount *int32 `protobuf:"varint,4,opt,name=ads_count,json=adsCount,proto3,oneof" json:"ads_count,omitempty"`
}

func (x *Impression) Reset() {
	*x = Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Impression) ProtoMessage() {}

func (x *Impression) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Impression.ProtoReflect.Descriptor instead.
func (*Impression) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{1}
}

func (x *Impression) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Impression) GetTagId() string {
	if x != nil && x.TagId != nil {
		return *x.TagId
	}
	return ""
}

func (x *Impression) GetPmp() *Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *Impression) GetAdsCount() int32 {
	if x != nil && x.AdsCount != nil {
		return *x.AdsCount
	}
	return 0
}

type App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// application bundle或package name, 需要和京媒平台填写的包名保持一致
	Bundle *string `protobuf:"bytes,1,opt,name=bundle,proto3,oneof" json:"bundle,omitempty"`
	// 应用id，需要和京媒平台保持一致
	AppId *string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3,oneof" json:"app_id,omitempty"`
}

func (x *App) Reset() {
	*x = App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*App) ProtoMessage() {}

func (x *App) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use App.ProtoReflect.Descriptor instead.
func (*App) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{2}
}

func (x *App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *App) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

type Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 经度base64编码，wgs84坐标系
	LongitudeEnc *string `protobuf:"bytes,1,opt,name=longitude_enc,json=longitudeEnc,proto3,oneof" json:"longitude_enc,omitempty"`
	// 纬度base64编码，wgs84坐标系
	LatitudeEnc *string `protobuf:"bytes,2,opt,name=latitude_enc,json=latitudeEnc,proto3,oneof" json:"latitude_enc,omitempty"`
}

func (x *Geo) Reset() {
	*x = Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Geo) ProtoMessage() {}

func (x *Geo) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Geo.ProtoReflect.Descriptor instead.
func (*Geo) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{3}
}

func (x *Geo) GetLongitudeEnc() string {
	if x != nil && x.LongitudeEnc != nil {
		return *x.LongitudeEnc
	}
	return ""
}

func (x *Geo) GetLatitudeEnc() string {
	if x != nil && x.LatitudeEnc != nil {
		return *x.LatitudeEnc
	}
	return ""
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户pin，只有金融使用
	Pin *string `protobuf:"bytes,1,opt,name=pin,proto3,oneof" json:"pin,omitempty"`
	// 表示用户或用户设备当前地理位置信息
	Geo *Geo `protobuf:"bytes,2,opt,name=geo,proto3,oneof" json:"geo,omitempty"`
	// 是否安装了京东APP
	JdAppInstalled *bool `protobuf:"varint,3,opt,name=jd_app_installed,json=jdAppInstalled,proto3,oneof" json:"jd_app_installed,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{4}
}

func (x *User) GetPin() string {
	if x != nil && x.Pin != nil {
		return *x.Pin
	}
	return ""
}

func (x *User) GetGeo() *Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *User) GetJdAppInstalled() bool {
	if x != nil && x.JdAppInstalled != nil {
		return *x.JdAppInstalled
	}
	return false
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 操作系统类型
	Os *OperatingSystem `protobuf:"varint,1,opt,name=os,proto3,enum=jd_ssp.api.OperatingSystem,oneof" json:"os,omitempty"`
	// 操作系统版本
	OsVersion *string `protobuf:"bytes,2,opt,name=os_version,json=osVersion,proto3,oneof" json:"os_version,omitempty"`
	// 系统更新时间, 示例：1688439524000(安卓),1688439524.074839(ios)
	OsUpdateTime *string `protobuf:"bytes,3,opt,name=os_update_time,json=osUpdateTime,proto3,oneof" json:"os_update_time,omitempty"`
	// 设备IMEI
	Imei *string `protobuf:"bytes,4,opt,name=imei,proto3,oneof" json:"imei,omitempty"`
	// IMEI原值MD5(32位)
	ImeiMd5 *string `protobuf:"bytes,6,opt,name=imei_md5,json=imeiMd5,proto3,oneof" json:"imei_md5,omitempty"`
	// 设备OAID
	Oaid *string `protobuf:"bytes,7,opt,name=oaid,proto3,oneof" json:"oaid,omitempty"`
	// 设备OAID原值MD5(32位)
	OaidMd5 *string `protobuf:"bytes,8,opt,name=oaid_md5,json=oaidMd5,proto3,oneof" json:"oaid_md5,omitempty"`
	// 设备IDFA
	Idfa *string `protobuf:"bytes,9,opt,name=idfa,proto3,oneof" json:"idfa,omitempty"`
	// 设备IDFA原值MD5(32位)
	IdfaMd5 *string `protobuf:"bytes,10,opt,name=idfa_md5,json=idfaMd5,proto3,oneof" json:"idfa_md5,omitempty"`
	// 设备CAID
	Caids []*Device_Caid `protobuf:"bytes,11,rep,name=caids,proto3" json:"caids,omitempty"`
	// 设备ip地址，目前ipv4和ipv6都支持
	Ip *string `protobuf:"bytes,12,opt,name=ip,proto3,oneof" json:"ip,omitempty"`
	// IP地址base64编码
	IpEnc *string `protobuf:"bytes,13,opt,name=ip_enc,json=ipEnc,proto3,oneof" json:"ip_enc,omitempty"`
	// 客户端真实user agent，非服务端ua
	Ua *string `protobuf:"bytes,14,opt,name=ua,proto3,oneof" json:"ua,omitempty"`
	// 设备连接类型
	ConnectionType *ConnectionType `protobuf:"varint,15,opt,name=connection_type,json=connectionType,proto3,enum=jd_ssp.api.ConnectionType,oneof" json:"connection_type,omitempty"`
	// 设备制造商, 示例：Xiaomi、Apple
	Make *string `protobuf:"bytes,16,opt,name=make,proto3,oneof" json:"make,omitempty"`
	// 设备硬件型号, 示例：Redmi K30 Pro、iPhone
	Model *string `protobuf:"bytes,17,opt,name=model,proto3,oneof" json:"model,omitempty"`
	// md5后的设备名称，JAID需要
	HwName *string `protobuf:"bytes,18,opt,name=hw_name,json=hwName,proto3,oneof" json:"hw_name,omitempty"`
	// 系统型号，JAID需要
	HwMachine *string `protobuf:"bytes,19,opt,name=hw_machine,json=hwMachine,proto3,oneof" json:"hw_machine,omitempty"`
	// 运营商名称，JAID需要
	Carrier *CarrierType `protobuf:"varint,20,opt,name=carrier,proto3,enum=jd_ssp.api.CarrierType,oneof" json:"carrier,omitempty"`
	// 表示浏览器语言，使用ISO-639标准, 示例：zh、zh-Hans-CN
	Language *string `protobuf:"bytes,21,opt,name=language,proto3,oneof" json:"language,omitempty"`
	// 屏幕的物理高度，以像素为单位
	ScreenHeight *int32 `protobuf:"varint,22,opt,name=screen_height,json=screenHeight,proto3,oneof" json:"screen_height,omitempty"`
	// 屏幕的物理宽度，以像素为单位
	ScreenWidth *int32 `protobuf:"varint,23,opt,name=screen_width,json=screenWidth,proto3,oneof" json:"screen_width,omitempty"`
	// 屏幕每英寸像素数量
	Ppi *int32 `protobuf:"varint,24,opt,name=ppi,proto3,oneof" json:"ppi,omitempty"`
	// 系统内存，JAID需要
	SysMemory *string `protobuf:"bytes,25,opt,name=sys_memory,json=sysMemory,proto3,oneof" json:"sys_memory,omitempty"`
	// 硬盘容量
	SysDiskSize *string `protobuf:"bytes,26,opt,name=sys_disk_size,json=sysDiskSize,proto3,oneof" json:"sys_disk_size,omitempty"`
	// 国家代码，JAID需要，示例：CN
	CountryCode *string `protobuf:"bytes,27,opt,name=country_code,json=countryCode,proto3,oneof" json:"country_code,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{5}
}

func (x *Device) GetOs() OperatingSystem {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return OperatingSystem_OS_UNKNOWN
}

func (x *Device) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *Device) GetOsUpdateTime() string {
	if x != nil && x.OsUpdateTime != nil {
		return *x.OsUpdateTime
	}
	return ""
}

func (x *Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *Device) GetCaids() []*Device_Caid {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *Device) GetIpEnc() string {
	if x != nil && x.IpEnc != nil {
		return *x.IpEnc
	}
	return ""
}

func (x *Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *Device) GetConnectionType() ConnectionType {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return ConnectionType_UNKNOWN
}

func (x *Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *Device) GetHwName() string {
	if x != nil && x.HwName != nil {
		return *x.HwName
	}
	return ""
}

func (x *Device) GetHwMachine() string {
	if x != nil && x.HwMachine != nil {
		return *x.HwMachine
	}
	return ""
}

func (x *Device) GetCarrier() CarrierType {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return CarrierType_UNKNOWN_CARRIER
}

func (x *Device) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *Device) GetScreenHeight() int32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *Device) GetScreenWidth() int32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *Device) GetPpi() int32 {
	if x != nil && x.Ppi != nil {
		return *x.Ppi
	}
	return 0
}

func (x *Device) GetSysMemory() string {
	if x != nil && x.SysMemory != nil {
		return *x.SysMemory
	}
	return ""
}

func (x *Device) GetSysDiskSize() string {
	if x != nil && x.SysDiskSize != nil {
		return *x.SysDiskSize
	}
	return ""
}

func (x *Device) GetCountryCode() string {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return ""
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求id，唯一标识一次广告请求；由媒体侧生成，请确保全局唯一
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 协议版本，示例：4.0，从4.0开始为protobuf协议
	// (版本号来自平台下载文档https://opendoc.jd.com/janGroup/help/api/buyer_api.html)
	Version *string `protobuf:"bytes,2,opt,name=version,proto3,oneof" json:"version,omitempty"`
	// 展现广告资源位描述
	Impressions []*Impression `protobuf:"bytes,3,rep,name=impressions,proto3" json:"impressions,omitempty"`
	// 应用信息
	App *App `protobuf:"bytes,4,opt,name=app,proto3,oneof" json:"app,omitempty"`
	// 用户信息
	User *User `protobuf:"bytes,5,opt,name=user,proto3,oneof" json:"user,omitempty"`
	// 设备信息
	Device *Device `protobuf:"bytes,6,opt,name=device,proto3,oneof" json:"device,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{6}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest) GetImpressions() []*Impression {
	if x != nil {
		return x.Impressions
	}
	return nil
}

func (x *BidRequest) GetApp() *App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 序号，标示顺序，与媒体方约定展示顺序时使用
	SequenceId *string `protobuf:"bytes,1,opt,name=sequence_id,json=sequenceId,proto3,oneof" json:"sequence_id,omitempty"`
	// 图片地址
	Url *string `protobuf:"bytes,2,opt,name=url,proto3,oneof" json:"url,omitempty"`
	// 图片宽度
	Width *int64 `protobuf:"varint,3,opt,name=width,proto3,oneof" json:"width,omitempty"`
	// 图片高度
	Height *int64 `protobuf:"varint,4,opt,name=height,proto3,oneof" json:"height,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{7}
}

func (x *Image) GetSequenceId() string {
	if x != nil && x.SequenceId != nil {
		return *x.SequenceId
	}
	return ""
}

func (x *Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *Image) GetWidth() int64 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Image) GetHeight() int64 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 视频地址
	Url *string `protobuf:"bytes,1,opt,name=url,proto3,oneof" json:"url,omitempty"`
	// 视频宽度
	Width *int64 `protobuf:"varint,2,opt,name=width,proto3,oneof" json:"width,omitempty"`
	// 视频高度
	Height *int64 `protobuf:"varint,3,opt,name=height,proto3,oneof" json:"height,omitempty"`
	// 视频时长，单位毫秒
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{8}
}

func (x *Video) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *Video) GetWidth() int64 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Video) GetHeight() int64 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Video) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 序号，排查问题用
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 规格集id
	SpecSetId *SpecSetId `protobuf:"varint,2,opt,name=spec_set_id,json=specSetId,proto3,enum=jd_ssp.api.SpecSetId,oneof" json:"spec_set_id,omitempty"`
	// 标题
	Title *string `protobuf:"bytes,3,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// 描述
	Desc *string `protobuf:"bytes,4,opt,name=desc,proto3,oneof" json:"desc,omitempty"`
	// 广告来源，用于媒体侧展示广告来源，如：JD
	AdResource *string `protobuf:"bytes,5,opt,name=ad_resource,json=adResource,proto3,oneof" json:"ad_resource,omitempty"`
	// 返回多张图片url时的对象
	Imgs []*Image `protobuf:"bytes,6,rep,name=imgs,proto3" json:"imgs,omitempty"`
	// 视频素材
	Video *Video `protobuf:"bytes,7,opt,name=video,proto3,oneof" json:"video,omitempty"`
	// icon url
	IconUrl *string `protobuf:"bytes,8,opt,name=icon_url,json=iconUrl,proto3,oneof" json:"icon_url,omitempty"`
	// 小程序id
	MimiProgramId *string `protobuf:"bytes,9,opt,name=mimi_program_id,json=mimiProgramId,proto3,oneof" json:"mimi_program_id,omitempty"`
	// 小程序链接
	MimiProgramPath *string `protobuf:"bytes,10,opt,name=mimi_program_path,json=mimiProgramPath,proto3,oneof" json:"mimi_program_path,omitempty"`
	// 点击跳转h5页面地址。ADX模式必须替换URL里面的 {WIN_PRICE} 二价宏，展示分成模式不需要。
	ClickUrl *string `protobuf:"bytes,11,opt,name=click_url,json=clickUrl,proto3,oneof" json:"click_url,omitempty"`
	// 告知京东赢得bid，并通过宏替换{WIN_PRICE} 提供二价。非ADX竞价广告无此字段返回。
	NoticeUrl *string `protobuf:"bytes,12,opt,name=notice_url,json=noticeUrl,proto3,oneof" json:"notice_url,omitempty"`
	// 曝光监测url列表, 会有多个，每个url都需要发送。
	// ADX模式必须替换URL里面的 {WIN_PRICE} 二价宏，展示分成不需要替换
	ExposalUrls []string `protobuf:"bytes,13,rep,name=exposal_urls,json=exposalUrls,proto3" json:"exposal_urls,omitempty"`
	// 点击监测url列表，可能会有多个，每个都需要触发上报
	ClickMonitorUrls []string `protobuf:"bytes,14,rep,name=click_monitor_urls,json=clickMonitorUrls,proto3" json:"click_monitor_urls,omitempty"`
	// Deeplink协议，用于呼起京东APP或广告主APP
	// 已封装点击监测，能成功呼起则不需要再单独触发点击链接，呼起失败时需要H5打开click_url
	DeeplinkUrl *string `protobuf:"bytes,15,opt,name=deeplink_url,json=deeplinkUrl,proto3,oneof" json:"deeplink_url,omitempty"`
	// ios系统呼起链接，用于呼起京东APP或广告主APP。
	// 已封装点击监测，无论能否成功呼起，都不需要再单独触发点击链接click_url
	UniversalLinkUrl *string `protobuf:"bytes,16,opt,name=universal_link_url,json=universalLinkUrl,proto3,oneof" json:"universal_link_url,omitempty"`
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{9}
}

func (x *Item) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Item) GetSpecSetId() SpecSetId {
	if x != nil && x.SpecSetId != nil {
		return *x.SpecSetId
	}
	return SpecSetId_SPEC_SET_UNKNOWN
}

func (x *Item) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Item) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *Item) GetAdResource() string {
	if x != nil && x.AdResource != nil {
		return *x.AdResource
	}
	return ""
}

func (x *Item) GetImgs() []*Image {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *Item) GetVideo() *Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Item) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

func (x *Item) GetMimiProgramId() string {
	if x != nil && x.MimiProgramId != nil {
		return *x.MimiProgramId
	}
	return ""
}

func (x *Item) GetMimiProgramPath() string {
	if x != nil && x.MimiProgramPath != nil {
		return *x.MimiProgramPath
	}
	return ""
}

func (x *Item) GetClickUrl() string {
	if x != nil && x.ClickUrl != nil {
		return *x.ClickUrl
	}
	return ""
}

func (x *Item) GetNoticeUrl() string {
	if x != nil && x.NoticeUrl != nil {
		return *x.NoticeUrl
	}
	return ""
}

func (x *Item) GetExposalUrls() []string {
	if x != nil {
		return x.ExposalUrls
	}
	return nil
}

func (x *Item) GetClickMonitorUrls() []string {
	if x != nil {
		return x.ClickMonitorUrls
	}
	return nil
}

func (x *Item) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *Item) GetUniversalLinkUrl() string {
	if x != nil && x.UniversalLinkUrl != nil {
		return *x.UniversalLinkUrl
	}
	return ""
}

type Admat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 若干组广告素材集合对象
	Items []*Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Admat) Reset() {
	*x = Admat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Admat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Admat) ProtoMessage() {}

func (x *Admat) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Admat.ProtoReflect.Descriptor instead.
func (*Admat) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{10}
}

func (x *Admat) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 京东生成的session id
	Sid *string `protobuf:"bytes,1,opt,name=sid,proto3,oneof" json:"sid,omitempty"`
	// 京东生成的素材校验id
	AdId *string `protobuf:"bytes,2,opt,name=ad_id,json=adId,proto3,oneof" json:"ad_id,omitempty"`
	// 对应请求中的imp中的id
	ImpId *string `protobuf:"bytes,3,opt,name=imp_id,json=impId,proto3,oneof" json:"imp_id,omitempty"`
	// 竞价价格，单位：分(人民币)
	Price *float64 `protobuf:"fixed64,4,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// 素材对象
	Adm *Admat `protobuf:"bytes,5,opt,name=adm,proto3,oneof" json:"adm,omitempty"`
	// 私有交易信息
	Pmp *Pmp `protobuf:"bytes,6,opt,name=pmp,proto3,oneof" json:"pmp,omitempty"`
}

func (x *Bid) Reset() {
	*x = Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bid) ProtoMessage() {}

func (x *Bid) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bid.ProtoReflect.Descriptor instead.
func (*Bid) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{11}
}

func (x *Bid) GetSid() string {
	if x != nil && x.Sid != nil {
		return *x.Sid
	}
	return ""
}

func (x *Bid) GetAdId() string {
	if x != nil && x.AdId != nil {
		return *x.AdId
	}
	return ""
}

func (x *Bid) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *Bid) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *Bid) GetAdm() *Admat {
	if x != nil {
		return x.Adm
	}
	return nil
}

func (x *Bid) GetPmp() *Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

type SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 针对imp的bid信息
	Bids []*Bid `protobuf:"bytes,1,rep,name=bids,proto3" json:"bids,omitempty"`
}

func (x *SeatBid) Reset() {
	*x = SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeatBid) ProtoMessage() {}

func (x *SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeatBid.ProtoReflect.Descriptor instead.
func (*SeatBid) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{12}
}

func (x *SeatBid) GetBids() []*Bid {
	if x != nil {
		return x.Bids
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体请求id，唯一标识一次广告请求
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 京东生成的bid_id
	BidId *string `protobuf:"bytes,2,opt,name=bid_id,json=bidId,proto3,oneof" json:"bid_id,omitempty"`
	// 请求错误时的错误码，0表示成功，用于问题排查
	StatusCode *int32 `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3,oneof" json:"status_code,omitempty"`
	// 错误信息，用于问题排查
	StatusMessage *string `protobuf:"bytes,4,opt,name=status_message,json=statusMessage,proto3,oneof" json:"status_message,omitempty"`
	// 响应席位
	SeatBids []*SeatBid `protobuf:"bytes,5,rep,name=seat_bids,json=seatBids,proto3" json:"seat_bids,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{13}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetBidId() string {
	if x != nil && x.BidId != nil {
		return *x.BidId
	}
	return ""
}

func (x *BidResponse) GetStatusCode() int32 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *BidResponse) GetStatusMessage() string {
	if x != nil && x.StatusMessage != nil {
		return *x.StatusMessage
	}
	return ""
}

func (x *BidResponse) GetSeatBids() []*SeatBid {
	if x != nil {
		return x.SeatBids
	}
	return nil
}

type Pmp_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 交易id，媒体自定义
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
}

func (x *Pmp_Deal) Reset() {
	*x = Pmp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pmp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pmp_Deal) ProtoMessage() {}

func (x *Pmp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pmp_Deal.ProtoReflect.Descriptor instead.
func (*Pmp_Deal) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Pmp_Deal) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

type Device_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CAID
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// 版本号
	Version *string `protobuf:"bytes,2,opt,name=version,proto3,oneof" json:"version,omitempty"`
}

func (x *Device_Caid) Reset() {
	*x = Device_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jd_ssp_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device_Caid) ProtoMessage() {}

func (x *Device_Caid) ProtoReflect() protoreflect.Message {
	mi := &file_jd_ssp_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device_Caid.ProtoReflect.Descriptor instead.
func (*Device_Caid) Descriptor() ([]byte, []int) {
	return file_jd_ssp_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Device_Caid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Device_Caid) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

var File_jd_ssp_api_proto protoreflect.FileDescriptor

var file_jd_ssp_api_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x22, 0x55,
	0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x2a, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c,
	0x73, 0x1a, 0x22, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x42, 0x05,
	0x0a, 0x03, 0x5f, 0x69, 0x64, 0x22, 0xaf, 0x01, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x74, 0x61, 0x67,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x50, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a,
	0x09, 0x61, 0x64, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x03, 0x52, 0x08, 0x61, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69,
	0x64, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x6d, 0x70, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x61, 0x64,
	0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x1b,
	0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x22, 0x7a, 0x0a,
	0x03, 0x47, 0x65, 0x6f, 0x12, 0x28, 0x0a, 0x0d, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x5f, 0x65, 0x6e, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x45, 0x6e, 0x63, 0x88, 0x01, 0x01, 0x12, 0x26,
	0x0a, 0x0c, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x45, 0x6e, 0x63, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x63, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x63, 0x22, 0x99, 0x01, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x15, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x03, 0x70, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x03, 0x67, 0x65, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x6f, 0x48, 0x01, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x10, 0x6a, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0e, 0x6a,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x69, 0x6e, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x67, 0x65, 0x6f,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6a, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x22, 0xab, 0x0a, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x30, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6a,
	0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x02, 0x6f, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x6f, 0x73, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02,
	0x52, 0x0c, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x03, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x69, 0x6d,
	0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x07,
	0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35,
	0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x07, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08,
	0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08,
	0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x05,
	0x63, 0x61, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6a, 0x64,
	0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x43, 0x61, 0x69, 0x64, 0x52, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x12, 0x13, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x02, 0x69, 0x70, 0x88, 0x01, 0x01,
	0x12, 0x1a, 0x0a, 0x06, 0x69, 0x70, 0x5f, 0x65, 0x6e, 0x63, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0a, 0x52, 0x05, 0x69, 0x70, 0x45, 0x6e, 0x63, 0x88, 0x01, 0x01, 0x12, 0x13, 0x0a, 0x02,
	0x75, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x02, 0x75, 0x61, 0x88, 0x01,
	0x01, 0x12, 0x48, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6a, 0x64, 0x5f,
	0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x0c, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x04, 0x6d, 0x61, 0x6b,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x0e, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12,
	0x1c, 0x0a, 0x07, 0x68, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0f, 0x52, 0x06, 0x68, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a,
	0x0a, 0x68, 0x77, 0x5f, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x10, 0x52, 0x09, 0x68, 0x77, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x48, 0x11, 0x52, 0x07, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x48, 0x12, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x13, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x48, 0x14, 0x52, 0x0b, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x70, 0x70, 0x69, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x48, 0x15, 0x52, 0x03, 0x70, 0x70, 0x69,
	0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x79, 0x73, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x48, 0x16, 0x52, 0x09, 0x73, 0x79, 0x73, 0x4d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x73, 0x79, 0x73, 0x5f, 0x64,
	0x69, 0x73, 0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x17,
	0x52, 0x0b, 0x73, 0x79, 0x73, 0x44, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x26, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x18, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x4d, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64,
	0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x6f, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x6f, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x69, 0x6d, 0x65, 0x69, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6d,
	0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6f, 0x61, 0x69, 0x64, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x69, 0x64, 0x66, 0x61, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d,
	0x64, 0x35, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x70, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x69, 0x70,
	0x5f, 0x65, 0x6e, 0x63, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x75, 0x61, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x6d, 0x61, 0x6b, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x68, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x68, 0x77, 0x5f, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x70,
	0x69, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x79, 0x73, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x79, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x22, 0xad, 0x02, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6a, 0x64,
	0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x26, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x48, 0x02,
	0x52, 0x03, 0x61, 0x70, 0x70, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x48, 0x03, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x04, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x70, 0x70, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x22, 0xa9, 0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a,
	0x0b, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22,
	0xa1, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x01, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x75, 0x72,
	0x6c, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xb6, 0x06, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x13, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x53, 0x65, 0x74, 0x49, 0x64, 0x48, 0x01, 0x52,
	0x09, 0x73, 0x70, 0x65, 0x63, 0x53, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0a, 0x61, 0x64, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x2c,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x48, 0x05, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06,
	0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f,
	0x6d, 0x69, 0x6d, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0d, 0x6d, 0x69, 0x6d, 0x69, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x6d, 0x69, 0x6d,
	0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x0f, 0x6d, 0x69, 0x6d, 0x69, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a,
	0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0a, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x75, 0x6e, 0x69,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03,
	0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x64, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x6d, 0x69, 0x6d, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6d, 0x69, 0x6d, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x2f, 0x0a, 0x05,
	0x41, 0x64, 0x6d, 0x61, 0x74, 0x12, 0x26, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xf6, 0x01,
	0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x73, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x18, 0x0a, 0x05,
	0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x61,
	0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x03, 0x61, 0x64, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6a, 0x64, 0x5f,
	0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x64, 0x6d, 0x61, 0x74, 0x48, 0x04, 0x52,
	0x03, 0x61, 0x64, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x50, 0x6d, 0x70, 0x48, 0x05, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x42,
	0x06, 0x0a, 0x04, 0x5f, 0x73, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x61, 0x64, 0x5f, 0x69,
	0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x64, 0x6d, 0x42, 0x06,
	0x0a, 0x04, 0x5f, 0x70, 0x6d, 0x70, 0x22, 0x2e, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x04, 0x62, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x6a, 0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x04, 0x62, 0x69, 0x64, 0x73, 0x22, 0xf7, 0x01, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x62,
	0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x62,
	0x69, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a,
	0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x65, 0x61,
	0x74, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6a,
	0x64, 0x5f, 0x73, 0x73, 0x70, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x52, 0x08, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x73, 0x42, 0x05, 0x0a, 0x03, 0x5f,
	0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2a, 0x8f, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e, 0x45, 0x54, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x45, 0x4c, 0x4c,
	0x55, 0x4c, 0x41, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x12, 0x0f,
	0x0a, 0x0b, 0x43, 0x45, 0x4c, 0x4c, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x32, 0x47, 0x10, 0x04, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x45, 0x4c, 0x4c, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x33, 0x47, 0x10, 0x05,
	0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x45, 0x4c, 0x4c, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x34, 0x47, 0x10,
	0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x45, 0x4c, 0x4c, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x35, 0x47,
	0x10, 0x07, 0x2a, 0x3d, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x52,
	0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x53, 0x5f, 0x49, 0x4f, 0x53, 0x10,
	0x02, 0x2a, 0x59, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x41, 0x52, 0x52,
	0x49, 0x45, 0x52, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41,
	0x5f, 0x55, 0x4e, 0x49, 0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x48, 0x49,
	0x4e, 0x41, 0x5f, 0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x2a, 0xfb, 0x02, 0x0a,
	0x09, 0x53, 0x70, 0x65, 0x63, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x50,
	0x45, 0x43, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x1a, 0x0a, 0x15, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c,
	0x45, 0x5f, 0x49, 0x4d, 0x47, 0x5f, 0x32, 0x5f, 0x33, 0x10, 0x91, 0x4e, 0x12, 0x1b, 0x0a, 0x16,
	0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x4d,
	0x47, 0x5f, 0x39, 0x5f, 0x31, 0x36, 0x10, 0x92, 0x4e, 0x12, 0x1e, 0x0a, 0x19, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x4d,
	0x47, 0x5f, 0x31, 0x36, 0x5f, 0x39, 0x10, 0x93, 0x4e, 0x12, 0x1d, 0x0a, 0x18, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x4d,
	0x47, 0x5f, 0x33, 0x5f, 0x32, 0x10, 0x94, 0x4e, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x49, 0x4d, 0x47, 0x5f,
	0x33, 0x5f, 0x32, 0x10, 0x95, 0x4e, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x46,
	0x45, 0x45, 0x44, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x49, 0x4d, 0x47, 0x5f, 0x32, 0x5f,
	0x31, 0x10, 0x96, 0x4e, 0x12, 0x20, 0x0a, 0x1b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x46, 0x45, 0x45,
	0x44, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x31,
	0x36, 0x5f, 0x39, 0x10, 0x97, 0x4e, 0x12, 0x20, 0x0a, 0x1b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x46,
	0x45, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x5f, 0x39, 0x5f, 0x31, 0x36, 0x10, 0x98, 0x4e, 0x12, 0x1d, 0x0a, 0x18, 0x53, 0x50, 0x4c, 0x41,
	0x53, 0x48, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f,
	0x39, 0x5f, 0x31, 0x36, 0x10, 0x99, 0x4e, 0x12, 0x1e, 0x0a, 0x19, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x46, 0x45, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x4d, 0x47, 0x5f,
	0x39, 0x5f, 0x31, 0x36, 0x10, 0x9a, 0x4e, 0x12, 0x21, 0x0a, 0x1c, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x53, 0x54, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x49,
	0x4d, 0x47, 0x5f, 0x39, 0x5f, 0x31, 0x36, 0x10, 0x9b, 0x4e, 0x42, 0x13, 0x5a, 0x11, 0x6d, 0x68,
	0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x6a, 0x64, 0x5f, 0x75, 0x70, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jd_ssp_api_proto_rawDescOnce sync.Once
	file_jd_ssp_api_proto_rawDescData = file_jd_ssp_api_proto_rawDesc
)

func file_jd_ssp_api_proto_rawDescGZIP() []byte {
	file_jd_ssp_api_proto_rawDescOnce.Do(func() {
		file_jd_ssp_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_jd_ssp_api_proto_rawDescData)
	})
	return file_jd_ssp_api_proto_rawDescData
}

var file_jd_ssp_api_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_jd_ssp_api_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_jd_ssp_api_proto_goTypes = []interface{}{
	(ConnectionType)(0),  // 0: jd_ssp.api.ConnectionType
	(OperatingSystem)(0), // 1: jd_ssp.api.OperatingSystem
	(CarrierType)(0),     // 2: jd_ssp.api.CarrierType
	(SpecSetId)(0),       // 3: jd_ssp.api.SpecSetId
	(*Pmp)(nil),          // 4: jd_ssp.api.Pmp
	(*Impression)(nil),   // 5: jd_ssp.api.Impression
	(*App)(nil),          // 6: jd_ssp.api.App
	(*Geo)(nil),          // 7: jd_ssp.api.Geo
	(*User)(nil),         // 8: jd_ssp.api.User
	(*Device)(nil),       // 9: jd_ssp.api.Device
	(*BidRequest)(nil),   // 10: jd_ssp.api.BidRequest
	(*Image)(nil),        // 11: jd_ssp.api.Image
	(*Video)(nil),        // 12: jd_ssp.api.Video
	(*Item)(nil),         // 13: jd_ssp.api.Item
	(*Admat)(nil),        // 14: jd_ssp.api.Admat
	(*Bid)(nil),          // 15: jd_ssp.api.Bid
	(*SeatBid)(nil),      // 16: jd_ssp.api.SeatBid
	(*BidResponse)(nil),  // 17: jd_ssp.api.BidResponse
	(*Pmp_Deal)(nil),     // 18: jd_ssp.api.Pmp.Deal
	(*Device_Caid)(nil),  // 19: jd_ssp.api.Device.Caid
}
var file_jd_ssp_api_proto_depIdxs = []int32{
	18, // 0: jd_ssp.api.Pmp.deals:type_name -> jd_ssp.api.Pmp.Deal
	4,  // 1: jd_ssp.api.Impression.pmp:type_name -> jd_ssp.api.Pmp
	7,  // 2: jd_ssp.api.User.geo:type_name -> jd_ssp.api.Geo
	1,  // 3: jd_ssp.api.Device.os:type_name -> jd_ssp.api.OperatingSystem
	19, // 4: jd_ssp.api.Device.caids:type_name -> jd_ssp.api.Device.Caid
	0,  // 5: jd_ssp.api.Device.connection_type:type_name -> jd_ssp.api.ConnectionType
	2,  // 6: jd_ssp.api.Device.carrier:type_name -> jd_ssp.api.CarrierType
	5,  // 7: jd_ssp.api.BidRequest.impressions:type_name -> jd_ssp.api.Impression
	6,  // 8: jd_ssp.api.BidRequest.app:type_name -> jd_ssp.api.App
	8,  // 9: jd_ssp.api.BidRequest.user:type_name -> jd_ssp.api.User
	9,  // 10: jd_ssp.api.BidRequest.device:type_name -> jd_ssp.api.Device
	3,  // 11: jd_ssp.api.Item.spec_set_id:type_name -> jd_ssp.api.SpecSetId
	11, // 12: jd_ssp.api.Item.imgs:type_name -> jd_ssp.api.Image
	12, // 13: jd_ssp.api.Item.video:type_name -> jd_ssp.api.Video
	13, // 14: jd_ssp.api.Admat.items:type_name -> jd_ssp.api.Item
	14, // 15: jd_ssp.api.Bid.adm:type_name -> jd_ssp.api.Admat
	4,  // 16: jd_ssp.api.Bid.pmp:type_name -> jd_ssp.api.Pmp
	15, // 17: jd_ssp.api.SeatBid.bids:type_name -> jd_ssp.api.Bid
	16, // 18: jd_ssp.api.BidResponse.seat_bids:type_name -> jd_ssp.api.SeatBid
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_jd_ssp_api_proto_init() }
func file_jd_ssp_api_proto_init() {
	if File_jd_ssp_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_jd_ssp_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Admat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pmp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jd_ssp_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_jd_ssp_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_jd_ssp_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jd_ssp_api_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_jd_ssp_api_proto_goTypes,
		DependencyIndexes: file_jd_ssp_api_proto_depIdxs,
		EnumInfos:         file_jd_ssp_api_proto_enumTypes,
		MessageInfos:      file_jd_ssp_api_proto_msgTypes,
	}.Build()
	File_jd_ssp_api_proto = out.File
	file_jd_ssp_api_proto_rawDesc = nil
	file_jd_ssp_api_proto_goTypes = nil
	file_jd_ssp_api_proto_depIdxs = nil
}
