// Copyright (c) 2023 JD Inc.
syntax = "proto3";

package jd_ssp.api;

option go_package = "mh_proxy/pb/jd_up";

// --------------------------------------- BidRequest结构 -------------------------------------------
enum ConnectionType {
    UNKNOWN  = 0;  // 无法探测当前网络状态
    ETHERNET = 1;  // 以太网接入
    WIFI     = 2;  // Wi-Fi网络接入
    // 备注：蜂窝数据，移动通信信号蜂窝状而命名，包含安卓的移动数据和IOS的蜂窝网络
    CELLULAR_UNKNOWN = 3;  // 蜂窝数据接入，未知网络类型
    CELLULAR_2G      = 4;  // 蜂窝数据2G网络
    CELLULAR_3G      = 5;  // 蜂窝数据3G网络
    CELLULAR_4G      = 6;  // 蜂窝数据4G网络
    CELLULAR_5G      = 7;  // 蜂窝数据5G网络
}

enum OperatingSystem {
    OS_UNKNOWN = 0;  // 未知类型
    OS_ANDROID = 1;  // 安卓
    OS_IOS     = 2;  // IOS
}

enum CarrierType {
    UNKNOWN_CARRIER = 0;  // 未知的运营商
    CHINA_MOBILE    = 1;  // 中国移动
    CHINA_UNICOM    = 2;  // 中国联通
    CHINA_TELECOM   = 3;  // 中国电信
}

message Pmp {
    message Deal {
        // 交易id，媒体自定义
        optional string id = 1;
    }
    // 交易信息
    repeated Deal deals = 1;
}

message Impression {
    // 展示id，唯一标识一个展示；由媒体侧生成，请确保全局唯一
    optional string id = 1;
    // 广告位id，在媒体系统中唯一标识一个广告位。（确保与对接平台上登记的tagid保持一致，否则无法返回广告)
    optional string tag_id = 2;
    // 私有交易信息，PMP模式下才需要此字段
    optional Pmp pmp = 3;
    // 召回广告数，默认1，不多于5个
    optional int32 ads_count = 4;
}

message App {
    // application bundle或package name, 需要和京媒平台填写的包名保持一致
    optional string bundle = 1;
    // 应用id，需要和京媒平台保持一致
    optional string app_id = 2;
}

message Geo {
    // 经度base64编码，wgs84坐标系
    optional string longitude_enc = 1;
    // 纬度base64编码，wgs84坐标系
    optional string latitude_enc  = 2;
}

message User {
    // 用户pin，只有金融使用
    optional string pin = 1;
    // 表示用户或用户设备当前地理位置信息
    optional Geo geo = 2;
    // 是否安装了京东APP
    optional bool jd_app_installed = 3;
}

message Device {
    // 操作系统类型
    optional OperatingSystem os = 1;
    // 操作系统版本
    optional string os_version = 2;
    // 系统更新时间, 示例：1688439524000(安卓),1688439524.074839(ios)
    optional string os_update_time = 3;
    // 设备IMEI
    optional string imei = 4;
    // IMEI原值MD5(32位)
    optional string imei_md5 = 6;
    // 设备OAID
    optional string oaid = 7;
    // 设备OAID原值MD5(32位)
    optional string oaid_md5 = 8;
    // 设备IDFA
    optional string idfa = 9;
    // 设备IDFA原值MD5(32位)
    optional string idfa_md5 = 10;
    message Caid {
        // CAID
        optional string id = 1;
        // 版本号
        optional string version = 2;
    }
    // 设备CAID
    repeated Caid caids = 11;
    // 设备ip地址，目前ipv4和ipv6都支持
    optional string ip = 12;
    // IP地址base64编码
    optional string ip_enc = 13;
    // 客户端真实user agent，非服务端ua
    optional string ua = 14;
    // 设备连接类型
    optional ConnectionType connection_type = 15;
    // 设备制造商, 示例：Xiaomi、Apple
    optional string make = 16;
    // 设备硬件型号, 示例：Redmi K30 Pro、iPhone
    optional string model = 17;
    //md5后的设备名称，JAID需要
    optional string hw_name = 18;
    //系统型号，JAID需要
    optional string hw_machine = 19;
    // 运营商名称，JAID需要
    optional CarrierType carrier = 20;
    // 表示浏览器语言，使用ISO-639标准, 示例：zh、zh-Hans-CN
    optional string language = 21;
    // 屏幕的物理高度，以像素为单位
    optional int32 screen_height = 22;
    // 屏幕的物理宽度，以像素为单位
    optional int32 screen_width = 23;
    // 屏幕每英寸像素数量
    optional int32 ppi = 24;
    // 系统内存，JAID需要
    optional string sys_memory = 25;
    // 硬盘容量
    optional string sys_disk_size = 26;
    // 国家代码，JAID需要，示例：CN
    optional string country_code = 27;
}

message BidRequest {
    // 请求id，唯一标识一次广告请求；由媒体侧生成，请确保全局唯一
    optional string id = 1;
    // 协议版本，示例：4.0，从4.0开始为protobuf协议
    // (版本号来自平台下载文档https://opendoc.jd.com/janGroup/help/api/buyer_api.html)
    optional string version = 2;
    // 展现广告资源位描述
    repeated Impression impressions = 3;
    // 应用信息
    optional App app = 4;
    // 用户信息
    optional User user = 5;
    // 设备信息
    optional Device device = 6;
}

// --------------------------------------- BidResponse结构 --------------------------------------------
enum SpecSetId {
    SPEC_SET_UNKNOWN             = 0;     // 未知
    SPLASH_SINGLE_IMG_2_3        = 10001;  // 开屏2:3单图
    SPLASH_SINGLE_IMG_9_16       = 10002;  // 开屏9:16单图
    FLOW_FEED_SINGLE_IMG_16_9    = 10003;  // 信息流16:9单图
    FLOW_FEED_SINGLE_IMG_3_2     = 10004;  // 信息流3:2单图
    FLOW_FEED_MULTI_IMG_3_2      = 10005;  // 信息流3:2组图
    FLOW_FEED_MULTI_IMG_2_1      = 10006;  // 信息流2:1组图
    FLOW_FEED_SINGLE_VIDEO_16_9  = 10007;  // 信息流16:9单视频
    FLOW_FEED_SINGLE_VIDEO_9_16  = 10008;  // 信息流9:16单视频
    SPLASH_SINGLE_VIDEO_9_16     = 10009;  // 开屏9:16单视频
    FLOW_FEED_SINGLE_IMG_9_16    = 10010;  // 信息流9:16单图
    INTERSTITIAL_SINGLE_IMG_9_16 = 10011;  // 插屏9:16单图

}

message Image {
    // 序号，标示顺序，与媒体方约定展示顺序时使用
    optional string sequence_id = 1;
    // 图片地址
    optional string url	= 2;
    // 图片宽度
    optional int64 width = 3;
    // 图片高度
    optional int64 height = 4;
}

message Video {
    // 视频地址
    optional string url	= 1;
    // 视频宽度
    optional int64 width = 2;
    // 视频高度
    optional int64 height = 3;
    // 视频时长，单位毫秒
    optional int32 duration = 4;
}

message Item {
    // 序号，排查问题用
    optional string id = 1;
    // 规格集id
    optional SpecSetId spec_set_id = 2;
    // 标题
    optional string title = 3;
    // 描述
    optional string desc = 4;
    // 广告来源，用于媒体侧展示广告来源，如：JD
    optional string ad_resource = 5;
    // 返回多张图片url时的对象
    repeated Image  imgs = 6;
    // 视频素材
    optional Video  video = 7;
    // icon url
    optional string icon_url = 8;
    // 小程序id
    optional string mimi_program_id = 9;
    // 小程序链接
    optional string mimi_program_path = 10;
    // 点击跳转h5页面地址。ADX模式必须替换URL里面的 {WIN_PRICE} 二价宏，展示分成模式不需要。
    optional string click_url = 11;
    // 告知京东赢得bid，并通过宏替换{WIN_PRICE} 提供二价。非ADX竞价广告无此字段返回。
    optional string notice_url = 12;
    // 曝光监测url列表, 会有多个，每个url都需要发送。
    // ADX模式必须替换URL里面的 {WIN_PRICE} 二价宏，展示分成不需要替换
    repeated string exposal_urls = 13;
    // 点击监测url列表，可能会有多个，每个都需要触发上报
    repeated string click_monitor_urls = 14;
    // Deeplink协议，用于呼起京东APP或广告主APP
    // 已封装点击监测，能成功呼起则不需要再单独触发点击链接，呼起失败时需要H5打开click_url
    optional string deeplink_url = 15;
    // ios系统呼起链接，用于呼起京东APP或广告主APP。
    // 已封装点击监测，无论能否成功呼起，都不需要再单独触发点击链接click_url
    optional string universal_link_url = 16;
}

message Admat {
    // 若干组广告素材集合对象
    repeated Item items = 1;
}

message Bid {
    // 京东生成的session id
    optional string sid = 1;
    // 京东生成的素材校验id
    optional string ad_id = 2;
    // 对应请求中的imp中的id
    optional string imp_id = 3;
    // 竞价价格，单位：分(人民币)
    optional double price = 4;
    // 素材对象
    optional Admat adm = 5;
    // 私有交易信息
    optional Pmp pmp = 6;
}

message SeatBid {
    // 针对imp的bid信息
    repeated Bid bids = 1;
}

message BidResponse {
    // 媒体请求id，唯一标识一次广告请求
    optional string id = 1;
    // 京东生成的bid_id
    optional string bid_id = 2;
    // 请求错误时的错误码，0表示成功，用于问题排查
    optional int32  status_code = 3;
    // 错误信息，用于问题排查
    optional string status_message = 4;
    // 响应席位
    repeated SeatBid seat_bids = 5;
}

