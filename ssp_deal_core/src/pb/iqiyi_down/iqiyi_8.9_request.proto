// This file is derived from the OpenRTB specification.

syntax = "proto2";

option go_package = "mh_proxy/pb/iqiyi_down";

package iqiyi_down;

message FloorPrice {
    optional int64 industry = 1;
    optional int32 price = 2;
    // This field will be set just for roll type requests.
    optional int32 skippable_roll_price = 3;
}

message UserFeature {
    optional string key = 1;
    optional string value = 2;
}

message UserSession {
    // This field stores creatives which have been watched by user.
    repeated uint64 delivered_creative_numeralization_value = 2;
}

message Banner {
    // Ad zone identifier.
    optional int64 ad_zone_id = 4;

    // This field is always 0 now.
    optional int32 ad_type = 12;

    // Available creative templates for this Ad zone.
    repeated int64 creative_template = 13;

    extensions 100 to max;
}

message Video {
    // The iqiyi-internal unique identifier of an ad zone.
    optional int64 ad_zone_id = 1;

    // Indicates whether the ad impression is linear or non-linear.
    // 1. Linear, example: pre-roll, mid-roll and post-roll.
    // 2. Non-linear, example: overlay, video link, pause, and tool bar.
    optional int32 linearity = 3;

    // The detail description of the type of an advertisement. The value and
    // corresponding meaning is as following: 1 pre-roll, 2 mid-roll, 3 post-roll,
    // 4 corner, 5 video link, 6 pause, 7 tool bar, 9 overlay.
    optional int32 ad_type = 13;

    // The minimum ad duration(in seconds) allowed on this video ad zone.
    optional int32 minduration = 4;

    // The maximum ad duration(in seconds) allowed on this video ad zone.
    optional int32 maxduration = 5;

    // Video bid response protocols.
    // 1 VAST 1.0
    // 2 VAST 2.0
    // 3 VAST 3.0
    // 4 VAST 1.0 Wrapper
    // 5 VAST 2.0 Wrapper
    // 6 VAST 3.0 Wrapper
    optional int32 protocol = 6;

    // Width of the player in pixels.
    optional int32 w = 7;

    // Height of the player in pixels.
    optional int32 h = 8;

    // A zero-based offset seconds from the start of a roll-type ad. The value
    // of this field is equal to M * 5 + N * 15, M = 0, 1 and N = 0, 1, 2, ...
    optional int32 startdelay = 9;

    // This field is meaningful only when this impression is linear. It indicates
    // whether "maxduration" is equal to the total duration this impression holds.
    // That's to say, the entire (pre/mid/post)-roll is available if it is true.
    optional bool is_entire_roll = 14 [default = false];

    // A zero-based offset seconds from the start of the video.
    optional int32 video_startdelay = 15;

    // Video orientation
    // 0 Landscape orientation.
    // 1 Portrait orientation.
    optional int32 video_orientation = 16;
}

message Impression {
    // The unique identifier of this impression within the context of the bid
    // request.
    optional string id = 1;

    // An impression is either banner or video, but not both.
    optional Banner banner = 2;
    optional Video video = 3;

    // The floor price list of this impression for all industries in
    // RMB(cent per CPM).
    repeated FloorPrice floor_price = 9;
    // Refer to this field to get the floor price if an industry is not found in
    // the "floor_price".
    optional int32 bidfloor = 4 [default = 0];

    // Campaign is a facility used by DSP to partition traffic.
    optional int32 campaign_id = 5;

    // The advertisements with these tags will be blocked on this impression.
    // Ad tag can be understood as the product type in an ad.
    repeated int32 blocked_ad_tag = 6;

    repeated int32 blocked_ad_attribute = 7;

    // This impression is a pmp one if this field is set to be true.
    optional bool is_pmp = 8 [default = false];

    // Note, this field works only when the impression can place extended ads.
    // 0 means that there is no extended ad.
    // 1 means that extended ads must be placed in front.
    // 2 means that extended ads must be placed in back.
    // 3 means that extended ads can be placed in front or in back.
    optional int32 extended_ads_position = 10 [default = 0];

    // Note, this field represents Beijing time in format of yyyymmdd.
    // For example, value of 20170509 should be interpreted as 2017-05-09.
    // This field indicates the planned impression date(today or future).
    // This field will be set only for non-real-time ads.
    // It means the bids will be cached to display in future.
    optional int32 impression_date = 11;

    // Note, this field works only when the impression can place skippable roll ads.
    // It indicates the max count of skippable roll ads.
    optional int32 max_skippable_roll_ads = 12;

    // Refer to this field to get the floor price of skippable roll if an
    // industry is not found in the "floor_price".
    // If this impression is not roll type, this field will not be set.
    optional int32 skippable_roll_bidfloor = 13;

    // Deprecated, refer to max_bids_allowed field.
    // The maximum number of bids allowed on this info stream impression.
    // Note, this field works only for info stream impression.
    optional int32 info_stream_max_bids_allowed = 15;

    // The creative with these orientations will be blocked on this impression.
    // 0 means no creatives will be blocked.
    // 1 means Landscape creative will be blocked.
    // 2 means Portrait creative will be blocked.
    // To be deprecated
    repeated int32 blocked_creative_orientation = 16;

    // Note, this field describes the budget type and it is a mask value.
    // Please refer to BidBudgetType enum to get the meaning of each mask bit.
    optional int32 budget_type = 17;

    // Note, this field describes the charge type and it is a mask value.
    // Please refer to BidChargeType enum to get the meaning of each mask bit.
    optional int32 charge_type = 18;

    // Is this impression support for playing mp4 creative directly.
    optional bool support_mp4_creative = 19 [default = false];

    // Maximum bids allowed for this impression.
    optional int32 max_bids_allowed = 20;

    // Is this impression support for playing fixed 15s roll.
    optional bool support_fixed_15s_roll = 21 [default = false];

    // Is this impression support for playing picture in roll ad.
    optional bool support_roll_play_picture = 22 [default = false];

    extensions 100 to max;
}

message Site {
    // The iqiyi-internal unique identifier of a site.
    optional int32 id = 1;

    // The iqiyi app's bundle id.
    optional string app_bundle_id = 2;

    optional Content content = 11;
}

message Content {
    optional string title = 3;

    // Original URL of the content, for buy-side contextualization or review.
    optional string url = 6;

    // The list of keywords describing the content.
    repeated string keyword = 9;

    // The duration of video content in seconds.
    optional int32 len = 16;

    // The iqiyi-internal unique identifier of an album.
    optional int64 album_id = 20;

    optional int64 channel_id = 22;

    repeated string region = 23;

    repeated string category = 24;

    optional string release_date = 25;

    repeated string starring = 26;

    repeated string video_quality = 27;

    repeated string tag = 28;

    repeated int64 video_tag = 29;

    optional string video_clip_id = 31;
}

message DeviceIdentity {
    optional string key = 1;
    optional string value = 2;
}

message Device {
    // Browser user agent string.
    optional string ua = 2;

    // IPv4 address closest to device.
    optional string ip = 3;

    // Geography information derived from IP address.
    optional Geo geo = 4;

    // Return the detected connection type for the device.
    // 0 Unknown
    // 1 Ethernet
    // 2 Wifi
    // 3 Cellular data - 2G
    // 4 Cellular data - 3G
    // 5 Cellular data - 4G
    // 6 Cellular data - 5G
    optional int32 connection_type = 15;

    // The iqiyi-internal unique identifier of a platform.
    optional int32 platform_id = 18;

    // The features supported by the iqiyi ua which the current bid request is
    // from.
    repeated int64 feature = 19;

    optional string android_id = 20;

    // The model for the device.
    // example: iphone, vivoX7
    optional string model = 21;

    // The operating system for the device.
    // example: ios, windows, android
    optional string os = 22;

    // The operating system version for the device.
    // example: 5.1.1
    optional string os_version = 23;

    // The version of the APP from which the current request comes. (eg. "7.9.1")
    optional string app_version = 24;

    // IPv6 address closest to device.
    optional string ipv6 = 25;

    optional string idfa = 26;

    optional string openudid = 27;

    optional string imei = 28;

    optional string mac = 29;

    optional string oaid = 30;

    // Device's screen height in pixel unit.
    optional int32 screen_height = 32;
    // Device's screen width in pixel unit.
    optional int32 screen_width = 33;

    repeated int64 installed_app = 34;

    // Such as oppo, huawei, xiaomi.
    // This field may be empty.
    optional string manufacturer = 35;

    optional string carrier_name = 36;

    optional string idfv = 37;

    optional string local_timezone = 38;

    // The operating system update time for the device.
    // It is the timestamp since 1970 in microsecond.
    optional int64 os_update_time = 39;

    // The startup time for the device.
    // It is the timestamp since 1970 in microsecond.
    optional int64 startup_time = 40;

    optional int32 cpu_num = 41;

    // The total disk space in byte.
    optional int64 disk_total = 42;

    // The total memory space in byte.
    optional int64 mem_total = 43;

    // The status value for app tracking authorization.
    // This field is valid only in iOS 14.0+ device.
    // -1 Not Supported
    // 0 Not Determined
    // 1 Restricted
    // 2 Denied
    // 3 Authorized
    optional int32 auth_status = 44;
    // device name
    optional string device_name = 45;
    // country code, e.g: GB
    optional string country_code = 46;
    // time zone offset secondes, e.g: 28800
    optional string time_zone_sec = 47;
    // the language used by the device, e.g: zh-Hans-CN
    optional string device_language = 48;
    // machine of device, e.g:D22AP
    optional string machine_of_device = 49;
    // device unique identification.
    // Key is identity name, e.g: AAID,
    // Value is identity value.
    repeated DeviceIdentity device_identity = 50;
    // Device boot mark.
    optional string boot_mark = 51;
    // Device update mark.
    optional string update_mark = 52;
    message Caid {
        optional string version = 1;
        optional string caid = 2;
    }
    message CaidInfo {
      repeated Caid caid = 1;
    }
    optional CaidInfo caid_info = 53;
}

message Geo {
    optional int32 country = 3;
    optional int32 metro = 5;
    optional int32 city = 6;
    optional double longitude = 7;
    optional double latitude = 8;
    optional string geohash = 9;
}

message ViewingHistory {
  optional string video_title = 1;
  repeated string video_tag = 2;
  repeated int64 multi_publish_channel_ids = 3;
}

message User {
    // The unique identifier of this user on the exchange.
    // For IOS, this is IDFA, UDID or MAC address.
    // For Android, this is IMEI, AndroidID or MAC address.
    optional string id = 1;

    //This field will be set true if the privacy of the current user should be protected.
    optional bool is_privacy_protected = 2 [default = false];

    repeated int32 dmp_id = 4;

    // Each UserFeature contains key and value fields.
    // For example,
    // UserFeature {
    //   key: "f1",
    //   value: "10"
    // }
    repeated UserFeature feature = 5;

    optional UserSession session = 6;

    // This field indicates viewing histories of the user ordered by date.
    // The latest record is at the beginning.
    repeated ViewingHistory viewing_history = 7;

    // This field stores user interest tag data and the callback url.
    // http://cb.adx.iqiyi.com/callback?tag_type=...&tag1=weight1-level1-macro1-macro2-...&tag2=...
    // Refer to the ADX document for DSP to get the value of each available macro.
    repeated string interest_tag = 8;

    // user type for adload handler.
    // 0-99 for jisu_user
    // 100-199 for iqiyi_user
    // -1 for other sites' user
    // details: ADDEV-20152
    optional int32 adload_user_type = 9;
}

message BidRequest {
    // Note, the value of this field is not unique.
    optional string id = 1;

    optional User user = 2;
    optional Site site = 3;
    optional Device device = 5;

    // The list of impression objects. Multiple impression auctions may be
    // specified in a single bid request. At least one impression is required
    // for a valid bid request.
    repeated Impression imp = 8;

    // If true, then this is a test request. Results will not be displayed to
    // users and DSP will not be billed for a response even if it wins the
    // auction. DSP should still do regular processing since the request may be
    // used to evaluate latencies or for other testing.
    optional bool is_test = 9 [default = false];

    // If true, then this request is intended to measure network latency. Please
    // return an empty BidResponse with only processing_time_ms set as quickly as
    // possible without executing any bidding logic.
    optional bool is_ping = 10 [default = false];

    // This field is ONLY used for debugging Baidu timeout issue.
    // NOTE: SHOULD DELETE THIS FIELD WHEN ISSUE IS RESOLVED.
    optional int64 request_timestamp = 11;
    // This field is ONLY used for debugging Baidu timeout issue.
    // NOTE: SHOULD DELETE THIS FIELD WHEN ISSUE IS RESOLVED.
    optional string request_cluster = 12;

    // Timeout milliseconds for this request.
    optional int32 timeout_ms = 13;

    extensions 100 to max;
}
