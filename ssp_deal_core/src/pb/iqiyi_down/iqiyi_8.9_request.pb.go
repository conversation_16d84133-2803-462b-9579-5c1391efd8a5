// This file is derived from the OpenRTB specification.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: iqiyi_8.9_request.proto

package iqiyi_down

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FloorPrice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Industry *int64 `protobuf:"varint,1,opt,name=industry" json:"industry,omitempty"`
	Price    *int32 `protobuf:"varint,2,opt,name=price" json:"price,omitempty"`
	// This field will be set just for roll type requests.
	SkippableRollPrice *int32 `protobuf:"varint,3,opt,name=skippable_roll_price,json=skippableRollPrice" json:"skippable_roll_price,omitempty"`
}

func (x *FloorPrice) Reset() {
	*x = FloorPrice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloorPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloorPrice) ProtoMessage() {}

func (x *FloorPrice) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloorPrice.ProtoReflect.Descriptor instead.
func (*FloorPrice) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{0}
}

func (x *FloorPrice) GetIndustry() int64 {
	if x != nil && x.Industry != nil {
		return *x.Industry
	}
	return 0
}

func (x *FloorPrice) GetPrice() int32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *FloorPrice) GetSkippableRollPrice() int32 {
	if x != nil && x.SkippableRollPrice != nil {
		return *x.SkippableRollPrice
	}
	return 0
}

type UserFeature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Value *string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *UserFeature) Reset() {
	*x = UserFeature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserFeature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserFeature) ProtoMessage() {}

func (x *UserFeature) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserFeature.ProtoReflect.Descriptor instead.
func (*UserFeature) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{1}
}

func (x *UserFeature) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *UserFeature) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type UserSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This field stores creatives which have been watched by user.
	DeliveredCreativeNumeralizationValue []uint64 `protobuf:"varint,2,rep,name=delivered_creative_numeralization_value,json=deliveredCreativeNumeralizationValue" json:"delivered_creative_numeralization_value,omitempty"`
}

func (x *UserSession) Reset() {
	*x = UserSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSession) ProtoMessage() {}

func (x *UserSession) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSession.ProtoReflect.Descriptor instead.
func (*UserSession) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{2}
}

func (x *UserSession) GetDeliveredCreativeNumeralizationValue() []uint64 {
	if x != nil {
		return x.DeliveredCreativeNumeralizationValue
	}
	return nil
}

type Banner struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Ad zone identifier.
	AdZoneId *int64 `protobuf:"varint,4,opt,name=ad_zone_id,json=adZoneId" json:"ad_zone_id,omitempty"`
	// This field is always 0 now.
	AdType *int32 `protobuf:"varint,12,opt,name=ad_type,json=adType" json:"ad_type,omitempty"`
	// Available creative templates for this Ad zone.
	CreativeTemplate []int64 `protobuf:"varint,13,rep,name=creative_template,json=creativeTemplate" json:"creative_template,omitempty"`
}

func (x *Banner) Reset() {
	*x = Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner) ProtoMessage() {}

func (x *Banner) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner.ProtoReflect.Descriptor instead.
func (*Banner) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{3}
}

func (x *Banner) GetAdZoneId() int64 {
	if x != nil && x.AdZoneId != nil {
		return *x.AdZoneId
	}
	return 0
}

func (x *Banner) GetAdType() int32 {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return 0
}

func (x *Banner) GetCreativeTemplate() []int64 {
	if x != nil {
		return x.CreativeTemplate
	}
	return nil
}

type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The iqiyi-internal unique identifier of an ad zone.
	AdZoneId *int64 `protobuf:"varint,1,opt,name=ad_zone_id,json=adZoneId" json:"ad_zone_id,omitempty"`
	// Indicates whether the ad impression is linear or non-linear.
	// 1. Linear, example: pre-roll, mid-roll and post-roll.
	// 2. Non-linear, example: overlay, video link, pause, and tool bar.
	Linearity *int32 `protobuf:"varint,3,opt,name=linearity" json:"linearity,omitempty"`
	// The detail description of the type of an advertisement. The value and
	// corresponding meaning is as following: 1 pre-roll, 2 mid-roll, 3 post-roll,
	// 4 corner, 5 video link, 6 pause, 7 tool bar, 9 overlay.
	AdType *int32 `protobuf:"varint,13,opt,name=ad_type,json=adType" json:"ad_type,omitempty"`
	// The minimum ad duration(in seconds) allowed on this video ad zone.
	Minduration *int32 `protobuf:"varint,4,opt,name=minduration" json:"minduration,omitempty"`
	// The maximum ad duration(in seconds) allowed on this video ad zone.
	Maxduration *int32 `protobuf:"varint,5,opt,name=maxduration" json:"maxduration,omitempty"`
	// Video bid response protocols.
	// 1 VAST 1.0
	// 2 VAST 2.0
	// 3 VAST 3.0
	// 4 VAST 1.0 Wrapper
	// 5 VAST 2.0 Wrapper
	// 6 VAST 3.0 Wrapper
	Protocol *int32 `protobuf:"varint,6,opt,name=protocol" json:"protocol,omitempty"`
	// Width of the player in pixels.
	W *int32 `protobuf:"varint,7,opt,name=w" json:"w,omitempty"`
	// Height of the player in pixels.
	H *int32 `protobuf:"varint,8,opt,name=h" json:"h,omitempty"`
	// A zero-based offset seconds from the start of a roll-type ad. The value
	// of this field is equal to M * 5 + N * 15, M = 0, 1 and N = 0, 1, 2, ...
	Startdelay *int32 `protobuf:"varint,9,opt,name=startdelay" json:"startdelay,omitempty"`
	// This field is meaningful only when this impression is linear. It indicates
	// whether "maxduration" is equal to the total duration this impression holds.
	// That's to say, the entire (pre/mid/post)-roll is available if it is true.
	IsEntireRoll *bool `protobuf:"varint,14,opt,name=is_entire_roll,json=isEntireRoll,def=0" json:"is_entire_roll,omitempty"`
	// A zero-based offset seconds from the start of the video.
	VideoStartdelay *int32 `protobuf:"varint,15,opt,name=video_startdelay,json=videoStartdelay" json:"video_startdelay,omitempty"`
	// Video orientation
	// 0 Landscape orientation.
	// 1 Portrait orientation.
	VideoOrientation *int32 `protobuf:"varint,16,opt,name=video_orientation,json=videoOrientation" json:"video_orientation,omitempty"`
}

// Default values for Video fields.
const (
	Default_Video_IsEntireRoll = bool(false)
)

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{4}
}

func (x *Video) GetAdZoneId() int64 {
	if x != nil && x.AdZoneId != nil {
		return *x.AdZoneId
	}
	return 0
}

func (x *Video) GetLinearity() int32 {
	if x != nil && x.Linearity != nil {
		return *x.Linearity
	}
	return 0
}

func (x *Video) GetAdType() int32 {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return 0
}

func (x *Video) GetMinduration() int32 {
	if x != nil && x.Minduration != nil {
		return *x.Minduration
	}
	return 0
}

func (x *Video) GetMaxduration() int32 {
	if x != nil && x.Maxduration != nil {
		return *x.Maxduration
	}
	return 0
}

func (x *Video) GetProtocol() int32 {
	if x != nil && x.Protocol != nil {
		return *x.Protocol
	}
	return 0
}

func (x *Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *Video) GetStartdelay() int32 {
	if x != nil && x.Startdelay != nil {
		return *x.Startdelay
	}
	return 0
}

func (x *Video) GetIsEntireRoll() bool {
	if x != nil && x.IsEntireRoll != nil {
		return *x.IsEntireRoll
	}
	return Default_Video_IsEntireRoll
}

func (x *Video) GetVideoStartdelay() int32 {
	if x != nil && x.VideoStartdelay != nil {
		return *x.VideoStartdelay
	}
	return 0
}

func (x *Video) GetVideoOrientation() int32 {
	if x != nil && x.VideoOrientation != nil {
		return *x.VideoOrientation
	}
	return 0
}

type Impression struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// The unique identifier of this impression within the context of the bid
	// request.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// An impression is either banner or video, but not both.
	Banner *Banner `protobuf:"bytes,2,opt,name=banner" json:"banner,omitempty"`
	Video  *Video  `protobuf:"bytes,3,opt,name=video" json:"video,omitempty"`
	// The floor price list of this impression for all industries in
	// RMB(cent per CPM).
	FloorPrice []*FloorPrice `protobuf:"bytes,9,rep,name=floor_price,json=floorPrice" json:"floor_price,omitempty"`
	// Refer to this field to get the floor price if an industry is not found in
	// the "floor_price".
	Bidfloor *int32 `protobuf:"varint,4,opt,name=bidfloor,def=0" json:"bidfloor,omitempty"`
	// Campaign is a facility used by DSP to partition traffic.
	CampaignId *int32 `protobuf:"varint,5,opt,name=campaign_id,json=campaignId" json:"campaign_id,omitempty"`
	// The advertisements with these tags will be blocked on this impression.
	// Ad tag can be understood as the product type in an ad.
	BlockedAdTag       []int32 `protobuf:"varint,6,rep,name=blocked_ad_tag,json=blockedAdTag" json:"blocked_ad_tag,omitempty"`
	BlockedAdAttribute []int32 `protobuf:"varint,7,rep,name=blocked_ad_attribute,json=blockedAdAttribute" json:"blocked_ad_attribute,omitempty"`
	// This impression is a pmp one if this field is set to be true.
	IsPmp *bool `protobuf:"varint,8,opt,name=is_pmp,json=isPmp,def=0" json:"is_pmp,omitempty"`
	// Note, this field works only when the impression can place extended ads.
	// 0 means that there is no extended ad.
	// 1 means that extended ads must be placed in front.
	// 2 means that extended ads must be placed in back.
	// 3 means that extended ads can be placed in front or in back.
	ExtendedAdsPosition *int32 `protobuf:"varint,10,opt,name=extended_ads_position,json=extendedAdsPosition,def=0" json:"extended_ads_position,omitempty"`
	// Note, this field represents Beijing time in format of yyyymmdd.
	// For example, value of 20170509 should be interpreted as 2017-05-09.
	// This field indicates the planned impression date(today or future).
	// This field will be set only for non-real-time ads.
	// It means the bids will be cached to display in future.
	ImpressionDate *int32 `protobuf:"varint,11,opt,name=impression_date,json=impressionDate" json:"impression_date,omitempty"`
	// Note, this field works only when the impression can place skippable roll ads.
	// It indicates the max count of skippable roll ads.
	MaxSkippableRollAds *int32 `protobuf:"varint,12,opt,name=max_skippable_roll_ads,json=maxSkippableRollAds" json:"max_skippable_roll_ads,omitempty"`
	// Refer to this field to get the floor price of skippable roll if an
	// industry is not found in the "floor_price".
	// If this impression is not roll type, this field will not be set.
	SkippableRollBidfloor *int32 `protobuf:"varint,13,opt,name=skippable_roll_bidfloor,json=skippableRollBidfloor" json:"skippable_roll_bidfloor,omitempty"`
	// Deprecated, refer to max_bids_allowed field.
	// The maximum number of bids allowed on this info stream impression.
	// Note, this field works only for info stream impression.
	InfoStreamMaxBidsAllowed *int32 `protobuf:"varint,15,opt,name=info_stream_max_bids_allowed,json=infoStreamMaxBidsAllowed" json:"info_stream_max_bids_allowed,omitempty"`
	// The creative with these orientations will be blocked on this impression.
	// 0 means no creatives will be blocked.
	// 1 means Landscape creative will be blocked.
	// 2 means Portrait creative will be blocked.
	// To be deprecated
	BlockedCreativeOrientation []int32 `protobuf:"varint,16,rep,name=blocked_creative_orientation,json=blockedCreativeOrientation" json:"blocked_creative_orientation,omitempty"`
	// Note, this field describes the budget type and it is a mask value.
	// Please refer to BidBudgetType enum to get the meaning of each mask bit.
	BudgetType *int32 `protobuf:"varint,17,opt,name=budget_type,json=budgetType" json:"budget_type,omitempty"`
	// Note, this field describes the charge type and it is a mask value.
	// Please refer to BidChargeType enum to get the meaning of each mask bit.
	ChargeType *int32 `protobuf:"varint,18,opt,name=charge_type,json=chargeType" json:"charge_type,omitempty"`
	// Is this impression support for playing mp4 creative directly.
	SupportMp4Creative *bool `protobuf:"varint,19,opt,name=support_mp4_creative,json=supportMp4Creative,def=0" json:"support_mp4_creative,omitempty"`
	// Maximum bids allowed for this impression.
	MaxBidsAllowed *int32 `protobuf:"varint,20,opt,name=max_bids_allowed,json=maxBidsAllowed" json:"max_bids_allowed,omitempty"`
	// Is this impression support for playing fixed 15s roll.
	SupportFixed_15SRoll *bool `protobuf:"varint,21,opt,name=support_fixed_15s_roll,json=supportFixed15sRoll,def=0" json:"support_fixed_15s_roll,omitempty"`
	// Is this impression support for playing picture in roll ad.
	SupportRollPlayPicture *bool `protobuf:"varint,22,opt,name=support_roll_play_picture,json=supportRollPlayPicture,def=0" json:"support_roll_play_picture,omitempty"`
}

// Default values for Impression fields.
const (
	Default_Impression_Bidfloor               = int32(0)
	Default_Impression_IsPmp                  = bool(false)
	Default_Impression_ExtendedAdsPosition    = int32(0)
	Default_Impression_SupportMp4Creative     = bool(false)
	Default_Impression_SupportFixed_15SRoll   = bool(false)
	Default_Impression_SupportRollPlayPicture = bool(false)
)

func (x *Impression) Reset() {
	*x = Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Impression) ProtoMessage() {}

func (x *Impression) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Impression.ProtoReflect.Descriptor instead.
func (*Impression) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{5}
}

func (x *Impression) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Impression) GetBanner() *Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *Impression) GetVideo() *Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Impression) GetFloorPrice() []*FloorPrice {
	if x != nil {
		return x.FloorPrice
	}
	return nil
}

func (x *Impression) GetBidfloor() int32 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return Default_Impression_Bidfloor
}

func (x *Impression) GetCampaignId() int32 {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return 0
}

func (x *Impression) GetBlockedAdTag() []int32 {
	if x != nil {
		return x.BlockedAdTag
	}
	return nil
}

func (x *Impression) GetBlockedAdAttribute() []int32 {
	if x != nil {
		return x.BlockedAdAttribute
	}
	return nil
}

func (x *Impression) GetIsPmp() bool {
	if x != nil && x.IsPmp != nil {
		return *x.IsPmp
	}
	return Default_Impression_IsPmp
}

func (x *Impression) GetExtendedAdsPosition() int32 {
	if x != nil && x.ExtendedAdsPosition != nil {
		return *x.ExtendedAdsPosition
	}
	return Default_Impression_ExtendedAdsPosition
}

func (x *Impression) GetImpressionDate() int32 {
	if x != nil && x.ImpressionDate != nil {
		return *x.ImpressionDate
	}
	return 0
}

func (x *Impression) GetMaxSkippableRollAds() int32 {
	if x != nil && x.MaxSkippableRollAds != nil {
		return *x.MaxSkippableRollAds
	}
	return 0
}

func (x *Impression) GetSkippableRollBidfloor() int32 {
	if x != nil && x.SkippableRollBidfloor != nil {
		return *x.SkippableRollBidfloor
	}
	return 0
}

func (x *Impression) GetInfoStreamMaxBidsAllowed() int32 {
	if x != nil && x.InfoStreamMaxBidsAllowed != nil {
		return *x.InfoStreamMaxBidsAllowed
	}
	return 0
}

func (x *Impression) GetBlockedCreativeOrientation() []int32 {
	if x != nil {
		return x.BlockedCreativeOrientation
	}
	return nil
}

func (x *Impression) GetBudgetType() int32 {
	if x != nil && x.BudgetType != nil {
		return *x.BudgetType
	}
	return 0
}

func (x *Impression) GetChargeType() int32 {
	if x != nil && x.ChargeType != nil {
		return *x.ChargeType
	}
	return 0
}

func (x *Impression) GetSupportMp4Creative() bool {
	if x != nil && x.SupportMp4Creative != nil {
		return *x.SupportMp4Creative
	}
	return Default_Impression_SupportMp4Creative
}

func (x *Impression) GetMaxBidsAllowed() int32 {
	if x != nil && x.MaxBidsAllowed != nil {
		return *x.MaxBidsAllowed
	}
	return 0
}

func (x *Impression) GetSupportFixed_15SRoll() bool {
	if x != nil && x.SupportFixed_15SRoll != nil {
		return *x.SupportFixed_15SRoll
	}
	return Default_Impression_SupportFixed_15SRoll
}

func (x *Impression) GetSupportRollPlayPicture() bool {
	if x != nil && x.SupportRollPlayPicture != nil {
		return *x.SupportRollPlayPicture
	}
	return Default_Impression_SupportRollPlayPicture
}

type Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The iqiyi-internal unique identifier of a site.
	Id *int32 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	// The iqiyi app's bundle id.
	AppBundleId *string  `protobuf:"bytes,2,opt,name=app_bundle_id,json=appBundleId" json:"app_bundle_id,omitempty"`
	Content     *Content `protobuf:"bytes,11,opt,name=content" json:"content,omitempty"`
}

func (x *Site) Reset() {
	*x = Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Site) ProtoMessage() {}

func (x *Site) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Site.ProtoReflect.Descriptor instead.
func (*Site) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{6}
}

func (x *Site) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Site) GetAppBundleId() string {
	if x != nil && x.AppBundleId != nil {
		return *x.AppBundleId
	}
	return ""
}

func (x *Site) GetContent() *Content {
	if x != nil {
		return x.Content
	}
	return nil
}

type Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *string `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`
	// Original URL of the content, for buy-side contextualization or review.
	Url *string `protobuf:"bytes,6,opt,name=url" json:"url,omitempty"`
	// The list of keywords describing the content.
	Keyword []string `protobuf:"bytes,9,rep,name=keyword" json:"keyword,omitempty"`
	// The duration of video content in seconds.
	Len *int32 `protobuf:"varint,16,opt,name=len" json:"len,omitempty"`
	// The iqiyi-internal unique identifier of an album.
	AlbumId      *int64   `protobuf:"varint,20,opt,name=album_id,json=albumId" json:"album_id,omitempty"`
	ChannelId    *int64   `protobuf:"varint,22,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`
	Region       []string `protobuf:"bytes,23,rep,name=region" json:"region,omitempty"`
	Category     []string `protobuf:"bytes,24,rep,name=category" json:"category,omitempty"`
	ReleaseDate  *string  `protobuf:"bytes,25,opt,name=release_date,json=releaseDate" json:"release_date,omitempty"`
	Starring     []string `protobuf:"bytes,26,rep,name=starring" json:"starring,omitempty"`
	VideoQuality []string `protobuf:"bytes,27,rep,name=video_quality,json=videoQuality" json:"video_quality,omitempty"`
	Tag          []string `protobuf:"bytes,28,rep,name=tag" json:"tag,omitempty"`
	VideoTag     []int64  `protobuf:"varint,29,rep,name=video_tag,json=videoTag" json:"video_tag,omitempty"`
	VideoClipId  *string  `protobuf:"bytes,31,opt,name=video_clip_id,json=videoClipId" json:"video_clip_id,omitempty"`
}

func (x *Content) Reset() {
	*x = Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{7}
}

func (x *Content) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Content) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *Content) GetKeyword() []string {
	if x != nil {
		return x.Keyword
	}
	return nil
}

func (x *Content) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *Content) GetAlbumId() int64 {
	if x != nil && x.AlbumId != nil {
		return *x.AlbumId
	}
	return 0
}

func (x *Content) GetChannelId() int64 {
	if x != nil && x.ChannelId != nil {
		return *x.ChannelId
	}
	return 0
}

func (x *Content) GetRegion() []string {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *Content) GetCategory() []string {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Content) GetReleaseDate() string {
	if x != nil && x.ReleaseDate != nil {
		return *x.ReleaseDate
	}
	return ""
}

func (x *Content) GetStarring() []string {
	if x != nil {
		return x.Starring
	}
	return nil
}

func (x *Content) GetVideoQuality() []string {
	if x != nil {
		return x.VideoQuality
	}
	return nil
}

func (x *Content) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *Content) GetVideoTag() []int64 {
	if x != nil {
		return x.VideoTag
	}
	return nil
}

func (x *Content) GetVideoClipId() string {
	if x != nil && x.VideoClipId != nil {
		return *x.VideoClipId
	}
	return ""
}

type DeviceIdentity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Value *string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *DeviceIdentity) Reset() {
	*x = DeviceIdentity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceIdentity) ProtoMessage() {}

func (x *DeviceIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceIdentity.ProtoReflect.Descriptor instead.
func (*DeviceIdentity) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{8}
}

func (x *DeviceIdentity) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *DeviceIdentity) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Browser user agent string.
	Ua *string `protobuf:"bytes,2,opt,name=ua" json:"ua,omitempty"`
	// IPv4 address closest to device.
	Ip *string `protobuf:"bytes,3,opt,name=ip" json:"ip,omitempty"`
	// Geography information derived from IP address.
	Geo *Geo `protobuf:"bytes,4,opt,name=geo" json:"geo,omitempty"`
	// Return the detected connection type for the device.
	// 0 Unknown
	// 1 Ethernet
	// 2 Wifi
	// 3 Cellular data - 2G
	// 4 Cellular data - 3G
	// 5 Cellular data - 4G
	// 6 Cellular data - 5G
	ConnectionType *int32 `protobuf:"varint,15,opt,name=connection_type,json=connectionType" json:"connection_type,omitempty"`
	// The iqiyi-internal unique identifier of a platform.
	PlatformId *int32 `protobuf:"varint,18,opt,name=platform_id,json=platformId" json:"platform_id,omitempty"`
	// The features supported by the iqiyi ua which the current bid request is
	// from.
	Feature   []int64 `protobuf:"varint,19,rep,name=feature" json:"feature,omitempty"`
	AndroidId *string `protobuf:"bytes,20,opt,name=android_id,json=androidId" json:"android_id,omitempty"`
	// The model for the device.
	// example: iphone, vivoX7
	Model *string `protobuf:"bytes,21,opt,name=model" json:"model,omitempty"`
	// The operating system for the device.
	// example: ios, windows, android
	Os *string `protobuf:"bytes,22,opt,name=os" json:"os,omitempty"`
	// The operating system version for the device.
	// example: 5.1.1
	OsVersion *string `protobuf:"bytes,23,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`
	// The version of the APP from which the current request comes. (eg. "7.9.1")
	AppVersion *string `protobuf:"bytes,24,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	// IPv6 address closest to device.
	Ipv6     *string `protobuf:"bytes,25,opt,name=ipv6" json:"ipv6,omitempty"`
	Idfa     *string `protobuf:"bytes,26,opt,name=idfa" json:"idfa,omitempty"`
	Openudid *string `protobuf:"bytes,27,opt,name=openudid" json:"openudid,omitempty"`
	Imei     *string `protobuf:"bytes,28,opt,name=imei" json:"imei,omitempty"`
	Mac      *string `protobuf:"bytes,29,opt,name=mac" json:"mac,omitempty"`
	Oaid     *string `protobuf:"bytes,30,opt,name=oaid" json:"oaid,omitempty"`
	// Device's screen height in pixel unit.
	ScreenHeight *int32 `protobuf:"varint,32,opt,name=screen_height,json=screenHeight" json:"screen_height,omitempty"`
	// Device's screen width in pixel unit.
	ScreenWidth  *int32  `protobuf:"varint,33,opt,name=screen_width,json=screenWidth" json:"screen_width,omitempty"`
	InstalledApp []int64 `protobuf:"varint,34,rep,name=installed_app,json=installedApp" json:"installed_app,omitempty"`
	// Such as oppo, huawei, xiaomi.
	// This field may be empty.
	Manufacturer  *string `protobuf:"bytes,35,opt,name=manufacturer" json:"manufacturer,omitempty"`
	CarrierName   *string `protobuf:"bytes,36,opt,name=carrier_name,json=carrierName" json:"carrier_name,omitempty"`
	Idfv          *string `protobuf:"bytes,37,opt,name=idfv" json:"idfv,omitempty"`
	LocalTimezone *string `protobuf:"bytes,38,opt,name=local_timezone,json=localTimezone" json:"local_timezone,omitempty"`
	// The operating system update time for the device.
	// It is the timestamp since 1970 in microsecond.
	OsUpdateTime *int64 `protobuf:"varint,39,opt,name=os_update_time,json=osUpdateTime" json:"os_update_time,omitempty"`
	// The startup time for the device.
	// It is the timestamp since 1970 in microsecond.
	StartupTime *int64 `protobuf:"varint,40,opt,name=startup_time,json=startupTime" json:"startup_time,omitempty"`
	CpuNum      *int32 `protobuf:"varint,41,opt,name=cpu_num,json=cpuNum" json:"cpu_num,omitempty"`
	// The total disk space in byte.
	DiskTotal *int64 `protobuf:"varint,42,opt,name=disk_total,json=diskTotal" json:"disk_total,omitempty"`
	// The total memory space in byte.
	MemTotal *int64 `protobuf:"varint,43,opt,name=mem_total,json=memTotal" json:"mem_total,omitempty"`
	// The status value for app tracking authorization.
	// This field is valid only in iOS 14.0+ device.
	// -1 Not Supported
	// 0 Not Determined
	// 1 Restricted
	// 2 Denied
	// 3 Authorized
	AuthStatus *int32 `protobuf:"varint,44,opt,name=auth_status,json=authStatus" json:"auth_status,omitempty"`
	// device name
	DeviceName *string `protobuf:"bytes,45,opt,name=device_name,json=deviceName" json:"device_name,omitempty"`
	// country code, e.g: GB
	CountryCode *string `protobuf:"bytes,46,opt,name=country_code,json=countryCode" json:"country_code,omitempty"`
	// time zone offset secondes, e.g: 28800
	TimeZoneSec *string `protobuf:"bytes,47,opt,name=time_zone_sec,json=timeZoneSec" json:"time_zone_sec,omitempty"`
	// the language used by the device, e.g: zh-Hans-CN
	DeviceLanguage *string `protobuf:"bytes,48,opt,name=device_language,json=deviceLanguage" json:"device_language,omitempty"`
	// machine of device, e.g:D22AP
	MachineOfDevice *string `protobuf:"bytes,49,opt,name=machine_of_device,json=machineOfDevice" json:"machine_of_device,omitempty"`
	// device unique identification.
	// Key is identity name, e.g: AAID,
	// Value is identity value.
	DeviceIdentity []*DeviceIdentity `protobuf:"bytes,50,rep,name=device_identity,json=deviceIdentity" json:"device_identity,omitempty"`
	// Device boot mark.
	BootMark *string `protobuf:"bytes,51,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`
	// Device update mark.
	UpdateMark *string          `protobuf:"bytes,52,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`
	CaidInfo   *Device_CaidInfo `protobuf:"bytes,53,opt,name=caid_info,json=caidInfo" json:"caid_info,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{9}
}

func (x *Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *Device) GetGeo() *Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Device) GetConnectionType() int32 {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return 0
}

func (x *Device) GetPlatformId() int32 {
	if x != nil && x.PlatformId != nil {
		return *x.PlatformId
	}
	return 0
}

func (x *Device) GetFeature() []int64 {
	if x != nil {
		return x.Feature
	}
	return nil
}

func (x *Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *Device) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *Device) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *Device) GetOpenudid() string {
	if x != nil && x.Openudid != nil {
		return *x.Openudid
	}
	return ""
}

func (x *Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *Device) GetScreenHeight() int32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *Device) GetScreenWidth() int32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *Device) GetInstalledApp() []int64 {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *Device) GetManufacturer() string {
	if x != nil && x.Manufacturer != nil {
		return *x.Manufacturer
	}
	return ""
}

func (x *Device) GetCarrierName() string {
	if x != nil && x.CarrierName != nil {
		return *x.CarrierName
	}
	return ""
}

func (x *Device) GetIdfv() string {
	if x != nil && x.Idfv != nil {
		return *x.Idfv
	}
	return ""
}

func (x *Device) GetLocalTimezone() string {
	if x != nil && x.LocalTimezone != nil {
		return *x.LocalTimezone
	}
	return ""
}

func (x *Device) GetOsUpdateTime() int64 {
	if x != nil && x.OsUpdateTime != nil {
		return *x.OsUpdateTime
	}
	return 0
}

func (x *Device) GetStartupTime() int64 {
	if x != nil && x.StartupTime != nil {
		return *x.StartupTime
	}
	return 0
}

func (x *Device) GetCpuNum() int32 {
	if x != nil && x.CpuNum != nil {
		return *x.CpuNum
	}
	return 0
}

func (x *Device) GetDiskTotal() int64 {
	if x != nil && x.DiskTotal != nil {
		return *x.DiskTotal
	}
	return 0
}

func (x *Device) GetMemTotal() int64 {
	if x != nil && x.MemTotal != nil {
		return *x.MemTotal
	}
	return 0
}

func (x *Device) GetAuthStatus() int32 {
	if x != nil && x.AuthStatus != nil {
		return *x.AuthStatus
	}
	return 0
}

func (x *Device) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *Device) GetCountryCode() string {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return ""
}

func (x *Device) GetTimeZoneSec() string {
	if x != nil && x.TimeZoneSec != nil {
		return *x.TimeZoneSec
	}
	return ""
}

func (x *Device) GetDeviceLanguage() string {
	if x != nil && x.DeviceLanguage != nil {
		return *x.DeviceLanguage
	}
	return ""
}

func (x *Device) GetMachineOfDevice() string {
	if x != nil && x.MachineOfDevice != nil {
		return *x.MachineOfDevice
	}
	return ""
}

func (x *Device) GetDeviceIdentity() []*DeviceIdentity {
	if x != nil {
		return x.DeviceIdentity
	}
	return nil
}

func (x *Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *Device) GetCaidInfo() *Device_CaidInfo {
	if x != nil {
		return x.CaidInfo
	}
	return nil
}

type Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Country   *int32   `protobuf:"varint,3,opt,name=country" json:"country,omitempty"`
	Metro     *int32   `protobuf:"varint,5,opt,name=metro" json:"metro,omitempty"`
	City      *int32   `protobuf:"varint,6,opt,name=city" json:"city,omitempty"`
	Longitude *float64 `protobuf:"fixed64,7,opt,name=longitude" json:"longitude,omitempty"`
	Latitude  *float64 `protobuf:"fixed64,8,opt,name=latitude" json:"latitude,omitempty"`
	Geohash   *string  `protobuf:"bytes,9,opt,name=geohash" json:"geohash,omitempty"`
}

func (x *Geo) Reset() {
	*x = Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Geo) ProtoMessage() {}

func (x *Geo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Geo.ProtoReflect.Descriptor instead.
func (*Geo) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{10}
}

func (x *Geo) GetCountry() int32 {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return 0
}

func (x *Geo) GetMetro() int32 {
	if x != nil && x.Metro != nil {
		return *x.Metro
	}
	return 0
}

func (x *Geo) GetCity() int32 {
	if x != nil && x.City != nil {
		return *x.City
	}
	return 0
}

func (x *Geo) GetLongitude() float64 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

func (x *Geo) GetLatitude() float64 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *Geo) GetGeohash() string {
	if x != nil && x.Geohash != nil {
		return *x.Geohash
	}
	return ""
}

type ViewingHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoTitle             *string  `protobuf:"bytes,1,opt,name=video_title,json=videoTitle" json:"video_title,omitempty"`
	VideoTag               []string `protobuf:"bytes,2,rep,name=video_tag,json=videoTag" json:"video_tag,omitempty"`
	MultiPublishChannelIds []int64  `protobuf:"varint,3,rep,name=multi_publish_channel_ids,json=multiPublishChannelIds" json:"multi_publish_channel_ids,omitempty"`
}

func (x *ViewingHistory) Reset() {
	*x = ViewingHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViewingHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewingHistory) ProtoMessage() {}

func (x *ViewingHistory) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewingHistory.ProtoReflect.Descriptor instead.
func (*ViewingHistory) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{11}
}

func (x *ViewingHistory) GetVideoTitle() string {
	if x != nil && x.VideoTitle != nil {
		return *x.VideoTitle
	}
	return ""
}

func (x *ViewingHistory) GetVideoTag() []string {
	if x != nil {
		return x.VideoTag
	}
	return nil
}

func (x *ViewingHistory) GetMultiPublishChannelIds() []int64 {
	if x != nil {
		return x.MultiPublishChannelIds
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of this user on the exchange.
	// For IOS, this is IDFA, UDID or MAC address.
	// For Android, this is IMEI, AndroidID or MAC address.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// This field will be set true if the privacy of the current user should be protected.
	IsPrivacyProtected *bool   `protobuf:"varint,2,opt,name=is_privacy_protected,json=isPrivacyProtected,def=0" json:"is_privacy_protected,omitempty"`
	DmpId              []int32 `protobuf:"varint,4,rep,name=dmp_id,json=dmpId" json:"dmp_id,omitempty"`
	// Each UserFeature contains key and value fields.
	// For example,
	//
	//	UserFeature {
	//	  key: "f1",
	//	  value: "10"
	//	}
	Feature []*UserFeature `protobuf:"bytes,5,rep,name=feature" json:"feature,omitempty"`
	Session *UserSession   `protobuf:"bytes,6,opt,name=session" json:"session,omitempty"`
	// This field indicates viewing histories of the user ordered by date.
	// The latest record is at the beginning.
	ViewingHistory []*ViewingHistory `protobuf:"bytes,7,rep,name=viewing_history,json=viewingHistory" json:"viewing_history,omitempty"`
	// This field stores user interest tag data and the callback url.
	// http://cb.adx.iqiyi.com/callback?tag_type=...&tag1=weight1-level1-macro1-macro2-...&tag2=...
	// Refer to the ADX document for DSP to get the value of each available macro.
	InterestTag []string `protobuf:"bytes,8,rep,name=interest_tag,json=interestTag" json:"interest_tag,omitempty"`
	// user type for adload handler.
	// 0-99 for jisu_user
	// 100-199 for iqiyi_user
	// -1 for other sites' user
	// details: ADDEV-20152
	AdloadUserType *int32 `protobuf:"varint,9,opt,name=adload_user_type,json=adloadUserType" json:"adload_user_type,omitempty"`
}

// Default values for User fields.
const (
	Default_User_IsPrivacyProtected = bool(false)
)

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{12}
}

func (x *User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *User) GetIsPrivacyProtected() bool {
	if x != nil && x.IsPrivacyProtected != nil {
		return *x.IsPrivacyProtected
	}
	return Default_User_IsPrivacyProtected
}

func (x *User) GetDmpId() []int32 {
	if x != nil {
		return x.DmpId
	}
	return nil
}

func (x *User) GetFeature() []*UserFeature {
	if x != nil {
		return x.Feature
	}
	return nil
}

func (x *User) GetSession() *UserSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *User) GetViewingHistory() []*ViewingHistory {
	if x != nil {
		return x.ViewingHistory
	}
	return nil
}

func (x *User) GetInterestTag() []string {
	if x != nil {
		return x.InterestTag
	}
	return nil
}

func (x *User) GetAdloadUserType() int32 {
	if x != nil && x.AdloadUserType != nil {
		return *x.AdloadUserType
	}
	return 0
}

type BidRequest struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Note, the value of this field is not unique.
	Id     *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	User   *User   `protobuf:"bytes,2,opt,name=user" json:"user,omitempty"`
	Site   *Site   `protobuf:"bytes,3,opt,name=site" json:"site,omitempty"`
	Device *Device `protobuf:"bytes,5,opt,name=device" json:"device,omitempty"`
	// The list of impression objects. Multiple impression auctions may be
	// specified in a single bid request. At least one impression is required
	// for a valid bid request.
	Imp []*Impression `protobuf:"bytes,8,rep,name=imp" json:"imp,omitempty"`
	// If true, then this is a test request. Results will not be displayed to
	// users and DSP will not be billed for a response even if it wins the
	// auction. DSP should still do regular processing since the request may be
	// used to evaluate latencies or for other testing.
	IsTest *bool `protobuf:"varint,9,opt,name=is_test,json=isTest,def=0" json:"is_test,omitempty"`
	// If true, then this request is intended to measure network latency. Please
	// return an empty BidResponse with only processing_time_ms set as quickly as
	// possible without executing any bidding logic.
	IsPing *bool `protobuf:"varint,10,opt,name=is_ping,json=isPing,def=0" json:"is_ping,omitempty"`
	// This field is ONLY used for debugging Baidu timeout issue.
	// NOTE: SHOULD DELETE THIS FIELD WHEN ISSUE IS RESOLVED.
	RequestTimestamp *int64 `protobuf:"varint,11,opt,name=request_timestamp,json=requestTimestamp" json:"request_timestamp,omitempty"`
	// This field is ONLY used for debugging Baidu timeout issue.
	// NOTE: SHOULD DELETE THIS FIELD WHEN ISSUE IS RESOLVED.
	RequestCluster *string `protobuf:"bytes,12,opt,name=request_cluster,json=requestCluster" json:"request_cluster,omitempty"`
	// Timeout milliseconds for this request.
	TimeoutMs *int32 `protobuf:"varint,13,opt,name=timeout_ms,json=timeoutMs" json:"timeout_ms,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_IsTest = bool(false)
	Default_BidRequest_IsPing = bool(false)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{13}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetSite() *Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetImp() []*Impression {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetIsTest() bool {
	if x != nil && x.IsTest != nil {
		return *x.IsTest
	}
	return Default_BidRequest_IsTest
}

func (x *BidRequest) GetIsPing() bool {
	if x != nil && x.IsPing != nil {
		return *x.IsPing
	}
	return Default_BidRequest_IsPing
}

func (x *BidRequest) GetRequestTimestamp() int64 {
	if x != nil && x.RequestTimestamp != nil {
		return *x.RequestTimestamp
	}
	return 0
}

func (x *BidRequest) GetRequestCluster() string {
	if x != nil && x.RequestCluster != nil {
		return *x.RequestCluster
	}
	return ""
}

func (x *BidRequest) GetTimeoutMs() int32 {
	if x != nil && x.TimeoutMs != nil {
		return *x.TimeoutMs
	}
	return 0
}

type Device_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version *string `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	Caid    *string `protobuf:"bytes,2,opt,name=caid" json:"caid,omitempty"`
}

func (x *Device_Caid) Reset() {
	*x = Device_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device_Caid) ProtoMessage() {}

func (x *Device_Caid) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device_Caid.ProtoReflect.Descriptor instead.
func (*Device_Caid) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{9, 0}
}

func (x *Device_Caid) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *Device_Caid) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

type Device_CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid []*Device_Caid `protobuf:"bytes,1,rep,name=caid" json:"caid,omitempty"`
}

func (x *Device_CaidInfo) Reset() {
	*x = Device_CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_request_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device_CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device_CaidInfo) ProtoMessage() {}

func (x *Device_CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_request_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device_CaidInfo.ProtoReflect.Descriptor instead.
func (*Device_CaidInfo) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_request_proto_rawDescGZIP(), []int{9, 1}
}

func (x *Device_CaidInfo) GetCaid() []*Device_Caid {
	if x != nil {
		return x.Caid
	}
	return nil
}

var File_iqiyi_8_9_request_proto protoreflect.FileDescriptor

var file_iqiyi_8_9_request_proto_rawDesc = []byte{
	0x0a, 0x17, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x38, 0x2e, 0x39, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x71, 0x69, 0x79, 0x69,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x22, 0x70, 0x0a, 0x0a, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f,
	0x6c, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x35, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x64,
	0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a,
	0x27, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x24,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x76, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x0a, 0x61, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x61, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2a, 0x08, 0x08, 0x64, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0xfd, 0x02, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x64, 0x5f, 0x7a, 0x6f, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x64, 0x5a, 0x6f,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d,
	0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x74, 0x69, 0x72, 0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x3a,
	0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x74, 0x69, 0x72, 0x65,
	0x52, 0x6f, 0x6c, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12,
	0x2b, 0x0a, 0x11, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdc, 0x07, 0x0a,
	0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69, 0x71,
	0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x37, 0x0a, 0x0b, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x2e, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x0a, 0x66,
	0x6c, 0x6f, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x08, 0x62, 0x69, 0x64,
	0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x08,
	0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x64, 0x54, 0x61, 0x67, 0x12,
	0x30, 0x0a, 0x14, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05, 0x52, 0x12, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x64, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x12, 0x1c, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x70, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05, 0x69, 0x73, 0x50, 0x6d, 0x70, 0x12,
	0x35, 0x0a, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x73, 0x5f,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01,
	0x30, 0x52, 0x13, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x64, 0x73, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x33, 0x0a, 0x16, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x61, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x6d, 0x61, 0x78, 0x53, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x6c,
	0x6c, 0x41, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x5f, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x6f, 0x6c, 0x6c, 0x42, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x1c,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x6d, 0x61, 0x78, 0x5f,
	0x62, 0x69, 0x64, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x18, 0x69, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x4d, 0x61,
	0x78, 0x42, 0x69, 0x64, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1c,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x1a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x37, 0x0a, 0x14, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x70, 0x34, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x12, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x70,
	0x34, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x78,
	0x5f, 0x62, 0x69, 0x64, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x42, 0x69, 0x64, 0x73, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x16, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x5f, 0x31, 0x35, 0x73, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x13, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x31, 0x35, 0x73, 0x52, 0x6f, 0x6c, 0x6c, 0x12,
	0x40, 0x0a, 0x19, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x5f,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x16, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x6f, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x79, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x2a, 0x08, 0x08, 0x64, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0x69, 0x0a, 0x04, 0x53,
	0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x82, 0x03, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6c, 0x62, 0x75, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x6c, 0x62, 0x75, 0x6d, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x18, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18,
	0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x63, 0x6c, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6c, 0x69, 0x70, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x0e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xc9, 0x0a, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x21, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03,
	0x67, 0x65, 0x6f, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x13, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x70, 0x76, 0x36, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x64, 0x66, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x6d, 0x65, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x18, 0x22, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x75, 0x66,
	0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x66, 0x76, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x73, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x28, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x70, 0x75, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x64,
	0x69, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x64, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65,
	0x6d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d,
	0x65, 0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x75,
	0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x2f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x53, 0x65, 0x63,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x31,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4f, 0x66, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x32, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f,
	0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x71,
	0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x1a, 0x34, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x1a, 0x37, 0x0a, 0x08, 0x43, 0x61, 0x69, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x52, 0x04, 0x63, 0x61, 0x69,
	0x64, 0x22, 0x9d, 0x01, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x74, 0x72, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6d, 0x65, 0x74, 0x72, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x65, 0x6f, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x65, 0x6f, 0x68, 0x61, 0x73,
	0x68, 0x22, 0x89, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x65, 0x77, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x54,
	0x61, 0x67, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x16, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x73, 0x22, 0xde, 0x02,
	0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x12, 0x69, 0x73, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x64, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x05, 0x64, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x71, 0x69,
	0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0f,
	0x76, 0x69, 0x65, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x0e, 0x76, 0x69, 0x65, 0x77, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x54, 0x61, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x64, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x61, 0x64, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xfd,
	0x02, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x71,
	0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x53,
	0x69, 0x74, 0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x69, 0x71, 0x69, 0x79,
	0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e,
	0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12,
	0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73, 0x54, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x67, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x5f, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x4d, 0x73, 0x2a, 0x08, 0x08, 0x64, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x42, 0x18,
	0x5a, 0x16, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x71,
	0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
}

var (
	file_iqiyi_8_9_request_proto_rawDescOnce sync.Once
	file_iqiyi_8_9_request_proto_rawDescData = file_iqiyi_8_9_request_proto_rawDesc
)

func file_iqiyi_8_9_request_proto_rawDescGZIP() []byte {
	file_iqiyi_8_9_request_proto_rawDescOnce.Do(func() {
		file_iqiyi_8_9_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_iqiyi_8_9_request_proto_rawDescData)
	})
	return file_iqiyi_8_9_request_proto_rawDescData
}

var file_iqiyi_8_9_request_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_iqiyi_8_9_request_proto_goTypes = []interface{}{
	(*FloorPrice)(nil),      // 0: iqiyi_down.FloorPrice
	(*UserFeature)(nil),     // 1: iqiyi_down.UserFeature
	(*UserSession)(nil),     // 2: iqiyi_down.UserSession
	(*Banner)(nil),          // 3: iqiyi_down.Banner
	(*Video)(nil),           // 4: iqiyi_down.Video
	(*Impression)(nil),      // 5: iqiyi_down.Impression
	(*Site)(nil),            // 6: iqiyi_down.Site
	(*Content)(nil),         // 7: iqiyi_down.Content
	(*DeviceIdentity)(nil),  // 8: iqiyi_down.DeviceIdentity
	(*Device)(nil),          // 9: iqiyi_down.Device
	(*Geo)(nil),             // 10: iqiyi_down.Geo
	(*ViewingHistory)(nil),  // 11: iqiyi_down.ViewingHistory
	(*User)(nil),            // 12: iqiyi_down.User
	(*BidRequest)(nil),      // 13: iqiyi_down.BidRequest
	(*Device_Caid)(nil),     // 14: iqiyi_down.Device.Caid
	(*Device_CaidInfo)(nil), // 15: iqiyi_down.Device.CaidInfo
}
var file_iqiyi_8_9_request_proto_depIdxs = []int32{
	3,  // 0: iqiyi_down.Impression.banner:type_name -> iqiyi_down.Banner
	4,  // 1: iqiyi_down.Impression.video:type_name -> iqiyi_down.Video
	0,  // 2: iqiyi_down.Impression.floor_price:type_name -> iqiyi_down.FloorPrice
	7,  // 3: iqiyi_down.Site.content:type_name -> iqiyi_down.Content
	10, // 4: iqiyi_down.Device.geo:type_name -> iqiyi_down.Geo
	8,  // 5: iqiyi_down.Device.device_identity:type_name -> iqiyi_down.DeviceIdentity
	15, // 6: iqiyi_down.Device.caid_info:type_name -> iqiyi_down.Device.CaidInfo
	1,  // 7: iqiyi_down.User.feature:type_name -> iqiyi_down.UserFeature
	2,  // 8: iqiyi_down.User.session:type_name -> iqiyi_down.UserSession
	11, // 9: iqiyi_down.User.viewing_history:type_name -> iqiyi_down.ViewingHistory
	12, // 10: iqiyi_down.BidRequest.user:type_name -> iqiyi_down.User
	6,  // 11: iqiyi_down.BidRequest.site:type_name -> iqiyi_down.Site
	9,  // 12: iqiyi_down.BidRequest.device:type_name -> iqiyi_down.Device
	5,  // 13: iqiyi_down.BidRequest.imp:type_name -> iqiyi_down.Impression
	14, // 14: iqiyi_down.Device.CaidInfo.caid:type_name -> iqiyi_down.Device.Caid
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_iqiyi_8_9_request_proto_init() }
func file_iqiyi_8_9_request_proto_init() {
	if File_iqiyi_8_9_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iqiyi_8_9_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FloorPrice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserFeature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceIdentity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViewingHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_request_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device_CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iqiyi_8_9_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_iqiyi_8_9_request_proto_goTypes,
		DependencyIndexes: file_iqiyi_8_9_request_proto_depIdxs,
		MessageInfos:      file_iqiyi_8_9_request_proto_msgTypes,
	}.Build()
	File_iqiyi_8_9_request_proto = out.File
	file_iqiyi_8_9_request_proto_rawDesc = nil
	file_iqiyi_8_9_request_proto_goTypes = nil
	file_iqiyi_8_9_request_proto_depIdxs = nil
}
