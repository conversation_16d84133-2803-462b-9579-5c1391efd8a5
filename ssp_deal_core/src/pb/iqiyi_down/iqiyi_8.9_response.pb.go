// This file is derived from the OpenRTB specification.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: iqiyi_8.9_response.proto

package iqiyi_down

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This message will be sent through the impression URL that is specified in the
// VAST XML when an advertisement is displayed to user successfully.
type Settlement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This filed can be used to dynamically determine the specific algorithm
	// for generating the fields "price" and "auth". You don't need to care the
	// value of this field until you are told to.
	Version *uint32 `protobuf:"varint,1,opt,name=version" json:"version,omitempty"`
	// Encrypted price.
	Price []byte `protobuf:"bytes,2,req,name=price" json:"price,omitempty"`
	// Authentication information for "price".
	Auth []byte `protobuf:"bytes,3,opt,name=auth" json:"auth,omitempty"`
}

func (x *Settlement) Reset() {
	*x = Settlement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settlement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settlement) ProtoMessage() {}

func (x *Settlement) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settlement.ProtoReflect.Descriptor instead.
func (*Settlement) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_response_proto_rawDescGZIP(), []int{0}
}

func (x *Settlement) GetVersion() uint32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *Settlement) GetPrice() []byte {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *Settlement) GetAuth() []byte {
	if x != nil {
		return x.Auth
	}
	return nil
}

type Bid struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// ID for the bid object chosen by the bidder for tracking and debugging
	// purposes. It is useful when multiple bids are submitted for a single
	// impression for a given seat.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// ID of the impression object to which this bid applies.
	Impid *string `protobuf:"bytes,2,req,name=impid" json:"impid,omitempty"`
	// The bidding price in RMB(cent per CPM).
	Price *int32 `protobuf:"varint,3,req,name=price" json:"price,omitempty"`
	// The VAST XML for describing the advertisement of this bid.
	Adm *string `protobuf:"bytes,6,req,name=adm" json:"adm,omitempty"`
	// The id of the creative to be presented to viewers. This field should be
	// populated with the "tvid" that is responded when the creative is uploaded.
	Crid *string `protobuf:"bytes,10,req,name=crid" json:"crid,omitempty"`
	// This field indidates the DSP hopes its ad begins at this time.
	// If the startdelay of an impression object from BidRequest is X, then the
	// valid value of this field is X + N * 15, N = 0, 1, 2, ...
	// Note, if a DSP hopes to have more opportunity to gain an impression, this
	// field should not be set.
	Startdelay *int32 `protobuf:"varint,16,opt,name=startdelay" json:"startdelay,omitempty"`
	// When a bid is returned according to user data such as age, interest,
	// gender and so on, this field should be set to true.
	IsPrecisionAdvertising *bool   `protobuf:"varint,17,opt,name=is_precision_advertising,json=isPrecisionAdvertising,def=0" json:"is_precision_advertising,omitempty"`
	DeeplinkUrl            *string `protobuf:"bytes,18,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`
	// App package name.
	ApkName *string `protobuf:"bytes,19,opt,name=apk_name,json=apkName" json:"apk_name,omitempty"`
	// The content of the creative will be presented to viewers. This field should
	// be in JSON format.
	CreativeContent  *string `protobuf:"bytes,20,opt,name=creative_content,json=creativeContent" json:"creative_content,omitempty"`
	UniversalLinkUrl *string `protobuf:"bytes,21,opt,name=universal_link_url,json=universalLinkUrl" json:"universal_link_url,omitempty"`
	WinNoticeUrl     *string `protobuf:"bytes,22,opt,name=win_notice_url,json=winNoticeUrl" json:"win_notice_url,omitempty"`
	MiniAppName      *string `protobuf:"bytes,23,opt,name=mini_app_name,json=miniAppName" json:"mini_app_name,omitempty"`
	MiniAppPath      *string `protobuf:"bytes,24,opt,name=mini_app_path,json=miniAppPath" json:"mini_app_path,omitempty"`
	FeedBackInfo     *string `protobuf:"bytes,25,opt,name=feed_back_info,json=feedBackInfo" json:"feed_back_info,omitempty"`
	// Download app from app store or not.
	DownloadFromStore *bool `protobuf:"varint,26,opt,name=download_from_store,json=downloadFromStore" json:"download_from_store,omitempty"`
	// Creative id from dsp, max length can't exceed 64 bytes.
	CreativeId *string `protobuf:"bytes,27,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	// Added bid info
	AdSource          *string `protobuf:"bytes,28,opt,name=ad_source,json=adSource" json:"ad_source,omitempty"`
	ProductName       *string `protobuf:"bytes,29,opt,name=product_name,json=productName" json:"product_name,omitempty"`
	PrimaryIndustry   *int64  `protobuf:"varint,30,opt,name=primary_industry,json=primaryIndustry" json:"primary_industry,omitempty"`
	SecondaryIndustry *int64  `protobuf:"varint,31,opt,name=secondary_industry,json=secondaryIndustry" json:"secondary_industry,omitempty"`
}

// Default values for Bid fields.
const (
	Default_Bid_IsPrecisionAdvertising = bool(false)
)

func (x *Bid) Reset() {
	*x = Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_response_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bid) ProtoMessage() {}

func (x *Bid) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_response_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bid.ProtoReflect.Descriptor instead.
func (*Bid) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_response_proto_rawDescGZIP(), []int{1}
}

func (x *Bid) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Bid) GetImpid() string {
	if x != nil && x.Impid != nil {
		return *x.Impid
	}
	return ""
}

func (x *Bid) GetPrice() int32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *Bid) GetAdm() string {
	if x != nil && x.Adm != nil {
		return *x.Adm
	}
	return ""
}

func (x *Bid) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

func (x *Bid) GetStartdelay() int32 {
	if x != nil && x.Startdelay != nil {
		return *x.Startdelay
	}
	return 0
}

func (x *Bid) GetIsPrecisionAdvertising() bool {
	if x != nil && x.IsPrecisionAdvertising != nil {
		return *x.IsPrecisionAdvertising
	}
	return Default_Bid_IsPrecisionAdvertising
}

func (x *Bid) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *Bid) GetApkName() string {
	if x != nil && x.ApkName != nil {
		return *x.ApkName
	}
	return ""
}

func (x *Bid) GetCreativeContent() string {
	if x != nil && x.CreativeContent != nil {
		return *x.CreativeContent
	}
	return ""
}

func (x *Bid) GetUniversalLinkUrl() string {
	if x != nil && x.UniversalLinkUrl != nil {
		return *x.UniversalLinkUrl
	}
	return ""
}

func (x *Bid) GetWinNoticeUrl() string {
	if x != nil && x.WinNoticeUrl != nil {
		return *x.WinNoticeUrl
	}
	return ""
}

func (x *Bid) GetMiniAppName() string {
	if x != nil && x.MiniAppName != nil {
		return *x.MiniAppName
	}
	return ""
}

func (x *Bid) GetMiniAppPath() string {
	if x != nil && x.MiniAppPath != nil {
		return *x.MiniAppPath
	}
	return ""
}

func (x *Bid) GetFeedBackInfo() string {
	if x != nil && x.FeedBackInfo != nil {
		return *x.FeedBackInfo
	}
	return ""
}

func (x *Bid) GetDownloadFromStore() bool {
	if x != nil && x.DownloadFromStore != nil {
		return *x.DownloadFromStore
	}
	return false
}

func (x *Bid) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *Bid) GetAdSource() string {
	if x != nil && x.AdSource != nil {
		return *x.AdSource
	}
	return ""
}

func (x *Bid) GetProductName() string {
	if x != nil && x.ProductName != nil {
		return *x.ProductName
	}
	return ""
}

func (x *Bid) GetPrimaryIndustry() int64 {
	if x != nil && x.PrimaryIndustry != nil {
		return *x.PrimaryIndustry
	}
	return 0
}

func (x *Bid) GetSecondaryIndustry() int64 {
	if x != nil && x.SecondaryIndustry != nil {
		return *x.SecondaryIndustry
	}
	return 0
}

type Seatbid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of bid objects. Each bid object should be related to an impression
	// object in the bid request.
	Bid []*Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`
}

func (x *Seatbid) Reset() {
	*x = Seatbid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_response_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Seatbid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Seatbid) ProtoMessage() {}

func (x *Seatbid) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_response_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Seatbid.ProtoReflect.Descriptor instead.
func (*Seatbid) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_response_proto_rawDescGZIP(), []int{2}
}

func (x *Seatbid) GetBid() []*Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// This id should be the same as the id of the corresponding BidRequest.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// The list of seatbid objects.
	Seatbid []*Seatbid `protobuf:"bytes,2,rep,name=seatbid" json:"seatbid,omitempty"`
	// If BidRequest.is_ping is true, please set this filed with your processing
	// time in milliseconds from receiving request to returning response.
	ProcessingTimeMs *int32 `protobuf:"varint,4,opt,name=processing_time_ms,json=processingTimeMs" json:"processing_time_ms,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_8_9_response_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_8_9_response_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_iqiyi_8_9_response_proto_rawDescGZIP(), []int{3}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*Seatbid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetProcessingTimeMs() int32 {
	if x != nil && x.ProcessingTimeMs != nil {
		return *x.ProcessingTimeMs
	}
	return 0
}

var File_iqiyi_8_9_response_proto protoreflect.FileDescriptor

var file_iqiyi_8_9_response_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x38, 0x2e, 0x39, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x71, 0x69, 0x79,
	0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x22, 0x50, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x22, 0xe8, 0x05, 0x0a, 0x03, 0x42, 0x69, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x64, 0x6d, 0x18, 0x06, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x61, 0x64, 0x6d, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x12, 0x3f, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x16, 0x69, 0x73, 0x50,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69,
	0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x61, 0x70, 0x70,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x69, 0x6e,
	0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x65, 0x65, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e,
	0x0a, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72,
	0x79, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x2a, 0x08, 0x08, 0x64, 0x10, 0x80, 0x80,
	0x80, 0x80, 0x02, 0x22, 0x2c, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x69, 0x71,
	0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69,
	0x64, 0x22, 0x84, 0x01, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2d, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64,
	0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x2a, 0x08,
	0x08, 0x64, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x42, 0x18, 0x5a, 0x16, 0x6d, 0x68, 0x5f, 0x70,
	0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x71, 0x69, 0x79, 0x69, 0x5f, 0x64, 0x6f,
	0x77, 0x6e,
}

var (
	file_iqiyi_8_9_response_proto_rawDescOnce sync.Once
	file_iqiyi_8_9_response_proto_rawDescData = file_iqiyi_8_9_response_proto_rawDesc
)

func file_iqiyi_8_9_response_proto_rawDescGZIP() []byte {
	file_iqiyi_8_9_response_proto_rawDescOnce.Do(func() {
		file_iqiyi_8_9_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_iqiyi_8_9_response_proto_rawDescData)
	})
	return file_iqiyi_8_9_response_proto_rawDescData
}

var file_iqiyi_8_9_response_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_iqiyi_8_9_response_proto_goTypes = []interface{}{
	(*Settlement)(nil),  // 0: iqiyi_down.Settlement
	(*Bid)(nil),         // 1: iqiyi_down.Bid
	(*Seatbid)(nil),     // 2: iqiyi_down.Seatbid
	(*BidResponse)(nil), // 3: iqiyi_down.BidResponse
}
var file_iqiyi_8_9_response_proto_depIdxs = []int32{
	1, // 0: iqiyi_down.Seatbid.bid:type_name -> iqiyi_down.Bid
	2, // 1: iqiyi_down.BidResponse.seatbid:type_name -> iqiyi_down.Seatbid
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_iqiyi_8_9_response_proto_init() }
func file_iqiyi_8_9_response_proto_init() {
	if File_iqiyi_8_9_response_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iqiyi_8_9_response_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Settlement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_response_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_response_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Seatbid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_8_9_response_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iqiyi_8_9_response_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_iqiyi_8_9_response_proto_goTypes,
		DependencyIndexes: file_iqiyi_8_9_response_proto_depIdxs,
		MessageInfos:      file_iqiyi_8_9_response_proto_msgTypes,
	}.Build()
	File_iqiyi_8_9_response_proto = out.File
	file_iqiyi_8_9_response_proto_rawDesc = nil
	file_iqiyi_8_9_response_proto_goTypes = nil
	file_iqiyi_8_9_response_proto_depIdxs = nil
}
