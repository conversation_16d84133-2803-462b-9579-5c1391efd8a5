// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.0
// source: wifi_20220926.proto

package wifi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RTBRequest_NetType int32

const (
	RTBRequest_NT_UnKnown     RTBRequest_NetType = 0 // 未知网络
	RTBRequest_NT_Ethernet    RTBRequest_NetType = 1 // 以太网
	RTBRequest_NT_Wifi        RTBRequest_NetType = 2 // WiFi
	RTBRequest_NT_Cellular    RTBRequest_NetType = 3 // 1G
	RTBRequest_NT_Cellular_2G RTBRequest_NetType = 4 // 2G
	RTBRequest_NT_Cellular_3G RTBRequest_NetType = 5 // 3G
	RTBRequest_NT_Cellular_4G RTBRequest_NetType = 6 // 4G
)

// Enum value maps for RTBRequest_NetType.
var (
	RTBRequest_NetType_name = map[int32]string{
		0: "NT_UnKnown",
		1: "NT_Ethernet",
		2: "NT_Wifi",
		3: "NT_Cellular",
		4: "NT_Cellular_2G",
		5: "NT_Cellular_3G",
		6: "NT_Cellular_4G",
	}
	RTBRequest_NetType_value = map[string]int32{
		"NT_UnKnown":     0,
		"NT_Ethernet":    1,
		"NT_Wifi":        2,
		"NT_Cellular":    3,
		"NT_Cellular_2G": 4,
		"NT_Cellular_3G": 5,
		"NT_Cellular_4G": 6,
	}
)

func (x RTBRequest_NetType) Enum() *RTBRequest_NetType {
	p := new(RTBRequest_NetType)
	*p = x
	return p
}

func (x RTBRequest_NetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RTBRequest_NetType) Descriptor() protoreflect.EnumDescriptor {
	return file_wifi_20220926_proto_enumTypes[0].Descriptor()
}

func (RTBRequest_NetType) Type() protoreflect.EnumType {
	return &file_wifi_20220926_proto_enumTypes[0]
}

func (x RTBRequest_NetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RTBRequest_NetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RTBRequest_NetType(num)
	return nil
}

// Deprecated: Use RTBRequest_NetType.Descriptor instead.
func (RTBRequest_NetType) EnumDescriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 0}
}

type RTBRequest_DeviceInfo_DeviceType int32

const (
	RTBRequest_DeviceInfo_DT_UnKnown RTBRequest_DeviceInfo_DeviceType = 0
	RTBRequest_DeviceInfo_DT_Phone   RTBRequest_DeviceInfo_DeviceType = 1
	RTBRequest_DeviceInfo_DT_Pad     RTBRequest_DeviceInfo_DeviceType = 2
	RTBRequest_DeviceInfo_DT_PC      RTBRequest_DeviceInfo_DeviceType = 3
	RTBRequest_DeviceInfo_DT_TV      RTBRequest_DeviceInfo_DeviceType = 4
	RTBRequest_DeviceInfo_DT_Wap     RTBRequest_DeviceInfo_DeviceType = 5
)

// Enum value maps for RTBRequest_DeviceInfo_DeviceType.
var (
	RTBRequest_DeviceInfo_DeviceType_name = map[int32]string{
		0: "DT_UnKnown",
		1: "DT_Phone",
		2: "DT_Pad",
		3: "DT_PC",
		4: "DT_TV",
		5: "DT_Wap",
	}
	RTBRequest_DeviceInfo_DeviceType_value = map[string]int32{
		"DT_UnKnown": 0,
		"DT_Phone":   1,
		"DT_Pad":     2,
		"DT_PC":      3,
		"DT_TV":      4,
		"DT_Wap":     5,
	}
)

func (x RTBRequest_DeviceInfo_DeviceType) Enum() *RTBRequest_DeviceInfo_DeviceType {
	p := new(RTBRequest_DeviceInfo_DeviceType)
	*p = x
	return p
}

func (x RTBRequest_DeviceInfo_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RTBRequest_DeviceInfo_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_wifi_20220926_proto_enumTypes[1].Descriptor()
}

func (RTBRequest_DeviceInfo_DeviceType) Type() protoreflect.EnumType {
	return &file_wifi_20220926_proto_enumTypes[1]
}

func (x RTBRequest_DeviceInfo_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RTBRequest_DeviceInfo_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RTBRequest_DeviceInfo_DeviceType(num)
	return nil
}

// Deprecated: Use RTBRequest_DeviceInfo_DeviceType.Descriptor instead.
func (RTBRequest_DeviceInfo_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 0, 0}
}

type RTBRequest_AdSlotInfo_AdType int32

const (
	RTBRequest_AdSlotInfo_AT_ALL      RTBRequest_AdSlotInfo_AdType = 0 // 无限制
	RTBRequest_AdSlotInfo_AT_REDIRECT RTBRequest_AdSlotInfo_AdType = 1 // 点击跳转
	RTBRequest_AdSlotInfo_AT_DOWNLOAD RTBRequest_AdSlotInfo_AdType = 2 // 点击下载
	RTBRequest_AdSlotInfo_AT_LBA      RTBRequest_AdSlotInfo_AdType = 3 // LBA
	RTBRequest_AdSlotInfo_AT_ONLYSHOW RTBRequest_AdSlotInfo_AdType = 4 // 仅展示，不可点击
)

// Enum value maps for RTBRequest_AdSlotInfo_AdType.
var (
	RTBRequest_AdSlotInfo_AdType_name = map[int32]string{
		0: "AT_ALL",
		1: "AT_REDIRECT",
		2: "AT_DOWNLOAD",
		3: "AT_LBA",
		4: "AT_ONLYSHOW",
	}
	RTBRequest_AdSlotInfo_AdType_value = map[string]int32{
		"AT_ALL":      0,
		"AT_REDIRECT": 1,
		"AT_DOWNLOAD": 2,
		"AT_LBA":      3,
		"AT_ONLYSHOW": 4,
	}
)

func (x RTBRequest_AdSlotInfo_AdType) Enum() *RTBRequest_AdSlotInfo_AdType {
	p := new(RTBRequest_AdSlotInfo_AdType)
	*p = x
	return p
}

func (x RTBRequest_AdSlotInfo_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RTBRequest_AdSlotInfo_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_wifi_20220926_proto_enumTypes[2].Descriptor()
}

func (RTBRequest_AdSlotInfo_AdType) Type() protoreflect.EnumType {
	return &file_wifi_20220926_proto_enumTypes[2]
}

func (x RTBRequest_AdSlotInfo_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RTBRequest_AdSlotInfo_AdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RTBRequest_AdSlotInfo_AdType(num)
	return nil
}

// Deprecated: Use RTBRequest_AdSlotInfo_AdType.Descriptor instead.
func (RTBRequest_AdSlotInfo_AdType) EnumDescriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 3, 0}
}

type RTBRequest_AdSlotInfo_AdSlotType int32

const (
	RTBRequest_AdSlotInfo_AST_BANNER        RTBRequest_AdSlotInfo_AdSlotType = 0 // banner广告位，已不使用
	RTBRequest_AdSlotInfo_AST_OPEN_SCREEN   RTBRequest_AdSlotInfo_AdSlotType = 1 // 开屏广告位
	RTBRequest_AdSlotInfo_AST_TABLE_PLAQUE  RTBRequest_AdSlotInfo_AdSlotType = 2 // 插屏广告位
	RTBRequest_AdSlotInfo_AST_FEEDS         RTBRequest_AdSlotInfo_AdSlotType = 3 // feeds流广告位
	RTBRequest_AdSlotInfo_AST_INTEGRAL_WALL RTBRequest_AdSlotInfo_AdSlotType = 4 // 积分墙广告位
	RTBRequest_AdSlotInfo_AST_QUIT          RTBRequest_AdSlotInfo_AdSlotType = 5 // 退出广告位
	RTBRequest_AdSlotInfo_AST_PUSH          RTBRequest_AdSlotInfo_AdSlotType = 6 // push广告
	RTBRequest_AdSlotInfo_AST_NOTICE        RTBRequest_AdSlotInfo_AdSlotType = 7 // 小喇叭广告位
	RTBRequest_AdSlotInfo_AST_BACKDOWNLOAD  RTBRequest_AdSlotInfo_AdSlotType = 8 // 预下载广告位
	RTBRequest_AdSlotInfo_AST_VIDEO         RTBRequest_AdSlotInfo_AdSlotType = 9 // 视频广告
)

// Enum value maps for RTBRequest_AdSlotInfo_AdSlotType.
var (
	RTBRequest_AdSlotInfo_AdSlotType_name = map[int32]string{
		0: "AST_BANNER",
		1: "AST_OPEN_SCREEN",
		2: "AST_TABLE_PLAQUE",
		3: "AST_FEEDS",
		4: "AST_INTEGRAL_WALL",
		5: "AST_QUIT",
		6: "AST_PUSH",
		7: "AST_NOTICE",
		8: "AST_BACKDOWNLOAD",
		9: "AST_VIDEO",
	}
	RTBRequest_AdSlotInfo_AdSlotType_value = map[string]int32{
		"AST_BANNER":        0,
		"AST_OPEN_SCREEN":   1,
		"AST_TABLE_PLAQUE":  2,
		"AST_FEEDS":         3,
		"AST_INTEGRAL_WALL": 4,
		"AST_QUIT":          5,
		"AST_PUSH":          6,
		"AST_NOTICE":        7,
		"AST_BACKDOWNLOAD":  8,
		"AST_VIDEO":         9,
	}
)

func (x RTBRequest_AdSlotInfo_AdSlotType) Enum() *RTBRequest_AdSlotInfo_AdSlotType {
	p := new(RTBRequest_AdSlotInfo_AdSlotType)
	*p = x
	return p
}

func (x RTBRequest_AdSlotInfo_AdSlotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RTBRequest_AdSlotInfo_AdSlotType) Descriptor() protoreflect.EnumDescriptor {
	return file_wifi_20220926_proto_enumTypes[3].Descriptor()
}

func (RTBRequest_AdSlotInfo_AdSlotType) Type() protoreflect.EnumType {
	return &file_wifi_20220926_proto_enumTypes[3]
}

func (x RTBRequest_AdSlotInfo_AdSlotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RTBRequest_AdSlotInfo_AdSlotType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RTBRequest_AdSlotInfo_AdSlotType(num)
	return nil
}

// Deprecated: Use RTBRequest_AdSlotInfo_AdSlotType.Descriptor instead.
func (RTBRequest_AdSlotInfo_AdSlotType) EnumDescriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 3, 1}
}

type RTBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid             *string                      `protobuf:"bytes,1,req,name=sid" json:"sid,omitempty"`                                                      // 唯一检索ID
	ClientIp        *string                      `protobuf:"bytes,2,opt,name=client_ip,json=clientIp" json:"client_ip,omitempty"`                            // APP的IP地址
	UserAgent       *string                      `protobuf:"bytes,3,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`                         // APP的User-Agent
	Language        *string                      `protobuf:"bytes,4,opt,name=language" json:"language,omitempty"`                                            // 如：zh-CN
	NetType         *RTBRequest_NetType          `protobuf:"varint,5,opt,name=net_type,json=netType,enum=wifi.RTBRequest_NetType" json:"net_type,omitempty"` // APP所处的网络类型
	Os              *string                      `protobuf:"bytes,6,opt,name=os" json:"os,omitempty"`                                                        // android、ios等
	OsVersion       *string                      `protobuf:"bytes,7,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`
	DeviceInfo      *RTBRequest_DeviceInfo       `protobuf:"bytes,8,opt,name=device_info,json=deviceInfo" json:"device_info,omitempty"`
	AppInfo         *RTBRequest_AppInfo          `protobuf:"bytes,9,opt,name=app_info,json=appInfo" json:"app_info,omitempty"`
	IdInfo          *RTBRequest_IdInfo           `protobuf:"bytes,10,opt,name=id_info,json=idInfo" json:"id_info,omitempty"`        // 设备ID信息
	AdSlots         []*RTBRequest_AdSlotInfo     `protobuf:"bytes,11,rep,name=ad_slots,json=adSlots" json:"ad_slots,omitempty"`     // 广告位信息，默认是一个
	IsTest          *bool                        `protobuf:"varint,12,opt,name=is_test,json=isTest,def=0" json:"is_test,omitempty"` // 测试请求标记
	Longitude       *float64                     `protobuf:"fixed64,14,opt,name=longitude" json:"longitude,omitempty"`
	Latitude        *float64                     `protobuf:"fixed64,15,opt,name=latitude" json:"latitude,omitempty"`
	LaloType        *uint32                      `protobuf:"varint,16,opt,name=lalo_type,json=laloType" json:"lalo_type,omitempty"`        // 0-高德, 1-百度, 2-腾讯, 3-谷歌
	MediaIndex      *string                      `protobuf:"bytes,23,opt,name=media_index,json=mediaIndex" json:"media_index,omitempty"`   // 媒体的序号
	TypeIdList      []string                     `protobuf:"bytes,24,rep,name=type_id_list,json=typeIdList" json:"type_id_list,omitempty"` // 广告支持的广告样式的列表，为空表示不限制
	IsHttps         *bool                        `protobuf:"varint,25,opt,name=is_https,json=isHttps,def=0" json:"is_https,omitempty"`     //是否https, true的话，返回的物料地址和上报地址都必须是https的，否则会被过滤
	CustomizedInfos []*RTBRequest_CustomizedInfo `protobuf:"bytes,28,rep,name=customized_infos,json=customizedInfos" json:"customized_infos,omitempty"`
	AppList         []string                     `protobuf:"bytes,36,rep,name=app_list,json=appList" json:"app_list,omitempty"` // 给DSP方传包名对应的ID号
}

// Default values for RTBRequest fields.
const (
	Default_RTBRequest_IsTest  = bool(false)
	Default_RTBRequest_IsHttps = bool(false)
)

func (x *RTBRequest) Reset() {
	*x = RTBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest) ProtoMessage() {}

func (x *RTBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest.ProtoReflect.Descriptor instead.
func (*RTBRequest) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0}
}

func (x *RTBRequest) GetSid() string {
	if x != nil && x.Sid != nil {
		return *x.Sid
	}
	return ""
}

func (x *RTBRequest) GetClientIp() string {
	if x != nil && x.ClientIp != nil {
		return *x.ClientIp
	}
	return ""
}

func (x *RTBRequest) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *RTBRequest) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *RTBRequest) GetNetType() RTBRequest_NetType {
	if x != nil && x.NetType != nil {
		return *x.NetType
	}
	return RTBRequest_NT_UnKnown
}

func (x *RTBRequest) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *RTBRequest) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *RTBRequest) GetDeviceInfo() *RTBRequest_DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *RTBRequest) GetAppInfo() *RTBRequest_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *RTBRequest) GetIdInfo() *RTBRequest_IdInfo {
	if x != nil {
		return x.IdInfo
	}
	return nil
}

func (x *RTBRequest) GetAdSlots() []*RTBRequest_AdSlotInfo {
	if x != nil {
		return x.AdSlots
	}
	return nil
}

func (x *RTBRequest) GetIsTest() bool {
	if x != nil && x.IsTest != nil {
		return *x.IsTest
	}
	return Default_RTBRequest_IsTest
}

func (x *RTBRequest) GetLongitude() float64 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

func (x *RTBRequest) GetLatitude() float64 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *RTBRequest) GetLaloType() uint32 {
	if x != nil && x.LaloType != nil {
		return *x.LaloType
	}
	return 0
}

func (x *RTBRequest) GetMediaIndex() string {
	if x != nil && x.MediaIndex != nil {
		return *x.MediaIndex
	}
	return ""
}

func (x *RTBRequest) GetTypeIdList() []string {
	if x != nil {
		return x.TypeIdList
	}
	return nil
}

func (x *RTBRequest) GetIsHttps() bool {
	if x != nil && x.IsHttps != nil {
		return *x.IsHttps
	}
	return Default_RTBRequest_IsHttps
}

func (x *RTBRequest) GetCustomizedInfos() []*RTBRequest_CustomizedInfo {
	if x != nil {
		return x.CustomizedInfos
	}
	return nil
}

func (x *RTBRequest) GetAppList() []string {
	if x != nil {
		return x.AppList
	}
	return nil
}

type RTBResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid           *string               `protobuf:"bytes,1,req,name=sid" json:"sid,omitempty"`                                             // 必须和请求中的sid是同一个值
	AdInfos       []*RTBResponse_AdInfo `protobuf:"bytes,2,rep,name=ad_infos,json=adInfos" json:"ad_infos,omitempty"`                      // 召回的广告，无召回时无需装填，一个广告位返回多个广告时，都填充在该数组
	DebugInfo     *string               `protobuf:"bytes,3,opt,name=debug_info,json=debugInfo" json:"debug_info,omitempty"`                // debug信息
	ProcessTimeMs *uint32               `protobuf:"varint,4,opt,name=process_time_ms,json=processTimeMs" json:"process_time_ms,omitempty"` // 处理耗时，单位：ms
}

func (x *RTBResponse) Reset() {
	*x = RTBResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBResponse) ProtoMessage() {}

func (x *RTBResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBResponse.ProtoReflect.Descriptor instead.
func (*RTBResponse) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{1}
}

func (x *RTBResponse) GetSid() string {
	if x != nil && x.Sid != nil {
		return *x.Sid
	}
	return ""
}

func (x *RTBResponse) GetAdInfos() []*RTBResponse_AdInfo {
	if x != nil {
		return x.AdInfos
	}
	return nil
}

func (x *RTBResponse) GetDebugInfo() string {
	if x != nil && x.DebugInfo != nil {
		return *x.DebugInfo
	}
	return ""
}

func (x *RTBResponse) GetProcessTimeMs() uint32 {
	if x != nil && x.ProcessTimeMs != nil {
		return *x.ProcessTimeMs
	}
	return 0
}

type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DebugInfo         *string          `protobuf:"bytes,1,opt,name=debug_info,json=debugInfo" json:"debug_info,omitempty"`                            // debug信息
	Adid              *string          `protobuf:"bytes,3,opt,name=adid" json:"adid,omitempty"`                                                       // 广告ID，针对素材预审接入方式，该值必须填预审通过后分配的creative_id，否则广告将不会被展现
	AdType            *string          `protobuf:"bytes,4,req,name=ad_type,json=adType" json:"ad_type,omitempty"`                                     // 广告类型：redirect/跳转,download/下载;
	LandingPageUrl    *string          `protobuf:"bytes,5,opt,name=landing_page_url,json=landingPageUrl" json:"landing_page_url,omitempty"`           // 跳转地址，当ad_type为redirect时填写，需要以http://或者https://开头的URL，如果地址中包含%%EXT%%宏，可替换为ext_info字段内容
	DeeplinkUrl       *string          `protobuf:"bytes,6,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`                      // deeplink调起APP的URL，deeplink广告的ad_type填写为redirect
	DownloadUrl       *string          `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl" json:"download_url,omitempty"`                      // APP的下载URL，当ad_type为download时填写
	Title             *string          `protobuf:"bytes,8,opt,name=title" json:"title,omitempty"`                                                     // 标题
	Content           *string          `protobuf:"bytes,9,opt,name=content" json:"content,omitempty"`                                                 // 预留字段
	Desc              *string          `protobuf:"bytes,10,opt,name=desc" json:"desc,omitempty"`                                                      // 预留字段
	AppName           *string          `protobuf:"bytes,11,opt,name=app_name,json=appName" json:"app_name,omitempty"`                                 // 只下载类型填写：应用名称
	PkgName           *string          `protobuf:"bytes,12,opt,name=pkg_name,json=pkgName" json:"pkg_name,omitempty"`                                 // 只下载类型填写：应用包名
	AppIcon           *string          `protobuf:"bytes,13,opt,name=app_icon,json=appIcon" json:"app_icon,omitempty"`                                 // 只下载类型填写：应用图标，需要以http://或者https://开头的URL
	ImageUrls         []string         `protobuf:"bytes,15,rep,name=image_urls,json=imageUrls" json:"image_urls,omitempty"`                           // 图片URL，需要以http://或者https://开头的URL
	ImageMd5S         []string         `protobuf:"bytes,16,rep,name=image_md5s,json=imageMd5s" json:"image_md5s,omitempty"`                           // 图片的MD5值，非图片URL的MD5值
	AttachDetail      *Ad_AttachDetail `protobuf:"bytes,17,opt,name=attach_detail,json=attachDetail" json:"attach_detail,omitempty"`                  // 附加创意的详细信息
	ClickUrls         []string         `protobuf:"bytes,18,rep,name=click_urls,json=clickUrls" json:"click_urls,omitempty"`                           // 广告点击时会上报的监测URL，需要以http://或者https://开头的URL
	DeeplinkClickUrls []string         `protobuf:"bytes,19,rep,name=deeplink_click_urls,json=deeplinkClickUrls" json:"deeplink_click_urls,omitempty"` // deeplink广告点击时上报的监测URL，需要以http://或者https://开头的URL
	ShowUrls          []string         `protobuf:"bytes,20,rep,name=show_urls,json=showUrls" json:"show_urls,omitempty"`                              // 展现上报的监测URL，广告在APP页面上渲染完成后会上报，需要以http://或者https://开头的URL
	DownloadUrls      []string         `protobuf:"bytes,22,rep,name=download_urls,json=downloadUrls" json:"download_urls,omitempty"`                  // 下载类广告APP开始下载时上报的监测URL
	DownloadedUrls    []string         `protobuf:"bytes,23,rep,name=downloaded_urls,json=downloadedUrls" json:"downloaded_urls,omitempty"`            // 下载类广告APP下载完成时上报的监测URL
	InstalledUrls     []string         `protobuf:"bytes,24,rep,name=installed_urls,json=installedUrls" json:"installed_urls,omitempty"`               // 下载类广告APP安装完成时上报的监测URL
	Category          []int64          `protobuf:"varint,25,rep,name=category" json:"category,omitempty"`                                             // 广告所属行业的ID
	ImgSize           *Ad_Size         `protobuf:"bytes,35,opt,name=img_size,json=imgSize" json:"img_size,omitempty"`                                 // 图片宽高尺寸
	CategoryName      []string         `protobuf:"bytes,36,rep,name=category_name,json=categoryName" json:"category_name,omitempty"`                  // 广告所属行业的中文名
	VideoStart        *string          `protobuf:"bytes,38,opt,name=video_start,json=videoStart" json:"video_start,omitempty"`                        // 视频开始播放时上报的监测URL
	VideoEnd          *string          `protobuf:"bytes,39,opt,name=video_end,json=videoEnd" json:"video_end,omitempty"`                              // 视频播放暂停/结束时上报的监控URL
	VideoUrl          *string          `protobuf:"bytes,40,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`                              // 视频URL
	DownloadMd5       *string          `protobuf:"bytes,41,opt,name=download_md5,json=downloadMd5" json:"download_md5,omitempty"`                     // 下载md5
	ExtInfo           *string          `protobuf:"bytes,59,opt,name=ext_info,json=extInfo" json:"ext_info,omitempty"`                                 // 当前素材的扩展信息，会替换到landing_page_url中
	PkgSize           *string          `protobuf:"bytes,63,opt,name=pkg_size,json=pkgSize" json:"pkg_size,omitempty"`                                 // 只下载类型填写：应用包大小
	AppVersion        *string          `protobuf:"bytes,87,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`                        // APP版本号
	AppDeveloper      *string          `protobuf:"bytes,88,opt,name=app_developer,json=appDeveloper" json:"app_developer,omitempty"`                  // APP开发者名称
	AppPermissions    []*Ad_Permission `protobuf:"bytes,89,rep,name=app_permissions,json=appPermissions" json:"app_permissions,omitempty"`            // APP权限列表
	AppPrivacy        *string          `protobuf:"bytes,90,opt,name=app_privacy,json=appPrivacy" json:"app_privacy,omitempty"`                        // APP隐私条款链接
	AppPermissionUrl  *string          `protobuf:"bytes,91,opt,name=app_permission_url,json=appPermissionUrl" json:"app_permission_url,omitempty"`    // 如果APP权限列表是一个URL，把URL填在这个字段中
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{2}
}

func (x *Ad) GetDebugInfo() string {
	if x != nil && x.DebugInfo != nil {
		return *x.DebugInfo
	}
	return ""
}

func (x *Ad) GetAdid() string {
	if x != nil && x.Adid != nil {
		return *x.Adid
	}
	return ""
}

func (x *Ad) GetAdType() string {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return ""
}

func (x *Ad) GetLandingPageUrl() string {
	if x != nil && x.LandingPageUrl != nil {
		return *x.LandingPageUrl
	}
	return ""
}

func (x *Ad) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *Ad) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *Ad) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Ad) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *Ad) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *Ad) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *Ad) GetPkgName() string {
	if x != nil && x.PkgName != nil {
		return *x.PkgName
	}
	return ""
}

func (x *Ad) GetAppIcon() string {
	if x != nil && x.AppIcon != nil {
		return *x.AppIcon
	}
	return ""
}

func (x *Ad) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *Ad) GetImageMd5S() []string {
	if x != nil {
		return x.ImageMd5S
	}
	return nil
}

func (x *Ad) GetAttachDetail() *Ad_AttachDetail {
	if x != nil {
		return x.AttachDetail
	}
	return nil
}

func (x *Ad) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *Ad) GetDeeplinkClickUrls() []string {
	if x != nil {
		return x.DeeplinkClickUrls
	}
	return nil
}

func (x *Ad) GetShowUrls() []string {
	if x != nil {
		return x.ShowUrls
	}
	return nil
}

func (x *Ad) GetDownloadUrls() []string {
	if x != nil {
		return x.DownloadUrls
	}
	return nil
}

func (x *Ad) GetDownloadedUrls() []string {
	if x != nil {
		return x.DownloadedUrls
	}
	return nil
}

func (x *Ad) GetInstalledUrls() []string {
	if x != nil {
		return x.InstalledUrls
	}
	return nil
}

func (x *Ad) GetCategory() []int64 {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Ad) GetImgSize() *Ad_Size {
	if x != nil {
		return x.ImgSize
	}
	return nil
}

func (x *Ad) GetCategoryName() []string {
	if x != nil {
		return x.CategoryName
	}
	return nil
}

func (x *Ad) GetVideoStart() string {
	if x != nil && x.VideoStart != nil {
		return *x.VideoStart
	}
	return ""
}

func (x *Ad) GetVideoEnd() string {
	if x != nil && x.VideoEnd != nil {
		return *x.VideoEnd
	}
	return ""
}

func (x *Ad) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

func (x *Ad) GetDownloadMd5() string {
	if x != nil && x.DownloadMd5 != nil {
		return *x.DownloadMd5
	}
	return ""
}

func (x *Ad) GetExtInfo() string {
	if x != nil && x.ExtInfo != nil {
		return *x.ExtInfo
	}
	return ""
}

func (x *Ad) GetPkgSize() string {
	if x != nil && x.PkgSize != nil {
		return *x.PkgSize
	}
	return ""
}

func (x *Ad) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *Ad) GetAppDeveloper() string {
	if x != nil && x.AppDeveloper != nil {
		return *x.AppDeveloper
	}
	return ""
}

func (x *Ad) GetAppPermissions() []*Ad_Permission {
	if x != nil {
		return x.AppPermissions
	}
	return nil
}

func (x *Ad) GetAppPrivacy() string {
	if x != nil && x.AppPrivacy != nil {
		return *x.AppPrivacy
	}
	return ""
}

func (x *Ad) GetAppPermissionUrl() string {
	if x != nil && x.AppPermissionUrl != nil {
		return *x.AppPermissionUrl
	}
	return ""
}

type CommonTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ads    []*Ad   `protobuf:"bytes,2,rep,name=ads" json:"ads,omitempty"`                     // 广告信息
	TypeId *string `protobuf:"bytes,3,opt,name=type_id,json=typeId" json:"type_id,omitempty"` // 广告的模板信息
}

func (x *CommonTemplate) Reset() {
	*x = CommonTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonTemplate) ProtoMessage() {}

func (x *CommonTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonTemplate.ProtoReflect.Descriptor instead.
func (*CommonTemplate) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{3}
}

func (x *CommonTemplate) GetAds() []*Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *CommonTemplate) GetTypeId() string {
	if x != nil && x.TypeId != nil {
		return *x.TypeId
	}
	return ""
}

type RTBRequest_DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type          *RTBRequest_DeviceInfo_DeviceType `protobuf:"varint,1,opt,name=type,enum=wifi.RTBRequest_DeviceInfo_DeviceType" json:"type,omitempty"`
	ScreenWidth   *uint32                           `protobuf:"varint,2,opt,name=screen_width,json=screenWidth" json:"screen_width,omitempty"`       // 屏幕宽度
	ScreenHeight  *uint32                           `protobuf:"varint,3,opt,name=screen_height,json=screenHeight" json:"screen_height,omitempty"`    // 屏幕高度
	ScreenDensity *uint32                           `protobuf:"varint,4,opt,name=screen_density,json=screenDensity" json:"screen_density,omitempty"` // 屏幕密度
	Horizontal    *bool                             `protobuf:"varint,5,opt,name=horizontal" json:"horizontal,omitempty"`                            // 横竖屏标记
	Vendor        *string                           `protobuf:"bytes,6,opt,name=vendor" json:"vendor,omitempty"`                                     // 设备生产商
	Model         *string                           `protobuf:"bytes,7,opt,name=model" json:"model,omitempty"`                                       // 设备型号
}

func (x *RTBRequest_DeviceInfo) Reset() {
	*x = RTBRequest_DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest_DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest_DeviceInfo) ProtoMessage() {}

func (x *RTBRequest_DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest_DeviceInfo.ProtoReflect.Descriptor instead.
func (*RTBRequest_DeviceInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RTBRequest_DeviceInfo) GetType() RTBRequest_DeviceInfo_DeviceType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return RTBRequest_DeviceInfo_DT_UnKnown
}

func (x *RTBRequest_DeviceInfo) GetScreenWidth() uint32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *RTBRequest_DeviceInfo) GetScreenHeight() uint32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *RTBRequest_DeviceInfo) GetScreenDensity() uint32 {
	if x != nil && x.ScreenDensity != nil {
		return *x.ScreenDensity
	}
	return 0
}

func (x *RTBRequest_DeviceInfo) GetHorizontal() bool {
	if x != nil && x.Horizontal != nil {
		return *x.Horizontal
	}
	return false
}

func (x *RTBRequest_DeviceInfo) GetVendor() string {
	if x != nil && x.Vendor != nil {
		return *x.Vendor
	}
	return ""
}

func (x *RTBRequest_DeviceInfo) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

type RTBRequest_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      *string `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	AppName    *string `protobuf:"bytes,2,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	AppVersion *string `protobuf:"bytes,3,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	PkgName    *string `protobuf:"bytes,4,opt,name=pkg_name,json=pkgName" json:"pkg_name,omitempty"`
	Mkt        *string `protobuf:"bytes,5,opt,name=mkt" json:"mkt,omitempty"`                     // 应用商店
	MktSn      *string `protobuf:"bytes,6,opt,name=mkt_sn,json=mktSn" json:"mkt_sn,omitempty"`    // app在商店内的编号
	MktCat     *string `protobuf:"bytes,7,opt,name=mkt_cat,json=mktCat" json:"mkt_cat,omitempty"` // 分类
	MktTag     *string `protobuf:"bytes,8,opt,name=mkt_tag,json=mktTag" json:"mkt_tag,omitempty"` // 标签
}

func (x *RTBRequest_AppInfo) Reset() {
	*x = RTBRequest_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest_AppInfo) ProtoMessage() {}

func (x *RTBRequest_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest_AppInfo.ProtoReflect.Descriptor instead.
func (*RTBRequest_AppInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 1}
}

func (x *RTBRequest_AppInfo) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetPkgName() string {
	if x != nil && x.PkgName != nil {
		return *x.PkgName
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetMkt() string {
	if x != nil && x.Mkt != nil {
		return *x.Mkt
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetMktSn() string {
	if x != nil && x.MktSn != nil {
		return *x.MktSn
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetMktCat() string {
	if x != nil && x.MktCat != nil {
		return *x.MktCat
	}
	return ""
}

func (x *RTBRequest_AppInfo) GetMktTag() string {
	if x != nil && x.MktTag != nil {
		return *x.MktTag
	}
	return ""
}

type RTBRequest_IdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Imei        *string `protobuf:"bytes,1,opt,name=imei" json:"imei,omitempty"`
	Mac         *string `protobuf:"bytes,2,opt,name=mac" json:"mac,omitempty"`
	AndroidId   *string `protobuf:"bytes,3,opt,name=android_id,json=androidId" json:"android_id,omitempty"`
	AndroidAdid *string `protobuf:"bytes,4,opt,name=android_adid,json=androidAdid" json:"android_adid,omitempty"` // android advertising ID
	Idfa        *string `protobuf:"bytes,5,opt,name=idfa" json:"idfa,omitempty"`                                  // iOS(>=6)
	OpenUDID    *string `protobuf:"bytes,6,opt,name=openUDID" json:"openUDID,omitempty"`                          // iOS(<6)
	Oaid        *string `protobuf:"bytes,7,opt,name=oaid" json:"oaid,omitempty"`
}

func (x *RTBRequest_IdInfo) Reset() {
	*x = RTBRequest_IdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest_IdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest_IdInfo) ProtoMessage() {}

func (x *RTBRequest_IdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest_IdInfo.ProtoReflect.Descriptor instead.
func (*RTBRequest_IdInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 2}
}

func (x *RTBRequest_IdInfo) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetAndroidAdid() string {
	if x != nil && x.AndroidAdid != nil {
		return *x.AndroidAdid
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetOpenUDID() string {
	if x != nil && x.OpenUDID != nil {
		return *x.OpenUDID
	}
	return ""
}

func (x *RTBRequest_IdInfo) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

type RTBRequest_AdSlotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     *string                           `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                                                                                 // DSP的广告位ID
	Width                  *uint32                           `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`                                                                          // 广告位的宽度，并非物料图片的宽度
	Height                 *uint32                           `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`                                                                        // 广告位的高度，并非物料图片的高度
	Type                   *RTBRequest_AdSlotInfo_AdType     `protobuf:"varint,4,opt,name=type,enum=wifi.RTBRequest_AdSlotInfo_AdType" json:"type,omitempty"`                                     // 广告位能接受的广告类型
	OpenScreen             *bool                             `protobuf:"varint,5,opt,name=open_screen,json=openScreen" json:"open_screen,omitempty"`                                              // 开屏标记，请使用ad_slot_type判断广告位类型
	AdSlotType             *RTBRequest_AdSlotInfo_AdSlotType `protobuf:"varint,6,opt,name=ad_slot_type,json=adSlotType,enum=wifi.RTBRequest_AdSlotInfo_AdSlotType" json:"ad_slot_type,omitempty"` // 广告位类型
	MinCpm                 *uint32                           `protobuf:"varint,7,opt,name=min_cpm,json=minCpm" json:"min_cpm,omitempty"`                                                          // 广告位底价
	ReqNum                 *uint32                           `protobuf:"varint,8,opt,name=req_num,json=reqNum,def=1" json:"req_num,omitempty"`                                                    // 广告位请求的广告条数，默认请求1条广告
	ExcludedLandingPageUrl []string                          `protobuf:"bytes,9,rep,name=excluded_landing_page_url,json=excludedLandingPageUrl" json:"excluded_landing_page_url,omitempty"`       // 不允许的落地页url
	ExcludedCategory       []int32                           `protobuf:"varint,10,rep,name=excluded_category,json=excludedCategory" json:"excluded_category,omitempty"`                           // 不允许的行业类型
	AllowedCategory        []int32                           `protobuf:"varint,11,rep,name=allowed_category,json=allowedCategory" json:"allowed_category,omitempty"`                              // 仅允许的行业类型
	Posid                  []int32                           `protobuf:"varint,13,rep,name=posid" json:"posid,omitempty"`
	PageIndex              *int32                            `protobuf:"varint,14,opt,name=page_index,json=pageIndex" json:"page_index,omitempty"` // 页编号
}

// Default values for RTBRequest_AdSlotInfo fields.
const (
	Default_RTBRequest_AdSlotInfo_ReqNum = uint32(1)
)

func (x *RTBRequest_AdSlotInfo) Reset() {
	*x = RTBRequest_AdSlotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest_AdSlotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest_AdSlotInfo) ProtoMessage() {}

func (x *RTBRequest_AdSlotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest_AdSlotInfo.ProtoReflect.Descriptor instead.
func (*RTBRequest_AdSlotInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 3}
}

func (x *RTBRequest_AdSlotInfo) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RTBRequest_AdSlotInfo) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *RTBRequest_AdSlotInfo) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *RTBRequest_AdSlotInfo) GetType() RTBRequest_AdSlotInfo_AdType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return RTBRequest_AdSlotInfo_AT_ALL
}

func (x *RTBRequest_AdSlotInfo) GetOpenScreen() bool {
	if x != nil && x.OpenScreen != nil {
		return *x.OpenScreen
	}
	return false
}

func (x *RTBRequest_AdSlotInfo) GetAdSlotType() RTBRequest_AdSlotInfo_AdSlotType {
	if x != nil && x.AdSlotType != nil {
		return *x.AdSlotType
	}
	return RTBRequest_AdSlotInfo_AST_BANNER
}

func (x *RTBRequest_AdSlotInfo) GetMinCpm() uint32 {
	if x != nil && x.MinCpm != nil {
		return *x.MinCpm
	}
	return 0
}

func (x *RTBRequest_AdSlotInfo) GetReqNum() uint32 {
	if x != nil && x.ReqNum != nil {
		return *x.ReqNum
	}
	return Default_RTBRequest_AdSlotInfo_ReqNum
}

func (x *RTBRequest_AdSlotInfo) GetExcludedLandingPageUrl() []string {
	if x != nil {
		return x.ExcludedLandingPageUrl
	}
	return nil
}

func (x *RTBRequest_AdSlotInfo) GetExcludedCategory() []int32 {
	if x != nil {
		return x.ExcludedCategory
	}
	return nil
}

func (x *RTBRequest_AdSlotInfo) GetAllowedCategory() []int32 {
	if x != nil {
		return x.AllowedCategory
	}
	return nil
}

func (x *RTBRequest_AdSlotInfo) GetPosid() []int32 {
	if x != nil {
		return x.Posid
	}
	return nil
}

func (x *RTBRequest_AdSlotInfo) GetPageIndex() int32 {
	if x != nil && x.PageIndex != nil {
		return *x.PageIndex
	}
	return 0
}

type RTBRequest_CustomizedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Val *string `protobuf:"bytes,2,opt,name=val" json:"val,omitempty"`
}

func (x *RTBRequest_CustomizedInfo) Reset() {
	*x = RTBRequest_CustomizedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBRequest_CustomizedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBRequest_CustomizedInfo) ProtoMessage() {}

func (x *RTBRequest_CustomizedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBRequest_CustomizedInfo.ProtoReflect.Descriptor instead.
func (*RTBRequest_CustomizedInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{0, 4}
}

func (x *RTBRequest_CustomizedInfo) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *RTBRequest_CustomizedInfo) GetVal() string {
	if x != nil && x.Val != nil {
		return *x.Val
	}
	return ""
}

type RTBResponse_AdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             *string  `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`                                                 // 广告位ID，必须和请求中的广告位ID一致
	AdId           *string  `protobuf:"bytes,2,opt,name=ad_id,json=adId" json:"ad_id,omitempty"`                                 // 广告ID，DSP方可自定义的值
	CreativeId     *string  `protobuf:"bytes,3,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`               // 广告创意ID，DSP方可自定义的值
	MaxCpm         *uint32  `protobuf:"varint,4,opt,name=max_cpm,json=maxCpm" json:"max_cpm,omitempty"`                          // 最高竞价，单位：分
	IsHtml         *bool    `protobuf:"varint,5,opt,name=is_html,json=isHtml" json:"is_html,omitempty"`                          // 是否是HTML物料，该值只能填false
	HtmlSnippet    *string  `protobuf:"bytes,6,opt,name=html_snippet,json=htmlSnippet" json:"html_snippet,omitempty"`            // HTML物料，已废弃
	Json           *string  `protobuf:"bytes,7,opt,name=json" json:"json,omitempty"`                                             // JSON物料
	ExtData        *string  `protobuf:"bytes,8,opt,name=ext_data,json=extData" json:"ext_data,omitempty"`                        // 附带在展现日志中的额外数据
	Posids         []int32  `protobuf:"varint,11,rep,name=posids" json:"posids,omitempty"`                                       // 信息流广告位有多个pos位置，可以针对每个不同pos分别报价；可以是请求数组的子集
	MaxCpms        []uint32 `protobuf:"varint,12,rep,name=max_cpms,json=maxCpms" json:"max_cpms,omitempty"`                      // 和posids组合使用，pos和max_cpms一一对应；报价数组，不同位置的报价，和posids对应
	AdSource       *string  `protobuf:"bytes,14,opt,name=ad_source,json=adSource" json:"ad_source,omitempty"`                    // 广告来源/角标
	Reviewed       *bool    `protobuf:"varint,15,opt,name=reviewed" json:"reviewed,omitempty"`                                   // 标记广告是否是预审过的，只针对预审流程使用
	StartTime      *uint32  `protobuf:"varint,16,opt,name=start_time,json=startTime" json:"start_time,omitempty"`                // 开屏参数: 开始时间戳
	ExpirationTime *uint32  `protobuf:"varint,17,opt,name=expiration_time,json=expirationTime" json:"expiration_time,omitempty"` // 开屏参数: 过期时间戳
}

func (x *RTBResponse_AdInfo) Reset() {
	*x = RTBResponse_AdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RTBResponse_AdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTBResponse_AdInfo) ProtoMessage() {}

func (x *RTBResponse_AdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTBResponse_AdInfo.ProtoReflect.Descriptor instead.
func (*RTBResponse_AdInfo) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RTBResponse_AdInfo) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetAdId() string {
	if x != nil && x.AdId != nil {
		return *x.AdId
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetMaxCpm() uint32 {
	if x != nil && x.MaxCpm != nil {
		return *x.MaxCpm
	}
	return 0
}

func (x *RTBResponse_AdInfo) GetIsHtml() bool {
	if x != nil && x.IsHtml != nil {
		return *x.IsHtml
	}
	return false
}

func (x *RTBResponse_AdInfo) GetHtmlSnippet() string {
	if x != nil && x.HtmlSnippet != nil {
		return *x.HtmlSnippet
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetJson() string {
	if x != nil && x.Json != nil {
		return *x.Json
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetExtData() string {
	if x != nil && x.ExtData != nil {
		return *x.ExtData
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetPosids() []int32 {
	if x != nil {
		return x.Posids
	}
	return nil
}

func (x *RTBResponse_AdInfo) GetMaxCpms() []uint32 {
	if x != nil {
		return x.MaxCpms
	}
	return nil
}

func (x *RTBResponse_AdInfo) GetAdSource() string {
	if x != nil && x.AdSource != nil {
		return *x.AdSource
	}
	return ""
}

func (x *RTBResponse_AdInfo) GetReviewed() bool {
	if x != nil && x.Reviewed != nil {
		return *x.Reviewed
	}
	return false
}

func (x *RTBResponse_AdInfo) GetStartTime() uint32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *RTBResponse_AdInfo) GetExpirationTime() uint32 {
	if x != nil && x.ExpirationTime != nil {
		return *x.ExpirationTime
	}
	return 0
}

type Ad_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  *uint32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`
	Height *uint32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
}

func (x *Ad_Size) Reset() {
	*x = Ad_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad_Size) ProtoMessage() {}

func (x *Ad_Size) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad_Size.ProtoReflect.Descriptor instead.
func (*Ad_Size) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Ad_Size) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Ad_Size) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

type Ad_AttachDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubTitle   *string  `protobuf:"bytes,1,opt,name=sub_title,json=subTitle" json:"sub_title,omitempty"`       // 附加创意标题，12字以内
	ButtonType *string  `protobuf:"bytes,2,req,name=button_type,json=buttonType" json:"button_type,omitempty"` // 附加创意类型，下载类广告填写“3”
	ButtonText *string  `protobuf:"bytes,3,opt,name=button_text,json=buttonText" json:"button_text,omitempty"` // 附加创意下载按钮文案，因合规要求，只能填写“立即下载”
	AttachUrl  *string  `protobuf:"bytes,4,opt,name=attach_url,json=attachUrl" json:"attach_url,omitempty"`    // 下载链接
	AppName    *string  `protobuf:"bytes,6,opt,name=app_name,json=appName" json:"app_name,omitempty"`          // APP名称
	ClickUrls  []string `protobuf:"bytes,7,rep,name=click_urls,json=clickUrls" json:"click_urls,omitempty"`    // 点击监测URL
}

func (x *Ad_AttachDetail) Reset() {
	*x = Ad_AttachDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad_AttachDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad_AttachDetail) ProtoMessage() {}

func (x *Ad_AttachDetail) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad_AttachDetail.ProtoReflect.Descriptor instead.
func (*Ad_AttachDetail) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Ad_AttachDetail) GetSubTitle() string {
	if x != nil && x.SubTitle != nil {
		return *x.SubTitle
	}
	return ""
}

func (x *Ad_AttachDetail) GetButtonType() string {
	if x != nil && x.ButtonType != nil {
		return *x.ButtonType
	}
	return ""
}

func (x *Ad_AttachDetail) GetButtonText() string {
	if x != nil && x.ButtonText != nil {
		return *x.ButtonText
	}
	return ""
}

func (x *Ad_AttachDetail) GetAttachUrl() string {
	if x != nil && x.AttachUrl != nil {
		return *x.AttachUrl
	}
	return ""
}

func (x *Ad_AttachDetail) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *Ad_AttachDetail) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

type Ad_Permission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"` // 权限名称
	Desc *string `protobuf:"bytes,2,opt,name=desc" json:"desc,omitempty"` // 权限描述
}

func (x *Ad_Permission) Reset() {
	*x = Ad_Permission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wifi_20220926_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad_Permission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad_Permission) ProtoMessage() {}

func (x *Ad_Permission) ProtoReflect() protoreflect.Message {
	mi := &file_wifi_20220926_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad_Permission.ProtoReflect.Descriptor instead.
func (*Ad_Permission) Descriptor() ([]byte, []int) {
	return file_wifi_20220926_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Ad_Permission) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Ad_Permission) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

var File_wifi_20220926_proto protoreflect.FileDescriptor

var file_wifi_20220926_proto_rawDesc = []byte{
	0x0a, 0x13, 0x77, 0x69, 0x66, 0x69, 0x5f, 0x32, 0x30, 0x32, 0x32, 0x30, 0x39, 0x32, 0x36, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x77, 0x69, 0x66, 0x69, 0x22, 0xa8, 0x13, 0x0a, 0x0a,
	0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54,
	0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e,
	0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x07, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77,
	0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a,
	0x08, 0x61, 0x64, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69,
	0x73, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x6c, 0x6f, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x6c, 0x61, 0x6c, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x20, 0x0a,
	0x0c, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x18, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x68, 0x74, 0x74, 0x70, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x07, 0x69, 0x73, 0x48, 0x74, 0x74, 0x70,
	0x73, 0x12, 0x4a, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x77, 0x69,
	0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x24, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xdf, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x22, 0x58, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e,
	0x0a, 0x0a, 0x44, 0x54, 0x5f, 0x55, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x54, 0x5f, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x44, 0x54, 0x5f, 0x50, 0x61, 0x64, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x54, 0x5f, 0x50,
	0x43, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x54, 0x5f, 0x54, 0x56, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x44, 0x54, 0x5f, 0x57, 0x61, 0x70, 0x10, 0x05, 0x1a, 0xd2, 0x01, 0x0a, 0x07, 0x41,
	0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x6b, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x6b, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x6b, 0x74, 0x5f, 0x73, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6b, 0x74, 0x53, 0x6e, 0x12, 0x17, 0x0a,
	0x07, 0x6d, 0x6b, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x6b, 0x74, 0x43, 0x61, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x6b, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6b, 0x74, 0x54, 0x61, 0x67, 0x1a,
	0xb4, 0x01, 0x0a, 0x06, 0x49, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d,
	0x65, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x61, 0x64, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x41, 0x64,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x55, 0x44,
	0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x55, 0x44,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x1a, 0x80, 0x06, 0x0a, 0x0a, 0x41, 0x64, 0x53, 0x6c, 0x6f,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x48, 0x0a, 0x0c,
	0x61, 0x64, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x53, 0x6c,
	0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x70,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x43, 0x70, 0x6d, 0x12,
	0x1a, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x31, 0x52, 0x06, 0x72, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x12, 0x39, 0x0a, 0x19, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x6f, 0x73, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x70,
	0x6f, 0x73, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x22, 0x53, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a,
	0x06, 0x41, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x54, 0x5f,
	0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x54,
	0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41,
	0x54, 0x5f, 0x4c, 0x42, 0x41, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x54, 0x5f, 0x4f, 0x4e,
	0x4c, 0x59, 0x53, 0x48, 0x4f, 0x57, 0x10, 0x04, 0x22, 0xbe, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x53, 0x54, 0x5f, 0x42,
	0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x53, 0x54, 0x5f, 0x4f,
	0x50, 0x45, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x41, 0x53, 0x54, 0x5f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x51, 0x55, 0x45,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x53, 0x54, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x53, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41,
	0x4c, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x53, 0x54, 0x5f,
	0x51, 0x55, 0x49, 0x54, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x53, 0x54, 0x5f, 0x50, 0x55,
	0x53, 0x48, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x43, 0x45, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x08, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x53,
	0x54, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x09, 0x1a, 0x34, 0x0a, 0x0e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x61, 0x6c, 0x22,
	0x84, 0x01, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4e,
	0x54, 0x5f, 0x55, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e,
	0x54, 0x5f, 0x45, 0x74, 0x68, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x4e, 0x54, 0x5f, 0x57, 0x69, 0x66, 0x69, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x54, 0x5f,
	0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x54,
	0x5f, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x32, 0x47, 0x10, 0x04, 0x12, 0x12,
	0x0a, 0x0e, 0x4e, 0x54, 0x5f, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x33, 0x47,
	0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x54, 0x5f, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61,
	0x72, 0x5f, 0x34, 0x47, 0x10, 0x06, 0x22, 0xa4, 0x04, 0x0a, 0x0b, 0x52, 0x54, 0x42, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x69, 0x66,
	0x69, 0x2e, 0x52, 0x54, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x73, 0x1a, 0x86, 0x03, 0x0a, 0x06, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x70, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x43, 0x70, 0x6d, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x73, 0x5f, 0x68, 0x74, 0x6d, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x69, 0x73, 0x48, 0x74, 0x6d, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x74, 0x6d, 0x6c, 0x5f,
	0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68,
	0x74, 0x6d, 0x6c, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6a, 0x73,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x73,
	0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6f, 0x73, 0x69, 0x64,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x70, 0x6d, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x43, 0x70, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd7, 0x0b,
	0x0a, 0x02, 0x41, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x64, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x64,
	0x35, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4d,
	0x64, 0x35, 0x73, 0x12, 0x3a, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x77, 0x69, 0x66,
	0x69, 0x2e, 0x41, 0x64, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2e,
	0x0a, 0x13, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x16, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x19, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x08,
	0x69, 0x6d, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x41, 0x64, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x07, 0x69,
	0x6d, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x57,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x59, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x41, 0x64, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x1a, 0x34, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xc6, 0x01, 0x0a, 0x0c, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75,
	0x74, 0x74, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x73, 0x1a, 0x34, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x45, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x03, 0x61, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x77, 0x69, 0x66, 0x69, 0x2e, 0x41, 0x64,
	0x52, 0x03, 0x61, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x42, 0x12,
	0x5a, 0x10, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x77, 0x69,
	0x66, 0x69,
}

var (
	file_wifi_20220926_proto_rawDescOnce sync.Once
	file_wifi_20220926_proto_rawDescData = file_wifi_20220926_proto_rawDesc
)

func file_wifi_20220926_proto_rawDescGZIP() []byte {
	file_wifi_20220926_proto_rawDescOnce.Do(func() {
		file_wifi_20220926_proto_rawDescData = protoimpl.X.CompressGZIP(file_wifi_20220926_proto_rawDescData)
	})
	return file_wifi_20220926_proto_rawDescData
}

var file_wifi_20220926_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_wifi_20220926_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_wifi_20220926_proto_goTypes = []interface{}{
	(RTBRequest_NetType)(0),               // 0: wifi.RTBRequest.NetType
	(RTBRequest_DeviceInfo_DeviceType)(0), // 1: wifi.RTBRequest.DeviceInfo.DeviceType
	(RTBRequest_AdSlotInfo_AdType)(0),     // 2: wifi.RTBRequest.AdSlotInfo.AdType
	(RTBRequest_AdSlotInfo_AdSlotType)(0), // 3: wifi.RTBRequest.AdSlotInfo.AdSlotType
	(*RTBRequest)(nil),                    // 4: wifi.RTBRequest
	(*RTBResponse)(nil),                   // 5: wifi.RTBResponse
	(*Ad)(nil),                            // 6: wifi.Ad
	(*CommonTemplate)(nil),                // 7: wifi.CommonTemplate
	(*RTBRequest_DeviceInfo)(nil),         // 8: wifi.RTBRequest.DeviceInfo
	(*RTBRequest_AppInfo)(nil),            // 9: wifi.RTBRequest.AppInfo
	(*RTBRequest_IdInfo)(nil),             // 10: wifi.RTBRequest.IdInfo
	(*RTBRequest_AdSlotInfo)(nil),         // 11: wifi.RTBRequest.AdSlotInfo
	(*RTBRequest_CustomizedInfo)(nil),     // 12: wifi.RTBRequest.CustomizedInfo
	(*RTBResponse_AdInfo)(nil),            // 13: wifi.RTBResponse.AdInfo
	(*Ad_Size)(nil),                       // 14: wifi.Ad.Size
	(*Ad_AttachDetail)(nil),               // 15: wifi.Ad.AttachDetail
	(*Ad_Permission)(nil),                 // 16: wifi.Ad.Permission
}
var file_wifi_20220926_proto_depIdxs = []int32{
	0,  // 0: wifi.RTBRequest.net_type:type_name -> wifi.RTBRequest.NetType
	8,  // 1: wifi.RTBRequest.device_info:type_name -> wifi.RTBRequest.DeviceInfo
	9,  // 2: wifi.RTBRequest.app_info:type_name -> wifi.RTBRequest.AppInfo
	10, // 3: wifi.RTBRequest.id_info:type_name -> wifi.RTBRequest.IdInfo
	11, // 4: wifi.RTBRequest.ad_slots:type_name -> wifi.RTBRequest.AdSlotInfo
	12, // 5: wifi.RTBRequest.customized_infos:type_name -> wifi.RTBRequest.CustomizedInfo
	13, // 6: wifi.RTBResponse.ad_infos:type_name -> wifi.RTBResponse.AdInfo
	15, // 7: wifi.Ad.attach_detail:type_name -> wifi.Ad.AttachDetail
	14, // 8: wifi.Ad.img_size:type_name -> wifi.Ad.Size
	16, // 9: wifi.Ad.app_permissions:type_name -> wifi.Ad.Permission
	6,  // 10: wifi.CommonTemplate.ads:type_name -> wifi.Ad
	1,  // 11: wifi.RTBRequest.DeviceInfo.type:type_name -> wifi.RTBRequest.DeviceInfo.DeviceType
	2,  // 12: wifi.RTBRequest.AdSlotInfo.type:type_name -> wifi.RTBRequest.AdSlotInfo.AdType
	3,  // 13: wifi.RTBRequest.AdSlotInfo.ad_slot_type:type_name -> wifi.RTBRequest.AdSlotInfo.AdSlotType
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_wifi_20220926_proto_init() }
func file_wifi_20220926_proto_init() {
	if File_wifi_20220926_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wifi_20220926_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest_DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest_IdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest_AdSlotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBRequest_CustomizedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RTBResponse_AdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad_AttachDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wifi_20220926_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad_Permission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wifi_20220926_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wifi_20220926_proto_goTypes,
		DependencyIndexes: file_wifi_20220926_proto_depIdxs,
		EnumInfos:         file_wifi_20220926_proto_enumTypes,
		MessageInfos:      file_wifi_20220926_proto_msgTypes,
	}.Build()
	File_wifi_20220926_proto = out.File
	file_wifi_20220926_proto_rawDesc = nil
	file_wifi_20220926_proto_goTypes = nil
	file_wifi_20220926_proto_depIdxs = nil
}
