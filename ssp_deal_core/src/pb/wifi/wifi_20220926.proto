
syntax = "proto2";

option go_package = "mh_proxy/pb/wifi";

package wifi;

message RTBRequest {

    enum NetType {
        NT_UnKnown = 0;     /* 未知网络 */
        NT_Ethernet = 1;    /* 以太网 */
        NT_Wifi = 2;        /* WiFi */
        NT_Cellular = 3;    /* 1G */
        NT_Cellular_2G = 4; /* 2G */
        NT_Cellular_3G = 5; /* 3G */
        NT_Cellular_4G = 6; /* 4G */
    }

    message DeviceInfo {
        enum DeviceType {
            DT_UnKnown = 0;
            DT_Phone = 1;
            DT_Pad = 2;
            DT_PC = 3;
            DT_TV = 4;
            DT_Wap = 5;
        }
        optional DeviceType type = 1;
        optional uint32 screen_width = 2;   /* 屏幕宽度 */
        optional uint32 screen_height = 3;  /* 屏幕高度 */
        optional uint32 screen_density = 4; /* 屏幕密度 */
        optional bool horizontal = 5;       /* 横竖屏标记 */
        optional string vendor = 6;         /* 设备生产商 */
        optional string model = 7;          /* 设备型号 */
    }

    message AppInfo {
        optional string app_id = 1;
        optional string app_name = 2;
        optional string app_version = 3;
        optional string pkg_name = 4;
        optional string mkt = 5;     /* 应用商店 */
        optional string mkt_sn = 6;  /* app在商店内的编号 */
        optional string mkt_cat = 7; /* 分类 */
        optional string mkt_tag = 8; /* 标签 */
    }

    message IdInfo {
        optional string imei = 1;
        optional string mac = 2;
        optional string android_id = 3;
        optional string android_adid = 4; /* android advertising ID */
        optional string idfa = 5;         /* iOS(>=6) */
        optional string openUDID = 6;     /* iOS(<6) */
        optional string oaid = 7;
    }

    message AdSlotInfo {
        enum AdType {
            AT_ALL = 0;      /* 无限制 */
            AT_REDIRECT = 1; /* 点击跳转 */
            AT_DOWNLOAD = 2; /* 点击下载 */
            AT_LBA = 3;      /* LBA */
            AT_ONLYSHOW = 4; /* 仅展示，不可点击 */
          }
      
          enum AdSlotType {
            AST_BANNER = 0;        /* banner广告位，已不使用 */
            AST_OPEN_SCREEN = 1;   /* 开屏广告位 */
            AST_TABLE_PLAQUE = 2;  /* 插屏广告位 */
            AST_FEEDS = 3;         /* feeds流广告位 */
            AST_INTEGRAL_WALL = 4; /* 积分墙广告位 */
            AST_QUIT = 5;          /* 退出广告位 */
            AST_PUSH = 6;          /* push广告 */
            AST_NOTICE = 7;        /* 小喇叭广告位 */
            AST_BACKDOWNLOAD = 8;  /* 预下载广告位 */
            AST_VIDEO = 9;         /* 视频广告 */
          }
      
          optional string id = 1;        /* DSP的广告位ID */
          optional uint32 width = 2;     /* 广告位的宽度，并非物料图片的宽度 */
          optional uint32 height = 3;    /* 广告位的高度，并非物料图片的高度 */
          optional AdType type = 4;      /* 广告位能接受的广告类型 */
          optional bool open_screen = 5; /* 开屏标记，请使用ad_slot_type判断广告位类型 */
          optional AdSlotType ad_slot_type = 6; /* 广告位类型 */
          optional uint32 min_cpm = 7;          /* 广告位底价 */
          optional uint32 req_num = 8 [default = 1]; /* 广告位请求的广告条数，默认请求1条广告 */
          repeated string excluded_landing_page_url = 9; /* 不允许的落地页url */
          repeated int32 excluded_category = 10; /* 不允许的行业类型 */
          repeated int32 allowed_category = 11;  /* 仅允许的行业类型 */
          repeated int32 posid = 13;
          optional int32 page_index = 14; /* 页编号 */
    }

    message CustomizedInfo {
        optional string key = 1;
        optional string val = 2;
    } /* 定制化标签，当定投某些固定APP时，key="InstallApps"，value是安装的包名列表，使用分号分割 */
    
    required string sid = 1;        /* 唯一检索ID */
    optional string client_ip = 2;  /* APP的IP地址 */
    optional string user_agent = 3; /* APP的User-Agent */
    optional string language = 4;   /* 如：zh-CN */
    optional NetType net_type = 5;  /* APP所处的网络类型 */
    optional string os = 6;         /* android、ios等 */
    optional string os_version = 7;
    optional DeviceInfo device_info = 8;
    optional AppInfo app_info = 9;
    optional IdInfo id_info = 10;      /* 设备ID信息 */
    repeated AdSlotInfo ad_slots = 11; /* 广告位信息，默认是一个 */
    optional bool is_test = 12 [default = false]; /* 测试请求标记 */
    optional double longitude = 14;
    optional double latitude = 15;
    optional uint32 lalo_type = 16;    /* 0-高德, 1-百度, 2-腾讯, 3-谷歌 */
    optional string media_index = 23;  /* 媒体的序号 */
    repeated string type_id_list = 24; /* 广告支持的广告样式的列表，为空表示不限制 */
    optional bool is_https = 25 [default = false]; //是否https, true的话，返回的物料地址和上报地址都必须是https的，否则会被过滤
    repeated CustomizedInfo customized_infos = 28;
    repeated string app_list = 36; /* 给DSP方传包名对应的ID号 */
}

message RTBResponse {
    message AdInfo {
        optional string id = 1;               /* 广告位ID，必须和请求中的广告位ID一致 */
        optional string ad_id = 2;            /* 广告ID，DSP方可自定义的值 */
        optional string creative_id = 3;      /* 广告创意ID，DSP方可自定义的值 */
        optional uint32 max_cpm = 4;          /* 最高竞价，单位：分 */
        optional bool is_html = 5;            /* 是否是HTML物料，该值只能填false */
        optional string html_snippet = 6;     /* HTML物料，已废弃 */
        optional string json = 7;             /* JSON物料 */
        optional string ext_data = 8;         /* 附带在展现日志中的额外数据 */
        repeated int32 posids = 11;           /* 信息流广告位有多个pos位置，可以针对每个不同pos分别报价；可以是请求数组的子集 */
        repeated uint32 max_cpms = 12;        /* 和posids组合使用，pos和max_cpms一一对应；报价数组，不同位置的报价，和posids对应 */
        optional string ad_source = 14;       /* 广告来源/角标 */
        optional bool reviewed = 15;          /* 标记广告是否是预审过的，只针对预审流程使用 */
        optional uint32 start_time = 16;      /* 开屏参数: 开始时间戳 */
        optional uint32 expiration_time = 17; /* 开屏参数: 过期时间戳 */
    }

    required string sid = 1;                /* 必须和请求中的sid是同一个值 */
    repeated AdInfo ad_infos = 2;           /* 召回的广告，无召回时无需装填，一个广告位返回多个广告时，都填充在该数组 */
    optional string debug_info = 3;         /* debug信息 */
    optional uint32 process_time_ms = 4;    /* 处理耗时，单位：ms */
}

message Ad {
    message Size {
      optional uint32 width = 1;
      optional uint32 height = 2;
    }
  
    message AttachDetail { /* 附加创意的字段 */
      optional string sub_title = 1;    /* 附加创意标题，12字以内 */
      required string button_type = 2;  /* 附加创意类型，下载类广告填写“3” */
      optional string button_text = 3;  /* 附加创意下载按钮文案，因合规要求，只能填写“立即下载” */
      optional string attach_url = 4;   /* 下载链接 */
      optional string app_name = 6;     /* APP名称 */
      repeated string click_urls = 7;   /* 点击监测URL */
    }
  
    message Permission {
      optional string name = 1; /* 权限名称 */
      optional string desc = 2; /* 权限描述 */
    }
  
    optional string debug_info = 1;           /* debug信息 */
    optional string adid = 3;                 /* 广告ID，针对素材预审接入方式，该值必须填预审通过后分配的creative_id，否则广告将不会被展现 */
    required string ad_type = 4;              /* 广告类型：redirect/跳转,download/下载; */
    optional string landing_page_url = 5;     /* 跳转地址，当ad_type为redirect时填写，需要以http://或者https://开头的URL，如果地址中包含%%EXT%%宏，可替换为ext_info字段内容 */
    optional string deeplink_url = 6;         /* deeplink调起APP的URL，deeplink广告的ad_type填写为redirect */
    optional string download_url = 7;         /* APP的下载URL，当ad_type为download时填写 */
    optional string title = 8;                /* 标题 */
    optional string content = 9;              /* 预留字段 */
    optional string desc = 10;                /* 预留字段 */
    optional string app_name = 11;            /* 只下载类型填写：应用名称 */
    optional string pkg_name = 12;            /* 只下载类型填写：应用包名 */
    optional string app_icon = 13;            /* 只下载类型填写：应用图标，需要以http://或者https://开头的URL */
    repeated string image_urls = 15;          /* 图片URL，需要以http://或者https://开头的URL */
    repeated string image_md5s = 16;          /* 图片的MD5值，非图片URL的MD5值 */
    optional AttachDetail attach_detail = 17; /* 附加创意的详细信息 */
    repeated string click_urls = 18;          /* 广告点击时会上报的监测URL，需要以http://或者https://开头的URL */
    repeated string deeplink_click_urls = 19; /* deeplink广告点击时上报的监测URL，需要以http://或者https://开头的URL */
    repeated string show_urls = 20;           /* 展现上报的监测URL，广告在APP页面上渲染完成后会上报，需要以http://或者https://开头的URL */
    repeated string download_urls = 22;       /* 下载类广告APP开始下载时上报的监测URL */
    repeated string downloaded_urls = 23;     /* 下载类广告APP下载完成时上报的监测URL */
    repeated string installed_urls = 24;      /* 下载类广告APP安装完成时上报的监测URL */
    repeated int64 category = 25;             /* 广告所属行业的ID */
    optional Size img_size = 35;              /* 图片宽高尺寸 */
    repeated string category_name = 36;       /* 广告所属行业的中文名 */
    optional string video_start = 38;         /* 视频开始播放时上报的监测URL */
    optional string video_end = 39;           /* 视频播放暂停/结束时上报的监控URL */
    optional string video_url = 40;           /* 视频URL */
    optional string download_md5 = 41;        /* 下载md5 */
    optional string ext_info = 59;            /* 当前素材的扩展信息，会替换到landing_page_url中 */
    optional string pkg_size = 63;            /* 只下载类型填写：应用包大小 */
    optional string app_version = 87;         /* APP版本号 */
    optional string app_developer = 88;       /* APP开发者名称 */
    repeated Permission app_permissions = 89; /* APP权限列表 */
    optional string app_privacy = 90;         /* APP隐私条款链接 */
    optional string app_permission_url = 91;  /* 如果APP权限列表是一个URL，把URL填在这个字段中 */
}

message CommonTemplate { /* 将CommonTemplate转换成Json格式String后，填入上面第7个字段json内 */
    repeated Ad ads = 2;          /* 广告信息 */
    optional string type_id = 3;  /* 广告的模板信息 */
}
