// Copyright 2020 Mucang Inc. All Rights Reserved.
// 本文件描述API接口版本：1.9

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.0
// source: mucang_1.9.proto

package mucang

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 设备类型
type BidRequest_Device_DeviceType int32

const (
	BidRequest_Device_DEVICE_UNKNOWN BidRequest_Device_DeviceType = 0
	BidRequest_Device_PHONE          BidRequest_Device_DeviceType = 1 // 手机。
	BidRequest_Device_TABLET         BidRequest_Device_DeviceType = 2 // 平板。
	BidRequest_Device_TV             BidRequest_Device_DeviceType = 3 // 智能电视。
)

// Enum value maps for BidRequest_Device_DeviceType.
var (
	BidRequest_Device_DeviceType_name = map[int32]string{
		0: "DEVICE_UNKNOWN",
		1: "PHONE",
		2: "TABLET",
		3: "TV",
	}
	BidRequest_Device_DeviceType_value = map[string]int32{
		"DEVICE_UNKNOWN": 0,
		"PHONE":          1,
		"TABLET":         2,
		"TV":             3,
	}
)

func (x BidRequest_Device_DeviceType) Enum() *BidRequest_Device_DeviceType {
	p := new(BidRequest_Device_DeviceType)
	*p = x
	return p
}

func (x BidRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[0].Descriptor()
}

func (BidRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[0]
}

func (x BidRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_DeviceType.Descriptor instead.
func (BidRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0, 0}
}

type BidRequest_Device_Platform int32

const (
	BidRequest_Device_OTHER   BidRequest_Device_Platform = 0 //其它
	BidRequest_Device_IPHONE  BidRequest_Device_Platform = 1 //iOS
	BidRequest_Device_ANDROID BidRequest_Device_Platform = 2 //android
)

// Enum value maps for BidRequest_Device_Platform.
var (
	BidRequest_Device_Platform_name = map[int32]string{
		0: "OTHER",
		1: "IPHONE",
		2: "ANDROID",
	}
	BidRequest_Device_Platform_value = map[string]int32{
		"OTHER":   0,
		"IPHONE":  1,
		"ANDROID": 2,
	}
)

func (x BidRequest_Device_Platform) Enum() *BidRequest_Device_Platform {
	p := new(BidRequest_Device_Platform)
	*p = x
	return p
}

func (x BidRequest_Device_Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[1].Descriptor()
}

func (BidRequest_Device_Platform) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[1]
}

func (x BidRequest_Device_Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_Platform.Descriptor instead.
func (BidRequest_Device_Platform) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0, 1}
}

// 网络连接类型
type BidRequest_Device_Network_ConnectionType int32

const (
	BidRequest_Device_Network_UNKNOWN BidRequest_Device_Network_ConnectionType = 0 //无法探测当前网络状态
	BidRequest_Device_Network_G2      BidRequest_Device_Network_ConnectionType = 1 //蜂窝数据2G网络
	BidRequest_Device_Network_G3      BidRequest_Device_Network_ConnectionType = 2 //蜂窝数据3G网络
	BidRequest_Device_Network_G4      BidRequest_Device_Network_ConnectionType = 3 //蜂窝数据4G网络
	BidRequest_Device_Network_G5      BidRequest_Device_Network_ConnectionType = 4 //蜂窝数据5G网络
	BidRequest_Device_Network_WIFI    BidRequest_Device_Network_ConnectionType = 5 //Wi-Fi网络接入
)

// Enum value maps for BidRequest_Device_Network_ConnectionType.
var (
	BidRequest_Device_Network_ConnectionType_name = map[int32]string{
		0: "UNKNOWN",
		1: "G2",
		2: "G3",
		3: "G4",
		4: "G5",
		5: "WIFI",
	}
	BidRequest_Device_Network_ConnectionType_value = map[string]int32{
		"UNKNOWN": 0,
		"G2":      1,
		"G3":      2,
		"G4":      3,
		"G5":      4,
		"WIFI":    5,
	}
)

func (x BidRequest_Device_Network_ConnectionType) Enum() *BidRequest_Device_Network_ConnectionType {
	p := new(BidRequest_Device_Network_ConnectionType)
	*p = x
	return p
}

func (x BidRequest_Device_Network_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_Network_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[2].Descriptor()
}

func (BidRequest_Device_Network_ConnectionType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[2]
}

func (x BidRequest_Device_Network_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_Network_ConnectionType.Descriptor instead.
func (BidRequest_Device_Network_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

// 移动运营商类型
type BidRequest_Device_Network_OperatorType int32

const (
	BidRequest_Device_Network_OTHER BidRequest_Device_Network_OperatorType = 0 //未知的运营商
	BidRequest_Device_Network_M     BidRequest_Device_Network_OperatorType = 1 //中国移动
	BidRequest_Device_Network_C     BidRequest_Device_Network_OperatorType = 2 //中国电信
	BidRequest_Device_Network_T     BidRequest_Device_Network_OperatorType = 3 //中国联通
)

// Enum value maps for BidRequest_Device_Network_OperatorType.
var (
	BidRequest_Device_Network_OperatorType_name = map[int32]string{
		0: "OTHER",
		1: "M",
		2: "C",
		3: "T",
	}
	BidRequest_Device_Network_OperatorType_value = map[string]int32{
		"OTHER": 0,
		"M":     1,
		"C":     2,
		"T":     3,
	}
)

func (x BidRequest_Device_Network_OperatorType) Enum() *BidRequest_Device_Network_OperatorType {
	p := new(BidRequest_Device_Network_OperatorType)
	*p = x
	return p
}

func (x BidRequest_Device_Network_OperatorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_Network_OperatorType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Device_Network_OperatorType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[3]
}

func (x BidRequest_Device_Network_OperatorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_Device_Network_OperatorType.Descriptor instead.
func (BidRequest_Device_Network_OperatorType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0, 0, 1}
}

//性别信息
type BidRequest_User_Gender int32

const (
	BidRequest_User_UNKNOWN BidRequest_User_Gender = 0 //未知
	BidRequest_User_Male    BidRequest_User_Gender = 1 //男性
	BidRequest_User_Female  BidRequest_User_Gender = 2 //女性
)

// Enum value maps for BidRequest_User_Gender.
var (
	BidRequest_User_Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "Male",
		2: "Female",
	}
	BidRequest_User_Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"Male":    1,
		"Female":  2,
	}
)

func (x BidRequest_User_Gender) Enum() *BidRequest_User_Gender {
	p := new(BidRequest_User_Gender)
	*p = x
	return p
}

func (x BidRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[4].Descriptor()
}

func (BidRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[4]
}

func (x BidRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_User_Gender.Descriptor instead.
func (BidRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 2, 0}
}

// 广告的类型。
type BidRequest_AdSlot_AdType int32

const (
	BidRequest_AdSlot_BANNER       BidRequest_AdSlot_AdType = 0 // 横幅广告。
	BidRequest_AdSlot_INTERSTITIAL BidRequest_AdSlot_AdType = 1 // 插屏广告。
	BidRequest_AdSlot_SPLASH       BidRequest_AdSlot_AdType = 2 // 开屏广告
	BidRequest_AdSlot_STREAM       BidRequest_AdSlot_AdType = 3 // 信息流广告。
)

// Enum value maps for BidRequest_AdSlot_AdType.
var (
	BidRequest_AdSlot_AdType_name = map[int32]string{
		0: "BANNER",
		1: "INTERSTITIAL",
		2: "SPLASH",
		3: "STREAM",
	}
	BidRequest_AdSlot_AdType_value = map[string]int32{
		"BANNER":       0,
		"INTERSTITIAL": 1,
		"SPLASH":       2,
		"STREAM":       3,
	}
)

func (x BidRequest_AdSlot_AdType) Enum() *BidRequest_AdSlot_AdType {
	p := new(BidRequest_AdSlot_AdType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[5].Descriptor()
}

func (BidRequest_AdSlot_AdType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[5]
}

func (x BidRequest_AdSlot_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_AdSlot_AdType.Descriptor instead.
func (BidRequest_AdSlot_AdType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3, 0}
}

// 广告出现的位置。
type BidRequest_AdSlot_Position int32

const (
	BidRequest_AdSlot_TOP        BidRequest_AdSlot_Position = 0 // 顶部。
	BidRequest_AdSlot_BOTTOM     BidRequest_AdSlot_Position = 1 // 底部。
	BidRequest_AdSlot_FLOW       BidRequest_AdSlot_Position = 2 // 信息流内。
	BidRequest_AdSlot_MIDDLE     BidRequest_AdSlot_Position = 3 // 中部(插屏广告专用)。
	BidRequest_AdSlot_FULLSCREEN BidRequest_AdSlot_Position = 4 // 全屏。
)

// Enum value maps for BidRequest_AdSlot_Position.
var (
	BidRequest_AdSlot_Position_name = map[int32]string{
		0: "TOP",
		1: "BOTTOM",
		2: "FLOW",
		3: "MIDDLE",
		4: "FULLSCREEN",
	}
	BidRequest_AdSlot_Position_value = map[string]int32{
		"TOP":        0,
		"BOTTOM":     1,
		"FLOW":       2,
		"MIDDLE":     3,
		"FULLSCREEN": 4,
	}
)

func (x BidRequest_AdSlot_Position) Enum() *BidRequest_AdSlot_Position {
	p := new(BidRequest_AdSlot_Position)
	*p = x
	return p
}

func (x BidRequest_AdSlot_Position) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_Position) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[6].Descriptor()
}

func (BidRequest_AdSlot_Position) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[6]
}

func (x BidRequest_AdSlot_Position) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_AdSlot_Position.Descriptor instead.
func (BidRequest_AdSlot_Position) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3, 1}
}

// 可选的创意类型。
type BidRequest_AdSlot_CreativeType int32

const (
	BidRequest_AdSlot_TEXT      BidRequest_AdSlot_CreativeType = 0 // 文字。
	BidRequest_AdSlot_IMAGE     BidRequest_AdSlot_CreativeType = 1 // 图片。
	BidRequest_AdSlot_GIF       BidRequest_AdSlot_CreativeType = 2 // 动图。
	BidRequest_AdSlot_HTML      BidRequest_AdSlot_CreativeType = 3 // HTML.
	BidRequest_AdSlot_VIDEO     BidRequest_AdSlot_CreativeType = 4 // 视频。
	BidRequest_AdSlot_TEXT_ICON BidRequest_AdSlot_CreativeType = 5 // 图文。
)

// Enum value maps for BidRequest_AdSlot_CreativeType.
var (
	BidRequest_AdSlot_CreativeType_name = map[int32]string{
		0: "TEXT",
		1: "IMAGE",
		2: "GIF",
		3: "HTML",
		4: "VIDEO",
		5: "TEXT_ICON",
	}
	BidRequest_AdSlot_CreativeType_value = map[string]int32{
		"TEXT":      0,
		"IMAGE":     1,
		"GIF":       2,
		"HTML":      3,
		"VIDEO":     4,
		"TEXT_ICON": 5,
	}
)

func (x BidRequest_AdSlot_CreativeType) Enum() *BidRequest_AdSlot_CreativeType {
	p := new(BidRequest_AdSlot_CreativeType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[7].Descriptor()
}

func (BidRequest_AdSlot_CreativeType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[7]
}

func (x BidRequest_AdSlot_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_AdSlot_CreativeType.Descriptor instead.
func (BidRequest_AdSlot_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3, 2}
}

// 创意的交互类型
type BidRequest_AdSlot_InteractionType int32

const (
	BidRequest_AdSlot_NO_INTERACTION BidRequest_AdSlot_InteractionType = 0 // 无动作，针对有些开屏不支持点击。
	BidRequest_AdSlot_SURFING        BidRequest_AdSlot_InteractionType = 1 // 使用浏览器打开网页。
	BidRequest_AdSlot_IN_APP         BidRequest_AdSlot_InteractionType = 2 // 在app中打开。
	BidRequest_AdSlot_DOWNLOAD       BidRequest_AdSlot_InteractionType = 3 // 下载应用。
	BidRequest_AdSlot_DIALING        BidRequest_AdSlot_InteractionType = 4 // 拨打电话。
	BidRequest_AdSlot_MESSAGE        BidRequest_AdSlot_InteractionType = 5 // 发送短信。
	BidRequest_AdSlot_EMAIL          BidRequest_AdSlot_InteractionType = 6 // 发送邮件。
)

// Enum value maps for BidRequest_AdSlot_InteractionType.
var (
	BidRequest_AdSlot_InteractionType_name = map[int32]string{
		0: "NO_INTERACTION",
		1: "SURFING",
		2: "IN_APP",
		3: "DOWNLOAD",
		4: "DIALING",
		5: "MESSAGE",
		6: "EMAIL",
	}
	BidRequest_AdSlot_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 0,
		"SURFING":        1,
		"IN_APP":         2,
		"DOWNLOAD":       3,
		"DIALING":        4,
		"MESSAGE":        5,
		"EMAIL":          6,
	}
)

func (x BidRequest_AdSlot_InteractionType) Enum() *BidRequest_AdSlot_InteractionType {
	p := new(BidRequest_AdSlot_InteractionType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[8].Descriptor()
}

func (BidRequest_AdSlot_InteractionType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[8]
}

func (x BidRequest_AdSlot_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidRequest_AdSlot_InteractionType.Descriptor instead.
func (BidRequest_AdSlot_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3, 3}
}

// 可选的创意类型。
type BidResponse_Ad_CreativeType int32

const (
	BidResponse_Ad_TEXT      BidResponse_Ad_CreativeType = 0 // 文字。
	BidResponse_Ad_IMAGE     BidResponse_Ad_CreativeType = 1 // 图片。
	BidResponse_Ad_GIF       BidResponse_Ad_CreativeType = 2 // 动图。
	BidResponse_Ad_HTML      BidResponse_Ad_CreativeType = 3 // HTML.
	BidResponse_Ad_VIDEO     BidResponse_Ad_CreativeType = 4 // 视频。
	BidResponse_Ad_TEXT_ICON BidResponse_Ad_CreativeType = 5 // 图文。
)

// Enum value maps for BidResponse_Ad_CreativeType.
var (
	BidResponse_Ad_CreativeType_name = map[int32]string{
		0: "TEXT",
		1: "IMAGE",
		2: "GIF",
		3: "HTML",
		4: "VIDEO",
		5: "TEXT_ICON",
	}
	BidResponse_Ad_CreativeType_value = map[string]int32{
		"TEXT":      0,
		"IMAGE":     1,
		"GIF":       2,
		"HTML":      3,
		"VIDEO":     4,
		"TEXT_ICON": 5,
	}
)

func (x BidResponse_Ad_CreativeType) Enum() *BidResponse_Ad_CreativeType {
	p := new(BidResponse_Ad_CreativeType)
	*p = x
	return p
}

func (x BidResponse_Ad_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[9].Descriptor()
}

func (BidResponse_Ad_CreativeType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[9]
}

func (x BidResponse_Ad_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Ad_CreativeType.Descriptor instead.
func (BidResponse_Ad_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 0}
}

// 创意的交互类型
type BidResponse_Ad_InteractionType int32

const (
	BidResponse_Ad_NO_INTERACTION BidResponse_Ad_InteractionType = 0 // 无动作，针对有些开屏不支持点击。
	BidResponse_Ad_SURFING        BidResponse_Ad_InteractionType = 1 // 使用浏览器打开网页。
	BidResponse_Ad_IN_APP         BidResponse_Ad_InteractionType = 2 // 在app中打开。
	BidResponse_Ad_DOWNLOAD       BidResponse_Ad_InteractionType = 3 // 下载应用。
	BidResponse_Ad_DIALING        BidResponse_Ad_InteractionType = 4 // 拨打电话。
	BidResponse_Ad_MESSAGE        BidResponse_Ad_InteractionType = 5 // 发送短信。
	BidResponse_Ad_EMAIL          BidResponse_Ad_InteractionType = 6 // 发送邮件。
	BidResponse_Ad_WECHAT_APPLET  BidResponse_Ad_InteractionType = 7 // 微信小程序。
)

// Enum value maps for BidResponse_Ad_InteractionType.
var (
	BidResponse_Ad_InteractionType_name = map[int32]string{
		0: "NO_INTERACTION",
		1: "SURFING",
		2: "IN_APP",
		3: "DOWNLOAD",
		4: "DIALING",
		5: "MESSAGE",
		6: "EMAIL",
		7: "WECHAT_APPLET",
	}
	BidResponse_Ad_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 0,
		"SURFING":        1,
		"IN_APP":         2,
		"DOWNLOAD":       3,
		"DIALING":        4,
		"MESSAGE":        5,
		"EMAIL":          6,
		"WECHAT_APPLET":  7,
	}
)

func (x BidResponse_Ad_InteractionType) Enum() *BidResponse_Ad_InteractionType {
	p := new(BidResponse_Ad_InteractionType)
	*p = x
	return p
}

func (x BidResponse_Ad_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_mucang_1_9_proto_enumTypes[10].Descriptor()
}

func (BidResponse_Ad_InteractionType) Type() protoreflect.EnumType {
	return &file_mucang_1_9_proto_enumTypes[10]
}

func (x BidResponse_Ad_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Ad_InteractionType.Descriptor instead.
func (BidResponse_Ad_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 1}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId  string             `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`    //广告请求 id
	ApiVersion string             `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"` //必填。此API的版本。
	Device     *BidRequest_Device `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`                           //设备信息
	App        *BidRequest_App    `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`                                 //应用信息
	Geo        *BidRequest_Geo    `protobuf:"bytes,5,opt,name=geo,proto3" json:"geo,omitempty"`                                 //位置信息
	User       *BidRequest_User   `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`                               //用户信息
	Adslots    *BidRequest_AdSlot `protobuf:"bytes,7,opt,name=adslots,proto3" json:"adslots,omitempty"`                         //广告位 id
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetAdslots() *BidRequest_AdSlot {
	if x != nil {
		return x.Adslots
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string            `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` //必填。BidRequest中所带的request_id。
	Ads       []*BidResponse_Ad `protobuf:"bytes,2,rep,name=ads,proto3" json:"ads,omitempty"`                              //可选。竞价广告列表，与adslots对应。
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BidResponse) GetAds() []*BidResponse_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

type WinNotice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId   string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`          //必填。BidRequest中所带的request_id。
	AdId        string `protobuf:"bytes,3,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                         //必填。广告 id
	BidPrice    uint64 `protobuf:"varint,4,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"`            //必填。广告出价, 单位为分/cpm。
	BidWinPrice uint64 `protobuf:"varint,5,opt,name=bid_win_price,json=bidWinPrice,proto3" json:"bid_win_price,omitempty"` //必填。广告实际赢价, 单位为分/cpm。
}

func (x *WinNotice) Reset() {
	*x = WinNotice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WinNotice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WinNotice) ProtoMessage() {}

func (x *WinNotice) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WinNotice.ProtoReflect.Descriptor instead.
func (*WinNotice) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{2}
}

func (x *WinNotice) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *WinNotice) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *WinNotice) GetBidPrice() uint64 {
	if x != nil {
		return x.BidPrice
	}
	return 0
}

func (x *WinNotice) GetBidWinPrice() uint64 {
	if x != nil {
		return x.BidWinPrice
	}
	return 0
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idfa             string                       `protobuf:"bytes,1,opt,name=idfa,proto3" json:"idfa,omitempty"`                                                    // 必填！iOS设备的IDFA
	Imei             string                       `protobuf:"bytes,2,opt,name=imei,proto3" json:"imei,omitempty"`                                                    // 必填！Android手机设备的IMEI
	AndroidId        string                       `protobuf:"bytes,3,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`                         // 必填！Android手机设备系统ID
	Oaid             string                       `protobuf:"bytes,4,opt,name=oaid,proto3" json:"oaid,omitempty"`                                                    // oaid 移动安全联盟开放匿名设备标识符
	IdfaMd5          string                       `protobuf:"bytes,5,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`                               // IDFA MD5 值
	ImeiMd5          string                       `protobuf:"bytes,6,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`                               // android IMEI MD5 值
	AndroididMd5     string                       `protobuf:"bytes,7,opt,name=androidid_md5,json=androididMd5,proto3" json:"androidid_md5,omitempty"`                // android id MD5 值
	OaidMd5          string                       `protobuf:"bytes,8,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`                               // oaid MD5 值
	Type             BidRequest_Device_DeviceType `protobuf:"varint,9,opt,name=type,proto3,enum=mucang.BidRequest_Device_DeviceType" json:"type,omitempty"`          // 必填。设备类型。
	OsVersion        string                       `protobuf:"bytes,10,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`                        // 可选。操作系统版本。
	Vendor           string                       `protobuf:"bytes,11,opt,name=vendor,proto3" json:"vendor,omitempty"`                                               // 可选。设备厂商，如Apple, Samsung。
	Model            string                       `protobuf:"bytes,12,opt,name=model,proto3" json:"model,omitempty"`                                                 // 可选。设备型号，如iPhone5s, Galaxy。
	Ua               string                       `protobuf:"bytes,13,opt,name=ua,proto3" json:"ua,omitempty"`                                                       // 可选。浏览器信息。
	Ip               string                       `protobuf:"bytes,14,opt,name=ip,proto3" json:"ip,omitempty"`                                                       // 可选。设备的ip。
	Mac              string                       `protobuf:"bytes,15,opt,name=mac,proto3" json:"mac,omitempty"`                                                     // 可选。设备的mac地址。
	Network          *BidRequest_Device_Network   `protobuf:"bytes,16,opt,name=network,proto3" json:"network,omitempty"`                                             //可选。网络信息
	Platform         BidRequest_Device_Platform   `protobuf:"varint,17,opt,name=platform,proto3,enum=mucang.BidRequest_Device_Platform" json:"platform,omitempty"`   //可选。设备平台
	Brand            string                       `protobuf:"bytes,18,opt,name=brand,proto3" json:"brand,omitempty"`                                                 //可选。设备品牌品牌
	BootMark         string                       `protobuf:"bytes,19,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`                           //可选。反作弊字段
	UpdateMark       string                       `protobuf:"bytes,20,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`                     //可选。反作弊字段
	ScreenHeight     int32                        `protobuf:"varint,21,opt,name=screen_height,json=screenHeight,proto3" json:"screen_height,omitempty"`              //可选。屏幕高度（像素）
	ScreenWidth      int32                        `protobuf:"varint,22,opt,name=screen_width,json=screenWidth,proto3" json:"screen_width,omitempty"`                 //可选。屏幕宽度（像素）
	DeviceInitTime   string                       `protobuf:"bytes,23,opt,name=device_init_time,json=deviceInitTime,proto3" json:"device_init_time,omitempty"`       //设备初始化时间
	SystemUpdateTime string                       `protobuf:"bytes,24,opt,name=system_update_time,json=systemUpdateTime,proto3" json:"system_update_time,omitempty"` //系统更新时间
	SystemBootTime   string                       `protobuf:"bytes,25,opt,name=system_boot_time,json=systemBootTime,proto3" json:"system_boot_time,omitempty"`       //系统启动时间
	Aaid             string                       `protobuf:"bytes,26,opt,name=aaid,proto3" json:"aaid,omitempty"`                                                   //阿里匿名广告标识符
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroididMd5() string {
	if x != nil {
		return x.AndroididMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetType() BidRequest_Device_DeviceType {
	if x != nil {
		return x.Type
	}
	return BidRequest_Device_DEVICE_UNKNOWN
}

func (x *BidRequest_Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *BidRequest_Device) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetNetwork() *BidRequest_Device_Network {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *BidRequest_Device) GetPlatform() BidRequest_Device_Platform {
	if x != nil {
		return x.Platform
	}
	return BidRequest_Device_OTHER
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetDeviceInitTime() string {
	if x != nil {
		return x.DeviceInitTime
	}
	return ""
}

func (x *BidRequest_Device) GetSystemUpdateTime() string {
	if x != nil {
		return x.SystemUpdateTime
	}
	return ""
}

func (x *BidRequest_Device) GetSystemBootTime() string {
	if x != nil {
		return x.SystemBootTime
	}
	return ""
}

func (x *BidRequest_Device) GetAaid() string {
	if x != nil {
		return x.Aaid
	}
	return ""
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`       //可选。app 应用名
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"` //可选。app 应用的版本
	PkgName string `protobuf:"bytes,3,opt,name=pkgName,proto3" json:"pkgName,omitempty"` //可选。包名
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidRequest_App) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender   BidRequest_User_Gender `protobuf:"varint,1,opt,name=gender,proto3,enum=mucang.BidRequest_User_Gender" json:"gender,omitempty"` // 可选。用户的性别
	Age      uint32                 `protobuf:"varint,2,opt,name=age,proto3" json:"age,omitempty"`                                          //可选用户的性别
	Keywords string                 `protobuf:"bytes,3,opt,name=keywords,proto3" json:"keywords,omitempty"`                                 //可选。用户画像的关键词列表，以逗号分隔。
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_User) GetGender() BidRequest_User_Gender {
	if x != nil {
		return x.Gender
	}
	return BidRequest_User_UNKNOWN
}

func (x *BidRequest_User) GetAge() uint32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *BidRequest_User) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

type BidRequest_AdSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                      string                              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                                                  // 必填。广告位id。
	Adtype                  BidRequest_AdSlot_AdType            `protobuf:"varint,2,opt,name=adtype,proto3,enum=mucang.BidRequest_AdSlot_AdType" json:"adtype,omitempty"`                                                                                    // 必填。广告类型。
	Pos                     BidRequest_AdSlot_Position          `protobuf:"varint,3,opt,name=pos,proto3,enum=mucang.BidRequest_AdSlot_Position" json:"pos,omitempty"`                                                                                        // 必填。广告展现位置。
	AcceptedSize            []*BidRequest_AdSlot_Size           `protobuf:"bytes,4,rep,name=accepted_size,json=acceptedSize,proto3" json:"accepted_size,omitempty"`                                                                                          // 必填。可选素材尺寸。
	AcceptedCreativeTypes   []BidRequest_AdSlot_CreativeType    `protobuf:"varint,5,rep,packed,name=accepted_creative_types,json=acceptedCreativeTypes,proto3,enum=mucang.BidRequest_AdSlot_CreativeType" json:"accepted_creative_types,omitempty"`          // 可选。可接受的创意类型。
	AcceptedInteractionType []BidRequest_AdSlot_InteractionType `protobuf:"varint,6,rep,packed,name=accepted_interaction_type,json=acceptedInteractionType,proto3,enum=mucang.BidRequest_AdSlot_InteractionType" json:"accepted_interaction_type,omitempty"` // 可选。app支持的创意交互类型。
	MinimumCpm              uint64                              `protobuf:"varint,7,opt,name=minimum_cpm,json=minimumCpm,proto3" json:"minimum_cpm,omitempty"`                                                                                               // 可选。最低的cpm出价, 单位为分/cpm。
}

func (x *BidRequest_AdSlot) Reset() {
	*x = BidRequest_AdSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot) ProtoMessage() {}

func (x *BidRequest_AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_AdSlot) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_AdSlot) GetAdtype() BidRequest_AdSlot_AdType {
	if x != nil {
		return x.Adtype
	}
	return BidRequest_AdSlot_BANNER
}

func (x *BidRequest_AdSlot) GetPos() BidRequest_AdSlot_Position {
	if x != nil {
		return x.Pos
	}
	return BidRequest_AdSlot_TOP
}

func (x *BidRequest_AdSlot) GetAcceptedSize() []*BidRequest_AdSlot_Size {
	if x != nil {
		return x.AcceptedSize
	}
	return nil
}

func (x *BidRequest_AdSlot) GetAcceptedCreativeTypes() []BidRequest_AdSlot_CreativeType {
	if x != nil {
		return x.AcceptedCreativeTypes
	}
	return nil
}

func (x *BidRequest_AdSlot) GetAcceptedInteractionType() []BidRequest_AdSlot_InteractionType {
	if x != nil {
		return x.AcceptedInteractionType
	}
	return nil
}

func (x *BidRequest_AdSlot) GetMinimumCpm() uint64 {
	if x != nil {
		return x.MinimumCpm
	}
	return 0
}

// 地理位置信息
type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude  float32 `protobuf:"fixed32,1,opt,name=latitude,proto3" json:"latitude,omitempty"`   //维度
	Longitude float32 `protobuf:"fixed32,2,opt,name=longitude,proto3" json:"longitude,omitempty"` //经度
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Geo) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *BidRequest_Geo) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

// 网络环境信息
type BidRequest_Device_Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionType BidRequest_Device_Network_ConnectionType `protobuf:"varint,1,opt,name=connection_type,json=connectionType,proto3,enum=mucang.BidRequest_Device_Network_ConnectionType" json:"connection_type,omitempty"` //可选！网络连接类型，用于判断网速
	OperatorType   BidRequest_Device_Network_OperatorType   `protobuf:"varint,2,opt,name=operator_type,json=operatorType,proto3,enum=mucang.BidRequest_Device_Network_OperatorType" json:"operator_type,omitempty"`         //可选！移动运营商类型，用于运营商定向广告
}

func (x *BidRequest_Device_Network) Reset() {
	*x = BidRequest_Device_Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Network) ProtoMessage() {}

func (x *BidRequest_Device_Network) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Network.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Network) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Device_Network) GetConnectionType() BidRequest_Device_Network_ConnectionType {
	if x != nil {
		return x.ConnectionType
	}
	return BidRequest_Device_Network_UNKNOWN
}

func (x *BidRequest_Device_Network) GetOperatorType() BidRequest_Device_Network_OperatorType {
	if x != nil {
		return x.OperatorType
	}
	return BidRequest_Device_Network_OTHER
}

type BidRequest_AdSlot_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  uint32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`   // 宽度。
	Height uint32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"` // 高度。
}

func (x *BidRequest_AdSlot_Size) Reset() {
	*x = BidRequest_AdSlot_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_Size) ProtoMessage() {}

func (x *BidRequest_AdSlot_Size) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_Size.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_Size) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *BidRequest_AdSlot_Size) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot_Size) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BidResponse_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdId     string                   `protobuf:"bytes,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`              //必填。广告 id
	Creative *BidResponse_Ad_Creative `protobuf:"bytes,2,opt,name=creative,proto3" json:"creative,omitempty"`                  //必填。广告素材
	BidPrice uint64                   `protobuf:"varint,3,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"` //可选。广告出价, 单位为分/cpm。
}

func (x *BidResponse_Ad) Reset() {
	*x = BidResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad) ProtoMessage() {}

func (x *BidResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_Ad) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *BidResponse_Ad) GetCreative() *BidResponse_Ad_Creative {
	if x != nil {
		return x.Creative
	}
	return nil
}

func (x *BidResponse_Ad) GetBidPrice() uint64 {
	if x != nil {
		return x.BidPrice
	}
	return 0
}

type BidResponse_Ad_Creative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeType     BidResponse_Ad_CreativeType    `protobuf:"varint,1,opt,name=creative_type,json=creativeType,proto3,enum=mucang.BidResponse_Ad_CreativeType" json:"creative_type,omitempty"`             //必填。该广告的创意类型
	InteractionType  BidResponse_Ad_InteractionType `protobuf:"varint,2,opt,name=interaction_type,json=interactionType,proto3,enum=mucang.BidResponse_Ad_InteractionType" json:"interaction_type,omitempty"` // 可选。广告支持的交互类型。
	Title            string                         `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                                                                                        //可选。广告标题
	Description      string                         `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                                                                            //可选。广告描述
	Images           []*BidResponse_Ad_Image        `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`                                                                                      //可选。素材图片
	Icon             *BidResponse_Ad_Image          `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`                                                                                          //可选。广告 icon
	TargetUrl        string                         `protobuf:"bytes,7,opt,name=target_url,json=targetUrl,proto3" json:"target_url,omitempty"`                                                               //可选。创意的落地页url。
	ShowUrl          []string                       `protobuf:"bytes,8,rep,name=show_url,json=showUrl,proto3" json:"show_url,omitempty"`                                                                     //可选。展现监测url列表。
	ClickUrl         []string                       `protobuf:"bytes,9,rep,name=click_url,json=clickUrl,proto3" json:"click_url,omitempty"`                                                                  //可选。点击监测url列表。
	Ext              string                         `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`                                                                                           //可选。扩展字段，监测url回传的数据。
	WinNoticeUrl     string                         `protobuf:"bytes,11,opt,name=win_notice_url,json=winNoticeUrl,proto3" json:"win_notice_url,omitempty"`                                                   //可选。获胜通知的url。
	Deeplink         string                         `protobuf:"bytes,12,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                                                                 //可选。deeplink。
	AppInfo          *BidResponse_Ad_AppInfo        `protobuf:"bytes,13,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`                                                                    //creative_type为DOWNLOAD时必填
	WechatAppletId   string                         `protobuf:"bytes,14,opt,name=wechat_applet_id,json=wechatAppletId,proto3" json:"wechat_applet_id,omitempty"`                                             //可选。微信小程序 id。
	WechatAppletPath string                         `protobuf:"bytes,15,opt,name=wechat_applet_path,json=wechatAppletPath,proto3" json:"wechat_applet_path,omitempty"`                                       //可选。微信小程序 path。
	Media            *BidResponse_Ad_Media          `protobuf:"bytes,16,opt,name=media,proto3" json:"media,omitempty"`                                                                                       //可选。媒体
	DplSuccessUrl    []string                       `protobuf:"bytes,17,rep,name=dpl_success_url,json=dplSuccessUrl,proto3" json:"dpl_success_url,omitempty"`                                                //可选。deeplink调起成功监测
	DplFailUrl       []string                       `protobuf:"bytes,18,rep,name=dpl_fail_url,json=dplFailUrl,proto3" json:"dpl_fail_url,omitempty"`                                                         //可选。deeplink调起失败监测
}

func (x *BidResponse_Ad_Creative) Reset() {
	*x = BidResponse_Ad_Creative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_Creative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_Creative) ProtoMessage() {}

func (x *BidResponse_Ad_Creative) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_Creative.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_Creative) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_Ad_Creative) GetCreativeType() BidResponse_Ad_CreativeType {
	if x != nil {
		return x.CreativeType
	}
	return BidResponse_Ad_TEXT
}

func (x *BidResponse_Ad_Creative) GetInteractionType() BidResponse_Ad_InteractionType {
	if x != nil {
		return x.InteractionType
	}
	return BidResponse_Ad_NO_INTERACTION
}

func (x *BidResponse_Ad_Creative) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetImages() []*BidResponse_Ad_Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetIcon() *BidResponse_Ad_Image {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetTargetUrl() string {
	if x != nil {
		return x.TargetUrl
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetShowUrl() []string {
	if x != nil {
		return x.ShowUrl
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetClickUrl() []string {
	if x != nil {
		return x.ClickUrl
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetWinNoticeUrl() string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetAppInfo() *BidResponse_Ad_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetWechatAppletId() string {
	if x != nil {
		return x.WechatAppletId
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetWechatAppletPath() string {
	if x != nil {
		return x.WechatAppletPath
	}
	return ""
}

func (x *BidResponse_Ad_Creative) GetMedia() *BidResponse_Ad_Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetDplSuccessUrl() []string {
	if x != nil {
		return x.DplSuccessUrl
	}
	return nil
}

func (x *BidResponse_Ad_Creative) GetDplFailUrl() []string {
	if x != nil {
		return x.DplFailUrl
	}
	return nil
}

type BidResponse_Ad_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`        //图片地址
	Width  uint32 `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`   //图片宽
	Height uint32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"` //图片高
}

func (x *BidResponse_Ad_Image) Reset() {
	*x = BidResponse_Ad_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_Image) ProtoMessage() {}

func (x *BidResponse_Ad_Image) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_Image) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *BidResponse_Ad_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_Ad_Image) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidResponse_Ad_Image) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BidResponse_Ad_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IconUrl           string `protobuf:"bytes,1,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`                                 //必填。icon 图片 url
	AppName           string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                 //必填。应用名称
	VersionName       string `protobuf:"bytes,3,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`                     //必填。版本号
	CompanyName       string `protobuf:"bytes,4,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`                     //必填。开发者公司名称
	AppPermissionsUrl string `protobuf:"bytes,5,opt,name=app_permissions_url,json=appPermissionsUrl,proto3" json:"app_permissions_url,omitempty"` //必填。应用权限链接
	PrivacyPolicyUrl  string `protobuf:"bytes,6,opt,name=privacy_policy_url,json=privacyPolicyUrl,proto3" json:"privacy_policy_url,omitempty"`    //必填。隐私协议链接
	DownloadUrl       string `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`                     //必填。下载地址
	PackageName       string `protobuf:"bytes,8,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                     //选填。包名
}

func (x *BidResponse_Ad_AppInfo) Reset() {
	*x = BidResponse_Ad_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_AppInfo) ProtoMessage() {}

func (x *BidResponse_Ad_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_AppInfo) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 2}
}

func (x *BidResponse_Ad_AppInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetAppPermissionsUrl() string {
	if x != nil {
		return x.AppPermissionsUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetPrivacyPolicyUrl() string {
	if x != nil {
		return x.PrivacyPolicyUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

type BidResponse_Ad_Media struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`             //媒体类型
	Duration   uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`    //时长
	Url        string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`               //原始地址
	MiddleUrl  string `protobuf:"bytes,4,opt,name=middleUrl,proto3" json:"middleUrl,omitempty"`   //middle地址
	Width      uint32 `protobuf:"varint,5,opt,name=width,proto3" json:"width,omitempty"`          //截图宽
	Height     uint32 `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`        //截图高
	FirstFrame string `protobuf:"bytes,7,opt,name=firstFrame,proto3" json:"firstFrame,omitempty"` //截图
}

func (x *BidResponse_Ad_Media) Reset() {
	*x = BidResponse_Ad_Media{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mucang_1_9_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_Media) ProtoMessage() {}

func (x *BidResponse_Ad_Media) ProtoReflect() protoreflect.Message {
	mi := &file_mucang_1_9_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_Media.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_Media) Descriptor() ([]byte, []int) {
	return file_mucang_1_9_proto_rawDescGZIP(), []int{1, 0, 3}
}

func (x *BidResponse_Ad_Media) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BidResponse_Ad_Media) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BidResponse_Ad_Media) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_Ad_Media) GetMiddleUrl() string {
	if x != nil {
		return x.MiddleUrl
	}
	return ""
}

func (x *BidResponse_Ad_Media) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidResponse_Ad_Media) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidResponse_Ad_Media) GetFirstFrame() string {
	if x != nil {
		return x.FirstFrame
	}
	return ""
}

var File_mucang_1_9_proto protoreflect.FileDescriptor

var file_mucang_1_9_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x5f, 0x31, 0x2e, 0x39, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x22, 0x85, 0x15, 0x0a, 0x0a, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x75, 0x63, 0x61,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x03,
	0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x75, 0x63, 0x61,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x28, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f,
	0x12, 0x2b, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a,
	0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f,
	0x74, 0x73, 0x1a, 0xe7, 0x09, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61,
	0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64,
	0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x38,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d,
	0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x73,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x3b, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f,
	0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x28, 0x0a, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x42, 0x6f, 0x6f, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x1a, 0xb2, 0x02, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x59, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x75,
	0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x47, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x32, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x33, 0x10,
	0x02, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x34, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x35, 0x10,
	0x04, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x05, 0x22, 0x2e, 0x0a, 0x0c, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x00, 0x12, 0x05, 0x0a, 0x01, 0x4d, 0x10, 0x01, 0x12, 0x05, 0x0a,
	0x01, 0x43, 0x10, 0x02, 0x12, 0x05, 0x0a, 0x01, 0x54, 0x10, 0x03, 0x22, 0x3f, 0x0a, 0x0a, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c,
	0x45, 0x54, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x54, 0x56, 0x10, 0x03, 0x22, 0x2e, 0x0a, 0x08,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x1a, 0x4d, 0x0a, 0x03,
	0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x99, 0x01, 0x0a, 0x04,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x2b, 0x0a, 0x06, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x61, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x65, 0x6d, 0x61, 0x6c, 0x65, 0x10, 0x02, 0x1a, 0xb7, 0x06, 0x0a, 0x06, 0x41, 0x64, 0x53, 0x6c,
	0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x38, 0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x41, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x03,
	0x70, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x75, 0x63, 0x61,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70,
	0x6f, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x75, 0x63, 0x61,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x5e, 0x0a, 0x17, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x15, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x65, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x75, 0x63,
	0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x70, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x43, 0x70, 0x6d, 0x1a,
	0x34, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x3e, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x53, 0x54, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0a, 0x0a,
	0x06, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52,
	0x45, 0x41, 0x4d, 0x10, 0x03, 0x22, 0x45, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x4f, 0x50, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x4f,
	0x54, 0x54, 0x4f, 0x4d, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x02,
	0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a,
	0x46, 0x55, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x04, 0x22, 0x50, 0x0a, 0x0c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x54, 0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x49, 0x46, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54,
	0x4d, 0x4c, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12,
	0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x05, 0x22, 0x71,
	0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x49, 0x41, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x06, 0x1a, 0x3f, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x22, 0xd8, 0x0d, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x1a, 0xff, 0x0c, 0x0a, 0x02,
	0x41, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x75, 0x63, 0x61,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x1a, 0x83, 0x06, 0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x48,
	0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x68,
	0x6f, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x65, 0x78, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69,
	0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x39, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x28, 0x0a, 0x10, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41,
	0x70, 0x70, 0x6c, 0x65, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x05, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x26, 0x0a,
	0x0f, 0x64, 0x70, 0x6c, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x70, 0x6c, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x70, 0x6c, 0x5f, 0x66, 0x61, 0x69,
	0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x70, 0x6c,
	0x46, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x1a, 0x47, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x1a, 0xa9, 0x02, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xb5, 0x01, 0x0a,
	0x05, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x64, 0x64,
	0x6c, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x22, 0x50, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x49, 0x46,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54, 0x4d, 0x4c, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05,
	0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f,
	0x49, 0x43, 0x4f, 0x4e, 0x10, 0x05, 0x22, 0x84, 0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c,
	0x4f, 0x41, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x41, 0x4c, 0x49, 0x4e, 0x47,
	0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x05, 0x12,
	0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x45,
	0x43, 0x48, 0x41, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x54, 0x10, 0x07, 0x22, 0x80, 0x01,
	0x0a, 0x09, 0x57, 0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x62, 0x69, 0x64, 0x5f, 0x77, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x57, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x37, 0x0a, 0x1f, 0x63, 0x6e, 0x2e, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x2e, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x42, 0x09, 0x4d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x42, 0x50, 0x4f, 0x5a, 0x09,
	0x2e, 0x2e, 0x2f, 0x6d, 0x75, 0x63, 0x61, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_mucang_1_9_proto_rawDescOnce sync.Once
	file_mucang_1_9_proto_rawDescData = file_mucang_1_9_proto_rawDesc
)

func file_mucang_1_9_proto_rawDescGZIP() []byte {
	file_mucang_1_9_proto_rawDescOnce.Do(func() {
		file_mucang_1_9_proto_rawDescData = protoimpl.X.CompressGZIP(file_mucang_1_9_proto_rawDescData)
	})
	return file_mucang_1_9_proto_rawDescData
}

var file_mucang_1_9_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_mucang_1_9_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_mucang_1_9_proto_goTypes = []interface{}{
	(BidRequest_Device_DeviceType)(0),             // 0: mucang.BidRequest.Device.DeviceType
	(BidRequest_Device_Platform)(0),               // 1: mucang.BidRequest.Device.Platform
	(BidRequest_Device_Network_ConnectionType)(0), // 2: mucang.BidRequest.Device.Network.ConnectionType
	(BidRequest_Device_Network_OperatorType)(0),   // 3: mucang.BidRequest.Device.Network.OperatorType
	(BidRequest_User_Gender)(0),                   // 4: mucang.BidRequest.User.Gender
	(BidRequest_AdSlot_AdType)(0),                 // 5: mucang.BidRequest.AdSlot.AdType
	(BidRequest_AdSlot_Position)(0),               // 6: mucang.BidRequest.AdSlot.Position
	(BidRequest_AdSlot_CreativeType)(0),           // 7: mucang.BidRequest.AdSlot.CreativeType
	(BidRequest_AdSlot_InteractionType)(0),        // 8: mucang.BidRequest.AdSlot.InteractionType
	(BidResponse_Ad_CreativeType)(0),              // 9: mucang.BidResponse.Ad.CreativeType
	(BidResponse_Ad_InteractionType)(0),           // 10: mucang.BidResponse.Ad.InteractionType
	(*BidRequest)(nil),                            // 11: mucang.BidRequest
	(*BidResponse)(nil),                           // 12: mucang.BidResponse
	(*WinNotice)(nil),                             // 13: mucang.WinNotice
	(*BidRequest_Device)(nil),                     // 14: mucang.BidRequest.Device
	(*BidRequest_App)(nil),                        // 15: mucang.BidRequest.App
	(*BidRequest_User)(nil),                       // 16: mucang.BidRequest.User
	(*BidRequest_AdSlot)(nil),                     // 17: mucang.BidRequest.AdSlot
	(*BidRequest_Geo)(nil),                        // 18: mucang.BidRequest.Geo
	(*BidRequest_Device_Network)(nil),             // 19: mucang.BidRequest.Device.Network
	(*BidRequest_AdSlot_Size)(nil),                // 20: mucang.BidRequest.AdSlot.Size
	(*BidResponse_Ad)(nil),                        // 21: mucang.BidResponse.Ad
	(*BidResponse_Ad_Creative)(nil),               // 22: mucang.BidResponse.Ad.Creative
	(*BidResponse_Ad_Image)(nil),                  // 23: mucang.BidResponse.Ad.Image
	(*BidResponse_Ad_AppInfo)(nil),                // 24: mucang.BidResponse.Ad.AppInfo
	(*BidResponse_Ad_Media)(nil),                  // 25: mucang.BidResponse.Ad.Media
}
var file_mucang_1_9_proto_depIdxs = []int32{
	14, // 0: mucang.BidRequest.device:type_name -> mucang.BidRequest.Device
	15, // 1: mucang.BidRequest.app:type_name -> mucang.BidRequest.App
	18, // 2: mucang.BidRequest.geo:type_name -> mucang.BidRequest.Geo
	16, // 3: mucang.BidRequest.user:type_name -> mucang.BidRequest.User
	17, // 4: mucang.BidRequest.adslots:type_name -> mucang.BidRequest.AdSlot
	21, // 5: mucang.BidResponse.ads:type_name -> mucang.BidResponse.Ad
	0,  // 6: mucang.BidRequest.Device.type:type_name -> mucang.BidRequest.Device.DeviceType
	19, // 7: mucang.BidRequest.Device.network:type_name -> mucang.BidRequest.Device.Network
	1,  // 8: mucang.BidRequest.Device.platform:type_name -> mucang.BidRequest.Device.Platform
	4,  // 9: mucang.BidRequest.User.gender:type_name -> mucang.BidRequest.User.Gender
	5,  // 10: mucang.BidRequest.AdSlot.adtype:type_name -> mucang.BidRequest.AdSlot.AdType
	6,  // 11: mucang.BidRequest.AdSlot.pos:type_name -> mucang.BidRequest.AdSlot.Position
	20, // 12: mucang.BidRequest.AdSlot.accepted_size:type_name -> mucang.BidRequest.AdSlot.Size
	7,  // 13: mucang.BidRequest.AdSlot.accepted_creative_types:type_name -> mucang.BidRequest.AdSlot.CreativeType
	8,  // 14: mucang.BidRequest.AdSlot.accepted_interaction_type:type_name -> mucang.BidRequest.AdSlot.InteractionType
	2,  // 15: mucang.BidRequest.Device.Network.connection_type:type_name -> mucang.BidRequest.Device.Network.ConnectionType
	3,  // 16: mucang.BidRequest.Device.Network.operator_type:type_name -> mucang.BidRequest.Device.Network.OperatorType
	22, // 17: mucang.BidResponse.Ad.creative:type_name -> mucang.BidResponse.Ad.Creative
	9,  // 18: mucang.BidResponse.Ad.Creative.creative_type:type_name -> mucang.BidResponse.Ad.CreativeType
	10, // 19: mucang.BidResponse.Ad.Creative.interaction_type:type_name -> mucang.BidResponse.Ad.InteractionType
	23, // 20: mucang.BidResponse.Ad.Creative.images:type_name -> mucang.BidResponse.Ad.Image
	23, // 21: mucang.BidResponse.Ad.Creative.icon:type_name -> mucang.BidResponse.Ad.Image
	24, // 22: mucang.BidResponse.Ad.Creative.app_info:type_name -> mucang.BidResponse.Ad.AppInfo
	25, // 23: mucang.BidResponse.Ad.Creative.media:type_name -> mucang.BidResponse.Ad.Media
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_mucang_1_9_proto_init() }
func file_mucang_1_9_proto_init() {
	if File_mucang_1_9_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_mucang_1_9_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WinNotice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_Creative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mucang_1_9_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_Media); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mucang_1_9_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mucang_1_9_proto_goTypes,
		DependencyIndexes: file_mucang_1_9_proto_depIdxs,
		EnumInfos:         file_mucang_1_9_proto_enumTypes,
		MessageInfos:      file_mucang_1_9_proto_msgTypes,
	}.Build()
	File_mucang_1_9_proto = out.File
	file_mucang_1_9_proto_rawDesc = nil
	file_mucang_1_9_proto_goTypes = nil
	file_mucang_1_9_proto_depIdxs = nil
}
