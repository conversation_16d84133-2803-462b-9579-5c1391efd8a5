
// Copyright 2020 Mucang Inc. All Rights Reserved.
// 本文件描述API接口版本：1.9
syntax = "proto3";
option java_package = "cn.mucang.advertproxy.api.proto";
option java_outer_classname = "MucangBPO";

option go_package = "mh_proxy/pb/mucang";

package mucang;

message BidRequest {
  string request_id = 1; //广告请求 id
  string api_version = 2; //必填。此API的版本。
  Device device = 3; //设备信息
  App app = 4;//应用信息
  Geo geo = 5; //位置信息
  User user = 6;//用户信息
  AdSlot adslots = 7; //广告位 id

  message Device {
    string idfa = 1; // 必填！iOS设备的IDFA
    string imei = 2; // 必填！Android手机设备的IMEI
    string android_id = 3; // 必填！Android手机设备系统ID
    string oaid = 4; // oaid 移动安全联盟开放匿名设备标识符
    string idfa_md5 = 5; // IDFA MD5 值
    string imei_md5 = 6; // android IMEI MD5 值
    string androidid_md5 = 7;// android id MD5 值
    string oaid_md5 = 8; // oaid MD5 值
    DeviceType type = 9; // 必填。设备类型。
    string os_version = 10; // 可选。操作系统版本。
    string vendor = 11; // 可选。设备厂商，如Apple, Samsung。
    string model = 12; // 可选。设备型号，如iPhone5s, Galaxy。
    string ua = 13; // 可选。浏览器信息。
    string ip = 14; // 可选。设备的ip。
    string mac = 15; // 可选。设备的mac地址。
    Network network = 16; //可选。网络信息
    Platform platform = 17; //可选。设备平台
    string brand = 18; //可选。设备品牌品牌
    string boot_mark = 19; //可选。反作弊字段
    string update_mark = 20; //可选。反作弊字段
    int32 screen_height = 21; //可选。屏幕高度（像素）
    int32 screen_width = 22; //可选。屏幕宽度（像素）
    string device_init_time=23; //设备初始化时间
    string system_update_time=24; //系统更新时间
    string system_boot_time=25; //系统启动时间
    string aaid=26; //阿里匿名广告标识符

    // 设备类型
    enum DeviceType {
      DEVICE_UNKNOWN = 0;
      PHONE = 1; // 手机。
      TABLET = 2; // 平板。
      TV = 3; // 智能电视。
    }

    // 网络环境信息
    message Network {
      // 网络连接类型
      enum ConnectionType {
        UNKNOWN = 0; //无法探测当前网络状态
        G2 = 1; //蜂窝数据2G网络
        G3 = 2; //蜂窝数据3G网络
        G4 = 3; //蜂窝数据4G网络
        G5 = 4; //蜂窝数据5G网络
        WIFI = 5; //Wi-Fi网络接入
      };
      // 移动运营商类型
      enum OperatorType {
        OTHER = 0; //未知的运营商
        M = 1; //中国移动
        C = 2; //中国电信
        T = 3; //中国联通
      };
      ConnectionType connection_type = 1; //可选！网络连接类型，用于判断网速
      OperatorType operator_type = 2; //可选！移动运营商类型，用于运营商定向广告
    }

    enum Platform {
      OTHER = 0; //其它
      IPHONE = 1; //iOS
      ANDROID = 2; //android
    }
  }

  message App {
    string name = 1; //可选。app 应用名
    string version = 2; //可选。app 应用的版本
    string pkgName = 3; //可选。包名
  }

  message User {
    Gender gender = 1; // 可选。用户的性别
    uint32 age = 2;//可选用户的性别
    string keywords = 3; //可选。用户画像的关键词列表，以逗号分隔。

    //性别信息
    enum Gender {
      UNKNOWN = 0; //未知
      Male = 1; //男性
      Female = 2; //女性
    }
  }

  message AdSlot {
    string id = 1; // 必填。广告位id。
    AdType adtype = 2; // 必填。广告类型。
    Position pos = 3; // 必填。广告展现位置。
    repeated Size accepted_size = 4; // 必填。可选素材尺寸。
    repeated CreativeType accepted_creative_types = 5; // 可选。可接受的创意类型。
    repeated InteractionType accepted_interaction_type = 6; // 可选。app支持的创意交互类型。
    uint64 minimum_cpm = 7; // 可选。最低的cpm出价, 单位为分/cpm。

    // 广告的类型。
    enum AdType {
      BANNER = 0; // 横幅广告。
      INTERSTITIAL = 1; // 插屏广告。
      SPLASH = 2; // 开屏广告
      STREAM = 3; // 信息流广告。
    }
    // 广告出现的位置。
    enum Position {
      TOP = 0; // 顶部。
      BOTTOM = 1; // 底部。
      FLOW = 2; // 信息流内。
      MIDDLE = 3; // 中部(插屏广告专用)。
      FULLSCREEN = 4; // 全屏。
    }
    // 可选的创意类型。
    enum CreativeType {
      TEXT = 0; // 文字。
      IMAGE = 1; // 图片。
      GIF = 2; // 动图。
      HTML = 3; // HTML.
      VIDEO = 4; // 视频。
      TEXT_ICON = 5; // 图文。
    }
    // 创意的交互类型
    enum InteractionType {
      NO_INTERACTION = 0; // 无动作，针对有些开屏不支持点击。
      SURFING = 1; // 使用浏览器打开网页。
      IN_APP = 2; // 在app中打开。
      DOWNLOAD = 3; // 下载应用。
      DIALING = 4; // 拨打电话。
      MESSAGE = 5; // 发送短信。
      EMAIL = 6; // 发送邮件。
    }
    message Size {
      uint32 width = 1; // 宽度。
      uint32 height = 2; // 高度。
    }
  }

  // 地理位置信息
  message Geo {
    float latitude = 1;//维度
    float longitude = 2;//经度
  }

}

message BidResponse {
  string request_id = 1; //必填。BidRequest中所带的request_id。
  repeated Ad ads = 2; //可选。竞价广告列表，与adslots对应。

  message Ad {
    string ad_id = 1; //必填。广告 id
    Creative creative = 2; //必填。广告素材
    uint64 bid_price = 3; //可选。广告出价, 单位为分/cpm。

    message Creative {
      CreativeType creative_type = 1;//必填。该广告的创意类型
      InteractionType interaction_type = 2; // 可选。广告支持的交互类型。
      string title = 3; //可选。广告标题
      string description = 4; //可选。广告描述
      repeated Image images = 5; //可选。素材图片
      Image icon = 6; //可选。广告 icon
      string target_url = 7; //可选。创意的落地页url。
      repeated string show_url = 8; //可选。展现监测url列表。
      repeated string click_url = 9; //可选。点击监测url列表。
      string ext = 10; //可选。扩展字段，监测url回传的数据。
      string win_notice_url = 11; //可选。获胜通知的url。
      string deeplink = 12; //可选。deeplink。
      AppInfo app_info = 13; //creative_type为DOWNLOAD时必填
      string wechat_applet_id = 14; //可选。微信小程序 id。
      string wechat_applet_path = 15; //可选。微信小程序 path。
      Media media = 16; //可选。媒体
      repeated string dpl_success_url = 17; //可选。deeplink调起成功监测
      repeated string dpl_fail_url = 18; //可选。deeplink调起失败监测
    }
    // 可选的创意类型。
    enum CreativeType {
      TEXT = 0; // 文字。
      IMAGE = 1; // 图片。
      GIF = 2; // 动图。
      HTML = 3; // HTML.
      VIDEO = 4; // 视频。
      TEXT_ICON = 5; // 图文。
    }

    // 创意的交互类型
    enum InteractionType {
      NO_INTERACTION = 0; // 无动作，针对有些开屏不支持点击。
      SURFING = 1; // 使用浏览器打开网页。
      IN_APP = 2; // 在app中打开。
      DOWNLOAD = 3; // 下载应用。
      DIALING = 4; // 拨打电话。
      MESSAGE = 5; // 发送短信。
      EMAIL = 6; // 发送邮件。
      WECHAT_APPLET = 7; // 微信小程序。
    }

    message Image {
      string url = 1; //图片地址
      uint32 width = 2; //图片宽
      uint32 height = 3; //图片高
    }

    message AppInfo {
      string icon_url = 1; //必填。icon 图片 url
      string app_name = 2;//必填。应用名称
      string version_name = 3;//必填。版本号
      string company_name = 4;//必填。开发者公司名称
      string app_permissions_url = 5;//必填。应用权限链接
      string privacy_policy_url = 6;//必填。隐私协议链接
      string download_url = 7;//必填。下载地址
      string package_name = 8;//选填。包名
    }

    message Media {
      string type = 1; //媒体类型
      uint32 duration = 2; //时长
      string url = 3; //原始地址
      string middleUrl = 4; //middle地址
      uint32 width = 5; //截图宽
      uint32 height = 6; //截图高
      string firstFrame = 7; //截图
    }
  }

}

message WinNotice {
  string request_id = 1; //必填。BidRequest中所带的request_id。
  string ad_id = 3; //必填。广告 id
  uint64 bid_price = 4; //必填。广告出价, 单位为分/cpm。
  uint64 bid_win_price = 5; //必填。广告实际赢价, 单位为分/cpm。
}