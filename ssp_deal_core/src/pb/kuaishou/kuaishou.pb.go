// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: kuaishou.proto

package kuaishou

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Imp_AdmType int32

const (
	Imp_UNKOWN Imp_AdmType = 0
	Imp_H5     Imp_AdmType = 1
	Imp_JSON   Imp_AdmType = 2
)

// Enum value maps for Imp_AdmType.
var (
	Imp_AdmType_name = map[int32]string{
		0: "UNKOWN",
		1: "H5",
		2: "JSON",
	}
	Imp_AdmType_value = map[string]int32{
		"UNKOWN": 0,
		"H5":     1,
		"JSON":   2,
	}
)

func (x Imp_AdmType) Enum() *Imp_AdmType {
	p := new(Imp_AdmType)
	*p = x
	return p
}

func (x Imp_AdmType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Imp_AdmType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[0].Descriptor()
}

func (Imp_AdmType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[0]
}

func (x Imp_AdmType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Imp_AdmType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Imp_AdmType(num)
	return nil
}

// Deprecated: Use Imp_AdmType.Descriptor instead.
func (Imp_AdmType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{2, 0}
}

type Device_DeviceType int32

const (
	Device_UNKNOWN_DEVICE_TYPE Device_DeviceType = 0
	Device_PHONE               Device_DeviceType = 1 //手机
	Device_TABLET              Device_DeviceType = 2 //平板
)

// Enum value maps for Device_DeviceType.
var (
	Device_DeviceType_name = map[int32]string{
		0: "UNKNOWN_DEVICE_TYPE",
		1: "PHONE",
		2: "TABLET",
	}
	Device_DeviceType_value = map[string]int32{
		"UNKNOWN_DEVICE_TYPE": 0,
		"PHONE":               1,
		"TABLET":              2,
	}
)

func (x Device_DeviceType) Enum() *Device_DeviceType {
	p := new(Device_DeviceType)
	*p = x
	return p
}

func (x Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[1].Descriptor()
}

func (Device_DeviceType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[1]
}

func (x Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Device_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Device_DeviceType(num)
	return nil
}

// Deprecated: Use Device_DeviceType.Descriptor instead.
func (Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{11, 0}
}

type Device_OsType int32

const (
	Device_UNKNOWN_OS_TYPE Device_OsType = 0
	Device_ANDROID         Device_OsType = 1 // Android
	Device_IOS             Device_OsType = 2 //iOS
)

// Enum value maps for Device_OsType.
var (
	Device_OsType_name = map[int32]string{
		0: "UNKNOWN_OS_TYPE",
		1: "ANDROID",
		2: "IOS",
	}
	Device_OsType_value = map[string]int32{
		"UNKNOWN_OS_TYPE": 0,
		"ANDROID":         1,
		"IOS":             2,
	}
)

func (x Device_OsType) Enum() *Device_OsType {
	p := new(Device_OsType)
	*p = x
	return p
}

func (x Device_OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device_OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[2].Descriptor()
}

func (Device_OsType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[2]
}

func (x Device_OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Device_OsType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Device_OsType(num)
	return nil
}

// Deprecated: Use Device_OsType.Descriptor instead.
func (Device_OsType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{11, 1}
}

type Geo_GeoType int32

const (
	Geo_UNKNOWN       Geo_GeoType = 0
	Geo_GPS           Geo_GeoType = 1
	Geo_IP            Geo_GeoType = 2
	Geo_USER_SUPPLIER Geo_GeoType = 3
	Geo_GEO_HASH      Geo_GeoType = 4
)

// Enum value maps for Geo_GeoType.
var (
	Geo_GeoType_name = map[int32]string{
		0: "UNKNOWN",
		1: "GPS",
		2: "IP",
		3: "USER_SUPPLIER",
		4: "GEO_HASH",
	}
	Geo_GeoType_value = map[string]int32{
		"UNKNOWN":       0,
		"GPS":           1,
		"IP":            2,
		"USER_SUPPLIER": 3,
		"GEO_HASH":      4,
	}
)

func (x Geo_GeoType) Enum() *Geo_GeoType {
	p := new(Geo_GeoType)
	*p = x
	return p
}

func (x Geo_GeoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Geo_GeoType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[3].Descriptor()
}

func (Geo_GeoType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[3]
}

func (x Geo_GeoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Geo_GeoType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Geo_GeoType(num)
	return nil
}

// Deprecated: Use Geo_GeoType.Descriptor instead.
func (Geo_GeoType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{12, 0}
}

type User_Gender int32

const (
	User_U User_Gender = 0 //unknown
	User_M User_Gender = 1 //male
	User_F User_Gender = 2 //female
	User_O User_Gender = 3 //other
)

// Enum value maps for User_Gender.
var (
	User_Gender_name = map[int32]string{
		0: "U",
		1: "M",
		2: "F",
		3: "O",
	}
	User_Gender_value = map[string]int32{
		"U": 0,
		"M": 1,
		"F": 2,
		"O": 3,
	}
)

func (x User_Gender) Enum() *User_Gender {
	p := new(User_Gender)
	*p = x
	return p
}

func (x User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[4].Descriptor()
}

func (User_Gender) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[4]
}

func (x User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *User_Gender) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = User_Gender(num)
	return nil
}

// Deprecated: Use User_Gender.Descriptor instead.
func (User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{16, 0}
}

type Tracking_TrackingEvent int32

const (
	Tracking_AD_CLICK                  Tracking_TrackingEvent = 0      // 广告被点击
	Tracking_AD_EXPOSURE               Tracking_TrackingEvent = 1      // 广告被展现
	Tracking_AD_CLOSE                  Tracking_TrackingEvent = 2      // 广告被关闭
	Tracking_VIDEO_AD_START            Tracking_TrackingEvent = 101000 // 视频开始播放
	Tracking_VIDEO_AD_FULL_SCREEN      Tracking_TrackingEvent = 101001 // 视频全屏
	Tracking_VIDEO_AD_END              Tracking_TrackingEvent = 101002 // 视频正常播放结束
	Tracking_VIDEO_AD_START_CARD_CLICK Tracking_TrackingEvent = 101003 // 点击预览图播放视频
	Tracking_APP_AD_DOWNLOAD           Tracking_TrackingEvent = 102000 // 下载推广APP
	Tracking_APP_AD_INSTALL            Tracking_TrackingEvent = 102001 // 安装推广APP
	Tracking_APP_AD_ACTIVE             Tracking_TrackingEvent = 102002 // 激活推广APP
	Tracking_AD_ACTION_BAR_CLICK       Tracking_TrackingEvent = 103000 // ActionBar点击
	Tracking_AD_LANDING_PAGE_CLICK     Tracking_TrackingEvent = 104000 //落地页前置点击检测
	Tracking_VIDEO_AD_PLAYED_2S        Tracking_TrackingEvent = 105000 //视频播放2s
	Tracking_VIDEO_AD_PLAYED_5S        Tracking_TrackingEvent = 106000 //视频播放5s
	Tracking_VIDEO_AD_LIKE             Tracking_TrackingEvent = 107000 // 点赞
	Tracking_VIDEO_AD_SHARE            Tracking_TrackingEvent = 108000 // 转发
	Tracking_VIDEO_AD_COMMENT          Tracking_TrackingEvent = 109000 // 评论
	Tracking_AD_EXPOSURE_1FRAME        Tracking_TrackingEvent = 110000 // 广告第一帧曝光
	Tracking_VIDEO_AD_PLAYED_3S        Tracking_TrackingEvent = 111000 //视频播放3s
)

// Enum value maps for Tracking_TrackingEvent.
var (
	Tracking_TrackingEvent_name = map[int32]string{
		0:      "AD_CLICK",
		1:      "AD_EXPOSURE",
		2:      "AD_CLOSE",
		101000: "VIDEO_AD_START",
		101001: "VIDEO_AD_FULL_SCREEN",
		101002: "VIDEO_AD_END",
		101003: "VIDEO_AD_START_CARD_CLICK",
		102000: "APP_AD_DOWNLOAD",
		102001: "APP_AD_INSTALL",
		102002: "APP_AD_ACTIVE",
		103000: "AD_ACTION_BAR_CLICK",
		104000: "AD_LANDING_PAGE_CLICK",
		105000: "VIDEO_AD_PLAYED_2S",
		106000: "VIDEO_AD_PLAYED_5S",
		107000: "VIDEO_AD_LIKE",
		108000: "VIDEO_AD_SHARE",
		109000: "VIDEO_AD_COMMENT",
		110000: "AD_EXPOSURE_1FRAME",
		111000: "VIDEO_AD_PLAYED_3S",
	}
	Tracking_TrackingEvent_value = map[string]int32{
		"AD_CLICK":                  0,
		"AD_EXPOSURE":               1,
		"AD_CLOSE":                  2,
		"VIDEO_AD_START":            101000,
		"VIDEO_AD_FULL_SCREEN":      101001,
		"VIDEO_AD_END":              101002,
		"VIDEO_AD_START_CARD_CLICK": 101003,
		"APP_AD_DOWNLOAD":           102000,
		"APP_AD_INSTALL":            102001,
		"APP_AD_ACTIVE":             102002,
		"AD_ACTION_BAR_CLICK":       103000,
		"AD_LANDING_PAGE_CLICK":     104000,
		"VIDEO_AD_PLAYED_2S":        105000,
		"VIDEO_AD_PLAYED_5S":        106000,
		"VIDEO_AD_LIKE":             107000,
		"VIDEO_AD_SHARE":            108000,
		"VIDEO_AD_COMMENT":          109000,
		"AD_EXPOSURE_1FRAME":        110000,
		"VIDEO_AD_PLAYED_3S":        111000,
	}
)

func (x Tracking_TrackingEvent) Enum() *Tracking_TrackingEvent {
	p := new(Tracking_TrackingEvent)
	*p = x
	return p
}

func (x Tracking_TrackingEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Tracking_TrackingEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[5].Descriptor()
}

func (Tracking_TrackingEvent) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[5]
}

func (x Tracking_TrackingEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Tracking_TrackingEvent) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Tracking_TrackingEvent(num)
	return nil
}

// Deprecated: Use Tracking_TrackingEvent.Descriptor instead.
func (Tracking_TrackingEvent) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{17, 0}
}

type Bid_BidType int32

const (
	Bid_NO_TYPE Bid_BidType = 0
	Bid_CPC     Bid_BidType = 1
	Bid_CPM     Bid_BidType = 2
)

// Enum value maps for Bid_BidType.
var (
	Bid_BidType_name = map[int32]string{
		0: "NO_TYPE",
		1: "CPC",
		2: "CPM",
	}
	Bid_BidType_value = map[string]int32{
		"NO_TYPE": 0,
		"CPC":     1,
		"CPM":     2,
	}
)

func (x Bid_BidType) Enum() *Bid_BidType {
	p := new(Bid_BidType)
	*p = x
	return p
}

func (x Bid_BidType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Bid_BidType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[6].Descriptor()
}

func (Bid_BidType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[6]
}

func (x Bid_BidType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Bid_BidType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Bid_BidType(num)
	return nil
}

// Deprecated: Use Bid_BidType.Descriptor instead.
func (Bid_BidType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{19, 0}
}

type Bid_LandingPageShowSytle int32

const (
	Bid_BLUE_BAR             Bid_LandingPageShowSytle = 0 //蓝条
	Bid_PREPOSE_LANDING_PAGE Bid_LandingPageShowSytle = 1 //落地页前置
)

// Enum value maps for Bid_LandingPageShowSytle.
var (
	Bid_LandingPageShowSytle_name = map[int32]string{
		0: "BLUE_BAR",
		1: "PREPOSE_LANDING_PAGE",
	}
	Bid_LandingPageShowSytle_value = map[string]int32{
		"BLUE_BAR":             0,
		"PREPOSE_LANDING_PAGE": 1,
	}
)

func (x Bid_LandingPageShowSytle) Enum() *Bid_LandingPageShowSytle {
	p := new(Bid_LandingPageShowSytle)
	*p = x
	return p
}

func (x Bid_LandingPageShowSytle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Bid_LandingPageShowSytle) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[7].Descriptor()
}

func (Bid_LandingPageShowSytle) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[7]
}

func (x Bid_LandingPageShowSytle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Bid_LandingPageShowSytle) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Bid_LandingPageShowSytle(num)
	return nil
}

// Deprecated: Use Bid_LandingPageShowSytle.Descriptor instead.
func (Bid_LandingPageShowSytle) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{19, 1}
}

type Card_AdxCardType int32

const (
	Card_UN_CARD  Card_AdxCardType = 0   //默认非卡片样式
	Card_PIC_CARD Card_AdxCardType = 100 //图片卡片
)

// Enum value maps for Card_AdxCardType.
var (
	Card_AdxCardType_name = map[int32]string{
		0:   "UN_CARD",
		100: "PIC_CARD",
	}
	Card_AdxCardType_value = map[string]int32{
		"UN_CARD":  0,
		"PIC_CARD": 100,
	}
)

func (x Card_AdxCardType) Enum() *Card_AdxCardType {
	p := new(Card_AdxCardType)
	*p = x
	return p
}

func (x Card_AdxCardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Card_AdxCardType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[8].Descriptor()
}

func (Card_AdxCardType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[8]
}

func (x Card_AdxCardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Card_AdxCardType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Card_AdxCardType(num)
	return nil
}

// Deprecated: Use Card_AdxCardType.Descriptor instead.
func (Card_AdxCardType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{20, 0}
}

type Adm_CreativeType int32

const (
	Adm_NO_TYPE           Adm_CreativeType = 0  // 无创意类型，主要针对原生自定义素材广告，不再制定返回广告的创意类型，根据广告位设置对返回字段进行读取即可
	Adm_TEXT              Adm_CreativeType = 1  // 纯文字广告，一般由title、description构成
	Adm_IMAGE             Adm_CreativeType = 2  // 纯图片广告，一般由单张image_src构成A
	Adm_TEXT_ICON         Adm_CreativeType = 3  // 图文混合广告，一般由单张icon_src和title、description构成，包括静态开屏广告
	Adm_VIDEO             Adm_CreativeType = 4  // 视频广告，一般由视频URL和视频时长构成，包括动态开屏广告
	Adm_ATLAS             Adm_CreativeType = 5  //图集
	Adm_STICKER           Adm_CreativeType = 6  // 便利贴
	Adm_VERTICAL_SCREEN   Adm_CreativeType = 7  // 竖版视频
	Adm_HORIZONTAL_SCREEN Adm_CreativeType = 8  // 横版视频
	Adm_VERTICAL_IMAGE    Adm_CreativeType = 9  // 竖版图片
	Adm_HORIZONTAL_IMAGE  Adm_CreativeType = 10 // 横版图片
)

// Enum value maps for Adm_CreativeType.
var (
	Adm_CreativeType_name = map[int32]string{
		0:  "NO_TYPE",
		1:  "TEXT",
		2:  "IMAGE",
		3:  "TEXT_ICON",
		4:  "VIDEO",
		5:  "ATLAS",
		6:  "STICKER",
		7:  "VERTICAL_SCREEN",
		8:  "HORIZONTAL_SCREEN",
		9:  "VERTICAL_IMAGE",
		10: "HORIZONTAL_IMAGE",
	}
	Adm_CreativeType_value = map[string]int32{
		"NO_TYPE":           0,
		"TEXT":              1,
		"IMAGE":             2,
		"TEXT_ICON":         3,
		"VIDEO":             4,
		"ATLAS":             5,
		"STICKER":           6,
		"VERTICAL_SCREEN":   7,
		"HORIZONTAL_SCREEN": 8,
		"VERTICAL_IMAGE":    9,
		"HORIZONTAL_IMAGE":  10,
	}
)

func (x Adm_CreativeType) Enum() *Adm_CreativeType {
	p := new(Adm_CreativeType)
	*p = x
	return p
}

func (x Adm_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Adm_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[9].Descriptor()
}

func (Adm_CreativeType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[9]
}

func (x Adm_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Adm_CreativeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Adm_CreativeType(num)
	return nil
}

// Deprecated: Use Adm_CreativeType.Descriptor instead.
func (Adm_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{24, 0}
}

type Adm_InteractionType int32

const (
	Adm_NO_INTERACTION Adm_InteractionType = 0 // 无动作，即广告广告点击后无需进行任何响应
	Adm_SURFING        Adm_InteractionType = 1 // 使用浏览器打开网页
	Adm_DOWNLOAD       Adm_InteractionType = 2 // 下载应用
)

// Enum value maps for Adm_InteractionType.
var (
	Adm_InteractionType_name = map[int32]string{
		0: "NO_INTERACTION",
		1: "SURFING",
		2: "DOWNLOAD",
	}
	Adm_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 0,
		"SURFING":        1,
		"DOWNLOAD":       2,
	}
)

func (x Adm_InteractionType) Enum() *Adm_InteractionType {
	p := new(Adm_InteractionType)
	*p = x
	return p
}

func (x Adm_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Adm_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[10].Descriptor()
}

func (Adm_InteractionType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[10]
}

func (x Adm_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Adm_InteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Adm_InteractionType(num)
	return nil
}

// Deprecated: Use Adm_InteractionType.Descriptor instead.
func (Adm_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{24, 1}
}

type Network_ConnectionType int32

const (
	Network_CONNECTION_UNKNOWN Network_ConnectionType = 0   // 无法探测当前网络状态
	Network_CELL_UNKNOWN       Network_ConnectionType = 1   // 蜂窝数据接入，未知网络类型
	Network_CELL_2G            Network_ConnectionType = 2   // 蜂窝数据2G网络
	Network_CELL_3G            Network_ConnectionType = 3   // 蜂窝数据3G网络
	Network_CELL_4G            Network_ConnectionType = 4   // 蜂窝数据4G网络
	Network_CELL_5G            Network_ConnectionType = 5   // 蜂窝数据5G网络
	Network_WIFI               Network_ConnectionType = 100 // Wi-Fi网络接入
	Network_ETHERNET           Network_ConnectionType = 101 // 以太网接入
	Network_NEW_TYPE           Network_ConnectionType = 999 // 未知新类型
)

// Enum value maps for Network_ConnectionType.
var (
	Network_ConnectionType_name = map[int32]string{
		0:   "CONNECTION_UNKNOWN",
		1:   "CELL_UNKNOWN",
		2:   "CELL_2G",
		3:   "CELL_3G",
		4:   "CELL_4G",
		5:   "CELL_5G",
		100: "WIFI",
		101: "ETHERNET",
		999: "NEW_TYPE",
	}
	Network_ConnectionType_value = map[string]int32{
		"CONNECTION_UNKNOWN": 0,
		"CELL_UNKNOWN":       1,
		"CELL_2G":            2,
		"CELL_3G":            3,
		"CELL_4G":            4,
		"CELL_5G":            5,
		"WIFI":               100,
		"ETHERNET":           101,
		"NEW_TYPE":           999,
	}
)

func (x Network_ConnectionType) Enum() *Network_ConnectionType {
	p := new(Network_ConnectionType)
	*p = x
	return p
}

func (x Network_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Network_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[11].Descriptor()
}

func (Network_ConnectionType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[11]
}

func (x Network_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Network_ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Network_ConnectionType(num)
	return nil
}

// Deprecated: Use Network_ConnectionType.Descriptor instead.
func (Network_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{25, 0}
}

type Network_OperatorType int32

const (
	Network_UNKNOWN_OPERATOR Network_OperatorType = 0  // 未知的运营商
	Network_CHINA_MOBILE     Network_OperatorType = 1  // 中国移动
	Network_CHINA_TELECOM    Network_OperatorType = 2  // 中国电信
	Network_CHINA_UNICOM     Network_OperatorType = 3  // 中国联通
	Network_OTHER_OPERATOR   Network_OperatorType = 99 // 其他运营商
)

// Enum value maps for Network_OperatorType.
var (
	Network_OperatorType_name = map[int32]string{
		0:  "UNKNOWN_OPERATOR",
		1:  "CHINA_MOBILE",
		2:  "CHINA_TELECOM",
		3:  "CHINA_UNICOM",
		99: "OTHER_OPERATOR",
	}
	Network_OperatorType_value = map[string]int32{
		"UNKNOWN_OPERATOR": 0,
		"CHINA_MOBILE":     1,
		"CHINA_TELECOM":    2,
		"CHINA_UNICOM":     3,
		"OTHER_OPERATOR":   99,
	}
)

func (x Network_OperatorType) Enum() *Network_OperatorType {
	p := new(Network_OperatorType)
	*p = x
	return p
}

func (x Network_OperatorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Network_OperatorType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[12].Descriptor()
}

func (Network_OperatorType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[12]
}

func (x Network_OperatorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Network_OperatorType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Network_OperatorType(num)
	return nil
}

// Deprecated: Use Network_OperatorType.Descriptor instead.
func (Network_OperatorType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{25, 1}
}

type BidResponse_NoBidReason int32

const (
	BidResponse_UNKNOWN            BidResponse_NoBidReason = 0
	BidResponse_TECHNICAL_ERROR    BidResponse_NoBidReason = 1 //技术错误
	BidResponse_INVALID_REQUEST    BidResponse_NoBidReason = 2 //非法请求
	BidResponse_WEB_SPIDER         BidResponse_NoBidReason = 3 //网络爬虫
	BidResponse_NON_HUMAN_TRAFFIC  BidResponse_NoBidReason = 4 //非认为请求
	BidResponse_ILLEGAL_IP         BidResponse_NoBidReason = 5 //非法ip
	BidResponse_UNSUPPORTED_DEVICE BidResponse_NoBidReason = 6 //不支持的设备类型
	BidResponse_BLOCKED_PUBLISHER  BidResponse_NoBidReason = 7 //来自受限展示者
	BidResponse_BLOCKED_SITE       BidResponse_NoBidReason = 8 //来自受限站点
	BidResponse_UNMATCHED_USER     BidResponse_NoBidReason = 9 //用户不匹配
)

// Enum value maps for BidResponse_NoBidReason.
var (
	BidResponse_NoBidReason_name = map[int32]string{
		0: "UNKNOWN",
		1: "TECHNICAL_ERROR",
		2: "INVALID_REQUEST",
		3: "WEB_SPIDER",
		4: "NON_HUMAN_TRAFFIC",
		5: "ILLEGAL_IP",
		6: "UNSUPPORTED_DEVICE",
		7: "BLOCKED_PUBLISHER",
		8: "BLOCKED_SITE",
		9: "UNMATCHED_USER",
	}
	BidResponse_NoBidReason_value = map[string]int32{
		"UNKNOWN":            0,
		"TECHNICAL_ERROR":    1,
		"INVALID_REQUEST":    2,
		"WEB_SPIDER":         3,
		"NON_HUMAN_TRAFFIC":  4,
		"ILLEGAL_IP":         5,
		"UNSUPPORTED_DEVICE": 6,
		"BLOCKED_PUBLISHER":  7,
		"BLOCKED_SITE":       8,
		"UNMATCHED_USER":     9,
	}
)

func (x BidResponse_NoBidReason) Enum() *BidResponse_NoBidReason {
	p := new(BidResponse_NoBidReason)
	*p = x
	return p
}

func (x BidResponse_NoBidReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_NoBidReason) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[13].Descriptor()
}

func (BidResponse_NoBidReason) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[13]
}

func (x BidResponse_NoBidReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_NoBidReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_NoBidReason(num)
	return nil
}

// Deprecated: Use BidResponse_NoBidReason.Descriptor instead.
func (BidResponse_NoBidReason) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{30, 0}
}

type AdTypeEnum_AdType int32

const (
	AdTypeEnum_NO_AD_TYPE                                 AdTypeEnum_AdType = 0
	AdTypeEnum_KUAISHOU_EXPLORE_FEED_VIDEO                AdTypeEnum_AdType = 1  //发现页第五位视频广告
	AdTypeEnum_KUAISHOU_PROFILE_BETWEEN_COMMENTS_PIC_TEXT AdTypeEnum_AdType = 2  //评论第四位图文广告
	AdTypeEnum_KUAISHOU_PROFILE_BETWEEN_COMMENTS_VIDEO    AdTypeEnum_AdType = 3  //评论第四位视频广告
	AdTypeEnum_KUAISHOU_PROFILE_PLAY_END_PIC_TEXT         AdTypeEnum_AdType = 4  //后贴片图文，暂不支持
	AdTypeEnum_KUAISHOU_PROFILE_PLAY_END_VIDEO            AdTypeEnum_AdType = 5  //后贴片视频，暂不支持
	AdTypeEnum_KUAISHOU_SPLASH_SLOT_VIDEO                 AdTypeEnum_AdType = 6  //快手动态开屏广告，暂不支持
	AdTypeEnum_KUAISHOU_SPLASH_SLOT_PIC_TEXT              AdTypeEnum_AdType = 7  //快手静态开屏广告
	AdTypeEnum_KUAISHOU_SPLASH_TOPVIEW_SLOT_VIDEO         AdTypeEnum_AdType = 8  //快手动态开屏+topview，暂不支持
	AdTypeEnum_KUAISHOU_SPLASH_TOPVIEW_SLOT_PIC_TEXT      AdTypeEnum_AdType = 9  //快手静态开屏+topview，暂不支持
	AdTypeEnum_FEED_GROUP_PIC_TEXT                        AdTypeEnum_AdType = 10 //图文信息流三图广告
	AdTypeEnum_FEED_SMALL_PIC_TEXT                        AdTypeEnum_AdType = 11 //图文信息流小图广告
	AdTypeEnum_FEED_LARGE_PIC_TEXT                        AdTypeEnum_AdType = 12 //图文信息流大图广告
)

// Enum value maps for AdTypeEnum_AdType.
var (
	AdTypeEnum_AdType_name = map[int32]string{
		0:  "NO_AD_TYPE",
		1:  "KUAISHOU_EXPLORE_FEED_VIDEO",
		2:  "KUAISHOU_PROFILE_BETWEEN_COMMENTS_PIC_TEXT",
		3:  "KUAISHOU_PROFILE_BETWEEN_COMMENTS_VIDEO",
		4:  "KUAISHOU_PROFILE_PLAY_END_PIC_TEXT",
		5:  "KUAISHOU_PROFILE_PLAY_END_VIDEO",
		6:  "KUAISHOU_SPLASH_SLOT_VIDEO",
		7:  "KUAISHOU_SPLASH_SLOT_PIC_TEXT",
		8:  "KUAISHOU_SPLASH_TOPVIEW_SLOT_VIDEO",
		9:  "KUAISHOU_SPLASH_TOPVIEW_SLOT_PIC_TEXT",
		10: "FEED_GROUP_PIC_TEXT",
		11: "FEED_SMALL_PIC_TEXT",
		12: "FEED_LARGE_PIC_TEXT",
	}
	AdTypeEnum_AdType_value = map[string]int32{
		"NO_AD_TYPE":                                 0,
		"KUAISHOU_EXPLORE_FEED_VIDEO":                1,
		"KUAISHOU_PROFILE_BETWEEN_COMMENTS_PIC_TEXT": 2,
		"KUAISHOU_PROFILE_BETWEEN_COMMENTS_VIDEO":    3,
		"KUAISHOU_PROFILE_PLAY_END_PIC_TEXT":         4,
		"KUAISHOU_PROFILE_PLAY_END_VIDEO":            5,
		"KUAISHOU_SPLASH_SLOT_VIDEO":                 6,
		"KUAISHOU_SPLASH_SLOT_PIC_TEXT":              7,
		"KUAISHOU_SPLASH_TOPVIEW_SLOT_VIDEO":         8,
		"KUAISHOU_SPLASH_TOPVIEW_SLOT_PIC_TEXT":      9,
		"FEED_GROUP_PIC_TEXT":                        10,
		"FEED_SMALL_PIC_TEXT":                        11,
		"FEED_LARGE_PIC_TEXT":                        12,
	}
)

func (x AdTypeEnum_AdType) Enum() *AdTypeEnum_AdType {
	p := new(AdTypeEnum_AdType)
	*p = x
	return p
}

func (x AdTypeEnum_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdTypeEnum_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_kuaishou_proto_enumTypes[14].Descriptor()
}

func (AdTypeEnum_AdType) Type() protoreflect.EnumType {
	return &file_kuaishou_proto_enumTypes[14]
}

func (x AdTypeEnum_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AdTypeEnum_AdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AdTypeEnum_AdType(num)
	return nil
}

// Deprecated: Use AdTypeEnum_AdType.Descriptor instead.
func (AdTypeEnum_AdType) EnumDescriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{32, 0}
}

type Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  *uint32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`   // 必填！宽度
	Height *uint32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"` // 必填！高度
}

func (x *Size) Reset() {
	*x = Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Size) ProtoMessage() {}

func (x *Size) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Size.ProtoReflect.Descriptor instead.
func (*Size) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{0}
}

func (x *Size) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Size) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

type Version struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Major *uint32 `protobuf:"varint,1,opt,name=major" json:"major,omitempty"` // 必填！
	Minor *uint32 `protobuf:"varint,2,opt,name=minor" json:"minor,omitempty"` // 选填！
	Micro *uint32 `protobuf:"varint,3,opt,name=micro" json:"micro,omitempty"` // 选填！
}

func (x *Version) Reset() {
	*x = Version{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Version) ProtoMessage() {}

func (x *Version) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Version.ProtoReflect.Descriptor instead.
func (*Version) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{1}
}

func (x *Version) GetMajor() uint32 {
	if x != nil && x.Major != nil {
		return *x.Major
	}
	return 0
}

func (x *Version) GetMinor() uint32 {
	if x != nil && x.Minor != nil {
		return *x.Minor
	}
	return 0
}

func (x *Version) GetMicro() uint32 {
	if x != nil && x.Micro != nil {
		return *x.Micro
	}
	return 0
}

type Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpId             *string             `protobuf:"bytes,1,req,name=imp_id,json=impId" json:"imp_id,omitempty"` //必填!曝光标识ID
	TagId             *string             `protobuf:"bytes,2,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"` //广告位标识ID
	Banner            *Banner             `protobuf:"bytes,3,opt,name=banner" json:"banner,omitempty"`            //Banner广告为
	Native            *Native             `protobuf:"bytes,4,opt,name=native" json:"native,omitempty"`
	Video             *Video              `protobuf:"bytes,5,opt,name=video" json:"video,omitempty"`
	Instl             *uint32             `protobuf:"varint,6,opt,name=instl,def=0" json:"instl,omitempty"`                        //是否是插屏广告
	BidFloor          *float64            `protobuf:"fixed64,7,opt,name=bid_floor,json=bidFloor,def=0" json:"bid_floor,omitempty"` //最低竞价价格
	Secure            *uint32             `protobuf:"varint,8,opt,name=secure" json:"secure,omitempty"`
	AdmType           *Imp_AdmType        `protobuf:"varint,9,opt,name=adm_type,json=admType,enum=kuaishou.ad.adx.Imp_AdmType" json:"adm_type,omitempty"`
	AdsCount          *uint32             `protobuf:"varint,10,opt,name=ads_count,json=adsCount" json:"ads_count,omitempty"` //请求的广告数
	Pmp               *Pmp                `protobuf:"bytes,11,opt,name=pmp" json:"pmp,omitempty"`
	CpmBidFloor       *float64            `protobuf:"fixed64,12,opt,name=cpm_bid_floor,json=cpmBidFloor,def=0" json:"cpm_bid_floor,omitempty"`                                 //CPM最低竞价价格
	AdType            []AdTypeEnum_AdType `protobuf:"varint,13,rep,name=ad_type,json=adType,enum=kuaishou.ad.adx.AdTypeEnum_AdType" json:"ad_type,omitempty"`                  // 可接受的广告类型列表
	DateTimestamp     []uint64            `protobuf:"varint,14,rep,name=date_timestamp,json=dateTimestamp" json:"date_timestamp,omitempty"`                                    //开屏广告需返回广告的投放日期列表，单位:日期，如值1567267200表示 2019年9月1号
	CreativeType      []Adm_CreativeType  `protobuf:"varint,15,rep,name=creative_type,json=creativeType,enum=kuaishou.ad.adx.Adm_CreativeType" json:"creative_type,omitempty"` // 可接受的创意类型
	AdStyle           *int32              `protobuf:"varint,16,opt,name=ad_style,json=adStyle" json:"ad_style,omitempty"`                                                      //广告位样式  1: 信息流、2: 激励视频、3：插屏、4：开屏、5：banner
	AppId             *string             `protobuf:"bytes,17,opt,name=app_id,json=appId" json:"app_id,omitempty"`                                                             // app标识
	PosId             *string             `protobuf:"bytes,18,opt,name=pos_id,json=posId" json:"pos_id,omitempty"`                                                             // 广告位标识 pos_id
	MediumPackageName *string             `protobuf:"bytes,20,opt,name=medium_package_name,json=mediumPackageName" json:"medium_package_name,omitempty"`
	BlockingKeyword   *string             `protobuf:"bytes,25,opt,name=blocking_keyword,json=blockingKeyword" json:"blocking_keyword,omitempty"` // 搜索广告位-搜索词
}

// Default values for Imp fields.
const (
	Default_Imp_Instl       = uint32(0)
	Default_Imp_BidFloor    = float64(0)
	Default_Imp_CpmBidFloor = float64(0)
)

func (x *Imp) Reset() {
	*x = Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Imp) ProtoMessage() {}

func (x *Imp) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Imp.ProtoReflect.Descriptor instead.
func (*Imp) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{2}
}

func (x *Imp) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *Imp) GetTagId() string {
	if x != nil && x.TagId != nil {
		return *x.TagId
	}
	return ""
}

func (x *Imp) GetBanner() *Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *Imp) GetNative() *Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *Imp) GetVideo() *Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Imp) GetInstl() uint32 {
	if x != nil && x.Instl != nil {
		return *x.Instl
	}
	return Default_Imp_Instl
}

func (x *Imp) GetBidFloor() float64 {
	if x != nil && x.BidFloor != nil {
		return *x.BidFloor
	}
	return Default_Imp_BidFloor
}

func (x *Imp) GetSecure() uint32 {
	if x != nil && x.Secure != nil {
		return *x.Secure
	}
	return 0
}

func (x *Imp) GetAdmType() Imp_AdmType {
	if x != nil && x.AdmType != nil {
		return *x.AdmType
	}
	return Imp_UNKOWN
}

func (x *Imp) GetAdsCount() uint32 {
	if x != nil && x.AdsCount != nil {
		return *x.AdsCount
	}
	return 0
}

func (x *Imp) GetPmp() *Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *Imp) GetCpmBidFloor() float64 {
	if x != nil && x.CpmBidFloor != nil {
		return *x.CpmBidFloor
	}
	return Default_Imp_CpmBidFloor
}

func (x *Imp) GetAdType() []AdTypeEnum_AdType {
	if x != nil {
		return x.AdType
	}
	return nil
}

func (x *Imp) GetDateTimestamp() []uint64 {
	if x != nil {
		return x.DateTimestamp
	}
	return nil
}

func (x *Imp) GetCreativeType() []Adm_CreativeType {
	if x != nil {
		return x.CreativeType
	}
	return nil
}

func (x *Imp) GetAdStyle() int32 {
	if x != nil && x.AdStyle != nil {
		return *x.AdStyle
	}
	return 0
}

func (x *Imp) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *Imp) GetPosId() string {
	if x != nil && x.PosId != nil {
		return *x.PosId
	}
	return ""
}

func (x *Imp) GetMediumPackageName() string {
	if x != nil && x.MediumPackageName != nil {
		return *x.MediumPackageName
	}
	return ""
}

func (x *Imp) GetBlockingKeyword() string {
	if x != nil && x.BlockingKeyword != nil {
		return *x.BlockingKeyword
	}
	return ""
}

type Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrivateAuction *uint32 `protobuf:"varint,1,opt,name=private_auction,json=privateAuction" json:"private_auction,omitempty"`
	Deal           []*Deal `protobuf:"bytes,2,rep,name=deal" json:"deal,omitempty"`
}

func (x *Pmp) Reset() {
	*x = Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pmp) ProtoMessage() {}

func (x *Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pmp.ProtoReflect.Descriptor instead.
func (*Pmp) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{3}
}

func (x *Pmp) GetPrivateAuction() uint32 {
	if x != nil && x.PrivateAuction != nil {
		return *x.PrivateAuction
	}
	return 0
}

func (x *Pmp) GetDeal() []*Deal {
	if x != nil {
		return x.Deal
	}
	return nil
}

type Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DealId      *string  `protobuf:"bytes,1,req,name=deal_id,json=dealId" json:"deal_id,omitempty"`         //直接交易的唯一ID
	BidFloor    *float32 `protobuf:"fixed32,2,opt,name=bid_floor,json=bidFloor" json:"bid_floor,omitempty"` //本次展示的最低竞价
	BidFloorCur *string  `protobuf:"bytes,3,opt,name=bid_floor_cur,json=bidFloorCur" json:"bid_floor_cur,omitempty"`
	At          *uint32  `protobuf:"varint,4,opt,name=at" json:"at,omitempty"`
	Wseat       []string `protobuf:"bytes,5,rep,name=wseat" json:"wseat,omitempty"`
	WaDomain    []string `protobuf:"bytes,6,rep,name=wa_domain,json=waDomain" json:"wa_domain,omitempty"`
}

func (x *Deal) Reset() {
	*x = Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deal) ProtoMessage() {}

func (x *Deal) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deal.ProtoReflect.Descriptor instead.
func (*Deal) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{4}
}

func (x *Deal) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *Deal) GetBidFloor() float32 {
	if x != nil && x.BidFloor != nil {
		return *x.BidFloor
	}
	return 0
}

func (x *Deal) GetBidFloorCur() string {
	if x != nil && x.BidFloorCur != nil {
		return *x.BidFloorCur
	}
	return ""
}

func (x *Deal) GetAt() uint32 {
	if x != nil && x.At != nil {
		return *x.At
	}
	return 0
}

func (x *Deal) GetWseat() []string {
	if x != nil {
		return x.Wseat
	}
	return nil
}

func (x *Deal) GetWaDomain() []string {
	if x != nil {
		return x.WaDomain
	}
	return nil
}

type Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BannerId *string `protobuf:"bytes,1,req,name=banner_id,json=bannerId" json:"banner_id,omitempty"`
	Size     *Size   `protobuf:"bytes,2,opt,name=size" json:"size,omitempty"`
	Pos      *uint32 `protobuf:"varint,3,opt,name=pos" json:"pos,omitempty"` // 广告在屏幕上的位置
}

func (x *Banner) Reset() {
	*x = Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner) ProtoMessage() {}

func (x *Banner) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner.ProtoReflect.Descriptor instead.
func (*Banner) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{5}
}

func (x *Banner) GetBannerId() string {
	if x != nil && x.BannerId != nil {
		return *x.BannerId
	}
	return ""
}

func (x *Banner) GetSize() *Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *Banner) GetPos() uint32 {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return 0
}

type Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NativeId  *string  `protobuf:"bytes,1,req,name=native_id,json=nativeId" json:"native_id,omitempty"`
	Request   *string  `protobuf:"bytes,2,opt,name=request" json:"request,omitempty"` //遵循原生广告制定规格的请求信息
	Size      *Size    `protobuf:"bytes,3,opt,name=size" json:"size,omitempty"`       //广告位宽高
	Version   *Version `protobuf:"bytes,4,opt,name=version" json:"version,omitempty"` //request版本
	Api       []uint32 `protobuf:"varint,5,rep,name=api" json:"api,omitempty"`
	Battr     []uint32 `protobuf:"varint,6,rep,name=battr" json:"battr,omitempty"`                           //限制的物料属性
	SizeRatio *float32 `protobuf:"fixed32,7,opt,name=size_ratio,json=sizeRatio" json:"size_ratio,omitempty"` //广告位宽高比，如果size_ratio>0，优先取size_ratio，其次取size
}

func (x *Native) Reset() {
	*x = Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Native) ProtoMessage() {}

func (x *Native) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Native.ProtoReflect.Descriptor instead.
func (*Native) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{6}
}

func (x *Native) GetNativeId() string {
	if x != nil && x.NativeId != nil {
		return *x.NativeId
	}
	return ""
}

func (x *Native) GetRequest() string {
	if x != nil && x.Request != nil {
		return *x.Request
	}
	return ""
}

func (x *Native) GetSize() *Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *Native) GetVersion() *Version {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *Native) GetApi() []uint32 {
	if x != nil {
		return x.Api
	}
	return nil
}

func (x *Native) GetBattr() []uint32 {
	if x != nil {
		return x.Battr
	}
	return nil
}

func (x *Native) GetSizeRatio() float32 {
	if x != nil && x.SizeRatio != nil {
		return *x.SizeRatio
	}
	return 0
}

type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mines            []string `protobuf:"bytes,1,rep,name=mines" json:"mines,omitempty"` //必填!支持播放的广告格式
	MinDuration      *uint32  `protobuf:"varint,2,opt,name=min_duration,json=minDuration" json:"min_duration,omitempty"`
	MaxDuration      *uint32  `protobuf:"varint,3,opt,name=max_duration,json=maxDuration" json:"max_duration,omitempty"`
	Protocol         []uint32 `protobuf:"varint,4,rep,name=protocol" json:"protocol,omitempty"`
	Size             *Size    `protobuf:"bytes,5,opt,name=size" json:"size,omitempty"`
	VideoSize        *float32 `protobuf:"fixed32,6,opt,name=video_size,json=videoSize" json:"video_size,omitempty"` //视频大小
	Battr            []uint32 `protobuf:"varint,7,rep,name=battr" json:"battr,omitempty"`
	FrequencyCapping *bool    `protobuf:"varint,8,opt,name=frequency_capping,json=frequencyCapping,def=0" json:"frequency_capping,omitempty"`
}

// Default values for Video fields.
const (
	Default_Video_FrequencyCapping = bool(false)
)

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{7}
}

func (x *Video) GetMines() []string {
	if x != nil {
		return x.Mines
	}
	return nil
}

func (x *Video) GetMinDuration() uint32 {
	if x != nil && x.MinDuration != nil {
		return *x.MinDuration
	}
	return 0
}

func (x *Video) GetMaxDuration() uint32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *Video) GetProtocol() []uint32 {
	if x != nil {
		return x.Protocol
	}
	return nil
}

func (x *Video) GetSize() *Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *Video) GetVideoSize() float32 {
	if x != nil && x.VideoSize != nil {
		return *x.VideoSize
	}
	return 0
}

func (x *Video) GetBattr() []uint32 {
	if x != nil {
		return x.Battr
	}
	return nil
}

func (x *Video) GetFrequencyCapping() bool {
	if x != nil && x.FrequencyCapping != nil {
		return *x.FrequencyCapping
	}
	return Default_Video_FrequencyCapping
}

type App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     *string    `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id,omitempty"` //交易平台设定的appid
	Name      *string    `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                //应用名称
	Bundle    *string    `protobuf:"bytes,3,opt,name=bundle" json:"bundle,omitempty"`            //app包名
	Domain    *string    `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`            //app对应的domain
	StoreUrl  *string    `protobuf:"bytes,5,opt,name=store_url,json=storeUrl" json:"store_url,omitempty"`
	Cat       []string   `protobuf:"bytes,6,rep,name=cat" json:"cat,omitempty"`
	Version   *Version   `protobuf:"bytes,7,opt,name=version" json:"version,omitempty"`
	Paid      *bool      `protobuf:"varint,8,opt,name=paid,def=0" json:"paid,omitempty"`
	Publisher *Publisher `protobuf:"bytes,9,opt,name=publisher" json:"publisher,omitempty"`
	Keywords  *string    `protobuf:"bytes,10,opt,name=keywords" json:"keywords,omitempty"`
}

// Default values for App fields.
const (
	Default_App_Paid = bool(false)
)

func (x *App) Reset() {
	*x = App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*App) ProtoMessage() {}

func (x *App) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use App.ProtoReflect.Descriptor instead.
func (*App) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{8}
}

func (x *App) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *App) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *App) GetStoreUrl() string {
	if x != nil && x.StoreUrl != nil {
		return *x.StoreUrl
	}
	return ""
}

func (x *App) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *App) GetVersion() *Version {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *App) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return Default_App_Paid
}

func (x *App) GetPublisher() *Publisher {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *App) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

type Publisher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PublisherId *string  `protobuf:"bytes,1,opt,name=publisher_id,json=publisherId" json:"publisher_id,omitempty"`
	Name        *string  `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Cat         []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	Domain      *string  `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`
}

func (x *Publisher) Reset() {
	*x = Publisher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Publisher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Publisher) ProtoMessage() {}

func (x *Publisher) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Publisher.ProtoReflect.Descriptor instead.
func (*Publisher) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{9}
}

func (x *Publisher) GetPublisherId() string {
	if x != nil && x.PublisherId != nil {
		return *x.PublisherId
	}
	return ""
}

func (x *Publisher) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Publisher) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *Publisher) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

type Udid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idfa               *string `protobuf:"bytes,1,opt,name=idfa" json:"idfa,omitempty"`                                                          // iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}
	Imei               *string `protobuf:"bytes,2,opt,name=imei" json:"imei,omitempty"`                                                          // Android手机设备的IMEI，格式要求[0-9a-fA-F]{14,15}
	Mac                *string `protobuf:"bytes,3,opt,name=mac" json:"mac,omitempty"`                                                            // Android非手机设备的WiFi网卡MAC地址，格式要求[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}
	ImeiMd5            *string `protobuf:"bytes,4,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`                                     // Android手机设备的IMEI，经过MD5加密，格式要求[0-9A-Za-z]{32}
	AndroidId          *string `protobuf:"bytes,5,opt,name=android_id,json=androidId" json:"android_id,omitempty"`                               //必填！Android手机设备系统ID，格式要求[0-9A-Za-z]{16}
	IdfaMd5            *string `protobuf:"bytes,8,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`                                     // iOS设备的IDFA，经过MD5加密，格式要求[0-9A-Za-z]{32}
	AndroididMd5       *string `protobuf:"bytes,9,opt,name=androidid_md5,json=androididMd5" json:"androidid_md5,omitempty"`                      // Android手机设备系统ID，经过MD5加密，格式要求[0-9A-Za-z]{32}
	Oaid               *string `protobuf:"bytes,10,opt,name=oaid" json:"oaid,omitempty"`                                                         // Android手机匿名设备标识符
	CurrentCaid        *string `protobuf:"bytes,11,opt,name=current_caid,json=currentCaid" json:"current_caid,omitempty"`                        // 当前caid
	CurrentCaidVersion *string `protobuf:"bytes,12,opt,name=current_caid_version,json=currentCaidVersion" json:"current_caid_version,omitempty"` // 当前caid版本
	LastCaid           *string `protobuf:"bytes,13,opt,name=last_caid,json=lastCaid" json:"last_caid,omitempty"`                                 // 上一版本caid
	LastCaidVersion    *string `protobuf:"bytes,14,opt,name=last_caid_version,json=lastCaidVersion" json:"last_caid_version,omitempty"`          // 上一版本caid版本
	Paid               *string `protobuf:"bytes,16,opt,name=paid" json:"paid,omitempty"`                                                         // 拼多多设备指纹
	Aaid               *string `protobuf:"bytes,17,opt,name=aaid" json:"aaid,omitempty"`                                                         // 阿里设备指纹
	CurrentPaid_1_5    *string `protobuf:"bytes,21,opt,name=current_paid_1_5,json=currentPaid15" json:"current_paid_1_5,omitempty"`              // 拼多多设备指纹paid1.5, 当前版本
	LastPaid_1_5       *string `protobuf:"bytes,22,opt,name=last_paid_1_5,json=lastPaid15" json:"last_paid_1_5,omitempty"`                       // 拼多多设备指纹paid1.5, 上一个版本p
}

func (x *Udid) Reset() {
	*x = Udid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Udid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Udid) ProtoMessage() {}

func (x *Udid) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Udid.ProtoReflect.Descriptor instead.
func (*Udid) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{10}
}

func (x *Udid) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *Udid) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *Udid) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *Udid) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *Udid) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *Udid) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *Udid) GetAndroididMd5() string {
	if x != nil && x.AndroididMd5 != nil {
		return *x.AndroididMd5
	}
	return ""
}

func (x *Udid) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *Udid) GetCurrentCaid() string {
	if x != nil && x.CurrentCaid != nil {
		return *x.CurrentCaid
	}
	return ""
}

func (x *Udid) GetCurrentCaidVersion() string {
	if x != nil && x.CurrentCaidVersion != nil {
		return *x.CurrentCaidVersion
	}
	return ""
}

func (x *Udid) GetLastCaid() string {
	if x != nil && x.LastCaid != nil {
		return *x.LastCaid
	}
	return ""
}

func (x *Udid) GetLastCaidVersion() string {
	if x != nil && x.LastCaidVersion != nil {
		return *x.LastCaidVersion
	}
	return ""
}

func (x *Udid) GetPaid() string {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return ""
}

func (x *Udid) GetAaid() string {
	if x != nil && x.Aaid != nil {
		return *x.Aaid
	}
	return ""
}

func (x *Udid) GetCurrentPaid_1_5() string {
	if x != nil && x.CurrentPaid_1_5 != nil {
		return *x.CurrentPaid_1_5
	}
	return ""
}

func (x *Udid) GetLastPaid_1_5() string {
	if x != nil && x.LastPaid_1_5 != nil {
		return *x.LastPaid_1_5
	}
	return ""
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAgent         *string            `protobuf:"bytes,1,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	Geo               *Geo               `protobuf:"bytes,2,opt,name=geo" json:"geo,omitempty"`
	Ip                *string            `protobuf:"bytes,3,opt,name=ip" json:"ip,omitempty"`
	Ipv6              *string            `protobuf:"bytes,4,opt,name=ipv6" json:"ipv6,omitempty"`
	DeviceType        *Device_DeviceType `protobuf:"varint,5,opt,name=device_type,json=deviceType,enum=kuaishou.ad.adx.Device_DeviceType" json:"device_type,omitempty"`
	OsType            *Device_OsType     `protobuf:"varint,6,opt,name=os_type,json=osType,enum=kuaishou.ad.adx.Device_OsType" json:"os_type,omitempty"` //操作系统类型
	OsVersion         *Version           `protobuf:"bytes,7,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`                            //操作系统版本
	Make              *string            `protobuf:"bytes,8,opt,name=make" json:"make,omitempty"`                                                       //设备制造商
	Model             *string            `protobuf:"bytes,9,opt,name=model" json:"model,omitempty"`                                                     //设备型号(手机品牌+型号拼接而成)
	ScreenSize        *Size              `protobuf:"bytes,10,opt,name=screen_size,json=screenSize" json:"screen_size,omitempty"`                        //屏幕尺寸
	Udid              *Udid              `protobuf:"bytes,11,opt,name=udid" json:"udid,omitempty"`                                                      //唯一设备标识
	InstallApp        []string           `protobuf:"bytes,12,rep,name=install_app,json=installApp" json:"install_app,omitempty"`                        // 安装的app列表
	PackageName       *string            `protobuf:"bytes,13,opt,name=package_name,json=packageName" json:"package_name,omitempty"`                     // 包名
	Ua                *string            `protobuf:"bytes,14,opt,name=ua" json:"ua,omitempty"`                                                          // webview user-agent
	BootMark          *string            `protobuf:"bytes,15,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`                              // 系统启动标识,反作弊字段
	UpdateMark        *string            `protobuf:"bytes,16,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`                        // 系统启动标识,反作弊字段
	ModelWithoutBrand *string            `protobuf:"bytes,17,opt,name=model_without_brand,json=modelWithoutBrand" json:"model_without_brand,omitempty"` // 设备型号（只有型号没有品牌）
	BrowserUa         *string            `protobuf:"bytes,18,opt,name=browser_ua,json=browserUa" json:"browser_ua,omitempty"`                           // 浏览器ua字段
	InstallAppMd5     []string           `protobuf:"bytes,19,rep,name=install_app_md5,json=installAppMd5" json:"install_app_md5,omitempty"`             // 安装的app列表(加密)
	DeviceModel       *string            `protobuf:"bytes,20,opt,name=device_model,json=deviceModel" json:"device_model,omitempty"`                     // 设备型号 (设备原始信息，不经过加工）
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{11}
}

func (x *Device) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *Device) GetGeo() *Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *Device) GetDeviceType() Device_DeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return Device_UNKNOWN_DEVICE_TYPE
}

func (x *Device) GetOsType() Device_OsType {
	if x != nil && x.OsType != nil {
		return *x.OsType
	}
	return Device_UNKNOWN_OS_TYPE
}

func (x *Device) GetOsVersion() *Version {
	if x != nil {
		return x.OsVersion
	}
	return nil
}

func (x *Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *Device) GetScreenSize() *Size {
	if x != nil {
		return x.ScreenSize
	}
	return nil
}

func (x *Device) GetUdid() *Udid {
	if x != nil {
		return x.Udid
	}
	return nil
}

func (x *Device) GetInstallApp() []string {
	if x != nil {
		return x.InstallApp
	}
	return nil
}

func (x *Device) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *Device) GetModelWithoutBrand() string {
	if x != nil && x.ModelWithoutBrand != nil {
		return *x.ModelWithoutBrand
	}
	return ""
}

func (x *Device) GetBrowserUa() string {
	if x != nil && x.BrowserUa != nil {
		return *x.BrowserUa
	}
	return ""
}

func (x *Device) GetInstallAppMd5() []string {
	if x != nil {
		return x.InstallAppMd5
	}
	return nil
}

func (x *Device) GetDeviceModel() string {
	if x != nil && x.DeviceModel != nil {
		return *x.DeviceModel
	}
	return ""
}

type Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat      *float64     `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"` //纬度
	Lon      *float64     `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"` //经度
	Type     *Geo_GeoType `protobuf:"varint,3,opt,name=type,enum=kuaishou.ad.adx.Geo_GeoType" json:"type,omitempty"`
	Country  *string      `protobuf:"bytes,4,opt,name=country" json:"country,omitempty"` //国家编码
	Province *string      `protobuf:"bytes,5,opt,name=province" json:"province,omitempty"`
	Region   *string      `protobuf:"bytes,6,opt,name=region" json:"region,omitempty"`
	City     *string      `protobuf:"bytes,7,opt,name=city" json:"city,omitempty"`                      //城市名
	GeoHash  *string      `protobuf:"bytes,8,opt,name=geo_hash,json=geoHash" json:"geo_hash,omitempty"` // geohash
}

func (x *Geo) Reset() {
	*x = Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Geo) ProtoMessage() {}

func (x *Geo) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Geo.ProtoReflect.Descriptor instead.
func (*Geo) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{12}
}

func (x *Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

func (x *Geo) GetType() Geo_GeoType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Geo_UNKNOWN
}

func (x *Geo) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *Geo) GetProvince() string {
	if x != nil && x.Province != nil {
		return *x.Province
	}
	return ""
}

func (x *Geo) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *Geo) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *Geo) GetGeoHash() string {
	if x != nil && x.GeoHash != nil {
		return *x.GeoHash
	}
	return ""
}

type Segment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentId *string `protobuf:"bytes,1,req,name=segment_id,json=segmentId" json:"segment_id,omitempty"`
	Name      *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Value     *string `protobuf:"bytes,3,opt,name=value" json:"value,omitempty"`
}

func (x *Segment) Reset() {
	*x = Segment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Segment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Segment) ProtoMessage() {}

func (x *Segment) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Segment.ProtoReflect.Descriptor instead.
func (*Segment) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{13}
}

func (x *Segment) GetSegmentId() string {
	if x != nil && x.SegmentId != nil {
		return *x.SegmentId
	}
	return ""
}

func (x *Segment) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Segment) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataId  *string    `protobuf:"bytes,1,req,name=data_id,json=dataId" json:"data_id,omitempty"`
	Name    *string    `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Segment []*Segment `protobuf:"bytes,3,rep,name=segment" json:"segment,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{14}
}

func (x *Data) GetDataId() string {
	if x != nil && x.DataId != nil {
		return *x.DataId
	}
	return ""
}

func (x *Data) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Data) GetSegment() []*Segment {
	if x != nil {
		return x.Segment
	}
	return nil
}

type UserEmb struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserEmbA []float32 `protobuf:"fixed32,1,rep,name=user_emb_a,json=userEmbA" json:"user_emb_a,omitempty"` // 向量a，一开始默认使用字段
	UserEmbB []float32 `protobuf:"fixed32,2,rep,name=user_emb_b,json=userEmbB" json:"user_emb_b,omitempty"` // 向量b，向量升级时的切换字段
}

func (x *UserEmb) Reset() {
	*x = UserEmb{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserEmb) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserEmb) ProtoMessage() {}

func (x *UserEmb) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserEmb.ProtoReflect.Descriptor instead.
func (*UserEmb) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{15}
}

func (x *UserEmb) GetUserEmbA() []float32 {
	if x != nil {
		return x.UserEmbA
	}
	return nil
}

func (x *UserEmb) GetUserEmbB() []float32 {
	if x != nil {
		return x.UserEmbB
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        *string      `protobuf:"bytes,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	BuyerId       *string      `protobuf:"bytes,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	Gender        *User_Gender `protobuf:"varint,3,opt,name=gender,enum=kuaishou.ad.adx.User_Gender" json:"gender,omitempty"`
	Age           *uint32      `protobuf:"varint,5,opt,name=age" json:"age,omitempty"`
	Keywords      *string      `protobuf:"bytes,6,opt,name=keywords" json:"keywords,omitempty"` //逗号分隔的关键词，兴趣和意图
	CustomData    *string      `protobuf:"bytes,7,opt,name=custom_data,json=customData" json:"custom_data,omitempty"`
	Data          []*Data      `protobuf:"bytes,8,rep,name=data" json:"data,omitempty"`
	UserTags      []string     `protobuf:"bytes,9,rep,name=user_tags,json=userTags" json:"user_tags,omitempty"`
	Orientation   []uint64     `protobuf:"varint,10,rep,name=orientation" json:"orientation,omitempty"`
	UserEmbedding *UserEmb     `protobuf:"bytes,11,opt,name=user_embedding,json=userEmbedding" json:"user_embedding,omitempty"` // 用户embedding向量
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{16}
}

func (x *User) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

func (x *User) GetBuyerId() string {
	if x != nil && x.BuyerId != nil {
		return *x.BuyerId
	}
	return ""
}

func (x *User) GetGender() User_Gender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return User_U
}

func (x *User) GetAge() uint32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

func (x *User) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *User) GetCustomData() string {
	if x != nil && x.CustomData != nil {
		return *x.CustomData
	}
	return ""
}

func (x *User) GetData() []*Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *User) GetUserTags() []string {
	if x != nil {
		return x.UserTags
	}
	return nil
}

func (x *User) GetOrientation() []uint64 {
	if x != nil {
		return x.Orientation
	}
	return nil
}

func (x *User) GetUserEmbedding() *UserEmb {
	if x != nil {
		return x.UserEmbedding
	}
	return nil
}

type Tracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingEvent *Tracking_TrackingEvent `protobuf:"varint,1,opt,name=tracking_event,json=trackingEvent,enum=kuaishou.ad.adx.Tracking_TrackingEvent" json:"tracking_event,omitempty"` // 被跟踪的广告展示过程事件
	TrackingUrl   []string                `protobuf:"bytes,2,rep,name=tracking_url,json=trackingUrl" json:"tracking_url,omitempty"`                                                    // 事件监控URL
}

func (x *Tracking) Reset() {
	*x = Tracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tracking) ProtoMessage() {}

func (x *Tracking) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tracking.ProtoReflect.Descriptor instead.
func (*Tracking) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{17}
}

func (x *Tracking) GetTrackingEvent() Tracking_TrackingEvent {
	if x != nil && x.TrackingEvent != nil {
		return *x.TrackingEvent
	}
	return Tracking_AD_CLICK
}

func (x *Tracking) GetTrackingUrl() []string {
	if x != nil {
		return x.TrackingUrl
	}
	return nil
}

type SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid           []*Bid  `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`
	Seat          *string `protobuf:"bytes,2,opt,name=seat" json:"seat,omitempty"` //出价者
	CookieMapping *bool   `protobuf:"varint,3,opt,name=cookie_mapping,json=cookieMapping,def=0" json:"cookie_mapping,omitempty"`
	BidGroup      *bool   `protobuf:"varint,4,opt,name=bid_group,json=bidGroup,def=0" json:"bid_group,omitempty"` //false表示可以独立胜出，true表示必须整组胜出或者失败
}

// Default values for SeatBid fields.
const (
	Default_SeatBid_CookieMapping = bool(false)
	Default_SeatBid_BidGroup      = bool(false)
)

func (x *SeatBid) Reset() {
	*x = SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeatBid) ProtoMessage() {}

func (x *SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeatBid.ProtoReflect.Descriptor instead.
func (*SeatBid) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{18}
}

func (x *SeatBid) GetBid() []*Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *SeatBid) GetSeat() string {
	if x != nil && x.Seat != nil {
		return *x.Seat
	}
	return ""
}

func (x *SeatBid) GetCookieMapping() bool {
	if x != nil && x.CookieMapping != nil {
		return *x.CookieMapping
	}
	return Default_SeatBid_CookieMapping
}

func (x *SeatBid) GetBidGroup() bool {
	if x != nil && x.BidGroup != nil {
		return *x.BidGroup
	}
	return Default_SeatBid_BidGroup
}

type Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidId                *string                   `protobuf:"bytes,1,req,name=bid_id,json=bidId" json:"bid_id,omitempty"` //竞拍者生成的竞价id，用于记录日志或者行为追踪
	ImpId                *string                   `protobuf:"bytes,2,req,name=imp_id,json=impId" json:"imp_id,omitempty"` //关联竞价请求中的Imp对象的ID
	Price                *float64                  `protobuf:"fixed64,3,req,name=price" json:"price,omitempty"`
	AdId                 *string                   `protobuf:"bytes,4,req,name=ad_id,json=adId" json:"ad_id,omitempty"`                //广告ID
	NoticeUrl            *string                   `protobuf:"bytes,5,opt,name=notice_url,json=noticeUrl" json:"notice_url,omitempty"` //Win notice URL
	AdTracking           []*Tracking               `protobuf:"bytes,6,rep,name=ad_tracking,json=adTracking" json:"ad_tracking,omitempty"`
	Adm                  *Adm                      `protobuf:"bytes,7,req,name=adm" json:"adm,omitempty"`
	Adomain              []string                  `protobuf:"bytes,8,rep,name=adomain" json:"adomain,omitempty"` //用于限制性广告域名
	Bundle               *string                   `protobuf:"bytes,9,opt,name=bundle" json:"bundle,omitempty"`
	CreativeId           *string                   `protobuf:"bytes,11,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	Cat                  []string                  `protobuf:"bytes,12,rep,name=cat" json:"cat,omitempty"`
	Attr                 []string                  `protobuf:"bytes,13,rep,name=attr" json:"attr,omitempty"`
	Size                 *Size                     `protobuf:"bytes,14,opt,name=size" json:"size,omitempty"`                                                                                                                //如果是信息流广告，该值表示封面的大小；如果是图文广告，该值表示图片素材的大小
	AdvertiserUsername   *string                   `protobuf:"bytes,15,opt,name=advertiser_username,json=advertiserUsername" json:"advertiser_username,omitempty"`                                                          // 广告主账户名
	Industry             *string                   `protobuf:"bytes,16,opt,name=industry" json:"industry,omitempty"`                                                                                                        //创意所属行业
	BidExtData           *string                   `protobuf:"bytes,17,opt,name=bid_ext_data,json=bidExtData" json:"bid_ext_data,omitempty"`                                                                                // dsp返回的usertag等信息
	BidType              *Bid_BidType              `protobuf:"varint,18,opt,name=bid_type,json=bidType,enum=kuaishou.ad.adx.Bid_BidType" json:"bid_type,omitempty"`                                                         //指定广告参与的竞价方式
	VideoSize            *Size                     `protobuf:"bytes,19,opt,name=video_size,json=videoSize" json:"video_size,omitempty"`                                                                                     //创意视频尺寸
	Ocpx                 *Ocpx                     `protobuf:"bytes,20,opt,name=ocpx" json:"ocpx,omitempty"`                                                                                                                //ocpx参数
	LandingPageShowStyle *Bid_LandingPageShowSytle `protobuf:"varint,21,opt,name=landing_page_show_style,json=landingPageShowStyle,enum=kuaishou.ad.adx.Bid_LandingPageShowSytle" json:"landing_page_show_style,omitempty"` //落地页样式
	LandingPageType      *int32                    `protobuf:"varint,22,opt,name=landing_page_type,json=landingPageType" json:"landing_page_type,omitempty"`                                                                //落地页类型
	Category             *Category                 `protobuf:"bytes,23,opt,name=category" json:"category,omitempty"`                                                                                                        //类目信息
	CommodityPrice       *float64                  `protobuf:"fixed64,24,opt,name=commodity_price,json=commodityPrice" json:"commodity_price,omitempty"`                                                                    //推广商品价格
	AdvertiserId         *uint64                   `protobuf:"varint,25,opt,name=advertiser_id,json=advertiserId" json:"advertiser_id,omitempty"`                                                                           // 广告主账户id
	PlanId               *uint64                   `protobuf:"varint,26,opt,name=plan_id,json=planId" json:"plan_id,omitempty"`                                                                                             // 计划id
	UnitId               *uint64                   `protobuf:"varint,27,opt,name=unit_id,json=unitId" json:"unit_id,omitempty"`                                                                                             // 单元id
	DealId               *string                   `protobuf:"bytes,28,opt,name=deal_id,json=dealId" json:"deal_id,omitempty"`                                                                                              // 直接交易的唯一ID;
	ActivityCardSize     *Size                     `protobuf:"bytes,29,opt,name=activity_card_size,json=activityCardSize" json:"activity_card_size,omitempty"`                                                              // 活动卡片尺寸
	Card                 *Card                     `protobuf:"bytes,32,opt,name=card" json:"card,omitempty"`                                                                                                                // 海量回传卡片信息
	UserId               *uint64                   `protobuf:"varint,34,opt,name=user_id,json=userId" json:"user_id,omitempty"`                                                                                             // 参竞账户对应的快手用户id
}

func (x *Bid) Reset() {
	*x = Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bid) ProtoMessage() {}

func (x *Bid) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bid.ProtoReflect.Descriptor instead.
func (*Bid) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{19}
}

func (x *Bid) GetBidId() string {
	if x != nil && x.BidId != nil {
		return *x.BidId
	}
	return ""
}

func (x *Bid) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *Bid) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *Bid) GetAdId() string {
	if x != nil && x.AdId != nil {
		return *x.AdId
	}
	return ""
}

func (x *Bid) GetNoticeUrl() string {
	if x != nil && x.NoticeUrl != nil {
		return *x.NoticeUrl
	}
	return ""
}

func (x *Bid) GetAdTracking() []*Tracking {
	if x != nil {
		return x.AdTracking
	}
	return nil
}

func (x *Bid) GetAdm() *Adm {
	if x != nil {
		return x.Adm
	}
	return nil
}

func (x *Bid) GetAdomain() []string {
	if x != nil {
		return x.Adomain
	}
	return nil
}

func (x *Bid) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *Bid) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *Bid) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *Bid) GetAttr() []string {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *Bid) GetSize() *Size {
	if x != nil {
		return x.Size
	}
	return nil
}

func (x *Bid) GetAdvertiserUsername() string {
	if x != nil && x.AdvertiserUsername != nil {
		return *x.AdvertiserUsername
	}
	return ""
}

func (x *Bid) GetIndustry() string {
	if x != nil && x.Industry != nil {
		return *x.Industry
	}
	return ""
}

func (x *Bid) GetBidExtData() string {
	if x != nil && x.BidExtData != nil {
		return *x.BidExtData
	}
	return ""
}

func (x *Bid) GetBidType() Bid_BidType {
	if x != nil && x.BidType != nil {
		return *x.BidType
	}
	return Bid_NO_TYPE
}

func (x *Bid) GetVideoSize() *Size {
	if x != nil {
		return x.VideoSize
	}
	return nil
}

func (x *Bid) GetOcpx() *Ocpx {
	if x != nil {
		return x.Ocpx
	}
	return nil
}

func (x *Bid) GetLandingPageShowStyle() Bid_LandingPageShowSytle {
	if x != nil && x.LandingPageShowStyle != nil {
		return *x.LandingPageShowStyle
	}
	return Bid_BLUE_BAR
}

func (x *Bid) GetLandingPageType() int32 {
	if x != nil && x.LandingPageType != nil {
		return *x.LandingPageType
	}
	return 0
}

func (x *Bid) GetCategory() *Category {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Bid) GetCommodityPrice() float64 {
	if x != nil && x.CommodityPrice != nil {
		return *x.CommodityPrice
	}
	return 0
}

func (x *Bid) GetAdvertiserId() uint64 {
	if x != nil && x.AdvertiserId != nil {
		return *x.AdvertiserId
	}
	return 0
}

func (x *Bid) GetPlanId() uint64 {
	if x != nil && x.PlanId != nil {
		return *x.PlanId
	}
	return 0
}

func (x *Bid) GetUnitId() uint64 {
	if x != nil && x.UnitId != nil {
		return *x.UnitId
	}
	return 0
}

func (x *Bid) GetDealId() string {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return ""
}

func (x *Bid) GetActivityCardSize() *Size {
	if x != nil {
		return x.ActivityCardSize
	}
	return nil
}

func (x *Bid) GetCard() *Card {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *Bid) GetUserId() uint64 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

type Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardId      *string           `protobuf:"bytes,1,opt,name=card_id,json=cardId" json:"card_id,omitempty"`              // 卡片id
	CardUrl     *string           `protobuf:"bytes,2,opt,name=card_url,json=cardUrl" json:"card_url,omitempty"`           // 卡片url
	CardWidth   *int32            `protobuf:"varint,3,opt,name=card_width,json=cardWidth" json:"card_width,omitempty"`    // 卡片宽
	CardHeight  *int32            `protobuf:"varint,4,opt,name=card_height,json=cardHeight" json:"card_height,omitempty"` // 卡片高
	CardTitle   *string           `protobuf:"bytes,5,opt,name=card_title,json=cardTitle" json:"card_title,omitempty"`     // 卡片title
	IconUrl     *string           `protobuf:"bytes,6,opt,name=icon_url,json=iconUrl" json:"icon_url,omitempty"`
	IconWidth   *int32            `protobuf:"varint,7,opt,name=icon_width,json=iconWidth" json:"icon_width,omitempty"`                                               // 卡片宽
	IconHeight  *int32            `protobuf:"varint,8,opt,name=icon_height,json=iconHeight" json:"icon_height,omitempty"`                                            // 卡片高
	AdxCardType *Card_AdxCardType `protobuf:"varint,9,opt,name=adx_card_type,json=adxCardType,enum=kuaishou.ad.adx.Card_AdxCardType" json:"adx_card_type,omitempty"` //卡片样式
}

func (x *Card) Reset() {
	*x = Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{20}
}

func (x *Card) GetCardId() string {
	if x != nil && x.CardId != nil {
		return *x.CardId
	}
	return ""
}

func (x *Card) GetCardUrl() string {
	if x != nil && x.CardUrl != nil {
		return *x.CardUrl
	}
	return ""
}

func (x *Card) GetCardWidth() int32 {
	if x != nil && x.CardWidth != nil {
		return *x.CardWidth
	}
	return 0
}

func (x *Card) GetCardHeight() int32 {
	if x != nil && x.CardHeight != nil {
		return *x.CardHeight
	}
	return 0
}

func (x *Card) GetCardTitle() string {
	if x != nil && x.CardTitle != nil {
		return *x.CardTitle
	}
	return ""
}

func (x *Card) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

func (x *Card) GetIconWidth() int32 {
	if x != nil && x.IconWidth != nil {
		return *x.IconWidth
	}
	return 0
}

func (x *Card) GetIconHeight() int32 {
	if x != nil && x.IconHeight != nil {
		return *x.IconHeight
	}
	return 0
}

func (x *Card) GetAdxCardType() Card_AdxCardType {
	if x != nil && x.AdxCardType != nil {
		return *x.AdxCardType
	}
	return Card_UN_CARD
}

type Category struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstCategoryNum   *int64  `protobuf:"varint,1,opt,name=first_category_num,json=firstCategoryNum" json:"first_category_num,omitempty"`
	FirstCategoryName  *string `protobuf:"bytes,2,opt,name=first_category_name,json=firstCategoryName" json:"first_category_name,omitempty"`
	SecondCategoryNum  *int64  `protobuf:"varint,3,opt,name=second_category_num,json=secondCategoryNum" json:"second_category_num,omitempty"`
	SecondCategoryName *string `protobuf:"bytes,4,opt,name=second_category_name,json=secondCategoryName" json:"second_category_name,omitempty"`
	ThirdCategoryNum   *int64  `protobuf:"varint,5,opt,name=third_category_num,json=thirdCategoryNum" json:"third_category_num,omitempty"`
	ThirdCategoryName  *string `protobuf:"bytes,6,opt,name=third_category_name,json=thirdCategoryName" json:"third_category_name,omitempty"`
}

func (x *Category) Reset() {
	*x = Category{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{21}
}

func (x *Category) GetFirstCategoryNum() int64 {
	if x != nil && x.FirstCategoryNum != nil {
		return *x.FirstCategoryNum
	}
	return 0
}

func (x *Category) GetFirstCategoryName() string {
	if x != nil && x.FirstCategoryName != nil {
		return *x.FirstCategoryName
	}
	return ""
}

func (x *Category) GetSecondCategoryNum() int64 {
	if x != nil && x.SecondCategoryNum != nil {
		return *x.SecondCategoryNum
	}
	return 0
}

func (x *Category) GetSecondCategoryName() string {
	if x != nil && x.SecondCategoryName != nil {
		return *x.SecondCategoryName
	}
	return ""
}

func (x *Category) GetThirdCategoryNum() int64 {
	if x != nil && x.ThirdCategoryNum != nil {
		return *x.ThirdCategoryNum
	}
	return 0
}

func (x *Category) GetThirdCategoryName() string {
	if x != nil && x.ThirdCategoryName != nil {
		return *x.ThirdCategoryName
	}
	return ""
}

type Ocpx struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdvertiseType *int32 `protobuf:"varint,1,opt,name=advertise_type,json=advertiseType" json:"advertise_type,omitempty"`
	NeedOcpx      *bool  `protobuf:"varint,2,opt,name=need_ocpx,json=needOcpx" json:"need_ocpx,omitempty"`
}

func (x *Ocpx) Reset() {
	*x = Ocpx{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ocpx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ocpx) ProtoMessage() {}

func (x *Ocpx) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ocpx.ProtoReflect.Descriptor instead.
func (*Ocpx) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{22}
}

func (x *Ocpx) GetAdvertiseType() int32 {
	if x != nil && x.AdvertiseType != nil {
		return *x.AdvertiseType
	}
	return 0
}

func (x *Ocpx) GetNeedOcpx() bool {
	if x != nil && x.NeedOcpx != nil {
		return *x.NeedOcpx
	}
	return false
}

type Atlas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl []string `protobuf:"bytes,1,rep,name=image_url,json=imageUrl" json:"image_url,omitempty"` //图片url集合
	CoverUrl *string  `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl" json:"cover_url,omitempty"` //封面
	MusicUrl *string  `protobuf:"bytes,3,opt,name=music_url,json=musicUrl" json:"music_url,omitempty"` //背景音乐
	Caption  *string  `protobuf:"bytes,4,opt,name=caption" json:"caption,omitempty"`                   //标题
}

func (x *Atlas) Reset() {
	*x = Atlas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Atlas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Atlas) ProtoMessage() {}

func (x *Atlas) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Atlas.ProtoReflect.Descriptor instead.
func (*Atlas) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{23}
}

func (x *Atlas) GetImageUrl() []string {
	if x != nil {
		return x.ImageUrl
	}
	return nil
}

func (x *Atlas) GetCoverUrl() string {
	if x != nil && x.CoverUrl != nil {
		return *x.CoverUrl
	}
	return ""
}

func (x *Atlas) GetMusicUrl() string {
	if x != nil && x.MusicUrl != nil {
		return *x.MusicUrl
	}
	return ""
}

func (x *Atlas) GetCaption() string {
	if x != nil && x.Caption != nil {
		return *x.Caption
	}
	return ""
}

type Adm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeType         *Adm_CreativeType    `protobuf:"varint,1,opt,name=creative_type,json=creativeType,enum=kuaishou.ad.adx.Adm_CreativeType" json:"creative_type,omitempty"`             //创意类型
	InteractionType      *Adm_InteractionType `protobuf:"varint,2,opt,name=interaction_type,json=interactionType,enum=kuaishou.ad.adx.Adm_InteractionType" json:"interaction_type,omitempty"` //交互类型
	Title                *string              `protobuf:"bytes,4,opt,name=title" json:"title,omitempty"`
	Desc                 *string              `protobuf:"bytes,5,opt,name=desc" json:"desc,omitempty"`
	ClickUrl             *string              `protobuf:"bytes,6,opt,name=click_url,json=clickUrl" json:"click_url,omitempty"`                 //点击跳转h5页面地址
	VideoUrl             *string              `protobuf:"bytes,7,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`                 //广告视频物料地址
	PhotoId              *uint64              `protobuf:"varint,8,opt,name=photo_id,json=photoId" json:"photo_id,omitempty"`                   //视频photo_id，这个只针对在快手app上存在的视频，video_url和photo_id必须至少存在一个
	VideoDuration        *uint32              `protobuf:"varint,9,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"` //视频持续时间，视频广告必填
	DeeplinkUrl          *string              `protobuf:"bytes,10,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`       //app唤醒地址
	CoverUrl             *string              `protobuf:"bytes,11,opt,name=cover_url,json=coverUrl" json:"cover_url,omitempty"`                //视频封面地址
	Atlas                *Atlas               `protobuf:"bytes,12,opt,name=atlas" json:"atlas,omitempty"`
	PackageName          *string              `protobuf:"bytes,13,opt,name=package_name,json=packageName" json:"package_name,omitempty"`                            //安卓安装包包名
	PackageSize          *uint32              `protobuf:"varint,14,opt,name=package_size,json=packageSize" json:"package_size,omitempty"`                           //安装包大小
	AppName              *string              `protobuf:"bytes,15,opt,name=app_name,json=appName" json:"app_name,omitempty"`                                        //应用名称
	IconUrl              *string              `protobuf:"bytes,16,opt,name=icon_url,json=iconUrl" json:"icon_url,omitempty"`                                        //应用icon url
	BundleId             *string              `protobuf:"bytes,17,opt,name=bundle_id,json=bundleId" json:"bundle_id,omitempty"`                                     //苹果应用bundleID
	AdType               *AdTypeEnum_AdType   `protobuf:"varint,18,opt,name=ad_type,json=adType,enum=kuaishou.ad.adx.AdTypeEnum_AdType" json:"ad_type,omitempty"`   //返回广告类型
	ImageUrl             *string              `protobuf:"bytes,19,opt,name=image_url,json=imageUrl" json:"image_url,omitempty"`                                     //图文广告的图片url,已弃用
	DateTimestamp        *uint64              `protobuf:"varint,20,opt,name=date_timestamp,json=dateTimestamp" json:"date_timestamp,omitempty"`                     //如果是开屏广告，表示当前广告的投放日期，单位:日期，如值1567267200表示 2019年9月1号
	PicUrls              []string             `protobuf:"bytes,21,rep,name=pic_urls,json=picUrls" json:"pic_urls,omitempty"`                                        //图文广告图片列表,适用于开屏图文/单图/多图等广告类型
	TargetType           *uint32              `protobuf:"varint,22,opt,name=target_type,json=targetType" json:"target_type,omitempty"`                              //广告的应用安装定向类型，仅对安卓下载类和dp类广告生效；0-不限；1-定向已安装；2-定向未安装
	ActivityCardUrl      *string              `protobuf:"bytes,23,opt,name=activity_card_url,json=activityCardUrl" json:"activity_card_url,omitempty"`              //活动卡片地址
	MarketDirect         *bool                `protobuf:"varint,24,opt,name=market_direct,json=marketDirect" json:"market_direct,omitempty"`                        //广告商是否支持应用直投
	AppVersion           *string              `protobuf:"bytes,25,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`                               //下载类app应用版本
	DeveloperName        *string              `protobuf:"bytes,26,opt,name=developer_name,json=developerName" json:"developer_name,omitempty"`                      //app开发者（公司）名称
	AppPrivacyUrl        *string              `protobuf:"bytes,27,opt,name=app_privacy_url,json=appPrivacyUrl" json:"app_privacy_url,omitempty"`                    //隐私政策url
	AppPermission        *string              `protobuf:"bytes,28,opt,name=app_permission,json=appPermission" json:"app_permission,omitempty"`                      //应用权限url
	PackageSizeV2        *uint64              `protobuf:"varint,29,opt,name=package_size_v2,json=packageSizeV2" json:"package_size_v2,omitempty"`                   //安装包大小, long类
	FunctionIntroduction *string              `protobuf:"bytes,30,opt,name=function_introduction,json=functionIntroduction" json:"function_introduction,omitempty"` //功能介绍
}

func (x *Adm) Reset() {
	*x = Adm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adm) ProtoMessage() {}

func (x *Adm) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adm.ProtoReflect.Descriptor instead.
func (*Adm) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{24}
}

func (x *Adm) GetCreativeType() Adm_CreativeType {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return Adm_NO_TYPE
}

func (x *Adm) GetInteractionType() Adm_InteractionType {
	if x != nil && x.InteractionType != nil {
		return *x.InteractionType
	}
	return Adm_NO_INTERACTION
}

func (x *Adm) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Adm) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *Adm) GetClickUrl() string {
	if x != nil && x.ClickUrl != nil {
		return *x.ClickUrl
	}
	return ""
}

func (x *Adm) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

func (x *Adm) GetPhotoId() uint64 {
	if x != nil && x.PhotoId != nil {
		return *x.PhotoId
	}
	return 0
}

func (x *Adm) GetVideoDuration() uint32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *Adm) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *Adm) GetCoverUrl() string {
	if x != nil && x.CoverUrl != nil {
		return *x.CoverUrl
	}
	return ""
}

func (x *Adm) GetAtlas() *Atlas {
	if x != nil {
		return x.Atlas
	}
	return nil
}

func (x *Adm) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *Adm) GetPackageSize() uint32 {
	if x != nil && x.PackageSize != nil {
		return *x.PackageSize
	}
	return 0
}

func (x *Adm) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *Adm) GetIconUrl() string {
	if x != nil && x.IconUrl != nil {
		return *x.IconUrl
	}
	return ""
}

func (x *Adm) GetBundleId() string {
	if x != nil && x.BundleId != nil {
		return *x.BundleId
	}
	return ""
}

func (x *Adm) GetAdType() AdTypeEnum_AdType {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return AdTypeEnum_NO_AD_TYPE
}

func (x *Adm) GetImageUrl() string {
	if x != nil && x.ImageUrl != nil {
		return *x.ImageUrl
	}
	return ""
}

func (x *Adm) GetDateTimestamp() uint64 {
	if x != nil && x.DateTimestamp != nil {
		return *x.DateTimestamp
	}
	return 0
}

func (x *Adm) GetPicUrls() []string {
	if x != nil {
		return x.PicUrls
	}
	return nil
}

func (x *Adm) GetTargetType() uint32 {
	if x != nil && x.TargetType != nil {
		return *x.TargetType
	}
	return 0
}

func (x *Adm) GetActivityCardUrl() string {
	if x != nil && x.ActivityCardUrl != nil {
		return *x.ActivityCardUrl
	}
	return ""
}

func (x *Adm) GetMarketDirect() bool {
	if x != nil && x.MarketDirect != nil {
		return *x.MarketDirect
	}
	return false
}

func (x *Adm) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *Adm) GetDeveloperName() string {
	if x != nil && x.DeveloperName != nil {
		return *x.DeveloperName
	}
	return ""
}

func (x *Adm) GetAppPrivacyUrl() string {
	if x != nil && x.AppPrivacyUrl != nil {
		return *x.AppPrivacyUrl
	}
	return ""
}

func (x *Adm) GetAppPermission() string {
	if x != nil && x.AppPermission != nil {
		return *x.AppPermission
	}
	return ""
}

func (x *Adm) GetPackageSizeV2() uint64 {
	if x != nil && x.PackageSizeV2 != nil {
		return *x.PackageSizeV2
	}
	return 0
}

func (x *Adm) GetFunctionIntroduction() string {
	if x != nil && x.FunctionIntroduction != nil {
		return *x.FunctionIntroduction
	}
	return ""
}

type Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ipv4           *string                 `protobuf:"bytes,1,opt,name=ipv4" json:"ipv4,omitempty"`                                                                                        // 必填！用户设备的公网IPv4地址，服务器对接必填，格式要求：***************
	ConnectionType *Network_ConnectionType `protobuf:"varint,2,opt,name=connection_type,json=connectionType,enum=kuaishou.ad.adx.Network_ConnectionType" json:"connection_type,omitempty"` // 必填！网络连接类型，用于判断网速
	OperatorType   *Network_OperatorType   `protobuf:"varint,3,opt,name=operator_type,json=operatorType,enum=kuaishou.ad.adx.Network_OperatorType" json:"operator_type,omitempty"`         // 必填！移动运营商类型，用于运营商定向广告
}

func (x *Network) Reset() {
	*x = Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Network) ProtoMessage() {}

func (x *Network) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Network.ProtoReflect.Descriptor instead.
func (*Network) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{25}
}

func (x *Network) GetIpv4() string {
	if x != nil && x.Ipv4 != nil {
		return *x.Ipv4
	}
	return ""
}

func (x *Network) GetConnectionType() Network_ConnectionType {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return Network_CONNECTION_UNKNOWN
}

func (x *Network) GetOperatorType() Network_OperatorType {
	if x != nil && x.OperatorType != nil {
		return *x.OperatorType
	}
	return Network_UNKNOWN_OPERATOR
}

type Feature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`                          // 名称
	Value    *string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`                        // 参数值, 可为空
	IntValue *uint64 `protobuf:"varint,3,opt,name=int_value,json=intValue" json:"int_value,omitempty"` // 参数值, 可为空
}

func (x *Feature) Reset() {
	*x = Feature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feature) ProtoMessage() {}

func (x *Feature) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feature.ProtoReflect.Descriptor instead.
func (*Feature) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{26}
}

func (x *Feature) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Feature) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *Feature) GetIntValue() uint64 {
	if x != nil && x.IntValue != nil {
		return *x.IntValue
	}
	return 0
}

type Preview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreviewCreativeId *string `protobuf:"bytes,1,opt,name=preview_creative_id,json=previewCreativeId" json:"preview_creative_id,omitempty"` // 体验的创意id
}

func (x *Preview) Reset() {
	*x = Preview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Preview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Preview) ProtoMessage() {}

func (x *Preview) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Preview.ProtoReflect.Descriptor instead.
func (*Preview) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{27}
}

func (x *Preview) GetPreviewCreativeId() string {
	if x != nil && x.PreviewCreativeId != nil {
		return *x.PreviewCreativeId
	}
	return ""
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId *string    `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"` //必填！bid request 唯一id,由交易平台生成
	Imp       []*Imp     `protobuf:"bytes,2,rep,name=imp" json:"imp,omitempty"`                              //必填！广告位信息
	App       *App       `protobuf:"bytes,3,opt,name=app" json:"app,omitempty"`
	Device    *Device    `protobuf:"bytes,4,opt,name=device" json:"device,omitempty"`
	User      *User      `protobuf:"bytes,5,opt,name=user" json:"user,omitempty"`
	Network   *Network   `protobuf:"bytes,6,opt,name=network" json:"network,omitempty"`
	Debug     *bool      `protobuf:"varint,7,opt,name=debug,def=0" json:"debug,omitempty"` //false=生产者模式，true=测试模式
	At        *uint32    `protobuf:"varint,8,opt,name=at,def=2" json:"at,omitempty"`       //竞价类型,1=first price;2=second price plus;交易平台自定义的竞价类型，需大于500
	Timeout   *uint32    `protobuf:"varint,9,opt,name=timeout,def=200" json:"timeout,omitempty"`
	BlackCat  []string   `protobuf:"bytes,10,rep,name=black_cat,json=blackCat" json:"black_cat,omitempty"` //广告行业黑名单
	BlackAdv  []string   `protobuf:"bytes,11,rep,name=black_adv,json=blackAdv" json:"black_adv,omitempty"` //广告主黑名单
	Feature   []*Feature `protobuf:"bytes,12,rep,name=feature" json:"feature,omitempty"`                   // 特性开关, 用于小流量实验
	Preview   *Preview   `protobuf:"bytes,13,opt,name=preview" json:"preview,omitempty"`                   // 体验信息
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_Debug   = bool(false)
	Default_BidRequest_At      = uint32(2)
	Default_BidRequest_Timeout = uint32(200)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{28}
}

func (x *BidRequest) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidRequest) GetImp() []*Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetNetwork() *Network {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *BidRequest) GetDebug() bool {
	if x != nil && x.Debug != nil {
		return *x.Debug
	}
	return Default_BidRequest_Debug
}

func (x *BidRequest) GetAt() uint32 {
	if x != nil && x.At != nil {
		return *x.At
	}
	return Default_BidRequest_At
}

func (x *BidRequest) GetTimeout() uint32 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return Default_BidRequest_Timeout
}

func (x *BidRequest) GetBlackCat() []string {
	if x != nil {
		return x.BlackCat
	}
	return nil
}

func (x *BidRequest) GetBlackAdv() []string {
	if x != nil {
		return x.BlackAdv
	}
	return nil
}

func (x *BidRequest) GetFeature() []*Feature {
	if x != nil {
		return x.Feature
	}
	return nil
}

func (x *BidRequest) GetPreview() *Preview {
	if x != nil {
		return x.Preview
	}
	return nil
}

type DpaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token  *string `protobuf:"bytes,1,opt,name=token" json:"token,omitempty"`
	Extend *string `protobuf:"bytes,2,opt,name=extend" json:"extend,omitempty"`
}

func (x *DpaInfo) Reset() {
	*x = DpaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DpaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DpaInfo) ProtoMessage() {}

func (x *DpaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DpaInfo.ProtoReflect.Descriptor instead.
func (*DpaInfo) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{29}
}

func (x *DpaInfo) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

func (x *DpaInfo) GetExtend() string {
	if x != nil && x.Extend != nil {
		return *x.Extend
	}
	return ""
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId   *string                  `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id,omitempty"` //必填，对应BidRequest中定义的request_id
	SeatBid     []*SeatBid               `protobuf:"bytes,2,rep,name=seat_bid,json=seatBid" json:"seat_bid,omitempty"`
	Status      *uint32                  `protobuf:"varint,3,opt,name=status" json:"status,omitempty"`           //0-ok，其他表示没有广告返回
	BidId       *string                  `protobuf:"bytes,4,opt,name=bid_id,json=bidId" json:"bid_id,omitempty"` //响应id
	NoBidReason *BidResponse_NoBidReason `protobuf:"varint,5,opt,name=noBidReason,enum=kuaishou.ad.adx.BidResponse_NoBidReason" json:"noBidReason,omitempty"`
	CustomData  *string                  `protobuf:"bytes,6,opt,name=custom_data,json=customData" json:"custom_data,omitempty"`  // dsp返回的usertag等信息
	ExtData     *string                  `protobuf:"bytes,7,opt,name=ext_data,json=extData" json:"ext_data,omitempty"`           // dsp返回的扩展字段，如自定义宏等
	Deal        *Deal                    `protobuf:"bytes,8,opt,name=deal" json:"deal,omitempty"`                                //返回对应的deal. 已废弃
	IsInterest  *bool                    `protobuf:"varint,9,opt,name=is_interest,json=isInterest" json:"is_interest,omitempty"` //是否参与dpa
	DpaInfo     *DpaInfo                 `protobuf:"bytes,10,opt,name=dpa_info,json=dpaInfo" json:"dpa_info,omitempty"`          //dpa相关附加信息
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{30}
}

func (x *BidResponse) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidResponse) GetSeatBid() []*SeatBid {
	if x != nil {
		return x.SeatBid
	}
	return nil
}

func (x *BidResponse) GetStatus() uint32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *BidResponse) GetBidId() string {
	if x != nil && x.BidId != nil {
		return *x.BidId
	}
	return ""
}

func (x *BidResponse) GetNoBidReason() BidResponse_NoBidReason {
	if x != nil && x.NoBidReason != nil {
		return *x.NoBidReason
	}
	return BidResponse_UNKNOWN
}

func (x *BidResponse) GetCustomData() string {
	if x != nil && x.CustomData != nil {
		return *x.CustomData
	}
	return ""
}

func (x *BidResponse) GetExtData() string {
	if x != nil && x.ExtData != nil {
		return *x.ExtData
	}
	return ""
}

func (x *BidResponse) GetDeal() *Deal {
	if x != nil {
		return x.Deal
	}
	return nil
}

func (x *BidResponse) GetIsInterest() bool {
	if x != nil && x.IsInterest != nil {
		return *x.IsInterest
	}
	return false
}

func (x *BidResponse) GetDpaInfo() *DpaInfo {
	if x != nil {
		return x.DpaInfo
	}
	return nil
}

type UrlDeliveryBidResponseWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Response *BidResponse `protobuf:"bytes,1,req,name=response" json:"response,omitempty"`
	UrlId    *uint64      `protobuf:"varint,2,req,name=url_id,json=urlId" json:"url_id,omitempty"`
}

func (x *UrlDeliveryBidResponseWrapper) Reset() {
	*x = UrlDeliveryBidResponseWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UrlDeliveryBidResponseWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UrlDeliveryBidResponseWrapper) ProtoMessage() {}

func (x *UrlDeliveryBidResponseWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UrlDeliveryBidResponseWrapper.ProtoReflect.Descriptor instead.
func (*UrlDeliveryBidResponseWrapper) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{31}
}

func (x *UrlDeliveryBidResponseWrapper) GetResponse() *BidResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *UrlDeliveryBidResponseWrapper) GetUrlId() uint64 {
	if x != nil && x.UrlId != nil {
		return *x.UrlId
	}
	return 0
}

type AdTypeEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AdTypeEnum) Reset() {
	*x = AdTypeEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kuaishou_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdTypeEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdTypeEnum) ProtoMessage() {}

func (x *AdTypeEnum) ProtoReflect() protoreflect.Message {
	mi := &file_kuaishou_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdTypeEnum.ProtoReflect.Descriptor instead.
func (*AdTypeEnum) Descriptor() ([]byte, []int) {
	return file_kuaishou_proto_rawDescGZIP(), []int{32}
}

var File_kuaishou_proto protoreflect.FileDescriptor

var file_kuaishou_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0f, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x22, 0x34, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x4b, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x22, 0xb2, 0x06, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x15, 0x0a, 0x06,
	0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x75, 0x61,
	0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x2c, 0x0a, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x17, 0x0a, 0x05, 0x69, 0x6e,
	0x73, 0x74, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x05, 0x69, 0x6e,
	0x73, 0x74, 0x6c, 0x12, 0x1e, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x01, 0x30, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x61,
	0x64, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e,
	0x49, 0x6d, 0x70, 0x2e, 0x41, 0x64, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x64, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x26, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x12, 0x25, 0x0a, 0x0d, 0x63, 0x70, 0x6d,
	0x5f, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01,
	0x3a, 0x01, 0x30, 0x52, 0x0b, 0x63, 0x70, 0x6d, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x3b, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e,
	0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64,
	0x6d, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x64, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x61, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x6f, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x6f, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x27, 0x0a, 0x07, 0x41, 0x64, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x55,
	0x4e, 0x4b, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x48, 0x35, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x02, 0x22, 0x59, 0x0a, 0x03, 0x50, 0x6d, 0x70,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x65, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68,
	0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x04,
	0x64, 0x65, 0x61, 0x6c, 0x22, 0xa3, 0x01, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x5f, 0x63, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x46,
	0x6c, 0x6f, 0x6f, 0x72, 0x43, 0x75, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x61, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x77, 0x73, 0x65, 0x61, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x77, 0x61, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x77, 0x61, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x62, 0x0a, 0x06, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x29, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x22, 0xe5,
	0x01, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b,
	0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x70, 0x69, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x70,
	0x69, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x7a, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x73, 0x69, 0x7a,
	0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x93, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x69,
	0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78,
	0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x29, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f,
	0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x05, 0x62, 0x61, 0x74, 0x74, 0x72, 0x12, 0x32, 0x0a, 0x11, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x10, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x22, 0xb4, 0x02, 0x0a,
	0x03, 0x41, 0x70, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x32,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x38, 0x0a,
	0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x52, 0x09, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x22, 0x6c, 0x0a, 0x09, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x22, 0xe0, 0x03, 0x0a, 0x04, 0x55, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d,
	0x65, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x61, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x69, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x63, 0x61, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74,
	0x43, 0x61, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x69,
	0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x31, 0x5f, 0x35, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x69, 0x64, 0x31,
	0x35, 0x12, 0x21, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x31,
	0x5f, 0x35, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x61,
	0x69, 0x64, 0x31, 0x35, 0x22, 0xd6, 0x06, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65,
	0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x43, 0x0a, 0x0b, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x37, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b,
	0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x36, 0x0a, 0x0b, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e,
	0x61, 0x64, 0x78, 0x2e, 0x55, 0x64, 0x69, 0x64, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x75, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b,
	0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x6f, 0x75,
	0x74, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x61, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x72, 0x55, 0x61, 0x12,
	0x26, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x41, 0x70, 0x70, 0x4d, 0x64, 0x35, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x3c, 0x0a, 0x0a, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x22, 0x33, 0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f,
	0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x22, 0xa2, 0x02,
	0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68,
	0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65, 0x6f, 0x2e, 0x47, 0x65,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x19, 0x0a,
	0x08, 0x67, 0x65, 0x6f, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x67, 0x65, 0x6f, 0x48, 0x61, 0x73, 0x68, 0x22, 0x48, 0x0a, 0x07, 0x47, 0x65, 0x6f, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x47, 0x50, 0x53, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x50, 0x10,
	0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49,
	0x45, 0x52, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x47, 0x45, 0x4f, 0x5f, 0x48, 0x41, 0x53, 0x48,
	0x10, 0x04, 0x22, 0x52, 0x0a, 0x07, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x67, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17,
	0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b,
	0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x45, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x62, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x65, 0x6d, 0x62, 0x5f, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x62, 0x41, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x65, 0x6d, 0x62, 0x5f, 0x62, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x45, 0x6d, 0x62, 0x42, 0x22, 0x90, 0x03, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x75, 0x79, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61,
	0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f,
	0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75, 0x61,
	0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x45, 0x6d, 0x62, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x22, 0x24, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x05, 0x0a,
	0x01, 0x55, 0x10, 0x00, 0x12, 0x05, 0x0a, 0x01, 0x4d, 0x10, 0x01, 0x12, 0x05, 0x0a, 0x01, 0x46,
	0x10, 0x02, 0x12, 0x05, 0x0a, 0x01, 0x4f, 0x10, 0x03, 0x22, 0xc8, 0x04, 0x0a, 0x08, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x4e, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x22, 0xc8, 0x03, 0x0a, 0x0d, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x08, 0x41,
	0x44, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x44, 0x5f,
	0x45, 0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44,
	0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x0e, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x88, 0x95, 0x06, 0x12, 0x1a,
	0x0a, 0x14, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x89, 0x95, 0x06, 0x12, 0x12, 0x0a, 0x0c, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x8a, 0x95, 0x06, 0x12, 0x1f,
	0x0a, 0x19, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x8b, 0x95, 0x06, 0x12,
	0x15, 0x0a, 0x0f, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x10, 0xf0, 0x9c, 0x06, 0x12, 0x14, 0x0a, 0x0e, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x10, 0xf1, 0x9c, 0x06, 0x12, 0x13, 0x0a, 0x0d,
	0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0xf2, 0x9c,
	0x06, 0x12, 0x19, 0x0a, 0x13, 0x41, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42,
	0x41, 0x52, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0xd8, 0xa4, 0x06, 0x12, 0x1b, 0x0a, 0x15,
	0x41, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f,
	0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0xc0, 0xac, 0x06, 0x12, 0x18, 0x0a, 0x12, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x44, 0x5f, 0x32, 0x53, 0x10,
	0xa8, 0xb4, 0x06, 0x12, 0x18, 0x0a, 0x12, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x45, 0x44, 0x5f, 0x35, 0x53, 0x10, 0x90, 0xbc, 0x06, 0x12, 0x13, 0x0a,
	0x0d, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x4c, 0x49, 0x4b, 0x45, 0x10, 0xf8,
	0xc3, 0x06, 0x12, 0x14, 0x0a, 0x0e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x53,
	0x48, 0x41, 0x52, 0x45, 0x10, 0xe0, 0xcb, 0x06, 0x12, 0x16, 0x0a, 0x10, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xc8, 0xd3, 0x06,
	0x12, 0x18, 0x0a, 0x12, 0x41, 0x44, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f,
	0x31, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10, 0xb0, 0xdb, 0x06, 0x12, 0x18, 0x0a, 0x12, 0x56, 0x49,
	0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x44, 0x5f, 0x33, 0x53,
	0x10, 0x98, 0xe3, 0x06, 0x22, 0x97, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x0e,
	0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6f,
	0x6b, 0x69, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x22, 0x0a, 0x09, 0x62, 0x69,
	0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x08, 0x62, 0x69, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xdf,
	0x09, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x6d, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x3a,
	0x0a, 0x0b, 0x61, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61,
	0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x0a,
	0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x64,
	0x6d, 0x18, 0x07, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68,
	0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x6d, 0x52, 0x03, 0x61,
	0x64, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x29, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69,
	0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x69, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x64, 0x45, 0x78,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68,
	0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34,
	0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x6f, 0x63, 0x70, 0x78, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4f, 0x63, 0x70, 0x78, 0x52, 0x04, 0x6f, 0x63, 0x70, 0x78, 0x12,
	0x60, 0x0a, 0x17, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x68, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x79, 0x74, 0x6c, 0x65, 0x52, 0x14, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x6e,
	0x69, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x12, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69,
	0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x69, 0x7a, 0x65,
	0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x07, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x43, 0x50, 0x43, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x4d, 0x10, 0x02,
	0x22, 0x3e, 0x0a, 0x14, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x53,
	0x68, 0x6f, 0x77, 0x53, 0x79, 0x74, 0x6c, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x4c, 0x55, 0x45,
	0x5f, 0x42, 0x41, 0x52, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x45, 0x50, 0x4f, 0x53,
	0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x01,
	0x22, 0xe5, 0x02, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x63, 0x6f,
	0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x63, 0x6f,
	0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x45, 0x0a, 0x0d, 0x61, 0x64, 0x78, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x41, 0x64, 0x78, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x64, 0x78, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x28,
	0x0a, 0x0b, 0x41, 0x64, 0x78, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x4e, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x49,
	0x43, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x64, 0x22, 0xa8, 0x02, 0x0a, 0x08, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x11, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x75, 0x6d, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x74, 0x68, 0x69, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x74, 0x68, 0x69, 0x72, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x4a, 0x0a, 0x04, 0x4f, 0x63, 0x70, 0x78, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x6f, 0x63, 0x70, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e, 0x65, 0x65, 0x64, 0x4f, 0x63, 0x70, 0x78, 0x22,
	0x78, 0x0a, 0x05, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55,
	0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x75, 0x73, 0x69, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x75, 0x73, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xca, 0x0a, 0x0a, 0x03, 0x41, 0x64,
	0x6d, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73,
	0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61,
	0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c,
	0x12, 0x2c, 0x0a, 0x05, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x41, 0x74, 0x6c, 0x61, 0x73, 0x52, 0x05, 0x61, 0x74, 0x6c, 0x61, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73,
	0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x69, 0x63, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x63, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x55, 0x72, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x76, 0x32, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x56, 0x32, 0x12, 0x33, 0x0a, 0x15, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb8, 0x01, 0x0a, 0x0c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09,
	0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x54, 0x4c, 0x41, 0x53, 0x10,
	0x05, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x06, 0x12, 0x13,
	0x0a, 0x0f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41,
	0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x45,
	0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x09, 0x12, 0x14,
	0x0a, 0x10, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x49, 0x4d, 0x41,
	0x47, 0x45, 0x10, 0x0a, 0x22, 0x40, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x55, 0x52, 0x46, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e,
	0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02, 0x22, 0xc4, 0x03, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x70, 0x76, 0x34, 0x12, 0x50, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f, 0x4e, 0x4e, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43,
	0x45, 0x4c, 0x4c, 0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c,
	0x5f, 0x35, 0x47, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x64, 0x12,
	0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e, 0x45, 0x54, 0x10, 0x65, 0x12, 0x0d, 0x0a,
	0x08, 0x4e, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xe7, 0x07, 0x22, 0x6f, 0x0a, 0x0c,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x54, 0x45,
	0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41,
	0x5f, 0x55, 0x4e, 0x49, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x63, 0x22, 0x50, 0x0a,
	0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x39, 0x0a, 0x07, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0xfc, 0x03, 0x0a, 0x0a, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75,
	0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70,
	0x12, 0x26, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e,
	0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73,
	0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68,
	0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75,
	0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x05,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x12, 0x11, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x3a, 0x01, 0x32, 0x52, 0x02, 0x61, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x03, 0x32, 0x30, 0x30, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x63, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6c, 0x61, 0x63,
	0x6b, 0x43, 0x61, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x61, 0x64,
	0x76, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x41, 0x64,
	0x76, 0x12, 0x32, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f,
	0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x07, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x22, 0x37, 0x0a, 0x07, 0x44, 0x70, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x22, 0xec, 0x04, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x33, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61,
	0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15,
	0x0a, 0x06, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0b, 0x6e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6b, 0x75, 0x61,
	0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0b, 0x6e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a,
	0x04, 0x64, 0x65, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x44, 0x65,
	0x61, 0x6c, 0x52, 0x04, 0x64, 0x65, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x64, 0x70, 0x61,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x75,
	0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x44, 0x70,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x70, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd0,
	0x01, 0x0a, 0x0b, 0x4e, 0x6f, 0x42, 0x69, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x0b,
	0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54,
	0x45, 0x43, 0x48, 0x4e, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x50, 0x49,
	0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x4e, 0x4f, 0x4e, 0x5f, 0x48, 0x55, 0x4d,
	0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x46, 0x46, 0x49, 0x43, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a,
	0x49, 0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x49, 0x50, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12,
	0x55, 0x4e, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49,
	0x43, 0x45, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f,
	0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x52, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x42,
	0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x54, 0x45, 0x10, 0x08, 0x12, 0x12, 0x0a,
	0x0e, 0x55, 0x4e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10,
	0x09, 0x22, 0x70, 0x0a, 0x1d, 0x55, 0x72, 0x6c, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x02, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e,
	0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x75, 0x72, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x75, 0x72,
	0x6c, 0x49, 0x64, 0x22, 0xd3, 0x03, 0x0a, 0x0a, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x22, 0xc4, 0x03, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a,
	0x0a, 0x4e, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x1f, 0x0a,
	0x1b, 0x4b, 0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52,
	0x45, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x01, 0x12, 0x2e,
	0x0a, 0x2a, 0x4b, 0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45,
	0x4e, 0x54, 0x53, 0x5f, 0x50, 0x49, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x02, 0x12, 0x2b,
	0x0a, 0x27, 0x4b, 0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x42, 0x45, 0x54, 0x57, 0x45, 0x45, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45,
	0x4e, 0x54, 0x53, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x4b,
	0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x49, 0x43, 0x5f, 0x54, 0x45, 0x58,
	0x54, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x4b, 0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x44,
	0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x4b, 0x55, 0x41, 0x49,
	0x53, 0x48, 0x4f, 0x55, 0x5f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x53, 0x4c, 0x4f, 0x54,
	0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x4b, 0x55, 0x41, 0x49,
	0x53, 0x48, 0x4f, 0x55, 0x5f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x53, 0x4c, 0x4f, 0x54,
	0x5f, 0x50, 0x49, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x4b,
	0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x54,
	0x4f, 0x50, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x56, 0x49, 0x44, 0x45,
	0x4f, 0x10, 0x08, 0x12, 0x29, 0x0a, 0x25, 0x4b, 0x55, 0x41, 0x49, 0x53, 0x48, 0x4f, 0x55, 0x5f,
	0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x50, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53,
	0x4c, 0x4f, 0x54, 0x5f, 0x50, 0x49, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x09, 0x12, 0x17,
	0x0a, 0x13, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x50, 0x49, 0x43,
	0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x45, 0x44, 0x5f,
	0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x49, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0b,
	0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x45, 0x44, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x50,
	0x49, 0x43, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0c, 0x42, 0x3e, 0x0a, 0x20, 0x63, 0x6f, 0x6d,
	0x2e, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x61, 0x64, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x72, 0x74, 0x62, 0x42, 0x0b, 0x41,
	0x64, 0x78, 0x52, 0x54, 0x42, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x0b, 0x2e, 0x2e,
	0x2f, 0x6b, 0x75, 0x61, 0x69, 0x73, 0x68, 0x6f, 0x75,
}

var (
	file_kuaishou_proto_rawDescOnce sync.Once
	file_kuaishou_proto_rawDescData = file_kuaishou_proto_rawDesc
)

func file_kuaishou_proto_rawDescGZIP() []byte {
	file_kuaishou_proto_rawDescOnce.Do(func() {
		file_kuaishou_proto_rawDescData = protoimpl.X.CompressGZIP(file_kuaishou_proto_rawDescData)
	})
	return file_kuaishou_proto_rawDescData
}

var file_kuaishou_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_kuaishou_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_kuaishou_proto_goTypes = []interface{}{
	(Imp_AdmType)(0),                      // 0: kuaishou.ad.adx.Imp.AdmType
	(Device_DeviceType)(0),                // 1: kuaishou.ad.adx.Device.DeviceType
	(Device_OsType)(0),                    // 2: kuaishou.ad.adx.Device.OsType
	(Geo_GeoType)(0),                      // 3: kuaishou.ad.adx.Geo.GeoType
	(User_Gender)(0),                      // 4: kuaishou.ad.adx.User.Gender
	(Tracking_TrackingEvent)(0),           // 5: kuaishou.ad.adx.Tracking.TrackingEvent
	(Bid_BidType)(0),                      // 6: kuaishou.ad.adx.Bid.BidType
	(Bid_LandingPageShowSytle)(0),         // 7: kuaishou.ad.adx.Bid.LandingPageShowSytle
	(Card_AdxCardType)(0),                 // 8: kuaishou.ad.adx.Card.AdxCardType
	(Adm_CreativeType)(0),                 // 9: kuaishou.ad.adx.Adm.CreativeType
	(Adm_InteractionType)(0),              // 10: kuaishou.ad.adx.Adm.InteractionType
	(Network_ConnectionType)(0),           // 11: kuaishou.ad.adx.Network.ConnectionType
	(Network_OperatorType)(0),             // 12: kuaishou.ad.adx.Network.OperatorType
	(BidResponse_NoBidReason)(0),          // 13: kuaishou.ad.adx.BidResponse.NoBidReason
	(AdTypeEnum_AdType)(0),                // 14: kuaishou.ad.adx.AdTypeEnum.AdType
	(*Size)(nil),                          // 15: kuaishou.ad.adx.Size
	(*Version)(nil),                       // 16: kuaishou.ad.adx.Version
	(*Imp)(nil),                           // 17: kuaishou.ad.adx.Imp
	(*Pmp)(nil),                           // 18: kuaishou.ad.adx.Pmp
	(*Deal)(nil),                          // 19: kuaishou.ad.adx.Deal
	(*Banner)(nil),                        // 20: kuaishou.ad.adx.Banner
	(*Native)(nil),                        // 21: kuaishou.ad.adx.Native
	(*Video)(nil),                         // 22: kuaishou.ad.adx.Video
	(*App)(nil),                           // 23: kuaishou.ad.adx.App
	(*Publisher)(nil),                     // 24: kuaishou.ad.adx.Publisher
	(*Udid)(nil),                          // 25: kuaishou.ad.adx.Udid
	(*Device)(nil),                        // 26: kuaishou.ad.adx.Device
	(*Geo)(nil),                           // 27: kuaishou.ad.adx.Geo
	(*Segment)(nil),                       // 28: kuaishou.ad.adx.Segment
	(*Data)(nil),                          // 29: kuaishou.ad.adx.Data
	(*UserEmb)(nil),                       // 30: kuaishou.ad.adx.UserEmb
	(*User)(nil),                          // 31: kuaishou.ad.adx.User
	(*Tracking)(nil),                      // 32: kuaishou.ad.adx.Tracking
	(*SeatBid)(nil),                       // 33: kuaishou.ad.adx.SeatBid
	(*Bid)(nil),                           // 34: kuaishou.ad.adx.Bid
	(*Card)(nil),                          // 35: kuaishou.ad.adx.Card
	(*Category)(nil),                      // 36: kuaishou.ad.adx.Category
	(*Ocpx)(nil),                          // 37: kuaishou.ad.adx.Ocpx
	(*Atlas)(nil),                         // 38: kuaishou.ad.adx.Atlas
	(*Adm)(nil),                           // 39: kuaishou.ad.adx.Adm
	(*Network)(nil),                       // 40: kuaishou.ad.adx.Network
	(*Feature)(nil),                       // 41: kuaishou.ad.adx.Feature
	(*Preview)(nil),                       // 42: kuaishou.ad.adx.Preview
	(*BidRequest)(nil),                    // 43: kuaishou.ad.adx.BidRequest
	(*DpaInfo)(nil),                       // 44: kuaishou.ad.adx.DpaInfo
	(*BidResponse)(nil),                   // 45: kuaishou.ad.adx.BidResponse
	(*UrlDeliveryBidResponseWrapper)(nil), // 46: kuaishou.ad.adx.UrlDeliveryBidResponseWrapper
	(*AdTypeEnum)(nil),                    // 47: kuaishou.ad.adx.AdTypeEnum
}
var file_kuaishou_proto_depIdxs = []int32{
	20, // 0: kuaishou.ad.adx.Imp.banner:type_name -> kuaishou.ad.adx.Banner
	21, // 1: kuaishou.ad.adx.Imp.native:type_name -> kuaishou.ad.adx.Native
	22, // 2: kuaishou.ad.adx.Imp.video:type_name -> kuaishou.ad.adx.Video
	0,  // 3: kuaishou.ad.adx.Imp.adm_type:type_name -> kuaishou.ad.adx.Imp.AdmType
	18, // 4: kuaishou.ad.adx.Imp.pmp:type_name -> kuaishou.ad.adx.Pmp
	14, // 5: kuaishou.ad.adx.Imp.ad_type:type_name -> kuaishou.ad.adx.AdTypeEnum.AdType
	9,  // 6: kuaishou.ad.adx.Imp.creative_type:type_name -> kuaishou.ad.adx.Adm.CreativeType
	19, // 7: kuaishou.ad.adx.Pmp.deal:type_name -> kuaishou.ad.adx.Deal
	15, // 8: kuaishou.ad.adx.Banner.size:type_name -> kuaishou.ad.adx.Size
	15, // 9: kuaishou.ad.adx.Native.size:type_name -> kuaishou.ad.adx.Size
	16, // 10: kuaishou.ad.adx.Native.version:type_name -> kuaishou.ad.adx.Version
	15, // 11: kuaishou.ad.adx.Video.size:type_name -> kuaishou.ad.adx.Size
	16, // 12: kuaishou.ad.adx.App.version:type_name -> kuaishou.ad.adx.Version
	24, // 13: kuaishou.ad.adx.App.publisher:type_name -> kuaishou.ad.adx.Publisher
	27, // 14: kuaishou.ad.adx.Device.geo:type_name -> kuaishou.ad.adx.Geo
	1,  // 15: kuaishou.ad.adx.Device.device_type:type_name -> kuaishou.ad.adx.Device.DeviceType
	2,  // 16: kuaishou.ad.adx.Device.os_type:type_name -> kuaishou.ad.adx.Device.OsType
	16, // 17: kuaishou.ad.adx.Device.os_version:type_name -> kuaishou.ad.adx.Version
	15, // 18: kuaishou.ad.adx.Device.screen_size:type_name -> kuaishou.ad.adx.Size
	25, // 19: kuaishou.ad.adx.Device.udid:type_name -> kuaishou.ad.adx.Udid
	3,  // 20: kuaishou.ad.adx.Geo.type:type_name -> kuaishou.ad.adx.Geo.GeoType
	28, // 21: kuaishou.ad.adx.Data.segment:type_name -> kuaishou.ad.adx.Segment
	4,  // 22: kuaishou.ad.adx.User.gender:type_name -> kuaishou.ad.adx.User.Gender
	29, // 23: kuaishou.ad.adx.User.data:type_name -> kuaishou.ad.adx.Data
	30, // 24: kuaishou.ad.adx.User.user_embedding:type_name -> kuaishou.ad.adx.UserEmb
	5,  // 25: kuaishou.ad.adx.Tracking.tracking_event:type_name -> kuaishou.ad.adx.Tracking.TrackingEvent
	34, // 26: kuaishou.ad.adx.SeatBid.bid:type_name -> kuaishou.ad.adx.Bid
	32, // 27: kuaishou.ad.adx.Bid.ad_tracking:type_name -> kuaishou.ad.adx.Tracking
	39, // 28: kuaishou.ad.adx.Bid.adm:type_name -> kuaishou.ad.adx.Adm
	15, // 29: kuaishou.ad.adx.Bid.size:type_name -> kuaishou.ad.adx.Size
	6,  // 30: kuaishou.ad.adx.Bid.bid_type:type_name -> kuaishou.ad.adx.Bid.BidType
	15, // 31: kuaishou.ad.adx.Bid.video_size:type_name -> kuaishou.ad.adx.Size
	37, // 32: kuaishou.ad.adx.Bid.ocpx:type_name -> kuaishou.ad.adx.Ocpx
	7,  // 33: kuaishou.ad.adx.Bid.landing_page_show_style:type_name -> kuaishou.ad.adx.Bid.LandingPageShowSytle
	36, // 34: kuaishou.ad.adx.Bid.category:type_name -> kuaishou.ad.adx.Category
	15, // 35: kuaishou.ad.adx.Bid.activity_card_size:type_name -> kuaishou.ad.adx.Size
	35, // 36: kuaishou.ad.adx.Bid.card:type_name -> kuaishou.ad.adx.Card
	8,  // 37: kuaishou.ad.adx.Card.adx_card_type:type_name -> kuaishou.ad.adx.Card.AdxCardType
	9,  // 38: kuaishou.ad.adx.Adm.creative_type:type_name -> kuaishou.ad.adx.Adm.CreativeType
	10, // 39: kuaishou.ad.adx.Adm.interaction_type:type_name -> kuaishou.ad.adx.Adm.InteractionType
	38, // 40: kuaishou.ad.adx.Adm.atlas:type_name -> kuaishou.ad.adx.Atlas
	14, // 41: kuaishou.ad.adx.Adm.ad_type:type_name -> kuaishou.ad.adx.AdTypeEnum.AdType
	11, // 42: kuaishou.ad.adx.Network.connection_type:type_name -> kuaishou.ad.adx.Network.ConnectionType
	12, // 43: kuaishou.ad.adx.Network.operator_type:type_name -> kuaishou.ad.adx.Network.OperatorType
	17, // 44: kuaishou.ad.adx.BidRequest.imp:type_name -> kuaishou.ad.adx.Imp
	23, // 45: kuaishou.ad.adx.BidRequest.app:type_name -> kuaishou.ad.adx.App
	26, // 46: kuaishou.ad.adx.BidRequest.device:type_name -> kuaishou.ad.adx.Device
	31, // 47: kuaishou.ad.adx.BidRequest.user:type_name -> kuaishou.ad.adx.User
	40, // 48: kuaishou.ad.adx.BidRequest.network:type_name -> kuaishou.ad.adx.Network
	41, // 49: kuaishou.ad.adx.BidRequest.feature:type_name -> kuaishou.ad.adx.Feature
	42, // 50: kuaishou.ad.adx.BidRequest.preview:type_name -> kuaishou.ad.adx.Preview
	33, // 51: kuaishou.ad.adx.BidResponse.seat_bid:type_name -> kuaishou.ad.adx.SeatBid
	13, // 52: kuaishou.ad.adx.BidResponse.noBidReason:type_name -> kuaishou.ad.adx.BidResponse.NoBidReason
	19, // 53: kuaishou.ad.adx.BidResponse.deal:type_name -> kuaishou.ad.adx.Deal
	44, // 54: kuaishou.ad.adx.BidResponse.dpa_info:type_name -> kuaishou.ad.adx.DpaInfo
	45, // 55: kuaishou.ad.adx.UrlDeliveryBidResponseWrapper.response:type_name -> kuaishou.ad.adx.BidResponse
	56, // [56:56] is the sub-list for method output_type
	56, // [56:56] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_kuaishou_proto_init() }
func file_kuaishou_proto_init() {
	if File_kuaishou_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_kuaishou_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Version); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Publisher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Udid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Segment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserEmb); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Category); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ocpx); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Atlas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Preview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DpaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UrlDeliveryBidResponseWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_kuaishou_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdTypeEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_kuaishou_proto_rawDesc,
			NumEnums:      15,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_kuaishou_proto_goTypes,
		DependencyIndexes: file_kuaishou_proto_depIdxs,
		EnumInfos:         file_kuaishou_proto_enumTypes,
		MessageInfos:      file_kuaishou_proto_msgTypes,
	}.Build()
	File_kuaishou_proto = out.File
	file_kuaishou_proto_rawDesc = nil
	file_kuaishou_proto_goTypes = nil
	file_kuaishou_proto_depIdxs = nil
}
