syntax = "proto2";

package kuaishou.ad.adx;

option go_package = "mh_proxy/pb/kuaishou";

option java_package = "com.kuaishou.protobuf.ad.adx.rtb";
option java_outer_classname = "AdxRTBProto";
option java_multiple_files = true;


// 二维尺寸信息

message Size {

  optional uint32 width = 1; // 必填！宽度

  optional uint32 height = 2; // 必填！高度

}

// 版本号信息

message Version {

  optional uint32 major = 1; // 必填！

  optional uint32 minor = 2; // 选填！

  optional uint32 micro = 3; // 选填！

}

message Imp {

  enum AdmType {

    UNKOWN = 0;

    H5 = 1;

    JSON = 2;

  }

  required string imp_id = 1; //必填!曝光标识ID

  optional string tag_id = 2; //广告位标识ID

  optional Banner banner = 3; //Banner广告为

  optional Native native = 4;

  optional Video video = 5;

  optional uint32 instl = 6 [default = 0]; //是否是插屏广告

  optional double bid_floor = 7 [default = 0]; //最低竞价价格

  optional uint32 secure = 8;

  optional AdmType adm_type = 9;

  optional uint32 ads_count = 10; //请求的广告数

  optional Pmp pmp = 11;

  optional double cpm_bid_floor = 12 [default = 0]; //CPM最低竞价价格

  repeated AdTypeEnum.AdType ad_type = 13; // 可接受的广告类型列表

  repeated uint64 date_timestamp = 14; //开屏广告需返回广告的投放日期列表，单位:日期，如值1567267200表示 2019年9月1号

  repeated Adm.CreativeType creative_type = 15; // 可接受的创意类型

  optional int32 ad_style = 16;  //广告位样式  1: 信息流、2: 激励视频、3：插屏、4：开屏、5：banner

  optional string app_id = 17; // app标识

  optional string pos_id = 18; // 广告位标识 pos_id

  optional string medium_package_name = 20;

  optional string blocking_keyword = 25; // 搜索广告位-搜索词

}

message Pmp {

  optional uint32 private_auction = 1;

  repeated Deal deal = 2;

}

message Deal {

  required string deal_id = 1; //直接交易的唯一ID

  optional float bid_floor = 2; //本次展示的最低竞价

  optional string bid_floor_cur = 3;

  optional uint32 at = 4;

  repeated string wseat = 5;

  repeated string wa_domain = 6;

}

message Banner {

  required string banner_id = 1;

  optional Size size = 2;

  optional uint32 pos = 3; // 广告在屏幕上的位置　

}

message Native {

  required string native_id = 1;

  optional string request = 2; //遵循原生广告制定规格的请求信息

  optional Size size = 3; //广告位宽高

  optional Version version = 4; //request版本

  repeated uint32 api = 5;

  repeated uint32 battr = 6; //限制的物料属性

  optional float size_ratio = 7; //广告位宽高比，如果size_ratio>0，优先取size_ratio，其次取size

}

message Video {

  repeated string mines = 1; //必填!支持播放的广告格式

  optional uint32 min_duration = 2;

  optional uint32 max_duration = 3;

  repeated uint32 protocol = 4;

  optional Size size = 5;

  optional float video_size = 6; //视频大小

  repeated uint32 battr = 7;

  optional bool frequency_capping = 8 [default = false];


}

message App {

  optional string app_id = 1; //交易平台设定的appid

  optional string name = 2; //应用名称

  optional string bundle = 3; //app包名

  optional string domain = 4; //app对应的domain

  optional string store_url = 5;

  repeated string cat = 6;

  optional Version version = 7;

  optional bool paid = 8 [default = false];

  optional Publisher publisher = 9;

  optional string keywords = 10;

}

message Publisher {

  optional string publisher_id = 1;

  optional string name = 2;

  repeated string cat = 3;

  optional string domain = 4;

}

message Udid {

  optional string idfa = 1; // iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}

  optional string imei = 2; // Android手机设备的IMEI，格式要求[0-9a-fA-F]{14,15}

  optional string mac = 3; // Android非手机设备的WiFi网卡MAC地址，格式要求[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}

  optional string imei_md5 = 4; // Android手机设备的IMEI，经过MD5加密，格式要求[0-9A-Za-z]{32}

  optional string android_id = 5; //必填！Android手机设备系统ID，格式要求[0-9A-Za-z]{16}

  optional string idfa_md5 = 8; // iOS设备的IDFA，经过MD5加密，格式要求[0-9A-Za-z]{32}

  optional string androidid_md5 = 9; // Android手机设备系统ID，经过MD5加密，格式要求[0-9A-Za-z]{32}

  optional string oaid = 10; // Android手机匿名设备标识符

  optional string current_caid = 11; // 当前caid

  optional string current_caid_version = 12; // 当前caid版本

  optional string last_caid = 13; // 上一版本caid

  optional string last_caid_version = 14;// 上一版本caid版本

  optional string paid = 16;// 拼多多设备指纹

  optional string aaid = 17;// 阿里设备指纹

  optional string current_paid_1_5 = 21; // 拼多多设备指纹paid1.5, 当前版本

  optional string last_paid_1_5 = 22; // 拼多多设备指纹paid1.5, 上一个版本p

};

message Device {

  //设备类型

  enum DeviceType {

    UNKNOWN_DEVICE_TYPE = 0;

    PHONE = 1; //手机

    TABLET = 2; //平板

  }

  enum OsType {

    UNKNOWN_OS_TYPE = 0;

    ANDROID = 1; // Android

    IOS = 2; //iOS

  }

  optional string user_agent = 1;

  optional Geo geo = 2;

  optional string ip = 3;

  optional string ipv6 = 4;

  optional DeviceType device_type = 5;

  optional OsType os_type = 6; //操作系统类型

  optional Version os_version = 7; //操作系统版本

  optional string make = 8; //设备制造商

  optional string model = 9; //设备型号(手机品牌+型号拼接而成)

  optional Size screen_size = 10; //屏幕尺寸

  optional Udid udid = 11; //唯一设备标识

  repeated string install_app = 12; // 安装的app列表

  optional string package_name = 13; // 包名

  optional string ua = 14; // webview user-agent

  optional string boot_mark = 15; // 系统启动标识,反作弊字段

  optional string update_mark = 16; // 系统启动标识,反作弊字段

  optional string model_without_brand = 17; // 设备型号（只有型号没有品牌）

  optional string browser_ua = 18; // 浏览器ua字段

  repeated string install_app_md5 = 19; // 安装的app列表(加密)

  optional string device_model = 20;  // 设备型号 (设备原始信息，不经过加工）

}

message Geo {

  enum GeoType {

    UNKNOWN = 0;

    GPS = 1;

    IP = 2;

    USER_SUPPLIER = 3;

    GEO_HASH = 4;

  }

  optional double lat = 1; //纬度

  optional double lon = 2; //经度

  optional GeoType type = 3;

  optional string country = 4; //国家编码

  optional string province = 5;

  optional string region = 6;

  optional string city = 7; //城市名

  optional string geo_hash = 8; // geohash

}

message Segment {

  required string segment_id = 1;

  optional string name = 2;

  optional string value = 3;

}

message Data {

  required string data_id = 1;

  optional string name = 2;

  repeated Segment segment = 3;

}


message UserEmb {

  repeated float user_emb_a = 1; // 向量a，一开始默认使用字段

  repeated float user_emb_b = 2; // 向量b，向量升级时的切换字段

}


message User {


  enum Gender {

    U = 0; //unknown

    M = 1; //male

    F = 2; //female

    O = 3; //other

  }

  optional string user_id = 1;

  optional string buyer_id = 2;

  optional Gender gender = 3;

  optional uint32 age = 5;

  optional string keywords = 6; //逗号分隔的关键词，兴趣和意图

  optional string custom_data = 7;

  repeated Data data = 8;

  repeated string user_tags = 9;

  repeated uint64 orientation = 10;

  optional UserEmb user_embedding = 11; // 用户embedding向量

}

// 广告效果跟踪信息

message Tracking {

  // 广告展示过程事件类型

  enum TrackingEvent {

    // 广告展示过程共性事件

    AD_CLICK = 0; // 广告被点击

    AD_EXPOSURE = 1; // 广告被展现

    AD_CLOSE = 2; // 广告被关闭

    // 视频类广告展示过程事件

    VIDEO_AD_START = 101000; // 视频开始播放

    VIDEO_AD_FULL_SCREEN = 101001; // 视频全屏

    VIDEO_AD_END = 101002; // 视频正常播放结束

    VIDEO_AD_START_CARD_CLICK = 101003; // 点击预览图播放视频


    // 下载类广告推广APP相关事件

    APP_AD_DOWNLOAD = 102000; // 下载推广APP

    APP_AD_INSTALL = 102001; // 安装推广APP

    APP_AD_ACTIVE = 102002; // 激活推广APP


    AD_ACTION_BAR_CLICK = 103000; // ActionBar点击


    AD_LANDING_PAGE_CLICK = 104000; //落地页前置点击检测

    VIDEO_AD_PLAYED_2S = 105000; //视频播放2s

    VIDEO_AD_PLAYED_5S = 106000; //视频播放5s

    VIDEO_AD_LIKE = 107000; // 点赞

    VIDEO_AD_SHARE = 108000; // 转发

    VIDEO_AD_COMMENT = 109000; // 评论

    AD_EXPOSURE_1FRAME = 110000; // 广告第一帧曝光

    VIDEO_AD_PLAYED_3S = 111000; //视频播放3s

  };

  optional TrackingEvent tracking_event = 1; // 被跟踪的广告展示过程事件

  repeated string tracking_url = 2; // 事件监控URL

}

message SeatBid {

  repeated Bid bid = 1;

  optional string seat = 2; //出价者

  optional bool cookie_mapping = 3 [default = false];

  optional bool bid_group = 4 [default = false]; //false表示可以独立胜出，true表示必须整组胜出或者失败

}

message Bid {

  enum BidType {

    NO_TYPE = 0;

    CPC = 1;

    CPM = 2;

  }

  enum LandingPageShowSytle {

    BLUE_BAR = 0; //蓝条

    PREPOSE_LANDING_PAGE = 1; //落地页前置

  }

  required string bid_id = 1; //竞拍者生成的竞价id，用于记录日志或者行为追踪

  required string imp_id = 2; //关联竞价请求中的Imp对象的ID

  required double price = 3;

  required string ad_id = 4; //广告ID

  optional string notice_url = 5; //Win notice URL

  repeated Tracking ad_tracking = 6;

  required Adm adm = 7;

  repeated string adomain = 8; //用于限制性广告域名

  optional string bundle = 9;

  //    optional string campaign_id = 10;

  optional string creative_id = 11;

  repeated string cat = 12;

  repeated string attr = 13;

  optional Size size = 14; //如果是信息流广告，该值表示封面的大小；如果是图文广告，该值表示图片素材的大小

  optional string advertiser_username = 15; // 广告主账户名

  optional string industry = 16; //创意所属行业

  optional string bid_ext_data = 17; // dsp返回的usertag等信息

  optional BidType bid_type = 18; //指定广告参与的竞价方式

  optional Size video_size = 19; //创意视频尺寸

  optional Ocpx ocpx = 20; //ocpx参数

  optional LandingPageShowSytle landing_page_show_style = 21; //落地页样式

  optional int32 landing_page_type = 22; //落地页类型

  optional Category category = 23; //类目信息

  optional double commodity_price = 24; //推广商品价格

  optional uint64 advertiser_id = 25; // 广告主账户id

  optional uint64 plan_id = 26; // 计划id

  optional uint64 unit_id = 27; // 单元id

  optional string deal_id = 28; // 直接交易的唯一ID;

  optional Size activity_card_size = 29; // 活动卡片尺寸

  optional Card card = 32; // 海量回传卡片信息

  optional uint64 user_id = 34; // 参竞账户对应的快手用户id

}


message Card {

  enum AdxCardType {

    UN_CARD = 0; //默认非卡片样式

    PIC_CARD = 100; //图片卡片

  }

  optional string card_id = 1; // 卡片id

  optional string card_url = 2; // 卡片url

  optional int32 card_width = 3; // 卡片宽

  optional int32 card_height = 4; // 卡片高

  optional string card_title = 5; // 卡片title

  optional string icon_url = 6;

  optional int32 icon_width = 7; // 卡片宽

  optional int32 icon_height = 8; // 卡片高

  optional AdxCardType adx_card_type = 9; //卡片样式

}



message Category {

  optional int64 first_category_num = 1;

  optional string first_category_name = 2;

  optional int64 second_category_num = 3;

  optional string second_category_name = 4;

  optional int64 third_category_num = 5;

  optional string third_category_name = 6;

}


message Ocpx {

  optional int32 advertise_type = 1;

  optional bool need_ocpx = 2;

}


message Atlas {

  repeated string image_url = 1; //图片url集合

  optional string cover_url = 2; //封面

  optional string music_url = 3; //背景音乐

  optional string caption = 4; //标题

}

message Adm {

  enum CreativeType {

    NO_TYPE = 0; // 无创意类型，主要针对原生自定义素材广告，不再制定返回广告的创意类型，根据广告位设置对返回字段进行读取即可

    TEXT = 1; // 纯文字广告，一般由title、description构成

    IMAGE = 2; // 纯图片广告，一般由单张image_src构成A

    TEXT_ICON = 3; // 图文混合广告，一般由单张icon_src和title、description构成，包括静态开屏广告

    VIDEO = 4; // 视频广告，一般由视频URL和视频时长构成，包括动态开屏广告

    ATLAS = 5; //图集

    STICKER = 6; // 便利贴

    VERTICAL_SCREEN = 7; // 竖版视频

    HORIZONTAL_SCREEN = 8; // 横版视频

    VERTICAL_IMAGE = 9; // 竖版图片

    HORIZONTAL_IMAGE = 10; // 横版图片

  };

  // 交互类型

  enum InteractionType {

    NO_INTERACTION = 0; // 无动作，即广告广告点击后无需进行任何响应

    SURFING = 1; // 使用浏览器打开网页

    DOWNLOAD = 2; // 下载应用

  };

  optional CreativeType creative_type = 1; //创意类型

  optional InteractionType interaction_type = 2; //交互类型

  optional string title = 4;

  optional string desc = 5;

  optional string click_url = 6; //点击跳转h5页面地址

  optional string video_url = 7; //广告视频物料地址

  optional uint64 photo_id = 8; //视频photo_id，这个只针对在快手app上存在的视频，video_url和photo_id必须至少存在一个

  optional uint32 video_duration = 9; //视频持续时间，视频广告必填

  optional string deeplink_url = 10; //app唤醒地址

  optional string cover_url = 11; //视频封面地址

  optional Atlas atlas = 12;

  optional string package_name = 13; //安卓安装包包名

  optional uint32 package_size = 14; //安装包大小

  optional string app_name = 15; //应用名称

  optional string icon_url = 16; //应用icon url

  optional string bundle_id = 17; //苹果应用bundleID

  optional AdTypeEnum.AdType ad_type = 18; //返回广告类型

  optional string image_url = 19; //图文广告的图片url,已弃用

  optional uint64 date_timestamp = 20; //如果是开屏广告，表示当前广告的投放日期，单位:日期，如值1567267200表示 2019年9月1号

  repeated string pic_urls = 21; //图文广告图片列表,适用于开屏图文/单图/多图等广告类型

  optional uint32 target_type = 22; //广告的应用安装定向类型，仅对安卓下载类和dp类广告生效；0-不限；1-定向已安装；2-定向未安装

  optional string activity_card_url = 23; //活动卡片地址

  optional bool market_direct = 24; //广告商是否支持应用直投

  optional string app_version = 25; //下载类app应用版本

  optional string developer_name = 26; //app开发者（公司）名称

  optional string app_privacy_url = 27; //隐私政策url

  optional string app_permission = 28; //应用权限url

  optional uint64 package_size_v2 = 29; //安装包大小, long类

  optional string function_introduction = 30; //功能介绍

}

// 网络环境信息

message Network {

  // 网络连接类型

  enum ConnectionType {

    CONNECTION_UNKNOWN = 0; // 无法探测当前网络状态

    CELL_UNKNOWN = 1; // 蜂窝数据接入，未知网络类型

    CELL_2G = 2; // 蜂窝数据2G网络

    CELL_3G = 3; // 蜂窝数据3G网络

    CELL_4G = 4; // 蜂窝数据4G网络

    CELL_5G = 5; // 蜂窝数据5G网络

    WIFI = 100; // Wi-Fi网络接入

    ETHERNET = 101; // 以太网接入

    NEW_TYPE = 999; // 未知新类型

  }

  // 移动运营商类型

  enum OperatorType {

    UNKNOWN_OPERATOR = 0; // 未知的运营商

    CHINA_MOBILE = 1; // 中国移动

    CHINA_TELECOM = 2; // 中国电信

    CHINA_UNICOM = 3; // 中国联通

    OTHER_OPERATOR = 99; // 其他运营商

  }

  optional string ipv4 = 1; // 必填！用户设备的公网IPv4地址，服务器对接必填，格式要求：***************

  optional ConnectionType connection_type = 2; // 必填！网络连接类型，用于判断网速

  optional OperatorType operator_type = 3; // 必填！移动运营商类型，用于运营商定向广告

}


// 特性信息

message Feature {

  required string name = 1; // 名称

  optional string value = 2; // 参数值, 可为空

  optional uint64 int_value = 3; // 参数值, 可为空

}


message Preview {

  optional string preview_creative_id = 1; // 体验的创意id

}


message BidRequest {

  required string request_id = 1; //必填！bid request 唯一id,由交易平台生成

  repeated Imp imp = 2; //必填！广告位信息

  optional App app = 3;

  optional Device device = 4;

  optional User user = 5;

  optional Network network = 6;

  optional bool debug = 7 [default = false]; //false=生产者模式，true=测试模式

  optional uint32 at = 8 [default = 2]; //竞价类型,1=first price;2=second price plus;交易平台自定义的竞价类型，需大于500

  optional uint32 timeout = 9 [default = 200];

  repeated string black_cat = 10; //广告行业黑名单

  repeated string black_adv = 11; //广告主黑名单

  repeated Feature feature = 12; // 特性开关, 用于小流量实验

  optional Preview preview = 13; // 体验信息

}


message DpaInfo {

  optional string token = 1;

  optional string extend = 2;

}

message BidResponse {

  enum NoBidReason {

    UNKNOWN = 0;

    TECHNICAL_ERROR = 1; //技术错误

    INVALID_REQUEST = 2; //非法请求

    WEB_SPIDER = 3; //网络爬虫

    NON_HUMAN_TRAFFIC = 4; //非认为请求

    ILLEGAL_IP = 5; //非法ip

    UNSUPPORTED_DEVICE = 6; //不支持的设备类型

    BLOCKED_PUBLISHER = 7; //来自受限展示者

    BLOCKED_SITE = 8; //来自受限站点

    UNMATCHED_USER = 9; //用户不匹配

  }

  optional string request_id = 1; //必填，对应BidRequest中定义的request_id

  repeated SeatBid seat_bid = 2;

  optional uint32 status = 3; //0-ok，其他表示没有广告返回

  optional string bid_id = 4; //响应id

  optional NoBidReason noBidReason = 5;

  optional string custom_data = 6; // dsp返回的usertag等信息

  optional string ext_data = 7; // dsp返回的扩展字段，如自定义宏等

  optional Deal deal = 8; //返回对应的deal. 已废弃

  optional bool is_interest = 9; //是否参与dpa

  optional DpaInfo dpa_info = 10; //dpa相关附加信息


}


message UrlDeliveryBidResponseWrapper {

  required BidResponse response = 1;

  required uint64 url_id = 2;

}


message AdTypeEnum {

  enum AdType {

    NO_AD_TYPE = 0;

    KUAISHOU_EXPLORE_FEED_VIDEO = 1; //发现页第五位视频广告

    KUAISHOU_PROFILE_BETWEEN_COMMENTS_PIC_TEXT = 2; //评论第四位图文广告

    KUAISHOU_PROFILE_BETWEEN_COMMENTS_VIDEO = 3; //评论第四位视频广告

    KUAISHOU_PROFILE_PLAY_END_PIC_TEXT = 4; //后贴片图文，暂不支持

    KUAISHOU_PROFILE_PLAY_END_VIDEO = 5; //后贴片视频，暂不支持

    KUAISHOU_SPLASH_SLOT_VIDEO = 6; //快手动态开屏广告，暂不支持

    KUAISHOU_SPLASH_SLOT_PIC_TEXT = 7; //快手静态开屏广告

    KUAISHOU_SPLASH_TOPVIEW_SLOT_VIDEO = 8; //快手动态开屏+topview，暂不支持

    KUAISHOU_SPLASH_TOPVIEW_SLOT_PIC_TEXT = 9; //快手静态开屏+topview，暂不支持

    FEED_GROUP_PIC_TEXT = 10; //图文信息流三图广告

    FEED_SMALL_PIC_TEXT = 11; //图文信息流小图广告

    FEED_LARGE_PIC_TEXT = 12; //图文信息流大图广告

  }

}