// 如果使用此注释，则使用proto3; 否则使用proto2

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: vivo.proto

package vivo

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 唯一标识一个BidRequest请求,32位字符组成的字符串
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 当前协议版本号，目前为1
	Ver    int32                    `protobuf:"varint,2,opt,name=ver,proto3" json:"ver,omitempty"`
	Imp    []*BidRequest_Impression `protobuf:"bytes,3,rep,name=imp,proto3" json:"imp,omitempty"`
	Device *BidRequest_Device       `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	// 是否测试流量,0：正式流量、1：测试流量(不产生曝光、不收费)
	Test int32 `protobuf:"varint,5,opt,name=test,proto3" json:"test,omitempty"`
	// 竞价类型,1：第一竞价、2：第二竞价；目前只支持第二竞价
	At int32 `protobuf:"varint,6,opt,name=at,proto3" json:"at,omitempty"`
	// 广告行业黑名单(二级广告行业)
	Bcat []int32 `protobuf:"varint,7,rep,packed,name=bcat,proto3" json:"bcat,omitempty"`
	// 广告主黑名单
	Badv []int32         `protobuf:"varint,8,rep,packed,name=badv,proto3" json:"badv,omitempty"`
	Geo  *BidRequest_Geo `protobuf:"bytes,9,opt,name=geo,proto3" json:"geo,omitempty"`
	App  *BidRequest_App `protobuf:"bytes,10,opt,name=app,proto3" json:"app,omitempty"`
	//推广应用列表
	InstalledAppIds []string `protobuf:"bytes,11,rep,name=installed_app_ids,json=installedAppIds,proto3" json:"installed_app_ids,omitempty"`
	// 广告行业黑名单(二级广告行业)，v2版本
	BcatV2 []int32 `protobuf:"varint,12,rep,packed,name=bcat_v2,json=bcatV2,proto3" json:"bcat_v2,omitempty"`
	// 应用转化率数组
	AppCvrs []*BidRequest_AppCvr `protobuf:"bytes,13,rep,name=app_cvrs,json=appCvrs,proto3" json:"app_cvrs,omitempty"`
	//搜索关键词
	Query string `protobuf:"bytes,14,opt,name=query,proto3" json:"query,omitempty"`
	//人群包是否命中
	CrowdStatus int32 `protobuf:"varint,15,opt,name=crowd_status,json=crowdStatus,proto3" json:"crowd_status,omitempty"`
	// 人群包id
	CrowdPackageIds []int32 `protobuf:"varint,16,rep,packed,name=crowd_package_ids,json=crowdPackageIds,proto3" json:"crowd_package_ids,omitempty"`
	//用户安装列表有效性，1-正常，2-异常
	InstalledAppStatus int32 `protobuf:"varint,17,opt,name=installed_app_status,json=installedAppStatus,proto3" json:"installed_app_status,omitempty"`
	//CPD特征字段
	QueryFeature string `protobuf:"bytes,18,opt,name=query_feature,json=queryFeature,proto3" json:"query_feature,omitempty"`
	// 频控历史队列
	FreqHis *BidRequest_FreqHis `protobuf:"bytes,19,opt,name=freq_his,json=freqHis,proto3" json:"freq_his,omitempty"`
	//超时时间，单位ms
	Timeout int32 `protobuf:"varint,20,opt,name=timeout,proto3" json:"timeout,omitempty"`
	//召回模型类型, 值为1时表示vivo adx能给非常宽裕的邀约超时时间, 那么dsp能走尽可能完整的召回模型
	ModelType int32 `protobuf:"varint,21,opt,name=model_type,json=modelType,proto3" json:"model_type,omitempty"`
	// 微信应用信息
	WechatReqMsg *BidRequest_WechatReqMsg `protobuf:"bytes,22,opt,name=wechat_req_msg,json=wechatReqMsg,proto3" json:"wechat_req_msg,omitempty"`
	// 特征传递的载体(场景维度)
	Context map[string]string `protobuf:"bytes,23,rep,name=context,proto3" json:"context,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 特征传递的载体(用户维度)
	UserContext map[string]string `protobuf:"bytes,24,rep,name=user_context,json=userContext,proto3" json:"user_context,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetVer() int32 {
	if x != nil {
		return x.Ver
	}
	return 0
}

func (x *BidRequest) GetImp() []*BidRequest_Impression {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetTest() int32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *BidRequest) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *BidRequest) GetBcat() []int32 {
	if x != nil {
		return x.Bcat
	}
	return nil
}

func (x *BidRequest) GetBadv() []int32 {
	if x != nil {
		return x.Badv
	}
	return nil
}

func (x *BidRequest) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetInstalledAppIds() []string {
	if x != nil {
		return x.InstalledAppIds
	}
	return nil
}

func (x *BidRequest) GetBcatV2() []int32 {
	if x != nil {
		return x.BcatV2
	}
	return nil
}

func (x *BidRequest) GetAppCvrs() []*BidRequest_AppCvr {
	if x != nil {
		return x.AppCvrs
	}
	return nil
}

func (x *BidRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *BidRequest) GetCrowdStatus() int32 {
	if x != nil {
		return x.CrowdStatus
	}
	return 0
}

func (x *BidRequest) GetCrowdPackageIds() []int32 {
	if x != nil {
		return x.CrowdPackageIds
	}
	return nil
}

func (x *BidRequest) GetInstalledAppStatus() int32 {
	if x != nil {
		return x.InstalledAppStatus
	}
	return 0
}

func (x *BidRequest) GetQueryFeature() string {
	if x != nil {
		return x.QueryFeature
	}
	return ""
}

func (x *BidRequest) GetFreqHis() *BidRequest_FreqHis {
	if x != nil {
		return x.FreqHis
	}
	return nil
}

func (x *BidRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *BidRequest) GetModelType() int32 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *BidRequest) GetWechatReqMsg() *BidRequest_WechatReqMsg {
	if x != nil {
		return x.WechatReqMsg
	}
	return nil
}

func (x *BidRequest) GetContext() map[string]string {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *BidRequest) GetUserContext() map[string]string {
	if x != nil {
		return x.UserContext
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//对应BidRequest中的请求id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// bidder 返回的响应ID,可用于问题追踪
	BidId string `protobuf:"bytes,2,opt,name=bid_id,json=bidId,proto3" json:"bid_id,omitempty"`
	// 状态 是否参与竞价 0:参与竞价 >0:不参与竞价
	Status  int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	SeatBid []*BidResponse_SeatBid `protobuf:"bytes,4,rep,name=seat_bid,json=seatBid,proto3" json:"seat_bid,omitempty"`
	//  未出价原因 0:未知错误、1：技术错误、2：无效请求、4：可疑流量、5：代理id、6：不支持的设备
	//  7:被屏蔽媒体、8：不匹配的用户
	Nbr int32 `protobuf:"varint,5,opt,name=nbr,proto3" json:"nbr,omitempty"`
	// dsp实验ID标识
	Exp string `protobuf:"bytes,6,opt,name=exp,proto3" json:"exp,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetBidId() string {
	if x != nil {
		return x.BidId
	}
	return ""
}

func (x *BidResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BidResponse) GetSeatBid() []*BidResponse_SeatBid {
	if x != nil {
		return x.SeatBid
	}
	return nil
}

func (x *BidResponse) GetNbr() int32 {
	if x != nil {
		return x.Nbr
	}
	return 0
}

func (x *BidResponse) GetExp() string {
	if x != nil {
		return x.Exp
	}
	return ""
}

// 广告位相关信息
type BidRequest_Impression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 此impression在当前Request中的唯一id,从0开始
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 广告位唯一标识,32位字符组成的字符串
	TagId string `protobuf:"bytes,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	// 广告类型 1：信息流、2：开屏、3：banner、4：插屏
	AdType int32 `protobuf:"varint,3,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`
	// 是否支持应用下载 0：不支持、1：支持
	Dld bool `protobuf:"varint,4,opt,name=dld,proto3" json:"dld,omitempty"`
	// 是否支持deeplink广告 0：不支持、1：支持
	Deeplink bool `protobuf:"varint,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// 底价 单位为 分/CPM
	BidFloor int64 `protobuf:"varint,6,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"`
	// 物料模板ID,多个ID的情况下DSP只要返回满足其中一个模板要求的创意即可
	TemplateIds []int32                    `protobuf:"varint,7,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
	Pmp         *BidRequest_Impression_Pmp `protobuf:"bytes,8,opt,name=pmp,proto3" json:"pmp,omitempty"`
	// 是否支持自定义h5下载，0:不支持，1支持
	H5Dld bool `protobuf:"varint,9,opt,name=h5_dld,json=h5Dld,proto3" json:"h5_dld,omitempty"`
	// 支持召回的广告数量，默认1
	AdsCount int32 `protobuf:"varint,10,opt,name=ads_count,json=adsCount,proto3" json:"ads_count,omitempty"`
	// 投放日期
	DeliveryDate string `protobuf:"bytes,11,opt,name=delivery_date,json=deliveryDate,proto3" json:"delivery_date,omitempty"`
	// 是否支持deeplink广告 0：不支持、1：不限制、2：仅支持
	SupportDeeplink int32 `protobuf:"varint,12,opt,name=support_deeplink,json=supportDeeplink,proto3" json:"support_deeplink,omitempty"`
	// cpc类型广告底价，单位：分/CPC
	CpcBidFloor int64 `protobuf:"varint,13,opt,name=cpc_bid_floor,json=cpcBidFloor,proto3" json:"cpc_bid_floor,omitempty"`
	//支持的出价类型,0-cpm,1-cpc
	BidTypes        []int32                                `protobuf:"varint,14,rep,packed,name=bid_types,json=bidTypes,proto3" json:"bid_types,omitempty"`
	CoBiddingConfig *BidRequest_Impression_CoBiddingConfig `protobuf:"bytes,15,opt,name=co_bidding_config,json=coBiddingConfig,proto3" json:"co_bidding_config,omitempty"`
	//流量质量可投类型 0：不可投、1：可投
	FlowQuality int32 `protobuf:"varint,16,opt,name=flowQuality,proto3" json:"flowQuality,omitempty"`
	// 原生态or快生态类型
	SourceType int32 `protobuf:"varint,17,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	//支持的广告形式: 1-普通网址, 2-应用下载, 3-deeplink 普通网址, 4-deeplink 应用下载, 5-自定义H5应用下载, 11-微信小程序SDK调起
	AdStyles []int32 `protobuf:"varint,18,rep,packed,name=ad_styles,json=adStyles,proto3" json:"ad_styles,omitempty"`
	// 限制召回的应用包名集合(如果该集合不为空, 需要严格按照此包名集合进行召回)
	PkgWhitelist  []string                               `protobuf:"bytes,19,rep,name=pkg_whitelist,json=pkgWhitelist,proto3" json:"pkg_whitelist,omitempty"`
	IncreaseRatio []*BidRequest_Impression_IncreaseRatio `protobuf:"bytes,20,rep,name=increase_ratio,json=increaseRatio,proto3" json:"increase_ratio,omitempty"`
	//流量包id
	FlowPkgIds []int32 `protobuf:"varint,21,rep,packed,name=flow_pkg_ids,json=flowPkgIds,proto3" json:"flow_pkg_ids,omitempty"`
	// 包名黑名单
	PkgBlacklist []string `protobuf:"bytes,22,rep,name=pkg_blacklist,json=pkgBlacklist,proto3" json:"pkg_blacklist,omitempty"`
	// 轻互动类型：1-激励视频，0-不支持
	EasyPlayType int32 `protobuf:"varint,23,opt,name=easy_play_type,json=easyPlayType,proto3" json:"easy_play_type,omitempty"`
	// 特征传递的载体(广告维度)
	ImpContext map[string]string `protobuf:"bytes,24,rep,name=imp_context,json=impContext,proto3" json:"imp_context,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BidRequest_Impression) Reset() {
	*x = BidRequest_Impression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Impression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Impression) ProtoMessage() {}

func (x *BidRequest_Impression) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Impression.ProtoReflect.Descriptor instead.
func (*BidRequest_Impression) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Impression) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BidRequest_Impression) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequest_Impression) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidRequest_Impression) GetDld() bool {
	if x != nil {
		return x.Dld
	}
	return false
}

func (x *BidRequest_Impression) GetDeeplink() bool {
	if x != nil {
		return x.Deeplink
	}
	return false
}

func (x *BidRequest_Impression) GetBidFloor() int64 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

func (x *BidRequest_Impression) GetTemplateIds() []int32 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

func (x *BidRequest_Impression) GetPmp() *BidRequest_Impression_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_Impression) GetH5Dld() bool {
	if x != nil {
		return x.H5Dld
	}
	return false
}

func (x *BidRequest_Impression) GetAdsCount() int32 {
	if x != nil {
		return x.AdsCount
	}
	return 0
}

func (x *BidRequest_Impression) GetDeliveryDate() string {
	if x != nil {
		return x.DeliveryDate
	}
	return ""
}

func (x *BidRequest_Impression) GetSupportDeeplink() int32 {
	if x != nil {
		return x.SupportDeeplink
	}
	return 0
}

func (x *BidRequest_Impression) GetCpcBidFloor() int64 {
	if x != nil {
		return x.CpcBidFloor
	}
	return 0
}

func (x *BidRequest_Impression) GetBidTypes() []int32 {
	if x != nil {
		return x.BidTypes
	}
	return nil
}

func (x *BidRequest_Impression) GetCoBiddingConfig() *BidRequest_Impression_CoBiddingConfig {
	if x != nil {
		return x.CoBiddingConfig
	}
	return nil
}

func (x *BidRequest_Impression) GetFlowQuality() int32 {
	if x != nil {
		return x.FlowQuality
	}
	return 0
}

func (x *BidRequest_Impression) GetSourceType() int32 {
	if x != nil {
		return x.SourceType
	}
	return 0
}

func (x *BidRequest_Impression) GetAdStyles() []int32 {
	if x != nil {
		return x.AdStyles
	}
	return nil
}

func (x *BidRequest_Impression) GetPkgWhitelist() []string {
	if x != nil {
		return x.PkgWhitelist
	}
	return nil
}

func (x *BidRequest_Impression) GetIncreaseRatio() []*BidRequest_Impression_IncreaseRatio {
	if x != nil {
		return x.IncreaseRatio
	}
	return nil
}

func (x *BidRequest_Impression) GetFlowPkgIds() []int32 {
	if x != nil {
		return x.FlowPkgIds
	}
	return nil
}

func (x *BidRequest_Impression) GetPkgBlacklist() []string {
	if x != nil {
		return x.PkgBlacklist
	}
	return nil
}

func (x *BidRequest_Impression) GetEasyPlayType() int32 {
	if x != nil {
		return x.EasyPlayType
	}
	return 0
}

func (x *BidRequest_Impression) GetImpContext() map[string]string {
	if x != nil {
		return x.ImpContext
	}
	return nil
}

// 设备信息
type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ip地址
	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	// 厂商
	Make string `protobuf:"bytes,2,opt,name=make,proto3" json:"make,omitempty"`
	// 设备型号
	Model string `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	//设备的操作系统Android、IOS
	Os string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	// 系统版本号
	Osv string `protobuf:"bytes,5,opt,name=osv,proto3" json:"osv,omitempty"`
	// 网络类型(建议使用connection_type) 0：未知网络类型  1:移动网络 2：wifi
	NetType int32 `protobuf:"varint,6,opt,name=net_type,json=netType,proto3" json:"net_type,omitempty"`
	// 安卓设备的Imei md5 串
	DidMd5 string `protobuf:"bytes,7,opt,name=did_md5,json=didMd5,proto3" json:"did_md5,omitempty"`
	// 设备的Android ID md5 串
	DpidMd5 string `protobuf:"bytes,8,opt,name=dpid_md5,json=dpidMd5,proto3" json:"dpid_md5,omitempty"`
	// 设备的mac地址的md5串
	MacMd5 string `protobuf:"bytes,9,opt,name=mac_md5,json=macMd5,proto3" json:"mac_md5,omitempty"`
	// user agent
	Ua string `protobuf:"bytes,10,opt,name=ua,proto3" json:"ua,omitempty"`
	// 网络 0：无法探测当前网络状态 1：Cellular Network-Unknown Generation 2：2G 3：3G 4：4G 5：5G 100：WiFi
	ConnectionType int32 `protobuf:"varint,11,opt,name=connection_type,json=connectionType,proto3" json:"connection_type,omitempty"`
	// oaid
	Oaid string `protobuf:"bytes,12,opt,name=oaid,proto3" json:"oaid,omitempty"`
	// 设备类型: 0-手机;1-平板;2-PC;3-互联网电视
	DeviceType int32 `protobuf:"varint,13,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// 每次Android启动的时候都会重置的启动标识符
	BootMark string `protobuf:"bytes,14,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	// /data/data 最后访问时间戳
	UpdateMark string `protobuf:"bytes,15,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	// 个性化开关, 1:开关打开, 0:开关关闭, -1:未知状态, 2:用户手机无该开关
	Ostatus int32 `protobuf:"varint,16,opt,name=ostatus,proto3" json:"ostatus,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetNetType() int32 {
	if x != nil {
		return x.NetType
	}
	return 0
}

func (x *BidRequest_Device) GetDidMd5() string {
	if x != nil {
		return x.DidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetDpidMd5() string {
	if x != nil {
		return x.DpidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil {
		return x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetOstatus() int32 {
	if x != nil {
		return x.Ostatus
	}
	return 0
}

//地理信息
type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//纬度
	Lat float32 `protobuf:"fixed32,1,opt,name=lat,proto3" json:"lat,omitempty"`
	//经度
	Lon float32 `protobuf:"fixed32,2,opt,name=lon,proto3" json:"lon,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Geo) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

//应用信息
type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app 包名; 在交易平台中唯一
	Bundle string `protobuf:"bytes,1,opt,name=bundle,proto3" json:"bundle,omitempty"`
	// 媒体类型: 0-电子书, 1-自有媒体, other-联盟媒体
	MediaType int32 `protobuf:"varint,2,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetMediaType() int32 {
	if x != nil {
		return x.MediaType
	}
	return 0
}

// 转化率分值
type BidRequest_Cvr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 转化类型：1-下载，2-激活
	CvType int32 `protobuf:"varint,1,opt,name=cv_type,json=cvType,proto3" json:"cv_type,omitempty"`
	//转化率
	Cvr float32 `protobuf:"fixed32,2,opt,name=cvr,proto3" json:"cvr,omitempty"`
}

func (x *BidRequest_Cvr) Reset() {
	*x = BidRequest_Cvr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Cvr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Cvr) ProtoMessage() {}

func (x *BidRequest_Cvr) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Cvr.ProtoReflect.Descriptor instead.
func (*BidRequest_Cvr) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Cvr) GetCvType() int32 {
	if x != nil {
		return x.CvType
	}
	return 0
}

func (x *BidRequest_Cvr) GetCvr() float32 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

// 应用转化率
type BidRequest_AppCvr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//包名
	Pkg string `protobuf:"bytes,1,opt,name=pkg,proto3" json:"pkg,omitempty"`
	//转化率分值对象
	Cvrs []*BidRequest_Cvr `protobuf:"bytes,2,rep,name=cvrs,proto3" json:"cvrs,omitempty"`
}

func (x *BidRequest_AppCvr) Reset() {
	*x = BidRequest_AppCvr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AppCvr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AppCvr) ProtoMessage() {}

func (x *BidRequest_AppCvr) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AppCvr.ProtoReflect.Descriptor instead.
func (*BidRequest_AppCvr) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_AppCvr) GetPkg() string {
	if x != nil {
		return x.Pkg
	}
	return ""
}

func (x *BidRequest_AppCvr) GetCvrs() []*BidRequest_Cvr {
	if x != nil {
		return x.Cvrs
	}
	return nil
}

// 频控历史队列
type BidRequest_FreqHis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//创意ID
	CreativeIds []string `protobuf:"bytes,1,rep,name=creative_ids,json=creativeIds,proto3" json:"creative_ids,omitempty"`
	//包名
	Pkgs []string `protobuf:"bytes,2,rep,name=pkgs,proto3" json:"pkgs,omitempty"`
}

func (x *BidRequest_FreqHis) Reset() {
	*x = BidRequest_FreqHis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_FreqHis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_FreqHis) ProtoMessage() {}

func (x *BidRequest_FreqHis) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_FreqHis.ProtoReflect.Descriptor instead.
func (*BidRequest_FreqHis) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_FreqHis) GetCreativeIds() []string {
	if x != nil {
		return x.CreativeIds
	}
	return nil
}

func (x *BidRequest_FreqHis) GetPkgs() []string {
	if x != nil {
		return x.Pkgs
	}
	return nil
}

// 微信应用请求信息
type BidRequest_WechatReqMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否支持原生页预算
	WechatNativePage bool `protobuf:"varint,1,opt,name=wechat_native_page,json=wechatNativePage,proto3" json:"wechat_native_page,omitempty"`
	// 微信版本号
	WechatVer int64 `protobuf:"varint,2,opt,name=wechat_ver,json=wechatVer,proto3" json:"wechat_ver,omitempty"`
	// openSDK版本号
	OpensdkVer int64 `protobuf:"varint,3,opt,name=opensdk_ver,json=opensdkVer,proto3" json:"opensdk_ver,omitempty"`
}

func (x *BidRequest_WechatReqMsg) Reset() {
	*x = BidRequest_WechatReqMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_WechatReqMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_WechatReqMsg) ProtoMessage() {}

func (x *BidRequest_WechatReqMsg) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_WechatReqMsg.ProtoReflect.Descriptor instead.
func (*BidRequest_WechatReqMsg) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 7}
}

func (x *BidRequest_WechatReqMsg) GetWechatNativePage() bool {
	if x != nil {
		return x.WechatNativePage
	}
	return false
}

func (x *BidRequest_WechatReqMsg) GetWechatVer() int64 {
	if x != nil {
		return x.WechatVer
	}
	return 0
}

func (x *BidRequest_WechatReqMsg) GetOpensdkVer() int64 {
	if x != nil {
		return x.OpensdkVer
	}
	return 0
}

// 媒体约定的PMP交易信息
type BidRequest_Impression_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//直接交易标识 ID；由交易平台和 DSP 提前约定
	DealId string `protobuf:"bytes,1,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`
}

func (x *BidRequest_Impression_Pmp) Reset() {
	*x = BidRequest_Impression_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Impression_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Impression_Pmp) ProtoMessage() {}

func (x *BidRequest_Impression_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Impression_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Impression_Pmp) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Impression_Pmp) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

//联合出价配置
type BidRequest_Impression_CoBiddingConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//实验id
	ExpId string `protobuf:"bytes,1,opt,name=exp_id,json=expId,proto3" json:"exp_id,omitempty"`
}

func (x *BidRequest_Impression_CoBiddingConfig) Reset() {
	*x = BidRequest_Impression_CoBiddingConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Impression_CoBiddingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Impression_CoBiddingConfig) ProtoMessage() {}

func (x *BidRequest_Impression_CoBiddingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Impression_CoBiddingConfig.ProtoReflect.Descriptor instead.
func (*BidRequest_Impression_CoBiddingConfig) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Impression_CoBiddingConfig) GetExpId() string {
	if x != nil {
		return x.ExpId
	}
	return ""
}

type BidRequest_Impression_IncreaseRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//包名
	Pkg string `protobuf:"bytes,1,opt,name=pkg,proto3" json:"pkg,omitempty"`
	//提价系数
	Ratio float32 `protobuf:"fixed32,2,opt,name=ratio,proto3" json:"ratio,omitempty"`
}

func (x *BidRequest_Impression_IncreaseRatio) Reset() {
	*x = BidRequest_Impression_IncreaseRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Impression_IncreaseRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Impression_IncreaseRatio) ProtoMessage() {}

func (x *BidRequest_Impression_IncreaseRatio) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Impression_IncreaseRatio.ProtoReflect.Descriptor instead.
func (*BidRequest_Impression_IncreaseRatio) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *BidRequest_Impression_IncreaseRatio) GetPkg() string {
	if x != nil {
		return x.Pkg
	}
	return ""
}

func (x *BidRequest_Impression_IncreaseRatio) GetRatio() float32 {
	if x != nil {
		return x.Ratio
	}
	return 0
}

// 交易席位信息
type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid,proto3" json:"bid,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

// 出价结果
type BidResponse_SeatBid_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//Bidder 的竞价响应ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//  曝光id,对应Impession 中的id
	ImpId int32 `protobuf:"varint,2,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"`
	//  出价 单位 分/CPM
	Price int64 `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	// 创意ID
	CreativeId string `protobuf:"bytes,4,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`
	// 竞价成功通知地址
	Nurl string `protobuf:"bytes,5,opt,name=nurl,proto3" json:"nurl,omitempty"`
	//  曝光监测地址
	ImpTrackers []string `protobuf:"bytes,6,rep,name=imp_trackers,json=impTrackers,proto3" json:"imp_trackers,omitempty"`
	// 点击监测地址
	ClkTrackers []string `protobuf:"bytes,7,rep,name=clk_trackers,json=clkTrackers,proto3" json:"clk_trackers,omitempty"`
	//直接交易标识 ID
	DealId string `protobuf:"bytes,8,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`
	//广告物料信息，非预审必填
	Adm *BidResponse_SeatBid_Bid_Adm `protobuf:"bytes,9,opt,name=adm,proto3" json:"adm,omitempty"`
	//仅用于视频广告;视频开始播放监测地址
	VideoStartTrackers []string `protobuf:"bytes,10,rep,name=video_start_trackers,json=videoStartTrackers,proto3" json:"video_start_trackers,omitempty"`
	//仅用于视频广告;视频播放结束监测地址
	VideoCompleteTrackers []string `protobuf:"bytes,11,rep,name=video_complete_trackers,json=videoCompleteTrackers,proto3" json:"video_complete_trackers,omitempty"`
	//  可见曝光监测地址
	ViewabilityImpTrackers []string `protobuf:"bytes,12,rep,name=viewability_imp_trackers,json=viewabilityImpTrackers,proto3" json:"viewability_imp_trackers,omitempty"`
	//  上下文监测地址
	ContextTrackers []string `protobuf:"bytes,13,rep,name=context_trackers,json=contextTrackers,proto3" json:"context_trackers,omitempty"`
	// DSP自定义的extend data，vivo AdExchange会对其内容进行URL安全的base64编码并替换到创意代码的${EXT_DATA}宏中
	ExtendData string `protobuf:"bytes,20,opt,name=extend_data,json=extendData,proto3" json:"extend_data,omitempty"`
	// 使用的cvr预估类型，类型定义与Cvr对象中cvType类型对应
	CvType int32 `protobuf:"varint,21,opt,name=cv_type,json=cvType,proto3" json:"cv_type,omitempty"`
	// 人群包id
	CrowdPackageIds []int32 `protobuf:"varint,22,rep,packed,name=crowd_package_ids,json=crowdPackageIds,proto3" json:"crowd_package_ids,omitempty"`
	// deeplink检测地址
	DpTrackers []string `protobuf:"bytes,23,rep,name=dp_trackers,json=dpTrackers,proto3" json:"dp_trackers,omitempty"`
	// 出价类型：0-cpm 1-cpc，默认0
	BidType       int32                                  `protobuf:"varint,24,opt,name=bid_type,json=bidType,proto3" json:"bid_type,omitempty"`
	CoBiddingResp *BidResponse_SeatBid_Bid_CoBiddingResp `protobuf:"bytes,25,opt,name=co_bidding_resp,json=coBiddingResp,proto3" json:"co_bidding_resp,omitempty"`
	// 竞败信息回调地址
	Lurl string `protobuf:"bytes,26,opt,name=lurl,proto3" json:"lurl,omitempty"`
	// 使用的流量包id
	FlowPkgIdlist []int32 `protobuf:"varint,27,rep,packed,name=flow_pkg_idlist,json=flowPkgIdlist,proto3" json:"flow_pkg_idlist,omitempty"`
	// 视频监测地址
	VideoTrackers []string `protobuf:"bytes,28,rep,name=video_trackers,json=videoTrackers,proto3" json:"video_trackers,omitempty"`
	// 轻互动监测地址
	PlayTrackers []string `protobuf:"bytes,29,rep,name=play_trackers,json=playTrackers,proto3" json:"play_trackers,omitempty"`
	// 0：运营消息提醒方式，默认，1：强提醒方式，悬浮+响铃+振动，2：弱提醒
	StrongReminder int32 `protobuf:"varint,30,opt,name=strong_reminder,json=strongReminder,proto3" json:"strong_reminder,omitempty"`
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpId() int32 {
	if x != nil {
		return x.ImpId
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpTrackers() []string {
	if x != nil {
		return x.ImpTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetClkTrackers() []string {
	if x != nil {
		return x.ClkTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetAdm() *BidResponse_SeatBid_Bid_Adm {
	if x != nil {
		return x.Adm
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetVideoStartTrackers() []string {
	if x != nil {
		return x.VideoStartTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetVideoCompleteTrackers() []string {
	if x != nil {
		return x.VideoCompleteTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetViewabilityImpTrackers() []string {
	if x != nil {
		return x.ViewabilityImpTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetContextTrackers() []string {
	if x != nil {
		return x.ContextTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetExtendData() string {
	if x != nil {
		return x.ExtendData
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetCvType() int32 {
	if x != nil {
		return x.CvType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetCrowdPackageIds() []int32 {
	if x != nil {
		return x.CrowdPackageIds
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetDpTrackers() []string {
	if x != nil {
		return x.DpTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetBidType() int32 {
	if x != nil {
		return x.BidType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetCoBiddingResp() *BidResponse_SeatBid_Bid_CoBiddingResp {
	if x != nil {
		return x.CoBiddingResp
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetLurl() string {
	if x != nil {
		return x.Lurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetFlowPkgIdlist() []int32 {
	if x != nil {
		return x.FlowPkgIdlist
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetVideoTrackers() []string {
	if x != nil {
		return x.VideoTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetPlayTrackers() []string {
	if x != nil {
		return x.PlayTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetStrongReminder() int32 {
	if x != nil {
		return x.StrongReminder
	}
	return 0
}

//物料信息
type BidResponse_SeatBid_Bid_Adm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	//推广物类型
	AdType int32 `protobuf:"varint,2,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`
	//deeplink链接
	Deeplink string `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	//落地页
	LandingSite string `protobuf:"bytes,4,opt,name=landing_site,json=landingSite,proto3" json:"landing_site,omitempty"`
	//应用包名  说明：应用包需通过vivo开发者上传到vivo应用商店.
	AppPackage string `protobuf:"bytes,5,opt,name=app_package,json=appPackage,proto3" json:"app_package,omitempty"`
	//素材图片
	Img []*BidResponse_SeatBid_Bid_Adm_Img `protobuf:"bytes,6,rep,name=img,proto3" json:"img,omitempty"`
	//vivo广告主字典中的广告主ID
	AdvertiserId int64 `protobuf:"varint,7,opt,name=advertiser_id,json=advertiserId,proto3" json:"advertiser_id,omitempty"`
	//vivo行业分类字典中的行业二级分类ID
	Category int64 `protobuf:"varint,8,opt,name=category,proto3" json:"category,omitempty"`
	//创意模板id
	TemplateId int32 `protobuf:"varint,9,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	//视频
	Video *BidResponse_SeatBid_Bid_Adm_Video `protobuf:"bytes,10,opt,name=video,proto3" json:"video,omitempty"`
	//创意来源（推广产品名称），普通网址类信息流视频广告必填
	Source string `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`
	//中间页，视频播放完成后，需要webview显示的html资源
	RenderSite string `protobuf:"bytes,12,opt,name=render_site,json=renderSite,proto3" json:"render_site,omitempty"`
	//广告主头像地址，竖版视频广告必填
	SourceAvatar string `protobuf:"bytes,13,opt,name=source_avatar,json=sourceAvatar,proto3" json:"source_avatar,omitempty"`
	//广告文案，长度和是否必填需符合创意模板，激励视频新样式必填
	Text string `protobuf:"bytes,14,opt,name=text,proto3" json:"text,omitempty"`
	//DSP侧应用渠道号
	Channel string `protobuf:"bytes,15,opt,name=channel,proto3" json:"channel,omitempty"`
	//下载应用包渠道类型：1-vivo应用商店包，2-广告主渠道包
	PackageChannel int32 `protobuf:"varint,16,opt,name=package_channel,json=packageChannel,proto3" json:"package_channel,omitempty"`
	// 搜索广告展示URL
	ShowUrl string `protobuf:"bytes,17,opt,name=show_url,json=showUrl,proto3" json:"show_url,omitempty"`
	// 子链数组
	Subchain []*BidResponse_SeatBid_Bid_Adm_Subchain `protobuf:"bytes,18,rep,name=subchain,proto3" json:"subchain,omitempty"`
	// 互动广告相关数据
	Interact *BidResponse_SeatBid_Bid_Adm_Interact `protobuf:"bytes,19,opt,name=interact,proto3" json:"interact,omitempty"`
	// 微信小程序sdk调起
	MiniProgram *BidResponse_SeatBid_Bid_Adm_MiniProgram `protobuf:"bytes,20,opt,name=mini_program,json=miniProgram,proto3" json:"mini_program,omitempty"`
	//  快应用链接
	QuickLink string `protobuf:"bytes,21,opt,name=quick_link,json=quickLink,proto3" json:"quick_link,omitempty"`
	// 应用信息
	AppInfo *BidResponse_SeatBid_Bid_Adm_AppInfo `protobuf:"bytes,22,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`
	// 样式id
	StyleId string `protobuf:"bytes,23,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	// icon_url(deeplink调起微信小程序专用)
	IconUrl string `protobuf:"bytes,24,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// 应用名称(deeplink调起微信小程序专用)
	AppName string `protobuf:"bytes,25,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 轻互动组件，json格式的字符串
	EasyPlayable string `protobuf:"bytes,26,opt,name=easy_playable,json=easyPlayable,proto3" json:"easy_playable,omitempty"`
	// 微信应用扩展信息
	WechatExt *BidResponse_SeatBid_Bid_Adm_WechatExt `protobuf:"bytes,27,opt,name=wechat_ext,json=wechatExt,proto3" json:"wechat_ext,omitempty"`
	// 副标题
	SubTitle string `protobuf:"bytes,28,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// 定制榜单信息
	CustomList *BidResponse_SeatBid_Bid_Adm_CustomList `protobuf:"bytes,29,opt,name=custom_list,json=customList,proto3" json:"custom_list,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Adm) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetLandingSite() string {
	if x != nil {
		return x.LandingSite
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetAppPackage() string {
	if x != nil {
		return x.AppPackage
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetImg() []*BidResponse_SeatBid_Bid_Adm_Img {
	if x != nil {
		return x.Img
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetAdvertiserId() int64 {
	if x != nil {
		return x.AdvertiserId
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm) GetCategory() int64 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm) GetVideo() *BidResponse_SeatBid_Bid_Adm_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetRenderSite() string {
	if x != nil {
		return x.RenderSite
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetSourceAvatar() string {
	if x != nil {
		return x.SourceAvatar
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetPackageChannel() int32 {
	if x != nil {
		return x.PackageChannel
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm) GetShowUrl() string {
	if x != nil {
		return x.ShowUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetSubchain() []*BidResponse_SeatBid_Bid_Adm_Subchain {
	if x != nil {
		return x.Subchain
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetInteract() *BidResponse_SeatBid_Bid_Adm_Interact {
	if x != nil {
		return x.Interact
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetMiniProgram() *BidResponse_SeatBid_Bid_Adm_MiniProgram {
	if x != nil {
		return x.MiniProgram
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetQuickLink() string {
	if x != nil {
		return x.QuickLink
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetAppInfo() *BidResponse_SeatBid_Bid_Adm_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetStyleId() string {
	if x != nil {
		return x.StyleId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetEasyPlayable() string {
	if x != nil {
		return x.EasyPlayable
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetWechatExt() *BidResponse_SeatBid_Bid_Adm_WechatExt {
	if x != nil {
		return x.WechatExt
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm) GetSubTitle() string {
	if x != nil {
		return x.SubTitle
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm) GetCustomList() *BidResponse_SeatBid_Bid_Adm_CustomList {
	if x != nil {
		return x.CustomList
	}
	return nil
}

// 联合出价响应
type BidResponse_SeatBid_Bid_CoBiddingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 上限类型：0-倍数，1-绝对值
	UpBoundType int32 `protobuf:"varint,2,opt,name=up_bound_type,json=upBoundType,proto3" json:"up_bound_type,omitempty"`
	// 上限值
	UpBound float32 `protobuf:"fixed32,3,opt,name=up_bound,json=upBound,proto3" json:"up_bound,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_CoBiddingResp) Reset() {
	*x = BidResponse_SeatBid_Bid_CoBiddingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_CoBiddingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_CoBiddingResp) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_CoBiddingResp) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_CoBiddingResp.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_CoBiddingResp) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *BidResponse_SeatBid_Bid_CoBiddingResp) GetUpBoundType() int32 {
	if x != nil {
		return x.UpBoundType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_CoBiddingResp) GetUpBound() float32 {
	if x != nil {
		return x.UpBound
	}
	return 0
}

//图片素材
type BidResponse_SeatBid_Bid_Adm_Img struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//宽
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//高
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	//图片地址
	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	// 图片标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 跳转链接
	LinkUrl string `protobuf:"bytes,5,opt,name=link_url,json=linkUrl,proto3" json:"link_url,omitempty"`
	// deepLink跳转链接
	Deeplink string `protobuf:"bytes,6,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// 图片素材曝光监测地址
	ImpTrackers []string `protobuf:"bytes,7,rep,name=imp_trackers,json=impTrackers,proto3" json:"imp_trackers,omitempty"`
	// 图片素材点击监测地址
	ClkTrackers []string `protobuf:"bytes,8,rep,name=clk_trackers,json=clkTrackers,proto3" json:"clk_trackers,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_Img{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_Img) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_Img) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_Img.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_Img) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetLinkUrl() string {
	if x != nil {
		return x.LinkUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetImpTrackers() []string {
	if x != nil {
		return x.ImpTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_Img) GetClkTrackers() []string {
	if x != nil {
		return x.ClkTrackers
	}
	return nil
}

//视频素材
type BidResponse_SeatBid_Bid_Adm_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//宽
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//高
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	//视频素材的播放时长，单位：秒
	Duration int32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	//视频文件的码率
	Bitrate int32 `protobuf:"varint,4,opt,name=bitrate,proto3" json:"bitrate,omitempty"`
	//视频文件的url
	VideoUrl string `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	//视频文件的大小，单位Byte
	Size int32 `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	//视频素材类型，以MIME类型表示，当前仅支持"video/mp4"
	Type string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_Video) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_Video) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_Video) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 1}
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetBitrate() int32 {
	if x != nil {
		return x.Bitrate
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_Video) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 子链对象
type BidResponse_SeatBid_Bid_Adm_Subchain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文本
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 链接
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// deepLink跳转链接
	Deeplink string `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// 子链物料曝光监测地址
	ImpTrackers []string `protobuf:"bytes,4,rep,name=imp_trackers,json=impTrackers,proto3" json:"imp_trackers,omitempty"`
	// 子链物料点击监测地址
	ClkTrackers []string `protobuf:"bytes,5,rep,name=clk_trackers,json=clkTrackers,proto3" json:"clk_trackers,omitempty"`
	// 热标文案url
	HotText string `protobuf:"bytes,6,opt,name=hotText,proto3" json:"hotText,omitempty"`
	// 打开方式,1：h5,4：dp
	ActionType int32 `protobuf:"varint,7,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_Subchain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_Subchain) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_Subchain.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_Subchain) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 2}
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetImpTrackers() []string {
	if x != nil {
		return x.ImpTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetClkTrackers() []string {
	if x != nil {
		return x.ClkTrackers
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetHotText() string {
	if x != nil {
		return x.HotText
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Subchain) GetActionType() int32 {
	if x != nil {
		return x.ActionType
	}
	return 0
}

// 互动广告相关数据
type BidResponse_SeatBid_Bid_Adm_Interact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 互动链接
	InteractUrl string `protobuf:"bytes,1,opt,name=interact_url,json=interactUrl,proto3" json:"interact_url,omitempty"`
	//  1 = 横屏 2 = 竖屏
	ScreenOrientation int32 `protobuf:"varint,2,opt,name=screen_orientation,json=screenOrientation,proto3" json:"screen_orientation,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_Interact) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_Interact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_Interact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_Interact) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_Interact) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_Interact.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_Interact) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 3}
}

func (x *BidResponse_SeatBid_Bid_Adm_Interact) GetInteractUrl() string {
	if x != nil {
		return x.InteractUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_Interact) GetScreenOrientation() int32 {
	if x != nil {
		return x.ScreenOrientation
	}
	return 0
}

// 微信小程序sdk调起
type BidResponse_SeatBid_Bid_Adm_MiniProgram struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 小程序原始ID
	OriginId string `protobuf:"bytes,1,opt,name=origin_id,json=originId,proto3" json:"origin_id,omitempty"`
	// 拉起小程序页面的可带参数路径
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_MiniProgram) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_MiniProgram{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_MiniProgram) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_MiniProgram) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_MiniProgram) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_MiniProgram.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_MiniProgram) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 4}
}

func (x *BidResponse_SeatBid_Bid_Adm_MiniProgram) GetOriginId() string {
	if x != nil {
		return x.OriginId
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_MiniProgram) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

// 应用信息
type BidResponse_SeatBid_Bid_Adm_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon_url
	IconUrl string `protobuf:"bytes,1,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// 下载地址
	DownloadUrl string `protobuf:"bytes,2,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 版本号 例如 1.1.30
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// 开发者名称
	Developer string `protobuf:"bytes,4,opt,name=developer,proto3" json:"developer,omitempty"`
	// 权限信息
	Permission string `protobuf:"bytes,5,opt,name=permission,proto3" json:"permission,omitempty"`
	// 隐私协议访问地址
	PrivacyPolicyUrl string `protobuf:"bytes,6,opt,name=privacy_policy_url,json=privacyPolicyUrl,proto3" json:"privacy_policy_url,omitempty"`
	// 名称
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_AppInfo) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_AppInfo) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 5}
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetPrivacyPolicyUrl() string {
	if x != nil {
		return x.PrivacyPolicyUrl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_AppInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 微信应用扩展信息
type BidResponse_SeatBid_Bid_Adm_WechatExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 微信小游戏扩展data
	WechatExtData string `protobuf:"bytes,1,opt,name=wechat_ext_data,json=wechatExtData,proto3" json:"wechat_ext_data,omitempty"`
	// 微信原生页扩展info
	WechatExtInfo string `protobuf:"bytes,2,opt,name=wechat_ext_info,json=wechatExtInfo,proto3" json:"wechat_ext_info,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_WechatExt) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_WechatExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_WechatExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_WechatExt) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_WechatExt) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_WechatExt.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_WechatExt) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 6}
}

func (x *BidResponse_SeatBid_Bid_Adm_WechatExt) GetWechatExtData() string {
	if x != nil {
		return x.WechatExtData
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_WechatExt) GetWechatExtInfo() string {
	if x != nil {
		return x.WechatExtInfo
	}
	return ""
}

type BidResponse_SeatBid_Bid_Adm_CustomList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 页面副标题颜色
	SubTitleColor string `protobuf:"bytes,1,opt,name=sub_title_color,json=subTitleColor,proto3" json:"sub_title_color,omitempty"`
	// 内容类型，1：资讯类；2：电商类
	ContentType int32 `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// 背景图
	BgPic *BidResponse_SeatBid_Bid_Adm_Img `protobuf:"bytes,3,opt,name=bg_pic,json=bgPic,proto3" json:"bg_pic,omitempty"`
	// 背景图大小
	BgSize int32 `protobuf:"varint,4,opt,name=bg_size,json=bgSize,proto3" json:"bg_size,omitempty"`
	// 词条信息集合
	Entries []*BidResponse_SeatBid_Bid_Adm_EntryInfo `protobuf:"bytes,5,rep,name=entries,proto3" json:"entries,omitempty"`
	// 按钮描述
	BtnDesc string `protobuf:"bytes,6,opt,name=btn_desc,json=btnDesc,proto3" json:"btn_desc,omitempty"`
	// 按钮logo
	BtnLogo *BidResponse_SeatBid_Bid_Adm_Img `protobuf:"bytes,7,opt,name=btn_logo,json=btnLogo,proto3" json:"btn_logo,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_CustomList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_CustomList) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_CustomList.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_CustomList) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 7}
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetSubTitleColor() string {
	if x != nil {
		return x.SubTitleColor
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetBgPic() *BidResponse_SeatBid_Bid_Adm_Img {
	if x != nil {
		return x.BgPic
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetBgSize() int32 {
	if x != nil {
		return x.BgSize
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetEntries() []*BidResponse_SeatBid_Bid_Adm_EntryInfo {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetBtnDesc() string {
	if x != nil {
		return x.BtnDesc
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_CustomList) GetBtnLogo() *BidResponse_SeatBid_Bid_Adm_Img {
	if x != nil {
		return x.BtnLogo
	}
	return nil
}

type BidResponse_SeatBid_Bid_Adm_EntryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 词条标题
	EntryTitle string `protobuf:"bytes,1,opt,name=entry_title,json=entryTitle,proto3" json:"entry_title,omitempty"`
	// 词条副标题
	EntrySubTitle      string `protobuf:"bytes,2,opt,name=entry_sub_title,json=entrySubTitle,proto3" json:"entry_sub_title,omitempty"`
	EntrySubTitleColor string `protobuf:"bytes,3,opt,name=entrySubTitleColor,proto3" json:"entrySubTitleColor,omitempty"`
	// 词条图片
	EntryPic *BidResponse_SeatBid_Bid_Adm_Img `protobuf:"bytes,4,opt,name=entry_pic,json=entryPic,proto3" json:"entry_pic,omitempty"`
	// 词条图片大小
	EntryPicSize int32 `protobuf:"varint,5,opt,name=entry_pic_size,json=entryPicSize,proto3" json:"entry_pic_size,omitempty"`
	// 激励图标
	IncentiveIcons []*BidResponse_SeatBid_Bid_Adm_Img `protobuf:"bytes,6,rep,name=incentive_icons,json=incentiveIcons,proto3" json:"incentive_icons,omitempty"`
	// 激励文案信息
	IncentiveInfos []*BidResponse_SeatBid_Bid_Adm_IncentiveInfo `protobuf:"bytes,7,rep,name=incentive_infos,json=incentiveInfos,proto3" json:"incentive_infos,omitempty"`
	// 词条deeplink跳转链接
	Deeplink string `protobuf:"bytes,8,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// 词条h5跳转url
	LandingSite string `protobuf:"bytes,9,opt,name=landing_site,json=landingSite,proto3" json:"landing_site,omitempty"`
	// 词条热标url
	HotText string `protobuf:"bytes,10,opt,name=hot_text,json=hotText,proto3" json:"hot_text,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_EntryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_EntryInfo) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_EntryInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_EntryInfo) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 8}
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetEntryTitle() string {
	if x != nil {
		return x.EntryTitle
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetEntrySubTitle() string {
	if x != nil {
		return x.EntrySubTitle
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetEntrySubTitleColor() string {
	if x != nil {
		return x.EntrySubTitleColor
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetEntryPic() *BidResponse_SeatBid_Bid_Adm_Img {
	if x != nil {
		return x.EntryPic
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetEntryPicSize() int32 {
	if x != nil {
		return x.EntryPicSize
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetIncentiveIcons() []*BidResponse_SeatBid_Bid_Adm_Img {
	if x != nil {
		return x.IncentiveIcons
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetIncentiveInfos() []*BidResponse_SeatBid_Bid_Adm_IncentiveInfo {
	if x != nil {
		return x.IncentiveInfos
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetLandingSite() string {
	if x != nil {
		return x.LandingSite
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_EntryInfo) GetHotText() string {
	if x != nil {
		return x.HotText
	}
	return ""
}

type BidResponse_SeatBid_Bid_Adm_IncentiveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 激励文案
	IncentiveText string `protobuf:"bytes,1,opt,name=incentive_text,json=incentiveText,proto3" json:"incentive_text,omitempty"`
	// 激励文案色值
	Color string `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Adm_IncentiveInfo) Reset() {
	*x = BidResponse_SeatBid_Bid_Adm_IncentiveInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vivo_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Adm_IncentiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Adm_IncentiveInfo) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Adm_IncentiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vivo_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Adm_IncentiveInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Adm_IncentiveInfo) Descriptor() ([]byte, []int) {
	return file_vivo_proto_rawDescGZIP(), []int{1, 0, 0, 0, 9}
}

func (x *BidResponse_SeatBid_Bid_Adm_IncentiveInfo) GetIncentiveText() string {
	if x != nil {
		return x.IncentiveText
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Adm_IncentiveInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

var File_vivo_proto protoreflect.FileDescriptor

var file_vivo_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x76, 0x69,
	0x76, 0x6f, 0x22, 0xa5, 0x17, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x76, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x69,
	0x6d, 0x70, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x63, 0x61, 0x74, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x62, 0x63, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62,
	0x61, 0x64, 0x76, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x62, 0x61, 0x64, 0x76, 0x12,
	0x26, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76,
	0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47,
	0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62,
	0x63, 0x61, 0x74, 0x5f, 0x76, 0x32, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x62, 0x63,
	0x61, 0x74, 0x56, 0x32, 0x12, 0x32, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x76, 0x72, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x76, 0x72, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x43, 0x76, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x72, 0x6f, 0x77, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x6f, 0x77, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x6f, 0x77, 0x64, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x72,
	0x6f, 0x77, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x66, 0x72, 0x65, 0x71, 0x5f, 0x68, 0x69, 0x73,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x48, 0x69, 0x73,
	0x52, 0x07, 0x66, 0x72, 0x65, 0x71, 0x48, 0x69, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x5f, 0x6d, 0x73, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x69, 0x76,
	0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x4d, 0x73, 0x67, 0x52, 0x0c, 0x77, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x71, 0x4d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x44, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a, 0xdf, 0x08, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x03, 0x64, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6d,
	0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x68, 0x35, 0x5f, 0x64, 0x6c, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x68, 0x35, 0x44, 0x6c, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x64, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x61, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x70,
	0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x11, 0x63,
	0x6f, 0x5f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0f, 0x63, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x51, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x51,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73, 0x74,
	0x79, 0x6c, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x53, 0x74,
	0x79, 0x6c, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6b, 0x67, 0x5f, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6b, 0x67,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x0e, 0x69, 0x6e, 0x63,
	0x72, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x14, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x49,
	0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0d, 0x69, 0x6e,
	0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x6b, 0x67, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x70, 0x6b, 0x67, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6b, 0x67, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x61, 0x73, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x61, 0x73, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x70, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x69, 0x6d, 0x70, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x1e, 0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x1a, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x42, 0x69, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x78, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x70, 0x49, 0x64,
	0x1a, 0x37, 0x0a, 0x0d, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x6b, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x70, 0x6b, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x1a, 0x3d, 0x0a, 0x0f, 0x49, 0x6d, 0x70,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x92, 0x03, 0x0a, 0x06, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x6f, 0x73, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12,
	0x19, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x69,
	0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x64,
	0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x70, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x17,
	0x0a, 0x07, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61,
	0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x29, 0x0a,
	0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x1a, 0x3c, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x30, 0x0a, 0x03, 0x43, 0x76, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x63, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x03, 0x63, 0x76, 0x72, 0x1a, 0x44, 0x0a, 0x06, 0x41, 0x70, 0x70, 0x43,
	0x76, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x6b, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x70, 0x6b, 0x67, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x76, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x76, 0x72, 0x52, 0x04, 0x63, 0x76, 0x72, 0x73, 0x1a, 0x40,
	0x0a, 0x07, 0x46, 0x72, 0x65, 0x71, 0x48, 0x69, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x6b, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6b, 0x67, 0x73,
	0x1a, 0x7c, 0x0a, 0x0c, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x4d, 0x73, 0x67,
	0x12, 0x2c, 0x0a, 0x12, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x77, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x56, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x5f, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x1a, 0x3a,
	0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9f, 0x22, 0x0a, 0x0b, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x65, 0x61,
	0x74, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69,
	0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x62, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x62,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x78, 0x70, 0x1a, 0xf6, 0x20, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12,
	0x2f, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76,
	0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x1a, 0xb9, 0x20, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x6c, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x03, 0x61, 0x64, 0x6d,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x52, 0x03, 0x61, 0x64, 0x6d, 0x12, 0x30,
	0x0a, 0x14, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73,
	0x12, 0x36, 0x0a, 0x17, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x15, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x76, 0x69, 0x65, 0x77,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x76, 0x69, 0x65, 0x77,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x63, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x6f, 0x77, 0x64,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x16, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0f, 0x63, 0x72, 0x6f, 0x77, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x70, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x53, 0x0a, 0x0f, 0x63, 0x6f, 0x5f, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74,
	0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1b, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0d, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x6b, 0x67, 0x49, 0x64, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x6c, 0x61, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x73, 0x74, 0x72, 0x6f, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x1a, 0xe4, 0x18, 0x0a, 0x03, 0x41, 0x64, 0x6d, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x03,
	0x69, 0x6d, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x69, 0x76, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x67,
	0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52,
	0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x69, 0x74, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x68, 0x6f, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x46, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74,
	0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x53, 0x75, 0x62, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x52, 0x08, 0x73, 0x75, 0x62, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x46,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e,
	0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x12, 0x50, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76,
	0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e,
	0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x69, 0x63,
	0x6b, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75,
	0x69, 0x63, 0x6b, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x44, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x69, 0x76, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x61, 0x73, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x61, 0x73, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x45, 0x78, 0x74, 0x52, 0x09, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x45, 0x78, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4d, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xc6, 0x01, 0x0a, 0x03,
	0x49, 0x6d, 0x67, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x55,
	0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6b, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x1a, 0x9e, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0c,
	0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xcd, 0x01, 0x0a, 0x08, 0x53, 0x75, 0x62, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6b, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6c, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x6f,
	0x74, 0x54, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x74,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x5c, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x55, 0x72, 0x6c, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x3e, 0x0a, 0x0b, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x1a, 0xe1, 0x01, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x5b, 0x0a, 0x09, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x45, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x65,
	0x78, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x45, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f,
	0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x45, 0x78, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xd2, 0x02, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c,
	0x0a, 0x06, 0x62, 0x67, 0x5f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64,
	0x6d, 0x2e, 0x49, 0x6d, 0x67, 0x52, 0x05, 0x62, 0x67, 0x50, 0x69, 0x63, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62,
	0x67, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x45, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x74, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x74, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x40, 0x0a, 0x08, 0x62, 0x74, 0x6e, 0x5f, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x69, 0x76, 0x6f,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x67,
	0x52, 0x07, 0x62, 0x74, 0x6e, 0x4c, 0x6f, 0x67, 0x6f, 0x1a, 0xf2, 0x03, 0x0a, 0x09, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x2e, 0x0a, 0x12, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x53, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x42, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x67, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x50, 0x69, 0x63, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x69,
	0x63, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x69, 0x63, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x69, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x67, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x65,
	0x6e, 0x74, 0x69, 0x76, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x58, 0x0a, 0x0f, 0x69, 0x6e,
	0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x69, 0x76, 0x6f, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x69, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x69, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x74, 0x54, 0x65, 0x78, 0x74, 0x1a, 0x4c,
	0x0a, 0x0d, 0x49, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0x4e, 0x0a, 0x0d,
	0x43, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x22, 0x0a,
	0x0d, 0x75, 0x70, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x70, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x70, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x07, 0x75, 0x70, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x16, 0x42, 0x0b,
	0x56, 0x69, 0x76, 0x6f, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x07, 0x2e, 0x2e, 0x2f,
	0x76, 0x69, 0x76, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_vivo_proto_rawDescOnce sync.Once
	file_vivo_proto_rawDescData = file_vivo_proto_rawDesc
)

func file_vivo_proto_rawDescGZIP() []byte {
	file_vivo_proto_rawDescOnce.Do(func() {
		file_vivo_proto_rawDescData = protoimpl.X.CompressGZIP(file_vivo_proto_rawDescData)
	})
	return file_vivo_proto_rawDescData
}

var file_vivo_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_vivo_proto_goTypes = []interface{}{
	(*BidRequest)(nil),                // 0: vivo.BidRequest
	(*BidResponse)(nil),               // 1: vivo.BidResponse
	(*BidRequest_Impression)(nil),     // 2: vivo.BidRequest.Impression
	(*BidRequest_Device)(nil),         // 3: vivo.BidRequest.Device
	(*BidRequest_Geo)(nil),            // 4: vivo.BidRequest.Geo
	(*BidRequest_App)(nil),            // 5: vivo.BidRequest.App
	(*BidRequest_Cvr)(nil),            // 6: vivo.BidRequest.Cvr
	(*BidRequest_AppCvr)(nil),         // 7: vivo.BidRequest.AppCvr
	(*BidRequest_FreqHis)(nil),        // 8: vivo.BidRequest.FreqHis
	(*BidRequest_WechatReqMsg)(nil),   // 9: vivo.BidRequest.WechatReqMsg
	nil,                               // 10: vivo.BidRequest.ContextEntry
	nil,                               // 11: vivo.BidRequest.UserContextEntry
	(*BidRequest_Impression_Pmp)(nil), // 12: vivo.BidRequest.Impression.Pmp
	(*BidRequest_Impression_CoBiddingConfig)(nil), // 13: vivo.BidRequest.Impression.CoBiddingConfig
	(*BidRequest_Impression_IncreaseRatio)(nil),   // 14: vivo.BidRequest.Impression.IncreaseRatio
	nil,                                 // 15: vivo.BidRequest.Impression.ImpContextEntry
	(*BidResponse_SeatBid)(nil),         // 16: vivo.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),     // 17: vivo.BidResponse.SeatBid.Bid
	(*BidResponse_SeatBid_Bid_Adm)(nil), // 18: vivo.BidResponse.SeatBid.Bid.Adm
	(*BidResponse_SeatBid_Bid_CoBiddingResp)(nil),     // 19: vivo.BidResponse.SeatBid.Bid.CoBiddingResp
	(*BidResponse_SeatBid_Bid_Adm_Img)(nil),           // 20: vivo.BidResponse.SeatBid.Bid.Adm.Img
	(*BidResponse_SeatBid_Bid_Adm_Video)(nil),         // 21: vivo.BidResponse.SeatBid.Bid.Adm.Video
	(*BidResponse_SeatBid_Bid_Adm_Subchain)(nil),      // 22: vivo.BidResponse.SeatBid.Bid.Adm.Subchain
	(*BidResponse_SeatBid_Bid_Adm_Interact)(nil),      // 23: vivo.BidResponse.SeatBid.Bid.Adm.Interact
	(*BidResponse_SeatBid_Bid_Adm_MiniProgram)(nil),   // 24: vivo.BidResponse.SeatBid.Bid.Adm.MiniProgram
	(*BidResponse_SeatBid_Bid_Adm_AppInfo)(nil),       // 25: vivo.BidResponse.SeatBid.Bid.Adm.AppInfo
	(*BidResponse_SeatBid_Bid_Adm_WechatExt)(nil),     // 26: vivo.BidResponse.SeatBid.Bid.Adm.WechatExt
	(*BidResponse_SeatBid_Bid_Adm_CustomList)(nil),    // 27: vivo.BidResponse.SeatBid.Bid.Adm.CustomList
	(*BidResponse_SeatBid_Bid_Adm_EntryInfo)(nil),     // 28: vivo.BidResponse.SeatBid.Bid.Adm.EntryInfo
	(*BidResponse_SeatBid_Bid_Adm_IncentiveInfo)(nil), // 29: vivo.BidResponse.SeatBid.Bid.Adm.IncentiveInfo
}
var file_vivo_proto_depIdxs = []int32{
	2,  // 0: vivo.BidRequest.imp:type_name -> vivo.BidRequest.Impression
	3,  // 1: vivo.BidRequest.device:type_name -> vivo.BidRequest.Device
	4,  // 2: vivo.BidRequest.geo:type_name -> vivo.BidRequest.Geo
	5,  // 3: vivo.BidRequest.app:type_name -> vivo.BidRequest.App
	7,  // 4: vivo.BidRequest.app_cvrs:type_name -> vivo.BidRequest.AppCvr
	8,  // 5: vivo.BidRequest.freq_his:type_name -> vivo.BidRequest.FreqHis
	9,  // 6: vivo.BidRequest.wechat_req_msg:type_name -> vivo.BidRequest.WechatReqMsg
	10, // 7: vivo.BidRequest.context:type_name -> vivo.BidRequest.ContextEntry
	11, // 8: vivo.BidRequest.user_context:type_name -> vivo.BidRequest.UserContextEntry
	16, // 9: vivo.BidResponse.seat_bid:type_name -> vivo.BidResponse.SeatBid
	12, // 10: vivo.BidRequest.Impression.pmp:type_name -> vivo.BidRequest.Impression.Pmp
	13, // 11: vivo.BidRequest.Impression.co_bidding_config:type_name -> vivo.BidRequest.Impression.CoBiddingConfig
	14, // 12: vivo.BidRequest.Impression.increase_ratio:type_name -> vivo.BidRequest.Impression.IncreaseRatio
	15, // 13: vivo.BidRequest.Impression.imp_context:type_name -> vivo.BidRequest.Impression.ImpContextEntry
	6,  // 14: vivo.BidRequest.AppCvr.cvrs:type_name -> vivo.BidRequest.Cvr
	17, // 15: vivo.BidResponse.SeatBid.bid:type_name -> vivo.BidResponse.SeatBid.Bid
	18, // 16: vivo.BidResponse.SeatBid.Bid.adm:type_name -> vivo.BidResponse.SeatBid.Bid.Adm
	19, // 17: vivo.BidResponse.SeatBid.Bid.co_bidding_resp:type_name -> vivo.BidResponse.SeatBid.Bid.CoBiddingResp
	20, // 18: vivo.BidResponse.SeatBid.Bid.Adm.img:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Img
	21, // 19: vivo.BidResponse.SeatBid.Bid.Adm.video:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Video
	22, // 20: vivo.BidResponse.SeatBid.Bid.Adm.subchain:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Subchain
	23, // 21: vivo.BidResponse.SeatBid.Bid.Adm.interact:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Interact
	24, // 22: vivo.BidResponse.SeatBid.Bid.Adm.mini_program:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.MiniProgram
	25, // 23: vivo.BidResponse.SeatBid.Bid.Adm.app_info:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.AppInfo
	26, // 24: vivo.BidResponse.SeatBid.Bid.Adm.wechat_ext:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.WechatExt
	27, // 25: vivo.BidResponse.SeatBid.Bid.Adm.custom_list:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.CustomList
	20, // 26: vivo.BidResponse.SeatBid.Bid.Adm.CustomList.bg_pic:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Img
	28, // 27: vivo.BidResponse.SeatBid.Bid.Adm.CustomList.entries:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.EntryInfo
	20, // 28: vivo.BidResponse.SeatBid.Bid.Adm.CustomList.btn_logo:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Img
	20, // 29: vivo.BidResponse.SeatBid.Bid.Adm.EntryInfo.entry_pic:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Img
	20, // 30: vivo.BidResponse.SeatBid.Bid.Adm.EntryInfo.incentive_icons:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.Img
	29, // 31: vivo.BidResponse.SeatBid.Bid.Adm.EntryInfo.incentive_infos:type_name -> vivo.BidResponse.SeatBid.Bid.Adm.IncentiveInfo
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_vivo_proto_init() }
func file_vivo_proto_init() {
	if File_vivo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_vivo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Impression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Cvr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AppCvr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_FreqHis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_WechatReqMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Impression_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Impression_CoBiddingConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Impression_IncreaseRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_CoBiddingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_Img); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_Subchain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_Interact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_MiniProgram); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_WechatExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_CustomList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_EntryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vivo_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Adm_IncentiveInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_vivo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_vivo_proto_goTypes,
		DependencyIndexes: file_vivo_proto_depIdxs,
		MessageInfos:      file_vivo_proto_msgTypes,
	}.Build()
	File_vivo_proto = out.File
	file_vivo_proto_rawDesc = nil
	file_vivo_proto_goTypes = nil
	file_vivo_proto_depIdxs = nil
}
