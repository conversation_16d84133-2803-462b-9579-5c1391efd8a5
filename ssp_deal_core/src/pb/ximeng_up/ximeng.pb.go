// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: ximeng.proto

package ximeng_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidResponse_Bid_Adm_OptimizationGoal int32

const (
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_UNSPECIFIED     BidResponse_Bid_Adm_OptimizationGoal = 0 // 未指定
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_LEAD_GENERATION BidResponse_Bid_Adm_OptimizationGoal = 1 // 线索类(表单or线索提交)
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_APP_INSTALL     BidResponse_Bid_Adm_OptimizationGoal = 2 // 拉新, 应用下载
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_APP_ENGAGEMENT  BidResponse_Bid_Adm_OptimizationGoal = 3 // 拉活, 应用使用度提升
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_BRAND_AWARENESS BidResponse_Bid_Adm_OptimizationGoal = 4 // 品牌曝光
	BidResponse_Bid_Adm_OPTIMIZATION_GOAL_MINI_PROGRAM    BidResponse_Bid_Adm_OptimizationGoal = 5 // 小程序
)

// Enum value maps for BidResponse_Bid_Adm_OptimizationGoal.
var (
	BidResponse_Bid_Adm_OptimizationGoal_name = map[int32]string{
		0: "OPTIMIZATION_GOAL_UNSPECIFIED",
		1: "OPTIMIZATION_GOAL_LEAD_GENERATION",
		2: "OPTIMIZATION_GOAL_APP_INSTALL",
		3: "OPTIMIZATION_GOAL_APP_ENGAGEMENT",
		4: "OPTIMIZATION_GOAL_BRAND_AWARENESS",
		5: "OPTIMIZATION_GOAL_MINI_PROGRAM",
	}
	BidResponse_Bid_Adm_OptimizationGoal_value = map[string]int32{
		"OPTIMIZATION_GOAL_UNSPECIFIED":     0,
		"OPTIMIZATION_GOAL_LEAD_GENERATION": 1,
		"OPTIMIZATION_GOAL_APP_INSTALL":     2,
		"OPTIMIZATION_GOAL_APP_ENGAGEMENT":  3,
		"OPTIMIZATION_GOAL_BRAND_AWARENESS": 4,
		"OPTIMIZATION_GOAL_MINI_PROGRAM":    5,
	}
)

func (x BidResponse_Bid_Adm_OptimizationGoal) Enum() *BidResponse_Bid_Adm_OptimizationGoal {
	p := new(BidResponse_Bid_Adm_OptimizationGoal)
	*p = x
	return p
}

func (x BidResponse_Bid_Adm_OptimizationGoal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_Adm_OptimizationGoal) Descriptor() protoreflect.EnumDescriptor {
	return file_ximeng_proto_enumTypes[0].Descriptor()
}

func (BidResponse_Bid_Adm_OptimizationGoal) Type() protoreflect.EnumType {
	return &file_ximeng_proto_enumTypes[0]
}

func (x BidResponse_Bid_Adm_OptimizationGoal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Bid_Adm_OptimizationGoal.Descriptor instead.
func (BidResponse_Bid_Adm_OptimizationGoal) EnumDescriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 0}
}

type BidResponse_Bid_Adm_ActionType int32

const (
	BidResponse_Bid_Adm_ACTION_TYPE_UNSPECIFIED     BidResponse_Bid_Adm_ActionType = 0 // 未指定
	BidResponse_Bid_Adm_ACTION_TYPE_LANDING_PAGE    BidResponse_Bid_Adm_ActionType = 1 // 打开落地页
	BidResponse_Bid_Adm_ACTION_TYPE_DEEP_LINK       BidResponse_Bid_Adm_ActionType = 2 // deep_link [+ 落地页]
	BidResponse_Bid_Adm_ACTION_TYPE_ANDROID_INSTALL BidResponse_Bid_Adm_ActionType = 3 // android下载安装
	BidResponse_Bid_Adm_ACTION_TYPE_IOS_INSTALL     BidResponse_Bid_Adm_ActionType = 4 // 应用商店安装
	BidResponse_Bid_Adm_ACTION_TYPE_MINI_PROGRAM    BidResponse_Bid_Adm_ActionType = 5 //小程序
)

// Enum value maps for BidResponse_Bid_Adm_ActionType.
var (
	BidResponse_Bid_Adm_ActionType_name = map[int32]string{
		0: "ACTION_TYPE_UNSPECIFIED",
		1: "ACTION_TYPE_LANDING_PAGE",
		2: "ACTION_TYPE_DEEP_LINK",
		3: "ACTION_TYPE_ANDROID_INSTALL",
		4: "ACTION_TYPE_IOS_INSTALL",
		5: "ACTION_TYPE_MINI_PROGRAM",
	}
	BidResponse_Bid_Adm_ActionType_value = map[string]int32{
		"ACTION_TYPE_UNSPECIFIED":     0,
		"ACTION_TYPE_LANDING_PAGE":    1,
		"ACTION_TYPE_DEEP_LINK":       2,
		"ACTION_TYPE_ANDROID_INSTALL": 3,
		"ACTION_TYPE_IOS_INSTALL":     4,
		"ACTION_TYPE_MINI_PROGRAM":    5,
	}
)

func (x BidResponse_Bid_Adm_ActionType) Enum() *BidResponse_Bid_Adm_ActionType {
	p := new(BidResponse_Bid_Adm_ActionType)
	*p = x
	return p
}

func (x BidResponse_Bid_Adm_ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_Adm_ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_ximeng_proto_enumTypes[1].Descriptor()
}

func (BidResponse_Bid_Adm_ActionType) Type() protoreflect.EnumType {
	return &file_ximeng_proto_enumTypes[1]
}

func (x BidResponse_Bid_Adm_ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Bid_Adm_ActionType.Descriptor instead.
func (BidResponse_Bid_Adm_ActionType) EnumDescriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 1}
}

type BidResponse_Bid_Adm_MiniProgramReleaseType int32

const (
	BidResponse_Bid_Adm_MINI_PROGRAM_RELEASE_TYPE_UNSPECIFIED BidResponse_Bid_Adm_MiniProgramReleaseType = 0 // 未指定
	BidResponse_Bid_Adm_MINI_PROGRAM_RELEASE_TYPE_PRODUCT     BidResponse_Bid_Adm_MiniProgramReleaseType = 1 // 正式
	BidResponse_Bid_Adm_MINI_PROGRAM_RELEASE_TYPE_TEST        BidResponse_Bid_Adm_MiniProgramReleaseType = 2 // 测试
	BidResponse_Bid_Adm_MINI_PROGRAM_RELEASE_TYPE_PREVIEW     BidResponse_Bid_Adm_MiniProgramReleaseType = 3 // 预览
)

// Enum value maps for BidResponse_Bid_Adm_MiniProgramReleaseType.
var (
	BidResponse_Bid_Adm_MiniProgramReleaseType_name = map[int32]string{
		0: "MINI_PROGRAM_RELEASE_TYPE_UNSPECIFIED",
		1: "MINI_PROGRAM_RELEASE_TYPE_PRODUCT",
		2: "MINI_PROGRAM_RELEASE_TYPE_TEST",
		3: "MINI_PROGRAM_RELEASE_TYPE_PREVIEW",
	}
	BidResponse_Bid_Adm_MiniProgramReleaseType_value = map[string]int32{
		"MINI_PROGRAM_RELEASE_TYPE_UNSPECIFIED": 0,
		"MINI_PROGRAM_RELEASE_TYPE_PRODUCT":     1,
		"MINI_PROGRAM_RELEASE_TYPE_TEST":        2,
		"MINI_PROGRAM_RELEASE_TYPE_PREVIEW":     3,
	}
)

func (x BidResponse_Bid_Adm_MiniProgramReleaseType) Enum() *BidResponse_Bid_Adm_MiniProgramReleaseType {
	p := new(BidResponse_Bid_Adm_MiniProgramReleaseType)
	*p = x
	return p
}

func (x BidResponse_Bid_Adm_MiniProgramReleaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_Adm_MiniProgramReleaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_ximeng_proto_enumTypes[2].Descriptor()
}

func (BidResponse_Bid_Adm_MiniProgramReleaseType) Type() protoreflect.EnumType {
	return &file_ximeng_proto_enumTypes[2]
}

func (x BidResponse_Bid_Adm_MiniProgramReleaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BidResponse_Bid_Adm_MiniProgramReleaseType.Descriptor instead.
func (BidResponse_Bid_Adm_MiniProgramReleaseType) EnumDescriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 2}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                       // 本次请求的唯一id
	ApiVersion  string             `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`     //协议版本（目前为"v1.2"）
	RequestType int32              `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"` // 请求类型(1: 正式环境; 2: ping,测试RTT; 4: 测试广告，不计费; 8:其他)
	Imps        []*BidRequest_Imp  `protobuf:"bytes,4,rep,name=imps,proto3" json:"imps,omitempty"`                                   // 曝光机会列表
	App         *BidRequest_App    `protobuf:"bytes,5,opt,name=app,proto3" json:"app,omitempty"`                                     // APP信息
	Device      *BidRequest_Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`                               // 设备信息
	User        *BidRequest_User   `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`                                   // 用户信息
	Geo         *BidRequest_Geo    `protobuf:"bytes,8,opt,name=geo,proto3" json:"geo,omitempty"`                                     // 地理位置信息
	Timeout     uint32             `protobuf:"varint,9,opt,name=timeout,proto3" json:"timeout,omitempty"`                            // 请求预留的时间，单位为ms
	Ext         *BidRequest_Ext    `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`                                    // 扩展参数
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetRequestType() int32 {
	if x != nil {
		return x.RequestType
	}
	return 0
}

func (x *BidRequest) GetImps() []*BidRequest_Imp {
	if x != nil {
		return x.Imps
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *BidRequest) GetExt() *BidRequest_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // 请求ID，需与BidRequest.id一致
	Code     int32                  `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`                        // 广告响应状态码
	BidId    string                 `protobuf:"bytes,2,opt,name=bid_id,json=bidId,proto3" json:"bid_id,omitempty"`          // 喜马联盟侧生成的竞价ID
	SeatBids []*BidResponse_SeatBid `protobuf:"bytes,4,rep,name=seat_bids,json=seatBids,proto3" json:"seat_bids,omitempty"` //喜马联盟侧生成的竞价信息，目前只有一个
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BidResponse) GetBidId() string {
	if x != nil {
		return x.BidId
	}
	return ""
}

func (x *BidResponse) GetSeatBids() []*BidResponse_SeatBid {
	if x != nil {
		return x.SeatBids
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                              // 标识唯一一次曝光机会
	TagId    string                  `protobuf:"bytes,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`           // 广告位ID
	AdType   int32                   `protobuf:"varint,3,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`       // 广告类型（1:开屏、2：信息流、3：激励视频、4：贴片、5：banner）
	BidType  int32                   `protobuf:"varint,4,opt,name=bid_type,json=bidType,proto3" json:"bid_type,omitempty"`    // 出价类型, 0：cpm 1：cpc 当前仅支持cpm
	BidFloor int64                   `protobuf:"varint,5,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"` // 底价，分/千次曝光
	Assets   []*BidRequest_Imp_Asset `protobuf:"bytes,6,rep,name=assets,proto3" json:"assets,omitempty"`                      // 模版信息，详见模版资源映射表
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidRequest_Imp) GetBidType() int32 {
	if x != nil {
		return x.BidType
	}
	return 0
}

func (x *BidRequest_Imp) GetBidFloor() int64 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

func (x *BidRequest_Imp) GetAssets() []*BidRequest_Imp_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`       // 应用名称：例如：喜马拉雅FM
	Bundle  string `protobuf:"bytes,2,opt,name=bundle,proto3" json:"bundle,omitempty"`   // 应用程序包名：例如：com.ximalaya.iting
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"` // 应用版本号: 例如: 9.2.6
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua               string                          `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`                                                              // UA信息
	Ipv4             *string                         `protobuf:"bytes,2,opt,name=ipv4,proto3,oneof" json:"ipv4,omitempty"`                                                    // 用户设备ipv4地址
	Ipv6             *string                         `protobuf:"bytes,3,opt,name=ipv6,proto3,oneof" json:"ipv6,omitempty"`                                                    // 用户设备ipv6地址
	Carrier          int32                           `protobuf:"varint,4,opt,name=carrier,proto3" json:"carrier,omitempty"`                                                   // 运营商（0：未知；1：移动; 2：联通；3：电信; 4: 广电）
	NetworkType      int32                           `protobuf:"varint,5,opt,name=network_type,json=networkType,proto3" json:"network_type,omitempty"`                        //网络连接类型（0:未知; 1:Ethernet/Wifi；2：2G；3：3G；4：4G; 5: 5G; 6: 6G[预留], 7: 移动网络未知）
	Make             string                          `protobuf:"bytes,6,opt,name=make,proto3" json:"make,omitempty"`                                                          // 手机品牌，如：iPhone，Xiaomi
	Model            string                          `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`                                                        // 手机型号，如：iPhoneX, KNT-AL10
	Os               string                          `protobuf:"bytes,8,opt,name=os,proto3" json:"os,omitempty"`                                                              // 操作系统名称（ios、android、windows、macos、linux, 均为小写）
	Osv              string                          `protobuf:"bytes,9,opt,name=osv,proto3" json:"osv,omitempty"`                                                            //  操作系统版本
	DeviceType       int32                           `protobuf:"varint,10,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`                          // 设备类型（0：手机；1:平板; 2: 电脑; 3: other）
	ScreenHeight     *int32                          `protobuf:"varint,11,opt,name=screen_height,json=screenHeight,proto3,oneof" json:"screen_height,omitempty"`              // 屏幕的物理高度，以像素为单位
	ScreenWidth      *int32                          `protobuf:"varint,12,opt,name=screen_width,json=screenWidth,proto3,oneof" json:"screen_width,omitempty"`                 // 屏幕的物理宽度，以像素为单位
	Imei             *string                         `protobuf:"bytes,13,opt,name=imei,proto3,oneof" json:"imei,omitempty"`                                                   // Android IMEI原始值明文
	ImeiMd5          *string                         `protobuf:"bytes,14,opt,name=imei_md5,json=imeiMd5,proto3,oneof" json:"imei_md5,omitempty"`                              // IMEI MD5, hex 小写
	AndroidId        *string                         `protobuf:"bytes,15,opt,name=android_id,json=androidId,proto3,oneof" json:"android_id,omitempty"`                        // AndroidId原始值明文
	AndroidIdMd5     *string                         `protobuf:"bytes,16,opt,name=android_id_md5,json=androidIdMd5,proto3,oneof" json:"android_id_md5,omitempty"`             // AndroidId MD5, hex 小写
	Oaid             *string                         `protobuf:"bytes,17,opt,name=oaid,proto3,oneof" json:"oaid,omitempty"`                                                   // Android OAID原始值明文
	OaidMd5          *string                         `protobuf:"bytes,18,opt,name=oaid_md5,json=oaidMd5,proto3,oneof" json:"oaid_md5,omitempty"`                              // Android OAID MD5, hex 小写
	HmsVersion       *string                         `protobuf:"bytes,19,opt,name=hms_version,json=hmsVersion,proto3,oneof" json:"hms_version,omitempty"`                     // 华为机型HMS Core版本号，华为设备必填
	AgVersion        *string                         `protobuf:"bytes,20,opt,name=ag_version,json=agVersion,proto3,oneof" json:"ag_version,omitempty"`                        // 设备应用市场版本号，华为设备必填
	AgCountryCode    *string                         `protobuf:"bytes,21,opt,name=ag_country_code,json=agCountryCode,proto3,oneof" json:"ag_country_code,omitempty"`          // 应用市场中设置的国家和地区，华为设备必填
	OppoStoreVersion *string                         `protobuf:"bytes,22,opt,name=oppo_store_version,json=oppoStoreVersion,proto3,oneof" json:"oppo_store_version,omitempty"` // oppo应用商店版本号，oppo设备必填
	VivoStoreVersion *string                         `protobuf:"bytes,23,opt,name=vivo_store_version,json=vivoStoreVersion,proto3,oneof" json:"vivo_store_version,omitempty"` // vivo应用商店版本号，vivo设备必填
	MiStoreVersion   *string                         `protobuf:"bytes,24,opt,name=mi_store_version,json=miStoreVersion,proto3,oneof" json:"mi_store_version,omitempty"`       // 小米应用商店版本号，小米设备必填
	MiuiVersion      *string                         `protobuf:"bytes,25,opt,name=miui_version,json=miuiVersion,proto3,oneof" json:"miui_version,omitempty"`                  // 小米系统版本，miui系统必填
	BootTs           *string                         `protobuf:"bytes,26,opt,name=boot_ts,json=bootTs,proto3,oneof" json:"boot_ts,omitempty"`                                 // 设备启动时间 iOS: 1657043138
	UpdateTs         *string                         `protobuf:"bytes,27,opt,name=update_ts,json=updateTs,proto3,oneof" json:"update_ts,omitempty"`                           // 设备系统更新时间 iOS: 1647545826.588793
	Idfa             *string                         `protobuf:"bytes,28,opt,name=idfa,proto3,oneof" json:"idfa,omitempty"`                                                   // IDFA原始值明文
	IdfaMd5          *string                         `protobuf:"bytes,29,opt,name=idfa_md5,json=idfaMd5,proto3,oneof" json:"idfa_md5,omitempty"`                              // IDFA MD5, 小写
	Caids            []*BidRequest_Caid              `protobuf:"bytes,30,rep,name=caids,proto3" json:"caids,omitempty"`                                                       // CAID 对象数组
	CaidCriteria     *BidRequest_Device_CAIDCriteria `protobuf:"bytes,31,opt,name=caid_criteria,json=caidCriteria,proto3,oneof" json:"caid_criteria,omitempty"`               // caid相关参数
	BirthTs          *string                         `protobuf:"bytes,32,opt,name=birth_ts,json=birthTs,proto3,oneof" json:"birth_ts,omitempty"`                              // 设备初始化时间[协议v1.2新增] iOS: 1647545718.125795458
	Paid_1_3         *string                         `protobuf:"bytes,33,opt,name=paid_1_3,json=paid13,proto3,oneof" json:"paid_1_3,omitempty"`                               // PDD归因v1.3版本paid[协议v1.2新增]
	Paid_1_4         *string                         `protobuf:"bytes,34,opt,name=paid_1_4,json=paid14,proto3,oneof" json:"paid_1_4,omitempty"`                               // PDD归因v1.4版本paid[协议v1.2新增]
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIpv4() string {
	if x != nil && x.Ipv4 != nil {
		return *x.Ipv4
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetNetworkType() int32 {
	if x != nil {
		return x.NetworkType
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidIdMd5() string {
	if x != nil && x.AndroidIdMd5 != nil {
		return *x.AndroidIdMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetHmsVersion() string {
	if x != nil && x.HmsVersion != nil {
		return *x.HmsVersion
	}
	return ""
}

func (x *BidRequest_Device) GetAgVersion() string {
	if x != nil && x.AgVersion != nil {
		return *x.AgVersion
	}
	return ""
}

func (x *BidRequest_Device) GetAgCountryCode() string {
	if x != nil && x.AgCountryCode != nil {
		return *x.AgCountryCode
	}
	return ""
}

func (x *BidRequest_Device) GetOppoStoreVersion() string {
	if x != nil && x.OppoStoreVersion != nil {
		return *x.OppoStoreVersion
	}
	return ""
}

func (x *BidRequest_Device) GetVivoStoreVersion() string {
	if x != nil && x.VivoStoreVersion != nil {
		return *x.VivoStoreVersion
	}
	return ""
}

func (x *BidRequest_Device) GetMiStoreVersion() string {
	if x != nil && x.MiStoreVersion != nil {
		return *x.MiStoreVersion
	}
	return ""
}

func (x *BidRequest_Device) GetMiuiVersion() string {
	if x != nil && x.MiuiVersion != nil {
		return *x.MiuiVersion
	}
	return ""
}

func (x *BidRequest_Device) GetBootTs() string {
	if x != nil && x.BootTs != nil {
		return *x.BootTs
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateTs() string {
	if x != nil && x.UpdateTs != nil {
		return *x.UpdateTs
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetCaids() []*BidRequest_Caid {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *BidRequest_Device) GetCaidCriteria() *BidRequest_Device_CAIDCriteria {
	if x != nil {
		return x.CaidCriteria
	}
	return nil
}

func (x *BidRequest_Device) GetBirthTs() string {
	if x != nil && x.BirthTs != nil {
		return *x.BirthTs
	}
	return ""
}

func (x *BidRequest_Device) GetPaid_1_3() string {
	if x != nil && x.Paid_1_3 != nil {
		return *x.Paid_1_3
	}
	return ""
}

func (x *BidRequest_Device) GetPaid_1_4() string {
	if x != nil && x.Paid_1_4 != nil {
		return *x.Paid_1_4
	}
	return ""
}

type BidRequest_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`           // caid 的值
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"` // caid 版本
}

func (x *BidRequest_Caid) Reset() {
	*x = BidRequest_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Caid) ProtoMessage() {}

func (x *BidRequest_Caid) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Caid.ProtoReflect.Descriptor instead.
func (*BidRequest_Caid) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Caid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Caid) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                          // [保留字段] 用户唯一标识
	AppList string `protobuf:"bytes,2,opt,name=app_list,json=appList,proto3" json:"app_list,omitempty"` // 用户已安装app列表, 英文逗号分隔，如 com.xmly.ting,com.xunmeng.pinduoduo,com.jingdong.app.mall
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_User) GetAppList() string {
	if x != nil {
		return x.AppList
	}
	return ""
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` // 坐标系: 经纬度有值时必传，坐标系类型 0:GCJ-02; 1:WGS-84; 2:bd09ll
	Lat  float32 `protobuf:"fixed32,2,opt,name=lat,proto3" json:"lat,omitempty"`  // 纬度 latitude, 取值从-90 到 90，南为负值
	Lon  float32 `protobuf:"fixed32,3,opt,name=lon,proto3" json:"lon,omitempty"`  // 经度 longitude, 取值从-180 到 180，西为负值
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BidRequest_Geo) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

type BidRequest_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Extra map[string]string `protobuf:"bytes,1,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Extra自定义Map
}

func (x *BidRequest_Ext) Reset() {
	*x = BidRequest_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Ext) ProtoMessage() {}

func (x *BidRequest_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Ext.ProtoReflect.Descriptor instead.
func (*BidRequest_Ext) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_Ext) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

type BidRequest_Imp_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"` //  广告位模版ID, 详见模版资源映射表
	Width      int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`                            // 广告位模版宽度
	Height     int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`                          // 广告位模版高度
}

func (x *BidRequest_Imp_Asset) Reset() {
	*x = BidRequest_Imp_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Asset) ProtoMessage() {}

func (x *BidRequest_Imp_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Asset.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Asset) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Asset) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *BidRequest_Imp_Asset) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_Imp_Asset) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BidRequest_Device_CAIDCriteria struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaidBootSec            string `protobuf:"bytes,1,opt,name=caid_boot_sec,json=caidBootSec,proto3" json:"caid_boot_sec,omitempty"`                                    // 设备启动时间 UnixTimestamp eg: 1691052221
	CaidCountryCode        string `protobuf:"bytes,2,opt,name=caid_country_code,json=caidCountryCode,proto3" json:"caid_country_code,omitempty"`                        // 国家 eg: CN
	CaidLanguage           string `protobuf:"bytes,3,opt,name=caid_language,json=caidLanguage,proto3" json:"caid_language,omitempty"`                                   // 语言 eg: zh-Hans-CN
	CaidDeviceNameMd5      string `protobuf:"bytes,4,opt,name=caid_device_name_md5,json=caidDeviceNameMd5,proto3" json:"caid_device_name_md5,omitempty"`                // 设备名称MD5 eg: e910dddb2748c36b47fcde5dd720eec1
	CaidSystemVersion      string `protobuf:"bytes,5,opt,name=caid_system_version,json=caidSystemVersion,proto3" json:"caid_system_version,omitempty"`                  // 系统版本 eg: 14.0
	CaidHardwareMachine    string `protobuf:"bytes,6,opt,name=caid_hardware_machine,json=caidHardwareMachine,proto3" json:"caid_hardware_machine,omitempty"`            // 设备 Machine, eg: iPhone10,3
	CaidCarrierInfo        string `protobuf:"bytes,7,opt,name=caid_carrier_info,json=caidCarrierInfo,proto3" json:"caid_carrier_info,omitempty"`                        // 运营商信息 eg: 中国移动
	CaidPhysicalMemoryByte string `protobuf:"bytes,8,opt,name=caid_physical_memory_byte,json=caidPhysicalMemoryByte,proto3" json:"caid_physical_memory_byte,omitempty"` // 物理内存容量 eg: 3955589120
	CaidHarddiskSizeByte   string `protobuf:"bytes,9,opt,name=caid_harddisk_size_byte,json=caidHarddiskSizeByte,proto3" json:"caid_harddisk_size_byte,omitempty"`       // 硬盘容量 eg: 63900340224
	CaidSystemUpdateSec    string `protobuf:"bytes,10,opt,name=caid_system_update_sec,json=caidSystemUpdateSec,proto3" json:"caid_system_update_sec,omitempty"`         // 系统更新时间, 保留小数点后 6 位，如不够需补 0
	CaidHardwareModel      string `protobuf:"bytes,11,opt,name=caid_hardware_model,json=caidHardwareModel,proto3" json:"caid_hardware_model,omitempty"`                 // 设备 Model eg: D22AP
	CaidTimeZone           string `protobuf:"bytes,12,opt,name=caid_time_zone,json=caidTimeZone,proto3" json:"caid_time_zone,omitempty"`                                // 时区 eg: 28800
	CaidMntId              string `protobuf:"bytes,13,opt,name=caid_mnt_id,json=caidMntId,proto3" json:"caid_mnt_id,omitempty"`                                         // mnt_id eg: 30225948939346695D0D744027A07803D5F8410346398776C879B727@/dev/disk1s1
	CaidFileInitTime       string `protobuf:"bytes,14,opt,name=caid_file_init_time,json=caidFileInitTime,proto3" json:"caid_file_init_time,omitempty"`                  // 设备初始化时间 eg: 1632467920.301150749
}

func (x *BidRequest_Device_CAIDCriteria) Reset() {
	*x = BidRequest_Device_CAIDCriteria{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_CAIDCriteria) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_CAIDCriteria) ProtoMessage() {}

func (x *BidRequest_Device_CAIDCriteria) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_CAIDCriteria.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_CAIDCriteria) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidBootSec() string {
	if x != nil {
		return x.CaidBootSec
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidCountryCode() string {
	if x != nil {
		return x.CaidCountryCode
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidLanguage() string {
	if x != nil {
		return x.CaidLanguage
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidDeviceNameMd5() string {
	if x != nil {
		return x.CaidDeviceNameMd5
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidSystemVersion() string {
	if x != nil {
		return x.CaidSystemVersion
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidHardwareMachine() string {
	if x != nil {
		return x.CaidHardwareMachine
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidCarrierInfo() string {
	if x != nil {
		return x.CaidCarrierInfo
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidPhysicalMemoryByte() string {
	if x != nil {
		return x.CaidPhysicalMemoryByte
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidHarddiskSizeByte() string {
	if x != nil {
		return x.CaidHarddiskSizeByte
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidSystemUpdateSec() string {
	if x != nil {
		return x.CaidSystemUpdateSec
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidHardwareModel() string {
	if x != nil {
		return x.CaidHardwareModel
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidTimeZone() string {
	if x != nil {
		return x.CaidTimeZone
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidMntId() string {
	if x != nil {
		return x.CaidMntId
	}
	return ""
}

func (x *BidRequest_Device_CAIDCriteria) GetCaidFileInitTime() string {
	if x != nil {
		return x.CaidFileInitTime
	}
	return ""
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bids []*BidResponse_Bid `protobuf:"bytes,1,rep,name=bids,proto3" json:"bids,omitempty"` // DSP参与竞价的位置，与BidRequest.imp对应，每个imp最多只可返回一个bid
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBids() []*BidResponse_Bid {
	if x != nil {
		return x.Bids
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                 // dsp侧针对这次竞价的ID
	ImpId          string               `protobuf:"bytes,2,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"`                              // 曝光ID，对应BidRequest.imp.id，必填
	AdId           string               `protobuf:"bytes,3,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                                 // 广告id
	Price          int64                `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                                          // DSP出价，单位：分/千次曝光
	WinNoticeUrls  []string             `protobuf:"bytes,5,rep,name=win_notice_urls,json=winNoticeUrls,proto3" json:"win_notice_urls,omitempty"`    // 竞价成功通知地址
	LossNoticeUrls []string             `protobuf:"bytes,6,rep,name=loss_notice_urls,json=lossNoticeUrls,proto3" json:"loss_notice_urls,omitempty"` // 可选。竞价失败通知的url列表
	ImpTrackers    []string             `protobuf:"bytes,7,rep,name=imp_trackers,json=impTrackers,proto3" json:"imp_trackers,omitempty"`            // 曝光监测地址
	ClkTrackers    []string             `protobuf:"bytes,8,rep,name=clk_trackers,json=clkTrackers,proto3" json:"clk_trackers,omitempty"`            // 点击监测地址
	Adm            *BidResponse_Bid_Adm `protobuf:"bytes,9,opt,name=adm,proto3" json:"adm,omitempty"`                                               // 创意物料信息
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_Bid) GetImpId() string {
	if x != nil {
		return x.ImpId
	}
	return ""
}

func (x *BidResponse_Bid) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *BidResponse_Bid) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetWinNoticeUrls() []string {
	if x != nil {
		return x.WinNoticeUrls
	}
	return nil
}

func (x *BidResponse_Bid) GetLossNoticeUrls() []string {
	if x != nil {
		return x.LossNoticeUrls
	}
	return nil
}

func (x *BidResponse_Bid) GetImpTrackers() []string {
	if x != nil {
		return x.ImpTrackers
	}
	return nil
}

func (x *BidResponse_Bid) GetClkTrackers() []string {
	if x != nil {
		return x.ClkTrackers
	}
	return nil
}

func (x *BidResponse_Bid) GetAdm() *BidResponse_Bid_Adm {
	if x != nil {
		return x.Adm
	}
	return nil
}

// 物料信息
type BidResponse_Bid_Adm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId       string                                `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`                                                                                         // 模版ID
	CreativeId       string                                `protobuf:"bytes,2,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`                                                                                         // 可选。广告物料的唯一标识
	Title            string                                `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                                                                                                                     // 标题
	Desc             string                                `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`                                                                                                                       // 描述
	Images           []*BidResponse_Bid_Adm_Image          `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`                                                                                                                   // 图片信息
	Video            *BidResponse_Bid_Adm_Video            `protobuf:"bytes,6,opt,name=video,proto3" json:"video,omitempty"`                                                                                                                     // 视频信息
	OptimizationGoal *BidResponse_Bid_Adm_OptimizationGoal `protobuf:"varint,7,opt,name=optimization_goal,json=optimizationGoal,proto3,enum=com.ximalaya.bidding.BidResponse_Bid_Adm_OptimizationGoal,oneof" json:"optimization_goal,omitempty"` // 推广目标 : 拉新，拉活，线索表单，小程序
	ActionType       *BidResponse_Bid_Adm_ActionType       `protobuf:"varint,8,opt,name=action_type,json=actionType,proto3,enum=com.ximalaya.bidding.BidResponse_Bid_Adm_ActionType,oneof" json:"action_type,omitempty"`                         // 交互类型
	LandingPage      string                                `protobuf:"bytes,9,opt,name=landing_page,json=landingPage,proto3" json:"landing_page,omitempty"`                                                                                      // 落地页url
	DeepLink         *string                               `protobuf:"bytes,10,opt,name=deep_link,json=deepLink,proto3,oneof" json:"deep_link,omitempty"`                                                                                        // 客户端优先跳转deeplink链接,其次跳转普通网址类或应用下载类的落地页
	AppInfo          *BidResponse_Bid_Adm_AppInfo          `protobuf:"bytes,11,opt,name=app_info,json=appInfo,proto3,oneof" json:"app_info,omitempty"`                                                                                           // APP信息，用于下载类广告需要的APP信息
	DownloadMonitor  *BidResponse_Bid_Adm_DownloadMonitor  `protobuf:"bytes,12,opt,name=download_monitor,json=downloadMonitor,proto3,oneof" json:"download_monitor,omitempty"`                                                                   // 下载类广告下载监测链接
	MiniProgramInfo  *BidResponse_Bid_Adm_MiniProgramInfo  `protobuf:"bytes,13,opt,name=mini_program_info,json=miniProgramInfo,proto3,oneof" json:"mini_program_info,omitempty"`                                                                 // 微信小程序信息
}

func (x *BidResponse_Bid_Adm) Reset() {
	*x = BidResponse_Bid_Adm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm) ProtoMessage() {}

func (x *BidResponse_Bid_Adm) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *BidResponse_Bid_Adm) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetImages() []*BidResponse_Bid_Adm_Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetVideo() *BidResponse_Bid_Adm_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetOptimizationGoal() BidResponse_Bid_Adm_OptimizationGoal {
	if x != nil && x.OptimizationGoal != nil {
		return *x.OptimizationGoal
	}
	return BidResponse_Bid_Adm_OPTIMIZATION_GOAL_UNSPECIFIED
}

func (x *BidResponse_Bid_Adm) GetActionType() BidResponse_Bid_Adm_ActionType {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return BidResponse_Bid_Adm_ACTION_TYPE_UNSPECIFIED
}

func (x *BidResponse_Bid_Adm) GetLandingPage() string {
	if x != nil {
		return x.LandingPage
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetDeepLink() string {
	if x != nil && x.DeepLink != nil {
		return *x.DeepLink
	}
	return ""
}

func (x *BidResponse_Bid_Adm) GetAppInfo() *BidResponse_Bid_Adm_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetDownloadMonitor() *BidResponse_Bid_Adm_DownloadMonitor {
	if x != nil {
		return x.DownloadMonitor
	}
	return nil
}

func (x *BidResponse_Bid_Adm) GetMiniProgramInfo() *BidResponse_Bid_Adm_MiniProgramInfo {
	if x != nil {
		return x.MiniProgramInfo
	}
	return nil
}

type BidResponse_Bid_Adm_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`        // 图片URL
	Width  int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`   // 图片宽度
	Height int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"` // 图片高度
	Mime   string `protobuf:"bytes,4,opt,name=mime,proto3" json:"mime,omitempty"`      // 图片类型， 如：JPG/JPEG/PNG
}

func (x *BidResponse_Bid_Adm_Image) Reset() {
	*x = BidResponse_Bid_Adm_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_Image) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_Image) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_Image) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 0}
}

func (x *BidResponse_Bid_Adm_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *BidResponse_Bid_Adm_Image) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Image) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Image) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

type BidResponse_Bid_Adm_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"` // 视频URL
	Width    int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`                      // 视频宽度
	Height   int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`                    // 视频高度
	Duration int32  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`                // 视频素材的播放时长，单位：秒
	Size     int32  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`                        // 视频文件的大小，单位Byte
	Mime     string `protobuf:"bytes,6,opt,name=mime,proto3" json:"mime,omitempty"`                         // 视频素材类型，以MIME类型表示
	Cover    string `protobuf:"bytes,7,opt,name=cover,proto3" json:"cover,omitempty"`                       // 视频封面图
}

func (x *BidResponse_Bid_Adm_Video) Reset() {
	*x = BidResponse_Bid_Adm_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_Video) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_Video) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_Video) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 1}
}

func (x *BidResponse_Bid_Adm_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_Video) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidResponse_Bid_Adm_Video) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *BidResponse_Bid_Adm_Video) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

type BidResponse_Bid_Adm_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name               string                                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                                                  // APP中文名称，针对应用下载类广告，用于下载广告
	Desc               string                                 `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`                                                                  // APP文字描述，用于下载广告
	Version            string                                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                                                            // APP版本号，用于下载广告
	PackageName        string                                 `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                                 // APP包名，用于下载广告
	PackageSize        *string                                `protobuf:"bytes,5,opt,name=package_size,json=packageSize,proto3,oneof" json:"package_size,omitempty"`                           // APP应用包大小，用于下载广告
	IosBundleId        *int64                                 `protobuf:"varint,6,opt,name=ios_bundle_id,json=iosBundleId,proto3,oneof" json:"ios_bundle_id,omitempty"`                        // iOS下载应用ID，用于下载广告
	UpdateTime         *int64                                 `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`                             //app更新时间
	Score              *float32                               `protobuf:"fixed32,8,opt,name=score,proto3,oneof" json:"score,omitempty"`                                                        //应用评分
	IconImageUrl       *string                                `protobuf:"bytes,9,opt,name=icon_image_url,json=iconImageUrl,proto3,oneof" json:"icon_image_url,omitempty"`                      //app icon url
	IconImageWidth     *int32                                 `protobuf:"varint,10,opt,name=icon_image_width,json=iconImageWidth,proto3,oneof" json:"icon_image_width,omitempty"`              //app icon width
	IconImageHeight    *int32                                 `protobuf:"varint,11,opt,name=icon_image_height,json=iconImageHeight,proto3,oneof" json:"icon_image_height,omitempty"`           //app icon height
	DownloadUrl        *string                                `protobuf:"bytes,12,opt,name=download_url,json=downloadUrl,proto3,oneof" json:"download_url,omitempty"`                          //下载地址
	DownloadUrlExpires *int64                                 `protobuf:"varint,13,opt,name=download_url_expires,json=downloadUrlExpires,proto3,oneof" json:"download_url_expires,omitempty"`  // 下载过期时间戳,秒级
	DownloadUrlFileMd5 *string                                `protobuf:"bytes,14,opt,name=download_url_file_md5,json=downloadUrlFileMd5,proto3,oneof" json:"download_url_file_md5,omitempty"` //文件md5
	UlkUrl             *string                                `protobuf:"bytes,15,opt,name=ulk_url,json=ulkUrl,proto3,oneof" json:"ulk_url,omitempty"`                                         // universal link url, 用于iOS调起
	UlkScheme          *string                                `protobuf:"bytes,16,opt,name=ulk_scheme,json=ulkScheme,proto3,oneof" json:"ulk_scheme,omitempty"`                                // universal link scheme,用于iOS嗅探
	AppStoreLink       *string                                `protobuf:"bytes,17,opt,name=app_store_link,json=appStoreLink,proto3,oneof" json:"app_store_link,omitempty"`                     // 安卓应用商店直投下载地址
	ComplianceInfo     *BidResponse_Bid_Adm_AppComplianceInfo `protobuf:"bytes,18,opt,name=compliance_info,json=complianceInfo,proto3,oneof" json:"compliance_info,omitempty"`                 // 合规六要素
}

func (x *BidResponse_Bid_Adm_AppInfo) Reset() {
	*x = BidResponse_Bid_Adm_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_AppInfo) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_AppInfo) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 2}
}

func (x *BidResponse_Bid_Adm_AppInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetPackageSize() string {
	if x != nil && x.PackageSize != nil {
		return *x.PackageSize
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetIosBundleId() int64 {
	if x != nil && x.IosBundleId != nil {
		return *x.IosBundleId
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetUpdateTime() int64 {
	if x != nil && x.UpdateTime != nil {
		return *x.UpdateTime
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetIconImageUrl() string {
	if x != nil && x.IconImageUrl != nil {
		return *x.IconImageUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetIconImageWidth() int32 {
	if x != nil && x.IconImageWidth != nil {
		return *x.IconImageWidth
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetIconImageHeight() int32 {
	if x != nil && x.IconImageHeight != nil {
		return *x.IconImageHeight
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetDownloadUrlExpires() int64 {
	if x != nil && x.DownloadUrlExpires != nil {
		return *x.DownloadUrlExpires
	}
	return 0
}

func (x *BidResponse_Bid_Adm_AppInfo) GetDownloadUrlFileMd5() string {
	if x != nil && x.DownloadUrlFileMd5 != nil {
		return *x.DownloadUrlFileMd5
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetUlkUrl() string {
	if x != nil && x.UlkUrl != nil {
		return *x.UlkUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetUlkScheme() string {
	if x != nil && x.UlkScheme != nil {
		return *x.UlkScheme
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetAppStoreLink() string {
	if x != nil && x.AppStoreLink != nil {
		return *x.AppStoreLink
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppInfo) GetComplianceInfo() *BidResponse_Bid_Adm_AppComplianceInfo {
	if x != nil {
		return x.ComplianceInfo
	}
	return nil
}

type BidResponse_Bid_Adm_AppComplianceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                                                                                     // 应用中文名
	Version         string            `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                                                                                               // 应用版本号码
	Developer       string            `protobuf:"bytes,3,opt,name=developer,proto3" json:"developer,omitempty"`                                                                                           // 开发者
	PrivacyUrl      string            `protobuf:"bytes,4,opt,name=privacy_url,json=privacyUrl,proto3" json:"privacy_url,omitempty"`                                                                       // 隐私协议网址
	Permission      map[string]string `protobuf:"bytes,5,rep,name=permission,proto3" json:"permission,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 权限列表
	PermissionsUrl  *string           `protobuf:"bytes,6,opt,name=permissions_url,json=permissionsUrl,proto3,oneof" json:"permissions_url,omitempty"`                                                     // 权限URL，与权限列表二选一填充
	FunctionDescUrl string            `protobuf:"bytes,7,opt,name=function_desc_url,json=functionDescUrl,proto3" json:"function_desc_url,omitempty"`                                                      // 功能
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) Reset() {
	*x = BidResponse_Bid_Adm_AppComplianceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_AppComplianceInfo) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_AppComplianceInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_AppComplianceInfo) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 3}
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetPrivacyUrl() string {
	if x != nil {
		return x.PrivacyUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetPermission() map[string]string {
	if x != nil {
		return x.Permission
	}
	return nil
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetPermissionsUrl() string {
	if x != nil && x.PermissionsUrl != nil {
		return *x.PermissionsUrl
	}
	return ""
}

func (x *BidResponse_Bid_Adm_AppComplianceInfo) GetFunctionDescUrl() string {
	if x != nil {
		return x.FunctionDescUrl
	}
	return ""
}

type BidResponse_Bid_Adm_DownloadMonitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DmDownStartMonitorUrls    []string `protobuf:"bytes,1,rep,name=dm_down_start_monitor_urls,json=dmDownStartMonitorUrls,proto3" json:"dm_down_start_monitor_urls,omitempty"`          //下载开始检测链接
	DmDownEndMonitorUrls      []string `protobuf:"bytes,2,rep,name=dm_down_end_monitor_urls,json=dmDownEndMonitorUrls,proto3" json:"dm_down_end_monitor_urls,omitempty"`                //下载完成检测链接
	DmInstallStartMonitorUrls []string `protobuf:"bytes,3,rep,name=dm_install_start_monitor_urls,json=dmInstallStartMonitorUrls,proto3" json:"dm_install_start_monitor_urls,omitempty"` //开始安装监控
	DmInstallEndMonitorUrls   []string `protobuf:"bytes,4,rep,name=dm_install_end_monitor_urls,json=dmInstallEndMonitorUrls,proto3" json:"dm_install_end_monitor_urls,omitempty"`       //安装完成监控
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) Reset() {
	*x = BidResponse_Bid_Adm_DownloadMonitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_DownloadMonitor) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_DownloadMonitor) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_DownloadMonitor.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_DownloadMonitor) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 4}
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) GetDmDownStartMonitorUrls() []string {
	if x != nil {
		return x.DmDownStartMonitorUrls
	}
	return nil
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) GetDmDownEndMonitorUrls() []string {
	if x != nil {
		return x.DmDownEndMonitorUrls
	}
	return nil
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) GetDmInstallStartMonitorUrls() []string {
	if x != nil {
		return x.DmInstallStartMonitorUrls
	}
	return nil
}

func (x *BidResponse_Bid_Adm_DownloadMonitor) GetDmInstallEndMonitorUrls() []string {
	if x != nil {
		return x.DmInstallEndMonitorUrls
	}
	return nil
}

type BidResponse_Bid_Adm_MiniProgramInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseType BidResponse_Bid_Adm_MiniProgramReleaseType `protobuf:"varint,1,opt,name=release_type,json=releaseType,proto3,enum=com.ximalaya.bidding.BidResponse_Bid_Adm_MiniProgramReleaseType" json:"release_type,omitempty"` //发布类型
	ProgramId   string                                     `protobuf:"bytes,2,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`                                                                             // 小程序id
	ProgramPath string                                     `protobuf:"bytes,3,opt,name=program_path,json=programPath,proto3" json:"program_path,omitempty"`                                                                       // 小程序页面路径
}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) Reset() {
	*x = BidResponse_Bid_Adm_MiniProgramInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ximeng_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Adm_MiniProgramInfo) ProtoMessage() {}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ximeng_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Adm_MiniProgramInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Adm_MiniProgramInfo) Descriptor() ([]byte, []int) {
	return file_ximeng_proto_rawDescGZIP(), []int{1, 1, 0, 5}
}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) GetReleaseType() BidResponse_Bid_Adm_MiniProgramReleaseType {
	if x != nil {
		return x.ReleaseType
	}
	return BidResponse_Bid_Adm_MINI_PROGRAM_RELEASE_TYPE_UNSPECIFIED
}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *BidResponse_Bid_Adm_MiniProgramInfo) GetProgramPath() string {
	if x != nil {
		return x.ProgramPath
	}
	return ""
}

var File_ximeng_proto protoreflect.FileDescriptor

var file_ximeng_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x78, 0x69, 0x6d, 0x65, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14,
	0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x22, 0xa6, 0x1a, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x69, 0x6d, 0x70, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61,
	0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x04, 0x69, 0x6d, 0x70,
	0x73, 0x12, 0x36, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x3f, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78,
	0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79,
	0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x36, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c,
	0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a,
	0x99, 0x02, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12,
	0x42, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62,
	0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x1a, 0x56, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x4b, 0x0a, 0x03, 0x41,
	0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xb5, 0x11, 0x0a, 0x06, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x75, 0x61, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x69, 0x70, 0x76, 0x34, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04,
	0x69, 0x70, 0x76, 0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x69, 0x70,
	0x76, 0x36, 0x88, 0x01, 0x01, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6f, 0x73, 0x76, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x1f,
	0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x03, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x04, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x69, 0x6d,
	0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x07,
	0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06,
	0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29,
	0x0a, 0x0e, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x49, 0x64, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6f, 0x61, 0x69,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x88,
	0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x68, 0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x0a, 0x68, 0x6d, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x67, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x09,
	0x61, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f,
	0x61, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x0d, 0x61, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x6f, 0x70, 0x70,
	0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x10, 0x6f, 0x70, 0x70, 0x6f, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12,
	0x76, 0x69, 0x76, 0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0e, 0x52, 0x10, 0x76, 0x69, 0x76, 0x6f,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x2d, 0x0a, 0x10, 0x6d, 0x69, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x0e, 0x6d, 0x69, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26,
	0x0a, 0x0c, 0x6d, 0x69, 0x75, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x10, 0x52, 0x0b, 0x6d, 0x69, 0x75, 0x69, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x74,
	0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x11, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x54,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x12, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x13, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x88, 0x01, 0x01, 0x12,
	0x1e, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x14, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x88, 0x01, 0x01, 0x12,
	0x3b, 0x0a, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x61, 0x69, 0x64, 0x52, 0x05, 0x63, 0x61, 0x69, 0x64, 0x73, 0x12, 0x5e, 0x0a, 0x0d,
	0x63, 0x61, 0x69, 0x64, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61,
	0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x41, 0x49,
	0x44, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x48, 0x15, 0x52, 0x0c, 0x63, 0x61, 0x69,
	0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08,
	0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x74, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x48, 0x16,
	0x52, 0x07, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x08,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x31, 0x5f, 0x33, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x48, 0x17,
	0x52, 0x06, 0x70, 0x61, 0x69, 0x64, 0x31, 0x33, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x08, 0x70,
	0x61, 0x69, 0x64, 0x5f, 0x31, 0x5f, 0x34, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x48, 0x18, 0x52,
	0x06, 0x70, 0x61, 0x69, 0x64, 0x31, 0x34, 0x88, 0x01, 0x01, 0x1a, 0x90, 0x05, 0x0a, 0x0c, 0x43,
	0x41, 0x49, 0x44, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x63,
	0x61, 0x69, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x42, 0x6f, 0x6f, 0x74, 0x53, 0x65, 0x63, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x69, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x61, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x64,
	0x35, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x61, 0x69, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x5f, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x63, 0x61, 0x69, 0x64, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x39, 0x0a, 0x19, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x61, 0x69, 0x64, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63,
	0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x42, 0x79, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x17,
	0x63, 0x61, 0x69, 0x64, 0x5f, 0x68, 0x61, 0x72, 0x64, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63,
	0x61, 0x69, 0x64, 0x48, 0x61, 0x72, 0x64, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x42,
	0x79, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x61, 0x69, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x69, 0x64,
	0x5f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x61, 0x69, 0x64, 0x48, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61, 0x69, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1e,
	0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x69, 0x64, 0x4d, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x13, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x61, 0x69,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x69, 0x70, 0x76, 0x36, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x69, 0x6d, 0x65, 0x69, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6f,
	0x61, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x68, 0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x61, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6f, 0x70, 0x70, 0x6f, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x76,
	0x69, 0x76, 0x6f, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6d, 0x69, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d, 0x69, 0x75, 0x69, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x62, 0x6f, 0x6f, 0x74,
	0x5f, 0x74, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x69, 0x64, 0x66, 0x61, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69,
	0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x69, 0x64,
	0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69,
	0x72, 0x74, 0x68, 0x5f, 0x74, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f,
	0x31, 0x5f, 0x33, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x31, 0x5f, 0x34,
	0x1a, 0x30, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x1a, 0x31, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x3d, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c,
	0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x03, 0x6c, 0x6f, 0x6e, 0x1a, 0x86, 0x01, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x45, 0x0a, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78,
	0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x84, 0x21,
	0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x74,
	0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x08, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x73,
	0x1a, 0x44, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x04, 0x62,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x04, 0x62, 0x69, 0x64, 0x73, 0x1a, 0xab, 0x1f, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x69, 0x6e, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x73, 0x73,
	0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6b, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6b,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x03, 0x61, 0x64, 0x6d, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61,
	0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d,
	0x52, 0x03, 0x61, 0x64, 0x6d, 0x1a, 0xfc, 0x1c, 0x0a, 0x03, 0x41, 0x64, 0x6d, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x47, 0x0a, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x45, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61,
	0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x6c, 0x0a, 0x11, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c,
	0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x6f, 0x61, 0x6c,
	0x48, 0x00, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x47, 0x6f, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x64, 0x65, 0x65,
	0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69,
	0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x03, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x69, 0x0a, 0x10, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61,
	0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d,
	0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x48, 0x04, 0x52, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61,
	0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x4d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x05, 0x52, 0x0f,
	0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x88,
	0x01, 0x01, 0x1a, 0x5b, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x1a,
	0xac, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x1a, 0xfb,
	0x07, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x69, 0x6f, 0x73, 0x5f, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x0b, 0x69, 0x6f, 0x73, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x24, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x02, 0x48, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x29, 0x0a, 0x0e, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0c, 0x69, 0x63, 0x6f,
	0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05, 0x52, 0x0e, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x48, 0x06, 0x52, 0x0f, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x07, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x08, 0x52, 0x12, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x15, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x12, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35,
	0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x75, 0x6c, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x06, 0x75, 0x6c, 0x6b, 0x55, 0x72, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x22, 0x0a, 0x0a, 0x75, 0x6c, 0x6b, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x09, 0x75, 0x6c, 0x6b, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01,
	0x12, 0x69, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x0d, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x69,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x69, 0x6f, 0x73, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73,
	0x42, 0x18, 0x0a, 0x16, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72,
	0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x75,
	0x6c, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x6c, 0x6b, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x9a, 0x03, 0x0a,
	0x11, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x12,
	0x6b, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61,
	0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x41,
	0x70, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x0f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x73, 0x63, 0x55, 0x72, 0x6c, 0x1a, 0x3d, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x1a, 0x85, 0x02, 0x0a, 0x0f, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x3a, 0x0a,
	0x1a, 0x64, 0x6d, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x16, 0x64, 0x6d, 0x44, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x18, 0x64, 0x6d, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x64, 0x6d, 0x44,
	0x6f, 0x77, 0x6e, 0x45, 0x6e, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x40, 0x0a, 0x1d, 0x64, 0x6d, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x19, 0x64, 0x6d, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x1b, 0x64, 0x6d, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72,
	0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x64, 0x6d, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x45, 0x6e, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c,
	0x73, 0x1a, 0xb8, 0x01, 0x0a, 0x0f, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x63, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x78, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61, 0x2e, 0x62, 0x69, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x2e, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x22, 0xf0, 0x01, 0x0a,
	0x10, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x6f, 0x61,
	0x6c, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x47, 0x4f, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x4f, 0x41, 0x4c, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x47,
	0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x4f,
	0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x4f, 0x41, 0x4c,
	0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x24,
	0x0a, 0x20, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47,
	0x4f, 0x41, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x45, 0x4e, 0x47, 0x41, 0x47, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x4f, 0x41, 0x4c, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x5f,
	0x41, 0x57, 0x41, 0x52, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x4f,
	0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x4f, 0x41, 0x4c,
	0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x05, 0x22,
	0xbe, 0x01, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x45, 0x50, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4c, 0x4c, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4f, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c,
	0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x05,
	0x22, 0xb5, 0x01, 0x0a, 0x16, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x4d,
	0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x4c, 0x45,
	0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x01, 0x12, 0x22, 0x0a,
	0x1e, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x52, 0x45,
	0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10,
	0x02, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x03, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x42, 0x1f, 0x42, 0x0f, 0x58, 0x69, 0x6d, 0x61, 0x6c, 0x61, 0x79, 0x61,
	0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x0c, 0x2e, 0x2e, 0x2f, 0x78, 0x69, 0x6d, 0x65,
	0x6e, 0x67, 0x5f, 0x75, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ximeng_proto_rawDescOnce sync.Once
	file_ximeng_proto_rawDescData = file_ximeng_proto_rawDesc
)

func file_ximeng_proto_rawDescGZIP() []byte {
	file_ximeng_proto_rawDescOnce.Do(func() {
		file_ximeng_proto_rawDescData = protoimpl.X.CompressGZIP(file_ximeng_proto_rawDescData)
	})
	return file_ximeng_proto_rawDescData
}

var file_ximeng_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_ximeng_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_ximeng_proto_goTypes = []interface{}{
	(BidResponse_Bid_Adm_OptimizationGoal)(0),       // 0: com.ximalaya.bidding.BidResponse.Bid.Adm.OptimizationGoal
	(BidResponse_Bid_Adm_ActionType)(0),             // 1: com.ximalaya.bidding.BidResponse.Bid.Adm.ActionType
	(BidResponse_Bid_Adm_MiniProgramReleaseType)(0), // 2: com.ximalaya.bidding.BidResponse.Bid.Adm.MiniProgramReleaseType
	(*BidRequest)(nil),                              // 3: com.ximalaya.bidding.BidRequest
	(*BidResponse)(nil),                             // 4: com.ximalaya.bidding.BidResponse
	(*BidRequest_Imp)(nil),                          // 5: com.ximalaya.bidding.BidRequest.Imp
	(*BidRequest_App)(nil),                          // 6: com.ximalaya.bidding.BidRequest.App
	(*BidRequest_Device)(nil),                       // 7: com.ximalaya.bidding.BidRequest.Device
	(*BidRequest_Caid)(nil),                         // 8: com.ximalaya.bidding.BidRequest.Caid
	(*BidRequest_User)(nil),                         // 9: com.ximalaya.bidding.BidRequest.User
	(*BidRequest_Geo)(nil),                          // 10: com.ximalaya.bidding.BidRequest.Geo
	(*BidRequest_Ext)(nil),                          // 11: com.ximalaya.bidding.BidRequest.Ext
	(*BidRequest_Imp_Asset)(nil),                    // 12: com.ximalaya.bidding.BidRequest.Imp.Asset
	(*BidRequest_Device_CAIDCriteria)(nil),          // 13: com.ximalaya.bidding.BidRequest.Device.CAIDCriteria
	nil,                                             // 14: com.ximalaya.bidding.BidRequest.Ext.ExtraEntry
	(*BidResponse_SeatBid)(nil),                     // 15: com.ximalaya.bidding.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),                         // 16: com.ximalaya.bidding.BidResponse.Bid
	(*BidResponse_Bid_Adm)(nil),                     // 17: com.ximalaya.bidding.BidResponse.Bid.Adm
	(*BidResponse_Bid_Adm_Image)(nil),               // 18: com.ximalaya.bidding.BidResponse.Bid.Adm.Image
	(*BidResponse_Bid_Adm_Video)(nil),               // 19: com.ximalaya.bidding.BidResponse.Bid.Adm.Video
	(*BidResponse_Bid_Adm_AppInfo)(nil),             // 20: com.ximalaya.bidding.BidResponse.Bid.Adm.AppInfo
	(*BidResponse_Bid_Adm_AppComplianceInfo)(nil),   // 21: com.ximalaya.bidding.BidResponse.Bid.Adm.AppComplianceInfo
	(*BidResponse_Bid_Adm_DownloadMonitor)(nil),     // 22: com.ximalaya.bidding.BidResponse.Bid.Adm.DownloadMonitor
	(*BidResponse_Bid_Adm_MiniProgramInfo)(nil),     // 23: com.ximalaya.bidding.BidResponse.Bid.Adm.MiniProgramInfo
	nil, // 24: com.ximalaya.bidding.BidResponse.Bid.Adm.AppComplianceInfo.PermissionEntry
}
var file_ximeng_proto_depIdxs = []int32{
	5,  // 0: com.ximalaya.bidding.BidRequest.imps:type_name -> com.ximalaya.bidding.BidRequest.Imp
	6,  // 1: com.ximalaya.bidding.BidRequest.app:type_name -> com.ximalaya.bidding.BidRequest.App
	7,  // 2: com.ximalaya.bidding.BidRequest.device:type_name -> com.ximalaya.bidding.BidRequest.Device
	9,  // 3: com.ximalaya.bidding.BidRequest.user:type_name -> com.ximalaya.bidding.BidRequest.User
	10, // 4: com.ximalaya.bidding.BidRequest.geo:type_name -> com.ximalaya.bidding.BidRequest.Geo
	11, // 5: com.ximalaya.bidding.BidRequest.ext:type_name -> com.ximalaya.bidding.BidRequest.Ext
	15, // 6: com.ximalaya.bidding.BidResponse.seat_bids:type_name -> com.ximalaya.bidding.BidResponse.SeatBid
	12, // 7: com.ximalaya.bidding.BidRequest.Imp.assets:type_name -> com.ximalaya.bidding.BidRequest.Imp.Asset
	8,  // 8: com.ximalaya.bidding.BidRequest.Device.caids:type_name -> com.ximalaya.bidding.BidRequest.Caid
	13, // 9: com.ximalaya.bidding.BidRequest.Device.caid_criteria:type_name -> com.ximalaya.bidding.BidRequest.Device.CAIDCriteria
	14, // 10: com.ximalaya.bidding.BidRequest.Ext.extra:type_name -> com.ximalaya.bidding.BidRequest.Ext.ExtraEntry
	16, // 11: com.ximalaya.bidding.BidResponse.SeatBid.bids:type_name -> com.ximalaya.bidding.BidResponse.Bid
	17, // 12: com.ximalaya.bidding.BidResponse.Bid.adm:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm
	18, // 13: com.ximalaya.bidding.BidResponse.Bid.Adm.images:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.Image
	19, // 14: com.ximalaya.bidding.BidResponse.Bid.Adm.video:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.Video
	0,  // 15: com.ximalaya.bidding.BidResponse.Bid.Adm.optimization_goal:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.OptimizationGoal
	1,  // 16: com.ximalaya.bidding.BidResponse.Bid.Adm.action_type:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.ActionType
	20, // 17: com.ximalaya.bidding.BidResponse.Bid.Adm.app_info:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.AppInfo
	22, // 18: com.ximalaya.bidding.BidResponse.Bid.Adm.download_monitor:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.DownloadMonitor
	23, // 19: com.ximalaya.bidding.BidResponse.Bid.Adm.mini_program_info:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.MiniProgramInfo
	21, // 20: com.ximalaya.bidding.BidResponse.Bid.Adm.AppInfo.compliance_info:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.AppComplianceInfo
	24, // 21: com.ximalaya.bidding.BidResponse.Bid.Adm.AppComplianceInfo.permission:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.AppComplianceInfo.PermissionEntry
	2,  // 22: com.ximalaya.bidding.BidResponse.Bid.Adm.MiniProgramInfo.release_type:type_name -> com.ximalaya.bidding.BidResponse.Bid.Adm.MiniProgramReleaseType
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_ximeng_proto_init() }
func file_ximeng_proto_init() {
	if File_ximeng_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ximeng_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_CAIDCriteria); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_AppComplianceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_DownloadMonitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ximeng_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Adm_MiniProgramInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_ximeng_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_ximeng_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_ximeng_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_ximeng_proto_msgTypes[18].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ximeng_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ximeng_proto_goTypes,
		DependencyIndexes: file_ximeng_proto_depIdxs,
		EnumInfos:         file_ximeng_proto_enumTypes,
		MessageInfos:      file_ximeng_proto_msgTypes,
	}.Build()
	File_ximeng_proto = out.File
	file_ximeng_proto_rawDesc = nil
	file_ximeng_proto_goTypes = nil
	file_ximeng_proto_depIdxs = nil
}
