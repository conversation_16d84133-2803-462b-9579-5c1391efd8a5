syntax = "proto3";

package com.ximalaya.bidding;

option java_outer_classname = "XimalayaBidding";

option go_package = "mh_proxy/pb/ximeng_up";

message BidRequest {
  string id = 1; // 本次请求的唯一id
  string api_version = 2; //协议版本（目前为"v1.2"）
  int32 request_type = 3; // 请求类型(1: 正式环境; 2: ping,测试RTT; 4: 测试广告，不计费; 8:其他)
  repeated Imp imps = 4; // 曝光机会列表
  App app = 5; // APP信息
  Device device = 6; // 设备信息
  User user = 7; // 用户信息
  Geo geo = 8; // 地理位置信息
  uint32 timeout = 9; // 请求预留的时间，单位为ms
  Ext ext = 10; // 扩展参数

  message Imp {
    string id = 1; // 标识唯一一次曝光机会
    string tag_id = 2; // 广告位ID
    int32 ad_type = 3; // 广告类型（1:开屏、2：信息流、3：激励视频、4：贴片、5：banner）
    int32 bid_type = 4; // 出价类型, 0：cpm 1：cpc 当前仅支持cpm
    int64 bid_floor = 5; // 底价，分/千次曝光
    repeated Asset assets = 6; // 模版信息，详见模版资源映射表
    message Asset {
      string template_id = 1; //  广告位模版ID, 详见模版资源映射表
      int32 width = 2; // 广告位模版宽度
      int32 height = 3; // 广告位模版高度
    }
  }

  message App {
    string name = 1; // 应用名称：例如：喜马拉雅FM
    string bundle = 2; // 应用程序包名：例如：com.ximalaya.iting
    string version = 3; // 应用版本号: 例如: 9.2.6
  }

  message Device {
    string ua = 1; // UA信息
    optional string ipv4 = 2; // 用户设备ipv4地址
    optional string ipv6 = 3; // 用户设备ipv6地址
    int32 carrier = 4; // 运营商（0：未知；1：移动; 2：联通；3：电信; 4: 广电）
    int32 network_type = 5; //网络连接类型（0:未知; 1:Ethernet/Wifi；2：2G；3：3G；4：4G; 5: 5G; 6: 6G[预留], 7: 移动网络未知）
    string make = 6; // 手机品牌，如：iPhone，Xiaomi
    string model = 7; // 手机型号，如：iPhoneX, KNT-AL10
    string os = 8; // 操作系统名称（ios、android、windows、macos、linux, 均为小写）
    string osv = 9; //  操作系统版本
    int32 device_type = 10; // 设备类型（0：手机；1:平板; 2: 电脑; 3: other）

    optional int32 screen_height = 11; // 屏幕的物理高度，以像素为单位
    optional int32 screen_width = 12; // 屏幕的物理宽度，以像素为单位

    optional string imei = 13; // Android IMEI原始值明文
    optional string imei_md5 = 14; // IMEI MD5, hex 小写


    // @exclude Android Criteria

    optional string android_id = 15; // AndroidId原始值明文
    optional string android_id_md5 = 16; // AndroidId MD5, hex 小写
    optional string oaid = 17; // Android OAID原始值明文
    optional string oaid_md5 = 18; // Android OAID MD5, hex 小写

    optional string hms_version = 19; // 华为机型HMS Core版本号，华为设备必填
    optional string ag_version = 20; // 设备应用市场版本号，华为设备必填
    optional string ag_country_code = 21; // 应用市场中设置的国家和地区，华为设备必填
    optional string oppo_store_version = 22; // oppo应用商店版本号，oppo设备必填
    optional string vivo_store_version = 23; // vivo应用商店版本号，vivo设备必填
    optional string mi_store_version = 24; // 小米应用商店版本号，小米设备必填
    optional string miui_version = 25; // 小米系统版本，miui系统必填

    optional string boot_ts = 26; // 设备启动时间 iOS: 1657043138
    optional string update_ts = 27; // 设备系统更新时间 iOS: 1647545826.588793

    // @exclude iOS Criteria

    optional string idfa = 28; // IDFA原始值明文
    optional string idfa_md5 = 29; // IDFA MD5, 小写
    repeated Caid caids = 30; // CAID 对象数组

    optional CAIDCriteria caid_criteria = 31; // caid相关参数
    optional string birth_ts = 32; // 设备初始化时间[协议v1.2新增] iOS: 1647545718.125795458
    optional string paid_1_3 = 33; // PDD归因v1.3版本paid[协议v1.2新增]
    optional string paid_1_4 = 34; // PDD归因v1.4版本paid[协议v1.2新增]

    message CAIDCriteria {
      string caid_boot_sec = 1; // 设备启动时间 UnixTimestamp eg: 1691052221
      string caid_country_code = 2; // 国家 eg: CN
      string caid_language = 3; // 语言 eg: zh-Hans-CN
      string caid_device_name_md5 = 4; // 设备名称MD5 eg: e910dddb2748c36b47fcde5dd720eec1
      string caid_system_version = 5; // 系统版本 eg: 14.0
      string caid_hardware_machine = 6; // 设备 Machine, eg: iPhone10,3
      string caid_carrier_info = 7; // 运营商信息 eg: 中国移动
      string caid_physical_memory_byte = 8; // 物理内存容量 eg: 3955589120
      string caid_harddisk_size_byte = 9; // 硬盘容量 eg: 63900340224
      string caid_system_update_sec = 10; // 系统更新时间, 保留小数点后 6 位，如不够需补 0
      string caid_hardware_model = 11; // 设备 Model eg: D22AP
      string caid_time_zone = 12; // 时区 eg: 28800
      string caid_mnt_id = 13; // mnt_id eg: 30225948939346695D0D744027A07803D5F8410346398776C879B727@/dev/disk1s1
      string caid_file_init_time = 14; // 设备初始化时间 eg: 1632467920.301150749
    }

  }

  message Caid {
    string id = 1; // caid 的值
    string version = 2; // caid 版本
  }

  message User {
    string id = 1; // [保留字段] 用户唯一标识
    string app_list = 2; // 用户已安装app列表, 英文逗号分隔，如 com.xmly.ting,com.xunmeng.pinduoduo,com.jingdong.app.mall
  }

  message Geo {
    int32 type = 1; // 坐标系: 经纬度有值时必传，坐标系类型 0:GCJ-02; 1:WGS-84; 2:bd09ll
    float lat = 2; // 纬度 latitude, 取值从-90 到 90，南为负值
    float lon = 3; // 经度 longitude, 取值从-180 到 180，西为负值

  }

  message Ext {
    map<string, string> extra = 1; // Extra自定义Map
  }
}

message BidResponse {
  string id = 1; // 请求ID，需与BidRequest.id一致
  int32 code = 3; // 广告响应状态码
  string bid_id = 2; // 喜马联盟侧生成的竞价ID
  repeated SeatBid seat_bids = 4; //喜马联盟侧生成的竞价信息，目前只有一个

  message SeatBid {
    repeated Bid bids = 1; // DSP参与竞价的位置，与BidRequest.imp对应，每个imp最多只可返回一个bid
  }

  message Bid {
    string id = 1; // dsp侧针对这次竞价的ID
    string imp_id = 2; // 曝光ID，对应BidRequest.imp.id，必填
    string ad_id = 3; // 广告id
    int64 price = 4; // DSP出价，单位：分/千次曝光
    repeated string win_notice_urls = 5; // 竞价成功通知地址
    repeated string loss_notice_urls = 6; // 可选。竞价失败通知的url列表
    repeated string imp_trackers = 7; // 曝光监测地址
    repeated string clk_trackers = 8; // 点击监测地址
    Adm adm = 9; // 创意物料信息

    //物料信息
    message Adm {
      string template_id = 1; // 模版ID
      string creative_id = 2; // 可选。广告物料的唯一标识
      string title = 3; // 标题
      string desc = 4; // 描述
      repeated Image images = 5; // 图片信息
      Video video = 6; // 视频信息

      optional OptimizationGoal optimization_goal = 7; // 推广目标 : 拉新，拉活，线索表单，小程序
      optional ActionType action_type = 8; // 交互类型

      string landing_page = 9; // 落地页url
      optional string deep_link = 10; // 客户端优先跳转deeplink链接,其次跳转普通网址类或应用下载类的落地页

      optional AppInfo app_info = 11; // APP信息，用于下载类广告需要的APP信息
      optional DownloadMonitor download_monitor = 12; // 下载类广告下载监测链接
      optional MiniProgramInfo mini_program_info = 13; // 微信小程序信息

      message Image {
        string url = 1; // 图片URL
        int32 width = 2; // 图片宽度
        int32 height = 3; // 图片高度
        string mime = 4; // 图片类型， 如：JPG/JPEG/PNG
      }
      message Video {
        string video_url = 1; // 视频URL
        int32 width = 2; // 视频宽度
        int32 height = 3; // 视频高度
        int32 duration = 4; // 视频素材的播放时长，单位：秒
        int32 size = 5; // 视频文件的大小，单位Byte
        string mime = 6; // 视频素材类型，以MIME类型表示
        string cover = 7; // 视频封面图
      }
      message AppInfo {
        string name = 1; // APP中文名称，针对应用下载类广告，用于下载广告
        string desc = 2; // APP文字描述，用于下载广告
        string version = 3; // APP版本号，用于下载广告
        string package_name = 4; // APP包名，用于下载广告
        optional string package_size = 5; // APP应用包大小，用于下载广告
        optional int64  ios_bundle_id = 6; // iOS下载应用ID，用于下载广告
        optional int64  update_time = 7; //app更新时间
        optional float  score = 8; //应用评分
        optional string icon_image_url = 9; //app icon url
        optional int32 icon_image_width = 10; //app icon width
        optional int32 icon_image_height = 11; //app icon height
        optional string download_url = 12; //下载地址
        optional int64 download_url_expires = 13;// 下载过期时间戳,秒级
        optional string download_url_file_md5 = 14; //文件md5
        optional string ulk_url = 15; // universal link url, 用于iOS调起
        optional string ulk_scheme = 16; // universal link scheme,用于iOS嗅探
        optional string app_store_link = 17; // 安卓应用商店直投下载地址
        optional AppComplianceInfo compliance_info = 18; // 合规六要素
      }

      message AppComplianceInfo {// 合规六要素
        string name = 1; // 应用中文名
        string version = 2; // 应用版本号码
        string developer = 3; // 开发者
        string privacy_url = 4; // 隐私协议网址
        map<string, string> permission = 5; // 权限列表
        optional string permissions_url = 6; // 权限URL，与权限列表二选一填充
        string function_desc_url = 7; // 功能
      }

      message DownloadMonitor {
        repeated string dm_down_start_monitor_urls = 1; //下载开始检测链接
        repeated string dm_down_end_monitor_urls = 2; //下载完成检测链接
        repeated string dm_install_start_monitor_urls = 3; //开始安装监控
        repeated string dm_install_end_monitor_urls = 4; //安装完成监控
      }

      message MiniProgramInfo {
        MiniProgramReleaseType release_type = 1; //发布类型
        string program_id = 2; // 小程序id
        string program_path = 3; // 小程序页面路径
      }

      enum OptimizationGoal {// 投放优化目标
        OPTIMIZATION_GOAL_UNSPECIFIED = 0; // 未指定
        OPTIMIZATION_GOAL_LEAD_GENERATION = 1; // 线索类(表单or线索提交)
        OPTIMIZATION_GOAL_APP_INSTALL = 2; // 拉新, 应用下载
        OPTIMIZATION_GOAL_APP_ENGAGEMENT = 3; // 拉活, 应用使用度提升
        OPTIMIZATION_GOAL_BRAND_AWARENESS = 4;// 品牌曝光
        OPTIMIZATION_GOAL_MINI_PROGRAM = 5;// 小程序
      }

      enum ActionType {//广告点击后优先行为
        ACTION_TYPE_UNSPECIFIED = 0; // 未指定
        ACTION_TYPE_LANDING_PAGE = 1; // 打开落地页
        ACTION_TYPE_DEEP_LINK = 2; // deep_link [+ 落地页]
        ACTION_TYPE_ANDROID_INSTALL = 3; // android下载安装
        ACTION_TYPE_IOS_INSTALL = 4; // 应用商店安装
        ACTION_TYPE_MINI_PROGRAM = 5;//小程序
      }

      enum MiniProgramReleaseType {//小程序发布类型
        MINI_PROGRAM_RELEASE_TYPE_UNSPECIFIED = 0; // 未指定
        MINI_PROGRAM_RELEASE_TYPE_PRODUCT = 1; // 正式
        MINI_PROGRAM_RELEASE_TYPE_TEST = 2; // 测试
        MINI_PROGRAM_RELEASE_TYPE_PREVIEW = 3; // 预览
      }
    }
  }
}
