// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: qimao_1.4.proto

package qimao

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdType int32

const (
	AdType_AdTypeUnknown AdType = 0
	AdType_Stream        AdType = 1 // 信息流
	AdType_Banner        AdType = 2 // banner
	AdType_Splash        AdType = 3 // 开屏
	AdType_RewardVideo   AdType = 4 //激励视频
)

// Enum value maps for AdType.
var (
	AdType_name = map[int32]string{
		0: "AdTypeUnknown",
		1: "Stream",
		2: "Banner",
		3: "Splash",
		4: "RewardVideo",
	}
	AdType_value = map[string]int32{
		"AdTypeUnknown": 0,
		"Stream":        1,
		"Banner":        2,
		"Splash":        3,
		"RewardVideo":   4,
	}
)

func (x AdType) Enum() *AdType {
	p := new(AdType)
	*p = x
	return p
}

func (x AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[0].Descriptor()
}

func (AdType) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[0]
}

func (x AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdType.Descriptor instead.
func (AdType) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0}
}

type AdsAction int32

const (
	AdsAction_AdsActionUnknown AdsAction = 0
	AdsAction_Webview          AdsAction = 1 //webview
	AdsAction_Download         AdsAction = 2 //下载
	AdsAction_Deeplink         AdsAction = 3 //deeplink
	AdsAction_WechatApplet     AdsAction = 4 //微信小程序
)

// Enum value maps for AdsAction.
var (
	AdsAction_name = map[int32]string{
		0: "AdsActionUnknown",
		1: "Webview",
		2: "Download",
		3: "Deeplink",
		4: "WechatApplet",
	}
	AdsAction_value = map[string]int32{
		"AdsActionUnknown": 0,
		"Webview":          1,
		"Download":         2,
		"Deeplink":         3,
		"WechatApplet":     4,
	}
)

func (x AdsAction) Enum() *AdsAction {
	p := new(AdsAction)
	*p = x
	return p
}

func (x AdsAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdsAction) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[1].Descriptor()
}

func (AdsAction) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[1]
}

func (x AdsAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdsAction.Descriptor instead.
func (AdsAction) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1}
}

type PriceRequest_IsTest int32

const (
	PriceRequest_False PriceRequest_IsTest = 0
	PriceRequest_True  PriceRequest_IsTest = 1
)

// Enum value maps for PriceRequest_IsTest.
var (
	PriceRequest_IsTest_name = map[int32]string{
		0: "False",
		1: "True",
	}
	PriceRequest_IsTest_value = map[string]int32{
		"False": 0,
		"True":  1,
	}
)

func (x PriceRequest_IsTest) Enum() *PriceRequest_IsTest {
	p := new(PriceRequest_IsTest)
	*p = x
	return p
}

func (x PriceRequest_IsTest) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_IsTest) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[2].Descriptor()
}

func (PriceRequest_IsTest) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[2]
}

func (x PriceRequest_IsTest) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_IsTest.Descriptor instead.
func (PriceRequest_IsTest) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 0}
}

type PriceRequest_Device_Os int32

const (
	PriceRequest_Device_OsUnknown PriceRequest_Device_Os = 0
	PriceRequest_Device_OsAndroid PriceRequest_Device_Os = 1 //安卓
	PriceRequest_Device_OsIos     PriceRequest_Device_Os = 2 //ios
)

// Enum value maps for PriceRequest_Device_Os.
var (
	PriceRequest_Device_Os_name = map[int32]string{
		0: "OsUnknown",
		1: "OsAndroid",
		2: "OsIos",
	}
	PriceRequest_Device_Os_value = map[string]int32{
		"OsUnknown": 0,
		"OsAndroid": 1,
		"OsIos":     2,
	}
)

func (x PriceRequest_Device_Os) Enum() *PriceRequest_Device_Os {
	p := new(PriceRequest_Device_Os)
	*p = x
	return p
}

func (x PriceRequest_Device_Os) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_Device_Os) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[3].Descriptor()
}

func (PriceRequest_Device_Os) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[3]
}

func (x PriceRequest_Device_Os) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_Device_Os.Descriptor instead.
func (PriceRequest_Device_Os) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 0, 0}
}

type PriceRequest_Pos_MaterialType int32

const (
	PriceRequest_Pos_MaterialTypeUnknown PriceRequest_Pos_MaterialType = 0
	PriceRequest_Pos_ImageWord           PriceRequest_Pos_MaterialType = 1 //图片+文字
	PriceRequest_Pos_VideoWord           PriceRequest_Pos_MaterialType = 2 //视频+封面+文字
)

// Enum value maps for PriceRequest_Pos_MaterialType.
var (
	PriceRequest_Pos_MaterialType_name = map[int32]string{
		0: "MaterialTypeUnknown",
		1: "ImageWord",
		2: "VideoWord",
	}
	PriceRequest_Pos_MaterialType_value = map[string]int32{
		"MaterialTypeUnknown": 0,
		"ImageWord":           1,
		"VideoWord":           2,
	}
)

func (x PriceRequest_Pos_MaterialType) Enum() *PriceRequest_Pos_MaterialType {
	p := new(PriceRequest_Pos_MaterialType)
	*p = x
	return p
}

func (x PriceRequest_Pos_MaterialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_Pos_MaterialType) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[4].Descriptor()
}

func (PriceRequest_Pos_MaterialType) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[4]
}

func (x PriceRequest_Pos_MaterialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_Pos_MaterialType.Descriptor instead.
func (PriceRequest_Pos_MaterialType) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 2, 0}
}

type PriceRequest_User_Gender int32

const (
	PriceRequest_User_unknown PriceRequest_User_Gender = 0 //未知
	PriceRequest_User_male    PriceRequest_User_Gender = 1 //男
	PriceRequest_User_female  PriceRequest_User_Gender = 2 //女
)

// Enum value maps for PriceRequest_User_Gender.
var (
	PriceRequest_User_Gender_name = map[int32]string{
		0: "unknown",
		1: "male",
		2: "female",
	}
	PriceRequest_User_Gender_value = map[string]int32{
		"unknown": 0,
		"male":    1,
		"female":  2,
	}
)

func (x PriceRequest_User_Gender) Enum() *PriceRequest_User_Gender {
	p := new(PriceRequest_User_Gender)
	*p = x
	return p
}

func (x PriceRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[5].Descriptor()
}

func (PriceRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[5]
}

func (x PriceRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_User_Gender.Descriptor instead.
func (PriceRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 3, 0}
}

type PriceRequest_Network_Carrier int32

const (
	PriceRequest_Network_CarrierUnknown      PriceRequest_Network_Carrier = 0 //未知
	PriceRequest_Network_CarrierChinaMobile  PriceRequest_Network_Carrier = 1 //移动
	PriceRequest_Network_CarrierChinaUnicom  PriceRequest_Network_Carrier = 2 //联通
	PriceRequest_Network_CarrierChinaTelecom PriceRequest_Network_Carrier = 3 //电信
)

// Enum value maps for PriceRequest_Network_Carrier.
var (
	PriceRequest_Network_Carrier_name = map[int32]string{
		0: "CarrierUnknown",
		1: "CarrierChinaMobile",
		2: "CarrierChinaUnicom",
		3: "CarrierChinaTelecom",
	}
	PriceRequest_Network_Carrier_value = map[string]int32{
		"CarrierUnknown":      0,
		"CarrierChinaMobile":  1,
		"CarrierChinaUnicom":  2,
		"CarrierChinaTelecom": 3,
	}
)

func (x PriceRequest_Network_Carrier) Enum() *PriceRequest_Network_Carrier {
	p := new(PriceRequest_Network_Carrier)
	*p = x
	return p
}

func (x PriceRequest_Network_Carrier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_Network_Carrier) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[6].Descriptor()
}

func (PriceRequest_Network_Carrier) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[6]
}

func (x PriceRequest_Network_Carrier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_Network_Carrier.Descriptor instead.
func (PriceRequest_Network_Carrier) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 4, 0}
}

type PriceRequest_Network_ConnType int32

const (
	PriceRequest_Network_ConnTypeUnknown    PriceRequest_Network_ConnType = 0   //无法探测当前网络状态
	PriceRequest_Network_ConnTypeCellular   PriceRequest_Network_ConnType = 1   //蜂窝数据接入，未知网络类型
	PriceRequest_Network_ConnTypeCellular2g PriceRequest_Network_ConnType = 2   //蜂窝数据 2G 网络
	PriceRequest_Network_ConnTypeCellular3g PriceRequest_Network_ConnType = 3   //蜂窝数据 3G 网络
	PriceRequest_Network_ConnTypeCellular4g PriceRequest_Network_ConnType = 4   //蜂窝数据 4G 网络
	PriceRequest_Network_ConnTypeCellular5g PriceRequest_Network_ConnType = 5   //蜂窝数据 5G 网络
	PriceRequest_Network_ConnTypeWifi       PriceRequest_Network_ConnType = 100 //WIFI 网络接入
)

// Enum value maps for PriceRequest_Network_ConnType.
var (
	PriceRequest_Network_ConnType_name = map[int32]string{
		0:   "ConnTypeUnknown",
		1:   "ConnTypeCellular",
		2:   "ConnTypeCellular2g",
		3:   "ConnTypeCellular3g",
		4:   "ConnTypeCellular4g",
		5:   "ConnTypeCellular5g",
		100: "ConnTypeWifi",
	}
	PriceRequest_Network_ConnType_value = map[string]int32{
		"ConnTypeUnknown":    0,
		"ConnTypeCellular":   1,
		"ConnTypeCellular2g": 2,
		"ConnTypeCellular3g": 3,
		"ConnTypeCellular4g": 4,
		"ConnTypeCellular5g": 5,
		"ConnTypeWifi":       100,
	}
)

func (x PriceRequest_Network_ConnType) Enum() *PriceRequest_Network_ConnType {
	p := new(PriceRequest_Network_ConnType)
	*p = x
	return p
}

func (x PriceRequest_Network_ConnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PriceRequest_Network_ConnType) Descriptor() protoreflect.EnumDescriptor {
	return file_qimao_1_4_proto_enumTypes[7].Descriptor()
}

func (PriceRequest_Network_ConnType) Type() protoreflect.EnumType {
	return &file_qimao_1_4_proto_enumTypes[7]
}

func (x PriceRequest_Network_ConnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PriceRequest_Network_ConnType.Descriptor instead.
func (PriceRequest_Network_ConnType) EnumDescriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 4, 1}
}

type PriceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId  string                `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`                    //必填，标识请求的唯一id
	At         uint32                `protobuf:"varint,2,opt,name=at,proto3" json:"at,omitempty"`                                                  //必填，竞价类型;1一价;2二价格；默认值1
	ApiVersion *string               `protobuf:"bytes,3,opt,name=api_version,json=apiVersion,proto3,oneof" json:"api_version,omitempty"`           //选填，dsp对应的api version 信息
	Device     *PriceRequest_Device  `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`                                           //必填，设备信息
	Media      *PriceRequest_Media   `protobuf:"bytes,5,opt,name=media,proto3" json:"media,omitempty"`                                             //必填，媒体信息
	Pos        *PriceRequest_Pos     `protobuf:"bytes,6,opt,name=pos,proto3" json:"pos,omitempty"`                                                 //必填，广告位信息列表，目前一次请求仅包含一个广告位
	User       *PriceRequest_User    `protobuf:"bytes,7,opt,name=user,proto3,oneof" json:"user,omitempty"`                                         //选填，用户信息，该设备对应的用户信息，投放受众
	Network    *PriceRequest_Network `protobuf:"bytes,8,opt,name=network,proto3,oneof" json:"network,omitempty"`                                   //选填，用户网络环境
	Ip         string                `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`                                                   //必填，设备的ip，用户粗略定位，定于定向
	Test       *PriceRequest_IsTest  `protobuf:"varint,10,opt,name=test,proto3,enum=qimaoadx0104.PriceRequest_IsTest,oneof" json:"test,omitempty"` //必填，是否测试广告：1是 0非
	Tmax       uint32                `protobuf:"varint,11,opt,name=tmax,proto3" json:"tmax,omitempty"`                                             //必填，最初等待时间单位ms，默认300ms
	Ext        []*Ext                `protobuf:"bytes,12,rep,name=ext,proto3" json:"ext,omitempty"`                                                //选填，存储双方约定的其他信息
	ReqSegment uint32                `protobuf:"varint,13,opt,name=req_segment,json=reqSegment,proto3" json:"req_segment,omitempty"`               //必填，1.一段请求  2.二段请求（默认二段请求；二段请求为价格素材分开请求返回，一段请求为价格素材一次性返回）
}

func (x *PriceRequest) Reset() {
	*x = PriceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest) ProtoMessage() {}

func (x *PriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest.ProtoReflect.Descriptor instead.
func (*PriceRequest) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0}
}

func (x *PriceRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PriceRequest) GetAt() uint32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *PriceRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *PriceRequest) GetDevice() *PriceRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *PriceRequest) GetMedia() *PriceRequest_Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *PriceRequest) GetPos() *PriceRequest_Pos {
	if x != nil {
		return x.Pos
	}
	return nil
}

func (x *PriceRequest) GetUser() *PriceRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *PriceRequest) GetNetwork() *PriceRequest_Network {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *PriceRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *PriceRequest) GetTest() PriceRequest_IsTest {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return PriceRequest_False
}

func (x *PriceRequest) GetTmax() uint32 {
	if x != nil {
		return x.Tmax
	}
	return 0
}

func (x *PriceRequest) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PriceRequest) GetReqSegment() uint32 {
	if x != nil {
		return x.ReqSegment
	}
	return 0
}

// 广告报价接口返回对象Response
type PriceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   uint32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`     //必填，状态码，0成功
	Msg    string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`        //选填，报错信息(code为2，3该值必传)
	Data   *PriceResponse_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`      //选填，广告报价内容
	Breaks uint32              `protobuf:"varint,4,opt,name=breaks,proto3" json:"breaks,omitempty"` // 选填，该用户熔断时长，单位秒，维度为广告位id(tagid)，0代表不熔断，理论最多熔断时长24h
}

func (x *PriceResponse) Reset() {
	*x = PriceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse) ProtoMessage() {}

func (x *PriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse.ProtoReflect.Descriptor instead.
func (*PriceResponse) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1}
}

func (x *PriceResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PriceResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PriceResponse) GetData() *PriceResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PriceResponse) GetBreaks() uint32 {
	if x != nil {
		return x.Breaks
	}
	return 0
}

// 广告素材接口请求对象Request
type AdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` //必填，标识请求的唯一id
	Token     string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`                          //必填，获取广告创意的唯一标识符
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`                            //必填，验证标识 加密方式详见【附录】
	Ts        string `protobuf:"bytes,4,opt,name=ts,proto3" json:"ts,omitempty"`                                //必填，毫秒时间戳
}

func (x *AdRequest) Reset() {
	*x = AdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdRequest) ProtoMessage() {}

func (x *AdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdRequest.ProtoReflect.Descriptor instead.
func (*AdRequest) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{2}
}

func (x *AdRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AdRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *AdRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *AdRequest) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

// 广告素材接口返回对象Response
type AdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      //必填，状态码，0成功 具体列表见【附录】
	Msg  *string          `protobuf:"bytes,2,opt,name=msg,proto3,oneof" json:"msg,omitempty"`   //选填，报错信息 （code为2，3该值必传）
	Data *AdResponse_Data `protobuf:"bytes,3,opt,name=data,proto3,oneof" json:"data,omitempty"` //选填，广告素材内容
}

func (x *AdResponse) Reset() {
	*x = AdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse) ProtoMessage() {}

func (x *AdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse.ProtoReflect.Descriptor instead.
func (*AdResponse) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3}
}

func (x *AdResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AdResponse) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

func (x *AdResponse) GetData() *AdResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	K string `protobuf:"bytes,1,opt,name=k,proto3" json:"k,omitempty"` //必填，扩展字段
	V string `protobuf:"bytes,2,opt,name=v,proto3" json:"v,omitempty"` //必填，扩展字段值
}

func (x *Ext) Reset() {
	*x = Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ext) ProtoMessage() {}

func (x *Ext) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ext.ProtoReflect.Descriptor instead.
func (*Ext) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{4}
}

func (x *Ext) GetK() string {
	if x != nil {
		return x.K
	}
	return ""
}

func (x *Ext) GetV() string {
	if x != nil {
		return x.V
	}
	return ""
}

type PriceRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua                *string                `protobuf:"bytes,1,opt,name=ua,proto3,oneof" json:"ua,omitempty"`                                               //选填，http 请求协议 user-agent
	Make              *string                `protobuf:"bytes,2,opt,name=make,proto3,oneof" json:"make,omitempty"`                                           //选填，设备厂商
	Brand             *string                `protobuf:"bytes,3,opt,name=brand,proto3,oneof" json:"brand,omitempty"`                                         //选填，品牌
	Model             *string                `protobuf:"bytes,4,opt,name=model,proto3,oneof" json:"model,omitempty"`                                         //选填，设备型号
	ImeiMd5           string                 `protobuf:"bytes,5,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`                            //必填，硬件设备imei md5值
	Oaid              *string                `protobuf:"bytes,6,opt,name=oaid,proto3,oneof" json:"oaid,omitempty"`                                           //选填，Android设备的oaid原值
	OaidMd5           string                 `protobuf:"bytes,7,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`                            //必填，Android设备的oaid md5值
	Idfa              *string                `protobuf:"bytes,8,opt,name=idfa,proto3,oneof" json:"idfa,omitempty"`                                           //选填，IOS设备的idfa原值
	IdfaMd5           string                 `protobuf:"bytes,9,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`                            //必填，IOS设备的idfa md5值
	Os                PriceRequest_Device_Os `protobuf:"varint,10,opt,name=os,proto3,enum=qimaoadx0104.PriceRequest_Device_Os" json:"os,omitempty"`          //必填，设备操作系统 1：安卓 2:Ios
	Osv               *string                `protobuf:"bytes,11,opt,name=osv,proto3,oneof" json:"osv,omitempty"`                                            //选填，操作系统版本
	W                 *uint32                `protobuf:"varint,12,opt,name=w,proto3,oneof" json:"w,omitempty"`                                               //选填，屏幕宽，像素
	H                 *uint32                `protobuf:"varint,13,opt,name=h,proto3,oneof" json:"h,omitempty"`                                               //选填，屏幕高，像素
	Ext               []*Ext                 `protobuf:"bytes,14,rep,name=ext,proto3" json:"ext,omitempty"`                                                  //选填，存储双方约定的其他信息
	Idfv              *string                `protobuf:"bytes,15,opt,name=idfv,proto3,oneof" json:"idfv,omitempty"`                                          //选填，iOS设备的idfv原值
	AndroidId         *string                `protobuf:"bytes,16,opt,name=android_id,json=androidId,proto3,oneof" json:"android_id,omitempty"`               //选填，androidid原值
	Usecret           *string                `protobuf:"bytes,17,opt,name=usecret,proto3,oneof" json:"usecret,omitempty"`                                    //选填，caid值
	Usecretversion    *string                `protobuf:"bytes,18,opt,name=usecretversion,proto3,oneof" json:"usecretversion,omitempty"`                      //选填，caid version值
	Preusecret        *string                `protobuf:"bytes,19,opt,name=preusecret,proto3,oneof" json:"preusecret,omitempty"`                              //选填，上个版本caid值
	Preusecretversion *string                `protobuf:"bytes,20,opt,name=preusecretversion,proto3,oneof" json:"preusecretversion,omitempty"`                //选填，上个版本caid version值
	Mac               *string                `protobuf:"bytes,21,opt,name=mac,proto3,oneof" json:"mac,omitempty"`                                            //选填，设备mac地址，⽰例：FA:1D:42:DB:25:F1
	Hwoaid            *string                `protobuf:"bytes,22,opt,name=hwoaid,proto3,oneof" json:"hwoaid,omitempty"`                                      //选填，荣耀老方式获取的oaid（新方式获取的会放入oaid中）
	Dpi               *int32                 `protobuf:"varint,23,opt,name=dpi,proto3,oneof" json:"dpi,omitempty"`                                           //选填，屏幕像素密度
	BootMark          string                 `protobuf:"bytes,24,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`                        // 系统启动标识
	UpdateMark        string                 `protobuf:"bytes,25,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`                  // 系统更新标识
	PddBootMark       string                 `protobuf:"bytes,26,opt,name=pdd_boot_mark,json=pddBootMark,proto3" json:"pdd_boot_mark,omitempty"`             // 拼多多启动标识
	PddUpdateMark     string                 `protobuf:"bytes,27,opt,name=pdd_update_mark,json=pddUpdateMark,proto3" json:"pdd_update_mark,omitempty"`       // 拼多多更新标识
	BirthTime         string                 `protobuf:"bytes,28,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`                     // 启动时间
	AppStoreVersion   string                 `protobuf:"bytes,29,opt,name=app_store_version,json=appStoreVersion,proto3" json:"app_store_version,omitempty"` // 相关的App Store版本，如oppo，vivo等
	HmsCoreVersion    string                 `protobuf:"bytes,30,opt,name=hms_core_version,json=hmsCoreVersion,proto3" json:"hms_core_version,omitempty"`    // 鸿蒙内核版本
}

func (x *PriceRequest_Device) Reset() {
	*x = PriceRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest_Device) ProtoMessage() {}

func (x *PriceRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest_Device.ProtoReflect.Descriptor instead.
func (*PriceRequest_Device) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PriceRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *PriceRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *PriceRequest_Device) GetBrand() string {
	if x != nil && x.Brand != nil {
		return *x.Brand
	}
	return ""
}

func (x *PriceRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *PriceRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *PriceRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *PriceRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *PriceRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *PriceRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *PriceRequest_Device) GetOs() PriceRequest_Device_Os {
	if x != nil {
		return x.Os
	}
	return PriceRequest_Device_OsUnknown
}

func (x *PriceRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *PriceRequest_Device) GetW() uint32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *PriceRequest_Device) GetH() uint32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *PriceRequest_Device) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PriceRequest_Device) GetIdfv() string {
	if x != nil && x.Idfv != nil {
		return *x.Idfv
	}
	return ""
}

func (x *PriceRequest_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *PriceRequest_Device) GetUsecret() string {
	if x != nil && x.Usecret != nil {
		return *x.Usecret
	}
	return ""
}

func (x *PriceRequest_Device) GetUsecretversion() string {
	if x != nil && x.Usecretversion != nil {
		return *x.Usecretversion
	}
	return ""
}

func (x *PriceRequest_Device) GetPreusecret() string {
	if x != nil && x.Preusecret != nil {
		return *x.Preusecret
	}
	return ""
}

func (x *PriceRequest_Device) GetPreusecretversion() string {
	if x != nil && x.Preusecretversion != nil {
		return *x.Preusecretversion
	}
	return ""
}

func (x *PriceRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *PriceRequest_Device) GetHwoaid() string {
	if x != nil && x.Hwoaid != nil {
		return *x.Hwoaid
	}
	return ""
}

func (x *PriceRequest_Device) GetDpi() int32 {
	if x != nil && x.Dpi != nil {
		return *x.Dpi
	}
	return 0
}

func (x *PriceRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *PriceRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *PriceRequest_Device) GetPddBootMark() string {
	if x != nil {
		return x.PddBootMark
	}
	return ""
}

func (x *PriceRequest_Device) GetPddUpdateMark() string {
	if x != nil {
		return x.PddUpdateMark
	}
	return ""
}

func (x *PriceRequest_Device) GetBirthTime() string {
	if x != nil {
		return x.BirthTime
	}
	return ""
}

func (x *PriceRequest_Device) GetAppStoreVersion() string {
	if x != nil {
		return x.AppStoreVersion
	}
	return ""
}

func (x *PriceRequest_Device) GetHmsCoreVersion() string {
	if x != nil {
		return x.HmsCoreVersion
	}
	return ""
}

type PriceRequest_Media struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid     string  `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`               //必填，dsp分配的appid
	Name      string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                 //必填，媒体名称
	Bundle    string  `protobuf:"bytes,3,opt,name=bundle,proto3" json:"bundle,omitempty"`             //必填，app包名
	Publisher *string `protobuf:"bytes,4,opt,name=publisher,proto3,oneof" json:"publisher,omitempty"` //选填，dsp分配的开发者id
	Ext       []*Ext  `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty"`                   //选填，存储双方约定的其他信息
}

func (x *PriceRequest_Media) Reset() {
	*x = PriceRequest_Media{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest_Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest_Media) ProtoMessage() {}

func (x *PriceRequest_Media) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest_Media.ProtoReflect.Descriptor instead.
func (*PriceRequest_Media) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PriceRequest_Media) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *PriceRequest_Media) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PriceRequest_Media) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *PriceRequest_Media) GetPublisher() string {
	if x != nil && x.Publisher != nil {
		return *x.Publisher
	}
	return ""
}

func (x *PriceRequest_Media) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type PriceRequest_Pos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string                          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                 //必填，广告位标识id，由dsp分配
	Floor         uint64                          `protobuf:"varint,2,opt,name=floor,proto3" json:"floor,omitempty"`                                                                                          //必填，底价（单位分）
	AdType        *AdType                         `protobuf:"varint,3,opt,name=ad_type,json=adType,proto3,enum=qimaoadx0104.AdType,oneof" json:"ad_type,omitempty"`                                           //选填，广告位类型 1信息流；2banner；3开屏；4激励视频
	W             uint32                          `protobuf:"varint,4,opt,name=w,proto3" json:"w,omitempty"`                                                                                                  //必填，七猫广告位宽，像素
	H             uint32                          `protobuf:"varint,5,opt,name=h,proto3" json:"h,omitempty"`                                                                                                  //必填，七猫广告位高，像素
	MaterialType  []PriceRequest_Pos_MaterialType `protobuf:"varint,6,rep,packed,name=material_type,json=materialType,proto3,enum=qimaoadx0104.PriceRequest_Pos_MaterialType" json:"material_type,omitempty"` //必填，媒体支持的素材类型  1图片+文字；2视频+封面+文字
	SupportAction []AdsAction                     `protobuf:"varint,7,rep,packed,name=support_action,json=supportAction,proto3,enum=qimaoadx0104.AdsAction" json:"support_action,omitempty"`                  //必填，
	Ext           []*Ext                          `protobuf:"bytes,8,rep,name=ext,proto3" json:"ext,omitempty"`                                                                                               //选填，存储双方约定的其他信息
	DealId        string                          `protobuf:"bytes,9,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`                                                                           //选填，PDB订单ID
}

func (x *PriceRequest_Pos) Reset() {
	*x = PriceRequest_Pos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest_Pos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest_Pos) ProtoMessage() {}

func (x *PriceRequest_Pos) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest_Pos.ProtoReflect.Descriptor instead.
func (*PriceRequest_Pos) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 2}
}

func (x *PriceRequest_Pos) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PriceRequest_Pos) GetFloor() uint64 {
	if x != nil {
		return x.Floor
	}
	return 0
}

func (x *PriceRequest_Pos) GetAdType() AdType {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return AdType_AdTypeUnknown
}

func (x *PriceRequest_Pos) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *PriceRequest_Pos) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *PriceRequest_Pos) GetMaterialType() []PriceRequest_Pos_MaterialType {
	if x != nil {
		return x.MaterialType
	}
	return nil
}

func (x *PriceRequest_Pos) GetSupportAction() []AdsAction {
	if x != nil {
		return x.SupportAction
	}
	return nil
}

func (x *PriceRequest_Pos) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PriceRequest_Pos) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

type PriceRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender *PriceRequest_User_Gender `protobuf:"varint,1,opt,name=gender,proto3,enum=qimaoadx0104.PriceRequest_User_Gender,oneof" json:"gender,omitempty"` //必填，用户性别  1男  2女 0未知
	Ext    []*Ext                    `protobuf:"bytes,2,rep,name=ext,proto3" json:"ext,omitempty"`                                                         // 选填，存储双方约定的其他信息
	Pp     string                    `protobuf:"bytes,3,opt,name=pp,proto3" json:"pp,omitempty"`                                                           // 预留字段
}

func (x *PriceRequest_User) Reset() {
	*x = PriceRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest_User) ProtoMessage() {}

func (x *PriceRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest_User.ProtoReflect.Descriptor instead.
func (*PriceRequest_User) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 3}
}

func (x *PriceRequest_User) GetGender() PriceRequest_User_Gender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return PriceRequest_User_unknown
}

func (x *PriceRequest_User) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PriceRequest_User) GetPp() string {
	if x != nil {
		return x.Pp
	}
	return ""
}

type PriceRequest_Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Carrier        *PriceRequest_Network_Carrier  `protobuf:"varint,1,opt,name=carrier,proto3,enum=qimaoadx0104.PriceRequest_Network_Carrier,oneof" json:"carrier,omitempty"`                                      //选填，运营商类型：0未知 1移动 2联通 3电信
	ConnectionType *PriceRequest_Network_ConnType `protobuf:"varint,2,opt,name=connection_type,json=connectionType,proto3,enum=qimaoadx0104.PriceRequest_Network_ConnType,oneof" json:"connection_type,omitempty"` //选填，网络连接类型
	Ext            []*Ext                         `protobuf:"bytes,3,rep,name=ext,proto3" json:"ext,omitempty"`                                                                                                    //选填，存储双方约定的其他信息
}

func (x *PriceRequest_Network) Reset() {
	*x = PriceRequest_Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceRequest_Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceRequest_Network) ProtoMessage() {}

func (x *PriceRequest_Network) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceRequest_Network.ProtoReflect.Descriptor instead.
func (*PriceRequest_Network) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{0, 4}
}

func (x *PriceRequest_Network) GetCarrier() PriceRequest_Network_Carrier {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return PriceRequest_Network_CarrierUnknown
}

func (x *PriceRequest_Network) GetConnectionType() PriceRequest_Network_ConnType {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return PriceRequest_Network_ConnTypeUnknown
}

func (x *PriceRequest_Network) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type PriceResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string                        `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` //必填，标识请求的唯一id
	Price     int32                         `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`                         //必填，价格（单位分）
	Lnurl     []string                      `protobuf:"bytes,3,rep,name=lnurl,proto3" json:"lnurl,omitempty"`                          //必填，竞胜/竞败的上报url，通过替换lnurl字段中的{AUCTION}来表明竞价结果 0竞败  1竞胜
	Token     string                        `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`                          //必填，获取广告创意的唯一标识符
	Ext       []*Ext                        `protobuf:"bytes,5,rep,name=ext,proto3" json:"ext,omitempty"`                              //选填，存储双方约定的其他信息
	Ads       *PriceResponse_Data_Ads       `protobuf:"bytes,6,opt,name=ads,proto3" json:"ads,omitempty"`                              //选填，开屏对应广告素材
	TrackUrls *PriceResponse_Data_TrackUrls `protobuf:"bytes,7,opt,name=track_urls,json=trackUrls,proto3" json:"track_urls,omitempty"` //选填，上报urls
}

func (x *PriceResponse_Data) Reset() {
	*x = PriceResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data) ProtoMessage() {}

func (x *PriceResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0}
}

func (x *PriceResponse_Data) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PriceResponse_Data) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *PriceResponse_Data) GetLnurl() []string {
	if x != nil {
		return x.Lnurl
	}
	return nil
}

func (x *PriceResponse_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *PriceResponse_Data) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *PriceResponse_Data) GetAds() *PriceResponse_Data_Ads {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *PriceResponse_Data) GetTrackUrls() *PriceResponse_Data_TrackUrls {
	if x != nil {
		return x.TrackUrls
	}
	return nil
}

type PriceResponse_Data_Ads struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action         AdsAction                       `protobuf:"varint,1,opt,name=action,proto3,enum=qimaoadx0104.AdsAction" json:"action,omitempty"`            //必填，跳转类型 1webview 2app download 3deeplink
	Image          []*PriceResponse_Data_Ads_Image `protobuf:"bytes,2,rep,name=image,proto3" json:"image,omitempty"`                                           //选填，图片素材
	Video          *PriceResponse_Data_Ads_Video   `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`                                           //选填，视频素材
	TargetUrl      string                          `protobuf:"bytes,4,opt,name=target_url,json=targetUrl,proto3" json:"target_url,omitempty"`                  //选填，广告/创意的落地页url地址
	ButtonText     string                          `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`               //选填，按钮文案
	Title          string                          `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`                                           //选填，广告标签
	Desc           string                          `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`                                             //选填，广告描述
	Icon           *PriceResponse_Data_Ads_Icon    `protobuf:"bytes,8,opt,name=icon,proto3" json:"icon,omitempty"`                                             //选填，创意中的icon的url
	Deeplink       string                          `protobuf:"bytes,9,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                     //选填，deeplink跳转地址
	AppInfo        *PriceResponse_Data_Ads_AppInfo `protobuf:"bytes,10,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`                       //选填，下载类广告必传
	Source         string                          `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`                                        //选填，落地页的来源
	Aid            string                          `protobuf:"bytes,12,opt,name=aid,proto3" json:"aid,omitempty"`                                              //选填，广告id
	Cid            string                          `protobuf:"bytes,13,opt,name=cid,proto3" json:"cid,omitempty"`                                              //选填，创意id
	Did            string                          `protobuf:"bytes,14,opt,name=did,proto3" json:"did,omitempty"`                                              //选填，计划id
	Applet         *PriceResponse_Data_Ads_Applet  `protobuf:"bytes,15,opt,name=applet,proto3" json:"applet,omitempty"`                                        //选填，小程序
	RewardDuration int32                           `protobuf:"varint,16,opt,name=reward_duration,json=rewardDuration,proto3" json:"reward_duration,omitempty"` //选填，激励视频奖励发放条件，单位为秒。用于约定video_reward上报的时机。不回传默认为20秒：当视频长度大于20秒时，播放超过20秒后上报，具体时长可通过__DURATION__宏来确定，不足20秒的视频播放完毕后上报。
}

func (x *PriceResponse_Data_Ads) Reset() {
	*x = PriceResponse_Data_Ads{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads) ProtoMessage() {}

func (x *PriceResponse_Data_Ads) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *PriceResponse_Data_Ads) GetAction() AdsAction {
	if x != nil {
		return x.Action
	}
	return AdsAction_AdsActionUnknown
}

func (x *PriceResponse_Data_Ads) GetImage() []*PriceResponse_Data_Ads_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *PriceResponse_Data_Ads) GetVideo() *PriceResponse_Data_Ads_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *PriceResponse_Data_Ads) GetTargetUrl() string {
	if x != nil {
		return x.TargetUrl
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetButtonText() string {
	if x != nil {
		return x.ButtonText
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetIcon() *PriceResponse_Data_Ads_Icon {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *PriceResponse_Data_Ads) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetAppInfo() *PriceResponse_Data_Ads_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *PriceResponse_Data_Ads) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetAid() string {
	if x != nil {
		return x.Aid
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetDid() string {
	if x != nil {
		return x.Did
	}
	return ""
}

func (x *PriceResponse_Data_Ads) GetApplet() *PriceResponse_Data_Ads_Applet {
	if x != nil {
		return x.Applet
	}
	return nil
}

func (x *PriceResponse_Data_Ads) GetRewardDuration() int32 {
	if x != nil {
		return x.RewardDuration
	}
	return 0
}

type PriceResponse_Data_TrackUrls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposeUrls       []string `protobuf:"bytes,1,rep,name=expose_urls,json=exposeUrls,proto3" json:"expose_urls,omitempty"`                    //选填，曝光上报
	ClickUrls        []string `protobuf:"bytes,2,rep,name=click_urls,json=clickUrls,proto3" json:"click_urls,omitempty"`                       //选填，点击上报
	DownloadStart    []string `protobuf:"bytes,3,rep,name=download_start,json=downloadStart,proto3" json:"download_start,omitempty"`           //选填，开始下载上报
	DownloadFinish   []string `protobuf:"bytes,4,rep,name=download_finish,json=downloadFinish,proto3" json:"download_finish,omitempty"`        //选填，完成下载上报
	InstallStart     []string `protobuf:"bytes,5,rep,name=install_start,json=installStart,proto3" json:"install_start,omitempty"`              //选填，开始安装上报
	InstallFinish    []string `protobuf:"bytes,6,rep,name=install_finish,json=installFinish,proto3" json:"install_finish,omitempty"`           //选填，安装完成上报
	DeeplinkSuccess  []string `protobuf:"bytes,7,rep,name=deeplink_success,json=deeplinkSuccess,proto3" json:"deeplink_success,omitempty"`     //选填，deeplink调起成功上报
	DeeplinkFail     []string `protobuf:"bytes,8,rep,name=deeplink_fail,json=deeplinkFail,proto3" json:"deeplink_fail,omitempty"`              //选填，deeplink调起失败上报
	VideoPlay0       []string `protobuf:"bytes,9,rep,name=video_play0,json=videoPlay0,proto3" json:"video_play0,omitempty"`                    //选填，视频播放进度上报，播放开始
	VideoPlay1       []string `protobuf:"bytes,10,rep,name=video_play1,json=videoPlay1,proto3" json:"video_play1,omitempty"`                   //选填，视频播放进度上报，播放25%
	VideoPlay2       []string `protobuf:"bytes,11,rep,name=video_play2,json=videoPlay2,proto3" json:"video_play2,omitempty"`                   //选填，视频播放进度上报，播放50%
	VideoPlay3       []string `protobuf:"bytes,12,rep,name=video_play3,json=videoPlay3,proto3" json:"video_play3,omitempty"`                   //选填，视频播放进度上报，播放75%
	VideoPlay4       []string `protobuf:"bytes,13,rep,name=video_play4,json=videoPlay4,proto3" json:"video_play4,omitempty"`                   //选填，视频播放进度上报，播放完成
	VideoReward      []string `protobuf:"bytes,14,rep,name=video_reward,json=videoReward,proto3" json:"video_reward,omitempty"`                //选填，激励视频奖励发放上报
	VideoSkip        []string `protobuf:"bytes,15,rep,name=video_skip,json=videoSkip,proto3" json:"video_skip,omitempty"`                      //选填，激励视频跳过上报
	AppletcallupSucc []string `protobuf:"bytes,16,rep,name=appletcallup_succ,json=appletcallupSucc,proto3" json:"appletcallup_succ,omitempty"` //选填，小程序调起成功
	AppletcallupFail []string `protobuf:"bytes,17,rep,name=appletcallup_fail,json=appletcallupFail,proto3" json:"appletcallup_fail,omitempty"` //选填，小程序调起失败
}

func (x *PriceResponse_Data_TrackUrls) Reset() {
	*x = PriceResponse_Data_TrackUrls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_TrackUrls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_TrackUrls) ProtoMessage() {}

func (x *PriceResponse_Data_TrackUrls) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_TrackUrls.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_TrackUrls) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *PriceResponse_Data_TrackUrls) GetExposeUrls() []string {
	if x != nil {
		return x.ExposeUrls
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetDownloadStart() []string {
	if x != nil {
		return x.DownloadStart
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetDownloadFinish() []string {
	if x != nil {
		return x.DownloadFinish
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetInstallStart() []string {
	if x != nil {
		return x.InstallStart
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetInstallFinish() []string {
	if x != nil {
		return x.InstallFinish
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetDeeplinkSuccess() []string {
	if x != nil {
		return x.DeeplinkSuccess
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetDeeplinkFail() []string {
	if x != nil {
		return x.DeeplinkFail
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoPlay0() []string {
	if x != nil {
		return x.VideoPlay0
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoPlay1() []string {
	if x != nil {
		return x.VideoPlay1
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoPlay2() []string {
	if x != nil {
		return x.VideoPlay2
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoPlay3() []string {
	if x != nil {
		return x.VideoPlay3
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoPlay4() []string {
	if x != nil {
		return x.VideoPlay4
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoReward() []string {
	if x != nil {
		return x.VideoReward
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetVideoSkip() []string {
	if x != nil {
		return x.VideoSkip
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetAppletcallupSucc() []string {
	if x != nil {
		return x.AppletcallupSucc
	}
	return nil
}

func (x *PriceResponse_Data_TrackUrls) GetAppletcallupFail() []string {
	if x != nil {
		return x.AppletcallupFail
	}
	return nil
}

type PriceResponse_Data_Ads_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                      //必填，应用名称
	AppPermission  string `protobuf:"bytes,2,opt,name=app_permission,json=appPermission,proto3" json:"app_permission,omitempty"`    //必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
	AppPrivacy     string `protobuf:"bytes,3,opt,name=app_privacy,json=appPrivacy,proto3" json:"app_privacy,omitempty"`             //必填，应用隐私政策url
	AppSize        uint64 `protobuf:"varint,4,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`                     //必填，应用包的大小，单位byte
	AppVersion     string `protobuf:"bytes,5,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`             //必填，应用版本
	Developer      string `protobuf:"bytes,6,opt,name=developer,proto3" json:"developer,omitempty"`                                 //必填，app开发者
	DownloadUrl    string `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`          //必填，应用下载url
	PackageName    string `protobuf:"bytes,8,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`          //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
	AppDescription string `protobuf:"bytes,9,opt,name=app_description,json=appDescription,proto3" json:"app_description,omitempty"` //必填，app功能介绍
	AppMd5         string `protobuf:"bytes,10,opt,name=app_md5,json=appMd5,proto3" json:"app_md5,omitempty"`                        // 非必填 md5，下发给客户端校验
}

func (x *PriceResponse_Data_Ads_AppInfo) Reset() {
	*x = PriceResponse_Data_Ads_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads_AppInfo) ProtoMessage() {}

func (x *PriceResponse_Data_Ads_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads_AppInfo.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads_AppInfo) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppPermission() string {
	if x != nil {
		return x.AppPermission
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppPrivacy() string {
	if x != nil {
		return x.AppPrivacy
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppSize() uint64 {
	if x != nil {
		return x.AppSize
	}
	return 0
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppDescription() string {
	if x != nil {
		return x.AppDescription
	}
	return ""
}

func (x *PriceResponse_Data_Ads_AppInfo) GetAppMd5() string {
	if x != nil {
		return x.AppMd5
	}
	return ""
}

type PriceResponse_Data_Ads_Applet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalId string `protobuf:"bytes,1,opt,name=original_id,json=originalId,proto3" json:"original_id,omitempty"` //必填，小程序原始id
	Path       string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`                               //必填，小程序调起路径
}

func (x *PriceResponse_Data_Ads_Applet) Reset() {
	*x = PriceResponse_Data_Ads_Applet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads_Applet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads_Applet) ProtoMessage() {}

func (x *PriceResponse_Data_Ads_Applet) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads_Applet.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads_Applet) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *PriceResponse_Data_Ads_Applet) GetOriginalId() string {
	if x != nil {
		return x.OriginalId
	}
	return ""
}

func (x *PriceResponse_Data_Ads_Applet) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type PriceResponse_Data_Ads_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *PriceResponse_Data_Ads_Image) Reset() {
	*x = PriceResponse_Data_Ads_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads_Image) ProtoMessage() {}

func (x *PriceResponse_Data_Ads_Image) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads_Image.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads_Image) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0, 2}
}

func (x *PriceResponse_Data_Ads_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PriceResponse_Data_Ads_Image) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Image) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type PriceResponse_Data_Ads_Icon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *PriceResponse_Data_Ads_Icon) Reset() {
	*x = PriceResponse_Data_Ads_Icon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads_Icon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads_Icon) ProtoMessage() {}

func (x *PriceResponse_Data_Ads_Icon) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads_Icon.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads_Icon) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0, 3}
}

func (x *PriceResponse_Data_Ads_Icon) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PriceResponse_Data_Ads_Icon) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Icon) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type PriceResponse_Data_Ads_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverUrl string `protobuf:"bytes,1,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填，视频封面图
	Duration uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`                //必填，视频时长（单位秒）
	H        uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`                              //必填，视频高，像素
	W        uint32 `protobuf:"varint,4,opt,name=w,proto3" json:"w,omitempty"`                              //必填，视频宽，像素
	Size     uint64 `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`                        //必填，视频大小，单位size
	Url      string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                           //必填，视频地址
}

func (x *PriceResponse_Data_Ads_Video) Reset() {
	*x = PriceResponse_Data_Ads_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceResponse_Data_Ads_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceResponse_Data_Ads_Video) ProtoMessage() {}

func (x *PriceResponse_Data_Ads_Video) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceResponse_Data_Ads_Video.ProtoReflect.Descriptor instead.
func (*PriceResponse_Data_Ads_Video) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{1, 0, 0, 4}
}

func (x *PriceResponse_Data_Ads_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *PriceResponse_Data_Ads_Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Video) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Video) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Video) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PriceResponse_Data_Ads_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type AdResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string                     `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` //必填，标识请求的唯一id
	Aid       string                     `protobuf:"bytes,2,opt,name=aid,proto3" json:"aid,omitempty"`                              //必填，广告id
	Cid       string                     `protobuf:"bytes,3,opt,name=cid,proto3" json:"cid,omitempty"`                              //必填，创意id
	Token     string                     `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`                          //必填，获取广告素材的唯一标识符
	Ads       *AdResponse_Data_Ads       `protobuf:"bytes,5,opt,name=ads,proto3" json:"ads,omitempty"`                              //必填，广告物料
	TrackUrls *AdResponse_Data_TrackUrls `protobuf:"bytes,6,opt,name=track_urls,json=trackUrls,proto3" json:"track_urls,omitempty"` //选填，曝光、点击、下载、安装等监测事件上报地址
	Ext       []*Ext                     `protobuf:"bytes,7,rep,name=ext,proto3" json:"ext,omitempty"`                              //选填，存储双方约定的其他信息
}

func (x *AdResponse_Data) Reset() {
	*x = AdResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data) ProtoMessage() {}

func (x *AdResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data.ProtoReflect.Descriptor instead.
func (*AdResponse_Data) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AdResponse_Data) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AdResponse_Data) GetAid() string {
	if x != nil {
		return x.Aid
	}
	return ""
}

func (x *AdResponse_Data) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *AdResponse_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *AdResponse_Data) GetAds() *AdResponse_Data_Ads {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *AdResponse_Data) GetTrackUrls() *AdResponse_Data_TrackUrls {
	if x != nil {
		return x.TrackUrls
	}
	return nil
}

func (x *AdResponse_Data) GetExt() []*Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type AdResponse_Data_Ads struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action         AdsAction                    `protobuf:"varint,1,opt,name=action,proto3,enum=qimaoadx0104.AdsAction" json:"action,omitempty"`            //必填，跳转类型 1webview 2app download 3deeplink
	Image          []*AdResponse_Data_Ads_Image `protobuf:"bytes,2,rep,name=image,proto3" json:"image,omitempty"`                                           //选填，图片素材
	Video          *AdResponse_Data_Ads_Video   `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`                                           //选填，视频素材
	TargetUrl      string                       `protobuf:"bytes,4,opt,name=target_url,json=targetUrl,proto3" json:"target_url,omitempty"`                  //选填，广告/创意的落地页url地址
	ButtonText     string                       `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`               //选填，按钮文案
	Title          string                       `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`                                           //选填，广告标签
	Desc           string                       `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`                                             //选填，广告描述
	Icon           *AdResponse_Data_Ads_Icon    `protobuf:"bytes,8,opt,name=icon,proto3" json:"icon,omitempty"`                                             //选填，创意中的icon的url
	Deeplink       string                       `protobuf:"bytes,9,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                     //选填，deeplink跳转地址
	AppInfo        *AdResponse_Data_Ads_AppInfo `protobuf:"bytes,10,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`                       //选填，下载类广告必传
	Source         string                       `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`                                        //选填，落地页的来源
	Applet         *AdResponse_Data_Ads_Applet  `protobuf:"bytes,12,opt,name=applet,proto3" json:"applet,omitempty"`                                        //选填，小程序
	RewardDuration int32                        `protobuf:"varint,13,opt,name=reward_duration,json=rewardDuration,proto3" json:"reward_duration,omitempty"` //选填，激励视频奖励发放条件，单位为秒。用于约定video_reward上报的时机。不回传默认为20秒：当视频长度大于20秒时，播放超过20秒后上报，具体时长可通过__DURATION__宏来确定，不足20秒的视频播放完毕后上报。
}

func (x *AdResponse_Data_Ads) Reset() {
	*x = AdResponse_Data_Ads{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads) ProtoMessage() {}

func (x *AdResponse_Data_Ads) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *AdResponse_Data_Ads) GetAction() AdsAction {
	if x != nil {
		return x.Action
	}
	return AdsAction_AdsActionUnknown
}

func (x *AdResponse_Data_Ads) GetImage() []*AdResponse_Data_Ads_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *AdResponse_Data_Ads) GetVideo() *AdResponse_Data_Ads_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *AdResponse_Data_Ads) GetTargetUrl() string {
	if x != nil {
		return x.TargetUrl
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetButtonText() string {
	if x != nil {
		return x.ButtonText
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetIcon() *AdResponse_Data_Ads_Icon {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *AdResponse_Data_Ads) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetAppInfo() *AdResponse_Data_Ads_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *AdResponse_Data_Ads) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AdResponse_Data_Ads) GetApplet() *AdResponse_Data_Ads_Applet {
	if x != nil {
		return x.Applet
	}
	return nil
}

func (x *AdResponse_Data_Ads) GetRewardDuration() int32 {
	if x != nil {
		return x.RewardDuration
	}
	return 0
}

type AdResponse_Data_TrackUrls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposeUrls       []string `protobuf:"bytes,1,rep,name=expose_urls,json=exposeUrls,proto3" json:"expose_urls,omitempty"`                    //选填，曝光上报
	ClickUrls        []string `protobuf:"bytes,2,rep,name=click_urls,json=clickUrls,proto3" json:"click_urls,omitempty"`                       //选填，点击上报
	DownloadStart    []string `protobuf:"bytes,3,rep,name=download_start,json=downloadStart,proto3" json:"download_start,omitempty"`           //选填，开始下载上报
	DownloadFinish   []string `protobuf:"bytes,4,rep,name=download_finish,json=downloadFinish,proto3" json:"download_finish,omitempty"`        //选填，完成下载上报
	InstallStart     []string `protobuf:"bytes,5,rep,name=install_start,json=installStart,proto3" json:"install_start,omitempty"`              //选填，开始安装上报
	InstallFinish    []string `protobuf:"bytes,6,rep,name=install_finish,json=installFinish,proto3" json:"install_finish,omitempty"`           //选填，安装完成上报
	DeeplinkSuccess  []string `protobuf:"bytes,7,rep,name=deeplink_success,json=deeplinkSuccess,proto3" json:"deeplink_success,omitempty"`     //选填，deeplink调起成功上报
	DeeplinkFail     []string `protobuf:"bytes,8,rep,name=deeplink_fail,json=deeplinkFail,proto3" json:"deeplink_fail,omitempty"`              //选填，deeplink调起失败上报
	VideoPlay0       []string `protobuf:"bytes,9,rep,name=video_play0,json=videoPlay0,proto3" json:"video_play0,omitempty"`                    //选填，视频播放进度上报，播放开始
	VideoPlay1       []string `protobuf:"bytes,10,rep,name=video_play1,json=videoPlay1,proto3" json:"video_play1,omitempty"`                   //选填，视频播放进度上报，播放25%
	VideoPlay2       []string `protobuf:"bytes,11,rep,name=video_play2,json=videoPlay2,proto3" json:"video_play2,omitempty"`                   //选填，视频播放进度上报，播放50%
	VideoPlay3       []string `protobuf:"bytes,12,rep,name=video_play3,json=videoPlay3,proto3" json:"video_play3,omitempty"`                   //选填，视频播放进度上报，播放75%
	VideoPlay4       []string `protobuf:"bytes,13,rep,name=video_play4,json=videoPlay4,proto3" json:"video_play4,omitempty"`                   //选填，视频播放进度上报，播放完成
	VideoReward      []string `protobuf:"bytes,14,rep,name=video_reward,json=videoReward,proto3" json:"video_reward,omitempty"`                //选填，激励视频奖励发放上报
	VideoSkip        []string `protobuf:"bytes,15,rep,name=video_skip,json=videoSkip,proto3" json:"video_skip,omitempty"`                      //选填，激励视频跳过上报
	AppletcallupSucc []string `protobuf:"bytes,16,rep,name=appletcallup_succ,json=appletcallupSucc,proto3" json:"appletcallup_succ,omitempty"` //选填，小程序调起成功
	AppletcallupFail []string `protobuf:"bytes,17,rep,name=appletcallup_fail,json=appletcallupFail,proto3" json:"appletcallup_fail,omitempty"` //选填，小程序调起失败
}

func (x *AdResponse_Data_TrackUrls) Reset() {
	*x = AdResponse_Data_TrackUrls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_TrackUrls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_TrackUrls) ProtoMessage() {}

func (x *AdResponse_Data_TrackUrls) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_TrackUrls.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_TrackUrls) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 1}
}

func (x *AdResponse_Data_TrackUrls) GetExposeUrls() []string {
	if x != nil {
		return x.ExposeUrls
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetDownloadStart() []string {
	if x != nil {
		return x.DownloadStart
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetDownloadFinish() []string {
	if x != nil {
		return x.DownloadFinish
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetInstallStart() []string {
	if x != nil {
		return x.InstallStart
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetInstallFinish() []string {
	if x != nil {
		return x.InstallFinish
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetDeeplinkSuccess() []string {
	if x != nil {
		return x.DeeplinkSuccess
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetDeeplinkFail() []string {
	if x != nil {
		return x.DeeplinkFail
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoPlay0() []string {
	if x != nil {
		return x.VideoPlay0
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoPlay1() []string {
	if x != nil {
		return x.VideoPlay1
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoPlay2() []string {
	if x != nil {
		return x.VideoPlay2
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoPlay3() []string {
	if x != nil {
		return x.VideoPlay3
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoPlay4() []string {
	if x != nil {
		return x.VideoPlay4
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoReward() []string {
	if x != nil {
		return x.VideoReward
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetVideoSkip() []string {
	if x != nil {
		return x.VideoSkip
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetAppletcallupSucc() []string {
	if x != nil {
		return x.AppletcallupSucc
	}
	return nil
}

func (x *AdResponse_Data_TrackUrls) GetAppletcallupFail() []string {
	if x != nil {
		return x.AppletcallupFail
	}
	return nil
}

type AdResponse_Data_Ads_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName        string `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                      //必填，应用名称
	AppPermission  string `protobuf:"bytes,2,opt,name=app_permission,json=appPermission,proto3" json:"app_permission,omitempty"`    //必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
	AppPrivacy     string `protobuf:"bytes,3,opt,name=app_privacy,json=appPrivacy,proto3" json:"app_privacy,omitempty"`             //必填，应用隐私政策url
	AppSize        uint64 `protobuf:"varint,4,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`                     //必填，应用包的大小，单位byte
	AppVersion     string `protobuf:"bytes,5,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`             //必填，应用版本
	Developer      string `protobuf:"bytes,6,opt,name=developer,proto3" json:"developer,omitempty"`                                 //必填，app开发者
	DownloadUrl    string `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`          //必填，应用下载url
	PackageName    string `protobuf:"bytes,8,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`          //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
	AppDescription string `protobuf:"bytes,9,opt,name=app_description,json=appDescription,proto3" json:"app_description,omitempty"` //必填，app功能介绍
	AppMd5         string `protobuf:"bytes,10,opt,name=app_md5,json=appMd5,proto3" json:"app_md5,omitempty"`                        //必填，app功能介绍
}

func (x *AdResponse_Data_Ads_AppInfo) Reset() {
	*x = AdResponse_Data_Ads_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads_AppInfo) ProtoMessage() {}

func (x *AdResponse_Data_Ads_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads_AppInfo.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads_AppInfo) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0, 0}
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppPermission() string {
	if x != nil {
		return x.AppPermission
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppPrivacy() string {
	if x != nil {
		return x.AppPrivacy
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppSize() uint64 {
	if x != nil {
		return x.AppSize
	}
	return 0
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppDescription() string {
	if x != nil {
		return x.AppDescription
	}
	return ""
}

func (x *AdResponse_Data_Ads_AppInfo) GetAppMd5() string {
	if x != nil {
		return x.AppMd5
	}
	return ""
}

type AdResponse_Data_Ads_Applet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OriginalId string `protobuf:"bytes,1,opt,name=original_id,json=originalId,proto3" json:"original_id,omitempty"` //必填，小程序原始id
	Path       string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`                               //必填，小程序调起路径
}

func (x *AdResponse_Data_Ads_Applet) Reset() {
	*x = AdResponse_Data_Ads_Applet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads_Applet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads_Applet) ProtoMessage() {}

func (x *AdResponse_Data_Ads_Applet) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads_Applet.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads_Applet) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0, 1}
}

func (x *AdResponse_Data_Ads_Applet) GetOriginalId() string {
	if x != nil {
		return x.OriginalId
	}
	return ""
}

func (x *AdResponse_Data_Ads_Applet) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type AdResponse_Data_Ads_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *AdResponse_Data_Ads_Image) Reset() {
	*x = AdResponse_Data_Ads_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads_Image) ProtoMessage() {}

func (x *AdResponse_Data_Ads_Image) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads_Image.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads_Image) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0, 2}
}

func (x *AdResponse_Data_Ads_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AdResponse_Data_Ads_Image) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *AdResponse_Data_Ads_Image) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type AdResponse_Data_Ads_Icon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` //必填，图片地址
	W   uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`    //必填，宽度，像素
	H   uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`    //必填，高度，像素
}

func (x *AdResponse_Data_Ads_Icon) Reset() {
	*x = AdResponse_Data_Ads_Icon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads_Icon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads_Icon) ProtoMessage() {}

func (x *AdResponse_Data_Ads_Icon) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads_Icon.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads_Icon) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0, 3}
}

func (x *AdResponse_Data_Ads_Icon) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AdResponse_Data_Ads_Icon) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *AdResponse_Data_Ads_Icon) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

type AdResponse_Data_Ads_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverUrl string `protobuf:"bytes,1,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"` //必填，视频封面图
	Duration uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`                //必填，视频时长（单位秒）
	H        uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`                              //必填，视频高，像素
	W        uint32 `protobuf:"varint,4,opt,name=w,proto3" json:"w,omitempty"`                              //必填，视频宽，像素
	Size     uint64 `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`                        //必填，视频大小，单位size
	Url      string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                           //必填，视频地址
}

func (x *AdResponse_Data_Ads_Video) Reset() {
	*x = AdResponse_Data_Ads_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_qimao_1_4_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdResponse_Data_Ads_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdResponse_Data_Ads_Video) ProtoMessage() {}

func (x *AdResponse_Data_Ads_Video) ProtoReflect() protoreflect.Message {
	mi := &file_qimao_1_4_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdResponse_Data_Ads_Video.ProtoReflect.Descriptor instead.
func (*AdResponse_Data_Ads_Video) Descriptor() ([]byte, []int) {
	return file_qimao_1_4_proto_rawDescGZIP(), []int{3, 0, 0, 4}
}

func (x *AdResponse_Data_Ads_Video) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *AdResponse_Data_Ads_Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *AdResponse_Data_Ads_Video) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *AdResponse_Data_Ads_Video) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *AdResponse_Data_Ads_Video) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AdResponse_Data_Ads_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_qimao_1_4_proto protoreflect.FileDescriptor

var file_qimao_1_4_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x5f, 0x31, 0x2e, 0x34, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x22,
	0xa4, 0x18, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x61, 0x74, 0x12,
	0x24, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x36, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x30, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x6f, 0x73, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x12, 0x38, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f,
	0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x48, 0x01, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x48, 0x02, 0x52, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x49, 0x73, 0x54, 0x65, 0x73, 0x74, 0x48, 0x03, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6d, 0x61, 0x78, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x74, 0x6d, 0x61, 0x78, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30,
	0x31, 0x30, 0x34, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x71, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xa2, 0x09,
	0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x13, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x02, 0x75, 0x61, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x19, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x17, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x17, 0x0a, 0x04, 0x69,
	0x64, 0x66, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x04, 0x69, 0x64, 0x66,
	0x61, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12,
	0x34, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x71, 0x69,
	0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f,
	0x73, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x15, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x06, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x88, 0x01, 0x01, 0x12, 0x11, 0x0a, 0x01,
	0x77, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x07, 0x52, 0x01, 0x77, 0x88, 0x01, 0x01, 0x12,
	0x11, 0x0a, 0x01, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x08, 0x52, 0x01, 0x68, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x45,
	0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x07, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x0e, 0x75,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0e, 0x52, 0x11, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x88, 0x01, 0x01, 0x12,
	0x1b, 0x0a, 0x06, 0x68, 0x77, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x10, 0x52, 0x06, 0x68, 0x77, 0x6f, 0x61, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x64, 0x70, 0x69, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x48, 0x11, 0x52, 0x03, 0x64, 0x70, 0x69,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72,
	0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x64, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x64, 0x64, 0x42, 0x6f, 0x6f,
	0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x64, 0x64, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x64, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x6d, 0x73, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x68, 0x6d, 0x73, 0x43, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x2d, 0x0a, 0x02, 0x4f, 0x73, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x73, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x73, 0x41, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x73, 0x49, 0x6f, 0x73, 0x10,
	0x02, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x75, 0x61, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6d, 0x61, 0x6b,
	0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6f, 0x61, 0x69, 0x64, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x69, 0x64, 0x66, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6f, 0x73, 0x76, 0x42,
	0x04, 0x0a, 0x02, 0x5f, 0x77, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x68, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x69, 0x64, 0x66, 0x76, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x5f, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6d, 0x61, 0x63, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x68, 0x77, 0x6f, 0x61, 0x69, 0x64, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x64,
	0x70, 0x69, 0x1a, 0x9f, 0x01, 0x0a, 0x05, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x21,
	0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x88, 0x01,
	0x01, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x45, 0x78,
	0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x1a, 0x9e, 0x03, 0x0a, 0x03, 0x50, 0x6f, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x12, 0x32, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31,
	0x30, 0x34, 0x2e, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x06, 0x61, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x68, 0x12, 0x50, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x71, 0x69, 0x6d, 0x61,
	0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x0e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x71,
	0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34,
	0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c,
	0x49, 0x64, 0x22, 0x45, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x57, 0x6f, 0x72, 0x64, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x57, 0x6f, 0x72, 0x64, 0x10, 0x02, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xb8, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x43,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e,
	0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x70, 0x70, 0x22, 0x2b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x6d, 0x61, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x65, 0x6d,
	0x61, 0x6c, 0x65, 0x10, 0x02, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x1a, 0x86, 0x04, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x49, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x07, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2b, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x01, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x45,
	0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x22, 0x66, 0x0a, 0x07, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x43, 0x68, 0x69, 0x6e, 0x61, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x68, 0x69, 0x6e, 0x61, 0x55, 0x6e,
	0x69, 0x63, 0x6f, 0x6d, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x43, 0x68, 0x69, 0x6e, 0x61, 0x54, 0x65, 0x6c, 0x65, 0x63, 0x6f, 0x6d, 0x10, 0x03, 0x22,
	0xa7, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x65, 0x6c,
	0x6c, 0x75, 0x6c, 0x61, 0x72, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x32, 0x67, 0x10, 0x02, 0x12,
	0x16, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x65, 0x6c, 0x6c, 0x75,
	0x6c, 0x61, 0x72, 0x33, 0x67, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x34, 0x67, 0x10, 0x04, 0x12,
	0x16, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x65, 0x6c, 0x6c, 0x75,
	0x6c, 0x61, 0x72, 0x35, 0x67, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x57, 0x69, 0x66, 0x69, 0x10, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x1d, 0x0a, 0x06, 0x49, 0x73, 0x54,
	0x65, 0x73, 0x74, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x54, 0x72, 0x75, 0x65, 0x10, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x70, 0x69,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x22, 0x99, 0x12, 0x0a, 0x0d, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x34,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x71,
	0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x1a, 0x93, 0x11, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6e,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x6e, 0x75, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31,
	0x30, 0x34, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x36, 0x0a, 0x03, 0x61,
	0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f,
	0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x52, 0x03,
	0x61, 0x64, 0x73, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61,
	0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x73, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x1a, 0x86,
	0x0a, 0x0a, 0x03, 0x41, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64,
	0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64,
	0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f,
	0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3d, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31,
	0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x47, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30,
	0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x61, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x6c, 0x65,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61,
	0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x65, 0x74, 0x52, 0x06, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xce, 0x02, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x70, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a,
	0x07, 0x61, 0x70, 0x70, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x70, 0x70, 0x4d, 0x64, 0x35, 0x1a, 0x3d, 0x0a, 0x06, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x1a, 0x35, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c,
	0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x1a, 0x34, 0x0a, 0x04,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x68, 0x1a, 0x82, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x01, 0x68, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01,
	0x77, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0xf8, 0x04, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x46, 0x61, 0x69,
	0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x30,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61,
	0x79, 0x30, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79,
	0x31, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c,
	0x61, 0x79, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x32, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50,
	0x6c, 0x61, 0x79, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c,
	0x61, 0x79, 0x33, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x50, 0x6c, 0x61, 0x79, 0x33, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70,
	0x6c, 0x61, 0x79, 0x34, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x50, 0x6c, 0x61, 0x79, 0x34, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x53, 0x6b, 0x69, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c,
	0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75, 0x70, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x18, 0x10, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75,
	0x70, 0x53, 0x75, 0x63, 0x63, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63,
	0x61, 0x6c, 0x6c, 0x75, 0x70, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75, 0x70, 0x46, 0x61,
	0x69, 0x6c, 0x22, 0x64, 0x0a, 0x09, 0x41, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x22, 0xc3, 0x11, 0x0a, 0x0a, 0x41, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e,
	0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x1a, 0xc0, 0x10, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x61, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33, 0x0a, 0x03,
	0x61, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x71, 0x69, 0x6d, 0x61,
	0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x52, 0x03, 0x61, 0x64,
	0x73, 0x12, 0x46, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x65, 0x78, 0x74,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64,
	0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0xc1,
	0x09, 0x0a, 0x03, 0x41, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64,
	0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64,
	0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x3a, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x44, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x71, 0x69, 0x6d, 0x61,
	0x6f, 0x61, 0x64, 0x78, 0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x61, 0x64, 0x78,
	0x30, 0x31, 0x30, 0x34, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x64, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x52,
	0x06, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0xce, 0x02, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x4d, 0x64,
	0x35, 0x1a, 0x3d, 0x0a, 0x06, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x1a, 0x35, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x1a, 0x34, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12,
	0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x1a, 0x82, 0x01,
	0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x12, 0x0c,
	0x0a, 0x01, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x1a, 0xf8, 0x04, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x29, 0x0a, 0x10,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x30, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x30, 0x12, 0x1f, 0x0a,
	0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x31, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x31, 0x12, 0x1f,
	0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x32, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x32, 0x12,
	0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x33, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x33,
	0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x34, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79,
	0x34, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x6b,
	0x69, 0x70, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53,
	0x6b, 0x69, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c,
	0x6c, 0x75, 0x70, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75, 0x70, 0x53, 0x75, 0x63, 0x63,
	0x12, 0x2b, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75, 0x70,
	0x5f, 0x66, 0x61, 0x69, 0x6c, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x61, 0x70, 0x70,
	0x6c, 0x65, 0x74, 0x63, 0x61, 0x6c, 0x6c, 0x75, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x42, 0x06, 0x0a,
	0x04, 0x5f, 0x6d, 0x73, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x21,
	0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x01, 0x6b, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01,
	0x76, 0x2a, 0x50, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x41,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x70, 0x6c, 0x61, 0x73, 0x68,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x10, 0x04, 0x2a, 0x5c, 0x0a, 0x09, 0x41, 0x64, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x10, 0x41, 0x64, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x65, 0x62, 0x76, 0x69, 0x65,
	0x77, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x10, 0x03, 0x12,
	0x10, 0x0a, 0x0c, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x10,
	0x04, 0x42, 0x0a, 0x5a, 0x08, 0x2e, 0x2e, 0x2f, 0x71, 0x69, 0x6d, 0x61, 0x6f, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_qimao_1_4_proto_rawDescOnce sync.Once
	file_qimao_1_4_proto_rawDescData = file_qimao_1_4_proto_rawDesc
)

func file_qimao_1_4_proto_rawDescGZIP() []byte {
	file_qimao_1_4_proto_rawDescOnce.Do(func() {
		file_qimao_1_4_proto_rawDescData = protoimpl.X.CompressGZIP(file_qimao_1_4_proto_rawDescData)
	})
	return file_qimao_1_4_proto_rawDescData
}

var file_qimao_1_4_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_qimao_1_4_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_qimao_1_4_proto_goTypes = []interface{}{
	(AdType)(0),                            // 0: qimaoadx0104.AdType
	(AdsAction)(0),                         // 1: qimaoadx0104.AdsAction
	(PriceRequest_IsTest)(0),               // 2: qimaoadx0104.PriceRequest.IsTest
	(PriceRequest_Device_Os)(0),            // 3: qimaoadx0104.PriceRequest.Device.Os
	(PriceRequest_Pos_MaterialType)(0),     // 4: qimaoadx0104.PriceRequest.Pos.MaterialType
	(PriceRequest_User_Gender)(0),          // 5: qimaoadx0104.PriceRequest.User.Gender
	(PriceRequest_Network_Carrier)(0),      // 6: qimaoadx0104.PriceRequest.Network.Carrier
	(PriceRequest_Network_ConnType)(0),     // 7: qimaoadx0104.PriceRequest.Network.ConnType
	(*PriceRequest)(nil),                   // 8: qimaoadx0104.PriceRequest
	(*PriceResponse)(nil),                  // 9: qimaoadx0104.PriceResponse
	(*AdRequest)(nil),                      // 10: qimaoadx0104.AdRequest
	(*AdResponse)(nil),                     // 11: qimaoadx0104.AdResponse
	(*Ext)(nil),                            // 12: qimaoadx0104.Ext
	(*PriceRequest_Device)(nil),            // 13: qimaoadx0104.PriceRequest.Device
	(*PriceRequest_Media)(nil),             // 14: qimaoadx0104.PriceRequest.Media
	(*PriceRequest_Pos)(nil),               // 15: qimaoadx0104.PriceRequest.Pos
	(*PriceRequest_User)(nil),              // 16: qimaoadx0104.PriceRequest.User
	(*PriceRequest_Network)(nil),           // 17: qimaoadx0104.PriceRequest.Network
	(*PriceResponse_Data)(nil),             // 18: qimaoadx0104.PriceResponse.Data
	(*PriceResponse_Data_Ads)(nil),         // 19: qimaoadx0104.PriceResponse.Data.Ads
	(*PriceResponse_Data_TrackUrls)(nil),   // 20: qimaoadx0104.PriceResponse.Data.TrackUrls
	(*PriceResponse_Data_Ads_AppInfo)(nil), // 21: qimaoadx0104.PriceResponse.Data.Ads.AppInfo
	(*PriceResponse_Data_Ads_Applet)(nil),  // 22: qimaoadx0104.PriceResponse.Data.Ads.Applet
	(*PriceResponse_Data_Ads_Image)(nil),   // 23: qimaoadx0104.PriceResponse.Data.Ads.Image
	(*PriceResponse_Data_Ads_Icon)(nil),    // 24: qimaoadx0104.PriceResponse.Data.Ads.Icon
	(*PriceResponse_Data_Ads_Video)(nil),   // 25: qimaoadx0104.PriceResponse.Data.Ads.Video
	(*AdResponse_Data)(nil),                // 26: qimaoadx0104.AdResponse.Data
	(*AdResponse_Data_Ads)(nil),            // 27: qimaoadx0104.AdResponse.Data.Ads
	(*AdResponse_Data_TrackUrls)(nil),      // 28: qimaoadx0104.AdResponse.Data.TrackUrls
	(*AdResponse_Data_Ads_AppInfo)(nil),    // 29: qimaoadx0104.AdResponse.Data.Ads.AppInfo
	(*AdResponse_Data_Ads_Applet)(nil),     // 30: qimaoadx0104.AdResponse.Data.Ads.Applet
	(*AdResponse_Data_Ads_Image)(nil),      // 31: qimaoadx0104.AdResponse.Data.Ads.Image
	(*AdResponse_Data_Ads_Icon)(nil),       // 32: qimaoadx0104.AdResponse.Data.Ads.Icon
	(*AdResponse_Data_Ads_Video)(nil),      // 33: qimaoadx0104.AdResponse.Data.Ads.Video
}
var file_qimao_1_4_proto_depIdxs = []int32{
	13, // 0: qimaoadx0104.PriceRequest.device:type_name -> qimaoadx0104.PriceRequest.Device
	14, // 1: qimaoadx0104.PriceRequest.media:type_name -> qimaoadx0104.PriceRequest.Media
	15, // 2: qimaoadx0104.PriceRequest.pos:type_name -> qimaoadx0104.PriceRequest.Pos
	16, // 3: qimaoadx0104.PriceRequest.user:type_name -> qimaoadx0104.PriceRequest.User
	17, // 4: qimaoadx0104.PriceRequest.network:type_name -> qimaoadx0104.PriceRequest.Network
	2,  // 5: qimaoadx0104.PriceRequest.test:type_name -> qimaoadx0104.PriceRequest.IsTest
	12, // 6: qimaoadx0104.PriceRequest.ext:type_name -> qimaoadx0104.Ext
	18, // 7: qimaoadx0104.PriceResponse.data:type_name -> qimaoadx0104.PriceResponse.Data
	26, // 8: qimaoadx0104.AdResponse.data:type_name -> qimaoadx0104.AdResponse.Data
	3,  // 9: qimaoadx0104.PriceRequest.Device.os:type_name -> qimaoadx0104.PriceRequest.Device.Os
	12, // 10: qimaoadx0104.PriceRequest.Device.ext:type_name -> qimaoadx0104.Ext
	12, // 11: qimaoadx0104.PriceRequest.Media.ext:type_name -> qimaoadx0104.Ext
	0,  // 12: qimaoadx0104.PriceRequest.Pos.ad_type:type_name -> qimaoadx0104.AdType
	4,  // 13: qimaoadx0104.PriceRequest.Pos.material_type:type_name -> qimaoadx0104.PriceRequest.Pos.MaterialType
	1,  // 14: qimaoadx0104.PriceRequest.Pos.support_action:type_name -> qimaoadx0104.AdsAction
	12, // 15: qimaoadx0104.PriceRequest.Pos.ext:type_name -> qimaoadx0104.Ext
	5,  // 16: qimaoadx0104.PriceRequest.User.gender:type_name -> qimaoadx0104.PriceRequest.User.Gender
	12, // 17: qimaoadx0104.PriceRequest.User.ext:type_name -> qimaoadx0104.Ext
	6,  // 18: qimaoadx0104.PriceRequest.Network.carrier:type_name -> qimaoadx0104.PriceRequest.Network.Carrier
	7,  // 19: qimaoadx0104.PriceRequest.Network.connection_type:type_name -> qimaoadx0104.PriceRequest.Network.ConnType
	12, // 20: qimaoadx0104.PriceRequest.Network.ext:type_name -> qimaoadx0104.Ext
	12, // 21: qimaoadx0104.PriceResponse.Data.ext:type_name -> qimaoadx0104.Ext
	19, // 22: qimaoadx0104.PriceResponse.Data.ads:type_name -> qimaoadx0104.PriceResponse.Data.Ads
	20, // 23: qimaoadx0104.PriceResponse.Data.track_urls:type_name -> qimaoadx0104.PriceResponse.Data.TrackUrls
	1,  // 24: qimaoadx0104.PriceResponse.Data.Ads.action:type_name -> qimaoadx0104.AdsAction
	23, // 25: qimaoadx0104.PriceResponse.Data.Ads.image:type_name -> qimaoadx0104.PriceResponse.Data.Ads.Image
	25, // 26: qimaoadx0104.PriceResponse.Data.Ads.video:type_name -> qimaoadx0104.PriceResponse.Data.Ads.Video
	24, // 27: qimaoadx0104.PriceResponse.Data.Ads.icon:type_name -> qimaoadx0104.PriceResponse.Data.Ads.Icon
	21, // 28: qimaoadx0104.PriceResponse.Data.Ads.app_info:type_name -> qimaoadx0104.PriceResponse.Data.Ads.AppInfo
	22, // 29: qimaoadx0104.PriceResponse.Data.Ads.applet:type_name -> qimaoadx0104.PriceResponse.Data.Ads.Applet
	27, // 30: qimaoadx0104.AdResponse.Data.ads:type_name -> qimaoadx0104.AdResponse.Data.Ads
	28, // 31: qimaoadx0104.AdResponse.Data.track_urls:type_name -> qimaoadx0104.AdResponse.Data.TrackUrls
	12, // 32: qimaoadx0104.AdResponse.Data.ext:type_name -> qimaoadx0104.Ext
	1,  // 33: qimaoadx0104.AdResponse.Data.Ads.action:type_name -> qimaoadx0104.AdsAction
	31, // 34: qimaoadx0104.AdResponse.Data.Ads.image:type_name -> qimaoadx0104.AdResponse.Data.Ads.Image
	33, // 35: qimaoadx0104.AdResponse.Data.Ads.video:type_name -> qimaoadx0104.AdResponse.Data.Ads.Video
	32, // 36: qimaoadx0104.AdResponse.Data.Ads.icon:type_name -> qimaoadx0104.AdResponse.Data.Ads.Icon
	29, // 37: qimaoadx0104.AdResponse.Data.Ads.app_info:type_name -> qimaoadx0104.AdResponse.Data.Ads.AppInfo
	30, // 38: qimaoadx0104.AdResponse.Data.Ads.applet:type_name -> qimaoadx0104.AdResponse.Data.Ads.Applet
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_qimao_1_4_proto_init() }
func file_qimao_1_4_proto_init() {
	if File_qimao_1_4_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_qimao_1_4_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest_Media); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest_Pos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceRequest_Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_TrackUrls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads_Applet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads_Icon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceResponse_Data_Ads_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_TrackUrls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads_Applet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads_Icon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_qimao_1_4_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdResponse_Data_Ads_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_qimao_1_4_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_qimao_1_4_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_qimao_1_4_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_qimao_1_4_proto_goTypes,
		DependencyIndexes: file_qimao_1_4_proto_depIdxs,
		EnumInfos:         file_qimao_1_4_proto_enumTypes,
		MessageInfos:      file_qimao_1_4_proto_msgTypes,
	}.Build()
	File_qimao_1_4_proto = out.File
	file_qimao_1_4_proto_rawDesc = nil
	file_qimao_1_4_proto_goTypes = nil
	file_qimao_1_4_proto_depIdxs = nil
}
