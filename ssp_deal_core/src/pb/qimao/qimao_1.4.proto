syntax = "proto3";  //语法声明
package qimaoadx0104; //包名

option go_package = "mh_proxy/pb/qimao";

message PriceRequest {
  string request_id = 1; //必填，标识请求的唯一id
  uint32 at = 2;         //必填，竞价类型;1一价;2二价格；默认值1
  optional string api_version = 3;//选填，dsp对应的api version 信息
  Device device = 4;     //必填，设备信息
  Media media = 5;       //必填，媒体信息
  Pos pos = 6;           //必填，广告位信息列表，目前一次请求仅包含一个广告位
  optional User user = 7;//选填，用户信息，该设备对应的用户信息，投放受众
  optional Network  network = 8;//选填，用户网络环境
  string ip = 9;         //必填，设备的ip，用户粗略定位，定于定向
  optional IsTest test = 10;      //必填，是否测试广告：1是 0非
  uint32 tmax = 11;      //必填，最初等待时间单位ms，默认300ms
  repeated Ext ext = 12; //选填，存储双方约定的其他信息
  uint32 req_segment = 13; //必填，1.一段请求  2.二段请求（默认二段请求；二段请求为价格素材分开请求返回，一段请求为价格素材一次性返回）


  enum IsTest {
    False = 0;
    True = 1;
  }

  message Device {
    optional string ua = 1;    //选填，http 请求协议 user-agent
    optional string make = 2;  //选填，设备厂商
    optional string brand = 3; //选填，品牌
    optional string model = 4; //选填，设备型号
    string imei_md5 = 5;       //必填，硬件设备imei md5值
    optional string oaid = 6;  //选填，Android设备的oaid原值
    string oaid_md5 = 7;       //必填，Android设备的oaid md5值
    optional string idfa = 8;  //选填，IOS设备的idfa原值
    string idfa_md5 = 9;       //必填，IOS设备的idfa md5值
    Os os = 10;                //必填，设备操作系统 1：安卓 2:Ios
    optional string osv = 11;  //选填，操作系统版本
    optional uint32 w = 12;    //选填，屏幕宽，像素
    optional uint32 h = 13;    //选填，屏幕高，像素
    repeated Ext ext = 14;     //选填，存储双方约定的其他信息
    enum Os {
      OsUnknown = 0;
      OsAndroid = 1;     //安卓
      OsIos = 2;         //ios
    }
    optional string idfv = 15;          //选填，iOS设备的idfv原值
    optional string android_id = 16;    //选填，androidid原值
    optional string usecret = 17; //选填，caid值
    optional string usecretversion = 18; //选填，caid version值
    optional string preusecret = 19; //选填，上个版本caid值
    optional string preusecretversion = 20; //选填，上个版本caid version值
    optional string mac = 21; //选填，设备mac地址，⽰例：FA:1D:42:DB:25:F1
    optional string hwoaid = 22; //选填，荣耀老方式获取的oaid（新方式获取的会放入oaid中）
    optional int32 dpi = 23; //选填，屏幕像素密度
    string boot_mark = 24; // 系统启动标识
    string update_mark = 25; // 系统更新标识
    string pdd_boot_mark = 26; // 拼多多启动标识
    string pdd_update_mark = 27; // 拼多多更新标识
    string birth_time = 28; // 启动时间
    string app_store_version = 29; // 相关的App Store版本，如oppo，vivo等
    string hms_core_version = 30; // 鸿蒙内核版本
  }

  message Media {
    string appid = 1;      //必填，dsp分配的appid
    string name = 2;       //必填，媒体名称
    string bundle = 3;     //必填，app包名
    optional string publisher = 4;//选填，dsp分配的开发者id
    repeated Ext ext = 5;  //选填，存储双方约定的其他信息
  }

  message Pos {
    string id = 1;        //必填，广告位标识id，由dsp分配
    uint64 floor = 2;     //必填，底价（单位分）
    optional AdType ad_type = 3;//选填，广告位类型 1信息流；2banner；3开屏；4激励视频
    uint32 w = 4;         //必填，七猫广告位宽，像素
    uint32 h = 5;         //必填，七猫广告位高，像素
    repeated MaterialType material_type = 6;//必填，媒体支持的素材类型  1图片+文字；2视频+封面+文字
    repeated AdsAction support_action = 7;  //必填，
    repeated Ext ext = 8; //选填，存储双方约定的其他信息
    string deal_id = 9;   //选填，PDB订单ID

    enum MaterialType {
      MaterialTypeUnknown = 0;
      ImageWord = 1; //图片+文字
      VideoWord = 2; //视频+封面+文字
    }
  }

  message User {
    optional Gender gender = 1;   //必填，用户性别  1男  2女 0未知
    repeated Ext ext = 2;// 选填，存储双方约定的其他信息
    string pp = 3; // 预留字段
    enum Gender {
      unknown = 0; //未知
      male = 1;    //男
      female = 2;  //女
    }
  }

  message Network {
    optional Carrier carrier = 1;         //选填，运营商类型：0未知 1移动 2联通 3电信
    optional ConnType connection_type = 2;//选填，网络连接类型
    repeated Ext ext = 3;        //选填，存储双方约定的其他信息
    enum Carrier {
      CarrierUnknown = 0;    //未知
      CarrierChinaMobile = 1;//移动
      CarrierChinaUnicom = 2;//联通
      CarrierChinaTelecom = 3;//电信
    }
    enum ConnType {
      ConnTypeUnknown = 0;   //无法探测当前网络状态
      ConnTypeCellular = 1;  //蜂窝数据接入，未知网络类型
      ConnTypeCellular2g = 2;//蜂窝数据 2G 网络
      ConnTypeCellular3g = 3;//蜂窝数据 3G 网络
      ConnTypeCellular4g = 4;//蜂窝数据 4G 网络
      ConnTypeCellular5g = 5;//蜂窝数据 5G 网络
      ConnTypeWifi = 100;    //WIFI 网络接入
    }
  }
}



//广告报价接口返回对象Response
message PriceResponse {
  uint32 code = 1;      //必填，状态码，0成功
  string msg = 2;      //选填，报错信息(code为2，3该值必传)
  Data data = 3;       //选填，广告报价内容
  uint32 breaks = 4; // 选填，该用户熔断时长，单位秒，维度为广告位id(tagid)，0代表不熔断，理论最多熔断时长24h
  message Data {
    string request_id = 1;//必填，标识请求的唯一id
    int32 price = 2;      //必填，价格（单位分）
    repeated string lnurl = 3;     //必填，竞胜/竞败的上报url，通过替换lnurl字段中的{AUCTION}来表明竞价结果 0竞败  1竞胜
    string token = 4;     //必填，获取广告创意的唯一标识符
    repeated Ext ext = 5; //选填，存储双方约定的其他信息
    Ads ads = 6;  //选填，开屏对应广告素材
    message Ads {
      AdsAction action = 1;    //必填，跳转类型 1webview 2app download 3deeplink
      repeated Image image = 2;//选填，图片素材
      Video video = 3;         //选填，视频素材
      string target_url = 4;   //选填，广告/创意的落地页url地址
      string button_text = 5;  //选填，按钮文案
      string title = 6;        //选填，广告标签
      string desc = 7;         //选填，广告描述
      Icon icon = 8;           //选填，创意中的icon的url
      string deeplink = 9;     //选填，deeplink跳转地址
      AppInfo app_info = 10;   //选填，下载类广告必传
      string source = 11;      //选填，落地页的来源
      string aid = 12;          //选填，广告id
      string cid = 13;          //选填，创意id
      string did = 14;         //选填，计划id
      Applet applet = 15;      //选填，小程序
      int32 reward_duration = 16; //选填，激励视频奖励发放条件，单位为秒。用于约定video_reward上报的时机。不回传默认为20秒：当视频长度大于20秒时，播放超过20秒后上报，具体时长可通过__DURATION__宏来确定，不足20秒的视频播放完毕后上报。

      message AppInfo {
        string app_name = 1;      //必填，应用名称
        string app_permission = 2;//必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
        string app_privacy = 3;   //必填，应用隐私政策url
        uint64 app_size = 4;      //必填，应用包的大小，单位byte
        string app_version = 5;   //必填，应用版本
        string developer = 6;     //必填，app开发者
        string download_url = 7;  //必填，应用下载url
        string package_name = 8;  //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
        string app_description = 9; //必填，app功能介绍
        string app_md5 = 10;          // 非必填 md5，下发给客户端校验
      }

      message Applet {
        string original_id = 1; //必填，小程序原始id
        string path = 2; //必填，小程序调起路径
      }

      message Image {
        string url = 1;//必填，图片地址
        uint32 w = 2;  //必填，宽度，像素
        uint32 h = 3;  //必填，高度，像素
      }

      message Icon {
        string url = 1;//必填，图片地址
        uint32 w = 2;  //必填，宽度，像素
        uint32 h = 3;  //必填，高度，像素
      }

      message Video {
        string cover_url = 1;//必填，视频封面图
        uint32 duration = 2; //必填，视频时长（单位秒）
        uint32 h = 3;        //必填，视频高，像素
        uint32 w = 4;        //必填，视频宽，像素
        uint64 size = 5;     //必填，视频大小，单位size
        string url = 6;      //必填，视频地址
      }
    }
    TrackUrls track_urls = 7; //选填，上报urls
    message TrackUrls {
      repeated string expose_urls = 1;     //选填，曝光上报
      repeated string click_urls = 2;      //选填，点击上报
      repeated string download_start = 3;  //选填，开始下载上报
      repeated string download_finish = 4; //选填，完成下载上报
      repeated string install_start = 5;   //选填，开始安装上报
      repeated string install_finish = 6;  //选填，安装完成上报
      repeated string deeplink_success = 7;//选填，deeplink调起成功上报
      repeated string deeplink_fail = 8;   //选填，deeplink调起失败上报
      repeated string video_play0 = 9;     //选填，视频播放进度上报，播放开始
      repeated string video_play1 = 10;    //选填，视频播放进度上报，播放25%
      repeated string video_play2 = 11;    //选填，视频播放进度上报，播放50%
      repeated string video_play3 = 12;    //选填，视频播放进度上报，播放75%
      repeated string video_play4 = 13;    //选填，视频播放进度上报，播放完成
      repeated string video_reward = 14;   //选填，激励视频奖励发放上报
      repeated string video_skip = 15;     //选填，激励视频跳过上报
      repeated string appletcallup_succ = 16; //选填，小程序调起成功
      repeated string appletcallup_fail = 17; //选填，小程序调起失败
    }
  }

}

//广告素材接口请求对象Request
message AdRequest {
  string request_id = 1;//必填，标识请求的唯一id
  string token = 2;     //必填，获取广告创意的唯一标识符
  string sign = 3;      //必填，验证标识 加密方式详见【附录】
  string ts = 4;        //必填，毫秒时间戳
}

//广告素材接口返回对象Response
message AdResponse {
  uint32 code = 1;        //必填，状态码，0成功 具体列表见【附录】
  optional string msg = 2;//选填，报错信息 （code为2，3该值必传）
  optional Data data = 3; //选填，广告素材内容

  message Data {
    string request_id = 1;   //必填，标识请求的唯一id
    string aid = 2;          //必填，广告id
    string cid = 3;          //必填，创意id
    string token = 4;        //必填，获取广告素材的唯一标识符
    Ads ads = 5;             //必填，广告物料
    TrackUrls track_urls = 6;//选填，曝光、点击、下载、安装等监测事件上报地址
    repeated Ext ext = 7;    //选填，存储双方约定的其他信息

    message Ads {
      AdsAction action = 1;    //必填，跳转类型 1webview 2app download 3deeplink
      repeated Image image = 2;//选填，图片素材
      Video video = 3;         //选填，视频素材
      string target_url = 4;   //选填，广告/创意的落地页url地址
      string button_text = 5;  //选填，按钮文案
      string title = 6;        //选填，广告标签
      string desc = 7;         //选填，广告描述
      Icon icon = 8;           //选填，创意中的icon的url
      string deeplink = 9;     //选填，deeplink跳转地址
      AppInfo app_info = 10;   //选填，下载类广告必传
      string source = 11;      //选填，落地页的来源
      Applet applet = 12;      //选填，小程序
      int32 reward_duration = 13; //选填，激励视频奖励发放条件，单位为秒。用于约定video_reward上报的时机。不回传默认为20秒：当视频长度大于20秒时，播放超过20秒后上报，具体时长可通过__DURATION__宏来确定，不足20秒的视频播放完毕后上报。

      message AppInfo {
        string app_name = 1;      //必填，应用名称
        string app_permission = 2;//必填，应用权限信息：支持url或者文本 (文本返回需要||分隔)
        string app_privacy = 3;   //必填，应用隐私政策url
        uint64 app_size = 4;      //必填，应用包的大小，单位byte
        string app_version = 5;   //必填，应用版本
        string developer = 6;     //必填，app开发者
        string download_url = 7;  //必填，应用下载url
        string package_name = 8;  //必填，Android 应用为包名，例："com.test.com";ios应用为 iTunes Id 例："19334722"
        string app_description = 9; //必填，app功能介绍
        string app_md5 = 10; //必填，app功能介绍
      }

      message Applet {
        string original_id = 1; //必填，小程序原始id
        string path = 2; //必填，小程序调起路径
      }

      message Image {
        string url = 1;//必填，图片地址
        uint32 w = 2;  //必填，宽度，像素
        uint32 h = 3;  //必填，高度，像素
      }

      message Icon {
        string url = 1;//必填，图片地址
        uint32 w = 2;  //必填，宽度，像素
        uint32 h = 3;  //必填，高度，像素
      }

      message Video {
        string cover_url = 1;//必填，视频封面图
        uint32 duration = 2; //必填，视频时长（单位秒）
        uint32 h = 3;        //必填，视频高，像素
        uint32 w = 4;        //必填，视频宽，像素
        uint64 size = 5;     //必填，视频大小，单位size
        string url = 6;      //必填，视频地址
      }
    }
    message TrackUrls {
      repeated string expose_urls = 1;     //选填，曝光上报
      repeated string click_urls = 2;      //选填，点击上报
      repeated string download_start = 3;  //选填，开始下载上报
      repeated string download_finish = 4; //选填，完成下载上报
      repeated string install_start = 5;   //选填，开始安装上报
      repeated string install_finish = 6;  //选填，安装完成上报
      repeated string deeplink_success = 7;//选填，deeplink调起成功上报
      repeated string deeplink_fail = 8;   //选填，deeplink调起失败上报
      repeated string video_play0 = 9;     //选填，视频播放进度上报，播放开始
      repeated string video_play1 = 10;    //选填，视频播放进度上报，播放25%
      repeated string video_play2 = 11;    //选填，视频播放进度上报，播放50%
      repeated string video_play3 = 12;    //选填，视频播放进度上报，播放75%
      repeated string video_play4 = 13;    //选填，视频播放进度上报，播放完成
      repeated string video_reward = 14;   //选填，激励视频奖励发放上报
      repeated string video_skip = 15;     //选填，激励视频跳过上报
      repeated string appletcallup_succ = 16; //选填，小程序调起成功
      repeated string appletcallup_fail = 17; //选填，小程序调起失败
    }
  }
}

message Ext {
  string k = 1;//必填，扩展字段
  string v = 2;//必填，扩展字段值
}

enum AdType{
  AdTypeUnknown = 0;
  Stream = 1; // 信息流
  Banner = 2; // banner
  Splash = 3; // 开屏
  RewardVideo = 4; //激励视频
}

enum AdsAction {
  AdsActionUnknown = 0;
  Webview = 1; //webview
  Download = 2;//下载
  Deeplink = 3;//deeplink
  WechatApplet = 4; //微信小程序
}