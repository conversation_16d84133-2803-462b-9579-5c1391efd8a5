syntax="proto2";
// Copyright 2018 Bytedance Inc. All Rights Reserved.
option go_package = "mh_proxy/pb/csj_up";

package csj;

// Copyright 2018 Bytedance Inc. All Rights Reserved.
message BidRequest {
  required string request_id = 1; // 必填。自定义的请求id，长度为32位，保证其请求的唯一性。
  required string api_version = 2; // 必填。此API的版本。
  required string uid = 3; //必填，用户ID
  optional User user = 4; // 可选。用户信息，用于人群定向。
  required string source_type = 5; //流量类型：app或者wap
  optional Wap wap = 6; // 必填。当source_type为wap时，必须提供
  optional App app = 7; // 必填。移动app的信息。
  required Device device = 8; // 必填。移动设备的信息。
  required string ua = 9; // 必填。User-Agent
  required string ip = 10; // 必填。设备的ip，用于定位，地域定向
  repeated AdSlot adslots = 11; // 必填，至少一个。广告位的信息。
  optional string ad_sdk_version = 12; // 内部使用，其他勿填
  optional string abtest_param = 13; //内部使用，其他勿填
  optional string abtest_version = 14; //内部使用，其他勿填
  optional string group_id = 15; // 内部使用，文章id
  optional string utm_source = 16; // 内部使用utm_source
  optional string adx_name = 17; // Dsp必填。 Dsp使用判断哪家adx
  optional string adx_password = 18; // Dsp必填。 Dsp用来验证adx
  optional uint32 timeout = 19; // Dsp必填。 超时时间
  repeated string nid_list = 20; // IOS14以上必填。 开发者已配置的穿山甲的network id列表
  optional string query = 21; // 搜索请求的关键词
  optional string verify_key = 22;
  optional string timestamp = 23;

  message App {
    required string appid = 1; // 必填。app应用id，由头条分配。
    optional string name = 2; // 可选。app应用名。
    optional string package_name = 3; // 可选。 包名
    optional uint32 app_category = 4; // 可选。 app类型
    optional string version = 5; // 可选。 app应用的版本。
    optional Geo geo = 6; // 可选。设备的地理位置信息
    optional bool is_paid_app = 7; // 可选。表示此app是否为付费app。
    optional string apk_sign = 8; // 可选。 apk签名sha1值
    optional string itunes_id = 9; // IOS14以上必填。 媒体在appstore的appid
  }

  message Wap {
    required string site_id = 1; //站点id
    required string title = 2; //网站标题
    required uint32 site_category = 3; //站点类型
    optional string url = 4; //网页URL
    optional string referral = 5; //referURL
    optional string info = 6; //附加信息
    optional Geo geo = 7; // 可选。设备的地理位置信息
  }

  message Device {
    required string did = 1; // 必填。设备的唯一标识，安卓为IMEI, IOS为IDFA，TV为IDFA。
    required string imei = 2; //设备IMEI（明文）
    required DeviceType type = 3; // 必填。设备类型。
    required OsType os = 4; // 必填。操作系统类型。
    optional string os_version = 5; // 可选。操作系统版本。
    optional string vendor = 6; // 可选。设备厂商，如Apple, Samsung。
    optional string model = 7; // 可选。设备型号，如iPhone5s, Galaxy。
    optional string language = 8; // 可选。设备设置的语言。
    optional ConnectionType conn_type = 9; // 可选。设备的网络类型。
    optional string mac = 10; // 可选。设备的mac地址。
    required uint32 screen_width = 11; //设备屏宽
    required uint32 screen_height = 12; //设备屏高
    optional string android_id = 13; //选填。安卓ID
    optional string uuid = 14; //选填。安卓UUID
    optional string open_udid = 15; //选填。IOS 软件生成替代UDID的标识
    optional string ssid = 16; //选填。无线网SSID名称
    optional string wifi_mac = 17; //选填。WIFI路由器MAC地址
    optional string phone_name = 18; //选填。手机名称
    optional string dsid = 19; //选填。苹果账号（dsid）
    optional string power_on_time = 20; //选填。开机时间
    optional string imsi = 21; //选填。IMSI（SIM卡串号）
    optional string rom_version = 22; //选填。手机ROM的版本
    optional string sys_compiling_time = 23; //选填。系统编译时间
    optional Orientation orientation = 24; // 可选。设备屏幕方向
    optional string imei_md5 = 25; //设备IMEI_MD5
    optional string imei_sha256 = 26; //设备IMEI_SHA256
    optional string timezone = 27; //国际化，时区
    optional string gp_version = 28; //国际化，google_play_version
    optional string idfv = 29; //国际化，IDFV
    optional string gaid = 30; //国际化，google_advertising_id
    optional CarrierType carrier = 31; // 可选。运营商类型
    optional string oaid = 32; // 可选。匿名设备标识符
    optional string carrier_name = 33; // 运营商名称（设备上获取的原值）
    optional string local_tz_time = 34; // 设备的local时区
    optional string startup_time = 35; // 手机开机时间
    optional string mb_time = 36; // 系统版本更新时间
    optional int32 cpu_num = 37; // 设备cpu数量
    optional int64 disk_total = 38; // 磁盘总空间（字节）
    optional int64 mem_total = 39; // 磁盘总空间（字节）
    optional int32 auth_status = 40; // 广告标识授权情况
    optional string country_code = 41; // 国家代码
    optional string device_type = 42; // 硬件型号
    optional string CAID1 = 43; // 当前caid设备标志符号
    optional string CAID1_version = 44; // 当前caid版本号
    optional string CAID2 = 45; // 上一个caid设备标志符号
    optional string CAID2_version = 46; // 上一个caid设备标志版本号
    repeated string skan_versions = 47; // // IOS14以上必填。 设备支持的skan版本

    // 设备类型
    enum DeviceType {
      DEVICE_UNKNOWN = 0;
      PHONE = 1; // 手机。
      TABLET = 2; // 平板。
      TV = 3; // 智能电视。
    }
    // 操作系统类型
    enum OsType {
      OS_UNKNOWN = 0;
      ANDROID = 1;
      IOS = 2;
      WINDOWS = 3;
    }
    // 网络类型
    enum ConnectionType {
      CONN_UNKNOWN = 0;
      WIFI = 1;
      MOBILE_2G = 2;
      MOBILE_3G = 3;
      MOBILE_4G = 4;
      MOBILE_5G = 5;
    }
    //设备屏幕方向
    enum Orientation {
      UNKNOWN = 0;
      VERTICAL = 1; //垂直
      HORIZONTAL = 2; //水平
    }
    // 运营商类型
    enum CarrierType {
      CARRIER_UNKNOWN = 0;
      MOBILE = 1;
      UNICOM = 2;
      TELECOM = 3;
    }
  }

  message Geo {
    optional float latitude = 1; //纬度
    optional float longitude = 2; //经度
    optional string city = 3; //城市，中文即可(utf-8编码)
    optional string province = 4; //省份，中文即可(utf-8编码)
    optional string district = 5; //区县，中文即可(utf-8编码)
  }

  message User {
    optional Gender gender = 1; // 可选。用户的性别。
    optional uint32 age = 2; // 可选。用户的年龄。
    optional string phone_nub = 3; // 可选。手机号码
    optional string keywords = 4; // 可选。用户画像的关键词列表，以逗号分隔。
    repeated string app_list = 5; // 可选。已经安装的应用列表。
    repeated Data data = 6; // 提供额外的用户信息

    enum Gender {
      UNKNOWN = 0;
      MALE = 1;
      FEMALE = 2;
    }
    message Data {
      required string name = 1;
      optional string value = 2;
    }
  }

  message AdSlot {
    required string id = 1; // 必填。广告位id。
    required AdType adtype = 2; // 必填。广告类型。
    required Position pos = 3; // 必填。广告展现位置。
    repeated Size accepted_size = 4; // 必填。可选素材尺寸。
    repeated CreativeType accepted_creative_types = 5; // 可选。可接受的创意类型。
    repeated InteractionType accepted_interaction_type = 6; // 可选。app支持的创意交互类型。
    optional uint64 minimum_cpm = 7; // 可选。最低的cpm出价, 单位为分/cpm。
    optional uint32 ad_count = 8; // 可选，需要的广告数量
    optional bool is_origin_ad = 9; // 可选，是否是原生广告
    optional bool is_support_dpl = 10; //可选，V1.2b版本必传，是否支持deeplink
    repeated int64 accepted_image_modes = 11; // 可选。app支持的image_modes
    optional uint32 refresh_count = 12; //资讯合作流量，刷次
    optional RefreshType refresh_type = 13; //资讯合作流量，刷新类型
    optional uint32 video_min_duration = 14; // 期望视频的最小长度（含），0表示不限制
    optional uint32 video_max_duration = 15; // 期望视频的最大长度（含），0表示不限制
    // pmp交易相关参数，仅在PD/PDB方式下填写，为空或无效值时默认RTB，需要提前与商务同学确认竞价方式及价格。
    // 同一流量，最多支持n个PD订单，可传多个；仅支持1个PDB订单。
    repeated Pmp pmp = 16;
    repeated string support_pkg = 17; // 包名白名单，只出在名单内的包名广告
    optional bool is_support_ulink = 18; // 可选，是否支持ulink调起
    optional bool is_filter_unlisted_package = 19; // 只有荣耀能使用，是否过滤未上架应用
    optional string parent_pkg = 20; // 母应用
    optional int32 easy_play_type = 21; // 轻互动广告位类型 0为不支持 1竖版激励 2横版激励 3沉浸视频流
    optional int32 wx_applet_type = 22; // dsp媒体支持的微信小程序调起方式 1为支持sdk调起 0为不支持
    // pmp交易相关参数
    message Pmp {
      // 订单id，仅在PD/PDB方式下填写，为空或无效值时默认RTB，需要提前与商务同学确认竞价方式及价格，提供订单id由运营同学线下配置。
      required string dealid = 1;
      // 竞价方式。0代表PDB，1代表PD。实际投放以运营同学线下配置为准。
      required uint32 at = 2;
      // 订单id对应的结算价格，单位：cpm 分
      // 仅做验证使用，实际投放以运营同学线下配置为准
      optional uint64 price = 3;
    }

    // 广告的类型。
    enum AdType {
      BANNER = 1; // 横幅广告。
      INTERSTITIAL = 2; // 插屏广告。
      SPLASH = 3; // 开屏广告(请求后立即展现)。
      CACHED_SPLASH = 4; // 缓存开屏广告(预加载)。
      STREAM = 5; // 信息流广告。
      PATCH = 6; // 贴片广告。
      REWARD_VIDEO = 7; // 激励视频。
      FULLSCREEN_VIDEO = 8; // 全屏视频。
      DRAW_VIDEO = 9; // 沉浸式视频信息流
    }
    // 广告出现的位置。
    enum Position {
      TOP = 1; // 顶部。
      BOTTOM = 2; // 底部。
      FLOW = 3; // 信息流内。
      MIDDLE = 4; // 中部(插屏广告专用)。
      FULLSCREEN = 5; // 全屏。
    }
    // 可选的创意类型。
    enum CreativeType {
      TEXT = 1; // 文字。
      IMAGE = 2; // 图片。
      GIF = 3; // 动图。
      HTML = 4; // HTML.
      VIDEO = 5; // 视频。
      TEXT_ICON = 6; // 图文。
    }
    // 创意的交互类型
    enum InteractionType {
      NO_INTERACTION = 1; // 无动作，针对有些开屏不支持点击。
      SURFING = 2; // 使用浏览器打开网页。
      IN_APP = 3; // 在app中打开。
      DOWLOAD = 4; // 下载应用。
      DIALING = 5; // 拨打电话。
      MESSAGE = 6; // 发送短信。
      EMAIL = 7; // 发送邮件。
    }
    // 创意的交互类型
    enum RefreshType {
      Open = 0; // 无动作，针对有些开屏不支持点击。
      Refresh = 1; // 使用浏览器打开网页。
      Load_More = 2; // 在app中打开。
    }
    message Size {
      required uint32 width = 1; // 宽度。
      required uint32 height = 2; // 高度。
      optional uint32 creativeType = 3; // 素材类型 1单图 2组图 4视频
    }
  }
}

message BidResponse {
  required string request_id = 1; // 必填。BidRequest中所带的request_id。
  repeated Ad ads = 2; // 可选。当竞价时必填，竞价广告列表，与adslots对应。
  optional uint32 processing_time_ms = 3; // 可选。从收到请求到返回响应所用的时间。
  optional int64 status_code = 4; // 可选。请求时的状态码。
  optional uint32 expiration_time = 5; // 可选。广告过期时间戳，单位为秒，针对预加载广告。
  optional int32 reason = 6; //可选。测试用的状态码
  optional int64 did = 7; //可选。联盟设备标识
  optional string message = 8; //可选。未返回广告的原因

  message Ad {
    required string ad_id = 1; // 必填。创意id。
    required MaterialMeta creative = 2; // 必填。素材。
    optional uint64 price = 3; // 出价
    repeated FilterWords filter_words = 4; //不喜欢反馈项
    optional string dealid = 5; // 订单id，仅在PD/PDB方式下返回

    message FilterWords {
      required string id = 1; //反馈项编号
      optional bool is_selected = 2; //是否默认勾选
      required string name = 3; //反馈项描述
    }

    message MaterialMeta {
      required CreativeType creative_type = 1; // 必填。该广告的创意类型。
      optional InteractionType interaction_type = 2; // 可选。广告支持的交互类型。
      optional Image image = 3; // 可选。素材图片。
      optional string target_url = 4; // 可选。创意的落地页url。
      optional string download_url = 5; // 可选,应用下载必填。应用下载url。
      optional string title = 6; // 可选。广告标题。
      optional string description = 7; // 可选。广告描述。
      optional string app_name = 8; // 可选。针对应用下载类广告。
      optional string package_name = 9; // 可选。安卓应用下载包名。
      repeated string win_notice_url = 10; // 可选。获胜通知的url列表。
      repeated string show_url = 11; // 可选。展现监测url列表。
      repeated string click_url = 12; // 可选。点击监测url列表。
      optional string ext = 13; // 可选。扩展字段，DSP方希望通过监测url回传的数据。
      optional ImageMode image_mode = 14; // 素材模式,
      repeated Image image_list = 15; // 多图
      optional string phone_num = 16; //电话拨打广告，号码
      optional string button_text = 17; //电话拨打广告，按钮名称
      optional string source = 18; // 落地页的来源
      optional string icon = 19; // icon
      optional string deeplink_url = 20; // 可选。应用吊起链接。
      optional Video video = 21; // 视频
      optional string subtitle = 22; //副标题
      optional int64 comment_num = 23; //可选。应用下载可用，应用评论人数
      optional int32 score = 24; //可选。应用下载可用，应用评分星级
      repeated Tracking tracking_event = 25; //可选。其他打点上报url
      optional int32 product_size = 26; // 可选。安装包大小
      optional string product_md5 = 27; // 可选。安装包md5
      optional int64 creative_id = 28; // 可选。广告物料的唯一标识
      optional string channel = 29; // 可选。分包送审的渠道标识
      optional bool is_support_market_url = 30; // 可选。是否支持商店直投url
      optional string market_url = 31; // 可选。商店直投url
      optional string creative_audit_id = 32; // 可选。物料送审唯一标识
      optional SkanParameters skan = 33; // IOS14以上必填。广告的skan参数
      optional bool is_support_app_info = 34; // 可选。媒体是否需要拼接下载合规
      optional AppInfo app_info = 35; // 可选。下载合规信息
      optional string ulink_url = 36; // ulink调起链接
      optional string ad_extra_content = 37; // 一些ad额外信息，json结构
      optional int64 first_industry_id = 38; // 广告一级行业ID
      optional int64 second_industry_id = 39; // 广告二级行业ID
      optional string easy_playable = 40; // dsp轻互动组件信息
      repeated string play_trackers = 41; // 轻互动监测地址
      optional string channel_id = 42; // 渠道包id
      optional string quick_app_url = 43; // 快应用调起链接

      // 可选的创意类型。
      enum CreativeType {
        TEXT = 1; // 文字。
        IMAGE = 2; // 图片。
        GIF = 3; // 动图。
        HTML = 4; // HTML.
        VIDEO = 5; // 视频。
        TEXT_ICON = 6; // 图文。
        NO_MATERIAL = 7; // 不需要广告物料。
      }
      // 创意的交互类型
      enum InteractionType {
        NO_INTERACTION = 1; // 无动作，针对有些开屏不支持点击。
        SURFING = 2; // 使用浏览器打开网页。
        IN_APP = 3; // 在app中打开。
        DOWLOAD = 4; // 下载应用。
        DIALING = 5; // 拨打电话。
        MESSAGE = 6; // 发送短信。
        EMAIL = 7; // 发送邮件。
      }
      // 图片素材信息。
      message Image {
        optional string url = 1;
        optional uint32 width = 2;
        optional uint32 height = 3;
      }

      message Tracking {
        optional string event = 1;
        optional string url = 2;
      }

      enum ImageMode {
        SMALL = 2; //小图
        LARGE = 3; //大图
        GROUP = 4; //组图
        VIDEO_LARGE = 5; //横版视频
        VERTICAL_VIDEO = 15; //竖版视频
        LARGE_VERTICAL = 16; //竖版大图
        SPLASH = 131; //开屏大图
      }
      // 视频
      message Video {
        optional uint32 cover_height = 1;
        optional uint32 cover_width = 2;
        optional string cover_url = 3;
        optional string resolution = 4;
        optional uint64 size = 5;
        optional float video_duration = 6;
        optional string video_url = 7;
      }
    }

    message SkanParameters {
      optional SkanStoreProductParams store_product_parameters = 1;
      optional SkanAdImpression ad_impression = 2;
      message SkanStoreProductParams{
        required string version = 1;
        required string ad_network_id = 2;
        required int64 campaign_id = 3;
        required string nonce = 4;
        required int64 itunes_item_id = 5;
        required int64 source_app_id = 6;
        required int64 timestamp = 7;
        required string signature = 8;
      }
      message SkanAdImpression {
        required string version = 1;
        required string ad_network_id = 2;
        required int64 ad_campaign_id = 3;
        required string ad_impression_id = 4;
        required int64 advertised_app_id = 5;
        required int64 source_app_id = 6;
        required int64 timestamp = 7;
        required string signature = 8;
      }
    }

    message AppInfo {
      optional string app_version = 1;
      optional string developer_name = 2;
      repeated PermissionItem permissions = 3;
      optional string privacy_policy_url = 4;
      optional string package_name = 5;
      optional string app_name = 6;
      optional string permissions_url = 7;
      optional string desc_url = 8;

      message PermissionItem {
        optional string permission_name = 1;
        optional string permission_desc = 2;
      }
    }
  }
}
