// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: csj_1.5.5.proto

package csj_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 设备类型
type BidRequest_Device_DeviceType int32

const (
	BidRequest_Device_DEVICE_UNKNOWN BidRequest_Device_DeviceType = 0
	BidRequest_Device_PHONE          BidRequest_Device_DeviceType = 1 // 手机。
	BidRequest_Device_TABLET         BidRequest_Device_DeviceType = 2 // 平板。
	BidRequest_Device_TV             BidRequest_Device_DeviceType = 3 // 智能电视。
)

// Enum value maps for BidRequest_Device_DeviceType.
var (
	BidRequest_Device_DeviceType_name = map[int32]string{
		0: "DEVICE_UNKNOWN",
		1: "PHONE",
		2: "TABLET",
		3: "TV",
	}
	BidRequest_Device_DeviceType_value = map[string]int32{
		"DEVICE_UNKNOWN": 0,
		"PHONE":          1,
		"TABLET":         2,
		"TV":             3,
	}
)

func (x BidRequest_Device_DeviceType) Enum() *BidRequest_Device_DeviceType {
	p := new(BidRequest_Device_DeviceType)
	*p = x
	return p
}

func (x BidRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[0].Descriptor()
}

func (BidRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[0]
}

func (x BidRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_DeviceType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_DeviceType.Descriptor instead.
func (BidRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2, 0}
}

// 操作系统类型
type BidRequest_Device_OsType int32

const (
	BidRequest_Device_OS_UNKNOWN BidRequest_Device_OsType = 0
	BidRequest_Device_ANDROID    BidRequest_Device_OsType = 1
	BidRequest_Device_IOS        BidRequest_Device_OsType = 2
	BidRequest_Device_WINDOWS    BidRequest_Device_OsType = 3
)

// Enum value maps for BidRequest_Device_OsType.
var (
	BidRequest_Device_OsType_name = map[int32]string{
		0: "OS_UNKNOWN",
		1: "ANDROID",
		2: "IOS",
		3: "WINDOWS",
	}
	BidRequest_Device_OsType_value = map[string]int32{
		"OS_UNKNOWN": 0,
		"ANDROID":    1,
		"IOS":        2,
		"WINDOWS":    3,
	}
)

func (x BidRequest_Device_OsType) Enum() *BidRequest_Device_OsType {
	p := new(BidRequest_Device_OsType)
	*p = x
	return p
}

func (x BidRequest_Device_OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[1].Descriptor()
}

func (BidRequest_Device_OsType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[1]
}

func (x BidRequest_Device_OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_OsType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_OsType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_OsType.Descriptor instead.
func (BidRequest_Device_OsType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2, 1}
}

// 网络类型
type BidRequest_Device_ConnectionType int32

const (
	BidRequest_Device_CONN_UNKNOWN BidRequest_Device_ConnectionType = 0
	BidRequest_Device_WIFI         BidRequest_Device_ConnectionType = 1
	BidRequest_Device_MOBILE_2G    BidRequest_Device_ConnectionType = 2
	BidRequest_Device_MOBILE_3G    BidRequest_Device_ConnectionType = 3
	BidRequest_Device_MOBILE_4G    BidRequest_Device_ConnectionType = 4
	BidRequest_Device_MOBILE_5G    BidRequest_Device_ConnectionType = 5
)

// Enum value maps for BidRequest_Device_ConnectionType.
var (
	BidRequest_Device_ConnectionType_name = map[int32]string{
		0: "CONN_UNKNOWN",
		1: "WIFI",
		2: "MOBILE_2G",
		3: "MOBILE_3G",
		4: "MOBILE_4G",
		5: "MOBILE_5G",
	}
	BidRequest_Device_ConnectionType_value = map[string]int32{
		"CONN_UNKNOWN": 0,
		"WIFI":         1,
		"MOBILE_2G":    2,
		"MOBILE_3G":    3,
		"MOBILE_4G":    4,
		"MOBILE_5G":    5,
	}
)

func (x BidRequest_Device_ConnectionType) Enum() *BidRequest_Device_ConnectionType {
	p := new(BidRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x BidRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[2].Descriptor()
}

func (BidRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[2]
}

func (x BidRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_ConnectionType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_ConnectionType.Descriptor instead.
func (BidRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2, 2}
}

// 设备屏幕方向
type BidRequest_Device_Orientation int32

const (
	BidRequest_Device_UNKNOWN    BidRequest_Device_Orientation = 0
	BidRequest_Device_VERTICAL   BidRequest_Device_Orientation = 1 //垂直
	BidRequest_Device_HORIZONTAL BidRequest_Device_Orientation = 2 //水平
)

// Enum value maps for BidRequest_Device_Orientation.
var (
	BidRequest_Device_Orientation_name = map[int32]string{
		0: "UNKNOWN",
		1: "VERTICAL",
		2: "HORIZONTAL",
	}
	BidRequest_Device_Orientation_value = map[string]int32{
		"UNKNOWN":    0,
		"VERTICAL":   1,
		"HORIZONTAL": 2,
	}
)

func (x BidRequest_Device_Orientation) Enum() *BidRequest_Device_Orientation {
	p := new(BidRequest_Device_Orientation)
	*p = x
	return p
}

func (x BidRequest_Device_Orientation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_Orientation) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Device_Orientation) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[3]
}

func (x BidRequest_Device_Orientation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_Orientation) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_Orientation(num)
	return nil
}

// Deprecated: Use BidRequest_Device_Orientation.Descriptor instead.
func (BidRequest_Device_Orientation) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2, 3}
}

// 运营商类型
type BidRequest_Device_CarrierType int32

const (
	BidRequest_Device_CARRIER_UNKNOWN BidRequest_Device_CarrierType = 0
	BidRequest_Device_MOBILE          BidRequest_Device_CarrierType = 1
	BidRequest_Device_UNICOM          BidRequest_Device_CarrierType = 2
	BidRequest_Device_TELECOM         BidRequest_Device_CarrierType = 3
)

// Enum value maps for BidRequest_Device_CarrierType.
var (
	BidRequest_Device_CarrierType_name = map[int32]string{
		0: "CARRIER_UNKNOWN",
		1: "MOBILE",
		2: "UNICOM",
		3: "TELECOM",
	}
	BidRequest_Device_CarrierType_value = map[string]int32{
		"CARRIER_UNKNOWN": 0,
		"MOBILE":          1,
		"UNICOM":          2,
		"TELECOM":         3,
	}
)

func (x BidRequest_Device_CarrierType) Enum() *BidRequest_Device_CarrierType {
	p := new(BidRequest_Device_CarrierType)
	*p = x
	return p
}

func (x BidRequest_Device_CarrierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_CarrierType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[4].Descriptor()
}

func (BidRequest_Device_CarrierType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[4]
}

func (x BidRequest_Device_CarrierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_CarrierType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_CarrierType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_CarrierType.Descriptor instead.
func (BidRequest_Device_CarrierType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2, 4}
}

type BidRequest_User_Gender int32

const (
	BidRequest_User_UNKNOWN BidRequest_User_Gender = 0
	BidRequest_User_MALE    BidRequest_User_Gender = 1
	BidRequest_User_FEMALE  BidRequest_User_Gender = 2
)

// Enum value maps for BidRequest_User_Gender.
var (
	BidRequest_User_Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	BidRequest_User_Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x BidRequest_User_Gender) Enum() *BidRequest_User_Gender {
	p := new(BidRequest_User_Gender)
	*p = x
	return p
}

func (x BidRequest_User_Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_User_Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[5].Descriptor()
}

func (BidRequest_User_Gender) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[5]
}

func (x BidRequest_User_Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_User_Gender) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_User_Gender(num)
	return nil
}

// Deprecated: Use BidRequest_User_Gender.Descriptor instead.
func (BidRequest_User_Gender) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 4, 0}
}

// 广告的类型。
type BidRequest_AdSlot_AdType int32

const (
	BidRequest_AdSlot_BANNER           BidRequest_AdSlot_AdType = 1 // 横幅广告。
	BidRequest_AdSlot_INTERSTITIAL     BidRequest_AdSlot_AdType = 2 // 插屏广告。
	BidRequest_AdSlot_SPLASH           BidRequest_AdSlot_AdType = 3 // 开屏广告(请求后立即展现)。
	BidRequest_AdSlot_CACHED_SPLASH    BidRequest_AdSlot_AdType = 4 // 缓存开屏广告(预加载)。
	BidRequest_AdSlot_STREAM           BidRequest_AdSlot_AdType = 5 // 信息流广告。
	BidRequest_AdSlot_PATCH            BidRequest_AdSlot_AdType = 6 // 贴片广告。
	BidRequest_AdSlot_REWARD_VIDEO     BidRequest_AdSlot_AdType = 7 // 激励视频。
	BidRequest_AdSlot_FULLSCREEN_VIDEO BidRequest_AdSlot_AdType = 8 // 全屏视频。
	BidRequest_AdSlot_DRAW_VIDEO       BidRequest_AdSlot_AdType = 9 // 沉浸式视频信息流
)

// Enum value maps for BidRequest_AdSlot_AdType.
var (
	BidRequest_AdSlot_AdType_name = map[int32]string{
		1: "BANNER",
		2: "INTERSTITIAL",
		3: "SPLASH",
		4: "CACHED_SPLASH",
		5: "STREAM",
		6: "PATCH",
		7: "REWARD_VIDEO",
		8: "FULLSCREEN_VIDEO",
		9: "DRAW_VIDEO",
	}
	BidRequest_AdSlot_AdType_value = map[string]int32{
		"BANNER":           1,
		"INTERSTITIAL":     2,
		"SPLASH":           3,
		"CACHED_SPLASH":    4,
		"STREAM":           5,
		"PATCH":            6,
		"REWARD_VIDEO":     7,
		"FULLSCREEN_VIDEO": 8,
		"DRAW_VIDEO":       9,
	}
)

func (x BidRequest_AdSlot_AdType) Enum() *BidRequest_AdSlot_AdType {
	p := new(BidRequest_AdSlot_AdType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[6].Descriptor()
}

func (BidRequest_AdSlot_AdType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[6]
}

func (x BidRequest_AdSlot_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_AdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_AdType(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_AdType.Descriptor instead.
func (BidRequest_AdSlot_AdType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 0}
}

// 广告出现的位置。
type BidRequest_AdSlot_Position int32

const (
	BidRequest_AdSlot_TOP        BidRequest_AdSlot_Position = 1 // 顶部。
	BidRequest_AdSlot_BOTTOM     BidRequest_AdSlot_Position = 2 // 底部。
	BidRequest_AdSlot_FLOW       BidRequest_AdSlot_Position = 3 // 信息流内。
	BidRequest_AdSlot_MIDDLE     BidRequest_AdSlot_Position = 4 // 中部(插屏广告专用)。
	BidRequest_AdSlot_FULLSCREEN BidRequest_AdSlot_Position = 5 // 全屏。
)

// Enum value maps for BidRequest_AdSlot_Position.
var (
	BidRequest_AdSlot_Position_name = map[int32]string{
		1: "TOP",
		2: "BOTTOM",
		3: "FLOW",
		4: "MIDDLE",
		5: "FULLSCREEN",
	}
	BidRequest_AdSlot_Position_value = map[string]int32{
		"TOP":        1,
		"BOTTOM":     2,
		"FLOW":       3,
		"MIDDLE":     4,
		"FULLSCREEN": 5,
	}
)

func (x BidRequest_AdSlot_Position) Enum() *BidRequest_AdSlot_Position {
	p := new(BidRequest_AdSlot_Position)
	*p = x
	return p
}

func (x BidRequest_AdSlot_Position) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_Position) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[7].Descriptor()
}

func (BidRequest_AdSlot_Position) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[7]
}

func (x BidRequest_AdSlot_Position) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_Position) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_Position(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_Position.Descriptor instead.
func (BidRequest_AdSlot_Position) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 1}
}

// 可选的创意类型。
type BidRequest_AdSlot_CreativeType int32

const (
	BidRequest_AdSlot_TEXT      BidRequest_AdSlot_CreativeType = 1 // 文字。
	BidRequest_AdSlot_IMAGE     BidRequest_AdSlot_CreativeType = 2 // 图片。
	BidRequest_AdSlot_GIF       BidRequest_AdSlot_CreativeType = 3 // 动图。
	BidRequest_AdSlot_HTML      BidRequest_AdSlot_CreativeType = 4 // HTML.
	BidRequest_AdSlot_VIDEO     BidRequest_AdSlot_CreativeType = 5 // 视频。
	BidRequest_AdSlot_TEXT_ICON BidRequest_AdSlot_CreativeType = 6 // 图文。
)

// Enum value maps for BidRequest_AdSlot_CreativeType.
var (
	BidRequest_AdSlot_CreativeType_name = map[int32]string{
		1: "TEXT",
		2: "IMAGE",
		3: "GIF",
		4: "HTML",
		5: "VIDEO",
		6: "TEXT_ICON",
	}
	BidRequest_AdSlot_CreativeType_value = map[string]int32{
		"TEXT":      1,
		"IMAGE":     2,
		"GIF":       3,
		"HTML":      4,
		"VIDEO":     5,
		"TEXT_ICON": 6,
	}
)

func (x BidRequest_AdSlot_CreativeType) Enum() *BidRequest_AdSlot_CreativeType {
	p := new(BidRequest_AdSlot_CreativeType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[8].Descriptor()
}

func (BidRequest_AdSlot_CreativeType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[8]
}

func (x BidRequest_AdSlot_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_CreativeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_CreativeType(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_CreativeType.Descriptor instead.
func (BidRequest_AdSlot_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 2}
}

// 创意的交互类型
type BidRequest_AdSlot_InteractionType int32

const (
	BidRequest_AdSlot_NO_INTERACTION BidRequest_AdSlot_InteractionType = 1 // 无动作，针对有些开屏不支持点击。
	BidRequest_AdSlot_SURFING        BidRequest_AdSlot_InteractionType = 2 // 使用浏览器打开网页。
	BidRequest_AdSlot_IN_APP         BidRequest_AdSlot_InteractionType = 3 // 在app中打开。
	BidRequest_AdSlot_DOWLOAD        BidRequest_AdSlot_InteractionType = 4 // 下载应用。
	BidRequest_AdSlot_DIALING        BidRequest_AdSlot_InteractionType = 5 // 拨打电话。
	BidRequest_AdSlot_MESSAGE        BidRequest_AdSlot_InteractionType = 6 // 发送短信。
	BidRequest_AdSlot_EMAIL          BidRequest_AdSlot_InteractionType = 7 // 发送邮件。
)

// Enum value maps for BidRequest_AdSlot_InteractionType.
var (
	BidRequest_AdSlot_InteractionType_name = map[int32]string{
		1: "NO_INTERACTION",
		2: "SURFING",
		3: "IN_APP",
		4: "DOWLOAD",
		5: "DIALING",
		6: "MESSAGE",
		7: "EMAIL",
	}
	BidRequest_AdSlot_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 1,
		"SURFING":        2,
		"IN_APP":         3,
		"DOWLOAD":        4,
		"DIALING":        5,
		"MESSAGE":        6,
		"EMAIL":          7,
	}
)

func (x BidRequest_AdSlot_InteractionType) Enum() *BidRequest_AdSlot_InteractionType {
	p := new(BidRequest_AdSlot_InteractionType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[9].Descriptor()
}

func (BidRequest_AdSlot_InteractionType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[9]
}

func (x BidRequest_AdSlot_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_InteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_InteractionType(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_InteractionType.Descriptor instead.
func (BidRequest_AdSlot_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 3}
}

// 创意的交互类型
type BidRequest_AdSlot_RefreshType int32

const (
	BidRequest_AdSlot_Open      BidRequest_AdSlot_RefreshType = 0 // 无动作，针对有些开屏不支持点击。
	BidRequest_AdSlot_Refresh   BidRequest_AdSlot_RefreshType = 1 // 使用浏览器打开网页。
	BidRequest_AdSlot_Load_More BidRequest_AdSlot_RefreshType = 2 // 在app中打开。
)

// Enum value maps for BidRequest_AdSlot_RefreshType.
var (
	BidRequest_AdSlot_RefreshType_name = map[int32]string{
		0: "Open",
		1: "Refresh",
		2: "Load_More",
	}
	BidRequest_AdSlot_RefreshType_value = map[string]int32{
		"Open":      0,
		"Refresh":   1,
		"Load_More": 2,
	}
)

func (x BidRequest_AdSlot_RefreshType) Enum() *BidRequest_AdSlot_RefreshType {
	p := new(BidRequest_AdSlot_RefreshType)
	*p = x
	return p
}

func (x BidRequest_AdSlot_RefreshType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_RefreshType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[10].Descriptor()
}

func (BidRequest_AdSlot_RefreshType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[10]
}

func (x BidRequest_AdSlot_RefreshType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_RefreshType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_RefreshType(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_RefreshType.Descriptor instead.
func (BidRequest_AdSlot_RefreshType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 4}
}

// 可选的创意类型。
type BidResponse_Ad_MaterialMeta_CreativeType int32

const (
	BidResponse_Ad_MaterialMeta_TEXT        BidResponse_Ad_MaterialMeta_CreativeType = 1 // 文字。
	BidResponse_Ad_MaterialMeta_IMAGE       BidResponse_Ad_MaterialMeta_CreativeType = 2 // 图片。
	BidResponse_Ad_MaterialMeta_GIF         BidResponse_Ad_MaterialMeta_CreativeType = 3 // 动图。
	BidResponse_Ad_MaterialMeta_HTML        BidResponse_Ad_MaterialMeta_CreativeType = 4 // HTML.
	BidResponse_Ad_MaterialMeta_VIDEO       BidResponse_Ad_MaterialMeta_CreativeType = 5 // 视频。
	BidResponse_Ad_MaterialMeta_TEXT_ICON   BidResponse_Ad_MaterialMeta_CreativeType = 6 // 图文。
	BidResponse_Ad_MaterialMeta_NO_MATERIAL BidResponse_Ad_MaterialMeta_CreativeType = 7 // 不需要广告物料。
)

// Enum value maps for BidResponse_Ad_MaterialMeta_CreativeType.
var (
	BidResponse_Ad_MaterialMeta_CreativeType_name = map[int32]string{
		1: "TEXT",
		2: "IMAGE",
		3: "GIF",
		4: "HTML",
		5: "VIDEO",
		6: "TEXT_ICON",
		7: "NO_MATERIAL",
	}
	BidResponse_Ad_MaterialMeta_CreativeType_value = map[string]int32{
		"TEXT":        1,
		"IMAGE":       2,
		"GIF":         3,
		"HTML":        4,
		"VIDEO":       5,
		"TEXT_ICON":   6,
		"NO_MATERIAL": 7,
	}
)

func (x BidResponse_Ad_MaterialMeta_CreativeType) Enum() *BidResponse_Ad_MaterialMeta_CreativeType {
	p := new(BidResponse_Ad_MaterialMeta_CreativeType)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialMeta_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialMeta_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[11].Descriptor()
}

func (BidResponse_Ad_MaterialMeta_CreativeType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[11]
}

func (x BidResponse_Ad_MaterialMeta_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialMeta_CreativeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialMeta_CreativeType(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_CreativeType.Descriptor instead.
func (BidResponse_Ad_MaterialMeta_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 0}
}

// 创意的交互类型
type BidResponse_Ad_MaterialMeta_InteractionType int32

const (
	BidResponse_Ad_MaterialMeta_NO_INTERACTION BidResponse_Ad_MaterialMeta_InteractionType = 1 // 无动作，针对有些开屏不支持点击。
	BidResponse_Ad_MaterialMeta_SURFING        BidResponse_Ad_MaterialMeta_InteractionType = 2 // 使用浏览器打开网页。
	BidResponse_Ad_MaterialMeta_IN_APP         BidResponse_Ad_MaterialMeta_InteractionType = 3 // 在app中打开。
	BidResponse_Ad_MaterialMeta_DOWLOAD        BidResponse_Ad_MaterialMeta_InteractionType = 4 // 下载应用。
	BidResponse_Ad_MaterialMeta_DIALING        BidResponse_Ad_MaterialMeta_InteractionType = 5 // 拨打电话。
	BidResponse_Ad_MaterialMeta_MESSAGE        BidResponse_Ad_MaterialMeta_InteractionType = 6 // 发送短信。
	BidResponse_Ad_MaterialMeta_EMAIL          BidResponse_Ad_MaterialMeta_InteractionType = 7 // 发送邮件。
)

// Enum value maps for BidResponse_Ad_MaterialMeta_InteractionType.
var (
	BidResponse_Ad_MaterialMeta_InteractionType_name = map[int32]string{
		1: "NO_INTERACTION",
		2: "SURFING",
		3: "IN_APP",
		4: "DOWLOAD",
		5: "DIALING",
		6: "MESSAGE",
		7: "EMAIL",
	}
	BidResponse_Ad_MaterialMeta_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 1,
		"SURFING":        2,
		"IN_APP":         3,
		"DOWLOAD":        4,
		"DIALING":        5,
		"MESSAGE":        6,
		"EMAIL":          7,
	}
)

func (x BidResponse_Ad_MaterialMeta_InteractionType) Enum() *BidResponse_Ad_MaterialMeta_InteractionType {
	p := new(BidResponse_Ad_MaterialMeta_InteractionType)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialMeta_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialMeta_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[12].Descriptor()
}

func (BidResponse_Ad_MaterialMeta_InteractionType) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[12]
}

func (x BidResponse_Ad_MaterialMeta_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialMeta_InteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialMeta_InteractionType(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_InteractionType.Descriptor instead.
func (BidResponse_Ad_MaterialMeta_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 1}
}

type BidResponse_Ad_MaterialMeta_ImageMode int32

const (
	BidResponse_Ad_MaterialMeta_SMALL          BidResponse_Ad_MaterialMeta_ImageMode = 2   //小图
	BidResponse_Ad_MaterialMeta_LARGE          BidResponse_Ad_MaterialMeta_ImageMode = 3   //大图
	BidResponse_Ad_MaterialMeta_GROUP          BidResponse_Ad_MaterialMeta_ImageMode = 4   //组图
	BidResponse_Ad_MaterialMeta_VIDEO_LARGE    BidResponse_Ad_MaterialMeta_ImageMode = 5   //横版视频
	BidResponse_Ad_MaterialMeta_VERTICAL_VIDEO BidResponse_Ad_MaterialMeta_ImageMode = 15  //竖版视频
	BidResponse_Ad_MaterialMeta_LARGE_VERTICAL BidResponse_Ad_MaterialMeta_ImageMode = 16  //竖版大图
	BidResponse_Ad_MaterialMeta_SPLASH         BidResponse_Ad_MaterialMeta_ImageMode = 131 //开屏大图
)

// Enum value maps for BidResponse_Ad_MaterialMeta_ImageMode.
var (
	BidResponse_Ad_MaterialMeta_ImageMode_name = map[int32]string{
		2:   "SMALL",
		3:   "LARGE",
		4:   "GROUP",
		5:   "VIDEO_LARGE",
		15:  "VERTICAL_VIDEO",
		16:  "LARGE_VERTICAL",
		131: "SPLASH",
	}
	BidResponse_Ad_MaterialMeta_ImageMode_value = map[string]int32{
		"SMALL":          2,
		"LARGE":          3,
		"GROUP":          4,
		"VIDEO_LARGE":    5,
		"VERTICAL_VIDEO": 15,
		"LARGE_VERTICAL": 16,
		"SPLASH":         131,
	}
)

func (x BidResponse_Ad_MaterialMeta_ImageMode) Enum() *BidResponse_Ad_MaterialMeta_ImageMode {
	p := new(BidResponse_Ad_MaterialMeta_ImageMode)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialMeta_ImageMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialMeta_ImageMode) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_1_5_5_proto_enumTypes[13].Descriptor()
}

func (BidResponse_Ad_MaterialMeta_ImageMode) Type() protoreflect.EnumType {
	return &file_csj_1_5_5_proto_enumTypes[13]
}

func (x BidResponse_Ad_MaterialMeta_ImageMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialMeta_ImageMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialMeta_ImageMode(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_ImageMode.Descriptor instead.
func (BidResponse_Ad_MaterialMeta_ImageMode) EnumDescriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 2}
}

// Copyright 2018 Bytedance Inc. All Rights Reserved.
type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId     *string              `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`              // 必填。自定义的请求id，长度为32位，保证其请求的唯一性。
	ApiVersion    *string              `protobuf:"bytes,2,req,name=api_version,json=apiVersion" json:"api_version,omitempty"`           // 必填。此API的版本。
	Uid           *string              `protobuf:"bytes,3,req,name=uid" json:"uid,omitempty"`                                           //必填，用户ID
	User          *BidRequest_User     `protobuf:"bytes,4,opt,name=user" json:"user,omitempty"`                                         // 可选。用户信息，用于人群定向。
	SourceType    *string              `protobuf:"bytes,5,req,name=source_type,json=sourceType" json:"source_type,omitempty"`           //流量类型：app或者wap
	Wap           *BidRequest_Wap      `protobuf:"bytes,6,opt,name=wap" json:"wap,omitempty"`                                           // 必填。当source_type为wap时，必须提供
	App           *BidRequest_App      `protobuf:"bytes,7,opt,name=app" json:"app,omitempty"`                                           // 必填。移动app的信息。
	Device        *BidRequest_Device   `protobuf:"bytes,8,req,name=device" json:"device,omitempty"`                                     // 必填。移动设备的信息。
	Ua            *string              `protobuf:"bytes,9,req,name=ua" json:"ua,omitempty"`                                             // 必填。User-Agent
	Ip            *string              `protobuf:"bytes,10,req,name=ip" json:"ip,omitempty"`                                            // 必填。设备的ip，用于定位，地域定向
	Adslots       []*BidRequest_AdSlot `protobuf:"bytes,11,rep,name=adslots" json:"adslots,omitempty"`                                  // 必填，至少一个。广告位的信息。
	AdSdkVersion  *string              `protobuf:"bytes,12,opt,name=ad_sdk_version,json=adSdkVersion" json:"ad_sdk_version,omitempty"`  // 内部使用，其他勿填
	AbtestParam   *string              `protobuf:"bytes,13,opt,name=abtest_param,json=abtestParam" json:"abtest_param,omitempty"`       //内部使用，其他勿填
	AbtestVersion *string              `protobuf:"bytes,14,opt,name=abtest_version,json=abtestVersion" json:"abtest_version,omitempty"` //内部使用，其他勿填
	GroupId       *string              `protobuf:"bytes,15,opt,name=group_id,json=groupId" json:"group_id,omitempty"`                   // 内部使用，文章id
	UtmSource     *string              `protobuf:"bytes,16,opt,name=utm_source,json=utmSource" json:"utm_source,omitempty"`             // 内部使用utm_source
	AdxName       *string              `protobuf:"bytes,17,opt,name=adx_name,json=adxName" json:"adx_name,omitempty"`                   // Dsp必填。 Dsp使用判断哪家adx
	AdxPassword   *string              `protobuf:"bytes,18,opt,name=adx_password,json=adxPassword" json:"adx_password,omitempty"`       // Dsp必填。 Dsp用来验证adx
	Timeout       *uint32              `protobuf:"varint,19,opt,name=timeout" json:"timeout,omitempty"`                                 // Dsp必填。 超时时间
	NidList       []string             `protobuf:"bytes,20,rep,name=nid_list,json=nidList" json:"nid_list,omitempty"`                   // IOS14以上必填。 开发者已配置的穿山甲的network id列表
	Query         *string              `protobuf:"bytes,21,opt,name=query" json:"query,omitempty"`                                      // 搜索请求的关键词
	VerifyKey     *string              `protobuf:"bytes,22,opt,name=verify_key,json=verifyKey" json:"verify_key,omitempty"`
	Timestamp     *string              `protobuf:"bytes,23,opt,name=timestamp" json:"timestamp,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetUid() string {
	if x != nil && x.Uid != nil {
		return *x.Uid
	}
	return ""
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *BidRequest) GetWap() *BidRequest_Wap {
	if x != nil {
		return x.Wap
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest) GetAdslots() []*BidRequest_AdSlot {
	if x != nil {
		return x.Adslots
	}
	return nil
}

func (x *BidRequest) GetAdSdkVersion() string {
	if x != nil && x.AdSdkVersion != nil {
		return *x.AdSdkVersion
	}
	return ""
}

func (x *BidRequest) GetAbtestParam() string {
	if x != nil && x.AbtestParam != nil {
		return *x.AbtestParam
	}
	return ""
}

func (x *BidRequest) GetAbtestVersion() string {
	if x != nil && x.AbtestVersion != nil {
		return *x.AbtestVersion
	}
	return ""
}

func (x *BidRequest) GetGroupId() string {
	if x != nil && x.GroupId != nil {
		return *x.GroupId
	}
	return ""
}

func (x *BidRequest) GetUtmSource() string {
	if x != nil && x.UtmSource != nil {
		return *x.UtmSource
	}
	return ""
}

func (x *BidRequest) GetAdxName() string {
	if x != nil && x.AdxName != nil {
		return *x.AdxName
	}
	return ""
}

func (x *BidRequest) GetAdxPassword() string {
	if x != nil && x.AdxPassword != nil {
		return *x.AdxPassword
	}
	return ""
}

func (x *BidRequest) GetTimeout() uint32 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

func (x *BidRequest) GetNidList() []string {
	if x != nil {
		return x.NidList
	}
	return nil
}

func (x *BidRequest) GetQuery() string {
	if x != nil && x.Query != nil {
		return *x.Query
	}
	return ""
}

func (x *BidRequest) GetVerifyKey() string {
	if x != nil && x.VerifyKey != nil {
		return *x.VerifyKey
	}
	return ""
}

func (x *BidRequest) GetTimestamp() string {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return ""
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId        *string           `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`                         // 必填。BidRequest中所带的request_id。
	Ads              []*BidResponse_Ad `protobuf:"bytes,2,rep,name=ads" json:"ads,omitempty"`                                                      // 可选。当竞价时必填，竞价广告列表，与adslots对应。
	ProcessingTimeMs *uint32           `protobuf:"varint,3,opt,name=processing_time_ms,json=processingTimeMs" json:"processing_time_ms,omitempty"` // 可选。从收到请求到返回响应所用的时间。
	StatusCode       *int64            `protobuf:"varint,4,opt,name=status_code,json=statusCode" json:"status_code,omitempty"`                     // 可选。请求时的状态码。
	ExpirationTime   *uint32           `protobuf:"varint,5,opt,name=expiration_time,json=expirationTime" json:"expiration_time,omitempty"`         // 可选。广告过期时间戳，单位为秒，针对预加载广告。
	Reason           *int32            `protobuf:"varint,6,opt,name=reason" json:"reason,omitempty"`                                               //可选。测试用的状态码
	Did              *int64            `protobuf:"varint,7,opt,name=did" json:"did,omitempty"`                                                     //可选。联盟设备标识
	Message          *string           `protobuf:"bytes,8,opt,name=message" json:"message,omitempty"`                                              //可选。未返回广告的原因
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidResponse) GetAds() []*BidResponse_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *BidResponse) GetProcessingTimeMs() uint32 {
	if x != nil && x.ProcessingTimeMs != nil {
		return *x.ProcessingTimeMs
	}
	return 0
}

func (x *BidResponse) GetStatusCode() int64 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *BidResponse) GetExpirationTime() uint32 {
	if x != nil && x.ExpirationTime != nil {
		return *x.ExpirationTime
	}
	return 0
}

func (x *BidResponse) GetReason() int32 {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return 0
}

func (x *BidResponse) GetDid() int64 {
	if x != nil && x.Did != nil {
		return *x.Did
	}
	return 0
}

func (x *BidResponse) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid       *string         `protobuf:"bytes,1,req,name=appid" json:"appid,omitempty"`                                 // 必填。app应用id，由头条分配。
	Name        *string         `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                                   // 可选。app应用名。
	PackageName *string         `protobuf:"bytes,3,opt,name=package_name,json=packageName" json:"package_name,omitempty"`  // 可选。 包名
	AppCategory *uint32         `protobuf:"varint,4,opt,name=app_category,json=appCategory" json:"app_category,omitempty"` // 可选。 app类型
	Version     *string         `protobuf:"bytes,5,opt,name=version" json:"version,omitempty"`                             // 可选。 app应用的版本。
	Geo         *BidRequest_Geo `protobuf:"bytes,6,opt,name=geo" json:"geo,omitempty"`                                     // 可选。设备的地理位置信息
	IsPaidApp   *bool           `protobuf:"varint,7,opt,name=is_paid_app,json=isPaidApp" json:"is_paid_app,omitempty"`     // 可选。表示此app是否为付费app。
	ApkSign     *string         `protobuf:"bytes,8,opt,name=apk_sign,json=apkSign" json:"apk_sign,omitempty"`              // 可选。 apk签名sha1值
	ItunesId    *string         `protobuf:"bytes,9,opt,name=itunes_id,json=itunesId" json:"itunes_id,omitempty"`           // IOS14以上必填。 媒体在appstore的appid
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_App) GetAppid() string {
	if x != nil && x.Appid != nil {
		return *x.Appid
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidRequest_App) GetAppCategory() uint32 {
	if x != nil && x.AppCategory != nil {
		return *x.AppCategory
	}
	return 0
}

func (x *BidRequest_App) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest_App) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_App) GetIsPaidApp() bool {
	if x != nil && x.IsPaidApp != nil {
		return *x.IsPaidApp
	}
	return false
}

func (x *BidRequest_App) GetApkSign() string {
	if x != nil && x.ApkSign != nil {
		return *x.ApkSign
	}
	return ""
}

func (x *BidRequest_App) GetItunesId() string {
	if x != nil && x.ItunesId != nil {
		return *x.ItunesId
	}
	return ""
}

type BidRequest_Wap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SiteId       *string         `protobuf:"bytes,1,req,name=site_id,json=siteId" json:"site_id,omitempty"`                    //站点id
	Title        *string         `protobuf:"bytes,2,req,name=title" json:"title,omitempty"`                                    //网站标题
	SiteCategory *uint32         `protobuf:"varint,3,req,name=site_category,json=siteCategory" json:"site_category,omitempty"` //站点类型
	Url          *string         `protobuf:"bytes,4,opt,name=url" json:"url,omitempty"`                                        //网页URL
	Referral     *string         `protobuf:"bytes,5,opt,name=referral" json:"referral,omitempty"`                              //referURL
	Info         *string         `protobuf:"bytes,6,opt,name=info" json:"info,omitempty"`                                      //附加信息
	Geo          *BidRequest_Geo `protobuf:"bytes,7,opt,name=geo" json:"geo,omitempty"`                                        // 可选。设备的地理位置信息
}

func (x *BidRequest_Wap) Reset() {
	*x = BidRequest_Wap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Wap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Wap) ProtoMessage() {}

func (x *BidRequest_Wap) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Wap.ProtoReflect.Descriptor instead.
func (*BidRequest_Wap) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Wap) GetSiteId() string {
	if x != nil && x.SiteId != nil {
		return *x.SiteId
	}
	return ""
}

func (x *BidRequest_Wap) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidRequest_Wap) GetSiteCategory() uint32 {
	if x != nil && x.SiteCategory != nil {
		return *x.SiteCategory
	}
	return 0
}

func (x *BidRequest_Wap) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidRequest_Wap) GetReferral() string {
	if x != nil && x.Referral != nil {
		return *x.Referral
	}
	return ""
}

func (x *BidRequest_Wap) GetInfo() string {
	if x != nil && x.Info != nil {
		return *x.Info
	}
	return ""
}

func (x *BidRequest_Wap) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Did              *string                           `protobuf:"bytes,1,req,name=did" json:"did,omitempty"`                                                                      // 必填。设备的唯一标识，安卓为IMEI, IOS为IDFA，TV为IDFA。
	Imei             *string                           `protobuf:"bytes,2,req,name=imei" json:"imei,omitempty"`                                                                    //设备IMEI（明文）
	Type             *BidRequest_Device_DeviceType     `protobuf:"varint,3,req,name=type,enum=csj.BidRequest_Device_DeviceType" json:"type,omitempty"`                             // 必填。设备类型。
	Os               *BidRequest_Device_OsType         `protobuf:"varint,4,req,name=os,enum=csj.BidRequest_Device_OsType" json:"os,omitempty"`                                     // 必填。操作系统类型。
	OsVersion        *string                           `protobuf:"bytes,5,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`                                         // 可选。操作系统版本。
	Vendor           *string                           `protobuf:"bytes,6,opt,name=vendor" json:"vendor,omitempty"`                                                                // 可选。设备厂商，如Apple, Samsung。
	Model            *string                           `protobuf:"bytes,7,opt,name=model" json:"model,omitempty"`                                                                  // 可选。设备型号，如iPhone5s, Galaxy。
	Language         *string                           `protobuf:"bytes,8,opt,name=language" json:"language,omitempty"`                                                            // 可选。设备设置的语言。
	ConnType         *BidRequest_Device_ConnectionType `protobuf:"varint,9,opt,name=conn_type,json=connType,enum=csj.BidRequest_Device_ConnectionType" json:"conn_type,omitempty"` // 可选。设备的网络类型。
	Mac              *string                           `protobuf:"bytes,10,opt,name=mac" json:"mac,omitempty"`                                                                     // 可选。设备的mac地址。
	ScreenWidth      *uint32                           `protobuf:"varint,11,req,name=screen_width,json=screenWidth" json:"screen_width,omitempty"`                                 //设备屏宽
	ScreenHeight     *uint32                           `protobuf:"varint,12,req,name=screen_height,json=screenHeight" json:"screen_height,omitempty"`                              //设备屏高
	AndroidId        *string                           `protobuf:"bytes,13,opt,name=android_id,json=androidId" json:"android_id,omitempty"`                                        //选填。安卓ID
	Uuid             *string                           `protobuf:"bytes,14,opt,name=uuid" json:"uuid,omitempty"`                                                                   //选填。安卓UUID
	OpenUdid         *string                           `protobuf:"bytes,15,opt,name=open_udid,json=openUdid" json:"open_udid,omitempty"`                                           //选填。IOS 软件生成替代UDID的标识
	Ssid             *string                           `protobuf:"bytes,16,opt,name=ssid" json:"ssid,omitempty"`                                                                   //选填。无线网SSID名称
	WifiMac          *string                           `protobuf:"bytes,17,opt,name=wifi_mac,json=wifiMac" json:"wifi_mac,omitempty"`                                              //选填。WIFI路由器MAC地址
	PhoneName        *string                           `protobuf:"bytes,18,opt,name=phone_name,json=phoneName" json:"phone_name,omitempty"`                                        //选填。手机名称
	Dsid             *string                           `protobuf:"bytes,19,opt,name=dsid" json:"dsid,omitempty"`                                                                   //选填。苹果账号（dsid）
	PowerOnTime      *string                           `protobuf:"bytes,20,opt,name=power_on_time,json=powerOnTime" json:"power_on_time,omitempty"`                                //选填。开机时间
	Imsi             *string                           `protobuf:"bytes,21,opt,name=imsi" json:"imsi,omitempty"`                                                                   //选填。IMSI（SIM卡串号）
	RomVersion       *string                           `protobuf:"bytes,22,opt,name=rom_version,json=romVersion" json:"rom_version,omitempty"`                                     //选填。手机ROM的版本
	SysCompilingTime *string                           `protobuf:"bytes,23,opt,name=sys_compiling_time,json=sysCompilingTime" json:"sys_compiling_time,omitempty"`                 //选填。系统编译时间
	Orientation      *BidRequest_Device_Orientation    `protobuf:"varint,24,opt,name=orientation,enum=csj.BidRequest_Device_Orientation" json:"orientation,omitempty"`             // 可选。设备屏幕方向
	ImeiMd5          *string                           `protobuf:"bytes,25,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`                                              //设备IMEI_MD5
	ImeiSha256       *string                           `protobuf:"bytes,26,opt,name=imei_sha256,json=imeiSha256" json:"imei_sha256,omitempty"`                                     //设备IMEI_SHA256
	Timezone         *string                           `protobuf:"bytes,27,opt,name=timezone" json:"timezone,omitempty"`                                                           //国际化，时区
	GpVersion        *string                           `protobuf:"bytes,28,opt,name=gp_version,json=gpVersion" json:"gp_version,omitempty"`                                        //国际化，google_play_version
	Idfv             *string                           `protobuf:"bytes,29,opt,name=idfv" json:"idfv,omitempty"`                                                                   //国际化，IDFV
	Gaid             *string                           `protobuf:"bytes,30,opt,name=gaid" json:"gaid,omitempty"`                                                                   //国际化，google_advertising_id
	Carrier          *BidRequest_Device_CarrierType    `protobuf:"varint,31,opt,name=carrier,enum=csj.BidRequest_Device_CarrierType" json:"carrier,omitempty"`                     // 可选。运营商类型
	Oaid             *string                           `protobuf:"bytes,32,opt,name=oaid" json:"oaid,omitempty"`                                                                   // 可选。匿名设备标识符
	CarrierName      *string                           `protobuf:"bytes,33,opt,name=carrier_name,json=carrierName" json:"carrier_name,omitempty"`                                  // 运营商名称（设备上获取的原值）
	LocalTzTime      *string                           `protobuf:"bytes,34,opt,name=local_tz_time,json=localTzTime" json:"local_tz_time,omitempty"`                                // 设备的local时区
	StartupTime      *string                           `protobuf:"bytes,35,opt,name=startup_time,json=startupTime" json:"startup_time,omitempty"`                                  // 手机开机时间
	MbTime           *string                           `protobuf:"bytes,36,opt,name=mb_time,json=mbTime" json:"mb_time,omitempty"`                                                 // 系统版本更新时间
	CpuNum           *int32                            `protobuf:"varint,37,opt,name=cpu_num,json=cpuNum" json:"cpu_num,omitempty"`                                                // 设备cpu数量
	DiskTotal        *int64                            `protobuf:"varint,38,opt,name=disk_total,json=diskTotal" json:"disk_total,omitempty"`                                       // 磁盘总空间（字节）
	MemTotal         *int64                            `protobuf:"varint,39,opt,name=mem_total,json=memTotal" json:"mem_total,omitempty"`                                          // 磁盘总空间（字节）
	AuthStatus       *int32                            `protobuf:"varint,40,opt,name=auth_status,json=authStatus" json:"auth_status,omitempty"`                                    // 广告标识授权情况
	CountryCode      *string                           `protobuf:"bytes,41,opt,name=country_code,json=countryCode" json:"country_code,omitempty"`                                  // 国家代码
	DeviceType       *string                           `protobuf:"bytes,42,opt,name=device_type,json=deviceType" json:"device_type,omitempty"`                                     // 硬件型号
	CAID1            *string                           `protobuf:"bytes,43,opt,name=CAID1" json:"CAID1,omitempty"`                                                                 // 当前caid设备标志符号
	CAID1Version     *string                           `protobuf:"bytes,44,opt,name=CAID1_version,json=CAID1Version" json:"CAID1_version,omitempty"`                               // 当前caid版本号
	CAID2            *string                           `protobuf:"bytes,45,opt,name=CAID2" json:"CAID2,omitempty"`                                                                 // 上一个caid设备标志符号
	CAID2Version     *string                           `protobuf:"bytes,46,opt,name=CAID2_version,json=CAID2Version" json:"CAID2_version,omitempty"`                               // 上一个caid设备标志版本号
	SkanVersions     []string                          `protobuf:"bytes,47,rep,name=skan_versions,json=skanVersions" json:"skan_versions,omitempty"`                               // // IOS14以上必填。 设备支持的skan版本
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetDid() string {
	if x != nil && x.Did != nil {
		return *x.Did
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetType() BidRequest_Device_DeviceType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Device_DEVICE_UNKNOWN
}

func (x *BidRequest_Device) GetOs() BidRequest_Device_OsType {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return BidRequest_Device_OS_UNKNOWN
}

func (x *BidRequest_Device) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *BidRequest_Device) GetVendor() string {
	if x != nil && x.Vendor != nil {
		return *x.Vendor
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

func (x *BidRequest_Device) GetConnType() BidRequest_Device_ConnectionType {
	if x != nil && x.ConnType != nil {
		return *x.ConnType
	}
	return BidRequest_Device_CONN_UNKNOWN
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetScreenWidth() uint32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetScreenHeight() uint32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *BidRequest_Device) GetOpenUdid() string {
	if x != nil && x.OpenUdid != nil {
		return *x.OpenUdid
	}
	return ""
}

func (x *BidRequest_Device) GetSsid() string {
	if x != nil && x.Ssid != nil {
		return *x.Ssid
	}
	return ""
}

func (x *BidRequest_Device) GetWifiMac() string {
	if x != nil && x.WifiMac != nil {
		return *x.WifiMac
	}
	return ""
}

func (x *BidRequest_Device) GetPhoneName() string {
	if x != nil && x.PhoneName != nil {
		return *x.PhoneName
	}
	return ""
}

func (x *BidRequest_Device) GetDsid() string {
	if x != nil && x.Dsid != nil {
		return *x.Dsid
	}
	return ""
}

func (x *BidRequest_Device) GetPowerOnTime() string {
	if x != nil && x.PowerOnTime != nil {
		return *x.PowerOnTime
	}
	return ""
}

func (x *BidRequest_Device) GetImsi() string {
	if x != nil && x.Imsi != nil {
		return *x.Imsi
	}
	return ""
}

func (x *BidRequest_Device) GetRomVersion() string {
	if x != nil && x.RomVersion != nil {
		return *x.RomVersion
	}
	return ""
}

func (x *BidRequest_Device) GetSysCompilingTime() string {
	if x != nil && x.SysCompilingTime != nil {
		return *x.SysCompilingTime
	}
	return ""
}

func (x *BidRequest_Device) GetOrientation() BidRequest_Device_Orientation {
	if x != nil && x.Orientation != nil {
		return *x.Orientation
	}
	return BidRequest_Device_UNKNOWN
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetImeiSha256() string {
	if x != nil && x.ImeiSha256 != nil {
		return *x.ImeiSha256
	}
	return ""
}

func (x *BidRequest_Device) GetTimezone() string {
	if x != nil && x.Timezone != nil {
		return *x.Timezone
	}
	return ""
}

func (x *BidRequest_Device) GetGpVersion() string {
	if x != nil && x.GpVersion != nil {
		return *x.GpVersion
	}
	return ""
}

func (x *BidRequest_Device) GetIdfv() string {
	if x != nil && x.Idfv != nil {
		return *x.Idfv
	}
	return ""
}

func (x *BidRequest_Device) GetGaid() string {
	if x != nil && x.Gaid != nil {
		return *x.Gaid
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() BidRequest_Device_CarrierType {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return BidRequest_Device_CARRIER_UNKNOWN
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetCarrierName() string {
	if x != nil && x.CarrierName != nil {
		return *x.CarrierName
	}
	return ""
}

func (x *BidRequest_Device) GetLocalTzTime() string {
	if x != nil && x.LocalTzTime != nil {
		return *x.LocalTzTime
	}
	return ""
}

func (x *BidRequest_Device) GetStartupTime() string {
	if x != nil && x.StartupTime != nil {
		return *x.StartupTime
	}
	return ""
}

func (x *BidRequest_Device) GetMbTime() string {
	if x != nil && x.MbTime != nil {
		return *x.MbTime
	}
	return ""
}

func (x *BidRequest_Device) GetCpuNum() int32 {
	if x != nil && x.CpuNum != nil {
		return *x.CpuNum
	}
	return 0
}

func (x *BidRequest_Device) GetDiskTotal() int64 {
	if x != nil && x.DiskTotal != nil {
		return *x.DiskTotal
	}
	return 0
}

func (x *BidRequest_Device) GetMemTotal() int64 {
	if x != nil && x.MemTotal != nil {
		return *x.MemTotal
	}
	return 0
}

func (x *BidRequest_Device) GetAuthStatus() int32 {
	if x != nil && x.AuthStatus != nil {
		return *x.AuthStatus
	}
	return 0
}

func (x *BidRequest_Device) GetCountryCode() string {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceType() string {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return ""
}

func (x *BidRequest_Device) GetCAID1() string {
	if x != nil && x.CAID1 != nil {
		return *x.CAID1
	}
	return ""
}

func (x *BidRequest_Device) GetCAID1Version() string {
	if x != nil && x.CAID1Version != nil {
		return *x.CAID1Version
	}
	return ""
}

func (x *BidRequest_Device) GetCAID2() string {
	if x != nil && x.CAID2 != nil {
		return *x.CAID2
	}
	return ""
}

func (x *BidRequest_Device) GetCAID2Version() string {
	if x != nil && x.CAID2Version != nil {
		return *x.CAID2Version
	}
	return ""
}

func (x *BidRequest_Device) GetSkanVersions() []string {
	if x != nil {
		return x.SkanVersions
	}
	return nil
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Latitude  *float32 `protobuf:"fixed32,1,opt,name=latitude" json:"latitude,omitempty"`   //纬度
	Longitude *float32 `protobuf:"fixed32,2,opt,name=longitude" json:"longitude,omitempty"` //经度
	City      *string  `protobuf:"bytes,3,opt,name=city" json:"city,omitempty"`             //城市，中文即可(utf-8编码)
	Province  *string  `protobuf:"bytes,4,opt,name=province" json:"province,omitempty"`     //省份，中文即可(utf-8编码)
	District  *string  `protobuf:"bytes,5,opt,name=district" json:"district,omitempty"`     //区县，中文即可(utf-8编码)
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Geo) GetLatitude() float32 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *BidRequest_Geo) GetLongitude() float32 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

func (x *BidRequest_Geo) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *BidRequest_Geo) GetProvince() string {
	if x != nil && x.Province != nil {
		return *x.Province
	}
	return ""
}

func (x *BidRequest_Geo) GetDistrict() string {
	if x != nil && x.District != nil {
		return *x.District
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender   *BidRequest_User_Gender `protobuf:"varint,1,opt,name=gender,enum=csj.BidRequest_User_Gender" json:"gender,omitempty"` // 可选。用户的性别。
	Age      *uint32                 `protobuf:"varint,2,opt,name=age" json:"age,omitempty"`                                       // 可选。用户的年龄。
	PhoneNub *string                 `protobuf:"bytes,3,opt,name=phone_nub,json=phoneNub" json:"phone_nub,omitempty"`              // 可选。手机号码
	Keywords *string                 `protobuf:"bytes,4,opt,name=keywords" json:"keywords,omitempty"`                              // 可选。用户画像的关键词列表，以逗号分隔。
	AppList  []string                `protobuf:"bytes,5,rep,name=app_list,json=appList" json:"app_list,omitempty"`                 // 可选。已经安装的应用列表。
	Data     []*BidRequest_User_Data `protobuf:"bytes,6,rep,name=data" json:"data,omitempty"`                                      // 提供额外的用户信息
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_User) GetGender() BidRequest_User_Gender {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return BidRequest_User_UNKNOWN
}

func (x *BidRequest_User) GetAge() uint32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

func (x *BidRequest_User) GetPhoneNub() string {
	if x != nil && x.PhoneNub != nil {
		return *x.PhoneNub
	}
	return ""
}

func (x *BidRequest_User) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_User) GetAppList() []string {
	if x != nil {
		return x.AppList
	}
	return nil
}

func (x *BidRequest_User) GetData() []*BidRequest_User_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type BidRequest_AdSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                      *string                             `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                                                                                                                        // 必填。广告位id。
	Adtype                  *BidRequest_AdSlot_AdType           `protobuf:"varint,2,req,name=adtype,enum=csj.BidRequest_AdSlot_AdType" json:"adtype,omitempty"`                                                                             // 必填。广告类型。
	Pos                     *BidRequest_AdSlot_Position         `protobuf:"varint,3,req,name=pos,enum=csj.BidRequest_AdSlot_Position" json:"pos,omitempty"`                                                                                 // 必填。广告展现位置。
	AcceptedSize            []*BidRequest_AdSlot_Size           `protobuf:"bytes,4,rep,name=accepted_size,json=acceptedSize" json:"accepted_size,omitempty"`                                                                                // 必填。可选素材尺寸。
	AcceptedCreativeTypes   []BidRequest_AdSlot_CreativeType    `protobuf:"varint,5,rep,name=accepted_creative_types,json=acceptedCreativeTypes,enum=csj.BidRequest_AdSlot_CreativeType" json:"accepted_creative_types,omitempty"`          // 可选。可接受的创意类型。
	AcceptedInteractionType []BidRequest_AdSlot_InteractionType `protobuf:"varint,6,rep,name=accepted_interaction_type,json=acceptedInteractionType,enum=csj.BidRequest_AdSlot_InteractionType" json:"accepted_interaction_type,omitempty"` // 可选。app支持的创意交互类型。
	MinimumCpm              *uint64                             `protobuf:"varint,7,opt,name=minimum_cpm,json=minimumCpm" json:"minimum_cpm,omitempty"`                                                                                     // 可选。最低的cpm出价, 单位为分/cpm。
	AdCount                 *uint32                             `protobuf:"varint,8,opt,name=ad_count,json=adCount" json:"ad_count,omitempty"`                                                                                              // 可选，需要的广告数量
	IsOriginAd              *bool                               `protobuf:"varint,9,opt,name=is_origin_ad,json=isOriginAd" json:"is_origin_ad,omitempty"`                                                                                   // 可选，是否是原生广告
	IsSupportDpl            *bool                               `protobuf:"varint,10,opt,name=is_support_dpl,json=isSupportDpl" json:"is_support_dpl,omitempty"`                                                                            //可选，V1.2b版本必传，是否支持deeplink
	AcceptedImageModes      []int64                             `protobuf:"varint,11,rep,name=accepted_image_modes,json=acceptedImageModes" json:"accepted_image_modes,omitempty"`                                                          // 可选。app支持的image_modes
	RefreshCount            *uint32                             `protobuf:"varint,12,opt,name=refresh_count,json=refreshCount" json:"refresh_count,omitempty"`                                                                              //资讯合作流量，刷次
	RefreshType             *BidRequest_AdSlot_RefreshType      `protobuf:"varint,13,opt,name=refresh_type,json=refreshType,enum=csj.BidRequest_AdSlot_RefreshType" json:"refresh_type,omitempty"`                                          //资讯合作流量，刷新类型
	VideoMinDuration        *uint32                             `protobuf:"varint,14,opt,name=video_min_duration,json=videoMinDuration" json:"video_min_duration,omitempty"`                                                                // 期望视频的最小长度（含），0表示不限制
	VideoMaxDuration        *uint32                             `protobuf:"varint,15,opt,name=video_max_duration,json=videoMaxDuration" json:"video_max_duration,omitempty"`                                                                // 期望视频的最大长度（含），0表示不限制
	// pmp交易相关参数，仅在PD/PDB方式下填写，为空或无效值时默认RTB，需要提前与商务同学确认竞价方式及价格。
	// 同一流量，最多支持n个PD订单，可传多个；仅支持1个PDB订单。
	Pmp                     []*BidRequest_AdSlot_Pmp `protobuf:"bytes,16,rep,name=pmp" json:"pmp,omitempty"`
	SupportPkg              []string                 `protobuf:"bytes,17,rep,name=support_pkg,json=supportPkg" json:"support_pkg,omitempty"`                                             // 包名白名单，只出在名单内的包名广告
	IsSupportUlink          *bool                    `protobuf:"varint,18,opt,name=is_support_ulink,json=isSupportUlink" json:"is_support_ulink,omitempty"`                              // 可选，是否支持ulink调起
	IsFilterUnlistedPackage *bool                    `protobuf:"varint,19,opt,name=is_filter_unlisted_package,json=isFilterUnlistedPackage" json:"is_filter_unlisted_package,omitempty"` // 只有荣耀能使用，是否过滤未上架应用
	ParentPkg               *string                  `protobuf:"bytes,20,opt,name=parent_pkg,json=parentPkg" json:"parent_pkg,omitempty"`                                                // 母应用
	EasyPlayType            *int32                   `protobuf:"varint,21,opt,name=easy_play_type,json=easyPlayType" json:"easy_play_type,omitempty"`                                    // 轻互动广告位类型 0为不支持 1竖版激励 2横版激励 3沉浸视频流
	WxAppletType            *int32                   `protobuf:"varint,22,opt,name=wx_applet_type,json=wxAppletType" json:"wx_applet_type,omitempty"`                                    // dsp媒体支持的微信小程序调起方式 1为支持sdk调起 0为不支持
}

func (x *BidRequest_AdSlot) Reset() {
	*x = BidRequest_AdSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot) ProtoMessage() {}

func (x *BidRequest_AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_AdSlot) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_AdSlot) GetAdtype() BidRequest_AdSlot_AdType {
	if x != nil && x.Adtype != nil {
		return *x.Adtype
	}
	return BidRequest_AdSlot_BANNER
}

func (x *BidRequest_AdSlot) GetPos() BidRequest_AdSlot_Position {
	if x != nil && x.Pos != nil {
		return *x.Pos
	}
	return BidRequest_AdSlot_TOP
}

func (x *BidRequest_AdSlot) GetAcceptedSize() []*BidRequest_AdSlot_Size {
	if x != nil {
		return x.AcceptedSize
	}
	return nil
}

func (x *BidRequest_AdSlot) GetAcceptedCreativeTypes() []BidRequest_AdSlot_CreativeType {
	if x != nil {
		return x.AcceptedCreativeTypes
	}
	return nil
}

func (x *BidRequest_AdSlot) GetAcceptedInteractionType() []BidRequest_AdSlot_InteractionType {
	if x != nil {
		return x.AcceptedInteractionType
	}
	return nil
}

func (x *BidRequest_AdSlot) GetMinimumCpm() uint64 {
	if x != nil && x.MinimumCpm != nil {
		return *x.MinimumCpm
	}
	return 0
}

func (x *BidRequest_AdSlot) GetAdCount() uint32 {
	if x != nil && x.AdCount != nil {
		return *x.AdCount
	}
	return 0
}

func (x *BidRequest_AdSlot) GetIsOriginAd() bool {
	if x != nil && x.IsOriginAd != nil {
		return *x.IsOriginAd
	}
	return false
}

func (x *BidRequest_AdSlot) GetIsSupportDpl() bool {
	if x != nil && x.IsSupportDpl != nil {
		return *x.IsSupportDpl
	}
	return false
}

func (x *BidRequest_AdSlot) GetAcceptedImageModes() []int64 {
	if x != nil {
		return x.AcceptedImageModes
	}
	return nil
}

func (x *BidRequest_AdSlot) GetRefreshCount() uint32 {
	if x != nil && x.RefreshCount != nil {
		return *x.RefreshCount
	}
	return 0
}

func (x *BidRequest_AdSlot) GetRefreshType() BidRequest_AdSlot_RefreshType {
	if x != nil && x.RefreshType != nil {
		return *x.RefreshType
	}
	return BidRequest_AdSlot_Open
}

func (x *BidRequest_AdSlot) GetVideoMinDuration() uint32 {
	if x != nil && x.VideoMinDuration != nil {
		return *x.VideoMinDuration
	}
	return 0
}

func (x *BidRequest_AdSlot) GetVideoMaxDuration() uint32 {
	if x != nil && x.VideoMaxDuration != nil {
		return *x.VideoMaxDuration
	}
	return 0
}

func (x *BidRequest_AdSlot) GetPmp() []*BidRequest_AdSlot_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

func (x *BidRequest_AdSlot) GetSupportPkg() []string {
	if x != nil {
		return x.SupportPkg
	}
	return nil
}

func (x *BidRequest_AdSlot) GetIsSupportUlink() bool {
	if x != nil && x.IsSupportUlink != nil {
		return *x.IsSupportUlink
	}
	return false
}

func (x *BidRequest_AdSlot) GetIsFilterUnlistedPackage() bool {
	if x != nil && x.IsFilterUnlistedPackage != nil {
		return *x.IsFilterUnlistedPackage
	}
	return false
}

func (x *BidRequest_AdSlot) GetParentPkg() string {
	if x != nil && x.ParentPkg != nil {
		return *x.ParentPkg
	}
	return ""
}

func (x *BidRequest_AdSlot) GetEasyPlayType() int32 {
	if x != nil && x.EasyPlayType != nil {
		return *x.EasyPlayType
	}
	return 0
}

func (x *BidRequest_AdSlot) GetWxAppletType() int32 {
	if x != nil && x.WxAppletType != nil {
		return *x.WxAppletType
	}
	return 0
}

type BidRequest_User_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Value *string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *BidRequest_User_Data) Reset() {
	*x = BidRequest_User_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User_Data) ProtoMessage() {}

func (x *BidRequest_User_Data) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User_Data.ProtoReflect.Descriptor instead.
func (*BidRequest_User_Data) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *BidRequest_User_Data) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_User_Data) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// pmp交易相关参数
type BidRequest_AdSlot_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订单id，仅在PD/PDB方式下填写，为空或无效值时默认RTB，需要提前与商务同学确认竞价方式及价格，提供订单id由运营同学线下配置。
	Dealid *string `protobuf:"bytes,1,req,name=dealid" json:"dealid,omitempty"`
	// 竞价方式。0代表PDB，1代表PD。实际投放以运营同学线下配置为准。
	At *uint32 `protobuf:"varint,2,req,name=at" json:"at,omitempty"`
	// 订单id对应的结算价格，单位：cpm 分
	// 仅做验证使用，实际投放以运营同学线下配置为准
	Price *uint64 `protobuf:"varint,3,opt,name=price" json:"price,omitempty"`
}

func (x *BidRequest_AdSlot_Pmp) Reset() {
	*x = BidRequest_AdSlot_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_Pmp) ProtoMessage() {}

func (x *BidRequest_AdSlot_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_Pmp) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 0}
}

func (x *BidRequest_AdSlot_Pmp) GetDealid() string {
	if x != nil && x.Dealid != nil {
		return *x.Dealid
	}
	return ""
}

func (x *BidRequest_AdSlot_Pmp) GetAt() uint32 {
	if x != nil && x.At != nil {
		return *x.At
	}
	return 0
}

func (x *BidRequest_AdSlot_Pmp) GetPrice() uint64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

type BidRequest_AdSlot_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width        *uint32 `protobuf:"varint,1,req,name=width" json:"width,omitempty"`               // 宽度。
	Height       *uint32 `protobuf:"varint,2,req,name=height" json:"height,omitempty"`             // 高度。
	CreativeType *uint32 `protobuf:"varint,3,opt,name=creativeType" json:"creativeType,omitempty"` // 素材类型 1单图 2组图 4视频
}

func (x *BidRequest_AdSlot_Size) Reset() {
	*x = BidRequest_AdSlot_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_Size) ProtoMessage() {}

func (x *BidRequest_AdSlot_Size) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_Size.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_Size) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{0, 5, 1}
}

func (x *BidRequest_AdSlot_Size) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot_Size) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidRequest_AdSlot_Size) GetCreativeType() uint32 {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return 0
}

type BidResponse_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdId        *string                       `protobuf:"bytes,1,req,name=ad_id,json=adId" json:"ad_id,omitempty"`                      // 必填。创意id。
	Creative    *BidResponse_Ad_MaterialMeta  `protobuf:"bytes,2,req,name=creative" json:"creative,omitempty"`                          // 必填。素材。
	Price       *uint64                       `protobuf:"varint,3,opt,name=price" json:"price,omitempty"`                               // 出价
	FilterWords []*BidResponse_Ad_FilterWords `protobuf:"bytes,4,rep,name=filter_words,json=filterWords" json:"filter_words,omitempty"` //不喜欢反馈项
	Dealid      *string                       `protobuf:"bytes,5,opt,name=dealid" json:"dealid,omitempty"`                              // 订单id，仅在PD/PDB方式下返回
}

func (x *BidResponse_Ad) Reset() {
	*x = BidResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad) ProtoMessage() {}

func (x *BidResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_Ad) GetAdId() string {
	if x != nil && x.AdId != nil {
		return *x.AdId
	}
	return ""
}

func (x *BidResponse_Ad) GetCreative() *BidResponse_Ad_MaterialMeta {
	if x != nil {
		return x.Creative
	}
	return nil
}

func (x *BidResponse_Ad) GetPrice() uint64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_Ad) GetFilterWords() []*BidResponse_Ad_FilterWords {
	if x != nil {
		return x.FilterWords
	}
	return nil
}

func (x *BidResponse_Ad) GetDealid() string {
	if x != nil && x.Dealid != nil {
		return *x.Dealid
	}
	return ""
}

type BidResponse_Ad_FilterWords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                                    //反馈项编号
	IsSelected *bool   `protobuf:"varint,2,opt,name=is_selected,json=isSelected" json:"is_selected,omitempty"` //是否默认勾选
	Name       *string `protobuf:"bytes,3,req,name=name" json:"name,omitempty"`                                //反馈项描述
}

func (x *BidResponse_Ad_FilterWords) Reset() {
	*x = BidResponse_Ad_FilterWords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_FilterWords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_FilterWords) ProtoMessage() {}

func (x *BidResponse_Ad_FilterWords) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_FilterWords.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_FilterWords) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_Ad_FilterWords) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse_Ad_FilterWords) GetIsSelected() bool {
	if x != nil && x.IsSelected != nil {
		return *x.IsSelected
	}
	return false
}

func (x *BidResponse_Ad_FilterWords) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type BidResponse_Ad_MaterialMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeType       *BidResponse_Ad_MaterialMeta_CreativeType    `protobuf:"varint,1,req,name=creative_type,json=creativeType,enum=csj.BidResponse_Ad_MaterialMeta_CreativeType" json:"creative_type,omitempty"`             // 必填。该广告的创意类型。
	InteractionType    *BidResponse_Ad_MaterialMeta_InteractionType `protobuf:"varint,2,opt,name=interaction_type,json=interactionType,enum=csj.BidResponse_Ad_MaterialMeta_InteractionType" json:"interaction_type,omitempty"` // 可选。广告支持的交互类型。
	Image              *BidResponse_Ad_MaterialMeta_Image           `protobuf:"bytes,3,opt,name=image" json:"image,omitempty"`                                                                                                  // 可选。素材图片。
	TargetUrl          *string                                      `protobuf:"bytes,4,opt,name=target_url,json=targetUrl" json:"target_url,omitempty"`                                                                         // 可选。创意的落地页url。
	DownloadUrl        *string                                      `protobuf:"bytes,5,opt,name=download_url,json=downloadUrl" json:"download_url,omitempty"`                                                                   // 可选,应用下载必填。应用下载url。
	Title              *string                                      `protobuf:"bytes,6,opt,name=title" json:"title,omitempty"`                                                                                                  // 可选。广告标题。
	Description        *string                                      `protobuf:"bytes,7,opt,name=description" json:"description,omitempty"`                                                                                      // 可选。广告描述。
	AppName            *string                                      `protobuf:"bytes,8,opt,name=app_name,json=appName" json:"app_name,omitempty"`                                                                               // 可选。针对应用下载类广告。
	PackageName        *string                                      `protobuf:"bytes,9,opt,name=package_name,json=packageName" json:"package_name,omitempty"`                                                                   // 可选。安卓应用下载包名。
	WinNoticeUrl       []string                                     `protobuf:"bytes,10,rep,name=win_notice_url,json=winNoticeUrl" json:"win_notice_url,omitempty"`                                                             // 可选。获胜通知的url列表。
	ShowUrl            []string                                     `protobuf:"bytes,11,rep,name=show_url,json=showUrl" json:"show_url,omitempty"`                                                                              // 可选。展现监测url列表。
	ClickUrl           []string                                     `protobuf:"bytes,12,rep,name=click_url,json=clickUrl" json:"click_url,omitempty"`                                                                           // 可选。点击监测url列表。
	Ext                *string                                      `protobuf:"bytes,13,opt,name=ext" json:"ext,omitempty"`                                                                                                     // 可选。扩展字段，DSP方希望通过监测url回传的数据。
	ImageMode          *BidResponse_Ad_MaterialMeta_ImageMode       `protobuf:"varint,14,opt,name=image_mode,json=imageMode,enum=csj.BidResponse_Ad_MaterialMeta_ImageMode" json:"image_mode,omitempty"`                        // 素材模式,
	ImageList          []*BidResponse_Ad_MaterialMeta_Image         `protobuf:"bytes,15,rep,name=image_list,json=imageList" json:"image_list,omitempty"`                                                                        // 多图
	PhoneNum           *string                                      `protobuf:"bytes,16,opt,name=phone_num,json=phoneNum" json:"phone_num,omitempty"`                                                                           //电话拨打广告，号码
	ButtonText         *string                                      `protobuf:"bytes,17,opt,name=button_text,json=buttonText" json:"button_text,omitempty"`                                                                     //电话拨打广告，按钮名称
	Source             *string                                      `protobuf:"bytes,18,opt,name=source" json:"source,omitempty"`                                                                                               // 落地页的来源
	Icon               *string                                      `protobuf:"bytes,19,opt,name=icon" json:"icon,omitempty"`                                                                                                   // icon
	DeeplinkUrl        *string                                      `protobuf:"bytes,20,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`                                                                  // 可选。应用吊起链接。
	Video              *BidResponse_Ad_MaterialMeta_Video           `protobuf:"bytes,21,opt,name=video" json:"video,omitempty"`                                                                                                 // 视频
	Subtitle           *string                                      `protobuf:"bytes,22,opt,name=subtitle" json:"subtitle,omitempty"`                                                                                           //副标题
	CommentNum         *int64                                       `protobuf:"varint,23,opt,name=comment_num,json=commentNum" json:"comment_num,omitempty"`                                                                    //可选。应用下载可用，应用评论人数
	Score              *int32                                       `protobuf:"varint,24,opt,name=score" json:"score,omitempty"`                                                                                                //可选。应用下载可用，应用评分星级
	TrackingEvent      []*BidResponse_Ad_MaterialMeta_Tracking      `protobuf:"bytes,25,rep,name=tracking_event,json=trackingEvent" json:"tracking_event,omitempty"`                                                            //可选。其他打点上报url
	ProductSize        *int32                                       `protobuf:"varint,26,opt,name=product_size,json=productSize" json:"product_size,omitempty"`                                                                 // 可选。安装包大小
	ProductMd5         *string                                      `protobuf:"bytes,27,opt,name=product_md5,json=productMd5" json:"product_md5,omitempty"`                                                                     // 可选。安装包md5
	CreativeId         *int64                                       `protobuf:"varint,28,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`                                                                    // 可选。广告物料的唯一标识
	Channel            *string                                      `protobuf:"bytes,29,opt,name=channel" json:"channel,omitempty"`                                                                                             // 可选。分包送审的渠道标识
	IsSupportMarketUrl *bool                                        `protobuf:"varint,30,opt,name=is_support_market_url,json=isSupportMarketUrl" json:"is_support_market_url,omitempty"`                                        // 可选。是否支持商店直投url
	MarketUrl          *string                                      `protobuf:"bytes,31,opt,name=market_url,json=marketUrl" json:"market_url,omitempty"`                                                                        // 可选。商店直投url
	CreativeAuditId    *string                                      `protobuf:"bytes,32,opt,name=creative_audit_id,json=creativeAuditId" json:"creative_audit_id,omitempty"`                                                    // 可选。物料送审唯一标识
	Skan               *BidResponse_Ad_SkanParameters               `protobuf:"bytes,33,opt,name=skan" json:"skan,omitempty"`                                                                                                   // IOS14以上必填。广告的skan参数
	IsSupportAppInfo   *bool                                        `protobuf:"varint,34,opt,name=is_support_app_info,json=isSupportAppInfo" json:"is_support_app_info,omitempty"`                                              // 可选。媒体是否需要拼接下载合规
	AppInfo            *BidResponse_Ad_AppInfo                      `protobuf:"bytes,35,opt,name=app_info,json=appInfo" json:"app_info,omitempty"`                                                                              // 可选。下载合规信息
	UlinkUrl           *string                                      `protobuf:"bytes,36,opt,name=ulink_url,json=ulinkUrl" json:"ulink_url,omitempty"`                                                                           // ulink调起链接
	AdExtraContent     *string                                      `protobuf:"bytes,37,opt,name=ad_extra_content,json=adExtraContent" json:"ad_extra_content,omitempty"`                                                       // 一些ad额外信息，json结构
	FirstIndustryId    *int64                                       `protobuf:"varint,38,opt,name=first_industry_id,json=firstIndustryId" json:"first_industry_id,omitempty"`                                                   // 广告一级行业ID
	SecondIndustryId   *int64                                       `protobuf:"varint,39,opt,name=second_industry_id,json=secondIndustryId" json:"second_industry_id,omitempty"`                                                // 广告二级行业ID
	EasyPlayable       *string                                      `protobuf:"bytes,40,opt,name=easy_playable,json=easyPlayable" json:"easy_playable,omitempty"`                                                               // dsp轻互动组件信息
	PlayTrackers       []string                                     `protobuf:"bytes,41,rep,name=play_trackers,json=playTrackers" json:"play_trackers,omitempty"`                                                               // 轻互动监测地址
	ChannelId          *string                                      `protobuf:"bytes,42,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`                                                                        // 渠道包id
	QuickAppUrl        *string                                      `protobuf:"bytes,43,opt,name=quick_app_url,json=quickAppUrl" json:"quick_app_url,omitempty"`                                                                // 快应用调起链接
}

func (x *BidResponse_Ad_MaterialMeta) Reset() {
	*x = BidResponse_Ad_MaterialMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *BidResponse_Ad_MaterialMeta) GetCreativeType() BidResponse_Ad_MaterialMeta_CreativeType {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return BidResponse_Ad_MaterialMeta_TEXT
}

func (x *BidResponse_Ad_MaterialMeta) GetInteractionType() BidResponse_Ad_MaterialMeta_InteractionType {
	if x != nil && x.InteractionType != nil {
		return *x.InteractionType
	}
	return BidResponse_Ad_MaterialMeta_NO_INTERACTION
}

func (x *BidResponse_Ad_MaterialMeta) GetImage() *BidResponse_Ad_MaterialMeta_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetTargetUrl() string {
	if x != nil && x.TargetUrl != nil {
		return *x.TargetUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetWinNoticeUrl() []string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetShowUrl() []string {
	if x != nil {
		return x.ShowUrl
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetClickUrl() []string {
	if x != nil {
		return x.ClickUrl
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetExt() string {
	if x != nil && x.Ext != nil {
		return *x.Ext
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetImageMode() BidResponse_Ad_MaterialMeta_ImageMode {
	if x != nil && x.ImageMode != nil {
		return *x.ImageMode
	}
	return BidResponse_Ad_MaterialMeta_SMALL
}

func (x *BidResponse_Ad_MaterialMeta) GetImageList() []*BidResponse_Ad_MaterialMeta_Image {
	if x != nil {
		return x.ImageList
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetPhoneNum() string {
	if x != nil && x.PhoneNum != nil {
		return *x.PhoneNum
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetButtonText() string {
	if x != nil && x.ButtonText != nil {
		return *x.ButtonText
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetVideo() *BidResponse_Ad_MaterialMeta_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetSubtitle() string {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetCommentNum() int64 {
	if x != nil && x.CommentNum != nil {
		return *x.CommentNum
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetScore() int32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetTrackingEvent() []*BidResponse_Ad_MaterialMeta_Tracking {
	if x != nil {
		return x.TrackingEvent
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetProductSize() int32 {
	if x != nil && x.ProductSize != nil {
		return *x.ProductSize
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetProductMd5() string {
	if x != nil && x.ProductMd5 != nil {
		return *x.ProductMd5
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetCreativeId() int64 {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetChannel() string {
	if x != nil && x.Channel != nil {
		return *x.Channel
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetIsSupportMarketUrl() bool {
	if x != nil && x.IsSupportMarketUrl != nil {
		return *x.IsSupportMarketUrl
	}
	return false
}

func (x *BidResponse_Ad_MaterialMeta) GetMarketUrl() string {
	if x != nil && x.MarketUrl != nil {
		return *x.MarketUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetCreativeAuditId() string {
	if x != nil && x.CreativeAuditId != nil {
		return *x.CreativeAuditId
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetSkan() *BidResponse_Ad_SkanParameters {
	if x != nil {
		return x.Skan
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetIsSupportAppInfo() bool {
	if x != nil && x.IsSupportAppInfo != nil {
		return *x.IsSupportAppInfo
	}
	return false
}

func (x *BidResponse_Ad_MaterialMeta) GetAppInfo() *BidResponse_Ad_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetUlinkUrl() string {
	if x != nil && x.UlinkUrl != nil {
		return *x.UlinkUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetAdExtraContent() string {
	if x != nil && x.AdExtraContent != nil {
		return *x.AdExtraContent
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetFirstIndustryId() int64 {
	if x != nil && x.FirstIndustryId != nil {
		return *x.FirstIndustryId
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetSecondIndustryId() int64 {
	if x != nil && x.SecondIndustryId != nil {
		return *x.SecondIndustryId
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta) GetEasyPlayable() string {
	if x != nil && x.EasyPlayable != nil {
		return *x.EasyPlayable
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetPlayTrackers() []string {
	if x != nil {
		return x.PlayTrackers
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetChannelId() string {
	if x != nil && x.ChannelId != nil {
		return *x.ChannelId
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetQuickAppUrl() string {
	if x != nil && x.QuickAppUrl != nil {
		return *x.QuickAppUrl
	}
	return ""
}

type BidResponse_Ad_SkanParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreProductParameters *BidResponse_Ad_SkanParameters_SkanStoreProductParams `protobuf:"bytes,1,opt,name=store_product_parameters,json=storeProductParameters" json:"store_product_parameters,omitempty"`
	AdImpression           *BidResponse_Ad_SkanParameters_SkanAdImpression       `protobuf:"bytes,2,opt,name=ad_impression,json=adImpression" json:"ad_impression,omitempty"`
}

func (x *BidResponse_Ad_SkanParameters) Reset() {
	*x = BidResponse_Ad_SkanParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_SkanParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_SkanParameters) ProtoMessage() {}

func (x *BidResponse_Ad_SkanParameters) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_SkanParameters.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_SkanParameters) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 2}
}

func (x *BidResponse_Ad_SkanParameters) GetStoreProductParameters() *BidResponse_Ad_SkanParameters_SkanStoreProductParams {
	if x != nil {
		return x.StoreProductParameters
	}
	return nil
}

func (x *BidResponse_Ad_SkanParameters) GetAdImpression() *BidResponse_Ad_SkanParameters_SkanAdImpression {
	if x != nil {
		return x.AdImpression
	}
	return nil
}

type BidResponse_Ad_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppVersion       *string                                  `protobuf:"bytes,1,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	DeveloperName    *string                                  `protobuf:"bytes,2,opt,name=developer_name,json=developerName" json:"developer_name,omitempty"`
	Permissions      []*BidResponse_Ad_AppInfo_PermissionItem `protobuf:"bytes,3,rep,name=permissions" json:"permissions,omitempty"`
	PrivacyPolicyUrl *string                                  `protobuf:"bytes,4,opt,name=privacy_policy_url,json=privacyPolicyUrl" json:"privacy_policy_url,omitempty"`
	PackageName      *string                                  `protobuf:"bytes,5,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	AppName          *string                                  `protobuf:"bytes,6,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	PermissionsUrl   *string                                  `protobuf:"bytes,7,opt,name=permissions_url,json=permissionsUrl" json:"permissions_url,omitempty"`
	DescUrl          *string                                  `protobuf:"bytes,8,opt,name=desc_url,json=descUrl" json:"desc_url,omitempty"`
}

func (x *BidResponse_Ad_AppInfo) Reset() {
	*x = BidResponse_Ad_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_AppInfo) ProtoMessage() {}

func (x *BidResponse_Ad_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_AppInfo) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 3}
}

func (x *BidResponse_Ad_AppInfo) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetDeveloperName() string {
	if x != nil && x.DeveloperName != nil {
		return *x.DeveloperName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetPermissions() []*BidResponse_Ad_AppInfo_PermissionItem {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *BidResponse_Ad_AppInfo) GetPrivacyPolicyUrl() string {
	if x != nil && x.PrivacyPolicyUrl != nil {
		return *x.PrivacyPolicyUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetPermissionsUrl() string {
	if x != nil && x.PermissionsUrl != nil {
		return *x.PermissionsUrl
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo) GetDescUrl() string {
	if x != nil && x.DescUrl != nil {
		return *x.DescUrl
	}
	return ""
}

// 图片素材信息。
type BidResponse_Ad_MaterialMeta_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	Width  *uint32 `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`
	Height *uint32 `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`
}

func (x *BidResponse_Ad_MaterialMeta_Image) Reset() {
	*x = BidResponse_Ad_MaterialMeta_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta_Image) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta_Image) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta_Image) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 0}
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

type BidResponse_Ad_MaterialMeta_Tracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *string `protobuf:"bytes,1,opt,name=event" json:"event,omitempty"`
	Url   *string `protobuf:"bytes,2,opt,name=url" json:"url,omitempty"`
}

func (x *BidResponse_Ad_MaterialMeta_Tracking) Reset() {
	*x = BidResponse_Ad_MaterialMeta_Tracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta_Tracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta_Tracking) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta_Tracking) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_Tracking.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta_Tracking) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 1}
}

func (x *BidResponse_Ad_MaterialMeta_Tracking) GetEvent() string {
	if x != nil && x.Event != nil {
		return *x.Event
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Tracking) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

// 视频
type BidResponse_Ad_MaterialMeta_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverHeight   *uint32  `protobuf:"varint,1,opt,name=cover_height,json=coverHeight" json:"cover_height,omitempty"`
	CoverWidth    *uint32  `protobuf:"varint,2,opt,name=cover_width,json=coverWidth" json:"cover_width,omitempty"`
	CoverUrl      *string  `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl" json:"cover_url,omitempty"`
	Resolution    *string  `protobuf:"bytes,4,opt,name=resolution" json:"resolution,omitempty"`
	Size          *uint64  `protobuf:"varint,5,opt,name=size" json:"size,omitempty"`
	VideoDuration *float32 `protobuf:"fixed32,6,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`
	VideoUrl      *string  `protobuf:"bytes,7,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`
}

func (x *BidResponse_Ad_MaterialMeta_Video) Reset() {
	*x = BidResponse_Ad_MaterialMeta_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta_Video) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta_Video) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta_Video) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 1, 2}
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverHeight() uint32 {
	if x != nil && x.CoverHeight != nil {
		return *x.CoverHeight
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverWidth() uint32 {
	if x != nil && x.CoverWidth != nil {
		return *x.CoverWidth
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverUrl() string {
	if x != nil && x.CoverUrl != nil {
		return *x.CoverUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetResolution() string {
	if x != nil && x.Resolution != nil {
		return *x.Resolution
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetSize() uint64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetVideoDuration() float32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

type BidResponse_Ad_SkanParameters_SkanStoreProductParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version      *string `protobuf:"bytes,1,req,name=version" json:"version,omitempty"`
	AdNetworkId  *string `protobuf:"bytes,2,req,name=ad_network_id,json=adNetworkId" json:"ad_network_id,omitempty"`
	CampaignId   *int64  `protobuf:"varint,3,req,name=campaign_id,json=campaignId" json:"campaign_id,omitempty"`
	Nonce        *string `protobuf:"bytes,4,req,name=nonce" json:"nonce,omitempty"`
	ItunesItemId *int64  `protobuf:"varint,5,req,name=itunes_item_id,json=itunesItemId" json:"itunes_item_id,omitempty"`
	SourceAppId  *int64  `protobuf:"varint,6,req,name=source_app_id,json=sourceAppId" json:"source_app_id,omitempty"`
	Timestamp    *int64  `protobuf:"varint,7,req,name=timestamp" json:"timestamp,omitempty"`
	Signature    *string `protobuf:"bytes,8,req,name=signature" json:"signature,omitempty"`
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) Reset() {
	*x = BidResponse_Ad_SkanParameters_SkanStoreProductParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_SkanParameters_SkanStoreProductParams) ProtoMessage() {}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_SkanParameters_SkanStoreProductParams.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_SkanParameters_SkanStoreProductParams) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 2, 0}
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetAdNetworkId() string {
	if x != nil && x.AdNetworkId != nil {
		return *x.AdNetworkId
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetCampaignId() int64 {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetNonce() string {
	if x != nil && x.Nonce != nil {
		return *x.Nonce
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetItunesItemId() int64 {
	if x != nil && x.ItunesItemId != nil {
		return *x.ItunesItemId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetSourceAppId() int64 {
	if x != nil && x.SourceAppId != nil {
		return *x.SourceAppId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanStoreProductParams) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

type BidResponse_Ad_SkanParameters_SkanAdImpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version         *string `protobuf:"bytes,1,req,name=version" json:"version,omitempty"`
	AdNetworkId     *string `protobuf:"bytes,2,req,name=ad_network_id,json=adNetworkId" json:"ad_network_id,omitempty"`
	AdCampaignId    *int64  `protobuf:"varint,3,req,name=ad_campaign_id,json=adCampaignId" json:"ad_campaign_id,omitempty"`
	AdImpressionId  *string `protobuf:"bytes,4,req,name=ad_impression_id,json=adImpressionId" json:"ad_impression_id,omitempty"`
	AdvertisedAppId *int64  `protobuf:"varint,5,req,name=advertised_app_id,json=advertisedAppId" json:"advertised_app_id,omitempty"`
	SourceAppId     *int64  `protobuf:"varint,6,req,name=source_app_id,json=sourceAppId" json:"source_app_id,omitempty"`
	Timestamp       *int64  `protobuf:"varint,7,req,name=timestamp" json:"timestamp,omitempty"`
	Signature       *string `protobuf:"bytes,8,req,name=signature" json:"signature,omitempty"`
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) Reset() {
	*x = BidResponse_Ad_SkanParameters_SkanAdImpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_SkanParameters_SkanAdImpression) ProtoMessage() {}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_SkanParameters_SkanAdImpression.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_SkanParameters_SkanAdImpression) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 2, 1}
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetAdNetworkId() string {
	if x != nil && x.AdNetworkId != nil {
		return *x.AdNetworkId
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetAdCampaignId() int64 {
	if x != nil && x.AdCampaignId != nil {
		return *x.AdCampaignId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetAdImpressionId() string {
	if x != nil && x.AdImpressionId != nil {
		return *x.AdImpressionId
	}
	return ""
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetAdvertisedAppId() int64 {
	if x != nil && x.AdvertisedAppId != nil {
		return *x.AdvertisedAppId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetSourceAppId() int64 {
	if x != nil && x.SourceAppId != nil {
		return *x.SourceAppId
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *BidResponse_Ad_SkanParameters_SkanAdImpression) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

type BidResponse_Ad_AppInfo_PermissionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionName *string `protobuf:"bytes,1,opt,name=permission_name,json=permissionName" json:"permission_name,omitempty"`
	PermissionDesc *string `protobuf:"bytes,2,opt,name=permission_desc,json=permissionDesc" json:"permission_desc,omitempty"`
}

func (x *BidResponse_Ad_AppInfo_PermissionItem) Reset() {
	*x = BidResponse_Ad_AppInfo_PermissionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_1_5_5_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_AppInfo_PermissionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_AppInfo_PermissionItem) ProtoMessage() {}

func (x *BidResponse_Ad_AppInfo_PermissionItem) ProtoReflect() protoreflect.Message {
	mi := &file_csj_1_5_5_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_AppInfo_PermissionItem.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_AppInfo_PermissionItem) Descriptor() ([]byte, []int) {
	return file_csj_1_5_5_proto_rawDescGZIP(), []int{1, 0, 3, 0}
}

func (x *BidResponse_Ad_AppInfo_PermissionItem) GetPermissionName() string {
	if x != nil && x.PermissionName != nil {
		return *x.PermissionName
	}
	return ""
}

func (x *BidResponse_Ad_AppInfo_PermissionItem) GetPermissionDesc() string {
	if x != nil && x.PermissionDesc != nil {
		return *x.PermissionDesc
	}
	return ""
}

var File_csj_1_5_5_proto protoreflect.FileDescriptor

var file_csj_1_5_5_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x63, 0x73, 0x6a, 0x5f, 0x31, 0x2e, 0x35, 0x2e, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x03, 0x63, 0x73, 0x6a, 0x22, 0xed, 0x28, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x02, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x25, 0x0a, 0x03, 0x77, 0x61, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x57, 0x61, 0x70, 0x52, 0x03, 0x77, 0x61, 0x70, 0x12, 0x25, 0x0a, 0x03, 0x61, 0x70, 0x70,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70,
	0x12, 0x2e, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x02, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x09, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x30, 0x0a, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f,
	0x74, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x64, 0x5f, 0x73, 0x64, 0x6b, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x53, 0x64,
	0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x62, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x74, 0x6d, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x74, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x64, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x78, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x64, 0x78, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x1a, 0x8e, 0x02, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x1e, 0x0a, 0x0b, 0x69,
	0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x41, 0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x6b, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x75, 0x6e, 0x65, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x75, 0x6e, 0x65,
	0x73, 0x49, 0x64, 0x1a, 0xc2, 0x01, 0x0a, 0x03, 0x57, 0x61, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x73, 0x69,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69,
	0x74, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x0c, 0x73, 0x69, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x1a, 0xce, 0x0e, 0x0a, 0x06, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63,
	0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x73,
	0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0b,
	0x20, 0x02, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x0c, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70,
	0x65, 0x6e, 0x55, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x73, 0x69, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x73, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69,
	0x66, 0x69, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x69,
	0x66, 0x69, 0x4d, 0x61, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x73, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x73, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x4f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x6d, 0x73, 0x69, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x73, 0x69,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x6f, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x79, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x79, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x44, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x72, 0x69,
	0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d, 0x65, 0x69, 0x53, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x67, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x64, 0x66, 0x76, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76,
	0x12, 0x12, 0x0a, 0x04, 0x67, 0x61, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x67, 0x61, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x74, 0x7a, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x54, 0x7a, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x62, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x62, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x70, 0x75,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x70, 0x75, 0x4e,
	0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x26, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x27,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x28, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x41, 0x49, 0x44, 0x31, 0x18, 0x2b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x43, 0x41, 0x49, 0x44, 0x31, 0x12, 0x23, 0x0a, 0x0d, 0x43, 0x41, 0x49,
	0x44, 0x31, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x43, 0x41, 0x49, 0x44, 0x31, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x41, 0x49, 0x44, 0x32, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43,
	0x41, 0x49, 0x44, 0x32, 0x12, 0x23, 0x0a, 0x0d, 0x43, 0x41, 0x49, 0x44, 0x32, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x41, 0x49,
	0x44, 0x32, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x61,
	0x6e, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x6b, 0x61, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x3f,
	0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54,
	0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x54, 0x56, 0x10, 0x03, 0x22,
	0x3b, 0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x53, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44,
	0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x03, 0x22, 0x68, 0x0a, 0x0e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x35, 0x47, 0x10, 0x05, 0x22, 0x38, 0x0a, 0x0b, 0x4f, 0x72, 0x69, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41, 0x4c, 0x10, 0x02,
	0x22, 0x47, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x49, 0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x1a, 0x8b, 0x01, 0x0a, 0x03, 0x47, 0x65,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x1a, 0xaf, 0x02, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x33, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x62, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x73, 0x6a, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x30, 0x0a, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x2b, 0x0a, 0x06,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10, 0x02, 0x1a, 0x9a, 0x0d, 0x0a, 0x06, 0x41, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x41, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x03, 0x70,
	0x6f, 0x73, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74,
	0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x12, 0x40,
	0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x69,
	0x7a, 0x65, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x5b, 0x0a, 0x17, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x15, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x62, 0x0a,
	0x19, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x65, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x70, 0x6d,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x43,
	0x70, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x70,
	0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x70, 0x6c, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65,
	0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x69, 0x6e,
	0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x10, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x4d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63,
	0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x6b, 0x67, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6b, 0x67, 0x12, 0x28,
	0x0a, 0x10, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x55, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x55, 0x6e, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x6b, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x50, 0x6b, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x61, 0x73, 0x79, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x61,
	0x73, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x78,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x77, 0x78, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x1a, 0x43, 0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x02, 0x61, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x1a, 0x58, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x94, 0x01, 0x0a, 0x06, 0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x41,
	0x4e, 0x4e, 0x45, 0x52, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53,
	0x54, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41,
	0x53, 0x48, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x53,
	0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x45, 0x41,
	0x4d, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x54, 0x43, 0x48, 0x10, 0x06, 0x12, 0x10,
	0x0a, 0x0c, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x07,
	0x12, 0x14, 0x0a, 0x10, 0x46, 0x55, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x09, 0x22, 0x45, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x4c, 0x4f, 0x57, 0x10,
	0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x55, 0x4c, 0x4c, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x05, 0x22, 0x50, 0x0a,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a,
	0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x49, 0x46, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x48,
	0x54, 0x4d, 0x4c, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x05,
	0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x06, 0x22,
	0x70, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x44, 0x4f, 0x57, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x49, 0x41, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x07, 0x22, 0x33, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x08, 0x0a, 0x04, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x6f, 0x61, 0x64, 0x5f,
	0x4d, 0x6f, 0x72, 0x65, 0x10, 0x02, 0x22, 0xa9, 0x21, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x97, 0x1f, 0x0a, 0x02, 0x41, 0x64, 0x12,
	0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x64, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65,
	0x61, 0x6c, 0x69, 0x64, 0x1a, 0x52, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x57, 0x6f,
	0x72, 0x64, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x9a, 0x13, 0x0a, 0x0c, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x52, 0x0a, 0x0d, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5b, 0x0a,
	0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x73, 0x6a, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6e, 0x4e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x55, 0x72,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74,
	0x12, 0x49, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74,
	0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x3c, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x50, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x69, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x04, 0x73, 0x6b, 0x61, 0x6e, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x41, 0x64, 0x2e, 0x53, 0x6b, 0x61, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x04, 0x73, 0x6b, 0x61, 0x6e, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x41,
	0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x24, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x61,
	0x64, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x69,
	0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x75,
	0x73, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x61, 0x73, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x61, 0x73, 0x79, 0x50, 0x6c, 0x61, 0x79,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x61,
	0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x71, 0x75, 0x69, 0x63,
	0x6b, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x41, 0x70, 0x70, 0x55, 0x72, 0x6c, 0x1a, 0x47, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x32, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0xe0, 0x01, 0x0a, 0x05, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x61, 0x0a, 0x0c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10,
	0x02, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x49, 0x46, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54,
	0x4d, 0x4c, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x05, 0x12,
	0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x0f,
	0x0a, 0x0b, 0x4e, 0x4f, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x07, 0x22,
	0x70, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x44, 0x4f, 0x57, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x49, 0x41, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10,
	0x07, 0x22, 0x72, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x09,
	0x0a, 0x05, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x41, 0x52,
	0x47, 0x45, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x04, 0x12,
	0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x10, 0x05,
	0x12, 0x12, 0x0a, 0x0e, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x56, 0x45,
	0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x10, 0x12, 0x0b, 0x0a, 0x06, 0x53, 0x50, 0x4c, 0x41,
	0x53, 0x48, 0x10, 0x83, 0x01, 0x1a, 0xa4, 0x06, 0x0a, 0x0e, 0x53, 0x6b, 0x61, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x73, 0x0a, 0x18, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x73, 0x6a,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e,
	0x53, 0x6b, 0x61, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x2e, 0x53,
	0x6b, 0x61, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x16, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x58, 0x0a,
	0x0d, 0x61, 0x64, 0x5f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x53, 0x6b, 0x61, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x2e, 0x53, 0x6b, 0x61, 0x6e, 0x41, 0x64, 0x49,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x64, 0x49, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x93, 0x02, 0x0a, 0x16, 0x53, 0x6b, 0x61, 0x6e,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d,
	0x61, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x02, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x05, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x74, 0x75, 0x6e, 0x65,
	0x73, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x02, 0x28, 0x03, 0x52,
	0x0c, 0x69, 0x74, 0x75, 0x6e, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x02, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07,
	0x20, 0x02, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x1a, 0xac, 0x02,
	0x0a, 0x10, 0x53, 0x6b, 0x61, 0x6e, 0x41, 0x64, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d,
	0x61, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x64, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x02, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x64, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x64, 0x5f, 0x69, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x0e, 0x61, 0x64, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x64, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x02, 0x28, 0x03, 0x52, 0x0f, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x02, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20,
	0x02, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x1a, 0xb3, 0x03, 0x0a,
	0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x4c, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x73, 0x63, 0x55, 0x72, 0x6c, 0x1a, 0x62,
	0x0a, 0x0e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x42, 0x14, 0x5a, 0x12, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x70,
	0x62, 0x2f, 0x63, 0x73, 0x6a, 0x5f, 0x75, 0x70,
}

var (
	file_csj_1_5_5_proto_rawDescOnce sync.Once
	file_csj_1_5_5_proto_rawDescData = file_csj_1_5_5_proto_rawDesc
)

func file_csj_1_5_5_proto_rawDescGZIP() []byte {
	file_csj_1_5_5_proto_rawDescOnce.Do(func() {
		file_csj_1_5_5_proto_rawDescData = protoimpl.X.CompressGZIP(file_csj_1_5_5_proto_rawDescData)
	})
	return file_csj_1_5_5_proto_rawDescData
}

var file_csj_1_5_5_proto_enumTypes = make([]protoimpl.EnumInfo, 14)
var file_csj_1_5_5_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_csj_1_5_5_proto_goTypes = []interface{}{
	(BidRequest_Device_DeviceType)(0),                            // 0: csj.BidRequest.Device.DeviceType
	(BidRequest_Device_OsType)(0),                                // 1: csj.BidRequest.Device.OsType
	(BidRequest_Device_ConnectionType)(0),                        // 2: csj.BidRequest.Device.ConnectionType
	(BidRequest_Device_Orientation)(0),                           // 3: csj.BidRequest.Device.Orientation
	(BidRequest_Device_CarrierType)(0),                           // 4: csj.BidRequest.Device.CarrierType
	(BidRequest_User_Gender)(0),                                  // 5: csj.BidRequest.User.Gender
	(BidRequest_AdSlot_AdType)(0),                                // 6: csj.BidRequest.AdSlot.AdType
	(BidRequest_AdSlot_Position)(0),                              // 7: csj.BidRequest.AdSlot.Position
	(BidRequest_AdSlot_CreativeType)(0),                          // 8: csj.BidRequest.AdSlot.CreativeType
	(BidRequest_AdSlot_InteractionType)(0),                       // 9: csj.BidRequest.AdSlot.InteractionType
	(BidRequest_AdSlot_RefreshType)(0),                           // 10: csj.BidRequest.AdSlot.RefreshType
	(BidResponse_Ad_MaterialMeta_CreativeType)(0),                // 11: csj.BidResponse.Ad.MaterialMeta.CreativeType
	(BidResponse_Ad_MaterialMeta_InteractionType)(0),             // 12: csj.BidResponse.Ad.MaterialMeta.InteractionType
	(BidResponse_Ad_MaterialMeta_ImageMode)(0),                   // 13: csj.BidResponse.Ad.MaterialMeta.ImageMode
	(*BidRequest)(nil),                                           // 14: csj.BidRequest
	(*BidResponse)(nil),                                          // 15: csj.BidResponse
	(*BidRequest_App)(nil),                                       // 16: csj.BidRequest.App
	(*BidRequest_Wap)(nil),                                       // 17: csj.BidRequest.Wap
	(*BidRequest_Device)(nil),                                    // 18: csj.BidRequest.Device
	(*BidRequest_Geo)(nil),                                       // 19: csj.BidRequest.Geo
	(*BidRequest_User)(nil),                                      // 20: csj.BidRequest.User
	(*BidRequest_AdSlot)(nil),                                    // 21: csj.BidRequest.AdSlot
	(*BidRequest_User_Data)(nil),                                 // 22: csj.BidRequest.User.Data
	(*BidRequest_AdSlot_Pmp)(nil),                                // 23: csj.BidRequest.AdSlot.Pmp
	(*BidRequest_AdSlot_Size)(nil),                               // 24: csj.BidRequest.AdSlot.Size
	(*BidResponse_Ad)(nil),                                       // 25: csj.BidResponse.Ad
	(*BidResponse_Ad_FilterWords)(nil),                           // 26: csj.BidResponse.Ad.FilterWords
	(*BidResponse_Ad_MaterialMeta)(nil),                          // 27: csj.BidResponse.Ad.MaterialMeta
	(*BidResponse_Ad_SkanParameters)(nil),                        // 28: csj.BidResponse.Ad.SkanParameters
	(*BidResponse_Ad_AppInfo)(nil),                               // 29: csj.BidResponse.Ad.AppInfo
	(*BidResponse_Ad_MaterialMeta_Image)(nil),                    // 30: csj.BidResponse.Ad.MaterialMeta.Image
	(*BidResponse_Ad_MaterialMeta_Tracking)(nil),                 // 31: csj.BidResponse.Ad.MaterialMeta.Tracking
	(*BidResponse_Ad_MaterialMeta_Video)(nil),                    // 32: csj.BidResponse.Ad.MaterialMeta.Video
	(*BidResponse_Ad_SkanParameters_SkanStoreProductParams)(nil), // 33: csj.BidResponse.Ad.SkanParameters.SkanStoreProductParams
	(*BidResponse_Ad_SkanParameters_SkanAdImpression)(nil),       // 34: csj.BidResponse.Ad.SkanParameters.SkanAdImpression
	(*BidResponse_Ad_AppInfo_PermissionItem)(nil),                // 35: csj.BidResponse.Ad.AppInfo.PermissionItem
}
var file_csj_1_5_5_proto_depIdxs = []int32{
	20, // 0: csj.BidRequest.user:type_name -> csj.BidRequest.User
	17, // 1: csj.BidRequest.wap:type_name -> csj.BidRequest.Wap
	16, // 2: csj.BidRequest.app:type_name -> csj.BidRequest.App
	18, // 3: csj.BidRequest.device:type_name -> csj.BidRequest.Device
	21, // 4: csj.BidRequest.adslots:type_name -> csj.BidRequest.AdSlot
	25, // 5: csj.BidResponse.ads:type_name -> csj.BidResponse.Ad
	19, // 6: csj.BidRequest.App.geo:type_name -> csj.BidRequest.Geo
	19, // 7: csj.BidRequest.Wap.geo:type_name -> csj.BidRequest.Geo
	0,  // 8: csj.BidRequest.Device.type:type_name -> csj.BidRequest.Device.DeviceType
	1,  // 9: csj.BidRequest.Device.os:type_name -> csj.BidRequest.Device.OsType
	2,  // 10: csj.BidRequest.Device.conn_type:type_name -> csj.BidRequest.Device.ConnectionType
	3,  // 11: csj.BidRequest.Device.orientation:type_name -> csj.BidRequest.Device.Orientation
	4,  // 12: csj.BidRequest.Device.carrier:type_name -> csj.BidRequest.Device.CarrierType
	5,  // 13: csj.BidRequest.User.gender:type_name -> csj.BidRequest.User.Gender
	22, // 14: csj.BidRequest.User.data:type_name -> csj.BidRequest.User.Data
	6,  // 15: csj.BidRequest.AdSlot.adtype:type_name -> csj.BidRequest.AdSlot.AdType
	7,  // 16: csj.BidRequest.AdSlot.pos:type_name -> csj.BidRequest.AdSlot.Position
	24, // 17: csj.BidRequest.AdSlot.accepted_size:type_name -> csj.BidRequest.AdSlot.Size
	8,  // 18: csj.BidRequest.AdSlot.accepted_creative_types:type_name -> csj.BidRequest.AdSlot.CreativeType
	9,  // 19: csj.BidRequest.AdSlot.accepted_interaction_type:type_name -> csj.BidRequest.AdSlot.InteractionType
	10, // 20: csj.BidRequest.AdSlot.refresh_type:type_name -> csj.BidRequest.AdSlot.RefreshType
	23, // 21: csj.BidRequest.AdSlot.pmp:type_name -> csj.BidRequest.AdSlot.Pmp
	27, // 22: csj.BidResponse.Ad.creative:type_name -> csj.BidResponse.Ad.MaterialMeta
	26, // 23: csj.BidResponse.Ad.filter_words:type_name -> csj.BidResponse.Ad.FilterWords
	11, // 24: csj.BidResponse.Ad.MaterialMeta.creative_type:type_name -> csj.BidResponse.Ad.MaterialMeta.CreativeType
	12, // 25: csj.BidResponse.Ad.MaterialMeta.interaction_type:type_name -> csj.BidResponse.Ad.MaterialMeta.InteractionType
	30, // 26: csj.BidResponse.Ad.MaterialMeta.image:type_name -> csj.BidResponse.Ad.MaterialMeta.Image
	13, // 27: csj.BidResponse.Ad.MaterialMeta.image_mode:type_name -> csj.BidResponse.Ad.MaterialMeta.ImageMode
	30, // 28: csj.BidResponse.Ad.MaterialMeta.image_list:type_name -> csj.BidResponse.Ad.MaterialMeta.Image
	32, // 29: csj.BidResponse.Ad.MaterialMeta.video:type_name -> csj.BidResponse.Ad.MaterialMeta.Video
	31, // 30: csj.BidResponse.Ad.MaterialMeta.tracking_event:type_name -> csj.BidResponse.Ad.MaterialMeta.Tracking
	28, // 31: csj.BidResponse.Ad.MaterialMeta.skan:type_name -> csj.BidResponse.Ad.SkanParameters
	29, // 32: csj.BidResponse.Ad.MaterialMeta.app_info:type_name -> csj.BidResponse.Ad.AppInfo
	33, // 33: csj.BidResponse.Ad.SkanParameters.store_product_parameters:type_name -> csj.BidResponse.Ad.SkanParameters.SkanStoreProductParams
	34, // 34: csj.BidResponse.Ad.SkanParameters.ad_impression:type_name -> csj.BidResponse.Ad.SkanParameters.SkanAdImpression
	35, // 35: csj.BidResponse.Ad.AppInfo.permissions:type_name -> csj.BidResponse.Ad.AppInfo.PermissionItem
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_csj_1_5_5_proto_init() }
func file_csj_1_5_5_proto_init() {
	if File_csj_1_5_5_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_csj_1_5_5_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Wap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_FilterWords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_SkanParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta_Tracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_SkanParameters_SkanStoreProductParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_SkanParameters_SkanAdImpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_1_5_5_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_AppInfo_PermissionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_csj_1_5_5_proto_rawDesc,
			NumEnums:      14,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_csj_1_5_5_proto_goTypes,
		DependencyIndexes: file_csj_1_5_5_proto_depIdxs,
		EnumInfos:         file_csj_1_5_5_proto_enumTypes,
		MessageInfos:      file_csj_1_5_5_proto_msgTypes,
	}.Build()
	File_csj_1_5_5_proto = out.File
	file_csj_1_5_5_proto_rawDesc = nil
	file_csj_1_5_5_proto_goTypes = nil
	file_csj_1_5_5_proto_depIdxs = nil
}
