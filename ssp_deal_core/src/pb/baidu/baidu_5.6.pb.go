// 本文件描述API接口版本：5.6.0

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.17.3
// source: baidu.proto

package baidu

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 设备类型
type Device_DeviceType int32

const (
	Device_PHONE          Device_DeviceType = 1 // 手机，含iTouch
	Device_TABLET         Device_DeviceType = 2 // 平板
	Device_SMART_TV       Device_DeviceType = 3 // 智能电视
	Device_OUTDOOR_SCREEN Device_DeviceType = 4 // 户外屏幕
)

// Enum value maps for Device_DeviceType.
var (
	Device_DeviceType_name = map[int32]string{
		1: "PHONE",
		2: "TABLET",
		3: "SMART_TV",
		4: "OUTDOOR_SCREEN",
	}
	Device_DeviceType_value = map[string]int32{
		"PHONE":          1,
		"TABLET":         2,
		"SMART_TV":       3,
		"OUTDOOR_SCREEN": 4,
	}
)

func (x Device_DeviceType) Enum() *Device_DeviceType {
	p := new(Device_DeviceType)
	*p = x
	return p
}

func (x Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[0].Descriptor()
}

func (Device_DeviceType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[0]
}

func (x Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Device_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Device_DeviceType(num)
	return nil
}

// Deprecated: Use Device_DeviceType.Descriptor instead.
func (Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{4, 0}
}

// 操作系统类型
type Device_OsType int32

const (
	Device_ANDROID Device_OsType = 1 // Android
	Device_IOS     Device_OsType = 2 // iOS
)

// Enum value maps for Device_OsType.
var (
	Device_OsType_name = map[int32]string{
		1: "ANDROID",
		2: "IOS",
	}
	Device_OsType_value = map[string]int32{
		"ANDROID": 1,
		"IOS":     2,
	}
)

func (x Device_OsType) Enum() *Device_OsType {
	p := new(Device_OsType)
	*p = x
	return p
}

func (x Device_OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device_OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[1].Descriptor()
}

func (Device_OsType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[1]
}

func (x Device_OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Device_OsType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Device_OsType(num)
	return nil
}

// Deprecated: Use Device_OsType.Descriptor instead.
func (Device_OsType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{4, 1}
}

// 网络连接类型
type Network_ConnectionType int32

const (
	Network_CONNECTION_UNKNOWN Network_ConnectionType = 0   // 无法探测当前网络状态
	Network_CELL_UNKNOWN       Network_ConnectionType = 1   // 蜂窝数据接入，未知网络类型
	Network_CELL_2G            Network_ConnectionType = 2   // 蜂窝数据2G网络
	Network_CELL_3G            Network_ConnectionType = 3   // 蜂窝数据3G网络
	Network_CELL_4G            Network_ConnectionType = 4   // 蜂窝数据4G网络
	Network_CELL_5G            Network_ConnectionType = 5   // 蜂窝数据5G网络
	Network_WIFI               Network_ConnectionType = 100 // Wi-Fi网络接入
	Network_ETHERNET           Network_ConnectionType = 101 // 以太网接入
	Network_NEW_TYPE           Network_ConnectionType = 999 // 未知新类型
)

// Enum value maps for Network_ConnectionType.
var (
	Network_ConnectionType_name = map[int32]string{
		0:   "CONNECTION_UNKNOWN",
		1:   "CELL_UNKNOWN",
		2:   "CELL_2G",
		3:   "CELL_3G",
		4:   "CELL_4G",
		5:   "CELL_5G",
		100: "WIFI",
		101: "ETHERNET",
		999: "NEW_TYPE",
	}
	Network_ConnectionType_value = map[string]int32{
		"CONNECTION_UNKNOWN": 0,
		"CELL_UNKNOWN":       1,
		"CELL_2G":            2,
		"CELL_3G":            3,
		"CELL_4G":            4,
		"CELL_5G":            5,
		"WIFI":               100,
		"ETHERNET":           101,
		"NEW_TYPE":           999,
	}
)

func (x Network_ConnectionType) Enum() *Network_ConnectionType {
	p := new(Network_ConnectionType)
	*p = x
	return p
}

func (x Network_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Network_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[2].Descriptor()
}

func (Network_ConnectionType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[2]
}

func (x Network_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Network_ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Network_ConnectionType(num)
	return nil
}

// Deprecated: Use Network_ConnectionType.Descriptor instead.
func (Network_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{6, 0}
}

// 移动运营商类型
type Network_OperatorType int32

const (
	Network_UNKNOWN_OPERATOR Network_OperatorType = 0  // 未知的运营商
	Network_CHINA_MOBILE     Network_OperatorType = 1  // 中国移动
	Network_CHINA_TELECOM    Network_OperatorType = 2  // 中国电信
	Network_CHINA_UNICOM     Network_OperatorType = 3  // 中国联通
	Network_OTHER_OPERATOR   Network_OperatorType = 99 // 其他运营商
)

// Enum value maps for Network_OperatorType.
var (
	Network_OperatorType_name = map[int32]string{
		0:  "UNKNOWN_OPERATOR",
		1:  "CHINA_MOBILE",
		2:  "CHINA_TELECOM",
		3:  "CHINA_UNICOM",
		99: "OTHER_OPERATOR",
	}
	Network_OperatorType_value = map[string]int32{
		"UNKNOWN_OPERATOR": 0,
		"CHINA_MOBILE":     1,
		"CHINA_TELECOM":    2,
		"CHINA_UNICOM":     3,
		"OTHER_OPERATOR":   99,
	}
)

func (x Network_OperatorType) Enum() *Network_OperatorType {
	p := new(Network_OperatorType)
	*p = x
	return p
}

func (x Network_OperatorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Network_OperatorType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[3].Descriptor()
}

func (Network_OperatorType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[3]
}

func (x Network_OperatorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Network_OperatorType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Network_OperatorType(num)
	return nil
}

// Deprecated: Use Network_OperatorType.Descriptor instead.
func (Network_OperatorType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{6, 1}
}

// GPS坐标类型
type Gps_CoordinateType int32

const (
	Gps_WGS84 Gps_CoordinateType = 1 // 全球卫星定位系统坐标系
	Gps_GCJ02 Gps_CoordinateType = 2 // 国家测绘局坐标系
	Gps_BD09  Gps_CoordinateType = 3 // 百度坐标系
)

// Enum value maps for Gps_CoordinateType.
var (
	Gps_CoordinateType_name = map[int32]string{
		1: "WGS84",
		2: "GCJ02",
		3: "BD09",
	}
	Gps_CoordinateType_value = map[string]int32{
		"WGS84": 1,
		"GCJ02": 2,
		"BD09":  3,
	}
)

func (x Gps_CoordinateType) Enum() *Gps_CoordinateType {
	p := new(Gps_CoordinateType)
	*p = x
	return p
}

func (x Gps_CoordinateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gps_CoordinateType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[4].Descriptor()
}

func (Gps_CoordinateType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[4]
}

func (x Gps_CoordinateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Gps_CoordinateType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Gps_CoordinateType(num)
	return nil
}

// Deprecated: Use Gps_CoordinateType.Descriptor instead.
func (Gps_CoordinateType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{7, 0}
}

// 广告展示过程事件类型
type Tracking_TrackingEvent int32

const (
	// 广告展示过程共性事件
	Tracking_AD_CLICK    Tracking_TrackingEvent = 0 // 广告被点击
	Tracking_AD_EXPOSURE Tracking_TrackingEvent = 1 // 广告被展现
	Tracking_AD_CLOSE    Tracking_TrackingEvent = 2 // 广告被关闭
	Tracking_SKIP        Tracking_TrackingEvent = 3 // 广告跳过
	Tracking_CLICK       Tracking_TrackingEvent = 4 // 广告点击
	// 视频类广告展示过程事件
	Tracking_VIDEO_AD_START            Tracking_TrackingEvent = 101000 // 视频开始播放
	Tracking_VIDEO_AD_FULL_SCREEN      Tracking_TrackingEvent = 101001 // 视频全屏
	Tracking_VIDEO_AD_END              Tracking_TrackingEvent = 101002 // 视频正常播放结束
	Tracking_VIDEO_AD_START_CARD_CLICK Tracking_TrackingEvent = 101003 // 点击预览图播放视频
	// 下载类广告推广APP相关事件
	Tracking_APP_AD_DOWNLOAD_PAGE     Tracking_TrackingEvent = 102000 // 下载详情页
	Tracking_APP_AD_DOWNLOAD          Tracking_TrackingEvent = 102001 // 立即下载
	Tracking_APP_AD_DOWNLOAD_BEGIN    Tracking_TrackingEvent = 102002 // 开始下载
	Tracking_APP_AD_DOWNLOAD_PAUSE    Tracking_TrackingEvent = 102003 // 暂停下载
	Tracking_APP_AD_DOWNLOAD_CONTINUE Tracking_TrackingEvent = 102004 // 继续下载
	Tracking_APP_AD_DOWNLOAD_FINISH   Tracking_TrackingEvent = 102005 // 完成下载
	Tracking_APP_AD_INSTALL           Tracking_TrackingEvent = 102006 // 立即安装
	Tracking_APP_AD_INSTALL_BEGIN     Tracking_TrackingEvent = 102007 // 开始安装
	Tracking_APP_AD_INSTALL_PAUSE     Tracking_TrackingEvent = 102008 // 暂停安装
	Tracking_APP_AD_INSTALL_CONTINUE  Tracking_TrackingEvent = 102009 // 继续安装
	Tracking_APP_AD_INSTALL_FINISH    Tracking_TrackingEvent = 102010 // 安装完成
	Tracking_APP_AD_INSTALL_OPEN      Tracking_TrackingEvent = 102011 // 安装后打开
	Tracking_APP_AD_ACTIVE            Tracking_TrackingEvent = 102012 // 激活推广APP
)

// Enum value maps for Tracking_TrackingEvent.
var (
	Tracking_TrackingEvent_name = map[int32]string{
		0:      "AD_CLICK",
		1:      "AD_EXPOSURE",
		2:      "AD_CLOSE",
		3:      "SKIP",
		4:      "CLICK",
		101000: "VIDEO_AD_START",
		101001: "VIDEO_AD_FULL_SCREEN",
		101002: "VIDEO_AD_END",
		101003: "VIDEO_AD_START_CARD_CLICK",
		102000: "APP_AD_DOWNLOAD_PAGE",
		102001: "APP_AD_DOWNLOAD",
		102002: "APP_AD_DOWNLOAD_BEGIN",
		102003: "APP_AD_DOWNLOAD_PAUSE",
		102004: "APP_AD_DOWNLOAD_CONTINUE",
		102005: "APP_AD_DOWNLOAD_FINISH",
		102006: "APP_AD_INSTALL",
		102007: "APP_AD_INSTALL_BEGIN",
		102008: "APP_AD_INSTALL_PAUSE",
		102009: "APP_AD_INSTALL_CONTINUE",
		102010: "APP_AD_INSTALL_FINISH",
		102011: "APP_AD_INSTALL_OPEN",
		102012: "APP_AD_ACTIVE",
	}
	Tracking_TrackingEvent_value = map[string]int32{
		"AD_CLICK":                  0,
		"AD_EXPOSURE":               1,
		"AD_CLOSE":                  2,
		"SKIP":                      3,
		"CLICK":                     4,
		"VIDEO_AD_START":            101000,
		"VIDEO_AD_FULL_SCREEN":      101001,
		"VIDEO_AD_END":              101002,
		"VIDEO_AD_START_CARD_CLICK": 101003,
		"APP_AD_DOWNLOAD_PAGE":      102000,
		"APP_AD_DOWNLOAD":           102001,
		"APP_AD_DOWNLOAD_BEGIN":     102002,
		"APP_AD_DOWNLOAD_PAUSE":     102003,
		"APP_AD_DOWNLOAD_CONTINUE":  102004,
		"APP_AD_DOWNLOAD_FINISH":    102005,
		"APP_AD_INSTALL":            102006,
		"APP_AD_INSTALL_BEGIN":      102007,
		"APP_AD_INSTALL_PAUSE":      102008,
		"APP_AD_INSTALL_CONTINUE":   102009,
		"APP_AD_INSTALL_FINISH":     102010,
		"APP_AD_INSTALL_OPEN":       102011,
		"APP_AD_ACTIVE":             102012,
	}
)

func (x Tracking_TrackingEvent) Enum() *Tracking_TrackingEvent {
	p := new(Tracking_TrackingEvent)
	*p = x
	return p
}

func (x Tracking_TrackingEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Tracking_TrackingEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[5].Descriptor()
}

func (Tracking_TrackingEvent) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[5]
}

func (x Tracking_TrackingEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Tracking_TrackingEvent) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Tracking_TrackingEvent(num)
	return nil
}

// Deprecated: Use Tracking_TrackingEvent.Descriptor instead.
func (Tracking_TrackingEvent) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{8, 0}
}

type Video_CopyRight int32

const (
	Video_CR_NONE  Video_CopyRight = 0
	Video_CR_EXIST Video_CopyRight = 1
	Video_CR_UGC   Video_CopyRight = 2
	Video_CR_OTHER Video_CopyRight = 3
)

// Enum value maps for Video_CopyRight.
var (
	Video_CopyRight_name = map[int32]string{
		0: "CR_NONE",
		1: "CR_EXIST",
		2: "CR_UGC",
		3: "CR_OTHER",
	}
	Video_CopyRight_value = map[string]int32{
		"CR_NONE":  0,
		"CR_EXIST": 1,
		"CR_UGC":   2,
		"CR_OTHER": 3,
	}
)

func (x Video_CopyRight) Enum() *Video_CopyRight {
	p := new(Video_CopyRight)
	*p = x
	return p
}

func (x Video_CopyRight) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Video_CopyRight) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[6].Descriptor()
}

func (Video_CopyRight) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[6]
}

func (x Video_CopyRight) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Video_CopyRight) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Video_CopyRight(num)
	return nil
}

// Deprecated: Use Video_CopyRight.Descriptor instead.
func (Video_CopyRight) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{9, 0}
}

type Video_MaterialFormat int32

const (
	Video_VIDEO_TCL_MP4 Video_MaterialFormat = 11 // tcl mp4[tcl专用]
	Video_VIDEO_TCL_TS  Video_MaterialFormat = 12 // tcl ts ini包[tcl专用]
	Video_VIDEO_TS      Video_MaterialFormat = 13 // common video ts format
)

// Enum value maps for Video_MaterialFormat.
var (
	Video_MaterialFormat_name = map[int32]string{
		11: "VIDEO_TCL_MP4",
		12: "VIDEO_TCL_TS",
		13: "VIDEO_TS",
	}
	Video_MaterialFormat_value = map[string]int32{
		"VIDEO_TCL_MP4": 11,
		"VIDEO_TCL_TS":  12,
		"VIDEO_TS":      13,
	}
)

func (x Video_MaterialFormat) Enum() *Video_MaterialFormat {
	p := new(Video_MaterialFormat)
	*p = x
	return p
}

func (x Video_MaterialFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Video_MaterialFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[7].Descriptor()
}

func (Video_MaterialFormat) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[7]
}

func (x Video_MaterialFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Video_MaterialFormat) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Video_MaterialFormat(num)
	return nil
}

// Deprecated: Use Video_MaterialFormat.Descriptor instead.
func (Video_MaterialFormat) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{9, 1}
}

// 请求协议类型
type MobadsRequest_RequestProtocolType int32

const (
	MobadsRequest_UNKNOWN_PROTOCOL_TYPE MobadsRequest_RequestProtocolType = 0 // 未知协议
	MobadsRequest_HTTP_PROTOCOL_TYPE    MobadsRequest_RequestProtocolType = 1 // http协议
	MobadsRequest_HTTPS_PROTOCOL_TYPE   MobadsRequest_RequestProtocolType = 2 // https协议
)

// Enum value maps for MobadsRequest_RequestProtocolType.
var (
	MobadsRequest_RequestProtocolType_name = map[int32]string{
		0: "UNKNOWN_PROTOCOL_TYPE",
		1: "HTTP_PROTOCOL_TYPE",
		2: "HTTPS_PROTOCOL_TYPE",
	}
	MobadsRequest_RequestProtocolType_value = map[string]int32{
		"UNKNOWN_PROTOCOL_TYPE": 0,
		"HTTP_PROTOCOL_TYPE":    1,
		"HTTPS_PROTOCOL_TYPE":   2,
	}
)

func (x MobadsRequest_RequestProtocolType) Enum() *MobadsRequest_RequestProtocolType {
	p := new(MobadsRequest_RequestProtocolType)
	*p = x
	return p
}

func (x MobadsRequest_RequestProtocolType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MobadsRequest_RequestProtocolType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[8].Descriptor()
}

func (MobadsRequest_RequestProtocolType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[8]
}

func (x MobadsRequest_RequestProtocolType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MobadsRequest_RequestProtocolType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MobadsRequest_RequestProtocolType(num)
	return nil
}

// Deprecated: Use MobadsRequest_RequestProtocolType.Descriptor instead.
func (MobadsRequest_RequestProtocolType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{13, 0}
}

// 媒体支持的能力
type MobadsRequest_MediaSupportAbility int32

const (
	MobadsRequest_ABILITY_EMPTY              MobadsRequest_MediaSupportAbility = 0 // 空
	MobadsRequest_ABILITY_APP_STORE_DOWNLOAD MobadsRequest_MediaSupportAbility = 1 // 直投下载
)

// Enum value maps for MobadsRequest_MediaSupportAbility.
var (
	MobadsRequest_MediaSupportAbility_name = map[int32]string{
		0: "ABILITY_EMPTY",
		1: "ABILITY_APP_STORE_DOWNLOAD",
	}
	MobadsRequest_MediaSupportAbility_value = map[string]int32{
		"ABILITY_EMPTY":              0,
		"ABILITY_APP_STORE_DOWNLOAD": 1,
	}
)

func (x MobadsRequest_MediaSupportAbility) Enum() *MobadsRequest_MediaSupportAbility {
	p := new(MobadsRequest_MediaSupportAbility)
	*p = x
	return p
}

func (x MobadsRequest_MediaSupportAbility) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MobadsRequest_MediaSupportAbility) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[9].Descriptor()
}

func (MobadsRequest_MediaSupportAbility) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[9]
}

func (x MobadsRequest_MediaSupportAbility) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MobadsRequest_MediaSupportAbility) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MobadsRequest_MediaSupportAbility(num)
	return nil
}

// Deprecated: Use MobadsRequest_MediaSupportAbility.Descriptor instead.
func (MobadsRequest_MediaSupportAbility) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{13, 1}
}

// 创意类型
type MaterialMeta_CreativeType int32

const (
	MaterialMeta_NO_TYPE    MaterialMeta_CreativeType = 0 // 无创意类型，主要针对原生自定义素材广告，不再制定返回广告的创意类型，根据广告位设置对返回字段进行读取即可
	MaterialMeta_TEXT       MaterialMeta_CreativeType = 1 // 纯文字广告，一般由title、description构成
	MaterialMeta_IMAGE      MaterialMeta_CreativeType = 2 // 纯图片广告，一般由单张image_src构成
	MaterialMeta_TEXT_ICON  MaterialMeta_CreativeType = 3 // 图文混合广告，一般由单张icon_src和title、description构成
	MaterialMeta_VIDEO      MaterialMeta_CreativeType = 4 // 视频广告，一般由视频URL和视频时长构成
	MaterialMeta_IMAGE_TEXT MaterialMeta_CreativeType = 5 // 图文混合广告，一般由image_src和title、description构成
	MaterialMeta_ICON_IMAGE MaterialMeta_CreativeType = 6 // 图标图片广告,一般由image_src和icon_src、title、description构成
)

// Enum value maps for MaterialMeta_CreativeType.
var (
	MaterialMeta_CreativeType_name = map[int32]string{
		0: "NO_TYPE",
		1: "TEXT",
		2: "IMAGE",
		3: "TEXT_ICON",
		4: "VIDEO",
		5: "IMAGE_TEXT",
		6: "ICON_IMAGE",
	}
	MaterialMeta_CreativeType_value = map[string]int32{
		"NO_TYPE":    0,
		"TEXT":       1,
		"IMAGE":      2,
		"TEXT_ICON":  3,
		"VIDEO":      4,
		"IMAGE_TEXT": 5,
		"ICON_IMAGE": 6,
	}
)

func (x MaterialMeta_CreativeType) Enum() *MaterialMeta_CreativeType {
	p := new(MaterialMeta_CreativeType)
	*p = x
	return p
}

func (x MaterialMeta_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaterialMeta_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[10].Descriptor()
}

func (MaterialMeta_CreativeType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[10]
}

func (x MaterialMeta_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MaterialMeta_CreativeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MaterialMeta_CreativeType(num)
	return nil
}

// Deprecated: Use MaterialMeta_CreativeType.Descriptor instead.
func (MaterialMeta_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{15, 0}
}

// 交互类型
type MaterialMeta_InteractionType int32

const (
	MaterialMeta_NO_INTERACTION MaterialMeta_InteractionType = 0 // 无动作，即广告广告点击后无需进行任何响应
	MaterialMeta_SURFING        MaterialMeta_InteractionType = 1 // 使用浏览器打开网页
	MaterialMeta_DOWNLOAD       MaterialMeta_InteractionType = 2 // 下载应用
	MaterialMeta_DEEPLINK       MaterialMeta_InteractionType = 3 // deeplink唤醒
)

// Enum value maps for MaterialMeta_InteractionType.
var (
	MaterialMeta_InteractionType_name = map[int32]string{
		0: "NO_INTERACTION",
		1: "SURFING",
		2: "DOWNLOAD",
		3: "DEEPLINK",
	}
	MaterialMeta_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 0,
		"SURFING":        1,
		"DOWNLOAD":       2,
		"DEEPLINK":       3,
	}
)

func (x MaterialMeta_InteractionType) Enum() *MaterialMeta_InteractionType {
	p := new(MaterialMeta_InteractionType)
	*p = x
	return p
}

func (x MaterialMeta_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaterialMeta_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[11].Descriptor()
}

func (MaterialMeta_InteractionType) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[11]
}

func (x MaterialMeta_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MaterialMeta_InteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MaterialMeta_InteractionType(num)
	return nil
}

// Deprecated: Use MaterialMeta_InteractionType.Descriptor instead.
func (MaterialMeta_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{15, 1}
}

type MaterialMeta_MaterialPattern int32

const (
	MaterialMeta_MATERIAL_PATTERN_FULL_SCREEN MaterialMeta_MaterialPattern = 1 // 全屏
	MaterialMeta_MATERIAL_PATTERN_HALF_SCREEN MaterialMeta_MaterialPattern = 2 // 半屏
)

// Enum value maps for MaterialMeta_MaterialPattern.
var (
	MaterialMeta_MaterialPattern_name = map[int32]string{
		1: "MATERIAL_PATTERN_FULL_SCREEN",
		2: "MATERIAL_PATTERN_HALF_SCREEN",
	}
	MaterialMeta_MaterialPattern_value = map[string]int32{
		"MATERIAL_PATTERN_FULL_SCREEN": 1,
		"MATERIAL_PATTERN_HALF_SCREEN": 2,
	}
)

func (x MaterialMeta_MaterialPattern) Enum() *MaterialMeta_MaterialPattern {
	p := new(MaterialMeta_MaterialPattern)
	*p = x
	return p
}

func (x MaterialMeta_MaterialPattern) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaterialMeta_MaterialPattern) Descriptor() protoreflect.EnumDescriptor {
	return file_baidu_proto_enumTypes[12].Descriptor()
}

func (MaterialMeta_MaterialPattern) Type() protoreflect.EnumType {
	return &file_baidu_proto_enumTypes[12]
}

func (x MaterialMeta_MaterialPattern) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MaterialMeta_MaterialPattern) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MaterialMeta_MaterialPattern(num)
	return nil
}

// Deprecated: Use MaterialMeta_MaterialPattern.Descriptor instead.
func (MaterialMeta_MaterialPattern) EnumDescriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{15, 2}
}

// 版本号信息
type Version struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Major *uint32 `protobuf:"varint,1,opt,name=major,def=0" json:"major,omitempty"` // 必填！
	Minor *uint32 `protobuf:"varint,2,opt,name=minor,def=0" json:"minor,omitempty"` // 选填！
	Micro *uint32 `protobuf:"varint,3,opt,name=micro,def=0" json:"micro,omitempty"` // 选填！
}

// Default values for Version fields.
const (
	Default_Version_Major = uint32(0)
	Default_Version_Minor = uint32(0)
	Default_Version_Micro = uint32(0)
)

func (x *Version) Reset() {
	*x = Version{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Version) ProtoMessage() {}

func (x *Version) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Version.ProtoReflect.Descriptor instead.
func (*Version) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{0}
}

func (x *Version) GetMajor() uint32 {
	if x != nil && x.Major != nil {
		return *x.Major
	}
	return Default_Version_Major
}

func (x *Version) GetMinor() uint32 {
	if x != nil && x.Minor != nil {
		return *x.Minor
	}
	return Default_Version_Minor
}

func (x *Version) GetMicro() uint32 {
	if x != nil && x.Micro != nil {
		return *x.Micro
	}
	return Default_Version_Micro
}

// 应用信息
type App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      *string  `protobuf:"bytes,1,opt,name=app_id,json=appId,def=" json:"app_id,omitempty"`           // 必填！应用ID，在Mobile SSP（以下简称MSSP）完成注册，并上传应用通过审核后，平台会提供应用ID
	ChannelId  *string  `protobuf:"bytes,2,opt,name=channel_id,json=channelId" json:"channel_id,omitempty"`    // 选填！发布渠道ID，渠道接入方必需填写
	AppVersion *Version `protobuf:"bytes,3,opt,name=app_version,json=appVersion" json:"app_version,omitempty"` // 必填！应用版本，将影响优选策略
	AppPackage *string  `protobuf:"bytes,4,opt,name=app_package,json=appPackage" json:"app_package,omitempty"` // 必填！应用包名，需要跟应用提交时一一对应，中文需要UTF-8编码
}

// Default values for App fields.
const (
	Default_App_AppId = string("")
)

func (x *App) Reset() {
	*x = App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*App) ProtoMessage() {}

func (x *App) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use App.ProtoReflect.Descriptor instead.
func (*App) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{1}
}

func (x *App) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return Default_App_AppId
}

func (x *App) GetChannelId() string {
	if x != nil && x.ChannelId != nil {
		return *x.ChannelId
	}
	return ""
}

func (x *App) GetAppVersion() *Version {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *App) GetAppPackage() string {
	if x != nil && x.AppPackage != nil {
		return *x.AppPackage
	}
	return ""
}

// 唯一用户标识，必需使用明文，必需按要求填写，具体填写指导详见接口说明文档
type UdId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idfa         *string `protobuf:"bytes,1,opt,name=idfa,def=" json:"idfa,omitempty"`                                     // iOS设备的IDFA，格式要求[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}
	Imei         *string `protobuf:"bytes,2,opt,name=imei,def=" json:"imei,omitempty"`                                     // Android手机设备的IMEI，格式要求[0-9a-fA-F]{14,15}
	Mac          *string `protobuf:"bytes,3,opt,name=mac,def=" json:"mac,omitempty"`                                       // Android非手机设备的WiFi网卡MAC地址，格式要求[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}
	ImeiMd5      *string `protobuf:"bytes,4,opt,name=imei_md5,json=imeiMd5,def=" json:"imei_md5,omitempty"`                // Android手机设备的IMEI，经过MD5加密，格式要求[0-9A-Za-z]{32}
	AndroidId    *string `protobuf:"bytes,5,opt,name=android_id,json=androidId,def=" json:"android_id,omitempty"`          //必填！Android手机设备系统ID，格式要求[0-9A-Za-z]{16}
	IdfaMd5      *string `protobuf:"bytes,8,opt,name=idfa_md5,json=idfaMd5,def=" json:"idfa_md5,omitempty"`                // iOS设备的IDFA，经过MD5加密，格式要求[0-9A-Za-z]{32}
	AndroididMd5 *string `protobuf:"bytes,9,opt,name=androidid_md5,json=androididMd5,def=" json:"androidid_md5,omitempty"` // Android手机设备系统ID，经过MD5加密，格式要求[0-9A-Za-z]{32}
	Passport     *string `protobuf:"bytes,10,opt,name=passport,def=" json:"passport,omitempty"`                            // 媒体登陆账号
	Oaid         *string `protobuf:"bytes,11,opt,name=oaid,def=" json:"oaid,omitempty"`                                    // oaid
	OaidMd5      *string `protobuf:"bytes,12,opt,name=oaid_md5,json=oaidMd5,def=" json:"oaid_md5,omitempty"`               // oaid_md5 经过MD5加密，格式要求[0-9A-Za-z]{32}
}

// Default values for UdId fields.
const (
	Default_UdId_Idfa         = string("")
	Default_UdId_Imei         = string("")
	Default_UdId_Mac          = string("")
	Default_UdId_ImeiMd5      = string("")
	Default_UdId_AndroidId    = string("")
	Default_UdId_IdfaMd5      = string("")
	Default_UdId_AndroididMd5 = string("")
	Default_UdId_Passport     = string("")
	Default_UdId_Oaid         = string("")
	Default_UdId_OaidMd5      = string("")
)

func (x *UdId) Reset() {
	*x = UdId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UdId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UdId) ProtoMessage() {}

func (x *UdId) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UdId.ProtoReflect.Descriptor instead.
func (*UdId) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{2}
}

func (x *UdId) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return Default_UdId_Idfa
}

func (x *UdId) GetImei() string {
	if x != nil && x.Imei != nil {
		return *x.Imei
	}
	return Default_UdId_Imei
}

func (x *UdId) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return Default_UdId_Mac
}

func (x *UdId) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return Default_UdId_ImeiMd5
}

func (x *UdId) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return Default_UdId_AndroidId
}

func (x *UdId) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return Default_UdId_IdfaMd5
}

func (x *UdId) GetAndroididMd5() string {
	if x != nil && x.AndroididMd5 != nil {
		return *x.AndroididMd5
	}
	return Default_UdId_AndroididMd5
}

func (x *UdId) GetPassport() string {
	if x != nil && x.Passport != nil {
		return *x.Passport
	}
	return Default_UdId_Passport
}

func (x *UdId) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return Default_UdId_Oaid
}

func (x *UdId) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return Default_UdId_OaidMd5
}

// 二维尺寸信息
type Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  *uint32 `protobuf:"varint,1,opt,name=width,def=0" json:"width,omitempty"`   // 必填！宽度
	Height *uint32 `protobuf:"varint,2,opt,name=height,def=0" json:"height,omitempty"` // 必填！高度
}

// Default values for Size fields.
const (
	Default_Size_Width  = uint32(0)
	Default_Size_Height = uint32(0)
)

func (x *Size) Reset() {
	*x = Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Size) ProtoMessage() {}

func (x *Size) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Size.ProtoReflect.Descriptor instead.
func (*Size) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{3}
}

func (x *Size) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return Default_Size_Width
}

func (x *Size) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return Default_Size_Height
}

// 设备信息
type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceType *Device_DeviceType `protobuf:"varint,1,opt,name=device_type,json=deviceType,enum=baidu.Device_DeviceType" json:"device_type,omitempty"` // 必填！设备类型
	OsType     *Device_OsType     `protobuf:"varint,2,opt,name=os_type,json=osType,enum=baidu.Device_OsType" json:"os_type,omitempty"`                 // 必填！操作系统类型
	OsVersion  *Version           `protobuf:"bytes,3,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`                                  // 必填！操作系统版本
	Vendor     []byte             `protobuf:"bytes,4,opt,name=vendor,def=" json:"vendor,omitempty"`                                                    // 必填！设备厂商名称，中文需要UTF-8编码
	Model      []byte             `protobuf:"bytes,5,opt,name=model,def=" json:"model,omitempty"`                                                      // 必填！设备型号，中文需要UTF-8编码
	Udid       *UdId              `protobuf:"bytes,6,opt,name=udid" json:"udid,omitempty"`                                                             // 必填！唯一设备标识，必需按要求填写
	ScreenSize *Size              `protobuf:"bytes,7,opt,name=screen_size,json=screenSize" json:"screen_size,omitempty"`                               // 必填！设备屏幕宽高
}

// Default values for Device fields.
var (
	Default_Device_Vendor = []byte("")
	Default_Device_Model  = []byte("")
)

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{4}
}

func (x *Device) GetDeviceType() Device_DeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return Device_PHONE
}

func (x *Device) GetOsType() Device_OsType {
	if x != nil && x.OsType != nil {
		return *x.OsType
	}
	return Device_ANDROID
}

func (x *Device) GetOsVersion() *Version {
	if x != nil {
		return x.OsVersion
	}
	return nil
}

func (x *Device) GetVendor() []byte {
	if x != nil && x.Vendor != nil {
		return x.Vendor
	}
	return append([]byte(nil), Default_Device_Vendor...)
}

func (x *Device) GetModel() []byte {
	if x != nil && x.Model != nil {
		return x.Model
	}
	return append([]byte(nil), Default_Device_Model...)
}

func (x *Device) GetUdid() *UdId {
	if x != nil {
		return x.Udid
	}
	return nil
}

func (x *Device) GetScreenSize() *Size {
	if x != nil {
		return x.ScreenSize
	}
	return nil
}

// WiFi热点信息
type WiFiAp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApMac       *string `protobuf:"bytes,1,opt,name=ap_mac,json=apMac" json:"ap_mac,omitempty"`                    // 必填！热点MAC地址，格式要求[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}:[0-9a-fA-F]{2}
	Rssi        *int32  `protobuf:"varint,2,opt,name=rssi" json:"rssi,omitempty"`                                  // 必填！热点信号强度，通常是负数
	ApName      []byte  `protobuf:"bytes,3,opt,name=ap_name,json=apName" json:"ap_name,omitempty"`                 // 必填！热点名称，可不传递，建议传递当前接入热点的名称，用于判断用户当前所处场所，中文需要UTF-8编码
	IsConnected *bool   `protobuf:"varint,4,opt,name=is_connected,json=isConnected" json:"is_connected,omitempty"` // 必填！是否是当前连接热点，配合热点名称可用于识别用户所处场所
}

func (x *WiFiAp) Reset() {
	*x = WiFiAp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WiFiAp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiFiAp) ProtoMessage() {}

func (x *WiFiAp) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiFiAp.ProtoReflect.Descriptor instead.
func (*WiFiAp) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{5}
}

func (x *WiFiAp) GetApMac() string {
	if x != nil && x.ApMac != nil {
		return *x.ApMac
	}
	return ""
}

func (x *WiFiAp) GetRssi() int32 {
	if x != nil && x.Rssi != nil {
		return *x.Rssi
	}
	return 0
}

func (x *WiFiAp) GetApName() []byte {
	if x != nil {
		return x.ApName
	}
	return nil
}

func (x *WiFiAp) GetIsConnected() bool {
	if x != nil && x.IsConnected != nil {
		return *x.IsConnected
	}
	return false
}

// 网络环境信息
type Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ipv4           *string                 `protobuf:"bytes,1,opt,name=ipv4" json:"ipv4,omitempty"`                                                                              // 必填！用户设备的公网IPv4地址，服务器对接必填，格式要求：***************
	ConnectionType *Network_ConnectionType `protobuf:"varint,2,opt,name=connection_type,json=connectionType,enum=baidu.Network_ConnectionType" json:"connection_type,omitempty"` // 必填！网络连接类型，用于判断网速
	OperatorType   *Network_OperatorType   `protobuf:"varint,3,opt,name=operator_type,json=operatorType,enum=baidu.Network_OperatorType" json:"operator_type,omitempty"`         // 必填！移动运营商类型，用于运营商定向广告
	CellularId     *string                 `protobuf:"bytes,4,opt,name=cellular_id,json=cellularId" json:"cellular_id,omitempty"`                                                // 选填！当前连接的运营商基站ID，用于快速用户定位
	WifiAps        []*WiFiAp               `protobuf:"bytes,5,rep,name=wifi_aps,json=wifiAps" json:"wifi_aps,omitempty"`                                                         // 选填！周边WiFi热点列表，用于精细用户定位
	Ipv6           *string                 `protobuf:"bytes,6,opt,name=ipv6" json:"ipv6,omitempty"`                                                                              // 选填! 用户设备的公网IPv6地址，服务器对接
}

func (x *Network) Reset() {
	*x = Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Network) ProtoMessage() {}

func (x *Network) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Network.ProtoReflect.Descriptor instead.
func (*Network) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{6}
}

func (x *Network) GetIpv4() string {
	if x != nil && x.Ipv4 != nil {
		return *x.Ipv4
	}
	return ""
}

func (x *Network) GetConnectionType() Network_ConnectionType {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return Network_CONNECTION_UNKNOWN
}

func (x *Network) GetOperatorType() Network_OperatorType {
	if x != nil && x.OperatorType != nil {
		return *x.OperatorType
	}
	return Network_UNKNOWN_OPERATOR
}

func (x *Network) GetCellularId() string {
	if x != nil && x.CellularId != nil {
		return *x.CellularId
	}
	return ""
}

func (x *Network) GetWifiAps() []*WiFiAp {
	if x != nil {
		return x.WifiAps
	}
	return nil
}

func (x *Network) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

// GPS信息
type Gps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoordinateType *Gps_CoordinateType `protobuf:"varint,1,opt,name=coordinate_type,json=coordinateType,enum=baidu.Gps_CoordinateType" json:"coordinate_type,omitempty"` // 必填！坐标类型
	Longitude      *float64            `protobuf:"fixed64,2,opt,name=longitude" json:"longitude,omitempty"`                                                              // 必填！经度
	Latitude       *float64            `protobuf:"fixed64,3,opt,name=latitude" json:"latitude,omitempty"`                                                                // 必填！纬度
	Timestamp      *uint32             `protobuf:"varint,4,opt,name=timestamp" json:"timestamp,omitempty"`                                                               // 必填！时间戳，单位秒
}

func (x *Gps) Reset() {
	*x = Gps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gps) ProtoMessage() {}

func (x *Gps) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gps.ProtoReflect.Descriptor instead.
func (*Gps) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{7}
}

func (x *Gps) GetCoordinateType() Gps_CoordinateType {
	if x != nil && x.CoordinateType != nil {
		return *x.CoordinateType
	}
	return Gps_WGS84
}

func (x *Gps) GetLongitude() float64 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

func (x *Gps) GetLatitude() float64 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *Gps) GetTimestamp() uint32 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

// 广告效果跟踪信息
type Tracking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingEvent *Tracking_TrackingEvent `protobuf:"varint,1,opt,name=tracking_event,json=trackingEvent,enum=baidu.Tracking_TrackingEvent" json:"tracking_event,omitempty"` // 被跟踪的广告展示过程事件
	TrackingUrl   []string                `protobuf:"bytes,2,rep,name=tracking_url,json=trackingUrl" json:"tracking_url,omitempty"`                                          // 事件监控URL
}

func (x *Tracking) Reset() {
	*x = Tracking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tracking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tracking) ProtoMessage() {}

func (x *Tracking) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tracking.ProtoReflect.Descriptor instead.
func (*Tracking) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{8}
}

func (x *Tracking) GetTrackingEvent() Tracking_TrackingEvent {
	if x != nil && x.TrackingEvent != nil {
		return *x.TrackingEvent
	}
	return Tracking_AD_CLICK
}

func (x *Tracking) GetTrackingUrl() []string {
	if x != nil {
		return x.TrackingUrl
	}
	return nil
}

type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          []byte                 `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`                                                                          // 视频标题，UTF-8编码
	ContentLength  *uint32                `protobuf:"varint,2,opt,name=content_length,json=contentLength" json:"content_length,omitempty"`                                    // 视频内容长度
	Copyright      *Video_CopyRight       `protobuf:"varint,3,opt,name=copyright,enum=baidu.Video_CopyRight" json:"copyright,omitempty"`                                      // 视频版权信息
	MaterialFormat []Video_MaterialFormat `protobuf:"varint,4,rep,name=material_format,json=materialFormat,enum=baidu.Video_MaterialFormat" json:"material_format,omitempty"` // 媒体指定的视频类型
}

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{9}
}

func (x *Video) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Video) GetContentLength() uint32 {
	if x != nil && x.ContentLength != nil {
		return *x.ContentLength
	}
	return 0
}

func (x *Video) GetCopyright() Video_CopyRight {
	if x != nil && x.Copyright != nil {
		return *x.Copyright
	}
	return Video_CR_NONE
}

func (x *Video) GetMaterialFormat() []Video_MaterialFormat {
	if x != nil {
		return x.MaterialFormat
	}
	return nil
}

type Page struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url             []byte   `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`                                                // 请求页面的URL
	Title           []byte   `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`                                            // 请求页面的内容标题，UTF-8编码
	SourceUrl       []byte   `protobuf:"bytes,3,opt,name=source_url,json=sourceUrl" json:"source_url,omitempty"`                   // 请求页面的内容可能来自另外的网址，该字段表示页面内容来源网址URL
	ContentId       *string  `protobuf:"bytes,4,opt,name=content_id,json=contentId" json:"content_id,omitempty"`                   // 请求页面的内容ID
	ContentCategory [][]byte `protobuf:"bytes,5,rep,name=content_category,json=contentCategory" json:"content_category,omitempty"` // 请求页面的内容分类，UTF-8编码
	ContentLabel    [][]byte `protobuf:"bytes,6,rep,name=content_label,json=contentLabel" json:"content_label,omitempty"`          // 请求页面的内容标签，UTF-8编码
	AuthorId        *string  `protobuf:"bytes,7,opt,name=author_id,json=authorId" json:"author_id,omitempty"`                      // 请求页面内容的作者ID
	QueryWord       []byte   `protobuf:"bytes,8,opt,name=query_word,json=queryWord" json:"query_word,omitempty"`                   // 请求页面搜索词
}

func (x *Page) Reset() {
	*x = Page{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Page) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Page) ProtoMessage() {}

func (x *Page) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Page.ProtoReflect.Descriptor instead.
func (*Page) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{10}
}

func (x *Page) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *Page) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Page) GetSourceUrl() []byte {
	if x != nil {
		return x.SourceUrl
	}
	return nil
}

func (x *Page) GetContentId() string {
	if x != nil && x.ContentId != nil {
		return *x.ContentId
	}
	return ""
}

func (x *Page) GetContentCategory() [][]byte {
	if x != nil {
		return x.ContentCategory
	}
	return nil
}

func (x *Page) GetContentLabel() [][]byte {
	if x != nil {
		return x.ContentLabel
	}
	return nil
}

func (x *Page) GetAuthorId() string {
	if x != nil && x.AuthorId != nil {
		return *x.AuthorId
	}
	return ""
}

func (x *Page) GetQueryWord() []byte {
	if x != nil {
		return x.QueryWord
	}
	return nil
}

// 用户信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender     *uint32           `protobuf:"varint,1,opt,name=gender" json:"gender,omitempty"`                          // 性别，0未知，1男性，2女性
	FeatureVec *UserInfo_Feature `protobuf:"bytes,3,opt,name=feature_vec,json=featureVec" json:"feature_vec,omitempty"` //特征向量
	// 用户隐私保护
	IsPrivacyProtected *bool `protobuf:"varint,4,opt,name=is_privacy_protected,json=isPrivacyProtected,def=0" json:"is_privacy_protected,omitempty"`
}

// Default values for UserInfo fields.
const (
	Default_UserInfo_IsPrivacyProtected = bool(false)
)

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{11}
}

func (x *UserInfo) GetGender() uint32 {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return 0
}

func (x *UserInfo) GetFeatureVec() *UserInfo_Feature {
	if x != nil {
		return x.FeatureVec
	}
	return nil
}

func (x *UserInfo) GetIsPrivacyProtected() bool {
	if x != nil && x.IsPrivacyProtected != nil {
		return *x.IsPrivacyProtected
	}
	return Default_UserInfo_IsPrivacyProtected
}

// 广告位信息
type AdSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdslotId   *string `protobuf:"bytes,1,opt,name=adslot_id,json=adslotId" json:"adslot_id,omitempty"`        // 必填！广告位ID，需要媒体在MSSP平台进行设置！非常重要！
	AdslotSize *Size   `protobuf:"bytes,2,opt,name=adslot_size,json=adslotSize" json:"adslot_size,omitempty"`  // 必填！广告位尺寸
	Video      *Video  `protobuf:"bytes,4,opt,name=video" json:"video,omitempty"`                              // 选填，但视频广告位必填！传递视频标题、时长、频道、版权等信息
	Ctkey      *string `protobuf:"bytes,5,opt,name=ctkey" json:"ctkey,omitempty"`                              // 选填,内容联盟流量分润ID,内容联盟流量必填
	SequenceId *uint32 `protobuf:"varint,6,opt,name=sequence_id,json=sequenceId" json:"sequence_id,omitempty"` // 选填,广告位在当前页面中的序列号,从1开始
	SpuChn     []byte  `protobuf:"bytes,7,opt,name=spu_chn,json=spuChn" json:"spu_chn,omitempty"`              // 选填，个人联盟渠道信息
}

func (x *AdSlot) Reset() {
	*x = AdSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdSlot) ProtoMessage() {}

func (x *AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdSlot.ProtoReflect.Descriptor instead.
func (*AdSlot) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{12}
}

func (x *AdSlot) GetAdslotId() string {
	if x != nil && x.AdslotId != nil {
		return *x.AdslotId
	}
	return ""
}

func (x *AdSlot) GetAdslotSize() *Size {
	if x != nil {
		return x.AdslotSize
	}
	return nil
}

func (x *AdSlot) GetVideo() *Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *AdSlot) GetCtkey() string {
	if x != nil && x.Ctkey != nil {
		return *x.Ctkey
	}
	return ""
}

func (x *AdSlot) GetSequenceId() uint32 {
	if x != nil && x.SequenceId != nil {
		return *x.SequenceId
	}
	return 0
}

func (x *AdSlot) GetSpuChn() []byte {
	if x != nil {
		return x.SpuChn
	}
	return nil
}

// Baidu Mobads API请求
type MobadsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId           *string                            `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id,omitempty"`                                                                                     // 必填！接入方自定义请求ID，[a-zA-Z0-9]{32}
	ApiVersion          *Version                           `protobuf:"bytes,2,opt,name=api_version,json=apiVersion" json:"api_version,omitempty"`                                                                                  // 必填！API版本，按照当前接入所参照的API文档版本赋值，影响所有后续逻辑，填写错误会导致拒绝请求。
	App                 *App                               `protobuf:"bytes,3,opt,name=app" json:"app,omitempty"`                                                                                                                  // 必填！应用信息
	Device              *Device                            `protobuf:"bytes,4,opt,name=device" json:"device,omitempty"`                                                                                                            // 必填！设备信息
	Network             *Network                           `protobuf:"bytes,5,opt,name=network" json:"network,omitempty"`                                                                                                          // 必填！网络环境信息
	Gps                 *Gps                               `protobuf:"bytes,6,opt,name=gps" json:"gps,omitempty"`                                                                                                                  // 选填！GPS定位信息，用于辅助触发LBS广告
	Adslot              *AdSlot                            `protobuf:"bytes,7,opt,name=adslot" json:"adslot,omitempty"`                                                                                                            // 必填！广告位信息
	IsDebug             *bool                              `protobuf:"varint,8,opt,name=is_debug,json=isDebug,def=0" json:"is_debug,omitempty"`                                                                                    // 选填！测试流量标记，可获取广告，但不被计费，勿用于线上请求
	RequestProtocolType *MobadsRequest_RequestProtocolType `protobuf:"varint,9,opt,name=request_protocol_type,json=requestProtocolType,enum=baidu.MobadsRequest_RequestProtocolType,def=1" json:"request_protocol_type,omitempty"` // 选填, https媒体必填！
	Page                *Page                              `protobuf:"bytes,10,opt,name=page" json:"page,omitempty"`                                                                                                               // 选填！请求页面信息
	Userinfo            *UserInfo                          `protobuf:"bytes,12,opt,name=userinfo" json:"userinfo,omitempty"`                                                                                                       // 选填 请求用户信息
	MediaSupportAbility *uint64                            `protobuf:"varint,13,opt,name=media_support_ability,json=mediaSupportAbility" json:"media_support_ability,omitempty"`                                                   // 选填 媒体支持的能力，赋值参考枚举MediaSupportAbility，同时支持多种时按位取或
}

// Default values for MobadsRequest fields.
const (
	Default_MobadsRequest_IsDebug             = bool(false)
	Default_MobadsRequest_RequestProtocolType = MobadsRequest_HTTP_PROTOCOL_TYPE
)

func (x *MobadsRequest) Reset() {
	*x = MobadsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobadsRequest) ProtoMessage() {}

func (x *MobadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobadsRequest.ProtoReflect.Descriptor instead.
func (*MobadsRequest) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{13}
}

func (x *MobadsRequest) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *MobadsRequest) GetApiVersion() *Version {
	if x != nil {
		return x.ApiVersion
	}
	return nil
}

func (x *MobadsRequest) GetApp() *App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *MobadsRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *MobadsRequest) GetNetwork() *Network {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *MobadsRequest) GetGps() *Gps {
	if x != nil {
		return x.Gps
	}
	return nil
}

func (x *MobadsRequest) GetAdslot() *AdSlot {
	if x != nil {
		return x.Adslot
	}
	return nil
}

func (x *MobadsRequest) GetIsDebug() bool {
	if x != nil && x.IsDebug != nil {
		return *x.IsDebug
	}
	return Default_MobadsRequest_IsDebug
}

func (x *MobadsRequest) GetRequestProtocolType() MobadsRequest_RequestProtocolType {
	if x != nil && x.RequestProtocolType != nil {
		return *x.RequestProtocolType
	}
	return Default_MobadsRequest_RequestProtocolType
}

func (x *MobadsRequest) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *MobadsRequest) GetUserinfo() *UserInfo {
	if x != nil {
		return x.Userinfo
	}
	return nil
}

func (x *MobadsRequest) GetMediaSupportAbility() uint64 {
	if x != nil && x.MediaSupportAbility != nil {
		return *x.MediaSupportAbility
	}
	return 0
}

// 广告元数据组索引结构
// 一条广告可能包含多个物料元信息,我们统称这些元信息为广告元数据组
// 返回广告时，total_num表明当前广告包含的物料元数据个数，current_index表明当前的物料元数据在元数据组中的索引
// 请求多个广告返回时，ad_key唯一标识一个广告元数据组(一个广告)，MetaIndex标识一个元数据组中的每个元数据信息
type MetaIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalNum     *uint32 `protobuf:"varint,1,opt,name=total_num,json=totalNum" json:"total_num,omitempty"`             // 每条广告对应元素组中元数据总数
	CurrentIndex *uint32 `protobuf:"varint,2,opt,name=current_index,json=currentIndex" json:"current_index,omitempty"` // 当前元数据所在索引
}

func (x *MetaIndex) Reset() {
	*x = MetaIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaIndex) ProtoMessage() {}

func (x *MetaIndex) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaIndex.ProtoReflect.Descriptor instead.
func (*MetaIndex) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{14}
}

func (x *MetaIndex) GetTotalNum() uint32 {
	if x != nil && x.TotalNum != nil {
		return *x.TotalNum
	}
	return 0
}

func (x *MetaIndex) GetCurrentIndex() uint32 {
	if x != nil && x.CurrentIndex != nil {
		return *x.CurrentIndex
	}
	return 0
}

// 广告物料元数据信息
type MaterialMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeType       *MaterialMeta_CreativeType    `protobuf:"varint,1,opt,name=creative_type,json=creativeType,enum=baidu.MaterialMeta_CreativeType" json:"creative_type,omitempty"`              // 创意类型
	InteractionType    *MaterialMeta_InteractionType `protobuf:"varint,2,opt,name=interaction_type,json=interactionType,enum=baidu.MaterialMeta_InteractionType" json:"interaction_type,omitempty"`  // 交互类型
	WinNoticeUrl       []string                      `protobuf:"bytes,3,rep,name=win_notice_url,json=winNoticeUrl" json:"win_notice_url,omitempty"`                                                  // 曝光日志URL列表，在曝光后必须在客户端逐个汇报完
	ClickUrl           *string                       `protobuf:"bytes,4,opt,name=click_url,json=clickUrl" json:"click_url,omitempty"`                                                                // 点击行为地址，用户点击后，在客户端进行响应，会经过多次302跳转最终到达目标地址
	Title              []byte                        `protobuf:"bytes,5,opt,name=title" json:"title,omitempty"`                                                                                      // 推广标题（即将废弃），中文需要UTF-8编码。下载类为app名（现请从brand_name取值），非下载类为广告标题（现请从ad_title取值）
	Description        [][]byte                      `protobuf:"bytes,6,rep,name=description" json:"description,omitempty"`                                                                          // 广告描述，默认只有一个元素，暂时约定最多只有两个元素，具体情况已线上广告实际返回结果为准，中文需要UTF-8编码
	IconSrc            []string                      `protobuf:"bytes,7,rep,name=icon_src,json=iconSrc" json:"icon_src,omitempty"`                                                                   // 广告图标地址，注意：单个广告可能有多张图标返回
	ImageSrc           []string                      `protobuf:"bytes,8,rep,name=image_src,json=imageSrc" json:"image_src,omitempty"`                                                                // 广告图片地址，注意：单个广告可能有多张图片返回
	AppPackage         *string                       `protobuf:"bytes,9,opt,name=app_package,json=appPackage" json:"app_package,omitempty"`                                                          // 下载类/deeplink广告应用包名
	AppSize            *uint32                       `protobuf:"varint,10,opt,name=app_size,json=appSize" json:"app_size,omitempty"`                                                                 // 下载类广告应用大小
	VideoUrl           *string                       `protobuf:"bytes,11,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`                                                               // 广告视频物料地址
	VideoDuration      *uint32                       `protobuf:"varint,12,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`                                               // 广告视频物料时长
	MetaIndex          *MetaIndex                    `protobuf:"bytes,13,opt,name=meta_index,json=metaIndex" json:"meta_index,omitempty"`                                                            // 当前元数据在一条广告元素组中的索引结构
	MaterialWidth      *uint32                       `protobuf:"varint,14,opt,name=material_width,json=materialWidth" json:"material_width,omitempty"`                                               // 物料的宽度:如果是图片,表示图片的宽度;如果是视频(含有视频截图),则为视频宽度;如果是图文或文本,则不会填充此字段
	MaterialHeight     *uint32                       `protobuf:"varint,15,opt,name=material_height,json=materialHeight" json:"material_height,omitempty"`                                            // 物料的高度:如果是图片,表示图片的高度;如果是视频(含有视频截图),则为视频高度;如果是图文或文本,则不会填充此字段
	BrandName          *string                       `protobuf:"bytes,16,opt,name=brand_name,json=brandName" json:"brand_name,omitempty"`                                                            // 广告品牌名称，下载类为app名，非下载类为推广的品牌名
	AdTitle            *string                       `protobuf:"bytes,17,opt,name=ad_title,json=adTitle" json:"ad_title,omitempty"`                                                                  // 广告推广标题
	MaterialSize       *uint32                       `protobuf:"varint,18,opt,name=material_size,json=materialSize" json:"material_size,omitempty"`                                                  // 图片、视频物料大小
	DeeplinkUrl        []byte                        `protobuf:"bytes,19,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`                                                      // deeplink唤醒打开页面
	FallbackType       *uint32                       `protobuf:"varint,20,opt,name=fallback_type,json=fallbackType" json:"fallback_type,omitempty"`                                                  // deeplink唤醒广告退化类型，1：浏览器打开页面，2：下载
	FallbackUrl        []byte                        `protobuf:"bytes,21,opt,name=fallback_url,json=fallbackUrl" json:"fallback_url,omitempty"`                                                      // deeplink唤醒广告退化链接
	ApkName            *string                       `protobuf:"bytes,22,opt,name=apk_name,json=apkName" json:"apk_name,omitempty"`                                                                  // app安装包的名字，如xxx.apk
	Rating             *float32                      `protobuf:"fixed32,23,opt,name=rating" json:"rating,omitempty"`                                                                                 // app评分数
	Comments           *uint32                       `protobuf:"varint,24,opt,name=comments" json:"comments,omitempty"`                                                                              // app评论数
	ImageSize          *MaterialMeta_ImageSize       `protobuf:"bytes,25,opt,name=image_size,json=imageSize" json:"image_size,omitempty"`                                                            // 图片物料尺寸,默认多个图片物料尺寸相同。目前仅激励视频，同时返回视频、图片物料时，此属性有效。其他情况统一采用material_width、material_height表示
	ButtonName         *string                       `protobuf:"bytes,26,opt,name=button_name,json=buttonName" json:"button_name,omitempty"`                                                         // 商品广告返回的button名称
	AppId              []byte                        `protobuf:"bytes,27,opt,name=app_id,json=appId" json:"app_id,omitempty"`                                                                        // 下载类广告，用于ios系统app内部唤醒appstore
	MaterialPattern    *MaterialMeta_MaterialPattern `protobuf:"varint,28,opt,name=material_pattern,json=materialPattern,enum=baidu.MaterialMeta_MaterialPattern" json:"material_pattern,omitempty"` // 物料规格 1：全屏 2：半屏，目前只有序章开屏使用
	SkipButtonPosition *float32                      `protobuf:"fixed32,29,opt,name=skip_button_position,json=skipButtonPosition" json:"skip_button_position,omitempty"`                             // 跳过按钮相对logo的位置（%），目前只有序章dsp投放开屏使用
	AppStoreLink       []byte                        `protobuf:"bytes,30,opt,name=app_store_link,json=appStoreLink" json:"app_store_link,omitempty"`                                                 // 直投下载，应用商店链接
	Publisher          *string                       `protobuf:"bytes,31,opt,name=publisher" json:"publisher,omitempty"`                                                                             // app开发者
	AppVersion         *string                       `protobuf:"bytes,32,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`                                                         // app版本信息
	PrivacyLink        []byte                        `protobuf:"bytes,33,opt,name=privacy_link,json=privacyLink" json:"privacy_link,omitempty"`                                                      // app隐私协议
	PermissionLink     []byte                        `protobuf:"bytes,34,opt,name=permission_link,json=permissionLink" json:"permission_link,omitempty"`                                             // app用户权限
}

func (x *MaterialMeta) Reset() {
	*x = MaterialMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialMeta) ProtoMessage() {}

func (x *MaterialMeta) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialMeta.ProtoReflect.Descriptor instead.
func (*MaterialMeta) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{15}
}

func (x *MaterialMeta) GetCreativeType() MaterialMeta_CreativeType {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return MaterialMeta_NO_TYPE
}

func (x *MaterialMeta) GetInteractionType() MaterialMeta_InteractionType {
	if x != nil && x.InteractionType != nil {
		return *x.InteractionType
	}
	return MaterialMeta_NO_INTERACTION
}

func (x *MaterialMeta) GetWinNoticeUrl() []string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return nil
}

func (x *MaterialMeta) GetClickUrl() string {
	if x != nil && x.ClickUrl != nil {
		return *x.ClickUrl
	}
	return ""
}

func (x *MaterialMeta) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *MaterialMeta) GetDescription() [][]byte {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *MaterialMeta) GetIconSrc() []string {
	if x != nil {
		return x.IconSrc
	}
	return nil
}

func (x *MaterialMeta) GetImageSrc() []string {
	if x != nil {
		return x.ImageSrc
	}
	return nil
}

func (x *MaterialMeta) GetAppPackage() string {
	if x != nil && x.AppPackage != nil {
		return *x.AppPackage
	}
	return ""
}

func (x *MaterialMeta) GetAppSize() uint32 {
	if x != nil && x.AppSize != nil {
		return *x.AppSize
	}
	return 0
}

func (x *MaterialMeta) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

func (x *MaterialMeta) GetVideoDuration() uint32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *MaterialMeta) GetMetaIndex() *MetaIndex {
	if x != nil {
		return x.MetaIndex
	}
	return nil
}

func (x *MaterialMeta) GetMaterialWidth() uint32 {
	if x != nil && x.MaterialWidth != nil {
		return *x.MaterialWidth
	}
	return 0
}

func (x *MaterialMeta) GetMaterialHeight() uint32 {
	if x != nil && x.MaterialHeight != nil {
		return *x.MaterialHeight
	}
	return 0
}

func (x *MaterialMeta) GetBrandName() string {
	if x != nil && x.BrandName != nil {
		return *x.BrandName
	}
	return ""
}

func (x *MaterialMeta) GetAdTitle() string {
	if x != nil && x.AdTitle != nil {
		return *x.AdTitle
	}
	return ""
}

func (x *MaterialMeta) GetMaterialSize() uint32 {
	if x != nil && x.MaterialSize != nil {
		return *x.MaterialSize
	}
	return 0
}

func (x *MaterialMeta) GetDeeplinkUrl() []byte {
	if x != nil {
		return x.DeeplinkUrl
	}
	return nil
}

func (x *MaterialMeta) GetFallbackType() uint32 {
	if x != nil && x.FallbackType != nil {
		return *x.FallbackType
	}
	return 0
}

func (x *MaterialMeta) GetFallbackUrl() []byte {
	if x != nil {
		return x.FallbackUrl
	}
	return nil
}

func (x *MaterialMeta) GetApkName() string {
	if x != nil && x.ApkName != nil {
		return *x.ApkName
	}
	return ""
}

func (x *MaterialMeta) GetRating() float32 {
	if x != nil && x.Rating != nil {
		return *x.Rating
	}
	return 0
}

func (x *MaterialMeta) GetComments() uint32 {
	if x != nil && x.Comments != nil {
		return *x.Comments
	}
	return 0
}

func (x *MaterialMeta) GetImageSize() *MaterialMeta_ImageSize {
	if x != nil {
		return x.ImageSize
	}
	return nil
}

func (x *MaterialMeta) GetButtonName() string {
	if x != nil && x.ButtonName != nil {
		return *x.ButtonName
	}
	return ""
}

func (x *MaterialMeta) GetAppId() []byte {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *MaterialMeta) GetMaterialPattern() MaterialMeta_MaterialPattern {
	if x != nil && x.MaterialPattern != nil {
		return *x.MaterialPattern
	}
	return MaterialMeta_MATERIAL_PATTERN_FULL_SCREEN
}

func (x *MaterialMeta) GetSkipButtonPosition() float32 {
	if x != nil && x.SkipButtonPosition != nil {
		return *x.SkipButtonPosition
	}
	return 0
}

func (x *MaterialMeta) GetAppStoreLink() []byte {
	if x != nil {
		return x.AppStoreLink
	}
	return nil
}

func (x *MaterialMeta) GetPublisher() string {
	if x != nil && x.Publisher != nil {
		return *x.Publisher
	}
	return ""
}

func (x *MaterialMeta) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *MaterialMeta) GetPrivacyLink() []byte {
	if x != nil {
		return x.PrivacyLink
	}
	return nil
}

func (x *MaterialMeta) GetPermissionLink() []byte {
	if x != nil {
		return x.PermissionLink
	}
	return nil
}

// 广告信息
// 同时填充material_meta和meta_group
// 建议使用meta_group，当前为兼容方案，后续版本将合并material_meta和meta_group为meta_group
type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdslotId     *string         `protobuf:"bytes,1,opt,name=adslot_id,json=adslotId" json:"adslot_id,omitempty"`             // 对应请求时填写的广告位ID
	HtmlSnippet  []byte          `protobuf:"bytes,2,opt,name=html_snippet,json=htmlSnippet" json:"html_snippet,omitempty"`    // HTML片段，在MSSP设置广告位返回模板时使用此字段，中文需要UTF-8编码
	MaterialMeta *MaterialMeta   `protobuf:"bytes,3,opt,name=material_meta,json=materialMeta" json:"material_meta,omitempty"` // 物料元数据，在MSSP设置广告位返回创意元数据时使用此字段
	AdKey        *string         `protobuf:"bytes,4,opt,name=ad_key,json=adKey" json:"ad_key,omitempty"`                      // 对当前返回广告的签名，可以唯一标识广告
	AdTracking   []*Tracking     `protobuf:"bytes,5,rep,name=ad_tracking,json=adTracking" json:"ad_tracking,omitempty"`       // 广告监控信息
	MetaGroup    []*MaterialMeta `protobuf:"bytes,6,rep,name=meta_group,json=metaGroup" json:"meta_group,omitempty"`          // 物料元数据组，在一个广告中含有多个物料元信息时使用
	MobAdtext    *string         `protobuf:"bytes,7,opt,name=mob_adtext,json=mobAdtext" json:"mob_adtext,omitempty"`          // 20160901新广告法出台，要求明确使用"广告"，该字段为"广告"小图标地址，媒体需要在渲染的时候添加
	MobAdlogo    *string         `protobuf:"bytes,8,opt,name=mob_adlogo,json=mobAdlogo" json:"mob_adlogo,omitempty"`          // 20160901新广告法出台，该字段为与上述字段配合使用的"熊掌"图标地址，媒体需要在渲染的时候添加
	CpmBid       *uint32         `protobuf:"varint,9,opt,name=cpm_bid,json=cpmBid" json:"cpm_bid,omitempty"`                  // 参与竞价的cpm,单位分
	Md5          *string         `protobuf:"bytes,10,opt,name=md5" json:"md5,omitempty"`                                      // 视频物料回传给媒体的md5值，目前只有tcl使用
	AdslotType   *uint32         `protobuf:"varint,11,opt,name=adslot_type,json=adslotType" json:"adslot_type,omitempty"`     // 广告产品样式，0：横幅，13：信息流，11：插屏，46：开屏
	ChargeType   *uint32         `protobuf:"varint,12,opt,name=charge_type,json=chargeType" json:"charge_type,omitempty"`     // 广告计费模式，0：CPM，1：CPC
	BuyerId      *uint32         `protobuf:"varint,13,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`              // 广告来源，竞价胜出的dsp，包括百度内部和第三方dsp等，1：NOVA，4：LU，6：MOBILE DSP，7826902：京东
	AntiTag      *int32          `protobuf:"varint,14,opt,name=anti_tag,json=antiTag" json:"anti_tag,omitempty"`              // 反作弊相关
	StartTime    *uint64         `protobuf:"varint,15,opt,name=start_time,json=startTime" json:"start_time,omitempty"`        // 曝光控制的开始时间，Unix时间戳，该字段仅供特殊媒体需要预加载时使用
	EndTime      *uint64         `protobuf:"varint,16,opt,name=end_time,json=endTime" json:"end_time,omitempty"`              // 曝光控制的结束时间，Unix时间戳，该字段仅供特殊媒体需要预加载时使用
	// (仅用于序章dsp)
	// 扩展字段，用于透传数据
	// dsp可以用序列化的json等字符串填充此字段,端上渲染时解析，bes不做处理
	CustomExtData []byte `protobuf:"bytes,17,opt,name=custom_ext_data,json=customExtData" json:"custom_ext_data,omitempty"`
	StyleType     *int32 `protobuf:"varint,18,opt,name=style_type,json=styleType" json:"style_type,omitempty"` // 返回竞胜的广告样式
	StyleConf     []byte `protobuf:"bytes,19,opt,name=style_conf,json=styleConf" json:"style_conf,omitempty"`  // 返回样式配置信息
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{16}
}

func (x *Ad) GetAdslotId() string {
	if x != nil && x.AdslotId != nil {
		return *x.AdslotId
	}
	return ""
}

func (x *Ad) GetHtmlSnippet() []byte {
	if x != nil {
		return x.HtmlSnippet
	}
	return nil
}

func (x *Ad) GetMaterialMeta() *MaterialMeta {
	if x != nil {
		return x.MaterialMeta
	}
	return nil
}

func (x *Ad) GetAdKey() string {
	if x != nil && x.AdKey != nil {
		return *x.AdKey
	}
	return ""
}

func (x *Ad) GetAdTracking() []*Tracking {
	if x != nil {
		return x.AdTracking
	}
	return nil
}

func (x *Ad) GetMetaGroup() []*MaterialMeta {
	if x != nil {
		return x.MetaGroup
	}
	return nil
}

func (x *Ad) GetMobAdtext() string {
	if x != nil && x.MobAdtext != nil {
		return *x.MobAdtext
	}
	return ""
}

func (x *Ad) GetMobAdlogo() string {
	if x != nil && x.MobAdlogo != nil {
		return *x.MobAdlogo
	}
	return ""
}

func (x *Ad) GetCpmBid() uint32 {
	if x != nil && x.CpmBid != nil {
		return *x.CpmBid
	}
	return 0
}

func (x *Ad) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

func (x *Ad) GetAdslotType() uint32 {
	if x != nil && x.AdslotType != nil {
		return *x.AdslotType
	}
	return 0
}

func (x *Ad) GetChargeType() uint32 {
	if x != nil && x.ChargeType != nil {
		return *x.ChargeType
	}
	return 0
}

func (x *Ad) GetBuyerId() uint32 {
	if x != nil && x.BuyerId != nil {
		return *x.BuyerId
	}
	return 0
}

func (x *Ad) GetAntiTag() int32 {
	if x != nil && x.AntiTag != nil {
		return *x.AntiTag
	}
	return 0
}

func (x *Ad) GetStartTime() uint64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *Ad) GetEndTime() uint64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *Ad) GetCustomExtData() []byte {
	if x != nil {
		return x.CustomExtData
	}
	return nil
}

func (x *Ad) GetStyleType() int32 {
	if x != nil && x.StyleType != nil {
		return *x.StyleType
	}
	return 0
}

func (x *Ad) GetStyleConf() []byte {
	if x != nil {
		return x.StyleConf
	}
	return nil
}

// Baidu Mobads API应答
type MobadsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId      *string `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id,omitempty"`                 // 对应请求的接入方自定义请求ID
	ErrorCode      *uint64 `protobuf:"varint,2,opt,name=error_code,json=errorCode" json:"error_code,omitempty"`                // 请求响应出错时的错误码，用于问题排查
	Ads            []*Ad   `protobuf:"bytes,3,rep,name=ads" json:"ads,omitempty"`                                              // 应答广告清单，一次请求可以返回多个广告，需要逐个解析
	ExpirationTime *uint32 `protobuf:"varint,4,opt,name=expiration_time,json=expirationTime" json:"expiration_time,omitempty"` // 广告清单过期时间戳，单位秒
	SearchKey      *string `protobuf:"bytes,5,opt,name=search_key,json=searchKey" json:"search_key,omitempty"`                 // 当次请求百度生成的唯一表示ID
	ExtStyle       []byte  `protobuf:"bytes,6,opt,name=ext_style,json=extStyle" json:"ext_style,omitempty"`                    // 特色样式渲染信息，仅特殊媒体使用
}

func (x *MobadsResponse) Reset() {
	*x = MobadsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobadsResponse) ProtoMessage() {}

func (x *MobadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobadsResponse.ProtoReflect.Descriptor instead.
func (*MobadsResponse) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{17}
}

func (x *MobadsResponse) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *MobadsResponse) GetErrorCode() uint64 {
	if x != nil && x.ErrorCode != nil {
		return *x.ErrorCode
	}
	return 0
}

func (x *MobadsResponse) GetAds() []*Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *MobadsResponse) GetExpirationTime() uint32 {
	if x != nil && x.ExpirationTime != nil {
		return *x.ExpirationTime
	}
	return 0
}

func (x *MobadsResponse) GetSearchKey() string {
	if x != nil && x.SearchKey != nil {
		return *x.SearchKey
	}
	return ""
}

func (x *MobadsResponse) GetExtStyle() []byte {
	if x != nil {
		return x.ExtStyle
	}
	return nil
}

type UserInfo_Feature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version *uint64   `protobuf:"varint,1,opt,name=version" json:"version,omitempty"` // 版本
	Value   []float32 `protobuf:"fixed32,2,rep,name=value" json:"value,omitempty"`    // 特征值
}

func (x *UserInfo_Feature) Reset() {
	*x = UserInfo_Feature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo_Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo_Feature) ProtoMessage() {}

func (x *UserInfo_Feature) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo_Feature.ProtoReflect.Descriptor instead.
func (*UserInfo_Feature) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{11, 0}
}

func (x *UserInfo_Feature) GetVersion() uint64 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *UserInfo_Feature) GetValue() []float32 {
	if x != nil {
		return x.Value
	}
	return nil
}

// 图片物料尺寸，宽、高
type MaterialMeta_ImageSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  *uint32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`   // 宽
	Height *uint32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"` // 高
}

func (x *MaterialMeta_ImageSize) Reset() {
	*x = MaterialMeta_ImageSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_baidu_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialMeta_ImageSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialMeta_ImageSize) ProtoMessage() {}

func (x *MaterialMeta_ImageSize) ProtoReflect() protoreflect.Message {
	mi := &file_baidu_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialMeta_ImageSize.ProtoReflect.Descriptor instead.
func (*MaterialMeta_ImageSize) Descriptor() ([]byte, []int) {
	return file_baidu_proto_rawDescGZIP(), []int{15, 0}
}

func (x *MaterialMeta_ImageSize) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *MaterialMeta_ImageSize) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

var File_baidu_proto protoreflect.FileDescriptor

var file_baidu_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x62,
	0x61, 0x69, 0x64, 0x75, 0x22, 0x54, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01,
	0x30, 0x52, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x05, 0x6d, 0x69, 0x6e, 0x6f,
	0x72, 0x12, 0x17, 0x0a, 0x05, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x3a, 0x01, 0x30, 0x52, 0x05, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x03, 0x41,
	0x70, 0x70, 0x12, 0x17, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x3a, 0x00, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22, 0x99, 0x02, 0x0a,
	0x04, 0x55, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x14, 0x0a, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x04, 0x69, 0x6d, 0x65,
	0x69, 0x12, 0x12, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d,
	0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35,
	0x12, 0x25, 0x0a, 0x0d, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1c, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x3a, 0x00, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x6f,
	0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x00, 0x52,
	0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x22, 0x3a, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x17, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x3a,
	0x01, 0x30, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x22, 0x89, 0x03, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x39, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x6f, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x62, 0x61,
	0x69, 0x64, 0x75, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x00, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x12, 0x16, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x3a, 0x00, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x04, 0x75, 0x64,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x2e, 0x55, 0x64, 0x49, 0x64, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x0b, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x45, 0x0a, 0x0a, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x56, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e,
	0x4f, 0x55, 0x54, 0x44, 0x4f, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x04,
	0x22, 0x1e, 0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e,
	0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x02,
	0x22, 0x6f, 0x0a, 0x06, 0x57, 0x69, 0x46, 0x69, 0x41, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x5f, 0x6d, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x4d, 0x61,
	0x63, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x73, 0x73, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x72, 0x73, 0x73, 0x69, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x61, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x22, 0x8f, 0x04, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76,
	0x34, 0x12, 0x46, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x62, 0x61, 0x69,
	0x64, 0x75, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x08,
	0x77, 0x69, 0x66, 0x69, 0x5f, 0x61, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x57, 0x69, 0x46, 0x69, 0x41, 0x70, 0x52, 0x07, 0x77,
	0x69, 0x66, 0x69, 0x41, 0x70, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x22, 0x95, 0x01, 0x0a, 0x0e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x12, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f,
	0x32, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x33, 0x47, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x34, 0x47, 0x10, 0x04, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x35, 0x47, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x57,
	0x49, 0x46, 0x49, 0x10, 0x64, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e, 0x45,
	0x54, 0x10, 0x65, 0x12, 0x0d, 0x0a, 0x08, 0x4e, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0xe7, 0x07, 0x22, 0x6f, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x49, 0x4e,
	0x41, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x48,
	0x49, 0x4e, 0x41, 0x5f, 0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x55, 0x4e, 0x49, 0x43, 0x4f, 0x4d, 0x10, 0x03, 0x12,
	0x12, 0x0a, 0x0e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f,
	0x52, 0x10, 0x63, 0x22, 0xd3, 0x01, 0x0a, 0x03, 0x47, 0x70, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x63,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x47, 0x70, 0x73,
	0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x30, 0x0a, 0x0e, 0x43, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x47, 0x53,
	0x38, 0x34, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x43, 0x4a, 0x30, 0x32, 0x10, 0x02, 0x12,
	0x08, 0x0a, 0x04, 0x42, 0x44, 0x30, 0x39, 0x10, 0x03, 0x22, 0x93, 0x05, 0x0a, 0x08, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55, 0x72, 0x6c, 0x22,
	0x9d, 0x04, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x41, 0x44, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x01,
	0x12, 0x0c, 0x0a, 0x08, 0x41, 0x44, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x53, 0x4b, 0x49, 0x50, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x4c, 0x49, 0x43,
	0x4b, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x0e, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f,
	0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x88, 0x95, 0x06, 0x12, 0x1a, 0x0a, 0x14, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x89, 0x95, 0x06, 0x12, 0x12, 0x0a, 0x0c, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41,
	0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x8a, 0x95, 0x06, 0x12, 0x1f, 0x0a, 0x19, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x5f, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x8b, 0x95, 0x06, 0x12, 0x1a, 0x0a, 0x14, 0x41, 0x50,
	0x50, 0x5f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x50, 0x41,
	0x47, 0x45, 0x10, 0xf0, 0x9c, 0x06, 0x12, 0x15, 0x0a, 0x0f, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44,
	0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0xf1, 0x9c, 0x06, 0x12, 0x1b, 0x0a,
	0x15, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xf2, 0x9c, 0x06, 0x12, 0x1b, 0x0a, 0x15, 0x41, 0x50,
	0x50, 0x5f, 0x41, 0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x50, 0x41,
	0x55, 0x53, 0x45, 0x10, 0xf3, 0x9c, 0x06, 0x12, 0x1e, 0x0a, 0x18, 0x41, 0x50, 0x50, 0x5f, 0x41,
	0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49,
	0x4e, 0x55, 0x45, 0x10, 0xf4, 0x9c, 0x06, 0x12, 0x1c, 0x0a, 0x16, 0x41, 0x50, 0x50, 0x5f, 0x41,
	0x44, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53,
	0x48, 0x10, 0xf5, 0x9c, 0x06, 0x12, 0x14, 0x0a, 0x0e, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x10, 0xf6, 0x9c, 0x06, 0x12, 0x1a, 0x0a, 0x14, 0x41,
	0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x45,
	0x47, 0x49, 0x4e, 0x10, 0xf7, 0x9c, 0x06, 0x12, 0x1a, 0x0a, 0x14, 0x41, 0x50, 0x50, 0x5f, 0x41,
	0x44, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x41, 0x55, 0x53, 0x45, 0x10,
	0xf8, 0x9c, 0x06, 0x12, 0x1d, 0x0a, 0x17, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x45, 0x10, 0xf9,
	0x9c, 0x06, 0x12, 0x1b, 0x0a, 0x15, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x10, 0xfa, 0x9c, 0x06, 0x12,
	0x19, 0x0a, 0x13, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x44, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0xfb, 0x9c, 0x06, 0x12, 0x13, 0x0a, 0x0d, 0x41, 0x50,
	0x50, 0x5f, 0x41, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0xfc, 0x9c, 0x06, 0x22,
	0xc7, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x62, 0x61, 0x69, 0x64,
	0x75, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x69, 0x67, 0x68,
	0x74, 0x52, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x44, 0x0a, 0x0f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x22, 0x40, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52,
	0x5f, 0x55, 0x47, 0x43, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x10, 0x03, 0x22, 0x43, 0x0a, 0x0e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x11, 0x0a, 0x0d, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f,
	0x54, 0x43, 0x4c, 0x5f, 0x4d, 0x50, 0x34, 0x10, 0x0b, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x5f, 0x54, 0x43, 0x4c, 0x5f, 0x54, 0x53, 0x10, 0x0c, 0x12, 0x0c, 0x0a, 0x08, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x5f, 0x54, 0x53, 0x10, 0x0d, 0x22, 0xf8, 0x01, 0x0a, 0x04, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x57, 0x6f, 0x72, 0x64, 0x22, 0xd0, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x56, 0x65, 0x63, 0x12, 0x37, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x12, 0x69, 0x73, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x1a, 0x39, 0x0a, 0x07,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x06, 0x41, 0x64, 0x53, 0x6c,
	0x6f, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x0b, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x53, 0x69, 0x7a,
	0x65, 0x52, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a,
	0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x62,
	0x61, 0x69, 0x64, 0x75, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x74, 0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x75, 0x5f,
	0x63, 0x68, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x70, 0x75, 0x43, 0x68,
	0x6e, 0x22, 0xd6, 0x05, 0x0a, 0x0d, 0x4d, 0x6f, 0x62, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70,
	0x70, 0x12, 0x25, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x62, 0x61, 0x69, 0x64,
	0x75, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x03, 0x67, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x47, 0x70, 0x73, 0x52, 0x03, 0x67, 0x70, 0x73,
	0x12, 0x25, 0x0a, 0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x52,
	0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x20, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x62, 0x75, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x52, 0x07, 0x69, 0x73, 0x44, 0x65, 0x62, 0x75, 0x67, 0x12, 0x70, 0x0a, 0x15, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x2e, 0x4d, 0x6f, 0x62, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x3a, 0x12, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x13, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x62, 0x61, 0x69, 0x64,
	0x75, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x13, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x61, 0x0a,
	0x13, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x48, 0x54, 0x54, 0x50, 0x53,
	0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02,
	0x22, 0x48, 0x0a, 0x13, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x41, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x01, 0x22, 0x4d, 0x0a, 0x09, 0x4d, 0x65,
	0x74, 0x61, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xe9, 0x0c, 0x0a, 0x0c, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0d, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4e, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x62, 0x61,
	0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6e, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0c, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x63, 0x6f, 0x6e, 0x53, 0x72, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x72, 0x63, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x53, 0x72, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a,
	0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x61,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x27, 0x0a, 0x0f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3c,
	0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x74, 0x61, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x62, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x73, 0x6b, 0x69, 0x70, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x27,
	0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x1a, 0x39, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x22, 0x6a, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41,
	0x47, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49, 0x43, 0x4f,
	0x4e, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x12, 0x0e,
	0x0a, 0x0a, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x05, 0x12, 0x0e,
	0x0a, 0x0a, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x06, 0x22, 0x4e,
	0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x03, 0x22, 0x55,
	0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x5f, 0x50, 0x41,
	0x54, 0x54, 0x45, 0x52, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x5f,
	0x50, 0x41, 0x54, 0x54, 0x45, 0x52, 0x4e, 0x5f, 0x48, 0x41, 0x4c, 0x46, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x02, 0x22, 0xfc, 0x04, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x74, 0x6d,
	0x6c, 0x5f, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x68, 0x74, 0x6d, 0x6c, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x38, 0x0a, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x64, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x30, 0x0a,
	0x0b, 0x61, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x0a, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x32, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x5f, 0x61, 0x64, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x62, 0x41, 0x64, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x5f, 0x61, 0x64, 0x6c, 0x6f, 0x67, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x62, 0x41, 0x64, 0x6c, 0x6f, 0x67,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x70, 0x6d, 0x5f, 0x62, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x63, 0x70, 0x6d, 0x42, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64,
	0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x62, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6e, 0x74,
	0x69, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x6e, 0x74,
	0x69, 0x54, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x65, 0x78, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x45,
	0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x22, 0xd0, 0x01, 0x0a, 0x0e, 0x4d, 0x6f, 0x62, 0x61, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x62, 0x61, 0x69, 0x64, 0x75, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61,
	0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78,
	0x74, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65,
	0x78, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x42, 0x13, 0x5a, 0x11, 0x6d, 0x68, 0x5f, 0x70, 0x72,
	0x6f, 0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x62, 0x61, 0x69, 0x64, 0x75,
}

var (
	file_baidu_proto_rawDescOnce sync.Once
	file_baidu_proto_rawDescData = file_baidu_proto_rawDesc
)

func file_baidu_proto_rawDescGZIP() []byte {
	file_baidu_proto_rawDescOnce.Do(func() {
		file_baidu_proto_rawDescData = protoimpl.X.CompressGZIP(file_baidu_proto_rawDescData)
	})
	return file_baidu_proto_rawDescData
}

var file_baidu_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_baidu_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_baidu_proto_goTypes = []interface{}{
	(Device_DeviceType)(0),                 // 0: baidu.Device.DeviceType
	(Device_OsType)(0),                     // 1: baidu.Device.OsType
	(Network_ConnectionType)(0),            // 2: baidu.Network.ConnectionType
	(Network_OperatorType)(0),              // 3: baidu.Network.OperatorType
	(Gps_CoordinateType)(0),                // 4: baidu.Gps.CoordinateType
	(Tracking_TrackingEvent)(0),            // 5: baidu.Tracking.TrackingEvent
	(Video_CopyRight)(0),                   // 6: baidu.Video.CopyRight
	(Video_MaterialFormat)(0),              // 7: baidu.Video.MaterialFormat
	(MobadsRequest_RequestProtocolType)(0), // 8: baidu.MobadsRequest.RequestProtocolType
	(MobadsRequest_MediaSupportAbility)(0), // 9: baidu.MobadsRequest.MediaSupportAbility
	(MaterialMeta_CreativeType)(0),         // 10: baidu.MaterialMeta.CreativeType
	(MaterialMeta_InteractionType)(0),      // 11: baidu.MaterialMeta.InteractionType
	(MaterialMeta_MaterialPattern)(0),      // 12: baidu.MaterialMeta.MaterialPattern
	(*Version)(nil),                        // 13: baidu.Version
	(*App)(nil),                            // 14: baidu.App
	(*UdId)(nil),                           // 15: baidu.UdId
	(*Size)(nil),                           // 16: baidu.Size
	(*Device)(nil),                         // 17: baidu.Device
	(*WiFiAp)(nil),                         // 18: baidu.WiFiAp
	(*Network)(nil),                        // 19: baidu.Network
	(*Gps)(nil),                            // 20: baidu.Gps
	(*Tracking)(nil),                       // 21: baidu.Tracking
	(*Video)(nil),                          // 22: baidu.Video
	(*Page)(nil),                           // 23: baidu.Page
	(*UserInfo)(nil),                       // 24: baidu.UserInfo
	(*AdSlot)(nil),                         // 25: baidu.AdSlot
	(*MobadsRequest)(nil),                  // 26: baidu.MobadsRequest
	(*MetaIndex)(nil),                      // 27: baidu.MetaIndex
	(*MaterialMeta)(nil),                   // 28: baidu.MaterialMeta
	(*Ad)(nil),                             // 29: baidu.Ad
	(*MobadsResponse)(nil),                 // 30: baidu.MobadsResponse
	(*UserInfo_Feature)(nil),               // 31: baidu.UserInfo.Feature
	(*MaterialMeta_ImageSize)(nil),         // 32: baidu.MaterialMeta.ImageSize
}
var file_baidu_proto_depIdxs = []int32{
	13, // 0: baidu.App.app_version:type_name -> baidu.Version
	0,  // 1: baidu.Device.device_type:type_name -> baidu.Device.DeviceType
	1,  // 2: baidu.Device.os_type:type_name -> baidu.Device.OsType
	13, // 3: baidu.Device.os_version:type_name -> baidu.Version
	15, // 4: baidu.Device.udid:type_name -> baidu.UdId
	16, // 5: baidu.Device.screen_size:type_name -> baidu.Size
	2,  // 6: baidu.Network.connection_type:type_name -> baidu.Network.ConnectionType
	3,  // 7: baidu.Network.operator_type:type_name -> baidu.Network.OperatorType
	18, // 8: baidu.Network.wifi_aps:type_name -> baidu.WiFiAp
	4,  // 9: baidu.Gps.coordinate_type:type_name -> baidu.Gps.CoordinateType
	5,  // 10: baidu.Tracking.tracking_event:type_name -> baidu.Tracking.TrackingEvent
	6,  // 11: baidu.Video.copyright:type_name -> baidu.Video.CopyRight
	7,  // 12: baidu.Video.material_format:type_name -> baidu.Video.MaterialFormat
	31, // 13: baidu.UserInfo.feature_vec:type_name -> baidu.UserInfo.Feature
	16, // 14: baidu.AdSlot.adslot_size:type_name -> baidu.Size
	22, // 15: baidu.AdSlot.video:type_name -> baidu.Video
	13, // 16: baidu.MobadsRequest.api_version:type_name -> baidu.Version
	14, // 17: baidu.MobadsRequest.app:type_name -> baidu.App
	17, // 18: baidu.MobadsRequest.device:type_name -> baidu.Device
	19, // 19: baidu.MobadsRequest.network:type_name -> baidu.Network
	20, // 20: baidu.MobadsRequest.gps:type_name -> baidu.Gps
	25, // 21: baidu.MobadsRequest.adslot:type_name -> baidu.AdSlot
	8,  // 22: baidu.MobadsRequest.request_protocol_type:type_name -> baidu.MobadsRequest.RequestProtocolType
	23, // 23: baidu.MobadsRequest.page:type_name -> baidu.Page
	24, // 24: baidu.MobadsRequest.userinfo:type_name -> baidu.UserInfo
	10, // 25: baidu.MaterialMeta.creative_type:type_name -> baidu.MaterialMeta.CreativeType
	11, // 26: baidu.MaterialMeta.interaction_type:type_name -> baidu.MaterialMeta.InteractionType
	27, // 27: baidu.MaterialMeta.meta_index:type_name -> baidu.MetaIndex
	32, // 28: baidu.MaterialMeta.image_size:type_name -> baidu.MaterialMeta.ImageSize
	12, // 29: baidu.MaterialMeta.material_pattern:type_name -> baidu.MaterialMeta.MaterialPattern
	28, // 30: baidu.Ad.material_meta:type_name -> baidu.MaterialMeta
	21, // 31: baidu.Ad.ad_tracking:type_name -> baidu.Tracking
	28, // 32: baidu.Ad.meta_group:type_name -> baidu.MaterialMeta
	29, // 33: baidu.MobadsResponse.ads:type_name -> baidu.Ad
	34, // [34:34] is the sub-list for method output_type
	34, // [34:34] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_baidu_proto_init() }
func file_baidu_proto_init() {
	if File_baidu_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_baidu_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Version); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UdId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WiFiAp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tracking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Page); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobadsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobadsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo_Feature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_baidu_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialMeta_ImageSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_baidu_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_baidu_proto_goTypes,
		DependencyIndexes: file_baidu_proto_depIdxs,
		EnumInfos:         file_baidu_proto_enumTypes,
		MessageInfos:      file_baidu_proto_msgTypes,
	}.Build()
	File_baidu_proto = out.File
	file_baidu_proto_rawDesc = nil
	file_baidu_proto_goTypes = nil
	file_baidu_proto_depIdxs = nil
}
