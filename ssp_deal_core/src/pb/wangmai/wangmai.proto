syntax = "proto3";
option java_package = "com.adwm.ssp.engine.service.rtb";
option java_outer_classname = "WmRtbSimple";

package wangmai;
option go_package = "mh_proxy/pb/wangmai";

message BidRequest {
  string id = 1;
  repeated Imp imp = 2;
  App app = 3;
  Device device = 4;
  User user = 5;
  string apiVersion = 6;
  repeated string installedApp = 7;

  message Imp{
    string id = 7;
    string tagId = 8;
    AdslotSize adslotSize = 9;
    double bidfloor = 10;
    bool deeplink = 11;
    int32 secure = 12;
    repeated Deal deals = 13;
  }

  message AdslotSize{
    int32 width = 14;
    int32 height = 15;
    repeated string mimes = 16;
    int32 size = 17;
    int32 titleLength = 18;
    int32 descLength = 19;
    int32 minDuration = 20;
    int32 maxDuration = 21;
  }

  message App{
    string appName = 22;
    string bundle = 23;
    string appVersion = 24;
  }

  message Device{
    string os = 25;
    string osv = 26;
    string imei = 27;
    string imeiMd5 = 28;
    string oaid = 29;
    string oaidMd5 = 30;
    string androidId = 31;
    string idfa = 32;
    string idfaMd5 = 33;
    string mac = 34;
    string macMd5 = 35;
    string ip = 36;
    string ipV6 = 37;
    string ua = 38;
    int32 connectionType = 39;
    string brand = 40;
    string make = 41;
    string model = 42;
    string hwv = 43;
    int32 carrier = 44;
    string mccMnc = 45;
    int32 screenHeight = 46;
    int32 screenWidth = 47;
    int32 ppi = 48;
    Geo geo = 49;
    string appList = 50;
    string bootMark = 51;
    string updateMark = 52;
    string verCodeOfHms = 53;
    string verCodeOfAG = 54;
    string romVersion = 55;
    int32 orientation = 56;
    string bootTimeSec = 57;
    string phoneName = 58;
    int64 memorySize = 59;
    int64 diskSize = 60;
    string osUpdateTimeSec = 61;
    string modelCode = 62;
    string timeZone = 63;
    // 废弃
    string fileTime = 64;
    string deviceBirthTime = 65;
    string caidVersion = 66;
  }

  message Deal{
    string id = 57;
    double bidfloor = 58;
  }



  message Geo{
    int32 type = 59;
    double lat = 60;
    double lon = 61;
    string country = 62;
  }

  message User{
    int32 age = 63;
    int32 gender = 64;
  }
}
message BidResponse {
  string id = 65;
  string bidid = 66;
  repeated SeatBid seatbid = 67;


  message SeatBid{
    repeated Bid bid = 68;
  }

  message Bid{
    string impid = 69;
    int32 adType = 70;
    int32 adStyle = 71;
    Item  items = 72;
    double price = 73;
    string nurl = 74;
    string crid = 99;
  }

  message Item{
    string title = 75;
    string desc = 76;
    string icon = 77;
    string html = 78;
    int32 mediaStyle = 79;
    string downloadUrl = 80;
    DownloadAppInfo downloadAppInfo = 81;
    string clickUrl = 82;
    string dplUrl = 83;
    repeated string imgs = 84;
    repeated string exposalUrls = 85;
    repeated string clickMonitorUrls = 86;
    Video video = 87;
    string miniProgramId = 88;
    string miniProgramPath = 89;
    int32 miniProgramType = 90;
    repeated string miniProgramSuccessTrackUrls = 91;
    repeated string downloadTrackUrls = 92;
    repeated string downloadedTrackUrls = 93;
    repeated string installedTrackUrls = 94;
    repeated string dpSuccessTrackUrls = 95;
    repeated string actionTrackUrls = 96;
    string packageName = 97;
  }

  message DownloadAppInfo{
    string appName = 88;
    string developer = 89;
    string version = 90;
    string packetSize = 91;
    string privacy = 92;
    string permission = 93;
  }

  message Video{
    string videoUrl = 94;
    int32 videoDuration = 95;
    string videoStartUrl = 96;
    string videoFinishUrl = 97;
    string videoVastXml = 98;
    string videoEndImgurl = 99;
    string videoPreImgurl = 100;
  }

}