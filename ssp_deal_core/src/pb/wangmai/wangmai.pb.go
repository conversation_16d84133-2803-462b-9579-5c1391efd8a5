// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: wangmai.proto

package wangmai

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Imp          []*BidRequest_Imp  `protobuf:"bytes,2,rep,name=imp,proto3" json:"imp,omitempty"`
	App          *BidRequest_App    `protobuf:"bytes,3,opt,name=app,proto3" json:"app,omitempty"`
	Device       *BidRequest_Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	User         *BidRequest_User   `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	ApiVersion   string             `protobuf:"bytes,6,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	InstalledApp []string           `protobuf:"bytes,7,rep,name=installedApp,proto3" json:"installedApp,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string                 `protobuf:"bytes,65,opt,name=id,proto3" json:"id,omitempty"`
	Bidid   string                 `protobuf:"bytes,66,opt,name=bidid,proto3" json:"bidid,omitempty"`
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,67,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                 `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	TagId      string                 `protobuf:"bytes,8,opt,name=tagId,proto3" json:"tagId,omitempty"`
	AdslotSize *BidRequest_AdslotSize `protobuf:"bytes,9,opt,name=adslotSize,proto3" json:"adslotSize,omitempty"`
	Bidfloor   float64                `protobuf:"fixed64,10,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Deeplink   bool                   `protobuf:"varint,11,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Secure     int32                  `protobuf:"varint,12,opt,name=secure,proto3" json:"secure,omitempty"`
	Deals      []*BidRequest_Deal     `protobuf:"bytes,13,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdslotSize() *BidRequest_AdslotSize {
	if x != nil {
		return x.AdslotSize
	}
	return nil
}

func (x *BidRequest_Imp) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetDeeplink() bool {
	if x != nil {
		return x.Deeplink
	}
	return false
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequest_Imp) GetDeals() []*BidRequest_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type BidRequest_AdslotSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width       int32    `protobuf:"varint,14,opt,name=width,proto3" json:"width,omitempty"`
	Height      int32    `protobuf:"varint,15,opt,name=height,proto3" json:"height,omitempty"`
	Mimes       []string `protobuf:"bytes,16,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Size        int32    `protobuf:"varint,17,opt,name=size,proto3" json:"size,omitempty"`
	TitleLength int32    `protobuf:"varint,18,opt,name=titleLength,proto3" json:"titleLength,omitempty"`
	DescLength  int32    `protobuf:"varint,19,opt,name=descLength,proto3" json:"descLength,omitempty"`
	MinDuration int32    `protobuf:"varint,20,opt,name=minDuration,proto3" json:"minDuration,omitempty"`
	MaxDuration int32    `protobuf:"varint,21,opt,name=maxDuration,proto3" json:"maxDuration,omitempty"`
}

func (x *BidRequest_AdslotSize) Reset() {
	*x = BidRequest_AdslotSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdslotSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdslotSize) ProtoMessage() {}

func (x *BidRequest_AdslotSize) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdslotSize.ProtoReflect.Descriptor instead.
func (*BidRequest_AdslotSize) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_AdslotSize) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_AdslotSize) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetTitleLength() int32 {
	if x != nil {
		return x.TitleLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetDescLength() int32 {
	if x != nil {
		return x.DescLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMinDuration() int32 {
	if x != nil {
		return x.MinDuration
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,22,opt,name=appName,proto3" json:"appName,omitempty"`
	Bundle     string `protobuf:"bytes,23,opt,name=bundle,proto3" json:"bundle,omitempty"`
	AppVersion string `protobuf:"bytes,24,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_App) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os              string          `protobuf:"bytes,25,opt,name=os,proto3" json:"os,omitempty"`
	Osv             string          `protobuf:"bytes,26,opt,name=osv,proto3" json:"osv,omitempty"`
	Imei            string          `protobuf:"bytes,27,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5         string          `protobuf:"bytes,28,opt,name=imeiMd5,proto3" json:"imeiMd5,omitempty"`
	Oaid            string          `protobuf:"bytes,29,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5         string          `protobuf:"bytes,30,opt,name=oaidMd5,proto3" json:"oaidMd5,omitempty"`
	AndroidId       string          `protobuf:"bytes,31,opt,name=androidId,proto3" json:"androidId,omitempty"`
	Idfa            string          `protobuf:"bytes,32,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5         string          `protobuf:"bytes,33,opt,name=idfaMd5,proto3" json:"idfaMd5,omitempty"`
	Mac             string          `protobuf:"bytes,34,opt,name=mac,proto3" json:"mac,omitempty"`
	MacMd5          string          `protobuf:"bytes,35,opt,name=macMd5,proto3" json:"macMd5,omitempty"`
	Ip              string          `protobuf:"bytes,36,opt,name=ip,proto3" json:"ip,omitempty"`
	IpV6            string          `protobuf:"bytes,37,opt,name=ipV6,proto3" json:"ipV6,omitempty"`
	Ua              string          `protobuf:"bytes,38,opt,name=ua,proto3" json:"ua,omitempty"`
	ConnectionType  int32           `protobuf:"varint,39,opt,name=connectionType,proto3" json:"connectionType,omitempty"`
	Brand           string          `protobuf:"bytes,40,opt,name=brand,proto3" json:"brand,omitempty"`
	Make            string          `protobuf:"bytes,41,opt,name=make,proto3" json:"make,omitempty"`
	Model           string          `protobuf:"bytes,42,opt,name=model,proto3" json:"model,omitempty"`
	Hwv             string          `protobuf:"bytes,43,opt,name=hwv,proto3" json:"hwv,omitempty"`
	Carrier         int32           `protobuf:"varint,44,opt,name=carrier,proto3" json:"carrier,omitempty"`
	MccMnc          string          `protobuf:"bytes,45,opt,name=mccMnc,proto3" json:"mccMnc,omitempty"`
	ScreenHeight    int32           `protobuf:"varint,46,opt,name=screenHeight,proto3" json:"screenHeight,omitempty"`
	ScreenWidth     int32           `protobuf:"varint,47,opt,name=screenWidth,proto3" json:"screenWidth,omitempty"`
	Ppi             int32           `protobuf:"varint,48,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo             *BidRequest_Geo `protobuf:"bytes,49,opt,name=geo,proto3" json:"geo,omitempty"`
	AppList         string          `protobuf:"bytes,50,opt,name=appList,proto3" json:"appList,omitempty"`
	BootMark        string          `protobuf:"bytes,51,opt,name=bootMark,proto3" json:"bootMark,omitempty"`
	UpdateMark      string          `protobuf:"bytes,52,opt,name=updateMark,proto3" json:"updateMark,omitempty"`
	VerCodeOfHms    string          `protobuf:"bytes,53,opt,name=verCodeOfHms,proto3" json:"verCodeOfHms,omitempty"`
	VerCodeOfAG     string          `protobuf:"bytes,54,opt,name=verCodeOfAG,proto3" json:"verCodeOfAG,omitempty"`
	RomVersion      string          `protobuf:"bytes,55,opt,name=romVersion,proto3" json:"romVersion,omitempty"`
	Orientation     int32           `protobuf:"varint,56,opt,name=orientation,proto3" json:"orientation,omitempty"`
	BootTimeSec     string          `protobuf:"bytes,57,opt,name=bootTimeSec,proto3" json:"bootTimeSec,omitempty"`
	PhoneName       string          `protobuf:"bytes,58,opt,name=phoneName,proto3" json:"phoneName,omitempty"`
	MemorySize      int64           `protobuf:"varint,59,opt,name=memorySize,proto3" json:"memorySize,omitempty"`
	DiskSize        int64           `protobuf:"varint,60,opt,name=diskSize,proto3" json:"diskSize,omitempty"`
	OsUpdateTimeSec string          `protobuf:"bytes,61,opt,name=osUpdateTimeSec,proto3" json:"osUpdateTimeSec,omitempty"`
	ModelCode       string          `protobuf:"bytes,62,opt,name=modelCode,proto3" json:"modelCode,omitempty"`
	TimeZone        string          `protobuf:"bytes,63,opt,name=timeZone,proto3" json:"timeZone,omitempty"`
	// 废弃
	FileTime        string `protobuf:"bytes,64,opt,name=fileTime,proto3" json:"fileTime,omitempty"`
	DeviceBirthTime string `protobuf:"bytes,65,opt,name=deviceBirthTime,proto3" json:"deviceBirthTime,omitempty"`
	CaidVersion     string `protobuf:"bytes,66,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetIpV6() string {
	if x != nil {
		return x.IpV6
	}
	return ""
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil {
		return x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil {
		return x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMccMnc() string {
	if x != nil {
		return x.MccMnc
	}
	return ""
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetAppList() string {
	if x != nil {
		return x.AppList
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfHms() string {
	if x != nil {
		return x.VerCodeOfHms
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfAG() string {
	if x != nil {
		return x.VerCodeOfAG
	}
	return ""
}

func (x *BidRequest_Device) GetRomVersion() string {
	if x != nil {
		return x.RomVersion
	}
	return ""
}

func (x *BidRequest_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *BidRequest_Device) GetBootTimeSec() string {
	if x != nil {
		return x.BootTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *BidRequest_Device) GetMemorySize() int64 {
	if x != nil {
		return x.MemorySize
	}
	return 0
}

func (x *BidRequest_Device) GetDiskSize() int64 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *BidRequest_Device) GetOsUpdateTimeSec() string {
	if x != nil {
		return x.OsUpdateTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetModelCode() string {
	if x != nil {
		return x.ModelCode
	}
	return ""
}

func (x *BidRequest_Device) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *BidRequest_Device) GetFileTime() string {
	if x != nil {
		return x.FileTime
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceBirthTime() string {
	if x != nil {
		return x.DeviceBirthTime
	}
	return ""
}

func (x *BidRequest_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

type BidRequest_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string  `protobuf:"bytes,57,opt,name=id,proto3" json:"id,omitempty"`
	Bidfloor float64 `protobuf:"fixed64,58,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
}

func (x *BidRequest_Deal) Reset() {
	*x = BidRequest_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Deal) ProtoMessage() {}

func (x *BidRequest_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Deal) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Deal) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32   `protobuf:"varint,59,opt,name=type,proto3" json:"type,omitempty"`
	Lat     float64 `protobuf:"fixed64,60,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon     float64 `protobuf:"fixed64,61,opt,name=lon,proto3" json:"lon,omitempty"`
	Country string  `protobuf:"bytes,62,opt,name=country,proto3" json:"country,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Age    int32 `protobuf:"varint,63,opt,name=age,proto3" json:"age,omitempty"`
	Gender int32 `protobuf:"varint,64,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_User) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *BidRequest_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_Bid `protobuf:"bytes,68,rep,name=bid,proto3" json:"bid,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid   string            `protobuf:"bytes,69,opt,name=impid,proto3" json:"impid,omitempty"`
	AdType  int32             `protobuf:"varint,70,opt,name=adType,proto3" json:"adType,omitempty"`
	AdStyle int32             `protobuf:"varint,71,opt,name=adStyle,proto3" json:"adStyle,omitempty"`
	Items   *BidResponse_Item `protobuf:"bytes,72,opt,name=items,proto3" json:"items,omitempty"`
	Price   float64           `protobuf:"fixed64,73,opt,name=price,proto3" json:"price,omitempty"`
	Nurl    string            `protobuf:"bytes,74,opt,name=nurl,proto3" json:"nurl,omitempty"`
	Crid    string            `protobuf:"bytes,99,opt,name=crid,proto3" json:"crid,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_Bid) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidResponse_Bid) GetAdStyle() int32 {
	if x != nil {
		return x.AdStyle
	}
	return 0
}

func (x *BidResponse_Bid) GetItems() *BidResponse_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BidResponse_Bid) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

type BidResponse_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                       string                       `protobuf:"bytes,75,opt,name=title,proto3" json:"title,omitempty"`
	Desc                        string                       `protobuf:"bytes,76,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                        string                       `protobuf:"bytes,77,opt,name=icon,proto3" json:"icon,omitempty"`
	Html                        string                       `protobuf:"bytes,78,opt,name=html,proto3" json:"html,omitempty"`
	MediaStyle                  int32                        `protobuf:"varint,79,opt,name=mediaStyle,proto3" json:"mediaStyle,omitempty"`
	DownloadUrl                 string                       `protobuf:"bytes,80,opt,name=downloadUrl,proto3" json:"downloadUrl,omitempty"`
	DownloadAppInfo             *BidResponse_DownloadAppInfo `protobuf:"bytes,81,opt,name=downloadAppInfo,proto3" json:"downloadAppInfo,omitempty"`
	ClickUrl                    string                       `protobuf:"bytes,82,opt,name=clickUrl,proto3" json:"clickUrl,omitempty"`
	DplUrl                      string                       `protobuf:"bytes,83,opt,name=dplUrl,proto3" json:"dplUrl,omitempty"`
	Imgs                        []string                     `protobuf:"bytes,84,rep,name=imgs,proto3" json:"imgs,omitempty"`
	ExposalUrls                 []string                     `protobuf:"bytes,85,rep,name=exposalUrls,proto3" json:"exposalUrls,omitempty"`
	ClickMonitorUrls            []string                     `protobuf:"bytes,86,rep,name=clickMonitorUrls,proto3" json:"clickMonitorUrls,omitempty"`
	Video                       *BidResponse_Video           `protobuf:"bytes,87,opt,name=video,proto3" json:"video,omitempty"`
	MiniProgramId               string                       `protobuf:"bytes,88,opt,name=miniProgramId,proto3" json:"miniProgramId,omitempty"`
	MiniProgramPath             string                       `protobuf:"bytes,89,opt,name=miniProgramPath,proto3" json:"miniProgramPath,omitempty"`
	MiniProgramType             int32                        `protobuf:"varint,90,opt,name=miniProgramType,proto3" json:"miniProgramType,omitempty"`
	MiniProgramSuccessTrackUrls []string                     `protobuf:"bytes,91,rep,name=miniProgramSuccessTrackUrls,proto3" json:"miniProgramSuccessTrackUrls,omitempty"`
	DownloadTrackUrls           []string                     `protobuf:"bytes,92,rep,name=downloadTrackUrls,proto3" json:"downloadTrackUrls,omitempty"`
	DownloadedTrackUrls         []string                     `protobuf:"bytes,93,rep,name=downloadedTrackUrls,proto3" json:"downloadedTrackUrls,omitempty"`
	InstalledTrackUrls          []string                     `protobuf:"bytes,94,rep,name=installedTrackUrls,proto3" json:"installedTrackUrls,omitempty"`
	DpSuccessTrackUrls          []string                     `protobuf:"bytes,95,rep,name=dpSuccessTrackUrls,proto3" json:"dpSuccessTrackUrls,omitempty"`
	ActionTrackUrls             []string                     `protobuf:"bytes,96,rep,name=actionTrackUrls,proto3" json:"actionTrackUrls,omitempty"`
	PackageName                 string                       `protobuf:"bytes,97,opt,name=packageName,proto3" json:"packageName,omitempty"`
}

func (x *BidResponse_Item) Reset() {
	*x = BidResponse_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Item) ProtoMessage() {}

func (x *BidResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Item.ProtoReflect.Descriptor instead.
func (*BidResponse_Item) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BidResponse_Item) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Item) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponse_Item) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

func (x *BidResponse_Item) GetMediaStyle() int32 {
	if x != nil {
		return x.MediaStyle
	}
	return 0
}

func (x *BidResponse_Item) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDownloadAppInfo() *BidResponse_DownloadAppInfo {
	if x != nil {
		return x.DownloadAppInfo
	}
	return nil
}

func (x *BidResponse_Item) GetClickUrl() string {
	if x != nil {
		return x.ClickUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDplUrl() string {
	if x != nil {
		return x.DplUrl
	}
	return ""
}

func (x *BidResponse_Item) GetImgs() []string {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidResponse_Item) GetExposalUrls() []string {
	if x != nil {
		return x.ExposalUrls
	}
	return nil
}

func (x *BidResponse_Item) GetClickMonitorUrls() []string {
	if x != nil {
		return x.ClickMonitorUrls
	}
	return nil
}

func (x *BidResponse_Item) GetVideo() *BidResponse_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Item) GetMiniProgramId() string {
	if x != nil {
		return x.MiniProgramId
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramPath() string {
	if x != nil {
		return x.MiniProgramPath
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramType() int32 {
	if x != nil {
		return x.MiniProgramType
	}
	return 0
}

func (x *BidResponse_Item) GetMiniProgramSuccessTrackUrls() []string {
	if x != nil {
		return x.MiniProgramSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadTrackUrls() []string {
	if x != nil {
		return x.DownloadTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadedTrackUrls() []string {
	if x != nil {
		return x.DownloadedTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetInstalledTrackUrls() []string {
	if x != nil {
		return x.InstalledTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDpSuccessTrackUrls() []string {
	if x != nil {
		return x.DpSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetActionTrackUrls() []string {
	if x != nil {
		return x.ActionTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

type BidResponse_DownloadAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,88,opt,name=appName,proto3" json:"appName,omitempty"`
	Developer  string `protobuf:"bytes,89,opt,name=developer,proto3" json:"developer,omitempty"`
	Version    string `protobuf:"bytes,90,opt,name=version,proto3" json:"version,omitempty"`
	PacketSize string `protobuf:"bytes,91,opt,name=packetSize,proto3" json:"packetSize,omitempty"`
	Privacy    string `protobuf:"bytes,92,opt,name=privacy,proto3" json:"privacy,omitempty"`
	Permission string `protobuf:"bytes,93,opt,name=permission,proto3" json:"permission,omitempty"`
}

func (x *BidResponse_DownloadAppInfo) Reset() {
	*x = BidResponse_DownloadAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_DownloadAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_DownloadAppInfo) ProtoMessage() {}

func (x *BidResponse_DownloadAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_DownloadAppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_DownloadAppInfo) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1, 3}
}

func (x *BidResponse_DownloadAppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPacketSize() string {
	if x != nil {
		return x.PacketSize
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPrivacy() string {
	if x != nil {
		return x.Privacy
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

type BidResponse_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl       string `protobuf:"bytes,94,opt,name=videoUrl,proto3" json:"videoUrl,omitempty"`
	VideoDuration  int32  `protobuf:"varint,95,opt,name=videoDuration,proto3" json:"videoDuration,omitempty"`
	VideoStartUrl  string `protobuf:"bytes,96,opt,name=videoStartUrl,proto3" json:"videoStartUrl,omitempty"`
	VideoFinishUrl string `protobuf:"bytes,97,opt,name=videoFinishUrl,proto3" json:"videoFinishUrl,omitempty"`
	VideoVastXml   string `protobuf:"bytes,98,opt,name=videoVastXml,proto3" json:"videoVastXml,omitempty"`
	VideoEndImgurl string `protobuf:"bytes,99,opt,name=videoEndImgurl,proto3" json:"videoEndImgurl,omitempty"`
	VideoPreImgurl string `protobuf:"bytes,100,opt,name=videoPreImgurl,proto3" json:"videoPreImgurl,omitempty"`
}

func (x *BidResponse_Video) Reset() {
	*x = BidResponse_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wangmai_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Video) ProtoMessage() {}

func (x *BidResponse_Video) ProtoReflect() protoreflect.Message {
	mi := &file_wangmai_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Video) Descriptor() ([]byte, []int) {
	return file_wangmai_proto_rawDescGZIP(), []int{1, 4}
}

func (x *BidResponse_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Video) GetVideoStartUrl() string {
	if x != nil {
		return x.VideoStartUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoFinishUrl() string {
	if x != nil {
		return x.VideoFinishUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoVastXml() string {
	if x != nil {
		return x.VideoVastXml
	}
	return ""
}

func (x *BidResponse_Video) GetVideoEndImgurl() string {
	if x != nil {
		return x.VideoEndImgurl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoPreImgurl() string {
	if x != nil {
		return x.VideoPreImgurl
	}
	return ""
}

var File_wangmai_proto protoreflect.FileDescriptor

var file_wangmai_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x22, 0x93, 0x11, 0x0a, 0x0a, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69,
	0x6d, 0x70, 0x12, 0x29, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x32, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x2c, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64,
	0x41, 0x70, 0x70, 0x1a, 0xeb, 0x01, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x61, 0x67, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x65, 0x12, 0x2e, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c,
	0x73, 0x1a, 0xea, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x69, 0x6d, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x64, 0x65, 0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69,
	0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x57,
	0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x85, 0x09, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6f, 0x73, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6d, 0x65, 0x69,
	0x4d, 0x64, 0x35, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d,
	0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64,
	0x35, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x63, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x56, 0x36, 0x18, 0x25,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x56, 0x36, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61,
	0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65,
	0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x77, 0x76, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x68, 0x77, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x63, 0x63, 0x4d, 0x6e, 0x63, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x63, 0x63, 0x4d, 0x6e, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x70, 0x69, 0x18, 0x30, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x29,
	0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x77, 0x61,
	0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x18,
	0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x34, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x22, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x18,
	0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66,
	0x48, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66,
	0x41, 0x47, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x4f, 0x66, 0x41, 0x47, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x38, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65,
	0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f,
	0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x73,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x40, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72,
	0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x42, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a,
	0x32, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x39, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x3e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x1a, 0x30, 0x0a, 0x04,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0xac,
	0x0d, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x69, 0x64, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18,
	0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74,
	0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x1a, 0x35, 0x0a, 0x07,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x44,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03,
	0x62, 0x69, 0x64, 0x1a, 0xbc, 0x01, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6d, 0x70, 0x69, 0x64, 0x18, 0x45, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x46, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x53,
	0x74, 0x79, 0x6c, 0x65, 0x18, 0x47, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x64, 0x53, 0x74,
	0x79, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x48, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x49, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75,
	0x72, 0x6c, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72,
	0x69, 0x64, 0x1a, 0xfa, 0x06, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x4d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x74, 0x6d,
	0x6c, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x74, 0x6d, 0x6c, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x4f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x50, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12,
	0x4e, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d,
	0x61, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x52, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x70, 0x6c, 0x55, 0x72, 0x6c, 0x18, 0x53, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x70, 0x6c,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x54, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73,
	0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x55, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x56, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x57,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x40, 0x0a, 0x1b, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x18, 0x5b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x30, 0x0a, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x12, 0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x61, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a,
	0xbd, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x58,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x59, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x18, 0x5c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12,
	0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x1a,
	0x8b, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x72,
	0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x55, 0x72, 0x6c, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x6d, 0x6c, 0x18, 0x62, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x6d, 0x6c, 0x12, 0x26, 0x0a,
	0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49,
	0x6d, 0x67, 0x75, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x72,
	0x65, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x42, 0x3a, 0x0a,
	0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x64, 0x77, 0x6d, 0x2e, 0x73, 0x73, 0x70, 0x2e, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x74, 0x62,
	0x42, 0x0b, 0x57, 0x6d, 0x52, 0x74, 0x62, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5a, 0x0a, 0x2e,
	0x2e, 0x2f, 0x77, 0x61, 0x6e, 0x67, 0x6d, 0x61, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_wangmai_proto_rawDescOnce sync.Once
	file_wangmai_proto_rawDescData = file_wangmai_proto_rawDesc
)

func file_wangmai_proto_rawDescGZIP() []byte {
	file_wangmai_proto_rawDescOnce.Do(func() {
		file_wangmai_proto_rawDescData = protoimpl.X.CompressGZIP(file_wangmai_proto_rawDescData)
	})
	return file_wangmai_proto_rawDescData
}

var file_wangmai_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_wangmai_proto_goTypes = []interface{}{
	(*BidRequest)(nil),                  // 0: wangmai.BidRequest
	(*BidResponse)(nil),                 // 1: wangmai.BidResponse
	(*BidRequest_Imp)(nil),              // 2: wangmai.BidRequest.Imp
	(*BidRequest_AdslotSize)(nil),       // 3: wangmai.BidRequest.AdslotSize
	(*BidRequest_App)(nil),              // 4: wangmai.BidRequest.App
	(*BidRequest_Device)(nil),           // 5: wangmai.BidRequest.Device
	(*BidRequest_Deal)(nil),             // 6: wangmai.BidRequest.Deal
	(*BidRequest_Geo)(nil),              // 7: wangmai.BidRequest.Geo
	(*BidRequest_User)(nil),             // 8: wangmai.BidRequest.User
	(*BidResponse_SeatBid)(nil),         // 9: wangmai.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),             // 10: wangmai.BidResponse.Bid
	(*BidResponse_Item)(nil),            // 11: wangmai.BidResponse.Item
	(*BidResponse_DownloadAppInfo)(nil), // 12: wangmai.BidResponse.DownloadAppInfo
	(*BidResponse_Video)(nil),           // 13: wangmai.BidResponse.Video
}
var file_wangmai_proto_depIdxs = []int32{
	2,  // 0: wangmai.BidRequest.imp:type_name -> wangmai.BidRequest.Imp
	4,  // 1: wangmai.BidRequest.app:type_name -> wangmai.BidRequest.App
	5,  // 2: wangmai.BidRequest.device:type_name -> wangmai.BidRequest.Device
	8,  // 3: wangmai.BidRequest.user:type_name -> wangmai.BidRequest.User
	9,  // 4: wangmai.BidResponse.seatbid:type_name -> wangmai.BidResponse.SeatBid
	3,  // 5: wangmai.BidRequest.Imp.adslotSize:type_name -> wangmai.BidRequest.AdslotSize
	6,  // 6: wangmai.BidRequest.Imp.deals:type_name -> wangmai.BidRequest.Deal
	7,  // 7: wangmai.BidRequest.Device.geo:type_name -> wangmai.BidRequest.Geo
	10, // 8: wangmai.BidResponse.SeatBid.bid:type_name -> wangmai.BidResponse.Bid
	11, // 9: wangmai.BidResponse.Bid.items:type_name -> wangmai.BidResponse.Item
	12, // 10: wangmai.BidResponse.Item.downloadAppInfo:type_name -> wangmai.BidResponse.DownloadAppInfo
	13, // 11: wangmai.BidResponse.Item.video:type_name -> wangmai.BidResponse.Video
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_wangmai_proto_init() }
func file_wangmai_proto_init() {
	if File_wangmai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wangmai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdslotSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_DownloadAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wangmai_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wangmai_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wangmai_proto_goTypes,
		DependencyIndexes: file_wangmai_proto_depIdxs,
		MessageInfos:      file_wangmai_proto_msgTypes,
	}.Build()
	File_wangmai_proto = out.File
	file_wangmai_proto_rawDesc = nil
	file_wangmai_proto_goTypes = nil
	file_wangmai_proto_depIdxs = nil
}
