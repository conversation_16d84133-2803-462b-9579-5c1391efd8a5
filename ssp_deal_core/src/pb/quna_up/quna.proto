syntax = "proto3";

package proto10;

option go_package = "mh_proxy/pb/quna_up";

message Request {
  //请求ID，全局唯一
  string id = 1;
  //协议版本号，填写1.1
  string version = 2;

  message Imp {
    //展示ID，确保全局唯一
    string id = 1;
    //广告位ID
    string tagid = 2;
    //竞价类的底价
    double bidfloor = 3;

    message Banner {
      uint32 w = 1;
      uint32 h = 2;
      uint32 pos = 3;
    }

    message NativeAsset {
      uint32 id = 1;
      uint32 isrequired = 2;

      message NaTitle {
        uint32 len = 1;
      }
      message NaImg {
        uint32 type = 1;
        uint32 wmin = 2;
        uint32 hmin = 3;
        repeated string mimes = 4;
      }
      message NaVideo {
        uint32 wmin = 1;
        uint32 hmin = 2;
        repeated string mimes = 3;
      }
      message NaData {
        uint32 type = 1;
        uint32 len = 2;
      }

      NaTitle title = 3;
      NaImg img = 4;
      NaVideo video = 5;
      NaData data = 6;
    }

    message Native {
      repeated NativeAsset assets = 1;
      uint32 layout = 2;
    }

    message Video {
      uint32 w = 1;
      uint32 h = 2;
      uint32 type = 3;
      uint32 minduration = 4;
      uint32 maxduration = 5;
      uint32 startdelay = 6;
      repeated string mimes = 7;
      uint32 orientation = 8;
      uint32 delivery = 9;
    }

    Banner banner = 4;
    Native native = 5;
    Video video = 6;

    bool isdeeplink = 8;
    bool isul = 9;
    bool isdownload = 10;
    uint32 secure = 11;

    message Deal {
      string id = 1;
      uint64 bidfloor = 2;
    }
    message Pmp {
      repeated Deal deals = 1;
    }
    Pmp pmp = 12;
  }
  repeated Imp imp = 3;

  message App {
    string bundle = 1;
    string name = 2;
    string version = 3;
    string appstoreversion = 4;
    repeated string cat = 5;
    repeated string keywords = 6;
    repeated string pagecat = 7;
  }
  App app = 4;

  message Site {
    string domain = 1;
    string name = 2;
    string page = 3;
    string ref = 4;
    string keywords = 5;
    repeated string pagecat = 6;
    string search = 7;
  }
  Site site = 5;

  message Device {
    string os = 1;
    string osv = 2;
    string did = 3;
    string didmd5 = 4;
    string oid = 5;
    string oidmd5 = 6;
    string androidid = 7;
    string androididmd5 = 8;
    string ifa = 9;
    string ifamd5 = 10;
    string caid = 11;
    string caid_version = 12;
    string aaid = 13;
    string openudid = 14;
    string idfv = 15;
    string mac = 16;
    string macidmd5 = 17;
    string ip = 18;
    string ipv6 = 19;
    string ua = 20;
    enum ConnectionType {
      Unknown = 0;
      Ethernet = 1;
      WiFi = 2;
      CellularNetworkUnknown = 3;
      G2 = 4;
      G3 = 5;
      G4 = 6;
      G5 = 7;
    }
    ConnectionType connectiontype = 21;
    enum DeviceType {
      Uknown = 0;
      Mobile = 1;
      PC = 2;
      TV = 3;
      Other = 4;
    }
    DeviceType devicetype = 22;
    string make = 23;
    string model = 24;
    string brand = 25;
    string carrier = 26;
    string flashver = 27;
    uint32 screenheight = 28;
    uint32 screenwidth = 29;
    uint32 orientation = 30;
    uint32 dpi = 31;
    double density = 32;
    uint32 ppi = 33;
    message Geo {
      double lat = 1;
      double lon = 2;
      double accu = 3;
      uint32 type = 4;
      uint64 timestamp = 5;
    }
    Geo geo = 34;
    uint64 elapsetime = 35;
    string romversion = 36;
    string syscompilingtime = 37;
    string boot_mark = 38;
    string update_mark = 39;
    string ag = 40;
    string hms = 41;
    string wifi_mac = 42;
    string imsi = 43;
    string ssid = 44;
    string udid = 45;
    string paid = 46;
    string birth_time = 47;
    string start_time_msec = 48;
    string update_time_nsec = 49;
    string language = 50;
    string hardware_model = 51;
    string country = 52;
    string local_tz_time = 53;
    string device_name_md5 = 54;
    int32 cpu_num = 55;
    int64 disk_total = 56;
    int64 mem_total = 57;
    int32 auth_status = 58;
    repeated string skadnetwork_versions = 59;
    string miuiversion = 60;
    repeated string install_apps = 61;
    string pre_caid = 62;
    string pre_caid_version = 63;
  }
  Device device = 6;

  message User {
    string id = 1;
    string gender = 2;
    uint32 yob = 3;
    string keywords = 4;
  }
  User user = 7;
}


message Response {
  string id = 1;
  string bidid = 2;

  message SeatBid {
    message Bid {
      string id = 1;
      string adid = 2;
      string crid = 3;
      string impid = 4;
      double price = 5;
      string admhtmljs = 6;
      message AdmObject {
        message Native {
          message Asset {
            uint32 id = 1;
            uint32 isrequired = 2;
            message Title {
              string text = 1;
            }
            message Img {
              string url = 1;
              uint32 w = 2;
              uint32 h = 3;
              uint32 type = 4;
            }
            message Video {
              string url = 1;
              string cover = 2;
              uint32 duration = 3;
            }
            message Data {
              string label = 1;
              string value = 2;
            }
            oneof OneAsset {
              Title title = 3;
              Img img = 4;
              Video video = 5;
              Data data = 6;
            }
          }
          repeated Asset assets = 1;
        }
        message Video {
          uint32 w = 1;
          uint32 h = 2;
          uint32 type = 3;
          uint32 size = 4;
          repeated string mimes = 5;
          string url = 6;
          uint32 duration = 7;
          string cover = 8;
          uint32 skip = 9;
          uint32 skip_min_time = 10;
          uint32 pre_load_ttl = 11;
          message Card {
            uint32 type = 1;
            string url = 2;
            string html = 3;
            string charset = 4;
            string icon = 5;
            string title = 6;
            string content = 7;
            uint32 comments = 8;
            uint32 rating = 9;
            message Button {
              string url = 1;
              string text = 2;
            }
            Button button = 10;
          }
          Card card = 12;
        }
        Native native = 1;
        Video video = 2;
      }
      AdmObject admobject = 7;
      string nurl = 8;
      string target = 9;
      string deeplink = 10;
      string universal_link = 11;
      string market_url = 12;
      string quick_app_link = 13;
      string download = 15;
      string wxappid = 16;
      string wxapppath = 17;
      string action = 18;

      message App {
        uint32 size = 1;
        string md5 = 2;
        string icon = 3;
        string package = 4;
        string version = 5;
        string name = 6;
        uint32 version_code = 7;
        string publisher = 8;
        string privacy_link = 9;
        string permission_link = 10;
        string app_desc = 11;
      }
      App app = 19;

      message Event {
        repeated string imp_urls = 1;
        repeated string click_urls = 2;
        repeated string start_dod_urls = 3;
        repeated string finish_dod_urls = 4;
        repeated string start_install_urls = 5;
        repeated string finish_install_urls = 6;
        repeated string active_urls = 7;
        repeated string start_play_urls = 8;
        repeated string start_25play_urls = 9;
        repeated string start_50play_urls = 10;
        repeated string start_75play_urls = 11;
        repeated string pause_play_urls = 12;
        repeated string replay_urls = 13;
        repeated string finish_play_urls = 14;
        repeated string deeplink_urls = 15;
        repeated string deeplink_furls = 16;
        repeated string step_play_urls = 17;
        repeated string mute_play_urls = 18;
        repeated string skip_play_urls = 19;
        repeated string close_play_urls = 20;
      }
      Event events = 20;

      message Ext {
        string ua = 1;
        string referer = 2;
        int32 click_delay = 3;
        int32 do_click = 4;
        int32 do_land = 5;
        int32 land_stay_time = 6;

        message Delay {
          string url = 1;
          int32 delay = 2;
        }
        Delay delay_monitor = 7;
      }
      Ext ext = 21;
    }

    repeated Bid bids = 1;
  }
  SeatBid seatbid = 3;
  string nbr = 4;
}