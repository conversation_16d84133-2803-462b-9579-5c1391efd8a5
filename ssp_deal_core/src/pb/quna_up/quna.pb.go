// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: quna.proto

package quna_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request_Device_ConnectionType int32

const (
	Request_Device_Unknown                Request_Device_ConnectionType = 0
	Request_Device_Ethernet               Request_Device_ConnectionType = 1
	Request_Device_WiFi                   Request_Device_ConnectionType = 2
	Request_Device_CellularNetworkUnknown Request_Device_ConnectionType = 3
	Request_Device_G2                     Request_Device_ConnectionType = 4
	Request_Device_G3                     Request_Device_ConnectionType = 5
	Request_Device_G4                     Request_Device_ConnectionType = 6
	Request_Device_G5                     Request_Device_ConnectionType = 7
)

// Enum value maps for Request_Device_ConnectionType.
var (
	Request_Device_ConnectionType_name = map[int32]string{
		0: "Unknown",
		1: "Ethernet",
		2: "WiFi",
		3: "CellularNetworkUnknown",
		4: "G2",
		5: "G3",
		6: "G4",
		7: "G5",
	}
	Request_Device_ConnectionType_value = map[string]int32{
		"Unknown":                0,
		"Ethernet":               1,
		"WiFi":                   2,
		"CellularNetworkUnknown": 3,
		"G2":                     4,
		"G3":                     5,
		"G4":                     6,
		"G5":                     7,
	}
)

func (x Request_Device_ConnectionType) Enum() *Request_Device_ConnectionType {
	p := new(Request_Device_ConnectionType)
	*p = x
	return p
}

func (x Request_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Request_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_quna_proto_enumTypes[0].Descriptor()
}

func (Request_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_quna_proto_enumTypes[0]
}

func (x Request_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Request_Device_ConnectionType.Descriptor instead.
func (Request_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 3, 0}
}

type Request_Device_DeviceType int32

const (
	Request_Device_Uknown Request_Device_DeviceType = 0
	Request_Device_Mobile Request_Device_DeviceType = 1
	Request_Device_PC     Request_Device_DeviceType = 2
	Request_Device_TV     Request_Device_DeviceType = 3
	Request_Device_Other  Request_Device_DeviceType = 4
)

// Enum value maps for Request_Device_DeviceType.
var (
	Request_Device_DeviceType_name = map[int32]string{
		0: "Uknown",
		1: "Mobile",
		2: "PC",
		3: "TV",
		4: "Other",
	}
	Request_Device_DeviceType_value = map[string]int32{
		"Uknown": 0,
		"Mobile": 1,
		"PC":     2,
		"TV":     3,
		"Other":  4,
	}
)

func (x Request_Device_DeviceType) Enum() *Request_Device_DeviceType {
	p := new(Request_Device_DeviceType)
	*p = x
	return p
}

func (x Request_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Request_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_quna_proto_enumTypes[1].Descriptor()
}

func (Request_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_quna_proto_enumTypes[1]
}

func (x Request_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Request_Device_DeviceType.Descriptor instead.
func (Request_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 3, 1}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求ID，全局唯一
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 协议版本号，填写1.1
	Version string          `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Imp     []*Request_Imp  `protobuf:"bytes,3,rep,name=imp,proto3" json:"imp,omitempty"`
	App     *Request_App    `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`
	Site    *Request_Site   `protobuf:"bytes,5,opt,name=site,proto3" json:"site,omitempty"`
	Device  *Request_Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	User    *Request_User   `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Request) GetImp() []*Request_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *Request) GetApp() *Request_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Request) GetSite() *Request_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *Request) GetDevice() *Request_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Request) GetUser() *Request_User {
	if x != nil {
		return x.User
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Bidid   string            `protobuf:"bytes,2,opt,name=bidid,proto3" json:"bidid,omitempty"`
	Seatbid *Response_SeatBid `protobuf:"bytes,3,opt,name=seatbid,proto3" json:"seatbid,omitempty"`
	Nbr     string            `protobuf:"bytes,4,opt,name=nbr,proto3" json:"nbr,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Response) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

func (x *Response) GetSeatbid() *Response_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *Response) GetNbr() string {
	if x != nil {
		return x.Nbr
	}
	return ""
}

type Request_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 展示ID，确保全局唯一
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 广告位ID
	Tagid string `protobuf:"bytes,2,opt,name=tagid,proto3" json:"tagid,omitempty"`
	// 竞价类的底价
	Bidfloor   float64             `protobuf:"fixed64,3,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Banner     *Request_Imp_Banner `protobuf:"bytes,4,opt,name=banner,proto3" json:"banner,omitempty"`
	Native     *Request_Imp_Native `protobuf:"bytes,5,opt,name=native,proto3" json:"native,omitempty"`
	Video      *Request_Imp_Video  `protobuf:"bytes,6,opt,name=video,proto3" json:"video,omitempty"`
	Isdeeplink bool                `protobuf:"varint,8,opt,name=isdeeplink,proto3" json:"isdeeplink,omitempty"`
	Isul       bool                `protobuf:"varint,9,opt,name=isul,proto3" json:"isul,omitempty"`
	Isdownload bool                `protobuf:"varint,10,opt,name=isdownload,proto3" json:"isdownload,omitempty"`
	Secure     uint32              `protobuf:"varint,11,opt,name=secure,proto3" json:"secure,omitempty"`
	Pmp        *Request_Imp_Pmp    `protobuf:"bytes,12,opt,name=pmp,proto3" json:"pmp,omitempty"`
}

func (x *Request_Imp) Reset() {
	*x = Request_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp) ProtoMessage() {}

func (x *Request_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp.ProtoReflect.Descriptor instead.
func (*Request_Imp) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Request_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Imp) GetTagid() string {
	if x != nil {
		return x.Tagid
	}
	return ""
}

func (x *Request_Imp) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *Request_Imp) GetBanner() *Request_Imp_Banner {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *Request_Imp) GetNative() *Request_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *Request_Imp) GetVideo() *Request_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Request_Imp) GetIsdeeplink() bool {
	if x != nil {
		return x.Isdeeplink
	}
	return false
}

func (x *Request_Imp) GetIsul() bool {
	if x != nil {
		return x.Isul
	}
	return false
}

func (x *Request_Imp) GetIsdownload() bool {
	if x != nil {
		return x.Isdownload
	}
	return false
}

func (x *Request_Imp) GetSecure() uint32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *Request_Imp) GetPmp() *Request_Imp_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

type Request_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bundle          string   `protobuf:"bytes,1,opt,name=bundle,proto3" json:"bundle,omitempty"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version         string   `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Appstoreversion string   `protobuf:"bytes,4,opt,name=appstoreversion,proto3" json:"appstoreversion,omitempty"`
	Cat             []string `protobuf:"bytes,5,rep,name=cat,proto3" json:"cat,omitempty"`
	Keywords        []string `protobuf:"bytes,6,rep,name=keywords,proto3" json:"keywords,omitempty"`
	Pagecat         []string `protobuf:"bytes,7,rep,name=pagecat,proto3" json:"pagecat,omitempty"`
}

func (x *Request_App) Reset() {
	*x = Request_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_App) ProtoMessage() {}

func (x *Request_App) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_App.ProtoReflect.Descriptor instead.
func (*Request_App) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Request_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *Request_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Request_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Request_App) GetAppstoreversion() string {
	if x != nil {
		return x.Appstoreversion
	}
	return ""
}

func (x *Request_App) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *Request_App) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *Request_App) GetPagecat() []string {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

type Request_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Name     string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Page     string   `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	Ref      string   `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	Keywords string   `protobuf:"bytes,5,opt,name=keywords,proto3" json:"keywords,omitempty"`
	Pagecat  []string `protobuf:"bytes,6,rep,name=pagecat,proto3" json:"pagecat,omitempty"`
	Search   string   `protobuf:"bytes,7,opt,name=search,proto3" json:"search,omitempty"`
}

func (x *Request_Site) Reset() {
	*x = Request_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Site) ProtoMessage() {}

func (x *Request_Site) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Site.ProtoReflect.Descriptor instead.
func (*Request_Site) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Request_Site) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *Request_Site) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Request_Site) GetPage() string {
	if x != nil {
		return x.Page
	}
	return ""
}

func (x *Request_Site) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *Request_Site) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *Request_Site) GetPagecat() []string {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

func (x *Request_Site) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

type Request_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os                  string                        `protobuf:"bytes,1,opt,name=os,proto3" json:"os,omitempty"`
	Osv                 string                        `protobuf:"bytes,2,opt,name=osv,proto3" json:"osv,omitempty"`
	Did                 string                        `protobuf:"bytes,3,opt,name=did,proto3" json:"did,omitempty"`
	Didmd5              string                        `protobuf:"bytes,4,opt,name=didmd5,proto3" json:"didmd5,omitempty"`
	Oid                 string                        `protobuf:"bytes,5,opt,name=oid,proto3" json:"oid,omitempty"`
	Oidmd5              string                        `protobuf:"bytes,6,opt,name=oidmd5,proto3" json:"oidmd5,omitempty"`
	Androidid           string                        `protobuf:"bytes,7,opt,name=androidid,proto3" json:"androidid,omitempty"`
	Androididmd5        string                        `protobuf:"bytes,8,opt,name=androididmd5,proto3" json:"androididmd5,omitempty"`
	Ifa                 string                        `protobuf:"bytes,9,opt,name=ifa,proto3" json:"ifa,omitempty"`
	Ifamd5              string                        `protobuf:"bytes,10,opt,name=ifamd5,proto3" json:"ifamd5,omitempty"`
	Caid                string                        `protobuf:"bytes,11,opt,name=caid,proto3" json:"caid,omitempty"`
	CaidVersion         string                        `protobuf:"bytes,12,opt,name=caid_version,json=caidVersion,proto3" json:"caid_version,omitempty"`
	Aaid                string                        `protobuf:"bytes,13,opt,name=aaid,proto3" json:"aaid,omitempty"`
	Openudid            string                        `protobuf:"bytes,14,opt,name=openudid,proto3" json:"openudid,omitempty"`
	Idfv                string                        `protobuf:"bytes,15,opt,name=idfv,proto3" json:"idfv,omitempty"`
	Mac                 string                        `protobuf:"bytes,16,opt,name=mac,proto3" json:"mac,omitempty"`
	Macidmd5            string                        `protobuf:"bytes,17,opt,name=macidmd5,proto3" json:"macidmd5,omitempty"`
	Ip                  string                        `protobuf:"bytes,18,opt,name=ip,proto3" json:"ip,omitempty"`
	Ipv6                string                        `protobuf:"bytes,19,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	Ua                  string                        `protobuf:"bytes,20,opt,name=ua,proto3" json:"ua,omitempty"`
	Connectiontype      Request_Device_ConnectionType `protobuf:"varint,21,opt,name=connectiontype,proto3,enum=proto10.Request_Device_ConnectionType" json:"connectiontype,omitempty"`
	Devicetype          Request_Device_DeviceType     `protobuf:"varint,22,opt,name=devicetype,proto3,enum=proto10.Request_Device_DeviceType" json:"devicetype,omitempty"`
	Make                string                        `protobuf:"bytes,23,opt,name=make,proto3" json:"make,omitempty"`
	Model               string                        `protobuf:"bytes,24,opt,name=model,proto3" json:"model,omitempty"`
	Brand               string                        `protobuf:"bytes,25,opt,name=brand,proto3" json:"brand,omitempty"`
	Carrier             string                        `protobuf:"bytes,26,opt,name=carrier,proto3" json:"carrier,omitempty"`
	Flashver            string                        `protobuf:"bytes,27,opt,name=flashver,proto3" json:"flashver,omitempty"`
	Screenheight        uint32                        `protobuf:"varint,28,opt,name=screenheight,proto3" json:"screenheight,omitempty"`
	Screenwidth         uint32                        `protobuf:"varint,29,opt,name=screenwidth,proto3" json:"screenwidth,omitempty"`
	Orientation         uint32                        `protobuf:"varint,30,opt,name=orientation,proto3" json:"orientation,omitempty"`
	Dpi                 uint32                        `protobuf:"varint,31,opt,name=dpi,proto3" json:"dpi,omitempty"`
	Density             float64                       `protobuf:"fixed64,32,opt,name=density,proto3" json:"density,omitempty"`
	Ppi                 uint32                        `protobuf:"varint,33,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo                 *Request_Device_Geo           `protobuf:"bytes,34,opt,name=geo,proto3" json:"geo,omitempty"`
	Elapsetime          uint64                        `protobuf:"varint,35,opt,name=elapsetime,proto3" json:"elapsetime,omitempty"`
	Romversion          string                        `protobuf:"bytes,36,opt,name=romversion,proto3" json:"romversion,omitempty"`
	Syscompilingtime    string                        `protobuf:"bytes,37,opt,name=syscompilingtime,proto3" json:"syscompilingtime,omitempty"`
	BootMark            string                        `protobuf:"bytes,38,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	UpdateMark          string                        `protobuf:"bytes,39,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	Ag                  string                        `protobuf:"bytes,40,opt,name=ag,proto3" json:"ag,omitempty"`
	Hms                 string                        `protobuf:"bytes,41,opt,name=hms,proto3" json:"hms,omitempty"`
	WifiMac             string                        `protobuf:"bytes,42,opt,name=wifi_mac,json=wifiMac,proto3" json:"wifi_mac,omitempty"`
	Imsi                string                        `protobuf:"bytes,43,opt,name=imsi,proto3" json:"imsi,omitempty"`
	Ssid                string                        `protobuf:"bytes,44,opt,name=ssid,proto3" json:"ssid,omitempty"`
	Udid                string                        `protobuf:"bytes,45,opt,name=udid,proto3" json:"udid,omitempty"`
	Paid                string                        `protobuf:"bytes,46,opt,name=paid,proto3" json:"paid,omitempty"`
	BirthTime           string                        `protobuf:"bytes,47,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`
	StartTimeMsec       string                        `protobuf:"bytes,48,opt,name=start_time_msec,json=startTimeMsec,proto3" json:"start_time_msec,omitempty"`
	UpdateTimeNsec      string                        `protobuf:"bytes,49,opt,name=update_time_nsec,json=updateTimeNsec,proto3" json:"update_time_nsec,omitempty"`
	Language            string                        `protobuf:"bytes,50,opt,name=language,proto3" json:"language,omitempty"`
	HardwareModel       string                        `protobuf:"bytes,51,opt,name=hardware_model,json=hardwareModel,proto3" json:"hardware_model,omitempty"`
	Country             string                        `protobuf:"bytes,52,opt,name=country,proto3" json:"country,omitempty"`
	LocalTzTime         string                        `protobuf:"bytes,53,opt,name=local_tz_time,json=localTzTime,proto3" json:"local_tz_time,omitempty"`
	DeviceNameMd5       string                        `protobuf:"bytes,54,opt,name=device_name_md5,json=deviceNameMd5,proto3" json:"device_name_md5,omitempty"`
	CpuNum              int32                         `protobuf:"varint,55,opt,name=cpu_num,json=cpuNum,proto3" json:"cpu_num,omitempty"`
	DiskTotal           int64                         `protobuf:"varint,56,opt,name=disk_total,json=diskTotal,proto3" json:"disk_total,omitempty"`
	MemTotal            int64                         `protobuf:"varint,57,opt,name=mem_total,json=memTotal,proto3" json:"mem_total,omitempty"`
	AuthStatus          int32                         `protobuf:"varint,58,opt,name=auth_status,json=authStatus,proto3" json:"auth_status,omitempty"`
	SkadnetworkVersions []string                      `protobuf:"bytes,59,rep,name=skadnetwork_versions,json=skadnetworkVersions,proto3" json:"skadnetwork_versions,omitempty"`
	Miuiversion         string                        `protobuf:"bytes,60,opt,name=miuiversion,proto3" json:"miuiversion,omitempty"`
	InstallApps         []string                      `protobuf:"bytes,61,rep,name=install_apps,json=installApps,proto3" json:"install_apps,omitempty"`
	PreCaid             string                        `protobuf:"bytes,62,opt,name=pre_caid,json=preCaid,proto3" json:"pre_caid,omitempty"`
	PreCaidVersion      string                        `protobuf:"bytes,63,opt,name=pre_caid_version,json=preCaidVersion,proto3" json:"pre_caid_version,omitempty"`
}

func (x *Request_Device) Reset() {
	*x = Request_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device) ProtoMessage() {}

func (x *Request_Device) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device.ProtoReflect.Descriptor instead.
func (*Request_Device) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Request_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *Request_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *Request_Device) GetDid() string {
	if x != nil {
		return x.Did
	}
	return ""
}

func (x *Request_Device) GetDidmd5() string {
	if x != nil {
		return x.Didmd5
	}
	return ""
}

func (x *Request_Device) GetOid() string {
	if x != nil {
		return x.Oid
	}
	return ""
}

func (x *Request_Device) GetOidmd5() string {
	if x != nil {
		return x.Oidmd5
	}
	return ""
}

func (x *Request_Device) GetAndroidid() string {
	if x != nil {
		return x.Androidid
	}
	return ""
}

func (x *Request_Device) GetAndroididmd5() string {
	if x != nil {
		return x.Androididmd5
	}
	return ""
}

func (x *Request_Device) GetIfa() string {
	if x != nil {
		return x.Ifa
	}
	return ""
}

func (x *Request_Device) GetIfamd5() string {
	if x != nil {
		return x.Ifamd5
	}
	return ""
}

func (x *Request_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *Request_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *Request_Device) GetAaid() string {
	if x != nil {
		return x.Aaid
	}
	return ""
}

func (x *Request_Device) GetOpenudid() string {
	if x != nil {
		return x.Openudid
	}
	return ""
}

func (x *Request_Device) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *Request_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *Request_Device) GetMacidmd5() string {
	if x != nil {
		return x.Macidmd5
	}
	return ""
}

func (x *Request_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Request_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *Request_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *Request_Device) GetConnectiontype() Request_Device_ConnectionType {
	if x != nil {
		return x.Connectiontype
	}
	return Request_Device_Unknown
}

func (x *Request_Device) GetDevicetype() Request_Device_DeviceType {
	if x != nil {
		return x.Devicetype
	}
	return Request_Device_Uknown
}

func (x *Request_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *Request_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Request_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Request_Device) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *Request_Device) GetFlashver() string {
	if x != nil {
		return x.Flashver
	}
	return ""
}

func (x *Request_Device) GetScreenheight() uint32 {
	if x != nil {
		return x.Screenheight
	}
	return 0
}

func (x *Request_Device) GetScreenwidth() uint32 {
	if x != nil {
		return x.Screenwidth
	}
	return 0
}

func (x *Request_Device) GetOrientation() uint32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *Request_Device) GetDpi() uint32 {
	if x != nil {
		return x.Dpi
	}
	return 0
}

func (x *Request_Device) GetDensity() float64 {
	if x != nil {
		return x.Density
	}
	return 0
}

func (x *Request_Device) GetPpi() uint32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *Request_Device) GetGeo() *Request_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Request_Device) GetElapsetime() uint64 {
	if x != nil {
		return x.Elapsetime
	}
	return 0
}

func (x *Request_Device) GetRomversion() string {
	if x != nil {
		return x.Romversion
	}
	return ""
}

func (x *Request_Device) GetSyscompilingtime() string {
	if x != nil {
		return x.Syscompilingtime
	}
	return ""
}

func (x *Request_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *Request_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *Request_Device) GetAg() string {
	if x != nil {
		return x.Ag
	}
	return ""
}

func (x *Request_Device) GetHms() string {
	if x != nil {
		return x.Hms
	}
	return ""
}

func (x *Request_Device) GetWifiMac() string {
	if x != nil {
		return x.WifiMac
	}
	return ""
}

func (x *Request_Device) GetImsi() string {
	if x != nil {
		return x.Imsi
	}
	return ""
}

func (x *Request_Device) GetSsid() string {
	if x != nil {
		return x.Ssid
	}
	return ""
}

func (x *Request_Device) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *Request_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *Request_Device) GetBirthTime() string {
	if x != nil {
		return x.BirthTime
	}
	return ""
}

func (x *Request_Device) GetStartTimeMsec() string {
	if x != nil {
		return x.StartTimeMsec
	}
	return ""
}

func (x *Request_Device) GetUpdateTimeNsec() string {
	if x != nil {
		return x.UpdateTimeNsec
	}
	return ""
}

func (x *Request_Device) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Request_Device) GetHardwareModel() string {
	if x != nil {
		return x.HardwareModel
	}
	return ""
}

func (x *Request_Device) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Request_Device) GetLocalTzTime() string {
	if x != nil {
		return x.LocalTzTime
	}
	return ""
}

func (x *Request_Device) GetDeviceNameMd5() string {
	if x != nil {
		return x.DeviceNameMd5
	}
	return ""
}

func (x *Request_Device) GetCpuNum() int32 {
	if x != nil {
		return x.CpuNum
	}
	return 0
}

func (x *Request_Device) GetDiskTotal() int64 {
	if x != nil {
		return x.DiskTotal
	}
	return 0
}

func (x *Request_Device) GetMemTotal() int64 {
	if x != nil {
		return x.MemTotal
	}
	return 0
}

func (x *Request_Device) GetAuthStatus() int32 {
	if x != nil {
		return x.AuthStatus
	}
	return 0
}

func (x *Request_Device) GetSkadnetworkVersions() []string {
	if x != nil {
		return x.SkadnetworkVersions
	}
	return nil
}

func (x *Request_Device) GetMiuiversion() string {
	if x != nil {
		return x.Miuiversion
	}
	return ""
}

func (x *Request_Device) GetInstallApps() []string {
	if x != nil {
		return x.InstallApps
	}
	return nil
}

func (x *Request_Device) GetPreCaid() string {
	if x != nil {
		return x.PreCaid
	}
	return ""
}

func (x *Request_Device) GetPreCaidVersion() string {
	if x != nil {
		return x.PreCaidVersion
	}
	return ""
}

type Request_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Gender   string `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Yob      uint32 `protobuf:"varint,3,opt,name=yob,proto3" json:"yob,omitempty"`
	Keywords string `protobuf:"bytes,4,opt,name=keywords,proto3" json:"keywords,omitempty"`
}

func (x *Request_User) Reset() {
	*x = Request_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_User) ProtoMessage() {}

func (x *Request_User) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_User.ProtoReflect.Descriptor instead.
func (*Request_User) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Request_User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_User) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Request_User) GetYob() uint32 {
	if x != nil {
		return x.Yob
	}
	return 0
}

func (x *Request_User) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

type Request_Imp_Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	W   uint32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	H   uint32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	Pos uint32 `protobuf:"varint,3,opt,name=pos,proto3" json:"pos,omitempty"`
}

func (x *Request_Imp_Banner) Reset() {
	*x = Request_Imp_Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Banner) ProtoMessage() {}

func (x *Request_Imp_Banner) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Banner.ProtoReflect.Descriptor instead.
func (*Request_Imp_Banner) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *Request_Imp_Banner) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *Request_Imp_Banner) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *Request_Imp_Banner) GetPos() uint32 {
	if x != nil {
		return x.Pos
	}
	return 0
}

type Request_Imp_NativeAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32                           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Isrequired uint32                           `protobuf:"varint,2,opt,name=isrequired,proto3" json:"isrequired,omitempty"`
	Title      *Request_Imp_NativeAsset_NaTitle `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Img        *Request_Imp_NativeAsset_NaImg   `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	Video      *Request_Imp_NativeAsset_NaVideo `protobuf:"bytes,5,opt,name=video,proto3" json:"video,omitempty"`
	Data       *Request_Imp_NativeAsset_NaData  `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Request_Imp_NativeAsset) Reset() {
	*x = Request_Imp_NativeAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_NativeAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_NativeAsset) ProtoMessage() {}

func (x *Request_Imp_NativeAsset) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_NativeAsset.ProtoReflect.Descriptor instead.
func (*Request_Imp_NativeAsset) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *Request_Imp_NativeAsset) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Request_Imp_NativeAsset) GetIsrequired() uint32 {
	if x != nil {
		return x.Isrequired
	}
	return 0
}

func (x *Request_Imp_NativeAsset) GetTitle() *Request_Imp_NativeAsset_NaTitle {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *Request_Imp_NativeAsset) GetImg() *Request_Imp_NativeAsset_NaImg {
	if x != nil {
		return x.Img
	}
	return nil
}

func (x *Request_Imp_NativeAsset) GetVideo() *Request_Imp_NativeAsset_NaVideo {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *Request_Imp_NativeAsset) GetData() *Request_Imp_NativeAsset_NaData {
	if x != nil {
		return x.Data
	}
	return nil
}

type Request_Imp_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*Request_Imp_NativeAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	Layout uint32                     `protobuf:"varint,2,opt,name=layout,proto3" json:"layout,omitempty"`
}

func (x *Request_Imp_Native) Reset() {
	*x = Request_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Native) ProtoMessage() {}

func (x *Request_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Native.ProtoReflect.Descriptor instead.
func (*Request_Imp_Native) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *Request_Imp_Native) GetAssets() []*Request_Imp_NativeAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *Request_Imp_Native) GetLayout() uint32 {
	if x != nil {
		return x.Layout
	}
	return 0
}

type Request_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	W           uint32   `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	H           uint32   `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	Type        uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Minduration uint32   `protobuf:"varint,4,opt,name=minduration,proto3" json:"minduration,omitempty"`
	Maxduration uint32   `protobuf:"varint,5,opt,name=maxduration,proto3" json:"maxduration,omitempty"`
	Startdelay  uint32   `protobuf:"varint,6,opt,name=startdelay,proto3" json:"startdelay,omitempty"`
	Mimes       []string `protobuf:"bytes,7,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Orientation uint32   `protobuf:"varint,8,opt,name=orientation,proto3" json:"orientation,omitempty"`
	Delivery    uint32   `protobuf:"varint,9,opt,name=delivery,proto3" json:"delivery,omitempty"`
}

func (x *Request_Imp_Video) Reset() {
	*x = Request_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Video) ProtoMessage() {}

func (x *Request_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Video.ProtoReflect.Descriptor instead.
func (*Request_Imp_Video) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 3}
}

func (x *Request_Imp_Video) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *Request_Imp_Video) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *Request_Imp_Video) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Request_Imp_Video) GetMinduration() uint32 {
	if x != nil {
		return x.Minduration
	}
	return 0
}

func (x *Request_Imp_Video) GetMaxduration() uint32 {
	if x != nil {
		return x.Maxduration
	}
	return 0
}

func (x *Request_Imp_Video) GetStartdelay() uint32 {
	if x != nil {
		return x.Startdelay
	}
	return 0
}

func (x *Request_Imp_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *Request_Imp_Video) GetOrientation() uint32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *Request_Imp_Video) GetDelivery() uint32 {
	if x != nil {
		return x.Delivery
	}
	return 0
}

type Request_Imp_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Bidfloor uint64 `protobuf:"varint,2,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
}

func (x *Request_Imp_Deal) Reset() {
	*x = Request_Imp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Deal) ProtoMessage() {}

func (x *Request_Imp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Deal.ProtoReflect.Descriptor instead.
func (*Request_Imp_Deal) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 4}
}

func (x *Request_Imp_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Imp_Deal) GetBidfloor() uint64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

type Request_Imp_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deals []*Request_Imp_Deal `protobuf:"bytes,1,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *Request_Imp_Pmp) Reset() {
	*x = Request_Imp_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Pmp) ProtoMessage() {}

func (x *Request_Imp_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Pmp.ProtoReflect.Descriptor instead.
func (*Request_Imp_Pmp) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 5}
}

func (x *Request_Imp_Pmp) GetDeals() []*Request_Imp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type Request_Imp_NativeAsset_NaTitle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Len uint32 `protobuf:"varint,1,opt,name=len,proto3" json:"len,omitempty"`
}

func (x *Request_Imp_NativeAsset_NaTitle) Reset() {
	*x = Request_Imp_NativeAsset_NaTitle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_NativeAsset_NaTitle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_NativeAsset_NaTitle) ProtoMessage() {}

func (x *Request_Imp_NativeAsset_NaTitle) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_NativeAsset_NaTitle.ProtoReflect.Descriptor instead.
func (*Request_Imp_NativeAsset_NaTitle) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 1, 0}
}

func (x *Request_Imp_NativeAsset_NaTitle) GetLen() uint32 {
	if x != nil {
		return x.Len
	}
	return 0
}

type Request_Imp_NativeAsset_NaImg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Wmin  uint32   `protobuf:"varint,2,opt,name=wmin,proto3" json:"wmin,omitempty"`
	Hmin  uint32   `protobuf:"varint,3,opt,name=hmin,proto3" json:"hmin,omitempty"`
	Mimes []string `protobuf:"bytes,4,rep,name=mimes,proto3" json:"mimes,omitempty"`
}

func (x *Request_Imp_NativeAsset_NaImg) Reset() {
	*x = Request_Imp_NativeAsset_NaImg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_NativeAsset_NaImg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_NativeAsset_NaImg) ProtoMessage() {}

func (x *Request_Imp_NativeAsset_NaImg) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_NativeAsset_NaImg.ProtoReflect.Descriptor instead.
func (*Request_Imp_NativeAsset_NaImg) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 1, 1}
}

func (x *Request_Imp_NativeAsset_NaImg) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaImg) GetWmin() uint32 {
	if x != nil {
		return x.Wmin
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaImg) GetHmin() uint32 {
	if x != nil {
		return x.Hmin
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaImg) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

type Request_Imp_NativeAsset_NaVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wmin  uint32   `protobuf:"varint,1,opt,name=wmin,proto3" json:"wmin,omitempty"`
	Hmin  uint32   `protobuf:"varint,2,opt,name=hmin,proto3" json:"hmin,omitempty"`
	Mimes []string `protobuf:"bytes,3,rep,name=mimes,proto3" json:"mimes,omitempty"`
}

func (x *Request_Imp_NativeAsset_NaVideo) Reset() {
	*x = Request_Imp_NativeAsset_NaVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_NativeAsset_NaVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_NativeAsset_NaVideo) ProtoMessage() {}

func (x *Request_Imp_NativeAsset_NaVideo) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_NativeAsset_NaVideo.ProtoReflect.Descriptor instead.
func (*Request_Imp_NativeAsset_NaVideo) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 1, 2}
}

func (x *Request_Imp_NativeAsset_NaVideo) GetWmin() uint32 {
	if x != nil {
		return x.Wmin
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaVideo) GetHmin() uint32 {
	if x != nil {
		return x.Hmin
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaVideo) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

type Request_Imp_NativeAsset_NaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Len  uint32 `protobuf:"varint,2,opt,name=len,proto3" json:"len,omitempty"`
}

func (x *Request_Imp_NativeAsset_NaData) Reset() {
	*x = Request_Imp_NativeAsset_NaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_NativeAsset_NaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_NativeAsset_NaData) ProtoMessage() {}

func (x *Request_Imp_NativeAsset_NaData) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_NativeAsset_NaData.ProtoReflect.Descriptor instead.
func (*Request_Imp_NativeAsset_NaData) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 0, 1, 3}
}

func (x *Request_Imp_NativeAsset_NaData) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Request_Imp_NativeAsset_NaData) GetLen() uint32 {
	if x != nil {
		return x.Len
	}
	return 0
}

type Request_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat       float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon       float64 `protobuf:"fixed64,2,opt,name=lon,proto3" json:"lon,omitempty"`
	Accu      float64 `protobuf:"fixed64,3,opt,name=accu,proto3" json:"accu,omitempty"`
	Type      uint32  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	Timestamp uint64  `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *Request_Device_Geo) Reset() {
	*x = Request_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_Geo) ProtoMessage() {}

func (x *Request_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_Geo.ProtoReflect.Descriptor instead.
func (*Request_Device_Geo) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *Request_Device_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Request_Device_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *Request_Device_Geo) GetAccu() float64 {
	if x != nil {
		return x.Accu
	}
	return 0
}

func (x *Request_Device_Geo) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Request_Device_Geo) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type Response_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bids []*Response_SeatBid_Bid `protobuf:"bytes,1,rep,name=bids,proto3" json:"bids,omitempty"`
}

func (x *Response_SeatBid) Reset() {
	*x = Response_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid) ProtoMessage() {}

func (x *Response_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid.ProtoReflect.Descriptor instead.
func (*Response_SeatBid) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Response_SeatBid) GetBids() []*Response_SeatBid_Bid {
	if x != nil {
		return x.Bids
	}
	return nil
}

type Response_SeatBid_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string                          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Adid          string                          `protobuf:"bytes,2,opt,name=adid,proto3" json:"adid,omitempty"`
	Crid          string                          `protobuf:"bytes,3,opt,name=crid,proto3" json:"crid,omitempty"`
	Impid         string                          `protobuf:"bytes,4,opt,name=impid,proto3" json:"impid,omitempty"`
	Price         float64                         `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	Admhtmljs     string                          `protobuf:"bytes,6,opt,name=admhtmljs,proto3" json:"admhtmljs,omitempty"`
	Admobject     *Response_SeatBid_Bid_AdmObject `protobuf:"bytes,7,opt,name=admobject,proto3" json:"admobject,omitempty"`
	Nurl          string                          `protobuf:"bytes,8,opt,name=nurl,proto3" json:"nurl,omitempty"`
	Target        string                          `protobuf:"bytes,9,opt,name=target,proto3" json:"target,omitempty"`
	Deeplink      string                          `protobuf:"bytes,10,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	UniversalLink string                          `protobuf:"bytes,11,opt,name=universal_link,json=universalLink,proto3" json:"universal_link,omitempty"`
	MarketUrl     string                          `protobuf:"bytes,12,opt,name=market_url,json=marketUrl,proto3" json:"market_url,omitempty"`
	QuickAppLink  string                          `protobuf:"bytes,13,opt,name=quick_app_link,json=quickAppLink,proto3" json:"quick_app_link,omitempty"`
	Download      string                          `protobuf:"bytes,15,opt,name=download,proto3" json:"download,omitempty"`
	Wxappid       string                          `protobuf:"bytes,16,opt,name=wxappid,proto3" json:"wxappid,omitempty"`
	Wxapppath     string                          `protobuf:"bytes,17,opt,name=wxapppath,proto3" json:"wxapppath,omitempty"`
	Action        string                          `protobuf:"bytes,18,opt,name=action,proto3" json:"action,omitempty"`
	App           *Response_SeatBid_Bid_App       `protobuf:"bytes,19,opt,name=app,proto3" json:"app,omitempty"`
	Events        *Response_SeatBid_Bid_Event     `protobuf:"bytes,20,opt,name=events,proto3" json:"events,omitempty"`
	Ext           *Response_SeatBid_Bid_Ext       `protobuf:"bytes,21,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *Response_SeatBid_Bid) Reset() {
	*x = Response_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid) ProtoMessage() {}

func (x *Response_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *Response_SeatBid_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetAdid() string {
	if x != nil {
		return x.Adid
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Response_SeatBid_Bid) GetAdmhtmljs() string {
	if x != nil {
		return x.Admhtmljs
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetAdmobject() *Response_SeatBid_Bid_AdmObject {
	if x != nil {
		return x.Admobject
	}
	return nil
}

func (x *Response_SeatBid_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetUniversalLink() string {
	if x != nil {
		return x.UniversalLink
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetMarketUrl() string {
	if x != nil {
		return x.MarketUrl
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetQuickAppLink() string {
	if x != nil {
		return x.QuickAppLink
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetDownload() string {
	if x != nil {
		return x.Download
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetWxappid() string {
	if x != nil {
		return x.Wxappid
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetWxapppath() string {
	if x != nil {
		return x.Wxapppath
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *Response_SeatBid_Bid) GetApp() *Response_SeatBid_Bid_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Response_SeatBid_Bid) GetEvents() *Response_SeatBid_Bid_Event {
	if x != nil {
		return x.Events
	}
	return nil
}

func (x *Response_SeatBid_Bid) GetExt() *Response_SeatBid_Bid_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type Response_SeatBid_Bid_AdmObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Native *Response_SeatBid_Bid_AdmObject_Native `protobuf:"bytes,1,opt,name=native,proto3" json:"native,omitempty"`
	Video  *Response_SeatBid_Bid_AdmObject_Video  `protobuf:"bytes,2,opt,name=video,proto3" json:"video,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject) Reset() {
	*x = Response_SeatBid_Bid_AdmObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *Response_SeatBid_Bid_AdmObject) GetNative() *Response_SeatBid_Bid_AdmObject_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject) GetVideo() *Response_SeatBid_Bid_AdmObject_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

type Response_SeatBid_Bid_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size           uint32 `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	Md5            string `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	Icon           string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Package        string `protobuf:"bytes,4,opt,name=package,proto3" json:"package,omitempty"`
	Version        string `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	Name           string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	VersionCode    uint32 `protobuf:"varint,7,opt,name=version_code,json=versionCode,proto3" json:"version_code,omitempty"`
	Publisher      string `protobuf:"bytes,8,opt,name=publisher,proto3" json:"publisher,omitempty"`
	PrivacyLink    string `protobuf:"bytes,9,opt,name=privacy_link,json=privacyLink,proto3" json:"privacy_link,omitempty"`
	PermissionLink string `protobuf:"bytes,10,opt,name=permission_link,json=permissionLink,proto3" json:"permission_link,omitempty"`
	AppDesc        string `protobuf:"bytes,11,opt,name=app_desc,json=appDesc,proto3" json:"app_desc,omitempty"`
}

func (x *Response_SeatBid_Bid_App) Reset() {
	*x = Response_SeatBid_Bid_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_App) ProtoMessage() {}

func (x *Response_SeatBid_Bid_App) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_App.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_App) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *Response_SeatBid_Bid_App) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Response_SeatBid_Bid_App) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetVersionCode() uint32 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *Response_SeatBid_Bid_App) GetPublisher() string {
	if x != nil {
		return x.Publisher
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetPrivacyLink() string {
	if x != nil {
		return x.PrivacyLink
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetPermissionLink() string {
	if x != nil {
		return x.PermissionLink
	}
	return ""
}

func (x *Response_SeatBid_Bid_App) GetAppDesc() string {
	if x != nil {
		return x.AppDesc
	}
	return ""
}

type Response_SeatBid_Bid_Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpUrls           []string `protobuf:"bytes,1,rep,name=imp_urls,json=impUrls,proto3" json:"imp_urls,omitempty"`
	ClickUrls         []string `protobuf:"bytes,2,rep,name=click_urls,json=clickUrls,proto3" json:"click_urls,omitempty"`
	StartDodUrls      []string `protobuf:"bytes,3,rep,name=start_dod_urls,json=startDodUrls,proto3" json:"start_dod_urls,omitempty"`
	FinishDodUrls     []string `protobuf:"bytes,4,rep,name=finish_dod_urls,json=finishDodUrls,proto3" json:"finish_dod_urls,omitempty"`
	StartInstallUrls  []string `protobuf:"bytes,5,rep,name=start_install_urls,json=startInstallUrls,proto3" json:"start_install_urls,omitempty"`
	FinishInstallUrls []string `protobuf:"bytes,6,rep,name=finish_install_urls,json=finishInstallUrls,proto3" json:"finish_install_urls,omitempty"`
	ActiveUrls        []string `protobuf:"bytes,7,rep,name=active_urls,json=activeUrls,proto3" json:"active_urls,omitempty"`
	StartPlayUrls     []string `protobuf:"bytes,8,rep,name=start_play_urls,json=startPlayUrls,proto3" json:"start_play_urls,omitempty"`
	Start_25PlayUrls  []string `protobuf:"bytes,9,rep,name=start_25play_urls,json=start25playUrls,proto3" json:"start_25play_urls,omitempty"`
	Start_50PlayUrls  []string `protobuf:"bytes,10,rep,name=start_50play_urls,json=start50playUrls,proto3" json:"start_50play_urls,omitempty"`
	Start_75PlayUrls  []string `protobuf:"bytes,11,rep,name=start_75play_urls,json=start75playUrls,proto3" json:"start_75play_urls,omitempty"`
	PausePlayUrls     []string `protobuf:"bytes,12,rep,name=pause_play_urls,json=pausePlayUrls,proto3" json:"pause_play_urls,omitempty"`
	ReplayUrls        []string `protobuf:"bytes,13,rep,name=replay_urls,json=replayUrls,proto3" json:"replay_urls,omitempty"`
	FinishPlayUrls    []string `protobuf:"bytes,14,rep,name=finish_play_urls,json=finishPlayUrls,proto3" json:"finish_play_urls,omitempty"`
	DeeplinkUrls      []string `protobuf:"bytes,15,rep,name=deeplink_urls,json=deeplinkUrls,proto3" json:"deeplink_urls,omitempty"`
	DeeplinkFurls     []string `protobuf:"bytes,16,rep,name=deeplink_furls,json=deeplinkFurls,proto3" json:"deeplink_furls,omitempty"`
	StepPlayUrls      []string `protobuf:"bytes,17,rep,name=step_play_urls,json=stepPlayUrls,proto3" json:"step_play_urls,omitempty"`
	MutePlayUrls      []string `protobuf:"bytes,18,rep,name=mute_play_urls,json=mutePlayUrls,proto3" json:"mute_play_urls,omitempty"`
	SkipPlayUrls      []string `protobuf:"bytes,19,rep,name=skip_play_urls,json=skipPlayUrls,proto3" json:"skip_play_urls,omitempty"`
	ClosePlayUrls     []string `protobuf:"bytes,20,rep,name=close_play_urls,json=closePlayUrls,proto3" json:"close_play_urls,omitempty"`
}

func (x *Response_SeatBid_Bid_Event) Reset() {
	*x = Response_SeatBid_Bid_Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_Event) ProtoMessage() {}

func (x *Response_SeatBid_Bid_Event) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_Event.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_Event) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 2}
}

func (x *Response_SeatBid_Bid_Event) GetImpUrls() []string {
	if x != nil {
		return x.ImpUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetClickUrls() []string {
	if x != nil {
		return x.ClickUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStartDodUrls() []string {
	if x != nil {
		return x.StartDodUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetFinishDodUrls() []string {
	if x != nil {
		return x.FinishDodUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStartInstallUrls() []string {
	if x != nil {
		return x.StartInstallUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetFinishInstallUrls() []string {
	if x != nil {
		return x.FinishInstallUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetActiveUrls() []string {
	if x != nil {
		return x.ActiveUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStartPlayUrls() []string {
	if x != nil {
		return x.StartPlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStart_25PlayUrls() []string {
	if x != nil {
		return x.Start_25PlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStart_50PlayUrls() []string {
	if x != nil {
		return x.Start_50PlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStart_75PlayUrls() []string {
	if x != nil {
		return x.Start_75PlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetPausePlayUrls() []string {
	if x != nil {
		return x.PausePlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetReplayUrls() []string {
	if x != nil {
		return x.ReplayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetFinishPlayUrls() []string {
	if x != nil {
		return x.FinishPlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetDeeplinkUrls() []string {
	if x != nil {
		return x.DeeplinkUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetDeeplinkFurls() []string {
	if x != nil {
		return x.DeeplinkFurls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetStepPlayUrls() []string {
	if x != nil {
		return x.StepPlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetMutePlayUrls() []string {
	if x != nil {
		return x.MutePlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetSkipPlayUrls() []string {
	if x != nil {
		return x.SkipPlayUrls
	}
	return nil
}

func (x *Response_SeatBid_Bid_Event) GetClosePlayUrls() []string {
	if x != nil {
		return x.ClosePlayUrls
	}
	return nil
}

type Response_SeatBid_Bid_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua           string                          `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	Referer      string                          `protobuf:"bytes,2,opt,name=referer,proto3" json:"referer,omitempty"`
	ClickDelay   int32                           `protobuf:"varint,3,opt,name=click_delay,json=clickDelay,proto3" json:"click_delay,omitempty"`
	DoClick      int32                           `protobuf:"varint,4,opt,name=do_click,json=doClick,proto3" json:"do_click,omitempty"`
	DoLand       int32                           `protobuf:"varint,5,opt,name=do_land,json=doLand,proto3" json:"do_land,omitempty"`
	LandStayTime int32                           `protobuf:"varint,6,opt,name=land_stay_time,json=landStayTime,proto3" json:"land_stay_time,omitempty"`
	DelayMonitor *Response_SeatBid_Bid_Ext_Delay `protobuf:"bytes,7,opt,name=delay_monitor,json=delayMonitor,proto3" json:"delay_monitor,omitempty"`
}

func (x *Response_SeatBid_Bid_Ext) Reset() {
	*x = Response_SeatBid_Bid_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_Ext) ProtoMessage() {}

func (x *Response_SeatBid_Bid_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_Ext.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_Ext) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 3}
}

func (x *Response_SeatBid_Bid_Ext) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *Response_SeatBid_Bid_Ext) GetReferer() string {
	if x != nil {
		return x.Referer
	}
	return ""
}

func (x *Response_SeatBid_Bid_Ext) GetClickDelay() int32 {
	if x != nil {
		return x.ClickDelay
	}
	return 0
}

func (x *Response_SeatBid_Bid_Ext) GetDoClick() int32 {
	if x != nil {
		return x.DoClick
	}
	return 0
}

func (x *Response_SeatBid_Bid_Ext) GetDoLand() int32 {
	if x != nil {
		return x.DoLand
	}
	return 0
}

func (x *Response_SeatBid_Bid_Ext) GetLandStayTime() int32 {
	if x != nil {
		return x.LandStayTime
	}
	return 0
}

func (x *Response_SeatBid_Bid_Ext) GetDelayMonitor() *Response_SeatBid_Bid_Ext_Delay {
	if x != nil {
		return x.DelayMonitor
	}
	return nil
}

type Response_SeatBid_Bid_AdmObject_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*Response_SeatBid_Bid_AdmObject_Native_Asset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0}
}

func (x *Response_SeatBid_Bid_AdmObject_Native) GetAssets() []*Response_SeatBid_Bid_AdmObject_Native_Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type Response_SeatBid_Bid_AdmObject_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	W           uint32                                     `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	H           uint32                                     `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
	Type        uint32                                     `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Size        uint32                                     `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	Mimes       []string                                   `protobuf:"bytes,5,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Url         string                                     `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	Duration    uint32                                     `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	Cover       string                                     `protobuf:"bytes,8,opt,name=cover,proto3" json:"cover,omitempty"`
	Skip        uint32                                     `protobuf:"varint,9,opt,name=skip,proto3" json:"skip,omitempty"`
	SkipMinTime uint32                                     `protobuf:"varint,10,opt,name=skip_min_time,json=skipMinTime,proto3" json:"skip_min_time,omitempty"`
	PreLoadTtl  uint32                                     `protobuf:"varint,11,opt,name=pre_load_ttl,json=preLoadTtl,proto3" json:"pre_load_ttl,omitempty"`
	Card        *Response_SeatBid_Bid_AdmObject_Video_Card `protobuf:"bytes,12,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Video) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Video) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Video) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Video.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Video) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 1}
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetSkip() uint32 {
	if x != nil {
		return x.Skip
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetSkipMinTime() uint32 {
	if x != nil {
		return x.SkipMinTime
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetPreLoadTtl() uint32 {
	if x != nil {
		return x.PreLoadTtl
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video) GetCard() *Response_SeatBid_Bid_AdmObject_Video_Card {
	if x != nil {
		return x.Card
	}
	return nil
}

type Response_SeatBid_Bid_AdmObject_Native_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Isrequired uint32 `protobuf:"varint,2,opt,name=isrequired,proto3" json:"isrequired,omitempty"`
	// Types that are assignable to OneAsset:
	//
	//	*Response_SeatBid_Bid_AdmObject_Native_Asset_Title_
	//	*Response_SeatBid_Bid_AdmObject_Native_Asset_Img_
	//	*Response_SeatBid_Bid_AdmObject_Native_Asset_Video_
	//	*Response_SeatBid_Bid_AdmObject_Native_Asset_Data_
	OneAsset isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset `protobuf_oneof:"OneAsset"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native_Asset.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native_Asset) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetIsrequired() uint32 {
	if x != nil {
		return x.Isrequired
	}
	return 0
}

func (m *Response_SeatBid_Bid_AdmObject_Native_Asset) GetOneAsset() isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset {
	if m != nil {
		return m.OneAsset
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetTitle() *Response_SeatBid_Bid_AdmObject_Native_Asset_Title {
	if x, ok := x.GetOneAsset().(*Response_SeatBid_Bid_AdmObject_Native_Asset_Title_); ok {
		return x.Title
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetImg() *Response_SeatBid_Bid_AdmObject_Native_Asset_Img {
	if x, ok := x.GetOneAsset().(*Response_SeatBid_Bid_AdmObject_Native_Asset_Img_); ok {
		return x.Img
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetVideo() *Response_SeatBid_Bid_AdmObject_Native_Asset_Video {
	if x, ok := x.GetOneAsset().(*Response_SeatBid_Bid_AdmObject_Native_Asset_Video_); ok {
		return x.Video
	}
	return nil
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset) GetData() *Response_SeatBid_Bid_AdmObject_Native_Asset_Data {
	if x, ok := x.GetOneAsset().(*Response_SeatBid_Bid_AdmObject_Native_Asset_Data_); ok {
		return x.Data
	}
	return nil
}

type isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset interface {
	isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset()
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Title_ struct {
	Title *Response_SeatBid_Bid_AdmObject_Native_Asset_Title `protobuf:"bytes,3,opt,name=title,proto3,oneof"`
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Img_ struct {
	Img *Response_SeatBid_Bid_AdmObject_Native_Asset_Img `protobuf:"bytes,4,opt,name=img,proto3,oneof"`
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Video_ struct {
	Video *Response_SeatBid_Bid_AdmObject_Native_Asset_Video `protobuf:"bytes,5,opt,name=video,proto3,oneof"`
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Data_ struct {
	Data *Response_SeatBid_Bid_AdmObject_Native_Asset_Data `protobuf:"bytes,6,opt,name=data,proto3,oneof"`
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Title_) isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset() {
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Img_) isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset() {
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Video_) isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset() {
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Data_) isResponse_SeatBid_Bid_AdmObject_Native_Asset_OneAsset() {
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Title struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Title) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native_Asset_Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Title) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Title) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native_Asset_Title.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Title) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0, 0}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Title) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Img struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	W    uint32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`
	H    uint32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`
	Type uint32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native_Asset_Img{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Img) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native_Asset_Img.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Img) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0, 1}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) GetW() uint32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) GetH() uint32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Img) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Cover    string `protobuf:"bytes,2,opt,name=cover,proto3" json:"cover,omitempty"`
	Duration uint32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native_Asset_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Video) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native_Asset_Video.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Video) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0, 2}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type Response_SeatBid_Bid_AdmObject_Native_Asset_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Data) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Native_Asset_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Data) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Data) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Native_Asset_Data.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Native_Asset_Data) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0, 0, 3}
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Data) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Native_Asset_Data) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Response_SeatBid_Bid_AdmObject_Video_Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     uint32                                            `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Url      string                                            `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Html     string                                            `protobuf:"bytes,3,opt,name=html,proto3" json:"html,omitempty"`
	Charset  string                                            `protobuf:"bytes,4,opt,name=charset,proto3" json:"charset,omitempty"`
	Icon     string                                            `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	Title    string                                            `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	Content  string                                            `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	Comments uint32                                            `protobuf:"varint,8,opt,name=comments,proto3" json:"comments,omitempty"`
	Rating   uint32                                            `protobuf:"varint,9,opt,name=rating,proto3" json:"rating,omitempty"`
	Button   *Response_SeatBid_Bid_AdmObject_Video_Card_Button `protobuf:"bytes,10,opt,name=button,proto3" json:"button,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Video_Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Video_Card) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Video_Card.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Video_Card) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 1, 0}
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetCharset() string {
	if x != nil {
		return x.Charset
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetComments() uint32 {
	if x != nil {
		return x.Comments
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetRating() uint32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card) GetButton() *Response_SeatBid_Bid_AdmObject_Video_Card_Button {
	if x != nil {
		return x.Button
	}
	return nil
}

type Response_SeatBid_Bid_AdmObject_Video_Card_Button struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card_Button) Reset() {
	*x = Response_SeatBid_Bid_AdmObject_Video_Card_Button{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card_Button) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_AdmObject_Video_Card_Button) ProtoMessage() {}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card_Button) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_AdmObject_Video_Card_Button.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_AdmObject_Video_Card_Button) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 0, 1, 0, 0}
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card_Button) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_AdmObject_Video_Card_Button) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type Response_SeatBid_Bid_Ext_Delay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Delay int32  `protobuf:"varint,2,opt,name=delay,proto3" json:"delay,omitempty"`
}

func (x *Response_SeatBid_Bid_Ext_Delay) Reset() {
	*x = Response_SeatBid_Bid_Ext_Delay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quna_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_SeatBid_Bid_Ext_Delay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_SeatBid_Bid_Ext_Delay) ProtoMessage() {}

func (x *Response_SeatBid_Bid_Ext_Delay) ProtoReflect() protoreflect.Message {
	mi := &file_quna_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_SeatBid_Bid_Ext_Delay.ProtoReflect.Descriptor instead.
func (*Response_SeatBid_Bid_Ext_Delay) Descriptor() ([]byte, []int) {
	return file_quna_proto_rawDescGZIP(), []int{1, 0, 0, 3, 0}
}

func (x *Response_SeatBid_Bid_Ext_Delay) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Response_SeatBid_Bid_Ext_Delay) GetDelay() int32 {
	if x != nil {
		return x.Delay
	}
	return 0
}

var File_quna_proto protoreflect.FileDescriptor

var file_quna_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x71, 0x75, 0x6e, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x31, 0x30, 0x22, 0xbb, 0x21, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x03, 0x69,
	0x6d, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03,
	0x69, 0x6d, 0x70, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x73,
	0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74, 0x65,
	0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x1a, 0x95, 0x0b, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61,
	0x67, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x33, 0x0a, 0x06,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x2e, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x12, 0x33, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x06,
	0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73, 0x75, 0x6c,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x75, 0x6c, 0x12, 0x1e, 0x0a, 0x0a,
	0x69, 0x73, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x73, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70,
	0x1a, 0x36, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x1a, 0xa5, 0x04, 0x0a, 0x0b, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x3e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31,
	0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4e, 0x61, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x4e, 0x61, 0x49, 0x6d, 0x67, 0x52, 0x03, 0x69,
	0x6d, 0x67, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x4e, 0x61, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x4e, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x1b, 0x0a, 0x07, 0x4e, 0x61, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x1a, 0x59, 0x0a, 0x05,
	0x4e, 0x61, 0x49, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6d, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x68, 0x6d, 0x69,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0x47, 0x0a, 0x07, 0x4e, 0x61, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73,
	0x1a, 0x2e, 0x0a, 0x06, 0x4e, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6c, 0x65, 0x6e,
	0x1a, 0x5a, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x1a, 0xef, 0x01, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x69, 0x6e,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d,
	0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x1a, 0x32,
	0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x1a, 0x36, 0x0a, 0x03, 0x50, 0x6d, 0x70, 0x12, 0x2f, 0x0a, 0x05, 0x64, 0x65, 0x61,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x44,
	0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x1a, 0xbd, 0x01, 0x0a, 0x03, 0x41,
	0x70, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x63, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x1a, 0xa6, 0x01, 0x0a, 0x04, 0x53,
	0x69, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x1a, 0xcf, 0x10, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76,
	0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x69,
	0x64, 0x6d, 0x64, 0x35, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x6d,
	0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x66, 0x61, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x66, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x66, 0x61, 0x6d,
	0x64, 0x35, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x66, 0x61, 0x6d, 0x64, 0x35,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x61, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x75, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x61, 0x63, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x61, 0x63, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76,
	0x36, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x0e, 0x0a,
	0x02, 0x75, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x4e, 0x0a,
	0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a,
	0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x6c, 0x61, 0x73, 0x68, 0x76, 0x65, 0x72, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x6c, 0x61, 0x73, 0x68, 0x76, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20, 0x0a,
	0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x64, 0x70, 0x69, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x70,
	0x69, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x70,
	0x70, 0x69, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x2d, 0x0a,
	0x03, 0x67, 0x65, 0x6f, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x1e, 0x0a, 0x0a,
	0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x72, 0x6f, 0x6d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10,
	0x73, 0x79, 0x73, 0x63, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x79, 0x73, 0x63, 0x6f, 0x6d, 0x70, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74,
	0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f,
	0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x67, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x6d, 0x73, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x68, 0x6d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x66, 0x69,
	0x5f, 0x6d, 0x61, 0x63, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x69, 0x66, 0x69,
	0x4d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x73, 0x69, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6d, 0x73, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x73, 0x69, 0x64, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x73, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x64, 0x69, 0x64, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6d, 0x73, 0x65, 0x63, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x65, 0x63, 0x12, 0x28, 0x0a, 0x10, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6e, 0x73, 0x65, 0x63, 0x18, 0x31,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x4e, 0x73, 0x65, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x7a, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x54,
	0x7a, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x70, 0x75, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x37, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x63, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x38, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x69, 0x73, 0x6b,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x39, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x6b, 0x61, 0x64, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x13, 0x73, 0x6b, 0x61, 0x64, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x75, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x69, 0x75,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x73, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x5f, 0x63, 0x61, 0x69, 0x64, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x72, 0x65, 0x43, 0x61, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x61,
	0x69, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x70, 0x72, 0x65, 0x43, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x1a, 0x6f, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61,
	0x63, 0x63, 0x75, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x61, 0x63, 0x63, 0x75, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x71, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x45, 0x74, 0x68, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x57, 0x69, 0x46, 0x69, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x65, 0x6c, 0x6c,
	0x75, 0x6c, 0x61, 0x72, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x32, 0x10, 0x04, 0x12, 0x06, 0x0a, 0x02,
	0x47, 0x33, 0x10, 0x05, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x34, 0x10, 0x06, 0x12, 0x06, 0x0a, 0x02,
	0x47, 0x35, 0x10, 0x07, 0x22, 0x3f, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x50, 0x43,
	0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x54, 0x56, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x74,
	0x68, 0x65, 0x72, 0x10, 0x04, 0x1a, 0x5c, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x79, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x79, 0x6f, 0x62, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x22, 0xcc, 0x1d, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31,
	0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x62, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6e, 0x62, 0x72, 0x1a, 0xd2, 0x1c,
	0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x62, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31,
	0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x04, 0x62, 0x69, 0x64, 0x73, 0x1a, 0x93, 0x1c, 0x0a,
	0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x6d, 0x68,
	0x74, 0x6d, 0x6c, 0x6a, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x6d,
	0x68, 0x74, 0x6d, 0x6c, 0x6a, 0x73, 0x12, 0x45, 0x0a, 0x09, 0x61, 0x64, 0x6d, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x09, 0x61, 0x64, 0x6d, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x75, 0x72,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75,
	0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x71,
	0x75, 0x69, 0x63, 0x6b, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x6e,
	0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x77, 0x78, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x77, 0x78, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x78, 0x61, 0x70, 0x70,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x78, 0x61, 0x70,
	0x70, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a,
	0x03, 0x61, 0x70, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61,
	0x70, 0x70, 0x12, 0x3b, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69,
	0x64, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x33, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x45, 0x78, 0x74, 0x52,
	0x03, 0x65, 0x78, 0x74, 0x1a, 0x87, 0x0c, 0x0a, 0x09, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69,
	0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x52, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x1a,
	0xca, 0x05, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x4c, 0x0a, 0x06, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x1a, 0xf1, 0x04, 0x0a, 0x05, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x12, 0x52, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x48, 0x00, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x49, 0x6d, 0x67, 0x48, 0x00, 0x52,
	0x03, 0x69, 0x6d, 0x67, 0x12, 0x52, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42,
	0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x48,
	0x00, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x4f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69,
	0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e,
	0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1b, 0x0a, 0x05, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x47, 0x0a, 0x03, 0x49, 0x6d, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a,
	0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a,
	0x4b, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x32, 0x0a, 0x04,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0a, 0x0a, 0x08, 0x4f, 0x6e, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x1a, 0x9f, 0x05, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x01, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x73, 0x6b, 0x69, 0x70, 0x4d, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c,
	0x70, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x74, 0x6c, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x54, 0x74, 0x6c, 0x12, 0x46,
	0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x1a, 0xd5, 0x02, 0x0a, 0x04, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x74, 0x6d, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x74, 0x6d, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x72, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x72,
	0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x51, 0x0a, 0x06, 0x62,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x2e,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x06, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x1a, 0x2e,
	0x0a, 0x06, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0xaf,
	0x02, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64,
	0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x44, 0x65, 0x73, 0x63,
	0x1a, 0x93, 0x06, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d,
	0x70, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d,
	0x70, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x55, 0x72, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x6f,
	0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x6f, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x44, 0x6f, 0x64, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x32, 0x35, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x32, 0x35, 0x70, 0x6c, 0x61,
	0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x35,
	0x30, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x35, 0x30, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x37, 0x35, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x37, 0x35, 0x70, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x75, 0x73, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6c,
	0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x66, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x46, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x73, 0x74, 0x65, 0x70, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x65, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x75, 0x74, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x74, 0x65,
	0x50, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x6b, 0x69, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x1a, 0xa9, 0x02, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x5f,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x6f, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6f, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x64, 0x6f, 0x4c, 0x61, 0x6e, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x6c, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x31, 0x30, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x45, 0x78, 0x74, 0x2e, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x1a, 0x2f, 0x0a, 0x05, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2e, 0x2f, 0x71, 0x75, 0x6e, 0x61, 0x5f, 0x75, 0x70,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_quna_proto_rawDescOnce sync.Once
	file_quna_proto_rawDescData = file_quna_proto_rawDesc
)

func file_quna_proto_rawDescGZIP() []byte {
	file_quna_proto_rawDescOnce.Do(func() {
		file_quna_proto_rawDescData = protoimpl.X.CompressGZIP(file_quna_proto_rawDescData)
	})
	return file_quna_proto_rawDescData
}

var file_quna_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_quna_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_quna_proto_goTypes = []interface{}{
	(Request_Device_ConnectionType)(0),                        // 0: proto10.Request.Device.ConnectionType
	(Request_Device_DeviceType)(0),                            // 1: proto10.Request.Device.DeviceType
	(*Request)(nil),                                           // 2: proto10.Request
	(*Response)(nil),                                          // 3: proto10.Response
	(*Request_Imp)(nil),                                       // 4: proto10.Request.Imp
	(*Request_App)(nil),                                       // 5: proto10.Request.App
	(*Request_Site)(nil),                                      // 6: proto10.Request.Site
	(*Request_Device)(nil),                                    // 7: proto10.Request.Device
	(*Request_User)(nil),                                      // 8: proto10.Request.User
	(*Request_Imp_Banner)(nil),                                // 9: proto10.Request.Imp.Banner
	(*Request_Imp_NativeAsset)(nil),                           // 10: proto10.Request.Imp.NativeAsset
	(*Request_Imp_Native)(nil),                                // 11: proto10.Request.Imp.Native
	(*Request_Imp_Video)(nil),                                 // 12: proto10.Request.Imp.Video
	(*Request_Imp_Deal)(nil),                                  // 13: proto10.Request.Imp.Deal
	(*Request_Imp_Pmp)(nil),                                   // 14: proto10.Request.Imp.Pmp
	(*Request_Imp_NativeAsset_NaTitle)(nil),                   // 15: proto10.Request.Imp.NativeAsset.NaTitle
	(*Request_Imp_NativeAsset_NaImg)(nil),                     // 16: proto10.Request.Imp.NativeAsset.NaImg
	(*Request_Imp_NativeAsset_NaVideo)(nil),                   // 17: proto10.Request.Imp.NativeAsset.NaVideo
	(*Request_Imp_NativeAsset_NaData)(nil),                    // 18: proto10.Request.Imp.NativeAsset.NaData
	(*Request_Device_Geo)(nil),                                // 19: proto10.Request.Device.Geo
	(*Response_SeatBid)(nil),                                  // 20: proto10.Response.SeatBid
	(*Response_SeatBid_Bid)(nil),                              // 21: proto10.Response.SeatBid.Bid
	(*Response_SeatBid_Bid_AdmObject)(nil),                    // 22: proto10.Response.SeatBid.Bid.AdmObject
	(*Response_SeatBid_Bid_App)(nil),                          // 23: proto10.Response.SeatBid.Bid.App
	(*Response_SeatBid_Bid_Event)(nil),                        // 24: proto10.Response.SeatBid.Bid.Event
	(*Response_SeatBid_Bid_Ext)(nil),                          // 25: proto10.Response.SeatBid.Bid.Ext
	(*Response_SeatBid_Bid_AdmObject_Native)(nil),             // 26: proto10.Response.SeatBid.Bid.AdmObject.Native
	(*Response_SeatBid_Bid_AdmObject_Video)(nil),              // 27: proto10.Response.SeatBid.Bid.AdmObject.Video
	(*Response_SeatBid_Bid_AdmObject_Native_Asset)(nil),       // 28: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset
	(*Response_SeatBid_Bid_AdmObject_Native_Asset_Title)(nil), // 29: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Title
	(*Response_SeatBid_Bid_AdmObject_Native_Asset_Img)(nil),   // 30: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Img
	(*Response_SeatBid_Bid_AdmObject_Native_Asset_Video)(nil), // 31: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Video
	(*Response_SeatBid_Bid_AdmObject_Native_Asset_Data)(nil),  // 32: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Data
	(*Response_SeatBid_Bid_AdmObject_Video_Card)(nil),         // 33: proto10.Response.SeatBid.Bid.AdmObject.Video.Card
	(*Response_SeatBid_Bid_AdmObject_Video_Card_Button)(nil),  // 34: proto10.Response.SeatBid.Bid.AdmObject.Video.Card.Button
	(*Response_SeatBid_Bid_Ext_Delay)(nil),                    // 35: proto10.Response.SeatBid.Bid.Ext.Delay
}
var file_quna_proto_depIdxs = []int32{
	4,  // 0: proto10.Request.imp:type_name -> proto10.Request.Imp
	5,  // 1: proto10.Request.app:type_name -> proto10.Request.App
	6,  // 2: proto10.Request.site:type_name -> proto10.Request.Site
	7,  // 3: proto10.Request.device:type_name -> proto10.Request.Device
	8,  // 4: proto10.Request.user:type_name -> proto10.Request.User
	20, // 5: proto10.Response.seatbid:type_name -> proto10.Response.SeatBid
	9,  // 6: proto10.Request.Imp.banner:type_name -> proto10.Request.Imp.Banner
	11, // 7: proto10.Request.Imp.native:type_name -> proto10.Request.Imp.Native
	12, // 8: proto10.Request.Imp.video:type_name -> proto10.Request.Imp.Video
	14, // 9: proto10.Request.Imp.pmp:type_name -> proto10.Request.Imp.Pmp
	0,  // 10: proto10.Request.Device.connectiontype:type_name -> proto10.Request.Device.ConnectionType
	1,  // 11: proto10.Request.Device.devicetype:type_name -> proto10.Request.Device.DeviceType
	19, // 12: proto10.Request.Device.geo:type_name -> proto10.Request.Device.Geo
	15, // 13: proto10.Request.Imp.NativeAsset.title:type_name -> proto10.Request.Imp.NativeAsset.NaTitle
	16, // 14: proto10.Request.Imp.NativeAsset.img:type_name -> proto10.Request.Imp.NativeAsset.NaImg
	17, // 15: proto10.Request.Imp.NativeAsset.video:type_name -> proto10.Request.Imp.NativeAsset.NaVideo
	18, // 16: proto10.Request.Imp.NativeAsset.data:type_name -> proto10.Request.Imp.NativeAsset.NaData
	10, // 17: proto10.Request.Imp.Native.assets:type_name -> proto10.Request.Imp.NativeAsset
	13, // 18: proto10.Request.Imp.Pmp.deals:type_name -> proto10.Request.Imp.Deal
	21, // 19: proto10.Response.SeatBid.bids:type_name -> proto10.Response.SeatBid.Bid
	22, // 20: proto10.Response.SeatBid.Bid.admobject:type_name -> proto10.Response.SeatBid.Bid.AdmObject
	23, // 21: proto10.Response.SeatBid.Bid.app:type_name -> proto10.Response.SeatBid.Bid.App
	24, // 22: proto10.Response.SeatBid.Bid.events:type_name -> proto10.Response.SeatBid.Bid.Event
	25, // 23: proto10.Response.SeatBid.Bid.ext:type_name -> proto10.Response.SeatBid.Bid.Ext
	26, // 24: proto10.Response.SeatBid.Bid.AdmObject.native:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native
	27, // 25: proto10.Response.SeatBid.Bid.AdmObject.video:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Video
	35, // 26: proto10.Response.SeatBid.Bid.Ext.delay_monitor:type_name -> proto10.Response.SeatBid.Bid.Ext.Delay
	28, // 27: proto10.Response.SeatBid.Bid.AdmObject.Native.assets:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native.Asset
	33, // 28: proto10.Response.SeatBid.Bid.AdmObject.Video.card:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Video.Card
	29, // 29: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.title:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Title
	30, // 30: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.img:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Img
	31, // 31: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.video:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Video
	32, // 32: proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.data:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Native.Asset.Data
	34, // 33: proto10.Response.SeatBid.Bid.AdmObject.Video.Card.button:type_name -> proto10.Response.SeatBid.Bid.AdmObject.Video.Card.Button
	34, // [34:34] is the sub-list for method output_type
	34, // [34:34] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_quna_proto_init() }
func file_quna_proto_init() {
	if File_quna_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_quna_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_NativeAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_NativeAsset_NaTitle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_NativeAsset_NaImg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_NativeAsset_NaVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_NativeAsset_NaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native_Asset_Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native_Asset_Img); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native_Asset_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Native_Asset_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Video_Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_AdmObject_Video_Card_Button); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quna_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_SeatBid_Bid_Ext_Delay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_quna_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*Response_SeatBid_Bid_AdmObject_Native_Asset_Title_)(nil),
		(*Response_SeatBid_Bid_AdmObject_Native_Asset_Img_)(nil),
		(*Response_SeatBid_Bid_AdmObject_Native_Asset_Video_)(nil),
		(*Response_SeatBid_Bid_AdmObject_Native_Asset_Data_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_quna_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_quna_proto_goTypes,
		DependencyIndexes: file_quna_proto_depIdxs,
		EnumInfos:         file_quna_proto_enumTypes,
		MessageInfos:      file_quna_proto_msgTypes,
	}.Build()
	File_quna_proto = out.File
	file_quna_proto_rawDesc = nil
	file_quna_proto_goTypes = nil
	file_quna_proto_depIdxs = nil
}
