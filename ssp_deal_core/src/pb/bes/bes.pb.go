// Copyright 2013-2015 Baidu Inc. All Rights Reserved.
//
// 文件内容: baidu exchange service 实时竞价协议文件
// 基于baidu_exchange_service.proto产出的BES外发版本
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: bes.proto

package bes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 新流量类型
type BidRequest_NewTTP int32

const (
	BidRequest_TTP_UNKNOW BidRequest_NewTTP = 0
	BidRequest_TTP_PC     BidRequest_NewTTP = 1
	BidRequest_TTP_WAP    BidRequest_NewTTP = 2
	BidRequest_TTP_APP    BidRequest_NewTTP = 3
	// 使用此值表示不区分流量类型
	// 如果有超过此值添加,同步更新为最大值
	BidRequest_TTP_MAX BidRequest_NewTTP = 9999
)

// Enum value maps for BidRequest_NewTTP.
var (
	BidRequest_NewTTP_name = map[int32]string{
		0:    "TTP_UNKNOW",
		1:    "TTP_PC",
		2:    "TTP_WAP",
		3:    "TTP_APP",
		9999: "TTP_MAX",
	}
	BidRequest_NewTTP_value = map[string]int32{
		"TTP_UNKNOW": 0,
		"TTP_PC":     1,
		"TTP_WAP":    2,
		"TTP_APP":    3,
		"TTP_MAX":    9999,
	}
)

func (x BidRequest_NewTTP) Enum() *BidRequest_NewTTP {
	p := new(BidRequest_NewTTP)
	*p = x
	return p
}

func (x BidRequest_NewTTP) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_NewTTP) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[0].Descriptor()
}

func (BidRequest_NewTTP) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[0]
}

func (x BidRequest_NewTTP) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_NewTTP) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_NewTTP(num)
	return nil
}

// Deprecated: Use BidRequest_NewTTP.Descriptor instead.
func (BidRequest_NewTTP) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 0}
}

// 广告托管限制
type BidRequest_HostAdLimit int32

const (
	BidRequest_NO_LIMIT  BidRequest_HostAdLimit = 1 // 无限制
	BidRequest_ONLY_HOST BidRequest_HostAdLimit = 2 // 仅支持托管物料
	BidRequest_NOT_HOST  BidRequest_HostAdLimit = 3 // 仅支持非托管物料
)

// Enum value maps for BidRequest_HostAdLimit.
var (
	BidRequest_HostAdLimit_name = map[int32]string{
		1: "NO_LIMIT",
		2: "ONLY_HOST",
		3: "NOT_HOST",
	}
	BidRequest_HostAdLimit_value = map[string]int32{
		"NO_LIMIT":  1,
		"ONLY_HOST": 2,
		"NOT_HOST":  3,
	}
)

func (x BidRequest_HostAdLimit) Enum() *BidRequest_HostAdLimit {
	p := new(BidRequest_HostAdLimit)
	*p = x
	return p
}

func (x BidRequest_HostAdLimit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_HostAdLimit) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[1].Descriptor()
}

func (BidRequest_HostAdLimit) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[1]
}

func (x BidRequest_HostAdLimit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_HostAdLimit) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_HostAdLimit(num)
	return nil
}

// Deprecated: Use BidRequest_HostAdLimit.Descriptor instead.
func (BidRequest_HostAdLimit) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1}
}

// 经纬度坐标标准
type BidRequest_Geo_Coordinate_Standard int32

const (
	// 百度地图的经纬度坐标标准
	BidRequest_Geo_Coordinate_BD_09 BidRequest_Geo_Coordinate_Standard = 0
	// 国测局制定的经纬度坐标标准
	BidRequest_Geo_Coordinate_GCJ_02 BidRequest_Geo_Coordinate_Standard = 1
	// 国际经纬度坐标标准
	BidRequest_Geo_Coordinate_WGS_84 BidRequest_Geo_Coordinate_Standard = 2
	// 百度地图的墨卡坐标标准,以米为单位
	BidRequest_Geo_Coordinate_BD_09_LL BidRequest_Geo_Coordinate_Standard = 3
)

// Enum value maps for BidRequest_Geo_Coordinate_Standard.
var (
	BidRequest_Geo_Coordinate_Standard_name = map[int32]string{
		0: "BD_09",
		1: "GCJ_02",
		2: "WGS_84",
		3: "BD_09_LL",
	}
	BidRequest_Geo_Coordinate_Standard_value = map[string]int32{
		"BD_09":    0,
		"GCJ_02":   1,
		"WGS_84":   2,
		"BD_09_LL": 3,
	}
)

func (x BidRequest_Geo_Coordinate_Standard) Enum() *BidRequest_Geo_Coordinate_Standard {
	p := new(BidRequest_Geo_Coordinate_Standard)
	*p = x
	return p
}

func (x BidRequest_Geo_Coordinate_Standard) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Geo_Coordinate_Standard) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[2].Descriptor()
}

func (BidRequest_Geo_Coordinate_Standard) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[2]
}

func (x BidRequest_Geo_Coordinate_Standard) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Geo_Coordinate_Standard) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Geo_Coordinate_Standard(num)
	return nil
}

// Deprecated: Use BidRequest_Geo_Coordinate_Standard.Descriptor instead.
func (BidRequest_Geo_Coordinate_Standard) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

// 设备类型定义
type BidRequest_Mobile_MobileDeviceType int32

const (
	BidRequest_Mobile_UNKNOWN_DEVICE BidRequest_Mobile_MobileDeviceType = 0
	BidRequest_Mobile_HIGHEND_PHONE  BidRequest_Mobile_MobileDeviceType = 1
	BidRequest_Mobile_TABLET         BidRequest_Mobile_MobileDeviceType = 2
	BidRequest_Mobile_SMART_TV       BidRequest_Mobile_MobileDeviceType = 4
	BidRequest_Mobile_OUTDOOR_SCREEN BidRequest_Mobile_MobileDeviceType = 5
)

// Enum value maps for BidRequest_Mobile_MobileDeviceType.
var (
	BidRequest_Mobile_MobileDeviceType_name = map[int32]string{
		0: "UNKNOWN_DEVICE",
		1: "HIGHEND_PHONE",
		2: "TABLET",
		4: "SMART_TV",
		5: "OUTDOOR_SCREEN",
	}
	BidRequest_Mobile_MobileDeviceType_value = map[string]int32{
		"UNKNOWN_DEVICE": 0,
		"HIGHEND_PHONE":  1,
		"TABLET":         2,
		"SMART_TV":       4,
		"OUTDOOR_SCREEN": 5,
	}
)

func (x BidRequest_Mobile_MobileDeviceType) Enum() *BidRequest_Mobile_MobileDeviceType {
	p := new(BidRequest_Mobile_MobileDeviceType)
	*p = x
	return p
}

func (x BidRequest_Mobile_MobileDeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_MobileDeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Mobile_MobileDeviceType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[3]
}

func (x BidRequest_Mobile_MobileDeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_MobileDeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_MobileDeviceType(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_MobileDeviceType.Descriptor instead.
func (BidRequest_Mobile_MobileDeviceType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 0}
}

// 移动平台名，例如android，iphone等等
type BidRequest_Mobile_OS int32

const (
	BidRequest_Mobile_UNKNOWN_OS    BidRequest_Mobile_OS = 0
	BidRequest_Mobile_IOS           BidRequest_Mobile_OS = 1
	BidRequest_Mobile_ANDROID       BidRequest_Mobile_OS = 2
	BidRequest_Mobile_WINDOWS_PHONE BidRequest_Mobile_OS = 3
)

// Enum value maps for BidRequest_Mobile_OS.
var (
	BidRequest_Mobile_OS_name = map[int32]string{
		0: "UNKNOWN_OS",
		1: "IOS",
		2: "ANDROID",
		3: "WINDOWS_PHONE",
	}
	BidRequest_Mobile_OS_value = map[string]int32{
		"UNKNOWN_OS":    0,
		"IOS":           1,
		"ANDROID":       2,
		"WINDOWS_PHONE": 3,
	}
)

func (x BidRequest_Mobile_OS) Enum() *BidRequest_Mobile_OS {
	p := new(BidRequest_Mobile_OS)
	*p = x
	return p
}

func (x BidRequest_Mobile_OS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_OS) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[4].Descriptor()
}

func (BidRequest_Mobile_OS) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[4]
}

func (x BidRequest_Mobile_OS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_OS) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_OS(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_OS.Descriptor instead.
func (BidRequest_Mobile_OS) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 1}
}

// 无线网络类型
type BidRequest_Mobile_WirelessNetworkType int32

const (
	BidRequest_Mobile_UNKNOWN_NETWORK BidRequest_Mobile_WirelessNetworkType = 0
	BidRequest_Mobile_WIFI            BidRequest_Mobile_WirelessNetworkType = 1
	BidRequest_Mobile_MOBILE_2G       BidRequest_Mobile_WirelessNetworkType = 2
	BidRequest_Mobile_MOBILE_3G       BidRequest_Mobile_WirelessNetworkType = 3
	BidRequest_Mobile_MOBILE_4G       BidRequest_Mobile_WirelessNetworkType = 4
)

// Enum value maps for BidRequest_Mobile_WirelessNetworkType.
var (
	BidRequest_Mobile_WirelessNetworkType_name = map[int32]string{
		0: "UNKNOWN_NETWORK",
		1: "WIFI",
		2: "MOBILE_2G",
		3: "MOBILE_3G",
		4: "MOBILE_4G",
	}
	BidRequest_Mobile_WirelessNetworkType_value = map[string]int32{
		"UNKNOWN_NETWORK": 0,
		"WIFI":            1,
		"MOBILE_2G":       2,
		"MOBILE_3G":       3,
		"MOBILE_4G":       4,
	}
)

func (x BidRequest_Mobile_WirelessNetworkType) Enum() *BidRequest_Mobile_WirelessNetworkType {
	p := new(BidRequest_Mobile_WirelessNetworkType)
	*p = x
	return p
}

func (x BidRequest_Mobile_WirelessNetworkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_WirelessNetworkType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[5].Descriptor()
}

func (BidRequest_Mobile_WirelessNetworkType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[5]
}

func (x BidRequest_Mobile_WirelessNetworkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_WirelessNetworkType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_WirelessNetworkType(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_WirelessNetworkType.Descriptor instead.
func (BidRequest_Mobile_WirelessNetworkType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 2}
}

// 移动设备ID的类型
type BidRequest_Mobile_MobileID_IDType int32

const (
	// 未知
	BidRequest_Mobile_MobileID_UNKNOWN BidRequest_Mobile_MobileID_IDType = 0
	// IMEI
	BidRequest_Mobile_MobileID_IMEI BidRequest_Mobile_MobileID_IDType = 1
	// MAC地址
	BidRequest_Mobile_MobileID_MAC BidRequest_Mobile_MobileID_IDType = 2
	// 百度私有持久化用户ID, 支持系统: Android
	// ！！只传递给内部DSP
	BidRequest_Mobile_MobileID_CUID BidRequest_Mobile_MobileID_IDType = 3
	// OAID
	BidRequest_Mobile_MobileID_OAID BidRequest_Mobile_MobileID_IDType = 4
	// CAID
	BidRequest_Mobile_MobileID_CAID BidRequest_Mobile_MobileID_IDType = 5
)

// Enum value maps for BidRequest_Mobile_MobileID_IDType.
var (
	BidRequest_Mobile_MobileID_IDType_name = map[int32]string{
		0: "UNKNOWN",
		1: "IMEI",
		2: "MAC",
		3: "CUID",
		4: "OAID",
		5: "CAID",
	}
	BidRequest_Mobile_MobileID_IDType_value = map[string]int32{
		"UNKNOWN": 0,
		"IMEI":    1,
		"MAC":     2,
		"CUID":    3,
		"OAID":    4,
		"CAID":    5,
	}
)

func (x BidRequest_Mobile_MobileID_IDType) Enum() *BidRequest_Mobile_MobileID_IDType {
	p := new(BidRequest_Mobile_MobileID_IDType)
	*p = x
	return p
}

func (x BidRequest_Mobile_MobileID_IDType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_MobileID_IDType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[6].Descriptor()
}

func (BidRequest_Mobile_MobileID_IDType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[6]
}

func (x BidRequest_Mobile_MobileID_IDType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_MobileID_IDType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_MobileID_IDType(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_MobileID_IDType.Descriptor instead.
func (BidRequest_Mobile_MobileID_IDType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 0, 0}
}

type BidRequest_Mobile_ForAdvertisingID_IDType int32

const (
	BidRequest_Mobile_ForAdvertisingID_UNKNOWN    BidRequest_Mobile_ForAdvertisingID_IDType = 0 // 未知
	BidRequest_Mobile_ForAdvertisingID_ANDROID_ID BidRequest_Mobile_ForAdvertisingID_IDType = 4 // Android适用
	BidRequest_Mobile_ForAdvertisingID_IDFA       BidRequest_Mobile_ForAdvertisingID_IDType = 5 // IOS适用
)

// Enum value maps for BidRequest_Mobile_ForAdvertisingID_IDType.
var (
	BidRequest_Mobile_ForAdvertisingID_IDType_name = map[int32]string{
		0: "UNKNOWN",
		4: "ANDROID_ID",
		5: "IDFA",
	}
	BidRequest_Mobile_ForAdvertisingID_IDType_value = map[string]int32{
		"UNKNOWN":    0,
		"ANDROID_ID": 4,
		"IDFA":       5,
	}
)

func (x BidRequest_Mobile_ForAdvertisingID_IDType) Enum() *BidRequest_Mobile_ForAdvertisingID_IDType {
	p := new(BidRequest_Mobile_ForAdvertisingID_IDType)
	*p = x
	return p
}

func (x BidRequest_Mobile_ForAdvertisingID_IDType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_ForAdvertisingID_IDType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[7].Descriptor()
}

func (BidRequest_Mobile_ForAdvertisingID_IDType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[7]
}

func (x BidRequest_Mobile_ForAdvertisingID_IDType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_ForAdvertisingID_IDType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_ForAdvertisingID_IDType(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_ForAdvertisingID_IDType.Descriptor instead.
func (BidRequest_Mobile_ForAdvertisingID_IDType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 2, 0}
}

// App允许的交互类型定义
// 电话、下载、应用唤醒
type BidRequest_Mobile_MobileApp_AppInteractionType int32

const (
	BidRequest_Mobile_MobileApp_TELEPHONE BidRequest_Mobile_MobileApp_AppInteractionType = 0
	BidRequest_Mobile_MobileApp_DOWNLOAD  BidRequest_Mobile_MobileApp_AppInteractionType = 1
	BidRequest_Mobile_MobileApp_DEEPLINK  BidRequest_Mobile_MobileApp_AppInteractionType = 2
)

// Enum value maps for BidRequest_Mobile_MobileApp_AppInteractionType.
var (
	BidRequest_Mobile_MobileApp_AppInteractionType_name = map[int32]string{
		0: "TELEPHONE",
		1: "DOWNLOAD",
		2: "DEEPLINK",
	}
	BidRequest_Mobile_MobileApp_AppInteractionType_value = map[string]int32{
		"TELEPHONE": 0,
		"DOWNLOAD":  1,
		"DEEPLINK":  2,
	}
)

func (x BidRequest_Mobile_MobileApp_AppInteractionType) Enum() *BidRequest_Mobile_MobileApp_AppInteractionType {
	p := new(BidRequest_Mobile_MobileApp_AppInteractionType)
	*p = x
	return p
}

func (x BidRequest_Mobile_MobileApp_AppInteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Mobile_MobileApp_AppInteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[8].Descriptor()
}

func (BidRequest_Mobile_MobileApp_AppInteractionType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[8]
}

func (x BidRequest_Mobile_MobileApp_AppInteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Mobile_MobileApp_AppInteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Mobile_MobileApp_AppInteractionType(num)
	return nil
}

// Deprecated: Use BidRequest_Mobile_MobileApp_AppInteractionType.Descriptor instead.
func (BidRequest_Mobile_MobileApp_AppInteractionType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 3, 0}
}

type BidRequest_AdSlot_NativeAdParam_Fields int32

const (
	// 标题
	BidRequest_AdSlot_NativeAdParam_TITLE BidRequest_AdSlot_NativeAdParam_Fields = 1
	// 内容描述
	BidRequest_AdSlot_NativeAdParam_DESC BidRequest_AdSlot_NativeAdParam_Fields = 2
	// 主题图
	BidRequest_AdSlot_NativeAdParam_IMAGE BidRequest_AdSlot_NativeAdParam_Fields = 4
	// logo 图标
	BidRequest_AdSlot_NativeAdParam_LOGOICON BidRequest_AdSlot_NativeAdParam_Fields = 8
	// 投放时下载的APP大小
	BidRequest_AdSlot_NativeAdParam_APPSIZE BidRequest_AdSlot_NativeAdParam_Fields = 16
	// 视频
	BidRequest_AdSlot_NativeAdParam_VIDEO BidRequest_AdSlot_NativeAdParam_Fields = 64
	// 品牌名
	BidRequest_AdSlot_NativeAdParam_BRAND_NAME BidRequest_AdSlot_NativeAdParam_Fields = 128
)

// Enum value maps for BidRequest_AdSlot_NativeAdParam_Fields.
var (
	BidRequest_AdSlot_NativeAdParam_Fields_name = map[int32]string{
		1:   "TITLE",
		2:   "DESC",
		4:   "IMAGE",
		8:   "LOGOICON",
		16:  "APPSIZE",
		64:  "VIDEO",
		128: "BRAND_NAME",
	}
	BidRequest_AdSlot_NativeAdParam_Fields_value = map[string]int32{
		"TITLE":      1,
		"DESC":       2,
		"IMAGE":      4,
		"LOGOICON":   8,
		"APPSIZE":    16,
		"VIDEO":      64,
		"BRAND_NAME": 128,
	}
)

func (x BidRequest_AdSlot_NativeAdParam_Fields) Enum() *BidRequest_AdSlot_NativeAdParam_Fields {
	p := new(BidRequest_AdSlot_NativeAdParam_Fields)
	*p = x
	return p
}

func (x BidRequest_AdSlot_NativeAdParam_Fields) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_NativeAdParam_Fields) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[9].Descriptor()
}

func (BidRequest_AdSlot_NativeAdParam_Fields) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[9]
}

func (x BidRequest_AdSlot_NativeAdParam_Fields) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_NativeAdParam_Fields) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_NativeAdParam_Fields(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_NativeAdParam_Fields.Descriptor instead.
func (BidRequest_AdSlot_NativeAdParam_Fields) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 0, 0}
}

// 最小可交互区域元素类
type BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement int32

const (
	// 标题元素
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_TITLE BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 1
	// 描述元素
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_DESC BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 2
	// 图标元素
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_ICON BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 4
	// 图片元素
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_IMAGE BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 8
	// 品牌信息
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_BRAND BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 16
	// 视频元素
	BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_VIDEO BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement = 32
)

// Enum value maps for BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement.
var (
	BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement_name = map[int32]string{
		1:  "META_ELE_TITLE",
		2:  "META_ELE_DESC",
		4:  "META_ELE_ICON",
		8:  "META_ELE_IMAGE",
		16: "META_ELE_BRAND",
		32: "META_ELE_VIDEO",
	}
	BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement_value = map[string]int32{
		"META_ELE_TITLE": 1,
		"META_ELE_DESC":  2,
		"META_ELE_ICON":  4,
		"META_ELE_IMAGE": 8,
		"META_ELE_BRAND": 16,
		"META_ELE_VIDEO": 32,
	}
)

func (x BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) Enum() *BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement {
	p := new(BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement)
	*p = x
	return p
}

func (x BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[10].Descriptor()
}

func (BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[10]
}

func (x BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement(num)
	return nil
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement.Descriptor instead.
func (BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 0}
}

// OCPM目标转化类型
type BidResponse_Ad_TransType int32

const (
	BidResponse_Ad_UNKNOWN_TYPE BidResponse_Ad_TransType = 0
	// 表单提交成功
	BidResponse_Ad_FORM_SUBMIT BidResponse_Ad_TransType = 3
	// 表单按钮点击
	BidResponse_Ad_FORM_CLICK BidResponse_Ad_TransType = 5
	// 表单有效请求，即Nginx收到表单请求打点
	BidResponse_Ad_FORM_VAILD_REQUEST BidResponse_Ad_TransType = 13
	// 表单调起按钮点击
	BidResponse_Ad_FORM_PULL_UP_CLICK BidResponse_Ad_TransType = 16
	// 应用调起
	BidResponse_Ad_APP_UP BidResponse_Ad_TransType = 71
)

// Enum value maps for BidResponse_Ad_TransType.
var (
	BidResponse_Ad_TransType_name = map[int32]string{
		0:  "UNKNOWN_TYPE",
		3:  "FORM_SUBMIT",
		5:  "FORM_CLICK",
		13: "FORM_VAILD_REQUEST",
		16: "FORM_PULL_UP_CLICK",
		71: "APP_UP",
	}
	BidResponse_Ad_TransType_value = map[string]int32{
		"UNKNOWN_TYPE":       0,
		"FORM_SUBMIT":        3,
		"FORM_CLICK":         5,
		"FORM_VAILD_REQUEST": 13,
		"FORM_PULL_UP_CLICK": 16,
		"APP_UP":             71,
	}
)

func (x BidResponse_Ad_TransType) Enum() *BidResponse_Ad_TransType {
	p := new(BidResponse_Ad_TransType)
	*p = x
	return p
}

func (x BidResponse_Ad_TransType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_TransType) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[11].Descriptor()
}

func (BidResponse_Ad_TransType) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[11]
}

func (x BidResponse_Ad_TransType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_TransType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_TransType(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_TransType.Descriptor instead.
func (BidResponse_Ad_TransType) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 0}
}

// (仅用于序章dsp)
// 物料规格
type BidResponse_Ad_MaterialInfo_MaterialPattern int32

const (
	BidResponse_Ad_MaterialInfo_MATERIAL_PATTERN_FULL_SCREEN BidResponse_Ad_MaterialInfo_MaterialPattern = 1 // 全屏
	BidResponse_Ad_MaterialInfo_MATERIAL_PATTERN_HALF_SCREEN BidResponse_Ad_MaterialInfo_MaterialPattern = 2 // 半屏
)

// Enum value maps for BidResponse_Ad_MaterialInfo_MaterialPattern.
var (
	BidResponse_Ad_MaterialInfo_MaterialPattern_name = map[int32]string{
		1: "MATERIAL_PATTERN_FULL_SCREEN",
		2: "MATERIAL_PATTERN_HALF_SCREEN",
	}
	BidResponse_Ad_MaterialInfo_MaterialPattern_value = map[string]int32{
		"MATERIAL_PATTERN_FULL_SCREEN": 1,
		"MATERIAL_PATTERN_HALF_SCREEN": 2,
	}
)

func (x BidResponse_Ad_MaterialInfo_MaterialPattern) Enum() *BidResponse_Ad_MaterialInfo_MaterialPattern {
	p := new(BidResponse_Ad_MaterialInfo_MaterialPattern)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialInfo_MaterialPattern) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialInfo_MaterialPattern) Descriptor() protoreflect.EnumDescriptor {
	return file_bes_proto_enumTypes[12].Descriptor()
}

func (BidResponse_Ad_MaterialInfo_MaterialPattern) Type() protoreflect.EnumType {
	return &file_bes_proto_enumTypes[12]
}

func (x BidResponse_Ad_MaterialInfo_MaterialPattern) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialInfo_MaterialPattern) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialInfo_MaterialPattern(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialInfo_MaterialPattern.Descriptor instead.
func (BidResponse_Ad_MaterialInfo_MaterialPattern) EnumDescriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 2, 0}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求ID，唯一标识本次请求，明文字符串
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// **** 用户信息 ****
	// 用户IP地址，点分十进制字符串
	Ip *string `protobuf:"bytes,2,opt,name=ip" json:"ip,omitempty"`
	// IPV6地址, 冒分16进制
	// 保证线上服务稳定，ipv6&ipv4 采用不同的字段下发用户IP地址
	// ipv6 仅赋值 ip6 , ipv4 仅赋值 ip
	Ip6 *string `protobuf:"bytes,42,opt,name=ip6" json:"ip6,omitempty"`
	// User-Agent
	UserAgent *string `protobuf:"bytes,3,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	// 用户分类
	UserCategory []int64         `protobuf:"varint,6,rep,name=user_category,json=userCategory" json:"user_category,omitempty"`
	UserGeoInfo  *BidRequest_Geo `protobuf:"bytes,28,opt,name=user_geo_info,json=userGeoInfo" json:"user_geo_info,omitempty"`
	// 发布商不允许的广告行业
	ExcludedProductCategory []int32 `protobuf:"varint,19,rep,packed,name=excluded_product_category,json=excludedProductCategory" json:"excluded_product_category,omitempty"`
	// 移动设备信息，当流量来自移动设备时该字段非空
	Mobile      *BidRequest_Mobile      `protobuf:"bytes,29,opt,name=mobile" json:"mobile,omitempty"`
	ArticleInfo *BidRequest_ArticleInfo `protobuf:"bytes,33,opt,name=article_info,json=articleInfo" json:"article_info,omitempty"`
	// 默认每次请求一个广告位
	Adslot []*BidRequest_AdSlot `protobuf:"bytes,20,rep,name=adslot" json:"adslot,omitempty"`
	// 此次请求所属于的流量特征包ID
	TagId []uint32 `protobuf:"varint,34,rep,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	// 与tag_id搭配使用，用于补充嗅探圈包命中的app信息
	InstalledAppIds [][]byte `protobuf:"bytes,36,rep,name=installed_app_ids,json=installedAppIds" json:"installed_app_ids,omitempty"`
	// 流量属性，代表当前流量支持的能力。
	// 是一个bitmap位图，每一位代表一个能力
	// 这里每个能力是向下兼容的，仅代表可以，不代表一定要，是否生效取决于DSP返回是否支持
	// 1 代表支持cpc投放，
	// 2 代表支持cpc计费
	// 4 代表支持OCPM投放
	// 8 代表支持OCPM计费
	AttributeInfo *uint32 `protobuf:"varint,35,opt,name=attribute_info,json=attributeInfo,def=0" json:"attribute_info,omitempty"`
	NewTtp        *int32  `protobuf:"varint,40,opt,name=new_ttp,json=newTtp" json:"new_ttp,omitempty"`
	// **** 系统使用 ****
	IsTest    *bool                   `protobuf:"varint,26,opt,name=is_test,json=isTest,def=0" json:"is_test,omitempty"`
	IsPing    *bool                   `protobuf:"varint,27,opt,name=is_ping,json=isPing,def=0" json:"is_ping,omitempty"`
	HostLimit *BidRequest_HostAdLimit `protobuf:"varint,39,opt,name=host_limit,json=hostLimit,enum=bes.BidRequest_HostAdLimit,def=1" json:"host_limit,omitempty"`
	LookLike  []*BidRequest_LookLike  `protobuf:"bytes,41,rep,name=look_like,json=lookLike" json:"look_like,omitempty"`
	// 外部dsp联合实验id
	ExExpIds []int32 `protobuf:"varint,43,rep,name=ex_exp_ids,json=exExpIds" json:"ex_exp_ids,omitempty"`
	// >0 : second bidding for media
	MediaBidType *int32 `protobuf:"varint,44,opt,name=media_bid_type,json=mediaBidType" json:"media_bid_type,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_AttributeInfo = uint32(0)
	Default_BidRequest_IsTest        = bool(false)
	Default_BidRequest_IsPing        = bool(false)
	Default_BidRequest_HostLimit     = BidRequest_NO_LIMIT
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest) GetIp6() string {
	if x != nil && x.Ip6 != nil {
		return *x.Ip6
	}
	return ""
}

func (x *BidRequest) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *BidRequest) GetUserCategory() []int64 {
	if x != nil {
		return x.UserCategory
	}
	return nil
}

func (x *BidRequest) GetUserGeoInfo() *BidRequest_Geo {
	if x != nil {
		return x.UserGeoInfo
	}
	return nil
}

func (x *BidRequest) GetExcludedProductCategory() []int32 {
	if x != nil {
		return x.ExcludedProductCategory
	}
	return nil
}

func (x *BidRequest) GetMobile() *BidRequest_Mobile {
	if x != nil {
		return x.Mobile
	}
	return nil
}

func (x *BidRequest) GetArticleInfo() *BidRequest_ArticleInfo {
	if x != nil {
		return x.ArticleInfo
	}
	return nil
}

func (x *BidRequest) GetAdslot() []*BidRequest_AdSlot {
	if x != nil {
		return x.Adslot
	}
	return nil
}

func (x *BidRequest) GetTagId() []uint32 {
	if x != nil {
		return x.TagId
	}
	return nil
}

func (x *BidRequest) GetInstalledAppIds() [][]byte {
	if x != nil {
		return x.InstalledAppIds
	}
	return nil
}

func (x *BidRequest) GetAttributeInfo() uint32 {
	if x != nil && x.AttributeInfo != nil {
		return *x.AttributeInfo
	}
	return Default_BidRequest_AttributeInfo
}

func (x *BidRequest) GetNewTtp() int32 {
	if x != nil && x.NewTtp != nil {
		return *x.NewTtp
	}
	return 0
}

func (x *BidRequest) GetIsTest() bool {
	if x != nil && x.IsTest != nil {
		return *x.IsTest
	}
	return Default_BidRequest_IsTest
}

func (x *BidRequest) GetIsPing() bool {
	if x != nil && x.IsPing != nil {
		return *x.IsPing
	}
	return Default_BidRequest_IsPing
}

func (x *BidRequest) GetHostLimit() BidRequest_HostAdLimit {
	if x != nil && x.HostLimit != nil {
		return *x.HostLimit
	}
	return Default_BidRequest_HostLimit
}

func (x *BidRequest) GetLookLike() []*BidRequest_LookLike {
	if x != nil {
		return x.LookLike
	}
	return nil
}

func (x *BidRequest) GetExExpIds() []int32 {
	if x != nil {
		return x.ExExpIds
	}
	return nil
}

func (x *BidRequest) GetMediaBidType() int32 {
	if x != nil && x.MediaBidType != nil {
		return *x.MediaBidType
	}
	return 0
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回ID，将请求中的id赋值给返回id，便于session trace
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// BES侧完成分流,
	// 对于非多广告流量只支持一个竞价广告返回
	Ad []*BidResponse_Ad `protobuf:"bytes,2,rep,name=ad" json:"ad,omitempty"`
	// **** 系统使用 ****
	// debug接口
	DebugString *string `protobuf:"bytes,3,opt,name=debug_string,json=debugString" json:"debug_string,omitempty"`
	// DSP处理时间
	ProcessingTimeMs *int32 `protobuf:"varint,4,opt,name=processing_time_ms,json=processingTimeMs" json:"processing_time_ms,omitempty"`
	// 参与竞价的最大广告数量限制
	AdsNumLimit *int32 `protobuf:"varint,5,opt,name=ads_num_limit,json=adsNumLimit" json:"ads_num_limit,omitempty"`
	// 短时屏蔽相关字段
	// 屏蔽状态
	// 0:不过滤;1:tu过滤;2:设备过滤
	BlockStatus *uint32 `protobuf:"varint,6,opt,name=block_status,json=blockStatus" json:"block_status,omitempty"`
	// 过滤结束时间
	BlockEndTime *uint64 `protobuf:"varint,7,opt,name=block_end_time,json=blockEndTime" json:"block_end_time,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetAd() []*BidResponse_Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

func (x *BidResponse) GetDebugString() string {
	if x != nil && x.DebugString != nil {
		return *x.DebugString
	}
	return ""
}

func (x *BidResponse) GetProcessingTimeMs() int32 {
	if x != nil && x.ProcessingTimeMs != nil {
		return *x.ProcessingTimeMs
	}
	return 0
}

func (x *BidResponse) GetAdsNumLimit() int32 {
	if x != nil && x.AdsNumLimit != nil {
		return *x.AdsNumLimit
	}
	return 0
}

func (x *BidResponse) GetBlockStatus() uint32 {
	if x != nil && x.BlockStatus != nil {
		return *x.BlockStatus
	}
	return 0
}

func (x *BidResponse) GetBlockEndTime() uint64 {
	if x != nil && x.BlockEndTime != nil {
		return *x.BlockEndTime
	}
	return 0
}

// **** 位置信息 ****
type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserCoordinate []*BidRequest_Geo_Coordinate `protobuf:"bytes,1,rep,name=user_coordinate,json=userCoordinate" json:"user_coordinate,omitempty"`
	UserLocation   *BidRequest_Geo_UserLocation `protobuf:"bytes,2,opt,name=user_location,json=userLocation" json:"user_location,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Geo) GetUserCoordinate() []*BidRequest_Geo_Coordinate {
	if x != nil {
		return x.UserCoordinate
	}
	return nil
}

func (x *BidRequest_Geo) GetUserLocation() *BidRequest_Geo_UserLocation {
	if x != nil {
		return x.UserLocation
	}
	return nil
}

// **** 移动设备信息 ****
type BidRequest_Mobile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []*BidRequest_Mobile_MobileID `protobuf:"bytes,13,rep,name=id" json:"id,omitempty"`
	// 设备类型
	DeviceType *BidRequest_Mobile_MobileDeviceType `protobuf:"varint,2,opt,name=device_type,json=deviceType,enum=bes.BidRequest_Mobile_MobileDeviceType" json:"device_type,omitempty"`
	Platform   *BidRequest_Mobile_OS               `protobuf:"varint,3,opt,name=platform,enum=bes.BidRequest_Mobile_OS,def=0" json:"platform,omitempty"`
	OsVersion  *BidRequest_Mobile_DeviceOsVersion  `protobuf:"bytes,4,opt,name=os_version,json=osVersion" json:"os_version,omitempty"`
	// 设备品牌
	Brand *string `protobuf:"bytes,5,opt,name=brand" json:"brand,omitempty"`
	// 设备机型
	Model *string `protobuf:"bytes,6,opt,name=model" json:"model,omitempty"`
	// SDK版本号：1.0.0
	SdkVersion *string `protobuf:"bytes,41,opt,name=sdk_version,json=sdkVersion" json:"sdk_version,omitempty"`
	// 设备屏宽
	ScreenWidth *int32 `protobuf:"varint,7,opt,name=screen_width,json=screenWidth" json:"screen_width,omitempty"`
	// 设备屏高
	ScreenHeight *int32 `protobuf:"varint,8,opt,name=screen_height,json=screenHeight" json:"screen_height,omitempty"`
	// 设备屏幕像素密度
	ScreenDensity *float32 `protobuf:"fixed32,15,opt,name=screen_density,json=screenDensity" json:"screen_density,omitempty"`
	// 运营商编号（MCC+MNC编号）
	// 例如中国移动 46000
	// 前三位是Mobile Country Code
	// 后两位是Mobile Network Code
	CarrierId           *int64                                 `protobuf:"varint,9,opt,name=carrier_id,json=carrierId" json:"carrier_id,omitempty"`
	WirelessNetworkType *BidRequest_Mobile_WirelessNetworkType `protobuf:"varint,10,opt,name=wireless_network_type,json=wirelessNetworkType,enum=bes.BidRequest_Mobile_WirelessNetworkType" json:"wireless_network_type,omitempty"`
	// 阿里系客户DSP反作弊参数，不是必填字段
	// boot_mark为系统开机时间戳，update_mark为系统最近一次更新时间戳
	// 只有IOS和Android有，buyer中对platform字段做校验后下发外部DSP
	BootMark      []byte `protobuf:"bytes,34,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`
	UpdateMark    []byte `protobuf:"bytes,35,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`
	AliUpdateMark []byte `protobuf:"bytes,36,opt,name=ali_update_mark,json=aliUpdateMark" json:"ali_update_mark,omitempty"`
	// 系统初始化时间
	InitMark         []byte                                `protobuf:"bytes,37,opt,name=init_mark,json=initMark" json:"init_mark,omitempty"`
	ForAdvertisingId []*BidRequest_Mobile_ForAdvertisingID `protobuf:"bytes,14,rep,name=for_advertising_id,json=forAdvertisingId" json:"for_advertising_id,omitempty"`
	// 移动应用信息，当流量来自App时该字段非空
	MobileApp *BidRequest_Mobile_MobileApp `protobuf:"bytes,12,opt,name=mobile_app,json=mobileApp" json:"mobile_app,omitempty"`
	// 厂商定制化ROM
	RomName []byte `protobuf:"bytes,16,opt,name=rom_name,json=romName" json:"rom_name,omitempty"`
	// 厂商定制化系统ROM版本
	RomVersion []byte `protobuf:"bytes,17,opt,name=rom_version,json=romVersion" json:"rom_version,omitempty"`
	// 拼多多归因参数V1.3
	// v1.3规则：paid=md5（系统更新时间）+md5（系统启动时间）
	Paid []byte `protobuf:"bytes,18,opt,name=paid" json:"paid,omitempty"`
	// 拼多多归因参数V1.4
	// paid=md5（系统初始化时间）+“-”+md5（系统更新时间）+“-”+md5（系统启动时间）
	PaidNew []byte `protobuf:"bytes,19,opt,name=paid_new,json=paidNew" json:"paid_new,omitempty"`
}

// Default values for BidRequest_Mobile fields.
const (
	Default_BidRequest_Mobile_Platform = BidRequest_Mobile_UNKNOWN_OS
)

func (x *BidRequest_Mobile) Reset() {
	*x = BidRequest_Mobile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Mobile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Mobile) ProtoMessage() {}

func (x *BidRequest_Mobile) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Mobile.ProtoReflect.Descriptor instead.
func (*BidRequest_Mobile) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Mobile) GetId() []*BidRequest_Mobile_MobileID {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *BidRequest_Mobile) GetDeviceType() BidRequest_Mobile_MobileDeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return BidRequest_Mobile_UNKNOWN_DEVICE
}

func (x *BidRequest_Mobile) GetPlatform() BidRequest_Mobile_OS {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return Default_BidRequest_Mobile_Platform
}

func (x *BidRequest_Mobile) GetOsVersion() *BidRequest_Mobile_DeviceOsVersion {
	if x != nil {
		return x.OsVersion
	}
	return nil
}

func (x *BidRequest_Mobile) GetBrand() string {
	if x != nil && x.Brand != nil {
		return *x.Brand
	}
	return ""
}

func (x *BidRequest_Mobile) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Mobile) GetSdkVersion() string {
	if x != nil && x.SdkVersion != nil {
		return *x.SdkVersion
	}
	return ""
}

func (x *BidRequest_Mobile) GetScreenWidth() int32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Mobile) GetScreenHeight() int32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Mobile) GetScreenDensity() float32 {
	if x != nil && x.ScreenDensity != nil {
		return *x.ScreenDensity
	}
	return 0
}

func (x *BidRequest_Mobile) GetCarrierId() int64 {
	if x != nil && x.CarrierId != nil {
		return *x.CarrierId
	}
	return 0
}

func (x *BidRequest_Mobile) GetWirelessNetworkType() BidRequest_Mobile_WirelessNetworkType {
	if x != nil && x.WirelessNetworkType != nil {
		return *x.WirelessNetworkType
	}
	return BidRequest_Mobile_UNKNOWN_NETWORK
}

func (x *BidRequest_Mobile) GetBootMark() []byte {
	if x != nil {
		return x.BootMark
	}
	return nil
}

func (x *BidRequest_Mobile) GetUpdateMark() []byte {
	if x != nil {
		return x.UpdateMark
	}
	return nil
}

func (x *BidRequest_Mobile) GetAliUpdateMark() []byte {
	if x != nil {
		return x.AliUpdateMark
	}
	return nil
}

func (x *BidRequest_Mobile) GetInitMark() []byte {
	if x != nil {
		return x.InitMark
	}
	return nil
}

func (x *BidRequest_Mobile) GetForAdvertisingId() []*BidRequest_Mobile_ForAdvertisingID {
	if x != nil {
		return x.ForAdvertisingId
	}
	return nil
}

func (x *BidRequest_Mobile) GetMobileApp() *BidRequest_Mobile_MobileApp {
	if x != nil {
		return x.MobileApp
	}
	return nil
}

func (x *BidRequest_Mobile) GetRomName() []byte {
	if x != nil {
		return x.RomName
	}
	return nil
}

func (x *BidRequest_Mobile) GetRomVersion() []byte {
	if x != nil {
		return x.RomVersion
	}
	return nil
}

func (x *BidRequest_Mobile) GetPaid() []byte {
	if x != nil {
		return x.Paid
	}
	return nil
}

func (x *BidRequest_Mobile) GetPaidNew() []byte {
	if x != nil {
		return x.PaidNew
	}
	return nil
}

// 页面文章信息
type BidRequest_ArticleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       *string  `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Title    []byte   `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`
	Category [][]byte `protobuf:"bytes,3,rep,name=category" json:"category,omitempty"`
	Label    [][]byte `protobuf:"bytes,4,rep,name=label" json:"label,omitempty"`
	AuthorId []byte   `protobuf:"bytes,5,opt,name=author_id,json=authorId" json:"author_id,omitempty"`
}

func (x *BidRequest_ArticleInfo) Reset() {
	*x = BidRequest_ArticleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_ArticleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_ArticleInfo) ProtoMessage() {}

func (x *BidRequest_ArticleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_ArticleInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_ArticleInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_ArticleInfo) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_ArticleInfo) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BidRequest_ArticleInfo) GetCategory() [][]byte {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *BidRequest_ArticleInfo) GetLabel() [][]byte {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *BidRequest_ArticleInfo) GetAuthorId() []byte {
	if x != nil {
		return x.AuthorId
	}
	return nil
}

// **** 广告位信息 ****
type BidRequest_AdSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 广告位ID, 全局唯一id
	AdBlockKey *uint64 `protobuf:"varint,1,opt,name=ad_block_key,json=adBlockKey" json:"ad_block_key,omitempty"`
	AdBlockId  []byte  `protobuf:"bytes,28,opt,name=ad_block_id,json=adBlockId" json:"ad_block_id,omitempty"`
	// 当前页面广告位顺序id，同一页面从1开始
	SequenceId *int32 `protobuf:"varint,2,opt,name=sequence_id,json=sequenceId" json:"sequence_id,omitempty"`
	// 曝光id
	Id *string `protobuf:"bytes,1001,opt,name=id" json:"id,omitempty"`
	// 展示类型
	AdslotType *int32 `protobuf:"varint,3,opt,name=adslot_type,json=adslotType" json:"adslot_type,omitempty"`
	// 宽
	Width *int32 `protobuf:"varint,4,opt,name=width" json:"width,omitempty"`
	// 高
	Height *int32 `protobuf:"varint,5,opt,name=height" json:"height,omitempty"`
	// 广告位实际宽度
	ActualWidth *int32 `protobuf:"varint,18,opt,name=actual_width,json=actualWidth" json:"actual_width,omitempty"`
	// 广告位实际高度
	ActualHeight *int32 `protobuf:"varint,19,opt,name=actual_height,json=actualHeight" json:"actual_height,omitempty"`
	// 展示位置
	SlotVisibility *int32 `protobuf:"varint,6,opt,name=slot_visibility,json=slotVisibility" json:"slot_visibility,omitempty"`
	// 发布商允许的创意类型
	CreativeType []int32 `protobuf:"varint,7,rep,packed,name=creative_type,json=creativeType" json:"creative_type,omitempty"`
	// 发布商不允许的landing page url
	ExcludedLandingPageUrl []string `protobuf:"bytes,8,rep,name=excluded_landing_page_url,json=excludedLandingPageUrl" json:"excluded_landing_page_url,omitempty"`
	// 媒体保护设置信息的ID
	PublisherSettingsListId []uint64 `protobuf:"fixed64,14,rep,name=publisher_settings_list_id,json=publisherSettingsListId" json:"publisher_settings_list_id,omitempty"`
	// 发布商设置的底价，单位分
	MinimumCpm *int32 `protobuf:"varint,9,opt,name=minimum_cpm,json=minimumCpm" json:"minimum_cpm,omitempty"`
	// 三方dsp动态底价
	DynamicMinCpm *int32 `protobuf:"varint,42,opt,name=dynamic_min_cpm,json=dynamicMinCpm" json:"dynamic_min_cpm,omitempty"`
	// 是否允许返回非原生广告创意，
	// 如果为true，则对于存在原生诉求的情况下可以返回非原生广告创意，
	// 如果为false，则必须返回原生创意
	AllowedNonNativead *bool                            `protobuf:"varint,23,opt,name=allowed_non_nativead,json=allowedNonNativead,def=1" json:"allowed_non_nativead,omitempty"`
	NativeadParam      *BidRequest_AdSlot_NativeAdParam `protobuf:"bytes,24,opt,name=nativead_param,json=nativeadParam" json:"nativead_param,omitempty"`
	// 是否为HTTPS请求
	// 如果为true，则所有资源（图片、视频等）必须以HTTPS返回
	// 注意：url字段对应协议与secure字段的值并无严格对应关系，
	//       比如，存在url协议为HTTP而secure为true的情况，
	//       因此，需要使用secure字段来决定是否以HTTPS返回资源，而不要依赖url字段
	Secure        *bool                          `protobuf:"varint,25,opt,name=secure,def=0" json:"secure,omitempty"`
	StyleInfo     []*BidRequest_AdSlot_StyleInfo `protobuf:"bytes,26,rep,name=style_info,json=styleInfo" json:"style_info,omitempty"`
	InventoryType []int32                        `protobuf:"varint,27,rep,name=inventory_type,json=inventoryType" json:"inventory_type,omitempty"` // 流量产品类型
	MaxAdNum      *int32                         `protobuf:"varint,29,opt,name=max_ad_num,json=maxAdNum" json:"max_ad_num,omitempty"`              // 广告位允许最大广告数
	CloudSettings []byte                         `protobuf:"bytes,30,opt,name=cloud_settings,json=cloudSettings" json:"cloud_settings,omitempty"`  // 云控相关字段下发
}

// Default values for BidRequest_AdSlot fields.
const (
	Default_BidRequest_AdSlot_AllowedNonNativead = bool(true)
	Default_BidRequest_AdSlot_Secure             = bool(false)
)

func (x *BidRequest_AdSlot) Reset() {
	*x = BidRequest_AdSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot) ProtoMessage() {}

func (x *BidRequest_AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_AdSlot) GetAdBlockKey() uint64 {
	if x != nil && x.AdBlockKey != nil {
		return *x.AdBlockKey
	}
	return 0
}

func (x *BidRequest_AdSlot) GetAdBlockId() []byte {
	if x != nil {
		return x.AdBlockId
	}
	return nil
}

func (x *BidRequest_AdSlot) GetSequenceId() int32 {
	if x != nil && x.SequenceId != nil {
		return *x.SequenceId
	}
	return 0
}

func (x *BidRequest_AdSlot) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_AdSlot) GetAdslotType() int32 {
	if x != nil && x.AdslotType != nil {
		return *x.AdslotType
	}
	return 0
}

func (x *BidRequest_AdSlot) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidRequest_AdSlot) GetActualWidth() int32 {
	if x != nil && x.ActualWidth != nil {
		return *x.ActualWidth
	}
	return 0
}

func (x *BidRequest_AdSlot) GetActualHeight() int32 {
	if x != nil && x.ActualHeight != nil {
		return *x.ActualHeight
	}
	return 0
}

func (x *BidRequest_AdSlot) GetSlotVisibility() int32 {
	if x != nil && x.SlotVisibility != nil {
		return *x.SlotVisibility
	}
	return 0
}

func (x *BidRequest_AdSlot) GetCreativeType() []int32 {
	if x != nil {
		return x.CreativeType
	}
	return nil
}

func (x *BidRequest_AdSlot) GetExcludedLandingPageUrl() []string {
	if x != nil {
		return x.ExcludedLandingPageUrl
	}
	return nil
}

func (x *BidRequest_AdSlot) GetPublisherSettingsListId() []uint64 {
	if x != nil {
		return x.PublisherSettingsListId
	}
	return nil
}

func (x *BidRequest_AdSlot) GetMinimumCpm() int32 {
	if x != nil && x.MinimumCpm != nil {
		return *x.MinimumCpm
	}
	return 0
}

func (x *BidRequest_AdSlot) GetDynamicMinCpm() int32 {
	if x != nil && x.DynamicMinCpm != nil {
		return *x.DynamicMinCpm
	}
	return 0
}

func (x *BidRequest_AdSlot) GetAllowedNonNativead() bool {
	if x != nil && x.AllowedNonNativead != nil {
		return *x.AllowedNonNativead
	}
	return Default_BidRequest_AdSlot_AllowedNonNativead
}

func (x *BidRequest_AdSlot) GetNativeadParam() *BidRequest_AdSlot_NativeAdParam {
	if x != nil {
		return x.NativeadParam
	}
	return nil
}

func (x *BidRequest_AdSlot) GetSecure() bool {
	if x != nil && x.Secure != nil {
		return *x.Secure
	}
	return Default_BidRequest_AdSlot_Secure
}

func (x *BidRequest_AdSlot) GetStyleInfo() []*BidRequest_AdSlot_StyleInfo {
	if x != nil {
		return x.StyleInfo
	}
	return nil
}

func (x *BidRequest_AdSlot) GetInventoryType() []int32 {
	if x != nil {
		return x.InventoryType
	}
	return nil
}

func (x *BidRequest_AdSlot) GetMaxAdNum() int32 {
	if x != nil && x.MaxAdNum != nil {
		return *x.MaxAdNum
	}
	return 0
}

func (x *BidRequest_AdSlot) GetCloudSettings() []byte {
	if x != nil {
		return x.CloudSettings
	}
	return nil
}

// 客户相似人群
type BidRequest_LookLike struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id *uint32 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	// value取值[0-10000]，越大越相似
	Value *int32 `protobuf:"varint,2,opt,name=value" json:"value,omitempty"`
}

func (x *BidRequest_LookLike) Reset() {
	*x = BidRequest_LookLike{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_LookLike) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_LookLike) ProtoMessage() {}

func (x *BidRequest_LookLike) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_LookLike.ProtoReflect.Descriptor instead.
func (*BidRequest_LookLike) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_LookLike) GetId() uint32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BidRequest_LookLike) GetValue() int32 {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return 0
}

// 经纬度信息
type BidRequest_Geo_Coordinate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地图坐标标准
	Standard *BidRequest_Geo_Coordinate_Standard `protobuf:"varint,1,opt,name=standard,enum=bes.BidRequest_Geo_Coordinate_Standard" json:"standard,omitempty"`
	// 维度
	Latitude *float32 `protobuf:"fixed32,2,opt,name=latitude" json:"latitude,omitempty"`
	// 经度
	Longitude *float32 `protobuf:"fixed32,3,opt,name=longitude" json:"longitude,omitempty"`
}

func (x *BidRequest_Geo_Coordinate) Reset() {
	*x = BidRequest_Geo_Coordinate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo_Coordinate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo_Coordinate) ProtoMessage() {}

func (x *BidRequest_Geo_Coordinate) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo_Coordinate.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo_Coordinate) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Geo_Coordinate) GetStandard() BidRequest_Geo_Coordinate_Standard {
	if x != nil && x.Standard != nil {
		return *x.Standard
	}
	return BidRequest_Geo_Coordinate_BD_09
}

func (x *BidRequest_Geo_Coordinate) GetLatitude() float32 {
	if x != nil && x.Latitude != nil {
		return *x.Latitude
	}
	return 0
}

func (x *BidRequest_Geo_Coordinate) GetLongitude() float32 {
	if x != nil && x.Longitude != nil {
		return *x.Longitude
	}
	return 0
}

// 用户位置信息
type BidRequest_Geo_UserLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 省份
	Province *string `protobuf:"bytes,1,opt,name=province" json:"province,omitempty"`
	// 城市
	City *string `protobuf:"bytes,2,opt,name=city" json:"city,omitempty"`
	// 区县
	District *string `protobuf:"bytes,3,opt,name=district" json:"district,omitempty"`
	// 街道
	Street *string `protobuf:"bytes,4,opt,name=street" json:"street,omitempty"`
}

func (x *BidRequest_Geo_UserLocation) Reset() {
	*x = BidRequest_Geo_UserLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo_UserLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo_UserLocation) ProtoMessage() {}

func (x *BidRequest_Geo_UserLocation) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo_UserLocation.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo_UserLocation) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Geo_UserLocation) GetProvince() string {
	if x != nil && x.Province != nil {
		return *x.Province
	}
	return ""
}

func (x *BidRequest_Geo_UserLocation) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *BidRequest_Geo_UserLocation) GetDistrict() string {
	if x != nil && x.District != nil {
		return *x.District
	}
	return ""
}

func (x *BidRequest_Geo_UserLocation) GetStreet() string {
	if x != nil && x.Street != nil {
		return *x.Street
	}
	return ""
}

// 新版移动设备序列号标识字段。允许同时存储多个序列号
type BidRequest_Mobile_MobileID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type              *BidRequest_Mobile_MobileID_IDType `protobuf:"varint,1,opt,name=type,enum=bes.BidRequest_Mobile_MobileID_IDType" json:"type,omitempty"`         // 序列号类型
	Id                *string                            `protobuf:"bytes,2,opt,name=id" json:"id,omitempty"`                                                         // 序列号
	GenerateTimestamp *uint64                            `protobuf:"varint,3,opt,name=generate_timestamp,json=generateTimestamp" json:"generate_timestamp,omitempty"` // id生成时间，单位秒（仅CAID有效）
	Version           []byte                             `protobuf:"bytes,4,opt,name=version" json:"version,omitempty"`                                               // id版本(仅CAID有效）
	Vendor            *uint32                            `protobuf:"varint,5,opt,name=vendor" json:"vendor,omitempty"`                                                // id供应商，0为热云，1为信通院（仅CAID有效
}

func (x *BidRequest_Mobile_MobileID) Reset() {
	*x = BidRequest_Mobile_MobileID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Mobile_MobileID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Mobile_MobileID) ProtoMessage() {}

func (x *BidRequest_Mobile_MobileID) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Mobile_MobileID.ProtoReflect.Descriptor instead.
func (*BidRequest_Mobile_MobileID) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *BidRequest_Mobile_MobileID) GetType() BidRequest_Mobile_MobileID_IDType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Mobile_MobileID_UNKNOWN
}

func (x *BidRequest_Mobile_MobileID) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Mobile_MobileID) GetGenerateTimestamp() uint64 {
	if x != nil && x.GenerateTimestamp != nil {
		return *x.GenerateTimestamp
	}
	return 0
}

func (x *BidRequest_Mobile_MobileID) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *BidRequest_Mobile_MobileID) GetVendor() uint32 {
	if x != nil && x.Vendor != nil {
		return *x.Vendor
	}
	return 0
}

// 移动操作系统版本号
// 例如 Android 2.1, major, micro分别是2,1
// 例如 Iphone 4.2.1，major, minor, micro分别是4,2,1
type BidRequest_Mobile_DeviceOsVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OsVersionMajor *int32 `protobuf:"varint,1,opt,name=os_version_major,json=osVersionMajor" json:"os_version_major,omitempty"`
	OsVersionMinor *int32 `protobuf:"varint,2,opt,name=os_version_minor,json=osVersionMinor" json:"os_version_minor,omitempty"`
	OsVersionMicro *int32 `protobuf:"varint,3,opt,name=os_version_micro,json=osVersionMicro" json:"os_version_micro,omitempty"`
}

func (x *BidRequest_Mobile_DeviceOsVersion) Reset() {
	*x = BidRequest_Mobile_DeviceOsVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Mobile_DeviceOsVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Mobile_DeviceOsVersion) ProtoMessage() {}

func (x *BidRequest_Mobile_DeviceOsVersion) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Mobile_DeviceOsVersion.ProtoReflect.Descriptor instead.
func (*BidRequest_Mobile_DeviceOsVersion) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 1}
}

func (x *BidRequest_Mobile_DeviceOsVersion) GetOsVersionMajor() int32 {
	if x != nil && x.OsVersionMajor != nil {
		return *x.OsVersionMajor
	}
	return 0
}

func (x *BidRequest_Mobile_DeviceOsVersion) GetOsVersionMinor() int32 {
	if x != nil && x.OsVersionMinor != nil {
		return *x.OsVersionMinor
	}
	return 0
}

func (x *BidRequest_Mobile_DeviceOsVersion) GetOsVersionMicro() int32 {
	if x != nil && x.OsVersionMicro != nil {
		return *x.OsVersionMicro
	}
	return 0
}

// 新版移动设备用户识别号字段，允许多种类型同时存在
type BidRequest_Mobile_ForAdvertisingID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type *BidRequest_Mobile_ForAdvertisingID_IDType `protobuf:"varint,1,opt,name=type,enum=bes.BidRequest_Mobile_ForAdvertisingID_IDType" json:"type,omitempty"` // ID类型
	Id   *string                                    `protobuf:"bytes,2,opt,name=id" json:"id,omitempty"`                                                         // ID内容
}

func (x *BidRequest_Mobile_ForAdvertisingID) Reset() {
	*x = BidRequest_Mobile_ForAdvertisingID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Mobile_ForAdvertisingID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Mobile_ForAdvertisingID) ProtoMessage() {}

func (x *BidRequest_Mobile_ForAdvertisingID) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Mobile_ForAdvertisingID.ProtoReflect.Descriptor instead.
func (*BidRequest_Mobile_ForAdvertisingID) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 2}
}

func (x *BidRequest_Mobile_ForAdvertisingID) GetType() BidRequest_Mobile_ForAdvertisingID_IDType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Mobile_ForAdvertisingID_UNKNOWN
}

func (x *BidRequest_Mobile_ForAdvertisingID) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

// **** 移动应用信息 ****
type BidRequest_Mobile_MobileApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 百度移动联盟为该App分配的app id
	AppId *string `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	// 如果来自苹果商店，则直接是app-store id
	// 如果来自Android设备，则是package的全名
	AppBundleId *string `protobuf:"bytes,2,opt,name=app_bundle_id,json=appBundleId" json:"app_bundle_id,omitempty"`
	// App应用分类
	AppCategory *int32 `protobuf:"varint,3,opt,name=app_category,json=appCategory" json:"app_category,omitempty"`
	// App开发者ID
	AppPublisherId *int32 `protobuf:"varint,4,opt,name=app_publisher_id,json=appPublisherId" json:"app_publisher_id,omitempty"`
	// App允许的交互类型
	AppInteractionType []BidRequest_Mobile_MobileApp_AppInteractionType `protobuf:"varint,5,rep,name=app_interaction_type,json=appInteractionType,enum=bes.BidRequest_Mobile_MobileApp_AppInteractionType" json:"app_interaction_type,omitempty"`
	// app 版本号
	AppVersion *string `protobuf:"bytes,6,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
}

func (x *BidRequest_Mobile_MobileApp) Reset() {
	*x = BidRequest_Mobile_MobileApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Mobile_MobileApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Mobile_MobileApp) ProtoMessage() {}

func (x *BidRequest_Mobile_MobileApp) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Mobile_MobileApp.ProtoReflect.Descriptor instead.
func (*BidRequest_Mobile_MobileApp) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 1, 3}
}

func (x *BidRequest_Mobile_MobileApp) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *BidRequest_Mobile_MobileApp) GetAppBundleId() string {
	if x != nil && x.AppBundleId != nil {
		return *x.AppBundleId
	}
	return ""
}

func (x *BidRequest_Mobile_MobileApp) GetAppCategory() int32 {
	if x != nil && x.AppCategory != nil {
		return *x.AppCategory
	}
	return 0
}

func (x *BidRequest_Mobile_MobileApp) GetAppPublisherId() int32 {
	if x != nil && x.AppPublisherId != nil {
		return *x.AppPublisherId
	}
	return 0
}

func (x *BidRequest_Mobile_MobileApp) GetAppInteractionType() []BidRequest_Mobile_MobileApp_AppInteractionType {
	if x != nil {
		return x.AppInteractionType
	}
	return nil
}

func (x *BidRequest_Mobile_MobileApp) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

// 原生广告诉求参数，信息流单样式表达
type BidRequest_AdSlot_NativeAdParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 按照位图设置相应的位为1，不需要请求的位保持0
	// 示例：原生广告必须包含标题和图标，则required_fields = (0x1 | 0x8) = 9
	RequiredFields []int64 `protobuf:"varint,1,rep,name=required_fields,json=requiredFields" json:"required_fields,omitempty"`
	// 标题最大长度
	TitleMaxLength *int32 `protobuf:"varint,2,opt,name=title_max_length,json=titleMaxLength" json:"title_max_length,omitempty"`
	// 描述最大长度
	DescMaxLength *int32 `protobuf:"varint,3,opt,name=desc_max_length,json=descMaxLength" json:"desc_max_length,omitempty"`
	// 广告主logo或图标的宽高、形状要求
	LogoIcon *BidRequest_AdSlot_NativeAdParam_ImageEle `protobuf:"bytes,4,opt,name=logo_icon,json=logoIcon" json:"logo_icon,omitempty"`
	// 主题图的宽高、形状要求
	Image *BidRequest_AdSlot_NativeAdParam_ImageEle `protobuf:"bytes,5,opt,name=image" json:"image,omitempty"`
	// 主题图数量，三图样式时image_num为3
	ImageNum *int32 `protobuf:"varint,6,opt,name=image_num,json=imageNum" json:"image_num,omitempty"`
}

func (x *BidRequest_AdSlot_NativeAdParam) Reset() {
	*x = BidRequest_AdSlot_NativeAdParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_NativeAdParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_NativeAdParam) ProtoMessage() {}

func (x *BidRequest_AdSlot_NativeAdParam) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_NativeAdParam.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_NativeAdParam) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *BidRequest_AdSlot_NativeAdParam) GetRequiredFields() []int64 {
	if x != nil {
		return x.RequiredFields
	}
	return nil
}

func (x *BidRequest_AdSlot_NativeAdParam) GetTitleMaxLength() int32 {
	if x != nil && x.TitleMaxLength != nil {
		return *x.TitleMaxLength
	}
	return 0
}

func (x *BidRequest_AdSlot_NativeAdParam) GetDescMaxLength() int32 {
	if x != nil && x.DescMaxLength != nil {
		return *x.DescMaxLength
	}
	return 0
}

func (x *BidRequest_AdSlot_NativeAdParam) GetLogoIcon() *BidRequest_AdSlot_NativeAdParam_ImageEle {
	if x != nil {
		return x.LogoIcon
	}
	return nil
}

func (x *BidRequest_AdSlot_NativeAdParam) GetImage() *BidRequest_AdSlot_NativeAdParam_ImageEle {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidRequest_AdSlot_NativeAdParam) GetImageNum() int32 {
	if x != nil && x.ImageNum != nil {
		return *x.ImageNum
	}
	return 0
}

// 广告样式信息，多样式请求，多样式请求优先级高于单样式
type BidRequest_AdSlot_StyleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 样式类型
	// 28 大图底部文字
	// 29 大图上部文字
	// 30 大图上部文字 + logo
	// 31 大图两行文字
	// 32 大图无文字
	// 33 左图右文
	// 34 右图左文
	// 35 三图
	// 36 三图 + logo
	// 37 横版视频
	// 41 竖版视频
	// 100 激励视频横版
	// 102 激励视频竖版
	StyleType *int32 `protobuf:"varint,1,opt,name=style_type,json=styleType" json:"style_type,omitempty"`
	// ad样式元素要求
	AdStyle *BidRequest_AdSlot_StyleInfo_AdStyle `protobuf:"bytes,2,opt,name=ad_style,json=adStyle" json:"ad_style,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo) Reset() {
	*x = BidRequest_AdSlot_StyleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1}
}

func (x *BidRequest_AdSlot_StyleInfo) GetStyleType() int32 {
	if x != nil && x.StyleType != nil {
		return *x.StyleType
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo) GetAdStyle() *BidRequest_AdSlot_StyleInfo_AdStyle {
	if x != nil {
		return x.AdStyle
	}
	return nil
}

type BidRequest_AdSlot_NativeAdParam_ImageEle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 宽
	Width *int32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`
	// 高
	Height *int32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	// 形状，
	// 0没有形状要求，
	// 1矩形，
	// 2圆形，
	// 3半圆形
	Shape *int32 `protobuf:"varint,3,opt,name=shape,def=0" json:"shape,omitempty"`
}

// Default values for BidRequest_AdSlot_NativeAdParam_ImageEle fields.
const (
	Default_BidRequest_AdSlot_NativeAdParam_ImageEle_Shape = int32(0)
)

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) Reset() {
	*x = BidRequest_AdSlot_NativeAdParam_ImageEle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_NativeAdParam_ImageEle) ProtoMessage() {}

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_NativeAdParam_ImageEle.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_NativeAdParam_ImageEle) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 0, 0}
}

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidRequest_AdSlot_NativeAdParam_ImageEle) GetShape() int32 {
	if x != nil && x.Shape != nil {
		return *x.Shape
	}
	return Default_BidRequest_AdSlot_NativeAdParam_ImageEle_Shape
}

// ad：表达广告主投放意图的最小单元
// meta：广告主呈现给用户的最小交互单元
// metagroup：相同的meta归类为metagroup
// element：组成meta的元素素材，包括title、desc、icon、image等
type BidRequest_AdSlot_StyleInfo_AdStyle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ad的MetaStyleGroup的描述
	MetaStyleGroup []*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup `protobuf:"bytes,3,rep,name=meta_style_group,json=metaStyleGroup" json:"meta_style_group,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle) GetMetaStyleGroup() []*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup {
	if x != nil {
		return x.MetaStyleGroup
	}
	return nil
}

// 文本元素的描述
type BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文本的最小字符数，建议填写，非必填，若不填写，则无要求
	MinLen *int32 `protobuf:"varint,1,opt,name=min_len,json=minLen" json:"min_len,omitempty"`
	// 文本的最大字符数，建议填写，非必填，若不填写，则无要求
	MaxLen *int32 `protobuf:"varint,2,opt,name=max_len,json=maxLen" json:"max_len,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 0}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) GetMinLen() int32 {
	if x != nil && x.MinLen != nil {
		return *x.MinLen
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement) GetMaxLen() int32 {
	if x != nil && x.MaxLen != nil {
		return *x.MaxLen
	}
	return 0
}

// 图片元素的描述
type BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 图片宽，像素值
	Width *int32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`
	// 图片高，像素值
	Height *int32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 1}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

// 视频元素的描述
type BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 视频宽，像素值
	Width *int32 `protobuf:"varint,1,opt,name=width" json:"width,omitempty"`
	// 视频高，像素值
	Height *int32 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	// 视频文件大小，单位Byte
	Size *int32 `protobuf:"varint,3,opt,name=size" json:"size,omitempty"`
	// 视频编码， 比如 h.264
	Encoding []string `protobuf:"bytes,4,rep,name=encoding" json:"encoding,omitempty"`
	// 视频最小比特率，单位 kbps
	MinBitrate *int32 `protobuf:"varint,5,opt,name=min_bitrate,json=minBitrate" json:"min_bitrate,omitempty"`
	// 视频最大比特率，单位 kbps
	MaxBitrate *int32 `protobuf:"varint,6,opt,name=max_bitrate,json=maxBitrate" json:"max_bitrate,omitempty"`
	// 允许的最大播放时长
	MaxDuration *int32 `protobuf:"varint,7,opt,name=max_duration,json=maxDuration" json:"max_duration,omitempty"`
	// 允许的最小播放时长
	MinDuration *int32 `protobuf:"varint,8,opt,name=min_duration,json=minDuration" json:"min_duration,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 2}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetEncoding() []string {
	if x != nil {
		return x.Encoding
	}
	return nil
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetMinBitrate() int32 {
	if x != nil && x.MinBitrate != nil {
		return *x.MinBitrate
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetMaxBitrate() int32 {
	if x != nil && x.MaxBitrate != nil {
		return *x.MaxBitrate
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement) GetMinDuration() int32 {
	if x != nil && x.MinDuration != nil {
		return *x.MinDuration
	}
	return 0
}

// 最小交互区域元素诉求描述
type BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 元素诉求类型的bit位描述,
	// 例如某交互区域，由title和image元素组成
	// 则此时required_elements为9（1|8）,具体的title和image诉求需要
	// 通过title_ele和image_ele进行获取
	RequiredElements *int64 `protobuf:"varint,1,opt,name=required_elements,json=requiredElements" json:"required_elements,omitempty"`
	// title元素
	TitleEle []*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement `protobuf:"bytes,2,rep,name=title_ele,json=titleEle" json:"title_ele,omitempty"`
	// desc元素
	DescEle []*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement `protobuf:"bytes,3,rep,name=desc_ele,json=descEle" json:"desc_ele,omitempty"`
	// icon元素
	IconEle []*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement `protobuf:"bytes,4,rep,name=icon_ele,json=iconEle" json:"icon_ele,omitempty"`
	// image元素
	ImageEle []*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement `protobuf:"bytes,5,rep,name=image_ele,json=imageEle" json:"image_ele,omitempty"`
	// video元素
	VideoEle []*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement `protobuf:"bytes,6,rep,name=video_ele,json=videoEle" json:"video_ele,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 3}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetRequiredElements() int64 {
	if x != nil && x.RequiredElements != nil {
		return *x.RequiredElements
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetTitleEle() []*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement {
	if x != nil {
		return x.TitleEle
	}
	return nil
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetDescEle() []*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement {
	if x != nil {
		return x.DescEle
	}
	return nil
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetIconEle() []*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement {
	if x != nil {
		return x.IconEle
	}
	return nil
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetImageEle() []*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement {
	if x != nil {
		return x.ImageEle
	}
	return nil
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle) GetVideoEle() []*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement {
	if x != nil {
		return x.VideoEle
	}
	return nil
}

// 将诉求元素相同的MetaStyle按照MetaStyleGroup进行分类
type BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 该metagroup下面meta的个数
	Num *int32 `protobuf:"varint,1,opt,name=num" json:"num,omitempty"`
	// metagroup下面meta的具体描述
	MetaStyle *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle `protobuf:"bytes,2,opt,name=meta_style,json=metaStyle" json:"meta_style,omitempty"`
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) Reset() {
	*x = BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) ProtoMessage() {}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{0, 3, 1, 0, 4}
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) GetNum() int32 {
	if x != nil && x.Num != nil {
		return *x.Num
	}
	return 0
}

func (x *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup) GetMetaStyle() *BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle {
	if x != nil {
		return x.MetaStyle
	}
	return nil
}

// **** 竞价广告信息 ****
type BidResponse_Ad struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// 广告位顺序ID
	SequenceId *int32 `protobuf:"varint,1,opt,name=sequence_id,json=sequenceId" json:"sequence_id,omitempty"`
	// 对应的曝光ID
	Impid *string `protobuf:"bytes,1002,opt,name=impid" json:"impid,omitempty"`
	// 创意ID
	// 对于BES托管的静态创意（dsp将创意上传到BES, BES进行广告渲染），
	// creative_id唯一标识DSP上传到BES的每个创意（目前仅支持一个创意）。
	// 对于动态创意，creative_id唯一标识html snippet。BES会对动态创
	// 意的物料、监测地址及landing page等进行审核。DSP应保证含有相同物料、监测地址及landing page
	// 的html_snippet的snippet id相同，避免重复审核。但当html snippet中的物料、监测地址及landing page
	// 发生改变时，需要生成新的creative_id。
	// 对于关键词，creative_id唯一标识关键词。
	// 对于非BES托管的静态创意，creative_id唯一标识该静态创意
	CreativeId *int64 `protobuf:"varint,2,opt,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	// 针对淘系（例如手淘）返回的字符串类型的创意ID，buyer 内部签名后，赋值至cretive_id
	CreativeIdStr *string `protobuf:"bytes,37,opt,name=creative_id_str,json=creativeIdStr" json:"creative_id_str,omitempty"`
	// html_snippet字段仅在返回动态创意时使用。DSP将拼装完成的
	// 创意及其物料拼装到html中，返回给BES。
	// BES经过判断，认为*拥有html_snippet字段的响应*属于动态创意，
	// 并通过此类字段获取必须的物料信息。相应数据中如遗漏字段，则
	// BES不能保证其参与竞价。
	//
	// 如何获知点击信息:
	// html snippet代码。该字段中需要填充click url的位置应填充
	// 宏%%CLICK_URL_{N}%%（这里{N}从0开始，应使用具体的序号代替，
	// 并与target_url中的顺序一致），并将click url填写至
	// target_url字段。BES会根据DSP的target_url
	// 构建最终click url之后，用其替换该宏。
	// 如下html中包含两个创意，则需要注册两个宏%%CLICK_URL_0%%和
	// %%CLICK_URL_1%%。并在target_url字段中顺序赋值。
	// std::string html("<BODY>...<a href="%%CLICK_URL_0%%"/>.."
	//  "<a href="%%CLICK_URL_1%%"/>...</BODY>"
	// ad.set_html_snippet(html);
	// ad.add_target_url("http://click.dsp.com?idea=ad1...");
	// ad.add_target_url("http://click.dsp.com?idea=ad2...");
	// 宏的错误（如顺序、遗漏等）或者target_url的赋值错
	// 误都会导致BES对target_url填充出错。
	//
	// 如何获知竞价后的计价信息:
	// 如DSP需要获知竞价成功后创意的cpm，可在期望的monitor_url
	// 字段特定位置添加宏%%PRICE%%。BES通过替换会使用cpm替换该宏。
	// 例:
	// http://wins.dsp.com?key1=val1&&cpm=%%PRICE%%...
	// 仅动态创意需要填充
	HtmlSnippet *string `protobuf:"bytes,7,opt,name=html_snippet,json=htmlSnippet" json:"html_snippet,omitempty"`
	// 广告主id。
	// 动态创意及非BES托管的静态创意需要填充
	// 动态创意要求一个html snippet的所有广告属于同一个广告主。
	AdvertiserId *uint64 `protobuf:"varint,8,opt,name=advertiser_id,json=advertiserId" json:"advertiser_id,omitempty"`
	// 物料尺寸 - 宽度。需与请求中的尺寸一致
	// 动态创意及非BES托管的静态创意需要填充
	Width *int32 `protobuf:"varint,9,opt,name=width" json:"width,omitempty"`
	// 物料尺寸 - 高度。需与请求中的尺寸一致
	// 动态创意及非BES托管的静态创意需要填充
	Height *int32 `protobuf:"varint,10,opt,name=height" json:"height,omitempty"`
	// 创意所属行业的行业id。本字段的意义与静态创意中入库物料所需
	// 行业id相同。
	// 动态创意及非BES托管的静态创意需要填充
	Category *int32 `protobuf:"varint,11,opt,name=category" json:"category,omitempty"`
	// 创意的物料类型
	// 动态创意及非BES托管的静态创意需要填充
	Type *int32 `protobuf:"varint,12,opt,name=type" json:"type,omitempty"`
	// 创意的landing page。要求所有创意的landing page拥有相同的域，
	// 同时landing page应为target_url的最后一次跳转。
	// 注意: 这里仅填landing page的domain信息即可。如:
	// http://landing_page.advertiser.com/example.php?param1=...
	// 如上url的landing page应填入landing_page.advertiser.com。
	// 动态创意及非BES托管的静态创意需要填充
	LandingPage *string `protobuf:"bytes,13,opt,name=landing_page,json=landingPage" json:"landing_page,omitempty"`
	// 跳转厂商应用商店的下载链接
	AppStoreLink []byte `protobuf:"bytes,36,opt,name=app_store_link,json=appStoreLink" json:"app_store_link,omitempty"`
	// 创意的click url。响应中含有多个创意的情况下，每个创意click
	// url的顺序应与创意在html snippet中的顺序一致。BES将顺序进行
	// click url的替换。
	// 如该顺序不正确，将引发点击的统计偏差。
	// 动态创意及非BES托管的静态创意需要填充
	TargetUrl []string `protobuf:"bytes,14,rep,name=target_url,json=targetUrl" json:"target_url,omitempty"`
	// 曝光监测。
	// 非BES托管的静态创意需要填充
	MonitorUrls []string `protobuf:"bytes,17,rep,name=monitor_urls,json=monitorUrls" json:"monitor_urls,omitempty"`
	// 竞胜通知。
	Nurl *string `protobuf:"bytes,32,opt,name=nurl" json:"nurl,omitempty"`
	// 竞败通知。
	Lurl *string `protobuf:"bytes,35,opt,name=lurl" json:"lurl,omitempty"`
	// 最高竞价，单位分
	MaxCpm *int32 `protobuf:"varint,3,opt,name=max_cpm,json=maxCpm" json:"max_cpm,omitempty"`
	// 扩展参数
	Extdata *string `protobuf:"bytes,5,opt,name=extdata" json:"extdata,omitempty"`
	// 替换宏 %%EXT1%%
	Ext1 *string `protobuf:"bytes,23,opt,name=ext1" json:"ext1,omitempty"`
	// 替换宏 %%EXT2%%
	Ext2 *string `protobuf:"bytes,24,opt,name=ext2" json:"ext2,omitempty"`
	// 替换宏 %%EXT3%%
	Ext3         *string                      `protobuf:"bytes,25,opt,name=ext3" json:"ext3,omitempty"`
	DeeplinkInfo *BidResponse_Ad_DeeplinkInfo `protobuf:"bytes,21,opt,name=deeplink_info,json=deeplinkInfo" json:"deeplink_info,omitempty"`
	// 仅非BES托管的原生广告填写，单样式信息流创意素材返回
	NativeAd     *BidResponse_Ad_NativeAd       `protobuf:"bytes,22,opt,name=native_ad,json=nativeAd" json:"native_ad,omitempty"`
	MaterialInfo []*BidResponse_Ad_MaterialInfo `protobuf:"bytes,26,rep,name=material_info,json=materialInfo" json:"material_info,omitempty"`
	// 对应StyleInfo返回的元素信息，多样式创意返回
	MetaInfoGroup []*BidResponse_Ad_MetaInfoGroup `protobuf:"bytes,27,rep,name=meta_info_group,json=metaInfoGroup" json:"meta_info_group,omitempty"`
	DownloadInfo  *BidResponse_Ad_DownloadInfo    `protobuf:"bytes,28,opt,name=download_info,json=downloadInfo" json:"download_info,omitempty"`
	// 表示预算类型，cpc/cpm
	// 0是CPM投放
	// 1是CPC投放
	// 4是OCPM投放
	BudgetType *int32 `protobuf:"varint,30,opt,name=budget_type,json=budgetType,def=0" json:"budget_type,omitempty"`
	// cpc预算广告主出价
	Bid *int32 `protobuf:"varint,31,opt,name=bid,def=0" json:"bid,omitempty"`
	// 请求为多选创意时，返回的目标创意类型, 对应请求中的StyleInfo.style_type
	StyleType *int32 `protobuf:"varint,33,opt,name=style_type,json=styleType" json:"style_type,omitempty"`
	// 表示计费类型
	// 0是CPM计费
	// 1是CPC计费
	// 2是OCPM计费
	ChargeType *int32 `protobuf:"varint,34,opt,name=charge_type,json=chargeType,def=0" json:"charge_type,omitempty"`
	// OCPM预算广告主出价
	Obid      *int32                    `protobuf:"varint,38,opt,name=obid,def=0" json:"obid,omitempty"`
	TransType *BidResponse_Ad_TransType `protobuf:"varint,39,opt,name=trans_type,json=transType,enum=bes.BidResponse_Ad_TransType" json:"trans_type,omitempty"`
	// OCPM广告主计划id
	PlanId *int32 `protobuf:"varint,40,opt,name=plan_id,json=planId" json:"plan_id,omitempty"` // 当前最大index 为 40,每次新增时此处需同步
}

// Default values for BidResponse_Ad fields.
const (
	Default_BidResponse_Ad_BudgetType = int32(0)
	Default_BidResponse_Ad_Bid        = int32(0)
	Default_BidResponse_Ad_ChargeType = int32(0)
	Default_BidResponse_Ad_Obid       = int32(0)
)

func (x *BidResponse_Ad) Reset() {
	*x = BidResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad) ProtoMessage() {}

func (x *BidResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_Ad) GetSequenceId() int32 {
	if x != nil && x.SequenceId != nil {
		return *x.SequenceId
	}
	return 0
}

func (x *BidResponse_Ad) GetImpid() string {
	if x != nil && x.Impid != nil {
		return *x.Impid
	}
	return ""
}

func (x *BidResponse_Ad) GetCreativeId() int64 {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return 0
}

func (x *BidResponse_Ad) GetCreativeIdStr() string {
	if x != nil && x.CreativeIdStr != nil {
		return *x.CreativeIdStr
	}
	return ""
}

func (x *BidResponse_Ad) GetHtmlSnippet() string {
	if x != nil && x.HtmlSnippet != nil {
		return *x.HtmlSnippet
	}
	return ""
}

func (x *BidResponse_Ad) GetAdvertiserId() uint64 {
	if x != nil && x.AdvertiserId != nil {
		return *x.AdvertiserId
	}
	return 0
}

func (x *BidResponse_Ad) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Ad) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidResponse_Ad) GetCategory() int32 {
	if x != nil && x.Category != nil {
		return *x.Category
	}
	return 0
}

func (x *BidResponse_Ad) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidResponse_Ad) GetLandingPage() string {
	if x != nil && x.LandingPage != nil {
		return *x.LandingPage
	}
	return ""
}

func (x *BidResponse_Ad) GetAppStoreLink() []byte {
	if x != nil {
		return x.AppStoreLink
	}
	return nil
}

func (x *BidResponse_Ad) GetTargetUrl() []string {
	if x != nil {
		return x.TargetUrl
	}
	return nil
}

func (x *BidResponse_Ad) GetMonitorUrls() []string {
	if x != nil {
		return x.MonitorUrls
	}
	return nil
}

func (x *BidResponse_Ad) GetNurl() string {
	if x != nil && x.Nurl != nil {
		return *x.Nurl
	}
	return ""
}

func (x *BidResponse_Ad) GetLurl() string {
	if x != nil && x.Lurl != nil {
		return *x.Lurl
	}
	return ""
}

func (x *BidResponse_Ad) GetMaxCpm() int32 {
	if x != nil && x.MaxCpm != nil {
		return *x.MaxCpm
	}
	return 0
}

func (x *BidResponse_Ad) GetExtdata() string {
	if x != nil && x.Extdata != nil {
		return *x.Extdata
	}
	return ""
}

func (x *BidResponse_Ad) GetExt1() string {
	if x != nil && x.Ext1 != nil {
		return *x.Ext1
	}
	return ""
}

func (x *BidResponse_Ad) GetExt2() string {
	if x != nil && x.Ext2 != nil {
		return *x.Ext2
	}
	return ""
}

func (x *BidResponse_Ad) GetExt3() string {
	if x != nil && x.Ext3 != nil {
		return *x.Ext3
	}
	return ""
}

func (x *BidResponse_Ad) GetDeeplinkInfo() *BidResponse_Ad_DeeplinkInfo {
	if x != nil {
		return x.DeeplinkInfo
	}
	return nil
}

func (x *BidResponse_Ad) GetNativeAd() *BidResponse_Ad_NativeAd {
	if x != nil {
		return x.NativeAd
	}
	return nil
}

func (x *BidResponse_Ad) GetMaterialInfo() []*BidResponse_Ad_MaterialInfo {
	if x != nil {
		return x.MaterialInfo
	}
	return nil
}

func (x *BidResponse_Ad) GetMetaInfoGroup() []*BidResponse_Ad_MetaInfoGroup {
	if x != nil {
		return x.MetaInfoGroup
	}
	return nil
}

func (x *BidResponse_Ad) GetDownloadInfo() *BidResponse_Ad_DownloadInfo {
	if x != nil {
		return x.DownloadInfo
	}
	return nil
}

func (x *BidResponse_Ad) GetBudgetType() int32 {
	if x != nil && x.BudgetType != nil {
		return *x.BudgetType
	}
	return Default_BidResponse_Ad_BudgetType
}

func (x *BidResponse_Ad) GetBid() int32 {
	if x != nil && x.Bid != nil {
		return *x.Bid
	}
	return Default_BidResponse_Ad_Bid
}

func (x *BidResponse_Ad) GetStyleType() int32 {
	if x != nil && x.StyleType != nil {
		return *x.StyleType
	}
	return 0
}

func (x *BidResponse_Ad) GetChargeType() int32 {
	if x != nil && x.ChargeType != nil {
		return *x.ChargeType
	}
	return Default_BidResponse_Ad_ChargeType
}

func (x *BidResponse_Ad) GetObid() int32 {
	if x != nil && x.Obid != nil {
		return *x.Obid
	}
	return Default_BidResponse_Ad_Obid
}

func (x *BidResponse_Ad) GetTransType() BidResponse_Ad_TransType {
	if x != nil && x.TransType != nil {
		return *x.TransType
	}
	return BidResponse_Ad_UNKNOWN_TYPE
}

func (x *BidResponse_Ad) GetPlanId() int32 {
	if x != nil && x.PlanId != nil {
		return *x.PlanId
	}
	return 0
}

// APP 唤醒信息
// 非BES托管的静态创意需要填充
// 目前只有交互类型为应用唤醒时需要填写该字段
type BidResponse_Ad_DeeplinkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用唤醒打开页面
	DeeplinkUrl *string `protobuf:"bytes,1,opt,name=deeplink_url,json=deeplinkUrl" json:"deeplink_url,omitempty"`
	// 应用唤醒版本
	AppVersion *uint32 `protobuf:"varint,2,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	// 应用唤醒退化链接
	FallbackUrl *string `protobuf:"bytes,3,opt,name=fallback_url,json=fallbackUrl" json:"fallback_url,omitempty"`
	// 应用唤醒退化链接类型: LANDING_PAGE = 1, DOWNLOAD = 2
	FallbackType *uint32 `protobuf:"varint,4,opt,name=fallback_type,json=fallbackType" json:"fallback_type,omitempty"`
	// 应用包名
	AppBundleId []byte `protobuf:"bytes,5,opt,name=app_bundle_id,json=appBundleId" json:"app_bundle_id,omitempty"`
	// 开发者名称
	Publisher []byte `protobuf:"bytes,6,opt,name=publisher" json:"publisher,omitempty"`
	// 版本号, 退化类型为下载时使用
	DownloadAppVersion []byte `protobuf:"bytes,7,opt,name=download_app_version,json=downloadAppVersion" json:"download_app_version,omitempty"`
	// 隐私协议
	PrivacyLink []byte `protobuf:"bytes,8,opt,name=privacy_link,json=privacyLink" json:"privacy_link,omitempty"`
	// 用户权限
	PermissionLink []byte `protobuf:"bytes,9,opt,name=permission_link,json=permissionLink" json:"permission_link,omitempty"`
	// universal link url, 用于ios调起, 优先级高于deeplink_url
	UlkUrl []byte `protobuf:"bytes,10,opt,name=ulk_url,json=ulkUrl" json:"ulk_url,omitempty"`
	// 用于ios嗅探是否安装某个app
	UlkScheme []byte `protobuf:"bytes,11,opt,name=ulk_scheme,json=ulkScheme" json:"ulk_scheme,omitempty"`
	// app name
	AppName []byte `protobuf:"bytes,12,opt,name=app_name,json=appName" json:"app_name,omitempty"`
}

func (x *BidResponse_Ad_DeeplinkInfo) Reset() {
	*x = BidResponse_Ad_DeeplinkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_DeeplinkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_DeeplinkInfo) ProtoMessage() {}

func (x *BidResponse_Ad_DeeplinkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_DeeplinkInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_DeeplinkInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_Ad_DeeplinkInfo) GetDeeplinkUrl() string {
	if x != nil && x.DeeplinkUrl != nil {
		return *x.DeeplinkUrl
	}
	return ""
}

func (x *BidResponse_Ad_DeeplinkInfo) GetAppVersion() uint32 {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return 0
}

func (x *BidResponse_Ad_DeeplinkInfo) GetFallbackUrl() string {
	if x != nil && x.FallbackUrl != nil {
		return *x.FallbackUrl
	}
	return ""
}

func (x *BidResponse_Ad_DeeplinkInfo) GetFallbackType() uint32 {
	if x != nil && x.FallbackType != nil {
		return *x.FallbackType
	}
	return 0
}

func (x *BidResponse_Ad_DeeplinkInfo) GetAppBundleId() []byte {
	if x != nil {
		return x.AppBundleId
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetPublisher() []byte {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetDownloadAppVersion() []byte {
	if x != nil {
		return x.DownloadAppVersion
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetPrivacyLink() []byte {
	if x != nil {
		return x.PrivacyLink
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetPermissionLink() []byte {
	if x != nil {
		return x.PermissionLink
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetUlkUrl() []byte {
	if x != nil {
		return x.UlkUrl
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetUlkScheme() []byte {
	if x != nil {
		return x.UlkScheme
	}
	return nil
}

func (x *BidResponse_Ad_DeeplinkInfo) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

type BidResponse_Ad_NativeAd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标题,编码UTF-8
	Title []byte `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// 描述,编码UTF-8
	Desc []byte `protobuf:"bytes,2,opt,name=desc" json:"desc,omitempty"`
	// image,支持多图片
	Image []*BidResponse_Ad_NativeAd_Image `protobuf:"bytes,3,rep,name=image" json:"image,omitempty"`
	// logo 或 icon
	LogoIcon *BidResponse_Ad_NativeAd_Image `protobuf:"bytes,4,opt,name=logo_icon,json=logoIcon" json:"logo_icon,omitempty"`
	// app 大小
	AppSize *int32 `protobuf:"varint,5,opt,name=app_size,json=appSize" json:"app_size,omitempty"`
	// 品牌名称,编码UTF-8
	BrandName []byte `protobuf:"bytes,6,opt,name=brand_name,json=brandName" json:"brand_name,omitempty"`
	// 创意关键字
	// 编码格式utf-8
	Keyword [][]byte `protobuf:"bytes,7,rep,name=keyword" json:"keyword,omitempty"`
	// 视频地址
	VideoUrl *string `protobuf:"bytes,8,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`
	// 视频宽高
	VideoWidth  *int32 `protobuf:"varint,9,opt,name=video_width,json=videoWidth" json:"video_width,omitempty"`
	VideoHeight *int32 `protobuf:"varint,10,opt,name=video_height,json=videoHeight" json:"video_height,omitempty"`
	// 视频时长(秒)
	VideoDuration *int32 `protobuf:"varint,11,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`
	// 视频大小（Byte）
	VideoSize *int32 `protobuf:"varint,12,opt,name=video_size,json=videoSize" json:"video_size,omitempty"`
}

func (x *BidResponse_Ad_NativeAd) Reset() {
	*x = BidResponse_Ad_NativeAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_NativeAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_NativeAd) ProtoMessage() {}

func (x *BidResponse_Ad_NativeAd) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_NativeAd.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_NativeAd) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *BidResponse_Ad_NativeAd) GetTitle() []byte {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetDesc() []byte {
	if x != nil {
		return x.Desc
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetImage() []*BidResponse_Ad_NativeAd_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetLogoIcon() *BidResponse_Ad_NativeAd_Image {
	if x != nil {
		return x.LogoIcon
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetAppSize() int32 {
	if x != nil && x.AppSize != nil {
		return *x.AppSize
	}
	return 0
}

func (x *BidResponse_Ad_NativeAd) GetBrandName() []byte {
	if x != nil {
		return x.BrandName
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetKeyword() [][]byte {
	if x != nil {
		return x.Keyword
	}
	return nil
}

func (x *BidResponse_Ad_NativeAd) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Ad_NativeAd) GetVideoWidth() int32 {
	if x != nil && x.VideoWidth != nil {
		return *x.VideoWidth
	}
	return 0
}

func (x *BidResponse_Ad_NativeAd) GetVideoHeight() int32 {
	if x != nil && x.VideoHeight != nil {
		return *x.VideoHeight
	}
	return 0
}

func (x *BidResponse_Ad_NativeAd) GetVideoDuration() int32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Ad_NativeAd) GetVideoSize() int32 {
	if x != nil && x.VideoSize != nil {
		return *x.VideoSize
	}
	return 0
}

// 非BES托管的普通广告创意物料信息，开屏等单创意返回
type BidResponse_Ad_MaterialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标题,编码UTF-8
	Title *string `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// 描述,编码UTF-8
	Desc *string `protobuf:"bytes,2,opt,name=desc" json:"desc,omitempty"`
	// 物料地址,编码UTF-8
	// 视频物料只支持MP4
	MaterialUrl *string `protobuf:"bytes,3,opt,name=material_url,json=materialUrl" json:"material_url,omitempty"`
	// 视频广告时长, 单位是秒
	VideoDuration *int32 `protobuf:"varint,4,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`
	// (仅用于序章dsp)
	MaterialPattern *BidResponse_Ad_MaterialInfo_MaterialPattern `protobuf:"varint,5,opt,name=material_pattern,json=materialPattern,enum=bes.BidResponse_Ad_MaterialInfo_MaterialPattern" json:"material_pattern,omitempty"`
	// (仅用于序章dsp)
	// 物料位置，在序章dsp投放开屏流量中，指的是按钮相对logo位置的值（%）
	MaterialPositionVertical *float32 `protobuf:"fixed32,6,opt,name=material_position_vertical,json=materialPositionVertical" json:"material_position_vertical,omitempty"`
	// (仅用于序章dsp)
	// 物料的md5值
	MaterialMd5 *string `protobuf:"bytes,7,opt,name=material_md5,json=materialMd5" json:"material_md5,omitempty"`
	// 创意关键字
	// 编码格式utf-8
	Keyword [][]byte `protobuf:"bytes,8,rep,name=keyword" json:"keyword,omitempty"`
	// 视频广告大小，单位Byte
	VideoSize *int32 `protobuf:"varint,9,opt,name=video_size,json=videoSize" json:"video_size,omitempty"`
}

func (x *BidResponse_Ad_MaterialInfo) Reset() {
	*x = BidResponse_Ad_MaterialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialInfo) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 2}
}

func (x *BidResponse_Ad_MaterialInfo) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Ad_MaterialInfo) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *BidResponse_Ad_MaterialInfo) GetMaterialUrl() string {
	if x != nil && x.MaterialUrl != nil {
		return *x.MaterialUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialInfo) GetVideoDuration() int32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Ad_MaterialInfo) GetMaterialPattern() BidResponse_Ad_MaterialInfo_MaterialPattern {
	if x != nil && x.MaterialPattern != nil {
		return *x.MaterialPattern
	}
	return BidResponse_Ad_MaterialInfo_MATERIAL_PATTERN_FULL_SCREEN
}

func (x *BidResponse_Ad_MaterialInfo) GetMaterialPositionVertical() float32 {
	if x != nil && x.MaterialPositionVertical != nil {
		return *x.MaterialPositionVertical
	}
	return 0
}

func (x *BidResponse_Ad_MaterialInfo) GetMaterialMd5() string {
	if x != nil && x.MaterialMd5 != nil {
		return *x.MaterialMd5
	}
	return ""
}

func (x *BidResponse_Ad_MaterialInfo) GetKeyword() [][]byte {
	if x != nil {
		return x.Keyword
	}
	return nil
}

func (x *BidResponse_Ad_MaterialInfo) GetVideoSize() int32 {
	if x != nil && x.VideoSize != nil {
		return *x.VideoSize
	}
	return 0
}

// 最小交互区域的返回元素描述
// 与请求中的MetaStyle相对应
type BidResponse_Ad_MetaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 交互信息
	ActionInfo *BidResponse_Ad_MetaInfo_ActionInfo `protobuf:"bytes,1,opt,name=action_info,json=actionInfo" json:"action_info,omitempty"`
	// 标题
	Title []*BidResponse_Ad_MetaInfo_TxtMaterial `protobuf:"bytes,2,rep,name=title" json:"title,omitempty"`
	// 文字描述
	Desc []*BidResponse_Ad_MetaInfo_TxtMaterial `protobuf:"bytes,3,rep,name=desc" json:"desc,omitempty"`
	// 图标
	Icon []*BidResponse_Ad_MetaInfo_SrcMaterial `protobuf:"bytes,4,rep,name=icon" json:"icon,omitempty"`
	// 图片
	Image []*BidResponse_Ad_MetaInfo_SrcMaterial `protobuf:"bytes,5,rep,name=image" json:"image,omitempty"`
	// 品牌信息
	BrandInfo *BidResponse_Ad_MetaInfo_BrandInfo `protobuf:"bytes,6,opt,name=brand_info,json=brandInfo" json:"brand_info,omitempty"`
	// 行业
	Category []int32 `protobuf:"varint,7,rep,name=category" json:"category,omitempty"`
	// 视频
	Video []*BidResponse_Ad_MetaInfo_SrcMaterial `protobuf:"bytes,9,rep,name=video" json:"video,omitempty"`
	// 广告主推广的app相关信息，下载类广告使用
	AppInfo *BidResponse_Ad_MetaInfo_AppInfo `protobuf:"bytes,10,opt,name=app_info,json=appInfo" json:"app_info,omitempty"`
	// 应用唤醒信息, 交互类型为ATTACH_APP_OPEN时需设置
	DeeplinkInfo *BidResponse_Ad_DeeplinkInfo `protobuf:"bytes,11,opt,name=deeplink_info,json=deeplinkInfo" json:"deeplink_info,omitempty"`
	// 按钮
	Button []*BidResponse_Ad_MetaInfo_SrcMaterial `protobuf:"bytes,13,rep,name=button" json:"button,omitempty"`
	// 物料类型
	// 0 文本
	// 1 图片
	// 4 图文
	// 7 视频
	// 9 html
	CreativeType *int32 `protobuf:"varint,15,opt,name=creative_type,json=creativeType" json:"creative_type,omitempty"`
	// 创意关键字
	// 编码格式utf-8
	Keyword [][]byte `protobuf:"bytes,16,rep,name=keyword" json:"keyword,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo) Reset() {
	*x = BidResponse_Ad_MetaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3}
}

func (x *BidResponse_Ad_MetaInfo) GetActionInfo() *BidResponse_Ad_MetaInfo_ActionInfo {
	if x != nil {
		return x.ActionInfo
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetTitle() []*BidResponse_Ad_MetaInfo_TxtMaterial {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetDesc() []*BidResponse_Ad_MetaInfo_TxtMaterial {
	if x != nil {
		return x.Desc
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetIcon() []*BidResponse_Ad_MetaInfo_SrcMaterial {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetImage() []*BidResponse_Ad_MetaInfo_SrcMaterial {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetBrandInfo() *BidResponse_Ad_MetaInfo_BrandInfo {
	if x != nil {
		return x.BrandInfo
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetCategory() []int32 {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetVideo() []*BidResponse_Ad_MetaInfo_SrcMaterial {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetAppInfo() *BidResponse_Ad_MetaInfo_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetDeeplinkInfo() *BidResponse_Ad_DeeplinkInfo {
	if x != nil {
		return x.DeeplinkInfo
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetButton() []*BidResponse_Ad_MetaInfo_SrcMaterial {
	if x != nil {
		return x.Button
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo) GetCreativeType() int32 {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo) GetKeyword() [][]byte {
	if x != nil {
		return x.Keyword
	}
	return nil
}

// 将组成元素相同的MetaInfo按照MetaInfoGroup进行分类
// 与请求的MetaStyleGroup对应
type BidResponse_Ad_MetaInfoGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回的最小交互区域的元素集合
	MetaInfo []*BidResponse_Ad_MetaInfo `protobuf:"bytes,1,rep,name=meta_info,json=metaInfo" json:"meta_info,omitempty"`
}

func (x *BidResponse_Ad_MetaInfoGroup) Reset() {
	*x = BidResponse_Ad_MetaInfoGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfoGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfoGroup) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfoGroup) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfoGroup.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfoGroup) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 4}
}

func (x *BidResponse_Ad_MetaInfoGroup) GetMetaInfo() []*BidResponse_Ad_MetaInfo {
	if x != nil {
		return x.MetaInfo
	}
	return nil
}

// 应用下载信息
// 非BES托管的静态创意，在交互类型为DOWNLOAD时，需要填充
// 下载应用包地址填写在target_url中
type BidResponse_Ad_DownloadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 下载包大小（单位为byte）
	AppSize *int32 `protobuf:"varint,1,opt,name=app_size,json=appSize" json:"app_size,omitempty"`
	// 包名
	AppPackageName *string `protobuf:"bytes,2,opt,name=app_package_name,json=appPackageName" json:"app_package_name,omitempty"`
	// ios_id Appstoreid
	IosId *string `protobuf:"bytes,7,opt,name=ios_id,json=iosId" json:"ios_id,omitempty"`
	// doc_id 百度开放平台应用id
	DocId *string `protobuf:"bytes,8,opt,name=doc_id,json=docId" json:"doc_id,omitempty"`
	// 下载描述
	DownloadDesc *string `protobuf:"bytes,9,opt,name=download_desc,json=downloadDesc" json:"download_desc,omitempty"`
	// 开发者名称
	Publisher []byte `protobuf:"bytes,3,opt,name=publisher" json:"publisher,omitempty"`
	// 版本号
	AppVersion []byte `protobuf:"bytes,4,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	// 隐私协议
	PrivacyLink []byte `protobuf:"bytes,5,opt,name=privacy_link,json=privacyLink" json:"privacy_link,omitempty"`
	// 用户权限
	PermissionLink []byte `protobuf:"bytes,6,opt,name=permission_link,json=permissionLink" json:"permission_link,omitempty"`
	// app name
	AppName []byte `protobuf:"bytes,10,opt,name=app_name,json=appName" json:"app_name,omitempty"`
}

func (x *BidResponse_Ad_DownloadInfo) Reset() {
	*x = BidResponse_Ad_DownloadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_DownloadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_DownloadInfo) ProtoMessage() {}

func (x *BidResponse_Ad_DownloadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_DownloadInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_DownloadInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 5}
}

func (x *BidResponse_Ad_DownloadInfo) GetAppSize() int32 {
	if x != nil && x.AppSize != nil {
		return *x.AppSize
	}
	return 0
}

func (x *BidResponse_Ad_DownloadInfo) GetAppPackageName() string {
	if x != nil && x.AppPackageName != nil {
		return *x.AppPackageName
	}
	return ""
}

func (x *BidResponse_Ad_DownloadInfo) GetIosId() string {
	if x != nil && x.IosId != nil {
		return *x.IosId
	}
	return ""
}

func (x *BidResponse_Ad_DownloadInfo) GetDocId() string {
	if x != nil && x.DocId != nil {
		return *x.DocId
	}
	return ""
}

func (x *BidResponse_Ad_DownloadInfo) GetDownloadDesc() string {
	if x != nil && x.DownloadDesc != nil {
		return *x.DownloadDesc
	}
	return ""
}

func (x *BidResponse_Ad_DownloadInfo) GetPublisher() []byte {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidResponse_Ad_DownloadInfo) GetAppVersion() []byte {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *BidResponse_Ad_DownloadInfo) GetPrivacyLink() []byte {
	if x != nil {
		return x.PrivacyLink
	}
	return nil
}

func (x *BidResponse_Ad_DownloadInfo) GetPermissionLink() []byte {
	if x != nil {
		return x.PermissionLink
	}
	return nil
}

func (x *BidResponse_Ad_DownloadInfo) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

type BidResponse_Ad_NativeAd_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	Width  *int32  `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`
	Height *int32  `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`
}

func (x *BidResponse_Ad_NativeAd_Image) Reset() {
	*x = BidResponse_Ad_NativeAd_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_NativeAd_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_NativeAd_Image) ProtoMessage() {}

func (x *BidResponse_Ad_NativeAd_Image) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_NativeAd_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_NativeAd_Image) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 1, 0}
}

func (x *BidResponse_Ad_NativeAd_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Ad_NativeAd_Image) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Ad_NativeAd_Image) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

// 交互信息
type BidResponse_Ad_MetaInfo_ActionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 交互动作链接地址，如果交互触发类型为点击，则此链接为点击链接
	ActionUrl []byte `protobuf:"bytes,1,opt,name=action_url,json=actionUrl" json:"action_url,omitempty"`
	// 最终跳转到的链接地址
	LandingPage []byte `protobuf:"bytes,2,opt,name=landing_page,json=landingPage" json:"landing_page,omitempty"`
	// 交互动作类型
	// 0 落地页
	// 16 下载
	// 1024 应用唤醒
	ActionType *int32 `protobuf:"varint,4,opt,name=action_type,json=actionType" json:"action_type,omitempty"`
	// 点击监控串(非302跳转的点击监控)
	ClickMonitorUrl [][]byte `protobuf:"bytes,6,rep,name=click_monitor_url,json=clickMonitorUrl" json:"click_monitor_url,omitempty"`
	// app下载地址
	AppDownloadUrl []byte `protobuf:"bytes,7,opt,name=app_download_url,json=appDownloadUrl" json:"app_download_url,omitempty"`
	// 跳转厂商应用商店的下载链接
	AppStoreLink []byte `protobuf:"bytes,8,opt,name=app_store_link,json=appStoreLink" json:"app_store_link,omitempty"`
	// 并行计费串
	ChargeLink []byte `protobuf:"bytes,9,opt,name=charge_link,json=chargeLink" json:"charge_link,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) Reset() {
	*x = BidResponse_Ad_MetaInfo_ActionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo_ActionInfo) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo_ActionInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo_ActionInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3, 0}
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetActionUrl() []byte {
	if x != nil {
		return x.ActionUrl
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetLandingPage() []byte {
	if x != nil {
		return x.LandingPage
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetActionType() int32 {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetClickMonitorUrl() [][]byte {
	if x != nil {
		return x.ClickMonitorUrl
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetAppDownloadUrl() []byte {
	if x != nil {
		return x.AppDownloadUrl
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetAppStoreLink() []byte {
	if x != nil {
		return x.AppStoreLink
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_ActionInfo) GetChargeLink() []byte {
	if x != nil {
		return x.ChargeLink
	}
	return nil
}

// 文本类物料，utf-8编码
type BidResponse_Ad_MetaInfo_TxtMaterial struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文本的描述,utf-8编码
	Txt []byte `protobuf:"bytes,1,opt,name=txt" json:"txt,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo_TxtMaterial) Reset() {
	*x = BidResponse_Ad_MetaInfo_TxtMaterial{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo_TxtMaterial) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo_TxtMaterial) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo_TxtMaterial) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo_TxtMaterial.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo_TxtMaterial) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3, 1}
}

func (x *BidResponse_Ad_MetaInfo_TxtMaterial) GetTxt() []byte {
	if x != nil {
		return x.Txt
	}
	return nil
}

// 资源类物料
type BidResponse_Ad_MetaInfo_SrcMaterial struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主要是指JPG、GIF、SWF、MP4、AVI、MPEG、MOV、FLV等文件
	// 1: mp4, 2: 3gp, 3: flv, 4: avi, 5: mov, 6: mpg
	// 7: mpeg, 8: wmv, 9: rmvb, 10: mkv, 13: ts
	// 100: swf, 101: jpg, 102: gif, 103: png, 201: vr
	// 202: 3d
	Type *int32 `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	// 资源物料url地址
	Url []byte `protobuf:"bytes,2,opt,name=url" json:"url,omitempty"`
	// 资源物料真实尺寸-宽
	Width *int32 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	// 资源物料真实尺寸-高
	Height *int32 `protobuf:"varint,4,opt,name=height" json:"height,omitempty"`
	// 视频物料的播放时长
	VideoDuration *int32 `protobuf:"varint,5,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`
	// 资源物料的大小，单位 Byte
	Size *int32 `protobuf:"varint,6,opt,name=size" json:"size,omitempty"`
	// 视频物料的比特率，单位 kbps
	Bitrate *int32 `protobuf:"varint,7,opt,name=bitrate" json:"bitrate,omitempty"`
	// 视频物料的编码，比如 h.262
	Encoding *string `protobuf:"bytes,8,opt,name=encoding" json:"encoding,omitempty"`
	// 物料的md5
	Md5 *string `protobuf:"bytes,9,opt,name=md5" json:"md5,omitempty"`
	// 物料文本，utf-8编码，比如按钮上的文字 查看、下载等
	Text []byte `protobuf:"bytes,10,opt,name=text" json:"text,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) Reset() {
	*x = BidResponse_Ad_MetaInfo_SrcMaterial{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo_SrcMaterial) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo_SrcMaterial.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo_SrcMaterial) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3, 2}
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetUrl() []byte {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetVideoDuration() int32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetBitrate() int32 {
	if x != nil && x.Bitrate != nil {
		return *x.Bitrate
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetEncoding() string {
	if x != nil && x.Encoding != nil {
		return *x.Encoding
	}
	return ""
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

func (x *BidResponse_Ad_MetaInfo_SrcMaterial) GetText() []byte {
	if x != nil {
		return x.Text
	}
	return nil
}

// 品牌信息
type BidResponse_Ad_MetaInfo_BrandInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 品牌名称
	BrandName []byte `protobuf:"bytes,1,opt,name=brand_name,json=brandName" json:"brand_name,omitempty"`
	// 品牌vip等级，从1开始，0代码无效
	BrandLevel *int32 `protobuf:"varint,2,opt,name=brand_level,json=brandLevel" json:"brand_level,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo_BrandInfo) Reset() {
	*x = BidResponse_Ad_MetaInfo_BrandInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo_BrandInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo_BrandInfo) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo_BrandInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo_BrandInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo_BrandInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3, 3}
}

func (x *BidResponse_Ad_MetaInfo_BrandInfo) GetBrandName() []byte {
	if x != nil {
		return x.BrandName
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_BrandInfo) GetBrandLevel() int32 {
	if x != nil && x.BrandLevel != nil {
		return *x.BrandLevel
	}
	return 0
}

// 广告主推广的app相关信息
type BidResponse_Ad_MetaInfo_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用名称
	AppName []byte `protobuf:"bytes,1,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	// 应用包名
	PackageName []byte `protobuf:"bytes,2,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// 应用安装包的名字，如xxx.apk
	ApkName []byte `protobuf:"bytes,3,opt,name=apk_name,json=apkName" json:"apk_name,omitempty"`
	// 应用大小，单位Byte
	Size *int32 `protobuf:"varint,4,opt,name=size" json:"size,omitempty"`
	// 应用下载数
	Downloads *int32 `protobuf:"varint,5,opt,name=downloads" json:"downloads,omitempty"`
	// 应用评分
	Rating *float32 `protobuf:"fixed32,6,opt,name=rating" json:"rating,omitempty"`
	// 应用评论数
	Comments *int32 `protobuf:"varint,7,opt,name=comments" json:"comments,omitempty"`
	// appstore的应用id，仅ios应用需要传递
	AppId []byte `protobuf:"bytes,8,opt,name=app_id,json=appId" json:"app_id,omitempty"`
	// 开发者
	Publisher []byte `protobuf:"bytes,9,opt,name=publisher" json:"publisher,omitempty"`
	// 版本号
	AppVersion []byte `protobuf:"bytes,10,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	// 隐私协议
	PrivacyLink []byte `protobuf:"bytes,11,opt,name=privacy_link,json=privacyLink" json:"privacy_link,omitempty"`
	// 用户权限
	PermissionLink []byte `protobuf:"bytes,12,opt,name=permission_link,json=permissionLink" json:"permission_link,omitempty"`
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) Reset() {
	*x = BidResponse_Ad_MetaInfo_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bes_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MetaInfo_AppInfo) ProtoMessage() {}

func (x *BidResponse_Ad_MetaInfo_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bes_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MetaInfo_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MetaInfo_AppInfo) Descriptor() ([]byte, []int) {
	return file_bes_proto_rawDescGZIP(), []int{1, 0, 3, 4}
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetPackageName() []byte {
	if x != nil {
		return x.PackageName
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetApkName() []byte {
	if x != nil {
		return x.ApkName
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetSize() int32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetDownloads() int32 {
	if x != nil && x.Downloads != nil {
		return *x.Downloads
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetRating() float32 {
	if x != nil && x.Rating != nil {
		return *x.Rating
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetComments() int32 {
	if x != nil && x.Comments != nil {
		return *x.Comments
	}
	return 0
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetAppId() []byte {
	if x != nil {
		return x.AppId
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetPublisher() []byte {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetAppVersion() []byte {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetPrivacyLink() []byte {
	if x != nil {
		return x.PrivacyLink
	}
	return nil
}

func (x *BidResponse_Ad_MetaInfo_AppInfo) GetPermissionLink() []byte {
	if x != nil {
		return x.PermissionLink
	}
	return nil
}

var file_bes_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*BidResponse_Ad)(nil),
		ExtensionType: (*int32)(nil),
		Field:         101,
		Name:          "bes.ad_status",
		Tag:           "varint,101,opt,name=ad_status",
		Filename:      "bes.proto",
	},
}

// Extension fields to BidResponse_Ad.
var (
	// optional int32 ad_status = 101;
	E_AdStatus = &file_bes_proto_extTypes[0]
)

var File_bes_proto protoreflect.FileDescriptor

var file_bes_proto_rawDesc = []byte{
	0x0a, 0x09, 0x62, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x62, 0x65, 0x73,
	0x22, 0xb4, 0x32, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x70, 0x36, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70,
	0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x65,
	0x6f, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62,
	0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65,
	0x6f, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x47, 0x65, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e,
	0x0a, 0x19, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x13, 0x20, 0x03, 0x28,
	0x05, 0x42, 0x02, 0x10, 0x01, 0x52, 0x17, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2e,
	0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x3e,
	0x0a, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e,
	0x0a, 0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x06, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0c,
	0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x73, 0x12, 0x28, 0x0a, 0x0e, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6e,
	0x65, 0x77, 0x5f, 0x74, 0x74, 0x70, 0x18, 0x28, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x65,
	0x77, 0x54, 0x74, 0x70, 0x12, 0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73,
	0x54, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73,
	0x50, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x64,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x3a, 0x08, 0x4e, 0x4f, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x52,
	0x09, 0x68, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x6c, 0x6f,
	0x6f, 0x6b, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x29, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c,
	0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x6b,
	0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x65, 0x78, 0x5f, 0x65, 0x78, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x2b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x65, 0x78, 0x45, 0x78, 0x70, 0x49, 0x64, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x42, 0x69,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xd4, 0x03, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x47, 0x0a,
	0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x2e, 0x43, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47,
	0x65, 0x6f, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xc8, 0x01,
	0x0a, 0x0a, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x47, 0x65, 0x6f, 0x2e, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x52, 0x08, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x22, 0x3b, 0x0a, 0x08, 0x53,
	0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x44, 0x5f, 0x30, 0x39,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x43, 0x4a, 0x5f, 0x30, 0x32, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x57, 0x47, 0x53, 0x5f, 0x38, 0x34, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x44,
	0x5f, 0x30, 0x39, 0x5f, 0x4c, 0x4c, 0x10, 0x03, 0x1a, 0x72, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x1a, 0xe6, 0x10, 0x0a,
	0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x4f, 0x53, 0x3a,
	0x0a, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x53, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x45, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x62, 0x65, 0x73, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x64, 0x6b, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x44, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x15, 0x77, 0x69, 0x72, 0x65, 0x6c,
	0x65, 0x73, 0x73, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x57,
	0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x13, 0x77, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74,
	0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6c, 0x69, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d,
	0x61, 0x6c, 0x69, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x69, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x55, 0x0a, 0x12, 0x66, 0x6f,
	0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x46,
	0x6f, 0x72, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x44, 0x52,
	0x10, 0x66, 0x6f, 0x72, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x49,
	0x64, 0x12, 0x3f, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x52, 0x09, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41,
	0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x6f, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x69, 0x64, 0x4e, 0x65, 0x77, 0x1a, 0xff, 0x01,
	0x0a, 0x08, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x44, 0x2e, 0x49, 0x44, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x11, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x22, 0x46, 0x0a, 0x06, 0x49, 0x44, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x49, 0x4d, 0x45, 0x49, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x41, 0x43, 0x10,
	0x02, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x55, 0x49, 0x44, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x4f,
	0x41, 0x49, 0x44, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x49, 0x44, 0x10, 0x05, 0x1a,
	0x8f, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x28, 0x0a,
	0x10, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x73, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x63, 0x72,
	0x6f, 0x1a, 0x97, 0x01, 0x0a, 0x10, 0x46, 0x6f, 0x72, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x49, 0x44, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x6f, 0x72,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x44, 0x2e, 0x49, 0x44,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x06, 0x49, 0x44,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x5f, 0x49, 0x44, 0x10,
	0x04, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x46, 0x41, 0x10, 0x05, 0x1a, 0xdc, 0x02, 0x0a, 0x09,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x65, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41, 0x70,
	0x70, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3f, 0x0a, 0x12, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0d, 0x0a, 0x09, 0x54, 0x45, 0x4c, 0x45, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x02, 0x22, 0x67, 0x0a, 0x10, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x0e, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x49, 0x47, 0x48, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x54, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x56, 0x10, 0x04, 0x12,
	0x12, 0x0a, 0x0e, 0x4f, 0x55, 0x54, 0x44, 0x4f, 0x4f, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x05, 0x22, 0x3d, 0x0a, 0x02, 0x4f, 0x53, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x53, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x10, 0x03, 0x22, 0x61, 0x0a, 0x13, 0x57, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x5f, 0x32, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x33, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45,
	0x5f, 0x34, 0x47, 0x10, 0x04, 0x1a, 0x82, 0x01, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x1a, 0x90, 0x15, 0x0a, 0x06, 0x41,
	0x64, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x64, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x61, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x0f, 0x0a, 0x02, 0x69, 0x64, 0x18, 0xe9,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x64, 0x73,
	0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x6c, 0x6f, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0d, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05,
	0x42, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x6c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x4c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x3b, 0x0a,
	0x1a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x06, 0x52, 0x17, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69,
	0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x70, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x43, 0x70, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x70, 0x6d, 0x18, 0x2a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x4d, 0x69, 0x6e,
	0x43, 0x70, 0x6d, 0x12, 0x36, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6e,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x61, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x4e, 0x6f, 0x6e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x61, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x61, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0d, 0x6e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x61, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65,
	0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0d, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x41, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x1a, 0xec, 0x03, 0x0a, 0x0d, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x28, 0x0a, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x4d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x73,
	0x63, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x64, 0x65, 0x73, 0x63, 0x4d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x12, 0x4a, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x41, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x45, 0x6c, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x43, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x62,
	0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64,
	0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x1a,
	0x51, 0x0a, 0x08, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x17, 0x0a, 0x05, 0x73, 0x68, 0x61,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x05, 0x73, 0x68, 0x61,
	0x70, 0x65, 0x22, 0x5f, 0x0a, 0x06, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x09, 0x0a, 0x05,
	0x54, 0x49, 0x54, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x45, 0x53, 0x43, 0x10,
	0x02, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x4c, 0x4f, 0x47, 0x4f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50,
	0x50, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x10, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f,
	0x10, 0x40, 0x12, 0x0f, 0x0a, 0x0a, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x10, 0x80, 0x01, 0x1a, 0xa7, 0x0a, 0x0a, 0x09, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x43, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x07, 0x61, 0x64,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x1a, 0xb5, 0x09, 0x0a, 0x07, 0x41, 0x64, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x61, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x62, 0x65,
	0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x1a, 0x3e, 0x0a, 0x0a, 0x54, 0x78, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6d,
	0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x61,
	0x78, 0x4c, 0x65, 0x6e, 0x1a, 0x3c, 0x0a, 0x0c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x1a, 0xf4, 0x01, 0x0a, 0x0c, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x42, 0x69, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x42, 0x69, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69,
	0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xd4, 0x03, 0x0a, 0x09, 0x4d, 0x65,
	0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x50, 0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x65, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x2e, 0x54, 0x78, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74,
	0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x2e, 0x54, 0x78, 0x74, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x64,
	0x65, 0x73, 0x63, 0x45, 0x6c, 0x65, 0x12, 0x50, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x65,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74,
	0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x69, 0x63, 0x6f, 0x6e, 0x45, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x65, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x62, 0x65,
	0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53,
	0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x09,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x65, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6c, 0x65,
	0x1a, 0x75, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x12, 0x51, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74,
	0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x64, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x09, 0x6d, 0x65,
	0x74, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x4d, 0x65, 0x74, 0x61,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x45, 0x54, 0x41, 0x5f,
	0x45, 0x4c, 0x45, 0x5f, 0x54, 0x49, 0x54, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d,
	0x45, 0x54, 0x41, 0x5f, 0x45, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x10, 0x02, 0x12, 0x11,
	0x0a, 0x0d, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x45, 0x4c, 0x45, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10,
	0x04, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x45, 0x4c, 0x45, 0x5f, 0x49, 0x4d,
	0x41, 0x47, 0x45, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x45, 0x4c,
	0x45, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x10, 0x10, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x45, 0x54,
	0x41, 0x5f, 0x45, 0x4c, 0x45, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x20, 0x1a, 0x30, 0x0a,
	0x08, 0x4c, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x6b, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x4c, 0x0a, 0x06, 0x4e, 0x65, 0x77, 0x54, 0x54, 0x50, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x54, 0x50,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x54, 0x50,
	0x5f, 0x50, 0x43, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x54, 0x50, 0x5f, 0x57, 0x41, 0x50,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x54, 0x50, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x07, 0x54, 0x54, 0x50, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x8f, 0x4e, 0x22, 0x38, 0x0a,
	0x0b, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0c, 0x0a, 0x08,
	0x4e, 0x4f, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x4e,
	0x4c, 0x59, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x4f, 0x54,
	0x5f, 0x48, 0x4f, 0x53, 0x54, 0x10, 0x03, 0x22, 0x90, 0x28, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x62, 0x75, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x22, 0x0a,
	0x0d, 0x61, 0x64, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x64, 0x73, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x8d, 0x26, 0x0a, 0x02, 0x41,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0xea, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x25, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x53,
	0x74, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x74, 0x6d, 0x6c, 0x5f, 0x73, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x74, 0x6d, 0x6c, 0x53, 0x6e,
	0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x24, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6d,
	0x61, 0x78, 0x5f, 0x63, 0x70, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x61,
	0x78, 0x43, 0x70, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x65, 0x78, 0x74, 0x31, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x78,
	0x74, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x74, 0x32, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x65, 0x78, 0x74, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x74, 0x33, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x78, 0x74, 0x33, 0x12, 0x45, 0x0a, 0x0d, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x39, 0x0a, 0x09, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x64, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x41, 0x64, 0x52, 0x08, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x12, 0x45, 0x0a, 0x0d,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x62,
	0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x0d, 0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x45,
	0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0b, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x62,
	0x75, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x03, 0x62, 0x69, 0x64,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x15, 0x0a, 0x04, 0x6f, 0x62, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x3a,
	0x01, 0x30, 0x52, 0x04, 0x6f, 0x62, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x62,
	0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x64, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x1a,
	0xad, 0x03, 0x0a, 0x0c, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d,
	0x61, 0x70, 0x70, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x30,
	0x0a, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x12, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x6c, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x75,
	0x6c, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6c, 0x6b, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x75, 0x6c, 0x6b, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x1a,
	0xf3, 0x03, 0x0a, 0x08, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x38, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x41, 0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x3f, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x57, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x47, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xd0, 0x03, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5b, 0x0a, 0x10, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x50,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x3c, 0x0a, 0x1a, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x72,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x64, 0x35, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x55, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74,
	0x74, 0x65, 0x72, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c,
	0x5f, 0x50, 0x41, 0x54, 0x54, 0x45, 0x52, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x4c, 0x5f, 0x50, 0x41, 0x54, 0x54, 0x45, 0x52, 0x4e, 0x5f, 0x48, 0x41, 0x4c, 0x46, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x02, 0x1a, 0xe1, 0x0d, 0x0a, 0x08, 0x4d, 0x65, 0x74,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x62, 0x65, 0x73,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x78, 0x74,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x3c, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x78, 0x74, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3c, 0x0a,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x65,
	0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x72, 0x63, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x65, 0x73,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x72, 0x63, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3e,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x72, 0x63, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x3f,
	0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41,
	0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x45, 0x0a, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x06, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x72, 0x63, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x52, 0x06, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x8c, 0x02, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0c, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12,
	0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x1a, 0x1f, 0x0a, 0x0b, 0x54, 0x78, 0x74, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x03, 0x74, 0x78, 0x74, 0x1a, 0xf8, 0x01, 0x0a, 0x0b, 0x53, 0x72, 0x63, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x1a, 0x4b, 0x0a, 0x09, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x1a,
	0xea, 0x02, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61, 0x70, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x1a, 0x4a, 0x0a, 0x0d,
	0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x39, 0x0a,
	0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xcc, 0x02, 0x0a, 0x0c, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x70, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x6f, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6f, 0x73, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x09, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x53,
	0x55, 0x42, 0x4d, 0x49, 0x54, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x56, 0x41, 0x49, 0x4c, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x0d, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x5f,
	0x43, 0x4c, 0x49, 0x43, 0x4b, 0x10, 0x10, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x50, 0x50, 0x5f, 0x55,
	0x50, 0x10, 0x47, 0x2a, 0x05, 0x08, 0x64, 0x10, 0xc8, 0x01, 0x3a, 0x30, 0x0a, 0x09, 0x61, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x2e, 0x62, 0x65, 0x73, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x18, 0x65, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0x5a, 0x06,
	0x2e, 0x2e, 0x2f, 0x62, 0x65, 0x73,
}

var (
	file_bes_proto_rawDescOnce sync.Once
	file_bes_proto_rawDescData = file_bes_proto_rawDesc
)

func file_bes_proto_rawDescGZIP() []byte {
	file_bes_proto_rawDescOnce.Do(func() {
		file_bes_proto_rawDescData = protoimpl.X.CompressGZIP(file_bes_proto_rawDescData)
	})
	return file_bes_proto_rawDescData
}

var file_bes_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_bes_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_bes_proto_goTypes = []interface{}{
	(BidRequest_NewTTP)(0),                                     // 0: bes.BidRequest.NewTTP
	(BidRequest_HostAdLimit)(0),                                // 1: bes.BidRequest.HostAdLimit
	(BidRequest_Geo_Coordinate_Standard)(0),                    // 2: bes.BidRequest.Geo.Coordinate.Standard
	(BidRequest_Mobile_MobileDeviceType)(0),                    // 3: bes.BidRequest.Mobile.MobileDeviceType
	(BidRequest_Mobile_OS)(0),                                  // 4: bes.BidRequest.Mobile.OS
	(BidRequest_Mobile_WirelessNetworkType)(0),                 // 5: bes.BidRequest.Mobile.WirelessNetworkType
	(BidRequest_Mobile_MobileID_IDType)(0),                     // 6: bes.BidRequest.Mobile.MobileID.IDType
	(BidRequest_Mobile_ForAdvertisingID_IDType)(0),             // 7: bes.BidRequest.Mobile.ForAdvertisingID.IDType
	(BidRequest_Mobile_MobileApp_AppInteractionType)(0),        // 8: bes.BidRequest.Mobile.MobileApp.AppInteractionType
	(BidRequest_AdSlot_NativeAdParam_Fields)(0),                // 9: bes.BidRequest.AdSlot.NativeAdParam.Fields
	(BidRequest_AdSlot_StyleInfo_AdStyle_MetaElement)(0),       // 10: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaElement
	(BidResponse_Ad_TransType)(0),                              // 11: bes.BidResponse.Ad.TransType
	(BidResponse_Ad_MaterialInfo_MaterialPattern)(0),           // 12: bes.BidResponse.Ad.MaterialInfo.MaterialPattern
	(*BidRequest)(nil),                                         // 13: bes.BidRequest
	(*BidResponse)(nil),                                        // 14: bes.BidResponse
	(*BidRequest_Geo)(nil),                                     // 15: bes.BidRequest.Geo
	(*BidRequest_Mobile)(nil),                                  // 16: bes.BidRequest.Mobile
	(*BidRequest_ArticleInfo)(nil),                             // 17: bes.BidRequest.ArticleInfo
	(*BidRequest_AdSlot)(nil),                                  // 18: bes.BidRequest.AdSlot
	(*BidRequest_LookLike)(nil),                                // 19: bes.BidRequest.LookLike
	(*BidRequest_Geo_Coordinate)(nil),                          // 20: bes.BidRequest.Geo.Coordinate
	(*BidRequest_Geo_UserLocation)(nil),                        // 21: bes.BidRequest.Geo.UserLocation
	(*BidRequest_Mobile_MobileID)(nil),                         // 22: bes.BidRequest.Mobile.MobileID
	(*BidRequest_Mobile_DeviceOsVersion)(nil),                  // 23: bes.BidRequest.Mobile.DeviceOsVersion
	(*BidRequest_Mobile_ForAdvertisingID)(nil),                 // 24: bes.BidRequest.Mobile.ForAdvertisingID
	(*BidRequest_Mobile_MobileApp)(nil),                        // 25: bes.BidRequest.Mobile.MobileApp
	(*BidRequest_AdSlot_NativeAdParam)(nil),                    // 26: bes.BidRequest.AdSlot.NativeAdParam
	(*BidRequest_AdSlot_StyleInfo)(nil),                        // 27: bes.BidRequest.AdSlot.StyleInfo
	(*BidRequest_AdSlot_NativeAdParam_ImageEle)(nil),           // 28: bes.BidRequest.AdSlot.NativeAdParam.ImageEle
	(*BidRequest_AdSlot_StyleInfo_AdStyle)(nil),                // 29: bes.BidRequest.AdSlot.StyleInfo.AdStyle
	(*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement)(nil),     // 30: bes.BidRequest.AdSlot.StyleInfo.AdStyle.TxtElement
	(*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement)(nil),   // 31: bes.BidRequest.AdSlot.StyleInfo.AdStyle.ImageElement
	(*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement)(nil),   // 32: bes.BidRequest.AdSlot.StyleInfo.AdStyle.VideoElement
	(*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle)(nil),      // 33: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle
	(*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup)(nil), // 34: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyleGroup
	(*BidResponse_Ad)(nil),                                     // 35: bes.BidResponse.Ad
	(*BidResponse_Ad_DeeplinkInfo)(nil),                        // 36: bes.BidResponse.Ad.DeeplinkInfo
	(*BidResponse_Ad_NativeAd)(nil),                            // 37: bes.BidResponse.Ad.NativeAd
	(*BidResponse_Ad_MaterialInfo)(nil),                        // 38: bes.BidResponse.Ad.MaterialInfo
	(*BidResponse_Ad_MetaInfo)(nil),                            // 39: bes.BidResponse.Ad.MetaInfo
	(*BidResponse_Ad_MetaInfoGroup)(nil),                       // 40: bes.BidResponse.Ad.MetaInfoGroup
	(*BidResponse_Ad_DownloadInfo)(nil),                        // 41: bes.BidResponse.Ad.DownloadInfo
	(*BidResponse_Ad_NativeAd_Image)(nil),                      // 42: bes.BidResponse.Ad.NativeAd.Image
	(*BidResponse_Ad_MetaInfo_ActionInfo)(nil),                 // 43: bes.BidResponse.Ad.MetaInfo.ActionInfo
	(*BidResponse_Ad_MetaInfo_TxtMaterial)(nil),                // 44: bes.BidResponse.Ad.MetaInfo.TxtMaterial
	(*BidResponse_Ad_MetaInfo_SrcMaterial)(nil),                // 45: bes.BidResponse.Ad.MetaInfo.SrcMaterial
	(*BidResponse_Ad_MetaInfo_BrandInfo)(nil),                  // 46: bes.BidResponse.Ad.MetaInfo.BrandInfo
	(*BidResponse_Ad_MetaInfo_AppInfo)(nil),                    // 47: bes.BidResponse.Ad.MetaInfo.AppInfo
}
var file_bes_proto_depIdxs = []int32{
	15, // 0: bes.BidRequest.user_geo_info:type_name -> bes.BidRequest.Geo
	16, // 1: bes.BidRequest.mobile:type_name -> bes.BidRequest.Mobile
	17, // 2: bes.BidRequest.article_info:type_name -> bes.BidRequest.ArticleInfo
	18, // 3: bes.BidRequest.adslot:type_name -> bes.BidRequest.AdSlot
	1,  // 4: bes.BidRequest.host_limit:type_name -> bes.BidRequest.HostAdLimit
	19, // 5: bes.BidRequest.look_like:type_name -> bes.BidRequest.LookLike
	35, // 6: bes.BidResponse.ad:type_name -> bes.BidResponse.Ad
	20, // 7: bes.BidRequest.Geo.user_coordinate:type_name -> bes.BidRequest.Geo.Coordinate
	21, // 8: bes.BidRequest.Geo.user_location:type_name -> bes.BidRequest.Geo.UserLocation
	22, // 9: bes.BidRequest.Mobile.id:type_name -> bes.BidRequest.Mobile.MobileID
	3,  // 10: bes.BidRequest.Mobile.device_type:type_name -> bes.BidRequest.Mobile.MobileDeviceType
	4,  // 11: bes.BidRequest.Mobile.platform:type_name -> bes.BidRequest.Mobile.OS
	23, // 12: bes.BidRequest.Mobile.os_version:type_name -> bes.BidRequest.Mobile.DeviceOsVersion
	5,  // 13: bes.BidRequest.Mobile.wireless_network_type:type_name -> bes.BidRequest.Mobile.WirelessNetworkType
	24, // 14: bes.BidRequest.Mobile.for_advertising_id:type_name -> bes.BidRequest.Mobile.ForAdvertisingID
	25, // 15: bes.BidRequest.Mobile.mobile_app:type_name -> bes.BidRequest.Mobile.MobileApp
	26, // 16: bes.BidRequest.AdSlot.nativead_param:type_name -> bes.BidRequest.AdSlot.NativeAdParam
	27, // 17: bes.BidRequest.AdSlot.style_info:type_name -> bes.BidRequest.AdSlot.StyleInfo
	2,  // 18: bes.BidRequest.Geo.Coordinate.standard:type_name -> bes.BidRequest.Geo.Coordinate.Standard
	6,  // 19: bes.BidRequest.Mobile.MobileID.type:type_name -> bes.BidRequest.Mobile.MobileID.IDType
	7,  // 20: bes.BidRequest.Mobile.ForAdvertisingID.type:type_name -> bes.BidRequest.Mobile.ForAdvertisingID.IDType
	8,  // 21: bes.BidRequest.Mobile.MobileApp.app_interaction_type:type_name -> bes.BidRequest.Mobile.MobileApp.AppInteractionType
	28, // 22: bes.BidRequest.AdSlot.NativeAdParam.logo_icon:type_name -> bes.BidRequest.AdSlot.NativeAdParam.ImageEle
	28, // 23: bes.BidRequest.AdSlot.NativeAdParam.image:type_name -> bes.BidRequest.AdSlot.NativeAdParam.ImageEle
	29, // 24: bes.BidRequest.AdSlot.StyleInfo.ad_style:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle
	34, // 25: bes.BidRequest.AdSlot.StyleInfo.AdStyle.meta_style_group:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyleGroup
	30, // 26: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle.title_ele:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.TxtElement
	30, // 27: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle.desc_ele:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.TxtElement
	31, // 28: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle.icon_ele:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.ImageElement
	31, // 29: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle.image_ele:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.ImageElement
	32, // 30: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle.video_ele:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.VideoElement
	33, // 31: bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyleGroup.meta_style:type_name -> bes.BidRequest.AdSlot.StyleInfo.AdStyle.MetaStyle
	36, // 32: bes.BidResponse.Ad.deeplink_info:type_name -> bes.BidResponse.Ad.DeeplinkInfo
	37, // 33: bes.BidResponse.Ad.native_ad:type_name -> bes.BidResponse.Ad.NativeAd
	38, // 34: bes.BidResponse.Ad.material_info:type_name -> bes.BidResponse.Ad.MaterialInfo
	40, // 35: bes.BidResponse.Ad.meta_info_group:type_name -> bes.BidResponse.Ad.MetaInfoGroup
	41, // 36: bes.BidResponse.Ad.download_info:type_name -> bes.BidResponse.Ad.DownloadInfo
	11, // 37: bes.BidResponse.Ad.trans_type:type_name -> bes.BidResponse.Ad.TransType
	42, // 38: bes.BidResponse.Ad.NativeAd.image:type_name -> bes.BidResponse.Ad.NativeAd.Image
	42, // 39: bes.BidResponse.Ad.NativeAd.logo_icon:type_name -> bes.BidResponse.Ad.NativeAd.Image
	12, // 40: bes.BidResponse.Ad.MaterialInfo.material_pattern:type_name -> bes.BidResponse.Ad.MaterialInfo.MaterialPattern
	43, // 41: bes.BidResponse.Ad.MetaInfo.action_info:type_name -> bes.BidResponse.Ad.MetaInfo.ActionInfo
	44, // 42: bes.BidResponse.Ad.MetaInfo.title:type_name -> bes.BidResponse.Ad.MetaInfo.TxtMaterial
	44, // 43: bes.BidResponse.Ad.MetaInfo.desc:type_name -> bes.BidResponse.Ad.MetaInfo.TxtMaterial
	45, // 44: bes.BidResponse.Ad.MetaInfo.icon:type_name -> bes.BidResponse.Ad.MetaInfo.SrcMaterial
	45, // 45: bes.BidResponse.Ad.MetaInfo.image:type_name -> bes.BidResponse.Ad.MetaInfo.SrcMaterial
	46, // 46: bes.BidResponse.Ad.MetaInfo.brand_info:type_name -> bes.BidResponse.Ad.MetaInfo.BrandInfo
	45, // 47: bes.BidResponse.Ad.MetaInfo.video:type_name -> bes.BidResponse.Ad.MetaInfo.SrcMaterial
	47, // 48: bes.BidResponse.Ad.MetaInfo.app_info:type_name -> bes.BidResponse.Ad.MetaInfo.AppInfo
	36, // 49: bes.BidResponse.Ad.MetaInfo.deeplink_info:type_name -> bes.BidResponse.Ad.DeeplinkInfo
	45, // 50: bes.BidResponse.Ad.MetaInfo.button:type_name -> bes.BidResponse.Ad.MetaInfo.SrcMaterial
	39, // 51: bes.BidResponse.Ad.MetaInfoGroup.meta_info:type_name -> bes.BidResponse.Ad.MetaInfo
	35, // 52: bes.ad_status:extendee -> bes.BidResponse.Ad
	53, // [53:53] is the sub-list for method output_type
	53, // [53:53] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	52, // [52:53] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_bes_proto_init() }
func file_bes_proto_init() {
	if File_bes_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bes_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Mobile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_ArticleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_LookLike); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo_Coordinate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo_UserLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Mobile_MobileID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Mobile_DeviceOsVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Mobile_ForAdvertisingID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Mobile_MobileApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_NativeAdParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_NativeAdParam_ImageEle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle_TxtElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle_ImageElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle_VideoElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot_StyleInfo_AdStyle_MetaStyleGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_DeeplinkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_NativeAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfoGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_DownloadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_NativeAd_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo_ActionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo_TxtMaterial); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo_SrcMaterial); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo_BrandInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bes_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MetaInfo_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bes_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   35,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_bes_proto_goTypes,
		DependencyIndexes: file_bes_proto_depIdxs,
		EnumInfos:         file_bes_proto_enumTypes,
		MessageInfos:      file_bes_proto_msgTypes,
		ExtensionInfos:    file_bes_proto_extTypes,
	}.Build()
	File_bes_proto = out.File
	file_bes_proto_rawDesc = nil
	file_bes_proto_goTypes = nil
	file_bes_proto_depIdxs = nil
}
