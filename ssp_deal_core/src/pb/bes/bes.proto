// Copyright 2013-2015 Baidu Inc. All Rights Reserved.
//
// 文件内容: baidu exchange service 实时竞价协议文件
// 基于baidu_exchange_service.proto产出的BES外发版本
//
syntax = "proto2";
package bes;
option go_package = "mh_proxy/pb/bes";

message BidRequest {
  // 请求ID，唯一标识本次请求，明文字符串
  required string id = 1;

  // **** 用户信息 ****
  // 用户IP地址，点分十进制字符串
  optional string ip = 2;
  // IPV6地址, 冒分16进制
  // 保证线上服务稳定，ipv6&ipv4 采用不同的字段下发用户IP地址
  // ipv6 仅赋值 ip6 , ipv4 仅赋值 ip
  optional string ip6 = 42;
  // User-Agent
  optional string user_agent = 3;

  // 用户分类
  repeated int64 user_category = 6;

  // **** 位置信息 ****
  message Geo {
    // 经纬度信息
    message Coordinate {
      // 经纬度坐标标准
      enum Standard {
        // 百度地图的经纬度坐标标准
        BD_09 = 0;
        // 国测局制定的经纬度坐标标准
        GCJ_02 = 1;
        // 国际经纬度坐标标准
        WGS_84 = 2;
        // 百度地图的墨卡坐标标准,以米为单位
        BD_09_LL = 3;
      }
      // 地图坐标标准
      optional Standard standard = 1;
      // 维度
      optional float latitude = 2;
      // 经度
      optional float longitude = 3;
    }
    repeated Coordinate user_coordinate = 1;

    // 用户位置信息
    message UserLocation {
      // 省份
      optional string province = 1;
      // 城市
      optional string city = 2;
      // 区县
      optional string district = 3;
      // 街道
      optional string street = 4;
    }
    optional UserLocation user_location = 2;
  }
  optional Geo user_geo_info = 28;

  // 发布商不允许的广告行业
  repeated int32 excluded_product_category = 19 [packed = true];

  // **** 移动设备信息 ****
  message Mobile {

    // 新版移动设备序列号标识字段。允许同时存储多个序列号
    message MobileID {
      // 移动设备ID的类型
      enum IDType {
        // 未知
        UNKNOWN = 0;
        // IMEI
        IMEI = 1;
        // MAC地址
        MAC = 2;
        // 百度私有持久化用户ID, 支持系统: Android
        // ！！只传递给内部DSP
        CUID = 3;
        // OAID
        OAID = 4;
        // CAID
        CAID = 5;
      }
      optional IDType type = 1; // 序列号类型
      optional string id = 2;   // 序列号
      optional uint64 generate_timestamp = 3; // id生成时间，单位秒（仅CAID有效）
      optional bytes version = 4; // id版本(仅CAID有效）
      optional uint32 vendor = 5; // id供应商，0为热云，1为信通院（仅CAID有效
    }
    repeated MobileID id = 13;

    // 设备类型定义
    enum MobileDeviceType {
      UNKNOWN_DEVICE = 0;
      HIGHEND_PHONE = 1;
      TABLET = 2;
      SMART_TV = 4;
      OUTDOOR_SCREEN = 5;
    }
    // 设备类型
    optional MobileDeviceType device_type = 2;
    // 移动平台名，例如android，iphone等等
    enum OS {
      UNKNOWN_OS = 0;
      IOS = 1;
      ANDROID = 2;
      WINDOWS_PHONE = 3;
    }
    optional OS platform = 3 [default = UNKNOWN_OS];
    // 移动操作系统版本号
    // 例如 Android 2.1, major, micro分别是2,1
    // 例如 Iphone 4.2.1，major, minor, micro分别是4,2,1
    message DeviceOsVersion {
      optional int32 os_version_major = 1;
      optional int32 os_version_minor = 2;
      optional int32 os_version_micro = 3;
    };
    optional DeviceOsVersion os_version = 4;
    // 设备品牌
    optional string brand = 5;
    // 设备机型
    optional string model = 6;
    // SDK版本号：1.0.0
    optional string sdk_version = 41;
    // 设备屏宽
    optional int32 screen_width = 7;
    // 设备屏高
    optional int32 screen_height = 8;
    // 设备屏幕像素密度
    optional float screen_density = 15;
    // 运营商编号（MCC+MNC编号）
    // 例如中国移动 46000
    // 前三位是Mobile Country Code
    // 后两位是Mobile Network Code
    optional int64 carrier_id = 9;
    // 无线网络类型
    enum WirelessNetworkType {
      UNKNOWN_NETWORK = 0;
      WIFI = 1;
      MOBILE_2G = 2;
      MOBILE_3G = 3;
      MOBILE_4G = 4;
    }
    optional WirelessNetworkType wireless_network_type = 10;

    // 阿里系客户DSP反作弊参数，不是必填字段
    // boot_mark为系统开机时间戳，update_mark为系统最近一次更新时间戳
    // 只有IOS和Android有，buyer中对platform字段做校验后下发外部DSP
    optional bytes boot_mark = 34;
    optional bytes update_mark = 35;
    optional bytes ali_update_mark = 36;

    // 系统初始化时间
    optional bytes init_mark = 37;

    // 新版移动设备用户识别号字段，允许多种类型同时存在
    message ForAdvertisingID {
      enum IDType {
        UNKNOWN = 0;    // 未知
        ANDROID_ID = 4; // Android适用
        IDFA = 5;       // IOS适用
      }
      optional IDType type = 1; // ID类型
      optional string id = 2; // ID内容
    }
    repeated ForAdvertisingID for_advertising_id = 14;

    // **** 移动应用信息 ****
    message MobileApp {
      // 百度移动联盟为该App分配的app id
      optional string app_id = 1;
      // 如果来自苹果商店，则直接是app-store id
      // 如果来自Android设备，则是package的全名
      optional string app_bundle_id = 2;
      // App应用分类
      optional int32 app_category = 3;
      // App开发者ID
      optional int32 app_publisher_id = 4;
      // App允许的交互类型定义
      // 电话、下载、应用唤醒
      enum AppInteractionType {
        TELEPHONE = 0;
        DOWNLOAD = 1;
        DEEPLINK = 2;
      }
      // App允许的交互类型
      repeated AppInteractionType app_interaction_type = 5;
      // app 版本号
      optional string app_version = 6;
    }
    // 移动应用信息，当流量来自App时该字段非空
    optional MobileApp mobile_app = 12;
    // 厂商定制化ROM
    optional bytes rom_name = 16;
    // 厂商定制化系统ROM版本
    optional bytes rom_version = 17;
    // 拼多多归因参数V1.3
    // v1.3规则：paid=md5（系统更新时间）+md5（系统启动时间）
    optional bytes paid = 18;
    // 拼多多归因参数V1.4
    // paid=md5（系统初始化时间）+“-”+md5（系统更新时间）+“-”+md5（系统启动时间）
    optional bytes paid_new = 19;
  }
  // 移动设备信息，当流量来自移动设备时该字段非空
  optional Mobile mobile = 29;

  // 页面文章信息
  message ArticleInfo {
    optional string id = 1;
    optional bytes title = 2;
    repeated bytes category = 3;
    repeated bytes label = 4;
    optional bytes author_id = 5;
  }
  optional ArticleInfo article_info = 33;

  // **** 广告位信息 ****
  message AdSlot {
    // 广告位ID, 全局唯一id
    optional uint64 ad_block_key = 1;
    optional bytes ad_block_id = 28;
    // 当前页面广告位顺序id，同一页面从1开始
    optional int32 sequence_id = 2;
    // 曝光id
    optional string id = 1001;
    // 展示类型
    optional int32 adslot_type = 3;
    // 宽
    optional int32 width = 4;
    // 高
    optional int32 height = 5;
    // 广告位实际宽度
    optional int32 actual_width = 18;
    // 广告位实际高度
    optional int32 actual_height = 19;
    // 展示位置
    optional int32 slot_visibility = 6;
    // 发布商允许的创意类型
    repeated int32 creative_type = 7 [packed = true];
    // 发布商不允许的landing page url
    repeated string excluded_landing_page_url = 8;
    // 媒体保护设置信息的ID
    repeated fixed64 publisher_settings_list_id = 14;
    // 发布商设置的底价，单位分
    optional int32 minimum_cpm = 9;
    // 三方dsp动态底价
    optional int32 dynamic_min_cpm = 42;

    // 原生广告诉求参数，信息流单样式表达
    message NativeAdParam {
      enum Fields {
        // 标题
        TITLE = 0x1;
        // 内容描述
        DESC = 0x2;
        // 主题图
        IMAGE = 0x4;
        // logo 图标
        LOGOICON = 0x8;
        // 投放时下载的APP大小
        APPSIZE = 0x10;
        // 视频
        VIDEO = 0x40;
        // 品牌名
        BRAND_NAME = 0x80;
      }
      message ImageEle {
        // 宽
        optional int32 width = 1;
        // 高
        optional int32 height = 2;
        // 形状，
        // 0没有形状要求，
        // 1矩形，
        // 2圆形，
        // 3半圆形
        optional int32 shape = 3 [default = 0];
      }
      // 按照位图设置相应的位为1，不需要请求的位保持0
      // 示例：原生广告必须包含标题和图标，则required_fields = (0x1 | 0x8) = 9
      repeated int64 required_fields = 1;
      // 标题最大长度
      optional int32 title_max_length = 2;
      // 描述最大长度
      optional int32 desc_max_length = 3;
      // 广告主logo或图标的宽高、形状要求
      optional ImageEle logo_icon = 4;
      // 主题图的宽高、形状要求
      optional ImageEle image = 5;
      // 主题图数量，三图样式时image_num为3
      optional int32 image_num = 6;
    }
    // 是否允许返回非原生广告创意，
    // 如果为true，则对于存在原生诉求的情况下可以返回非原生广告创意，
    // 如果为false，则必须返回原生创意
    optional bool allowed_non_nativead = 23 [default = true];
    optional NativeAdParam nativead_param = 24;

    // 是否为HTTPS请求
    // 如果为true，则所有资源（图片、视频等）必须以HTTPS返回
    // 注意：url字段对应协议与secure字段的值并无严格对应关系，
    //       比如，存在url协议为HTTP而secure为true的情况，
    //       因此，需要使用secure字段来决定是否以HTTPS返回资源，而不要依赖url字段
    optional bool secure = 25 [default = false];

    // 广告样式信息，多样式请求，多样式请求优先级高于单样式
    message StyleInfo {
      // 样式类型
      // 28 大图底部文字
      // 29 大图上部文字
      // 30 大图上部文字 + logo
      // 31 大图两行文字
      // 32 大图无文字
      // 33 左图右文
      // 34 右图左文
      // 35 三图
      // 36 三图 + logo
      // 37 横版视频
      // 41 竖版视频
      // 100 激励视频横版
      // 102 激励视频竖版
      optional int32 style_type = 1;
      // ad样式元素要求
      optional AdStyle ad_style = 2;
      // ad：表达广告主投放意图的最小单元
      // meta：广告主呈现给用户的最小交互单元
      // metagroup：相同的meta归类为metagroup
      // element：组成meta的元素素材，包括title、desc、icon、image等
      message AdStyle {
        // 文本元素的描述
        message TxtElement {
          // 文本的最小字符数，建议填写，非必填，若不填写，则无要求
          optional int32 min_len = 1;
          // 文本的最大字符数，建议填写，非必填，若不填写，则无要求
          optional int32 max_len = 2;
        }

        // 图片元素的描述
        message ImageElement {
          // 图片宽，像素值
          optional int32 width = 1;
          // 图片高，像素值
          optional int32 height = 2;
        }
        // 视频元素的描述
        message VideoElement {
          // 视频宽，像素值
          optional int32 width = 1;
          // 视频高，像素值
          optional int32 height = 2;
          // 视频文件大小，单位Byte
          optional int32 size = 3;
          // 视频编码， 比如 h.264
          repeated string encoding = 4;
          // 视频最小比特率，单位 kbps
          optional int32 min_bitrate = 5;
          // 视频最大比特率，单位 kbps
          optional int32 max_bitrate = 6;
          // 允许的最大播放时长
          optional int32 max_duration = 7;
          // 允许的最小播放时长
          optional int32 min_duration = 8;
        }
        // 最小可交互区域元素类
        enum MetaElement {
          // 标题元素
          META_ELE_TITLE = 0x1;
          // 描述元素
          META_ELE_DESC = 0x2;
          // 图标元素
          META_ELE_ICON = 0x4;
          // 图片元素
          META_ELE_IMAGE = 0x8;
          // 品牌信息
          META_ELE_BRAND = 0x10;
          // 视频元素
          META_ELE_VIDEO = 0x20;
        }

        // 最小交互区域元素诉求描述
        message MetaStyle {
          // 元素诉求类型的bit位描述,
          // 例如某交互区域，由title和image元素组成
          // 则此时required_elements为9（1|8）,具体的title和image诉求需要
          // 通过title_ele和image_ele进行获取
          optional int64 required_elements = 1;
          // title元素
          repeated TxtElement title_ele = 2;
          // desc元素
          repeated TxtElement desc_ele = 3;
          // icon元素
          repeated ImageElement icon_ele = 4;
          // image元素
          repeated ImageElement image_ele = 5;
          // video元素
          repeated VideoElement video_ele = 6;
        }
        // 将诉求元素相同的MetaStyle按照MetaStyleGroup进行分类
        message MetaStyleGroup {
          // 该metagroup下面meta的个数
          optional int32 num = 1;
          // metagroup下面meta的具体描述
          optional MetaStyle meta_style = 2;
        }
        // ad的MetaStyleGroup的描述
        repeated MetaStyleGroup meta_style_group = 3;
      }
      // 因接口兼容，预留protobuf index 3，后续属性index从4开始添加
    }
    repeated StyleInfo style_info = 26;
    repeated int32 inventory_type = 27;   // 流量产品类型
    optional int32 max_ad_num = 29;       // 广告位允许最大广告数
    optional bytes cloud_settings = 30;   // 云控相关字段下发
  }

  // 默认每次请求一个广告位
  repeated AdSlot adslot = 20;

  // 此次请求所属于的流量特征包ID
  repeated uint32 tag_id = 34;
  // 与tag_id搭配使用，用于补充嗅探圈包命中的app信息
  repeated bytes installed_app_ids = 36;

  // 流量属性，代表当前流量支持的能力。
  // 是一个bitmap位图，每一位代表一个能力
  // 这里每个能力是向下兼容的，仅代表可以，不代表一定要，是否生效取决于DSP返回是否支持
  // 1 代表支持cpc投放，
  // 2 代表支持cpc计费
  // 4 代表支持OCPM投放
  // 8 代表支持OCPM计费
  optional uint32 attribute_info = 35 [default = 0];

  // 新流量类型
  enum NewTTP {
    TTP_UNKNOW = 0;
    TTP_PC = 1;
    TTP_WAP = 2;
    TTP_APP = 3;

    // 使用此值表示不区分流量类型
    // 如果有超过此值添加,同步更新为最大值
    TTP_MAX = 9999;
  }
  optional int32 new_ttp = 40;

  // **** 系统使用 ****
  optional bool is_test = 26 [default = false];
  optional bool is_ping = 27 [default = false];

  // 广告托管限制
  enum HostAdLimit {
    NO_LIMIT = 1;   // 无限制
    ONLY_HOST = 2;  // 仅支持托管物料
    NOT_HOST = 3;   // 仅支持非托管物料
  }
  optional HostAdLimit host_limit = 39 [default = NO_LIMIT];
  // 客户相似人群
  message LookLike {
    optional uint32 id = 1;
    // value取值[0-10000]，越大越相似
    optional int32 value = 2;
  }
  repeated LookLike look_like = 41;
  // 外部dsp联合实验id
  repeated int32 ex_exp_ids = 43;
  // >0 : second bidding for media
  optional int32 media_bid_type = 44;
}

message BidResponse {
  // 返回ID，将请求中的id赋值给返回id，便于session trace
  required string id = 1;

  // **** 竞价广告信息 ****
  message Ad {
    // 广告位顺序ID
    optional int32 sequence_id = 1;
    // 对应的曝光ID
    optional string impid = 1002;
    // 创意ID
    // 对于BES托管的静态创意（dsp将创意上传到BES, BES进行广告渲染），
    // creative_id唯一标识DSP上传到BES的每个创意（目前仅支持一个创意）。
    // 对于动态创意，creative_id唯一标识html snippet。BES会对动态创
    // 意的物料、监测地址及landing page等进行审核。DSP应保证含有相同物料、监测地址及landing page
    // 的html_snippet的snippet id相同，避免重复审核。但当html snippet中的物料、监测地址及landing page
    // 发生改变时，需要生成新的creative_id。
    // 对于关键词，creative_id唯一标识关键词。
    // 对于非BES托管的静态创意，creative_id唯一标识该静态创意
    optional int64 creative_id = 2;
    // 针对淘系（例如手淘）返回的字符串类型的创意ID，buyer 内部签名后，赋值至cretive_id
    optional string creative_id_str = 37;
    // html_snippet字段仅在返回动态创意时使用。DSP将拼装完成的
    // 创意及其物料拼装到html中，返回给BES。
    // BES经过判断，认为*拥有html_snippet字段的响应*属于动态创意，
    // 并通过此类字段获取必须的物料信息。相应数据中如遗漏字段，则
    // BES不能保证其参与竞价。
    //
    // 如何获知点击信息:
    // html snippet代码。该字段中需要填充click url的位置应填充
    // 宏%%CLICK_URL_{N}%%（这里{N}从0开始，应使用具体的序号代替，
    // 并与target_url中的顺序一致），并将click url填写至
    // target_url字段。BES会根据DSP的target_url
    // 构建最终click url之后，用其替换该宏。
    // 如下html中包含两个创意，则需要注册两个宏%%CLICK_URL_0%%和
    // %%CLICK_URL_1%%。并在target_url字段中顺序赋值。
    // std::string html("<BODY>...<a href="%%CLICK_URL_0%%"/>.."
    //  "<a href="%%CLICK_URL_1%%"/>...</BODY>"
    // ad.set_html_snippet(html);
    // ad.add_target_url("http://click.dsp.com?idea=ad1...");
    // ad.add_target_url("http://click.dsp.com?idea=ad2...");
    // 宏的错误（如顺序、遗漏等）或者target_url的赋值错
    // 误都会导致BES对target_url填充出错。
    //
    // 如何获知竞价后的计价信息:
    // 如DSP需要获知竞价成功后创意的cpm，可在期望的monitor_url
    // 字段特定位置添加宏%%PRICE%%。BES通过替换会使用cpm替换该宏。
    // 例:
    // http://wins.dsp.com?key1=val1&&cpm=%%PRICE%%...
    // 仅动态创意需要填充
    optional string html_snippet = 7;

    // 广告主id。
    // 动态创意及非BES托管的静态创意需要填充
    // 动态创意要求一个html snippet的所有广告属于同一个广告主。
    optional uint64 advertiser_id = 8;
    // 物料尺寸 - 宽度。需与请求中的尺寸一致
    // 动态创意及非BES托管的静态创意需要填充
    optional int32  width = 9;
    // 物料尺寸 - 高度。需与请求中的尺寸一致
    // 动态创意及非BES托管的静态创意需要填充
    optional int32  height = 10;
    // 创意所属行业的行业id。本字段的意义与静态创意中入库物料所需
    // 行业id相同。
    // 动态创意及非BES托管的静态创意需要填充
    optional int32  category = 11;
    // 创意的物料类型
    // 动态创意及非BES托管的静态创意需要填充
    optional int32  type = 12;
    // 创意的landing page。要求所有创意的landing page拥有相同的域，
    // 同时landing page应为target_url的最后一次跳转。
    // 注意: 这里仅填landing page的domain信息即可。如:
    // http://landing_page.advertiser.com/example.php?param1=...
    // 如上url的landing page应填入landing_page.advertiser.com。
    // 动态创意及非BES托管的静态创意需要填充
    optional string landing_page = 13;
    // 跳转厂商应用商店的下载链接
    optional bytes app_store_link = 36;
    // 创意的click url。响应中含有多个创意的情况下，每个创意click
    // url的顺序应与创意在html snippet中的顺序一致。BES将顺序进行
    // click url的替换。
    // 如该顺序不正确，将引发点击的统计偏差。
    // 动态创意及非BES托管的静态创意需要填充
    repeated string target_url = 14;
    // 曝光监测。
    // 非BES托管的静态创意需要填充
    repeated string monitor_urls = 17;
    // 竞胜通知。
    optional string nurl = 32;
    // 竞败通知。
    optional string lurl = 35;
    // 最高竞价，单位分
    optional int32 max_cpm = 3;
    // 扩展参数
    optional string extdata = 5;
    // 替换宏 %%EXT1%%
    optional string ext1 = 23;
    // 替换宏 %%EXT2%%
    optional string ext2 = 24;
    // 替换宏 %%EXT3%%
    optional string ext3 = 25;

    // APP 唤醒信息
    // 非BES托管的静态创意需要填充
    // 目前只有交互类型为应用唤醒时需要填写该字段
    message DeeplinkInfo {
      // 应用唤醒打开页面
      optional string deeplink_url = 1;
      // 应用唤醒版本
      optional uint32 app_version = 2;
      // 应用唤醒退化链接
      optional string fallback_url = 3;
      // 应用唤醒退化链接类型: LANDING_PAGE = 1, DOWNLOAD = 2
      optional uint32 fallback_type = 4;
      // 应用包名
      optional bytes app_bundle_id = 5;
      // 开发者名称
      optional bytes publisher = 6;
      // 版本号, 退化类型为下载时使用
      optional bytes download_app_version = 7;
      // 隐私协议
      optional bytes privacy_link = 8;
      // 用户权限
      optional bytes permission_link = 9;
      // universal link url, 用于ios调起, 优先级高于deeplink_url
      optional bytes ulk_url = 10;
      // 用于ios嗅探是否安装某个app
      optional bytes ulk_scheme = 11;
      // app name
      optional bytes app_name = 12;
    }
    optional DeeplinkInfo deeplink_info = 21;
    message NativeAd {
      // 标题,编码UTF-8
      optional bytes title = 1;
      // 描述,编码UTF-8
      optional bytes desc = 2;
      message Image {
        optional string url = 1;
        optional int32 width = 2;
        optional int32 height = 3;
      }
      // image,支持多图片
      repeated Image image = 3;
      // logo 或 icon
      optional Image logo_icon = 4;
      // app 大小
      optional int32 app_size = 5;
      // 品牌名称,编码UTF-8
      optional bytes brand_name = 6;
      // 创意关键字
      // 编码格式utf-8
      repeated bytes keyword = 7;
      // 视频地址
      optional string video_url = 8;
      // 视频宽高
      optional int32 video_width = 9;
      optional int32 video_height = 10;
      // 视频时长(秒)
      optional int32 video_duration = 11;
      // 视频大小（Byte）
      optional int32 video_size = 12;
    }
    // 仅非BES托管的原生广告填写，单样式信息流创意素材返回
    optional NativeAd native_ad = 22;

    // 非BES托管的普通广告创意物料信息，开屏等单创意返回
    message MaterialInfo {
      // 标题,编码UTF-8
      optional string title = 1;
      // 描述,编码UTF-8
      optional string desc = 2;
      // 物料地址,编码UTF-8
      // 视频物料只支持MP4
      optional string material_url = 3;
      // 视频广告时长, 单位是秒
      optional int32 video_duration = 4;
      // (仅用于序章dsp)
      // 物料规格
      enum MaterialPattern {
        MATERIAL_PATTERN_FULL_SCREEN = 1; // 全屏
        MATERIAL_PATTERN_HALF_SCREEN = 2; // 半屏
      }
      // (仅用于序章dsp)
      optional MaterialPattern material_pattern = 5;
      // (仅用于序章dsp)
      // 物料位置，在序章dsp投放开屏流量中，指的是按钮相对logo位置的值（%）
      optional float material_position_vertical = 6;
      // (仅用于序章dsp)
      // 物料的md5值
      optional string material_md5 = 7;
      // 创意关键字
      // 编码格式utf-8
      repeated bytes keyword = 8;
      // 视频广告大小，单位Byte
      optional int32 video_size = 9;
    }
    repeated MaterialInfo material_info = 26;

    // 最小交互区域的返回元素描述
    // 与请求中的MetaStyle相对应
    message MetaInfo {
      // 交互信息
      message ActionInfo {
        // 交互动作链接地址，如果交互触发类型为点击，则此链接为点击链接
        optional bytes action_url = 1;
        // 最终跳转到的链接地址
        optional bytes landing_page = 2;
        // 交互动作类型
        // 0 落地页
        // 16 下载
        // 1024 应用唤醒
        optional int32 action_type = 4;
        // 点击监控串(非302跳转的点击监控)
        repeated bytes click_monitor_url = 6;
        // app下载地址
        optional bytes app_download_url = 7;
        // 跳转厂商应用商店的下载链接
        optional bytes app_store_link = 8;
        // 并行计费串
        optional bytes charge_link = 9;
      }

      // 文本类物料，utf-8编码
      message TxtMaterial {
        // 文本的描述,utf-8编码
        optional bytes txt = 1;
      }
      // 资源类物料
      message SrcMaterial {
        // 主要是指JPG、GIF、SWF、MP4、AVI、MPEG、MOV、FLV等文件
        // 1: mp4, 2: 3gp, 3: flv, 4: avi, 5: mov, 6: mpg
        // 7: mpeg, 8: wmv, 9: rmvb, 10: mkv, 13: ts
        // 100: swf, 101: jpg, 102: gif, 103: png, 201: vr
        // 202: 3d
        optional int32 type = 1;
        // 资源物料url地址
        optional bytes url = 2;
        // 资源物料真实尺寸-宽
        optional int32 width = 3;
        // 资源物料真实尺寸-高
        optional int32 height = 4;
        // 视频物料的播放时长
        optional int32 video_duration = 5;
        // 资源物料的大小，单位 Byte
        optional int32 size = 6;
        // 视频物料的比特率，单位 kbps
        optional int32 bitrate = 7;
        // 视频物料的编码，比如 h.262
        optional string encoding = 8;
        // 物料的md5
        optional string md5 = 9;
        // 物料文本，utf-8编码，比如按钮上的文字 查看、下载等
        optional bytes text = 10;
      }
      // 品牌信息
      message BrandInfo {
        // 品牌名称
        optional bytes brand_name = 1;
        // 品牌vip等级，从1开始，0代码无效
        optional int32 brand_level = 2;
      }

      // 广告主推广的app相关信息
      message AppInfo {
        // 应用名称
        optional bytes app_name = 1;
        // 应用包名
        optional bytes package_name = 2;
        // 应用安装包的名字，如xxx.apk
        optional bytes apk_name = 3;
        // 应用大小，单位Byte
        optional int32 size = 4;
        // 应用下载数
        optional int32 downloads = 5;
        // 应用评分
        optional float rating = 6;
        // 应用评论数
        optional int32 comments = 7;
        // appstore的应用id，仅ios应用需要传递
        optional bytes app_id = 8;
        // 开发者
        optional bytes publisher = 9;
        // 版本号
        optional bytes app_version = 10;
        // 隐私协议
        optional bytes privacy_link = 11;
        // 用户权限
        optional bytes permission_link = 12;
      }
      // 交互信息
      optional ActionInfo action_info = 1;
      // 标题
      repeated TxtMaterial title = 2;
      // 文字描述
      repeated TxtMaterial desc = 3;
      // 图标
      repeated SrcMaterial icon = 4;
      // 图片
      repeated SrcMaterial image = 5;
      // 品牌信息
      optional BrandInfo brand_info = 6;
      // 行业
      repeated int32 category = 7;
      // 视频
      repeated SrcMaterial video = 9;
      // 广告主推广的app相关信息，下载类广告使用
      optional AppInfo app_info = 10;
      // 应用唤醒信息, 交互类型为ATTACH_APP_OPEN时需设置
      optional DeeplinkInfo deeplink_info = 11;
      // 按钮
      repeated SrcMaterial button = 13;
      // 物料类型
      // 0 文本
      // 1 图片
      // 4 图文
      // 7 视频
      // 9 html
      optional int32 creative_type = 15;
      // 创意关键字
      // 编码格式utf-8
      repeated bytes keyword = 16;
    }

    // 将组成元素相同的MetaInfo按照MetaInfoGroup进行分类
    // 与请求的MetaStyleGroup对应
    message MetaInfoGroup {
      // 返回的最小交互区域的元素集合
      repeated MetaInfo meta_info = 1;
    }
    // 对应StyleInfo返回的元素信息，多样式创意返回
    repeated MetaInfoGroup meta_info_group = 27;

    // 应用下载信息
    // 非BES托管的静态创意，在交互类型为DOWNLOAD时，需要填充
    // 下载应用包地址填写在target_url中
    message DownloadInfo {
      // 下载包大小（单位为byte）
      optional int32 app_size = 1;
      // 包名
      optional string app_package_name = 2;
      // ios_id Appstoreid
      optional string ios_id = 7;
      // doc_id 百度开放平台应用id
      optional string doc_id = 8;
      // 下载描述
      optional string download_desc = 9;
      // 开发者名称
      optional bytes publisher = 3;
      // 版本号
      optional bytes app_version = 4;
      // 隐私协议
      optional bytes privacy_link = 5;
      // 用户权限
      optional bytes permission_link = 6;
      // app name
      optional bytes app_name = 10;
    }
    optional DownloadInfo download_info = 28;

    // 表示预算类型，cpc/cpm
    // 0是CPM投放
    // 1是CPC投放
    // 4是OCPM投放
    optional int32 budget_type = 30 [default = 0];
    // cpc预算广告主出价
    optional int32 bid = 31 [default = 0];
    // 请求为多选创意时，返回的目标创意类型, 对应请求中的StyleInfo.style_type
    optional int32 style_type = 33;
    // 表示计费类型
    // 0是CPM计费
    // 1是CPC计费
    // 2是OCPM计费
    optional int32 charge_type = 34 [default = 0];
    // OCPM预算广告主出价
    optional int32 obid = 38 [default = 0];
    // OCPM目标转化类型
    enum TransType {
      UNKNOWN_TYPE = 0;
      // 表单提交成功
      FORM_SUBMIT = 3;
      // 表单按钮点击
      FORM_CLICK = 5;
      // 表单有效请求，即Nginx收到表单请求打点
      FORM_VAILD_REQUEST = 13;
      // 表单调起按钮点击
      FORM_PULL_UP_CLICK = 16;
      // 应用调起
      APP_UP = 71;
    }
    optional TransType trans_type = 39;
    // OCPM广告主计划id
    optional int32 plan_id = 40;
    // 当前最大index 为 40,每次新增时此处需同步

    extensions 100 to 199;
  }
  // BES侧完成分流,
  // 对于非多广告流量只支持一个竞价广告返回
  repeated Ad ad = 2;

  // **** 系统使用 ****
  // debug接口
  optional string debug_string = 3;
  // DSP处理时间
  optional int32 processing_time_ms = 4;

  // 参与竞价的最大广告数量限制
  optional int32 ads_num_limit = 5;

  // 短时屏蔽相关字段
  // 屏蔽状态
  // 0:不过滤;1:tu过滤;2:设备过滤
  optional uint32 block_status = 6;
  // 过滤结束时间
  optional uint64 block_end_time = 7;

}

// for our system
extend BidResponse.Ad {
  optional int32 ad_status = 101;
}

