// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.19.4
// source: ifeng_rtb.proto

package ifeng

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 竞价请求唯一识别符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 竞价类型。1 = First Price, 2 = Second Price Plus. 当前固定为2。也就是采用第二名出价价格作为成交价。
	At int32 `protobuf:"varint,2,opt,name=at,proto3" json:"at,omitempty"`
	//平台类型。1、PC，2、WAP，3、APP
	Platform int32              `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	Imp      []*BidRequest_Imp  `protobuf:"bytes,4,rep,name=imp,proto3" json:"imp,omitempty"`
	App      *BidRequest_App    `protobuf:"bytes,5,opt,name=app,proto3" json:"app,omitempty"`
	Device   *BidRequest_Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	User     *BidRequest_User   `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
	//是否测试模式，0 = 线上， 1 = 测试模式
	Test int32 `protobuf:"varint,8,opt,name=test,proto3" json:"test,omitempty"`
	//禁投广告主行业类型
	Bindus []string `protobuf:"bytes,9,rep,name=bindus,proto3" json:"bindus,omitempty"`
	//只投广告主行业类型
	DeliverTrade []string `protobuf:"bytes,10,rep,name=deliverTrade,proto3" json:"deliverTrade,omitempty"`
	//禁投广告词
	Forbidenwords []string `protobuf:"bytes,11,rep,name=forbidenwords,proto3" json:"forbidenwords,omitempty"`
	//只投广告词
	Onlywords []string `protobuf:"bytes,12,rep,name=onlywords,proto3" json:"onlywords,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *BidRequest) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetTest() int32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *BidRequest) GetBindus() []string {
	if x != nil {
		return x.Bindus
	}
	return nil
}

func (x *BidRequest) GetDeliverTrade() []string {
	if x != nil {
		return x.DeliverTrade
	}
	return nil
}

func (x *BidRequest) GetForbidenwords() []string {
	if x != nil {
		return x.Forbidenwords
	}
	return nil
}

func (x *BidRequest) GetOnlywords() []string {
	if x != nil {
		return x.Onlywords
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 竞价响应识别符，如不等同于竞价请求，拒绝该竞价响应（对应bidreqeust中的id）
	Id      string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,2,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
	//竞价响应id，用于辅助竞价方追踪监测
	Bidid string `protobuf:"bytes,3,opt,name=bidid,proto3" json:"bidid,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

// 广告位对象广告曝光请求对象组。在一个竞价请求中可包含多个广告曝光请求。一次有效的竞价请求至少有一个广告曝光请求。
type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//竞价请求中广告曝光请求的唯一识别符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//广告类型。1、原生；2视频贴片
	Adtype int32                    `protobuf:"varint,2,opt,name=adtype,proto3" json:"adtype,omitempty"`
	Native []*BidRequest_Imp_Native `protobuf:"bytes,3,rep,name=native,proto3" json:"native,omitempty"`
	Video  *BidRequest_Imp_Video    `protobuf:"bytes,4,opt,name=video,proto3" json:"video,omitempty"`
	//特定广告位置或标签的识别符。当前版本设置为凤飞系统中的广告位id。
	Tagid string `protobuf:"bytes,5,opt,name=tagid,proto3" json:"tagid,omitempty"`
	//标明响应是否需要是https。0：http；1：https。
	Secure int32 `protobuf:"varint,6,opt,name=secure,proto3" json:"secure,omitempty"`
	//竞拍底价。CPM记，单位为人民币分。
	Bidfloor int64               `protobuf:"varint,7,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Pmp      *BidRequest_Imp_Pmp `protobuf:"bytes,8,opt,name=pmp,proto3" json:"pmp,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetAdtype() int32 {
	if x != nil {
		return x.Adtype
	}
	return 0
}

func (x *BidRequest_Imp) GetNative() []*BidRequest_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetTagid() string {
	if x != nil {
		return x.Tagid
	}
	return ""
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequest_Imp) GetBidfloor() int64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetPmp() *BidRequest_Imp_Pmp {
	if x != nil {
		return x.Pmp
	}
	return nil
}

//App信息
type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//凤飞系统app唯一标识符
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//app名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	//app本次请求对应包名
	Bundle string `protobuf:"bytes,3,opt,name=bundle,proto3" json:"bundle,omitempty"`
	//app版本号
	Ver string `protobuf:"bytes,4,opt,name=ver,proto3" json:"ver,omitempty"`
	//app 类型
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_App) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *BidRequest_App) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 设备信息
type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 浏览器user agent
	Ua string `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`
	// IPv4地址
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// 设备IMEI号,MD5加密
	Didmd5 string `protobuf:"bytes,3,opt,name=didmd5,proto3" json:"didmd5,omitempty"`
	// iOS终端设备的identifier for advertising明文，保持字母大写。
	Ifa string `protobuf:"bytes,4,opt,name=ifa,proto3" json:"ifa,omitempty"`
	// Android终端设备的AndroidID，MD5加密
	Dpidmd5 string `protobuf:"bytes,5,opt,name=dpidmd5,proto3" json:"dpidmd5,omitempty"`
	// 终端网卡MAC地址，MD5加密
	Macmd5 string `protobuf:"bytes,6,opt,name=macmd5,proto3" json:"macmd5,omitempty"`
	// 网络连接类型。0.未知;1.WIFI;2.2G;3.3G;4.4G;5.5G
	Connectiontype int32 `protobuf:"varint,7,opt,name=connectiontype,proto3" json:"connectiontype,omitempty"`
	// 运营商。0.未知;1.中国移动;2.中国联通;3.中国电信;
	Carrier int32 `protobuf:"varint,8,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// 设备制造商(例如：Apple)
	Make string `protobuf:"bytes,9,opt,name=make,proto3" json:"make,omitempty"`
	// 设备型号(例如：iPhone)
	Model string `protobuf:"bytes,10,opt,name=model,proto3" json:"model,omitempty"`
	// 设备操作系统(例如：iOS)
	Os string `protobuf:"bytes,11,opt,name=os,proto3" json:"os,omitempty"`
	// 操作系统版本(例如：4.2.1)
	Osv string `protobuf:"bytes,12,opt,name=osv,proto3" json:"osv,omitempty"`
	// 设备屏幕像素高
	H int32 `protobuf:"varint,13,opt,name=h,proto3" json:"h,omitempty"`
	// 设备屏幕像素宽
	W int32 `protobuf:"varint,14,opt,name=w,proto3" json:"w,omitempty"`
	// 设备像素密度
	Ppi   int32                  `protobuf:"varint,15,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo   *BidRequest_Device_Geo `protobuf:"bytes,16,opt,name=geo,proto3" json:"geo,omitempty"`
	Brand string                 `protobuf:"bytes,17,opt,name=brand,proto3" json:"brand,omitempty"`
	Did   string                 `protobuf:"bytes,18,opt,name=did,proto3" json:"did,omitempty"`
	Oaid  string                 `protobuf:"bytes,19,opt,name=oaid,proto3" json:"oaid,omitempty"`
	//系统更新标识，原值传输，取值参见附录一， iOS：1581141691.570419583 Android： 1004697.70999999
	UpdateMark string `protobuf:"bytes,20,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	//系统启动标识，原值传输，取值参见附录一， iOS：1623815045.970028， Android： ec7f4f33-411a-47bc-8067-744a4e7e072
	BootMark string `protobuf:"bytes,21,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	// 设备类型：0.未知;1.pc;2.phone;3.pad;4.tv
	Devicetype int32 `protobuf:"varint,22,opt,name=devicetype,proto3" json:"devicetype,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetDidmd5() string {
	if x != nil {
		return x.Didmd5
	}
	return ""
}

func (x *BidRequest_Device) GetIfa() string {
	if x != nil {
		return x.Ifa
	}
	return ""
}

func (x *BidRequest_Device) GetDpidmd5() string {
	if x != nil {
		return x.Dpidmd5
	}
	return ""
}

func (x *BidRequest_Device) GetMacmd5() string {
	if x != nil {
		return x.Macmd5
	}
	return ""
}

func (x *BidRequest_Device) GetConnectiontype() int32 {
	if x != nil {
		return x.Connectiontype
	}
	return 0
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetDid() string {
	if x != nil {
		return x.Did
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetDevicetype() int32 {
	if x != nil {
		return x.Devicetype
	}
	return 0
}

// 用户对象
type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用用户ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

//原生广告请求对象
type BidRequest_Imp_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image []*BidRequest_Imp_Native_Image `protobuf:"bytes,1,rep,name=image,proto3" json:"image,omitempty"`
	//广告形式
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	//广告位宽度（pixels）
	W int32 `protobuf:"varint,3,opt,name=w,proto3" json:"w,omitempty"`
	//广告位高度（pixels）
	H    int32                       `protobuf:"varint,4,opt,name=h,proto3" json:"h,omitempty"`
	Icon *BidRequest_Imp_Native_Icon `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	//标题文字长度
	Titlelen int32 `protobuf:"varint,6,opt,name=titlelen,proto3" json:"titlelen,omitempty"`
	//描述文字长度
	Textlen int32 `protobuf:"varint,7,opt,name=textlen,proto3" json:"textlen,omitempty"`
	//1、下载；2、唤醒（deeplink）
	Support []int32 `protobuf:"varint,8,rep,packed,name=support,proto3" json:"support,omitempty"`
}

func (x *BidRequest_Imp_Native) Reset() {
	*x = BidRequest_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native) ProtoMessage() {}

func (x *BidRequest_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Native) GetImage() []*BidRequest_Imp_Native_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BidRequest_Imp_Native) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetIcon() *BidRequest_Imp_Native_Icon {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetTitlelen() int32 {
	if x != nil {
		return x.Titlelen
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetTextlen() int32 {
	if x != nil {
		return x.Textlen
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetSupport() []int32 {
	if x != nil {
		return x.Support
	}
	return nil
}

//视频贴片请求对象
type BidRequest_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//支持的文件类型，固定为：video/mp4
	Mimes []string `protobuf:"bytes,1,rep,name=mimes,proto3" json:"mimes,omitempty"`
	//视频素材宽度（pixels）
	W int32 `protobuf:"varint,2,opt,name=w,proto3" json:"w,omitempty"`
	//视频素材高度（pixels）
	H int32 `protobuf:"varint,3,opt,name=h,proto3" json:"h,omitempty"`
	//最大播放时长，单位秒
	Maxduration int32 `protobuf:"varint,4,opt,name=maxduration,proto3" json:"maxduration,omitempty"`
	//最小播放时长，单位秒
	Minduration int32 `protobuf:"varint,5,opt,name=minduration,proto3" json:"minduration,omitempty"`
	//时长的误差值，单位为毫秒。
	Diffduration int32 `protobuf:"varint,6,opt,name=diffduration,proto3" json:"diffduration,omitempty"`
}

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Imp_Video) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_Imp_Video) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxduration() int32 {
	if x != nil {
		return x.Maxduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMinduration() int32 {
	if x != nil {
		return x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetDiffduration() int32 {
	if x != nil {
		return x.Diffduration
	}
	return 0
}

//该广告位请求对应私有交易对象
type BidRequest_Imp_Pmp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deals   *BidRequest_Imp_Pmp_Deal `protobuf:"bytes,1,opt,name=deals,proto3" json:"deals,omitempty"`
	Withrtb int32                    `protobuf:"varint,2,opt,name=withrtb,proto3" json:"withrtb,omitempty"`
}

func (x *BidRequest_Imp_Pmp) Reset() {
	*x = BidRequest_Imp_Pmp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *BidRequest_Imp_Pmp) GetDeals() *BidRequest_Imp_Pmp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *BidRequest_Imp_Pmp) GetWithrtb() int32 {
	if x != nil {
		return x.Withrtb
	}
	return 0
}

//图片数组对象
type BidRequest_Imp_Native_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//素材宽度
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//素材高度
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
}

func (x *BidRequest_Imp_Native_Image) Reset() {
	*x = BidRequest_Imp_Native_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Image) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Image) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Image.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Image) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *BidRequest_Imp_Native_Image) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Image) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

//图标/Logo对象
type BidRequest_Imp_Native_Icon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//图标宽度
	W int32 `protobuf:"varint,1,opt,name=w,proto3" json:"w,omitempty"`
	//图标高度
	H int32 `protobuf:"varint,2,opt,name=h,proto3" json:"h,omitempty"`
}

func (x *BidRequest_Imp_Native_Icon) Reset() {
	*x = BidRequest_Imp_Native_Icon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Icon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Icon) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Icon) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Icon.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Icon) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 0, 1}
}

func (x *BidRequest_Imp_Native_Icon) GetW() int32 {
	if x != nil {
		return x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Icon) GetH() int32 {
	if x != nil {
		return x.H
	}
	return 0
}

type BidRequest_Imp_Pmp_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//交易id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//约定交易价格
	Price int64 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *BidRequest_Imp_Pmp_Deal) Reset() {
	*x = BidRequest_Imp_Pmp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Pmp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Pmp_Deal) ProtoMessage() {}

func (x *BidRequest_Imp_Pmp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Pmp_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Pmp_Deal) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 0, 2, 0}
}

func (x *BidRequest_Imp_Pmp_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp_Pmp_Deal) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type BidRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 经度，取值范围[-180.0 , +180.0]
	Lon float32 `protobuf:"fixed32,1,opt,name=lon,proto3" json:"lon,omitempty"`
	// 纬度，取值范围[-90.0 , +90.0]
	Lat float32 `protobuf:"fixed32,2,opt,name=lat,proto3" json:"lat,omitempty"`
}

func (x *BidRequest_Device_Geo) Reset() {
	*x = BidRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Geo) ProtoMessage() {}

func (x *BidRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *BidRequest_Device_Geo) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

//竞价席位对象数组。
type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_SeatBid_Bid `protobuf:"bytes,1,rep,name=bid,proto3" json:"bid,omitempty"`
	//代表出价的竞价方席位识别符，由凤飞系统生成并提供
	Seat string `protobuf:"bytes,2,opt,name=seat,proto3" json:"seat,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_SeatBid_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse_SeatBid) GetSeat() string {
	if x != nil {
		return x.Seat
	}
	return ""
}

//出价对象数组。每个出价对象关联竞价请求中的一个广告曝光请求对象
type BidResponse_SeatBid_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Impid string `protobuf:"bytes,2,opt,name=impid,proto3" json:"impid,omitempty"`
	//竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
	Dealid string `protobuf:"bytes,3,opt,name=dealid,proto3" json:"dealid,omitempty"`
	//广告出价。CPM记，单位为人民币分。非私有交易必须返回
	Price int64 `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	//广告类型。1、原生；2视频贴片
	Adtype   int32                             `protobuf:"varint,5,opt,name=adtype,proto3" json:"adtype,omitempty"`
	Creative *BidResponse_SeatBid_Bid_Creative `protobuf:"bytes,6,opt,name=creative,proto3" json:"creative,omitempty"`
	//视频贴片vast代码
	Vast string `protobuf:"bytes,7,opt,name=vast,proto3" json:"vast,omitempty"`
	//曝光监测地址数组（异步发送，支持宏替换）
	Aimps []string `protobuf:"bytes,8,rep,name=aimps,proto3" json:"aimps,omitempty"`
	//点击监测地址数组（异步发送，支持宏替换）
	Aclks []string `protobuf:"bytes,9,rep,name=aclks,proto3" json:"aclks,omitempty"`
	//胜出通知。（由Server端发送，支持宏替换）
	Nurl string `protobuf:"bytes,10,opt,name=nurl,proto3" json:"nurl,omitempty"`
}

func (x *BidResponse_SeatBid_Bid) Reset() {
	*x = BidResponse_SeatBid_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_SeatBid_Bid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetDealid() string {
	if x != nil {
		return x.Dealid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetAdtype() int32 {
	if x != nil {
		return x.Adtype
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid) GetCreative() *BidResponse_SeatBid_Bid_Creative {
	if x != nil {
		return x.Creative
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetVast() string {
	if x != nil {
		return x.Vast
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid) GetAimps() []string {
	if x != nil {
		return x.Aimps
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetAclks() []string {
	if x != nil {
		return x.Aclks
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

//广告创意
type BidResponse_SeatBid_Bid_Creative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//广告创意ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	//图片素材地址数组
	Images []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
	//创意落地页地址
	Landpage string `protobuf:"bytes,3,opt,name=landpage,proto3" json:"landpage,omitempty"`
	//下载型创意直接下载地址,为防止出现问题，先标记弃用，将来可能会删除
	//新的功能会移到app对象中
	//
	// Deprecated: Do not use.
	Dlurl string `protobuf:"bytes,4,opt,name=dlurl,proto3" json:"dlurl,omitempty"`
	//Deep link地址
	Dplurl string `protobuf:"bytes,5,opt,name=dplurl,proto3" json:"dplurl,omitempty"`
	//图标/Logo地址或Base64编码（用http开头区分）
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	//创意标题，超出Request要求会被截断
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	//创意标题，超出Request要求会被截断
	Text string `protobuf:"bytes,8,opt,name=text,proto3" json:"text,omitempty"`
	//创意补充，简介
	Desc string `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`
	//原生广告返回素材对应广告形式
	Type string                                `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	App  *BidResponse_SeatBid_Bid_Creative_App `protobuf:"bytes,11,opt,name=app,proto3" json:"app,omitempty"`
	// 下载类型0.普通下载(默认)；1.广点通下载
	Daction int32 `protobuf:"varint,12,opt,name=daction,proto3" json:"daction,omitempty"`
	//Deep link唤起成功监测数组
	Dpsurls []string `protobuf:"bytes,13,rep,name=dpsurls,proto3" json:"dpsurls,omitempty"`
	//复制码
	Copycode string `protobuf:"bytes,14,opt,name=copycode,proto3" json:"copycode,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Creative) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetLandpage() string {
	if x != nil {
		return x.Landpage
	}
	return ""
}

// Deprecated: Do not use.
func (x *BidResponse_SeatBid_Bid_Creative) GetDlurl() string {
	if x != nil {
		return x.Dlurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDplurl() string {
	if x != nil {
		return x.Dplurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative) GetApp() *BidResponse_SeatBid_Bid_Creative_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDaction() int32 {
	if x != nil {
		return x.Daction
	}
	return 0
}

func (x *BidResponse_SeatBid_Bid_Creative) GetDpsurls() []string {
	if x != nil {
		return x.Dpsurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative) GetCopycode() string {
	if x != nil {
		return x.Copycode
	}
	return ""
}

type BidResponse_SeatBid_Bid_Creative_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//app中文名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	//直接下载地址，目的是想要替换掉Creative里的dlurl
	Dlurl string `protobuf:"bytes,2,opt,name=dlurl,proto3" json:"dlurl,omitempty"`
	//android用，包名
	Package string `protobuf:"bytes,3,opt,name=package,proto3" json:"package,omitempty"`
	//ios用，APP_ID
	Appid string `protobuf:"bytes,4,opt,name=appid,proto3" json:"appid,omitempty"`
	//开始下载监测
	Dsurls []string `protobuf:"bytes,7,rep,name=dsurls,proto3" json:"dsurls,omitempty"`
	// 下载完成监测
	Dcurls []string `protobuf:"bytes,8,rep,name=dcurls,proto3" json:"dcurls,omitempty"`
	//开始安装监测
	Installstarturls []string `protobuf:"bytes,9,rep,name=installstarturls,proto3" json:"installstarturls,omitempty"`
	//安装完成监测
	Installedurls []string `protobuf:"bytes,10,rep,name=installedurls,proto3" json:"installedurls,omitempty"`
}

func (x *BidResponse_SeatBid_Bid_Creative_App) Reset() {
	*x = BidResponse_SeatBid_Bid_Creative_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ifeng_rtb_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid_Bid_Creative_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid_Bid_Creative_App) ProtoMessage() {}

func (x *BidResponse_SeatBid_Bid_Creative_App) ProtoReflect() protoreflect.Message {
	mi := &file_ifeng_rtb_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid_Bid_Creative_App.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid_Bid_Creative_App) Descriptor() ([]byte, []int) {
	return file_ifeng_rtb_proto_rawDescGZIP(), []int{1, 0, 0, 0, 0}
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDlurl() string {
	if x != nil {
		return x.Dlurl
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDsurls() []string {
	if x != nil {
		return x.Dsurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetDcurls() []string {
	if x != nil {
		return x.Dcurls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetInstallstarturls() []string {
	if x != nil {
		return x.Installstarturls
	}
	return nil
}

func (x *BidResponse_SeatBid_Bid_Creative_App) GetInstalledurls() []string {
	if x != nil {
		return x.Installedurls
	}
	return nil
}

var File_ifeng_rtb_proto protoreflect.FileDescriptor

var file_ifeng_rtb_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x22, 0xf0, 0x0f, 0x0a,
	0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2b, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52,
	0x03, 0x69, 0x6d, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70,
	0x70, 0x12, 0x34, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74,
	0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x62,
	0x69, 0x6e, 0x64, 0x75, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6e,
	0x64, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x62, 0x69,
	0x64, 0x65, 0x6e, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x65, 0x6e, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x6e, 0x6c, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x6e, 0x6c, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x1a, 0x94, 0x07, 0x0a, 0x03,
	0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x66,
	0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x61, 0x67, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69,
	0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69,
	0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x2f, 0x0a, 0x03, 0x70, 0x6d, 0x70, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50,
	0x6d, 0x70, 0x52, 0x03, 0x70, 0x6d, 0x70, 0x1a, 0xca, 0x02, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68,
	0x12, 0x39, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x6c, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x6c, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x65, 0x78, 0x74, 0x6c,
	0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x65, 0x78, 0x74, 0x6c, 0x65,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x23, 0x0a, 0x05, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68,
	0x1a, 0x22, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x01, 0x68, 0x1a, 0xa1, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d,
	0x69, 0x6d, 0x65, 0x73, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x69, 0x66, 0x66, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x69, 0x66, 0x66,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x87, 0x01, 0x0a, 0x03, 0x50, 0x6d, 0x70,
	0x12, 0x38, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x6d, 0x70, 0x2e, 0x44,
	0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x69,
	0x74, 0x68, 0x72, 0x74, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x77, 0x69, 0x74,
	0x68, 0x72, 0x74, 0x62, 0x1a, 0x2c, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x1a, 0x67, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xb9, 0x04, 0x0a, 0x06,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x66, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x66, 0x61,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61,
	0x63, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x6d,
	0x64, 0x35, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x0c,
	0x0a, 0x01, 0x77, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x70, 0x69, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x32,
	0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x66,
	0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67,
	0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x69, 0x64, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x29, 0x0a, 0x03,
	0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x1a, 0x16, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xb4, 0x08, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x38, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x1a,
	0xc4, 0x07, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x03, 0x62,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x66, 0x65, 0x6e, 0x67,
	0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x65, 0x61, 0x74, 0x1a, 0xee, 0x06, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x61, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x08, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x66,
	0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x61, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x76, 0x61, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x69, 0x6d, 0x70, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x61, 0x69, 0x6d, 0x70, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x63, 0x6c, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63, 0x6c,
	0x6b, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x1a, 0xdd, 0x04, 0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x64, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x64, 0x70, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x05, 0x64, 0x6c, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x64, 0x6c, 0x75, 0x72,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x70, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x70, 0x6c, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x41, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69,
	0x66, 0x65, 0x6e, 0x67, 0x5f, 0x72, 0x74, 0x62, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61,
	0x70, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x64, 0x70, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x70, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x63, 0x6f,
	0x64, 0x65, 0x1a, 0xe1, 0x01, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x64, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64,
	0x6c, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x73, 0x75, 0x72, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x63, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x63,
	0x75, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x72, 0x6c, 0x73,
	0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x75, 0x72, 0x6c, 0x73, 0x42, 0x13, 0x5a, 0x11, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x66, 0x65, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_ifeng_rtb_proto_rawDescOnce sync.Once
	file_ifeng_rtb_proto_rawDescData = file_ifeng_rtb_proto_rawDesc
)

func file_ifeng_rtb_proto_rawDescGZIP() []byte {
	file_ifeng_rtb_proto_rawDescOnce.Do(func() {
		file_ifeng_rtb_proto_rawDescData = protoimpl.X.CompressGZIP(file_ifeng_rtb_proto_rawDescData)
	})
	return file_ifeng_rtb_proto_rawDescData
}

var file_ifeng_rtb_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_ifeng_rtb_proto_goTypes = []interface{}{
	(*BidRequest)(nil),                           // 0: ifeng_rtb.BidRequest
	(*BidResponse)(nil),                          // 1: ifeng_rtb.BidResponse
	(*BidRequest_Imp)(nil),                       // 2: ifeng_rtb.BidRequest.Imp
	(*BidRequest_App)(nil),                       // 3: ifeng_rtb.BidRequest.App
	(*BidRequest_Device)(nil),                    // 4: ifeng_rtb.BidRequest.Device
	(*BidRequest_User)(nil),                      // 5: ifeng_rtb.BidRequest.User
	(*BidRequest_Imp_Native)(nil),                // 6: ifeng_rtb.BidRequest.Imp.Native
	(*BidRequest_Imp_Video)(nil),                 // 7: ifeng_rtb.BidRequest.Imp.Video
	(*BidRequest_Imp_Pmp)(nil),                   // 8: ifeng_rtb.BidRequest.Imp.Pmp
	(*BidRequest_Imp_Native_Image)(nil),          // 9: ifeng_rtb.BidRequest.Imp.Native.Image
	(*BidRequest_Imp_Native_Icon)(nil),           // 10: ifeng_rtb.BidRequest.Imp.Native.Icon
	(*BidRequest_Imp_Pmp_Deal)(nil),              // 11: ifeng_rtb.BidRequest.Imp.Pmp.Deal
	(*BidRequest_Device_Geo)(nil),                // 12: ifeng_rtb.BidRequest.Device.Geo
	(*BidResponse_SeatBid)(nil),                  // 13: ifeng_rtb.BidResponse.SeatBid
	(*BidResponse_SeatBid_Bid)(nil),              // 14: ifeng_rtb.BidResponse.SeatBid.Bid
	(*BidResponse_SeatBid_Bid_Creative)(nil),     // 15: ifeng_rtb.BidResponse.SeatBid.Bid.Creative
	(*BidResponse_SeatBid_Bid_Creative_App)(nil), // 16: ifeng_rtb.BidResponse.SeatBid.Bid.Creative.App
}
var file_ifeng_rtb_proto_depIdxs = []int32{
	2,  // 0: ifeng_rtb.BidRequest.imp:type_name -> ifeng_rtb.BidRequest.Imp
	3,  // 1: ifeng_rtb.BidRequest.app:type_name -> ifeng_rtb.BidRequest.App
	4,  // 2: ifeng_rtb.BidRequest.device:type_name -> ifeng_rtb.BidRequest.Device
	5,  // 3: ifeng_rtb.BidRequest.user:type_name -> ifeng_rtb.BidRequest.User
	13, // 4: ifeng_rtb.BidResponse.seatbid:type_name -> ifeng_rtb.BidResponse.SeatBid
	6,  // 5: ifeng_rtb.BidRequest.Imp.native:type_name -> ifeng_rtb.BidRequest.Imp.Native
	7,  // 6: ifeng_rtb.BidRequest.Imp.video:type_name -> ifeng_rtb.BidRequest.Imp.Video
	8,  // 7: ifeng_rtb.BidRequest.Imp.pmp:type_name -> ifeng_rtb.BidRequest.Imp.Pmp
	12, // 8: ifeng_rtb.BidRequest.Device.geo:type_name -> ifeng_rtb.BidRequest.Device.Geo
	9,  // 9: ifeng_rtb.BidRequest.Imp.Native.image:type_name -> ifeng_rtb.BidRequest.Imp.Native.Image
	10, // 10: ifeng_rtb.BidRequest.Imp.Native.icon:type_name -> ifeng_rtb.BidRequest.Imp.Native.Icon
	11, // 11: ifeng_rtb.BidRequest.Imp.Pmp.deals:type_name -> ifeng_rtb.BidRequest.Imp.Pmp.Deal
	14, // 12: ifeng_rtb.BidResponse.SeatBid.bid:type_name -> ifeng_rtb.BidResponse.SeatBid.Bid
	15, // 13: ifeng_rtb.BidResponse.SeatBid.Bid.creative:type_name -> ifeng_rtb.BidResponse.SeatBid.Bid.Creative
	16, // 14: ifeng_rtb.BidResponse.SeatBid.Bid.Creative.app:type_name -> ifeng_rtb.BidResponse.SeatBid.Bid.Creative.App
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_ifeng_rtb_proto_init() }
func file_ifeng_rtb_proto_init() {
	if File_ifeng_rtb_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ifeng_rtb_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Icon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Pmp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ifeng_rtb_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid_Bid_Creative_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ifeng_rtb_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ifeng_rtb_proto_goTypes,
		DependencyIndexes: file_ifeng_rtb_proto_depIdxs,
		MessageInfos:      file_ifeng_rtb_proto_msgTypes,
	}.Build()
	File_ifeng_rtb_proto = out.File
	file_ifeng_rtb_proto_rawDesc = nil
	file_ifeng_rtb_proto_goTypes = nil
	file_ifeng_rtb_proto_depIdxs = nil
}
