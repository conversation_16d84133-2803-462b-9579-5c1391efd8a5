syntax = "proto3";

option go_package = "mh_proxy/pb/ifeng";

package ifeng_rtb;

message BidRequest {
    // 竞价请求唯一识别符
    string id = 1;

    // 竞价类型。1 = First Price, 2 = Second Price Plus. 当前固定为2。也就是采用第二名出价价格作为成交价。
    int32 at = 2;

    //平台类型。1、PC，2、WAP，3、APP
    int32 platform = 3;

    // 广告位对象广告曝光请求对象组。在一个竞价请求中可包含多个广告曝光请求。一次有效的竞价请求至少有一个广告曝光请求。
    message Imp {
        //竞价请求中广告曝光请求的唯一识别符
        string id = 1;
        //广告类型。1、原生；2视频贴片
        int32 adtype = 2;

        //原生广告请求对象
        message Native {
            //图片数组对象
            message Image {
                //素材宽度
                int32 w = 1;

                //素材高度
                int32 h = 2;
            }
            repeated Image image = 1;

            //广告形式
            string type = 2;

            //广告位宽度（pixels）
            int32 w = 3;

            //广告位高度（pixels）
            int32 h = 4;

            //图标/Logo对象
            message Icon {
                //图标宽度
                int32 w = 1;

                //图标高度
                int32 h = 2;
            }
            Icon icon = 5;

            //标题文字长度
            int32 titlelen = 6;

            //描述文字长度
            int32 textlen = 7;

            //1、下载；2、唤醒（deeplink）
            repeated int32 support = 8;
        }
        repeated Native native = 3;

        //视频贴片请求对象
        message Video {
            //支持的文件类型，固定为：video/mp4
            repeated string mimes = 1;

            //视频素材宽度（pixels）
            int32 w = 2;

            //视频素材高度（pixels）
            int32 h = 3;

            //最大播放时长，单位秒
            int32 maxduration = 4;

            //最小播放时长，单位秒
            int32 minduration = 5;

            //时长的误差值，单位为毫秒。
            int32 diffduration = 6;
        }
        Video video = 4;

        //特定广告位置或标签的识别符。当前版本设置为凤飞系统中的广告位id。
        string tagid = 5;

        //标明响应是否需要是https。0：http；1：https。
        int32 secure = 6;

        //竞拍底价。CPM记，单位为人民币分。
        int64 bidfloor = 7;

        //该广告位请求对应私有交易对象
        message Pmp {
            message Deal {
                //交易id
                string id = 1;

                //约定交易价格
                int64 price = 2;
            }
            Deal deals = 1;
            int32 withrtb = 2;
        }
        Pmp pmp = 8;

    }
    repeated Imp imp = 4;

    //App信息
    message App {
        //凤飞系统app唯一标识符
        string id = 1;

        //app名称
        string name = 2;

        //app本次请求对应包名
        string bundle = 3;

        //app版本号
        string ver = 4;

        //app 类型
        string type = 5;
    }
    App app = 5;

    // 设备信息
    message Device {
        // 浏览器user agent
        string ua = 1;

        // IPv4地址
        string ip = 2;

        // 设备IMEI号,MD5加密
        string didmd5 = 3;

        // iOS终端设备的identifier for advertising明文，保持字母大写。
        string ifa = 4;

        // Android终端设备的AndroidID，MD5加密
        string dpidmd5 = 5;

        // 终端网卡MAC地址，MD5加密
        string macmd5 = 6;

        // 网络连接类型。0.未知;1.WIFI;2.2G;3.3G;4.4G;5.5G
        int32 connectiontype = 7;

        // 运营商。0.未知;1.中国移动;2.中国联通;3.中国电信;
        int32 carrier = 8;

        // 设备制造商(例如：Apple)
        string make = 9;

        // 设备型号(例如：iPhone)
        string model = 10;

        // 设备操作系统(例如：iOS)
        string os = 11;

        // 操作系统版本(例如：4.2.1)
        string osv = 12;

        // 设备屏幕像素高
        int32 h = 13;

        // 设备屏幕像素宽
        int32 w = 14;

        // 设备像素密度
        int32 ppi = 15;

        message Geo {
            // 经度，取值范围[-180.0 , +180.0]
            float lon = 1;

            // 纬度，取值范围[-90.0 , +90.0]
            float lat = 2;
        }
        Geo geo = 16;

        string brand = 17;

        string did = 18;

        string oaid = 19;

        //系统更新标识，原值传输，取值参见附录一， iOS：1581141691.570419583 Android： 1004697.70999999
        string update_mark = 20;

        //系统启动标识，原值传输，取值参见附录一， iOS：1623815045.970028， Android： ec7f4f33-411a-47bc-8067-744a4e7e072
        string boot_mark = 21;

        // 设备类型：0.未知;1.pc;2.phone;3.pad;4.tv
        int32 devicetype = 22;
    }
    Device device = 6;

    // 用户对象
    message User {
        // 应用用户ID
        string id = 1;
    }
    User user = 7;

    //是否测试模式，0 = 线上， 1 = 测试模式
    int32 test = 8;

    //禁投广告主行业类型
    repeated string bindus = 9;

    //只投广告主行业类型
    repeated string deliverTrade = 10;

    //禁投广告词
    repeated string forbidenwords = 11;

    //只投广告词
    repeated string onlywords = 12;

}

message BidResponse {
    // 竞价响应识别符，如不等同于竞价请求，拒绝该竞价响应（对应bidreqeust中的id）
    string id = 1;

    //竞价席位对象数组。
    message SeatBid {
        //出价对象数组。每个出价对象关联竞价请求中的一个广告曝光请求对象
        message Bid {
            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string id = 1;

            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string impid = 2;

            //竞价对象识别符。由竞价方选择用以追踪监测及排除故障。
            string dealid = 3;

            //广告出价。CPM记，单位为人民币分。非私有交易必须返回
            int64 price = 4;

            //广告类型。1、原生；2视频贴片
            int32 adtype = 5;

            //广告创意
            message Creative {
                //广告创意ID
                string id = 1;

                //图片素材地址数组
                repeated string images = 2;

                //创意落地页地址
                string landpage = 3;

                //下载型创意直接下载地址,为防止出现问题，先标记弃用，将来可能会删除
                //新的功能会移到app对象中
                string dlurl = 4 [deprecated = true];

                //Deep link地址
                string dplurl = 5;

                //图标/Logo地址或Base64编码（用http开头区分）
                string icon = 6;

                //创意标题，超出Request要求会被截断
                string title = 7;

                //创意标题，超出Request要求会被截断
                string text = 8;

                //创意补充，简介
                string desc = 9;

                //原生广告返回素材对应广告形式
                string type = 10;

                App app = 11;

                message App {
                    //app中文名称
                    string name = 1;

                    //直接下载地址，目的是想要替换掉Creative里的dlurl
                    string dlurl = 2;

                    //android用，包名
                    string package = 3;

                    //ios用，APP_ID
                    string appid = 4;

                    //开始下载监测
                    repeated string dsurls=7;

                    // 下载完成监测
                    repeated string dcurls = 8;

                    //开始安装监测
                    repeated string installstarturls=9;

                    //安装完成监测
                    repeated string installedurls=10;
                }

                // 下载类型0.普通下载(默认)；1.广点通下载
                int32 daction = 12;

                //Deep link唤起成功监测数组
                repeated string dpsurls = 13;

                //复制码
                string copycode=14;
            }
            Creative creative = 6;

            //视频贴片vast代码
            string vast = 7;

            //曝光监测地址数组（异步发送，支持宏替换）
            repeated string aimps = 8;

            //点击监测地址数组（异步发送，支持宏替换）
            repeated string aclks = 9;

            //胜出通知。（由Server端发送，支持宏替换）
            string nurl = 10;
        }
        repeated Bid bid = 1;

        //代表出价的竞价方席位识别符，由凤飞系统生成并提供
        string seat = 2;
    }
    repeated SeatBid seatbid = 2;

    //竞价响应id，用于辅助竞价方追踪监测
    string bidid = 3;
}