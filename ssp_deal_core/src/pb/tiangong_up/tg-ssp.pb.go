// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: tg-ssp.proto

package tiangong_up

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IdentifierType int32

const (
	IdentifierType_OTHER    IdentifierType = 99 // 未知
	IdentifierType_IMEI     IdentifierType = 0  // 安卓 imei
	IdentifierType_OAID     IdentifierType = 1  // 安卓 oaid
	IdentifierType_IDFA     IdentifierType = 2  // ios idfa
	IdentifierType_MAC      IdentifierType = 3  // 设备 mac
	IdentifierType_CAID     IdentifierType = 4  // 设备 caid
	IdentifierType_OAID_MD5 IdentifierType = 5  // 安卓 oaid MD5
	IdentifierType_CAID_MD5 IdentifierType = 6  // 设备caid MD5
)

// Enum value maps for IdentifierType.
var (
	IdentifierType_name = map[int32]string{
		99: "OTHER",
		0:  "IMEI",
		1:  "OAID",
		2:  "IDFA",
		3:  "MAC",
		4:  "CAID",
		5:  "OAID_MD5",
		6:  "CAID_MD5",
	}
	IdentifierType_value = map[string]int32{
		"OTHER":    99,
		"IMEI":     0,
		"OAID":     1,
		"IDFA":     2,
		"MAC":      3,
		"CAID":     4,
		"OAID_MD5": 5,
		"CAID_MD5": 6,
	}
)

func (x IdentifierType) Enum() *IdentifierType {
	p := new(IdentifierType)
	*p = x
	return p
}

func (x IdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_tg_ssp_proto_enumTypes[0].Descriptor()
}

func (IdentifierType) Type() protoreflect.EnumType {
	return &file_tg_ssp_proto_enumTypes[0]
}

func (x IdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *IdentifierType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = IdentifierType(num)
	return nil
}

// Deprecated: Use IdentifierType.Descriptor instead.
func (IdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source *Request_Source `protobuf:"bytes,1,opt,name=source" json:"source,omitempty"`
	Device *Request_Device `protobuf:"bytes,2,opt,name=device" json:"device,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetSource() *Request_Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Request) GetDevice() *Request_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//对应Request中的impressionId
	ImpressionId *string `protobuf:"bytes,1,opt,name=impressionId" json:"impressionId,omitempty"`
	// 广告应答状态码，返回码：0 表示成功，非 0 表示失败,无广告返回
	Status *int32 `protobuf:"varint,2,opt,name=status" json:"status,omitempty"`
	// 若有错误，此字段为详细的错误信息
	Msg *string      `protobuf:"bytes,3,opt,name=msg" json:"msg,omitempty"`
	Ad  *Response_Ad `protobuf:"bytes,4,opt,name=ad" json:"ad,omitempty"`
	// 是否需要媒体额外填充素材：true 需要填充；false 不需要填充，直接播当前返回素材
	NeedFill  *bool                 `protobuf:"varint,5,opt,name=needFill" json:"needFill,omitempty"`
	UserScore []*Response_UserScore `protobuf:"bytes,6,rep,name=userScore" json:"userScore,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetImpressionId() string {
	if x != nil && x.ImpressionId != nil {
		return *x.ImpressionId
	}
	return ""
}

func (x *Response) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *Response) GetMsg() string {
	if x != nil && x.Msg != nil {
		return *x.Msg
	}
	return ""
}

func (x *Response) GetAd() *Response_Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

func (x *Response) GetNeedFill() bool {
	if x != nil && x.NeedFill != nil {
		return *x.NeedFill
	}
	return false
}

func (x *Response) GetUserScore() []*Response_UserScore {
	if x != nil {
		return x.UserScore
	}
	return nil
}

// 资源位信息
type Request_Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 天宫广告位id
	SourceId *string `protobuf:"bytes,1,opt,name=sourceId" json:"sourceId,omitempty"`
	// 媒体资源位id
	ImpressionId *string `protobuf:"bytes,2,opt,name=impressionId" json:"impressionId,omitempty"`
	// 媒体资源位的宽和高
	Width  *int32 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	Height *int32 `protobuf:"varint,4,opt,name=height" json:"height,omitempty"`
	// 是否为多张素材资源位
	IsMulti *bool `protobuf:"varint,5,opt,name=isMulti" json:"isMulti,omitempty"`
	// 是否为全屏
	IsFullscreen *bool `protobuf:"varint,6,opt,name=isFullscreen" json:"isFullscreen,omitempty"`
	// 投放日期：为空表示请求当前日期广告，非空表示预加载指定日期广告
	DeliveryDate *string `protobuf:"bytes,7,opt,name=deliveryDate" json:"deliveryDate,omitempty"`
}

func (x *Request_Source) Reset() {
	*x = Request_Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Source) ProtoMessage() {}

func (x *Request_Source) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Source.ProtoReflect.Descriptor instead.
func (*Request_Source) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Request_Source) GetSourceId() string {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return ""
}

func (x *Request_Source) GetImpressionId() string {
	if x != nil && x.ImpressionId != nil {
		return *x.ImpressionId
	}
	return ""
}

func (x *Request_Source) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Request_Source) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Request_Source) GetIsMulti() bool {
	if x != nil && x.IsMulti != nil {
		return *x.IsMulti
	}
	return false
}

func (x *Request_Source) GetIsFullscreen() bool {
	if x != nil && x.IsFullscreen != nil {
		return *x.IsFullscreen
	}
	return false
}

func (x *Request_Source) GetDeliveryDate() string {
	if x != nil && x.DeliveryDate != nil {
		return *x.DeliveryDate
	}
	return ""
}

// 设备信息
type Request_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ipv4 点分十进制, 必须为终端真实IP地址
	Ip *string `protobuf:"bytes,1,opt,name=ip" json:"ip,omitempty"`
	// IOS6.0及以上的idfa号;安卓设备的imei号;OTT设备的mac地址【已废弃】
	Identifier *string `protobuf:"bytes,2,opt,name=identifier" json:"identifier,omitempty"`
	// IOS设备，原始idfa的md5值;安卓设备，原始imei的md5值;OTT设备，原始 mac 地址去冒号转的 md5 值
	DeiviceId *string `protobuf:"bytes,3,opt,name=deiviceId" json:"deiviceId,omitempty"`
	// android_id
	AndroidId *string `protobuf:"bytes,4,opt,name=android_id,json=androidId" json:"android_id,omitempty"`
	// 设备类型，0-手机;1-平板;2-PC;3-互联网电视
	DeviceType *int32 `protobuf:"varint,5,opt,name=device_type,json=deviceType" json:"device_type,omitempty"`
	// 设备品牌
	// 例如：nokia, samsung
	Brand *string `protobuf:"bytes,6,opt,name=brand" json:"brand,omitempty"`
	// 设备型号
	// 例如：n70, galaxy
	Model *string `protobuf:"bytes,7,opt,name=model" json:"model,omitempty"`
	// 操作系统
	// 例如：Android,iOS
	Os *string `protobuf:"bytes,8,opt,name=os" json:"os,omitempty"`
	// 操作系统版本
	// 例如：7.0.2
	Osv *string `protobuf:"bytes,9,opt,name=osv" json:"osv,omitempty"`
	// 设备所处网络环境 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g [ default = 1 ]
	Network *int32 `protobuf:"varint,10,opt,name=network" json:"network,omitempty"`
	// 设备的网络运营商 0-未知, 1-移动, 2-联通, 3-电信
	Operator *int32 `protobuf:"varint,11,opt,name=operator" json:"operator,omitempty"`
	// 设备屏幕尺寸：宽
	Width *int32 `protobuf:"varint,12,opt,name=width" json:"width,omitempty"`
	// 设备屏幕尺寸：高
	Height *int32 `protobuf:"varint,13,opt,name=height" json:"height,omitempty"`
	// 设备密度，对应于pixel_ratio [default=1000]
	PixelRatio *int32 `protobuf:"varint,14,opt,name=pixel_ratio,json=pixelRatio" json:"pixel_ratio,omitempty"`
	// 屏幕方向 0-未知, 1-竖屏, 2-横屏
	Orientation *int32 `protobuf:"varint,15,opt,name=orientation" json:"orientation,omitempty"`
	// 用户所处时区的分钟偏移量[ default = 480 ]
	// 例如：如果是东八区，则 timezone_offset = 60 * 8 = 480.
	TimezoneOffset *int32              `protobuf:"varint,16,opt,name=timezone_offset,json=timezoneOffset" json:"timezone_offset,omitempty"`
	Geo            *Request_Device_Geo `protobuf:"bytes,17,opt,name=geo" json:"geo,omitempty"`
	// 设备号类型
	IdentifierType *IdentifierType `protobuf:"varint,18,opt,name=identifierType,enum=IdentifierType" json:"identifierType,omitempty"`
	// oaid 【已废弃】
	Oaid *string `protobuf:"bytes,19,opt,name=oaid" json:"oaid,omitempty"`
	// caid
	CaidInfo []*Request_Device_CaidInfo `protobuf:"bytes,20,rep,name=caid_info,json=caidInfo" json:"caid_info,omitempty"`
}

func (x *Request_Device) Reset() {
	*x = Request_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device) ProtoMessage() {}

func (x *Request_Device) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device.ProtoReflect.Descriptor instead.
func (*Request_Device) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Request_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *Request_Device) GetIdentifier() string {
	if x != nil && x.Identifier != nil {
		return *x.Identifier
	}
	return ""
}

func (x *Request_Device) GetDeiviceId() string {
	if x != nil && x.DeiviceId != nil {
		return *x.DeiviceId
	}
	return ""
}

func (x *Request_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *Request_Device) GetDeviceType() int32 {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return 0
}

func (x *Request_Device) GetBrand() string {
	if x != nil && x.Brand != nil {
		return *x.Brand
	}
	return ""
}

func (x *Request_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *Request_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *Request_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *Request_Device) GetNetwork() int32 {
	if x != nil && x.Network != nil {
		return *x.Network
	}
	return 0
}

func (x *Request_Device) GetOperator() int32 {
	if x != nil && x.Operator != nil {
		return *x.Operator
	}
	return 0
}

func (x *Request_Device) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Request_Device) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Request_Device) GetPixelRatio() int32 {
	if x != nil && x.PixelRatio != nil {
		return *x.PixelRatio
	}
	return 0
}

func (x *Request_Device) GetOrientation() int32 {
	if x != nil && x.Orientation != nil {
		return *x.Orientation
	}
	return 0
}

func (x *Request_Device) GetTimezoneOffset() int32 {
	if x != nil && x.TimezoneOffset != nil {
		return *x.TimezoneOffset
	}
	return 0
}

func (x *Request_Device) GetGeo() *Request_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Request_Device) GetIdentifierType() IdentifierType {
	if x != nil && x.IdentifierType != nil {
		return *x.IdentifierType
	}
	return IdentifierType_OTHER
}

func (x *Request_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *Request_Device) GetCaidInfo() []*Request_Device_CaidInfo {
	if x != nil {
		return x.CaidInfo
	}
	return nil
}

type Request_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 纬度, 取值范围[-90.0 , +90.0]
	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"`
	// 经度, 取值范围[-180.0 , +180.0]
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"`
}

func (x *Request_Device_Geo) Reset() {
	*x = Request_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_Geo) ProtoMessage() {}

func (x *Request_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_Geo.ProtoReflect.Descriptor instead.
func (*Request_Device_Geo) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *Request_Device_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *Request_Device_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

type Request_Device_CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// caid版本
	Version *string `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	// caid值
	Caid *string `protobuf:"bytes,2,opt,name=caid" json:"caid,omitempty"`
	// caid md5值
	CaidMd5 *string `protobuf:"bytes,3,opt,name=caid_md5,json=caidMd5" json:"caid_md5,omitempty"`
}

func (x *Request_Device_CaidInfo) Reset() {
	*x = Request_Device_CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_CaidInfo) ProtoMessage() {}

func (x *Request_Device_CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_CaidInfo.ProtoReflect.Descriptor instead.
func (*Request_Device_CaidInfo) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{0, 1, 1}
}

func (x *Request_Device_CaidInfo) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *Request_Device_CaidInfo) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

func (x *Request_Device_CaidInfo) GetCaidMd5() string {
	if x != nil && x.CaidMd5 != nil {
		return *x.CaidMd5
	}
	return ""
}

// 广告内容
type Response_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 创意标题
	Title *string `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// 曝光监测地址
	ImpressionUrl *string `protobuf:"bytes,2,opt,name=impressionUrl" json:"impressionUrl,omitempty"`
	// 点击地址，在触发点击时，通过此地址到达落地页
	ClickUrl *string `protobuf:"bytes,3,opt,name=clickUrl" json:"clickUrl,omitempty"`
	// 创意地址域名
	CreativeDomain *string                 `protobuf:"bytes,4,opt,name=creativeDomain" json:"creativeDomain,omitempty"`
	Creative       []*Response_Ad_Creative `protobuf:"bytes,5,rep,name=creative" json:"creative,omitempty"`
	// 三方曝光监测地址
	ThirdImpUrl *string `protobuf:"bytes,6,opt,name=thirdImpUrl" json:"thirdImpUrl,omitempty"`
	// 三方点击监测地址
	ThirdClickUrl *string `protobuf:"bytes,7,opt,name=thirdClickUrl" json:"thirdClickUrl,omitempty"`
	// 京东曝光监测地址
	JdImpUrl *string `protobuf:"bytes,8,opt,name=jdImpUrl" json:"jdImpUrl,omitempty"`
	// 京东点击地址，在触发点击时，通过此地址到达落地页
	JdClickUrl *string `protobuf:"bytes,9,opt,name=jdClickUrl" json:"jdClickUrl,omitempty"`
	// （三方+京东）直呼协议链接
	OpenUrl *string `protobuf:"bytes,10,opt,name=openUrl" json:"openUrl,omitempty"`
	// 京东直呼协议链接
	JdOpenUrl *string `protobuf:"bytes,11,opt,name=jdOpenUrl" json:"jdOpenUrl,omitempty"`
	// 创意副标题
	SubTitle *string `protobuf:"bytes,12,opt,name=subTitle" json:"subTitle,omitempty"`
	// 广告主名称
	Source *string `protobuf:"bytes,13,opt,name=source" json:"source,omitempty"`
	// 广告主头像
	SourceAvatar *string `protobuf:"bytes,14,opt,name=sourceAvatar" json:"sourceAvatar,omitempty"`
	// 创意id
	UniqId *int32 `protobuf:"varint,15,opt,name=uniqId" json:"uniqId,omitempty"`
	// 扩展点击监测地址集合
	ExtendClickUrls []string `protobuf:"bytes,16,rep,name=extendClickUrls" json:"extendClickUrls,omitempty"`
	// （三方+京东）ULink链接
	ULinkUrl *string `protobuf:"bytes,17,opt,name=uLinkUrl" json:"uLinkUrl,omitempty"`
	// 京东ULink链接
	JdULinkUrl *string `protobuf:"bytes,18,opt,name=jdULinkUrl" json:"jdULinkUrl,omitempty"`
}

func (x *Response_Ad) Reset() {
	*x = Response_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Ad) ProtoMessage() {}

func (x *Response_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Ad.ProtoReflect.Descriptor instead.
func (*Response_Ad) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Response_Ad) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Response_Ad) GetImpressionUrl() string {
	if x != nil && x.ImpressionUrl != nil {
		return *x.ImpressionUrl
	}
	return ""
}

func (x *Response_Ad) GetClickUrl() string {
	if x != nil && x.ClickUrl != nil {
		return *x.ClickUrl
	}
	return ""
}

func (x *Response_Ad) GetCreativeDomain() string {
	if x != nil && x.CreativeDomain != nil {
		return *x.CreativeDomain
	}
	return ""
}

func (x *Response_Ad) GetCreative() []*Response_Ad_Creative {
	if x != nil {
		return x.Creative
	}
	return nil
}

func (x *Response_Ad) GetThirdImpUrl() string {
	if x != nil && x.ThirdImpUrl != nil {
		return *x.ThirdImpUrl
	}
	return ""
}

func (x *Response_Ad) GetThirdClickUrl() string {
	if x != nil && x.ThirdClickUrl != nil {
		return *x.ThirdClickUrl
	}
	return ""
}

func (x *Response_Ad) GetJdImpUrl() string {
	if x != nil && x.JdImpUrl != nil {
		return *x.JdImpUrl
	}
	return ""
}

func (x *Response_Ad) GetJdClickUrl() string {
	if x != nil && x.JdClickUrl != nil {
		return *x.JdClickUrl
	}
	return ""
}

func (x *Response_Ad) GetOpenUrl() string {
	if x != nil && x.OpenUrl != nil {
		return *x.OpenUrl
	}
	return ""
}

func (x *Response_Ad) GetJdOpenUrl() string {
	if x != nil && x.JdOpenUrl != nil {
		return *x.JdOpenUrl
	}
	return ""
}

func (x *Response_Ad) GetSubTitle() string {
	if x != nil && x.SubTitle != nil {
		return *x.SubTitle
	}
	return ""
}

func (x *Response_Ad) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *Response_Ad) GetSourceAvatar() string {
	if x != nil && x.SourceAvatar != nil {
		return *x.SourceAvatar
	}
	return ""
}

func (x *Response_Ad) GetUniqId() int32 {
	if x != nil && x.UniqId != nil {
		return *x.UniqId
	}
	return 0
}

func (x *Response_Ad) GetExtendClickUrls() []string {
	if x != nil {
		return x.ExtendClickUrls
	}
	return nil
}

func (x *Response_Ad) GetULinkUrl() string {
	if x != nil && x.ULinkUrl != nil {
		return *x.ULinkUrl
	}
	return ""
}

func (x *Response_Ad) GetJdULinkUrl() string {
	if x != nil && x.JdULinkUrl != nil {
		return *x.JdULinkUrl
	}
	return ""
}

// 用户分值
type Response_UserScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 场景
	Scene *string `protobuf:"bytes,1,opt,name=scene" json:"scene,omitempty"`
	// 分值，分值越高用户优先级越高，分值范围为：[0,10]
	Score *float64 `protobuf:"fixed64,2,opt,name=score" json:"score,omitempty"`
}

func (x *Response_UserScore) Reset() {
	*x = Response_UserScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_UserScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_UserScore) ProtoMessage() {}

func (x *Response_UserScore) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_UserScore.ProtoReflect.Descriptor instead.
func (*Response_UserScore) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Response_UserScore) GetScene() string {
	if x != nil && x.Scene != nil {
		return *x.Scene
	}
	return ""
}

func (x *Response_UserScore) GetScore() float64 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

type Response_Ad_Creative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 创意序号，从0开始
	Id *int32 `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	// 创意地址，与creativeDomain组装形成完整创意地址
	CreativeUrl *string `protobuf:"bytes,2,opt,name=creativeUrl" json:"creativeUrl,omitempty"`
	// 创意内容md5
	Md5 *string `protobuf:"bytes,3,opt,name=md5" json:"md5,omitempty"`
}

func (x *Response_Ad_Creative) Reset() {
	*x = Response_Ad_Creative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tg_ssp_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response_Ad_Creative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response_Ad_Creative) ProtoMessage() {}

func (x *Response_Ad_Creative) ProtoReflect() protoreflect.Message {
	mi := &file_tg_ssp_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response_Ad_Creative.ProtoReflect.Descriptor instead.
func (*Response_Ad_Creative) Descriptor() ([]byte, []int) {
	return file_tg_ssp_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *Response_Ad_Creative) GetId() int32 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Response_Ad_Creative) GetCreativeUrl() string {
	if x != nil && x.CreativeUrl != nil {
		return *x.CreativeUrl
	}
	return ""
}

func (x *Response_Ad_Creative) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

var File_tg_ssp_proto protoreflect.FileDescriptor

var file_tg_ssp_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x74, 0x67, 0x2d, 0x73, 0x73, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98,
	0x08, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x1a, 0xd8, 0x01, 0x0a,
	0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x46, 0x75, 0x6c, 0x6c, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x1a, 0xdf, 0x05, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x69, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x69, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6f, 0x73, 0x76, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e,
	0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x25,
	0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f,
	0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x37, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x12, 0x35, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x63, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x29, 0x0a, 0x03, 0x47, 0x65, 0x6f,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c,
	0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6c, 0x6f, 0x6e, 0x1a, 0x53, 0x0a, 0x08, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x22, 0x9a, 0x07, 0x0a, 0x08, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x1c, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x02,
	0x61, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x6c, 0x12, 0x31,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x1a, 0x99, 0x05, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x12, 0x26, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x49, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x68, 0x69, 0x72, 0x64, 0x49, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a,
	0x0d, 0x74, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6a, 0x64, 0x49, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x64, 0x49, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12,
	0x1e, 0x0a, 0x0a, 0x6a, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6a, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x64, 0x4f,
	0x70, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x64,
	0x4f, 0x70, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x6e, 0x69, 0x71, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x75, 0x6e, 0x69, 0x71, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a,
	0x0a, 0x6a, 0x64, 0x55, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6a, 0x64, 0x55, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x1a, 0x4e, 0x0a,
	0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x1a, 0x37, 0x0a,
	0x09, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x2a, 0x68, 0x0a, 0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x10, 0x63, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4d, 0x45, 0x49, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x4f, 0x41, 0x49, 0x44, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x44, 0x46, 0x41, 0x10,
	0x02, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x41, 0x43, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41,
	0x49, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x41, 0x49, 0x44, 0x5f, 0x4d, 0x44, 0x35,
	0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x49, 0x44, 0x5f, 0x4d, 0x44, 0x35, 0x10, 0x06,
	0x42, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x2e, 0x6a, 0x64, 0x2e, 0x74, 0x67, 0x2e, 0x72, 0x6d,
	0x70, 0x2e, 0x70, 0x75, 0x62, 0x73, 0x5a, 0x17, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x2f, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x61, 0x6e, 0x67, 0x6f, 0x6e, 0x67, 0x5f, 0x75, 0x70,
}

var (
	file_tg_ssp_proto_rawDescOnce sync.Once
	file_tg_ssp_proto_rawDescData = file_tg_ssp_proto_rawDesc
)

func file_tg_ssp_proto_rawDescGZIP() []byte {
	file_tg_ssp_proto_rawDescOnce.Do(func() {
		file_tg_ssp_proto_rawDescData = protoimpl.X.CompressGZIP(file_tg_ssp_proto_rawDescData)
	})
	return file_tg_ssp_proto_rawDescData
}

var file_tg_ssp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tg_ssp_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_tg_ssp_proto_goTypes = []interface{}{
	(IdentifierType)(0),             // 0: IdentifierType
	(*Request)(nil),                 // 1: Request
	(*Response)(nil),                // 2: Response
	(*Request_Source)(nil),          // 3: Request.Source
	(*Request_Device)(nil),          // 4: Request.Device
	(*Request_Device_Geo)(nil),      // 5: Request.Device.Geo
	(*Request_Device_CaidInfo)(nil), // 6: Request.Device.CaidInfo
	(*Response_Ad)(nil),             // 7: Response.Ad
	(*Response_UserScore)(nil),      // 8: Response.UserScore
	(*Response_Ad_Creative)(nil),    // 9: Response.Ad.Creative
}
var file_tg_ssp_proto_depIdxs = []int32{
	3, // 0: Request.source:type_name -> Request.Source
	4, // 1: Request.device:type_name -> Request.Device
	7, // 2: Response.ad:type_name -> Response.Ad
	8, // 3: Response.userScore:type_name -> Response.UserScore
	5, // 4: Request.Device.geo:type_name -> Request.Device.Geo
	0, // 5: Request.Device.identifierType:type_name -> IdentifierType
	6, // 6: Request.Device.caid_info:type_name -> Request.Device.CaidInfo
	9, // 7: Response.Ad.creative:type_name -> Response.Ad.Creative
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_tg_ssp_proto_init() }
func file_tg_ssp_proto_init() {
	if File_tg_ssp_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tg_ssp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_UserScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tg_ssp_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response_Ad_Creative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tg_ssp_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tg_ssp_proto_goTypes,
		DependencyIndexes: file_tg_ssp_proto_depIdxs,
		EnumInfos:         file_tg_ssp_proto_enumTypes,
		MessageInfos:      file_tg_ssp_proto_msgTypes,
	}.Build()
	File_tg_ssp_proto = out.File
	file_tg_ssp_proto_rawDesc = nil
	file_tg_ssp_proto_goTypes = nil
	file_tg_ssp_proto_depIdxs = nil
}
