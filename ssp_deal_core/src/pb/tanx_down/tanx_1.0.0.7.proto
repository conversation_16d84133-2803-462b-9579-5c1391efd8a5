syntax = "proto2";

option go_package = "mh_proxy/pb/tanx_down";

package tanx_down;

option cc_generic_services = true;

enum PricingType {
    PRICING_CPM = 1;
    PRICING_CPC = 2;
}

message BidRequest {
    // 当前协议版本号，目前为1
    required int32 version = 1;

    // 本请求唯一ID，32字节的字符串
    required string id = 2;

    repeated Imp imps = 3;

    optional Site site = 4;

    optional App app = 5;

    optional Content content = 6;

    optional Device device = 7;

    optional User user = 8;

    // 如果为true，那么这是一个测试请求，DSP需要返回一个正常填写的应答
    // Tanx不会展现给用户，且不会对该次请求计费
    optional bool is_test = 9 [ default = false ];

    // 如果为true，那么这是一个ping请求，DSP需要返回一个包含请求ID的空的应答
    optional bool is_ping = 10 [ default = false ];

    // 如果为true，那么这是一个预览请求，DSP需要返回一个正常填写的应答
    optional bool is_preview = 11 [ default = false ];

    // 流量所属来源:adx type 0表示SSP流量, 3-TanxSDK联盟流量
    optional int32 adx_type = 12 [default = 0];

    // 是否必须返回https广告
    optional bool https_required = 13 [default = false];

    // 附加信息
    optional string extend_info = 14;

    // 阿里内部数据
    optional PrivateInfo private_info = 15;

    // 为DSP预留的最大竞价时间，包括网络传输时间，单位:ms
    optional int32 max_bid_time = 16;

    // 媒体实验ID
    repeated string media_exp_ids = 17;

    message Imp {
        // 标识此曝光机会的唯一ID
        required string imp_id = 1;

        // 推广位的唯一标识
        required string pid = 2;

        // 兼容原YES系统的tagid, 即将废弃不再维护, 请使用pid
        optional string tag_id = 3;

        // 媒体的唯一标识, 和创意审核状态查询api配合使用
        optional string publisher_id = 4;

        // 资源类型, 如：开屏、信息流等，参见字典
        optional int32 resource_type = 5;

        // 信息流广告刷数
        optional int32 refresh_num = 6;

        // 推广位屏数
        optional int32 screen_num = 7;

        // 推广位位置, 如信息流广告的第几个坑位
        optional int32 position = 8;

        // 推广位尺寸
        optional int32 width = 9;
        optional int32 height = 10;

        // 视频资源位信息
        message Video {
            // 贴片位置相对于所在视频的起始时间，0 表示前贴片, -1 表示后贴片，大于0的值表示中插
            optional int32 videoad_start_delay = 1;

            // 上述位置可能包含多个贴片，表示本贴片在上述集合中相对起始位置，单位:毫秒
            optional int32 videoad_section_start_delay = 2;

            // 贴片最小播放时间长度,视频创意播放时间不可小于该值，单位:毫秒
            optional int32 min_ad_duration = 3;

            // 贴片最大播放时间长度，单位:毫秒
            optional int32 max_ad_duration = 4;
        }
        optional Video video = 11;

        // 推广位希望从单个DSP获取的竞价广告数量，DSP可以提供小于等于此值的广告个数
        // 多于max_ad_num数值的广告，将被截断而不参与竞价
        optional int32 max_ad_num = 12 [default = 1];

        // 最低竞标价格，货币单位为人民币，数值含义为分/千次展现
        optional int32 min_cpm_price = 13;

        // 最低竞标价格，货币单位为人民币，数值含义为分/点击
        optional int32 min_cpc_price = 14;

        // 是否对先后发生的多次展现重复计费
        optional bool impression_repeatable = 15 [default = false];

        // 本次请求可用的模板列表，参见文档 native-template.pdf
        message Template {
            // 模板ID
            required uint64 id = 1;

            // 必填字段
            repeated string required_fields = 2;

            // 可选字段
            repeated string optional_fields = 3;
        }
        // 本次请求可用的模板列表，参见文档 native-template.pdf
        repeated Template templates = 16;

        // PMP Deal信息
        message Deal {
            // deal id
            required uint64 deal_id = 1;

            enum DealType {
                // PA
                PRIVATE_AUCTION = 1;
                // PD
                PREFERRED_DEAL = 2;
                // PDB
                DIRECT_BUY = 3;
            }
            // deal类型
            required DealType deal_type = 2;

            // 允许的广告主白名单，不设置则默认所有均允许
            repeated uint64 adv_ids = 3;

            // 交易价格/底价, 单位：分/CPM
            optional int32 price = 4;

            // 是否对先后发生的多次展现重复计费
            optional bool impression_repeatable = 5 [default = false];
        }
        // PMP Deal信息列表
        repeated Deal deals = 17;

        // 预投放日期, 跨天预加载广告使用，格式:"20220910"
        optional string campaign_date = 18;

        // 推广位可选的广告打开方式,默认支持Click(含H5), 其他APP打开方式为：
        // 1-APP内打开H5落地页, 3-deeplink, 4-Universal Link(IOS), 5-直播间打开, 6-半屏落地页(优酷聚焦贴片/快手落地页前置)
        repeated int32 open_types = 19;

        // 竞价类型，空默认为CPM
        repeated PricingType pricing_type = 20;

        // 媒体创意优选列表
        message ProposedCreative {
            optional string crid = 1;
        }
        repeated ProposedCreative proposed_creatives = 21;

        // 商品信息
        message ItemInfo {
            repeated string item_id = 1;   // 商品id
            optional string seller_id = 2; // 卖家id
        }
        repeated ItemInfo item_infos = 22;

        // 屏蔽的广告属性
        message ExcludedInfo {
            // 媒体要求的过滤规则id，多值
            repeated string publisher_filter_ids = 1;

            // 禁止投放的创意ID
            repeated string creative_ids = 2;

            // 媒体禁止的目标跳转url, 所有条目的总长度不超过200个字符
            // 该字段可能不是全量数据，建议使用离线数据获取媒体设置的全量数据
            repeated string excluded_click_through_urls = 3;

            // 禁止投放的广告主ID
            repeated uint64 adv_ids = 4;

            // 禁止投放的广告类目ID
            repeated uint64 ad_category = 5;

            // 禁止投放的主播ID
            repeated uint64 streamer_ids = 6;
        }
        optional ExcludedInfo excluded_info = 23;
        // 支持的下载类型: 1.链接下载 2.应用商店下载
        repeated int32 download_types = 24;
    }

    message Site {
        // 推广位所在的网站名称
        optional string name = 1;

        // 推广位所在的页面url
        optional string page = 2;

        // 推广位所在的页面refer url
        optional string ref = 3;

        // 推广位所在网站的分类
        // 参见数据字典 Tanx-dict-site-category.txt
        repeated int32 category = 4;
    }

    message App {
        // 应用名，例如：淘宝, UTF-8编码
        optional string name = 1;

        // 应用包名, 例如：com.moji.MojiWeather
        optional string package_name = 2;

        // app版本号
        optional string version = 3;

        // app类目
        repeated int32 category = 4;
    }

    // 内容上下文
    message Content {
        // 标题, UTF-8编码
        optional string title = 1;

        // 视频时长，单位:秒
        optional int32 duration = 2;

        // 关键词
        repeated string keywords = 3;

        // 用户的搜索关键词
        optional string query_term = 4;

        // 输入框提示词
        repeated string query_suggestion = 5;

        // 视频节目或剧集ID
        optional string program_id = 6;

        // 视频ID, 原为id，改为video_id
        optional string video_id = 7;

        // 内容生产者账号
        repeated string producer_id = 8;

        // 内容类目, 兼容优酷内容类目【二级类目】
        repeated int32 category = 9;
    }

    message Device {
        // 用户的IP地址，IPv4或IPv6, 如：***********
        optional string ip = 1;

        // 用户的浏览器类型，即HTTP请求头部的User-Agent
        optional string user_agent = 2;
        // 设备类型：0—手机，1—平板，2—PC，3—互联网电视
        optional int32 device_type = 3;

        // 操作系统 0-未识别；1-PC(Windows)；2-iOS；3-Android；4-Windows Phone；
        // 5-Mac；6-Linux；7-Yun；8-Tizen；9-Cycle
        optional int32 os = 4;

        // 操作系统版本号 (e.g., "3.1.2").
        optional string osv = 5;

        // 制造商(e.g., "Apple").
        optional string make = 6;

        // 设备型号(e.g., "iPhone").
        optional string model = 7;

        // 运营商, 0-未识别；1-中国移动；2-中国联通：3-中国电信；4-其他；
        optional int32 carrier = 8;

        // 设备所处网络环境, 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g, 5-5g
        optional int32 connection_type = 9;

        // 屏幕宽高
        optional int32 screen_width = 10;
        optional int32 screen_height = 11;

        // 地理位置信息
        message Geo {
            // 经度（小数点格式）例如：116.41667
            optional string lon = 1;

            // 纬度（小数点格式）例如：39.91667
            optional string lat = 2;

            // 地域码-国家码
            optional int32 country_code = 3;

            // 地域码-省份码
            optional int32 province_code = 4;

            // 地域码-城市码
            optional int32 city_code = 5;

            // 地域码-区县码
            optional int32 district_code = 6;
        }
        optional Geo geo = 12;

        // 用户已安装的app列表,1-手淘,2-支付宝,3-闲鱼,4-饿了么
        repeated int32 installed_app_ids = 13;

        // 媒体端的唯一设备id
        optional string media_device_id = 14;

        optional string idfa = 15;

        optional string oaid = 16;

        optional string android_id = 17;

        // 支持盒子传过来yun os相关的UUID
        optional string uuid = 18;

        optional string mac = 19;

        // 该值为imei md5值
        optional string imei_md5 = 20;

        // 该值为idfa md5值
        optional string idfa_md5 = 21;

        // 该值为oaid md5值
        optional string oaid_md5 = 22;

        // 该值为mac  md5值
        optional string mac_md5 = 23;

        // 广协CAID
        message CAID {
            // 广协版本号，如"20201201"
            required string ver = 1;
            required string caid = 2;
        }
        repeated CAID caids = 24;
        // ios上设备标识
        optional string open_udid = 25;
    }

    // 人群信息
    message User {
        // 年龄
        optional int32 age = 1;

        // 性别：0-未知、1-男性、2-女性
        optional int32 gender = 2;

        // 婚姻：0-未婚、1-已婚
        optional int32 marriage = 3;

        // 在校：0-不在校、1-在校学生
        optional int32 school = 4;

        // 标准化的人群标签
        repeated uint64 category = 5;

        // 人群ID
        repeated uint64 dmp_ids = 6;
    }

    // 预留字段，暂无实际值
    message PrivateInfo {
        optional bool is_stress_test = 1;
        optional string trace_info = 2;
        optional string cookie_cna = 3;
        optional string utdid = 4;
        optional string aid = 5;
        // ali aaid，非android的aaid
        optional string ali_aaid = 6;
        // 淘宝id，给k2传递用于减少aid查询量
        optional string taobao_id = 7;
        //utf-8的unicode值
        optional string taobao_nick = 8;
        // 是否立即渲染，主要是开屏使用，避免返回过大素材
        // 名称待定，20230309
        optional bool is_realtime_request = 9 [ default = false ];
        // 客户端类型：a=app,w=web,off=离线应用
        optional string client_type = 10;
        // 广告位设备类型:0-pad,1-phone,2-ott,3-pc,4-ooh,5-不限
        optional int32 pid_device_type = 11;
        // 广告位操作系统类型:0-ios,1-android,5-不限
        optional int32 pid_os_type = 12;
    }
}

message BidResponse {
    // 当前协议版本号，目前为1
    required int32 version = 1;

    // 请填充BidRequest中的id, 32字节的字符串
    required string id = 2;

    repeated SeatBid seat_bids = 3;

    // 不参竞缓存时长，单位：秒
    optional int32 cache_duration = 4 [ default = 0 ];

    // DSP返回的用户信息
    optional UserInfo user_info = 5;

    // 每个SeatBid针对一个Imp参竞
    message SeatBid {
        // 对应Imp中的imp_id, 表示针对此Imp参竞
        required string imp_id = 1;
        // 通用参竞广告列表
        repeated Bid bids = 2;
        // 直投普通广告候补广告列表
        repeated Bid backup_bids = 3;
    }

    message Bid {
        // 必填,广告创意ID, 长度不超过64字节，且不能包含空格/回车/换行和其他不可见字符
        required string creative_id = 1;

        // 创意类型
        // 参见数据字典 Tanx-dict-creative-type.txt
        // 1  文字
        // 2  图片
        // 3  Flash
        // 4  视频
        // 12 直播
        // 13 短视频
        // 14 pop联动
        // 15 底纹词联动
        optional int32 creative_type = 2;

        // MIME 素材类型 TODO:判断此字段是否可删除，由Tanx适配媒体时自动填写
        // optional string mime_type = 3;

        // DSP声明的本广告所属的广告行业类目
        // 参见数据字典 Tanx-dict-ad-category.txt
        repeated uint64 category = 3;

        // 广告代码片段
        optional string html_snippet = 4;

        // 创意模板，包含标题、图片等创意元素
        optional TemplateData template_data = 5;

        // 创意子模板，包含标题、图片等创意元素, 依赖主模板样式，只在复杂创意样式使用
        optional TemplateData sub_template_data = 6;

        // 打开落地页相关
        optional Landing landing = 7;

        // 监测相关
        optional Monitor monitor = 8;

        // 竞价类型
        optional PricingType pricing_type = 9;

        // 广告竞标价格（人民币），单位为分/千次展现
        // 该字段值不允许为0, 且不能低于推广位最低竞标价格（BidRequest.min_cpm_price）
        optional int32 max_cpm_price = 10;

        // 广告竞标价格（人民币），单位为分/点击
        // 该字段值不允许为0, 且不能低于推广位最低竞标价格（BidRequest.min_cpc_price）
        optional int32 max_cpc_price = 11;

        // 参与PMP竞价的id, 要和请求中Deal定义中的dealid一致
        // 如果不设置表示参与公开竞价
        optional uint64 deal_id = 12;

        // 广告主ID
        optional uint64 adv_id = 13;

        // 创意投放日期, 仅开屏使用, 格式:"20160602"
        optional string campaign_date = 14;

        // 出价类型
        enum BidType {
            // 正常出价
            NORMAL = 0;

            // 抄底广告，联动投放
            DEFAULT = 1;

            // 市场广告(新增)
            MARKET = 2;
        }
        optional BidType bid_type = 15;

        // 广告相关的APP信息
        optional AppInfo app_info = 16;

        // 阿里内部业务线信息
        optional BizInfo biz_info = 17;

        // 用户质量分
        repeated UserQuality user_qualities = 18;

        // DSP自定义数据, 只能包含[_0-9a-zA-Z]，长度不超过64
        optional string network_guid = 19;

        // DSP自定义的扩展字段，格式可为k=v&k1=v1形式，最长128个字节，超长可能会被截断
        // 可用于智能唤端参数__EXT__等场景
        optional string extend_data = 20;
    }

    message TemplateData {
        required uint64 template_id = 1;

        // 模板字段信息
        message Field {
            // 模板中创意元素的属性名, 如：title, 参见文档 native-template.pdf
            required string name = 1;

            // 属性值
            required string value = 2;
        }
        repeated Field fields = 2;
    }

    message Landing {
        // 广告的点击跳转地址, H5方式打开或者ios universal link打开
        // 必须以https://开头.
        repeated string click_through_url = 1;

        // deeplink唤端链接
        optional string deeplink_uri = 2;

        // 广告跳转的最终目标页面地址, 非点击地址
        // 必须以https://开头.
        optional string destination_url = 3;

        // 智能唤端场景使用，deeplink的唤端优先级
        optional string app_schema_ids = 4;

        // 落地页打开方式, 默认方式无需填写，优先deeplink，其次click_through_url
        // 若需指定其他方式打开(如：ulink)，请填写
        // 1-APP内打开H5落地页, 3-deeplink, 4-Universal Link(IOS), 5-直播间打开, 6-半屏落地页(优酷聚焦贴片/快手落地页前置)
        // 7-微信小程序
        optional int32 open_type = 5;

        // 只用于多落地页场景，表示每个click_through_url对应的点击区域，一般无需填写
        repeated string click_area = 6;

        // 小程序相关
        message MiniApp {
            // 小程序所在的主APP客户端，1-微信，表示调用微信SDK唤起微信小程序
            required int32 host_app = 1;
            // 主APP内的小程序ID
            required string app_id = 2;
            // 小程序path
            required string path = 3;
        }
        optional MiniApp mini_app = 7;
    }

    message Monitor {
        // 曝光监测地址
        repeated string imp_tracking_url = 1;

        // 点击监测地址
        repeated string click_tracking_url = 2;

        // android平台app下载完成反馈地址
        // 仅适用于无线app h5流量
        optional string download_complete_url = 3;

        // 竞价胜出反馈地址
        // 仅适用于无线app流量
        optional string winnotice_url = 4;

        // 其他事件监测url
        message EventTrack {
            // type定义
            // 1:播放开始监测;
            // 2:播放中间监测;
            // 3:播放完毕监测;
            // 4:主动播放开始监测;
            // 5:用户互动数据监测;
            // 6:曝光监测;
            // 7:点击监测;
            optional int32 type = 1;
            // 监测链接
            repeated string url = 2;

            // 此事件后触发监测，相对时间，单位：秒
            optional int32 time = 3;

            // 监测发送方式
            // 0-代表API监测方式，由播放器或客户端直接发出U；
            // 1-代表MMA-SDK监测方式，客户端调起MMA-SDK，由MMA-SDK发出U；
            optional int32 send_type = 4;
        }
        repeated EventTrack event_tracks = 5;
    }

    message AppInfo {
        // APP名称,UTF-8编码
        optional string app_name = 1;

        // Android的package name， iOS的Bundle ID
        optional string package_name = 2;

        // 下载地址
        optional string download_url = 3;

        // 支持的下载类型, 参照请求中的参数download_types
        optional int32 download_type = 4;

        // 应用商店ID, iOS下载必填
        optional string app_store_id = 5;
    }

    message BizInfo {
        // 产品线标识, 参见字典取值
        optional int32 product_id = 1;

        // 业务方标识，参见字典取值
        optional int32 biz_id = 2;

        // 预算类型：1-市场，2-用增，3-商业化
        optional int32 budget_type = 3;
        // 合同ID
        optional string contract_id = 4;
        // DSP内部的计划ID, 唯一标识内部一个计划
        optional string campaigin_id = 5;
        // 推广单元ID
        optional string adgroup_id = 6;
        // DSP内部分桶ID
        repeated string bucket_ids = 7;
        // 是否可以抄底投放（如直投PDB抄底）
        optional bool is_default_ad_enabled = 8 [ default = false ];
        // 产品优先级:
        optional int32 product_priority = 9;
        // 订单优先级
        optional int32 order_priority = 10;
        // 客户优先级
        optional int32 adv_priority = 11;
        // 算法打分
        optional double score = 12;
        // 广告优先级
        optional int32 ad_priority = 13 [ default = 0 ];
        // 创意投放日期，优先使用campaign_date，天级曝光控制
        // begin_time和end_time如存在则只能在其区间内才可曝光
        // 仅二环开屏支持
        optional uint64 begin_time = 14;
        optional uint64 end_time = 15;
    }

    message UserQuality {
        // 用户评估分类：1-用户质量评估
        optional int32 type = 1;

        // 用户评估分值
        optional double quality = 2;
    }

    // 用户数据
    message UserInfo {
        // DSP内部的user id
        optional string user_id = 1;
    }
}

service BidService {
    rpc Bid(BidRequest) returns(BidResponse) { }
}
