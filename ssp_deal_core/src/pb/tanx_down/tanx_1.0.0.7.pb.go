// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: tanx_1.0.0.7.proto

package tanx_down

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PricingType int32

const (
	PricingType_PRICING_CPM PricingType = 1
	PricingType_PRICING_CPC PricingType = 2
)

// Enum value maps for PricingType.
var (
	PricingType_name = map[int32]string{
		1: "PRICING_CPM",
		2: "PRICING_CPC",
	}
	PricingType_value = map[string]int32{
		"PRICING_CPM": 1,
		"PRICING_CPC": 2,
	}
)

func (x PricingType) Enum() *PricingType {
	p := new(PricingType)
	*p = x
	return p
}

func (x PricingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PricingType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanx_1_0_0_7_proto_enumTypes[0].Descriptor()
}

func (PricingType) Type() protoreflect.EnumType {
	return &file_tanx_1_0_0_7_proto_enumTypes[0]
}

func (x PricingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PricingType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PricingType(num)
	return nil
}

// Deprecated: Use PricingType.Descriptor instead.
func (PricingType) EnumDescriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0}
}

type BidRequest_Imp_Deal_DealType int32

const (
	// PA
	BidRequest_Imp_Deal_PRIVATE_AUCTION BidRequest_Imp_Deal_DealType = 1
	// PD
	BidRequest_Imp_Deal_PREFERRED_DEAL BidRequest_Imp_Deal_DealType = 2
	// PDB
	BidRequest_Imp_Deal_DIRECT_BUY BidRequest_Imp_Deal_DealType = 3
)

// Enum value maps for BidRequest_Imp_Deal_DealType.
var (
	BidRequest_Imp_Deal_DealType_name = map[int32]string{
		1: "PRIVATE_AUCTION",
		2: "PREFERRED_DEAL",
		3: "DIRECT_BUY",
	}
	BidRequest_Imp_Deal_DealType_value = map[string]int32{
		"PRIVATE_AUCTION": 1,
		"PREFERRED_DEAL":  2,
		"DIRECT_BUY":      3,
	}
)

func (x BidRequest_Imp_Deal_DealType) Enum() *BidRequest_Imp_Deal_DealType {
	p := new(BidRequest_Imp_Deal_DealType)
	*p = x
	return p
}

func (x BidRequest_Imp_Deal_DealType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Deal_DealType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanx_1_0_0_7_proto_enumTypes[1].Descriptor()
}

func (BidRequest_Imp_Deal_DealType) Type() protoreflect.EnumType {
	return &file_tanx_1_0_0_7_proto_enumTypes[1]
}

func (x BidRequest_Imp_Deal_DealType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Deal_DealType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Deal_DealType(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Deal_DealType.Descriptor instead.
func (BidRequest_Imp_Deal_DealType) EnumDescriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 2, 0}
}

// 出价类型
type BidResponse_Bid_BidType int32

const (
	// 正常出价
	BidResponse_Bid_NORMAL BidResponse_Bid_BidType = 0
	// 抄底广告，联动投放
	BidResponse_Bid_DEFAULT BidResponse_Bid_BidType = 1
	// 市场广告(新增)
	BidResponse_Bid_MARKET BidResponse_Bid_BidType = 2
)

// Enum value maps for BidResponse_Bid_BidType.
var (
	BidResponse_Bid_BidType_name = map[int32]string{
		0: "NORMAL",
		1: "DEFAULT",
		2: "MARKET",
	}
	BidResponse_Bid_BidType_value = map[string]int32{
		"NORMAL":  0,
		"DEFAULT": 1,
		"MARKET":  2,
	}
)

func (x BidResponse_Bid_BidType) Enum() *BidResponse_Bid_BidType {
	p := new(BidResponse_Bid_BidType)
	*p = x
	return p
}

func (x BidResponse_Bid_BidType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_BidType) Descriptor() protoreflect.EnumDescriptor {
	return file_tanx_1_0_0_7_proto_enumTypes[2].Descriptor()
}

func (BidResponse_Bid_BidType) Type() protoreflect.EnumType {
	return &file_tanx_1_0_0_7_proto_enumTypes[2]
}

func (x BidResponse_Bid_BidType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_BidType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_BidType(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_BidType.Descriptor instead.
func (BidResponse_Bid_BidType) EnumDescriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 1, 0}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前协议版本号，目前为1
	Version *int32 `protobuf:"varint,1,req,name=version" json:"version,omitempty"`
	// 本请求唯一ID，32字节的字符串
	Id      *string             `protobuf:"bytes,2,req,name=id" json:"id,omitempty"`
	Imps    []*BidRequest_Imp   `protobuf:"bytes,3,rep,name=imps" json:"imps,omitempty"`
	Site    *BidRequest_Site    `protobuf:"bytes,4,opt,name=site" json:"site,omitempty"`
	App     *BidRequest_App     `protobuf:"bytes,5,opt,name=app" json:"app,omitempty"`
	Content *BidRequest_Content `protobuf:"bytes,6,opt,name=content" json:"content,omitempty"`
	Device  *BidRequest_Device  `protobuf:"bytes,7,opt,name=device" json:"device,omitempty"`
	User    *BidRequest_User    `protobuf:"bytes,8,opt,name=user" json:"user,omitempty"`
	// 如果为true，那么这是一个测试请求，DSP需要返回一个正常填写的应答
	// Tanx不会展现给用户，且不会对该次请求计费
	IsTest *bool `protobuf:"varint,9,opt,name=is_test,json=isTest,def=0" json:"is_test,omitempty"`
	// 如果为true，那么这是一个ping请求，DSP需要返回一个包含请求ID的空的应答
	IsPing *bool `protobuf:"varint,10,opt,name=is_ping,json=isPing,def=0" json:"is_ping,omitempty"`
	// 如果为true，那么这是一个预览请求，DSP需要返回一个正常填写的应答
	IsPreview *bool `protobuf:"varint,11,opt,name=is_preview,json=isPreview,def=0" json:"is_preview,omitempty"`
	// 流量所属来源:adx type 0表示SSP流量, 3-TanxSDK联盟流量
	AdxType *int32 `protobuf:"varint,12,opt,name=adx_type,json=adxType,def=0" json:"adx_type,omitempty"`
	// 是否必须返回https广告
	HttpsRequired *bool `protobuf:"varint,13,opt,name=https_required,json=httpsRequired,def=0" json:"https_required,omitempty"`
	// 附加信息
	ExtendInfo *string `protobuf:"bytes,14,opt,name=extend_info,json=extendInfo" json:"extend_info,omitempty"`
	// 阿里内部数据
	PrivateInfo *BidRequest_PrivateInfo `protobuf:"bytes,15,opt,name=private_info,json=privateInfo" json:"private_info,omitempty"`
	// 为DSP预留的最大竞价时间，包括网络传输时间，单位:ms
	MaxBidTime *int32 `protobuf:"varint,16,opt,name=max_bid_time,json=maxBidTime" json:"max_bid_time,omitempty"`
	// 媒体实验ID
	MediaExpIds []string `protobuf:"bytes,17,rep,name=media_exp_ids,json=mediaExpIds" json:"media_exp_ids,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_IsTest        = bool(false)
	Default_BidRequest_IsPing        = bool(false)
	Default_BidRequest_IsPreview     = bool(false)
	Default_BidRequest_AdxType       = int32(0)
	Default_BidRequest_HttpsRequired = bool(false)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetImps() []*BidRequest_Imp {
	if x != nil {
		return x.Imps
	}
	return nil
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetContent() *BidRequest_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetIsTest() bool {
	if x != nil && x.IsTest != nil {
		return *x.IsTest
	}
	return Default_BidRequest_IsTest
}

func (x *BidRequest) GetIsPing() bool {
	if x != nil && x.IsPing != nil {
		return *x.IsPing
	}
	return Default_BidRequest_IsPing
}

func (x *BidRequest) GetIsPreview() bool {
	if x != nil && x.IsPreview != nil {
		return *x.IsPreview
	}
	return Default_BidRequest_IsPreview
}

func (x *BidRequest) GetAdxType() int32 {
	if x != nil && x.AdxType != nil {
		return *x.AdxType
	}
	return Default_BidRequest_AdxType
}

func (x *BidRequest) GetHttpsRequired() bool {
	if x != nil && x.HttpsRequired != nil {
		return *x.HttpsRequired
	}
	return Default_BidRequest_HttpsRequired
}

func (x *BidRequest) GetExtendInfo() string {
	if x != nil && x.ExtendInfo != nil {
		return *x.ExtendInfo
	}
	return ""
}

func (x *BidRequest) GetPrivateInfo() *BidRequest_PrivateInfo {
	if x != nil {
		return x.PrivateInfo
	}
	return nil
}

func (x *BidRequest) GetMaxBidTime() int32 {
	if x != nil && x.MaxBidTime != nil {
		return *x.MaxBidTime
	}
	return 0
}

func (x *BidRequest) GetMediaExpIds() []string {
	if x != nil {
		return x.MediaExpIds
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前协议版本号，目前为1
	Version *int32 `protobuf:"varint,1,req,name=version" json:"version,omitempty"`
	// 请填充BidRequest中的id, 32字节的字符串
	Id       *string                `protobuf:"bytes,2,req,name=id" json:"id,omitempty"`
	SeatBids []*BidResponse_SeatBid `protobuf:"bytes,3,rep,name=seat_bids,json=seatBids" json:"seat_bids,omitempty"`
	// 不参竞缓存时长，单位：秒
	CacheDuration *int32 `protobuf:"varint,4,opt,name=cache_duration,json=cacheDuration,def=0" json:"cache_duration,omitempty"`
	// DSP返回的用户信息
	UserInfo *BidResponse_UserInfo `protobuf:"bytes,5,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
}

// Default values for BidResponse fields.
const (
	Default_BidResponse_CacheDuration = int32(0)
)

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetSeatBids() []*BidResponse_SeatBid {
	if x != nil {
		return x.SeatBids
	}
	return nil
}

func (x *BidResponse) GetCacheDuration() int32 {
	if x != nil && x.CacheDuration != nil {
		return *x.CacheDuration
	}
	return Default_BidResponse_CacheDuration
}

func (x *BidResponse) GetUserInfo() *BidResponse_UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标识此曝光机会的唯一ID
	ImpId *string `protobuf:"bytes,1,req,name=imp_id,json=impId" json:"imp_id,omitempty"`
	// 推广位的唯一标识
	Pid *string `protobuf:"bytes,2,req,name=pid" json:"pid,omitempty"`
	// 兼容原YES系统的tagid, 即将废弃不再维护, 请使用pid
	TagId *string `protobuf:"bytes,3,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	// 媒体的唯一标识, 和创意审核状态查询api配合使用
	PublisherId *string `protobuf:"bytes,4,opt,name=publisher_id,json=publisherId" json:"publisher_id,omitempty"`
	// 资源类型, 如：开屏、信息流等，参见字典
	ResourceType *int32 `protobuf:"varint,5,opt,name=resource_type,json=resourceType" json:"resource_type,omitempty"`
	// 信息流广告刷数
	RefreshNum *int32 `protobuf:"varint,6,opt,name=refresh_num,json=refreshNum" json:"refresh_num,omitempty"`
	// 推广位屏数
	ScreenNum *int32 `protobuf:"varint,7,opt,name=screen_num,json=screenNum" json:"screen_num,omitempty"`
	// 推广位位置, 如信息流广告的第几个坑位
	Position *int32 `protobuf:"varint,8,opt,name=position" json:"position,omitempty"`
	// 推广位尺寸
	Width  *int32                `protobuf:"varint,9,opt,name=width" json:"width,omitempty"`
	Height *int32                `protobuf:"varint,10,opt,name=height" json:"height,omitempty"`
	Video  *BidRequest_Imp_Video `protobuf:"bytes,11,opt,name=video" json:"video,omitempty"`
	// 推广位希望从单个DSP获取的竞价广告数量，DSP可以提供小于等于此值的广告个数
	// 多于max_ad_num数值的广告，将被截断而不参与竞价
	MaxAdNum *int32 `protobuf:"varint,12,opt,name=max_ad_num,json=maxAdNum,def=1" json:"max_ad_num,omitempty"`
	// 最低竞标价格，货币单位为人民币，数值含义为分/千次展现
	MinCpmPrice *int32 `protobuf:"varint,13,opt,name=min_cpm_price,json=minCpmPrice" json:"min_cpm_price,omitempty"`
	// 最低竞标价格，货币单位为人民币，数值含义为分/点击
	MinCpcPrice *int32 `protobuf:"varint,14,opt,name=min_cpc_price,json=minCpcPrice" json:"min_cpc_price,omitempty"`
	// 是否对先后发生的多次展现重复计费
	ImpressionRepeatable *bool `protobuf:"varint,15,opt,name=impression_repeatable,json=impressionRepeatable,def=0" json:"impression_repeatable,omitempty"`
	// 本次请求可用的模板列表，参见文档 native-template.pdf
	Templates []*BidRequest_Imp_Template `protobuf:"bytes,16,rep,name=templates" json:"templates,omitempty"`
	// PMP Deal信息列表
	Deals []*BidRequest_Imp_Deal `protobuf:"bytes,17,rep,name=deals" json:"deals,omitempty"`
	// 预投放日期, 跨天预加载广告使用，格式:"20220910"
	CampaignDate *string `protobuf:"bytes,18,opt,name=campaign_date,json=campaignDate" json:"campaign_date,omitempty"`
	// 推广位可选的广告打开方式,默认支持Click(含H5), 其他APP打开方式为：
	// 1-APP内打开H5落地页, 3-deeplink, 4-Universal Link(IOS), 5-直播间打开, 6-半屏落地页(优酷聚焦贴片/快手落地页前置)
	OpenTypes []int32 `protobuf:"varint,19,rep,name=open_types,json=openTypes" json:"open_types,omitempty"`
	// 竞价类型，空默认为CPM
	PricingType       []PricingType                      `protobuf:"varint,20,rep,name=pricing_type,json=pricingType,enum=tanx_down.PricingType" json:"pricing_type,omitempty"`
	ProposedCreatives []*BidRequest_Imp_ProposedCreative `protobuf:"bytes,21,rep,name=proposed_creatives,json=proposedCreatives" json:"proposed_creatives,omitempty"`
	ItemInfos         []*BidRequest_Imp_ItemInfo         `protobuf:"bytes,22,rep,name=item_infos,json=itemInfos" json:"item_infos,omitempty"`
	ExcludedInfo      *BidRequest_Imp_ExcludedInfo       `protobuf:"bytes,23,opt,name=excluded_info,json=excludedInfo" json:"excluded_info,omitempty"`
	// 支持的下载类型: 1.链接下载 2.应用商店下载
	DownloadTypes []int32 `protobuf:"varint,24,rep,name=download_types,json=downloadTypes" json:"download_types,omitempty"`
}

// Default values for BidRequest_Imp fields.
const (
	Default_BidRequest_Imp_MaxAdNum             = int32(1)
	Default_BidRequest_Imp_ImpressionRepeatable = bool(false)
)

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Imp) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *BidRequest_Imp) GetPid() string {
	if x != nil && x.Pid != nil {
		return *x.Pid
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil && x.TagId != nil {
		return *x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetPublisherId() string {
	if x != nil && x.PublisherId != nil {
		return *x.PublisherId
	}
	return ""
}

func (x *BidRequest_Imp) GetResourceType() int32 {
	if x != nil && x.ResourceType != nil {
		return *x.ResourceType
	}
	return 0
}

func (x *BidRequest_Imp) GetRefreshNum() int32 {
	if x != nil && x.RefreshNum != nil {
		return *x.RefreshNum
	}
	return 0
}

func (x *BidRequest_Imp) GetScreenNum() int32 {
	if x != nil && x.ScreenNum != nil {
		return *x.ScreenNum
	}
	return 0
}

func (x *BidRequest_Imp) GetPosition() int32 {
	if x != nil && x.Position != nil {
		return *x.Position
	}
	return 0
}

func (x *BidRequest_Imp) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidRequest_Imp) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetMaxAdNum() int32 {
	if x != nil && x.MaxAdNum != nil {
		return *x.MaxAdNum
	}
	return Default_BidRequest_Imp_MaxAdNum
}

func (x *BidRequest_Imp) GetMinCpmPrice() int32 {
	if x != nil && x.MinCpmPrice != nil {
		return *x.MinCpmPrice
	}
	return 0
}

func (x *BidRequest_Imp) GetMinCpcPrice() int32 {
	if x != nil && x.MinCpcPrice != nil {
		return *x.MinCpcPrice
	}
	return 0
}

func (x *BidRequest_Imp) GetImpressionRepeatable() bool {
	if x != nil && x.ImpressionRepeatable != nil {
		return *x.ImpressionRepeatable
	}
	return Default_BidRequest_Imp_ImpressionRepeatable
}

func (x *BidRequest_Imp) GetTemplates() []*BidRequest_Imp_Template {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *BidRequest_Imp) GetDeals() []*BidRequest_Imp_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *BidRequest_Imp) GetCampaignDate() string {
	if x != nil && x.CampaignDate != nil {
		return *x.CampaignDate
	}
	return ""
}

func (x *BidRequest_Imp) GetOpenTypes() []int32 {
	if x != nil {
		return x.OpenTypes
	}
	return nil
}

func (x *BidRequest_Imp) GetPricingType() []PricingType {
	if x != nil {
		return x.PricingType
	}
	return nil
}

func (x *BidRequest_Imp) GetProposedCreatives() []*BidRequest_Imp_ProposedCreative {
	if x != nil {
		return x.ProposedCreatives
	}
	return nil
}

func (x *BidRequest_Imp) GetItemInfos() []*BidRequest_Imp_ItemInfo {
	if x != nil {
		return x.ItemInfos
	}
	return nil
}

func (x *BidRequest_Imp) GetExcludedInfo() *BidRequest_Imp_ExcludedInfo {
	if x != nil {
		return x.ExcludedInfo
	}
	return nil
}

func (x *BidRequest_Imp) GetDownloadTypes() []int32 {
	if x != nil {
		return x.DownloadTypes
	}
	return nil
}

type BidRequest_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 推广位所在的网站名称
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// 推广位所在的页面url
	Page *string `protobuf:"bytes,2,opt,name=page" json:"page,omitempty"`
	// 推广位所在的页面refer url
	Ref *string `protobuf:"bytes,3,opt,name=ref" json:"ref,omitempty"`
	// 推广位所在网站的分类
	// 参见数据字典 Tanx-dict-site-category.txt
	Category []int32 `protobuf:"varint,4,rep,name=category" json:"category,omitempty"`
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_Site) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Site) GetPage() string {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return ""
}

func (x *BidRequest_Site) GetRef() string {
	if x != nil && x.Ref != nil {
		return *x.Ref
	}
	return ""
}

func (x *BidRequest_Site) GetCategory() []int32 {
	if x != nil {
		return x.Category
	}
	return nil
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用名，例如：淘宝, UTF-8编码
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// 应用包名, 例如：com.moji.MojiWeather
	PackageName *string `protobuf:"bytes,2,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// app版本号
	Version *string `protobuf:"bytes,3,opt,name=version" json:"version,omitempty"`
	// app类目
	Category []int32 `protobuf:"varint,4,rep,name=category" json:"category,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidRequest_App) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest_App) GetCategory() []int32 {
	if x != nil {
		return x.Category
	}
	return nil
}

// 内容上下文
type BidRequest_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标题, UTF-8编码
	Title *string `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// 视频时长，单位:秒
	Duration *int32 `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	// 关键词
	Keywords []string `protobuf:"bytes,3,rep,name=keywords" json:"keywords,omitempty"`
	// 用户的搜索关键词
	QueryTerm *string `protobuf:"bytes,4,opt,name=query_term,json=queryTerm" json:"query_term,omitempty"`
	// 输入框提示词
	QuerySuggestion []string `protobuf:"bytes,5,rep,name=query_suggestion,json=querySuggestion" json:"query_suggestion,omitempty"`
	// 视频节目或剧集ID
	ProgramId *string `protobuf:"bytes,6,opt,name=program_id,json=programId" json:"program_id,omitempty"`
	// 视频ID, 原为id，改为video_id
	VideoId *string `protobuf:"bytes,7,opt,name=video_id,json=videoId" json:"video_id,omitempty"`
	// 内容生产者账号
	ProducerId []string `protobuf:"bytes,8,rep,name=producer_id,json=producerId" json:"producer_id,omitempty"`
	// 内容类目, 兼容优酷内容类目【二级类目】
	Category []int32 `protobuf:"varint,9,rep,name=category" json:"category,omitempty"`
}

func (x *BidRequest_Content) Reset() {
	*x = BidRequest_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Content) ProtoMessage() {}

func (x *BidRequest_Content) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Content.ProtoReflect.Descriptor instead.
func (*BidRequest_Content) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BidRequest_Content) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidRequest_Content) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *BidRequest_Content) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *BidRequest_Content) GetQueryTerm() string {
	if x != nil && x.QueryTerm != nil {
		return *x.QueryTerm
	}
	return ""
}

func (x *BidRequest_Content) GetQuerySuggestion() []string {
	if x != nil {
		return x.QuerySuggestion
	}
	return nil
}

func (x *BidRequest_Content) GetProgramId() string {
	if x != nil && x.ProgramId != nil {
		return *x.ProgramId
	}
	return ""
}

func (x *BidRequest_Content) GetVideoId() string {
	if x != nil && x.VideoId != nil {
		return *x.VideoId
	}
	return ""
}

func (x *BidRequest_Content) GetProducerId() []string {
	if x != nil {
		return x.ProducerId
	}
	return nil
}

func (x *BidRequest_Content) GetCategory() []int32 {
	if x != nil {
		return x.Category
	}
	return nil
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户的IP地址，IPv4或IPv6, 如：***********
	Ip *string `protobuf:"bytes,1,opt,name=ip" json:"ip,omitempty"`
	// 用户的浏览器类型，即HTTP请求头部的User-Agent
	UserAgent *string `protobuf:"bytes,2,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	// 设备类型：0—手机，1—平板，2—PC，3—互联网电视
	DeviceType *int32 `protobuf:"varint,3,opt,name=device_type,json=deviceType" json:"device_type,omitempty"`
	// 操作系统 0-未识别；1-PC(Windows)；2-iOS；3-Android；4-Windows Phone；
	// 5-Mac；6-Linux；7-Yun；8-Tizen；9-Cycle
	Os *int32 `protobuf:"varint,4,opt,name=os" json:"os,omitempty"`
	// 操作系统版本号 (e.g., "3.1.2").
	Osv *string `protobuf:"bytes,5,opt,name=osv" json:"osv,omitempty"`
	// 制造商(e.g., "Apple").
	Make *string `protobuf:"bytes,6,opt,name=make" json:"make,omitempty"`
	// 设备型号(e.g., "iPhone").
	Model *string `protobuf:"bytes,7,opt,name=model" json:"model,omitempty"`
	// 运营商, 0-未识别；1-中国移动；2-中国联通：3-中国电信；4-其他；
	Carrier *int32 `protobuf:"varint,8,opt,name=carrier" json:"carrier,omitempty"`
	// 设备所处网络环境, 0-未识别, 1-wifi, 2-2g, 3-3g, 4-4g, 5-5g
	ConnectionType *int32 `protobuf:"varint,9,opt,name=connection_type,json=connectionType" json:"connection_type,omitempty"`
	// 屏幕宽高
	ScreenWidth  *int32                 `protobuf:"varint,10,opt,name=screen_width,json=screenWidth" json:"screen_width,omitempty"`
	ScreenHeight *int32                 `protobuf:"varint,11,opt,name=screen_height,json=screenHeight" json:"screen_height,omitempty"`
	Geo          *BidRequest_Device_Geo `protobuf:"bytes,12,opt,name=geo" json:"geo,omitempty"`
	// 用户已安装的app列表,1-手淘,2-支付宝,3-闲鱼,4-饿了么
	InstalledAppIds []int32 `protobuf:"varint,13,rep,name=installed_app_ids,json=installedAppIds" json:"installed_app_ids,omitempty"`
	// 媒体端的唯一设备id
	MediaDeviceId *string `protobuf:"bytes,14,opt,name=media_device_id,json=mediaDeviceId" json:"media_device_id,omitempty"`
	Idfa          *string `protobuf:"bytes,15,opt,name=idfa" json:"idfa,omitempty"`
	Oaid          *string `protobuf:"bytes,16,opt,name=oaid" json:"oaid,omitempty"`
	AndroidId     *string `protobuf:"bytes,17,opt,name=android_id,json=androidId" json:"android_id,omitempty"`
	// 支持盒子传过来yun os相关的UUID
	Uuid *string `protobuf:"bytes,18,opt,name=uuid" json:"uuid,omitempty"`
	Mac  *string `protobuf:"bytes,19,opt,name=mac" json:"mac,omitempty"`
	// 该值为imei md5值
	ImeiMd5 *string `protobuf:"bytes,20,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`
	// 该值为idfa md5值
	IdfaMd5 *string `protobuf:"bytes,21,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`
	// 该值为oaid md5值
	OaidMd5 *string `protobuf:"bytes,22,opt,name=oaid_md5,json=oaidMd5" json:"oaid_md5,omitempty"`
	// 该值为mac  md5值
	MacMd5 *string                   `protobuf:"bytes,23,opt,name=mac_md5,json=macMd5" json:"mac_md5,omitempty"`
	Caids  []*BidRequest_Device_CAID `protobuf:"bytes,24,rep,name=caids" json:"caids,omitempty"`
	// ios上设备标识
	OpenUdid *string `protobuf:"bytes,25,opt,name=open_udid,json=openUdid" json:"open_udid,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetUserAgent() string {
	if x != nil && x.UserAgent != nil {
		return *x.UserAgent
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceType() int32 {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return 0
}

func (x *BidRequest_Device) GetOs() int32 {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return 0
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil && x.ConnectionType != nil {
		return *x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil && x.ScreenWidth != nil {
		return *x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil && x.ScreenHeight != nil {
		return *x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetInstalledAppIds() []int32 {
	if x != nil {
		return x.InstalledAppIds
	}
	return nil
}

func (x *BidRequest_Device) GetMediaDeviceId() string {
	if x != nil && x.MediaDeviceId != nil {
		return *x.MediaDeviceId
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil && x.AndroidId != nil {
		return *x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetUuid() string {
	if x != nil && x.Uuid != nil {
		return *x.Uuid
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil && x.MacMd5 != nil {
		return *x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetCaids() []*BidRequest_Device_CAID {
	if x != nil {
		return x.Caids
	}
	return nil
}

func (x *BidRequest_Device) GetOpenUdid() string {
	if x != nil && x.OpenUdid != nil {
		return *x.OpenUdid
	}
	return ""
}

// 人群信息
type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 年龄
	Age *int32 `protobuf:"varint,1,opt,name=age" json:"age,omitempty"`
	// 性别：0-未知、1-男性、2-女性
	Gender *int32 `protobuf:"varint,2,opt,name=gender" json:"gender,omitempty"`
	// 婚姻：0-未婚、1-已婚
	Marriage *int32 `protobuf:"varint,3,opt,name=marriage" json:"marriage,omitempty"`
	// 在校：0-不在校、1-在校学生
	School *int32 `protobuf:"varint,4,opt,name=school" json:"school,omitempty"`
	// 标准化的人群标签
	Category []uint64 `protobuf:"varint,5,rep,name=category" json:"category,omitempty"`
	// 人群ID
	DmpIds []uint64 `protobuf:"varint,6,rep,name=dmp_ids,json=dmpIds" json:"dmp_ids,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 5}
}

func (x *BidRequest_User) GetAge() int32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

func (x *BidRequest_User) GetGender() int32 {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return 0
}

func (x *BidRequest_User) GetMarriage() int32 {
	if x != nil && x.Marriage != nil {
		return *x.Marriage
	}
	return 0
}

func (x *BidRequest_User) GetSchool() int32 {
	if x != nil && x.School != nil {
		return *x.School
	}
	return 0
}

func (x *BidRequest_User) GetCategory() []uint64 {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *BidRequest_User) GetDmpIds() []uint64 {
	if x != nil {
		return x.DmpIds
	}
	return nil
}

// 预留字段，暂无实际值
type BidRequest_PrivateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsStressTest *bool   `protobuf:"varint,1,opt,name=is_stress_test,json=isStressTest" json:"is_stress_test,omitempty"`
	TraceInfo    *string `protobuf:"bytes,2,opt,name=trace_info,json=traceInfo" json:"trace_info,omitempty"`
	CookieCna    *string `protobuf:"bytes,3,opt,name=cookie_cna,json=cookieCna" json:"cookie_cna,omitempty"`
	Utdid        *string `protobuf:"bytes,4,opt,name=utdid" json:"utdid,omitempty"`
	Aid          *string `protobuf:"bytes,5,opt,name=aid" json:"aid,omitempty"`
	// ali aaid，非android的aaid
	AliAaid *string `protobuf:"bytes,6,opt,name=ali_aaid,json=aliAaid" json:"ali_aaid,omitempty"`
	// 淘宝id，给k2传递用于减少aid查询量
	TaobaoId *string `protobuf:"bytes,7,opt,name=taobao_id,json=taobaoId" json:"taobao_id,omitempty"`
	// utf-8的unicode值
	TaobaoNick *string `protobuf:"bytes,8,opt,name=taobao_nick,json=taobaoNick" json:"taobao_nick,omitempty"`
	// 是否立即渲染，主要是开屏使用，避免返回过大素材
	// 名称待定，20230309
	IsRealtimeRequest *bool `protobuf:"varint,9,opt,name=is_realtime_request,json=isRealtimeRequest,def=0" json:"is_realtime_request,omitempty"`
	// 客户端类型：a=app,w=web,off=离线应用
	ClientType *string `protobuf:"bytes,10,opt,name=client_type,json=clientType" json:"client_type,omitempty"`
	// 广告位设备类型:0-pad,1-phone,2-ott,3-pc,4-ooh,5-不限
	PidDeviceType *int32 `protobuf:"varint,11,opt,name=pid_device_type,json=pidDeviceType" json:"pid_device_type,omitempty"`
	// 广告位操作系统类型:0-ios,1-android,5-不限
	PidOsType *int32 `protobuf:"varint,12,opt,name=pid_os_type,json=pidOsType" json:"pid_os_type,omitempty"`
}

// Default values for BidRequest_PrivateInfo fields.
const (
	Default_BidRequest_PrivateInfo_IsRealtimeRequest = bool(false)
)

func (x *BidRequest_PrivateInfo) Reset() {
	*x = BidRequest_PrivateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_PrivateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_PrivateInfo) ProtoMessage() {}

func (x *BidRequest_PrivateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_PrivateInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_PrivateInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 6}
}

func (x *BidRequest_PrivateInfo) GetIsStressTest() bool {
	if x != nil && x.IsStressTest != nil {
		return *x.IsStressTest
	}
	return false
}

func (x *BidRequest_PrivateInfo) GetTraceInfo() string {
	if x != nil && x.TraceInfo != nil {
		return *x.TraceInfo
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetCookieCna() string {
	if x != nil && x.CookieCna != nil {
		return *x.CookieCna
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetUtdid() string {
	if x != nil && x.Utdid != nil {
		return *x.Utdid
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetAid() string {
	if x != nil && x.Aid != nil {
		return *x.Aid
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetAliAaid() string {
	if x != nil && x.AliAaid != nil {
		return *x.AliAaid
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetTaobaoId() string {
	if x != nil && x.TaobaoId != nil {
		return *x.TaobaoId
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetTaobaoNick() string {
	if x != nil && x.TaobaoNick != nil {
		return *x.TaobaoNick
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetIsRealtimeRequest() bool {
	if x != nil && x.IsRealtimeRequest != nil {
		return *x.IsRealtimeRequest
	}
	return Default_BidRequest_PrivateInfo_IsRealtimeRequest
}

func (x *BidRequest_PrivateInfo) GetClientType() string {
	if x != nil && x.ClientType != nil {
		return *x.ClientType
	}
	return ""
}

func (x *BidRequest_PrivateInfo) GetPidDeviceType() int32 {
	if x != nil && x.PidDeviceType != nil {
		return *x.PidDeviceType
	}
	return 0
}

func (x *BidRequest_PrivateInfo) GetPidOsType() int32 {
	if x != nil && x.PidOsType != nil {
		return *x.PidOsType
	}
	return 0
}

// 视频资源位信息
type BidRequest_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 贴片位置相对于所在视频的起始时间，0 表示前贴片, -1 表示后贴片，大于0的值表示中插
	VideoadStartDelay *int32 `protobuf:"varint,1,opt,name=videoad_start_delay,json=videoadStartDelay" json:"videoad_start_delay,omitempty"`
	// 上述位置可能包含多个贴片，表示本贴片在上述集合中相对起始位置，单位:毫秒
	VideoadSectionStartDelay *int32 `protobuf:"varint,2,opt,name=videoad_section_start_delay,json=videoadSectionStartDelay" json:"videoad_section_start_delay,omitempty"`
	// 贴片最小播放时间长度,视频创意播放时间不可小于该值，单位:毫秒
	MinAdDuration *int32 `protobuf:"varint,3,opt,name=min_ad_duration,json=minAdDuration" json:"min_ad_duration,omitempty"`
	// 贴片最大播放时间长度，单位:毫秒
	MaxAdDuration *int32 `protobuf:"varint,4,opt,name=max_ad_duration,json=maxAdDuration" json:"max_ad_duration,omitempty"`
}

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BidRequest_Imp_Video) GetVideoadStartDelay() int32 {
	if x != nil && x.VideoadStartDelay != nil {
		return *x.VideoadStartDelay
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetVideoadSectionStartDelay() int32 {
	if x != nil && x.VideoadSectionStartDelay != nil {
		return *x.VideoadSectionStartDelay
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMinAdDuration() int32 {
	if x != nil && x.MinAdDuration != nil {
		return *x.MinAdDuration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxAdDuration() int32 {
	if x != nil && x.MaxAdDuration != nil {
		return *x.MaxAdDuration
	}
	return 0
}

// 本次请求可用的模板列表，参见文档 native-template.pdf
type BidRequest_Imp_Template struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模板ID
	Id *uint64 `protobuf:"varint,1,req,name=id" json:"id,omitempty"`
	// 必填字段
	RequiredFields []string `protobuf:"bytes,2,rep,name=required_fields,json=requiredFields" json:"required_fields,omitempty"`
	// 可选字段
	OptionalFields []string `protobuf:"bytes,3,rep,name=optional_fields,json=optionalFields" json:"optional_fields,omitempty"`
}

func (x *BidRequest_Imp_Template) Reset() {
	*x = BidRequest_Imp_Template{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Template) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Template) ProtoMessage() {}

func (x *BidRequest_Imp_Template) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Template.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Template) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *BidRequest_Imp_Template) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *BidRequest_Imp_Template) GetRequiredFields() []string {
	if x != nil {
		return x.RequiredFields
	}
	return nil
}

func (x *BidRequest_Imp_Template) GetOptionalFields() []string {
	if x != nil {
		return x.OptionalFields
	}
	return nil
}

// PMP Deal信息
type BidRequest_Imp_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deal id
	DealId *uint64 `protobuf:"varint,1,req,name=deal_id,json=dealId" json:"deal_id,omitempty"`
	// deal类型
	DealType *BidRequest_Imp_Deal_DealType `protobuf:"varint,2,req,name=deal_type,json=dealType,enum=tanx_down.BidRequest_Imp_Deal_DealType" json:"deal_type,omitempty"`
	// 允许的广告主白名单，不设置则默认所有均允许
	AdvIds []uint64 `protobuf:"varint,3,rep,name=adv_ids,json=advIds" json:"adv_ids,omitempty"`
	// 交易价格/底价, 单位：分/CPM
	Price *int32 `protobuf:"varint,4,opt,name=price" json:"price,omitempty"`
	// 是否对先后发生的多次展现重复计费
	ImpressionRepeatable *bool `protobuf:"varint,5,opt,name=impression_repeatable,json=impressionRepeatable,def=0" json:"impression_repeatable,omitempty"`
}

// Default values for BidRequest_Imp_Deal fields.
const (
	Default_BidRequest_Imp_Deal_ImpressionRepeatable = bool(false)
)

func (x *BidRequest_Imp_Deal) Reset() {
	*x = BidRequest_Imp_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Deal) ProtoMessage() {}

func (x *BidRequest_Imp_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Deal) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *BidRequest_Imp_Deal) GetDealId() uint64 {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return 0
}

func (x *BidRequest_Imp_Deal) GetDealType() BidRequest_Imp_Deal_DealType {
	if x != nil && x.DealType != nil {
		return *x.DealType
	}
	return BidRequest_Imp_Deal_PRIVATE_AUCTION
}

func (x *BidRequest_Imp_Deal) GetAdvIds() []uint64 {
	if x != nil {
		return x.AdvIds
	}
	return nil
}

func (x *BidRequest_Imp_Deal) GetPrice() int32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidRequest_Imp_Deal) GetImpressionRepeatable() bool {
	if x != nil && x.ImpressionRepeatable != nil {
		return *x.ImpressionRepeatable
	}
	return Default_BidRequest_Imp_Deal_ImpressionRepeatable
}

// 媒体创意优选列表
type BidRequest_Imp_ProposedCreative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crid *string `protobuf:"bytes,1,opt,name=crid" json:"crid,omitempty"`
}

func (x *BidRequest_Imp_ProposedCreative) Reset() {
	*x = BidRequest_Imp_ProposedCreative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_ProposedCreative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_ProposedCreative) ProtoMessage() {}

func (x *BidRequest_Imp_ProposedCreative) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_ProposedCreative.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_ProposedCreative) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 3}
}

func (x *BidRequest_Imp_ProposedCreative) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

// 商品信息
type BidRequest_Imp_ItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId   []string `protobuf:"bytes,1,rep,name=item_id,json=itemId" json:"item_id,omitempty"`       // 商品id
	SellerId *string  `protobuf:"bytes,2,opt,name=seller_id,json=sellerId" json:"seller_id,omitempty"` // 卖家id
}

func (x *BidRequest_Imp_ItemInfo) Reset() {
	*x = BidRequest_Imp_ItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_ItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_ItemInfo) ProtoMessage() {}

func (x *BidRequest_Imp_ItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_ItemInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_ItemInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 4}
}

func (x *BidRequest_Imp_ItemInfo) GetItemId() []string {
	if x != nil {
		return x.ItemId
	}
	return nil
}

func (x *BidRequest_Imp_ItemInfo) GetSellerId() string {
	if x != nil && x.SellerId != nil {
		return *x.SellerId
	}
	return ""
}

// 屏蔽的广告属性
type BidRequest_Imp_ExcludedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 媒体要求的过滤规则id，多值
	PublisherFilterIds []string `protobuf:"bytes,1,rep,name=publisher_filter_ids,json=publisherFilterIds" json:"publisher_filter_ids,omitempty"`
	// 禁止投放的创意ID
	CreativeIds []string `protobuf:"bytes,2,rep,name=creative_ids,json=creativeIds" json:"creative_ids,omitempty"`
	// 媒体禁止的目标跳转url, 所有条目的总长度不超过200个字符
	// 该字段可能不是全量数据，建议使用离线数据获取媒体设置的全量数据
	ExcludedClickThroughUrls []string `protobuf:"bytes,3,rep,name=excluded_click_through_urls,json=excludedClickThroughUrls" json:"excluded_click_through_urls,omitempty"`
	// 禁止投放的广告主ID
	AdvIds []uint64 `protobuf:"varint,4,rep,name=adv_ids,json=advIds" json:"adv_ids,omitempty"`
	// 禁止投放的广告类目ID
	AdCategory []uint64 `protobuf:"varint,5,rep,name=ad_category,json=adCategory" json:"ad_category,omitempty"`
	// 禁止投放的主播ID
	StreamerIds []uint64 `protobuf:"varint,6,rep,name=streamer_ids,json=streamerIds" json:"streamer_ids,omitempty"`
}

func (x *BidRequest_Imp_ExcludedInfo) Reset() {
	*x = BidRequest_Imp_ExcludedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_ExcludedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_ExcludedInfo) ProtoMessage() {}

func (x *BidRequest_Imp_ExcludedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_ExcludedInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_ExcludedInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 0, 5}
}

func (x *BidRequest_Imp_ExcludedInfo) GetPublisherFilterIds() []string {
	if x != nil {
		return x.PublisherFilterIds
	}
	return nil
}

func (x *BidRequest_Imp_ExcludedInfo) GetCreativeIds() []string {
	if x != nil {
		return x.CreativeIds
	}
	return nil
}

func (x *BidRequest_Imp_ExcludedInfo) GetExcludedClickThroughUrls() []string {
	if x != nil {
		return x.ExcludedClickThroughUrls
	}
	return nil
}

func (x *BidRequest_Imp_ExcludedInfo) GetAdvIds() []uint64 {
	if x != nil {
		return x.AdvIds
	}
	return nil
}

func (x *BidRequest_Imp_ExcludedInfo) GetAdCategory() []uint64 {
	if x != nil {
		return x.AdCategory
	}
	return nil
}

func (x *BidRequest_Imp_ExcludedInfo) GetStreamerIds() []uint64 {
	if x != nil {
		return x.StreamerIds
	}
	return nil
}

// 地理位置信息
type BidRequest_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 经度（小数点格式）例如：116.41667
	Lon *string `protobuf:"bytes,1,opt,name=lon" json:"lon,omitempty"`
	// 纬度（小数点格式）例如：39.91667
	Lat *string `protobuf:"bytes,2,opt,name=lat" json:"lat,omitempty"`
	// 地域码-国家码
	CountryCode *int32 `protobuf:"varint,3,opt,name=country_code,json=countryCode" json:"country_code,omitempty"`
	// 地域码-省份码
	ProvinceCode *int32 `protobuf:"varint,4,opt,name=province_code,json=provinceCode" json:"province_code,omitempty"`
	// 地域码-城市码
	CityCode *int32 `protobuf:"varint,5,opt,name=city_code,json=cityCode" json:"city_code,omitempty"`
	// 地域码-区县码
	DistrictCode *int32 `protobuf:"varint,6,opt,name=district_code,json=districtCode" json:"district_code,omitempty"`
}

func (x *BidRequest_Device_Geo) Reset() {
	*x = BidRequest_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Geo) ProtoMessage() {}

func (x *BidRequest_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Geo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *BidRequest_Device_Geo) GetLon() string {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return ""
}

func (x *BidRequest_Device_Geo) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *BidRequest_Device_Geo) GetCountryCode() int32 {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetProvinceCode() int32 {
	if x != nil && x.ProvinceCode != nil {
		return *x.ProvinceCode
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetCityCode() int32 {
	if x != nil && x.CityCode != nil {
		return *x.CityCode
	}
	return 0
}

func (x *BidRequest_Device_Geo) GetDistrictCode() int32 {
	if x != nil && x.DistrictCode != nil {
		return *x.DistrictCode
	}
	return 0
}

// 广协CAID
type BidRequest_Device_CAID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 广协版本号，如"20201201"
	Ver  *string `protobuf:"bytes,1,req,name=ver" json:"ver,omitempty"`
	Caid *string `protobuf:"bytes,2,req,name=caid" json:"caid,omitempty"`
}

func (x *BidRequest_Device_CAID) Reset() {
	*x = BidRequest_Device_CAID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_CAID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_CAID) ProtoMessage() {}

func (x *BidRequest_Device_CAID) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_CAID.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_CAID) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{0, 4, 1}
}

func (x *BidRequest_Device_CAID) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_Device_CAID) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

// 每个SeatBid针对一个Imp参竞
type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对应Imp中的imp_id, 表示针对此Imp参竞
	ImpId *string `protobuf:"bytes,1,req,name=imp_id,json=impId" json:"imp_id,omitempty"`
	// 通用参竞广告列表
	Bids []*BidResponse_Bid `protobuf:"bytes,2,rep,name=bids" json:"bids,omitempty"`
	// 直投普通广告候补广告列表
	BackupBids []*BidResponse_Bid `protobuf:"bytes,3,rep,name=backup_bids,json=backupBids" json:"backup_bids,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_SeatBid) GetImpId() string {
	if x != nil && x.ImpId != nil {
		return *x.ImpId
	}
	return ""
}

func (x *BidResponse_SeatBid) GetBids() []*BidResponse_Bid {
	if x != nil {
		return x.Bids
	}
	return nil
}

func (x *BidResponse_SeatBid) GetBackupBids() []*BidResponse_Bid {
	if x != nil {
		return x.BackupBids
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 必填,广告创意ID, 长度不超过64字节，且不能包含空格/回车/换行和其他不可见字符
	CreativeId *string `protobuf:"bytes,1,req,name=creative_id,json=creativeId" json:"creative_id,omitempty"`
	// 创意类型
	// 参见数据字典 Tanx-dict-creative-type.txt
	// 1  文字
	// 2  图片
	// 3  Flash
	// 4  视频
	// 12 直播
	// 13 短视频
	// 14 pop联动
	// 15 底纹词联动
	CreativeType *int32 `protobuf:"varint,2,opt,name=creative_type,json=creativeType" json:"creative_type,omitempty"`
	// DSP声明的本广告所属的广告行业类目
	// 参见数据字典 Tanx-dict-ad-category.txt
	Category []uint64 `protobuf:"varint,3,rep,name=category" json:"category,omitempty"`
	// 广告代码片段
	HtmlSnippet *string `protobuf:"bytes,4,opt,name=html_snippet,json=htmlSnippet" json:"html_snippet,omitempty"`
	// 创意模板，包含标题、图片等创意元素
	TemplateData *BidResponse_TemplateData `protobuf:"bytes,5,opt,name=template_data,json=templateData" json:"template_data,omitempty"`
	// 创意子模板，包含标题、图片等创意元素, 依赖主模板样式，只在复杂创意样式使用
	SubTemplateData *BidResponse_TemplateData `protobuf:"bytes,6,opt,name=sub_template_data,json=subTemplateData" json:"sub_template_data,omitempty"`
	// 打开落地页相关
	Landing *BidResponse_Landing `protobuf:"bytes,7,opt,name=landing" json:"landing,omitempty"`
	// 监测相关
	Monitor *BidResponse_Monitor `protobuf:"bytes,8,opt,name=monitor" json:"monitor,omitempty"`
	// 竞价类型
	PricingType *PricingType `protobuf:"varint,9,opt,name=pricing_type,json=pricingType,enum=tanx_down.PricingType" json:"pricing_type,omitempty"`
	// 广告竞标价格（人民币），单位为分/千次展现
	// 该字段值不允许为0, 且不能低于推广位最低竞标价格（BidRequest.min_cpm_price）
	MaxCpmPrice *int32 `protobuf:"varint,10,opt,name=max_cpm_price,json=maxCpmPrice" json:"max_cpm_price,omitempty"`
	// 广告竞标价格（人民币），单位为分/点击
	// 该字段值不允许为0, 且不能低于推广位最低竞标价格（BidRequest.min_cpc_price）
	MaxCpcPrice *int32 `protobuf:"varint,11,opt,name=max_cpc_price,json=maxCpcPrice" json:"max_cpc_price,omitempty"`
	// 参与PMP竞价的id, 要和请求中Deal定义中的dealid一致
	// 如果不设置表示参与公开竞价
	DealId *uint64 `protobuf:"varint,12,opt,name=deal_id,json=dealId" json:"deal_id,omitempty"`
	// 广告主ID
	AdvId *uint64 `protobuf:"varint,13,opt,name=adv_id,json=advId" json:"adv_id,omitempty"`
	// 创意投放日期, 仅开屏使用, 格式:"20160602"
	CampaignDate *string                  `protobuf:"bytes,14,opt,name=campaign_date,json=campaignDate" json:"campaign_date,omitempty"`
	BidType      *BidResponse_Bid_BidType `protobuf:"varint,15,opt,name=bid_type,json=bidType,enum=tanx_down.BidResponse_Bid_BidType" json:"bid_type,omitempty"`
	// 广告相关的APP信息
	AppInfo *BidResponse_AppInfo `protobuf:"bytes,16,opt,name=app_info,json=appInfo" json:"app_info,omitempty"`
	// 阿里内部业务线信息
	BizInfo *BidResponse_BizInfo `protobuf:"bytes,17,opt,name=biz_info,json=bizInfo" json:"biz_info,omitempty"`
	// 用户质量分
	UserQualities []*BidResponse_UserQuality `protobuf:"bytes,18,rep,name=user_qualities,json=userQualities" json:"user_qualities,omitempty"`
	// DSP自定义数据, 只能包含[_0-9a-zA-Z]，长度不超过64
	NetworkGuid *string `protobuf:"bytes,19,opt,name=network_guid,json=networkGuid" json:"network_guid,omitempty"`
	// DSP自定义的扩展字段，格式可为k=v&k1=v1形式，最长128个字节，超长可能会被截断
	// 可用于智能唤端参数__EXT__等场景
	ExtendData *string `protobuf:"bytes,20,opt,name=extend_data,json=extendData" json:"extend_data,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidResponse_Bid) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *BidResponse_Bid) GetCreativeType() int32 {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return 0
}

func (x *BidResponse_Bid) GetCategory() []uint64 {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *BidResponse_Bid) GetHtmlSnippet() string {
	if x != nil && x.HtmlSnippet != nil {
		return *x.HtmlSnippet
	}
	return ""
}

func (x *BidResponse_Bid) GetTemplateData() *BidResponse_TemplateData {
	if x != nil {
		return x.TemplateData
	}
	return nil
}

func (x *BidResponse_Bid) GetSubTemplateData() *BidResponse_TemplateData {
	if x != nil {
		return x.SubTemplateData
	}
	return nil
}

func (x *BidResponse_Bid) GetLanding() *BidResponse_Landing {
	if x != nil {
		return x.Landing
	}
	return nil
}

func (x *BidResponse_Bid) GetMonitor() *BidResponse_Monitor {
	if x != nil {
		return x.Monitor
	}
	return nil
}

func (x *BidResponse_Bid) GetPricingType() PricingType {
	if x != nil && x.PricingType != nil {
		return *x.PricingType
	}
	return PricingType_PRICING_CPM
}

func (x *BidResponse_Bid) GetMaxCpmPrice() int32 {
	if x != nil && x.MaxCpmPrice != nil {
		return *x.MaxCpmPrice
	}
	return 0
}

func (x *BidResponse_Bid) GetMaxCpcPrice() int32 {
	if x != nil && x.MaxCpcPrice != nil {
		return *x.MaxCpcPrice
	}
	return 0
}

func (x *BidResponse_Bid) GetDealId() uint64 {
	if x != nil && x.DealId != nil {
		return *x.DealId
	}
	return 0
}

func (x *BidResponse_Bid) GetAdvId() uint64 {
	if x != nil && x.AdvId != nil {
		return *x.AdvId
	}
	return 0
}

func (x *BidResponse_Bid) GetCampaignDate() string {
	if x != nil && x.CampaignDate != nil {
		return *x.CampaignDate
	}
	return ""
}

func (x *BidResponse_Bid) GetBidType() BidResponse_Bid_BidType {
	if x != nil && x.BidType != nil {
		return *x.BidType
	}
	return BidResponse_Bid_NORMAL
}

func (x *BidResponse_Bid) GetAppInfo() *BidResponse_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *BidResponse_Bid) GetBizInfo() *BidResponse_BizInfo {
	if x != nil {
		return x.BizInfo
	}
	return nil
}

func (x *BidResponse_Bid) GetUserQualities() []*BidResponse_UserQuality {
	if x != nil {
		return x.UserQualities
	}
	return nil
}

func (x *BidResponse_Bid) GetNetworkGuid() string {
	if x != nil && x.NetworkGuid != nil {
		return *x.NetworkGuid
	}
	return ""
}

func (x *BidResponse_Bid) GetExtendData() string {
	if x != nil && x.ExtendData != nil {
		return *x.ExtendData
	}
	return ""
}

type BidResponse_TemplateData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId *uint64                           `protobuf:"varint,1,req,name=template_id,json=templateId" json:"template_id,omitempty"`
	Fields     []*BidResponse_TemplateData_Field `protobuf:"bytes,2,rep,name=fields" json:"fields,omitempty"`
}

func (x *BidResponse_TemplateData) Reset() {
	*x = BidResponse_TemplateData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_TemplateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_TemplateData) ProtoMessage() {}

func (x *BidResponse_TemplateData) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_TemplateData.ProtoReflect.Descriptor instead.
func (*BidResponse_TemplateData) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BidResponse_TemplateData) GetTemplateId() uint64 {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return 0
}

func (x *BidResponse_TemplateData) GetFields() []*BidResponse_TemplateData_Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

type BidResponse_Landing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 广告的点击跳转地址, H5方式打开或者ios universal link打开
	// 必须以https://开头.
	ClickThroughUrl []string `protobuf:"bytes,1,rep,name=click_through_url,json=clickThroughUrl" json:"click_through_url,omitempty"`
	// deeplink唤端链接
	DeeplinkUri *string `protobuf:"bytes,2,opt,name=deeplink_uri,json=deeplinkUri" json:"deeplink_uri,omitempty"`
	// 广告跳转的最终目标页面地址, 非点击地址
	// 必须以https://开头.
	DestinationUrl *string `protobuf:"bytes,3,opt,name=destination_url,json=destinationUrl" json:"destination_url,omitempty"`
	// 智能唤端场景使用，deeplink的唤端优先级
	AppSchemaIds *string `protobuf:"bytes,4,opt,name=app_schema_ids,json=appSchemaIds" json:"app_schema_ids,omitempty"`
	// 落地页打开方式, 默认方式无需填写，优先deeplink，其次click_through_url
	// 若需指定其他方式打开(如：ulink)，请填写
	// 1-APP内打开H5落地页, 3-deeplink, 4-Universal Link(IOS), 5-直播间打开, 6-半屏落地页(优酷聚焦贴片/快手落地页前置)
	// 7-微信小程序
	OpenType *int32 `protobuf:"varint,5,opt,name=open_type,json=openType" json:"open_type,omitempty"`
	// 只用于多落地页场景，表示每个click_through_url对应的点击区域，一般无需填写
	ClickArea []string                     `protobuf:"bytes,6,rep,name=click_area,json=clickArea" json:"click_area,omitempty"`
	MiniApp   *BidResponse_Landing_MiniApp `protobuf:"bytes,7,opt,name=mini_app,json=miniApp" json:"mini_app,omitempty"`
}

func (x *BidResponse_Landing) Reset() {
	*x = BidResponse_Landing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Landing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Landing) ProtoMessage() {}

func (x *BidResponse_Landing) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Landing.ProtoReflect.Descriptor instead.
func (*BidResponse_Landing) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 3}
}

func (x *BidResponse_Landing) GetClickThroughUrl() []string {
	if x != nil {
		return x.ClickThroughUrl
	}
	return nil
}

func (x *BidResponse_Landing) GetDeeplinkUri() string {
	if x != nil && x.DeeplinkUri != nil {
		return *x.DeeplinkUri
	}
	return ""
}

func (x *BidResponse_Landing) GetDestinationUrl() string {
	if x != nil && x.DestinationUrl != nil {
		return *x.DestinationUrl
	}
	return ""
}

func (x *BidResponse_Landing) GetAppSchemaIds() string {
	if x != nil && x.AppSchemaIds != nil {
		return *x.AppSchemaIds
	}
	return ""
}

func (x *BidResponse_Landing) GetOpenType() int32 {
	if x != nil && x.OpenType != nil {
		return *x.OpenType
	}
	return 0
}

func (x *BidResponse_Landing) GetClickArea() []string {
	if x != nil {
		return x.ClickArea
	}
	return nil
}

func (x *BidResponse_Landing) GetMiniApp() *BidResponse_Landing_MiniApp {
	if x != nil {
		return x.MiniApp
	}
	return nil
}

type BidResponse_Monitor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 曝光监测地址
	ImpTrackingUrl []string `protobuf:"bytes,1,rep,name=imp_tracking_url,json=impTrackingUrl" json:"imp_tracking_url,omitempty"`
	// 点击监测地址
	ClickTrackingUrl []string `protobuf:"bytes,2,rep,name=click_tracking_url,json=clickTrackingUrl" json:"click_tracking_url,omitempty"`
	// android平台app下载完成反馈地址
	// 仅适用于无线app h5流量
	DownloadCompleteUrl *string `protobuf:"bytes,3,opt,name=download_complete_url,json=downloadCompleteUrl" json:"download_complete_url,omitempty"`
	// 竞价胜出反馈地址
	// 仅适用于无线app流量
	WinnoticeUrl *string                           `protobuf:"bytes,4,opt,name=winnotice_url,json=winnoticeUrl" json:"winnotice_url,omitempty"`
	EventTracks  []*BidResponse_Monitor_EventTrack `protobuf:"bytes,5,rep,name=event_tracks,json=eventTracks" json:"event_tracks,omitempty"`
}

func (x *BidResponse_Monitor) Reset() {
	*x = BidResponse_Monitor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Monitor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Monitor) ProtoMessage() {}

func (x *BidResponse_Monitor) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Monitor.ProtoReflect.Descriptor instead.
func (*BidResponse_Monitor) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 4}
}

func (x *BidResponse_Monitor) GetImpTrackingUrl() []string {
	if x != nil {
		return x.ImpTrackingUrl
	}
	return nil
}

func (x *BidResponse_Monitor) GetClickTrackingUrl() []string {
	if x != nil {
		return x.ClickTrackingUrl
	}
	return nil
}

func (x *BidResponse_Monitor) GetDownloadCompleteUrl() string {
	if x != nil && x.DownloadCompleteUrl != nil {
		return *x.DownloadCompleteUrl
	}
	return ""
}

func (x *BidResponse_Monitor) GetWinnoticeUrl() string {
	if x != nil && x.WinnoticeUrl != nil {
		return *x.WinnoticeUrl
	}
	return ""
}

func (x *BidResponse_Monitor) GetEventTracks() []*BidResponse_Monitor_EventTrack {
	if x != nil {
		return x.EventTracks
	}
	return nil
}

type BidResponse_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// APP名称,UTF-8编码
	AppName *string `protobuf:"bytes,1,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	// Android的package name， iOS的Bundle ID
	PackageName *string `protobuf:"bytes,2,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// 下载地址
	DownloadUrl *string `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl" json:"download_url,omitempty"`
	// 支持的下载类型, 参照请求中的参数download_types
	DownloadType *int32 `protobuf:"varint,4,opt,name=download_type,json=downloadType" json:"download_type,omitempty"`
	// 应用商店ID, iOS下载必填
	AppStoreId *string `protobuf:"bytes,5,opt,name=app_store_id,json=appStoreId" json:"app_store_id,omitempty"`
}

func (x *BidResponse_AppInfo) Reset() {
	*x = BidResponse_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_AppInfo) ProtoMessage() {}

func (x *BidResponse_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_AppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_AppInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 5}
}

func (x *BidResponse_AppInfo) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_AppInfo) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_AppInfo) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_AppInfo) GetDownloadType() int32 {
	if x != nil && x.DownloadType != nil {
		return *x.DownloadType
	}
	return 0
}

func (x *BidResponse_AppInfo) GetAppStoreId() string {
	if x != nil && x.AppStoreId != nil {
		return *x.AppStoreId
	}
	return ""
}

type BidResponse_BizInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品线标识, 参见字典取值
	ProductId *int32 `protobuf:"varint,1,opt,name=product_id,json=productId" json:"product_id,omitempty"`
	// 业务方标识，参见字典取值
	BizId *int32 `protobuf:"varint,2,opt,name=biz_id,json=bizId" json:"biz_id,omitempty"`
	// 预算类型：1-市场，2-用增，3-商业化
	BudgetType *int32 `protobuf:"varint,3,opt,name=budget_type,json=budgetType" json:"budget_type,omitempty"`
	// 合同ID
	ContractId *string `protobuf:"bytes,4,opt,name=contract_id,json=contractId" json:"contract_id,omitempty"`
	// DSP内部的计划ID, 唯一标识内部一个计划
	CampaiginId *string `protobuf:"bytes,5,opt,name=campaigin_id,json=campaiginId" json:"campaigin_id,omitempty"`
	// 推广单元ID
	AdgroupId *string `protobuf:"bytes,6,opt,name=adgroup_id,json=adgroupId" json:"adgroup_id,omitempty"`
	// DSP内部分桶ID
	BucketIds []string `protobuf:"bytes,7,rep,name=bucket_ids,json=bucketIds" json:"bucket_ids,omitempty"`
	// 是否可以抄底投放（如直投PDB抄底）
	IsDefaultAdEnabled *bool `protobuf:"varint,8,opt,name=is_default_ad_enabled,json=isDefaultAdEnabled,def=0" json:"is_default_ad_enabled,omitempty"`
	// 产品优先级:
	ProductPriority *int32 `protobuf:"varint,9,opt,name=product_priority,json=productPriority" json:"product_priority,omitempty"`
	// 订单优先级
	OrderPriority *int32 `protobuf:"varint,10,opt,name=order_priority,json=orderPriority" json:"order_priority,omitempty"`
	// 客户优先级
	AdvPriority *int32 `protobuf:"varint,11,opt,name=adv_priority,json=advPriority" json:"adv_priority,omitempty"`
	// 算法打分
	Score *float64 `protobuf:"fixed64,12,opt,name=score" json:"score,omitempty"`
	// 广告优先级
	AdPriority *int32 `protobuf:"varint,13,opt,name=ad_priority,json=adPriority,def=0" json:"ad_priority,omitempty"`
	// 创意投放日期，优先使用campaign_date，天级曝光控制
	// begin_time和end_time如存在则只能在其区间内才可曝光
	// 仅二环开屏支持
	BeginTime *uint64 `protobuf:"varint,14,opt,name=begin_time,json=beginTime" json:"begin_time,omitempty"`
	EndTime   *uint64 `protobuf:"varint,15,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
}

// Default values for BidResponse_BizInfo fields.
const (
	Default_BidResponse_BizInfo_IsDefaultAdEnabled = bool(false)
	Default_BidResponse_BizInfo_AdPriority         = int32(0)
)

func (x *BidResponse_BizInfo) Reset() {
	*x = BidResponse_BizInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_BizInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_BizInfo) ProtoMessage() {}

func (x *BidResponse_BizInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_BizInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_BizInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 6}
}

func (x *BidResponse_BizInfo) GetProductId() int32 {
	if x != nil && x.ProductId != nil {
		return *x.ProductId
	}
	return 0
}

func (x *BidResponse_BizInfo) GetBizId() int32 {
	if x != nil && x.BizId != nil {
		return *x.BizId
	}
	return 0
}

func (x *BidResponse_BizInfo) GetBudgetType() int32 {
	if x != nil && x.BudgetType != nil {
		return *x.BudgetType
	}
	return 0
}

func (x *BidResponse_BizInfo) GetContractId() string {
	if x != nil && x.ContractId != nil {
		return *x.ContractId
	}
	return ""
}

func (x *BidResponse_BizInfo) GetCampaiginId() string {
	if x != nil && x.CampaiginId != nil {
		return *x.CampaiginId
	}
	return ""
}

func (x *BidResponse_BizInfo) GetAdgroupId() string {
	if x != nil && x.AdgroupId != nil {
		return *x.AdgroupId
	}
	return ""
}

func (x *BidResponse_BizInfo) GetBucketIds() []string {
	if x != nil {
		return x.BucketIds
	}
	return nil
}

func (x *BidResponse_BizInfo) GetIsDefaultAdEnabled() bool {
	if x != nil && x.IsDefaultAdEnabled != nil {
		return *x.IsDefaultAdEnabled
	}
	return Default_BidResponse_BizInfo_IsDefaultAdEnabled
}

func (x *BidResponse_BizInfo) GetProductPriority() int32 {
	if x != nil && x.ProductPriority != nil {
		return *x.ProductPriority
	}
	return 0
}

func (x *BidResponse_BizInfo) GetOrderPriority() int32 {
	if x != nil && x.OrderPriority != nil {
		return *x.OrderPriority
	}
	return 0
}

func (x *BidResponse_BizInfo) GetAdvPriority() int32 {
	if x != nil && x.AdvPriority != nil {
		return *x.AdvPriority
	}
	return 0
}

func (x *BidResponse_BizInfo) GetScore() float64 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *BidResponse_BizInfo) GetAdPriority() int32 {
	if x != nil && x.AdPriority != nil {
		return *x.AdPriority
	}
	return Default_BidResponse_BizInfo_AdPriority
}

func (x *BidResponse_BizInfo) GetBeginTime() uint64 {
	if x != nil && x.BeginTime != nil {
		return *x.BeginTime
	}
	return 0
}

func (x *BidResponse_BizInfo) GetEndTime() uint64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

type BidResponse_UserQuality struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户评估分类：1-用户质量评估
	Type *int32 `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	// 用户评估分值
	Quality *float64 `protobuf:"fixed64,2,opt,name=quality" json:"quality,omitempty"`
}

func (x *BidResponse_UserQuality) Reset() {
	*x = BidResponse_UserQuality{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_UserQuality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_UserQuality) ProtoMessage() {}

func (x *BidResponse_UserQuality) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_UserQuality.ProtoReflect.Descriptor instead.
func (*BidResponse_UserQuality) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 7}
}

func (x *BidResponse_UserQuality) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidResponse_UserQuality) GetQuality() float64 {
	if x != nil && x.Quality != nil {
		return *x.Quality
	}
	return 0
}

// 用户数据
type BidResponse_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// DSP内部的user id
	UserId *string `protobuf:"bytes,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
}

func (x *BidResponse_UserInfo) Reset() {
	*x = BidResponse_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_UserInfo) ProtoMessage() {}

func (x *BidResponse_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_UserInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_UserInfo) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 8}
}

func (x *BidResponse_UserInfo) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

// 模板字段信息
type BidResponse_TemplateData_Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模板中创意元素的属性名, 如：title, 参见文档 native-template.pdf
	Name *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	// 属性值
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *BidResponse_TemplateData_Field) Reset() {
	*x = BidResponse_TemplateData_Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_TemplateData_Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_TemplateData_Field) ProtoMessage() {}

func (x *BidResponse_TemplateData_Field) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_TemplateData_Field.ProtoReflect.Descriptor instead.
func (*BidResponse_TemplateData_Field) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 2, 0}
}

func (x *BidResponse_TemplateData_Field) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidResponse_TemplateData_Field) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// 小程序相关
type BidResponse_Landing_MiniApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 小程序所在的主APP客户端，1-微信，表示调用微信SDK唤起微信小程序
	HostApp *int32 `protobuf:"varint,1,req,name=host_app,json=hostApp" json:"host_app,omitempty"`
	// 主APP内的小程序ID
	AppId *string `protobuf:"bytes,2,req,name=app_id,json=appId" json:"app_id,omitempty"`
	// 小程序path
	Path *string `protobuf:"bytes,3,req,name=path" json:"path,omitempty"`
}

func (x *BidResponse_Landing_MiniApp) Reset() {
	*x = BidResponse_Landing_MiniApp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Landing_MiniApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Landing_MiniApp) ProtoMessage() {}

func (x *BidResponse_Landing_MiniApp) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Landing_MiniApp.ProtoReflect.Descriptor instead.
func (*BidResponse_Landing_MiniApp) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 3, 0}
}

func (x *BidResponse_Landing_MiniApp) GetHostApp() int32 {
	if x != nil && x.HostApp != nil {
		return *x.HostApp
	}
	return 0
}

func (x *BidResponse_Landing_MiniApp) GetAppId() string {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return ""
}

func (x *BidResponse_Landing_MiniApp) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

// 其他事件监测url
type BidResponse_Monitor_EventTrack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type定义
	// 1:播放开始监测;
	// 2:播放中间监测;
	// 3:播放完毕监测;
	// 4:主动播放开始监测;
	// 5:用户互动数据监测;
	// 6:曝光监测;
	// 7:点击监测;
	Type *int32 `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	// 监测链接
	Url []string `protobuf:"bytes,2,rep,name=url" json:"url,omitempty"`
	// 此事件后触发监测，相对时间，单位：秒
	Time *int32 `protobuf:"varint,3,opt,name=time" json:"time,omitempty"`
	// 监测发送方式
	// 0-代表API监测方式，由播放器或客户端直接发出U；
	// 1-代表MMA-SDK监测方式，客户端调起MMA-SDK，由MMA-SDK发出U；
	SendType *int32 `protobuf:"varint,4,opt,name=send_type,json=sendType" json:"send_type,omitempty"`
}

func (x *BidResponse_Monitor_EventTrack) Reset() {
	*x = BidResponse_Monitor_EventTrack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tanx_1_0_0_7_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Monitor_EventTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Monitor_EventTrack) ProtoMessage() {}

func (x *BidResponse_Monitor_EventTrack) ProtoReflect() protoreflect.Message {
	mi := &file_tanx_1_0_0_7_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Monitor_EventTrack.ProtoReflect.Descriptor instead.
func (*BidResponse_Monitor_EventTrack) Descriptor() ([]byte, []int) {
	return file_tanx_1_0_0_7_proto_rawDescGZIP(), []int{1, 4, 0}
}

func (x *BidResponse_Monitor_EventTrack) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidResponse_Monitor_EventTrack) GetUrl() []string {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *BidResponse_Monitor_EventTrack) GetTime() int32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

func (x *BidResponse_Monitor_EventTrack) GetSendType() int32 {
	if x != nil && x.SendType != nil {
		return *x.SendType
	}
	return 0
}

var File_tanx_1_0_0_7_proto protoreflect.FileDescriptor

var file_tanx_1_0_0_7_proto_rawDesc = []byte{
	0x0a, 0x12, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x31, 0x2e, 0x30, 0x2e, 0x30, 0x2e, 0x37, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x22,
	0xe3, 0x24, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6d, 0x70, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d,
	0x70, 0x52, 0x04, 0x69, 0x6d, 0x70, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74,
	0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52,
	0x03, 0x61, 0x70, 0x70, 0x12, 0x37, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73, 0x54,
	0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x69, 0x73, 0x50,
	0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x09,
	0x69, 0x73, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1c, 0x0a, 0x08, 0x61, 0x64, 0x78,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x07,
	0x61, 0x64, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x0e, 0x68, 0x74, 0x74, 0x70, 0x73,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x3a,
	0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0d, 0x68, 0x74, 0x74, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74,
	0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c,
	0x6d, 0x61, 0x78, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x42, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x65, 0x78, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x45, 0x78, 0x70, 0x49,
	0x64, 0x73, 0x1a, 0xb7, 0x0f, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03,
	0x70, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e,
	0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x35, 0x0a, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61,
	0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x12, 0x1f, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x64, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x31, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x41,
	0x64, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x70, 0x6d, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e,
	0x43, 0x70, 0x6d, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x5f,
	0x63, 0x70, 0x63, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6d, 0x69, 0x6e, 0x43, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x15,
	0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c,
	0x73, 0x65, 0x52, 0x14, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61,
	0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x05, 0x64, 0x65,
	0x61, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e,
	0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x59, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x61,
	0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65,
	0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x4b, 0x0a,
	0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x18, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x1a, 0xc6, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x6c,
	0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x61,
	0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x3d, 0x0a, 0x1b, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x18, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69,
	0x6e, 0x5f, 0x61, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x41, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x64, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78,
	0x41, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x6c, 0x0a, 0x08, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x95, 0x02, 0x0a, 0x04, 0x44, 0x65, 0x61,
	0x6c, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x04, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x09, 0x64, 0x65,
	0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x2e, 0x44, 0x65,
	0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x76, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x06, 0x61, 0x64, 0x76, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x3a, 0x0a, 0x15, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x14, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x43, 0x0a, 0x08, 0x44,
	0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x52, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x5f, 0x41, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x41, 0x4c, 0x10, 0x02,
	0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x42, 0x55, 0x59, 0x10, 0x03,
	0x1a, 0x26, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x1a, 0x40, 0x0a, 0x08, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x1a, 0xff, 0x01, 0x0a, 0x0c, 0x45,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x14, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x3d, 0x0a, 0x1b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x18, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x54, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x55, 0x72, 0x6c, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x61, 0x64, 0x76, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04,
	0x52, 0x06, 0x61, 0x64, 0x76, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x64, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x61,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x0b, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x1a, 0x5c, 0x0a, 0x04,
	0x53, 0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x1a, 0x72, 0x0a, 0x03, 0x41, 0x70,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x1a, 0x98,
	0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x29, 0x0a, 0x10, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x1a, 0xc8, 0x07, 0x0a, 0x06, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x32, 0x0a, 0x03, 0x67,
	0x65, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69,
	0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64,
	0x35, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x61,
	0x69, 0x64, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x41, 0x49, 0x44, 0x52, 0x05, 0x63, 0x61,
	0x69, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x75, 0x64, 0x69, 0x64,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x55, 0x64, 0x69, 0x64,
	0x1a, 0xb3, 0x01, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x2c, 0x0a, 0x04, 0x43, 0x41, 0x49, 0x44, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x61, 0x69, 0x64, 0x1a, 0x99, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x72, 0x72, 0x69,
	0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x6f, 0x6f, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x63, 0x68, 0x6f, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6d, 0x70, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x64, 0x6d, 0x70, 0x49, 0x64, 0x73,
	0x1a, 0x92, 0x03, 0x0a, 0x0b, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x74, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x5f,
	0x63, 0x6e, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6f, 0x6b, 0x69,
	0x65, 0x43, 0x6e, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x74, 0x64, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x74, 0x64, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x6c, 0x69, 0x5f, 0x61, 0x61, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x6c, 0x69, 0x41, 0x61, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x6f, 0x62, 0x61,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x6f, 0x62,
	0x61, 0x6f, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x6f, 0x62, 0x61, 0x6f, 0x5f, 0x6e,
	0x69, 0x63, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x6f, 0x62, 0x61,
	0x6f, 0x4e, 0x69, 0x63, 0x6b, 0x12, 0x35, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x11, 0x69, 0x73, 0x52, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x69, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x69, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x69, 0x64, 0x5f, 0x6f, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x69, 0x64, 0x4f,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x84, 0x18, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x52, 0x08, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0e,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x8d, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x62, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x04, 0x62, 0x69, 0x64, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x62, 0x61, 0x63, 0x6b, 0x75,
	0x70, 0x5f, 0x62, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74,
	0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x42, 0x69, 0x64, 0x73, 0x1a, 0xe5, 0x07, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21,
	0x0a, 0x0c, 0x68, 0x74, 0x6d, 0x6c, 0x5f, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x74, 0x6d, 0x6c, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65,
	0x74, 0x12, 0x48, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x73, 0x75, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x07,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x6c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x0a, 0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x07, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x12, 0x39, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6d,
	0x61, 0x78, 0x5f, 0x63, 0x70, 0x6d, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x43, 0x70, 0x6d, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x70, 0x63, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x43, 0x70, 0x63, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x64, 0x76, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x61, 0x64,
	0x76, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e,
	0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x62, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x39, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x7a,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a,
	0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77,
	0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x51,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2e, 0x0a, 0x07,
	0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x10, 0x02, 0x1a, 0xa5, 0x01, 0x0a,
	0x0c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x04, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x1a, 0x31, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x1a, 0xf7, 0x02, 0x0a, 0x07, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67,
	0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x63, 0x6b, 0x54, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x69, 0x12,
	0x27, 0x0a, 0x0f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x70, 0x70, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x49, 0x64, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x41, 0x72, 0x65, 0x61, 0x12, 0x41, 0x0a, 0x08, 0x6d, 0x69,
	0x6e, 0x69, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74,
	0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x69, 0x6e,
	0x69, 0x41, 0x70, 0x70, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x1a, 0x4f, 0x0a,
	0x07, 0x4d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x61, 0x70, 0x70, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x07, 0x68, 0x6f, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x1a, 0xed,
	0x02, 0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6d,
	0x70, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6d, 0x70, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x55,
	0x72, 0x6c, 0x12, 0x32, 0x0a, 0x15, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77,
	0x69, 0x6e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x4c, 0x0a, 0x0c, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x0b, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x1a, 0x63, 0x0a, 0x0a, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xb1,
	0x01, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x64, 0x1a, 0x85, 0x04, 0x0a, 0x07, 0x42, 0x69, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x62,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x61, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x12, 0x69,
	0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x25, 0x0a, 0x0e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x76, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x64, 0x76, 0x50, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0b,
	0x61, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x61, 0x64, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x3b, 0x0a, 0x0b, 0x55, 0x73,
	0x65, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x1a, 0x23, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x2a, 0x2f, 0x0a, 0x0b,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x52, 0x49, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x50, 0x4d, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x52, 0x49, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x50, 0x43, 0x10, 0x02, 0x32, 0x44, 0x0a,
	0x0a, 0x42, 0x69, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x03, 0x42,
	0x69, 0x64, 0x12, 0x15, 0x2e, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x74, 0x61, 0x6e, 0x78,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x1a, 0x5a, 0x15, 0x6d, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f,
	0x70, 0x62, 0x2f, 0x74, 0x61, 0x6e, 0x78, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x80, 0x01, 0x01,
}

var (
	file_tanx_1_0_0_7_proto_rawDescOnce sync.Once
	file_tanx_1_0_0_7_proto_rawDescData = file_tanx_1_0_0_7_proto_rawDesc
)

func file_tanx_1_0_0_7_proto_rawDescGZIP() []byte {
	file_tanx_1_0_0_7_proto_rawDescOnce.Do(func() {
		file_tanx_1_0_0_7_proto_rawDescData = protoimpl.X.CompressGZIP(file_tanx_1_0_0_7_proto_rawDescData)
	})
	return file_tanx_1_0_0_7_proto_rawDescData
}

var file_tanx_1_0_0_7_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_tanx_1_0_0_7_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_tanx_1_0_0_7_proto_goTypes = []interface{}{
	(PricingType)(0),                        // 0: tanx_down.PricingType
	(BidRequest_Imp_Deal_DealType)(0),       // 1: tanx_down.BidRequest.Imp.Deal.DealType
	(BidResponse_Bid_BidType)(0),            // 2: tanx_down.BidResponse.Bid.BidType
	(*BidRequest)(nil),                      // 3: tanx_down.BidRequest
	(*BidResponse)(nil),                     // 4: tanx_down.BidResponse
	(*BidRequest_Imp)(nil),                  // 5: tanx_down.BidRequest.Imp
	(*BidRequest_Site)(nil),                 // 6: tanx_down.BidRequest.Site
	(*BidRequest_App)(nil),                  // 7: tanx_down.BidRequest.App
	(*BidRequest_Content)(nil),              // 8: tanx_down.BidRequest.Content
	(*BidRequest_Device)(nil),               // 9: tanx_down.BidRequest.Device
	(*BidRequest_User)(nil),                 // 10: tanx_down.BidRequest.User
	(*BidRequest_PrivateInfo)(nil),          // 11: tanx_down.BidRequest.PrivateInfo
	(*BidRequest_Imp_Video)(nil),            // 12: tanx_down.BidRequest.Imp.Video
	(*BidRequest_Imp_Template)(nil),         // 13: tanx_down.BidRequest.Imp.Template
	(*BidRequest_Imp_Deal)(nil),             // 14: tanx_down.BidRequest.Imp.Deal
	(*BidRequest_Imp_ProposedCreative)(nil), // 15: tanx_down.BidRequest.Imp.ProposedCreative
	(*BidRequest_Imp_ItemInfo)(nil),         // 16: tanx_down.BidRequest.Imp.ItemInfo
	(*BidRequest_Imp_ExcludedInfo)(nil),     // 17: tanx_down.BidRequest.Imp.ExcludedInfo
	(*BidRequest_Device_Geo)(nil),           // 18: tanx_down.BidRequest.Device.Geo
	(*BidRequest_Device_CAID)(nil),          // 19: tanx_down.BidRequest.Device.CAID
	(*BidResponse_SeatBid)(nil),             // 20: tanx_down.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),                 // 21: tanx_down.BidResponse.Bid
	(*BidResponse_TemplateData)(nil),        // 22: tanx_down.BidResponse.TemplateData
	(*BidResponse_Landing)(nil),             // 23: tanx_down.BidResponse.Landing
	(*BidResponse_Monitor)(nil),             // 24: tanx_down.BidResponse.Monitor
	(*BidResponse_AppInfo)(nil),             // 25: tanx_down.BidResponse.AppInfo
	(*BidResponse_BizInfo)(nil),             // 26: tanx_down.BidResponse.BizInfo
	(*BidResponse_UserQuality)(nil),         // 27: tanx_down.BidResponse.UserQuality
	(*BidResponse_UserInfo)(nil),            // 28: tanx_down.BidResponse.UserInfo
	(*BidResponse_TemplateData_Field)(nil),  // 29: tanx_down.BidResponse.TemplateData.Field
	(*BidResponse_Landing_MiniApp)(nil),     // 30: tanx_down.BidResponse.Landing.MiniApp
	(*BidResponse_Monitor_EventTrack)(nil),  // 31: tanx_down.BidResponse.Monitor.EventTrack
}
var file_tanx_1_0_0_7_proto_depIdxs = []int32{
	5,  // 0: tanx_down.BidRequest.imps:type_name -> tanx_down.BidRequest.Imp
	6,  // 1: tanx_down.BidRequest.site:type_name -> tanx_down.BidRequest.Site
	7,  // 2: tanx_down.BidRequest.app:type_name -> tanx_down.BidRequest.App
	8,  // 3: tanx_down.BidRequest.content:type_name -> tanx_down.BidRequest.Content
	9,  // 4: tanx_down.BidRequest.device:type_name -> tanx_down.BidRequest.Device
	10, // 5: tanx_down.BidRequest.user:type_name -> tanx_down.BidRequest.User
	11, // 6: tanx_down.BidRequest.private_info:type_name -> tanx_down.BidRequest.PrivateInfo
	20, // 7: tanx_down.BidResponse.seat_bids:type_name -> tanx_down.BidResponse.SeatBid
	28, // 8: tanx_down.BidResponse.user_info:type_name -> tanx_down.BidResponse.UserInfo
	12, // 9: tanx_down.BidRequest.Imp.video:type_name -> tanx_down.BidRequest.Imp.Video
	13, // 10: tanx_down.BidRequest.Imp.templates:type_name -> tanx_down.BidRequest.Imp.Template
	14, // 11: tanx_down.BidRequest.Imp.deals:type_name -> tanx_down.BidRequest.Imp.Deal
	0,  // 12: tanx_down.BidRequest.Imp.pricing_type:type_name -> tanx_down.PricingType
	15, // 13: tanx_down.BidRequest.Imp.proposed_creatives:type_name -> tanx_down.BidRequest.Imp.ProposedCreative
	16, // 14: tanx_down.BidRequest.Imp.item_infos:type_name -> tanx_down.BidRequest.Imp.ItemInfo
	17, // 15: tanx_down.BidRequest.Imp.excluded_info:type_name -> tanx_down.BidRequest.Imp.ExcludedInfo
	18, // 16: tanx_down.BidRequest.Device.geo:type_name -> tanx_down.BidRequest.Device.Geo
	19, // 17: tanx_down.BidRequest.Device.caids:type_name -> tanx_down.BidRequest.Device.CAID
	1,  // 18: tanx_down.BidRequest.Imp.Deal.deal_type:type_name -> tanx_down.BidRequest.Imp.Deal.DealType
	21, // 19: tanx_down.BidResponse.SeatBid.bids:type_name -> tanx_down.BidResponse.Bid
	21, // 20: tanx_down.BidResponse.SeatBid.backup_bids:type_name -> tanx_down.BidResponse.Bid
	22, // 21: tanx_down.BidResponse.Bid.template_data:type_name -> tanx_down.BidResponse.TemplateData
	22, // 22: tanx_down.BidResponse.Bid.sub_template_data:type_name -> tanx_down.BidResponse.TemplateData
	23, // 23: tanx_down.BidResponse.Bid.landing:type_name -> tanx_down.BidResponse.Landing
	24, // 24: tanx_down.BidResponse.Bid.monitor:type_name -> tanx_down.BidResponse.Monitor
	0,  // 25: tanx_down.BidResponse.Bid.pricing_type:type_name -> tanx_down.PricingType
	2,  // 26: tanx_down.BidResponse.Bid.bid_type:type_name -> tanx_down.BidResponse.Bid.BidType
	25, // 27: tanx_down.BidResponse.Bid.app_info:type_name -> tanx_down.BidResponse.AppInfo
	26, // 28: tanx_down.BidResponse.Bid.biz_info:type_name -> tanx_down.BidResponse.BizInfo
	27, // 29: tanx_down.BidResponse.Bid.user_qualities:type_name -> tanx_down.BidResponse.UserQuality
	29, // 30: tanx_down.BidResponse.TemplateData.fields:type_name -> tanx_down.BidResponse.TemplateData.Field
	30, // 31: tanx_down.BidResponse.Landing.mini_app:type_name -> tanx_down.BidResponse.Landing.MiniApp
	31, // 32: tanx_down.BidResponse.Monitor.event_tracks:type_name -> tanx_down.BidResponse.Monitor.EventTrack
	3,  // 33: tanx_down.BidService.Bid:input_type -> tanx_down.BidRequest
	4,  // 34: tanx_down.BidService.Bid:output_type -> tanx_down.BidResponse
	34, // [34:35] is the sub-list for method output_type
	33, // [33:34] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_tanx_1_0_0_7_proto_init() }
func file_tanx_1_0_0_7_proto_init() {
	if File_tanx_1_0_0_7_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tanx_1_0_0_7_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_PrivateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Template); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_ProposedCreative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_ItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_ExcludedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_CAID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_TemplateData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Landing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Monitor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_BizInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_UserQuality); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_TemplateData_Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Landing_MiniApp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tanx_1_0_0_7_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Monitor_EventTrack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tanx_1_0_0_7_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tanx_1_0_0_7_proto_goTypes,
		DependencyIndexes: file_tanx_1_0_0_7_proto_depIdxs,
		EnumInfos:         file_tanx_1_0_0_7_proto_enumTypes,
		MessageInfos:      file_tanx_1_0_0_7_proto_msgTypes,
	}.Build()
	File_tanx_1_0_0_7_proto = out.File
	file_tanx_1_0_0_7_proto_rawDesc = nil
	file_tanx_1_0_0_7_proto_goTypes = nil
	file_tanx_1_0_0_7_proto_depIdxs = nil
}
