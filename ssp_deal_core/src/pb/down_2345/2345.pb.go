// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: 2345.proto

package down_2345

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reqid      string          `protobuf:"bytes,1,opt,name=reqid,proto3" json:"reqid,omitempty"`                             //必填 	标识一次请求的唯一id
	ApiVersion string          `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"` //版本号，固定为2.0
	ImpList    []*Request_Imp  `protobuf:"bytes,3,rep,name=imp_list,json=impList,proto3" json:"imp_list,omitempty"`          //必填 	曝光信息, 广告位信息, 一个BidRequest可能包含多个imp对象, 至少包含一个 object的结构见imp object
	App        *Request_App    `protobuf:"bytes,4,opt,name=app,proto3" json:"app,omitempty"`                                 //选填 	APP信息, 仅当广告位出现在APP时, 才会包含APP对象 object的结构见app object
	Device     *Request_Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`                           //选填 	设备信息
	User       *Request_User   `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`                               //选填 	用户信息
	Site       *Request_Site   `protobuf:"bytes,7,opt,name=site,proto3" json:"site,omitempty"`                               //选填 	站点信息, 仅当广告位出现在web时, 才会包含site对象
	Bcat       []string        `protobuf:"bytes,8,rep,name=bcat,proto3" json:"bcat,omitempty"`                               //选填 	拒绝的广告分类
	Badv       []string        `protobuf:"bytes,9,rep,name=badv,proto3" json:"badv,omitempty"`                               //选填 	拒绝的广告主域名
	Test       int32           `protobuf:"varint,10,opt,name=test,proto3" json:"test,omitempty"`                             //选填 	测试字段, 标识是否涉及收费 0-生产模式 1-调试模式
	At         int32           `protobuf:"varint,11,opt,name=at,proto3" json:"at,omitempty"`                                 //选填 	该值固定为2 竞价类型 1-first price 2-second price plus dsp可以只处理at值为2的请求（仅rtb）
	Ext        *Request_Ext    `protobuf:"bytes,12,opt,name=ext,proto3" json:"ext,omitempty"`                                //选填 	保留字段
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetReqid() string {
	if x != nil {
		return x.Reqid
	}
	return ""
}

func (x *Request) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *Request) GetImpList() []*Request_Imp {
	if x != nil {
		return x.ImpList
	}
	return nil
}

func (x *Request) GetApp() *Request_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *Request) GetDevice() *Request_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Request) GetUser() *Request_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Request) GetSite() *Request_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *Request) GetBcat() []string {
	if x != nil {
		return x.Bcat
	}
	return nil
}

func (x *Request) GetBadv() []string {
	if x != nil {
		return x.Badv
	}
	return nil
}

func (x *Request) GetTest() int32 {
	if x != nil {
		return x.Test
	}
	return 0
}

func (x *Request) GetAt() int32 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *Request) GetExt() *Request_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resid       string            `protobuf:"bytes,1,opt,name=resid,proto3" json:"resid,omitempty"`                                  //必填 	response id, 与request id相同
	SeatBidList []*SeatBidOptions `protobuf:"bytes,2,rep,name=seat_bid_list,json=seatBidList,proto3" json:"seat_bid_list,omitempty"` //选填 广告位-竞价列表, 如果参与竞价, 必须至少包含一个seat_bid对象 object的结构
	Bidid       string            `protobuf:"bytes,3,opt,name=bidid,proto3" json:"bidid,omitempty"`                                  //选填 由DSP定义的响应id，用于 logging/tracking(日志或效果追踪)
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetResid() string {
	if x != nil {
		return x.Resid
	}
	return ""
}

func (x *Response) GetSeatBidList() []*SeatBidOptions {
	if x != nil {
		return x.SeatBidList
	}
	return nil
}

func (x *Response) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

type SeatBidOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidList []*BidOptions `protobuf:"bytes,1,rep,name=bid_list,json=bidList,proto3" json:"bid_list,omitempty"` //必填 竞价信息
	Adv     string        `protobuf:"bytes,2,opt,name=adv,proto3" json:"adv,omitempty"`                        //选填 该次竞价是代表谁参与的，一般设置成广告主id，用于logging/tracking
}

func (x *SeatBidOptions) Reset() {
	*x = SeatBidOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeatBidOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeatBidOptions) ProtoMessage() {}

func (x *SeatBidOptions) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeatBidOptions.ProtoReflect.Descriptor instead.
func (*SeatBidOptions) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{2}
}

func (x *SeatBidOptions) GetBidList() []*BidOptions {
	if x != nil {
		return x.BidList
	}
	return nil
}

func (x *SeatBidOptions) GetAdv() string {
	if x != nil {
		return x.Adv
	}
	return ""
}

type BidOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpId             string                    `protobuf:"bytes,1,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"`                                     //必填 本次竞价所关联的imp id
	Price             int32                     `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`                                                 //必填 出价 单位:分
	CreativeId        string                    `protobuf:"bytes,3,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`                      //必填 广告创意素材id, DSP需保证该id在DSP侧的唯一性
	Deal              string                    `protobuf:"bytes,4,opt,name=deal,proto3" json:"deal,omitempty"`                                                    //选填 预定的资源id, 仅pd和pdb业务使用
	DirectiveResponse *DirectiveResponseOptions `protobuf:"bytes,5,opt,name=directive_response,json=directiveResponse,proto3" json:"directive_response,omitempty"` //选填 广告创意信息, 免审核时使用, 白名单内的DSP此项必填
	IappFilter        int32                     `protobuf:"varint,6,opt,name=iapp_filter,json=iappFilter,proto3" json:"iapp_filter,omitempty"`                     //选填 0=不过滤(默认) 1=过滤已安装 2=过滤未安装
	ExtData           string                    `protobuf:"bytes,7,opt,name=ext_data,json=extData,proto3" json:"ext_data,omitempty"`                               //选填 自定义宏, 会替换竞价成功后的__EXT_DATA__宏
}

func (x *BidOptions) Reset() {
	*x = BidOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidOptions) ProtoMessage() {}

func (x *BidOptions) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidOptions.ProtoReflect.Descriptor instead.
func (*BidOptions) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{3}
}

func (x *BidOptions) GetImpId() string {
	if x != nil {
		return x.ImpId
	}
	return ""
}

func (x *BidOptions) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidOptions) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *BidOptions) GetDeal() string {
	if x != nil {
		return x.Deal
	}
	return ""
}

func (x *BidOptions) GetDirectiveResponse() *DirectiveResponseOptions {
	if x != nil {
		return x.DirectiveResponse
	}
	return nil
}

func (x *BidOptions) GetIappFilter() int32 {
	if x != nil {
		return x.IappFilter
	}
	return 0
}

func (x *BidOptions) GetExtData() string {
	if x != nil {
		return x.ExtData
	}
	return ""
}

type DirectiveResponseOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeId        string                             `protobuf:"bytes,1,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`             //必填 广告创意素材id, DSP需保证该id在DSP侧的唯一性
	AdvertiserId      int32                              `protobuf:"varint,2,opt,name=advertiser_id,json=advertiserId,proto3" json:"advertiser_id,omitempty"`      //必填 广告主id
	AdvertiserName    string                             `protobuf:"bytes,3,opt,name=advertiser_name,json=advertiserName,proto3" json:"advertiser_name,omitempty"` //必填 广告主名称
	Vocation          int32                              `protobuf:"varint,4,opt,name=vocation,proto3" json:"vocation,omitempty"`                                  //必填 广告主行业编码, 详见广告主行业编码表
	TemplateId        int32                              `protobuf:"varint,5,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`            //必填 广告模板id
	Material          *DirectiveResponseOptions_Material `protobuf:"bytes,6,opt,name=material,proto3" json:"material,omitempty"`                                   //必填 创意素材内容, 包括视频, 图片, 文本等. 需严格按照广告形式的定义顺序来指定上传
	AppInfo           *DirectiveResponseOptions_AppInfo  `protobuf:"bytes,7,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`                      //选填 app下载唤起
	Url               string                             `protobuf:"bytes,8,opt,name=url,proto3" json:"url,omitempty"`                                             //必填 落地url 支持302
	Imptk             []string                           `protobuf:"bytes,9,rep,name=imptk,proto3" json:"imptk,omitempty"`                                         //选填 曝光监测url, 最多5个
	Clktk             []string                           `protobuf:"bytes,10,rep,name=clktk,proto3" json:"clktk,omitempty"`                                        //选填 点击监测url, 最多3个
	Dstarttk          []string                           `protobuf:"bytes,11,rep,name=dstarttk,proto3" json:"dstarttk,omitempty"`                                  //选填 仅用于应用下载广告，开始下载监播地址, 如果需要该信息请与我方确认投放资源是否支持，最多3个
	Dfinishtk         []string                           `protobuf:"bytes,12,rep,name=dfinishtk,proto3" json:"dfinishtk,omitempty"`                                //选填 仅用于应用下载广告，下载完成监播地址，最多3个
	Dinstalltk        []string                           `protobuf:"bytes,13,rep,name=dinstalltk,proto3" json:"dinstalltk,omitempty"`                              //选填 仅用于应用下载广告；安装完成监播地址，最多3个
	Deeplinktk        []string                           `protobuf:"bytes,14,rep,name=deeplinktk,proto3" json:"deeplinktk,omitempty"`                              //选填 唤醒成功上报地址，当前仅用于开屏广告位，最多8个
	Dstartinstalltk   []string                           `protobuf:"bytes,15,rep,name=dstartinstalltk,proto3" json:"dstartinstalltk,omitempty"`                    //选填 仅用于应用下载广告；开始安装监播地址。如果需要该信息请与我方确认投放资源是否支持，最多3个
	Deeplinkfailedtk  []string                           `protobuf:"bytes,16,rep,name=deeplinkfailedtk,proto3" json:"deeplinkfailedtk,omitempty"`                  //选填 deeplink调起失败时上报的url列表，最多3个
	Installedtk       []string                           `protobuf:"bytes,17,rep,name=installedtk,proto3" json:"installedtk,omitempty"`                            //选填 deeplink跳转时判断应用已安装情况下上报的url列表，最多3个
	Uninstalledtk     []string                           `protobuf:"bytes,18,rep,name=uninstalledtk,proto3" json:"uninstalledtk,omitempty"`                        //选填 deeplink跳转时判断应用未安装情况下上报的url列表，最多3个
	Incentiveloadedtk []string                           `protobuf:"bytes,19,rep,name=incentiveloadedtk,proto3" json:"incentiveloadedtk,omitempty"`                //选填 仅用于激励视频广告，激励视频加载成功时统计url列表, 最多3个
	Videostarttk      []string                           `protobuf:"bytes,20,rep,name=videostarttk,proto3" json:"videostarttk,omitempty"`                          //选填 仅用于信息流视频、激励视频，视频开始播放统计url列表，最多3个
	FirstQuartiletk   []string                           `protobuf:"bytes,21,rep,name=firstQuartiletk,proto3" json:"firstQuartiletk,omitempty"`                    //选填 仅用于激励视频，视频播放至25%时统计url列表，最多3个
	Midpointtk        []string                           `protobuf:"bytes,22,rep,name=midpointtk,proto3" json:"midpointtk,omitempty"`                              //选填 仅用于激励视频，视频播放至50%时统计url列表，最多3个
	ThirdQuartiletk   []string                           `protobuf:"bytes,23,rep,name=thirdQuartiletk,proto3" json:"thirdQuartiletk,omitempty"`                    //选填 仅用于激励视频，视频播放至75%时统计url列表，最多3个
	VideoCompletetk   []string                           `protobuf:"bytes,24,rep,name=videoCompletetk,proto3" json:"videoCompletetk,omitempty"`                    //选填 仅用于信息流视频、激励视频，视频播放完成统计url列表，最多3个
	Incentiveerrortk  []string                           `protobuf:"bytes,25,rep,name=incentiveerrortk,proto3" json:"incentiveerrortk,omitempty"`                  //选填 仅用于激励视频广告，激励视频播放错误时统计url列表，最多3个
	Nurl              string                             `protobuf:"bytes,26,opt,name=nurl,proto3" json:"nurl,omitempty"`                                          //选填 竞胜通知
	Lurl              string                             `protobuf:"bytes,27,opt,name=lurl,proto3" json:"lurl,omitempty"`                                          //选填 竞败通知
}

func (x *DirectiveResponseOptions) Reset() {
	*x = DirectiveResponseOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions) ProtoMessage() {}

func (x *DirectiveResponseOptions) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4}
}

func (x *DirectiveResponseOptions) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *DirectiveResponseOptions) GetAdvertiserId() int32 {
	if x != nil {
		return x.AdvertiserId
	}
	return 0
}

func (x *DirectiveResponseOptions) GetAdvertiserName() string {
	if x != nil {
		return x.AdvertiserName
	}
	return ""
}

func (x *DirectiveResponseOptions) GetVocation() int32 {
	if x != nil {
		return x.Vocation
	}
	return 0
}

func (x *DirectiveResponseOptions) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *DirectiveResponseOptions) GetMaterial() *DirectiveResponseOptions_Material {
	if x != nil {
		return x.Material
	}
	return nil
}

func (x *DirectiveResponseOptions) GetAppInfo() *DirectiveResponseOptions_AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *DirectiveResponseOptions) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DirectiveResponseOptions) GetImptk() []string {
	if x != nil {
		return x.Imptk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetClktk() []string {
	if x != nil {
		return x.Clktk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDstarttk() []string {
	if x != nil {
		return x.Dstarttk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDfinishtk() []string {
	if x != nil {
		return x.Dfinishtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDinstalltk() []string {
	if x != nil {
		return x.Dinstalltk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDeeplinktk() []string {
	if x != nil {
		return x.Deeplinktk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDstartinstalltk() []string {
	if x != nil {
		return x.Dstartinstalltk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetDeeplinkfailedtk() []string {
	if x != nil {
		return x.Deeplinkfailedtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetInstalledtk() []string {
	if x != nil {
		return x.Installedtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetUninstalledtk() []string {
	if x != nil {
		return x.Uninstalledtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetIncentiveloadedtk() []string {
	if x != nil {
		return x.Incentiveloadedtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetVideostarttk() []string {
	if x != nil {
		return x.Videostarttk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetFirstQuartiletk() []string {
	if x != nil {
		return x.FirstQuartiletk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetMidpointtk() []string {
	if x != nil {
		return x.Midpointtk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetThirdQuartiletk() []string {
	if x != nil {
		return x.ThirdQuartiletk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetVideoCompletetk() []string {
	if x != nil {
		return x.VideoCompletetk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetIncentiveerrortk() []string {
	if x != nil {
		return x.Incentiveerrortk
	}
	return nil
}

func (x *DirectiveResponseOptions) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *DirectiveResponseOptions) GetLurl() string {
	if x != nil {
		return x.Lurl
	}
	return ""
}

type Request_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                           //必填 	标识该imp的唯一id
	SeatId      int32                  `protobuf:"varint,2,opt,name=seat_id,json=seatId,proto3" json:"seat_id,omitempty"`                    //选填 	广告位标识id, DSP先同步获取ADX资源的素材标准，然后根据本字段识别曝光资源的来源和素材标准
	DisplayList []*Request_Imp_Display `protobuf:"bytes,3,rep,name=display_list,json=displayList,proto3" json:"display_list,omitempty"`      //选填 	广告位支持的广告形式: 包含多个，代表该位置支持多种广告形式，投放方可以根据自己需要选取其中一个广告投放，返回投放广告对应的广告形式id object的结构见display object
	BidInfoList []*Request_Imp_BidInfo `protobuf:"bytes,4,rep,name=bid_info_list,json=bidInfoList,proto3" json:"bid_info_list,omitempty"`    //选填 	竞价信息，包含允许的竞价类型和底价，至少存在一个bidinfo object的结构
	DealId      []string               `protobuf:"bytes,5,rep,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`                     //选填 	直接交易标识 ID,由交易平台和 DSP 提前约定
	ActionType  []int32                `protobuf:"varint,6,rep,packed,name=action_type,json=actionType,proto3" json:"action_type,omitempty"` //支持的广告交互类型 0-无限制、1-H5、2-下载、3-唤醒、4-微信小程序、10-LBA、21-仅展示不可点击
	AdType      int32                  `protobuf:"varint,7,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`                    //选填 	广告类型: 1-开屏 2-插屏 3-信息流 4-激励视频 5-推送
}

func (x *Request_Imp) Reset() {
	*x = Request_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp) ProtoMessage() {}

func (x *Request_Imp) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp.ProtoReflect.Descriptor instead.
func (*Request_Imp) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Request_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Imp) GetSeatId() int32 {
	if x != nil {
		return x.SeatId
	}
	return 0
}

func (x *Request_Imp) GetDisplayList() []*Request_Imp_Display {
	if x != nil {
		return x.DisplayList
	}
	return nil
}

func (x *Request_Imp) GetBidInfoList() []*Request_Imp_BidInfo {
	if x != nil {
		return x.BidInfoList
	}
	return nil
}

func (x *Request_Imp) GetDealId() []string {
	if x != nil {
		return x.DealId
	}
	return nil
}

func (x *Request_Imp) GetActionType() []int32 {
	if x != nil {
		return x.ActionType
	}
	return nil
}

func (x *Request_Imp) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

type Request_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                      //必填 	APP id, 由ADX定义
	Name        string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  //选填 	APP名称
	PackageName string  `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` //选填 APP package name, 平台内唯一
	Cat         []int32 `protobuf:"varint,4,rep,packed,name=cat,proto3" json:"cat,omitempty"`                            //选填 	APP类型        需要参数测试？？？？？
	Ver         string  `protobuf:"bytes,5,opt,name=ver,proto3" json:"ver,omitempty"`                                    //选填 	APP版本号
	Keywords    string  `protobuf:"bytes,6,opt,name=keywords,proto3" json:"keywords,omitempty"`                          //选填 	逗号分隔的APP关键词
	Cid         string  `protobuf:"bytes,7,opt,name=cid,proto3" json:"cid,omitempty"`                                    //选填 	ssp对应渠道id
	Appchannel  string  `protobuf:"bytes,8,opt,name=appchannel,proto3" json:"appchannel,omitempty"`                      //选填 	app渠道id
}

func (x *Request_App) Reset() {
	*x = Request_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_App) ProtoMessage() {}

func (x *Request_App) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_App.ProtoReflect.Descriptor instead.
func (*Request_App) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Request_App) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_App) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Request_App) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Request_App) GetCat() []int32 {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *Request_App) GetVer() string {
	if x != nil {
		return x.Ver
	}
	return ""
}

func (x *Request_App) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *Request_App) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *Request_App) GetAppchannel() string {
	if x != nil {
		return x.Appchannel
	}
	return ""
}

type Request_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ua           string               `protobuf:"bytes,1,opt,name=ua,proto3" json:"ua,omitempty"`                                    //选填 	浏览器的user agent
	DeviceType   int32                `protobuf:"varint,2,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"` //选填 	设备类型 0-PC 1-手机 2-平板 3-互联网电视
	Brand        string               `protobuf:"bytes,3,opt,name=brand,proto3" json:"brand,omitempty"`                              //选填 	设备品牌(如: apple)
	Model        string               `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`                              //选填 	设备型号(如: iphone)
	Make         string               `protobuf:"bytes,5,opt,name=make,proto3" json:"make,omitempty"`                                //选填 	设备厂商(如: apple)
	Orientation  int32                `protobuf:"varint,6,opt,name=orientation,proto3" json:"orientation,omitempty"`                 // 屏幕方向 0-位置 1-竖屏 2-横屏
	HmsVersion   string               `protobuf:"bytes,7,opt,name=hmsVersion,proto3" json:"hmsVersion,omitempty"`                    // 华为机型HMS Core版本号(如: 60000306)
	AsVersion    string               `protobuf:"bytes,8,opt,name=asVersion,proto3" json:"asVersion,omitempty"`                      // 大陆厂商安卓设备AS版本号(如: 110302302)
	Os           int32                `protobuf:"varint,9,opt,name=os,proto3" json:"os,omitempty"`                                   //选填 	操作系统 0-Windows 1-MacOS 2-Linux 3-IOS 4-Android
	Osv          string               `protobuf:"bytes,10,opt,name=osv,proto3" json:"osv,omitempty"`                                 //选填 	操作系统版本号
	Density      int32                `protobuf:"varint,11,opt,name=density,proto3" json:"density,omitempty"`                        //选填 	屏幕密度, 默认为400  即将废弃
	Ip           string               `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip,omitempty"`                                   //选填 	ipv4
	Ipv6         string               `protobuf:"bytes,13,opt,name=ipv6,proto3" json:"ipv6,omitempty"`                               //选填 	ipv6
	Carrier      int32                `protobuf:"varint,14,opt,name=carrier,proto3" json:"carrier,omitempty"`                        //选填 	运营商 0-电信 1-移动 2-联通 3-网通 4-未知
	Network      int32                `protobuf:"varint,15,opt,name=network,proto3" json:"network,omitempty"`                        //选填 	网络类型 0-wifi 1-earthnet(有线网络) 2-2G 3-3G 4-4G 5-5G 6-未知
	Width        int32                `protobuf:"varint,16,opt,name=width,proto3" json:"width,omitempty"`                            //选填 	物理屏幕宽度
	Height       int32                `protobuf:"varint,17,opt,name=height,proto3" json:"height,omitempty"`                          //选填 	物理屏幕高度
	Imei         string               `protobuf:"bytes,18,opt,name=imei,proto3" json:"imei,omitempty"`                               //选填 	设备号imei
	ImeiMd5      string               `protobuf:"bytes,19,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`          //选填 设备号imei的md5
	Oaid         string               `protobuf:"bytes,20,opt,name=oaid,proto3" json:"oaid,omitempty"`                               //选填 Android Q以上版本的设备号 明文形式
	OaidMd5      string               `protobuf:"bytes,21,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`          //选填 oaid的md5
	Dpid         string               `protobuf:"bytes,22,opt,name=dpid,proto3" json:"dpid,omitempty"`                               //选填 	Android id
	DpidMd5      string               `protobuf:"bytes,23,opt,name=dpid_md5,json=dpidMd5,proto3" json:"dpid_md5,omitempty"`          //选填 	Android id的md5
	Idfa         string               `protobuf:"bytes,24,opt,name=idfa,proto3" json:"idfa,omitempty"`                               //选填 	Apple设备的idfa
	IdfaMd5      string               `protobuf:"bytes,25,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`          //选填 	Apple设备的idfa的md5
	Mac          string               `protobuf:"bytes,26,opt,name=mac,proto3" json:"mac,omitempty"`                                 //选填 	设备的mac值
	MacMd5       string               `protobuf:"bytes,27,opt,name=mac_md5,json=macMd5,proto3" json:"mac_md5,omitempty"`             //选填 设备的mac值的md5
	BootMark     string               `protobuf:"bytes,28,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`       //选填 取原值进⾏传输 iOS：1623815045.970028 Android：ec7f4f33-411a-47bc-8067-744a4e7e0723
	UpdateMark   string               `protobuf:"bytes,29,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"` //选填 取原值进⾏传输 iOS：1581141691.570419583 Android：1004697.709999999
	BirthTime    string               `protobuf:"bytes,30,opt,name=birthTime,proto3" json:"birthTime,omitempty"`                     //选填 设备初始化时间
	BootTime     string               `protobuf:"bytes,31,opt,name=bootTime,proto3" json:"bootTime,omitempty"`                       //选填 系统启动时间
	UpdateTime   string               `protobuf:"bytes,32,opt,name=updateTime,proto3" json:"updateTime,omitempty"`                   //选填 系统更新时间
	Idfv         string               `protobuf:"bytes,33,opt,name=idfv,proto3" json:"idfv,omitempty"`                               //选填 苹果开发者标识
	Paid         string               `protobuf:"bytes,34,opt,name=paid,proto3" json:"paid,omitempty"`                               //选填 拼多多版caid，仅iOS
	Caid         *Request_Device_Caid `protobuf:"bytes,35,opt,name=caid,proto3" json:"caid,omitempty"`
	Geo          *Request_Device_Geo  `protobuf:"bytes,36,opt,name=geo,proto3" json:"geo,omitempty"`                    //选填 	地域信息 object的结构见geo object
	Ppi          int32                `protobuf:"varint,37,opt,name=ppi,proto3" json:"ppi,omitempty"`                   //选填 	像素密度，表示每英寸像素点
	PxRatio      float32              `protobuf:"fixed32,38,opt,name=pxRatio,proto3" json:"pxRatio,omitempty"`          //选填 屏幕密度
	Aaid         string               `protobuf:"bytes,39,opt,name=aaid,proto3" json:"aaid,omitempty"`                  //选填 阿里集团内推出的匿名广告标识符，格式示例：CD7D878A870C-97D4-89A4-3EB3-D48AF066
	ElapseTime   string               `protobuf:"bytes,40,opt,name=elapseTime,proto3" json:"elapseTime,omitempty"`      //选填 开机时长
	SysCpuNum    int32                `protobuf:"varint,41,opt,name=sysCpuNum,proto3" json:"sysCpuNum,omitempty"`       //选填  设备cpu数量
	BatteryState int32                `protobuf:"varint,42,opt,name=batteryState,proto3" json:"batteryState,omitempty"` //设备当前充电状态 1:未知状态，2:正在充电，3 放电 4:未充满，5:满状态
	Battery      int32                `protobuf:"varint,43,opt,name=battery,proto3" json:"battery,omitempty"`           //选填  设备电量
	RomVersion   string               `protobuf:"bytes,44,opt,name=romVersion,proto3" json:"romVersion,omitempty"`      //选填 系统rom版本
	SdFreeSpace  string               `protobuf:"bytes,45,opt,name=sdFreeSpace,proto3" json:"sdFreeSpace,omitempty"`    //选填 磁盘剩余空间
}

func (x *Request_Device) Reset() {
	*x = Request_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device) ProtoMessage() {}

func (x *Request_Device) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device.ProtoReflect.Descriptor instead.
func (*Request_Device) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Request_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *Request_Device) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *Request_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Request_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Request_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *Request_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *Request_Device) GetHmsVersion() string {
	if x != nil {
		return x.HmsVersion
	}
	return ""
}

func (x *Request_Device) GetAsVersion() string {
	if x != nil {
		return x.AsVersion
	}
	return ""
}

func (x *Request_Device) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *Request_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *Request_Device) GetDensity() int32 {
	if x != nil {
		return x.Density
	}
	return 0
}

func (x *Request_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Request_Device) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *Request_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *Request_Device) GetNetwork() int32 {
	if x != nil {
		return x.Network
	}
	return 0
}

func (x *Request_Device) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Request_Device) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Request_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *Request_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *Request_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *Request_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *Request_Device) GetDpid() string {
	if x != nil {
		return x.Dpid
	}
	return ""
}

func (x *Request_Device) GetDpidMd5() string {
	if x != nil {
		return x.DpidMd5
	}
	return ""
}

func (x *Request_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *Request_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *Request_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *Request_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *Request_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *Request_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *Request_Device) GetBirthTime() string {
	if x != nil {
		return x.BirthTime
	}
	return ""
}

func (x *Request_Device) GetBootTime() string {
	if x != nil {
		return x.BootTime
	}
	return ""
}

func (x *Request_Device) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Request_Device) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *Request_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *Request_Device) GetCaid() *Request_Device_Caid {
	if x != nil {
		return x.Caid
	}
	return nil
}

func (x *Request_Device) GetGeo() *Request_Device_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *Request_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *Request_Device) GetPxRatio() float32 {
	if x != nil {
		return x.PxRatio
	}
	return 0
}

func (x *Request_Device) GetAaid() string {
	if x != nil {
		return x.Aaid
	}
	return ""
}

func (x *Request_Device) GetElapseTime() string {
	if x != nil {
		return x.ElapseTime
	}
	return ""
}

func (x *Request_Device) GetSysCpuNum() int32 {
	if x != nil {
		return x.SysCpuNum
	}
	return 0
}

func (x *Request_Device) GetBatteryState() int32 {
	if x != nil {
		return x.BatteryState
	}
	return 0
}

func (x *Request_Device) GetBattery() int32 {
	if x != nil {
		return x.Battery
	}
	return 0
}

func (x *Request_Device) GetRomVersion() string {
	if x != nil {
		return x.RomVersion
	}
	return ""
}

func (x *Request_Device) GetSdFreeSpace() string {
	if x != nil {
		return x.SdFreeSpace
	}
	return ""
}

type Request_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`             //选填 	由ADX定义的用户id
	Yob      int32  `protobuf:"varint,2,opt,name=yob,proto3" json:"yob,omitempty"`          //选填 	出生年份 4位数字
	Gender   string `protobuf:"bytes,3,opt,name=gender,proto3" json:"gender,omitempty"`     //选填 	性别 M-男 F-女 默认-未知
	Keywords string `protobuf:"bytes,4,opt,name=keywords,proto3" json:"keywords,omitempty"` //选填 	逗号分隔得用户关键词或兴趣点
	Wuid     string `protobuf:"bytes,5,opt,name=wuid,proto3" json:"wuid,omitempty"`         //选填 	逗号分隔得用户关键词或兴趣点
}

func (x *Request_User) Reset() {
	*x = Request_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_User) ProtoMessage() {}

func (x *Request_User) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_User.ProtoReflect.Descriptor instead.
func (*Request_User) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Request_User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_User) GetYob() int32 {
	if x != nil {
		return x.Yob
	}
	return 0
}

func (x *Request_User) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Request_User) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *Request_User) GetWuid() string {
	if x != nil {
		return x.Wuid
	}
	return ""
}

type Request_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            //必填 	站点id, 由ADX定义的site id
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`         //选填 	站点名称
	Domain   string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`     //选填 站点域名
	Url      string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`           //选填 	当前页面的url
	Ref      string `protobuf:"bytes,5,opt,name=ref,proto3" json:"ref,omitempty"`           //选填 	当前页面的referer url
	Keywords string `protobuf:"bytes,6,opt,name=keywords,proto3" json:"keywords,omitempty"` //选填 	逗号分隔的页面关键词
}

func (x *Request_Site) Reset() {
	*x = Request_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Site) ProtoMessage() {}

func (x *Request_Site) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Site.ProtoReflect.Descriptor instead.
func (*Request_Site) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Request_Site) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Request_Site) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Request_Site) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *Request_Site) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Request_Site) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *Request_Site) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

type Request_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IappList string `protobuf:"bytes,1,opt,name=iapp_list,json=iappList,proto3" json:"iapp_list,omitempty"` //需要测试与完善扩展结构
}

func (x *Request_Ext) Reset() {
	*x = Request_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Ext) ProtoMessage() {}

func (x *Request_Ext) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Ext.ProtoReflect.Descriptor instead.
func (*Request_Ext) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Request_Ext) GetIappList() string {
	if x != nil {
		return x.IappList
	}
	return ""
}

type Request_Imp_Display struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId int32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"` //必填 	广告模板id
	Width      int32 `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`                             //必填 	广告位宽度
	Height     int32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`                           //必填 	广告位高度
}

func (x *Request_Imp_Display) Reset() {
	*x = Request_Imp_Display{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_Display) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_Display) ProtoMessage() {}

func (x *Request_Imp_Display) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_Display.ProtoReflect.Descriptor instead.
func (*Request_Imp_Display) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *Request_Imp_Display) GetTemplateId() int32 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *Request_Imp_Display) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Request_Imp_Display) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type Request_Imp_BidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidType  int32   `protobuf:"varint,1,opt,name=bid_type,json=bidType,proto3" json:"bid_type,omitempty"`     //必填 	允许的竞价类型 0-CPM
	BidFloor float32 `protobuf:"fixed32,2,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"` //选填 	可接受的底价 单位:分/千次曝光
}

func (x *Request_Imp_BidInfo) Reset() {
	*x = Request_Imp_BidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Imp_BidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Imp_BidInfo) ProtoMessage() {}

func (x *Request_Imp_BidInfo) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Imp_BidInfo.ProtoReflect.Descriptor instead.
func (*Request_Imp_BidInfo) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *Request_Imp_BidInfo) GetBidType() int32 {
	if x != nil {
		return x.BidType
	}
	return 0
}

func (x *Request_Imp_BidInfo) GetBidFloor() float32 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

type Request_Device_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                       // caid 原值
	BootTimeInSec string `protobuf:"bytes,2,opt,name=bootTimeInSec,proto3" json:"bootTimeInSec,omitempty"` // 设备启动时间
	CountryCode   string `protobuf:"bytes,3,opt,name=countryCode,proto3" json:"countryCode,omitempty"`     // 国家
	Language      string `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`           // 语言
	DeviceName    string `protobuf:"bytes,5,opt,name=deviceName,proto3" json:"deviceName,omitempty"`       // 设备名称
	Model         string `protobuf:"bytes,6,opt,name=model,proto3" json:"model,omitempty"`                 // 设备型号
	SystemVersion string `protobuf:"bytes,7,opt,name=systemVersion,proto3" json:"systemVersion,omitempty"` // 系统版本
	Machine       string `protobuf:"bytes,8,opt,name=machine,proto3" json:"machine,omitempty"`             // 设备 machine
	CarrierInfo   string `protobuf:"bytes,9,opt,name=carrierInfo,proto3" json:"carrierInfo,omitempty"`     // 运营商
	Memory        string `protobuf:"bytes,10,opt,name=memory,proto3" json:"memory,omitempty"`              // 物理内存
	Disk          string `protobuf:"bytes,11,opt,name=disk,proto3" json:"disk,omitempty"`                  // 磁盘大小
	SysFileTime   string `protobuf:"bytes,12,opt,name=sysFileTime,proto3" json:"sysFileTime,omitempty"`    //
	TimeZone      string `protobuf:"bytes,13,opt,name=timeZone,proto3" json:"timeZone,omitempty"`          // 时区
	InitTime      string `protobuf:"bytes,14,opt,name=initTime,proto3" json:"initTime,omitempty"`          // 设备初始化时间
	Version       string `protobuf:"bytes,15,opt,name=version,proto3" json:"version,omitempty"`            // caid 原值版本号
}

func (x *Request_Device_Caid) Reset() {
	*x = Request_Device_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_Caid) ProtoMessage() {}

func (x *Request_Device_Caid) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_Caid.ProtoReflect.Descriptor instead.
func (*Request_Device_Caid) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *Request_Device_Caid) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request_Device_Caid) GetBootTimeInSec() string {
	if x != nil {
		return x.BootTimeInSec
	}
	return ""
}

func (x *Request_Device_Caid) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *Request_Device_Caid) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Request_Device_Caid) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *Request_Device_Caid) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Request_Device_Caid) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *Request_Device_Caid) GetMachine() string {
	if x != nil {
		return x.Machine
	}
	return ""
}

func (x *Request_Device_Caid) GetCarrierInfo() string {
	if x != nil {
		return x.CarrierInfo
	}
	return ""
}

func (x *Request_Device_Caid) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *Request_Device_Caid) GetDisk() string {
	if x != nil {
		return x.Disk
	}
	return ""
}

func (x *Request_Device_Caid) GetSysFileTime() string {
	if x != nil {
		return x.SysFileTime
	}
	return ""
}

func (x *Request_Device_Caid) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *Request_Device_Caid) GetInitTime() string {
	if x != nil {
		return x.InitTime
	}
	return ""
}

func (x *Request_Device_Caid) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type Request_Device_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lat      float32 `protobuf:"fixed32,1,opt,name=lat,proto3" json:"lat,omitempty"`         //选填 	纬度 (-90.0 - 90.0), 负值代表南
	Lon      float32 `protobuf:"fixed32,2,opt,name=lon,proto3" json:"lon,omitempty"`         //选填 	经度 (-180.0 - 180.0), 负值代表西
	Type     int32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`        //选填 	位置来源 1-GPS定位 2-IP地址 3-用户提供(如注册信息)
	Country  string  `protobuf:"bytes,4,opt,name=country,proto3" json:"country,omitempty"`   //选填 	国家
	Province string  `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"` //选填 	国家
	City     string  `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`         //选填 	城市
}

func (x *Request_Device_Geo) Reset() {
	*x = Request_Device_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request_Device_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request_Device_Geo) ProtoMessage() {}

func (x *Request_Device_Geo) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request_Device_Geo.ProtoReflect.Descriptor instead.
func (*Request_Device_Geo) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{0, 2, 1}
}

func (x *Request_Device_Geo) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Request_Device_Geo) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *Request_Device_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Request_Device_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Request_Device_Geo) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *Request_Device_Geo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

type DirectiveResponseOptions_Material struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string                                     `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`             // 推荐 广告标题
	Description string                                     `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"` // 推荐 广告描述
	Btn         string                                     `protobuf:"bytes,3,opt,name=btn,proto3" json:"btn,omitempty"`                 // 广告按钮文字
	Brand       string                                     `protobuf:"bytes,4,opt,name=brand,proto3" json:"brand,omitempty"`             // 品牌描述
	Images      []*DirectiveResponseOptions_Material_Image `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`           // 推荐 广告图片素材，图文或纯图时必填
	Video       *DirectiveResponseOptions_Material_Video   `protobuf:"bytes,6,opt,name=video,proto3" json:"video,omitempty"`             // 推荐 广告视频素材，视频广告必填
	Icon        *DirectiveResponseOptions_Material_Image   `protobuf:"bytes,7,opt,name=icon,proto3" json:"icon,omitempty"`               // 广告 icon 图片
}

func (x *DirectiveResponseOptions_Material) Reset() {
	*x = DirectiveResponseOptions_Material{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_Material) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_Material) ProtoMessage() {}

func (x *DirectiveResponseOptions_Material) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_Material.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_Material) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DirectiveResponseOptions_Material) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DirectiveResponseOptions_Material) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DirectiveResponseOptions_Material) GetBtn() string {
	if x != nil {
		return x.Btn
	}
	return ""
}

func (x *DirectiveResponseOptions_Material) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *DirectiveResponseOptions_Material) GetImages() []*DirectiveResponseOptions_Material_Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *DirectiveResponseOptions_Material) GetVideo() *DirectiveResponseOptions_Material_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *DirectiveResponseOptions_Material) GetIcon() *DirectiveResponseOptions_Material_Image {
	if x != nil {
		return x.Icon
	}
	return nil
}

type DirectiveResponseOptions_AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductType   int32                                          `protobuf:"varint,1,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`       //必填 1-普通链接 2-android应用下载 3-ios应用下载
	AndroidUrl    string                                         `protobuf:"bytes,2,opt,name=android_url,json=androidUrl,proto3" json:"android_url,omitempty"`           //选填 安卓下载地址，下载类时android_url或 ios_url⾄少填⼀个，其它情况直接过滤为空
	IosUrl        string                                         `protobuf:"bytes,3,opt,name=ios_url,json=iosUrl,proto3" json:"ios_url,omitempty"`                       //选填 IOS下载地址，下载类选填
	Deeplink      string                                         `protobuf:"bytes,4,opt,name=deeplink,proto3" json:"deeplink,omitempty"`                                 //选填 应用直达URL，SDK客户端会对deeplink地址进行有效性校验，无效的话会自动转成非唤起的广告形式，打开落地页
	PackageName   string                                         `protobuf:"bytes,5,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`        //选填 下载应用包名 下载类广告必填
	AppName       string                                         `protobuf:"bytes,6,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                    //选填 应用名称，下载和唤醒类广告必填
	AppSize       int32                                          `protobuf:"varint,7,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`                   //选填 应用大小，单位 KB，下载类需要
	AppLogo       string                                         `protobuf:"bytes,8,opt,name=app_logo,json=appLogo,proto3" json:"app_logo,omitempty"`                    //选填 应用logo，下载和唤醒类广告必填
	Intro         string                                         `protobuf:"bytes,9,opt,name=intro,proto3" json:"intro,omitempty"`                                       // 选填 应用介绍
	Version       string                                         `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`                                  // 选填 应用版本号，下载类广告必填
	Developer     string                                         `protobuf:"bytes,11,opt,name=developer,proto3" json:"developer,omitempty"`                              // 选填 应用开发者，下载类广告必填
	Privacy       string                                         `protobuf:"bytes,12,opt,name=privacy,proto3" json:"privacy,omitempty"`                                  // 选填 应用隐私协议，下载类广告必填
	PrivacyUrl    string                                         `protobuf:"bytes,13,opt,name=privacy_url,json=privacyUrl,proto3" json:"privacy_url,omitempty"`          // 选填 应用隐私协议链接，下载类广告必填。注：下载类广告 privacy、privacy_url 任填一个即可。
	Permissions   []*DirectiveResponseOptions_AppInfo_Permission `protobuf:"bytes,14,rep,name=permissions,proto3" json:"permissions,omitempty"`                          // 应用隐私权限列表
	PermissionUrl string                                         `protobuf:"bytes,15,opt,name=permission_url,json=permissionUrl,proto3" json:"permission_url,omitempty"` // 选填 应用隐私权限Url，下载类广告必填。注：下载类广告 permission_url、function_desc 任填一个即可
	FunctionDesc  string                                         `protobuf:"bytes,16,opt,name=function_desc,json=functionDesc,proto3" json:"function_desc,omitempty"`    // 选填 产品功能介绍，下载类广告必填
	WxMiniProgram *DirectiveResponseOptions_AppInfo_MiniProgram  `protobuf:"bytes,17,opt,name=wx_miniProgram,json=wxMiniProgram,proto3" json:"wx_miniProgram,omitempty"` // 调起小程序
	UniversalLink string                                         `protobuf:"bytes,18,opt,name=universal_link,json=universalLink,proto3" json:"universal_link,omitempty"` // 选填 IOS universal_link
}

func (x *DirectiveResponseOptions_AppInfo) Reset() {
	*x = DirectiveResponseOptions_AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_AppInfo) ProtoMessage() {}

func (x *DirectiveResponseOptions_AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_AppInfo.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_AppInfo) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 1}
}

func (x *DirectiveResponseOptions_AppInfo) GetProductType() int32 {
	if x != nil {
		return x.ProductType
	}
	return 0
}

func (x *DirectiveResponseOptions_AppInfo) GetAndroidUrl() string {
	if x != nil {
		return x.AndroidUrl
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetIosUrl() string {
	if x != nil {
		return x.IosUrl
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetAppSize() int32 {
	if x != nil {
		return x.AppSize
	}
	return 0
}

func (x *DirectiveResponseOptions_AppInfo) GetAppLogo() string {
	if x != nil {
		return x.AppLogo
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetIntro() string {
	if x != nil {
		return x.Intro
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetPrivacy() string {
	if x != nil {
		return x.Privacy
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetPrivacyUrl() string {
	if x != nil {
		return x.PrivacyUrl
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetPermissions() []*DirectiveResponseOptions_AppInfo_Permission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *DirectiveResponseOptions_AppInfo) GetPermissionUrl() string {
	if x != nil {
		return x.PermissionUrl
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetFunctionDesc() string {
	if x != nil {
		return x.FunctionDesc
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo) GetWxMiniProgram() *DirectiveResponseOptions_AppInfo_MiniProgram {
	if x != nil {
		return x.WxMiniProgram
	}
	return nil
}

func (x *DirectiveResponseOptions_AppInfo) GetUniversalLink() string {
	if x != nil {
		return x.UniversalLink
	}
	return ""
}

type DirectiveResponseOptions_Material_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`        // 必填 图片地址
	Width  int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`   // 推荐 图片宽度，单位：像素
	Height int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"` // 推荐 图片高度，单位：像素
}

func (x *DirectiveResponseOptions_Material_Image) Reset() {
	*x = DirectiveResponseOptions_Material_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_Material_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_Material_Image) ProtoMessage() {}

func (x *DirectiveResponseOptions_Material_Image) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_Material_Image.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_Material_Image) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 0, 0}
}

func (x *DirectiveResponseOptions_Material_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DirectiveResponseOptions_Material_Image) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *DirectiveResponseOptions_Material_Image) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type DirectiveResponseOptions_Material_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string                                   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`            // 必填 视频地址
	Duration int32                                    `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"` // 推荐 视频时长，单位：秒
	Size     int32                                    `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`         // 选填 视频大小，单位：KB
	Cover    *DirectiveResponseOptions_Material_Image `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`        // 推荐 视频封面图片或视频后贴片图片
	Protocol int32                                    `protobuf:"varint,5,opt,name=protocol,proto3" json:"protocol,omitempty"` // 选填 视频协议类型
}

func (x *DirectiveResponseOptions_Material_Video) Reset() {
	*x = DirectiveResponseOptions_Material_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_Material_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_Material_Video) ProtoMessage() {}

func (x *DirectiveResponseOptions_Material_Video) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_Material_Video.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_Material_Video) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 0, 1}
}

func (x *DirectiveResponseOptions_Material_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DirectiveResponseOptions_Material_Video) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *DirectiveResponseOptions_Material_Video) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DirectiveResponseOptions_Material_Video) GetCover() *DirectiveResponseOptions_Material_Image {
	if x != nil {
		return x.Cover
	}
	return nil
}

func (x *DirectiveResponseOptions_Material_Video) GetProtocol() int32 {
	if x != nil {
		return x.Protocol
	}
	return 0
}

type DirectiveResponseOptions_AppInfo_Permission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"` // 必填 隐私权限标题
	Desc  string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`   // 必填 隐私权限详细描述
}

func (x *DirectiveResponseOptions_AppInfo_Permission) Reset() {
	*x = DirectiveResponseOptions_AppInfo_Permission{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_AppInfo_Permission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_AppInfo_Permission) ProtoMessage() {}

func (x *DirectiveResponseOptions_AppInfo_Permission) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_AppInfo_Permission.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_AppInfo_Permission) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 1, 0}
}

func (x *DirectiveResponseOptions_AppInfo_Permission) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo_Permission) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type DirectiveResponseOptions_AppInfo_MiniProgram struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WxUsername string `protobuf:"bytes,1,opt,name=wx_username,json=wxUsername,proto3" json:"wx_username,omitempty"`  // 必填 所需跳转的小程序原始 ID（以"gh_"开头）
	WxPath     string `protobuf:"bytes,2,opt,name=wx_path,json=wxPath,proto3" json:"wx_path,omitempty"`              // 推荐 所需跳转的小程序内页面路径及参数，不填默认拉起小程序主页
	WxMinitype uint32 `protobuf:"varint,3,opt,name=wx_minitype,json=wxMinitype,proto3" json:"wx_minitype,omitempty"` // 必填 所需跳转的小程序类型，0：正式版，1：开发版，2：体验版
}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) Reset() {
	*x = DirectiveResponseOptions_AppInfo_MiniProgram{}
	if protoimpl.UnsafeEnabled {
		mi := &file__2345_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectiveResponseOptions_AppInfo_MiniProgram) ProtoMessage() {}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) ProtoReflect() protoreflect.Message {
	mi := &file__2345_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectiveResponseOptions_AppInfo_MiniProgram.ProtoReflect.Descriptor instead.
func (*DirectiveResponseOptions_AppInfo_MiniProgram) Descriptor() ([]byte, []int) {
	return file__2345_proto_rawDescGZIP(), []int{4, 1, 1}
}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) GetWxUsername() string {
	if x != nil {
		return x.WxUsername
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) GetWxPath() string {
	if x != nil {
		return x.WxPath
	}
	return ""
}

func (x *DirectiveResponseOptions_AppInfo_MiniProgram) GetWxMinitype() uint32 {
	if x != nil {
		return x.WxMinitype
	}
	return 0
}

var File__2345_proto protoreflect.FileDescriptor

var file__2345_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x64, 0x6f,
	0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x22, 0x8b, 0x18, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x71, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x08, 0x69, 0x6d,
	0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64,
	0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x03, 0x61, 0x70, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x77,
	0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x31, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32,
	0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f,
	0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34,
	0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x74, 0x65, 0x52, 0x04,
	0x73, 0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x63, 0x61, 0x74, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x62, 0x63, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x64, 0x76,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x64, 0x76, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x61, 0x74,
	0x12, 0x28, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0xa5, 0x03, 0x0a, 0x03, 0x49,
	0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x61, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42,
	0x0a, 0x0d, 0x62, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34,
	0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x42, 0x69,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x58, 0x0a, 0x07, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a,
	0x41, 0x0a, 0x07, 0x42, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f,
	0x6f, 0x72, 0x1a, 0xbe, 0x01, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03,
	0x63, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x63, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x1a, 0xe4, 0x0d, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x1f,
	0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73,
	0x76, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x70, 0x76, 0x36, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35,
	0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x70, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x70, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x70, 0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x70, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x63, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x74,
	0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f,
	0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x64, 0x66, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x22, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33,
	0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x03,
	0x67, 0x65, 0x6f, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x6f, 0x77, 0x6e,
	0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x70, 0x70, 0x69, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x78, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x26, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x07, 0x70, 0x78, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x61, 0x69,
	0x64, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x61, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x79, 0x73, 0x43, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x29, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x73, 0x79, 0x73, 0x43, 0x70, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x62,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x6f, 0x6d,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x64, 0x46,
	0x72, 0x65, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x64, 0x46, 0x72, 0x65, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x1a, 0xb2, 0x03, 0x0a, 0x04,
	0x43, 0x61, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x49, 0x6e, 0x53, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x6f, 0x6f,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x73, 0x6b,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x79, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x1a, 0x87, 0x01, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x1a, 0x70, 0x0a, 0x04, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x79, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x79, 0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x75, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x75, 0x69, 0x64, 0x1a, 0x82, 0x01, 0x0a,
	0x04, 0x53, 0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x1a, 0x22, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x61, 0x70, 0x70,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x61, 0x70,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x75, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x65, 0x73, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x74, 0x5f,
	0x62, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x0e,
	0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30,
	0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x42, 0x69, 0x64,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x62, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x61, 0x64, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61,
	0x64, 0x76, 0x22, 0xfe, 0x01, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x61, 0x6c, 0x12, 0x52, 0x0a, 0x12, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x11, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x61, 0x70, 0x70, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x61,
	0x70, 0x70, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x89, 0x13, 0x0a, 0x18, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x76, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x76, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x08,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x08, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x46, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f,
	0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x74, 0x6b, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x6d, 0x70, 0x74, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x6b, 0x74, 0x6b, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x6b, 0x74, 0x6b, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x73, 0x74, 0x61, 0x72, 0x74, 0x74, 0x6b, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x73, 0x74, 0x61, 0x72, 0x74, 0x74, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x74, 0x6b, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x74, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x74, 0x6b, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x74, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x74, 0x6b, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x74, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x74, 0x6b, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x64, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x74, 0x6b,
	0x12, 0x2a, 0x0a, 0x10, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x74, 0x6b, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x74, 0x6b, 0x12, 0x20, 0x0a, 0x0b,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x74, 0x6b, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x74, 0x6b, 0x12, 0x24,
	0x0a, 0x0d, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x74, 0x6b, 0x18,
	0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x74, 0x6b, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76,
	0x65, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x74, 0x6b, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64,
	0x74, 0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x74, 0x6b, 0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x74, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x51,
	0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x74, 0x6b, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x51, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x74, 0x6b,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x74, 0x6b, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x74, 0x6b,
	0x12, 0x28, 0x0a, 0x0f, 0x74, 0x68, 0x69, 0x72, 0x64, 0x51, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c,
	0x65, 0x74, 0x6b, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x72, 0x64,
	0x51, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x74, 0x6b, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x74, 0x6b, 0x18, 0x18, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x74, 0x6b, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76,
	0x65, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x74, 0x6b, 0x18, 0x19, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x69, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x69, 0x76, 0x65, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x74, 0x6b,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x1a, 0xc3, 0x04, 0x0a, 0x08, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x62, 0x74, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x74, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34,
	0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x48, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x46, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x64, 0x6f, 0x77, 0x6e,
	0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x1a, 0x47, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xaf, 0x01, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x48, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32,
	0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x1a, 0xce,
	0x06, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x6f, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x69, 0x6f, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x70, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x72, 0x6f,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x55, 0x72, 0x6c,
	0x12, 0x58, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34,
	0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x5e, 0x0a, 0x0e, 0x77, 0x78, 0x5f, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x69, 0x6e, 0x69,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0d, 0x77, 0x78, 0x4d, 0x69, 0x6e, 0x69, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x1a, 0x36, 0x0a,
	0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x1a, 0x68, 0x0a, 0x0b, 0x4d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x78, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x78, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x78, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x78, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77, 0x78, 0x4d, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0e, 0x5a, 0x0c, 0x2e, 0x2e, 0x2f, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x32, 0x33, 0x34, 0x35, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file__2345_proto_rawDescOnce sync.Once
	file__2345_proto_rawDescData = file__2345_proto_rawDesc
)

func file__2345_proto_rawDescGZIP() []byte {
	file__2345_proto_rawDescOnce.Do(func() {
		file__2345_proto_rawDescData = protoimpl.X.CompressGZIP(file__2345_proto_rawDescData)
	})
	return file__2345_proto_rawDescData
}

var file__2345_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file__2345_proto_goTypes = []interface{}{
	(*Request)(nil),                                      // 0: down_2345.Request
	(*Response)(nil),                                     // 1: down_2345.Response
	(*SeatBidOptions)(nil),                               // 2: down_2345.SeatBidOptions
	(*BidOptions)(nil),                                   // 3: down_2345.BidOptions
	(*DirectiveResponseOptions)(nil),                     // 4: down_2345.DirectiveResponseOptions
	(*Request_Imp)(nil),                                  // 5: down_2345.Request.Imp
	(*Request_App)(nil),                                  // 6: down_2345.Request.App
	(*Request_Device)(nil),                               // 7: down_2345.Request.Device
	(*Request_User)(nil),                                 // 8: down_2345.Request.User
	(*Request_Site)(nil),                                 // 9: down_2345.Request.Site
	(*Request_Ext)(nil),                                  // 10: down_2345.Request.Ext
	(*Request_Imp_Display)(nil),                          // 11: down_2345.Request.Imp.Display
	(*Request_Imp_BidInfo)(nil),                          // 12: down_2345.Request.Imp.BidInfo
	(*Request_Device_Caid)(nil),                          // 13: down_2345.Request.Device.Caid
	(*Request_Device_Geo)(nil),                           // 14: down_2345.Request.Device.Geo
	(*DirectiveResponseOptions_Material)(nil),            // 15: down_2345.DirectiveResponseOptions.Material
	(*DirectiveResponseOptions_AppInfo)(nil),             // 16: down_2345.DirectiveResponseOptions.AppInfo
	(*DirectiveResponseOptions_Material_Image)(nil),      // 17: down_2345.DirectiveResponseOptions.Material.Image
	(*DirectiveResponseOptions_Material_Video)(nil),      // 18: down_2345.DirectiveResponseOptions.Material.Video
	(*DirectiveResponseOptions_AppInfo_Permission)(nil),  // 19: down_2345.DirectiveResponseOptions.AppInfo.Permission
	(*DirectiveResponseOptions_AppInfo_MiniProgram)(nil), // 20: down_2345.DirectiveResponseOptions.AppInfo.MiniProgram
}
var file__2345_proto_depIdxs = []int32{
	5,  // 0: down_2345.Request.imp_list:type_name -> down_2345.Request.Imp
	6,  // 1: down_2345.Request.app:type_name -> down_2345.Request.App
	7,  // 2: down_2345.Request.device:type_name -> down_2345.Request.Device
	8,  // 3: down_2345.Request.user:type_name -> down_2345.Request.User
	9,  // 4: down_2345.Request.site:type_name -> down_2345.Request.Site
	10, // 5: down_2345.Request.ext:type_name -> down_2345.Request.Ext
	2,  // 6: down_2345.Response.seat_bid_list:type_name -> down_2345.SeatBidOptions
	3,  // 7: down_2345.SeatBidOptions.bid_list:type_name -> down_2345.BidOptions
	4,  // 8: down_2345.BidOptions.directive_response:type_name -> down_2345.DirectiveResponseOptions
	15, // 9: down_2345.DirectiveResponseOptions.material:type_name -> down_2345.DirectiveResponseOptions.Material
	16, // 10: down_2345.DirectiveResponseOptions.app_info:type_name -> down_2345.DirectiveResponseOptions.AppInfo
	11, // 11: down_2345.Request.Imp.display_list:type_name -> down_2345.Request.Imp.Display
	12, // 12: down_2345.Request.Imp.bid_info_list:type_name -> down_2345.Request.Imp.BidInfo
	13, // 13: down_2345.Request.Device.caid:type_name -> down_2345.Request.Device.Caid
	14, // 14: down_2345.Request.Device.geo:type_name -> down_2345.Request.Device.Geo
	17, // 15: down_2345.DirectiveResponseOptions.Material.images:type_name -> down_2345.DirectiveResponseOptions.Material.Image
	18, // 16: down_2345.DirectiveResponseOptions.Material.video:type_name -> down_2345.DirectiveResponseOptions.Material.Video
	17, // 17: down_2345.DirectiveResponseOptions.Material.icon:type_name -> down_2345.DirectiveResponseOptions.Material.Image
	19, // 18: down_2345.DirectiveResponseOptions.AppInfo.permissions:type_name -> down_2345.DirectiveResponseOptions.AppInfo.Permission
	20, // 19: down_2345.DirectiveResponseOptions.AppInfo.wx_miniProgram:type_name -> down_2345.DirectiveResponseOptions.AppInfo.MiniProgram
	17, // 20: down_2345.DirectiveResponseOptions.Material.Video.cover:type_name -> down_2345.DirectiveResponseOptions.Material.Image
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file__2345_proto_init() }
func file__2345_proto_init() {
	if File__2345_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file__2345_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeatBidOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_Display); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Imp_BidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request_Device_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_Material); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_Material_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_Material_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_AppInfo_Permission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file__2345_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectiveResponseOptions_AppInfo_MiniProgram); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file__2345_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file__2345_proto_goTypes,
		DependencyIndexes: file__2345_proto_depIdxs,
		MessageInfos:      file__2345_proto_msgTypes,
	}.Build()
	File__2345_proto = out.File
	file__2345_proto_rawDesc = nil
	file__2345_proto_goTypes = nil
	file__2345_proto_depIdxs = nil
}
