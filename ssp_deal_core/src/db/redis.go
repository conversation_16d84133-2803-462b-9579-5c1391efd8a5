package db

import (
	"context"
	"mh_proxy/config"

	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"
)

// GlbRedis ...
var GlbRedis *redis.Client

// InitRedis ...
func InitRedis() (err error) {
	// mysqlDB, err := sql.Open("mysql", config.MYSQL_USERNAME+":"+config.MYSQL_PASSWORD+"@tcp("+config.MYSQL_HOST+":"+config.MYSQL_PORT+")/"+config.MYSQL_DBNAME)

	redisClient := redis.NewClient(&redis.Options{
		// Addr:     "localhost:6379",
		Addr:     config.RedisHost + ":" + config.RedisPort,
		Password: "", // no password set
		DB:       0,  // use default DB
	})

	ctx := context.Background()
	_, err = redisClient.Ping(ctx).Result()
	if err != nil {
		return err
	}

	GlbRedis = redisClient
	return nil
}

// close redis connection
func CloseRedis() {
	if GlbRedis == nil {
		return
	}
	// close redis connection
	GlbRedis.Close()
}
