/** 创建本地表 */
-- CREATE DATABASE IF NOT EXISTS ssp_rawdata ON CLUSTER default

CREATE TABLE
    IF NOT EXISTS ssp_rawdata.loc_req_data ON CLUSTER default (
        `uid`                        String,
        `app_id`                     String,
        `pos_id`                     String,
        `p_app_id`                   String,
        `p_pos_id`                   String,
        `channel`                    String,
        `app_name`                   String,
        `app_bundle`                 String,
        `os`                         String,
        `osv`                        String,
        `did_md5`                    String,
        `imei`                       String,
        `imei_md5`                   String,
        `android_id`                 String,
        `android_id_md5`             String,
        `idfa`                       String,
        `idfa_md5`                   String,
        `ip`                         String,
        `ua`                         String,
        `oaid`                       String,
        `model`                      String,
        `manufacturer`               String,
        `screen_width`               Int32,
        `screen_height`              Int32,
        `appstore_version`           String,
        `hms_version`                String,
        `device_type`                String,
        `connect_type`               String,
        `carrier`                    String,
        `down_req_num`               Int32,
        `down_resp_num`              Int32,
        `down_cost_time`             Int32,
        `up_req_time`                Int32,
        `up_req_num`                 Int32,
        `up_resp_time`               Int32,
        `up_resp_num`                Int32,
        `up_resp_ok_num`             Int32,
        `up_cost_time`               Int32,
        `code`                       Int32,
        `internal_code`              Int32,
        `up_resp_code`               Int32,
        `up_price_report_win_num`    Int32,
        `up_price_report_failed_num` Int32,
        `is_win`                     Int32,
        `floor_price`                Int32,
        `final_price`                Int32,
        `up_price`                   Int32,
        `up_resp_failed_num`         Int32,
        `bitcode`                    Int64,
        `dd`                         Date,
        `hh`                         String,
        `mm`                         String,
        `report_time`                DateTime
    ) ENGINE = MergeTree ()
PARTITION BY
    toYYYYMMDD (report_time)
ORDER BY
    (app_id, pos_id, p_app_id, p_pos_id, is_win, dd, hh, mm) TTL dd + INTERVAL 1 HOUR;

/** 创建分布式表 */
CREATE TABLE
    IF NOT EXISTS ssp_rawdata.req_data ON CLUSTER default AS ssp_rawdata.loc_req_data ENGINE = Distributed (default, ssp_rawdata, loc_req_data);