/** 创建本地表 */
CREATE TABLE
    IF NOT EXISTS ssp_rawdata.loc_exp_data ON CLUSTER default (
        `uid`            String,
        `app_id`         String,
        `app_type`       Int32,
        `pos_id`         String,
        `p_app_id`       String,
        `p_app_type`     Int32,
        `p_pos_id`       String,
        `channel`        String,
        `app_name`       String,
        `app_bundle`     String,
        `os`             String,
        `osv`            String,
        `did_md5`        String,
        `imei`           String,
        `imei_md5`       String,
        `android_id`     String,
        `android_id_md5` String,
        `idfa`           String,
        `idfa_md5`       String,
        `ip`             String,
        `ua`             String,
        `oaid`           String,
        `model`          String,
        `manufacturer`   String,
        `floor_price`    Int32,
        `final_price`    Int32,
        `ecpm`           Int32,
        `exp_time`       Int64,
        `dd`             Date,
        `hh`             String,
        `mm`             String,
        `report_time`    DateTime
    ) ENGINE = MergeTree ()
PARTITION BY
    toYYYYMMDD (report_time)
ORDER BY
    (app_id, pos_id, p_app_id, p_pos_id, dd, hh, mm) TTL dd + INTERVAL 30 HOUR;

/** 创建分布式表 */
CREATE TABLE
    IF NOT EXISTS ssp_rawdata.exp_data ON CLUSTER default AS ssp_rawdata.loc_exp_data ENGINE = Distributed (default, ssp_rawdata, loc_exp_data);