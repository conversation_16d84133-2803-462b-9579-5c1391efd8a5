/** 创建本地表 */
CREATE TABLE
    IF NOT EXISTS ssp_rawdata.loc_clk_data ON CLUSTER default (
        `uid`                     String,
        `bidid`                   String,
        `app_id`                  String,
        `app_type`                Int32,
        `pos_id`                  String,
        `p_app_id`                String,
        `p_app_type`              Int32,
        `p_pos_id`                String,
        `channel`                 String,
        `app_name`                String,
        `app_bundle`              String,
        `os`                      String,
        `osv`                     String,
        `did_md5`                 String,
        `imei`                    String,
        `imei_md5`                String,
        `android_id`              String,
        `android_id_md5`          String,
        `idfa`                    String,
        `idfa_md5`                String,
        `ip`                      String,
        `ua`                      String,
        `oaid`                    String,
        `model`                   String,
        `manufacturer`            String,
        `req_width`               String,
        `req_height`              String,
        `width`                   String,
        `height`                  String,
        `down_x`                  String,
        `down_y`                  String,
        `up_x`                    String,
        `up_y`                    String,
        `floor_price`             Int32,
        `final_price`             Int32,
        `ecpm`                    Int32,
        `clk_time`                Int64,
        `sld`                     String,
        `is_server_sld`           Int32,
        `is_server_replace_xy`    Int32,
        `is_logic_pixel`          Int32,
        `dd`                      Date,
        `hh`                      String,
        `mm`                      String,
        `report_time`             DateTime
    ) ENGINE = MergeTree ()
PARTITION BY
    toYYYYMMDD (report_time)
ORDER BY
    (app_id, pos_id, p_app_id, p_pos_id, dd, hh, mm) TTL dd + INTERVAL 7 DAY;

/** 创建分布式表 */
CREATE TABLE
    IF NOT EXISTS ssp_rawdata.clk_data ON CLUSTER default AS ssp_rawdata.loc_clk_data ENGINE = Distributed (default, ssp_rawdata, loc_clk_data);