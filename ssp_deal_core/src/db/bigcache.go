package db

import (
	"fmt"
	"log"
	"time"

	"github.com/allegro/bigcache/v3"
)

// GlbBigCache ...
var GlbBigCache *bigcache.BigCache
var GlbBigCacheMinute *bigcache.BigCache

// InitBigCache ...
func InitBigCache() (err error) {

	config := bigcache.Config{
		// number of shards (must be a power of 2)
		Shards: 2,

		// time after which entry can be evicted
		LifeWindow: 30 * 24 * time.Hour,

		// Interval between removing expired entries (clean up).
		// If set to <= 0 then no action is performed.
		// Setting to < 1 second is counterproductive — bigcache has a one second resolution.
		CleanWindow: 1 * time.Second,

		// rps * lifeWindow, used only in initial memory allocation
		MaxEntriesInWindow: 1000 * 10 * 60,

		// max entry size in bytes, used only in initial memory allocation
		MaxEntrySize: 1000,

		// prints information about additional memory allocation
		Verbose: true,

		// cache will not allocate more memory than this limit, value in MB
		// if value is reached then the oldest entries can be overridden for the new ones
		// 0 value means no size limit
		HardMaxCacheSize: 256,

		// callback fired when the oldest entry is removed because of its expiration time or no space left
		// for the new entry, or because delete was called. A bitmask representing the reason will be returned.
		// Default value is nil which means no callback and it prevents from unwrapping the oldest entry.
		OnRemove: nil,

		// OnRemoveWithReason is a callback fired when the oldest entry is removed because of its expiration time or no space left
		// for the new entry, or because delete was called. A constant representing the reason will be passed through.
		// Default value is nil which means no callback and it prevents from unwrapping the oldest entry.
		// Ignored if OnRemove is specified.
		OnRemoveWithReason: debugOnRemoveWithReason,
	}

	cache, initErr := bigcache.NewBigCache(config)
	if initErr != nil {
		log.Fatal(initErr)
		return initErr
	}

	GlbBigCache = cache

	/////////////////////////////////////////////////////////////////////////////
	configMinute := bigcache.Config{
		// number of shards (must be a power of 2)
		Shards: 2,

		// time after which entry can be evicted
		LifeWindow: 1 * time.Minute,

		// Interval between removing expired entries (clean up).
		// If set to <= 0 then no action is performed.
		// Setting to < 1 second is counterproductive — bigcache has a one second resolution.
		CleanWindow: 1 * time.Second,

		// rps * lifeWindow, used only in initial memory allocation
		MaxEntriesInWindow: 1000 * 10 * 60,

		// max entry size in bytes, used only in initial memory allocation
		MaxEntrySize: 500,

		// prints information about additional memory allocation
		Verbose: true,

		// cache will not allocate more memory than this limit, value in MB
		// if value is reached then the oldest entries can be overridden for the new ones
		// 0 value means no size limit
		HardMaxCacheSize: 256,

		// callback fired when the oldest entry is removed because of its expiration time or no space left
		// for the new entry, or because delete was called. A bitmask representing the reason will be returned.
		// Default value is nil which means no callback and it prevents from unwrapping the oldest entry.
		OnRemove: nil,

		// OnRemoveWithReason is a callback fired when the oldest entry is removed because of its expiration time or no space left
		// for the new entry, or because delete was called. A constant representing the reason will be passed through.
		// Default value is nil which means no callback and it prevents from unwrapping the oldest entry.
		// Ignored if OnRemove is specified.
		OnRemoveWithReason: debugMinuteOnRemoveWithReason,
	}

	// 1 min cache, max_cache_size: 4 * 1024 MB
	minuteCache, err := bigcache.NewBigCache(configMinute)
	if err != nil {
		return err
	}

	GlbBigCacheMinute = minuteCache
	return nil
}

func CloseBigCache() {
	if GlbBigCache != nil {
		GlbBigCache.Close()
	}
	if GlbBigCacheMinute != nil {
		GlbBigCacheMinute.Close()
	}
}

// debugOnRemoveWithReason ...
func debugOnRemoveWithReason(key string, entry []byte, reason bigcache.RemoveReason) {
	fmt.Println("bigcache on remove key:", key, ", reason:", reason)
}

// debugOnRemoveWithReason ...
func debugMinuteOnRemoveWithReason(key string, entry []byte, reason bigcache.RemoveReason) {
	fmt.Println("minute bigcache on remove key:", key, ", reason:", reason)
}
