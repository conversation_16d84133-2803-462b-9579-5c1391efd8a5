package db

import (
	"mh_proxy/config"

	"database/sql"
	"fmt"

	"github.com/ClickHouse/clickhouse-go"
)

// GlbClickHouseDb ...
var GlbClickHouseDb *sql.DB

// InitClickHouse ...
func InitClickHouse() error {
	clickHouseDB, err := sql.Open("clickhouse", config.ClickHouseHostPort)
	if err != nil {
		fmt.Println("clickhouse open err:", err)
	}
	if err := clickHouseDB.Ping(); err != nil {
		if exception, ok := err.(*clickhouse.Exception); ok {
			fmt.Printf("[%d] %s \n%s\n", exception.Code, exception.Message, exception.StackTrace)
		} else {
			fmt.Println("clickhouse ping err:", err)
		}
		return err
	}
	GlbClickHouseDb = clickHouseDB

	// var (
	// 	tx, _   = GlbClickHouseDb.Begin()
	// 	stmt, _ = tx.Prepare("INSERT INTO example (country_code, os_id, browser_id, categories, action_day, action_time) VALUES (?, ?, ?, ?, ?, ?)")
	// )
	// tx, _ := GlbClickHouseDb.Begin()
	// stmt, _ := tx.Prepare("INSERT INTO example (country_code, os_id, browser_id, categories, action_day, action_time) VALUES (?, ?, ?, ?, ?, ?)")

	// defer stmt.Close()

	// for i := 0; i < 100; i++ {
	// 	if _, err := stmt.Exec(
	// 		"RU",
	// 		10+i,
	// 		100+i,
	// 		clickhouse.Array([]int16{1, 2, 3}),
	// 		time.Now(),
	// 		time.Now(),
	// 	); err != nil {
	// 		log.Fatal(err)
	// 	}
	// }

	// if err := tx.Commit(); err != nil {
	// 	log.Fatal(err)
	// }

	return nil
}

// CloseClickHouse...
func CloseClickHouse() {
	if GlbClickHouseDb == nil {
		return
	}
	GlbClickHouseDb.Close()
}

// 1. create database
// CREATE DATABASE IF NOT EXISTS ssp_rawdata ON CLUSTER default
// 2. create exp table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.exp_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`channel`            String,
// 	`app_name`           String,
// 	`app_bundle`         String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`floor_price`        Int32,
// 	`final_price`        Int32,
// 	`ecpm`               Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/exp_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 10 MINUTE
// ORDER BY report_time SETTINGS index_granularity = 8192
// 3. create clk table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.clk_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`channel`            String,
// 	`app_name`           String,
// 	`app_bundle`         String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`req_width`          String,
// 	`req_height`         String,
// 	`width`              String,
// 	`height`             String,
// 	`down_x`             String,
// 	`down_y`             String,
// 	`up_x`               String,
// 	`up_y`               String,
// 	`floor_price`        Int32,
// 	`final_price`        Int32,
// 	`ecpm`               Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/clk_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 10 MINUTE
// ORDER BY report_time SETTINGS index_granularity = 8192
// 4. create req table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.req_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`channel`            String,
// 	`app_name`           String,
// 	`app_bundle`         String,
// 	`os`                 String,
// 	`osv`                String,
// 	`did_md5`            String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`screen_width`       Int32,
// 	`screen_height`      Int32,
// 	`appstore_version`   String,
// 	`hms_version`        String,
// 	`device_type`        String,
// 	`connect_type`       String,
// 	`carrier`            String,
// 	`down_req_num`       Int32,
// 	`down_resp_num`      Int32,
// 	`down_cost_time`     Int32,
// 	`up_req_time`        Int32,
// 	`up_req_num`         Int32,
// 	`up_resp_time`       Int32,
// 	`up_resp_num`        Int32,
// 	`up_resp_ok_num`     Int32,
// 	`up_cost_time`       Int32,
// 	`code`               Int32,
// 	`internal_code`      Int32,
// 	`is_win`             Int32,
// 	`floor_price`        Int32,
// 	`final_price`        Int32,
// 	`up_price`           Int32,
// 	`up_resp_failed_num` Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/req_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 HOUR
// ORDER BY report_time SETTINGS index_granularity = 8192
// 5. create material table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.material_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`channel`            String,
// 	`os`                 String,
// 	`title`              String,
// 	`description`        String,
// 	`image_url`          String,
// 	`icon_url`           String,
// 	`video_url`          String,
// 	`download_url`       String,
// 	`landpage_url`       String,
// 	`package_name`       String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/material_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 10 MINUTE
// ORDER BY report_time SETTINGS index_granularity = 8192

//6. create price table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.price_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`bid_price`          Int32,
// 	`deal_price`         Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/price_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 HOUR
// ORDER BY report_time SETTINGS index_granularity = 8192

// 7. create mismatch_bundle_data table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.mismatch_bundle_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`bundle`           String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/mismatch_bundle_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 HOUR
// ORDER BY report_time SETTINGS index_granularity = 8192

// 8. create rtb_req_price_data table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.rtb_req_price_data ON CLUSTER default (
// 	`tag_id`             String,
// 	`price`              Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/rtb_req_price_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 HOUR
// ORDER BY report_time SETTINGS index_granularity = 8192

// 9. create deeplink table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.deeplink_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`deeplink_result`    String,
// 	`deeplink_reason`    String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX pos_id_index pos_id TYPE minmax GRANULARITY 30,
// 	INDEX p_app_id_index p_app_id TYPE minmax GRANULARITY 30,
// 	INDEX p_pos_id_index p_pos_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/deeplink_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 14. create did ip table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.did_ip ON CLUSTER default (
// 	`ip`                 String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`did_md5`            String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`oaid`               String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`req_time`           Int64,
// 	`exp_time`           Int64,
// 	`clk_time`           Int64,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/did_ip/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 15. create did req table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.did_req ON CLUSTER default (
// 	`did_md5`            String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`oaid`               String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`req_time`           Int64,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/did_req/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 16. create did exp table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.did_exp ON CLUSTER default (
// 	`did_md5`            String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`oaid`               String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`exp_time`           Int64,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/did_exp/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 17. create did clk table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.did_clk ON CLUSTER default (
// 	`did_md5`            String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`oaid`               String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`clk_time`           Int64,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/did_clk/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 18. create did app list table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.did_app_list ON CLUSTER default (
// 	`did_md5`            String,
// 	`imei`               String,
// 	`android_id`         String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`app_list`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/did_app_list/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 19. create win price data table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.win_url_data ON CLUSTER default (
// 	`uid`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`p_app_id`           String,
// 	`p_pos_id`           String,
// 	`is_win`             Int32,
// 	`req`                String,
// 	`resp`               String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/win_url_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 2 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 20. create timeout data table
// CREATE TABLE IF NOT EXISTS ssp_rawdata.timeout_data ON CLUSTER default (
// 	`rtb`                String,
// 	`app_id`             String,
// 	`pos_id`             String,
// 	`timeout`            Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/timeout_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// DROP TABLE ssp_rawdata.xxxxxx ON CLUSTER default

// ALTER TABLE ssp_rawdata.req_data ON CLUSTER default MODIFY TTL report_time + INTERVAL 1 HOUR
// ALTER TABLE ssp_rawdata.exp_data ON CLUSTER default MODIFY TTL report_time + INTERVAL 1 HOUR
// ALTER TABLE ssp_rawdata.clk_data ON CLUSTER default MODIFY TTL report_time + INTERVAL 1 HOUR
// ALTER TABLE ssp_rawdata.price_data ON CLUSTER default ADD COLUMN tag_id String AFTER uid

// CREATE TABLE IF NOT EXISTS ssp_rawdata.audit_data ON CLUSTER default (
// 	`uid`                          String,
// 	`app_id`                       String,
// 	`pos_id`                       String,
// 	`pos_type`                     Int32,
// 	`pos_width`                    Int32,
// 	`pos_height`                   Int32,
// 	`tag_id`                       String,
// 	`origin_tag_id`                String,
// 	`style_id`                     String,
// 	`media_group`                  String,
// 	`extra_tag`                    String,
// 	`md5_key`                      String,
// 	`crid`                         String,
// 	`title`                        String,
// 	`description`                  String,
// 	`image_url`                    String,
// 	`image_width`                  Int32,
// 	`image_height`                 Int32,
// 	`video_url`                    String,
// 	`cover_url`                    String,
// 	`video_width`                  Int32,
// 	`video_height`                 Int32,
// 	`video_duration`               Int32,
// 	`landpage_url`                 String,
// 	`download_url`                 String,
// 	`os`                           String,
// 	`package_name`                 String,
// 	`deeplink`                     String,
// 	`interact_type`                Int32,
// 	`icon_url`                     String,
// 	`dd`                           Date,
// 	`hh`                           String,
// 	`mm`                           String,
// 	`report_time`                  DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/audit_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 3 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// CREATE TABLE IF NOT EXISTS ssp_rawdata.replace_redis_key ON CLUSTER default (
// 	`app_id`             String,
// 	`replace_redis_key`  String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX app_id_index app_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/tables/replace_redis_key/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 3 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192
