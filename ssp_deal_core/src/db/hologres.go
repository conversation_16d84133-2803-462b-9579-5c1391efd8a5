package db

import (
	"mh_proxy/utilities"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

// writeMode -> update
var GlbHologresAdxSSPUpdateDataDb *holoclient.HoloClient
var GlbHologresAdxSSPUpdateDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

var GlbHologresAdxSSPDataDb *holoclient.HoloClient
var GlbHologresAdxSSPDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

func InitHologres() error {

	if utilities.SkipHologress {
		return nil
	}

	writeMode := utilities.HologresWriteMode
	batchSize := utilities.HologresBatchSize

	threadSize := utilities.HologresThreadSize

	writeBatchByteSize := utilities.HologresWriteBatchByteSize

	writeMaxIntervalMs := utilities.HologresWriteMaxIntervalMs

	// adx_ssp writeMode -> update
	hologresUpdateClient := databases.NewHologres(
		utilities.PostgresAdxSSPDataDSN,
		1,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxSSPUpdateDataDb = hologresUpdateClient

	// adx_ssp
	hologresAdxSSPDataClient := databases.NewHologres(
		utilities.PostgresAdxSSPDataDSN,
		writeMode,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxSSPDataDb = hologresAdxSSPDataClient
	NewHologresAdxSSPDataTableSchema("deal", "req_data")
	NewHologresAdxSSPDataTableSchema("deal", "exp_data")
	NewHologresAdxSSPDataTableSchema("deal", "clk_data")
	NewHologresAdxSSPDataTableSchema("deal", "rtb_cpm_bid_floor_data")
	NewHologresAdxSSPDataTableSchema("deal", "rtb_cpm_data")
	NewHologresAdxSSPDataTableSchema("deal", "win_price_data")
	NewHologresAdxSSPDataTableSchema("deal", "loss_price_data")
	NewHologresAdxSSPDataTableSchema("deal", "video_start_data")
	NewHologresAdxSSPDataTableSchema("deal", "video_end_data")
	NewHologresAdxSSPDataTableSchema("magic", "clickpoint_lib")
	NewHologresAdxSSPDataTableSchema("magic", "limit_ip")
	NewHologresAdxSSPDataTableSchema("magic", "limit_did")
	NewHologresAdxSSPDataTableSchema("material", "win_data")
	NewHologresAdxSSPDataTableSchema("material", "replace_data")
	NewHologresAdxSSPDataTableSchema("material", "all_data")
	NewHologresAdxSSPDataTableSchema("material", "dict_query")

	// NewHologresAdxSSPDataTableSchema("device", "statistics_dau_req")
	// NewHologresAdxSSPDataTableSchema("device", "statistics_dau_upstream_req")
	// NewHologresAdxSSPDataTableSchema("device", "statistics_mau_req")
	// NewHologresAdxSSPDataTableSchema("device", "statistics_mau_upstream_req")
	NewHologresAdxSSPDataTableSchema("device", "did_manufacturer_daily_trusted")
	NewHologresAdxSSPDataTableSchema("device", "did_manufacturer_and_model_trusted")
	NewHologresAdxSSPDataTableSchema("device", "statistics_ip_did_records_demand")
	//NewHologresAdxSSPDataTableSchema("device", "dmp_did")
	NewHologresAdxSSPDataTableSchema("device", "device_info_debug") // device info debug表 // 2024-12-26

	NewHologresAdxSSPDataTableSchema("debug", "demand_json_debug")
	NewHologresAdxSSPDataTableSchema("debug", "demand_req_data")
	NewHologresAdxSSPDataTableSchema("debug", "demand_replace_req_data")
	NewHologresAdxSSPDataTableSchema("debug", "demand_replace_clk_data")
	NewHologresAdxSSPDataTableSchema("debug", "demand_replace_exp_data")
	NewHologresAdxSSPDataTableSchema("debug", "supply_req_data")
	NewHologresAdxSSPDataTableSchema("debug", "supply_tmp_req_data")
	NewHologresAdxSSPDataTableSchema("debug", "ksug_debug")
	NewHologresAdxSSPDataTableSchema("debug", "json_debug")
	NewHologresAdxSSPDataTableSchema("debug", "debug_ks_applist")
	NewHologresAdxSSPDataTableSchema("debug", "anticheat_debug")
	NewHologresAdxSSPDataTableSchema("debug", "debug_replace_ip")
	NewHologresAdxSSPDataTableSchema("debug", "sdk_track")
	NewHologresAdxSSPDataTableSchema("debug", "ks_bundle_debug")
	NewHologresAdxSSPDataTableSchema("debug", "vivo_bundle_debug")
	NewHologresAdxSSPDataTableSchema("debug", "vivo_publisher_debug")
	NewHologresAdxSSPDataTableSchema("debug", "bes_bundle_debug")
	NewHologresAdxSSPDataTableSchema("debug", "debug_timeout_data")

	// writeMode -> update
	NewHologresAdxSSPDataUpdateTableSchema("magic", "didlib_android")
	NewHologresAdxSSPDataUpdateTableSchema("magic", "didlib_ios")
	NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_did_demand")
	NewHologresAdxSSPDataUpdateTableSchema("device", "statistics_ip_demand")

	NewHologresAdxSSPDataUpdateTableSchema("device", "app_last_date")
	NewHologresAdxSSPDataUpdateTableSchema("device", "applist_did_data")
	NewHologresAdxSSPDataUpdateTableSchema("device", "dpi")
	NewHologresAdxSSPDataUpdateTableSchema("device", "dict_model")

	NewHologresAdxSSPDataUpdateTableSchema("debug", "applist_data")
	NewHologresAdxSSPDataUpdateTableSchema("debug", "oppo_debug")
	NewHologresAdxSSPDataUpdateTableSchema("debug", "ks_tagid")
	NewHologresAdxSSPDataUpdateTableSchema("debug", "dpi_debug")
	NewHologresAdxSSPDataUpdateTableSchema("debug", "ssp_deal_stack")
	NewHologresAdxSSPDataUpdateTableSchema("debug", "sdk_oaid_debug")
	NewHologresAdxSSPDataUpdateTableSchema("anticheat", "sdk_anticheat_data")

	return nil
}

// CloseHologres...
func CloseHologres() {
	if GlbHologresAdxSSPUpdateDataDb == nil {
		GlbHologresAdxSSPUpdateDataDb.Close()
	}

	if GlbHologresAdxSSPDataDb == nil {
		GlbHologresAdxSSPDataDb.Close()
	}
}

func NewHologresAdxSSPDataUpdateTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxSSPUpdateDataTableSchemas[tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxSSPUpdateDataDb.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxSSPUpdateDataTableSchemas[tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}

func NewHologresAdxSSPDataTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxSSPDataTableSchemas[schemaName+tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxSSPDataDb.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxSSPDataTableSchemas[schemaName+tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}
