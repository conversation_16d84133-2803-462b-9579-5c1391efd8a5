package router

import (
	"fmt"
	"mh_proxy/api"
	"mh_proxy/models"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// Init ...
func Init() *gin.Engine {
	router := gin.Default()

	router.Use(ErrorHandler())

	// 路由组: api
	v1 := router.Group("/api")
	{
		v1.GET("/v1", api.API)
		v1.GET("/test", api.Test)
		v1.GET("/callback", api.Callback)
	}

	// 路由组: report
	report := router.Group("/report")
	{
		report.GET("/req", api.Request)
		report.GET("/reqv3", api.RequestV3)
		report.GET("/expose", api.Expose)
		report.GET("/click", api.Click)
		report.GET("/dp", api.DP)
		report.GET("/videostart", api.VideoStart)
		report.GET("/videoend", api.VideoEnd)

		// sdk上报请求
		report.POST("/reqv3.3", api.RequestV33)
	}

	// 路由组: rtb
	rtb := router.Group("/rtb")
	{
		rtb.POST("/request", api.RtbPostRequest)
		rtb.GET("/request", api.RtbGetRequest)
		// rtb价格上报
		rtb.GET("/price", api.RtbPrice)
		rtb.POST("/qimao/getAd", api.QimaoAd)
	}

	// 路由组: 价格上报 to deleted
	priceToDeleted := router.Group("/win")
	{
		priceToDeleted.GET("/price", api.ApiPriceWin)
	}

	// 路由组: api价格上报
	price := router.Group("/price")
	{
		price.GET("/win", api.ApiPriceWin)
		price.GET("/loss", api.ApiPriceLoss)
	}

	// 路由组: sdk
	sdk := router.Group("/sdk")
	{
		// 获取sdk配置
		sdk.GET("/v1", api.SdkConfig)
		sdk.GET("/v2", api.SdkConfigV2)
		// sdk获取api广告
		sdk.GET("/getapi", api.API)
		sdk.GET("/anticheat", api.SdkAntiCheat)

		// 获取sdk配置 v3
		sdk.GET("/v3", api.SdkConfigV3)
		// 获取sdk配置 v3.0
		sdk.GET("/v3.0", api.SdkConfigV30)
		// sdk获取api广告
		sdk.GET("/getapiv3", api.SDKAPIV3)
		// 接口保留, 实现暂时改为false
		sdk.GET("/anticheatv3", api.SdkAntiCheatV3)
		// sdk价格失败上报
		sdk.GET("/pricefailed", api.SdkPriceFailed)
		// 通过app_id获取云控选项
		sdk.GET("/appinfo", api.SdkAppInfo)
		sdk.POST("/appinfo", api.SdkAppInfoPost)

		// 获取sdk配置
		sdk.POST("/v3.3", api.SdkConfigV33)
		// sdk获取api广告
		sdk.POST("/getapiv3.3", api.SDKAPIV33)
		// sdk价格成功上报
		sdk.GET("/win", api.SdkPriceWin)
		// sdk价格失败上报
		sdk.GET("/loss", api.SdkPriceLoss)
		// 获取sdk加密配置
		sdk.POST("/v3.4", api.SdkConfigV34)

		// sdk debug
		sdk.POST("/debug", api.SdkDebug)

		sdk.POST("/anticheatv4", api.SdkAntiCheatV4)
	}

	// 路由组: sdk -> report
	sdkReport := router.Group("/deal")
	{
		sdkReport.POST("/sensorbattery", api.SdkReportSensorBattory)
	}

	// 路由组: sdk -> extra
	sdkExtra := router.Group("/extra")
	{
		sdkExtra.GET("/geturl", api.SdkExtraNull)
		sdkExtra.GET("/getwk", api.SdkExtraNull)
		sdkExtra.GET("/getntf", api.SdkExtraNull)
		sdkExtra.GET("/getcp", api.SdkExtraNull)
		// sdk上报应用列表
		sdkExtra.POST("/al", api.SdkExtraAL)
		// sdk获取剪贴板 通知, clipboard notification
		sdkExtra.POST("/cn", api.SdkExtraCN)
		// sdk获取唤醒, wake up, 接口保留, 实现暂时改为false
		sdkExtra.POST("/wk", api.SdkExtraWK)
		// sdk获取唤醒配置 wake up config, 接口保留, 实现暂时改为false
		sdkExtra.POST("/wkcfg", api.SdkExtraWKConfig)

		// 剪贴板接口, /extra/cp, 下发数组
		sdkExtra.POST("/cp", api.SdkExtraCP)
		// 通知接口, /extra/notice, 跟之前/extra/cn保持一致
		sdkExtra.POST("/notice", api.SdkExtraCN)
	}

	// router.Run(":8081")
	return router
}

func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				var errorMsg string
				switch v := err.(type) {
				case error:
					errorMsg = v.Error()
				default:
					errorMsg = err.(string)
				}
				stack := debug.Stack()

				fmt.Println(errorMsg)
				fmt.Println(string(stack))

				go models.BigDataHoloSspDealStackDebug(errorMsg, string(stack))

				c.AbortWithStatus(http.StatusInternalServerError)
			}
		}()
		// 继续处理请求
		c.Next()
	}
}
