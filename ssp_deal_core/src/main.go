package main

import (
	"context"
	"fmt"
	"mh_proxy/db"
	"mh_proxy/models"
	"mh_proxy/router"
	"mh_proxy/utils"
	"os"
	"os/signal"
	"syscall"
	"time"

	"net/http"
	_ "net/http/pprof"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	_ "go.uber.org/automaxprocs"
)

func crontabReadConfig() *cron.Cron {
	c := cron.New(cron.WithParser(cron.NewParser(
		cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor,
	)))

	// 每30s执行一次
	c.AddFunc("*/30 * * * * ?", models.CrontabReadConfig)
	c.Start()
	return c
}

func main() {
	// // Disable Console Color, you don't need console color when writing the logs to file.
	// gin.DisableConsoleColor()
	// // Force log's color
	// // gin.ForceConsoleColor()

	// // Use the following code if you need to write the logs to file and console at the same time.
	// gin.DefaultWriter = io.MultiWriter(f, os.Stdout)

	gin.SetMode(gin.ReleaseMode)

	// 初始化 redis
	if err := db.InitRedis(); err != nil {
		fmt.Println("init redis failed", err)
		return
	}

	// 初始化 mysql
	if err := db.InitMysql(); err != nil {
		fmt.Println("init mysql failed", err)
		return
	}

	// 初始化 clickhouse
	if err := db.InitClickHouse(); err != nil {
		fmt.Println("init clickhouse failed", err)
		return
	}

	// 初始化 bigcache
	if err := db.InitBigCache(); err != nil {
		fmt.Println("init bigcache failed", err)
		return
	}

	// 初始化 hologres
	if err := db.InitHologres(); err != nil {
		fmt.Println("init hologres failed", err)
		return
	}

	// crontab
	models.CrontabReadConfig()
	cronJob := crontabReadConfig()

	// 创建 pprof 调试服务器，并保存引用以便后续关闭
	pprofServer := &http.Server{
		Addr:    ":6060",
		Handler: nil,
	}
	go func() {
		if err := pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("failed to start pprof server: %s\n", err)
		}
	}()

	// 初始化路由
	r := router.Init()

	// 创建 HTTP 服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 在单独的 goroutine 中启动服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("failed to listen: %s\n", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("starting to shutdown servers...")

	// 创建一个 25 秒的上下文用于超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 25*time.Second)
	defer cancel()

	// 优雅地关闭主服务器
	if err := srv.Shutdown(ctx); err != nil {
		fmt.Printf("server shutdown error: %s\n", err)
	}

	// 优雅地关闭 pprof 服务器
	if err := pprofServer.Shutdown(ctx); err != nil {
		fmt.Printf("pprof server shutdown error: %s\n", err)
	}

	// 停止定时任务
	cronCtx := cronJob.Stop()
	select {
	case <-cronCtx.Done():
		fmt.Println("all cron jobs completed")
	case <-time.After(10 * time.Second):
		fmt.Println("timed out waiting for cron jobs to complete")
	}

	// 关闭数据库连接
	db.CloseMysql()
	db.CloseRedis()
	db.CloseClickHouse()
	db.CloseBigCache()
	db.CloseHologres()

	// 关闭 HTTP 客户端 - 移到服务器关闭之后
	utils.CloseHttpClient()

	fmt.Println("server shutdown completed")
}
