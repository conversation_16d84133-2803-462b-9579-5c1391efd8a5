package main

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"math/rand"
	"mh_proxy/utils"
	"testing"
	"time"
)

func Test_Base64UrlSafeEncode(t *testing.T) {
	rand.Seed(time.Now().UnixNano())

	for n := 0; n < 20; n++ {
		key := []byte(utils.GetMd5(utils.ConvertIntToString(int(rand.Int63n(999999999999)))))
		key1 := utils.Base64URLEncode(key)
		key2 := base64.URLEncoding.EncodeToString(key)
		if key1 != key2 {
			fmt.Println(key1, key2, string(key))
		}
	}
}

func Test_Decrypt2345Price(t *testing.T) {
	price := "MzNmZWYzMjUyYzYwYTEwNVIHAAUZRUITMjBjMw=="
	eKey := "9a83c25c527265b706b16f56ac6a70ff"
	iKey := "74386d13165f5d61052fc093421f12e4"

	finalPrice, error := utils.Decrypt2345Price(price, eKey, iKey)
	fmt.Println("Test_Decrypt2345Price finalPrice, error: ", finalPrice, error)

}

func Test_Encrypt2345Price(t *testing.T) {
	price := utils.ConvertStringToFloat("4000")

	eKey := "9a83c25c527265b706b16f56ac6a70ff"
	iKey := "74386d13165f5d61052fc093421f12e4"

	finalPrice, error := utils.Encrypt2345Price(price, eKey, iKey)
	fmt.Println("Test_Encrypt2345Price finalPrice, error: ", finalPrice, error)
}

func Test_Decrypt2345Price2(t *testing.T) {
	price := "MjY1NDBmY2ZiODhkY2RlY1xWUxQYF0VDYTQ5MA=="
	eKey := "9a83c25c527265b706b16f56ac6a70ff"
	iKey := "74386d13165f5d61052fc093421f12e4"

	finalPrice, error := utils.Decrypt2345Price(price, eKey, iKey)
	fmt.Println("Test_Decrypt2345Price2 finalPrice, error: ", finalPrice, error)

}

func Test_EncryptXiaoniuPrice(t *testing.T) {
	appKey := "68b10c9cc519934c9066ecff2db3264a"
	priceString := "923.0"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := base64.RawURLEncoding.EncodeToString(finalPrice)
	fmt.Println("Test_EncryptXiaoniuPrice finalPrice: ", priceString, ":", finalString)
}

func Test_DecryptXiaoniuPrice(t *testing.T) {
	appKey := "68b10c9cc519934c9066ecff2db3264a"
	s := "5zQQP5UaGwb2U5QYZa2f1w"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptXiaoniuPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_EncryptTaduPrice(t *testing.T) {
	appKey := "46c7c0922c5e326525ee7d0771a8e731"
	priceString := "923.0"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := base64.RawURLEncoding.EncodeToString(finalPrice)
	fmt.Println("Test_EncryptTaduPrice finalPrice: ", priceString, ":", finalString)
}

func Test_DecryptTaduPrice(t *testing.T) {
	appKey := "46c7c0922c5e326525ee7d0771a8e731"
	s := "xMRGS7cq-v5827TnGXX1DA"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptTaduPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_DecryptHuangyouPrice(t *testing.T) {
	appKey := "79a0821598efdce9e852d4b0a22d09eb"
	s := "NVU8cjJjGf4t0zYnlVWPCw"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptHuangyouPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_DecryptHuangyouPrice2(t *testing.T) {
	appKey := "79a0821598efdce9e852d4b0a22d09eb"
	s := "e4u5pk3EOb1nEfr46BFvWg"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptHuangyouPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_EncryptZhangyuePrice(t *testing.T) {
	appKey := "8kAHpjhwaOw6Q8APcCOTaHrWPXn15S5g"
	priceString := "800"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := base64.RawURLEncoding.EncodeToString(finalPrice)
	fmt.Println("Test_EncryptZhangyuePrice finalPrice: ", priceString, ":", finalString)
}

func Test_DecryptZhangyuePrice(t *testing.T) {
	appKey := "8kAHpjhwaOw6Q8APcCOTaHrWPXn15S5g"
	s := "f_xNvG1GdUayceo9Bh4dRw"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptZhangyuePrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_DecryptMeituPrice(t *testing.T) {
	price := "W0MurQAE0i8AAAAAAAAAAN-QM4Hh2IrU1WDrwg=="
	eKey := "ce7a8033fc93417e88dddabf34cc703f"
	iKey := "be980f628658b114d500a322eb222dec"

	finalPrice, error := utils.DecryptPrice(price, eKey, iKey)
	fmt.Println("Test_DecryptMeituPrice finalPrice, error: ", finalPrice, error)
}

func Test_EncryptZhongqingkandianPrice(t *testing.T) {
	appKey := "09106e1dc218d842a34554f902af4faf"
	priceString := "923.0"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := base64.RawURLEncoding.EncodeToString(finalPrice)
	fmt.Println("Test_EncryptZhongqingkandianPrice finalPrice: ", priceString, ":", finalString)
}

func Test_DecryptZhongqingkandianPrice(t *testing.T) {
	appKey := "09106e1dc218d842a34554f902af4faf"
	s := "rP93jXM13wAYYzBdhTghtA"
	s1, _ := base64.RawURLEncoding.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))
	fmt.Println("Test_DecryptZhongqingkandianPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_EncryptQutoutiaoPrice(t *testing.T) {
	appKey := "2y8cy8xvncd43cr0"
	priceString := "100"

	finalPrice := utils.AesCBCPKCS5Encrypt(priceString, appKey)

	finalString := hex.EncodeToString(finalPrice)
	fmt.Println("Test_EncryptQutoutiaoPrice finalPrice: ", priceString, ":", finalString)
}

func Test_DecryptQutoutiaoPrice(t *testing.T) {
	appKey := "2y8cy8xvncd43cr0"
	s := "90cccbaa6c59fc1f2e6964083dec216a"
	s1, _ := hex.DecodeString(s)

	finalPrice := utils.AesCBCPKCS5Decrypt(s1, []byte(appKey))

	fmt.Println("Test_DecryptQutoutiaoPrice finalPrice: ", s, ":", string(finalPrice))
}

func Test_DecryptFancyPrice(t *testing.T) {
	price := "1B2M2Y8AsgTpgAmY7PhCflcr-s3onHFWz04eMA"
	eKey := "KDE3P9ObsXTqvpnwkXTldBHyrzVFcXPm"
	iKey := "bUfrSuIBHcr4IVpts0ULAbAsRWxutDWL"

	finalPrice, error := utils.DecryptPrice(price, eKey, iKey)
	fmt.Println("Test_DecryptFancyPrice finalPrice, error: ", finalPrice, error)
}


func Test_EncryptFancyPrice(t *testing.T) {
	price := utils.ConvertStringToFloat("4000")

	eKey := "KDE3P9ObsXTqvpnwkXTldBHyrzVFcXPm"
	iKey := "bUfrSuIBHcr4IVpts0ULAbAsRWxutDWL"

	finalPrice, error := utils.EncryptPrice(price, eKey, iKey)
	fmt.Println("Test_EncryptFancyPrice finalPrice, error: ", finalPrice, error)
}