# SSP广告服务系统

## 系统概述

SSP（Supply-Side Platform）广告服务系统是一个基于Go语言开发的广告投放平台，主要功能是连接广告主和媒体资源，通过实时竞价(RTB)和其他广告分发机制优化广告收益。系统使用Gin框架构建Web服务，支持高并发请求处理和多平台集成。

## 系统架构

### 核心组件

1. **API层** (`/api`)
   - RTB请求处理 (`rtb.go`)
   - SDK接口服务 (`sdk.go`)
   - 广告上报接口 (`report.go`)
   - 价格管理接口 (`price.go`)

2. **核心业务层** (`/core`)
   - 广告交换核心 (`adx.go`)
   - 素材管理 (`material.go`)
   - 上游平台对接 (`up_*.go`)

3. **RTB模块** (`/rtb`)
   - RTB通用定义 (`rtb_common.go`)
   - 各平台RTB实现 (`rtb_[platform].go`)

4. **数据模型层** (`/models`)
   - Redis数据模型
   - RTB数据模型
   - 定时任务模型
   - 设备信息模型

5. **数据存储层** (`/db`)
   - MySQL: 配置和基础数据
   - Redis: 缓存和实时数据
   - ClickHouse: 大数据存储和分析
   - Hologres: 实时数据分析
   - BigCache: 内存缓存

### 技术栈

- **框架**: Gin Web框架
- **数据库**: MySQL, Redis, ClickHouse, Hologres
- **缓存**: Redis, BigCache
- **任务调度**: cron
- **监控**: prometheus

## 主要功能

### 1. 广告请求处理

- 参数验证和补充
- 上游平台选择
- 并行请求处理
- 广告响应整合
- 防作弊验证

### 2. 实时竞价(RTB)

支持多个主流广告平台的RTB对接：
- 腾讯广点通(GDT)
- 头条(CSJ)
- 快手
- 爱奇艺
- 百度
- 小米
- 华为
- OPPO/VIVO
- 京东
- 知乎
- 网易

### 3. SDK集成

提供完整的SDK集成方案：
- 多版本SDK支持(v1-v3.3)
- 配置管理接口
- 广告请求接口
- 事件上报接口
- 反作弊功能
- 调试接口

### 4. 数据收集与分析

跟踪和记录各类广告事件：
- 广告请求数据
- 曝光数据
- 点击数据
- 转化数据
- 视频广告事件
- 设备信息

### 5. 素材管理

- 素材审核
- 素材替换
- 素材分发
- 素材统计

## 系统工作流程

1. **初始化流程**
   - 连接数据库(MySQL, Redis, ClickHouse)
   - 初始化缓存
   - 启动定时任务
   - 设置路由
   - 启动Web服务

2. **广告请求流程**
   - 接收广告请求
   - 验证请求参数
   - 补充请求信息
   - 选择广告源
   - 并行请求处理
   - 返回最优广告

3. **数据处理流程**
   - 收集请求数据
   - 记录广告事件
   - 更新统计信息
   - 生成分析报表

## 部署架构

系统采用分布式部署架构：
- Web服务集群
- Redis集群
- MySQL主从架构
- ClickHouse集群
- 负载均衡

## 监控和运维

- Prometheus指标收集
- 系统状态监控
- 性能指标跟踪
- 错误日志记录
- 告警机制

## 安全机制

- 请求验证
- 防作弊检测
- 数据加密
- 访问控制
- 审计日志

## 扩展性设计

系统支持灵活扩展：
- 新平台接入
- 新广告类型
- 新的数据源
- 自定义策略
- 第三方服务集成 