package metrics

import "time"

// PipelineMetrics 记录管道运行的性能指标
// MetricsCallback 定义了指标回调函数的类型
type MetricsCallback func(metrics PipelineMetrics)

// MetricsSubscription 表示一个指标订阅
type MetricsSubscription struct {
	interval time.Duration

	Callback MetricsCallback
	StopCh   chan struct{}
}

type PipelineMetrics struct {
	// 总运行时间
	TotalDuration time.Duration `json:"total_duration,omitempty"`
	// 数据处理时间
	ProcessingDuration time.Duration `json:"processing_duration,omitempty"`
	// 空闲时间
	IdleDuration time.Duration `json:"idle_duration,omitempty"`
	// 处理的数据批次数
	BatchCount int64 `json:"batch_count,omitempty"`
	// 处理的数据项总数
	ItemCount int64 `json:"item_count,omitempty"`

	// 新增指标
	// 消息处理延迟（从接收到处理完成的时间）
	MessageLatency time.Duration `json:"message_latency,omitempty"`
	// 错误计数
	ErrorCount int64 `json:"error_count,omitempty"`
	// 当前队列长度
	QueueLength int64 `json:"queue_length,omitempty"`
	// 内存使用量（字节）
	MemoryUsage int64 `json:"memory_usage,omitempty"`
	// 消息处理速率（每秒处理消息数）
	ProcessingRate float64 `json:"processing_rate,omitempty"`
	// 消息丢弃计数
	DroppedCount int64 `json:"dropped_count,omitempty"`
	// 重试次数
	RetryCount int64 `json:"retry_count,omitempty"`
	// 最大处理延迟
	MaxLatency time.Duration `json:"max_latency,omitempty"`
	// 最小处理延迟
	MinLatency time.Duration `json:"min_latency,omitempty"`
	// 平均处理延迟
	AvgLatency time.Duration `json:"avg_latency,omitempty"`
}

type PipelineMillisecondsMetrics struct {
	// 总运行时间
	TotalMs int64 `json:"total_ms,omitempty"`
	// 数据处理时间
	ProcessingMs int64 `json:"processing_ms,omitempty"`
	// 空闲时间
	IdleMs int64 `json:"idle_ms,omitempty"`
	// 处理的数据批次数
	BatchCount int64 `json:"batch_count,omitempty"`
	// 处理的数据项总数
	ItemCount int64 `json:"item_count,omitempty"`

	// 新增指标
	// 消息处理延迟（从接收到处理完成的时间）
	MessageLatency int64 `json:"message_latency,omitempty"`
	// 错误计数
	ErrorCount int64 `json:"error_count,omitempty"`
	// 当前队列长度
	QueueLength int64 `json:"queue_length,omitempty"`
	// 内存使用量（字节）
	MemoryUsage int64 `json:"memory_usage,omitempty"`
	// 消息处理速率（每秒处理消息数）
	ProcessingRate float64 `json:"processing_rate,omitempty"`
	// 消息丢弃计数
	DroppedCount int64 `json:"dropped_count,omitempty"`
	// 重试次数
	RetryCount int64 `json:"retry_count,omitempty"`
	// 最大处理延迟
	MaxLatency int64 `json:"max_latency,omitempty"`
	// 最小处理延迟
	MinLatency int64 `json:"min_latency,omitempty"`
	// 平均处理延迟
	AvgLatency int64 `json:"avg_latency,omitempty"`
}

func NewMetricsSubscription(callback MetricsCallback, interval time.Duration) *MetricsSubscription {
	return &MetricsSubscription{
		Callback: callback,
		interval: interval,
		StopCh:   make(chan struct{}),
	}
}

func (m PipelineMetrics) ToMilliseconds() PipelineMillisecondsMetrics {
	return PipelineMillisecondsMetrics{
		TotalMs:        m.TotalDuration.Milliseconds(),
		ProcessingMs:   m.ProcessingDuration.Milliseconds(),
		IdleMs:         m.IdleDuration.Milliseconds(),
		BatchCount:     m.BatchCount,
		ItemCount:      m.ItemCount,
		MessageLatency: m.MessageLatency.Milliseconds(),
		ErrorCount:     m.ErrorCount,
		QueueLength:    m.QueueLength,
		MemoryUsage:    m.MemoryUsage,
		ProcessingRate: m.ProcessingRate,
		DroppedCount:   m.DroppedCount,
		RetryCount:     m.RetryCount,
		MaxLatency:     m.MaxLatency.Milliseconds(),
		MinLatency:     m.MinLatency.Milliseconds(),
		AvgLatency:     m.AvgLatency.Milliseconds(),
	}
}

// GetIdleRatio 计算空闲时间占比
func (m *PipelineMetrics) GetIdleRatio() float64 {
	if m.TotalDuration == 0 {
		return 0
	}
	return float64(m.IdleDuration) / float64(m.TotalDuration)
}

// GetProcessingRate 计算消息处理速率
func (m *PipelineMetrics) GetProcessingRate() float64 {
	if m.TotalDuration == 0 {
		return 0
	}
	return float64(m.ItemCount) / m.TotalDuration.Seconds()
}

// UpdateLatency 更新延迟统计
func (m *PipelineMetrics) UpdateLatency(latency time.Duration) {
	if m.MaxLatency < latency {
		m.MaxLatency = latency
	}
	if m.MinLatency == 0 || m.MinLatency > latency {
		m.MinLatency = latency
	}
	// 使用移动平均计算平均延迟
	if m.AvgLatency == 0 {
		m.AvgLatency = latency
	} else {
		m.AvgLatency = (m.AvgLatency + latency) / 2
	}
}

// Clone 创建指标的深拷贝
func (m *PipelineMetrics) Clone() PipelineMetrics {
	return PipelineMetrics{
		TotalDuration:      m.TotalDuration,
		ProcessingDuration: m.ProcessingDuration,
		IdleDuration:       m.IdleDuration,
		BatchCount:         m.BatchCount,
		ItemCount:          m.ItemCount,
		MessageLatency:     m.MessageLatency,
		ErrorCount:         m.ErrorCount,
		QueueLength:        m.QueueLength,
		MemoryUsage:        m.MemoryUsage,
		ProcessingRate:     m.ProcessingRate,
		DroppedCount:       m.DroppedCount,
		RetryCount:         m.RetryCount,
		MaxLatency:         m.MaxLatency,
		MinLatency:         m.MinLatency,
		AvgLatency:         m.AvgLatency,
	}
}
