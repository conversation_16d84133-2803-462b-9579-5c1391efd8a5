package metrics

import (
	"sync"
	"sync/atomic"
	"time"
)

// MetricsUpdater 用于管理和更新性能指标
type MetricsUpdater struct {
	// 原子计数器
	batchCount atomic.Int64
	itemCount  atomic.Int64
	errorCount atomic.Int64

	// 时间相关字段
	startTime    time.Time
	lastDataTime time.Time
	mu           sync.Mutex // 新增互斥锁

	// 指标数据
	metrics *PipelineMetrics
}

// NewMetricsUpdater 创建新的指标更新器
func NewMetricsUpdater() *MetricsUpdater {
	now := time.Now()
	return &MetricsUpdater{
		startTime:    now,
		lastDataTime: now,
		metrics:      &PipelineMetrics{},
	}
}

// IncrementBatchCount 增加批次计数
func (u *MetricsUpdater) IncrementBatchCount() {
	u.batchCount.Add(1)
}

// IncrementItemCount 增加项目计数
func (u *MetricsUpdater) IncrementItemCount() {
	u.itemCount.Add(1)
}

// IncrementErrorCount 增加错误计数
func (u *MetricsUpdater) IncrementErrorCount() {
	u.errorCount.Add(1)
}

// UpdateProcessingMetrics 更新处理相关的指标
func (u *MetricsUpdater) UpdateProcessingMetrics() {

	u.mu.Lock()
	defer u.mu.Unlock()

	now := time.Now()
	u.metrics.ProcessingDuration = now.Sub(u.lastDataTime)
	u.lastDataTime = now

	// 更新延迟
	latency := now.Sub(u.startTime)
	u.metrics.UpdateLatency(latency)

	// 更新计数器
	u.metrics.BatchCount = u.batchCount.Load()
	u.metrics.ItemCount = u.itemCount.Load()
	u.metrics.ErrorCount = u.errorCount.Load()

	// 更新总运行时间和处理速率
	u.metrics.TotalDuration = now.Sub(u.startTime)
	u.metrics.ProcessingRate = u.metrics.GetProcessingRate()

	// 计算空闲时间
	u.metrics.IdleDuration = u.metrics.TotalDuration - u.metrics.ProcessingDuration
}

// GetMetrics 获取当前指标的快照
func (u *MetricsUpdater) GetMetrics() PipelineMetrics {
	u.mu.Lock()
	defer u.mu.Unlock()

	return u.metrics.Clone()
}

// Reset 重置指标
func (u *MetricsUpdater) Reset() {
	u.mu.Lock()
	defer u.mu.Unlock()

	now := time.Now()
	u.startTime = now
	u.lastDataTime = now
	u.batchCount.Store(0)
	u.itemCount.Store(0)
	u.errorCount.Store(0)
	u.metrics = &PipelineMetrics{}
}
