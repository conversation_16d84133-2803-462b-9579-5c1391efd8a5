//////////////////////////////////////// 熔断器实现 ////////////////////////////////////////////////////////////////
/*
熔断器(Circuit Breaker)是一种系统保护机制，主要工作原理如下：

三种状态：
Closed(关闭)：正常状态，所有请求都允许通过
Open(打开)：熔断状态，所有请求都被拒绝
Half-Open(半开)：试探状态，允许有限数量的请求通过

状态转换逻辑：
当失败次数达到阈值(连续失败3次或总失败率>50%)，从Closed→Open
Open状态持续Timeout(30秒)后自动转为Half-Open
Half-Open状态下，如果请求成功率达到阈值(连续成功5次)，转为Closed；否则回到Open

统计机制：
每Interval(10秒)重置统计周期
记录总请求数、成功数、失败数、连续成功/失败数

作用：
防止故障扩散：当依赖服务出现问题时快速失败
减轻系统负载：避免持续重试加重系统负担
自动恢复：系统恢复后自动尝试重新连接
*/

package utilities

import (
	"errors"
	"sync"
	"time"
)

// State 表示熔断器状态
type State int

const (
	StateClosed   State = iota // 关闭状态：允许所有请求通过
	StateHalfOpen              // 半开状态：允许有限数量的请求通过
	StateOpen                  // 开放状态：拒绝所有请求
)

// Counts 记录请求统计信息
type Counts struct {
	Requests             uint32 // 总请求数
	TotalSuccesses       uint32 // 总成功数
	TotalFailures        uint32 // 总失败数
	ConsecutiveSuccesses uint32 // 连续成功数
	ConsecutiveFailures  uint32 // 连续失败数
}

// BreakerSettings 熔断器配置
type BreakerSettings struct {
	Interval      time.Duration // 关闭状态下的统计周期
	Timeout       time.Duration // 开放状态转为半开状态的等待时间
	MaxRequests   uint32        // 半开状态下允许的最大请求数
	ReadyToTrip   func(counts Counts) bool
	OnStateChange func(name string, from State, to State)
}

// CircuitBreaker 熔断器实现
type CircuitBreaker struct {
	settings   BreakerSettings
	mutex      sync.Mutex
	state      State
	generation uint64
	counts     Counts
	expiry     time.Time
}

// NewCircuitBreaker 创建新的熔断器
func NewCircuitBreaker(st BreakerSettings) *CircuitBreaker {
	cb := &CircuitBreaker{
		settings: st,
	}
	cb.toNewGeneration(time.Now())
	return cb
}

// Execute 执行请求
func (cb *CircuitBreaker) Execute(req func() (interface{}, bool, error)) (interface{}, error) {
	generation, err := cb.beforeRequest()
	if err != nil {
		return nil, err
	}

	defer func() {
		if e := recover(); e != nil {
			cb.afterRequest(generation, false)
			panic(e)
		}
	}()

	result, ignore, err := req()
	cb.afterRequest(generation, ignore || err == nil)
	return result, err
}

func (cb *CircuitBreaker) beforeRequest() (uint64, error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)

	if state == StateOpen {
		return generation, errors.New("circuit breaker is open")
	} else if state == StateHalfOpen && cb.counts.Requests >= cb.settings.MaxRequests {
		return generation, errors.New("too many requests in half-open state")
	}

	cb.counts.Requests++
	return generation, nil
}

func (cb *CircuitBreaker) afterRequest(before uint64, success bool) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)
	if generation != before {
		return
	}

	if success {
		cb.onSuccess(state, now)
	} else {
		cb.onFailure(state, now)
	}
}

func (cb *CircuitBreaker) onSuccess(state State, now time.Time) {
	switch state {
	case StateClosed:
		cb.counts.TotalSuccesses++
		cb.counts.ConsecutiveSuccesses++
		cb.counts.ConsecutiveFailures = 0
	case StateHalfOpen:
		cb.counts.TotalSuccesses++
		cb.counts.ConsecutiveSuccesses++
		cb.counts.ConsecutiveFailures = 0
		if cb.counts.ConsecutiveSuccesses >= cb.settings.MaxRequests {
			cb.setState(StateClosed, now)
		}
	}
}

func (cb *CircuitBreaker) onFailure(state State, now time.Time) {
	switch state {
	case StateClosed:
		cb.counts.TotalFailures++
		cb.counts.ConsecutiveFailures++
		cb.counts.ConsecutiveSuccesses = 0
		if cb.settings.ReadyToTrip(cb.counts) {
			cb.setState(StateOpen, now)
		}
	case StateHalfOpen:
		cb.setState(StateOpen, now)
	}
}

func (cb *CircuitBreaker) currentState(now time.Time) (State, uint64) {
	switch cb.state {
	case StateClosed:
		if !cb.expiry.IsZero() && cb.expiry.Before(now) {
			cb.toNewGeneration(now)
		}
	case StateOpen:
		if cb.expiry.Before(now) {
			cb.setState(StateHalfOpen, now)
		}
	}
	return cb.state, cb.generation
}

func (cb *CircuitBreaker) setState(state State, now time.Time) {
	if cb.state == state {
		return
	}

	prev := cb.state
	cb.state = state

	cb.toNewGeneration(now)

	if cb.settings.OnStateChange != nil {
		cb.settings.OnStateChange("", prev, state)
	}
}

func (cb *CircuitBreaker) toNewGeneration(now time.Time) {
	cb.generation++
	cb.counts = Counts{}

	var zero time.Time
	switch cb.state {
	case StateClosed:
		if cb.settings.Interval > 0 {
			cb.expiry = now.Add(cb.settings.Interval)
		} else {
			cb.expiry = zero
		}
	case StateOpen:
		cb.expiry = now.Add(cb.settings.Timeout)
	default: // StateHalfOpen
		cb.expiry = zero
	}
}
