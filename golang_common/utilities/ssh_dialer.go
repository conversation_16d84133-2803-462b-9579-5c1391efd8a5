package utilities

import (
	"context"
	"crypto/tls"
	"database/sql/driver"
	"net"
	"time"

	"github.com/lib/pq"
	"golang.org/x/crypto/ssh"
)

type ViaSSHDialer struct {
	client *ssh.Client
}

func (d *ViaSSHDialer) Dial(ctx context.Context, address string) (net.Conn, error) {
	return d.client.Dial("tcp", address)
}

func (d *ViaSSHDialer) DialForClickhouse(network, address string, timeout time.Duration, config *tls.Config) (net.Conn, error) {
	return d.client.Dial("tcp", address)
}

func (d *ViaSSHDialer) DialForRedis(ctx context.Context, network, address string) (net.Conn, error) {
	return d.client.Dial("tcp", address)
}

func NewViaSSHDialer(client *ssh.Client) *ViaSSHDialer {
	return &ViaSSHDialer{client}
}

type PostgresViaSSHDialer struct {
	client *ssh.Client
}

func (d *PostgresViaSSHDialer) Open(s string) (_ driver.Conn, err error) {
	return pq.Dial<PERSON>pen(d, s)
}

func (d *PostgresViaSSHDialer) Dial(network, address string) (net.Conn, error) {
	return d.client.Dial(network, address)
}

func (d *PostgresViaSSHDialer) DialTimeout(network, address string, timeout time.Duration) (net.Conn, error) {
	return d.client.Dial(network, address)
}

func NewPostgresViaSSHDialer(client *ssh.Client) *PostgresViaSSHDialer {
	return &PostgresViaSSHDialer{client}
}
