# Utilities 工具包

这个包提供了一系列实用的工具函数，用于处理环境变量、时间操作、运行时信息、SSH连接和字符串处理等常见任务。

## 环境变量工具 (env.go)

提供了一系列获取环境变量的函数，支持多种数据类型：

- `GetEnv(key, defaultValue string)`: 获取字符串类型的环境变量
- `GetEnvInt(key string, defaultValue int)`: 获取整数类型的环境变量
- `GetEnvInt64(key string, defaultValue int64)`: 获取64位整数类型的环境变量
- `GetEnvBool(key string, defaultValue bool)`: 获取布尔类型的环境变量
- `GetEnvStringArray(key string, defaultValue []string)`: 获取字符串数组类型的环境变量
- `GetEnvIntArray(key string, defaultValue []int)`: 获取整数数组类型的环境变量
- `GetEnvInt64Array(key string, defaultValue []int64)`: 获取64位整数数组类型的环境变量

## 时间工具 (time.go)

### 基础时间操作函数

- `GetTomorrowDDHHMMSS()`: 获取明天的日期和时间
- `GetNowDDHHMMSS()`: 获取当前的日期和时间
- `GetLastMinuteDDHHMMSS()`: 获取上一分钟的日期和时间
- `GetLastTenMinuteDDHHMMSS()`: 获取十分钟前的日期和时间
- `GetLastHourDDHHMMSS()`: 获取一小时前的日期和时间
- `GetLastTowHourDDHHMMSS()`: 获取两小时前的日期和时间
- `GetYesterdayDDHHMMSS()`: 获取昨天的日期和时间
- `GetDaysAgoDDHHMMSS(days int)`: 获取指定天数前的日期和时间
- `GetDaysAgoShortDDHHMMSS(days int)`: 获取指定天数前的短格式日期和时间
- `GetNowString()`: 获取当前时间的标准格式字符串
- `GetNowShortString()`: 获取当前时间的短格式字符串
- `GetTimeFromString(timeString string)`: 将时间字符串解析为time.Time类型

### TTL相关函数详细分析

#### 1. RandomTTL
- 实现：使用math/rand包生成随机数
- 特点：基于时间戳作为种子的伪随机数
- 适用场景：一般性的TTL随机化，对安全性要求不高的场景

#### 2. CRandomTTL
- 实现：使用crypto/rand包生成加密安全的随机数
- 特点：
  * 提供更高的随机性和安全性
  * 性能相对较低
  * 包含panic恢复机制
- 适用场景：需要高安全性的场景，如密钥过期时间设置

#### 3. QuickRandomTTL
- 实现：直接使用math/rand.Int63n生成随机数
- 特点：
  * 性能最高
  * 随机性相对较低
- 适用场景：高性能场景，对随机分布要求不高的情况

#### 4. RandTTLWithAmount
- 实现：基于数据量动态计算TTL范围，使用crypto/rand生成随机数
- 特点：
  * 根据数据量自适应TTL范围
  * 使用加密安全的随机数
  * 包含环境变量配置选项
- 适用场景：需要根据数据量调整过期时间的场景，如缓存管理

#### 5. QuickRandTTLWithAmount
- 实现：类似RandTTLWithAmount，但使用math/rand代替crypto/rand
- 特点：
  * 比RandTTLWithAmount性能更好
  * 保持数据量自适应特性
- 适用场景：大规模缓存系统，需要性能优先的场景

#### 6. QuickRandTTLWithAmountFromTomorrowMidnightToDaysLater
- 实现：结合明天凌晨时间点和数据量计算TTL
- 特点：
  * 确保TTL在指定的未来时间范围内
  * 考虑了时区因素
  * 使用快速的伪随机数生成
- 适用场景：需要在特定未来时间段内分散过期时间的场景

## 运行时信息工具 (runtime.go)

提供了运行时信息的打印功能：

- `PrintRuntimeInfo()`: 打印当前运行时信息
- `PrintRuntimeInfoWithTag(tag string)`: 打印带标签的运行时信息

包含的信息：
- GOMAXPROCS
- CPU核心数
- CGO调用次数
- Goroutine数量
- 内存分配情况
- 系统总内存和空闲内存

## SSH连接工具 (ssh_dialer.go)

提供了通过SSH进行网络连接的功能：

- `ViaSSHDialer`: 通用SSH连接器
- `PostgressViaSSHDialer`: PostgreSQL专用SSH连接器

## 字符串工具 (strings.go)

提供了字符串处理相关的功能：

- `EscapeSQLString(sqlString string)`: SQL字符串转义
- `Md5String(str string)`: 计算字符串的MD5值
- `Md5HalfString(str string)`: 计算字符串的部分MD5值
- `Md5HalfStringToUInt64(str string)`: MD5值转换为uint64
- `UInt64SplitToInt32(val uint64)`: uint64拆分为两个int32
- `Md5HalfStringToInt32(str string)`: MD5值转换为两个int32

# HTTP 客户端实现说明

本项目提供了两种 HTTP 客户端实现：基于 `go-resty` 的 `HttpClient` 和基于 `fasthttp` 的 `FastHttpClient`。这两种实现各有特点，适用于不同的场景。

## 适用场景

- fasthttp：极致高并发、低延迟、短连接、无 HTTP/2 需求、对性能极致敏感的广告投放、RTB、竞价等场景。
- resty（net/http）：需要 HTTP/2、功能丰富、兼容性好、业务复杂、对性能要求不是极致的场景。

## 性能对比

根据基准测试结果，在相同条件下：
- FastHTTP 平均操作时间：9,881 ns/op
- Resty 平均操作时间：11,646 ns/op
（⚠️ 注意：Resty默认回包会zip自动解压缩，fasthttp不会）

FastHTTP 相比 Resty 性能提升约 15.2%。


## go-resty 客户端 (HttpClient)

### 优点
1. **功能丰富**
   - 内置请求重试机制
   - 支持请求/响应拦截器
   - 支持请求/响应中间件
   - 自动处理 JSON 序列化/反序列化
   - 支持文件上传/下载
   - 支持 Cookie 管理

2. **使用简单**
   - API 设计友好，易于使用
   - 链式调用风格
   - 详细的错误信息
   - 完善的文档和社区支持

3. **可扩展性**
   - 易于添加自定义功能
   - 支持自定义中间件
   - 灵活的配置选项

### 缺点
1. **性能相对较低**
   - 相比 FastHTTP 性能略低
   - 内存使用量较大
   - 对象创建开销较大

2. **资源消耗**
   - 每个请求都会创建新的对象
   - 垃圾回收压力较大

### 适用场景
1. 需要丰富功能的通用 HTTP 客户端
2. 开发阶段和原型验证
3. 对性能要求不是特别高的场景
4. 需要处理复杂 HTTP 交互的场景
5. 需要良好可维护性的项目

## fasthttp 客户端 (FastHttpClient)

### 优点
1. **高性能**
   - 极低的内存分配
   - 对象池复用
   - 零内存分配设计
   - 高效的连接管理

2. **资源效率**
   - 最小化内存使用
   - 减少垃圾回收压力
   - 高效的连接复用

3. **并发处理**
   - 优秀的并发性能
   - 高效的连接池管理
   - 适合高并发场景

### 缺点
1. **功能相对简单**
   - 需要手动处理更多细节
   - 缺少一些高级特性
   - 错误处理相对复杂

2. **使用复杂度**
   - API 设计相对底层
   - 需要更多手动配置
   - 学习曲线较陡

3. **兼容性**
   - 不完全兼容标准库
   - 某些特殊场景可能需要额外处理

## 选择建议

### 选择 go-resty 的情况
1. 项目需要丰富的 HTTP 功能
2. 开发效率优先于性能
3. 需要处理复杂的 HTTP 交互
4. 团队更熟悉标准库风格的 API
5. 需要良好的可维护性和可扩展性

### 选择 fasthttp 的情况
1. 性能是首要考虑因素
2. 需要处理高并发请求
3. 资源使用需要优化
4. 服务需要处理大量 HTTP 请求
5. 对延迟要求极高的场景

## 使用示例

### go-resty 客户端
```go
client := GetHTTPClient()
response, statusCode, err := client.Do(
    context.Background(),
    http.MethodGet,
    "https://api.example.com",
    WithHeaders(map[string]string{
        "Content-Type": "application/json",
    }),
    WithQuery(map[string]string{
        "key": "value",
    }),
)
```

### fasthttp 客户端
```go
client := NewFastClient(
    WithRetries(2),
)
defer client.Close()

response, statusCode, err := client.Do(
    context.Background(),
    http.MethodGet,
    "https://api.example.com",
    WithHeaders(map[string]string{
        "Content-Type": "application/json",
    }),
    WithQuery(map[string]string{
        "key": "value",
    }),
)
```