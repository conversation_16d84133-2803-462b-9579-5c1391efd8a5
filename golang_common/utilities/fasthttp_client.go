package utilities

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/url"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
)

// FastHttpClient 使用fasthttp的HTTP客户端
type FastHttpClient struct {
	client   *fasthttp.Client
	Config   *ClientConfig
	resolver *Resolver
}

// NewFastClient 创建新的fasthttp客户端
func NewFastClient(opts ...ClientOption) *FastHttpClient {
	// 使用默认配置创建ClientConfig
	config := &ClientConfig{
		Retries:    DefaultRetries,
		IsDebugLog: false,
	}

	// 应用选项
	for _, opt := range opts {
		opt(config)
	}

	dnsResolver := NewCache(context.Background())

	// 创建fasthttp客户端
	client := &fasthttp.Client{
		MaxConnsPerHost:               1000,              // 【连接池核心参数】每个主机（host:port）最大连接数，超出后新请求会等待或报错。适合高并发业务。
		ReadTimeout:                   DefaultTimeout,    // 单次请求的读超时时间（包括从服务器读取响应的时间）
		WriteTimeout:                  DefaultTimeout,    // 单次请求的写超时时间（包括向服务器发送请求的时间）
		MaxIdleConnDuration:           120 * time.Second, // 连接池中空闲连接最大存活时间，超时后自动关闭
		NoDefaultUserAgentHeader:      true,              // 不自动添加 User-Agent 头，需业务自行设置
		DisableHeaderNamesNormalizing: true,              // 禁用 header 名字规范化，header 区分大小写
		MaxConnDuration:               0,                 // 单个连接最大生命周期，0 表示不限制（适合长连接场景）
		MaxConnWaitTimeout:            0,                 // 获取空闲连接最大等待时间，0 表示不等待（高并发下建议设置合理值防止阻塞）
		ReadBufferSize:                0,                 // 读缓冲区大小，0 表示使用默认（4KB），可根据响应体大小调整
		WriteBufferSize:               0,                 // 写缓冲区大小，0 表示使用默认（4KB），可根据请求体大小调整
		MaxResponseBodySize:           0,                 // 响应体最大字节数，0 表示不限制，防止大包攻击可设置上限
		DisablePathNormalizing:        false,             // 是否禁用路径标准化，通常保持 false
		ConnPoolStrategy:              fasthttp.LIFO,     // 连接池策略，LIFO（后进先出，性能更优）或 LRU（最近最少使用）
		Dial: func(addr string) (net.Conn, error) {
			separator := strings.LastIndex(addr, ":")
			if separator < 0 {
				return nil, fmt.Errorf("invalid address format: %s", addr)
			}

			host := addr[:separator]
			port := addr[separator:]

			// 尝试从缓存获取IP
			ip, err := dnsResolver.FetchOneString(host)
			if err == nil {
				return fasthttp.Dial(ip + port)
			}

			// 缓存解析失败时，回退到系统默认DNS解析
			return fasthttp.Dial(addr)
		},
	}

	return &FastHttpClient{
		client:   client,
		Config:   config,
		resolver: dnsResolver,
	}
}

// DoWithTimeout 使用指定的超时时间执行HTTP请求
func (c *FastHttpClient) DoWithTimeout(ctx context.Context, timeout time.Duration, method, url string, opts ...RequestOption) (body []byte, statusCode int, err error) {
	if timeout <= 0 {
		return c.Do(ctx, method, url, opts...)
	}

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	body, statusCode, err = c.Do(timeoutCtx, method, url, opts...)

	if err != nil && timeoutCtx.Err() == context.DeadlineExceeded {
		return nil, fasthttp.StatusGatewayTimeout, fmt.Errorf("request timeout=%v, statusCode=%v, err=%v", timeout, statusCode, err)
	}
	return body, statusCode, err
}

// Do 执行HTTP请求
func (c *FastHttpClient) Do(ctx context.Context, method, url string, opts ...RequestOption) (body []byte, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
			log.Printf("panic: %v\n, stack:%v\n", r, GetStack())
			statusCode = fasthttp.StatusInternalServerError
		}
	}()

	// 准备请求配置
	reqConfig := requestConfig{}
	for _, opt := range opts {
		opt(&reqConfig)
	}

	return c.executeWithRetry(ctx, method, url, &reqConfig)
}

// executeWithRetry 实现带重试的请求执行逻辑
func (c *FastHttpClient) executeWithRetry(ctx context.Context, method, url string, reqConfig *requestConfig) (result []byte, statusCode int, err error) {
	maxRetryCount := c.Config.Retries
	for i := 0; i <= maxRetryCount; i++ {
		if result, statusCode, err = c.executeRawRequest(ctx, method, url, reqConfig); err != nil {
			continue
		}

		// 成功响应
		if statusCode >= fasthttp.StatusOK && statusCode < fasthttp.StatusMultipleChoices {
			return result, statusCode, nil
		}

		// 不需要重试
		if !c.shouldRetry(statusCode) {
			return result, statusCode, err
		}
	}

	return result, statusCode, fmt.Errorf("timeout return")
}

// executeRawRequest 执行原始请求
func (c *FastHttpClient) executeRawRequest(ctx context.Context, method string, urlStr string, reqConfig *requestConfig) ([]byte, int, error) {
	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)
	defer fasthttp.ReleaseResponse(resp)

	// 设置请求方法
	req.Header.SetMethod(method)
	req.SetRequestURI(urlStr)

	// 设置请求参数
	if reqConfig.queryEx != nil {
		queryStr := reqConfig.queryEx.Encode()
		if strings.Contains(urlStr, "?") {
			req.SetRequestURI(urlStr + "&" + queryStr)
		} else {
			req.SetRequestURI(urlStr + "?" + queryStr)
		}
	} else if reqConfig.query != nil {
		queryParams := url.Values{}
		for k, v := range reqConfig.query {
			queryParams.Add(k, v)
		}
		queryStr := queryParams.Encode()
		if strings.Contains(urlStr, "?") {
			req.SetRequestURI(urlStr + "&" + queryStr)
		} else {
			req.SetRequestURI(urlStr + "?" + queryStr)
		}
	}

	// 设置表单数据
	if reqConfig.formData != nil {
		formValues := url.Values{}
		for k, v := range reqConfig.formData {
			formValues.Add(k, v)
		}
		req.SetBodyString(formValues.Encode())
	}

	// 设置JSON bodyBytes
	if len(reqConfig.bodyBytes) > 0 {
		req.SetBody(reqConfig.bodyBytes)
	}

	// 设置请求头
	if reqConfig.headers != nil {
		for k, v := range reqConfig.headers {
			req.Header.Set(k, v)
		}
	}

	// 执行请求
	err := c.client.Do(req, resp)
	if c.Config.IsDebugLog {
		// 优化处理query和queryEx参数的情况
		var queryParams map[string]string
		if reqConfig.queryEx != nil {
			// 如果queryEx有值，将其转换为map[string]string格式
			queryParams = make(map[string]string)
			for k, values := range reqConfig.queryEx {
				if len(values) > 0 {
					// 如果有多个值，用逗号分隔
					queryParams[k] = strings.Join(values, ",")
				}
			}
		} else {
			// 使用原有的query参数
			queryParams = reqConfig.query
		}

		curl := GetCurlCommand(method, urlStr, reqConfig.headers, reqConfig.formData, string(reqConfig.bodyBytes), queryParams)
		log.Println(curl)
		log.Printf("Response: %s\n", resp.Body())
	}

	if err != nil {
		return nil, 0, err
	}

	return resp.Body(), resp.StatusCode(), nil
}

// shouldRetry 判断是否需要重试
func (c *FastHttpClient) shouldRetry(statusCode int) bool {
	if c.Config.Retries <= 0 {
		return false
	}

	return statusCode == fasthttp.StatusTooManyRequests ||
		statusCode == fasthttp.StatusRequestTimeout ||
		statusCode >= fasthttp.StatusInternalServerError
}

// Close 关闭客户端
func (c *FastHttpClient) Close() {
	if c.resolver != nil {
		c.resolver.Close()
		c.resolver = nil
	}
}
