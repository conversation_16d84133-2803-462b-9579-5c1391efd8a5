package utilities

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httptest"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// globalHTTPClient 全局HTTP客户端
var (
	httpClient     *HttpClient
	httpClientOnce sync.Once
)

// GetGlobalHTTPClient 获取全局HTTP客户端（单例模式）
func GetHTTPClient() *HttpClient {
	httpClientOnce.Do(func() {
		httpClient = NewClient(
		// WithDebugLog(true),
		)
	})
	return httpClient
}

func CloseHTTPClient() {
	httpClient.Close()
}

// restyV2的性能：
// goos: darwin
// goarch: arm64
// pkg: codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities
// cpu: Apple M4
// BenchmarkHTTPClientPerformance/GetHTTPClient().DoWithTimeout-10         	     106	  11074666 ns/op
// BenchmarkHTTPClientPerformance/StandardHTTPClient-10                    	     100	  10875093 ns/op

func BenchmarkHTTPClientPerformance(b *testing.B) {
	// 创建一个本地HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟服务器处理延迟
		time.Sleep(10 * time.Millisecond)

		// 读取请求体
		body, _ := io.ReadAll(r.Body)

		// 返回请求体作为响应
		w.WriteHeader(http.StatusOK)
		w.Write(body)
	}))
	defer server.Close()

	// 测试数据
	testData := []byte("这是一个测试请求体")
	ctx := context.Background()

	// 测试GetHTTPClient().DoWithTimeout
	b.Run("GetHTTPClient().DoWithTimeout", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, _ = GetHTTPClient().DoWithTimeout(
				ctx,
				200*time.Millisecond,
				http.MethodPost,
				server.URL,
				WithHeaders(map[string]string{"Content-Type": "application/json"}),
				WithJSONBody(testData),
			)
		}
	})

	// 测试标准库http.Client
	b.Run("StandardHTTPClient", func(b *testing.B) {
		client := &http.Client{Timeout: 200 * time.Millisecond}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			req, _ := http.NewRequestWithContext(ctx, http.MethodPost, server.URL, bytes.NewReader(testData))
			req.Header.Set("Content-Type", "application/json")
			resp, _ := client.Do(req)
			if resp != nil {
				_, _ = io.ReadAll(resp.Body)
				resp.Body.Close()
			}
		}
	})
}

// 测试不同负载下的性能
// restyV2的性能：
// 负载类型: 小负载 (100 字节)
// GoResty 平均耗时: 11.071054ms
// StandardHTTP 平均耗时: 10.83857ms
// StandardHTTP 方法快 2.10%

// 负载类型: 中负载 (10kb)
// GoResty 平均耗时: 11.466421ms
// StandardHTTP 平均耗时: 11.298539ms
// StandardHTTP 方法快 1.46%

// 负载类型: 大负载 (100 kb)
// GoResty 平均耗时: 11.386583ms
// StandardHTTP 平均耗时: 11.053203ms
// StandardHTTP 方法快 2.93%

// 负载类型: 超大负载 (1 MB)
// GoResty 平均耗时: 13.775684ms
// StandardClient 平均耗时: 13.275937ms
// StandardClient 方法快 3.63%
func TestHTTPClientWithDifferentPayloads(t *testing.T) {
	// 创建一个本地HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟服务器处理延迟
		time.Sleep(10 * time.Millisecond)

		// 读取请求体
		body, _ := io.ReadAll(r.Body)

		// 返回请求体作为响应
		w.WriteHeader(http.StatusOK)
		w.Write(body)
	}))
	defer server.Close()

	// 测试不同大小的负载
	payloads := []struct {
		name string
		size int
	}{
		{"小负载", 100},
		{"中负载", 10 * 1024},         // 10KB
		{"大负载", 100 * 1024},        // 100KB
		{"超大负载", 1024 * 1024},      // 1MB
		{"巨大负载", 10 * 1024 * 1024}, // 10MB
	}

	ctx := context.Background()
	iterations := 1000

	for _, payload := range payloads {
		// 生成测试数据
		testData := make([]byte, payload.size)
		for i := 0; i < payload.size; i++ {
			testData[i] = byte(i % 256)
		}

		// 测试GetHTTPClient().DoWithTimeout
		doWithTimeoutTotal := time.Duration(0)
		for i := 0; i < iterations; i++ {
			start := time.Now()
			_, _, _ = GetHTTPClient().DoWithTimeout(
				ctx,
				1000*time.Millisecond, // 增加超时时间以适应大负载
				http.MethodPost,
				server.URL,
				WithHeaders(map[string]string{"Content-Type": "application/octet-stream"}),
				WithJSONBody(testData),
			)
			doWithTimeoutTotal += time.Since(start)
		}
		doWithTimeoutAvg := doWithTimeoutTotal / time.Duration(iterations)

		// 测试标准库http.Client
		standardClientTotal := time.Duration(0)
		client := &http.Client{Timeout: 1000 * time.Millisecond} // 增加超时时间以适应大负载
		for i := 0; i < iterations; i++ {
			start := time.Now()
			req, _ := http.NewRequestWithContext(ctx, http.MethodPost, server.URL, bytes.NewReader(testData))
			req.Header.Set("Content-Type", "application/octet-stream")
			resp, _ := client.Do(req)
			if resp != nil {
				_, _ = io.ReadAll(resp.Body)
				resp.Body.Close()
			}
			standardClientTotal += time.Since(start)
		}
		standardClientAvg := standardClientTotal / time.Duration(iterations)

		// 输出结果
		log.Printf("负载类型: %s (%d 字节)\n", payload.name, payload.size)
		log.Printf("DoWithTimeout 平均耗时: %v\n", doWithTimeoutAvg)
		log.Printf("StandardClient 平均耗时: %v\n", standardClientAvg)

		// 计算性能差异
		var diff float64
		var faster string
		if doWithTimeoutAvg < standardClientAvg {
			diff = float64(standardClientAvg-doWithTimeoutAvg) / float64(standardClientAvg) * 100
			faster = "DoWithTimeout"
		} else {
			diff = float64(doWithTimeoutAvg-standardClientAvg) / float64(doWithTimeoutAvg) * 100
			faster = "StandardClient"
		}
		log.Printf("%s 方法快 %.2f%%\n\n", faster, diff)
	}
}

// 测试并发请求性能
// GoResty vs StdHttp 并发性能对比：
// ------------------------------------------------------------------------------------------------------------
//
//	qps                  GoResty耗时                     StdHttp耗时                   结果
//
// ------------------------------------------------------------------------------------------------------------
// 并发级别:1(qps),		GoResty 总耗时: 25.512667ms		StdHttp 总耗时: 21.541083ms		StdHttp 方法快 15.57%
// 并发级别:10(qps),	GoResty 总耗时: 21.48975ms		StdHttp 总耗时: 21.480333ms		StdHttp 方法快 0.04%
// 并发级别:100(qps),	GoResty 总耗时: 22.7575ms		StdHttp 总耗时: 22.950917ms		GoResty 方法快 0.84%
// 并发级别:500(qps),	GoResty 总耗时: 34.096291ms		StdHttp 总耗时: 41.510541ms		GoResty 方法快 17.86%
// 并发级别:1000(qps),	GoResty 总耗时: 46.516667ms		StdHttp 总耗时: 59.490792ms		GoResty 方法快 21.81%
// 并发级别:1500(qps),	GoResty 总耗时: 56.312541ms		StdHttp 总耗时: 70.628917ms		GoResty 方法快 20.27%
// 并发级别:5000(qps),	GoResty 总耗时: 170.754792ms	StdHttp 总耗时: 546.268667ms	GoResty 方法快 68.74%
// 并发级别:9999(qps),	GoResty 总耗时: 275.863333ms	StdHttp 总耗时: 646.97575ms		GoResty 方法快 57.36%
func TestHTTPClientConcurrentRequests(t *testing.T) {
	// 创建一个本地HTTP服务器
	// server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 	// 模拟服务器处理延迟
	// 	time.Sleep(20 * time.Millisecond)

	// 	// 读取请求体
	// 	body, _ := io.ReadAll(r.Body)

	// 	// 返回请求体作为响应
	// 	w.WriteHeader(http.StatusOK)
	// 	w.Write(body)
	// }))
	// defer server.Close()

	// 测试数据
	testData := []byte("这是一个测试请求体")
	ctx := context.Background()

	// 测试不同的并发级别
	concurrencyLevels := []int{1, 10, 100, 500, 1000, 1500, 5000, 9999}

	testUrl := "https://sandbox.maplehaze.cn/api/test"
	for _, concurrency := range concurrencyLevels {
		fmt.Printf("并发级别:%d(qps) \t", concurrency)

		// 测试GetHTTPClient().DoWithTimeout
		doWithTimeoutStart := time.Now()
		done := make(chan bool, concurrency)

		for i := 0; i < concurrency; i++ {
			go func() {
				_, _, _ = GetHTTPClient().DoWithTimeout(
					ctx,
					500*time.Millisecond,
					http.MethodPost,
					testUrl,
					WithHeaders(map[string]string{"Content-Type": "application/json"}),
					WithJSONBody(testData),
				)
				done <- true
			}()
		}

		// 等待所有请求完成
		for i := 0; i < concurrency; i++ {
			<-done
		}
		doWithTimeoutElapsed := time.Since(doWithTimeoutStart)

		// 测试标准库http.Client
		standardClientStart := time.Now()
		client := &http.Client{Timeout: 500 * time.Millisecond}

		for i := 0; i < concurrency; i++ {
			go func() {
				req, _ := http.NewRequestWithContext(ctx, http.MethodPost, testUrl, bytes.NewReader(testData))
				req.Header.Set("Content-Type", "application/json")
				resp, _ := client.Do(req)
				if resp != nil {
					_, _ = io.ReadAll(resp.Body)
					resp.Body.Close()
				}
				done <- true
			}()
		}

		// 等待所有请求完成
		for i := 0; i < concurrency; i++ {
			<-done
		}
		standardClientElapsed := time.Since(standardClientStart)

		// 输出结果
		fmt.Printf("GoResty 总耗时: %v\t", doWithTimeoutElapsed)
		fmt.Printf("StdHttp 总耗时: %v\t", standardClientElapsed)

		// 计算性能差异
		var diff float64
		var faster string
		if doWithTimeoutElapsed < standardClientElapsed {
			diff = float64(standardClientElapsed-doWithTimeoutElapsed) / float64(standardClientElapsed) * 100
			faster = "GoResty"
		} else {
			diff = float64(doWithTimeoutElapsed-standardClientElapsed) / float64(doWithTimeoutElapsed) * 100
			faster = "StdHttp"
		}
		fmt.Printf("%s 方法快 %.2f%%\n", faster, diff)
	}
}

// TestHTTPClientConcurrencyBenchmark 测试不同QPS下HTTP客户端的性能表现
func TestHTTPClientConcurrencyBenchmark(t *testing.T) {
	// 测试的URL
	url1 := "https://sandbox.maplehaze.cn/api/test"
	url2 := "https://sandbox2.maplehaze.cn/api/test"

	// 测试的QPS级别
	qpsLevels := []int{10, 100, 200, 500, 1000, 2000, 5000}

	// 每个QPS级别测试的持续时间
	testDuration := 10 * time.Second

	// 创建HTTP客户端
	client := NewClient(
		WithRetries(0),      // 禁用重试以便更准确地测量性能
		WithDebugLog(false), // 关闭调试日志
	)
	defer client.Close()

	fmt.Println("开始HTTP客户端并发压力测试...")
	fmt.Println("============================================================================================================================")
	fmt.Printf("%-10s | %-20s | %-15s | %-10s | %-15s | %-10s | %-10s\n",
		"QPS", "URL", "平均响应时间(ms)", "成功请求数", "失败请求数", "成功率(%)", "实际QPS")
	fmt.Println("----------------------------------------------------------------------------------------------------------------------------")

	// 对每个QPS级别进行测试
	for _, qps := range qpsLevels {
		// 测试URL1
		successCount1, failCount1, avgResponseTime1, actualQPS1 := benchmarkURLV2(t, url1, qps, testDuration)
		successRate1 := float64(successCount1) / float64(successCount1+failCount1) * 100

		// 测试URL2
		successCount2, failCount2, avgResponseTime2, actualQPS2 := benchmarkURLV2(t, url2, qps, testDuration)
		successRate2 := float64(successCount2) / float64(successCount2+failCount2) * 100

		// 输出结果
		fmt.Printf("%-10d | %-20s | %-20.2f | %-15d | %-15d | %-10.2f | %-10.2f\n",
			qps, "sandbox1", avgResponseTime1, successCount1, failCount1, successRate1, actualQPS1)
		fmt.Printf("%-10d | %-20s | %-20.2f | %-15d | %-15d | %-10.2f | %-10.2f\n",
			qps, "sandbox2", avgResponseTime2, successCount2, failCount2, successRate2, actualQPS2)

		// 比较两个URL的性能
		if avgResponseTime1 < avgResponseTime2 {
			fasterPct := (avgResponseTime2 - avgResponseTime1) / avgResponseTime2 * 100
			fmt.Printf("QPS=%d: sandbox1比sandbox2快 %.2f%%\n", qps, fasterPct)
		} else {
			fasterPct := (avgResponseTime1 - avgResponseTime2) / avgResponseTime1 * 100
			fmt.Printf("QPS=%d: sandbox2比sandbox1快 %.2f%%\n", qps, fasterPct)
		}
		fmt.Println("----------------------------------------------------------------------------------------------------------------------------")
	}
}

// benchmarkURLV2 使用标准HTTP库对指定URL进行压力测试
func benchmarkURLV2(t *testing.T, url string, targetQPS int, duration time.Duration) (int64, int64, float64, float64) {
	var (
		successCount int64 = 0
		failCount    int64 = 0
		totalLatency int64 = 0
		requestCount int64 = 0
		startTime          = time.Now()
	)

	// 创建标准HTTP客户端
	client := &http.Client{
		Timeout: 2 * time.Second,
		// Transport: &http.Transport{
		// 	MaxIdleConns:        2000,
		// 	MaxIdleConnsPerHost: 500,
		// 	MaxConnsPerHost:     1000,
		// 	IdleConnTimeout:     120 * time.Second,
		// 	ForceAttemptHTTP2:   true,
		// },
	}

	// 计算请求间隔
	interval := time.Second / time.Duration(targetQPS)

	// 创建上下文，用于控制测试持续时间
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()

	// 创建等待组，用于等待所有goroutine完成
	var wg sync.WaitGroup

	// 启动发送请求的goroutine
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				wg.Add(1)
				go func() {
					defer wg.Done()

					// 记录请求开始时间
					start := time.Now()

					// 创建请求
					reqCtx, reqCancel := context.WithTimeout(context.Background(), 2*time.Second)
					defer reqCancel()

					req, err := http.NewRequestWithContext(reqCtx, http.MethodGet, url, nil)
					if err != nil {
						atomic.AddInt64(&failCount, 1)
						return
					}

					// 发送请求
					resp, err := client.Do(req)

					// 计算请求耗时
					latency := time.Since(start).Milliseconds()

					// 原子操作更新计数器
					atomic.AddInt64(&requestCount, 1)
					atomic.AddInt64(&totalLatency, latency)

					if err != nil {
						atomic.AddInt64(&failCount, 1)
					} else {
						statusCode := resp.StatusCode
						if statusCode >= 400 {
							atomic.AddInt64(&failCount, 1)
						} else {
							atomic.AddInt64(&successCount, 1)
						}
						// 确保响应体被读取并关闭
						if resp.Body != nil {
							_, _ = io.ReadAll(resp.Body)
							resp.Body.Close()
						}
					}
				}()
			}
		}
	}()

	// 等待测试持续时间结束
	<-ctx.Done()

	// 等待所有请求完成
	wg.Wait()

	// 计算实际测试持续时间
	actualDuration := time.Since(startTime).Seconds()

	// 计算平均响应时间和实际QPS
	avgResponseTime := float64(totalLatency) / float64(successCount+failCount)
	actualQPS := float64(successCount+failCount) / actualDuration

	return successCount, failCount, avgResponseTime, actualQPS
}

// benchmarkURL 对指定URL进行压力测试
func benchmarkURL(t *testing.T, client *HttpClient, url string, targetQPS int, duration time.Duration) (int64, int64, float64, float64) {
	var (
		successCount int64 = 0
		failCount    int64 = 0
		totalLatency int64 = 0
		requestCount int64 = 0
		startTime          = time.Now()
	)

	// 计算请求间隔
	interval := time.Second / time.Duration(targetQPS)

	// 创建上下文，用于控制测试持续时间
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()

	// 创建等待组，用于等待所有goroutine完成
	var wg sync.WaitGroup

	// 启动发送请求的goroutine
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				wg.Add(1)
				go func() {
					defer wg.Done()

					// 记录请求开始时间
					start := time.Now()

					// 发送请求
					_, statusCode, err := client.DoWithTimeout(context.Background(), 2*time.Second, http.MethodGet, url)

					// 计算请求耗时
					latency := time.Since(start).Milliseconds()

					// 原子操作更新计数器
					atomic.AddInt64(&requestCount, 1)
					atomic.AddInt64(&totalLatency, latency)

					if err != nil || statusCode >= 400 {
						atomic.AddInt64(&failCount, 1)
					} else {
						atomic.AddInt64(&successCount, 1)
					}
				}()
			}
		}
	}()

	// 等待测试持续时间结束
	<-ctx.Done()

	// 等待所有请求完成
	wg.Wait()

	// 计算实际测试持续时间
	actualDuration := time.Since(startTime).Seconds()

	// 计算平均响应时间和实际QPS
	avgResponseTime := float64(totalLatency) / float64(successCount+failCount)
	actualQPS := float64(successCount+failCount) / actualDuration

	return successCount, failCount, avgResponseTime, actualQPS
}

func BenchmarkHttpClient(b *testing.B) {
	// 创建测试服务器
	// 创建一个本地HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟服务器处理延迟
		// time.Sleep(10 * time.Millisecond)

		// 读取请求体
		body, _ := io.ReadAll(r.Body)

		// 返回请求体作为响应
		w.WriteHeader(http.StatusOK)
		w.Write(body)
	}))
	defer server.Close()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 测试数据
	testURL := server.URL
	testHeaders := map[string]string{
		"Content-Type": "application/json",
		"User-Agent":   "Benchmark",
	}
	testBody := map[string]interface{}{
		"key": "value",
	}

	// 基准测试：Resty客户端
	b.Run("Resty", func(b *testing.B) {
		ctx := context.Background()
		client := GetHTTPClient()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.DoWithTimeout(
				ctx,
				1*time.Second,
				"GET",
				testURL,
				WithHeaders(testHeaders),
				WithJSONBody(testBody),
			)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	// 基准测试：FastHTTP客户端
	b.Run("FastHTTP", func(b *testing.B) {
		client := NewFastClient(
			WithRetries(0),
			WithDebugLog(false),
		)
		defer client.Close()

		ctx := context.Background()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, err := client.DoWithTimeout(
				ctx,
				1*time.Second,
				"GET",
				testURL,
				WithHeaders(testHeaders),
				WithJSONBody(testBody),
			)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

func BenchmarkHttpClientParallel(b *testing.B) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 模拟服务器处理延迟
		// time.Sleep(10 * time.Millisecond)

		// 读取请求体
		body, _ := io.ReadAll(r.Body)

		// 返回请求体作为响应
		w.WriteHeader(http.StatusOK)
		w.Write(body)
	}))
	defer server.Close()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 测试数据
	testURL := server.URL
	testHeaders := map[string]string{
		"Content-Type": "application/json",
		"User-Agent":   "Benchmark",
	}
	testBody := map[string]interface{}{
		"key": "value",
	}

	// 基准测试：Resty客户端（并行）
	b.Run("RestyParallel", func(b *testing.B) {
		client := NewClient(
			WithRetries(0),
			WithDebugLog(false),
		)
		defer client.Close()

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, _, err := client.DoWithTimeout(
					context.Background(),
					1*time.Second,
					"GET",
					testURL,
					WithHeaders(testHeaders),
					WithJSONBody(testBody),
				)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	})

	// 基准测试：FastHTTP客户端（并行）
	b.Run("FastHTTPParallel", func(b *testing.B) {
		client := NewFastClient(
			WithRetries(0),
			WithDebugLog(false),
		)
		defer client.Close()

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, _, err := client.DoWithTimeout(
					context.Background(),
					1*time.Second,
					"GET",
					testURL,
					WithHeaders(testHeaders),
					WithJSONBody(testBody),
				)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	})
}
