package utilities

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/valyala/fasthttp"
)

// TestDoWithTimeoutAndContextTimeout 测试外层context超时和DoWithTimeout超时的交互
func TestDoWithTimeoutAndContextTimeout(t *testing.T) {
	// 创建一个延迟响应的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 获取延迟参数，默认延迟300ms（比两个超时都长）
		delay := 300 * time.Millisecond
		time.Sleep(delay)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok"}`))
	}))
	defer server.Close()

	// 测试用例
	testCases := []struct {
		name           string
		contextTimeout time.Duration
		methodTimeout  time.Duration
		expectTimeout  bool
		expectedError  string
	}{
		{
			name:           "外层超时先生效",
			contextTimeout: 100 * time.Millisecond,
			methodTimeout:  200 * time.Millisecond,
			expectTimeout:  true,
			expectedError:  "request timeout",
		},
		{
			name:           "方法超时先生效",
			contextTimeout: 200 * time.Millisecond,
			methodTimeout:  100 * time.Millisecond,
			expectTimeout:  true,
			expectedError:  "request timeout",
		},
		{
			name:           "无超时情况",
			contextTimeout: 500 * time.Millisecond,
			methodTimeout:  500 * time.Millisecond,
			expectTimeout:  false,
			expectedError:  "",
		},
	}

	// 测试 FastHTTP 客户端
	t.Run("FastHTTP", func(t *testing.T) {
		client := NewFastClient(
			WithRetries(0), // 禁用重试以简化测试
		)
		defer client.Close()

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				runTimeoutTest(t, client, server.URL, tc)
			})
		}
	})
}

// runTimeoutTest 运行超时测试
func runTimeoutTest(t *testing.T, client interface{}, serverURL string, tc struct {
	name           string
	contextTimeout time.Duration
	methodTimeout  time.Duration
	expectTimeout  bool
	expectedError  string
}) {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), tc.contextTimeout)
	defer cancel()

	// 记录开始时间
	startTime := time.Now()

	var statusCode int
	var err error

	// 根据客户端类型执行请求
	switch c := client.(type) {
	case *HttpClient:
		_, statusCode, err = c.DoWithTimeout(ctx, tc.methodTimeout, http.MethodGet, serverURL)
	case *FastHttpClient:
		_, statusCode, err = c.DoWithTimeout(ctx, tc.methodTimeout, http.MethodGet, serverURL)
	default:
		t.Fatalf("未知的客户端类型")
	}

	// 计算实际耗时
	duration := time.Since(startTime)

	// 输出测试结果
	fmt.Printf("测试用例[%s]: 外层超时=%s, 方法超时=%s, 实际耗时=%s, 状态码=%d, 错误=%v\n",
		tc.name, tc.contextTimeout, tc.methodTimeout, duration, statusCode, err)

	// 验证结果
	if tc.expectTimeout {
		if err == nil {
			t.Errorf("期望超时错误，但请求成功完成")
		} else if !strings.Contains(err.Error(), tc.expectedError) {
			t.Errorf("期望错误信息包含 %q，实际得到 %q", tc.expectedError, err.Error())
		}

		// 验证哪个超时先生效
		if tc.contextTimeout < tc.methodTimeout {
			// 外层超时应该先生效，耗时应该接近外层超时时间
			if duration > tc.contextTimeout*time.Duration(15) {
				t.Errorf("外层超时应该先生效，期望耗时接近 %s，实际耗时 %s", tc.contextTimeout, duration)
			}
		} else {
			// 方法超时应该先生效，耗时应该接近方法超时时间
			if duration > tc.methodTimeout*time.Duration(15) {
				t.Errorf("方法超时应该先生效，期望耗时接近 %s，实际耗时 %s", tc.methodTimeout, duration)
			}
		}
	} else {
		if err != nil {
			t.Errorf("期望请求成功，但得到错误: %v", err)
		}
	}
}

// TestRequestOptions 测试请求选项功能
func TestRequestOptions(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望Content-Type为application/json，实际为%s", r.Header.Get("Content-Type"))
		}

		// 验证查询参数
		if r.URL.Query().Get("key") != "value" {
			t.Errorf("期望查询参数key=value，实际为%s", r.URL.Query().Get("key"))
		}

		// 验证请求体
		body := make([]byte, r.ContentLength)
		r.Body.Read(body)
		if string(body) != `{"test":"data"}` {
			t.Errorf("期望请求体为{\"test\":\"data\"}，实际为%s", string(body))
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok"}`))
	}))
	defer server.Close()

	// 测试数据
	testURL := server.URL
	testHeaders := map[string]string{
		"Content-Type": "application/json",
	}
	testQuery := map[string]string{
		"key": "value",
	}
	testBody := map[string]interface{}{
		"test": "data",
	}

	// 测试 FastHTTP 客户端
	t.Run("FastHTTP", func(t *testing.T) {
		client := NewFastClient()
		defer client.Close()

		_, statusCode, err := client.Do(
			context.Background(),
			http.MethodPost,
			testURL,
			WithHeaders(testHeaders),
			WithQuery(testQuery),
			WithJSONBody(testBody),
		)

		if err != nil {
			t.Errorf("FastHTTP请求失败: %v", err)
		}
		if statusCode != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", statusCode)
		}
	})
}

// TestRetry 测试重试功能
func TestRetry(t *testing.T) {
	// 创建测试服务器，前两次请求返回500错误，第三次返回200
	attempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		attempts++
		if attempts <= 2 {
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok"}`))
	}))
	defer server.Close()

	// 测试 FastHTTP 客户端
	t.Run("FastHTTP", func(t *testing.T) {
		client := NewFastClient(
			WithRetries(2), // 设置重试次数为2
		)
		defer client.Close()

		attempts = 0
		_, statusCode, err := client.Do(
			context.Background(),
			http.MethodGet,
			server.URL,
		)

		if err != nil {
			t.Errorf("FastHTTP请求失败: %v", err)
		}
		if statusCode != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", statusCode)
		}
		if attempts != 3 {
			t.Errorf("期望尝试3次，实际为%d", attempts)
		}
	})
}

// TestCompressionHandling 测试客户端对压缩响应的处理能力
func TestCompressionHandling(t *testing.T) {
	// 创建测试服务器，返回压缩的响应
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 检查客户端是否支持压缩
		acceptEncoding := r.Header.Get("Accept-Encoding")
		t.Logf("客户端Accept-Encoding: %s", acceptEncoding)

		// 设置响应头
		w.Header().Set("Content-Encoding", "gzip")
		w.Header().Set("Content-Type", "application/json")

		// 创建gzip写入器
		gzipWriter := gzip.NewWriter(w)
		defer gzipWriter.Close()

		// 写入压缩数据
		jsonData := `{"message":"This is a compressed response","data":{"key":"value"}}`
		if _, err := gzipWriter.Write([]byte(jsonData)); err != nil {
			t.Errorf("写入压缩数据失败: %v", err)
			return
		}
	}))
	defer server.Close()

	// 测试 Resty 客户端
	t.Run("Resty", func(t *testing.T) {
		client := GetHTTPClient()

		response, statusCode, err := client.Do(
			context.Background(),
			http.MethodGet,
			server.URL,
		)

		if err != nil {
			t.Errorf("Resty请求失败: %v", err)
			return
		}
		if statusCode != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", statusCode)
			return
		}

		// 验证响应内容
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err != nil {
			t.Errorf("解析JSON响应失败: %v", err)
			return
		}

		// 验证响应内容
		if message, ok := result["message"].(string); !ok || message != "This is a compressed response" {
			t.Errorf("响应内容不匹配，期望message='This is a compressed response'，实际=%v", result["message"])
		}

		t.Logf("Resty客户端成功处理压缩响应")
	})

	// 测试 FastHTTP 客户端
	t.Run("FastHTTP 原始数据", func(t *testing.T) {
		client := NewFastClient()
		defer client.Close()

		response, statusCode, err := client.Do(
			context.Background(),
			http.MethodGet,
			server.URL,
		)

		if err != nil {
			t.Errorf("FastHTTP请求失败: %v", err)
			return
		}
		if statusCode != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", statusCode)
			return
		}

		// 尝试解析响应内容
		var result map[string]interface{}
		if err := json.Unmarshal(response, &result); err != nil {
			t.Logf("FastHTTP响应解析失败，可能是压缩数据未自动解压: %v", err)
			t.Logf("原始响应内容: %s", string(response))
			return
		}

		// 验证响应内容
		if message, ok := result["message"].(string); !ok || message != "This is a compressed response" {
			t.Logf("FastHTTP响应内容不匹配，可能是压缩数据未自动解压")
			t.Logf("期望message='This is a compressed response'，实际=%v", result["message"])
			return
		}

		t.Logf("FastHTTP客户端成功处理压缩响应")
	})
	t.Run("FastHTTP 手动解压zip", func(t *testing.T) {
		client := NewFastClient()
		defer client.Close()

		response, statusCode, err := client.Do(
			context.Background(),
			http.MethodGet,
			server.URL,
		)

		if err != nil {
			t.Errorf("FastHTTP请求失败: %v", err)
			return
		}
		if statusCode != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", statusCode)
			return
		}

		buf := bytes.NewBuffer(response)
		reader, _ := gzip.NewReader(buf)
		defer reader.Close()
		bodyContent, _ := io.ReadAll(reader)

		// 尝试解析响应内容
		var result map[string]interface{}
		if err := json.Unmarshal(bodyContent, &result); err != nil {
			t.Logf("FastHTTP响应解析失败，可能是压缩数据未自动解压: %v", err)
			t.Logf("原始响应内容: %s", string(response))
			return
		}

		// 验证响应内容
		if message, ok := result["message"].(string); !ok || message != "This is a compressed response" {
			t.Logf("FastHTTP响应内容不匹配，可能是压缩数据未自动解压")
			t.Logf("期望message='This is a compressed response'，实际=%v", result["message"])
			return
		}

		t.Logf("FastHTTP客户端成功处理压缩响应")
	})

}

// TestHTTP2Support 测试HTTP/2支持情况
func TestHTTP2Support(t *testing.T) {
	// 生成测试证书
	cert, err := generateTestCert()
	if err != nil {
		t.Fatalf("生成测试证书失败: %v", err)
	}

	// 创建TLS配置
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		NextProtos:   []string{"h2", "http/1.1"}, // 支持HTTP/2
	}

	// 创建一个支持HTTP/2的测试服务器
	server := httptest.NewUnstartedServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 记录请求使用的协议版本
		proto := r.Proto
		fmt.Printf("请求使用的协议版本: %s\n", proto)

		// 返回协议版本信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{
			"protocol": proto,
		})
	}))

	// 配置服务器TLS
	server.TLS = tlsConfig
	server.StartTLS()
	defer server.Close()

	// 获取服务器证书以供客户端使用
	t.Logf("服务器URL: %s", server.URL)

	// 更简单的方法：直接使用测试服务器的HTTP客户端
	// 测试 Resty 客户端 (使用测试服务器的客户端，已配置好信任自签名证书)
	t.Run("Resty HTTP/2", func(t *testing.T) {
		// 使用服务器的HTTP客户端配置创建resty客户端
		// 这样客户端会自动接受服务器的自签名证书
		httpClient := server.Client()
		httpClient.Transport.(*http.Transport).ForceAttemptHTTP2 = true

		// 创建临时resty客户端
		restyClient := resty.NewWithClient(httpClient)

		// 发起请求
		resp, err := restyClient.R().Get(server.URL)
		if err != nil {
			t.Errorf("Resty请求失败: %v", err)
			return
		}

		if resp.StatusCode() != http.StatusOK {
			t.Errorf("期望状态码200，实际为%d", resp.StatusCode())
			return
		}

		// 解析响应
		var result map[string]string
		if err := json.Unmarshal(resp.Body(), &result); err != nil {
			t.Errorf("解析JSON响应失败: %v", err)
			return
		}

		// 验证是否使用HTTP/2
		if protocol := result["protocol"]; !strings.Contains(protocol, "HTTP/2") {
			fmt.Printf("Resty应该使用HTTP/2，实际使用: %s\n", protocol)
		} else {
			fmt.Println("Resty成功使用HTTP/2协议")
		}
	})

	// 测试 FastHTTP 客户端
	t.Run("FastHTTP HTTP/1.1", func(t *testing.T) {
		// FastHTTP需要一个新的客户端配置，跳过TLS验证
		fasthttpClient := &fasthttp.Client{
			TLSConfig: &tls.Config{
				InsecureSkipVerify: true, // 跳过证书验证
			},
		}

		// 创建请求
		req := fasthttp.AcquireRequest()
		resp := fasthttp.AcquireResponse()
		defer fasthttp.ReleaseRequest(req)
		defer fasthttp.ReleaseResponse(resp)

		req.SetRequestURI(server.URL)

		// 发起请求
		err := fasthttpClient.Do(req, resp)
		if err != nil {
			fmt.Printf("FastHTTP请求失败: %v\n", err)
			return
		}

		if resp.StatusCode() != http.StatusOK {
			fmt.Printf("期望状态码200，实际为%d\n", resp.StatusCode())
			return
		}

		// 解析响应
		var result map[string]string
		if err := json.Unmarshal(resp.Body(), &result); err != nil {
			fmt.Printf("解析JSON响应失败: %v\n", err)
			return
		}

		// FastHTTP只支持HTTP/1.1
		if protocol := result["protocol"]; !strings.Contains(protocol, "HTTP/1.1") {
			fmt.Printf("FastHTTP应该使用HTTP/1.1，实际使用: %s\n", protocol)
		} else {
			fmt.Println("FastHTTP使用HTTP/1.1协议（不支持HTTP/2）")
		}
	})
}

// generateTestCert 生成测试用的自签名证书
func generateTestCert() (tls.Certificate, error) {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("生成私钥失败: %v", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"Test Organization"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(24 * time.Hour),
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		IPAddresses:           []net.IP{net.ParseIP("127.0.0.1")},
		DNSNames:              []string{"localhost"},
	}

	// 生成证书
	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("生成证书失败: %v", err)
	}

	// 创建证书对象
	cert := tls.Certificate{
		Certificate: [][]byte{derBytes},
		PrivateKey:  privateKey,
		Leaf:        &template,
	}

	return cert, nil
}
