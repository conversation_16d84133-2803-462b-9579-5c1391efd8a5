package utilities

import (
	"log"
	"math/rand"
	"testing"
	"time"
)

func TestRandomTTL(t *testing.T) {
	ttl, err := RandomTTL(2, 5)

	log.Println(ttl, err, ttl.Seconds())
}

func TestRandomMinuteTTL(t *testing.T) {
	// 次日00:30 到 01:00 的随机时间
	ttl, err := RandomMinuteTTL(30, 60)
	log.Println(ttl, err, ttl.Seconds())
}

func TestCRandomTTL(t *testing.T) {
	ttl, err := CRandomTTL(2, 5)

	log.Println(ttl, err, ttl.Seconds())
}

func TestQuickRandomTTL(t *testing.T) {
	ttl, err := QuickRandomTTL(2, 5)

	log.Println(ttl, err, ttl.Seconds())
}

func TestRand(t *testing.T) {
	s := rand.Int63n(100000)
	log.Println(s)
}

func TestDaysAgo(t *testing.T) {
	nowDD, _, _, _ := GetNowDDHHMMSS()
	days := 22

	dd, _, _, _ := GetDaysAgoDDHHMMSS(days)
	log.Println(days, "days ago from", nowDD, "is", dd)

	days = 30
	dd, _, _, _ = GetDaysAgoDDHHMMSS(days)
	log.Println(days, "days ago from", nowDD, "is", dd)

	days = 7
	dd, _, _, _ = GetDaysAgoDDHHMMSS(days)
	log.Println(days, "days ago from", nowDD, "is", dd)
}

func TestGetTimeFromString(t *testing.T) {
	timeString := GetNowString()
	timeValue, err := GetTimeFromString(timeString)
	newTime := time.Unix(timeValue.Unix(), 0)
	log.Println(timeString, timeValue, newTime, err)
}

func TestRandTTLWithAmount(t *testing.T) {
	log.Println(RandTTLWithAmount(50000000, 3600*time.Second))
}

func TestQuickRandTTLWithAmount(t *testing.T) {
	log.Println(QuickRandTTLWithAmount(50000000, 3600*time.Second))
}

func TestQuickRandTTLWithAmountFromTomorrowMidnightToDaysLater(t *testing.T) {
	log.Println(QuickRandTTLWithAmountFromTomorrowMidnightToDaysLater(5000000, 1))
}
