package utilities

import (
	"crypto/md5"
	"encoding/hex"
	"log"
	"strconv"
)

func EscapeSQLString(sqlString string) string {
	dest := make([]byte, 0, 2*len(sqlString))
	var escape byte
	for i := 0; i < len(sqlString); i++ {
		c := sqlString[i]

		escape = 0

		switch c {
		case 0: /* Must be escaped for 'mysql' */
			escape = '0'
		case '\n': /* Must be escaped for logs */
			escape = 'n'
		case '\r':
			escape = 'r'

		case '\\':
			escape = '\\'

		case '\'':
			escape = '\''

		case '"': /* Better safe than sorry */
			escape = '"'

		case '\032': //十进制26,八进制32,十六进制1a, /* This gives problems on Win32 */
			escape = 'Z'
		}

		if escape != 0 {
			dest = append(dest, '\\', escape)
		} else {
			dest = append(dest, c)
		}
	}

	return string(dest)
}

func Md5String(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func Md5HalfString(str string) string {
	return Md5String(str)[8:24]
}

func Md5HalfStringToUInt64(str string) (value uint64) {
	v, err := strconv.ParseUint(str, 16, 64)
	if err != nil {
		log.Println("Md5HalfStringToUInt64 error:", err)
		return
	}

	return uint64(v)
}

func UInt64SplitToInt32(val uint64) (int32, int32) {
	// 将int64的高32位和低32位分别映射到两个int32上
	high := int32(val >> 32)
	low := int32(val & 0xFFFFFFFF)

	return high, low
}

func Md5HalfStringToInt32(str string) (int32, int32) {
	return UInt64SplitToInt32(Md5HalfStringToUInt64(str))
}
