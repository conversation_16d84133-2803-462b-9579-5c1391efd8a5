package utilities

import (
	"log"
	"runtime"

	"github.com/pbnjay/memory"
)

/*

`runtime.MemStats{}`
- `Alloc`: 表示已分配的内存字节数，即当前程序使用的内存量。
- `TotalAlloc`: 表示从程序开始运行到现在已分配的内存总量，包括已释放的内存。
- `Sys`: 表示程序向操作系统申请的内存总量，包括未分配的内存。
- `NumGC`: 表示程序运行过程中已执行的垃圾回收次数。
- `TotalMemory`: 表示程序当前总共占用的内存量，包括已分配和未分配的内存。
- `FreeMemory`: 表示程序当前空闲的内存量，即未被程序使用的内存量。
- `NumCPU`: 表示当前系统的 CPU 核心数量。
- `NumCgoCall`: 表示程序运行过程中执行的 Cgo 调用次数。
- `NumGoroutine`: 表示当前程序中活跃的 Goroutine 数量。
*/

func PrintRuntimeInfo() {
	memStats := &runtime.MemStats{}
	runtime.ReadMemStats(memStats)
	log.Println("================================== RUNTIME ==================================")
	log.Println("GOMAXPROCS(0): ", runtime.GOMAXPROCS(0))
	log.Println("NumCPU: ", runtime.NumCPU())
	log.Println("NumCgoCall: ", runtime.NumCgoCall())
	log.Println("NumGoroutine: ", runtime.NumGoroutine())
	log.Printf(
		"Alloc: %v KB, TotalAlloc: %v KB, Sys: %v KB, NumGC %v\n",
		bToKb(memStats.Alloc),
		bToKb(memStats.TotalAlloc),
		bToKb(memStats.Sys),
		uint64(memStats.NumGC),
	)
	log.Printf("Total system memory: %d MB\n", bToMb(memory.TotalMemory()))
	log.Printf("Free memory: %d MB\n", bToMb(memory.FreeMemory()))
	log.Println("=============================================================================")
}

func PrintRuntimeInfoWithTag(tag string) {
	memStats := &runtime.MemStats{}
	runtime.ReadMemStats(memStats)
	log.Println("================================== RUNTIME ==================================")
	log.Printf("[%s]", tag)
	log.Println("GOMAXPROCS(0): ", runtime.GOMAXPROCS(0))
	log.Println("NumCPU: ", runtime.NumCPU())
	log.Println("NumCgoCall: ", runtime.NumCgoCall())
	log.Println("NumGoroutine: ", runtime.NumGoroutine())
	log.Printf(
		"Alloc: %v KB, TotalAlloc: %v KB, Sys: %v KB, NumGC %v\n",
		bToKb(memStats.Alloc),
		bToKb(memStats.TotalAlloc),
		bToKb(memStats.Sys),
		uint64(memStats.NumGC),
	)
	log.Printf("Total system memory: %d MB\n", bToMb(memory.TotalMemory()))
	log.Printf("Free memory: %d MB\n", bToMb(memory.FreeMemory()))
	log.Println("=============================================================================")
}

func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

func bToKb(b uint64) uint64 {
	return b / 1024
}
