package utilities

import (
	"log"
	"testing"
)

func TestTheTestGetSplitKeyIndex(t *testing.T) {
	log.Println(GetSplitKeyIndex("11218cd98f00b204e9800998ecf8427e", 3, 4))
}
func TestGetSplitKeyIndex(t *testing.T) {
	tests := []struct {
		name     string
		md5      string
		bit      int
		div      int64
		expected int64
	}{
		{
			name:     "正常情况-16进制数除以分片因子",
			md5:      "a1b2c3d4e5f6",
			bit:      4,
			div:      100,
			expected: 0xa1b2 / 100,
		},
		{
			name:     "边界情况-使用1位MD5",
			md5:      "f5678901234",
			bit:      1,
			div:      10,
			expected: 0xf / 10,
		},
		{
			name:     "边界情况-分片因子为1",
			md5:      "abcdef123456",
			bit:      3,
			div:      1,
			expected: 0xabc,
		},
		{
			name:     "错误情况-非法MD5字符串",
			md5:      "xyz",
			bit:      2,
			div:      10,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetSplitKeyIndex(tt.md5, tt.bit, tt.div)
			if result != tt.expected {
				t.Errorf("GetSplitKeyIndex() = %v, want %v", result, tt.expected)
			}
		})
	}
}
