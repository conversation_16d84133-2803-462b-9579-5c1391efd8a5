// http_client 属性是可以修改的
package utilities

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"net/url"
	"runtime"
	"sort"
	"strings"
	"time"

	"github.com/allegro/bigcache/v3"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	// "resty.dev/v3"
)

// 默认配置常量
const (
	DefaultTimeout          = 600 * time.Millisecond
	DefaultRetries          = 0                // 默认不重试
	DefaultBreakerInterval  = 10 * time.Second // 熔断器统计周期
	DefaultBreakerTimeout   = 30 * time.Second // 熔断后恢复时间
	DefaultFailureThreshold = 5                // 连续失败阈值
	DefaultMaxRequests      = 5                // 半开状态最大请求数
)

// HttpClient 扩展HTTP客户端
type HttpClient struct {
	*resty.Client

	Config   *ClientConfig
	resolver *Resolver // DNS解析器
}

// ClientConfig 客户端配置
type ClientConfig struct {
	Retries    int
	IsDebugLog bool
	TLSConfig  *tls.Config
}

// ClientOption 客户端配置选项类型
type ClientOption func(*ClientConfig)

// RequestOption 请求配置选项类型
type RequestOption func(*requestConfig)

// requestConfig 请求配置
type requestConfig struct {
	headers map[string]string
	query   map[string]string // query参数和queryEx参数二选一，如果都设置，则使用queryEx
	queryEx url.Values

	body      interface{}
	bodyBytes []byte
	formData  map[string]string
}

// NewClient 创建新的局部HTTP客户端
func NewClient(opts ...ClientOption) *HttpClient {
	// 使用默认配置创建ClientConfig
	config := &ClientConfig{
		Retries:    DefaultRetries,
		IsDebugLog: false,
	}

	// 应用选项
	for _, opt := range opts {
		opt(config)
	}

	dnsResolver := NewCache(context.Background())
	// 先创建标准http.Client
	httpClient := &http.Client{
		Transport: &http.Transport{
			// DisableCompression: true,

			// 最大空闲连接数（所有host的总和）
			MaxIdleConns: 2000, // 减少空闲连接数

			// 每个host的最大空闲连接数
			MaxIdleConnsPerHost: 500, // 减少每个host的空闲连接

			// 每个host的最大连接数（包含活跃+空闲）
			MaxConnsPerHost: 1000, // 减少最大连接数

			// 空闲连接的超时时间 - 减少以避免长时间保持连接
			IdleConnTimeout: 120 * time.Second, // 减少空闲超时

			// 添加响应头超时
			ResponseHeaderTimeout: 10 * time.Second,

			// 添加TLS握手超时
			TLSHandshakeTimeout: 5 * time.Second,

			// 添加期望继续超时
			ExpectContinueTimeout: 3 * time.Second,

			// 强制尝试HTTP/2（即使服务端没有明确支持）
			ForceAttemptHTTP2: true,

			// 对于测试场景，可以考虑禁用连接复用
			DisableKeepAlives: false,

			// 自定义DNS解析和连接建立逻辑
			DialContext: func(ctx context.Context, network string, address string) (net.Conn, error) {
				// 创建带超时的拨号器
				dialer := &net.Dialer{
					Timeout:   5 * time.Second,
					KeepAlive: 10 * time.Second, // 减少keep-alive时间
					DualStack: true,
				}

				separator := strings.LastIndex(address, ":")
				if separator < 0 {
					return nil, fmt.Errorf("invalid address format: %s", address)
				}

				host := address[:separator]
				port := address[separator:]

				// 尝试从缓存获取IP
				ip, err := dnsResolver.FetchOneString(host)
				if err == nil {
					// 使用缓存IP建立连接，重要：使用传入的ctx
					return dialer.DialContext(ctx, "tcp", ip+port)
				}

				// 缓存解析失败时，回退到系统默认DNS解析
				return dialer.DialContext(ctx, network, address)
			},
		},
		Timeout: DefaultTimeout,
	}

	// 然后用它创建resty客户端
	client := resty.NewWithClient(httpClient)

	// 禁用非必要功能
	client.SetRetryCount(0)
	client.SetDebug(false)
	client.SetCloseConnection(false)

	return &HttpClient{
		Client:   client,
		Config:   config,
		resolver: dnsResolver, // 创建DNS解析器
	}
}

// DoWithTimeout 使用指定的超时时间执行HTTP请求
func (c *HttpClient) DoWithTimeout(ctx context.Context, timeout time.Duration, method, url string, opts ...RequestOption) (body []byte, statusCode int, err error) {
	// timeout不合法，则相当于调用Do函数
	if timeout <= 0 {
		return c.Do(ctx, method, url, opts...)
	}

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 执行请求并捕获可能的超时错误
	body, statusCode, err = c.Do(timeoutCtx, method, url, opts...)

	// 特殊处理超时错误，使其更明确
	if err != nil && timeoutCtx.Err() == context.DeadlineExceeded {
		return nil, http.StatusGatewayTimeout, fmt.Errorf("request timeout=%v, statusCode=%v, err=%v", timeout, statusCode, err)
	}
	return body, statusCode, err
}

// Do 执行HTTP请求
func (c *HttpClient) Do(ctx context.Context, method, url string, opts ...RequestOption) (body []byte, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
			log.Printf("panic: %v\n, stack:%v\n", r, GetStack())
			statusCode = http.StatusInternalServerError
		}
	}()

	// 准备请求配置
	reqConfig := requestConfig{}
	for _, opt := range opts {
		opt(&reqConfig)
	}

	return c.executeWithRetry(ctx, method, url, &reqConfig)
}

// executeWithRetry 实现带重试的请求执行逻辑
func (c *HttpClient) executeWithRetry(ctx context.Context, method, url string, reqConfig *requestConfig) (result []byte, statusCode int, err error) {
	maxRetryCount := c.Config.Retries
	for i := 0; i <= maxRetryCount; i++ {
		if result, statusCode, err = c.executeRawRequest(ctx, method, url, reqConfig); err != nil {
			continue
		}

		// 成功响应
		if statusCode >= http.StatusOK && statusCode < http.StatusMultipleChoices {
			return result, statusCode, nil
		}

		// 不需要重试
		if !c.shouldRetry(statusCode) {
			return result, statusCode, err
		}
	}

	return result, statusCode, fmt.Errorf("timeout return")
}

// executeRawRequest 执行原始请求并返回响应对象
func (c *HttpClient) executeRawRequest(ctx context.Context, method string, urlStr string, reqConfig *requestConfig) ([]byte, int, error) {
	req := c.Client.R().SetContext(ctx)
	// if c.Config.IsDebugLog {
	// 	req.EnableTrace()
	// }

	// 设置请求参数
	if reqConfig.queryEx != nil {
		req.SetQueryParamsFromValues(reqConfig.queryEx)
	} else if reqConfig.query != nil {
		// 如果 queryEx 为空，则使用 query
		req.SetQueryParams(reqConfig.query)
	}

	if reqConfig.formData != nil {
		req.SetFormData(reqConfig.formData)
	}
	if reqConfig.body != nil {
		req.SetBody(reqConfig.body)
	}
	if reqConfig.headers != nil {
		req.SetHeaders(reqConfig.headers)
	}

	var err error
	var resp *resty.Response
	resp, err = req.Execute(method, urlStr)

	if resp == nil {
		return nil, 0, fmt.Errorf("nil response error")
	}

	if resp.RawResponse != nil && resp.RawResponse.Body != nil {
		defer resp.RawResponse.Body.Close()
	}

	if c.Config.IsDebugLog {
		// 优化处理query和queryEx参数的情况
		var queryParams map[string]string
		if reqConfig.queryEx != nil {
			// 如果queryEx有值，将其转换为map[string]string格式
			queryParams = make(map[string]string)
			for k, values := range reqConfig.queryEx {
				if len(values) > 0 {
					// 如果有多个值，用逗号分隔
					queryParams[k] = strings.Join(values, ",")
				}
			}
		} else {
			// 使用原有的query参数
			queryParams = reqConfig.query
		}

		curl := GetCurlCommand(method, urlStr, reqConfig.headers, reqConfig.formData, reqConfig.body, queryParams)
		result := Dump(resp)
		log.Println(curl)
		log.Println(result)
	}

	return resp.Body(), resp.StatusCode(), err
}

// shouldRetry 判断是否需要重试
func (c *HttpClient) shouldRetry(statusCode int) bool {
	// 未设置重试次数则不用重试
	if c.Config.Retries <= 0 {
		return false
	}

	// 需要重试的情况：
	// 1. 5xx服务器错误
	// 2. 429 Too Many Requests
	// 3. 408 Request Timeout
	return statusCode == http.StatusTooManyRequests ||
		statusCode == http.StatusRequestTimeout ||
		statusCode >= http.StatusInternalServerError
}

func (c *HttpClient) Close() {
	// 关闭DNS解析器
	if c.resolver != nil {
		c.resolver.Close()
		c.resolver = nil
	}

	if c.Client != nil {
		// 关闭所有连接，仅仅是空闲连接
		c.Client.GetClient().CloseIdleConnections()

		// 获取底层Transport并关闭
		if transport, ok := c.Client.GetClient().Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}

		// 清理其他可能的资源
		c.Client = nil
	}
}

// 客户端配置选项

// WithRetries 设置重试次数
func WithRetries(n int) ClientOption {
	if n < 0 {
		n = 0
	}
	return func(c *ClientConfig) {
		c.Retries = n
	}
}

func WithDebugLog(open bool) ClientOption {
	return func(c *ClientConfig) {
		c.IsDebugLog = open
	}
}

// WithTLSConfig 设置TLS配置
func WithTLSConfig(tls *tls.Config) ClientOption {
	return func(c *ClientConfig) {
		c.TLSConfig = tls
	}
}

// 请求配置选项
// WithHeaders 设置请求头
func WithHeaders(headers map[string]string) RequestOption {
	return func(rc *requestConfig) {
		if rc.headers != nil {
			for k, v := range headers {
				rc.headers[k] = v
			}
		} else {
			rc.headers = headers
		}
	}
}

// WithQuery 设置查询参数
func WithQuery(params map[string]string) RequestOption {
	return func(rc *requestConfig) {
		if rc.query != nil {
			for k, v := range params {
				rc.query[k] = v
			}
		} else {
			rc.query = params
		}
	}
}

// WithJSONBody 设置JSON请求体
func WithJSONBody(body interface{}) RequestOption {
	bodyBytes, ok := body.([]byte)
	if !ok {
		bodyStr, ok := body.(string)
		if ok {
			bodyBytes = []byte(bodyStr)
		} else {
			bodyBytes, _ = jsoniter.Marshal(body)
		}
	}

	return func(rc *requestConfig) {
		rc.body = body
		rc.bodyBytes = bodyBytes
	}
}

// WithFormData 设置表单数据
func WithFormData(form map[string]string) RequestOption {
	return func(rc *requestConfig) {
		if rc.formData != nil {
			for k, v := range form {
				rc.formData[k] = v
			}
		} else {
			rc.formData = form
		}
	}
}

// WithProtobufBody 设置Protobuf请求体
func WithProtobufBody(body []byte) RequestOption {
	return func(rc *requestConfig) {
		rc.body = body
		rc.bodyBytes = body
	}
}

func Dump(resp *resty.Response) string {
	if resp == nil || resp.Request == nil {
		return ""
	}

	req := resp.Request.RawRequest
	if req == nil {
		return ""
	}

	buffer := new(bytes.Buffer)

	buffer.WriteString("\n==============================================================================\n")
	buffer.WriteString("~~~ REQUEST ~~~\n")
	buffer.WriteString(fmt.Sprintf("%s  %s  %s\n", req.Method, req.URL.RequestURI(), req.Proto))
	buffer.WriteString(fmt.Sprintf("HOST   : %s\n", req.URL.Host))
	buffer.WriteString(fmt.Sprintf("HEADERS:\n%s\n", composeHeaders(req, req.Header)))
	buffer.WriteString(fmt.Sprintf("http code: %v\n\nBODY   :\n%v\n", resp.StatusCode(), fmt.Sprintf("%v", formatJson(resp.Body()))))

	buffer.WriteString("------------------------------------------------------------------------------\n")

	return buffer.String()
}

func formatJson(data []byte) string {
	dst := new(bytes.Buffer)
	json.Indent(dst, data, "", "   ")
	return dst.String()
}

func composeHeaders(r *http.Request, hdrs http.Header) string {
	str := make([]string, 0, len(hdrs))
	for _, k := range sortHeaderKeys(hdrs) {
		str = append(str, "\t"+strings.TrimSpace(fmt.Sprintf("%25s: %s", k, strings.Join(hdrs[k], ", "))))
	}
	return strings.Join(str, "\n")
}

func sortHeaderKeys(hdrs http.Header) []string {
	keys := make([]string, 0, len(hdrs))
	for key := range hdrs {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	return keys
}

// WithQueryEx 设置复杂查询参数（支持一个键对应多个值）
func WithQueryEx(params url.Values) RequestOption {
	return func(rc *requestConfig) {
		if rc.queryEx != nil {
			// 合并参数
			for k, values := range params {
				for _, v := range values {
					rc.queryEx.Add(k, v)
				}
			}
		} else {
			rc.queryEx = params
		}
	}
}

func GetCurlCommand(method, urlStr string, headers map[string]string, formData map[string]string, body interface{}, params map[string]string) string {
	curl := fmt.Sprintf("curl -X %s", method)

	// 处理 URL 和查询参数
	if len(params) > 0 {
		queryParams := url.Values{}
		for k, v := range params {
			queryParams.Add(k, v)
		}

		if strings.Contains(urlStr, "?") {
			urlStr += "&" + queryParams.Encode()
		} else {
			urlStr += "?" + queryParams.Encode()
		}
	}
	curl += fmt.Sprintf(" '%s'", urlStr)

	// 添加自定义 headers
	if len(headers) > 0 {
		for k, v := range headers {
			curl += fmt.Sprintf(" -H '%s: %s'", k, v)
		}
	}

	// 处理 formData
	if len(formData) > 0 {
		// 如果有formData，自动添加Content-Type头（如果没有设置的话）
		hasContentType := false
		if len(headers) > 0 {
			for k := range headers {
				if strings.ToLower(k) == "content-type" {
					hasContentType = true
					break
				}
			}
		}
		if !hasContentType {
			curl += " -H 'Content-Type: application/x-www-form-urlencoded'"
		}

		// 构建表单数据
		formValues := url.Values{}
		for k, v := range formData {
			formValues.Add(k, v)
		}
		curl += fmt.Sprintf(" -d '%s'", formValues.Encode())
	} else if body != nil {
		// 只有在没有formData时才处理body
		bodyBytes, err := jsoniter.Marshal(body)
		if err == nil {
			curl += fmt.Sprintf(" -d '%s'", string(bodyBytes))
		}
	}

	return curl
}

func stack() []byte {
	buf := make([]byte, 9128)
	n := runtime.Stack(buf, false)
	return buf[:n]
}

func GetStack() string {
	return fmt.Sprintf("%v", string(stack()))
}

///////////////////////////////// IP cache /////////////////////////////////////////////

// 使用BigCache替代自定义缓存
type Resolver struct {
	cache *bigcache.BigCache
}

// 修改常量定义，移除refreshConcurrency
const (
	defaultEntryTTL = 30 * time.Minute // 默认缓存条目存活时间
)

// NewCache 创建DNS缓存解析器
func NewCache(ctx context.Context) *Resolver {
	// 配置BigCache
	config := bigcache.DefaultConfig(defaultEntryTTL)
	// 为4GB内存的Pod环境优化配置
	config.Shards = 1024               // 增加分片数量以减少锁竞争
	config.HardMaxCacheSize = 128      // 限制最大内存使用为128MB
	config.MaxEntriesInWindow = 100000 // 限制时间窗口内的最大条目数
	config.MaxEntrySize = 512          // 每个条目最大512字节(足够存储多个IP)
	config.Verbose = false             // 生产环境关闭详细日志

	cache, err := bigcache.New(ctx, config)
	if err != nil {
		// 如果BigCache初始化失败，回退到简单的内存缓存
		log.Printf("Failed to initialize BigCache: %v, using fallback cache\n", err)
		return &Resolver{
			cache: nil,
		}
	}

	r := &Resolver{
		cache: cache,
	}

	return r
}

// 简化Close方法，只关闭缓存
func (r *Resolver) Close() {
	if r.cache != nil {
		r.cache.Close()
		r.cache = nil
	}
}

func (r *Resolver) FetchIPs(address string) ([]net.IP, error) {
	if r == nil || r.cache == nil {
		return nil, fmt.Errorf("invalid resolver")
	}

	ips, err := r.Lookup(address)
	if err != nil {
		return nil, err
	}

	result := make([]net.IP, 0, len(ips))
	for _, ipStr := range ips {
		if ip := net.ParseIP(ipStr); ip != nil {
			result = append(result, ip)
		}
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("invalid IP addresses found for %s", address)
	}

	return result, nil
}

// FetchOneString 获取域名对应的第一个IP地址字符串
func (r *Resolver) FetchOneString(address string) (string, error) {
	if r == nil || r.cache == nil {
		return "", fmt.Errorf("invalid resolver")
	}

	ips, err := r.FetchIPs(address)
	if err != nil || len(ips) == 0 {
		return "", err
	}
	return ips[0].String(), nil
}

// Lookup 核心查询方法
func (r *Resolver) Lookup(address string) ([]string, error) {
	if r == nil || r.cache == nil {
		return nil, fmt.Errorf("invalid resolver")
	}

	// 先尝试从缓存读取
	if ips, ok := r.getFromCache(address); ok {
		return ips, nil
	}

	// 缓存未命中则进行DNS查询
	return r.updateCache(address)
}

// getFromCache 从缓存获取数据
func (r *Resolver) getFromCache(address string) ([]string, bool) {
	if r == nil || r.cache == nil {
		return nil, false
	}

	// 从BigCache获取数据
	data, err := r.cache.Get(address)
	if err != nil {
		return nil, false
	}

	// 解析存储的IP列表
	var ips []string
	if err := json.Unmarshal(data, &ips); err != nil {
		return nil, false
	}

	// 每次访问都更新缓存，延长过期时间
	// BigCache会自动处理过期，但我们可以通过重新设置来刷新过期时间
	r.cache.Set(address, data)

	return ips, true
}

// updateCache 更新缓存
func (r *Resolver) updateCache(address string) ([]string, error) {
	if r == nil || r.cache == nil {
		return nil, fmt.Errorf("invalid resolver")
	}

	// 执行DNS查询
	ips, err := net.LookupIP(address)
	if err != nil {
		// 查询失败时检查是否有旧缓存
		if cachedIPs, ok := r.getFromCache(address); ok {
			return cachedIPs, nil
		}
		return nil, err
	}

	// 转换IP为字符串存储
	ipStrs := make([]string, 0, len(ips))
	for _, ip := range ips {
		// 只保留IPv4和IPv6地址
		if ipv4 := ip.To4(); ipv4 != nil {
			ipStrs = append(ipStrs, ipv4.String())
		} else if ip.To16() != nil {
			ipStrs = append(ipStrs, ip.String())
		}
	}

	if len(ipStrs) == 0 {
		return nil, fmt.Errorf("no valid IP addresses found for %s", address)
	}

	// 序列化IP列表
	data, err := json.Marshal(ipStrs)
	if err != nil {
		return nil, err
	}

	// 存储到BigCache
	if r.cache != nil {
		if err := r.cache.Set(address, data); err != nil {
			// 缓存设置失败，但仍然返回查询结果
			log.Printf("Failed to cache DNS result for %s: %v\n", address, err)
		}
	}

	return ipStrs, nil
}

// ValuesToMap 将url.Values转换为map[string]string
func ValuesToMap(values url.Values) map[string]string {
	result := make(map[string]string, len(values))
	for key, vals := range values {
		if len(vals) > 0 {
			result[key] = vals[0] // 只取第一个值
		}
	}
	return result
}

/////////////////////////////////////////////
