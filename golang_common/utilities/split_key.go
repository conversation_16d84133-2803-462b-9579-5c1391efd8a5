package utilities

import "strconv"

// GetSplitKeyIndex 计算分片键索引，用于数据分片路由
//
// 参数:
//   - md5: 输入的MD5字符串，通常是对数据的唯一标识符计算得到的MD5值
//   - bit: 需要使用的MD5字符串的前bit位数（十六进制位数）
//   - div: 分片因子，用于调整最终的分片数量
//
// 返回值:
//   - int64: 计算得到的分片索引。如果输入的MD5字符串无效，则返回0
//
// 算法说明:
//   1. 取MD5字符串的前bit位作为十六进制数
//   2. 将该十六进制数转换为十进制整数
//   3. 将得到的整数除以分片因子div得到最终的分片索引
//
// 示例:
//   - 分1024片: bit=3, div=4  (因为 16^3/4 = 1024)
//   - 分4096片: bit=3, div=1  (因为 16^3/1 = 4096)
//   - 分128片:  bit=2, div=2  (因为 16^2/2 = 128)
//
// 注意: 确保输入的MD5字符串长度不小于bit参数值，否则会返回0
func GetSplitKeyIndex(md5 string, bit int, div int64) int64 {
	value, err := strconv.ParseInt(md5[0:bit], 16, 64)

	if err != nil {
		return 0
	}

	return value / div
}
