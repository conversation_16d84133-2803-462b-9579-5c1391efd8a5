package utilities

import (
	"database/sql/driver"
	"net"
	"time"

	"github.com/lib/pq"
	"golang.org/x/crypto/ssh"
)

// Deprecated: use PostgresViaSSHDialer instead
type PostgressViaSSHDialer struct {
	client *ssh.Client
}

func (d *PostgressViaSSHDialer) Open(s string) (_ driver.Conn, err error) {
	return pq.DialOpen(d, s)
}

func (d *PostgressViaSSHDialer) Dial(network, address string) (net.Conn, error) {
	return d.client.Dial(network, address)
}

func (d *PostgressViaSSHDialer) DialTimeout(network, address string, timeout time.Duration) (net.Conn, error) {
	return d.client.Dial(network, address)
}

// Deprecated: use NewPostgresViaSSHDialer instead
func NewPostgressViaSSHDialer(client *ssh.Client) *PostgressViaSSHDialer {
	return &PostgressViaSSHDialer{client}
}
