package utilities

import (
	crand "crypto/rand"
	"log"
	"math/big"
	"math/rand"
	"time"
)

func GetTomorrowDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(time.Hour * 24)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetNowDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now()
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastMinuteDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Minute)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastTenMinuteDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Minute * 10)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastHourDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetLastTowHourDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour * 2)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetYesterdayDDHHMMSS() (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour * 24)
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetDaysAgoDDHHMMSS(days int) (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour * 24 * time.Duration(days))
	return timestamp.Format("2006-01-02"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetDaysAgoShortDDHHMMSS(days int) (string, string, string, string) {
	timestamp := time.Now().Add(-time.Hour * 24 * time.Duration(days))
	return timestamp.Format("20060102"), timestamp.Format("15"), timestamp.Format("04"), timestamp.Format("05")
}

func GetNowString() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func GetNowShortString() string {
	return time.Now().Format("20060102150405")
}

func GetTimeFromString(timeString string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02 15:04:05", timeString, time.Local)
}

func GetRand() (r *rand.Rand) {
	r = rand.New(rand.NewSource(time.Now().UnixNano()))
	return
}

// RandomTTL 生成一个从当前时间到明天凌晨之间的随机TTL时间
// 参数:
//   - minHour: 明天凌晨最小小时数
//   - maxHour: 明天凌晨最大小时数
//
// 返回:
//   - ttl: 生成的随机时间段
//   - err: 错误信息
//
// 实现原理:
//  1. 获取明天的日期
//  2. 在minHour和maxHour之间生成随机秒数
//  3. 计算从当前时间到随机时间点的时间段
func RandomTTL(minHour int64, maxHour int64) (ttl time.Duration, err error) {
	r := GetRand()

	tdd, _, _, _ := GetTomorrowDDHHMMSS()

	s := r.Int63n((maxHour-minHour)*60*60) + minHour*60*60

	zeroHour, err := time.ParseInLocation("2006-01-02", tdd, time.Local)
	if err != nil {
		return 0, err
	}

	// log.Println("当前时刻", time.Now())
	// log.Println("今晚0点", zeroHour)
	// log.Println("当前时刻距离今晚0点时间", time.Since(zeroHour))
	// log.Println("随机时刻距离今晚0点的秒数", s)
	// log.Println("随机时刻距离今晚0点的时间", time.Duration(s*int64(time.Second)))

	ttl = time.Duration(s*int64(time.Second)) - time.Since(zeroHour)

	// log.Println("当前时刻距离随机时刻的时间", ttl)

	return
}

// RandomMinuteTTL 生成一个从当前时间到明天凌晨之间的随机TTL时间（分钟级别）
// 参数:
//   - minMinute: 明天凌晨最小分钟数
//   - maxMinute: 明天凌晨最大分钟数
//
// 返回:
//   - ttl: 生成的随机时间段
//   - err: 错误信息
//
// 实现原理:
//  1. 获取明天的日期
//  2. 在minMinute和maxMinute之间生成随机秒数
//  3. 计算从当前时间到随机时间点的时间段
func RandomMinuteTTL(minMinute int64, maxMinute int64) (ttl time.Duration, err error) {
	r := GetRand()

	tdd, _, _, _ := GetTomorrowDDHHMMSS()

	s := r.Int63n((maxMinute-minMinute)*60) + minMinute*60

	zeroHour, err := time.ParseInLocation("2006-01-02", tdd, time.Local)
	if err != nil {
		return 0, err
	}

	// log.Println("当前时刻", time.Now())
	// log.Println("今晚0点", zeroHour)
	// log.Println("当前时刻距离今晚0点时间", time.Since(zeroHour))
	// log.Println("随机时刻距离今晚0点的秒数", s)
	// log.Println("随机时刻距离今晚0点的时间", time.Duration(s*int64(time.Second)))

	ttl = time.Duration(s*int64(time.Second)) - time.Since(zeroHour)

	// log.Println("当前时刻距离随机时刻的时间", ttl)

	return
}

func CRandomTTL(minHour int64, maxHour int64) (ttl time.Duration, err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("CRandomTTL error:", err)
		}
	}()

	tdd, _, _, _ := GetTomorrowDDHHMMSS()

	rs, err := crand.Int(crand.Reader, big.NewInt((maxHour-minHour)*60*60))
	if err != nil {
		return 0, err
	}

	s := rs.Int64() + minHour*60*60

	zeroHour, err := time.ParseInLocation("2006-01-02", tdd, time.Local)
	if err != nil {
		return 0, err
	}

	// log.Println("当前时刻", time.Now())
	// log.Println("今晚0点", zeroHour)
	// log.Println("当前时刻距离今晚0点时间", time.Since(zeroHour))
	// log.Println("随机时刻距离今晚0点的秒数", s)
	// log.Println("随机时刻距离今晚0点的时间", time.Duration(s*int64(time.Second)))

	ttl = time.Duration(s*int64(time.Second)) - time.Since(zeroHour)

	// log.Println("当前时刻距离随机时刻的时间", ttl)

	return
}

func QuickRandomTTL(minHour int64, maxHour int64) (ttl time.Duration, err error) {

	tdd, _, _, _ := GetTomorrowDDHHMMSS()

	s := rand.Int63n((maxHour-minHour)*60*60) + minHour*60*60

	zeroHour, err := time.ParseInLocation("2006-01-02", tdd, time.Local)
	if err != nil {
		return 0, err
	}

	// log.Println("当前时刻", time.Now())
	// log.Println("今晚0点", zeroHour)
	// log.Println("当前时刻距离今晚0点时间", time.Since(zeroHour))
	// log.Println("随机时刻距离今晚0点的秒数", s)
	// log.Println("随机时刻距离今晚0点的时间", time.Duration(s*int64(time.Second)))

	ttl = time.Duration(s*int64(time.Second)) - time.Since(zeroHour)

	// log.Println("当前时刻距离随机时刻的时间", ttl)

	return
}

// 随机返回TTL
// amount 数据量估值
// baseTTL TTL起步值
func RandTTLWithAmount(amount int64, baseTTL time.Duration) (ttlSecond time.Duration, err error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("RandTTlWithAmount error:", err)
		}
	}()

	redisBigAmountTTLDivisor := GetEnvInt64("REDIS_BIG_AMOUNT_TTL_DIVISOR", 5000000)

	minute := int64(amount / redisBigAmountTTLDivisor)
	if minute < 1 {
		minute = 1
	}
	section := minute * 60
	rs, err := crand.Int(crand.Reader, big.NewInt(section))
	if err != nil {
		return 0, err
	}
	ttlSecond = time.Duration(rs.Int64()*int64(time.Second)) + baseTTL
	return
}

// 伪随机返回TTL 对CPU消耗较小
// amount 数据量估值
// baseTTL TTL起步值
func QuickRandTTLWithAmount(amount int64, baseTTL time.Duration) (ttlSecond time.Duration, err error) {
	redisBigAmountTTLDivisor := GetEnvInt64("REDIS_BIG_AMOUNT_TTL_DIVISOR", 5000000)

	minute := int64(amount / redisBigAmountTTLDivisor)
	if minute < 1 {
		minute = 1
	}
	section := minute * 60
	ttlSecond = time.Duration(rand.Int63n(section)*int64(time.Second)) + baseTTL
	return
}

// 根据数量级获得Tn天后的随机TTL
func QuickRandTTLWithAmountFromTomorrowMidnightToDaysLater(
	amount int64,
	daysLater int,
) (
	ttl time.Duration,
	err error,
) {
	tdd, _, _, _ := GetTomorrowDDHHMMSS()
	zeroHour, err := time.ParseInLocation("2006-01-02", tdd, time.Local)
	if err != nil {
		return 0, err
	}

	baseTTL := -time.Since(zeroHour) + time.Duration(daysLater*int(time.Hour)*24)

	return QuickRandTTLWithAmount(amount, baseTTL)
}

func OneHundredYearsDuration() time.Duration {
	return 100 * 365 * 24 * time.Hour
}
