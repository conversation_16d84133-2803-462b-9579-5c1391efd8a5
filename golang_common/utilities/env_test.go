package utilities

import (
	"log"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 获取环境变量值，并设置默认值
func Test_GetEnv(t *testing.T) {
	defaultValue := "this value is empty"

	emptyEnvValue := GetEnv("EMPTY_ENV", defaultValue)
	assert.Equal(t, emptyEnvValue, defaultValue)

	goPath := GetEnv("GOPATH", defaultValue)
	assert.NotEqual(t, goPath, defaultValue)

}

func Test_GetEnvInt(t *testing.T) {
	defaultInt := 123
	envIntValue := GetEnvInt("ENV_INT", defaultInt)
	assert.Equal(t, envIntValue, defaultInt)

	os.Setenv("ENV_INT", "666")
	envIntValue = GetEnvInt("ENV_INT", defaultInt)
	assert.NotEqual(t, envIntValue, defaultInt)
}

func Test_GetEnvInt64(t *testing.T) {
	defaultInt64 := 123999999999
	envInt64Value := GetEnvInt("ENV_INT64", defaultInt64)
	assert.Equal(t, envInt64Value, defaultInt64)

	os.Setenv("ENV_INT64", "666")
	envInt64Value = GetEnvInt("ENV_INT64", defaultInt64)
	assert.NotEqual(t, envInt64Value, defaultInt64)
}

func Test_GetEnvBool(t *testing.T) {
	defaultBool := true
	envBoolValue := GetEnvBool("ENV_BOOL", defaultBool)
	assert.Equal(t, envBoolValue, defaultBool)

	os.Setenv("ENV_BOOL", "false")
	envBoolValue = GetEnvBool("ENV_BOOL", defaultBool)
	assert.NotEqual(t, envBoolValue, defaultBool)
}

func Test_GetEnvStringArray(t *testing.T) {
	defaultArray := []string{
		"a",
		"b",
		"c",
	}
	envArrayValue := GetEnvStringArray("ENV_STRING_ARRAY", defaultArray)

	log.Println(envArrayValue, defaultArray)
	assert.Equal(t, envArrayValue, defaultArray)

	os.Setenv("ENV_STRING_ARRAY", "[\"a\", \"b\", \"d\"]")
	envArrayValue = GetEnvStringArray("ENV_STRING_ARRAY", defaultArray)
	log.Println(envArrayValue, defaultArray)

	assert.NotEqual(t, envArrayValue, defaultArray)

}

func Test_GetEnvIntArray(t *testing.T) {
	defaultArray := []int{
		1,
		2,
		3,
	}
	envArrayValue := GetEnvIntArray("ENV_INT_ARRAY", defaultArray)

	log.Println(envArrayValue, defaultArray)
	assert.Equal(t, envArrayValue, defaultArray)

	os.Setenv("ENV_INT_ARRAY", "[1, 2, 4]")
	envArrayValue = GetEnvIntArray("ENV_INT_ARRAY", defaultArray)
	log.Println(envArrayValue, defaultArray)

	assert.NotEqual(t, envArrayValue, defaultArray)

}

func Test_GetEnvInt64Array(t *testing.T) {
	defaultArray := []int64{
		1,
		2,
		3,
	}
	envArrayValue := GetEnvInt64Array("ENV_INT_ARRAY", defaultArray)

	log.Println(envArrayValue, defaultArray)
	assert.Equal(t, envArrayValue, defaultArray)

	os.Setenv("ENV_INT_ARRAY", "[1, 2, 4]")
	envArrayValue = GetEnvInt64Array("ENV_INT_ARRAY", defaultArray)
	log.Println(envArrayValue, defaultArray)

	assert.NotEqual(t, envArrayValue, defaultArray)

}
