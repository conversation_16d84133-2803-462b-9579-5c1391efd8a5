package databases

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/ssh"
)

const (
	// RedisHost string = "127.0.0.1"
	// RedisPort string = "6379"

	RedisHost string = "r-2zejfdoaq1comnt5pv.redis.rds.aliyuncs.com"
	RedisPort string = "6379"
)

// customDialer 实现自定义的 Redis 连接器
func customDialer(ctx context.Context, network, addr string) (net.Conn, error) {
	sshHost := "ecs.maplehaze.cn"
	sshPort := "22"
	sshUser := "root"
	sshPass := ""
	privateKey := "/Users/<USER>/Documents/tools/maplehaze-ecs.pem"

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         15 * time.Second,
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to establish SSH connection: %v", err)
	}

	// 创建 SSH 连接器并获取 Redis 连接
	return utilities.NewViaSSHDialer(sshcon).DialForRedis(ctx, network, addr)
}

func newRedisClient() (Redis, error) {
	// 创建测试用的 Redis 客户端
	return NewRedisClient(
		WithAddr(RedisHost+":"+RedisPort),
		WithDB(0),
		WithPoolSize(10),
		WithMinIdleConns(5),
		WithMaxRetries(3),
		WithDialTimeout(5*time.Second),
		WithReadTimeout(-2*time.Second),
		WithWriteTimeout(-2*time.Second),
		WithPoolTimeout(4*time.Second),
		WithDialer(customDialer),
	)
}

func TestBitmap(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}

	var offset int64 = 2147483647*2 + 1 // math.MaxInt - 1
	defer redisClient.Close()
	err = redisClient.SetBit(ctx, 5*time.Second, "test_bitmap", offset, 1)
	assert.NoError(t, err)
	err = redisClient.Expire(ctx, 5*time.Second, "test_bitmap", 5*time.Second)
	assert.NoError(t, err)

	// 测试 GetBit
	value, _ := redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", offset)
	fmt.Printf("value: %v\n", value)
}

func TestRedisWithTimeout(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	// 测试基础操作
	t.Run("Basic Operations", func(t *testing.T) {
		// 测试 Set 和 Get
		err := redisClient.Set(ctx, 10*time.Second, "test_key", "test_value", 10*time.Second)
		assert.NoError(t, err)

		val, err := redisClient.Get(ctx, 1*time.Nanosecond, "test_key")
		assert.NoError(t, err)
		assert.Equal(t, "test_value", val)

		// 测试 Exists
		exists, err := redisClient.Exists(ctx, 1*time.Second, "test_key")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), exists)

		// 测试 Del
		err = redisClient.Del(ctx, 1*time.Second, "test_key")
		assert.NoError(t, err)

		// 测试 TTL
		err = redisClient.Set(ctx, 1*time.Second, "ttl_key", "value", 10*time.Second)
		assert.NoError(t, err)
		ttl, err := redisClient.TTL(ctx, 1*time.Second, "ttl_key")
		assert.NoError(t, err)
		assert.True(t, ttl > 0)
	})

	// 测试 String 类型操作
	t.Run("String Operations", func(t *testing.T) {
		// 测试 MSet 和 MGet
		err := redisClient.MSet(ctx, 1*time.Second, "key1", "value1", "key2", "value2")
		assert.NoError(t, err)

		values, err := redisClient.MGet(ctx, 1*time.Second, "key1", "key2")
		assert.NoError(t, err)
		assert.Equal(t, "value1", values[0])
		assert.Equal(t, "value2", values[1])

		// 测试 Incr 和 Decr
		val, err := redisClient.Incr(ctx, 1*time.Second, "counter")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), val)

		val, err = redisClient.Decr(ctx, 1*time.Second, "counter")
		assert.NoError(t, err)
		assert.Equal(t, int64(0), val)

		// 测试 SetNX
		success, err := redisClient.SetNX(ctx, 1*time.Second, "nx_key", "value", 10*time.Second)
		assert.NoError(t, err)
		assert.True(t, success)

		success, err = redisClient.SetNX(ctx, 1*time.Second, "nx_key", "value", 10*time.Second)
		assert.NoError(t, err)
		assert.False(t, success)
	})

	// 测试 Hash 类型操作
	t.Run("Hash Operations", func(t *testing.T) {
		// 测试 HSet 和 HGet
		err := redisClient.HSet(ctx, 1*time.Second, "hash_key", "field1", "value1", "field2", "value2")
		assert.NoError(t, err)

		val, err := redisClient.HGet(ctx, 1*time.Second, "hash_key", "field1")
		assert.NoError(t, err)
		assert.Equal(t, "value1", val)

		// 测试 HGetAll
		all, err := redisClient.HGetAll(ctx, 1*time.Second, "hash_key")
		assert.NoError(t, err)
		assert.Equal(t, "value1", all["field1"])
		assert.Equal(t, "value2", all["field2"])

		// 测试 HDel
		deleted, err := redisClient.HDel(ctx, 1*time.Second, "hash_key", "field1")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), deleted)

		// 测试 HExists
		exists, err := redisClient.HExists(ctx, 1*time.Second, "hash_key", "field2")
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试 HIncrBy
		incrVal, err := redisClient.HIncrBy(ctx, 1*time.Second, "hash_key", "counter", 1)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), incrVal)
	})

	// 测试 List 类型操作
	t.Run("List Operations", func(t *testing.T) {
		// 测试 LPush 和 RPush
		len, err := redisClient.LPush(ctx, 1*time.Second, "list_key", "value1", "value2")
		assert.NoError(t, err)
		assert.Equal(t, int64(2), len)

		len, err = redisClient.RPush(ctx, 1*time.Second, "list_key", "value3")
		assert.NoError(t, err)
		assert.Equal(t, int64(3), len)

		// 测试 LPop 和 RPop
		val, err := redisClient.LPop(ctx, 1*time.Second, "list_key")
		assert.NoError(t, err)
		assert.Equal(t, "value2", val)

		val, err = redisClient.RPop(ctx, 1*time.Second, "list_key")
		assert.NoError(t, err)
		assert.Equal(t, "value3", val)

		// 测试 LLen
		len, err = redisClient.LLen(ctx, 1*time.Second, "list_key")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), len)

		// 测试 LRange
		values, err := redisClient.LRange(ctx, 1*time.Second, "list_key", 0, -1)
		assert.NoError(t, err)
		assert.Equal(t, []string{"value1"}, values)
	})

	// 测试 Set 类型操作
	t.Run("Set Operations", func(t *testing.T) {
		// 测试 SAdd
		added, err := redisClient.SAdd(ctx, 1*time.Second, "set_key", "member1", "member2")
		assert.NoError(t, err)
		assert.Equal(t, int64(2), added)

		// 测试 SIsMember
		isMember, err := redisClient.SIsMember(ctx, 1*time.Second, "set_key", "member1")
		assert.NoError(t, err)
		assert.True(t, isMember)

		// 测试 SMembers
		members, err := redisClient.SMembers(ctx, 1*time.Second, "set_key")
		assert.NoError(t, err)
		assert.Len(t, members, 2)

		// 测试 SCard
		card, err := redisClient.SCard(ctx, 1*time.Second, "set_key")
		assert.NoError(t, err)
		assert.Equal(t, int64(2), card)

		// 测试 SRem
		removed, err := redisClient.SRem(ctx, 1*time.Second, "set_key", "member1")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), removed)
	})

	// 测试 Sorted Set 类型操作
	t.Run("Sorted Set Operations", func(t *testing.T) {
		// 测试 ZAdd
		z1 := &redis.Z{Score: 1, Member: "member1"}
		z2 := &redis.Z{Score: 2, Member: "member2"}
		added, err := redisClient.ZAdd(ctx, 1*time.Second, "zset_key", *z1, *z2)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), added)

		// 测试 ZRange
		members, err := redisClient.ZRange(ctx, 1*time.Second, "zset_key", 0, -1)
		assert.NoError(t, err)
		assert.Equal(t, []string{"member1", "member2"}, members)

		// 测试 ZRangeWithScores
		zMembers, err := redisClient.ZRangeWithScores(ctx, 1*time.Second, "zset_key", 0, -1)
		assert.NoError(t, err)
		assert.Len(t, zMembers, 2)

		// 测试 ZCard
		card, err := redisClient.ZCard(ctx, 1*time.Second, "zset_key")
		assert.NoError(t, err)
		assert.Equal(t, int64(2), card)

		// 测试 ZScore
		score, err := redisClient.ZScore(ctx, 1*time.Second, "zset_key", "member1")
		assert.NoError(t, err)
		assert.Equal(t, float64(1), score)

		// 测试 ZRem
		removed, err := redisClient.ZRem(ctx, 1*time.Second, "zset_key", "member1")
		assert.NoError(t, err)
		assert.Equal(t, int64(1), removed)
	})

	// 测试 Pipeline 操作
	t.Run("Pipeline Operations", func(t *testing.T) {
		cmders, err := redisClient.PipelineExec(ctx, 1*time.Second, func(pipe redis.Pipeliner) error {
			pipe.Set(ctx, "pipe_key1", "value1", 10*time.Second)
			pipe.Set(ctx, "pipe_key2", "value2", 10*time.Second)
			return nil
		})
		assert.NoError(t, err)
		assert.Len(t, cmders, 2)
	})

	// 测试 Scan 操作
	t.Run("Scan Operations", func(t *testing.T) {
		// 先设置一些测试数据
		err := redisClient.Set(ctx, 1*time.Second, "scan_key1", "value1", 10*time.Second)
		assert.NoError(t, err)
		err = redisClient.Set(ctx, 1*time.Second, "scan_key2", "value2", 10*time.Second)
		assert.NoError(t, err)

		// 测试 Scan
		keys, cursor, err := redisClient.Scan(ctx, 1*time.Second, 0, "scan_key*", 10)
		assert.NoError(t, err)
		assert.True(t, len(keys) >= 2)
		assert.Equal(t, uint64(0), cursor)
	})

	// 测试 Bitmap 操作
	t.Run("Bitmap Operations", func(t *testing.T) {
		// 测试 SetBit
		err := redisClient.SetBit(ctx, 5*time.Second, "test_bitmap", 100, 1)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "test_bitmap", 5*time.Second)
		assert.NoError(t, err)

		// 测试 GetBit
		value, err := redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", 100)
		assert.NoError(t, err)
		assert.Equal(t, 1, value)

		// 测试 BitCount
		count, err := redisClient.BitCount(ctx, 5*time.Second, "test_bitmap", 0, -1)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count)

		// 测试范围操作
		for i := 0; i < 10; i++ {
			err := redisClient.SetBit(ctx, 5*time.Second, "range_bitmap", int64(i), 1)
			assert.NoError(t, err)
		}
		err = redisClient.Expire(ctx, 5*time.Second, "range_bitmap", 5*time.Second)
		assert.NoError(t, err)

		count, err = redisClient.BitCount(ctx, 5*time.Second, "range_bitmap", 0, 4)
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)

	})

	// 测试 Bloom Filter 操作
	t.Run("Bloom Filter Operations", func(t *testing.T) {
		// 测试添加元素
		err := redisClient.BloomAdd(ctx, 5*time.Second, "test_bloom", "test_value")
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "test_bloom", 5*time.Second)
		assert.NoError(t, err)

		// 测试检查存在的元素
		exists, err := redisClient.BloomExist(ctx, 5*time.Second, "test_bloom", "test_value")
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试检查不存在的元素
		exists, err = redisClient.BloomExist(ctx, 5*time.Second, "test_bloom", "non_existent")
		assert.NoError(t, err)
		assert.False(t, exists)

		// 测试批量操作
		values := []string{"value1", "value2", "value3"}
		err = redisClient.BloomAdds(ctx, 5*time.Second, "batch_bloom", values)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "batch_bloom", 5*time.Second)
		assert.NoError(t, err)

		existsList, err := redisClient.BloomExists(ctx, 5*time.Second, "batch_bloom", values)
		assert.NoError(t, err)
		assert.Len(t, existsList, 3)
		for _, e := range existsList {
			assert.True(t, e)
		}

		// 测试混合检查
		mixedValues := []string{"value1", "non_existent", "value3"}
		existsList, err = redisClient.BloomExists(ctx, 5*time.Second, "batch_bloom", mixedValues)
		assert.NoError(t, err)
		assert.Len(t, existsList, 3)
		assert.True(t, existsList[0])
		assert.False(t, existsList[1])
		assert.True(t, existsList[2])
	})

	// 测试 Bloom Filter 误判率
	t.Run("Bloom Filter False Positive Rate", func(t *testing.T) {
		// 添加100个元素
		for i := 0; i < 100; i++ {
			err := redisClient.BloomAdd(ctx, 5*time.Second, "false_positive_test", fmt.Sprintf("value_%d", i))
			assert.NoError(t, err)
		}
		err = redisClient.Expire(ctx, 5*time.Second, "false_positive_test", 5*time.Second)
		assert.NoError(t, err)

		// 测试1000个不存在的元素，统计误判率
		falsePositives := 0
		for i := 100; i < 1100; i++ {
			exists, err := redisClient.BloomExist(ctx, 5*time.Second, "false_positive_test", fmt.Sprintf("value_%d", i))
			assert.NoError(t, err)
			if exists {
				falsePositives++
			}
		}

		// 计算实际误判率
		actualFalsePositiveRate := float64(falsePositives) / 1000.0
		t.Logf("Actual false positive rate: %f", actualFalsePositiveRate)
		assert.Less(t, actualFalsePositiveRate, 0.02) // 允许实际误判率略高于预期
	})

	// 测试阿里云自定义命令
	// t.Run("Aliyun Custom Commands", func(t *testing.T) {
	// 	// 测试 EXHSET
	// 	result, err := redisClient.EXHSET(ctx, 1*time.Second, "exh_key", "field1", "value1")
	// 	assert.NoError(t, err)
	// 	assert.NotNil(t, result)

	// 	// 测试 EXHGET
	// 	val, err := redisClient.EXHGET(ctx, 1*time.Second, "exh_key", "field1")
	// 	assert.NoError(t, err)
	// 	assert.Equal(t, "value1", val)

	// 	// 测试 EXHINCRBY
	// 	val, err = redisClient.EXHINCRBY(ctx, 1*time.Second, "exh_key", "counter", 1)
	// 	assert.NoError(t, err)
	// 	assert.Equal(t, int64(1), val)

	// 	// 测试 EXHKEYS
	// 	keys, err := redisClient.EXHKEYS(ctx, 1*time.Second, "exh_key")
	// 	assert.NoError(t, err)
	// 	assert.Contains(t, keys, "field1")
	// 	assert.Contains(t, keys, "field2")

	// 	// 测试 EXHEXPIRE
	// 	result, err = redisClient.EXHEXPIRE(ctx, 1*time.Second, "exh_key", "field1", 10)
	// 	assert.NoError(t, err)
	// 	assert.NotNil(t, result)

	// 	// 测试 EXHTTL
	// 	ttl, err := redisClient.EXHTTL(ctx, 1*time.Second, "exh_key", "field1")
	// 	assert.NoError(t, err)
	// 	assert.True(t, ttl.(int64) > 0)

	// 	// 测试 EXHSCAN
	// 	scanResult, err := redisClient.EXHSCAN(ctx, 1*time.Second, "exh_key", 0)
	// 	assert.NoError(t, err)
	// 	assert.NotNil(t, scanResult)

	// 	// 测试 EXHDEL
	// 	result, err = redisClient.EXHDEL(ctx, 1*time.Second, "exh_key", "field1")
	// 	assert.NoError(t, err)
	// 	assert.NotNil(t, result)

	// 	// 验证删除后的结果
	// 	val, err = redisClient.EXHGET(ctx, 1*time.Second, "exh_key", "field1")
	// 	assert.Error(t, err)
	// 	assert.Nil(t, val)
	// })
}

func TestRedisBitmap(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	t.Run("Basic Bitmap Operations", func(t *testing.T) {
		// 测试 SetBit
		err := redisClient.SetBit(ctx, 5*time.Second, "test_bitmap", 100, 1)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "test_bitmap", 5*time.Second)
		assert.NoError(t, err)

		// 测试 GetBit
		value, err := redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", 100)
		// time.Sleep(6 * time.Second)
		// value, err = redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", 100)
		assert.NoError(t, err)
		assert.Equal(t, 1, value)

		// 测试 BitCount
		count, err := redisClient.BitCount(ctx, 5*time.Second, "test_bitmap", 0, -1)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count)

	})

	t.Run("Bitmap Range Operations", func(t *testing.T) {
		// 设置多个位
		for i := 0; i < 10; i++ {
			err := redisClient.SetBit(ctx, 5*time.Second, "range_bitmap", int64(i), 1)
			assert.NoError(t, err)
		}
		err = redisClient.Expire(ctx, 5*time.Second, "range_bitmap", 5*time.Second)
		assert.NoError(t, err)

		// 测试范围统计
		count, err := redisClient.BitCount(ctx, 5*time.Second, "range_bitmap", 0, 4)
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count)
	})

	t.Run("Bitmap Edge Cases", func(t *testing.T) {
		// 测试设置0
		err := redisClient.SetBit(ctx, 5*time.Second, "edge_bitmap", 1000, 0)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "edge_bitmap", 5*time.Second)
		assert.NoError(t, err)
		value, err := redisClient.GetBit(ctx, 5*time.Second, "edge_bitmap", 1000)
		assert.NoError(t, err)
		assert.Equal(t, 0, value)

		// 测试不存在的位
		value, err = redisClient.GetBit(ctx, 5*time.Second, "edge_bitmap", 2000)
		assert.NoError(t, err)
		assert.Equal(t, 0, value)
	})
}

func TestRedisBloomFilter(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	t.Run("Basic Bloom Filter Operations", func(t *testing.T) {
		// 测试添加元素
		err := redisClient.BloomAdd(ctx, 5*time.Second, "test_bloom", "test_value")
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "test_bloom", 5*time.Second)
		assert.NoError(t, err)

		// 测试检查存在的元素
		exists, err := redisClient.BloomExist(ctx, 5*time.Second, "test_bloom", "test_value")
		time.Sleep(6 * time.Second)
		exists, err = redisClient.BloomExist(ctx, 5*time.Second, "test_bloom", "test_value")
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试检查不存在的元素
		exists, err = redisClient.BloomExist(ctx, 5*time.Second, "test_bloom", "non_existent")
		assert.NoError(t, err)
		assert.False(t, exists)
	})

	t.Run("Batch Bloom Filter Operations", func(t *testing.T) {
		// 测试批量添加
		values := []string{"value1", "value2", "value3"}
		err := redisClient.BloomAdds(ctx, 5*time.Second, "batch_bloom", values)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "batch_bloom", 5*time.Second)
		assert.NoError(t, err)

		// 测试批量检查
		existsList, err := redisClient.BloomExists(ctx, 5*time.Second, "batch_bloom", values)
		assert.NoError(t, err)
		assert.Len(t, existsList, 3)
		for _, e := range existsList {
			assert.True(t, e)
		}

		// 测试混合检查
		mixedValues := []string{"value1", "non_existent", "value3"}
		existsList, err = redisClient.BloomExists(ctx, 5*time.Second, "batch_bloom", mixedValues)
		assert.NoError(t, err)
		assert.Len(t, existsList, 3)
		assert.True(t, existsList[0])
		assert.False(t, existsList[1])
		assert.True(t, existsList[2])
	})

	t.Run("Bloom Filter Edge Cases", func(t *testing.T) {
		// 测试空值
		err := redisClient.BloomAdd(ctx, 5*time.Second, "edge_bloom", "")
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "edge_bloom", 5*time.Second)
		assert.NoError(t, err)
		exists, err := redisClient.BloomExist(ctx, 5*time.Second, "edge_bloom", "")
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试特殊字符
		specialValue := "!@#$%^&*()_+"
		err = redisClient.BloomAdd(ctx, 5*time.Second, "edge_bloom", specialValue)
		assert.NoError(t, err)
		err = redisClient.Expire(ctx, 5*time.Second, "edge_bloom", 5*time.Second)
		assert.NoError(t, err)
		exists, err = redisClient.BloomExist(ctx, 5*time.Second, "edge_bloom", specialValue)
		assert.NoError(t, err)
		assert.True(t, exists)
	})
}

// Actual false positive rate: 0.004700
// PASS
func TestBloomFilterFalsePositive(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	// 添加100个元素
	for i := 0; i < 100; i++ {
		err := redisClient.BloomAdd(ctx, 5*time.Second, "false_positive_test", fmt.Sprintf("value_%d", i))
		assert.NoError(t, err)
	}
	err = redisClient.Expire(ctx, 5*time.Second, "false_positive_test", 5*time.Second)
	assert.NoError(t, err)

	// 测试100000个不存在的元素，统计误判率
	falsePositives := 0
	for i := 100; i < 100100; i++ {
		exists, err := redisClient.BloomExist(ctx, 5*time.Second, "false_positive_test", fmt.Sprintf("value_%d", i))
		assert.NoError(t, err)
		if exists {
			falsePositives++
		}
	}

	// 计算实际误判率
	actualFalsePositiveRate := float64(falsePositives) / 100000.0
	fmt.Printf("Actual false positive rate: %f\n", actualFalsePositiveRate)
	assert.Less(t, actualFalsePositiveRate, 0.02) // 允许实际误判率略高于预期
}

func TestBloomAddIfNotExists(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	t.Run("Basic BloomAddIfNotExists Operations", func(t *testing.T) {
		// 测试单个元素添加
		values := []string{"test_value"}
		exists1, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "test_bloom", values, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists1, 1)
		assert.False(t, exists1[0]) // 应该不存在，因为之前未添加

		// 再次添加相同的元素
		exists2, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "test_bloom", values, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists2, 1)
		assert.True(t, exists2[0]) // 应该存在，因为之前已添加
	})

	t.Run("Multiple BloomAddIfNotExists Operations", func(t *testing.T) {
		// 测试多个元素添加
		values := []string{"value1", "value2", "value3"}
		exists1, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "multi_bloom", values, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists1, 3)
		for _, e := range exists1 {
			assert.False(t, e) // 所有元素都应该不存在
		}

		// 再次添加相同的元素
		exists2, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "multi_bloom", values, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists2, 3)
		for _, e := range exists2 {
			assert.True(t, e) // 所有元素都应该存在
		}

		// 添加混合元素（部分已存在，部分不存在）
		mixedValues := []string{"value1", "new_value", "value3"}
		exists3, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "multi_bloom", mixedValues, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists3, 3)
		assert.True(t, exists3[0])  // value1 已存在
		assert.False(t, exists3[1]) // new_value 不存在
		assert.True(t, exists3[2])  // value3 已存在
	})

	t.Run("BloomAddIfNotExists Edge Cases", func(t *testing.T) {
		// 测试空数组
		exists1, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "edge_bloom", []string{}, 5*time.Second)
		assert.NoError(t, err)
		assert.Empty(t, exists1)

		// 测试空字符串
		exists2, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "edge_bloom", []string{""}, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists2, 1)
		assert.False(t, exists2[0]) // 空字符串应该不存在

		// 测试特殊字符
		specialValues := []string{"!@#$%^&*()", "测试中文", "space in string"}
		exists3, err := redisClient.BloomAddIfNotExists(ctx, 5*time.Second, "edge_bloom", specialValues, 5*time.Second)
		assert.NoError(t, err)
		assert.Len(t, exists3, 3)
		for _, e := range exists3 {
			assert.False(t, e) // 所有特殊字符都应该不存在
		}
	})
}

func TestSetBitWithExpire(t *testing.T) {
	ctx := context.Background()
	redisClient, err := newRedisClient()
	if err != nil {
		t.Fatalf("Failed to create Redis client: %v", err)
		return
	}
	defer redisClient.Close()

	t.Run("Basic SetBitWithExpire Operations", func(t *testing.T) {
		// 测试设置位为1
		err := redisClient.SetBitWithExpire(ctx, 5*time.Second, "test_bitmap", 100, 1, 5*time.Second)
		assert.NoError(t, err)

		// 验证位值
		value1, err := redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", 100)
		assert.NoError(t, err)
		assert.Equal(t, 1, value1)

		// 验证TTL
		ttl, err := redisClient.TTL(ctx, 5*time.Second, "test_bitmap")
		assert.NoError(t, err)
		assert.True(t, ttl > 0 && ttl <= 5*time.Second)

		// 测试设置位为0
		err = redisClient.SetBitWithExpire(ctx, 5*time.Second, "test_bitmap", 100, 0, 5*time.Second)
		assert.NoError(t, err)

		// 验证位值
		value2, err := redisClient.GetBit(ctx, 5*time.Second, "test_bitmap", 100)
		assert.NoError(t, err)
		assert.Equal(t, 0, value2)
	})

	t.Run("SetBitWithExpire Edge Cases", func(t *testing.T) {
		// 测试无效的值
		err := redisClient.SetBitWithExpire(ctx, 5*time.Second, "edge_bitmap", 100, 2, 5*time.Second)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "value must be 0 or 1")

		// 测试负值
		err = redisClient.SetBitWithExpire(ctx, 5*time.Second, "edge_bitmap", 100, -1, 5*time.Second)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "value must be 0 or 1")

		// 测试零过期时间
		err = redisClient.SetBitWithExpire(ctx, 5*time.Second, "edge_bitmap", 100, 1, 0)
		assert.NoError(t, err)
		ttl, err := redisClient.TTL(ctx, 5*time.Second, "edge_bitmap")
		assert.NoError(t, err)
		assert.Equal(t, time.Duration(-2), ttl) // -2 表示键存在但没有过期时间
	})

	t.Run("SetBitWithExpire Expiration", func(t *testing.T) {
		// 测试过期时间
		err := redisClient.SetBitWithExpire(ctx, 5*time.Second, "expire_bitmap", 100, 1, 2*time.Second)
		assert.NoError(t, err)

		// 立即验证
		value1, err := redisClient.GetBit(ctx, 5*time.Second, "expire_bitmap", 100)
		assert.NoError(t, err)
		assert.Equal(t, 1, value1)

		// 等待过期
		time.Sleep(3 * time.Second)

		// 验证键是否已过期
		exists, err := redisClient.Exists(ctx, 5*time.Second, "expire_bitmap")
		assert.NoError(t, err)
		assert.Equal(t, int64(0), exists)
	})

	t.Run("SetBitWithExpire Multiple Bits", func(t *testing.T) {
		// 测试设置多个位
		for i := 0; i < 10; i++ {
			err := redisClient.SetBitWithExpire(ctx, 5*time.Second, "multi_bitmap", int64(i), i%2, 5*time.Second)
			assert.NoError(t, err)
		}

		// 验证所有位
		for i := 0; i < 10; i++ {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "multi_bitmap", int64(i))
			assert.NoError(t, err)
			assert.Equal(t, i%2, value)
		}

		// 验证位计数
		count, err := redisClient.BitCount(ctx, 5*time.Second, "multi_bitmap", 0, 11000)
		assert.NoError(t, err)
		assert.Equal(t, int64(5), count) // 应该有5个1
	})

	t.Run("TairRoaring Specific Operations", func(t *testing.T) {
		// 测试 TR.SETBITS 命令
		_, err := redisClient.Do(ctx, 5*time.Second, "TR.SETBITS", "tair_bitmap", 1, 2, 3, 4, 5)
		assert.NoError(t, err)

		// 验证位值
		for i := 1; i <= 5; i++ {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "tair_bitmap", int64(i))
			assert.NoError(t, err)
			assert.Equal(t, 1, value)
		}

		// 测试 TR.CLEARBITS 命令
		_, err = redisClient.Do(ctx, 5*time.Second, "TR.CLEARBITS", "tair_bitmap", 2, 4)
		assert.NoError(t, err)

		// 验证清除后的位值
		value2, err := redisClient.GetBit(ctx, 5*time.Second, "tair_bitmap", 2)
		assert.NoError(t, err)
		assert.Equal(t, 0, value2)

		value4, err := redisClient.GetBit(ctx, 5*time.Second, "tair_bitmap", 4)
		assert.NoError(t, err)
		assert.Equal(t, 0, value4)

		// 测试 TR.SETRANGE 命令
		_, err = redisClient.Do(ctx, 5*time.Second, "TR.SETRANGE", "tair_bitmap", 10, 15)
		assert.NoError(t, err)

		// 验证范围设置
		for i := 10; i <= 15; i++ {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "tair_bitmap", int64(i))
			assert.NoError(t, err)
			assert.Equal(t, 1, value)
		}

		// 测试 TR.FLIPRANGE 命令
		_, err = redisClient.Do(ctx, 5*time.Second, "TR.FLIPRANGE", "tair_bitmap", 10, 12)
		assert.NoError(t, err)

		// 验证翻转后的位值
		for i := 10; i <= 12; i++ {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "tair_bitmap", int64(i))
			assert.NoError(t, err)
			assert.Equal(t, 0, value)
		}
	})

	t.Run("SetBitsWithExpire Operations", func(t *testing.T) {
		// 测试批量设置位
		offsets := []int64{1, 2, 3, 4, 5}
		err := redisClient.SetBitsWithExpire(ctx, 5*time.Second, "multi_bits_bitmap", offsets, 5*time.Second)
		assert.NoError(t, err)

		// 验证所有位都被正确设置
		for _, offset := range offsets {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "multi_bits_bitmap", offset)
			assert.NoError(t, err)
			assert.Equal(t, 1, value)
		}

		// 验证TTL
		ttl, err := redisClient.TTL(ctx, 5*time.Second, "multi_bits_bitmap")
		assert.NoError(t, err)
		assert.True(t, ttl > 0 && ttl <= 5*time.Second)

		// 测试空数组
		err = redisClient.SetBitsWithExpire(ctx, 5*time.Second, "empty_bits_bitmap", []int64{}, 5*time.Second)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "offsets array cannot be empty")

		// 测试零过期时间
		err = redisClient.SetBitsWithExpire(ctx, 5*time.Second, "zero_expire_bitmap", offsets, 0)
		assert.NoError(t, err)
		ttl, err = redisClient.TTL(ctx, 5*time.Second, "zero_expire_bitmap")
		assert.NoError(t, err)
		assert.Equal(t, time.Duration(-2), ttl) // -2 表示键存在但没有过期时间

		// 测试过期时间
		err = redisClient.SetBitsWithExpire(ctx, 5*time.Second, "expire_bits_bitmap", offsets, 2*time.Second)
		assert.NoError(t, err)

		// 立即验证
		for _, offset := range offsets {
			value, err := redisClient.GetBit(ctx, 5*time.Second, "expire_bits_bitmap", offset)
			assert.NoError(t, err)
			assert.Equal(t, 1, value)
		}

		// 等待过期
		time.Sleep(3 * time.Second)

		// 验证键是否已过期
		exists, err := redisClient.Exists(ctx, 5*time.Second, "expire_bits_bitmap")
		assert.NoError(t, err)
		assert.Equal(t, int64(0), exists)
	})
}
