package databases

import (
	"context"
	"fmt"
	"log"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"
)

// KafkaProducer Kafka生产者接口
type KafkaProducer interface {
	// Produce 批量发送消息到Kafka
	//
	// 参数:
	//   - ctx: 上下文，用于控制发送超时和取消
	//   - msgs: 要发送的消息列表
	//
	// 消息发送流程:
	//   1. 消息首先进入内部缓冲通道(msgChan)
	//   2. 后台协程从通道中批量读取消息
	//   3. 当达到批次大小或超时时，批量发送到Kafka
	//
	// 背压控制:
	//   1. msgChan 默认缓冲大小为 10000
	//   2. 当 msgChan 已满时，新的消息会被阻塞
	//   3. 如果 ctx 被取消，会立即返回错误
	//
	// 错误处理:
	//   1. 如果 ctx 已取消，返回 ctx.Err()
	//   2. 如果生产者已关闭，返回错误
	//   3. 如果消息通道已满，返回错误
	//
	// 注意事项:
	//   - 当 len(msgs) > msgChan容量时会阻塞
	//   - 如果 ctx 取消时还有未发送的消息，会继续尝试发送 5 秒
	Produce(ctx context.Context, msgs []*Message) error

	// Close 优雅关闭生产者
	//
	// 关闭流程:
	//   1. 标记生产者为已关闭状态
	//   2. 关闭停止信号通道，通知后台协程退出
	//   3. 等待所有已入队消息处理完成
	//   4. 关闭消息通道
	//   5. 关闭底层 Kafka writer
	//
	// 注意事项:
	//   - 如果调用 Close 时有未发送的消息，会继续尝试发送 5 秒
	//   - 重复调用 Close 是安全的，会直接返回 nil
	Close() error
}

// Producer Kafka生产者实现
type Producer struct {
	writer   *kafka.Writer   // Kafka底层写入器
	wg       sync.WaitGroup  // 等待组，用于优雅关闭
	closed   bool            // 是否已关闭
	mu       sync.Mutex      // 互斥锁，保护closed字段
	config   *ProducerConfig // 配置
	stopChan chan struct{}   // 停止信号通道
	msgChan  chan *Message   // 消息通道，用于背压控制
	errChan  chan error      // 错误通道
}

func getProducerConfig(config *ProducerConfig) *ProducerConfig {
	// 如果没有提供高级配置，使用默认值
	defConf := NewDefaultAdvancedConfig()
	if config.Advanced == nil {
		config.Advanced = defConf
	}

	if config.Advanced != nil && config.Advanced.ChannelBufferSize == 0 {
		config.Advanced.ChannelBufferSize = defConf.ChannelBufferSize
	}
	if config.Advanced != nil && config.Advanced.BatchSize == 0 {
		config.Advanced.BatchSize = defConf.BatchSize
	}
	if config.Advanced != nil && config.Advanced.BatchTimeout == 0 {
		config.Advanced.BatchTimeout = defConf.BatchTimeout
	}
	if config.Advanced != nil && config.Advanced.MaxRetries == 0 {
		config.Advanced.MaxRetries = defConf.MaxRetries
	}
	if config.Advanced != nil && config.Advanced.RetryInterval == 0 {
		config.Advanced.RetryInterval = defConf.RetryInterval
	}
	if config.Advanced != nil && config.Advanced.MaxWait == 0 {
		config.Advanced.MaxWait = defConf.MaxWait
	}
	// if config.Advanced != nil && config.Advanced.RequiredAcks == 0 {
	// 	config.Advanced.RequiredAcks = defConf.RequiredAcks
	// }
	if config.Advanced != nil && config.Advanced.ErrorHandler == nil {
		config.Advanced.ErrorHandler = defConf.ErrorHandler
	}
	return config
}

// NewProducer 创建新的Kafka生产者
func NewProducer(ctx context.Context, config *ProducerConfig) (KafkaProducer, error) {
	// 验证配置
	if err := ValidateProducerConfig(config); err != nil {
		return nil, fmt.Errorf("validate producer config failed: %v", err)
	}

	// 如果没有提供高级配置，使用默认值
	config = getProducerConfig(config)

	// 处理可能的多个brokers地址
	brokers := strings.Split(config.Basic.BootstrapServers, ",")
	if len(brokers) == 0 || (len(brokers) == 1 && brokers[0] == "") {
		return nil, fmt.Errorf("invalid bootstrap servers: %s", config.Basic.BootstrapServers)
	}

	// 配置安全选项
	var writer *kafka.Writer
	if config.Security != nil && config.Security.SecurityProtocol == "sasl_ssl" {
		tlsConfig, err := CreateTLSConfig(config.Security.SslCaLocation)
		if err != nil {
			return nil, fmt.Errorf("create TLS config failed: %v", err)
		}

		mechanism := plain.Mechanism{
			Username: config.Security.SaslUsername,
			Password: config.Security.SaslPassword,
		}

		// 创建Kafka写入器
		writer = &kafka.Writer{
			Addr:                   kafka.TCP(brokers...),
			Topic:                  config.Basic.Topic,
			Balancer:               &kafka.Hash{},
			BatchSize:              config.Advanced.BatchSize,
			BatchTimeout:           config.Advanced.BatchTimeout,
			RequiredAcks:           kafka.RequiredAcks(config.Advanced.RequiredAcks),
			AllowAutoTopicCreation: false,

			Transport: &kafka.Transport{
				Dial: (&net.Dialer{
					Timeout:   30 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				TLS:  tlsConfig,
				SASL: mechanism,
			},
		}

	} else {
		// 创建Kafka写入器
		writer = &kafka.Writer{
			Addr:                   kafka.TCP(brokers...),
			Topic:                  config.Basic.Topic,
			Balancer:               &kafka.Hash{},
			BatchSize:              config.Advanced.BatchSize,
			BatchTimeout:           config.Advanced.BatchTimeout,
			RequiredAcks:           kafka.RequiredAcks(config.Advanced.RequiredAcks),
			AllowAutoTopicCreation: true,
		}
	}

	// 创建生产者实例
	producer := &Producer{
		writer:   writer,
		config:   config,
		stopChan: make(chan struct{}),
		msgChan:  make(chan *Message, config.Advanced.ChannelBufferSize),
		errChan:  make(chan error, 1),
	}

	// 启动后台处理协程
	producer.start(ctx)

	return producer, nil
}

// start 启动生产者处理协程
func (p *Producer) start(ctx context.Context) {
	p.wg.Add(1)
	go p.processMessages(ctx)
}

// processMessages 处理消息的后台协程
func (p *Producer) processMessages(ctx context.Context) {
	defer p.wg.Done()

	batch := make([]*Message, 0, p.config.Advanced.BatchSize)
	batchTimer := time.NewTimer(p.config.Advanced.BatchTimeout)
	defer batchTimer.Stop()

	for {
		select {
		case <-ctx.Done():
			if len(batch) > 0 {
				p.sendBatch(ctx, batch)
			}
			return

		case <-p.stopChan:
			if len(batch) > 0 {
				p.sendBatch(ctx, batch)
			}
			return

		case msg, ok := <-p.msgChan:
			if !ok {
				if len(batch) > 0 {
					p.sendBatch(ctx, batch)
				}
				return
			}

			batch = append(batch, msg)
			if len(batch) >= p.config.Advanced.BatchSize {
				p.sendBatch(ctx, batch)
				batch = make([]*Message, 0, p.config.Advanced.BatchSize)
				if !batchTimer.Stop() {
					<-batchTimer.C
				}
				batchTimer.Reset(p.config.Advanced.BatchTimeout)
			}

		case <-batchTimer.C:
			if len(batch) > 0 {
				p.sendBatch(ctx, batch)
				batch = make([]*Message, 0, p.config.Advanced.BatchSize)
			}
			batchTimer.Reset(p.config.Advanced.BatchTimeout)
		}
	}
}

// sendBatch 发送一批消息
func (p *Producer) sendBatch(ctx context.Context, batch []*Message) {
	if len(batch) == 0 {
		return
	}

	// 转换为Kafka消息
	kafkaMsgs := make([]kafka.Message, len(batch))
	for i, msg := range batch {
		kafkaMsgs[i] = kafka.Message{
			Key:   msg.Key,
			Value: msg.Value,
		}
	}

	// 带重试的发送
	var err error
	for i := 0; i <= p.config.Advanced.MaxRetries; i++ {
		select {
		case <-ctx.Done():
			// 不传入ctx，使用context.Background()，取消会导致消息发送失败
			cancelCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			p.writer.WriteMessages(cancelCtx, kafkaMsgs...)

			return
		default:
			// 继续执行
		}

		// 不传入ctx，使用context.Background()，取消会导致消息发送失败
		err = p.writer.WriteMessages(context.Background(), kafkaMsgs...)
		if err == nil {
			return
		}

		if i < p.config.Advanced.MaxRetries {
			// if p.config.Advanced.ErrorHandler != nil {
			// 	p.config.Advanced.ErrorHandler(batch, fmt.Errorf("kafka-producer send msg failed(retry %d/%d), topic: %v, err: %v", i+1, p.config.Advanced.MaxRetries, p.config.Basic.Topic, err))
			// } else {
			// 	log.Printf("kafka-producer send msg failed(retry %d/%d), topic: %v, err: %v", i+1, p.config.Advanced.MaxRetries, p.config.Basic.Topic, err)
			// }

			time.Sleep(p.config.Advanced.RetryInterval)
			continue
		}
	}

	if err != nil {
		if p.config.Advanced.ErrorHandler != nil {
			p.config.Advanced.ErrorHandler(batch, fmt.Errorf("kafka-producer send msg failed, topic: %v, err: %v", p.config.Basic.Topic, err))
		} else {
			log.Printf("kafka-producer send msg failed, topic: %v, err: %v", p.config.Basic.Topic, err)
		}

	}
}

// Produce 发送消息
// 支持批量发送多条消息
func (p *Producer) Produce(ctx context.Context, msgs []*Message) error {
	// 检查上下文是否已取消
	if ctx.Err() != nil {
		return ctx.Err()
	}

	// 检查生产者是否已关闭
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return fmt.Errorf("producer closed")
	}
	p.mu.Unlock()

	// 检查消息是否为空
	if len(msgs) == 0 {
		return nil
	}

	// 分批发送到消息通道
	for i := 0; i < len(msgs); i++ {
		select {
		case p.msgChan <- msgs[i]:
			continue
		case <-ctx.Done():
			return ctx.Err()
		default:
			return fmt.Errorf("producer msg chan full")
		}
	}

	return nil
}

// Close 关闭生产者
// 会等待所有已入队的消息处理完成
func (p *Producer) Close() error {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return nil
	}
	p.closed = true
	p.mu.Unlock()

	// 关闭停止通道，通知处理协程退出
	close(p.stopChan)

	// 等待所有消息处理完成
	p.wg.Wait()

	// 关闭消息通道
	close(p.msgChan)

	// 关闭writer
	if p.writer != nil {
		return p.writer.Close()
	}

	return nil
}
