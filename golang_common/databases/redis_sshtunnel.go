package databases

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/ssh"
)

func NewRedisSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn string) (*ssh.Client, *redis.Client, error) {

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         15 * time.Second,
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	if sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig); err == nil {
		redisDB := redis.NewClient(&redis.Options{
			Dialer:       utilities.NewViaSSHDialer(sshcon).DialForRedis,
			Addr:         dsn,
			Password:     "",
			DB:           0,
			ReadTimeout:  -2,
			WriteTimeout: -2,
		})

		err = redisDB.Ping(context.Background()).Err()

		return sshcon, redisDB, err
	}

	return nil, nil, err
}
