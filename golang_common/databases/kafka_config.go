package databases

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"os"
	"time"
)

// KafkaBasicConfig 定义 Kafka 基础配置（必填项）
type KafkaBasicConfig struct {
	Topic            string // 主题名称
	BootstrapServers string // Kafka 服务器地址，多个地址用逗号分隔
}

// KafkaSecurityConfig 定义 Kafka 安全配置（可选，但如果需要认证则所有字段都必填）
type KafkaSecurityConfig struct {
	SecurityProtocol string // 安全协议，如"sasl_ssl"
	SslCaLocation    string // SSL CA证书路径
	SaslUsername     string // SASL用户名
	SaslPassword     string // SASL密码
}

// KafkaAdvancedConfig 定义 Kafka 高级配置（所有字段都是可选的）
type KafkaAdvancedConfig struct {
	// 性能调优
	ChannelBufferSize int           `default:"10000"` // 消息通道缓冲大小
	BatchSize         int           `default:"100"`   // 批次大小
	BatchTimeout      time.Duration `default:"1s"`    // 批量发送超时时间
	WorkerCount       int           `default:"0"`     // 工作协程数量，0表示使用CPU核心数

	// 重试配置
	MaxRetries    int           `default:"3"`  // 重试次数
	RetryInterval time.Duration `default:"1s"` // 重试间隔
	MaxWait       time.Duration `default:"3s"` // 最大等待时间

	// 可靠性配置
	RequiredAcks int `default:"0"` // 0: 不等待确认, 1: 等待leader确认, -1: 等待所有ISR确认

	// 错误处理
	ErrorHandler func([]*Message, error) // 错误处理函数
}

// ProducerConfig 定义生产者配置
type ProducerConfig struct {
	Basic    KafkaBasicConfig     // 必填的基础配置
	Security *KafkaSecurityConfig // 可选的安全配置
	Advanced *KafkaAdvancedConfig // 可选的高级配置
}

// ConsumerConfig 定义消费者配置
type ConsumerConfig struct {
	Basic    KafkaBasicConfig     // 必填的基础配置
	GroupId  string               // 消费者组ID（必填）
	Security *KafkaSecurityConfig // 可选的安全配置
	Advanced *KafkaAdvancedConfig // 可选的高级配置
}

// NewDefaultAdvancedConfig 返回默认的高级配置
func NewDefaultAdvancedConfig() *KafkaAdvancedConfig {
	return &KafkaAdvancedConfig{
		ChannelBufferSize: 10000,
		BatchSize:         100,
		BatchTimeout:      time.Second,
		MaxRetries:        3,
		RetryInterval:     time.Second,
		MaxWait:           3 * time.Second,
		RequiredAcks:      0,
	}
}

// Validate 验证基础配置
func (c *KafkaBasicConfig) Validate() error {
	if c.Topic == "" {
		return fmt.Errorf("Topic 不能为空")
	}
	if c.BootstrapServers == "" {
		return fmt.Errorf("BootstrapServers 不能为空")
	}
	return nil
}

// Validate 验证安全配置
func (c *KafkaSecurityConfig) Validate() error {
	if c == nil {
		return nil
	}
	if c.SecurityProtocol != "" {
		if c.SslCaLocation == "" {
			return fmt.Errorf("启用安全协议时 SslCaLocation 不能为空")
		}
		if c.SaslUsername == "" {
			return fmt.Errorf("启用安全协议时 SaslUsername 不能为空")
		}
		if c.SaslPassword == "" {
			return fmt.Errorf("启用安全协议时 SaslPassword 不能为空")
		}
	}
	return nil
}

// ValidateProducerConfig 验证生产者配置
func ValidateProducerConfig(config *ProducerConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	if err := config.Basic.Validate(); err != nil {
		return err
	}
	if err := config.Security.Validate(); err != nil {
		return err
	}
	return nil
}

// ValidateConsumerConfig 验证消费者配置
func ValidateConsumerConfig(config *ConsumerConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	if err := config.Basic.Validate(); err != nil {
		return err
	}
	if config.GroupId == "" {
		return fmt.Errorf("GroupId 不能为空")
	}
	if err := config.Security.Validate(); err != nil {
		return err
	}
	return nil
}

// Message 要发送的消息结构
type Message struct {
	Key   []byte
	Value []byte
}

// MarshalJSON 自定义 Message 的 JSON 序列化方法
func (m *Message) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf(`{"key":"%s","value":"%s"}`, string(m.Key), string(m.Value))), nil
}

// String 返回消息的字符串表示
func (m *Message) String() string {
	return fmt.Sprintf("Key: %s, Value: %s", string(m.Key), string(m.Value))
}

// createTLSConfig 创建TLS配置
func CreateTLSConfig(caPath string) (*tls.Config, error) {
	caCert, err := os.ReadFile(caPath)
	if err != nil {
		return nil, fmt.Errorf("读取CA证书失败: %v", err)
	}

	caCertPool := x509.NewCertPool()
	caCertPool.AppendCertsFromPEM(caCert)

	return &tls.Config{
		RootCAs:            caCertPool,
		InsecureSkipVerify: false,
	}, nil
}

// KafkaError 定义Kafka错误类型
type KafkaError struct {
	err       error
	temporary bool
	retryable bool
}

func (e *KafkaError) Error() string {
	return e.err.Error()
}

func (e *KafkaError) Temporary() bool {
	return e.temporary
}

func (e *KafkaError) Retryable() bool {
	return e.retryable
}
