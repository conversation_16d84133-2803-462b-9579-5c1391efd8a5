package databases

import (
	"context"
	"log"
	"os"
	"testing"

	"github.com/redis/go-redis/v9"
	"gotest.tools/assert"
)

func getRedisConn() (*redis.Client, error) {
	sshHost, _ := os.LookupEnv("SSH_TUNNEL_HOST")
	sshPort, _ := os.LookupEnv("SSH_TUNNEL_PORT")
	sshUser, _ := os.LookupEnv("SSH_TUNNEL_USER")
	sshPass, _ := os.LookupEnv("SSH_TUNNEL_PASSWORD")
	privateKey, _ := os.LookupEnv("SSH_TUNNEL_PRIVATE_KEY")
	dsn, _ := os.LookupEnv("REDIS_DSN")

	_, redisConn, err := NewRedisSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn)
	if err != nil {
		return nil, err
	}

	return redisConn, nil
}
func TestRedisHSetNX(t *testing.T) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("TestBatchSaveDauReqHourly error:", err)
		}
	}()

	redisConn, err := getRedisConn()
	if err != nil {
		log.Panicln(err)
	}
	assert.NilError(t, err)

	ctx := context.Background()
	key := "test_ssh_tunnel"
	r, err := redisConn.HSetNX(ctx, key, "field", 1).Result()
	log.Println(r, err)

	r, err = redisConn.HSetNX(ctx, key, "field", 1).Result()
	log.Println(r, err)

	value, err := redisConn.Del(ctx, key).Result()
	log.Println(value, err)

	assert.NilError(t, err)

}

func TestRedisHSetNXWithPipeline(t *testing.T) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("TestRedisHSetNXWithPipeline error:", err)
		}
	}()

	redisConn, err := getRedisConn()
	if err != nil {
		log.Panicln(err)
	}
	assert.NilError(t, err)

	ctx := context.Background()
	key := "test_ssh_tunnel"

	pipe := redisConn.Pipeline()

	for i := 0; i < 10; i++ {
		pipe.HSetNX(ctx, key, "field", i).Result()
	}

	cmds, err := pipe.Exec(ctx)

	assert.NilError(t, err)

	for _, cmd := range cmds {
		obj := cmd.(*redis.BoolCmd).Val()

		log.Println(obj)
	}

	value, err := redisConn.Del(ctx, key).Result()
	log.Println(value, err)

	assert.NilError(t, err)
}
