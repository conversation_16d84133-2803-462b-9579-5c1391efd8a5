package databases

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	"github.com/go-sql-driver/mysql"
	"golang.org/x/crypto/ssh"

	"github.com/jmoiron/sqlx"
)

func NewMysqlSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn string) (*ssh.Client, *sqlx.DB, error) {

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	if sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig); err == nil {
		mysql.RegisterDialContext("tcp", utilities.NewViaSSHDialer(sshcon).Dial)
		if db, err := sql.Open("mysql", dsn); err == nil {
			mysqlDB := sqlx.NewDb(db, "mysql")
			if err = mysqlDB.Ping(); err == nil {
				return sshcon, mysqlDB, nil
			}
			return sshcon, mysqlDB, err
		}
		return sshcon, nil, err
	}
	return nil, nil, err
}

func NewMysqlSQLSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn string) (*ssh.Client, *sql.DB, error) {

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	if sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig); err == nil {
		mysql.RegisterDialContext("tcp", utilities.NewViaSSHDialer(sshcon).Dial)
		if db, err := sql.Open("mysql", dsn); err == nil {
			if err = db.Ping(); err == nil {
				return sshcon, db, nil
			}
			return sshcon, db, err
		}
		return sshcon, nil, err
	}
	return nil, nil, err
}
