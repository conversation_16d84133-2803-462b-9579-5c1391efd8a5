# Kafka 客户端

这是一个基于 `segmentio/kafka-go` 封装的高性能 Kafka 客户端，提供了生产者和消费者的简单易用的接口。

## 特性

### 生产者特性
- 异步批量发送消息，提高吞吐量
- 内部 Channel 实现背压控制
- 支持批量发送和自动重试
- 支持 SASL/SSL 安全认证
- 合理的默认配置和必填项验证
- 支持自定义错误处理
- 优雅关闭机制

### 消费者特性
- 多协程并行处理消息（默认为 CPU 核心数）
- 支持批量提交 offset
- 智能的错误分类和重试机制
- 支持 SASL/SSL 安全认证
- 自动的资源清理
- 优雅关闭机制

## 快速开始

### 生产者示例

```go
// 创建生产者配置
config := &databases.ProducerConfig{
    Basic: &databases.BasicConfig{
        BootstrapServers: "localhost:9092",
        Topic:           "test-topic",
    },
    // 可选：安全配置
    Security: &databases.SecurityConfig{
        SecurityProtocol: "sasl_ssl",
        SaslUsername:    "user",
        SaslPassword:    "password",
        SslCaLocation:   "/path/to/ca.pem",
    },
    // 可选：高级配置
    Advanced: &databases.AdvancedConfig{
        BatchSize:      100,
        BatchTimeout:   time.Second,
        MaxRetries:     3,
        RetryInterval:  time.Second,
    },
}

// 创建生产者
producer, err := databases.NewProducer(context.Background(), config)
if err != nil {
    log.Fatal(err)
}
defer producer.Close()

// 发送消息
msgs := []*databases.Message{
    {
        Key:   []byte("key1"),
        Value: []byte("value1"),
    },
}
err = producer.Produce(context.Background(), msgs)
if err != nil {
    log.Printf("发送消息失败: %v", err)
}
```

### 消费者示例

```go
// 创建消费者配置
config := &databases.ConsumerConfig{
    Basic: &databases.BasicConfig{
        BootstrapServers: "localhost:9092",
        Topic:           "test-topic",
    },
    GroupId: "test-group",
    // 可选：安全配置
    Security: &databases.SecurityConfig{
        SecurityProtocol: "sasl_ssl",
        SaslUsername:    "user",
        SaslPassword:    "password",
        SslCaLocation:   "/path/to/ca.pem",
    },
    // 可选：高级配置
    Advanced: &databases.AdvancedConfig{
        BatchSize:      100,
        BatchTimeout:   time.Second,
        MaxRetries:     3,
        RetryInterval:  time.Second,
        WorkerCount:    4, // 指定工作协程数
    },
}

// 创建消费者
consumer, err := databases.NewConsumer(context.Background(), config)
if err != nil {
    log.Fatal(err)
}
defer consumer.Close()

// 启动消费
err = consumer.Consume(context.Background(), func(msg *kafka.Message) error {
    log.Printf("收到消息: key=%s, value=%s", string(msg.Key), string(msg.Value))
    return nil
})
if err != nil {
    log.Printf("消费消息失败: %v", err)
}
```

## 配置说明

### 基础配置 (BasicConfig)
- `BootstrapServers`: Kafka 服务器地址，多个地址用逗号分隔
- `Topic`: 主题名称

### 安全配置 (SecurityConfig)
- `SecurityProtocol`: 安全协议，支持 "sasl_ssl"
- `SaslUsername`: SASL 用户名
- `SaslPassword`: SASL 密码
- `SslCaLocation`: SSL CA 证书路径

### 高级配置 (AdvancedConfig)
- `BatchSize`: 批量处理的消息数量
- `BatchTimeout`: 批量处理的超时时间
- `MaxRetries`: 最大重试次数
- `RetryInterval`: 重试间隔时间
- `ChannelBufferSize`: 内部通道缓冲大小
- `WorkerCount`: 消费者工作协程数量（仅消费者）
- `ErrorHandler`: 自定义错误处理函数

## 错误处理

该客户端实现了智能的错误分类机制，将错误分为以下几类：
- 临时错误（如网络问题）：自动重试
- 可重试错误：按配置的重试策略处理
- 不可重试错误：立即返回错误

可以通过配置 `ErrorHandler` 来自定义错误处理逻辑：

```go
config.Advanced.ErrorHandler = func(msgs []*Message, err error) {
    log.Printf("处理消息失败: %v", err)
}
```

## 最佳实践

1. 合理设置批量大小和超时时间
   - 较大的批量大小可以提高吞吐量
   - 较短的超时时间可以减少延迟

2. 使用适当的工作协程数
   - 默认使用 CPU 核心数
   - 可以根据实际负载调整

3. 正确处理上下文取消
   - 使用 context.Context 控制生命周期
   - 确保优雅关闭

4. 实现错误处理
   - 配置合理的重试策略
   - 添加自定义错误处理函数

5. 资源管理
   - 使用 defer 关闭资源
   - 确保消息都被正确提交

## 限制说明

1. 不支持的功能：
   - 动态调整分区
   - 手动分区分配
   - 消息压缩
   - 消息头部设置
   - 运行时动态配置

2. 性能考虑：
   - 批量处理会带来一定延迟
   - 需要合理设置缓冲区大小

3. 错误处理：
   - 部分错误可能需要人工介入
   - 网络异常恢复机制可能需要优化
