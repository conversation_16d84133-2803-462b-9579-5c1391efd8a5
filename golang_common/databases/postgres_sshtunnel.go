package databases

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	"golang.org/x/crypto/ssh"
)

func NewPostgresSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn string) (*ssh.Client, *sql.DB, error) {

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	if sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig); err == nil {
		if !HasDriver("postgres+ssh") {
			sql.Register("postgres+ssh", utilities.NewPostgresViaSSHDialer(sshcon))
		}
		if db, err := sql.Open("postgres+ssh", dsn); err == nil {
			if err = db.Ping(); err == nil {
				return sshcon, db, nil
			}
			return sshcon, db, err
		}
		return sshcon, nil, err
	}
	return nil, nil, err
}

func HasDriver(name string) (has bool) {
	for _, dName := range sql.Drivers() {
		if dName == name {
			return true
		}
	}
	return false
}
