package databases

import (
	"crypto/tls"
	"crypto/x509"
	"io"
	"log"
	"os"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"
)

func NewKafkaSASLSSLDialer(
	caCertPath string,
	username string,
	password string,
) (dialer *kafka.Dialer) {
	caCertFile, err := os.Open(caCertPath)
	if err != nil {
		log.Fatalf("Failed to open CA certificate file: %v", err)
	}
	defer caCertFile.Close()

	caCert, err := io.ReadAll(caCertFile)
	if err != nil {
		log.Fatalf("Failed to read CA certificate: %v", err)
	}
	caCertPool := x509.NewCertPool()
	if !caCertPool.AppendCertsFromPEM(caCert) {
		log.Fatal("Failed to append CA certificate to pool")
	}
	tlsConfig := &tls.Config{
		RootCAs: caCertPool,
	}

	mechanism := plain.Mechanism{
		Username: username,
		Password: password,
	}

	dialer = &kafka.Dialer{
		SASLMechanism: mechanism,
		TLS:           tlsConfig,
	}

	return
}
