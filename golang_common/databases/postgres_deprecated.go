package databases

import (
	"database/sql"

	_ "github.com/lib/pq"

	"github.com/jmoiron/sqlx"
)

// Deprecated
func NewPostgress(dsn string) (*sqlx.DB, error) {
	if postgres, err := sqlx.Open("postgres", dsn); err == nil {
		if err = postgres.Ping(); err == nil {
			return postgres, nil
		} else {
			return nil, err
		}
	} else {
		return nil, err
	}
}

// Deprecated: use NewPostgres instead
func NewPostgressSQL(dsn string) (*sql.DB, error) {
	if postgres, err := sql.Open("postgres", dsn); err == nil {
		if err = postgres.Ping(); err == nil {
			return postgres, nil
		} else {
			return nil, err
		}
	} else {
		return nil, err
	}
}
