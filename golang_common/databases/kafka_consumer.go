package databases

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"
)

// KafkaConsumer Kafka消费接口
type KafkaConsumer interface {
	// Consume 启动消费处理
	//
	// 参数:
	//   - ctx: 上下文，用于控制消费超时和取消
	//   - handler: 消息处理函数，用于处理每条消费到的消息
	//
	// 消费流程:
	//   1. 启动消费者协程从 Kafka 读取消息
	//   2. 启动多个工作协程处理消息（默认为 CPU 核心数）
	//   3. 消息按批次处理和提交
	//
	// 消息处理:
	//   1. 消息首先由消费者协程读取并放入消息通道
	//   2. 工作协程从通道中获取消息并调用 handler 处理
	//   3. 处理完成的消息会按批次提交 offset
	//
	// 批次提交:
	//   1. 当消息数量达到 BatchSize 时提交
	//   2. 当达到 CommitInterval 时提交
	//   3. 当上下文取消时，提交所有已处理消息
	//
	// 错误处理:
	//   1. 如果 handler 返回错误，消费会停止
	//   2. 如果提交失败，会进行重试
	//   3. 如果重试仍失败，消费会停止
	//
	// 注意事项:
	//   - 此方法会阻塞直到上下文取消或发生错误
	//   - 如果需要主动停止消费，可以取消上下文
	Consume(ctx context.Context, handler MessageHandler) error

	// Close 优雅关闭消费者
	//
	// 关闭流程:
	//   1. 标记消费者为已关闭状态
	//   2. 关闭停止信号通道，通知所有协程退出
	//   3. 等待所有工作协程完成
	//   4. 提交所有已处理的消息
	//   5. 关闭底层 Kafka reader
	//
	// 注意事项:
	//   - 会等待所有正在处理的消息完成
	//   - 会尝试提交所有已处理消息的 offset
	//   - 重复调用 Close 是安全的
	Close() error
}

// Consumer Kafka消费者封装
type Consumer struct {
	reader   *kafka.Reader
	topics   []string
	running  bool
	mu       sync.Mutex
	wg       sync.WaitGroup
	stopChan chan struct{}
	config   *ConsumerConfig // 使用新的配置结构
}

func getConsumerConfig(config *ConsumerConfig) *ConsumerConfig {
	defConf := NewDefaultAdvancedConfig()
	if config.Advanced == nil {
		config.Advanced = defConf
	}

	if config.Advanced != nil && config.Advanced.ChannelBufferSize == 0 {
		config.Advanced.ChannelBufferSize = defConf.ChannelBufferSize
	}
	if config.Advanced != nil && config.Advanced.BatchSize == 0 {
		config.Advanced.BatchSize = defConf.BatchSize
	}
	if config.Advanced != nil && config.Advanced.BatchTimeout == 0 {
		config.Advanced.BatchTimeout = defConf.BatchTimeout
	}
	if config.Advanced != nil && config.Advanced.MaxRetries == 0 {
		config.Advanced.MaxRetries = defConf.MaxRetries
	}
	if config.Advanced != nil && config.Advanced.RetryInterval == 0 {
		config.Advanced.RetryInterval = defConf.RetryInterval
	}
	if config.Advanced != nil && config.Advanced.MaxWait == 0 {
		config.Advanced.MaxWait = defConf.MaxWait
	}
	if config.Advanced != nil && config.Advanced.RequiredAcks == 0 {
		config.Advanced.RequiredAcks = defConf.RequiredAcks
	}
	if config.Advanced != nil && config.Advanced.ErrorHandler == nil {
		config.Advanced.ErrorHandler = defConf.ErrorHandler
	}

	return config
}

// NewConsumer 创建新的Kafka消费者
func NewConsumer(ctx context.Context, config *ConsumerConfig) (KafkaConsumer, error) {
	// 验证配置
	if err := ValidateConsumerConfig(config); err != nil {
		return nil, fmt.Errorf("validate consumer config failed: %v", err)
	}

	// 如果没有提供高级配置，使用默认值
	config = getConsumerConfig(config)

	brokers := strings.Split(config.Basic.BootstrapServers, ",")
	// 清理broker地址
	for i, broker := range brokers {
		brokers[i] = strings.TrimSpace(broker)
	}

	if len(brokers) == 0 || brokers[0] == "" {
		return nil, fmt.Errorf("invalid bootstrap servers: %s", config.Basic.BootstrapServers)
	}

	// 创建基本的Reader配置，支持老代码的所有配置项
	readerConfig := kafka.ReaderConfig{
		Brokers:         brokers,
		Topic:           config.Basic.Topic,
		GroupID:         config.GroupId,
		MaxWait:         config.Advanced.MaxWait, // 兼容老代码
		StartOffset:     kafka.LastOffset,
		ReadLagInterval: -1, // 禁用读取延迟统计，减少额外请求
	}

	// 配置安全选项
	if config.Security != nil && config.Security.SecurityProtocol == "sasl_ssl" {
		tlsConfig, err := CreateTLSConfig(config.Security.SslCaLocation)
		if err != nil {
			return nil, fmt.Errorf("create TLS config failed: %v", err)
		}

		mechanism := plain.Mechanism{
			Username: config.Security.SaslUsername,
			Password: config.Security.SaslPassword,
		}

		readerConfig.Dialer = &kafka.Dialer{
			Timeout:       30 * time.Second,
			DualStack:     true,
			SASLMechanism: mechanism,
			TLS:           tlsConfig,
		}
	} else {
		// 创建默认的Dialer，避免nil指针
		readerConfig.Dialer = &kafka.Dialer{
			Timeout:   30 * time.Second,
			DualStack: true,
		}
	}

	consumer := &Consumer{
		reader:   kafka.NewReader(readerConfig),
		topics:   []string{config.Basic.Topic},
		stopChan: make(chan struct{}),
		config:   config,
	}

	// 监听context取消信号
	if ctx != nil {
		go func() {
			select {
			case <-ctx.Done():
				consumer.Close()
			case <-consumer.stopChan:
				return
			}
		}()
	}

	return consumer, nil
}

// MessageHandler 消息处理函数类型
type MessageHandler func(msg *kafka.Message) error

// Consume 启动消费者
func (c *Consumer) Consume(ctx context.Context, handler MessageHandler) error {
	if !c.setRunning(true) {
		return fmt.Errorf("consumer is already running")
	}
	defer c.setRunning(false)

	// 创建上下文，用于控制内部操作
	consumeCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 使用缓冲通道处理错误和消息
	errChan := make(chan error, 1)
	msgChan := make(chan kafka.Message, c.optimalConcurrency()*2)

	// 启动消息消费和处理协程
	c.wg.Add(2)
	go c.consumeMessages(consumeCtx, msgChan, errChan)
	go c.processMessages(consumeCtx, msgChan, errChan, handler)

	// 等待上下文取消或出现错误
	select {
	case <-ctx.Done():
		c.stop()
		return ctx.Err()
	case err := <-errChan:
		c.stop()
		return err
	}
}

// consumeMessages 从Kafka读取消息
func (c *Consumer) consumeMessages(ctx context.Context, msgChan chan<- kafka.Message, errChan chan<- error) {
	defer c.wg.Done()
	defer close(msgChan)

	// 动态休眠时间，避免空轮询导致CPU占用过高
	sleepTime := 1 * time.Millisecond
	maxSleepTime := 100 * time.Millisecond

	for {
		select {
		case <-ctx.Done():
			return
		case <-c.stopChan:
			return
		default:
			msg, err := c.reader.FetchMessage(ctx)

			if err != nil {
				if ctx.Err() != nil {
					return
				}

				kafkaErr := classifyError(err)
				if kafkaErr == nil {
					continue
				}

				if kafkaErr.Temporary() {
					sleepTime = min(sleepTime*2, maxSleepTime)
					time.Sleep(sleepTime)
					continue
				}

				if !kafkaErr.Retryable() {
					select {
					case errChan <- fmt.Errorf("unrecoverable error: %v", err):
					case <-ctx.Done():
					}
					return
				}

				// 可重试的错误，记录并继续
				fmt.Printf("Retryable error occurred: %v\n", err)
				continue
			}

			// 成功获取消息，重置休眠时间
			sleepTime = 1 * time.Millisecond

			select {
			case msgChan <- msg:
			case <-ctx.Done():
				return
			case <-c.stopChan:
				return
			}
		}
	}
}

// processMessages 处理消息
func (c *Consumer) processMessages(ctx context.Context, msgChan <-chan kafka.Message, errChan chan<- error, handler MessageHandler) {
	defer c.wg.Done()

	// 创建worker池
	workerCount := runtime.NumCPU()
	if c.config.Advanced != nil && c.config.Advanced.WorkerCount > 0 {
		workerCount = c.config.Advanced.WorkerCount
	}

	var wg sync.WaitGroup
	wg.Add(workerCount)

	// 为每个worker创建独立的错误通道
	workerErrChans := make([]chan error, workerCount)
	for i := 0; i < workerCount; i++ {
		workerErrChans[i] = make(chan error, 1)
	}

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		workerID := i
		go func() {
			defer wg.Done()
			defer close(workerErrChans[workerID])

			batchSize := c.config.Advanced.BatchSize
			commitInterval := c.config.Advanced.BatchTimeout
			if batchSize == 0 {
				batchSize = 100
			}
			if commitInterval == 0 {
				commitInterval = time.Second
			}

			batch := make([]kafka.Message, 0, batchSize)
			commitTicker := time.NewTicker(commitInterval)
			defer commitTicker.Stop()

			// 提交当前批次的消息
			commitBatch := func() error {
				if len(batch) == 0 {
					return nil
				}
				// 提交消息不要使用ctx，使用context.Background()，当ctx.Done()时，保证提交消息成功
				if err := c.commitWithRetry(context.Background(), batch); err != nil {
					log.Printf("kafka-consumer Commit batch failed, topic: %v, group: %v, error: %v\n", c.config.Basic.Topic, c.config.GroupId, err)
					return err
				}
				batch = batch[:0]
				return nil
			}

			for {
				select {
				case msg, ok := <-msgChan:
					if !ok {
						if err := commitBatch(); err != nil {
							workerErrChans[workerID] <- err
						}
						return
					}

					if err := handler(&msg); err != nil {
						workerErrChans[workerID] <- err
						return
					}

					batch = append(batch, msg)
					if len(batch) >= batchSize {
						if err := commitBatch(); err != nil {
							workerErrChans[workerID] <- err
							return
						}
					}

				case <-commitTicker.C:
					if err := commitBatch(); err != nil {
						workerErrChans[workerID] <- err
						return
					}

				case <-ctx.Done():
					if err := commitBatch(); err != nil {
						workerErrChans[workerID] <- err
					}
					return
				}
			}
		}()
	}

	// 等待所有worker完成并收集错误
	go func() {
		wg.Wait()
		for _, ch := range workerErrChans {
			if err := <-ch; err != nil {
				select {
				case errChan <- err:
				case <-ctx.Done():
				}
				return
			}
		}
	}()
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	c.stop()
	if c.reader != nil {
		return c.reader.Close()
	}
	return nil
}

// commitWithRetry 带重试的消息提交
func (c *Consumer) commitWithRetry(ctx context.Context, msgs []kafka.Message) error {
	maxRetries := 3
	retryInterval := time.Second

	if c.config.Advanced != nil {
		if c.config.Advanced.MaxRetries > 0 {
			maxRetries = c.config.Advanced.MaxRetries
		}
		if c.config.Advanced.RetryInterval > 0 {
			retryInterval = c.config.Advanced.RetryInterval
		}
	}

	var lastErr error
	for i := 0; i <= maxRetries; i++ {
		err := c.reader.CommitMessages(ctx, msgs...)
		if err == nil {
			return nil
		}

		lastErr = err
		if i < maxRetries {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryInterval):
				continue
			}
		}
	}

	return fmt.Errorf("commit msg failed（retry %d）: %v", maxRetries, lastErr)
}

// stop 停止消费者
func (c *Consumer) stop() {
	c.setRunning(false)

	// 关闭停止通道
	c.mu.Lock()
	select {
	case <-c.stopChan:
		// 已经关闭
	default:
		close(c.stopChan)
	}
	c.mu.Unlock()

	// 等待所有协程结束
	c.wg.Wait()
}

// setRunning 设置运行状态
func (c *Consumer) setRunning(running bool) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running == running {
		return false
	}
	c.running = running
	return true
}

// optimalConcurrency 计算最佳并发数
func (c *Consumer) optimalConcurrency() int {
	// 使用CPU核心数作为基础并发数
	concurrency := runtime.NumCPU()
	if concurrency < 1 {
		concurrency = 1
	}
	if concurrency > 16 {
		concurrency = 16 // 限制最大并发数
	}
	return concurrency
}

// min 返回两个值中的较小值
func min(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

// classifyError 对错误进行分类
func classifyError(err error) *KafkaError {
	if err == nil {
		return nil
	}

	errStr := err.Error()

	// 网络相关的临时错误
	if strings.Contains(errStr, "connection reset by peer") ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "i/o timeout") ||
		strings.Contains(errStr, "temporary") {
		return &KafkaError{
			err:       err,
			temporary: true,
			retryable: true,
		}
	}

	// 上下文取消或超时
	if strings.Contains(errStr, "context canceled") ||
		strings.Contains(errStr, "deadline exceeded") {
		return &KafkaError{
			err:       err,
			temporary: false,
			retryable: false,
		}
	}

	// Kafka特定错误
	if strings.Contains(errStr, "coordinator not available") ||
		strings.Contains(errStr, "leader not available") ||
		strings.Contains(errStr, "not coordinator") {
		return &KafkaError{
			err:       err,
			temporary: true,
			retryable: true,
		}
	}

	// 偏移量相关错误
	if strings.Contains(errStr, "offset out of range") {
		return &KafkaError{
			err:       err,
			temporary: false,
			retryable: false,
		}
	}

	// 默认为非临时性错误但可重试
	return &KafkaError{
		err:       err,
		temporary: false,
		retryable: true,
	}
}
