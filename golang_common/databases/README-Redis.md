# Redis 客户端接口说明文档

## 简介

本接口是对 Redis 客户端的封装，提供了完整的 Redis 操作功能，包括基础数据类型操作、Bitmap 操作和 Bloom Filter 操作。所有操作都支持超时控制，确保操作的可靠性和性能。

## 主要特性

1. 完整的 Redis 数据类型支持
   - String 类型操作
   - Hash 类型操作
   - List 类型操作
   - Set 类型操作
   - Sorted Set 类型操作
   - Bitmap 操作
   - Bloom Filter 操作

2. 超时控制
   - 所有操作都支持超时控制
   - 可配置的连接超时、读取超时、写入超时
   - 连接池超时控制

3. 连接池管理
   - 可配置的连接池大小
   - 最小空闲连接数控制
   - 最大重试次数设置

4. 批量操作支持
   - Pipeline 操作支持
   - 批量读写操作
   - 批量 Bloom Filter 操作

## 使用场景

### 1. Bitmap 使用场景

#### 1.1 用户在线状态统计
```go
// 记录用户在线状态
err := redisClient.SetBit(ctx, 5*time.Second, "user_online", userId, 1)

// 检查用户是否在线
isOnline, err := redisClient.GetBit(ctx, 5*time.Second, "user_online", userId)

// 统计在线用户数
onlineCount, err := redisClient.BitCount(ctx, 5*time.Second, "user_online", 0, -1)
```

#### 1.2 用户签到统计
```go
// 记录用户签到
err := redisClient.SetBit(ctx, 5*time.Second, "user_sign_in:"+userId, dayOffset, 1)

// 统计用户签到天数
signInDays, err := redisClient.BitCount(ctx, 5*time.Second, "user_sign_in:"+userId, 0, -1)
```

#### 1.3 活跃用户统计
```go
// 记录用户活跃
err := redisClient.SetBit(ctx, 5*time.Second, "active_users:"+date, userId, 1)

// 统计活跃用户数
activeCount, err := redisClient.BitCount(ctx, 5*time.Second, "active_users:"+date, 0, -1)
```

### 2. Bloom Filter 使用场景

#### 2.1 缓存穿透防护
```go
// 初始化商品ID布隆过滤器
err := redisClient.BloomAdd(ctx, 5*time.Second, "product_ids", productId)

// 检查商品是否存在
exists, err := redisClient.BloomExist(ctx, 5*time.Second, "product_ids", productId)
```

#### 2.2 用户邮箱注册查重
```go
// 检查邮箱是否已注册
exists, err := redisClient.BloomExist(ctx, 5*time.Second, "registered_emails", email)
if !exists {
    // 注册新用户
    err := redisClient.BloomAdd(ctx, 5*time.Second, "registered_emails", email)
}
```

#### 2.3 爬虫URL去重
```go
// 批量检查URL是否已爬取
urls := []string{"url1", "url2", "url3"}
existsList, err := redisClient.BloomExists(ctx, 5*time.Second, "crawled_urls", urls)

// 批量添加已爬取URL
err := redisClient.BloomAdds(ctx, 5*time.Second, "crawled_urls", urls)
```

## 性能优化建议

1. 连接池配置
   ```go
   redisClient, err := NewRedisClient(
       WithPoolSize(10 * runtime.GOMAXPROCS(0)),  // 根据CPU核心数设置
       WithMinIdleConns(5),                       // 保持最小空闲连接
       WithMaxRetries(3),                         // 设置重试次数
   )
   ```

2. 超时控制
   ```go
   // 设置合理的超时时间
   ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
   defer cancel()
   ```

3. 批量操作
   ```go
   // 使用Pipeline进行批量操作
   cmders, err := redisClient.PipelineExec(ctx, 5*time.Second, func(pipe redis.Pipeliner) error {
       pipe.Set(ctx, "key1", "value1", 0)
       pipe.Set(ctx, "key2", "value2", 0)
       return nil
   })
   ```

4. Bloom Filter 参数优化
   ```go
   // 根据预期数据量和误判率计算最优参数
   m, k := CalculateBloomFilterParams(1000000, 0.01)  // 100万数据，1%误判率
   ```

## 注意事项

1. Bitmap 使用注意
   - 适合存储密集数据
   - 需要预先知道最大ID
   - 不适合存储稀疏数据
   - 每个位只能存储0或1

2. Bloom Filter 使用注意
   - 可能存在误判（假阳性）
   - 不支持删除操作
   - 需要预先知道数据规模
   - 不能获取原始数据

3. 性能考虑
   - 合理设置连接池大小
   - 使用Pipeline进行批量操作
   - 设置适当的超时时间
   - 考虑使用集群部署

4. 错误处理
   - 所有操作都需要检查错误
   - 实现重试机制
   - 记录错误日志
   - 监控Redis性能

## 示例代码

### 基础操作示例
```go
// 创建Redis客户端
redisClient, err := NewRedisClient(
    WithAddr("localhost:6379"),
    WithPassword(""),
    WithDB(0),
)
if err != nil {
    log.Fatal(err)
}
defer redisClient.Close()

// 设置键值
err = redisClient.Set(ctx, 5*time.Second, "key", "value", 0)

// 获取键值
value, err := redisClient.Get(ctx, 5*time.Second, "key")
```

### Bitmap 操作示例
```go
// 设置位
err = redisClient.SetBit(ctx, 5*time.Second, "bitmap", 100, 1)

// 获取位
bit, err := redisClient.GetBit(ctx, 5*time.Second, "bitmap", 100)

// 统计位数
count, err := redisClient.BitCount(ctx, 5*time.Second, "bitmap", 0, -1)
```

### Bloom Filter 操作示例
```go
// 添加元素
err = redisClient.BloomAdd(ctx, 5*time.Second, "bloom", "value")

// 检查元素
exists, err := redisClient.BloomExist(ctx, 5*time.Second, "bloom", "value")

// 批量操作
values := []string{"value1", "value2", "value3"}
err = redisClient.BloomAdds(ctx, 5*time.Second, "bloom", values)
existsList, err := redisClient.BloomExists(ctx, 5*time.Second, "bloom", values)
``` 

