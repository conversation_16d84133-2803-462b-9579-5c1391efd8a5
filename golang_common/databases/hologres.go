package databases

import (
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

func NewHologres(
	dsn string,
	writeMode int,
	batchSize int,
	threadSize int,
	writeBatchByteSize int64,
	writeMaxIntervalMs int64,
) *holoclient.HoloClient {
	holoConfig := holoclient.NewHoloConfig(dsn)
	holoConfig.SetWriteMode(holoclient.HoloWriteMode(writeMode))
	holoConfig.SetBatchSize(batchSize)
	holoConfig.SetThreadSize(threadSize)
	holoConfig.SetWriteBatchByteSize(writeBatchByteSize)
	holoConfig.SetWriteMaxIntervalMs(writeMaxIntervalMs)

	client := holoclient.NewHoloClient(holoConfig)
	holoclient.HoloClientLoggerOpen()
	return client
}

func NewHologresTableSchema(client *holoclient.HoloClient, schemaName string, tableName string) *holoclient.HoloTableSchema {
	return client.GetTableschema(schemaName, tableName, false)
}

type HoloTime time.Time

func (t HoloTime) UnixMicroFixForHolo() int64 {
	return time.Time(t).UnixMicro() - 946684800000000
}
