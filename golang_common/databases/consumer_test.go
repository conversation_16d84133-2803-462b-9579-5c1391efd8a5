package databases

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
)

var (
	consumer     KafkaConsumer
	consumerOnce sync.Once
)

const (
	consumerGroup = "test"
)

// GetConsumer 获取Consumer单例实例
func GetConsumer(ctx context.Context) KafkaConsumer {
	consumerOnce.Do(func() {
		var err error
		consumer, err = NewConsumer(ctx, &ConsumerConfig{
			Basic: KafkaBasicConfig{
				Topic:            TestTopic,
				BootstrapServers: "alikafka-serverless-cn-fhh3zns0s04-1000.alikafka.aliyuncs.com:9093,alikafka-serverless-cn-fhh3zns0s04-2000.alikafka.aliyuncs.com:9093,alikafka-serverless-cn-fhh3zns0s04-3000.alikafka.aliyuncs.com:9093",
			},
			GroupId: consumerGroup,
			Security: &KafkaSecurityConfig{
				SecurityProtocol: "sasl_ssl",
				SaslUsername:     KafkaUserName,
				SaslPassword:     KafkaPassword,
				SslCaLocation:    "/Users/<USER>/Documents/tools/only-4096-ca-cert",
			},
		})
		if err != nil {
			fmt.Printf("NewConsumer err:%v\n", err)
		}
	})

	return consumer
}

// TestConsume 测试消费消息
func TestConsume(t *testing.T) {
	// 使用新的消费者组ID，避免偏移量问题
	uniqueGroupID := fmt.Sprintf("test_group_%d", time.Now().UnixNano())
	fmt.Printf("使用唯一消费者组ID: %s\n", uniqueGroupID)

	cancelCtx, cancel := context.WithTimeout(ctx, 35*time.Second)
	defer cancel()

	// 直接创建新的消费者实例，不使用单例
	consumer := GetConsumer(cancelCtx)
	if consumer == nil {
		t.Skip("跳过测试：无法创建Consumer")
		return
	}
	defer consumer.Close()

	// 定义消息处理函数
	var messageReceived bool
	handler := func(msg *kafka.Message) error {
		fmt.Printf("收到消息: Topic=%s, Partition=%d, Offset=%d, Key=%s, Value=%s\n",
			msg.Topic, msg.Partition, msg.Offset,
			string(msg.Key),
			string(msg.Value))
		messageReceived = true
		return nil
	}

	fmt.Println("开始消费消息...")

	// 启动消费
	err := consumer.Consume(ctx, handler)
	if err != nil && err != context.DeadlineExceeded {
		t.Fatalf("Consume失败: %v", err)
	}

	if !messageReceived {
		t.Errorf("在测试时间内没有收到消息，请检查以下可能的原因：\n" +
			"1. Topic名称是否正确\n" +
			"2. 是否有消息发送到该Topic\n" +
			"3. 安全配置是否正确\n" +
			"4. 网络连接是否正常")
	}
}
