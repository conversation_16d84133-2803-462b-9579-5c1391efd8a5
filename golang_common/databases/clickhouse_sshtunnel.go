package databases

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"

	"github.com/ClickHouse/clickhouse-go"
	"github.com/jmoiron/sqlx"
	"golang.org/x/crypto/ssh"
)

func NewClickhouseSSHTunnel(sshHost, sshPort, sshUser, sshPass, privateKey, dsn string) (*ssh.Client, *sqlx.DB, error) {

	sshConfig := &ssh.ClientConfig{
		User:            sshUser,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	key, err := os.ReadFile(privateKey)
	if err != nil {
		log.Fatalf("Unable to read private key: %v", err)
	}
	signer, err := ssh.ParsePrivateKey(key)
	if signer != nil {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PublicKeysCallback(func() ([]ssh.Signer, error) {
			return []ssh.Signer{signer}, err
		}))
	}

	if sshPass != "" {
		sshConfig.Auth = append(sshConfig.Auth, ssh.PasswordCallback(func() (string, error) {
			return sshPass, nil
		}))
	}

	if sshcon, err := ssh.Dial("tcp", fmt.Sprintf("%s:%s", sshHost, sshPort), sshConfig); err == nil {
		clickhouse.RegisterDial(utilities.NewViaSSHDialer(sshcon).DialForClickhouse)
		if db, err := sql.Open("clickhouse", dsn); err == nil {
			clickhouseDB := sqlx.NewDb(db, "clickhouse")
			if err = clickhouseDB.Ping(); err == nil {
				return sshcon, clickhouseDB, nil
			}
			return sshcon, clickhouseDB, err
		}
		return sshcon, nil, err
	}
	return nil, nil, err
}
