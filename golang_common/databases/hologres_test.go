package databases

import (
	"log"
	"os"
	"strconv"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

func TestHologres(t *testing.T) {

	holoclient.HoloClientLoggerOpen()

	connInfo, _ := os.LookupEnv("POSTGRES_DSN")
	config := holoclient.NewHoloConfig(connInfo)
	client := holoclient.NewHoloClient(config)
	schema := client.GetTableschema("public", "test", false)

	for i := 0; i < 5; i++ {
		get := holoclient.NewGetRequest(schema)
		get.SetGetValByColIndex(0, strconv.Itoa(i), len(strconv.Itoa(i)))
		//get.SetGetValByColName("created_at", strconv.Itoa(i), len(strconv.Itoa(i)))

		res := client.Get(get)
		log.Printf("Record %d:\n", i)
		if res == nil {
			log.Println("No record")
		} else {
			for col := 0; col < schema.NumColumns(); col++ {
				col_val := res.GetVal(col)
				log.Println(col_val)
			}
		}
		get.DestroyGet()
	}

	client.Flush()
	client.Close()

	holoclient.HoloClientLoggerClose()
}
