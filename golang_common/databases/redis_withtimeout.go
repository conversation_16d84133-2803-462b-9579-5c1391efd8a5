package databases

import (
	"context"
	"fmt"
	"net"
	"runtime"
	"time"

	"github.com/redis/go-redis/v9"
)

const (
	// DefaultMinIdleConns 连接池默认最小空闲连接数
	DefaultMinIdleConns = 0
	// DefaultMaxRetries 默认最大重试次数
	DefaultMaxRetries = 3
	// DefaultDialTimeout 默认连接超时时间
	DefaultDialTimeout = 5 * time.Second
	// DefaultReadTimeout 默认读取超时时间
	DefaultReadTimeout = 3 * time.Second
	// DefaultWriteTimeout 默认写入超时时间
	DefaultWriteTimeout = 3 * time.Second
	// DefaultPoolTimeout 默认连接池超时时间
	DefaultPoolTimeout = 4 * time.Second
)

// Redis Redis客户端接口定义
type Redis interface {
	// 基础操作（带超时控制）
	Get(ctx context.Context, timeout time.Duration, key string) (string, error)
	Set(ctx context.Context, timeout time.Duration, key string, value interface{}, expiration time.Duration) error
	Del(ctx context.Context, timeout time.Duration, keys ...string) error
	Exists(ctx context.Context, timeout time.Duration, keys ...string) (int64, error)
	Expire(ctx context.Context, timeout time.Duration, key string, expiration time.Duration) error
	TTL(ctx context.Context, timeout time.Duration, key string) (time.Duration, error)
	Ping(ctx context.Context, timeout time.Duration) error
	Close() error

	// String 类型操作（带超时控制）
	MSet(ctx context.Context, timeout time.Duration, pairs ...interface{}) error
	MGet(ctx context.Context, timeout time.Duration, keys ...string) ([]interface{}, error)
	Incr(ctx context.Context, timeout time.Duration, key string) (int64, error)
	IncrBy(ctx context.Context, timeout time.Duration, key string, value int64) (int64, error)
	Decr(ctx context.Context, timeout time.Duration, key string) (int64, error)
	DecrBy(ctx context.Context, timeout time.Duration, key string, value int64) (int64, error)
	SetNX(ctx context.Context, timeout time.Duration, key string, value interface{}, expiration time.Duration) (bool, error)

	// Hash 类型操作（带超时控制）
	HSet(ctx context.Context, timeout time.Duration, key string, values ...interface{}) error
	HGet(ctx context.Context, timeout time.Duration, key, field string) (string, error)
	HGetAll(ctx context.Context, timeout time.Duration, key string) (map[string]string, error)
	HDel(ctx context.Context, timeout time.Duration, key string, fields ...string) (int64, error)
	HExists(ctx context.Context, timeout time.Duration, key, field string) (bool, error)
	HIncrBy(ctx context.Context, timeout time.Duration, key, field string, incr int64) (int64, error)
	HKeys(ctx context.Context, timeout time.Duration, key string) ([]string, error)
	HLen(ctx context.Context, timeout time.Duration, key string) (int64, error)
	HMGet(ctx context.Context, timeout time.Duration, key string, fields ...string) ([]interface{}, error)
	HMSet(ctx context.Context, timeout time.Duration, key string, values ...interface{}) error

	// List 类型操作（带超时控制）
	LPush(ctx context.Context, timeout time.Duration, key string, values ...interface{}) (int64, error)
	RPush(ctx context.Context, timeout time.Duration, key string, values ...interface{}) (int64, error)
	LPop(ctx context.Context, timeout time.Duration, key string) (string, error)
	RPop(ctx context.Context, timeout time.Duration, key string) (string, error)
	LLen(ctx context.Context, timeout time.Duration, key string) (int64, error)
	LRange(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]string, error)

	// Set 类型操作（带超时控制）
	SAdd(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error)
	SRem(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error)
	SIsMember(ctx context.Context, timeout time.Duration, key string, member interface{}) (bool, error)
	SMembers(ctx context.Context, timeout time.Duration, key string) ([]string, error)
	SCard(ctx context.Context, timeout time.Duration, key string) (int64, error)

	// Sorted Set 类型操作（带超时控制）
	ZAdd(ctx context.Context, timeout time.Duration, key string, members ...redis.Z) (int64, error)
	ZRem(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error)
	ZRange(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]string, error)
	ZRangeWithScores(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]redis.Z, error)
	ZCard(ctx context.Context, timeout time.Duration, key string) (int64, error)
	ZScore(ctx context.Context, timeout time.Duration, key, member string) (float64, error)

	// Pipeline 操作（带超时控制）
	PipelineExec(ctx context.Context, timeout time.Duration, pipeFunc func(pipe redis.Pipeliner) error) ([]redis.Cmder, error)

	// 以下方法在生产环境中不建议使用，仅用于开发和调试
	Scan(ctx context.Context, timeout time.Duration, cursor uint64, match string, count int64) ([]string, uint64, error) // 警告：Scan命令虽然比Keys好，但在大数据量时仍可能影响性能
	Do(ctx context.Context, timeout time.Duration, args ...interface{}) (interface{}, error)                             // 警告：Do命令应谨慎使用
	// Keys(ctx context.Context, timeout time.Duration, pattern string) ([]string, error)  // 警告：Keys命令在生产环境中应避免使用，因为它会阻塞Redis服务器，影响性能
	// FlushDB(ctx context.Context, timeout time.Duration) error  // 警告：清空数据库操作非常危险，可能导致数据丢失
	// FlushAll(ctx context.Context, timeout time.Duration) error  // 警告：清空所有数据库操作非常危险，可能导致数据丢失

	// 文档阿里云自定义命令
	// https://help.aliyun.com/zh/redis/developer-reference/the-tairhash-command
	// Bitmap 操作（带超时控制） 阿里云版本
	SetBit(ctx context.Context, timeout time.Duration, key string, offset int64, value int) error
	SetBits(ctx context.Context, timeout time.Duration, key string, offsets []int64) error
	SetBitWithExpire(ctx context.Context, timeout time.Duration, key string, offset int64, value int, expiration time.Duration) error
	SetBitsWithExpire(ctx context.Context, timeout time.Duration, key string, offsets []int64, expiration time.Duration) error
	GetBit(ctx context.Context, timeout time.Duration, key string, offset int64) (int, error)
	BitCount(ctx context.Context, timeout time.Duration, key string, start, end int64) (int64, error)

	// Bloom Filter 操作（带超时控制）
	BloomAdd(ctx context.Context, timeout time.Duration, key string, value string) error
	BloomAdds(ctx context.Context, timeout time.Duration, key string, values []string) error
	BloomExist(ctx context.Context, timeout time.Duration, key string, value string) (bool, error)
	BloomExists(ctx context.Context, timeout time.Duration, key string, values []string) ([]bool, error)
	// BloomAddIfNotExists 使用Lua脚本实现布隆过滤器的原子操作，包含以下功能：
	// 1. 检查每个元素是否已存在于布隆过滤器中
	// 2. 如果元素不存在，则添加到布隆过滤器
	// 3. 设置键的过期时间
	// 4. 返回每个元素的添加状态
	BloomAddIfNotExists(ctx context.Context, timeout time.Duration, key string, values []string, expiration time.Duration) ([]bool, error)
}

// RedisClient Redis客户端包装器，支持超时控制
type RedisClient struct {
	client *redis.Client
}

// NewRedisClient 创建新的Redis客户端，支持连接池配置
func NewRedisClient(opts ...RedisOption) (Redis, error) {
	// 设置默认配置选项
	options := &RedisOptions{
		PoolSize:     10 * runtime.GOMAXPROCS(0), // 连接池大小，根据实际并发量调整
		MinIdleConns: DefaultMinIdleConns,        // 最小空闲连接数，保持一定数量的空闲连接以应对突发流量
		MaxRetries:   DefaultMaxRetries,          // 最大重试次数，避免无限重试
		DialTimeout:  DefaultDialTimeout,         // 连接超时时间，包括TCP连接和Redis握手时间
		ReadTimeout:  DefaultReadTimeout,         // 读取超时时间，包括命令执行时间
		WriteTimeout: DefaultWriteTimeout,        // 写入超时时间，包括命令发送时间
		PoolTimeout:  DefaultPoolTimeout,         // 连接池超时时间，获取连接的最大等待时间
	}

	// 应用自定义配置选项
	for _, opt := range opts {
		opt(options)
	}

	// 创建Redis客户端，使用配置的连接池参数
	client := redis.NewClient(&redis.Options{
		Dialer:       options.Dialer,
		Addr:         options.Addr,
		Password:     options.Password,
		DB:           options.DB,
		PoolSize:     options.PoolSize,     // 连接池大小
		MinIdleConns: options.MinIdleConns, // 最小空闲连接数
		MaxRetries:   options.MaxRetries,   // 最大重试次数
		DialTimeout:  options.DialTimeout,  // 连接超时时间
		ReadTimeout:  options.ReadTimeout,  // 读取超时时间
		WriteTimeout: options.WriteTimeout, // 写入超时时间
		PoolTimeout:  options.PoolTimeout,  // 连接池超时时间
	})

	// 测试连接是否成功
	if err := client.Ping(context.Background()).Err(); err != nil {
		return nil, err
	}

	return &RedisClient{
		client: client,
	}, nil
}

// Ping 测试Redis连接，支持超时控制
func (r *RedisClient) Ping(ctx context.Context, timeout time.Duration) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Ping(timeoutCtx).Err()
}

// TTL 获取键的剩余生存时间，支持超时控制
func (r *RedisClient) TTL(ctx context.Context, timeout time.Duration, key string) (time.Duration, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.TTL(timeoutCtx, key).Result()
}

// Close 关闭Redis连接
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Get 获取键值，支持超时控制
func (r *RedisClient) Get(ctx context.Context, timeout time.Duration, key string) (string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Get(timeoutCtx, key).Result()
}

// Set 设置键值，支持超时控制
func (r *RedisClient) Set(ctx context.Context, timeout time.Duration, key string, value interface{}, expiration time.Duration) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Set(timeoutCtx, key, value, expiration).Err()
}

// Del 删除键，支持超时控制
func (r *RedisClient) Del(ctx context.Context, timeout time.Duration, keys ...string) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Del(timeoutCtx, keys...).Err()
}

// Exists 检查键是否存在，支持超时控制
func (r *RedisClient) Exists(ctx context.Context, timeout time.Duration, keys ...string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Exists(timeoutCtx, keys...).Result()
}

// HSet 设置哈希表字段值，支持超时控制
func (r *RedisClient) HSet(ctx context.Context, timeout time.Duration, key string, values ...interface{}) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HSet(timeoutCtx, key, values...).Err()
}

// HGet 获取哈希表字段值，支持超时控制
func (r *RedisClient) HGet(ctx context.Context, timeout time.Duration, key, field string) (string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HGet(timeoutCtx, key, field).Result()
}

// HGetAll 获取哈希表所有字段和值，支持超时控制
func (r *RedisClient) HGetAll(ctx context.Context, timeout time.Duration, key string) (map[string]string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HGetAll(timeoutCtx, key).Result()
}

// Expire 设置键的过期时间，支持超时控制
func (r *RedisClient) Expire(ctx context.Context, timeout time.Duration, key string, expiration time.Duration) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Expire(timeoutCtx, key, expiration).Err()
}

// SetNX 设置键值，仅当键不存在时，支持超时控制
func (r *RedisClient) SetNX(ctx context.Context, timeout time.Duration, key string, value interface{}, expiration time.Duration) (bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SetNX(timeoutCtx, key, value, expiration).Result()
}

// Incr 将键的值增加1，支持超时控制
func (r *RedisClient) Incr(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Incr(timeoutCtx, key).Result()
}

// Do 执行自定义命令，支持超时控制
func (r *RedisClient) Do(ctx context.Context, timeout time.Duration, args ...interface{}) (interface{}, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Do(timeoutCtx, args...).Result()
}

// String 类型操作
// MSet 批量设置多个键值对，支持超时控制
func (r *RedisClient) MSet(ctx context.Context, timeout time.Duration, pairs ...interface{}) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.MSet(timeoutCtx, pairs...).Err()
}

// MGet 批量获取多个键的值，支持超时控制
func (r *RedisClient) MGet(ctx context.Context, timeout time.Duration, keys ...string) ([]interface{}, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.MGet(timeoutCtx, keys...).Result()
}

// IncrBy 将键的值增加指定的整数，支持超时控制
func (r *RedisClient) IncrBy(ctx context.Context, timeout time.Duration, key string, value int64) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.IncrBy(timeoutCtx, key, value).Result()
}

// Decr 将键的值减1，支持超时控制
func (r *RedisClient) Decr(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Decr(timeoutCtx, key).Result()
}

// DecrBy 将键的值减少指定的整数，支持超时控制
func (r *RedisClient) DecrBy(ctx context.Context, timeout time.Duration, key string, value int64) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.DecrBy(timeoutCtx, key, value).Result()
}

// Hash 类型操作
// HDel 删除哈希表中的一个或多个字段，支持超时控制
func (r *RedisClient) HDel(ctx context.Context, timeout time.Duration, key string, fields ...string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HDel(timeoutCtx, key, fields...).Result()
}

// HExists 查看哈希表是否存在指定的字段，支持超时控制
func (r *RedisClient) HExists(ctx context.Context, timeout time.Duration, key, field string) (bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HExists(timeoutCtx, key, field).Result()
}

// HIncrBy 为哈希表中的字段值加上指定的增量值，支持超时控制
func (r *RedisClient) HIncrBy(ctx context.Context, timeout time.Duration, key, field string, incr int64) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HIncrBy(timeoutCtx, key, field, incr).Result()
}

// HKeys 获取哈希表中的所有字段，支持超时控制
func (r *RedisClient) HKeys(ctx context.Context, timeout time.Duration, key string) ([]string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HKeys(timeoutCtx, key).Result()
}

// HLen 获取哈希表中字段的数量，支持超时控制
func (r *RedisClient) HLen(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HLen(timeoutCtx, key).Result()
}

// HMGet 获取哈希表中多个字段的值，支持超时控制
func (r *RedisClient) HMGet(ctx context.Context, timeout time.Duration, key string, fields ...string) ([]interface{}, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HMGet(timeoutCtx, key, fields...).Result()
}

// HMSet 同时将多个 field-value 对设置到哈希表中，支持超时控制
func (r *RedisClient) HMSet(ctx context.Context, timeout time.Duration, key string, values ...interface{}) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.HMSet(timeoutCtx, key, values...).Err()
}

// List 类型操作
// LPush 将一个或多个值插入到列表头部，支持超时控制
func (r *RedisClient) LPush(ctx context.Context, timeout time.Duration, key string, values ...interface{}) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.LPush(timeoutCtx, key, values...).Result()
}

// RPush 将一个或多个值插入到列表尾部，支持超时控制
func (r *RedisClient) RPush(ctx context.Context, timeout time.Duration, key string, values ...interface{}) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.RPush(timeoutCtx, key, values...).Result()
}

// LPop 移除并返回列表的第一个元素，支持超时控制
func (r *RedisClient) LPop(ctx context.Context, timeout time.Duration, key string) (string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.LPop(timeoutCtx, key).Result()
}

// RPop 移除并返回列表的最后一个元素，支持超时控制
func (r *RedisClient) RPop(ctx context.Context, timeout time.Duration, key string) (string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.RPop(timeoutCtx, key).Result()
}

// LLen 获取列表长度，支持超时控制
func (r *RedisClient) LLen(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.LLen(timeoutCtx, key).Result()
}

// LRange 获取列表指定范围内的元素，支持超时控制
func (r *RedisClient) LRange(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.LRange(timeoutCtx, key, start, stop).Result()
}

// Set 类型操作
// SAdd 将一个或多个成员元素加入到集合中，支持超时控制
func (r *RedisClient) SAdd(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SAdd(timeoutCtx, key, members...).Result()
}

// SRem 移除集合中一个或多个成员，支持超时控制
func (r *RedisClient) SRem(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SRem(timeoutCtx, key, members...).Result()
}

// SIsMember 判断成员元素是否是集合的成员，支持超时控制
func (r *RedisClient) SIsMember(ctx context.Context, timeout time.Duration, key string, member interface{}) (bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SIsMember(timeoutCtx, key, member).Result()
}

// SMembers 返回集合中的所有成员，支持超时控制
func (r *RedisClient) SMembers(ctx context.Context, timeout time.Duration, key string) ([]string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SMembers(timeoutCtx, key).Result()
}

// SCard 获取集合的成员数，支持超时控制
func (r *RedisClient) SCard(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.SCard(timeoutCtx, key).Result()
}

// Sorted Set 类型操作
// ZAdd 将一个或多个成员元素及其分数值加入到有序集当中，支持超时控制
func (r *RedisClient) ZAdd(ctx context.Context, timeout time.Duration, key string, members ...redis.Z) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZAdd(timeoutCtx, key, members...).Result()
}

// ZRem 移除有序集中的一个或多个成员，支持超时控制
func (r *RedisClient) ZRem(ctx context.Context, timeout time.Duration, key string, members ...interface{}) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZRem(timeoutCtx, key, members...).Result()
}

// ZRange 返回有序集中指定区间内的成员，支持超时控制
func (r *RedisClient) ZRange(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]string, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZRange(timeoutCtx, key, start, stop).Result()
}

// ZRangeWithScores 返回有序集中指定区间内的成员和分数，支持超时控制
func (r *RedisClient) ZRangeWithScores(ctx context.Context, timeout time.Duration, key string, start, stop int64) ([]redis.Z, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZRangeWithScores(timeoutCtx, key, start, stop).Result()
}

// ZCard 获取有序集合的成员数，支持超时控制
func (r *RedisClient) ZCard(ctx context.Context, timeout time.Duration, key string) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZCard(timeoutCtx, key).Result()
}

// ZScore 返回有序集中成员的分数值，支持超时控制
func (r *RedisClient) ZScore(ctx context.Context, timeout time.Duration, key, member string) (float64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.ZScore(timeoutCtx, key, member).Result()
}

// PipelineExec 执行 Pipeline 操作，支持超时控制
func (r *RedisClient) PipelineExec(ctx context.Context, timeout time.Duration, pipeFunc func(pipe redis.Pipeliner) error) ([]redis.Cmder, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	pipe := r.client.Pipeline()
	if err := pipeFunc(pipe); err != nil {
		return nil, err
	}
	return pipe.Exec(timeoutCtx)
}

// Scan 扫描键，支持超时控制
func (r *RedisClient) Scan(ctx context.Context, timeout time.Duration, cursor uint64, match string, count int64) ([]string, uint64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	return r.client.Scan(timeoutCtx, cursor, match, count).Result()
}

// SetBit 设置位图中的位
func (r *RedisClient) SetBit(ctx context.Context, timeout time.Duration, key string, offset int64, value int) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 使用阿里云的 TR.SETBIT 命令
	_, err := r.client.Do(timeoutCtx, "TR.SETBIT", key, offset, value).Result()
	return err
}

// SetBits 批量设置位图中的多个位为1
// 参数说明：
// - ctx: 上下文，用于控制超时和取消
// - timeout: 操作超时时间
// - key: 位图的键名
// - offsets: 要设置的位的偏移量数组
//
// 返回值：
// - error: 错误信息，包括参数验证错误和Redis操作错误
//
// 使用示例：
// offsets := []int64{1, 2, 3, 4, 5}
// err := redisClient.SetBits(ctx, timeout, "mybitmap", offsets)
func (r *RedisClient) SetBits(ctx context.Context, timeout time.Duration, key string, offsets []int64) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 参数验证
	if len(offsets) == 0 {
		return fmt.Errorf("offsets array cannot be empty")
	}

	// 构建命令参数
	args := make([]interface{}, len(offsets)+2)
	args[0] = "TR.SETBITS"
	args[1] = key
	for i, offset := range offsets {
		args[i+2] = offset
	}

	// 使用阿里云的 TR.SETBITS 命令
	_, err := r.client.Do(timeoutCtx, args...).Result()
	return err
}

// SetBitWithExpire 原子地设置位图中的位并设置过期时间
func (r *RedisClient) SetBitWithExpire(ctx context.Context, timeout time.Duration, key string, offset int64, value int, expiration time.Duration) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 参数验证
	if value != 0 && value != 1 {
		return fmt.Errorf("value must be 0 or 1")
	}

	// 构建Lua脚本
	script := `
		redis.call('TR.SETBIT', KEYS[1], ARGV[1], ARGV[2])
		redis.call('EXPIRE', KEYS[1], ARGV[3])
		return true
	`

	// 构建参数
	args := []interface{}{
		offset,
		value,
		int(expiration.Seconds()),
	}

	_, err := r.client.Eval(timeoutCtx, script, []string{key}, args...).Result()
	return err
}

// SetBitsWithExpire 原子地批量设置位图中的多个位为1并设置过期时间
// 参数说明：
// - ctx: 上下文，用于控制超时和取消
// - timeout: 操作超时时间
// - key: 位图的键名
// - offsets: 要设置的位的偏移量数组
// - expiration: 键的过期时间
//
// 返回值：
// - error: 错误信息，包括参数验证错误和Redis操作错误
//
// 使用示例：
// offsets := []int64{1, 2, 3, 4, 5}
// err := redisClient.SetBitsWithExpire(ctx, timeout, "mybitmap", offsets, 24*time.Hour)
func (r *RedisClient) SetBitsWithExpire(ctx context.Context, timeout time.Duration, key string, offsets []int64, expiration time.Duration) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 参数验证
	if len(offsets) == 0 {
		return fmt.Errorf("offsets array cannot be empty")
	}

	// 构建Lua脚本
	script := `
		local offsets = {}
		for i = 1, #ARGV - 1 do
			table.insert(offsets, tonumber(ARGV[i]))
		end
		
		-- 设置所有位
		for _, offset in ipairs(offsets) do
			redis.call('TR.SETBIT', KEYS[1], offset, 1)
		end
		
		-- 设置过期时间
		redis.call('EXPIRE', KEYS[1], ARGV[#ARGV])
		return true
	`

	// 构建参数
	args := make([]interface{}, len(offsets)+1)
	for i, offset := range offsets {
		args[i] = offset
	}
	args[len(offsets)] = int(expiration.Seconds())

	// 执行Lua脚本
	_, err := r.client.Eval(timeoutCtx, script, []string{key}, args...).Result()
	return err
}

// GetBit 获取位图中的位
func (r *RedisClient) GetBit(ctx context.Context, timeout time.Duration, key string, offset int64) (int, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 使用阿里云的 TR.GETBIT 命令
	result, err := r.client.Do(timeoutCtx, "TR.GETBIT", key, offset).Result()
	if err != nil {
		return 0, err
	}

	// 转换结果为 int
	ret, ok := result.(int64)
	if !ok {
		return 0, fmt.Errorf("unexpected result type: %T", result)
	}
	return int(ret), nil
}

// BitCount 统计位图中值为1的位的数量
func (r *RedisClient) BitCount(ctx context.Context, timeout time.Duration, key string, start, end int64) (int64, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 使用阿里云的 TR.BITCOUNT 命令
	// 注意：end 不支持 -1，需要传入具体的结束位置
	if end < 0 {
		end = 0x7fffffff // 使用一个足够大的数作为结束位置
	}
	result, err := r.client.Do(timeoutCtx, "TR.BITCOUNT", key, start, end).Result()
	if err != nil {
		return 0, err
	}

	ret, ok := result.(int64)
	if !ok {
		return 0, fmt.Errorf("unexpected result type: %T", result)
	}
	return ret, nil
}

// BloomAdd 添加元素到布隆过滤器
func (r *RedisClient) BloomAdd(ctx context.Context, timeout time.Duration, key string, value string) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	_, err := r.client.Do(timeoutCtx, "BF.ADD", key, value).Result()
	return err
}

// BloomAdds 批量添加元素到布隆过滤器
func (r *RedisClient) BloomAdds(ctx context.Context, timeout time.Duration, key string, values []string) error {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	args := make([]interface{}, len(values)+2)
	args[0] = "BF.MADD"
	args[1] = key
	for i, value := range values {
		args[i+2] = value
	}
	_, err := r.client.Do(timeoutCtx, args...).Result()
	return err
}

// BloomExist 检查元素是否在布隆过滤器中
func (r *RedisClient) BloomExist(ctx context.Context, timeout time.Duration, key string, value string) (bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	result, err := r.client.Do(timeoutCtx, "BF.EXISTS", key, value).Result()
	if err != nil {
		return false, err
	}

	ret, ok := result.(int64)
	if !ok {
		return false, nil
	}
	return ret == 1, nil
}

// BloomExists 批量检查元素是否在布隆过滤器中
func (r *RedisClient) BloomExists(ctx context.Context, timeout time.Duration, key string, values []string) ([]bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	args := make([]interface{}, len(values)+2)
	args[0] = "BF.MEXISTS"
	args[1] = key
	for i, value := range values {
		args[i+2] = value
	}

	result, err := r.client.Do(timeoutCtx, args...).Result()
	if err != nil {
		return nil, err
	}

	// 转换结果
	results := make([]bool, len(values))
	for i, v := range result.([]interface{}) {
		ret, _ := v.(int64)
		results[i] = ret == 1
	}

	return results, nil
}

// BloomAddIfNotExists 使用Lua脚本实现布隆过滤器的原子操作，包含以下功能：
// 1. 检查每个元素是否已存在于布隆过滤器中
// 2. 如果元素不存在，则添加到布隆过滤器
// 3. 设置键的过期时间
// 4. 返回每个元素的添加状态
//
// 参数说明：
// - ctx: 上下文，用于控制超时和取消
// - timeout: 操作超时时间
// - key: 布隆过滤器的键名
// - values: 要添加的元素数组
// - expiration: 键的过期时间
//
// 返回值：
// - []bool: 表示每个元素是否已存在（true表示已存在，false表示不存在且已添加）
// - error: 错误信息，包括参数验证错误和Redis操作错误
//
// 使用示例：
// values := []string{"value1", "value2", "value3"}
// exists, err := redisClient.BloomAddIfNotExists(ctx, timeout, "mybloom", values, 24*time.Hour)
func (r *RedisClient) BloomAddIfNotExists(ctx context.Context, timeout time.Duration, key string, values []string, expiration time.Duration) ([]bool, error) {
	var timeoutCtx context.Context
	var cancel context.CancelFunc
	if timeout > 0 {
		timeoutCtx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	} else {
		timeoutCtx = ctx
	}

	// 构建Lua脚本
	script := `
		local results = {}
		local count = tonumber(ARGV[1])
		local expiration = tonumber(ARGV[2])
		
		for i = 1, count do
			local value = ARGV[i + 2]
			local exists = redis.call('BF.EXISTS', KEYS[1], value)
			if exists == 0 then
				redis.call('BF.ADD', KEYS[1], value)
			end
			table.insert(results, exists)
		end
		
		redis.call('EXPIRE', KEYS[1], expiration)
		return results
	`

	// 构建参数
	args := make([]interface{}, len(values)+2)
	args[0] = len(values)
	args[1] = int(expiration.Seconds())
	for i, value := range values {
		args[i+2] = value
	}

	result, err := r.client.Eval(timeoutCtx, script, []string{key}, args...).Result()
	if err != nil {
		return nil, err
	}

	// 转换结果
	results := make([]bool, len(values))
	for i, v := range result.([]interface{}) {
		ret, _ := v.(int64)
		results[i] = ret == 1
	}

	return results, nil
}

// ///////////////////////////RedisOptions/////////////////////////////
// RedisOptions Redis客户端配置选项
type RedisOptions struct {
	Addr         string        // Redis服务器地址
	Password     string        // Redis密码
	DB           int           // Redis数据库编号
	PoolSize     int           // 连接池大小
	MinIdleConns int           // 最小空闲连接数
	MaxRetries   int           // 最大重试次数
	DialTimeout  time.Duration // 连接超时时间
	ReadTimeout  time.Duration // 读取超时时间
	WriteTimeout time.Duration // 写入超时时间
	PoolTimeout  time.Duration // 连接池超时时间

	Dialer func(ctx context.Context, network, addr string) (net.Conn, error) // 自定义连接器
}

// RedisOption Redis配置选项函数
type RedisOption func(*RedisOptions)

// WithAddr 设置Redis服务器地址
func WithAddr(addr string) RedisOption {
	return func(o *RedisOptions) {
		o.Addr = addr
	}
}

// WithPassword 设置Redis密码
func WithPassword(password string) RedisOption {
	return func(o *RedisOptions) {
		o.Password = password
	}
}

// WithDB 设置Redis数据库编号
func WithDB(db int) RedisOption {
	return func(o *RedisOptions) {
		o.DB = db
	}
}

// WithPoolSize 设置连接池大小
func WithPoolSize(size int) RedisOption {
	return func(o *RedisOptions) {
		o.PoolSize = size
	}
}

// WithMinIdleConns 设置最小空闲连接数
func WithMinIdleConns(n int) RedisOption {
	return func(o *RedisOptions) {
		o.MinIdleConns = n
	}
}

// WithMaxRetries 设置最大重试次数
func WithMaxRetries(n int) RedisOption {
	return func(o *RedisOptions) {
		o.MaxRetries = n
	}
}

// WithDialTimeout 设置连接超时时间
func WithDialTimeout(timeout time.Duration) RedisOption {
	return func(o *RedisOptions) {
		o.DialTimeout = timeout
	}
}

// WithReadTimeout 设置读取超时时间
func WithReadTimeout(timeout time.Duration) RedisOption {
	return func(o *RedisOptions) {
		o.ReadTimeout = timeout
	}
}

// WithWriteTimeout 设置写入超时时间
func WithWriteTimeout(timeout time.Duration) RedisOption {
	return func(o *RedisOptions) {
		o.WriteTimeout = timeout
	}
}

// WithPoolTimeout 设置连接池超时时间
func WithPoolTimeout(timeout time.Duration) RedisOption {
	return func(o *RedisOptions) {
		o.PoolTimeout = timeout
	}
}

// WithDialer 设置自定义连接器
func WithDialer(dialer func(ctx context.Context, network, addr string) (net.Conn, error)) RedisOption {
	return func(o *RedisOptions) {
		o.Dialer = dialer
	}
}
