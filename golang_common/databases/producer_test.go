package databases

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
)

var (
	producer     KafkaProducer
	producerOnce sync.Once
	ctx          = context.Background()
)

const (
	KafkaUserName = "alikafka_serverless-cn-fhh3zns0s04"
	KafkaPassword = "pB4bjjIuVxNFZU5B5ZeoJHy7YPo7kCX9"
	TestTopic     = "100001"
)

// GetProducer 获取Producer单例实例
func GetProducer(ctx context.Context) KafkaProducer {
	producerOnce.Do(func() {
		var err error
		producer, err = NewProducer(ctx, &ProducerConfig{
			Basic: KafkaBasicConfig{
				Topic:            TestTopic,
				BootstrapServers: "alikafka-serverless-cn-fhh3zns0s04-1000.alikafka.aliyuncs.com:9093,alikafka-serverless-cn-fhh3zns0s04-2000.alikafka.aliyuncs.com:9093,alikafka-serverless-cn-fhh3zns0s04-3000.alikafka.aliyuncs.com:9093",
			},
			// Security: &KafkaSecurityConfig{
			// 	SecurityProtocol: "sasl_ssl",
			// 	SaslUsername:     KafkaUserName,
			// 	SaslPassword:     KafkaPassword,
			// 	SslCaLocation:    "/Users/<USER>/Documents/tools/only-4096-ca-cert",
			// },
			// Advanced: &KafkaAdvancedConfig{
			// 	RequiredAcks: 0,
			// 	ErrorHandler: func(batch []*Message, err error) {
			// 		// 直接打印每条消息的内容
			// 		fmt.Printf("生产者错误: %v\n消息内容:\n", err)
			// 		for i, msg := range batch {
			// 			fmt.Printf("[%d] %s\n", i, msg.String())
			// 		}
			// 	},
			// },
		})
		if err != nil {
			fmt.Printf("NewProducer err:%v\n", err)
		}
	})

	return producer
}

// TestProduceBatch 测试批量发送消息
func TestProduceBatch(t *testing.T) {
	cancelCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	producer := GetProducer(cancelCtx)
	if producer == nil {
		t.Skip("跳过测试：无法创建Producer")
		return
	}
	defer producer.Close()

	timestamp := time.Now().String()
	msgs := []*Message{
		{
			Key:   []byte("batch_key_1"),
			Value: []byte("batch_value_1_" + timestamp),
		},
		{
			Key:   []byte("batch_key_2"),
			Value: []byte("batch_value_2_" + timestamp),
		},
		{
			Key:   []byte("batch_key_3"),
			Value: []byte("batch_value_3_" + timestamp),
		},
	}

	err := producer.Produce(cancelCtx, msgs)
	if err != nil {
		fmt.Printf("ProduceBatch失败: %v\n", err)
		return
	}
	time.Sleep(2 * time.Second)
}

func TestProduceAndConsume(t *testing.T) {

	// 创建消费者上下文，15秒后取消
	consumerCtx, consumerCancel := context.WithTimeout(ctx, 12*time.Second)
	defer consumerCancel()

	// 创建一个唯一的测试ID，用于标识本次测试的消息
	testID := fmt.Sprintf("test_%d", time.Now().Unix())
	fmt.Printf("测试ID: %s\n", testID)

	// 创建消费者
	consumer := GetConsumer(ctx)
	if consumer == nil {
		fmt.Printf("创建消费者失败\n")
		return
	}
	defer consumer.Close()

	// 用于统计接收到的消息数量
	var receivedCount int
	var receivedCountMu sync.Mutex

	// 用于等待消费者完成
	var wg sync.WaitGroup
	wg.Add(1)

	// 启动消费者协程
	go func() {
		defer wg.Done()

		err := consumer.Consume(consumerCtx, func(msg *kafka.Message) error {
			receivedCountMu.Lock()
			receivedCount++
			currentCount := receivedCount
			receivedCountMu.Unlock()

			if currentCount%10 == 0 {
				fmt.Printf("已消费 %d 条消息\n", currentCount)
			}

			return nil
		})

		if err != nil && err != context.DeadlineExceeded && err != context.Canceled {
			fmt.Printf("消费者错误: %v\n", err)
		}

		fmt.Printf("消费者退出，共消费 %d 条消息\n", receivedCount)

	}()

	// 创建生产者上下文，10秒后取消
	producerCtx, producerCancel := context.WithTimeout(ctx, 10*time.Second)
	defer producerCancel()
	// 创建生产者
	producer := GetProducer(producerCtx)
	if producer == nil {
		t.Skip("跳过测试：无法创建Producer")
		return
	}
	defer producer.Close()

	wg.Add(1)
	go func() {
		defer wg.Done()
		// 生产消息，每100ms发送一条，直到上下文取消
		msgCount := 0
		msgSuccessCount := 0
		ticker := time.NewTicker(100 * time.Millisecond)
		defer ticker.Stop()

		for {
			select {
			case <-producerCtx.Done():
				ticker.Stop()
				return

			case <-ticker.C:

				msgCount++
				msg := &Message{
					Key:   []byte(fmt.Sprintf("%d", msgCount)),
					Value: []byte(fmt.Sprintf("value%d", msgCount)),
				}

				// 使用单条消息发送API
				err := producer.Produce(producerCtx, []*Message{msg})
				if err != nil {
					// 如果是上下文取消导致的错误，不视为测试失败
					if err == context.DeadlineExceeded || err == context.Canceled {
						ticker.Stop()
						fmt.Printf("生产者停止发送: %v\n", err)
						break
					}
					fmt.Printf("发送消息失败: %v\n", err)
				} else {
					msgSuccessCount++
				}

				if msgCount%10 == 0 {
					fmt.Printf("已发送 %d 条消息, 成功 %d 条\n", msgCount, msgSuccessCount)
				}
			}
		}
	}()

	// time.Sleep(5 * time.Second)
	// producer.Close()
	// consumer.Close()

	wg.Wait()

}
