package logger

import (
	"fmt"
	stdlog "log"
)

// StdLogAdapter 标准库log适配器
type StdLogAdapter struct{}

func (a *StdLogAdapter) Write(p []byte) (n int, err error) {
	sugar.Info(string(p))
	return len(p), nil
}

// ReplaceStdLog 替换标准库log输出
func ReplaceStdLog() {
	adapter := &StdLogAdapter{}
	stdlog.SetOutput(adapter)
	stdlog.SetFlags(0) // 清除标准库日志前缀
	stdlog.SetPrefix("")
}

// StdLogCompatible 提供与标准库log兼容的API
type StdLogCompatible struct{}

// Printf 输出格式化信息
func (*StdLogCompatible) Printf(format string, v ...interface{}) {
	sugar.Infof(format, v...)
}

// Println 输出行信息
func (*StdLogCompatible) Println(v ...interface{}) {
	sugar.Info(fmt.Sprintln(v...))
}

// Print 输出基本信息
func (*StdLogCompatible) Print(v ...interface{}) {
	sugar.Info(fmt.Sprint(v...))
}

// Fatal 输出错误并退出
func (*StdLogCompatible) Fatal(v ...interface{}) {
	sugar.Fatal(fmt.Sprint(v...))
}

// Fatalf 输出格式化错误并退出
func (*StdLogCompatible) Fatalf(format string, v ...interface{}) {
	sugar.Fatalf(format, v...)
}

// Fatalln 输出行错误并退出
func (*StdLogCompatible) Fatalln(v ...interface{}) {
	sugar.Fatal(fmt.Sprintln(v...))
}

// 提供给外部使用的兼容标准库的日志接口
var StdLog = &StdLogCompatible{}
