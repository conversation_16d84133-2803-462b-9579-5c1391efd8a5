# 高性能日志系统（基于Zap）

本日志系统基于业界领先的高性能日志库[<PERSON><PERSON>'s zap](https://github.com/uber-go/zap)和[lumberjack](https://github.com/natefinch/lumberjack)，提供了企业级的日志功能，特别优化了容器环境的使用场景。

## 性能优势

Zap相比标准库和其他日志框架具有显著性能优势：
- 内存分配减少40-45倍
- CPU使用率降低4-10倍
- 高度优化的JSON序列化和结构化日志

## 特性

- **高性能**：通过减少内存分配和CPU使用实现高吞吐量日志
- **结构化日志**：支持JSON格式输出，便于日志收集和分析
- **自动文件轮转**：基于lumberjack实现基于大小的日志轮转
- **文件自动清理**：按日期和数量自动管理日志文件
- **日志级别控制**：灵活的日志级别过滤（debug, info, warn, error, fatal等）
- **集成标准库**：完全兼容Go标准库log包的接口
- **调用者信息记录**：自动记录日志调用的文件和行号
- **字段支持**：支持结构化字段，便于过滤和搜索
- **环境变量配置**：通过环境变量灵活配置日志行为

## 使用方法

### 1. 基本用法

```go
import "rta_core/utils/logger"

func main() {
    // 初始化日志（也可以通过环境变量自动初始化）
    logger.InitLoggerFromEnv()
    
    // 使用全局函数记录日志
    logger.Debug("调试信息: %v", someVar)
    logger.Info("信息: %v", someVar)
    logger.Warn("警告: %v", someVar)
    logger.Error("错误: %v", someVar)
    logger.Fatal("致命错误: %v", someVar) // 会导致程序退出
    
    // 程序退出前同步日志
    defer logger.Sync()
}
```

### 2. 使用结构化字段

```go
// 创建带有固定字段的日志记录器
fields := map[string]interface{}{
    "userID": 123456,
    "module": "payment",
    "traceID": "abc-123-xyz",
}
log := logger.WithFields(fields)

// 使用该记录器记录日志
log.Infof("处理请求完成")
log.Warnf("遇到性能问题")
```

### 3. 直接使用Zap

```go
// 获取原生zap日志器
zapLogger := logger.GetLogger()
zapLogger.Info("这是一条信息",
    zap.Int("statusCode", 200),
    zap.String("path", "/api/users"),
    zap.Duration("latency", time.Millisecond*153),
)

// 获取Sugar版本的日志器（更易用但略低性能）
sugar := logger.GetSugaredLogger()
sugar.Infow("请求完成",
    "statusCode", 200,
    "path", "/api/users",
    "latency", time.Millisecond*153,
)
```

### 4. 与标准库log兼容

```go
import (
    "log"
    "rta_core/utils/logger"
)

func main() {
    // 替换标准库日志实现
    logger.ReplaceStdLog()
    
    // 现在标准库log输出会通过zap处理
    log.Println("通过标准库接口输出")
    log.Printf("格式化输出: %v", someVar)
    
    // 或使用兼容接口
    stdLogger := logger.StdLog
    stdLogger.Println("Hello world")
}
```

## 环境变量配置

| 环境变量 | 说明 | 默认值 |
|----------|------|--------|
| LOG_LEVEL | 日志级别 (debug/info/warn/error/dpanic/panic/fatal) | info |
| LOG_DIR | 日志文件目录 | logs |
| LOG_FILENAME | 日志文件名 | app.log |
| LOG_MAX_SIZE | 单个日志文件最大大小（MB） | 100 |
| LOG_MAX_AGE | 日志文件保留天数 | 7 |
| LOG_MAX_BACKUPS | 最大保留文件数量 | 10 |
| LOG_STDOUT | 是否同时输出到标准输出 | true |
| LOG_REPORT_CALLER | 是否记录调用者信息 | true |
| LOG_JSON_FORMAT | 是否使用JSON格式输出 | false |

## 性能调优建议

1. **适当设置日志级别**：生产环境建议使用INFO或WARN级别
2. **避免过度日志**：不要在热点代码路径中记录过多DEBUG信息
3. **使用原生zap.Logger**：对于高性能需求，直接使用`GetLogger()`获取的原生zap.Logger
4. **批量记录字段**：使用WithFields预先设置重复字段，避免每次日志都创建
5. **减少字符串拼接**：使用结构化字段代替字符串拼接

## 示例

```go
package main

import (
    "rta_core/utils/logger"
    "time"
    
    "go.uber.org/zap"
)

func main() {
    // 初始化日志系统（从环境变量）
    logger.InitLoggerFromEnv()
    defer logger.Sync()
    
    // 请求处理示例
    processRequest("/api/users", "GET", 123)
}

func processRequest(path, method string, userID int) {
    // 记录请求开始
    startTime := time.Now()
    logger.Info("开始处理请求: %s %s", method, path)
    
    // 使用结构化日志
    reqLogger := logger.WithFields(map[string]interface{}{
        "path": path,
        "method": method,
        "userID": userID,
    })
    
    // 业务处理...
    time.Sleep(time.Millisecond * 100)
    
    // 记录请求完成
    latency := time.Since(startTime)
    reqLogger.Infof("请求处理完成，耗时: %v", latency)
} 