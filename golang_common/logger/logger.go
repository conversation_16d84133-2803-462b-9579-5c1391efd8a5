package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	// 全局Logger实例
	logger *zap.Logger
	// SugaredLogger提供了更易用的API
	sugar *zap.SugaredLogger
)

// Config 日志配置
type Config struct {
	Level        string // 日志级别: debug, info, warn, error, dpanic, panic, fatal
	FileDir      string // 日志文件目录
	Filename     string // 日志文件名
	MaxSize      int    // 单个日志文件最大大小（MB）
	MaxAge       int    // 日志文件保留天数
	MaxBackups   int    // 最大保留文件数
	Stdout       bool   // 是否同时输出到标准输出
	ReportCaller bool   // 是否记录调用者信息
	JSONFormat   bool   // 是否使用JSON格式
}

func (c Config) IsZero() bool {
	return c.Filename == "" && c.FileDir == ""
}

// 默认配置
var defaultConfig = Config{
	Level:        "info",
	FileDir:      "logs",
	Filename:     "logger.log",
	MaxSize:      100, // 100MB
	MaxAge:       4,   // 4天
	MaxBackups:   5,   // 最多5个备份
	Stdout:       true,
	ReportCaller: true,
	JSONFormat:   false,
}

// getLogLevel 将字符串转换为zapcore.Level
func getLogLevel(level string) zapcore.Level {
	switch strings.ToLower(level) {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "dpanic":
		return zapcore.DPanicLevel
	case "panic":
		return zapcore.PanicLevel
	case "fatal":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// InitLogger 初始化日志
func InitLogger(config Config) {
	// 如果配置为空，使用默认配置
	if config.IsZero() {
		config = defaultConfig
	}

	// 确保日志目录存在
	if err := os.MkdirAll(config.FileDir, 0755); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create log directory: %v\n", err)
	}

	// 日志轮转配置
	logPath := filepath.Join(config.FileDir, config.Filename)
	lumberjackLogger := &lumberjack.Logger{
		Filename:   logPath,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   true,
	}

	// 时间编码器
	timeEncoder := func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format("2006-01-02 15:04:05.999"))
	}

	// 日志格式配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     timeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 配置编码器
	var encoder zapcore.Encoder
	if config.JSONFormat {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 配置日志级别
	level := getLogLevel(config.Level)
	atomicLevel := zap.NewAtomicLevelAt(level)

	// 配置输出
	var writeSyncer zapcore.WriteSyncer
	if config.Stdout {
		writeSyncer = zapcore.NewMultiWriteSyncer(
			zapcore.AddSync(lumberjackLogger),
			zapcore.AddSync(os.Stdout),
		)
	} else {
		writeSyncer = zapcore.AddSync(lumberjackLogger)
	}

	// 创建日志核心
	core := zapcore.NewCore(encoder, writeSyncer, atomicLevel)

	// 创建Logger
	var zapLogger *zap.Logger
	if config.ReportCaller {
		zapLogger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	} else {
		zapLogger = zap.New(core)
	}

	// 设置全局变量
	logger = zapLogger
	sugar = zapLogger.Sugar()

	// 替换zap的全局logger
	zap.ReplaceGlobals(zapLogger)
}

// Debug 输出调试级别日志
func Debug(format string, args ...interface{}) {
	sugar.Debugf(format, args...)
}

// Info 输出信息级别日志
func Info(format string, args ...interface{}) {
	sugar.Infof(format, args...)
}

// Warn 输出警告级别日志
func Warn(format string, args ...interface{}) {
	sugar.Warnf(format, args...)
}

// Error 输出错误级别日志
func Error(format string, args ...interface{}) {
	sugar.Errorf(format, args...)
}

// Fatal 输出致命级别日志并退出程序
func Fatal(format string, args ...interface{}) {
	sugar.Fatalf(format, args...)
}

// Sync 同步日志缓冲区数据到磁盘
func Sync() error {
	return logger.Sync()
}

// Close 关闭日志系统
func Close() error {
	return Sync()
}

// GetLogger 获取zap.Logger实例
func GetLogger() *zap.Logger {
	return logger
}

// GetSugaredLogger 获取zap.SugaredLogger实例
func GetSugaredLogger() *zap.SugaredLogger {
	return sugar
}

// WithFields 创建带有字段的Logger
func WithFields(fields map[string]interface{}) *zap.SugaredLogger {
	zapFields := make([]zap.Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, zap.Any(k, v))
	}
	return logger.With(zapFields...).Sugar()
}
