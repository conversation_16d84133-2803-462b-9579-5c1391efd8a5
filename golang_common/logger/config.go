package logger

import (
	"os"
	"strconv"
	"strings"
)

// InitLoggerFromEnv 从环境变量初始化日志配置
func InitLoggerFromEnv() {
	config := Config{
		Level:        "info",
		FileDir:      "logs",
		Filename:     "logger.log",
		MaxSize:      100,
		MaxAge:       4,
		MaxBackups:   5,
		Stdout:       true,
		ReportCaller: true,
		JSONFormat:   false,
	}

	// 日志级别
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Level = strings.ToLower(level)
	}

	// 日志目录
	if dir := os.Getenv("LOG_DIR"); dir != "" {
		config.FileDir = dir
	}

	// 日志文件名
	if filename := os.Getenv("LOG_FILENAME"); filename != "" {
		config.Filename = filename
	}

	// 日志文件大小限制（MB）
	if maxSize := os.Getenv("LOG_MAX_SIZE"); maxSize != "" {
		if size, err := strconv.Atoi(maxSize); err == nil && size > 0 {
			config.MaxSize = size
		}
	}

	// 日志保留天数
	if maxAge := os.Getenv("LOG_MAX_AGE"); maxAge != "" {
		if age, err := strconv.Atoi(maxAge); err == nil && age > 0 {
			config.MaxAge = age
		}
	}

	// 最大备份数量
	if maxBackups := os.Getenv("LOG_MAX_BACKUPS"); maxBackups != "" {
		if backups, err := strconv.Atoi(maxBackups); err == nil && backups > 0 {
			config.MaxBackups = backups
		}
	}

	// 是否输出到标准输出
	if stdout := os.Getenv("LOG_STDOUT"); stdout != "" {
		config.Stdout = strings.ToLower(stdout) == "true" || stdout == "1"
	}

	// 是否记录调用者信息
	if reportCaller := os.Getenv("LOG_REPORT_CALLER"); reportCaller != "" {
		config.ReportCaller = strings.ToLower(reportCaller) == "true" || reportCaller == "1"
	}

	// 是否使用JSON格式
	if jsonFormat := os.Getenv("LOG_JSON_FORMAT"); jsonFormat != "" {
		config.JSONFormat = strings.ToLower(jsonFormat) == "true" || jsonFormat == "1"
	}

	// 初始化日志
	InitLogger(config)
}
