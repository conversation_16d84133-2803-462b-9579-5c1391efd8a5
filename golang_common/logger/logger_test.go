package logger

import (
	"testing"
	"time"

	"go.uber.org/zap"
)

// ExampleLogging 展示不同的日志记录方式
func TestExampleLogging(t *testing.T) {
	// 1. 使用全局函数记录日志 - 最简单的方式
	Info("这是一条简单的信息日志")
	Error("这是一条错误日志: %v", "数据库连接失败")

	// 2. 使用SugaredLogger - 格式化灵活，但性能略低
	sugar := GetSugaredLogger()
	sugar.Infof("用户 %s 登录成功", "admin")
	sugar.Warnw("服务性能降级",
		"component", "database",
		"latency", 153*time.Millisecond,
	)

	// 3. 使用原生Logger - 性能最高
	zapLogger := GetLogger()
	zapLogger.Info("系统初始化完成",
		zap.String("environment", "production"),
		zap.Int("connectedUsers", 42),
		zap.Duration("bootTime", 1249*time.Millisecond),
	)

	// 4. 使用带有字段的日志器 - 适合追踪请求、用户等
	fields := map[string]interface{}{
		"requestID": "req-123-abc",
		"userID":    1001,
		"action":    "checkout",
		"module":    "payment",
	}
	reqLogger := WithFields(fields)
	reqLogger.Infof("开始处理支付请求")

	// 模拟处理
	time.Sleep(10 * time.Millisecond)

	reqLogger.Infof("支付请求处理完成")
}
