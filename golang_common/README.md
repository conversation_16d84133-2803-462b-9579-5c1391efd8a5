# golang_common

## 安装

安装

```shell
  # 配置私有仓库
  go env -w GOPRIVATE=codeup.aliyun.com

  # 配置git仓库访问权限, GIT_USER:在codeup中"Https密码"中的克隆帐号; GIT_TOKEN: 个人访问令牌
  echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc

  go get codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common
```

Mac 下使用

```shell
# Mac 13.5 之后升级了 clang 15.0 导致不能用

brew install gcc

go env -w CC='/usr/local/Cellar/gcc/13.2.0/bin/gcc-13'
go env -w CXX='/usr/local/Cellar/gcc/13.2.0/bin/g++-13'

```
