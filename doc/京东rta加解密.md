# 京东科技通用RTA接口-加密文档

## 请求地址
- **预发环境**: `https://mk-pre.jrjd.com/rta/genericaes/(mediaCode)`  
- **生产环境**: `https://mk.jrjd.com/rta/genericaes/(mediaCode)`  
- **请求方式**: `POST` (Content-Type: `application/json`)

---

## 入参说明
### 主请求参数
| 字段        | 类型     | 必传 | 说明                                                                 |
|-------------|----------|------|----------------------------------------------------------------------|
| requestId   | String   | Y    | 调用方生成的唯一请求ID                                               |
| mediaCode   | String   | Y    | 媒体编码                                                             |
| timestamp   | String   | Y    | 时间戳（毫秒），需联系京东科技分配                                   |
| requestData | String   | Y    | RTA参数JSON格式化后的字符串，需加密（字段见下表）                    |
| sign        | String   | Y    | 对入参报文的签名值（签名算法见下文）                                 |

### `requestData` 参数
| 字段         | 类型          | 必传 | 说明                                                                 |
|--------------|---------------|------|----------------------------------------------------------------------|
| idType       | String        | Y    | 设备ID类型：<br>`imei`（手机IMEI）<br>`paid_orgnl`（安卓OAID）<br>`idfa`（苹果IDFA）<br>`mobile`（手机号） |
| idMd5Value   | String        | Y    | 设备ID的MD5值                                                        |
| idValue      | String        | N    | 设备ID明文（与`idMd5Value`二选一）                                   |
| rtaIds       | List<String>  | N    | RTA策略列表                                                          |
| ext          | String        | N    | 扩展字段（JSON格式字符串）                                           |

---

## 出参说明
| 字段         | 类型          | 说明                                                                 |
|--------------|---------------|----------------------------------------------------------------------|
| requestId    | String        | 原请求ID                                                            |
| code         | String        | `0`表示成功，其他为异常码                                           |
| msg          | String        | 异常信息                                                            |
| status       | Integer       | 是否参与：<br>`0`不参与<br>`1`参与                                   |
| rtaIdResults | List<Object>  | RTA ID结果（无需关注）                                              |
| rtaId        | String        | RTA ID                                                              |
| status       | Integer       | 是否参与（同主`status`）                                            |

---

## 加密与签名算法
### 密钥获取
登录京东科技代理端获取：  
[https://aglo.jd.com/#/Home](https://aglo.jd.com/#/Home)

### AES加密工具类
```java
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class AesUtils {
    public static String encrypt(String content, String key) {
        try {
            byte[] bytesKey = Base64.decodeBase64(key);
            SecretKeySpec secretKey = new SecretKeySpec(bytesKey, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] result = cipher.doFinal(content.getBytes("utf-8"));
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}