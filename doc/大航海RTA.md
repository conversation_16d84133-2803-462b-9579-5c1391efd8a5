# 大航海ADS中台 RTA用户定向产品文档

版本：V2.2  
日期：2020年 2月 28日

## 一、概念及功能介绍

### 1. 接口概念及功能

RTA（Real time API）是手淘为各合作渠道方提供的实时数据问询接口功能，合作渠道可通过实时问询该接口，实时筛选在推广业务的可投放人群，为 ocpx广告投放提供足量、准确的数据源。

### 2. 业务概念

- **拉新**：获取手机端新登录用户。
- **拉活**：唤醒手机中在装淘宝的用户。
- **流失召回**：通过广告推广唤醒长时间未打开淘宝但手机仍在装淘宝的用户。
- **新客**：发现新购买行为的用户。

说明：使用设备ID作为用户标识，iPhone为 IDFA，Android手机为 IMEI或 OAID。

## 二、对接流程及注意事项

### 1. RTA对接流程

1. 渠道入驻大航海，签订合同、创建广告位、获取投放素材&链接；
2. 技术准入标准：每日可问询的设备量级在 1亿以上，QPS在 5K以上；
3. 配置问询接口域名，阿里做加白处理；
4. 按照手淘API接口字段规范开发、联调；
5. 联调通过后，调整 RTA接口的问询 QPS，正式启动线上问询；
6. 如联调失败，修复问题后再次联调，直至成功；
7. 实时互斥逻辑：同一业务类型，针对同一用户的广告触达有频次限制。

### 2. RTA接口说明

#### 单次请求接口

- 使用场景：面向有一定技术能力和数据能力的广告主。
- 请求参数字段说明：
  - channel: String, 必传, 大航海渠道ID
  - advertisingSpaceId: String, 必传, 广告位ID
  - imei/idfa/oaid及其md5值等

- 返回值字段说明：
  - errcode: Int, 错误码
  - result: Boolean, true表示目标用户，false表示非目标用户
  - task_id: String, 在大航海平台领取的任务ID

#### 批量请求接口

- 使用场景：基于单次RTA请求接口封装批量请求，一次最多查询20个设备。
- 请求参数字段说明：
  - channel: String, 必传, 大航海渠道ID
  - advertisingSpaceId: String, 必传, 广告位ID
  - imei_md5/idfa_md5/oaid_md5等

- 返回值字段说明：
  - errcode: Int, 错误码
  - results: Array, 命中的设备对应的任务信息

### 3. 阿里大航海离线数据对接

实时RTA问询服务对接完成前，渠道方可先对接使用大航海离线人群数据，每日取渠道方判定可投人群包与阿里云OSS可投人群包的交集进行推广投放。

## 三、对接常见Q&A

- **问**：top接口的appkey是否需要申请？
- **答**：是。

- **问**：adid，channel，cid这几个参数怎么获取？
- **答**：adid和channel由运营负责配置，cid暂时不用。

- **问**：为什么投放了type返回的都是拉新？
- **答**：接口默认返回就是拉新，需通过广告位id来区分投放计划。

- **问**：调用top接口时提示“isv.permission-api-package-limit”？
- **答**：appkey需要申请权限。