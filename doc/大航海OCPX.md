# 大航海 ADS中台-OCPX数据上报&转化回传产品文档

版本：V2.4  
日期：2020年 3月 25日

## 一、概念及功能介绍

### 1. 接口概念及功能

阿里提供 OCPX投放曝光、点击上报及转化回传功能，渠道方使用 OCPX投放时，实时上报曝光&点击并回调广告主侧转化数据，来优化 OCPX算法模型，校准 CTR&CVR预估水平，进而降低转化成本。

### 2. 业务概念

- **新登**：首次在手机客户端上登录的用户。
- **唤醒**：通过广告推广使已安装淘宝的用户再次打开应用。
- **流失召回**：针对长时间未打开淘宝但手机仍在装淘宝的用户进行召回。
- **新客**：发现新购买行为的用户。

说明：使用设备ID作为用户标识，iPhone为 IDFA，Android手机为 IMEI或 OAID。

## 二、对接流程及注意事项

### 1. 对接流程

1. 渠道入驻大航海，签订合同、创建广告位、获取投放素材&链接；
2. 按手淘接口字段规范开发、联调；
3. 联调通过后，正式启动投放；
4. 如联调失败，修复问题后再次联调，直至成功；
5. 注意事项：务必填写广告投放相关字段，以免遗漏渠道转化归因数据；

### 2. 接口介绍

#### 曝光上报接口

- 接口类型: HTTPS
- 接口主域：`https://wcp.taobao.com/adstrack/track.json?action=1`
- 请求类型: GET/POST
- 必填参数：
  - channel: String, 必传, 大航海渠道ID
  - advertisingSpaceId: String, 必传, 广告位ID
  - taskId: String, 必传, 任务ID
  - 设备信息（IMEI/Md5_IMEI/IDFA/Md5_IDFA/OAID/Md5_OAID）至少一项不可为空

#### 点击上报及转化回调接口

- 接口类型: HTTPS
- 接口主域：`https://wcp.taobao.com/adstrack/track.json?action=2`
- 请求类型: GET/POST
- 必填参数：
  - channel: String, 必传, 大航海渠道ID
  - advertisingSpaceId: String, 必传, 广告位ID
  - taskId: String, 必传, 任务ID
  - 设备信息（IMEI/Md5_IMEI/IDFA/Md5_IDFA/OAID/Md5_OAID）至少一项不可为空
- 转化类型包括激活、新登、唤端和购买等

表一：接口重点字段

| 参数名 | 参数类型 | 是否必传 | 备注 |
| --- | --- | --- | --- |
| channel | String | 是 | 大航海渠道ID |
| advertisingSpaceId | String | 是 | 大航海广告位ID |
| taskId | String | 是 | 大航海的任务ID |
| ... | ... | ... | ... |

## 三、对接常见Q&A

- **问**：top接口的appkey是否需要申请？
- **答**：是。

- **问**：adid，channel，cid这几个参数怎么获取？
- **答**：adid和channel由运营负责配置，cid暂时不用。

- **问**：为什么投放了type返回的都是拉新？
- **答**：接口默认返回就是拉新，需通过广告位id来区分投放计划。

- **问**：调用top接口时提示“isv.permission-api-package-limit”？
- **答**：appkey需要申请权限。