# 京东科技--点击监测回传文档

## 1. 上报链接
上报链接由京东科技提供，示例：  
`https://www.xxx.com/?code=XXX&account_id=XXX&idfa={idfa}&oa_id={oaid}&platform={os}&click_time={ts}&callback_url={callback}`  

### 上报链接说明：
1. **code** 和 **account_id**：京东科技自定义参数，无需处理。  
2. **callback_url**：有转化发生时，会调用渠道侧提供的回传链接 `callback_url`。  

---

## 2. 通讯协议
- 协议：`HTTPS GET`  
- 请求/响应格式：`application/json`  

---

## 3. 请求参数说明

| 参数名       | 参数类型 | 是否必填 | 参数说明                          |
|--------------|----------|----------|-----------------------------------|
| {idfa}       | string   | 否       | iOS设备标识，传IDFA原值           |
| {oaid}       | string   | 否       | Android设备标识，传OAID原值       |
| {os}         | string   | 是       | 设备操作系统：`android` 或 `ios`  |
| {ts}         | long     | 是       | 广告点击时间（毫秒时间戳）        |
| {callback}   | string   | 否       | 转化回调URL                       |

---

## 4. 响应结果

| 字段名称 | 字段说明   | 类型   | 必填 | 备注                          |
|----------|------------|--------|------|-------------------------------|
| code     | 响应码     | string | Y    | `"0"`：成功；`"-1"`：失败     |
| msg      | 响应信息   | string | Y    | 成功或失败的描述信息          |

---

## 5. 回调地址说明
1. **请求方式**：`GET`  
2. **参数拼接**：  
   - 在 `callback` 链接后拼接 `event_type` 参数，取值约定：  
     - `0`：表示APP新增用户  
     - `1`：表示APP低活跃用户  