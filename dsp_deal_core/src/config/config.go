package config

import "time"

// mysql
const (
	MySQLHost      string = "pc-2zec97555354c4g16.rwlb.rds.aliyuncs.com"
	MySQLAdxDbname string = "adx"
	MySQLDspDbname string = "dsp"
	MySQLUserName  string = "maplehaze"
	MySQLPassword  string = "Mhint@123"
	MySQLPort      string = "3306"
)

// redis
const (
	RedisHost string = "r-2zejfdoaq1comnt5pv.redis.rds.aliyuncs.com"
	RedisPort string = "6379"
)

// ClickHouse
const (
	ClickHouseHostPort     string = "tcp://cc-2ze0ogu94yp20oa12-clickhouse.clickhouseserver.rds.aliyuncs.com:9000?username=maplehaze&password=Mhint@123"
	ClickHouseReqMaxNum    int    = 300000
	ClickHouseExpMaxNum    int    = 135000
	ClickHouseClkMaxNum    int    = 27000
	ClickHouseActiveMaxNum int    = 500
	ClickHouseInterval     int64  = 60
)

// holo
const (
	// vpc
	HoloHostPort string = "host=hgpostcn-cn-zxu34m2qh002-cn-beijing-vpc-st.hologres.aliyuncs.com port=80 dbname=dsp_rawdata user=BASIC$maplehaze password=Mhint@123 sslmode=disable"
	// 外网
	// HoloHostPort string = "host=hgpostcn-cn-zxu34m2qh002-cn-beijing.hologres.aliyuncs.com port=80 dbname=dsp_rawdata user=BASIC$maplehaze password=Mhint@123 sslmode=disable"
)

// log path
const (
	IPDBPath string = "./ip2region.db"
)

// 接口回调
const (
	ExternalExpURL               string = "https://dsp.maplehaze.cn/api/exp"
	ExternalClkURL               string = "https://dsp.maplehaze.cn/api/clk"
	ExternalDhhActiveURL         string = "https://dsp.maplehaze.cn/dhh/act"
	ExternalAMapActiveURL        string = "https://dsp.maplehaze.cn/act/amap"
	ExternalKuaiShouActiveURL    string = "https://dsp.maplehaze.cn/act/kuaishou"
	ExternalUCActiveURL          string = "https://dsp.maplehaze.cn/act/uc"
	ExternalDPURL                string = "https://dsp.maplehaze.cn/api/dp"
	ExternalTencentActiveURL     string = "https://dsp.maplehaze.cn/act/tencent"
	ExternalJuMeiTongYiActiveURL string = "https://dsp.maplehaze.cn/act/jumei_ty"
	ExternalJuMeiPZDSActiveURL   string = "https://dsp.maplehaze.cn/act/jumei_pzds"
	ExternalWeiBoActiveURL       string = "https://dsp.maplehaze.cn/act/weibo"
	ExternalAlipayActiveURL      string = "https://dsp.maplehaze.cn/act/alipay"
	ExternalJDActiveURL          string = "https://dsp.maplehaze.cn/act/jingdong"
)

// cache
const (
	// CacheServerURL         string        = "http://cache-config-core-svc"
	// CacheServerHttpTimeOut time.Duration = 100 * time.Millisecond
	CacheTTL time.Duration = 60 * time.Second
)
