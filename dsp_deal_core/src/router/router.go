package router

import (
	"dsp_core/api"
	"dsp_core/db"
	"fmt"
	"runtime"

	monitor "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/monitor/api"
	"github.com/gin-gonic/gin"
)

// Init ...
func Init() *gin.Engine {
	router := gin.Default()

	router.Use(monitor.PrometheusAPIMiddleware())

	// 初始化 prometheus

	if err := monitor.Init("dsp_deal_core", 1); err != nil {
		panic(fmt.Sprintf("init prometheus failed: %v", err))
	}

	// metrics 路由组
	metricApi := router.Group("/metrics")
	{
		metricApi.GET("/bigcache", func(c *gin.Context) {
			stats := db.GlbBigCache.Stats()
			entriesCount := db.GlbBigCache.Len()       // 获取当前条目数量
			capacityBytes := db.GlbBigCache.Capacity() // 获取当前分配的容量(字节)

			// 获取系统内存信息
			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			c.JSON(200, gin.H{
				// BigCache统计
				"entries":          entriesCount,                                                              // 当前条目数量
				"capacity_bytes":   capacityBytes,                                                             // 当前内部分配的空间容量(字节)
				"capacity_mb":      capacityBytes / 1024 / 1024,                                               // 当前内部分配的空间容量(MB)
				"max_capacity_mb":  db.GlbBigCacheMaxSizeMB,                                                   // 最大容量限制(MB)
				"usage_percentage": float64(capacityBytes) * 100 / float64(db.GlbBigCacheMaxSizeMB*1024*1024), // 空间占用率(%)

				// 缓存命中统计
				"hits":       stats.Hits,       // 缓存命中次数
				"misses":     stats.Misses,     // 缓存未命中次数
				"delHits":    stats.DelHits,    // 删除操作命中次数
				"delMisses":  stats.DelMisses,  // 删除操作未命中次数
				"collisions": stats.Collisions, // 哈希冲突次数

				// 系统内存信息(仅供参考)
				"alloc_mb": m.Alloc / 1024 / 1024, // 当前分配的总内存(MB)
				"sys_mb":   m.Sys / 1024 / 1024,   // 从系统获取的内存(MB)
			})
		})
		metricApi.GET("", monitor.PrometheusHandler())
	}

	// 路由组: api
	v1 := router.Group("/api")
	{
		v1.POST("/req", api.Req)
		v1.GET("/exp", api.Exp)
		v1.GET("/clk", api.Clk)
		v1.GET("/test", api.Test) // 只对 test 接口加中间件
		v1.GET("/testHolo3", api.CheckHologres3)
	}
	v2 := router.Group("/dhh")
	{
		v2.GET("/act", api.DhhAct)
	}
	v3 := router.Group("/cpa")
	{
		v3.GET("/act", api.TencentAct)
	}
	v4 := router.Group("/adx")
	{
		v4.GET("/report", api.AdxClk)
		v4.GET("/exp", api.AdxExp)
		v4.GET("/clk", api.AdxClk)
	}
	// v5 := router.Group("/store")
	// {
	// 	v5.GET("/report", api.StoreReport)
	// }
	// v6 := router.Group("/app")
	// {
	// 	v6.GET("/act", api.AppAct)
	// }
	v7 := router.Group("/extra")
	{
		v7.POST("/cn", api.ExtraCN)
		v7.POST("/wk", api.ExtraWK)
		// 剪贴板接口, 返回数组
		v7.POST("/cp", api.ExtraCP)
	}
	v8 := router.Group("/act")
	{
		v8.GET("/amap", api.AMapAct)
		v8.GET("/kuaishou", api.KuaiShouAct)
		v8.GET("/uc", api.UCAct)
		v8.GET("/tencent", api.TencentAct)
		v8.GET("/jumei_ty", api.JuMeiTongYiAct)
		v8.GET("/jumei_pzds", api.JuMeiPanZhiDaiShouAct)
		v8.GET("/weibo", api.WeiBoAct)
		v8.GET("/alipay", api.AlipayAct)
		v8.GET("/jingdong", api.JDAct)
	}

	v9 := router.Group("/flow")
	{
		v9.POST("/req", api.FlowReq)
	}
	// router.Run(":8081")
	return router
}
