package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"io"
	l "log"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// IsUCRtaOK ...
func IsUCRtaOK(c *gin.Context, bigdataUID string, dspReq *models.DspReqStu, planInfo *models.DspPlanStu) bool {
	return false
}

// UCClkReport ocpx
func UCClkReport(c *gin.Context, log url.Values) {
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, log.Get("plan_id"))
	if planInfo == nil {
		return
	}

	if planInfo.IsOCPX == 1 {
	} else {
		return
	}

	if len(planInfo.UCOCPXCh) == 0 {
		return
	}

	os := log.Get("os")
	if os == "ios" {
		return
	}
	// osv := log.Get("osv")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	oaidMd5 := log.Get("oaid_md5")
	// idfa := log.Get("idfa")
	// idfaMd5 := log.Get("idfa_md5")
	// caidMultiStr := log.Get("caid_multi")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	// idfaMd5Str := ""
	// if len(idfa) > 0 {
	// 	idfaMd5Str = utils.GetMd5(idfa)
	// } else if len(idfaMd5) > 0 {
	// 	idfaMd5Str = idfaMd5
	// }

	ip := c.ClientIP()
	ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeUCParams(planInfo, log)
	activateParams.Add("log", encodeParams)

	callback := config.ExternalUCActiveURL + "?" + activateParams.Encode()
	////////////////////////////////////////////////////////////////////////////////////////
	// ocpx接口仅限DSP媒体适配使用。投放其他媒体需要报备。未经报备、泄露接口给外部等违规情况，将直接关停不再放行。
	// *请勿将支持曝光的媒体放入点击请求，异常点击量会触发异常/告警阈值。
	// *未上线前无法收到回调，请找我配合验证激活是否符合。
	// *不支持投放iOS，投放扣结算。

	// UC android
	// 点击
	// https://unet.ucweb.com/v3/ad/grcx_dsp?type=click&ch=渠道标识从素材库获取&product=UC&targetPkg=com.UCMobile&imei=__IMEI__&imeiSum=__IMEI_SUM__&oaid=__OAID__&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__
	// 曝光
	// https://unet.ucweb.com/v3/ad/show/grcx_dsp?ch=渠道标识从素材库获取&product=UC&targetPkg=com.UCMobile&imei=__IMEI__&imeiSum=__IMEI_SUM__&oaid=__OAID__&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__
	// ocpxURL := "https://unet.ucweb.com/v3/ad/grcx_dsp?type=click&ch=__CH__" +
	// 	"&product=UC&targetPkg=com.UCMobile" +
	// 	"&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__" +
	// 	"&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__&os=__OS__"

	// ocpxURL = strings.Replace(ocpxURL, "__CALLBACK__", callback, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__IP__", ip, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__UA__", ua, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
	// ocpxURL = strings.Replace(ocpxURL, "__OS__", os, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__CH__", planInfo.UCOCPXCh, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__campaignId__", planInfo.PID, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__creativeId__", utils.GetMd5(planInfo.PID), -1)
	// ocpxURL = strings.Replace(ocpxURL, "__adGroupId__", planInfo.GID, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__unionSite__", planInfo.PID, -1)
	// if len(imeiMd5Str) > 0 {
	// 	ocpxURL = ocpxURL + "&imeiSum=" + imeiMd5Str
	// }
	// if len(oaid) > 0 {
	// 	ocpxURL = ocpxURL + "&oaidSum=" + utils.GetMd5(oaid)
	// }

	ocpxURL := "https://unet.ucweb.com/v3/ad/grcx_dsp?type=click"
	////////////////////////////////////////////////////////////////////////////////////////
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()

	q.Add("ch", planInfo.UCOCPXCh)
	q.Add("product", "UC")
	q.Add("targetPkg", planInfo.PackageName)
	q.Add("reqId", uuid.NewV4().String())
	q.Add("time", utils.ConvertInt64ToString(utils.GetCurrentSecond()))
	q.Add("ip", ip)
	q.Add("ua", ua)
	q.Add("campaignId", planInfo.PID)
	q.Add("creativeId", utils.GetMd5(planInfo.PID))
	q.Add("adGroupId", planInfo.GID)
	q.Add("unionSite", planInfo.UCOCPXPosID)
	q.Add("os", "android")
	if os == "android" {
		if len(imeiMd5Str) > 0 {
			q.Add("imeiSum", imeiMd5Str)
		}
		if len(oaid) > 0 {
			q.Add("oaidSum", utils.GetMd5(oaid))
		} else if len(oaidMd5) > 0 {
			q.Add("oaidSum", oaidMd5)
		}
	} else if os == "ios" {
	}

	q.Add("callbackUrl", callback)
	requestGet.URL.RawQuery = q.Encode()

	l.Println("uc ocpx req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("uc clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("uc clk resp is nil")
		return
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)
	l.Println("uc ocpx resp: " + string(bodyContent))
}

// EncodeUCParams ...
func EncodeUCParams(planInfo *models.DspPlanStu, log url.Values) string {
	activateParams := url.Values{}
	activateParams.Add("uid", log.Get("uid"))
	activateParams.Add("plan_id", log.Get("plan_id"))
	activateParams.Add("market_type", log.Get("market_type"))
	activateParams.Add("ads_type", log.Get("ads_type"))
	activateParams.Add("ext_dsp_channel", log.Get("ext_dsp_channel"))
	activateParams.Add("media_channel", log.Get("media_channel"))
	activateParams.Add("sub_channel_id", log.Get("sub_channel_id"))
	activateParams.Add("ext_adx", log.Get("ext_adx"))
	activateParams.Add("os", log.Get("os"))
	activateParams.Add("osv", log.Get("osv"))

	activateParams.Add("did_md5", log.Get("did_md5"))
	if len(log.Get("imei")) > 0 {
		activateParams.Add("imei", log.Get("imei"))
	}
	if len(log.Get("imei_md5")) > 0 {
		activateParams.Add("imei_md5", log.Get("imei_md5"))
	}
	if len(log.Get("android_id")) > 0 {
		activateParams.Add("android_id", log.Get("android_id"))
	}
	if len(log.Get("android_id_md5")) > 0 {
		activateParams.Add("android_id_md5", log.Get("android_id_md5"))
	}
	if len(log.Get("idfa")) > 0 {
		activateParams.Add("idfa", log.Get("idfa"))
	}
	if len(log.Get("idfa_md5")) > 0 {
		activateParams.Add("idfa_md5", log.Get("idfa_md5"))
	}
	if len(log.Get("oaid")) > 0 {
		activateParams.Add("oaid", log.Get("oaid"))
	}
	if len(log.Get("oaid_md5")) > 0 {
		activateParams.Add("oaid_md5", log.Get("oaid_md5"))
	}
	if len(strings.TrimSpace(log.Get("model"))) > 0 {
		activateParams.Add("model", strings.TrimSpace(log.Get("model")))
	}
	if len(strings.TrimSpace(log.Get("manufacturer"))) > 0 {
		activateParams.Add("manufacturer", strings.TrimSpace(log.Get("manufacturer")))
	}
	if len(log.Get("caid_multi")) > 0 {
		activateParams.Add("caid_multi", log.Get("caid_multi"))
	}
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
		} else if planInfo.UnitPriceType == 1 {
		} else if planInfo.UnitPriceType == 2 {
			activateParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}

	encodeStr, _ := utils.EncodeString([]byte(activateParams.Encode()))
	return encodeStr
}

// UCClkReportFromAdx ocpx
func UCClkReportFromAdx(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) {
	if planInfo == nil {
		return
	}

	if planInfo.IsOCPX == 1 {
	} else {
		return
	}

	if len(planInfo.UCOCPXCh) == 0 {
		return
	}

	os := mhCpaReq.Os
	if os == "ios" {
		return
	}
	// osv := log.Get("osv")
	// oaid := mhCpaReq.Oaid
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	ip := mhCpaReq.IP
	ua := mhCpaReq.UA

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeUCParamsFromAdx(planInfo, mhCpaReq)
	activateParams.Add("log", encodeParams)

	callback := config.ExternalUCActiveURL + "?" + activateParams.Encode()
	////////////////////////////////////////////////////////////////////////////////////////
	// ocpx接口仅限DSP媒体适配使用。投放其他媒体需要报备。未经报备、泄露接口给外部等违规情况，将直接关停不再放行。
	// *请勿将支持曝光的媒体放入点击请求，异常点击量会触发异常/告警阈值。
	// *未上线前无法收到回调，请找我配合验证激活是否符合。
	// *不支持投放iOS，投放扣结算。

	// UC android
	// 点击
	// https://unet.ucweb.com/v3/ad/grcx_dsp?type=click&ch=渠道标识从素材库获取&product=UC&targetPkg=com.UCMobile&imei=__IMEI__&imeiSum=__IMEI_SUM__&oaid=__OAID__&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__
	// 曝光
	// https://unet.ucweb.com/v3/ad/show/grcx_dsp?ch=渠道标识从素材库获取&product=UC&targetPkg=com.UCMobile&imei=__IMEI__&imeiSum=__IMEI_SUM__&oaid=__OAID__&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__
	// ocpxURL := "https://unet.ucweb.com/v3/ad/grcx_dsp?type=click&ch=__CH__" +
	// 	"&product=UC&targetPkg=com.UCMobile" +
	// 	"&time=__TS__&callbackUrl=__CALLBACK__&ip=__IP__&ua=__UA__&campaignId=__campaignId__" +
	// 	"&creativeId=__creativeId__&adGroupId=__adGroupId__&unionSite=__unionSite__&os=__OS__"

	// ocpxURL = strings.Replace(ocpxURL, "__CALLBACK__", callback, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__IP__", ip, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__UA__", ua, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__TS__", utils.ConvertInt64ToString(utils.GetCurrentSecond()), -1)
	// ocpxURL = strings.Replace(ocpxURL, "__OS__", os, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__CH__", planInfo.UCOCPXCh, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__campaignId__", planInfo.PID, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__creativeId__", utils.GetMd5(planInfo.PID), -1)
	// ocpxURL = strings.Replace(ocpxURL, "__adGroupId__", planInfo.GID, -1)
	// ocpxURL = strings.Replace(ocpxURL, "__unionSite__", planInfo.PID, -1)
	// if len(mhCpaReq.Imei) > 0 {
	// 	ocpxURL = ocpxURL + "&imeiSum=" + utils.GetMd5(mhCpaReq.Imei)
	// } else if len(mhCpaReq.ImeiMd5) > 0 {
	// 	ocpxURL = ocpxURL + "&imeiSum=" + mhCpaReq.ImeiMd5
	// }
	// if len(mhCpaReq.Oaid) > 0 {
	// 	ocpxURL = ocpxURL + "&oaidSum=" + utils.GetMd5(mhCpaReq.Oaid)
	// } else if len(mhCpaReq.OaidMd5) > 0 {
	// 	ocpxURL = ocpxURL + "&oaidSum=" + mhCpaReq.OaidMd5
	// }
	ocpxURL := "https://unet.ucweb.com/v3/ad/grcx_dsp?type=click" +
		"&product=UC&targetPkg=com.UCMobile"
	////////////////////////////////////////////////////////////////////////////////////////
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()

	q.Add("ch", planInfo.UCOCPXCh)
	q.Add("time", utils.ConvertInt64ToString(utils.GetCurrentSecond()))
	q.Add("ip", ip)
	q.Add("ua", ua)
	q.Add("campaignId", planInfo.PID)
	q.Add("creativeId", utils.GetMd5(planInfo.PID))
	q.Add("adGroupId", planInfo.GID)
	q.Add("unionSite", planInfo.UCOCPXPosID)
	q.Add("os", "android")
	if os == "android" {
		if len(mhCpaReq.Imei) > 0 {
			q.Add("imeiSum", utils.GetMd5(mhCpaReq.Imei))

		} else if len(mhCpaReq.ImeiMd5) > 0 {
			q.Add("imeiSum", mhCpaReq.ImeiMd5)
		}
		if len(mhCpaReq.Oaid) > 0 {
			q.Add("oaidSum", utils.GetMd5(mhCpaReq.Oaid))

		} else if len(mhCpaReq.OaidMd5) > 0 {
			q.Add("oaidSum", mhCpaReq.OaidMd5)

		}
	} else if os == "ios" {
	}

	q.Add("callbackUrl", callback)
	requestGet.URL.RawQuery = q.Encode()

	l.Println("uc ocpx req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("uc clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("uc clk resp is nil")
		return
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)
	l.Println("uc ocpx resp: " + string(bodyContent))
}

// EncodeUCParamsFromAdx ...
func EncodeUCParamsFromAdx(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) string {
	bigdataParams := url.Values{}
	bigdataParams.Add("uid", mhCpaReq.UID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "1")
	bigdataParams.Add("ext_adx", mhCpaReq.Channel)

	// unit_price_type: 0 CPM, 1 CPC, 2 CPA
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
			bigdataParams.Add("cpm_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 1 {
			bigdataParams.Add("cpc_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 2 {
			bigdataParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	// ssp_price_type: 0 CPM, 1 CPC
	if planInfo.SspPriceNum > 0 {
		if planInfo.SspPriceType == 0 {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		} else if planInfo.SspPriceType == 1 {
			bigdataParams.Add("ext_cpc_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		}
	}
	bigdataParams.Add("os", mhCpaReq.Os)
	if len(mhCpaReq.Imei) > 0 {
		bigdataParams.Add("imei", mhCpaReq.Imei)
	}
	if len(mhCpaReq.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", mhCpaReq.ImeiMd5)
	}
	if len(mhCpaReq.Oaid) > 0 {
		bigdataParams.Add("oaid", mhCpaReq.Oaid)
	}
	if len(mhCpaReq.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", mhCpaReq.OaidMd5)
	}
	if len(mhCpaReq.Idfa) > 0 {
		bigdataParams.Add("idfa", mhCpaReq.Idfa)
	}
	if len(mhCpaReq.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", mhCpaReq.IdfaMd5)
	}

	if len(mhCpaReq.Osv) > 0 {
		bigdataParams.Add("osv", mhCpaReq.Osv)
	}
	if len(mhCpaReq.DIDMd5) > 0 {
		bigdataParams.Add("did_md5", mhCpaReq.DIDMd5)
	}

	bigdataParams.Add("ip", mhCpaReq.IP)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}
