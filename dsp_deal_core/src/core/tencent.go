package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"io/ioutil"
	l "log"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
)

// TencentExp ...
func TencentExp(c *gin.Context, log url.Values) {
}

// TencentSSPClk ...
func TencentSSPClk(c *gin.Context, log url.Values) {
	// uid
	uid := log.Get("uid")

	// plan_id
	planID := log.Get("plan_id")

	// os
	os := log.Get("os")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	oaidMd5 := log.Get("oaid_md5")
	// androidID := log.Get("android_id")
	// androidIDMd5 := log.Get("android_id_md5")
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")
	// crid := log.Get("crid")

	// 调用上游
	mhCpaReq := models.MHCpaReq{}
	mhCpaReq.Channel = "ssp"
	mhCpaReq.UID = uid
	mhCpaReq.Imei = imei
	mhCpaReq.ImeiMd5 = imeiMd5
	mhCpaReq.Idfa = idfa
	mhCpaReq.IdfaMd5 = idfaMd5
	mhCpaReq.Oaid = oaid
	mhCpaReq.OaidMd5 = oaidMd5
	mhCpaReq.Os = os
	mhCpaReq.IP = c.ClientIP()
	mhCpaReq.UA = c.GetHeader("User-Agent")
	mhCpaReq.CPAPrice = utils.ConvertStringToInt(log.Get("cpa_price"))
	mhCpaReq.ExtCPAPrice = utils.ConvertStringToInt(log.Get("ext_cpa_price"))

	l.Println(mhCpaReq)
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo == nil {
		l.Printf("TencentSSPClk plan_id=%v not found\n", planID)
		return
	}

	if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "3" {
		// tencent
		TencentClkReport(c, planInfo, &mhCpaReq)
	}
}

// TencentClkReport ...
func TencentClkReport(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) {
	// uid
	// uid := log.Get("uid")

	// plan_id
	// planID := log.Get("plan_id")

	// os
	os := mhCpaReq.Os
	imei := mhCpaReq.Imei
	imeiMd5 := mhCpaReq.ImeiMd5
	oaid := mhCpaReq.Oaid
	oaidMd5 := mhCpaReq.OaidMd5
	// androidID := log.Get("android_id")
	// androidIDMd5 := log.Get("android_id_md5")
	idfa := mhCpaReq.Idfa
	idfaMd5 := mhCpaReq.IdfaMd5
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")
	// crid := log.Get("crid")

	// 调用上游
	// mhCpaReq := MHCpaReq{}
	// mhCpaReq.DownType = 1
	// mhCpaReq.Imei = imei
	// mhCpaReq.ImeiMd5 = imeiMd5
	// mhCpaReq.Idfa = idfa
	// mhCpaReq.IdfaMd5 = idfaMd5
	// mhCpaReq.Oaid = oaid
	// mhCpaReq.Os = os
	// mhCpaReq.IP = c.ClientIP()
	// mhCpaReq.UA = c.GetHeader("User-Agent")

	activateParams := url.Values{}
	tencentParams := EncodeTencentParams(planInfo, mhCpaReq)
	activateParams.Add("log", tencentParams)

	callback := config.ExternalTencentActiveURL + "?" + activateParams.Encode()

	// imeiMd5Str := ""
	// if len(imei) > 0 {
	// 	imeiMd5Str = utils.GetMd5(imei)
	// } else if len(imeiMd5) > 0 {
	// 	imeiMd5Str = imeiMd5
	// }
	// oaidMd5Str := ""
	// if len(oaid) > 0 {
	// 	oaidMd5Str = utils.GetMd5(oaid)
	// }
	// idfaStr := ""
	// if len(idfa) > 0 {
	// 	idfaStr = idfa
	// } else if len(idfaMd5) > 0 {
	// 	idfaStr = strings.ToLower(idfaMd5)
	// }

	// tecentClkURL := "https://ac.o2.qq.com/ocpa/ocpa_process/click"
	tecentClkURL := "https://api.iegadp.qq.com/iegadp/api/ocpa/ClickReport"
	// if strings.ToLower(os) == "ios" {
	// 	tecentClkURL = "https://api.iegadp.qq.com/iegadp/api/ocpa/ExposeReport"
	// }

	// excel示例, 2024-09-25
	// android
	// https://api.iegadp.qq.com/iegadp/api/ocpa/ClickReport?
	// media_type=fenglan
	// &gid=107868
	// &platid=1
	// &media=22245
	// &channel=10720621
	// &sch_id=
	// &loc_id=
	// &imei=__IMEI__
	// &imei_md5=__IMEI_MD5__
	// &oaid=__OAID__
	// &time=__TS__
	// &cip=__IP__
	// &callback=__CALLBACK__
	// &med_id=22245

	// ios
	// https://api.iegadp.qq.com/iegadp/api/ocpa/ExposeReport?
	// media_type=fenglan
	// &gid=107868
	// &platid=2
	// &media=22245
	// &channel=10034178
	// &sch_id=
	// &loc_id=
	// &idfa=__IDFA__
	// &idfa_md5=__IDFA_MD5__
	// &time=__TS__
	// &cip=__IP__
	// &callback=__CALLBACK__
	// &med_id=22245

	params := url.Values{}
	// params.Add("sign", "report")
	params.Add("gid", planInfo.TencentGID)
	params.Add("media", planInfo.TencentMedia)
	params.Add("med_id", planInfo.TencentMedia)
	params.Add("channel", planInfo.TencentChannel)
	params.Add("sch_id", planInfo.TencentSchID)
	// params.Add("mtr_id", selfPosInfo.TencentMtrID)
	params.Add("loc_id", planInfo.TencentLocID)
	if strings.ToLower(os) == "android" {
		// params.Add("os", "1")
		params.Add("platid", "1")
		// if len(imeiMd5Str) == 0 && len(oaid) == 0 && len(oaidMd5Str) == 0 {
		// 	return
		// }
	} else if strings.ToLower(os) == "ios" {
		// params.Add("os", "2")
		params.Add("platid", "2")
		// if len(idfaStr) == 0 {
		// 	return
		// }
	}
	// if len(log.Get("tencent_o2_trace_id")) > 0 {
	// 	params.Add("o2_trace_id", log.Get("tencent_o2_trace_id"))
	// }
	// params.Add("imei", imeiMd5Str)
	if strings.ToLower(os) == "android" {
		if len(imei) > 0 {
			params.Add("imei", imei)
		} else if len(imeiMd5) > 0 {
			params.Add("imei_md5", strings.ToLower(imeiMd5))
		}
		if len(oaid) > 0 {
			params.Add("oaid", oaid)
		}
		if len(oaidMd5) > 0 {
			params.Add("oaid_md5", oaidMd5)
		}
	} else if strings.ToLower(os) == "ios" {
		// params.Add("idfa", idfaStr)
		if len(idfa) > 0 {
			params.Add("idfa", idfa)
		} else if len(idfaMd5) > 0 {
			params.Add("idfa_md5", strings.ToLower(idfaMd5))
		}
		if len(mhCpaReq.CAIDMulti) > 0 {
			tmpCAIDMulitJsonData, _ := json.Marshal(mhCpaReq.CAIDMulti)
			params.Add("caid", string(tmpCAIDMulitJsonData))
		}
	}
	params.Add("cip", mhCpaReq.IP)
	params.Add("callback", callback)
	// params.Add("callback_type", "fenglan")
	params.Add("media_type", "fenglan")
	params.Add("time", utils.ConvertInt64ToString(utils.GetCurrentSecond()))
	// params.Add("mediaside_click_timestamp", utils.ConvertInt64ToString(utils.GetCurrentSecond()))

	// params.Add("oaid_md5", oaidMd5Str)
	// params.Add("scid", log.Get("tencent_scid"))
	// params.Add("sc_name", log.Get("tencent_sc_name"))
	// params.Add("aid", "")
	// params.Add("cid", "")
	// params.Add("adgroup_id", "")
	// params.Add("account_id", "")
	// params.Add("mac", "")
	// params.Add("android_id", "")

	// 以下说明针对《OCPA外部对接说明文档-new.pdf》
	// 文档字段修改：
	// 1、med_id改为media
	// 2、os改为platid
	// 3、time改为mediaside_click_timestamp
	// 文档字段不上报，ui中也取消：
	// 1、mtr_id（去掉）
	// 2、scid（去掉）
	// 3、sc_name（去掉）
	// 4、cip（去掉）
	// 系统中有以下字段，但文档中没有，备忘但不使用：
	// 1、aid
	// 2、cid
	// 2、adgroup_id
	// 3、account_id
	// 4、mac
	// 5、android_id
	// 6、o2_trace_id
	tecentClkURL = tecentClkURL + "?" + params.Encode()
	l.Println("cpa clk req: " + tecentClkURL)

	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", tecentClkURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	// l.Println("cpa clk req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("cpa clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("cpa clk resp is nil")
		return
	}
	defer resp.Body.Close()

	bodyContent, err := ioutil.ReadAll(resp.Body)
	// l.Printf("resp status code:[%d]\n", resp.StatusCode)
	// l.Printf("resp body data:[%s]\n", string(bodyContent))
	l.Println("cpa clk resp: " + string(bodyContent))

	tencentRespStu := TencentRespStu{}
	json.Unmarshal([]byte(bodyContent), &tencentRespStu)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Println("tencent debug panic:", err)
			}
		}()
		models.BigDataDebug(c, tecentClkURL, string(bodyContent), mhCpaReq)
	}()
}

// EncodeTencentParams ...
func EncodeTencentParams(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) string {
	cpaParams := url.Values{}
	cpaParams.Add("uid", mhCpaReq.UID)
	cpaParams.Add("plan_id", planInfo.PID)
	cpaParams.Add("market_type", planInfo.GroupMarketingType)
	cpaParams.Add("ads_type", planInfo.GroupAdsType)
	cpaParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	cpaParams.Add("os", mhCpaReq.Os)
	if len(mhCpaReq.Imei) > 0 {
		cpaParams.Add("imei", mhCpaReq.Imei)
	}
	if len(mhCpaReq.ImeiMd5) > 0 {
		cpaParams.Add("imei_md5", mhCpaReq.ImeiMd5)
	}
	if len(mhCpaReq.Oaid) > 0 {
		cpaParams.Add("oaid", mhCpaReq.Oaid)
	}
	if len(mhCpaReq.OaidMd5) > 0 {
		cpaParams.Add("oaid_md5", mhCpaReq.OaidMd5)
	}
	if len(mhCpaReq.Idfa) > 0 {
		cpaParams.Add("idfa", mhCpaReq.Idfa)
	}
	if len(mhCpaReq.IdfaMd5) > 0 {
		cpaParams.Add("idfa_md5", mhCpaReq.IdfaMd5)
	}
	cpaParams.Add("ip", mhCpaReq.IP)
	if mhCpaReq.Channel == "ssp" {
		cpaParams.Add("media_channel", "0")
	} else if mhCpaReq.Channel == "ks" {
		cpaParams.Add("media_channel", "1")
		cpaParams.Add("ext_adx", "0")
	} else if mhCpaReq.Channel == "oceanengine" {
		cpaParams.Add("media_channel", "1")
		cpaParams.Add("ext_adx", "1")
	} else if mhCpaReq.Channel == "douyu" {
		cpaParams.Add("media_channel", "1")
		cpaParams.Add("ext_adx", "2")
	} else if mhCpaReq.Channel == "store" {
		cpaParams.Add("media_channel", "2")
	}
	if len(mhCpaReq.CAIDMulti) > 0 {
		tmpCAIDMultiJson, _ := json.Marshal(mhCpaReq.CAIDMulti)
		cpaParams.Add("caid_multi", string(tmpCAIDMultiJson))
	}
	// 价格
	cpaParams.Add("cpa_price", utils.ConvertIntToString(mhCpaReq.CPAPrice))
	cpaParams.Add("ext_cpa_price", utils.ConvertIntToString(mhCpaReq.ExtCPAPrice))

	encodeStr, _ := utils.EncodeString([]byte(cpaParams.Encode()))
	return encodeStr
}

// TencentRespStu ...
type TencentRespStu struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// FeiFanRespStu ...
// type FeiFanRespStu struct {
// 	Code string `json:"code"`
// 	Msg  string `json:"msg"`
// }
