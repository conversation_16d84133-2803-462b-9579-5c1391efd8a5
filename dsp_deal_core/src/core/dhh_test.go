package core

import (
	"context"
	"dsp_core/models"
	"testing"

	"github.com/google/uuid"
)

func TestIsDangHangHaiOK(t *testing.T) {
	channel_id := "2200803434291"
	posId := "1545227"
	taskId := "32896"

	// 构造dspReq数据结构
	dspReq := &models.DspReqStu{
		Device: models.DspReqDeviceStu{
			Oaid: "a34d81a9-182c-4e89-8a0f-f371857ff6fc",
		},
	}

	userId := uuid.New().String()
	ctx := context.Background()
	IsDangHangHaiOK(ctx, dspReq, channel_id, posId, taskId, userId, nil)
}
