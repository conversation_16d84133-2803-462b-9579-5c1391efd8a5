// 1.2 聚媒通义

package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"fmt"
	"io"
	l "log"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

const (
	ocpxJuMeiTYURL = "http://clk2.jmedium.cn/s2s_wkhb/track"
)

// JumeiTongyiClkReport ocpx
func JumeiTongYiClkReport(c *gin.Context, log url.Values, source string) {
	// 获取计划信息
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, log.Get("plan_id"))
	if planInfo == nil {
		return
	}

	// 从log获取必要参数
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	caid := log.Get("caid")
	caidMd5 := log.Get("caid_md5")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	oaidMd5 := log.Get("oaid_md5")
	androidID := log.Get("android_id")

	// 构建上报URL
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxJuMeiTYURL, nil)
	q := requestGet.URL.Query()

	// 添加必填参数
	q.Add("request_id", uuid.NewV4().String())
	q.Add("ip", c.ClientIP())
	q.Add("ua", c.GetHeader("User-Agent"))
	q.Add("model", log.Get("model"))
	q.Add("osversion", log.Get("osv"))
	q.Add("brand", log.Get("manufacturer"))
	q.Add("ts", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()))

	// 根据设备类型添加设备标识
	// ios
	if idfa != "" {
		q.Add("idfa", strings.ToUpper(idfa))
	}
	if idfaMd5 != "" {
		q.Add("idfa_md5", strings.ToLower(idfaMd5))
	}

	// android
	if imei != "" {
		q.Add("imei", imei)
	}
	if imeiMd5 != "" {
		q.Add("imei_md5", strings.ToLower(imeiMd5))
	}
	if androidID != "" {
		q.Add("android_id", androidID)
	}

	if caid != "" {
		q.Add("caid", url.QueryEscape(caid))
	}
	if caidMd5 != "" {
		q.Add("caid_md5", caidMd5)
	}

	if oaid != "" {
		q.Add("oaid", oaid)
	}
	if oaidMd5 != "" {
		q.Add("oaid_md5", strings.ToLower(oaidMd5))
	}

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeJuMeiTongYiParams(planInfo, log, source)
	activateParams.Add("log", encodeParams)

	callback := fmt.Sprintf("%v?%v", config.ExternalJuMeiTongYiActiveURL, activateParams.Encode())
	q.Add("callback", callback)

	// 发送点击上报请求
	// requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.URL.RawQuery = q.Encode()
	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("jumei_tongyi clk get request failed, err:[%v]]\n", err.Error())
	}
	if resp == nil {
		l.Println("jumei_tongyi clk resp is nil")
		return
	}
	defer resp.Body.Close()
	bodyContent, _ := io.ReadAll(resp.Body)
	l.Println("jumei_tongyi ocpx resp: " + string(bodyContent))
}

func EncodeJuMeiTongYiParams(planInfo *models.DspPlanStu, log url.Values, source string) string {
	activateParams := url.Values{}
	activateParams.Add("uid", log.Get("uid"))
	activateParams.Add("plan_id", log.Get("plan_id"))
	activateParams.Add("market_type", log.Get("market_type"))
	activateParams.Add("ads_type", log.Get("ads_type"))
	activateParams.Add("ext_dsp_channel", log.Get("ext_dsp_channel"))
	activateParams.Add("media_channel", log.Get("media_channel"))
	activateParams.Add("sub_channel_id", log.Get("sub_channel_id"))
	activateParams.Add("ext_adx", log.Get("ext_adx"))
	activateParams.Add("os", log.Get("os"))
	activateParams.Add("osv", log.Get("osv"))

	activateParams.Add("did_md5", log.Get("did_md5"))
	if len(log.Get("imei")) > 0 {
		activateParams.Add("imei", log.Get("imei"))
	}
	if len(log.Get("imei_md5")) > 0 {
		activateParams.Add("imei_md5", log.Get("imei_md5"))
	}
	if len(log.Get("android_id")) > 0 {
		activateParams.Add("android_id", log.Get("android_id"))
	}
	if len(log.Get("android_id_md5")) > 0 {
		activateParams.Add("android_id_md5", log.Get("android_id_md5"))
	}
	if len(log.Get("idfa")) > 0 {
		activateParams.Add("idfa", log.Get("idfa"))
	}
	if len(log.Get("idfa_md5")) > 0 {
		activateParams.Add("idfa_md5", log.Get("idfa_md5"))
	}
	if len(log.Get("oaid")) > 0 {
		activateParams.Add("oaid", log.Get("oaid"))
	}
	if len(log.Get("oaid_md5")) > 0 {
		activateParams.Add("oaid_md5", log.Get("oaid_md5"))
	}
	if len(strings.TrimSpace(log.Get("model"))) > 0 {
		activateParams.Add("model", strings.TrimSpace(log.Get("model")))
	}
	if len(strings.TrimSpace(log.Get("manufacturer"))) > 0 {
		activateParams.Add("manufacturer", strings.TrimSpace(log.Get("manufacturer")))
	}
	if len(log.Get("caid_multi")) > 0 {
		activateParams.Add("caid_multi", log.Get("caid_multi"))
	}
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
		} else if planInfo.UnitPriceType == 1 {
		} else if planInfo.UnitPriceType == 2 {
			activateParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}

	// if planInfo.IsOCPX == 1 {
	// 	var acceptedTransformType []string
	// 	if strings.Contains(planInfo.KuaiShouOCPXPromotionTypesJson, "kwai_laxin") {
	// 		acceptedTransformType = append(acceptedTransformType, "1")
	// 	}
	// 	if strings.Contains(planInfo.KuaiShouOCPXPromotionTypesJson, "kwai_lite_laxin") {
	// 		acceptedTransformType = append(acceptedTransformType, "1")
	// 	}
	// 	if strings.Contains(planInfo.KuaiShouOCPXPromotionTypesJson, "kwai_lahui") {
	// 		acceptedTransformType = append(acceptedTransformType, "4")
	// 	}
	// 	if strings.Contains(planInfo.KuaiShouOCPXPromotionTypesJson, "kwai_lite_lahui") {
	// 		acceptedTransformType = append(acceptedTransformType, "4")
	// 	}
	// 	activateParams.Add("accepted_transform_type", strings.Join(acceptedTransformType, ","))
	// } else {
	// 	activateParams.Add("accepted_transform_type", "1,4")
	// }

	activateParams.Add("source", source)
	encodeStr, _ := utils.EncodeString([]byte(activateParams.Encode()))
	return encodeStr
}
