package core

import (
	"context"
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
)

// 测试微博点击上报功能
func TestWeiBoClkReport(t *testing.T) {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建一个测试用的HTTP请求和响应记录器
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// 创建一个测试请求
	req, _ := http.NewRequest("GET", "/api/clk", nil)
	c.Request = req

	// 构建log参数 - 使用用户提供的真实数据
	log := url.Values{}
	log.Add("uid", "0ef5419b-e83d-4f75-aabe-31472a39b0b8")
	log.Add("group_id", "fa234146-120a-49ad-9548-da67fc6e1cda")
	log.Add("plan_id", "f8944e25b2acfcb6")
	log.Add("market_type", "1")
	log.Add("ads_type", "1")
	log.Add("ext_dsp_channel", "12")
	log.Add("media_channel", "1")
	log.Add("ext_adx", "105")
	log.Add("os", "android")
	log.Add("did_md5", "b6d88266252df42d")
	log.Add("imei", "861234567890123")
	log.Add("imei_md5", "861234567890123")
	log.Add("oaid", "test_oaid")
	log.Add("oaid_md5", "test_oaid_md5")
	log.Add("ip", "*************")
	log.Add("ua", "")
	log.Add("model", "")
	log.Add("manufacturer", "")

	WeiBoClkReport(c, log, "clk")
}

func TestWeiBoClkReportFromAdx(t *testing.T) {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	c := context.Background()

	os := "ios"
	imei := ""
	imeiMd5 := ""
	oaid := ""
	oaidMd5 := ""
	idfa := "F7F65F18-E065-463C-B210-80404895D343"
	idfaMd5 := ""
	caidMultiStr := "[{\"caid\":\"1e19f125f7ecd76a0f5bde2a70073f9e\",\"caid_version\":\"20230330\"},{\"caid\":\"499f1f0a52204ae163faf63a31054681\",\"caid_version\":\"20220111\"}]"
	source := "clk"
	imeiMd5Str := ""

	planInfo := &models.DspPlanStu{
		IsOCPX: 1,
		PID:    "f8944e25b2acfcb6",
		OS:     "ios",
	}
	log := url.Values{}
	log.Add("uid", "0ef5419b-e83d-4f75-aabe-31472a39b0b8")
	log.Add("group_id", "fa234146-120a-49ad-9548-da67fc6e1cda")
	log.Add("plan_id", "f8944e25b2acfcb6")
	log.Add("market_type", "1")
	log.Add("ads_type", "1")
	log.Add("ext_dsp_channel", "12")
	log.Add("media_channel", "1")
	log.Add("ext_adx", "105")
	log.Add("os", "ios")
	log.Add("idfa", idfa)
	// log.Add("caid", "1e19f125f7ecd76a0f5bde2a70073f9e")
	// log.Add("caid_version", "20230330")
	log.Add("caid_multi", caidMultiStr)

	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = idfaMd5
	}

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeWeiBoParams(planInfo, log, source)
	activateParams.Add("log", encodeParams)
	activateParams.Add("event", "__EVENT__")

	callback := config.ExternalWeiBoActiveURL + "?" + activateParams.Encode()
	logger.GetSugaredLogger().Infof("weibo ocpx callback: %v\n\n", callback)

	ocpxURL := "https://ocpx.sina.cn/track/zybang"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()

	q.Add("os", string(os))
	if os == "android" {
		if len(imeiMd5Str) > 0 {
			q.Add("imei_md5", string(imeiMd5Str))
		}
		if len(oaid) > 0 {
			q.Add("oaid_md5", utils.GetMd5(oaid))
		} else if len(oaidMd5) > 0 {
			q.Add("oaid_md5", oaidMd5)
		}
	} else if os == "ios" {
		if len(idfaMd5Str) > 0 {
			q.Add("idfa_md5", idfaMd5Str)
		}

		// caid multi
		if len(caidMultiStr) > 0 {
			var caidMulti []models.DspReqDeviceCAIDMulti
			json.Unmarshal([]byte(caidMultiStr), &caidMulti)

			var tmpCAIDArray []WeiBoCAIDStu
			for _, item := range caidMulti {
				var tmpCAID WeiBoCAIDStu
				tmpCAID.CAID = item.CAID
				tmpCAID.CAIDVersion = item.CAIDVersion

				tmpCAIDArray = append(tmpCAIDArray, tmpCAID)
			}
			if len(tmpCAIDArray) > 0 {
				jsonData, _ := json.Marshal(tmpCAIDArray)
				q.Add("caid", string(jsonData))
			}
		}
	}

	q.Add("ts", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()))
	q.Add("callback", callback)
	requestGet.URL.RawQuery = q.Encode()

	resp, err := client.Do(requestGet)
	if resp == nil {
		logger.GetSugaredLogger().Infof("weibo ocpx get request error, callback:%s, url:%v, err:%v", callback, requestGet.URL.String(), err)
		return
	}

	logger.GetSugaredLogger().Infof("weibo ocpx get request, callback:%s, url:%v, status=%v, err:%v", callback, requestGet.URL.String(), resp.StatusCode, err)
	defer resp.Body.Close()
	bodyContent, _ := io.ReadAll(resp.Body)
	logger.GetSugaredLogger().Infof("weibo ocpx resp: %v", string(bodyContent))

}
