package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	adCommonCore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

// DeviceInfo 设备信息结构体
type DeviceInfo struct {
	OS        string
	Imei      string
	ImeiMd5   string
	Oaid      string
	OaidMd5   string
	Idfa      string
	IdfaMd5   string
	AndroidID string
	Mac       string
}

// 创建模拟的计划信息用于测试
func createMockPlanInfo() *models.DspPlanStu {
	planInfo := &models.DspPlanStu{
		PID:                "test_pid_123",
		GID:                "test_gid_456",
		PName:              "测试计划",
		OS:                 "android",
		PromotionType:      1,
		GroupMarketingType: "1",
		DownloadLink:       "https://example.com/app",
		IsOCPX:             1,
		Creatives: []models.DspCreativeStu{
			{
				CRID:         "test_creative_001",
				CreativeType: 1,
				Title:        "测试创意",
			},
		},
	}
	return planInfo
}

// buildAlipayBaseParams 构建支付宝基础参数
func buildAlipayBaseParams(source string) url.Values {
	q := url.Values{}
	q.Add("action", source)                   // ⾏为类型,expose曝光,click点击
	q.Add("requestFrom", "gerui")             // 来源渠道,由⽀付宝定义并配置在⽀付宝
	q.Add("pid", "****************")          // 服务商, ⼊驻⽀付宝开放平台分配
	q.Add("partnerId", "tab3geruiapkgerui31") // 服务商⼆级渠道, 服务商⾃定义的⼆级渠道，⼴点通的值为accountid
	q.Add("accountid", "gerui0710")           // ⼴告账户ID
	q.Add("benefit", "HK280143")              // 权益/钩⼦i
	return q
}

// addDeviceInfoToParams 添加设备信息到参数中
func addDeviceInfoToParams(q url.Values, deviceInfo DeviceInfo) bool {
	reqOs := "3"
	hasDeviceInfo := false

	if deviceInfo.OS == "android" {
		reqOs = "0"
		if len(deviceInfo.Imei) > 0 {
			q.Add("imei", utils.GetMd5(deviceInfo.Imei))
			hasDeviceInfo = true
		} else if len(deviceInfo.ImeiMd5) > 0 {
			q.Add("imei", deviceInfo.ImeiMd5)
			hasDeviceInfo = true
		}

		if len(deviceInfo.Oaid) > 0 {
			q.Add("oaid", deviceInfo.Oaid)
			q.Add("oaidmd5", utils.GetMd5(deviceInfo.Oaid))
			hasDeviceInfo = true
		} else if len(deviceInfo.OaidMd5) > 0 {
			q.Add("oaidmd5", deviceInfo.OaidMd5)
			hasDeviceInfo = true
		}

		if len(deviceInfo.AndroidID) > 0 {
			q.Add("androidid", utils.GetMd5(deviceInfo.AndroidID))
			hasDeviceInfo = true
		}
	} else if deviceInfo.OS == "ios" {
		reqOs = "1"
		if len(deviceInfo.Idfa) > 0 {
			q.Add("idfa", deviceInfo.Idfa)
			q.Add("idfamd5", utils.GetMd5(deviceInfo.Idfa))
			hasDeviceInfo = true
		} else if len(deviceInfo.IdfaMd5) > 0 {
			q.Add("idfamd5", deviceInfo.IdfaMd5)
			hasDeviceInfo = true
		}
	}

	if len(deviceInfo.Mac) > 0 {
		q.Add("mac", utils.GetMd5(strings.ToUpper(strings.ReplaceAll(deviceInfo.Mac, ":", ""))))
	}

	q.Add("os", reqOs)
	return hasDeviceInfo
}

// sendAlipayRequest 发送支付宝请求
func sendAlipayRequest(c *gin.Context, q url.Values, source string, beginTime time.Time) {
	ocpxURL := "https://ugapi.alipay.com/monitor"
	httpResty := adCommonCore.GetHTTPClientDebug(true)
	bodyContent, statusCode, err := httpResty.DoWithTimeout(
		c,
		600*time.Millisecond,
		http.MethodGet,
		ocpxURL,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/json; charset=utf-8"}),
		utilities.WithQueryEx(q),
	)

	logger.GetSugaredLogger().Infof("alipay report source=%v, rsp=%v, statusCode=%v, err=%v, time=%v", source, string(bodyContent), statusCode, err, time.Since(beginTime))
}

// buildOCPXPromotionTypes 构建OCPX推广类型
func buildOCPXPromotionTypes(planInfo *models.DspPlanStu, params url.Values) {
	if planInfo.IsOCPX == 1 && len(planInfo.OCPXPromotionTypesJson) > 0 {
		var tmpOCPCPromotionTypes []models.DspOCPXPromotionTypeStu
		json.Unmarshal([]byte(planInfo.OCPXPromotionTypesJson), &tmpOCPCPromotionTypes)

		var tmpArray []string
		for _, item := range tmpOCPCPromotionTypes {
			tmpArray = append(tmpArray, item.OCPXPromotionType)
		}
		params.Add("accepted_transform_type", strings.Join(tmpArray, ","))
	}
}

// AlipayReport 支付宝曝光上报
func AlipayReport(c *gin.Context, log url.Values, source string) {
	beginTime := time.Now()
	pid := log.Get("plan_id")
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, pid)
	logger.GetSugaredLogger().Infof("alipay report1 start, plan_id: %s, source: %s", pid, source)
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("alipay report1 plan info not found, plan_id: %s, source: %s", pid, source)
		return
	}

	if planInfo.IsOCPX != 1 {
		logger.GetSugaredLogger().Errorf("alipay report1 plan is not ocpx, plan_id: %s, source: %s", pid, source)
		// return
	}

	// 构建基础参数
	q := buildAlipayBaseParams(source)

	// 构建设备信息
	deviceInfo := DeviceInfo{
		OS:        log.Get("os"),
		Imei:      log.Get("imei"),
		ImeiMd5:   log.Get("imei_md5"),
		Oaid:      log.Get("oaid"),
		OaidMd5:   log.Get("oaid_md5"),
		Idfa:      log.Get("idfa"),
		IdfaMd5:   log.Get("idfa_md5"),
		AndroidID: log.Get("android_id"),
		Mac:       log.Get("mac"),
	}

	// 添加设备信息到参数中
	hasDeviceInfo := addDeviceInfoToParams(q, deviceInfo)
	if !hasDeviceInfo {
		logger.GetSugaredLogger().Errorf("alipay report1 missing device info, plan_id: %s, source: %s", pid, source)
		return
	}

	// 添加其他必要参数
	ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	q.Add("ip", ip)
	// q.Add("ua", ua)
	q.Add("timestamp", timestamp)

	// 构建回调URL
	activateParams := url.Values{}
	encodeParams := EncodeAlipayParams(planInfo, log, source)
	activateParams.Add("log", encodeParams)
	// activateParams.Add("transformtype", "__TRANSFORMTYPE__")
	callback := config.ExternalAlipayActiveURL + "?" + activateParams.Encode()
	q.Add("callback", callback)

	logger.GetSugaredLogger().Infof("alipay report1 plan_id=%v, source=%v, callback=%v", pid, source, callback)

	// 构建OCPX推广类型
	buildOCPXPromotionTypes(planInfo, q)

	// 发送请求
	sendAlipayRequest(c, q, source, beginTime)
}

func EncodeAlipayParams(planInfo *models.DspPlanStu, log url.Values, source string) string {
	activateParams := url.Values{}

	// 基础参数
	baseFields := []string{"uid", "group_id", "plan_id", "market_type", "ads_type", "ext_dsp_channel", "media_channel", "sub_channel_id", "ext_adx", "os", "osv", "did_md5"}
	for _, field := range baseFields {
		activateParams.Add(field, log.Get(field))
	}

	// 设备信息（可选字段）
	optionalFields := []string{"imei", "imei_md5", "android_id", "android_id_md5", "idfa", "idfa_md5", "oaid", "oaid_md5", "caid_multi"}
	for _, field := range optionalFields {
		if value := log.Get(field); len(value) > 0 {
			activateParams.Add(field, value)
		}
	}

	// 设备型号信息（需要去除空格）
	if model := strings.TrimSpace(log.Get("model")); len(model) > 0 {
		activateParams.Add("model", model)
	}
	if manufacturer := strings.TrimSpace(log.Get("manufacturer")); len(manufacturer) > 0 {
		activateParams.Add("manufacturer", manufacturer)
	}

	// CPA价格
	if planInfo.UnitPriceNum > 0 && planInfo.UnitPriceType == 2 {
		activateParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
	}

	// OCPX推广类型
	if planInfo.IsOCPX == 1 && len(planInfo.OCPXPromotionTypesJson) > 0 {
		var tmpOCPCPromotionTypes []models.DspOCPXPromotionTypeStu
		json.Unmarshal([]byte(planInfo.OCPXPromotionTypesJson), &tmpOCPCPromotionTypes)

		var tmpArray []string
		for _, item := range tmpOCPCPromotionTypes {
			tmpArray = append(tmpArray, item.OCPXPromotionType)
		}
		activateParams.Add("accepted_transform_type", strings.Join(tmpArray, ","))
	}

	activateParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(activateParams.Encode()))
	return url.QueryEscape(encodeStr)
}

// AlipayClkReportFromAdx ocpx
func AlipayClkReportFromAdx(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) {
	logger.GetSugaredLogger().Infof("alipay report2 start, source=%v", source)
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("alipay report2 plan info not found, source=%v", source)
		return
	}

	pid := planInfo.PID
	if planInfo.IsOCPX != 1 {
		logger.GetSugaredLogger().Errorf("alipay report2 plan is not ocpx, plan_id=%v, source=%v", pid, source)
		return
	}

	beginTime := time.Now()

	// 构建基础参数
	q := buildAlipayBaseParams(source)

	// 构建设备信息
	deviceInfo := DeviceInfo{
		OS:      mhCpaReq.Os,
		Imei:    mhCpaReq.Imei,
		ImeiMd5: mhCpaReq.ImeiMd5,
		Oaid:    mhCpaReq.Oaid,
		OaidMd5: mhCpaReq.OaidMd5,
		Idfa:    mhCpaReq.Idfa,
		IdfaMd5: mhCpaReq.IdfaMd5,
	}

	// 添加设备信息到参数中
	hasDeviceInfo := addDeviceInfoToParams(q, deviceInfo)
	if !hasDeviceInfo {
		logger.GetSugaredLogger().Errorf("alipay report2 missing device info, plan_id: %s, source: %s", pid, source)
		return
	}

	// 添加其他必要参数
	ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	q.Add("ip", ip)
	// q.Add("ua", ua)
	q.Add("timestamp", timestamp)

	// 构建回调URL
	activateParams := url.Values{}
	encodeParams := EncodeAlipayParamsFromAdx(planInfo, mhCpaReq, source)
	activateParams.Add("log", encodeParams)
	// activateParams.Add("transformtype", "__TRANSFORMTYPE__")
	callback := config.ExternalAlipayActiveURL + "?" + activateParams.Encode()
	q.Add("callback", callback)

	logger.GetSugaredLogger().Infof("alipay report2 plan_id=%v, source=%v, callback=%v", pid, source, callback)
	// 构建OCPX推广类型
	buildOCPXPromotionTypes(planInfo, q)

	// 发送请求
	sendAlipayRequest(c, q, source, beginTime)
}

func EncodeAlipayParamsFromAdx(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) string {
	bigdataParams := url.Values{}

	// 基础参数
	bigdataParams.Add("uid", mhCpaReq.UID)
	bigdataParams.Add("group_id", planInfo.GID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "1")
	bigdataParams.Add("ext_adx", mhCpaReq.Channel)
	bigdataParams.Add("os", mhCpaReq.Os)
	bigdataParams.Add("ip", mhCpaReq.IP)

	// 价格信息
	if planInfo.UnitPriceNum > 0 {
		priceFields := map[int]string{0: "cpm_price", 1: "cpc_price", 2: "cpa_price"}
		if field, exists := priceFields[planInfo.UnitPriceType]; exists {
			bigdataParams.Add(field, utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	if planInfo.SspPriceNum > 0 {
		sspPriceFields := map[int]string{0: "ext_cpm_price", 1: "ext_cpc_price"}
		if field, exists := sspPriceFields[planInfo.SspPriceType]; exists {
			bigdataParams.Add(field, utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		}
	}

	// 设备信息（可选字段）
	deviceFields := map[string]string{
		"imei": mhCpaReq.Imei, "imei_md5": mhCpaReq.ImeiMd5,
		"oaid": mhCpaReq.Oaid, "oaid_md5": mhCpaReq.OaidMd5,
		"idfa": mhCpaReq.Idfa, "idfa_md5": mhCpaReq.IdfaMd5,
		"osv": mhCpaReq.Osv, "did_md5": mhCpaReq.DIDMd5,
	}
	for field, value := range deviceFields {
		if len(value) > 0 {
			bigdataParams.Add(field, value)
		}
	}

	// OCPX推广类型
	if planInfo.IsOCPX == 1 && len(planInfo.OCPXPromotionTypesJson) > 0 {
		var tmpOCPCPromotionTypes []models.DspOCPXPromotionTypeStu
		json.Unmarshal([]byte(planInfo.OCPXPromotionTypesJson), &tmpOCPCPromotionTypes)

		var tmpArray []string
		for _, item := range tmpOCPCPromotionTypes {
			tmpArray = append(tmpArray, item.OCPXPromotionType)
		}
		bigdataParams.Add("accepted_transform_type", strings.Join(tmpArray, ","))
	}

	bigdataParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	return url.QueryEscape(encodeStr)
}
