package core

import (
	"context"
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"io/ioutil"
	"log"
	l "log"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	adCommonCore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/gin-gonic/gin"
)

// 文档：https://open.taobao.com/api.htm?docId=48588&docType=2
// IsDangHangHaiOK ...
func IsDangHangHaiOK(c context.Context, dspReq *models.DspReqStu, dhhChannelID string, dhhPosID string, dhhTaskID string, bigdataUID string, planInfo *models.DspPlanStu) bool {
	// log.Println("dahanghai")

	// t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
	// // nextUnixTime := t.Unix() + 2
	// // log.Println("next unix time: ", nextUnixTime)
	// leftUnixTime := t.Unix() + 2 - time.Now().Unix()
	// // log.Println("left unix time: ", leftUnixTime)

	// // 单日dhh限制
	// tmpRedisKey := "max_dhh_code_7_" + time.Now().Format("2006-01-02")
	// redisValue, redisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()

	// if redisErr != nil {
	// 	// log.Println("redis error:", redisErr)
	// } else {
	// 	log.Println("redis value:", redisValue)
	// 	if utils.ConvertStringToInt(redisValue) > 99 {
	// 		return false
	// 	}
	// }

	appKey := "32087138"
	appSecret := "dfc566de86f98f4013f7307de0f856be"
	client := adCommonCore.GetFastHTTPClientDebug(true)

	// timestamp
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	signMap := map[string]interface{}{
		"app_key":              appKey,
		"format":               "json",
		"method":               "taobao.usergrowth.dhh.delivery.ask",
		"sign_method":          "md5",
		"timestamp":            timestamp,
		"v":                    "2.0",
		"advertising_space_id": dhhPosID,
		"channel":              dhhChannelID,
	}
	if len(dspReq.Device.Imei) > 0 {
		signMap["imei_md5"] = utils.GetMd5(dspReq.Device.Imei)
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		signMap["imei_md5"] = strings.ToLower(dspReq.Device.ImeiMd5)
	}
	if len(dspReq.Device.Oaid) > 0 {
		signMap["oaid_md5"] = utils.GetMd5(dspReq.Device.Oaid)
	}
	if len(dspReq.Device.OaidMd5) > 0 {
		signMap["oaid_md5"] = strings.ToLower(dspReq.Device.OaidMd5)
	}
	if len(dspReq.Device.Idfa) > 0 {
		signMap["idfa_md5"] = utils.GetMd5(dspReq.Device.Idfa)
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		signMap["idfa_md5"] = strings.ToLower(dspReq.Device.IdfaMd5)
	}

	// sign
	var signKeys []string
	for k := range signMap {
		signKeys = append(signKeys, k)
	}
	sort.Strings(signKeys)

	sign := appSecret
	for _, k := range signKeys {
		// log.Println("Key:", k, ", Value:", postData[k])
		sign = sign + k + signMap[k].(string)
	}
	sign = sign + appSecret
	signValue := strings.ToUpper(utils.GetMd5(sign))

	queryParams := url.Values{}
	queryParams.Add("app_key", appKey)
	queryParams.Add("format", "json")
	queryParams.Add("method", "taobao.usergrowth.dhh.delivery.ask")
	queryParams.Add("sign_method", "md5")
	queryParams.Add("sign", signValue)
	queryParams.Add("timestamp", timestamp)
	queryParams.Add("v", "2.0")

	dhhURL := "http://gw.api.taobao.com/router/rest" + "?" + queryParams.Encode()

	formData := make(map[string]string)
	formData["advertising_space_id"] = dhhPosID
	formData["channel"] = dhhChannelID
	if len(dspReq.Device.Imei) > 0 {
		formData["imei_md5"] = utils.GetMd5(dspReq.Device.Imei)
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		formData["imei_md5"] = strings.ToLower(dspReq.Device.ImeiMd5)
	}
	if len(dspReq.Device.Oaid) > 0 {
		formData["oaid_md5"] = utils.GetMd5(dspReq.Device.Oaid)
	}
	if len(dspReq.Device.OaidMd5) > 0 {
		formData["oaid_md5"] = strings.ToLower(dspReq.Device.OaidMd5)
	}

	if len(dspReq.Device.Idfa) > 0 {
		formData["idfa_md5"] = utils.GetMd5(dspReq.Device.Idfa)
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		formData["idfa_md5"] = strings.ToLower(dspReq.Device.IdfaMd5)
	}

	// 使用 adCommonCore 的 HTTP 客户端发送请求
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}

	bodyContent, statusCode, err := client.DoWithTimeout(
		c,
		500*time.Millisecond,
		http.MethodPost,
		dhhURL,
		utilities.WithHeaders(headers),
		utilities.WithFormData(formData),
	)
	if err != nil {
		l.Printf("dhh get request failed, err:[%s]", err.Error())
		return false
	}

	log.Printf("dhh resp result code=%v, rspData=%s\n", statusCode, string(bodyContent))

	dhhRespStu := DaHangHaiUserGrowth{}
	json.Unmarshal([]byte(bodyContent), &dhhRespStu)
	if dhhRespStu.BHHResp.Result && len(dhhRespStu.BHHResp.TaskIDList.TaskIDs) > 0 {
		for _, taskItem := range dhhRespStu.BHHResp.TaskIDList.TaskIDs {
			if taskItem == dhhTaskID {
				l.Println("dhh ok task id: ", dhhTaskID)
				return true
			}
		}
	}

	return false
}

// IsDangHangHaiGroupOK ...
func IsDangHangHaiGroupOK(c *gin.Context, dspReq *models.DspReqStu, dhhChannelID string, dhhPosID string) (bool, []string) {
	// log.Println("dahanghai")

	// t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
	// // nextUnixTime := t.Unix() + 2
	// // log.Println("next unix time: ", nextUnixTime)
	// leftUnixTime := t.Unix() + 2 - time.Now().Unix()
	// // log.Println("left unix time: ", leftUnixTime)

	// // 单日dhh限制
	// tmpRedisKey := "max_dhh_code_7_" + time.Now().Format("2006-01-02")
	// redisValue, redisErr := db.GlbRedis.Get(c, tmpRedisKey).Result()

	// if redisErr != nil {
	// 	// log.Println("redis error:", redisErr)
	// } else {
	// 	log.Println("redis value:", redisValue)
	// 	if utils.ConvertStringToInt(redisValue) > 99 {
	// 		return false
	// 	}
	// }

	appKey := "32087138"
	appSecret := "dfc566de86f98f4013f7307de0f856be"
	client := &http.Client{Timeout: time.Duration(500) * time.Millisecond}

	// timestamp
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	signMap := map[string]interface{}{
		"app_key":              appKey,
		"format":               "json",
		"method":               "taobao.usergrowth.dhh.delivery.ask",
		"sign_method":          "md5",
		"timestamp":            timestamp,
		"v":                    "2.0",
		"advertising_space_id": dhhPosID,
		"channel":              dhhChannelID,
	}
	if len(dspReq.Device.Imei) > 0 {
		signMap["imei_md5"] = utils.GetMd5(dspReq.Device.Imei)
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		signMap["imei_md5"] = strings.ToLower(dspReq.Device.ImeiMd5)
	}
	if len(dspReq.Device.Oaid) > 0 {
		signMap["oaid_md5"] = utils.GetMd5(dspReq.Device.Oaid)
	}
	if len(dspReq.Device.Idfa) > 0 {
		signMap["idfa_md5"] = utils.GetMd5(dspReq.Device.Idfa)
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		signMap["idfa_md5"] = strings.ToLower(dspReq.Device.IdfaMd5)
	}

	// sign
	var signKeys []string
	for k := range signMap {
		signKeys = append(signKeys, k)
	}
	sort.Strings(signKeys)

	sign := appSecret
	for _, k := range signKeys {
		// l.Println("Key:", k, ", Value:", postData[k])
		sign = sign + k + signMap[k].(string)
	}
	sign = sign + appSecret
	signValue := strings.ToUpper(utils.GetMd5(sign))

	queryParams := url.Values{}
	queryParams.Add("app_key", appKey)
	queryParams.Add("format", "json")
	queryParams.Add("method", "taobao.usergrowth.dhh.delivery.ask")
	queryParams.Add("sign_method", "md5")
	queryParams.Add("sign", signValue)
	queryParams.Add("timestamp", timestamp)
	queryParams.Add("v", "2.0")

	dhhURL := "http://gw.api.taobao.com/router/rest" + "?" + queryParams.Encode()

	bodyParams := url.Values{}
	bodyParams.Add("advertising_space_id", dhhPosID)
	bodyParams.Add("channel", dhhChannelID)
	if len(dspReq.Device.Imei) > 0 {
		bodyParams.Add("imei_md5", utils.GetMd5(dspReq.Device.Imei))
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		bodyParams.Add("imei_md5", strings.ToLower(dspReq.Device.ImeiMd5))
	}
	if len(dspReq.Device.Oaid) > 0 {
		bodyParams.Add("oaid_md5", utils.GetMd5(dspReq.Device.Oaid))
	}
	if len(dspReq.Device.Idfa) > 0 {
		bodyParams.Add("idfa_md5", utils.GetMd5(dspReq.Device.Idfa))
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		bodyParams.Add("idfa_md5", strings.ToLower(dspReq.Device.IdfaMd5))
	}

	// l.Println("dhh req url: ", dhhURL)
	// l.Println("dhh req body: ", bodyParams.Encode())

	requestGet, _ := http.NewRequest("POST", dhhURL, strings.NewReader(bodyParams.Encode()))
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")
	// log.Println(requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("dhh resp is nil")
		return false, nil
	}
	defer resp.Body.Close()
	bodyContent, err := ioutil.ReadAll(resp.Body)
	// log.Printf("resp status code:[%d]\n", resp.StatusCode)
	// log.Printf("resp body data:[%s]\n", string(bodyContent))
	// log.Println(resp.StatusCode)
	// log.Println("dhh resp: ", string(bodyContent))

	dhhRespStu := DaHangHaiUserGrowth{}
	json.Unmarshal([]byte(bodyContent), &dhhRespStu)
	// log.Println(bhhRespStu)
	// log.Println(bhhRespStu.BHHResp.Result)
	// log.Println(bhhRespStu.BHHResp.ErrCode)
	// log.Println(bhhRespStu.BHHResp.TaskID)
	if dhhRespStu.BHHResp.Result == true {
		if len(dhhRespStu.BHHResp.TaskIDList.TaskIDs) > 0 {
			return true, dhhRespStu.BHHResp.TaskIDList.TaskIDs
		}
	}

	// if dhhRespStu.BHHErrResp.Code == 7 {
	// 	if redisErr != nil {
	// 		// log.Println("redis error:", redisErr)
	// 		redisErr = db.GlbRedis.Set(c, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second).Err()
	// 	} else {
	// 		// log.Println("redis value:", redisValue)
	// 		redisErr = db.GlbRedis.Set(c, tmpRedisKey, 1+utils.ConvertStringToInt(redisValue), time.Duration(leftUnixTime)*time.Second).Err()
	// 	}
	// }

	return false, nil
}

// DhhExpReport ...
func DhhExpReport(c *gin.Context, log url.Values) {
	// uid
	// uid := log.Get("uid")

	// app id
	// localAppID := c.Query("app_id")

	// pos id
	// localPosID := log.Get("pos_id")

	// p_app_id
	// pAppID := log.Get("p_app_id")

	// p_pos_id
	// pPosID := log.Get("p_pos_id")

	// os := log.Get("os")
	// osv := log.Get("osv")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	// log.Println("dhh local app id: " + localAppID)
	// log.Println("dhh local pos id: " + localPosID)
	// log.Println("dhh p app id: " + pAppID)
	// log.Println("dhh p pos id: " + pPosID)

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	oaidMd5Str := ""
	if len(oaid) > 0 {
		oaidMd5Str = utils.GetMd5(oaid)
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = idfaMd5
	}

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	dhhExpURL := "https://wcp.taobao.com/adstrack/track.json?action=1"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", dhhExpURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()
	q.Add("channel", log.Get("dhh_channel_id"))
	q.Add("advertisingSpaceId", log.Get("dhh_pos_id"))
	q.Add("taskId", log.Get("dhh_task_id"))
	if len(imeiMd5Str) > 0 {
		q.Add("imeiMd5", string(imeiMd5Str))
	}
	if len(oaidMd5Str) > 0 {
		q.Add("oaidMd5", string(oaidMd5Str))
	}
	if len(idfaMd5Str) > 0 {
		q.Add("idfaMd5", string(idfaMd5Str))
	}
	requestGet.URL.RawQuery = q.Encode()

	// l.Println("dhh exp req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("dhh exp get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("dhh exp resp is nil")
		return
	}
	defer resp.Body.Close()
	// bodyContent, err := ioutil.ReadAll(resp.Body)
	// log.Printf("resp status code:[%d]\n", resp.StatusCode)
	// log.Printf("resp body data:[%s]\n", string(bodyContent))
	// log.Println("dhh exp resp: " + string(bodyContent))
	// dhhResp := ""
	// json.Unmarshal([]byte(bodyContent), &dhhResp)

	// if dhhResp == "success" {
	// 	models.DhhReportToMysql(c, uid, localAppID, localPosID, pAppID, pPosID, ip, osv, os, "",
	// 		model, manufacturer, imei, imeiMd5, oaid, oaidMd5Str, idfa, idfaMd5, ua, "dhh", requestGet.URL.String(), string(bodyContent))
	// }
}

// DhhClkReport ...
func DhhClkReport(c *gin.Context, log url.Values) {
	// uid
	// uid := log.Get("uid")

	// app id
	// localAppID := c.Query("app_id")

	// pos id
	// localPosID := log.Get("pos_id")

	// p_app_id
	// pAppID := log.Get("p_app_id")

	// p_pos_id
	// pPosID := log.Get("p_pos_id")

	// os := log.Get("os")
	// osv := log.Get("osv")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	// localAppName := log.Get("app_name")
	// localAppBundle := log.Get("app_bundle")
	// androidID := log.Get("android_id")
	// androidIDMd5 := log.Get("android_id_md5")

	// log.Println("dhh local app id: " + localAppID)
	// log.Println("dhh local pos id: " + localPosID)
	// log.Println("dhh p app id: " + pAppID)
	// log.Println("dhh p pos id: " + pPosID)

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	oaidMd5Str := ""
	if len(oaid) > 0 {
		oaidMd5Str = utils.GetMd5(oaid)
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = idfaMd5
	}

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	// activateParams.Add("uid", uid)
	activateParams.Add("plan_id", log.Get("plan_id"))
	activateParams.Add("log", c.Query("log"))
	callback := config.ExternalDhhActiveURL + "?" + activateParams.Encode()

	dhhClkURL := "https://wcp.taobao.com/adstrack/track.json?action=2"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", dhhClkURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()
	q.Add("channel", log.Get("dhh_channel_id"))
	q.Add("advertisingSpaceId", log.Get("dhh_pos_id"))
	q.Add("taskId", log.Get("dhh_task_id"))
	if len(imeiMd5Str) > 0 {
		q.Add("imeiMd5", string(imeiMd5Str))
	}
	if len(oaidMd5Str) > 0 {
		q.Add("oaidMd5", string(oaidMd5Str))
	}
	if len(idfaMd5Str) > 0 {
		q.Add("idfaMd5", string(idfaMd5Str))
	}
	q.Add("callbackUrl", callback)
	requestGet.URL.RawQuery = q.Encode()

	// l.Println("dhh clk req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("dhh clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("dhh clk resp is nil")
		return
	}
	defer resp.Body.Close()
	// bodyContent, err := ioutil.ReadAll(resp.Body)
	// // log.Printf("resp status code:[%d]\n", resp.StatusCode)
	// // log.Printf("resp body data:[%s]\n", string(bodyContent))
	// log.Println("dhh clk resp: " + string(bodyContent))
	// dhhResp := ""
	// json.Unmarshal([]byte(bodyContent), &dhhResp)

	// if dhhResp == "success" {
	// 	// models.DhhReportToMysql(c, uid, localAppID, localPosID, pAppID, pPosID, ip, osv, os, "",
	// 	// 	model, manufacturer, imei, imeiMd5, oaid, oaidMd5Str, idfa, idfaMd5, ua, "dhh", requestGet.URL.String(), string(bodyContent))
	// 	// models.BigDataDHHClick(c, uid, localAppID, localPosID, pAppID, pPosID, localAppName, localAppBundle, ip, osv, os,
	// 	// 	model, manufacturer, imei, imeiMd5, androidID, androidIDMd5, oaid, idfa, idfaMd5, ua, requestGet.URL.String(), string(bodyContent))
	// }
}

// DhhClkReportFromAdx ...
func DhhClkReportFromAdx(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) {
	l.Println("dhh clk report from adx")

	imei := mhCpaReq.Imei
	imeiMd5 := mhCpaReq.ImeiMd5
	oaid := mhCpaReq.Oaid
	oaidMd5 := mhCpaReq.OaidMd5
	idfa := mhCpaReq.Idfa
	idfaMd5 := mhCpaReq.IdfaMd5

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	oaidMd5Str := ""
	if len(oaid) > 0 {
		oaidMd5Str = utils.GetMd5(oaid)
	} else if len(oaidMd5) > 0 {
		oaidMd5Str = strings.ToLower(oaidMd5)
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = strings.ToLower(idfaMd5)
	}

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	dhhParams := EncodeDhhParams(planInfo, mhCpaReq)
	activateParams.Add("log", dhhParams)
	callback := config.ExternalDhhActiveURL + "?" + activateParams.Encode()

	dhhClkURL := "https://wcp.taobao.com/adstrack/track.json?action=2"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", dhhClkURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()
	q.Add("channel", planInfo.DHHChannelID)
	q.Add("advertisingSpaceId", planInfo.DHHPosID)
	q.Add("taskId", planInfo.DhhTaskID)
	if len(imeiMd5Str) > 0 {
		q.Add("imeiMd5", string(imeiMd5Str))
	}
	if len(oaidMd5Str) > 0 {
		q.Add("oaidMd5", string(oaidMd5Str))
	}
	if len(idfaMd5Str) > 0 {
		q.Add("idfaMd5", string(idfaMd5Str))
	}
	q.Add("callbackUrl", callback)
	requestGet.URL.RawQuery = q.Encode()

	// l.Println("dhh clk req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("dhh clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Printf("dhh clk resp is nil")
		return
	}
	defer resp.Body.Close()
	// bodyContent, err := ioutil.ReadAll(resp.Body)
	// // log.Printf("resp status code:[%d]\n", resp.StatusCode)
	// // log.Printf("resp body data:[%s]\n", string(bodyContent))
	// log.Println("dhh clk resp: " + string(bodyContent))
	// dhhResp := ""
	// json.Unmarshal([]byte(bodyContent), &dhhResp)

	// if dhhResp == "success" {
	// 	// models.DhhReportToMysql(c, uid, localAppID, localPosID, pAppID, pPosID, ip, osv, os, "",
	// 	// 	model, manufacturer, imei, imeiMd5, oaid, oaidMd5Str, idfa, idfaMd5, ua, "dhh", requestGet.URL.String(), string(bodyContent))
	// 	// models.BigDataDHHClick(c, uid, localAppID, localPosID, pAppID, pPosID, localAppName, localAppBundle, ip, osv, os,
	// 	// 	model, manufacturer, imei, imeiMd5, androidID, androidIDMd5, oaid, idfa, idfaMd5, ua, requestGet.URL.String(), string(bodyContent))
	// }
}

// EncodeDhhParams ...
func EncodeDhhParams(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq) string {
	bigdataParams := url.Values{}
	bigdataParams.Add("uid", mhCpaReq.UID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "1")
	bigdataParams.Add("ext_adx", mhCpaReq.Channel)

	bigdataParams.Add("dhh_channel_id", planInfo.DHHChannelID)
	bigdataParams.Add("dhh_pos_id", planInfo.DHHPosID)
	bigdataParams.Add("dhh_task_id", planInfo.DhhTaskID)
	// unit_price_type: 0 CPM, 1 CPC, 2 CPA
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
			bigdataParams.Add("cpm_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 1 {
			bigdataParams.Add("cpc_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 2 {
			bigdataParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	// ssp_price_type: 0 CPM, 1 CPC
	if planInfo.SspPriceNum > 0 {
		if planInfo.SspPriceType == 0 {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		} else if planInfo.SspPriceType == 1 {
			bigdataParams.Add("ext_cpc_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		}
	}
	bigdataParams.Add("os", mhCpaReq.Os)
	if len(mhCpaReq.Imei) > 0 {
		bigdataParams.Add("imei", mhCpaReq.Imei)
	}
	if len(mhCpaReq.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", mhCpaReq.ImeiMd5)
	}
	if len(mhCpaReq.Oaid) > 0 {
		bigdataParams.Add("oaid", mhCpaReq.Oaid)
	}
	if len(mhCpaReq.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", mhCpaReq.OaidMd5)
	}
	if len(mhCpaReq.Idfa) > 0 {
		bigdataParams.Add("idfa", mhCpaReq.Idfa)
	}
	if len(mhCpaReq.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", mhCpaReq.IdfaMd5)
	}
	bigdataParams.Add("ip", mhCpaReq.IP)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

// dhh resp: {"usergrowth_dhh_delivery_ask_response":{"errcode":0,"result":true,"task_id_list":{"string":["32896","1163346009","1763656536"]},"request_id":"11kmhwbmq6uwy"}}

// DaHangHaiUserGrowth ...
type DaHangHaiUserGrowth struct {
	BHHResp    DaHangHaiUserGrowthResp    `json:"usergrowth_dhh_delivery_ask_response"`
	BHHErrResp DaHangHaiUserGrowthErrResp `json:"error_response"`
}

// DaHangHaiUserGrowthResp ...
type DaHangHaiUserGrowthResp struct {
	ErrCode    int                 `json:"errcode"`
	Result     bool                `json:"result"`
	TaskIDList DaHangHaiTaskIDList `json:"task_id_list"`
	RequestID  string              `json:"request_id"`
}

// DaHangHaiUserGrowthErrResp ...
type DaHangHaiUserGrowthErrResp struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	SubCode   string `json:"sub_code"`
	SubMsg    string `json:"sub_msg"`
	RequestID string `json:"request_id"`
}

// DaHangHaiTaskIDList ...
type DaHangHaiTaskIDList struct {
	TaskIDs []string `json:"string"`
}
