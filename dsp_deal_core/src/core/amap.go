package core

import (
	"bytes"
	"context"
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"io"
	l "log"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
)

// IsAmapOK ...
func IsAmapOK(c context.Context, bigdataUID string, dspReq *models.DspReqStu, planInfo *models.DspPlanStu) bool {
	// log.Println("amap")
	l.Println("amap source:", planInfo.AMapSource)
	l.Println("amap channel:", planInfo.AMapChannel)
	l.Println("amap rta ids:", planInfo.AMapRtaIDs)
	rtaAmapURL := "https://amap-rta-online.amap.com/third/potential_guest/" + planInfo.AMapChannel
	l.Println("amap req url:", rtaAmapURL)

	client := &http.Client{}

	osvMajor := 0
	if len(dspReq.Device.OsVersion) > 0 {
		osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
		osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// l.Println(osvMajor)
	}

	didType := ""
	didSecure := "NOMAL"
	dids := []string{}
	didMaps := []map[string]interface{}{}

	if dspReq.Device.Os == "android" {
		if osvMajor < 10 {
			if len(dspReq.Device.Imei) > 0 {
				didType = "IMEI"
				didSecure = "MD5"

				dids = append(dids, utils.GetMd5(dspReq.Device.Imei))
			} else if len(dspReq.Device.ImeiMd5) > 0 && dspReq.Device.ImeiMd5 != utils.GetMd5("") {
				didType = "IMEI"
				didSecure = "MD5"

				dids = append(dids, dspReq.Device.ImeiMd5)
			} else {
				return false
			}

		} else {
			if len(dspReq.Device.Oaid) > 0 {
				didType = "OAID"

				dids = append(dids, dspReq.Device.Oaid)
			} else {
				return false
			}
		}
	} else if dspReq.Device.Os == "ios" {
		// isCAIDOK := false
		// if len(dspReq.Device.CAID) > 0 && len(dspReq.Device.CAIDVersion) > 0 {
		// 	didType = "CAID"

		// 	if dspReq.Device.CAIDVersion == "20220111" {
		// 		isCAIDOK = true

		// 		caidMap := map[string]interface{}{}
		// 		caidMap["20220111"] = dspReq.Device.CAID

		// 		didMaps = append(didMaps, caidMap)
		// 	}
		// } else if len(dspReq.Device.Idfa) > 0 && isCAIDOK == false {
		// 	didType = "IDFA"

		// 	dids = append(dids, dspReq.Device.Idfa)
		// } else {
		// 	return false
		// }
		return false
	}

	////////////////////////////////////////////////////////////////////////////////
	// post data
	postData := map[string]interface{}{
		"reqId":     utils.Get16Md5(bigdataUID),
		"dids":      utils.Get16Md5(bigdataUID),
		"didType":   didType,
		"didSecure": didSecure,
	}

	// post data -> dids
	if didType == "CAID" {
		postData["dids"] = didMaps
	} else {
		postData["dids"] = dids
	}

	// post data -> rtaidlist
	rtaIDsStu := []AMapRtaIDStu{}
	json.Unmarshal([]byte(planInfo.AMapRtaIDs), &rtaIDsStu)
	rtaIDs := []string{}
	for _, item := range rtaIDsStu {
		rtaIDs = append(rtaIDs, item.RtaID)
	}
	postData["rtaIdList"] = rtaIDs
	////////////////////////////////////////////////////////////////////////////////

	jsonData, _ := json.Marshal(postData)
	l.Println("amap req: " + string(jsonData))

	requestGet, _ := http.NewRequest("POST", rtaAmapURL, bytes.NewReader(jsonData))
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")
	requestGet.Header.Add("Connection", "keep-alive")
	// requestGet.Header.Add("X-Forwarded-For", mhReq.Device.IP)
	// requestGet.Header.Add("User-Agent", destUA)
	resp, err := client.Do(requestGet)
	if err != nil {
		// l.Printf("get request failed, err:[%s]", err.Error())
		return false
	}
	if resp == nil {
		l.Println("IsAaapOK amap clk resp is nil")
		return false
	}
	defer resp.Body.Close()
	bodyContent, err := io.ReadAll(resp.Body)
	l.Println("amp resp: ", resp.StatusCode, string(bodyContent))

	if resp.StatusCode != 200 {
		return false
	}

	if didType == "CAID" {
		// respStu := AMapCAIDResp{}
		// json.Unmarshal([]byte(bodyContent), &respStu)

		// if respStu.RespCode == 0 {
		// 	for _, resultItem := range respStu.Result {
		// 		for _, didItem := range resultItem.DIDs {
		// 			if strings.Contains(didItem.DID, dspReq.Device.CAID) {
		// 				return true
		// 			}
		// 		}
		// 	}
		// }
	} else {
		respStu := AMapResp{}
		json.Unmarshal([]byte(bodyContent), &respStu)
		for _, resultItem := range respStu.Result {
			for _, didItem := range resultItem.DIDs {
				if didType == "IMEI" {
					if len(dspReq.Device.Imei) > 0 {
						if strings.Contains(didItem, utils.GetMd5(dspReq.Device.Imei)) {
							return true
						}
					} else if len(dspReq.Device.ImeiMd5) > 0 && dspReq.Device.ImeiMd5 != utils.GetMd5("") {
						if strings.Contains(didItem, dspReq.Device.ImeiMd5) {
							return true
						}
					}

				} else if didType == "OAID" {
					if strings.Contains(didItem, dspReq.Device.Oaid) {
						return true
					}
				} else if didType == "IDFA" {
					if strings.Contains(didItem, dspReq.Device.Idfa) {
						return true
					}
				}
			}
		}
	}

	return false
}

// AMapClkReport ...
func AMapClkReport(c *gin.Context, log url.Values) {

	os := log.Get("os")
	// osv := log.Get("osv")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	caid := log.Get("caid")
	caidVersion := log.Get("caid_version")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	// oaidMd5Str := ""
	if len(oaid) > 0 {
		// oaidMd5Str = utils.GetMd5(oaid)
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = idfaMd5
	}

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	// activateParams.Add("uid", uid)
	activateParams.Add("uid", log.Get("uid"))
	activateParams.Add("group_id", log.Get("group_id"))
	activateParams.Add("plan_id", log.Get("plan_id"))
	activateParams.Add("market_type", log.Get("market_type"))
	activateParams.Add("ads_type", log.Get("ads_type"))
	activateParams.Add("ext_dsp_channel", log.Get("ext_dsp_channel"))
	activateParams.Add("media_channel", log.Get("media_channel"))
	activateParams.Add("sub_channel_id", log.Get("sub_channel_id"))
	activateParams.Add("ext_adx", log.Get("ext_adx"))
	activateParams.Add("os", log.Get("os"))
	activateParams.Add("osv", log.Get("osv"))
	if len(log.Get("imei")) > 0 {
		activateParams.Add("imei", log.Get("imei"))
	}
	if len(log.Get("imei_md5")) > 0 {
		activateParams.Add("imei_md5", log.Get("imei_md5"))
	}
	if len(log.Get("android_id")) > 0 {
		activateParams.Add("android_id", log.Get("android_id"))
	}
	if len(log.Get("android_id_md5")) > 0 {
		activateParams.Add("android_id_md5", log.Get("android_id_md5"))
	}
	if len(log.Get("idfa")) > 0 {
		activateParams.Add("idfa", log.Get("idfa"))
	}
	if len(log.Get("idfa_md5")) > 0 {
		activateParams.Add("idfa_md5", log.Get("idfa_md5"))
	}
	if len(log.Get("oaid")) > 0 {
		activateParams.Add("oaid", log.Get("oaid"))
	}
	if len(log.Get("oaid_md5")) > 0 {
		activateParams.Add("oaid_md5", log.Get("oaid_md5"))
	}
	if len(strings.TrimSpace(log.Get("model"))) > 0 {
		activateParams.Add("model", strings.TrimSpace(log.Get("model")))
	}
	if len(strings.TrimSpace(log.Get("manufacturer"))) > 0 {
		activateParams.Add("manufacturer", strings.TrimSpace(log.Get("manufacturer")))
	}
	if len(log.Get("crid")) > 0 {
		activateParams.Add("crid", log.Get("crid"))
	}
	if len(log.Get("cpa_price")) > 0 {
		activateParams.Add("cpa_price", log.Get("cpa_price"))
	}
	if len(log.Get("ext_cpa_price")) > 0 {
		activateParams.Add("ext_cpa_price", log.Get("ext_cpa_price"))
	}
	// activateParams.Add("log", c.Query("log"))
	callback := config.ExternalAMapActiveURL + "?" + activateParams.Encode()

	// Android:
	// https://amap-ocpx-online.amap.com/ws/channel/common-device-info?source=abc_wy_ym_tt_01&imei=__IMEI__&oaid=__OAID__&oaid_md5=__OAID_MD5__&TS=__TS__&callback_url=__CALLBACK__
	// iOS:
	// https://amap-ocpx-online.amap.com/ws/channel/common-device- info?source=abc_wy_ym_tt_01&idfa=__IDFA__&idfa_md5=__IDFA_MD5__&caid=__CAID__&TS=__ TS__&callback_url=__CALLBACK__
	amapOCPXURL := "http://amap-ocpx-online.amap.com/ws/channel/common-device-info?source=" + log.Get("amap_source")
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", amapOCPXURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()
	if os == "android" {
		if len(imeiMd5Str) > 0 {
			q.Add("imei", string(imeiMd5Str))
		} else {
			q.Add("imei", "")
		}
		if len(oaid) > 0 {
			q.Add("oaid", oaid)
		} else {
			q.Add("oaid", "")
		}
		q.Add("oaid_md5", "")
	} else if os == "ios" {
		q.Add("idfa", "")
		if len(idfaMd5Str) > 0 {
			q.Add("idfa_md5", idfaMd5Str)
		} else {
			q.Add("idfa_md5", "")
		}

		// caid
		if len(caid) > 0 && len(caidVersion) > 0 {
			var tmpAMapCAIDArray []AMapCAIDStu
			var tmpAMapCAID AMapCAIDStu
			tmpAMapCAID.CAID = caid
			tmpAMapCAID.CAIDVersion = caidVersion
			tmpAMapCAIDArray = append(tmpAMapCAIDArray, tmpAMapCAID)
			jsonData, _ := json.Marshal(tmpAMapCAIDArray)

			q.Add("caid", url.QueryEscape(string(jsonData)))
		} else {
			q.Add("caid", "")
		}
	}

	q.Add("callback_url", callback)
	q.Add("ts", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()))
	requestGet.URL.RawQuery = q.Encode()

	l.Println("amap ocpx req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		l.Printf("amap clk get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		l.Println("amap ocpx resp is nil")
		return
	}
	defer resp.Body.Close()
	bodyContent, err := io.ReadAll(resp.Body)
	l.Println("amap ocpx resp: " + string(bodyContent))
}

// AMapResp ...
type AMapResp struct {
	RespCode int              `json:"resCode"`
	Result   []AMapResultResp `json:"result"`
}

type AMapResultResp struct {
	RtaID string   `json:"rtaId"`
	DIDs  []string `json:"dids"`
}

// AMapCAIDResp ...
type AMapCAIDResp struct {
	RespCode int                  `json:"resCode"`
	Result   []AMapCAIDResultResp `json:"result"`
}

type AMapCAIDResultResp struct {
	RtaID string                  `json:"rtaId"`
	DIDs  []AMapCAIDResultDIDResp `json:"dids"`
}

// AMapCAIDResultDIDResp ...
type AMapCAIDResultDIDResp struct {
	DID string `json:"20220111"`
}

// AMapRtaIDStu
type AMapRtaIDStu struct {
	RtaID string `json:"amap_rta_id"`
}

// AMapCAIDStu ...
type AMapCAIDStu struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}
