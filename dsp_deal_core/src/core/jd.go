package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	adCommonCore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

// JDDeviceInfo 京东科技设备信息结构体
type JDDeviceInfo struct {
	OS        string
	Imei      string
	ImeiMd5   string
	Oaid      string
	OaidMd5   string
	Idfa      string
	IdfaMd5   string
	AndroidID string
	Mac       string
}

// buildJDBaseParams 构建京东科技基础参数
func buildJDBaseParams(source string) url.Values {
	q := url.Values{}
	q.Add("code", "geruichuangxin")
	q.Add("account_id", "gerui01")
	q.Add("event_type", source) // click 或 expose
	return q
}

// getJdUrl 获取京东点击链接
func getJdUrl(deviceInfo JDDeviceInfo, callback string) (jdUrl string) {
	// 构建回调URL
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	// 根据操作系统添加对应的设备标识
	if strings.ToLower(deviceInfo.OS) == "android" {
		jdUrl = fmt.Sprintf("https://mk.jr.jd.com/market/reportUserClickV2?code=geruichuangxin&account_id=gerui01&oaid=%v&platform=android&click_time=%v&callback_url=%v", deviceInfo.Oaid, timestamp, callback)
	} else if strings.ToLower(deviceInfo.OS) == "ios" {
		jdUrl = fmt.Sprintf("https://mk.jr.jd.com/market/reportUserClickV2?code=geruichuangxin&account_id=gerui01&idfa=%v&platform=ios&click_time=%v&callback_url=%v", deviceInfo.Idfa, timestamp, callback)
	}

	return jdUrl
}

// addJDDeviceInfoToParams 添加设备信息到参数中
func addJDDeviceInfoToParams(params url.Values, deviceInfo JDDeviceInfo) (ret bool) {
	hasDeviceInfo := false

	// 根据操作系统添加对应的设备标识
	if strings.ToLower(deviceInfo.OS) == "android" {
		if len(deviceInfo.Oaid) > 0 {
			params.Add("oaid", deviceInfo.Oaid)
			hasDeviceInfo = true
		}
		if len(deviceInfo.ImeiMd5) > 0 {
			params.Add("imei_md5", deviceInfo.ImeiMd5)
			hasDeviceInfo = true
		}
		if len(deviceInfo.AndroidID) > 0 {
			params.Add("android_id", deviceInfo.AndroidID)
			hasDeviceInfo = true
		}
	} else if strings.ToLower(deviceInfo.OS) == "ios" {
		if len(deviceInfo.Idfa) > 0 {
			params.Add("idfa", deviceInfo.Idfa)
			hasDeviceInfo = true
		}
		if len(deviceInfo.IdfaMd5) > 0 {
			params.Add("idfa_md5", deviceInfo.IdfaMd5)
			hasDeviceInfo = true
		}
	}

	// 添加通用设备信息
	if len(deviceInfo.Mac) > 0 {
		params.Add("mac", deviceInfo.Mac)
		hasDeviceInfo = true
	}

	return hasDeviceInfo
}

// sendJDRequest 发送京东科技请求
func sendJDRequest(c *gin.Context, jdURL string, params url.Values, source string, beginTime time.Time) {
	bodyContent, statusCode, err := adCommonCore.GetHTTPClientDebug(true).DoWithTimeout(
		c,
		600*time.Millisecond,
		http.MethodGet,
		jdURL,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/json; charset=utf-8"}),
		utilities.WithQueryEx(params),
	)

	logger.GetSugaredLogger().Infof("jd request result: url=%v, params=%v, statusCode=%v, bodyContent=%v, source=%v, cost=%v, err=%v",
		jdURL, params.Encode(), statusCode, string(bodyContent), source, time.Since(beginTime), err)

	if err != nil {
		logger.GetSugaredLogger().Errorf("jd request error: %v", err)
		return
	}

	// 解析响应
	if statusCode == 200 {
		var response map[string]interface{}
		if err := json.Unmarshal(bodyContent, &response); err == nil {
			if code, exists := response["code"]; exists {
				logger.GetSugaredLogger().Infof("jd response code: %v, msg: %v", code, response["msg"])
			}
		}
	}
}

// JDReport 京东科技点击上报
func JDReport(c *gin.Context, log url.Values, source string) {
	beginTime := time.Now()
	pid := log.Get("plan_id")
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, pid) // createMockPlanInfo()

	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("jd report plan info not found, plan_id: %s, source: %s", pid, source)
		return
	}

	if planInfo.IsOCPX != 1 {
		logger.GetSugaredLogger().Errorf("jd report plan is not ocpx, plan_id: %s, source: %s", pid, source)
		return
	}

	// 构建基础参数
	q := buildJDBaseParams(source)

	// 构建设备信息
	deviceInfo := JDDeviceInfo{
		OS:        log.Get("os"),
		Imei:      log.Get("imei"),
		ImeiMd5:   log.Get("imei_md5"),
		Oaid:      log.Get("oaid"),
		OaidMd5:   log.Get("oaid_md5"),
		Idfa:      log.Get("idfa"),
		IdfaMd5:   log.Get("idfa_md5"),
		AndroidID: log.Get("android_id"),
		Mac:       log.Get("mac"),
	}

	// 添加设备信息到参数中
	activateParams := url.Values{}
	encodeParams := EncodeJDParams(planInfo, log, source)
	activateParams.Add("log", encodeParams)
	// activateParams.Add("actionType", source)
	callback := config.ExternalJDActiveURL + "?" + activateParams.Encode()

	jdUrl := getJdUrl(deviceInfo, callback)
	hasDeviceInfo := addJDDeviceInfoToParams(q, deviceInfo)
	if !hasDeviceInfo {
		logger.GetSugaredLogger().Errorf("jd report missing device info, plan_id: %s, source: %s", pid, source)
		return
	}

	logger.GetSugaredLogger().Infof("jd report plan_id=%v, jdUrl=%v", pid, jdUrl)
	// 发送请求
	sendJDRequest(c, jdUrl, q, source, beginTime)
}

// EncodeJDParams 编码京东科技参数
func EncodeJDParams(planInfo *models.DspPlanStu, log url.Values, source string) string {
	activateParams := url.Values{}

	// 基础参数
	baseFields := []string{"uid", "group_id", "plan_id", "market_type", "ads_type", "ext_dsp_channel", "media_channel", "sub_channel_id", "ext_adx", "os", "osv", "did_md5"}
	for _, field := range baseFields {
		activateParams.Add(field, log.Get(field))
	}

	// 设备信息（可选字段）
	optionalFields := []string{"imei", "imei_md5", "android_id", "android_id_md5", "idfa", "idfa_md5", "oaid", "oaid_md5", "caid_multi"}
	for _, field := range optionalFields {
		if value := log.Get(field); len(value) > 0 {
			activateParams.Add(field, value)
		}
	}

	// 设备型号信息（需要去除空格）
	if model := strings.TrimSpace(log.Get("model")); len(model) > 0 {
		activateParams.Add("model", model)
	}
	if manufacturer := strings.TrimSpace(log.Get("manufacturer")); len(manufacturer) > 0 {
		activateParams.Add("manufacturer", manufacturer)
	}

	// CPA价格
	if planInfo.UnitPriceNum > 0 && planInfo.UnitPriceType == 2 {
		activateParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
	}

	activateParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(activateParams.Encode()))
	return url.QueryEscape(encodeStr)
}

// JDClkReportFromAdx 从ADX来的京东科技点击上报
func JDClkReportFromAdx(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) {
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("jd report from adx plan info not found, source=%v", source)
		return
	}

	pid := planInfo.PID
	if planInfo.IsOCPX != 1 {
		logger.GetSugaredLogger().Errorf("jd report from adx plan is not ocpx, plan_id=%v, source=%v", pid, source)
		return
	}

	beginTime := time.Now()

	// 构建设备信息
	deviceInfo := JDDeviceInfo{
		OS:      mhCpaReq.Os,
		Imei:    mhCpaReq.Imei,
		ImeiMd5: mhCpaReq.ImeiMd5,
		Oaid:    mhCpaReq.Oaid,
		OaidMd5: mhCpaReq.OaidMd5,
		Idfa:    mhCpaReq.Idfa,
		IdfaMd5: mhCpaReq.IdfaMd5,
	}

	// 检查设备信息是否有效
	hasDeviceInfo := false
	if strings.ToLower(deviceInfo.OS) == "android" && len(deviceInfo.Oaid) > 0 {
		hasDeviceInfo = true
	} else if strings.ToLower(deviceInfo.OS) == "ios" && len(deviceInfo.Idfa) > 0 {
		hasDeviceInfo = true
	}

	if !hasDeviceInfo {
		logger.GetSugaredLogger().Errorf("jd report from adx missing device info, plan_id: %s, source: %s", pid, source)
		return
	}

	// 构建回调URL
	activateParams := url.Values{}
	encodeParams := EncodeJDParamsFromAdx(planInfo, mhCpaReq, source)
	activateParams.Add("log", encodeParams)
	callback := config.ExternalJDActiveURL + "?" + activateParams.Encode()
	jdUrl := getJdUrl(deviceInfo, callback)

	// 发送请求
	sendJDRequest(c, jdUrl, url.Values{}, source, beginTime)
}

// EncodeJDParamsFromAdx 从ADX编码京东科技参数
func EncodeJDParamsFromAdx(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) string {
	bigdataParams := url.Values{}

	// 基础参数
	bigdataParams.Add("uid", mhCpaReq.UID)
	bigdataParams.Add("group_id", planInfo.GID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "1")
	bigdataParams.Add("ext_adx", mhCpaReq.Channel)
	bigdataParams.Add("os", mhCpaReq.Os)
	bigdataParams.Add("ip", mhCpaReq.IP)

	// 价格信息
	if planInfo.UnitPriceNum > 0 {
		priceFields := map[int]string{0: "cpm_price", 1: "cpc_price", 2: "cpa_price"}
		if field, exists := priceFields[planInfo.UnitPriceType]; exists {
			bigdataParams.Add(field, utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	if planInfo.SspPriceNum > 0 {
		sspPriceFields := map[int]string{0: "ext_cpm_price", 1: "ext_cpc_price"}
		if field, exists := sspPriceFields[planInfo.SspPriceType]; exists {
			bigdataParams.Add(field, utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		}
	}

	// 设备信息（可选字段）
	deviceFields := map[string]string{
		"imei": mhCpaReq.Imei, "imei_md5": mhCpaReq.ImeiMd5,
		"oaid": mhCpaReq.Oaid, "oaid_md5": mhCpaReq.OaidMd5,
		"idfa": mhCpaReq.Idfa, "idfa_md5": mhCpaReq.IdfaMd5,
		"osv": mhCpaReq.Osv, "did_md5": mhCpaReq.DIDMd5,
	}
	for field, value := range deviceFields {
		if len(value) > 0 {
			bigdataParams.Add(field, value)
		}
	}

	bigdataParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	return url.QueryEscape(encodeStr)
}
