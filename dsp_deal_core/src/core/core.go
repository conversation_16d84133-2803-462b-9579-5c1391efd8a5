package core

import (
	"context"
	"dsp_core/base"
	"dsp_core/config"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"math/rand"
	"net/url"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	uuid "github.com/satori/go.uuid"
)

// GetFromDsp ...
func GetFromDsp(c context.Context, dspReq *models.DspReqStu, planID string) *models.DspPlanRespStu {
	// logger.GetSugaredLogger().Info("get from dsp")

	if len(planID) == 0 {
		return nil
	}

	// 取计划信息
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo == nil {
		return nil
	}

	bigdataUID := uuid.NewV4().String()

	// 策略是否OK
	isPolicyOK, policyErrorCode, policyReason := isPlanPolicyOK(c, *dspReq, planInfo, bigdataUID)
	if isPolicyOK {
	} else {
		return DspErrorResp(c, bigdataUID, *dspReq, planInfo, "", policyErrorCode, policyReason)
	}

	// 数据优化类型
	if planInfo.GroupMarketingType == "0" && planInfo.GroupAdsType == "2" {
		if len(planInfo.ExpLinks) == 0 && len(planInfo.ClkLinks) == 0 {
			return DspErrorResp(c, bigdataUID, *dspReq, planInfo, "", models.ErrCodeNoExpClkLink, models.ReasonNoExpClkLink)
		}
	}

	// exp_link
	mhImpParams := url.Values{}
	bigdataParams := EncodeParams(dspReq, planInfo, bigdataUID, "", "")
	mhImpParams.Add("log", bigdataParams)

	// click_link
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", "__REQ_WIDTH__")
	mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	mhClkParams.Add("width", "__WIDTH__")
	mhClkParams.Add("height", "__HEIGHT__")
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("plan_id", planInfo.PID)
	mhClkParams.Add("log", bigdataParams)

	// resp
	var planResp models.DspRespStu
	planResp.Code = 10000

	// resp data
	var planRespData models.DspRespDataStu
	// 曝光监测链接array
	planRespData.ExpLinks = append(planRespData.ExpLinks, planInfo.ExpLinks...)
	planRespData.ExpLinks = append(planRespData.ExpLinks, config.ExternalExpURL+"?"+mhImpParams.Encode())
	// 点击监测链接array
	planRespData.ClkLinks = append(planRespData.ClkLinks, planInfo.ClkLinks...)
	planRespData.ClkLinks = append(planRespData.ClkLinks, config.ExternalClkURL+"?"+mhClkParams.Encode())

	planResp.Data = append(planResp.Data, planRespData)

	// dsp resp
	var dspResp models.DspPlanRespStu
	dspResp.Resp = planResp

	// 上报大数据
	go models.BigDataAdxReq(c, *dspReq, planInfo, bigdataUID, "", models.ErrCodeSuccess, models.ReasonSuccess)

	return &dspResp
}

// DspErrorResp ...
func DspErrorResp(c context.Context, bigdataUID string, dspReq models.DspReqStu, planInfo *models.DspPlanStu, msg string, errorCode int, reason string) *models.DspPlanRespStu {
	var dspResp models.DspPlanRespStu
	dspResp.Resp.Code = errorCode

	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", errorCode, reason)

	return &dspResp
}

// EncodeParams ...
func EncodeParams(dspReq *models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string, extraStr string, crid string) string {
	bigdataParams := url.Values{}
	bigdataParams.Add("uid", bigdataUID)
	bigdataParams.Add("group_id", planInfo.GID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "0")
	if extraStr == "1" {
		bigdataParams.Add("dhh_channel_id", planInfo.DHHChannelID)
		bigdataParams.Add("dhh_pos_id", planInfo.DHHPosID)
		bigdataParams.Add("dhh_task_id", planInfo.DhhTaskID)
	} else if extraStr == "2" {
		// bigdataParams.Add("tencent_gid", planInfo.TencentGID)
		// bigdataParams.Add("tencent_media", planInfo.TencentMedia)
		// bigdataParams.Add("tencent_channel", planInfo.TencentChannel)
		// bigdataParams.Add("tencent_sch_id", planInfo.TencentSchID)
		// bigdataParams.Add("tencent_loc_id", planInfo.TencentLocID)
		// bigdataParams.Add("tencent_scid", planInfo.TencentScID)
		// bigdataParams.Add("tencent_sc_name", planInfo.TencentScName)
		// bigdataParams.Add("tencent_mtr_id", planInfo.TencentMtrID)
		// bigdataParams.Add("tencent_o2_trace_id", planInfo.TencentO2TraceID)
	} else if extraStr == "5" {
		bigdataParams.Add("amap_source", planInfo.AMapSource)
	}
	// unit_price_type: 0 CPM, 1 CPC, 2 CPA
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
			bigdataParams.Add("cpm_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 1 {
			bigdataParams.Add("cpc_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 2 {
			bigdataParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	// ssp_price_type: 0 CPM, 1 CPC, 2 动态CPM
	if planInfo.SspPriceType == 0 {
		bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
	} else if planInfo.SspPriceType == 1 {
		bigdataParams.Add("ext_cpc_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
	} else if planInfo.SspPriceType == 2 {
		tmpPrice := int(float32(dspReq.Pos.CPMBidFloor) * planInfo.SspDynamicCPMTimes)
		tmpMaxPrice := int(planInfo.SspDynamicCPMMaxNum * 100)
		if tmpPrice >= tmpMaxPrice {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(tmpMaxPrice))
		} else {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(tmpPrice))
		}
	}
	bigdataParams.Add("os", dspReq.Device.Os)
	bigdataParams.Add("osv", dspReq.Device.OsVersion)
	bigdataParams.Add("did_md5", dspReq.Device.DIDMd5Key)
	if len(dspReq.Device.Imei) > 0 {
		bigdataParams.Add("imei", dspReq.Device.Imei)
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", dspReq.Device.ImeiMd5)
	}
	if len(dspReq.Device.AndroidID) > 0 {
		bigdataParams.Add("android_id", dspReq.Device.AndroidID)
	}
	if len(dspReq.Device.AndroidIDMd5) > 0 {
		bigdataParams.Add("android_id_md5", dspReq.Device.AndroidIDMd5)
	}
	if len(dspReq.Device.Oaid) > 0 {
		bigdataParams.Add("oaid", dspReq.Device.Oaid)
	}
	if len(dspReq.Device.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", dspReq.Device.OaidMd5)
	}
	if len(dspReq.Device.Idfa) > 0 {
		bigdataParams.Add("idfa", dspReq.Device.Idfa)
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", dspReq.Device.IdfaMd5)
	}
	// if len(dspReq.Device.CAID) > 0 && len(dspReq.Device.CAIDVersion) > 0 {
	// 	bigdataParams.Add("caid", dspReq.Device.CAID)
	// 	bigdataParams.Add("caid_version", dspReq.Device.CAIDVersion)
	// }

	if len(dspReq.Device.CAIDMulti) > 0 {
		tmpByte, _ := json.Marshal(dspReq.Device.CAIDMulti)
		bigdataParams.Add("caid_multi", string(tmpByte))
	}

	bigdataParams.Add("model", dspReq.Device.Model)
	bigdataParams.Add("manufacturer", dspReq.Device.Manufacturer)
	bigdataParams.Add("crid", crid)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

func isPlanPolicyOK(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string) (bool, int, string) {

	// 底价策略
	if planInfo.SspPriceType == 2 {
		if dspReq.Pos.CPMBidFloor > int(planInfo.SspDynamicCPMMaxNum*100) {
			// logger.GetSugaredLogger().Info("req wrong ecpm: ", planInfo.PID, dspReq.Pos.CPMBidFloor, planInfo.SspDynamicCPMMaxNum)
			return false, models.ErrCodeEcpm, models.ReasonEcpm
		}
	}

	// 设备Manufacturer限制
	if IsManufacturerOK(c, dspReq, planInfo) {
	} else {
		// logger.GetSugaredLogger().Info("req wrong manufacturer: ", planInfo.PID, dspReq.Device.Manufacturer, planInfo.AndroidManufacturer)
		return false, models.ErrCodeManufacturer, models.ReasonManufacturer
	}

	// 安卓版本限制
	if dspReq.Device.Os == "android" {
		if planInfo.AndroidVersion == "all" {
		} else {
			osvMajor := 0
			if len(dspReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// logger.GetSugaredLogger().Info(osvMajor)
			}

			if osvMajor > utils.ConvertStringToInt(planInfo.AndroidVersion) {
			} else {
				return false, models.ErrCodeAndroidVersion, models.ReasonAndroidVersion
			}
		}
	}
	// 安卓版本过滤限制
	if IsAndroidVersionFilterOK(c, &dspReq, planInfo) {
	} else {
		// logger.GetSugaredLogger().Info("req android version filter failter: ", planInfo.PID, dspReq.Device.Manufacturer, dspReq.Device.OsVersion)
		return false, models.ErrCodeAndroidVersion, models.ReasonAndroidVersion
	}

	// sdk版本限制
	if len(planInfo.SDKVersion) > 0 && len(dspReq.SDKVersion) > 0 {
		tmpReqVersionArray := strings.Split(dspReq.SDKVersion, ".")
		tmpConfigVersionArray := strings.Split(planInfo.SDKVersion, ".")

		tmpReqVersion0 := 0
		tmpConfigVersion0 := 0

		tmpReqVersion1 := 0
		tmpConfigVersion1 := 0

		tmpReqVersion2 := 0
		tmpConfigVersion2 := 0

		tmpReqVersion3 := 0
		tmpConfigVersion3 := 0

		if len(tmpReqVersionArray) > 0 && len(tmpConfigVersionArray) > 0 {
			if utils.IsNum(tmpReqVersionArray[0]) && utils.IsNum(tmpConfigVersionArray[0]) {
				tmpReqVersion0 = utils.ConvertStringToInt(tmpReqVersionArray[0])
				tmpConfigVersion0 = utils.ConvertStringToInt(tmpConfigVersionArray[0])
				if tmpReqVersion0 > tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 1 && len(tmpConfigVersionArray) > 1 {
			if utils.IsNum(tmpReqVersionArray[1]) && utils.IsNum(tmpConfigVersionArray[1]) {
				tmpReqVersion1 = utils.ConvertStringToInt(tmpReqVersionArray[1])
				tmpConfigVersion1 = utils.ConvertStringToInt(tmpConfigVersionArray[1])
				if tmpReqVersion1 > tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 2 && len(tmpConfigVersionArray) > 2 {
			if utils.IsNum(tmpReqVersionArray[2]) && utils.IsNum(tmpConfigVersionArray[2]) {
				tmpReqVersion2 = utils.ConvertStringToInt(tmpReqVersionArray[2])
				tmpConfigVersion2 = utils.ConvertStringToInt(tmpConfigVersionArray[2])
				if tmpReqVersion2 > tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 3 && len(tmpConfigVersionArray) > 3 {
			if utils.IsNum(tmpReqVersionArray[3]) && utils.IsNum(tmpConfigVersionArray[3]) {
				tmpReqVersion3 = utils.ConvertStringToInt(tmpReqVersionArray[3])
				tmpConfigVersion3 = utils.ConvertStringToInt(tmpConfigVersionArray[3])
				if tmpReqVersion3 > tmpConfigVersion3 && tmpReqVersion2 == tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
	}

	// 地域限制
	if planInfo.LBSType == 1 {
		if len(planInfo.LBSWhiteList) == 0 && len(planInfo.LBSBlackList) == 0 {
			return false, models.ErrCodeLbs, models.ReasonLbs
		}
		if len(dspReq.Device.LBSIPProvince) == 0 || len(dspReq.Device.LBSIPCity) == 0 {
			return false, models.ErrCodeLbs, models.ReasonLbs
		}
		// 地域白名单
		if len(planInfo.LBSWhiteList) > 0 {
			isInWhiteCity := false
			for _, item := range planInfo.LBSWhiteList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInWhiteCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInWhiteCity = true
						break
					}
				}
			}

			if isInWhiteCity {
			} else {
				return false, models.ErrCodeLbs, models.ReasonLbs
			}
		}

		// 地域黑名单
		if len(planInfo.LBSBlackList) > 0 {
			isInBlackCity := false
			for _, item := range planInfo.LBSBlackList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInBlackCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInBlackCity = true
						break
					}
				}
			}

			if isInBlackCity {
				return false, models.ErrCodeLbs, models.ReasonLbs
			}
		}
	}

	// 限制类型: 0 不限制, 1 限制
	// 单设备请求间隔设置
	if planInfo.MaxReqDeviceIntervalType == "1" {
		tmpRedisKey := "limit_device_req_interval_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + dspReq.Device.DIDMd5Key
		_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(int64(utils.ConvertStringToInt(planInfo.MaxReqDeviceIntervalNum)))*time.Millisecond)
		} else {
			// logger.GetSugaredLogger().Info("plan max device req interval failed, os: " + dspReq.Device.Os + ", did: " + dspReq.Device.DIDMd5Key + ", id: " + planInfo.PID)
			return false, models.ErrCodeMaxDeviceReqInterval, models.ReasonMaxDeviceReqInterval
		}
	}
	// 单日曝光限制
	if planInfo.MaxExpType == "1" {
		tmpRedisKey := "max_exp_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
		tmpRedisValue, cacheErr := models.CacheFromRedis(c, tmpRedisKey)

		if cacheErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			// logger.GetSugaredLogger().Info("dsp pos max exp total redis key: ", maxReqRedisKey)
			// logger.GetSugaredLogger().Info("dsp pos max exp total redis value: ", tmpRedisInt)
			// logger.GetSugaredLogger().Info("dsp pos max exp total value: ", selfPosInfo.MaxExpTotal)
			if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxExpNum) {
				// logger.GetSugaredLogger().Info("dsp pos max exp failed, " + planInfo.PID)
				return false, models.ErrCodeMaxExpTotal, models.ReasonMaxExpTotal
			}
		}
	}
	// 单日点击限制
	if planInfo.MaxClkType == "1" {
		tmpRedisKey := "max_clk_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
		tmpRedisValue, cacheErr := models.CacheFromRedis(c, tmpRedisKey)

		if cacheErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxClkNum) {
				// logger.GetSugaredLogger().Info("dsp pos max clk failed, " + planInfo.PID)
				return false, models.ErrCodeMaxClkTotal, models.ReasonMaxClkTotal
			}
		}
	}
	// 单日设备曝光限制, 单日设备点击限制
	if planInfo.MaxExpDeviceType == "1" || planInfo.MaxClkDeviceType == "1" || planInfo.MaxLimitIPType == "1" {
		tmpDid := dspReq.Device.DIDMd5Key
		if len(tmpDid) > 0 {
			// 单日设备曝光限制
			if planInfo.MaxExpDeviceType == "1" {
				tmpRedisKey := "limit_device_exp_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpDid
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxExpDeviceNum) {
						// logger.GetSugaredLogger().Info("plan max device exp failed, os: " + dspReq.Device.Os + ", did: " + tmpDid + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceExp, models.ReasonMaxDeviceExp
					}
				}
			}
			// 单日设备点击限制
			if planInfo.MaxClkDeviceType == "1" {
				tmpRedisKey := "limit_device_clk_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpDid
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxClkDeviceNum) {
						// logger.GetSugaredLogger().Info("plan max device clk failed, os: " + dspReq.Device.Os + ", did: " + tmpDid + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceClk, models.ReasonMaxDeviceClk
					}
				}
			}
		}
		// 单日ip请求限制
		tmpIP := dspReq.Device.IP
		if len(tmpIP) > 0 {
			if planInfo.MaxLimitIPType == "1" {
				tmpRedisKey := "limit_ip_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpIP
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxLimitIPNum) {
						// logger.GetSugaredLogger().Info("plan max device ip failed, os: " + dspReq.Device.Os + ", ip: " + tmpIP + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceIp, models.ReasonMaxDeviceIp
					}
				}

				// ip通过限制请求
				t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
				leftUnixTime := t.Unix() + 2 - time.Now().Unix()

				// ttl 0:00 - 3:00
				leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

				if redisErr != nil {
					db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
				} else {
					db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
				}
			}
		}
	}
	// 日期限制
	if planInfo.DateType == "1" {
		tmpSplitArray := strings.Split(planInfo.DateValue, ",")
		// logger.GetSugaredLogger().Info(len(tmpSplitArray))
		if len(tmpSplitArray) == 2 {
			startDate := tmpSplitArray[0]
			startDate = strings.Replace(startDate, "-", "", -1)

			endDate := tmpSplitArray[1]
			endDate = strings.Replace(endDate, "-", "", -1)

			nowDate := time.Now().Format("20060102")

			// logger.GetSugaredLogger().Info(startDate)
			// logger.GetSugaredLogger().Info(endDate)
			// logger.GetSugaredLogger().Info("now date: " + nowDate)

			if utils.ConvertStringToInt(nowDate) < utils.ConvertStringToInt(startDate) || utils.ConvertStringToInt(nowDate) > utils.ConvertStringToInt(endDate) {
				// logger.GetSugaredLogger().Info("plan pos date failed, " + planInfo.PID)
				return false, models.ErrCodeDate, models.ReasonDate
			}
		}
	}
	// 时间限制
	if planInfo.TimeType == "1" {
		if len(planInfo.TimeList) == 0 {
			// logger.GetSugaredLogger().Info("plan pos time failed, " + planInfo.PID)
			return false, models.ErrCodeTime, models.ReasonTime
		}

		isTimeOK := false
		for _, item := range planInfo.TimeList {
			nowHour := time.Now().Format("15")
			startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[0], time.Local)
			endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[1], time.Local)

			if utils.ConvertStringToInt(nowHour) >= startTime.Hour() &&
				utils.ConvertStringToInt(nowHour) <= endTime.Hour() {
				isTimeOK = true
			}
		}
		if isTimeOK {
		} else {
			// logger.GetSugaredLogger().Info("plan pos time failed, " + planInfo.PID)
			return false, models.ErrCodeTime, models.ReasonTime
		}
	}

	// 人群包定向开, 并且rta开, 开启复投生效
	if planInfo.IsRepetition == 1 && planInfo.CrowdLibType == "1" && len(planInfo.CrowdLibID) > 0 && planInfo.IsRTA == 1 {
	} else {
		return base.IsPlanPolicyCrowdLibOK(c, dspReq, planInfo, bigdataUID)
	}

	return true, models.ErrCodeSuccess, models.ReasonSuccess
}

// all,huawei,xiaomi,oppo,vivo
func IsManufacturerOK(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu) bool {
	if strings.Contains(planInfo.AndroidManufacturer, "all") {
		return true
	}
	if len(planInfo.AndroidManufacturer) == 0 {
		return true
	}
	manufactureFilterArray := strings.Split(planInfo.AndroidManufacturer, ",")
	for _, manufactureFilterItem := range manufactureFilterArray {
		if manufactureFilterItem == "oppo" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"oppo")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "vivo" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"vivo")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "huawei" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"huawei")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "honor" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"honor")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "xiaomi" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"xiaomi")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "lenovo" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"lenovo")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}

		} else if manufactureFilterItem == "meizu" {
			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"meizu")
			if len(manufactureConfigArray) == 0 {
				continue
			}

			isInManufactureConfig := false
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureConfig = true
				}
			}
			if isInManufactureConfig {
				return true
			}
		} else if manufactureFilterItem == "others" {
			isInManufactureOthers := true

			manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"oppo")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"vivo")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"huawei")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"honor")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"xiaomi")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"lenovo")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"meizu")
			for _, manufactureConfigItem := range manufactureConfigArray {
				if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
					isInManufactureOthers = false
				}
			}

			if isInManufactureOthers {
				return true
			}
		}
	}

	return false
}

// GetReplaceManufactureConfig ...
// GET ssp_manufacturer_packages_huawei
// GET ssp_manufacturer_packages_xiaomi
// GET ssp_manufacturer_packages_oppo
// GET ssp_manufacturer_packages_vivo
// GET ssp_manufacturer_packages_lenovo
// GET ssp_manufacturer_packages_meizu
func GetReplaceManufactureConfig(c context.Context, cacheKey string) []string {
	var manufactureArray []string

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		logger.GetSugaredLogger().Error("cache error:", cacheKey, cacheError)

		redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, cacheKey)
		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GlbBigCache.Set(cacheKey, []byte(""))
		} else {
			cacheValue = []byte(redisValue)
			db.GlbBigCache.Set(cacheKey, []byte(redisValue))
		}
	}

	json.Unmarshal(cacheValue, &manufactureArray)
	return manufactureArray
}

// IsAndroidVersionFilterOK
func IsAndroidVersionFilterOK(c context.Context, dspReq *models.DspReqStu, planInfo *models.DspPlanStu) bool {
	if dspReq.Device.Os == "ios" {
		return true
	}

	if len(planInfo.AndroidVersionFilters) == 0 {
		return true
	}

	osvMajor := 0
	if len(dspReq.Device.OsVersion) > 0 {
		osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
		osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// logger.GetSugaredLogger().Info(osvMajor)
	}

	manufactureFlag := GetManufactureFlag(c, dspReq)

	isFound := false
	for _, item := range planInfo.AndroidVersionFilters {
		if item.FilterManufacturer == manufactureFlag {
			isFound = true
			if osvMajor > utils.ConvertStringToInt(item.FilterOsv) {
				return false
			} else {
				return true
			}
		}
	}

	if isFound {
	} else {
		for _, item := range planInfo.AndroidVersionFilters {
			if item.FilterManufacturer == "all" {
				if osvMajor > utils.ConvertStringToInt(item.FilterOsv) {
					return false
				} else {
					return true
				}
			}
		}
	}

	return true
}

func GetManufactureFlag(c context.Context, dspReq *models.DspReqStu) string {
	manufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"oppo")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "oppo"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"vivo")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "vivo"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"huawei")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "huawei"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"xiaomi")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "xiaomi"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"lenovo")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "lenovo"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"meizu")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "meizu"
		}
	}

	manufactureConfigArray = GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"honor")
	for _, manufactureConfigItem := range manufactureConfigArray {
		if strings.ToLower(dspReq.Device.Manufacturer) == manufactureConfigItem {
			return "honor"
		}
	}

	return "others"
}
