package core

import (
	"dsp_core/models"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
)

func init() {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()

}

// 创建测试用的gin.Context
func createTestContext() *gin.Context {
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest(http.MethodGet, "/test", nil)
	return c
}

// 测试Android设备的曝光上报
func TestAlipayReportAndroid(t *testing.T) {
	// 这里只是一个占位测试，实际测试需要使用mock框架
	// 在实际项目中，可以使用gomock或testify/mock来模拟依赖
	// 由于当前环境限制，我们只保留基本测试结构
	c := createTestContext()
	log := url.Values{}
	log.Add("plan_id", "72deff8913a90837")
	log.Add("os", "android")
	log.Add("imei", "123456789012345")
	planInfo := createMockPlanInfo()
	AlipayClkReportFromAdx(c, planInfo, &models.MHCpaReq{
		UID:     "test_uid",
		Channel: "test_channel",
		IP:      "127.0.0.1",
		Os:      "android",
		Imei:    "123456789012345",
		ImeiMd5: "test_imei_md5",
		// Oaid:    "test_oaid",
		// OaidMd5: "test_oaid_md5",
		// Idfa:    "test_idfa",
		// IdfaMd5: "test_idfa_md5",
		// Osv:    "10.0",
		// DIDMd5: "test_did_md5",
	}, "click")
	// AlipayReport(c, log, "expose")
}

// 测试iOS设备的点击上报
func TestAlipayReportIOS(t *testing.T) {
	// 这里只是一个占位测试，实际测试需要使用mock框架
	c := createTestContext()
	log := url.Values{}
	log.Add("plan_id", "72deff8913a90837")
	log.Add("os", "ios")
	log.Add("idfa", "test-idfa-value")
	AlipayReport(c, log, "click")
}
