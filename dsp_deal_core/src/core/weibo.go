package core

import (
	"dsp_core/config"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
)

// WeiBoClkReport ocpx
func WeiBoClkReport(c *gin.Context, log url.Values, source string) {
	pid := log.Get("plan_id")
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, pid)
	if planInfo == nil {
		// logger.GetSugaredLogger().Infof("weibo ocpx: planInfo is nil, plan_id: %v, log=%v", pid, log)
		return
	}

	if planInfo.IsOCPX == 1 {
	} else {
		// logger.GetSugaredLogger().Infof("weibo ocpx: plan is not OCPX, plan_id: %v, log=%v", pid, log)
		return
	}

	os := log.Get("os")
	// osv := log.Get("osv")
	imei := log.Get("imei")
	imeiMd5 := log.Get("imei_md5")
	oaid := log.Get("oaid")
	oaidMd5 := log.Get("oaid_md5")
	idfa := log.Get("idfa")
	idfaMd5 := log.Get("idfa_md5")
	caidMultiStr := log.Get("caid_multi")
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	imeiMd5Str := ""
	if len(imei) > 0 {
		imeiMd5Str = utils.GetMd5(imei)
	} else if len(imeiMd5) > 0 {
		imeiMd5Str = imeiMd5
	}
	idfaMd5Str := ""
	if len(idfa) > 0 {
		idfaMd5Str = utils.GetMd5(idfa)
	} else if len(idfaMd5) > 0 {
		idfaMd5Str = idfaMd5
	}

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeWeiBoParams(planInfo, log, source)
	activateParams.Add("log", encodeParams)
	activateParams.Add("event", "__EVENT__")

	callback := config.ExternalWeiBoActiveURL + "?" + activateParams.Encode()

	ocpxURL := "https://ocpx.sina.cn/track/zybang"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()

	q.Add("os", string(os))
	if os == "android" {
		if len(imeiMd5Str) > 0 {
			q.Add("imei_md5", string(imeiMd5Str))
		}
		if len(oaid) > 0 {
			q.Add("oaid_md5", utils.GetMd5(oaid))
		} else if len(oaidMd5) > 0 {
			q.Add("oaid_md5", oaidMd5)
		}
	} else if os == "ios" {
		if len(idfaMd5Str) > 0 {
			q.Add("idfa_md5", idfaMd5Str)
		}

		// caid multi
		if len(caidMultiStr) > 0 {
			var caidMulti []models.DspReqDeviceCAIDMulti
			json.Unmarshal([]byte(caidMultiStr), &caidMulti)

			var tmpCAIDArray []WeiBoCAIDStu
			for _, item := range caidMulti {
				var tmpCAID WeiBoCAIDStu
				tmpCAID.CAID = item.CAID
				tmpCAID.CAIDVersion = item.CAIDVersion

				tmpCAIDArray = append(tmpCAIDArray, tmpCAID)
			}
			if len(tmpCAIDArray) > 0 {
				jsonData, _ := json.Marshal(tmpCAIDArray)
				q.Add("caid", string(jsonData))
			}
		}
	}

	q.Add("ts", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()))
	q.Add("callback", callback)
	requestGet.URL.RawQuery = q.Encode()

	resp, err := client.Do(requestGet)
	if err != nil {
		logger.GetSugaredLogger().Errorf("weibo ocpx get request failed, url=%v, callback=%v, err=%v", requestGet.URL.String(), callback, err)
	}
	if resp == nil {
		return
	}

	defer resp.Body.Close()
	bodyContent, _ := io.ReadAll(resp.Body)
	logger.GetSugaredLogger().Info("weibo ocpx resp: " + string(bodyContent))

}

// WeiBoCAIDStu ...
type WeiBoCAIDStu struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"version"`
}

// EncodeWeiBoParams ...
func EncodeWeiBoParams(planInfo *models.DspPlanStu, log url.Values, source string) string {
	activateParams := url.Values{}
	activateParams.Add("uid", log.Get("uid"))
	activateParams.Add("group_id", log.Get("group_id"))
	activateParams.Add("plan_id", log.Get("plan_id"))
	activateParams.Add("market_type", log.Get("market_type"))
	activateParams.Add("ads_type", log.Get("ads_type"))
	activateParams.Add("ext_dsp_channel", log.Get("ext_dsp_channel"))
	activateParams.Add("media_channel", log.Get("media_channel"))
	activateParams.Add("sub_channel_id", log.Get("sub_channel_id"))
	activateParams.Add("ext_adx", log.Get("ext_adx"))
	activateParams.Add("os", log.Get("os"))
	activateParams.Add("osv", log.Get("osv"))

	activateParams.Add("did_md5", log.Get("did_md5"))
	if len(log.Get("imei")) > 0 {
		activateParams.Add("imei", log.Get("imei"))
	}
	if len(log.Get("imei_md5")) > 0 {
		activateParams.Add("imei_md5", log.Get("imei_md5"))
	}
	if len(log.Get("android_id")) > 0 {
		activateParams.Add("android_id", log.Get("android_id"))
	}
	if len(log.Get("android_id_md5")) > 0 {
		activateParams.Add("android_id_md5", log.Get("android_id_md5"))
	}
	if len(log.Get("idfa")) > 0 {
		activateParams.Add("idfa", log.Get("idfa"))
	}
	if len(log.Get("idfa_md5")) > 0 {
		activateParams.Add("idfa_md5", log.Get("idfa_md5"))
	}
	if len(log.Get("oaid")) > 0 {
		activateParams.Add("oaid", log.Get("oaid"))
	}
	if len(log.Get("oaid_md5")) > 0 {
		activateParams.Add("oaid_md5", log.Get("oaid_md5"))
	}
	if len(strings.TrimSpace(log.Get("model"))) > 0 {
		activateParams.Add("model", strings.TrimSpace(log.Get("model")))
	}
	if len(strings.TrimSpace(log.Get("manufacturer"))) > 0 {
		activateParams.Add("manufacturer", strings.TrimSpace(log.Get("manufacturer")))
	}
	if len(log.Get("caid_multi")) > 0 {
		activateParams.Add("caid_multi", log.Get("caid_multi"))
	}
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
		} else if planInfo.UnitPriceType == 1 {
		} else if planInfo.UnitPriceType == 2 {
			activateParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}

	if planInfo.IsOCPX == 1 && len(planInfo.OCPXPromotionTypesJson) > 0 {
		var tmpOCPCPromotionTypes []models.DspOCPXPromotionTypeStu
		json.Unmarshal([]byte(planInfo.OCPXPromotionTypesJson), &tmpOCPCPromotionTypes)

		var tmpArray []string
		for _, item := range tmpOCPCPromotionTypes {
			tmpArray = append(tmpArray, item.OCPXPromotionType)
		}
		activateParams.Add("accepted_transform_type", strings.Join(tmpArray, ","))
	}

	activateParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(activateParams.Encode()))
	return encodeStr
}

// WeiBoClkReportFromAdx ocpx
func WeiBoClkReportFromAdx(c *gin.Context, planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) {
	if planInfo == nil {
		return
	}

	if planInfo.IsOCPX == 1 {
	} else {
		return
	}

	os := mhCpaReq.Os
	// osv := log.Get("osv")
	// oaid := mhCpaReq.Oaid
	// model := log.Get("model")
	// manufacturer := log.Get("manufacturer")

	// ip := c.ClientIP()
	// ua := c.GetHeader("User-Agent")

	// callback url
	activateParams := url.Values{}
	encodeParams := EncodeWeiBoParamsFromAdx(planInfo, mhCpaReq, source)
	activateParams.Add("log", encodeParams)
	activateParams.Add("event", "__EVENT__")

	callback := config.ExternalWeiBoActiveURL + "?" + activateParams.Encode()

	ocpxURL := "http://sandbox.maplehaze.cn/api/test"
	client := &http.Client{}
	requestGet, _ := http.NewRequest("GET", ocpxURL, nil)
	requestGet = requestGet.WithContext(c)

	requestGet.Header.Add("Content-Type", "application/json; charset=utf-8")

	q := requestGet.URL.Query()

	// q := url.Values{}
	q.Add("os", os)
	if os == "android" {
		if len(mhCpaReq.Oaid) > 0 {
			q.Add("oaid_md5", utils.GetMd5(mhCpaReq.Oaid))
		} else if len(mhCpaReq.OaidMd5) > 0 {
			q.Add("oaid_md5", mhCpaReq.OaidMd5)
		} else {
			return
		}
	} else if os == "ios" {
		return
	}

	q.Add("ts", utils.ConvertInt64ToString(utils.GetCurrentMilliSecond()))
	q.Add("callback", callback)

	requestGet.URL.RawQuery = q.Encode()

	logger.GetSugaredLogger().Info("weibo ocpx callback: " + callback)
	logger.GetSugaredLogger().Info("weibo ocpx req: " + requestGet.URL.String())

	resp, err := client.Do(requestGet)
	if err != nil {
		logger.GetSugaredLogger().Errorf("weibo ocpx get request failed, err:[%s]", err.Error())
	}
	if resp == nil {
		logger.GetSugaredLogger().Error("weibo ocpx resp is nil")
		return
	}
	defer resp.Body.Close()

	bodyContent, _ := io.ReadAll(resp.Body)
	logger.GetSugaredLogger().Info("weibo ocpx resp: " + string(bodyContent))
}

// EncodeWeiBoParamsFromAdx ...
func EncodeWeiBoParamsFromAdx(planInfo *models.DspPlanStu, mhCpaReq *models.MHCpaReq, source string) string {
	bigdataParams := url.Values{}
	bigdataParams.Add("uid", mhCpaReq.UID)
	bigdataParams.Add("group_id", planInfo.GID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "1")
	bigdataParams.Add("ext_adx", mhCpaReq.Channel)

	// unit_price_type: 0 CPM, 1 CPC, 2 CPA
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
			bigdataParams.Add("cpm_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 1 {
			bigdataParams.Add("cpc_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 2 {
			bigdataParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	// ssp_price_type: 0 CPM, 1 CPC
	if planInfo.SspPriceNum > 0 {
		if planInfo.SspPriceType == 0 {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		} else if planInfo.SspPriceType == 1 {
			bigdataParams.Add("ext_cpc_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
		}
	}
	bigdataParams.Add("os", mhCpaReq.Os)
	if len(mhCpaReq.Imei) > 0 {
		bigdataParams.Add("imei", mhCpaReq.Imei)
	}
	if len(mhCpaReq.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", mhCpaReq.ImeiMd5)
	}
	if len(mhCpaReq.Oaid) > 0 {
		bigdataParams.Add("oaid", mhCpaReq.Oaid)
	}
	if len(mhCpaReq.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", mhCpaReq.OaidMd5)
	}
	if len(mhCpaReq.Idfa) > 0 {
		bigdataParams.Add("idfa", mhCpaReq.Idfa)
	}
	if len(mhCpaReq.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", mhCpaReq.IdfaMd5)
	}

	if len(mhCpaReq.Osv) > 0 {
		bigdataParams.Add("osv", mhCpaReq.Osv)
	}
	if len(mhCpaReq.DIDMd5) > 0 {
		bigdataParams.Add("did_md5", mhCpaReq.DIDMd5)
	}

	bigdataParams.Add("ip", mhCpaReq.IP)

	if planInfo.IsOCPX == 1 && len(planInfo.OCPXPromotionTypesJson) > 0 {
		var tmpOCPCPromotionTypes []models.DspOCPXPromotionTypeStu
		json.Unmarshal([]byte(planInfo.OCPXPromotionTypesJson), &tmpOCPCPromotionTypes)

		var tmpArray []string
		for _, item := range tmpOCPCPromotionTypes {
			tmpArray = append(tmpArray, item.OCPXPromotionType)
		}
		bigdataParams.Add("accepted_transform_type", strings.Join(tmpArray, ","))
	}

	bigdataParams.Add("source", source)

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}
