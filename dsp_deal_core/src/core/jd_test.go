package core

import (
	"dsp_core/models"
	"net/url"
	"testing"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func init() {
	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 确保程序退出前同步日志
	defer logger.Sync()
}

func TestAddJDDeviceInfoToParams(t *testing.T) {
	params := url.Values{}
	deviceInfo := JDDeviceInfo{
		OS:      "android",
		Oaid:    "test_oaid",
		Idfa:    "test_idfa",
		ImeiMd5: "test_imei_md5",
	}

	hasDeviceInfo := addJDDeviceInfoToParams(params, deviceInfo)

	assert.True(t, hasDeviceInfo)
	assert.Equal(t, "test_oaid", params.Get("oaid"))
	assert.Equal(t, "test_idfa", params.Get("idfa"))
}

func TestJDReport(t *testing.T) {
	c, _ := gin.CreateTestContext(nil)
	log := url.Values{}
	log.Add("plan_id", "72deff8913a90837")
	log.Add("os", "android")
	log.Add("oaid", "123456789012345")

	// 这个测试需要有效的计划信息，在实际环境中运行
	JDReport(c, log, "click")
}

func TestJDClkReportFromAdx(t *testing.T) {
	c, _ := gin.CreateTestContext(nil)
	planInfo := &models.DspPlanStu{
		PID:    "72deff8913a90837",
		IsOCPX: 1,
	}

	mhCpaReq := &models.MHCpaReq{
		UID:  "test_uid",
		Os:   "android",
		Oaid: "123456789012345",
	}

	// 这个测试需要网络连接，在实际环境中运行
	JDClkReportFromAdx(c, planInfo, mhCpaReq, "click")

}
