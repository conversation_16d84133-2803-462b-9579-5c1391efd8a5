BEGIN;

CREATE TABLE
    public.clk_data (
        id text NOT NULL,
        uid text NOT NULL,
        plan_id text NOT NULL,
        market_type text NOT NULL,
        ads_type text NOT NULL,
        ext_dsp_channel text NOT NULL,
        media_channel text NOT NULL,
        sub_channel_id text NOT NULL,
        ext_adx text NOT NULL,
        os text NOT NULL,
        osv text NOT NULL,
        imei text NOT NULL,
        imei_md5 text NOT NULL,
        android_id text NOT NULL,
        android_id_md5 text NOT NULL,
        idfa text NOT NULL,
        idfa_md5 text NOT NULL,
        ip text NOT NULL,
        ua text NOT NULL,
        oaid text NOT NULL,
        model text NOT NULL,
        manufacturer text NOT NULL,
        crid text NOT NULL,
        req_width text NOT NULL,
        req_height text NOT NULL,
        width text NOT NULL,
        height text NOT NULL,
        down_x text NOT NULL,
        down_y text NOT NULL,
        up_x text NOT NULL,
        up_y text NOT NULL,
        callback text NOT NULL,
        cpc_price int NOT NULL,
        ext_cpc_price int NOT NULL,
        dd text NOT NULL,
        hh text NOT NULL,
        mm text NOT NULL,
        report_time timestamp DEFAULT now(),
        PRIMARY KEY (id)
    );

-- 行存column
CALL set_table_property('public.clk_data', 'orientation', 'row');

-- 是否开启Binlog(replica:开启, none:关闭)
call set_table_property('public.clk_data', 'binlog.level', 'replica');

-- binlog.ttl，Binlog的TTL，单位为秒
call set_table_property('public.clk_data', 'binlog.ttl', '86400');

-- ttl 1天
CALL set_table_property('public.clk_data', 'time_to_live_in_seconds', '86400');

-- table_group 
call set_table_property('public.clk_data', 'table_group', 'dsp_rawdata_tg_10');

COMMIT;