package db

import (
	"dsp_core/utilities"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

// writeMode -> INSERT_OR_UPDATE
var GlbHologresAdxDSPUpdateDataDb *holoclient.HoloClient
var GlbHologresAdxDSPUpdateDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

var GlbHologresAdxDSPUpdateDataDbV3 *holoclient.HoloClient

// writeMode -> INSERT_OR_IGNORE
var GlbHologresAdxDSPIgnoreDataDb *holoclient.HoloClient
var GlbHologresAdxDSPIgnoreDataTableSchemas = map[string]*holoclient.HoloTableSchema{}

var GlbHologresAdxDSPIgnoreDataDbV3 *holoclient.HoloClient

func InitHologres() error {

	writeMode := utilities.HologresWriteMode
	batchSize := utilities.HologresBatchSize

	threadSize := utilities.HologresThreadSize

	writeBatchByteSize := utilities.HologresWriteBatchByteSize

	writeMaxIntervalMs := utilities.HologresWriteMaxIntervalMs

	// adx_dsp writeMode -> update
	hologresUpdateClient := databases.NewHologres(
		utilities.PostgresAdxDSPDataDSN,
		1,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxDSPUpdateDataDb = hologresUpdateClient

	// 初始化 v3
	hologresUpdateClientV3 := databases.NewHologres(
		utilities.PostgresAdxDSPDataDSNV3,
		1,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxDSPUpdateDataDbV3 = hologresUpdateClientV3

	// adx_dsp writeMode -> ignore
	hologresIgnoreClient := databases.NewHologres(
		utilities.PostgresAdxDSPDataDSN,
		writeMode,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxDSPIgnoreDataDb = hologresIgnoreClient

	// 初始化 v3
	hologresIgnoreClientV3 := databases.NewHologres(
		utilities.PostgresAdxDSPDataDSNV3,
		writeMode,
		batchSize,
		threadSize,
		writeBatchByteSize,
		writeMaxIntervalMs,
	)

	GlbHologresAdxDSPIgnoreDataDbV3 = hologresIgnoreClientV3

	NewHologresAdxDSPDataIgnoreTableSchema("deal", "exp_data")
	NewHologresAdxDSPDataIgnoreTableSchema("deal", "clk_data")
	NewHologresAdxDSPDataIgnoreTableSchema("deal", "conversion_data")
	NewHologresAdxDSPDataIgnoreTableSchema("debug", "debug_data")

	// writeMode -> update
	// NewHologresAdxDSPDataUpdateTableSchema("debug", "debug_xxx")

	return nil
}

// CloseHologres...
func CloseHologres() {
	if GlbHologresAdxDSPUpdateDataDb != nil {
		GlbHologresAdxDSPUpdateDataDb.Close()
	}

	if GlbHologresAdxDSPIgnoreDataDb != nil {
		GlbHologresAdxDSPIgnoreDataDb.Close()
	}

	if GlbHologresAdxDSPUpdateDataDbV3 != nil {
		GlbHologresAdxDSPUpdateDataDbV3.Close()
	}

	if GlbHologresAdxDSPIgnoreDataDbV3 != nil {
		GlbHologresAdxDSPIgnoreDataDbV3.Close()
	}
}

// func NewHologresAdxDSPDataUpdateTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
// 	schema := GlbHologresAdxDSPUpdateDataTableSchemas[tableName]
// 	if schema != nil {
// 		//log.Println("[HOLO]has same name schema")
// 		return schema
// 	} else {
// 		newSchema := GlbHologresAdxDSPUpdateDataDb.GetTableschema(schemaName, tableName, false)
// 		GlbHologresAdxDSPUpdateDataTableSchemas[tableName] = newSchema
// 		//log.Println("[HOLO]new schema")
// 		return newSchema
// 	}
// }

func NewHologresAdxDSPDataIgnoreTableSchema(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxDSPIgnoreDataTableSchemas[schemaName+tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxDSPIgnoreDataDb.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxDSPIgnoreDataTableSchemas[schemaName+tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}
