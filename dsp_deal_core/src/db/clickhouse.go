package db

import (
	"dsp_core/config"
	"log"

	"database/sql"

	"github.com/ClickHouse/clickhouse-go"
)

// GlbClickHouseDb ...
var GlbClickHouseDb *sql.DB

// InitClickHouse ...
func InitClickHouse() error {
	clickHouseDB, err := sql.Open("clickhouse", config.ClickHouseHostPort)
	if err != nil {
		log.Println(err)
	}
	if err := clickHouseDB.Ping(); err != nil {
		if exception, ok := err.(*clickhouse.Exception); ok {
			log.Printf("[%d] %s \n%s\n", exception.Code, exception.Message, exception.StackTrace)
		} else {
			log.Println(err)
		}
		return err
	}
	GlbClickHouseDb = clickHouseDB

	return nil
}

// CloseClickHouse...
func CloseClickHouse() {
	if GlbClickHouseDb == nil {
		return
	}
	GlbClickHouseDb.Close()
}

// DROP TABLE dsp_rawdata.xxxxxx ON CLUSTER default

// 1. create database
// CREATE DATABASE IF NOT EXISTS dsp_rawdata ON CLUSTER default
// 2. create exp table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.exp_data ON CLUSTER default (
// 	`uid`                String,
// 	`plan_id`            String,
// 	`market_type`        String,
// 	`ads_type`           String,
// 	`ext_dsp_channel`    String,
// 	`media_channel`      String,
// 	`sub_channel_id`     String,
// 	`ext_adx`            String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`crid`               String,
// 	`cpm_price`          Int32,
// 	`ext_cpm_price`      Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX plan_id_index plan_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/dsp_rawdata/exp_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 3. create clk table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.clk_data ON CLUSTER default (
// 	`uid`                String,
// 	`plan_id`            String,
// 	`market_type`        String,
// 	`ads_type`           String,
// 	`ext_dsp_channel`    String,
// 	`media_channel`      String,
// 	`sub_channel_id`     String,
// 	`ext_adx`            String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`crid`               String,
// 	`req_width`          String,
// 	`req_height`         String,
// 	`width`              String,
// 	`height`             String,
// 	`down_x`             String,
// 	`down_y`             String,
// 	`up_x`               String,
// 	`up_y`               String,
// 	`callback`           String,
// 	`cpc_price`          Int32,
// 	`ext_cpc_price`      Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX plan_id_index plan_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/dsp_rawdata/clk_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 4. create req table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.req_data ON CLUSTER default (
// 	`uid`                String,
// 	`plan_id`            String,
// 	`market_type`        String,
// 	`ads_type`           String,
// 	`ext_dsp_channel`    String,
// 	`media_channel`      String,
// 	`sub_channel_id`     String,
// 	`ext_adx`            String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`crid`               String,
// 	`code`               Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX plan_id_index plan_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/dsp_rawdata/req_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// TTL report_time + INTERVAL 1 DAY
// ORDER BY report_time SETTINGS index_granularity = 8192

// 5. create active table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.active_data ON CLUSTER default (
// 	`uid`                String,
// 	`plan_id`            String,
// 	`market_type`        String,
// 	`ads_type`           String,
// 	`ext_dsp_channel`    String,
// 	`media_channel`      String,
// 	`sub_channel_id`     String,
// 	`ext_adx`            String,
// 	`os`                 String,
// 	`osv`                String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`android_id`         String,
// 	`android_id_md5`     String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`ua`                 String,
// 	`oaid`               String,
// 	`model`              String,
// 	`manufacturer`       String,
// 	`crid`               String,
// 	`cpa_price`          Int32,
// 	`ext_cpa_price`      Int32,
// 	`callback`           String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX plan_id_index plan_id TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/dsp_rawdata/active_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// ORDER BY report_time SETTINGS index_granularity = 8192

// 6. create debug table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.debug_data ON CLUSTER default (
// 	`req`                String,
// 	`resp`               String,
// 	`os`                 String,
// 	`imei`               String,
// 	`imei_md5`           String,
// 	`idfa`               String,
// 	`idfa_md5`           String,
// 	`ip`                 String,
// 	`oaid`               String,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedMergeTree('/clickhouse/dsp_rawdata/debug_data/{shard}', '{replica}')
// PARTITION BY toYYYYMMDD(report_time)
// ORDER BY report_time SETTINGS index_granularity = 8192

// 3. create active callback table
// CREATE TABLE IF NOT EXISTS dsp_rawdata.active_callback_data ON CLUSTER default (
// 	`uid`                String,
// 	`callback`           String,
// 	`status`             Int32,
// 	`dd`                 Date,
// 	`hh`                 String,
// 	`mm`                 String,
// 	`report_time`        DateTime,
// 	INDEX uid_index uid TYPE minmax GRANULARITY 30,
// 	INDEX dd_index dd TYPE minmax GRANULARITY 10,
// 	INDEX hh_index hh TYPE minmax GRANULARITY 10,
// 	INDEX mm_index mm TYPE minmax GRANULARITY 10,
// 	INDEX report_index report_time TYPE minmax GRANULARITY 10
// ) ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/active_callback_data/{shard}', '{replica}', report_time)
// PARTITION BY toYYYYMMDD(report_time)
// ORDER BY (uid, callback) SETTINGS index_granularity = 8192
