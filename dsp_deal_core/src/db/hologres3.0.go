package db

import (
	"dsp_core/utilities"
	"sync"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
)

// writeMode -> INSERT_OR_UPDATE
var GlbHologresAdxDSPUpdateDataDb3 *holoclient.HoloClient
var GlbHologresAdxDSPUpdateDataTableSchemas3 = map[string]*holoclient.HoloTableSchema{}

// writeMode -> INSERT_OR_IGNORE
var GlbHologresAdxDSPIgnoreDataDb3 *holoclient.HoloClient
var GlbHologresAdxDSPIgnoreDataTableSchemas3 = map[string]*holoclient.HoloTableSchema{}

var onceHologres3 sync.Once

func InitHologres3() error {
	onceHologres3.Do(func() {
		writeMode := utilities.HologresWriteMode
		batchSize := utilities.HologresBatchSize

		threadSize := utilities.HologresThreadSize

		writeBatchByteSize := utilities.HologresWriteBatchByteSize

		writeMaxIntervalMs := utilities.HologresWriteMaxIntervalMs

		// 公网地址：   hgpostcn-cn-v3m4cxdml00k-cn-beijing.hologres.aliyuncs.com:80
		// 内网地址 ： hgpostcn-cn-v3m4cxdml00k-cn-beijing-internal.hologres.aliyuncs.com:80
		// vpc 地址 ： hgpostcn-cn-v3m4cxdml00k-cn-beijing-vpc-st.hologres.aliyuncs.com:80

		// adx_dsp writeMode -> update
		hologresUpdateClient := databases.NewHologres(
			"host=hgpostcn-cn-v3m4cxdml00k-cn-beijing.hologres.aliyuncs.com port=80 dbname=adx_dsp user=BASIC$maplehaze password=Mhint@123 sslmode=disable",
			1,
			batchSize,
			threadSize,
			writeBatchByteSize,
			writeMaxIntervalMs,
		)

		GlbHologresAdxDSPUpdateDataDb3 = hologresUpdateClient

		// adx_dsp writeMode -> ignore
		hologresIgnoreClient := databases.NewHologres(
			"host=hgpostcn-cn-v3m4cxdml00k-cn-beijing.hologres.aliyuncs.com port=80 dbname=adx_dsp user=BASIC$maplehaze password=Mhint@123 sslmode=disable",
			writeMode,
			batchSize,
			threadSize,
			writeBatchByteSize,
			writeMaxIntervalMs,
		)

		GlbHologresAdxDSPIgnoreDataDb3 = hologresIgnoreClient
		NewHologresAdxDSPDataIgnoreTableSchema3("deal", "exp_data")
		NewHologresAdxDSPDataIgnoreTableSchema3("deal", "clk_data")
		NewHologresAdxDSPDataIgnoreTableSchema3("deal", "conversion_data")
		NewHologresAdxDSPDataIgnoreTableSchema3("debug", "debug_data")

		// writeMode -> update
		// NewHologresAdxDSPDataUpdateTableSchema("debug", "debug_xxx")

	})

	return nil
}

// CloseHologres...
func CloseHologres3() {
	if GlbHologresAdxDSPUpdateDataDb3 != nil {
		GlbHologresAdxDSPUpdateDataDb3.Close()
	}

	if GlbHologresAdxDSPIgnoreDataDb3 != nil {
		GlbHologresAdxDSPIgnoreDataDb3.Close()
	}

}

func NewHologresAdxDSPDataIgnoreTableSchema3(schemaName string, tableName string) *holoclient.HoloTableSchema {
	schema := GlbHologresAdxDSPIgnoreDataTableSchemas3[schemaName+tableName]
	if schema != nil {
		//log.Println("[HOLO]has same name schema")
		return schema
	} else {
		newSchema := GlbHologresAdxDSPIgnoreDataDb3.GetTableschema(schemaName, tableName, false)
		GlbHologresAdxDSPIgnoreDataTableSchemas3[schemaName+tableName] = newSchema
		//log.Println("[HOLO]new schema")
		return newSchema
	}
}
