package db

import (
	"database/sql"
	"dsp_core/config"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// GlbMySQLDb ...
var GlbMySQLDspDb *sql.DB

// InitMysql ...
func InitMysql() (err error) {
	// db, err := sql.Open("mysql", "user:password@/dbname")
	// db, err := sql.Open("mysql", "root:12345678@tcp(127.0.0.1:3306)/adx")

	// dsp mysql
	mysqlDSPDB, err := sql.Open("mysql", config.MySQLUserName+":"+config.MySQLPassword+"@tcp("+config.MySQLHost+":"+config.MySQLPort+")/"+config.MySQLDspDbname)
	mysqlDSPDB.SetMaxOpenConns(30)
	mysqlDSPDB.SetMaxIdleConns(15)
	mysqlDSPDB.SetConnMaxLifetime(10 * time.Minute)

	GlbMySQLDspDb = mysqlDSPDB

	if err != nil {
		log.Printf("init mysql err:%v", err)
		return err
	}

	err = mysqlDSPDB.Ping()
	if err != nil {
		log.Printf("init mysql ping err:%v", err)
		return err
	}
	return nil
}

// CloseMysql...
func CloseMysql() {
	if GlbMySQLDspDb == nil {
		return
	}
	GlbMySQLDspDb.Close()
}
