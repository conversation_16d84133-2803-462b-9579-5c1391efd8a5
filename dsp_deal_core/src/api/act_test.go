package api

import (
	"dsp_core/utils"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"testing"
)

// 生成测试用的log参数
func generateTestLogParam() (string, error) {
	// 创建测试参数
	params := url.Values{}
	params.Add("uid", "test_uid_1233")
	params.Add("group_id", "560ed53d3e312922")
	params.Add("plan_id", "3502c607274401c1") // 使用与createMockPlanInfo中相同的PID
	params.Add("market_type", "1")
	params.Add("ads_type", "1")
	params.Add("ext_dsp_channel", "test_channel")
	params.Add("media_channel", "test_media")
	params.Add("sub_channel_id", "test_sub_channel")
	params.Add("ext_adx", "test_adx")
	params.Add("os", "android")
	params.Add("osv", "10.0")
	params.Add("imei", "123456789012345")
	params.Add("imei_md5", "md5_of_imei")
	// params.Add("android_id", "android_id_value")
	params.Add("source", "click")

	// 编码参数
	encoded, err := utils.EncodeString([]byte(params.Encode()))
	if err != nil {
		return "", errors.New(fmt.Sprintf("编码参数失败: %v", err))
	}

	return encoded, nil
}

// TestAlipayAct 测试支付宝回调接口
func TestAlipayAct(t *testing.T) {
	// 生成加密的log参数
	logParam, err := generateTestLogParam()
	if err != nil {
		t.Fatalf("生成测试参数失败: %v", err)
	}

	// 构建转化类型参数
	transformType := "1" // 1=新登

	// 生成curl命令
	curlCmd := fmt.Sprintf("curl -X GET 'https://dsp.maplehaze.cn/act/alipay?transformtype=%s&log=%s'", transformType, url.QueryEscape(logParam))

	// 输出测试信息
	fmt.Printf("支付宝回调测试curl命令: %s\n\n", curlCmd)
}

func TestAlipayCallback(t *testing.T) {
	logQuery := "W4Ch5DwXu7YhMwFVAVKxxm7UTEjKIqwZ8XHRvRszpWmY9ffI0UBp/xVS6jHzKd3OQ0qCmjSV3ESI03fgILielfx81/ o86GHm36Pp1aRN9eJXq3 oRMmfGi8SltiADtLf6/Qh0uLd1Krwv2XM2ofC5qDAEQgCHVAP9XYD5GFeE55rEyrN8fi m42Gm/X8UWi LtQyv7S3SuxeIvZqrV4hJHE5jTBpAuS52d9rW34FVG0i3I/G9z5JlvZDr5QBTnxcW6gPMtaJUA40kfxig6ENIaTUHryEQDdn9n3lTalrHcjbCWUOmYXgUokTlqOQWb9Q3n2qSEveasAvKAZbXPBveTompi7gB2tPU ZdvA3cbiKpgat aUPM7S4A6rtsi4 P/yYODf4mnjItMAk11cKzB/MsMUyzPgm3BKRBo0RqN09nFpWAK319riq DTouyC9K5ceL8qeCPsi9/XlGdR uKa92J2eWefp hm8Czt2zJ0RcJDO11l23Yfvh8cWxjxI"
	// 步骤1: 替换空格为+号
	replaceData := strings.Replace(logQuery, " ", "+", -1)
	// 步骤2: URL解码（注意：这里应该是QueryUnescape，不是QueryEscape）
	// rawData, err := url.QueryUnescape(replaceData)
	// if err != nil {
	// 	t.Errorf("URL解码失败: %v", err)
	// 	return
	// }

	// 步骤3: AES解密
	logByte, err := utils.DecodeString(replaceData)
	if err != nil {
		t.Errorf("AES解密失败: %v", err)
		return
	}

	// 步骤4: 解析查询参数
	log, err := url.ParseQuery(string(logByte))
	if err != nil {
		t.Errorf("解析查询参数失败: %v", err)
		return
	}
	fmt.Printf("解析成功，参数: %v\n", log)
}
