package api

import (
	"dsp_core/utils"
	"fmt"
	"net/url"
	"testing"
)

func TestClk(t *testing.T) {
	// 构建log参数
	log := url.Values{}
	log.Add("uid", "0ef5419b-e83d-4f75-aabe-31472a39b0b8")
	log.Add("group_id", "560ed53d3e312922")
	log.Add("plan_id", "3502c607274401c1")
	log.Add("market_type", "1")
	log.Add("ads_type", "1")
	log.Add("ext_dsp_channel", "13")
	log.Add("media_channel", "1")
	log.Add("ext_adx", "105")
	log.Add("os", "android")
	log.Add("imei", "aac4bc239553c500c1a9be35269066f0")
	log.Add("caid", "aac4bc239553c500c1a9be35269066f0")
	log.Add("caid_version", "20230330")

	// 将参数编码为字符串
	logStr := log.Encode()

	// 使用EncodeString加密
	ret, err := utils.EncodeString([]byte(logStr))
	if err != nil {
		fmt.Printf("encode error: %v\n", err)
		return
	}

	dRet, err := utils.DecodeString(ret)
	if err != nil {
		fmt.Printf("decode error: %v\n", err)
		return
	}
	fmt.Printf("decode result: %v\n", string(dRet))
	// 打印加密后的参数
	fmt.Printf("加密后的log参数: %v\n\n", ret)

	// 打印curl请求示例，使用url.QueryEscape对ret进行URL编码
	fmt.Printf("curl -X GET \"https://dsp.maplehaze.cn/api/clk?log=%v\"\n\n", url.QueryEscape(ret))
}
