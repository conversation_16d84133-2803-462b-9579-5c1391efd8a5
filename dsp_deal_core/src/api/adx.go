package api

import (
	"dsp_core/core"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"log"
	"math/rand"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	deviceidgenerate "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate"
	deviceidgeneratemodel "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/device_id_generate/models"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// 快手广告平台链接
// http://localhost:8082/adx/report?planid=abcdef&starid=10000&channel=ks&imeimd5=IMEI_KS&oaid=oaid1&oaidmd5=oaid2&idfamd5=__IDFA2__&ip=************&os=0&callback=CALLBACK_KS
// https://dsp.maplehaze.cn/cpa/act?log=EdiVY%2BLIIj87fTCL5%2BWJFdd%2FpQ%2B6TZmu9gUiBgZXsMoDWGAbpgZR8zZ0DPzLidQk3A0GaArE%2FSShYbqDLxFHByyvAJnhxRSuXbBLFMux53SO%2BJPP2qAtdteb9D3CILQVGF57RkPE3kUm2Jqnnc5OaO22Uirji0u0GIzl9cUX2vOozeJugv2DU9OjQabtxibqvHPdyhNuPwF1uxIofFuITfgYHRF5rXTJ3PR8oDKe5xFeEuSux967%2F8NFwvbZUx4AYqrKGZ%2FL5Ul6rAHLlHEf5cUrMbd6hJYMRXsShZbLg%2FTrO%2FhGKVgD1C5N6mEoL3ZF9ZwX9aLbFZmKSrMI0y6m4Q%3D%3D
// http://localhost:8082/adx/report?planid=f2f29318d6889986&channel=ks&imeimd5=IMEI_KS1&idfamd5=__IDFA2__&ip=************&oaid=oaid1&os=0&callback=CALLBACK_KS1
// https://dsp.maplehaze.cn/cpa/act?log=EdiVY%2BLIIj87fTCL5%2BWJFdd%2FpQ%2B6TZmu9gUiBgZXsMoDWGAbpgZR8zZ0DPzLidQkg%2BO42c3KvC55mYdK4DPTlrUyH5tCWA95ShVgLTpuI3%2Byd2FlEvw1ge0fASvbbJTAV4BbPA34yec5hZC0W4TFD0e8WlTma9KPWT5tKotWBTM6SVWlhuptaAfptIqzl%2B2cAySCOnhf83BYP3zUz8j0oPPeOYVzVOScIWQvHM3d9NuLnyS79nDwcJJmpaISLfPvrNyhvJAU6ehGRoya0vW51wYQmaNrnTtXRT05pnmjGFg%3D

// 巨量引擎广告平台链接
// http://localhost:8082/adx/report?planid=f2f29318d6889986&starid=10001&channel=oceanengine&imeimd5=IMEI_DOUYIN&idfa=__IDFA__&ip=************&oaid=oaid2&os=0&callback=CALLBACK_DOUYIN
// https://dsp.maplehaze.cn/cpa/act?log=EdiVY%2BLIIj87fTCL5%2BWJFdd%2FpQ%2B6TZmu9gUiBgZXsMrpkgdyETtyjFJxON0i%2FYL6PxfazLPuH3yze1Fe%2Fsza%2FJK6hZkSbc4EkdqZ20d8LbEb%2Bz%2FC0JeX1IzTLGcJJ%2FNNC2U5zmnDh%2Bf8IU3tqKnv8fOebWQ1bJaJmsXbaMqtLSJppYl1MpN1REJ%2BNgYf6kHVbKTWrcP3ep1XMDw0f6gK4%2BISnZUotOZt2HC4ZmDbKYuhU2fMy13uN%2BxMlwiHACso1Wp4DsiHSGbNP5tR2LvvdN3IO6nGh5wFUrvvjZwJdmv8DPzTuXdfTRe5Jv1LQUpXX3bd1FVEmFteQl%2FoAK7hhQ%3D%3D
// http://localhost:8082/adx/report?planid=f2f29318d6889986&channel=oceanengine&imeimd5=IMEI_DOUYIN1&idfa=__IDFA__&ip=************&oaid=oaid1&os=0&callback=CALLBACK_DOUYIN1
// https://dsp.maplehaze.cn/cpa/act?log=EdiVY%2BLIIj87fTCL5%2BWJFdd%2FpQ%2B6TZmu9gUiBgZXsMrpkgdyETtyjFJxON0i%2FYL6jTSap4ylG%2FTGUL9mQaFKSP9I3eea%2B%2B0Pl4NTb31fRLhK6%2F4Dbn2wQ%2Fp%2Fe8DJHFzCzj%2FEf5rzh7yPN%2B0py%2FZ9BrNEfv7xxESomWw%2FNG2QrZQXawb6LTzwitc36eaxqRHCt%2BfikErxy3LBdcAJgsCQu118fDOuLqT6oztGUti4ncwSMv1qYD22gfZslxlCzlvJn9k0TooLsdUr6q2jXgYHSxFviaX6GVmWEzU%2B21RU7t0%3D

// 快手广告平台链接
// https://dsp.maplehaze.cn/adx/report?planid=xxxxxx&starid=xxxxxx&channel=ks&imeimd5=__IMEI2__&idfamd5=__IDFA2__&ip=__IP__&oaid=__OAID__&oaidmd5=__OAID2__&os=__OS__&callback=__CALLBACK__
// 巨量引擎广告平台链接
// https://dsp.maplehaze.cn/adx/report?planid=xxxxxx&starid=xxxxxx&channel=oceanengine&imeimd5=__IMEI__&idfa=__IDFA__&ip=__IP__&oaid=__OAID__&os=__OS__&callback=__CALLBACK_URL__

// 斗鱼
// key: 7491e7f03ecf48d0a54a604df50d8f85
// https://www.douyu.com?a1=[[_DYSOURCE_]]&a2=[[_DYADID_]]&a3=[[_DYREQUESTID_]]&a4=[[_DYIDFA_]]&a5=[[_DYIMEI_]]&a6=[[_DYOAID_]]&a7=[[_DYANID_]]&a8=[[_DYDTYPE_]]&a9=[[_DYDVERSION_]]&a10=[[_DYOSTYPE_]]&a11=[[_DYOSVERSION_]]&a12=[[_DYMAC_]]&a13=[[_DYIP_]]&a14=[[_DYANAME_]]&a15=[[_DYHEIGHT_]]&a16=[[_DYWIDTH_]]&a17=[[_DYUSERAGENT_]]&a18=[[_DYID_]]&a19=[[_DYCALLBACK_]]
// https://dsp.maplehaze.cn/adx/report?planid=cc7d2bac1cade310&starid=10001&channel=douyu&imeimd5=[[_DYIMEI_]]&idfamd5=[[_DYIDFA_]]&ip=[[_DYIP_]]&oaidmd5=[[_DYOAID_]]&os=[[_DYOSTYPE_]]&callback=[[_DYCALLBACK_]]&dysrc=[[_DYSOURCE_]]&dyid=[[_DYID_]]
// http://localhost:8082/adx/report?planid=f2f29318d6889986&starid=10001&channel=douyu&imeimd5=[[_DYIMEI_]]&idfamd5=[[_DYIDFA_]]&ip=[[_DYIP_]]&oaidmd5=[[_DYOAID_]]&os=[[_DYOSTYPE_]]&callback=[[_DYCALLBACK_]]&dysrc=[[_DYSOURCE_]]&dyid=[[_DYID_]]

// baidu
// https://dsp.maplehaze.cn/adx/report?planid=df25b308ccd977d4&channel=baidu&imeimd5=__IMEI__&idfa=__IDFA__&ip=__IP__&oaid=__OAID__&os=__OS__&callback=__CALLBACK_URL__

// 最新
// https://dsp.maplehaze.cn/adx/report?planid=ab025a2ca3f1f804&channel=ks&imeimd5=__IMEI2__&idfamd5=__IDFA2__&ip=__IP__&oaid=__OAID__&oaidmd5=__HASH_OAID__&os=__OS__&callback=__CALLBACK__

// AdxExp ...
func AdxExp(c *gin.Context) {
	// log.Println("adx report")

	channel := c.Query("channel")

	if channel == "103" || channel == "105" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfaMd5 := c.Query("idfamd5")
		oaid := c.Query("oaid")
		oaidMd5 := c.Query("oaidmd5")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		// log.Println(planID)
		// log.Println(imeiMd5)
		// log.Println(idfaMd5)
		// log.Println(oaid)
		// log.Println(os)
		// log.Println(ip)
		// log.Println(callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = strings.ToLower(strings.Replace(imeiMd5, "__IMEI2__", "", -1))
		mhCpaReq.IdfaMd5 = strings.ToLower(strings.Replace(idfaMd5, "__IDFA2__", "", -1))
		mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
		oaidMd5 = strings.Replace(oaidMd5, "__OAID2__", "", -1)
		oaidMd5 = strings.ToLower(oaidMd5)
		mhCpaReq.OaidMd5 = oaidMd5
		os = strings.ToLower(strings.Replace(os, "__OS__", "", -1))
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		} else if len(mhCpaReq.IdfaMd5) > 0 {
			mhCpaReq.Os = "ios"
		} else if len(mhCpaReq.ImeiMd5) > 0 || len(mhCpaReq.Oaid) > 0 {
			mhCpaReq.Os = "android"
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		// logger.GetSugaredLogger().Infof("AdxExp channel=103/105 mhCpaReq=%v", mhCpaReq)

		ExpFromAdx(c, planID, &mhCpaReq)
	} else if channel == "101" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfa := c.Query("idfa")
		oaid := c.Query("oaid")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		// log.Println(planID)
		// log.Println(imeiMd5)
		// log.Println(idfa)
		// log.Println(oaid)
		// log.Println(os)
		// log.Println(ip)
		// log.Println(callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = strings.ToLower(strings.Replace(imeiMd5, "__IMEI__", "", -1))
		mhCpaReq.Idfa = strings.ToLower(strings.Replace(idfa, "__IDFA__", "", -1))
		mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
		os = strings.ToLower(strings.Replace(os, "__OS__", "", -1))
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK_URL__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		// logger.GetSugaredLogger().Infof("AdxExp channel=101, mhCpaReq=%v", mhCpaReq)

		ExpFromAdx(c, planID, &mhCpaReq)
	} else if channel == "104" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfa := c.Query("idfa")
		oaid := c.Query("oaid")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")
		aKey := "Mzc4NzMwMTA="

		if strings.ToLower(imeiMd5) == "null" {
			imeiMd5 = ""
		}

		if strings.ToLower(idfa) == "null" {
			idfa = ""
		}

		if strings.ToLower(oaid) == "null" {
			oaid = ""
		}

		callback = strings.Replace(callback, "{{ATYPE}}", "feed_deeplink", -1)
		callback = strings.Replace(callback, "{{AVALUE}}", "0", -1)

		callbackSign := utils.GetMd5(callback + aKey)
		callback = callback + "&sign=" + callbackSign

		// log := logger.GetSugaredLogger()
		// debug
		// log.Info("AdxERxp planId=%v, imeiMd5=%v, idfa=%v, oaid=%v, os=%v, ip=%v, callback=%v", planID, imeiMd5, idfa, oaid, os, ip, callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = imeiMd5
		mhCpaReq.Idfa = idfa
		mhCpaReq.Oaid = oaid
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		} else {
			return
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK_URL__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		// log.Println(mhCpaReq)

		ExpFromAdx(c, planID, &mhCpaReq)
	} else if channel == "102" {
		planID := c.Query("planid")
		// muid := c.Query("muid")
		oaidMd5 := c.Query("oaidmd5")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		// mhCpaReq.IdfaMd5 = strings.ToLower(strings.Replace(idfaMd5, "__MUID__", "", -1))
		mhCpaReq.OaidMd5 = strings.ToLower(strings.Replace(oaidMd5, "__HASH_OAID__", "", -1))
		os = strings.ToLower(strings.Replace(os, "__DEVICE_OS_TYPE__", "", -1))
		if os == "android" {
			mhCpaReq.Os = "android"
		} else if os == "ios" {
			mhCpaReq.Os = "ios"
		}

		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)

		mhCpaReq.ClickId = c.Query("clickid")
		mhCpaReq.ClickTime = c.Query("clicktime")
		// logger.GetSugaredLogger().Infof("AdxClk channel=102 mhCpaReq=%v", mhCpaReq)

		ExpFromAdx(c, planID, &mhCpaReq)
	}

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok adx"
	c.PureJSON(200, resp)
}

// AdxClk ...
func AdxClk(c *gin.Context) {
	// log.Println("adx report")

	channel := c.Query("channel")

	if channel == "ks" || channel == "103" || channel == "105" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfaMd5 := c.Query("idfamd5")
		oaid := c.Query("oaid")
		oaidMd5 := c.Query("oaidmd5")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		// log.Println(planID)
		// log.Println(imeiMd5)
		// log.Println(idfaMd5)
		// log.Println(oaid)
		// log.Println(os)
		// log.Println(ip)
		// log.Println(callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = strings.ToLower(strings.Replace(imeiMd5, "__IMEI2__", "", -1))
		mhCpaReq.IdfaMd5 = strings.ToLower(strings.Replace(idfaMd5, "__IDFA2__", "", -1))
		mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
		oaidMd5 = strings.Replace(oaidMd5, "__OAID2__", "", -1)
		oaidMd5 = strings.ToLower(oaidMd5)
		mhCpaReq.OaidMd5 = oaidMd5
		os = strings.ToLower(strings.Replace(os, "__OS__", "", -1))
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		} else if len(mhCpaReq.IdfaMd5) > 0 {
			mhCpaReq.Os = "ios"
		} else if len(mhCpaReq.ImeiMd5) > 0 || len(mhCpaReq.Oaid) > 0 {
			mhCpaReq.Os = "android"
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		logger.GetSugaredLogger().Infof("AdxClk channel=ks/103/105 mhCpaReq=%v", mhCpaReq)

		ClkFromAdx(c, planID, &mhCpaReq)
	} else if channel == "oceanengine" || channel == "101" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfa := c.Query("idfa")
		oaid := c.Query("oaid")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		// log.Println(planID)
		// log.Println(imeiMd5)
		// log.Println(idfa)
		// log.Println(oaid)
		// log.Println(os)
		// log.Println(ip)
		// log.Println(callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = strings.ToLower(strings.Replace(imeiMd5, "__IMEI__", "", -1))
		mhCpaReq.Idfa = strings.ToLower(strings.Replace(idfa, "__IDFA__", "", -1))
		mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
		os = strings.ToLower(strings.Replace(os, "__OS__", "", -1))
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK_URL__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		log.Println(mhCpaReq)

		ClkFromAdx(c, planID, &mhCpaReq)
	} else if channel == "baidu" || channel == "104" {
		planID := c.Query("planid")
		imeiMd5 := c.Query("imeimd5")
		idfa := c.Query("idfa")
		oaid := c.Query("oaid")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")
		aKey := "Mzc4NzMwMTA="

		if strings.ToLower(imeiMd5) == "null" {
			imeiMd5 = ""
		}

		if strings.ToLower(idfa) == "null" {
			idfa = ""
		}

		if strings.ToLower(oaid) == "null" {
			oaid = ""
		}

		callback = strings.Replace(callback, "{{ATYPE}}", "feed_deeplink", -1)
		callback = strings.Replace(callback, "{{AVALUE}}", "0", -1)

		callbackSign := utils.GetMd5(callback + aKey)
		callback = callback + "&sign=" + callbackSign

		// debug
		log.Println("debug 0")
		log.Println(planID)
		log.Println(imeiMd5)
		log.Println(idfa)
		log.Println(oaid)
		log.Println(os)
		log.Println(ip)
		log.Println(callback)
		log.Println("debug 1")

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = imeiMd5
		mhCpaReq.Idfa = idfa
		mhCpaReq.Oaid = oaid
		if os == "0" {
			mhCpaReq.Os = "android"
		} else if os == "1" {
			mhCpaReq.Os = "ios"
		} else {
			return
		}
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK_URL__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		// log.Println(mhCpaReq)

		ClkFromAdx(c, planID, &mhCpaReq)
	} else if channel == "ams" || channel == "102" {
		planID := c.Query("planid")
		// muid := c.Query("muid")
		oaidMd5 := c.Query("oaidmd5")
		os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		// mhCpaReq.IdfaMd5 = strings.ToLower(strings.Replace(idfaMd5, "__MUID__", "", -1))
		mhCpaReq.OaidMd5 = strings.ToLower(strings.Replace(oaidMd5, "__HASH_OAID__", "", -1))
		os = strings.ToLower(strings.Replace(os, "__DEVICE_OS_TYPE__", "", -1))
		if os == "android" {
			mhCpaReq.Os = "android"
		} else if os == "ios" {
			mhCpaReq.Os = "ios"
		}

		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)

		mhCpaReq.ClickId = c.Query("clickid")
		mhCpaReq.ClickTime = c.Query("clicktime")
		log.Println(mhCpaReq)

		ClkFromAdx(c, planID, &mhCpaReq)
	} else if channel == "106" {
		// 爱思助手
		planID := c.Query("planid")
		ua := c.Query("ua")
		idfa := c.Query("idfa")
		osv := c.Query("osv")
		model := c.Query("model")
		ip := c.Query("ip")
		callback := c.Query("callback")
		caidmultijson := c.Query("caid")

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.UA = strings.Replace(ua, "{ua}", "", -1)
		mhCpaReq.Osv = strings.Replace(osv, "{os}", "", -1)
		mhCpaReq.Idfa = strings.Replace(idfa, "{idfa}", "", -1)
		if strings.Contains(mhCpaReq.Idfa, "00000000") {
			mhCpaReq.Idfa = ""
		}
		mhCpaReq.Model = strings.Replace(model, "{model}", "", -1)
		json.Unmarshal([]byte(caidmultijson), &mhCpaReq.CAIDMulti)
		mhCpaReq.Os = "ios"
		mhCpaReq.Manufacturer = "Apple"

		mhCpaReq.IP = strings.Replace(ip, "{ip}", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "{callback}", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)
		// tmpJsonData, _ := json.Marshal(mhCpaReq)
		// log.Println("kbg_debug_aisi resp: ", string(tmpJsonData))

		// tmpCAIDMulitJsonData, _ := json.Marshal(mhCpaReq.CAIDMulti)
		// log.Println("kbg_debug_aisi debug_caid ->: ", url.QueryEscape(string(tmpCAIDMulitJsonData)))

		ClkFromAdx(c, planID, &mhCpaReq)
	} else if channel == "107" {
		planID := strings.ToLower(c.Query("planid"))
		imeiMd5 := c.Query("imeimd5")
		oaid := c.Query("oaid")
		// os := c.Query("os")
		ip := c.Query("ip")
		callback := c.Query("callback")
		ua := c.Query("ua")

		// log.Println(planID)
		// log.Println(imeiMd5)
		// log.Println(idfaMd5)
		// log.Println(oaid)
		// log.Println(os)
		// log.Println(ip)
		// log.Println(callback)

		mhCpaReq := models.MHCpaReq{}
		mhCpaReq.Channel = channel
		mhCpaReq.UID = uuid.NewV4().String()
		mhCpaReq.ImeiMd5 = strings.ToLower(strings.Replace(imeiMd5, "__IMEI__", "", -1))
		mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
		mhCpaReq.Os = "android"
		mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
		mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)
		mhCpaReq.UA = strings.Replace(ua, "__UA__", "", -1)

		mhCpaReq.DIDMd5 = GetDeviceIDMd5Key(mhCpaReq)

		tmpJsonData, _ := json.Marshal(mhCpaReq)
		log.Println("kbg_debug_xiaomi req: ", string(tmpJsonData))

		ClkFromAdx(c, planID, &mhCpaReq)

		// https://api.e.mi.com/doc.html#/1.0.0-mdtag9b26f-omd/document-2bd1c4c260259b072818205a8ae20139
		// xiaomi自定义返回
		resp := map[string]interface{}{}
		resp["code"] = 0
		resp["failMsg"] = ""
		c.PureJSON(200, resp)
		return
	}

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok adx"
	c.PureJSON(200, resp)
}

// http://localhost:8081/store/report?planid=__PLANID__&imei=__IMEI__&imeimd5=__IMEI_MD5__&oaid=__OAID__&idfa=__IDFA__&idfamd5=__IDFA_MD5__&ip=__IP__&callback=__CALLBACK__&ua=__UA__&media=__MEDIA__

// StoreReport ...
// func StoreReport(c *gin.Context) {
// 	// log.Println("cpa store")

// 	planID := c.Query("planid")
// 	imei := c.Query("imei")
// 	imeiMd5 := c.Query("imeimd5")
// 	idfa := c.Query("idfa")
// 	idfaMd5 := c.Query("idfamd5")
// 	oaid := c.Query("oaid")
// 	os := c.Query("os")
// 	ip := c.Query("ip")
// 	callback := c.Query("callback")
// 	ua := c.Query("ua")

// 	// log.Println(planID)
// 	// log.Println(imeiMd5)
// 	// log.Println(idfaMd5)
// 	// log.Println(oaid)
// 	// log.Println(os)
// 	// log.Println(ip)
// 	// log.Println(callback)

// 	mhCpaReq := models.MHCpaReq{}
// 	mhCpaReq.Channel = "store"
// 	mhCpaReq.UID = uuid.NewV4().String()
// 	mhCpaReq.Imei = strings.Replace(imei, "__IMEI__", "", -1)
// 	mhCpaReq.ImeiMd5 = strings.Replace(imeiMd5, "__IMEI_MD5__", "", -1)
// 	mhCpaReq.Idfa = strings.Replace(idfa, "__IDFA__", "", -1)
// 	mhCpaReq.IdfaMd5 = strings.Replace(idfaMd5, "__IDFA_MD5__", "", -1)
// 	mhCpaReq.Oaid = strings.Replace(oaid, "__OAID__", "", -1)
// 	mhCpaReq.Os = strings.ToLower(strings.Replace(os, "__OS__", "", -1))
// 	mhCpaReq.IP = strings.Replace(ip, "__IP__", "", -1)
// 	mhCpaReq.UA = strings.Replace(ua, "__UA__", "", -1)
// 	mhCpaReq.DownCallback = strings.Replace(callback, "__CALLBACK__", "", -1)

// 	// log.Println(mhCpaReq)

// 	ClkFromAdx(c, planID, &mhCpaReq)

// 	resp := map[string]interface{}{}
// 	resp["ret"] = 0
// 	resp["msg"] = "ok store"
// 	c.PureJSON(200, resp)
// }

// ExpFromAdx ...
func ExpFromAdx(c *gin.Context, planID string, mhCpaReq *models.MHCpaReq) {
	isOCPX := 0

	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo != nil {
		if planInfo.IsOCPX == 1 && planInfo.IsExpToOCPX == 1 {
			randNum := rand.Intn(100)
			if randNum < planInfo.ExpToOCPXWeight {
				isOCPX = 1
				if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "3" {
					// tencent
					core.TencentClkReport(c, planInfo, mhCpaReq)
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "0" {
					// dhh
					core.DhhClkReportFromAdx(c, planInfo, mhCpaReq)
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "8" {
					// ks ocpx
					core.KuaiShouClkReportFromAdx(c, planInfo, mhCpaReq, "exp")
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "9" {
					// uc ocpx
					core.UCClkReportFromAdx(c, planInfo, mhCpaReq)
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "12" {
					// weibo ocpx
					core.WeiBoClkReportFromAdx(c, planInfo, mhCpaReq, "exp")
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "13" {
					// zhifubao ocpx
					core.AlipayClkReportFromAdx(c, planInfo, mhCpaReq, "exp")
				} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "14" {
					// jd ocpx
					core.JDClkReportFromAdx(c, planInfo, mhCpaReq, "exp")
				}
			}
		}
	}

	if planInfo != nil {
		// 上报大数据
		go func() {
			defer func() {
				if err := recover(); err != nil {
					log.Println("bigdata adx click panic:", err)
				}
			}()

			mediaChannel := ""
			subChannelID := ""
			extAdx := ""
			if mhCpaReq.Channel == "ssp" {
				mediaChannel = "0"
			} else if mhCpaReq.Channel == "store" {
				mediaChannel = "2"
			} else {
				mediaChannel = "1"
				extAdx = mhCpaReq.Channel
			}

			cpmPrice := 0
			extCpmPrice := 0
			if planInfo.UnitPriceNum > 0 {
				if planInfo.UnitPriceType == 0 {
					cpmPrice = int(planInfo.UnitPriceNum * 100)
				} else if planInfo.UnitPriceType == 1 {
				} else if planInfo.UnitPriceType == 2 {
				}
			}
			if planInfo.SspPriceType == 0 {
				extCpmPrice = int(planInfo.SspPriceNum * 100)
			}
			models.BigDataExpFromAdx(c, planInfo, mhCpaReq, mediaChannel, subChannelID, extAdx, cpmPrice, extCpmPrice, isOCPX)
		}()
	}
}

// ClkFromAdx ...
func ClkFromAdx(c *gin.Context, planID string, mhCpaReq *models.MHCpaReq) {
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo != nil && planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "3" {
		// tencent
		if mhCpaReq.Channel == "ssp" {
		} else if mhCpaReq.Channel == "ks" || mhCpaReq.Channel == "oceanengine" || mhCpaReq.Channel == "douyu" || mhCpaReq.Channel == "baidu" {
		} else if mhCpaReq.Channel == "store" {
			// 设置ext_cpa_price
			// mhCpaReq.ExtCPAPrice = int(planInfo.StorePriceNum * 100)
		}

		if planInfo.UnitPriceNum > 0 {
			if planInfo.UnitPriceType == 0 {
			} else if planInfo.UnitPriceType == 1 {
			} else if planInfo.UnitPriceType == 2 {
				// 设置cpa_price
				mhCpaReq.CPAPrice = int(planInfo.UnitPriceNum * 100)
			}
		}

		core.TencentClkReport(c, planInfo, mhCpaReq)
	} else if planInfo != nil && planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "0" {
		// dhh
		core.DhhClkReportFromAdx(c, planInfo, mhCpaReq)
	} else if planInfo != nil && planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "8" {
		// ks ocpx
		core.KuaiShouClkReportFromAdx(c, planInfo, mhCpaReq, "clk")
	} else if planInfo != nil && planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "9" {
		// uc ocpx
		core.UCClkReportFromAdx(c, planInfo, mhCpaReq)
	} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "12" {
		// weibo ocpx
		core.WeiBoClkReportFromAdx(c, planInfo, mhCpaReq, "clk")
	} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "13" {
		// zhifubao ocpx
		core.AlipayClkReportFromAdx(c, planInfo, mhCpaReq, "clk")
	} else if planInfo.GroupMarketingType == "1" && planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "14" {
		// jd ocpx
		core.JDClkReportFromAdx(c, planInfo, mhCpaReq, "clk")
	}

	if planInfo != nil {
		// 上报大数据
		go func() {
			defer func() {
				if err := recover(); err != nil {
					log.Println("bigdata adx click panic:", err)
				}
			}()

			mediaChannel := ""
			subChannelID := ""
			extAdx := ""
			if mhCpaReq.Channel == "ssp" {
				mediaChannel = "0"
			} else if mhCpaReq.Channel == "store" {
				mediaChannel = "2"
			} else {
				mediaChannel = "1"
				extAdx = mhCpaReq.Channel
			}

			cpcPrice := 0
			extCpcPrice := 0
			if planInfo.UnitPriceNum > 0 {
				if planInfo.UnitPriceType == 0 {
				} else if planInfo.UnitPriceType == 1 {
					cpcPrice = int(planInfo.UnitPriceNum * 100)
				} else if planInfo.UnitPriceType == 2 {
				}
			}
			if planInfo.SspPriceType == 1 {
				extCpcPrice = int(planInfo.SspPriceNum * 100)
			}
			models.BigDataClkFromAdx(c, planInfo, mhCpaReq, mediaChannel, subChannelID, extAdx, cpcPrice, extCpcPrice)
		}()
	}
}

// GetDeviceIDMd5Key ...
func GetDeviceIDMd5Key(cpaReq models.MHCpaReq) string {
	var deviceInfo deviceidgeneratemodel.DeviceModel
	deviceInfo.Os = cpaReq.Os
	deviceInfo.OsVersion = cpaReq.Osv
	if deviceInfo.Os == "android" {
		deviceInfo.Imei = cpaReq.Imei
		deviceInfo.ImeiMd5 = cpaReq.ImeiMd5
		deviceInfo.Oaid = cpaReq.Oaid
		deviceInfo.OaidMd5 = cpaReq.OaidMd5
	} else if deviceInfo.Os == "ios" {
		deviceInfo.Idfa = cpaReq.Idfa
		deviceInfo.IdfaMd5 = cpaReq.IdfaMd5

		if len(cpaReq.CAIDMulti) > 0 {
			var tmpCAIDMulti []deviceidgeneratemodel.DeviceCAIDMultiModel
			for _, tmpItem := range cpaReq.CAIDMulti {
				if len(tmpItem.CAID) > 0 && len(tmpItem.CAIDVersion) > 0 {
					var tmpCAIDItem deviceidgeneratemodel.DeviceCAIDMultiModel

					tmpCAIDItem.CAID = tmpItem.CAID
					tmpCAIDItem.CAIDVersion = tmpItem.CAIDVersion
					tmpCAIDMulti = append(tmpCAIDMulti, tmpCAIDItem)
				}
			}

			deviceInfo.CAIDMulti = tmpCAIDMulti
		}
	}

	return deviceidgenerate.GenerateDeviceID(deviceInfo)
}
