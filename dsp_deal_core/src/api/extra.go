package api

import (
	"context"
	"dsp_core/base"
	"dsp_core/config"
	"dsp_core/core"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"math/rand"
	"net/url"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/ip_to_region/core/ip2location"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// ExtraCN ...
func ExtraCN(c *gin.Context) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		logger.GetSugaredLogger().Errorf("ExtraCN GetRawData=%v", err)
	}
	// logger.GetSugaredLogger().Info("clipboard req:", string(bodyContent))
	var dspReq models.DspReqStu
	err = json.Unmarshal([]byte(bodyContent), &dspReq)

	if err != nil {
		logger.GetSugaredLogger().Errorf("ExtraCN err=%v", err)
	}

	// ctx
	ctx, cancel := context.WithTimeout(c, 100*time.Millisecond)
	defer cancel() // 确保在函数返回时取消 context
	ch := make(chan any, 1)

	go func() {
		resp := ExtraCNGoroutine(ctx, dspReq)
		ch <- resp
		close(ch)
	}()

	// 使用 select 语句监听 context 的 Done() 通道
	select {
	case <-ctx.Done():
		// 如果 Done() 通道被关闭，说明 context 被取消或超时
		// logger.GetSugaredLogger().Infof("ExtraCN Done err=%v", ctx.Err())
		respMap := map[string]interface{}{}
		respMap["code"] = 900317

		c.PureJSON(200, respMap)
		return
	case res := <-ch:
		// jsonData, _ := json.Marshal(res)
		// logger.GetSugaredLogger().Infof("kbg_debug_extra_cn resp: %v", string(jsonData))

		c.PureJSON(200, res)
		return
	}
}

// ExtraCNGoroutine ...
func ExtraCNGoroutine(c context.Context, dspReq models.DspReqStu) any {
	bigdataUID := uuid.NewV4().String()
	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, dspReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		dspReq.Device.LBSIPCountry = ip2locationResp.Country
		dspReq.Device.LBSIPProvince = ip2locationResp.Region
		dspReq.Device.LBSIPCity = ip2locationResp.City
	}

	isOK := false

	var extraClipBoard *models.ExtraClipboardStu
	if dspReq.ExtraClipboard == 1 {
		extraClipBoard = getExtraClipboard(c, dspReq, bigdataUID)
		if extraClipBoard != nil {
			isOK = true
		}
	}

	var extraNotification *models.ExtraNotificationStu
	if dspReq.ExtraNotification == 1 {
		extraNotification = getExtraNotification(c, dspReq, bigdataUID)
		if extraNotification != nil {
			isOK = true
		}
	}

	if isOK {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 10000
		if extraClipBoard != nil {
			respMap["extra_clipboard"] = extraClipBoard
		}

		if extraNotification != nil {
			respMap["extra_notification"] = extraNotification
		}

		return respMap
	} else {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 900312

		return respMap
	}
}

// ExtraWK ...
func ExtraWK(c *gin.Context) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		logger.GetSugaredLogger().Info(err)
	}
	var dspReq models.DspReqStu
	err = json.Unmarshal([]byte(bodyContent), &dspReq)

	if err != nil {
		logger.GetSugaredLogger().Info(err)
	}

	bigdataUID := uuid.NewV4().String()
	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, dspReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		dspReq.Device.LBSIPCountry = ip2locationResp.Country
		dspReq.Device.LBSIPProvince = ip2locationResp.Region
		dspReq.Device.LBSIPCity = ip2locationResp.City
	}

	isOK := false

	var extraWakeUp *models.ExtraWakeUpStu
	if dspReq.ExtraWakeUp == 1 {
		extraWakeUp = getExtraWakeUp(c, dspReq, bigdataUID)
		if extraWakeUp != nil {
			isOK = true
		}
	}

	if isOK {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 10000
		if extraWakeUp != nil {
			respMap["extra_wakeup"] = extraWakeUp
		}

		c.PureJSON(200, respMap)
		return
	} else {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 900312

		c.PureJSON(200, respMap)
		return
	}
}

// ExtraCP ...
func ExtraCP(c *gin.Context) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		logger.GetSugaredLogger().Errorf("ExtraCP GetRawData err=%v", err)
	}
	// logger.GetSugaredLogger().Info("clipboard req:", string(bodyContent))
	var dspReq models.DspReqStu
	err = json.Unmarshal([]byte(bodyContent), &dspReq)
	if err != nil {
		logger.GetSugaredLogger().Errorf("GetRawData Unmarshal err=%v", err)
	}

	// ctx
	ctx, cancel := context.WithTimeout(c, 100*time.Millisecond)
	defer cancel() // 确保在函数返回时取消 context
	ch := make(chan any, 1)

	go func() {
		resp := ExtraCPGoroutine(ctx, dspReq)
		ch <- resp
		close(ch)
	}()

	// 使用 select 语句监听 context 的 Done() 通道
	select {
	case <-ctx.Done():
		// 如果 Done() 通道被关闭，说明 context 被取消或超时
		// logger.GetSugaredLogger().Info("ExtraCP done")
		respMap := map[string]interface{}{}
		respMap["code"] = 900317

		c.PureJSON(200, respMap)
		return
	case res := <-ch:
		// jsonData, _ := json.Marshal(res)
		// logger.GetSugaredLogger().Info("kbg_debug_extra_cp resp: ", string(jsonData))

		c.PureJSON(200, res)
		return
	}
}

// ExtraCPGoroutine ...
func ExtraCPGoroutine(c context.Context, dspReq models.DspReqStu) any {
	bigdataUID := uuid.NewV4().String()
	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, dspReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		dspReq.Device.LBSIPCountry = ip2locationResp.Country
		dspReq.Device.LBSIPProvince = ip2locationResp.Region
		dspReq.Device.LBSIPCity = ip2locationResp.City
	}

	// extra
	extraClipboardPlanGroup := models.GetExtraClipboardByGroupID(c)
	if extraClipboardPlanGroup == nil {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 900312

		return respMap
	}

	if getExtraClipboardGroupPolicy(c, dspReq, extraClipboardPlanGroup) {
	} else {
		respMap := map[string]interface{}{}
		respMap["code"] = 900312

		return respMap
	}

	var tmpExtraClipboardArray []models.ExtraClipboardStu

	for _, item := range extraClipboardPlanGroup.Plans {
		if item.IsActive {
		} else {
			continue
		}
		tmpPlanInfo := models.GetPlanInfoFromMysqlByPlanID(c, item.PlanID)
		if tmpPlanInfo == nil {
			logger.GetSugaredLogger().Errorf("ExtraCPGoroutine plan_id not found: %v", item.PlanID)
			continue
		}

		tmpExtraClipboard := getExtraClipboardPolicy(c, dspReq, tmpPlanInfo, bigdataUID)
		if tmpExtraClipboard != nil {
			tmpExtraClipboard.Weight = item.Weight
			// exp links
			var respListItemImpArray []string

			mhImpParams := url.Values{}
			bigdataParams := EncodeParams(&dspReq, tmpPlanInfo, uuid.NewV4().String(), "0", "")
			mhImpParams.Add("log", bigdataParams)
			respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

			tmpExtraClipboard.ExpLinks = respListItemImpArray

			tmpExtraClipboardArray = append(tmpExtraClipboardArray, *tmpExtraClipboard)
		}
	}
	// logger.GetSugaredLogger().Info("kbg_debug_clipboard end: ", len(tmpExtraClipboardArray))

	if len(tmpExtraClipboardArray) == 0 {
		// resp
		respMap := map[string]interface{}{}
		respMap["code"] = 900312

		return respMap
	}

	// resp
	respMap := map[string]interface{}{}
	respMap["code"] = 10000
	respMap["extra_clipboards"] = tmpExtraClipboardArray

	return respMap
}

// getExtraClipboard ...
func getExtraClipboard(c context.Context, dspReq models.DspReqStu, bigdataUID string) *models.ExtraClipboardStu {

	// extra
	extraClipboardPlanGroup := models.GetExtraClipboardByGroupID(c)
	if extraClipboardPlanGroup == nil {
		return nil
	}

	if getExtraClipboardGroupPolicy(c, dspReq, extraClipboardPlanGroup) {
	} else {
		return nil
	}

	var extraClipboard *models.ExtraClipboardStu
	var planInfo *models.DspPlanStu
	var tmpExtraClipboardPlans []models.DspExtraPlanStu

	for _, item := range extraClipboardPlanGroup.Plans {
		if item.IsActive {
		} else {
			continue
		}
		tmpPlanInfo := models.GetPlanInfoFromMysqlByPlanID(c, item.PlanID)
		if tmpPlanInfo == nil {
			continue
		}

		tmpExtraClipboard := getExtraClipboardPolicy(c, dspReq, tmpPlanInfo, bigdataUID)
		if tmpExtraClipboard != nil {
			item.ExtraClipboardStu = tmpExtraClipboard
			tmpExtraClipboardPlans = append(tmpExtraClipboardPlans, item)
		}
	}

	// 按照比重
	var tmpTotalWeight = 0
	for _, item := range tmpExtraClipboardPlans {
		tmpTotalWeight = tmpTotalWeight + item.Weight
	}
	if tmpTotalWeight == 0 {
		return nil
	}
	tmpRandWeight := rand.Intn(tmpTotalWeight)

	tmpWeight1 := 0
	for _, item := range tmpExtraClipboardPlans {
		tmpWeight1 = tmpWeight1 + item.Weight
		if tmpRandWeight < tmpWeight1 {
			planInfo = models.GetPlanInfoFromMysqlByPlanID(c, item.PlanID)
			extraClipboard = item.ExtraClipboardStu
			break
		}
	}

	if extraClipboard == nil {
		return nil
	}

	// exp links
	var respListItemImpArray []string

	mhImpParams := url.Values{}
	// mhImpParams.Add("uid", bigdataUID)
	// mhImpParams.Add("plan_id", planInfo.PID)
	// mhImpParams.Add("extra", extraStr)
	bigdataParams := EncodeParams(&dspReq, planInfo, bigdataUID, "0", "")
	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	// for _, expItem := range planInfo.ExpLinks {
	// 	tmpItem := expItem + "&mapledspuid=" + bigdataUID
	// 	tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
	// 	tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
	// 	tmpItem = strings.Replace(tmpItem, "__ANDROIDID__", dspReq.Device.AndroidID, -1)
	// 	tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
	// 	tmpItem = strings.Replace(tmpItem, "__UA__", dspReq.Device.Ua, -1)
	// 	respListItemImpArray = append(respListItemImpArray, tmpItem)
	// }

	extraClipboard.ExpLinks = respListItemImpArray

	// 上报大数据
	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeSuccess, models.ReasonSuccess)

	return extraClipboard
}

// getExtraClipboardGroupPolicy 剪贴板组策略
func getExtraClipboardGroupPolicy(c context.Context, dspReq models.DspReqStu, extraPlanGroupInfo *models.DspExtraPlanGroupClipboardStu) bool {
	// 目标数
	if extraPlanGroupInfo.ExtraClipboardMaxNumType == 1 {
		tmpRedisKey := "extra_max_clipboard_total_" + time.Now().Format("2006-01-02") + "_" + extraPlanGroupInfo.ExtraPlanGroupID
		tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			if tmpRedisInt >= extraPlanGroupInfo.ExtraClipboardMaxNum {
				logger.GetSugaredLogger().Error("clipboard max failed, ", extraPlanGroupInfo.ExtraPlanGroupID, dspReq.Device.Oaid)
				return false
			}
		}

		// 目标数 + 1
		t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
		leftUnixTime := t.Unix() + 2 - time.Now().Unix()

		// ttl 0:00 - 3:00
		leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
		} else {
			// logger.GetSugaredLogger().Info("redis value:", redisValue)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
		}
	}

	// 沉默周期
	if extraPlanGroupInfo.ExtraClipboardTimeoutType == 1 && extraPlanGroupInfo.ExtraClipboardTimeoutMaxNum > 0 {
		tmpRedisKey := "extra_clipboard_did_" + time.Now().Format("2006-01-02") + "_" + extraPlanGroupInfo.ExtraPlanGroupID + "_" + dspReq.Device.DIDMd5Key
		_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(extraPlanGroupInfo.ExtraClipboardTimeoutMaxNum)*time.Minute)
		} else {
			// logger.GetSugaredLogger().Info("clipboard silence failed, ", extraPlanGroupInfo.ExtraPlanGroupID, dspReq.Device.Oaid)
			return false
		}
	}

	return true
}

func getExtraClipboardPolicy(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string) *models.ExtraClipboardStu {
	// logger.GetSugaredLogger().Info("get extra clipboard")
	if planInfo == nil {
		return nil
	}
	if strings.Contains(planInfo.Market, "clipboard") {
	} else {
		return nil
	}
	if dspReq.Device.Os == "android" {
	} else {
		return nil
	}

	// 地域限制
	if planInfo.LBSType == 1 {
		if len(planInfo.LBSWhiteList) == 0 && len(planInfo.LBSBlackList) == 0 {
			return nil
		}
		if len(dspReq.Device.LBSIPProvince) == 0 || len(dspReq.Device.LBSIPCity) == 0 {
			return nil
		}
		// 地域白名单
		if len(planInfo.LBSWhiteList) > 0 {
			isInWhiteCity := false
			for _, item := range planInfo.LBSWhiteList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInWhiteCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInWhiteCity = true
						break
					}
				}
			}

			if isInWhiteCity {
			} else {
				return nil
			}
		}

		// 地域黑名单
		if len(planInfo.LBSBlackList) > 0 {
			isInBlackCity := false
			for _, item := range planInfo.LBSBlackList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInBlackCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInBlackCity = true
						break
					}
				}
			}

			if isInBlackCity {
				return nil
			}
		}
	}

	// 设备Manufacturer限制
	if core.IsManufacturerOK(c, dspReq, planInfo) {
	} else {
		logger.GetSugaredLogger().Info("clicpboard wrong manufacturer: ", planInfo.PID, dspReq.Device.Manufacturer, planInfo.AndroidManufacturer)
		return nil
	}

	// 安卓版本限制
	if dspReq.Device.Os == "android" {
		if planInfo.AndroidVersion == "all" {
		} else {
			osvMajor := 0
			if len(dspReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// logger.GetSugaredLogger().Info(osvMajor)
			}

			if osvMajor > utils.ConvertStringToInt(planInfo.AndroidVersion) {
			} else {
				return nil
			}
		}
	}
	// 安卓版本过滤限制
	if IsAndroidVersionFilterOK(c, &dspReq, planInfo) {
	} else {
		// logger.GetSugaredLogger().Info("clicpboard version filter failter: ", planInfo.PID, dspReq.Device.Manufacturer, dspReq.Device.OsVersion)
		return nil
	}

	// sdk版本限制
	if len(planInfo.SDKVersion) > 0 && len(dspReq.SDKVersion) > 0 {
		tmpReqVersionArray := strings.Split(dspReq.SDKVersion, ".")
		tmpConfigVersionArray := strings.Split(planInfo.SDKVersion, ".")

		tmpReqVersion0 := 0
		tmpConfigVersion0 := 0

		tmpReqVersion1 := 0
		tmpConfigVersion1 := 0

		tmpReqVersion2 := 0
		tmpConfigVersion2 := 0

		tmpReqVersion3 := 0
		tmpConfigVersion3 := 0

		if len(tmpReqVersionArray) > 0 && len(tmpConfigVersionArray) > 0 {
			if utils.IsNum(tmpReqVersionArray[0]) && utils.IsNum(tmpConfigVersionArray[0]) {
				tmpReqVersion0 = utils.ConvertStringToInt(tmpReqVersionArray[0])
				tmpConfigVersion0 = utils.ConvertStringToInt(tmpConfigVersionArray[0])
				if tmpReqVersion0 > tmpConfigVersion0 {
					return nil
				}
			}
		}
		if len(tmpReqVersionArray) > 1 && len(tmpConfigVersionArray) > 1 {
			if utils.IsNum(tmpReqVersionArray[1]) && utils.IsNum(tmpConfigVersionArray[1]) {
				tmpReqVersion1 = utils.ConvertStringToInt(tmpReqVersionArray[1])
				tmpConfigVersion1 = utils.ConvertStringToInt(tmpConfigVersionArray[1])
				if tmpReqVersion1 > tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return nil
				}
			}
		}
		if len(tmpReqVersionArray) > 2 && len(tmpConfigVersionArray) > 2 {
			if utils.IsNum(tmpReqVersionArray[2]) && utils.IsNum(tmpConfigVersionArray[2]) {
				tmpReqVersion2 = utils.ConvertStringToInt(tmpReqVersionArray[2])
				tmpConfigVersion2 = utils.ConvertStringToInt(tmpConfigVersionArray[2])
				if tmpReqVersion2 > tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return nil
				}
			}
		}
		if len(tmpReqVersionArray) > 3 && len(tmpConfigVersionArray) > 3 {
			if utils.IsNum(tmpReqVersionArray[3]) && utils.IsNum(tmpConfigVersionArray[3]) {
				tmpReqVersion3 = utils.ConvertStringToInt(tmpReqVersionArray[3])
				tmpConfigVersion3 = utils.ConvertStringToInt(tmpConfigVersionArray[3])
				if tmpReqVersion3 > tmpConfigVersion3 && tmpReqVersion2 == tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return nil
				}
			}
		}
	}

	// 时间限制
	// 只"10808"时间限制策略
	// logger.GetSugaredLogger().Info("kbg_debug_clicpboard supply_app_id: ", dspReq.Pos.SupplyAppID)
	if dspReq.Pos.SupplyAppID == "10808" {
		if planInfo.TimeType == "1" {
			if len(planInfo.TimeList) == 0 {
				logger.GetSugaredLogger().Infof("plan pos time failed, pid=%v", planInfo.PID)
				return nil
			}

			isTimeOK := false
			for _, item := range planInfo.TimeList {
				nowHour := time.Now().Format("15")
				startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[0], time.Local)
				endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[1], time.Local)

				if utils.ConvertStringToInt(nowHour) >= startTime.Hour() &&
					utils.ConvertStringToInt(nowHour) <= endTime.Hour() {
					isTimeOK = true
				}
			}
			if isTimeOK {
			} else {
				logger.GetSugaredLogger().Infof("plan pos time failed, pid=%v", planInfo.PID)
				return nil
			}
		}
	}

	// 人群包定向开
	isCrowdLibOK, _, _ := base.IsPlanPolicyCrowdLibOK(c, dspReq, planInfo, bigdataUID)
	if isCrowdLibOK {
	} else {
		return nil
	}

	if len(planInfo.ExtraClipboardText) == 0 {
		return nil
	}

	// response
	tmpResponse := models.ExtraClipboardStu{}
	tmpResponse.ClipboardText = planInfo.ExtraClipboardText
	if planInfo.IsExtraClipboardNoPackageName == 1 {
	} else {
		tmpResponse.PackageName = planInfo.PackageName
	}

	return &tmpResponse
}

// getExtraNotification ...
func getExtraNotification(c context.Context, dspReq models.DspReqStu, bigdataUID string) *models.ExtraNotificationStu {
	// logger.GetSugaredLogger().Info("kbg_debug getExtraNotification")
	// extra
	extraNotificationPlans := models.GetExtraNotificationByGroupID(c)
	if extraNotificationPlans == nil {
		return nil
	}

	var extraNotification *models.ExtraNotificationStu
	var planInfo *models.DspPlanStu

	// extraStr,  0(正常投放),  1(大航海),  2(腾讯游戏)
	extraStr := "0"

	for _, extraPlanItem := range *extraNotificationPlans {
		tmpExtra := getExtraNotificationPolicy(c, dspReq, &extraPlanItem)
		// logger.GetSugaredLogger().Info("kbg_debug getExtraNotification", tmpExtra)

		if tmpExtra != nil {
			if extraPlanItem.GroupAdsType == "1" && extraPlanItem.GroupExtDspChannel == "0" {
				// logger.GetSugaredLogger().Info("kbg_debug getExtraNotification dhh 0")

				if core.IsDangHangHaiOK(c, &dspReq, extraPlanItem.DHHChannelID, extraPlanItem.DHHPosID, extraPlanItem.DhhTaskID, bigdataUID, &extraPlanItem) {
					// logger.GetSugaredLogger().Info("kbg_debug getExtraNotification dhh 1")

					extraStr = "1"
				} else {
					// logger.GetSugaredLogger().Info("kbg_debug getExtraNotification dhh 2")

					continue
				}
			}

			extraNotification = tmpExtra
			planInfo = &extraPlanItem
			break
		}
	}

	if extraNotification == nil {
		return nil
	}

	// exp links
	var respListItemImpArray []string

	mhImpParams := url.Values{}
	// mhImpParams.Add("uid", bigdataUID)
	// mhImpParams.Add("plan_id", planInfo.PID)
	// mhImpParams.Add("extra", extraStr)
	bigdataParams := EncodeParams(&dspReq, planInfo, bigdataUID, extraStr, "")
	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	extraNotification.ExpLinks = respListItemImpArray

	// click links
	var respListItemClkArray []string
	// click_link maplehaze
	mhClkParams := url.Values{}
	// mhClkParams.Add("req_width", "__REQ_WIDTH__")
	// mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	// mhClkParams.Add("width", "__WIDTH__")
	// mhClkParams.Add("height", "__HEIGHT__")
	// mhClkParams.Add("down_x", "__DOWN_X__")
	// mhClkParams.Add("down_y", "__DOWN_Y__")
	// mhClkParams.Add("up_x", "__UP_X__")
	// mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("plan_id", planInfo.PID)
	mhClkParams.Add("log", bigdataParams)

	// click_link maplehaze
	respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())

	extraNotification.ClkLinks = respListItemClkArray

	// 上报大数据
	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeSuccess, models.ReasonSuccess)

	return extraNotification
}

func getExtraNotificationPolicy(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu) *models.ExtraNotificationStu {
	// logger.GetSugaredLogger().Info("get extra notification")
	if strings.Contains(planInfo.Market, "notification") {
	} else {
		return nil
	}
	if dspReq.Device.Os == "android" {
	} else {
		return nil
	}

	// 地域限制
	if planInfo.LBSType == 1 {
		if len(planInfo.LBSWhiteList) == 0 && len(planInfo.LBSBlackList) == 0 {
			return nil
		}
		if len(dspReq.Device.LBSIPProvince) == 0 || len(dspReq.Device.LBSIPCity) == 0 {
			return nil
		}
		// 地域白名单
		if len(planInfo.LBSWhiteList) > 0 {
			isInWhiteCity := false
			for _, item := range planInfo.LBSWhiteList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInWhiteCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInWhiteCity = true
						break
					}
				}
			}

			if isInWhiteCity {
			} else {
				return nil
			}
		}

		// 地域黑名单
		if len(planInfo.LBSBlackList) > 0 {
			isInBlackCity := false
			for _, item := range planInfo.LBSBlackList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInBlackCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInBlackCity = true
						break
					}
				}
			}

			if isInBlackCity {
				return nil
			}
		}
	}

	// 设备Manufacturer限制
	if core.IsManufacturerOK(c, dspReq, planInfo) {
	} else {
		logger.GetSugaredLogger().Info("notification wrong manufacturer: ", planInfo.PID, dspReq.Device.Manufacturer, planInfo.AndroidManufacturer)
		return nil
	}

	// 时间限制
	if planInfo.TimeType == "1" {
		if len(planInfo.TimeList) == 0 {
			logger.GetSugaredLogger().Errorf("plan pos time failed, pid=", planInfo.PID)
			return nil
		}

		isTimeOK := false
		for _, item := range planInfo.TimeList {
			nowHour := time.Now().Format("15")
			startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[0], time.Local)
			endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[1], time.Local)

			if utils.ConvertStringToInt(nowHour) >= startTime.Hour() &&
				utils.ConvertStringToInt(nowHour) <= endTime.Hour() {
				isTimeOK = true
			}
		}
		if isTimeOK {
		} else {
			logger.GetSugaredLogger().Infof("plan pos time failed, pid=%v", planInfo.PID)
			return nil
		}
	}

	// 沉默周期
	if planInfo.ExtraNotificationTimeoutType == "1" && len(planInfo.ExtraNotificationTimeoutMaxNum) > 0 {
		tmpRedisKey := "extra_notification_did_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + dspReq.Device.DIDMd5Key
		_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(utils.ConvertStringToInt(planInfo.ExtraNotificationTimeoutMaxNum))*time.Hour)
		} else {
			return nil
		}
	}

	// 时段, 目标数
	if planInfo.ExtraNotificationType == "1" {
		// 通知1 ---> 时段目标数
		isNotification1Failed := true

		tmpSplitArray := strings.Split(planInfo.ExtraNotification1TimeValue, ",")
		if len(tmpSplitArray) == 2 {
			// 时段
			startTime := tmpSplitArray[0]
			startTime = strings.Replace(startTime, ":", "", -1)

			endTime := tmpSplitArray[1]
			endTime = strings.Replace(endTime, ":", "", -1)

			nowTime := time.Now().Format("150405")
			if utils.ConvertStringToInt(nowTime) < utils.ConvertStringToInt(startTime) || utils.ConvertStringToInt(nowTime) > utils.ConvertStringToInt(endTime) {
				// logger.GetSugaredLogger().Infof("notification 1 failed, pid=%v", planInfo.PID)
				isNotification1Failed = false
			}

			// 目标数
			tmpRedisKey := "extra_max_notification1_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
			tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

			if redisErr != nil {
				// logger.GetSugaredLogger().Info("redis error:", redisErr)
			} else {
				tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
				if tmpRedisInt >= utils.ConvertStringToInt(planInfo.ExtraNotification1MaxNum) {
					// logger.GetSugaredLogger().Infof("notification1 max failed, pid=%v", planInfo.PID)
					isNotification1Failed = false
				}
			}

			// 目标数 + 1
			t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
			leftUnixTime := t.Unix() + 2 - time.Now().Unix()

			// ttl 0:00 - 3:00
			leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

			if redisErr != nil {
				// logger.GetSugaredLogger().Info("redis error:", redisErr)
				db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
			} else {
				// logger.GetSugaredLogger().Info("redis value:", redisValue)
				db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
			}
		}

		// 通知2 时段
		isNotification2Failed := true

		tmpSplitArray = strings.Split(planInfo.ExtraNotification2TimeValue, ",")
		if len(tmpSplitArray) == 2 {
			// 时段
			startTime := tmpSplitArray[0]
			startTime = strings.Replace(startTime, ":", "", -1)

			endTime := tmpSplitArray[1]
			endTime = strings.Replace(endTime, ":", "", -1)

			nowTime := time.Now().Format("150405")
			if utils.ConvertStringToInt(nowTime) < utils.ConvertStringToInt(startTime) || utils.ConvertStringToInt(nowTime) > utils.ConvertStringToInt(endTime) {
				// logger.GetSugaredLogger().Infof("notification 2 failed, pid=%v", planInfo.PID)
				isNotification2Failed = false
			}

			// 目标数
			tmpRedisKey := "extra_max_notification2_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
			tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

			if redisErr != nil {
				// logger.GetSugaredLogger().Info("redis error:", redisErr)
			} else {
				tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
				if tmpRedisInt >= utils.ConvertStringToInt(planInfo.ExtraNotification2MaxNum) {
					// logger.GetSugaredLogger().Infof("notification2 max failed, pid=%v", planInfo.PID)
					isNotification2Failed = false
				}
			}

			// 目标数 + 1
			t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
			leftUnixTime := t.Unix() + 2 - time.Now().Unix()

			// ttl 0:00 - 3:00
			leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

			if redisErr != nil {
				// logger.GetSugaredLogger().Info("redis error:", redisErr)
				db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
			} else {
				// logger.GetSugaredLogger().Info("redis value:", redisValue)
				db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
			}
		}
		if isNotification1Failed == false && isNotification2Failed == false {
			logger.GetSugaredLogger().Infof("notification 1 2 failed, pid=%v", planInfo.PID)
			return nil
		}
	}

	if len(planInfo.Creatives) == 0 {
		return nil
	}

	tmpCreative := planInfo.Creatives[0]

	// response
	tmpResponse := models.ExtraNotificationStu{}
	tmpResponse.NotificationTitle = tmpCreative.NotificationTitle
	tmpResponse.NotificationDescription = tmpCreative.NotificationDescription
	tmpResponse.NotificationIconURL = tmpCreative.NotificationIconURL
	if len(planInfo.DeepLink) > 0 {
		tmpDeepLink := strings.Replace(planInfo.DeepLink, " ", "", -1)
		tmpSplitArray := strings.Split(tmpDeepLink, ",")
		randValue := rand.Intn(len(tmpSplitArray))
		tmpResponse.NotificationDeepLink = tmpSplitArray[randValue]

		// 防止最后位','
		if len(tmpSplitArray[randValue]) == 0 {
			tmpResponse.NotificationDeepLink = tmpSplitArray[0]
		}
	} else {
		tmpResponse.NotificationDeepLink = ""
	}
	tmpResponse.NotificationH5Link = planInfo.H5Link
	tmpResponse.NotificationDownloadLink = planInfo.DownloadLink

	return &tmpResponse
}

// getExtraWakeUp ...
func getExtraWakeUp(c *gin.Context, dspReq models.DspReqStu, bigdataUID string) *models.ExtraWakeUpStu {
	// extra
	extraWakeUpPlans := models.GetExtraWakeUpByGroupID(c)
	if extraWakeUpPlans == nil {
		return nil
	}

	var extraWakeUp *models.ExtraWakeUpStu
	var planInfo *models.DspPlanStu

	// extraStr,  0(正常投放),  1(大航海),  2(腾讯游戏)
	extraStr := "0"

	for _, extraPlanItem := range *extraWakeUpPlans {
		tmpExtraWakeUp := getExtraWakeUpPolicy(c, dspReq, &extraPlanItem)
		if tmpExtraWakeUp != nil {
			if extraPlanItem.GroupAdsType == "1" && extraPlanItem.GroupExtDspChannel == "0" {
				if core.IsDangHangHaiOK(c, &dspReq, extraPlanItem.DHHChannelID, extraPlanItem.DHHPosID, extraPlanItem.DhhTaskID, bigdataUID, &extraPlanItem) {
					extraStr = "1"
				} else {
					continue
				}
			}

			extraWakeUp = tmpExtraWakeUp
			planInfo = &extraPlanItem
			break
		}
	}

	if extraWakeUp == nil {
		return nil
	}

	// exp links
	var respListItemImpArray []string

	mhImpParams := url.Values{}
	// mhImpParams.Add("uid", bigdataUID)
	// mhImpParams.Add("plan_id", planInfo.PID)
	// mhImpParams.Add("extra", extraStr)
	bigdataParams := EncodeParams(&dspReq, planInfo, bigdataUID, extraStr, "")
	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	extraWakeUp.ExpLinks = respListItemImpArray

	// 上报大数据
	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeSuccess, models.ReasonSuccess)

	return extraWakeUp
}

func getExtraWakeUpPolicy(c *gin.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu) *models.ExtraWakeUpStu {
	// logger.GetSugaredLogger().Info("get extra wakeup")
	if strings.Contains(planInfo.Market, "wakeup") {
	} else {
		return nil
	}
	if dspReq.Device.Os == "android" {
	} else {
		return nil
	}

	// 地域限制
	if planInfo.LBSType == 1 {
		if len(planInfo.LBSWhiteList) == 0 && len(planInfo.LBSBlackList) == 0 {
			return nil
		}
		if len(dspReq.Device.LBSIPProvince) == 0 || len(dspReq.Device.LBSIPCity) == 0 {
			return nil
		}
		// 地域白名单
		if len(planInfo.LBSWhiteList) > 0 {
			isInWhiteCity := false
			for _, item := range planInfo.LBSWhiteList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInWhiteCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInWhiteCity = true
						break
					}
				}
			}

			if isInWhiteCity {
			} else {
				return nil
			}
		}

		// 地域黑名单
		if len(planInfo.LBSBlackList) > 0 {
			isInBlackCity := false
			for _, item := range planInfo.LBSBlackList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInBlackCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInBlackCity = true
						break
					}
				}
			}

			if isInBlackCity {
				return nil
			}
		}
	}

	// 目标数
	if planInfo.ExtraWakeupType == "1" {
		tmpRedisKey := "extra_max_wakeup_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
		tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			if tmpRedisInt >= utils.ConvertStringToInt(planInfo.ExtraWakeupMaxNum) {
				// logger.GetSugaredLogger().Info("wakeup max failed, " + planInfo.PID)
				return nil
			}
		}

		// 目标数 + 1
		t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
		leftUnixTime := t.Unix() + 2 - time.Now().Unix()

		// ttl 0:00 - 3:00
		leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
		} else {
			// logger.GetSugaredLogger().Info("redis value:", redisValue)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
		}
	}

	// 时段
	if planInfo.ExtraWakeupTimeType == "1" {
		tmpSplitArray := strings.Split(planInfo.ExtraWakeupTimeValue, ",")
		if len(tmpSplitArray) == 2 {
			// 时段
			startTime := tmpSplitArray[0]
			startTime = strings.Replace(startTime, ":", "", -1)

			endTime := tmpSplitArray[1]
			endTime = strings.Replace(endTime, ":", "", -1)

			nowTime := time.Now().Format("150405")
			if utils.ConvertStringToInt(nowTime) < utils.ConvertStringToInt(startTime) || utils.ConvertStringToInt(nowTime) > utils.ConvertStringToInt(endTime) {
				// logger.GetSugaredLogger().Infof("getExtraWakeUpPolicy wakeup 1 failed, pid=%v", planInfo.PID)
				return nil
			}
		}
	}

	// 沉默周期
	if planInfo.ExtraWakeupTimeoutType == "1" && len(planInfo.ExtraWakeupTimeoutMaxNum) > 0 {
		tmpRedisKey := "extra_wakeup_did_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + dspReq.Device.DIDMd5Key
		_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(utils.ConvertStringToInt(planInfo.ExtraWakeupTimeoutMaxNum))*time.Hour)
		} else {
			return nil
		}
	}

	if len(planInfo.DeepLink) == 0 {
		return nil
	}

	// response
	tmpResponse := models.ExtraWakeUpStu{}
	tmpResponse.WakeUpDeepLink = planInfo.DeepLink

	return &tmpResponse
}

// GetReplaceManufactureConfig ...
// GET ssp_manufacturer_packages_huawei
// GET ssp_manufacturer_packages_xiaomi
// GET ssp_manufacturer_packages_oppo
// GET ssp_manufacturer_packages_vivo
// GET ssp_manufacturer_packages_lenovo
// GET ssp_manufacturer_packages_meizu
func GetReplaceManufactureConfig(c context.Context, cacheKey string) []string {
	var manufactureArray []string

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		logger.GetSugaredLogger().Errorf("GetReplaceManufactureConfig cache key:%v, error: %v", cacheKey, cacheError)

		redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, cacheKey)
		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GlbBigCache.Set(cacheKey, []byte(""))
		} else {
			cacheValue = []byte(redisValue)
			db.GlbBigCache.Set(cacheKey, []byte(redisValue))
		}
	}

	json.Unmarshal(cacheValue, &manufactureArray)
	return manufactureArray
}

// IsAndroidVersionFilterOK
func IsAndroidVersionFilterOK(c context.Context, dspReq *models.DspReqStu, planInfo *models.DspPlanStu) bool {
	if dspReq.Device.Os == "ios" {
		return true
	}

	if len(planInfo.AndroidVersionFilters) == 0 {
		return true
	}

	osvMajor := 0
	if len(dspReq.Device.OsVersion) > 0 {
		osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
		osvMajor = utils.ConvertStringToInt(osvMajorStr)
		// logger.GetSugaredLogger().Info(osvMajor)
	}

	manufactureFlag := core.GetManufactureFlag(c, dspReq)

	isFound := false
	for _, item := range planInfo.AndroidVersionFilters {
		if item.FilterManufacturer == manufactureFlag {
			isFound = true
			if osvMajor > utils.ConvertStringToInt(item.FilterOsv) {
				return false
			} else {
				return true
			}
		}
	}

	if isFound {
	} else {
		for _, item := range planInfo.AndroidVersionFilters {
			if item.FilterManufacturer == "all" {
				if osvMajor > utils.ConvertStringToInt(item.FilterOsv) {
					return false
				} else {
					return true
				}
			}
		}
	}

	return true
}

// 900312 dsp无extra
