package api

import (
	"dsp_core/models"
	"dsp_core/utils"
	"net/url"
	"strings"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"

	"github.com/gin-gonic/gin"
)

// http://localhost:8081/dhh/act?uid=fe2d9179-9155-461d-92dc-034f1127e924&assistReportMatched=&transformType=12&adid=

// DhhAct ...
func DhhAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("dhh act")

	transformType := c.Query("transformType")
	if transformType == "12" {
	} else {
		// return
	}

	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		l.Info("dhh act nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Info("bigdata dhh panic:", err)
			}
		}()
		models.BigDataHoloActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok act"
	c.PureJSON(200, resp)
}

// TencentAct ...
func TencentAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("tencent act")

	logQuery := c.Query("log")
	logQuery = strings.Replace(logQuery, " ", "+", -1)

	if len(logQuery) == 0 {
		l.Info("tencent act nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata tencent panic error=%v", err)
			}
		}()
		models.BigDataHoloActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok act"
	c.PureJSON(200, resp)
}

// AMapAct ...
func AMapAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("amap act")

	logQuery := c.Query("log")
	logQuery = strings.Replace(logQuery, " ", "+", -1)

	if len(logQuery) == 0 {
		l.Error("amap act nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata amap panic:%v", err)
			}
		}()
		models.BigDataHoloActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["result"] = 1
	c.PureJSON(200, resp)
}

// KuaiShouAct ...
func KuaiShouAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("kuaishou act test")

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata kuaishou panic:%v", err)
			}
		}()

		logQuery := c.Query("log")
		logQuery = strings.Replace(logQuery, " ", "+", -1)
		logQuery, _ = url.QueryUnescape(logQuery)

		if len(logQuery) == 0 {
			l.Error("kuaishou act nothing to do")
			return
		}
		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))
		models.BigDataHoloActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = ""

	respData := map[string]interface{}{}
	respData["callback"] = "ok"
	resp["data"] = respData

	c.PureJSON(200, resp)
}

// UCAct ...
func UCAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("uc act")

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata uc panic:%v", err)
			}
		}()

		logQuery := c.Query("log")
		logQuery = strings.Replace(logQuery, " ", "+", -1)
		// logQuery, _ = url.QueryUnescape(logQuery)

		if len(logQuery) == 0 {
			l.Error("uc act nothing to do")
			return
		}
		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))
		models.BigDataHoloActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok act"

	c.PureJSON(200, resp)
}

// JuMeiTongYiAct
func JuMeiTongYiAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("jumei_tongyi act")

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata jumei_tongyi panic:%v", err)
			}
		}()

		logQuery := c.Query("log")
		logQuery = strings.Replace(logQuery, " ", "+", -1)
		logQuery, _ = url.QueryUnescape(logQuery)

		if len(logQuery) == 0 {
			l.Error("jumei_tongyi act nothing to do")
			return
		}
		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))
		models.BigDataJuMeiTongYiActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = ""

	respData := map[string]interface{}{}
	respData["callback"] = "ok"
	resp["data"] = respData

	c.PureJSON(200, resp)
}

// JuMeiTongYiAct
func JuMeiPanZhiDaiShouAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("jumei_pzds act")

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata jumei_pzds panic:%v", err)
			}
		}()

		logQuery := c.Query("log")
		logQuery = strings.Replace(logQuery, " ", "+", -1)
		logQuery, _ = url.QueryUnescape(logQuery)
		if len(logQuery) == 0 {
			l.Error("jumei_pzds act nothing to do")
			return
		}

		logDecode, _ := utils.DecodeString(logQuery)
		log, _ := url.ParseQuery(string(logDecode))
		models.BigDataJuMeiPZDSActive(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = ""

	respData := map[string]interface{}{}
	respData["callback"] = "ok"
	resp["data"] = respData

	c.PureJSON(200, resp)
}

// WeiBoAct
func WeiBoAct(c *gin.Context) {
	l := logger.GetSugaredLogger()
	// l.Info("weibo act")

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				l.Errorf("bigdata weibo panic:%v", err)
			}
		}()

		logQuery := c.Query("log")
		logQuery = strings.Replace(logQuery, " ", "+", -1)
		// logQuery, _ = url.QueryUnescape(logQuery)
		if len(logQuery) == 0 {
			l.Error("weibo act nothing to do")
			return
		}

		logDecode, err := utils.DecodeString(logQuery)
		if err != nil {
			l.Errorf("weibo act decode string error, logQuery:%v, err:%v", logQuery, err)
			return
		}
		log, err := url.ParseQuery(string(logDecode))
		if err != nil {
			l.Errorf("weibo act parse query error, logDecode:%v, err:%v", logDecode, err)
			return
		}
		models.BigDataHoloActive(c, log)
		l.Infof("weibo act success, log: %v", log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok act"
	c.PureJSON(200, resp)
}

// AlipayAct 处理支付宝转化回传
func AlipayAct(c *gin.Context) {
	// logger.GetSugaredLogger().Info("AlipayAct callback begin")
	// 获取转化类型
	transformType := c.Query("transformtype")
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		logger.GetSugaredLogger().Error("alipay callback missing parameters")
		c.JSON(200, gin.H{"ret": -1, "msg": "missing parameters"})
		return
	}

	logQuery = strings.Replace(logQuery, " ", "+", -1)
	// logQuery, err := url.QueryUnescape(logQuery)
	// if err != nil {
	// 	logger.GetSugaredLogger().Errorf("alipay callback unescape log logQuery=%v, error: %v", logQuery, err)
	// 	c.JSON(200, gin.H{"ret": -1, "msg": "unescape log error"})
	// 	return
	// }

	logger.GetSugaredLogger().Infof("alipay callback begin transformType=%v, log=%v", transformType, logQuery)
	// 解析log参数
	logDecode, err := utils.DecodeString(logQuery)
	if err != nil {
		logger.GetSugaredLogger().Errorf("alipay callback decode log error: %v", err)
		c.JSON(200, gin.H{"ret": -1, "msg": "decode log error"})
		return
	}

	log, err := url.ParseQuery(string(logDecode))
	if err != nil {
		logger.GetSugaredLogger().Errorf("alipay callback parse log error: %v", err)
		c.JSON(200, gin.H{"ret": -1, "msg": "parse log error"})
		return
	}

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.GetSugaredLogger().Errorf("alipay callback bigdata panic: %v", err)
			}
		}()

		// 调用大数据上报接口
		models.BigDataHoloActive(c, log)
	}()

	// logger.GetSugaredLogger().Info("AlipayAct callback finish")
	c.JSON(200, gin.H{"ret": 0, "msg": "ok"})
}

// JDAct 处理京东科技转化回传
func JDAct(c *gin.Context) {
	// logger.GetSugaredLogger().Info("JDAct callback begin")
	// 获取转化类型
	// eventType := c.Query("actionType")
	logQuery := c.Query("log")
	if len(logQuery) == 0 {
		logger.GetSugaredLogger().Error("jd callback missing parameters")
		c.JSON(200, gin.H{"ret": -1, "msg": "missing parameters"})
		return
	}

	logQuery = strings.Replace(logQuery, " ", "+", -1)

	logger.GetSugaredLogger().Infof("jd callback begin log=%v", logQuery)
	// 解析log参数
	logDecode, err := utils.DecodeString(logQuery)
	if err != nil {
		logger.GetSugaredLogger().Errorf("jd callback decode log error: %v", err)
		c.JSON(200, gin.H{"ret": -1, "msg": "decode log error"})
		return
	}

	log, err := url.ParseQuery(string(logDecode))
	if err != nil {
		logger.GetSugaredLogger().Errorf("jd callback parse log error: %v", err)
		c.JSON(200, gin.H{"ret": -1, "msg": "parse log error"})
		return
	}

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.GetSugaredLogger().Errorf("jd callback bigdata panic: %v", err)
			}
		}()

		// 调用大数据上报接口
		models.BigDataHoloActive(c, log)
	}()

	// logger.GetSugaredLogger().Info("JDAct callback finish")
	c.JSON(200, gin.H{"ret": 0, "msg": "ok"})
}
