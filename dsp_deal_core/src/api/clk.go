package api

import (
	"dsp_core/core"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"math/rand"
	"net/url"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
)

// Clk ...
func Clk(c *gin.Context) {
	// logger.GetSugaredLogger().Info("click begin")

	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		logger.GetSugaredLogger().Info("cpa clk extDspChannel nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// xxxxxx
	// marketType := log.Get("market_type")
	adsType := log.Get("ads_type")
	extDspChannel := log.Get("ext_dsp_channel")
	logger.GetSugaredLogger().Infof("clk extDspChannel=%v, adsType=%v, log=%v", extDspChannel, adsType, log)

	if adsType == "1" && extDspChannel == "0" {
		// 大航海
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("dhh clk panic: %v", err)
				}
			}()
			core.DhhClkReport(c, log)
		}()

	} else if adsType == "1" && extDspChannel == "3" {
		// 腾讯游戏
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("tencent clk panic: %v", err)
				}
			}()
			core.TencentSSPClk(c, log)
		}()
	} else if adsType == "1" && extDspChannel == "5" {
		// 高德地图
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("amap clk panic: %v", err)
				}
			}()
			core.AMapClkReport(c, log)
		}()
	} else if adsType == "1" && extDspChannel == "8" {
		// 快手
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("kuaishou clk panic: %v", err)
				}
			}()
			core.KuaiShouClkReport(c, log, "clk")
			// core.KuaiShouOCPXClkReport(c, log)
		}()
	} else if adsType == "1" && extDspChannel == "9" {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("uc clk panic: %v", err)
				}
			}()
			core.UCClkReport(c, log)
		}()
	} else if adsType == "1" && extDspChannel == "10" {
		// 聚媒通义
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("jumei_tongyi clk panic: %v", err)
				}
			}()
			core.JumeiTongYiClkReport(c, log, "clk")
		}()
	} else if adsType == "1" && extDspChannel == "11" {
		// 聚媒盼之代售
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("jumei_pzds clk panic: %v", err)
				}
			}()
			core.JumeiPZDSClkReport(c, log, "clk")
		}()
	} else if adsType == "1" && extDspChannel == "12" {
		// 微博
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("weibo clk panic: %v", err)
				}
			}()
			core.WeiBoClkReport(c, log, "clk")
		}()
	} else if adsType == "1" && extDspChannel == "13" {
		// 支付宝OCPX点击上报
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("alipay clk panic: %v", err)
				}
			}()
			core.AlipayReport(c, log, "click")
		}()
	} else if adsType == "1" && extDspChannel == "14" {
		// 京东科技OCPX点击上报
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("jd clk panic: %v", err)
				}
			}()
			core.JDReport(c, log, "click")
		}()
	}

	// 处理自营策略
	ClkPolicy(c, log)

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.GetSugaredLogger().Errorf("bigdata click panic: %v", err)
			}
		}()
		models.BigDataClk(c, log)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok clk"

	// for debug
	if c.Query("mh_debug") == "1" {
		debugInfoMap := map[string]interface{}{}
		debugInfoMap["reqid"] = log.Get("uid")
		debugInfoMap["plan_id"] = log.Get("plan_id")
		debugInfoMap["os"] = log.Get("os")
		debugInfoMap["osv"] = log.Get("osv")
		debugInfoMap["did_md5"] = log.Get("did_md5")
		debugInfoMap["imei"] = log.Get("imei")
		debugInfoMap["imei_md5"] = log.Get("imei_md5")
		debugInfoMap["android_id"] = log.Get("android_id")
		debugInfoMap["android_id_md5"] = log.Get("android_id_md5")
		debugInfoMap["idfa"] = log.Get("idfa")
		debugInfoMap["idfa_md5"] = log.Get("idfa_md5")
		debugInfoMap["ip"] = c.ClientIP()
		debugInfoMap["ua"] = c.GetHeader("User-Agent")
		debugInfoMap["oaid"] = log.Get("oaid")
		debugInfoMap["oaid_md5"] = log.Get("oaid_md5")
		debugInfoMap["model"] = log.Get("model")
		debugInfoMap["manufacturer"] = log.Get("manufacturer")
		debugInfoMap["cpc_price"] = utils.ConvertStringToInt(log.Get("cpc_price"))
		debugInfoMap["ext_cpc_price"] = utils.ConvertStringToInt(log.Get("ext_cpc_price"))

		resp["debug"] = debugInfoMap
	}
	c.PureJSON(200, resp)
}

func ClkPolicy(c *gin.Context, log url.Values) {

	// plan_id
	planID := log.Get("plan_id")
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("ClkPolicy plan_id not found, planId=%v, log=%v", planID, log)
		return
	}

	// os
	// ip := c.ClientIP()
	tmpDid := log.Get("did_md5")
	if len(planID) == 0 || len(tmpDid) == 0 {
		logger.GetSugaredLogger().Errorf("ClkPolicy plan_id or did_md5 not found, planId=%v, did_md5=%v, log=%v", planID, tmpDid, log)
		return
	}

	t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
	// nextUnixTime := t.Unix() + 2
	// logger.GetSugaredLogger().Info("next unix time: ", nextUnixTime)
	leftUnixTime := t.Unix() + 2 - time.Now().Unix()
	// logger.GetSugaredLogger().Info("left unix time: ", leftUnixTime)

	// ttl 0:00 - 3:00
	leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

	// 判断是否重复点击
	reqId := log.Get("uid")
	curDate := time.Now().Format("2006-01-02")
	uniqueRedisKey := "max_clk_total_" + curDate + "_" + reqId
	uniqValue, _ := db.GetRedis().Get(c, utils.Timeout50mill, uniqueRedisKey)
	defer db.GetRedis().Set(c, utils.Timeout50mill, uniqueRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
	logger.GetSugaredLogger().Infof("ClkPolicy reqId=%v, uniqueRedisKey=%v, uniqValue=%v", reqId, uniqueRedisKey, uniqValue)
	if planInfo.MaxClkType == "1" && uniqValue != "1" {
		// 单日点击限制
		tmpRedisKey := "max_clk_total_" + curDate + "_" + planID
		redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)
		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
		} else {
			// logger.GetSugaredLogger().Info("redis value:", redisValue)
			redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(redisValue), time.Duration(leftUnixTime)*time.Second)
		}
	}

	if planInfo.MaxClkDeviceType == "1" && uniqValue != "1" {
		// 单日设备点击限制
		tmpRedisKey := "limit_device_clk_" + curDate + "_" + planID + "_" + tmpDid
		redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)
		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
		} else {
			// logger.GetSugaredLogger().Info("redis value:", redisValue)
			redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(redisValue), time.Duration(leftUnixTime)*time.Second)
		}
	}
}
