package api

import (
	"context"
	"dsp_core/core"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"log"
	"math/rand"
	"strings"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/ip_to_region/core/ip2location"
	"github.com/gin-gonic/gin"
)

// FlowReq ...
/*
{
	"sdk_version": "*******",
	"device": {
		"os": "android",
		"os_version": "14",
		"model": "M2011K2C",
		"manufacturer": "Xiaomi",
		"device_type": 1,
		"android_id": "1df2632e006986c5",
		"oaid": "78fda2910b467103",
		"ua": "Mozilla/5.0 (Linux; Android 14; M2011K2C Build/UKQ1.231207.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.6099.193 Mobile Safari/537.36",
		"ip": "**************",
		"did_md5": "4f35f9c9af8e8006"
	},
	"media": {
		"app_id": "10012"
	},
	"pos": {
		"pos_id": "58761",
		"pos_type": "native"
	},
	"network": {
		"connect_type": 1
	},
	"geo": {
		"lat": 40.040916442871094,
		"lng": 116.32888793945312
	}
}
*/

// FlowReq ...
func FlowReq(c *gin.Context) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		log.Println(err)
		var resp models.DspRespStu
		resp.Code = 900101

		c.PureJSON(200, resp)
		return
	}
	var dspReq models.DspReqStu

	err = json.Unmarshal([]byte(bodyContent), &dspReq)
	log.Println("dsp flow req: ", string(bodyContent))
	if err != nil {
		log.Println(err)
		var resp models.DspRespStu
		resp.Code = 900101

		c.PureJSON(200, resp)
		return
	}

	// ctx
	ctx, cancel := context.WithTimeout(c, 200*time.Millisecond)
	defer cancel() // 确保在函数返回时取消 context
	ch := make(chan any, 1)

	go func() {
		resp := FlowReqGoroutine(ctx, dspReq)
		ch <- resp
		close(ch)
	}()

	// 使用 select 语句监听 context 的 Done() 通道
	select {
	case <-ctx.Done():
		// 如果 Done() 通道被关闭，说明 context 被取消或超时
		log.Printf("FlowReq Done err=%v", ctx.Err())
		var resp models.DspRespStu
		resp.Code = 900317

		c.PureJSON(200, resp)
		return
	case res := <-ch:
		jsonData, _ := json.Marshal(res)
		log.Println("kbg_debug_flow resp: ", string(jsonData))

		c.PureJSON(200, res)
		return
	}
}

// FlowReqGoroutine ...
func FlowReqGoroutine(c context.Context, dspReq models.DspReqStu) any {

	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, dspReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		dspReq.Device.LBSIPCountry = ip2locationResp.Country
		dspReq.Device.LBSIPProvince = ip2locationResp.Region
		dspReq.Device.LBSIPCity = ip2locationResp.City
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 通过媒体id和广告位类型查流量组id array
	var supplyAppGroupIDArray []string
	if true {
		var supplyAppGroupArray []models.DspSupplyAppGroupConfigStu
		cacheValue, cacheError := db.GlbBigCache.Get("go_dsp_all_supply_app_group_config")
		if cacheError != nil {
			var resp models.DspRespStu
			resp.Code = 900102

			return resp
		}
		json.Unmarshal(cacheValue, &supplyAppGroupArray)
		for _, supplyAppGroupItem := range supplyAppGroupArray {
			if isInArray(dspReq.Media.SupplyAppID, supplyAppGroupItem.SupplyAppIDs) &&
				isInArray(dspReq.Pos.SupplyPosType, supplyAppGroupItem.PosTypes) {
				// 通过率
				if rand.Intn(10000) < supplyAppGroupItem.Weight {
					supplyAppGroupIDArray = append(supplyAppGroupIDArray, supplyAppGroupItem.UID)
				}
				continue
			}
		}
		if len(supplyAppGroupIDArray) == 0 {
			var resp models.DspRespStu
			resp.Code = 900102

			return resp
		}
	}

	log.Println("dsp flow req supplyappgroup uid: ", supplyAppGroupIDArray)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 通过流量组id查计划id array
	var planIDArray []string
	if true {
		var dspPlanArray []models.DspPlanStu
		cacheValue, cacheError := db.GlbBigCache.Get("go_dsp_all_plan_with_supply_app_group")
		if cacheError != nil {
			var resp models.DspRespStu
			resp.Code = 900103

			return resp
		}
		json.Unmarshal(cacheValue, &dspPlanArray)
		for _, supplyAppGroupID := range supplyAppGroupIDArray {
			for _, planItem := range dspPlanArray {
				if len(planItem.SupplyAppGroupIDList) > 0 {
					if isInArray(supplyAppGroupID, planItem.SupplyAppGroupIDList) {
						planIDArray = append(planIDArray, planItem.PID)
					}
				}
			}
		}
	}
	if len(planIDArray) == 0 {
		var resp models.DspRespStu
		resp.Code = 900103

		return resp
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 去重
	planIDArray = utils.RemoveDuplicates(planIDArray)
	log.Println("dsp flow req plan id: ", planIDArray)
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// 并发
	var wg sync.WaitGroup
	wg.Add(len(planIDArray))

	// 并发处理multiResp,需要对multiResp加锁
	var multiRespLock sync.Mutex
	var multiResp []models.DspPlanRespStu
	for i := 0; i < len(planIDArray); i++ {
		go func(planID string) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("serious panic:", err, planID)
					wg.Done()
				}
			}()

			// get resp by plan id
			tmpDspResp := core.GetFromDsp(c, &dspReq, planID)

			if tmpDspResp != nil && tmpDspResp.Resp.Code == 10000 {
				multiRespLock.Lock()
				multiResp = append(multiResp, *tmpDspResp)
				multiRespLock.Unlock()
			}

			wg.Done()
		}(planIDArray[i])
	}
	wg.Wait()
	////////////////////////////////////////////////////////////////////////////////////////////////////
	log.Println("dsp flow resp array: ", len(multiResp))
	if len(multiResp) == 0 {
		var resp models.DspRespStu
		resp.Code = 900104

		return resp
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////
	// resp
	var resp models.DspRespStu
	resp.Code = 10000

	var respData models.DspRespDataStu
	for _, respItem := range multiResp {
		if respItem.Resp.Code == 10000 {
			if len(respItem.Resp.Data) > 0 {
				for _, expItem := range respItem.Resp.Data[0].ExpLinks {
					tmpItem := expItem
					if strings.Contains(tmpItem, "miaozhen.com") {
						// 文档: https://docs.cn.miaozhen.com/api_tag_guide_ad.html
						if dspReq.Device.Os == "android" {
							tmpItem = strings.Replace(tmpItem, "__OS__", "0", -1)
							if len(dspReq.Device.Oaid) > 0 {
								tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
							}
							if len(dspReq.Device.Imei) > 0 {
								tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
							}
						} else if dspReq.Device.Os == "ios" {
							tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
							if len(dspReq.Device.Idfa) > 0 {
								tmpItem = strings.Replace(tmpItem, "__IDFA__", dspReq.Device.Idfa, -1)
							}

							if len(dspReq.Device.CAIDMulti) > 0 {
								var miaozhenCAIDArray []MiaoZhenCAIDMulti
								for _, item := range dspReq.Device.CAIDMulti {
									var miaozhenCAIDItem MiaoZhenCAIDMulti
									miaozhenCAIDItem.CAID = item.CAID
									miaozhenCAIDItem.CAIDVersion = item.CAIDVersion
									miaozhenCAIDArray = append(miaozhenCAIDArray, miaozhenCAIDItem)
								}

								tmpMiaoZhenCAIDByte, _ := json.Marshal(miaozhenCAIDArray)
								tmpItem = strings.Replace(tmpItem, "__CAID__", string(tmpMiaoZhenCAIDByte), -1)
							}
						}
						tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
					} else {
						tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
						tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
						tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
					}

					respData.ExpLinks = append(respData.ExpLinks, tmpItem)
				}

				for _, clkItem := range respItem.Resp.Data[0].ClkLinks {
					tmpItem := clkItem
					if strings.Contains(tmpItem, "miaozhen.com") {
						// 文档: https://docs.cn.miaozhen.com/api_tag_guide_ad.html
						if dspReq.Device.Os == "android" {
							tmpItem = strings.Replace(tmpItem, "__OS__", "0", -1)
							if len(dspReq.Device.Oaid) > 0 {
								tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
							}
							if len(dspReq.Device.Imei) > 0 {
								tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
							}
						} else if dspReq.Device.Os == "ios" {
							tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
							if len(dspReq.Device.Idfa) > 0 {
								tmpItem = strings.Replace(tmpItem, "__IDFA__", dspReq.Device.Idfa, -1)
							}

							if len(dspReq.Device.CAIDMulti) > 0 {
								var miaozhenCAIDArray []MiaoZhenCAIDMulti
								for _, item := range dspReq.Device.CAIDMulti {
									var miaozhenCAIDItem MiaoZhenCAIDMulti
									miaozhenCAIDItem.CAID = item.CAID
									miaozhenCAIDItem.CAIDVersion = item.CAIDVersion
									miaozhenCAIDArray = append(miaozhenCAIDArray, miaozhenCAIDItem)
								}

								tmpMiaoZhenCAIDByte, _ := json.Marshal(miaozhenCAIDArray)
								tmpItem = strings.Replace(tmpItem, "__CAID__", string(tmpMiaoZhenCAIDByte), -1)
							}
						}
						tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
					} else {
						tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
						tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
						tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
					}

					respData.ClkLinks = append(respData.ClkLinks, tmpItem)
				}

				// respData.ExpLinks = append(respData.ExpLinks, respItem.Resp.Data[0].ExpLinks...)
				// respData.ClkLinks = append(respData.ClkLinks, respItem.Resp.Data[0].ClkLinks...)
			}
		}
	}
	resp.Data = append(resp.Data, respData)

	tmpByte, _ := json.Marshal(resp)
	log.Println("dsp flow resp:", string(tmpByte))

	return resp
}

// error code
// 10000  正常返回
// 900101 请求解析错误
// 900102 流量组配置里, 未查到流量请求的流量组id
// 900103 未查到配置流量组id的计划id
// 900104 未查到通过计划策略的计划id
// 900105
// 900106
// 900107
// 900108
// 900109
// 900110
