package api

import (
	"fmt"
	"net/url"
	"testing"
)

func TestCheckHologres3(t *testing.T) {
	// 构建CheckHologres3接口的查询参数
	params := url.Values{}
	params.Add("uid", "test-uid-12345")
	params.Add("group_id", "test-group-67890")
	params.Add("plan_id", "test-plan-abcde")
	params.Add("market_type", "1")
	params.Add("ads_type", "2")
	params.Add("ext_dsp_channel", "10")
	params.Add("os", "android")
	params.Add("osv", "11.0")
	params.Add("did_md5", "test-did-md5-hash")
	params.Add("imei", "test-imei-123456")
	params.Add("imei_md5", "test-imei-md5-hash")
	params.Add("android_id", "test-android-id")
	params.Add("android_id_md5", "test-android-id-md5")
	params.Add("idfa", "test-idfa-value")
	params.Add("idfa_md5", "test-idfa-md5-hash")
	params.Add("oaid", "test-oaid-value")
	params.Add("oaid_md5", "test-oaid-md5-hash")
	params.Add("model", "Samsung Galaxy S21")
	params.Add("manufacturer", "Samsung")
	params.Add("crid", "test-creative-id")
	params.Add("cpm_price", "1000")
	params.Add("ext_cpm_price", "1200")
	params.Add("deal_time", "1640995200000000") // 2022-01-01 00:00:00 UTC in microseconds
	params.Add("supply_crid", "test-supply-crid")
	params.Add("demand_crid", "test-demand-crid")

	// 将参数编码为查询字符串
	queryString := params.Encode()

	// 打印测试参数信息
	fmt.Printf("=== CheckHologres3 接口测试 ===\n")
	fmt.Printf("查询参数: %s\n\n", queryString)

	// 生成本地测试的curl命令
	fmt.Printf("本地测试curl命令:\n")
	fmt.Printf("curl -X GET \"http://localhost:8081/api/testHolo3?%s\"\n\n", queryString)

	// 生成生产环境的curl命令（假设的域名）
	fmt.Printf("生产环境curl命令:\n")
	fmt.Printf("curl -X GET \"https://dsp.maplehaze.cn/api/testHolo3?%s\"\n\n", queryString)

	// 生成带User-Agent头的curl命令
	fmt.Printf("带User-Agent的curl命令:\n")
	fmt.Printf("curl -X GET \"http://localhost:8081/api/testHolo3?%s\" \\\n", queryString)
	fmt.Printf("     -H \"User-Agent: Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36\"\n\n")

	// 打印参数说明
	fmt.Printf("=== 参数说明 ===\n")
	fmt.Printf("uid: 用户唯一标识\n")
	fmt.Printf("group_id: 分组ID\n")
	fmt.Printf("plan_id: 计划ID\n")
	fmt.Printf("market_type: 市场类型\n")
	fmt.Printf("ads_type: 广告类型\n")
	fmt.Printf("ext_dsp_channel: 外部DSP渠道\n")
	fmt.Printf("os: 操作系统\n")
	fmt.Printf("osv: 操作系统版本\n")
	fmt.Printf("cpm_price: CPM价格（分）\n")
	fmt.Printf("deal_time: 交易时间（微秒时间戳）\n")
}
