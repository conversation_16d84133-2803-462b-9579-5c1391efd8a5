package api

import (
	"context"
	"dsp_core/base"
	"dsp_core/config"
	"dsp_core/core"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"encoding/json"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/ip_to_region/core/ip2location"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// Req ...
func Req(c *gin.Context) {
	bodyContent, err := c.GetRawData()
	if err != nil {
		logger.GetSugaredLogger().Errorf("Req GetRawData err:%v", err)
	}
	var dspReq models.DspReqStu

	err = json.Unmarshal([]byte(bodyContent), &dspReq)

	if err != nil {
		logger.GetSugaredLogger().<PERSON>rrorf("Req Unmarshal err:%v", err)
	}

	bigdataUID := uuid.NewV4().String()

	// if dspReq.Pos.PlanID == "d035272db972e2e1" {
	// 	go models.BigDataDebug1(c, bigdataUID+"&a", "req", dspReq.Pos.PlanID, dspReq.Pos.PlanID, dspReq.Pos.PlanID)
	// }

	// ctx
	ctx, cancel := context.WithTimeout(c, 200*time.Millisecond)
	defer cancel() // 确保在函数返回时取消 context
	ch := make(chan any, 1)

	go func() {
		resp := ReqGoroutine(ctx, dspReq, bigdataUID)
		ch <- resp
		close(ch)
	}()

	// 使用 select 语句监听 context 的 Done() 通道
	select {
	case <-ctx.Done():
		// 如果 Done() 通道被关闭，说明 context 被取消或超时
		// logger.GetSugaredLogger().Infof("Req Done err:%v", ctx.Err())
		var planInfo models.DspPlanStu
		planInfo.PID = dspReq.Pos.PlanID
		c.PureJSON(200, getNoFillReturnWithSaveBigdata(c, dspReq, &planInfo, bigdataUID, models.ErrCodeTimeout, models.ReasonTimeout))
		return
	case res := <-ch:
		// jsonData, _ := json.Marshal(res)
		// logger.GetSugaredLogger().Info("kbg_debug_req resp: ", string(jsonData))

		c.PureJSON(200, res)
		return
	}
}

// ReqGoroutine ...
func ReqGoroutine(c context.Context, dspReq models.DspReqStu, bigdataUID string) any {

	// ip location
	ip2locationDB, ip2location, _ := ip2location.NewIp2location()
	defer ip2locationDB.Close()
	// 通过ip接口取值
	ip2locationResp, _ := ip2location.Convert(ip2locationDB, dspReq.Device.IP)
	if ip2locationResp == nil {
	} else {
		dspReq.Device.LBSIPCountry = ip2locationResp.Country
		dspReq.Device.LBSIPProvince = ip2locationResp.Region
		dspReq.Device.LBSIPCity = ip2locationResp.City
	}

	// logger.GetSugaredLogger().Info(dspReq.Pos)
	// logger.GetSugaredLogger().Info(dspReq.Device)
	// logger.GetSugaredLogger().Info(string(bodyContent))
	// logger.GetSugaredLogger().Info(err)
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, dspReq.Pos.PlanID)

	// 计划组
	var destPlanGroupInfo *models.DspPlanGroupStu
	if planInfo == nil {
		// 通过cache取计划组信息
		destPlanGroupInfo = models.GetPlanGroupInfo(c, dspReq.Pos.PlanID)
		if destPlanGroupInfo == nil || len(destPlanGroupInfo.PlanGroupPlanInfo) == 0 {
			// logger.GetSugaredLogger().Info("wrong pid:", dspReq.Pos.PlanID)
			respMap := map[string]interface{}{}
			respMap["code"] = models.ErrCodePlanGroupNotFound

			return respMap
		}

		// 过滤出计划组里的通过策略的计划
		getDestPlanGroupInfoByPolicy(c, dspReq, destPlanGroupInfo, bigdataUID)
		if destPlanGroupInfo == nil || len(destPlanGroupInfo.PlanGroupPlanInfo) == 0 {
			respMap := map[string]interface{}{}
			respMap["code"] = models.ErrCodeNoValidPlan

			return respMap
		}
	}

	// 单个计划策略是否OK
	if planInfo != nil {
		isPolicyOK, policyErrorCode, policyReason := isPlanPolicyOK(c, dspReq, planInfo, bigdataUID, true)
		// logger.GetSugaredLogger().Infof("isPlanPolicyOK isPolicyOK=%v, policyErrorCode=%v, policyReason=%v", isPolicyOK, policyErrorCode, policyReason)
		if isPolicyOK {
		} else {
			return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, policyErrorCode, policyReason)
		}
	}

	// extraStr,  0(正常投放),  1(大航海),  2(腾讯游戏),  5(高德地图), 8(快手), 12(微博), 13(支付宝)
	extraStr := "0"

	// dhh amap ks plan
	if planInfo != nil {
		// 上报rta之前的统计请求数量
		go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeRtaBefore, models.ReasonRtaBefore)

		if planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "0" {
			extraStr = "1"
			if core.IsDangHangHaiOK(c, &dspReq, planInfo.DHHChannelID, planInfo.DHHPosID, planInfo.DhhTaskID, bigdataUID, planInfo) {

			} else {
				// logger.GetSugaredLogger().Info("dsp dahanghai failed")
				return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeRtaFailed, models.ReasonRtaFailed)
			}
		} else if planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "5" {
			extraStr = "5"
			if core.IsAmapOK(c, bigdataUID, &dspReq, planInfo) {

			} else {
				// logger.GetSugaredLogger().Info("amap failed")
				return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeRtaFailed, models.ReasonRtaFailed)
			}
		} else if planInfo.GroupAdsType == "1" &&
			(planInfo.GroupExtDspChannel == "8" ||
				planInfo.GroupExtDspChannel == "12" ||
				planInfo.GroupExtDspChannel == "13" ||
				planInfo.GroupExtDspChannel == "14") {

			extraStr = planInfo.GroupExtDspChannel
			if planInfo.IsRTA == 1 {
				if planInfo.IsRepetition == 1 && planInfo.CrowdLibType == "1" && len(planInfo.CrowdLibID) > 0 && planInfo.IsRTA == 1 {
					isPlanPolicyCrowdLibOK, _, _ := base.IsPlanPolicyCrowdLibOK(c, dspReq, planInfo, bigdataUID)
					kuaishouRtaOK := core.IsRtaOK(c, bigdataUID, &dspReq, planInfo)
					if kuaishouRtaOK || isPlanPolicyCrowdLibOK {

					} else {
						// 上报人群包不通过数
						go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeCrowdLib, models.ReasonCrowdLib)

						// logger.GetSugaredLogger().Info("ks rta failed")
						return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeRtaFailed, models.ReasonRtaFailed)
					}
				} else {
					if core.IsRtaOK(c, bigdataUID, &dspReq, planInfo) {

					} else {
						// logger.GetSugaredLogger().Info("ks rta failed")
						return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeRtaFailed, models.ReasonRtaFailed)
					}
				}
			}
		}

		// logger.GetSugaredLogger().Infof("ReqGoroutine extraStr=%v, planInfo.IsRTA=%v, IsRepetition=%v, CrowdLibType=%v, CrowdLibID=%v", extraStr, planInfo.IsRTA, planInfo.IsRepetition, planInfo.CrowdLibType, planInfo.CrowdLibID)
	}

	// 计划组
	if destPlanGroupInfo != nil && len(destPlanGroupInfo.PlanGroupPlanInfo) >= 0 {
		isCrowdLibNotOK := false
		planInfo = nil
		for _, planGroupItem := range destPlanGroupInfo.PlanGroupPlanInfo {
			tmpPlanInfo := models.GetPlanInfoFromMysqlByPlanID(c, planGroupItem.PlanID)
			if tmpPlanInfo == nil {
				// logger.GetSugaredLogger().Errorf("ReqGoroutine wrong pid:%v\n", planGroupItem.PlanID)
				continue
			}

			if tmpPlanInfo.GroupAdsType == "1" &&
				(tmpPlanInfo.GroupExtDspChannel == "8" ||
					tmpPlanInfo.GroupExtDspChannel == "12" ||
					tmpPlanInfo.GroupExtDspChannel == "13" ||
					tmpPlanInfo.GroupExtDspChannel == "14") {

				extraStr = tmpPlanInfo.GroupExtDspChannel
				if tmpPlanInfo.IsRTA == 1 {
					if tmpPlanInfo.IsRepetition == 1 && tmpPlanInfo.CrowdLibType == "1" && len(tmpPlanInfo.CrowdLibID) > 0 && tmpPlanInfo.IsRTA == 1 {
						isPlanPolicyCrowdLibOK, _, _ := base.IsPlanPolicyCrowdLibOK(c, dspReq, tmpPlanInfo, bigdataUID)
						kuaishouRtaOK := core.IsRtaOK(c, bigdataUID, &dspReq, tmpPlanInfo)
						if kuaishouRtaOK || isPlanPolicyCrowdLibOK {
							planInfo = tmpPlanInfo
							break
						}
						isCrowdLibNotOK = true
					} else {
						kuaishouRtaOK := core.IsRtaOK(c, bigdataUID, &dspReq, tmpPlanInfo)
						if kuaishouRtaOK {
							planInfo = tmpPlanInfo
							break
						}
					}

				} else {
					planInfo = tmpPlanInfo
					break
				}
			}
		}

		if planInfo == nil {
			tmpPlanInfo := models.GetPlanInfoFromMysqlByPlanID(c, destPlanGroupInfo.PlanGroupPlanInfo[0].PlanID)

			if isCrowdLibNotOK {
				// 上报人群包不通过数
				go models.BigDataAdxReq(c, dspReq, tmpPlanInfo, bigdataUID, "", models.ErrCodeCrowdLib, models.ReasonCrowdLib)
			}

			// 返回rta不通过
			return getNoFillReturnWithSaveBigdata(c, dspReq, tmpPlanInfo, bigdataUID, models.ErrCodeRtaFailed, models.ReasonRtaFailed)
		}
	}

	// tencent
	if planInfo.GroupAdsType == "1" && planInfo.GroupExtDspChannel == "3" {
		extraStr = "2"
	}

	var dspCreatives []models.DspCreativeStu
	for _, respCreativeItem := range planInfo.Creatives {
		if dspReq.Pos.CreativeType == 0 {
			if respCreativeItem.CreativeType == 0 {
				if respCreativeItem.ImageWidth == dspReq.Pos.Width && respCreativeItem.ImageHeight == dspReq.Pos.Height {
					dspCreatives = append(dspCreatives, respCreativeItem)
				}
			}
		} else if dspReq.Pos.CreativeType == 1 {
			if respCreativeItem.CreativeType == 1 {
				if respCreativeItem.VideoWidth == dspReq.Pos.Width && respCreativeItem.VideoHeight == dspReq.Pos.Height {
					dspCreatives = append(dspCreatives, respCreativeItem)
				}
			}
		} else {
			if respCreativeItem.CreativeType == 0 {
				if respCreativeItem.ImageWidth == dspReq.Pos.Width && respCreativeItem.ImageHeight == dspReq.Pos.Height {
					dspCreatives = append(dspCreatives, respCreativeItem)
				}
			} else if respCreativeItem.CreativeType == 1 {
				if respCreativeItem.VideoWidth == dspReq.Pos.Width && respCreativeItem.VideoHeight == dspReq.Pos.Height {
					dspCreatives = append(dspCreatives, respCreativeItem)
				}
			}
		}
	}

	if len(dspCreatives) == 0 {
		return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeNoCreatives, models.ReasonNoCreatives)
	}

	var destCreative models.DspCreativeStu
	randCreativeValue := rand.Intn(len(dspCreatives))
	destCreative = dspCreatives[randCreativeValue]

	// resp
	respMap := map[string]interface{}{}
	respMap["code"] = models.ErrCodeSuccess
	var respDataArray []map[string]interface{}
	respDataMap := map[string]interface{}{}
	respDataMap["title"] = destCreative.Title
	respDataMap["description"] = destCreative.Description
	respDataMap["icon_url"] = destCreative.IconURL
	respDataMap["crid"] = destCreative.CRID
	respDataMap["material_type"] = destCreative.CreativeType
	if destCreative.CreativeType == 0 {
		respDataMap["image_url"] = destCreative.ImageURL
		respDataMap["image_width"] = destCreative.ImageWidth
		respDataMap["image_height"] = destCreative.ImageHeight
	} else if destCreative.CreativeType == 1 {
		respDataMap["video_url"] = destCreative.VideoURL
		respDataMap["cover_url"] = destCreative.CoverURL
		respDataMap["video_width"] = destCreative.VideoWidth
		respDataMap["video_height"] = destCreative.VideoHeight
		respDataMap["video_duration"] = destCreative.VideoDuration
	}

	if planInfo.PromotionType == 0 {
		respDataMap["h5_link"] = planInfo.H5Link
		if len(planInfo.DeepLink) > 0 {
			respDataMap["deep_link"] = planInfo.DeepLink
		}
		if len(planInfo.PackageName) > 0 {
			respDataMap["package_name"] = planInfo.PackageName
		}
		if len(planInfo.QuickAppLink) > 0 {
			respDataMap["quick_app_link"] = planInfo.QuickAppLink
		}
	} else if planInfo.PromotionType == 1 {
		respDataMap["h5_link"] = planInfo.H5Link
		respDataMap["download_link"] = planInfo.DownloadLink

		if len(planInfo.DeepLink) > 0 {
			respDataMap["deep_link"] = planInfo.DeepLink
		}
		if len(planInfo.StoreDeepLink) > 0 {
			respDataMap["market_url"] = planInfo.StoreDeepLink
			if IsManufactureXiaoMi(c, dspReq.Device.Manufacturer) {
				if len(planInfo.PackageName) > 0 {
					respDataMap["market_url"] = "mimarket://details?appClientId=2882303761517740913&detailStyle=2&id=" + planInfo.PackageName
				}
			}
		}
		if len(planInfo.AppName) > 0 {
			respDataMap["app_name"] = planInfo.AppName
		}
		if len(planInfo.PackageName) > 0 {
			respDataMap["package_name"] = planInfo.PackageName
		}
		if len(planInfo.PackageSize) > 0 {
			respDataMap["package_size"] = planInfo.PackageSize
		}
		if len(planInfo.Publisher) > 0 {
			respDataMap["publisher"] = planInfo.Publisher
		}
		if len(planInfo.AppVersion) > 0 {
			respDataMap["app_version"] = planInfo.AppVersion
		}
		if len(planInfo.AppVersionCode) > 0 {
			respDataMap["app_version_code"] = planInfo.AppVersionCode
		}
		if len(planInfo.PrivacyLink) > 0 {
			respDataMap["privacy_link"] = planInfo.PrivacyLink
		}
		if len(planInfo.Permissions) > 0 {
			respDataMap["permissons"] = planInfo.Permissions
		}
		if len(planInfo.AppInfo) > 0 {
			respDataMap["app_info"] = planInfo.AppInfo
		}
	}

	// SDK过滤已安装
	if planInfo.IsFilterPackageNames == 1 {
		respDataMap["filter_package_names"] = planInfo.FilterPackageNames
	}
	// SDK定向已安装
	if planInfo.IsDirectInstalledPackageNames == 1 {
		respDataMap["direct_install_package_names"] = planInfo.DirectInstalledPackageNames
	}

	// SDK过滤已安装开 or SDK定向已安装开, 判定sdk版本号>= 3.0.25
	if planInfo.IsFilterPackageNames == 1 || planInfo.IsDirectInstalledPackageNames == 1 {
		if IsSDKVersionMoreThanOrEqual3025(c, dspReq.SDKVersion) {
		} else {
			// logger.GetSugaredLogger().Infof("sdk version no ok, sdkVersion=%v", dspReq.SDKVersion)
			return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeSDKVersionNotOk, models.ReasonSDKVersionNotOk)
		}
	}

	if len(planInfo.H5Link) == 0 && len(planInfo.DownloadLink) == 0 {
		return getNoFillReturnWithSaveBigdata(c, dspReq, planInfo, bigdataUID, models.ErrCodeNoLink, models.ReasonNoLink)
	}

	// exp links
	var respListItemImpArray []string

	mhImpParams := url.Values{}
	// mhImpParams.Add("uid", bigdataUID)
	// mhImpParams.Add("plan_id", planInfo.PID)
	// mhImpParams.Add("extra", extraStr)
	bigdataParams := EncodeParams(&dspReq, planInfo, bigdataUID, extraStr, destCreative.CRID)
	mhImpParams.Add("log", bigdataParams)
	respListItemImpArray = append(respListItemImpArray, config.ExternalExpURL+"?"+mhImpParams.Encode())

	for _, expItem := range planInfo.ExpLinks {
		tmpItem := expItem
		if strings.Contains(tmpItem, "miaozhen.com") {
			// 文档: https://docs.cn.miaozhen.com/api_tag_guide_ad.html
			if dspReq.Device.Os == "android" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "0", -1)
				if len(dspReq.Device.Oaid) > 0 {
					tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
				}
				if len(dspReq.Device.Imei) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
				}
			} else if dspReq.Device.Os == "ios" {
				tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
				if len(dspReq.Device.Idfa) > 0 {
					tmpItem = strings.Replace(tmpItem, "__IDFA__", dspReq.Device.Idfa, -1)
				}

				if len(dspReq.Device.CAIDMulti) > 0 {
					var miaozhenCAIDArray []MiaoZhenCAIDMulti
					for _, item := range dspReq.Device.CAIDMulti {
						var miaozhenCAIDItem MiaoZhenCAIDMulti
						miaozhenCAIDItem.CAID = item.CAID
						miaozhenCAIDItem.CAIDVersion = item.CAIDVersion
						miaozhenCAIDArray = append(miaozhenCAIDArray, miaozhenCAIDItem)
					}

					tmpMiaoZhenCAIDByte, _ := json.Marshal(miaozhenCAIDArray)
					tmpItem = strings.Replace(tmpItem, "__CAID__", string(tmpMiaoZhenCAIDByte), -1)
				}
			}
			tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
		} else {
			tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
			tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
			tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
		}

		// tmpItem = strings.Replace(tmpItem, "__UA__", dspReq.Device.Ua, -1)
		respListItemImpArray = append(respListItemImpArray, tmpItem)
	}

	respDataMap["exp_links"] = respListItemImpArray

	// clk links
	var respListItemClkArray []string
	// click_link maplehaze
	mhClkParams := url.Values{}
	mhClkParams.Add("req_width", "__REQ_WIDTH__")
	mhClkParams.Add("req_height", "__REQ_HEIGHT__")
	mhClkParams.Add("width", "__WIDTH__")
	mhClkParams.Add("height", "__HEIGHT__")
	mhClkParams.Add("down_x", "__DOWN_X__")
	mhClkParams.Add("down_y", "__DOWN_Y__")
	mhClkParams.Add("up_x", "__UP_X__")
	mhClkParams.Add("up_y", "__UP_Y__")
	mhClkParams.Add("plan_id", planInfo.PID)
	mhClkParams.Add("log", bigdataParams)
	mhClkParams.Add("callback", "__MH_CALLBACK__")

	// click_link maplehaze
	respListItemClkArray = append(respListItemClkArray, config.ExternalClkURL+"?"+mhClkParams.Encode())
	if len(planInfo.ClkLinks) > 0 {
		for _, clkItem := range planInfo.ClkLinks {
			tmpItem := clkItem
			if strings.Contains(tmpItem, "miaozhen.com") {
				// 文档: https://docs.cn.miaozhen.com/api_tag_guide_ad.html
				if dspReq.Device.Os == "android" {
					tmpItem = strings.Replace(tmpItem, "__OS__", "0", -1)
					if len(dspReq.Device.Oaid) > 0 {
						tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
					}
					if len(dspReq.Device.Imei) > 0 {
						tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
					}
				} else if dspReq.Device.Os == "ios" {
					tmpItem = strings.Replace(tmpItem, "__OS__", "1", -1)
					if len(dspReq.Device.Idfa) > 0 {
						tmpItem = strings.Replace(tmpItem, "__IDFA__", dspReq.Device.Idfa, -1)
					}

					if len(dspReq.Device.CAIDMulti) > 0 {
						var miaozhenCAIDArray []MiaoZhenCAIDMulti
						for _, item := range dspReq.Device.CAIDMulti {
							var miaozhenCAIDItem MiaoZhenCAIDMulti
							miaozhenCAIDItem.CAID = item.CAID
							miaozhenCAIDItem.CAIDVersion = item.CAIDVersion
							miaozhenCAIDArray = append(miaozhenCAIDArray, miaozhenCAIDItem)
						}

						tmpMiaoZhenCAIDByte, _ := json.Marshal(miaozhenCAIDArray)
						tmpItem = strings.Replace(tmpItem, "__CAID__", string(tmpMiaoZhenCAIDByte), -1)
					}
				}
				tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
			} else {
				tmpItem = strings.Replace(tmpItem, "__IMEI__", dspReq.Device.Imei, -1)
				tmpItem = strings.Replace(tmpItem, "__OAID__", dspReq.Device.Oaid, -1)
				tmpItem = strings.Replace(tmpItem, "__IP__", dspReq.Device.IP, -1)
			}

			// tmpItem = strings.Replace(tmpItem, "__UA__", dspReq.Device.Ua, -1)
			respListItemClkArray = append(respListItemClkArray, tmpItem)
		}
	}

	respDataMap["clk_links"] = respListItemClkArray
	if planInfo.SspPriceType == 2 {
		tmpPrice := int(float32(dspReq.Pos.CPMBidFloor) * planInfo.SspDynamicCPMTimes)
		tmpMaxPrice := int(planInfo.SspDynamicCPMMaxNum * 100)
		if tmpPrice >= tmpMaxPrice {
			respDataMap["ecpm"] = tmpMaxPrice

			// logger.GetSugaredLogger().Info("kbg_debug_ecpm 0: ", planInfo.PID, planInfo.SspDynamicCPMTimes, planInfo.SspDynamicCPMMaxNum, dspReq.Pos.CPMBidFloor, tmpMaxPrice)
		} else {
			respDataMap["ecpm"] = tmpPrice

			// logger.GetSugaredLogger().Info("kbg_debug_ecpm 1: ", planInfo.PID, planInfo.SspDynamicCPMTimes, planInfo.SspDynamicCPMMaxNum, dspReq.Pos.CPMBidFloor, tmpPrice)
		}
	} else {
		respDataMap["ecpm"] = int(planInfo.SspPriceNum * 100)
	}

	respDataArray = append(respDataArray, respDataMap)
	respMap["data"] = respDataArray

	// 上报大数据
	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, destCreative.CRID, models.ErrCodeSuccess, models.ReasonSuccess)

	// tmpByte, _ := json.Marshal(respMap)
	// tmpString := string(tmpByte)
	// logger.GetSugaredLogger().Info("dsp resp: " + tmpString)

	return respMap

	// c.JSON(200, gin.H{
	// 	"message": "pong v1 1",
	// })
}

func getNoFillReturnWithSaveBigdata(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string, errorCode int, reason string) any {
	// logger.GetSugaredLogger().Infof("nofill, bigdataUID=%v, errorCode=%v, reason=%v", bigdataUID, errorCode, reason)

	go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", errorCode, reason)

	respMap := map[string]interface{}{}
	respMap["code"] = errorCode

	return respMap
}

// EncodeParams ...
func EncodeParams(dspReq *models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string, extraStr string, crid string) string {
	if planInfo == nil {
		// logger.GetSugaredLogger().Errorf("planInfo is nil error, bigdataUID=%v, extraStr=%v, crid=%v", bigdataUID, extraStr, crid)
		return ""
	}

	bigdataParams := url.Values{}
	bigdataParams.Add("uid", bigdataUID)
	bigdataParams.Add("group_id", planInfo.GID)
	bigdataParams.Add("plan_id", planInfo.PID)
	bigdataParams.Add("market_type", planInfo.GroupMarketingType)
	bigdataParams.Add("ads_type", planInfo.GroupAdsType)
	bigdataParams.Add("ext_dsp_channel", planInfo.GroupExtDspChannel)
	bigdataParams.Add("media_channel", "0")
	if extraStr == "1" {
		bigdataParams.Add("dhh_channel_id", planInfo.DHHChannelID)
		bigdataParams.Add("dhh_pos_id", planInfo.DHHPosID)
		bigdataParams.Add("dhh_task_id", planInfo.DhhTaskID)
	} else if extraStr == "2" {
		// bigdataParams.Add("tencent_gid", planInfo.TencentGID)
		// bigdataParams.Add("tencent_media", planInfo.TencentMedia)
		// bigdataParams.Add("tencent_channel", planInfo.TencentChannel)
		// bigdataParams.Add("tencent_sch_id", planInfo.TencentSchID)
		// bigdataParams.Add("tencent_loc_id", planInfo.TencentLocID)
		// bigdataParams.Add("tencent_scid", planInfo.TencentScID)
		// bigdataParams.Add("tencent_sc_name", planInfo.TencentScName)
		// bigdataParams.Add("tencent_mtr_id", planInfo.TencentMtrID)
		// bigdataParams.Add("tencent_o2_trace_id", planInfo.TencentO2TraceID)
	} else if extraStr == "5" {
		bigdataParams.Add("amap_source", planInfo.AMapSource)
	}
	// unit_price_type: 0 CPM, 1 CPC, 2 CPA
	if planInfo.UnitPriceNum > 0 {
		if planInfo.UnitPriceType == 0 {
			bigdataParams.Add("cpm_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 1 {
			bigdataParams.Add("cpc_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		} else if planInfo.UnitPriceType == 2 {
			bigdataParams.Add("cpa_price", utils.ConvertIntToString(int(planInfo.UnitPriceNum*100)))
		}
	}
	// ssp_price_type: 0 CPM, 1 CPC, 2 动态CPM
	if planInfo.SspPriceType == 0 {
		bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
	} else if planInfo.SspPriceType == 1 {
		bigdataParams.Add("ext_cpc_price", utils.ConvertIntToString(int(planInfo.SspPriceNum*100)))
	} else if planInfo.SspPriceType == 2 {
		tmpPrice := int(float32(dspReq.Pos.CPMBidFloor) * planInfo.SspDynamicCPMTimes)
		tmpMaxPrice := int(planInfo.SspDynamicCPMMaxNum * 100)
		if tmpPrice >= tmpMaxPrice {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(tmpMaxPrice))
		} else {
			bigdataParams.Add("ext_cpm_price", utils.ConvertIntToString(tmpPrice))
		}
	}
	bigdataParams.Add("os", dspReq.Device.Os)
	bigdataParams.Add("osv", dspReq.Device.OsVersion)
	bigdataParams.Add("did_md5", dspReq.Device.DIDMd5Key)
	if len(dspReq.Device.Imei) > 0 {
		bigdataParams.Add("imei", dspReq.Device.Imei)
	}
	if len(dspReq.Device.ImeiMd5) > 0 {
		bigdataParams.Add("imei_md5", dspReq.Device.ImeiMd5)
	}
	if len(dspReq.Device.AndroidID) > 0 {
		bigdataParams.Add("android_id", dspReq.Device.AndroidID)
	}
	if len(dspReq.Device.AndroidIDMd5) > 0 {
		bigdataParams.Add("android_id_md5", dspReq.Device.AndroidIDMd5)
	}
	if len(dspReq.Device.Oaid) > 0 {
		bigdataParams.Add("oaid", dspReq.Device.Oaid)
	}
	if len(dspReq.Device.OaidMd5) > 0 {
		bigdataParams.Add("oaid_md5", dspReq.Device.OaidMd5)
	}
	if len(dspReq.Device.Idfa) > 0 {
		bigdataParams.Add("idfa", dspReq.Device.Idfa)
	}
	if len(dspReq.Device.IdfaMd5) > 0 {
		bigdataParams.Add("idfa_md5", dspReq.Device.IdfaMd5)
	}
	// if len(dspReq.Device.CAID) > 0 && len(dspReq.Device.CAIDVersion) > 0 {
	// 	bigdataParams.Add("caid", dspReq.Device.CAID)
	// 	bigdataParams.Add("caid_version", dspReq.Device.CAIDVersion)
	// }

	if len(dspReq.Device.CAIDMulti) > 0 {
		tmpByte, _ := json.Marshal(dspReq.Device.CAIDMulti)
		bigdataParams.Add("caid_multi", string(tmpByte))
	}

	bigdataParams.Add("model", dspReq.Device.Model)
	bigdataParams.Add("manufacturer", dspReq.Device.Manufacturer)
	bigdataParams.Add("crid", crid)
	bigdataParams.Add("deal_time", utils.ConvertInt64ToString(time.Now().UnixMicro()))

	encodeStr, _ := utils.EncodeString([]byte(bigdataParams.Encode()))
	// encodeStr = url.QueryEscape(encodeStr)

	return encodeStr
}

// isPlanPolicyOK 注意：开启复投,rta和人群包开关都开启的情况不判断人群包。
// reportCrowLib 判断人群包之前是否上报人群包，为了准确计算人群包通过率
// return: 是否通过, 错误码, 错误原因
func isPlanPolicyOK(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string, reportCrowLib bool) (bool, int, string) {
	// 用户定向，请求通过率
	rv := rand.Intn(100)
	// logger.GetSugaredLogger().Infof("plan request isPlanPolicyOk pass rate, pid=%v, name=%v, rv=%v, passRateType=%v, passRateVal=%v", planInfo.PID, planInfo.PName, rv, planInfo.RequestPassRateVal, planInfo.RequestPassRateVal)
	if planInfo.RequestPassRateType == "1" && len(planInfo.RequestPassRateVal) > 0 && rv > utils.ConvertStringToInt(planInfo.RequestPassRateVal) {
		return false, models.ErrCodePassRate, models.ReasonPassRate
	}

	// 价格
	if planInfo.SspPriceType == 2 {
		if dspReq.Pos.CPMBidFloor > int(planInfo.SspDynamicCPMMaxNum*100) {
			// logger.GetSugaredLogger().Info("req wrong ecpm: ", planInfo.PID, dspReq.Pos.CPMBidFloor, planInfo.SspDynamicCPMMaxNum)
			return false, models.ErrCodeEcpm, models.ReasonEcpm
		}
	}

	// 设备厂商
	if core.IsManufacturerOK(c, dspReq, planInfo) {
	} else {
		// logger.GetSugaredLogger().Info("req wrong manufacturer: ", planInfo.PID, dspReq.Device.Manufacturer, planInfo.AndroidManufacturer)
		return false, models.ErrCodeManufacturer, models.ReasonManufacturer
	}

	// 安卓版本限制
	if dspReq.Device.Os == "android" {
		if planInfo.AndroidVersion == "all" {
		} else {
			osvMajor := 0
			if len(dspReq.Device.OsVersion) > 0 {
				osvMajorStr := strings.Split(dspReq.Device.OsVersion, ".")[0]
				osvMajor = utils.ConvertStringToInt(osvMajorStr)
				// logger.GetSugaredLogger().Info(osvMajor)
			}

			if osvMajor > utils.ConvertStringToInt(planInfo.AndroidVersion) {
			} else {
				return false, models.ErrCodeAndroidVersion, models.ReasonAndroidVersion
			}
		}
	}
	// 安卓版本过滤限制
	if IsAndroidVersionFilterOK(c, &dspReq, planInfo) {
	} else {
		// logger.GetSugaredLogger().Info("req android version filter failter: ", planInfo.PID, dspReq.Device.Manufacturer, dspReq.Device.OsVersion)
		return false, models.ErrCodeAndroidVersion, models.ReasonAndroidVersion
	}

	// sdk版本限制
	if len(planInfo.SDKVersion) > 0 && len(dspReq.SDKVersion) > 0 {
		tmpReqVersionArray := strings.Split(dspReq.SDKVersion, ".")
		tmpConfigVersionArray := strings.Split(planInfo.SDKVersion, ".")

		tmpReqVersion0 := 0
		tmpConfigVersion0 := 0

		tmpReqVersion1 := 0
		tmpConfigVersion1 := 0

		tmpReqVersion2 := 0
		tmpConfigVersion2 := 0

		tmpReqVersion3 := 0
		tmpConfigVersion3 := 0

		if len(tmpReqVersionArray) > 0 && len(tmpConfigVersionArray) > 0 {
			if utils.IsNum(tmpReqVersionArray[0]) && utils.IsNum(tmpConfigVersionArray[0]) {
				tmpReqVersion0 = utils.ConvertStringToInt(tmpReqVersionArray[0])
				tmpConfigVersion0 = utils.ConvertStringToInt(tmpConfigVersionArray[0])
				if tmpReqVersion0 > tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 1 && len(tmpConfigVersionArray) > 1 {
			if utils.IsNum(tmpReqVersionArray[1]) && utils.IsNum(tmpConfigVersionArray[1]) {
				tmpReqVersion1 = utils.ConvertStringToInt(tmpReqVersionArray[1])
				tmpConfigVersion1 = utils.ConvertStringToInt(tmpConfigVersionArray[1])
				if tmpReqVersion1 > tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 2 && len(tmpConfigVersionArray) > 2 {
			if utils.IsNum(tmpReqVersionArray[2]) && utils.IsNum(tmpConfigVersionArray[2]) {
				tmpReqVersion2 = utils.ConvertStringToInt(tmpReqVersionArray[2])
				tmpConfigVersion2 = utils.ConvertStringToInt(tmpConfigVersionArray[2])
				if tmpReqVersion2 > tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
		if len(tmpReqVersionArray) > 3 && len(tmpConfigVersionArray) > 3 {
			if utils.IsNum(tmpReqVersionArray[3]) && utils.IsNum(tmpConfigVersionArray[3]) {
				tmpReqVersion3 = utils.ConvertStringToInt(tmpReqVersionArray[3])
				tmpConfigVersion3 = utils.ConvertStringToInt(tmpConfigVersionArray[3])
				if tmpReqVersion3 > tmpConfigVersion3 && tmpReqVersion2 == tmpConfigVersion2 && tmpReqVersion1 == tmpConfigVersion1 && tmpReqVersion0 == tmpConfigVersion0 {
					return false, models.ErrCodeSdkVersion, models.ReasonSdkVersion
				}
			}
		}
	}

	// 地域限制
	if planInfo.LBSType == 1 {
		if len(planInfo.LBSWhiteList) == 0 && len(planInfo.LBSBlackList) == 0 {
			return false, models.ErrCodeLbs, models.ReasonLbs
		}
		if len(dspReq.Device.LBSIPProvince) == 0 || len(dspReq.Device.LBSIPCity) == 0 {
			return false, models.ErrCodeLbs, models.ReasonLbs
		}
		// 地域白名单
		if len(planInfo.LBSWhiteList) > 0 {
			isInWhiteCity := false
			for _, item := range planInfo.LBSWhiteList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInWhiteCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInWhiteCity = true
						break
					}
				}
			}

			if isInWhiteCity {
			} else {
				return false, models.ErrCodeLbs, models.ReasonLbs
			}
		}

		// 地域黑名单
		if len(planInfo.LBSBlackList) > 0 {
			isInBlackCity := false
			for _, item := range planInfo.LBSBlackList {
				if len(item) == 1 {
					if item[0] == dspReq.Device.LBSIPProvince {
						isInBlackCity = true
						break
					}
				} else if len(item) == 2 {
					if item[0] == dspReq.Device.LBSIPProvince && item[1] == dspReq.Device.LBSIPCity {
						isInBlackCity = true
						break
					}
				}
			}

			if isInBlackCity {
				return false, models.ErrCodeLbs, models.ReasonLbs
			}
		}
	}

	// 限制类型: 0 不限制, 1 限制
	// 单设备请求间隔设置
	if planInfo.MaxReqDeviceIntervalType == "1" {
		tmpRedisKey := "limit_device_req_interval_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + dspReq.Device.DIDMd5Key
		_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(int64(utils.ConvertStringToInt(planInfo.MaxReqDeviceIntervalNum)))*time.Millisecond)
		} else {
			// logger.GetSugaredLogger().Info("plan max device req interval failed, os: " + dspReq.Device.Os + ", did: " + dspReq.Device.DIDMd5Key + ", id: " + planInfo.PID)
			return false, models.ErrCodeMaxDeviceReqInterval, models.ReasonMaxDeviceReqInterval
		}
	}
	// 单日曝光限制
	if planInfo.MaxExpType == "1" {
		tmpRedisKey := "max_exp_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
		tmpRedisValue, cacheErr := models.CacheFromRedis(c, tmpRedisKey)

		if cacheErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			// logger.GetSugaredLogger().Info("dsp pos max exp total redis key: ", maxReqRedisKey)
			// logger.GetSugaredLogger().Info("dsp pos max exp total redis value: ", tmpRedisInt)
			// logger.GetSugaredLogger().Info("dsp pos max exp total value: ", selfPosInfo.MaxExpTotal)
			if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxExpNum) {
				// logger.GetSugaredLogger().Info("dsp pos max exp failed, " + planInfo.PID)
				return false, models.ErrCodeMaxExpTotal, models.ReasonMaxExpTotal
			}
		}
	}
	// 单日点击限制
	if planInfo.MaxClkType == "1" {
		tmpRedisKey := "max_clk_total_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID
		tmpRedisValue, cacheErr := models.CacheFromRedis(c, tmpRedisKey)

		if cacheErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
		} else {
			tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
			if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxClkNum) {
				// logger.GetSugaredLogger().Info("dsp pos max clk failed, " + planInfo.PID)
				return false, models.ErrCodeMaxClkTotal, models.ReasonMaxClkTotal
			}
		}
	}
	// 单日设备曝光限制, 单日设备点击限制
	if planInfo.MaxExpDeviceType == "1" || planInfo.MaxClkDeviceType == "1" || planInfo.MaxLimitIPType == "1" {
		tmpDid := dspReq.Device.DIDMd5Key
		if len(tmpDid) > 0 {
			// 单日设备曝光限制
			if planInfo.MaxExpDeviceType == "1" {
				tmpRedisKey := "limit_device_exp_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpDid
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxExpDeviceNum) {
						// logger.GetSugaredLogger().Info("plan max device exp failed, os: " + dspReq.Device.Os + ", did: " + tmpDid + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceExp, models.ReasonMaxDeviceExp
					}
				}
			}
			// 单日设备点击限制
			if planInfo.MaxClkDeviceType == "1" {
				tmpRedisKey := "limit_device_clk_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpDid
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxClkDeviceNum) {
						// logger.GetSugaredLogger().Info("plan max device clk failed, os: " + dspReq.Device.Os + ", did: " + tmpDid + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceClk, models.ReasonMaxDeviceClk
					}
				}
			}
		}
		// 单日ip请求限制
		tmpIP := dspReq.Device.IP
		if len(tmpIP) > 0 {
			if planInfo.MaxLimitIPType == "1" {
				tmpRedisKey := "limit_ip_" + time.Now().Format("2006-01-02") + "_" + planInfo.PID + "_" + tmpIP
				tmpRedisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, tmpRedisKey)

				if redisErr != nil {
					// logger.GetSugaredLogger().Info("redis error:", redisErr)
				} else {
					tmpRedisInt := utils.ConvertStringToInt(tmpRedisValue)
					if tmpRedisInt >= utils.ConvertStringToInt(planInfo.MaxLimitIPNum) {
						// logger.GetSugaredLogger().Info("plan max device ip failed, os: " + dspReq.Device.Os + ", ip: " + tmpIP + ", id: " + planInfo.PID)
						return false, models.ErrCodeMaxDeviceIp, models.ReasonMaxDeviceIp
					}
				}

				// ip通过限制请求
				t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
				leftUnixTime := t.Unix() + 2 - time.Now().Unix()

				// ttl 0:00 - 3:00
				leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

				if redisErr != nil {
					redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second)
				} else {
					redisErr = db.GetRedis().Set(c, utils.Timeout50mill, tmpRedisKey, 1+utils.ConvertStringToInt(tmpRedisValue), time.Duration(leftUnixTime)*time.Second)
				}
			}
		}
	}
	// 日期限制
	if planInfo.DateType == "1" {
		tmpSplitArray := strings.Split(planInfo.DateValue, ",")
		// logger.GetSugaredLogger().Info(len(tmpSplitArray))
		if len(tmpSplitArray) == 2 {
			startDate := tmpSplitArray[0]
			startDate = strings.Replace(startDate, "-", "", -1)

			endDate := tmpSplitArray[1]
			endDate = strings.Replace(endDate, "-", "", -1)

			nowDate := time.Now().Format("20060102")

			// logger.GetSugaredLogger().Info(startDate)
			// logger.GetSugaredLogger().Info(endDate)
			// logger.GetSugaredLogger().Info("now date: " + nowDate)

			if utils.ConvertStringToInt(nowDate) < utils.ConvertStringToInt(startDate) || utils.ConvertStringToInt(nowDate) > utils.ConvertStringToInt(endDate) {
				// logger.GetSugaredLogger().Info("plan pos date failed, " + planInfo.PID)
				return false, models.ErrCodeDate, models.ReasonDate
			}
		}
	}
	// 时间限制
	if planInfo.TimeType == "1" {
		if len(planInfo.TimeList) == 0 {
			// logger.GetSugaredLogger().Info("plan pos time failed, " + planInfo.PID)
			return false, models.ErrCodeTime, models.ReasonTime
		}

		isTimeOK := false
		for _, item := range planInfo.TimeList {
			nowHour := time.Now().Format("15")
			startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[0], time.Local)
			endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", item.TimeValue[1], time.Local)

			if utils.ConvertStringToInt(nowHour) >= startTime.Hour() &&
				utils.ConvertStringToInt(nowHour) <= endTime.Hour() {
				isTimeOK = true
			}
		}
		if isTimeOK {
		} else {
			// logger.GetSugaredLogger().Info("plan pos time failed, " + planInfo.PID)
			return false, models.ErrCodeTime, models.ReasonTime
		}
	}

	// 人群包定向开, 并且rta开, 开启复投生效
	if planInfo.IsRepetition == 1 && planInfo.CrowdLibType == "1" && len(planInfo.CrowdLibID) > 0 && planInfo.IsRTA == 1 {
	} else {
		if reportCrowLib {
			// 上报人群包之前的统计请求数量
			go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeCrowdLibBefore, models.ReasonCrowdLibBefore)
		}

		return base.IsPlanPolicyCrowdLibOK(c, dspReq, planInfo, bigdataUID)
	}

	return true, models.ErrCodeSuccess, models.ReasonSuccess
}

func getDestPlanGroupInfoByPolicy(c context.Context, dspReq models.DspReqStu, planGroupInfo *models.DspPlanGroupStu, bigdataUID string) {
	var tmpDspPlanGroupPlanInfo []models.DspPlanGroupPlanStu
	for _, planGroupItem := range planGroupInfo.PlanGroupPlanInfo {

		planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planGroupItem.PlanID)
		if planInfo == nil {
			// logger.GetSugaredLogger().Errorf("getDestPlanGroupInfoByPolicy no plan info with plan_id: %v", planGroupItem.PlanID)
			continue
		}

		isPolicyOK, _, _ := isPlanPolicyOK(c, dspReq, planInfo, bigdataUID, false)
		if isPolicyOK {
		} else {
			continue
		}
		tmpDspPlanGroupPlanInfo = append(tmpDspPlanGroupPlanInfo, planGroupItem)
	}

	planGroupInfo.PlanGroupPlanInfo = tmpDspPlanGroupPlanInfo
}

func isInArray(target string, strArray []string) bool {
	for _, element := range strArray {
		if target == element {
			return true
		}
	}
	return false
}

// 针对sdk 3.0.25以下版本, 如果大于等于3.0.25返回true, 如果小于3.0.25返回false
func IsSDKVersionMoreThanOrEqual3025(c context.Context, sdkVersion string) bool {
	if len(sdkVersion) <= 0 {
		return false
	}
	// logger.GetSugaredLogger().Info(sdkVersion)
	tmpSDKVersionArray := strings.Split(sdkVersion, ".")

	tmpSDKVersion0 := 0
	tmpWhiteVersion0 := 3

	tmpSDKVersion1 := 0
	tmpWhiteVersion1 := 0

	tmpSDKVersion2 := 0
	tmpWhiteVersion2 := 25

	if len(tmpSDKVersionArray) > 0 {
		if IsNum(tmpSDKVersionArray[0]) {
			tmpSDKVersion0 = utils.ConvertStringToInt(tmpSDKVersionArray[0])
			if tmpSDKVersion0 < tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 1 {
		if IsNum(tmpSDKVersionArray[1]) {
			tmpSDKVersion1 = utils.ConvertStringToInt(tmpSDKVersionArray[1])
			if tmpSDKVersion1 < tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}
	if len(tmpSDKVersionArray) > 2 {
		if IsNum(tmpSDKVersionArray[2]) {
			tmpSDKVersion2 = utils.ConvertStringToInt(tmpSDKVersionArray[2])
			if tmpSDKVersion2 < tmpWhiteVersion2 && tmpSDKVersion1 == tmpWhiteVersion1 && tmpSDKVersion0 == tmpWhiteVersion0 {
				return false
			}
		}
	}

	return true
}

func IsNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

// IsManufactureXiaoMi 是否厂商小米类型
func IsManufactureXiaoMi(c context.Context, manufacturer string) bool {
	if len(manufacturer) == 0 {
		return false
	}
	xiaomiManufactureConfigArray := GetReplaceManufactureConfig(c, "ssp_manufacturer_packages_"+"xiaomi")
	if len(xiaomiManufactureConfigArray) > 0 {
		for _, manufactureConfigItem := range xiaomiManufactureConfigArray {
			if strings.ToLower(manufacturer) == manufactureConfigItem {
				return true
			}
		}
	}
	return false
}

type MiaoZhenCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"version"`
}

// error code
// 10000  正常返回
// 900301 dsp单日曝光限制
// 900302 dsp单日点击限制
// 900303 dsp单设备曝光限制
// 900304 dsp单设备点击限制
// 900305 dsp单日请求ip限制
// 900306 dsp地域限制
// 900307 dsp机型限制
// 900308 dsp安卓版本限制
// 900309 dsp预算限制
// 900310 dsp投放日期限制
// 900311 dsp投放时段限制
// 900312 dsp不在人群定向包
// 900313 dsp bid_cpm_floor不合适
// 900314 dsp单设备请求间隔限制
// 900315 dsp sdk限制
// 900316 数据优化类型未配置曝光点击链接
// 900317 超时
// 900318 dsp请求通过率限制
// 900399 dsp其他错误
