package api

import (
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"runtime"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// Test http://localhost:8081/api/test
func Test(c *gin.Context) {

	// logger.GetSugaredLogger().Info("runtime num cpu:", runtime.NumCPU())
	// logger.GetSugaredLogger().Info("runtime go max procs:", runtime.GOMAXPROCS(-1))

	cpuNum := utils.ConvertIntToString(runtime.NumCPU())
	goMaxProcsNum := utils.ConvertIntToString(runtime.GOMAXPROCS(-1))

	c.<PERSON>(200, gin.H{
		"message": "dsp-deal-core server OK" + ", runtime num cpu: " + cpuNum + ", runtime GOMAXPROCS: " + goMaxProcsNum,
	})
}

func CheckHologres3(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			logger.GetSugaredLogger().Error("CheckHologres3 panic: ", err)
		}
	}()

	db.InitHologres3()
	startTime := time.Now()
	var bigdataAdxExpItem models.BigdataExpStu
	bigdataAdxExpItem.UID = c.Query("uid")
	bigdataAdxExpItem.GroupID = c.Query("group_id")
	bigdataAdxExpItem.PlanID = c.Query("plan_id")
	bigdataAdxExpItem.MarketType = c.Query("market_type")
	bigdataAdxExpItem.AdsType = c.Query("ads_type")
	bigdataAdxExpItem.ExtDspChannel = c.Query("ext_dsp_channel")
	bigdataAdxExpItem.MediaChannel = "0"
	bigdataAdxExpItem.SubChannelID = ""
	bigdataAdxExpItem.ExtAdx = ""
	bigdataAdxExpItem.OS = c.Query("os")
	bigdataAdxExpItem.OSV = c.Query("osv")
	bigdataAdxExpItem.DIDMd5 = c.Query("did_md5")
	bigdataAdxExpItem.Imei = c.Query("imei")
	bigdataAdxExpItem.ImeiMd5 = c.Query("imei_md5")
	bigdataAdxExpItem.AndroidID = c.Query("android_id")
	bigdataAdxExpItem.AndroidIDMd5 = c.Query("android_id_md5")
	bigdataAdxExpItem.Idfa = c.Query("idfa")
	bigdataAdxExpItem.IdfaMd5 = c.Query("idfa_md5")
	bigdataAdxExpItem.IP = c.ClientIP()
	bigdataAdxExpItem.UA = c.GetHeader("User-Agent")
	bigdataAdxExpItem.Oaid = c.Query("oaid")
	bigdataAdxExpItem.OaidMd5 = c.Query("oaid_md5")
	bigdataAdxExpItem.Model = c.Query("model")
	bigdataAdxExpItem.Manufacturer = c.Query("manufacturer")
	bigdataAdxExpItem.CRID = c.Query("crid")
	bigdataAdxExpItem.CPMPrice = utils.ConvertStringToInt(c.Query("cpm_price"))
	bigdataAdxExpItem.ExtCPMPrice = utils.ConvertStringToInt(c.Query("ext_cpm_price"))
	bigdataAdxExpItem.Day = time.Now()
	bigdataAdxExpItem.Hour = time.Now().Format("15")
	bigdataAdxExpItem.Minute = time.Now().Format("04")
	bigdataAdxExpItem.Now = time.Now()
	bigdataAdxExpItem.IsOCPX = 1
	bigdataAdxExpItem.DealTime = utils.ConvertStringToInt64(c.Query("deal_time"))
	bigdataAdxExpItem.SupplyCRID = c.Query("supply_crid")
	bigdataAdxExpItem.DemandCRID = c.Query("demand_crid")

	// save to holo
	ret := bigDataHoloExp3(bigdataAdxExpItem)

	c.JSON(200, gin.H{
		"result": "success",
		"cost":   time.Since(startTime),
		"ret":    ret,
	})
}

func bigDataHoloExp3(bigdataAdxExpItem models.BigdataExpStu) int {
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema3("deal", "exp_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxExpItem.UID, len(bigdataAdxExpItem.UID))
	put.SetTextValByColName("group_id", bigdataAdxExpItem.GroupID, len(bigdataAdxExpItem.GroupID))
	put.SetTextValByColName("plan_id", bigdataAdxExpItem.PlanID, len(bigdataAdxExpItem.PlanID))
	put.SetTextValByColName("market_type", bigdataAdxExpItem.MarketType, len(bigdataAdxExpItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataAdxExpItem.AdsType, len(bigdataAdxExpItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataAdxExpItem.ExtDspChannel, len(bigdataAdxExpItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataAdxExpItem.MediaChannel, len(bigdataAdxExpItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataAdxExpItem.SubChannelID, len(bigdataAdxExpItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataAdxExpItem.ExtAdx, len(bigdataAdxExpItem.ExtAdx))
	put.SetTextValByColName("os", bigdataAdxExpItem.OS, len(bigdataAdxExpItem.OS))
	put.SetTextValByColName("osv", bigdataAdxExpItem.OSV, len(bigdataAdxExpItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxExpItem.DIDMd5, len(bigdataAdxExpItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxExpItem.Imei, len(bigdataAdxExpItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxExpItem.ImeiMd5, len(bigdataAdxExpItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxExpItem.AndroidID, len(bigdataAdxExpItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxExpItem.AndroidIDMd5, len(bigdataAdxExpItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxExpItem.Idfa, len(bigdataAdxExpItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxExpItem.IdfaMd5, len(bigdataAdxExpItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxExpItem.IP, len(bigdataAdxExpItem.IP))
	put.SetTextValByColName("ua", bigdataAdxExpItem.UA, len(bigdataAdxExpItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxExpItem.Oaid, len(bigdataAdxExpItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataAdxExpItem.OaidMd5, len(bigdataAdxExpItem.OaidMd5))
	put.SetTextValByColName("model", bigdataAdxExpItem.Model, len(bigdataAdxExpItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxExpItem.Manufacturer, len(bigdataAdxExpItem.Manufacturer))
	put.SetTextValByColName("crid", bigdataAdxExpItem.CRID, len(bigdataAdxExpItem.CRID))
	put.SetInt32ValByColName("cpm_price", int32(bigdataAdxExpItem.CPMPrice))
	put.SetInt32ValByColName("ext_cpm_price", int32(bigdataAdxExpItem.ExtCPMPrice))
	put.SetInt32ValByColName("is_ocpx", int32(bigdataAdxExpItem.IsOCPX))
	if bigdataAdxExpItem.DealTime > 0 {
		put.SetTimestamptzValByColName("deal_time", bigdataAdxExpItem.DealTime-946684800000000)
	}
	put.SetTextValByColName("supply_crid", bigdataAdxExpItem.SupplyCRID, len(bigdataAdxExpItem.SupplyCRID))
	put.SetTextValByColName("demand_crid", bigdataAdxExpItem.DemandCRID, len(bigdataAdxExpItem.DemandCRID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	holoclient.HoloClientLoggerOpen()
	defer holoclient.HoloClientLoggerClose()
	res := db.GlbHologresAdxDSPIgnoreDataDb3.Submit(put)
	return res
}
