package api

import (
	"dsp_core/core"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"math/rand"
	"net/url"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
)

// Exp ...
func Exp(c *gin.Context) {
	// logger.GetSugaredLogger().Info("exp")

	logQuery := c.Query("log")

	if len(logQuery) == 0 {
		logger.GetSugaredLogger().Error("exp nothing to do")
		return
	}
	logDecode, _ := utils.DecodeString(logQuery)
	log, _ := url.ParseQuery(string(logDecode))

	// xxxxxx
	// marketType := log.Get("market_type")
	adsType := log.Get("ads_type")
	extDspChannel := log.Get("ext_dsp_channel")

	isOCPX := 0
	planID := log.Get("plan_id")
	// logger.GetSugaredLogger().Infof("exp start plan_id=%v, adsType=%v, extDspChannel=%v", planID, adsType, extDspChannel)
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo != nil {
		// logger.GetSugaredLogger().Infof("exp start plan_id=%v, IsOCPX=%v, IsExpToOCPX=%v", planID, planInfo.IsOCPX, planInfo.IsExpToOCPX)

		if planInfo.IsOCPX == 1 && planInfo.IsExpToOCPX == 1 {
			randNum := rand.Intn(100)
			if randNum < planInfo.ExpToOCPXWeight {
				isOCPX = 1
				if adsType == "1" && extDspChannel == "0" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("dhh exp panic: %v", err)
							}
						}()
						core.DhhClkReport(c, log)
					}()

				} else if adsType == "1" && extDspChannel == "3" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("tencent exp panic: %v", err)
							}
						}()
						core.TencentSSPClk(c, log)
					}()
				} else if adsType == "1" && extDspChannel == "5" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("amap exp panic: %v", err)
							}
						}()
						core.AMapClkReport(c, log)
					}()
				} else if adsType == "1" && extDspChannel == "8" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("kuaishou exp panic: %v", err)
							}
						}()
						core.KuaiShouClkReport(c, log, "exp")
						// core.KuaiShouOCPXClkReport(c, log)
					}()
				} else if adsType == "1" && extDspChannel == "9" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("uc exp panic: %v", err)
							}
						}()
						core.UCClkReport(c, log)
					}()
				} else if adsType == "1" && extDspChannel == "12" {
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("weibo exp panic: %v", err)
							}
						}()
						core.WeiBoClkReport(c, log, "exp")
					}()
				} else if adsType == "1" && extDspChannel == "13" {
					// 支付宝OCPX曝光上报
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("alipay exp panic: %v", err)
							}
						}()
						core.AlipayReport(c, log, "expose")
					}()
				} else if adsType == "1" && extDspChannel == "14" {
					// 京东科技OCPX曝光上报
					go func() {
						defer func() {
							if err := recover(); err != nil {
								logger.GetSugaredLogger().Errorf("jd exp panic: %v", err)
							}
						}()
						core.JDReport(c, log, "expose")
					}()
				}
			}
		}
	}

	if adsType == "1" && extDspChannel == "0" {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logger.GetSugaredLogger().Errorf("dhh exp panic: %v", err)
				}
			}()
			core.DhhExpReport(c, log)
		}()

	} else if adsType == "1" && extDspChannel == "3" {
		// go func() {
		// 	defer func() {
		// 		if err := recover(); err != nil {
		// 			logger.GetSugaredLogger().Info("tencent exp panic:", err)
		// 		}
		// 	}()
		// 	core.TencentExp(c, log)
		// }()
	}

	// 处理自营策略
	ExpPolicy(c, log)

	// 上报大数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.GetSugaredLogger().Errorf("bigdata exp panic: %v", err)
			}
		}()
		models.BigDataExp(c, log, isOCPX)
	}()

	resp := map[string]interface{}{}
	resp["ret"] = 0
	resp["msg"] = "ok exp"

	// for debug
	if c.Query("mh_debug") == "1" {
		debugInfoMap := map[string]interface{}{}
		debugInfoMap["reqid"] = log.Get("uid")
		debugInfoMap["plan_id"] = log.Get("plan_id")
		debugInfoMap["os"] = log.Get("os")
		debugInfoMap["osv"] = log.Get("osv")
		debugInfoMap["did_md5"] = log.Get("did_md5")
		debugInfoMap["imei"] = log.Get("imei")
		debugInfoMap["imei_md5"] = log.Get("imei_md5")
		debugInfoMap["android_id"] = log.Get("android_id")
		debugInfoMap["android_id_md5"] = log.Get("android_id_md5")
		debugInfoMap["idfa"] = log.Get("idfa")
		debugInfoMap["idfa_md5"] = log.Get("idfa_md5")
		debugInfoMap["ip"] = c.ClientIP()
		debugInfoMap["ua"] = c.GetHeader("User-Agent")
		debugInfoMap["oaid"] = log.Get("oaid")
		debugInfoMap["oaid_md5"] = log.Get("oaid_md5")
		debugInfoMap["model"] = log.Get("model")
		debugInfoMap["manufacturer"] = log.Get("manufacturer")
		debugInfoMap["cpm_price"] = utils.ConvertStringToInt(log.Get("cpm_price"))
		debugInfoMap["ext_cpm_price"] = utils.ConvertStringToInt(log.Get("ext_cpm_price"))

		resp["debug"] = debugInfoMap
	}
	c.PureJSON(200, resp)
}

func ExpPolicy(c *gin.Context, log url.Values) {

	// plan_id
	planID := log.Get("plan_id")
	planInfo := models.GetPlanInfoFromMysqlByPlanID(c, planID)
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("ExpPolicy plan_id: %v not found\n", planID)
		return
	}

	// os
	// ip := c.ClientIP()
	tmpDid := log.Get("did_md5")
	if len(planID) == 0 || len(tmpDid) == 0 {
		return
	}

	t, _ := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02")+" 23:59:59", time.Local)
	// nextUnixTime := t.Unix() + 2
	// logger.GetSugaredLogger().Info("next unix time: ", nextUnixTime)
	leftUnixTime := t.Unix() + 2 - time.Now().Unix()
	// logger.GetSugaredLogger().Info("left unix time: ", leftUnixTime)

	// ttl 0:00 - 3:00
	leftUnixTime = leftUnixTime + rand.Int63n(3600*3)

	if planInfo.MaxExpType == "1" {
		// 单日曝光限制
		tmpRedisKey := "max_exp_total_" + time.Now().Format("2006-01-02") + "_" + planID
		// logger.GetSugaredLogger().Info("redis key: ", tmpRedisKey)
		// redisValue, redisErr := db.GetRedis().Get(c, tmpRedisKey).Result()
		// if redisErr != nil {
		// 	// logger.GetSugaredLogger().Info("redis error:", redisErr)
		// 	redisErr = db.GetRedis().Set(c, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second).Err()
		// } else {
		// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
		// 	redisErr = db.GetRedis().Set(c, tmpRedisKey, 1+utils.ConvertStringToInt(redisValue), time.Duration(leftUnixTime)*time.Second).Err()
		// }
		redisValue, redisErr := db.GetRedis().Incr(c, utils.Timeout50mill, tmpRedisKey)
		if redisErr != nil {
			logger.GetSugaredLogger().Error("redis incr error:", tmpRedisKey, redisErr, redisValue)
		} else {
			// logger.GetSugaredLogger().Info("redis incr value:", redisDAUKey, redisErr, redisDAUValue)
			db.GetRedis().Expire(c, utils.Timeout50mill, tmpRedisKey, time.Duration(leftUnixTime)*time.Second)
		}
	}

	if planInfo.MaxExpDeviceType == "1" {
		// 单日设备曝光限制
		tmpRedisKey := "limit_device_exp_" + time.Now().Format("2006-01-02") + "_" + planID + "_" + tmpDid
		// redisValue, redisErr = db.GetRedis().Get(c, tmpRedisKey).Result()
		// if redisErr != nil {
		// 	// logger.GetSugaredLogger().Info("redis error:", redisErr)
		// 	redisErr = db.GetRedis().Set(c, tmpRedisKey, 1, time.Duration(leftUnixTime)*time.Second).Err()
		// } else {
		// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
		// 	redisErr = db.GetRedis().Set(c, tmpRedisKey, 1+utils.ConvertStringToInt(redisValue), time.Duration(leftUnixTime)*time.Second).Err()
		// }
		// logger.GetSugaredLogger().Info("debug_limit_device_exp:", tmpRedisKey)

		redisValue, redisErr := db.GetRedis().Incr(c, utils.Timeout50mill, tmpRedisKey)
		if redisErr != nil {
			logger.GetSugaredLogger().Info("redis incr error:", tmpRedisKey, redisErr, redisValue)
		} else {
			// logger.GetSugaredLogger().Info("redis incr value:", redisDAUKey, redisErr, redisDAUValue)
			db.GetRedis().Expire(c, utils.Timeout50mill, tmpRedisKey, time.Duration(leftUnixTime)*time.Second)
		}
	}
}
