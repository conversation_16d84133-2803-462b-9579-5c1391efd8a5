package main

import (
	"context"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/router"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	adCommonCore "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/core"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/robfig/cron/v3"
	_ "go.uber.org/automaxprocs"
)

func crontabReadConfig() *cron.Cron {
	c := cron.New(cron.WithParser(cron.NewParser(
		cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor,
	)))

	// 每30s执行一次
	c.AddFunc("*/30 * * * * ?", models.CrontabReadConfig)
	c.Start()
	return c
}

func main() {

	// // Disable Console Color, you don't need console color when writing the logs to file.
	// gin.DisableConsoleColor()
	// // Force log's color
	// // gin.ForceConsoleColor()
	// // Logging to a file.
	// f, _ := os.Create(config.LogPath + "gin_" + time.Now().Format("20060102") + ".log")
	// gin.DefaultWriter = io.MultiWriter(f)

	// // Use the following code if you need to write the logs to file and console at the same time.
	// gin.DefaultWriter = io.MultiWriter(f, os.Stdout)

	// router.Run(":8081")

	// 初始化日志系统
	logger.InitLoggerFromEnv()

	// 替换标准库log
	logger.ReplaceStdLog()

	// 获取zap日志实例
	log := logger.GetSugaredLogger()

	// 确保程序退出前同步日志
	defer logger.Sync()

	// 初始化资源
	if err := initResources(); err != nil {
		log.Errorf("Failed to initialize resources: %v\n", err)
		return
	}

	// crontab
	models.CrontabReadConfig()
	cronJob := crontabReadConfig()

	// pprof 服务
	pprofServer := &http.Server{Addr: ":6060"}
	go func() {
		if err := pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("failed to start pprof server: %s\n", err)
		}
	}()

	// 初始化路由
	r := router.Init()

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 启动HTTP服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("failed to listen: %s\n", err)
		}
	}()

	// 等待中断信号
	// 创建用于接收系统信号的通道
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("starting to shutdown servers...")

	// 设置5秒超时时间关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 25*time.Second)
	defer cancel()

	// 关闭pprof服务器
	if err := pprofServer.Shutdown(ctx); err != nil {
		log.Errorf("pprof server shutdown error: %v\n", err)
	}

	// 关闭HTTP服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Errorf("server forced to shutdown:%v", err)
	}

	// 停止定时任务
	cronCtx := cronJob.Stop()
	select {
	case <-cronCtx.Done():
		log.Info("all cron jobs completed")
	case <-time.After(10 * time.Second):
		log.Info("timed out waiting for cron jobs to complete")
	}

	// 关闭数据库连接
	db.CloseMysql()
	db.CloseRedis()
	db.CloseClickHouse()
	db.CloseBigCache()
	db.CloseHologres()

	// 释放HTTP客户端资源
	adCommonCore.CloseHTTPClient()
	adCommonCore.CloseFastHTTPClient()

	log.Info("server shutdown completed")
}

// 初始化资源
func initResources() error {
	var wg sync.WaitGroup
	errChan := make(chan error, 5) // 用于接收初始化错误

	// 初始化redis
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitRedis(); err != nil {
			errChan <- fmt.Errorf("failed to init redis: %w", err)
		}
	}()

	// 初始化mysql
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitMysql(); err != nil {
			errChan <- fmt.Errorf("failed to init mysql: %w", err)
		}
	}()

	// 初始化clickhouse
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitClickHouse(); err != nil {
			errChan <- fmt.Errorf("failed to init clickhouse: %w", err)
		}
	}()

	// 初始化bigcache
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitBigCache(); err != nil {
			errChan <- fmt.Errorf("failed to init bigcache: %w", err)
		}
	}()

	// 初始化hologres
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := db.InitHologres(); err != nil {
			errChan <- fmt.Errorf("failed to init hologres: %w", err)
		}
	}()

	// 等待所有初始化完成
	wg.Wait()
	close(errChan)

	// 检查是否有初始化错误
	var initErr error
	for err := range errChan {
		if err != nil {
			initErr = err
			break
		}
	}

	// 如果有错误，清理已初始化的资源
	if initErr != nil {
		// 清理资源
		db.CloseMysql()
		db.CloseRedis()
		db.CloseClickHouse()
		db.CloseBigCache()
		db.CloseHologres()
		return initErr
	}

	return nil
}
