package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"hash"
	"log"
	"net"
	"strconv"
	"strings"
	"time"
)

// GetMd5 ...
func GetMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// Get16Md5 ...
func Get16Md5(str string) string {
	return GetMd5(str)[8:24]
}

// GetSha1 ...
func GetSha1(str string) string {
	sha1 := sha1.New()
	sha1.Write([]byte(str))
	return hex.EncodeToString(sha1.Sum([]byte("")))

}

// ConvertStringToInt ...
func ConvertStringToInt(str string) int {
	value, _ := strconv.Atoi(str)
	return value
}

// ConvertStringToInt64 ...
func ConvertStringToInt64(str string) int64 {
	value, _ := strconv.ParseInt(str, 10, 64)
	return value
}

// ConvertIntToString ...
func ConvertIntToString(value int) string {
	return strconv.Itoa(value)
}

// ConvertInt64ToString ...
func ConvertInt64ToString(value int64) string {
	return strconv.FormatInt(value, 10)
}

// ConvertStringToFloat ...
func ConvertStringToFloat(str string) float64 {
	value, _ := strconv.ParseFloat(str, 32)
	return value
}

func checkIP(ipStr string) bool {
	address := net.ParseIP(ipStr)
	if address == nil {
		log.Println("checkIP wrong ip: ", ipStr)
		return false
	}
	// log.Println("right ip: ", address.String())
	return true
}

// ip to int64
func inetAton(ipStr string) int64 {
	bits := strings.Split(ipStr, ".")

	b0, _ := strconv.Atoi(bits[0])
	b1, _ := strconv.Atoi(bits[1])
	b2, _ := strconv.Atoi(bits[2])
	b3, _ := strconv.Atoi(bits[3])

	var sum int64

	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

// IsInnerIP ...
func IsInnerIP(ipStr string) bool {
	if !checkIP(ipStr) {
		return false
	}
	inputIPNum := inetAton(ipStr)
	innerIPA := inetAton("**************")
	innerIPB := inetAton("**************")
	innerIPC := inetAton("***************")
	innerIPD := inetAton("**************")
	innerIPF := inetAton("***************")

	return inputIPNum>>24 == innerIPA>>24 || inputIPNum>>20 == innerIPB>>20 ||
		inputIPNum>>16 == innerIPC>>16 || inputIPNum>>22 == innerIPD>>22 ||
		inputIPNum>>24 == innerIPF>>24
}

// IsImei ...
func IsImei(imei string) bool {
	// meid
	if len(imei) == 14 {
		return true
	}
	// imei
	if len(imei) != 15 {
		return false
	}

	prefix := imei[0 : len(imei)-1]
	// log.Println(prefix)
	lastDig := imei[len(imei)-1:]
	// log.Println(lastDig)

	var total, sum1, sum2 int
	n := len(prefix)
	for i := 0; i < n; i++ {
		num, _ := strconv.Atoi(string(prefix[i]))
		// 奇数
		if i%2 == 0 {
			sum1 += num
		} else { // 偶数
			tmp := num * 2
			if tmp < 10 {
				sum2 += tmp
			} else {
				sum2 = sum2 + tmp + 1 - 10
			}
		}
	}
	total = sum1 + sum2
	if total%10 == 0 {
		if ConvertStringToInt(lastDig) == 0 {
			return true
		}
	} else {
		if ConvertStringToInt(lastDig) == 10-(total%10) {
			return true
		}
	}
	return false
}

// GetCurrentMilliSecond ...
func GetCurrentMilliSecond() int64 {
	return time.Now().UnixNano() / 1e6
}

// GetCurrentSecond ...
func GetCurrentSecond() int64 {
	return time.Now().Unix()
}

// GetCurrentHour ...
func GetCurrentHour() int {
	return time.Now().Hour()
}

// const ...
const (
	PayloadSize    = 8
	InitVectorSize = 16
	SignatureSize  = 4
	// EKey           = "dc31f9c99fd64b724ad58628f1cbcdc17571ddcd"
	// IKey           = "48fdef1455de9f986dd280ce1fd910b9e4f7b13c"
	UTF8 = "utf-8"
)

// AddBase64Padding ...
func AddBase64Padding(paramBase64 string) string {
	if i := len(paramBase64) % 4; i != 0 {
		paramBase64 += strings.Repeat("=", 4-i)
	}
	return paramBase64
}

// CreateHmac ...
func CreateHmac(key, mode string, isBase64 bool) (hash.Hash, error) {
	var b64DecodedKey, k []byte
	var err error
	if isBase64 {
		b64DecodedKey, err = base64.URLEncoding.DecodeString(AddBase64Padding(key))
		if err == nil {
			key = string(b64DecodedKey[:])
		}
	}
	if mode == UTF8 {
		k = []byte(key)
	} else {
		k, err = hex.DecodeString(key)
	}
	if err != nil {
		return nil, err
	}
	return hmac.New(sha1.New, k), nil
}

// HmacSum ...
func HmacSum(hmacParam hash.Hash, buf []byte) []byte {
	hmacParam.Reset()
	hmacParam.Write(buf)
	return hmacParam.Sum(nil)
}

// GenerateHmac ...
func GenerateHmac(key string, buf []byte) ([]byte, error) {
	hmacParam, err := CreateHmac(key, UTF8, false)
	if err != nil {
		err = fmt.Errorf("jzt/encrypt: create hmac error, %s", err.Error())
		return nil, err
	}
	return HmacSum(hmacParam, buf), nil
}

// EncryptPrice ...
func EncryptPrice(price float64, eKey string, iKey string) (string, error) {
	var (
		iv         = make([]byte, InitVectorSize)
		encoded    = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	sum := md5.Sum([]byte(""))
	copy(iv[:], sum[:])

	// pad = hmac(e_key, iv)  // first 8 bytes
	pad, err := GenerateHmac(eKey, iv[:])
	if err != nil {
		return "", err
	}
	pad = pad[:PayloadSize]

	bits := uint64(price)
	binary.BigEndian.PutUint64(priceBytes, bits)
	// enc_price = pad <xor> price
	for i := range priceBytes {
		encoded[i] = pad[i] ^ priceBytes[i]
	}

	// signature = hmac(i_key, data || iv), first 4 bytes
	sig, err := GenerateHmac(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return "", err
	}
	signature = sig[:SignatureSize]

	// final_message = WebSafeBase64Encode( iv || enc_price || signature )
	finalMessage := strings.TrimRight(
		base64.URLEncoding.EncodeToString(append(append(iv[:], encoded[:]...), signature[:]...)),
		"=")
	return finalMessage, nil
}

// DecryptPrice ...
func DecryptPrice(finalMessage string, eKey string, iKey string) (float64, error) {
	var err error
	var errPrice float64
	encryptedPrice := AddBase64Padding(finalMessage)
	decoded, err := base64.URLEncoding.DecodeString(encryptedPrice)
	if err != nil {
		return errPrice, err
	}
	var (
		iv         = make([]byte, InitVectorSize)
		p          = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], decoded[0:16])
	copy(p[:], decoded[16:24])
	copy(signature[:], decoded[24:28])

	pad, err := GenerateHmac(eKey, iv[:])
	if err != nil {
		return errPrice, err
	}
	pad = pad[:PayloadSize]
	for i := range p {
		priceBytes[i] = pad[i] ^ p[i]
	}

	sig, err := GenerateHmac(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return errPrice, err
	}
	sig = sig[:SignatureSize]
	for i := range sig {
		if sig[i] != signature[i] {
			return errPrice, fmt.Errorf("jzt/decrypt: Failed to decrypt, got:%s ,expect:%s", string(sig), string(signature))
		}
	}
	// price := math.Float64frombits(binary.BigEndian.Uint64(priceBytes))
	price := float64(binary.BigEndian.Uint64(priceBytes[:]))

	return price, nil
}

func Generate2345Hmac(key string, buf []byte) []byte {

	h := hmac.New(sha1.New, []byte(key))
	h.Write(buf)
	signature := hex.EncodeToString(h.Sum(nil))
	return []byte(signature)

}

// EncryptPrice ...
func Encrypt2345Price(price float64, eKey string, iKey string) (string, error) {
	var (
		iv         = make([]byte, InitVectorSize)
		encoded    = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], []byte("33fef3252c60a105"))
	pad := Generate2345Hmac(eKey, iv[:])
	pad = pad[:PayloadSize]

	priceString := strconv.FormatFloat(price, 'f', 0, 64)
	priceString = fmt.Sprintf("%-8s", priceString)
	copy(priceBytes[:], []byte(priceString))
	for i := range priceBytes {
		encoded[i] = pad[i] ^ priceBytes[i]
	}

	sig := Generate2345Hmac(iKey, append(priceBytes[:], iv[:]...))
	signature = sig[:SignatureSize]

	finalMessage := base64.URLEncoding.EncodeToString(append(append(iv[:], encoded[:]...), signature[:]...))
	return finalMessage, nil
}

// DecryptPrice ...
func Decrypt2345Price(finalMessage string, eKey string, iKey string) (float64, error) {
	var err error
	var errPrice float64
	encryptedPrice := AddBase64Padding(finalMessage)
	decoded, err := base64.URLEncoding.DecodeString(encryptedPrice)
	if err != nil {
		return errPrice, err
	}

	var (
		iv         = make([]byte, InitVectorSize)
		p          = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	copy(iv[:], decoded[0:16])
	copy(p[:], decoded[16:24])
	copy(signature[:], decoded[24:28])

	pad := Generate2345Hmac(eKey, iv[:])
	pad = pad[:PayloadSize]
	for i := range p {
		priceBytes[i] = p[i] ^ pad[i]
	}

	sig := Generate2345Hmac(iKey, append(priceBytes[:], iv[:]...))

	sig = sig[:SignatureSize]

	for i := range sig {
		if sig[i] != signature[i] {
			return errPrice, fmt.Errorf("jzt/decrypt: Failed to decrypt, got:%s ,expect:%s", string(sig), string(signature))
		}
	}
	price := ConvertStringToFloat(strings.Trim(string(priceBytes[:]), " "))
	return price, nil
}

// PKCS7Padding 填充模式
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	//Repeat()函数的功能是把切片[]byte{byte(padding)}复制padding个，然后合并成新的字节切片返回
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS7UnPadding 填充的反向操作，删除填充字符串
func PKCS7UnPadding(origData []byte) ([]byte, error) {
	//获取数据长度
	length := len(origData)
	if length == 0 {
		return nil, errors.New("加密字符串错误！")
	} else {
		//获取填充字符串长度
		unpadding := int(origData[length-1])
		//截取切片，删除填充字节，并且返回明文
		return origData[:(length - unpadding)], nil
	}
}

func AesCBCPKCS5Decrypt(crypted, key []byte) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		log.Println("err is:", err)
	}
	blockMode := NewECBDecrypter(block)
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS5UnPadding(origData)
	log.Println("source is :", origData, string(origData))
	return origData
}

func AesCBCPKCS5Encrypt(src, key string) []byte {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		log.Println("key error1", err)
	}
	if src == "" {
		log.Println("plain content empty")
	}
	ecb := NewECBEncrypter(block)
	content := []byte(src)
	content = PKCS5Padding(content, block.BlockSize())
	crypted := make([]byte, len(content))
	ecb.CryptBlocks(crypted, content)
	// 普通base64编码加密 区别于urlsafe base64
	log.Println("base64 result:", base64.StdEncoding.EncodeToString(crypted))

	log.Println("base64UrlSafe result:", Base64UrlSafeEncode(crypted))
	return crypted
}

func Base64URLDecode(data string) ([]byte, error) {
	var missing = (4 - len(data)%4) % 4
	data += strings.Repeat("=", missing)
	res, err := base64.URLEncoding.DecodeString(data)
	log.Println("  decodebase64urlsafe is :", string(res), err)
	return base64.URLEncoding.DecodeString(data)
}

func Base64UrlSafeEncode(source []byte) string {
	// Base64 Url Safe is the same as Base64 but does not contain '/' and '+' (replaced by '_' and '-') and trailing '=' are removed.
	bytearr := base64.StdEncoding.EncodeToString(source)
	safeurl := strings.Replace(string(bytearr), "/", "_", -1)
	safeurl = strings.Replace(safeurl, "+", "-", -1)
	safeurl = strings.Replace(safeurl, "=", "", -1)
	return safeurl
}

type ecb struct {
	b         cipher.Block
	blockSize int
}

func newECB(b cipher.Block) *ecb {
	return &ecb{
		b:         b,
		blockSize: b.BlockSize(),
	}
}

type ecbEncrypter ecb

// NewECBEncrypter returns a BlockMode which encrypts in electronic code book
// mode, using the given Block.
func NewECBEncrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbEncrypter)(newECB(b))
}
func (x *ecbEncrypter) BlockSize() int { return x.blockSize }
func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

type ecbDecrypter ecb

// NewECBDecrypter returns a BlockMode which decrypts in electronic code book
// mode, using the given Block.
func NewECBDecrypter(b cipher.Block) cipher.BlockMode {
	return (*ecbDecrypter)(newECB(b))
}
func (x *ecbDecrypter) BlockSize() int { return x.blockSize }
func (x *ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(src)%x.blockSize != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	if len(dst) < len(src) {
		panic("crypto/cipher: output smaller than input")
	}
	for len(src) > 0 {
		x.b.Decrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	// 去掉最后一个字节 unpadding 次
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// AesCBCEncrypt 实现加密
func AesCBCEncrypt(origData []byte, key []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//获取块的大小
	blockSize := block.BlockSize()
	//对数据进行填充，让数据长度满足需求
	origData = PKCS7Padding(origData, blockSize)
	//采用AES加密方法中CBC加密模式
	blocMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypted := make([]byte, len(origData))
	//执行加密
	blocMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

// AesCBCDecrypt 实现解密
func AesCBCDecrypt(cypted []byte, key []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//获取块大小
	blockSize := block.BlockSize()
	//创建加密客户端实例
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	origData := make([]byte, len(cypted))
	//这个函数也可以用来解密
	blockMode.CryptBlocks(origData, cypted)
	//去除填充字符串
	origData, err = PKCS7UnPadding(origData)
	if err != nil {
		return nil, err
	}
	return origData, err
}

// EncodeString 加密base64
func EncodeString(pwd []byte) (string, error) {
	result, err := AesCBCEncrypt(pwd, []byte("1234567887654321"))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(result), err
}

// DecodeString 解密
func DecodeString(pwd string) ([]byte, error) {
	//解密base64字符串
	pwdByte, err := base64.StdEncoding.DecodeString(pwd)
	if err != nil {
		return nil, err
	}
	//执行AES解密
	return AesCBCDecrypt(pwdByte, []byte("1234567887654321"))
}

// AesECBDecrypt ...
func AesECBDecrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	origData, err := PKCS7UnPadding(decrypted)
	if err != nil {
		return nil
	}
	return origData
}

// AesECBEncrypt ...
func AesECBEncrypt(data, key []byte) []byte {
	block, _ := aes.NewCipher(key)
	data = PKCS7Padding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return decrypted
}

// AesCBCDecryptWithIV 实现解密WithIV
func AesCBCDecryptWithIV(cypted []byte, key []byte, iv []byte) ([]byte, error) {
	//创建加密算法实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//创建加密客户端实例
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(cypted))
	//这个函数也可以用来解密
	blockMode.CryptBlocks(origData, cypted)
	//去除填充字符串
	origData, err = PKCS7UnPadding(origData)
	if err != nil {
		return nil, err
	}
	return origData, err
}

func InterceptStringWith3Dots(resStr string, strLen int) string {
	result := resStr
	r := []rune(resStr)
	if len(r) >= strLen {
		result = string(r[:strLen]) + "..."
	}
	return result
}

func IsNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

func RemoveDuplicates(nums []string) []string {
	result := make([]string, 0) // 创建新的切片来保存结果
	seen := map[string]bool{}   // 创建一个空的map来记录已经遇到过的数字

	for _, num := range nums {
		if !seen[num] { // 如果该数字还没有被添加进result或者seen中
			result = append(result, num) // 将其添加到result中
			seen[num] = true             // 标记这个数字为已经处理过了
		}
	}

	return result
}
