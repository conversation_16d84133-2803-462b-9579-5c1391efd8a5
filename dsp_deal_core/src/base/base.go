package base

import (
	"context"
	"dsp_core/db"
	"dsp_core/models"
	"dsp_core/utils"
	"math/rand"
)

// 是否人群包限制OK
func IsPlanPolicyCrowdLibOK(c context.Context, dspReq models.DspReqStu, planInfo *models.DspPlanStu, bigdataUID string) (bool, int, string) {
	if planInfo.CrowdLibType == "1" {
		if len(planInfo.CrowdLibID) > 0 {
			if len(dspReq.Device.DIDMd5Key) < 2 {
				// logger.GetSugaredLogger().Info("no in crowd lib with wrong crowd_lib_id:", planInfo.CrowdLibID, dspReq.Device.DIDMd5Key)
				return false, models.ErrCodeCrowdLib, models.ReasonCrowdLib
			}

			redisKey := "dmp_crowd_" + utils.Get16Md5(planInfo.CrowdLibID) + "_" + dspReq.Device.DIDMd5Key
			_, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, redisKey)

			// Handle Redis timeout error with 50% probability
			if redisErr == context.DeadlineExceeded && rand.Intn(2) == 0 {
				// logger.GetSugaredLogger().Errorf("isPlanPolicyCrowdLibOK redis timeout error api, crowdlibId=%v, didmd5key=%v", planInfo.CrowdLibID, dspReq.Device.DIDMd5Key)
				return true, models.ErrCodeSuccess, models.ReasonSuccess
			}

			// 定向人群包 - 排除
			if planInfo.CrowdLibUseType == "1" {
				if redisErr != nil {
					// logger.GetSugaredLogger().Error("no in crowd lib:", planInfo.CrowdLibID, dspReq.Device.DIDMd5Key)
					// go models.BigDataAdxReq(c, dspReq, planInfo, bigdataUID, "", models.ErrCodeExcludeCrowdLibPass, models.ReasonCrowdLib)
				} else {
					// logger.GetSugaredLogger().Info("no in crowd lib:", planInfo.CrowdLibID, dspReq.Device.DIDMd5Key)
					return false, models.ErrCodeCrowdLib, models.ReasonExcludeCrowdLib
				}
			} else {
				// 定向人群包 - 定向
				if redisErr != nil {
					// logger.GetSugaredLogger().Error("no in crowd lib:", planInfo.CrowdLibID, dspReq.Device.DIDMd5Key)
					return false, models.ErrCodeCrowdLib, models.ReasonCrowdLib
				}
			}

		}
	}

	return true, models.ErrCodeSuccess, models.ReasonSuccess
}
