package models

import (
	"context"
	"dsp_core/db"
	"dsp_core/utils"
	"encoding/json"
	"log"
	"time"

	monitor "codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/monitor/api"
	"github.com/gin-gonic/gin"
)

// ===============================================================================================================================================
// GetAllPlanInfoFromCacheServer ...
// func GetAllPlanInfoFromCacheServer(c *gin.Context) *[]DspPlanStu {

// 	cacheKey := "go_dsp_all_plan"

// 	var dspPlanListArray []DspPlanStu

// 	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
// 	if cacheError != nil {
// 		// log.Println("cache error:", cacheKey, cacheError)
// 	} else {
// 		// log.Println("cache value:", cacheValue)
// 		if len(cacheValue) == 0 {
// 			return nil
// 		}
// 		json.Unmarshal([]byte(cacheValue), &dspPlanListArray)

// 		return &dspPlanListArray
// 	}

// 	return nil
// }

// ===============================================================================================================================================
// GetPlanInfoFromMysqlByPlanID ...
func GetPlanInfoFromMysqlByPlanID(c context.Context, planID string) *DspPlanStu {

	cacheKey := "go_dsp_plan_" + planID

	beginTime := time.Now()
	defer func() {
		monitor.ReportFunctionMetrics("dsp_deal_core", "GetPlanInfoFromMysqlByPlanID", float64(time.Since(beginTime).Milliseconds()))
	}()

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var dspPlanData DspPlanStu

	if cacheError != nil {
		// log.Println("cache error:", cacheKey, cacheError)
	} else {
		// log.Println("cache value:", cacheValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &dspPlanData)

		return &dspPlanData
	}

	return nil
}

// ===============================================================================================================================================
// GetCpaPlanInfoByPlanIDFromRedis ...
func GetCpaPlanInfoByPlanIDFromRedis(c *gin.Context, cacheKey string) *DspPlanStu {

	var dspPlanData DspPlanStu

	redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, cacheKey)
	if redisErr != nil {
		// log.Println("redis error:", redisErr)
	} else {
		// log.Println("redis value:", redisValue)
		db.GlbBigCache.Set(cacheKey, []byte(redisValue))

		if len(redisValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(redisValue), &dspPlanData)

		return &dspPlanData
	}

	log.Println("-----------------------------------", cacheKey)
	return nil
}

// ===============================================================================================================================================
// GetPlanGroupInfo ...
func GetPlanGroupInfo(c context.Context, planGroupID string) *DspPlanGroupStu {
	start := time.Now()
	defer func() {
		monitor.ReportFunctionMetrics("dsp_deal_core", "GetPlanGroupInfo", float64(time.Since(start).Milliseconds()))
	}()

	cacheKey := "go_dsp_plan_group_" + planGroupID

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var planGroupInfo DspPlanGroupStu

	if cacheError != nil {
		// log.Println("cache error:", cacheKey, cacheError)
	} else {
		// log.Println("cache value:", cacheValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &planGroupInfo)

		return &planGroupInfo
	}

	return nil
}

// ===============================================================================================================================================
// CacheServerPlansRespStu ...
type CacheServerPlansRespStu struct {
	Code int          `json:"code"`
	Data []DspPlanStu `json:"data"`
}

// CacheServerPlanRespStu ...
type CacheServerPlanRespStu struct {
	Code int        `json:"code"`
	Data DspPlanStu `json:"data"`
}

// market_type
// ads_type
// ext_dsp_channel
// media_channel: 0 ssp, 1 三方ADX, 2 商店, 3 ssp数据优化, 4 KOL
// sub_channel_id: kol_star_id
// ext_adx: ks 0, oceanengine 1, douyu 2
// unit_price_type: 0 CPM, 1 CPC, 2 CPA

type DspPlanStu struct {
	GID                string `json:"gid"`
	GroupMarketingType string `json:"group_marketing_type"`
	GroupAdsType       string `json:"group_ads_type"`
	GroupExtDspChannel string `json:"group_ext_dsp_channel"`
	CreativeGroupID    string `json:"creative_group_id"`
	Status             int    `json:"status"`
	PID                string `json:"pid"`
	PName              string `json:"name"`
	OS                 string `json:"os"`

	// 推广类型
	PromotionType int `json:"promotion_type"`
	// 是否SDK过滤已安装
	IsFilterPackageNames int `json:"is_filter_package_names"`
	// SDK过滤已安装
	FilterPackageNames string `json:"filter_package_names"`
	// 是否SDK定向已安装
	IsDirectInstalledPackageNames int `json:"is_direct_installed_package_names"`
	// SDK定向已安装
	DirectInstalledPackageNames string `json:"direct_installed_package_names"`
	// SDK定向已安装
	AppInfo string `json:"app_info"`
	// 是否复投, 此功能必须开人群包定向. 0: 人群包和rta必须都ok; 1: 人群包或者rta有1个ok就行
	IsRepetition int `json:"is_repetition"`

	DownloadLink                   string                               `json:"download_link"`
	H5Link                         string                               `json:"h5_link"`
	DeepLink                       string                               `json:"dp_link"`
	StoreDeepLink                  string                               `json:"store_dp_link"`
	QuickAppLink                   string                               `json:"quick_app_link"`
	AppName                        string                               `json:"app_name"`
	PackageName                    string                               `json:"package_name"`
	PackageSize                    string                               `json:"package_size"`
	Publisher                      string                               `json:"publisher"`
	AppVersion                     string                               `json:"app_version"`
	AppVersionCode                 string                               `json:"app_version_code"`
	PrivacyLink                    string                               `json:"privacy_link"`
	Permissions                    string                               `json:"permissons"`
	Market                         string                               `json:"market"`
	DHHChannelID                   string                               `json:"dhh_channel_id"`
	DHHPosID                       string                               `json:"dhh_pos_id"`
	DhhTaskID                      string                               `json:"dhh_task_id"`
	AMapSource                     string                               `json:"amap_source"`
	AMapChannel                    string                               `json:"amap_channel"`
	AMapRtaIDs                     string                               `json:"amap_rta_ids"`
	IsRTA                          int                                  `json:"is_rta"`
	RTAConfigID                    string                               `json:"rta_config_id"`
	IsOCPX                         int                                  `json:"is_ocpx"`
	IsExpToOCPX                    int                                  `json:"is_exp_to_ocpx"`
	ExpToOCPXWeight                int                                  `json:"exp_to_ocpx_weight"`
	KuaiShouOCPXAdID               string                               `json:"ks_ocpx_adid"`
	OCPXPromotionTypesJson         string                               `json:"ocpx_promotion_types_json"`
	UCOCPXCh                       string                               `json:"uc_ocpx_ch"`
	UCOCPXPosID                    string                               `json:"uc_ocpx_pos_id"`
	TencentGID                     string                               `json:"tencent_gid"`
	TencentMedia                   string                               `json:"tencent_media"`
	TencentChannel                 string                               `json:"tencent_channel"`
	TencentSchID                   string                               `json:"tencent_sch_id"`
	TencentLocID                   string                               `json:"tencent_loc_id"`
	CrowdLibType                   string                               `json:"crowd_lib_type"` // 人群包开关。0: 不用人群包，1: 开启人群包
	CrowdLibID                     string                               `json:"crowd_lib_id"`
	CrowdLibUseType                string                               `json:"crowd_lib_use_type"`           // 0 定向, 1 排除
	MaxReqDeviceIntervalType       string                               `json:"max_req_device_interval_type"` // 单设备请求间隔设置
	MaxReqDeviceIntervalNum        string                               `json:"max_req_device_interval_num"`  // 单设备请求间隔设置
	MaxExpType                     string                               `json:"max_exp_type"`
	MaxExpNum                      string                               `json:"max_exp_num"`
	MaxClkType                     string                               `json:"max_clk_type"`
	MaxClkNum                      string                               `json:"max_clk_num"`
	MaxExpDeviceType               string                               `json:"max_exp_device_type"`
	MaxExpDeviceNum                string                               `json:"max_exp_device_num"`
	MaxClkDeviceType               string                               `json:"max_clk_device_type"`
	MaxClkDeviceNum                string                               `json:"max_clk_device_num"`
	MaxLimitIPType                 string                               `json:"max_limit_ip_type"`
	MaxLimitIPNum                  string                               `json:"max_limit_ip_num"`
	LBSType                        int                                  `json:"lbs_type"`
	LBSWhiteListJson               string                               `json:"lbs_white_list_json"`
	LBSWhiteList                   [][]string                           `json:"lbs_white_list"`
	LBSBlackListJson               string                               `json:"lbs_black_list_json"`
	LBSBlackList                   [][]string                           `json:"lbs_black_list"`
	AndroidManufacturer            string                               `json:"android_manufacturer"`
	AndroidVersion                 string                               `json:"android_version"`
	AndroidVersionFiltersJson      string                               `json:"android_version_filters_json"`
	AndroidVersionFilters          []DspPlanAndroidVersionFilterItemStu `json:"android_version_filters"`
	SDKVersion                     string                               `json:"sdk_version"`
	DateType                       string                               `json:"date_type"`
	DateValue                      string                               `json:"date_value"`
	TimeType                       string                               `json:"time_type"`
	TimeListJson                   string                               `json:"time_list_json"`
	TimeList                       []DspPlanTimeItemStu                 `json:"time_list"`
	ExtraNotificationType          string                               `json:"extra_notification_type"`
	ExtraNotification1TimeValue    string                               `json:"extra_notification1_time_value"`
	ExtraNotification1MaxNum       string                               `json:"extra_notification1_max_num"`
	ExtraNotification2TimeValue    string                               `json:"extra_notification2_time_value"`
	ExtraNotification2MaxNum       string                               `json:"extra_notification2_max_num"`
	ExtraNotificationTimeoutType   string                               `json:"extra_notification_timeout_type"`
	ExtraNotificationTimeoutMaxNum string                               `json:"extra_notification_timeout_max_num"`
	ExtraWakeupType                string                               `json:"extra_wakeup_type"`
	ExtraWakeupMaxNum              string                               `json:"extra_wakeup_max_num"`
	ExtraWakeupTimeoutType         string                               `json:"extra_wakeup_timeout_type"`
	ExtraWakeupTimeoutMaxNum       string                               `json:"extra_wakeup_timeout_max_num"`
	ExtraWakeupTimeType            string                               `json:"extra_wakeup_time_type"`
	ExtraWakeupTimeValue           string                               `json:"extra_wakeup_time_value"`
	ExtraClipboardText             string                               `json:"extra_clipboard_text"`               // 策略之后最终clipboard
	IsExtraClipboardNoPackageName  int                                  `json:"is_extra_clipboard_no_package_name"` // 口令通投开关, 开: 不传包名
	ExtraClipboardsConfigJson      string                               `json:"extra_clipboards_config_json"`       // ui配置多个clipboard json
	ExtraClipboardsConfig          []DspPlanExtraClipboardItemStu       `json:"extra_clipboards_config"`            // ui配置多个clipboard
	Creatives                      []DspCreativeStu                     `json:"creatives"`
	ExpLinks                       []string                             `json:"exp_links"`
	ClkLinks                       []string                             `json:"clk_links"`
	UnitPriceType                  int                                  `json:"unit_price_type"`
	UnitPriceNum                   float32                              `json:"unit_price_num"`
	SspPriceType                   int                                  `json:"ssp_price_type"` // 0 CPM, 1 CPC, 2 动态CPM
	SspPriceNum                    float32                              `json:"ssp_price_num"`
	StoreActiveReduceRate          int                                  `json:"store_active_reduce_rate"`

	RequestPassRateType string `json:"request_pass_rate_type"` // 用户定向请求通过率 0:不限，1:限制
	RequestPassRateVal  string `json:"request_pass_rate_val"`  // 用户定向请求通过率 0-100

	// 动态CPM倍率
	SspDynamicCPMTimes float32 `json:"ssp_dynamic_cpm_times"`
	// 动态CPM最大值(元)
	SspDynamicCPMMaxNum float32 `json:"ssp_dynamic_cpm_max_num"`

	// 流量包id
	SupplyAppGroupIDListJson string   `json:"supply_app_group_id_list_json,omitempty"`
	SupplyAppGroupIDList     []string `json:"supply_app_group_id_list,omitempty"`
}

type DspPlanTimeItemStu struct {
	TimeValue []string `json:"time_value"`
}

type DspPlanAndroidVersionFilterItemStu struct {
	FilterManufacturer string `json:"filter_manufacturer"`
	FilterOsv          string `json:"filter_osv"`
}

type DspPlanExtraClipboardItemStu struct {
	Clipboard string `json:"text"`
	Weight    int    `json:"weight"`
}

type DspCreativeStu struct {
	CreativeGID   string `json:"creative_gid"`
	CreativeType  int    `json:"creative_type"`
	CRID          string `json:"crid"`
	Title         string `json:"title"`
	Description   string `json:"description"`
	IconURL       string `json:"icon_url"`
	ImageURL      string `json:"image_url"`
	ImageWidth    int    `json:"width"`
	ImageHeight   int    `json:"height"`
	VideoURL      string `json:"video_url"`
	CoverURL      string `json:"cover_url"`
	VideoWidth    int    `json:"video_width"`
	VideoHeight   int    `json:"video_height"`
	VideoDuration int    `json:"duration"`
	// ext notification素材
	NotificationTitle       string `json:"notification_title"`
	NotificationDescription string `json:"notification_description"`
	NotificationIconURL     string `json:"notification_icon_url"`
}

// 计划组
type DspPlanGroupStu struct {
	PlanGroupID           string                `json:"plan_gid"`
	Type                  int                   `json:"type"`
	PlanGroupPlanInfoJson string                `json:"plans_group_plan_info_json"`
	PlanGroupPlanInfo     []DspPlanGroupPlanStu `json:"plans_group_plan_info"`
}

type DspPlanGroupPlanStu struct {
	PlanID   string `json:"pid"`
	Priority string `json:"priority"`
	IsActive string `json:"is_active"`
}

type DspSupplyAppGroupConfigStu struct {
	UID          string   `json:"uid"`
	Os           string   `json:"os"`
	Weight       int      `json:"weight"`
	PosTypes     []string `json:"pos_types"`
	SupplyAppIDs []string `json:"supply_app_ids"`
}

type DspOCPXPromotionTypeStu struct {
	OCPXPromotionType string `json:"ocpx_promotion_type"`
}
