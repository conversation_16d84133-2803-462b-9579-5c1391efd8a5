package models

// 错误码定义
const (
	// 成功码
	ErrCodeSuccess = 10000
	ReasonSuccess  = "success"

	// dsp单日曝光限制
	ErrCodeMaxExpTotal = 900301
	ReasonMaxExpTotal  = "max exp total limit"

	// dsp单日点击限制
	ErrCodeMaxClkTotal = 900302
	ReasonMaxClkTotal  = "max clk total limit"

	// dsp单设备曝光限制
	ErrCodeMaxDeviceExp = 900303
	ReasonMaxDeviceExp  = "max device exp limit"

	// dsp单设备点击限制
	ErrCodeMaxDeviceClk = 900304
	ReasonMaxDeviceClk  = "max device clk limit"

	// dsp单日请求ip限制
	ErrCodeMaxDeviceIp = 900305
	ReasonMaxDeviceIp  = "max device ip limit"

	// dsp地域限制
	ErrCodeLbs = 900306
	ReasonLbs  = "lbs limit"

	// dsp机型限制
	ErrCodeManufacturer = 900307
	ReasonManufacturer  = "manufacturer limit"

	// dsp安卓版本限制
	ErrCodeAndroidVersion = 900308
	ReasonAndroidVersion  = "android version limit"

	// dsp预算限制
	ErrCodeBudget = 900309
	ReasonBudget  = "budget limit"

	// dsp投放日期限制
	ErrCodeDate = 900310
	ReasonDate  = "plan pos date limit"

	// dsp投放时段限制
	ErrCodeTime = 900311
	ReasonTime  = "plan pos time limit"

	// dsp不在人群定向包
	ErrCodeCrowdLib       = 900312
	ReasonCrowdLib        = "no in crowd lib" // 定向人群包 - 定向
	ReasonExcludeCrowdLib = "in crowd lib"    // 定向人群包 - 排除

	// dsp bid_cpm_floor不合适
	ErrCodeEcpm = 900313
	ReasonEcpm  = "ecpm not ok"

	// dsp单设备请求间隔限制
	ErrCodeMaxDeviceReqInterval = 900314
	ReasonMaxDeviceReqInterval  = "max device req interval limit"

	// dsp sdk限制
	ErrCodeSdkVersion = 900315
	ReasonSdkVersion  = "sdk version limit"

	// 数据优化类型未配置曝光点击链接
	ErrCodeNoExpClkLink = 900316
	ReasonNoExpClkLink  = "no exp clk link"

	// 超时
	ErrCodeTimeout = 900317
	ReasonTimeout  = "timeout"

	// dsp请求通过率限制
	ErrCodePassRate = 900318
	ReasonPassRate  = "dsp pass rate limit"

	// 计划组未找到
	ErrCodePlanGroupNotFound = 900319
	ReasonPlanGroupNotFound  = "plan group not found"

	// 没有有效的计划
	ErrCodeNoValidPlan = 900320
	ReasonNoValidPlan  = "no valid plan"

	// 没有创意
	ErrCodeNoCreatives = 900321
	ReasonNoCreatives  = "no creatives"

	// 没有h5链接或下载链接
	ErrCodeNoLink = 900322
	ReasonNoLink  = "no h5 link or download link"

	// SDK版本不符合要求
	ErrCodeSDKVersionNotOk = 900323
	ReasonSDKVersionNotOk  = "sdk version not ok"

	// RTA调用失败
	ErrCodeRtaFailed = 900324
	ReasonRtaFailed  = "rta failed"

	// 走人群包之前的流量
	ErrCodeCrowdLibBefore = 900325
	ReasonCrowdLibBefore  = "crowd lib before" // 走人群包之前的数量

	// 走rta之前的流量
	ErrCodeRtaBefore = 900326
	ReasonRtaBefore  = "rta before" // 走rta之前的流量
)
