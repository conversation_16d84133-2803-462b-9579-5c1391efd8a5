package models

import (
	"context"
	"dsp_core/config"
	"dsp_core/db"
	"dsp_core/utils"
	"encoding/json"
	"math/rand"
	"net/url"
	"strings"
	"sync"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases/holoclient"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// req
var bigdataReqArray []BigdataReqStu
var bigdataReqMutex sync.Mutex

// exp
var bigdataExpArray []BigdataExpStu
var bigdataExpMutex sync.Mutex

// clk
var bigdataClkArray []BigdataClkStu
var bigdataClkMutex sync.Mutex

// active
var bigdataActiveArray []BigdataActiveStu
var bigdataActiveMutex sync.Mutex

var bigdataReqTime int64
var bigdataExpTime int64
var bigdataClkTime int64
var bigdataActiveTime int64

// BigDataAdxReq ...
func BigDataAdxReq(c context.Context, dspReq DspReqStu, planInfo *DspPlanStu, bigdataUID string, crid string, code int, reason string) {
	defer func() {
		if err := recover(); err != nil {
			logger.GetSugaredLogger().Errorf("BigDataAdxReq panic, bigdataUID=%v, crid=%v, code=%v, err=%v", bigdataUID, crid, code, err)
		}
	}()

	// logger.GetSugaredLogger().Info("bigdata req")
	if planInfo == nil {
		logger.GetSugaredLogger().Errorf("planInfo is nil error, bigdataUID:%v, crid=%v, code=%v\n", bigdataUID, crid, code)
		return
	}

	var bigdataReqItem BigdataReqStu
	bigdataReqItem.UID = bigdataUID
	bigdataReqItem.PlanID = planInfo.PID
	bigdataReqItem.MarketType = planInfo.GroupMarketingType
	bigdataReqItem.AdsType = planInfo.GroupAdsType
	bigdataReqItem.ExtDspChannel = planInfo.GroupExtDspChannel
	bigdataReqItem.MediaChannel = "0"
	bigdataReqItem.SubChannelID = ""
	bigdataReqItem.ExtAdx = ""
	bigdataReqItem.OS = dspReq.Device.Os
	bigdataReqItem.OSV = dspReq.Device.OsVersion
	bigdataReqItem.Imei = dspReq.Device.Imei
	bigdataReqItem.ImeiMd5 = dspReq.Device.ImeiMd5
	bigdataReqItem.AndroidID = dspReq.Device.AndroidID
	bigdataReqItem.AndroidIDMd5 = dspReq.Device.AndroidIDMd5
	bigdataReqItem.Idfa = dspReq.Device.Idfa
	bigdataReqItem.IdfaMd5 = dspReq.Device.IdfaMd5
	bigdataReqItem.IP = dspReq.Device.IP
	bigdataReqItem.UA = dspReq.Device.Ua
	bigdataReqItem.Oaid = dspReq.Device.Oaid
	bigdataReqItem.Model = dspReq.Device.Model
	bigdataReqItem.Manufacturer = dspReq.Device.Manufacturer
	bigdataReqItem.CRID = crid
	bigdataReqItem.Code = code
	bigdataReqItem.Reason = reason
	bigdataReqItem.Day = time.Now()
	bigdataReqItem.Hour = time.Now().Format("15")
	bigdataReqItem.Minute = time.Now().Format("04")
	bigdataReqItem.Now = time.Now()

	bigdataReqMutex.Lock()
	bigdataReqArray = append(bigdataReqArray, bigdataReqItem)

	if len(bigdataReqArray) < config.ClickHouseReqMaxNum && utils.GetCurrentSecond()-bigdataReqTime < config.ClickHouseInterval {
		bigdataReqMutex.Unlock()
		return
	}

	destReqArray := bigdataReqArray[0:]
	bigdataReqArray = bigdataReqArray[0:0]
	bigdataReqMutex.Unlock()

	bigdataReqTime = utils.GetCurrentSecond()
	// logger.GetSugaredLogger().Info("clickhouse req ms 0: ", utils.GetCurrentMilliSecond())

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, _ := tx.Prepare("INSERT INTO dsp_rawdata.req_data (uid, plan_id, market_type, ads_type, ext_dsp_channel, media_channel, sub_channel_id, ext_adx, " +
		"os, osv, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, model," +
		"manufacturer, crid, code, reason, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	defer stmt.Close()

	for _, item := range destReqArray {
		if _, err := stmt.Exec(
			item.UID,
			item.PlanID,
			item.MarketType,
			item.AdsType,
			item.ExtDspChannel,
			item.MediaChannel,
			item.SubChannelID,
			item.ExtAdx,
			item.OS,
			item.OSV,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.CRID,
			item.Code,
			item.Reason,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			logger.GetSugaredLogger().Errorf("BigDataAdxReq Exec bigdataUID=%v, crid=%v, code=%v, err=%v", bigdataUID, crid, code, err)
		}
	}

	if err := tx.Commit(); err != nil {
		logger.GetSugaredLogger().Errorf("BigDataAdxReq commit bigdataUID=%v, crid=%v, code=%v, err=%v", bigdataUID, crid, code, err)
	}
	// logger.GetSugaredLogger().Info("clickhouse req end")
}

// BigDataExp ...
func BigDataExp(c *gin.Context, log url.Values, isOCPX int) {

	var bigdataAdxExpItem BigdataExpStu
	bigdataAdxExpItem.UID = log.Get("uid")
	bigdataAdxExpItem.GroupID = log.Get("group_id")
	bigdataAdxExpItem.PlanID = log.Get("plan_id")
	bigdataAdxExpItem.MarketType = log.Get("market_type")
	bigdataAdxExpItem.AdsType = log.Get("ads_type")
	bigdataAdxExpItem.ExtDspChannel = log.Get("ext_dsp_channel")
	bigdataAdxExpItem.MediaChannel = "0"
	bigdataAdxExpItem.SubChannelID = ""
	bigdataAdxExpItem.ExtAdx = ""
	bigdataAdxExpItem.OS = log.Get("os")
	bigdataAdxExpItem.OSV = log.Get("osv")
	bigdataAdxExpItem.DIDMd5 = log.Get("did_md5")
	bigdataAdxExpItem.Imei = log.Get("imei")
	bigdataAdxExpItem.ImeiMd5 = log.Get("imei_md5")
	bigdataAdxExpItem.AndroidID = log.Get("android_id")
	bigdataAdxExpItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxExpItem.Idfa = log.Get("idfa")
	bigdataAdxExpItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataAdxExpItem.IP = c.ClientIP()
	bigdataAdxExpItem.UA = c.GetHeader("User-Agent")
	bigdataAdxExpItem.Oaid = log.Get("oaid")
	bigdataAdxExpItem.OaidMd5 = log.Get("oaid_md5")
	bigdataAdxExpItem.Model = log.Get("model")
	bigdataAdxExpItem.Manufacturer = log.Get("manufacturer")
	bigdataAdxExpItem.CRID = log.Get("crid")
	bigdataAdxExpItem.CPMPrice = utils.ConvertStringToInt(log.Get("cpm_price"))
	bigdataAdxExpItem.ExtCPMPrice = utils.ConvertStringToInt(log.Get("ext_cpm_price"))
	bigdataAdxExpItem.Day = time.Now()
	bigdataAdxExpItem.Hour = time.Now().Format("15")
	bigdataAdxExpItem.Minute = time.Now().Format("04")
	bigdataAdxExpItem.Now = time.Now()
	bigdataAdxExpItem.IsOCPX = isOCPX
	bigdataAdxExpItem.DealTime = utils.ConvertStringToInt64(log.Get("deal_time"))
	bigdataAdxExpItem.SupplyCRID = c.Query("supply_crid")
	bigdataAdxExpItem.DemandCRID = c.Query("demand_crid")

	// save to holo
	BigDataHoloExp(bigdataAdxExpItem)

	// save to clickhouse
	BigDataClickHouseExp(bigdataAdxExpItem)
}

// BigDataClickHouseExp ...
func BigDataClickHouseExp(bigdataAdxExpItem BigdataExpStu) {
	bigdataExpMutex.Lock()
	bigdataExpArray = append(bigdataExpArray, bigdataAdxExpItem)

	if len(bigdataExpArray) < config.ClickHouseExpMaxNum && utils.GetCurrentSecond()-bigdataExpTime < config.ClickHouseInterval {
		bigdataExpMutex.Unlock()
		return
	}

	destExpArray := bigdataExpArray[0:]
	bigdataExpArray = bigdataExpArray[0:0]
	bigdataExpMutex.Unlock()

	bigdataExpTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, _ := tx.Prepare("INSERT INTO dsp_rawdata.exp_data (uid, plan_id, market_type, ads_type, ext_dsp_channel, media_channel, sub_channel_id, ext_adx, " +
		"os, osv, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, model," +
		"manufacturer, crid, cpm_price, ext_cpm_price, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	defer stmt.Close()

	for _, item := range destExpArray {
		if _, err := stmt.Exec(
			item.UID,
			item.PlanID,
			item.MarketType,
			item.AdsType,
			item.ExtDspChannel,
			item.MediaChannel,
			item.SubChannelID,
			item.ExtAdx,
			item.OS,
			item.OSV,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.CRID,
			item.CPMPrice,
			item.ExtCPMPrice,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			logger.GetSugaredLogger().Errorf("BigDataClickHouseExp Exec item=%v, err=%v", item, err)
		}
	}

	if err := tx.Commit(); err != nil {
		logger.GetSugaredLogger().Errorf("BigDataClickHouseExp Commit err=%v", err)
	}
}

// BigDataExpFromAdx ...
func BigDataExpFromAdx(c *gin.Context, planInfo *DspPlanStu, mhCpaReq *MHCpaReq, mediaChannel string, subChannelID string, extAdx string, cpmPrice int, extCpmPrice int, isOCPX int) {

	var bigdataAdxExpItem BigdataExpStu
	bigdataAdxExpItem.UID = mhCpaReq.UID
	bigdataAdxExpItem.GroupID = planInfo.GID
	bigdataAdxExpItem.PlanID = planInfo.PID
	bigdataAdxExpItem.MarketType = planInfo.GroupMarketingType
	bigdataAdxExpItem.AdsType = planInfo.GroupAdsType
	bigdataAdxExpItem.ExtDspChannel = planInfo.GroupExtDspChannel
	bigdataAdxExpItem.MediaChannel = mediaChannel
	bigdataAdxExpItem.SubChannelID = subChannelID
	bigdataAdxExpItem.ExtAdx = extAdx
	bigdataAdxExpItem.OS = mhCpaReq.Os
	bigdataAdxExpItem.OSV = mhCpaReq.Osv
	bigdataAdxExpItem.DIDMd5 = mhCpaReq.DIDMd5
	bigdataAdxExpItem.Imei = mhCpaReq.Imei
	bigdataAdxExpItem.ImeiMd5 = mhCpaReq.ImeiMd5
	bigdataAdxExpItem.AndroidID = ""
	bigdataAdxExpItem.AndroidIDMd5 = ""
	bigdataAdxExpItem.Idfa = mhCpaReq.Idfa
	bigdataAdxExpItem.IdfaMd5 = mhCpaReq.IdfaMd5
	bigdataAdxExpItem.IP = mhCpaReq.IP
	bigdataAdxExpItem.UA = mhCpaReq.UA
	bigdataAdxExpItem.Oaid = mhCpaReq.Oaid
	bigdataAdxExpItem.OaidMd5 = mhCpaReq.OaidMd5
	bigdataAdxExpItem.Model = mhCpaReq.Model
	bigdataAdxExpItem.Manufacturer = mhCpaReq.Manufacturer
	bigdataAdxExpItem.CRID = ""
	bigdataAdxExpItem.CPMPrice = cpmPrice
	bigdataAdxExpItem.ExtCPMPrice = extCpmPrice
	bigdataAdxExpItem.Day = time.Now()
	bigdataAdxExpItem.Hour = time.Now().Format("15")
	bigdataAdxExpItem.Minute = time.Now().Format("04")
	bigdataAdxExpItem.Now = time.Now()
	bigdataAdxExpItem.IsOCPX = isOCPX

	// save to holo
	BigDataHoloExp(bigdataAdxExpItem)

	// save to clickhouse
	BigDataClickHouseExp(bigdataAdxExpItem)
}

// BigDataHoloExp ...
func BigDataHoloExp(bigdataAdxExpItem BigdataExpStu) {
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("deal", "exp_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxExpItem.UID, len(bigdataAdxExpItem.UID))
	put.SetTextValByColName("group_id", bigdataAdxExpItem.GroupID, len(bigdataAdxExpItem.GroupID))
	put.SetTextValByColName("plan_id", bigdataAdxExpItem.PlanID, len(bigdataAdxExpItem.PlanID))
	put.SetTextValByColName("market_type", bigdataAdxExpItem.MarketType, len(bigdataAdxExpItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataAdxExpItem.AdsType, len(bigdataAdxExpItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataAdxExpItem.ExtDspChannel, len(bigdataAdxExpItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataAdxExpItem.MediaChannel, len(bigdataAdxExpItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataAdxExpItem.SubChannelID, len(bigdataAdxExpItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataAdxExpItem.ExtAdx, len(bigdataAdxExpItem.ExtAdx))
	put.SetTextValByColName("os", bigdataAdxExpItem.OS, len(bigdataAdxExpItem.OS))
	put.SetTextValByColName("osv", bigdataAdxExpItem.OSV, len(bigdataAdxExpItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxExpItem.DIDMd5, len(bigdataAdxExpItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxExpItem.Imei, len(bigdataAdxExpItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxExpItem.ImeiMd5, len(bigdataAdxExpItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxExpItem.AndroidID, len(bigdataAdxExpItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxExpItem.AndroidIDMd5, len(bigdataAdxExpItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxExpItem.Idfa, len(bigdataAdxExpItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxExpItem.IdfaMd5, len(bigdataAdxExpItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxExpItem.IP, len(bigdataAdxExpItem.IP))
	put.SetTextValByColName("ua", bigdataAdxExpItem.UA, len(bigdataAdxExpItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxExpItem.Oaid, len(bigdataAdxExpItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataAdxExpItem.OaidMd5, len(bigdataAdxExpItem.OaidMd5))
	put.SetTextValByColName("model", bigdataAdxExpItem.Model, len(bigdataAdxExpItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxExpItem.Manufacturer, len(bigdataAdxExpItem.Manufacturer))
	put.SetTextValByColName("crid", bigdataAdxExpItem.CRID, len(bigdataAdxExpItem.CRID))
	put.SetInt32ValByColName("cpm_price", int32(bigdataAdxExpItem.CPMPrice))
	put.SetInt32ValByColName("ext_cpm_price", int32(bigdataAdxExpItem.ExtCPMPrice))
	put.SetInt32ValByColName("is_ocpx", int32(bigdataAdxExpItem.IsOCPX))
	if bigdataAdxExpItem.DealTime > 0 {
		put.SetTimestamptzValByColName("deal_time", bigdataAdxExpItem.DealTime-946684800000000)
	}
	put.SetTextValByColName("supply_crid", bigdataAdxExpItem.SupplyCRID, len(bigdataAdxExpItem.SupplyCRID))
	put.SetTextValByColName("demand_crid", bigdataAdxExpItem.DemandCRID, len(bigdataAdxExpItem.DemandCRID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
	// // 写入 v3 100% 概率
	// if rand.Intn(100) < 100 {
	// 	db.GlbHologresAdxDSPIgnoreDataDbV3.Submit(put)
	// }
}

// BigDataClk ...
func BigDataClk(c *gin.Context, log url.Values) {
	// logger.GetSugaredLogger().Info("bigdata clk")

	var bigdataAdxClkItem BigdataClkStu
	bigdataAdxClkItem.UID = log.Get("uid")
	bigdataAdxClkItem.GroupID = log.Get("group_id")
	bigdataAdxClkItem.PlanID = log.Get("plan_id")
	bigdataAdxClkItem.MarketType = log.Get("market_type")
	bigdataAdxClkItem.AdsType = log.Get("ads_type")
	bigdataAdxClkItem.ExtDspChannel = log.Get("ext_dsp_channel")
	bigdataAdxClkItem.MediaChannel = log.Get("media_channel")
	bigdataAdxClkItem.SubChannelID = log.Get("sub_channel_id")
	bigdataAdxClkItem.ExtAdx = log.Get("ext_adx")
	bigdataAdxClkItem.OS = log.Get("os")
	bigdataAdxClkItem.OSV = log.Get("osv")
	bigdataAdxClkItem.DIDMd5 = log.Get("did_md5")
	bigdataAdxClkItem.Imei = log.Get("imei")
	bigdataAdxClkItem.ImeiMd5 = log.Get("imei_md5")
	bigdataAdxClkItem.AndroidID = log.Get("android_id")
	bigdataAdxClkItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxClkItem.Idfa = log.Get("idfa")
	bigdataAdxClkItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataAdxClkItem.IP = c.ClientIP()
	bigdataAdxClkItem.UA = c.GetHeader("User-Agent")
	bigdataAdxClkItem.Oaid = log.Get("oaid")
	bigdataAdxClkItem.OaidMd5 = log.Get("oaid_md5")
	bigdataAdxClkItem.CAIDMulti = log.Get("caid_multi")
	bigdataAdxClkItem.Model = log.Get("model")
	bigdataAdxClkItem.Manufacturer = log.Get("manufacturer")
	bigdataAdxClkItem.CRID = log.Get("crid")
	bigdataAdxClkItem.ReqWidth = c.Query("req_width")
	bigdataAdxClkItem.ReqHeight = c.Query("req_height")
	bigdataAdxClkItem.Width = c.Query("width")
	bigdataAdxClkItem.Height = c.Query("height")
	bigdataAdxClkItem.DownX = c.Query("down_x")
	bigdataAdxClkItem.DownY = c.Query("down_y")
	bigdataAdxClkItem.UpX = c.Query("up_x")
	bigdataAdxClkItem.UpY = c.Query("up_y")
	bigdataAdxClkItem.CPCPrice = utils.ConvertStringToInt(log.Get("cpc_price"))
	bigdataAdxClkItem.ExtCPCPrice = utils.ConvertStringToInt(log.Get("ext_cpc_price"))
	bigdataAdxClkItem.CallBack = strings.Replace(c.Query("callback"), "__MH_CALLBACK__", "", -1)
	bigdataAdxClkItem.Day = time.Now()
	bigdataAdxClkItem.Hour = time.Now().Format("15")
	bigdataAdxClkItem.Minute = time.Now().Format("04")
	bigdataAdxClkItem.Now = time.Now()
	bigdataAdxClkItem.DealTime = utils.ConvertStringToInt64(log.Get("deal_time"))
	bigdataAdxClkItem.SupplyCRID = c.Query("supply_crid")
	bigdataAdxClkItem.DemandCRID = c.Query("demand_crid")

	// save to holo
	BigDataHoloClk(bigdataAdxClkItem)

	// save to clickhouse
	BigDataClickHouseClk(bigdataAdxClkItem)
}

// BigDataHoloClk ...
func BigDataClickHouseClk(bigdataAdxClkItem BigdataClkStu) {
	bigdataClkMutex.Lock()
	bigdataClkArray = append(bigdataClkArray, bigdataAdxClkItem)

	if len(bigdataClkArray) < config.ClickHouseClkMaxNum && utils.GetCurrentSecond()-bigdataClkTime < config.ClickHouseInterval {
		bigdataClkMutex.Unlock()
		return
	}

	destClkArray := bigdataClkArray[0:]
	bigdataClkArray = bigdataClkArray[0:0]
	bigdataClkMutex.Unlock()

	bigdataClkTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, _ := tx.Prepare("INSERT INTO dsp_rawdata.clk_data (uid, plan_id, market_type, ads_type, ext_dsp_channel, media_channel, sub_channel_id, ext_adx, " +
		"os, osv, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, model," +
		"manufacturer, crid, req_width, req_height, width, height, down_x, down_y, up_x, up_y, cpc_price, ext_cpc_price, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	defer stmt.Close()

	for _, item := range destClkArray {
		if _, err := stmt.Exec(
			item.UID,
			item.PlanID,
			item.MarketType,
			item.AdsType,
			item.ExtDspChannel,
			item.MediaChannel,
			item.SubChannelID,
			item.ExtAdx,
			item.OS,
			item.OSV,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.Model,
			item.Manufacturer,
			item.CRID,
			item.ReqWidth,
			item.ReqHeight,
			item.Width,
			item.Height,
			item.DownX,
			item.DownY,
			item.UpX,
			item.UpY,
			item.CPCPrice,
			item.ExtCPCPrice,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			logger.GetSugaredLogger().Errorf("BigDataClickHouseClk Exec item=%v, err=%v", item, err)
		}
	}

	if err := tx.Commit(); err != nil {
		logger.GetSugaredLogger().Errorf("BigDataClickHouseClk Commit err=%v", err)
	}
}

// BigDataClkFromAdx ...
func BigDataClkFromAdx(c *gin.Context, planInfo *DspPlanStu, mhCpaReq *MHCpaReq, mediaChannel string, subChannelID string, extAdx string, cpcPrice int, extCpcPrice int) {
	// logger.GetSugaredLogger().Info("clickhouse clk begin")

	// callback redis 1 hour
	if len(mhCpaReq.DownCallback) > 0 {
		redisCallBackKey := "redis_active_callback_" + mhCpaReq.UID
		db.GetRedis().Set(c, utils.Timeout50mill, redisCallBackKey, mhCpaReq.DownCallback, 1*time.Hour)
	}

	var bigdataAdxClkItem BigdataClkStu
	bigdataAdxClkItem.UID = mhCpaReq.UID
	bigdataAdxClkItem.PlanID = planInfo.PID
	bigdataAdxClkItem.GroupID = planInfo.GID
	bigdataAdxClkItem.MarketType = planInfo.GroupMarketingType
	bigdataAdxClkItem.AdsType = planInfo.GroupAdsType
	bigdataAdxClkItem.ExtDspChannel = planInfo.GroupExtDspChannel
	bigdataAdxClkItem.MediaChannel = mediaChannel
	bigdataAdxClkItem.SubChannelID = subChannelID
	bigdataAdxClkItem.ExtAdx = extAdx
	bigdataAdxClkItem.OS = mhCpaReq.Os
	bigdataAdxClkItem.OSV = mhCpaReq.Osv
	bigdataAdxClkItem.Imei = mhCpaReq.Imei
	bigdataAdxClkItem.ImeiMd5 = mhCpaReq.ImeiMd5
	// bigdataAdxClkItem.AndroidID = log.Get("android_id")
	// bigdataAdxClkItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataAdxClkItem.Idfa = mhCpaReq.Idfa
	bigdataAdxClkItem.IdfaMd5 = mhCpaReq.IdfaMd5
	bigdataAdxClkItem.OSV = mhCpaReq.Osv
	bigdataAdxClkItem.DIDMd5 = mhCpaReq.DIDMd5
	bigdataAdxClkItem.IP = mhCpaReq.IP
	bigdataAdxClkItem.UA = mhCpaReq.UA
	bigdataAdxClkItem.Oaid = mhCpaReq.Oaid
	bigdataAdxClkItem.OaidMd5 = mhCpaReq.OaidMd5
	if len(mhCpaReq.CAIDMulti) > 0 {
		tmpCAIDMultiJson, _ := json.Marshal(mhCpaReq.CAIDMulti)
		bigdataAdxClkItem.CAIDMulti = string(tmpCAIDMultiJson)
	}
	// bigdataAdxClkItem.Model = log.Get("model")
	// bigdataAdxClkItem.Manufacturer = log.Get("manufacturer")
	// bigdataAdxClkItem.CRID = log.Get("crid")
	bigdataAdxClkItem.CallBack = mhCpaReq.DownCallback
	bigdataAdxClkItem.CPCPrice = cpcPrice
	bigdataAdxClkItem.ExtCPCPrice = extCpcPrice
	bigdataAdxClkItem.Day = time.Now()
	bigdataAdxClkItem.Hour = time.Now().Format("15")
	bigdataAdxClkItem.Minute = time.Now().Format("04")
	bigdataAdxClkItem.Now = time.Now()

	bigdataAdxClkItem.ClickId = mhCpaReq.ClickId
	bigdataAdxClkItem.ClickTime = mhCpaReq.ClickTime

	// save to holo
	BigDataHoloClk(bigdataAdxClkItem)

	// save to clickhouse
	BigDataClickHouseClk(bigdataAdxClkItem)
}

// BigDataHoloClk ...
func BigDataHoloClk(bigdataAdxClkItem BigdataClkStu) {
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("deal", "clk_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataAdxClkItem.UID, len(bigdataAdxClkItem.UID))
	put.SetTextValByColName("group_id", bigdataAdxClkItem.GroupID, len(bigdataAdxClkItem.GroupID))
	put.SetTextValByColName("plan_id", bigdataAdxClkItem.PlanID, len(bigdataAdxClkItem.PlanID))
	put.SetTextValByColName("market_type", bigdataAdxClkItem.MarketType, len(bigdataAdxClkItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataAdxClkItem.AdsType, len(bigdataAdxClkItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataAdxClkItem.ExtDspChannel, len(bigdataAdxClkItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataAdxClkItem.MediaChannel, len(bigdataAdxClkItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataAdxClkItem.SubChannelID, len(bigdataAdxClkItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataAdxClkItem.ExtAdx, len(bigdataAdxClkItem.ExtAdx))
	put.SetTextValByColName("os", bigdataAdxClkItem.OS, len(bigdataAdxClkItem.OS))
	put.SetTextValByColName("osv", bigdataAdxClkItem.OSV, len(bigdataAdxClkItem.OSV))
	put.SetTextValByColName("did_md5", bigdataAdxClkItem.DIDMd5, len(bigdataAdxClkItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataAdxClkItem.Imei, len(bigdataAdxClkItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataAdxClkItem.ImeiMd5, len(bigdataAdxClkItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataAdxClkItem.AndroidID, len(bigdataAdxClkItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataAdxClkItem.AndroidIDMd5, len(bigdataAdxClkItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataAdxClkItem.Idfa, len(bigdataAdxClkItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataAdxClkItem.IdfaMd5, len(bigdataAdxClkItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataAdxClkItem.IP, len(bigdataAdxClkItem.IP))
	put.SetTextValByColName("ua", bigdataAdxClkItem.UA, len(bigdataAdxClkItem.UA))
	put.SetTextValByColName("oaid", bigdataAdxClkItem.Oaid, len(bigdataAdxClkItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataAdxClkItem.OaidMd5, len(bigdataAdxClkItem.OaidMd5))
	put.SetTextValByColName("caid_multi", bigdataAdxClkItem.CAIDMulti, len(bigdataAdxClkItem.CAIDMulti))
	put.SetTextValByColName("model", bigdataAdxClkItem.Model, len(bigdataAdxClkItem.Model))
	put.SetTextValByColName("manufacturer", bigdataAdxClkItem.Manufacturer, len(bigdataAdxClkItem.Manufacturer))
	put.SetTextValByColName("crid", bigdataAdxClkItem.CRID, len(bigdataAdxClkItem.CRID))
	put.SetTextValByColName("req_width", bigdataAdxClkItem.ReqWidth, len(bigdataAdxClkItem.ReqWidth))
	put.SetTextValByColName("req_height", bigdataAdxClkItem.ReqHeight, len(bigdataAdxClkItem.ReqHeight))
	put.SetTextValByColName("width", bigdataAdxClkItem.Width, len(bigdataAdxClkItem.Width))
	put.SetTextValByColName("height", bigdataAdxClkItem.Height, len(bigdataAdxClkItem.Height))
	put.SetTextValByColName("down_x", bigdataAdxClkItem.DownX, len(bigdataAdxClkItem.DownX))
	put.SetTextValByColName("down_y", bigdataAdxClkItem.DownY, len(bigdataAdxClkItem.DownY))
	put.SetTextValByColName("up_x", bigdataAdxClkItem.UpX, len(bigdataAdxClkItem.UpX))
	put.SetTextValByColName("up_y", bigdataAdxClkItem.UpY, len(bigdataAdxClkItem.UpY))
	put.SetTextValByColName("callback", bigdataAdxClkItem.CallBack, len(bigdataAdxClkItem.CallBack))
	put.SetInt32ValByColName("cpc_price", int32(bigdataAdxClkItem.CPCPrice))
	put.SetInt32ValByColName("ext_cpc_price", int32(bigdataAdxClkItem.ExtCPCPrice))

	// debug ams click_id, click_time
	put.SetTextValByColName("click_id", bigdataAdxClkItem.ClickId, len(bigdataAdxClkItem.ClickId))
	put.SetTextValByColName("click_time", bigdataAdxClkItem.ClickTime, len(bigdataAdxClkItem.ClickTime))

	if bigdataAdxClkItem.DealTime > 0 {
		put.SetTimestamptzValByColName("deal_time", bigdataAdxClkItem.DealTime-946684800000000)
	}
	put.SetTextValByColName("supply_crid", bigdataAdxClkItem.SupplyCRID, len(bigdataAdxClkItem.SupplyCRID))
	put.SetTextValByColName("demand_crid", bigdataAdxClkItem.DemandCRID, len(bigdataAdxClkItem.DemandCRID))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
}

// BigDataHoloActive ...
func BigDataHoloActive(c *gin.Context, log url.Values) {
	// logger.GetSugaredLogger().Infof("kbg_debug, holo active uid=%v, pid=%v", log.Get("uid"), log.Get("plan_id"))
	// save to holo
	var bigdataActiveItem BigdataActiveStu
	bigdataActiveItem.UID = log.Get("uid")
	bigdataActiveItem.GroupID = log.Get("group_id")
	bigdataActiveItem.PlanID = log.Get("plan_id")
	bigdataActiveItem.MarketType = log.Get("market_type")
	bigdataActiveItem.AdsType = log.Get("ads_type")
	bigdataActiveItem.ExtDspChannel = log.Get("ext_dsp_channel")
	bigdataActiveItem.MediaChannel = log.Get("media_channel")
	bigdataActiveItem.SubChannelID = log.Get("sub_channel_id")
	bigdataActiveItem.ExtAdx = log.Get("ext_adx")
	bigdataActiveItem.OS = log.Get("os")
	bigdataActiveItem.OSV = log.Get("osv")
	bigdataActiveItem.DIDMd5 = log.Get("did_md5")
	bigdataActiveItem.Imei = log.Get("imei")
	bigdataActiveItem.ImeiMd5 = log.Get("imei_md5")
	bigdataActiveItem.AndroidID = log.Get("android_id")
	bigdataActiveItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataActiveItem.Idfa = log.Get("idfa")
	bigdataActiveItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataActiveItem.IP = c.ClientIP()
	bigdataActiveItem.UA = c.GetHeader("User-Agent")
	bigdataActiveItem.Oaid = log.Get("oaid")
	bigdataActiveItem.OaidMd5 = log.Get("oaid_md5")
	bigdataActiveItem.CAIDMulti = log.Get("caid_multi")
	bigdataActiveItem.Model = log.Get("model")
	bigdataActiveItem.Manufacturer = log.Get("manufacturer")
	bigdataActiveItem.CRID = log.Get("crid")
	bigdataActiveItem.CPAPrice = utils.ConvertStringToInt(log.Get("cpa_price"))
	bigdataActiveItem.ExtCPAPrice = utils.ConvertStringToInt(log.Get("ext_cpa_price"))
	bigdataActiveItem.CallBack = ""
	bigdataActiveItem.TransformType = c.Query("actionType")
	if bigdataActiveItem.MarketType == "1" && bigdataActiveItem.AdsType == "1" && bigdataActiveItem.ExtDspChannel == "12" {
		bigdataActiveItem.TransformType = c.Query("event")
	} else if bigdataActiveItem.MarketType == "1" && bigdataActiveItem.AdsType == "1" && bigdataActiveItem.ExtDspChannel == "13" {
		bigdataActiveItem.TransformType = c.Query("transformtype")
	} else if bigdataActiveItem.MarketType == "1" && bigdataActiveItem.AdsType == "1" && bigdataActiveItem.ExtDspChannel == "14" {
		bigdataActiveItem.TransformType = c.Query("event_type") //  表示APP新， 1 表示APP促活
	}

	bigdataActiveItem.AcceptedTransformType = log.Get("accepted_transform_type")
	bigdataActiveItem.Source = log.Get("source")

	bigdataActiveItem.IsCallBack = 0
	if bigdataActiveItem.MarketType == "1" && bigdataActiveItem.AdsType == "1" && (bigdataActiveItem.ExtDspChannel == "3" || bigdataActiveItem.ExtDspChannel == "8") {
		planInfo := GetPlanInfoFromMysqlByPlanID(c, log.Get("plan_id"))
		activeReduceRate := planInfo.StoreActiveReduceRate
		randValue := rand.Intn(100)
		if randValue < 100-activeReduceRate {
			bigdataActiveItem.IsCallBack = 1
		}
		// logger.GetSugaredLogger().Info("kbg_debug, reduce:", activeReduceRate, randValue, bigdataActiveItem.IsCallBack)
	}

	// save to holo
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("deal", "conversion_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataActiveItem.UID, len(bigdataActiveItem.UID))
	put.SetTextValByColName("group_id", bigdataActiveItem.GroupID, len(bigdataActiveItem.GroupID))
	put.SetTextValByColName("plan_id", bigdataActiveItem.PlanID, len(bigdataActiveItem.PlanID))
	put.SetTextValByColName("market_type", bigdataActiveItem.MarketType, len(bigdataActiveItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataActiveItem.AdsType, len(bigdataActiveItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataActiveItem.ExtDspChannel, len(bigdataActiveItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataActiveItem.MediaChannel, len(bigdataActiveItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataActiveItem.SubChannelID, len(bigdataActiveItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataActiveItem.ExtAdx, len(bigdataActiveItem.ExtAdx))
	put.SetTextValByColName("os", bigdataActiveItem.OS, len(bigdataActiveItem.OS))
	put.SetTextValByColName("osv", bigdataActiveItem.OSV, len(bigdataActiveItem.OSV))
	put.SetTextValByColName("did_md5", bigdataActiveItem.DIDMd5, len(bigdataActiveItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataActiveItem.Imei, len(bigdataActiveItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataActiveItem.ImeiMd5, len(bigdataActiveItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataActiveItem.AndroidID, len(bigdataActiveItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataActiveItem.AndroidIDMd5, len(bigdataActiveItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataActiveItem.Idfa, len(bigdataActiveItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataActiveItem.IdfaMd5, len(bigdataActiveItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataActiveItem.IP, len(bigdataActiveItem.IP))
	put.SetTextValByColName("ua", bigdataActiveItem.UA, len(bigdataActiveItem.UA))
	put.SetTextValByColName("oaid", bigdataActiveItem.Oaid, len(bigdataActiveItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataActiveItem.OaidMd5, len(bigdataActiveItem.OaidMd5))
	put.SetTextValByColName("caid_multi", bigdataActiveItem.CAIDMulti, len(bigdataActiveItem.CAIDMulti))
	put.SetTextValByColName("model", bigdataActiveItem.Model, len(bigdataActiveItem.Model))
	put.SetTextValByColName("manufacturer", bigdataActiveItem.Manufacturer, len(bigdataActiveItem.Manufacturer))
	put.SetInt32ValByColName("cpa_price", int32(bigdataActiveItem.CPAPrice))
	put.SetInt32ValByColName("ext_cpa_price", int32(bigdataActiveItem.ExtCPAPrice))
	put.SetTextValByColName("transform_type", bigdataActiveItem.TransformType, len(bigdataActiveItem.TransformType))
	put.SetTextValByColName("accepted_transform_type", bigdataActiveItem.AcceptedTransformType, len(bigdataActiveItem.AcceptedTransformType))
	put.SetTextValByColName("source", bigdataActiveItem.Source, len(bigdataActiveItem.Source))
	put.SetInt32ValByColName("is_callback", int32(bigdataActiveItem.IsCallBack))
	put.SetInt32ValByColName("is_finish_callback", 0)

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)

	// save to clickhouse
	bigdataActiveMutex.Lock()
	bigdataActiveArray = append(bigdataActiveArray, bigdataActiveItem)

	if len(bigdataActiveArray) < config.ClickHouseClkMaxNum && utils.GetCurrentSecond()-bigdataActiveTime < config.ClickHouseInterval {
		bigdataActiveMutex.Unlock()
		return
	}
	destDhhArray := bigdataActiveArray[0:]
	bigdataActiveArray = bigdataActiveArray[0:0]
	bigdataActiveMutex.Unlock()

	bigdataActiveTime = utils.GetCurrentSecond()

	tx, _ := db.GlbClickHouseDb.Begin()
	stmt, _ := tx.Prepare("INSERT INTO dsp_rawdata.active_data (uid, plan_id, market_type, ads_type, ext_dsp_channel, media_channel, sub_channel_id, ext_adx, " +
		"os, osv, imei, imei_md5, android_id, android_id_md5, idfa, idfa_md5, ip, ua, oaid, oaid_md5, model," +
		"manufacturer, crid, cpa_price, ext_cpa_price, callback, transform_type, dd, hh, mm, report_time" +
		") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

	defer stmt.Close()

	for _, item := range destDhhArray {
		if _, err := stmt.Exec(
			item.UID,
			item.PlanID,
			item.MarketType,
			item.AdsType,
			item.ExtDspChannel,
			item.MediaChannel,
			item.SubChannelID,
			item.ExtAdx,
			item.OS,
			item.OSV,
			item.Imei,
			item.ImeiMd5,
			item.AndroidID,
			item.AndroidIDMd5,
			item.Idfa,
			item.IdfaMd5,
			item.IP,
			item.UA,
			item.Oaid,
			item.OaidMd5,
			item.Model,
			item.Manufacturer,
			item.CRID,
			item.CPAPrice,
			item.ExtCPAPrice,
			item.CallBack,
			item.TransformType,
			time.Now(),
			time.Now().Format("15"),
			time.Now().Format("04"),
			time.Now(),
		); err != nil {
			logger.GetSugaredLogger().Errorf("BigDataHoloActive Exec item=%v, err=%v", item, err)
		}
	}

	if err := tx.Commit(); err != nil {
		logger.GetSugaredLogger().Errorf("BigDataHoloActive Commit err=%v", err)
	}
}

// BigDataDebug ...
func BigDataDebug(c *gin.Context, req string, resp string, mhCpaReq *MHCpaReq) {

	// save to holo
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("debug", "debug_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", mhCpaReq.UID, len(mhCpaReq.UID))
	put.SetTextValByColName("req", req, len(req))
	put.SetTextValByColName("resp", resp, len(resp))

	tmpJsonData, _ := json.Marshal(mhCpaReq)
	put.SetTextValByColName("info", string(tmpJsonData), len(string(tmpJsonData)))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
}

// BigDataDebug1 ...
func BigDataDebug1(c context.Context, uuid string, reqid string, req string, resp string, info string) {

	// save to holo
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("debug", "debug_data"))
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", reqid, len(reqid))
	put.SetTextValByColName("req", req, len(req))
	put.SetTextValByColName("resp", resp, len(resp))

	put.SetTextValByColName("info", info, len(info))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
}

// BigDataJuMeiTongYiActive
func BigDataJuMeiTongYiActive(c *gin.Context, log url.Values) {

	var bigdataActiveItem BigdataActiveStu
	bigdataActiveItem.UID = log.Get("uid")
	bigdataActiveItem.PlanID = log.Get("plan_id")
	bigdataActiveItem.MarketType = log.Get("market_type")
	bigdataActiveItem.AdsType = log.Get("ads_type")
	bigdataActiveItem.ExtDspChannel = log.Get("ext_dsp_channel")
	bigdataActiveItem.MediaChannel = log.Get("media_channel")
	bigdataActiveItem.SubChannelID = log.Get("sub_channel_id")
	bigdataActiveItem.ExtAdx = log.Get("ext_adx")
	bigdataActiveItem.OS = log.Get("os")
	bigdataActiveItem.OSV = log.Get("osv")
	bigdataActiveItem.DIDMd5 = log.Get("did_md5")
	bigdataActiveItem.Imei = log.Get("imei")
	bigdataActiveItem.ImeiMd5 = log.Get("imei_md5")
	bigdataActiveItem.AndroidID = log.Get("android_id")
	bigdataActiveItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataActiveItem.Idfa = log.Get("idfa")
	bigdataActiveItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataActiveItem.IP = c.ClientIP()
	bigdataActiveItem.UA = c.GetHeader("User-Agent")
	bigdataActiveItem.Oaid = log.Get("oaid")
	bigdataActiveItem.OaidMd5 = log.Get("oaid_md5")
	bigdataActiveItem.CAIDMulti = log.Get("caid_multi")
	bigdataActiveItem.Model = log.Get("model")
	bigdataActiveItem.Manufacturer = log.Get("manufacturer")
	// bigdataActiveItem.CRID = log.Get("crid")
	bigdataActiveItem.CPAPrice = utils.ConvertStringToInt(log.Get("cpa_price"))
	// bigdataActiveItem.ExtCPAPrice = utils.ConvertStringToInt(log.Get("ext_cpa_price"))
	bigdataActiveItem.CallBack = ""
	bigdataActiveItem.TransformType = c.Query("transfer_event") // 1 激活 2 注册 4 次留 5 付费
	// bigdataActiveItem.AcceptedTransformType = log.Get("accepted_transform_type")
	bigdataActiveItem.Source = log.Get("source")

	// save to holo
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("deal", "conversion_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataActiveItem.UID, len(bigdataActiveItem.UID))
	put.SetTextValByColName("plan_id", bigdataActiveItem.PlanID, len(bigdataActiveItem.PlanID))
	put.SetTextValByColName("market_type", bigdataActiveItem.MarketType, len(bigdataActiveItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataActiveItem.AdsType, len(bigdataActiveItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataActiveItem.ExtDspChannel, len(bigdataActiveItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataActiveItem.MediaChannel, len(bigdataActiveItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataActiveItem.SubChannelID, len(bigdataActiveItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataActiveItem.ExtAdx, len(bigdataActiveItem.ExtAdx))
	put.SetTextValByColName("os", bigdataActiveItem.OS, len(bigdataActiveItem.OS))
	put.SetTextValByColName("osv", bigdataActiveItem.OSV, len(bigdataActiveItem.OSV))
	put.SetTextValByColName("did_md5", bigdataActiveItem.DIDMd5, len(bigdataActiveItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataActiveItem.Imei, len(bigdataActiveItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataActiveItem.ImeiMd5, len(bigdataActiveItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataActiveItem.AndroidID, len(bigdataActiveItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataActiveItem.AndroidIDMd5, len(bigdataActiveItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataActiveItem.Idfa, len(bigdataActiveItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataActiveItem.IdfaMd5, len(bigdataActiveItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataActiveItem.IP, len(bigdataActiveItem.IP))
	put.SetTextValByColName("ua", bigdataActiveItem.UA, len(bigdataActiveItem.UA))
	put.SetTextValByColName("oaid", bigdataActiveItem.Oaid, len(bigdataActiveItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataActiveItem.OaidMd5, len(bigdataActiveItem.OaidMd5))
	put.SetTextValByColName("caid_multi", bigdataActiveItem.CAIDMulti, len(bigdataActiveItem.CAIDMulti))
	put.SetTextValByColName("model", bigdataActiveItem.Model, len(bigdataActiveItem.Model))
	put.SetTextValByColName("manufacturer", bigdataActiveItem.Manufacturer, len(bigdataActiveItem.Manufacturer))
	// put.SetInt32ValByColName("cpa_price", int32(bigdataActiveItem.CPAPrice))
	// put.SetInt32ValByColName("ext_cpa_price", int32(bigdataActiveItem.ExtCPAPrice))
	put.SetTextValByColName("transform_type", bigdataActiveItem.TransformType, len(bigdataActiveItem.TransformType))
	// put.SetTextValByColName("accepted_transform_type", bigdataActiveItem.AcceptedTransformType, len(bigdataActiveItem.AcceptedTransformType))
	put.SetTextValByColName("source", bigdataActiveItem.Source, len(bigdataActiveItem.Source))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
}

// BigDataJuMeiPZDSActive
func BigDataJuMeiPZDSActive(c *gin.Context, log url.Values) {

	var bigdataActiveItem BigdataActiveStu
	bigdataActiveItem.UID = log.Get("uid")
	bigdataActiveItem.PlanID = log.Get("plan_id")
	bigdataActiveItem.MarketType = log.Get("market_type")
	bigdataActiveItem.AdsType = log.Get("ads_type")
	bigdataActiveItem.ExtDspChannel = log.Get("ext_dsp_channel")
	bigdataActiveItem.MediaChannel = log.Get("media_channel")
	bigdataActiveItem.SubChannelID = log.Get("sub_channel_id")
	bigdataActiveItem.ExtAdx = log.Get("ext_adx")
	bigdataActiveItem.OS = log.Get("os")
	bigdataActiveItem.OSV = log.Get("osv")
	bigdataActiveItem.DIDMd5 = log.Get("did_md5")
	bigdataActiveItem.Imei = log.Get("imei")
	bigdataActiveItem.ImeiMd5 = log.Get("imei_md5")
	bigdataActiveItem.AndroidID = log.Get("android_id")
	bigdataActiveItem.AndroidIDMd5 = log.Get("android_id_md5")
	bigdataActiveItem.Idfa = log.Get("idfa")
	bigdataActiveItem.IdfaMd5 = log.Get("idfa_md5")
	bigdataActiveItem.IP = c.ClientIP()
	bigdataActiveItem.UA = c.GetHeader("User-Agent")
	bigdataActiveItem.Oaid = log.Get("oaid")
	bigdataActiveItem.OaidMd5 = log.Get("oaid_md5")
	bigdataActiveItem.CAIDMulti = log.Get("caid_multi")
	bigdataActiveItem.Model = log.Get("model")
	bigdataActiveItem.Manufacturer = log.Get("manufacturer")
	// bigdataActiveItem.CRID = log.Get("crid")
	bigdataActiveItem.CPAPrice = utils.ConvertStringToInt(log.Get("cpa_price"))
	// bigdataActiveItem.ExtCPAPrice = utils.ConvertStringToInt(log.Get("ext_cpa_price"))
	bigdataActiveItem.CallBack = ""
	bigdataActiveItem.TransformType = c.Query("transfer_event") // 1 激活 2 注册 4 次留 5 付费
	// bigdataActiveItem.AcceptedTransformType = log.Get("accepted_transform_type")
	bigdataActiveItem.Source = log.Get("source")

	// save to holo
	put := holoclient.NewMutationRequest(db.NewHologresAdxDSPDataIgnoreTableSchema("deal", "conversion_data"))
	uuid := uuid.NewV4().String()
	put.SetTextValByColName("id", uuid, len(uuid))
	put.SetTextValByColName("reqid", bigdataActiveItem.UID, len(bigdataActiveItem.UID))
	put.SetTextValByColName("plan_id", bigdataActiveItem.PlanID, len(bigdataActiveItem.PlanID))
	put.SetTextValByColName("market_type", bigdataActiveItem.MarketType, len(bigdataActiveItem.MarketType))
	put.SetTextValByColName("ads_type", bigdataActiveItem.AdsType, len(bigdataActiveItem.AdsType))
	put.SetTextValByColName("ext_dsp_channel", bigdataActiveItem.ExtDspChannel, len(bigdataActiveItem.ExtDspChannel))
	put.SetTextValByColName("media_channel", bigdataActiveItem.MediaChannel, len(bigdataActiveItem.MediaChannel))
	put.SetTextValByColName("sub_channel_id", bigdataActiveItem.SubChannelID, len(bigdataActiveItem.SubChannelID))
	put.SetTextValByColName("ext_adx", bigdataActiveItem.ExtAdx, len(bigdataActiveItem.ExtAdx))
	put.SetTextValByColName("os", bigdataActiveItem.OS, len(bigdataActiveItem.OS))
	put.SetTextValByColName("osv", bigdataActiveItem.OSV, len(bigdataActiveItem.OSV))
	put.SetTextValByColName("did_md5", bigdataActiveItem.DIDMd5, len(bigdataActiveItem.DIDMd5))
	put.SetTextValByColName("imei", bigdataActiveItem.Imei, len(bigdataActiveItem.Imei))
	put.SetTextValByColName("imei_md5", bigdataActiveItem.ImeiMd5, len(bigdataActiveItem.ImeiMd5))
	put.SetTextValByColName("android_id", bigdataActiveItem.AndroidID, len(bigdataActiveItem.AndroidID))
	put.SetTextValByColName("android_id_md5", bigdataActiveItem.AndroidIDMd5, len(bigdataActiveItem.AndroidIDMd5))
	put.SetTextValByColName("idfa", bigdataActiveItem.Idfa, len(bigdataActiveItem.Idfa))
	put.SetTextValByColName("idfa_md5", bigdataActiveItem.IdfaMd5, len(bigdataActiveItem.IdfaMd5))
	put.SetTextValByColName("ip", bigdataActiveItem.IP, len(bigdataActiveItem.IP))
	put.SetTextValByColName("ua", bigdataActiveItem.UA, len(bigdataActiveItem.UA))
	put.SetTextValByColName("oaid", bigdataActiveItem.Oaid, len(bigdataActiveItem.Oaid))
	put.SetTextValByColName("oaid_md5", bigdataActiveItem.OaidMd5, len(bigdataActiveItem.OaidMd5))
	put.SetTextValByColName("caid_multi", bigdataActiveItem.CAIDMulti, len(bigdataActiveItem.CAIDMulti))
	put.SetTextValByColName("model", bigdataActiveItem.Model, len(bigdataActiveItem.Model))
	put.SetTextValByColName("manufacturer", bigdataActiveItem.Manufacturer, len(bigdataActiveItem.Manufacturer))
	put.SetInt32ValByColName("cpa_price", int32(bigdataActiveItem.CPAPrice))
	// put.SetInt32ValByColName("ext_cpa_price", int32(bigdataActiveItem.ExtCPAPrice))
	put.SetTextValByColName("transform_type", bigdataActiveItem.TransformType, len(bigdataActiveItem.TransformType))
	// put.SetTextValByColName("accepted_transform_type", bigdataActiveItem.AcceptedTransformType, len(bigdataActiveItem.AcceptedTransformType))
	put.SetTextValByColName("source", bigdataActiveItem.Source, len(bigdataActiveItem.Source))

	day := time.Now().Format("20060102")
	hour := time.Now().Format("15")
	minute := time.Now().Format("04")
	put.SetTextValByColName("dd", day, len(day))
	put.SetTextValByColName("hh", hour, len(hour))
	put.SetTextValByColName("mm", minute, len(minute))
	put.SetTimestamptzValByColName("report_time", time.Now().UnixMicro()-946684800000000)

	db.GlbHologresAdxDSPIgnoreDataDb.Submit(put)
}

// BigdataReqStu ...
type BigdataReqStu struct {
	UID           string
	PlanID        string
	MarketType    string
	AdsType       string
	ExtDspChannel string
	MediaChannel  string
	SubChannelID  string
	ExtAdx        string
	OS            string
	OSV           string
	Imei          string
	ImeiMd5       string
	AndroidID     string
	AndroidIDMd5  string
	Idfa          string
	IdfaMd5       string
	IP            string
	UA            string
	Oaid          string
	Model         string
	Manufacturer  string
	CRID          string
	Code          int
	Reason        string
	Day           time.Time
	Hour          string
	Minute        string
	Now           time.Time
}

// BigdataExpStu ...
type BigdataExpStu struct {
	UID           string
	GroupID       string
	PlanID        string
	MarketType    string
	AdsType       string
	ExtDspChannel string
	MediaChannel  string
	SubChannelID  string
	ExtAdx        string
	OS            string
	OSV           string
	DIDMd5        string
	Imei          string
	ImeiMd5       string
	AndroidID     string
	AndroidIDMd5  string
	Idfa          string
	IdfaMd5       string
	IP            string
	UA            string
	Oaid          string
	OaidMd5       string
	CAIDMulti     string
	Model         string
	Manufacturer  string
	CRID          string
	CPMPrice      int
	ExtCPMPrice   int
	Day           time.Time
	Hour          string
	Minute        string
	Now           time.Time
	IsOCPX        int // 是否曝光转ocpx
	// 填充时间
	DealTime int64
	// 创意id
	SupplyCRID string
	DemandCRID string
}

// BigdataClkStu ...
type BigdataClkStu struct {
	UID           string
	GroupID       string
	PlanID        string
	MarketType    string
	AdsType       string
	ExtDspChannel string
	MediaChannel  string
	SubChannelID  string
	ExtAdx        string
	OS            string
	OSV           string
	DIDMd5        string
	Imei          string
	ImeiMd5       string
	AndroidID     string
	AndroidIDMd5  string
	Idfa          string
	IdfaMd5       string
	IP            string
	UA            string
	Oaid          string
	OaidMd5       string
	CAIDMulti     string
	Model         string
	Manufacturer  string
	CRID          string
	ReqWidth      string
	ReqHeight     string
	Width         string
	Height        string
	DownX         string
	DownY         string
	UpX           string
	UpY           string
	CallBack      string
	CPCPrice      int
	ExtCPCPrice   int
	Day           time.Time
	Hour          string
	Minute        string
	Now           time.Time

	// debug ams click_id, click_time
	ClickTime string
	ClickId   string

	// 填充时间
	DealTime int64
	// 创意id
	SupplyCRID string
	DemandCRID string
}

// BigdataActiveStu ...
type BigdataActiveStu struct {
	UID                   string
	GroupID               string
	PlanID                string
	MarketType            string
	AdsType               string
	ExtDspChannel         string
	MediaChannel          string
	SubChannelID          string
	ExtAdx                string
	OS                    string
	OSV                   string
	DIDMd5                string
	Imei                  string
	ImeiMd5               string
	AndroidID             string
	AndroidIDMd5          string
	Idfa                  string
	IdfaMd5               string
	IP                    string
	UA                    string
	Oaid                  string
	OaidMd5               string
	CAIDMulti             string
	Model                 string
	Manufacturer          string
	CRID                  string
	CPAPrice              int
	ExtCPAPrice           int
	CallBack              string
	TransformType         string
	AcceptedTransformType string
	Day                   time.Time
	Hour                  string
	Minute                string
	Now                   time.Time
	Source                string // 来源: exp, clk
	IsCallBack            int    // 是否即将返回给媒体callback
	IsFinishCallBack      int    // 是否已经返回给媒体callback
}
