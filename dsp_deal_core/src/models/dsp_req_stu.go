package models

// DspReqStu ...
type DspReqStu struct {
	ApiVersion        string          `json:"api_version"`
	SDKVersion        string          `json:"sdk_version"`
	Device            DspReqDeviceStu `json:"device"`
	Pos               DspReqPosStu    `json:"pos"`
	ExtraClipboard    int             `json:"extra_clipboard"`
	ExtraNotification int             `json:"extra_notification"`
	ExtraWakeUp       int             `json:"extra_wakeup"`

	// 流量包增加
	Media   DspReqMediaStu `json:"media"`
	Network DspReqNetwork  `json:"network"`
	Geo     DspReqGeo      `json:"geo"`
}

// DspReqPosStu ...
type DspReqPosStu struct {
	PlanID       string `json:"plan_id"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	CreativeType int    `json:"creative_type"`
	// ssp supply_app_id
	SupplyAppID string `json:"supply_app_id"`
	// cpm floor
	CPMBidFloor int `json:"cpm_bid_floor"`

	// 流量包使用广告位ID, 广告位类型, splash, interstitial, native, reward
	SupplyPosID   string `json:"pos_id,omitempty"`
	SupplyPosType string `json:"pos_type,omitempty"`
}

// DspReqDeviceStu ...
type DspReqDeviceStu struct {
	Os           string `json:"os"`
	OsVersion    string `json:"os_version"`
	Model        string `json:"model"`
	Manufacturer string `json:"manufacturer"`
	DeviceType   int    `json:"device_type"`
	Imei         string `json:"imei"`
	ImeiMd5      string `json:"imei_md5"`
	AndroidID    string `json:"android_id"`
	AndroidIDMd5 string `json:"android_id_md5"`
	Oaid         string `json:"oaid"`
	OaidMd5      string `json:"oaid_md5"`
	Idfa         string `json:"idfa"`
	IdfaMd5      string `json:"idfa_md5"`
	Ua           string `json:"ua"`
	IP           string `json:"ip"`
	// lbs库ip
	LBSIPCountry  string `json:"lbs_ip_country,omitempty"`
	LBSIPProvince string `json:"lbs_ip_province,omitempty"`
	LBSIPCity     string `json:"lbs_ip_city,omitempty"`

	Mac       string `json:"mac"`
	DIDMd5Key string `json:"did_md5"`
	// ios 14 新增字段
	DeviceStartSec     string `json:"device_start_sec"`
	Country            string `json:"country"`
	Language           string `json:"language"`
	DeviceNameMd5      string `json:"device_name_md5"`
	HardwareMachine    string `json:"hardware_machine"`
	HardwareModel      string `json:"hardware_model"`
	PhysicalMemoryByte string `json:"physical_memory_byte"`
	HarddiskSizeByte   string `json:"harddisk_size_byte"`
	SystemUpdateSec    string `json:"system_update_sec"`
	TimeZone           string `json:"time_zone"`
	// CAID               string `json:"caid"`
	// CAIDVersion        string `json:"caid_version"`
	CAIDMulti []DspReqDeviceCAIDMulti `json:"caid_multi"`
}

type DspReqDeviceCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}

// DspReqMediaStu ...
type DspReqMediaStu struct {
	SupplyAppID string `json:"app_id,omitempty"`
}

// DspReqNetwork ...
type DspReqNetwork struct {
	ConnectType int `json:"connect_type,omitempty"`
	Carrier     int `json:"carrier,omitempty"`
}

// DspReqGeo ...
type DspReqGeo struct {
	Lat float64 `json:"lat,omitempty"`
	Lng float64 `json:"lng,omitempty"`
}
