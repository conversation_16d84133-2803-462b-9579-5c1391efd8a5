package models

// DspPlanRespStu ...
type DspPlanRespStu struct {
	Resp  DspRespStu          `json:"resp"`
	Extra DspPlanRespExtraStu `json:"resp_extra"`
}

// DspPlanRespExtraStu ...
type DspPlanRespExtraStu struct {
}

// DspReqStu ...
type DspRespStu struct {
	Code    int              `json:"code"`
	Message string           `json:"msg"`
	Data    []DspRespDataStu `json:"data,omitempty"`
}

type DspRespDataStu struct {
	ExpLinks []string `json:"exp_links"`
	ClkLinks []string `json:"clk_links"`
}
