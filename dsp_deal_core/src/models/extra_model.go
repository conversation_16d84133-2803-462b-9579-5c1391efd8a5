package models

import (
	"context"
	"dsp_core/db"
	"dsp_core/utils"
	"encoding/json"

	"github.com/gin-gonic/gin"
)

// order by priority ...
func GetExtraClipboardByGroupID(c context.Context) *DspExtraPlanGroupClipboardStu {

	cacheKey := "go_dsp_extra_clipboard_plan"

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var dspPlanClipboardArray DspExtraPlanGroupClipboardStu

	if cacheError != nil {
		// logger.GetSugaredLogger().Info("cache error:", cacheKey, cacheError)
	} else {
		// logger.GetSugaredLogger().Info("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &dspPlanClipboardArray)

		return &dspPlanClipboardArray
	}

	redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, cacheKey)
	if redisErr != nil {
		// logger.GetSugaredLogger().Info("redis error:", redisErr)
	} else {
		// logger.GetSugaredLogger().Info("redis value:", redisValue)
		db.GlbBigCache.Set(cacheKey, []byte(redisValue))

		if len(redisValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(redisValue), &dspPlanClipboardArray)

		return &dspPlanClipboardArray
	}

	return nil
}

// order by priority ...
func GetExtraNotificationByGroupID(c context.Context) *[]DspPlanStu {

	// cacheKey := "go_dsp_extra_notification_plan"

	// cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	// var dspPlanListArray []DspPlanStu

	// if cacheError != nil {
	// 	// logger.GetSugaredLogger().Info("cache error:", cacheKey, cacheError)
	// } else {
	// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
	// 	if len(cacheValue) == 0 {
	// 		return nil
	// 	}
	// 	json.Unmarshal([]byte(cacheValue), &dspPlanListArray)

	// 	return &dspPlanListArray
	// }

	// redisValue, redisErr := db.GetRedis().Get(c, cacheKey).Result()
	// if redisErr != nil {
	// 	// logger.GetSugaredLogger().Info("redis error:", redisErr)
	// } else {
	// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
	// 	db.GlbBigCache.Set(cacheKey, []byte(redisValue))

	// 	if len(redisValue) == 0 {
	// 		return nil
	// 	}
	// 	json.Unmarshal([]byte(redisValue), &dspPlanListArray)

	// 	return &dspPlanListArray
	// }

	return nil
}

// order by priority ...
func GetExtraWakeUpByGroupID(c *gin.Context) *[]DspPlanStu {

	// cacheKey := "go_dsp_extra_wakeup_plan"

	// cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	// var dspPlanListArray []DspPlanStu

	// if cacheError != nil {
	// 	// logger.GetSugaredLogger().Info("cache error:", cacheKey, cacheError)
	// } else {
	// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
	// 	if len(cacheValue) == 0 {
	// 		return nil
	// 	}
	// 	json.Unmarshal([]byte(cacheValue), &dspPlanListArray)

	// 	return &dspPlanListArray
	// }

	// redisValue, redisErr := db.GetRedis().Get(c, cacheKey).Result()
	// if redisErr != nil {
	// 	// logger.GetSugaredLogger().Info("redis error:", redisErr)
	// } else {
	// 	// logger.GetSugaredLogger().Info("redis value:", redisValue)
	// 	db.GlbBigCache.Set(cacheKey, []byte(redisValue))

	// 	if len(redisValue) == 0 {
	// 		return nil
	// 	}
	// 	json.Unmarshal([]byte(redisValue), &dspPlanListArray)

	// 	return &dspPlanListArray
	// }

	return nil
}

// ExtraNotificationStu ...
type ExtraNotificationStu struct {
	NotificationTitle        string `json:"notification_title"`
	NotificationDescription  string `json:"notification_description"`
	NotificationIconURL      string `json:"notification_icon_url"`
	NotificationDeepLink     string `json:"notification_dp_link"`
	NotificationH5Link       string `json:"notification_h5_link"`
	NotificationDownloadLink string `json:"notification_download_link"`

	ExpLinks []string `json:"exp_links"`
	ClkLinks []string `json:"clk_links"`
}

// ExtraClipboardStu ...
type ExtraClipboardStu struct {
	ClipboardText string   `json:"clipboard_text"`
	ExpLinks      []string `json:"exp_links"`

	// 比重
	Weight int `json:"weight"`
	// 包名
	PackageName string `json:"package_name"`
}

// ExtraWakeUpStu ...
type ExtraWakeUpStu struct {
	WakeUpDeepLink string   `json:"wakeup_dp_link"`
	ExpLinks       []string `json:"exp_links"`
}

// 剪贴板
// type DspPlanClipboardStu struct {
// 	PlanID            string             `json:"pid"`
// 	Priority          int                `json:"priority"`
// 	ExtraClipboardStu *ExtraClipboardStu `json:"extra_clipboard"`
// }

// 剪贴板组
type DspExtraPlanGroupClipboardStu struct {
	ExtraPlanGroupID            string            `json:"extra_plan_gid"`
	ExtraClipboardMaxNumType    int               `json:"extra_clipboard_max_num_type"`
	ExtraClipboardMaxNum        int               `json:"extra_clipboard_max_num"`
	ExtraClipboardTimeoutType   int               `json:"extra_clipboard_timeout_type"`
	ExtraClipboardTimeoutMaxNum int               `json:"extra_clipboard_timeout_max_num"`
	PlansJson                   string            `json:"plans_json"`
	Plans                       []DspExtraPlanStu `json:"plans"`
}

// 剪贴板组 -> 计划
type DspExtraPlanStu struct {
	PlanID   string `json:"pid"`
	Weight   int    `json:"weight"`
	IsActive bool   `json:"is_active"`

	ExtraClipboardStu *ExtraClipboardStu `json:"extra_clipboard"`
}
