package models

import (
	"dsp_core/utils"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
)

// CrontabReadConfig ...
func CrontabReadConfig() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.GetSugaredLogger().Errorf("cron panic: %v", err)
			}
		}()
		GoCrontabReadConfig()
	}()
}

// CrontabReadConfig ...
func GoCrontabReadConfig() {
	logger.GetSugaredLogger().Info("crontab --->", time.Now().Format("2006-01-02 15:04:05"))

	// dsp
	dspBeginTime := utils.GetCurrentMilliSecond()

	GetAllPlanInfoFromMysqlToCache()

	// 广告计划组
	GetAllPlanGroupInfoToCache()

	GetAllExtraClipboardToCache()

	// 获取流量包配置
	GetAppSupplyAppGroupConfigToCache()

	// 根据流量包id获取计划id配置
	GetAllPlansBySupplyAppGroupIDToCache()

	logger.GetSugaredLogger().Info("crontab dsp cost --->", utils.GetCurrentMilliSecond()-dspBeginTime)
}
