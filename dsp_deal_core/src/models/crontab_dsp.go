package models

import (
	"context"
	"dsp_core/db"
	"dsp_core/utils"
	"encoding/json"
	"log"
	"math/rand"
	"sort"
)

// GetAllPlanInfoFromMysqlToCache ...
func GetAllPlanInfoFromMysqlToCache() {
	c := context.Background()

	// get plan info to cache
	GetPlanInfoFromMysqlByPlanIDToCache(c)

	// no used
	GetNoUsedPlanInfoFromMysqlToCache()
}

// GetNoUsedPlanInfoFromMysqlToCache ...
func GetNoUsedPlanInfoFromMysqlToCache() {
	rows, err := db.GlbMySQLDspDb.Query("SELECT a.pid, a.name, " +
		"IFNULL(a.app_name, ''), IFNULL(a.market, '')  " +
		"FROM ad_plans a " +
		"where a.status != 1")

	if err != nil {
		log.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var dspPlanItem DspPlanStu
		err := rows.Scan(&dspPlanItem.PID, &dspPlanItem.PName,
			&dspPlanItem.AppName, &dspPlanItem.Market)
		if err != nil {
			log.Printf("scan failed, err:%v\n", err)

			return
		}
		// if strings.Contains(dspPlanItem.Market, "ssp") ||
		// 	strings.Contains(dspPlanItem.Market, "clipboard") ||
		// 	strings.Contains(dspPlanItem.Market, "notification") ||
		// 	strings.Contains(dspPlanItem.Market, "wakeup") {

		// 	cacheKey := "go_dsp_plan_" + dspPlanItem.PID

		// 	db.GlbBigCache.Set(cacheKey, []byte(""))
		// }

		cacheKey := "go_dsp_plan_" + dspPlanItem.PID
		db.GlbBigCache.Set(cacheKey, []byte(""))
	}
}

// GetPlanInfoFromMysqlByPlanIDToCache ...
func GetPlanInfoFromMysqlByPlanIDToCache(c context.Context) {

	rows, err := db.GlbMySQLDspDb.Query("SELECT a.pid, a.gid, b.marketing_type, IFNULL(b.ads_type, ''), IFNULL(b.ext_dsp_channel, ''), " +
		"a.status, a.pid, a.name, IFNULL(a.os, ''), " +
		"IFNULL(a.promotion_type, 0), " +
		"IFNULL(a.is_filter_package_names, 0), " +
		"IFNULL(a.filter_package_names, ''), " +
		"IFNULL(a.is_direct_installed_package_names, 0), " +
		"IFNULL(a.direct_installed_package_names, ''), " +
		"IFNULL(a.is_repetition, 0), " +
		"IFNULL(a.app_info, ''), " +
		"IFNULL(a.download_link, ''), IFNULL(a.h5_link, ''), IFNULL(a.dp_link, ''), " +
		"IFNULL(a.store_dp_link, ''), IFNULL(a.quick_app_link, ''), " +
		"IFNULL(a.app_name, ''), IFNULL(a.package_name, ''), IFNULL(a.package_size, ''), " +
		"IFNULL(a.publisher, ''), IFNULL(a.app_version, ''), IFNULL(a.app_version_code, ''), IFNULL(a.privacy_link, ''), IFNULL(a.permissions, ''), " +
		"IFNULL(a.market, ''), IFNULL(a.dhh_channel_id, ''), IFNULL(a.dhh_pos_id, ''), IFNULL(a.dhh_task_id, ''), " +
		"IFNULL(a.amap_source, ''), IFNULL(a.amap_channel, ''), IFNULL(a.amap_rta_ids, ''), " +
		"IFNULL(a.is_rta, 0), " +
		"IFNULL(a.rta_config_id, ''), " +
		"IFNULL(a.ks_ocpx_adid, ''), IFNULL(a.ocpx_promotion_types, ''), " +
		"IFNULL(a.is_ocpx, 0), " +
		"IFNULL(a.is_exp_to_ocpx, 0), " +
		"IFNULL(a.exp_to_ocpx_weight, 0), " +
		"IFNULL(a.uc_ocpx_ch, ''), " +
		"IFNULL(a.uc_ocpx_pos_id, ''), " +
		"IFNULL(a.tencent_gid, ''), IFNULL(a.tencent_media, ''), IFNULL(a.tencent_channel, ''), " +
		"IFNULL(a.tencent_sch_id, ''), IFNULL(a.tencent_loc_id, ''), " +
		"IFNULL(a.crowd_lib_type, ''), IFNULL(a.crowd_lib_id, ''), IFNULL(a.crowd_lib_use_type, ''), " +
		"IFNULL(a.max_req_device_interval_type, ''), IFNULL(a.max_req_device_interval_num, ''), " +
		"IFNULL(a.max_exp_type, ''), IFNULL(a.max_exp_num, ''), " +
		"IFNULL(a.max_clk_type, ''), IFNULL(a.max_clk_num, ''), IFNULL(a.max_exp_device_type, ''), " +
		"IFNULL(a.max_exp_device_num, ''), IFNULL(a.max_clk_device_type, ''), IFNULL(a.max_clk_device_num, ''), " +
		"IFNULL(a.max_limit_ip_type, ''), IFNULL(a.max_limit_ip_num, ''), " +
		"IFNULL(a.lbs_type, 0), IFNULL(a.lbs_white_list, ''), IFNULL(a.lbs_black_list, ''), IFNULL(a.android_manufacturer, ''), " +
		"IFNULL(a.android_version, ''), IFNULL(a.android_version_filters, ''), IFNULL(a.sdk_version, ''), IFNULL(a.date_type, ''), IFNULL(a.date_value, ''), " +
		"IFNULL(a.time_type, ''), IFNULL(a.time_list, ''), " +
		"IFNULL(a.unit_price_type, 0), IFNULL(a.unit_price_num, 0), IFNULL(a.ssp_price_type, 0), IFNULL(a.ssp_price_num, 0), " +
		"IFNULL(a.ssp_dynamic_cpm_times, 0), " +
		"IFNULL(a.ssp_dynamic_cpm_max_num, 0), " +
		"IFNULL(a.supply_app_group_ids, ''), " +
		"IFNULL(a.store_active_reduce_rate, 0), " +
		"IFNULL(a.request_pass_rate_type, ''), IFNULL(a.request_pass_rate_val, ''), " +
		"IFNULL(a.extra_notification_type, ''), IFNULL(a.extra_notification1_time_value, ''), IFNULL(a.extra_notification1_max_num, ''), " +
		"IFNULL(a.extra_notification2_time_value, ''), IFNULL(a.extra_notification2_max_num, ''), IFNULL(a.extra_notification_timeout_type, ''), " +
		"IFNULL(a.extra_notification_timeout_max_num, ''), IFNULL(a.extra_wakeup_type, ''), IFNULL(a.extra_wakeup_max_num, ''), " +
		"IFNULL(a.extra_wakeup_timeout_type, ''), IFNULL(a.extra_wakeup_timeout_max_num, ''), IFNULL(a.extra_wakeup_time_type, ''), " +
		"IFNULL(a.extra_wakeup_time_value, ''), " +
		"IFNULL(a.is_extra_clipboard_no_package_name, 0), IFNULL(a.extra_clipboards, ''), " +
		"IFNULL(c.creative_gid, '') " +
		"FROM ad_plans a " +
		"LEFT JOIN ad_groups b on a.gid = b.gid " +
		"LEFT JOIN ad_creative_groups c on a.pid = c.pid " +
		"where a.status = 1 and b.status = 1 and a.deleted_at is null and c.deleted_at is null")

	if err != nil {
		log.Println("query failed, err:", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var dspPlanData DspPlanStu

		err = rows.Scan(&dspPlanData.PID, &dspPlanData.GID, &dspPlanData.GroupMarketingType, &dspPlanData.GroupAdsType, &dspPlanData.GroupExtDspChannel,
			&dspPlanData.Status, &dspPlanData.PID, &dspPlanData.PName, &dspPlanData.OS,
			&dspPlanData.PromotionType,
			&dspPlanData.IsFilterPackageNames,
			&dspPlanData.FilterPackageNames,
			&dspPlanData.IsDirectInstalledPackageNames,
			&dspPlanData.DirectInstalledPackageNames,
			&dspPlanData.IsRepetition,
			&dspPlanData.AppInfo,
			&dspPlanData.DownloadLink, &dspPlanData.H5Link, &dspPlanData.DeepLink,
			&dspPlanData.StoreDeepLink, &dspPlanData.QuickAppLink,
			&dspPlanData.AppName, &dspPlanData.PackageName, &dspPlanData.PackageSize,
			&dspPlanData.Publisher, &dspPlanData.AppVersion, &dspPlanData.AppVersionCode, &dspPlanData.PrivacyLink, &dspPlanData.Permissions,
			&dspPlanData.Market, &dspPlanData.DHHChannelID, &dspPlanData.DHHPosID, &dspPlanData.DhhTaskID,
			&dspPlanData.AMapSource, &dspPlanData.AMapChannel, &dspPlanData.AMapRtaIDs,
			&dspPlanData.IsRTA,
			&dspPlanData.RTAConfigID,
			&dspPlanData.KuaiShouOCPXAdID, &dspPlanData.OCPXPromotionTypesJson,
			&dspPlanData.IsOCPX,
			&dspPlanData.IsExpToOCPX,
			&dspPlanData.ExpToOCPXWeight,
			&dspPlanData.UCOCPXCh,
			&dspPlanData.UCOCPXPosID,
			&dspPlanData.TencentGID, &dspPlanData.TencentMedia, &dspPlanData.TencentChannel,
			&dspPlanData.TencentSchID, &dspPlanData.TencentLocID,
			&dspPlanData.CrowdLibType, &dspPlanData.CrowdLibID, &dspPlanData.CrowdLibUseType,
			&dspPlanData.MaxReqDeviceIntervalType, &dspPlanData.MaxReqDeviceIntervalNum,
			&dspPlanData.MaxExpType, &dspPlanData.MaxExpNum,
			&dspPlanData.MaxClkType, &dspPlanData.MaxClkNum, &dspPlanData.MaxExpDeviceType,
			&dspPlanData.MaxExpDeviceNum, &dspPlanData.MaxClkDeviceType, &dspPlanData.MaxClkDeviceNum,
			&dspPlanData.MaxLimitIPType, &dspPlanData.MaxLimitIPNum,
			&dspPlanData.LBSType, &dspPlanData.LBSWhiteListJson, &dspPlanData.LBSBlackListJson, &dspPlanData.AndroidManufacturer,
			&dspPlanData.AndroidVersion, &dspPlanData.AndroidVersionFiltersJson, &dspPlanData.SDKVersion, &dspPlanData.DateType, &dspPlanData.DateValue,
			&dspPlanData.TimeType, &dspPlanData.TimeListJson,
			&dspPlanData.UnitPriceType, &dspPlanData.UnitPriceNum, &dspPlanData.SspPriceType, &dspPlanData.SspPriceNum,
			&dspPlanData.SspDynamicCPMTimes,
			&dspPlanData.SspDynamicCPMMaxNum,
			&dspPlanData.SupplyAppGroupIDListJson,
			&dspPlanData.StoreActiveReduceRate,
			&dspPlanData.RequestPassRateType, &dspPlanData.RequestPassRateVal,
			&dspPlanData.ExtraNotificationType, &dspPlanData.ExtraNotification1TimeValue, &dspPlanData.ExtraNotification1MaxNum,
			&dspPlanData.ExtraNotification2TimeValue, &dspPlanData.ExtraNotification2MaxNum, &dspPlanData.ExtraNotificationTimeoutType,
			&dspPlanData.ExtraNotificationTimeoutMaxNum, &dspPlanData.ExtraWakeupType, &dspPlanData.ExtraWakeupMaxNum,
			&dspPlanData.ExtraWakeupTimeoutType, &dspPlanData.ExtraWakeupTimeoutMaxNum, &dspPlanData.ExtraWakeupTimeType,
			&dspPlanData.ExtraWakeupTimeValue,
			&dspPlanData.IsExtraClipboardNoPackageName, &dspPlanData.ExtraClipboardsConfigJson,
			&dspPlanData.CreativeGroupID,
		)

		if err != nil {
			log.Println("scan failed, err:", err)
			continue
		}

		if len(dspPlanData.SupplyAppGroupIDListJson) > 2 {
			json.Unmarshal([]byte(dspPlanData.SupplyAppGroupIDListJson), &dspPlanData.SupplyAppGroupIDList)
		}

		// lbs白名单
		if dspPlanData.LBSType == 1 && len(dspPlanData.LBSWhiteListJson) > 2 {
			// log.Println("kbg_debug_white:", dspPlanData.PID, dspPlanData.LBSWhiteListJson)
			json.Unmarshal([]byte(dspPlanData.LBSWhiteListJson), &dspPlanData.LBSWhiteList)
			// log.Println("kbg_debug_white 1:", dspPlanData.PID, dspPlanData.LBSWhiteList)

			// for _, item := range dspPlanData.LBSWhiteList {
			// 	log.Println("kbg_debug_white ->:", item, len(item))
			// 	for _, item1 := range item {
			// 		log.Println("kbg_debug_white item1 ->:", item1)
			// 	}
			// }
		}
		dspPlanData.LBSWhiteListJson = ""

		// lbs黑名单
		if dspPlanData.LBSType == 1 && len(dspPlanData.LBSBlackListJson) > 2 {
			// log.Println("kbg_debug_black:", dspPlanData.PID, dspPlanData.LBSBlackListJson)
			json.Unmarshal([]byte(dspPlanData.LBSBlackListJson), &dspPlanData.LBSBlackList)
			// log.Println("kbg_debug_black 1:", dspPlanData.PID, dspPlanData.LBSBlackList)

			// for _, item := range dspPlanData.LBSBlackList {
			// 	log.Println("kbg_debug_black ->:", item, len(item))
			// 	for _, item1 := range item {
			// 		log.Println("kbg_debug_black item1 ->:", item1)
			// 	}
			// }
		}
		dspPlanData.LBSBlackListJson = ""

		//  and a.deleted_at IS NULL
		// log.Println(dspPlanData.GID)
		// log.Println(dspPlanData.GroupMarketingType)
		// log.Println(dspPlanData.GroupAdsType)
		// log.Println(dspPlanData.GroupExtDspChannel)
		// log.Println("kbg_debug_price: ", dspPlanData.PID, dspPlanData.SspPriceNum, dspPlanData.SspDynamicCPMTimes, dspPlanData.SspDynamicCPMMaxNum)
		// log.Println("kbg_debug_dp: ", dspPlanData.PID, dspPlanData.DeepLink, dspPlanData.IsStoreDeepLink, dspPlanData.StoreDeepLink, dspPlanData.IsMiStoreHalfScreenDeepLink)
		if len(dspPlanData.TimeListJson) > 0 {
			json.Unmarshal([]byte(dspPlanData.TimeListJson), &dspPlanData.TimeList)
		}
		if len(dspPlanData.AndroidVersionFiltersJson) > 0 {
			json.Unmarshal([]byte(dspPlanData.AndroidVersionFiltersJson), &dspPlanData.AndroidVersionFilters)
		}
		if len(dspPlanData.ExtraClipboardsConfigJson) > 0 {
			json.Unmarshal([]byte(dspPlanData.ExtraClipboardsConfigJson), &dspPlanData.ExtraClipboardsConfig)

			var tmpTotalWeight = 0
			for _, item := range dspPlanData.ExtraClipboardsConfig {
				tmpTotalWeight = tmpTotalWeight + item.Weight
			}
			if tmpTotalWeight > 0 {
				tmpRandWeight := rand.Intn(tmpTotalWeight)

				tmpWeight1 := 0
				for _, item := range dspPlanData.ExtraClipboardsConfig {
					tmpWeight1 = tmpWeight1 + item.Weight
					if tmpRandWeight < tmpWeight1 {
						dspPlanData.ExtraClipboardText = item.Clipboard
						break
					}
				}
			}
			// log.Println("debug 1:", dspPlanData.PID, dspPlanData.ExtraClipboardsConfig, dspPlanData.ExtraClipboardText)
		}

		// creatives
		// log.Println(creativeType)
		creatives := getCreativesFromMysqlByCreativeGroupIDToCache(c, dspPlanData.CreativeGroupID)
		if creatives != nil {
			dspPlanData.Creatives = creatives
		}
		// exp links
		expLinks := getExpLinkFromMysqlByPlanIDToCache(c, dspPlanData.PID)
		if expLinks != nil {
			dspPlanData.ExpLinks = expLinks
		}
		// clk links
		clkLinks := getClkLinkFromMysqlByPlanIDToCache(c, dspPlanData.PID)
		if clkLinks != nil {
			dspPlanData.ClkLinks = clkLinks
		}

		dspPlanJSON, _ := json.Marshal(dspPlanData)

		db.GlbBigCache.Set("go_dsp_plan_"+dspPlanData.PID, dspPlanJSON)
	}
}

func getCreativesFromMysqlByCreativeGroupIDToCache(c context.Context, creativeGroupID string) []DspCreativeStu {

	var creativesArray []DspCreativeStu

	sqlStr := "select creative_gid, creative_type, crid, IFNULL(a.title, ''), IFNULL(a.description, ''), IFNULL(a.icon_url, ''), " +
		"IFNULL(a.image_url, ''), IFNULL(a.width, 0), IFNULL(a.height, 0), " +
		"IFNULL(a.video_url, ''), IFNULL(a.cover_url, ''), IFNULL(a.video_width, 0), IFNULL(a.video_height, 0), IFNULL(a.duration, 0), " +
		"IFNULL(a.notification_title, ''), IFNULL(a.notification_description, ''), IFNULL(a.notification_icon_url, '') " +
		"from ad_creatives a " +
		"where a.creative_gid = '" + creativeGroupID + "'" + " and a.deleted_at is null"

	rows, err := db.GlbMySQLDspDb.Query(sqlStr)
	if err != nil {
		log.Printf("query failed, err:%v\n", err)
		return nil
	}
	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var item DspCreativeStu
		err := rows.Scan(&item.CreativeGID, &item.CreativeType, &item.CRID, &item.Title, &item.Description, &item.IconURL,
			&item.ImageURL, &item.ImageWidth, &item.ImageHeight,
			&item.VideoURL, &item.CoverURL, &item.VideoWidth, &item.VideoHeight, &item.VideoDuration,
			&item.NotificationTitle, &item.NotificationDescription, &item.NotificationIconURL,
		)
		if err != nil {
			log.Printf("scan failed, err:%v\n", err)
			return nil
		}

		creativesArray = append(creativesArray, item)
	}
	return creativesArray
}

func getExpLinkFromMysqlByPlanIDToCache(c context.Context, planID string) []string {

	var expLinkArray []string

	sqlStr := "select link from ad_plans_exp_links a " +
		"where a.pid = '" + planID + "'" + " and a.deleted_at is null"

	rows, err := db.GlbMySQLDspDb.Query(sqlStr)
	if err != nil {
		log.Printf("query failed, err:%v\n", err)
		return nil
	}
	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var item string
		err := rows.Scan(&item)
		if err != nil {
			log.Printf("scan failed, err:%v\n", err)
			return nil
		}
		// log.Println(price)

		expLinkArray = append(expLinkArray, item)
	}
	return expLinkArray
}

func getClkLinkFromMysqlByPlanIDToCache(c context.Context, planID string) []string {

	var clkLinkArray []string

	sqlStr := "select link from ad_plans_clk_links a " +
		"where a.pid = '" + planID + "'" + " and a.deleted_at is null"

	rows, err := db.GlbMySQLDspDb.Query(sqlStr)
	if err != nil {
		log.Printf("query failed, err:%v\n", err)
		return nil
	}
	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var item string
		err := rows.Scan(&item)
		if err != nil {
			log.Printf("scan failed, err:%v\n", err)
			return nil
		}
		// log.Println(price)

		clkLinkArray = append(clkLinkArray, item)
	}
	return clkLinkArray
}

// GetAllPlanGroupInfoToCache ...
func GetAllPlanGroupInfoToCache() {
	// c := context.Background()

	rows, err := db.GlbMySQLDspDb.Query("SELECT IFNULL(a.type, 0), " +
		"IFNULL(a.plan_gid, ''), " +
		"IFNULL(a.plans, '') " +
		"FROM ad_plan_groups a " +
		"where a.deleted_at is null")

	if err != nil {
		log.Printf("query failed, err:%v\n", err)
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var dspGroupInfo DspPlanGroupStu
		err := rows.Scan(&dspGroupInfo.Type, &dspGroupInfo.PlanGroupID, &dspGroupInfo.PlanGroupPlanInfoJson)
		if err != nil {
			log.Printf("scan failed, err:%v\n", err)
			return
		}

		tmpDspPlanGroupPlanStu := []DspPlanGroupPlanStu{}
		json.Unmarshal([]byte(dspGroupInfo.PlanGroupPlanInfoJson), &tmpDspPlanGroupPlanStu)
		// 按优先级排序
		sort.Sort(PlanGroupPlanPrioritySort(tmpDspPlanGroupPlanStu))

		// 取is_active = 1
		var tmpItemImpArray []DspPlanGroupPlanStu
		for _, tmpItem := range tmpDspPlanGroupPlanStu {
			if tmpItem.IsActive == "1" {
				tmpItemImpArray = append(tmpItemImpArray, tmpItem)
			}
		}
		dspGroupInfo.PlanGroupPlanInfo = tmpItemImpArray

		// log.Println("------------------------------")
		// log.Println("kbg_debug 0:", dspGroupInfo.Type)
		// log.Println("kbg_debug 1:", dspGroupInfo.PlanGroupID)
		// log.Println("kbg_debug 2:", dspGroupInfo.PlanGroupPlanInfoJson)
		// log.Println("kbg_debug 3:", dspGroupInfo.PlanGroupPlanInfo)

		cacheKey := "go_dsp_plan_group_" + dspGroupInfo.PlanGroupID

		tmpJSON, _ := json.Marshal(dspGroupInfo)
		db.GlbBigCache.Set(cacheKey, tmpJSON)
	}
}

type PlanGroupPlanPrioritySort []DspPlanGroupPlanStu

func (s PlanGroupPlanPrioritySort) Len() int {
	//返回传入数据的总数
	return len(s)
}
func (s PlanGroupPlanPrioritySort) Swap(i, j int) {
	//两个对象满足Less()则位置对换
	//表示执行交换数组中下标为i的数据和下标为j的数据
	s[i], s[j] = s[j], s[i]
}
func (s PlanGroupPlanPrioritySort) Less(i, j int) bool {
	//按字段比较大小,此处是升序排序
	return utils.ConvertStringToInt(s[i].Priority) < utils.ConvertStringToInt(s[j].Priority)
}

// GetAllExtraClipboardToCache ...
func GetAllExtraClipboardToCache() {
	// c := context.Background()

	cacheKey := "go_dsp_extra_clipboard_plan"

	clipboardGroupID := "b08d168e365122f0"
	var dspExtraPlanGroupClipboard DspExtraPlanGroupClipboardStu

	row := db.GlbMySQLDspDb.QueryRow("SELECT IFNULL(a.extra_plan_gid, ''), " +
		"IFNULL(a.extra_clipboard_max_num_type, 0), " +
		"IFNULL(a.extra_clipboard_max_num, 0), " +
		"IFNULL(a.extra_clipboard_timeout_type, 0), " +
		"IFNULL(a.extra_clipboard_timeout_max_num, 0), " +
		"IFNULL(a.plans, 0) " +
		"FROM ad_extra_plan_groups a " +
		"where a.deleted_at is null and a.extra_plan_gid = '" + clipboardGroupID + "'")

	err := row.Scan(&dspExtraPlanGroupClipboard.ExtraPlanGroupID,
		&dspExtraPlanGroupClipboard.ExtraClipboardMaxNumType,
		&dspExtraPlanGroupClipboard.ExtraClipboardMaxNum,
		&dspExtraPlanGroupClipboard.ExtraClipboardTimeoutType,
		&dspExtraPlanGroupClipboard.ExtraClipboardTimeoutMaxNum,
		&dspExtraPlanGroupClipboard.PlansJson,
	)
	if err != nil {
		log.Println("get extra dsp plan data failed, plan id: ", clipboardGroupID, err.Error())
		db.GlbBigCache.Set(cacheKey, []byte(""))
		return
	}

	if len(dspExtraPlanGroupClipboard.PlansJson) > 0 {
		json.Unmarshal([]byte(dspExtraPlanGroupClipboard.PlansJson), &dspExtraPlanGroupClipboard.Plans)
	}

	dspPlanJSON, _ := json.Marshal(dspExtraPlanGroupClipboard)

	// log.Println("kbg_debug: ", string(dspPlanJSON))
	db.GlbBigCache.Set(cacheKey, dspPlanJSON)
}

// GetPlanInfoFromMysqlByPlanIDFromRedis ...
func GetPlanInfoFromMysqlByPlanIDFromRedis(c context.Context, planID string) *DspPlanStu {

	cacheKey := "go_dsp_plan_" + planID

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	var dspPlanData DspPlanStu

	if cacheError != nil {
		// log.Println("cache error:", cacheKey, cacheError)
	} else {
		// log.Println("redis value:", redisValue)
		if len(cacheValue) == 0 {
			return nil
		}
		json.Unmarshal([]byte(cacheValue), &dspPlanData)

		return &dspPlanData
	}

	log.Println("err -----------------------------------", cacheKey)

	return nil
}

func GetAppSupplyAppGroupConfigToCache() {

	var dspSupplyAppGroupArray []DspSupplyAppGroupConfigStu

	cacheKey := "go_dsp_all_supply_app_group_config"

	rows, err := db.GlbMySQLDspDb.Query("SELECT a.uid, IFNULL(a.os, ''), IFNULL(a.weight, 0), IFNULL(a.pos_types, ''), IFNULL(a.supply_app_ids, '') " +
		"FROM supply_app_group a")

	if err != nil {
		log.Println("query failed, err:", err)
		db.GlbBigCache.Set(cacheKey, []byte(""))
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var dspPlanInfo DspSupplyAppGroupConfigStu
		var tmpPosTypes string
		var tmpSupplyAppIDs string
		err := rows.Scan(&dspPlanInfo.UID, &dspPlanInfo.Os, &dspPlanInfo.Weight, &tmpPosTypes, &tmpSupplyAppIDs)
		if err != nil {
			log.Println("scan failed, err:", err)
			db.GlbBigCache.Set(cacheKey, []byte(""))
			return
		}
		if len(tmpPosTypes) == 0 || len(tmpSupplyAppIDs) == 0 || dspPlanInfo.Weight == 0 {
			continue
		}

		json.Unmarshal([]byte(tmpPosTypes), &dspPlanInfo.PosTypes)
		json.Unmarshal([]byte(tmpSupplyAppIDs), &dspPlanInfo.SupplyAppIDs)

		dspSupplyAppGroupArray = append(dspSupplyAppGroupArray, dspPlanInfo)
	}

	if len(dspSupplyAppGroupArray) == 0 {
		db.GlbBigCache.Set(cacheKey, []byte(""))
		return
	}

	dspPlanJSON, _ := json.Marshal(dspSupplyAppGroupArray)
	db.GlbBigCache.Set(cacheKey, dspPlanJSON)
}

func GetAllPlansBySupplyAppGroupIDToCache() {
	cacheKey := "go_dsp_all_plan_with_supply_app_group"

	var dspPlanListArray []DspPlanStu

	rows, err := db.GlbMySQLDspDb.Query("SELECT a.pid,  " +
		"IFNULL(a.supply_app_group_ids, '') " +
		"FROM ad_plans a " +
		"LEFT JOIN ad_groups b on a.gid = b.gid " +
		"where a.deleted_at is null and a.status = 1 and b.status = 1 and a.deleted_at is null")

	if err != nil {
		log.Println("query failed, err:", err)
		db.GlbBigCache.Set(cacheKey, []byte(""))
		return
	}

	// 非常重要：关闭rows释放持有的数据库链接
	defer rows.Close()

	// 循环读取结果集中的数据
	for rows.Next() {
		var dspPlanItem DspPlanStu
		err := rows.Scan(&dspPlanItem.PID,
			&dspPlanItem.SupplyAppGroupIDListJson,
		)
		if err != nil {
			log.Println("scan failed, err:", err)
			db.GlbBigCache.Set(cacheKey, []byte(""))

			return
		}

		if len(dspPlanItem.SupplyAppGroupIDListJson) > 2 {
			json.Unmarshal([]byte(dspPlanItem.SupplyAppGroupIDListJson), &dspPlanItem.SupplyAppGroupIDList)
		}
		dspPlanItem.SupplyAppGroupIDListJson = ""
		dspPlanListArray = append(dspPlanListArray, dspPlanItem)
	}

	dspPlanJSON, _ := json.Marshal(dspPlanListArray)

	db.GlbBigCache.Set(cacheKey, dspPlanJSON)
}
