package models

import (
	"context"
	"dsp_core/db"
	"dsp_core/utils"
	"errors"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/logger"
)

// CacheFromRedis ...
func CacheFromRedis(c context.Context, cacheKey string) (string, error) {

	cacheValue, cacheError := db.GlbBigCache.Get(cacheKey)
	if cacheError != nil {
		logger.GetSugaredLogger().Error("cache error:", cacheError)

		redisValue, redisErr := db.GetRedis().Get(c, utils.Timeout50mill, cacheKey)
		if redisErr != nil {
			// logger.GetSugaredLogger().Info("redis error:", redisErr)
			db.GlbBigCache.Set(cacheKey, []byte(""))
		} else {
			cacheValue = []byte(redisValue)
			db.GlbBigCache.Set(cacheKey, []byte(redisValue))
		}
	}
	if len(cacheValue) > 0 {
		return string(cacheValue), nil

	}
	return "", errors.New("cache error")
}
