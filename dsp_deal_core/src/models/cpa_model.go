package models

// MHCpaReq ...
type MHCpaReq struct {
	Channel      string `json:"channel"` // 1. ssp 2. ks 3. oceanengine, 4. bibili 5. store 6. douyu
	UID          string `json:"uid"`
	DIDMd5       string `json:"did_md5"`
	Imei         string `json:"imei"`
	ImeiMd5      string `json:"imei_md5"`
	Oaid         string `json:"oaid"`
	OaidMd5      string `json:"oaid_md5"`
	Idfa         string `json:"idfa"`
	IdfaMd5      string `json:"idfa_md5"`
	Os           string `json:"os"`
	Model        string `json:"model"`
	Manufacturer string `json:"manufacturer"`
	Osv          string `json:"osv"`
	IP           string `json:"ip"`
	UA           string `json:"ua"`
	DownCallback string `json:"callback"`
	CPAPrice     int    `json:"cpa_price"`
	ExtCPAPrice  int    `json:"ext_cpa_price"`

	// debug ams click_id, click_time
	ClickId   string `json:"click_id"`
	ClickTime string `json:"click_time"`

	CAIDMulti []MHCpaReqCAIDMulti `json:"caid_multi"`
}

type MHCpaReqCAIDMulti struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"version"`
}
