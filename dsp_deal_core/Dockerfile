##################################################
# FROM golang:1.20.3-alpine3.17 as go

# WORKDIR /src
# COPY ./ /src

# RUN go env -w GO111MODULE=on && \
#     go env -w GOPROXY=https://goproxy.cn,direct && \
#     cd src && \
#     CGO_ENABLED=0 GOOS=linux go build -o main

# ###

# FROM alpine:3.17

# RUN apk update --no-cache && \
#     apk add --no-cache tzdata && \
#     cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
#     echo "Asia/Shanghai" > /etc/timezone && \
#     rm -rf /var/cache/apk/*

# WORKDIR /app

# COPY --from=go /src/src/main /app
# COPY --from=go /src/config/ip2region.db /app

# CMD [ "/app/main" ]
##################################################

FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401agolang1.22-holoclientc20250102 as go

WORKDIR /src
COPY ./ /src

ARG GIT_USER
ARG GIT_TOKEN

RUN yum install -y tzdata libpq jemalloc unzip git

RUN echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc && \
    go env -w GO111MODULE=on && \
    go env -w GOPROXY=https://mirrors.aliyun.com/goproxy && \
    go env -w GOPRIVATE=codeup.aliyun.com && \
    unzip -d /src/data /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    rm -rf /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    cd src && \
    CGO_ENABLED=1 GOOS=linux go build -o main

FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401golang1.22-base20250102

RUN yum install -y tzdata libpq jemalloc procps-ng

RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

COPY --from=go /src/src/main /app
COPY --from=go /usr/local/lib64/libholo-client.so /usr/local/lib64/libholo-client.so
COPY --from=go /usr/lib/liblog4c.* /usr/lib/
COPY --from=go /src/log4crc /app/log4crc
COPY --from=go /src/data /app/data

RUN echo "/usr/local/lib64" >> /etc/ld.so.conf.d/holo.conf && \
    echo "/usr/lib" >> /etc/ld.so.conf.d/holo.conf && \
    ldconfig

# 设置日志环境变量
ENV LOG_LEVEL="info" \
LOG_DIR="/src/logs" \
LOG_FILENAME="dsp_deal_core.log" \
LOG_MAX_SIZE="1000" \
LOG_MAX_AGE="1" \
LOG_MAX_BACKUPS="1" \
LOG_STDOUT="true" \
LOG_REPORT_CALLER="true" \
LOG_JSON_FORMAT="false"

CMD [ "/app/main" ]
# CMD [ "tail", "-f", "/dev/null" ]
