##################################################
# FROM golang:1.20.3-alpine3.17 as go

# WORKDIR /src
# COPY ./ /src

# RUN apk update --no-cache && \
#     apk add --no-cache curl vim tzdata gcc binutils musl-dev libpq jemalloc supervisor && \
#     go env -w GO111MODULE=on && \
#     go env -w GOPROXY=https://goproxy.cn,direct && \
#     cp config/ip2region.db ./src/ && \
#     cd src && \
#     CGO_ENABLED=0 GOOS=linux go build -o main && \
#     cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
#     echo "Asia/Shanghai" > /etc/timezone

# RUN mkdir -p /src/logs/
# CMD /src/run.sh

# CMD [ "tail", "-f", "/dev/null" ]
##################################################

FROM mh-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/base/golang:20250401agolang1.22-holoclientc20250102 as go

WORKDIR /src
COPY ./ /src

ARG GIT_USER
ARG GIT_TOKEN

RUN yum install -y tzdata libpq jemalloc vim supervisor unzip git procps-ng

RUN echo "machine codeup.aliyun.com login ${GIT_USER} password ${GIT_TOKEN}" | cat > ~/.netrc && \
    go env -w GO111MODULE=on && \
    go env -w GOPROXY=https://mirrors.aliyun.com/goproxy && \
    go env -w GOPRIVATE=codeup.aliyun.com

RUN cd /src/src && \
    unzip -d /src/data /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    rm -rf /src/data/IPV6-COUNTRY-REGION-CITY.BIN.zip && \
    CGO_ENABLED=1 GOOS=linux go build -o main && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

RUN mkdir -p /app/
RUN cp -r /src/data /app/

RUN mkdir -p /src/logs/
RUN mkdir -p /src/runtime/nginx/logs

# 设置日志环境变量
ENV LOG_LEVEL="info" \
LOG_DIR="/src/logs" \
LOG_FILENAME="dsp_deal_core.log" \
LOG_MAX_SIZE="1000" \
LOG_MAX_AGE="1" \
LOG_MAX_BACKUPS="1" \
LOG_STDOUT="true" \
LOG_REPORT_CALLER="true" \
LOG_JSON_FORMAT="false"

CMD /src/run.sh

# CMD [ "tail", "-f", "/dev/null" ]

